package com.xgen.cloud.nds.aws._public.svc;

import static com.amazonaws.SDKGlobalConfiguration.AWS_WEB_IDENTITY_ENV_VAR;
import static com.xgen.cloud.common.aws._public.clients.AWSCredentialsUtil.getAWSCredentials;
import static com.xgen.cloud.common.aws._public.clients.AWSCredentialsUtil.getAWSCredentialsProvider;
import static com.xgen.cloud.nds.aws._public.svc.PrometheusCollectors.AWS_CAPACITY_QUOTA_ERROR_TOTAL;
import static com.xgen.cloud.nds.aws._public.util.AWSApiUtils.getAWSApiException;

import com.amazonaws.AbortedException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.AmazonWebServiceRequest;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSCredentialsProviderChain;
import com.amazonaws.auth.AWSSessionCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.AnonymousAWSCredentials;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.auth.EC2ContainerCredentialsProviderWrapper;
import com.amazonaws.auth.WebIdentityTokenCredentialsProvider;
import com.amazonaws.auth.policy.Policy;
import com.amazonaws.auth.policy.Statement;
import com.amazonaws.event.ProgressEvent;
import com.amazonaws.event.ProgressListener;
import com.amazonaws.event.SyncProgressListener;
import com.amazonaws.services.cloudwatch.AmazonCloudWatch;
import com.amazonaws.services.cloudwatch.model.Dimension;
import com.amazonaws.services.cloudwatch.model.GetMetricDataRequest;
import com.amazonaws.services.cloudwatch.model.GetMetricDataResult;
import com.amazonaws.services.cloudwatch.model.Metric;
import com.amazonaws.services.cloudwatch.model.MetricDataQuery;
import com.amazonaws.services.cloudwatch.model.MetricDataResult;
import com.amazonaws.services.cloudwatch.model.MetricStat;
import com.amazonaws.services.ebs.AmazonEBS;
import com.amazonaws.services.ebs.model.Block;
import com.amazonaws.services.ebs.model.ChangedBlock;
import com.amazonaws.services.ebs.model.ListChangedBlocksRequest;
import com.amazonaws.services.ebs.model.ListChangedBlocksResult;
import com.amazonaws.services.ebs.model.ListSnapshotBlocksRequest;
import com.amazonaws.services.ebs.model.ListSnapshotBlocksResult;
import com.amazonaws.services.ec2.AmazonEC2;
import com.amazonaws.services.ec2.model.AcceptVpcEndpointConnectionsRequest;
import com.amazonaws.services.ec2.model.AcceptVpcEndpointConnectionsResult;
import com.amazonaws.services.ec2.model.AcceptVpcPeeringConnectionRequest;
import com.amazonaws.services.ec2.model.AcceptVpcPeeringConnectionResult;
import com.amazonaws.services.ec2.model.Address;
import com.amazonaws.services.ec2.model.AllocateAddressRequest;
import com.amazonaws.services.ec2.model.AllocateAddressResult;
import com.amazonaws.services.ec2.model.AssociateAddressRequest;
import com.amazonaws.services.ec2.model.AttachInternetGatewayRequest;
import com.amazonaws.services.ec2.model.AttachNetworkInterfaceRequest;
import com.amazonaws.services.ec2.model.AttachVolumeRequest;
import com.amazonaws.services.ec2.model.AuthorizeSecurityGroupEgressRequest;
import com.amazonaws.services.ec2.model.AuthorizeSecurityGroupIngressRequest;
import com.amazonaws.services.ec2.model.AvailabilityZone;
import com.amazonaws.services.ec2.model.BlockDeviceMapping;
import com.amazonaws.services.ec2.model.CancelCapacityReservationRequest;
import com.amazonaws.services.ec2.model.CapacityReservation;
import com.amazonaws.services.ec2.model.CapacityReservationInstancePlatform;
import com.amazonaws.services.ec2.model.CapacityReservationPreference;
import com.amazonaws.services.ec2.model.CapacityReservationSpecification;
import com.amazonaws.services.ec2.model.CapacityReservationTarget;
import com.amazonaws.services.ec2.model.CopySnapshotRequest;
import com.amazonaws.services.ec2.model.CopySnapshotResult;
import com.amazonaws.services.ec2.model.CreateCapacityReservationRequest;
import com.amazonaws.services.ec2.model.CreateCapacityReservationResult;
import com.amazonaws.services.ec2.model.CreateInternetGatewayRequest;
import com.amazonaws.services.ec2.model.CreateInternetGatewayResult;
import com.amazonaws.services.ec2.model.CreateNetworkInterfaceRequest;
import com.amazonaws.services.ec2.model.CreateNetworkInterfaceResult;
import com.amazonaws.services.ec2.model.CreateRouteRequest;
import com.amazonaws.services.ec2.model.CreateRouteResult;
import com.amazonaws.services.ec2.model.CreateSecurityGroupRequest;
import com.amazonaws.services.ec2.model.CreateSecurityGroupResult;
import com.amazonaws.services.ec2.model.CreateSnapshotRequest;
import com.amazonaws.services.ec2.model.CreateSnapshotResult;
import com.amazonaws.services.ec2.model.CreateSubnetRequest;
import com.amazonaws.services.ec2.model.CreateSubnetResult;
import com.amazonaws.services.ec2.model.CreateTagsRequest;
import com.amazonaws.services.ec2.model.CreateTransitGatewayVpcAttachmentRequest;
import com.amazonaws.services.ec2.model.CreateTransitGatewayVpcAttachmentResult;
import com.amazonaws.services.ec2.model.CreateVolumePermission;
import com.amazonaws.services.ec2.model.CreateVolumePermissionModifications;
import com.amazonaws.services.ec2.model.CreateVolumeRequest;
import com.amazonaws.services.ec2.model.CreateVolumeResult;
import com.amazonaws.services.ec2.model.CreateVpcEndpointRequest;
import com.amazonaws.services.ec2.model.CreateVpcEndpointResult;
import com.amazonaws.services.ec2.model.CreateVpcEndpointServiceConfigurationRequest;
import com.amazonaws.services.ec2.model.CreateVpcEndpointServiceConfigurationResult;
import com.amazonaws.services.ec2.model.CreateVpcPeeringConnectionRequest;
import com.amazonaws.services.ec2.model.CreateVpcPeeringConnectionResult;
import com.amazonaws.services.ec2.model.CreateVpcRequest;
import com.amazonaws.services.ec2.model.CreateVpcResult;
import com.amazonaws.services.ec2.model.CreditSpecificationRequest;
import com.amazonaws.services.ec2.model.DeleteInternetGatewayRequest;
import com.amazonaws.services.ec2.model.DeleteNetworkInterfaceRequest;
import com.amazonaws.services.ec2.model.DeleteRouteRequest;
import com.amazonaws.services.ec2.model.DeleteSecurityGroupRequest;
import com.amazonaws.services.ec2.model.DeleteSnapshotRequest;
import com.amazonaws.services.ec2.model.DeleteSubnetRequest;
import com.amazonaws.services.ec2.model.DeleteVolumeRequest;
import com.amazonaws.services.ec2.model.DeleteVpcEndpointServiceConfigurationsRequest;
import com.amazonaws.services.ec2.model.DeleteVpcEndpointServiceConfigurationsResult;
import com.amazonaws.services.ec2.model.DeleteVpcEndpointsRequest;
import com.amazonaws.services.ec2.model.DeleteVpcEndpointsResult;
import com.amazonaws.services.ec2.model.DeleteVpcPeeringConnectionRequest;
import com.amazonaws.services.ec2.model.DeleteVpcRequest;
import com.amazonaws.services.ec2.model.DescribeAddressesRequest;
import com.amazonaws.services.ec2.model.DescribeAddressesResult;
import com.amazonaws.services.ec2.model.DescribeAvailabilityZonesRequest;
import com.amazonaws.services.ec2.model.DescribeAvailabilityZonesResult;
import com.amazonaws.services.ec2.model.DescribeCapacityReservationsRequest;
import com.amazonaws.services.ec2.model.DescribeCapacityReservationsResult;
import com.amazonaws.services.ec2.model.DescribeImagesRequest;
import com.amazonaws.services.ec2.model.DescribeImagesResult;
import com.amazonaws.services.ec2.model.DescribeInstanceAttributeRequest;
import com.amazonaws.services.ec2.model.DescribeInstanceAttributeResult;
import com.amazonaws.services.ec2.model.DescribeInstanceCreditSpecificationsRequest;
import com.amazonaws.services.ec2.model.DescribeInstanceCreditSpecificationsResult;
import com.amazonaws.services.ec2.model.DescribeInstanceStatusRequest;
import com.amazonaws.services.ec2.model.DescribeInstanceStatusResult;
import com.amazonaws.services.ec2.model.DescribeInstanceTypeOfferingsRequest;
import com.amazonaws.services.ec2.model.DescribeInstancesRequest;
import com.amazonaws.services.ec2.model.DescribeInstancesResult;
import com.amazonaws.services.ec2.model.DescribeInternetGatewaysRequest;
import com.amazonaws.services.ec2.model.DescribeInternetGatewaysResult;
import com.amazonaws.services.ec2.model.DescribeNetworkInterfacesRequest;
import com.amazonaws.services.ec2.model.DescribeNetworkInterfacesResult;
import com.amazonaws.services.ec2.model.DescribeRouteTablesRequest;
import com.amazonaws.services.ec2.model.DescribeRouteTablesResult;
import com.amazonaws.services.ec2.model.DescribeSecurityGroupsRequest;
import com.amazonaws.services.ec2.model.DescribeSecurityGroupsResult;
import com.amazonaws.services.ec2.model.DescribeSnapshotsRequest;
import com.amazonaws.services.ec2.model.DescribeSnapshotsResult;
import com.amazonaws.services.ec2.model.DescribeSubnetsRequest;
import com.amazonaws.services.ec2.model.DescribeSubnetsResult;
import com.amazonaws.services.ec2.model.DescribeVolumeStatusRequest;
import com.amazonaws.services.ec2.model.DescribeVolumeStatusResult;
import com.amazonaws.services.ec2.model.DescribeVolumesModificationsRequest;
import com.amazonaws.services.ec2.model.DescribeVolumesModificationsResult;
import com.amazonaws.services.ec2.model.DescribeVolumesRequest;
import com.amazonaws.services.ec2.model.DescribeVolumesResult;
import com.amazonaws.services.ec2.model.DescribeVpcEndpointConnectionsRequest;
import com.amazonaws.services.ec2.model.DescribeVpcEndpointConnectionsResult;
import com.amazonaws.services.ec2.model.DescribeVpcEndpointServicesRequest;
import com.amazonaws.services.ec2.model.DescribeVpcEndpointServicesResult;
import com.amazonaws.services.ec2.model.DescribeVpcEndpointsRequest;
import com.amazonaws.services.ec2.model.DescribeVpcEndpointsResult;
import com.amazonaws.services.ec2.model.DescribeVpcPeeringConnectionsRequest;
import com.amazonaws.services.ec2.model.DescribeVpcPeeringConnectionsResult;
import com.amazonaws.services.ec2.model.DescribeVpcsRequest;
import com.amazonaws.services.ec2.model.DescribeVpcsResult;
import com.amazonaws.services.ec2.model.DetachInternetGatewayRequest;
import com.amazonaws.services.ec2.model.DetachVolumeRequest;
import com.amazonaws.services.ec2.model.DisassociateAddressRequest;
import com.amazonaws.services.ec2.model.DnsOptionsSpecification;
import com.amazonaws.services.ec2.model.DomainType;
import com.amazonaws.services.ec2.model.DryRunResult;
import com.amazonaws.services.ec2.model.EbsBlockDevice;
import com.amazonaws.services.ec2.model.EndDateType;
import com.amazonaws.services.ec2.model.Filter;
import com.amazonaws.services.ec2.model.GetConsoleOutputRequest;
import com.amazonaws.services.ec2.model.GetConsoleOutputResult;
import com.amazonaws.services.ec2.model.Image;
import com.amazonaws.services.ec2.model.Instance;
import com.amazonaws.services.ec2.model.InstanceAttributeName;
import com.amazonaws.services.ec2.model.InstanceCreditSpecificationRequest;
import com.amazonaws.services.ec2.model.InstanceMatchCriteria;
import com.amazonaws.services.ec2.model.InstanceState;
import com.amazonaws.services.ec2.model.InstanceStatus;
import com.amazonaws.services.ec2.model.InstanceTypeOffering;
import com.amazonaws.services.ec2.model.InternetGateway;
import com.amazonaws.services.ec2.model.IpPermission;
import com.amazonaws.services.ec2.model.LocationType;
import com.amazonaws.services.ec2.model.ModifyCapacityReservationRequest;
import com.amazonaws.services.ec2.model.ModifyInstanceAttributeRequest;
import com.amazonaws.services.ec2.model.ModifyInstanceCapacityReservationAttributesRequest;
import com.amazonaws.services.ec2.model.ModifyInstanceCapacityReservationAttributesResult;
import com.amazonaws.services.ec2.model.ModifyInstanceCreditSpecificationRequest;
import com.amazonaws.services.ec2.model.ModifySnapshotAttributeRequest;
import com.amazonaws.services.ec2.model.ModifyVolumeRequest;
import com.amazonaws.services.ec2.model.ModifyVpcAttributeRequest;
import com.amazonaws.services.ec2.model.ModifyVpcEndpointServiceConfigurationRequest;
import com.amazonaws.services.ec2.model.ModifyVpcEndpointServiceConfigurationResult;
import com.amazonaws.services.ec2.model.ModifyVpcEndpointServicePermissionsRequest;
import com.amazonaws.services.ec2.model.ModifyVpcEndpointServicePermissionsResult;
import com.amazonaws.services.ec2.model.ModifyVpcPeeringConnectionOptionsRequest;
import com.amazonaws.services.ec2.model.ModifyVpcPeeringConnectionOptionsResult;
import com.amazonaws.services.ec2.model.NetworkInterface;
import com.amazonaws.services.ec2.model.PeeringConnectionOptionsRequest;
import com.amazonaws.services.ec2.model.Placement;
import com.amazonaws.services.ec2.model.RebootInstancesRequest;
import com.amazonaws.services.ec2.model.RejectVpcEndpointConnectionsRequest;
import com.amazonaws.services.ec2.model.RejectVpcEndpointConnectionsResult;
import com.amazonaws.services.ec2.model.RejectVpcPeeringConnectionRequest;
import com.amazonaws.services.ec2.model.RejectVpcPeeringConnectionResult;
import com.amazonaws.services.ec2.model.ReleaseAddressRequest;
import com.amazonaws.services.ec2.model.ResourceType;
import com.amazonaws.services.ec2.model.RevokeSecurityGroupEgressRequest;
import com.amazonaws.services.ec2.model.RevokeSecurityGroupIngressRequest;
import com.amazonaws.services.ec2.model.RouteTable;
import com.amazonaws.services.ec2.model.RunInstancesRequest;
import com.amazonaws.services.ec2.model.RunInstancesResult;
import com.amazonaws.services.ec2.model.SecurityGroup;
import com.amazonaws.services.ec2.model.Snapshot;
import com.amazonaws.services.ec2.model.StartInstancesRequest;
import com.amazonaws.services.ec2.model.StartInstancesResult;
import com.amazonaws.services.ec2.model.StopInstancesRequest;
import com.amazonaws.services.ec2.model.StopInstancesResult;
import com.amazonaws.services.ec2.model.Subnet;
import com.amazonaws.services.ec2.model.Tag;
import com.amazonaws.services.ec2.model.TagSpecification;
import com.amazonaws.services.ec2.model.TerminateInstancesRequest;
import com.amazonaws.services.ec2.model.TerminateInstancesResult;
import com.amazonaws.services.ec2.model.Volume;
import com.amazonaws.services.ec2.model.VolumeStatusItem;
import com.amazonaws.services.ec2.model.VolumeType;
import com.amazonaws.services.ec2.model.Vpc;
import com.amazonaws.services.ec2.model.VpcEndpointConnection;
import com.amazonaws.services.ec2.model.VpcEndpointType;
import com.amazonaws.services.ec2.model.VpcPeeringConnection;
import com.amazonaws.services.elasticloadbalancingv2.AmazonElasticLoadBalancing;
import com.amazonaws.services.elasticloadbalancingv2.model.Action;
import com.amazonaws.services.elasticloadbalancingv2.model.ActionTypeEnum;
import com.amazonaws.services.elasticloadbalancingv2.model.AddTagsRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.CreateListenerRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.CreateListenerResult;
import com.amazonaws.services.elasticloadbalancingv2.model.CreateLoadBalancerRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.CreateLoadBalancerResult;
import com.amazonaws.services.elasticloadbalancingv2.model.CreateTargetGroupRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.CreateTargetGroupResult;
import com.amazonaws.services.elasticloadbalancingv2.model.DeleteListenerRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DeleteListenerResult;
import com.amazonaws.services.elasticloadbalancingv2.model.DeleteLoadBalancerRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DeleteLoadBalancerResult;
import com.amazonaws.services.elasticloadbalancingv2.model.DeleteTargetGroupRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DeleteTargetGroupResult;
import com.amazonaws.services.elasticloadbalancingv2.model.DeregisterTargetsRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DeregisterTargetsResult;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeListenersRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeListenersResult;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeLoadBalancerAttributesRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeLoadBalancerAttributesResult;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeLoadBalancersRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeLoadBalancersResult;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeTagsRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeTargetGroupsRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeTargetGroupsResult;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeTargetHealthRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.DescribeTargetHealthResult;
import com.amazonaws.services.elasticloadbalancingv2.model.LoadBalancerAttribute;
import com.amazonaws.services.elasticloadbalancingv2.model.LoadBalancerSchemeEnum;
import com.amazonaws.services.elasticloadbalancingv2.model.LoadBalancerTypeEnum;
import com.amazonaws.services.elasticloadbalancingv2.model.ModifyListenerRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.ModifyListenerResult;
import com.amazonaws.services.elasticloadbalancingv2.model.ModifyLoadBalancerAttributesRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.ModifyLoadBalancerAttributesResult;
import com.amazonaws.services.elasticloadbalancingv2.model.ModifyTargetGroupAttributesRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.ModifyTargetGroupAttributesResult;
import com.amazonaws.services.elasticloadbalancingv2.model.ModifyTargetGroupRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.ModifyTargetGroupResult;
import com.amazonaws.services.elasticloadbalancingv2.model.ProtocolEnum;
import com.amazonaws.services.elasticloadbalancingv2.model.RegisterTargetsRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.RegisterTargetsResult;
import com.amazonaws.services.elasticloadbalancingv2.model.SetSubnetsRequest;
import com.amazonaws.services.elasticloadbalancingv2.model.SetSubnetsResult;
import com.amazonaws.services.elasticloadbalancingv2.model.TagDescription;
import com.amazonaws.services.elasticloadbalancingv2.model.TargetDescription;
import com.amazonaws.services.elasticloadbalancingv2.model.TargetGroupAttribute;
import com.amazonaws.services.elasticloadbalancingv2.model.TargetHealthDescription;
import com.amazonaws.services.elasticloadbalancingv2.model.TargetTypeEnum;
import com.amazonaws.services.kms.AWSKMS;
import com.amazonaws.services.kms.model.DecryptRequest;
import com.amazonaws.services.kms.model.DecryptResult;
import com.amazonaws.services.kms.model.DescribeKeyRequest;
import com.amazonaws.services.kms.model.DescribeKeyResult;
import com.amazonaws.services.kms.model.EncryptRequest;
import com.amazonaws.services.kms.model.EncryptResult;
import com.amazonaws.services.kms.model.KeyMetadata;
import com.amazonaws.services.ram.AWSRAM;
import com.amazonaws.services.ram.model.AcceptResourceShareInvitationRequest;
import com.amazonaws.services.ram.model.GetResourceShareInvitationsRequest;
import com.amazonaws.services.ram.model.GetResourceShareInvitationsResult;
import com.amazonaws.services.ram.model.RejectResourceShareInvitationRequest;
import com.amazonaws.services.route53.AmazonRoute53;
import com.amazonaws.services.route53.model.AssociateVPCWithHostedZoneRequest;
import com.amazonaws.services.route53.model.AssociateVPCWithHostedZoneResult;
import com.amazonaws.services.route53.model.Change;
import com.amazonaws.services.route53.model.ChangeAction;
import com.amazonaws.services.route53.model.ChangeBatch;
import com.amazonaws.services.route53.model.ChangeInfo;
import com.amazonaws.services.route53.model.ChangeResourceRecordSetsRequest;
import com.amazonaws.services.route53.model.ChangeResourceRecordSetsResult;
import com.amazonaws.services.route53.model.ChangeTagsForResourceRequest;
import com.amazonaws.services.route53.model.ChangeTagsForResourceResult;
import com.amazonaws.services.route53.model.CreateHealthCheckRequest;
import com.amazonaws.services.route53.model.CreateHealthCheckResult;
import com.amazonaws.services.route53.model.CreateVPCAssociationAuthorizationRequest;
import com.amazonaws.services.route53.model.CreateVPCAssociationAuthorizationResult;
import com.amazonaws.services.route53.model.DeleteHealthCheckRequest;
import com.amazonaws.services.route53.model.DisassociateVPCFromHostedZoneRequest;
import com.amazonaws.services.route53.model.DisassociateVPCFromHostedZoneResult;
import com.amazonaws.services.route53.model.GetChangeRequest;
import com.amazonaws.services.route53.model.GetChangeResult;
import com.amazonaws.services.route53.model.GetHealthCheckRequest;
import com.amazonaws.services.route53.model.GetHealthCheckResult;
import com.amazonaws.services.route53.model.GetHostedZoneRequest;
import com.amazonaws.services.route53.model.GetHostedZoneResult;
import com.amazonaws.services.route53.model.HealthCheck;
import com.amazonaws.services.route53.model.HealthCheckConfig;
import com.amazonaws.services.route53.model.HostedZone;
import com.amazonaws.services.route53.model.ListResourceRecordSetsRequest;
import com.amazonaws.services.route53.model.ListResourceRecordSetsResult;
import com.amazonaws.services.route53.model.ListVPCAssociationAuthorizationsRequest;
import com.amazonaws.services.route53.model.ListVPCAssociationAuthorizationsResult;
import com.amazonaws.services.route53.model.RRType;
import com.amazonaws.services.route53.model.ResourceRecord;
import com.amazonaws.services.route53.model.ResourceRecordSet;
import com.amazonaws.services.route53.model.VPC;
import com.amazonaws.services.route53resolver.AmazonRoute53Resolver;
import com.amazonaws.services.route53resolver.model.GetResolverDnssecConfigRequest;
import com.amazonaws.services.route53resolver.model.GetResolverDnssecConfigResult;
import com.amazonaws.services.route53resolver.model.ResolverDnssecConfig;
import com.amazonaws.services.route53resolver.model.UpdateResolverDnssecConfigRequest;
import com.amazonaws.services.route53resolver.model.Validation;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AbortMultipartUploadRequest;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.CompleteMultipartUploadRequest;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.CopyObjectResult;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.DeleteObjectsRequest;
import com.amazonaws.services.s3.model.DeleteObjectsResult;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.GetBucketLocationRequest;
import com.amazonaws.services.s3.model.GetObjectMetadataRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.InitiateMultipartUploadRequest;
import com.amazonaws.services.s3.model.InitiateMultipartUploadResult;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.ObjectTagging;
import com.amazonaws.services.s3.model.PartETag;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.Region;
import com.amazonaws.services.s3.model.RestoreObjectRequest;
import com.amazonaws.services.s3.model.RestoreObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.s3.model.SetObjectTaggingRequest;
import com.amazonaws.services.s3.model.UploadPartRequest;
import com.amazonaws.services.securitytoken.AWSSecurityTokenService;
import com.amazonaws.services.securitytoken.AWSSecurityTokenServiceClientBuilder;
import com.amazonaws.services.securitytoken.model.AssumeRoleRequest;
import com.amazonaws.services.securitytoken.model.AssumeRoleResult;
import com.amazonaws.services.securitytoken.model.Credentials;
import com.amazonaws.services.securitytoken.model.GetCallerIdentityRequest;
import com.amazonaws.services.securitytoken.model.GetCallerIdentityResult;
import com.amazonaws.services.securitytoken.model.GetFederationTokenRequest;
import com.amazonaws.services.securitytoken.model.GetFederationTokenResult;
import com.amazonaws.services.securitytoken.model.GetSessionTokenRequest;
import com.amazonaws.services.securitytoken.model.GetSessionTokenResult;
import com.amazonaws.waiters.Waiter;
import com.amazonaws.waiters.WaiterParameters;
import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.aws._public.AwsUtils;
import com.xgen.cloud.common.aws._public.clients.AWSApiCredentialsProviderImplementations;
import com.xgen.cloud.common.aws._public.clients.AWSClientsFactory;
import com.xgen.cloud.common.aws._public.clients.AWSCredentialsUtil;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.ErrorSource;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.common.util._public.util.DaemonThreadFactory;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAvailabilityZone;
import com.xgen.cloud.nds.aws._public.model.AWSErrorCode;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSPeerVpc;
import com.xgen.cloud.nds.aws._public.model.AWSPhysicalZoneId;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.ChunkedS3InputStream;
import com.xgen.cloud.nds.aws._public.model.NDSAWSMetricDefinition;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.model.ui.NDSAWSDescribeInstanceStatusView;
import com.xgen.cloud.nds.aws._public.model.ui.NDSAWSDescribeVolumeStatusView;
import com.xgen.cloud.nds.aws._public.svc.AWSResourceObservable.AWSResource;
import com.xgen.cloud.nds.aws._public.svc.AWSResourceObservable.Type;
import com.xgen.cloud.nds.aws._public.util.AWSApiUtils;
import com.xgen.cloud.nds.aws._public.util.PolicyUtils;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.cloudproviderconsole.NDSCloudProviderConsoleMetricDefinition;
import com.xgen.cloud.nds.cloudprovider._public.model.cloudproviderconsole.NDST2CSVRow;
import com.xgen.cloud.nds.cloudprovider._public.model.resourceobserver.ResourceEvent;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.NDSCloudProviderEventView;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.cloudprovidermonitoring.CloudProviderMonitoringError.ErrorType;
import com.xgen.cloud.nds.project._public.model.cloudprovidermonitoring.QuotaUsageEvent;
import com.xgen.cloud.nds.project._public.svc.NDSAlertSvc;
import com.xgen.cloud.nds.project._public.svc.cloudprovidermonitoring.CloudProviderMonitoringErrorSvc;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

@Singleton
public class AWSApiSvc {

  private static final Logger LOG = LoggerFactory.getLogger(AWSApiSvc.class);
  public static final Duration MAX_DURATION_FOR_ASSUME_ROLE = Duration.ofHours(1);

  private final Map<
          Pair<ObjectId, AWSApiCredentialsProviderImplementations>,
          Pair<AWSCredentialsProvider, Date>>
      _credentials;
  private final AWSAccountDao _awsAccountDao;
  private final CloudProviderMonitoringErrorSvc _cloudProviderMonitoringErrorSvc;
  private final NDSAlertSvc _ndsAlertSvc;
  private final AppSettings _appSettings;
  private final AWSClientsFactory _clientsFactory;
  private final AWSResourceObservable _resourceObservable;
  private final AWSDNSRecordObservable _dnsObservable;

  private final ScheduledExecutorService _scheduler =
      Executors.newScheduledThreadPool(
          1, new DaemonThreadFactory("AWSApiSvcCredentialsValidation"));
  private final AtomicReference<List<AWSApiCredentialsProviderImplementations>>
      _lastCredentialsProviders = new AtomicReference<>();
  private final AtomicReference<Date> _lastValidationDate = new AtomicReference<>(null);

  private static final String GET_OBJECT_RESOURCE_FORMAT = "arn:aws:s3:::%s/%s";
  private static final String FEDERATED_USERNAME_FORMAT = "%s-%s";
  private static final Set<ErrorCode> CAPACITY_QUOTA_ERROR_CODES =
      Set.of(
          AWSErrorCode.LIMIT_EXCEEDED,
          NDSErrorCode.NO_CAPACITY,
          AWSErrorCode.RESERVATION_CAPACITY_EXCEEDED);

  private static final int THROTTLING_RETRY_COUNT = 8;
  static final String ASSUME_ROLE_WORKLOAD_IDENTITY_NAME = "MONGODB_ATLAS_WORKLOAD_IDENTITY";
  private static final String ASSUME_ROLE_BASE_SESSION_NAME = "MONGODB_ATLAS_AWS_API";

  private static final long SIXTY_THREE_DAYS_IN_SECONDS = Duration.ofDays(63).toSeconds();
  private static final long ONE_HOUR_IN_SECONDS = Duration.ofHours(1).toSeconds();
  private static final long FIVE_MINUTES_IN_SECONDS = Duration.ofMinutes(5).toSeconds();
  private static final long FIFTEEN_DAYS_IN_SECONDS = Duration.ofDays(15).toSeconds();
  private static final long ONE_MINUTE_IN_SECONDS = Duration.ofMinutes(1).toSeconds();

  private static final String METRIC_DIMENSION_NAME_INSTANCE_ID = "InstanceId";
  private static final String METRIC_DIMENSION_NAME_VOLUME_ID = "VolumeId";

  protected static final int ASSUME_ROLE_CREDENTIALS_EXPIRATION_MINS = 60;
  protected static final int ASSUME_ROLE_CREDENTIALS_CACHE_EXPIRATION_PADDING_MINS = 15;
  protected static final int DEFAULT_CREDENTIALS_CACHE_EXPIRATION_MINS = 45;
  protected static final int CREDENTIALS_VALIDATION_CHECK_INTERVAL_MINS = 1;
  static final String MMS_AWS_API_CREDENTIALS_PROVIDERS_CONFIG_FIELD =
      "mms.aws.api.credentialsProviders";
  static final String MMS_AWS_API_ASSUME_ROLE_ROLE_ARN = "mms.aws.api.assumeRoleRoleArn";
  static final String MMS_AWS_API_CREDENTIALS_VALIDATION_ENABLED =
      "mms.aws.api.credentialsValidation.enabled";

  public static final String TOKEN_START = "START";
  public static final String TOKEN_DONE = "DONE";
  public static final List<String> AWS_ROOT_DEVICE_NAMES = List.of("/dev/xvda", "/dev/sda1");

  private static final int AWS_ACCOUNT_STRING_INDEX = 3;

  private static final String PROXY_PROTOCOL_V2_FLAG = "proxy_protocol_v2.enabled";
  public static TargetGroupAttribute PROXY_PROTOCOL_ENABLED_ATTRIBUTE =
      new TargetGroupAttribute().withKey(PROXY_PROTOCOL_V2_FLAG).withValue("true");
  private static final int SNAPSHOT_BLOCK_SIZE_BYTES = 512 * 1024;

  @Inject
  public AWSApiSvc(
      final AWSAccountDao pAWSAccountDao,
      final CloudProviderMonitoringErrorSvc pCloudProviderMonitoringErrorSvc,
      final AppSettings pAppSettings,
      final NDSAlertSvc pNDSAlertSvc,
      final AWSResourceObservable pResourceObservable,
      final AWSDNSRecordObservable pDNSObservable) {
    _credentials = new HashMap<>();
    _awsAccountDao = pAWSAccountDao;
    _cloudProviderMonitoringErrorSvc = pCloudProviderMonitoringErrorSvc;
    _ndsAlertSvc = pNDSAlertSvc;
    _appSettings = pAppSettings;
    _clientsFactory = new AWSClientsFactory(pAppSettings.getNDSGovUSEnabled());
    _resourceObservable = pResourceObservable;
    _dnsObservable = pDNSObservable;
  }

  @PreDestroy
  public void stop() {
    _scheduler.shutdownNow();
  }

  List<AWSApiCredentialsProviderImplementations> getAwsApiCredentialsProvidersImplementations() {
    try {
      final List<AWSApiCredentialsProviderImplementations> providers =
          _appSettings.getListProperty(MMS_AWS_API_CREDENTIALS_PROVIDERS_CONFIG_FIELD, ",").stream()
              .map(AWSApiCredentialsProviderImplementations::valueOf)
              .toList();
      return providers.isEmpty()
          ? AWSApiCredentialsProviderImplementations.DEFAULT_IMPLEMENTATIONS
          : providers;
    } catch (Exception pE) {
      // In case of misconfigured/invalid app setting
      return AWSApiCredentialsProviderImplementations.DEFAULT_IMPLEMENTATIONS;
    }
  }

  public String getAssumeRoleRoleArn(final AWSAccount pAWSAccount) {
    return Optional.ofNullable(pAWSAccount)
        .flatMap(AWSAccount::getAssumeRoleRoleARN)
        .orElseGet(() -> _appSettings.getStrProp(MMS_AWS_API_ASSUME_ROLE_ROLE_ARN, null));
  }

  protected AWSAccount getAWSAccount(final ObjectId pAWSAccountId) {
    return _awsAccountDao
        .find(pAWSAccountId)
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    String.format("Failed to find AWS Account by id (%s)", pAWSAccountId)));
  }

  protected String getAwsAccountString(final boolean isLocal, final AWSAccount pAwsAccount) {
    if (pAwsAccount == null) return "";
    final String roleArn =
        isLocal ? pAwsAccount.getRootARN() : pAwsAccount.getAssumeRoleARN().orElse(null);
    if (roleArn == null) return "";
    final String[] res = roleArn.split(":+");
    return res[AWS_ACCOUNT_STRING_INDEX];
  }

  protected Map<
          Pair<ObjectId, AWSApiCredentialsProviderImplementations>,
          Pair<AWSCredentialsProvider, Date>>
      getCredentialsCache() {
    return _credentials;
  }

  public Optional<WebIdentityTokenCredentialsProvider> getWebIdentityTokenCredentialsProvider() {
    final String roleSessionName =
        String.format("%s_%s", ASSUME_ROLE_WORKLOAD_IDENTITY_NAME, new ObjectId());
    final WebIdentityTokenCredentialsProvider provider =
        WebIdentityTokenCredentialsProvider.builder().roleSessionName(roleSessionName).build();
    final String awsRoleArn = System.getenv("AWS_ROLE_ARN");
    final String awsWebIdentityTokenFile = System.getenv("AWS_WEB_IDENTITY_TOKEN_FILE");
    // pre-check creds
    try {
      if (Optional.ofNullable(provider)
          .map(AWSCredentialsProvider::getCredentials)
          .filter(c -> c.getAWSAccessKeyId() != null)
          .filter(c -> c.getAWSSecretKey() != null)
          .isEmpty()) {
        LOG.warn(
            "[WORKLOAD_IDENTITY] Web identity token credentials are not configured for this host."
                + " roleArn={} webIdentityTokenFile={}",
            awsRoleArn,
            awsWebIdentityTokenFile);
        return Optional.empty();
      }
    } catch (final Exception pE) {
      LOG.warn(
          "[WORKLOAD_IDENTITY] Web identity token credentials are not configured for this host."
              + " roleArn={} webIdentityTokenFile={}",
          awsRoleArn,
          awsWebIdentityTokenFile,
          pE);
      return Optional.empty();
    }
    return Optional.of(provider);
  }

  public AWSCredentialsProvider getCredentialsProviderForAccount(
      final AWSAccount pAWSAccount,
      final List<AWSApiCredentialsProviderImplementations> pProviderImplementations) {
    final List<AWSCredentialsProvider> providers =
        pProviderImplementations.stream()
            .map(
                implementation ->
                    switch (implementation) {
                      case STATIC -> {
                        LOG.info(
                            "[STATIC] Using static credentials provider for AWS account."
                                + " awsAccount={}",
                            pAWSAccount.getName());
                        yield AWSCredentialsUtil.getAWSCredentialsProvider(
                            pAWSAccount.getAccessKey(), pAWSAccount.getSecretKey(), null, null);
                      }
                      case WORKLOAD_IDENTITY -> {
                        final String assumeRoleRoleARN = getAssumeRoleRoleArn(pAWSAccount);
                        if (assumeRoleRoleARN == null) {
                          LOG.warn(
                              "[WORKLOAD_IDENTITY] Unable to use web identity token credentials"
                                  + " because {} is not configured. awsAccount={}",
                              MMS_AWS_API_ASSUME_ROLE_ROLE_ARN,
                              pAWSAccount.getName());
                          yield null;
                        }
                        try {
                          yield getWebIdentityTokenCredentialsProvider()
                              .map(
                                  p ->
                                      generateAssumeRoleSessionCredentialsWithExpiration(
                                              p, assumeRoleRoleARN)
                                          .getLeft())
                              .orElse(null);
                        } catch (Exception pE) {
                          LOG.warn(
                              "[WORKLOAD_IDENTITY] Failed to assume role with web identity token"
                                  + " credentials. roleArn={} awsAccount={}",
                              assumeRoleRoleARN,
                              pAWSAccount.getName(),
                              pE);
                          yield null;
                        }
                      }
                    })
            .filter(Objects::nonNull)
            .toList();
    if (providers.isEmpty()) {
      throw new IllegalArgumentException(
          String.format(
              "Unable to find valid credentials for %s from specified providers: %s",
              pAWSAccount.getName(),
              pProviderImplementations.stream().map(Enum::name).collect(Collectors.joining(", "))));
    }
    return new AWSCredentialsProviderChain(providers);
  }

  Pair<AWSCredentialsProvider, Date> generateAssumeRoleSessionCredentialsWithExpiration(
      final AWSCredentialsProvider pCredentials, final String pAssumeRoleARN) {
    final String roleSessionName =
        String.format("%s_%s", ASSUME_ROLE_BASE_SESSION_NAME, new ObjectId());
    final Credentials assumeRoleCredentials =
        assumeRole(
            pCredentials,
            pAssumeRoleARN,
            null,
            roleSessionName,
            null,
            Duration.ofMinutes(ASSUME_ROLE_CREDENTIALS_EXPIRATION_MINS),
            LOG);

    final AWSCredentials sessionCredentials =
        getAWSCredentials(
            assumeRoleCredentials.getAccessKeyId(),
            assumeRoleCredentials.getSecretAccessKey(),
            assumeRoleCredentials.getSessionToken());

    // pad the expiration to ensure credentials get refreshed before expiration
    final Date paddedExpiration =
        new DateTime(assumeRoleCredentials.getExpiration())
            .minusMinutes(ASSUME_ROLE_CREDENTIALS_CACHE_EXPIRATION_PADDING_MINS)
            .toDate();
    final AWSCredentialsProvider sessionProvider =
        AWSCredentialsUtil.getAWSCredentialsProvider(sessionCredentials, null);
    return Pair.of(sessionProvider, paddedExpiration);
  }

  /**
   * Get temporary credentials after role assumption chaining. This method fetches temp session
   * credentials along with the expiration.
   *
   * @param pAssumeRoleARN Role to assume prior to returning temporary credentials
   * @param pRegionName Region name of the STS client to initialize to return credentials
   * @return {@link Pair} of credentials and the expiry date
   */
  public Pair<AWSCredentialsProvider, Date> generateAssumeRoleSessionCredentialsWithExpiration(
      final String pAssumeRoleARN, final RegionName pRegionName) {
    return generateAssumeRoleSessionCredentialsWithExpiration(
        getCredentialsProviderFromRoleArn(pAssumeRoleARN, pRegionName.getName()), pAssumeRoleARN);
  }

  private Pair<AWSCredentialsProvider, Date> generateCredentialsWithExpiration(
      final ObjectId pAWSAccountId,
      final List<AWSApiCredentialsProviderImplementations> pProviderImplementations) {
    final AWSAccount account = getAWSAccount(pAWSAccountId);
    final List<AWSApiCredentialsProviderImplementations> impls =
        account.getCredentialsProviderImplementations().orElse(pProviderImplementations);
    if ((account.isForCloudProviderAccess() || account.isForBilling())
        && account.getAssumeRoleRoleARN().isEmpty()) {
      // TODO: https://jira.mongodb.org/browse/CLOUDP-313242
      // Remove this when forCloudProviderAccess and forBilling accounts are using assumeRoleRoleARN
      return generateCloudProviderAccessAccountCredentialsWithExpiration(account, impls);
    } else {
      final AWSCredentialsProvider rootCredentials =
          getCredentialsProviderForAccount(account, impls);

      return account
          .getAssumeRoleARN()
          .map(r -> generateAssumeRoleSessionCredentialsWithExpiration(rootCredentials, r))
          .orElseGet(
              () ->
                  Pair.of(
                      rootCredentials,
                      new DateTime()
                          .plusMinutes(DEFAULT_CREDENTIALS_CACHE_EXPIRATION_MINS)
                          .toDate()));
    }
  }

  Pair<AWSCredentialsProvider, Date> generateCloudProviderAccessAccountCredentialsWithExpiration(
      final AWSAccount pCloudProviderAccessAccount,
      final List<AWSApiCredentialsProviderImplementations> pProviderImplementations) {
    // forCloudProviderAccess and forBilling are special accounts
    if (!pCloudProviderAccessAccount.isForCloudProviderAccess()
        && !pCloudProviderAccessAccount.isForBilling()) {
      throw new IllegalArgumentException(
          "Requires forCloudProviderAccess or forBilling AWS account");
    }

    return pProviderImplementations.stream()
        .map(
            implementation ->
                switch (implementation) {
                  case STATIC -> {
                    // This account should always ignore the assumeRoleARN when using static
                    // credentials
                    LOG.info(
                        "[STATIC] Using static credentials provider for cloud provider"
                            + " access account. awsAccount={}",
                        pCloudProviderAccessAccount.getName());
                    yield Pair.of(
                        AWSCredentialsUtil.getAWSCredentialsProvider(
                            pCloudProviderAccessAccount.getAccessKey(),
                            pCloudProviderAccessAccount.getSecretKey(),
                            null,
                            null),
                        new DateTime()
                            .plusMinutes(DEFAULT_CREDENTIALS_CACHE_EXPIRATION_MINS)
                            .toDate());
                  }
                  case WORKLOAD_IDENTITY -> {
                    try {
                      // This account MUST use the assumeRoleARN for workload identity auth
                      if (pCloudProviderAccessAccount.getAssumeRoleARN().isEmpty()) {
                        LOG.warn(
                            "[WORKLOAD_IDENTITY] Assume role ARN is missing for cloud provider"
                                + " access account. awsAccount={}",
                            pCloudProviderAccessAccount.getName());
                        yield null;
                      } else {
                        // This account MUST NOT use the cloudcontrol-root role
                        // It assumes the assumeRoleARN directly with the web identity credentials
                        yield getWebIdentityTokenCredentialsProvider()
                            .map(
                                p ->
                                    generateAssumeRoleSessionCredentialsWithExpiration(
                                        p,
                                        pCloudProviderAccessAccount
                                            .getAssumeRoleARN()
                                            .orElseThrow()))
                            .orElse(null);
                      }
                    } catch (final Exception pE) {
                      LOG.warn(
                          "[WORKLOAD_IDENTITY] Failed to generate credentials for cloud provider"
                              + " access account. awsAccount={}",
                          pCloudProviderAccessAccount.getName(),
                          pE);
                      yield null;
                    }
                  }
                })
        .filter(Objects::nonNull)
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalStateException(
                    "Unable to generate credentials for cloud provider access account"));
  }

  private Optional<AWSCredentialsProvider> getCachedCredentials(
      final ObjectId pAWSAccountId,
      final List<AWSApiCredentialsProviderImplementations> pProviderImplementations) {
    final Pair<ObjectId, AWSApiCredentialsProviderImplementations> cacheKey =
        Pair.of(pAWSAccountId, pProviderImplementations.get(0));
    return Optional.ofNullable(getCredentialsCache().get(cacheKey))
        .filter(cp -> cp.getRight().after(new Date()))
        .map(Pair::getLeft);
  }

  public synchronized AWSCredentialsProvider getCredentials(final ObjectId pAWSAccountId) {
    // Enabling credentials validation for workloads that use this code path
    enableCredentialsValidation();
    return getCredentials(pAWSAccountId, null);
  }

  public synchronized AWSCredentialsProvider getCredentials(
      final ObjectId pAWSAccountId,
      final List<AWSApiCredentialsProviderImplementations> pProviderImplementations) {
    final List<AWSApiCredentialsProviderImplementations> impls =
        Optional.ofNullable(pProviderImplementations)
            .orElseGet(this::getAwsApiCredentialsProvidersImplementations);
    return getCachedCredentials(pAWSAccountId, impls)
        .orElseGet(
            () -> {
              // generate new credentials...
              final Pair<AWSCredentialsProvider, Date> creds =
                  generateCredentialsWithExpiration(pAWSAccountId, impls);
              // and cache them
              final Pair<ObjectId, AWSApiCredentialsProviderImplementations> cacheKey =
                  Pair.of(pAWSAccountId, impls.get(0));
              getCredentialsCache().put(cacheKey, creds);
              return creds.getLeft();
            });
  }

  private synchronized void clearCredentialsCache() {
    getCredentialsCache().clear();
  }

  /**
   * Returns whether credentials validation is currently enabled. Validation is considered enabled
   * if it has been initialized (i.e., _lastValidationDate is not null).
   *
   * @return true if credentials validation is enabled, false otherwise
   */
  public boolean isCredentialsValidationEnabled() {
    return _lastValidationDate.get() != null;
  }

  private void enableCredentialsValidation() {
    try {
      // Only enable credentials validation if the app setting is enabled
      if (!_appSettings.getBoolProp(MMS_AWS_API_CREDENTIALS_VALIDATION_ENABLED, false)) {
        LOG.debug("AWS API credentials validation is disabled by app setting");
        return;
      }

      // Set _lastValidationDate to a non-null value to enroll this workload in validation
      if (_lastValidationDate.compareAndSet(null, new Date())) {
        // if successful (i.e. _lastValidationDate was not null before), schedule the thread
        LOG.info("Enabling credentials validation");
        _scheduler.scheduleAtFixedRate(
            this::validateAllAccountCredentialsIfNecessary,
            CREDENTIALS_VALIDATION_CHECK_INTERVAL_MINS,
            CREDENTIALS_VALIDATION_CHECK_INTERVAL_MINS,
            TimeUnit.MINUTES);
      }
    } catch (final Exception pE) {
      // catching all errors just to make this method safe to call
      LOG.warn("Error enabling credentials validation", pE);
    }
  }

  private void validateAllAccountCredentialsIfNecessary() {
    final List<AWSApiCredentialsProviderImplementations> credentialsProviders =
        getAwsApiCredentialsProvidersImplementations();
    final boolean providersChanged =
        !credentialsProviders.equals(_lastCredentialsProviders.getAndSet(credentialsProviders));
    // validate only if credentials providers have changed or
    // the last validation was more than 45 minutes ago
    if (providersChanged
        || _lastValidationDate
            .get()
            .before(DateUtils.addMinutes(new Date(), -DEFAULT_CREDENTIALS_CACHE_EXPIRATION_MINS))) {
      _lastValidationDate.set(new Date());
      if (providersChanged) {
        // clear credentials cache to force regenerating credentials using the new configuration
        clearCredentialsCache();
      }

      // We assume that STATIC credentials are working fine as-is, and this validation can generate
      // unnecessarily scary errors if we're running it before we're ready.
      if (credentialsProviders.contains(
          AWSApiCredentialsProviderImplementations.WORKLOAD_IDENTITY)) {
        validateAllAccountCredentials();
      }
    }
  }

  @VisibleForTesting
  protected List<Pair<AWSAccount, Exception>> validateAllAccountCredentials() {
    final String reportTitle = "VALIDATE_AWS_ACCOUNT_CREDENTIALS";
    try {
      // validate credentials only for accounts with regions and assignmentEnabled
      final List<AWSAccount> accounts =
          _awsAccountDao.findAllAccounts().stream()
              .filter(AWSAccount::getAssignmentEnabled)
              .filter(a -> !a.getRegions().isEmpty())
              .sorted(Comparator.comparing(AWSAccount::getName))
              .toList();
      final List<Pair<AWSAccount, Exception>> errors =
          accounts.stream()
              .map(
                  account -> {
                    try {
                      // check STATIC creds first
                      getAccountId(
                          account.getId(),
                          account.getRegionNames().stream().findFirst().orElseThrow(),
                          List.of(AWSApiCredentialsProviderImplementations.STATIC),
                          LOG);
                      // only check WORKLOAD_IDENTITY if the account has STATIC access
                      try {
                        getAccountId(
                            account.getId(),
                            account.getRegionNames().stream().findFirst().orElseThrow(),
                            List.of(AWSApiCredentialsProviderImplementations.WORKLOAD_IDENTITY),
                            LOG);
                        return null; // ok
                      } catch (final AbortedException pAborted) {
                        LOG.info(
                            "{}: Skipping workload identity credentials validation because"
                                + " credentials validation was aborted. awsAccount={}",
                            reportTitle,
                            account.getName());
                        return null;
                      } catch (final Exception pE) {
                        LOG.warn(
                            "{}: Failed to validate credentials. awsAccount={}",
                            reportTitle,
                            account.getName(),
                            pE);
                        return Pair.of(account, pE);
                      }
                    } catch (final Exception pE) {
                      LOG.info(
                          "{}: Skipping workload identity credentials validation because host does"
                              + " not have STATIC access. awsAccount={}",
                          reportTitle,
                          account.getName());
                      return null;
                    }
                  })
              .filter(Objects::nonNull)
              .toList();
      if (errors.isEmpty()) {
        LOG.info(
            "{}: Successfully validated credentials for {} accounts", reportTitle, accounts.size());
      } else {
        LOG.warn(
            "{}: Invalid credentials detected:\n{}",
            reportTitle,
            errors.stream()
                .map(p -> String.format("%s: %s", p.getLeft().getName(), p.getRight().getMessage()))
                .collect(Collectors.joining("\n")));
      }
      return errors;
    } catch (final Exception pE) {
      LOG.error("{}: credentials validation failed", reportTitle, pE);
    }
    return null;
  }

  @VisibleForTesting
  protected AmazonEC2 getEC2Client(final ObjectId pAWSAccountId, final AWSRegionName pRegionName) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getEC2Client(provider, pRegionName.getValue());
  }

  public AmazonEBS getEbsClient(final ObjectId pAWSAccountId, final AWSRegionName pRegionName) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getEbsClient(provider, pRegionName.getValue());
  }

  public AmazonS3 getS3Client(final ObjectId pAWSAccountId, final AWSRegionName pRegionName) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getS3Client(provider, pRegionName.getValue());
  }

  public AmazonS3 getS3Client(
      final ObjectId pAWSAccountId, final AWSRegionName pRegionName, final int pTimeout) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getS3ClientWithTimeout(provider, pRegionName.getValue(), pTimeout);
  }

  public AmazonS3 getS3ClientWithRetries(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final int pRequestTimeout,
      final int pAttempts,
      final int pSocketTimeout) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory()
        .getS3ClientWithRetryForMultipleTransientConditions(
            provider, pRegionName.getValue(), pRequestTimeout, pAttempts, pSocketTimeout);
  }

  private AmazonS3 getS3Client(final Credentials pCredentials, final AWSRegionName pRegionName) {
    final AWSCredentials credentials =
        getAWSCredentials(
            pCredentials.getAccessKeyId(),
            pCredentials.getSecretAccessKey(),
            pCredentials.getSessionToken());
    final AWSCredentialsProvider provider =
        AWSCredentialsUtil.getAWSCredentialsProvider(credentials, null);
    return getClientsFactory().getS3Client(provider, pRegionName.getValue());
  }

  public AmazonS3 getS3Client(
      final AWSCredentials pAWSCredentials, final boolean pUseGovCloudRegion) {
    final AWSCredentialsProvider provider =
        AWSCredentialsUtil.getAWSCredentialsProvider(pAWSCredentials, null);
    return getS3Client(provider, pUseGovCloudRegion);
  }

  private AmazonS3 getS3Client(
      final AWSCredentialsProvider pProvider, final boolean pUseGovCloudRegion) {
    return getClientsFactory().getS3ClientWithGlobalAccess(pProvider, pUseGovCloudRegion);
  }

  private AmazonS3 getS3Client(final ObjectId pAWSAccountId, final boolean pUseGovCloudRegion) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getS3ClientWithGlobalAccess(provider, pUseGovCloudRegion);
  }

  private AWSRAM getRamClient(final ObjectId pAWSAccountId, final AWSRegionName pRegionName) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getRamClient(provider, pRegionName.getValue());
  }

  private AmazonRoute53 getRoute53Client(final ObjectId pAWSAccountId) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getRoute53Client(provider);
  }

  private AmazonRoute53Resolver getRoute53ResolverClient(
      final ObjectId pAWSAccountId, final AWSRegionName pRegion) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getRoute53ResolverClient(provider, pRegion.getValue());
  }

  public AWSSecurityTokenService getSTSClient(
      final ObjectId pAWSAccountId, final RegionName pRegionName) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getSTSClient(provider, pRegionName.getValue());
  }

  public AWSSecurityTokenService getSTSClient(
      final ObjectId pAWSAccountId,
      final RegionName pRegionName,
      final List<AWSApiCredentialsProviderImplementations> pProviderImplementations) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId, pProviderImplementations);
    return getClientsFactory().getSTSClient(provider, pRegionName.getValue());
  }

  protected AWSKMS getKMSClient(
      final String pAccessKey,
      final String pSecretKey,
      final String pSessionToken,
      final AWSRegionName pRegionName) {
    final AWSCredentialsProvider provider =
        AWSCredentialsUtil.getAWSCredentialsProvider(pAccessKey, pSecretKey, pSessionToken, null);
    return getClientsFactory().getAWSKMSClient(provider, pRegionName.getValue());
  }

  @VisibleForTesting
  protected AmazonElasticLoadBalancing getElasticLoadBalancingClient(
      final ObjectId pAWSAccountId, final AWSRegionName pRegionName) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getElasticLoadBalancingClient(provider, pRegionName.getValue());
  }

  private AmazonCloudWatch getCloudWatchClient(
      final ObjectId pAWSAccountId, final AWSRegionName pRegionName) {
    final AWSCredentialsProvider provider = getCredentials(pAWSAccountId);
    return getClientsFactory().getCloudWatchClient(provider, pRegionName.getValue());
  }

  public void cancelCapacityReservation(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pReservationId,
      final Logger pLogger) {
    getAWSApiCallBuilder(
            new CancelCapacityReservationRequest().withCapacityReservationId(pReservationId),
            getEC2Client(pAWSAccountId, pRegionName)::cancelCapacityReservation,
            "Problem canceling capacity reservation",
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public CapacityReservation reserveCapacity(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pAvailabilityZone,
      final Optional<AWSPhysicalZoneId> pZoneId,
      final String pInstanceType,
      final Duration pDuration,
      final TagSpecification pTags,
      final Logger pLogger) {
    return reserveCapacity(
        pAWSAccountId,
        pRegionName,
        pAvailabilityZone,
        pZoneId,
        pInstanceType,
        pDuration,
        1,
        pTags,
        pLogger);
  }

  public CapacityReservation reserveCapacity(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pAvailabilityZone,
      final Optional<AWSPhysicalZoneId> pZoneId,
      final String pInstanceType,
      final Duration pDuration,
      final int pCount,
      final TagSpecification pTags,
      final Logger pLogger) {
    final CreateCapacityReservationRequest request =
        new CreateCapacityReservationRequest()
            .withAvailabilityZone(pAvailabilityZone)
            .withInstanceCount(pCount)
            .withInstanceType(pInstanceType)
            .withInstancePlatform(CapacityReservationInstancePlatform.LinuxUNIX)
            .withInstanceMatchCriteria(InstanceMatchCriteria.Targeted)
            .withEndDate(DateUtils.addMinutes(new Date(), (int) pDuration.toMinutes()))
            .withEndDateType(EndDateType.Limited)
            .withTagSpecifications(pTags);

    final CreateCapacityReservationResult result;
    try {
      result =
          getAWSApiCallBuilder(
                  request,
                  getEC2Client(pAWSAccountId, pRegionName)::createCapacityReservation,
                  "Problem creating capacity reservation",
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .throttleRetries(THROTTLING_RETRY_COUNT)
              .makeApiCall();
    } catch (final AWSApiException e) {
      if (NDSErrorCode.NO_CAPACITY.equals(e.getErrorCode())) {
        String msg =
            "Cannot create capacity reservation: "
                + "EC2 out of capacity for instance type "
                + pInstanceType
                + " in region "
                + pRegionName
                + " in zone "
                + pZoneId.map(AWSPhysicalZoneId::toString).orElse("Unknown")
                + ".";
        pLogger.debug(msg);
      }
      throw e;
    }

    return result.getCapacityReservation();
  }

  public CapacityReservation getCapacityReservation(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pReservationId,
      final Logger pLogger) {
    final List<CapacityReservation> result =
        getAWSApiCallBuilder(
                new DescribeCapacityReservationsRequest()
                    .withCapacityReservationIds(pReservationId),
                getEC2Client(pAWSAccountId, pRegionName)::describeCapacityReservations,
                "Problem describing capacity reservations",
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall()
            .getCapacityReservations();
    if (result.isEmpty()) {
      throw new AWSApiException(CommonErrorCode.NOT_FOUND);
    }
    return result.get(0);
  }

  public List<CapacityReservation> getCapacityReservations(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final List<Filter> pFilters,
      final Logger pLogger) {
    final List<CapacityReservation> result =
        getAWSApiCallBuilder(
                new DescribeCapacityReservationsRequest().withFilters(pFilters),
                getEC2Client(pAWSAccountId, pRegionName)::describeCapacityReservations,
                "Problem describing capacity reservations",
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall()
            .getCapacityReservations();
    if (result.isEmpty()) {
      throw new AWSApiException(CommonErrorCode.NOT_FOUND);
    }
    return result;
  }

  public String findCapacityReservationsAndApplyForNextToken(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final int pMaxResultSize,
      final String pNextToken,
      final Logger pLogger,
      final Consumer<DescribeCapacityReservationsResult> resultConsumer) {
    // This method should not be called with a null pNextToken
    // because a null next token indicates there are no more pages of results
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final String prettyError =
        String.format(
            "Problem retrieving Capacity Reservations in Region(%s), for Account Id (%s)",
            pRegionName.getName(), pAWSAccountId.toString());

    final DescribeCapacityReservationsRequest request =
        new DescribeCapacityReservationsRequest().withMaxResults(pMaxResultSize);

    if (!pNextToken.equals(AWSApiSvc.TOKEN_START)) {
      request.withNextToken(pNextToken);
    }

    final DescribeCapacityReservationsResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegionName)::describeCapacityReservations,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();
    resultConsumer.accept(result);

    final String newNextToken = result.getNextToken();
    return (newNextToken != null) ? newNextToken : TOKEN_DONE;
  }

  public boolean modifyInstanceCapacityReservation(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pEc2InstanceId,
      final String pReservationId,
      final Logger pLogger) {
    final ModifyInstanceCapacityReservationAttributesRequest request =
        new ModifyInstanceCapacityReservationAttributesRequest()
            .withInstanceId(pEc2InstanceId)
            .withCapacityReservationSpecification(
                new CapacityReservationSpecification()
                    .withCapacityReservationTarget(
                        new CapacityReservationTarget().withCapacityReservationId(pReservationId)));

    final ModifyInstanceCapacityReservationAttributesResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegionName)
                    ::modifyInstanceCapacityReservationAttributes,
                "Problem modifying instance capacity reservation attributes",
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .makeApiCall();
    return result.getReturn();
  }

  public boolean removeReservationFromInstance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pEc2InstanceId,
      final Logger pLogger) {
    final ModifyInstanceCapacityReservationAttributesRequest request =
        new ModifyInstanceCapacityReservationAttributesRequest()
            .withInstanceId(pEc2InstanceId)
            .withCapacityReservationSpecification(
                new CapacityReservationSpecification()
                    .withCapacityReservationPreference(CapacityReservationPreference.None));

    final ModifyInstanceCapacityReservationAttributesResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegionName)
                    ::modifyInstanceCapacityReservationAttributes,
                "Problem modifying instance capacity reservation attributes",
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();
    return result.getReturn();
  }

  public boolean acceptAssistedCapacityReservation(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pReservationId,
      final Date pEndDate,
      final Logger pLogger) {
    return modifyCapacityReservation(
        pAWSAccountId,
        pRegionName,
        new ModifyCapacityReservationRequest()
            .withCapacityReservationId(pReservationId)
            .withAccept(true)
            .withEndDate(pEndDate),
        pLogger);
  }

  public boolean setReservationEndDate(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pReservationId,
      final Date pEndDate,
      final Logger pLogger) {
    return modifyCapacityReservation(
        pAWSAccountId,
        pRegionName,
        new ModifyCapacityReservationRequest()
            .withCapacityReservationId(pReservationId)
            .withEndDateType(EndDateType.Limited)
            .withEndDate(pEndDate),
        pLogger);
  }

  public boolean modifyCapacityReservation(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final ModifyCapacityReservationRequest pRequest,
      final Logger pLogger) {
    return getAWSApiCallBuilder(
            pRequest,
            getEC2Client(pAWSAccountId, pRegionName)::modifyCapacityReservation,
            "Problem modifying capacity reservation attribute",
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall()
        .getReturn();
  }

  public Vpc findVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId) {

    if (StringUtils.isEmpty(pVpcId)) {
      throw new IllegalArgumentException("`pVpcId` is required");
    }

    final String prettyErr = "Problem finding vpc";

    final DescribeVpcsResult result =
        getAWSApiCallBuilder(
                new DescribeVpcsRequest().withVpcIds(pVpcId),
                getEC2Client(pAWSAccountId, pRegionName)::describeVpcs,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getVpcs() == null || result.getVpcs().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getVpcs().get(0);
  }

  public void findVpcsAndApply(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final int pMaxResultSize,
      final Logger pLogger,
      final Consumer<DescribeVpcsResult> resultConsumer) {
    final String prettyError =
        String.format(
            "Problem retrieving AWS VPCs in Region (%s), Account Id (%s)",
            pRegion.getName(), pAWSAccountId.toString());

    final DescribeVpcsRequest request = new DescribeVpcsRequest().withMaxResults(pMaxResultSize);
    DescribeVpcsResult result;
    String nextToken = null;
    do {
      if (nextToken != null) {
        request.withNextToken(nextToken);
      }
      result =
          getAWSApiCallBuilder(
                  request,
                  getEC2Client(pAWSAccountId, pRegion)::describeVpcs,
                  prettyError,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
              .makeApiCall();
      resultConsumer.accept(result);
      nextToken = result.getNextToken();
    } while (nextToken != null);
  }

  public String findVpcsAndApplyForNextToken(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final int pMaxResultSize,
      final String pNextToken,
      final Logger pLogger,
      final Consumer<DescribeVpcsResult> resultConsumer) {
    // This method should not be called with a null pNextToken
    // because a null next token indicates there are no more pages of results
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final String prettyError =
        String.format(
            "Problem retrieving AWS VPCs in Region (%s), Account Id (%s)",
            pRegion.getName(), pAWSAccountId.toString());

    final DescribeVpcsRequest request = new DescribeVpcsRequest().withMaxResults(pMaxResultSize);

    if (!pNextToken.equals(AWSApiSvc.TOKEN_START)) {
      request.withNextToken(pNextToken);
    }

    final DescribeVpcsResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegion)::describeVpcs,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();
    resultConsumer.accept(result);

    final String newNextToken = result.getNextToken();
    return (newNextToken != null) ? newNextToken : TOKEN_DONE;
  }

  public Vpc createVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pCidrBlock,
      final TagSpecification pTags) {

    final String prettyErr = "Problem creating vpc";
    CreateVpcRequest createVpcRequest =
        new CreateVpcRequest().withCidrBlock(pCidrBlock).withTagSpecifications(pTags);

    return createVpc(pAWSAccountId, pRegionName, pLogger, prettyErr, createVpcRequest);
  }

  @VisibleForTesting
  public Vpc createVpcWithIpv6(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pIpv4CidrBlock) {

    final String prettyErr = "Problem creating vpc with IPv6 addresses";
    CreateVpcRequest createVpcRequest =
        new CreateVpcRequest()
            .withCidrBlock(
                pIpv4CidrBlock) // AWS requires all VPCs contain IPv4s even if they use IPv6
            .withAmazonProvidedIpv6CidrBlock(true);

    return createVpc(pAWSAccountId, pRegionName, pLogger, prettyErr, createVpcRequest);
  }

  private Vpc createVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pPrettyErr,
      final CreateVpcRequest createVpcRequest) {

    final CreateVpcResult result =
        getAWSApiCallBuilder(
                createVpcRequest,
                getEC2Client(pAWSAccountId, pRegionName)::createVpc,
                pPrettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .makeApiCall();

    _resourceObservable.notify(
        new AWSResource(pAWSAccountId, pRegionName, Type.VPC, result.getVpc().getVpcId()),
        ResourceEvent.CREATED);

    return result.getVpc();
  }

  public void deleteVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId) {

    final String prettyErr = "Problem deleting vpc";

    getAWSApiCallBuilder(
            new DeleteVpcRequest().withVpcId(pVpcId),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).deleteVpc(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, AWSErrorCode.DEPENDENCY_VIOLATION))
        .makeApiCall();
  }

  public void enableDNSHostnamesOnVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId) {

    final String prettyErr = "Problem enabling DNS hostnames on vpc";

    getAWSApiCallBuilder(
            new ModifyVpcAttributeRequest().withVpcId(pVpcId).withEnableDnsHostnames(true),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).modifyVpcAttribute(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void enableDNSSECValidationOnVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId) {
    final String prettyErr = "Problem enabling DNSSEC validation on vpc";
    getAWSApiCallBuilder(
            new UpdateResolverDnssecConfigRequest()
                .withResourceId(pVpcId)
                .withValidation(Validation.ENABLE),
            r -> {
              getRoute53ResolverClient(pAWSAccountId, pRegionName).updateResolverDnssecConfig(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public ResolverDnssecConfig checkDNSSECValidationOnVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId) {
    final String prettyErr = "Problem checking DNSSEC validation on vpc";

    final GetResolverDnssecConfigResult result =
        getAWSApiCallBuilder(
                new GetResolverDnssecConfigRequest().withResourceId(pVpcId),
                getRoute53ResolverClient(pAWSAccountId, pRegionName)::getResolverDnssecConfig,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    return result.getResolverDNSSECConfig();
  }

  public InternetGateway findInternetGateway(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pIgwId) {

    if (StringUtils.isEmpty(pIgwId)) {
      throw new IllegalArgumentException("`pIgwId` is required");
    }

    final String prettyErr = "Problem finding internet gateway";

    final DescribeInternetGatewaysResult result =
        getAWSApiCallBuilder(
                new DescribeInternetGatewaysRequest().withInternetGatewayIds(pIgwId),
                getEC2Client(pAWSAccountId, pRegionName)::describeInternetGateways,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getInternetGateways() == null || result.getInternetGateways().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getInternetGateways().get(0);
  }

  public List<InternetGateway> findInternetGatewaysInVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId) {

    if (StringUtils.isEmpty(pVpcId)) {
      throw new IllegalArgumentException("`pVpcId` is required");
    }

    final String prettyErr = "Problem finding internet gateways";

    final DescribeInternetGatewaysResult result =
        getAWSApiCallBuilder(
                new DescribeInternetGatewaysRequest()
                    .withFilters(
                        new Filter("attachment.vpc-id", Collections.singletonList(pVpcId))),
                getEC2Client(pAWSAccountId, pRegionName)::describeInternetGateways,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getInternetGateways() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getInternetGateways();
  }

  public InternetGateway createInternetGateway(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final List<Tag> pTags) {

    final String prettyErr = "Problem creating internet gateway";

    final CreateInternetGatewayResult result =
        getAWSApiCallBuilder(
                new CreateInternetGatewayRequest()
                    .withTagSpecifications(
                        new TagSpecification()
                            .withResourceType(ResourceType.InternetGateway)
                            .withTags(pTags)),
                getEC2Client(pAWSAccountId, pRegionName)::createInternetGateway,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .makeApiCall();

    _resourceObservable.notify(
        new AWSResource(
            pAWSAccountId,
            pRegionName,
            Type.INTERNET_GATEWAY,
            result.getInternetGateway().getInternetGatewayId()),
        ResourceEvent.CREATED);

    return result.getInternetGateway();
  }

  public void deleteInternetGateway(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pIgwId) {

    final String prettyErr = "Problem deleting internet gateway";

    getAWSApiCallBuilder(
            new DeleteInternetGatewayRequest().withInternetGatewayId(pIgwId),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).deleteInternetGateway(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void attachInternetGateway(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pIgwId,
      final String pVpcId) {

    final String prettyErr = "Problem attaching internet gateway to vpc";

    getAWSApiCallBuilder(
            new AttachInternetGatewayRequest().withInternetGatewayId(pIgwId).withVpcId(pVpcId),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).attachInternetGateway(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, AWSErrorCode.DUPLICATE))
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();

    _resourceObservable.notify(
        new AWSResource(pAWSAccountId, pRegionName, pVpcId, Type.INTERNET_GATEWAY, pIgwId),
        ResourceEvent.ATTACHED);
  }

  public void detachInternetGateway(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pIgwId,
      final String pVpcId) {

    final String prettyErr = "Problem detaching internet gateway from vpc";

    getAWSApiCallBuilder(
            new DetachInternetGatewayRequest().withInternetGatewayId(pIgwId).withVpcId(pVpcId),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).detachInternetGateway(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(
            Set.of(
                CommonErrorCode.NOT_FOUND,
                AWSErrorCode.GATEWAY_NOT_ATTACHED,
                AWSErrorCode.DEPENDENCY_VIOLATION))
        .makeApiCall();
  }

  public Optional<RouteTable> findMainRouteTableForVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId) {

    if (StringUtils.isEmpty(pVpcId)) {
      throw new IllegalArgumentException("`pVpcId` is required");
    }

    final String prettyErr = "Problem finding route table";

    final DescribeRouteTablesResult result =
        getAWSApiCallBuilder(
                new DescribeRouteTablesRequest()
                    .withFilters(
                        new Filter("vpc-id", Collections.singletonList(pVpcId)),
                        new Filter("association.main", Collections.singletonList("true"))),
                getEC2Client(pAWSAccountId, pRegionName)::describeRouteTables,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    if (result.getRouteTables() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getRouteTables().stream().findFirst();
  }

  public void createRouteTableGatewayEntry(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pRouteTableId,
      final String pDestinationCidr,
      final String pIgwId) {

    final String prettyErr = "Problem creating route table route";

    final CreateRouteResult result =
        getAWSApiCallBuilder(
                new CreateRouteRequest()
                    .withRouteTableId(pRouteTableId)
                    .withDestinationCidrBlock(pDestinationCidr)
                    .withGatewayId(pIgwId),
                getEC2Client(pAWSAccountId, pRegionName)::createRoute,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .makeApiCall();

    if (!result.getReturn()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }
  }

  public Subnet createSubnet(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId,
      final String pAvailabilityZone,
      final String pCidrBlock) {

    final String prettyErr = "Problem creating subnet";

    CreateSubnetRequest createSubnetRequest =
        new CreateSubnetRequest()
            .withVpcId(pVpcId)
            .withAvailabilityZone(pAvailabilityZone)
            .withCidrBlock(pCidrBlock);

    return createSubnet(pAWSAccountId, pRegionName, pLogger, prettyErr, createSubnetRequest);
  }

  @VisibleForTesting
  public Subnet createIpv6Subnet(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId,
      final String pAvailabilityZone,
      final String pIpv6CidrBlock) {

    final String prettyErr = "Problem creating Ipv6 subnet";

    CreateSubnetRequest createSubnetRequest =
        new CreateSubnetRequest()
            .withVpcId(pVpcId)
            .withAvailabilityZone(pAvailabilityZone)
            .withIpv6Native(true)
            .withIpv6CidrBlock(pIpv6CidrBlock);

    return createSubnet(pAWSAccountId, pRegionName, pLogger, prettyErr, createSubnetRequest);
  }

  private Subnet createSubnet(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pPrettyErr,
      final CreateSubnetRequest createSubnetRequest) {

    final CreateSubnetResult result =
        getAWSApiCallBuilder(
                createSubnetRequest,
                getEC2Client(pAWSAccountId, pRegionName)::createSubnet,
                pPrettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(AWSErrorCode.CONFLICT))
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .makeApiCall();

    if (result.getSubnet() == null) {
      throw new AWSApiException(pPrettyErr, NDSErrorCode.INTERNAL);
    }

    _resourceObservable.notify(
        new AWSResource(pAWSAccountId, pRegionName, Type.SUBNET, result.getSubnet().getSubnetId()),
        ResourceEvent.CREATED);

    return result.getSubnet();
  }

  public List<AvailabilityZone> findAvailabilityZones(
      final ObjectId pAWSAccountId, final AWSRegionName pRegionName, final Logger pLogger) {

    final String prettyErr = "Problem finding availability zones";

    final DescribeAvailabilityZonesResult result =
        getAWSApiCallBuilder(
                new DescribeAvailabilityZonesRequest(),
                getEC2Client(pAWSAccountId, pRegionName)::describeAvailabilityZones,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    if (result.getAvailabilityZones() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getAvailabilityZones();
  }

  public List<Subnet> findAllSubnetsInVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId) {

    if (StringUtils.isEmpty(pVpcId)) {
      throw new IllegalArgumentException("`pVpcId` is required");
    }

    final String prettyErr = "Problem finding subnets";

    final DescribeSubnetsResult result =
        getAWSApiCallBuilder(
                new DescribeSubnetsRequest().withFilters(new Filter("vpc-id").withValues(pVpcId)),
                getEC2Client(pAWSAccountId, pRegionName)::describeSubnets,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    if (result.getSubnets() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getSubnets();
  }

  public List<Subnet> findSubnets(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final List<String> pSubnetIds) {

    if (pSubnetIds == null || pSubnetIds.isEmpty()) {
      throw new IllegalArgumentException("`pSubnetIds` is required");
    }

    final String prettyErr = "Problem finding subnets";

    final DescribeSubnetsResult result =
        getAWSApiCallBuilder(
                new DescribeSubnetsRequest().withSubnetIds(pSubnetIds),
                getEC2Client(pAWSAccountId, pRegionName)::describeSubnets,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getSubnets() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getSubnets();
  }

  public void deleteSubnet(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pSubnetId) {

    final String prettyErr = "Problem deleting subnet";

    getAWSApiCallBuilder(
            new DeleteSubnetRequest().withSubnetId(pSubnetId),
            (r) -> {
              getEC2Client(pAWSAccountId, pRegionName).deleteSubnet(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, AWSErrorCode.DEPENDENCY_VIOLATION))
        .makeApiCall();
  }

  public void tagResources(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final Collection<String> pResources,
      final Collection<Tag> pTags) {
    final String prettyErr = "Problem creating tags";
    getAWSApiCallBuilder(
            new CreateTagsRequest().withResources(pResources).withTags(pTags),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).createTags(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void acceptResourceShareInvitation(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pResourceShareInvitationArn,
      final Logger pLogger) {
    getAWSApiCallBuilder(
            new AcceptResourceShareInvitationRequest()
                .withResourceShareInvitationArn(pResourceShareInvitationArn)
                .withClientToken(new ObjectId().toHexString()),
            r -> {
              getRamClient(pAWSAccountId, pRegionName).acceptResourceShareInvitation(r);
              return null;
            },
            "Problem accepting resource share invitation",
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void rejectResourceShareInvitation(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pResourceShareInvitationArn,
      final Logger pLogger) {
    getAWSApiCallBuilder(
            new RejectResourceShareInvitationRequest()
                .withResourceShareInvitationArn(pResourceShareInvitationArn)
                .withClientToken(new ObjectId().toHexString()),
            r -> {
              getRamClient(pAWSAccountId, pRegionName).rejectResourceShareInvitation(r);
              return null;
            },
            "Problem rejecting resource share invitation",
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public GetResourceShareInvitationsResult getResourceShareInvitations(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      String pResourceShareArn,
      final Logger pLogger) {
    List<String> resourceShareArns = new ArrayList<>();
    resourceShareArns.add(pResourceShareArn);

    GetResourceShareInvitationsRequest request =
        new GetResourceShareInvitationsRequest()
            .withResourceShareArns(resourceShareArns)
            // This is the maximum value for this field
            // https://docs.aws.amazon.com/ram/latest/APIReference/API_GetResourceShareInvitations.html#API_GetResourceShareInvitations_RequestBody
            .withMaxResults(500);

    return getAWSApiCallBuilder(
            request,
            getRamClient(pAWSAccountId, pRegionName)::getResourceShareInvitations,
            "Problem getting resource share invitations",
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void addElbTags(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final Collection<String> pResourceArns,
      final Collection<com.amazonaws.services.elasticloadbalancingv2.model.Tag> pTags) {
    final String prettyErr = "Problem adding tags";
    getAWSApiCallBuilder(
            new AddTagsRequest().withResourceArns(pResourceArns).withTags(pTags),
            r -> {
              getElasticLoadBalancingClient(pAWSAccountId, pRegionName).addTags(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();
  }

  /**
   * Finds ENIs by their IDs.
   *
   * @param awsAccountId The AWS account ID.
   * @param regionName The name of the AWS Region.
   * @param logger The Logger of the current consumer of the method.
   * @param eniIds The list of ENI IDs to find.
   * @return A list of NetworkInterface objects corresponding to the provided ENI IDs.
   */
  public Collection<NetworkInterface> findNetworkInterfaces(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final List<String> eniIds) {

    if (eniIds == null || eniIds.isEmpty()) {
      return List.of();
    }

    return makeFindNetworkInterfacesRequest(
            awsAccountId,
            regionName,
            logger,
            new DescribeNetworkInterfacesRequest().withNetworkInterfaceIds(eniIds))
        .getNetworkInterfaces();
  }

  /**
   * Finds ENIs by VPC ID and description.
   *
   * @param awsAccountId The AWS account ID.
   * @param regionName The name of the AWS Region.
   * @param logger The Logger of the current consumer of the method.
   * @param vpcId The ID of the VPC to which the ENIs belong.
   * @param description The description of the ENIs to find.
   * @return A list of NetworkInterface objects that match the provided VPC ID and description.
   */
  public Collection<NetworkInterface> findNetworkInterfaces(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final String vpcId,
      final String description) {

    return makeFindNetworkInterfacesRequest(
            awsAccountId,
            regionName,
            logger,
            new DescribeNetworkInterfacesRequest()
                .withFilters(
                    new Filter("description", List.of(description)),
                    new Filter("vpc-id", List.of(vpcId))))
        .getNetworkInterfaces();
  }

  private DescribeNetworkInterfacesResult makeFindNetworkInterfacesRequest(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final DescribeNetworkInterfacesRequest request) {

    final String prettyErr = "Problem finding network interfaces";

    final var result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(awsAccountId, regionName)::describeNetworkInterfaces,
                prettyErr,
                NDSErrorCode.INTERNAL,
                logger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getNetworkInterfaces() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result;
  }

  /**
   * Create an IPv6 ENI
   *
   * @param awsAccountId The AWS account ID.
   * @param regionName The name of the AWS Region.
   * @param logger The Logger of the current consumer of the method.
   * @param subnetId The subnet ID of an AWS VPC.
   * @param securityGroupId The security group of an AWS VPC.
   * @param description The description for the network interface.
   * @param tags The tags to apply to the network interface.
   * @return The network interface that was created
   */
  public NetworkInterface createIpv6Eni(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final String subnetId,
      final String securityGroupId,
      final String description,
      final List<Tag> tags) {

    if (StringUtils.isEmpty(subnetId)) {
      throw new IllegalArgumentException("`subnetId` is required");
    }

    if (StringUtils.isEmpty(securityGroupId)) {
      throw new IllegalArgumentException("`securityGroupId` is required");
    }

    final String prettyErr = "Problem creating IPv6 ENI";

    final CreateNetworkInterfaceRequest request =
        new CreateNetworkInterfaceRequest()
            .withSubnetId(subnetId)
            .withGroups(securityGroupId)
            .withIpv6AddressCount(1)
            .withEnablePrimaryIpv6(true)
            .withDescription(description);

    if (!tags.isEmpty()) {
      request.withTagSpecifications(
          new TagSpecification().withResourceType(ResourceType.NetworkInterface).withTags(tags));
    }

    final CreateNetworkInterfaceResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(awsAccountId, regionName)::createNetworkInterface,
                prettyErr,
                NDSErrorCode.INTERNAL,
                logger)
            .makeApiCall();

    if (result == null
        || result.getNetworkInterface() == null
        || result.getNetworkInterface().getNetworkInterfaceId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getNetworkInterface();
  }

  /**
   * Deletes an ENI
   *
   * @param awsAccountId The AWS account ID.
   * @param regionName The name of the AWS Region.
   * @param logger The Logger of the current consumer of the method.
   * @param eniId The ENI ID to be deleted.
   */
  public void deleteEni(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final String eniId) {

    final String prettyErr = "Problem deleting ENI";

    getAWSApiCallBuilder(
            new DeleteNetworkInterfaceRequest().withNetworkInterfaceId(eniId),
            getEC2Client(awsAccountId, regionName)::deleteNetworkInterface,
            prettyErr,
            NDSErrorCode.INTERNAL,
            logger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  /**
   * Submits a request to attach a given network interface to a given ec2 instance.
   *
   * @param awsAccountId the AWS account in which the instance resides
   * @param regionName the AWS region in which the instance is deployed
   * @param logger a Logger to record errors
   * @param instanceId the ec2 instance to attach to
   * @param eniId the network interface to attach
   * @param deviceIndex the index of the device for the network interface attachment
   * @return the identifier of the network attachment
   */
  public String attachNetworkInterface(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final String instanceId,
      final String eniId,
      final int deviceIndex) {
    final var req =
        new AttachNetworkInterfaceRequest()
            .withInstanceId(instanceId)
            .withNetworkInterfaceId(eniId)
            .withDeviceIndex(deviceIndex);

    return getAWSApiCallBuilder(
            req,
            getEC2Client(awsAccountId, regionName)::attachNetworkInterface,
            "Problem attaching ENI to instance",
            NDSErrorCode.INTERNAL,
            logger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall()
        .getAttachmentId();
  }

  /**
   * Finds security groups by their IDs.
   *
   * @param awsAccountId The AWS account ID.
   * @param regionName The name of the AWS Region.
   * @param logger The Logger of the current consumer of the method.
   * @param securityGroupId The ID of the security group to find.
   * @return A list of SecurityGroup objects corresponding to the provided security group IDs.
   */
  public Optional<SecurityGroup> findSecurityGroup(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final String securityGroupId) {

    final DescribeSecurityGroupsResult result =
        makeFindSecurityGroupsRequest(
            awsAccountId,
            regionName,
            logger,
            new DescribeSecurityGroupsRequest().withGroupIds(List.of(securityGroupId)));

    return result.getSecurityGroups().stream().findFirst();
  }

  /**
   * Finds a security group by its name and VPC ID.
   *
   * @param awsAccountId The AWS account ID.
   * @param regionName The name of the AWS Region.
   * @param logger The Logger of the current consumer of the method.
   * @param vpcId The ID of the VPC to which the security group belongs.
   * @param securityGroupName The name of the security group to find.
   * @return An Optional containing the SecurityGroup if found, otherwise empty.
   */
  public Optional<SecurityGroup> findSecurityGroup(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final String vpcId,
      final String securityGroupName) {

    if (StringUtils.isEmpty(vpcId)) {
      throw new IllegalArgumentException("`vpcId` is required");
    }

    final var result =
        makeFindSecurityGroupsRequest(
            awsAccountId,
            regionName,
            logger,
            new DescribeSecurityGroupsRequest()
                .withFilters(
                    new Filter("vpc-id").withValues(vpcId),
                    new Filter("group-name").withValues(securityGroupName)));

    return result.getSecurityGroups().stream().findFirst();
  }

  private DescribeSecurityGroupsResult makeFindSecurityGroupsRequest(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final DescribeSecurityGroupsRequest request) {

    final String prettyErr = "Problem finding security groups";

    final DescribeSecurityGroupsResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(awsAccountId, regionName)::describeSecurityGroups,
                prettyErr,
                NDSErrorCode.INTERNAL,
                logger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getSecurityGroups() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result;
  }

  /**
   * Deletes a security group.
   *
   * @param awsAccountId The AWS account ID.
   * @param regionName The name of the AWS Region.
   * @param logger The Logger of the current consumer of the method.
   * @param securityGroupId The ID of the security group to be deleted.
   */
  public void deleteSecurityGroup(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final String securityGroupId) {
    final String prettyErr = "Problem deleting security group";

    getAWSApiCallBuilder(
            new DeleteSecurityGroupRequest().withGroupId(securityGroupId),
            getEC2Client(awsAccountId, regionName)::deleteSecurityGroup,
            prettyErr,
            NDSErrorCode.INTERNAL,
            logger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void addSecurityGroupEgressRules(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pSecurityGroupId,
      final Collection<IpPermission> pIpPermissions) {
    final String prettyErr = "Problem adding security group egress rules";
    getAWSApiCallBuilder(
            new AuthorizeSecurityGroupEgressRequest()
                .withGroupId(pSecurityGroupId)
                .withIpPermissions(pIpPermissions),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).authorizeSecurityGroupEgress(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(
            Set.of(
                CommonErrorCode.NOT_FOUND,
                AWSErrorCode.DUPLICATE,
                AWSErrorCode.NO_SECURITY_GROUP_FOUND))
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();
  }

  public void removeSecurityGroupEgressRules(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pSecurityGroupId,
      final Collection<IpPermission> pIpPermissions) {

    final String prettyErr = "Problem remove security group ingress rules";

    getAWSApiCallBuilder(
            new RevokeSecurityGroupEgressRequest()
                .withGroupId(pSecurityGroupId)
                .withIpPermissions(pIpPermissions),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).revokeSecurityGroupEgress(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void addSecurityGroupIngressRules(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pSecurityGroupId,
      final Collection<IpPermission> pIpPermissions) {

    final String prettyErr = "Problem adding security group ingress rules";

    getAWSApiCallBuilder(
            new AuthorizeSecurityGroupIngressRequest()
                .withGroupId(pSecurityGroupId)
                .withIpPermissions(pIpPermissions),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).authorizeSecurityGroupIngress(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(
            Set.of(
                CommonErrorCode.NOT_FOUND,
                AWSErrorCode.DUPLICATE,
                AWSErrorCode.NO_SECURITY_GROUP_FOUND))
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();
  }

  public void removeSecurityGroupIngressRules(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pSecurityGroupId,
      final Collection<IpPermission> pIpPermissions) {

    final String prettyErr = "Problem remove security group ingress rules";

    getAWSApiCallBuilder(
            new RevokeSecurityGroupIngressRequest()
                .withGroupId(pSecurityGroupId)
                .withIpPermissions(pIpPermissions),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).revokeSecurityGroupIngress(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public String createSecurityGroup(
      final String pVpcId,
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pSecurityGroupName,
      final String pDescription,
      final Collection<Tag> pTags,
      final Logger pLogger) {

    if (StringUtils.isEmpty(pVpcId)) {
      throw new IllegalArgumentException("`pVpcId` is required");
    }

    final String prettyErr =
        String.format("Problem creating security group %s", pSecurityGroupName);

    final CreateSecurityGroupRequest request =
        new CreateSecurityGroupRequest()
            .withVpcId(pVpcId)
            .withGroupName(pSecurityGroupName)
            .withDescription(pDescription);

    if (!pTags.isEmpty()) {
      request.withTagSpecifications(
          new TagSpecification().withResourceType(ResourceType.SecurityGroup).withTags(pTags));
    }

    final CreateSecurityGroupResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegionName)::createSecurityGroup,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    if (result.getGroupId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getGroupId();
  }

  public Optional<SecurityGroup> findDefaultSecurityGroupForVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId) {

    if (StringUtils.isEmpty(pVpcId)) {
      throw new IllegalArgumentException("`pVpcId` is required");
    }

    final String prettyErr = "Problem finding default security group";

    final DescribeSecurityGroupsResult result =
        getAWSApiCallBuilder(
                new DescribeSecurityGroupsRequest()
                    .withFilters(new Filter("vpc-id").withValues(pVpcId)),
                getEC2Client(pAWSAccountId, pRegionName)::describeSecurityGroups,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    if (result.getSecurityGroups() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getSecurityGroups().stream()
        .filter(sg -> sg.getGroupName().equals("default"))
        .findFirst();
  }

  public String getConsoleOutput(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pInstanceId) {
    final GetConsoleOutputRequest req =
        new GetConsoleOutputRequest().withInstanceId(pInstanceId).withLatest(true);

    final GetConsoleOutputResult res =
        getAWSApiCallBuilder(
                req,
                getEC2Client(pAWSAccountId, pRegionName)::getConsoleOutput,
                "Problem getting console output",
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();
    return res.getDecodedOutput();
  }

  public Volume createEBSVolume(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pSnapshotId,
      final VolumeType pVolumeType,
      final boolean pVolumeEncrypted,
      final int pSizeGb,
      final int pIops,
      final int pThroughput,
      final String pAvailabilityZone,
      final List<Tag> pTags,
      final boolean pUseCmk) {
    final String prettyErr = "Problem creating EBS volume";

    final CreateVolumeRequest request =
        new CreateVolumeRequest()
            .withVolumeType(pVolumeType)
            .withAvailabilityZone(pAvailabilityZone)
            .withSize(pSizeGb)
            .withTagSpecifications(
                new TagSpecification().withResourceType("volume").withTags(pTags))
            .withEncrypted(pVolumeEncrypted)
            .withSnapshotId(pSnapshotId);

    if (pVolumeType == VolumeType.Io1
        || pVolumeType == VolumeType.Io2
        || pVolumeType == VolumeType.Gp3) {
      request.withIops(pIops);
    }

    if (pVolumeType == VolumeType.Gp3) {
      request.withThroughput(pThroughput);
    }
    // We should only attach KmsKeyId to req when it's a cluster encrypted before.
    if (pVolumeEncrypted && pUseCmk) {
      final String pKmsKeyId = generateCmk(pRegionName, pAWSAccountId);
      request.withKmsKeyId(pKmsKeyId);
    }

    final CreateVolumeResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegionName)::createVolume,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .makeApiCall();

    if (result.getVolume() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    _resourceObservable.notify(
        new AWSResource(
            pAWSAccountId, pRegionName, Type.EBS_VOLUME, result.getVolume().getVolumeId()),
        ResourceEvent.CREATED);

    return result.getVolume();
  }

  public String generateCmk(final AWSRegionName pAwsRegionName, final ObjectId awsAccountId) {
    final String awsKmsKeyArnTemplate =
        pAwsRegionName.isUSGovRegion()
            ? _appSettings.getBackupFasterRestoreKmsGovKeyArnTemplate()
            : _appSettings.getBackupFasterRestoreKmsKeyArnTemplate();

    if (awsKmsKeyArnTemplate == null) return null;
    final boolean isLocal = _appSettings.getAppEnv().isLocal();
    return String.format(
        awsKmsKeyArnTemplate,
        pAwsRegionName.getValue(),
        getAwsAccountString(isLocal, getAWSAccount(awsAccountId)));
  }

  public List<Volume> describeEBSVolumes(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final List<String> pEBSVolumeIds) {

    final String prettyErr = "Problem describing EBS Volume";
    final DescribeVolumesResult result =
        getAWSApiCallBuilder(
                new DescribeVolumesRequest().withVolumeIds(pEBSVolumeIds),
                getEC2Client(pAWSAccountId, pRegionName)::describeVolumes,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    return result.getVolumes();
  }

  public String getAccountId(
      final ObjectId pAWSAccountId, final AWSRegionName pRegionName, final Logger pLogger) {
    return getAccountId(pAWSAccountId, pRegionName, null, pLogger);
  }

  public String getAccountId(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final List<AWSApiCredentialsProviderImplementations> pProviderImplementations,
      final Logger pLogger) {
    final GetCallerIdentityRequest req = new GetCallerIdentityRequest();
    final AWSRegionName regionForStsClient;
    if (pRegionName.isUSGovRegion()) {
      regionForStsClient = AWSRegionName.US_GOV_EAST_1;
    } else {
      regionForStsClient = AWSRegionName.US_EAST_1;
    }
    final GetCallerIdentityResult result =
        getAWSApiCallBuilder(
                req,
                getSTSClient(pAWSAccountId, regionForStsClient, pProviderImplementations)
                    ::getCallerIdentity,
                "Problem looking up account id",
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();
    return result.getAccount();
  }

  public Volume findInstanceRootVolume(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pInstanceId) {
    final DescribeVolumesRequest req =
        new DescribeVolumesRequest()
            .withFilters(
                new Filter("attachment.instance-id", List.of(pInstanceId)),
                new Filter("attachment.device", AWS_ROOT_DEVICE_NAMES));

    final DescribeVolumesResult result =
        getAWSApiCallBuilder(
                req,
                getEC2Client(pAWSAccountId, pRegionName)::describeVolumes,
                "Problem finding root volume",
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getVolumes() == null || result.getVolumes().isEmpty()) {
      throw new AWSApiException("Problem finding root volume", NDSErrorCode.INTERNAL);
    }

    return result.getVolumes().get(0);
  }

  public Volume findEBSVolume(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEBSVolumeId) {
    if (StringUtils.isEmpty(pEBSVolumeId)) {
      throw new IllegalArgumentException("`pEBSVolumeId` is required");
    }

    final String prettyErr = "Problem finding EBS volume";

    final DescribeVolumesResult result =
        getAWSApiCallBuilder(
                new DescribeVolumesRequest().withVolumeIds(pEBSVolumeId),
                getEC2Client(pAWSAccountId, pRegionName)::describeVolumes,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getVolumes() == null || result.getVolumes().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getVolumes().get(0);
  }

  public Volume findEBSVolumeByNameTag(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pNameTag) {
    if (StringUtils.isEmpty(pNameTag)) {
      throw new IllegalArgumentException("`pNameTag` is required");
    }

    final DescribeVolumesResult result =
        findEBSVolumeByNameTagInner(pAWSAccountId, pRegionName, pLogger, pNameTag);

    if (result.getVolumes() == null || result.getVolumes().isEmpty()) {
      return null;
    }

    return result.getVolumes().get(0);
  }

  public DescribeVolumesResult findEBSVolumeByNameTagInner(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pNameTag) {
    final String prettyErr = "Problem finding EBS volume";

    return getAWSApiCallBuilder(
            new DescribeVolumesRequest().withFilters(new Filter("tag:name", List.of(pNameTag))),
            getEC2Client(pAWSAccountId, pRegionName)::describeVolumes,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void findEBSVolumesAndApply(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final int pMaxResultSize,
      final Logger pLogger,
      final Consumer<DescribeVolumesResult> resultConsumer) {
    final String prettyError =
        String.format(
            "Problem retrieving EBS volumes in Region (%s), Account Id (%s)",
            pRegion.getName(), pAWSAccountId.toString());

    final DescribeVolumesRequest request =
        new DescribeVolumesRequest().withMaxResults(pMaxResultSize);
    DescribeVolumesResult result;
    String nextToken = null;
    do {
      if (nextToken != null) {
        request.withNextToken(nextToken);
      }
      result =
          getAWSApiCallBuilder(
                  request,
                  getEC2Client(pAWSAccountId, pRegion)::describeVolumes,
                  prettyError,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
              .makeApiCall();
      resultConsumer.accept(result);
      nextToken = result.getNextToken();
    } while (nextToken != null);
  }

  public String findEBSVolumesAndApplyForNextToken(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final int pMaxResultSize,
      final String pNextToken,
      final Logger pLogger,
      final Consumer<DescribeVolumesResult> resultConsumer) {
    // This method should not be called with a null nextToken
    // because a null nextToken indicates there are no more pages of results
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final String prettyError =
        String.format(
            "Problem retrieving EBS volumes in Region (%s), Account Id (%s)",
            pRegion.getName(), pAWSAccountId.toString());

    final DescribeVolumesRequest request =
        new DescribeVolumesRequest().withMaxResults(pMaxResultSize);

    if (!pNextToken.equals(AWSApiSvc.TOKEN_START)) {
      request.withNextToken(pNextToken);
    }

    final DescribeVolumesResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegion)::describeVolumes,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();
    resultConsumer.accept(result);

    final String newNextToken = result.getNextToken();
    return (newNextToken != null) ? newNextToken : TOKEN_DONE;
  }

  public void deleteEBSVolume(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEBSVolumeId) {
    final String prettyErr = "Problem deleting EBS volume";

    getAWSApiCallBuilder(
            new DeleteVolumeRequest().withVolumeId(pEBSVolumeId),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).deleteVolume(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void modifyEBSVolume(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEBSVolumeId,
      final double pSize,
      final int pIops,
      final VolumeType pVolumeType,
      final int pThroughput) {
    final String prettyErr = "Problem modifying EBS volume";

    if (pVolumeType == VolumeType.Gp3) {
      if (pIops < AWSNDSDefaults.MINIMUM_GP3_IOPS) {
        throw new IllegalArgumentException(
            String.format(
                "Minimum GP3 IOPS cannot be below %s. Saw %s",
                AWSNDSDefaults.MINIMUM_GP3_IOPS, pIops));
      }

      if (pThroughput < AWSNDSDefaults.MINIMUM_GP3_THROUGHPUT) {
        throw new IllegalArgumentException(
            String.format(
                "Minimum GP3 throughput cannot be below %s. Saw %s",
                AWSNDSDefaults.MINIMUM_GP3_THROUGHPUT, pThroughput));
      }
    }

    final ModifyVolumeRequest request =
        new ModifyVolumeRequest()
            .withVolumeId(pEBSVolumeId)
            .withVolumeType(pVolumeType)
            .withSize((int) pSize);

    // Gp2 volumes have a fixed IOPS:GB ratio
    if (pVolumeType != VolumeType.Gp2) {
      request.withIops(pIops);
    }

    if (pVolumeType == VolumeType.Gp3) {
      request.withThroughput(pThroughput);
    }

    getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::modifyVolume,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();
  }

  public DescribeVolumesModificationsResult getVolumeModificationResult(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEBSVolumnId) {
    final String prettyErr = "Problem modifying EBS volume";

    return getAWSApiCallBuilder(
            new DescribeVolumesModificationsRequest().withVolumeIds(pEBSVolumnId),
            getEC2Client(pAWSAccountId, pRegionName)::describeVolumesModifications,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public VolumeStatusItem getEBSVolumeStatus(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEBSVolumeId) {

    return getEBSVolumeStatuses(pAWSAccountId, pRegionName, pLogger, List.of(pEBSVolumeId)).get(0);
  }

  public List<VolumeStatusItem> getEBSVolumeStatuses(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final List<String> pEBSVolumeIds) {

    if (pEBSVolumeIds.stream().anyMatch(StringUtils::isEmpty)) {
      throw new IllegalArgumentException("`pEBSVolumeIds` cannot include empty string(s)");
    }

    final String prettyErr = "Problem finding status for EBS volume";

    final DescribeVolumeStatusResult result =
        getAWSApiCallBuilder(
                new DescribeVolumeStatusRequest().withVolumeIds(pEBSVolumeIds),
                getEC2Client(pAWSAccountId, pRegionName)::describeVolumeStatus,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result.getVolumeStatuses().isEmpty()) {
      throw new AWSApiException(prettyErr, AWSErrorCode.NO_VOLUME_STATUS_FOUND);
    }

    return result.getVolumeStatuses();
  }

  public String copyEbsSnapshot(
      final ObjectId pAWSAccountId,
      final AWSRegionName pSourceRegion,
      final AWSRegionName pDestinationRegion,
      final String sourceSnapshotId,
      final Logger pLogger,
      final String pSnapshotDescription,
      final String pKmsKeyId,
      final List<Tag> pTags) {

    final CopySnapshotRequest request =
        new CopySnapshotRequest()
            .withTagSpecifications(
                new TagSpecification().withResourceType("snapshot").withTags(pTags))
            .withDescription(pSnapshotDescription)
            .withSourceSnapshotId(sourceSnapshotId)
            .withDestinationRegion(pDestinationRegion.getValue())
            .withSourceRegion(pSourceRegion.getValue())
            .withEncrypted(true)
            .withKmsKeyId(pKmsKeyId);
    final String prettyErr =
        String.format(
            "Problem copying EBS snapshot with ID (%s) to Region (%s)",
            sourceSnapshotId, pDestinationRegion);

    final CopySnapshotResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pDestinationRegion)::copySnapshot,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pDestinationRegion)
            .makeApiCall();
    if (result.getSnapshotId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    _resourceObservable.notify(
        new AWSResource(pAWSAccountId, pDestinationRegion, Type.SNAPSHOT, result.getSnapshotId()),
        ResourceEvent.CREATED);

    return result.getSnapshotId();
  }

  public long calculateIncrementalSnapshotSizeInBytes(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pFirstSnapshotId,
      final String pSecondSnapshotId,
      final Logger pLogger) {

    ListChangedBlocksRequest request =
        new ListChangedBlocksRequest()
            .withFirstSnapshotId(pFirstSnapshotId)
            .withSecondSnapshotId(pSecondSnapshotId);
    final String prettyErr =
        String.format(
            "Problem calculating EBS snapshot diff in Region (%s), FirstSnapshotId (%s),"
                + " SecondSnapshotId (%s)",
            pRegionName, pFirstSnapshotId, pSecondSnapshotId);

    long snapshotSizeInBytes = 0;
    String nextToken = null;

    do {
      request.withNextToken(nextToken);
      final ListChangedBlocksResult result =
          getAWSApiCallBuilder(
                  request,
                  getEbsClient(pAWSAccountId, pRegionName)::listChangedBlocks,
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .makeApiCall();
      snapshotSizeInBytes += (long) result.getChangedBlocks().size() * result.getBlockSize();
      nextToken = result.getNextToken();
    } while (nextToken != null);

    return snapshotSizeInBytes;
  }

  /**
   * Calculates the total snapshot size in bytes for a single snapshot. Splits listChangedBlocks
   * requests across jobs that get the changed blocks across different block index intervals. Uses
   * Java parallel streams to assign the jobs concurrently
   *
   * @param pFirstSnapshotId identifies the snapshot which size will be computed
   * @param pFirstSnapshotDiskSize disk size of first snapshot to determine search intervals
   * @return total size of snapshot
   */
  public long calculateSnapshotSizeInBytesParallel(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pFirstSnapshotId,
      final long pFirstSnapshotDiskSize,
      final int pBlocksPerJob,
      final Logger pLogger) {
    return calculateIncrementalSnapshotSizeInBytesParallel(
        pAWSAccountId,
        pRegionName,
        pFirstSnapshotId,
        null,
        pFirstSnapshotDiskSize,
        pBlocksPerJob,
        pLogger);
  }

  /**
   * Calculates the incremental size in bytes between 2 snapshots. Splits listChangedBlocks requests
   * across jobs that get the changed blocks across different block index intervals. Uses Java
   * parallel streams to assign the jobs concurrently
   *
   * @param pFirstSnapshotId identifies the newer snapshot to be compared
   * @param pSecondSnapshotId identifies the older snapshot to be compared
   * @param pFirstSnapshotDiskSize disk size of first snapshot to determine search intervals
   * @return incremental size difference of firstSnapshot compared to secondSnapshot in bytes
   */
  public long calculateIncrementalSnapshotSizeInBytesParallel(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pFirstSnapshotId,
      final String pSecondSnapshotId,
      final long pFirstSnapshotDiskSize,
      final int pBlocksPerJob,
      final Logger pLogger) {

    // use the disk size of the snapshot as the upper bound for search comparison.
    final int totalBlocks =
        (int)
            (Units.convert(pFirstSnapshotDiskSize, Units.GIGABYTES, Units.BYTES)
                / SNAPSHOT_BLOCK_SIZE_BYTES);

    final int numJobs = (int) Math.ceil((double) totalBlocks / pBlocksPerJob);
    AtomicInteger jobsCompleted = new AtomicInteger();
    // parallelize paginated output by having each thread scan an even interval:
    return IntStream.range(0, numJobs)
        .parallel()
        .mapToLong(
            threadIndex -> {
              // set interval bounds for thread
              int startingBlockIndex = (threadIndex * pBlocksPerJob);
              int endingBlockIndex =
                  (threadIndex == numJobs - 1) ? totalBlocks : startingBlockIndex + pBlocksPerJob;

              // calculate full size of snapshot if no second snapshot is provided
              long sizeForInterval;
              if (pSecondSnapshotId == null) {
                sizeForInterval =
                    calculateSnapshotSizeInBytesForInterval(
                        pAWSAccountId,
                        pRegionName,
                        pFirstSnapshotId,
                        startingBlockIndex,
                        endingBlockIndex,
                        pLogger);
              } else {
                sizeForInterval =
                    calculateIncrementalSnapshotSizeInBytesForInterval(
                        pAWSAccountId,
                        pRegionName,
                        pFirstSnapshotId,
                        pSecondSnapshotId,
                        startingBlockIndex,
                        endingBlockIndex,
                        pLogger);
              }
              jobsCompleted.getAndIncrement();
              LOG.info(
                  "Completed snapshot size calculation for snapshot {} in index range {} - {}. Job"
                      + " Index {}, {} jobs completed out of {} total jobs",
                  pFirstSnapshotId,
                  startingBlockIndex,
                  endingBlockIndex,
                  threadIndex,
                  jobsCompleted,
                  numJobs);
              return sizeForInterval;
            })
        .sum();
  }

  private long calculateIncrementalSnapshotSizeInBytesForInterval(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pFirstSnapshotId,
      final String pSecondSnapshotId,
      final int pStartingBlockIndex,
      final int pEndingBlockIndex,
      final Logger pLogger) {
    ListChangedBlocksRequest request =
        new ListChangedBlocksRequest()
            .withFirstSnapshotId(pFirstSnapshotId)
            .withSecondSnapshotId(pSecondSnapshotId)
            .withStartingBlockIndex(pStartingBlockIndex)
            .withMaxResults(10000);
    final String prettyErr =
        String.format(
            "Problem calculating EBS snapshot diff in Region (%s), FirstSnapshotId (%s),"
                + " SecondSnapshotId (%s)",
            pRegionName, pFirstSnapshotId, pSecondSnapshotId);

    long totalSize = 0;
    ListChangedBlocksResult response;
    int lastBlockIndex;
    do {
      response =
          getAWSApiCallBuilder(
                  request,
                  getEbsClient(pAWSAccountId, pRegionName)::listChangedBlocks,
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .makeApiCall();

      // get number of blocks less than interval upper bound
      if (response.getChangedBlocks().isEmpty()) return totalSize;
      int numChangedBlocksInInterval = 0;
      for (final ChangedBlock block : response.getChangedBlocks()) {
        if (block.getBlockIndex() < pEndingBlockIndex) {
          numChangedBlocksInInterval++;
        } else {
          break;
        }
      }
      totalSize += (long) numChangedBlocksInInterval * response.getBlockSize();

      // max results can be smaller for the last page (with min value 100)
      lastBlockIndex =
          response.getChangedBlocks().get(response.getChangedBlocks().size() - 1).getBlockIndex();
      request.setMaxResults(Math.min(Math.max(100, pEndingBlockIndex - lastBlockIndex), 10000));

      // paginate until response contains endingBlockIndex
      request.setNextToken(response.getNextToken());
    } while (response.getNextToken() != null && lastBlockIndex < pEndingBlockIndex);
    return totalSize;
  }

  private long calculateSnapshotSizeInBytesForInterval(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pSnapshotId,
      final int pStartingBlockIndex,
      final int pEndingBlockIndex,
      final Logger pLogger) {
    final ListSnapshotBlocksRequest request =
        new ListSnapshotBlocksRequest()
            .withSnapshotId(pSnapshotId)
            .withStartingBlockIndex(pStartingBlockIndex)
            .withMaxResults(10000);
    final String prettyErr =
        String.format(
            "Problem calculating EBS snapshot size in bytes. Region (%s), SnapshotId (%s)",
            pRegionName, pSnapshotId);

    ListSnapshotBlocksResult response;
    long totalSize = 0;
    int lastBlockIndex;
    do {
      response =
          getAWSApiCallBuilder(
                  request,
                  getEbsClient(pAWSAccountId, pRegionName)::listSnapshotBlocks,
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .makeApiCall();

      // get number of blocks less than interval upper bound
      if (response.getBlocks().isEmpty()) return totalSize;
      int numChangedBlocksInInterval = 0;
      for (final Block block : response.getBlocks()) {
        if (block.getBlockIndex() < pEndingBlockIndex) {
          numChangedBlocksInInterval++;
        } else {
          break;
        }
      }
      totalSize += (long) numChangedBlocksInInterval * response.getBlockSize();

      // max results can be smaller for the last page (with min value 100)
      lastBlockIndex = response.getBlocks().get(response.getBlocks().size() - 1).getBlockIndex();
      request.setMaxResults(Math.min(Math.max(100, pEndingBlockIndex - lastBlockIndex), 10000));

      // paginate until response contains endingBlockIndex
      request.setNextToken(response.getNextToken());
    } while (response.getNextToken() != null && lastBlockIndex < pEndingBlockIndex);
    return totalSize;
  }

  public long calculateSnapshotSizeInBytes(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pSnapshotId,
      final Logger pLogger) {

    final ListSnapshotBlocksRequest request =
        new ListSnapshotBlocksRequest().withSnapshotId(pSnapshotId);
    final String prettyErr =
        String.format(
            "Problem calculating EBS snapshot size in bytes. Region (%s), SnapshotId (%s)",
            pRegionName, pSnapshotId);

    long snapshotSizeInBytes = 0;
    String nextToken = null;

    do {
      request.withNextToken(nextToken);
      final ListSnapshotBlocksResult result =
          getAWSApiCallBuilder(
                  request,
                  getEbsClient(pAWSAccountId, pRegionName)::listSnapshotBlocks,
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .makeApiCall();
      snapshotSizeInBytes += (long) result.getBlocks().size() * result.getBlockSize();
      nextToken = result.getNextToken();
    } while (nextToken != null);
    return snapshotSizeInBytes;
  }

  public Snapshot createEbsSnapshot(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEBSVolumeId,
      final String pSnapshotDescription,
      final List<Tag> pTags) {

    final CreateSnapshotRequest request =
        new CreateSnapshotRequest()
            .withTagSpecifications(
                new TagSpecification().withResourceType("snapshot").withTags(pTags))
            .withVolumeId(pEBSVolumeId)
            .withDescription(pSnapshotDescription);

    final String prettyErr =
        String.format(
            "Problem creating EBS snapshot with Volume ID (%s) in Region (%s)",
            pEBSVolumeId, pRegionName);

    final CreateSnapshotResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegionName)::createSnapshot,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .makeApiCall();

    if (result.getSnapshot() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    _resourceObservable.notify(
        new AWSResource(
            pAWSAccountId, pRegionName, Type.SNAPSHOT, result.getSnapshot().getSnapshotId()),
        ResourceEvent.CREATED);

    return result.getSnapshot();
  }

  public Snapshot findEbsSnapshot(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEBSSnapshotId) {
    if (StringUtils.isEmpty(pEBSSnapshotId)) {
      throw new IllegalArgumentException("pEBSSnapshotId is required");
    }

    final String prettyErr =
        String.format(
            "Problem finding EBS snapshot with Snapshot Id (%s) in Region (%s)",
            pEBSSnapshotId, pRegionName);

    final DescribeSnapshotsResult result =
        getAWSApiCallBuilder(
                new DescribeSnapshotsRequest().withSnapshotIds(pEBSSnapshotId),
                getEC2Client(pAWSAccountId, pRegionName)::describeSnapshots,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getSnapshots().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getSnapshots().get(0);
  }

  /*
  This function should returns at most 1 snapshot that matches the filter.
  */
  public Optional<Snapshot> findEbsSnapshotBySnapshotTag(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final ObjectId snapshotId) {
    final Filter filter = new Filter("tag:snapshotId", List.of(snapshotId.toString()));

    final String prettyErr =
        String.format(
            "Problem querying for EBS snapshot with in Region (%s) with filter (%s)",
            pRegionName, filter);

    final DescribeSnapshotsResult result =
        getAWSApiCallBuilder(
                new DescribeSnapshotsRequest().withFilters(filter),
                getEC2Client(pAWSAccountId, pRegionName)::describeSnapshots,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getSnapshots().isEmpty()) {
      return Optional.empty();
    } else if (result.getSnapshots().size() > 1) {
      throw new IllegalStateException(
          "Found multiple AWS snapshots when querying with snapshotId " + snapshotId);
    }

    return Optional.of(result.getSnapshots().get(0));
  }

  /**
   * Find ebs snapshots that in either completed or error status
   *
   * @param pAWSAccountId
   * @param pRegion
   * @param ownerId
   * @param pMaxResultSize
   * @param pLogger
   * @param resultConsumer with snapshots stored
   */
  public void findSnapshotsAndApply(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final String ownerId,
      final int pMaxResultSize,
      final Logger pLogger,
      final Consumer<DescribeSnapshotsResult> resultConsumer) {
    final String prettyErr =
        String.format(
            "Problem retrieving EBS snapshots in Region (%s), Account Id (%s), and AWS Owner Id"
                + " (%s)",
            pRegion.getName(), pAWSAccountId.toString(), ownerId);

    // exclude pending snapshots
    final Filter filter = new Filter("status", List.of("completed", "error"));

    final DescribeSnapshotsRequest request =
        new DescribeSnapshotsRequest()
            .withOwnerIds(ownerId)
            .withMaxResults(pMaxResultSize)
            .withFilters(filter);

    DescribeSnapshotsResult result;
    String nextToken = null;
    do {
      if (nextToken != null) {
        request.withNextToken(nextToken);
      }
      result =
          getAWSApiCallBuilder(
                  request,
                  getEC2Client(pAWSAccountId, pRegion)::describeSnapshots,
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
              .makeApiCall();
      resultConsumer.accept(result);
      nextToken = result.getNextToken();
    } while (nextToken != null);
  }

  public String findSnapshotsAndApplyForNextToken(
      final ObjectId pAccountId,
      final AWSRegionName pRegion,
      final String ownerId,
      final int pMaxResultSize,
      final String pNextToken,
      final Logger pLogger,
      final Consumer<DescribeSnapshotsResult> resultConsumer) {
    // This method should not be called with a null pNextToken
    // because a null token indicates there are no more pages of results
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final String prettyErr =
        String.format(
            "Problem retrieving EBS snapshots in Region (%s), Account Id (%s), and AWS Owner Id"
                + " (%s)",
            pRegion.getName(), pAccountId.toString(), ownerId);

    final DescribeSnapshotsRequest request =
        new DescribeSnapshotsRequest().withOwnerIds(ownerId).withMaxResults(pMaxResultSize);

    if (!pNextToken.equals(AWSApiSvc.TOKEN_START)) {
      request.withNextToken(pNextToken);
    }

    final DescribeSnapshotsResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAccountId, pRegion)::describeSnapshots,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();
    resultConsumer.accept(result);

    final String newNextToken = result.getNextToken();
    return (newNextToken != null) ? newNextToken : TOKEN_DONE;
  }

  public void deleteEbsSnapshot(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEBSSnapshotId)
      throws AWSApiException {
    final String prettyErr =
        String.format(
            "Problem deleting EBS snapshot with Snapshot Id (%s) in Region (%s) for AwsAccountId"
                + " (%s)",
            pEBSSnapshotId, pRegionName, pAWSAccountId);

    getAWSApiCallBuilder(
            new DeleteSnapshotRequest().withSnapshotId(pEBSSnapshotId),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).deleteSnapshot(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void shareEbsSnapshot(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final ObjectId pTargetAWSAccountId,
      final Logger pLogger,
      final String pEBSSnapshotId)
      throws AWSApiException {
    final String prettyErr =
        String.format(
            "Problem Share EBS snapshot with Snapshot Id (%s) in Region (%s) with TargetAccount",
            pEBSSnapshotId, pRegionName);

    final List<CreateVolumePermission> createVolumePermissions = new ArrayList<>();
    final CreateVolumePermission cp = new CreateVolumePermission();
    final CreateVolumePermissionModifications perms = new CreateVolumePermissionModifications();
    final AWSAccount awsAccount = getAWSAccount(pTargetAWSAccountId);
    final String rootOrRoleArn = awsAccount.getAssumeRoleARN().orElse(awsAccount.getRootARN());
    createVolumePermissions.add(cp.withUserId(AwsUtils.getAccountIdFromArn(rootOrRoleArn)));
    perms.setAdd(createVolumePermissions);

    getAWSApiCallBuilder(
            new ModifySnapshotAttributeRequest()
                .withSnapshotId(pEBSSnapshotId)
                .withCreateVolumePermission(perms),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).modifySnapshotAttribute(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  /** VPC Peering Methods */
  public CreateVpcPeeringConnectionResult createVpcPeeringConnection(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pAtlasVpcId,
      final AWSPeerVpc pAWSPeerVpc,
      final Set<ErrorCode> pExpectedErrorCodes) {
    final String prettyErr = "Problem creating peering connection";
    final CreateVpcPeeringConnectionRequest request =
        new CreateVpcPeeringConnectionRequest()
            .withPeerOwnerId(pAWSPeerVpc.getAwsAccountId())
            .withPeerVpcId(pAWSPeerVpc.getVpcId())
            .withVpcId(pAtlasVpcId);
    return getAWSApiCallBuilder(
            (pAWSPeerVpc.getAccepterRegionName().isPresent()
                ? request.withPeerRegion(pAWSPeerVpc.getAccepterRegionName().get().getValue())
                : request),
            getEC2Client(pAWSAccountId, pRegionName)::createVpcPeeringConnection,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(pExpectedErrorCodes)
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();
  }

  public void deleteVpcPeeringConnection(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pConnectionId) {
    final String prettyErr = "Problem delete peering connection";

    getAWSApiCallBuilder(
            new DeleteVpcPeeringConnectionRequest().withVpcPeeringConnectionId(pConnectionId),
            getEC2Client(pAWSAccountId, pRegionName)::deleteVpcPeeringConnection,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(AWSErrorCode.INVALID_STATE_TRANSITION))
        .makeApiCall();
  }

  public VpcPeeringConnection describeVpcPeeringConnection(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pConnectionId) {
    if (StringUtils.isEmpty(pConnectionId)) {
      throw new IllegalArgumentException("'connectionId' is required");
    }
    final String prettyErr = "Problem finding peering connection";

    final DescribeVpcPeeringConnectionsResult result =
        getAWSApiCallBuilder(
                new DescribeVpcPeeringConnectionsRequest()
                    .withVpcPeeringConnectionIds(pConnectionId),
                getEC2Client(pAWSAccountId, pRegionName)::describeVpcPeeringConnections,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getVpcPeeringConnections().isEmpty()
        || result.getVpcPeeringConnections().size() != 1) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getVpcPeeringConnections().get(0);
  }

  public List<VpcPeeringConnection> findVpcPeeringConnectionsInVpc(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId,
      final boolean pVpcIsRequester) {

    if (StringUtils.isEmpty(pVpcId)) {
      throw new IllegalArgumentException("`pVpcId` is required");
    }

    final String prettyErr = "Problem finding vpc peering connection";
    final String filterStr =
        pVpcIsRequester ? "requester-vpc-info.vpc-id" : "accepter-vpc-info.vpc-id";

    final DescribeVpcPeeringConnectionsResult result =
        getAWSApiCallBuilder(
                new DescribeVpcPeeringConnectionsRequest()
                    .withFilters(new Filter(filterStr).withValues(pVpcId)),
                getEC2Client(pAWSAccountId, pRegionName)::describeVpcPeeringConnections,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    if (result.getVpcPeeringConnections() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getVpcPeeringConnections();
  }

  public ModifyVpcPeeringConnectionOptionsResult enableDNSResolutionOnPeeringConnection(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pConnectionId) {
    final String prettyErr = "Problem modifying peering connection options";
    return getAWSApiCallBuilder(
            new ModifyVpcPeeringConnectionOptionsRequest()
                .withRequesterPeeringConnectionOptions(
                    new PeeringConnectionOptionsRequest().withAllowDnsResolutionFromRemoteVpc(true))
                .withVpcPeeringConnectionId(pConnectionId),
            getEC2Client(pAWSAccountId, pRegionName)::modifyVpcPeeringConnectionOptions,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public VpcPeeringConnection acceptVpcPeeringConnection(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pConnectionId) {
    final String prettyError = "Problem accepting peering connection";
    final AcceptVpcPeeringConnectionResult result =
        getAWSApiCallBuilder(
                new AcceptVpcPeeringConnectionRequest().withVpcPeeringConnectionId(pConnectionId),
                getEC2Client(pAWSAccountId, pRegionName)::acceptVpcPeeringConnection,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();
    return result.getVpcPeeringConnection();
  }

  public boolean rejectVpcPeeringConnection(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pConnectionId) {
    final String prettyError = "Problem accepting peering connection";
    final RejectVpcPeeringConnectionResult result =
        getAWSApiCallBuilder(
                new RejectVpcPeeringConnectionRequest().withVpcPeeringConnectionId(pConnectionId),
                getEC2Client(pAWSAccountId, pRegionName)::rejectVpcPeeringConnection,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();
    return result.isReturn();
  }

  /** PrivateLink methods */
  public CreateTargetGroupResult createTargetGroup(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pVpcId,
      final int pPort,
      final String pName,
      final TargetTypeEnum pTargetType) {
    return createTargetGroup(
        pAWSAccountId, pRegion, pLogger, pVpcId, pPort, pName, pTargetType, null, null, null, null);
  }

  public CreateTargetGroupResult createTargetGroup(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pVpcId,
      final int pPort,
      final String pName,
      final TargetTypeEnum pTargetType,
      final Integer pHealthCheckIntervalSeconds,
      final Integer pHealthyThresholdCount,
      final ProtocolEnum pHealthCheckProtocol,
      final String pHealthCheckPath) {
    final String prettyError = "Problem creating target group";

    final CreateTargetGroupRequest request =
        new CreateTargetGroupRequest()
            .withName(pName)
            .withPort(pPort)
            .withProtocol(ProtocolEnum.TCP)
            .withTargetType(pTargetType)
            .withVpcId(pVpcId);

    Optional.ofNullable(pHealthCheckIntervalSeconds)
        .ifPresent(request::withHealthCheckIntervalSeconds);
    Optional.ofNullable(pHealthCheckIntervalSeconds)
        .ifPresent(request::withHealthCheckTimeoutSeconds);
    Optional.ofNullable(pHealthyThresholdCount).ifPresent(request::withHealthyThresholdCount);
    Optional.ofNullable(pHealthCheckProtocol).ifPresent(request::withHealthCheckProtocol);
    Optional.ofNullable(pHealthCheckPath).ifPresent(request::withHealthCheckPath);

    final CreateTargetGroupResult result =
        getAWSApiCallBuilder(
                request,
                getElasticLoadBalancingClient(pAWSAccountId, pRegion)::createTargetGroup,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(AWSErrorCode.DUPLICATE))
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegion)
            .makeApiCall();

    result.getTargetGroups().stream()
        .filter(targetGroup -> targetGroup.getTargetGroupName().equals(pName))
        .forEach(
            targetGroup ->
                _resourceObservable.notify(
                    new AWSResource(
                        pAWSAccountId, pRegion, Type.TARGET_GROUP, targetGroup.getTargetGroupArn()),
                    ResourceEvent.CREATED));

    return result;
  }

  public ModifyTargetGroupResult modifyTargetGroupHealthCheckSettings(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn,
      final Integer pHealthCheckIntervalSeconds,
      final Integer pHealthyThresholdCount) {
    final String prettyError = "Problem modifying target group";
    final ModifyTargetGroupRequest request =
        new ModifyTargetGroupRequest().withTargetGroupArn(pTargetGroupArn);

    Optional.ofNullable(pHealthCheckIntervalSeconds)
        .ifPresent(request::withHealthCheckIntervalSeconds);
    Optional.ofNullable(pHealthCheckIntervalSeconds)
        .ifPresent(request::withHealthCheckTimeoutSeconds);
    Optional.ofNullable(pHealthyThresholdCount).ifPresent(request::withHealthyThresholdCount);

    return getAWSApiCallBuilder(
            request,
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::modifyTargetGroup,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public ModifyTargetGroupAttributesResult modifyTargetGroupAttributes(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn,
      final List<TargetGroupAttribute> pTargetGroupAttribute) {
    final String prettyError = "Problem modifying target group attributes";

    final ModifyTargetGroupAttributesRequest request =
        new ModifyTargetGroupAttributesRequest()
            .withTargetGroupArn(pTargetGroupArn)
            .withAttributes(pTargetGroupAttribute);

    return getAWSApiCallBuilder(
            request,
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::modifyTargetGroupAttributes,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public RegisterTargetsResult registerInstanceWithTargetGroup(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn,
      final String pEc2InstanceId) {
    return registerInstancesWithTargetGroup(
        pAWSAccountId, pRegion, pLogger, pTargetGroupArn, Set.of(pEc2InstanceId));
  }

  public RegisterTargetsResult registerInstancesWithTargetGroup(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn,
      final Set<String> pEc2InstanceIds) {
    final String prettyError = "Problem registering instance with target group";
    return getAWSApiCallBuilder(
            new RegisterTargetsRequest()
                .withTargetGroupArn(pTargetGroupArn)
                .withTargets(
                    pEc2InstanceIds.stream()
                        .map((pInstanceId) -> new TargetDescription().withId(pInstanceId))
                        .collect(Collectors.toList())),
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::registerTargets,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public DeregisterTargetsResult deregisterInstanceWithTargetGroup(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn,
      final String pEc2InstanceId) {
    final String prettyError = "Problem deregistering instance with target group";
    return getAWSApiCallBuilder(
            new DeregisterTargetsRequest()
                .withTargetGroupArn(pTargetGroupArn)
                .withTargets(new TargetDescription().withId(pEc2InstanceId)),
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::deregisterTargets,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  /**
   * Retrieves the health status of a specific target in an AWS Load Balancer target group.
   *
   * <p>This is a convenience method for checking the health of a single EC2 instance. For checking
   * multiple instances, use {@link #findTargetHealths(ObjectId, AWSRegionName, Logger, String,
   * List)}.
   *
   * @param pAWSAccountId the AWS account ID where the target group resides
   * @param pRegion the AWS region where the target group is located
   * @param pLogger logger for recording API call details and errors
   * @param pTargetGroupArn the ARN of the target group to query
   * @param pEc2InstanceId the specific EC2 instance ID to check
   * @return DescribeTargetHealthResult containing the health description for the specified instance
   */
  public DescribeTargetHealthResult findTargetHealth(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn,
      final String pEc2InstanceId) {
    return findTargetHealths(
        pAWSAccountId, pRegion, pLogger, pTargetGroupArn, List.of(pEc2InstanceId));
  }

  /**
   * Retrieves the health status of all targets in an AWS Load Balancer target group.
   *
   * <p>This is a convenience method that returns health information for all registered targets in
   * the target group. For checking specific instances, use {@link #findTargetHealths(ObjectId,
   * AWSRegionName, Logger, String, List)}.
   *
   * @param pAWSAccountId the AWS account ID where the target group resides
   * @param pRegion the AWS region where the target group is located
   * @param pLogger logger for recording API call details and errors
   * @param pTargetGroupArn the ARN of the target group to query
   * @return DescribeTargetHealthResult containing health descriptions for all targets in the group
   */
  public DescribeTargetHealthResult findTargetHealths(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn) {
    return findTargetHealths(pAWSAccountId, pRegion, pLogger, pTargetGroupArn, null);
  }

  /**
   * Retrieves the health status of targets in an AWS Load Balancer target group.
   *
   * @param pAWSAccountId the AWS account ID where the target group resides
   * @param pRegion the AWS region where the target group is located
   * @param pLogger logger for recording API call details and errors
   * @param pTargetGroupArn the ARN of the target group to query
   * @param pEc2InstanceIds optional list of specific EC2 instance IDs to check; if null or empty,
   *     health status for all targets in the group is returned
   * @return DescribeTargetHealthResult containing health descriptions for the specified targets
   */
  public DescribeTargetHealthResult findTargetHealths(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn,
      final List<String> pEc2InstanceIds) {
    final String prettyError = "Problem finding target health description";
    final DescribeTargetHealthRequest request =
        new DescribeTargetHealthRequest().withTargetGroupArn(pTargetGroupArn);
    Optional.ofNullable(pEc2InstanceIds)
        .map(List::stream)
        .map(s -> s.map(id -> new TargetDescription().withId(id)))
        .map(Stream::toList)
        .ifPresent(request::withTargets);

    return getAWSApiCallBuilder(
            request,
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::describeTargetHealth,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  /**
   * Retrieves the EC2 instance IDs of all targets registered with an AWS Load Balancer target
   * group.
   *
   * <p>This method queries the target group's health status and extracts just the instance IDs,
   * providing a convenient way to get a list of all registered instances without needing to process
   * the full health description objects.
   *
   * @param pAWSAccountId the AWS account ID where the target group resides
   * @param pRegion the AWS region where the target group is located
   * @param pLogger logger for recording API call details and errors
   * @param pTargetGroupArn the ARN of the target group to query
   * @return list of EC2 instance IDs registered with the target group
   */
  public List<String> findTargetInstanceIds(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn) {
    final DescribeTargetHealthResult targetHealthResult =
        findTargetHealths(pAWSAccountId, pRegion, pLogger, pTargetGroupArn);
    return targetHealthResult.getTargetHealthDescriptions().stream()
        .map(TargetHealthDescription::getTarget)
        .map(TargetDescription::getId)
        .toList();
  }

  public DeleteTargetGroupResult deleteTargetGroup(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn) {
    final String prettyError = "Problem deleting target group";

    final DeleteTargetGroupRequest request =
        new DeleteTargetGroupRequest().withTargetGroupArn(pTargetGroupArn);

    return getAWSApiCallBuilder(
            request,
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::deleteTargetGroup,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public DescribeTargetGroupsResult findTargetGroups(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pTargetGroupArn) {
    return getAWSApiCallBuilder(
            new DescribeTargetGroupsRequest().withTargetGroupArns(pTargetGroupArn),
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::describeTargetGroups,
            "Problem finding target group",
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public CreateListenerResult createLoadBalancerListener(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pLoadBalancerArn,
      final String pTargetGroupArn,
      final int pPort) {
    final String prettyError = "Problem creating listener on load balancer";
    return getAWSApiCallBuilder(
            new CreateListenerRequest()
                .withLoadBalancerArn(pLoadBalancerArn)
                .withDefaultActions(
                    new Action()
                        .withType(ActionTypeEnum.Forward)
                        .withTargetGroupArn(pTargetGroupArn))
                .withPort(pPort)
                .withProtocol(ProtocolEnum.TCP),
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::createListener,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(AWSErrorCode.DUPLICATE))
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegion)
        .makeApiCall();
  }

  public DeleteListenerResult deleteListener(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pListenerArn) {
    final String prettyError = "Problem deleting listener";

    return getAWSApiCallBuilder(
            new DeleteListenerRequest().withListenerArn(pListenerArn),
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::deleteListener,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public DescribeListenersResult findListener(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final Logger pLogger,
      final String pListenerArn) {
    return getAWSApiCallBuilder(
            new DescribeListenersRequest().withListenerArns(pListenerArn),
            getElasticLoadBalancingClient(pAWSAccountId, pRegion)::describeListeners,
            "Problem finding listener",
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  /**
   * Modifies an LB listener to update its target group.
   *
   * @param pAWSAccountId The AWS account ID
   * @param pRegionName The AWS region name
   * @param pListenerArn The ARN of the listener to modify
   * @param pTargetGroupArn The ARN of the target group to set as the listener's target
   * @param pLogger The logger to use
   * @return The result of modifying the listener
   * @throws AWSApiException if there was an error modifying the listener
   */
  public ModifyListenerResult modifyListener(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pListenerArn,
      final String pTargetGroupArn,
      final Logger pLogger) {
    final String prettyError = "Problem modifying listener target group";
    return getAWSApiCallBuilder(
            new ModifyListenerRequest()
                .withListenerArn(pListenerArn)
                .withDefaultActions(
                    new Action()
                        .withType(ActionTypeEnum.Forward)
                        .withTargetGroupArn(pTargetGroupArn)),
            getElasticLoadBalancingClient(pAWSAccountId, pRegionName)::modifyListener,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public CreateLoadBalancerResult createNetworkLoadBalancer(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pName,
      final List<String> pSubnetIds,
      final Collection<com.amazonaws.services.elasticloadbalancingv2.model.Tag> pTags) {
    return createNetworkLoadBalancer(
        pAWSAccountId,
        pRegionName,
        pLogger,
        pName,
        LoadBalancerSchemeEnum.Internal,
        pSubnetIds,
        pTags);
  }

  public CreateLoadBalancerResult createNetworkLoadBalancer(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pName,
      final LoadBalancerSchemeEnum pLoadBalancerSchemeEnum,
      final List<String> pSubnetIds,
      final Collection<com.amazonaws.services.elasticloadbalancingv2.model.Tag> pTags) {
    final String prettyError = "Problem creating network load balancer";
    final CreateLoadBalancerRequest request =
        new CreateLoadBalancerRequest()
            .withScheme(pLoadBalancerSchemeEnum)
            .withType(LoadBalancerTypeEnum.Network)
            .withSubnets(pSubnetIds)
            .withName(pName)
            .withTags(pTags);
    final CreateLoadBalancerResult result =
        getAWSApiCallBuilder(
                request,
                getElasticLoadBalancingClient(pAWSAccountId, pRegionName)::createLoadBalancer,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .makeApiCall();

    result.getLoadBalancers().stream()
        .filter(targetGroup -> targetGroup.getLoadBalancerName().equals(pName))
        .forEach(
            targetGroup ->
                _resourceObservable.notify(
                    new AWSResource(
                        pAWSAccountId,
                        pRegionName,
                        Type.LOAD_BALANCER,
                        targetGroup.getLoadBalancerArn()),
                    ResourceEvent.CREATED));

    return result;
  }

  public ModifyLoadBalancerAttributesResult modifyNetworkLoadBalancerAttributes(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pNetworkLoadBalancerArn,
      final LoadBalancerAttribute pLoadBalancerAttribute) {
    final String prettyError = "Problem modifying network load balancer attributes";
    final ModifyLoadBalancerAttributesRequest request =
        new ModifyLoadBalancerAttributesRequest()
            .withAttributes(pLoadBalancerAttribute)
            .withLoadBalancerArn(pNetworkLoadBalancerArn);

    return getAWSApiCallBuilder(
            request,
            getElasticLoadBalancingClient(pAWSAccountId, pRegionName)::modifyLoadBalancerAttributes,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public SetSubnetsResult setNetworkLoadBalancerSubnets(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pNetworkLoadBalancerArn,
      final Collection<String> pSubnets) {
    final String prettyError = "Problem setting network load balancer subnets";
    final SetSubnetsRequest request =
        new SetSubnetsRequest().withSubnets(pSubnets).withLoadBalancerArn(pNetworkLoadBalancerArn);

    return getAWSApiCallBuilder(
            request,
            getElasticLoadBalancingClient(pAWSAccountId, pRegionName)::setSubnets,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public DescribeLoadBalancersResult describeNetworkLoadBalancer(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pNetworkLoadBalancerArn) {
    final String prettyError = "Problem finding network load balancer";
    final DescribeLoadBalancersRequest request =
        new DescribeLoadBalancersRequest().withLoadBalancerArns(pNetworkLoadBalancerArn);

    return getAWSApiCallBuilder(
            request,
            getElasticLoadBalancingClient(pAWSAccountId, pRegionName)::describeLoadBalancers,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public DescribeLoadBalancerAttributesResult describeNetworkLoadBalancerAttributes(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pNetworkLoadBalancerArn) {
    final String prettyError = "Problem finding network load balancer";
    final DescribeLoadBalancerAttributesRequest request =
        new DescribeLoadBalancerAttributesRequest().withLoadBalancerArn(pNetworkLoadBalancerArn);

    return getAWSApiCallBuilder(
            request,
            getElasticLoadBalancingClient(pAWSAccountId, pRegionName)
                ::describeLoadBalancerAttributes,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void findNetworkLoadBalancersAndApply(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final int pMaxPageSize,
      final Logger pLogger,
      final Consumer<DescribeLoadBalancersResult> resultConsumer) {
    final DescribeLoadBalancersRequest request =
        new DescribeLoadBalancersRequest().withPageSize(pMaxPageSize);
    DescribeLoadBalancersResult result;
    String nextMarker = null;
    do {
      if (nextMarker != null) {
        request.withMarker(nextMarker);
      }
      result =
          getAWSApiCallBuilder(
                  request,
                  getElasticLoadBalancingClient(pAWSAccountId, pRegionName)::describeLoadBalancers,
                  "Problem retrieving network load balancers",
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
              .makeApiCall();
      resultConsumer.accept(result);
      nextMarker = result.getNextMarker();
    } while (nextMarker != null);
  }

  public String findNetworkLoadBalancersAndApplyForNextMarker(
      final ObjectId pAccountId,
      final AWSRegionName pRegionName,
      final int pMaxPageSize,
      final String pNextMarker,
      final Logger pLogger,
      final Consumer<DescribeLoadBalancersResult> resultConsumer) {
    // This method should not be called with a null pNextMarker
    // because a null token indicates there are no more pages of results
    if (pNextMarker == null) {
      throw new IllegalArgumentException("pNextMarker should not be null");
    }

    final DescribeLoadBalancersRequest request =
        new DescribeLoadBalancersRequest().withPageSize(pMaxPageSize);

    if (!pNextMarker.equals(AWSApiSvc.TOKEN_START)) {
      request.withMarker(pNextMarker);
    }

    final DescribeLoadBalancersResult result =
        getAWSApiCallBuilder(
                request,
                getElasticLoadBalancingClient(pAccountId, pRegionName)::describeLoadBalancers,
                "Problem retrieving network load balancers",
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();
    resultConsumer.accept(result);

    final String newNextMarker = result.getNextMarker();
    return (newNextMarker != null) ? newNextMarker : TOKEN_DONE;
  }

  public List<com.amazonaws.services.elasticloadbalancingv2.model.Tag> getElbv2Tags(
      final ObjectId pAWSAccountId, final AWSRegionName pRegionName, final String pResourceArn) {
    final String prettyErr =
        String.format(
            "Problem find load balancer tags in Region (%s), Account Id (%s)",
            pRegionName.getName(), pAWSAccountId.toString());

    final DescribeTagsRequest request = new DescribeTagsRequest().withResourceArns(pResourceArn);

    final Optional<TagDescription> resourceTagDescription =
        getAWSApiCallBuilder(
                request,
                getElasticLoadBalancingClient(pAWSAccountId, pRegionName)::describeTags,
                prettyErr,
                NDSErrorCode.INTERNAL,
                LOG)
            .makeApiCall()
            .getTagDescriptions()
            .stream()
            .filter(tagDescription -> tagDescription.getResourceArn().equals(pResourceArn))
            .findFirst();

    if (resourceTagDescription.isPresent()) {
      return resourceTagDescription.get().getTags();
    }

    return new ArrayList<>();
  }

  public CreateVpcEndpointServiceConfigurationResult createVpcEndpointService(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final ObjectId pPrivateLinkConnectionId,
      final String pNetworkLoadBalancerArn,
      final List<Tag> pTags) {
    final String prettyError = "Problem creating vpc endpoint service";

    final TagSpecification tagSpecification =
        new TagSpecification().withResourceType(ResourceType.VpcEndpointService).withTags(pTags);

    final CreateVpcEndpointServiceConfigurationRequest request =
        new CreateVpcEndpointServiceConfigurationRequest()
            .withClientToken(pPrivateLinkConnectionId.toString())
            .withNetworkLoadBalancerArns(pNetworkLoadBalancerArn)
            .withTagSpecifications(tagSpecification)
            .withAcceptanceRequired(true);

    final CreateVpcEndpointServiceConfigurationResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegionName)::createVpcEndpointServiceConfiguration,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .makeApiCall();
    _resourceObservable.notify(
        new AWSResource(
            pAWSAccountId,
            pRegionName,
            Type.PRIVATE_LINK_SERVICE,
            result.getServiceConfiguration().getServiceId()),
        ResourceEvent.CREATED);
    return result;
  }

  public DescribeVpcEndpointServicesResult describeVpcEndpointService(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcEndpointServiceName) {
    final String prettyError = "Problem finding vpc endpoint service";
    final DescribeVpcEndpointServicesRequest request =
        new DescribeVpcEndpointServicesRequest().withServiceNames(pVpcEndpointServiceName);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::describeVpcEndpointServices,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public ModifyVpcEndpointServiceConfigurationResult addNetworkLoadBalancerArnToVpcEndpointService(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEndpointServiceId,
      final String pNetworkLoadBalancerArn) {
    final String prettyError = "Problem adding network load balancer arn to vpc endpoint service";
    final ModifyVpcEndpointServiceConfigurationRequest request =
        new ModifyVpcEndpointServiceConfigurationRequest()
            .withServiceId(pEndpointServiceId)
            .withAddNetworkLoadBalancerArns(pNetworkLoadBalancerArn);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::modifyVpcEndpointServiceConfiguration,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public ModifyVpcEndpointServicePermissionsResult modifyVpcEndpointServicePermissions(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEndpointServiceId,
      final String pWhitelistPrinciple) {
    final String prettyError = "Problem modifying vpc endpoint service permissions";
    final ModifyVpcEndpointServicePermissionsRequest request =
        new ModifyVpcEndpointServicePermissionsRequest()
            .withServiceId(pEndpointServiceId)
            .withAddAllowedPrincipals(pWhitelistPrinciple);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::modifyVpcEndpointServicePermissions,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public DeleteVpcEndpointServiceConfigurationsResult deleteVpcEndpointService(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEndpointServiceId) {
    final String prettyError = "Problem deleting vpc endpoint service";
    final DeleteVpcEndpointServiceConfigurationsRequest request =
        new DeleteVpcEndpointServiceConfigurationsRequest().withServiceIds(pEndpointServiceId);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::deleteVpcEndpointServiceConfigurations,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public DeleteLoadBalancerResult deleteLoadBalancer(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pLoadBalancerArn) {
    final String prettyError = "Problem deleting load balancer";
    final DeleteLoadBalancerRequest request =
        new DeleteLoadBalancerRequest().withLoadBalancerArn(pLoadBalancerArn);

    return getAWSApiCallBuilder(
            request,
            getElasticLoadBalancingClient(pAWSAccountId, pRegionName)::deleteLoadBalancer,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public AcceptVpcEndpointConnectionsResult acceptVpcEndpointConnection(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEndpointServiceId,
      final String pInterfaceEndpointId) {
    final String prettyError = "Problem accepting endpoint connection";
    final AcceptVpcEndpointConnectionsRequest request =
        new AcceptVpcEndpointConnectionsRequest()
            .withServiceId(pEndpointServiceId)
            .withVpcEndpointIds(pInterfaceEndpointId);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::acceptVpcEndpointConnections,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public RejectVpcEndpointConnectionsResult rejectVpcEndpointConnection(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEndpointServiceId,
      final String pInterfaceEndpointId) {
    final String prettyError = "Problem rejecting endpoint connection";
    final RejectVpcEndpointConnectionsRequest request =
        new RejectVpcEndpointConnectionsRequest()
            .withServiceId(pEndpointServiceId)
            .withVpcEndpointIds(pInterfaceEndpointId);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::rejectVpcEndpointConnections,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public RejectVpcEndpointConnectionsResult rejectVpcEndpointConnections(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEndpointServiceId,
      final List<String> pInterfaceEndpointIds) {
    final String prettyError = "Problem rejecting endpoint connections";
    final RejectVpcEndpointConnectionsRequest request =
        new RejectVpcEndpointConnectionsRequest()
            .withServiceId(pEndpointServiceId)
            .withVpcEndpointIds(pInterfaceEndpointIds);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::rejectVpcEndpointConnections,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public CreateVpcEndpointResult createInterfaceEndpoint(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId,
      final List<String> pSubnetIds,
      final String pEndpointServiceName,
      final List<Tag> pTags,
      final List<String> pSecurityGroupIds,
      final String pPolicyDoc,
      final Boolean pPrivateDnsEnabled,
      final DnsOptionsSpecification pDnsOptions) {

    final String prettyError = "Problem creating interface endpoint";

    final CreateVpcEndpointRequest request =
        new CreateVpcEndpointRequest()
            .withVpcEndpointType(VpcEndpointType.Interface)
            .withVpcId(pVpcId)
            .withSubnetIds(pSubnetIds)
            .withServiceName(pEndpointServiceName)
            .withTagSpecifications(
                new TagSpecification().withResourceType("vpc-endpoint").withTags(pTags));

    if (pDnsOptions != null) {
      request.withDnsOptions(pDnsOptions);
    }

    if (pPrivateDnsEnabled != null) {
      request.withPrivateDnsEnabled(pPrivateDnsEnabled);
    }

    if (!pSecurityGroupIds.isEmpty()) {
      request.withSecurityGroupIds(pSecurityGroupIds);
    }

    if (!StringUtils.isEmpty(pPolicyDoc)) {
      request.withPolicyDocument(pPolicyDoc);
    }

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::createVpcEndpoint,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public DeleteVpcEndpointsResult deleteInterfaceEndpoint(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pInterfaceEndpointId) {
    final String prettyError = "Problem deleting interface endpoint";
    final DeleteVpcEndpointsRequest request =
        new DeleteVpcEndpointsRequest().withVpcEndpointIds(pInterfaceEndpointId);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::deleteVpcEndpoints,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public DescribeVpcEndpointsResult describeInterfaceEndpoint(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pInterfaceEndpointId) {
    final String prettyError = "Problem describing interface endpoint";
    DescribeVpcEndpointsRequest request =
        new DescribeVpcEndpointsRequest().withVpcEndpointIds(pInterfaceEndpointId);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::describeVpcEndpoints,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public CreateVPCAssociationAuthorizationResult createVPCAssociationAuthorization(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId,
      final String pHostedZoneId) {
    final String prettyError = "Problem creating VPC association auth request";
    String regionName = pRegionName.toString().replace("_", "-");
    VPC vpc = new VPC().withVPCId(pVpcId).withVPCRegion(regionName.toLowerCase());
    final CreateVPCAssociationAuthorizationRequest request =
        new CreateVPCAssociationAuthorizationRequest().withVPC(vpc).withHostedZoneId(pHostedZoneId);
    return getAWSApiCallBuilder(
            request,
            getRoute53Client(pAWSAccountId)::createVPCAssociationAuthorization,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public ListVPCAssociationAuthorizationsResult listVPCAssociationAuthorizations(
      final ObjectId pAWSAccountId, final Logger pLogger, final String pHostedZoneId) {
    final String prettyError = "Problem listing VPC association auth request";
    final ListVPCAssociationAuthorizationsRequest request =
        new ListVPCAssociationAuthorizationsRequest().withHostedZoneId(pHostedZoneId);
    return getAWSApiCallBuilder(
            request,
            getRoute53Client(pAWSAccountId)::listVPCAssociationAuthorizations,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public AssociateVPCWithHostedZoneResult acceptAssociateVPCWithHostedZoneRequest(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId,
      final String pHostedZoneId) {
    final String prettyError = "Problem accepting VPC association request";
    String regionName = pRegionName.toString().replace("_", "-");
    VPC vpc = new VPC().withVPCId(pVpcId).withVPCRegion(regionName.toLowerCase());
    final AssociateVPCWithHostedZoneRequest request =
        new AssociateVPCWithHostedZoneRequest().withVPC(vpc).withHostedZoneId(pHostedZoneId);
    return getAWSApiCallBuilder(
            request,
            getRoute53Client(pAWSAccountId)::associateVPCWithHostedZone,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public DisassociateVPCFromHostedZoneResult disassociateVPCWithHostedZoneRequest(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pVpcId,
      final String pHostedZoneId) {
    final String prettyError = "Problem disassociating VPC association request";
    String regionName = pRegionName.toString().replace("_", "-");
    VPC vpc = new VPC().withVPCId(pVpcId).withVPCRegion(regionName.toLowerCase());
    final DisassociateVPCFromHostedZoneRequest request =
        new DisassociateVPCFromHostedZoneRequest().withVPC(vpc).withHostedZoneId(pHostedZoneId);
    return getAWSApiCallBuilder(
            request,
            getRoute53Client(pAWSAccountId)::disassociateVPCFromHostedZone,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.INVALID_PARAMETER))
        .makeApiCall();
  }

  public CreateTransitGatewayVpcAttachmentResult createTransitGatewayVpcAttachmentRequest(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pVpcId,
      final String pTgwId,
      final List<String> pSubnetIds,
      final Logger pLogger) {

    CreateTransitGatewayVpcAttachmentRequest request =
        new CreateTransitGatewayVpcAttachmentRequest()
            .withTransitGatewayId(pTgwId)
            .withVpcId(pVpcId)
            .withSubnetIds(pSubnetIds);

    return getAWSApiCallBuilder(
            request,
            getEC2Client(pAWSAccountId, pRegionName)::createTransitGatewayVpcAttachment,
            "Problem creating transit gateway vpc attachment",
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public Collection<NetworkInterface> findLoadBalancerNetworkInterfaces(
      final ObjectId awsAccountId,
      final AWSRegionName regionName,
      final Logger logger,
      final String loadBalancerArn,
      final String vpcId) {
    final String loadBalancerQuery = loadBalancerArn.split("/", 2)[1];
    final String descriptionQuery = String.format("ELB %s", loadBalancerQuery);

    return findNetworkInterfaces(awsAccountId, regionName, logger, vpcId, descriptionQuery);
  }

  public DescribeVpcEndpointConnectionsResult describeEndpointConnections(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEndpointServiceId) {
    final String prettyError = "Problem finding endpoint connections";
    return getAWSApiCallBuilder(
            new DescribeVpcEndpointConnectionsRequest()
                .withFilters(new Filter("service-id", List.of(pEndpointServiceId))),
            getEC2Client(pAWSAccountId, pRegionName)::describeVpcEndpointConnections,
            prettyError,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public Optional<VpcEndpointConnection> findVpcEndpointConnection(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEndpointServiceId,
      final String pEndpointId) {
    final String prettyError = "Problem finding endpoint connections";
    final DescribeVpcEndpointConnectionsResult describeVpcEndpointConnectionsResult =
        getAWSApiCallBuilder(
                new DescribeVpcEndpointConnectionsRequest()
                    .withFilters(
                        new Filter("service-id", List.of(pEndpointServiceId)),
                        new Filter("vpc-endpoint-id", List.of(pEndpointId))),
                getEC2Client(pAWSAccountId, pRegionName)::describeVpcEndpointConnections,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    return describeVpcEndpointConnectionsResult.getVpcEndpointConnections().stream().findFirst();
  }

  public void createRouteForRouteTable(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pConnectionId,
      final String pCidrBlock,
      final String pRouteTableId) {
    final String prettyErr = "Problem creating route for peering connection";

    getAWSApiCallBuilder(
            new CreateRouteRequest()
                .withRouteTableId(pRouteTableId)
                .withVpcPeeringConnectionId(pConnectionId)
                .withDestinationCidrBlock(pCidrBlock),
            getEC2Client(pAWSAccountId, pRegionName)::createRoute,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();
  }

  public void removeRouteForRouteTable(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pCidrBlock,
      final String pRouteTableId) {
    final String prettyErr = "Problem removing route for peering connection";

    getAWSApiCallBuilder(
            new DeleteRouteRequest()
                .withRouteTableId(pRouteTableId)
                .withDestinationCidrBlock(pCidrBlock),
            getEC2Client(pAWSAccountId, pRegionName)::deleteRoute,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  // This is similar to createRouteForRouteTable, but accepts a transit
  // gateway attachment id instead of a vpc peering connection.
  public void createRouteForTransitGatewayRouteTable(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pTgwId,
      final String pCidrBlock,
      final String pRouteTableId) {
    final String prettyErr = "Problem creating route for transit gateway connection";

    getAWSApiCallBuilder(
            new CreateRouteRequest()
                .withRouteTableId(pRouteTableId)
                .withTransitGatewayId(pTgwId)
                .withDestinationCidrBlock(pCidrBlock),
            getEC2Client(pAWSAccountId, pRegionName)::createRoute,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();
  }

  /*
   * makeApiCall generically logs all request objects, which is very useful,
   * but the RunInstancesRequest happens to contain some secrets that we
   * pass to the instance in the UserData. That inforomation should not goto
   * the logs
   */
  private static class RedactedRunInstancesRequest extends RunInstancesRequest {

    private static final long serialVersionUID = 1L;

    protected void addFields(final StringBuilder sb) {
      if (getImageId() != null) {
        sb.append("ImageId: ").append(getImageId()).append(",");
      }
      if (getMinCount() != null) {
        sb.append("MinCount: ").append(getMinCount()).append(",");
      }
      if (getMaxCount() != null) {
        sb.append("MaxCount: ").append(getMaxCount()).append(",");
      }
      if (getKeyName() != null) {
        sb.append("KeyName: ").append(getKeyName()).append(",");
      }
      if (getSecurityGroups() != null) {
        sb.append("SecurityGroups: ").append(getSecurityGroups()).append(",");
      }
      if (getSecurityGroupIds() != null) {
        sb.append("SecurityGroupIds: ").append(getSecurityGroupIds()).append(",");
      }
      if (getUserData() != null) {
        sb.append("UserData: <redacted>,");
      }
      if (getInstanceType() != null) {
        sb.append("InstanceType: ").append(getInstanceType()).append(",");
      }
      if (getPlacement() != null) {
        sb.append("Placement: ").append(getPlacement()).append(",");
      }
      if (getKernelId() != null) {
        sb.append("KernelId: ").append(getKernelId()).append(",");
      }
      if (getRamdiskId() != null) {
        sb.append("RamdiskId: " + getRamdiskId() + ",");
      }
      if (getBlockDeviceMappings() != null) {
        sb.append("BlockDeviceMappings: " + getBlockDeviceMappings() + ",");
      }
      if (isMonitoring() != null) {
        sb.append("Monitoring: ").append(isMonitoring()).append(",");
      }
      if (getSubnetId() != null) {
        sb.append("SubnetId: ").append(getSubnetId()).append(",");
      }
      if (isDisableApiTermination() != null) {
        sb.append("DisableApiTermination: ").append(isDisableApiTermination()).append(",");
      }
      if (getInstanceInitiatedShutdownBehavior() != null) {
        sb.append("InstanceInitiatedShutdownBehavior: ")
            .append(getInstanceInitiatedShutdownBehavior())
            .append(",");
      }
      if (getPrivateIpAddress() != null) {
        sb.append("PrivateIpAddress: ").append(getPrivateIpAddress()).append(",");
      }
      if (getClientToken() != null) {
        sb.append("ClientToken: ").append(getClientToken()).append(",");
      }
      if (getAdditionalInfo() != null) {
        sb.append("AdditionalInfo: ").append(getAdditionalInfo()).append(",");
      }
      if (getNetworkInterfaces() != null) {
        sb.append("NetworkInterfaces: ").append(getNetworkInterfaces()).append(",");
      }
      if (getIamInstanceProfile() != null) {
        sb.append("IamInstanceProfile: ").append(getIamInstanceProfile()).append(",");
      }
      if (isEbsOptimized() != null) {
        sb.append("EbsOptimized: ").append(isEbsOptimized());
      }
      if (getCapacityReservationSpecification() != null) {
        sb.append("CapacityReservationSpecification: ")
            .append(getCapacityReservationSpecification());
      }
    }

    @Override
    public String toString() {
      final StringBuilder sb = new StringBuilder();
      sb.append("{");
      addFields(sb);
      sb.append("}");
      return sb.toString();
    }
  }

  private static class RedactedRunInstancesDryRunRequest extends RedactedRunInstancesRequest {

    private static final long serialVersionUID = 1L;

    @Override
    protected void addFields(final StringBuilder sb) {
      super.addFields(sb);
      sb.append("DryRun: true,");
    }
  }

  public List<Instance> createEC2Instances(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pToken,
      final AWSInstanceSize pInstanceSize,
      final AWSInstanceFamily pInstanceFamily,
      final String pImageId,
      final String pRootDeviceName,
      final int pRootVolumeSizeGB,
      final String pSubnetId,
      final String pUserData,
      final AWSAvailabilityZone pAvailabilityZone,
      final Optional<AWSPhysicalZoneId> pZoneId,
      final int pNumInstances,
      final List<Tag> pTags,
      final String pReservationId,
      final boolean pDryRun,
      final VolumeType pRootVolumeType) {
    final String prettyErr = "Problem creating EC2 instance";

    final RunInstancesRequest request =
        (pDryRun ? new RedactedRunInstancesDryRunRequest() : new RedactedRunInstancesRequest())
            .withInstanceType(pInstanceSize.getInstanceFamilies().get(pInstanceFamily))
            .withImageId(pImageId)
            .withMinCount(pNumInstances)
            .withMaxCount(pNumInstances);

    if (!StringUtils.isEmpty(pSubnetId)) {
      request.withSubnetId(pSubnetId);
    }

    if (pAvailabilityZone != null) {
      request.withPlacement(new Placement(pAvailabilityZone.getName()));
    }

    if (!StringUtils.isEmpty(pToken)) {
      request.withClientToken(pToken);
    }

    if (!StringUtils.isEmpty(pUserData)) {
      request.withUserData(pUserData);
    }

    if (pInstanceFamily.hasCreditSpecification()) {
      request.setCreditSpecification(new CreditSpecificationRequest().withCpuCredits("standard"));
    }

    if (pTags != null && !pTags.isEmpty()) {
      request.setTagSpecifications(
          List.of(new TagSpecification().withResourceType("instance").withTags(pTags)));
    }

    if (!StringUtils.isEmpty(pRootDeviceName)) {
      final EbsBlockDevice rootVolumeDevice =
          new EbsBlockDevice()
              .withVolumeSize(pRootVolumeSizeGB)
              .withVolumeType(pRootVolumeType)
              .withEncrypted(true);
      final BlockDeviceMapping rootVolumeMapping =
          new BlockDeviceMapping().withDeviceName(pRootDeviceName).withEbs(rootVolumeDevice);
      request.withBlockDeviceMappings(rootVolumeMapping);
    }

    if (pReservationId != null) {
      request.withCapacityReservationSpecification(
          new CapacityReservationSpecification()
              .withCapacityReservationTarget(
                  new CapacityReservationTarget().withCapacityReservationId(pReservationId)));
    }

    if (pDryRun) {
      // NOTE: this block will always throw
      final DryRunResult<RunInstancesRequest> dryRunResult;
      final Set<ErrorCode> expectedErrors =
          Set.of(
              NDSErrorCode.NO_CAPACITY,
              AWSErrorCode.DRY_RUN_OPERATION,
              AWSErrorCode.LIMIT_EXCEEDED,
              AWSErrorCode.RESOURCE_COUNT_EXCEEDED);
      // let the AWSApiException bubble up if the request would have failed due to some error
      // condition (e.g., "InsufficientInstanceCapacity")
      dryRunResult =
          getAWSApiCallBuilder(
                  request,
                  getEC2Client(pAWSAccountId, pRegionName)::dryRun,
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .expectedErrorCodes(expectedErrors)
              .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
              .makeApiCall();
      // if the dry-run "succeeded" (i.e., the API responded with the "DryRunOperation" exception
      // indicating success, or an "UnauthorizedOperation" or "AuthFailure" indicating lack of
      // privileges to issue the request), then unpack the underlying exception and bubble up
      throw getAWSApiException(
          dryRunResult.getDryRunResponse(),
          request,
          NDSErrorCode.INTERNAL,
          pLogger,
          prettyErr,
          expectedErrors);
    }

    final RunInstancesResult result;
    try {
      result =
          getAWSApiCallBuilder(
                  request,
                  getEC2Client(pAWSAccountId, pRegionName)::runInstances,
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .throttleRetries(THROTTLING_RETRY_COUNT)
              .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
              .makeApiCall();
    } catch (final AWSApiException e) {
      if (NDSErrorCode.NO_CAPACITY.equals(e.getErrorCode())) {
        String msg =
            "Cannot create instance: EC2 out of capacity or quota for instance size "
                + pInstanceSize
                + " in region "
                + pRegionName
                + " in zone "
                + pZoneId.map(AWSPhysicalZoneId::toString).orElse("Unknown")
                + " due to "
                + e.getErrorCode();
        pLogger.debug(msg);
      }
      throw e;
    }

    if (result.getReservation() == null
        || result.getReservation().getInstances() == null
        || result.getReservation().getInstances().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    result
        .getReservation()
        .getInstances()
        .forEach(
            instance -> {
              _resourceObservable.notify(
                  new AWSResource(
                      pAWSAccountId, pRegionName, Type.EC2_INSTANCE, instance.getInstanceId()),
                  ResourceEvent.CREATED);
            });

    return result.getReservation().getInstances();
  }

  public List<Instance> findEC2InstanceByTags(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final Map<String, String> pTags) {
    final String prettyErr = "Problem finding EC2 instance";
    final DescribeInstancesResult describeInstancesResult =
        getAWSApiCallBuilder(
                new DescribeInstancesRequest().withFilters(getFiltersForTags(pTags)),
                getEC2Client(pAWSAccountId, pRegionName)::describeInstances,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    return describeInstancesResult.getReservations().stream()
        .flatMap(r -> r.getInstances().stream())
        .collect(Collectors.toList());
  }

  public List<Filter> getFiltersForTags(final Map<String, String> pTags) {
    return pTags.entrySet().stream()
        .map(entry -> new Filter("tag:" + entry.getKey(), List.of(entry.getValue())))
        .collect(Collectors.toList());
  }

  public Instance createEC2Instance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pToken,
      final AWSInstanceSize pInstanceSize,
      final AWSInstanceFamily pInstanceFamily,
      final String pImageId,
      final String pRootDeviceName,
      final int pRootVolumeSizeGB,
      final String pSubnetId,
      final Optional<AWSPhysicalZoneId> pZoneId,
      final String pUserData,
      final List<Tag> pTags,
      final String pReservationId,
      final VolumeType pRootVolumeType) {
    return createEC2Instances(
            pAWSAccountId,
            pRegionName,
            pLogger,
            pToken,
            pInstanceSize,
            pInstanceFamily,
            pImageId,
            pRootDeviceName,
            pRootVolumeSizeGB,
            pSubnetId,
            pUserData,
            null,
            pZoneId,
            1,
            pTags,
            pReservationId,
            false,
            pRootVolumeType)
        .get(0);
  }

  public Instance findEC2Instance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId) {

    return findEC2Instances(pAWSAccountId, pRegionName, pLogger, List.of(pEC2InstanceId)).get(0);
  }

  public List<Instance> findEC2Instances(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final List<String> pEC2InstanceIds) {

    if (pEC2InstanceIds.stream().anyMatch(StringUtils::isEmpty)) {
      throw new IllegalArgumentException("`pEC2InstanceIds` cannot include empty string(s)");
    }

    final String prettyErr = "Problem finding EC2 instance";

    final DescribeInstancesResult result =
        getAWSApiCallBuilder(
                new DescribeInstancesRequest().withInstanceIds(pEC2InstanceIds),
                getEC2Client(pAWSAccountId, pRegionName)::describeInstances,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result.getReservations() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    if (result.getReservations().isEmpty()) {
      throw new AWSApiException(prettyErr, CommonErrorCode.NOT_FOUND);
    }

    if (result.getReservations().get(0) == null
        || result.getReservations().get(0).getInstances() == null
        || result.getReservations().get(0).getInstances().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getReservations().stream()
        .map(reservation -> reservation.getInstances().get(0))
        .collect(Collectors.toList());
  }

  public void findEC2InstancesAndApply(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final int pMaxResultSize,
      final Logger pLogger,
      final Consumer<DescribeInstancesResult> resultConsumer) {
    final String prettyError =
        String.format(
            "Problem retrieving EC2 Instances in Region (%s), Account Id (%s)",
            pRegion.getName(), pAWSAccountId.toString());

    final DescribeInstancesRequest request =
        new DescribeInstancesRequest().withMaxResults(pMaxResultSize);

    DescribeInstancesResult result;
    String nextToken = null;
    do {
      if (nextToken != null) {
        request.withNextToken(nextToken);
      }
      result =
          getAWSApiCallBuilder(
                  request,
                  getEC2Client(pAWSAccountId, pRegion)::describeInstances,
                  prettyError,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
              .makeApiCall();
      resultConsumer.accept(result);
      nextToken = result.getNextToken();
    } while (nextToken != null);
  }

  public String findEC2InstancesAndApplyForNextToken(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegion,
      final int pMaxResultSize,
      final String pNextToken,
      final Logger pLogger,
      final Consumer<DescribeInstancesResult> resultConsumer) {
    // This method should not be called with a null pNextToken
    // because a null next token indicates there are no more pages of results
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final String prettyError =
        String.format(
            "Problem retrieving EC2 Instances in Region (%s), Account Id (%s)",
            pRegion.getName(), pAWSAccountId.toString());

    final DescribeInstancesRequest request =
        new DescribeInstancesRequest().withMaxResults(pMaxResultSize);

    if (!pNextToken.equals(AWSApiSvc.TOKEN_START)) {
      request.withNextToken(pNextToken);
    }

    final DescribeInstancesResult result =
        getAWSApiCallBuilder(
                request,
                getEC2Client(pAWSAccountId, pRegion)::describeInstances,
                prettyError,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();
    resultConsumer.accept(result);

    final String newNextToken = result.getNextToken();
    return (newNextToken != null) ? newNextToken : TOKEN_DONE;
  }

  public InstanceStatus getEC2InstanceStatus(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId) {

    return getEC2InstanceStatuses(pAWSAccountId, pRegionName, pLogger, List.of(pEC2InstanceId))
        .get(0);
  }

  public List<InstanceStatus> getEC2InstanceStatuses(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final List<String> pEC2InstanceIds) {

    if (pEC2InstanceIds.stream().anyMatch(StringUtils::isEmpty)) {
      throw new IllegalArgumentException("`pEC2InstanceIds` cannot include empty string(s)");
    }

    final String prettyErr = "Problem getting EC2 instance status";

    final DescribeInstanceStatusResult result =
        getAWSApiCallBuilder(
                new DescribeInstanceStatusRequest().withInstanceIds(pEC2InstanceIds),
                getEC2Client(pAWSAccountId, pRegionName)::describeInstanceStatus,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result.getInstanceStatuses() == null || result.getInstanceStatuses().isEmpty()) {
      throw new AWSApiException(prettyErr, AWSErrorCode.NO_INSTANCE_STATUS_FOUND);
    }

    return result.getInstanceStatuses();
  }

  public InstanceState deleteEC2Instance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId) {
    final String prettyErr = "Problem deleting EC2 instance";

    final TerminateInstancesResult result =
        getAWSApiCallBuilder(
                new TerminateInstancesRequest().withInstanceIds(pEC2InstanceId),
                getEC2Client(pAWSAccountId, pRegionName)::terminateInstances,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result.getTerminatingInstances() == null || result.getTerminatingInstances().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getTerminatingInstances().get(0).getCurrentState();
  }

  public List<Instance> describeEC2Instances(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final List<String> pEC2InstanceIds) {

    final String prettyErr = "Problem describing EC2 instance";
    final DescribeInstancesResult result =
        getAWSApiCallBuilder(
                new DescribeInstancesRequest().withInstanceIds(pEC2InstanceIds),
                getEC2Client(pAWSAccountId, pRegionName)::describeInstances,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    return result.getReservations().stream()
        .flatMap(r -> r.getInstances().stream())
        .collect(Collectors.toList());
  }

  public InstanceState stopEC2Instance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId) {

    final String prettyErr = "Problem stopping EC2 instance";
    final StopInstancesResult result =
        getAWSApiCallBuilder(
                new StopInstancesRequest().withInstanceIds(pEC2InstanceId),
                getEC2Client(pAWSAccountId, pRegionName)::stopInstances,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result.getStoppingInstances() == null || result.getStoppingInstances().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getStoppingInstances().get(0).getCurrentState();
  }

  public InstanceState startEC2Instance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Optional<AWSPhysicalZoneId> pPhysicalZoneId,
      final Logger pLogger,
      final String pEC2InstanceId,
      final AWSInstanceFamily pInstanceFamily,
      final AWSInstanceSize pAWSInstanceSize) {

    final String prettyErr = "Problem starting EC2 instance";

    final StartInstancesResult result;
    try {
      result =
          getAWSApiCallBuilder(
                  new StartInstancesRequest().withInstanceIds(pEC2InstanceId),
                  getEC2Client(pAWSAccountId, pRegionName)::startInstances,
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
              .makeApiCall();
    } catch (final AWSApiException e) {
      if (NDSErrorCode.NO_CAPACITY.equals(e.getErrorCode())) {
        String msg =
            "Cannot start instance with id "
                + pEC2InstanceId
                + ": EC2 out of capacity for instance size "
                + pAWSInstanceSize
                + " in region "
                + pRegionName
                + " in zone "
                + pPhysicalZoneId.map(AWSPhysicalZoneId::toString).orElse("Unknown")
                + ".";
        pLogger.debug(msg);
      }
      throw e;
    }

    if (result.getStartingInstances() == null || result.getStartingInstances().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getStartingInstances().get(0).getCurrentState();
  }

  public void rebootEC2Instance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId) {

    final String prettyErr = "Problem rebooting EC2 instance";

    getAWSApiCallBuilder(
            new RebootInstancesRequest().withInstanceIds(pEC2InstanceId),
            getEC2Client(pAWSAccountId, pRegionName)::rebootInstances,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public String getUserData(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId) {
    final String prettyErr = "Problem retrieving user data";

    final DescribeInstanceAttributeRequest req =
        new DescribeInstanceAttributeRequest()
            .withInstanceId(pEC2InstanceId)
            .withAttribute(InstanceAttributeName.UserData);

    final DescribeInstanceAttributeResult result =
        getAWSApiCallBuilder(
                req,
                getEC2Client(pAWSAccountId, pRegionName)::describeInstanceAttribute,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result.getInstanceAttribute() == null
        || result.getInstanceAttribute().getUserData() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getInstanceAttribute().getUserData();
  }

  public void updateEC2InstanceType(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId,
      final AWSInstanceSize pAWSInstanceSize,
      final AWSInstanceFamily pAWSInstanceFamily) {
    final String prettyErr = "Problem updating instance size";

    final ModifyInstanceAttributeRequest req =
        new ModifyInstanceAttributeRequest()
            .withInstanceId(pEC2InstanceId)
            .withInstanceType(pAWSInstanceSize.getInstanceFamilies().get(pAWSInstanceFamily));
    getAWSApiCallBuilder(
            req,
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).modifyInstanceAttribute(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();
  }

  public String getEC2InstanceCreditSpec(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId) {
    final String prettyErr = "Problem getting instance cpu credit spec";

    final DescribeInstanceCreditSpecificationsRequest req =
        new DescribeInstanceCreditSpecificationsRequest().withInstanceIds(pEC2InstanceId);

    final DescribeInstanceCreditSpecificationsResult result =
        getAWSApiCallBuilder(
                req,
                getEC2Client(pAWSAccountId, pRegionName)::describeInstanceCreditSpecifications,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    return result.getInstanceCreditSpecifications().get(0).getCpuCredits();
  }

  public void updateEC2InstanceCreditSpec(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId,
      final String pCPUCreditOption) {
    final String prettyErr = "Problem updating instance cpu credit spec";

    final ModifyInstanceCreditSpecificationRequest req =
        new ModifyInstanceCreditSpecificationRequest()
            .withInstanceCreditSpecifications(
                new InstanceCreditSpecificationRequest()
                    .withInstanceId(pEC2InstanceId)
                    .withCpuCredits(pCPUCreditOption));
    getAWSApiCallBuilder(
            req,
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).modifyInstanceCreditSpecification(req);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void updateEC2ENASupport(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId) {
    final String prettyErr = "Problem updating instance size";

    final ModifyInstanceAttributeRequest req =
        new ModifyInstanceAttributeRequest().withInstanceId(pEC2InstanceId).withEnaSupport(true);
    getAWSApiCallBuilder(
            req,
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).modifyInstanceAttribute(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void updateInstanceUserData(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId,
      final String pUserData) {
    final String prettyErr = "Problem updating instance user data";
    final ModifyInstanceAttributeRequest req =
        new ModifyInstanceAttributeRequest().withInstanceId(pEC2InstanceId).withUserData(pUserData);
    getAWSApiCallBuilder(
            req,
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).modifyInstanceAttribute(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
        .makeApiCall();
  }

  public void attachEBSVolumeToEC2Instance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId,
      final String pEBSVolumeId,
      final String pDeviceName) {
    final String prettyErr = "Problem attaching EBS volume to EC2 instance";
    getAWSApiCallBuilder(
            new AttachVolumeRequest()
                .withVolumeId(pEBSVolumeId)
                .withInstanceId(pEC2InstanceId)
                .withDevice(pDeviceName),
            getEC2Client(pAWSAccountId, pRegionName)::attachVolume,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(
            Set.of(CommonErrorCode.NOT_FOUND, AWSErrorCode.INCORRECT_STATE, AWSErrorCode.IN_USE))
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public void forceDetachEBSVolumeFromInstance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId,
      final String pDeviceName,
      final String pVolumeId) {
    final String prettyErr = "Problem detaching EBS volume from EC2 instance";

    getAWSApiCallBuilder(
            new DetachVolumeRequest()
                .withDevice(pDeviceName)
                .withForce(
                    true) // Force detachment since the volume would already be unmounted by chef
                .withInstanceId(pEC2InstanceId)
                .withVolumeId(pVolumeId),
            getEC2Client(pAWSAccountId, pRegionName)::detachVolume,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(
            Set.of(CommonErrorCode.NOT_FOUND, AWSErrorCode.INCORRECT_STATE, AWSErrorCode.IN_USE))
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public Pair<String, String> createElasticIp(
      final ObjectId pAWSAccountId, final AWSRegionName pRegionName, final Logger pLogger) {
    final String prettyErr = "Problem creating elastic ip";

    final AllocateAddressResult result =
        getAWSApiCallBuilder(
                new AllocateAddressRequest().withDomain(DomainType.Vpc),
                getEC2Client(pAWSAccountId, pRegionName)::allocateAddress,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    _resourceObservable.notify(
        new AWSResource(pAWSAccountId, pRegionName, Type.ELASTIC_IP, result.getAllocationId()),
        ResourceEvent.CREATED);

    return Pair.of(result.getAllocationId(), result.getPublicIp());
  }

  public Address findElasticIp(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pElasticIpId) {

    if (StringUtils.isEmpty(pElasticIpId)) {
      throw new IllegalArgumentException("`pElasticIpId` is required");
    }

    final String prettyErr = "Problem finding elastic ip";

    final DescribeAddressesResult result =
        getAWSApiCallBuilder(
                new DescribeAddressesRequest().withAllocationIds(pElasticIpId),
                getEC2Client(pAWSAccountId, pRegionName)::describeAddresses,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null || result.getAddresses().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getAddresses().get(0);
  }

  public void associateElasticIpWithEC2Instance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEC2InstanceId,
      final String pElasticIpId) {
    final String prettyErr = "Problem associating elastic ip with EC2 instance";
    getAWSApiCallBuilder(
            new AssociateAddressRequest()
                .withInstanceId(pEC2InstanceId)
                .withAllocationId(pElasticIpId),
            getEC2Client(pAWSAccountId, pRegionName)::associateAddress,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(
            Set.of(
                CommonErrorCode.NOT_FOUND,
                AWSErrorCode.INVALID_INSTANCE_ID,
                AWSErrorCode.INCORRECT_INSTANCE_STATE))
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .recordAndPublishQuotaCapacityError(pAWSAccountId, pRegionName)
        .makeApiCall();
  }

  public void disassociateElasticIpWithEC2Instance(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pAssociationId) {
    final String prettyErr = "Problem disassociating elastic ip from EC2 instance";

    getAWSApiCallBuilder(
            new DisassociateAddressRequest().withAssociationId(pAssociationId),
            getEC2Client(pAWSAccountId, pRegionName)::disassociateAddress,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(
            Set.of(
                CommonErrorCode.NOT_FOUND,
                AWSErrorCode.INVALID_INSTANCE_ID,
                AWSErrorCode.INCORRECT_INSTANCE_STATE))
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public void deleteElasticIp(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pEIPId) {
    final String prettyErr = "Problem deleting elastic ip";

    getAWSApiCallBuilder(
            new ReleaseAddressRequest().withAllocationId(pEIPId),
            r -> {
              getEC2Client(pAWSAccountId, pRegionName).releaseAddress(r);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, AWSErrorCode.IN_USE))
        .makeApiCall();
  }

  public Long getObjectSize(
      final AWSAccount pAwsAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pKey,
      final Logger pLogger) {
    final String prettyErr = "Problem getting object size from S3";

    return getAWSApiCallBuilder(
            new GetObjectMetadataRequest(pBucketName, pKey),
            r ->
                getS3Client(pAwsAccount.getId(), pRegionName)
                    .getObjectMetadata(pBucketName, pKey)
                    .getContentLength(),
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public Long getPublicObjectSize(
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pKey,
      final Logger pLogger) {
    final String prettyErr = "Problem getting object size from S3";

    return getAWSApiCallBuilder(
            new GetObjectMetadataRequest(pBucketName, pKey),
            r ->
                _clientsFactory
                    .getS3Client(
                        new AWSStaticCredentialsProvider(new AnonymousAWSCredentials()),
                        pRegionName.getValue())
                    .getObjectMetadata(pBucketName, pKey)
                    .getContentLength(),
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public DeleteObjectsResult deleteObjects(
      final AWSAccount pAwsAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final List<String> pCpsOplogKeys,
      final Logger pLogger) {
    final String prettyErr = "Failed to delete S3 objects";

    final DeleteObjectsRequest request = new DeleteObjectsRequest(pBucketName);

    final List<DeleteObjectsRequest.KeyVersion> keys = new ArrayList<>(pCpsOplogKeys.size());
    pCpsOplogKeys.stream().map(DeleteObjectsRequest.KeyVersion::new).forEach(keys::add);
    request.setKeys(keys);

    return getAWSApiCallBuilder(
            request,
            r -> {
              final DeleteObjectsResult results =
                  getS3Client(pAwsAccount.getId(), pRegionName).deleteObjects(request);
              return results;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public DeleteObjectsResult deleteObjectsThrottled(
      final AWSAccount pAwsAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final List<String> pListKey,
      final Logger pLogger) {
    final String prettyErr = "Failed to delete S3 objects";

    final DeleteObjectsRequest request = new DeleteObjectsRequest(pBucketName);

    final List<DeleteObjectsRequest.KeyVersion> keys = new ArrayList<>(pListKey.size());
    pListKey.stream().map(DeleteObjectsRequest.KeyVersion::new).forEach(keys::add);
    request.setKeys(keys);

    return getAWSApiCallBuilder(
            request,
            r -> {
              final DeleteObjectsResult results =
                  getS3Client(pAwsAccount.getId(), pRegionName).deleteObjects(request);
              return results;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public void deleteObjectsThrottledWithRetries(
      final AWSAccount pAwsAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final List<String> pListKey,
      final Logger pLogger,
      final int pTimeout,
      final int pRetries) {
    final String prettyErr = "Failed to delete S3 objects";

    final DeleteObjectsRequest request = new DeleteObjectsRequest(pBucketName);

    request.setKeys(
        pListKey.stream().map(DeleteObjectsRequest.KeyVersion::new).collect(Collectors.toList()));

    getAWSApiCallBuilder(
            request,
            r -> {
              final DeleteObjectsResult results =
                  getS3ClientWithRetries(
                          pAwsAccount.getId(), pRegionName, pTimeout, pRetries, pTimeout)
                      .deleteObjects(request);
              return results;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public void setObjectTagging(
      final AWSAccount pAwsAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pKey,
      final String pTagKey,
      final String pTagValue,
      final Logger pLogger) {
    final String prettyErr = "Failed to tag S3 object";

    List<com.amazonaws.services.s3.model.Tag> tags = new ArrayList<>();
    tags.add(new com.amazonaws.services.s3.model.Tag(pTagKey, pTagValue));

    getAWSApiCallBuilder(
            new SetObjectTaggingRequest(pBucketName, pKey, new ObjectTagging(tags)),
            getS3Client(pAwsAccount.getId(), pRegionName)::setObjectTagging,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public void deleteObject(
      final AWSAccount pAwsAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pKey,
      final Logger pLogger) {
    final String prettyErr = "Failed to delete S3 object";

    getAWSApiCallBuilder(
            new DeleteObjectRequest(pBucketName, pKey),
            r -> {
              getS3Client(pAwsAccount.getId(), pRegionName).deleteObject(pBucketName, pKey);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public void deleteObject(
      final AWSCredentials pAWSCredentials,
      final String pS3BucketName,
      final String pKey,
      final Logger pLogger)
      throws IllegalArgumentException {
    final boolean useGovCloudRegion = false;
    final String prettyErr = "Failed to delete S3 object";
    getAWSApiCallBuilder(
            new DeleteObjectRequest(pS3BucketName, pKey),
            r -> {
              getS3Client(pAWSCredentials, useGovCloudRegion).deleteObject(pS3BucketName, pKey);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public Optional<S3Object> getS3Object(
      final AmazonS3 pS3Client, final GetObjectRequest pRequest, final Logger pLogger) {
    final String prettyErr = "Problem fetching file from S3";
    return Optional.ofNullable(
        getAWSApiCallBuilder(
                pRequest, pS3Client::getObject, prettyErr, NDSErrorCode.INTERNAL, pLogger)
            .makeApiCall());
  }

  public Optional<S3Object> getS3Object(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final GetObjectRequest pRequest,
      final Logger pLogger) {
    return getS3Object(getS3Client(pAWSAccountId, pRegionName), pRequest, pLogger);
  }

  public Optional<S3Object> getS3Object(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final GetObjectRequest pRequest,
      final int pTimeout,
      final Logger pLogger) {
    return getS3Object(getS3Client(pAWSAccountId, pRegionName, pTimeout), pRequest, pLogger);
  }

  public void downloadFile(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pKey,
      final File pDestinationFile,
      final Logger pLogger) {
    final String prettyErr = "Problem downloading file from S3";

    getAWSApiCallBuilder(
            new GetObjectRequest(pBucketName, pKey),
            r -> {
              getS3Client(pAWSAccountId, pRegionName).getObject(r, pDestinationFile);
              return null;
            },
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public InputStream downloadPublicFileStream(
      final AWSRegionName pRegionName, final GetObjectRequest pRequest, final Logger pLogger) {
    final String prettyErr = "Problem downloading file stream from S3";

    return getS3Object(
            _clientsFactory.getS3Client(
                new AWSStaticCredentialsProvider(new AnonymousAWSCredentials()),
                pRegionName.getValue()),
            pRequest,
            pLogger)
        .map(S3Object::getObjectContent)
        .orElseThrow(() -> new AWSApiException(prettyErr, CommonErrorCode.NOT_FOUND));
  }

  public InputStream downloadFileStream(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final GetObjectRequest pRequest,
      final int pTimeout,
      final Logger pLogger) {
    final String prettyErr = "Problem downloading file stream from S3";

    return getS3Object(pAWSAccount.getId(), pRegionName, pRequest, pTimeout, pLogger)
        .map(S3Object::getObjectContent)
        .orElseThrow(() -> new AWSApiException(prettyErr, CommonErrorCode.NOT_FOUND));
  }

  public InputStream downloadFileStreamThrottled(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final GetObjectRequest pRequest,
      final int pTimeout,
      final Logger pLogger) {
    final String prettyErr = "Problem downloading file stream from S3";

    return getAWSApiCallBuilder(
            pRequest,
            r ->
                getS3Client(pAWSAccount.getId(), pRegionName, pTimeout)
                    .getObject(r)
                    .getObjectContent(),
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  /**
   * Splits the S3 object download into chunks to avoid reliability issue with long-running open
   * connections.
   */
  public InputStream downloadLargeFileStream(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucket,
      final String pKey,
      final long pChunkSize) {

    final AmazonS3 s3Client = getS3Client(pAWSAccount.getId(), pRegionName);
    return new ChunkedS3InputStream(s3Client, pBucket, pKey, pChunkSize);
  }

  /**
   * Download a large file via a continuous stream with a configured number of retries upon
   * transient exceptions.
   *
   * @param pAWSAccount AWS account metadata containing the S3 bucket.
   * @param pRegionName AWS region containing the S3 bucket.
   * @param pBucket Name of S3 bucket
   * @param pKey Full key under S3 bucket being streamed
   * @param pChunkSize Number of bytes to be returned in any given request. A large file is split up
   *     into chunks of this size.
   * @param pRequestTimeout Request timeout in milliseconds.
   * @param pRetries Maximum number of retries for transient errors
   * @param pSocketTimeout Socket timeout in milliseconds
   * @return Input stream for content of the next chunk of the file from S3.
   */
  public InputStream downloadLargeFileStreamWithRetries(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucket,
      final String pKey,
      final long pChunkSize,
      final int pRequestTimeout,
      final int pRetries,
      final int pSocketTimeout) {

    final AmazonS3 s3Client =
        getS3ClientWithRetries(
            pAWSAccount.getId(), pRegionName, pRequestTimeout, pRetries, pSocketTimeout);
    return new ChunkedS3InputStream(s3Client, pBucket, pKey, pChunkSize);
  }

  public GetObjectRequest getStreamRequest(
      final String pBucketName,
      final String pKey,
      final Logger pLogger,
      final boolean pLogByteTransferUpdates) {

    final ProgressListener progressListener =
        new SyncProgressListener() {
          private long _totalByteTransferred = 0;
          private long _totalBytesToDownload = 0;
          private Date _lastProgressUpdated = null;

          @Override
          public void progressChanged(final ProgressEvent progressEvent) {
            switch (progressEvent.getEventType()) {
              case RESPONSE_CONTENT_LENGTH_EVENT:
                _totalBytesToDownload = progressEvent.getBytes();
                pLogger.info(
                    "About to download {} bytes from {}/{}.",
                    progressEvent.getBytes(),
                    pBucketName,
                    pKey);
                break;
              case RESPONSE_BYTE_TRANSFER_EVENT:
                _totalByteTransferred += progressEvent.getBytesTransferred();
                final Date now = new Date();
                // Log progress update every 5 seconds instead of every event
                if (pLogByteTransferUpdates
                    && (_lastProgressUpdated == null
                        || now.after(DateUtils.addSeconds(_lastProgressUpdated, 5)))) {
                  _lastProgressUpdated = now;
                  pLogger.info(
                      "Downloaded {}/{} bytes from {}/{}.",
                      _totalByteTransferred,
                      _totalBytesToDownload,
                      pBucketName,
                      pKey);
                }
                break;
              case TRANSFER_STARTED_EVENT:
                pLogger.info("Started download from {}/{}.", pBucketName, pKey);
                break;
              case TRANSFER_CANCELED_EVENT:
                pLogger.warn("Download cancelled from {}/{}.", pBucketName, pKey);
                break;
              case TRANSFER_FAILED_EVENT:
                pLogger
                    .atError()
                    .setMessage("Download failed")
                    .addKeyValue("Bucket name", pBucketName)
                    .addKeyValue("Key", pKey)
                    .log();
                break;
              case TRANSFER_COMPLETED_EVENT:
                pLogger.info("Download completed from {}/{}.", pBucketName, pKey);
            }
          }
        };

    final ProgressListener.ExceptionReporter reporter =
        new ProgressListener.ExceptionReporter(progressListener);

    return new GetObjectRequest(pBucketName, pKey).withGeneralProgressListener(reporter);
  }

  protected PartETag uploadPart(
      final String pKey,
      final String pBucketName,
      final String pPrettyErr,
      final int pPartNum,
      final int pSize,
      final byte[] pBuffer,
      final InitiateMultipartUploadResult pInitResponse,
      final AmazonS3 pClient,
      final Logger pLogger) {
    final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(pBuffer, 0, pSize);
    final ObjectMetadata metadata = new ObjectMetadata();
    metadata.setContentLength(pSize);
    metadata.setContentType("application/octet-stream");

    final UploadPartRequest uploadPartRequest =
        new UploadPartRequest()
            .withBucketName(pBucketName)
            .withKey(pKey)
            .withUploadId(pInitResponse.getUploadId())
            .withPartNumber(pPartNum)
            .withInputStream(byteArrayInputStream)
            .withPartSize(pSize)
            .withObjectMetadata(metadata)
            .withGeneralProgressListener(
                new ProgressListener() {
                  private long _totalByteTransferred = 0;
                  private Date _lastProgressUpdated = null;

                  @Override
                  public void progressChanged(final ProgressEvent progressEvent) {
                    switch (progressEvent.getEventType()) {
                      case REQUEST_BYTE_TRANSFER_EVENT:
                        _totalByteTransferred += progressEvent.getBytesTransferred();
                        final Date now = new Date();
                        // Log progress update every 5 seconds instead of every single event
                        if (_lastProgressUpdated == null
                            || now.after(DateUtils.addSeconds(_lastProgressUpdated, 5))) {
                          _lastProgressUpdated = now;
                          pLogger.info(
                              "Uploaded {} bytes of part number {} to {}/{}.",
                              _totalByteTransferred,
                              pPartNum,
                              pBucketName,
                              pKey);
                        }
                        break;
                      case TRANSFER_PART_STARTED_EVENT:
                        pLogger.info(
                            "Started upload for part number {} of {}/{}",
                            pPartNum,
                            pBucketName,
                            pKey);
                        break;
                      case TRANSFER_PART_FAILED_EVENT:
                        pLogger
                            .atError()
                            .setMessage("Upload failed {}")
                            .addKeyValue("Part number", pPartNum)
                            .addKeyValue("Bucket name", pBucketName)
                            .addKeyValue("Key", pKey)
                            .log();
                        break;
                      case TRANSFER_PART_COMPLETED_EVENT:
                        pLogger.info(
                            "Transfer completed for part number {} of {}/{}.",
                            pPartNum,
                            pBucketName,
                            pKey);
                    }
                  }
                });

    final PartETag eTag =
        getAWSApiCallBuilder(
                uploadPartRequest,
                r -> pClient.uploadPart(r).getPartETag(),
                pPrettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    return eTag;
  }

  /**
   * The parallel multipart upload method spawns multiple threads to upload an input stream in
   * parallel. The number of threads you want to run in parallel will depend on the latency to S3
   * and the latency when reading the part size provided from the input stream.
   *
   * <p>Rough calculation is parallelism = ceiling(latency of uploading/latency of input stream) + 1
   *
   * <p>e.g. if it takes 1 second to read 100MB from the input stream and it takes 2 seconds to
   * upload 100MB to S3 then the parallelism should be 3.
   *
   * <p>The overall intuition is to always have one thread reading from the input stream. We never
   * want the input stream to be idle and not being read.
   *
   * @param pAWSAccount the AWS Account containing the bucket to which we are uploading.
   * @param pBucketName the name of the S3 bucket to which we are uploading.
   * @param pKey the key that all data in the input stream will be uploaded to in S3.
   * @param pInputStream the stream of data that will be uploaded to S3.
   * @param pLogger the Logger of the current consumer of the method.
   * @param pRegionName the name of the AWS Region that contains the bucket.
   * @param pPartSizeBytes the size of the parts that will be uploaded to S3 in bytes.
   * @param pWorkerThreadCount the number of threads that will participate in the upload.
   * @param pTimeoutSeconds the amount of time to wait for the upload before aborting the upload.
   * @throws InterruptedException caller should consider the upload failed when this error is
   *     returned.
   */
  public void parallelMultipartUpload(
      final AWSAccount pAWSAccount,
      final String pBucketName,
      final String pKey,
      final InputStream pInputStream,
      final Logger pLogger,
      final AWSRegionName pRegionName,
      final int pPartSizeBytes,
      final int pWorkerThreadCount,
      final int pTimeoutSeconds)
      throws InterruptedException {
    if (pPartSizeBytes < 5 * 1024 * 1024) {
      throw new AWSApiException(
          "part size must be greater than or equal to 5 MB; was " + pPartSizeBytes + " bytes",
          CommonErrorCode.BAD_REQUEST);
    }

    final String prettyErr = "Problem uploading input stream to S3";
    final ConcurrentLinkedDeque<PartETag> partETags = new ConcurrentLinkedDeque<>();
    final ConcurrentLinkedDeque<Exception> exceptions = new ConcurrentLinkedDeque<>();
    final AtomicInteger currentPartNumber = new AtomicInteger(0);
    final Lock inputStreamReaderLock = new ReentrantLock();
    final CountDownLatch doneSignal = new CountDownLatch(pWorkerThreadCount);
    final Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();

    // Step 1: Initiate the multipart upload
    final AmazonS3 initiateUploadClient = getS3Client(pAWSAccount.getId(), pRegionName);
    final InitiateMultipartUploadRequest initRequest =
        new InitiateMultipartUploadRequest(pBucketName, pKey);
    final InitiateMultipartUploadResult initResponse =
        getAWSApiCallBuilder(
                initRequest,
                initiateUploadClient::initiateMultipartUpload,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    // The Runnable is executed in parallel on a dedicated thread
    final Runnable uploadPartRunnable =
        () -> {
          try {
            MDC.setContextMap(mdcContextMap);
            // NB(pt): we have to check the state of the thread's interrupted flag to ensure that we
            // should continue. If the flag is set to interrupted it means that we should halt and
            // exit.
            while (!Thread.currentThread().isInterrupted()) {
              int bytesRead = 0;
              int lastBytesRead = 0;
              byte[] buffer = new byte[pPartSizeBytes * 2];
              int partNumber;

              inputStreamReaderLock.lock();
              try {
                // Read the next part from the input stream up to the part size.
                partNumber = currentPartNumber.incrementAndGet();
                while (bytesRead < pPartSizeBytes) {
                  lastBytesRead = pInputStream.read(buffer, bytesRead, pPartSizeBytes - bytesRead);

                  if (Thread.currentThread().isInterrupted()) {
                    doneSignal.countDown();
                    return;
                  }

                  // -1 signifies that the input stream is now empty so we exit and upload any data
                  // obtained so far.
                  if (lastBytesRead == -1) {
                    break;
                  }

                  bytesRead += lastBytesRead;
                }
              } catch (Exception e) {
                pLogger.error("failed to pull data from stream with error", e);
                exceptions.add(e);
                // end loop and let thread signal it's done before terminating.
                break;
              } finally {
                // Release the inputStreamReaderLock to allow a waiting thread to read from the
                // input stream.
                inputStreamReaderLock.unlock();
              }

              if ((lastBytesRead == -1 && bytesRead > 0) || bytesRead >= pPartSizeBytes) {
                try {
                  pLogger.info(
                      String.format("Uploading part %s with size %s to S3", partNumber, bytesRead));
                  final AmazonS3 uploadClient = getS3Client(pAWSAccount.getId(), pRegionName);
                  final PartETag partETag =
                      uploadPart(
                          pKey,
                          pBucketName,
                          prettyErr,
                          partNumber,
                          bytesRead,
                          buffer,
                          initResponse,
                          uploadClient,
                          pLogger);
                  partETags.add(partETag);
                } catch (Exception e) {
                  pLogger.error(
                      String.format(
                          "failed to upload part %s with size %s to S3 with error",
                          partNumber, bytesRead),
                      e);
                  exceptions.add(e);
                  // end loop and let thread signal it's done before terminating.
                  break;
                }
              }

              // end loop when input stream is empty.
              if (lastBytesRead == -1) {
                break;
              }
            }
          } finally {
            // signal that the thread has completed uploading.
            doneSignal.countDown();
            MDC.clear();
          }
        };

    // Step 2: Upload the parts consuming the input stream.
    final List<Thread> runningThreads =
        IntStream.range(0, pWorkerThreadCount)
            .mapToObj(
                i -> {
                  final Thread thread = new Thread(uploadPartRunnable);
                  thread.start();
                  return thread;
                })
            .toList();

    // Block main thread waiting on all spawned worker threads signalling they have completed their
    // work.
    final AmazonS3 abortUploadClient = getS3Client(pAWSAccount.getId(), pRegionName);
    if (!doneSignal.await(pTimeoutSeconds, TimeUnit.SECONDS)) {
      // Timed out before completing the upload, halting all running worker threads.
      runningThreads.forEach(Thread::interrupt);
      // Wait for threads to halt.
      doneSignal.await();
      getAWSApiCallBuilder(
              new AbortMultipartUploadRequest(pBucketName, pKey, initResponse.getUploadId()),
              r -> {
                abortUploadClient.abortMultipartUpload(r);
                return null;
              },
              prettyErr,
              NDSErrorCode.INTERNAL,
              pLogger)
          .makeApiCall();
      throw new AWSApiException(NDSErrorCode.AWS_S3_UPLOAD_TIMEOUT);
    }

    final List<Exception> actualExceptions = exceptions.stream().filter(Objects::nonNull).toList();
    if (!actualExceptions.isEmpty()) {
      getAWSApiCallBuilder(
              new AbortMultipartUploadRequest(pBucketName, pKey, initResponse.getUploadId()),
              r -> {
                abortUploadClient.abortMultipartUpload(r);
                return null;
              },
              prettyErr,
              NDSErrorCode.INTERNAL,
              pLogger)
          .makeApiCall();
      throw new AWSApiException(NDSErrorCode.AWS_S3_UPLOAD_EXCEPTION);
    }

    // Step 3: Complete the upload.
    final AmazonS3 completeUploadClient = getS3Client(pAWSAccount.getId(), pRegionName);
    final List<PartETag> completedParts = partETags.stream().filter(Objects::nonNull).toList();

    final CompleteMultipartUploadRequest completeRequest =
        new CompleteMultipartUploadRequest()
            .withBucketName(pBucketName)
            .withKey(pKey)
            .withUploadId(initResponse.getUploadId())
            .withPartETags(completedParts);
    try {
      completeUploadClient.completeMultipartUpload(completeRequest);
    } catch (final AmazonServiceException ase) {
      getAWSApiCallBuilder(
              new AbortMultipartUploadRequest(pBucketName, pKey, initResponse.getUploadId()),
              r -> {
                completeUploadClient.abortMultipartUpload(r);
                return null;
              },
              prettyErr,
              NDSErrorCode.INTERNAL,
              pLogger)
          .makeApiCall();

      pLogger.debug("Multipart Upload to AWS failed and was aborted");

      throw getAWSApiException(
          ase, completeRequest, NDSErrorCode.INTERNAL, pLogger, prettyErr, Collections.emptySet());
    }
  }

  public void multipartUpload(
      final AWSAccount pAWSAccount,
      final String pBucketName,
      final String pKey,
      final InputStream pInputStream,
      final Logger pLogger,
      final AWSRegionName pRegionName)
      throws IOException {
    final String prettyErr = "Problem uploading input stream to S3";

    final AmazonS3 client = getS3Client(pAWSAccount.getId(), pRegionName);
    final InitiateMultipartUploadRequest initRequest =
        new InitiateMultipartUploadRequest(pBucketName, pKey);
    final List<PartETag> partETags = new ArrayList<>();

    final InitiateMultipartUploadResult initResponse =
        getAWSApiCallBuilder(
                initRequest,
                r -> client.initiateMultipartUpload(r),
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    final int partSize = (int) Units.MEGABYTES.convertTo(5, Units.BYTES);

    int partNum = 1;
    int len = 0;

    // We continue filling the buffer until we reach partSize or more, so we need to double the
    // space to ensure we can
    // always write to the buffer.
    final byte[] buffer = new byte[partSize * 2];
    int size = 0;

    // NOTE: because this upload request has to be done synchronously, we are setting the timeout
    // explicitly in the while loop
    final Date uploadTimeoutDate = DateUtils.addHours(new Date(), 1);
    while ((len = pInputStream.read(buffer, size, partSize)) != -1) {
      if (new Date().after(uploadTimeoutDate)) {
        throw new AWSApiException(NDSErrorCode.AWS_S3_UPLOAD_TIMEOUT);
      }
      size += len;

      if (size >= partSize) {
        final PartETag eTag =
            uploadPart(
                pKey, pBucketName, prettyErr, partNum, size, buffer, initResponse, client, pLogger);
        partETags.add(eTag);
        partNum++;
        size = 0;
      }
    }

    if (size > 0) {
      final PartETag eTag =
          uploadPart(
              pKey, pBucketName, prettyErr, partNum, size, buffer, initResponse, client, pLogger);
      partETags.add(eTag);
    }

    final CompleteMultipartUploadRequest completeRequest =
        new CompleteMultipartUploadRequest()
            .withBucketName(pBucketName)
            .withKey(pKey)
            .withUploadId(initResponse.getUploadId())
            .withPartETags(partETags);

    try {
      client.completeMultipartUpload(completeRequest);
    } catch (final AmazonServiceException ase) {
      getAWSApiCallBuilder(
              new AbortMultipartUploadRequest(pBucketName, pKey, initResponse.getUploadId()),
              r -> {
                client.abortMultipartUpload(r);
                return null;
              },
              prettyErr,
              NDSErrorCode.INTERNAL,
              pLogger)
          .makeApiCall();

      pLogger.debug("Multipart Upload to AWS failed and was aborted");

      throw getAWSApiException(
          ase, completeRequest, NDSErrorCode.INTERNAL, pLogger, prettyErr, Collections.emptySet());
    }
  }

  public URL getPresignedDownloadURL(
      final Credentials pCredentials,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pObjectKey,
      final Date pExpiration,
      final Logger pLogger) {
    final String prettyErr = "Error getting presigned download URL";

    return getAWSApiCallBuilder(
            new GeneratePresignedUrlRequest(pBucketName, pObjectKey)
                .withMethod(HttpMethod.GET)
                .withExpiration(pExpiration),
            getS3Client(pCredentials, pRegionName)::generatePresignedUrl,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public void deleteRoute53HealthCheck(
      final ObjectId pAWSAccountId, final Logger pLogger, String pHealthCheckId) {
    final String prettyErr = "Problem deleting Route53 Health Check";
    DeleteHealthCheckRequest request =
        new DeleteHealthCheckRequest().withHealthCheckId(pHealthCheckId);

    getAWSApiCallBuilder(
            request,
            getRoute53Client(pAWSAccountId)::deleteHealthCheck,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  public String createRoute53TCPHealthCheck(
      final ObjectId pAWSAccountId,
      final Logger pLogger,
      final String pCallerReference,
      final String pIpAddress,
      final int pPort) {
    final String prettyErr = "Problem creating Route53 Health Check";
    HealthCheckConfig config =
        new HealthCheckConfig().withType("TCP").withPort(pPort).withIPAddress(pIpAddress);
    CreateHealthCheckRequest request =
        new CreateHealthCheckRequest()
            .withHealthCheckConfig(config)
            .withCallerReference(pCallerReference);

    CreateHealthCheckResult result =
        getAWSApiCallBuilder(
                request,
                getRoute53Client(pAWSAccountId)::createHealthCheck,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null
        || result.getHealthCheck() == null
        || result.getHealthCheck().getId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getHealthCheck().getId();
  }

  public HealthCheck findRoute53HealthCheck(
      final ObjectId pAWSAccountId, final Logger pLogger, String pId) {
    final String prettyErr = String.format("Problem getting Route53 Health Check with Id: %s", pId);

    GetHealthCheckRequest request = new GetHealthCheckRequest().withHealthCheckId(pId);

    GetHealthCheckResult result =
        getAWSApiCallBuilder(
                request,
                getRoute53Client(pAWSAccountId)::getHealthCheck,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null
        || result.getHealthCheck() == null
        || result.getHealthCheck().getId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getHealthCheck();
  }

  public boolean changeTagsForHealthCheckResource(
      final ObjectId pAWSAccountId, final Logger pLogger, String pId, Map<String, String> pTags) {
    final String prettyErr = String.format("Problem changing tags for resource with Id: %s", pId);

    ChangeTagsForResourceRequest request =
        new ChangeTagsForResourceRequest()
            .withResourceType("healthcheck")
            .withResourceId(pId)
            .withAddTags(
                pTags.entrySet().stream()
                    .map(
                        e ->
                            new com.amazonaws.services.route53.model.Tag()
                                .withKey(e.getKey())
                                .withValue(e.getValue()))
                    .toList()
                    .toArray(new com.amazonaws.services.route53.model.Tag[0]));

    ChangeTagsForResourceResult result =
        getAWSApiCallBuilder(
                request,
                getRoute53Client(pAWSAccountId)::changeTagsForResource,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return true;
  }

  public String changeDNSRecords(
      final ObjectId pAWSAccountId,
      final Logger pLogger,
      final String pHostedZoneId,
      final List<Change> pChanges) {
    final String prettyErr = String.format("Problem changing DNS records %s", pChanges);
    final ChangeResourceRecordSetsResult result =
        getAWSApiCallBuilder(
                new ChangeResourceRecordSetsRequest()
                    .withHostedZoneId(pHostedZoneId)
                    .withChangeBatch(new ChangeBatch().withChanges(pChanges)),
                getRoute53Client(pAWSAccountId)::changeResourceRecordSets,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, AWSErrorCode.TOO_FAST))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null
        || result.getChangeInfo() == null
        || result.getChangeInfo().getId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    pChanges.forEach(
        change -> {
          final ChangeAction changeAction = ChangeAction.fromValue(change.getAction());
          _dnsObservable.notify(
              org.apache.commons.lang3.tuple.Pair.of(pHostedZoneId, change), changeAction);
        });
    return result.getChangeInfo().getId();
  }

  private String changeDNSRecord(
      final ObjectId pAWSAccountId,
      final Logger pLogger,
      final String pHostedZoneId,
      final ChangeAction pChangeAction,
      final String pDNSRecord,
      final RRType pRecordType,
      final List<String> pTargetValues,
      final long pTTL) {
    final String prettyErr =
        String.format(
            "Problem changing (%s) DNS %s Record", pChangeAction.toString(), pRecordType.name());

    final List<ResourceRecord> resourceRecords =
        pTargetValues.stream().map(ResourceRecord::new).collect(Collectors.toList());
    final ResourceRecordSet resourceRecordSet =
        new ResourceRecordSet()
            .withName(pDNSRecord)
            .withType(pRecordType)
            .withResourceRecords(resourceRecords)
            .withTTL(pTTL);

    final ChangeResourceRecordSetsResult result =
        getAWSApiCallBuilder(
                new ChangeResourceRecordSetsRequest()
                    .withHostedZoneId(pHostedZoneId)
                    .withChangeBatch(
                        new ChangeBatch()
                            .withChanges(
                                new Change()
                                    .withAction(pChangeAction)
                                    .withResourceRecordSet(resourceRecordSet))),
                getRoute53Client(pAWSAccountId)::changeResourceRecordSets,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, AWSErrorCode.TOO_FAST))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null
        || result.getChangeInfo() == null
        || result.getChangeInfo().getId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getChangeInfo().getId();
  }

  public Optional<String> findHostedZoneFirstNameServer(
      final ObjectId pAWSAccountId, final Logger pLogger, final String pHostedZoneId) {
    final String prettyErr = "Problem finding hosted zone";

    final GetHostedZoneResult result =
        getAWSApiCallBuilder(
                new GetHostedZoneRequest().withId(pHostedZoneId),
                getRoute53Client(pAWSAccountId)::getHostedZone,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null || result.getDelegationSet() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getDelegationSet().getNameServers().stream().findFirst();
  }

  public Optional<ResourceRecordSet> findDNSRecordByIdentifier(
      final ObjectId pAWSAccountId,
      final Logger pLogger,
      final String pIdentifier,
      final String pHostedZoneId,
      final String pDNSRecord,
      final RRType pRecordType) {
    final String prettyErr =
        String.format("Problem finding DNS Record with identifier: %s", pIdentifier);

    final ListResourceRecordSetsResult result =
        getAWSApiCallBuilder(
                new ListResourceRecordSetsRequest()
                    .withStartRecordIdentifier(pIdentifier)
                    .withHostedZoneId(pHostedZoneId)
                    .withStartRecordName(pDNSRecord)
                    .withStartRecordType(pRecordType),
                getRoute53Client(pAWSAccountId)::listResourceRecordSets,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null || result.getResourceRecordSets() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    if (pDNSRecord.contains("*")) {
      return result.getResourceRecordSets().stream()
          .filter(r -> r.getName().replace("\\052", "*").replaceAll("\\.$", "").equals(pDNSRecord))
          .filter(r -> r.getSetIdentifier().equals(pIdentifier))
          .findFirst();
    }

    return result.getResourceRecordSets().stream()
        .filter(r -> r.getName().replaceAll("\\.$", "").equals(pDNSRecord))
        .filter(r -> r.getSetIdentifier().equals(pIdentifier))
        .findFirst();
  }

  public Optional<HostedZone> findHostedZone(
      final ObjectId pAWSAccountId, final Logger pLogger, final String pHostedZoneId) {
    final String prettyErr =
        String.format("Problem finding hosted zone with ID: %s", pHostedZoneId);

    final GetHostedZoneRequest request = new GetHostedZoneRequest().withId(pHostedZoneId);
    final GetHostedZoneResult result =
        getAWSApiCallBuilder(
                request,
                getRoute53Client(pAWSAccountId)::getHostedZone,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result.getHostedZone() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return Optional.of(result.getHostedZone());
  }

  public Optional<ResourceRecordSet> findDNSRecord(
      final ObjectId pAWSAccountId,
      final Logger pLogger,
      final String pHostedZoneId,
      final String pDNSRecord,
      final RRType pRecordType) {
    final String prettyErr = String.format("Problem finding DNS %s Record", pRecordType.name());

    final ListResourceRecordSetsResult result =
        getAWSApiCallBuilder(
                new ListResourceRecordSetsRequest()
                    .withHostedZoneId(pHostedZoneId)
                    .withStartRecordName(pDNSRecord)
                    .withStartRecordType(pRecordType),
                getRoute53Client(pAWSAccountId)::listResourceRecordSets,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null || result.getResourceRecordSets() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    if (pDNSRecord.contains("*")) {
      return result.getResourceRecordSets().stream()
          .filter(r -> !r.getResourceRecords().isEmpty())
          .filter(r -> r.getName().replace("\\052", "*").replaceAll("\\.$", "").equals(pDNSRecord))
          .findFirst();
    }

    return result.getResourceRecordSets().stream()
        .filter(r -> !r.getResourceRecords().isEmpty())
        .filter(r -> r.getName().replaceAll("\\.$", "").equals(pDNSRecord))
        .findFirst();
  }

  public String upsertDNSRecord(
      final ObjectId pAWSAccountId,
      final Logger pLogger,
      final RRType pType,
      final String pHostedZoneId,
      final String pDNSRecord,
      final List<String> pTargetValues,
      final long pTTL) {
    return changeDNSRecord(
        pAWSAccountId,
        pLogger,
        pHostedZoneId,
        ChangeAction.UPSERT,
        pDNSRecord,
        pType,
        pTargetValues,
        pTTL);
  }

  public String deleteDNSRecord(
      final ObjectId pAWSAccountId,
      final Logger pLogger,
      final RRType pType,
      final String pHostedZoneId,
      final String pDNSRecord,
      final List<String> pTargetValues,
      final long pTTL) {
    return changeDNSRecord(
        pAWSAccountId,
        pLogger,
        pHostedZoneId,
        ChangeAction.DELETE,
        pDNSRecord,
        pType,
        pTargetValues,
        pTTL);
  }

  public ChangeInfo getDNSChangeInfo(
      final ObjectId pAWSAccountId, final Logger pLogger, final String pChangeId) {
    final String prettyErr = "Problem upserting DNS A Record";

    final GetChangeResult result =
        getAWSApiCallBuilder(
                new GetChangeRequest().withId(pChangeId),
                getRoute53Client(pAWSAccountId)::getChange,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND))
            .makeApiCall();

    if (result == null || result.getChangeInfo() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getChangeInfo();
  }

  public Image findImage(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pImageId) {

    if (StringUtils.isEmpty(pImageId)) {
      throw new IllegalArgumentException("`pImageId` is required");
    }

    final String prettyErr = "Problem finding image";

    final DescribeImagesResult result =
        getAWSApiCallBuilder(
                new DescribeImagesRequest().withImageIds(pImageId),
                getEC2Client(pAWSAccountId, pRegionName)::describeImages,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();

    if (result == null || result.getImages() == null || result.getImages().isEmpty()) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getImages().get(0);
  }

  public KeyMetadata findKeyMetadata(
      final String pAccessKey,
      final String pSecretKey,
      final String pSessionToken,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pKeyId) {
    final String prettyErr = "Problem finding key";

    final DescribeKeyResult result =
        getAWSApiCallBuilder(
                new DescribeKeyRequest().withKeyId(pKeyId),
                getKMSClient(pAccessKey, pSecretKey, pSessionToken, pRegionName)::describeKey,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(
                Set.of(
                    CommonErrorCode.NOT_FOUND,
                    CommonErrorCode.NO_AUTHORIZATION,
                    CommonErrorCode.INVALID_PARAMETER))
            .makeApiCall();

    if (result == null || result.getKeyMetadata() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.INTERNAL);
    }

    return result.getKeyMetadata();
  }

  public void checkEncryptDecrypt(
      final String pAccessKey,
      final String pSecretKey,
      final String pSessionToken,
      final AWSRegionName pRegionName,
      final Logger pLogger,
      final String pKeyId) {
    final String prettyErr = "Problem checking encrypt and decrypt access for key";

    final String textToEncrypt = "This text is for testing";

    final ByteBuffer plainText = ByteBuffer.wrap(textToEncrypt.getBytes());

    // test encrypt
    final EncryptResult encryptResult =
        getAWSApiCallBuilder(
                new EncryptRequest()
                    .addEncryptionContextEntry("Purpose", "MongoDB Atlas - Test key access")
                    .withKeyId(pKeyId)
                    .withPlaintext(plainText),
                getKMSClient(pAccessKey, pSecretKey, pSessionToken, pRegionName)::encrypt,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.NO_AUTHORIZATION))
            .makeApiCall();

    if (encryptResult == null || encryptResult.getKeyId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT);
    }

    // test decrypt
    final ByteBuffer cipherBlob = encryptResult.getCiphertextBlob().asReadOnlyBuffer();
    final byte[] bytes = new byte[cipherBlob.remaining()];
    cipherBlob.get(bytes);

    final String encryptedCipherText = Base64.getEncoder().encodeToString(bytes);
    final ByteBuffer bufferToDecrypt =
        ByteBuffer.wrap(Base64.getDecoder().decode(encryptedCipherText));

    final DecryptResult decryptResult =
        getAWSApiCallBuilder(
                new DecryptRequest()
                    .addEncryptionContextEntry("Purpose", "MongoDB Atlas - Test key access")
                    .withCiphertextBlob(bufferToDecrypt),
                getKMSClient(pAccessKey, pSecretKey, pSessionToken, pRegionName)::decrypt,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    if (decryptResult == null || decryptResult.getKeyId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT);
    }
  }

  public EncryptResult encrypt(
      final String pAccessKey,
      final String pSecretKey,
      final String pSessionToken,
      final AWSRegionName pRegionName,
      final String pKeyId,
      final ByteBuffer plainText,
      final Logger pLogger) {
    final String prettyErr = "Problem encrypting plaintext";

    return getAWSApiCallBuilder(
            new EncryptRequest().withKeyId(pKeyId).withPlaintext(plainText),
            getKMSClient(pAccessKey, pSecretKey, pSessionToken, pRegionName)::encrypt,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.NO_AUTHORIZATION))
        .makeApiCall();
  }

  public void decrypt(
      final String pAccessKey,
      final String pSecretKey,
      final String pSessionToken,
      final AWSRegionName pRegionName,
      final ByteBuffer pBufferToDecrypt,
      final Logger pLogger) {
    final String prettyErr = "Problem decrypting cipher text";

    final DecryptResult decryptResult =
        getAWSApiCallBuilder(
                new DecryptRequest().withCiphertextBlob(pBufferToDecrypt),
                getKMSClient(pAccessKey, pSecretKey, pSessionToken, pRegionName)::decrypt,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    if (decryptResult == null || decryptResult.getKeyId() == null) {
      throw new AWSApiException(prettyErr, NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT);
    }
  }

  /**
   * Wrapper utility to retrieve temporary session credentials for a caller (typically a data plane
   * node). If the timeout is shorter than one hour, the cloudcontrol-root role (assumeRoleRoleArn)
   * will be assumed. This method falls back to getFederationToken if the caller is not using
   * session credentials, or if the credentials must be persisted for longer than one hour. This is
   * because AWS disallows role chaining for durations longer than one hour.
   *
   * @param pAWSAccount AWSAccount to grant permissions for. Note that if assumeRoleRoleArn is
   *     present the credentials may be granted for this role instead.
   * @param pRegionName used by getFederationToken
   * @param pName session username specific to the temporary credentials
   * @param pDuration expiration time for temporary credentials
   * @param pPolicy permissions for the assumed role / federation token
   * @return temporary AWS credentials with specified scope and duration
   */
  public Credentials getTemporarySessionCredentials(
      final AWSAccount pAWSAccount,
      final RegionName pRegionName,
      final String pName,
      final Duration pDuration,
      final Policy pPolicy,
      final Logger pLogger) {
    final String assumeRoleArn =
        pAWSAccount.getAssumeRoleARN().orElse(getAssumeRoleRoleArn(pAWSAccount));
    final AWSCredentialsProvider credentialsProvider = getCredentials(pAWSAccount.getId());
    if (pDuration.compareTo(MAX_DURATION_FOR_ASSUME_ROLE) <= 0
        && assumeRoleArn != null
        && credentialsProvider.getCredentials() instanceof AWSSessionCredentials) {
      try {
        return assumeRole(
            credentialsProvider,
            assumeRoleArn,
            null,
            pName,
            pPolicy,
            pDuration,
            pLogger,
            PromMetricsSvc.ErrorSource.CONTROL_PLANE_INTERNAL);
      } catch (final AWSApiException pE) {
        pLogger.error(
            "Encountered an unexpected error self-assuming role {} from account {}. Falling back to"
                + " federation token. federatedUsername={}, policy={}",
            assumeRoleArn,
            pAWSAccount.getId(),
            pName,
            pPolicy.toJson(),
            pE);
      }
    }
    return getFederationToken(pAWSAccount.getId(), pRegionName, pName, pDuration, pPolicy, pLogger);
  }

  public Credentials getTemporarySessionCredentials(
      final ObjectId pAWSAccountId,
      final RegionName pRegionName,
      final String pName,
      final Duration pDuration,
      final Policy pPolicy,
      final Logger pLogger) {
    return getTemporarySessionCredentials(
        getAWSAccount(pAWSAccountId), pRegionName, pName, pDuration, pPolicy, pLogger);
  }

  Credentials getFederationToken(
      final ObjectId pAWSAccountId,
      final RegionName pRegionName,
      final String pName,
      final Duration pDuration,
      final Policy pPolicy,
      final Logger pLogger) {
    final String prettyErr = "Problem getting federation token";

    final int durationInSeconds = (int) pDuration.toSeconds();
    final String policyString = pPolicy.toJson();

    if (pName.length() < 2 || pName.length() > 32) {
      throw new IllegalArgumentException("Name length cannot be less than 2 or greater than 32");
    }

    if (durationInSeconds < 900 || durationInSeconds > 129600) {
      throw new IllegalArgumentException("Duration in seconds must be in range [900, 129600]");
    }

    final GetFederationTokenResult getFederationTokenResult =
        getAWSApiCallBuilder(
                new GetFederationTokenRequest()
                    .withName(pName)
                    .withDurationSeconds(durationInSeconds)
                    .withPolicy(policyString),
                getSTSClient(
                        pAWSAccountId,
                        pRegionName,
                        List.of(AWSApiCredentialsProviderImplementations.STATIC))
                    ::getFederationToken,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();
    return getFederationTokenResult.getCredentials();
  }

  public Credentials assumeRole(
      final AWSCredentialsProvider pProvider,
      final String pRoleARN,
      final String pExternalId,
      final String pRoleSessionName,
      final Policy pInlinedPolicy,
      final Duration pDuration,
      final Logger pLogger)
      throws AWSApiException {
    return assumeRole(
        pProvider,
        pRoleARN,
        pExternalId,
        pRoleSessionName,
        pInlinedPolicy,
        pDuration,
        pLogger,
        ErrorSource.CONTROL_PLANE_INTERNAL);
  }

  public Credentials assumeRole(
      final AWSCredentialsProvider pProvider,
      final String pRoleARN,
      final String pExternalId,
      final String pRoleSessionName,
      final Policy pInlinedPolicy,
      final Duration pDuration,
      final Logger pLogger,
      final ErrorSource pErrorSource)
      throws AWSApiException {
    final String prettyErr = "Problem assuming the role";
    final int durationInSeconds = (int) pDuration.toSeconds();

    if (durationInSeconds < 900) {
      throw new IllegalArgumentException("Duration in seconds must be greater than 900");
    }

    final AWSSecurityTokenService stsClient =
        getClientsFactory()
            .getSTSClient(
                pProvider, NDSSettings.isGovCloudArn(pRoleARN), NDSSettings.isCNArn(pRoleARN));

    final AssumeRoleRequest request =
        new AssumeRoleRequest()
            .withRoleArn(pRoleARN)
            .withExternalId(pExternalId)
            .withDurationSeconds(durationInSeconds)
            .withRoleSessionName(pRoleSessionName);
    if (pInlinedPolicy != null) {
      request.withPolicy(pInlinedPolicy.toJson());
    }

    final AssumeRoleResult assumeRoleResult =
        getAWSApiCallBuilder(
                request, stsClient::assumeRole, prettyErr, NDSErrorCode.INTERNAL, pLogger)
            .makeApiCall();

    if (pProvider.getCredentials() instanceof AWSSessionCredentials) {
      // if the original credentials provider was already a temporary session, we don't need to get
      // a new session token
      return assumeRoleResult.getCredentials();
    } else {

      final GetSessionTokenRequest getSessionTokenRequest =
          new GetSessionTokenRequest().withDurationSeconds(durationInSeconds);

      final GetSessionTokenResult getSessionTokenResult =
          getAWSApiCallBuilder(
                  getSessionTokenRequest,
                  r ->
                      stsClient
                          .getSessionToken(r)
                          .withCredentials(assumeRoleResult.getCredentials()),
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .errorSource(pErrorSource)
              .makeApiCall();

      return getSessionTokenResult.getCredentials();
    }
  }

  public Credentials assumeRole(
      final ObjectId pAWSAccountId,
      final String pRoleARN,
      final String pExternalId,
      final String pRoleSessionName,
      final Policy pInlinedPolicy,
      final Duration pDuration,
      final Logger pLogger,
      final ErrorSource pErrorSource) {
    return assumeRole(
        pAWSAccountId,
        null,
        pRoleARN,
        pExternalId,
        pRoleSessionName,
        pInlinedPolicy,
        pDuration,
        pLogger,
        pErrorSource);
  }

  public Credentials assumeRole(
      final ObjectId pAWSAccountId,
      final List<AWSApiCredentialsProviderImplementations> pCredentialsProviderImplementations,
      final String pRoleARN,
      final String pExternalId,
      final String pRoleSessionName,
      final Policy pInlinedPolicy,
      final Duration pDuration,
      final Logger pLogger,
      final ErrorSource pErrorSource) {
    return assumeRole(
        getCredentials(pAWSAccountId, pCredentialsProviderImplementations),
        pRoleARN,
        pExternalId,
        pRoleSessionName,
        pInlinedPolicy,
        pDuration,
        pLogger,
        pErrorSource);
  }

  public PutObjectResult putObject(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pKey,
      final String pInput,
      final Logger pLogger) {
    final ObjectMetadata metadata = assembleObjectMetadata(pInput);
    final InputStream stream = new ByteArrayInputStream(pInput.getBytes(StandardCharsets.UTF_8));
    return putObject(pAWSAccount, pRegionName, pBucketName, pKey, stream, metadata, pLogger);
  }

  protected ObjectMetadata assembleObjectMetadata(final String pInput) {
    final ObjectMetadata metadata = new ObjectMetadata();
    // IOUtils.toByteArray(pInput);
    final byte[] contentBytes = pInput.getBytes(StandardCharsets.UTF_8);
    final long contentLength = contentBytes.length;
    metadata.setContentLength(contentLength);
    return metadata;
  }

  public PutObjectResult putObject(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pKey,
      final InputStream pInput,
      final ObjectMetadata pObjectMetadata,
      final Logger pLogger) {
    final String prettyErr = "Problem with put object";
    final PutObjectRequest request =
        new PutObjectRequest(pBucketName, pKey, pInput, pObjectMetadata);
    return getAWSApiCallBuilder(
            request,
            getS3Client(pAWSAccount.getId(), pRegionName)::putObject,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public ListObjectsV2Result listObjects(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pPrefix,
      final String pContinuationToken,
      final Logger pLogger) {

    return listObjects(
        pAWSAccount, pRegionName, pBucketName, pPrefix, pContinuationToken, null, pLogger);
  }

  public ListObjectsV2Result listObjects(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pPrefix,
      final String pContinuationToken,
      final String pEncodingType,
      final Logger pLogger) {
    return listObjects(
        pAWSAccount,
        pRegionName,
        pBucketName,
        pPrefix,
        pContinuationToken,
        pEncodingType,
        List.of(),
        pLogger);
  }

  public ListObjectsV2Result listObjects(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pPrefix,
      final String pContinuationToken,
      final String pEncodingType,
      final List<String> pObjectAttributes,
      final Logger pLogger) {

    final ListObjectsV2Request request =
        new ListObjectsV2Request().withBucketName(pBucketName).withPrefix(pPrefix);

    if (pEncodingType != null) {
      request.withEncodingType(pEncodingType);
    }

    if (pContinuationToken != null) {
      request.withContinuationToken(pContinuationToken);
    }

    if (!pObjectAttributes.isEmpty()) {
      request.withOptionalObjectAttributes(pObjectAttributes);
    }

    return listObjects(pAWSAccount.getId(), pRegionName, request, pLogger);
  }

  public ListObjectsV2Result listObjects(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final ListObjectsV2Request request,
      final Logger pLogger) {
    final String prettyErr = "Problem listing objects";
    return getAWSApiCallBuilder(
            request,
            getS3Client(pAWSAccountId, pRegionName)::listObjectsV2,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public ListObjectsV2Result listObjectsForLogSSHSession(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pPrefix,
      final String pDelimiter,
      final Logger pLogger) {

    final ListObjectsV2Request request =
        new ListObjectsV2Request()
            .withBucketName(pBucketName)
            .withPrefix(pPrefix)
            .withDelimiter(pDelimiter);

    return listObjects(pAWSAccount.getId(), pRegionName, request, pLogger);
  }

  public RestoreObjectResult restoreObject(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucket,
      final String pKey,
      final int pExpirationInDays,
      final Logger pLogger) {
    final String prettyErr = String.format("Problem restoring object to %s", pBucket);

    final RestoreObjectRequest restoreReq =
        new RestoreObjectRequest(pBucket, pKey, pExpirationInDays);

    return getAWSApiCallBuilder(
            restoreReq,
            getS3Client(pAWSAccount.getId(), pRegionName)::restoreObjectV2,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public void waitUntilObjectExists(
      final AWSAccount pAWSAccount,
      final AWSRegionName pRegionName,
      final String pBucketName,
      final String pKey,
      final Logger pLogger) {
    pLogger.info("Waiting for object {} to exist in bucket {}", pKey, pBucketName);
    getS3Client(pAWSAccount.getId(), pRegionName).waiters();

    final Waiter<GetObjectMetadataRequest> waiter =
        getS3Client(pAWSAccount.getId(), pRegionName).waiters().objectExists();
    waiter.run(new WaiterParameters<>(new GetObjectMetadataRequest(pBucketName, pKey)));
    pLogger.info("Finished waiting object {} to exist in bucket {}", pKey, pBucketName);
  }

  /**
   * Recursively posts request to {@link AmazonS3#listObjectsV2(ListObjectsV2Request)} until the
   * first object that matches the given filter is found.
   *
   * @return an optional value of a filtering function param if value is found or an empty {@link
   *     Optional} if there is no matching object and there is no pagination token in the response.
   */
  public <T> Optional<T> findFirstMatchingObject(
      final AWSAccount pAwsAccount,
      final AWSRegionName pRegionName,
      ListObjectsV2Request pRequest,
      final Function<ListObjectsV2Result, T> pMatchFilter,
      final int pMaxNumCalls,
      final Logger pLogger) {
    final String prettyErr = String.format("Problem listing objects for region %s", pRegionName);
    final ListObjectsV2Result response =
        getAWSApiCallBuilder(
                pRequest,
                getS3Client(pAwsAccount.getId(), pRegionName)::listObjectsV2,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    final T result = pMatchFilter.apply(response);
    if (result != null) {
      return Optional.of(result);
    }

    if (!response.isTruncated()) {
      return Optional.empty();
    }

    if (pMaxNumCalls == 0) {
      LOG.error(
          "Exhausted attempts to find searched object in bucket {}, with prefix {}. "
              + "The number of objects in the bucket might be too large.",
          pRequest.getBucketName(),
          pRequest.getPrefix());
      return Optional.empty();
    }

    pRequest.setContinuationToken(response.getNextContinuationToken());
    return findFirstMatchingObject(
        pAwsAccount, pRegionName, pRequest, pMatchFilter, pMaxNumCalls - 1, pLogger);
  }

  public List<S3ObjectSummary> listObjects(
      final AWSCredentials pAWSCredentials,
      final String pBucketName,
      final Integer pMaxKeys,
      final boolean pUseGovCloudRegion,
      final Logger pLogger) {
    return listObjects(pAWSCredentials, pBucketName, null, pMaxKeys, pUseGovCloudRegion, pLogger);
  }

  public List<S3ObjectSummary> listObjects(
      final AWSCredentials pAWSCredentials,
      final String pBucketName,
      final String pPrefix,
      final Integer pMaxKeys,
      final boolean pUseGovCloudRegion,
      final Logger pLogger) {
    final String prettyErr = "Problem listing objects";

    final ListObjectsV2Request request =
        new ListObjectsV2Request().withBucketName(pBucketName).withPrefix(pPrefix);
    if (pMaxKeys != null) {
      request.withMaxKeys(pMaxKeys);
    }

    final ListObjectsV2Result result =
        getAWSApiCallBuilder(
                request,
                getS3Client(pAWSCredentials, pUseGovCloudRegion)::listObjectsV2,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    return result.getObjectSummaries();
  }

  public Optional<AWSRegionName> getS3BucketRegion(
      final AWSCredentials pAWSCredentials,
      final String pS3BucketName,
      final boolean pUseGovCloudRegion,
      final Logger pLogger)
      throws IllegalArgumentException {
    final String prettyErr = "Problem retrieving bucket location: " + pS3BucketName;
    final GetBucketLocationRequest request = new GetBucketLocationRequest(pS3BucketName);

    final String regionId =
        getAWSApiCallBuilder(
                request,
                getS3Client(pAWSCredentials, pUseGovCloudRegion)::getBucketLocation,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    return AWSApiSvc.findByS3Region(regionId);
  }

  public CopyObjectResult copyBucketObject(
      final AWSAccount pAWSAccount,
      final String pFromBucket,
      final String pSourceKey,
      final String pToBucket,
      final String pDestinationKey,
      final boolean pUseGovCloudRegion,
      final Logger pLogger) {
    final String prettyErr =
        String.format("Problem copying object from %s to %s", pFromBucket, pToBucket);
    CopyObjectRequest copyReq =
        new CopyObjectRequest(pFromBucket, pSourceKey, pToBucket, pDestinationKey);

    final CopyObjectResult result =
        getAWSApiCallBuilder(
                copyReq,
                getS3Client(pAWSAccount.getId(), pUseGovCloudRegion)::copyObject,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT, CommonErrorCode.SERVER_ERROR))
            .throttleRetries(THROTTLING_RETRY_COUNT)
            .makeApiCall();
    return result;
  }

  public void uploadObjectToS3(
      final AWSCredentials pAWSCredentials,
      final String pS3BucketName,
      final String key,
      final InputStream inputStream,
      final ObjectMetadata metadata,
      final CannedAccessControlList acl,
      final boolean pUseGovCloudRegion,
      final Logger pLogger)
      throws IllegalArgumentException {
    uploadObjectToS3(
        AWSCredentialsUtil.getAWSCredentialsProvider(pAWSCredentials, null),
        pS3BucketName,
        key,
        inputStream,
        metadata,
        acl,
        pUseGovCloudRegion,
        pLogger);
  }

  public void uploadObjectToS3(
      final AWSCredentialsProvider pProvider,
      final String pS3BucketName,
      final String key,
      final InputStream inputStream,
      final ObjectMetadata metadata,
      final CannedAccessControlList acl,
      final boolean pUseGovCloudRegion,
      final Logger pLogger)
      throws IllegalArgumentException {
    final String prettyErr = "Problem retrieving bucket location: " + pS3BucketName;
    final PutObjectRequest request =
        new PutObjectRequest(pS3BucketName, key, inputStream, metadata).withCannedAcl(acl);
    getAWSApiCallBuilder(
            request,
            getS3Client(pProvider, pUseGovCloudRegion)::putObject,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT, CommonErrorCode.SERVER_ERROR))
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .makeApiCall();
  }

  private String generateRoleSessionName() {
    return "TempSession-" + ObjectId.get();
  }

  /**
   * Assume a role specified by ARN using the web identity token file for that environment
   *
   * @param awsRoleArn AWS resource identifier for the role we want to assume
   * @param pRegion AWS Region to use when initializing the STS client
   * @return AWSCredentialsProvider for the assumed role
   */
  public AWSCredentialsProvider getCredentialsProviderFromRoleArn(
      String awsRoleArn, String pRegion) {
    String roleSessionName = generateRoleSessionName();
    // This provides default creds from WebIdentityTokenCredentialsProvider
    AWSCredentialsProvider currCredentialsProvider =
        new AWSCredentialsProviderChain(
            WebIdentityTokenCredentialsProvider.builder()
                .roleArn(awsRoleArn)
                .roleSessionName(roleSessionName)
                .webIdentityTokenFile(System.getenv(AWS_WEB_IDENTITY_ENV_VAR))
                .build(),
            new EC2ContainerCredentialsProviderWrapper());
    final AWSSecurityTokenService stsClient =
        AWSSecurityTokenServiceClientBuilder.standard()
            .withCredentials(currCredentialsProvider)
            .withRegion(pRegion)
            .build();
    // Create the AssumeRoleWithWebIdentity request
    AssumeRoleRequest assumeRoleRequest =
        new AssumeRoleRequest().withRoleArn(awsRoleArn).withRoleSessionName(roleSessionName);
    // This provides creds for the given role arn.
    AssumeRoleResult assumeRoleResult = stsClient.assumeRole(assumeRoleRequest);

    String accessKeyId = assumeRoleResult.getCredentials().getAccessKeyId();
    String secretAccessKey = assumeRoleResult.getCredentials().getSecretAccessKey();
    String sessionToken = assumeRoleResult.getCredentials().getSessionToken();
    AWSCredentials sessionCredentials =
        new BasicSessionCredentials(accessKeyId, secretAccessKey, sessionToken);

    // Return an AWSCredentialsProvider
    return new AWSStaticCredentialsProvider(sessionCredentials);
  }

  public static Optional<AWSRegionName> findByS3Region(final String pRegionId)
      throws IllegalArgumentException {
    return AWSRegionName.findByValue(Region.fromValue(pRegionId).toAWSRegion().getName());
  }

  public ObjectMetadata getObjectMetadata(
      final AWSCredentials pAWSCredentials,
      final String pBucketName,
      final String pKey,
      final boolean pUseGovCloudRegion,
      final Logger pLogger) {
    return getObjectMetadata(
        getS3Client(pAWSCredentials, pUseGovCloudRegion), pBucketName, pKey, pLogger);
  }

  public ObjectMetadata getObjectMetadata(
      final AWSCredentialsProvider pProvider,
      final String pBucketName,
      final String pKey,
      final boolean pUseGovCloudRegion,
      final Logger pLogger) {
    return getObjectMetadata(
        getS3Client(pProvider, pUseGovCloudRegion), pBucketName, pKey, pLogger);
  }

  public ObjectMetadata getObjectMetadata(
      final AmazonS3 pS3Client, final String pBucketName, final String pKey, final Logger pLogger) {
    final String prettyErr = "Problem retrieving object metadata";

    return getAWSApiCallBuilder(
            new GetObjectMetadataRequest(pBucketName, pKey),
            pS3Client::getObjectMetadata,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall();
  }

  public ObjectMetadata getObjectMetadataThrottled(
      final AWSCredentials pAWSCredentials,
      final String pBucketName,
      final String pKey,
      final boolean pUseGovCloudRegion,
      final Set<ErrorCode> pExpectedErrorCodes,
      final Logger pLogger) {
    return getObjectMetadataThrottled(
        getAWSCredentialsProvider(pAWSCredentials, null),
        pBucketName,
        pKey,
        pUseGovCloudRegion,
        pExpectedErrorCodes,
        pLogger);
  }

  public ObjectMetadata getObjectMetadataThrottled(
      final AWSCredentialsProvider pProvider,
      final String pBucketName,
      final String pKey,
      final boolean pUseGovCloudRegion,
      final Set<ErrorCode> pExpectedErrorCodes,
      final Logger pLogger) {
    final String prettyErr = "Problem retrieving object metadata";

    return getAWSApiCallBuilder(
            new GetObjectMetadataRequest(pBucketName, pKey),
            getS3Client(pProvider, pUseGovCloudRegion)::getObjectMetadata,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .throttleRetries(THROTTLING_RETRY_COUNT)
        .expectedErrorCodes(pExpectedErrorCodes)
        .makeApiCall();
  }

  public List<NDSCloudProviderEventView> getInstanceAndVolumeStatuses(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pEC2InstanceId,
      final Logger pLogger) {
    final Instance instance = findEC2Instance(pAWSAccountId, pRegionName, pLogger, pEC2InstanceId);
    final List<String> volumeIds =
        instance.getBlockDeviceMappings().stream()
            .map(bdm -> bdm.getEbs().getVolumeId())
            .collect(Collectors.toList());

    final InstanceStatus instanceStatus =
        getEC2InstanceStatus(pAWSAccountId, pRegionName, pLogger, pEC2InstanceId);
    final List<VolumeStatusItem> volumeStatuses =
        getEBSVolumeStatuses(pAWSAccountId, pRegionName, pLogger, volumeIds);

    return Stream.concat(
            Stream.of(instanceStatus).map(NDSAWSDescribeInstanceStatusView::new),
            volumeStatuses.stream().map(NDSAWSDescribeVolumeStatusView::new))
        .collect(Collectors.toList());
  }

  public Map<String, Map<NDSCloudProviderConsoleMetricDefinition, List<NDST2CSVRow>>>
      getMetricsAsCSVForInstance(
          final ObjectId pAWSAccountId,
          final AWSRegionName pRegionName,
          final String pEC2InstanceId,
          final Date pStartTime,
          final Date pEndTime,
          final Set<NDSCloudProviderConsoleMetricDefinition>
              pNDSCloudProviderConsoleMetricDefinitions,
          final Logger pLogger) {
    final String prettyErr = "Problem retrieving CloudWatch metrics";

    final AmazonCloudWatch cloudWatchClient = getCloudWatchClient(pAWSAccountId, pRegionName);

    final Instance instance = findEC2Instance(pAWSAccountId, pRegionName, pLogger, pEC2InstanceId);
    final List<String> volumeIds =
        instance.getBlockDeviceMappings().stream()
            .map(bdm -> bdm.getEbs().getVolumeId())
            .collect(Collectors.toList());

    final Set<NDSAWSMetricDefinition> ndsAWSMetricDefinitions =
        pNDSCloudProviderConsoleMetricDefinitions.stream()
            .map(
                md ->
                    NDSAWSMetricDefinition.findFirstByMetricAndStatistic(
                        md.getMetric(), md.getStatistic()))
            .flatMap(Optional::stream)
            .collect(Collectors.toSet());

    final int metricPeriod = getMetricPeriodInSeconds(pStartTime);

    final List<MetricDataQuery> metricDataQueries =
        Stream.concat(
                ndsAWSMetricDefinitions.stream()
                    .filter(
                        metricDefinition ->
                            metricDefinition
                                .getNamespace()
                                .equals(NDSAWSMetricDefinition.Namespace.AWS_EC2))
                    .map(
                        metricDefinition ->
                            new MetricDataQuery()
                                .withId(getMetricDataQueryId(metricDefinition, pEC2InstanceId))
                                .withMetricStat(
                                    new MetricStat()
                                        .withMetric(
                                            new Metric()
                                                .withNamespace(metricDefinition.getNamespace())
                                                .withMetricName(metricDefinition.getMetric())
                                                .withDimensions(
                                                    new Dimension()
                                                        .withName(METRIC_DIMENSION_NAME_INSTANCE_ID)
                                                        .withValue(pEC2InstanceId)))
                                        .withPeriod(metricPeriod)
                                        .withStat(metricDefinition.getStatistic()))
                                .withLabel(
                                    MetricDefinitionDetails.getLabel(
                                        METRIC_DIMENSION_NAME_INSTANCE_ID))),
                ndsAWSMetricDefinitions.stream()
                    .filter(
                        metricDefinition ->
                            metricDefinition
                                .getNamespace()
                                .equals(NDSAWSMetricDefinition.Namespace.AWS_EBS))
                    .flatMap(
                        metricDefinition ->
                            volumeIds.stream()
                                .map(
                                    volumeId ->
                                        new MetricDataQuery()
                                            .withId(
                                                getMetricDataQueryId(metricDefinition, volumeId))
                                            .withMetricStat(
                                                new MetricStat()
                                                    .withMetric(
                                                        new Metric()
                                                            .withNamespace(
                                                                metricDefinition.getNamespace())
                                                            .withMetricName(
                                                                metricDefinition.getMetric())
                                                            .withDimensions(
                                                                new Dimension()
                                                                    .withName(
                                                                        METRIC_DIMENSION_NAME_VOLUME_ID)
                                                                    .withValue(volumeId)))
                                                    .withPeriod(metricPeriod)
                                                    .withStat(metricDefinition.getStatistic()))
                                            .withLabel(
                                                MetricDefinitionDetails.getLabel(
                                                    METRIC_DIMENSION_NAME_VOLUME_ID)))))
            .collect(Collectors.toList());

    final GetMetricDataRequest getMetricDataRequest =
        new GetMetricDataRequest()
            .withStartTime(pStartTime)
            .withEndTime(pEndTime)
            .withMetricDataQueries(metricDataQueries);
    final GetMetricDataResult getMetricDataResult =
        getAWSApiCallBuilder(
                getMetricDataRequest,
                cloudWatchClient::getMetricData,
                prettyErr,
                NDSErrorCode.INTERNAL,
                pLogger)
            .makeApiCall();

    final Map<String, Map<NDSCloudProviderConsoleMetricDefinition, List<NDST2CSVRow>>> metrics =
        new HashMap<>();

    pLogger.debug(
        "Got ({}) metric data results for ({})",
        getMetricDataResult.getMetricDataResults().size(),
        pEC2InstanceId);
    getMetricDataResult
        .getMetricDataResults()
        .forEach(
            metricDataResult -> {
              pLogger.debug(
                  "Metric data result ID ({}), label ({}), timestamps size ({}), values size ({})",
                  metricDataResult.getId(),
                  metricDataResult.getLabel(),
                  metricDataResult.getTimestamps().size(),
                  metricDataResult.getValues().size());
              final MetricDefinitionDetails metricDefinitionDetails =
                  new MetricDefinitionDetails(metricDataResult);
              final Optional<NDSAWSMetricDefinition> ndsMetricDefinition =
                  NDSAWSMetricDefinition.findFirstByMetricAndStatistic(
                      metricDefinitionDetails.metricDefinitionName(),
                      metricDefinitionDetails.statistic());
              if (ndsMetricDefinition.isEmpty()) {
                pLogger.debug(
                    "Could not find metric definition for "
                        + "resource ID ({}), metric definition name ({}, {})",
                    metricDefinitionDetails.resourceId(),
                    metricDefinitionDetails.metricDefinitionName(),
                    metricDefinitionDetails.statistic());
                return;
              }

              final Map<NDSCloudProviderConsoleMetricDefinition, List<NDST2CSVRow>>
                  metricsForResource =
                      metrics.getOrDefault(
                          metricDefinitionDetails.metricDefinitionName(), new HashMap<>());

              final List<NDST2CSVRow> csvValues =
                  Stream.concat(
                          Stream.of(NDST2CSVRow.headerRow(ndsMetricDefinition.get())),
                          IntStream.range(0, metricDataResult.getTimestamps().size())
                              .mapToObj(
                                  i ->
                                      new NDST2CSVRow(
                                          String.valueOf(
                                              metricDataResult.getTimestamps().get(i).getTime()
                                                  / 1000L),
                                          String.valueOf(metricDataResult.getValues().get(i))))
                              .sorted(Comparator.comparing(NDST2CSVRow::getTime)))
                      .toList();
              metricsForResource.put(ndsMetricDefinition.get(), csvValues);
              metrics.put(metricDefinitionDetails.metricDefinitionName(), metricsForResource);
            });

    return metrics;
  }

  public List<MetricDataResult> getAWSLoadBalancerMetric(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final String pLoadBalancerName,
      final Map<String, String> pMetricNamesAndStatsMap,
      final Date pStartTime,
      final Date pEndTime,
      final Logger pLogger) {
    final String prettyErr = "Problem retrieving metrics";
    final AmazonCloudWatch cloudWatchClient = getCloudWatchClient(pAWSAccountId, pRegionName);

    final Set<NDSAWSMetricDefinition> ndsAWSMetricDefinitions =
        pMetricNamesAndStatsMap.keySet().stream()
            .map(NDSAWSMetricDefinition::findFirstByMetric)
            .flatMap(Optional::stream)
            .collect(Collectors.toSet());

    final int metricPeriod = getMetricPeriodInSeconds(pStartTime);
    final List<MetricDataQuery> metricDataQueries = new ArrayList<>();

    ndsAWSMetricDefinitions.stream()
        .map(
            metricDefinition ->
                new MetricDataQuery()
                    .withId(getMetricDataQueryId(metricDefinition, pLoadBalancerName))
                    .withMetricStat(
                        new MetricStat()
                            .withMetric(
                                new Metric()
                                    .withNamespace(metricDefinition.getNamespace())
                                    .withMetricName(metricDefinition.getMetric())
                                    .withDimensions(
                                        new Dimension()
                                            .withName(metricDefinition.getFacet().getFacetName())
                                            .withValue(pLoadBalancerName)))
                            .withPeriod(metricPeriod)
                            .withStat(pMetricNamesAndStatsMap.get(metricDefinition.getMetric()))))
        .forEach(metricDataQueries::add);

    final GetMetricDataRequest getMetricDataRequest =
        new GetMetricDataRequest()
            .withStartTime(pStartTime)
            .withEndTime(pEndTime)
            .withMetricDataQueries(metricDataQueries);
    return getAWSApiCallBuilder(
            getMetricDataRequest,
            cloudWatchClient::getMetricData,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall()
        .getMetricDataResults();
  }

  private String getMetricDataQueryId(
      final NDSAWSMetricDefinition pNDSAWSMetricDefinition, final String pResourceId) {
    return String.format(
            "%s_%s_%s_%s",
            pNDSAWSMetricDefinition.getNamespace(),
            pNDSAWSMetricDefinition.getMetric(),
            pNDSAWSMetricDefinition.getStatistic(),
            pResourceId)
        .replace("/", "_")
        .replace("-", "_")
        .replace("%", "percent")
        .toLowerCase();
  }

  // We request metric data with the following label formats:
  //   "${PROP('Namespace')} ${PROP('Dim.InstanceId')} ${PROP('MetricName')}"
  //   "${PROP('Namespace')} ${PROP('Dim.VolumeId')} ${PROP('MetricName')}"
  // Which result in the following example label:
  //   AWS/EC2 i-031c67716418c0bf3 CPUUtilization
  public record MetricDefinitionDetails(
      String resourceId, String metricDefinitionName, String statistic) {

    private static final String DELIMITER = " ";

    public MetricDefinitionDetails(MetricDataResult pMetricDataResult) {
      this(pMetricDataResult.getLabel().split(DELIMITER));
    }

    public MetricDefinitionDetails(final String[] labelSections) {
      this(labelSections[1], labelSections[2], labelSections[3]);
    }

    // https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/graph-dynamic-labels.html
    public static String getLabel(final String pMetricsDimension) {
      return String.format(
          String.join(
              DELIMITER,
              new String[] {
                "${PROP('Namespace')}",
                "${PROP('Dim.%s')}",
                "${PROP('MetricName')}",
                "${PROP('Stat')}"
              }),
          pMetricsDimension);
    }
  }

  public List<InstanceTypeOffering> getAWSAvailabilityZonesForType(
      ObjectId pAWSAccountId,
      AWSRegionName pRegionName,
      String pInstanceType,
      final Logger pLogger) {
    final String prettyErr = "Problem retrieving instance type offerings";

    DescribeInstanceTypeOfferingsRequest describeInstanceTypeOfferingsRequest =
        new DescribeInstanceTypeOfferingsRequest()
            .withLocationType(LocationType.AvailabilityZoneId)
            .withFilters(new Filter("instance-type", List.of(pInstanceType)));
    return getAWSApiCallBuilder(
            describeInstanceTypeOfferingsRequest,
            getEC2Client(pAWSAccountId, pRegionName)::describeInstanceTypeOfferings,
            prettyErr,
            NDSErrorCode.INTERNAL,
            pLogger)
        .makeApiCall()
        .getInstanceTypeOfferings();
  }

  public List<NetworkInterface> getAWSNetworkInterfacesAboveSecurityGroupLimit(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final int pSecurityGroupLimit,
      final Logger pLogger) {
    final String prettyErr = "Problem retrieving AWS network interfaces";
    final List<NetworkInterface> networkInterfacesAboveSecurityGroupLimit = new ArrayList<>();
    DescribeNetworkInterfacesRequest request = new DescribeNetworkInterfacesRequest();
    String nextToken = null;
    do {
      if (nextToken != null) {
        request.withNextToken(nextToken);
      }
      final DescribeNetworkInterfacesResult apiResult =
          getAWSApiCallBuilder(
                  request,
                  getEC2Client(pAWSAccountId, pRegionName)::describeNetworkInterfaces,
                  prettyErr,
                  NDSErrorCode.INTERNAL,
                  pLogger)
              .makeApiCall();
      apiResult.getNetworkInterfaces().stream()
          .filter(ni -> ni.getGroups().size() > pSecurityGroupLimit)
          .forEach(networkInterfacesAboveSecurityGroupLimit::add);
      nextToken = apiResult.getNextToken();
    } while (nextToken != null);
    return networkInterfacesAboveSecurityGroupLimit;
  }

  @VisibleForTesting
  protected <T extends AmazonWebServiceRequest, R> AWSApiCallBuilder<T, R> getAWSApiCallBuilder(
      final T pRequest,
      final Function<T, R> pApiCall,
      final String pPrettyErr,
      final ErrorCode pDefaultFailureCode,
      final Logger pLogger) {
    return new AWSApiCallBuilder<>(pRequest, pApiCall, pPrettyErr, pDefaultFailureCode, pLogger);
  }

  @VisibleForTesting
  protected class AWSApiCallBuilder<T extends AmazonWebServiceRequest, R> {

    private final T _request;
    private final Function<T, R> _apiCall;
    private final String _prettyErr;
    private final ErrorCode _defaultFailureCode;
    private final Logger _logger;
    private Set<ErrorCode> _expectedErrorCodes;
    private int _throttleRetries;
    private Optional<ObjectId> _awsAccountId;
    private Optional<AWSRegionName> _awsRegionName;
    private ErrorSource _errorSource;

    protected AWSApiCallBuilder(
        final T pRequest,
        final Function<T, R> pApiCall,
        final String pPrettyErr,
        final ErrorCode pDefaultFailureCode,
        final Logger pLogger) {
      // required fields
      _request = pRequest;
      _apiCall = pApiCall;
      _prettyErr = pPrettyErr;
      _defaultFailureCode = pDefaultFailureCode;
      _logger = pLogger;

      // optional fields
      _expectedErrorCodes = Collections.emptySet();
      _throttleRetries = 0;
      _awsAccountId = Optional.empty();
      _awsRegionName = Optional.empty();
      _errorSource = ErrorSource.CONTROL_PLANE_INTERNAL;
    }

    private AWSApiCallBuilder<T, R> expectedErrorCodes(final Set<ErrorCode> pExpectedErrorCodes) {
      _expectedErrorCodes = pExpectedErrorCodes;
      return this;
    }

    private AWSApiCallBuilder<T, R> throttleRetries(final int pThrottleRetries) {
      _throttleRetries = pThrottleRetries;
      return this;
    }

    @VisibleForTesting
    protected AWSApiCallBuilder<T, R> errorSource(final ErrorSource pErrorSource) {
      _errorSource = pErrorSource;
      return this;
    }

    private AWSApiCallBuilder<T, R> recordAndPublishQuotaCapacityError(
        final ObjectId pAWSAccountId, final AWSRegionName pAWSRegionName) {
      _awsAccountId = Optional.of(pAWSAccountId);
      _awsRegionName = Optional.of(pAWSRegionName);
      return this;
    }

    @VisibleForTesting
    protected R makeApiCall() {
      try {
        return AWSApiUtils.makeApiCall(
            _request,
            _apiCall,
            _prettyErr,
            _defaultFailureCode,
            _expectedErrorCodes,
            _throttleRetries,
            _logger,
            _appSettings,
            _errorSource);
      } catch (AWSApiException ex) {
        if (_awsAccountId.isPresent() && _awsRegionName.isPresent()) {
          AWSApiSvc.this.recordAndPublishQuotaCapacityError(
              _awsAccountId.get(), _awsRegionName.get(), ex, _request.getClass().getSimpleName());
        }
        throw ex;
      }
    }
  }

  public void recordCapacityReservationError(
      final ObjectId pAWSAccountId,
      final AWSRegionName pAWSRegionName,
      final AWSApiException pException) {
    recordAndPublishQuotaCapacityError(
        pAWSAccountId, pAWSRegionName, pException, "CreateCapacityReservationRequest");
  }

  protected void recordAndPublishQuotaCapacityError(
      final ObjectId pAWSAccountId,
      final AWSRegionName pAWSRegionName,
      final AWSApiException pException,
      final String pRequestName) {
    // LIMIT_EXCEEDED means we are hitting quota limit
    // https://docs.aws.amazon.com/AWSEC2/latest/APIReference/errors-overview.html#CommonErrors
    if (!CAPACITY_QUOTA_ERROR_CODES.contains(pException.getErrorCode())) {
      return;
    }

    final ErrorType errorType =
        pException.getErrorCode() == AWSErrorCode.LIMIT_EXCEEDED
            ? ErrorType.QUOTA
            : ErrorType.CAPACITY;
    final AWSAccount awsAccount = getAWSAccount(pAWSAccountId);

    NDSPromMetricsSvc.incrementCounter(
        AWS_CAPACITY_QUOTA_ERROR_TOTAL,
        errorType.name(),
        pAWSRegionName.getValue(),
        awsAccount.getName(),
        pRequestName);

    if (errorType.equals(ErrorType.QUOTA)) {
      _ndsAlertSvc.createQuotaAlert(
          QuotaUsageEvent.Type.QUOTA_USAGE_EXCEEDED_EVENT,
          CloudProvider.AWS,
          awsAccount.getName(),
          pRequestName,
          pAWSRegionName.getValue());
    }

    _cloudProviderMonitoringErrorSvc.create(
        CloudProvider.AWS,
        pAWSAccountId,
        awsAccount.getName(),
        pAWSRegionName,
        pException.getMessage(),
        errorType,
        pRequestName);
  }

  protected AWSClientsFactory getClientsFactory() {
    return _clientsFactory;
  }

  public static Policy getS3PresignedDownloadURLPolicy(
      final String pS3Bucket, final String pS3File, final List<String> pIpAddresses) {
    final Policy policy = new Policy();
    final List<Statement> statements =
        Collections.singletonList(
            PolicyUtils.generateS3GetObjectStatement(
                String.format(GET_OBJECT_RESOURCE_FORMAT, pS3Bucket, pS3File), pIpAddresses));
    policy.setStatements(statements);
    return policy;
  }

  // Federated usernames must have a length of 2-32.
  public static String generateFederatedUsername(String pUserType) {
    return String.format(
        FEDERATED_USERNAME_FORMAT, pUserType, EncryptionUtils.randomAlphanumeric(8));
  }

  public static int getMetricPeriodInSeconds(Date pStartTime) {
    long timeDifference =
        Math.abs(new Date().getTime() - pStartTime.getTime())
            / 1000; // time between now and desired start time in seconds.

    if (timeDifference >= SIXTY_THREE_DAYS_IN_SECONDS) { // Greater than or equal to 63 days
      return (int) ONE_HOUR_IN_SECONDS;
    } else if (timeDifference >= FIFTEEN_DAYS_IN_SECONDS) { // Between 15 and 63 days
      return (int) FIVE_MINUTES_IN_SECONDS;
    } else {
      return (int) ONE_MINUTE_IN_SECONDS; // Less than 15 days
    }
  }
}
