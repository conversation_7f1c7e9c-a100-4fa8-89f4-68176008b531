package com.xgen.cloud.nds.aws._public.svc;

import static com.xgen.cloud.nds.aws._public.svc.PrometheusCollectors.AWS_CAPACITY_QUOTA_ERROR_TOTAL;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.awsv2._public.clients.AwsClientsFactoryV2;
import com.xgen.cloud.common.awsv2._public.clients.AwsCredentialsUtilV2;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.ErrorSource;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSErrorCode;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.util.AwsApiUtilsV2;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.cloudprovidermonitoring.CloudProviderMonitoringError.ErrorType;
import com.xgen.cloud.nds.project._public.model.cloudprovidermonitoring.QuotaUsageEvent;
import com.xgen.cloud.nds.project._public.svc.NDSAlertSvc;
import com.xgen.cloud.nds.project._public.svc.cloudprovidermonitoring.CloudProviderMonitoringErrorSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.util.Collections;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.awscore.AwsRequest;
import software.amazon.awssdk.awscore.AwsResponse;
import software.amazon.awssdk.services.ec2.Ec2Client;
import software.amazon.awssdk.services.health.HealthClient;
import software.amazon.awssdk.services.kafka.KafkaClient;
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest;
import software.amazon.awssdk.services.sts.model.Credentials;

/**
 * A wrapper around the AWS v2 SDK that provides error handling, observability, and various
 * abstractions that reduce boilerplate for callers.
 *
 * @see AWSApiSvc for an alternative that wraps the AWS v1 SDK.
 */
@Singleton
public class AWSApiSvcV2Framework {

  private final AWSApiSvc v1AwsApiSvc;
  private final AwsClientsFactoryV2 clientsFactory;
  private final AWSAccountDao awsAccountDao;
  private final NDSAlertSvc ndsAlertSvc;
  private final CloudProviderMonitoringErrorSvc cloudProviderMonitoringErrorSvc;

  private static final Set<ErrorCode> CAPACITY_QUOTA_ERROR_CODES =
      Set.of(
          AWSErrorCode.LIMIT_EXCEEDED,
          NDSErrorCode.NO_CAPACITY,
          AWSErrorCode.RESERVATION_CAPACITY_EXCEEDED);

  @Inject
  public AWSApiSvcV2Framework(
      final AWSApiSvc awsApiSvc,
      final AWSAccountDao awsAccountDao,
      final CloudProviderMonitoringErrorSvc cloudProviderMonitoringErrorSvc,
      final AppSettings appSettings,
      final NDSAlertSvc ndsAlertSvc) {
    this.v1AwsApiSvc = awsApiSvc;
    this.awsAccountDao = awsAccountDao;
    this.clientsFactory = new AwsClientsFactoryV2(appSettings.getNDSGovUSEnabled());
    this.ndsAlertSvc = ndsAlertSvc;
    this.cloudProviderMonitoringErrorSvc = cloudProviderMonitoringErrorSvc;
  }

  protected AWSAccount getAWSAccount(final ObjectId awsAccountId) {
    return awsAccountDao
        .find(awsAccountId)
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    String.format("Failed to find AWS Account by id (%s)", awsAccountId)));
  }

  /**
   * Returns credentials from an <a
   * href="https://docs.aws.amazon.com/STS/latest/APIReference/API_AssumeRole.html">AssumeRole</a>
   * request to the STS service.
   */
  public Credentials assumeRole(
      final AwsCredentialsProvider provider,
      final String roleARN,
      final String externalID,
      final String roleSessionName,
      final Duration duration,
      final Logger logger)
      throws AWSApiException {
    return assumeRole(
        provider,
        roleARN,
        externalID,
        roleSessionName,
        duration,
        logger,
        ErrorSource.CONTROL_PLANE_INTERNAL);
  }

  /**
   * Returns credentials from an <a
   * href="https://docs.aws.amazon.com/STS/latest/APIReference/API_AssumeRole.html">AssumeRole</a>
   * request to the STS service.
   */
  public Credentials assumeRole(
      final AwsCredentialsProvider provider,
      final String roleARN,
      final String externalID,
      final String roleSessionName,
      final Duration duration,
      final Logger logger,
      final ErrorSource errorSource)
      throws AWSApiException {
    final int durationInSeconds = (int) duration.toSeconds();
    if (durationInSeconds < 900) {
      throw new IllegalArgumentException("Duration in seconds must be greater than 900");
    }

    final var stsClient =
        clientsFactory.getStsClient(
            provider, NDSSettings.isGovCloudArn(roleARN), NDSSettings.isCNArn(roleARN));

    final var assumeRoleReq =
        AssumeRoleRequest.builder()
            .roleArn(roleARN)
            .externalId(externalID)
            .durationSeconds(durationInSeconds)
            .roleSessionName(roleSessionName)
            .build();

    final var assumeRoleRes =
        new AWSApiCallBuilder<>(
                assumeRoleReq,
                stsClient::assumeRole,
                "Problem assuming the role",
                NDSErrorCode.INTERNAL,
                logger)
            .errorSource(errorSource)
            .makeApiCall();

    return assumeRoleRes.credentials();
  }

  /** Gets V1 credentials provider using the legacy service and wraps it in the V2 interface */
  @VisibleForTesting
  protected synchronized AwsCredentialsProvider getCredentials(final ObjectId awsAccountId) {
    return AwsCredentialsUtilV2.toV2CredentialsProvider(v1AwsApiSvc.getCredentials(awsAccountId));
  }

  /**
   * Gets an EC2 client for the given account and region.
   *
   * @param awsAccountId The AWS account ID.
   * @param regionName The AWS region name.
   * @return An EC2 client.
   */
  @VisibleForTesting
  Ec2Client getEc2Client(final ObjectId awsAccountId, final AWSRegionName regionName) {
    final AwsCredentialsProvider provider = getCredentials(awsAccountId);
    return clientsFactory.getEc2Client(provider, regionName.getValue());
  }

  @VisibleForTesting
  KafkaClient getKafkaClient(final ObjectId awsAccountId, final AWSRegionName regionName) {
    final AwsCredentialsProvider provider = getCredentials(awsAccountId);
    return clientsFactory.getKafkaClient(provider, regionName.getValue());
  }

  @VisibleForTesting
  HealthClient getHealthClient(final ObjectId awsAccountId, final AWSRegionName regionName) {
    final AwsCredentialsProvider provider = getCredentials(awsAccountId);
    return clientsFactory.getHealthClient(provider);
  }

  /**
   * If the given exception indicates a quota has been reached, increment a prometheus counter and
   * trigger an alert.
   */
  @VisibleForTesting
  protected void recordAndPublishQuotaCapacityError(
      final ObjectId awsAccountId,
      final AWSRegionName awsRegionName,
      final AWSApiException exception,
      final String requestName) {
    if (!CAPACITY_QUOTA_ERROR_CODES.contains(exception.getErrorCode())) {
      return;
    }

    // LIMIT_EXCEEDED means we are hitting quota limit
    // https://docs.aws.amazon.com/AWSEC2/latest/APIReference/errors-overview.html#CommonErrors
    final ErrorType errorType =
        exception.getErrorCode() == AWSErrorCode.LIMIT_EXCEEDED
            ? ErrorType.QUOTA
            : ErrorType.CAPACITY;
    final AWSAccount awsAccount = getAWSAccount(awsAccountId);

    NDSPromMetricsSvc.incrementCounter(
        AWS_CAPACITY_QUOTA_ERROR_TOTAL,
        errorType.name(),
        awsRegionName.getValue(),
        awsAccount.getName(),
        requestName);

    if (errorType.equals(ErrorType.QUOTA)) {
      ndsAlertSvc.createQuotaAlert(
          QuotaUsageEvent.Type.QUOTA_USAGE_EXCEEDED_EVENT,
          CloudProvider.AWS,
          awsAccount.getName(),
          requestName,
          awsRegionName.getValue());
    }

    cloudProviderMonitoringErrorSvc.create(
        CloudProvider.AWS,
        awsAccountId,
        awsAccount.getName(),
        awsRegionName,
        exception.getMessage(),
        errorType,
        requestName);
  }

  /**
   * A builder for {@link AwsApiUtilsV2#makeApiCall}. This is typically the entry point for all
   * consumers of this AWS v2 SDK wrapper.
   *
   * @param request The request to make, typically some AWS request object
   * @param apiCall The function to call, typically some AWS client method
   * @param prettyErr A pretty error message
   * @param defaultFailureCode The default error code
   * @param logger The logger
   * @return A builder
   * @param <REQ> The request type, typically some AWS defined request type
   * @param <RES> The response type, typically some AWS defined response type
   */
  <REQ extends AwsRequest, RES extends AwsResponse>
      AWSApiCallBuilder<REQ, RES> getAWSApiCallBuilder(
          final REQ request,
          final Function<REQ, RES> apiCall,
          final String prettyErr,
          final ErrorCode defaultFailureCode,
          final Logger logger) {
    return new AWSApiCallBuilder<>(request, apiCall, prettyErr, defaultFailureCode, logger);
  }

  /** A builder for {@link AwsApiUtilsV2#makeApiCall} */
  public class AWSApiCallBuilder<REQ extends AwsRequest, RES extends AwsResponse> {
    private final REQ request;
    private final Function<REQ, RES> apiCall;
    private final String prettyErr;
    private final ErrorCode defaultFailureCode;
    private final Logger logger;
    private Set<ErrorCode> expectedErrorCodes;
    private int throttleRetries;
    private Optional<ObjectId> awsAccountId;
    private Optional<AWSRegionName> awsRegionName;
    private ErrorSource errorSource;

    private AWSApiCallBuilder(
        REQ request,
        Function<REQ, RES> apiCall,
        String prettyErr,
        ErrorCode defaultFailureCode,
        Logger logger) {
      // required fields
      this.request = request;
      this.apiCall = apiCall;
      this.prettyErr = prettyErr;
      this.defaultFailureCode = defaultFailureCode;
      this.logger = logger;

      // optional fields
      this.expectedErrorCodes = Collections.emptySet();
      this.throttleRetries = 0;
      this.awsAccountId = Optional.empty();
      this.awsRegionName = Optional.empty();
      this.errorSource = ErrorSource.CONTROL_PLANE_INTERNAL;
    }

    public AWSApiCallBuilder<REQ, RES> expectedErrorCodes(final Set<ErrorCode> expectedErrorCodes) {
      this.expectedErrorCodes = expectedErrorCodes;
      return this;
    }

    public AWSApiCallBuilder<REQ, RES> throttleRetries(final int throttleRetries) {
      this.throttleRetries = throttleRetries;
      return this;
    }

    public AWSApiCallBuilder<REQ, RES> errorSource(final ErrorSource errorSource) {
      this.errorSource = errorSource;
      return this;
    }

    public AWSApiCallBuilder<REQ, RES> recordAndPublishQuotaCapacityError(
        final ObjectId awsAccountId, final AWSRegionName awsRegionName) {
      this.awsAccountId = Optional.of(awsAccountId);
      this.awsRegionName = Optional.of(awsRegionName);
      return this;
    }

    @VisibleForTesting
    RES makeApiCall() {
      try {
        return AwsApiUtilsV2.makeApiCall(
            request,
            apiCall,
            prettyErr,
            defaultFailureCode,
            expectedErrorCodes,
            throttleRetries,
            logger,
            errorSource);
      } catch (AWSApiException ex) {
        if (awsAccountId.isPresent() && awsRegionName.isPresent()) {
          AWSApiSvcV2Framework.this.recordAndPublishQuotaCapacityError(
              awsAccountId.get(), awsRegionName.get(), ex, request.getClass().getSimpleName());
        }

        throw ex;
      }
    }
  }
}
