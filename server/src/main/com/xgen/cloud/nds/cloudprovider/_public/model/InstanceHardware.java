package com.xgen.cloud.nds.cloudprovider._public.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.annotations.VisibleForTesting;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.monitoring.topology._public.model.ReplicaSet.Member.State;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareHealth.HealthItem;
import com.xgen.cloud.nds.cloudprovider._public.model.chef.CloudChefConf;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.common._public.model.BiConnectorReadPreference;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.common._public.model.NDSProcessType;
import com.xgen.cloud.nds.common._public.model.NDSProcessType.ProcessRestartTaskToRun;
import com.xgen.cloud.nds.common._public.model.NDSReplicaSetMemberState;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.common._public.model.PingResult;
import jakarta.annotation.Nullable;
import java.time.Duration;
import java.util.AbstractMap.SimpleEntry;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.BsonReader;
import org.bson.BsonWriter;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.types.ObjectId;
import org.slf4j.Logger;

public abstract class InstanceHardware {

  public static final Duration MIN_REPAIR_WAIT_TIME = Duration.ofHours(3);
  public static final Duration MINIMUM_RESUME_RUNTIME = Duration.ofMinutes(60);
  public static final int CLOUD_PROVIDER_INSTANCE_ID_HASH_LENGTH = 24;

  private ObjectId _cloudContainerId;
  private final ObjectId _instanceId;
  private Integer _diskSizeGB;
  private final String _publicIP;
  private boolean _provisioned;
  private Action _action;
  private final Date _lastUpdated;
  private final Date _lastDiskModifyDate;
  private final boolean _forceReplacement;
  private final CloudProvider _cloudProvider;
  private final boolean _forceServerRestart;
  private final boolean _forceServerResync;

  private final boolean _forceStopStartVM;
  private final List<ForceStopStartVMSource> _forceStopStartVMSources;
  private final Date _restartRequestedDate;
  private boolean _isPaused;
  private final BiConnector _biConnector;
  private final Date _rebootRequestedDate;
  private final RebootRequestedBy _rebootRequestedBy;
  private final String _rebootRequestedChefCommitHash;
  private final Date _externalRebootRequestedDate;
  private final Map<NDSProcessType, Date> _processRestartRequestedDates;
  private final Map<NDSProcessType, ProcessRestartTaskToRun> _processRestartTasksToRun;
  private final boolean _needsCriticalReboot;

  private final Date _configLastUpdatedDate;
  private final Date _configNeedsUpdateAfter;
  private final Date _agentApiKeyNeedsUpdateAfter;
  private final Date _rotateSslAfter;
  private final Date _rotateSslCritical;
  private final boolean _needsReloadSslOnProcesses;
  private final Date _reloadSslOnProcessesRequestedDate;
  private final Date _lastResumeDate;
  private final int _memberIndex;
  private final Date _createDate;
  private final Date _lastInstanceSizeModifyDate;
  private Hostnames _hostnames;
  private final boolean _needsOSSwap;
  private final HostnameScheme _hostnameSchemeForAgents;
  private final boolean _needsHostnameChange;
  private final int _acmeSalt;
  private final Date _actionLastModifiedDate;
  private final boolean _powerCycleAttempted;
  private boolean _forcePlanningPrimary;
  private final Date _needsUngracefulDisconnectDate;
  private String _appliedOSPolicyVersion;
  // used to schedule logical re-syncs as a part of config server type transitions.  This field
  // is general and may be used for other use-cases.  It will schedule a logical initial
  // sync of this node outside maintenance windows.
  @Nullable private Date _needsDiskCompactionResyncDate;
  @Nullable private final String _acmeProvider;

  public InstanceHardware(final BasicDBObject pDBObject) {
    _cloudContainerId = pDBObject.getObjectId(FieldDefs.CLOUD_PROVIDER_CONTAINER_ID);
    _instanceId = pDBObject.getObjectId(FieldDefs.INSTANCE_ID);
    if (pDBObject.get(FieldDefs.DISK_SIZE_GB) != null) {
      _diskSizeGB = pDBObject.getInt(FieldDefs.DISK_SIZE_GB);
    } else {
      _diskSizeGB = null;
    }
    final BasicDBList hostnames = (BasicDBList) pDBObject.get(FieldDefs.HOSTNAMES);
    _hostnames = new Hostnames(hostnames != null ? hostnames : new BasicDBList());
    _publicIP = pDBObject.getString(FieldDefs.PUBLIC_IP);
    _provisioned = pDBObject.getBoolean(FieldDefs.PROVISIONED);
    _action = Action.valueOf(pDBObject.getString(FieldDefs.ACTION));
    _lastUpdated = pDBObject.getDate(FieldDefs.LAST_UPDATE_DATE);
    _lastDiskModifyDate = pDBObject.getDate(FieldDefs.LAST_DISK_MODIFY_DATE);
    _forceReplacement = pDBObject.getBoolean(FieldDefs.FORCE_REPLACEMENT);
    _cloudProvider = CloudProvider.valueOf(pDBObject.getString(FieldDefs.CLOUD_PROVIDER));
    _forceServerRestart = pDBObject.getBoolean(FieldDefs.FORCE_SERVER_RESTART);
    _forceServerResync = pDBObject.getBoolean(FieldDefs.FORCE_SERVER_RESYNC);
    _forceStopStartVM = pDBObject.getBoolean(FieldDefs.FORCE_STOP_START_VM);
    _forceStopStartVMSources =
        Optional.ofNullable(pDBObject.get(FieldDefs.FORCE_STOP_START_VM_SOURCES))
            .map(BasicDBList.class::cast)
            .stream()
            .flatMap(Collection::stream)
            .map(String.class::cast)
            .map(ForceStopStartVMSource::valueOf)
            .toList();
    _isPaused = pDBObject.getBoolean(FieldDefs.IS_PAUSED, false);
    _biConnector = new BiConnector((BasicDBObject) pDBObject.get(FieldDefs.BI_CONNECTOR));
    _restartRequestedDate = pDBObject.getDate(FieldDefs.RESTART_REQUESTED_DATE);
    _rebootRequestedDate = pDBObject.getDate(FieldDefs.REBOOT_REQUESTED_DATE);
    _appliedOSPolicyVersion = pDBObject.getString(FieldDefs.APPLIED_OS_POLICY_VERSION);
    _needsDiskCompactionResyncDate = pDBObject.getDate(FieldDefs.NEEDS_DISK_COMPACTION_RESYNC_DATE);

    final String rebootRequestedByTypeStr = pDBObject.getString(FieldDefs.REBOOT_REQUESTED_BY);
    _rebootRequestedBy =
        rebootRequestedByTypeStr != null
            ? RebootRequestedBy.valueOf(rebootRequestedByTypeStr)
            : null;

    _rebootRequestedChefCommitHash =
        pDBObject.getString(FieldDefs.REBOOT_REQUESTED_CHEF_COMMIT_HASH);
    _externalRebootRequestedDate = pDBObject.getDate(FieldDefs.EXTERNAL_REBOOT_REQUESTED_DATE);

    _processRestartRequestedDates = new HashMap<>();
    _processRestartTasksToRun = new HashMap<>();

    final Map<?, ?> processRestartRequestedDatesMap =
        Optional.ofNullable(pDBObject.get(FieldDefs.PROCESS_RESTART_REQUESTED_DATES))
            .map(BasicDBObject.class::cast)
            .map(BasicDBObject::toMap)
            .orElse(new HashMap<>());
    final Map<?, ?> processRestartTasksToRunMap =
        Optional.ofNullable(pDBObject.get(FieldDefs.PROCESS_RESTART_TASKS_TO_RUN))
            .map(BasicDBObject.class::cast)
            .map(BasicDBObject::toMap)
            .orElse(new HashMap<>());

    processRestartRequestedDatesMap.keySet().stream()
        .map(String.class::cast)
        .forEach(
            k ->
                _processRestartRequestedDates.put(
                    NDSProcessType.findByType(k), (Date) processRestartRequestedDatesMap.get(k)));
    processRestartTasksToRunMap.keySet().stream()
        .map(String.class::cast)
        .forEach(
            k ->
                _processRestartTasksToRun.put(
                    NDSProcessType.findByType(k),
                    Optional.ofNullable(processRestartTasksToRunMap.get(k))
                        .map(String.class::cast)
                        .map(ProcessRestartTaskToRun::valueOf)
                        .orElse(null)));

    _needsCriticalReboot = pDBObject.getBoolean(FieldDefs.NEEDS_CRITICAL_REBOOT);
    _configLastUpdatedDate = pDBObject.getDate(FieldDefs.CONFIG_LAST_UPDATED_DATE);
    _configNeedsUpdateAfter = pDBObject.getDate(FieldDefs.CONFIG_NEEDS_UPDATE_AFTER);
    _agentApiKeyNeedsUpdateAfter = pDBObject.getDate(FieldDefs.AGENT_API_KEY_NEEDS_UPDATE_AFTER);
    _rotateSslAfter = pDBObject.getDate(FieldDefs.ROTATE_SSL_AFTER);
    _rotateSslCritical = pDBObject.getDate(FieldDefs.ROTATE_SSL_CRITICAL);
    _needsReloadSslOnProcesses =
        pDBObject.getBoolean(FieldDefs.NEEDS_RELOAD_SSL_ON_PROCESSES, false);
    _reloadSslOnProcessesRequestedDate =
        pDBObject.getDate(FieldDefs.RELOAD_SSL_ON_PROCESSES_REQUESTED_DATE);
    _lastResumeDate = pDBObject.getDate(FieldDefs.LAST_RESUME_DATE, null);
    _memberIndex = pDBObject.getInt(FieldDefs.MEMBER_INDEX);
    _createDate = pDBObject.getDate(FieldDefs.CREATE_DATE);
    _lastInstanceSizeModifyDate = pDBObject.getDate(FieldDefs.LAST_INSTANCE_SIZE_MODIFY_DATE);
    _needsOSSwap = pDBObject.getBoolean(FieldDefs.NEEDS_OS_SWAP, false);
    _hostnameSchemeForAgents =
        Optional.ofNullable(pDBObject.getString(FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS))
            .map(HostnameScheme::valueOf)
            .orElse(null);
    _needsHostnameChange = pDBObject.getBoolean(FieldDefs.NEEDS_HOSTNAME_CHANGE, false);
    _acmeSalt = pDBObject.getInt(FieldDefs.ACME_SALT, 0);
    _actionLastModifiedDate = pDBObject.getDate(FieldDefs.ACTION_LAST_MODIFIED_DATE);
    _powerCycleAttempted = pDBObject.getBoolean(FieldDefs.POWER_CYCLE_ATTEMPTED, false);
    _forcePlanningPrimary = pDBObject.getBoolean(FieldDefs.FORCE_PLANNING_PRIMARY, false);
    _needsUngracefulDisconnectDate =
        pDBObject.getDate(FieldDefs.NEEDS_UNGRACEFUL_DISCONNECT_DATE, null);
    _acmeProvider = pDBObject.getString(FieldDefs.ACME_PROVIDER, null);
  }

  public void setHostNames(Hostnames pHostnames) {
    _hostnames = pHostnames;
  }

  public void setDiskSizeGB(int pDiskSizeGB) {
    _diskSizeGB = pDiskSizeGB;
  }

  public void setCloudContainerId(ObjectId pCloudContainerId) {
    _cloudContainerId = pCloudContainerId;
  }

  public void setIsPaused(boolean pPaused) {
    _isPaused = pPaused;
  }

  public void setForcePlanningPrimary(boolean pForcePlanningPrimary) {
    _forcePlanningPrimary = pForcePlanningPrimary;
  }

  public void setAppliedOSPolicyVersion(String pAppliedOSPolicyVersion) {
    _appliedOSPolicyVersion = pAppliedOSPolicyVersion;
  }

  public void setNeedsDiskCompactionResyncDate(final Date pNeedsDiskCompactionResyncDate) {
    _needsDiskCompactionResyncDate = pNeedsDiskCompactionResyncDate;
  }

  public abstract Builder copy();

  public abstract static class Builder {
    protected final BasicDBObject _dbObject;

    public abstract Builder setPhysicalZoneId(final PhysicalZoneId pPhysicalZoneId);

    public Builder setAppliedOSPolicyVersion(final String pAppliedOSPolicyVersion) {
      _dbObject.put(FieldDefs.APPLIED_OS_POLICY_VERSION, pAppliedOSPolicyVersion);
      return this;
    }

    public Builder setInstanceId(final ObjectId pInstanceId) {
      _dbObject.put(FieldDefs.INSTANCE_ID, pInstanceId);
      return this;
    }

    public Builder setCloudContainerId(final ObjectId pCloudContainerId) {
      _dbObject.put(FieldDefs.CLOUD_PROVIDER_CONTAINER_ID, pCloudContainerId);
      return this;
    }

    public Builder setHealth(final InstanceHardwareHealth pInstanceHardwareHealth) {
      _dbObject.put(FieldDefs.HEALTH, pInstanceHardwareHealth.toDBObject());
      return this;
    }

    public Builder setInstanceSize(final InstanceSize pInstanceSize) {
      _dbObject.put(FieldDefs.INSTANCE_SIZE, pInstanceSize);
      return this;
    }

    public Builder setRebootRequestedDate(final Date pRebootRequestedDate) {
      _dbObject.put(FieldDefs.REBOOT_REQUESTED_DATE, pRebootRequestedDate);
      return this;
    }

    public Builder setRestartRequestedDate(final Date pRebootRequestedDate) {
      _dbObject.put(FieldDefs.RESTART_REQUESTED_DATE, pRebootRequestedDate);
      return this;
    }

    public Builder setNeedsCriticalReboot(final boolean pNeedsCriticalReboot) {
      _dbObject.put(FieldDefs.NEEDS_CRITICAL_REBOOT, pNeedsCriticalReboot);
      return this;
    }

    public Builder setNeedsForceReplacement(final boolean pNeedsForceReplacement) {
      _dbObject.put(FieldDefs.FORCE_REPLACEMENT, pNeedsForceReplacement);
      return this;
    }

    public Builder setIsPaused(final boolean pIsPaused) {
      _dbObject.put(FieldDefs.IS_PAUSED, pIsPaused);
      return this;
    }

    public Builder setIsProvisioned(final boolean pIsProvisioned) {
      _dbObject.put(FieldDefs.PROVISIONED, pIsProvisioned);
      return this;
    }

    public Builder setLastResumeDate(final Date pLastResumeDate) {
      _dbObject.put(FieldDefs.LAST_RESUME_DATE, pLastResumeDate);
      return this;
    }

    public Builder setLastInstanceSizeModifyDate(final Date pInstanceSizeModifyDate) {
      _dbObject.put(FieldDefs.LAST_INSTANCE_SIZE_MODIFY_DATE, pInstanceSizeModifyDate);
      return this;
    }

    public Builder setLastDiskModifyDate(final Date pDiskModifyDate) {
      _dbObject.put(FieldDefs.LAST_DISK_MODIFY_DATE, pDiskModifyDate);
      return this;
    }

    public Builder(final BasicDBObject pDBObject) {
      _dbObject = pDBObject;
    }

    public InstanceHardware build() {
      return InstanceHardware.getHardware(_dbObject);
    }
  }

  public static BasicDBObject getEmptyHardware(
      final CloudProvider pProvider,
      final ObjectId pInstanceId,
      final Date pCreateDate,
      final Integer pMemberIndex) {
    return new BasicDBObject()
        .append(FieldDefs.CLOUD_PROVIDER, pProvider.name())
        .append(FieldDefs.INSTANCE_ID, pInstanceId)
        .append(FieldDefs.CREATE_DATE, pCreateDate)
        .append(FieldDefs.LAST_UPDATE_DATE, pCreateDate)
        .append(FieldDefs.LAST_DISK_MODIFY_DATE, null)
        .append(FieldDefs.PROVISIONED, false)
        .append(FieldDefs.ACTION, Action.NONE.name())
        .append(FieldDefs.HEALTH, new BasicDBObject())
        .append(FieldDefs.IS_PAUSED, false)
        .append(FieldDefs.BI_CONNECTOR, BiConnector.getDefaultBiConnector().toDBObject())
        .append(FieldDefs.REBOOT_REQUESTED_DATE, null)
        .append(FieldDefs.PROCESS_RESTART_REQUESTED_DATES, new BasicDBObject())
        .append(FieldDefs.EXTERNAL_REBOOT_REQUESTED_DATE, null)
        .append(FieldDefs.RESTART_REQUESTED_DATE, null)
        .append(FieldDefs.NEEDS_CRITICAL_REBOOT, false)
        .append(FieldDefs.ROTATE_SSL_AFTER, null)
        .append(FieldDefs.ROTATE_SSL_CRITICAL, null)
        .append(FieldDefs.NEEDS_RELOAD_SSL_ON_PROCESSES, false)
        .append(FieldDefs.LAST_RESUME_DATE, null)
        .append(FieldDefs.MEMBER_INDEX, pMemberIndex)
        .append(FieldDefs.LAST_INSTANCE_SIZE_MODIFY_DATE, null)
        .append(FieldDefs.NEEDS_OS_SWAP, false)
        .append(FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS, null)
        .append(FieldDefs.NEEDS_HOSTNAME_CHANGE, false)
        .append(FieldDefs.ACME_SALT, 0)
        .append(FieldDefs.ACTION_LAST_MODIFIED_DATE, null)
        .append(FieldDefs.POWER_CYCLE_ATTEMPTED, false)
        .append(FieldDefs.FORCE_PLANNING_PRIMARY, false)
        .append(FieldDefs.CONFIG_NEEDS_UPDATE_AFTER, null)
        .append(FieldDefs.CONFIG_LAST_UPDATED_DATE, null)
        .append(FieldDefs.APPLIED_OS_POLICY_VERSION, null);
  }

  public static InstanceHardware getHardware(final BasicDBObject pDBObject) {
    final String providerOnHardware = pDBObject.getString(FieldDefs.CLOUD_PROVIDER);
    final CloudProvider cloudProvider = CloudProvider.findByName(providerOnHardware);

    try {
      return CloudProviderRegistry.getByCloudProvider(cloudProvider)
          .getInstanceHardwareProvider()
          .getHardware(pDBObject);
    } catch (final IllegalArgumentException pE) {
      throw new IllegalArgumentException("Unsupported Cloud Provider: " + providerOnHardware, pE);
    }
  }

  public static boolean shardDescriptionIsValid(
      final Set<CloudProvider> pClusterCloudProviders,
      final InstanceHardware pInstanceHardware,
      final CloudProvider pHardwareProvider) {
    if (!pClusterCloudProviders.contains(pHardwareProvider)) {
      throw new IllegalArgumentException(
          String.format(
              "ClusterDescription must contain %s nodes. Cluster providers are : %s",
              pHardwareProvider,
              pClusterCloudProviders.stream()
                  .map(CloudProvider::name)
                  .collect(Collectors.joining(", "))));
    }

    return pInstanceHardware.isProvisioned();
  }

  public static boolean configServerDescriptionIsValid(
      final Set<CloudProvider> pCloudProviders,
      final InstanceHardware pInstanceHardware,
      final CloudProvider pHardwareProvider,
      final boolean pIsClusterSharded) {
    if (!pCloudProviders.contains(pHardwareProvider)) {
      throw new IllegalArgumentException(
          String.format(
              "ClusterDescription must have %s nodes. Cluster providers are : %s",
              pHardwareProvider,
              pCloudProviders.stream().map(CloudProvider::name).collect(Collectors.joining(", "))));
    }
    if (!pIsClusterSharded) {
      throw new IllegalArgumentException("ClusterDescription must be of ClusterType SHARDED.");
    }

    return pInstanceHardware.isProvisioned();
  }

  public BasicDBObject toDBObject() {
    final BasicDBObject processRestartRequestedDatesDbObj = new BasicDBObject();
    final BasicDBObject processRestartTasksToRunDbObj = new BasicDBObject();
    _processRestartRequestedDates.forEach(
        (k, v) -> processRestartRequestedDatesDbObj.append(k.getType(), v));
    _processRestartTasksToRun.forEach(
        (k, v) ->
            processRestartTasksToRunDbObj.append(
                k.getType(),
                Optional.ofNullable(v).map(ProcessRestartTaskToRun::name).orElse(null)));

    final BasicDBList forceStopStartVMSourcesDBList = new BasicDBList();
    forceStopStartVMSourcesDBList.addAll(
        _forceStopStartVMSources.stream().map(Enum::name).toList());

    final BasicDBObject dbObject =
        new BasicDBObject()
            .append(FieldDefs.CLOUD_PROVIDER_CONTAINER_ID, _cloudContainerId)
            .append(FieldDefs.CLOUD_PROVIDER, _cloudProvider.name())
            .append(FieldDefs.INSTANCE_ID, _instanceId)
            .append(FieldDefs.CREATE_DATE, _createDate)
            .append(FieldDefs.LAST_UPDATE_DATE, _lastUpdated)
            .append(FieldDefs.LAST_DISK_MODIFY_DATE, _lastDiskModifyDate)
            .append(FieldDefs.PROVISIONED, _provisioned)
            .append(FieldDefs.ACTION, _action.name())
            .append(FieldDefs.HEALTH, getHealth().toDBObject())
            .append(FieldDefs.IS_PAUSED, _isPaused)
            .append(FieldDefs.BI_CONNECTOR, getBiConnector().toDBObject())
            .append(FieldDefs.REBOOT_REQUESTED_DATE, _rebootRequestedDate)
            .append(FieldDefs.REBOOT_REQUESTED_CHEF_COMMIT_HASH, _rebootRequestedChefCommitHash)
            .append(FieldDefs.REBOOT_REQUESTED_BY, _rebootRequestedBy)
            .append(FieldDefs.EXTERNAL_REBOOT_REQUESTED_DATE, _externalRebootRequestedDate)
            .append(FieldDefs.PROCESS_RESTART_REQUESTED_DATES, processRestartRequestedDatesDbObj)
            .append(FieldDefs.PROCESS_RESTART_TASKS_TO_RUN, processRestartTasksToRunDbObj)
            .append(FieldDefs.RESTART_REQUESTED_DATE, _restartRequestedDate)
            .append(FieldDefs.NEEDS_CRITICAL_REBOOT, _needsCriticalReboot)
            .append(FieldDefs.ROTATE_SSL_AFTER, _rotateSslAfter)
            .append(FieldDefs.ROTATE_SSL_CRITICAL, _rotateSslCritical)
            .append(FieldDefs.NEEDS_RELOAD_SSL_ON_PROCESSES, _needsReloadSslOnProcesses)
            .append(
                FieldDefs.RELOAD_SSL_ON_PROCESSES_REQUESTED_DATE,
                _reloadSslOnProcessesRequestedDate)
            .append(FieldDefs.LAST_RESUME_DATE, _lastResumeDate)
            .append(FieldDefs.MEMBER_INDEX, _memberIndex)
            .append(FieldDefs.DISK_SIZE_GB, _diskSizeGB)
            .append(FieldDefs.HOSTNAMES, _hostnames.toDBList())
            .append(FieldDefs.PUBLIC_IP, _publicIP)
            .append(FieldDefs.FORCE_REPLACEMENT, _forceReplacement)
            .append(FieldDefs.FORCE_SERVER_RESTART, _forceServerRestart)
            .append(FieldDefs.FORCE_SERVER_RESYNC, _forceServerResync)
            .append(FieldDefs.FORCE_STOP_START_VM, _forceStopStartVM)
            .append(FieldDefs.FORCE_STOP_START_VM_SOURCES, forceStopStartVMSourcesDBList)
            .append(FieldDefs.CONFIG_LAST_UPDATED_DATE, _configLastUpdatedDate)
            .append(FieldDefs.CONFIG_NEEDS_UPDATE_AFTER, _configNeedsUpdateAfter)
            .append(FieldDefs.AGENT_API_KEY_NEEDS_UPDATE_AFTER, _agentApiKeyNeedsUpdateAfter)
            .append(FieldDefs.LAST_INSTANCE_SIZE_MODIFY_DATE, _lastInstanceSizeModifyDate)
            .append(FieldDefs.NEEDS_OS_SWAP, _needsOSSwap)
            .append(
                FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
                _hostnameSchemeForAgents == null ? null : _hostnameSchemeForAgents.name())
            .append(FieldDefs.NEEDS_HOSTNAME_CHANGE, _needsHostnameChange)
            .append(FieldDefs.ACME_SALT, _acmeSalt)
            .append(FieldDefs.ACTION_LAST_MODIFIED_DATE, _actionLastModifiedDate)
            .append(FieldDefs.POWER_CYCLE_ATTEMPTED, _powerCycleAttempted)
            .append(FieldDefs.FORCE_PLANNING_PRIMARY, _forcePlanningPrimary)
            .append(FieldDefs.NEEDS_UNGRACEFUL_DISCONNECT_DATE, _needsUngracefulDisconnectDate)
            .append(FieldDefs.APPLIED_OS_POLICY_VERSION, _appliedOSPolicyVersion)
            .append(FieldDefs.NEEDS_DISK_COMPACTION_RESYNC_DATE, _needsDiskCompactionResyncDate);

    getACMEProvider()
        .ifPresent(acmeProvider -> dbObject.append(FieldDefs.ACME_PROVIDER, acmeProvider));

    return dbObject;
  }

  public Optional<Date> getCreatedDate() {
    return Optional.ofNullable(_createDate);
  }

  public Optional<String> getAppliedOSPolicyVersion() {
    return Optional.ofNullable(_appliedOSPolicyVersion);
  }

  public Optional<Date> getNeedsDiskCompactionResyncDate() {
    return Optional.ofNullable(_needsDiskCompactionResyncDate);
  }

  public ObjectId getCloudContainerId() {
    return _cloudContainerId;
  }

  public ObjectId getInstanceId() {
    return _instanceId;
  }

  public abstract Optional<String> getInstanceSize();

  public abstract Optional<? extends NDSInstanceSize> getNDSInstanceSize();

  public abstract Optional<String> getInstanceFamily();

  public abstract Optional<? extends InstanceFamily> getInstanceFamilyValue();

  public abstract Optional<OS> getOS();

  public abstract Optional<String> getZoneTag();

  public abstract Optional<PhysicalZoneId> getPhysicalZoneId();

  public Optional<Integer> getDiskSizeGB() {
    return Optional.ofNullable(_diskSizeGB);
  }

  public Hostnames getHostnames() {
    return _hostnames;
  }

  public Optional<String> getPublicIP() {
    return Optional.ofNullable(_publicIP);
  }

  public Optional<String> getFuturePublicIp() {
    return Optional.empty();
  }

  public boolean isProvisioned() {
    return _provisioned;
  }

  @VisibleForTesting
  public void setProvisioned(boolean pProvisioned) {
    _provisioned = pProvisioned;
  }

  public Action getAction() {
    return _action;
  }

  @VisibleForTesting
  public void setAction(Action pAction) {
    _action = pAction;
  }

  public Date getLastUpdated() {
    return _lastUpdated;
  }

  public Optional<Date> getLastDiskModifyDate() {
    return Optional.ofNullable(_lastDiskModifyDate);
  }

  public boolean needsForceReplacement() {
    return _forceReplacement;
  }

  public CloudProvider getCloudProvider() {
    return _cloudProvider;
  }

  public boolean needsForceServerRestart() {
    return _forceServerRestart;
  }

  public boolean needsForceServerResync() {
    return _forceServerResync;
  }

  public boolean needsForceStopStartVM() {
    return _forceStopStartVM;
  }

  public List<ForceStopStartVMSource> getForceStopStartVMSources() {
    return _forceStopStartVMSources;
  }

  public Optional<Date> getRestartRequestedDate() {
    return Optional.ofNullable(_restartRequestedDate);
  }

  public boolean isPaused() {
    return _isPaused;
  }

  public boolean isReadyForPrivateEndpointRule() {
    return !isPaused() && isProvisioned();
  }

  public Optional<Date> getNextRestartOrRebootRequestedDate() {
    return getRebootRequestedDate()
        .map(
            rebootDate ->
                getRestartRequestedDate()
                    .filter(restartDate -> restartDate.before(rebootDate))
                    .orElse(rebootDate))
        .or(this::getRestartRequestedDate);
  }

  public boolean isRestartBeforeRebootRequestedDate() {
    final Optional<Date> restartDate = getRestartRequestedDate();
    final Optional<Date> rebootDate = getRebootRequestedDate();

    if (restartDate.isEmpty()) {
      return false;
    }

    if (rebootDate.isEmpty()) {
      return true;
    }

    return restartDate.get().before(rebootDate.get());
  }

  public Optional<Date> getRebootRequestedDate() {
    return Optional.ofNullable(_rebootRequestedDate);
  }

  public Optional<RebootRequestedBy> getRebootRequestedBy() {
    return Optional.ofNullable(_rebootRequestedBy);
  }

  public Optional<String> getRebootRequestedChefCommitHash() {
    return Optional.ofNullable(_rebootRequestedChefCommitHash);
  }

  public Optional<Date> getExternalRebootRequestedDate() {
    return Optional.ofNullable(_externalRebootRequestedDate);
  }

  public Map<NDSProcessType, Date> getProcessRestartRequestedDates() {
    return _processRestartRequestedDates;
  }

  public Map<NDSProcessType, ProcessRestartTaskToRun> getProcessRestartTasksToRun() {
    return _processRestartTasksToRun;
  }

  public Optional<Date> getConfigNeedsUpdateAfter() {
    return Optional.ofNullable(_configNeedsUpdateAfter);
  }

  public Optional<Date> getAgentApiKeyNeedsUpdateAfter() {
    return Optional.ofNullable(_agentApiKeyNeedsUpdateAfter);
  }

  public Optional<Date> getConfigLastUpdatedDate() {
    return Optional.ofNullable(_configLastUpdatedDate);
  }

  public boolean needsCriticalReboot() {
    return _needsCriticalReboot;
  }

  public abstract boolean doesShardHardwareMatch(
      final HardwareSpec pHardwareSpec,
      final double pClusterDiskSizeGB,
      final Set<CloudProvider> pClusterCloudProviders);

  public abstract boolean doesConfigServerHardwareMatch(
      final HardwareSpec pConfigHardwareSpec,
      final double pConfigDiskSizeGB,
      final Set<CloudProvider> pClusterCloudProviders,
      final boolean pIsClusterSharded);

  public abstract boolean doesShardHardwareSizeMatch(
      final HardwareSpec pHardwareSpec, final Set<CloudProvider> pClusterCloudProviders);

  public abstract boolean doesConfigServerHardwareSizeMatch(
      final HardwareSpec pConfigHardwareSpec,
      final Set<CloudProvider> pClusterCloudProviders,
      final boolean pIsClusterSharded);

  public abstract boolean doesShardDiskMatch(
      final HardwareSpec pHardwareSpec,
      final double pClusterDiskSizeGB,
      final Set<CloudProvider> pClusterCloudProviders);

  public abstract boolean doesConfigServerDiskMatch(
      final HardwareSpec pConfigHardwareSpec,
      final double pConfigDiskSizeGB,
      final Set<CloudProvider> pClusterCloudProviders,
      final boolean pIsClusterSharded);

  public abstract boolean canModifyVolume(
      final double pGoalDiskSizeGB,
      final HardwareSpec pHardwareSpec,
      final boolean pIsDesiredHardwareSpecNVMe);

  public abstract boolean canModifyInstanceSize(
      final InstanceFamily pClusterInstanceFamily, final boolean pIsDesiredHardwareSpecNVMe);

  public boolean canRunSwapMachineMove(
      final InstanceFamily pDesiredInstanceFamily, final boolean pIsDesiredHardwareSpecNVMe) {
    return !isNVMe() && !pIsDesiredHardwareSpecNVMe;
  }

  public abstract Optional<String> getDataDeviceName();

  public abstract Optional<String> getBackupDeviceName();

  public abstract Optional<List<String>> getServerlessRestoreDeviceNames();

  public Optional<String> getWiredTigerEngineConfigString() {
    return Optional.empty();
  }

  public Optional<Double> getWiredTigerEngineCacheSizeGB() {
    return Optional.empty();
  }

  public Optional<Integer> getMaxIncomingConnections() {
    return Optional.empty();
  }

  /**
   * https://github.com/mongodb/mongo/blob/72419ff3ee61489447ec56518ca23b8df826ea4e/src/mongo/db/index_builds/multi_index_block.idl#L48-L64
   */
  public Optional<RollableValue<Integer>> getMaxIndexBuildMemoryUsageMegabytes() {
    return Optional.empty();
  }

  /**
   * https://github.com/mongodb/mongo/blob/72419ff3ee61489447ec56518ca23b8df826ea4e/src/mongo/db/index_builds/two_phase_index_build_knobs.idl#L43-L57
   */
  public Optional<RollableValue<Integer>> getMaxNumActiveUserIndexBuilds() {
    return Optional.empty();
  }

  public abstract InstanceHardwareHealth getHealth();

  public abstract boolean isNVMe();

  public abstract Optional<Integer> getDiskSizeGBForOplog();

  public abstract Optional<Date> getScheduledShutDownDate();

  public abstract Optional<String> getCloudProviderInstanceIdHash();

  public abstract Optional<String> getCloudProviderInstanceId();

  public BiConnector getBiConnector() {
    return _biConnector;
  }

  public Optional<Date> getRotateSslAfter() {
    return Optional.ofNullable(_rotateSslAfter);
  }

  public Optional<Date> getRotateSslCritical() {
    return Optional.ofNullable(_rotateSslCritical);
  }

  public boolean needsReloadSslOnProcesses() {
    return _needsReloadSslOnProcesses;
  }

  public Optional<Date> getReloadSslOnProcessesRequestedDate() {
    return Optional.ofNullable(_reloadSslOnProcessesRequestedDate);
  }

  public Optional<Date> getLastResumeDate() {
    return Optional.ofNullable(_lastResumeDate);
  }

  public int getMemberIndex() {
    return _memberIndex;
  }

  public boolean needsHostnameChange() {
    return _needsHostnameChange;
  }

  public abstract CloudChefConf buildCloudChefConfFromBase(final CloudChefConf.Builder<?> pBuilder);

  public Optional<Date> getLastInstanceSizeModifyDate() {
    return Optional.ofNullable(_lastInstanceSizeModifyDate);
  }

  public boolean needsOSSwap() {
    return _needsOSSwap;
  }

  public abstract boolean needsCpuArchitectureSwap(final CpuArchitecture pClusterCpuArchitecture);

  public abstract boolean needsDiskWarming();

  public Optional<String> getHostnameForAgents() {
    if (getHostnameSchemeForAgents() == null) {
      return Optional.empty();
    }
    return getHostnames().stream()
        .filter(o -> o.getScheme().equals(getHostnameSchemeForAgents()))
        .findFirst()
        .map(InstanceHostname::getHostname);
  }

  public HostnameScheme getHostnameSchemeForAgents() {
    return _hostnameSchemeForAgents;
  }

  public int getACMESalt() {
    return _acmeSalt;
  }

  public Optional<Date> getActionLastModifiedDate() {
    return Optional.ofNullable(_actionLastModifiedDate);
  }

  public boolean powerCycleAttempted() {
    return _powerCycleAttempted;
  }

  /**
   * Configuration parameters for instance hardware healing operations.
   *
   * @param removeIcmpPing whether to exclude ICMP ping checks from health evaluation
   * @param isNewAutoHealResyncAlgoEnabled whether the new auto-heal resync algorithm is enabled,
   *     which allows resync actions to bypass the minimum repair wait time and retry faster
   * @param isReducedThresholds whether to use reduced health thresholds for healing
   */
  public record HealingConfiguration(
      boolean removeIcmpPing, boolean isNewAutoHealResyncAlgoEnabled, boolean isReducedThresholds) {
    /** Creates a default healing configuration with standard settings. */
    public static HealingConfiguration defaultConfig() {
      return new HealingConfiguration(false, false, false);
    }
  }

  /**
   * Determines if this instance needs healing based on its health status and repair timing
   * constraints.
   *
   * <p>This method evaluates the instance's health data and applies business rules to determine the
   * appropriate healing action. It considers health data freshness, minimum repair wait times, and
   * the new auto-heal resync algorithm behavior.
   *
   * @param pLogger the logger for diagnostic output during health evaluation
   * @param pHealingConfiguration the configuration parameters for healing operations
   * @return a {@link ScheduledAction} indicating the healing action to take, the earliest date it
   *     can be performed, and the reason for the decision
   */
  public ScheduledAction needsHealing(
      final Logger pLogger, final HealingConfiguration pHealingConfiguration) {
    if (getHealth().getCollectedFor().isEmpty()
        || getLastUpdated().after(getHealth().getCollectedFor().get())) {
      return new ScheduledAction(
          Action.NONE,
          // The maximum wait time for actions in the planner is 7 days so make this 7 arbitrarily
          DateUtils.addDays(new Date(), 7),
          "No health data collected for this instance yet");
    }

    final ScheduledAction desiredAction = getHealAction(pLogger, pHealingConfiguration);

    if (!desiredAction.getAction().isActionable()) {
      return desiredAction;
    }

    // Skips the minimum repair wait time check for resync actions
    if (desiredAction.getAction() == Action.HEAL_RESYNC
        && pHealingConfiguration.isNewAutoHealResyncAlgoEnabled) {
      return desiredAction;
    }

    // Mask healing actions with wait if we are waiting for this server to be eligible (time wise)
    // for repair again
    if (!getLastUpdated()
        .before(new Date(System.currentTimeMillis() - MIN_REPAIR_WAIT_TIME.toMillis()))) {
      return new ScheduledAction(
          Action.HEAL_WAIT,
          // The maximum wait time for actions in the planner is 7 days so make this 7 arbitrarily
          DateUtils.addDays(new Date(), 7),
          "Waiting for the minimum repair wait time to pass before taking action");
    }

    return desiredAction;
  }

  /**
   * Determines the appropriate healing action for this instance based on its current health status.
   *
   * <p>The healing decision process follows this priority order:
   *
   * <ol>
   *   <li>If any major components report HEAL_WAIT, return HEAL_WAIT
   *   <li>If automation agent is stale but monitoring/ping are healthy, return HEAL_CANNOT_REPAIR
   *   <li>If all major components (automation, monitoring, ping) are stale, return HEAL_REPAIR
   *   <li>If any major components report HEAL_REPAIR, return HEAL_WAIT
   *   <li>If replica set member state is unhealthy, return HEAL_RESYNC
   *   <li>If replica set member is in RECOVERING state, return HEAL_WAIT
   *   <li>Otherwise, return NONE
   * </ol>
   *
   * @param pLogger the logger for diagnostic output during health evaluation
   * @param pHealConfiguration the healing configuration containing feature flag settings * such as
   *     whether to remove ICMP ping checks ({@code removeIcmpPing}), whether * the new auto-heal
   *     resync algorithm is enabled ({@code isNewAutoHealResyncAlgoEnabled}) and whether the new
   *     reduced thresholds are enabled ({@code isReducedThresholds}). This configuration is
   *     typically derived from feature flags for the group
   * @return a {@link ScheduledAction} indicating the healing action to take, the earliest date it
   *     can be performed, and the reason for the decision.
   * @see HealthThreshold for the specific time thresholds used in health evaluation
   * @see #needsHealing(Logger, HealingConfiguration) for the complete healing workflow including
   *     timing constraints
   */
  public ScheduledAction getHealAction(
      final Logger pLogger, final HealingConfiguration pHealConfiguration) {

    final boolean isIcmpPingRemoved = pHealConfiguration.removeIcmpPing();
    final boolean isReducedThresholds = pHealConfiguration.isReducedThresholds();

    final ScheduledAction automationAction =
        getActionWithDateForHealthTimeThreshold(
            getHealth().getLastAutomationAgentAudit(),
            !isNVMe() || isReducedThresholds
                ? HealthThreshold.AUTOMATION_AGENT_AUDIT_REDUCED
                : HealthThreshold.AUTOMATION_AGENT_AUDIT);

    final ScheduledAction monitoringAction =
        getActionWithDateForHealthTimeThreshold(
            getHealth().getLastMonitoringProcessPing(),
            !isNVMe() || isReducedThresholds
                ? HealthThreshold.MONITORING_PROCESS_PING_REDUCED
                : HealthThreshold.MONITORING_PROCESS_PING);

    final ScheduledAction pingAction =
        getActionWithDateForHealthItem(
            getHealth().getLastICMPPing(),
            !isNVMe() || isReducedThresholds
                ? HealthThreshold.ICMP_PING_REDUCED
                : HealthThreshold.ICMP_PING,
            false);

    final ScheduledAction replicaSetMemberAction =
        getReplicaSetMemberActionWithDateForHealthItem(
            getHealth().getReplicaSetMemberState(),
            !isNVMe() || isReducedThresholds
                ? HealthThreshold.REPLICA_SET_MEMBER_RECOVERY_REDUCED
                : HealthThreshold.REPLICA_SET_MEMBER_RECOVERY);

    // Intentionally exclude replicaSetMemberAction
    final Map<String, ScheduledAction> majorActions =
        getMajorActions(automationAction, monitoringAction, pingAction, isIcmpPingRemoved);

    final Set<String> componentsResponsibleForHealWait =
        majorActions.entrySet().stream()
            .filter((mapPair) -> mapPair.getValue().getAction() == Action.HEAL_WAIT)
            .map(Entry::getKey)
            .collect(Collectors.toUnmodifiableSet());

    final Supplier<Set<String>> allActionDecisions =
        () ->
            Stream.concat(
                    majorActions.entrySet().stream(),
                    Stream.of(new SimpleEntry<>("replicaSetMemberAction", replicaSetMemberAction)))
                .map(
                    (actionPair) -> {
                      final ScheduledAction action = actionPair.getValue();
                      return String.format(
                          "Name: %s, Action: %s, Date: %s, Reason: %s",
                          actionPair.getKey(),
                          action.getAction(),
                          action.getDate(),
                          action.getReason());
                    })
                .collect(Collectors.toUnmodifiableSet());

    pLogger.info(
        "Common HealAction Check: {}, ICMP ping result <{}> ({}) ",
        allActionDecisions.get(),
        pingAction.getAction(),
        isIcmpPingRemoved ? "removed" : "included");

    if (!componentsResponsibleForHealWait.isEmpty()) {
      return new ScheduledAction(
          Action.HEAL_WAIT,
          maxDateForActions(Action.HEAL_WAIT, automationAction, monitoringAction, pingAction),
          String.format(
              "Components reporting unhealthy state with HEAL_WAIT action: %s Component states:"
                  + " %s",
              componentsResponsibleForHealWait, allActionDecisions.get()));
    }

    // We should not do anything if the automation agent is in a bad state
    if (isAutomationAgentPingStale(
        automationAction, monitoringAction, pingAction, isIcmpPingRemoved)) {
      return new ScheduledAction(
          Action.HEAL_CANNOT_REPAIR,
          // The maximum wait time for actions in the planner is 7 days so make this 7 arbitrarily
          DateUtils.addDays(new Date(), 7),
          "Automation agent in a bad state but monitoring and ping are healthy",
          AutoHealMessage.CANNOT_REPAIR_AUTOMATION_AGENT_FAIL);
    }

    if (arePingsStale(automationAction, monitoringAction, pingAction, isIcmpPingRemoved)) {
      return new ScheduledAction(
          Action.HEAL_REPAIR,
          maxDateForActions(Action.HEAL_REPAIR, automationAction, monitoringAction, pingAction),
          "Automation agent, monitoring agent, and ping are all unhealthy");
    }

    final Set<String> componentsResponsibleForHealRepair =
        majorActions.entrySet().stream()
            .filter((mapPair) -> mapPair.getValue().getAction() == Action.HEAL_REPAIR)
            .map(Entry::getKey)
            .collect(Collectors.toUnmodifiableSet());

    // If at least one of these is repair, we should wait
    if (!componentsResponsibleForHealRepair.isEmpty()) {
      return new ScheduledAction(
          Action.HEAL_WAIT,
          // The maximum wait time for actions in the planner is 7 days so make this 7 arbitrarily
          DateUtils.addDays(new Date(), 7),
          String.format(
              "Components reporting unhealthy state with HEAL_REPAIR: %s Component states: %s",
              componentsResponsibleForHealRepair, allActionDecisions.get()));
    }

    if (replicaSetMemberAction.getAction() == Action.HEAL_REPAIR) {
      return new ScheduledAction(
          Action.HEAL_RESYNC,
          replicaSetMemberAction.getDate(),
          "Replica set member state is unhealthy");
    }

    if (replicaSetMemberAction.getAction() == Action.HEAL_WAIT) {
      return new ScheduledAction(
          Action.HEAL_WAIT, replicaSetMemberAction.getDate(), "In RECOVERING replica set state");
    }

    // The maximum wait time for actions in the planner is 7 days so make this 7 arbitrarily
    return new ScheduledAction(Action.NONE, DateUtils.addDays(new Date(), 7));
  }

  private Map<String, ScheduledAction> getMajorActions(
      final ScheduledAction pAutomationAction,
      final ScheduledAction pMonitoringAction,
      final ScheduledAction pPingAction,
      final boolean pRemoveIcmpPing) {
    final Map<String, ScheduledAction> healthUpdates = new HashMap<>();
    healthUpdates.put("lastAutomationAgentAuditDate", pAutomationAction);
    healthUpdates.put("lastMonitoringProcessPingDate", pMonitoringAction);

    if (!pRemoveIcmpPing) {
      healthUpdates.put("lastICMPPing", pPingAction);
    }

    return healthUpdates;
  }

  private boolean isAutomationAgentPingStale(
      final ScheduledAction pAutomationAction,
      final ScheduledAction pMonitoringAction,
      final ScheduledAction pPingAction,
      final boolean pRemoveIcmpPing) {
    return pAutomationAction.getAction() == Action.HEAL_REPAIR
        && pMonitoringAction.getAction() == Action.NONE
        && (pRemoveIcmpPing || pPingAction.getAction() == Action.NONE);
  }

  private boolean arePingsStale(
      final ScheduledAction pAutomationAction,
      final ScheduledAction pMonitoringAction,
      final ScheduledAction pPingAction,
      final boolean pRemoveIcmpPing) {
    return pAutomationAction.getAction() == Action.HEAL_REPAIR
        && pMonitoringAction.getAction() == Action.HEAL_REPAIR
        && (pRemoveIcmpPing || pPingAction.getAction() == Action.HEAL_REPAIR);
  }

  public boolean canHostBiConnector(final Logger pLogger, final boolean pRemoveIcmpPing) {
    // If the action is in an exclude state, return unavailable
    if (Action.BI_CONNECTOR_EXCLUDE_STATES.contains(getAction())) {
      return false;
    }

    // If we haven't received automation, monitoring, and ICMP pings within their respective health
    // thresholds, then consider this instance unfit to host the mongosqld processs.  If any of
    // these pings have been received within the designated threshold, the BI Connector can be
    // hosted on the instance.
    final ScheduledAction automationAction =
        getActionWithDateForHealthTimeThreshold(
            getHealth().getLastAutomationAgentAudit(), HealthThreshold.AUTOMATION_AGENT_AUDIT);

    final ScheduledAction monitoringAction =
        getActionWithDateForHealthTimeThreshold(
            getHealth().getLastMonitoringProcessPing(), HealthThreshold.MONITORING_PROCESS_PING);

    final ScheduledAction pingAction =
        getActionWithDateForHealthItem(
            getHealth().getLastICMPPing(), HealthThreshold.ICMP_PING, false);

    final Set<Action> healActions = Set.of(Action.HEAL_WAIT, Action.HEAL_REPAIR);

    // return true if any of the pings are healthy
    if (pRemoveIcmpPing) {
      pLogger.info(
          "Removed ICMP ping result <{}> from canHostBiConnector Check", pingAction.getAction());
      return !healActions.contains(automationAction.getAction())
          || !healActions.contains(monitoringAction.getAction());
    } else {
      pLogger.info(
          "Including ICMP ping result <{}> in canHostBiConnector Check", pingAction.getAction());
      return !healActions.contains(automationAction.getAction())
          || !healActions.contains(monitoringAction.getAction())
          || !healActions.contains(pingAction.getAction());
    }
  }

  protected ScheduledAction getActionWithDateForHealthTimeThreshold(
      final Optional<Date> pLastCollected, final IHealthThreshold pThreshhold) {
    if (!pLastCollected.isPresent()) {
      return new ScheduledAction(Action.NONE, null, "No health data collected yet");
    }
    final Date lastCollected = pLastCollected.get();
    final Date allHealthCollectedAt = getHealth().getCollectedFor().get();

    final Date unhealthyDate =
        new Date(allHealthCollectedAt.getTime() - pThreshhold.getUnhealthy().toMillis());
    if (lastCollected.before(unhealthyDate)) {
      return new ScheduledAction(
          Action.HEAL_REPAIR,
          lastCollected,
          String.format(
              "Health data collected at %s is unhealthy for more than %s",
              allHealthCollectedAt, pThreshhold.getUnhealthy()));
    }

    final Date waitDate =
        new Date(allHealthCollectedAt.getTime() - pThreshhold.getWait().toMillis());
    // Include the estimated time when it will move to HEAL_REPAIR
    final Date startRepairingDate =
        new Date(
            allHealthCollectedAt.getTime()
                + (allHealthCollectedAt.getTime() - unhealthyDate.getTime()));
    if (lastCollected.before(waitDate)) {
      return new ScheduledAction(
          Action.HEAL_WAIT,
          startRepairingDate,
          String.format(
              "Health data collected at %s waited for more than %s",
              allHealthCollectedAt, pThreshhold.getWait()));
    }

    return new ScheduledAction(Action.NONE, startRepairingDate);
  }

  protected ScheduledAction getActionWithDateForHealthItem(
      final Optional<HealthItem> pHealthItem,
      final IHealthThreshold pThreshold,
      final boolean isForRetiredInstance) {
    if (!pHealthItem.isPresent()) {
      return new ScheduledAction(Action.NONE, null);
    }

    // Only care about doing anything if we can confirm the bad value exists
    if (!pHealthItem.get().getValue().equals(pThreshold.getBadValue().get())) {
      return new ScheduledAction(Action.NONE, null);
    }

    final ScheduledAction timeBasedAction =
        getActionWithDateForHealthTimeThreshold(
            Optional.of(pHealthItem.get().getInitiallySeen()),
            isForRetiredInstance ? HealthThreshold.INSTANCE_RETIREMENT : pThreshold);

    return timeBasedAction.getAction() == Action.NONE
        ? new ScheduledAction(Action.HEAL_WAIT, timeBasedAction.getDate())
        : timeBasedAction;
  }

  protected ScheduledAction getReplicaSetMemberActionWithDateForHealthItem(
      final Optional<InstanceHardwareHealth.HealthItem> pHealthItem,
      final IHealthThreshold pThreshold) {
    if (!pHealthItem.isPresent()) {
      return new ScheduledAction(Action.NONE, null);
    }

    if (pHealthItem
        .get()
        .getValue()
        .equals(NDSReplicaSetMemberState.RECOVERING_NEEDS_RESYNC.name())) {
      // If we have discovered the instance can never catch up, we should repair the node and resync
      // immediately.
      return new ScheduledAction(Action.HEAL_REPAIR, pHealthItem.get().getInitiallySeen());
    }

    return getActionWithDateForHealthItem(pHealthItem, pThreshold, false);
  }

  /**
   * @return the current region of the node (not goal region)
   */
  public abstract RegionName getRegion(
      final Optional<CloudProviderContainer> pCloudProviderContainer);

  public boolean isDiskModificationBlockedAndWithinMandatoryCooldownPeriod(
      final int pMinimumGuaranteedDiskWaitTimeHours) {
    // Disk modification being blocked is only an issue for AWS and GCP
    return false;
  }

  public Optional<String> getACMEProvider() {
    return Optional.ofNullable(_acmeProvider);
  }

  // Returns true if the instance config needs to be updated.
  public boolean doesConfigNeedUpdate() {
    return getConfigNeedsUpdateAfter()
        .map(needsUpdateAfter -> needsUpdateAfter.before(new Date()))
        .orElse(false);
  }

  // Returns true if the instance config needs to be updated.
  public boolean doesAgentAPIKeyNeedUpdate() {
    return getAgentApiKeyNeedsUpdateAfter()
        .map(needsUpdateAfter -> needsUpdateAfter.before(new Date()))
        .orElse(false);
  }

  // Returns true if the instance config last updated date is before the enabled mongot last
  // update date.
  public boolean isBeforeEnabledMongotLastUpdateDate(Optional<Date> enabledMongotLastUpdateDate) {
    // Return early if enabled mongot date is not set, indicating no toggle occurred.
    if (enabledMongotLastUpdateDate.isEmpty()) {
      return false;
    }

    // Precaution default to the zero value if the config last updated date is unset.
    Date configLastUpdatedDate = getConfigLastUpdatedDate().orElse(new Date(0));
    return configLastUpdatedDate.before(enabledMongotLastUpdateDate.get());
  }

  /**
   * Returns true if this instance's config was last updated before the given gateway router
   * eligibility update date.
   */
  public boolean isBeforeGatewayRouterLastUpdateDate(
      final Optional<Date> pGatewayRouterLastUpdateDate) {
    if (pGatewayRouterLastUpdateDate.isEmpty()) {
      return false;
    }

    final Date instanceConfigLastUpdateDate = getConfigLastUpdatedDate().orElse(new Date(0));
    return pGatewayRouterLastUpdateDate.orElseThrow().after(instanceConfigLastUpdateDate);
  }

  @Override
  public boolean equals(Object other) {
    if (other == null) {
      return false;
    }
    if (other == this) {
      return true;
    }
    if (other.getClass() != getClass()) {
      return false;
    }

    InstanceHardware otherInstanceHardware = (InstanceHardware) other;
    return new EqualsBuilder()
        .append(_cloudContainerId, otherInstanceHardware._cloudContainerId)
        .append(_instanceId, otherInstanceHardware._instanceId)
        .append(_diskSizeGB, otherInstanceHardware._diskSizeGB)
        .append(_publicIP, otherInstanceHardware._publicIP)
        .append(_provisioned, otherInstanceHardware._provisioned)
        .append(_action, otherInstanceHardware._action)
        .append(_lastUpdated, otherInstanceHardware._lastUpdated)
        .append(_lastDiskModifyDate, otherInstanceHardware._lastDiskModifyDate)
        .append(_forceReplacement, otherInstanceHardware._forceReplacement)
        .append(_cloudProvider, otherInstanceHardware._cloudProvider)
        .append(_forceServerRestart, otherInstanceHardware._forceServerRestart)
        .append(_forceServerResync, otherInstanceHardware._forceServerResync)
        .append(_forceStopStartVM, otherInstanceHardware._forceStopStartVM)
        .append(_forceStopStartVMSources, otherInstanceHardware._forceStopStartVMSources)
        .append(_restartRequestedDate, otherInstanceHardware._restartRequestedDate)
        .append(_isPaused, otherInstanceHardware._isPaused)
        .append(_biConnector, otherInstanceHardware._biConnector)
        .append(_rebootRequestedDate, otherInstanceHardware._rebootRequestedDate)
        .append(_rebootRequestedBy, otherInstanceHardware._rebootRequestedBy)
        .append(
            _rebootRequestedChefCommitHash, otherInstanceHardware._rebootRequestedChefCommitHash)
        .append(_externalRebootRequestedDate, otherInstanceHardware._externalRebootRequestedDate)
        .append(_processRestartRequestedDates, otherInstanceHardware._processRestartRequestedDates)
        .append(_processRestartTasksToRun, otherInstanceHardware._processRestartTasksToRun)
        .append(_needsCriticalReboot, otherInstanceHardware._needsCriticalReboot)
        .append(_configLastUpdatedDate, otherInstanceHardware._configLastUpdatedDate)
        .append(_configNeedsUpdateAfter, otherInstanceHardware._configNeedsUpdateAfter)
        .append(_agentApiKeyNeedsUpdateAfter, otherInstanceHardware._agentApiKeyNeedsUpdateAfter)
        .append(_rotateSslAfter, otherInstanceHardware._rotateSslAfter)
        .append(_rotateSslCritical, otherInstanceHardware._rotateSslCritical)
        .append(_needsReloadSslOnProcesses, otherInstanceHardware._needsReloadSslOnProcesses)
        .append(
            _reloadSslOnProcessesRequestedDate,
            otherInstanceHardware._reloadSslOnProcessesRequestedDate)
        .append(_lastResumeDate, otherInstanceHardware._lastResumeDate)
        .append(_memberIndex, otherInstanceHardware._memberIndex)
        .append(_createDate, otherInstanceHardware._createDate)
        .append(_lastInstanceSizeModifyDate, otherInstanceHardware._lastInstanceSizeModifyDate)
        .append(_hostnames, otherInstanceHardware._hostnames)
        .append(_needsOSSwap, otherInstanceHardware._needsOSSwap)
        .append(_hostnameSchemeForAgents, otherInstanceHardware._hostnameSchemeForAgents)
        .append(_needsHostnameChange, otherInstanceHardware._needsHostnameChange)
        .append(_acmeSalt, otherInstanceHardware._acmeSalt)
        .append(_actionLastModifiedDate, otherInstanceHardware._actionLastModifiedDate)
        .append(_powerCycleAttempted, otherInstanceHardware._powerCycleAttempted)
        .append(_forcePlanningPrimary, otherInstanceHardware._forcePlanningPrimary)
        .append(
            _needsUngracefulDisconnectDate, otherInstanceHardware._needsUngracefulDisconnectDate)
        .append(_appliedOSPolicyVersion, otherInstanceHardware._appliedOSPolicyVersion)
        .append(
            _needsDiskCompactionResyncDate, otherInstanceHardware._needsDiskCompactionResyncDate)
        .append(_acmeProvider, otherInstanceHardware._acmeProvider)
        .isEquals();
  }

  @Override
  public int hashCode() {
    return new HashCodeBuilder()
        .append(_cloudContainerId)
        .append(_instanceId)
        .append(_diskSizeGB)
        .append(_publicIP)
        .append(_provisioned)
        .append(_action)
        .append(_lastUpdated)
        .append(_lastDiskModifyDate)
        .append(_forceReplacement)
        .append(_cloudProvider)
        .append(_forceServerRestart)
        .append(_forceServerResync)
        .append(_forceStopStartVM)
        .append(_forceStopStartVMSources)
        .append(_restartRequestedDate)
        .append(_isPaused)
        .append(_biConnector)
        .append(_rebootRequestedDate)
        .append(_rebootRequestedBy)
        .append(_rebootRequestedChefCommitHash)
        .append(_externalRebootRequestedDate)
        .append(_processRestartRequestedDates)
        .append(_processRestartTasksToRun)
        .append(_needsCriticalReboot)
        .append(_configLastUpdatedDate)
        .append(_configNeedsUpdateAfter)
        .append(_agentApiKeyNeedsUpdateAfter)
        .append(_rotateSslAfter)
        .append(_rotateSslCritical)
        .append(_needsReloadSslOnProcesses)
        .append(_reloadSslOnProcessesRequestedDate)
        .append(_lastResumeDate)
        .append(_memberIndex)
        .append(_createDate)
        .append(_lastInstanceSizeModifyDate)
        .append(_hostnames)
        .append(_needsOSSwap)
        .append(_hostnameSchemeForAgents)
        .append(_needsHostnameChange)
        .append(_acmeSalt)
        .append(_actionLastModifiedDate)
        .append(_powerCycleAttempted)
        .append(_forcePlanningPrimary)
        .append(_needsUngracefulDisconnectDate)
        .append(_appliedOSPolicyVersion)
        .append(_needsDiskCompactionResyncDate)
        .append(_acmeProvider)
        .toHashCode();
  }

  public static class FieldDefs {

    public static final String INSTANCE_ID = "instanceId";
    public static final String DISK_SIZE_GB = "diskSizeGB";
    public static final String CLOUD_PROVIDER_CONTAINER_ID = "cloudProviderContainerId";
    public static final String CREATE_DATE = "createDate";
    public static final String LAST_UPDATE_DATE = "lastUpdateDate";
    public static final String LAST_DISK_MODIFY_DATE = "lastDiskModifyDate";
    public static final String HOSTNAMES = "hostnames";
    public static final String PUBLIC_IP = "publicIP";
    public static final String PROVISIONED = "provisioned";
    public static final String ACTION = "action";
    public static final String HEALTH = "health";
    public static final String FORCE_REPLACEMENT = "forceReplacement";
    public static final String CLOUD_PROVIDER = "cloudProvider";
    public static final String FORCE_SERVER_RESTART = "forceServerRestart";
    public static final String FORCE_SERVER_RESYNC = "forceServerResync";
    public static final String FORCE_STOP_START_VM = "forceStopStartVM";
    public static final String FORCE_STOP_START_VM_SOURCES = "forceStopStartVMSources";
    public static final String INSTANCE_SIZE = "instanceSize";
    public static final String IS_PAUSED = "isPaused";
    public static final String BI_CONNECTOR = "biConnector";
    public static final String REBOOT_REQUESTED_DATE = "rebootRequestedDate";
    public static final String REBOOT_REQUESTED_BY = "rebootRequestedBy";
    public static final String EXTERNAL_REBOOT_REQUESTED_DATE = "externalRebootRequestedDate";
    public static final String RESTART_REQUESTED_DATE = "restartRequestedDate";
    public static final String PROCESS_RESTART_REQUESTED_DATES = "processRestartRequestedDates";
    public static final String PROCESS_RESTART_TASKS_TO_RUN = "processRestartTasksToRun";
    public static final String CONFIG_LAST_UPDATED_DATE = "configLastUpdatedDate";
    public static final String CONFIG_NEEDS_UPDATE_AFTER = "configNeedsUpdateAfter";
    public static final String AGENT_API_KEY_NEEDS_UPDATE_AFTER = "agentAPIKeyNeedsUpdateAfter";
    public static final String NEEDS_CRITICAL_REBOOT = "needsCriticalReboot";
    public static final String REBOOT_REQUESTED_CHEF_COMMIT_HASH = "rebootRequestedChefCommitHash";
    public static final String ROTATE_SSL_AFTER = "rotateSslAfter";
    public static final String ROTATE_SSL_CRITICAL = "rotateSslCritical";
    public static final String NEEDS_RELOAD_SSL_ON_PROCESSES = "needsReloadSslOnProcesses";
    public static final String RELOAD_SSL_ON_PROCESSES_REQUESTED_DATE =
        "reloadSslOnProcessesRequestedDate";
    public static final String LAST_RESUME_DATE = "lastResumeDate";
    public static final String MEMBER_INDEX = "memberIndex";
    public static final String LAST_INSTANCE_SIZE_MODIFY_DATE = "lastInstanceSizeModifyDate";
    public static final String NEEDS_OS_SWAP = "needsOSSwap";
    public static final String HOSTNAME_SCHEME_FOR_AGENTS = "hostnameSchemeForAgents";
    public static final String NEEDS_HOSTNAME_CHANGE = "needsHostnameChange";
    public static final String ACME_SALT = "acmeSalt";
    public static final String ACTION_LAST_MODIFIED_DATE = "actionLastModifiedDate";
    public static final String POWER_CYCLE_ATTEMPTED = "powerCycleAttempted";
    public static final String FORCE_PLANNING_PRIMARY = "forcePlanningPrimary";
    public static final String NEEDS_UNGRACEFUL_DISCONNECT_DATE = "needsUngracefulDisconnectDate";
    public static final String APPLIED_OS_POLICY_VERSION = "appliedOSPolicyVersion";
    public static final String NEEDS_DISK_COMPACTION_RESYNC_DATE = "needsDiskCompactionResyncDate";
    public static final String ACME_PROVIDER = "acmeProvider";
  }

  public interface IHealthThreshold {

    String name();

    Duration getWait();

    Duration getUnhealthy();

    Optional<String> getBadValue();
  }

  public enum HealthThreshold implements IHealthThreshold {
    AUTOMATION_AGENT_AUDIT(Duration.ofMinutes(4), Duration.ofHours(1)),
    AUTOMATION_AGENT_AUDIT_REDUCED(Duration.ofMinutes(4), Duration.ofMinutes(30)),
    MONITORING_PROCESS_PING(Duration.ofMinutes(4), Duration.ofHours(1)),
    MONITORING_PROCESS_PING_REDUCED(Duration.ofMinutes(4), Duration.ofMinutes(30)),
    ICMP_PING(Duration.ofHours(1), Duration.ofHours(1), PingResult.UNREACHABLE.getDisplay()),
    ICMP_PING_REDUCED(
        Duration.ofMinutes(30), Duration.ofMinutes(30), PingResult.UNREACHABLE.getDisplay()),
    REPLICA_SET_MEMBER_RECOVERY(Duration.ofHours(1), Duration.ofHours(1), State.RECOVERING.name()),
    REPLICA_SET_MEMBER_RECOVERY_REDUCED(
        Duration.ofMinutes(30), Duration.ofMinutes(30), State.RECOVERING.name()),
    INSTANCE_RETIREMENT(Duration.ZERO, Duration.ZERO);

    private final Duration _wait;
    private final Duration _unhealthy;
    private final String _badValue;

    HealthThreshold(final Duration pWait, final Duration pUnhealthy, final String pBadValue) {
      _wait = pWait;
      _unhealthy = pUnhealthy;
      _badValue = pBadValue;
    }

    HealthThreshold(final Duration pWait, final Duration pUnhealthy) {
      this(pWait, pUnhealthy, null);
    }

    @Override
    public Duration getWait() {
      return _wait;
    }

    @Override
    public Duration getUnhealthy() {
      return _unhealthy;
    }

    @Override
    public Optional<String> getBadValue() {
      return Optional.ofNullable(_badValue);
    }
  }

  public enum AutoHealMessage {
    // user-visible messages
    CRITICAL_POWER_CYCLE_FOR_RETIREMENT(
        "Instance was scheduled for retirement by the Cloud Provider and subsequently became"
            + " degraded. Atlas auto-healing will stop and start the instance immediately to avoid"
            + " prolonged workload impact."),
    CRITICAL_POWER_CYCLE("Instance is degraded."),
    POWER_CYCLE_FOR_RETIREMENT("Instance scheduled for retirement"),
    INSTANCE_REPLACEMENT_REQUESTED(
        "Attempt to recover the instance by stopping and starting was unsuccessful. Atlas"
            + " auto-healing will replace the instance"),
    NODE_RESYNC("Atlas auto-healing will resync the node"),

    // internal messages
    POWER_CYCLE_OPTIMIZATION(
        "Optimized healing action: power cycle instance before trying replacement"),
    CANNOT_REPAIR_NO_DATA(
        "Instance associated with replica set" + " either has no primary or is not sending pings."),
    CANNOT_RESYNC("Instance cannot be resynced due to a bad state"),
    CANNOT_REPAIR_AUTOMATION_AGENT_FAIL(
        "Automation agent in a bad state but monitoring and ping are healthy"),
    HEAL_WAIT(
        "Components reporting unhealthy state with HEAL_WAIT action: %s Component states:" + " %s"),
    INSTANCE_REPLACEMENT_FORCED(
        "Instance forceReplacement is set to true, Atlas auto-healing will replace the instance"),
    OPTIMIZED_HEALING_ACTION(
        "Optimizing action from HEAL_RESYNC to HEAL_REPAIR for instance. Performing optimized"
            + " initial sync."),
    NONE("");

    private final String _auditEventMessage;

    AutoHealMessage(final String pAuditEventMessage) {
      _auditEventMessage = pAuditEventMessage;
    }

    public String getAuditEventMessage() {
      return _auditEventMessage;
    }
  }

  public static class ScheduledAction {

    @JsonProperty("action")
    private final Action _action;

    @JsonProperty("date")
    private final Date _date;

    @JsonProperty("reason")
    private final String _reason;

    private final AutoHealMessage _autoHealMessage;

    public ScheduledAction(final Action pAction, final Date pDate) {
      this(pAction, pDate, null);
    }

    public ScheduledAction(final Action pAction, final Date pDate, final String pReason) {
      _action = pAction;
      _date = pDate;
      _reason = pReason;
      _autoHealMessage = AutoHealMessage.NONE;
    }

    public ScheduledAction(
        final Action pAction,
        final Date pDate,
        final String pReason,
        final AutoHealMessage pAutoHealMessage) {
      _action = pAction;
      _date = pDate;
      _reason = pReason;
      _autoHealMessage = pAutoHealMessage;
    }

    public Action getAction() {
      return _action;
    }

    public Date getDate() {
      return _date;
    }

    public String getReason() {
      return _reason;
    }

    public AutoHealMessage getAuditEventMessage() {
      return _autoHealMessage;
    }
  }

  public enum RebootRequestedBy {
    CHEF,
    OTHER
  }

  public enum Action {
    NONE(false),
    CREATE(true),
    DELETE(true),
    SYNC_PAUSE_STATE(true),
    RECONFIGURE(true),
    REQUESTED_REBOOT(true),
    REQUESTED_PROCESS_RESTART(true),
    OS_SWAP(true),
    REQUESTED_RELOAD_SSL_ON_PROCESSES(true),
    HEAL_WAIT(false),
    HEAL_REPAIR(true),
    HEAL_RESTART(true),
    UPDATE_CONFIG(true),
    ROTATE_AGENT_API_KEY(true),
    HEAL_CANNOT_REPAIR(false),
    SCHEDULED_DOWNTIME(false),
    HEAL_POWER_CYCLE(true),
    HEAL_POWER_CYCLE_CRITICAL(true),
    HEAL_RESYNC(true),
    HEAL_RESYNC_OIS(true),
    HEAL_RESYNC_FILECOPY(true),
    HEAL_RESYNC_LOGICAL(true),
    HEAL_CANNOT_RESYNC(false),
    // END OF NOTE
    // Schedules rolling logical initial syncs. At the time of writing, this is only used for
    // config server type transitions.  At this moment, it's not used by the rolling re-sync
    // automation, although it could be in the future.  It does not utilize OIS or file copy
    // initial sync optimizations yet, but it could in the future.
    DISK_COMPACTION_RESYNC(true),
    OS_POLICY_UPDATE_AND_REBOOT(true),
    STOP_START_VM(true),
    RESERVE_IPAM_IP(true),
    SWAP_FUTURE_IP(true),
    SWAP_PAST_IP(true),
    RELEASE_AWS_IP(true);

    private final boolean _actionable;

    Action(final boolean pActionable) {
      _actionable = pActionable;
    }

    public boolean isActionable() {
      return _actionable;
    }

    public static final Set<Action> ACTIONABLE_HEAL_STATES =
        Set.of(
            HEAL_REPAIR,
            HEAL_POWER_CYCLE,
            HEAL_RESYNC,
            HEAL_RESYNC_OIS,
            HEAL_RESYNC_FILECOPY,
            HEAL_RESYNC_LOGICAL,
            HEAL_RESTART,
            HEAL_POWER_CYCLE_CRITICAL);

    public static final Set<Action> TOPOLOGY_CHANGE_STATES = Set.of(CREATE, DELETE, RECONFIGURE);

    public static final Set<Action> ALL_HEAL_STATES =
        Set.of(
            HEAL_CANNOT_REPAIR,
            HEAL_CANNOT_RESYNC,
            HEAL_POWER_CYCLE,
            HEAL_POWER_CYCLE_CRITICAL,
            HEAL_REPAIR,
            HEAL_RESYNC,
            HEAL_RESYNC_OIS,
            HEAL_RESYNC_FILECOPY,
            HEAL_RESYNC_LOGICAL,
            HEAL_WAIT,
            HEAL_RESTART);

    public static final Set<Action> HEAL_SYNC_STATES =
        Set.of(HEAL_RESYNC_OIS, HEAL_RESYNC_FILECOPY, HEAL_RESYNC_LOGICAL);

    public static final Set<Action> BI_CONNECTOR_EXCLUDE_STATES =
        Set.of(HEAL_CANNOT_REPAIR, HEAL_POWER_CYCLE, HEAL_REPAIR, HEAL_RESYNC, DELETE, OS_SWAP);
  }

  protected static Date maxDateForActions(
      final Action pTargetAction, final ScheduledAction... pScheduledActions) {
    return Arrays.stream(pScheduledActions)
        .filter(s -> s.getAction().equals(pTargetAction))
        .max((s1, s2) -> s1.getDate().getTime() - s2.getDate().getTime() < 0 ? -1 : 0)
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    String.format("No provided action has action %s", pTargetAction.name())))
        .getDate();
  }

  public abstract Optional<Double> getRamSizeGB();

  public static class BiConnector {

    private final boolean _enabled;
    private final BiConnectorReadPreference _readPreference;

    public BiConnector(final BasicDBObject pDBObject) {
      final String readPreference = pDBObject.getString(FieldDefs.READ_PREFERENCE);
      _enabled = pDBObject.getBoolean(FieldDefs.ENABLED);
      _readPreference = BiConnectorReadPreference.fromValue(readPreference).get();
    }

    public BiConnector(final boolean pEnabled, final BiConnectorReadPreference pReadPreference) {
      _enabled = pEnabled;
      _readPreference = pReadPreference;
    }

    public BasicDBObject toDBObject() {
      return new BasicDBObject()
          .append(FieldDefs.ENABLED, _enabled)
          .append(FieldDefs.READ_PREFERENCE, _readPreference.getValue());
    }

    public BiConnectorReadPreference getReadPreference() {
      return _readPreference;
    }

    public boolean isEnabled() {
      return _enabled;
    }

    @Override
    public boolean equals(Object other) {
      if (other == null) {
        return false;
      }
      if (other == this) {
        return true;
      }
      if (other.getClass() != getClass()) {
        return false;
      }

      BiConnector otherBiConnector = (BiConnector) other;
      return new EqualsBuilder()
          .append(_enabled, otherBiConnector._enabled)
          .append(_readPreference, otherBiConnector._readPreference)
          .isEquals();
    }

    @Override
    public int hashCode() {
      return new HashCodeBuilder().append(_enabled).append(_readPreference).toHashCode();
    }

    public static class FieldDefs {

      public static final String ENABLED = "enabled";
      public static final String READ_PREFERENCE = "readPreference";
    }

    public static BiConnector getDefaultBiConnector() {
      return new BiConnector(false, BiConnectorReadPreference.getDefault());
    }
  }

  /** To support using InstanceHardware model in newer POJO supported classes */
  public static class InstanceHardwareCodec implements Codec<InstanceHardware> {

    private final CodecRegistry _codecRegistry;

    public InstanceHardwareCodec(final CodecRegistry pCodecRegistry) {
      _codecRegistry = pCodecRegistry;
    }

    @Override
    public InstanceHardware decode(final BsonReader pReader, final DecoderContext pDecoderContext) {
      final DBObject dbObject = _codecRegistry.get(DBObject.class).decode(pReader, pDecoderContext);

      return getHardware((BasicDBObject) dbObject);
    }

    @Override
    public void encode(
        final BsonWriter pWriter,
        final InstanceHardware pHardware,
        final EncoderContext pEncoderContext) {
      final BasicDBObject dbObject = pHardware.toDBObject();

      _codecRegistry.get(DBObject.class).encode(pWriter, dbObject, pEncoderContext);
    }

    @Override
    public Class<InstanceHardware> getEncoderClass() {
      return InstanceHardware.class;
    }
  }

  public boolean isForcePlanningPrimary() {
    return _forcePlanningPrimary;
  }

  public Optional<Date> getNeedsUngracefulDisconnectDate() {
    return Optional.ofNullable(_needsUngracefulDisconnectDate);
  }
}
