version: 1.0.0
aliases:
  - //.github/CODEOWNERS-aliases.yml
filters:
  - "/OWNERS.yaml":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "/AZBalancingRequirement.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-performance
  - "/RollableValue.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/chef/ExternalLogSinksChefConfig.java":
    approvers:
      - 10gen/code-review-team-intel-ii
  - "/privatelink/**":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/privatelink/AWSS3PrivateEndpoint.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/chef/GatewayRouterBootstrapConfig.java":
    approvers:
      - 10gen/code-review-team-data-exfil-maintainers
