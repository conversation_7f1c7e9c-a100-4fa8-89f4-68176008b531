package com.xgen.cloud.nds.ifr._public.svc;

import com.xgen.cloud.abtesting._public.model.ABTest;
import com.xgen.cloud.abtesting._public.svc.ABTestSvc;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.ifrabtest._public.model.ExperimentStatus;
import com.xgen.cloud.nds.ifr._public.model.IFREvent;
import com.xgen.cloud.nds.ifr._public.model.IFREvent.IFREventType;
import com.xgen.cloud.nds.project._public.model.versions.IFRState;
import com.xgen.cloud.nds.project._public.model.versions.IFRState.WaveStatus;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.PhasedVersionFactory;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersionParameters;
import com.xgen.cloud.nds.project._public.model.versions.ReleaseMode;
import com.xgen.cloud.nds.project._public.svc.versions.PhasedVersionSvc;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class IFRStateSyncCronSvc {

  private static final Logger LOG = LoggerFactory.getLogger(IFRStateSyncCronSvc.class);

  private static final Counter FAILED_IFR_WAVE_ALLOCATION_TRANSFORMATION =
      NDSPromMetricsSvc.registerStandardErrorCounter(
          "mms_nds_ifr_failed_wave_allocation_transformation_total",
          "Count of failed transformation of wave allocation from S3 bucket to internal atlas");

  private static final Counter PHASED_VERSION_ABTEST_VERSION_MISMATCH =
      NDSPromMetricsSvc.registerStandardErrorCounter(
          "mms_nds_ifr_phased_version_abtest_mismatch_total",
          "Count of experimentId mismatch between PhasedVersion and ABTest versions");

  private static final Histogram IFR_UPDATE_METADATA_DURATION =
      Histogram.build()
          .name("mms_nds_ifr_update_metadata_duration")
          .help("Histogram of time taken to update ifr metadata in seconds")
          .labelNames("waveNumber")
          .register();

  private final PhasedVersionSvc _phasedVersionSvc;
  private final ABTestSvc _abTestSvc;
  private final PhasedVersionFactory _phasedVersionFactory;
  private final IFRSvc _ifrSvc;

  @Inject
  public IFRStateSyncCronSvc(
      final PhasedVersionSvc pPhasedVersionSvc,
      final ABTestSvc pAbTestSvc,
      final PhasedVersionFactory pPhasedVersionFactory,
      final IFRSvc pIfrSvc) {
    _phasedVersionSvc = pPhasedVersionSvc;
    _abTestSvc = pAbTestSvc;
    _phasedVersionFactory = pPhasedVersionFactory;
    _ifrSvc = pIfrSvc;
  }

  private static final List<ExperimentStatus> ATLAS_ACTIVE_STATUSES =
      List.of(
          ExperimentStatus.CANCELLED,
          ExperimentStatus.CHECKING_WAVE_ALLOCATIONS,
          ExperimentStatus.COMPLETE,
          ExperimentStatus.COOLDOWN,
          ExperimentStatus.LIVE);

  public void syncIfrState() {
    // Handle experiments for ordinary software types
    _phasedVersionSvc.getCachedVersionForSoftwareTypes().entrySet().stream()
        .filter(e -> e.getValue().getReleaseMode().equals(ReleaseMode.IFR))
        .forEach(
            e -> {
              var matchingIfrTestOpt =
                  _abTestSvc.getIFRExperiments(e.getKey()).stream()
                      .filter(t -> ATLAS_ACTIVE_STATUSES.contains(t.getExperimentStatus()))
                      .findFirst();
              // If there is no matching ABTest or no IFR rollout config, skip.
              // Note that this fails for CONTINUOUS_DELIVERY_MONGODB_VERSION as there is no
              // experiment associated with that software type. This is handled before this stream
              // is processed.
              if (matchingIfrTestOpt.isEmpty()
                  || matchingIfrTestOpt.get().getIFRRolloutConfig() == null) {
                LOG.warn(
                    "Couldn't find a matching ABTest for PhasedVersion with softwareType: {}",
                    e.getKey());
                return;
              }
              synchronizeAtlasState(matchingIfrTestOpt.get(), e.getValue());
            });
  }

  private void synchronizeAtlasState(
      final ABTest ifrExperiment, final PhasedVersion phasedVersion) {
    if (phasedVersion.isIfrStateLocked()) {
      LOG.warn(
          "PhasedVersion is locked for SoftwareType: {}, skipping IFR state synchronization.",
          phasedVersion.getSoftwareType());
      return;
    }
    PhasedVersion currentPhasedVersion = phasedVersion;
    var currentIfrStateOpt = currentPhasedVersion.getIfrState();
    // This should never be the case but just for sanity checking.
    if (currentIfrStateOpt.isPresent()
        && !currentIfrStateOpt.get().experimentId().equals(ifrExperiment.getId())) {
      IllegalArgumentException exception =
          new IllegalArgumentException(
              String.format(
                  "Mismatched experimentId between PhasedVersion (%s) and ABTest (%s)",
                  currentIfrStateOpt.get().experimentId(), ifrExperiment.getId().toString()));
      NDSPromMetricsSvc.incrementStandardErrorCounter(
          PHASED_VERSION_ABTEST_VERSION_MISMATCH, exception);
      throw exception;
    }

    final boolean transitionedToNewExperimentStatus;
    final boolean transitionedToNewWaveNumber;
    final boolean changedIsPausedState;
    final boolean changedRolloutType;
    final boolean backwardsWave;
    final boolean allowedStateTransition;
    if (currentIfrStateOpt.isEmpty()) {
      transitionedToNewExperimentStatus = true;
      transitionedToNewWaveNumber = true;
      changedIsPausedState = true;
      changedRolloutType = false;
      backwardsWave = false;
      allowedStateTransition = true;
    } else {
      var newIFRState = currentIfrStateOpt.get();
      transitionedToNewExperimentStatus =
          newIFRState.experimentStatus() != ifrExperiment.getExperimentStatus();
      transitionedToNewWaveNumber =
          newIFRState.wave() != ifrExperiment.getIFRRolloutConfig().getLiveWaveNumber();
      changedIsPausedState =
          currentPhasedVersion.isPaused()
              != ifrExperiment.getIFRRolloutConfig().getIsRolloutPaused();
      changedRolloutType =
          newIFRState.rolloutType() != ifrExperiment.getIFRRolloutConfig().getRolloutType();
      backwardsWave = newIFRState.wave() > ifrExperiment.getIFRRolloutConfig().getLiveWaveNumber();
      allowedStateTransition =
          IFRState.isAllowedStateTransition(
              newIFRState.experimentStatus(), ifrExperiment.getExperimentStatus());
    }
    // Changing the rollout type for an existing experiment is not allowed.
    if (changedRolloutType) {
      LOG.error(
          "Attempted to change rollout type for experiment {}: {} -> {} ",
          ifrExperiment.getId(),
          currentIfrStateOpt.get().rolloutType(),
          ifrExperiment.getIFRRolloutConfig().getRolloutType());
      return;
    }
    // An experiment must not go backwards a wave.
    if (backwardsWave) {
      LOG.error(
          "Attempted to go backwards a wave for experiment: {}. Current wave: {}, new wave: {}",
          ifrExperiment.getId(),
          currentIfrStateOpt.get().wave(),
          ifrExperiment.getIFRRolloutConfig().getLiveWaveNumber());
      return;
    }
    // Block certain wave transitions.
    // TODO: https://jira.mongodb.org/browse/CLOUDP-339232 will add configurable support for
    // waveless rollouts which require different transitions.
    if (!allowedStateTransition) {
      LOG.error(
          "Attempted disallowed experiment state transition from {} to {} for experiment: {}",
          currentIfrStateOpt.get().experimentStatus(),
          ifrExperiment.getExperimentStatus(),
          ifrExperiment.getId());
      return;
    }
    // If we transitioned to a new state, update the PhasedVersion if either we didn't
    // have a currentIfrState or we had one with the same experimentId.
    if (transitionedToNewExperimentStatus || transitionedToNewWaveNumber || changedIsPausedState) {
      LOG.info(
          "Found a change in IFR state for version: {}",
          ifrExperiment.getIFRRolloutConfig().getMongoDBVersion().toString());
      var newIFRState =
          new IFRState(
              ifrExperiment.getId(),
              ifrExperiment.getExperimentStatus(),
              ifrExperiment.getIFRRolloutConfig().getRolloutType(),
              ifrExperiment.getIFRRolloutConfig().getLiveWaveNumber(),
              WaveStatus.INITIALIZING);
      // Start building the new PhasedVersion object.
      var newPhasedVersionParams = getPhasedVersionParameters(currentPhasedVersion);
      switch (ifrExperiment.getExperimentStatus()) {
        case LIVE, COOLDOWN -> newPhasedVersionParams.setIfrState(newIFRState);
        case COMPLETE -> newPhasedVersionParams.setPreviousIfrState(newIFRState);
        default -> {}
      }
      newPhasedVersionParams.setIsPaused(ifrExperiment.getIFRRolloutConfig().getIsRolloutPaused());
      newPhasedVersionParams.setTargetVersion(
          ifrExperiment.getIFRRolloutConfig().getMongoDBVersion().getVersion());
      var newPhasedVersion = _phasedVersionFactory.createPhasedVersion(newPhasedVersionParams);
      // Update in case any changes were made wrt. the original object.
      if (!newPhasedVersion.equals(currentPhasedVersion)) {
        // Send pause/unpause events if necessary.
        if (newPhasedVersion.isPaused() != currentPhasedVersion.isPaused()) {
          _ifrSvc.saveEvent(
              new IFREvent(
                  new ObjectId(),
                  ifrExperiment.getId(),
                  null,
                  newPhasedVersion.isPaused()
                      ? IFREventType.ROLLOUT_PAUSED
                      : IFREventType.ROLLOUT_UNPAUSED,
                  ifrExperiment.getIFRRolloutConfig().getLiveWaveNumber(),
                  false,
                  new Date()));
        }
        savePhasedVersion(
            newPhasedVersion,
            ifrExperiment.getId(),
            ifrExperiment.getIFRRolloutConfig().getLiveWaveNumber());
        currentPhasedVersion = newPhasedVersion;
        LOG.info(
            "Saved new PhasedVersion: {}; old PhasedVersion {}",
            newPhasedVersion,
            currentPhasedVersion);
      }
    }

    // If the status is FINALIZED or only the paused status was changed, we don't need to
    // transform the wave allocation.
    if (currentPhasedVersion.getIfrState().isPresent()
            && currentPhasedVersion.getIfrState().get().waveStatus().equals(WaveStatus.FINALIZED)
        || (changedIsPausedState
            && !transitionedToNewExperimentStatus
            && !transitionedToNewWaveNumber)) {
      return;
    }

    // Trigger the wave allocation.
    try {
      LOG.info(
          "Starting a new wave allocation transformation for experiment {}, wave {}",
          ifrExperiment.getId(),
          ifrExperiment.getIFRRolloutConfig().getLiveWaveNumber());
      _ifrSvc.triggerWaveAllocationTransformation(
          ifrExperiment.getId(), ifrExperiment.getIFRRolloutConfig().getLiveWaveNumber());
      // Save the IFR rollout configuration.
      savePhasedVersion(
          updateIFRStateWaveStatusFinalized(currentPhasedVersion),
          ifrExperiment.getId(),
          ifrExperiment.getIFRRolloutConfig().getLiveWaveNumber());
    } catch (com.xgen.cloud.common.model._public.error.SvcException exception) {
      NDSPromMetricsSvc.incrementStandardErrorCounter(
          FAILED_IFR_WAVE_ALLOCATION_TRANSFORMATION, exception);
      LOG.error(
          "Failed to trigger wave allocation transformation for experiment {}: {}",
          ifrExperiment.getId(),
          exception.getMessage(),
          exception);
    }
  }

  @SuppressWarnings("try")
  private void savePhasedVersion(
      final PhasedVersion pPhasedVersion, ObjectId experimentId, int waveNumber) {
    try (final var timer =
        IFR_UPDATE_METADATA_DURATION.labels(String.valueOf(waveNumber)).startTimer()) {
      _phasedVersionSvc.save(pPhasedVersion);
    }
  }

  // Used to directly trigger the wave transformation for E2E manual tests.
  public void syncIfrStateForE2ETesting() {
    try {
      // Relies on experiment Id and wave being overridden in app settings.
      _ifrSvc.triggerWaveAllocationTransformation(new ObjectId(), -1);
    } catch (com.xgen.cloud.common.model._public.error.SvcException exception) {
      LOG.error(
          "Failed to trigger wave allocation transformation for E2E test experimen", exception);
    }
  }

  private PhasedVersionParameters getPhasedVersionParameters(final PhasedVersion pPhasedVersion) {
    return new PhasedVersionParameters(
            pPhasedVersion.getSoftwareType(), pPhasedVersion.getTargetVersion())
        .setIfrState(pPhasedVersion.getIfrState().orElse(null))
        .setPreviousIfrState(pPhasedVersion.getPreviousIfrState().orElse(null))
        .setIsPaused(pPhasedVersion.isPaused())
        .setReleaseMode(pPhasedVersion.getReleaseMode())
        .setIsPreviousVersionSafe(pPhasedVersion.isPreviousVersionSafe())
        .setPhasedReleaseCriteria(pPhasedVersion.getPhasedReleaseCriteria().orElse(null))
        .setCriticalReleaseDurationHours(pPhasedVersion.getCriticalReleaseDurationHours());
  }

  private PhasedVersion updateIFRStateWaveStatusFinalized(final PhasedVersion pPhasedVersion) {
    // IFR State should always be present as we have just saved it.
    if (pPhasedVersion.getIfrState().isEmpty()) {
      throw new IllegalArgumentException("IfrState should have been set");
    }

    var presentIFRState = pPhasedVersion.getIfrState().get();
    var newIFRState =
        new IFRState(
            presentIFRState.experimentId(),
            presentIFRState.experimentStatus(),
            presentIFRState.rolloutType(),
            presentIFRState.wave(),
            WaveStatus.FINALIZED);

    var newPhasedVersionParams = getPhasedVersionParameters(pPhasedVersion);
    newPhasedVersionParams.setIfrState(newIFRState);
    return _phasedVersionFactory.createPhasedVersion(newPhasedVersionParams);
  }
}
