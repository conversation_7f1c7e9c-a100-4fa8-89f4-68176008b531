package com.xgen.cloud.nds.serverless.runtime.res.api_2023_01_01;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.OPERATION_ID_OVERRIDE;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.VERB_OVERRIDE;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.openapi._public.constant.OpenApiConst.ResponseDescriptions;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.atlas.ApiAtlasBaseLegacyClusterDescriptionResource;
import com.xgen.svc.mms.api.res.atlas.ApiAtlasLegacyServerlessInstanceDescriptionResource.PaginatedServerlessInstanceDescriptionView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasServerlessInstanceDescriptionCreateView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasServerlessInstanceDescriptionUpdateView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasServerlessInstanceDescriptionView;
import com.xgen.svc.mms.res.filter.AllowTemporaryApiKeys;
import com.xgen.svc.mms.res.filter.PaidInFull;
import com.xgen.svc.mms.res.filter.SubscriptionPlan;
import com.xgen.svc.nds.svc.TenantClusterConfigurationSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.svc.nds.tenantUpgrade.svc.TenantUpgradeSvc;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@SubscriptionPlan(PlanTypeSet.NDS)
@PaidInFull
@Path("/api/atlas/v2/groups/{groupId}/serverless")
@Singleton
public class ApiAtlasLegacyServerlessInstanceDescriptionResource
    extends ApiAtlasBaseLegacyClusterDescriptionResource<
        ApiAtlasServerlessInstanceDescriptionView> {

  private final com.xgen.svc.mms.api.res.atlas.ApiAtlasLegacyServerlessInstanceDescriptionResource
      _v1Resource;

  @Inject
  public ApiAtlasLegacyServerlessInstanceDescriptionResource(
      final AppSettings pSettings,
      final NDSUISvc pNDSUISvc,
      final NDSGroupSvc pNDSGroupSvc,
      final AuditSvc pAuditSvc,
      final TenantClusterConfigurationSvc pTenantClusterConfigurationSvc,
      final OnlineArchiveSvc pOnlineArchiveSvc,
      final TenantUpgradeSvc pTenantUpgradeSvc,
      final com.xgen.svc.mms.api.res.atlas.ApiAtlasLegacyServerlessInstanceDescriptionResource
          pV1Resource) {
    super(
        pSettings,
        pNDSUISvc,
        pNDSGroupSvc,
        pAuditSvc,
        pTenantClusterConfigurationSvc,
        pOnlineArchiveSvc,
        pTenantUpgradeSvc);
    _v1Resource = pV1Resource;
  }

  @GET
  @Produces({VersionMediaType.V_2023_01_01_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY, RoleSet.NAME.GLOBAL_BAAS_INTERNAL_ADMIN})
  @Operation(
      summary = "Return All Serverless Instances in One Project",
      operationId = "listGroupServerlessInstances",
      description =
          "Returns details for all serverless instances in the specified project. To use this"
              + " resource, the requesting Service Account or API Key must have the Project Read"
              + " Only role.\n\n"
              + "This endpoint also lists Flex clusters that were created using the"
              + " [createServerlessInstance]"
              + "(https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Serverless-Instances/operation/createServerlessInstance)"
              + " endpoint or former Serverless instances that have been migrated to Flex clusters,"
              + " until January 2026 after which this endpoint will be sunset. Continuous backups"
              + " are not supported and serverlessContinuousBackupEnabled will not take effect on"
              + " these clusters. Please use the listFlexClusters endpoint instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "listFlexClusters",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/listFlexClusters"),
      tags = {"Serverless Instances"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "includeCount"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty")
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema =
                        @Schema(implementation = PaginatedServerlessInstanceDescriptionView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-01-01")
                          })
                    })),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Clusters Security III")
            }),
        @Extension(
            name = IPA_EXCEPTION, // TODO CLOUDP-309178
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-117-description-should-not-use-inline-links",
                  value = "Description has multiple links, and externalDocs only supports one."),
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "listInstances"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listServerlessInstances")
            }),
      })
  @AllowTemporaryApiKeys
  public Response getAllServerlessInstances(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope)
      throws Exception {
    return _v1Resource.getAllServerlessInstances(pRequest, pGroup, pEnvelope);
  }

  @GET
  @Path("/{name}")
  @Produces({VersionMediaType.V_2023_01_01_JSON})
  @RolesAllowed({
    RoleSet.NAME.GROUP_READ_ONLY,
    RoleSet.NAME.GLOBAL_BAAS_INTERNAL_ADMIN,
    RoleSet.NAME.GLOBAL_QUERY_ENGINE_INTERNAL_ADMIN
  })
  @Operation(
      summary = "Return One Serverless Instance from One Project",
      operationId = "getGroupServerlessInstance",
      description =
          "Returns details for one serverless instance in the specified project. To use this"
              + " resource, the requesting Service Account or API Key must have the Project Read"
              + " Only role.\n\n"
              + "This API can also be used on Flex clusters that were created with the"
              + " [createServerlessInstance]"
              + "(https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Serverless-Instances/operation/createServerlessInstance)"
              + " endpoint or Flex clusters that were migrated from Serverless instances."
              + " Continuous backups are not supported and serverlessContinuousBackupEnabled will"
              + " not take effect on these clusters. This endpoint will be sunset in January 2026."
              + " Please use the getFlexCluster endpoint instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "getFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/getFlexCluster"),
      tags = {"Serverless Instances"},
      parameters = {
        @Parameter(ref = "groupId"),
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "name",
            description = "Human-readable label that identifies the serverless instance.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema =
                        @Schema(implementation = ApiAtlasServerlessInstanceDescriptionView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-01-01")
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                                value = "API predates IPA validation."),
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Clusters Security III")
            }),
        @Extension(
            name = IPA_EXCEPTION, // TODO CLOUDP-309178
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-117-description-should-not-use-inline-links",
                  value = "Description has multiple links, and externalDocs only supports one."),
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "getInstance"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getServerlessInstance")
            }),
      })
  @AllowTemporaryApiKeys
  public Response getServerlessInstance(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("name") final String pName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    return _v1Resource.getServerlessInstance(pGroup, pName, pEnvelope);
  }

  @POST
  @Produces(VersionMediaType.V_2023_01_01_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Create One Serverless Instance in One Project",
      operationId = "createGroupServerlessInstance",
      description =
          "Update as of Feb 2025: This endpoint now creates a Flex cluster instead. This endpoint"
              + " will no longer be supported starting January 2026. Continuous backups are not"
              + " supported and serverlessContinuousBackupEnabled will not take effect. Please use"
              + " the createFlexCluster endpoint instead.\n\n"
              + "Creates one serverless instance in the specified project. To use this resource,"
              + " the requesting Service Account or API Key must have the Project Owner role.",
      externalDocs =
          @ExternalDocumentation(
              description = "createFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/createFlexCluster"),
      tags = {"Serverless Instances"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty")
      },
      responses = {
        @ApiResponse(
            responseCode = "201",
            description = "Created",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema =
                        @Schema(implementation = ApiAtlasServerlessInstanceDescriptionView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-01-01")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "402", ref = "paymentRequired"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Create One Serverless Instance in One Project.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2023_01_01_JSON,
                      schema =
                          @Schema(
                              implementation =
                                  ApiAtlasServerlessInstanceDescriptionCreateView.class),
                      extensions = {
                        @Extension(
                            name = IPA_EXCEPTION,
                            properties = {
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                                  value = "Content predates IPA validation."),
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                                  value = "Content predates IPA validation."),
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-106-create-method-request-body-is-get-method-response",
                                  value = "Content predates IPA validation.")
                            })
                      })),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Clusters Security III")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "createInstance"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "createServerlessInstance")
            })
      })
  public Response createServerlessInstance(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasServerlessInstanceDescriptionCreateView
          pServerlessInstanceDescriptionCreateView,
      @Context final AppUser pCurrentUser)
      throws Exception {
    return _v1Resource.createServerlessInstance(
        pRequest,
        pOrganization,
        pGroup,
        pNDSGroup,
        pAuditInfo,
        pEnvelope,
        pServerlessInstanceDescriptionCreateView,
        pCurrentUser);
  }

  @PATCH
  @Path("/{name}")
  @Produces(VersionMediaType.V_2023_01_01_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Update One Serverless Instance in One Project",
      operationId = "updateGroupServerlessInstance",
      description =
          "Updates one serverless instance in the specified project. To use this resource, the"
              + " requesting Service Account or API Key must have the Project Owner role.\n\n"
              + "This API can also be used on Flex clusters that were created with the"
              + " [createServerlessInstance]"
              + "(https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Serverless-Instances/operation/createServerlessInstance)"
              + " endpoint or Flex clusters that were migrated from Serverless instances. This"
              + " endpoint will be sunset in January 2026. Continuous backups are not supported and"
              + " serverlessContinuousBackupEnabled will not take effect on these clusters. Please"
              + " use the updateFlexCluster endpoint instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "updateFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/updateFlexCluster"),
      tags = {"Serverless Instances"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "name",
            description = "Human-readable label that identifies the serverless instance.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema =
                        @Schema(implementation = ApiAtlasServerlessInstanceDescriptionView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-01-01")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "402", ref = "paymentRequired"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Update One Serverless Instance in One Project.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2023_01_01_JSON,
                      schema =
                          @Schema(
                              implementation =
                                  ApiAtlasServerlessInstanceDescriptionUpdateView.class),
                      extensions =
                          @Extension(
                              name = IPA_EXCEPTION,
                              properties = {
                                @ExtensionProperty(
                                    name =
                                        "xgen-IPA-107-update-method-request-body-is-get-method-response",
                                    value = "API predates IPA validation."),
                                @ExtensionProperty(
                                    name =
                                        "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                    value = "API predates IPA validation.")
                              }))),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Clusters Security III")
            }),
        @Extension(
            name = IPA_EXCEPTION, // TODO CLOUDP-309178
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-117-description-should-not-use-inline-links",
                  value = "Description has multiple links, and externalDocs only supports one."),
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "updateInstance"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "updateServerlessInstance")
            }),
      })
  public Response updateServerlessInstance(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("name") final String pName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasServerlessInstanceDescriptionUpdateView
          pServerlessInstanceDescriptionUpdateView,
      @Context final AppUser pCurrentUser)
      throws Exception {
    return _v1Resource.updateServerlessInstance(
        pRequest,
        pOrganization,
        pGroup,
        pAuditInfo,
        pName,
        pEnvelope,
        pServerlessInstanceDescriptionUpdateView,
        pCurrentUser);
  }

  @DELETE
  @Path("/{name}")
  @Produces(VersionMediaType.V_2023_01_01_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Remove One Serverless Instance from One Project",
      operationId = "deleteGroupServerlessInstance",
      description =
          "Removes one serverless instance from the specified project. The serverless instance must"
              + " have termination protection disabled in order to be deleted. To use this"
              + " resource, the requesting Service Account or API Key must have the Project Owner"
              + " role.\n\n"
              + "This API can also be used on Flex clusters that were created with the"
              + " [createServerlessInstance](https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Serverless-Instances/operation/createServerlessInstance)"
              + " endpoint or Flex clusters that were migrated from Serverless instances. This"
              + " endpoint will be sunset in January 2026. Please use the deleteFlexCluster"
              + " endpoint instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "deleteFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/deleteFlexCluster"),
      tags = {"Serverless Instances"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "name",
            description = "Human-readable label that identifies the serverless instance.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "202",
            description = ResponseDescriptions.ACCEPTED,
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema = @Schema(ref = "RequestAccepted"),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-01-01")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-108-delete-method-return-204-response",
                  value = "Schema predates IPA validation.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Clusters Security III")
            }),
        @Extension(
            name = IPA_EXCEPTION, // TODO CLOUDP-309178
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-117-description-should-not-use-inline-links",
                  value = "Description has multiple links, and externalDocs only supports one."),
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "deleteInstance"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "deleteServerlessInstance")
            }),
      })
  public Response deleteServerlessInstance(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("name") final String pName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    return _v1Resource.deleteServerlessInstance(pRequest, pGroup, pAuditInfo, pName, pEnvelope);
  }
}
