package com.xgen.cloud.nds.capacity._public.svc;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureAvailabilityZone;
import com.xgen.cloud.nds.azure._public.model.AzureAvailabilityZoneName;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzurePhysicalZoneId;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.settings.NDSAzureBlocklistSettings;
import com.xgen.cloud.nds.capacity._private.dao.AzureCapacityDenylistDao;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenyListEntry.Status;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenylistStatBucket;
import com.xgen.cloud.nds.capacity._public.model.azure.AzureCapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.model.azure.AzureCapacityDenyListEntry.Identifier;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Clock;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class AzureCapacityDenylistSvc
    extends CapacityDenylistSvc<
        AzureRegionName,
        AzureNDSInstanceSize,
        AzureInstanceFamily,
        AzureCapacityDenyListEntry,
        Identifier> {

  private static final Logger LOG = LoggerFactory.getLogger(AzureCapacityDenylistSvc.class);

  @Inject private AzureCapacityDenylistDao _azureCapacityDenylistDao;
  @Inject private AzureSubscriptionDao _azureSubscriptionDao;
  @Inject private AppSettings appSettings;
  @Inject private GroupSvc groupSvc;
  @Inject private Clock _clock;
  @Inject private NDSAzureBlocklistSettings ndsaAzureBlocklistSettings;

  @Override
  protected AzureCapacityDenylistDao getCapacityDenylistDao() {
    return _azureCapacityDenylistDao;
  }

  public List<AzureCapacityDenyListEntry> getDenylistEntriesNeedingCapacityCheck() {
    return getCapacityDenylistDao().getAllEntries().stream()
        .filter(entry -> !entry.getStatus().isOverride())
        .filter(
            entry ->
                entry
                    .getIdentifier()
                    .getAzurePhysicalZoneId()
                    .isPresent()) // skip availability sets
        .toList();
  }

  /**
   * This method returns all entries. This may include multiple entries per instance size / region
   */
  public List<AzureCapacityDenyListEntry> getAllCapacityDenylistEntries() {
    return getCapacityDenylistDao().getAllEntries();
  }

  public List<AzureCapacityDenyListEntry> getAllUnavailableCapacityDenylistEntries() {
    return getCapacityDenylistDao().getAllEntries().stream()
        .filter(CapacityDenyListEntry::isUnavailable)
        .toList();
  }

  public List<AzureCapacityDenyListEntry> getCapacityDenylistEntriesForRegion(
      final AzureRegionName pRegion) {
    return getCapacityDenylistDao().getAllEntriesForRegion(pRegion);
  }

  @Override
  protected AzureCapacityDenyListEntry createInitialEntryForEvent(
      final Identifier pEntryIdentifier,
      final int pNumSuccesses,
      final int pNumFailures,
      final Optional<Status> pOverrideStatus,
      final Optional<String> pJira) {
    final Instant now = _clock.instant();
    final List<CapacityDenylistStatBucket> buckets =
        createInitialStatBucketsForEvent(now, pNumSuccesses, pNumFailures);

    return new AzureCapacityDenyListEntry(
        pEntryIdentifier.getRegionName(),
        pEntryIdentifier.getInstanceSize(),
        pEntryIdentifier.getInstanceFamily(),
        pEntryIdentifier.getAzurePhysicalZoneId(),
        Date.from(now),
        calculateStatus(buckets, pOverrideStatus),
        pJira,
        buckets);
  }

  private Optional<AzurePhysicalZoneId> getPhysicalZoneIdFromName(
      final AzureRegionName pRegionName,
      final ObjectId pSubscriptionId,
      final Optional<AzureAvailabilityZoneName> pAzureAvailabilityZoneName) {
    return pAzureAvailabilityZoneName
        .flatMap(
            zoneName ->
                _azureSubscriptionDao
                    .find(pSubscriptionId)
                    .flatMap(
                        subscription ->
                            subscription
                                .getRegion(pRegionName)
                                .getAvailabilityZoneByName(zoneName)))
        .flatMap(AzureAvailabilityZone::getPhysicalZoneId);
  }

  public void registerCapacityFailure(
      final String pInstanceType,
      final AzureRegionName pRegionName,
      final ObjectId pSubscriptionId,
      final Optional<AzureAvailabilityZoneName> pAzureAvailabilityZoneName) {
    final Optional<AzureNDSInstanceSize> instanceSize =
        AzureNDSInstanceSize.findByAzureInstanceType(pInstanceType);
    final Optional<AzureInstanceFamily> instanceFamily =
        AzureInstanceFamily.fromInstanceType(pInstanceType);
    final Optional<AzurePhysicalZoneId> zoneId =
        getPhysicalZoneIdFromName(pRegionName, pSubscriptionId, pAzureAvailabilityZoneName);
    instanceSize.ifPresent(
        azureNDSInstanceSize ->
            registerCapacityFailure(
                instanceFamily.orElseThrow(), azureNDSInstanceSize, pRegionName, zoneId));
  }

  public void registerCapacityFailure(
      final AzureInstanceFamily pInstanceFamily,
      final AzureInstanceSize pInstanceSize,
      final AzureRegionName pRegionName,
      final ObjectId pSubscriptionId,
      final Optional<AzureAvailabilityZoneName> pAzureAvailabilityZoneName) {
    final Optional<AzurePhysicalZoneId> zoneId =
        getPhysicalZoneIdFromName(pRegionName, pSubscriptionId, pAzureAvailabilityZoneName);
    registerCapacityFailure(pInstanceFamily, pInstanceSize, pRegionName, zoneId);
  }

  public void registerCapacityFailure(
      final AzureInstanceFamily pInstanceFamily,
      final AzureInstanceSize pInstanceSize,
      final AzureRegionName pRegionName,
      final Optional<AzurePhysicalZoneId> pPhysicalZoneId) {
    if (pRegionName.supportsAvailabilityZones() && pPhysicalZoneId.isEmpty()) {
      return;
    }
    // Instance size deny-listing is currently only implemented for NDSInstanceSizes, so we
    // exclude any other implementations of the interface here.
    if (pInstanceSize instanceof final AzureNDSInstanceSize azureNDSInstanceSize) {
      registerCapacityFailureInternal(
          new Identifier(pRegionName, azureNDSInstanceSize, pInstanceFamily, pPhysicalZoneId));
    }
  }

  public void registerCapacityOverride(
      final AzureInstanceFamily pInstanceFamily,
      final AzureInstanceSize pInstanceSize,
      final AzureRegionName pRegionName,
      final Optional<AzurePhysicalZoneId> pPhysicalZoneId,
      final Status pOverrideStatus,
      final Optional<String> pJira) {
    if (pRegionName.supportsAvailabilityZones() && pPhysicalZoneId.isEmpty()) {
      return;
    }
    // Instance size deny-listing is currently only implemented for NDSInstanceSizes, so we
    // exclude any other implementations of the interface here.
    if (pInstanceSize instanceof final AzureNDSInstanceSize azureNDSInstanceSize) {
      registerCapacityOverrideInternal(
          new Identifier(pRegionName, azureNDSInstanceSize, pInstanceFamily, pPhysicalZoneId),
          pOverrideStatus,
          pJira);
    }
  }

  public void registerSuccessfulVMReservation(
      final String pInstanceType,
      final AzureRegionName pRegionName,
      final ObjectId pSubscriptionId,
      final Optional<AzureAvailabilityZoneName> pAzureAvailabilityZoneName) {
    final Optional<AzureNDSInstanceSize> instanceSize =
        AzureNDSInstanceSize.findByAzureInstanceType(pInstanceType);
    final Optional<AzureInstanceFamily> instanceFamily =
        AzureInstanceFamily.fromInstanceType(pInstanceType);
    final Optional<AzurePhysicalZoneId> zoneId =
        getPhysicalZoneIdFromName(pRegionName, pSubscriptionId, pAzureAvailabilityZoneName);
    instanceSize.ifPresent(
        azureNDSInstanceSize ->
            registerSuccessfulVMReservation(
                instanceFamily.orElseThrow(), azureNDSInstanceSize, pRegionName, zoneId));
  }

  public void registerSuccessfulVMReservation(
      final AzureInstanceFamily pInstanceFamily,
      final AzureInstanceSize pInstanceSize,
      final AzureRegionName pRegionName,
      final ObjectId pSubscriptionId,
      final Optional<AzureAvailabilityZoneName> pAzureAvailabilityZoneName) {
    final Optional<AzurePhysicalZoneId> zoneId =
        getPhysicalZoneIdFromName(pRegionName, pSubscriptionId, pAzureAvailabilityZoneName);
    registerSuccessfulVMReservation(pInstanceFamily, pInstanceSize, pRegionName, zoneId);
  }

  public void registerSuccessfulVMReservation(
      final AzureInstanceFamily pInstanceFamily,
      final AzureInstanceSize pInstanceSize,
      final AzureRegionName pRegionName,
      final Optional<AzurePhysicalZoneId> pPhysicalZoneId) {
    if (pRegionName.supportsAvailabilityZones() && pPhysicalZoneId.isEmpty()) {
      return;
    }
    // Instance size deny-listing is currently only implemented for NDSInstanceSizes, so we
    // exclude any other implementations of the interface here.
    if (pInstanceSize instanceof final AzureNDSInstanceSize azureNDSInstanceSize) {
      registerSuccessfulVMReservationInternal(
          new Identifier(pRegionName, azureNDSInstanceSize, pInstanceFamily, pPhysicalZoneId));
    }
  }

  public Map<AzureRegionName, List<AzureNDSInstanceSize>> getUnavailableInstanceSizesByRegion(
      final ObjectId groupId) {
    if (isFeatureFlagEnabled(
        FeatureFlag.ATLAS_EXCLUDE_REGION_USEAST2_FROM_AZURE_DSV5_ESV5_FAMILIES,
        appSettings,
        null,
        groupSvc.findById(groupId))) {
      LOG.trace("Ignoring capacity restrictions for groupId={}", groupId);
      return new HashMap<>();
    }

    final Map<AzureRegionName, List<AzureNDSInstanceSize>> unavailableInstanceSizesByRegion =
        getUnavailableInstanceSizesByRegion();

    if (isFeatureFlagEnabled(
        FeatureFlag.ATLAS_AZURE_EXCLUDE_CONSTRAINED_COMBOS,
        appSettings,
        null,
        groupSvc.findById(groupId))) {

      final Map<AzureRegionName, Map<AzureNDSInstanceSize, Set<AzureInstanceFamily>>>
          blocklistedInstanceSizes = ndsaAzureBlocklistSettings.getAzureCloudProviderBlocklist();
      LOG.trace(
          "Ignoring capacity restrictions in overridden regions {} due to global blocklist for"
              + " groupId={}",
          blocklistedInstanceSizes,
          groupId);

      final Map<AzureRegionName, List<AzureNDSInstanceSize>> filteredBlocklist = new HashMap<>();

      for (Map.Entry<AzureRegionName, List<AzureNDSInstanceSize>> entry :
          unavailableInstanceSizesByRegion.entrySet()) {
        final AzureRegionName constrainedRegionName = entry.getKey();
        final Set<AzureNDSInstanceSize> constrainedInstanceSizes = new HashSet<>(entry.getValue());
        final Set<AzureNDSInstanceSize> blocklistedInstanceSizesForRegion =
            blocklistedInstanceSizes.getOrDefault(constrainedRegionName, Map.of()).keySet();
        // Regions we have overridden are allowed. We do not track the actual constrained size
        constrainedInstanceSizes.removeAll(blocklistedInstanceSizesForRegion);
        // Do not include empty set
        if (!constrainedInstanceSizes.isEmpty()) {
          filteredBlocklist.put(constrainedRegionName, constrainedInstanceSizes.stream().toList());
        }
      }
      return filteredBlocklist;
    }

    return unavailableInstanceSizesByRegion;
  }

  public Map<AzureRegionName, List<AzureNDSInstanceSize>> getAllUnavailableInstanceSizesByRegion() {
    return getUnavailableInstanceSizesByRegion();
  }

  public void deleteCapacityDenyListEntry(final Identifier pEntryIdentifier) {
    _azureCapacityDenylistDao.deleteCapacityDenylistEntry(pEntryIdentifier);
  }
}
