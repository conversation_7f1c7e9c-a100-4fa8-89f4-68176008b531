package com.xgen.cloud.nds.flex.runtime.svc;

import com.xgen.cloud.authz.resource._public.view.api.ApiAtlasTagView;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Status;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Type;
import com.xgen.cloud.cps.restore._public.ui.ApiAtlasServerlessBackupSnapshotView;
import com.xgen.cloud.cps.restore._public.ui.ServerlessBackupOptionsView;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionNameHelper;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex.runtime.res.api_2024_11_13.ApiAtlasFlexClusterResource;
import com.xgen.cloud.nds.project._public.model.FlexTenantMigrationState;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.serverless._public.model.ServerlessHardwareSpec;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.ServerlessAutoScaling;
import com.xgen.svc.mms.api.util.ApiAtlasFlexClusterDescriptionUtil;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasCloudProviderView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasServerlessInstanceDescriptionView;
import com.xgen.svc.mms.api.view.atlas.api_2024_11_13.ApiAtlasFlexClusterDescriptionCreate20241113View;
import com.xgen.svc.mms.api.view.atlas.api_2024_11_13.ApiAtlasFlexClusterDescriptionProviderSettingsCreate20241113View;
import com.xgen.svc.nds.model.TenantBackupSnapshot;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView.ClusterDescriptionViewBuilder;
import com.xgen.svc.nds.model.ui.ReplicationSpecView;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class LegacyServerlessApiShimToFlexSvc {
  private static final Logger LOG = LoggerFactory.getLogger(LegacyServerlessApiShimToFlexSvc.class);

  private final ApiAtlasFlexClusterResource _apiAtlasFlexClusterResource;
  private final ApiAtlasFlexClusterDescriptionUtil _apiAtlasFlexClusterDescriptionUtil;

  @Inject
  public LegacyServerlessApiShimToFlexSvc(
      final ApiAtlasFlexClusterResource pApiAtlasFlexClusterResource,
      final ApiAtlasFlexClusterDescriptionUtil pApiAtlasFlexClusterDescriptionUtil) {
    _apiAtlasFlexClusterResource = pApiAtlasFlexClusterResource;
    _apiAtlasFlexClusterDescriptionUtil = pApiAtlasFlexClusterDescriptionUtil;
  }

  public ClusterDescriptionView updateFlexClusterDescriptionViewFromServerless(
      final ApiAtlasServerlessInstanceDescriptionView pClusterDescriptionView,
      final ClusterDescriptionView pExistingView) {
    final ClusterDescriptionViewBuilder builder = pExistingView.toBuilder();

    final FlexTenantMigrationState.Builder newFlexMigrationState =
        pExistingView.getFlexTenantMigrationState().orElseThrow().toBuilder();

    if (pClusterDescriptionView.getServerlessBackupOptions() != null) {
      newFlexMigrationState.setVisibleContinuousBackupState(
          pClusterDescriptionView
              .getServerlessBackupOptions()
              .getServerlessContinuousBackupEnabled());
    }
    builder.flexTenantMigrationState(newFlexMigrationState.build());
    builder.terminationProtectionEnabled(pClusterDescriptionView.getTerminationProtectionEnabled());

    return builder.build();
  }

  public ClusterDescriptionView createFlexClusterDescriptionViewFromServerless(
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final Boolean pEnvelope,
      final String pName,
      final String pBackingCloudProvider,
      final String pRegionName,
      final boolean pIsTerminationProtectionEnabled,
      final boolean pContinuousBackupState)
      throws SvcException {
    _apiAtlasFlexClusterDescriptionUtil.verifyFlexIsSupported(pEnvelope);

    LOG.info(
        String.format(
            "Shimming serverless cluster %s in group %s to flex", pName, pNDSGroup.getGroupId()));

    final ApiAtlasFlexClusterDescriptionProviderSettingsCreate20241113View
        providerSettingsCreate20241113View =
            new ApiAtlasFlexClusterDescriptionProviderSettingsCreate20241113View(
                FlexInstanceSize.FLEX.getDiskSizeGB(),
                ApiAtlasCloudProviderView.findByName(pBackingCloudProvider).orElseThrow(),
                pRegionName);

    final ApiAtlasFlexClusterDescriptionCreate20241113View
        apiAtlasFlexClusterDescriptionCreateView =
            ApiAtlasFlexClusterDescriptionCreate20241113View.builder()
                .name(pName)
                .providerSettings(providerSettingsCreate20241113View)
                .terminationProtectionEnabled(pIsTerminationProtectionEnabled)
                .build();

    return _apiAtlasFlexClusterResource
        .buildClusterViewForCreate(
            apiAtlasFlexClusterDescriptionCreateView, pEnvelope, pNDSGroup, pGroup.getOrgId())
        .toBuilder()
        .flexTenantMigrationState(
            FlexTenantMigrationState.builder()
                .setIsTenantCreatedFromApi(true)
                .setTenantApiInstanceSize(ServerlessInstanceSize.SERVERLESS_V2)
                .setTenantApiCloudProvider(CloudProvider.SERVERLESS)
                .setVisibleAutoIndexingState(false)
                .setVisibleContinuousBackupState(pContinuousBackupState)
                .build())
        .build();
  }

  // TODO(CLOUDP-288414): need to return the continuous backup settings for the
  // cluster
  public ApiAtlasServerlessInstanceDescriptionView
      convertFlexClusterDescriptionViewToServerlessResponse(
          final ClusterDescriptionView pFlexClusterDescriptionView,
          final AppSettings pSettings,
          final List<ApiAtlasTagView> pTags) {

    final ReplicationSpecView flexReplicationSpec =
        pFlexClusterDescriptionView.getReplicationSpecList().get(0);

    final HardwareSpec serverlessHardwareSpec =
        new ServerlessHardwareSpec(
            flexReplicationSpec.getRegionConfigs().get(0).getElectableSpecs().getNodeCount(),
            ServerlessInstanceSize.SERVERLESS_V2,
            flexReplicationSpec
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs()
                .getBackingCloudProvider());

    final RegionName regionName =
        RegionNameHelper.findByNameOrElseThrow(
            flexReplicationSpec.getCloudProvider(),
            flexReplicationSpec.getRegionConfigs().get(0).getRegionName());

    final ServerlessAutoScaling pServerlessComputeAutoScaling =
        ServerlessAutoScaling.getDefaultAutoScaling();
    final RegionConfig regionConfig =
        new RegionConfig(
            regionName,
            CloudProvider.SERVERLESS,
            pServerlessComputeAutoScaling,
            null,
            flexReplicationSpec.getRegionConfigs().get(0).getPriority(),
            serverlessHardwareSpec,
            null);

    final ReplicationSpec.Builder replicationSpecBuilder = new ReplicationSpec.Builder();
    replicationSpecBuilder
        .setReplicationSpecId(flexReplicationSpec.getId())
        .setExternalId(flexReplicationSpec.getExternalId())
        .setZoneId(flexReplicationSpec.getZoneId())
        .setZoneName(flexReplicationSpec.getZoneName())
        .setNumShards(flexReplicationSpec.getNumShards())
        .setRegionConfigs(List.of(regionConfig));

    final ServerlessBackupOptionsView serverlessBackupOptions =
        new ServerlessBackupOptionsView(
            pFlexClusterDescriptionView.getFlexTenantMigrationState().isPresent()
                && pFlexClusterDescriptionView
                    .getFlexTenantMigrationState()
                    .orElseThrow()
                    .getVisibleContinuousBackupState()
                    .orElse(false));

    final ClusterDescriptionView serverlessClusterDescriptionView =
        pFlexClusterDescriptionView.toBuilder()
            .cloudProvider(CloudProvider.PROVIDER_SERVERLESS)
            .serverlessBackupOptionsView(serverlessBackupOptions)
            .terminationProtectionEnabled(
                pFlexClusterDescriptionView.isTerminationProtectionEnabled())
            .replicationSpecList(
                List.of(
                    new ReplicationSpecView(
                        replicationSpecBuilder.build(),
                        pFlexClusterDescriptionView.getDiskSizeGB(),
                        pFlexClusterDescriptionView.getCustomerProvidedDiskSizeGB())))
            .build();

    return new ApiAtlasServerlessInstanceDescriptionView(
        serverlessClusterDescriptionView, pSettings, pTags);
  }

  public ApiAtlasServerlessBackupSnapshotView
      createServerlessBackupSnapshotViewFromTenantSnapshotView(
          final TenantBackupSnapshot pFlexSnapshot, final AppSettings pSettings) {

    BackupSnapshot.Status status;
    switch (pFlexSnapshot.getState()) {
      case QUEUED, PENDING -> status = Status.QUEUED;
      case RUNNING -> status = Status.IN_PROGRESS;
      case COMPLETED -> status = Status.COMPLETED;
      default -> status = Status.FAILED;
    }

    return new ApiAtlasServerlessBackupSnapshotView(
        pSettings,
        pFlexSnapshot.getId(),
        pFlexSnapshot.getClusterName(),
        pFlexSnapshot.getStartedDate(),
        pFlexSnapshot.getScheduledDeletionDate(),
        pFlexSnapshot.getMongoDbVersion(),
        Type.SCHEDULED,
        status,
        pFlexSnapshot.getUsedDiskSpace());
  }
}
