package com.xgen.cloud.nds.datavalidation._public.model;

import com.xgen.cloud.common.dao.codec._public.encrypted.string.EncryptedString;
import com.xgen.cloud.common.model._public.annotation.GenEncryptField;
import com.xgen.cloud.common.model._public.annotation.WithGenEncryptField;
import jakarta.annotation.Nullable;
import java.util.Date;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

@WithGenEncryptField(DB = "nds", Collection = "config.dataValidationCollectionResults")
public class NamespaceValidationResult {
  private final String _db;
  private final String _collection;

  @GenEncryptField(FieldDefs.EXCEPTION_ENCOUNTERED)
  private final EncryptedString _exceptionEncountered;

  @GenEncryptField(FieldDefs.VALIDATE_OUTPUT)
  private final EncryptedString _validateOutput;

  private final Boolean _outputTruncated;

  // Used for correlating individual NamespaceValidationResults with a DataValidationRun
  private final ObjectId _validationRunId;

  private final Date _createdAt;

  @BsonCreator
  public NamespaceValidationResult(
      @BsonProperty(FieldDefs.DB) final String pDb,
      @BsonProperty(FieldDefs.COLLECTION) final String pCollection,
      @BsonProperty(FieldDefs.VALIDATE_OUTPUT) final EncryptedString pValidateOutput,
      @BsonProperty(FieldDefs.EXCEPTION_ENCOUNTERED) final EncryptedString pExceptionEncountered,
      @BsonProperty(FieldDefs.OUTPUT_TRUNCATED) final Boolean pOutputTruncated,
      @Nullable @BsonProperty(FieldDefs.VALIDATION_RUN_ID) final ObjectId pValidationRunId,
      @Nullable @BsonProperty(FieldDefs.CREATED_AT) final Date pCreatedAt) {
    _db = pDb;
    _collection = pCollection;
    _validateOutput = pValidateOutput;
    _exceptionEncountered = pExceptionEncountered;
    _outputTruncated = pOutputTruncated;
    _validationRunId = pValidationRunId;
    _createdAt = pCreatedAt;
  }

  // When db.collection.validate uses too much memory, the MongoD will get OOM-killed causing these
  // connection closed error
  public boolean hasResourceExhaustedRelatedError() {
    final Pattern clientErrorPattern =
        Pattern.compile(
            "(connection\\srefused)|(connection\\sclosed)|(connection\\sreset\\sby\\speer)",
            Pattern.CASE_INSENSITIVE);
    return Optional.ofNullable(_exceptionEncountered)
        .map(EncryptedString::getValue)
        .map(clientErrorPattern::matcher)
        .map(Matcher::find)
        .orElse(false);
  }

  public static class FieldDefs {
    public static final String DB = "db";
    public static final String COLLECTION = "collection";
    public static final String VALIDATE_OUTPUT = "validateOutput";
    public static final String EXCEPTION_ENCOUNTERED = "exceptionEncountered";
    public static final String OUTPUT_TRUNCATED = "outputTruncated";
    public static final String VALIDATION_RUN_ID = "validationRunId";
    public static final String CREATED_AT = "createdAt";
  }

  public String getDb() {
    return this._db;
  }

  public String getCollection() {
    return this._collection;
  }

  public EncryptedString getExceptionEncountered() {
    return this._exceptionEncountered;
  }

  public EncryptedString getValidateOutput() {
    return this._validateOutput;
  }

  public Boolean getOutputTruncated() {
    return this._outputTruncated;
  }

  public ObjectId getValidationRunId() {
    return this._validationRunId;
  }

  @BsonIgnore
  public Optional<ObjectId> getValidationRunIdAsOptional() {
    return Optional.ofNullable(_validationRunId);
  }

  public Date getCreatedAt() {
    return this._createdAt;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object o) {
    if (o == this) return true;
    if (!(o instanceof NamespaceValidationResult)) return false;
    final NamespaceValidationResult other = (NamespaceValidationResult) o;
    if (!other.canEqual((java.lang.Object) this)) return false;
    final java.lang.Object this$_db = this.getDb();
    final java.lang.Object other$_db = other.getDb();
    if (this$_db == null ? other$_db != null : !this$_db.equals(other$_db)) return false;
    final java.lang.Object this$_collection = this.getCollection();
    final java.lang.Object other$_collection = other.getCollection();
    if (this$_collection == null
        ? other$_collection != null
        : !this$_collection.equals(other$_collection)) return false;
    final java.lang.Object this$_exceptionEncountered = this.getExceptionEncountered();
    final java.lang.Object other$_exceptionEncountered = other.getExceptionEncountered();
    if (this$_exceptionEncountered == null
        ? other$_exceptionEncountered != null
        : !this$_exceptionEncountered.equals(other$_exceptionEncountered)) return false;
    final java.lang.Object this$_validateOutput = this.getValidateOutput();
    final java.lang.Object other$_validateOutput = other.getValidateOutput();
    if (this$_validateOutput == null
        ? other$_validateOutput != null
        : !this$_validateOutput.equals(other$_validateOutput)) return false;
    if (this.getOutputTruncated() == null
        ? other.getOutputTruncated() != null
        : !this.getOutputTruncated().equals(other.getOutputTruncated())) {
      return false;
    }
    if (this.getValidationRunId() == null
        ? other.getValidationRunId() != null
        : !this.getValidationRunId().equals(other.getValidationRunId())) {
      return false;
    }
    if (this.getCreatedAt() == null
        ? other.getCreatedAt() != null
        : !this.getCreatedAt().equals(other.getCreatedAt())) {
      return false;
    }
    return true;
  }

  protected boolean canEqual(final java.lang.Object other) {
    return other instanceof NamespaceValidationResult;
  }

  @java.lang.Override
  public int hashCode() {
    final int PRIME = 59;
    int result = 1;
    final java.lang.Object $_db = this.getDb();
    result = result * PRIME + ($_db == null ? 43 : $_db.hashCode());
    final java.lang.Object $_collection = this.getCollection();
    result = result * PRIME + ($_collection == null ? 43 : $_collection.hashCode());
    final java.lang.Object $_exceptionEncountered = this.getExceptionEncountered();
    result =
        result * PRIME + ($_exceptionEncountered == null ? 43 : $_exceptionEncountered.hashCode());
    final java.lang.Object $_validateOutput = this.getValidateOutput();
    result = result * PRIME + ($_validateOutput == null ? 43 : $_validateOutput.hashCode());
    final java.lang.Object $_outputTruncated = this.getOutputTruncated();
    result = result * PRIME + ($_outputTruncated == null ? 43 : $_outputTruncated.hashCode());
    final java.lang.Object $_validationRunId = this.getValidationRunId();
    result = result * PRIME + ($_validationRunId == null ? 43 : $_validationRunId.hashCode());
    final java.lang.Object $_createdAt = this.getCreatedAt();
    result = result * PRIME + ($_createdAt == null ? 43 : $_createdAt.hashCode());

    return result;
  }
}
