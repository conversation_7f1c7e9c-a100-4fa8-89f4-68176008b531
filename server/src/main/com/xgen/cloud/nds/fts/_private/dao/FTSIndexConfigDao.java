package com.xgen.cloud.nds.fts._private.dao;

import static com.mongodb.client.model.Filters.exists;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Projections.include;
import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.joinFields;
import static com.xgen.svc.core.dao.base.BaseDao.EACH;
import static com.xgen.svc.core.dao.base.BaseDao.ELEM_MATCH;
import static com.xgen.svc.core.dao.base.BaseDao.INC;
import static com.xgen.svc.core.dao.base.BaseDao.SET;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.ReadPreference;
import com.mongodb.WriteConcern;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import com.mongodb.client.model.ReturnDocument;
import com.mongodb.client.model.UpdateOneModel;
import com.mongodb.client.result.UpdateResult;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.fts._public.model.FTSCrashReport;
import com.xgen.cloud.nds.fts._public.model.FTSCrashReport.FTSCrashReportCodec;
import com.xgen.cloud.nds.fts._public.model.FTSCrashReportIncident;
import com.xgen.cloud.nds.fts._public.model.FTSCrashReportIncident.FTSCrashReportIncidentCodec;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSIndexConfig;
import com.xgen.cloud.nds.fts._public.model.FTSIndexConfig.FieldDefs;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostDetailedStatuses;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSIndexStatusMap;
import com.xgen.cloud.nds.fts._public.model.FTSReplicationStatus;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSynonymMappingDefinition.FTSSynonymMappingDefinitionCodec;
import com.xgen.cloud.nds.fts._public.model.FTSTypeSetDefinition;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndex;
import com.xgen.cloud.nds.fts._public.model.IndexedView;
import com.xgen.svc.core.dao.base.BaseDao;
import com.xgen.svc.core.dao.base.BaseTDao;
import com.xgen.svc.core.dao.base.MongoIndex;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Instant;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.BsonDateTime;
import org.bson.BsonDocument;
import org.bson.BsonElement;
import org.bson.BsonInt32;
import org.bson.BsonObjectId;
import org.bson.BsonString;
import org.bson.BsonValue;
import org.bson.RawBsonDocument;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.types.ObjectId;

@Singleton
public class FTSIndexConfigDao extends BaseTDao<FTSIndexConfig> {
  // in minutes before incident is eligible to be popped
  public static int INCIDENTS_LOOKUP_WINDOW = -10;
  private final FTSDaoAppSettings _ftsDaoAppSettings;
  private final AppSettings _appSettings;

  public static final Counter INDEX_CONFIG_STATS_CONCURRENT_MODIFICATION =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_fts_index_config_stats_concurrent_modification_total",
          "Record when a concurrent modification exception occurs updating host stats",
          "operation_type",
          "host_type");

  @Inject
  public FTSIndexConfigDao(
      final MongoSvc pMongoSvc,
      final FTSDaoAppSettings pFTSDaoAppSettings,
      final AppSettings pAppSettings) {
    super(pMongoSvc, FTSIndexConfig.DB_NAME, FTSIndexConfig.COLLECTION_NAME);
    _ftsDaoAppSettings = pFTSDaoAppSettings;
    _appSettings = pAppSettings;
  }

  @Override
  protected CodecRegistry initializeCodecRegistry() {
    final CodecRegistry codecRegistry = super.initializeCodecRegistry();

    return CodecRegistries.fromRegistries(
        CodecRegistries.fromCodecs(
            new FTSIndex.FTSIndexCodec(codecRegistry),
            new FTSIndexConfig.FTSIndexConfigCodec(codecRegistry),
            new FTSCrashReportCodec(codecRegistry),
            new FTSCrashReportIncidentCodec(codecRegistry),
            new FTSSynonymMappingDefinitionCodec(codecRegistry),
            new FTSTypeSetDefinition.FTSTypeSetDefinitionCodec(codecRegistry)),
        codecRegistry);
  }

  @Override
  public List<MongoIndex> getIndexes() {
    final List<MongoIndex> list = super.getIndexes();
    list.add(
        MongoIndex.newWithoutValidation()
            .key(joinFields(FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.GROUP_ID), 1)
            .key(
                joinFields(FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.CLUSTER_NAME), 1));
    list.add(
        MongoIndex.newWithoutValidation()
            .key(joinFields(FTSIndexConfig.FieldDefs.INDEXES, FTSIndex.FieldDefs.NAME), 1)
            .key(joinFields(FTSIndexConfig.FieldDefs.INDEXES, FTSIndex.FieldDefs.DATABASE), 1)
            .key(
                joinFields(FTSIndexConfig.FieldDefs.INDEXES, FTSIndex.FieldDefs.COLLECTION_UUID),
                1));
    list.add(
        MongoIndex.newWithoutValidation()
            .key(joinFields(FTSIndexConfig.FieldDefs.INDEXES, FTSIndex.FieldDefs.NAME), 1)
            .key(joinFields(FTSIndexConfig.FieldDefs.INDEXES, FTSIndex.FieldDefs.DATABASE), 1)
            .key(
                joinFields(
                    FTSIndexConfig.FieldDefs.INDEXES,
                    FTSIndex.FieldDefs.LAST_OBSERVED_COLLECTION_NAME),
                1));
    list.add(
        MongoIndex.newWithoutValidation()
            .key(joinFields(FTSIndexConfig.FieldDefs.INDEXES, FTSIndex.FieldDefs.INDEX_ID), 1));
    list.add(
        MongoIndex.newWithoutValidation()
            .key(joinFields(FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.GROUP_ID), 1)
            .key(joinFields(FieldDefs.LAST_CRASH_REPORTS, FTSCrashReport.FieldDefs.HOSTNAME), 1));

    return list;
  }

  public static BasicDBObject getId(final ObjectId pGroupId, final String pClusterName) {
    return new BasicDBObject()
        .append(FTSIndexConfig.FieldDefs.GROUP_ID, pGroupId)
        .append(FTSIndexConfig.FieldDefs.CLUSTER_NAME, pClusterName);
  }

  public Optional<FTSIndexConfig> find(final ObjectId pGroupId, final String pClusterName) {
    return Optional.ofNullable(
        getCollection()
            .find(
                new BasicDBObject()
                    .append(
                        joinFields(
                            FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.CLUSTER_NAME),
                        pClusterName)
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.GROUP_ID),
                        pGroupId))
            .first());
  }

  public List<FTSIndexConfig> findByGroup(
      final ObjectId pGroupId, final ReadPreference pReadPreference) {
    final ReadPreference readPreference =
        pReadPreference != null ? pReadPreference : getCollection().getReadPreference();
    return getCollection()
        .withReadPreference(readPreference)
        .find(
            new BasicDBObject()
                .append(
                    joinFields(FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.GROUP_ID),
                    pGroupId))
        .into(new ArrayList<>());
  }

  public Optional<FTSIndexConfig> findConfigWithCrashByGroupIdAndHostname(
      final ObjectId pGroupId, final String pHostname, final ReadPreference pReadPreference) {
    // caution: if there is a config at the hostname with no recorded crashes this will return empty
    return Optional.ofNullable(
        getCollection()
            .withReadPreference(pReadPreference)
            .find(
                new BasicDBObject()
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.GROUP_ID),
                        pGroupId)
                    .append(
                        joinFields(FieldDefs.LAST_CRASH_REPORTS, FTSCrashReport.FieldDefs.HOSTNAME),
                        pHostname))
            .first());
  }

  public Optional<FTSIndexConfig> findProjectedByTenant(
      final ObjectId pMTMGroupId, final String pMTMClusterName, final String pTenantId) {
    return Optional.ofNullable(
        getCollection()
            .find(
                new BasicDBObject()
                    .append(
                        joinFields(
                            FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.CLUSTER_NAME),
                        pMTMClusterName)
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.GROUP_ID),
                        pMTMGroupId))
            .projection(
                new BasicDBObject()
                    .append(FTSIndexConfig.FieldDefs.ID, 1)
                    .append(FTSIndexConfig.FieldDefs.CREATE_DATE, 1)
                    .append(FTSIndexConfig.FieldDefs.VERSION, 1)
                    .append(FTSIndexConfig.FieldDefs.ANALYZERS, 1)
                    .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, 1)
                    .append(joinFields(FTSIndexConfig.FieldDefs.TENANT_INDEXES, pTenantId), 1)
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_VECTOR_INDEXES, pTenantId), 1))
            .first());
  }

  public void create(final ObjectId pGroupId, final String pClusterName) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final FTSIndexConfig.Builder builder = new FTSIndexConfig.Builder();
    builder.setGroupId(pGroupId);
    builder.setClusterName(pClusterName);
    builder.setCreateDate(new Date());
    final FTSIndexConfig indexConfig = builder.build();
    insertMajority(indexConfig);
  }

  public boolean deleteFTSIndexConfig(final ObjectId pGroupId, final String pClusterName) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject().append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName));
    return deleteOneMajority(query).getDeletedCount() == 1;
  }

  public void setAnalyzers(
      final ObjectId pGroupId, final String pClusterName, final List<BasicDBObject> pAnalyzers) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject().append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject()
                    .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date())
                    .append(FTSIndexConfig.FieldDefs.ANALYZERS, pAnalyzers));

    updateOneMajority(query, update);
  }

  public int setIndexesAndAnalyzers(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<FTSIndex> pFTSIndexes,
      final List<BasicDBObject> pAnalyzers) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject().append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName));
    final Date createDate = new Date();
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject()
                    .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date())
                    .append(FTSIndexConfig.FieldDefs.ANALYZERS, pAnalyzers)
                    .append(
                        FTSIndexConfig.FieldDefs.INDEXES,
                        pFTSIndexes.stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.SEARCH)
                            .map(
                                index ->
                                    index
                                        .toDBObject()
                                        .append(FTSIndex.FieldDefs.CREATE_DATE, createDate)
                                        .append(
                                            FTSIndex.FieldDefs.DEFINITION_VERSION_CREATED_AT,
                                            createDate))
                            .collect(Collectors.toList()))
                    .append(
                        FTSIndexConfig.FieldDefs.VECTOR_INDEXES,
                        pFTSIndexes.stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.VECTOR_SEARCH)
                            .map(
                                index ->
                                    index
                                        .toDBObject()
                                        .append(FTSIndex.FieldDefs.CREATE_DATE, createDate)
                                        .append(
                                            FTSIndex.FieldDefs.DEFINITION_VERSION_CREATED_AT,
                                            createDate))
                            .collect(Collectors.toList())));
    Optional<FTSIndexConfig> ftsIndexConfig =
        findOneAndUpdateMajority(
            query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));
    return getNumIndexes(ftsIndexConfig);
  }

  /**
   * addFTSIndexes
   *
   * @return number of indexes that exist after adding each index in the specified list
   */
  public int addFTSIndexes(
      final ObjectId pGroupId, final String pClusterName, final List<FTSIndex> pFTSIndexes)
      throws SvcException {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                AND,
                List.of(
                    queryWithUniqueIndexList(FTSIndexConfig.FieldDefs.INDEXES, pFTSIndexes),
                    queryWithUniqueIndexList(FTSIndexConfig.FieldDefs.VECTOR_INDEXES, pFTSIndexes)))
            .append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName));
    final Date createDate = new Date();
    final BasicDBObject pushes = new BasicDBObject();
    for (final FTSIndex.Type type : FTSIndex.Type.values()) {
      final List<FTSIndex> indexes =
          pFTSIndexes.stream()
              .filter(ftsIndex -> ftsIndex.getActualType() == type)
              .collect(Collectors.toList());
      if (indexes.isEmpty()) {
        continue;
      }
      pushes.append(getIndexesFieldNameByType(type), pushIndexList(indexes, createDate));
    }
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date()))
            .append(UpdateOperators.PUSH, pushes);
    Optional<FTSIndexConfig> ftsIndexConfig =
        findOneAndUpdateMajority(
            query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));

    if (ftsIndexConfig.isEmpty()) {
      throwDuplicateIndexException(pFTSIndexes);
    }

    return getNumIndexes(ftsIndexConfig);
  }

  /**
   * addFTSIndex
   *
   * @return number of indexes that exist after adding the specified index
   */
  public int addFTSIndex(
      final ObjectId pGroupId, final String pClusterName, final FTSIndex pFTSIndex)
      throws SvcException {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                AND,
                List.of(
                    queryWithUniqueIndex(FTSIndexConfig.FieldDefs.INDEXES, pFTSIndex),
                    queryWithUniqueIndex(FTSIndexConfig.FieldDefs.VECTOR_INDEXES, pFTSIndex)))
            .append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName));
    final Date createDate = new Date();
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date()))
            .append(
                UpdateOperators.PUSH,
                new BasicDBObject()
                    .append(
                        getIndexesFieldNameByType(pFTSIndex.getActualType()),
                        pFTSIndex
                            .toDBObject()
                            .append(FTSIndex.FieldDefs.CREATE_DATE, createDate)
                            .append(FTSIndex.FieldDefs.DEFINITION_VERSION_CREATED_AT, createDate)));

    Optional<FTSIndexConfig> ftsIndexConfig =
        findOneAndUpdateMajority(
            query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));

    if (ftsIndexConfig.isEmpty()) {
      throwDuplicateIndexException(List.of(pFTSIndex));
    }

    return getNumIndexes(ftsIndexConfig);
  }

  /**
   * addTenantFTSIndex
   *
   * @return number of indexes that exist for the specified tenant after adding the specified index
   */
  public int addTenantFTSIndex(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final FTSIndex pFTSIndex)
      throws SvcException {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                AND,
                List.of(
                    queryWithUniqueIndex(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_INDEXES, pTenantId), pFTSIndex),
                    queryWithUniqueIndex(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_VECTOR_INDEXES, pTenantId),
                        pFTSIndex)))
            .append(FTSIndexConfig.FieldDefs.ID, getId(pMTMGroupId, pMTMClusterName));
    final Date createDate = new Date();
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date()))
            .append(
                UpdateOperators.PUSH,
                new BasicDBObject()
                    .append(
                        joinFields(
                            getTenantIndexesFieldNameByType(pFTSIndex.getActualType()), pTenantId),
                        pFTSIndex
                            .toDBObject()
                            .append(FTSIndex.FieldDefs.CREATE_DATE, createDate)
                            .append(FTSIndex.FieldDefs.DEFINITION_VERSION_CREATED_AT, createDate)));

    Optional<FTSIndexConfig> ftsIndexConfig =
        findOneAndUpdateMajority(
            query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));

    if (ftsIndexConfig.isEmpty()) {
      throwDuplicateIndexException(List.of(pFTSIndex));
    }

    return getNumTenantIndexes(ftsIndexConfig, pTenantId);
  }

  public int addTenantFTSIndexes(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final List<FTSIndex> pFTSIndexes)
      throws SvcException {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                AND,
                List.of(
                    queryWithUniqueIndexList(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_INDEXES, pTenantId),
                        pFTSIndexes),
                    queryWithUniqueIndexList(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_VECTOR_INDEXES, pTenantId),
                        pFTSIndexes)))
            .append(FTSIndexConfig.FieldDefs.ID, getId(pMTMGroupId, pMTMClusterName));
    final Date now = new Date();
    final BasicDBObject pushes = new BasicDBObject();
    for (final FTSIndex.Type type : FTSIndex.Type.values()) {
      final List<FTSIndex> indexes =
          pFTSIndexes.stream()
              .filter(ftsIndex -> ftsIndex.getActualType() == type)
              .collect(Collectors.toList());
      if (indexes.isEmpty()) {
        continue;
      }
      pushes.append(
          joinFields(getTenantIndexesFieldNameByType(type), pTenantId),
          pushIndexList(indexes, now));
    }
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, now))
            .append(UpdateOperators.PUSH, pushes);

    Optional<FTSIndexConfig> ftsIndexConfig =
        findOneAndUpdateMajority(
            query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));

    if (ftsIndexConfig.isEmpty()) {
      throwDuplicateIndexException(pFTSIndexes);
    }

    return getNumTenantIndexes(ftsIndexConfig, pTenantId);
  }

  /**
   * Because this exception message is user-visible, we want to capture as much information as
   * possible. That's why we try to provide the detailed error before falling back to the
   * less-detailed message.
   */
  private static void throwDuplicateIndexException(List<FTSIndex> pFTSIndexes) throws SvcException {
    if (pFTSIndexes.size() == 1) {
      var index = pFTSIndexes.get(0);
      throw new SvcException(
          NDSErrorCode.FTS_DUPLICATE_INDEX, index.getName(), index.getLastObservedCollectionName());
    }
    throw new SvcException(NDSErrorCode.FTS_DUPLICATE_INDEX_MULTIPLE_INSERT);
  }

  /**
   * setFTSIndexes
   *
   * @return number of indexes that exist after setting each index in the specified list
   */
  public int setFTSIndexes(
      final ObjectId pGroupId, final String pClusterName, final List<FTSIndex> pFTSIndexes) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject().append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject()
                    .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date())
                    .append(
                        FTSIndexConfig.FieldDefs.INDEXES,
                        pFTSIndexes.stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.SEARCH)
                            .map(
                                index ->
                                    index
                                        .toDBObject()
                                        .append(FTSIndex.FieldDefs.CREATE_DATE, new Date()))
                            .collect(Collectors.toList()))
                    .append(
                        FTSIndexConfig.FieldDefs.VECTOR_INDEXES,
                        pFTSIndexes.stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.VECTOR_SEARCH)
                            .map(
                                index ->
                                    index
                                        .toDBObject()
                                        .append(FTSIndex.FieldDefs.CREATE_DATE, new Date()))
                            .collect(Collectors.toList())));

    Optional<FTSIndexConfig> ftsIndexConfig =
        findOneAndUpdateMajority(
            query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));
    return getNumIndexes(ftsIndexConfig);
  }

  public void setFTSIndexesStatsAndDetailedStatuses(
      final ObjectId pGroupId,
      final String pClusterName,
      final Optional<Date> pExpectedLastUpdateDate,
      final Optional<Long> pExpectedVersion,
      final List<FTSIndex> pFTSIndexes)
      throws SvcException {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject().append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName));
    // update the indexes only if the lastUpdateDate and version match the expected value
    // (i.e. we didn't lose a race)
    if (pExpectedLastUpdateDate.isPresent()) {
      query.append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, pExpectedLastUpdateDate.get());
    }
    if (pExpectedVersion.isPresent()) {
      query.append(FieldDefs.VERSION, pExpectedVersion.get());
    }
    final Date now = new Date();

    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.SET,
                new BasicDBObject()
                    .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, now)
                    .append(
                        FTSIndexConfig.FieldDefs.INDEXES,
                        pFTSIndexes.stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.SEARCH)
                            .map(FTSIndex::toDBObject)
                            .collect(Collectors.toList()))
                    .append(
                        FTSIndexConfig.FieldDefs.VECTOR_INDEXES,
                        pFTSIndexes.stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.VECTOR_SEARCH)
                            .map(FTSIndex::toDBObject)
                            .collect(Collectors.toList())));

    // assume only stats are being updated so don't increment the version
    // only set the created date in each index if it's not already set
    final UpdateResult result = updateOneMajority(query, update);
    if (result.getModifiedCount() != 1) {
      // if the update didn't happen, we must have lost the race
      throw new SvcException(NDSErrorCode.CONCURRENT_MODIFICATION);
    }
  }

  /**
   * setTenantFTSIndexes
   *
   * @return number of indexes that exist for the specified tenant after setting each index in the
   *     specified list
   */
  public int setTenantFTSIndexesForNewTenant(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final List<FTSIndex> pFTSIndexes) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(FTSIndexConfig.FieldDefs.ID, getId(pMTMGroupId, pMTMClusterName));
    final Date now = new Date();
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject()
                    .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, now)
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_INDEXES, pTenantId),
                        pFTSIndexes.stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.SEARCH)
                            .map(
                                index ->
                                    index.toDBObject().append(FTSIndex.FieldDefs.CREATE_DATE, now))
                            .collect(Collectors.toList()))
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_VECTOR_INDEXES, pTenantId),
                        pFTSIndexes.stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.VECTOR_SEARCH)
                            .map(
                                index ->
                                    index.toDBObject().append(FTSIndex.FieldDefs.CREATE_DATE, now))
                            .collect(Collectors.toList())));

    Optional<FTSIndexConfig> ftsIndexConfig =
        findOneAndUpdateMajority(
            query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));

    return getNumTenantIndexes(ftsIndexConfig, pTenantId);
  }

  public boolean setTenantFTSIndexes(
      final ObjectId pGroupId,
      final String pClusterName,
      final Optional<Date> pExpectedLastUpdateDate,
      final Long pExpectedVersion,
      final Map<String, List<FTSIndex>> pAllTenantIndexes) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();

    // update the indexes only if the lastUpdateDate and version match the expected value
    // (i.e. we didn't lose a race)
    final BasicDBObject query =
        new BasicDBObject()
            .append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(FieldDefs.VERSION, pExpectedVersion);
    if (pExpectedLastUpdateDate.isPresent()) {
      query.append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, pExpectedLastUpdateDate.get());
    }
    final Map<String, List<FTSIndex>> allTenantSearchIndexes =
        pAllTenantIndexes.entrySet().stream()
            .map(
                entry ->
                    new AbstractMap.SimpleEntry<>(
                        entry.getKey(),
                        entry.getValue().stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.SEARCH)
                            .collect(Collectors.toList())))
            .filter(entry -> !entry.getValue().isEmpty())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    final Map<String, List<FTSIndex>> allTenantVectorSearchIndexes =
        pAllTenantIndexes.entrySet().stream()
            .map(
                entry ->
                    new AbstractMap.SimpleEntry<>(
                        entry.getKey(),
                        entry.getValue().stream()
                            .filter(index -> index.getActualType() == FTSIndex.Type.VECTOR_SEARCH)
                            .collect(Collectors.toList())))
            .filter(entry -> !entry.getValue().isEmpty())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    // assume only stats are being updated so don't increment the version
    // only set the created date in each index if it's not already set
    final Date now = new Date();
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.SET,
                new BasicDBObject()
                    .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, now)
                    .append(
                        FTSIndexConfig.FieldDefs.TENANT_INDEXES,
                        FTSIndexConfig.getDBObjectFromTenantIndexes(allTenantSearchIndexes))
                    .append(
                        FTSIndexConfig.FieldDefs.TENANT_VECTOR_INDEXES,
                        FTSIndexConfig.getDBObjectFromTenantIndexes(allTenantVectorSearchIndexes)));
    return updateOneMajority(query, update).getModifiedCount() >= 1;
  }

  public void setIndexDefinitionVersion(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final long definitionVersion) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateFTSIndexField(
        pGroupId, pClusterName, pIndexId, FTSIndex.FieldDefs.DEFINITION_VERSION, definitionVersion);
  }

  public void requestDeleteFTSIndex(
      final ObjectId pGroupId, final String pClusterName, final ObjectId pIndexId) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateFTSIndexField(
        pGroupId, pClusterName, pIndexId, FTSIndex.FieldDefs.DELETE_REQUESTED_DATE, new Date());
  }

  public void unSetRequestDeleteFTSIndex(
      final ObjectId pGroupId, final String pClusterName, final ObjectId pIndexId) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateFTSIndexField(
        pGroupId, pClusterName, pIndexId, FTSIndex.FieldDefs.DELETE_REQUESTED_DATE, null);
  }

  @VisibleForTesting
  public void setFTSIndexDefinitionCreatedAt(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final Date newDate) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateFTSIndexField(
        pGroupId,
        pClusterName,
        pIndexId,
        FTSIndex.FieldDefs.DEFINITION_VERSION_CREATED_AT,
        newDate);
  }

  public void requestDeleteTenantFTSIndex(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final ObjectId pIndexId) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateTenantFTSIndexField(
        pMTMGroupId,
        pMTMClusterName,
        pTenantId,
        pIndexId,
        FTSIndex.FieldDefs.DELETE_REQUESTED_DATE,
        new Date());
  }

  public void unSetRequestDeleteTenantFTSIndex(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final ObjectId pIndexId) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateTenantFTSIndexField(
        pMTMGroupId,
        pMTMClusterName,
        pTenantId,
        pIndexId,
        FTSIndex.FieldDefs.DELETE_REQUESTED_DATE,
        null);
  }

  /**
   * deleteFTSIndex
   *
   * @return number of indexes that exist after deleting the specified index
   */
  public int deleteFTSIndex(
      final ObjectId pGroupId, final String pClusterName, final ObjectId pIndexId) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject().append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date()))
            .append(
                UpdateOperators.PULL,
                new BasicDBObject()
                    .append(
                        FTSIndexConfig.FieldDefs.INDEXES,
                        new BasicDBObject().append(FTSIndex.FieldDefs.INDEX_ID, pIndexId))
                    .append(
                        FTSIndexConfig.FieldDefs.VECTOR_INDEXES,
                        new BasicDBObject().append(FTSIndex.FieldDefs.INDEX_ID, pIndexId)));

    Optional<FTSIndexConfig> ftsIndexConfig =
        findOneAndUpdateMajority(
            query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));

    return getNumIndexes(ftsIndexConfig);
  }

  /**
   * deleteTenantFTSIndex
   *
   * @return number of indexes that exist for the specified tenant after deleting the specified
   *     index
   */
  public int deleteTenantFTSIndex(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final ObjectId pIndexId) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(FTSIndexConfig.FieldDefs.ID, getId(pMTMGroupId, pMTMClusterName));

    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date()))
            .append(
                UpdateOperators.PULL,
                new BasicDBObject()
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_INDEXES, pTenantId),
                        new BasicDBObject().append(FTSIndex.FieldDefs.INDEX_ID, pIndexId))
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_VECTOR_INDEXES, pTenantId),
                        new BasicDBObject().append(FTSIndex.FieldDefs.INDEX_ID, pIndexId)));

    Optional<FTSIndexConfig> ftsIndexConfig =
        findOneAndUpdateMajority(
            query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));
    deleteTenantRecordIfEmpty(pMTMGroupId, pMTMClusterName, pTenantId);

    return getNumTenantIndexes(ftsIndexConfig, pTenantId);
  }

  public void deleteTenantFTSIndexes(
      final ObjectId pMTMGroupId, final String pMTMClusterName, final String pTenantId) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(FTSIndexConfig.FieldDefs.ID, getId(pMTMGroupId, pMTMClusterName));

    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.INC,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
            .append(
                UpdateOperators.SET,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date()))
            .append(
                UpdateOperators.UNSET,
                new BasicDBObject()
                    .append(joinFields(FTSIndexConfig.FieldDefs.TENANT_INDEXES, pTenantId), "")
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.TENANT_VECTOR_INDEXES, pTenantId), ""));

    updateOneMajority(query, update);
  }

  private void deleteTenantRecordIfEmpty(
      final ObjectId pMTMGroupId, final String pMTMClusterName, final String pTenantId) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final List<UpdateOneModel<FTSIndexConfig>> updates =
        Arrays.stream(FTSIndex.Type.values())
            .map(
                indexType -> {
                  final BasicDBObject query =
                      new BasicDBObject()
                          .append(FTSIndexConfig.FieldDefs.ID, getId(pMTMGroupId, pMTMClusterName))
                          .append(
                              joinFields(getTenantIndexesFieldNameByType(indexType), pTenantId),
                              new BasicDBObject(BaseDao.SIZE, 0));

                  final BasicDBObject update =
                      new BasicDBObject()
                          .append(
                              UpdateOperators.UNSET,
                              new BasicDBObject()
                                  .append(
                                      joinFields(
                                          getTenantIndexesFieldNameByType(indexType), pTenantId),
                                      ""));
                  return new UpdateOneModel<FTSIndexConfig>(query, update);
                })
            .collect(Collectors.toList());
    getCollection().withWriteConcern(WriteConcern.MAJORITY).bulkWrite(updates);
  }

  public Optional<FTSIndexConfig> updateUserDefinedFTSIndexFields(
      final ObjectId pGroupId, final String pClusterName, final FTSIndex pFTSIndex) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(
                joinFields(
                    getIndexesFieldNameByType(pFTSIndex.getActualType()),
                    FTSIndex.FieldDefs.INDEX_ID),
                pFTSIndex.getIndexId());
    final BasicDBObject update =
        getIndexUpdateDBObject(pFTSIndex, getIndexesFieldNameByType(pFTSIndex.getActualType()));
    return findOneAndUpdateReplicaSafe(
        query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));
  }

  public Optional<FTSIndexConfig> updateTenantUserDefinedFTSIndexFields(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final FTSIndex pFTSIndex) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(FTSIndexConfig.FieldDefs.ID, getId(pMTMGroupId, pMTMClusterName))
            .append(
                joinFields(
                    getTenantIndexesFieldNameByType(pFTSIndex.getActualType()),
                    pTenantId,
                    FTSIndex.FieldDefs.INDEX_ID),
                pFTSIndex.getIndexId());
    final BasicDBObject update =
        getIndexUpdateDBObject(
            pFTSIndex,
            joinFields(getTenantIndexesFieldNameByType(pFTSIndex.getActualType()), pTenantId));
    return findOneAndUpdateReplicaSafe(
        query, update, new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));
  }

  private static BasicDBObject getIndexUpdateDBObject(
      final FTSIndex pFTSIndex, final String pField) {
    switch (pFTSIndex.getActualType()) {
      case SEARCH:
        return getTextSearchIndexUpdateDBObject(pFTSIndex.toTextSearchIndex(), pField);
      case VECTOR_SEARCH:
        return getVectorSearchIndexUpdateDBObject(pFTSIndex.toVectorSearchIndex(), pField);
      default:
        throw new IllegalArgumentException("Unknown index type: " + pFTSIndex.getActualType());
    }
  }

  private static BasicDBObject getIndexUpdateBaseDBObject(
      final FTSIndex pFTSIndex, final String pField) {
    return new BasicDBObject()
        .append(
            UpdateOperators.INC,
            new BasicDBObject()
                .append(FTSIndexConfig.FieldDefs.VERSION, 1)
                .append(joinFields(pField, "$", FTSIndex.FieldDefs.DEFINITION_VERSION), 1))
        .append(
            UpdateOperators.SET,
            new BasicDBObject()
                .append(
                    joinFields(pField, "$", FTSIndex.FieldDefs.TYPE),
                    pFTSIndex.getType().map(FTSIndex.Type::getStringValue).orElse(null))
                .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date())
                .append(
                    joinFields(pField, "$", FTSIndex.FieldDefs.DEFINITION_VERSION_CREATED_AT),
                    new Date())
                .append(
                    joinFields(pField, "$", FTSIndex.FieldDefs.NUM_PARTITIONS),
                    pFTSIndex.getNumPartitions().orElse(null)));
  }

  private static BasicDBObject getTextSearchIndexUpdateDBObject(
      final FTSSearchIndex pFTSIndex, final String pField) {
    BasicDBObject updateObject = getIndexUpdateBaseDBObject(pFTSIndex, pField);
    ((BasicDBObject) updateObject.get(UpdateOperators.SET))
        .append(
            joinFields(pField, "$", FTSSearchIndex.FieldDefs.ANALYZER),
            pFTSIndex.getAnalyzer().orElse(null))
        .append(
            joinFields(pField, "$", FTSSearchIndex.FieldDefs.ANALYZERS),
            pFTSIndex.getAnalyzers().orElse(null))
        .append(
            joinFields(pField, "$", FTSSearchIndex.FieldDefs.SEARCH_ANALYZER),
            pFTSIndex.getSearchAnalyzer().orElse(null))
        .append(
            joinFields(pField, "$", FTSSearchIndex.FieldDefs.MAPPINGS),
            pFTSIndex.getMappings().orElse(null))
        .append(
            joinFields(pField, "$", FTSSearchIndex.FieldDefs.SYNONYMS),
            pFTSIndex.getSynonyms().orElse(null))
        .append(
            joinFields(pField, "$", FTSSearchIndex.FieldDefs.STORED_SOURCE),
            pFTSIndex.getStoredSource().map(FTSSearchIndex.StoredSource::toDBOBject).orElse(null))
        .append(
            joinFields(pField, "$", FTSSearchIndex.FieldDefs.INDEX_FEATURE_VERSION),
            pFTSIndex.getIndexFeatureVersion());
    return updateObject;
  }

  private static BasicDBObject getVectorSearchIndexUpdateDBObject(
      final FTSVectorSearchIndex pFTSIndex, final String pField) {
    BasicDBObject updateObject = getIndexUpdateBaseDBObject(pFTSIndex, pField);
    ((BasicDBObject) updateObject.get(UpdateOperators.SET))
        .append(
            joinFields(pField, "$", FTSVectorSearchIndex.FieldDefs.FIELDS),
            pFTSIndex.getFields().orElse(null))
        .append(
            joinFields(pField, "$", FTSVectorSearchIndex.FieldDefs.INDEX_FEATURE_VERSION),
            pFTSIndex.getIndexFeatureVersion());
    return updateObject;
  }

  public void updateFTSIndexLastObservedCollectionName(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final String pCollectionName,
      final Long pOptime) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateFTSIndexField(
        pGroupId,
        pClusterName,
        pIndexId,
        FTSIndex.FieldDefs.LAST_OBSERVED_COLLECTION_NAME,
        pCollectionName,
        Optional.of(pOptime));
  }

  // Use version to enforce optimistic concurrency control when adding collection uuid
  public void updateFTSIndexCollectionUUID(
      final FTSIndexConfig pFTSIndexConfig,
      final FTSIndex pFTSIndex,
      final UUID pCollectionUUID,
      final Long pOptime) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                FTSIndexConfig.FieldDefs.ID,
                getId(pFTSIndexConfig.getGroupId(), pFTSIndexConfig.getClusterName()))
            .append(FTSIndexConfig.FieldDefs.VERSION, pFTSIndexConfig.getVersion());
    updateFTSIndexField(
        query,
        pFTSIndex.getIndexId(),
        FTSIndex.FieldDefs.COLLECTION_UUID,
        pCollectionUUID,
        Optional.of(pOptime));
  }

  public void updateIndexedView(
      final FTSIndexConfig indexConfig,
      final Optional<String> tenantId,
      final FTSIndex ftsIndex,
      final IndexedView indexedView) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();

    // the possibly-dotted syntax that identifies the array of indexes containing our index
    var indexFieldName =
        tenantId
            .map(id -> joinFields(getTenantIndexesFieldNameByType(ftsIndex.getActualType()), id))
            .orElseGet(() -> getIndexesFieldNameByType(ftsIndex.getActualType()));

    // the dotted syntax for queries (without "$") that identifies the index ID field
    var indexIdHierarchy = joinFields(indexFieldName, FTSIndex.FieldDefs.INDEX_ID);

    // find the config document
    final BsonDocument query =
        doc(
            elem(FieldDefs.ID, configId(indexConfig.getGroupId(), indexConfig.getClusterName())),
            elem(indexIdHierarchy, new BsonObjectId(ftsIndex.getIndexId())));

    // update fields on the top-level config object
    var bsonNow = new BsonDateTime(Instant.now().getEpochSecond());
    var configVersionIncrement = elem(FieldDefs.VERSION, new BsonInt32(1));
    var configDateSet = elem(FieldDefs.LAST_UPDATE_DATE, bsonNow);

    // update fields on the nested index using $ to identify the nested doc
    Function<String, String> indexPrefix = field -> joinFields(indexFieldName, "$", field);
    var indexVersionIncrement =
        elem(indexPrefix.apply(FTSIndex.FieldDefs.DEFINITION_VERSION), new BsonInt32(1));
    var indexDefinitionDateSet =
        elem(indexPrefix.apply(FTSIndex.FieldDefs.DEFINITION_VERSION_CREATED_AT), bsonNow);
    var indexedViewSet =
        elem(indexPrefix.apply(FTSIndex.FieldDefs.VIEW), indexedView.toBsonDocument());

    var updateDoc =
        doc(
            elem(INC, doc(configVersionIncrement, indexVersionIncrement)),
            elem(SET, doc(configDateSet, indexDefinitionDateSet, indexedViewSet)));

    updateOneMajority(query, updateDoc);
  }

  private static BsonDocument doc(BsonElement... elements) {
    return new BsonDocument(Arrays.asList(elements));
  }

  private static BsonDocument configId(ObjectId groupId, String clusterName) {
    return new BsonDocument(
        List.of(
            new BsonElement(FTSIndexConfig.FieldDefs.GROUP_ID, new BsonObjectId(groupId)),
            new BsonElement(FTSIndexConfig.FieldDefs.CLUSTER_NAME, new BsonString(clusterName))));
  }

  private static BsonElement elem(String key, BsonValue value) {
    return new BsonElement(key, value);
  }

  public void updateFTSIndexStatus(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final FTSIndex.Status pStatus) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateFTSIndexField(
        pGroupId, pClusterName, pIndexId, FTSIndex.FieldDefs.STATUS, pStatus.name());
  }

  public void updateTenantFTSIndexStatus(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final ObjectId pIndexId,
      final FTSIndex.Status pStatus) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateTenantFTSIndexField(
        pMTMGroupId,
        pMTMClusterName,
        pTenantId,
        pIndexId,
        FTSIndex.FieldDefs.STATUS,
        pStatus.name());
  }

  public void updateTenantFTSIndexLastObservedCollectionName(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final ObjectId pIndexId,
      final String pCollectionName,
      final Long pOptime) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateTenantFTSIndexField(
        pMTMGroupId,
        pMTMClusterName,
        pTenantId,
        pIndexId,
        FTSIndex.FieldDefs.LAST_OBSERVED_COLLECTION_NAME,
        pCollectionName,
        Optional.of(pOptime));
  }

  // Use version to enforce optimistic concurrency control when adding collection uuid
  public void updateTenantFTSIndexCollectionUUID(
      final FTSIndexConfig pFTSIndexConfig,
      final String pTenantId,
      final FTSIndex pFTSIndex,
      final UUID pCollectionUUID,
      final Long pOptime) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                FTSIndexConfig.FieldDefs.ID,
                getId(pFTSIndexConfig.getGroupId(), pFTSIndexConfig.getClusterName()))
            .append(FTSIndexConfig.FieldDefs.VERSION, pFTSIndexConfig.getVersion());
    updateTenantFTSIndexField(
        query,
        pTenantId,
        pFTSIndex.getIndexId(),
        FTSIndex.FieldDefs.COLLECTION_UUID,
        pCollectionUUID,
        Optional.of(pOptime));
  }

  // Used only for testing, to simulate indexes created before with an older IndexFeatureVersion
  // field.
  @VisibleForTesting
  public void updateSearchIndexFeatureVersionField(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final int pIndexFeatureVersion) {
    updateFTSIndexField(
        pGroupId,
        pClusterName,
        pIndexId,
        FTSSearchIndex.FieldDefs.INDEX_FEATURE_VERSION,
        pIndexFeatureVersion);
  }

  private <T> void updateFTSIndexField(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final String fieldName,
      final T fieldValue) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateFTSIndexField(pGroupId, pClusterName, pIndexId, fieldName, fieldValue, Optional.empty());
  }

  private <T> void updateFTSIndexField(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final String fieldName,
      final T fieldValue,
      final Optional<Long> pOptimeOpt) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject().append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName));
    updateFTSIndexField(query, pIndexId, fieldName, fieldValue, pOptimeOpt);
  }

  private <T> void updateFTSIndexField(
      final BasicDBObject pQuery,
      final ObjectId pIndexId,
      final String fieldName,
      final T fieldValue,
      final Optional<Long> pOptimeOpt) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final List<UpdateOneModel<FTSIndexConfig>> updates =
        Arrays.stream(FTSIndex.Type.values())
            .map(
                indexType -> {
                  final BasicDBObject query =
                      new BasicDBObject()
                          .append(
                              AND,
                              List.of(
                                  pQuery,
                                  new BasicDBObject()
                                      .append(
                                          joinFields(
                                              getIndexesFieldNameByType(indexType),
                                              FTSIndex.FieldDefs.INDEX_ID),
                                          pIndexId)));
                  final BasicDBObject settingFields =
                      new BasicDBObject()
                          .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date())
                          .append(
                              joinFields(getIndexesFieldNameByType(indexType), "$", fieldName),
                              fieldValue);
                  pOptimeOpt.ifPresent(
                      aLong ->
                          settingFields.append(
                              joinFields(
                                  getIndexesFieldNameByType(indexType),
                                  "$",
                                  FTSIndex.FieldDefs.OPTIME),
                              aLong));
                  final BasicDBObject update =
                      new BasicDBObject()
                          .append(
                              UpdateOperators.INC,
                              new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
                          .append(UpdateOperators.SET, settingFields);
                  return new UpdateOneModel<FTSIndexConfig>(query, update);
                })
            .collect(Collectors.toList());
    getCollection().withWriteConcern(WriteConcern.MAJORITY).bulkWrite(updates);
  }

  // Used only for testing, to simulate indexes created before the new IndexFeatureVersion field.
  @VisibleForTesting
  public void updateTenantFTSIndexFeatureVersion(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final ObjectId pIndexId,
      final int pIndexFeatureVersion) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateTenantFTSIndexField(
        pMTMGroupId,
        pMTMClusterName,
        pTenantId,
        pIndexId,
        FTSSearchIndex.FieldDefs.INDEX_FEATURE_VERSION,
        pIndexFeatureVersion,
        Optional.empty());
  }

  private <T> void updateTenantFTSIndexField(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final ObjectId pIndexId,
      final String fieldName,
      final T fieldValue) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateTenantFTSIndexField(
        pMTMGroupId, pMTMClusterName, pTenantId, pIndexId, fieldName, fieldValue, Optional.empty());
  }

  private <T> void updateTenantFTSIndexField(
      final ObjectId pMTMGroupId,
      final String pMTMClusterName,
      final String pTenantId,
      final ObjectId pIndexId,
      final String fieldName,
      final T fieldValue,
      final Optional<Long> pOptimeOpt) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject query =
        new BasicDBObject()
            .append(FTSIndexConfig.FieldDefs.ID, getId(pMTMGroupId, pMTMClusterName));
    updateTenantFTSIndexField(query, pTenantId, pIndexId, fieldName, fieldValue, pOptimeOpt);
  }

  private <T> void updateTenantFTSIndexField(
      final BasicDBObject pQuery,
      final String pTenantId,
      final ObjectId pIndexId,
      final String fieldName,
      final T fieldValue,
      final Optional<Long> pOptimeOpt) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final List<UpdateOneModel<FTSIndexConfig>> updates =
        Arrays.stream(FTSIndex.Type.values())
            .map(
                indexType -> {
                  final BasicDBObject query =
                      new BasicDBObject()
                          .append(
                              AND,
                              List.of(
                                  pQuery,
                                  new BasicDBObject()
                                      .append(
                                          joinFields(
                                              getTenantIndexesFieldNameByType(indexType),
                                              pTenantId,
                                              FTSIndex.FieldDefs.INDEX_ID),
                                          pIndexId)));
                  final BasicDBObject settingFields =
                      new BasicDBObject()
                          .append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date())
                          .append(
                              joinFields(
                                  getTenantIndexesFieldNameByType(indexType),
                                  pTenantId,
                                  "$",
                                  fieldName),
                              fieldValue);
                  pOptimeOpt.ifPresent(
                      aLong ->
                          settingFields.append(
                              joinFields(
                                  getTenantIndexesFieldNameByType(indexType),
                                  pTenantId,
                                  "$",
                                  FTSIndex.FieldDefs.OPTIME),
                              aLong));
                  final BasicDBObject update =
                      new BasicDBObject()
                          .append(
                              UpdateOperators.INC,
                              new BasicDBObject().append(FTSIndexConfig.FieldDefs.VERSION, 1))
                          .append(UpdateOperators.SET, settingFields);
                  return new UpdateOneModel<FTSIndexConfig>(query, update);
                })
            .collect(Collectors.toList());
    getCollection().withWriteConcern(WriteConcern.MAJORITY).bulkWrite(updates);
  }

  private FTSIndexStatusMap<FTSIndexHostStat> getUpdatedFTSIndexHostStat(
      FTSIndex storedIndex, FTSIndex index, String pHostname, Date updatedDate) {
    final FTSIndexHostStat hostStat =
        index
            .getHostStat(pHostname)
            .map(FTSIndexHostStat::toBuilder)
            .map(b -> b.lastUpdateDate(updatedDate))
            .map(FTSIndexHostStat.FTSIndexHostStatBuilder::build)
            .orElse(null);
    final FTSIndexStatusMap<FTSIndexHostStat> stats = new FTSIndexStatusMap<>();
    storedIndex.getStats().ifPresent(stats::putAll);
    stats.put(pHostname, hostStat);
    return stats;
  }

  /**
   * Updates stored indexes with current stats and detailed statuses for a given hostname, handling
   * concurrent modifications by retrying with a reloaded index config. Returns a list of updated
   * indexes where matches are found and unchanged stored indexes otherwise.
   */
  public List<FTSIndex> setStatsDefinitions(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final FTSIndexConfig pInitialConfig,
      final List<FTSIndex> pIndexes,
      final boolean skipDetailedStatuses)
      throws SvcException {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    // We can assume that the caller is passing in an update to date FTSIndexConfig, optimistically,
    // because if we are wrong the concurrency lock on lastUpdateDate will cause a reload
    Optional<FTSIndexConfig> currentConfig = Optional.of(pInitialConfig);

    int retries = 3;
    while (retries > 0) {
      final Date updatedDate = new Date();
      final List<FTSIndex> storedIndexes =
          currentConfig.map(FTSIndexConfig::getIndexesForAllTypes).orElse(null);
      if (storedIndexes == null) {
        return Collections.emptyList();
      }
      Map<ObjectId, FTSIndex> indexMap =
          pIndexes.stream().collect(Collectors.toMap(FTSIndex::getIndexId, index -> index));
      final List<FTSIndex> updatedIndexes =
          storedIndexes.stream()
              .map(
                  storedIndex -> {
                    if (indexMap.containsKey(storedIndex.getIndexId())) {
                      FTSIndex index = indexMap.get(storedIndex.getIndexId());
                      final FTSIndexStatusMap<FTSIndexHostStat> stats =
                          getUpdatedFTSIndexHostStat(storedIndex, index, pHostname, updatedDate);

                      final FTSIndex.Builder<?> builder = storedIndex.copy().setStats(stats);

                      final FTSIndexStatusMap<FTSIndexHostDetailedStatuses> detailedStatuses =
                          new FTSIndexStatusMap<>();

                      index
                          .getHostDetailedStatuses(pHostname)
                          .ifPresent(
                              hostDetailedStatuses -> {
                                if (!skipDetailedStatuses) {
                                  storedIndex
                                      .getDetailedStatuses()
                                      .ifPresent(detailedStatuses::putAll);
                                  detailedStatuses.put(pHostname, hostDetailedStatuses);
                                }
                                builder.setDetailedStatuses(detailedStatuses);
                              });

                      return builder.build();
                    } else {
                      return storedIndex;
                    }
                  })
              .collect(Collectors.toList());
      try {
        setFTSIndexesStatsAndDetailedStatuses(
            pGroupId,
            pClusterName,
            currentConfig.flatMap(FTSIndexConfig::getLastUpdateDate),
            currentConfig.map(FTSIndexConfig::getVersion),
            updatedIndexes);
        return updatedIndexes;
      } catch (final SvcException e) {
        // retry
        retries--;
        NDSPromMetricsSvc.incrementCounter(
            INDEX_CONFIG_STATS_CONCURRENT_MODIFICATION, "setHostStats", "dedicated");

        try {
          // Spread out conf calls to avoid conflicts
          Thread.sleep(RandomUtils.nextInt(3_000));
        } catch (final InterruptedException ex) {
          Thread.currentThread().interrupt();
          throw new SvcException(NDSErrorCode.CONCURRENT_MODIFICATION);
        }
        // Query the config when the thread wakes up.
        // This is a race condition as a result don't query the config before the thread goes to
        // sleep because there's a higher chance that other threads have changed the same data.
        // The real fix to this issue is to use DB transactions.
        currentConfig = find(pGroupId, pClusterName);
      }
    }
    throw new SvcException(NDSErrorCode.CONCURRENT_MODIFICATION);
  }

  // For testing purpose: manually set stats for given index id.
  @VisibleForTesting
  public void setStatsDefinition(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final String pHostname,
      final FTSIndexHostStat pFTSIndexHostStat) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateStats(
        pGroupId,
        pClusterName,
        Optional.empty(),
        pIndexId,
        pHostname,
        FTSIndex.FieldDefs.STATS,
        Optional.empty(),
        pFTSIndexHostStat.toDBObject());
  }

  /**
   * Updates stored tenant indexes with current stats and detailed statuses for a given MTM
   * hostname. Returns a list of updated indexes where matches are found and unchanged stored
   * indexes otherwise.
   */
  public List<FTSIndex> getStatsUpdatedTenantIndexes(
      final List<FTSIndex> pStoredIndexes, final List<FTSIndex> pIndexes, final String pHostname) {
    Preconditions.checkArgument(pStoredIndexes != null, "pStoredIndexes must not be null");
    final Date updatedDate = new Date();
    Map<ObjectId, FTSIndex> indexMap =
        pIndexes.stream().collect(Collectors.toMap(FTSIndex::getIndexId, index -> index));
    return pStoredIndexes.stream()
        .map(
            storedIndex -> {
              if (indexMap.containsKey(storedIndex.getIndexId())) {
                FTSIndex index = indexMap.get(storedIndex.getIndexId());
                final FTSIndexStatusMap<FTSIndexHostStat> stats =
                    getUpdatedFTSIndexHostStat(storedIndex, index, pHostname, updatedDate);

                final FTSIndex.Builder<?> builder = storedIndex.copy().setStats(stats);
                final FTSIndexStatusMap<FTSIndexHostDetailedStatuses> detailedStatuses =
                    new FTSIndexStatusMap<>();
                storedIndex.getDetailedStatuses().ifPresent(detailedStatuses::putAll);
                index
                    .getHostDetailedStatuses(pHostname)
                    .ifPresent(
                        hostDetailedStatuses -> {
                          detailedStatuses.put(pHostname, hostDetailedStatuses);
                          builder.setDetailedStatuses(detailedStatuses);
                        });
                return builder.build();
              } else {
                return storedIndex;
              }
            })
        .collect(Collectors.toList());
  }

  public int getFTSIndexConfigSizeBytes(final ObjectId pGroupId, final String pClusterName) {
    final MongoCollection<RawBsonDocument> mongoCollection =
        getDatabase(getMongo(), FTSIndexConfig.DB_NAME)
            .getCollection(FTSIndexConfig.COLLECTION_NAME, RawBsonDocument.class);
    final MongoCursor<RawBsonDocument> cursor =
        mongoCollection
            .find(
                new BasicDBObject()
                    .append(
                        joinFields(
                            FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.CLUSTER_NAME),
                        pClusterName)
                    .append(
                        joinFields(FTSIndexConfig.FieldDefs.ID, FTSIndexConfig.FieldDefs.GROUP_ID),
                        pGroupId))
            .iterator();
    final RawBsonDocument doc = cursor.next();

    return doc.getByteBuffer().remaining();
  }

  // For testing purpose: manually set detailed status
  @VisibleForTesting
  public <T> void setDetailedStatusesDefinition(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final String pHostname,
      final T pValue) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateStats(
        pGroupId,
        pClusterName,
        Optional.empty(),
        pIndexId,
        pHostname,
        FTSIndex.FieldDefs.DETAILED_STATUSES,
        Optional.empty(),
        pValue);
  }

  // For testing purpose: manually set detailed status
  @VisibleForTesting
  public <T> void setDetailedStatusesDefinitionTenant(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pIndexId,
      final String pTenantId,
      final String pHostname,
      final T pValue) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateStats(
        pGroupId,
        pClusterName,
        Optional.of(pTenantId),
        pIndexId,
        pHostname,
        FTSIndex.FieldDefs.DETAILED_STATUSES,
        Optional.empty(),
        pValue);
  }

  private <T> void updateStats(
      final ObjectId pGroupId,
      final String pClusterName,
      Optional<String> tenantId,
      final ObjectId pIndexId,
      final String pHostname,
      final String statsFieldName,
      Optional<String> statsField,
      final T pValue) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    Preconditions.checkState(
        statsFieldName.equals(FTSIndex.FieldDefs.STATS)
            || statsFieldName.equals(FTSIndex.FieldDefs.DETAILED_STATUSES));
    final List<UpdateOneModel<FTSIndexConfig>> updates =
        Arrays.stream(FTSIndex.Type.values())
            .map(
                indexType -> {
                  String fieldPrefix =
                      tenantId.isEmpty()
                          ? getIndexesFieldNameByType(indexType)
                          : joinFields(getTenantIndexesFieldNameByType(indexType), tenantId.get());
                  final BasicDBObject query =
                      new BasicDBObject()
                          .append(FTSIndexConfig.FieldDefs.ID, getId(pGroupId, pClusterName))
                          .append(joinFields(fieldPrefix, FTSIndex.FieldDefs.INDEX_ID), pIndexId);
                  final BasicDBObject update =
                      new BasicDBObject(
                          SET,
                          new BasicDBObject(
                              joinFields(
                                  fieldPrefix,
                                  "$",
                                  statsFieldName,
                                  statsField.isEmpty()
                                      ? pHostname
                                      : joinFields(pHostname, statsField.get())),
                              pValue));
                  return new UpdateOneModel<FTSIndexConfig>(query, update);
                })
            .collect(Collectors.toList());
    getCollection().withWriteConcern(WriteConcern.MAJORITY).bulkWrite(updates);
  }

  // For testing purpose: manually set lastUpdateDate so that stats can be successfully updated
  @VisibleForTesting
  public void setLastUpdateDateForHost(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final ObjectId pIndexId,
      final Date pDate) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateStats(
        pGroupId,
        pClusterName,
        Optional.empty(),
        pIndexId,
        pHostname,
        FTSIndex.FieldDefs.STATS,
        Optional.of(FTSIndexHostStat.FieldDefs.LAST_UPDATE_DATE),
        pDate);
  }

  // For testing purpose: manually set lastUpdateDate to test getUpdatePullForCrashReport
  @VisibleForTesting
  public UpdateResult setLastUpdateForAddCrashReport(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final String pErrorCode,
      final String pMessage,
      final Date pDate) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    // add an incident with timestamp pDate
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                FTSIndexConfig.FieldDefs.ID,
                new BasicDBObject()
                    .append(FTSIndexConfig.FieldDefs.GROUP_ID, pGroupId)
                    .append(FTSIndexConfig.FieldDefs.CLUSTER_NAME, pClusterName))
            .append(
                FieldDefs.LAST_CRASH_REPORTS,
                new BasicDBObject()
                    .append(
                        ELEM_MATCH,
                        new BasicDBObject()
                            .append(FTSCrashReport.FieldDefs.HOSTNAME, pHostname)
                            .append(FTSCrashReport.FieldDefs.ERROR_CODE, pErrorCode)));
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.PUSH,
                new BasicDBObject()
                    .append(
                        String.join(
                            ".$.",
                            FieldDefs.LAST_CRASH_REPORTS,
                            FTSCrashReport.FieldDefs.INCIDENTS),
                        new BasicDBObject()
                            .append(FTSCrashReportIncident.FieldDefs.TIMESTAMP, pDate))
                    .append(FTSCrashReportIncident.FieldDefs.MESSAGE, pMessage));
    return updateOneMajority(query, update);
  }

  // For testing purpose: manually set lastUpdateDate so that stats can be successfully updated
  @VisibleForTesting
  public void setLastUpdateDateForTenantHost(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final ObjectId pIndexId,
      final String pTenantId,
      final Date pDate) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    updateStats(
        pGroupId,
        pClusterName,
        Optional.of(pTenantId),
        pIndexId,
        pHostname,
        FTSIndex.FieldDefs.STATS,
        Optional.of(FTSIndexHostStat.FieldDefs.LAST_UPDATE_DATE),
        pDate);
  }

  private BasicDBObject getQueryElemMatch(
      FTSIndexConfig pConfig, String pHostname, String pErrorCode) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                FTSIndexConfig.FieldDefs.ID, getId(pConfig.getGroupId(), pConfig.getClusterName()))
            .append(
                FieldDefs.LAST_CRASH_REPORTS,
                new BasicDBObject()
                    .append(
                        ELEM_MATCH,
                        new BasicDBObject()
                            .append(FTSCrashReport.FieldDefs.HOSTNAME, pHostname)
                            .append(FTSCrashReport.FieldDefs.ERROR_CODE, pErrorCode)));
    pConfig.getLastUpdateDate().ifPresent(date -> query.append(FieldDefs.LAST_UPDATE_DATE, date));
    return query;
  }

  private boolean pushIncidentToExistingReport(
      final FTSIndexConfig pConfig,
      final String pHostname,
      final String pMessage,
      final String pErrorCode) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject queryElemMatch = getQueryElemMatch(pConfig, pHostname, pErrorCode);
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.PUSH,
                new BasicDBObject()
                    .append(
                        String.join(
                            ".$.",
                            FieldDefs.LAST_CRASH_REPORTS,
                            FTSCrashReport.FieldDefs.INCIDENTS),
                        new BasicDBObject()
                            .append(FTSCrashReportIncident.FieldDefs.TIMESTAMP, new Date())
                            .append(FTSCrashReportIncident.FieldDefs.MESSAGE, pMessage)));
    final UpdateResult pushResult = updateOneMajority(queryElemMatch, update);
    return pushResult.getModifiedCount() != 0;
  }

  private boolean pushIncidentToNewReport(
      final FTSIndexConfig pConfig,
      final String pHostname,
      final String pMessage,
      final String pErrorCode) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject queryId =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pConfig.getGroupId(), pConfig.getClusterName()));
    pConfig.getLastUpdateDate().ifPresent(date -> queryId.append(FieldDefs.LAST_UPDATE_DATE, date));
    final List<BasicDBObject> incidentsArray =
        List.of(
            new BasicDBObject()
                .append(FTSCrashReportIncident.FieldDefs.TIMESTAMP, new Date())
                .append(FTSCrashReportIncident.FieldDefs.MESSAGE, pMessage));
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.PUSH,
                new BasicDBObject()
                    .append(
                        FieldDefs.LAST_CRASH_REPORTS,
                        new BasicDBObject()
                            .append(FTSCrashReport.FieldDefs.HOSTNAME, pHostname)
                            .append(FTSCrashReport.FieldDefs.ERROR_CODE, pErrorCode)
                            .append(FTSCrashReport.FieldDefs.INCIDENTS, incidentsArray)))
            .append(
                UpdateOperators.SET,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date()));
    final UpdateResult newResult = updateOneMajority(queryId, update);
    return newResult.getModifiedCount() == 1;
  }

  public boolean pullOldIncidents(
      final FTSIndexConfig pConfig, final String pHostname, final String pErrorCode) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    final BasicDBObject queryElemMatch = getQueryElemMatch(pConfig, pHostname, pErrorCode);
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.PULL,
                new BasicDBObject()
                    .append(
                        FieldDefs.LAST_CRASH_REPORTS + ".$.incidents",
                        new BasicDBObject()
                            .append(
                                FTSCrashReportIncident.FieldDefs.TIMESTAMP,
                                new BasicDBObject()
                                    .append(
                                        QueryOperators.LT,
                                        DateUtils.addMinutes(
                                            new Date(), INCIDENTS_LOOKUP_WINDOW)))))
            .append(
                UpdateOperators.SET,
                new BasicDBObject().append(FTSIndexConfig.FieldDefs.LAST_UPDATE_DATE, new Date()));
    return updateOneMajority(queryElemMatch, update).getModifiedCount() != 0;
  }

  public void addCrashReport(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final String pMessage,
      final String pErrorCode)
      throws SvcException {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    addCrashReport(pGroupId, pClusterName, pHostname, pMessage, pErrorCode, 3);
  }

  private void addCrashReport(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final String pMessage,
      final String pErrorCode,
      final int retries)
      throws SvcException {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    if (retries < 0) {
      throw new SvcException(NDSErrorCode.CONCURRENT_MODIFICATION);
    }

    final FTSIndexConfig ftsIndexConfig = find(pGroupId, pClusterName).orElse(null);
    if (ftsIndexConfig == null) {
      throw new SvcException(NDSErrorCode.INVALID_CLUSTER);
    }

    // return -> success
    if (pushIncidentToExistingReport(ftsIndexConfig, pHostname, pMessage, pErrorCode)) {
      if (pullOldIncidents(ftsIndexConfig, pHostname, pErrorCode)) {
        return;
      }
    } else if (pushIncidentToNewReport(ftsIndexConfig, pHostname, pMessage, pErrorCode)) {
      return;
    }

    addCrashReport(pGroupId, pClusterName, pHostname, pMessage, pErrorCode, retries - 1);
  }

  public Optional<Date> checkCrashReport(
      final ObjectId pGroupId,
      final String pHostname,
      final String pErrorCode,
      final ReadPreference pReadPreference) {
    final Optional<FTSIndexConfig> config =
        findConfigWithCrashByGroupIdAndHostname(pGroupId, pHostname, pReadPreference);
    if (config.isEmpty()) {
      return Optional.empty();
    }
    final Optional<FTSCrashReport> crashReport =
        config.get().getLastCrashReports().stream()
            .filter(
                report ->
                    report.getErrorCode().equals(pErrorCode)
                        && report.getHostname().equals(pHostname))
            .findFirst();
    if (crashReport.isPresent() && crashReport.get().getIncidents().orElse(List.of()).size() > 0) {
      final int lastIndex = crashReport.get().getIncidents().get().size() - 1;
      final Date lastIncidentTimestamp =
          crashReport
              .get()
              .getIncidents()
              .get()
              .get(lastIndex)
              .getDate(FTSCrashReportIncident.FieldDefs.TIMESTAMP);
      if (lastIncidentTimestamp.compareTo(DateUtils.addMinutes(new Date(), INCIDENTS_LOOKUP_WINDOW))
          >= 0) {
        return Optional.of(lastIncidentTimestamp);
      }
    }
    return Optional.empty();
  }

  public Optional<Date> checkReplicationStatus(
      final ObjectId pGroupId, final String pHostname, final ReadPreference pReadPreference) {
    final List<FTSIndexConfig> configs = findByGroup(pGroupId, pReadPreference);

    Optional<FTSIndexConfig> config =
        configs.stream()
            .filter(c -> c.getHostReplicationStatus().containsKey(pHostname))
            .findFirst();

    if (config.isEmpty()) {
      return Optional.empty();
    }
    final Optional<FTSReplicationStatus> replicationStatus =
        Optional.ofNullable(config.get().getHostReplicationStatus().get(pHostname));

    if (replicationStatus.isPresent() && replicationStatus.get().getIsReplicationStopped()) {
      Date lastUpdateTime = replicationStatus.get().getLastUpdatedTime();
      Date startTime = replicationStatus.get().getStartTime();

      // we only alert customer 10 mins after replication is stopped
      if (lastUpdateTime.compareTo(DateUtils.addMinutes(startTime, 10)) >= 0) {
        return Optional.of(replicationStatus.get().getLastUpdatedTime());
      }
    }

    return Optional.empty();
  }

  public Stream<Pair<FTSIndexConfig.Id, List<FTSIndex>>> findConfigsWithSearchIndexes(
      ReadPreference readPreference) {
    return DbUtils.stream(
            getCollection()
                .withReadPreference(readPreference)
                .find(
                    or(
                        exists(
                            joinFields(FTSIndexConfig.FieldDefs.INDEXES, FTSIndex.FieldDefs.NAME)),
                        exists(
                            joinFields(
                                FTSIndexConfig.FieldDefs.VECTOR_INDEXES, FTSIndex.FieldDefs.NAME))))
                .projection(
                    include(
                        FTSIndexConfig.FieldDefs.ID,
                        FTSIndexConfig.FieldDefs.INDEXES,
                        FTSIndexConfig.FieldDefs.TENANT_INDEXES,
                        FTSIndexConfig.FieldDefs.VECTOR_INDEXES,
                        FTSIndexConfig.FieldDefs.TENANT_VECTOR_INDEXES))
                .cursor())
        .map(
            partialFTSIndexConfig ->
                Pair.of(
                    partialFTSIndexConfig.getId(), partialFTSIndexConfig.getIndexesForAllTypes()));
  }

  public void setHostReplicationStatus(
      FTSIndexConfig indexConfig, String pHostname, FTSReplicationStatus pFTSReplicationStatus) {
    _ftsDaoAppSettings.ensureSearchWritesAllowed();
    Map<String, FTSReplicationStatus> currentReplicationStatus =
        indexConfig.getHostReplicationStatus();
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                FTSIndexConfig.FieldDefs.ID,
                getId(indexConfig.getGroupId(), indexConfig.getClusterName()));
    BasicDBObject update =
        new BasicDBObject()
            .append(
                UpdateOperators.SET,
                new BasicDBObject()
                    .append(
                        joinFields(FieldDefs.HOST_REPLICATION_STATUS, pHostname),
                        pFTSReplicationStatus.toDBObject()));

    if (currentReplicationStatus != null && currentReplicationStatus.containsKey(pHostname)) {
      if (currentReplicationStatus
          .get(pHostname)
          .getLastUpdatedTime()
          .after(pFTSReplicationStatus.getLastUpdatedTime())) {
        // If the provided replication status is not more recent than the stored one
        return;
      }
      if (currentReplicationStatus.get(pHostname).getIsReplicationStopped()
          && !pFTSReplicationStatus.getIsReplicationStopped()) {
        // If the host experienced a replication stop and is operational now
        // delete the replication status for the host
        update =
            new BasicDBObject()
                .append(
                    UpdateOperators.UNSET,
                    new BasicDBObject()
                        .append(joinFields(FieldDefs.HOST_REPLICATION_STATUS, pHostname), ""));
      }
    } else {
      if (!pFTSReplicationStatus.getIsReplicationStopped()) {
        // If the host did not experience a replication stop and is still operational
        return;
      }
    }
    updateOneMajority(query, update);
  }

  /**
   * If a cluster has text search indexes or vector search indexes, returns its groupId and
   * clusterName in the stream.
   */
  public Stream<Pair<ObjectId, String>> findDedicatedClustersWithSearchIndexes(
      ReadPreference readPreference) {
    return DbUtils.stream(
            getCollection()
                .withReadPreference(readPreference)
                .find(
                    or(
                        exists(
                            joinFields(FTSIndexConfig.FieldDefs.INDEXES, FTSIndex.FieldDefs.NAME)),
                        exists(
                            joinFields(
                                FTSIndexConfig.FieldDefs.VECTOR_INDEXES, FTSIndex.FieldDefs.NAME))))
                .projection(include(FTSIndexConfig.FieldDefs.ID))
                .cursor())
        .map(
            partialFTSIndexConfig ->
                Pair.of(
                    partialFTSIndexConfig.getGroupId(), partialFTSIndexConfig.getClusterName()));
  }

  private static boolean indexContainsUUID(FTSIndex pFTSIndex) {
    return pFTSIndex.getCollectionUUID().isPresent();
  }

  /**
   * queryWithUniqueIndex
   *
   * <p>This method returns a query ensures that any existing indices are distinct from an index.
   * This is likely used when that index is being added. Placing this restriction on the query
   * avoids race conditions where a duplicate index is added after a check but before the addition
   * of the index.
   *
   * @param pField the field where indices are stored in an array
   * @param pFTSIndex an index which should be unique from existing ones
   * @return a query which ensures uniqueness of new indices with existing queries
   */
  private static BasicDBObject queryWithUniqueIndex(String pField, FTSIndex pFTSIndex) {
    return new BasicDBObject()
        .append(
            pField,
            new BasicDBObject()
                .append(
                    NOT,
                    indexContainsUUID(pFTSIndex)
                        ? elemMatchIndexWithUUID(pFTSIndex)
                        : elemMatchIndexWithoutUUID(pFTSIndex)));
  }

  /**
   * queryWithUniqueIndexList
   *
   * <p>This method returns a query ensures that any existing indices are distinct from any of the
   * indices in a list. This is likely used when that list of indices is being added. Placing this
   * restriction on the query avoids race conditions where a duplicate index is added after a check
   * but before the addition of the index.
   *
   * @param pField the field where indices are stored in an array
   * @param pFTSIndexes a list of indices which should be unique from existing ones
   * @return a query which ensures uniqueness of new indices with existing queries
   */
  private static BasicDBObject queryWithUniqueIndexList(String pField, List<FTSIndex> pFTSIndexes) {
    boolean isUUIDInRequest = pFTSIndexes.stream().allMatch(FTSIndexConfigDao::indexContainsUUID);
    return new BasicDBObject()
        .append(
            NOR,
            pFTSIndexes.stream()
                .map(
                    index ->
                        new BasicDBObject()
                            .append(
                                pField,
                                isUUIDInRequest
                                    ? elemMatchIndexWithUUID(index)
                                    : elemMatchIndexWithoutUUID(index)))
                .collect(Collectors.toCollection(BasicDBList::new)));
  }

  /**
   * elemMatchIndexWithUUID
   *
   * <p>This method returns a query which matches any array which includes an index. It is assumed
   * that the index has UUID included, so we attempt to match using the tuple (indexName,
   * collectionUUID). However, if the UUID does not exist, then we instead use the tuple (indexName,
   * databaseName, lastObservedCollectionName). Using the UUID allows us to better handle the case
   * where a collection is renamed recently before the index creation is attempted.
   *
   * @param pIndex an index with a collectionUUID
   * @return a query for arrays including a matching index
   */
  private static BasicDBObject elemMatchIndexWithUUID(FTSIndex pIndex) {
    BasicDBList matchByCollectionUUIDOrCollectionName = new BasicDBList();
    matchByCollectionUUIDOrCollectionName.add(
        new BasicDBObject()
            .append(FTSIndex.FieldDefs.COLLECTION_UUID, pIndex.getCollectionUUID().get()));
    matchByCollectionUUIDOrCollectionName.add(
        new BasicDBObject()
            .append(FTSIndex.FieldDefs.COLLECTION_UUID, null)
            .append(FTSIndex.FieldDefs.DATABASE, pIndex.getDatabase())
            .append(
                FTSIndex.FieldDefs.LAST_OBSERVED_COLLECTION_NAME,
                pIndex.getLastObservedCollectionName()));

    return new BasicDBObject()
        .append(
            ELEM_MATCH,
            new BasicDBObject()
                .append(FTSIndex.FieldDefs.NAME, pIndex.getName())
                .append(OR, matchByCollectionUUIDOrCollectionName));
  }

  /**
   * elemMatchIndexWithUUID
   *
   * <p>This method returns a query which matches any array which includes an index. It is assumed
   * that the index does not have UUID included, so we attempt to match using the tuple (indexName,
   * databaseName, lastObservedCollectionName). This should be the same behavior as when the
   * collectionUUID is null inside the database
   *
   * @param pIndex an index without a collectionUUID
   * @return a query for arrays including a matching index
   */
  private static BasicDBObject elemMatchIndexWithoutUUID(FTSIndex pIndex) {
    return new BasicDBObject()
        .append(
            ELEM_MATCH,
            new BasicDBObject()
                .append(FTSIndex.FieldDefs.NAME, pIndex.getName())
                .append(FTSIndex.FieldDefs.DATABASE, pIndex.getDatabase())
                .append(
                    FTSIndex.FieldDefs.LAST_OBSERVED_COLLECTION_NAME,
                    pIndex.getLastObservedCollectionName()));
  }

  private static BasicDBObject pushIndexList(List<FTSIndex> pFTSIndexes, Date pCreateDate) {
    return new BasicDBObject()
        .append(
            EACH,
            pFTSIndexes.stream()
                .map(FTSIndex::toDBObject)
                .map(
                    i -> {
                      i.append(FTSIndex.FieldDefs.CREATE_DATE, pCreateDate);
                      i.append(FTSIndex.FieldDefs.DEFINITION_VERSION_CREATED_AT, pCreateDate);
                      return i;
                    })
                .collect(Collectors.toList()));
  }

  private static String getIndexesFieldNameByType(FTSIndex.Type pType) {
    switch (pType) {
      case SEARCH:
        return FTSIndexConfig.FieldDefs.INDEXES;
      case VECTOR_SEARCH:
        return FTSIndexConfig.FieldDefs.VECTOR_INDEXES;
      default:
        throw new IllegalArgumentException("Unknown index type: " + pType);
    }
  }

  private static String getTenantIndexesFieldNameByType(FTSIndex.Type pType) {
    switch (pType) {
      case SEARCH:
        return FTSIndexConfig.FieldDefs.TENANT_INDEXES;
      case VECTOR_SEARCH:
        return FTSIndexConfig.FieldDefs.TENANT_VECTOR_INDEXES;
      default:
        throw new IllegalArgumentException("Unknown index type: " + pType);
    }
  }

  /**
   * getNumIndexes
   *
   * @return number of indexes that exist
   */
  private static int getNumIndexes(final Optional<FTSIndexConfig> pFTSIndexConfig) {
    return pFTSIndexConfig.map(indexConfig -> indexConfig.getIndexesForAllTypes().size()).orElse(0);
  }

  /**
   * getNumTenantIndexes
   *
   * @return number of indexes that exist for the specified tenant
   */
  private static int getNumTenantIndexes(
      final Optional<FTSIndexConfig> pFTSIndexConfig, final String pTenantId) {
    return pFTSIndexConfig
        .map(indexConfig -> indexConfig.getTenantIndexesByTenantIdForAllTypes(pTenantId).size())
        .orElse(0);
  }
}
