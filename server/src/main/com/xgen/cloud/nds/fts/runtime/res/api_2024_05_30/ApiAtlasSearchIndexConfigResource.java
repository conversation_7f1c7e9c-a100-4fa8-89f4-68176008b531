package com.xgen.cloud.nds.fts.runtime.res.api_2024_05_30;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.OPERATION_ID_OVERRIDE;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.VERB_OVERRIDE;
import static com.xgen.cloud.search.api._public.util.api_2024_05_30.ApiAtlasSearchIndexResponseViewFactory.createResponseView;

import com.google.errorprone.annotations.Var;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetryAttributeSvc;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetrySvc.CustomTelemetryFieldKey;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.res._public.util.NDSErrorCodeUtils;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndex;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.search.api._public.view.api_2024_05_30.ApiAtlasSearchIndexCreateRequestView;
import com.xgen.cloud.search.api._public.view.api_2024_05_30.ApiAtlasSearchIndexResponseView;
import com.xgen.cloud.search.api._public.view.api_2024_05_30.ApiAtlasSearchIndexUpdateRequestView;
import com.xgen.module.iam.annotation.RequireCustomerGrantForEmployeeAccess;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.mms.res.filter.PaidInFull;
import com.xgen.svc.mms.res.filter.SubscriptionPlan;
import com.xgen.svc.nds.svc.FTSIndexConfigSvc;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

@SubscriptionPlan(PlanTypeSet.NDS)
@PaidInFull
@Path("/api/atlas/v2/groups/{groupId}/clusters/{clusterName}/search")
@AllowNDSCNRegionsOnlyGroups
@Singleton
public class ApiAtlasSearchIndexConfigResource extends ApiBaseResource {

  private final FTSIndexConfigSvc ftsIndexConfigSvc;
  private final AppSettings appSettings;
  private final ApiTelemetryAttributeSvc apiTelemetryAttributeSvc;

  @Inject
  public ApiAtlasSearchIndexConfigResource(
      final AppSettings appSettings,
      final FTSIndexConfigSvc ftsIndexConfigSvc,
      final ApiTelemetryAttributeSvc apiTelemetryAttributeSvc) {
    super(appSettings);
    this.ftsIndexConfigSvc = ftsIndexConfigSvc;
    this.appSettings = appSettings;
    this.apiTelemetryAttributeSvc = apiTelemetryAttributeSvc;
  }

  @POST
  @Path("/indexes")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "API predates IPA validation."),
      })
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({VersionMediaType.V_2024_05_30_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN, RoleSet.NAME.GROUP_SEARCH_INDEX_EDITOR})
  @Operation(
      summary = "Create One Atlas Search Index",
      operationId = "createGroupClusterSearchIndex",
      description =
          "Creates one Atlas Search index on the specified collection. Atlas Search indexes define"
              + " the fields on which to create the index and the analyzers to use when creating"
              + " the index. Only clusters running MongoDB v4.2 or later can use Atlas Search. To"
              + " use this resource, the requesting Service Account or API Key must have the"
              + " Project Data Access Admin role.",
      tags = {"Atlas Search"},
      externalDocs =
          @ExternalDocumentation(
              description = "Atlas Search Indexes",
              url = "https://dochub.mongodb.org/core/index-definitions-fts"),
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description =
                "Name of the cluster that contains the collection on which to create an"
                    + " Atlas Search index.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "201",
            description = "Created",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_05_30_JSON,
                    schema = @Schema(implementation = ApiAtlasSearchIndexResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2024-05-30")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Creates one Atlas Search index on the specified collection.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2024_05_30_JSON,
                      schema =
                          @Schema(implementation = ApiAtlasSearchIndexCreateRequestView.class))),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Search Web Platform")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "createClusterSearchIndex")
            })
      })
  @WithSpan
  public Response createSearchIndex(
      @Context final AuditInfo auditInfo,
      @Context final HttpServletRequest request,
      @Context final Group group,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope,
      final ApiAtlasSearchIndexCreateRequestView createRequestView)
      throws Exception {
    final ObjectId indexId = new ObjectId();
    try {
      final var cluster =
          this.ftsIndexConfigSvc.fetchSearchPermittedClusterDescription(group.getId(), clusterName);
      final FTSIndex index;
      try {
        index = createRequestView.toFTSIndex(indexId);
      } catch (IllegalArgumentException e) {
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
      }

      if (!ValidationUtils.isValidSearchIndexName(index.getName())) {
        throw ApiErrorCode.INVALID_SEARCH_INDEX_NAME.exception(envelope, index.getName());
      }
      boolean isDetailedStatusesReported =
          this.ftsIndexConfigSvc.isIndexDetailedStatusesReported(group.getId());
      if (cluster.isFlexOrSharedTenantCluster()) {
        this.ftsIndexConfigSvc.addTenantFTSIndex(group.getId(), clusterName, index, auditInfo);
      } else {
        this.ftsIndexConfigSvc.addFTSIndex(group.getId(), clusterName, index, auditInfo);
      }

      return new ApiResponseBuilder(envelope)
          .created()
          .content(createResponseView(index, isDetailedStatusesReported))
          .build();
    } catch (final SvcException e) {
      translateAndThrow(group.getId(), clusterName, envelope, null, e);

      throw e;
    }
  }

  @GET
  @Path("/indexes")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "API predates IPA validation."),
      })
  @Produces({VersionMediaType.V_2024_05_30_JSON})
  @RolesAllowed({
    RoleSet.NAME.GROUP_DATA_ACCESS_ANY,
    RoleSet.NAME.GROUP_SEARCH_INDEX_EDITOR,
    RoleSet.NAME.GLOBAL_INTERNAL_TOOLS_READ_ONLY
  })
  @Operation(
      summary = "Return All Atlas Search Indexes for One Cluster",
      operationId = "listGroupClusterSearchIndexes",
      description =
          "Returns all Atlas Search indexes on the specified cluster. Atlas Search indexes contain"
              + " the indexed fields and the analyzers used to create the indexes. To use this"
              + " resource, the requesting Service Account or API Key must have the Project Data"
              + " Access Read Write role.",
      tags = {"Atlas Search"},
      externalDocs =
          @ExternalDocumentation(
              description = "Atlas Search Indexes",
              url = "https://dochub.mongodb.org/core/index-definitions-fts"),
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description =
                "Name of the cluster that contains the collection with one or more Atlas"
                    + " Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_05_30_JSON,
                    array =
                        @ArraySchema(
                            arraySchema =
                                @Schema(
                                    description =
                                        "List of Atlas Search indexes that MongoDB Cloud"
                                            + " returns for this request."),
                            schema =
                                @Schema(implementation = ApiAtlasSearchIndexResponseView.class),
                            extensions = {
                              @Extension(
                                  name = IPA_EXCEPTION,
                                  properties = {
                                    @ExtensionProperty(
                                        name = "xgen-IPA-124-array-max-items",
                                        value = "Schema predates IPA validation."),
                                  })
                            }),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2024-05-30")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError"),
        @ApiResponse(
            responseCode = "503",
            description = "Service Unavailable.",
            content = @Content(schema = @Schema(implementation = ApiError.class)))
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Search Web Platform")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listClusterSearchIndexes")
            })
      })
  public Response getSearchIndexesCluster(
      @Context final Group group,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope)
      throws Exception {
    try {
      final var cluster = this.ftsIndexConfigSvc.verifyGroupAndCluster(group.getId(), clusterName);
      boolean isDetailedStatusesReported =
          this.ftsIndexConfigSvc.isIndexDetailedStatusesReported(group.getId());

      final List<FTSIndex> ftsIndexes =
          cluster.isFlexOrSharedTenantCluster()
              ? this.ftsIndexConfigSvc.getTenantFTSIndexes(group.getId(), clusterName)
              : this.ftsIndexConfigSvc.getFTSIndexes(group.getId(), clusterName);

      var content =
          ftsIndexes.stream()
              .map(index -> createResponseView(index, isDetailedStatusesReported))
              .collect(Collectors.toList());
      return new ApiResponseBuilder(envelope).ok().content(content).build();

    } catch (final SvcException e) {
      translateAndThrow(group.getId(), clusterName, envelope, null, e);

      throw e;
    }
  }

  @GET
  @Path("/indexes/{databaseName}/{collectionName}")
  @Produces({VersionMediaType.V_2024_05_30_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ANY, RoleSet.NAME.GROUP_SEARCH_INDEX_EDITOR})
  @Operation(
      summary = "Return All Atlas Search Indexes for One Collection",
      operationId = "listGroupClusterSearchIndex",
      description =
          "Returns all Atlas Search indexes on the specified collection. Atlas Search indexes"
              + " contain the indexed fields and the analyzers used to create the indexes. To use"
              + " this resource, the requesting Service Account or API Key must have the Project"
              + " Data Access Read Write role.",
      tags = {"Atlas Search"},
      externalDocs =
          @ExternalDocumentation(
              description = "Atlas Search Indexes",
              url = "https://dochub.mongodb.org/core/index-definitions-fts"),
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description =
                "Name of the cluster that contains the collection with one or more Atlas"
                    + " Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "collectionName",
            description = "Name of the collection that contains one or more Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "databaseName",
            description =
                "Label that identifies the database that contains the collection"
                    + " with one or more Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_05_30_JSON,
                    array =
                        @ArraySchema(
                            arraySchema =
                                @Schema(
                                    description =
                                        "List of Atlas Search indexes that MongoDB Cloud"
                                            + " returns for this request."),
                            schema =
                                @Schema(implementation = ApiAtlasSearchIndexResponseView.class),
                            extensions = {
                              @Extension(
                                  name = IPA_EXCEPTION,
                                  properties = {
                                    @ExtensionProperty(
                                        name = "xgen-IPA-124-array-max-items",
                                        value = "Schema predates IPA validation."),
                                  })
                            }),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2024-05-30")
                          }),
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError"),
        @ApiResponse(
            responseCode = "503",
            description = "Service Unavailable.",
            content = @Content(schema = @Schema(implementation = ApiError.class)))
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Search Web Platform")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listSearchIndex")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "list"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            })
      })
  public Response getSearchIndexes(
      @Context final Group group,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      @Parameter(hidden = true) @PathParam("databaseName") final String databaseName,
      @Parameter(hidden = true) @PathParam("collectionName") final String leafCollection,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope)
      throws Exception {
    try {
      final var cluster = this.ftsIndexConfigSvc.verifyGroupAndCluster(group.getId(), clusterName);
      boolean isDetailedStatusesReported =
          this.ftsIndexConfigSvc.isIndexDetailedStatusesReported(group.getId());

      final List<FTSIndex> ftsIndexes =
          cluster.isFlexOrSharedTenantCluster()
              ? this.ftsIndexConfigSvc.getTenantFTSIndexesForNamespace(
                  group.getId(), clusterName, databaseName, leafCollection)
              : this.ftsIndexConfigSvc.getFTSIndexesForNamespace(
                  group.getId(), clusterName, databaseName, leafCollection);

      var content =
          ftsIndexes.stream()
              .map(index -> createResponseView(index, isDetailedStatusesReported))
              .toList();

      return new ApiResponseBuilder(envelope).ok().content(content).build();

    } catch (final SvcException e) {
      translateAndThrow(group.getId(), clusterName, envelope, null, e);

      throw e;
    }
  }

  @GET
  @Path("/indexes/{indexId}")
  @Produces({VersionMediaType.V_2024_05_30_JSON})
  @RolesAllowed({
    RoleSet.NAME.GROUP_DATA_ACCESS_ANY,
    RoleSet.NAME.GROUP_SEARCH_INDEX_EDITOR,
    RoleSet.NAME.GLOBAL_MONITORING_ADMIN,
    RoleSet.NAME.GLOBAL_CRASH_LOG_ANALYST
  })
  @RequireCustomerGrantForEmployeeAccess()
  @Operation(
      summary = "Return One Atlas Search Index by ID",
      operationId = "getGroupClusterSearchIndex",
      description =
          "Returns one Atlas Search index in the specified project. You identify this index using"
              + " its unique ID. Atlas Search index contains the indexed fields and the analyzers"
              + " used to create the index. To use this resource, the requesting Service Account or"
              + " API Key must have the Project Data Access Read Write role.",
      tags = {"Atlas Search"},
      externalDocs =
          @ExternalDocumentation(
              description = "Atlas Search Indexes",
              url = "https://dochub.mongodb.org/core/index-definitions-fts"),
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description =
                "Name of the cluster that contains the collection with one or more Atlas"
                    + " Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "indexId",
            description =
                "Unique 24-hexadecimal digit string that identifies the Application Search"
                    + " [index](https://dochub.mongodb.org/core/index-definitions-fts)."
                    + " Use the [Get All Application Search Indexes for a Collection"
                    + " API](https://docs.atlas.mongodb.com/reference/api/fts-indexes-get-all/)"
                    + " endpoint to find the IDs of all Application Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.OBJECT_ID_REGEX),
            required = true,
            extensions = {
              @Extension(
                  name = IPA_EXCEPTION,
                  properties = {
                    @ExtensionProperty(
                        name = "xgen-IPA-117-description-should-not-use-inline-links",
                        value = "Parameters don't support externalDocs."),
                  })
            })
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_05_30_JSON,
                    schema = @Schema(implementation = ApiAtlasSearchIndexResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2024-05-30")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError"),
        @ApiResponse(
            responseCode = "503",
            description = "Service Unavailable.",
            content = @Content(schema = @Schema(implementation = ApiError.class)))
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Search Web Platform")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getClusterSearchIndex")
            })
      })
  public Response getSearchIndexById(
      @Context final Group group,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope,
      @Parameter(hidden = true) @PathParam("indexId") final ObjectId indexId)
      throws Exception {

    try {
      boolean isDetailedStatusesReported =
          this.ftsIndexConfigSvc.isIndexDetailedStatusesReported(group.getId());

      final Optional<FTSIndex> ftsIndexOpt =
          this.ftsIndexConfigSvc.isSharedOrFlexTenantCluster(group.getId(), clusterName)
              ? this.ftsIndexConfigSvc.getTenantFTSIndex(group.getId(), clusterName, indexId)
              : this.ftsIndexConfigSvc.getFTSIndex(group.getId(), clusterName, indexId);

      if (ftsIndexOpt.isEmpty()) {
        throw new SvcException(CommonErrorCode.NOT_FOUND);
      }

      return new ApiResponseBuilder(envelope)
          .ok()
          .content(createResponseView(ftsIndexOpt.get(), isDetailedStatusesReported))
          .build();
    } catch (final SvcException e) {
      translateAndThrow(group.getId(), clusterName, envelope, indexId.toHexString(), e);

      throw e;
    }
  }

  @GET
  @Path("/indexes/{databaseName}/{collectionName}/{indexName}")
  @Produces({VersionMediaType.V_2024_05_30_JSON})
  @RolesAllowed({
    RoleSet.NAME.GROUP_DATA_ACCESS_ANY,
    RoleSet.NAME.GROUP_SEARCH_INDEX_EDITOR,
    RoleSet.NAME.GLOBAL_MONITORING_ADMIN,
    RoleSet.NAME.GLOBAL_CRASH_LOG_ANALYST
  })
  @RequireCustomerGrantForEmployeeAccess()
  @Operation(
      summary = "Return One Atlas Search Index by Name",
      operationId = "getGroupClusterSearchIndexByName",
      description =
          "Returns one Atlas Search index in the specified project. You identify this index using"
              + " its database, collection and name. Atlas Search index contains the indexed fields"
              + " and the analyzers used to create the index. To use this resource, the requesting"
              + " Service Account or API Key must have the Project Data Access Read Write role.",
      tags = {"Atlas Search"},
      externalDocs =
          @ExternalDocumentation(
              description = "Atlas Search Indexes",
              url = "https://dochub.mongodb.org/core/index-definitions-fts"),
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description =
                "Name of the cluster that contains the collection with one or more Atlas"
                    + " Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "collectionName",
            description = "Name of the collection that contains one or more Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "databaseName",
            description =
                "Label that identifies the database that contains the collection"
                    + " with one or more Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "indexName",
            description = "Name of the Atlas Search index to return.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_05_30_JSON,
                    schema = @Schema(implementation = ApiAtlasSearchIndexResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2024-05-30")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Search Web Platform")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "getByName"),
              @ExtensionProperty(name = "customMethod", value = "true", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getIndexByName")
            }),
      })
  public Response getSearchIndexByName(
      @Context final Group group,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      @Parameter(hidden = true) @PathParam("databaseName") final String databaseName,
      @Parameter(hidden = true) @PathParam("collectionName") final String leafCollection,
      @Parameter(hidden = true) @PathParam("indexName") final String indexName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope)
      throws Exception {
    try {
      final var cluster =
          this.ftsIndexConfigSvc.fetchSearchPermittedClusterDescription(group.getId(), clusterName);
      boolean isDetailedStatusesReported =
          this.ftsIndexConfigSvc.isIndexDetailedStatusesReported(group.getId());

      final var ftsIndexOpt =
          cluster.isFlexOrSharedTenantCluster()
              ? this.ftsIndexConfigSvc.getTenantFTSIndex(
                  group.getId(), clusterName, databaseName, leafCollection, indexName)
              : this.ftsIndexConfigSvc.getFTSIndex(
                  group.getId(), clusterName, databaseName, leafCollection, indexName);

      if (ftsIndexOpt.isEmpty()) {
        throw new SvcException(CommonErrorCode.NOT_FOUND);
      }

      return new ApiResponseBuilder(envelope)
          .ok()
          .content(createResponseView(ftsIndexOpt.get(), isDetailedStatusesReported))
          .build();
    } catch (final SvcException e) {
      translateAndThrow(group.getId(), clusterName, envelope, indexName, e);

      throw e;
    }
  }

  @DELETE
  @Path("/indexes/{indexId}")
  @Produces({VersionMediaType.V_2024_05_30_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN, RoleSet.NAME.GROUP_SEARCH_INDEX_EDITOR})
  @Operation(
      summary = "Remove One Atlas Search Index by ID",
      operationId = "deleteGroupClusterSearchIndex",
      description =
          "Removes one Atlas Search index that you identified with its unique ID. To use this"
              + " resource, the requesting Service Account or API Key must have the Project Data"
              + " Access Admin role. This deletion is eventually consistent.",
      tags = {"Atlas Search"},
      externalDocs =
          @ExternalDocumentation(
              description = "Atlas Search Indexes",
              url = "https://dochub.mongodb.org/core/index-definitions-fts"),
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description =
                "Name of the cluster that contains the database and collection with one or more"
                    + " Application Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "indexId",
            description =
                "Unique 24-hexadecimal digit string that identifies the Atlas Search"
                    + " index. Use the [Get All Atlas Search Indexes for a Collection"
                    + " API](https://docs.atlas.mongodb.com/reference/api/fts-indexes-get-all/)"
                    + " endpoint to find the IDs of all Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.OBJECT_ID_REGEX),
            required = true,
            extensions = {
              @Extension(
                  name = IPA_EXCEPTION,
                  properties = {
                    @ExtensionProperty(
                        name = "xgen-IPA-117-description-should-not-use-inline-links",
                        value = "Parameters don't support externalDocs."),
                  })
            })
      },
      responses = {
        @ApiResponse(
            responseCode = "204",
            description = OpenApiConst.ResponseDescriptions.NO_BODY,
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2024_05_30_JSON,
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2024-05-30")
                        })
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Search Web Platform")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "deleteClusterSearchIndex")
            })
      })
  @WithSpan
  public Response deleteSearchIndex(
      @Context final AuditInfo auditInfo,
      @Context final HttpServletRequest request,
      @Context final Group group,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope,
      @Parameter(hidden = true) @PathParam("indexId") final ObjectId indexId)
      throws SvcException {
    try {
      final var cluster =
          this.ftsIndexConfigSvc.fetchSearchPermittedClusterDescription(group.getId(), clusterName);
      if (cluster.isFlexOrSharedTenantCluster()) {
        this.ftsIndexConfigSvc.requestDeleteTenantFTSIndex(
            group.getId(), clusterName, indexId, auditInfo);
      } else {
        this.ftsIndexConfigSvc.requestDeleteFTSIndex(
            group.getId(), clusterName, indexId, auditInfo);
      }
      return new ApiResponseBuilder(envelope).noContent().build();
    } catch (final SvcException e) {
      // this is the only method that returns RESOURCE_NOT_FOUND instead of CLUSTER_NAME_NOT_FOUND.
      if (e.getErrorCode().equals(NDSErrorCode.CLUSTER_NOT_FOUND)) {
        throw ApiErrorCode.RESOURCE_NOT_FOUND.exception(envelope, clusterName, group.getId());
      }

      translateAndThrow(group.getId(), clusterName, envelope, indexId.toHexString(), e);

      throw e;
    }
  }

  @DELETE
  @Path("/indexes/{databaseName}/{collectionName}/{indexName}")
  @Produces({VersionMediaType.V_2024_05_30_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN, RoleSet.NAME.GROUP_SEARCH_INDEX_EDITOR})
  @Operation(
      summary = "Remove One Atlas Search Index by Name",
      operationId = "deleteGroupClusterSearchIndexByName",
      description =
          "Removes one Atlas Search index that you identified with its database, collection, and"
              + " name. To use this resource, the requesting Service Account or API Key must have"
              + " the Project Data Access Admin role. This deletion is eventually consistent.",
      tags = {"Atlas Search"},
      externalDocs =
          @ExternalDocumentation(
              description = "Atlas Search Indexes",
              url = "https://dochub.mongodb.org/core/index-definitions-fts"),
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description =
                "Name of the cluster that contains the database and collection with one or more"
                    + " Application Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "collectionName",
            description = "Name of the collection that contains one or more Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "databaseName",
            description =
                "Label that identifies the database that contains the collection"
                    + " with one or more Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "indexName",
            description = "Name of the Atlas Search index to delete.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "204",
            description = OpenApiConst.ResponseDescriptions.NO_BODY,
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_05_30_JSON,
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2024-05-30")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Search Web Platform")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "deleteByName"),
              @ExtensionProperty(name = "customMethod", value = "true", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "deleteIndexByName")
            }),
      })
  @WithSpan
  public Response deleteSearchIndexByName(
      @Context final AuditInfo auditInfo,
      @Context final HttpServletRequest request,
      @Context final Group group,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope,
      @Parameter(hidden = true) @PathParam("databaseName") final String databaseName,
      @Parameter(hidden = true) @PathParam("collectionName") final String leafCollection,
      @Parameter(hidden = true) @PathParam("indexName") final String indexName)
      throws SvcException {
    try {
      final var cluster =
          this.ftsIndexConfigSvc.fetchSearchPermittedClusterDescription(group.getId(), clusterName);
      if (cluster.isFlexOrSharedTenantCluster()) {
        this.ftsIndexConfigSvc.requestDeleteTenantFTSIndex(
            group.getId(),
            clusterName,
            databaseName,
            leafCollection,
            Optional.empty(),
            indexName,
            auditInfo);
      } else {
        this.ftsIndexConfigSvc.requestDeleteFTSIndex(
            group.getId(),
            clusterName,
            databaseName,
            leafCollection,
            Optional.empty(),
            indexName,
            auditInfo);
      }
      return new ApiResponseBuilder(envelope).noContent().build();
    } catch (final SvcException e) {
      translateAndThrow(group.getId(), clusterName, envelope, indexName, e);

      throw e;
    }
  }

  @PATCH
  @Path("/indexes/{indexId}")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({VersionMediaType.V_2024_05_30_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN, RoleSet.NAME.GROUP_SEARCH_INDEX_EDITOR})
  @Operation(
      summary = "Update One Atlas Search Index by ID",
      operationId = "updateGroupClusterSearchIndex",
      description =
          "Updates one Atlas Search index that you identified with its unique ID. Atlas Search"
              + " indexes define the fields on which to create the index and the analyzers to use"
              + " when creating the index. To use this resource, the requesting Service Account or"
              + " API Key must have the Project Data Access Admin role.",
      tags = {"Atlas Search"},
      externalDocs =
          @ExternalDocumentation(
              description = "Atlas Search Indexes",
              url = "https://dochub.mongodb.org/core/index-definitions-fts"),
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description =
                "Name of the cluster that contains the collection whose Atlas Search index you want"
                    + " to update.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "indexId",
            description =
                "Unique 24-hexadecimal digit string that identifies the Atlas Search"
                    + " [index](https://dochub.mongodb.org/core/index-definitions-fts)."
                    + " Use the [Get All Atlas Search Indexes for a Collection"
                    + " API](https://docs.atlas.mongodb.com/reference/api/fts-indexes-get-all/)"
                    + " endpoint to find the IDs of all Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.OBJECT_ID_REGEX),
            required = true,
            extensions = {
              @Extension(
                  name = IPA_EXCEPTION,
                  properties = {
                    @ExtensionProperty(
                        name = "xgen-IPA-117-description-should-not-use-inline-links",
                        value = "Parameters don't support externalDocs."),
                  })
            })
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2024_05_30_JSON,
                  schema = @Schema(implementation = ApiAtlasSearchIndexResponseView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2024-05-30")
                        })
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Details to update on the Atlas Search index.",
              content = {
                @Content(
                    mediaType = VersionMediaType.V_2024_05_30_JSON,
                    schema = @Schema(implementation = ApiAtlasSearchIndexUpdateRequestView.class))
              }),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Search Web Platform")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "updateClusterSearchIndex")
            })
      })
  @WithSpan
  public Response updateUserDefinedIndexFields(
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("indexId") final ObjectId pIndexId,
      final ApiAtlasSearchIndexUpdateRequestView pUpdateRequestView)
      throws Exception {
    @Var FTSIndex updatedIndex;
    try {
      final var cluster =
          this.ftsIndexConfigSvc.fetchSearchPermittedClusterDescription(
              pGroup.getId(), pClusterName);
      BasicDBObject definition =
          BasicDBObject.parse(
              CustomJacksonJsonProvider.createObjectMapper()
                  .writeValueAsString(pUpdateRequestView.definition()));
      Function<FTSIndex.Type, FTSIndex> definitionProducer =
          type ->
              switch (type) {
                case SEARCH -> new FTSSearchIndex(definition);
                case VECTOR_SEARCH -> new FTSVectorSearchIndex(definition);
              };
      if (cluster.isFlexOrSharedTenantCluster()) {
        updatedIndex =
            this.ftsIndexConfigSvc.updateTenantUserDefinedFTSIndexFields(
                pGroup.getId(),
                pClusterName,
                pIndexId,
                definitionProducer,
                pAuditInfo,
                Optional.empty());
      } else {
        updatedIndex =
            this.ftsIndexConfigSvc.updateUserDefinedFTSIndexFields(
                pGroup.getId(),
                pClusterName,
                pIndexId,
                definitionProducer,
                pAuditInfo,
                Optional.empty());
      }

      return new ApiResponseBuilder(pEnvelope)
          .ok()
          .content(createResponseView(updatedIndex, false))
          .build();
    } catch (final SvcException e) {
      translateAndThrow(pGroup.getId(), pClusterName, pEnvelope, pIndexId.toHexString(), e);

      throw e;
    }
  }

  @PATCH
  @Path("/indexes/{databaseName}/{collectionName}/{indexName}")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({VersionMediaType.V_2024_05_30_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN, RoleSet.NAME.GROUP_SEARCH_INDEX_EDITOR})
  @Operation(
      summary = "Update One Atlas Search Index by Name",
      operationId = "updateGroupClusterSearchIndexByName",
      description =
          "Updates one Atlas Search index that you identified with its database, collection name,"
              + " and index name. Atlas Search indexes define the fields on which to create the"
              + " index and the analyzers to use when creating the index. To use this resource, the"
              + " requesting Service Account or API Key must have the Project Data Access Admin"
              + " role.",
      tags = {"Atlas Search"},
      externalDocs =
          @ExternalDocumentation(
              description = "Atlas Search Indexes",
              url = "https://dochub.mongodb.org/core/index-definitions-fts"),
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description =
                "Name of the cluster that contains the collection whose Atlas Search index you want"
                    + " to update.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "collectionName",
            description = "Name of the collection that contains one or more Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "databaseName",
            description =
                "Label that identifies the database that contains the collection"
                    + " with one or more Atlas Search indexes.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "indexName",
            description = "Name of the Atlas Search index to update.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2024_05_30_JSON,
                    schema = @Schema(implementation = ApiAtlasSearchIndexResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2024-05-30")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Details to update the Atlas Search index with.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2024_05_30_JSON,
                      schema =
                          @Schema(implementation = ApiAtlasSearchIndexUpdateRequestView.class))),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Search Web Platform")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "updateByName"),
              @ExtensionProperty(name = "customMethod", value = "true", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "updateIndexByName")
            }),
      })
  @WithSpan
  public Response updateUserDefinedIndexFieldsByName(
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @PathParam("databaseName") final String pDatabaseName,
      @Parameter(hidden = true) @PathParam("collectionName") final String pLeafCollection,
      @Parameter(hidden = true) @PathParam("indexName") final String pIndexName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasSearchIndexUpdateRequestView pUpdateRequestView)
      throws Exception {
    @Var FTSIndex updatedIndex;
    try {
      final var cluster =
          this.ftsIndexConfigSvc.fetchSearchPermittedClusterDescription(
              pGroup.getId(), pClusterName);
      BasicDBObject definition =
          BasicDBObject.parse(
              CustomJacksonJsonProvider.createObjectMapper()
                  .writeValueAsString(pUpdateRequestView.definition()));
      Function<FTSIndex.Type, FTSIndex> definitionProducer =
          type ->
              switch (type) {
                case SEARCH -> new FTSSearchIndex(definition);
                case VECTOR_SEARCH -> new FTSVectorSearchIndex(definition);
              };

      if (cluster.isFlexOrSharedTenantCluster()) {
        updatedIndex =
            this.ftsIndexConfigSvc.updateTenantUserDefinedFTSIndexFields(
                pGroup.getId(),
                pClusterName,
                pDatabaseName,
                pLeafCollection,
                pIndexName,
                definitionProducer,
                pAuditInfo,
                Optional.empty());
      } else {
        updatedIndex =
            this.ftsIndexConfigSvc.updateUserDefinedFTSIndexFields(
                pGroup.getId(),
                pClusterName,
                pDatabaseName,
                pLeafCollection,
                pIndexName,
                definitionProducer,
                pAuditInfo,
                Optional.empty());
      }

      apiTelemetryAttributeSvc.putCustomAttribute(
          pRequest,
          CustomTelemetryFieldKey.SEARCH_TYPE,
          updatedIndex.getActualType().getStringValue());

      return new ApiResponseBuilder(pEnvelope)
          .ok()
          .content(createResponseView(updatedIndex, false))
          .build();
    } catch (final SvcException e) {
      translateAndThrow(pGroup.getId(), pClusterName, pEnvelope, pIndexName, e);

      throw e;
    }
  }

  private void translateAndThrow(
      ObjectId groupId,
      String clusterName,
      Boolean envelope,
      String indexIdOrName,
      SvcException e) {
    if (e.getErrorCode().equals(CommonErrorCode.NOT_FOUND)) {
      throw ApiErrorCode.RESOURCE_NOT_FOUND.exception(
          envelope,
          indexIdOrName != null
              ? String.format("search index %s", indexIdOrName)
              : "due to missing search index info");
    }

    if (e.getErrorCode().equals(NDSErrorCode.INVALID_GROUP_ID)) {
      throw ApiErrorCode.INVALID_GROUP_ID.exception(envelope, groupId);
    }

    if (e.getErrorCode().equals(NDSErrorCode.CLUSTER_NOT_FOUND)) {
      throw ApiErrorCode.CLUSTER_NAME_NOT_FOUND.exception(envelope, clusterName, groupId);
    }

    if (e.getErrorCode().equals(NDSErrorCode.SERVERLESS_INSTANCE_FTS_NOT_SUPPORTED)) {
      handleServerlessInstanceFTSNotSupportedException(groupId, clusterName, envelope);
    }

    if (e.getErrorCode().equals(NDSErrorCode.FTS_DUPLICATE_INDEX)) {
      throw ApiErrorCode.ATLAS_SEARCH_DUPLICATE_INDEX.exception(
          envelope, e.getMessageParams().toArray());
    }

    if (e.getErrorCode() == NDSErrorCode.TENANT_FTS_INDEX_TOO_LARGE) {
      throw ApiErrorCode.TENANT_SEARCH_INDEX_TOO_LARGE.exception(envelope);
    }

    if (e.getErrorCode() == NDSErrorCode.MAXIMUM_INDEXES_FOR_TENANT_EXCEEDED) {
      throw ApiErrorCode.MAXIMUM_INDEXES_FOR_TENANT_EXCEEDED.exception(envelope);
    }

    if (e.getErrorCode() == NDSErrorCode.MISSING_ATTRIBUTE) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(envelope, e.getMessageParams());
    }

    if (e.getErrorCode().equals(NDSErrorCode.FTS_DEPLOYMENT_DESCRIPTION_MIGRATION_IN_PROGRESS)) {
      final String errDescription = (String) e.getMessageParams().get(0);
      throw ApiErrorCode.ATLAS_SEARCH_DEPLOYMENT_CONFLICT.exception(envelope, errDescription);
    }

    if (e.getErrorCode().equals(NDSErrorCode.FTS_TRANSIENT_ERROR)) {
      throw ApiErrorCode.ATLAS_SEARCH_TRANSIENT_ERROR.exception(envelope);
    }

    if (e.getErrorCode().equals(NDSErrorCode.FTS_INDEX_DELETION_ALREADY_REQUESTED)) {
      throw ApiErrorCode.ATLAS_SEARCH_INDEX_DELETION_ALREADY_REQUESTED.exception(envelope);
    }

    if (e.getErrorCode().equals(NDSErrorCode.FTS_TOO_MANY_INDEXES)) {
      throw ApiErrorCode.ATLAS_SEARCH_TOO_MANY_INDEXES.exception(envelope);
    }

    if (e.getErrorCode().equals(NDSErrorCode.COLLECTION_NOT_FOUND)) {
      throw ApiErrorCode.ATLAS_SEARCH_COLLECTION_NOT_FOUND.exception(
          envelope, e.getMessageParams().toArray());
    }

    if (e.getErrorCode().equals(NDSErrorCode.ROOT_COLLECTION_NOT_STANDARD_COLLECTION)) {
      throw ApiErrorCode.ATLAS_SEARCH_ROOT_COLLECTION_NOT_STANDARD_COLLECTION.exception(
          envelope, e.getMessageParams().toArray());
    }

    if (e.getErrorCode().equals(NDSErrorCode.VIEW_DEPTH_LIMIT_EXCEEDED)) {
      throw ApiErrorCode.ATLAS_SEARCH_VIEW_DEPTH_LIMIT_EXCEEDED.exception(
          envelope, e.getMessageParams().toArray());
    }

    if (e.getErrorCode().equals(NDSErrorCode.INVALID_VIEW_PIPELINE)) {
      throw ApiErrorCode.ATLAS_SEARCH_INVALID_VIEW_PIPELINE.exception(
          envelope, e.getMessageParams().toArray());
    }

    if (e.getErrorCode().equals(NDSErrorCode.SEARCH_ON_PAUSED_CLUSTER_NOT_SUPPORTED)) {
      throw ApiErrorCode.ATLAS_SEARCH_ON_PAUSED_CLUSTER.exception(envelope);
    }

    NDSErrorCodeUtils.handleGeneralExceptions(e, envelope);
  }

  private void handleServerlessInstanceFTSNotSupportedException(
      ObjectId groupId, final String clusterName, final Boolean envelope) {
    if (this.appSettings.isServerlessEnabled()) {
      throw ApiErrorCode.CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API.exception(
          envelope, clusterName);
    } else {
      throw ApiErrorCode.CLUSTER_NAME_NOT_FOUND.exception(envelope, clusterName, groupId);
    }
  }

  private void setAdminApiTelemetryAttributes(
      final HttpServletRequest request, final FTSIndex.Type type) {}
}
