package com.xgen.cloud.nds.temporarydownloadlinks.runtime.res;

import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.util._public.util.NetUtils;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited;
import com.xgen.cloud.nds.temporarydownloadlinks._public.model.TemporaryDownloadLink;
import com.xgen.cloud.nds.temporarydownloadlinks._public.model.TemporaryDownloadLink.Type;
import com.xgen.cloud.nds.temporarydownloadlinks._public.svc.TemporaryDownloadLinkSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HEAD;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.ResponseBuilder;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;

@Path("/download/temp")
@Singleton
public class TemporaryDownloadLinkResource {

  static final String RATE_LIMIT_POLICY_PREFIX = "mms.tempDownloadLinkService";
  private final TemporaryDownloadLinkSvc temporaryDownloadLinkSvc;

  @Inject
  public TemporaryDownloadLinkResource(final TemporaryDownloadLinkSvc temporaryDownloadLinkSvc) {
    this.temporaryDownloadLinkSvc = temporaryDownloadLinkSvc;
  }

  @GET
  @Path("/S3/{key: .*}")
  @UiCall(auth = false)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "getS3DownloadLink",
      types = {RateLimited.Type.IP})
  public Response getS3DownloadLink(
      @Context HttpServletRequest request,
      @PathParam("key") final String key,
      @QueryParam("token") final String token) {
    final Pair<Long, Long> range = parseRangeHeader(request.getHeader(HttpHeaders.RANGE));
    return resolveTemporaryDownloadLink(token, Type.S3, key, request.getRemoteAddr())
        .flatMap(l -> temporaryDownloadLinkSvc.downloadFile(l, range.getLeft(), range.getRight()))
        .map(
            dl -> {
              final ResponseBuilder response =
                  Response.status(
                      dl.headers().containsKey(HttpHeaders.CONTENT_RANGE)
                          ? HttpStatus.SC_PARTIAL_CONTENT
                          : HttpStatus.SC_OK);
              response.header(HttpHeaders.ACCEPT_RANGES, "bytes");
              dl.headers().forEach(response::header);
              return response.entity(dl.inputStream()).build();
            })
        .orElseGet(TemporaryDownloadLinkResource::notFound);
  }

  @HEAD
  @Path("/S3/{key: .*}")
  @UiCall(auth = false)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "headS3DownloadLink",
      types = {RateLimited.Type.IP})
  public Response headS3DownloadLink(
      @Context HttpServletRequest request,
      @PathParam("key") final String key,
      @QueryParam("token") final String token) {
    return resolveTemporaryDownloadLink(token, Type.S3, key, request.getRemoteAddr())
        .flatMap(temporaryDownloadLinkSvc::headFileHeaders)
        .map(
            headers -> {
              final ResponseBuilder response =
                  Response.ok().header(HttpHeaders.ACCEPT_RANGES, "bytes");
              headers.forEach(response::header);
              return response.build();
            })
        .orElseGet(TemporaryDownloadLinkResource::notFound);
  }

  private Optional<? extends TemporaryDownloadLink> resolveTemporaryDownloadLink(
      final String token,
      final TemporaryDownloadLink.Type type,
      final String key,
      final String ipAddress) {
    return temporaryDownloadLinkSvc
        .findS3DownloadLinkByToken(token)
        .filter(l -> l.getKey().equals(key))
        .filter(l -> l.getType().equals(type))
        // make sure it's not expired
        .filter(l -> l.getExpirationDate().after(new Date()))
        // Blocked IP also results in 404 so as not to leak information
        .filter(
            l -> {
              try {
                return l.isIpAllowed(
                    NetUtils.getIPv4Address(ipAddress).orElseThrow().getHostAddress());
              } catch (Exception pE) {
                throw new IllegalArgumentException("Invalid client IP");
              }
            });
  }

  private static Pair<Long, Long> parseRangeHeader(final String rangeStr) {
    return Optional.ofNullable(rangeStr)
        .filter(r -> r.startsWith("bytes="))
        .map(r -> r.substring("bytes=".length()).split("-"))
        .map(
            parts ->
                Pair.of(
                    Long.parseLong(parts[0]), parts.length > 1 ? Long.parseLong(parts[1]) : null))
        .orElseGet(() -> Pair.of(null, null));
  }

  private static Response notFound() {
    return Response.status(HttpStatus.SC_NOT_FOUND).build();
  }
}
