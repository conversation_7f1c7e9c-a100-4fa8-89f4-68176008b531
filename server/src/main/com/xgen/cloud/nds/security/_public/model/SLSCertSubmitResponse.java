package com.xgen.cloud.nds.security._public.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Response model for SLS certificate request submission. Returns the request ID for tracking the
 * certificate request.
 */
@Schema(description = "SLS Certificate Submit Response.")
public class SLSCertSubmitResponse {

  @JsonProperty(FieldDefs.REQUEST_ID)
  @Schema(description = "Request ID for tracking the certificate request.")
  private String _requestId;

  public SLSCertSubmitResponse() {}

  public SLSCertSubmitResponse(final String pRequestId) {
    _requestId = pRequestId;
  }

  public static class FieldDefs {
    public static final String REQUEST_ID = "requestId";
  }

  public String getRequestId() {
    return _requestId;
  }

  public void setRequestId(final String pRequestId) {
    _requestId = pRequestId;
  }

  @Override
  public String toString() {
    return "SLSCertSubmitResponse{" + "requestId='" + _requestId + '\'' + '}';
  }
}
