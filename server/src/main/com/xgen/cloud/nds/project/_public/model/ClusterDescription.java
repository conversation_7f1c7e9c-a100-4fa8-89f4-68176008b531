package com.xgen.cloud.nds.project._public.model;

import static com.xgen.cloud.common.util._public.util.CollectionUtils.findOnlyElement;
import static com.xgen.cloud.nds.common._public.model.INDSDefaults.PAUSE_IDLE_M0_CLUSTER_EMAIL_WARNING_BUFFER;

import com.amazonaws.services.ec2.model.VolumeType;
import com.google.common.annotations.VisibleForTesting;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.common.model._public.access.EmployeeAccessGrantType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.util._public.json.JsonUtils;
import com.xgen.cloud.common.util._public.util.SetUtils;
import com.xgen.cloud.deployment._public.model.IndexConfig;
import com.xgen.cloud.deployment._public.model.diff.ItemDiff;
import com.xgen.cloud.deployment._public.model.diff.ItemDiff.Status;
import com.xgen.cloud.deployment._public.model.diff.ItemDiffs;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.ClusterDescriptionBackupOptions;
import com.xgen.cloud.nds.cloudprovider._public.model.ClusterDescriptionProviderOptions;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSizeTier;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceFamilyClass;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.ShardRegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoIndexing;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleCrossCloudInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSizeBuilder;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSizeBuilderFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleSingleCloudInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ComputeAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.DiskGBAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.cloudprovider._public.model.xcloud.CrossCloudInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.util.RegionNameUtil;
import com.xgen.cloud.nds.common._public.model.BiConnectorReadPreference;
import com.xgen.cloud.nds.common._public.model.ComplianceLevel;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.ExtendedDiskRestrictions;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.INDSDefaults;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.SubdomainLevel;
import com.xgen.cloud.nds.common._public.model.MongoDBConfigType;
import com.xgen.cloud.nds.common._public.model.NDSLabel;
import com.xgen.cloud.nds.common._public.model.NDSModelCodec.NDSModel;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.common._public.model.ProxyProtocolForPrivateLinkMode;
import com.xgen.cloud.nds.common._public.model.ReplicaSetScalingStrategy;
import com.xgen.cloud.nds.common._public.model.error.MissingReplicationSpecException;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyStatus;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.sampledataset._public.model.SampleDatasetLoadStatus.SampleDataset;
import com.xgen.cloud.partnerintegrations.common._public.model.IntegrationType;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import dev.morphia.mapping.Mapper;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;

/**
 * Represents the goal state of the cluster, serving as a base model for both customer-defined and
 * planner-defined updates.
 *
 * <p>There are two primary perspectives of goal state representation:
 *
 * <ul>
 *   <li><b>Customer's Goal State:</b> Represented by {@code ClusterDescriptionUpdate}, which allows
 *       UI/API updates to be staged before affecting the actual {@code ClusterDescription}.
 *   <li><b>Planner's Goal State:</b> Maintained within {@code ClusterDescription} itself. This
 *       state is updated at the start of planning and should not be modified afterward. Ideally,
 *       updates should be applied via {@code ClusterDescriptionUpdateDao} to ensure consistency.
 * </ul>
 *
 * <p>Direct modifications to {@code ClusterDescription} should be avoided because:
 *
 * <ul>
 *   <li>The planner might be in the middle of a plan and may not take the change into account.
 *   <li>Staged updates might overwrite changes when merged into the existing configuration.
 * </ul>
 *
 * <p>Note that while the goal state is considered fixed during planning and execution, this
 * constraint does not necessarily apply to other goal state documents.
 */
public class ClusterDescription implements NDSModel {

  public static final List<State> ALL_STATES_EXCEPT_DELETED =
      Arrays.stream(State.values())
          .filter(s -> !s.equals(State.DELETED))
          .collect(Collectors.toList());

  public static final List<State> ALL_STATES =
      Arrays.stream(State.values()).collect(Collectors.toList());

  public static final String VALID_NAME_REGEX = "^[a-zA-Z0-9][a-zA-Z0-9-]*$";
  public static final Pattern VALID_NAME_PATTERN = Pattern.compile(VALID_NAME_REGEX);

  private static final String MIN_AUTO_INDEXING_INSTANCE_SIZE = "M10";
  private static final String MAX_AUTO_INDEXING_INSTANCE_SIZE = "M30";

  public static final String MORE_THAN_ONE_CLOUD_PROVIDER_ERROR_MESSAGE =
      "More than one cloud provider found.";

  private static final Logger LOG = LoggerFactory.getLogger(ClusterDescription.class);

  private static final Mapper _morphiaMapper = new Mapper();

  private final String _name;
  private final ObjectId _groupId;
  private final ObjectId _uniqueId;
  private final Date _createDate;
  private final Date _lastUpdateDate;
  private final Date _deleteAfterDate;
  private final VersionUtils.Version _mongoDBVersion;
  private final String _mongoDBMajorVersion;
  private final FixedVersion _fixedMongoDBVersion;
  private final FixedVersion _fixedFeatureCompatibilityVersion;
  private final FixedVersion _fixedCpuArch;
  private final FixedVersion _fixedOs;
  private final FixedVersion _fixedACMEProvider;
  private final double _diskSizeGB;
  private final double _customerProvidedDiskSizeGB;
  private final String[] _mongoDBUriHosts;
  private final Date _mongoDBUriHostsLastUpdateDate;
  private final State _state;
  private final ClusterType _clusterType;
  private final InternalClusterRole _internalClusterRole;
  private final boolean _deleteRequested;
  private final DeleteClusterReason _deleteReason;
  private final boolean _backupEnabled;
  private final boolean _diskBackupEnabled;
  private final boolean _pitEnabled;
  private final List<ObjectId> _restoreJobIds;
  private final RestoreJobType _restoreJobType;
  private final Date _deletedDate;
  private final Date _needsMongoDBConfigPublishAfter;
  private final Date _ensureClusterConnectivityAfter;
  private final ProcessRestartAllowedState _needsMongoDBConfigPublishRestartAllowed;
  private final boolean _isMTM;
  private final boolean _isMTMSentinel;
  private final boolean _isEligibleForReducedFlexPricing;

  // if true, the cluster is requested to be paused, not necessarily paused yet.
  // use _pausedDate instead to check if pause is completed or not.
  private final boolean _isPaused;
  private final Date _pausedDate;
  private final BiConnector _biConnector;
  private final String _srvAddress;
  private final Set<ClusterTag> _clusterTags;
  private final GeoSharding _geoSharding;
  private final EncryptionAtRestProvider _encryptionAtRestProvider;
  private final List<IndexConfig> _pendingIndexes;
  private final EmployeeAccessGrant _employeeAccessGrant;
  private final LogRetention _logRetention;
  private final List<NDSLabel> _labels;
  private final HostnameScheme _hostnameSchemeForAgents;
  private final SubdomainLevel _hostnameSubdomainLevel;
  private final String _deploymentClusterName;
  private final String[] _privateMongoDBUriHosts;
  private final String _privateSrvAddress;
  private final Map<String, List<String>> _privateMongoDBUriHostsMap;
  private final Map<String, String> _privateSrvAddressMap;
  private final boolean _stopContinuousBackup;
  private final Date _forceReplicaSetReconfig;
  private final List<String> _forceReplicaSetReconfigHostsToSkip;
  private final Date _resurrectRequested;
  private final ResurrectOptions _resurrectOptions;
  private final boolean _accessTemporarilyRevoked;
  private final Integer _replicaSetVersionOverride;
  private final RootCertType _rootCertType;
  private final VersionReleaseSystem _versionReleaseSystem;
  private final Optional<String> _continuousDeliveryFCV;
  private final Set<String> _migrateFromAvailabilitySets;
  private final String _loadBalancedHostname;
  private final String _loadBalancedMeshHostname;
  private final List<String> _shardsDraining;
  private final Date _cancelShardDrainRequested;
  private final Optional<ClusterDescriptionProviderOptions> _freeTenantProviderOptions;
  private final Optional<ClusterDescriptionProviderOptions> _serverlessTenantProviderOptions;
  private final Optional<ClusterDescriptionBackupOptions> _serverlessBackupOptions;
  private final Optional<ClusterDescriptionProviderOptions> _flexTenantProviderOptions;
  private final OsTunedFileOverrides _osTunedFileOverrides;
  private final String _dnsPin;
  private final String _clusterNamePrefix;
  private final ClusterProvisionType _clusterProvisionType;
  // For cross-cloud clusters. In the future will replace provider-specific _replicationSpecs field.
  private final List<ReplicationSpec> _replicationSpecList;
  private final Date _needsServerlessSnapshotForPit;
  private final Date _needsEnvoySyncAfter;

  private final boolean _terminationProtectionEnabled;
  private final Date _needsSampleDataLoadAfter;
  private final Boolean _createSampleSearchIndex;
  private final SampleDataset _sampleDatasetToLoad;
  private final Date _lastDataValidationDate;
  private final Date _lastDbCheckDate;
  private final Date _needsDbCheckAfter;
  private final int _dbCheckPreflightRetryCount;
  private final Optional<DiskWarmingMode> _diskWarmingMode;
  private final List<BumperFileOverride> _bumperFileOverrides;
  private final ReplicaSetScalingStrategy _replicaSetScalingStrategy;
  private final AutoScalingMode _autoScalingMode;
  private final String _osPolicyVersion;
  private final Boolean _isCriticalOSPolicyRelease;
  private final Optional<Boolean> _redactClientLogData;
  private final List<Integer> _cpuSocketBinding;
  private final Date _needsPrioritiesResetForPriorityTakeoverAfter;
  private final boolean _useAwsTimeBasedSnapshotCopyForFastInitialSync;

  // _allowUnsafeRollingOperation determines whether the unsafeRollingOperation flag can be set on
  // the Agent process during a crash loop scenario. This property is set by an admin after a
  // crash loop has been detected
  private final boolean _allowUnsafeRollingOperation;

  // NB(pt): FlexTenantMigrationState data structure is subject to change as the flex
  // Migration Project kicks off. True as of 2024/07/16.
  private final FlexTenantMigrationState _flexTenantMigrationState;

  // Used to indicate if IPAM IPs should be reserved for all nodes
  // in the cluster that are currently on AWS IPs.
  private final boolean _reserveIpamIpRequested;
  private final ClusterConnectionStringConfiguration _clusterConnectionStringConfiguration;
  private final boolean _gtsRollout;

  private final Optional<SwapIpMaintenanceRound> _swapIpMaintenanceRoundCompleted;
  private final boolean _freeFromServerless;
  private final StorageSystem _storageSystem;
  private final MongotuneStatus _mongotuneStatus;
  private final Date _needsMongotuneConfigPublishAfter;

  private final ProxyProtocolForPrivateLinkMode _proxyProtocolForPrivateLinkMode;
  @Nullable private final PartnerIntegrationsData _partnerIntegrationsData;

  private final Optional<Date> _alwaysManagedDefaultRWConcernSince;
  @Nullable private final AutoSharding _autoSharding;

  private final EffectiveFields _useEffectiveClusterFields;

  /**
   * Whether this cluster is eligible for gateway router. True for AWS-only clusters when the
   * feature is enabled.
   */
  private final Boolean _gatewayRouterEligible;

  /**
   * Date when gateway router eligibility was last updated. Updated when eligibility changes due to
   * cluster configuration changes.
   */
  private final Date _gatewayRouterEligibilityLastUpdateDate;

  public ClusterDescription(final BasicDBObject pDBObject) {
    final BasicDBObject id = (BasicDBObject) pDBObject.get(FieldDefs.ID);
    _name = id.getString(FieldDefs.NAME);
    _groupId = id.getObjectId(FieldDefs.GROUP_ID);
    _uniqueId = pDBObject.getObjectId(FieldDefs.UNIQUE_ID);
    _createDate = pDBObject.getDate(FieldDefs.CREATE_DATE);
    _lastUpdateDate = pDBObject.getDate(FieldDefs.LAST_UPDATE_DATE);
    _deleteAfterDate = pDBObject.getDate(FieldDefs.DELETE_AFTER_DATE);
    _mongoDBVersion = VersionUtils.parse(pDBObject.getString(FieldDefs.MONGODB_VERSION));
    _mongoDBMajorVersion = pDBObject.getString(FieldDefs.MONGODB_MAJOR_VERSION);
    _fixedMongoDBVersion =
        pDBObject.get(FieldDefs.FIXED_MONGODB_VERSION) == null
            ? null
            : new FixedVersion((BasicDBObject) pDBObject.get(FieldDefs.FIXED_MONGODB_VERSION));
    _fixedFeatureCompatibilityVersion =
        pDBObject.get(FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION) == null
            ? null
            : new FixedVersion(
                (BasicDBObject) pDBObject.get(FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION));
    _fixedCpuArch =
        pDBObject.get(FieldDefs.FIXED_CPU_ARCH) == null
            ? null
            : new FixedVersion((BasicDBObject) pDBObject.get(FieldDefs.FIXED_CPU_ARCH));
    _fixedOs =
        pDBObject.get(FieldDefs.FIXED_OS) == null
            ? null
            : new FixedVersion((BasicDBObject) pDBObject.get(FieldDefs.FIXED_OS));
    _fixedACMEProvider =
        pDBObject.get(FieldDefs.FIXED_ACME_PROVIDER) == null
            ? null
            : new FixedVersion((BasicDBObject) pDBObject.get(FieldDefs.FIXED_ACME_PROVIDER));
    _diskSizeGB = pDBObject.getDouble(FieldDefs.DISK_SIZE_GB);
    _customerProvidedDiskSizeGB = pDBObject.getDouble(FieldDefs.CUSTOMER_PROVIDED_DISK_SIZE_GB, 0);
    _mongoDBUriHosts =
        ((BasicDBList) pDBObject.get(FieldDefs.MONGODB_URI_HOSTS)).toArray(new String[0]);
    _mongoDBUriHostsLastUpdateDate = pDBObject.getDate(FieldDefs.MONGODB_URI_LAST_UPDATE_DATE);
    _state = State.valueOf(pDBObject.getString(FieldDefs.STATE));
    _clusterType = ClusterType.valueOf(pDBObject.getString(FieldDefs.CLUSTER_TYPE));
    _internalClusterRole =
        InternalClusterRole.valueOf(pDBObject.getString(FieldDefs.INTERNAL_CLUSTER_ROLE));
    _deleteRequested = pDBObject.getBoolean(FieldDefs.DELETE_REQUESTED);
    _deleteReason =
        pDBObject.getString(FieldDefs.DELETE_REASON) == null
                || pDBObject.getString(FieldDefs.DELETE_REASON).isEmpty()
            ? null
            : DeleteClusterReason.valueOf(pDBObject.getString(FieldDefs.DELETE_REASON));
    _backupEnabled = pDBObject.getBoolean(FieldDefs.BACKUP_ENABLED);
    _diskBackupEnabled = pDBObject.getBoolean(FieldDefs.DISK_BACKUP_ENABLED);
    _pitEnabled = pDBObject.getBoolean(FieldDefs.PIT_ENABLED);
    _isMTM = pDBObject.getBoolean(FieldDefs.IS_MTM);
    _isMTMSentinel = pDBObject.getBoolean(FieldDefs.IS_MTM_SENTINEL);
    _isEligibleForReducedFlexPricing =
        pDBObject.getBoolean(FieldDefs.IS_ELIGIBLE_FOR_REDUCED_FLEX_PRICING);
    _isPaused = pDBObject.getBoolean(FieldDefs.IS_PAUSED);
    _pausedDate = pDBObject.getDate(FieldDefs.PAUSED_DATE);
    _biConnector = new BiConnector((BasicDBObject) pDBObject.get(FieldDefs.BI_CONNECTOR));
    _srvAddress = pDBObject.getString(FieldDefs.SRV_ADDRESS);
    _clusterTags =
        ((BasicDBList) pDBObject.getOrDefault(FieldDefs.CLUSTER_TAGS, new BasicDBList()))
            .stream().map(String.class::cast).map(ClusterTag::valueOf).collect(Collectors.toSet());

    final BasicDBList dbRestoreJobIds =
        pDBObject.get(FieldDefs.RESTORE_JOB_IDS) != null
            ? (BasicDBList) pDBObject.get(FieldDefs.RESTORE_JOB_IDS)
            : new BasicDBList();

    _restoreJobIds =
        dbRestoreJobIds.stream().map(ObjectId.class::cast).collect(Collectors.toList());
    final String restoreJobTypeStr = pDBObject.getString(FieldDefs.RESTORE_JOB_TYPE);
    _restoreJobType = restoreJobTypeStr == null ? null : RestoreJobType.valueOf(restoreJobTypeStr);
    _deletedDate = pDBObject.getDate(FieldDefs.DELETED_DATE);
    _ensureClusterConnectivityAfter =
        pDBObject.getDate(FieldDefs.ENSURE_CLUSTER_CONNECTIVITY_AFTER);
    _needsMongoDBConfigPublishAfter =
        pDBObject.getDate(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER);
    _needsMongoDBConfigPublishRestartAllowed =
        ProcessRestartAllowedState.valueOf(
            pDBObject.getString(
                FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                ProcessRestartAllowedState.IMMEDIATE.name()));
    _geoSharding = new GeoSharding((BasicDBObject) pDBObject.get(FieldDefs.GEO_SHARDING));
    _encryptionAtRestProvider =
        EncryptionAtRestProvider.valueOf(
            pDBObject.getString(FieldDefs.ENCRYPTION_AT_REST_PROVIDER));
    _pendingIndexes =
        ((BasicDBList) pDBObject.getOrDefault(FieldDefs.PENDING_INDEXES, new BasicDBList()))
            .stream()
                .map(BasicDBObject.class::cast)
                .map(pi -> _morphiaMapper.fromDBObject(null, IndexConfig.class, pi, null))
                .collect(Collectors.toList());

    final BasicDBObject employeeAccessGrant =
        (BasicDBObject) pDBObject.get(FieldDefs.EMPLOYEE_ACCESS_GRANT);
    if (employeeAccessGrant != null) {
      _employeeAccessGrant =
          _morphiaMapper.fromDBObject(null, EmployeeAccessGrant.class, employeeAccessGrant, null);
    } else {
      _employeeAccessGrant = null;
    }

    _logRetention =
        LogRetention.valueOf(
            pDBObject.getString(FieldDefs.LOG_RETENTION, LogRetention.DAYS30.name()));

    final BasicDBList dbLabels =
        pDBObject.get(FieldDefs.LABELS) != null
            ? (BasicDBList) pDBObject.get(FieldDefs.LABELS)
            : new BasicDBList();
    _labels =
        dbLabels.stream()
            .map(doc -> new NDSLabel((BasicDBObject) doc))
            .collect(Collectors.toList());
    _hostnameSchemeForAgents =
        Optional.ofNullable(pDBObject.getString(FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS))
            .map(InstanceHostname.HostnameScheme::valueOf)
            .orElse(null);
    _hostnameSubdomainLevel =
        Optional.ofNullable(pDBObject.getString(FieldDefs.HOSTNAME_SUBDOMAIN_LEVEL))
            .map(InstanceHostname.SubdomainLevel::valueOf)
            .orElse(null);
    _deploymentClusterName = pDBObject.getString(FieldDefs.DEPLOYMENT_CLUSTER_NAME);

    _privateMongoDBUriHosts =
        ((BasicDBList) pDBObject.get(FieldDefs.PRIVATE_MONGODB_URI_HOSTS)).toArray(new String[0]);

    _privateSrvAddress = pDBObject.getString(FieldDefs.PRIVATE_SRV_ADDRESS);

    final BasicDBObject privateURIHostMapObject =
        pDBObject.get(FieldDefs.PRIVATE_MONGODB_URI_HOSTS_MAP) != null
            ? (BasicDBObject) pDBObject.get(FieldDefs.PRIVATE_MONGODB_URI_HOSTS_MAP)
            : new BasicDBObject();

    _privateMongoDBUriHostsMap =
        decodePrivateEndpointIDMapWithTransform(
            privateURIHostMapObject,
            key -> {
              final BasicDBList list = (BasicDBList) privateURIHostMapObject.get(key);
              return list.stream().map(Object::toString).collect(Collectors.toList());
            });

    final BasicDBObject privateSrvMapObject =
        pDBObject.get(FieldDefs.PRIVATE_SRV_ADDRESS_MAP) != null
            ? (BasicDBObject) pDBObject.get(FieldDefs.PRIVATE_SRV_ADDRESS_MAP)
            : new BasicDBObject();

    _privateSrvAddressMap = decodePrivateEndpointIDMap(privateSrvMapObject);

    _stopContinuousBackup = pDBObject.getBoolean(FieldDefs.STOP_CONTINUOUS_BACKUP, false);

    _replicationSpecList = readCrossCloudReplicationSpecs(pDBObject);
    _forceReplicaSetReconfig =
        pDBObject.containsField(FieldDefs.FORCE_REPLICA_SET_RECONFIG_REQUEST_DATE)
            ? pDBObject.getDate(FieldDefs.FORCE_REPLICA_SET_RECONFIG_REQUEST_DATE)
            : Optional.ofNullable(pDBObject.get(FieldDefs.FORCE_REPLICA_SET_RECONFIG))
                .filter(reconfig -> reconfig == Boolean.TRUE)
                .map(r -> new Date())
                .orElse(null);
    _forceReplicaSetReconfigHostsToSkip =
        ((List<?>)
                pDBObject.getOrDefault(
                    FieldDefs.FORCE_REPLICA_SET_RECONFIG_HOSTS_TO_SKIP, List.of()))
            .stream().map(String.class::cast).collect(Collectors.toList());
    _resurrectRequested = pDBObject.getDate(FieldDefs.RESURRECT_REQUESTED);
    _resurrectOptions =
        Optional.ofNullable(pDBObject.get(FieldDefs.RESURRECT_OPTIONS))
            .map(BasicDBObject.class::cast)
            .map(ResurrectOptions::new)
            .orElse(null);

    final Object replicaSetVersionOverride = pDBObject.get(FieldDefs.REPLICA_SET_VERSION_OVERRIDE);
    _replicaSetVersionOverride =
        replicaSetVersionOverride != null ? (Integer) replicaSetVersionOverride : null;
    _rootCertType =
        RootCertType.valueOf(
            pDBObject.getString(FieldDefs.ROOT_CERT_TYPE, RootCertType.ISRGROOTX1.name()));

    _versionReleaseSystem =
        VersionReleaseSystem.valueOf(
            pDBObject.getString(FieldDefs.VERSION_RELEASE_SYSTEM, VersionReleaseSystem.LTS.name()));
    _continuousDeliveryFCV =
        Optional.ofNullable(pDBObject.getString(FieldDefs.CONTINUOUS_DELIVERY_FCV));

    final Object mfasObj = pDBObject.get(FieldDefs.MIGRATE_FROM_AVAILABILITY_SETS);
    final BasicDBList migrationFromAvailabilitySets =
        mfasObj != null ? (BasicDBList) mfasObj : new BasicDBList();
    _migrateFromAvailabilitySets =
        migrationFromAvailabilitySets.stream().map(String.class::cast).collect(Collectors.toSet());
    _loadBalancedHostname = pDBObject.getString(FieldDefs.LOAD_BALANCED_HOSTNAME);
    _loadBalancedMeshHostname = pDBObject.getString(FieldDefs.LOAD_BALANCED_MESH_HOSTNAME);

    final BasicDBList dbShardsDraining = (BasicDBList) pDBObject.get(FieldDefs.SHARDS_DRAINING);

    _shardsDraining =
        (dbShardsDraining != null)
            ? dbShardsDraining.stream().map(String.class::cast).collect(Collectors.toList())
            : new ArrayList<>();

    _cancelShardDrainRequested = pDBObject.getDate(FieldDefs.CANCEL_SHARD_DRAIN_REQUESTED);

    final CloudProvider tenantCloudProvider =
        // only one tenant provider can be present in the set of cloud providers
        SetUtils.intersection(
                getCloudProviders(),
                Set.of(CloudProvider.FREE, CloudProvider.SERVERLESS, CloudProvider.FLEX))
            .stream()
            .findFirst()
            .orElse(null);
    final CloudProviderProvider tenantProviderProvider;
    final Optional<ClusterDescriptionProviderOptions> providerOptions;
    if (tenantCloudProvider != null) {
      tenantProviderProvider = CloudProviderRegistry.getByCloudProvider(tenantCloudProvider);
      providerOptions =
          Optional.of(
              tenantProviderProvider.getClusterDescriptionProviderOptions(
                  (BasicDBObject)
                      pDBObject.getOrDefault(FieldDefs.CLOUD_PROVIDER_OPTIONS, new BasicDBObject()),
                  getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow()));
    } else {
      tenantProviderProvider = null;
      providerOptions = Optional.empty();
    }
    if (tenantCloudProvider == CloudProvider.FREE) {
      _freeTenantProviderOptions = providerOptions;
    } else {
      _freeTenantProviderOptions = Optional.empty();
    }
    if (tenantCloudProvider == CloudProvider.SERVERLESS) {
      _serverlessTenantProviderOptions = providerOptions;
      _serverlessBackupOptions =
          Optional.of(
              tenantProviderProvider.getClusterDescriptionBackupOptions(
                  (BasicDBObject)
                      pDBObject.getOrDefault(
                          FieldDefs.SERVERLESS_BACKUP_OPTIONS,
                          tenantProviderProvider.getClusterDescriptionDefaultBackupOptions())));
    } else {
      _serverlessTenantProviderOptions = Optional.empty();
      _serverlessBackupOptions = Optional.empty();
    }
    if (tenantCloudProvider == CloudProvider.FLEX) {
      _flexTenantProviderOptions = providerOptions;
    } else {
      _flexTenantProviderOptions = Optional.empty();
    }

    _accessTemporarilyRevoked =
        isServerlessTenantCluster()
            && getServerlessTenantProviderOptions().getAccessTemporarilyRevoked();

    final BasicDBObject osTunedFileOverrides =
        pDBObject.get(FieldDefs.OS_TUNED_FILE_OVERRIDES) == null
            ? new BasicDBObject()
            : (BasicDBObject) pDBObject.get(FieldDefs.OS_TUNED_FILE_OVERRIDES);
    _osTunedFileOverrides = new OsTunedFileOverrides(osTunedFileOverrides);
    _dnsPin = pDBObject.getString(FieldDefs.DNS_PIN);
    _clusterNamePrefix = pDBObject.getString(FieldDefs.CLUSTER_NAME_PREFIX);
    _clusterProvisionType =
        ClusterProvisionType.valueOf(
            pDBObject.getString(
                FieldDefs.CLUSTER_PROVISION_TYPE, ClusterProvisionType.REGULAR.name()));

    _needsServerlessSnapshotForPit = pDBObject.getDate(FieldDefs.NEEDS_SERVERLESS_SNAPSHOT_FOR_PIT);
    _terminationProtectionEnabled =
        pDBObject.getBoolean(FieldDefs.TERMINATION_PROTECTION_ENABLED, false);
    _needsSampleDataLoadAfter = pDBObject.getDate(FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER, null);
    _createSampleSearchIndex = pDBObject.getBoolean(FieldDefs.CREATE_SAMPLE_SEARCH_INDEX, false);
    _needsEnvoySyncAfter = pDBObject.getDate(FieldDefs.NEEDS_ENVOY_SYNC_AFTER, null);
    _sampleDatasetToLoad =
        SampleDataset.valueOf(
            pDBObject.getString(FieldDefs.SAMPLE_DATASET_TO_LOAD, SampleDataset.DEFAULT.name()));
    _lastDataValidationDate = pDBObject.getDate(FieldDefs.LAST_DATA_VALIDATION_DATE, null);
    _lastDbCheckDate = pDBObject.getDate(FieldDefs.LAST_DB_CHECK_DATE, null);
    _needsDbCheckAfter = pDBObject.getDate(FieldDefs.NEEDS_DB_CHECK_AFTER, null);
    _dbCheckPreflightRetryCount = pDBObject.getInt(FieldDefs.DB_CHECK_PREFLIGHT_RETRY_COUNT, 0);
    final String diskWarmingModeValue = pDBObject.getString(FieldDefs.DISK_WARMING_MODE);
    _diskWarmingMode =
        diskWarmingModeValue == null
            ? Optional.empty()
            : Optional.ofNullable(DiskWarmingMode.valueOf(diskWarmingModeValue));
    _bumperFileOverrides =
        ((BasicDBList) pDBObject.getOrDefault(FieldDefs.BUMPER_FILE_OVERRIDES, new BasicDBList()))
            .stream()
                .map(BasicDBObject.class::cast)
                .map(BumperFileOverride::new)
                .collect(Collectors.toList());
    _replicaSetScalingStrategy =
        Optional.ofNullable(pDBObject.getString(FieldDefs.REPLICA_SET_SCALING_STRATEGY))
            .map(ReplicaSetScalingStrategy::valueOf)
            .orElse(null);
    _autoScalingMode =
        AutoScalingMode.valueOf(
            pDBObject.getString(FieldDefs.AUTO_SCALING_MODE, AutoScalingMode.CLUSTER.name()));
    _osPolicyVersion = pDBObject.getString(FieldDefs.OS_POLICY_VERSION);
    _isCriticalOSPolicyRelease =
        pDBObject.getBoolean(FieldDefs.IS_CRITICAL_OS_POLICY_RELEASE, false);
    // It is important to be able to preserve a null value for this attribute. In the current
    // implementation changing this attribute in a cluster will require a rolling restart even if
    // false. To avoid that, we need to be able to differentiate between set and unset (null)
    // values.
    _redactClientLogData =
        Optional.ofNullable((Boolean) pDBObject.get((FieldDefs.REDACT_CLIENT_LOG_DATA)));
    _useAwsTimeBasedSnapshotCopyForFastInitialSync =
        pDBObject.getBoolean(
            FieldDefs.USE_AWS_TIME_BASED_SNAPSHOT_COPY_FOR_FAST_INITIAL_SYNC, false);

    final BasicDBList cpuSocketBinding = (BasicDBList) pDBObject.get(FieldDefs.CPU_SOCKET_BINDING);
    _cpuSocketBinding =
        cpuSocketBinding != null
            ? cpuSocketBinding.stream()
                .filter(c -> c instanceof Integer)
                .map(Integer.class::cast)
                .collect(Collectors.toList())
            : List.of();

    final BasicDBObject flexTenantMigrationState =
        pDBObject.get(FieldDefs.FLEX_TENANT_MIGRATION_STATE) != null
            ? (BasicDBObject) pDBObject.get(FieldDefs.FLEX_TENANT_MIGRATION_STATE)
            : (BasicDBObject) pDBObject.get(FieldDefs.OLD_FLEX_TENANT_MIGRATION_STATE);

    _flexTenantMigrationState =
        Optional.ofNullable(flexTenantMigrationState)
            .map(FlexTenantMigrationState::new)
            .orElse(null);

    _gatewayRouterEligible = pDBObject.getBoolean(FieldDefs.GATEWAY_ROUTER_ELIGIBLE, false);
    _gatewayRouterEligibilityLastUpdateDate =
        pDBObject.getDate(FieldDefs.GATEWAY_ROUTER_ELIGIBILITY_LAST_UPDATE_DATE, null);

    _reserveIpamIpRequested = pDBObject.getBoolean(FieldDefs.RESERVE_IPAM_IP_REQUESTED, false);
    _swapIpMaintenanceRoundCompleted =
        Optional.ofNullable(pDBObject.getString(FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED))
            .map(SwapIpMaintenanceRound::valueOf);
    _clusterConnectionStringConfiguration =
        Optional.ofNullable(pDBObject.get(FieldDefs.CLUSTER_CONNECTION_STRING_CONFIGURATION))
            .map(BasicDBObject.class::cast)
            .map(ClusterConnectionStringConfiguration::new)
            .orElse(
                ClusterConnectionStringConfiguration
                    .getDefaultClusterConnectionStringConfiguration());
    _gtsRollout = pDBObject.getBoolean(FieldDefs.GTS_ROLLOUT, false);
    _freeFromServerless = pDBObject.getBoolean(FieldDefs.FREE_FROM_SERVERLESS, false);
    _needsPrioritiesResetForPriorityTakeoverAfter =
        pDBObject.getDate(FieldDefs.NEEDS_PRIORITIES_RESET_FOR_PRIORITY_TAKEOVER_AFTER, null);
    _allowUnsafeRollingOperation =
        pDBObject.getBoolean(FieldDefs.ALLOW_UNSAFE_ROLLING_OPERATION, false);
    _storageSystem =
        StorageSystem.filter(
            pDBObject.getString(FieldDefs.STORAGE_SYSTEM, StorageSystem.LOCAL.name()));
    _mongotuneStatus =
        Optional.ofNullable(pDBObject.get(FieldDefs.MONGOTUNE))
            .map(BasicDBObject.class::cast)
            .map(MongotuneStatus::new)
            .orElse(null);
    _needsMongotuneConfigPublishAfter =
        pDBObject.getDate(FieldDefs.NEEDS_MONGOTUNE_CONFIG_PUBLISH_AFTER, null);
    _proxyProtocolForPrivateLinkMode =
        ProxyProtocolForPrivateLinkMode.valueOf(
            pDBObject.getString(
                FieldDefs.PROXY_PROTOCOL_FOR_PRIVATE_LINK_MODE,
                ProxyProtocolForPrivateLinkMode.STANDARD.name()));

    _partnerIntegrationsData =
        Optional.ofNullable(pDBObject.get(FieldDefs.PARTNER_INTEGRATIONS_DATA))
            .map(BasicDBObject.class::cast)
            .map(PartnerIntegrationsData::new)
            .orElse(null);
    _alwaysManagedDefaultRWConcernSince =
        Optional.ofNullable(
            pDBObject.getDate(FieldDefs.ALWAYS_MANAGED_DEFAULT_RW_CONCERN_SINCE, null));
    _autoSharding =
        Optional.ofNullable(pDBObject.get(FieldDefs.AUTO_SHARDING))
            .map(BasicDBObject.class::cast)
            .map(AutoSharding::new)
            .orElse(null);
    _useEffectiveClusterFields =
        EffectiveFields.valueOf(
            pDBObject.getString(
                FieldDefs.USE_EFFECTIVE_CLUSTER_FIELDS, EffectiveFields.DISABLED.name()));
  }

  public static ClusterDescription getCloudProviderClusterDescription(
      final BasicDBObject pDBObject) {
    return ClusterDescriptionFactory.get(pDBObject);
  }

  public List<NDSInstanceSize> getShardInstanceSizeListForNodeType(NodeTypeFamily pNodeTypeFamily) {
    return getReplicationSpecsWithShardDataAndNodesOfFamily(pNodeTypeFamily).stream()
        .map(rs -> rs.getInstanceSizeByNodeTypeFamily(pNodeTypeFamily))
        .toList();
  }

  public Set<String> getMigrateFromAvailabilitySets() {
    return _migrateFromAvailabilitySets;
  }

  public Optional<String> getOSPolicyVersion() {
    return Optional.ofNullable(_osPolicyVersion);
  }

  public Optional<Boolean> getIsCriticalOSPolicyRelease() {
    return Optional.ofNullable(_isCriticalOSPolicyRelease);
  }

  public Optional<Boolean> getRedactClientLogData() {
    return _redactClientLogData;
  }

  public static List<ReplicationSpec> readCrossCloudReplicationSpecs(
      final BasicDBObject pClusterDescription) {
    final BasicDBList replicationSpecListObject =
        (BasicDBList) pClusterDescription.get(FieldDefs.REPLICATION_SPEC_LIST);

    return replicationSpecListObject.stream()
        .map(BasicDBObject.class::cast)
        .map(dbObject -> new ReplicationSpec(false, dbObject))
        .collect(Collectors.toList());
  }

  public Map<CloudProvider, List<RegionConfig>> getCloudProviderRegionConfigMap() {
    return getReplicationSpecsWithShardData().stream()
        .map(ReplicationSpec::getRegionConfigs)
        .flatMap(Collection::stream)
        .collect(Collectors.groupingBy(RegionConfig::getCloudProvider));
  }

  public MongoDBConfigType getMongoDBConfigType() {
    if (isTenantCluster()) {
      return MongoDBConfigType.PROXY;
    }
    return MongoDBConfigType.AUTOMATION;
  }

  public BasicDBObject getCloudProviderOptions() {
    if (isSharedTenantCluster()) {
      return getFreeTenantProviderOptions().toDBObject();
    }

    if (isServerlessTenantCluster()) {
      return getServerlessTenantProviderOptions().toDBObject();
    }

    if (isFlexTenantCluster()) {
      return getFlexTenantProviderOptions().toDBObject();
    }

    throw new IllegalStateException("Only tenant providers have cloudProviderOptions");
  }

  public BasicDBObject toDBObject() {
    final BasicDBObject id =
        new BasicDBObject()
            .append(FieldDefs.NAME, getName())
            .append(FieldDefs.GROUP_ID, getGroupId());

    final BasicDBList pendingIndexesDBList =
        getPendingIndexes().stream()
            .map(_morphiaMapper::toDBObject)
            .collect(DbUtils.toBasicDBList());

    final BasicDBObject privateLinkHostsMap =
        encodePrivateEndpointIDMapWithTransform(
            getPrivateMongoDBUriHostsMap(),
            (hosts) -> hosts.stream().collect(DbUtils.toBasicDBList()));

    final BasicDBObject dbObj =
        new BasicDBObject()
            .append(FieldDefs.ID, id)
            .append(FieldDefs.UNIQUE_ID, getUniqueId())
            .append(FieldDefs.CREATE_DATE, getCreateDate())
            .append(FieldDefs.LAST_UPDATE_DATE, getLastUpdateDate())
            .append(FieldDefs.DELETE_AFTER_DATE, getDeleteAfterDate().orElse(null))
            .append(FieldDefs.MONGODB_VERSION, getMongoDBVersion().getVersion())
            .append(FieldDefs.MONGODB_MAJOR_VERSION, getMongoDBMajorVersion())
            .append(
                FieldDefs.FIXED_MONGODB_VERSION,
                getFixedMongoDBVersion().map(FixedVersion::toDBObject).orElse(null))
            .append(
                FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION,
                getFixedFeatureCompatibilityVersion().map(FixedVersion::toDBObject).orElse(null))
            .append(
                FieldDefs.FIXED_CPU_ARCH,
                getFixedCpuArch().map(FixedVersion::toDBObject).orElse(null))
            .append(FieldDefs.FIXED_OS, getFixedOs().map(FixedVersion::toDBObject).orElse(null))
            .append(
                FieldDefs.FIXED_ACME_PROVIDER,
                getFixedACMEProvider().map(FixedVersion::toDBObject).orElse(null))
            .append(FieldDefs.DISK_SIZE_GB, getDiskSizeGB())
            .append(FieldDefs.CUSTOMER_PROVIDED_DISK_SIZE_GB, getCustomerProvidedDiskSizeGB())
            .append(FieldDefs.USE_EFFECTIVE_CLUSTER_FIELDS, getUseEffectiveClusterFields().name())
            .append(
                FieldDefs.MONGODB_URI_HOSTS,
                Arrays.stream(getMongoDBUriHosts()).collect(DbUtils.toBasicDBList()))
            .append(FieldDefs.MONGODB_URI_LAST_UPDATE_DATE, getMongoDBUriHostsLastUpdateDate())
            .append(FieldDefs.STATE, getState().name())
            .append(FieldDefs.CLUSTER_TYPE, getClusterType().name())
            .append(FieldDefs.INTERNAL_CLUSTER_ROLE, getInternalClusterRole().name())
            .append(FieldDefs.DELETE_REQUESTED, isDeleteRequested())
            .append(FieldDefs.DELETE_REASON, getDeleteReason().orElse(null))
            .append(FieldDefs.BACKUP_ENABLED, isBackupEnabled())
            .append(FieldDefs.DISK_BACKUP_ENABLED, isDiskBackupEnabled())
            .append(FieldDefs.PIT_ENABLED, isPitEnabled())
            .append(
                FieldDefs.RESTORE_JOB_IDS,
                getRestoreJobIds().stream().collect(DbUtils.toBasicDBList()))
            .append(
                FieldDefs.RESTORE_JOB_TYPE,
                getRestoreJobType().map(RestoreJobType::name).orElse(null))
            .append(FieldDefs.DELETED_DATE, getDeletedDate().orElse(null))
            .append(
                FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER,
                getNeedsMongoDBConfigPublishAfter().orElse(null))
            .append(
                FieldDefs.ENSURE_CLUSTER_CONNECTIVITY_AFTER,
                getEnsureClusterConnectivityAfter().orElse(null))
            .append(FieldDefs.IS_MTM, isMTM())
            .append(FieldDefs.IS_MTM_SENTINEL, isMTMSentinel())
            .append(
                FieldDefs.IS_ELIGIBLE_FOR_REDUCED_FLEX_PRICING, isEligibleForReducedFlexPricing())
            .append(FieldDefs.IS_PAUSED, isPaused())
            .append(FieldDefs.PAUSED_DATE, getPausedDate().orElse(null))
            .append(FieldDefs.BI_CONNECTOR, getBiConnector().toDBObject())
            .append(FieldDefs.SRV_ADDRESS, getSRVAddress().orElse(null))
            .append(
                FieldDefs.CLUSTER_TAGS,
                getClusterTags().stream().map(ClusterTag::name).collect(DbUtils.toBasicDBList()))
            .append(FieldDefs.GEO_SHARDING, getGeoSharding().toDBObject())
            .append(FieldDefs.ENCRYPTION_AT_REST_PROVIDER, getEncryptionAtRestProvider().name())
            .append(FieldDefs.PENDING_INDEXES, pendingIndexesDBList)
            .append(
                FieldDefs.EMPLOYEE_ACCESS_GRANT,
                getEmployeeAccessGrant().map(_morphiaMapper::toDBObject).orElse(null))
            .append(FieldDefs.LOG_RETENTION, getLogRetention().name())
            .append(
                FieldDefs.LABELS,
                getLabels().stream().map(NDSLabel::toDBObject).collect(DbUtils.toBasicDBList()))
            .append(
                FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
                getHostnameSchemeForAgents().map(Enum::name).orElse(null))
            .append(
                FieldDefs.HOSTNAME_SUBDOMAIN_LEVEL,
                getHostnameSubdomainLevel() == null ? null : getHostnameSubdomainLevel().name())
            .append(FieldDefs.DEPLOYMENT_CLUSTER_NAME, getDeploymentClusterName())
            .append(
                FieldDefs.PRIVATE_MONGODB_URI_HOSTS,
                Arrays.stream(getPrivateMongoDBUriHosts()).collect(DbUtils.toBasicDBList()))
            .append(FieldDefs.PRIVATE_SRV_ADDRESS, getPrivateSrvAddress())
            .append(FieldDefs.PRIVATE_MONGODB_URI_HOSTS_MAP, privateLinkHostsMap)
            .append(
                FieldDefs.PRIVATE_SRV_ADDRESS_MAP,
                encodePrivateEndpointIDMap(getPrivateSrvAddressMap()))
            .append(FieldDefs.STOP_CONTINUOUS_BACKUP, isStopContinuousBackup())
            .append(
                FieldDefs.FORCE_REPLICA_SET_RECONFIG_REQUEST_DATE,
                getForceReplicaSetReconfigVersion().orElse(null))
            .append(
                FieldDefs.FORCE_REPLICA_SET_RECONFIG_HOSTS_TO_SKIP,
                getForceReplicaSetReconfigHostsToSkip())
            .append(FieldDefs.RESURRECT_REQUESTED, getResurrectRequested().orElse(null))
            .append(
                FieldDefs.RESURRECT_OPTIONS,
                getResurrectOptions().map(ResurrectOptions::toDBObject).orElse(null))
            .append(
                FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                getNeedsMongoDBConfigPublishRestartAllowed().name())
            .append(FieldDefs.ROOT_CERT_TYPE, getRootCertType().name())
            .append(FieldDefs.VERSION_RELEASE_SYSTEM, getVersionReleaseSystem().name())
            .append(FieldDefs.CONTINUOUS_DELIVERY_FCV, getContinuousDeliveryFCV().orElse(null))
            .append(
                FieldDefs.MIGRATE_FROM_AVAILABILITY_SETS,
                getMigrateFromAvailabilitySets().stream().collect(DbUtils.toBasicDBList()))
            .append(FieldDefs.LOAD_BALANCED_HOSTNAME, getLoadBalancedHostname().orElse(null))
            .append(
                FieldDefs.LOAD_BALANCED_MESH_HOSTNAME, getLoadBalancedMeshHostname().orElse(null))
            .append(
                FieldDefs.SHARDS_DRAINING,
                getShardsDraining().stream().collect(DbUtils.toBasicDBList()))
            .append(
                FieldDefs.CANCEL_SHARD_DRAIN_REQUESTED, getCancelShardDrainRequested().orElse(null))
            .append(
                FieldDefs.REPLICATION_SPEC_LIST,
                getReplicationSpecsWithShardData().stream()
                    .map(ReplicationSpec::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append(FieldDefs.OS_TUNED_FILE_OVERRIDES, getOsTunedFileOverrides().toDBObject())
            .append(FieldDefs.DNS_PIN, getDnsPin())
            .append(FieldDefs.CLUSTER_NAME_PREFIX, getClusterNamePrefix())
            .append(FieldDefs.CLUSTER_PROVISION_TYPE, getClusterProvisionType().name())
            .append(FieldDefs.NEEDS_SERVERLESS_SNAPSHOT_FOR_PIT, getNeedsServerlessSnapshotForPit())
            .append(FieldDefs.TERMINATION_PROTECTION_ENABLED, isTerminationProtectionEnabled())
            .append(FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER, getNeedsSampleDataLoadAfter())
            .append(FieldDefs.CREATE_SAMPLE_SEARCH_INDEX, getCreateSampleSearchIndex())
            .append(FieldDefs.NEEDS_ENVOY_SYNC_AFTER, getNeedsEnvoySyncAfter().orElse(null))
            .append(FieldDefs.LAST_DATA_VALIDATION_DATE, getLastDataValidationDate())
            .append(FieldDefs.LAST_DB_CHECK_DATE, getLastDbCheckDate())
            .append(FieldDefs.NEEDS_DB_CHECK_AFTER, getNeedsDbCheckAfter())
            .append(FieldDefs.DB_CHECK_PREFLIGHT_RETRY_COUNT, getDbCheckPreflightRetryCount())
            .append(
                FieldDefs.BUMPER_FILE_OVERRIDES,
                _bumperFileOverrides.stream()
                    .map(BumperFileOverride::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append(
                FieldDefs.REPLICA_SET_SCALING_STRATEGY,
                getReplicaSetScalingStrategy().map(ReplicaSetScalingStrategy::name).orElse(null))
            .append(FieldDefs.AUTO_SCALING_MODE, getAutoScalingMode().name())
            .append(FieldDefs.OS_POLICY_VERSION, getOSPolicyVersion().orElse(null))
            .append(
                FieldDefs.IS_CRITICAL_OS_POLICY_RELEASE,
                getIsCriticalOSPolicyRelease().orElse(null))
            .append(FieldDefs.REDACT_CLIENT_LOG_DATA, getRedactClientLogData().orElse(null))
            .append(
                FieldDefs.CPU_SOCKET_BINDING,
                getCpuSocketBinding().stream().collect(DbUtils.toBasicDBList()))
            .append(
                FieldDefs.CLUSTER_CONNECTION_STRING_CONFIGURATION,
                getClusterConnectionStringConfiguration().toDBObject())
            .append(FieldDefs.GTS_ROLLOUT, getGtsRollout())
            .append(FieldDefs.RESERVE_IPAM_IP_REQUESTED, _reserveIpamIpRequested)
            .append(FieldDefs.FREE_FROM_SERVERLESS, _freeFromServerless)
            .append(
                FieldDefs.NEEDS_PRIORITIES_RESET_FOR_PRIORITY_TAKEOVER_AFTER,
                _needsPrioritiesResetForPriorityTakeoverAfter)
            .append(FieldDefs.ALLOW_UNSAFE_ROLLING_OPERATION, getAllowUnsafeRollingOperation())
            .append(
                FieldDefs.ALWAYS_MANAGED_DEFAULT_RW_CONCERN_SINCE,
                _alwaysManagedDefaultRWConcernSince.orElse(null))
            .append(
                FieldDefs.NEEDS_MONGOTUNE_CONFIG_PUBLISH_AFTER,
                getNeedsMongotuneConfigPublishAfter().orElse(null))
            .append(
                FieldDefs.USE_AWS_TIME_BASED_SNAPSHOT_COPY_FOR_FAST_INITIAL_SYNC,
                getUseAwsTimeBasedSnapshotCopyForFastInitialSync());

    _swapIpMaintenanceRoundCompleted.ifPresent(
        simrc -> dbObj.append(FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED, simrc.name()));

    getSampleDatasetToLoad()
        .ifPresent(dataset -> dbObj.append(FieldDefs.SAMPLE_DATASET_TO_LOAD, dataset.name()));
    getDiskWarmingMode().ifPresent(mode -> dbObj.append(FieldDefs.DISK_WARMING_MODE, mode.name()));
    _freeTenantProviderOptions.ifPresent(
        opts -> dbObj.append(FieldDefs.CLOUD_PROVIDER_OPTIONS, opts.toDBObject()));
    _serverlessTenantProviderOptions.ifPresent(
        opts -> dbObj.append(FieldDefs.CLOUD_PROVIDER_OPTIONS, opts.toDBObject()));
    _serverlessBackupOptions.ifPresent(
        opts -> dbObj.append(FieldDefs.SERVERLESS_BACKUP_OPTIONS, opts.toDBObject()));
    _flexTenantProviderOptions.ifPresent(
        opts -> dbObj.append(FieldDefs.CLOUD_PROVIDER_OPTIONS, opts.toDBObject()));
    getFlexTenantMigrationState()
        .ifPresent(ms -> dbObj.append(FieldDefs.FLEX_TENANT_MIGRATION_STATE, ms.toDBObject()));
    getGatewayRouterEligible()
        .ifPresent(eligible -> dbObj.append(FieldDefs.GATEWAY_ROUTER_ELIGIBLE, eligible));
    getGatewayRouterEligibilityLastUpdateDate()
        .ifPresent(
            date -> dbObj.append(FieldDefs.GATEWAY_ROUTER_ELIGIBILITY_LAST_UPDATE_DATE, date));
    getStorageSystem()
        .ifPresent(storageSystem -> dbObj.append(FieldDefs.STORAGE_SYSTEM, storageSystem.name()));
    getMongotuneStatus()
        .ifPresent(
            mongotuneStatus -> dbObj.append(FieldDefs.MONGOTUNE, mongotuneStatus.toDBObject()));

    getProxyProtocolForPrivateLinkMode()
        .ifPresent(
            proxyProtocolForPrivateLinkMode ->
                dbObj.append(
                    FieldDefs.PROXY_PROTOCOL_FOR_PRIVATE_LINK_MODE,
                    proxyProtocolForPrivateLinkMode.name()));

    getPartnerIntegrationsData()
        .ifPresent(
            partnerIntegrationsData ->
                dbObj.append(
                    FieldDefs.PARTNER_INTEGRATIONS_DATA, partnerIntegrationsData.toDBObject()));

    getAutoSharding().ifPresent(as -> dbObj.append(FieldDefs.AUTO_SHARDING, as.toDBObject()));

    return dbObj;
  }

  static Map<String, String> decodePrivateEndpointIDMap(final DBObject pDBMapObject) {
    return decodePrivateEndpointIDMapWithTransform(
        pDBMapObject, (key) -> (String) pDBMapObject.get(key));
  }

  // Dots cannot be present in values that will be persisted in mongo as field names -
  // when populating a model object from mongo reverse encoding of field names
  static <V> Map<String, V> decodePrivateEndpointIDMapWithTransform(
      final DBObject pDBMapObject, final Function<String, V> pValueFcn) {
    return pDBMapObject.keySet().stream()
        .collect(Collectors.toMap(ClusterDescription::decodePrivateEndpointIDMapField, pValueFcn));
  }

  static String decodePrivateEndpointIDMapField(String pFieldValue) {
    return pFieldValue.replace("@", ".");
  }

  BasicDBObject encodePrivateEndpointIDMap(final Map<String, String> pMapWithPossiblyDottedFields) {
    return encodePrivateEndpointIDMapWithTransform(
        pMapWithPossiblyDottedFields, Function.identity());
  }

  // Dots cannot be present in values that will be persisted in mongo as field names
  <V, T> BasicDBObject encodePrivateEndpointIDMapWithTransform(
      final Map<String, V> pMapWithPossiblyDottedFields, final Function<V, T> pValueTransformer) {
    final BasicDBObject mapWithEncodedFields = new BasicDBObject();
    pMapWithPossiblyDottedFields.forEach(
        (endpointIdentifier, value) ->
            mapWithEncodedFields.append(
                encodePrivateEndpointIDMapField(endpointIdentifier),
                pValueTransformer.apply(value)));
    return mapWithEncodedFields;
  }

  public static String encodePrivateEndpointIDMapField(String pFieldValue) {
    return pFieldValue.replace(".", "@");
  }

  public <T extends Builder<T, C>, C extends ClusterDescription> Builder<T, C> copy() {
    return new Builder<>(toDBObject());
  }

  public RegionName getDataProcessingRegion() throws SvcException {
    return getReplicationSpecsWithShardData()
        .get(0)
        .getHighestPriorityRegion()
        .getDataProcessingRegion();
  }

  public String getName() {
    return _name;
  }

  /**
   * Returns the replicationSpec with the matching spec id. Note that this method also includes
   * dedicated config server replication specs
   */
  public Optional<ReplicationSpec> getReplicationSpecById(final ObjectId pSpecId) {
    if (pSpecId == null) {
      return Optional.empty();
    }
    return getAllReplicationSpecsIncludingConfig().stream()
        .filter(spec -> spec.getId().equals(pSpecId))
        .findFirst();
  }

  public List<ReplicationSpec> getReplicationSpecsByZoneId(final ObjectId pZoneId) {
    if (pZoneId == null) {
      return new ArrayList<>();
    }
    // filter dedicated config server which doesn't have a zoneId
    return getReplicationSpecsWithShardData().stream()
        .filter(spec -> pZoneId.equals(spec.getZoneId()))
        .collect(Collectors.toList());
  }

  public ObjectId getGroupId() {
    return _groupId;
  }

  public ClusterDescriptionId getClusterDescriptionId() {
    return new ClusterDescriptionId(getName(), getGroupId());
  }

  public ObjectId getUniqueId() {
    return _uniqueId;
  }

  public Date getCreateDate() {
    return _createDate;
  }

  public Date getLastUpdateDate() {
    return _lastUpdateDate;
  }

  public Optional<Date> getDeleteAfterDate() {
    return Optional.ofNullable(_deleteAfterDate);
  }

  // existing paused date means the cluster has been successfully paused
  public Optional<Date> getPausedDate() {
    return Optional.ofNullable(_pausedDate);
  }

  public VersionUtils.Version getMongoDBVersion() {
    return _mongoDBVersion;
  }

  public String getMongoDBMajorVersion() {
    return _mongoDBMajorVersion;
  }

  public Optional<FixedVersion> getFixedMongoDBVersion() {
    return Optional.ofNullable(_fixedMongoDBVersion);
  }

  public boolean isMongoDBVersionFixed() {
    return _fixedMongoDBVersion != null;
  }

  public Optional<FixedVersion> getFixedFeatureCompatibilityVersion() {
    return Optional.ofNullable(_fixedFeatureCompatibilityVersion);
  }

  public boolean isFeatureCompatibilityVersionFixed() {
    return _fixedFeatureCompatibilityVersion != null;
  }

  public Optional<FixedVersion> getFixedCpuArch() {
    return Optional.ofNullable(_fixedCpuArch);
  }

  public Optional<FixedVersion> getFixedACMEProvider() {
    return Optional.ofNullable(_fixedACMEProvider);
  }

  public boolean isCpuArchFixed() {
    return _fixedCpuArch != null;
  }

  public Optional<FixedVersion> getFixedOs() {
    return Optional.ofNullable(_fixedOs);
  }

  public boolean isOsFixed() {
    return _fixedOs != null;
  }

  public Set<FixedVersionType> getFixedVersionTypes() {
    return Arrays.stream(FixedVersionType.values())
        .filter(type -> getFixedVersionByType(type).isPresent())
        .collect(Collectors.toSet());
  }

  public Optional<FixedVersion> getFixedVersionByType(final FixedVersionType pType) {
    switch (pType) {
      case MONGODB:
        return getFixedMongoDBVersion();
      case FEATURE_COMPATIBILITY:
        return getFixedFeatureCompatibilityVersion();
      case CPU_ARCHITECTURE:
        return getFixedCpuArch();
      case OS:
        return getFixedOs();
      case ACME_PROVIDER:
        return getFixedACMEProvider();
      default:
        return Optional.empty();
    }
  }

  public List<ClusterDescription> splitByFixedVersionType() {
    Builder<?, ? extends ClusterDescription> builder = copy();
    getFixedVersionTypes().forEach(type -> builder.setFixedVersionByType(type, Optional.empty()));
    final ClusterDescription template = builder.build();
    return getFixedVersionTypes().stream()
        .map(
            type ->
                template.copy().setFixedVersionByType(type, getFixedVersionByType(type)).build())
        .collect(Collectors.toList());
  }

  public int getNumShards() {
    return getReplicationSpecsWithShardData().stream()
        .mapToInt(ReplicationSpec::getNumShards)
        .sum();
  }

  public Set<String> getZoneNames() {
    return getReplicationSpecsWithShardData().stream()
        .map(ReplicationSpec::getZoneName)
        .collect(Collectors.toSet());
  }

  public Set<ObjectId> getZoneIds() {
    return getReplicationSpecsWithShardData().stream()
        .map(ReplicationSpec::getZoneId)
        .collect(Collectors.toSet());
  }

  public boolean isGlobalCluster() {
    return getZoneNames().size() > 1;
  }

  public int getTotalNumNodesWithShardData() {
    return getReplicationSpecsWithShardData().stream()
        .mapToInt(spec -> spec.getNumShards() * spec.getTotalNodes())
        .sum();
  }

  public int getTotalVisibleNumNodes() {
    return getReplicationSpecsWithShardData().stream()
        .mapToInt(spec -> spec.getNumShards() * spec.getTotalVisibleNodes())
        .sum();
  }

  public int getTotalAnalyticsNumNodes() {
    return getReplicationSpecsWithShardData().stream()
        .mapToInt(spec -> spec.getNumShards() * spec.getTotalAnalyticsNodes())
        .sum();
  }

  public int getTotalElectableNumNodesWithShardData() {
    return getReplicationSpecsWithShardData().stream()
        .mapToInt(spec -> spec.getNumShards() * spec.getTotalElectableNodes())
        .sum();
  }

  public int getTotalReadOnlyNumNodes() {
    return getReplicationSpecsWithShardData().stream()
        .mapToInt(spec -> spec.getNumShards() * spec.getTotalReadOnlyNodes())
        .sum();
  }

  public int getTotalVisibleNumNodesWithShardDataByRegion(final RegionName pRegionName) {
    return getReplicationSpecsWithShardData().stream()
        .mapToInt(spec -> spec.getNumShards() * spec.getTotalVisibleNumNodesInRegion(pRegionName))
        .sum();
  }

  /**
   * Gets the disk size for the given replication spec. Note that this includes dedicated config
   * server replication specs as well
   */
  public double getDiskSizeGBWithReplicationSpecId(final ObjectId pReplicationSpecId) {
    return _diskSizeGB;
  }

  public List<HardwareSpec> getHardwareSpecs(final NodeType pNodeType) {
    if (pNodeType == null) {
      throw new IllegalArgumentException("NodeType cannot be null");
    }

    return switch (pNodeType) {
      case ELECTABLE ->
          getAllRegionConfigs().stream()
              .map(RegionConfig::getElectableSpecs)
              .collect(Collectors.toList());
      case ANALYTICS ->
          getAllRegionConfigs().stream()
              .map(RegionConfig::getAnalyticsSpecs)
              .collect(Collectors.toList());
      case READ_ONLY ->
          getAllRegionConfigs().stream()
              .map(RegionConfig::getReadOnlySpecs)
              .collect(Collectors.toList());
      case HIDDEN_SECONDARY ->
          getAllRegionConfigs().stream()
              .map(RegionConfig::getHiddenSecondarySpecs)
              .collect(Collectors.toList());
    };
  }

  public Optional<HardwareSpec> getOnlyHardwareSpec(final NodeType pNodeType) {
    if (pNodeType == null) {
      throw new IllegalArgumentException("NodeType cannot be null");
    }

    final Set<RegionConfig> regionConfigSet =
        getAllRegionConfigs().stream().collect(Collectors.toSet());
    if (regionConfigSet.size() > 1) {
      // This should not happen
      return Optional.empty();
    }

    final RegionConfig regionConfig = regionConfigSet.iterator().next();

    return switch (pNodeType) {
      case ELECTABLE -> Optional.of(regionConfig.getElectableSpecs());
      case ANALYTICS -> Optional.of(regionConfig.getAnalyticsSpecs());
      case READ_ONLY -> Optional.of(regionConfig.getReadOnlySpecs());
      case HIDDEN_SECONDARY -> Optional.of(regionConfig.getHiddenSecondarySpecs());
    };
  }

  @Deprecated
  public HardwareSpec getHardwareSpec(final NodeType pNodeType) {
    if (pNodeType == null) {
      throw new IllegalArgumentException("NodeType cannot be null");
    }

    return switch (pNodeType) {
      case ELECTABLE -> getFirstShardRegionConfig_DEPRECATED().getElectableSpecs();
      case ANALYTICS -> getFirstShardRegionConfig_DEPRECATED().getAnalyticsSpecs();
      case READ_ONLY -> getFirstShardRegionConfig_DEPRECATED().getReadOnlySpecs();
      case HIDDEN_SECONDARY -> getFirstShardRegionConfig_DEPRECATED().getHiddenSecondarySpecs();
    };
  }

  public Optional<HardwareSpec> getHardwareSpec(
      final ObjectId pReplicationSpecId, final RegionName pRegionName, final NodeType pNodeType) {
    return getReplicationSpecById(pReplicationSpecId)
        .flatMap(rs -> rs.getRegionConfigByRegion(pRegionName))
        .map(pRegionConfig -> pRegionConfig.getHardwareSpecByNodeType(pNodeType));
  }

  public Optional<HardwareSpec> getOnlyHardwareSpecForProvider(
      final CloudProvider pCloudProvider, final NodeType pNodeType) {
    return findOnlyElement(getHardwareSpecsForProvider(pCloudProvider, pNodeType));
  }

  public List<HardwareSpec> getHardwareSpecsForProvider(
      final CloudProvider pCloudProvider, final NodeType pNodeType) {

    if (pNodeType == null) {
      throw new IllegalArgumentException("NodeType cannot be null");
    }

    return getAllRegionConfigs().stream()
        .filter(
            regionConfig ->
                regionConfig.getCloudProvider().equals(pCloudProvider)
                    && regionConfig instanceof ShardRegionConfig)
        .map(hs -> hs.getHardwareSpecByNodeType(pNodeType))
        .toList();
  }

  public List<HardwareSpec> getAllShardAndConfigHardwareSpecsForProvider(
      final CloudProvider pCloudProvider) {
    return getAllHardwareSpecsForReplicationSpecsAndProvider(
        getAllReplicationSpecsIncludingConfig(), pCloudProvider);
  }

  public List<HardwareSpec> getAllHardwareSpecsForReplicationSpecsAndProvider(
      final List<ReplicationSpec> pReplicationSpecs, final CloudProvider pCloudProvider) {
    return pReplicationSpecs.stream()
        .map(ReplicationSpec::getRegionConfigs)
        .flatMap(List::stream)
        .filter(rc -> rc.getCloudProvider() == pCloudProvider)
        .map(RegionConfig::getHardwareSpecs)
        .flatMap(List::stream)
        .collect(Collectors.toList());
  }

  public Optional<HardwareSpec> findFirstHardwareSpecForProvider(
      final CloudProvider pCloudProvider) {
    return getAllShardAndConfigHardwareSpecsForProvider(pCloudProvider).stream()
        .filter(hs -> hs.getNodeCount() > 0)
        .findFirst();
  }

  public Optional<NDSInstanceSize> getOnlyInstanceSize(final NodeType pNodeType) {
    return findOnlyElement(getInstanceSizes(pNodeType));
  }

  public Optional<NDSInstanceSize> getOnlyInstanceSizeForProvider(
      final CloudProvider provider, final NodeType nodeType) {
    return findOnlyElement(getInstanceSizesForProviderAndNodeType(provider, nodeType));
  }

  public Optional<NDSInstanceSize> getOnlyInstanceSizeForProvider(
      final CloudProvider provider, final NodeTypeFamily nodeTypeFamily) {
    final Set<NodeType> nodeTypes = NodeType.getNodeTypesOfFamily(nodeTypeFamily);
    final Set<NDSInstanceSize> instanceSizes =
        nodeTypes.stream()
            .flatMap(nt -> getInstanceSizesForProviderAndNodeType(provider, nt).stream())
            .collect(Collectors.toSet());
    return findOnlyElement(instanceSizes);
  }

  // should only be used in cases when pure string of instance size is needed and cloud provider
  // does not matter
  public Optional<NDSInstanceSize> getOnlyInstanceSizeAcrossProviders(final NodeType pNodeType) {
    final Set<NDSInstanceSize> instanceSizes = getInstanceSizes(pNodeType);
    if (instanceSizes.size() > 0 && !hasAsymmetricShardsForNodeType(pNodeType)) {
      return Optional.of(instanceSizes.iterator().next());
    }
    return Optional.empty();
  }

  public Optional<AutoScaleInstanceSize> getOnlyAutoScaleInstanceSize(final NodeType pNodeType) {
    return findOnlyElement(getAutoScaleInstanceSizes(pNodeType));
  }

  public Set<AutoScaleInstanceSize> getAutoScaleInstanceSizes(final NodeType pNodeType) {
    final Set<NDSInstanceSize> instanceSizes = getInstanceSizes(pNodeType);
    final AutoScaleInstanceSizeBuilder autoScaleInstanceSizeBuilder =
        AutoScaleInstanceSizeBuilderFactory.getAutoScaleInstanceSizeBuilder(getCloudProviders());

    return instanceSizes.stream()
        .map(autoScaleInstanceSizeBuilder::getAutoScaleInstanceSize)
        .collect(Collectors.toSet());
  }

  public Optional<NDSInstanceSize> getMaxInstanceSize(final NodeType pNodeType) {
    final Set<NDSInstanceSize> instanceSizes = getInstanceSizes(pNodeType);
    return instanceSizes.stream()
        .max(Comparator.comparing(InstanceSize::name, InstanceSize::compareByNames));
  }

  public Optional<NDSInstanceSize> getMaxInstanceSizeForProvider(
      final NodeType pNodeType, final CloudProvider pCloudProvider) {
    final Set<NDSInstanceSize> instanceSizes = getInstanceSizes(pNodeType);
    return instanceSizes.stream()
        .filter(size -> size.getCloudProvider().equals(pCloudProvider))
        .max(Comparator.comparing(InstanceSize::name, InstanceSize::compareByNames));
  }

  public Optional<NDSInstanceSize> getMinInstanceSize(final NodeType pNodeType) {
    final Set<NDSInstanceSize> instanceSizes = getInstanceSizes(pNodeType);
    return instanceSizes.stream()
        .min(Comparator.comparing(InstanceSize::name, InstanceSize::compareByNames));
  }

  public Optional<NDSInstanceSize> getMinInstanceSizeForProvider(
      final NodeType pNodeType, final CloudProvider pCloudProvider) {
    final Set<NDSInstanceSize> instanceSizes = getInstanceSizes(pNodeType);
    return instanceSizes.stream()
        .filter(size -> size.getCloudProvider().equals(pCloudProvider))
        .min(Comparator.comparing(InstanceSize::name, InstanceSize::compareByNames));
  }

  public Optional<Integer> getMaxDiskIOPS(NodeType nodeType) {
    return getAllRegionConfigs().stream()
        .map(rc -> rc.getHardwareSpecByNodeType(nodeType).getDiskIOPS(getDiskSizeGB()))
        .max(Integer::compareTo);
  }

  public Optional<HardwareSpec> getSmallestShardHardwareByInstanceSize(final NodeType pNodeType) {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(
            rs ->
                rs.getRegionConfigs().stream()
                    .map(rc -> rc.getHardwareSpecByNodeType(pNodeType))
                    .filter(hs -> hs.getNodeCount() > 0))
        .min(Comparator.comparing(hs -> hs.getInstanceSize().name(), InstanceSize::compareByNames));
  }

  public Set<NDSInstanceSize> getInstanceSizes(final NodeType pNodeType) {
    return getInstanceSizesForNonZeroNodeType(getReplicationSpecsWithShardData(), pNodeType);
  }

  public Set<NDSInstanceSize> getInstanceSizesForProvider(final CloudProvider pCloudProvider) {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(rc -> rc.getRegionConfigs().stream())
        .filter(rc -> rc.getCloudProvider() == pCloudProvider)
        .flatMap(rc -> rc.getHardwareSpecs().stream())
        .filter(hs -> hs.getNodeCount() > 0)
        .map(HardwareSpec::getInstanceSize)
        .collect(Collectors.toSet());
  }

  public Set<NDSInstanceSize> getInstanceSizesForProviderAndNodeType(
      final CloudProvider pCloudProvider, final NodeType pNodeType) {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(rc -> rc.getRegionConfigs().stream())
        .filter(rc -> rc.getCloudProvider() == pCloudProvider)
        .map(rc -> rc.getHardwareSpecByNodeType(pNodeType))
        .filter(hs -> hs.getNodeCount() > 0)
        .map(HardwareSpec::getInstanceSize)
        .collect(Collectors.toSet());
  }

  public InstanceFamily getInstanceFamily(final NodeType pNodeType) {
    return getHardwareSpec(pNodeType).getInstanceFamily();
  }

  public Optional<InstanceFamily> getOnlyInstanceFamily(final NodeType pNodeType) {
    return findOnlyElement(getInstanceFamilies(pNodeType));
  }

  public Set<InstanceFamily> getInstanceFamilies(final NodeType pNodeType) {
    return getHardwareSpecs(pNodeType).stream()
        .map(HardwareSpec::getInstanceFamily)
        .collect(Collectors.toSet());
  }

  @VisibleForTesting
  // OSes have to be consistent across shards, so we only return the first found here.
  // This method is only used for testing, consider using the below getOs(replicationSpecId,
  // regionName, nodeType) method in business logic
  public OS getOS(final NodeType pNodeType) {
    return getHardwareSpecs(pNodeType).stream().map(HardwareSpec::getOS).findFirst().get();
  }

  public boolean usesOs(final OS pOS) {
    final List<ReplicationSpec> replicationSpecs = getAllReplicationSpecsIncludingConfig();
    for (final ReplicationSpec replicationSpec : replicationSpecs) {
      for (final RegionName regionName : replicationSpec.getRegions()) {
        for (final NodeType nodeType : NodeType.values()) {
          final Optional<RegionConfig> regionConfig =
              replicationSpec.getRegionConfigByRegion(regionName);
          if (regionConfig.isPresent()) {
            final HardwareSpec hwSpec = regionConfig.get().getHardwareSpecByNodeType(nodeType);
            if (hwSpec != null && hwSpec.getNodeCount() > 0) {
              if (getOS(replicationSpec.getId(), regionName, nodeType) == pOS) {
                return true;
              }
            }
          }
        }
      }
    }
    return false;
  }

  public OS getOS(
      final ObjectId pReplicationSpecId, final RegionName pRegionName, final NodeType pNodeType) {

    final Optional<HardwareSpec> hardwareSpec =
        getHardwareSpec(pReplicationSpecId, pRegionName, pNodeType);

    if (hardwareSpec.isEmpty()) {
      throw new IllegalArgumentException(
          String.format(
              "Failed to find hardware spec for replication spec with id %s, region %s and node"
                  + " type %s",
              pReplicationSpecId, pRegionName, pNodeType));
    }

    return hardwareSpec.orElseThrow().getOS();
  }

  public Set<InstanceFamily> getInstanceFamiliesForProvider(
      final CloudProvider pCloudProvider, final NodeType pNodeType) {
    return getHardwareSpecsForProvider(pCloudProvider, pNodeType).stream()
        .map(HardwareSpec::getInstanceFamily)
        .collect(Collectors.toSet());
  }

  public Optional<InstanceFamily> getOnlyInstanceFamilyForProvider(
      final CloudProvider pCloudProvider, final NodeType pNodeType) {
    return findOnlyElement(getInstanceFamiliesForProvider(pCloudProvider, pNodeType));
  }

  public CpuArchitecture getCpuArchitectureForProvider(
      final CloudProvider pCloudProvider, final NodeType pNodeType) {
    final Set<CpuArchitecture> archs =
        getHardwareSpecsForProvider(pCloudProvider, pNodeType).stream()
            .map(hs -> hs.getInstanceFamily().getCpuArchitecture())
            .collect(Collectors.toSet());

    return findOnlyElement(archs)
        .orElseThrow(
            () ->
                new IllegalStateException(
                    "Expected to find exactly one cpu arch for provider "
                        + pCloudProvider
                        + " but found "
                        + archs));
  }

  public OS getOSForProvider(final CloudProvider pCloudProvider, final NodeType pNodeType) {
    final Set<OS> oss =
        getHardwareSpecsForProvider(pCloudProvider, pNodeType).stream()
            .map(HardwareSpec::getOS)
            .collect(Collectors.toSet());
    if (oss.size() != 1) {
      throw new IllegalStateException(
          "Expected to find exactly one os for provider " + pCloudProvider + " but found " + oss);
    }
    return oss.iterator().next();
  }

  public boolean isNVMe(final NodeType pNodeType) {
    return getInstanceSizes(pNodeType).stream().anyMatch(NDSInstanceSize::isNVMe);
  }

  public boolean isAnyNodeNvme() {
    return Arrays.stream(NodeType.values()).anyMatch(this::isNVMe);
  }

  public boolean isAnyDiskAzurePv2() {
    return getReplicationSpecsWithShardData().stream().anyMatch(ReplicationSpec::isOnAzureSsdV2);
  }

  /**
   * Used to determine if a cluster is eligible for file copy based initial sync. If we allow a mix
   * of NVMe and non NVMe instances the logic consuming this may need to change
   */
  public boolean isFullyNVMe() {
    final Set<Boolean> nvmeStatus =
        Arrays.stream(NodeType.values())
            .flatMap(nodeType -> getInstanceSizes(nodeType).stream())
            .map(NDSInstanceSize::isNVMe)
            .collect(Collectors.toUnmodifiableSet());
    return nvmeStatus.size() == 1 && nvmeStatus.contains(true);
  }

  /**
   * Deprecating this field with the introduction of the asymmetric sharding initiative This method
   * calls the deprecated methods defined by CLOUDP-230119 and is not compatible with asymmetric
   * sharding
   *
   * <p>consider getOnlyAutoScaleInstanceSize instead
   */
  @Deprecated
  public AutoScaleInstanceSize getAutoScaleInstanceSize(final NodeTypeFamily pNodeTypeFamily) {
    return AutoScaleInstanceSizeBuilderFactory.getAutoScaleInstanceSizeBuilder(getCloudProviders())
        .getAutoScaleInstanceSize(
            getHardwareSpec(NodeTypeFamily.getRepresentativeNodeTypeFromFamily(pNodeTypeFamily))
                .getInstanceSize());
  }

  public AutoScaleInstanceSize getAutoScaleInstanceSizeOfMaxInstanceSize(
      final NodeTypeFamily pNodeTypeFamily) {
    final NDSInstanceSize maxInstanceSize =
        getMaxInstanceSize(NodeTypeFamily.getRepresentativeNodeTypeFromFamily(pNodeTypeFamily))
            .orElseThrow();
    return AutoScaleInstanceSizeBuilderFactory.getAutoScaleInstanceSizeBuilder(getCloudProviders())
        .getAutoScaleInstanceSize(maxInstanceSize);
  }

  public Optional<AutoScaleInstanceSize> getMaxAutoScaleInstanceSize(
      final NodeTypeFamily pNodeTypeFamily) {
    return Optional.ofNullable(getAutoScaling(pNodeTypeFamily))
        .map(AutoScaling::getCompute)
        .flatMap(compute -> compute.getMaxAutoScaleInstanceSize(getCloudProviders()));
  }

  public Optional<AutoScaleInstanceSize> getMinAutoScaleInstanceSize(
      final NodeTypeFamily pNodeTypeFamily) {
    return Optional.ofNullable(getAutoScaling(pNodeTypeFamily))
        .map(AutoScaling::getCompute)
        .flatMap(compute -> compute.getMinAutoScaleInstanceSize(getCloudProviders()));
  }

  public Optional<CpuArchitecture> getPreferredCpuArchForProvider(
      final CloudProvider pCloudProvider, final NodeType pNodeType) {
    if (!getCloudProviders().contains(pCloudProvider)) {
      return Optional.empty();
    }
    final Set<CpuArchitecture> cpuArch =
        getHardwareSpecsForProvider(pCloudProvider, pNodeType).stream()
            .flatMap(hs -> hs.getPreferredCpuArch().stream())
            .collect(Collectors.toSet());

    return switch (cpuArch.size()) {
      case 0 -> Optional.empty();
      case 1 -> Optional.of(cpuArch.iterator().next());
      default ->
          throw new IllegalStateException(
              "Expected to find 1 preferred cpu arch for provider "
                  + pCloudProvider
                  + " but found "
                  + cpuArch);
    };
  }

  public Optional<RegionConfig> getOnlyShardRegionConfigForProvider(
      final CloudProvider pCloudProvider) {
    final List<RegionConfig> configsForProvider =
        getReplicationSpecsWithShardData().stream()
            .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
            .filter(regionConfig -> regionConfig.getCloudProvider().equals(pCloudProvider))
            .toList();

    return findOnlyElement(configsForProvider);
  }

  @Deprecated
  public int getDiskIOPS(final NodeType pNodeType) {
    return getHardwareSpec(pNodeType).getDiskIOPS(getDiskSizeGB());
  }

  /**
   * Checks whether all ReplicationSpecs in the ClusterDescription are already split
   *
   * @return true if all ReplicationSpecs are split, false otherwise
   */
  public boolean hasSplitReplicationSpecs() {
    return hasSplitReplicationSpecs(_replicationSpecList);
  }

  public static boolean hasSplitReplicationSpecs(List<ReplicationSpec> replicationSpecList) {
    return replicationSpecList.stream().allMatch(rs -> rs.getNumShards() == 1);
  }

  /**
   * Returns all the ReplicationSpecs that belong to the same zone.
   *
   * @param zoneId The identifier of the zone ReplicationSpecs belong to.
   * @return A list of the ReplicationSpecs that are part of the given zone.
   */
  public List<ReplicationSpec> getReplicationSpecsInZone(ObjectId zoneId) {
    return getReplicationSpecsInZone(getReplicationSpecsWithShardData(), zoneId);
  }

  public List<ObjectId> getReplicationSpecIdsInZone(ObjectId zoneId) {
    return getReplicationSpecsInZone(getReplicationSpecsWithShardData(), zoneId).stream()
        .map(ReplicationSpec::getId)
        .toList();
  }

  public static List<ReplicationSpec> getReplicationSpecsInZone(
      final List<ReplicationSpec> pReplicationSpecList, final ObjectId pZoneId) {
    return pReplicationSpecList.stream().filter(rs -> rs.getZoneId().equals(pZoneId)).toList();
  }

  public static Set<NDSInstanceSize> getInstanceSizesForNonZeroNodeType(
      List<ReplicationSpec> pReplicationSpecsWithShardData, NodeType pNodeType) {
    return pReplicationSpecsWithShardData.stream()
        .flatMap(rs -> rs.getNonZeroInstanceSizesForNodeType(pNodeType).stream())
        .collect(Collectors.toSet());
  }

  public static boolean hasAsymmetricShardsForNodeType(
      List<ReplicationSpec> pReplicationSpecsWithShardData, NodeType pNodeType) {
    final Set<NDSInstanceSize> rawInstanceSizes =
        getInstanceSizesForNonZeroNodeType(pReplicationSpecsWithShardData, pNodeType);
    final Set<CrossCloudInstanceSize> crossCloudInstanceSizes = new HashSet<>();
    final Set<NDSInstanceSize> nonCrossCloudSizes = new HashSet<>();

    rawInstanceSizes.forEach(
        size ->
            CrossCloudInstanceSize.getCrossCloudInstanceSize(size)
                .ifPresentOrElse(crossCloudInstanceSizes::add, () -> nonCrossCloudSizes.add(size)));
    return crossCloudInstanceSizes.size() + nonCrossCloudSizes.size() > 1;
  }

  public static boolean hasAsymmetricShards(List<ReplicationSpec> pReplicationSpecsWithShardData) {
    return hasAsymmetricShardsForNodeType(pReplicationSpecsWithShardData, NodeType.ELECTABLE)
        || hasAsymmetricShardsForNodeType(pReplicationSpecsWithShardData, NodeType.ANALYTICS);
  }

  public boolean hasAsymmetricShardsForNodeType(final NodeType pNodeType) {
    return hasAsymmetricShardsForNodeType(getReplicationSpecsWithShardData(), pNodeType);
  }

  public boolean hasAsymmetricShards() {
    return hasAsymmetricShards(getReplicationSpecsWithShardData());
  }

  public List<ReplicationSpec> getReplicationSpecsWithShardData() {
    return _replicationSpecList;
  }

  public List<ReplicationSpec> getReplicationSpecsWithShardDataAndNodesOfFamily(
      final NodeTypeFamily pNodeTypeFamily) {
    return getReplicationSpecsWithShardData().stream()
        .filter(rs -> rs.hasNodesOfFamily(pNodeTypeFamily))
        .toList();
  }

  public List<ReplicationSpec> getAllReplicationSpecsIncludingConfig() {
    return getReplicationSpecsWithShardData();
  }

  // Overridden by sharded clusters
  public Optional<ReplicationSpec> getDedicatedConfigServerReplicationSpec() {
    return Optional.empty();
  }

  // Overridden by sharded clusters
  public Optional<ReplicationSpec> getEmbeddedConfigServerReplicationSpec() {
    return Optional.empty();
  }

  public List<RegionConfig> getFirstReplicationSpecVisibleRegions() {
    return getReplicationSpecsWithShardData().get(0).getRegionConfigs().stream()
        .flatMap(rc -> Collections.nCopies(rc.getTotalVisibleNodes(), rc).stream())
        .collect(Collectors.toList());
  }

  public Set<RegionName> getRegionNames() {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(rs -> rs.getRegions().stream())
        .collect(Collectors.toSet());
  }

  public Set<RegionName> getElectableNodeRegionNames() {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(rs -> rs.getElectableNodeRegions().stream())
        .collect(Collectors.toUnmodifiableSet());
  }

  public Optional<String> getLoadBalancedHostname() {
    return Optional.ofNullable(_loadBalancedHostname);
  }

  public Optional<String> getLoadBalancedMeshHostname() {
    return Optional.ofNullable(_loadBalancedMeshHostname);
  }

  public List<String> getShardsDraining() {
    return _shardsDraining;
  }

  public Optional<Date> getCancelShardDrainRequested() {
    return Optional.ofNullable(_cancelShardDrainRequested);
  }

  public boolean isMultiRegion() {
    return getRegionNames().size() > 1;
  }

  public boolean isTenantCluster() {
    return getCloudProviders().stream().anyMatch(CloudProvider::isTenantProvider);
  }

  public boolean isDedicatedCluster() {
    return getCloudProviders().stream().anyMatch(CloudProvider::isDedicatedProvider);
  }

  public CloudProvider getLegacyProvider() {
    return _replicationSpecList.get(0).getRegionConfigs().get(0).getCloudProvider();
  }

  public boolean isAutomaticallyPaused() {
    return !isCrossCloudCluster() && isFreeTenantCluster() && isPaused();
  }

  /**
   * Check if cluster is shared tier (M0-M5)
   *
   * @return true if M0-M5 cluster
   */
  public boolean isSharedTenantCluster() {
    return !isCrossCloudCluster() && getCloudProviders().contains(CloudProvider.FREE);
  }

  public boolean isFreeTenantCluster() {
    return isSharedTenantCluster()
        && getOnlyInstanceSize(NodeType.ELECTABLE).filter(NDSInstanceSize::isM0).isPresent();
  }

  public boolean isServerlessTenantCluster() {
    return !isCrossCloudCluster() && getCloudProviders().contains(CloudProvider.SERVERLESS);
  }

  public boolean isFlexTenantCluster() {
    return !isCrossCloudCluster() && getCloudProviders().contains(CloudProvider.FLEX);
  }

  /**
   * Determines if this cluster is a Flex cluster that was migrated from Serverless and still uses
   * serverless networking (load balanced hostname).
   *
   * @return true if this is a Flex cluster migrated from Serverless with load balanced networking
   */
  public boolean isFlexWithServerlessNetworking() {
    if (_flexTenantMigrationState == null) {
      return false;
    }
    final Optional<CloudProvider> formerCloudProvider =
        _flexTenantMigrationState.getFormerCloudProvider();
    return getCloudProviders().contains(CloudProvider.FLEX)
        && formerCloudProvider.map(CloudProvider.SERVERLESS::equals).orElse(false)
        && getLoadBalancedHostname().isPresent()
        && !getLoadBalancedHostname().get().isEmpty();
  }

  public boolean isMigratedOrApiShimCreatedServerlessTenantCluster() {
    if (_flexTenantMigrationState == null) {
      return false;
    }
    final Optional<CloudProvider> apiCloudProvider =
        _flexTenantMigrationState.getTenantApiCloudProvider();
    final Optional<CloudProvider> formerCloudProvider =
        _flexTenantMigrationState.getFormerCloudProvider();
    return apiCloudProvider.map(CloudProvider.SERVERLESS::equals).orElse(false)
        || formerCloudProvider.map(CloudProvider.SERVERLESS::equals).orElse(false);
  }

  // TODO: after Serverless is sunset, merge this with `isTenantCluster`
  public boolean isFlexOrSharedTenantCluster() {
    return !isCrossCloudCluster()
        && getCloudProviders().stream()
            .anyMatch(
                provider -> List.of(CloudProvider.FREE, CloudProvider.FLEX).contains(provider));
  }

  public CloudProvider getTenantType() {
    return getCloudProviders().stream()
        .filter(CloudProvider::isTenantProvider)
        .findFirst()
        .orElse(CloudProvider.NONE);
  }

  // NB(pt): the Flex billing system uses this to select what metrics to query for, please only
  // alter with care
  public boolean wasMigratedFromServerlessToFlex() {
    return isFlexTenantCluster()
        && getFlexTenantMigrationState()
            .flatMap(
                flexTenantMigrationState ->
                    flexTenantMigrationState
                        .getFormerCloudProvider()
                        .map(cp -> cp.equals(CloudProvider.SERVERLESS)))
            .orElse(false);
  }

  public boolean isClusterCreatedFromLegacyApi() {
    return getFlexTenantMigrationState()
        .map(FlexTenantMigrationState::isTenantCreatedFromApi)
        .orElse(false);
  }

  public boolean isCNRegionsOnlyCluster() throws SvcException {
    try {
      return RegionNameUtil.isAllCNRegions(getRegionNames());
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS)) {
        throw new SvcException(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, "cluster", "regions");
      } else {
        throw pE;
      }
    }
  }

  public boolean isGovRegionsOnlyCluster() throws SvcException {
    try {
      return RegionNameUtil.getCommonComplianceLevel(getRegionNames())
          .map(ComplianceLevel.US_GOV::equals)
          .orElse(false);
    } catch (final SvcException pE) {
      if (pE.getErrorCode().equals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS)) {
        throw new SvcException(
            NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, "cluster", "regions");
      }
      throw pE;
    }
  }

  public boolean isComputeAutoScalingSupported() {
    return getInstanceSizes(NodeType.ELECTABLE).stream()
        .allMatch(InstanceSize::isComputeAutoScalingSupported);
  }

  public boolean isPredictiveAutoScalingEnabled(final NodeTypeFamily nodeTypeFamily) {
    // Predictive auto-scaling is not supported for dedicated analytics nodes.
    if (nodeTypeFamily == NodeTypeFamily.ANALYTICS) {
      return false;
    }
    return Optional.ofNullable(getAutoScaling(nodeTypeFamily))
        .map(AutoScaling::isPredictiveEnabled)
        .orElse(false);
  }

  /**
   * True if the "base" and analytics specs differ in instance size - it is possible for the cluster
   * to not be meaningfully asymmetric due to a lack of analytics nodes even if this returns true.
   */
  public boolean hasAsymmetricAnalyticsSize() {
    return !getInstanceSizes(NodeType.ELECTABLE).containsAll(getInstanceSizes(NodeType.ANALYTICS));
  }

  public boolean hasAsymmetricAnalyticsInstanceFamilyClass() {
    if (isTenantCluster()) {
      return false;
    }

    final Set<NDSInstanceFamilyClass> analyticsInstanceClass =
        getInstanceSizes(NodeType.ANALYTICS).stream()
            .map(InstanceSize::getFamilyClass)
            .collect(Collectors.toSet());
    final Set<NDSInstanceFamilyClass> electableInstanceClass =
        getInstanceSizes(NodeType.ELECTABLE).stream()
            .map(InstanceSize::getFamilyClass)
            .collect(Collectors.toSet());
    return !electableInstanceClass.containsAll(analyticsInstanceClass);
  }

  public boolean hasNonZeroAsymmetricAnalyticsNodes() {
    return hasAsymmetricAnalyticsSize() && getTotalAnalyticsNumNodes() > 0;
  }

  public Optional<VolumeType> getEBSVolumeType() {
    final List<HardwareSpec> hardwareSpecs =
        getHardwareSpecsForProvider(CloudProvider.AWS, NodeType.ELECTABLE);
    return CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
        .getInstanceHardwareProvider()
        .getEBSVolumeType(hardwareSpecs);
  }

  public Optional<Integer> getDiskIOPSForClusterDiff() {
    if (isTenantCluster()) {
      return Optional.empty();
    }

    return Stream.of(
            findFirstHardwareSpecForProvider(CloudProvider.AWS),
            findFirstHardwareSpecForProvider(CloudProvider.AZURE),
            findFirstHardwareSpecForProvider(CloudProvider.GCP))
        .flatMap(Optional::stream)
        .map(spec -> spec.getConfiguredDiskIops(getDiskSizeGB()))
        .filter(Objects::nonNull)
        .min(Comparator.comparingInt(x -> x));
  }

  public ItemDiff getNewClusterDiff() {
    final ItemDiff diff = ItemDiff.forClusterDescription(getName(), ItemDiff.Status.NEW);

    // Hide this field for flex clusters as they should align with serverless style instances in the
    // future
    if (!isServerlessTenantCluster() && !isFlexTenantCluster()) {
      diff.addItem(FieldDefs.CLUSTER_TYPE, "Cluster Type", null, getClusterType());
    }

    diff.addItem(
        FieldDefs.INTERNAL_CLUSTER_ROLE, "Internal Cluster Role", null, getInternalClusterRole());

    diff.addItem(
        CLOUD_PROVIDER_DIFF,
        "Cloud Provider",
        null,
        isCrossCloudCluster()
            ? getCloudProviders().stream()
                .map(CloudProvider::getDescription)
                .collect(Collectors.toList())
            : getLegacyProvider().getDescription());

    // NOTE: we do not display "Tenant Backup Enabled" for Free/Flex
    diff.addItem(FieldDefs.BACKUP_ENABLED, "Backup Enabled", null, isBackupEnabled());

    diff.addItem(
        FieldDefs.DISK_BACKUP_ENABLED, "Cloud Backup Enabled", null, isDiskBackupEnabled());

    diff.addItem(FieldDefs.PIT_ENABLED, "Continuous Cloud Backup Enabled", null, isPitEnabled());

    diff.addItem(FieldDefs.DISK_SIZE_GB, "Disk Size GB", null, getDiskSizeGB());

    diff.addItem(
        FieldDefs.MONGODB_MAJOR_VERSION, "MongoDB Major Version", null, getMongoDBMajorVersion());

    // Hide this field for flex clusters as they should align with serverless style instances in the
    // future
    if (!isServerlessTenantCluster() && !isFlexTenantCluster()) {
      diff.addItem(
          FieldDefs.REPLICATION_SPEC_LIST,
          "Replication Spec",
          null,
          getReplicationSpecsWithShardData().toString());
    }

    diff.addItem(
        FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
        "Encryption at Rest Provider",
        null,
        getEncryptionAtRestProvider().name());

    if (isDedicatedCluster()) {
      diff.addItem(
          FieldDefs.REPLICA_SET_SCALING_STRATEGY,
          "Replica Set Scaling Strategy",
          null,
          getReplicaSetScalingStrategy().map(ReplicaSetScalingStrategy::name).orElse(null));

      diff.addItem(
          FieldDefs.REDACT_CLIENT_LOG_DATA,
          "Redact Client Log Data",
          null,
          getRedactClientLogData().orElse(null));
    }

    diff.addItem(
        FieldDefs.BI_CONNECTOR, "BI Connector Enabled", null, getBiConnector().isEnabled());

    diff.addItem(
        FieldDefs.BI_CONNECTOR,
        "BI Connector Read Preference",
        null,
        getBiConnector().getReadPreference().getValue());

    if (isAutoScaleSupported()) {
      final DiskGBAutoScaling diskGBAutoScaling = getAutoScaling(NodeTypeFamily.BASE).getDiskGB();
      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Disk Auto Scaling Enabled",
          null,
          diskGBAutoScaling.isEnabled());

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Auto Indexing Enabled",
          null,
          getAutoScaling(NodeTypeFamily.BASE).isAutoIndexingEnabled());

      final ComputeAutoScaling computeAutoScaling =
          getAutoScaling(NodeTypeFamily.BASE).getCompute();
      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Compute Auto Scaling Enabled",
          null,
          computeAutoScaling.isEnabled());

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Compute Auto Scaling Scale Down Enabled",
          null,
          computeAutoScaling.isScaleDownEnabled());

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Predictive Compute Auto Scaling Enabled",
          null,
          computeAutoScaling.isPredictiveEnabled());

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Compute Auto Scaling Min Instance Size",
          null,
          computeAutoScaling.getMinInstanceSize());

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Compute Auto Scaling Max Instance Size",
          null,
          computeAutoScaling.getMaxInstanceSize());

      if (getAnalyticsAutoScaling().isPresent() && getTotalAnalyticsNumNodes() > 0) {
        final ComputeAutoScaling analyticsComputeAutoScaling =
            getAutoScaling(NodeTypeFamily.ANALYTICS).getCompute();
        diff.addItem(
            RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
            "Analytics Compute Auto Scaling Enabled",
            null,
            analyticsComputeAutoScaling.isEnabled());

        diff.addItem(
            RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
            "Analytics Compute Auto Scaling Scale Down Enabled",
            null,
            analyticsComputeAutoScaling.isScaleDownEnabled());

        diff.addItem(
            RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
            "Analytics Compute Auto Scaling Min Instance Size",
            null,
            analyticsComputeAutoScaling.getMinInstanceSize());

        diff.addItem(
            RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
            "Analytics Compute Auto Scaling Max Instance Size",
            null,
            analyticsComputeAutoScaling.getMaxInstanceSize());
      }
    }

    // NOTE: This probably should have been hidden for Serverless as well as it is not returned as
    //       part of the public API response
    if (!hasAsymmetricShardsForNodeType(NodeType.ELECTABLE) && !isFlexTenantCluster()) {
      diff.addItem(
          FieldDefs.INSTANCE_SIZE,
          "Instance Size",
          null,
          getOnlyInstanceSizeAcrossProviders(NodeType.ELECTABLE).get());
    }

    if (!hasAsymmetricShardsForNodeType(NodeType.ANALYTICS) && getTotalAnalyticsNumNodes() > 0) {
      diff.addItem(
          FieldDefs.ANALYTICS_INSTANCE_SIZE,
          "Analytics Instance Size",
          null,
          getOnlyInstanceSizeAcrossProviders(NodeType.ANALYTICS).get());
    }

    if (getLabels() != null) {
      diff.addItem(FieldDefs.LABELS, "Labels", null, getLabels().toString());
    }

    final Optional<Integer> diskIOPS = getDiskIOPSForClusterDiff();
    diskIOPS.ifPresent(iops -> diff.addItem(FieldDefs.DISK_IOPS, "Disk IOPS", null, iops));

    if (getCloudProviders().contains(CloudProvider.AWS)) {
      final List<HardwareSpec> hardwareSpec =
          getHardwareSpecsForProvider(CloudProvider.AWS, NodeType.ELECTABLE);
      diff.addItem(
          FieldDefs.ENCRYPT_EBS_VOLUME,
          "Encrypt EBS Volume",
          null,
          CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
              .getInstanceHardwareProvider()
              .getEncryptEBSVolume(hardwareSpec));

      diff.addItem(
          FieldDefs.EBS_VOLUME_TYPE, "EBS Volume Type", null, getEBSVolumeType().orElse(null));
    } else if (isTenantCluster()) {
      diff.addItem(
          RegionConfig.FieldDefs.REGION_NAME,
          String.format("%s Region", getTenantType().getDescription()),
          null,
          getTenantRegionDisplay());
    }

    if (getRootCertType() != null) {
      diff.addItem(
          FieldDefs.ROOT_CERT_TYPE, "TLS Certificate Root", null, getRootCertType().name());
    }

    diff.addItem(
        FieldDefs.VERSION_RELEASE_SYSTEM,
        "Version Release System",
        null,
        getVersionReleaseSystem().name());

    diff.addItem(
        FieldDefs.TERMINATION_PROTECTION_ENABLED,
        "Termination Protection Enabled",
        null,
        isTerminationProtectionEnabled());

    if (getNeedsSampleDataLoadAfter() != null) {
      diff.addItem(
          FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER,
          "Needs Sample Data Load After",
          null,
          getNeedsSampleDataLoadAfter());
    }

    if (getCreateSampleSearchIndex()) {
      diff.addItem(
          FieldDefs.CREATE_SAMPLE_SEARCH_INDEX,
          "Create Sample Search Index",
          null,
          getCreateSampleSearchIndex());
    }

    if (getSampleDatasetToLoad().isPresent()) {
      diff.addItem(
          FieldDefs.SAMPLE_DATASET_TO_LOAD,
          "Name of Sample Dataset To Load",
          null,
          getSampleDatasetToLoad().get().name().toLowerCase());
    }

    return diff;
  }

  public ItemDiffs getNewClusterReplicationSpecDiff() {
    final ItemDiffs diffs = new ItemDiffs();

    IntStream.range(0, getReplicationSpecsWithShardData().size())
        .forEach(
            index -> {
              final ReplicationSpec rs = getReplicationSpecsWithShardData().get(index);

              String shardDisplayName = "shard " + index;
              if (this.getClusterType().isReplicaSet()) {
                shardDisplayName = NDSDefaults.getReplicaSetNameForUnshardedCluster(this);
              }

              final ItemDiff diff = ItemDiff.forReplicationSpec(shardDisplayName, Status.NEW);

              diff.addItem(
                  FieldDefs.INSTANCE_SIZE,
                  "Instance Size",
                  null,
                  rs.getElectableInstanceSize().name());

              if (rs.getAnalyticsSpec().getNodeCount() > 0) {
                diff.addItem(
                    FieldDefs.ANALYTICS_INSTANCE_SIZE,
                    "Analytics Instance Size",
                    null,
                    rs.getAnalyticsInstanceSize().name());
              }
              diffs.addDiff(diff);
            });
    return diffs;
  }

  public ItemDiff getUpdatedClusterDiff(final ClusterDescription pOldClusterDescription) {
    final ItemDiff diff = ItemDiff.forClusterDescription(getName(), ItemDiff.Status.MODIFIED);

    if (getClusterType() != pOldClusterDescription.getClusterType()
        && !isServerlessTenantCluster()
        && !isFlexTenantCluster()) {
      diff.addItem(
          ClusterDescription.FieldDefs.CLUSTER_TYPE,
          "Cluster Type",
          pOldClusterDescription.getClusterType(),
          getClusterType());
    }

    if (getInternalClusterRole() != pOldClusterDescription.getInternalClusterRole()) {
      diff.addItem(
          ClusterDescription.FieldDefs.INTERNAL_CLUSTER_ROLE,
          "Internal Cluster Role",
          pOldClusterDescription.getInternalClusterRole(),
          getInternalClusterRole());
    }

    if (!getReplicaSetScalingStrategy()
        .equals(pOldClusterDescription.getReplicaSetScalingStrategy())) {
      diff.addItem(
          FieldDefs.REPLICA_SET_SCALING_STRATEGY,
          "Replica Set Scaling Strategy",
          pOldClusterDescription
              .getReplicaSetScalingStrategy()
              .map(ReplicaSetScalingStrategy::name)
              .orElse(null),
          getReplicaSetScalingStrategy().map(ReplicaSetScalingStrategy::name).orElse(null));
    }

    if (!getCloudProviders().equals(pOldClusterDescription.getCloudProviders())) {
      diff.addItem(
          CLOUD_PROVIDER_DIFF,
          "Cloud Providers",
          pOldClusterDescription.getCloudProviders().stream()
              .map(p -> p.getDescription())
              .collect(Collectors.toList()),
          getCloudProviders().stream().map(p -> p.getDescription()).collect(Collectors.toList()));
    }

    if (isBackupEnabled() != pOldClusterDescription.isBackupEnabled()) {
      diff.addItem(
          ClusterDescription.FieldDefs.BACKUP_ENABLED,
          "Backup Enabled",
          pOldClusterDescription.isBackupEnabled(),
          isBackupEnabled());
    }

    if (isDiskBackupEnabled() != pOldClusterDescription.isDiskBackupEnabled()) {
      diff.addItem(
          ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED,
          "Cloud Backup Enabled",
          pOldClusterDescription.isDiskBackupEnabled(),
          isDiskBackupEnabled());
    }

    if (isPitEnabled() != pOldClusterDescription.isPitEnabled()) {
      diff.addItem(
          FieldDefs.PIT_ENABLED,
          "Continuous Cloud Backup Enabled",
          pOldClusterDescription.isPitEnabled(),
          isPitEnabled());
    }

    if (getDiskSizeGB() != pOldClusterDescription.getDiskSizeGB()) {
      diff.addItem(
          ClusterDescription.FieldDefs.DISK_SIZE_GB,
          "Disk Size GB",
          pOldClusterDescription.getDiskSizeGB(),
          getDiskSizeGB());
    }

    if (!getMongoDBMajorVersion().equals(pOldClusterDescription.getMongoDBMajorVersion())) {
      diff.addItem(
          ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION,
          "MongoDB Major Version",
          pOldClusterDescription.getMongoDBMajorVersion(),
          getMongoDBMajorVersion());
    }
    if (!getReplicationSpecsWithShardData()
            .toString()
            .equals(pOldClusterDescription.getReplicationSpecsWithShardData().toString())
        && !isServerlessTenantCluster()
        && !isFlexTenantCluster()) {
      diff.addItem(
          FieldDefs.REPLICATION_SPEC_LIST,
          "Replication Spec",
          pOldClusterDescription.getReplicationSpecsWithShardData().toString(),
          getReplicationSpecsWithShardData().toString());
    }

    if (isPaused() != pOldClusterDescription.isPaused()) {
      diff.addItem(
          ClusterDescription.FieldDefs.IS_PAUSED,
          "Paused",
          pOldClusterDescription.isPaused(),
          isPaused());
    }

    if (!getEncryptionAtRestProvider()
        .equals(pOldClusterDescription.getEncryptionAtRestProvider())) {
      diff.addItem(
          ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
          "Encryption at Rest Provider",
          pOldClusterDescription.getEncryptionAtRestProvider().name(),
          getEncryptionAtRestProvider().name());
    }

    if (getBiConnector().isEnabled() != pOldClusterDescription.getBiConnector().isEnabled()) {
      diff.addItem(
          FieldDefs.BI_CONNECTOR,
          "BI Connector Enabled",
          pOldClusterDescription.getBiConnector().isEnabled(),
          getBiConnector().isEnabled());
    }

    if (!getBiConnector()
        .getReadPreference()
        .equals(pOldClusterDescription.getBiConnector().getReadPreference())) {
      diff.addItem(
          FieldDefs.BI_CONNECTOR,
          "BI Connector Read Preference",
          pOldClusterDescription.getBiConnector().getReadPreference().getValue(),
          getBiConnector().getReadPreference().getValue());
    }

    if (isAutoScaleSupported()) {
      final DiskGBAutoScaling diskGBAutoScaling = getAutoScaling(NodeTypeFamily.BASE).getDiskGB();
      if (diskGBAutoScaling.isEnabled()
          != pOldClusterDescription.getAutoScaling(NodeTypeFamily.BASE).getDiskGB().isEnabled()) {
        diff.addItem(
            RegionConfig.FieldDefs.AUTO_SCALING,
            "Disk Auto Scaling Enabled",
            pOldClusterDescription.getAutoScaling(NodeTypeFamily.BASE).isDiskGBEnabled(),
            getAutoScaling(NodeTypeFamily.BASE).isDiskGBEnabled());
      }

      final AutoIndexing autoIndexing = getAutoScaling(NodeTypeFamily.BASE).getAutoIndexing();
      final AutoIndexing oldAutoIndexing =
          pOldClusterDescription.getAutoScaling(NodeTypeFamily.BASE).getAutoIndexing();
      if (autoIndexing == null || oldAutoIndexing == null) {
        LOG.warn(
            "Cannot check if auto-indexing is different for clusterName={} in groupId={} with"
                + " newAutoIndexing={} and oldAutoIndexing={}",
            getName(),
            getGroupId(),
            autoIndexing,
            oldAutoIndexing);
      } else if (autoIndexing.isEnabled() != oldAutoIndexing.isEnabled()) {
        diff.addItem(
            RegionConfig.FieldDefs.AUTO_SCALING,
            "Auto Indexing Enabled",
            pOldClusterDescription.getAutoScaling(NodeTypeFamily.BASE).isAutoIndexingEnabled(),
            getAutoScaling(NodeTypeFamily.BASE).isAutoIndexingEnabled());
      }

      final ComputeAutoScaling computeAutoScaling =
          getAutoScaling(NodeTypeFamily.BASE).getCompute();
      final ComputeAutoScaling oldComputeAutoScaling =
          pOldClusterDescription.getAutoScaling(NodeTypeFamily.BASE).getCompute();
      if (computeAutoScaling.isEnabled() != oldComputeAutoScaling.isEnabled()) {
        diff.addItem(
            RegionConfig.FieldDefs.AUTO_SCALING,
            "Compute Auto Scaling Enabled",
            oldComputeAutoScaling.isEnabled(),
            computeAutoScaling.isEnabled());
      }

      if (computeAutoScaling.isScaleDownEnabled() != oldComputeAutoScaling.isScaleDownEnabled()) {
        diff.addItem(
            RegionConfig.FieldDefs.AUTO_SCALING,
            "Compute Auto Scaling Scale Down Enabled",
            oldComputeAutoScaling.isScaleDownEnabled(),
            computeAutoScaling.isScaleDownEnabled());
      }

      if (computeAutoScaling.isPredictiveEnabled() != oldComputeAutoScaling.isPredictiveEnabled()) {
        diff.addItem(
            RegionConfig.FieldDefs.AUTO_SCALING,
            "Predictive Compute Auto Scaling Enabled",
            oldComputeAutoScaling.isPredictiveEnabled(),
            computeAutoScaling.isPredictiveEnabled());
      }

      if (computeAutoScaling.getMinInstanceSize() != oldComputeAutoScaling.getMinInstanceSize()) {
        diff.addItem(
            RegionConfig.FieldDefs.AUTO_SCALING,
            "Compute Auto Scaling Min Instance Size",
            oldComputeAutoScaling.getMinInstanceSize(),
            computeAutoScaling.getMinInstanceSize());
      }

      if (computeAutoScaling.getMaxInstanceSize() != oldComputeAutoScaling.getMaxInstanceSize()) {
        diff.addItem(
            RegionConfig.FieldDefs.AUTO_SCALING,
            "Compute Auto Scaling Max Instance Size",
            oldComputeAutoScaling.getMaxInstanceSize(),
            computeAutoScaling.getMaxInstanceSize());
      }

      if (getAnalyticsAutoScaling().isPresent() && getTotalAnalyticsNumNodes() > 0) {
        final ComputeAutoScaling computeAnalyticsAutoScaling =
            getAutoScaling(NodeTypeFamily.ANALYTICS).getCompute();
        final ComputeAutoScaling oldComputeAnalyticsAutoScaling =
            pOldClusterDescription.getAutoScaling(NodeTypeFamily.ANALYTICS).getCompute();
        if (computeAnalyticsAutoScaling.isEnabled() != oldComputeAnalyticsAutoScaling.isEnabled()) {
          diff.addItem(
              RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
              "Analytics Compute Auto Scaling Enabled",
              oldComputeAnalyticsAutoScaling.isEnabled(),
              computeAnalyticsAutoScaling.isEnabled());
        }

        if (computeAnalyticsAutoScaling.isScaleDownEnabled()
            != oldComputeAnalyticsAutoScaling.isScaleDownEnabled()) {
          diff.addItem(
              RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
              "Analytics Compute Auto Scaling Scale Down Enabled",
              oldComputeAnalyticsAutoScaling.isScaleDownEnabled(),
              computeAnalyticsAutoScaling.isScaleDownEnabled());
        }

        if (computeAnalyticsAutoScaling.getMinInstanceSize()
            != oldComputeAnalyticsAutoScaling.getMinInstanceSize()) {
          diff.addItem(
              RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
              "Analytics Compute Auto Scaling Min Instance Size",
              oldComputeAnalyticsAutoScaling.getMinInstanceSize(),
              computeAnalyticsAutoScaling.getMinInstanceSize());
        }

        if (computeAnalyticsAutoScaling.getMaxInstanceSize()
            != oldComputeAnalyticsAutoScaling.getMaxInstanceSize()) {
          diff.addItem(
              RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
              "Analytics Compute Auto Scaling Max Instance Size",
              oldComputeAnalyticsAutoScaling.getMaxInstanceSize(),
              computeAnalyticsAutoScaling.getMaxInstanceSize());
        }
      }
    }

    if (!getGeoSharding()
        .getCustomZoneMapping()
        .equals(pOldClusterDescription.getGeoSharding().getCustomZoneMapping())) {
      diff.addItem(
          FieldDefs.GEO_SHARDING,
          "Custom Zone Mapping",
          pOldClusterDescription.getCustomZoneMappingString(),
          getCustomZoneMappingString());
    }

    if (!getGeoSharding()
        .getManagedNamespaces()
        .equals(pOldClusterDescription.getGeoSharding().getManagedNamespaces())) {
      diff.addItem(
          FieldDefs.GEO_SHARDING,
          "Managed Namespaces",
          pOldClusterDescription.getGeoSharding().getManagedNamespaces().toString(),
          getGeoSharding().getManagedNamespaces().toString());
    }

    final boolean neverAsymmetricElectableNodes =
        !this.hasAsymmetricShardsForNodeType(NodeType.ELECTABLE)
            && !pOldClusterDescription.hasAsymmetricShardsForNodeType(NodeType.ELECTABLE);
    final boolean neverAsymmetricAnalyticsNodes =
        !this.hasAsymmetricShardsForNodeType(NodeType.ANALYTICS)
            && !pOldClusterDescription.hasAsymmetricShardsForNodeType(NodeType.ANALYTICS);

    if (neverAsymmetricElectableNodes
        && !Objects.equals(
            getOnlyInstanceSizeAcrossProviders(NodeType.ELECTABLE),
            pOldClusterDescription.getOnlyInstanceSizeAcrossProviders(NodeType.ELECTABLE))) {
      diff.addItem(
          FieldDefs.INSTANCE_SIZE,
          "Instance Size",
          pOldClusterDescription.getOnlyInstanceSizeAcrossProviders(NodeType.ELECTABLE).get(),
          getOnlyInstanceSizeAcrossProviders(NodeType.ELECTABLE).get());
    }

    if (neverAsymmetricAnalyticsNodes
        && !Objects.equals(
            getOnlyInstanceSizeAcrossProviders(NodeType.ANALYTICS),
            pOldClusterDescription.getOnlyInstanceSizeAcrossProviders(NodeType.ANALYTICS))
        && getTotalAnalyticsNumNodes() > 0) {
      diff.addItem(
          FieldDefs.ANALYTICS_INSTANCE_SIZE,
          "Analytics Instance Size",
          pOldClusterDescription
              .getOnlyInstanceSizeAcrossProviders(NodeType.ANALYTICS)
              .orElse(null),
          getOnlyInstanceSizeAcrossProviders(NodeType.ANALYTICS).orElse(null));
    }

    if (!Objects.equals(getLabels(), pOldClusterDescription.getLabels())) {
      diff.addItem(
          FieldDefs.LABELS,
          "Labels",
          pOldClusterDescription.getLabels().toString(),
          getLabels().toString());
    }

    if (isTerminationProtectionEnabled()
        != pOldClusterDescription.isTerminationProtectionEnabled()) {
      diff.addItem(
          FieldDefs.TERMINATION_PROTECTION_ENABLED,
          "Termination Protection Enabled",
          pOldClusterDescription.isTerminationProtectionEnabled(),
          isTerminationProtectionEnabled());
    }

    if (getNeedsSampleDataLoadAfter() != pOldClusterDescription.getNeedsSampleDataLoadAfter()) {
      diff.addItem(
          FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER,
          "Needs Sample Data Load After",
          pOldClusterDescription.getNeedsSampleDataLoadAfter(),
          getNeedsSampleDataLoadAfter());
    }

    if (getCreateSampleSearchIndex() != pOldClusterDescription.getCreateSampleSearchIndex()) {
      diff.addItem(
          FieldDefs.CREATE_SAMPLE_SEARCH_INDEX,
          "Create Sample Search Index",
          pOldClusterDescription.getCreateSampleSearchIndex(),
          getCreateSampleSearchIndex());
    }

    final String newSampleDatasetToLoad =
        getSampleDatasetToLoad().isPresent()
            ? getSampleDatasetToLoad().get().name().toLowerCase()
            : null;
    final String oldSampleDatasetToLoad =
        pOldClusterDescription.getSampleDatasetToLoad().isPresent()
            ? pOldClusterDescription.getSampleDatasetToLoad().get().name().toLowerCase()
            : null;

    if (!newSampleDatasetToLoad.equals(oldSampleDatasetToLoad)) {
      diff.addItem(
          FieldDefs.SAMPLE_DATASET_TO_LOAD,
          "Name of Sample Dataset To Load",
          oldSampleDatasetToLoad,
          newSampleDatasetToLoad);
    }

    final Optional<Integer> oldDiskIops = pOldClusterDescription.getDiskIOPSForClusterDiff();
    final Optional<Integer> newDiskIops = getDiskIOPSForClusterDiff();
    if (!oldDiskIops.equals(newDiskIops)) {
      diff.addItem(
          FieldDefs.DISK_IOPS, "Disk IOPS", oldDiskIops.orElse(null), newDiskIops.orElse(null));
    }

    final Optional<VolumeType> oldEBSVolumeType = pOldClusterDescription.getEBSVolumeType();
    final Optional<VolumeType> newEBSVolumeType = getEBSVolumeType();
    if (!Objects.equals(oldEBSVolumeType, newEBSVolumeType)) {
      diff.addItem(
          FieldDefs.EBS_VOLUME_TYPE,
          "EBS Volume Type",
          oldEBSVolumeType.orElse(null),
          newEBSVolumeType.orElse(null));
    }

    if (getCloudProviders().contains(CloudProvider.AWS)) {
      final List<HardwareSpec> newHardwareSpecs =
          getHardwareSpecsForProvider(CloudProvider.AWS, NodeType.ELECTABLE);
      final boolean newHardwareEncryptEBSVolume =
          CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
              .getInstanceHardwareProvider()
              .getEncryptEBSVolume(newHardwareSpecs);
      if (!pOldClusterDescription.getCloudProviders().contains(CloudProvider.AWS)) {
        diff.addItem(
            FieldDefs.ENCRYPT_EBS_VOLUME, "Encrypt EBS Volume", false, newHardwareEncryptEBSVolume);
      } else {
        final List<HardwareSpec> oldHardwareSpecs =
            pOldClusterDescription.getHardwareSpecsForProvider(
                CloudProvider.AWS, NodeType.ELECTABLE);
        final boolean oldHardwareEncryptEBSVolume =
            CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                .getInstanceHardwareProvider()
                .getEncryptEBSVolume(oldHardwareSpecs);
        if (newHardwareEncryptEBSVolume != oldHardwareEncryptEBSVolume) {
          diff.addItem(
              FieldDefs.ENCRYPT_EBS_VOLUME,
              "Encrypt EBS Volume",
              oldHardwareEncryptEBSVolume,
              newHardwareEncryptEBSVolume);
        }
      }
    }
    if (isTenantCluster()) {
      if (!getRegionName().equals(pOldClusterDescription.getRegionName())) {
        diff.addItem(
            RegionConfig.FieldDefs.REGION_NAME,
            String.format("%s Region", getTenantType().getDescription()),
            pOldClusterDescription.getTenantRegionDisplay(),
            getTenantRegionDisplay());
      }
    }

    if (!getRootCertType().equals(pOldClusterDescription.getRootCertType())) {
      diff.addItem(
          FieldDefs.ROOT_CERT_TYPE,
          "TLS Certificate Root",
          pOldClusterDescription.getRootCertType().name(),
          getRootCertType().name());
    }

    if (!getVersionReleaseSystem().equals(pOldClusterDescription.getVersionReleaseSystem())) {
      diff.addItem(
          FieldDefs.VERSION_RELEASE_SYSTEM,
          "Version Release System",
          pOldClusterDescription.getVersionReleaseSystem() != null
              ? pOldClusterDescription.getVersionReleaseSystem().name()
              : VersionReleaseSystem.LTS.name(),
          getVersionReleaseSystem().name());
    }

    if (pOldClusterDescription.getDiskWarmingMode().isPresent()
        && !getDiskWarmingMode().equals(pOldClusterDescription.getDiskWarmingMode())) {
      diff.addItem(
          FieldDefs.DISK_WARMING_MODE,
          "Fast Disk Pre-Warming",
          getDisplayBooleanValueForDiskWarming(pOldClusterDescription),
          getDisplayBooleanValueForDiskWarming(this));
    }

    if (pOldClusterDescription.getRedactClientLogData().isPresent()
        && !getRedactClientLogData().equals(pOldClusterDescription.getRedactClientLogData())) {
      diff.addItem(
          FieldDefs.REDACT_CLIENT_LOG_DATA,
          "Redact Client Log Data",
          pOldClusterDescription.getRedactClientLogData().orElse(null),
          getRedactClientLogData().orElse(null));
    }

    if (isServerlessTenantCluster()) {
      if (pOldClusterDescription.isServerlessTenantCluster()) {
        if (!getServerlessBackupOptions()
            .equals(pOldClusterDescription.getServerlessBackupOptions())) {
          diff.addItem(
              FieldDefs.SERVERLESS_BACKUP_OPTIONS,
              "Serverless Backup",
              !pOldClusterDescription
                      .getServerlessBackupOptions()
                      .isServerlessContinuousBackupEnabled()
                  ? "Basic"
                  : "Serverless Continuous",
              !getServerlessBackupOptions().isServerlessContinuousBackupEnabled()
                  ? "Basic"
                  : "Serverless Continuous");
        }
      } else {
        diff.addItem(
            FieldDefs.SERVERLESS_BACKUP_OPTIONS,
            "Serverless Backup",
            null,
            !getServerlessBackupOptions().isServerlessContinuousBackupEnabled()
                ? "Basic"
                : "Serverless Continuous");
      }
    }

    if (getSwapIpMaintenanceRoundCompleted().isPresent()) {
      if (pOldClusterDescription.getSwapIpMaintenanceRoundCompleted().isPresent()) {
        if (getSwapIpMaintenanceRoundCompleted().get()
            != pOldClusterDescription.getSwapIpMaintenanceRoundCompleted().get()) {
          diff.addItem(
              FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED,
              "IP Migration Round",
              pOldClusterDescription.getSwapIpMaintenanceRoundCompleted().get().name(),
              getSwapIpMaintenanceRoundCompleted().get().name());
        }
      } else {
        diff.addItem(
            FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED,
            "IP Migration Round",
            null,
            getSwapIpMaintenanceRoundCompleted().get().name());
      }
    } else if (pOldClusterDescription.getSwapIpMaintenanceRoundCompleted().isPresent()) {
      diff.addItem(
          FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED,
          "IP Migration Round",
          pOldClusterDescription.getSwapIpMaintenanceRoundCompleted().get().name(),
          null);
    }

    return diff;
  }

  public ItemDiffs getUpdatedClusterReplicationSpecDiff(
      final ClusterDescription pOldClusterDescription) {
    final ItemDiffs diffs = new ItemDiffs();

    IntStream.range(0, getReplicationSpecsWithShardData().size())
        .forEach(
            index -> {
              final ReplicationSpec rs = getReplicationSpecsWithShardData().get(index);
              final Optional<ReplicationSpec> oldReplicationSpec =
                  pOldClusterDescription.getReplicationSpecById(rs.getId());

              if (oldReplicationSpec.isEmpty()) {
                // new replication spec added
                final ItemDiff diff = ItemDiff.forReplicationSpec("shard " + index, Status.NEW);
                diff.addItem(
                    FieldDefs.IS_ASYMMETRIC_SHARD,
                    "Is Asymmetric Shard",
                    null,
                    isAsymmetricShard(rs));
                diff.addItem(
                    FieldDefs.INSTANCE_SIZE,
                    "Instance Size",
                    null,
                    rs.getElectableInstanceSize().name());

                diffs.addDiff(diff);
                return;
              }

              // replication spec was updated
              String shardDisplayName = "shard " + index;
              final ItemDiff diff =
                  ItemDiff.forReplicationSpec(shardDisplayName, ItemDiff.Status.MODIFIED);

              if ((!rs.getElectableInstanceSize()
                      .getCloudProvider()
                      .equals(
                          oldReplicationSpec.get().getElectableInstanceSize().getCloudProvider()))
                  || (!rs.getElectableInstanceSize()
                      .equals(oldReplicationSpec.get().getElectableInstanceSize()))) {
                diff.addItem(
                    FieldDefs.INSTANCE_SIZE,
                    "Instance Size",
                    oldReplicationSpec.get().getElectableInstanceSize().name(),
                    rs.getElectableInstanceSize().name());
              }

              // either new analytics node is added or analytics instance size change
              final Optional<NDSInstanceSize> analyticsInstanceSize =
                  rs.getTotalAnalyticsNodes() > 0
                      ? Optional.of(rs.getAnalyticsInstanceSize())
                      : Optional.empty();
              final Optional<NDSInstanceSize> oldAnalyticsInstanceSize =
                  oldReplicationSpec.get().getTotalAnalyticsNodes() > 0
                      ? Optional.of(oldReplicationSpec.get().getAnalyticsInstanceSize())
                      : Optional.empty();

              if (!analyticsInstanceSize.equals(oldAnalyticsInstanceSize)) {
                diff.addItem(
                    FieldDefs.ANALYTICS_INSTANCE_SIZE,
                    "Analytics Instance Size",
                    oldAnalyticsInstanceSize.orElse(null),
                    analyticsInstanceSize.orElse(null));
              }

              diffs.addDiff(diff);
            });
    return diffs;
  }

  private boolean isAsymmetricShard(final ReplicationSpec pReplicationSpec) {
    return getReplicationSpecsWithShardData().stream()
        .anyMatch(
            rs ->
                !rs.getElectableInstanceSize().equals(pReplicationSpec.getElectableInstanceSize())
                    || !rs.getAnalyticsInstanceSize()
                        .equals(pReplicationSpec.getAnalyticsInstanceSize()));
  }

  public ItemDiff getUpdatedClusterInternalFieldsDiff(
      final ClusterDescription pOldClusterDescription) {
    final ItemDiff diff = ItemDiff.forClusterDescription(getName(), ItemDiff.Status.MODIFIED);
    if (!Objects.equals(
        getInstanceFamily(NodeType.ELECTABLE),
        pOldClusterDescription.getInstanceFamily(NodeType.ELECTABLE))) {
      diff.addItem(
          FieldDefs.INSTANCE_FAMILY,
          "Instance Family",
          pOldClusterDescription.getInstanceFamily(NodeType.ELECTABLE),
          getInstanceFamily(NodeType.ELECTABLE));
    }
    if (!Objects.equals(
            getInstanceFamily(NodeType.ANALYTICS),
            pOldClusterDescription.getInstanceFamily(NodeType.ANALYTICS))
        && getTotalAnalyticsNumNodes() > 0) {
      diff.addItem(
          FieldDefs.ANALYTICS_INSTANCE_FAMILY,
          "Analytics Instance Family",
          pOldClusterDescription.getInstanceFamily(NodeType.ANALYTICS),
          getInstanceFamily(NodeType.ANALYTICS));
    }

    if (getAutoScalingMode() != pOldClusterDescription.getAutoScalingMode()) {
      diff.addItem(
          FieldDefs.AUTO_SCALING_MODE,
          "Auto Scaling Mode",
          pOldClusterDescription.getAutoScalingMode().name(),
          getAutoScalingMode().name());
    }

    return diff;
  }

  public ItemDiff getDeletedClusterDiff() {
    final ItemDiff diff = ItemDiff.forClusterDescription(getName(), ItemDiff.Status.REMOVED);

    if (!isServerlessTenantCluster() && !isFlexTenantCluster()) {
      diff.addItem(FieldDefs.CLUSTER_TYPE, "Cluster Type", getClusterType(), null);
    }

    diff.addItem(
        FieldDefs.INTERNAL_CLUSTER_ROLE, "Internal Cluster Role", getInternalClusterRole(), null);

    diff.addItem(
        CLOUD_PROVIDER_DIFF,
        "Cloud Provider",
        isCrossCloudCluster()
            ? getCloudProviders().stream()
                .map(CloudProvider::getDescription)
                .collect(Collectors.toList())
            : getLegacyProvider().getDescription(),
        null);

    diff.addItem(FieldDefs.BACKUP_ENABLED, "Backup Enabled", isBackupEnabled(), null);

    diff.addItem(
        FieldDefs.DISK_BACKUP_ENABLED, "Cloud Backup Enabled", isDiskBackupEnabled(), null);

    if (isDedicatedCluster()) {
      diff.addItem(
          FieldDefs.REPLICA_SET_SCALING_STRATEGY,
          "Replica Set Scaling Strategy",
          getReplicaSetScalingStrategy().map(ReplicaSetScalingStrategy::name).orElse(null),
          null);

      diff.addItem(
          FieldDefs.REDACT_CLIENT_LOG_DATA,
          "Redact Client Log Data",
          getRedactClientLogData().orElse(null),
          null);
    }

    diff.addItem(FieldDefs.PIT_ENABLED, "Continuous Cloud Backup Enabled", isPitEnabled(), null);

    diff.addItem(FieldDefs.DISK_SIZE_GB, "Disk Size GB", getDiskSizeGB(), null);

    diff.addItem(
        FieldDefs.MONGODB_MAJOR_VERSION, "MongoDB Major Version", getMongoDBMajorVersion(), null);

    if (!isServerlessTenantCluster() && !isFlexTenantCluster()) {
      diff.addItem(
          FieldDefs.REPLICATION_SPEC_LIST,
          "Replication Spec",
          getReplicationSpecsWithShardData().toString(),
          null);
    }

    diff.addItem(
        FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
        "Encryption at Rest Provider",
        getEncryptionAtRestProvider().name(),
        null);

    diff.addItem(
        FieldDefs.BI_CONNECTOR, "BI Connector Enabled", getBiConnector().isEnabled(), null);

    diff.addItem(
        FieldDefs.BI_CONNECTOR,
        "BI Connector Read Preference",
        getBiConnector().getReadPreference().getValue(),
        null);

    if (isAutoScaleSupported()) {
      final DiskGBAutoScaling diskGBAutoScaling = getAutoScaling(NodeTypeFamily.BASE).getDiskGB();
      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Disk Auto Scaling Enabled",
          diskGBAutoScaling.isEnabled(),
          null);

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Auto Indexing Enabled",
          getAutoScaling(NodeTypeFamily.BASE).isAutoIndexingEnabled(),
          null);

      final ComputeAutoScaling computeAutoScaling =
          getAutoScaling(NodeTypeFamily.BASE).getCompute();
      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Compute Auto Scaling Enabled",
          computeAutoScaling.isEnabled(),
          null);

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Compute Auto Scaling Scale Down Enabled",
          computeAutoScaling.isScaleDownEnabled(),
          null);

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Predictive Compute Auto Scaling Enabled",
          computeAutoScaling.isPredictiveEnabled(),
          null);

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Compute Auto Scaling Min Instance Size",
          computeAutoScaling.isScaleDownEnabled(),
          null);

      diff.addItem(
          RegionConfig.FieldDefs.AUTO_SCALING,
          "Compute Auto Scaling Max Instance Size",
          computeAutoScaling.getMaxInstanceSize(),
          null);

      if (getAnalyticsAutoScaling().isPresent()) {
        final ComputeAutoScaling computeAnalyticsAutoScaling =
            getAutoScaling(NodeTypeFamily.ANALYTICS).getCompute();
        diff.addItem(
            RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
            "Analytics Compute Auto Scaling Enabled",
            computeAnalyticsAutoScaling.isEnabled(),
            null);

        diff.addItem(
            RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
            "Analytics Compute Auto Scaling Scale Down Enabled",
            computeAnalyticsAutoScaling.isScaleDownEnabled(),
            null);

        diff.addItem(
            RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
            "Analytics Compute Auto Scaling Min Instance Size",
            computeAnalyticsAutoScaling.getMinInstanceSize(),
            null);

        diff.addItem(
            RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING,
            "Analytics Compute Auto Scaling Max Instance Size",
            computeAnalyticsAutoScaling.getMaxInstanceSize(),
            null);
      }
    }

    if (!hasAsymmetricShardsForNodeType(NodeType.ELECTABLE) && !isFlexTenantCluster()) {
      diff.addItem(
          FieldDefs.INSTANCE_SIZE,
          "Instance Size",
          getOnlyInstanceSizeAcrossProviders(NodeType.ELECTABLE).get(),
          null);
    }

    if (!hasAsymmetricShardsForNodeType(NodeType.ANALYTICS) && getTotalAnalyticsNumNodes() > 0) {
      diff.addItem(
          FieldDefs.ANALYTICS_INSTANCE_SIZE,
          "Analytics Instance Size",
          getOnlyInstanceSizeAcrossProviders(NodeType.ANALYTICS).get(),
          null);
    }

    if (getLabels() != null) {
      diff.addItem(FieldDefs.LABELS, "Labels", getLabels().toString(), null);
    }

    diff.addItem(
        FieldDefs.TERMINATION_PROTECTION_ENABLED,
        "Termination Protection Enabled",
        isTerminationProtectionEnabled(),
        null);

    if (getCloudProviders().contains(CloudProvider.AWS)) {
      final List<HardwareSpec> hardwareSpecs =
          getHardwareSpecsForProvider(CloudProvider.AWS, NodeType.ELECTABLE);
      final Set<Integer> iopsValues =
          hardwareSpecs.stream()
              .map(hs -> hs.getConfiguredDiskIops(getDiskSizeGB()))
              .filter(Objects::nonNull)
              .collect(Collectors.toSet());
      if (iopsValues.size() == 1) {
        diff.addItem(FieldDefs.DISK_IOPS, "Disk IOPS", iopsValues.iterator().next(), null);
      } else {
        // TODO: ISS IOPS
      }
      diff.addItem(
          FieldDefs.ENCRYPT_EBS_VOLUME,
          "Encrypt EBS Volume",
          CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
              .getInstanceHardwareProvider()
              .getEncryptEBSVolume(hardwareSpecs),
          null);
      diff.addItem(
          FieldDefs.EBS_VOLUME_TYPE, "EBS Volume Type", getEBSVolumeType().orElse(null), null);
    } else if (isTenantCluster()) {
      diff.addItem(
          RegionConfig.FieldDefs.REGION_NAME,
          String.format("%s Region", getTenantType().getDescription()),
          getTenantRegionDisplay(),
          null);
    }

    if (getRootCertType() != null) {
      diff.addItem(
          FieldDefs.ROOT_CERT_TYPE, "TLS Certificate Root", getRootCertType().name(), null);
    }

    diff.addItem(
        FieldDefs.VERSION_RELEASE_SYSTEM,
        "Version Release System",
        getVersionReleaseSystem().name(),
        null);

    return diff;
  }

  public ItemDiffs getDeletedClusterReplicationSpecDiff() {
    final ItemDiffs diffs = new ItemDiffs();

    IntStream.range(0, getReplicationSpecsWithShardData().size())
        .forEach(
            index -> {
              final ReplicationSpec rs = getReplicationSpecsWithShardData().get(index);

              String shardDisplayName = "shard " + index;
              if (this.getClusterType().isReplicaSet()) {
                shardDisplayName = NDSDefaults.getReplicaSetNameForUnshardedCluster(this);
              }

              final ItemDiff diff = ItemDiff.forReplicationSpec(shardDisplayName, Status.REMOVED);

              diff.addItem(
                  FieldDefs.INSTANCE_SIZE,
                  "Instance Size",
                  rs.getElectableInstanceSize().name(),
                  null);

              if (rs.getAnalyticsSpec().getNodeCount() > 0) {
                diff.addItem(
                    FieldDefs.ANALYTICS_INSTANCE_SIZE,
                    "Analytics Instance Size",
                    rs.getAnalyticsInstanceSize().name(),
                    null);
              }
              diffs.addDiff(diff);
            });
    return diffs;
  }

  public ItemDiff getDeletedClusterInternalFieldsDiff() {
    final ItemDiff diff = ItemDiff.forClusterDescription(getName(), ItemDiff.Status.REMOVED);
    if (getInstanceFamily(NodeType.ELECTABLE) != null) {
      diff.addItem(
          FieldDefs.INSTANCE_FAMILY,
          "Instance Family",
          getInstanceFamily(NodeType.ELECTABLE),
          null);
    }

    if (getInstanceFamily(NodeType.ANALYTICS) != null) {
      diff.addItem(
          FieldDefs.ANALYTICS_INSTANCE_FAMILY,
          "Analytics Instance Family",
          getInstanceFamily(NodeType.ANALYTICS),
          null);
    }
    return diff;
  }

  public double getDiskSizeGB() {
    return _diskSizeGB;
  }

  /**
   * Returns the disk size in GB as originally specified by the customer.
   *
   * <p>This represents the disk size value that was explicitly provided by the customer when
   * creating or modifying the cluster configuration, before any system adjustments or calculations
   * are applied.
   *
   * @return the customer-provided disk size in gigabytes, or 0 if no custom size was specified
   */
  public double getCustomerProvidedDiskSizeGB() {
    return _customerProvidedDiskSizeGB;
  }

  /**
   * Returns the configuration setting that determines whether effective cluster fields should be
   * used.
   *
   * <p>Effective cluster fields provide a mechanism to override or supplement the standard cluster
   * configuration with computed or derived values. This setting controls whether the system should
   * apply these effective field values when processing cluster API operations.
   *
   * @return the effective fields configuration setting, indicating whether effective cluster fields
   *     are enabled or disabled
   */
  public EffectiveFields getUseEffectiveClusterFields() {
    return _useEffectiveClusterFields;
  }

  public String[] getMongoDBUriHosts() {
    return _mongoDBUriHosts;
  }

  public String getFirstMongoDBUriHost() {
    return _mongoDBUriHosts.length > 0 ? _mongoDBUriHosts[0] : null;
  }

  public Optional<String> getFirstPrivateMongoDBUriHost() {
    return _privateMongoDBUriHosts.length > 0
        ? Optional.ofNullable(_privateMongoDBUriHosts[0])
        : Optional.empty();
  }

  public Date getMongoDBUriHostsLastUpdateDate() {
    return _mongoDBUriHostsLastUpdateDate;
  }

  public State getState() {
    return _state;
  }

  public ClusterType getClusterType() {
    return _clusterType;
  }

  public InternalClusterRole getInternalClusterRole() {
    return _internalClusterRole;
  }

  public List<ObjectId> getRestoreJobIds() {
    return _restoreJobIds;
  }

  public Optional<RestoreJobType> getRestoreJobType() {
    return Optional.ofNullable(_restoreJobType);
  }

  public RootCertType getRootCertType() {
    if (_rootCertType == null) {
      return RootCertType.DST;
    }
    return _rootCertType;
  }

  public VersionReleaseSystem getVersionReleaseSystem() {
    return _versionReleaseSystem;
  }

  public Optional<String> getContinuousDeliveryFCV() {
    return _continuousDeliveryFCV;
  }

  public boolean isDeleteRequested() {
    return _deleteRequested;
  }

  public Optional<DeleteClusterReason> getDeleteReason() {
    return Optional.ofNullable(_deleteReason);
  }

  public boolean isDeleted() {
    return _state == State.DELETED;
  }

  public boolean isDeletedOrDeleteRequested() {
    return isDeleted() || isDeleteRequested();
  }

  public boolean isBackupEnabled() {
    return _backupEnabled;
  }

  public boolean isDiskBackupEnabled() {
    return _diskBackupEnabled;
  }

  public boolean isPitEnabled() {
    return _pitEnabled;
  }

  public boolean isStopContinuousBackup() {
    return _stopContinuousBackup;
  }

  public boolean isServerlessContinuousBackupEnabled() {
    return isServerlessTenantCluster()
        && getServerlessBackupOptions().isServerlessContinuousBackupEnabled();
  }

  public boolean getForceReplicaSetReconfig() {
    return _forceReplicaSetReconfig != null;
  }

  public Optional<Date> getForceReplicaSetReconfigVersion() {
    return Optional.ofNullable(_forceReplicaSetReconfig);
  }

  public List<String> getForceReplicaSetReconfigHostsToSkip() {
    return _forceReplicaSetReconfigHostsToSkip;
  }

  public String getDisplayBooleanValueForDiskWarming(final ClusterDescription pClusterDescription) {
    final Optional<DiskWarmingMode> modeOpt = pClusterDescription.getDiskWarmingMode();
    if (modeOpt.isPresent() && !modeOpt.get().isVisibleEarlier()) {
      return "enabled";
    }
    return "disabled";
  }

  public boolean isMTM() {
    return _isMTM;
  }

  public boolean isMTMSentinel() {
    return _isMTMSentinel;
  }

  public boolean isEligibleForReducedFlexPricing() {
    return _isEligibleForReducedFlexPricing;
  }

  public boolean isPaused() {
    return _isPaused;
  }

  public boolean isPausedWithinDuration(@NotNull Duration duration) {
    if (_pausedDate == null) {
      return false;
    }

    return _isPaused && isDateWithinDuration(_pausedDate, duration);
  }

  public Optional<Date> getDeletedDate() {
    return Optional.ofNullable(_deletedDate);
  }

  public Optional<Date> getEnsureClusterConnectivityAfter() {
    return Optional.ofNullable(_ensureClusterConnectivityAfter);
  }

  public Optional<Date> getNeedsMongoDBConfigPublishAfter() {
    return Optional.ofNullable(_needsMongoDBConfigPublishAfter);
  }

  public ProcessRestartAllowedState getNeedsMongoDBConfigPublishRestartAllowed() {
    return _needsMongoDBConfigPublishRestartAllowed;
  }

  @Deprecated
  // This method throws an exception for multi cloud clusters. use getCloudProviders() instead.
  public CloudProvider getCloudProvider() {
    final CloudProvider cloudProvider =
        _replicationSpecList.get(0).getRegionConfigs().get(0).getCloudProvider();
    if (!_replicationSpecList.stream()
        .flatMap(spec -> spec.getRegionConfigs().stream())
        .allMatch(config -> config.getCloudProvider().equals(cloudProvider))) {
      throw new IllegalStateException(MORE_THAN_ONE_CLOUD_PROVIDER_ERROR_MESSAGE);
    }
    return cloudProvider;
  }

  public Optional<CloudProvider> getOnlyCloudProvider() {
    return findOnlyElement(getCloudProviders());
  }

  public Set<CloudProvider> getCloudProviders() {
    return _replicationSpecList.stream()
        .flatMap(rspec -> rspec.getRegionConfigs().stream())
        .map(RegionConfig::getCloudProvider)
        .collect(Collectors.toSet());
  }

  public Set<CloudProvider> getCloudProvidersForNodeType(final NodeType nodeType) {
    return _replicationSpecList.stream()
        .flatMap(rspec -> rspec.getRegionConfigs().stream())
        .filter(rc -> rc.getHardwareSpecByNodeType(nodeType).getNodeCount() > 0)
        .map(RegionConfig::getCloudProvider)
        .collect(Collectors.toSet());
  }

  public boolean isCrossCloudCluster() {
    return getCloudProviders().size() > 1;
  }

  public String getBackupRegion() {
    return CloudProviderRegistry.getByCloudProvider(getCloudProvider())
        .getRegionProvider()
        .getBackupRegion(getUniqueId(), getHighestPriorityRegion());
  }

  public RegionName getHighestPriorityRegion() {
    return getReplicationSpecsWithShardData().get(0).getHighestPriorityRegion();
  }

  public int getRegionPriority(final ObjectId pReplicationSpecId, final RegionName pRegion) {
    return getReplicationSpecById(pReplicationSpecId)
        .flatMap(spec -> spec.getRegionConfigByRegion(pRegion))
        .map(RegionConfig::getPriority)
        .orElse(0);
  }

  public boolean isAutoScaleSupported() {
    return !isTenantCluster();
  }

  public Optional<Date> getNeedsPrioritiesResetForPriorityTakeoverAfter() {
    return Optional.ofNullable(_needsPrioritiesResetForPriorityTakeoverAfter);
  }

  public Optional<Date> getAlwaysManagedDefaultRWConcernSince() {
    return _alwaysManagedDefaultRWConcernSince;
  }

  /**
   * Returns the auto-sharding configuration for this cluster.
   *
   * @return the auto-sharding configuration, or empty if not configured
   */
  public Optional<AutoSharding> getAutoSharding() {
    return Optional.ofNullable(_autoSharding);
  }

  /**
   * Gets the autoscaling configuration with fallback logic.
   *
   * @return if {@link NodeTypeFamily#ANALYTICS} - returns analytics autoscaling if present;
   *     otherwise falls back to base. TODO CLOUDP-132328 - this needs to respect validation logic
   *     and only fall back to base if it is compatible with the analytics spec If {@link
   *     NodeTypeFamily#BASE} - returns base autoscaling if present
   */
  @Nullable
  public AutoScaling getAutoScaling(final NodeTypeFamily pNodeTypeFamily) {
    if (pNodeTypeFamily == NodeTypeFamily.BASE) {
      return getBaseAutoScaling();
    } else if (pNodeTypeFamily == NodeTypeFamily.ANALYTICS) {
      return getAnalyticsAutoScalingWithFallbackToBase();
    } else {
      throw new IllegalArgumentException(
          String.format("Invalid node type family %s", pNodeTypeFamily));
    }
  }

  public AutoScaling getBaseAutoScaling() {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
        .map(RegionConfig::getBaseAutoScaling)
        .filter(Objects::nonNull)
        .findFirst()
        .orElse(null);
  }

  private Optional<AutoScaling> getAnalyticsAutoScaling() {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
        .map(RegionConfig::getAnalyticsAutoScaling)
        .filter(Objects::nonNull)
        .filter(AutoScaling::isNotEmpty)
        .findFirst();
  }

  public AutoScaling getAnalyticsAutoScalingWithFallbackToBase() {
    final Optional<AutoScaling> analyticsAutoScalingOpt = getAnalyticsAutoScaling();
    if (analyticsAutoScalingOpt.isEmpty()) {
      final Set<NDSInstanceSize> analyticsInstanceSizes =
          getHardwareSpecs(NodeType.ANALYTICS).stream()
              .map(HardwareSpec::getInstanceSize)
              .collect(Collectors.toSet());
      final AutoScaling baseAutoScaling = getAutoScaling(NodeTypeFamily.BASE);
      if (!analyticsInstanceSizes.isEmpty()) {
        if (baseAutoScaling != null) {
          if (baseAutoScaling.isComputeEnabled()) {
            if (analyticsInstanceSizes.stream()
                .allMatch(baseAutoScaling.getCompute()::isCompatibleWithInstanceSize)) {
              LOG.trace(
                  "Cannot find analytics autoscaling for clusterName={} in groupId={}. Defaulting"
                      + " to get base autoscaling",
                  getName(),
                  getGroupId());
              return baseAutoScaling;
            } else if (getTotalAnalyticsNumNodes() == 0) {
              // Return fully disabled auto scaling document.
              return baseAutoScaling.getAutoScalingWithFlagsSet(false, false, false, false);
            } else {
              throw new IllegalStateException(
                  String.format(
                      "Ambiguous analytics autoscaling configuration for cluster %s in group %s",
                      getName(), getGroupId()));
            }
          }
        }
      }
      return baseAutoScaling;
    }

    return analyticsAutoScalingOpt.get();
  }

  public Optional<AutoScaling> getAnalyticsAutoScalingWithoutFallbackToBase() {
    return getAnalyticsAutoScaling();
  }

  /**
   * Gets the autoscaling configuration with fallback logic.
   *
   * @return if {@link NodeTypeFamily#ANALYTICS} - returns analytics autoscaling if present;
   *     otherwise falls back to base. TODO CLOUDP-132328 - this needs to respect validation logic
   *     and only fall back to base if it is compatible with the analytics spec If {@link
   *     NodeTypeFamily#BASE} - returns base autoscaling if present
   */
  public Optional<AutoScaling> getAutoScalingForProvider(
      final CloudProvider pCloudProvider, final NodeTypeFamily pNodeTypeFamily) {
    return getAutoScalingForProvider(pCloudProvider, pNodeTypeFamily, false);
  }

  private Optional<AutoScaling> getAutoScalingForProvider(
      final CloudProvider pCloudProvider,
      final NodeTypeFamily pNodeTypeFamily,
      final boolean pSkipFallback) {

    if (pNodeTypeFamily == NodeTypeFamily.BASE) {
      return getBaseAutoScalingForProvider(pCloudProvider);
    } else if (pNodeTypeFamily == NodeTypeFamily.ANALYTICS) {
      return pSkipFallback
          ? getAnalyticsAutoScalingForProvider(pCloudProvider)
          : getAnalyticsAutoScalingForProviderWithBaseFallback(pCloudProvider);
    } else {
      throw new IllegalArgumentException(
          String.format("Invalid node type family %s", pNodeTypeFamily));
    }
  }

  public Optional<AutoScaling> getBaseAutoScalingForProvider(final CloudProvider pCloudProvider) {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
        .filter(regionConfig -> regionConfig.getCloudProvider().equals(pCloudProvider))
        .map(RegionConfig::getBaseAutoScaling)
        .filter(Objects::nonNull)
        .findFirst();
  }

  public Optional<AutoScaling> getAnalyticsAutoScalingForProviderWithBaseFallback(
      final CloudProvider pCloudProvider) {
    final Optional<AutoScaling> analyticsScaling =
        getAnalyticsAutoScalingForProvider(pCloudProvider);
    if (analyticsScaling.isPresent()) {
      return analyticsScaling;
    }
    return getBaseAutoScalingForProvider(pCloudProvider);
  }

  private Optional<AutoScaling> getAnalyticsAutoScalingForProvider(
      final CloudProvider pCloudProvider) {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
        .filter(regionConfig -> regionConfig.getCloudProvider().equals(pCloudProvider))
        .map(RegionConfig::getAnalyticsAutoScaling)
        .filter(Objects::nonNull)
        .filter(AutoScaling::isNotEmpty)
        .findFirst();
  }

  public boolean isExtendMaxDiskSizeAllowed(
      final ExtendedDiskRestrictions pExtendedDiskRestriction) {
    // extended disk size is only allowed for this cluster if it's single-region
    // overloaded function to handle three states:
    // 1) DISALLOWED 2) SINGLE_REGION extend only 3) MULTI_REGION extend
    return (pExtendedDiskRestriction == ExtendedDiskRestrictions.SINGLE_REGION_ONLY
            && !isMultiRegion())
        || pExtendedDiskRestriction == ExtendedDiskRestrictions.ALLOWED;
  }

  public boolean isExceededMaxInstanceStorage(
      final ExtendedDiskRestrictions pExtendedDiskRestrictions) {
    return getDiskSizeGB() > getMaxAllowedDiskSizeGB(NodeType.ELECTABLE, pExtendedDiskRestrictions);
  }

  public int getMaxAllowedDiskSizeGB(
      final NodeType pNodeType, final ExtendedDiskRestrictions pExtendedDiskRestriction) {
    // Find the lowest ceiling across all hardware. Evaluate the hardwareSpec, not the InstanceSize
    // directly
    return getAllRegionConfigs().stream()
        .map(rc -> rc.getHardwareSpecByNodeType(pNodeType))
        .mapToInt(
            hs -> hs.getMaxAllowedDiskSizeGB(isExtendMaxDiskSizeAllowed(pExtendedDiskRestriction)))
        .min()
        .orElseThrow();
  }

  public double getMaxStorageSizeGBForCluster(
      final ExtendedDiskRestrictions pExtendedDiskRestrictions) {
    if (isFlexOrSharedTenantCluster()) {
      return this.getDiskSizeGB();
    }

    final ReplicationSpec minInstanceSizeShardSpec =
        getMinShardByInstanceSizeForNodeTypeFamily(NodeTypeFamily.BASE).orElseThrow();

    final HardwareSpec pHardwareSpec = minInstanceSizeShardSpec.getElectableSpec();
    final int maxAllowedDiskSizeGB =
        pHardwareSpec.getMaxAllowedDiskSizeGB(
            isExtendMaxDiskSizeAllowed(pExtendedDiskRestrictions));
    final boolean exceededMaxInstanceStorage = getDiskSizeGB() > maxAllowedDiskSizeGB;
    return exceededMaxInstanceStorage
        ? pHardwareSpec.getMaxCloudProviderDiskSizeGB()
        : maxAllowedDiskSizeGB;
  }

  public BiConnector getBiConnector() {
    return _biConnector;
  }

  public Optional<String> getSRVAddress() {
    return Optional.ofNullable(_srvAddress);
  }

  public Set<ClusterTag> getClusterTags() {
    return _clusterTags;
  }

  public GeoSharding getGeoSharding() {
    return _geoSharding;
  }

  public EncryptionAtRestProvider getEncryptionAtRestProvider() {
    return _encryptionAtRestProvider;
  }

  public boolean isEncryptionAtRestEnabled() {
    return getEncryptionAtRestProvider() != EncryptionAtRestProvider.NONE;
  }

  public Optional<EmployeeAccessGrant> getEmployeeAccessGrant() {
    return Optional.ofNullable(_employeeAccessGrant);
  }

  public Optional<EmployeeAccessGrant> getActiveEmployeeAccessGrant() {
    return getEmployeeAccessGrant().filter(grant -> grant.getExpiration().after(new Date()));
  }

  public boolean hasTemporaryAccessGrantedForGrantType(final EmployeeAccessGrantType pGrantType) {
    return getActiveEmployeeAccessGrant()
        .map(grant -> grant.providesAccessToRequiredGrantType(pGrantType))
        .orElse(false);
  }

  public Optional<Date> getExpirationDateForGrantType(final EmployeeAccessGrantType pGrantType) {
    if (!hasTemporaryAccessGrantedForGrantType(pGrantType)) {
      return Optional.empty();
    }
    return getActiveEmployeeAccessGrant().map(EmployeeAccessGrant::getExpiration);
  }

  public OsTunedFileOverrides getOsTunedFileOverrides() {
    return _osTunedFileOverrides;
  }

  public LogRetention getLogRetention() {
    return _logRetention;
  }

  public Optional<Date> getResurrectRequested() {
    return Optional.ofNullable(_resurrectRequested);
  }

  public Optional<ResurrectOptions> getResurrectOptions() {
    return Optional.ofNullable(_resurrectOptions);
  }

  public boolean isAccessTemporarilyRevoked() {
    return _accessTemporarilyRevoked;
  }

  // Tenant Provider Options (begin)

  public String getTenantRegionDisplay() {
    return String.format(
        "%s: %s (%s)",
        getBackingProvider().toString(), getRegionName().getValue(), getRegionName().getLocation());
  }

  public CloudProvider getBackingProvider() {
    if (isTenantCluster()) {
      return getRegionName().getProvider();
    } else {
      throw new UnsupportedOperationException("Cannot get backing provider for non-tenant cluster");
    }
  }

  public RegionName getRegionName() {
    if (isTenantCluster()) {
      return getFirstShardRegionConfig_DEPRECATED().getRegionName();
    }
    throw new UnsupportedOperationException("Cannot get region name for non-tenant cluster");
  }

  public RegionName getRegionName(
      final InstanceHardware pInstanceHardware,
      final Optional<CloudProviderContainer> pCloudProviderContainer) {
    RegionName regionName;
    switch (pInstanceHardware.getCloudProvider()) {
      case FREE:
      case SERVERLESS:
      case FLEX:
        regionName = getRegionName();
        break;
      case AWS:
      case AZURE:
      case GCP:
        regionName = pInstanceHardware.getRegion(pCloudProviderContainer);
        break;
      default:
        throw new UnsupportedOperationException();
    }
    return regionName;
  }

  public List<RegionConfig> getAllRegionConfigs() {
    return getReplicationSpecsWithShardData().stream()
        .flatMap(rs -> rs.getRegionConfigs().stream())
        .collect(Collectors.toList());
  }

  /**
   * DEPRECATED - callers should instead iterate through all replication specs and region configs to
   * get the required RegionConfig/ShardRegionConfig. This method is not compatible with independent
   * shard scaling (CLOUDP-167874)
   */
  @Deprecated
  private RegionConfig getFirstShardRegionConfig_DEPRECATED() {
    // CLOUDP-167874: Independent Shard Scaling
    // https://wiki.corp.mongodb.com/display/MMS/Independent+Shard+Scaling
    return getReplicationSpecsWithShardData().get(0).getRegionConfigs().stream()
        .filter(regionConfig -> regionConfig instanceof ShardRegionConfig)
        .findFirst()
        .get();
  }

  public Date getNextTenantBackupDate() {
    if (isSharedTenantCluster()) {
      return getFreeTenantProviderOptions().getNextBackupDate();
    } else if (isFlexTenantCluster()) {
      return getFlexTenantProviderOptions().getNextBackupDate();
    } else if (isServerlessTenantCluster()) {
      return getServerlessTenantProviderOptions().getNextBackupDate();
    } else {
      throw new UnsupportedOperationException(
          "Cannot get next tenant backup date for non-tenant cluster");
    }
  }

  public boolean isTenantBackupEnabled() {
    if (isSharedTenantCluster()) {
      return getFreeTenantProviderOptions().isTenantBackupEnabled();
    } else if (isFlexTenantCluster()) {
      return getFlexTenantProviderOptions().isTenantBackupEnabled();
    } else if (isServerlessTenantCluster()) {
      return getServerlessTenantProviderOptions().isTenantBackupEnabled();
    } else {
      throw new UnsupportedOperationException(
          "Cannot get tenant backup enabled for non-tenant cluster");
    }
  }

  public ClusterDescriptionProviderOptions getFreeTenantProviderOptions() {
    if (!isSharedTenantCluster()) {
      throw new IllegalStateException(
          "called getFreeTenantProviderOptions on cluster with provider(s) "
              + getCloudProviders().stream().map(Enum::name).collect(Collectors.joining(", ")));
    }
    return _freeTenantProviderOptions.orElseThrow();
  }

  public ClusterDescriptionProviderOptions getServerlessTenantProviderOptions() {
    if (!isServerlessTenantCluster()) {
      throw new IllegalStateException(
          "called getServerlessTenantProviderOptions on cluster with provider(s) "
              + getCloudProviders().stream().map(Enum::name).collect(Collectors.joining(", ")));
    }
    return _serverlessTenantProviderOptions.orElseThrow();
  }

  public ClusterDescriptionBackupOptions getServerlessBackupOptions() {
    if (!isServerlessTenantCluster()) {
      throw new IllegalStateException(
          "called getServerlessBackupOptions on a cluster that is not serverless"
              + getCloudProviders().stream().map(Enum::name).collect(Collectors.joining(", ")));
    }
    return _serverlessBackupOptions.orElseThrow();
  }

  public Optional<ClusterDescriptionBackupOptions> getServerlessBackupOptionsOpt() {
    return _serverlessBackupOptions;
  }

  public ClusterDescriptionProviderOptions getFlexTenantProviderOptions() {
    if (!isFlexTenantCluster()) {
      throw new IllegalStateException(
          "called getFlexTenantProviderOptions on cluster with provider(s) "
              + getCloudProviders().stream().map(Enum::name).collect(Collectors.joining(", ")));
    }
    return _flexTenantProviderOptions.orElseThrow();
  }

  public boolean needsPrivateDNS() {
    return getCloudProviders().stream().anyMatch(CloudProvider::needsPrivateDNS)
        || (isTenantCluster() && getBackingProvider().needsPrivateDNS());
  }

  // Tenant Provider Options (end)

  public ClusterDescription getClusterDescriptionWithPartialReplicationSpecUpdate(
      final InstanceSize pInstanceSize, final NodeTypeFamily pNodeTypeFamily) {

    final List<ReplicationSpec> updatedReplicationSpecList;
    final Optional<AutoScaling> autoScaling =
        getAutoScalingForProvider(pInstanceSize.getCloudProvider(), pNodeTypeFamily, true);
    final AutoScaling newAutoScaling;
    if (autoScaling.isPresent()
        && shouldDisableAutoIndexing(pInstanceSize.name(), autoScaling.get().getAutoIndexing())) {
      newAutoScaling =
          autoScaling.get().toBuilder()
              .autoIndexing(AutoIndexing.getDisabledAutoIndexing())
              .build();
    } else {
      newAutoScaling = null;
    }

    // Updating the instance size for all since this path is only used for the symmetric cluster
    updatedReplicationSpecList =
        getReplicationSpecsWithShardData().stream()
            .map(
                replicationSpec ->
                    replicationSpec.updateExistingInstanceSizeAndAutoScaling(
                        pInstanceSize,
                        newAutoScaling,
                        pInstanceSize.getCloudProvider(),
                        pNodeTypeFamily))
            .collect(Collectors.toList());

    return copy().setReplicationSpecList(updatedReplicationSpecList).build();
  }

  private ClusterDescription getClusterDescriptionWithPartialReplicationSpecUpdate(
      final Map<ObjectId, NDSInstanceSize> pReplicationSpecToInstanceSizeChange,
      final NodeTypeFamily pNodeTypeFamily) {

    final CloudProvider cloudProvider =
        pReplicationSpecToInstanceSizeChange.values().stream()
            .findFirst()
            .orElseThrow()
            .getCloudProvider();

    final Optional<AutoScaling> autoScaling =
        getAutoScalingForProvider(cloudProvider, pNodeTypeFamily, true);
    final AutoScaling newAutoScaling;
    if (autoScaling.isPresent()
        && pReplicationSpecToInstanceSizeChange.values().stream()
            .anyMatch(
                instanceSize ->
                    shouldDisableAutoIndexing(
                        instanceSize.name(), autoScaling.get().getAutoIndexing()))) {
      newAutoScaling =
          autoScaling.get().toBuilder()
              .autoIndexing(AutoIndexing.getDisabledAutoIndexing())
              .build();
    } else {
      newAutoScaling = null;
    }

    final List<ReplicationSpec> updatedReplicationSpecList =
        getReplicationSpecsWithShardData().stream()
            .map(
                replicationSpec -> {
                  if (pReplicationSpecToInstanceSizeChange.containsKey(replicationSpec.getId())) {
                    final NDSInstanceSize instanceSize =
                        pReplicationSpecToInstanceSizeChange.get(replicationSpec.getId());
                    return replicationSpec.updateExistingInstanceSizeAndAutoScaling(
                        instanceSize, newAutoScaling, cloudProvider, pNodeTypeFamily);
                  } else {
                    return replicationSpec.updateExistingAutoScalingForCloudProvider(
                        newAutoScaling, cloudProvider);
                  }
                })
            .collect(Collectors.toList());

    return copy().setReplicationSpecList(updatedReplicationSpecList).build();
  }

  public static BasicDBObject getId(final ObjectId pGroupId, final String pClusterName) {
    return new BasicDBObject()
        .append(FieldDefs.NAME, pClusterName)
        .append(FieldDefs.GROUP_ID, pGroupId);
  }

  public String getClusterNamePrefix() {
    return _clusterNamePrefix.toLowerCase();
  }

  public String getDnsPin() {
    return _dnsPin;
  }

  public ClusterProvisionType getClusterProvisionType() {
    return _clusterProvisionType == null ? ClusterProvisionType.REGULAR : _clusterProvisionType;
  }

  public Date getNeedsServerlessSnapshotForPit() {
    return _needsServerlessSnapshotForPit;
  }

  public boolean isFastProvisioned() {
    return _clusterProvisionType == ClusterProvisionType.FAST;
  }

  public boolean isTerminationProtectionEnabled() {
    return _terminationProtectionEnabled;
  }

  public Date getNeedsSampleDataLoadAfter() {
    return _needsSampleDataLoadAfter;
  }

  public Boolean getCreateSampleSearchIndex() {
    return _createSampleSearchIndex;
  }

  public Optional<Date> getNeedsEnvoySyncAfter() {
    return Optional.ofNullable(_needsEnvoySyncAfter);
  }

  public Optional<SampleDataset> getSampleDatasetToLoad() {
    return Optional.of(_sampleDatasetToLoad);
  }

  public Date getLastDataValidationDate() {
    return _lastDataValidationDate;
  }

  public Date getLastDbCheckDate() {
    return _lastDbCheckDate;
  }

  public Date getNeedsDbCheckAfter() {
    return _needsDbCheckAfter;
  }

  public int getDbCheckPreflightRetryCount() {
    return _dbCheckPreflightRetryCount;
  }

  public Optional<DiskWarmingMode> getDiskWarmingMode() {
    return _diskWarmingMode;
  }

  // this is only used for reading the value during rollout - since diskWarmingMode can be empty,
  // fully_warmed or visible_earlier
  public DiskWarmingMode getDiskWarmingModeOrDefault() {
    return _diskWarmingMode.orElse(DiskWarmingMode.VISIBLE_EARLIER);
  }

  public Optional<ReplicaSetScalingStrategy> getReplicaSetScalingStrategy() {
    return Optional.ofNullable(_replicaSetScalingStrategy);
  }

  public ClusterConnectionStringConfiguration getClusterConnectionStringConfiguration() {
    return _clusterConnectionStringConfiguration;
  }

  public Optional<MongotuneStatus> getMongotuneStatus() {
    return Optional.ofNullable(_mongotuneStatus);
  }

  /**
   * Returns the date after which mongotune configuration should be published.
   *
   * @return an Optional containing the date if it is set, otherwise an empty Optional.
   */
  public Optional<Date> getNeedsMongotuneConfigPublishAfter() {
    return Optional.ofNullable(_needsMongotuneConfigPublishAfter);
  }

  public boolean isConfiguredForPrivateEndpointLegacyConnectionStrings(
      final boolean pClusterSupportsLoadBalancedStrings) {
    if (this.getClusterType().isReplicaSet()) {
      return true;
    }
    return switch (getClusterConnectionStringConfiguration().legacyConnectionStringStatus()) {
      case ENABLED -> true;
      case DISABLED -> false;
      case DEFAULT ->
          !pClusterSupportsLoadBalancedStrings
              || ((ShardedClusterDescription) this).hasPrivateEndpointLegacyConnectionStrings();
    };
  }

  public boolean isConfiguredForPrivateEndpointRegionalizedConnectionStrings(
      final NDSGroup pGroup) {
    if (this.getClusterType().isReplicaSet()) {
      return false;
    }
    return switch (getClusterConnectionStringConfiguration().regionalizedConnectionStringStatus()) {
      case ENABLED -> true;
      case DISABLED -> false;
      case DEFAULT -> pGroup.isRegionalizedPrivateLinkEnabled();
    };
  }

  public boolean isConfiguredForPrivateEndpointNonRegionalizedConnectionStrings(
      final NDSGroup pGroup) {
    if (this.getClusterType().isReplicaSet()) {
      return true;
    }
    return switch (getClusterConnectionStringConfiguration()
        .nonRegionalizedConnectionStringStatus()) {
      case ENABLED -> true;
      case DISABLED -> false;
      case DEFAULT -> !pGroup.isRegionalizedPrivateLinkEnabled();
    };
  }

  public boolean isConfiguredForLoadBalancedConnectionStrings() {
    if (this.getClusterType().isReplicaSet()) {
      return false;
    }
    return switch (getClusterConnectionStringConfiguration().loadBalancedConnectionStringStatus()) {
      case ENABLED, DEFAULT -> true;
      case DISABLED -> false;
    };
  }

  public boolean hasSingleTargetPrivateEndpointStrings() {
    return !getPrivateSrvAddressMap().isEmpty() || !getPrivateMongoDBUriHostsMap().isEmpty();
  }

  public List<BumperFileOverride> getBumperFileOverrides() {
    return _bumperFileOverrides;
  }

  public int getGoalNumberBumperFiles(final String pAgentHostname) {
    return this.getBumperFileOverrides().stream()
        .filter(override -> override.hostname().equals(pAgentHostname))
        .findFirst()
        .map(BumperFileOverride::numberOfBumperFiles)
        .orElse(INDSDefaults.GOAL_NUMBER_INFLATED_BUMPER_FILES);
  }

  public AutoScalingMode getAutoScalingMode() {
    return _autoScalingMode != null ? _autoScalingMode : AutoScalingMode.CLUSTER;
  }

  public boolean getGtsRollout() {
    return _gtsRollout;
  }

  public boolean isFreeFromServerless() {
    return _freeFromServerless;
  }

  /**
   * @return whether the replicationSpecList could be merged
   */
  public boolean hasMergeableReplicationSpecs() {
    return getReplicationSpecsWithShardData().stream()
        .collect(Collectors.groupingBy(ReplicationSpec::getZoneId))
        .values()
        .stream()
        .anyMatch(specs -> specs.size() > 1);
  }

  public enum DiskAutoScaleResult {
    // Indicates that we can't further expand the disk because we have reached a hard limit for the
    // cluster provider
    AT_MAX_DISK_SIZE_FOR_PROVIDER,

    // Indicates that we can't further expand the disk because we have reached a hard limit for
    // the cluster-tier
    AT_MAX_DISK_SIZE_FOR_INSTANCE_SIZE,

    // Indicates the instance size was increased as a result of disk auto-scaling
    INSTANCE_SIZE_INCREASED,

    // Indicates the disk size was increased as a result of disk auto-scaling
    DISK_SIZE_INCREASED,

    // No change required
    DISK_CHANGE_UNNECESSARY;

    private static final Set<DiskAutoScaleResult> POSITIVE_DISK_AUTO_SCALING_RESULTS =
        Set.of(
            DiskAutoScaleResult.DISK_SIZE_INCREASED, DiskAutoScaleResult.INSTANCE_SIZE_INCREASED);

    private static final Set<DiskAutoScaleResult> NEGATIVE_DISK_AUTO_SCALING_RESULTS =
        Set.of(
            DiskAutoScaleResult.AT_MAX_DISK_SIZE_FOR_PROVIDER,
            DiskAutoScaleResult.AT_MAX_DISK_SIZE_FOR_INSTANCE_SIZE);

    public boolean isPositiveScalingResult() {
      return POSITIVE_DISK_AUTO_SCALING_RESULTS.contains(this);
    }

    public boolean isNegativeScalingResult() {
      return NEGATIVE_DISK_AUTO_SCALING_RESULTS.contains(this);
    }
  }

  private Optional<ClusterDescription> performAutoScaleForNVMe(
      final ExtendedDiskRestrictions pExtendedDiskRestrictions) {
    final boolean extendMaxAllowedDiskSize = isExtendMaxDiskSizeAllowed(pExtendedDiskRestrictions);
    final boolean hasAutoScaleUpdate =
        getReplicationSpecsWithShardData().stream()
            .map(
                crossCloudReplicationSpec ->
                    crossCloudReplicationSpec.performAutoScaleForNVMe(extendMaxAllowedDiskSize))
            .allMatch(Optional::isPresent);

    if (!hasAutoScaleUpdate) {
      return Optional.empty();
    }

    final List<ReplicationSpec> updatedReplicationSpecList =
        getReplicationSpecsWithShardData().stream()
            .map(
                crossCloudReplicationSpec ->
                    crossCloudReplicationSpec.performAutoScaleForNVMe(extendMaxAllowedDiskSize))
            .map(Optional::get)
            .collect(Collectors.toList());

    final InstanceSize updatedInstanceSize =
        updatedReplicationSpecList
            .get(0)
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .getInstanceSize();

    return Optional.of(
        copy()
            .setReplicationSpecList(updatedReplicationSpecList)
            .setDiskSizeGB(updatedInstanceSize.getDefaultDiskSizeGB())
            .build());
  }

  protected List<ReplicationSpec> updateReplicationSpecListAutoScaling(
      final Map<ReplicationSpec, DiskAutoScaleChange> pDiskChanges,
      final List<ReplicationSpec> pUpdatedReplicationSpecList) {
    final Optional<AutoScaling> autoScaling =
        Optional.ofNullable(getAutoScaling(NodeTypeFamily.BASE));
    if (autoScaling.isPresent()
        && pDiskChanges.values().stream()
            .anyMatch(
                change ->
                    shouldDisableAutoIndexing(
                        change.getAutoScaleInstanceSize().getName(),
                        autoScaling.get().getAutoIndexing()))) {
      return pUpdatedReplicationSpecList.stream()
          .map(
              replicationSpec -> {
                ReplicationSpec newReplicationSpec = replicationSpec.copy().build();

                for (final CloudProvider cloudProvider : getCloudProviders()) {
                  final Optional<AutoScaling> existingAutoScalingOpt =
                      getAutoScalingForProvider(cloudProvider, NodeTypeFamily.BASE);
                  if (existingAutoScalingOpt.isEmpty()) {
                    continue;
                  }
                  final AutoScaling existingAutoScaling = existingAutoScalingOpt.get();

                  final AutoScaling newAutoScaling =
                      existingAutoScaling.toBuilder()
                          .autoIndexing(AutoIndexing.getDisabledAutoIndexing())
                          .build();

                  newReplicationSpec =
                      newReplicationSpec.updateExistingAutoScalingForCloudProvider(
                          newAutoScaling, cloudProvider);
                }

                return newReplicationSpec;
              })
          .collect(Collectors.toList());
    }
    return pUpdatedReplicationSpecList;
  }

  public double getDiskSizeForDiskAutoScaling(
      final double pCurrentFractionDiskUsed, final double pDesiredFractionDiskUsed) {
    final double targetDiskSize =
        Math.ceil((pCurrentFractionDiskUsed / pDesiredFractionDiskUsed) * getDiskSizeGB());

    if (Double.compare(targetDiskSize, 0) <= 0) {
      throw new IllegalStateException(
          String.format(
              "Invalid target disk size - diskUsage: %.2f, desiredDiskUsage: %.2f, diskSizeGB:"
                  + " %.2f",
              pCurrentFractionDiskUsed, pDesiredFractionDiskUsed, getDiskSizeGB()));
    }

    if (!getCloudProviders().contains(CloudProvider.AZURE)) {
      return targetDiskSize;
    }

    final boolean isOnAzureSsdV2 =
        getAllShardAndConfigHardwareSpecsForProvider(CloudProvider.AZURE).stream()
            .filter(h -> h.getNodeCount() > 0)
            .allMatch(HardwareSpec::isOnAzureSsdV2);

    if (isOnAzureSsdV2) {
      return targetDiskSize;
    }

    return Arrays.stream(
            CloudProviderRegistry.getByCloudProvider(CloudProvider.AZURE).getDiskSizeOptions())
        .filter(diskSizeGB -> diskSizeGB > targetDiskSize)
        .min()
        .stream()
        .mapToObj(i -> (double) i)
        .findFirst()
        .or(
            () ->
                getMinInstanceSizeForProvider(NodeType.ELECTABLE, CloudProvider.AZURE)
                    .map(
                        ndsInstanceSize ->
                            (double) ndsInstanceSize.getMaxCloudProviderDiskSizeGB()))
        .orElse(targetDiskSize);
  }

  public Pair<Optional<ClusterDescription>, DiskAutoScaleResult>
      performDiskAutoScaleWithInstanceSizeUpdate(
          final double pCurrentFractionDiskUsed,
          final double pDesiredFractionDiskUsed,
          @Nullable final AutoScaleInstanceSize pMaxInstanceSize,
          final ExtendedDiskRestrictions pExtendedDiskRestrictions,
          final NodeType pNodeType) {
    final boolean extendMaxAllowedDiskSize = isExtendMaxDiskSizeAllowed(pExtendedDiskRestrictions);
    if (isTenantCluster()) {
      throw new UnsupportedOperationException();
    }

    if (isNVMe(pNodeType)) {
      return Pair.of(
          performAutoScaleForNVMe(pExtendedDiskRestrictions),
          DiskAutoScaleResult.INSTANCE_SIZE_INCREASED);
    }

    final double targetDiskSize =
        getDiskSizeForDiskAutoScaling(pCurrentFractionDiskUsed, pDesiredFractionDiskUsed);

    final Map<ReplicationSpec, DiskAutoScaleChange> changes = new HashMap<>();
    final List<ReplicationSpec> replicationSpecs = getReplicationSpecsWithShardData();
    for (final ReplicationSpec replicationSpec : replicationSpecs) {
      /*
       * The logic to update replication specs will only scale up. As such, we find the smaller of
       * the two unique hardware specs to determine if we need to enforce a new minimum floor
       * instance size across the replication spec for disk size compatability.
       */
      final AutoScaleInstanceSize baseAutoScaleSize =
          getAutoScaleInstanceSizeFromReplicationSpecId(
                  replicationSpec.getId(), NodeTypeFamily.BASE)
              .orElseThrow();
      final AutoScaleInstanceSize analyticsAutoScaleSize =
          getAutoScaleInstanceSizeFromReplicationSpecId(
                  replicationSpec.getId(), NodeTypeFamily.ANALYTICS)
              .orElseThrow();
      final AutoScaleInstanceSize autoScaleInstanceSize =
          analyticsAutoScaleSize.isLessThanOrEqualTo(baseAutoScaleSize)
              ? analyticsAutoScaleSize
              : baseAutoScaleSize;

      // if we deprecate instance sizes for analytics nodes going forward this logic may need to
      // change - currently checking base only to defer updating unintentionally asymmetric tests
      final boolean isLegacyDiskSize =
          getDiskSizeGB()
              > baseAutoScaleSize.getMaxAllowedDiskSizeGB(
                  getCloudProviders(), extendMaxAllowedDiskSize, isAnyDiskAzurePv2());
      final DiskAutoScaleChange change;
      if (isLegacyDiskSize) {
        LOG.warn(
            "Cluster description has diskSizeGB > max for instance size, and likely existed before"
                + " we lowered storage maximums for its tier. Do not scale the instance tier, and"
                + " allow disk size GB to increase to maximum for cloud provider. clusterName={} in"
                + " groupId={} with instanceSizes={} and diskSizeGB={}",
            getName(),
            getGroupId(),
            getInstanceSizes(pNodeType),
            getDiskSizeGB());
        change =
            getLegacyDiskAutoScaleUpdate(getDiskSizeGB(), targetDiskSize, autoScaleInstanceSize);
      } else {
        change =
            getDiskAutoScaleAndInstanceSizeUpdate(
                getDiskSizeGB(),
                targetDiskSize,
                autoScaleInstanceSize,
                pMaxInstanceSize,
                pExtendedDiskRestrictions);
      }

      changes.put(replicationSpec, change);
    }

    final boolean cantChange =
        changes.values().stream().anyMatch(change -> change.getResult().isNegativeScalingResult());

    if (cantChange) {
      final DiskAutoScaleResult reasonForFailure =
          changes.values().stream()
              .filter(change -> change.getResult().isNegativeScalingResult())
              .findFirst()
              .map(DiskAutoScaleChange::getResult)
              .orElseThrow();

      LOG.atLevel(Level.INFO)
          .addKeyValue("clusterName", getName())
          .addKeyValue("groupId", getGroupId())
          .log("Cluster was unable to perform disk autoscaling due to {}", reasonForFailure);
      return Pair.of(
          Optional.empty(), reasonForFailure); // Guaranteed to exist since we matched it above
    }

    LOG.atLevel(Level.INFO)
        .addKeyValue("clusterName", getName())
        .addKeyValue("groupId", getGroupId())
        .log(
            "Cluster is performing disk autoscaling with the following changes {}",
            changes.entrySet().stream()
                .collect(Collectors.toMap(entry -> entry.getKey().getId(), Entry::getValue)));

    final List<ReplicationSpec> updatedReplicationSpecList =
        getReplicationSpecsWithShardData().stream()
            .map(
                replicationSpec ->
                    replicationSpec.performDiskAutoScaleWithInstanceSizeUpdate(
                        changes.get(replicationSpec)))
            .collect(Collectors.toList());

    final List<ReplicationSpec> updatedReplicationSpecListWithAutoScaling =
        updateReplicationSpecListAutoScaling(changes, updatedReplicationSpecList);

    final ClusterDescription updatedClusterDescription =
        copy()
            .setReplicationSpecList(updatedReplicationSpecListWithAutoScaling)
            .setDiskSizeGB(changes.values().stream().findFirst().orElseThrow().getDiskSizeGB())
            .build();

    return Pair.of(
        Optional.of(updatedClusterDescription),
        changes.values().stream().findFirst().orElseThrow().getResult());
  }

  public Optional<AutoScaleInstanceSize> getAutoScaleInstanceSizeFromReplicationSpecId(
      final ObjectId pReplicationSpecId, final NodeTypeFamily pNodeTypeFamily) {
    final Optional<ReplicationSpec> replicationSpec = getReplicationSpecById(pReplicationSpecId);
    final AutoScaleInstanceSizeBuilder builder =
        AutoScaleInstanceSizeBuilderFactory.getAutoScaleInstanceSizeBuilder(getCloudProviders());

    return replicationSpec.map(
        rs ->
            switch (pNodeTypeFamily) {
              case BASE -> builder.getAutoScaleInstanceSize(rs.getElectableInstanceSize());
              case ANALYTICS -> builder.getAutoScaleInstanceSize(rs.getAnalyticsInstanceSize());
            });
  }

  public Optional<ReplicationSpec> getMinShardByInstanceSizeForNodeTypeFamily(
      final NodeTypeFamily pNodeTypeFamily) {
    return getReplicationSpecsWithShardData().stream()
        .min(
            Comparator.comparing(
                rs ->
                    InstanceSizeTier.getNonDeprecatedInstanceSizeNormalizedIndex(
                            rs.getInstanceSizeByNodeTypeFamily(pNodeTypeFamily).name())
                        .orElseThrow(
                            () ->
                                new IllegalStateException(
                                    String.format(
                                        "Unable to parse instance tier index for instance tier"
                                            + " %s",
                                        rs.getInstanceSizeByNodeTypeFamily(pNodeTypeFamily)
                                            .name())))));
  }

  public Optional<NDSInstanceSize> getMinShardInstanceSizeAcrossNodeTypeFamily(
      final NodeTypeFamily pNodeTypeFamily) {
    return switch (pNodeTypeFamily) {
      case ANALYTICS ->
          getMinShardByInstanceSizeForNodeTypeFamily(NodeTypeFamily.ANALYTICS)
              .map(ReplicationSpec::getAnalyticsInstanceSize);
      case BASE ->
          getMinShardByInstanceSizeForNodeTypeFamily(NodeTypeFamily.BASE)
              .map(ReplicationSpec::getElectableInstanceSize);
    };
  }

  private boolean shouldDisableAutoIndexing(
      final String pInstanceSizeName, final AutoIndexing pAutoIndexing) {
    if (pInstanceSizeName == null || pAutoIndexing == null) {
      LOG.warn(
          "Cannot check if auto-indexing should be disabled for clusterName={} in groupId={} with"
              + " instanceSize={} and autoIndexing={}",
          getName(),
          getGroupId(),
          pInstanceSizeName,
          pAutoIndexing);
      return false;
    }
    final boolean autoIndexingEnabled = pAutoIndexing.isEnabled();
    final boolean aboveMaxAutoIndexingInstanceSize =
        InstanceSize.compareByNames(pInstanceSizeName, MAX_AUTO_INDEXING_INSTANCE_SIZE) > 0;
    final boolean belowMinAutoIndexingInstanceSize =
        InstanceSize.compareByNames(pInstanceSizeName, MIN_AUTO_INDEXING_INSTANCE_SIZE) < 0;
    return (autoIndexingEnabled
        && (aboveMaxAutoIndexingInstanceSize || belowMinAutoIndexingInstanceSize));
  }

  private DiskAutoScaleChange getLegacyDiskAutoScaleUpdate(
      final double pDiskSizeGB,
      final double pTargetDiskSizeGB,
      final AutoScaleInstanceSize pCurrentInstanceSize) {

    final double maxDiskSizeGBForProvider =
        pCurrentInstanceSize.getMaxCloudProviderDiskSizeGB(
            getCloudProviders(), isAnyDiskAzurePv2());
    if (pDiskSizeGB >= maxDiskSizeGBForProvider) {
      return DiskAutoScaleChange.builder()
          .diskSizeGB(maxDiskSizeGBForProvider)
          .instanceSize(pCurrentInstanceSize)
          .result(DiskAutoScaleResult.AT_MAX_DISK_SIZE_FOR_PROVIDER)
          .build();
    }

    if (pDiskSizeGB >= pTargetDiskSizeGB) {
      return DiskAutoScaleChange.builder()
          .diskSizeGB(pTargetDiskSizeGB)
          .instanceSize(pCurrentInstanceSize)
          .result(DiskAutoScaleResult.DISK_CHANGE_UNNECESSARY)
          .build();
    }

    return DiskAutoScaleChange.builder()
        .diskSizeGB(pTargetDiskSizeGB)
        .instanceSize(pCurrentInstanceSize)
        .result(DiskAutoScaleResult.DISK_SIZE_INCREASED)
        .build();
  }

  private DiskAutoScaleChange getDiskAutoScaleAndInstanceSizeUpdate(
      final double pDiskSizeGB,
      final double pTargetDiskSizeGB,
      final AutoScaleInstanceSize pCurrentInstanceSize,
      @Nullable final AutoScaleInstanceSize pMaxInstanceSize,
      final ExtendedDiskRestrictions pExtendedDiskRestrictions) {
    final boolean extendMaxAllowedDiskSize = isExtendMaxDiskSizeAllowed(pExtendedDiskRestrictions);
    final boolean isAzureDiskV2 = isAnyDiskAzurePv2();

    // cannot exceed max disk size for provider
    final double maxDiskSizeGBForProvider =
        pCurrentInstanceSize.getMaxCloudProviderDiskSizeGB(getCloudProviders(), isAzureDiskV2);
    if (pDiskSizeGB >= maxDiskSizeGBForProvider) {
      return DiskAutoScaleChange.builder()
          .diskSizeGB(maxDiskSizeGBForProvider)
          .instanceSize(pCurrentInstanceSize)
          .result(DiskAutoScaleResult.AT_MAX_DISK_SIZE_FOR_PROVIDER)
          .build();
    }

    // reached target disk size return
    if (pDiskSizeGB >= pTargetDiskSizeGB) {
      return DiskAutoScaleChange.builder()
          .diskSizeGB(pTargetDiskSizeGB)
          .instanceSize(pCurrentInstanceSize)
          .result(DiskAutoScaleResult.DISK_CHANGE_UNNECESSARY)
          .build();
    }

    // disk scale to max for instance size if possible
    final double maxDiskSizeGBForInstance =
        pCurrentInstanceSize.getMaxAllowedDiskSizeGB(
            getCloudProviders(), extendMaxAllowedDiskSize, isAzureDiskV2);

    if (pDiskSizeGB < maxDiskSizeGBForInstance) {
      // prioritize targetSize before next maxDiskPerInstance
      final double nextDiskSizeGB = Math.min(maxDiskSizeGBForInstance, pTargetDiskSizeGB);
      final DiskAutoScaleChange scaledByDisk =
          getDiskAutoScaleAndInstanceSizeUpdate(
              nextDiskSizeGB,
              pTargetDiskSizeGB,
              pCurrentInstanceSize,
              pMaxInstanceSize,
              pExtendedDiskRestrictions);

      // bubble up instance size increases
      return scaledByDisk.getResult().equals(DiskAutoScaleResult.INSTANCE_SIZE_INCREASED)
          ? scaledByDisk
          : scaledByDisk.toBuilder().result(DiskAutoScaleResult.DISK_SIZE_INCREASED).build();
    }

    // if you're already at the max instance size, you cannot scale by instance size
    final Optional<AutoScaleInstanceSize> nextInstanceSize =
        pCurrentInstanceSize.getNextActiveInstanceSizeWithLargerMaxDiskSizeGB(
            pMaxInstanceSize,
            getAllowedDeprecatedInstanceSizes(),
            getCloudProviders(),
            extendMaxAllowedDiskSize,
            isAzureDiskV2);
    if (nextInstanceSize.isEmpty()) {
      return DiskAutoScaleChange.builder()
          .diskSizeGB(maxDiskSizeGBForInstance)
          .instanceSize(pCurrentInstanceSize)
          .result(DiskAutoScaleResult.AT_MAX_DISK_SIZE_FOR_INSTANCE_SIZE)
          .build();
    }

    // if you're not, bump to next applicable instance size and re-attempt disk auto-scale
    final DiskAutoScaleChange scaledByInstance =
        getDiskAutoScaleAndInstanceSizeUpdate(
            maxDiskSizeGBForInstance,
            pTargetDiskSizeGB,
            nextInstanceSize.get(),
            pMaxInstanceSize,
            pExtendedDiskRestrictions);
    return scaledByInstance.toBuilder().result(DiskAutoScaleResult.INSTANCE_SIZE_INCREASED).build();
  }

  private Set<AutoScaleInstanceSize> getAllowedDeprecatedInstanceSizes() {
    final Optional<NDSInstanceSize> computeAutoScalingMaxInstanceSize =
        Optional.ofNullable(getAutoScaling(NodeTypeFamily.BASE).getCompute().getMaxInstanceSize());
    if (computeAutoScalingMaxInstanceSize.isPresent()) {
      if (isCrossCloudCluster()) {
        return Set.of(new AutoScaleCrossCloudInstanceSize(computeAutoScalingMaxInstanceSize.get()));
      }

      return Set.of(new AutoScaleSingleCloudInstanceSize(computeAutoScalingMaxInstanceSize.get()));
    }

    return Set.of();
  }

  @Deprecated
  public Optional<ClusterDescription> performDiskAutoScale(
      final double scaleFactor, final ExtendedDiskRestrictions pExtendedDiskRestrictions) {
    if (isTenantCluster()) {
      throw new UnsupportedOperationException();
    }
    // Ensure the new disk size is a whole number
    double newDiskSizeGB = Math.ceil(scaleFactor * getDiskSizeGB());

    if (newDiskSizeGB == 0) {
      return Optional.empty();
    }

    if (Double.compare(newDiskSizeGB, getMaxStorageSizeGBForCluster(pExtendedDiskRestrictions))
        == 1) {
      newDiskSizeGB = getMaxStorageSizeGBForCluster(pExtendedDiskRestrictions);
    }
    if (Double.compare(newDiskSizeGB, getDiskSizeGB()) == 0) {
      // Value did not change, return empty
      return Optional.empty();
    }

    return Optional.of(this.copy().setDiskSizeGB(newDiskSizeGB).build());
  }

  public Optional<ClusterDescription> performComputeAutoScaleForCluster(
      final AutoScaleInstanceSize pNewInstanceSize, final NodeTypeFamily pNodeTypeFamily)
      throws SvcException {
    if (isTenantCluster()) {
      throw new SvcException(NDSErrorCode.AUTO_SCALING_NOT_SUPPORTED);
    }

    if (getAutoScaleInstanceSize(pNodeTypeFamily).equals(pNewInstanceSize)) {
      return Optional.empty();
    }

    ClusterDescription updatedClusterDescription = copy().build();

    for (final CloudProvider cloudProvider : getCloudProviders()) {
      if (cloudProvider == CloudProvider.AWS) {
        final NDSInstanceSize newAWSInstanceSize =
            pNewInstanceSize.getInstanceSizeForProvider(CloudProvider.AWS);
        final Optional<NDSInstanceSize> existingAWSInstanceSize =
            getOnlyInstanceSizeForProvider(CloudProvider.AWS, pNodeTypeFamily);

        if (existingAWSInstanceSize.isPresent()
            && !newAWSInstanceSize
                .getFamilyClass()
                .equals(existingAWSInstanceSize.get().getFamilyClass())) {
          throw new IllegalArgumentException(
              String.format(
                  "Expected pNewInstanceSize to have family class %s, but received %s",
                  existingAWSInstanceSize.get().getFamilyClass(),
                  newAWSInstanceSize.getFamilyClass()));
        }
      }

      updatedClusterDescription =
          updatedClusterDescription.getClusterDescriptionWithPartialReplicationSpecUpdate(
              pNewInstanceSize.getInstanceSizeForProvider(cloudProvider), pNodeTypeFamily);
    }

    return Optional.of(updatedClusterDescription);
  }

  public ClusterDescription performComputeAutoScaleForShards(
      final Map<ObjectId, AutoScaleInstanceSize> pReplicationSpecIdToInstanceSizeChange,
      final NodeTypeFamily pNodeTypeFamily)
      throws SvcException {
    if (isTenantCluster()) {
      throw new SvcException(NDSErrorCode.AUTO_SCALING_NOT_SUPPORTED);
    }

    ClusterDescription updatedClusterDescription = copy().build();

    for (final CloudProvider cloudProvider : getCloudProviders()) {
      final Map<ObjectId, NDSInstanceSize> replicationSpecIdToCloudProviderInstanceSize =
          pReplicationSpecIdToInstanceSizeChange.entrySet().stream()
              .filter(
                  entry -> {
                    final ObjectId replicationSpecId = entry.getKey();
                    final ReplicationSpec replicationSpec =
                        getReplicationSpecById(replicationSpecId).orElseThrow();
                    return replicationSpec.getCloudProviders().contains(cloudProvider);
                  })
              .collect(
                  Collectors.toMap(
                      Map.Entry::getKey,
                      entry -> {
                        final ObjectId replicationSpecId = entry.getKey();
                        final AutoScaleInstanceSize autoScaleInstanceSize = entry.getValue();
                        if (cloudProvider == CloudProvider.AWS) {
                          final NDSInstanceSize newAWSInstanceSize =
                              autoScaleInstanceSize.getInstanceSizeForProvider(CloudProvider.AWS);
                          final NDSInstanceSize existingAWSInstanceSize =
                              getReplicationSpecById(replicationSpecId)
                                  .orElseThrow()
                                  .getInstanceSizeByProviderAndNodeType(
                                      CloudProvider.AWS,
                                      NodeTypeFamily.getRepresentativeNodeTypeFromFamily(
                                          pNodeTypeFamily));

                          if (!newAWSInstanceSize
                              .getFamilyClass()
                              .equals(existingAWSInstanceSize.getFamilyClass())) {
                            throw new IllegalArgumentException(
                                String.format(
                                    "Expected pNewInstanceSize to have family class %s, but"
                                        + " received %s",
                                    existingAWSInstanceSize.getFamilyClass(),
                                    newAWSInstanceSize.getFamilyClass()));
                          }
                        }
                        return autoScaleInstanceSize.getInstanceSizeForProvider(cloudProvider);
                      }));

      if (!replicationSpecIdToCloudProviderInstanceSize.isEmpty()) {
        updatedClusterDescription =
            updatedClusterDescription.getClusterDescriptionWithPartialReplicationSpecUpdate(
                replicationSpecIdToCloudProviderInstanceSize, pNodeTypeFamily);
      }
    }

    return updatedClusterDescription;
  }

  public enum DiskBackupState {
    STARTED,
    TERMINATING,
    DELETED,
  }

  public enum FixedVersionType {
    MONGODB(FieldDefs.FIXED_MONGODB_VERSION),
    FEATURE_COMPATIBILITY(FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION),
    CPU_ARCHITECTURE(FieldDefs.FIXED_CPU_ARCH),
    OS(FieldDefs.FIXED_OS),
    ACME_PROVIDER(FieldDefs.FIXED_ACME_PROVIDER);

    private final String _fieldName;

    FixedVersionType(final String pFieldName) {
      _fieldName = pFieldName;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  private String getCustomZoneMappingString() {
    final BasicDBObject zoneMapObject = new BasicDBObject();
    for (final String key : getGeoSharding().getCustomZoneMapping().keySet()) {
      final ObjectId id = getGeoSharding().getCustomZoneMapping().get(key);
      final ReplicationSpec replicationSpec =
          getReplicationSpecForCustomZoneMapping(id)
              .orElseThrow(MissingReplicationSpecException::new);
      zoneMapObject.append(key, replicationSpec.getZoneName());
    }

    return JsonUtils.serializeLegacy(zoneMapObject);
  }

  /**
   * Returns replication spec corresponding to the provided id that can be either zone id or
   * replication spec id. In case of zone id, returns first replication spec in that zone. This
   * method is meant to be used for getting replication specs for custom zone mappings, since
   * mapping can contain either zone id or replication spec id.
   *
   * @param pId Zone id or replication spec id.
   * @return Optional replication spec corresponding to the provided id.
   */
  public Optional<ReplicationSpec> getReplicationSpecForCustomZoneMapping(final ObjectId pId) {
    return getReplicationSpecsByZoneId(pId).stream()
        .findFirst()
        .or(() -> getReplicationSpecById(pId));
  }

  public Optional<Integer> getReplicaSetVersionOverride() {
    return Optional.ofNullable(_replicaSetVersionOverride);
  }

  public VersionUtils.Version getFixedFCVAwareMongoDBVersion() {
    return getFixedFeatureCompatibilityVersion()
        .map(FixedVersion::getVersion)
        .map(Version::fromString)
        .orElse(getMongoDBVersion());
  }

  /**
   * @return the effective FCV for the cluster as a major version string
   */
  public String getFeatureCompatibilityVersion() {
    return getFeatureCompatibilityVersion(
            getMongoDBVersion(),
            getVersionReleaseSystem(),
            getContinuousDeliveryFCV(),
            getFixedFeatureCompatibilityVersion(),
            isMongoDBVersionFixed())
        .getMajorVersionString();
  }

  /**
   * @return the effective FCV for the cluster
   */
  public static VersionUtils.Version getFeatureCompatibilityVersion(
      final VersionUtils.Version pMongodbVersion,
      final VersionReleaseSystem pVersionReleaseSystem,
      final Optional<String> pContinuousFCV,
      final Optional<FixedVersion> pFixedFCV,
      final boolean pIsMongoDBVersionFixed) {
    if (pFixedFCV.isPresent()) {
      return pFixedFCV.map(FixedVersion::getVersion).map(VersionUtils::parse).get();
    }

    if (pVersionReleaseSystem == VersionReleaseSystem.CONTINUOUS
        && pContinuousFCV.isPresent()
        && !pIsMongoDBVersionFixed) {
      return pContinuousFCV.map(VersionUtils::parse).get();
    }

    return VersionUtils.parse(
        VersionUtils.getDefaultFeatureCompatibilityVersionForMongoDBVersion(pMongodbVersion));
  }

  public List<IndexConfig> getPendingIndexes() {
    return _pendingIndexes;
  }

  public List<NDSLabel> getLabels() {
    return _labels;
  }

  public String[] getPrivateMongoDBUriHosts() {
    return _privateMongoDBUriHosts;
  }

  public String getPrivateSrvAddress() {
    return _privateSrvAddress;
  }

  public Map<String, List<String>> getPrivateMongoDBUriHostsMap() {
    return _privateMongoDBUriHostsMap;
  }

  public Map<String, String> getPrivateSrvAddressMap() {
    return _privateSrvAddressMap;
  }

  // This should only be null for cluster descriptions submitted via an update. Any cluster
  // description saved to the database will have a non-null value.
  public Optional<InstanceHostname.HostnameScheme> getHostnameSchemeForAgents() {
    return Optional.ofNullable(_hostnameSchemeForAgents);
  }

  public boolean isLegacyHostnameScheme() {
    return getHostnameSchemeForAgents().get() == InstanceHostname.HostnameScheme.LEGACY;
  }

  public InstanceHostname.SubdomainLevel getHostnameSubdomainLevel() {
    return _hostnameSubdomainLevel;
  }

  public String getDeploymentClusterName() {
    return _deploymentClusterName;
  }

  public float getInstanceSizeMaxNormalizedCPUUsage(
      final Optional<ObjectId> pReplicationSpecId, final NodeTypeFamily pNodeTypeFamily) {
    final NodeType nodeType = NodeTypeFamily.getRepresentativeNodeTypeFromFamily(pNodeTypeFamily);

    if (pReplicationSpecId.isPresent()) {
      final ReplicationSpec replicationSpec =
          this.getReplicationSpecById(pReplicationSpecId.get())
              .orElseThrow(
                  () ->
                      new IllegalStateException(
                          String.format(
                              "Could not get instance size max normalized cpu usage since replica "
                                  + "set corresponding to rsId %s for cluster %s in group %s does "
                                  + "not exist. This will generally happen when the cluster is "
                                  + "concurrently being deleted, and is the expected behavior "
                                  + "in that case",
                              pReplicationSpecId.get(), getName(), getGroupId())));

      return replicationSpec.getCloudProviders().stream()
          .map(provider -> replicationSpec.getInstanceSizeByProviderAndNodeType(provider, nodeType))
          .map(
              instanceSize ->
                  instanceSize.getMaxNormalizedCPUUsage(
                      replicationSpec.getInstanceFamilyByProviderAndNodeType(
                          instanceSize.getCloudProvider(), nodeType)))
          .min(Float::compare)
          .orElse(1f);
    } else if (getAutoScalingMode() == AutoScalingMode.CLUSTER) {
      // If a value is not found, assume 100% (1f)
      return getCloudProvidersForNodeType(nodeType).stream()
          .map(
              provider ->
                  this.getOnlyInstanceSizeForProvider(provider, nodeType)
                      .orElseThrow(
                          () ->
                              new IllegalStateException(
                                  "Did not find exactly 1 instance size in cluster with cluster"
                                      + " wide autoscaling")))
          .flatMap(
              instanceSize ->
                  // Can have multiple instance families being used when using mulitple regions.
                  this.getInstanceFamiliesForProvider(instanceSize.getCloudProvider(), nodeType)
                      .stream()
                      .map(instanceSize::getMaxNormalizedCPUUsage))
          .min(Float::compare)
          .orElse(1f);
    } else {
      throw new IllegalStateException(
          "Attempting to perform shard scaling but replicationSpecId not provided");
    }
  }

  public Optional<Date> getAccessToBeRevokedDateForM0Cluster() {
    // Not a free cluster or already paused
    if (!isFreeTenantCluster() || isPaused()) {
      return Optional.empty();
    }

    // Access already revoked
    final Date ndsAccessRevoked = getFreeTenantProviderOptions().getNdsAccessRevokedDate();
    if (ndsAccessRevoked != null) {
      return Optional.empty();
    }

    final Date userNotifiedAboutPauseDate =
        getFreeTenantProviderOptions().getUserNotifiedAboutPauseDate();

    // Cluster pause email not sent
    if (userNotifiedAboutPauseDate == null) {
      return Optional.empty();
    }

    return Optional.of(
        DateUtils.addDays(userNotifiedAboutPauseDate, PAUSE_IDLE_M0_CLUSTER_EMAIL_WARNING_BUFFER));
  }

  public Optional<StorageSystem> getStorageSystem() {
    return Optional.ofNullable(_storageSystem);
  }

  public boolean isDisaggregatedStorageSystem() {
    return getStorageSystem().isPresent()
        && getStorageSystem().get() == StorageSystem.DISAGGREGATED_STORAGE;
  }

  public Optional<ProxyProtocolForPrivateLinkMode> getProxyProtocolForPrivateLinkMode() {
    return Optional.ofNullable(_proxyProtocolForPrivateLinkMode);
  }

  /**
   * Determines if the cluster needs to configure disaggregated storage.
   *
   * @return {@code true} if this cluster uses disaggregated storage and any of the replication
   *     specs don't have disaggregated storage configured, {@code false} otherwise
   */
  public boolean needsToConfigureDisaggregatedStorage() {
    if (!isDisaggregatedStorageSystem()) {
      return false;
    }
    return _replicationSpecList.stream()
        .anyMatch(rs -> rs.getDisaggregatedStorageConfig().isEmpty());
  }

  /**
   * This always returns ATLAS_MANAGED for replica sets because non-sharded clusters do not have the
   * option to set this field. This method is overridden on ShardedClusterDescription
   */
  public ConfigServerManagementMode getConfigServerManagementMode() {
    return ConfigServerManagementMode.ATLAS_MANAGED;
  }

  public Optional<FlexTenantMigrationState> getFlexTenantMigrationState() {
    return Optional.ofNullable(_flexTenantMigrationState);
  }

  public List<Integer> getCpuSocketBinding() {
    return _cpuSocketBinding;
  }

  public boolean getReserveIpamIpRequested() {
    return _reserveIpamIpRequested;
  }

  /**
   * @return the last swap IP maintenance round that has completed. An empty value means that no IP
   *     swaps have been performed
   */
  public Optional<SwapIpMaintenanceRound> getSwapIpMaintenanceRoundCompleted() {
    return _swapIpMaintenanceRoundCompleted;
  }

  public boolean getAllowUnsafeRollingOperation() {
    return _allowUnsafeRollingOperation;
  }

  public Optional<PartnerIntegrationsData> getPartnerIntegrationsData() {
    return Optional.ofNullable(_partnerIntegrationsData);
  }

  /**
   * @return whether the cluster is using the AWS time-based snapshot copy for optimized initial
   *     sync across regions.
   */
  public boolean getUseAwsTimeBasedSnapshotCopyForFastInitialSync() {
    return _useAwsTimeBasedSnapshotCopyForFastInitialSync;
  }

  /**
   * Returns whether this cluster is eligible for gateway router.
   *
   * @return true if eligible, false if not eligible, empty if never evaluated
   */
  public Optional<Boolean> getGatewayRouterEligible() {
    return Optional.ofNullable(_gatewayRouterEligible);
  }

  /**
   * Returns when gateway router eligibility was last updated.
   *
   * @return the last update date, or empty if never evaluated
   */
  public Optional<Date> getGatewayRouterEligibilityLastUpdateDate() {
    return Optional.ofNullable(_gatewayRouterEligibilityLastUpdateDate);
  }

  public enum State {
    IDLE,
    WORKING,
    DELETED,
    REPAIRING;

    public boolean isActive() {
      return this == WORKING || this == REPAIRING;
    }
  }

  public enum ClusterType {
    REPLICASET,
    SHARDED,
    GEOSHARDED,
    @Hidden
    DISAGGREGATED;

    /**
     * @return true if the cluster type is a sharded cluster
     */
    public boolean isSharded() {
      return this == SHARDED || this == GEOSHARDED;
    }

    /**
     * @return true if the cluster type is a replica set
     */
    public boolean isReplicaSet() {
      return this == REPLICASET || this == DISAGGREGATED;
    }
  }

  @Schema(
      description =
          "Internal classification of the cluster's role. Possible values: NONE (regular user"
              + " cluster), SYSTEM_CLUSTER (system cluster for backup), INTERNAL_SHADOW_CLUSTER"
              + " (internal use shadow cluster for testing).")
  public enum InternalClusterRole {
    NONE,
    SYSTEM_CLUSTER,
    INTERNAL_SHADOW_CLUSTER
  }

  public enum EncryptionAtRestProvider {
    NONE,
    AWS,
    AZURE,
    GCP;

    public static EncryptionAtRestProvider find(final CloudProvider pCloudProvider) {
      return switch (pCloudProvider) {
        case AZURE -> EncryptionAtRestProvider.AZURE;
        case AWS -> EncryptionAtRestProvider.AWS;
        case GCP -> EncryptionAtRestProvider.GCP;
        default -> EncryptionAtRestProvider.NONE;
      };
    }

    public boolean isPrivateNetworkingSupported() {
      return this == AZURE || this == AWS;
    }
  }

  public enum LogRetention {
    DAYS30,
    DAYS90,
    YEARS7
  }

  public enum ProcessRestartAllowedState {
    NONE,
    IMMEDIATE
  }

  @Schema(
      description =
          "Root Certificate Authority that MongoDB Atlas cluster uses. MongoDB Cloud "
              + "supports Internet Security Research Group.")
  public enum RootCertType {
    @Hidden
    DST("O=Digital Signature Trust Co.,CN=DST Root CA X3"),
    ISRGROOTX1("C=US,O=Internet Security Research Group,CN=ISRG Root X1");

    public final String label;

    RootCertType(String label) {
      this.label = label;
    }

    public static RootCertType getRootCertTypeByString(final String pDefault) {
      return Arrays.asList(values()).stream()
          .filter(i -> i.name().equals(pDefault))
          .findFirst()
          .get();
    }
  }

  public static final String CLOUD_PROVIDER_DIFF = "cloudProviderName";

  public static class FieldDefs {

    public static final String ID = "_id";
    public static final String NAME = "name";
    public static final String GROUP_ID = "groupId";
    public static final String UNIQUE_ID = "uniqueId";
    public static final String CREATE_DATE = "createDate";
    public static final String LAST_UPDATE_DATE = "lastUpdateDate";
    public static final String DELETE_AFTER_DATE = "deleteAfterDate";
    public static final String MONGODB_VERSION = "mongoDBVersion";
    public static final String MONGODB_MAJOR_VERSION = "mongoDBMajorVersion";
    public static final String FIXED_MONGODB_VERSION = "fixedMongoDBVersion";
    public static final String FIXED_FEATURE_COMPATIBILITY_VERSION =
        "fixedFeatureCompatibilityVersion";
    public static final String FIXED_CPU_ARCH = "fixedCpuArch";
    public static final String FIXED_OS = "fixedOs";
    public static final String FIXED_ACME_PROVIDER = "fixedACMEProvider";
    public static final String DISK_SIZE_GB = "diskSizeGB";
    public static final String CUSTOMER_PROVIDED_DISK_SIZE_GB = "customerProvidedDiskSizeGB";
    public static final String MONGODB_URI_HOSTS = "mongoDBUriHosts";
    public static final String MONGODB_URI_LAST_UPDATE_DATE = "mongoDBUriHostsLastUpdateDate";
    public static final String STATE = "state";
    public static final String CLUSTER_TYPE = "clusterType";
    public static final String INTERNAL_CLUSTER_ROLE = "internalClusterRole";
    public static final String DELETE_REQUESTED = "deleteRequested";
    public static final String DELETE_REASON = "deleteReason";
    public static final String BACKUP_ENABLED = "backupEnabled";
    public static final String DISK_BACKUP_ENABLED = "diskBackupEnabled";
    public static final String PIT_ENABLED = "pitEnabled";
    public static final String RESTORE_JOB_IDS = "restoreJobIds";
    public static final String RESTORE_JOB_TYPE = "restoreJobType";
    public static final String DELETED_DATE = "deletedDate";
    public static final String ENSURE_CLUSTER_CONNECTIVITY_AFTER = "ensureClusterConnectivityAfter";
    public static final String NEEDS_MONGODB_CONFIG_PUBLISH_AFTER =
        "needsMongoDBConfigPublishAfter";
    public static final String NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED =
        "needsMongoDBConfigPublishRestartAllowed";
    public static final String IS_MTM = "isMTM";
    public static final String IS_MTM_SENTINEL = "isMTMSentinel";
    public static final String IS_ELIGIBLE_FOR_REDUCED_FLEX_PRICING =
        "isEligibleForReducedFlexPricing";
    public static final String IS_PAUSED = "isPaused";
    public static final String PAUSED_DATE = "pausedDate";
    public static final String CLOUD_PROVIDER_OPTIONS = "cloudProviderOptions";
    public static final String SERVERLESS_BACKUP_OPTIONS = "serverlessBackupOptions";
    public static final String BI_CONNECTOR = "biConnector";
    public static final String SRV_ADDRESS = "srvAddress";
    public static final String CLUSTER_TAGS = "clusterTags";
    public static final String GEO_SHARDING = "geoSharding";
    public static final String ENCRYPTION_AT_REST_PROVIDER = "encryptionAtRestProvider";
    public static final String PENDING_INDEXES = "pendingIndexes";
    public static final String EMPLOYEE_ACCESS_GRANT = "employeeAccessGrant";
    public static final String LOAD_BALANCED_HOSTNAME = "loadBalancedHostname";
    public static final String LOAD_BALANCED_MESH_HOSTNAME = "loadBalancedMeshHostname";
    public static final String LOG_RETENTION = "logRetention";
    public static final String LABELS = "labels";
    public static final String HOSTNAME_SCHEME_FOR_AGENTS = "hostnameSchemeForAgents";
    public static final String HOSTNAME_SUBDOMAIN_LEVEL = "hostnameSubdomainLevel";
    public static final String DEPLOYMENT_CLUSTER_NAME = "deploymentClusterName";
    public static final String PRIVATE_MONGODB_URI_HOSTS = "privateMongoDBUriHosts";
    public static final String PRIVATE_SRV_ADDRESS = "privateSrvAddress";
    public static final String PRIVATE_MONGODB_URI_HOSTS_MAP = "privateMongoDBUriHostsMap";
    public static final String PRIVATE_SRV_ADDRESS_MAP = "privateSRVAddressMap";
    public static final String STOP_CONTINUOUS_BACKUP = "stopContinuousBackup";
    public static final String FORCE_REPLICA_SET_RECONFIG = "forceReplicaSetReconfig";
    public static final String FORCE_REPLICA_SET_RECONFIG_HOSTS_TO_SKIP =
        "forceReplicaSetReconfigHostsToSkip";
    public static final String FORCE_REPLICA_SET_RECONFIG_REQUEST_DATE =
        "forceReplicaSetReconfigRequestDate";
    public static final String RESURRECT_REQUESTED = "resurrectRequested";
    public static final String RESURRECT_OPTIONS = "resurrectOptions";
    public static final String SHARDS_DRAINING = "shardsDraining";
    public static final String OS_TUNED_FILE_OVERRIDES = "osTunedFileOverrides";

    // Database field is actually on the subclasses of ClusterDescription,
    // but since this class has an abstract getting using this for a constant
    // name for the diffs
    public static final String INSTANCE_FAMILY = "instanceFamily";
    public static final String ANALYTICS_INSTANCE_FAMILY = "analyticsInstanceFamily";

    // Builder fields that will eventually be removed when the models in the DB
    // are transitioned to the cross cloud compatible models.
    public static final String INSTANCE_SIZE = "instanceSize";
    public static final String ANALYTICS_INSTANCE_SIZE = "analyticsInstanceSize";

    public static final String REPLICATION_SPEC_LIST = "replicationSpecList";

    public static final String ACCESS_TEMPORARILY_REVOKED = "accessTemporarilyRevoked";

    // AWS provider options fields
    public static final String ENCRYPT_EBS_VOLUME = "encryptEBSVolume";
    public static final String DISK_IOPS = "diskIOPS";
    public static final String EBS_VOLUME_TYPE = "ebsVolumeType";

    // Azure provider options
    public static final String DISK_TYPE = "diskType";

    // Free provider options
    public static final String PROVIDER_NAME = "providerName";
    public static final String ROOT_CERT_TYPE = "rootCertType";

    // Used only for tenant upgrades
    public static final String REPLICA_SET_VERSION_OVERRIDE = "replicaSetVersionOverride";

    // Continuous delivery
    public static final String VERSION_RELEASE_SYSTEM = "versionReleaseSystem";
    public static final String CONTINUOUS_DELIVERY_FCV = "continuousDeliveryFCV";

    // Force availability set migration for Azure cluster
    public static final String MIGRATE_FROM_AVAILABILITY_SETS = "migrateFromAvailabilitySets";

    // Fast Shared Tier
    public static final String DNS_PIN = "dnsPin";
    public static final String CLUSTER_NAME_PREFIX = "clusterNamePrefix";
    public static final String CLUSTER_PROVISION_TYPE = "clusterProvisionType";

    public static final String NEEDS_SERVERLESS_SNAPSHOT_FOR_PIT = "needsServerlessSnapshotForPIT";

    public static final String TERMINATION_PROTECTION_ENABLED = "terminationProtectionEnabled";

    public static final String NEEDS_SAMPLE_DATA_LOAD_AFTER = "needsSampleDataLoadAfter";
    public static final String CREATE_SAMPLE_SEARCH_INDEX = "createSampleSearchIndex";
    public static final String NEEDS_ENVOY_SYNC_AFTER = "needsEnvoySyncAfter";
    public static final String SAMPLE_DATASET_TO_LOAD = "sampleDatasetToLoad";
    public static final String LAST_DATA_VALIDATION_DATE = "lastDataValidationDate";
    public static final String LAST_DB_CHECK_DATE = "lastDbCheckDate";
    public static final String NEEDS_DB_CHECK_AFTER = "needsDbCheckAfter";
    public static final String DB_CHECK_PREFLIGHT_RETRY_COUNT = "dbCheckPreflightRetryCount";
    public static final String DISK_WARMING_MODE = "diskWarmingMode";
    public static final String BUMPER_FILE_OVERRIDES = "bumperFileOverrides";
    public static final String REPLICA_SET_SCALING_STRATEGY = "replicaSetScalingStrategy";
    public static final String AUTO_SCALING_MODE = "autoScalingMode";
    public static final String OS_POLICY_VERSION = "osPolicyVersion";
    public static final String IS_CRITICAL_OS_POLICY_RELEASE = "isCriticalOSPolicyRelease";
    public static final String REDACT_CLIENT_LOG_DATA = "redactClientLogData";
    public static final String USE_AWS_TIME_BASED_SNAPSHOT_COPY_FOR_FAST_INITIAL_SYNC =
        "useAwsTimeBasedSnapshotCopyForFastInitialSync";
    // numa
    public static final String CPU_SOCKET_BINDING = "cpuSocketBinding";

    public static final String FLEX_TENANT_MIGRATION_STATE = "flexTenantMigrationState";
    public static final String OLD_FLEX_TENANT_MIGRATION_STATE = "flexMigrationState";
    public static final String RESERVE_IPAM_IP_REQUESTED = "reserveIpamIpRequested";
    public static final String SWAP_IP_MAINTENANCE_ROUND_COMPLETED =
        "swapIpMaintenanceRoundCompleted";

    public static final String CANCEL_SHARD_DRAIN_REQUESTED = "cancelShardDrainRequested";
    public static final String CLUSTER_CONNECTION_STRING_CONFIGURATION =
        "clusterConnectionStringConfiguration";
    public static final String GTS_ROLLOUT = "gtsRollout";
    private static final String FREE_FROM_SERVERLESS = "freeFromServerless";
    public static final String NEEDS_PRIORITIES_RESET_FOR_PRIORITY_TAKEOVER_AFTER =
        "needsPrioritiesResetForPriorityTakeoverAfter";
    public static final String ALLOW_UNSAFE_ROLLING_OPERATION = "allowUnsafeRollingOperation";
    public static final String STORAGE_SYSTEM = "storageSystem";
    public static final String MONGOTUNE = "mongotune";
    public static final String NEEDS_MONGOTUNE_CONFIG_PUBLISH_AFTER =
        "needsMongotuneConfigPublishAfter";
    public static final String PROXY_PROTOCOL_FOR_PRIVATE_LINK_MODE =
        "proxyProtocolForPrivateLinkMode";
    public static final String PARTNER_INTEGRATIONS_DATA = "partnerIntegrationsData";
    public static final String ALWAYS_MANAGED_DEFAULT_RW_CONCERN_SINCE =
        "alwaysManagedDefaultRWConcernSince";
    public static final String AUTO_SHARDING = "autoSharding";
    public static final String IS_ASYMMETRIC_SHARD = "isAsymmetricShard";
    public static final String USE_EFFECTIVE_CLUSTER_FIELDS = "useEffectiveClusterFields";
    public static final String GATEWAY_ROUTER_ELIGIBLE = "gatewayRouterEligible";
    public static final String GATEWAY_ROUTER_ELIGIBILITY_LAST_UPDATE_DATE =
        "gatewayRouterEligibilityLastUpdateDate";
  }

  public static class Builder<T extends Builder<T, C>, C extends ClusterDescription> {

    protected BasicDBObject _dbObject;
    private static final Mapper _morphiaMapper = new Mapper();

    public Builder() {
      _dbObject = new BasicDBObject();
    }

    public Builder(final BasicDBObject pDBObject) {
      _dbObject = pDBObject;
    }

    @SuppressWarnings("unchecked")
    public T self() {
      return (T) this;
    }

    public <M> M mixin(final BiFunction<BasicDBObject, T, M> pMixinGetter) {
      return pMixinGetter.apply(_dbObject, self());
    }

    public T updateField(final String pKey, final Object pValue) {
      updateField(new String[] {pKey}, pValue);
      return self();
    }

    public T updateField(final String[] pPath, final Object pValue) {
      BasicDBObject currentDoc = _dbObject;
      for (int n = 0; n < pPath.length - 1; n++) {
        if (!currentDoc.containsField(pPath[n])) {
          currentDoc.append(pPath[n], new BasicDBObject());
        }

        final Object o = _dbObject.get(pPath[n]);
        if (!(o instanceof BasicDBObject)) {
          throw new IllegalArgumentException(
              String.format("Path (%s) does not point to a DBObject", String.join(".", pPath)));
        }

        currentDoc = (BasicDBObject) o;
      }

      currentDoc.append(pPath[pPath.length - 1], pValue);
      return self();
    }

    public T setName(final String pName) {
      updateField(new String[] {FieldDefs.ID, FieldDefs.NAME}, pName);
      return self();
    }

    public T setGroupId(final ObjectId pGroupId) {
      updateField(new String[] {FieldDefs.ID, FieldDefs.GROUP_ID}, pGroupId);
      return self();
    }

    public T setUniqueId(final ObjectId pUniqueId) {
      updateField(FieldDefs.UNIQUE_ID, pUniqueId);
      return self();
    }

    public T setCreateDate(final Date pCreateDate) {
      updateField(FieldDefs.CREATE_DATE, pCreateDate);
      return self();
    }

    public T setLastUpdateDate(final Date pLastUpdateDate) {
      updateField(FieldDefs.LAST_UPDATE_DATE, pLastUpdateDate);
      return self();
    }

    public T setDeleteAfterDate(final Date pDeleteAfterDate) {
      updateField(FieldDefs.DELETE_AFTER_DATE, pDeleteAfterDate);
      return self();
    }

    public T setMongoDBVersion(final String pVersion) {
      updateField(FieldDefs.MONGODB_VERSION, pVersion);
      return self();
    }

    public T setMongoDBMajorVersion(final String pMajorVersion) {
      updateField(FieldDefs.MONGODB_MAJOR_VERSION, pMajorVersion);
      return self();
    }

    public T setFixedMongoDBVersion(final Optional<FixedVersion> pFixedMongoDBVersion) {
      updateField(
          FieldDefs.FIXED_MONGODB_VERSION,
          pFixedMongoDBVersion.map(FixedVersion::toDBObject).orElse(null));
      return self();
    }

    public T setOSPolicyVersion(final String newOSPolicyVersion) {
      updateField(FieldDefs.OS_POLICY_VERSION, newOSPolicyVersion);
      return self();
    }

    public T setIsCriticalOSPolicyRelease(final Boolean newIsCriticalOSPolicyRelease) {
      updateField(FieldDefs.IS_CRITICAL_OS_POLICY_RELEASE, newIsCriticalOSPolicyRelease);
      return self();
    }

    public T setFixedFeatureCompatibilityVersion(
        final Optional<FixedVersion> pFixedFeatureCompatibilityVersion) {
      updateField(
          FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION,
          pFixedFeatureCompatibilityVersion.map(FixedVersion::toDBObject).orElse(null));
      return self();
    }

    public T setFixedCpuArch(final Optional<FixedVersion> pFixedCpuArch) {
      updateField(
          FieldDefs.FIXED_CPU_ARCH, pFixedCpuArch.map(FixedVersion::toDBObject).orElse(null));
      return self();
    }

    public T setFixedOs(final Optional<FixedVersion> pFixedOs) {
      updateField(FieldDefs.FIXED_OS, pFixedOs.map(FixedVersion::toDBObject).orElse(null));
      return self();
    }

    @VisibleForTesting
    public T setFixedAcmeProvider(final Optional<FixedVersion> pFixedAcmeProvider) {
      updateField(
          FieldDefs.FIXED_ACME_PROVIDER,
          pFixedAcmeProvider.map(FixedVersion::toDBObject).orElse(null));
      return self();
    }

    public T setFixedVersionByType(
        final FixedVersionType pFixedVersionType, final Optional<FixedVersion> pFixedVersion) {
      switch (pFixedVersionType) {
        case MONGODB:
          return setFixedMongoDBVersion(pFixedVersion);
        case FEATURE_COMPATIBILITY:
          return setFixedFeatureCompatibilityVersion(pFixedVersion);
        case CPU_ARCHITECTURE:
          return setFixedCpuArch(pFixedVersion);
        case OS:
          return setFixedOs(pFixedVersion);
        default:
          return self();
      }
    }

    /**
     * When using setReplicationSpecList(), ensure that all of the HardwareSpecs for the cluster are
     * filled out. Moving forward, getters for cloud provider options will fetch from these
     * HardwareSpecs. See helper ClusterDescription::getReplicationSpecListWithHardwareSpecUpdates
     * to update fields on embedded HardwareSpecs.
     */
    public T setReplicationSpecList(final List<ReplicationSpec> pReplicationSpecs) {
      updateField(
          FieldDefs.REPLICATION_SPEC_LIST,
          pReplicationSpecs.stream()
              .map(ReplicationSpec::toDBObject)
              .collect(DbUtils.toBasicDBList()));
      return self();
    }

    public T updateHardwareForProvider(
        final CloudProvider pCloudProvider,
        final HardwareSpec.Builder pHardwareUpdate,
        final ObjectId replicationSpecId) {
      return setReplicationSpecList(
          readCrossCloudReplicationSpecs(_dbObject).stream()
              .map(
                  ccrs -> {
                    if (ccrs.getId().equals(replicationSpecId)) {
                      return ccrs.copy()
                          .updateAllHardwareForProvider(pCloudProvider, pHardwareUpdate)
                          .build();
                    } else {
                      return ccrs;
                    }
                  })
              .collect(Collectors.toList()));
    }

    public T updateHardwareForNodeType(
        final HardwareSpec.Builder pHardwareUpdate,
        final NodeTypeFamily pNodeTypeFamily,
        final ObjectId replicationSpecId) {
      return setReplicationSpecList(
          readCrossCloudReplicationSpecs(_dbObject).stream()
              .map(
                  ccrs -> {
                    if (ccrs.getId().equals(replicationSpecId)) {
                      return ccrs.copy().updateHardware(pHardwareUpdate, pNodeTypeFamily).build();
                    } else {
                      return ccrs;
                    }
                  })
              .collect(Collectors.toList()));
    }

    public T updateHardwaresForNodeType(
        final HardwareSpec.Builder pHardwareUpdate, final NodeType pNodeType) {
      return setReplicationSpecList(
          readCrossCloudReplicationSpecs(_dbObject).stream()
              .map(ccrs -> ccrs.copy().updateHardware(pHardwareUpdate, pNodeType).build())
              .collect(Collectors.toList()));
    }

    private void setTenantOptions(final BasicDBObject pOptions, final String pField) {
      // TODO<CLOUDP-262799>: clean this up
      // Multi Cloud Cluster Schema Migration epic note:
      // We loop over the options and set them individually, because if we set the whole object we
      // will lose some legacy fields like instanceSize and providerName.
      // We won't need to do this after the migration
      pOptions.forEach((option, value) -> updateField(new String[] {pField, option}, value));
    }

    public T setFreeTenantProviderOptions(final ClusterDescriptionProviderOptions pOptions) {
      setTenantOptions(pOptions.toDBObject(), FieldDefs.CLOUD_PROVIDER_OPTIONS);
      return self();
    }

    public T setServerlessTenantProviderOptions(final ClusterDescriptionProviderOptions pOptions) {
      setTenantOptions(pOptions.toDBObject(), FieldDefs.CLOUD_PROVIDER_OPTIONS);
      return self();
    }

    public T setServerlessBackupOptions(
        final ClusterDescriptionBackupOptions pServerlessBackupOptions) {
      setTenantOptions(pServerlessBackupOptions.toDBObject(), FieldDefs.SERVERLESS_BACKUP_OPTIONS);
      return self();
    }

    public T setFlexTenantProviderOptions(final ClusterDescriptionProviderOptions pOptions) {
      setTenantOptions(pOptions.toDBObject(), FieldDefs.CLOUD_PROVIDER_OPTIONS);
      return self();
    }

    public T setDiskSizeGB(final double pDiskSizeGB) {
      updateField(FieldDefs.DISK_SIZE_GB, pDiskSizeGB);
      return self();
    }

    /**
     * Sets the disk size in GB as originally specified by the customer.
     *
     * <p>This method allows setting the disk size value that was explicitly provided by the
     * customer when creating or modifying the cluster configuration. This value represents the
     * original customer input before any system adjustments or calculations are applied.
     *
     * @param pCustomerProvidedDiskSizeGB the customer-provided disk size in gigabytes, must be
     *     non-negative
     * @return this builder instance for method chaining
     */
    public T setCustomerProvidedDiskSizeGB(final double pCustomerProvidedDiskSizeGB) {
      updateField(FieldDefs.CUSTOMER_PROVIDED_DISK_SIZE_GB, pCustomerProvidedDiskSizeGB);
      return self();
    }

    /**
     * Sets the configuration for whether effective cluster fields should be used.
     *
     * <p>Effective cluster fields provide a mechanism to override or supplement the standard
     * cluster configuration with computed or derived values. This setting controls whether the
     * system should apply these effective field values when processing cluster API operations.
     *
     * @param pUseEffectiveFields the effective fields configuration setting to apply, must not be
     *     null
     * @return this builder instance for method chaining
     */
    public T setUseEffectiveFields(final EffectiveFields pUseEffectiveFields) {
      updateField(FieldDefs.USE_EFFECTIVE_CLUSTER_FIELDS, pUseEffectiveFields.name());
      return self();
    }

    public T addPendingIndexes(final List<IndexConfig> pPendingIndexes) {
      final BasicDBList pendingIndexes =
          _dbObject.containsField(FieldDefs.PENDING_INDEXES)
              ? (BasicDBList) _dbObject.get(FieldDefs.PENDING_INDEXES)
              : new BasicDBList();

      final List<IndexConfig.KeyPattern> existingKeyPatterns =
          pendingIndexes.stream()
              .map(e -> _morphiaMapper.fromDBObject(null, IndexConfig.class, (DBObject) e, null))
              .map(IndexConfig::getKeyPattern)
              .collect(Collectors.toList());

      final List<DBObject> nonDupes =
          pPendingIndexes.stream()
              .filter(pi -> !existingKeyPatterns.contains(pi.getKeyPattern()))
              .map(
                  pi -> {
                    existingKeyPatterns.add(pi.getKeyPattern());
                    return _morphiaMapper.toDBObject(pi);
                  })
              .collect(Collectors.toList());

      pendingIndexes.addAll(nonDupes);

      _dbObject.put(FieldDefs.PENDING_INDEXES, pendingIndexes);

      return self();
    }

    public T setLabels(final List<NDSLabel> pLabels) {
      final BasicDBList labels = new BasicDBList();
      labels.addAll(pLabels.stream().map(NDSLabel::toDBObject).collect(Collectors.toList()));
      updateField(FieldDefs.LABELS, labels);
      return self();
    }

    public T setMongoUriHosts(final String[] pMongoUriHosts) {
      updateField(
          FieldDefs.MONGODB_URI_HOSTS,
          Arrays.stream(pMongoUriHosts).collect(new DbUtils.BasicDBListCollector<>()));
      return self();
    }

    public T setPrivateMongoUriHosts(final String[] pMongoUriHosts) {
      updateField(
          FieldDefs.PRIVATE_MONGODB_URI_HOSTS,
          Arrays.stream(pMongoUriHosts).collect(new DbUtils.BasicDBListCollector<>()));
      return self();
    }

    public T setPrivateSRVAddress(final String pSRVAddress) {
      updateField(FieldDefs.PRIVATE_SRV_ADDRESS, pSRVAddress);
      return self();
    }

    public T setPrivateLinkMongoUriHosts(
        final Map<String, List<String>> pPrivateLinkMongoUriHostsMap) {
      final BasicDBObject obj = new BasicDBObject();
      pPrivateLinkMongoUriHostsMap.forEach(
          (key, val) -> obj.append(key, val.stream().collect(DbUtils.toBasicDBList())));
      updateField(FieldDefs.PRIVATE_MONGODB_URI_HOSTS_MAP, obj);
      return self();
    }

    public T setPrivateLinkSrvAddresses(final Map<String, String> pPrivateLinkSrvAddresses) {
      final BasicDBObject obj = new BasicDBObject(pPrivateLinkSrvAddresses);
      updateField(FieldDefs.PRIVATE_SRV_ADDRESS_MAP, obj);
      return self();
    }

    public T setMongoUriLastUpdateDate(final Date pLastUpdateDate) {
      updateField(FieldDefs.MONGODB_URI_LAST_UPDATE_DATE, pLastUpdateDate);
      return self();
    }

    public T setState(final State pState) {
      updateField(FieldDefs.STATE, pState.name());
      return self();
    }

    public T setEncryptionAtRestProvider(final EncryptionAtRestProvider pEncryptionAtRestProvider) {
      updateField(FieldDefs.ENCRYPTION_AT_REST_PROVIDER, pEncryptionAtRestProvider.name());
      return self();
    }

    public T setClusterType(final ClusterType pClusterType) {
      updateField(FieldDefs.CLUSTER_TYPE, pClusterType.name());
      return self();
    }

    public T setInternalClusterRole(final InternalClusterRole pInternalClusterRole) {
      updateField(FieldDefs.INTERNAL_CLUSTER_ROLE, pInternalClusterRole.name());
      return self();
    }

    public T setDeleteRequested(final boolean pIsDeleteRequested) {
      updateField(FieldDefs.DELETE_REQUESTED, pIsDeleteRequested);
      return self();
    }

    public T setDeleteReason(final Optional<DeleteClusterReason> pDeleteReason) {
      updateField(FieldDefs.DELETE_REASON, pDeleteReason.orElse(null));
      return self();
    }

    public T setBackupEnabled(final boolean pIsBackupEnabled) {
      updateField(FieldDefs.BACKUP_ENABLED, pIsBackupEnabled);
      return self();
    }

    public T setDiskBackupEnabled(final boolean pDiskBackupEnabled) {
      updateField(FieldDefs.DISK_BACKUP_ENABLED, pDiskBackupEnabled);
      return self();
    }

    public T setPitEnabled(final boolean pPitEnabled) {
      updateField(FieldDefs.PIT_ENABLED, pPitEnabled);
      return self();
    }

    public T setStopContinuousBackup(final boolean pStopContinuousBackup) {
      updateField(FieldDefs.STOP_CONTINUOUS_BACKUP, pStopContinuousBackup);
      return self();
    }

    public T setRestoreJobIds(final List<ObjectId> pJobIds) {
      updateField(FieldDefs.RESTORE_JOB_IDS, pJobIds.stream().collect(DbUtils.toBasicDBList()));
      return self();
    }

    public T setRestoreJobType(final RestoreJobType pRestoreJobType) {
      updateField(
          FieldDefs.RESTORE_JOB_TYPE, pRestoreJobType == null ? null : pRestoreJobType.name());
      return self();
    }

    public T setDeletedDate(final Date pDeletedDate) {
      updateField(FieldDefs.DELETED_DATE, pDeletedDate);
      return self();
    }

    public T setNeedsPrioritiesResetForPriorityTakeoverAfter(final Date pDate) {
      updateField(FieldDefs.NEEDS_PRIORITIES_RESET_FOR_PRIORITY_TAKEOVER_AFTER, pDate);
      return self();
    }

    public T setEnsureClusterConnectivityAfter(final Date pEnsureAfter) {
      updateField(FieldDefs.ENSURE_CLUSTER_CONNECTIVITY_AFTER, pEnsureAfter);
      return self();
    }

    public T setNeedsMongoDBConfigPublishAfter(final Date pPublishAfter) {
      updateField(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, pPublishAfter);
      return self();
    }

    public T setNeedsMongoDBConfigPublishRestartAllowed(
        final ProcessRestartAllowedState pPublishRestartAllowed) {
      updateField(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED, pPublishRestartAllowed);
      return self();
    }

    public T setIsMTM(final boolean pIsMTM) {
      updateField(FieldDefs.IS_MTM, pIsMTM);
      return self();
    }

    public T setIsMTMSentinel(final boolean pIsMTMSentinel) {
      updateField(FieldDefs.IS_MTM_SENTINEL, pIsMTMSentinel);
      return self();
    }

    public T setIsEligibleForReducedFlexPricing(final boolean pIsEligibleForReducedFlexPricing) {
      updateField(FieldDefs.IS_ELIGIBLE_FOR_REDUCED_FLEX_PRICING, pIsEligibleForReducedFlexPricing);
      return self();
    }

    public T setAutoScalingForProvider(
        final CloudProvider pCloudProvider,
        final AutoScaling pAutoScaling,
        final NodeTypeFamily pNodeTypeFamily) {
      setReplicationSpecList(
          readCrossCloudReplicationSpecs(_dbObject).stream()
              .map(
                  spec -> {
                    if (pNodeTypeFamily == NodeTypeFamily.BASE) {
                      return spec.copy()
                          .updateBaseAutoScalingForProvider(pCloudProvider, pAutoScaling)
                          .build();
                    } else if (pNodeTypeFamily == NodeTypeFamily.ANALYTICS) {
                      return spec.copy()
                          .updateAnalyticsAutoScalingForProvider(pCloudProvider, pAutoScaling)
                          .build();
                    } else {
                      throw new IllegalArgumentException(
                          String.format("Invalid node type family %s", pNodeTypeFamily));
                    }
                  })
              .collect(Collectors.toList()));
      return self();
    }

    public T setIsPaused(final boolean pIsPaused) {
      updateField(FieldDefs.IS_PAUSED, pIsPaused);
      return self();
    }

    public T setPausedDate(final Date pPausedDate) {
      updateField(FieldDefs.PAUSED_DATE, pPausedDate);
      return self();
    }

    public T setBiConnector(final BiConnector pBiConnector) {
      final BasicDBObject obj = pBiConnector.toDBObject();
      final BasicDBObject existingObj = (BasicDBObject) this._dbObject.get(FieldDefs.BI_CONNECTOR);
      if (existingObj != null) {
        final BiConnector existingBiConnector = new BiConnector(existingObj);
        final boolean biConnectorCurrentlyEnabled = existingBiConnector.isEnabled();
        // Set the disabled date on the BI Connector if it is current enabled and
        // is being disabled
        if (biConnectorCurrentlyEnabled && !pBiConnector.isEnabled()) {
          obj.put(BiConnector.FieldDefs.LAST_DISABLED_DATE, new Date());
        } else {
          obj.put(
              BiConnector.FieldDefs.LAST_DISABLED_DATE,
              existingBiConnector.getLastDisabledDate().orElse(null));
        }

        // Handle hostname and needs sync updates / retention of existing
        if (!pBiConnector.getHostnames().isEmpty()) {
          obj.put(BiConnector.FieldDefs.HOSTNAMES, pBiConnector.getHostnames().toDBList());
        } else {
          obj.put(BiConnector.FieldDefs.HOSTNAMES, existingBiConnector.getHostnames().toDBList());
        }

        final Map<String, String> privateLinkHostnameMap;
        if (!pBiConnector.getPrivateLinkHostnamesMap().isEmpty()) {
          privateLinkHostnameMap = pBiConnector.getPrivateLinkHostnamesMap();
        } else {
          privateLinkHostnameMap = existingBiConnector.getPrivateLinkHostnamesMap();
        }
        obj.put(
            BiConnector.FieldDefs.PRIVATELINK_HOSTNAMES_MAP,
            new BasicDBObject(privateLinkHostnameMap));

        obj.put(
            BiConnector.FieldDefs.NEEDS_SYNC,
            pBiConnector.getNeedsSync().orElse(existingBiConnector.getNeedsSync().orElse(null)));
      }
      updateField(FieldDefs.BI_CONNECTOR, obj);
      return self();
    }

    public T setSRVAddress(final String pSRVAddress) {
      updateField(FieldDefs.SRV_ADDRESS, pSRVAddress);
      return self();
    }

    public T setClusterTags(final Set<ClusterTag> pTags) {
      updateField(
          FieldDefs.CLUSTER_TAGS,
          pTags.stream().map(ClusterTag::name).collect(DbUtils.toBasicDBList()));
      return self();
    }

    public T setGeoSharding(final GeoSharding pGeoSharding) {
      updateField(FieldDefs.GEO_SHARDING, pGeoSharding.toDBObject());
      return self();
    }

    public T setEmployeeAccessGrant(final EmployeeAccessGrant pEmployeeAccessGrant) {
      final DBObject fieldValue =
          Optional.ofNullable(pEmployeeAccessGrant).map(_morphiaMapper::toDBObject).orElse(null);
      updateField(FieldDefs.EMPLOYEE_ACCESS_GRANT, fieldValue);
      return self();
    }

    @VisibleForTesting
    public T setOsTunedFileOverrides(final OsTunedFileOverrides pOsTunedFileOverrides) {
      updateField(FieldDefs.OS_TUNED_FILE_OVERRIDES, pOsTunedFileOverrides.toDBObject());
      return self();
    }

    public T setLogRetention(final LogRetention pLogRetention) {
      updateField(FieldDefs.LOG_RETENTION, pLogRetention);
      return self();
    }

    public T setHostnameSchemeForAgents(
        final InstanceHostname.HostnameScheme pHostnameSchemeForAgents) {
      updateField(FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS, pHostnameSchemeForAgents);
      return self();
    }

    public T setHostnameSubdomainLevel(InstanceHostname.SubdomainLevel pHostnameSubdomainLevel) {
      updateField(FieldDefs.HOSTNAME_SUBDOMAIN_LEVEL, pHostnameSubdomainLevel);
      return self();
    }

    public T setDeploymentClusterName(final String pDeploymentClusterName) {
      updateField(FieldDefs.DEPLOYMENT_CLUSTER_NAME, pDeploymentClusterName);
      return self();
    }

    public T setForceReplicaSetReconfig(final Date pForceReplicaSetReconfig) {
      final Optional<Date> version =
          Optional.ofNullable(_dbObject.getDate(FieldDefs.FORCE_REPLICA_SET_RECONFIG_REQUEST_DATE));
      // The version field must ALWAYS proceed forwards.
      //
      // If a forced-update is currently merged, and a new update comes in, the version field must
      // be incremented to preserve the force state.
      // If the version is not incremented we will disable force.
      if (version.isPresent()
          && pForceReplicaSetReconfig != null
          && !pForceReplicaSetReconfig.after(version.get())) {
        updateField(FieldDefs.FORCE_REPLICA_SET_RECONFIG_REQUEST_DATE, null);
      } else {
        updateField(FieldDefs.FORCE_REPLICA_SET_RECONFIG_REQUEST_DATE, pForceReplicaSetReconfig);
      }
      return self();
    }

    public T setForceReplicaSetReconfigHostsToSkip(
        final List<String> pForceReplicaSetReconfigHostsToSkip) {
      updateField(
          FieldDefs.FORCE_REPLICA_SET_RECONFIG_HOSTS_TO_SKIP, pForceReplicaSetReconfigHostsToSkip);
      return self();
    }

    public T setResurrectRequested(final Date pResurrectRequested) {
      updateField(FieldDefs.RESURRECT_REQUESTED, pResurrectRequested);
      return self();
    }

    public T setResurrectOptions(final ResurrectOptions pResurrectOptions) {
      updateField(
          FieldDefs.RESURRECT_OPTIONS,
          Optional.ofNullable(pResurrectOptions).map(ResurrectOptions::toDBObject).orElse(null));
      return self();
    }

    public T setAccessTemporarilyRevoked(final Boolean pAccessTemporarilyRevoked) {
      updateField(FieldDefs.ACCESS_TEMPORARILY_REVOKED, pAccessTemporarilyRevoked);
      return self();
    }

    public T setReplicaSetVersionOverride(final Integer pVersion) {
      updateField(FieldDefs.REPLICA_SET_VERSION_OVERRIDE, pVersion);
      return self();
    }

    @SuppressWarnings("unchecked")
    public C build() {
      return (C) getCloudProviderClusterDescription(_dbObject);
    }

    public T setRootCertType(final RootCertType pRootCertType) {
      updateField(FieldDefs.ROOT_CERT_TYPE, pRootCertType);
      return self();
    }

    public T setVersionReleaseSystem(final VersionReleaseSystem pVersionReleaseSystem) {
      updateField(FieldDefs.VERSION_RELEASE_SYSTEM, pVersionReleaseSystem);
      return self();
    }

    public T setContinuousDeliveryFCV(final String pContinuousDeliveryFCV) {
      updateField(FieldDefs.CONTINUOUS_DELIVERY_FCV, pContinuousDeliveryFCV);
      return self();
    }

    public T setMigrateFromAvailabilitySets(final Set<String> pMigrateFromAvailabilitySets) {
      updateField(
          FieldDefs.MIGRATE_FROM_AVAILABILITY_SETS,
          pMigrateFromAvailabilitySets.stream().collect(DbUtils.toBasicDBList()));
      return self();
    }

    public T setLoadBalancedHostname(final String pLoadBalancedHostname) {
      updateField(FieldDefs.LOAD_BALANCED_HOSTNAME, pLoadBalancedHostname);
      return self();
    }

    public T setLoadBalancedMeshHostname(final String pLoadBalancedMeshHostname) {
      updateField(FieldDefs.LOAD_BALANCED_MESH_HOSTNAME, pLoadBalancedMeshHostname);
      return self();
    }

    public T setDnsPin(final String pDnsPin) {
      updateField(FieldDefs.DNS_PIN, pDnsPin);
      return self();
    }

    public T setClusterNamePrefix(final String pClusterNamePrefix) {
      updateField(FieldDefs.CLUSTER_NAME_PREFIX, pClusterNamePrefix);
      return self();
    }

    public T setClusterProvisionType(final ClusterProvisionType pProvisionType) {
      updateField(FieldDefs.CLUSTER_PROVISION_TYPE, pProvisionType);
      return self();
    }

    public T setTerminationProtectionEnabled(final boolean pTerminationProtectionEnabled) {
      updateField(FieldDefs.TERMINATION_PROTECTION_ENABLED, pTerminationProtectionEnabled);
      return self();
    }

    public T setNeedsSampleDataLoadAfter(final Date pNeedsSampleDataLoadAfter) {
      updateField(FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER, pNeedsSampleDataLoadAfter);
      return self();
    }

    public T setCreateSampleSearchIndex(final boolean pCreateSampleSearchIndex) {
      updateField(FieldDefs.CREATE_SAMPLE_SEARCH_INDEX, pCreateSampleSearchIndex);
      return self();
    }

    @VisibleForTesting
    public T setNeedsServerlessSnapshotForPit(final Date pNeedsServerlessSnapshotForPit) {
      updateField(FieldDefs.NEEDS_SERVERLESS_SNAPSHOT_FOR_PIT, pNeedsServerlessSnapshotForPit);
      return self();
    }

    public T setNeedsEnvoySyncAfter(final Date pNeedsEnvoySyncAfter) {
      updateField(FieldDefs.NEEDS_ENVOY_SYNC_AFTER, pNeedsEnvoySyncAfter);
      return self();
    }

    public T setSampleDatasetToLoad(final SampleDataset pSampleDataset) {
      updateField(FieldDefs.SAMPLE_DATASET_TO_LOAD, pSampleDataset);

      return self();
    }

    public T setLastDataValidationDate(final Date pLastDataValidationDate) {
      updateField(FieldDefs.LAST_DATA_VALIDATION_DATE, pLastDataValidationDate);
      return self();
    }

    public T setLastDbCheckDate(final Date pLastDbCheckDate) {
      updateField(FieldDefs.LAST_DB_CHECK_DATE, pLastDbCheckDate);
      return self();
    }

    public T setNeedsDbCheckAfter(final Date pNeedsDbCheckAfter) {
      updateField(FieldDefs.NEEDS_DB_CHECK_AFTER, pNeedsDbCheckAfter);
      return self();
    }

    public T setDbCheckPreflightRetryCount(final int pDbCheckPreflightRetryCount) {
      updateField(FieldDefs.DB_CHECK_PREFLIGHT_RETRY_COUNT, pDbCheckPreflightRetryCount);
      return self();
    }

    public T setDiskWarmingMode(final Optional<DiskWarmingMode> pDiskWarmingMode) {
      updateField(
          FieldDefs.DISK_WARMING_MODE,
          pDiskWarmingMode.isEmpty() ? null : pDiskWarmingMode.get().name());
      return self();
    }

    public T setBumperFileOverrides(final List<BumperFileOverride> pBumperFileOverrides) {
      updateField(
          FieldDefs.BUMPER_FILE_OVERRIDES,
          pBumperFileOverrides.stream()
              .map(BumperFileOverride::toDBObject)
              .collect(DbUtils.toBasicDBList()));
      return self();
    }

    public T setReplicaSetScalingStrategy(
        final ReplicaSetScalingStrategy pReplicaSetScalingStrategy) {
      updateField(FieldDefs.REPLICA_SET_SCALING_STRATEGY, pReplicaSetScalingStrategy);
      return self();
    }

    public T setAutoScalingMode(final AutoScalingMode pAutoScalingMode) {
      updateField(FieldDefs.AUTO_SCALING_MODE, pAutoScalingMode);
      return self();
    }

    public T setFlexTenantMigrationState(
        @Nullable final FlexTenantMigrationState pFlexTenantMigrationState) {
      updateField(
          FieldDefs.FLEX_TENANT_MIGRATION_STATE,
          pFlexTenantMigrationState != null ? pFlexTenantMigrationState.toDBObject() : null);
      return self();
    }

    /**
     * Sets whether the cluster is eligible for gateway router.
     *
     * @param pGatewayRouterEligible eligibility flag
     * @return this builder instance
     */
    public T setGatewayRouterEligible(final Boolean pGatewayRouterEligible) {
      updateField(FieldDefs.GATEWAY_ROUTER_ELIGIBLE, pGatewayRouterEligible);
      return self();
    }

    /**
     * Sets the last update date for gateway router eligibility.
     *
     * @param pGatewayRouterEligibilityLastUpdateDate the last update date
     * @return this builder instance
     */
    public T setGatewayRouterEligibilityLastUpdateDate(
        final Date pGatewayRouterEligibilityLastUpdateDate) {
      updateField(
          FieldDefs.GATEWAY_ROUTER_ELIGIBILITY_LAST_UPDATE_DATE,
          pGatewayRouterEligibilityLastUpdateDate);
      return self();
    }

    public T setRedactClientLogData(final Boolean pRedactClientLogData) {
      updateField(FieldDefs.REDACT_CLIENT_LOG_DATA, pRedactClientLogData);
      return self();
    }

    public T setCpuSocketBinding(final List<Integer> pCpuSocketBinding) {
      updateField(
          FieldDefs.CPU_SOCKET_BINDING,
          pCpuSocketBinding.stream().collect(DbUtils.toBasicDBList()));
      return self();
    }

    public T setClusterConnectionStringConfiguration(
        final ClusterConnectionStringConfiguration pClusterConnectionStringConfiguration) {
      updateField(
          FieldDefs.CLUSTER_CONNECTION_STRING_CONFIGURATION,
          pClusterConnectionStringConfiguration.toDBObject());
      return self();
    }

    public T setGTSRollout(final boolean pGTSRollout) {
      updateField(FieldDefs.GTS_ROLLOUT, pGTSRollout);
      return self();
    }

    public T setFreeFromServerless(final boolean pFreeFromServerless) {
      updateField(FieldDefs.FREE_FROM_SERVERLESS, pFreeFromServerless);
      return self();
    }

    public T setSwapIpMaintenanceRound(
        final Optional<SwapIpMaintenanceRound> swapIpMaintenanceRound) {
      updateField(
          FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED, swapIpMaintenanceRound.orElse(null));
      return self();
    }

    public T setCancelShardDrainRequested(final Date pCancelShardDrainRequested) {
      updateField(FieldDefs.CANCEL_SHARD_DRAIN_REQUESTED, pCancelShardDrainRequested);
      return self();
    }

    public T setAllowUnsafeRollingOperation(final boolean pAllowUnsafeRollingOperation) {
      updateField(FieldDefs.ALLOW_UNSAFE_ROLLING_OPERATION, pAllowUnsafeRollingOperation);
      return self();
    }

    public T setStorageSystem(final StorageSystem pStorageSystem) {
      updateField(FieldDefs.STORAGE_SYSTEM, pStorageSystem);
      return self();
    }

    public T setMongotuneStatus(final MongotuneStatus pMongotuneStatus) {
      updateField(
          FieldDefs.MONGOTUNE,
          Optional.ofNullable(pMongotuneStatus).map(MongotuneStatus::toDBObject).orElse(null));
      return self();
    }

    /**
     * Sets whether the cluster is using AWS time-based snapshot copies for optimized initial sync
     * across regions.
     *
     * @param pUseAwsTimeBasedSnapshotCopyForFastInitialSync true if the cluster is using AWS
     *     time-based snapshot copies for optimized initial sync across regions
     * @return this builder
     */
    public T setUseAwsTimeBasedSnapshotCopyForFastInitialSync(
        final boolean pUseAwsTimeBasedSnapshotCopyForFastInitialSync) {
      updateField(
          FieldDefs.USE_AWS_TIME_BASED_SNAPSHOT_COPY_FOR_FAST_INITIAL_SYNC,
          pUseAwsTimeBasedSnapshotCopyForFastInitialSync);
      return self();
    }

    /**
     * Sets the date after which mongotune config should be published.
     *
     * @param pNeedsMongotuneConfigPublishAfter the date after which mongotune config should be
     *     published
     * @return this builder
     */
    public T setNeedsMongotuneConfigPublishAfter(final Date pNeedsMongotuneConfigPublishAfter) {
      updateField(
          FieldDefs.NEEDS_MONGOTUNE_CONFIG_PUBLISH_AFTER, pNeedsMongotuneConfigPublishAfter);
      return self();
    }

    public T setProxyProtocolForPrivateLinkMode(
        final ProxyProtocolForPrivateLinkMode pProxyProtocolForPrivateLinkMode) {
      updateField(FieldDefs.PROXY_PROTOCOL_FOR_PRIVATE_LINK_MODE, pProxyProtocolForPrivateLinkMode);
      return self();
    }

    public T setPartnerIntegrationsData(final PartnerIntegrationsData pPartnerIntegrationsData) {
      updateField(
          FieldDefs.PARTNER_INTEGRATIONS_DATA,
          Optional.ofNullable(pPartnerIntegrationsData)
              .map(PartnerIntegrationsData::toDBObject)
              .orElse(null));
      return self();
    }

    public T setAlwaysManagedDefaultRWConcernSince(
        @Nullable final Date pAlwaysManagedDefaultRWConcernSince) {
      updateField(
          FieldDefs.ALWAYS_MANAGED_DEFAULT_RW_CONCERN_SINCE, pAlwaysManagedDefaultRWConcernSince);
      return self();
    }

    /**
     * Sets the auto-sharding configuration for this cluster.
     *
     * @param pAutoSharding the auto-sharding configuration, or null to disable
     * @return this builder instance
     */
    public T setAutoSharding(@Nullable final AutoSharding pAutoSharding) {
      updateField(
          FieldDefs.AUTO_SHARDING, pAutoSharding != null ? pAutoSharding.toDBObject() : null);
      return self();
    }
  }

  public static class BiConnector {

    private final boolean _enabled;
    private final BiConnectorReadPreference _readPreference;
    private final Date _lastDisabledDate;
    private final Hostnames _hostnames;
    private final Map<String, String> _privateLinkHostnamesMap;
    private final Date _needsSync;

    public BiConnector(final BasicDBObject pDBObject) {
      _enabled = pDBObject.getBoolean(FieldDefs.ENABLED);
      _readPreference =
          BiConnectorReadPreference.fromValue(pDBObject.getString(FieldDefs.READ_PREFERENCE)).get();
      _lastDisabledDate = pDBObject.getDate(FieldDefs.LAST_DISABLED_DATE);
      _hostnames =
          new Hostnames(
              Optional.ofNullable(pDBObject.get(FieldDefs.HOSTNAMES))
                  .map(BasicDBList.class::cast)
                  .orElse(new BasicDBList()));
      _needsSync = pDBObject.getDate(FieldDefs.NEEDS_SYNC);
      _privateLinkHostnamesMap =
          ((BasicDBObject)
                  pDBObject.getOrDefault(FieldDefs.PRIVATELINK_HOSTNAMES_MAP, new BasicDBObject()))
              .entrySet().stream()
                  .collect(Collectors.toMap(Map.Entry::getKey, e -> (String) e.getValue()));
    }

    public BiConnector(final boolean pEnabled, final BiConnectorReadPreference pReadPreference) {
      _enabled = pEnabled;
      _readPreference = pReadPreference;
      _lastDisabledDate = null;
      _hostnames = new Hostnames();
      _needsSync = null;
      _privateLinkHostnamesMap = Map.of();
    }

    public BasicDBObject toDBObject() {
      return new BasicDBObject()
          .append(FieldDefs.ENABLED, _enabled)
          .append(FieldDefs.READ_PREFERENCE, _readPreference.getValue())
          .append(FieldDefs.LAST_DISABLED_DATE, _lastDisabledDate)
          .append(FieldDefs.HOSTNAMES, _hostnames.toDBList())
          .append(FieldDefs.NEEDS_SYNC, _needsSync)
          .append(FieldDefs.PRIVATELINK_HOSTNAMES_MAP, new BasicDBObject(_privateLinkHostnamesMap));
    }

    public BiConnectorReadPreference getReadPreference() {
      return _readPreference;
    }

    public boolean isEnabled() {
      return _enabled;
    }

    public Optional<Date> getLastDisabledDate() {
      return Optional.ofNullable(_lastDisabledDate);
    }

    public Hostnames getHostnames() {
      return _hostnames;
    }

    public Optional<Date> getNeedsSync() {
      return Optional.ofNullable(_needsSync);
    }

    public Map<String, String> getPrivateLinkHostnamesMap() {
      return _privateLinkHostnamesMap;
    }

    public Builder copy() {
      return new Builder(this.toDBObject());
    }

    public static class Builder {
      protected BasicDBObject _dbObject;

      public Builder() {
        _dbObject = new BasicDBObject();
      }

      public Builder(final BasicDBObject pDBObject) {
        _dbObject = pDBObject;
      }

      public Builder setHostnames(final Hostnames pHostnames) {
        _dbObject.put(FieldDefs.HOSTNAMES, pHostnames.toDBList());
        return this;
      }

      public Builder setNeedsSync(final Date pNeedsSync) {
        _dbObject.put(FieldDefs.NEEDS_SYNC, pNeedsSync);
        return this;
      }

      public Builder setPrivateLinkHostnamesMap(final Map<String, String> pPrivateLinkHostnames) {
        _dbObject.put(
            FieldDefs.PRIVATELINK_HOSTNAMES_MAP, new BasicDBObject(pPrivateLinkHostnames));
        return this;
      }

      public Builder setEnabled(final boolean pEnabled) {
        _dbObject.put(FieldDefs.ENABLED, pEnabled);
        return this;
      }

      public BiConnector build() {
        return new BiConnector(_dbObject);
      }
    }

    public static class FieldDefs {

      public static final String ENABLED = "enabled";
      public static final String READ_PREFERENCE = "readPreference";
      public static final String LAST_DISABLED_DATE = "lastDisabledDate";
      public static final String HOSTNAMES = "hostnames";
      public static final String NEEDS_SYNC = "needsSync";
      public static final String PRIVATELINK_HOSTNAMES_MAP = "privateLinkHostnamesMap";
    }
  }

  public static class ResurrectOptions {

    private ObjectId _clusterUniqueId;

    public ResurrectOptions(final BasicDBObject pDBObject) {
      if (pDBObject != null) {
        _clusterUniqueId = pDBObject.getObjectId(FieldDefs.CLUSTER_UNIQUE_ID);
      }
    }

    public ResurrectOptions(final ObjectId pClusterUniqueId) {
      _clusterUniqueId = pClusterUniqueId;
    }

    public ObjectId getClusterUniqueId() {
      return _clusterUniqueId;
    }

    public BasicDBObject toDBObject() {
      return new BasicDBObject(FieldDefs.CLUSTER_UNIQUE_ID, _clusterUniqueId);
    }

    public static class FieldDefs {

      public static final String CLUSTER_UNIQUE_ID = "clusterUniqueId";
    }

    public boolean equals(final Object pOther) {
      return pOther instanceof ResurrectOptions
          && getClusterUniqueId().equals(((ResurrectOptions) pOther).getClusterUniqueId());
    }

    public int hashCode() {
      return new HashCodeBuilder().append(getClusterUniqueId()).toHashCode();
    }
  }

  public static class OsTunedFileOverrides {

    private final Map<String, List<String>> _shardOverrides;
    private final Map<String, List<String>> _configOverrides;

    public OsTunedFileOverrides(
        final Map<String, List<String>> pShardOverrides,
        final Map<String, List<String>> pConfigOverrides) {
      _shardOverrides = pShardOverrides;
      _configOverrides = pConfigOverrides;
    }

    public OsTunedFileOverrides(final DBObject pDBObject) {
      _shardOverrides =
          Optional.ofNullable(pDBObject.get(FieldDefs.SHARD_OVERRIDES))
              .map(BasicDBObject.class::cast)
              .stream()
              .map(Map::entrySet)
              .flatMap(Collection::stream)
              .collect(
                  Collectors.toMap(
                      Entry::getKey,
                      e ->
                          Stream.of(e.getValue())
                              .map(BasicDBList.class::cast)
                              .flatMap(Collection::stream)
                              .map(String.class::cast)
                              .collect(Collectors.toList())));
      _configOverrides =
          Optional.ofNullable(pDBObject.get(FieldDefs.CONFIG_OVERRIDES))
              .map(BasicDBObject.class::cast)
              .stream()
              .map(Map::entrySet)
              .flatMap(Collection::stream)
              .collect(
                  Collectors.toMap(
                      Entry::getKey,
                      e ->
                          Stream.of(e.getValue())
                              .map(BasicDBList.class::cast)
                              .flatMap(Collection::stream)
                              .map(String.class::cast)
                              .collect(Collectors.toList())));
    }

    public DBObject toDBObject() {
      final BasicDBObject shardOverrides = new BasicDBObject();
      getShardOverrides()
          .forEach(
              (key, value) ->
                  shardOverrides.append(key, value.stream().collect(DbUtils.toBasicDBList())));
      final BasicDBObject configOverrides = new BasicDBObject();
      getConfigOverrides()
          .forEach(
              (key, value) ->
                  configOverrides.append(key, value.stream().collect(DbUtils.toBasicDBList())));
      return new BasicDBObject()
          .append(FieldDefs.SHARD_OVERRIDES, shardOverrides)
          .append(FieldDefs.CONFIG_OVERRIDES, configOverrides);
    }

    public Map<String, List<String>> getShardOverrides() {
      return _shardOverrides;
    }

    public Map<String, List<String>> getConfigOverrides() {
      return _configOverrides;
    }

    @Override
    public int hashCode() {
      return Objects.hash(getShardOverrides(), getConfigOverrides());
    }

    @Override
    public boolean equals(final Object pOther) {
      if (pOther == this) {
        return true;
      }
      if (!(pOther instanceof OsTunedFileOverrides other)) {
        return false;
      }
      return Objects.equals(getShardOverrides(), other.getShardOverrides())
          && Objects.equals(getConfigOverrides(), other.getConfigOverrides());
    }

    public static class FieldDefs {

      public static final String SHARD_OVERRIDES = "shardOverrides";
      public static final String CONFIG_OVERRIDES = "configOverrides";
    }
  }

  public enum RestoreJobType {
    DAEMON,
    SNAPSHOT,
    SERVERLESS
  }

  public enum ClusterTag {
    TTL_MONITOR_OFF,
    DECOMPOSED_SHARDED_CLUSTER,
    REGION_OUTAGE_SIMULATION,
    SHARDED_CLUSTER_REASSEMBLING,
    RUNNING_ONLINE_DB_CHECK
  }

  @Schema(description = "Disk warming mode selection.")
  public enum DiskWarmingMode {
    FULLY_WARMED,
    VISIBLE_EARLIER;

    public boolean isVisibleEarlier() {
      return this == VISIBLE_EARLIER;
    }
  }

  public enum VersionReleaseSystem {
    LTS,
    CONTINUOUS
  }

  public enum ClusterProvisionType {
    REGULAR,
    FAST;

    public boolean hasPrecreatedDNS() {
      return this == FAST;
    }

    public boolean hasPreallocatedCapacity() {
      return this == FAST;
    }
  }

  public enum SwapIpMaintenanceRound {
    FIRST,
    SECOND,
    FINAL;

    /**
     * @return the round that should have been performed immediately preceding {@code this}
     */
    public Optional<SwapIpMaintenanceRound> previous() {
      return switch (this) {
        case FIRST -> Optional.empty();
        case SECOND -> Optional.of(FIRST);
        case FINAL -> Optional.of(SECOND);
      };
    }

    /**
     * @return the round that should be performed immediately following {@code this}
     */
    public Optional<SwapIpMaintenanceRound> next() {
      return switch (this) {
        case FIRST -> Optional.of(SECOND);
        case SECOND -> Optional.of(FINAL);
        case FINAL -> Optional.empty();
      };
    }

    /**
     * @return true if {@code this} is a later round than {@code round}
     */
    public boolean isAfter(SwapIpMaintenanceRound round) {
      return switch (this) {
        case FIRST -> false;
        case SECOND -> round == FIRST;
        case FINAL -> round != FINAL;
      };
    }
  }

  public enum StorageSystem {
    LOCAL,
    DISAGGREGATED_STORAGE;

    /**
     * Map the name of the storage system to its enum.
     *
     * @param pStorageSystemName to filter.
     * @return the {@link StorageSystem} that matches the name or {@link #LOCAL} if none do.
     */
    public static StorageSystem filter(@Nullable String pStorageSystemName) {
      if (pStorageSystemName == null) return LOCAL;
      return pStorageSystemName.equalsIgnoreCase(DISAGGREGATED_STORAGE.name())
          ? DISAGGREGATED_STORAGE
          : LOCAL;
    }
  }

  public boolean isAssociatedWithVercelNative() {
    return _partnerIntegrationsData != null
        && _partnerIntegrationsData.getIntegrationType() == IntegrationType.VERCEL_NATIVE;
  }

  private boolean isDateWithinDuration(@NotNull Date date, @NotNull Duration duration) {
    final Instant givenInstant = date.toInstant();
    final Instant nowInstant = Instant.now();

    // Safe comparison since Instant dates are always in UTC
    final Duration actualDuration = Duration.between(givenInstant, nowInstant).abs();

    return actualDuration.compareTo(duration) <= 0;
  }

  /**
   * Write blocks are issued by mongotune on high disk utilization.
   *
   * @return true if the cluster has writes blocked by mongotune
   */
  public boolean isWriteBlocked() {
    return getMongotuneStatus()
        .map(MongotuneStatus::policies)
        .map(policies -> policies.get(PolicyType.DISK_WRITE_BLOCKING))
        .map(policyStatus -> ((DiskWriteBlockPolicyStatus) policyStatus).isWriteBlocked())
        .orElse(false);
  }
}
