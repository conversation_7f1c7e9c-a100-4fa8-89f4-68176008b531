package com.xgen.cloud.nds.project._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.nin;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;
import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.joinFields;
import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.toBasicDBList;
import static com.xgen.cloud.deployment._public.model.IndexConfig.ACTION_FIELD;
import static com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import static com.xgen.cloud.nds.project._public.model.ClusterDescription.getId;

import com.mongodb.AggregationOptions;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.ReadPreference;
import com.mongodb.WriteResult;
import com.mongodb.client.model.DBCollectionFindOptions;
import com.mongodb.client.model.DBCollectionUpdateOptions;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.cloud.common.driverwrappers._public.legacy.Cursor;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.guice._public.extensions.annotations.ShardCollection;
import com.xgen.cloud.common.metrics._public.annotations.PromMethodMetrics;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.DaoOpType;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.DaoOperation;
import com.xgen.cloud.common.model._public.access.EmployeeAccessGrantType;
import com.xgen.cloud.common.model._public.annotation.MethodCallPromTimed;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.deployment._public.model.IndexConfig;
import com.xgen.cloud.deployment._public.model.IndexConfig.Action;
import com.xgen.cloud.nds.cloudprovider._public.model.AZBalancingRequirement;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.DisaggregatedStorageConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.ShardRegionConfig;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyStatus;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyStatus;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.BumperFileOverride;
import com.xgen.cloud.nds.project._public.model.CheckMetadataConsistency;
import com.xgen.cloud.nds.project._public.model.ClusterConnectionStringConfiguration;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterProvisionType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FixedVersionType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.OsTunedFileOverrides;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.RootCertType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionEligibility;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionOperationOrigin;
import com.xgen.cloud.nds.project._public.model.DedicatedConfigServerReplicationSpec;
import com.xgen.cloud.nds.project._public.model.EmployeeAccessGrant;
import com.xgen.cloud.nds.project._public.model.FleetAttributes;
import com.xgen.cloud.nds.project._public.model.FleetAttributes.FleetAttribute;
import com.xgen.cloud.nds.project._public.model.FlexTenantMigrationState;
import com.xgen.cloud.nds.project._public.model.GeoSharding;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.sampledataset._public.model.SampleDatasetLoadStatus.SampleDataset;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import com.xgen.svc.core.dao.base.BaseDao;
import com.xgen.svc.core.dao.base.MongoIndex;
import com.xgen.svc.core.dao.base.SearchOperator;
import dev.morphia.mapping.Mapper;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Represents the planner's goal state. This state is updated at the start of planning and should
 * not be changed afterward. Changes to the {@code ClusterDescription} should ideally be made via
 * {@code ClusterDescriptionUpdateDao}.
 *
 * <p>Ideally, direct modifications to {@code ClusterDescription} should be avoided because:
 *
 * <ul>
 *   <li>The planner might be in the middle of a plan and not take the change into account.
 *   <li>Staged changes might overwrite it when the update is merged into the existing config.
 * </ul>
 *
 * <p>Note that the concept of a fixed goal state during planning and execution does not necessarily
 * apply to other goal state documents.
 *
 * <p>It is encouraged to continue using this class for the methods listed here instead of relying
 * on subclasses unless necessary.
 */
@Singleton
public class ClusterDescriptionDao extends BaseDao {
  private static final Logger LOG = LoggerFactory.getLogger(ClusterDescriptionDao.class);
  private static final boolean IS_COLLECTION_SHARDED = true;
  public static final String DATABASE_NAME = "nds";
  public static final String COLLECTION_NAME = "config.nds.clusterDescriptions";
  public static final String METHOD_CALL_DURATION =
      "mms_nds_cluster_description_dao_method_invocation_duration_seconds";
  public static final String METHOD_CALL_DURATION_DESCRIPTION =
      "run time for ClusterDescriptionDao method";
  private static final Mapper _morphiaMapper = new Mapper();
  private final AppSettings _appSettings;
  private final boolean _isCollectionSharded;
  private static final String CSRS_ARRAY_FILTER_IDENTIFIER = "csrs";

  // Due to these fields not existing in HardwareSpec.java we define them here
  private static final String HARDWARE_SPEC_INSTANCE_FAMILY_FIELD = "instanceFamily";
  private static final String HARDWARE_SPEC_OS_FIELD = "os";

  @Inject
  public ClusterDescriptionDao(final MongoSvc pMongoSvc, final AppSettings pAppSettings) {
    this(pMongoSvc, DATABASE_NAME, COLLECTION_NAME, pAppSettings, IS_COLLECTION_SHARDED);
  }

  protected ClusterDescriptionDao(
      final MongoSvc pMongoSvc,
      final String pDatabase,
      final String pCollection,
      final AppSettings pAppSettings,
      final boolean pIsCollectionSharded) {
    super(pMongoSvc, pDatabase, pCollection);
    _appSettings = pAppSettings;
    _isCollectionSharded = pIsCollectionSharded;
  }

  @ShardCollection
  public void shardCollection() {
    if (_isCollectionSharded) {
      final int numInitialChunks = 0;
      final boolean unique = false;
      final boolean createShardKeyIndex = true;
      final BasicDBObject shardKey = new BasicDBObject("_id.groupId", "hashed");
      ensureSharded(LOG, shardKey, numInitialChunks, unique, createShardKeyIndex);
    }
  }

  @Override
  public List<MongoIndex> getIndexes() {
    final List<MongoIndex> list = super.getIndexes();
    list.add(new MongoIndex(this).key(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), 1));
    list.add(new MongoIndex(this).key(joinFields(FieldDefs.ID, FieldDefs.NAME), 1));
    list.add(new MongoIndex(this).key(joinFields(FieldDefs.STATE), 1));
    list.add(new MongoIndex(this).key(joinFields(FieldDefs.UNIQUE_ID), 1));
    list.add(
        new MongoIndex(this)
            .key(joinFields(FieldDefs.FIXED_ACME_PROVIDER, FixedVersion.FieldDefs.VERSION), 1)
            .sparse());
    list.add(
        new MongoIndex(this)
            .key(joinFields(FieldDefs.FIXED_MONGODB_VERSION, FixedVersion.FieldDefs.VERSION), 1)
            .sparse());
    list.add(
        new MongoIndex(this)
            .key(joinFields(FieldDefs.FIXED_CPU_ARCH, FixedVersion.FieldDefs.VERSION), 1)
            .sparse());
    list.add(
        new MongoIndex(this)
            .key(
                joinFields(
                    FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION, FixedVersion.FieldDefs.VERSION),
                1)
            .sparse());
    list.add(
        new MongoIndex(this)
            .key(joinFields(FieldDefs.FIXED_OS, FixedVersion.FieldDefs.VERSION), 1)
            .sparse());

    return list;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void save(final ClusterDescription pClusterDescription) {
    // Track as update since we always include _id (ClusterDescription _id is not just an ObjectId).

    // https://docs.mongodb.com/manual/reference/method/db.collection.save/#update
    // "If the document contains an _id field, then the save() method is equivalent to an update
    // with
    // the upsert option set to true and the query predicate on the _id field."
    saveMajority(pClusterDescription.toDBObject());
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.DELETE)
  public void remove(final ObjectId pGroupId, final String pClusterName) {
    removeMajority(new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setMigrateFromAvailabilitySets(
      final ObjectId pGroupId,
      final String pClusterName,
      final Set<String> pExistingAvailabilitySetNames) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject(
                FieldDefs.MIGRATE_FROM_AVAILABILITY_SETS,
                pExistingAvailabilitySetNames.stream().collect(DbUtils.toBasicDBList()))));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.DELETE)
  public boolean markDeleted(final ObjectId pGroupId, final String pClusterName, final Date pNow) {
    final WriteResult result =
        updateOneMajority(
            new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName))
                // ensures that the cluster won't be marked deleted a second time (and hence change
                // its deletedDate)
                .append(FieldDefs.STATE, new BasicDBObject(NE, State.DELETED.name())),
            new BasicDBObject(
                SET,
                new BasicDBObject(FieldDefs.STATE, State.DELETED.name())
                    .append(FieldDefs.DELETED_DATE, pNow)
                    .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, null)
                    .append(FieldDefs.ENSURE_CLUSTER_CONNECTIVITY_AFTER, null)));
    return result.getN() == 1;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setState(
      final ObjectId pGroupId, final String pClusterName, final ClusterDescription.State pState) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(SET, new BasicDBObject(FieldDefs.STATE, pState.name())));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setDiskEnableBackupPit(final ObjectId pGroupId, final String pClusterName) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.DISK_BACKUP_ENABLED, true)
                .append(FieldDefs.PIT_ENABLED, true)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setMongoDBUriHosts(
      final ObjectId pGroupId,
      final String pClusterName,
      final String[] pOldMongoDBUriHosts,
      final String[] pNewMongoDBUriHosts) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(
                FieldDefs.MONGODB_URI_HOSTS,
                Arrays.stream(pOldMongoDBUriHosts).collect(DbUtils.toBasicDBList())),
        new BasicDBObject(
            SET,
            new BasicDBObject(
                    FieldDefs.MONGODB_URI_HOSTS,
                    Arrays.stream(pNewMongoDBUriHosts).collect(DbUtils.toBasicDBList()))
                .append(FieldDefs.MONGODB_URI_LAST_UPDATE_DATE, new Date())));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setClusterProvisionType(
      final ObjectId pGroupId,
      final String pClusterName,
      final ClusterProvisionType pClusterProvisionType) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET, new BasicDBObject(FieldDefs.CLUSTER_PROVISION_TYPE, pClusterProvisionType)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setSampleDataLoadProperties(
      final ObjectId pGroupId,
      final String pClusterName,
      final Date pDate,
      final SampleDataset pSampleDataset) {
    final String sampleDatasetName = pSampleDataset != null ? pSampleDataset.name() : null;
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER, pDate)
                .append(FieldDefs.SAMPLE_DATASET_TO_LOAD, sampleDatasetName)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public Date setReplicaSetVersionOverride(
      final ObjectId pGroupId, final String pClusterName, final Integer pVersion) {
    final Date currTime = new Date();
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.REPLICA_SET_VERSION_OVERRIDE, pVersion)
                .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, currTime)));

    return currTime;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setRootCertType(
      final ObjectId pGroupId, final String pClusterName, final RootCertType pCertType) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET, new BasicDBObject().append(FieldDefs.ROOT_CERT_TYPE, pCertType.name())));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateMongotuneStatus(
      final ObjectId pGroupId,
      final String pClusterName,
      @Nullable final String pVersion,
      @Nullable final String pState,
      @Nullable final Map<PolicyType, PolicyStatus> policies) {
    final BasicDBObject updates = new BasicDBObject();
    if (pVersion != null) {
      updates.append(joinFields(FieldDefs.MONGOTUNE, MongotuneStatus.FieldDefs.VERSION), pVersion);
    }
    if (pState != null) {
      updates.append(joinFields(FieldDefs.MONGOTUNE, MongotuneStatus.FieldDefs.STATE), pState);
    }
    if (policies != null) {
      updates.append(
          joinFields(FieldDefs.MONGOTUNE, MongotuneStatus.FieldDefs.POLICIES),
          new BasicDBObject(
              policies.entrySet().stream()
                  .collect(
                      Collectors.toMap(
                          e -> e.getKey().getDbKey(), e -> e.getValue().toDBObject()))));
    }

    if (updates.isEmpty()) {
      return;
    }
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(SET, updates));
  }

  // returns whether writeBlocked has been set
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean setDiskWriteBlockStatus(
      final ObjectId pGroupId, final String pClusterName, final boolean pShouldBlockWrites) {
    final String writeBlockedPath =
        joinFields(
            FieldDefs.MONGOTUNE,
            MongotuneStatus.FieldDefs.POLICIES,
            PolicyType.DISK_WRITE_BLOCKING.getDbKey(),
            DiskWriteBlockPolicyStatus.FieldDefs.WRITE_BLOCKED);

    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(writeBlockedPath, new BasicDBObject(NE, pShouldBlockWrites));

    final BasicDBObject update =
        new BasicDBObject(SET, new BasicDBObject(writeBlockedPath, pShouldBlockWrites));

    return updateOneMajority(query, update).isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateMongoDBVersion(
      final ObjectId pGroupId, final String pClusterName, final String pVersion) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(SET, new BasicDBObject(FieldDefs.MONGODB_VERSION, pVersion)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateMongoDBMajorAndFullVersions(
      final ObjectId pGroupId, final String pClusterName, final VersionUtils.Version pVersion) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.MONGODB_MAJOR_VERSION, pVersion.getMajorVersionString())
                .append(FieldDefs.MONGODB_VERSION, pVersion.getVersion())));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateContinuousDeliveryFCV(
      final ObjectId pGroupId, final String pClusterName, final String pVersion) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(SET, new BasicDBObject(FieldDefs.CONTINUOUS_DELIVERY_FCV, pVersion)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateMongoDBMajorVersion(
      final ObjectId pGroupId, final String pClusterName, final String pVersion) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(SET, new BasicDBObject(FieldDefs.MONGODB_MAJOR_VERSION, pVersion)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void convertToSelfManagedGeoSharding(final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject updates =
        new BasicDBObject()
            .append(FieldDefs.CLUSTER_TYPE, ClusterDescription.ClusterType.GEOSHARDED.name())
            .append(
                joinFields(FieldDefs.GEO_SHARDING, GeoSharding.FieldDefs.SELF_MANAGED_SHARDING),
                true)
            .append(
                joinFields(FieldDefs.GEO_SHARDING, GeoSharding.FieldDefs.CUSTOM_ZONE_MAPPING),
                new BasicDBObject())
            .append(
                joinFields(FieldDefs.GEO_SHARDING, GeoSharding.FieldDefs.MANAGED_NAMESPACES),
                new BasicDBList());

    updateOneMajority(query, new BasicDBObject(SET, updates));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateBiConnector(
      final ObjectId pGroupId,
      final String pClusterName,
      final ClusterDescription.BiConnector pBiConnector) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET, new BasicDBObject(FieldDefs.BI_CONNECTOR, pBiConnector.toDBObject())));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setBiConnectorNeedsSync(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject(
                DbUtils.joinFields(
                    FieldDefs.BI_CONNECTOR, ClusterDescription.BiConnector.FieldDefs.NEEDS_SYNC),
                pDate)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BasicDBObject> findFixedVersionClusters() {
    return DbUtils.toList(
        getDbCollection()
            .find(
                new BasicDBObject()
                    .append(
                        joinFields(FieldDefs.FIXED_MONGODB_VERSION, FixedVersion.FieldDefs.VERSION),
                        new BasicDBObject(NE, null))));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean fixACMEProvider(
      final ObjectId pGroupId, final String pClusterName, final FixedVersion pFixedVersion) {
    final BasicDBObject update =
        new BasicDBObject().append(FieldDefs.FIXED_ACME_PROVIDER, pFixedVersion.toDBObject());
    return updateOneMajority(
            new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
            new BasicDBObject(SET, update))
        .isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean unfixACMEProvider(final ObjectId pGroupId, final String pClusterName) {
    return updateOneMajority(
            new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
            new BasicDBObject(SET, new BasicDBObject(FieldDefs.FIXED_ACME_PROVIDER, null)))
        .isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public WriteResult setGTSRolloutFlag(
      final ObjectId pGroupId, final String pClusterName, final boolean pGTSRolloutFlag) {
    return updateOne(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(SET, new BasicDBObject(FieldDefs.GTS_ROLLOUT, pGTSRolloutFlag)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean fixMongoDBVersion(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pMajorVersion,
      final String pVersion,
      final String pReason,
      final boolean pFactorInVersionReleaseSystem,
      @Nullable final Date pExpiration,
      @Nullable final FixedVersion.FixedBy pFixedBy) {
    final BasicDBObject update =
        new BasicDBObject()
            .append(FieldDefs.MONGODB_MAJOR_VERSION, pMajorVersion)
            .append(FieldDefs.MONGODB_VERSION, pVersion);

    final FixedVersion fixedMongoDBVersion =
        new FixedVersion(pVersion, pReason, new Date(), pExpiration, pFixedBy);
    update.append(FieldDefs.FIXED_MONGODB_VERSION, fixedMongoDBVersion.toDBObject());

    if (pFactorInVersionReleaseSystem) {
      final boolean fixedVersionIsLTS = VersionUtils.parse(pVersion).isLTSRelease();

      // Retrieving the existing cluster description to determine if the version release system
      // needs to be updated
      // Note: It would be possible to avoid this using $cond but is not worth the complexity of
      // using MQL to do string matching
      final Optional<ClusterDescription> cd = findByName(pGroupId, pClusterName);
      if (cd.isEmpty()) {
        return false;
      }

      // Only update the version release system if we are moving across the LTS Boundary
      // Example: Moving from 8.0.X to 8.1.x (or the opposite) should update the release system
      // Example: Moving from 8.0.X to 8.0.Y should not update the version release system
      if (fixedVersionIsLTS != cd.get().getMongoDBVersion().isLTSRelease()) {
        final VersionReleaseSystem versionReleaseSystem =
            VersionUtils.parse(pVersion).isLTSRelease()
                ? VersionReleaseSystem.LTS
                : VersionReleaseSystem.CONTINUOUS;
        update.append(FieldDefs.VERSION_RELEASE_SYSTEM, versionReleaseSystem.name());
      }
    }
    return updateOneMajority(
            new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
            new BasicDBObject(SET, update))
        .isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean unfixMongoDBVersion(final ObjectId pGroupId, final String pClusterName) {
    return updateOneMajority(
            new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
            new BasicDBObject(SET, new BasicDBObject(FieldDefs.FIXED_MONGODB_VERSION, null)))
        .isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean fixMongoDBFCVForCluster(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pMajorVersion,
      final String pReason,
      @Nullable final Date pExpirationDate,
      final Date pPinnedDate,
      @Nullable final FixedVersion.FixedBy pFixedBy) {
    final FixedVersion fixedFeatureCompatibilityVersion =
        new FixedVersion(pMajorVersion, pReason, pPinnedDate, pExpirationDate, pFixedBy);
    return updateOneMajority(
            new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
            new BasicDBObject(
                SET,
                new BasicDBObject(
                    FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION,
                    fixedFeatureCompatibilityVersion.toDBObject())))
        .isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean unfixMongoDBFCVForCluster(final ObjectId pGroupId, final String pClusterName) {
    return updateOneMajority(
            new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
            new BasicDBObject(
                SET, new BasicDBObject(FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION, null)))
        .isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BasicDBObject> findFixedMongoDBFCVClusters() {
    return DbUtils.toList(
        getDbCollection()
            .find(
                new BasicDBObject(
                    joinFields(
                        FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION,
                        FixedVersion.FieldDefs.VERSION),
                    new BasicDBObject(NE, null))));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean fixCpuArchForCluster(
      final ObjectId pGroupId,
      final String pClusterName,
      final CloudProvider pCloudProvider,
      final CpuArchitecture pPreferredCpuArch,
      final Map<ObjectId, Map<NodeType, Pair<InstanceFamily, OS>>>
          pReplicationSpecIdToInstanceFamilyMap,
      final Optional<InstanceFamily> pDedicatedConfigReplicasetInstanceFamily,
      final Optional<OS> pDedicatedConfigOS,
      final String pReason,
      @Nullable final Date pExpirationDate,
      @Nullable final FixedVersion.FixedBy pFixedBy) {
    final DBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject updates = new BasicDBObject();
    final FixedVersion fixedCpuArch =
        new FixedVersion(pPreferredCpuArch.name(), pReason, new Date(), pExpirationDate, pFixedBy);
    updates.append(FieldDefs.FIXED_CPU_ARCH, fixedCpuArch.toDBObject());

    final List<Pair<String, NodeType>> hardwareSpecFields =
        List.of(
            Pair.of(RegionConfig.FieldDefs.ELECTABLE_SPECS, NodeType.ELECTABLE),
            Pair.of(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS, NodeType.ANALYTICS),
            Pair.of(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS, NodeType.READ_ONLY),
            Pair.of(ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS, NodeType.HIDDEN_SECONDARY));

    final List<BasicDBObject> arrayFilters = new ArrayList<>();
    arrayFilters.add(
        new BasicDBObject(
            joinFields("rc", RegionConfig.FieldDefs.CLOUD_PROVIDER), pCloudProvider.name()));

    final List<ObjectId> replicationSpecIdList =
        pReplicationSpecIdToInstanceFamilyMap.keySet().stream().toList();
    IntStream.range(0, pReplicationSpecIdToInstanceFamilyMap.size())
        .forEach(
            i -> {
              final String rsIdIdentifier = String.format("replicationSpec%s", i);
              hardwareSpecFields.forEach(
                  hardwareSpecField -> {
                    final String hardwareSpecFieldName = hardwareSpecField.getLeft();
                    final NodeType nodeType = hardwareSpecField.getRight();
                    final Pair<InstanceFamily, OS> instanceFamilyOSPair =
                        pReplicationSpecIdToInstanceFamilyMap
                            .get(replicationSpecIdList.get(i))
                            .get(nodeType);
                    updates.append(
                        joinFields(
                            FieldDefs.REPLICATION_SPEC_LIST,
                            "$[" + rsIdIdentifier + "]",
                            ReplicationSpec.FieldDefs.REGION_CONFIGS,
                            "$[rc]",
                            hardwareSpecFieldName,
                            "preferredCpuArch"),
                        pPreferredCpuArch.name());
                    updates.append(
                        joinFields(
                            FieldDefs.REPLICATION_SPEC_LIST,
                            "$[" + rsIdIdentifier + "]",
                            ReplicationSpec.FieldDefs.REGION_CONFIGS,
                            "$[rc]",
                            hardwareSpecFieldName,
                            HARDWARE_SPEC_INSTANCE_FAMILY_FIELD),
                        instanceFamilyOSPair.getLeft().getName());
                    updates.append(
                        joinFields(
                            FieldDefs.REPLICATION_SPEC_LIST,
                            "$[" + rsIdIdentifier + "]",
                            ReplicationSpec.FieldDefs.REGION_CONFIGS,
                            "$[rc]",
                            hardwareSpecFieldName,
                            HARDWARE_SPEC_OS_FIELD),
                        instanceFamilyOSPair.getRight().name());
                  });
              arrayFilters.add(
                  new BasicDBObject(
                      String.format("%s.id", rsIdIdentifier), replicationSpecIdList.get(i)));
            });

    if (pDedicatedConfigReplicasetInstanceFamily.isPresent()) {
      updates.append(
          joinFields(
              ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
              ReplicationSpec.FieldDefs.REGION_CONFIGS,
              "$[rc]",
              RegionConfig.FieldDefs.ELECTABLE_SPECS,
              "preferredCpuArch"),
          pPreferredCpuArch.name());
      updates.append(
          joinFields(
              ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
              ReplicationSpec.FieldDefs.REGION_CONFIGS,
              "$[rc]",
              RegionConfig.FieldDefs.ELECTABLE_SPECS,
              ShardedClusterDescription.CONFIG_INSTANCE_FAMILY),
          pDedicatedConfigReplicasetInstanceFamily.get().getName());
      updates.append(
          joinFields(
              ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
              ReplicationSpec.FieldDefs.REGION_CONFIGS,
              "$[rc]",
              RegionConfig.FieldDefs.ELECTABLE_SPECS,
              HARDWARE_SPEC_OS_FIELD),
          pDedicatedConfigOS.get().name());
    }

    final DBCollectionUpdateOptions dbUpdateOptions =
        new DBCollectionUpdateOptions()
            .arrayFilters(arrayFilters)
            .upsert(false)
            .writeConcern(getMajorityWriteConcern());

    return updateWithOptions(
            query, new BasicDBObject().append(BaseDao.SET, updates), dbUpdateOptions)
        .isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean updateFixedVersion(
      final ObjectId pGroupId,
      final String pClusterName,
      final FixedVersionType pType,
      final FixedVersion updatedFixedVersion) {
    final DBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject update =
        new BasicDBObject(
            SET, new BasicDBObject(pType.getFieldName(), updatedFixedVersion.toDBObject()));

    return updateOneMajority(query, update).isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean unfixCpuArchForCluster(final ObjectId pGroupId, final String pClusterName) {
    final DBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject update = new BasicDBObject(FieldDefs.FIXED_CPU_ARCH, null);

    return updateOneMajority(query, new BasicDBObject(SET, update)).isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BasicDBObject> findFixedCpuArchClusters() {
    return DbUtils.toList(
        getDbCollection()
            .find(
                new BasicDBObject(
                    joinFields(FieldDefs.FIXED_CPU_ARCH, FixedVersion.FieldDefs.VERSION),
                    new BasicDBObject(NE, null))));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean unfixOsForCluster(final ObjectId pGroupId, final String pClusterName) {
    final DBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject update = new BasicDBObject(FieldDefs.FIXED_OS, null);

    return updateOneMajority(query, new BasicDBObject(SET, update)).isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BasicDBObject> findFixedOsClusters() {
    return DbUtils.toList(
        getDbCollection()
            .find(
                new BasicDBObject(
                    joinFields(FieldDefs.FIXED_OS, FixedVersion.FieldDefs.VERSION),
                    new BasicDBObject(NE, null))));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BasicDBObject> findFixedACMEProvidersClusters() {
    return DbUtils.toList(
        getDbCollection()
            .find(
                new BasicDBObject(
                    joinFields(FieldDefs.FIXED_ACME_PROVIDER, FixedVersion.FieldDefs.VERSION),
                    new BasicDBObject(NE, null))));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setOSForCluster(
      final ObjectId pGroupId,
      final String pClusterName,
      final boolean pUpdateDedicatedCSRS,
      final CloudProvider pCloudProvider,
      final InstanceFamily pInstanceFamily,
      final OS pOS) {
    final DBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject updates = new BasicDBObject();

    final List<String> hardwareSpecFields =
        List.of(
            RegionConfig.FieldDefs.ELECTABLE_SPECS,
            ShardRegionConfig.FieldDefs.ANALYTICS_SPECS,
            ShardRegionConfig.FieldDefs.READ_ONLY_SPECS,
            ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS);

    hardwareSpecFields.forEach(
        hardwareSpecField -> {
          updates.append(
              joinFields(
                  FieldDefs.REPLICATION_SPEC_LIST,
                  "$[]",
                  ReplicationSpec.FieldDefs.REGION_CONFIGS,
                  String.format("$[%s%s]", "rc", hardwareSpecField),
                  hardwareSpecField,
                  HARDWARE_SPEC_OS_FIELD),
              pOS.name());
        });

    if (pUpdateDedicatedCSRS) {
      updates.append(
          joinFields(
              ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
              ReplicationSpec.FieldDefs.REGION_CONFIGS,
              "$[crc]",
              RegionConfig.FieldDefs.ELECTABLE_SPECS,
              HARDWARE_SPEC_OS_FIELD),
          pOS.name());
    }

    final List<BasicDBObject> arrayFilters =
        hardwareSpecFields.stream()
            .map(
                hardwareSpecField ->
                    new BasicDBObject(
                            joinFields(
                                String.format("%s%s", "rc", hardwareSpecField),
                                RegionConfig.FieldDefs.CLOUD_PROVIDER),
                            pCloudProvider.name())
                        .append(
                            joinFields(
                                String.format("%s%s", "rc", hardwareSpecField),
                                hardwareSpecField,
                                FieldDefs.INSTANCE_FAMILY),
                            pInstanceFamily.getName()))
            .collect(Collectors.toList());

    if (pUpdateDedicatedCSRS) {
      arrayFilters.add(
          new BasicDBObject(
                  joinFields("crc", RegionConfig.FieldDefs.CLOUD_PROVIDER), pCloudProvider.name())
              .append(
                  joinFields(
                      "crc",
                      RegionConfig.FieldDefs.ELECTABLE_SPECS,
                      ShardedClusterDescription.CONFIG_INSTANCE_FAMILY),
                  pInstanceFamily.getName()));
    }

    final DBCollectionUpdateOptions dbUpdateOptions =
        new DBCollectionUpdateOptions()
            .arrayFilters(arrayFilters)
            .upsert(false)
            .writeConcern(getMajorityWriteConcern());

    updateWithOptions(query, new BasicDBObject().append(BaseDao.SET, updates), dbUpdateOptions);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public WriteResult setInstanceFamilyForCluster(
      final ObjectId pGroupId,
      final String pClusterName,
      final boolean pUpdateDedicatedCSRS,
      final CloudProvider pCloudProviderToUpdate,
      final List<String> pHardwareSpecFields,
      final InstanceFamily pInstanceFamilyToUpdate,
      final InstanceFamily pNewInstanceFamily) {

    // Build update commands
    final BasicDBObject updates = new BasicDBObject();

    pHardwareSpecFields.forEach(
        hardwareSpecName ->
            updates.append(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    "$[]",
                    ReplicationSpec.FieldDefs.REGION_CONFIGS,
                    String.format("$[%s]", hardwareSpecName),
                    hardwareSpecName,
                    HARDWARE_SPEC_INSTANCE_FAMILY_FIELD),
                pNewInstanceFamily));

    if (pUpdateDedicatedCSRS) {
      updates.append(
          joinFields(
              ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
              ReplicationSpec.FieldDefs.REGION_CONFIGS,
              String.format("$[%s]", ClusterDescriptionDao.CSRS_ARRAY_FILTER_IDENTIFIER),
              RegionConfig.FieldDefs.ELECTABLE_SPECS,
              ShardedClusterDescription.CONFIG_INSTANCE_FAMILY),
          pNewInstanceFamily);
    }

    final List<BasicDBObject> arrayFilters = new ArrayList<>();
    pHardwareSpecFields.stream()
        .map(
            hardwareSpecName ->
                new BasicDBObject()
                    .append(
                        joinFields(hardwareSpecName, RegionConfig.FieldDefs.CLOUD_PROVIDER),
                        pCloudProviderToUpdate)
                    .append(
                        joinFields(
                            hardwareSpecName,
                            hardwareSpecName,
                            HARDWARE_SPEC_INSTANCE_FAMILY_FIELD),
                        pInstanceFamilyToUpdate))
        .forEach(arrayFilters::add);

    if (pUpdateDedicatedCSRS) {
      final BasicDBObject filter =
          new BasicDBObject()
              .append(
                  joinFields(CSRS_ARRAY_FILTER_IDENTIFIER, RegionConfig.FieldDefs.CLOUD_PROVIDER),
                  pCloudProviderToUpdate)
              .append(
                  joinFields(
                      CSRS_ARRAY_FILTER_IDENTIFIER,
                      RegionConfig.FieldDefs.ELECTABLE_SPECS,
                      ShardedClusterDescription.CONFIG_INSTANCE_FAMILY),
                  pInstanceFamilyToUpdate);

      arrayFilters.add(filter);
    }

    final BasicDBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject update = new BasicDBObject().append(BaseDao.SET, updates);
    final DBCollectionUpdateOptions dbUpdateOptions =
        new DBCollectionUpdateOptions()
            .arrayFilters(arrayFilters)
            .upsert(false)
            .writeConcern(getMajorityWriteConcern());

    return updateWithOptions(query, update, dbUpdateOptions);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public WriteResult setInstanceFamilyForReplicationSpecId(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<ReplicationSpec> pOldReplicationSpecs,
      final List<ReplicationSpec> pNewReplicationSpecs)
      throws StaleUpdateException {

    // Build update commands
    final BasicDBObject updates =
        new BasicDBObject()
            .append(
                FieldDefs.REPLICATION_SPEC_LIST, ReplicationSpec.toDBList(pNewReplicationSpecs));

    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(
                FieldDefs.REPLICATION_SPEC_LIST, ReplicationSpec.toDBList(pOldReplicationSpecs));

    final BasicDBObject update = new BasicDBObject().append(BaseDao.SET, updates);
    final DBCollectionUpdateOptions dbUpdateOptions =
        new DBCollectionUpdateOptions().upsert(false).writeConcern(getMajorityWriteConcern());

    final WriteResult result = updateWithOptions(query, update, dbUpdateOptions);
    if (result.getN() != 1) {
      throw new BaseDao.StaleUpdateException(
          String.format(
              "Unable to update cluster with groupId %s, clusterName %s", pGroupId, pClusterName));
    }
    return result;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public WriteResult updateDedicatedConfigReplicationSpec(
      final ObjectId pGroupId,
      final String pClusterName,
      final DedicatedConfigServerReplicationSpec pOldCSRSSpec,
      final DedicatedConfigServerReplicationSpec pNewCSRSSpec)
      throws StaleUpdateException {

    // Build update commands
    final BasicDBObject updates = new BasicDBObject();

    updates.append(
        ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
        pNewCSRSSpec.toDBObject());

    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(
                ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
                pOldCSRSSpec.toDBObject());
    final BasicDBObject update = new BasicDBObject().append(BaseDao.SET, updates);
    final DBCollectionUpdateOptions dbUpdateOptions =
        new DBCollectionUpdateOptions().upsert(false).writeConcern(getMajorityWriteConcern());

    final WriteResult result = updateWithOptions(query, update, dbUpdateOptions);
    if (result.getN() != 1) {
      throw new BaseDao.StaleUpdateException(
          String.format(
              "Unable to update cluster with groupId %s, clusterName %s, result.getN=%d",
              pGroupId, pClusterName, result.getN()));
    }
    return result;
  }

  public void setAzurePreferredStorageTypeForCluster(
      final ClusterDescription pClusterDescription, final String pStorageType) {
    setAzurePreferredStorageTypeForCluster(
        pClusterDescription.getGroupId(),
        pClusterDescription.getName(),
        shouldUpdateDedicatedConfigServerReplicationSpec(pClusterDescription),
        pStorageType);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setAzurePreferredStorageTypeForCluster(
      final ObjectId pGroupId,
      final String pClusterName,
      final boolean pUpdateDedicatedCSRS,
      final String pStorageType) {
    final DBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject updates = new BasicDBObject();

    final List<String> hardwareSpecFields =
        List.of(
            RegionConfig.FieldDefs.ELECTABLE_SPECS,
            ShardRegionConfig.FieldDefs.ANALYTICS_SPECS,
            ShardRegionConfig.FieldDefs.READ_ONLY_SPECS,
            ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS);

    hardwareSpecFields.forEach(
        hardwareSpecField ->
            updates.append(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    "$[]",
                    ReplicationSpec.FieldDefs.REGION_CONFIGS,
                    String.format("$[%s%s]", "rc", hardwareSpecField),
                    hardwareSpecField,
                    "preferredStorageType"),
                pStorageType));

    if (pUpdateDedicatedCSRS) {
      updates.append(
          joinFields(
              ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
              ReplicationSpec.FieldDefs.REGION_CONFIGS,
              "$[crc]",
              RegionConfig.FieldDefs.ELECTABLE_SPECS,
              "configPreferredStorageType"),
          pStorageType);
    }

    final List<BasicDBObject> arrayFilters =
        hardwareSpecFields.stream()
            .map(
                hardwareSpecField ->
                    new BasicDBObject(
                        joinFields(
                            String.format("%s%s", "rc", hardwareSpecField),
                            RegionConfig.FieldDefs.CLOUD_PROVIDER),
                        CloudProvider.AZURE.name()))
            .collect(Collectors.toList());

    if (pUpdateDedicatedCSRS) {
      arrayFilters.add(
          new BasicDBObject(
              joinFields("crc", RegionConfig.FieldDefs.CLOUD_PROVIDER),
              CloudProvider.AZURE.name()));
    }

    final DBCollectionUpdateOptions dbUpdateOptions =
        new DBCollectionUpdateOptions()
            .arrayFilters(arrayFilters)
            .upsert(false)
            .writeConcern(getMajorityWriteConcern());

    updateWithOptions(query, new BasicDBObject().append(BaseDao.SET, updates), dbUpdateOptions);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setEmployeeAccessGrant(
      final ObjectId pGroupId,
      final String pClusterName,
      final EmployeeAccessGrantType pGrantType,
      final Date pGrantExpiration) {
    final DBObject query = new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));
    final DBObject update =
        new BasicDBObject()
            .append(
                SET,
                new BasicDBObject()
                    .append(
                        FieldDefs.EMPLOYEE_ACCESS_GRANT,
                        new EmployeeAccessGrant(pGrantType, pGrantExpiration)));

    updateOneMajority(query, update);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void revokeEmployeeAccessGrant(final ObjectId pGroupId, final String pClusterName) {
    final DBObject query = new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));
    final DBObject update =
        new BasicDBObject()
            .append(SET, new BasicDBObject().append(FieldDefs.EMPLOYEE_ACCESS_GRANT, null));

    updateOneMajority(query, update);
  }

  public Optional<ClusterDescription> findByName(
      final ObjectId pGroupId, final String pClusterName) {
    return findByName(pGroupId, pClusterName, false);
  }

  @MethodCallPromTimed(
      name = "mms_nds_dao_clusterdescription_findbyname_duration_seconds",
      help = "Duration in seconds to query a single ClusterDescription by groupId and name",
      labelNames = "readpref",
      labelValues = "primary")
  public Optional<ClusterDescription> findByName(
      final ObjectId pGroupId, final String pClusterName, final Boolean pIncludeRecentlyDeleted) {
    return findByName(pGroupId, pClusterName, pIncludeRecentlyDeleted, null);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<ClusterDescription> findByName(
      final ObjectId pGroupId,
      final String pClusterName,
      final Boolean pIncludeRecentlyDeleted,
      final ReadPreference pOptionalReadPreference) {
    final List<State> clusterTypesFilter =
        pIncludeRecentlyDeleted
            ? ClusterDescription.ALL_STATES
            : ClusterDescription.ALL_STATES_EXCEPT_DELETED;
    final BasicDBObject doc =
        findOne(
            new BasicDBObject()
                .append(FieldDefs.ID, getId(pGroupId, pClusterName))
                .append(FieldDefs.STATE, new BasicDBObject(IN, toQueryList(clusterTypesFilter))),
            pOptionalReadPreference);

    return doc != null
        ? Optional.of(ClusterDescription.getCloudProviderClusterDescription(doc))
        : Optional.empty();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<ClusterDescription> findByNameAnyState(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject doc =
        findOne(new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)));

    return Optional.ofNullable(doc).map(ClusterDescription::getCloudProviderClusterDescription);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<ClusterDescription> findByCaseInsensitiveNameAnyState(
      final ObjectId pGroupId, final String pClusterName) {

    final String regex = String.format("^%s$", pClusterName);
    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID + "." + FieldDefs.GROUP_ID, pGroupId)
            .append(
                FieldDefs.ID + "." + FieldDefs.NAME,
                new BasicDBObject("$regex", regex).append("$options", "i"));
    final BasicDBObject doc = findOne(query);

    return Optional.ofNullable(doc).map(ClusterDescription::getCloudProviderClusterDescription);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<ClusterDescription> findByDeploymentClusterName(
      final ObjectId pGroupId,
      final String pDeploymentClusterName,
      final ReadPreference pOptionalReadPreference) {
    final BasicDBObject doc =
        findOne(
            new BasicDBObject()
                .append(DbUtils.joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
                .append(FieldDefs.DEPLOYMENT_CLUSTER_NAME, pDeploymentClusterName)
                .append(
                    FieldDefs.STATE,
                    new BasicDBObject(
                        IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))),
            pOptionalReadPreference);

    return Optional.ofNullable(doc).map(ClusterDescription::getCloudProviderClusterDescription);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<ClusterDescription> findByRegExName(
      final ObjectId pGroupId,
      final String pClusterNameRegEx,
      final String pOptions,
      final ReadPreference pOptionalReadPref) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(
                FieldDefs.ID + "." + FieldDefs.NAME,
                new BasicDBObject("$regex", pClusterNameRegEx).append("$options", pOptions))
            .append(FieldDefs.ID + "." + FieldDefs.GROUP_ID, pGroupId)
            .append(
                FieldDefs.STATE,
                new BasicDBObject(IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED)));
    final BasicDBObject doc = findOne(query, pOptionalReadPref);
    return doc != null
        ? Optional.of(ClusterDescription.getCloudProviderClusterDescription(doc))
        : Optional.empty();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<String> findNameByUniqueId(
      @Nullable final ObjectId pOptionalGroupId, final ObjectId pUniqueId) {
    final BasicDBObject query = new BasicDBObject(FieldDefs.UNIQUE_ID, pUniqueId);
    if (pOptionalGroupId != null) {
      query.append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pOptionalGroupId);
    }

    final BasicDBObject doc = findOne(query, new BasicDBObject(FieldDefs.ID, 1));
    if (doc == null) {
      return Optional.empty();
    }
    final BasicDBObject id = (BasicDBObject) doc.get(FieldDefs.ID);
    return Optional.of(id.getString(FieldDefs.NAME));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Map<ObjectId, String> findNamesByUniqueIds(
      final Collection<Pair<ObjectId, ObjectId>> groupIdToUniqueIdPairs) {

    final Map<ObjectId, String> namesByUniqueId = new HashMap<>();

    if (groupIdToUniqueIdPairs.isEmpty()) {
      return namesByUniqueId;
    }

    final BasicDBList andExpresssions = new BasicDBList();

    groupIdToUniqueIdPairs.stream()
        .map(
            pair ->
                pair.getLeft() != null
                    ? new BasicDBObject()
                        .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pair.getLeft())
                        .append(FieldDefs.UNIQUE_ID, pair.getRight())
                    : new BasicDBObject().append(FieldDefs.UNIQUE_ID, pair.getRight()))
        .forEach(andExpresssions::add);

    final BasicDBObject query = new BasicDBObject(OR, andExpresssions);

    final BasicDBObject projection =
        new BasicDBObject(FieldDefs.NAME, 1).append(FieldDefs.UNIQUE_ID, 1).append(FieldDefs.ID, 1);

    try (final DBCursor cursor =
        getDbCollection()
            .find(query, projection)
            .setReadPreference(DriverUtils.SECONDARY_PREFERRED_MINIMUM)) {
      while (cursor.hasNext()) {
        final BasicDBObject doc = toBasic(cursor.next());
        final ObjectId uniqueId = doc.getObjectId(FieldDefs.UNIQUE_ID);

        final BasicDBObject id = (BasicDBObject) doc.get(FieldDefs.ID);
        String name = id.getString(FieldDefs.NAME);
        if (name != null) {
          namesByUniqueId.put(uniqueId, name);
        }
      }
    }

    return namesByUniqueId;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<ObjectId> findUniqueIdByName(final ObjectId pGroupId, final String pName) {
    final Optional<BasicDBObject> result =
        Optional.ofNullable(
            findOne(
                new BasicDBObject()
                    .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pName)
                    .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId),
                new BasicDBObject(FieldDefs.UNIQUE_ID, 1)));

    return result
        .flatMap(doc -> Optional.ofNullable(doc.get(FieldDefs.UNIQUE_ID)))
        .map(f -> (ObjectId) f);
  }

  public Optional<ClusterDescription> findByUniqueId(
      @Nullable final ObjectId pOptionalGroupId, final ObjectId pUniqueId) {
    return findByUniqueId(pOptionalGroupId, pUniqueId, getDbCollection().getReadPreference());
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<ClusterDescription> findByUniqueId(
      @Nullable final ObjectId pOptionalGroupId,
      final ObjectId pUniqueId,
      final ReadPreference pReadPreference) {
    final BasicDBObject query = new BasicDBObject(FieldDefs.UNIQUE_ID, pUniqueId);

    if (pOptionalGroupId != null) {
      query.append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pOptionalGroupId);
    }

    final BasicDBObject doc = findOne(query, pReadPreference);
    if (doc == null) {
      return Optional.empty();
    }

    return Optional.of(ClusterDescription.getCloudProviderClusterDescription(doc));
  }

  /**
   * Use this method cautiously - ClusterDescriptionDao is sharded on groupId. This will still use
   * the index on uniqueId but will send scatter gather queries.
   */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public DBCursor findActiveCustomerClusterByUniqueIds(final Collection<ObjectId> pUniqueIds) {
    final BasicDBObject query =
        new BasicDBObject(FieldDefs.UNIQUE_ID, new BasicDBObject(IN, pUniqueIds))
            .append(FieldDefs.DELETE_REQUESTED, false)
            .append(FieldDefs.STATE, new BasicDBObject(NE, State.DELETED.name()))
            .append(FieldDefs.IS_MTM, false);

    return getDbCollection().find(query);
  }

  /**
   * Use this method cautiously - ClusterDescriptionDao is sharded on groupId. This will still use
   * the index on uniqueId but will send scatter gather queries.
   */
  public Map<ObjectId, Optional<ClusterDescription>> findByUniqueIds(
      final Collection<ObjectId> pUniqueIds) {
    return findByUniqueIds(pUniqueIds, ReadPreference.primary());
  }

  /**
   * Use this method cautiously - ClusterDescriptionDao is sharded on groupId. This will still use
   * the index on uniqueId but will send scatter gather queries.
   */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Map<ObjectId, Optional<ClusterDescription>> findByUniqueIds(
      final Collection<ObjectId> pUniqueIds, final ReadPreference pReadPreference) {
    if (pUniqueIds.isEmpty()) { // intentionally throw on null pUniqueIds
      return Collections.emptyMap();
    }

    final BasicDBObject query =
        new BasicDBObject(FieldDefs.UNIQUE_ID, new BasicDBObject(IN, pUniqueIds));

    final Map<ObjectId, Optional<ClusterDescription>> descriptionsByUniqueId = new HashMap<>();
    try (final DBCursor cursor = getDbCollection().find(query).setReadPreference(pReadPreference)) {
      while (cursor.hasNext()) {
        final BasicDBObject doc = toBasic(cursor.next());

        final ObjectId uniqueId = doc.getObjectId(FieldDefs.UNIQUE_ID);
        descriptionsByUniqueId.put(
            uniqueId, Optional.of(ClusterDescription.getCloudProviderClusterDescription(doc)));
      }
    }

    // Ensure every provided uniqueId has an Optional value in the returned Map.
    for (final ObjectId uniqueId : pUniqueIds) {
      descriptionsByUniqueId.putIfAbsent(uniqueId, Optional.empty());
    }

    return descriptionsByUniqueId;
  }

  /**
   * Use this method cautiously - ClusterDescriptionDao is sharded on groupId. This will still use
   * the index on uniqueId but will send scatter gather queries.
   */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Map<ObjectId, Optional<ClusterDescriptionId>> findClusterDescriptionIdsByUniqueIds(
      final Collection<ObjectId> pUniqueIds) {
    if (pUniqueIds.isEmpty()) { // intentionally throw on null pUniqueIds
      return Collections.emptyMap();
    }

    final BasicDBObject query =
        new BasicDBObject(FieldDefs.UNIQUE_ID, new BasicDBObject(IN, pUniqueIds));

    final BasicDBObject projection =
        new BasicDBObject()
            .append(FieldDefs.UNIQUE_ID, 1)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), 1)
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), 1);

    final Map<ObjectId, Optional<ClusterDescriptionId>> clusterIdsByUniqueId = new HashMap<>();
    try (final DBCursor cursor = getDbCollection().find(query, projection)) {
      while (cursor.hasNext()) {
        final BasicDBObject doc = toBasic(cursor.next());

        final ObjectId uniqueId = doc.getObjectId(FieldDefs.UNIQUE_ID);
        final BasicDBObject id = (BasicDBObject) doc.get(FieldDefs.ID);
        clusterIdsByUniqueId.put(
            uniqueId,
            Optional.of(
                new ClusterDescriptionId(
                    id.getString(FieldDefs.NAME), id.getObjectId(FieldDefs.GROUP_ID))));
      }
    }

    // Ensure every provided uniqueId has an Optional value in the returned Map.
    for (final ObjectId uniqueId : pUniqueIds) {
      clusterIdsByUniqueId.putIfAbsent(uniqueId, Optional.empty());
    }

    return clusterIdsByUniqueId;
  }

  public List<ClusterDescription> findByIds(final Collection<DBObject> pIds) {
    return findByIds(pIds, ReadPreference.primary());
  }

  public List<ClusterDescription> findByClusterDescriptionIds(
      final Collection<ClusterDescriptionId> pIds) {
    return findByIds(
        pIds.stream()
            .map(id -> getId(id.getGroupId(), id.getClusterName()))
            .collect(Collectors.toList()));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<ClusterDescription> findByIds(
      final Collection<DBObject> pIds, final ReadPreference pReadPreference) {
    // pass pIds through getId() so that the field order is correct
    final Collection<DBObject> ids =
        pIds.stream()
            .map(i -> getId((ObjectId) i.get(FieldDefs.GROUP_ID), (String) i.get(FieldDefs.NAME)))
            .collect(Collectors.toList());
    try (final DBCursor cursor =
        getDbCollection()
            .find(
                new BasicDBObject(FieldDefs.ID, new BasicDBObject(IN, ids))
                    .append(
                        FieldDefs.STATE,
                        new BasicDBObject(
                            IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))),
                new DBCollectionFindOptions().readPreference(pReadPreference))) {
      return cursor.toArray().stream()
          .map(doc -> ClusterDescription.getCloudProviderClusterDescription((BasicDBObject) doc))
          .collect(Collectors.toList());
    }
  }

  public List<ClusterDescription> findByGroup(final ObjectId pGroupId) {
    return findByGroup(pGroupId, ReadPreference.primary());
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<ClusterDescription> findByGroup(
      final ObjectId pGroupId, final ReadPreference pPreference) {
    try (final DBCursor cursor =
        getDbCollection()
            .find(
                new BasicDBObject()
                    .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
                    .append(
                        FieldDefs.STATE,
                        new BasicDBObject(
                            IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))))
            .setReadPreference(pPreference)) {
      return cursor.toArray().stream()
          .map(doc -> ClusterDescription.getCloudProviderClusterDescription((BasicDBObject) doc))
          .collect(Collectors.toList());
    }
  }

  public List<ClusterDescription> findByGroups(final Collection<ObjectId> pGroupIds) {
    return findByGroups(pGroupIds, ReadPreference.primary());
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<ClusterDescription> findByGroups(
      final Collection<ObjectId> pGroupIds, final ReadPreference pReadPreference) {
    try (final DBCursor cursor =
        getDbCollection()
            .find(
                new BasicDBObject(
                        joinFields(FieldDefs.ID, FieldDefs.GROUP_ID),
                        new BasicDBObject(IN, pGroupIds))
                    .append(
                        FieldDefs.STATE,
                        new BasicDBObject(
                            IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))),
                new DBCollectionFindOptions().readPreference(pReadPreference))) {

      return cursor.toArray().stream()
          .map(doc -> ClusterDescription.getCloudProviderClusterDescription((BasicDBObject) doc))
          .collect(Collectors.toList());
    }
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<ClusterDescription> findByNames(
      final ObjectId pGroupId, final List<String> pClusterNames, final ReadPreference pPreference) {
    final List<BasicDBObject> ids =
        pClusterNames.stream().map(cn -> getId(pGroupId, cn)).collect(Collectors.toList());
    final BasicDBObject query =
        new BasicDBObject(FieldDefs.ID, new BasicDBObject(IN, ids))
            .append(
                FieldDefs.STATE,
                new BasicDBObject(IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED)));
    try (final DBCursor cursor = getDbCollection().find(query).setReadPreference(pPreference)) {
      return cursor.toArray().stream()
          .map(doc -> ClusterDescription.getCloudProviderClusterDescription((BasicDBObject) doc))
          .collect(Collectors.toList());
    }
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<ClusterDescription> findDeletedByGroup(
      final ObjectId pGroupId, final ReadPreference pPreference) {
    final BasicDBObject query =
        new BasicDBObject(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(FieldDefs.STATE, State.DELETED.name());
    try (final DBCursor cursor = getDbCollection().find(query).setReadPreference(pPreference)) {

      return cursor.toArray().stream()
          .map(doc -> ClusterDescription.getCloudProviderClusterDescription((BasicDBObject) doc))
          .collect(Collectors.toList());
    }
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public long countByGroup(final ObjectId pGroupId, final boolean pExcludeDeleted) {
    final BasicDBObject query =
        new BasicDBObject().append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId);

    if (pExcludeDeleted) {
      query.append(
          FieldDefs.STATE,
          new BasicDBObject(IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED)));
    }

    return getDbCollection().count(query);
  }

  /** This should *only* be used for testing purposes. */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setCreateDate(
      final ObjectId pGroupId, final String pClusterName, final Date pCreateDate) {
    final DBObject query = new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));
    final DBObject update =
        new BasicDBObject()
            .append(SET, new BasicDBObject().append(FieldDefs.CREATE_DATE, pCreateDate));
    updateOneMajority(query, update);
  }

  /** This should *only* be used for testing purposes. */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setPausedDate(
      final ObjectId pGroupId, final String pClusterName, final Date pPausedDate) {
    final DBObject query = new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));
    final DBObject update =
        new BasicDBObject()
            .append(SET, new BasicDBObject().append(FieldDefs.PAUSED_DATE, pPausedDate));
    updateOneMajority(query, update);
  }

  /** This should *only* be used for testing purposes. */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setLastUpdateDate(
      final ObjectId pGroupId, final String pClusterName, final Date pLastUpdateDate) {
    final DBObject query = new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));
    final DBObject update =
        new BasicDBObject()
            .append(SET, new BasicDBObject().append(FieldDefs.LAST_UPDATE_DATE, pLastUpdateDate));
    updateOneMajority(query, update);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setLoadBalancedHostname(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final String pMeshHostname) {
    final DBObject query = new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));
    final DBObject update =
        new BasicDBObject()
            .append(
                SET,
                new BasicDBObject()
                    .append(FieldDefs.LOAD_BALANCED_HOSTNAME, pHostname)
                    .append(FieldDefs.LOAD_BALANCED_MESH_HOSTNAME, pMeshHostname));
    updateOneMajority(query, update);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<ClusterDescription> findLastUpdatedClusterByGroup(final ObjectId pGroupId) {
    final List<DBObject> docs =
        getDbCollection()
            .find(
                new BasicDBObject()
                    .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
                    .append(
                        FieldDefs.STATE,
                        new BasicDBObject(
                            IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))))
            .sort(new BasicDBObject(FieldDefs.LAST_UPDATE_DATE, -1))
            .limit(1)
            .toArray();
    if (docs.isEmpty()) {
      return Optional.empty();
    }
    return Optional.of(
        ClusterDescription.getCloudProviderClusterDescription((BasicDBObject) docs.get(0)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<ClusterDescription> findForBillingByGroup(
      final ObjectId pGroupId, final Date pStartTime, final Date pEndTime) {
    try (final DBCursor cursor =
        getDbCollection()
            .find(
                new BasicDBObject()
                    .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
                    .append(FieldDefs.CREATE_DATE, new BasicDBObject(LT, pEndTime))
                    .append(
                        OR,
                        Arrays.asList(
                            new BasicDBObject(
                                FieldDefs.STATE,
                                new BasicDBObject(
                                    IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))),
                            new BasicDBObject(
                                FieldDefs.DELETED_DATE, new BasicDBObject(GTE, pStartTime)))))
            .setReadPreference(DriverUtils.SECONDARY_PREFERRED_MINIMUM)) {
      return cursor.toArray().stream()
          .map(doc -> ClusterDescription.getCloudProviderClusterDescription((BasicDBObject) doc))
          .collect(Collectors.toList());
    }
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public boolean hasActiveByGroupId(final ObjectId pGroupId) {
    return getDbCollection()
            .count(
                new BasicDBObject()
                    .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
                    .append(
                        FieldDefs.STATE,
                        new BasicDBObject(
                            IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))))
        > 0;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public boolean hasActiveClustersByGroupIdExcludingPausedFree(final ObjectId pGroupId) {

    // (!FREE || !IS_PAUSED) is logically equivalent to !(FREE && IS_PAUSED)
    final BasicDBList orExpressions = new BasicDBList();
    orExpressions.add(
        new BasicDBObject()
            .append(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    ReplicationSpec.FieldDefs.REGION_CONFIGS,
                    RegionConfig.FieldDefs.CLOUD_PROVIDER),
                new BasicDBObject(BaseDao.NE, CloudProvider.FREE.name())));
    orExpressions.add(new BasicDBObject().append(FieldDefs.IS_PAUSED, false));

    final BasicDBList andExpressions = new BasicDBList();
    andExpressions.add(
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(
                FieldDefs.STATE,
                new BasicDBObject(IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))));
    andExpressions.add(new BasicDBObject().append(BaseDao.OR, orExpressions));

    final DBObject query = new BasicDBObject().append(AND, andExpressions);

    return getDbCollection().count(query) > 0;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<ClusterDescription> findClustersByGroupAfterDeletedDate(
      final ObjectId pGroupId, final Date pDeletedDate) {
    final BasicDBObject query =
        new BasicDBObject(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(
                BaseDao.OR,
                List.of(
                    new BasicDBObject(
                        FieldDefs.STATE,
                        new BasicDBObject(
                            IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))),
                    new BasicDBObject(FieldDefs.STATE, State.DELETED.name())
                        .append(FieldDefs.DELETED_DATE, new BasicDBObject(GTE, pDeletedDate))));
    try (final DBCursor cursor =
        getDbCollection().find(query).setReadPreference(ReadPreference.primary())) {
      return cursor.toArray().stream()
          .map(doc -> ClusterDescription.getCloudProviderClusterDescription((BasicDBObject) doc))
          .collect(Collectors.toList());
    }
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public DBCursor findClusterDescriptionsByBuildVersion(final String pMongoDBBuildVersionName) {
    return getDbCollection()
        .find(
            new BasicDBObject()
                .append(
                    FieldDefs.STATE,
                    new BasicDBObject()
                        .append(IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED)))
                .append(FieldDefs.MONGODB_VERSION, pMongoDBBuildVersionName));
  }

  /**
   * Return a cursor to all clusters that matches a particular mongodb version created before a
   * specified date. This was written solely for the purpose of the fleet data validation project
   * and is not meant to be used for production workload - the query fields are not index and will
   * cause significant load if not used judiciously.
   *
   * <p>See CLOUDP-45062 for more details
   */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public DBCursor findClustersToValidate(
      final String pMongoDBMajorVersion,
      final Date pCreatedBefore,
      final List<CloudProvider> pProviders) {
    final BasicDBObject inProviders =
        new BasicDBObject(
            IN, pProviders.stream().map(CloudProvider::name).collect(Collectors.toList()));
    return getDbCollection()
        .find(
            new BasicDBObject()
                .append(
                    FieldDefs.STATE,
                    new BasicDBObject()
                        .append(IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED)))
                .append(
                    joinFields(
                        FieldDefs.REPLICATION_SPEC_LIST,
                        ReplicationSpec.FieldDefs.REGION_CONFIGS,
                        RegionConfig.FieldDefs.CLOUD_PROVIDER),
                    inProviders)
                .append(FieldDefs.MONGODB_MAJOR_VERSION, pMongoDBMajorVersion)
                .append(FieldDefs.CREATE_DATE, new BasicDBObject(LT, pCreatedBefore))
                .append(
                    FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
                    ClusterDescription.EncryptionAtRestProvider.NONE.name()),
            new BasicDBObject(FieldDefs.ID, 1))
        .sort(new BasicDBObject(joinFields(FieldDefs.ID, FieldDefs.NAME), 1));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BasicDBObject> findByGroupId(final ObjectId pGroupId) {
    return DbUtils.toList(
        getDbCollection()
            .find(new BasicDBObject(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BasicDBObject> findActiveByGroupIds(final Set<ObjectId> pGroupIds) {
    return DbUtils.toList(getDbCollection().find(activeClusterByGroupIdQuery(pGroupIds)));
  }

  private BasicDBObject activeClusterByGroupIdQuery(final Set<ObjectId> pGroupIds) {
    return new BasicDBObject(
            joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), new BasicDBObject(IN, pGroupIds))
        .append(FieldDefs.STATE, new BasicDBObject(NE, State.DELETED.name()));
  }

  public void setForceReplicaSetReconfig(final ObjectId pGroupId, final String pClusterName) {
    setForceReplicaSetReconfig(pGroupId, pClusterName, new Date());
  }

  public void resetForceReplicaSetReconfig(final ObjectId pGroupId, final String pClusterName) {
    setForceReplicaSetReconfig(pGroupId, pClusterName, null);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  private void setForceReplicaSetReconfig(
      final ObjectId pGroupId, final String pClusterName, final Date pForceReplicaSetReconfig) {
    final DBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName);
    final BasicDBObject update =
        new BasicDBObject()
            .append(FieldDefs.FORCE_REPLICA_SET_RECONFIG, pForceReplicaSetReconfig != null)
            .append(FieldDefs.FORCE_REPLICA_SET_RECONFIG_REQUEST_DATE, pForceReplicaSetReconfig);
    if (pForceReplicaSetReconfig == null) {
      update.append(FieldDefs.FORCE_REPLICA_SET_RECONFIG_HOSTS_TO_SKIP, List.of());
    }
    updateOneMajority(query, new BasicDBObject().append(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  private void setDateFieldForClusters(
      final ObjectId pGroupId,
      final Collection<String> pClusterNames,
      final String pField,
      final Date pDate) {
    if (pClusterNames.isEmpty()) {
      return;
    }
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), new BasicDBObject(IN, pClusterNames));
    final BasicDBObject update = new BasicDBObject(pField, pDate);
    updateAllMajority(query, new BasicDBObject(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  private void unsetDateFieldForCluster(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pDateFieldToUnset,
      final Date pOriginalDate) {
    if (pClusterName.isEmpty()) {
      return;
    }
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName)
            .append(pDateFieldToUnset, pOriginalDate);
    final BasicDBObject update = new BasicDBObject(pDateFieldToUnset, null);
    updateOneMajority(query, new BasicDBObject(SET, update));
  }

  public void setEnsureClusterConnectivityAfterForCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    setDateFieldForClusters(
        pGroupId, List.of(pClusterName), FieldDefs.ENSURE_CLUSTER_CONNECTIVITY_AFTER, pDate);
  }

  public void unsetEnsureClusterConnectivityAfterForCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pOriginalDate) {
    unsetDateFieldForCluster(
        pGroupId, pClusterName, FieldDefs.ENSURE_CLUSTER_CONNECTIVITY_AFTER, pOriginalDate);
  }

  public void setNeedsPublishDateForCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    setNeedsPublishDateForClusters(pGroupId, Collections.singletonList(pClusterName), pDate);
  }

  public void setNeedsPublishDateForClusterByUniqueId(
      @Nullable final ObjectId pOptionalGroupId, final ObjectId pUniqueId, final Date pDate) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.UNIQUE_ID, pUniqueId)
            .append(
                FieldDefs.STATE,
                new BasicDBObject(IN, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED)));

    if (pOptionalGroupId != null) {
      query.append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pOptionalGroupId);
    }

    final BasicDBObject update =
        new BasicDBObject(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, pDate);
    updateAllMajority(query, new BasicDBObject(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNeedsPublishWithRestartForCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    final DBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName);
    final DBObject update =
        new BasicDBObject()
            .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, pDate)
            .append(
                FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name());
    updateOneMajority(query, new BasicDBObject().append(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNeedsPublishWithEnvoySyncForServerlessCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    final DBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName)
            .append(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    ReplicationSpec.FieldDefs.REGION_CONFIGS,
                    RegionConfig.FieldDefs.CLOUD_PROVIDER),
                CloudProvider.SERVERLESS.name());

    final DBObject update =
        new BasicDBObject()
            .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, pDate)
            .append(FieldDefs.NEEDS_ENVOY_SYNC_AFTER, pDate);
    updateOneMajority(query, new BasicDBObject().append(SET, update));
  }

  public void setNeedsPublishDateForClusters(
      final ObjectId pGroupId, final Collection<String> pClusterNames, final Date pDate) {
    setDateFieldForClusters(
        pGroupId, pClusterNames, FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, pDate);
  }

  /**
   * Sets the date after which the cluster's mongotune config should be published.
   *
   * @param pGroupId the group ID of the cluster
   * @param pClusterName the name of the cluster
   * @param pDate the date after which the mongotune config should be published
   */
  public void setNeedsMaintainedMongotuneConfigUpdateDateForCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    setDateFieldForClusters(
        pGroupId, List.of(pClusterName), FieldDefs.NEEDS_MONGOTUNE_CONFIG_PUBLISH_AFTER, pDate);
  }

  /**
   * Unsets the date after which the cluster's mongotune config should be published.
   *
   * @param pGroupId the group ID of the cluster
   * @param pClusterName the name of the cluster
   * @param pOriginalDate the original date to unset
   */
  public void unsetNeedsMaintainedMongotuneConfigUpdateDateForCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pOriginalDate) {
    unsetDateFieldForCluster(
        pGroupId, pClusterName, FieldDefs.NEEDS_MONGOTUNE_CONFIG_PUBLISH_AFTER, pOriginalDate);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setOSPolicyVersionAndIsCriticalOSPolicyRelease(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pNewOSPolicyVersion,
      final Boolean pIsCriticalOSPolicyRelease) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName);
    final BasicDBObject update =
        new BasicDBObject()
            .append(FieldDefs.OS_POLICY_VERSION, pNewOSPolicyVersion)
            .append(FieldDefs.IS_CRITICAL_OS_POLICY_RELEASE, pIsCriticalOSPolicyRelease);

    updateOneMajority(query, new BasicDBObject(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNeedsServerlessSnapshotForPit(
      final ObjectId pGroupId, final String pClusterNames, final Date pDate) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterNames);
    final BasicDBObject update =
        new BasicDBObject(FieldDefs.NEEDS_SERVERLESS_SNAPSHOT_FOR_PIT, pDate);
    updateAllMajority(query, new BasicDBObject(SET, update));
  }

  public void unsetNeedsServerlessSnapshotForPit(
      final ObjectId pGroupId, final String pClusterName, final Date pOriginalDate) {
    unsetDateFieldForCluster(
        pGroupId, pClusterName, FieldDefs.NEEDS_SERVERLESS_SNAPSHOT_FOR_PIT, pOriginalDate);
  }

  public void setNeedsPrioritiesResetForPriorityTakeoverAfter(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    setDateFieldForClusters(
        pGroupId,
        List.of(pClusterName),
        FieldDefs.NEEDS_PRIORITIES_RESET_FOR_PRIORITY_TAKEOVER_AFTER,
        pDate);
  }

  public void unsetNeedsPrioritiesResetForPriorityTakeoverAfter(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    unsetDateFieldForCluster(
        pGroupId,
        pClusterName,
        FieldDefs.NEEDS_PRIORITIES_RESET_FOR_PRIORITY_TAKEOVER_AFTER,
        pDate);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNeedsPublishWithRestartForClusters(
      final ObjectId pGroupId, final Collection<String> pClusterNames, final Date pDate) {
    if (pClusterNames.isEmpty()) {
      return;
    }
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), new BasicDBObject(IN, pClusterNames));
    final BasicDBObject update =
        new BasicDBObject(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, pDate)
            .append(
                FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name());
    updateAllMajority(query, new BasicDBObject(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNeedsPublishDateForClustersInGroup(final ObjectId pGroupId) {
    final BasicDBObject query =
        new BasicDBObject().append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId);
    final BasicDBObject update =
        new BasicDBObject().append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, new Date());
    updateAllMajority(query, new BasicDBObject(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNeedsPublishWithRestartForClustersInGroup(final ObjectId pGroupId) {
    final BasicDBObject query =
        new BasicDBObject().append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId);
    final DBObject update =
        new BasicDBObject()
            .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, new Date())
            .append(
                FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name());
    updateAllMajority(query, new BasicDBObject(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNeedsPublishDateForDedicatedClustersInGroup(final ObjectId pGroupId) {
    final DBObject query =
        DbUtils.bsonToDBObject(
            and(
                eq(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId),
                nin(
                    joinFields(
                        FieldDefs.REPLICATION_SPEC_LIST,
                        ReplicationSpec.FieldDefs.REGION_CONFIGS,
                        RegionConfig.FieldDefs.CLOUD_PROVIDER),
                    List.of(
                        CloudProvider.FREE.name(),
                        CloudProvider.SERVERLESS.name(),
                        CloudProvider.FLEX.name()))));
    final DBObject update =
        DbUtils.bsonToDBObject(set(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, new Date()));
    updateAllMajority(query, update);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNeedsPublishWithRestartForActiveDedicatedClustersInGroup(final ObjectId pGroupId) {
    final DBObject query =
        DbUtils.bsonToDBObject(
            and(
                eq(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId),
                in(
                    joinFields(
                        FieldDefs.REPLICATION_SPEC_LIST,
                        ReplicationSpec.FieldDefs.REGION_CONFIGS,
                        RegionConfig.FieldDefs.CLOUD_PROVIDER),
                    List.of(
                        CloudProvider.AWS.name(),
                        CloudProvider.AZURE.name(),
                        CloudProvider.GCP.name())),
                eq(FieldDefs.IS_PAUSED, false),
                in(FieldDefs.STATE, toQueryList(ClusterDescription.ALL_STATES_EXCEPT_DELETED))));
    final DBObject update =
        DbUtils.bsonToDBObject(
            combine(
                set(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, new Date()),
                set(
                    FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                    ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name())));
    updateAllMajority(query, update);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  private void setNeedsPublishDateForTenantClustersInGroup(
      final ObjectId pGroupId, final Set<CloudProvider> pProviders) {
    final BasicDBList providers =
        pProviders.stream().map(CloudProvider::name).collect(toBasicDBList());
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    ReplicationSpec.FieldDefs.REGION_CONFIGS,
                    RegionConfig.FieldDefs.CLOUD_PROVIDER),
                new BasicDBObject(IN, providers));
    final BasicDBObject update =
        new BasicDBObject().append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, new Date());
    updateAllMajority(query, new BasicDBObject(SET, update));
  }

  public void setNeedsPublishDateForTenantClustersInGroup(final ObjectId pGroupId) {
    setNeedsPublishDateForTenantClustersInGroup(
        pGroupId, Set.of(CloudProvider.FREE, CloudProvider.SERVERLESS, CloudProvider.FLEX));
  }

  // Only used in tests. needsMongoDBConfigPublishRestartAllowed should never
  // be updated if needsMongoDBConfigPublishAfter is not also being updated
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNeedsMongoDBConfigPublishRestartAllowedForCluster(
      final ObjectId pGroupId,
      final String pClusterName,
      final ClusterDescription.ProcessRestartAllowedState pRestartAllowed) {
    final DBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName);
    final DBObject update =
        new BasicDBObject(
            FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED, pRestartAllowed.name());
    updateOneMajority(query, new BasicDBObject().append(SET, update));
  }

  public void clearNeedsPublishDateForCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pOriginalDate) {
    unsetDateFieldForCluster(
        pGroupId, pClusterName, FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, pOriginalDate);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void clearNeedsPublishDateAndRestartAllowedForCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pOriginalDate) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName)
            .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, pOriginalDate);
    final BasicDBObject update =
        new BasicDBObject()
            .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, null)
            .append(
                FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                ClusterDescription.ProcessRestartAllowedState.NONE.name());
    updateOneMajority(query, new BasicDBObject(SET, update));
  }

  public void clearNeedsEnvoySyncDateForCluster(
      final ObjectId pGroupId, final String pClusterName, final Date pOriginalDate) {
    unsetDateFieldForCluster(
        pGroupId, pClusterName, FieldDefs.NEEDS_ENVOY_SYNC_AFTER, pOriginalDate);
  }

  public void setBiConnectorNeedsSyncForClusters(
      final ObjectId pGroupId, final Collection<String> pClusterNames, final Date pDate) {
    setDateFieldForClusters(
        pGroupId,
        pClusterNames,
        DbUtils.joinFields(
            FieldDefs.BI_CONNECTOR, ClusterDescription.BiConnector.FieldDefs.NEEDS_SYNC),
        pDate);
  }

  public void clearBiConnectorNeedsSync(
      final ObjectId pGroupId, final String pClusterName, final Date pOriginalDate) {
    final String needsSyncFieldName =
        DbUtils.joinFields(
            FieldDefs.BI_CONNECTOR, ClusterDescription.BiConnector.FieldDefs.NEEDS_SYNC);
    unsetDateFieldForCluster(pGroupId, pClusterName, needsSyncFieldName, pOriginalDate);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateShardsDrainingList(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<String> pUpdatedShardDrainList) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName);

    final BasicDBList updatedShardsDrainingList = new BasicDBList();
    updatedShardsDrainingList.addAll(pUpdatedShardDrainList);

    final BasicDBObject update =
        new BasicDBObject(FieldDefs.SHARDS_DRAINING, updatedShardsDrainingList);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean setCancelShardDrainRequested(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName);
    final BasicDBObject update = new BasicDBObject(FieldDefs.CANCEL_SHARD_DRAIN_REQUESTED, pDate);
    final WriteResult result = updateOneMajority(query, new BasicDBObject(BaseDao.SET, update));
    return result.wasAcknowledged() && result.getN() >= 1;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<ClusterDescription> adminSearch(
      final List<ObjectId> pIds,
      final List<State> pStates,
      final List<String> pNames,
      final SearchOperator pSearchOperator) {
    final ArrayList<BasicDBObject> queryList = new ArrayList<>();

    if (!pNames.isEmpty()) {
      final List<Pattern> namesPatterns = pNames.stream().map(Pattern::compile).toList();
      namesPatterns.stream()
          .map(p -> new BasicDBObject(joinFields(FieldDefs.ID, FieldDefs.NAME), p))
          .forEach(queryList::add);
    }

    // Match the groupId nested in _id or match the uniqueId if we have objectId queries.
    if (!pIds.isEmpty()) {
      queryList.add(
          new BasicDBObject(
              OR,
              Arrays.asList(
                  new BasicDBObject(
                      joinFields(FieldDefs.ID, FieldDefs.GROUP_ID),
                      new BasicDBObject(pSearchOperator.getListOperator(), pIds)),
                  new BasicDBObject(
                      FieldDefs.UNIQUE_ID,
                      new BasicDBObject(pSearchOperator.getListOperator(), pIds)))));
    }

    // Match the state of the cluster if we have any queries that match a value in
    // ClusterDescription.State.
    if (!pStates.isEmpty()) {
      queryList.add(
          new BasicDBObject(
              FieldDefs.STATE,
              new BasicDBObject(
                  pSearchOperator.getListOperator(),
                  pStates.stream().map(Enum::name).collect(Collectors.toSet()))));
    }

    // Nothing to query for, don't waste any database calls.
    if (queryList.isEmpty()) {
      return new ArrayList<>();
    }

    // Combine the constructed queries using the operator specified by the user, either AND or OR.
    final BasicDBObject query = new BasicDBObject(pSearchOperator.getOperator(), queryList);
    final List<BasicDBObject> results =
        // sort by deletedDate so that active clusters show up first (deletedDate == null)
        DbUtils.toList(
            getDbCollection()
                .find(query)
                .sort(new BasicDBObject(FieldDefs.DELETED_DATE, 1))
                .limit(NDSDefaults.ADMIN_SEARCH_LIMIT));

    return results.stream()
        .map(ClusterDescription::getCloudProviderClusterDescription)
        .collect(Collectors.toList());
  }

  public void addPendingIndexes(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<IndexConfig> pPendingIndexConfigs) {
    modifyPendingIndexes(pGroupId, pClusterName, pPendingIndexConfigs, PUSH);
  }

  public void removePendingIndexes(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<IndexConfig> pPendingIndexConfigs) {
    modifyPendingIndexes(pGroupId, pClusterName, pPendingIndexConfigs, PULL);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setPendingIndexActionCancel(
      final ObjectId pGroupId, final String pClusterName, final IndexConfig pIndexConfig) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName)
            .append(
                FieldDefs.PENDING_INDEXES,
                new BasicDBObject(ELEM_MATCH, getMorphia().toDBObject(pIndexConfig)));

    final BasicDBObject update =
        new BasicDBObject(
            joinFields(FieldDefs.PENDING_INDEXES, "$", ACTION_FIELD), Action.CANCEL.name());
    updateOneMajority(query, new BasicDBObject(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void cancelAllPendingIndexes(final ObjectId pGroupId, final String pClusterName) {

    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName);

    final BasicDBObject update =
        new BasicDBObject(
            joinFields(FieldDefs.PENDING_INDEXES, "$[]", ACTION_FIELD), Action.CANCEL.name());
    updateOneMajority(query, new BasicDBObject(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  private void modifyPendingIndexes(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<IndexConfig> pPendingIndexConfigs,
      final String pOperation) {

    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName);

    final BasicDBList pendingIndexesConfigDBList =
        pPendingIndexConfigs.stream()
            .map(_morphiaMapper::toDBObject)
            .collect(DbUtils.toBasicDBList());

    final BasicDBObject update =
        new BasicDBObject(
            FieldDefs.PENDING_INDEXES,
            new BasicDBObject(PUSH.equals(pOperation) ? EACH : IN, pendingIndexesConfigDBList));
    updateOneMajority(query, new BasicDBObject(pOperation, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean updateTenantClusterNdsAccessTemporarilyRevoked(
      final ClusterDescription pClusterDescription, final Boolean pAccessTemporarilyRevoked) {
    final BasicDBObject query =
        new BasicDBObject(
            FieldDefs.ID, getId(pClusterDescription.getGroupId(), pClusterDescription.getName()));

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject(
                joinFields(
                    FieldDefs.CLOUD_PROVIDER_OPTIONS,
                    ClusterDescription.FieldDefs.ACCESS_TEMPORARILY_REVOKED),
                pAccessTemporarilyRevoked));

    return updateOneMajority(query, update).isUpdateOfExisting();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateByLastUpdateDate(final ClusterDescription pClusterDescription)
      throws StaleUpdateException {
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pClusterDescription.getGroupId())
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterDescription.getName())
            .append(FieldDefs.LAST_UPDATE_DATE, pClusterDescription.getLastUpdateDate());

    final WriteResult writeResult =
        updateOneMajority(
            query, pClusterDescription.copy().setLastUpdateDate(new Date()).build().toDBObject());

    if (writeResult.getN() != 1) {
      throw new StaleUpdateException(
          String.format(
              "ClusterDescription in %s with groupId %s and name %s has been updated since %s",
              getCollectionName(),
              pClusterDescription.getGroupId(),
              pClusterDescription.getName(),
              pClusterDescription.getLastUpdateDate().toString()));
    }
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateOsTunedFileOverrides(
      final ObjectId pGroupId,
      final String pClusterName,
      final OsTunedFileOverrides pOsTunedFileOverrides) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(joinFields(FieldDefs.ID, FieldDefs.NAME), pClusterName);
    final BasicDBObject update =
        new BasicDBObject()
            .append(FieldDefs.OS_TUNED_FILE_OVERRIDES, pOsTunedFileOverrides.toDBObject());

    updateOneMajority(query, new BasicDBObject(SET, update));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean updatePrivateEndpointSRVAddressMap(
      final ObjectId pGroupId,
      final String pClusterName,
      final Map<String, String> pPrivateSrvAddressMap) {
    final DBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject update =
        new BasicDBObject(
            SET, new BasicDBObject(FieldDefs.PRIVATE_SRV_ADDRESS_MAP, pPrivateSrvAddressMap));
    return updateOneMajority(query, update).getN() == 1;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean setHasPrivateEndpointLegacyConnectionStrings(
      final ObjectId pGroupId,
      final String pClusterName,
      final boolean pHasPrivateEndpointLegacyConnectionStrings) {
    final DBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));
    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject(
                ShardedClusterDescription.FieldDefs.HAS_PRIVATE_ENDPOINT_LEGACY_CONNECTION_STRINGS,
                pHasPrivateEndpointLegacyConnectionStrings));
    return updateOneMajority(query, update).getN() == 1;
  }

  /*
   Note that the following two methods only set isPaused flag without pausedDate
   that's because we are requesting to pause not indicating pause is completed
  */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean clearPauseRequest(
      final String pClusterName, final ObjectId pGroupId, final Date pLastUpdateDate) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(FieldDefs.LAST_UPDATE_DATE, pLastUpdateDate)
            .append(FieldDefs.IS_PAUSED, true);
    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.IS_PAUSED, false)
                .append(FieldDefs.LAST_UPDATE_DATE, new Date()));

    return updateOneMajority(query, update).getN() == 1;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setPauseRequest(final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(FieldDefs.IS_PAUSED, false);
    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject(FieldDefs.IS_PAUSED, true)
                .append(FieldDefs.LAST_UPDATE_DATE, new Date()));
    updateOneMajority(query, update);
  }

  public void setNeedsDbCheckAfter(
      final ObjectId pGroupId, final String pClusterName, final Date pNeedsDbCheckAfterDate) {
    setDateFieldForClusters(
        pGroupId, List.of(pClusterName), FieldDefs.NEEDS_DB_CHECK_AFTER, pNeedsDbCheckAfterDate);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void resetDbCheckPreflightRetryCount(final ObjectId pGroupId, final String pClusterName) {
    final Date daysUntilNextAutomaticDbCheck =
        Date.from(
            Instant.now()
                .plus(_appSettings.getDbCheckMinDaysBetweenClusterValidations(), ChronoUnit.DAYS));

    final BasicDBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.NEEDS_DB_CHECK_AFTER, daysUntilNextAutomaticDbCheck)
                .append(FieldDefs.DB_CHECK_PREFLIGHT_RETRY_COUNT, 0));
    updateOneMajority(query, update);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void incrementDbCheckPreflightRetryCount(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject query =
        new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject(INC, new BasicDBObject(FieldDefs.DB_CHECK_PREFLIGHT_RETRY_COUNT, 1));
    updateOneMajority(query, update);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void resetCheckMetadataConsistencyPreflightRetryCount(
      final ObjectId pGroupId, final String pClusterName) {
    final Date daysUntilNextAutomaticCheckMetadataConsistency =
        Date.from(
            Instant.now()
                .plus(
                    _appSettings.getCheckMetadataConsistencyMinDaysBetweenClusterValidations(),
                    ChronoUnit.DAYS));

    final BasicDBObject query = new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.NEEDS_CHECK_AFTER),
                    daysUntilNextAutomaticCheckMetadataConsistency)
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.PREFLIGHT_RETRY_COUNT),
                    0));
    updateOneMajority(query, update);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void incrementCheckMetadataConsistencyPreflightRetryCount(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject query =
        new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject(
            INC,
            new BasicDBObject(
                joinFields(
                    ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                    CheckMetadataConsistency.FieldDefs.PREFLIGHT_RETRY_COUNT),
                1));
    updateOneMajority(query, update);
  }

  public void setLastDbCheckDate(
      final ObjectId pGroupId, final String pClusterName, final Date pLastDbCheckDate) {
    setDateFieldForClusters(
        pGroupId, List.of(pClusterName), FieldDefs.LAST_DB_CHECK_DATE, pLastDbCheckDate);
  }

  public void setLastDataValidationDate(
      final ObjectId pGroupId, final String pClusterName, final Date pLastDataValidationDate) {
    setDateFieldForClusters(
        pGroupId,
        List.of(pClusterName),
        FieldDefs.LAST_DATA_VALIDATION_DATE,
        pLastDataValidationDate);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public CorruptionDetectionEligibility countCorruptionDetectionEligibleClusters() {
    final List<DBObject> pipeline = new ArrayList<>();

    final Map<CorruptionDetectionCategory, CorruptionDetectionData> dataByCorruptionDetectionType =
        Map.of(
            CorruptionDetectionCategory.DBCHECK,
            new CorruptionDetectionData(
                FieldDefs.LAST_DB_CHECK_DATE,
                _appSettings.getDbCheckMinDaysBetweenClusterValidations(),
                _appSettings.getDbCheckMinClusterAgeDays()),
            CorruptionDetectionCategory.DATA_VALIDATION,
            new CorruptionDetectionData(
                FieldDefs.LAST_DATA_VALIDATION_DATE,
                _appSettings.getDataValidationMinDaysBetweenClusterValidations(),
                _appSettings.getDataValidationMinClusterAgeDays()),
            CorruptionDetectionCategory.SHARDED_METADATA_CONSISTENCY,
            new CorruptionDetectionData(
                joinFields(
                    ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                    CheckMetadataConsistency.FieldDefs.LAST_CHECK_DATE),
                _appSettings.getCheckMetadataConsistencyMinDaysBetweenClusterValidations(),
                _appSettings.getCheckMetadataConsistencyMinClusterAgeDays()));

    final BasicDBObject matchExp =
        getCorruptionDetectionEligibilityMatchStageQuery(dataByCorruptionDetectionType);

    pipeline.add(new BasicDBObject(MATCH, matchExp));

    final BasicDBObject groupExp =
        getCorruptionDetectionEligibilityGroupStageQuery(dataByCorruptionDetectionType);

    pipeline.add(new BasicDBObject(GROUP, groupExp));

    final Cursor cursor = aggregate(pipeline);
    if (!cursor.hasNext()) {
      return new CorruptionDetectionEligibility(0, 0, 0);
    }

    final BasicDBObject counts = (BasicDBObject) cursor.next();
    return new CorruptionDetectionEligibility(
        counts.getLong(CorruptionDetectionCategory.DBCHECK.name(), 0L),
        counts.getLong(CorruptionDetectionCategory.DATA_VALIDATION.name(), 0L),
        counts.getLong(CorruptionDetectionCategory.SHARDED_METADATA_CONSISTENCY.name(), 0L));
  }

  public void setAutoScalingMode(
      final ObjectId pGroupId,
      final String pClusterName,
      final AutoScalingMode pAutoScalingMode,
      final Date pLastUpdateDate)
      throws StaleUpdateException {

    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(FieldDefs.LAST_UPDATE_DATE, pLastUpdateDate);

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.AUTO_SCALING_MODE, pAutoScalingMode)
                .append(FieldDefs.LAST_UPDATE_DATE, new Date()));

    if (updateOneMajority(query, update).getN() != 1) {
      throw new StaleUpdateException(
          String.format(
              "Attempt to set AutoScalingMode to %s on cluster %s/%s with last update date %s"
                  + " failed to match document",
              pAutoScalingMode, pGroupId, pClusterName, pLastUpdateDate.toInstant().toString()));
    }
  }

  public Cursor sampleClustersForDbCheck(final long pNumberOfClustersToSample) {
    final Map<CorruptionDetectionCategory, CorruptionDetectionData>
        dataByCorruptionDetectionTypeMap =
            Map.of(
                CorruptionDetectionCategory.DBCHECK,
                new CorruptionDetectionData(
                    FieldDefs.LAST_DB_CHECK_DATE,
                    _appSettings.getDbCheckMinDaysBetweenClusterValidations(),
                    _appSettings.getDbCheckMinClusterAgeDays()));

    final BasicDBObject matchQuery =
        getCorruptionDetectionEligibilityMatchStageQuery(dataByCorruptionDetectionTypeMap);
    matchQuery.append(FieldDefs.NEEDS_DB_CHECK_AFTER, null);

    return aggregateClustersForCorruptionDetection(matchQuery, pNumberOfClustersToSample);
  }

  public Cursor sampleClustersForDataValidation(final long pNumberOfClustersToSample) {
    final Map<CorruptionDetectionCategory, CorruptionDetectionData>
        dataByCorruptionDetectionTypeMap =
            Map.of(
                CorruptionDetectionCategory.DATA_VALIDATION,
                new CorruptionDetectionData(
                    FieldDefs.LAST_DATA_VALIDATION_DATE,
                    _appSettings.getDataValidationMinDaysBetweenClusterValidations(),
                    _appSettings.getDataValidationMinClusterAgeDays()));

    final BasicDBObject matchQuery =
        getCorruptionDetectionEligibilityMatchStageQuery(dataByCorruptionDetectionTypeMap);

    matchQuery
        .append(
            FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
            ClusterDescription.EncryptionAtRestProvider.NONE.name())
        .append(
            joinFields(
                FieldDefs.REPLICATION_SPEC_LIST,
                ReplicationSpec.FieldDefs.REGION_CONFIGS,
                RegionConfig.FieldDefs.ELECTABLE_SPECS,
                "instanceSize"),
            new BasicDBObject(REGEXP, "^((?!_NVME).)*$"));

    return aggregateClustersForCorruptionDetection(matchQuery, pNumberOfClustersToSample);
  }

  public Cursor sampleClustersForCheckMetadataConsistency(final long pNumberOfClustersToSample) {
    final Map<CorruptionDetectionCategory, CorruptionDetectionData>
        dataByCorruptionDetectionTypeMap =
            Map.of(
                CorruptionDetectionCategory.SHARDED_METADATA_CONSISTENCY,
                new CorruptionDetectionData(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.LAST_CHECK_DATE),
                    _appSettings.getCheckMetadataConsistencyMinDaysBetweenClusterValidations(),
                    _appSettings.getCheckMetadataConsistencyMinClusterAgeDays()));

    final BasicDBObject matchQuery =
        getCorruptionDetectionEligibilityMatchStageQuery(dataByCorruptionDetectionTypeMap);
    matchQuery
        .append(
            joinFields(
                ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                CheckMetadataConsistency.FieldDefs.NEEDS_CHECK_AFTER),
            null)
        .append(
            ClusterDescription.FieldDefs.CLUSTER_TYPE,
            new BasicDBObject(IN, List.of(ClusterType.SHARDED, ClusterType.GEOSHARDED)));

    // If we are explicitly targeting a certain mongodb version, we should defer to that version.
    if (!matchQuery.containsKey(FieldDefs.MONGODB_VERSION)) {
      matchQuery.append(
          FieldDefs.MONGODB_VERSION,
          new BasicDBObject(GTE, VersionUtils.SEVEN_ZERO_ZERO.toString()));
    }

    return aggregateClustersForCorruptionDetection(matchQuery, pNumberOfClustersToSample);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  private Cursor aggregateClustersForCorruptionDetection(
      final BasicDBObject pMatchQuery, final long pNumberOfClustersToSample) {
    final BasicDBObject match = new BasicDBObject(MATCH, pMatchQuery);
    final BasicDBObject project = new BasicDBObject(PROJECT, new BasicDBObject(ID, 1));

    // MongoDB $sample requires a positive integer size parameter
    if (pNumberOfClustersToSample <= 0) {
      // Return empty cursor when sample size is 0 or negative by adding a match condition that
      // returns no results
      final BasicDBObject emptyMatch =
          new BasicDBObject(MATCH, new BasicDBObject("_id", new BasicDBObject("$exists", false)));
      return aggregate(List.of(match, project, emptyMatch));
    }

    final BasicDBObject sample =
        new BasicDBObject(SAMPLE, new BasicDBObject("size", pNumberOfClustersToSample));

    return aggregate(List.of(match, project, sample));
  }

  private BasicDBObject getCorruptionDetectionEligibilityMatchStageQuery(
      final Map<CorruptionDetectionCategory, CorruptionDetectionData>
          pDataByCorruptionDetectionTypeMap) {
    final BasicDBList tenantCloudProvider = new BasicDBList();
    tenantCloudProvider.add(CloudProvider.FREE.name());
    tenantCloudProvider.add(CloudProvider.SERVERLESS.name());
    tenantCloudProvider.add(CloudProvider.FLEX.name());

    final int minClusterAgeDays =
        pDataByCorruptionDetectionTypeMap.values().stream()
            .map(CorruptionDetectionData::minClusterAgeDays)
            .min(Integer::compare)
            .orElseThrow();
    final Date minClusterAgeDate =
        Date.from(Instant.now().minus(minClusterAgeDays, ChronoUnit.DAYS));

    final BasicDBList neverSampledRecently = new BasicDBList();
    pDataByCorruptionDetectionTypeMap
        .values()
        .forEach(
            (corruptionDetectionData) -> {
              final Date minDaysBetweenValidationsDate =
                  Date.from(
                      Instant.now()
                          .minus(corruptionDetectionData.minClusterAgeDays(), ChronoUnit.DAYS));
              neverSampledRecently.add(
                  new BasicDBObject(
                      corruptionDetectionData.minDaysBetweenClusterValidationsField(),
                      new BasicDBObject(LTE, minDaysBetweenValidationsDate)));
              neverSampledRecently.add(
                  new BasicDBObject(
                      corruptionDetectionData.minDaysBetweenClusterValidationsField(), null));
            });

    // Not deleted, paused, free, serverless, FLEX, too new, or outside the sampling window
    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.STATE, new BasicDBObject(NE, State.DELETED))
            .append(FieldDefs.IS_PAUSED, false)
            .append(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    ReplicationSpec.FieldDefs.REGION_CONFIGS,
                    RegionConfig.FieldDefs.CLOUD_PROVIDER),
                new BasicDBObject(BaseDao.NIN, tenantCloudProvider))
            .append(OR, neverSampledRecently)
            .append(FieldDefs.CREATE_DATE, new BasicDBObject(LTE, minClusterAgeDate));

    // Target specific MongoDB version.
    _appSettings
        .getCorruptionDetectionTargetClusterVersionRegex()
        .ifPresent(r -> query.append(FieldDefs.MONGODB_VERSION, new BasicDBObject(REGEX, r)));

    return query;
  }

  private BasicDBObject getCorruptionDetectionEligibilityGroupStageQuery(
      final Map<CorruptionDetectionCategory, CorruptionDetectionData>
          pDataByCorruptionDetectionTypeMap) {
    final BasicDBObject groupQuery = new BasicDBObject(ID, null);

    pDataByCorruptionDetectionTypeMap.forEach(
        (pCategory, corruptionDetectionData) -> {
          final List<Object> andConditions = new ArrayList<>();
          andConditions.add(
              new BasicDBObject()
                  .append(
                      OR,
                      List.of(
                          new BasicDBObject(
                              LTE,
                              List.of(
                                  $(
                                      corruptionDetectionData
                                          .minDaysBetweenClusterValidationsField()),
                                  Date.from(
                                      Instant.now()
                                          .minus(
                                              corruptionDetectionData
                                                  .minDaysBetweenClusterValidationsValue(),
                                              ChronoUnit.DAYS)))),
                          new BasicDBObject(
                              EQ,
                              Stream.of(
                                      $(
                                          corruptionDetectionData
                                              .minDaysBetweenClusterValidationsField()),
                                      null)
                                  .collect(Collectors.toCollection(BasicDBList::new))))));
          andConditions.add(
              new BasicDBObject(
                  LTE,
                  List.of(
                      $(ClusterDescription.FieldDefs.CREATE_DATE),
                      Date.from(
                          Instant.now()
                              .minus(
                                  corruptionDetectionData.minClusterAgeDays(), ChronoUnit.DAYS)))));

          if (pCategory == CorruptionDetectionCategory.SHARDED_METADATA_CONSISTENCY) {
            andConditions.add(
                new BasicDBObject(
                    IN,
                    List.of(
                        $(ClusterDescription.FieldDefs.CLUSTER_TYPE),
                        List.of(ClusterType.SHARDED, ClusterType.GEOSHARDED))));
            andConditions.add(
                new BasicDBObject(
                    GTE,
                    List.of(
                        $(FieldDefs.MONGODB_VERSION), VersionUtils.SEVEN_ZERO_ZERO.toString())));
          }

          groupQuery.append(
              pCategory.name(),
              new BasicDBObject(
                  SUM,
                  new BasicDBObject(COND, List.of(new BasicDBObject(AND, andConditions), 1, 0))));
        });

    return groupQuery;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public int updateMTMClustersMongoDBMajorVersion(
      final ObjectId pGroupId,
      final VersionUtils.Version pOriginalMajorVersion,
      final VersionUtils.Version pNewMajorVersion,
      final VersionUtils.Version pNewFullVersion,
      final Date pNextPlanningDate) {

    final BasicDBObject query =
        new BasicDBObject()
            .append(joinFields(FieldDefs.ID, FieldDefs.GROUP_ID), pGroupId)
            .append(FieldDefs.IS_MTM, true)
            .append(FieldDefs.STATE, new BasicDBObject(NE, State.DELETED.name()))
            .append(FieldDefs.MONGODB_MAJOR_VERSION, pOriginalMajorVersion.getVersion());

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.MONGODB_MAJOR_VERSION, pNewMajorVersion.getVersion())
                .append(FieldDefs.MONGODB_VERSION, pNewFullVersion.getVersion())
                .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, pNextPlanningDate)
                .append(
                    FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                    ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name()));

    return updateAllMajority(query, update).getN();
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean setShardingMetadataConsistencyCheck(
      final ObjectId pGroupId,
      final String pClusterName,
      final Date pNeedsCheckMetadataConsistencyAfter,
      final boolean pBypassMaintenanceWindow,
      final boolean pSkipPreflightChecks,
      final CorruptionDetectionOperationOrigin pOperationOrigin) {
    final BasicDBObject query =
        new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.NEEDS_CHECK_AFTER),
                    pNeedsCheckMetadataConsistencyAfter)
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.BYPASS_MAINTENANCE_WINDOW),
                    pBypassMaintenanceWindow)
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.SKIP_PREFLIGHT_CHECKS),
                    pSkipPreflightChecks)
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.OPERATION_ORIGIN),
                    pOperationOrigin));

    return updateOneMajority(query, update).getN() == 1;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean unsetShardingMetadataConsistencyCheck(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(
                joinFields(
                    ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                    CheckMetadataConsistency.FieldDefs.NEEDS_CHECK_AFTER),
                new BasicDBObject(NE, null));

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.NEEDS_CHECK_AFTER),
                    null)
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.BYPASS_MAINTENANCE_WINDOW),
                    false)
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.SKIP_PREFLIGHT_CHECKS),
                    false)
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.OPERATION_ORIGIN),
                    null)
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.LAST_CHECK_DATE),
                    new Date()));

    return updateOneMajority(query, update).getN() == 1;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean updateNeedsCheckMetadataConsistencyAfter(
      final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    final BasicDBObject query =
        new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(
                    joinFields(
                        ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
                        CheckMetadataConsistency.FieldDefs.NEEDS_CHECK_AFTER),
                    pDate));

    return updateOneMajority(query, update).getN() == 1;
  }

  /** Clears an existing bumper file override on the cluster for the host */
  public void clearBumperFileOverride(
      final ObjectId pGroupId, final String pClusterName, final String pHostname) {
    final BasicDBObject query =
        new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject(
            PULL,
            new BasicDBObject(
                FieldDefs.BUMPER_FILE_OVERRIDES,
                new BasicDBObject(BumperFileOverride.FieldDefs.HOSTNAME, pHostname)));
    updateOneMajority(query, update);
  }

  /** Adds or updates a bumper file override on the cluster for the host */
  public void setBumperFileOverride(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final int pGoalNumBumperFiles) {
    final boolean updatedExistingOverride =
        updateExistingBumperFileOverride(pGroupId, pClusterName, pHostname, pGoalNumBumperFiles);
    if (!updatedExistingOverride) {
      final boolean addedNewOverride =
          addBumperFileOverride(pGroupId, pClusterName, pHostname, pGoalNumBumperFiles);
      if (!addedNewOverride) {
        throw new IllegalStateException(
            String.format(
                "Unable to set or update bumper file override for cluster %s in group %s for host"
                    + " %s",
                pClusterName, pGroupId, pHostname));
      }
    }
  }

  /**
   * Attempts to update an existing bumper file override. Does not add a new override if one is not
   * currently present for the hostname
   *
   * @return true if update occurs (override currently exists)
   */
  private boolean updateExistingBumperFileOverride(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final int pGoalNumBumperFiles) {
    final BasicDBObject queryExistingOverride =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(
                joinFields(FieldDefs.BUMPER_FILE_OVERRIDES, BumperFileOverride.FieldDefs.HOSTNAME),
                pHostname);
    final BasicDBObject updateExistingOverride =
        new BasicDBObject(
            SET,
            new BasicDBObject(
                joinFields(
                    FieldDefs.BUMPER_FILE_OVERRIDES,
                    DOLLAR_SIGN,
                    BumperFileOverride.FieldDefs.NUMBER_OF_BUMPER_FILES),
                pGoalNumBumperFiles));
    return updateOneMajority(queryExistingOverride, updateExistingOverride).getN() > 0;
  }

  /**
   * adds a new bumper file override for the host. This does not update existing overrides for the
   * same host
   *
   * @return true if update occurs (no override already exists)
   */
  private boolean addBumperFileOverride(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pHostname,
      final int pGoalNumBumperFiles) {
    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(
                joinFields(FieldDefs.BUMPER_FILE_OVERRIDES, BumperFileOverride.FieldDefs.HOSTNAME),
                new BasicDBObject(NE, pHostname));
    final BasicDBObject update =
        new BasicDBObject(
            PUSH,
            new BasicDBObject(
                FieldDefs.BUMPER_FILE_OVERRIDES,
                new BumperFileOverride(pHostname, pGoalNumBumperFiles).toDBObject()));
    return updateOneMajority(query, update).getN() > 0;
  }

  boolean shouldUpdateDedicatedConfigServerReplicationSpec(
      final ClusterDescription clusterDescriptionBeforeUpdate) {
    return clusterDescriptionBeforeUpdate.getClusterType().isSharded()
        && ((ShardedClusterDescription) clusterDescriptionBeforeUpdate)
            .getConfigServerType()
            .isDedicated();
  }

  public int setAlwaysManagedDefaultRWConcernSince(
      final ObjectId groupId, final String name, final Date date) {
    return updateOneMajority(
            new BasicDBObject()
                .append(FieldDefs.ID, getId(groupId, name))
                .append(FieldDefs.ALWAYS_MANAGED_DEFAULT_RW_CONCERN_SINCE, null),
            new BasicDBObject(
                SET, new BasicDBObject(FieldDefs.ALWAYS_MANAGED_DEFAULT_RW_CONCERN_SINCE, date)))
        .getN();
  }

  private enum CorruptionDetectionCategory {
    DBCHECK,
    DATA_VALIDATION,
    SHARDED_METADATA_CONSISTENCY
  }

  private record CorruptionDetectionData(
      String minDaysBetweenClusterValidationsField,
      int minDaysBetweenClusterValidationsValue,
      int minClusterAgeDays) {}

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setCpuSocketBinding(
      final ObjectId pGroupId, final String pClusterName, final List<Integer> pCpuSocketBinding) {
    final BasicDBList cpuSocketBinding =
        CollectionUtils.isNotEmpty(pCpuSocketBinding)
            ? pCpuSocketBinding.stream().collect(DbUtils.toBasicDBList())
            : null;
    updateOneMajority(
        new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.CPU_SOCKET_BINDING, cpuSocketBinding)
                .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, new Date())
                .append(
                    FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                    ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name())));
  }

  /**
   * Sets the reserveIpamIpRequested field on the cluster description
   *
   * @param pGroupId The group ID
   * @param pClusterName The cluster name
   * @param pReserveIpamIpRequested boolean value to set for reserveIpamIpRequested
   */
  public void setReserveIpamIpRequested(
      final ObjectId pGroupId, final String pClusterName, final boolean pReserveIpamIpRequested) {
    updateOneMajority(
        new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject()
                .append(FieldDefs.RESERVE_IPAM_IP_REQUESTED, pReserveIpamIpRequested)));
  }

  /**
   * Unsets the reserveIpamIpRequested field on the cluster description
   *
   * @param pGroupId The group ID
   * @param pClusterName The cluster name
   */
  public void unsetReserveIpamIpRequested(final ObjectId pGroupId, final String pClusterName) {
    updateOneMajority(
        new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            UNSET, new BasicDBObject().append(FieldDefs.RESERVE_IPAM_IP_REQUESTED, 1)));
  }

  /**
   * @see ClusterDescription#getSwapIpMaintenanceRoundCompleted
   */
  public void setSwapIpMaintenanceRoundCompleted(
      final ObjectId groupId,
      final String clusterName,
      final Optional<ClusterDescription.SwapIpMaintenanceRound> swapIpMaintenanceRound) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(groupId, clusterName)),
        swapIpMaintenanceRound
            .map(
                imr ->
                    new BasicDBObject(
                        SET,
                        new BasicDBObject(
                            FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED, imr.name())))
            .orElseGet(
                () ->
                    new BasicDBObject(
                        UNSET,
                        new BasicDBObject(FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED, 1))));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setClusterConnectionStringConfiguration(
      final ObjectId pGroupId,
      final String pClusterName,
      final ClusterConnectionStringConfiguration pConfiguration) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject(
                FieldDefs.CLUSTER_CONNECTION_STRING_CONFIGURATION, pConfiguration.toDBObject())));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setFlexTenantMigrationState(
      final ObjectId pGroupId,
      final String pClusterName,
      final FlexTenantMigrationState pFlexTenantMigrationState) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject(
                FieldDefs.FLEX_TENANT_MIGRATION_STATE, pFlexTenantMigrationState.toDBObject())));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setAllowUnsafeRollingOperation(
      final ObjectId pGroupId,
      final String pClusterName,
      final boolean pAllowUnsafeRollingOperation) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject(
                FieldDefs.ALLOW_UNSAFE_ROLLING_OPERATION, pAllowUnsafeRollingOperation)));
  }

  // this should only be used in development environments for testing purposes
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setCreatedDate(final ObjectId pGroupId, final String pClusterName, final Date pDate) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(SET, new BasicDBObject(FieldDefs.CREATE_DATE, pDate)));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setAZBalancingOverrideForRegionConfigs(
      final ObjectId groupId,
      final String clusterName,
      final RegionName regionName,
      final AZBalancingRequirement azBalancingRequirement) {
    final List<BasicDBObject> arrayFilter =
        List.of(
            new BasicDBObject()
                .append(
                    joinFields("rc", RegionConfig.FieldDefs.CLOUD_PROVIDER),
                    regionName.getProvider())
                .append(joinFields("rc", RegionConfig.FieldDefs.REGION_NAME), regionName));

    updateWithOptions(
        new BasicDBObject(FieldDefs.ID, getId(groupId, clusterName)),
        new BasicDBObject(
            SET,
            new BasicDBObject(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    POSITIONAL_ALL,
                    ReplicationSpec.FieldDefs.REGION_CONFIGS,
                    "$[rc]",
                    RegionConfig.FieldDefs.AZ_BALANCING_REQUIREMENT_OVERRIDE),
                azBalancingRequirement.toDBObject())),
        new DBCollectionUpdateOptions()
            .arrayFilters(arrayFilter)
            .writeConcern(getMajorityWriteConcern()));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void unsetAZBalancingOverrideForRegionConfigs(
      final ObjectId groupId, final String clusterName, final RegionName regionName) {
    final List<BasicDBObject> arrayFilter =
        List.of(
            new BasicDBObject()
                .append(
                    joinFields("rc", RegionConfig.FieldDefs.CLOUD_PROVIDER),
                    regionName.getProvider())
                .append(joinFields("rc", RegionConfig.FieldDefs.REGION_NAME), regionName));

    updateWithOptions(
        new BasicDBObject(FieldDefs.ID, getId(groupId, clusterName)),
        new BasicDBObject(
            UNSET,
            new BasicDBObject(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    POSITIONAL_ALL,
                    ReplicationSpec.FieldDefs.REGION_CONFIGS,
                    "$[rc]",
                    RegionConfig.FieldDefs.AZ_BALANCING_REQUIREMENT_OVERRIDE),
                "")),
        new DBCollectionUpdateOptions()
            .arrayFilters(arrayFilter)
            .writeConcern(getMajorityWriteConcern()));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Map<String, List<FleetAttribute>> getActiveClusterAttributesAggregation() {
    final BasicDBObject additionalFilter =
        new BasicDBObject()
            .append(FieldDefs.IS_PAUSED, false)
            .append(FieldDefs.IS_MTM, false)
            .append(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    ReplicationSpec.FieldDefs.REGION_CONFIGS,
                    RegionConfig.FieldDefs.CLOUD_PROVIDER),
                new BasicDBObject(
                    BaseDao.IN,
                    List.of(
                        CloudProvider.AWS.name(),
                        CloudProvider.AZURE.name(),
                        CloudProvider.GCP.name())));

    return getClusterAttributesAggregation(additionalFilter);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Map<String, List<FleetAttribute>> getPausedClusterAttributesAggregation() {
    final BasicDBObject additionalFilter =
        new BasicDBObject()
            .append(FieldDefs.IS_PAUSED, true)
            .append(FieldDefs.CLOUD_PROVIDER_OPTIONS, new BasicDBObject(EXISTS, false));

    return getClusterAttributesAggregation(additionalFilter);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Map<String, List<FleetAttribute>> getMTMClusterAttributesAggregation() {
    final BasicDBObject additionalFilter = new BasicDBObject().append(FieldDefs.IS_MTM, true);

    return getClusterAttributesAggregation(additionalFilter);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setIsEligibleForReducedFlexPricing(
      final ObjectId pGroupId, final String pClusterName) {
    updateOneMajority(
        new BasicDBObject(FieldDefs.ID, getId(pGroupId, pClusterName)),
        new BasicDBObject(
            SET, new BasicDBObject(FieldDefs.IS_ELIGIBLE_FOR_REDUCED_FLEX_PRICING, true)));
  }

  /**
   * Updates the disaggregated storage configuration for a specific replication spec in a cluster.
   *
   * @param pGroupId The ObjectId of the group to which the cluster belongs
   * @param pClusterName The name of the cluster to update
   * @param pReplicationSpecId The ObjectId of the replication spec to update
   * @param pDisaggregatedStorageConfig The new disaggregated storage configuration to set
   * @return The result of the update operation
   */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public WriteResult setDisaggregatedStorageConfig(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pReplicationSpecId,
      final DisaggregatedStorageConfig pDisaggregatedStorageConfig) {
    final List<BasicDBObject> arrayFilter =
        List.of(
            new BasicDBObject()
                .append(joinFields("rc", ReplicationSpec.FieldDefs.ID), pReplicationSpecId));

    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, getId(pGroupId, pClusterName))
            .append(
                FieldDefs.STORAGE_SYSTEM,
                ClusterDescription.StorageSystem.DISAGGREGATED_STORAGE.name())
            .append(
                joinFields(FieldDefs.REPLICATION_SPEC_LIST, ReplicationSpec.FieldDefs.ID),
                pReplicationSpecId);

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject(
                joinFields(
                    FieldDefs.REPLICATION_SPEC_LIST,
                    "$[rc]",
                    ReplicationSpec.FieldDefs.DISAGGREGATED_STORAGE_CONFIG),
                pDisaggregatedStorageConfig.toDBObject()));

    return updateWithOptions(
        query,
        update,
        new DBCollectionUpdateOptions()
            .arrayFilters(arrayFilter)
            .writeConcern(getMajorityWriteConcern()));
  }

  private Map<String, List<FleetAttribute>> getClusterAttributesAggregation(
      final BasicDBObject pAdditionalFilter) {

    // Match all non-deleted clusters along witht he match passed into the function
    final BasicDBObject matchQuery =
        new BasicDBObject().append(FieldDefs.STATE, new BasicDBObject(NE, State.DELETED));

    if (!pAdditionalFilter.isEmpty()) {
      pAdditionalFilter.forEach(matchQuery::append);
    }

    final List<String> topLevelFields =
        List.of(
            FieldDefs.MONGODB_VERSION,
            FieldDefs.MONGODB_MAJOR_VERSION,
            FieldDefs.VERSION_RELEASE_SYSTEM,
            FieldDefs.OS_POLICY_VERSION,
            FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
            FieldDefs.IS_MTM,
            FieldDefs.IS_PAUSED,
            FleetAttributes.MONGOTUNE_VERSION);

    final AggregationOptions opts = AggregationOptions.builder().allowDiskUse(true).build();
    final ReadPreference readPref = DriverUtils.SECONDARY_PREFERRED_MINIMUM;

    final List<DBObject> pipeline = new ArrayList<>();
    pipeline.add(new BasicDBObject(MATCH, matchQuery));

    // Add stages to extract nested fields for aggregation
    pipeline.add(
        new BasicDBObject(
            BaseDao.ADD_FIELDS,
            new BasicDBObject(
                FleetAttributes.MONGOTUNE_VERSION,
                "$" + FieldDefs.MONGOTUNE + "." + MongotuneStatus.FieldDefs.VERSION)));

    pipeline.add(FleetAttribute.facetExpressionForTopLevelFields(topLevelFields));
    try (final Cursor cursor = getDbCollection().aggregate(pipeline, opts, readPref)) {
      if (cursor.hasNext()) {
        return FleetAttribute.dataFromDBObject(LOG, (BasicDBObject) cursor.next());
      }
    }

    return Map.of();
  }

  public void setProxyProtocolForPrivateLinkMode(
      final ClusterDescription pClusterDescription, final String pProxyProtocolForPrivateLinkMode) {
    setProxyProtocolForPrivateLinkMode(
        pClusterDescription.getGroupId(),
        pClusterDescription.getName(),
        pProxyProtocolForPrivateLinkMode);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean setProxyProtocolForPrivateLinkMode(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pProxyProtocolForPrivateLinkMode) {
    final BasicDBObject query =
        new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));

    final BasicDBObject update =
        new BasicDBObject(
            SET,
            new BasicDBObject(
                ClusterDescription.FieldDefs.PROXY_PROTOCOL_FOR_PRIVATE_LINK_MODE,
                pProxyProtocolForPrivateLinkMode));

    return updateOneMajority(query, update).getN() == 1;
  }

  /**
   * Sets (or unsets) the partner integrations data field for a cluster
   *
   * @param pGroupId The ObjectId of the group to which the cluster belongs
   * @param pClusterName The name of the cluster to update
   * @param pPartnerIntegrationsData The new partner integrations data to set, or null to unset
   */
  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setPartnerIntegrationsData(
      final ObjectId pGroupId,
      final String pClusterName,
      @Nullable final PartnerIntegrationsData pPartnerIntegrationsData) {
    final DBObject query = new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));
    final DBObject update =
        new BasicDBObject()
            .append(
                SET,
                new BasicDBObject()
                    .append(FieldDefs.PARTNER_INTEGRATIONS_DATA, pPartnerIntegrationsData));
    updateOneMajority(query, update);
  }

  /** Updates the gateway router eligibility state for a cluster. */
  public void setGatewayRouterEligibilityState(
      final ObjectId pGroupId,
      final String pClusterName,
      final boolean pEligible,
      final Date pUpdateDate) {
    final DBObject query = new BasicDBObject().append(FieldDefs.ID, getId(pGroupId, pClusterName));
    final DBObject update =
        new BasicDBObject()
            .append(
                SET,
                new BasicDBObject()
                    .append(ClusterDescription.FieldDefs.GATEWAY_ROUTER_ELIGIBLE, pEligible)
                    .append(
                        ClusterDescription.FieldDefs.GATEWAY_ROUTER_ELIGIBILITY_LAST_UPDATE_DATE,
                        pUpdateDate));
    updateOneMajority(query, update);
  }
}
