package com.xgen.cloud.nds.project.runtime.res.api_2024_10_23;

import static com.xgen.cloud.apiusagedata._public.view.ApiTelemetryView.FieldDefs.CLUSTER_NAME;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.OPERATION_ID_OVERRIDE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetryAttributeSvc;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetrySvc.CustomTelemetryFieldKey;
import com.xgen.cloud.authz.resource._public.view.api.ApiAtlasTagView;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.common.versioning._public.constants.VersioningConstants;
import com.xgen.cloud.common.view._public.base.ApiView;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterUpdateContext;
import com.xgen.cloud.nds.flex.runtime.svc.LegacySharedApiShimToFlexSvc;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.FlexTenantMigrationState;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.resourcepolicy._public.model.AtlasResourcePolicyAuthResponse;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.util.ApiAtlasClusterDescriptionUtil;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionConnectionStringsUtil;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionV15View;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasLegacyClusterDescriptionView;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescription20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasReplicationSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasTenantRegionConfig20240805View;
import com.xgen.svc.mms.res.filter.PaidInFull;
import com.xgen.svc.mms.res.filter.SubscriptionPlan;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.RegionConfigView;
import com.xgen.svc.nds.svc.NDSResourcePolicySvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import net.logstash.logback.argument.StructuredArguments;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SubscriptionPlan(PlanTypeSet.NDS)
@PaidInFull
@Path("/api/atlas/v2/groups/{groupId}/clusters")
@Singleton
public class ApiAtlasClusterDescriptionResource20241023 extends ApiBaseResource {
  private final Logger LOG =
      LoggerFactory.getLogger(ApiAtlasClusterDescriptionResource20241023.class);

  private final ApiAtlasClusterDescriptionUtil _apiAtlasClusterDescriptionUtil;
  private final AppSettings _appSettings;
  private final NDSUISvc _ndsUISvc;
  private final OnlineArchiveSvc _onlineArchiveSvc;
  private final FeatureFlagSvc _featureFlagSvc;
  private final NDSGroupSvc _ndsGroupSvc;
  private final AuditSvc _auditSvc;
  private final NDSResourcePolicySvc _ndsResourcePolicySvc;
  private final NDSClusterSvc _ndsClusterSvc;
  private final LegacySharedApiShimToFlexSvc _legacySharedApiShimToFlexSvc;
  private final ApiTelemetryAttributeSvc _apiTelemetryAttributeSvc;

  @Inject
  public ApiAtlasClusterDescriptionResource20241023(
      final ApiAtlasClusterDescriptionUtil pApiAtlasClusterDescriptionUtil,
      final AppSettings pAppSettings,
      final NDSUISvc pNDSUISvc,
      final OnlineArchiveSvc pOnlineArchiveSvc,
      final FeatureFlagSvc pFeatureFlagSvc,
      final NDSGroupSvc pNdsGroupSvc,
      final AuditSvc pAuditSvc,
      final NDSResourcePolicySvc pNDSResourcePolicySvc,
      final NDSClusterSvc pNDSClusterSvc,
      final LegacySharedApiShimToFlexSvc pLegacySharedApiShimToFlexSvc,
      final ApiTelemetryAttributeSvc pApiTelemetryAttributeSvc) {
    super(pAppSettings);
    _apiAtlasClusterDescriptionUtil = pApiAtlasClusterDescriptionUtil;
    _appSettings = pAppSettings;
    _ndsUISvc = pNDSUISvc;
    _onlineArchiveSvc = pOnlineArchiveSvc;
    _featureFlagSvc = pFeatureFlagSvc;
    _ndsGroupSvc = pNdsGroupSvc;
    _auditSvc = pAuditSvc;
    _ndsResourcePolicySvc = pNDSResourcePolicySvc;
    _ndsClusterSvc = pNDSClusterSvc;
    _legacySharedApiShimToFlexSvc = pLegacySharedApiShimToFlexSvc;
    _apiTelemetryAttributeSvc = pApiTelemetryAttributeSvc;
  }

  // Asymmetric Sharded Cluster Example (autoscaling not available)
  public static final String ASYMMETRIC_SHARDED_CLUSTER_EXAMPLE_20240805 =
      """
      {
        "clusterType": "SHARDED",
        "name": "myCluster",
        "replicationSpecs": [
          {
            "regionConfigs": [
              {
                "analyticsAutoScaling": {
                  "autoIndexing": {
                    "enabled": false
                  },
                  "compute": {
                    "enabled": false
                  },
                  "diskGB": {
                    "enabled": true
                  }
                },
                "analyticsSpecs": {
                  "instanceSize": "M40",
                  "nodeCount": 0,
                  "diskSizeGB": 10.0
                },
                "autoScaling": {
                  "autoIndexing": {
                    "enabled": false
                  },
                  "compute": {
                    "enabled": false
                  },
                  "diskGB": {
                    "enabled": true
                  }
                },
                "electableSpecs": {
                  "instanceSize": "M50",
                  "nodeCount": 3,
                  "diskSizeGB": 10.0
                },
                "priority": 7,
                "providerName": "AWS",
                "readOnlySpecs": {
                  "instanceSize": "M50",
                  "nodeCount": 0,
                  "diskSizeGB": 10.0
                },
                "regionName": "US_EAST_1"
              }
            ],
            "zoneName": "Zone 1"
          },
          {
            "regionConfigs": [
              {
                "analyticsAutoScaling": {
                  "autoIndexing": {
                    "enabled": false
                  },
                  "compute": {
                    "enabled": false
                  },
                  "diskGB": {
                    "enabled": true
                  }
                },
                "analyticsSpecs": {
                  "instanceSize": "M30",
                  "nodeCount": 0,
                  "diskSizeGB": 10.0
                },
                "autoScaling": {
                  "autoIndexing": {
                    "enabled": false
                  },
                  "compute": {
                    "enabled": false
                  },
                  "diskGB": {
                    "enabled": true
                  }
                },
                "electableSpecs": {
                  "instanceSize": "M40",
                  "nodeCount": 3,
                  "diskSizeGB": 10.0
                },
                "priority": 7,
                "providerName": "AWS",
                "readOnlySpecs": {
                  "instanceSize": "M40",
                  "nodeCount": 0,
                  "diskSizeGB": 10.0
                },
                "regionName": "US_EAST_1"
              }
            ],
            "zoneName": "Zone 1"
          }
        ]
      }
      """;

  // Asymmetric Sharded Cluster Example (autoscaling is available)
  public static final String ASYMMETRIC_SHARDED_CLUSTER_EXAMPLE_20241023 =
      """
      {
        "clusterType": "SHARDED",
        "name": "myCluster",
        "replicationSpecs": [
          {
            "regionConfigs": [
              {
                "analyticsAutoScaling": {
                  "autoIndexing": {
                    "enabled": false
                  },
                  "compute": {
                    "enabled": true,
                    "scaleDownEnabled": true,
                    "maxInstanceSize": "M40",
                    "minInstanceSize": "M30"
                  },
                  "diskGB": {
                    "enabled": true
                  }
                },
                "analyticsSpecs": {
                  "instanceSize": "M40",
                  "nodeCount": 0,
                  "diskSizeGB": 10.0
                },
                "autoScaling": {
                  "autoIndexing": {
                    "enabled": false
                  },
                  "compute": {
                    "enabled": true,
                    "scaleDownEnabled": true,
                    "maxInstanceSize": "M60",
                    "minInstanceSize": "M30"
                  },
                  "diskGB": {
                    "enabled": true
                  }
                },
                "electableSpecs": {
                  "instanceSize": "M60",
                  "nodeCount": 3,
                  "diskSizeGB": 10.0
                },
                "priority": 7,
                "providerName": "AWS",
                "readOnlySpecs": {
                  "instanceSize": "M60",
                  "nodeCount": 0,
                  "diskSizeGB": 10.0
                },
                "regionName": "US_EAST_1"
              }
            ],
            "zoneName": "Zone 1"
          },
          {
            "regionConfigs": [
              {
                "analyticsAutoScaling": {
                  "autoIndexing": {
                    "enabled": false
                  },
                  "compute": {
                    "enabled": true,
                    "scaleDownEnabled": true,
                    "maxInstanceSize": "M40",
                    "minInstanceSize": "M30"
                  },
                  "diskGB": {
                    "enabled": true
                  }
                },
                "analyticsSpecs": {
                  "instanceSize": "M30",
                  "nodeCount": 0,
                  "diskSizeGB": 10.0
                },
                "autoScaling": {
                  "autoIndexing": {
                    "enabled": false
                  },
                  "compute": {
                    "enabled": true,
                    "scaleDownEnabled": true,
                    "maxInstanceSize": "M60",
                    "minInstanceSize": "M30"
                  },
                  "diskGB": {
                    "enabled": true
                  }
                },
                "electableSpecs": {
                  "instanceSize": "M40",
                  "nodeCount": 3,
                  "diskSizeGB": 10.0
                },
                "priority": 7,
                "providerName": "AWS",
                "readOnlySpecs": {
                  "instanceSize": "M40",
                  "nodeCount": 0,
                  "diskSizeGB": 10.0
                },
                "regionName": "US_EAST_1"
              }
            ],
            "zoneName": "Zone 1"
          }
        ]
      }
      """;

  @POST
  @Produces(VersionMediaType.V_2024_10_23_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed(RoleSet.NAME.GROUP_ATLAS_ADMIN)
  @Operation(
      summary = "Create One Cluster in One Project",
      operationId = "createGroupCluster",
      description =
          "Creates one cluster in the specified project. Clusters contain a group of hosts that"
              + " maintain the same data set. This resource can create clusters with"
              + " asymmetrically-sized shards. Each project supports up to 25 database deployments."
              + " To use this resource, the requesting Service Account or API Key must have the"
              + " Project Owner role. This feature is not available for serverless clusters.\n\n"
              + "Please note that using an instanceSize of M2 or M5 will create a Flex cluster"
              + " instead. Support for the instanceSize of M2 or M5 will be discontinued in January"
              + " 2026. We recommend using the createFlexCluster API for such configurations moving"
              + " forward.",
      externalDocs =
          @ExternalDocumentation(
              description = "createFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/createFlexCluster"),
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty")
      },
      responses = {
        @ApiResponse(
            responseCode = "201",
            description = "Created",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema = @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_11_30)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescriptionV15View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_02_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_10_23_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_10_23_EXTENSION_TYPE)
                        }),
                  }),
              @Content(
                  mediaType = VersionMediaType.V_PREVIEW_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_PREVIEW_EXTENSION_TYPE)
                        }),
                    @Extension(
                        name = "x-xgen-preview",
                        properties = {
                          @ExtensionProperty(name = "public", value = "true"),
                        }),
                    @Extension(
                        name = "x-xgen-hidden-env",
                        properties = {@ExtensionProperty(name = "envs", value = "qa,stage,prod")}),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name =
                                  "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                              value = "Content predates IPA validation."),
                          @ExtensionProperty(
                              name = "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                              value = "Content predates IPA validation.")
                        })
                  }),
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "402", ref = "paymentRequired"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2025-06-05",
                  value =
                      "Fixed a bug that previously permitted users to configure multiple"
                          + " regionConfigs for the same region and cloud provider within a"
                          + " replicationSpec"),
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "createCluster")
            }),
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Cluster to create in this project.",
              content = {
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema = @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class),
                    extensions = {
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name = "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation.")
                          })
                    }),
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiAtlasClusterDescriptionV15View.class),
                    extensions = {
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name = "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation.")
                          })
                    }),
                @Content(
                    mediaType = VersionMediaType.V_2024_08_05_JSON,
                    examples = {
                      @ExampleObject(
                          name = "Cluster",
                          value = ASYMMETRIC_SHARDED_CLUSTER_EXAMPLE_20240805)
                    },
                    schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name = "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation.")
                          })
                    }),
                @Content(
                    mediaType = VersionMediaType.V_2024_10_23_JSON,
                    examples = {
                      @ExampleObject(
                          name = "Cluster",
                          value = ASYMMETRIC_SHARDED_CLUSTER_EXAMPLE_20241023)
                    },
                    schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_2024_10_23_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name = "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation.")
                          })
                    }),
                @Content(
                    mediaType = VersionMediaType.V_PREVIEW_JSON,
                    schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_PREVIEW_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = "x-xgen-preview",
                          properties = {
                            @ExtensionProperty(name = "public", value = "true"),
                          }),
                      @Extension(
                          name = "x-xgen-hidden-env",
                          properties = {
                            @ExtensionProperty(name = "envs", value = "qa,stage,prod")
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name = "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation.")
                          })
                    })
              }))
  public Response createCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      @Context final AppUser pCurrentUser)
      throws Exception {
    return createClusterInner(
        pRequest,
        pOrganization,
        pGroup,
        pNDSGroup,
        pAuditInfo,
        pEnvelope,
        pClusterDescriptionView,
        pCurrentUser,
        ClusterCreateContext.forClusterApi(true));
  }

  @PATCH
  @Path("/{clusterName}")
  @Produces(VersionMediaType.V_2024_10_23_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_CLUSTER_MANAGER})
  @Operation(
      summary = "Update One Cluster in One Project",
      operationId = "updateGroupCluster",
      description =
          "Updates the details for one cluster in the specified project. Clusters contain a group"
              + " of hosts that maintain the same data set. This resource can update clusters with"
              + " asymmetrically-sized shards. To update a cluster's termination protection, the"
              + " requesting Service Account or API Key must have the Project Owner role. For all"
              + " other updates, the requesting Service Account or API Key must have the Project"
              + " Cluster Manager role. You can't modify a paused cluster (`paused : true`). You"
              + " must call this endpoint to set `paused : false`. After this endpoint responds"
              + " with `paused : false`, you can call it again with the changes you want to make to"
              + " the cluster. This feature is not available for serverless clusters.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema = @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_11_30)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescriptionV15View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_02_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_10_23_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_10_23_EXTENSION_TYPE)
                        }),
                  }),
              @Content(
                  mediaType = VersionMediaType.V_PREVIEW_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_PREVIEW_EXTENSION_TYPE)
                        }),
                    @Extension(
                        name = "x-xgen-preview",
                        properties = {
                          @ExtensionProperty(name = "public", value = "true"),
                        }),
                    @Extension(
                        name = "x-xgen-hidden-env",
                        properties = {@ExtensionProperty(name = "envs", value = "qa,stage,prod")})
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Cluster to update in the specified project.",
              content = {
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema = @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class),
                    extensions = {
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-107-update-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                value = "API predates IPA validation.")
                          })
                    }),
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiAtlasClusterDescriptionV15View.class),
                    extensions = {
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-107-update-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                value = "API predates IPA validation.")
                          })
                    }),
                @Content(
                    mediaType = VersionMediaType.V_2024_08_05_JSON,
                    schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-107-update-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                value = "API predates IPA validation.")
                          })
                    }),
                @Content(
                    mediaType = VersionMediaType.V_2024_10_23_JSON,
                    schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_2024_10_23_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-107-update-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                value = "API predates IPA validation.")
                          })
                    }),
                @Content(
                    mediaType = VersionMediaType.V_PREVIEW_JSON,
                    schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_PREVIEW_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = "x-xgen-preview",
                          properties = {
                            @ExtensionProperty(name = "public", value = "true"),
                          }),
                      @Extension(
                          name = "x-xgen-hidden-env",
                          properties = {
                            @ExtensionProperty(name = "envs", value = "qa,stage,prod")
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-107-update-method-request-has-no-readonly-fields",
                                value = "Content predates IPA validation."),
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                value = "API predates IPA validation.")
                          })
                    })
              }),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2025-06-05",
                  value =
                      "Fixed a bug that previously permitted users to configure multiple"
                          + " regionConfigs for the same region and cloud provider within a"
                          + " replicationSpec"),
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "updateCluster")
            }),
      })
  public Response updateCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView)
      throws Exception {
    final boolean enablesAutoScalingInRequest =
        Optional.ofNullable(pClusterDescriptionView)
            .map(ApiAtlasClusterDescription20240805View::getReplicationSpecs)
            .stream()
            .flatMap(Collection::stream)
            .filter(Objects::nonNull)
            .map(ApiAtlasReplicationSpec20240805View::getRegionConfigs)
            .filter(Objects::nonNull)
            .flatMap(Collection::stream)
            .filter(Objects::nonNull)
            .filter(ApiAtlasDedicatedRegionConfig20240805View.class::isInstance)
            .map(ApiAtlasDedicatedRegionConfig20240805View.class::cast)
            .anyMatch(
                rc -> rc.hasEnableAutoscalingFields() || rc.hasEnableAnalyticsAutoscalingFields());
    return updateClusterInner(
        pRequest,
        pOrganization,
        pGroup,
        pNDSGroup,
        pAppUser,
        pAuditInfo,
        pName,
        pEnvelope,
        pClusterDescriptionView,
        ClusterUpdateContext.forClusterApi(enablesAutoScalingInRequest));
  }

  protected Response createClusterInner(
      HttpServletRequest pRequest,
      Organization pOrganization,
      Group pGroup,
      NDSGroup pNDSGroup,
      AuditInfo pAuditInfo,
      Boolean pEnvelope,
      ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      AppUser pCurrentUser,
      ClusterCreateContext pCreateContext)
      throws SvcException {
    if (Optional.ofNullable(pClusterDescriptionView).isEmpty()) {
      throw ApiErrorCode.INVALID_JSON.exception(pEnvelope);
    }
    if (!FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, _appSettings, null, pGroup)) {
      saveEnableISSAudit(pGroup.getId(), pAuditInfo);
      _featureFlagSvc.enableNonConfigServiceFeatureFlag(
          pGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
      _ndsGroupSvc.setPlanningNow(pGroup.getId());
    }

    final ClusterDescriptionView view;

    _apiAtlasClusterDescriptionUtil.validateClusterDescription20240805ViewForCreate(
        pClusterDescriptionView, pCreateContext, pEnvelope, pNDSGroup);
    // Updating our view to actually be flex
    if (_apiAtlasClusterDescriptionUtil.isFlexShimLogicEnabled(pGroup)
        && pClusterDescriptionView.isSharedTenantCluster()) {
      final ApiAtlasTenantRegionConfig20240805View regionConfig =
          (ApiAtlasTenantRegionConfig20240805View)
              pClusterDescriptionView.getReplicationSpecs().get(0).getRegionConfigs().get(0);
      view =
          _legacySharedApiShimToFlexSvc.createFlexClusterDescriptionViewFromShared(
              pGroup,
              pNDSGroup,
              pEnvelope,
              pClusterDescriptionView.getName(),
              regionConfig.getBackingProviderName().name(),
              regionConfig.getRegionName(),
              pClusterDescriptionView
                  .getReplicationSpecs()
                  .get(0)
                  .getInstanceSizes()
                  .get(0)
                  .getInstanceSize(),
              pClusterDescriptionView.getReplicationSpecs().get(0).getZoneName());
    } else {
      view =
          _apiAtlasClusterDescriptionUtil.buildClusterDescriptionViewForCreate(
              pClusterDescriptionView, pEnvelope, pNDSGroup, pOrganization.getId());
    }

    final ClusterDescriptionProcessArgsView existingProcessArgs =
        _ndsUISvc.getProcessArgsOrDefault(view.getGroupId(), view.getName());
    final ClusterDescriptionProcessArgsView mergedProcessArgsView =
        pClusterDescriptionView
            .getAdvancedConfiguration()
            .map(
                advancedConfiguration ->
                    advancedConfiguration.mergeAndConvertToProcessArgsViewWithoutAdminFields(
                        existingProcessArgs))
            .orElse(existingProcessArgs);
    final AtlasResourcePolicyAuthResponse authResult =
        _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
            pGroup, view, mergedProcessArgsView, pAuditInfo);

    if (authResult.decision().isDeny()) {
      return ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.response(pEnvelope);
    }

    try {
      if (FeatureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI,
          _appSettings,
          pOrganization,
          null)) {
        _ndsUISvc.createProcessArgs(
            pGroup,
            pNDSGroup,
            pCreateContext,
            view,
            mergedProcessArgsView,
            pAuditInfo,
            pCurrentUser);
      }
      _ndsUISvc.createCluster(
          pOrganization,
          pGroup.getId(),
          view,
          pCreateContext,
          pCurrentUser,
          pAuditInfo,
          pRequest,
          pClusterDescriptionView.getTags());
    } catch (final SvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleClusterCreateExceptions(
          pE, view.getName(), pGroup, pEnvelope);
      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(
          pE, view.getName(), pGroup, pEnvelope);
    }

    final ClusterDescriptionView clusterDescriptionView =
        _ndsUISvc.getClusterDescription(pGroup.getId(), view.getName());
    final List<ApiAtlasTagView> tags =
        _apiAtlasClusterDescriptionUtil.saveClusterTags(
            clusterDescriptionView,
            pClusterDescriptionView.getTags(),
            pGroup,
            pAuditInfo,
            pEnvelope);

    if (_apiAtlasClusterDescriptionUtil.isFlexShimLogicEnabled(pGroup)
        && pClusterDescriptionView.isSharedTenantCluster()) {
      if (clusterDescriptionView.isFlexTenantCluster()) {
        final FlexTenantMigrationState flexTenantMigrationState =
            clusterDescriptionView.getFlexTenantMigrationState().orElseThrow();
        final ApiAtlasClusterDescription20240805View createdView =
            (ApiAtlasClusterDescription20240805View)
                _legacySharedApiShimToFlexSvc.convertFlexClusterDescriptionViewToSharedResponse(
                    clusterDescriptionView,
                    flexTenantMigrationState.getTenantApiInstanceSize().orElseThrow(),
                    pNDSGroup,
                    tags,
                    pEnvelope,
                    VersionMediaType.V_2024_08_05_EXTENSION_TYPE);

        setAdminApiTelemetryAttributes(pRequest, createdView.getName(), createdView.getId());

        return new ApiResponseBuilder(pEnvelope).created().content(createdView).build();
      } else {
        // If somehow our shimming logic failed and we do not end up creating a flex cluster, we
        // should just return the original response of our clusters API. We should not be hitting
        // this state.
        LOG.error(
            String.format(
                "We should have created a flex cluster %s in %s but instead created a %s.",
                clusterDescriptionView.getName(),
                clusterDescriptionView.getGroupId(),
                clusterDescriptionView.getInstanceSize()));
      }
    }

    final ApiAtlasClusterDescription20240805View createdView =
        new ApiAtlasClusterDescription20240805View(
            clusterDescriptionView,
            _ndsUISvc
                .getProcessArgsOptional(pGroup.getId(), clusterDescriptionView.getName())
                .orElse(null),
            _appSettings,
            ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
                view, pNDSGroup),
            pNDSGroup.getCloudProviderContainers(),
            _onlineArchiveSvc.generateFederatedURI(
                pGroup.getId(), view.getName(), view.getMongoDBMajorVersion(), false),
            tags);

    setAdminApiTelemetryAttributes(pRequest, createdView.getName(), createdView.getId());

    return new ApiResponseBuilder(pEnvelope).created().content(createdView).build();
  }

  protected Response updateClusterInner(
      HttpServletRequest pRequest,
      Organization pOrganization,
      Group pGroup,
      NDSGroup pNDSGroup,
      AppUser pAppUser,
      AuditInfo pAuditInfo,
      String pName,
      Boolean pEnvelope,
      ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      ClusterUpdateContext pClusterUpdateContext) {
    if (!FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, _appSettings, null, pGroup)) {
      saveEnableISSAudit(pGroup.getId(), pAuditInfo);
      _featureFlagSvc.enableNonConfigServiceFeatureFlag(
          pGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
      _ndsGroupSvc.setPlanningNow(pGroup.getId());
    }
    try {
      final ClusterDescriptionView existingClusterView =
          _ndsUISvc.getClusterDescription(pGroup.getId(), pName);

      if (!existingClusterView.hasSplitReplicationSpecs()) {
        return new ApiResponseBuilder(pEnvelope)
            .status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode())
            .content(
                new ApiError(
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(),
                    CommonErrorCode.SERVICE_MAINTENANCE.name(),
                    CommonErrorCode.SERVICE_MAINTENANCE.getMessage(),
                    ApiErrorCode.SERVICE_UNAVAILABLE,
                    List.of()))
            .build();
      }

      // this method is called from 20240805 and 20241003, but we're only interested in the older
      // one.
      // We use the supportsIndependentShardAutoScaling context value to determine this
      if (!pClusterUpdateContext.supportsIndependentShardAutoScaling()
          && Objects.equals(existingClusterView.getAutoScalingMode(), AutoScalingMode.SHARD)) {
        final boolean requestContainsAutoScaling =
            pClusterDescriptionView.getReplicationSpecs() != null;
        final RegionConfigView regionConfig =
            existingClusterView.getReplicationSpecList().get(0).getRegionConfigs().get(0);
        final boolean existingClusterAutoScalingEnabled =
            regionConfig.getBaseAutoScaling().getCompute().isEnabled()
                || (regionConfig.getAnalyticsAutoScaling() != null
                    && regionConfig.getAnalyticsAutoScaling().getCompute().isEnabled());

        _ndsUISvc
            .getClusterDescriptionApiShardScalingModeOldApiUpdateCounter()
            .labels(
                "2024-08-05",
                requestContainsAutoScaling ? "autoscaling present" : "autoscaling not present",
                existingClusterAutoScalingEnabled
                    ? "existing autoscaling enabled"
                    : "existing autoscaling disabled")
            .inc();
        LOG.info(
            "updateCluster called with autoscaling present with groupId={} clusterName={}"
                + " apiVersion={} existingAutoscaling={}",
            existingClusterView.getGroupId(),
            existingClusterView.getName(),
            "2024-08-05",
            existingClusterAutoScalingEnabled);
      }

      final ClusterDescriptionView updatedClusterView =
          buildClusterViewForUpdate(
              pClusterDescriptionView, pNDSGroup, pGroup, pName, pClusterUpdateContext, pEnvelope);
      setAdminApiTelemetryAttributes(
          pRequest, updatedClusterView.getName(), updatedClusterView.getUniqueId());

      if (updatedClusterView.isPausedNVMeCluster()) {
        throw ApiErrorCode.CANNOT_PAUSE_NVME_CLUSTER.exception(pEnvelope);
      }

      // Fall back to default process args when PA do not exist for resource policy evaluation.
      final Optional<ClusterDescriptionProcessArgsView> existingProcessArgs =
          _ndsUISvc.getProcessArgsOptional(
              updatedClusterView.getGroupId(), updatedClusterView.getName());
      if (existingProcessArgs.isEmpty()) {
        LOG.warn(
            "No process args document found. Falling back to default process args. {}",
            StructuredArguments.entries(Map.of("groupId", pGroup.getId(), "clusterName", pName)));
      }
      final ClusterDescriptionProcessArgsView mergedProcessArgsView =
          pClusterDescriptionView
              .getAdvancedConfiguration()
              .map(
                  advancedConfiguration ->
                      advancedConfiguration.mergeAndConvertToProcessArgsViewWithoutAdminFields(
                          existingProcessArgs.orElse(
                              ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(
                                  Optional.empty()))))
              .orElse(
                  existingProcessArgs.orElse(
                      ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(
                          Optional.empty())));

      final AtlasResourcePolicyAuthResponse authResult =
          _ndsResourcePolicySvc.isUpdateClusterRequestAuthorized(
              pGroup,
              existingClusterView.getUniqueId(),
              updatedClusterView,
              mergedProcessArgsView,
              pAuditInfo);

      if (authResult.decision().isDeny()) {
        return ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.response(pEnvelope);
      }

      if (FeatureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI,
              _appSettings,
              pOrganization,
              null)
          && pClusterDescriptionView.getAdvancedConfiguration().isPresent()) {
        // Attempt to update process args if advancedConfiguration was specified
        _ndsUISvc.updateProcessArgs(
            pGroup, pName, mergedProcessArgsView, pAuditInfo, null, pAppUser);
      }

      _ndsUISvc.updateCluster(
          pOrganization,
          pNDSGroup.getGroupId(),
          pName,
          updatedClusterView,
          pAppUser,
          pAuditInfo,
          pRequest,
          pClusterUpdateContext);

      if (pClusterDescriptionView.hasTags()) {
        final ClusterDescriptionView clusterViewAfterUpdate =
            _ndsUISvc.getClusterDescription(pGroup.getId(), pName);
        _apiAtlasClusterDescriptionUtil.saveClusterTags(
            clusterViewAfterUpdate,
            pClusterDescriptionView.getTags(),
            pGroup,
            pAuditInfo,
            pEnvelope);
      }
    } catch (final SvcException | UncheckedSvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleClusterUpdateExceptions(pE, pName, pEnvelope);
      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, pName, pGroup, pEnvelope);
      _apiAtlasClusterDescriptionUtil.handleCreateUpdateProcessArgsExceptions(
          pE, pName, pGroup, pEnvelope, _appSettings);
    }

    return getClusterInner(pRequest, pGroup, pNDSGroup, pAuditInfo, pName, pEnvelope);
  }

  protected ClusterDescriptionView buildClusterViewForUpdate(
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final String pName,
      final ClusterUpdateContext pClusterUpdateContext,
      final Boolean pEnvelope) {

    // Check whether the cluster exists and check that it is not a serverless instance
    final ClusterDescriptionView existing =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionViewV2AndAbove(
            pGroup, pName, pEnvelope, true, false);

    // The call above will throw the appropriate error if our cluster is not allowed to be flex
    if (existing.isFreeOrSharedTierTenantCluster() || existing.isFlexTenantCluster()) {
      throw ApiErrorCode.TENANT_CLUSTER_UPDATE_UNSUPPORTED.exception(pEnvelope);
    }
    final ClusterDescriptionProcessArgsView processArgsView =
        _ndsUISvc.getProcessArgsOrDefault(pGroup.getId(), pName);

    final ApiAtlasClusterDescription20240805View existingClusterApiView =
        new ApiAtlasClusterDescription20240805View(
            existing,
            processArgsView,
            _appSettings,
            ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
                existing, pNDSGroup),
            pNDSGroup.getCloudProviderContainers(),
            _onlineArchiveSvc.generateFederatedURI(
                pNDSGroup.getGroupId(), pName, existing.getMongoDBMajorVersion(), false));

    validatePauseStateRequest(pClusterDescriptionView, existingClusterApiView, pEnvelope);
    validateSelfManagedShardingRequest(pClusterDescriptionView, existingClusterApiView, pEnvelope);

    if (Optional.ofNullable(pClusterDescriptionView.getName()).isPresent()
        && !pClusterDescriptionView.getName().equals(existing.getName())) {
      throw ApiErrorCode.CLUSTER_CANNOT_CHANGE_NAME.exception(pEnvelope);
    }

    if (Optional.ofNullable(pClusterDescriptionView.getClusterType()).isPresent()
        && !pClusterDescriptionView.hasValidClusterType()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD);
    }

    if (pClusterDescriptionView.getRootCertType() == ClusterDescription.RootCertType.DST) {
      throw ApiErrorCode.DST_ROOT_CERT_UNSUPPORTED.exception(pEnvelope);
    }

    if (Optional.ofNullable(pClusterDescriptionView.getMongoDBMajorVersion()).isPresent()) {
      ApiAtlasClusterDescriptionUtil.validateMongoDBMajorVersion(
          pClusterDescriptionView.getMongoDBMajorVersion(), pEnvelope);
    }

    final boolean hasReplicationSpecsChange =
        Optional.ofNullable(pClusterDescriptionView.getReplicationSpecs()).isPresent();
    if (hasReplicationSpecsChange) {
      _apiAtlasClusterDescriptionUtil.validateReplicationSpecs(
          pClusterDescriptionView.getReplicationSpecs(),
          pNDSGroup.getGroupId(),
          pClusterUpdateContext.supportsIndependentShardAutoScaling(),
          pEnvelope);
    }

    if (Optional.ofNullable(pClusterDescriptionView.getBiConnector()).isPresent()) {
      ApiAtlasClusterDescriptionUtil.validateBiConnectorForUpdate(
          existingClusterApiView.getBiConnector(),
          pClusterDescriptionView.getBiConnector(),
          hasReplicationSpecsChange
              ? pClusterDescriptionView.getTotalAnalyticsNodeCount()
              : existingClusterApiView.getTotalAnalyticsNodeCount(),
          pEnvelope);
    }

    final ApiAtlasClusterDescription20240805View patchedView =
        pClusterDescriptionView.toUpdatedClusterDescriptionView(existingClusterApiView);

    validateBackupSettings(patchedView, existing, pEnvelope);

    return patchedView.toClusterDescriptionView();
  }

  protected void validatePauseStateRequest(
      final ApiAtlasClusterDescription20240805View pNewClusterDescriptionView,
      final ApiAtlasClusterDescription20240805View pExistingClusterDescriptionView,
      final Boolean pEnvelope) {
    final boolean willUpdatePauseStateOnly =
        willUpdatePauseStateOnly(pNewClusterDescriptionView, pEnvelope);

    if (pExistingClusterDescriptionView.getPaused() && !willUpdatePauseStateOnly) {
      throw ApiErrorCode.CANNOT_UPDATE_PAUSED_CLUSTER.exception(
          pEnvelope, pExistingClusterDescriptionView.getName());
    }

    if (pExistingClusterDescriptionView.getPaused() && pNewClusterDescriptionView.getPaused()) {
      throw ApiErrorCode.CLUSTER_ALREADY_PAUSED.exception(
          pEnvelope, pExistingClusterDescriptionView.getName());
    }

    if (!pExistingClusterDescriptionView.isPaused()
        && Optional.ofNullable(pNewClusterDescriptionView.getPaused()).orElse(false)
        && !willUpdatePauseStateOnly) {
      throw ApiErrorCode.CANNOT_UPDATE_AND_PAUSE_CLUSTER.exception(
          pEnvelope, pNewClusterDescriptionView.getName());
    }
  }

  private boolean willUpdatePauseStateOnly(
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      final Boolean pEnvelope) {
    final JSONObject viewObject;
    try {
      viewObject =
          new JSONObject(
              CustomJacksonJsonProvider.createObjectMapper()
                  .writeValueAsString(pClusterDescriptionView));
    } catch (JsonProcessingException e) {
      LOG.warn("Unable to serialise clusterdescriptionview as json", e);
      throw ApiErrorCode.ATLAS_GENERAL_ERROR.exception(pEnvelope);
    }

    return viewObject.length() == 1
        && viewObject.has(ApiAtlasClusterDescription20240805View.PAUSED_FIELD);
  }

  private void validateSelfManagedShardingRequest(
      final ApiAtlasClusterDescription20240805View pNewClusterDescriptionView,
      final ApiAtlasClusterDescription20240805View pExistingClusterDescriptionView,
      final Boolean pEnvelope) {
    final boolean transitioningFromReplicaSetToGeoSharding =
        Objects.equals(
                pNewClusterDescriptionView.getClusterType(),
                ClusterDescription.ClusterType.GEOSHARDED.toString())
            && Objects.equals(
                pExistingClusterDescriptionView.getClusterType(),
                ClusterDescription.ClusterType.REPLICASET.toString());

    if (pNewClusterDescriptionView.getSelfManagedSharding() != null
        && pNewClusterDescriptionView.isSelfManagedSharding()
            != pExistingClusterDescriptionView.isSelfManagedSharding()
        && !transitioningFromReplicaSetToGeoSharding) {
      throw ApiErrorCode.CANNOT_MODIFY_GLOBAL_CLUSTER_MANAGEMENT_SETTING.exception(
          pEnvelope, pExistingClusterDescriptionView.getName());
    }
  }

  private void validateBackupSettings(
      final ApiAtlasClusterDescription20240805View pNewClusterDescriptionView,
      final ClusterDescriptionView pExistingClusterDescriptionView,
      final Boolean pEnvelope) {
    final boolean providerBackupEnabled = pNewClusterDescriptionView.getBackupEnabled();

    // make sure that nvme clusters have backup enabled
    final boolean isNVMe = pNewClusterDescriptionView.isNVMe();
    if (isNVMe && !providerBackupEnabled) {
      throw ApiErrorCode.NVME_STORAGE_PROVIDER_BACKUP_REQUIRED.exception(pEnvelope);
    }

    if (!providerBackupEnabled) {
      return;
    }

    final boolean legacyBackupEnabled = pExistingClusterDescriptionView.isBackupEnabled();

    if (legacyBackupEnabled) {
      throw ApiErrorCode.CANNOT_ENABLE_BACKUP_WITH_ACTIVE_LEGACY_BACKUP.exception(pEnvelope);
    }
  }

  protected void saveEnableISSAudit(final ObjectId pGroupId, final AuditInfo pAuditInfo) {
    final NDSAudit.Builder builder =
        new NDSAudit.Builder(NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE);
    builder.groupId(pGroupId);
    builder.hidden(true);
    builder.auditInfo(pAuditInfo);
    final Event event = builder.build();
    _auditSvc.saveAuditEvent(event);
  }

  protected Response getClusterInner(
      HttpServletRequest pRequest,
      Group pGroup,
      NDSGroup pNDSGroup,
      AuditInfo pAuditInfo,
      String pClusterName,
      Boolean pEnvelope) {
    final ClusterDescriptionView view =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionViewV2AndAbove(
            pGroup, pClusterName, pEnvelope, true, false);

    setAdminApiTelemetryAttributes(pRequest, pClusterName, view.getUniqueId());

    final ApiView returnObject;

    if (_apiAtlasClusterDescriptionUtil.isFlexShimLogicEnabled(pGroup)
        && view.isFlexTenantCluster()) {
      final FreeInstanceSize formerInstanceSize =
          (FreeInstanceSize)
              view.getFlexTenantMigrationState().orElseThrow().getValidMigrationInstanceSize();
      returnObject =
          _legacySharedApiShimToFlexSvc.convertFlexClusterDescriptionViewToSharedResponse(
              view,
              formerInstanceSize,
              pNDSGroup,
              _apiAtlasClusterDescriptionUtil.getClusterTags(view, pGroup),
              pEnvelope,
              VersionMediaType.V_2024_08_05_EXTENSION_TYPE);
    } else {
      returnObject =
          new ApiAtlasClusterDescription20240805View(
              view,
              _ndsUISvc.getProcessArgsOptional(pGroup.getId(), pClusterName).orElse(null),
              _appSettings,
              ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
                  view, pNDSGroup),
              pNDSGroup.getCloudProviderContainers(),
              _onlineArchiveSvc.generateFederatedURI(
                  pGroup.getId(), pClusterName, view.getMongoDBMajorVersion(), false),
              _apiAtlasClusterDescriptionUtil.getClusterTags(view, pGroup));
    }

    return new ApiResponseBuilder(pEnvelope).ok().content(returnObject).build();
  }

  private void setAdminApiTelemetryAttributes(
      final HttpServletRequest pRequest, final String pName, final ObjectId pClusterId) {
    if (FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ENABLE_API_TELEMETRY_CUSTOM_FIELDS, _appSettings, null, null)) {
      _apiTelemetryAttributeSvc.putStandardAttribute(pRequest, CLUSTER_NAME, pName);
      if (pClusterId != null) {
        _apiTelemetryAttributeSvc.putCustomAttribute(
            pRequest, CustomTelemetryFieldKey.CLUSTER_ID, pClusterId.toString());
      }
    }
  }
}
