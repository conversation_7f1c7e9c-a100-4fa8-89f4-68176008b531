package com.xgen.cloud.nds.project.runtime.res.api_2024_08_05;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.OPERATION_ID_OVERRIDE;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.VERB_OVERRIDE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetryAttributeSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.versioning._public.annotation.Sunset;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.common.versioning._public.constants.VersioningConstants;
import com.xgen.cloud.common.view._public.base.ApiListView;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterUpdateContext;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex.runtime.svc.LegacySharedApiShimToFlexSvc;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.GeoSharding;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project.runtime.res.api_2024_10_23.ApiAtlasClusterDescriptionResource20241023;
import com.xgen.cloud.nds.resourcepolicy._public.model.AtlasResourcePolicyAuthResponse;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.atlas.ApiAtlasClusterDescriptionV15Resource.PaginatedClusterDescriptionV15View;
import com.xgen.svc.mms.api.res.atlas.ApiAtlasLegacyClusterDescriptionResource.PaginatedLegacyClusterView;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.util.ApiAtlasClusterDescriptionUtil;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionConnectionStringsUtil;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionProcessArgsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionV15View;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasCustomZoneMappingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasEmployeeAccessGrantView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasGeoShardingView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasLegacyClusterDescriptionView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasManagedNamespacesView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasZoneMappingView;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescription20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescriptionAutoScalingModeConfiguration20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescriptionProcessArgs20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasGeoSharding20240805View;
import com.xgen.svc.mms.res.filter.AllowTemporaryApiKeys;
import com.xgen.svc.mms.res.filter.PaidInFull;
import com.xgen.svc.mms.res.filter.SubscriptionPlan;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.svc.nds.model.ui.ReplicationSpecView;
import com.xgen.svc.nds.svc.NDSResourcePolicySvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.svc.nds.util.GeoShardingLocationUtil;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SubscriptionPlan(PlanTypeSet.NDS)
@PaidInFull
@Path("/api/atlas/v2/groups/{groupId}/clusters")
@Singleton
public class ApiAtlasClusterDescriptionResource20240805
    extends ApiAtlasClusterDescriptionResource20241023 {

  private static final Logger LOG =
      LoggerFactory.getLogger(ApiAtlasClusterDescriptionResource20240805.class);
  private final ApiAtlasClusterDescriptionUtil _apiAtlasClusterDescriptionUtil;
  private final AppSettings _appSettings;
  private final NDSUISvc _ndsUISvc;
  private final OnlineArchiveSvc _onlineArchiveSvc;
  private final FeatureFlagSvc _featureFlagSvc;
  private final NDSGroupSvc _ndsGroupSvc;
  private final AuditSvc _auditSvc;
  private final NDSResourcePolicySvc _ndsResourcePolicySvc;
  private final NDSClusterSvc _ndsClusterSvc;
  private final LegacySharedApiShimToFlexSvc _legacySharedApiShimToFlexSvc;

  @Inject
  public ApiAtlasClusterDescriptionResource20240805(
      final ApiAtlasClusterDescriptionUtil pApiAtlasClusterDescriptionUtil,
      final AppSettings pAppSettings,
      final NDSUISvc pNDSUISvc,
      final OnlineArchiveSvc pOnlineArchiveSvc,
      final FeatureFlagSvc pFeatureFlagSvc,
      final NDSGroupSvc pNdsGroupSvc,
      final AuditSvc pAuditSvc,
      final NDSResourcePolicySvc pNDSResourcePolicySvc,
      final NDSClusterSvc pNDSClusterSvc,
      final LegacySharedApiShimToFlexSvc pLegacySharedApiShimToFlexSvc,
      final ApiTelemetryAttributeSvc pApiTelemetryAttributeSvc) {
    super(
        pApiAtlasClusterDescriptionUtil,
        pAppSettings,
        pNDSUISvc,
        pOnlineArchiveSvc,
        pFeatureFlagSvc,
        pNdsGroupSvc,
        pAuditSvc,
        pNDSResourcePolicySvc,
        pNDSClusterSvc,
        pLegacySharedApiShimToFlexSvc,
        pApiTelemetryAttributeSvc);
    _apiAtlasClusterDescriptionUtil = pApiAtlasClusterDescriptionUtil;
    _appSettings = pAppSettings;
    _ndsUISvc = pNDSUISvc;
    _onlineArchiveSvc = pOnlineArchiveSvc;
    _featureFlagSvc = pFeatureFlagSvc;
    _ndsGroupSvc = pNdsGroupSvc;
    _auditSvc = pAuditSvc;
    _ndsResourcePolicySvc = pNDSResourcePolicySvc;
    _ndsClusterSvc = pNDSClusterSvc;
    _legacySharedApiShimToFlexSvc = pLegacySharedApiShimToFlexSvc;
  }

  @GET
  @Path("/{clusterName}")
  @Produces({VersionMediaType.V_2024_08_05_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY, RoleSet.NAME.GLOBAL_BAAS_INTERNAL_ADMIN})
  @AllowTemporaryApiKeys
  @Operation(
      summary = "Return One Cluster from One Project",
      operationId = "getGroupCluster",
      description =
          "Returns the details for one cluster in the specified project. Clusters contain a group"
              + " of hosts that maintain the same data set. The response includes clusters with"
              + " asymmetrically-sized shards. To use this resource, the requesting Service Account"
              + " or API Key must have the Project Read Only role. This feature is not available"
              + " for serverless clusters.\n\n"
              + "This endpoint can also be used on Flex clusters that were created using the"
              + " [createCluster](https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Clusters/operation/createCluster)"
              + " endpoint or former M2/M5 clusters that have been migrated to Flex clusters until"
              + " January 2026. Please use the getFlexCluster endpoint for Flex clusters instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "getFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/getFlexCluster"),
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema = @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_11_30)
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescriptionV15View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_02_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema = @Schema(implementation = ApiAtlasClusterDescription20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  }),
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty( // TODO CLOUDP-309177
                  name = "xgen-IPA-117-description-should-not-use-inline-links",
                  value = "Description has multiple links, and externalDocs only supports one."),
            }),
        @Extension(
            properties = {@ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getCluster")}),
      })
  public Response getCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    return getClusterInner(pRequest, pGroup, pNDSGroup, pAuditInfo, pClusterName, pEnvelope);
  }

  @GET
  @Path("/{clusterName}/autoScalingConfiguration")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-113-singleton-should-have-update-method",
            value = "API predates IPA validation.")
      })
  @Produces({VersionMediaType.V_2024_08_05_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY, RoleSet.NAME.GLOBAL_BAAS_INTERNAL_ADMIN})
  @AllowTemporaryApiKeys
  @Deprecated(since = "2024-10-23")
  @Operation(
      summary = "Return Auto Scaling Configuration for One Sharded Cluster",
      operationId = "autoGroupClusterScalingConfiguration",
      description =
          "Returns the internal configuration of AutoScaling for sharded clusters. This endpoint"
              + " can be used for diagnostic purposes to ensure that sharded clusters updated from"
              + " older APIs have gained support for AutoScaling each shard independently.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema =
                      @Schema(
                          implementation =
                              ApiAtlasClusterDescriptionAutoScalingModeConfiguration20240805View
                                  .class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  }),
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "customMethod", value = "True", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "autoScalingConfiguration")
            }),
      })
  public Response getAutoScalingMode(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    final AutoScalingMode autoScalingMode =
        _apiAtlasClusterDescriptionUtil
            .getClusterDescriptionViewV2AndAbove(pGroup, pClusterName, pEnvelope, true, false)
            .getAutoScalingMode();
    return new ApiResponseBuilder(pEnvelope)
        .ok()
        .content(
            ApiAtlasClusterDescriptionAutoScalingModeConfiguration20240805View.forAutoScalingMode(
                autoScalingMode))
        .build();
  }

  @GET
  @Produces({VersionMediaType.V_2024_08_05_JSON})
  @RolesAllowed({
    RoleSet.NAME.GROUP_READ_ONLY,
    RoleSet.NAME.GLOBAL_BAAS_INTERNAL_ADMIN,
    NAME.GLOBAL_CHARTS_ADMIN
  })
  @AllowTemporaryApiKeys
  @Operation(
      summary = "Return All Clusters in One Project",
      operationId = "listGroupClusters",
      description =
          "Returns the details for all clusters in the specific project to which you have access."
              + " Clusters contain a group of hosts that maintain the same data set. The response"
              + " includes clusters with asymmetrically-sized shards. To use this resource, the"
              + " requesting Service Account or API Key must have the Project Read Only role. This"
              + " feature is not  available for serverless clusters.\n\n"
              + "This endpoint can also be used on Flex clusters that were created using the"
              + " [createCluster](https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Clusters/operation/createCluster)"
              + " endpoint or former M2/M5 clusters that have been migrated to Flex clusters until"
              + " January 2026. Please use the listFlexClusters endpoint for Flex clusters"
              + " instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "listFlexClusters",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/listFlexClusters"),
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "includeCount"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "includeDeletedWithRetainedBackups",
            description = "Flag that indicates whether to return Clusters with retain backups.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "boolean", defaultValue = "false"))
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema = @Schema(implementation = PaginatedLegacyClusterView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_11_30)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = PaginatedClusterDescriptionV15View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_02_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema = @Schema(implementation = PaginatedClusterDescription20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                  }),
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty( // TODO CLOUDP-309177
                  name = "xgen-IPA-117-description-should-not-use-inline-links",
                  value = "Description has multiple links, and externalDocs only supports one."),
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listClusters")
            }),
      })
  public Response getAllClusters(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @QueryParam("includeDeletedWithRetainedBackups")
          final boolean pIncludeDeletedWithRetainedBackups)
      throws Exception {

    final List<ClusterDescriptionView> clusters =
        _ndsUISvc
            .getClusterDescriptions(pGroup.getId(), false, pIncludeDeletedWithRetainedBackups)
            .stream()
            .filter(cluster -> !cluster.isServerlessTenantCluster())
            // we only want to include flex clusters if our shim logic is enabled AND our flex was
            // created from our shim logic or migrated from shared
            .filter(
                cluster ->
                    !cluster.isFlexTenantCluster()
                        || _apiAtlasClusterDescriptionUtil.isFlexClusterValid(cluster, pGroup))
            .map(
                cd ->
                    _apiAtlasClusterDescriptionUtil.getClusterDescriptionViewV2AndAbove(
                        pGroup, cd.getName(), pEnvelope, true, pIncludeDeletedWithRetainedBackups))
            .toList();

    Optional<ClusterDescriptionView> cd =
        clusters.stream().filter(cluster -> !cluster.hasSplitReplicationSpecs()).findFirst();
    if (cd.isPresent()) {
      LOG.error(
          "Somehow split a clusterdescription that did not split correctly: {}",
          new ObjectMapper().writeValueAsString(cd));
      throw new IllegalStateException(
          "Somehow split a clusterdescription that did not split correctly");
    }

    final Map<ObjectId, Optional<ClusterDescriptionProcessArgsView>> clusterIdToProcessArgs =
        _ndsUISvc.getProcessArgsViewsForClusters(clusters);

    final List<ApiAtlasClusterDescription20240805View> apiClusterViews = new ArrayList<>();
    clusters.forEach(
        view -> {
          if (_apiAtlasClusterDescriptionUtil.isFlexShimLogicEnabled(pGroup)
              && view.isFlexTenantCluster()) {
            final FreeInstanceSize formerInstanceSize =
                (FreeInstanceSize)
                    view.getFlexTenantMigrationState()
                        .orElseThrow()
                        .getValidMigrationInstanceSize();
            apiClusterViews.add(
                (ApiAtlasClusterDescription20240805View)
                    _legacySharedApiShimToFlexSvc.convertFlexClusterDescriptionViewToSharedResponse(
                        view,
                        formerInstanceSize,
                        pNDSGroup,
                        _apiAtlasClusterDescriptionUtil.getClusterTags(view, pGroup),
                        pEnvelope,
                        VersionMediaType.V_2024_08_05_EXTENSION_TYPE));
          } else {
            apiClusterViews.add(
                new ApiAtlasClusterDescription20240805View(
                    view,
                    clusterIdToProcessArgs.get(view.getUniqueId()).orElse(null),
                    _appSettings,
                    ApiAtlasClusterDescriptionConnectionStringsUtil
                        .shouldExposePrivateStringsForCluster(view, pNDSGroup),
                    pNDSGroup.getCloudProviderContainers(),
                    _onlineArchiveSvc.generateFederatedURI(
                        pGroup.getId(), view.getName(), view.getMongoDBMajorVersion(), false),
                    _apiAtlasClusterDescriptionUtil.getClusterTags(view, pGroup)));
          }
        });

    return handlePagination(pRequest, apiClusterViews, pEnvelope);
  }

  /** See https://github.com/swagger-api/swagger-core/issues/3496 */
  @Schema(name = "PaginatedClusterDescription20240805")
  public static class PaginatedClusterDescription20240805View
      extends ApiListView<ApiAtlasClusterDescription20240805View> {}

  @POST
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed(NAME.GROUP_ATLAS_ADMIN)
  @Deprecated(since = "2024-10-23")
  @Sunset("2026-03-01")
  @Hidden
  public Response createCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      @Context final AppUser pCurrentUser)
      throws Exception {
    return createClusterInner(
        pRequest,
        pOrganization,
        pGroup,
        pNDSGroup,
        pAuditInfo,
        pEnvelope,
        pClusterDescriptionView,
        pCurrentUser,
        ClusterCreateContext.forClusterApi(false));
  }

  @PATCH
  @Path("/{clusterName}")
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({NAME.GROUP_CLUSTER_MANAGER})
  @Deprecated(since = "2024-10-23")
  @Sunset("2026-03-01")
  @Hidden
  public Response updateCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView)
      throws Exception {
    return updateClusterInner(
        pRequest,
        pOrganization,
        pGroup,
        pNDSGroup,
        pAppUser,
        pAuditInfo,
        pName,
        pEnvelope,
        pClusterDescriptionView,
        ClusterUpdateContext.forClusterApi(false));
  }

  @GET
  @Path("/{clusterName}/processArgs")
  @Produces({VersionMediaType.V_2024_08_05_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY})
  @AllowTemporaryApiKeys
  @Operation(
      summary = "Return Advanced Configuration Options for One Cluster",
      operationId = "getGroupClusterProcessArgs",
      description =
          "Returns the advanced configuration details for one cluster in the specified project."
              + " Clusters contain a group of hosts that maintain the same data set. Advanced"
              + " configuration details include the read/write concern, index and oplog limits, and"
              + " other database settings. This feature isn't available for `M0` free clusters,"
              + " `M2` and `M5` shared-tier clusters, flex clusters, or serverless clusters. To use"
              + " this resource, the requesting Service Account or API Key must have the Project"
              + " Read Only role.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema =
                      @Schema(implementation = ApiAtlasClusterDescriptionProcessArgsView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema =
                      @Schema(
                          implementation = ApiAtlasClusterDescriptionProcessArgs20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getProcessArgs")
            }),
      })
  public Response getProcessArgs(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope)
      throws Exception {

    // Checks that the cluster is not serverless or invalid flex
    _apiAtlasClusterDescriptionUtil.getClusterDescriptionViewV2AndAbove(
        pGroup, pClusterName, pEnvelope, true, false);

    return _apiAtlasClusterDescriptionUtil.getProcessArgs(
        pGroup, pClusterName, pEnvelope, ApiAtlasClusterDescriptionProcessArgs20240805View.class);
  }

  @PATCH
  @Path("/{clusterName}/processArgs")
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({NAME.GROUP_CLUSTER_MANAGER})
  @Operation(
      summary = "Update Advanced Configuration Options for One Cluster",
      operationId = "updateGroupClusterProcessArgs",
      description =
          "Updates the advanced configuration details for one cluster in the specified project."
              + " Clusters contain a group of hosts that maintain the same data set. Advanced"
              + " configuration details include the read/write concern, index and oplog limits, and"
              + " other database settings. To use this resource, the requesting Service Account or"
              + " API Key must have the Project Cluster Manager role. This feature isn't available"
              + " for `M0` free clusters, `M2` and `M5` shared-tier clusters, flex clusters, or"
              + " serverless clusters.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema =
                      @Schema(implementation = ApiAtlasClusterDescriptionProcessArgsView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema =
                      @Schema(
                          implementation = ApiAtlasClusterDescriptionProcessArgs20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description =
                  "Advanced configuration details to add for one cluster in the specified"
                      + " project.",
              content = {
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema =
                        @Schema(implementation = ApiAtlasClusterDescriptionProcessArgsView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                value = "API predates IPA validation.")
                          })
                    }),
                @Content(
                    mediaType = VersionMediaType.V_2024_08_05_JSON,
                    schema =
                        @Schema(
                            implementation =
                                ApiAtlasClusterDescriptionProcessArgs20240805View.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name =
                                    "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                value = "API predates IPA validation.")
                          })
                    })
              }),
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "updateProcessArgs")
            }),
      })
  public Response updateProcessArgs(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @Context final Organization pOrganization,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasClusterDescriptionProcessArgs20240805View pProcessArgsView)
      throws Exception {
    if (!FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, _appSettings, null, pGroup)) {
      saveEnableISSAudit(pGroup.getId(), pAuditInfo);
      _featureFlagSvc.enableNonConfigServiceFeatureFlag(
          pGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
      _ndsGroupSvc.setPlanningNow(pGroup.getId());
    }

    try {
      final ClusterDescriptionView cdView =
          _ndsUISvc.getClusterDescription(pGroup.getId(), pClusterName);
      final AtlasResourcePolicyAuthResponse authResult =
          _ndsResourcePolicySvc.isUpdateClusterRequestAuthorized(
              pGroup,
              cdView.getUniqueId(),
              cdView,
              pProcessArgsView.toClusterDescriptionProcessArgsView(),
              pAuditInfo);

      if (authResult.decision().isDeny()) {
        return ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.response(pEnvelope);
      }
    } catch (final SvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
      throw ApiErrorCode.ATLAS_GENERAL_ERROR.exception(pEnvelope, pE.getMessageParams());
    }

    try {
      // updateProcessArgs does not allow updates for free, serverless, or invalid flex clusters
      _ndsUISvc.updateProcessArgs(
          pGroup,
          pClusterName,
          pProcessArgsView.toClusterDescriptionProcessArgsView(),
          pAuditInfo,
          null,
          pAppUser);
    } catch (final SvcException | UncheckedSvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleCreateUpdateProcessArgsExceptions(
          pE, pClusterName, pGroup, pEnvelope, _appSettings);
      LOG.warn(pE.getErrorCode().getMessage() + ": " + pE.getMessage());
      throw ApiErrorCode.UNEXPECTED_ERROR.exception(pEnvelope);
    }
    return _apiAtlasClusterDescriptionUtil.getProcessArgs(
        pGroup, pClusterName, pEnvelope, pProcessArgsView.getClass());
  }

  @GET
  @Path("/{clusterName}/globalWrites")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-113-singleton-should-have-update-method",
            value = "API predates IPA validation.")
      })
  @Produces({VersionMediaType.V_2024_08_05_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY})
  @AllowTemporaryApiKeys
  @Operation(
      summary = "Return One Managed Namespace in One Global Cluster",
      operationId = "getGroupClusterGlobalWrites",
      description =
          "Returns one managed namespace within the specified global cluster. A managed namespace"
              + " identifies a collection using the database name, the dot separator, and the"
              + " collection name. To use this resource, the requesting Service Account or API Key"
              + " must have the Project Read Only role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_11_30)
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_02_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoSharding20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  })
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getClusterGlobalWrites")
            })
      },
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"))
  public Response getGeoSharding(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    // Checks that the cluster is not serverless or flex
    final ClusterDescriptionView view =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionViewV2AndAbove(
            pGroup, pClusterName, pEnvelope, true, false);

    return new ApiResponseBuilder(pEnvelope)
        .ok()
        .content(
            new ApiAtlasGeoSharding20240805View(
                view.getGeoShardingView(), view.getReplicationSpecList()))
        .build();
  }

  @POST
  @Path("/{clusterName}/globalWrites/managedNamespaces")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "API predates IPA validation."),
      })
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN})
  @Operation(
      summary = "Create One Managed Namespace in One Global Cluster",
      operationId = "createGroupClusterGlobalWriteManagedNamespace",
      description =
          "Creates one managed namespace within the specified global cluster. A managed namespace"
              + " identifies a collection using the database name, the dot separator, and the"
              + " collection name. To use this resource, the requesting Service Account or API Key"
              + " must have the Project Data Access Admin role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_11_30)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_02_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoSharding20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "405", ref = "methodNotAllowed"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Managed namespace to create within the specified global cluster.",
              content = {
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema = @Schema(implementation = ApiAtlasManagedNamespacesView.class)),
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiAtlasManagedNamespacesView.class)),
                @Content(
                    mediaType = VersionMediaType.V_2024_08_05_JSON,
                    schema = @Schema(implementation = ApiAtlasManagedNamespacesView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                          })
                    })
              }),
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "createManagedNamespace")
            })
      })
  public Response addManagedNamespace(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasManagedNamespacesView pManagedNamespacesView) {
    if (!FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, _appSettings, null, pGroup)) {
      saveEnableISSAudit(pGroup.getId(), pAuditInfo);
      _featureFlagSvc.enableNonConfigServiceFeatureFlag(
          pGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
      _ndsGroupSvc.setPlanningNow(pGroup.getId());
    }

    final String db = pManagedNamespacesView.getDb();
    final String collection = pManagedNamespacesView.getCollection();
    final String customShardKey = pManagedNamespacesView.getCustomShardKey();
    final boolean isShardKeyUnique = pManagedNamespacesView.isShardKeyUnique();
    final boolean isCustomShardKeyHashed = pManagedNamespacesView.isCustomShardKeyHashed();
    final boolean presplitHashedZones = pManagedNamespacesView.isPresplitHashedZones();
    final Long numInitialChunks = pManagedNamespacesView.getNumInitialChunks();

    final List<String> missingAttributes = new ArrayList<>();
    if (db == null || db.isEmpty()) {
      missingAttributes.add(ApiAtlasManagedNamespacesView.DB_FIELD);
    }
    if (collection == null || collection.isEmpty()) {
      missingAttributes.add(ApiAtlasManagedNamespacesView.COLLECTION_FIELD);
    }
    if (customShardKey == null || customShardKey.isEmpty()) {
      missingAttributes.add(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD);
    }

    if (missingAttributes.size() > 0) {
      throw ApiErrorCode.MISSING_ATTRIBUTES.exception(pEnvelope, missingAttributes);
    }

    // Checks that the cluster is not serverless or flex
    final ClusterDescriptionView updatedClusterView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionViewV2AndAbove(
            pGroup, pClusterName, pEnvelope, true, false);

    if (!updatedClusterView.getClusterType().equals(ClusterType.GEOSHARDED.name())) {
      throw ApiErrorCode.NOT_GLOBAL_WRITES_CLUSTER.exception(
          pEnvelope, pClusterName, pGroup.getId());
    }

    final GeoSharding updatedGeoSharding = updatedClusterView.getGeoShardingView().toGeoSharding();
    updatedGeoSharding.addManagedNamespace(
        db,
        collection,
        customShardKey,
        isShardKeyUnique,
        isCustomShardKeyHashed,
        presplitHashedZones,
        numInitialChunks);
    updatedClusterView.setGeoShardingView(new GeoShardingView(updatedGeoSharding));

    try {
      if (!_ndsUISvc.clusterDescriptionHasSplitReplicationSpecs(pGroup.getId(), pClusterName)) {
        return new ApiResponseBuilder(pEnvelope)
            .status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode())
            .content(
                new ApiError(
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(),
                    CommonErrorCode.SERVICE_MAINTENANCE.name(),
                    CommonErrorCode.SERVICE_MAINTENANCE.getMessage(),
                    ApiErrorCode.SERVICE_UNAVAILABLE,
                    List.of()))
            .build();
      }

      _ndsUISvc.updateCluster(
          pOrganization,
          pGroup.getId(),
          pClusterName,
          updatedClusterView,
          pAppUser,
          pAuditInfo,
          pRequest,
          ClusterUpdateContext.forClusterApi(false));
    } catch (final SvcException pE) {
      if (NDSErrorCode.INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE.equals(pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE.exception(
            pEnvelope, "managed namespaces", pClusterName);
      }

      if (NDSErrorCode.DUPLICATE_MANAGED_NAMESPACE.equals(pE.getErrorCode())) {
        throw ApiErrorCode.DUPLICATE_MANAGED_NAMESPACE.exception(pEnvelope, db + "." + collection);
      }

      if (NDSErrorCode.MANAGED_NAMESPACE_CANNOT_ALREADY_BE_SHARDED.equals(pE.getErrorCode())) {
        throw ApiErrorCode.MANAGED_NAMESPACE_CANNOT_ALREADY_BE_SHARDED.exception(
            pEnvelope, db + "." + collection);
      }

      if (NDSErrorCode.INVALID_SHARD_KEY_CONFIGURATION.equals(pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_SHARD_KEY_CONFIGURATION.exception(
            pEnvelope, pE.getMessageParams().get(0));
      }

      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }
    return getGeoSharding(pGroup, pAuditInfo, pClusterName, pEnvelope);
  }

  @POST
  @Path("/{clusterName}/globalWrites/customZoneMapping")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "API predates IPA validation."),
      })
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Add One Custom Zone Mapping to One Global Cluster",
      operationId = "createGroupClusterGlobalWriteCustomZoneMapping",
      description =
          "Creates one custom zone mapping for the specified global cluster. A custom zone mapping"
              + " matches one ISO 3166-2 location code to a zone in your global cluster. By"
              + " default, MongoDB Cloud maps each location code to the closest geographical zone."
              + " To use this resource, the requesting Service Account or API Key must have the"
              + " Project Owner role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_11_30)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_02_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoSharding20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Custom zone mapping to add to the specified global cluster.",
              content = {
                @Content(
                    mediaType = VersionMediaType.V_2023_01_01_JSON,
                    schema = @Schema(implementation = ApiAtlasCustomZoneMappingsView.class)),
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiAtlasCustomZoneMappingsView.class)),
                @Content(
                    mediaType = VersionMediaType.V_2024_08_05_JSON,
                    schema = @Schema(implementation = ApiAtlasCustomZoneMappingsView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                          })
                    })
              }),
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "createCustomZoneMapping")
            })
      })
  public Response addCustomZoneMappings(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasCustomZoneMappingsView pCustomZoneMappingsView) {
    if (!FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, _appSettings, null, pGroup)) {
      saveEnableISSAudit(pGroup.getId(), pAuditInfo);
      _featureFlagSvc.enableNonConfigServiceFeatureFlag(
          pGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
      _ndsGroupSvc.setPlanningNow(pGroup.getId());
    }

    // Checks that the cluster is not serverless or flex
    final ClusterDescriptionView updatedClusterView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionViewV2AndAbove(
            pGroup, pClusterName, pEnvelope, true, false);

    final GeoSharding originalGeoSharding = updatedClusterView.getGeoShardingView().toGeoSharding();
    final Map<String, ObjectId> updatedCustomZoneMappings =
        originalGeoSharding.getCustomZoneMapping();

    for (final ApiAtlasZoneMappingView mapping : pCustomZoneMappingsView.getCustomZoneMappings()) {
      final String isoCode = mapping.getLocation();
      final String zoneName = mapping.getZone();

      if (!GeoShardingLocationUtil.isValidLocationCode(isoCode)) {
        throw ApiErrorCode.INVALID_LOCATION_CODE.exception(pEnvelope, isoCode);
      }

      final List<ReplicationSpecView> replicationSpecView =
          updatedClusterView.getReplicationSpecViewsByZone(zoneName);
      if (replicationSpecView.isEmpty()) {
        throw ApiErrorCode.INVALID_ATTRIBUTE.exception(pEnvelope, zoneName);
      }

      updatedCustomZoneMappings.put(isoCode, replicationSpecView.get(0).getId());
    }

    final GeoSharding updatedGeoSharding =
        new GeoSharding(
            updatedCustomZoneMappings,
            originalGeoSharding.getManagedNamespaces(),
            originalGeoSharding.isSelfManagedSharding());
    updatedClusterView.setGeoShardingView(new GeoShardingView(updatedGeoSharding));

    try {
      if (!_ndsUISvc.clusterDescriptionHasSplitReplicationSpecs(pGroup.getId(), pClusterName)) {
        return new ApiResponseBuilder(pEnvelope)
            .status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode())
            .content(
                new ApiError(
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(),
                    CommonErrorCode.SERVICE_MAINTENANCE.name(),
                    CommonErrorCode.SERVICE_MAINTENANCE.getMessage(),
                    ApiErrorCode.SERVICE_UNAVAILABLE,
                    List.of()))
            .build();
      }
      _ndsUISvc.updateCluster(
          pOrganization,
          pGroup.getId(),
          pClusterName,
          updatedClusterView,
          pAppUser,
          pAuditInfo,
          pRequest,
          ClusterUpdateContext.forClusterApi(false));
    } catch (final SvcException pE) {
      if (NDSErrorCode.INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE.equals(pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE.exception(
            pEnvelope, "zone mappings", pClusterName);
      }

      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }
    return getGeoSharding(pGroup, pAuditInfo, pClusterName, pEnvelope);
  }

  @DELETE
  @Path("/{clusterName}/globalWrites/managedNamespaces")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "API predates IPA validation."),
      })
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN})
  @Operation(
      summary = "Remove One Managed Namespace from One Global Cluster",
      operationId = "deleteGroupClusterGlobalWriteManagedNamespaces",
      description =
          "Removes one managed namespace within the specified global cluster. A managed namespace"
              + " identifies a collection using the database name, the dot separator, and the"
              + " collection name. Deleting a managed namespace does not remove the associated"
              + " collection or data. To use this resource, the requesting Service Account or API"
              + " Key must have the Project Data Access Admin role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "db",
            in = ParameterIn.QUERY,
            description =
                "Human-readable label that identifies the database that contains the collection."),
        @Parameter(
            name = "collection",
            description =
                "Human-readable label that identifies the collection associated with the managed"
                    + " namespace.",
            in = ParameterIn.QUERY)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_11_30)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_02_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoSharding20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "deleteManagedNamespaces")
            })
      })
  public Response removeManagedNamespace(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("db") final String pDbName,
      @Parameter(hidden = true) @QueryParam("collection") final String pCollectionName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    if (!FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, _appSettings, null, pGroup)) {
      saveEnableISSAudit(pGroup.getId(), pAuditInfo);
      _featureFlagSvc.enableNonConfigServiceFeatureFlag(
          pGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
      _ndsGroupSvc.setPlanningNow(pGroup.getId());
    }

    if (pDbName == null) {
      throw ApiErrorCode.MISSING_QUERY_PARAMETER.exception(pEnvelope, "db");
    }
    if (pCollectionName == null) {
      throw ApiErrorCode.MISSING_QUERY_PARAMETER.exception(pEnvelope, "collection");
    }

    // Checks that the cluster is not serverless or flex
    final ClusterDescriptionView updatedClusterView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionViewV2AndAbove(
            pGroup, pClusterName, pEnvelope, true, false);

    final GeoSharding updatedGeoSharding = updatedClusterView.getGeoShardingView().toGeoSharding();
    updatedGeoSharding.removeManagedNamespace(pDbName, pCollectionName);
    updatedClusterView.setGeoShardingView(new GeoShardingView(updatedGeoSharding));

    try {
      if (!_ndsUISvc.clusterDescriptionHasSplitReplicationSpecs(pGroup.getId(), pClusterName)) {
        return new ApiResponseBuilder(pEnvelope)
            .status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode())
            .content(
                new ApiError(
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(),
                    CommonErrorCode.SERVICE_MAINTENANCE.name(),
                    CommonErrorCode.SERVICE_MAINTENANCE.getMessage(),
                    ApiErrorCode.SERVICE_UNAVAILABLE,
                    List.of()))
            .build();
      }
      _ndsUISvc.updateCluster(
          pOrganization,
          pGroup.getId(),
          pClusterName,
          updatedClusterView,
          pAppUser,
          pAuditInfo,
          pRequest,
          ClusterUpdateContext.forClusterApi(false));
    } catch (final SvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }

    return getGeoSharding(pGroup, pAuditInfo, pClusterName, pEnvelope);
  }

  @DELETE
  @Path("/{clusterName}/globalWrites/customZoneMapping")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "API predates IPA validation."),
      })
  @Produces(VersionMediaType.V_2024_08_05_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Remove All Custom Zone Mappings from One Global Cluster",
      operationId = "deleteGroupClusterGlobalWriteCustomZoneMapping",
      description =
          "Removes all custom zone mappings for the specified global cluster. A custom zone mapping"
              + " matches one ISO 3166-2 location code to a zone in your global cluster. Removing"
              + " the custom zone mappings restores the default mapping. By default, MongoDB Cloud"
              + " maps each location code to the closest geographical zone. To use this resource,"
              + " the requesting Service Account or API Key must have the Project Owner role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_01_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_11_30)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoShardingView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2023_02_01_EXTENSION_TYPE)
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_SUNSET,
                              value = VersioningConstants.X_SUNSET_DATE_2026_03_01)
                        })
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  schema = @Schema(implementation = ApiAtlasGeoSharding20240805View.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        }),
                  })
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"),
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "deleteCustomZoneMapping")
            })
      })
  public Response removeCustomZoneMappings(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    if (!FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, _appSettings, null, pGroup)) {
      saveEnableISSAudit(pGroup.getId(), pAuditInfo);
      _featureFlagSvc.enableNonConfigServiceFeatureFlag(
          pGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
      _ndsGroupSvc.setPlanningNow(pGroup.getId());
    }

    // Checks that the cluster is not serverless or flex
    final ClusterDescriptionView updatedClusterView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionViewV2AndAbove(
            pGroup, pClusterName, pEnvelope, true, false);

    final GeoSharding originalGeoSharding = updatedClusterView.getGeoShardingView().toGeoSharding();
    final GeoSharding updatedGeoSharding =
        new GeoSharding(
            new HashMap<>(),
            originalGeoSharding.getManagedNamespaces(),
            originalGeoSharding.isSelfManagedSharding());
    updatedClusterView.setGeoShardingView(new GeoShardingView(updatedGeoSharding));

    try {
      if (!_ndsUISvc.clusterDescriptionHasSplitReplicationSpecs(pGroup.getId(), pClusterName)) {
        return new ApiResponseBuilder(pEnvelope)
            .status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode())
            .content(
                new ApiError(
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(),
                    CommonErrorCode.SERVICE_MAINTENANCE.name(),
                    CommonErrorCode.SERVICE_MAINTENANCE.getMessage(),
                    ApiErrorCode.SERVICE_UNAVAILABLE,
                    List.of()))
            .build();
      }
      _ndsUISvc.updateCluster(
          pOrganization,
          pGroup.getId(),
          pClusterName,
          updatedClusterView,
          pAppUser,
          pAuditInfo,
          pRequest,
          ClusterUpdateContext.forClusterApi(false));
    } catch (final SvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }

    return getGeoSharding(pGroup, pAuditInfo, pClusterName, pEnvelope);
  }

  @POST
  @Path("/{clusterName}:grantMongoDBEmployeeAccess")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-109-custom-method-must-use-camel-case",
            value = "API predates IPA validation."),
      })
  @Consumes({MediaType.APPLICATION_JSON, VersionMediaType.V_2024_08_05_JSON})
  @Produces({VersionMediaType.V_2024_08_05_JSON})
  @RolesAllowed({NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Grant MongoDB Employee Cluster Access for One Cluster",
      operationId = "grantGroupClusterMongoDbEmployeeAccess",
      description =
          "Grants MongoDB employee cluster access for the given duration and at the specified level"
              + " for one cluster.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Grant access level and expiration.",
              content = {
                @Content(
                    mediaType = VersionMediaType.V_2024_08_05_JSON,
                    schema = @Schema(implementation = ApiAtlasEmployeeAccessGrantView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = VersioningConstants.X_XGEN_VERSION,
                                value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                          })
                    })
              }),
      responses = {
        @ApiResponse(
            responseCode = "204",
            description = OpenApiConst.ResponseDescriptions.NO_BODY,
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        })
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "grantMongoEmployeeAccess")
            }),
      })
  public Response grantMongoDBEmployeeAccess(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasEmployeeAccessGrantView pEmployeeAccessGrantView) {

    if (pEmployeeAccessGrantView.getGrantType() == null) {
      return new ApiResponseBuilder(pEnvelope)
          .badRequest("A value for grantType must be specified.")
          .build();
    }
    if (pEmployeeAccessGrantView.getExpirationTime() == null) {
      return new ApiResponseBuilder(pEnvelope)
          .badRequest("A value for expiration must be specified.")
          .build();
    }

    try {
      final ClusterDescription clusterDescription =
          _ndsClusterSvc
              .getActiveClusterDescription(pGroup.getId(), pClusterName)
              .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

      _ndsClusterSvc.grantEmployeeAccess(
          clusterDescription,
          pEmployeeAccessGrantView.getGrantType(),
          pEmployeeAccessGrantView.getExpirationTime(),
          pGroup,
          pAuditInfo,
          pRequest);
    } catch (final SvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }
    return new ApiResponseBuilder(pEnvelope).noContent().build();
  }

  @POST
  @Path("/{clusterName}:revokeMongoDBEmployeeAccess")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-109-custom-method-must-use-camel-case",
            value = "API predates IPA validation."),
      })
  @Consumes({MediaType.APPLICATION_JSON, VersionMediaType.V_2024_08_05_JSON})
  @Produces({VersionMediaType.V_2024_08_05_JSON})
  @RolesAllowed({NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Revoke MongoDB Employee Cluster Access for One Cluster",
      operationId = "revokeGroupClusterMongoDbEmployeeAccess",
      description = "Revokes a previously granted MongoDB employee cluster access.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "204",
            description = OpenApiConst.ResponseDescriptions.NO_BODY,
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2024_08_05_JSON,
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = VersioningConstants.X_XGEN_VERSION,
                              value = VersionMediaType.V_2024_08_05_EXTENSION_TYPE)
                        })
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Dedicated")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "revokeMongoEmployeeAccess")
            }),
      })
  public Response revokeMongoDBEmployeeAccess(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {

    try {
      final ClusterDescription clusterDescription =
          _ndsClusterSvc
              .getActiveClusterDescription(pGroup.getId(), pClusterName)
              .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

      _ndsClusterSvc.revokeGrantedEmployeeAccess(clusterDescription, pGroup, pAuditInfo, pRequest);
    } catch (final SvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }
    return new ApiResponseBuilder(pEnvelope).noContent().build();
  }
}
