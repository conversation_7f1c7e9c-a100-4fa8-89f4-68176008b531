package com.xgen.cloud.nds.project.runtime.res.api_preview;

import static com.xgen.cloud.apiusagedata._public.view.ApiTelemetryView.FieldDefs.CLUSTER_NAME;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetryAttributeSvc;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetrySvc.CustomTelemetryFieldKey;
import com.xgen.cloud.authz.resource._public.view.api.ApiAtlasTagView;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterUpdateContext;
import com.xgen.cloud.nds.project._private.dao.DisaggregatedStorageServiceLogDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.resourcepolicy._public.model.AtlasResourcePolicyAuthResponse;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.sls._public.svc.DisaggregatedStorageSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.util.ApiAtlasClusterDescriptionUtil;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionConnectionStringsUtil;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescription20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasReplicationSpec20240805View;
import com.xgen.svc.mms.res.filter.PaidInFull;
import com.xgen.svc.mms.res.filter.SubscriptionPlan;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.HardwareSpecView;
import com.xgen.svc.nds.model.ui.RegionConfigView;
import com.xgen.svc.nds.model.ui.ReplicationSpecView;
import com.xgen.svc.nds.svc.NDSResourcePolicySvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Nullable;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import org.bson.types.ObjectId;

@SubscriptionPlan(PlanTypeSet.NDS)
@PaidInFull
@Path("/api/atlas/v2/groups/{groupId}/clusters")
@Singleton
public class ApiAtlasClusterDescriptionResourcePreview extends ApiBaseResource {
  private final ApiAtlasClusterDescriptionUtil apiAtlasClusterDescriptionUtil;
  private final AppSettings appSettings;
  private final NDSUISvc ndsUISvc;
  private final NDSClusterSvc ndsClusterSvc;
  private final OnlineArchiveSvc onlineArchiveSvc;
  private final ApiTelemetryAttributeSvc apiTelemetryAttributeSvc;
  private final NDSResourcePolicySvc ndsResourcePolicySvc;
  private final DisaggregatedStorageSvc disaggregatedStorageSvc;
  private final DisaggregatedStorageServiceLogDao disaggregatedStorageServiceLogDao;

  @Inject
  public ApiAtlasClusterDescriptionResourcePreview(
      final ApiAtlasClusterDescriptionUtil apiAtlasClusterDescriptionUtil,
      final AppSettings appSettings,
      final NDSUISvc ndsUISvc,
      final NDSClusterSvc ndsClusterSvc,
      final OnlineArchiveSvc onlineArchiveSvc,
      final ApiTelemetryAttributeSvc apiTelemetryAttributeSvc,
      final NDSResourcePolicySvc ndsResourcePolicySvc,
      final DisaggregatedStorageSvc disaggregatedStorageSvc,
      final DisaggregatedStorageServiceLogDao disaggregatedStorageServiceLogDao) {
    super(appSettings);
    this.apiAtlasClusterDescriptionUtil = apiAtlasClusterDescriptionUtil;
    this.appSettings = appSettings;
    this.ndsClusterSvc = ndsClusterSvc;
    this.ndsUISvc = ndsUISvc;
    this.onlineArchiveSvc = onlineArchiveSvc;
    this.apiTelemetryAttributeSvc = apiTelemetryAttributeSvc;
    this.ndsResourcePolicySvc = ndsResourcePolicySvc;
    // The following fields,disaggregatedStorageSvc and disaggregatedStorageServiceLogDao, need to
    // be injected
    // to ensure the proper initialization of disaggregatedStorageSvc dependencies within the class.
    this.disaggregatedStorageSvc = disaggregatedStorageSvc;
    this.disaggregatedStorageServiceLogDao = disaggregatedStorageServiceLogDao;
  }

  /**
   * Creates a disaggregated cluster in the specified organization and group given the environment
   * is supported and feature flag is enabled. This endpoint processes the request based on the
   * passed-in cluster description view and user context.
   *
   * @param request The HTTP request object, providing contextual information for the operation.
   * @param organization The organization in which the cluster is created.
   * @param group The group under which the new cluster will be created.
   * @param ndsGroup The NDS group object containing additional metadata or settings for the new
   *     cluster.
   * @param auditInfo The audit information for tracking the creation of the cluster, ensuring
   *     compliance and traceability.
   * @param envelope A query parameter indicating whether the response should be wrapped in an
   *     envelope (a structure designed to standardize API responses).
   * @param clusterDescriptionView The detailed view object specifying the configuration and
   *     properties of the cluster to be created.
   * @param currentUser The user performing this operation, useful for validating permissions and
   *     audit tracking.
   * @return Response Returns a Response object indicating the result of the cluster creation
   *     operation. If the creation is successful, the response contains the details of the newly
   *     created cluster.
   * @throws Exception Throws if there is an issue or error during cluster creation. This could
   *     occur due to invalid input, permission issues, or a problem communicating with backend
   *     systems/services.
   *     <p><strong>Feature Flag Dependency:</strong> This method implements logic specific to
   *     disaggregated clusters only if the corresponding disaggregated feature flag is enabled. If
   *     disabled, standard cluster creation logic is used.
   *     <p><strong>Use Case:</strong> This endpoint is primarily used by platform users or
   *     administrators to create clustered environments tailored to their operational needs. It is
   *     especially valuable for deployment scenarios requiring disaggregated architectures.
   */
  @POST
  @Produces(VersionMediaType.V_PREVIEW_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed(RoleSet.NAME.GROUP_ATLAS_ADMIN)
  @Hidden
  public Response createCluster(
      @Context final HttpServletRequest request,
      @Context final Organization organization,
      @Context final Group group,
      @Context final NDSGroup ndsGroup,
      @Context final AuditInfo auditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope,
      final ApiAtlasClusterDescription20240805View clusterDescriptionView,
      @Context final AppUser currentUser)
      throws Exception {
    if (!isFeatureFlagEnabled(
        FeatureFlag.DISAGGREGATED_STORAGE_ATLAS, appSettings, organization, group)) {
      return ApiErrorCode.DISAGGREGATED_STORAGE_FEATURE_FLAG_MISSING.response(envelope);
    }

    final ClusterDescriptionView cdView =
        apiAtlasClusterDescriptionUtil.buildClusterViewForDisaggregatedCluster(
            clusterDescriptionView, envelope, ndsGroup, organization.getId());
    final ClusterDescriptionProcessArgsView existingProcessArgs =
        ndsUISvc.getProcessArgsOrDefault(cdView.getGroupId(), cdView.getName());
    final ClusterDescriptionProcessArgsView mergedProcessArgsView =
        clusterDescriptionView
            .getAdvancedConfiguration()
            .map(
                advancedConfiguration ->
                    advancedConfiguration.mergeAndConvertToProcessArgsViewWithoutAdminFields(
                        existingProcessArgs))
            .orElse(existingProcessArgs);

    // resource policy check
    final AtlasResourcePolicyAuthResponse authResult =
        ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
            group, cdView, mergedProcessArgsView, auditInfo);

    if (authResult.decision().isDeny()) {
      return ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.response(envelope);
    }

    try {
      if (FeatureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI, appSettings, organization, null)) {
        ndsUISvc.createProcessArgs(
            group,
            ndsGroup,
            ClusterCreateContext.forClusterApi(
                false), // disaggregated clusters are treated as replica sets for now
            cdView,
            mergedProcessArgsView,
            auditInfo,
            currentUser);
      }
      // build cluster description
      ndsUISvc.createDisaggregatedStorageCluster(
          organization,
          group.getId(),
          cdView,
          ClusterCreateContext.forClusterApi(false),
          currentUser,
          auditInfo,
          request,
          null,
          null);

      this.ndsClusterSvc.fixClusterMongoDBVersion(
          group.getId(), cdView.getName(), cdView.getMongoDBVersion(), "", auditInfo);
    } catch (final SvcException pE) {
      apiAtlasClusterDescriptionUtil.handleClusterCreateExceptions(
          pE, cdView.getName(), group, envelope);
      apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, cdView.getName(), group, envelope);
    }

    final ClusterDescriptionView updatedClusterDescriptionView =
        ndsUISvc.getClusterDescription(group.getId(), cdView.getName());
    final List<ApiAtlasTagView> tags =
        apiAtlasClusterDescriptionUtil.saveClusterTags(
            updatedClusterDescriptionView,
            clusterDescriptionView.getTags(),
            group,
            auditInfo,
            envelope);

    final ApiAtlasClusterDescription20240805View createdView =
        new ApiAtlasClusterDescription20240805View(
            updatedClusterDescriptionView,
            ndsUISvc.getProcessArgsOptional(group.getId(), cdView.getName()).orElse(null),
            appSettings,
            ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
                cdView, ndsGroup),
            ndsGroup.getCloudProviderContainers(),
            onlineArchiveSvc.generateFederatedURI(
                group.getId(), cdView.getName(), cdView.getMongoDBMajorVersion(), false),
            tags);

    setAdminApiTelemetryAttributes(
        request, createdView.getName(), createdView.getId(), organization, group);

    return new ApiResponseBuilder(envelope).created().content(createdView).build();
  }

  /**
   * Updates instance sizes for a disaggregated cluster in the specified organization and group.
   * This endpoint only allows updating instance sizes while preserving all other disaggregated
   * cluster configurations.
   *
   * @param request The HTTP request object, providing contextual information for the operation.
   * @param organization The organization in which the cluster exists.
   * @param group The group under which the cluster exists.
   * @param ndsGroup The NDS group object containing additional metadata for the cluster.
   * @param auditInfo The audit information for tracking the update operation.
   * @param envelope A query parameter indicating whether the response should be wrapped in an
   *     envelope.
   * @param clusterName The name of the cluster to update.
   * @param clusterDescriptionView The cluster description view containing the instance size
   *     updates.
   * @param currentUser The user performing this operation.
   * @return Response Returns a Response object indicating the result of the cluster update
   *     operation.
   * @throws Exception Throws if there is an issue during cluster update.
   */
  @PATCH
  @Path("/{clusterName}")
  @Produces(VersionMediaType.V_PREVIEW_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed(RoleSet.NAME.GROUP_ATLAS_ADMIN)
  @Hidden
  public Response updateCluster(
      @Context final HttpServletRequest request,
      @Context final Organization organization,
      @Context final Group group,
      @Context final NDSGroup ndsGroup,
      @Context final AuditInfo auditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      final ApiAtlasClusterDescription20240805View clusterDescriptionView,
      @Context final AppUser currentUser)
      throws Exception {

    // 1. Feature flag validation
    if (!isFeatureFlagEnabled(
        FeatureFlag.DISAGGREGATED_STORAGE_ATLAS, appSettings, organization, group)) {
      return ApiErrorCode.DISAGGREGATED_STORAGE_FEATURE_FLAG_MISSING.response(envelope);
    }

    try {
      // 2. Get existing cluster and validate it's disaggregated
      final ClusterDescriptionView existingClusterView =
          ndsUISvc.getClusterDescription(group.getId(), clusterName);

      validateDisaggregatedStorageCluster(existingClusterView, envelope);

      // 3. Validate only instance size changes allowed
      validateOnlyInstanceSizeChanges(existingClusterView, clusterDescriptionView, envelope);

      // 4. Build cluster view for disaggregated cluster update
      final ClusterDescriptionView updatedClusterView =
          apiAtlasClusterDescriptionUtil.buildClusterViewForDisaggregatedClusterUpdate(
              existingClusterView, clusterDescriptionView, ndsGroup);

      // 5. Validate against Atlas resource policies
      final ClusterDescriptionProcessArgsView existingProcessArgs =
          ndsUISvc.getProcessArgsOrDefault(group.getId(), existingClusterView.getName());
      final ClusterDescriptionProcessArgsView mergedProcessArgsView =
          clusterDescriptionView
              .getAdvancedConfiguration()
              .map(
                  advancedConfiguration ->
                      advancedConfiguration.mergeAndConvertToProcessArgsViewWithoutAdminFields(
                          existingProcessArgs))
              .orElse(existingProcessArgs);

      final AtlasResourcePolicyAuthResponse authResult =
          ndsResourcePolicySvc.isUpdateClusterRequestAuthorized(
              group,
              existingClusterView.getUniqueId(),
              updatedClusterView,
              mergedProcessArgsView,
              auditInfo);

      if (authResult.decision().isDeny()) {
        throw ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.exception(envelope);
      }

      // 6. Update cluster through UI service
      ndsUISvc.updateCluster(
          organization,
          group.getId(),
          clusterName,
          updatedClusterView,
          currentUser,
          auditInfo,
          request,
          ClusterUpdateContext.forClusterApi(false));

      // 7. Get updated cluster for response
      final ClusterDescriptionView finalClusterView =
          ndsUISvc.getClusterDescription(group.getId(), clusterName);

      final ApiAtlasClusterDescription20240805View updatedView =
          new ApiAtlasClusterDescription20240805View(
              finalClusterView,
              ndsUISvc.getProcessArgsOptional(group.getId(), clusterName).orElse(null),
              appSettings,
              ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
                  finalClusterView, ndsGroup),
              ndsGroup.getCloudProviderContainers(),
              onlineArchiveSvc.generateFederatedURI(
                  group.getId(), clusterName, finalClusterView.getMongoDBMajorVersion(), false),
              null);

      setAdminApiTelemetryAttributes(
          request, updatedView.getName(), updatedView.getId(), organization, group);

      return new ApiResponseBuilder(envelope).ok().content(updatedView).build();

    } catch (final SvcException pE) {
      apiAtlasClusterDescriptionUtil.handleClusterUpdateExceptions(pE, clusterName, envelope);
      apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, clusterName, group, envelope);
      throw pE; // This should not be reached due to exception handling above
    }
  }

  private void validateDisaggregatedStorageCluster(
      final ClusterDescriptionView existingCluster, final Boolean envelope) throws SvcException {
    if (!existingCluster.getClusterType().equals(ClusterType.DISAGGREGATED.name())) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
          envelope, "Only disaggregated storage clusters can be updated through this endpoint");
    }
  }

  private void validateOnlyInstanceSizeChanges(
      final ClusterDescriptionView existingCluster,
      final ApiAtlasClusterDescription20240805View requestedChanges,
      final Boolean envelope)
      throws SvcException {
    if (requestedChanges.getName() != null
        && !requestedChanges.getName().equals(existingCluster.getName())) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, "Cluster name cannot be changed");
    }

    if (requestedChanges.getClusterType() != null
        && !requestedChanges.getClusterType().equals(existingCluster.getClusterType())) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, "Cluster type cannot be changed");
    }

    if (requestedChanges.getReplicationSpecs() == null) {
      return; // nothing to validate
    }

    final List<ApiAtlasReplicationSpec20240805View> reqSpecs =
        requestedChanges.getReplicationSpecs();
    final List<ReplicationSpecView> existingSpecs = existingCluster.getReplicationSpecList();

    if (existingSpecs == null || existingSpecs.isEmpty()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
          envelope, "Existing cluster has no replication specs to update");
    }

    if (reqSpecs.size() != existingSpecs.size()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
          envelope, "Number of replication specs (shards) cannot be changed");
    }

    for (int i = 0; i < reqSpecs.size(); i++) {
      final ApiAtlasReplicationSpec20240805View reqSpec = reqSpecs.get(i);
      final ReplicationSpecView exSpec = existingSpecs.get(i);

      // Zone name cannot change if provided
      if (reqSpec.getZoneName() != null && !reqSpec.getZoneName().equals(exSpec.getZoneName())) {
        throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
            envelope, "zoneName cannot be changed for replication specs");
      }

      // Region configs count must match
      final List<ApiAtlasRegionConfig20240805View> reqRegions = reqSpec.getRegionConfigs();
      final List<RegionConfigView> exRegions = exSpec.getRegionConfigs();

      if (reqRegions.size() != exRegions.size()) {
        throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
            envelope, "Region configuration count cannot be changed");
      }

      for (int j = 0; j < reqRegions.size(); j++) {
        final ApiAtlasRegionConfig20240805View reqRegion = reqRegions.get(j);
        final RegionConfigView exRegion = exRegions.get(j);

        // providerName must not change if provided
        if (reqRegion.getProviderName() != null) {
          final String reqProvider = reqRegion.getProviderName().toCloudProvider().name();
          if (!reqProvider.equals(exRegion.getCloudProvider())) {
            throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
                envelope, "providerName cannot be changed in regionConfigs");
          }
        }

        // regionName must not change if provided
        if (reqRegion.getRegionName() != null
            && !reqRegion.getRegionName().equals(exRegion.getRegionName())) {
          throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
              envelope, "regionName cannot be changed in regionConfigs");
        }

        // priority must not change if provided
        if (reqRegion.getPriority() != null
            && !reqRegion.getPriority().equals(exRegion.getPriority())) {
          throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
              envelope, "priority cannot be changed in regionConfigs");
        }

        // Only instanceSize, diskSizeGB, diskIOPS, diskThroughput, and ebsVolumeType (AWS) can
        // change in hardware specs
        validateHardwareOnlyInstanceSizeChange(
            reqRegion.getElectableSpecs(), exRegion.getElectableSpecs(), envelope, "electable");

        if (reqRegion instanceof ApiAtlasDedicatedRegionConfig20240805View dedicatedReq) {
          // Autoscaling changes are allowed in preview updates

          // readOnly/analytics specs may be present; only instanceSize may change and no new specs
          // can be added
          if (dedicatedReq.getReadOnlySpecs() != null) {
            if (exRegion.getReadOnlySpecs() == null) {
              throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
                  envelope,
                  "readOnlySpecs cannot be added; only instanceSize, diskSizeGB, diskIOPS,"
                      + " diskThroughput, and ebsVolumeType updates are allowed");
            }
            validateHardwareOnlyInstanceSizeChange(
                dedicatedReq.getReadOnlySpecs(), exRegion.getReadOnlySpecs(), envelope, "readOnly");
          }
          if (dedicatedReq.getAnalyticsSpecs() != null) {
            if (exRegion.getAnalyticsSpecs() == null) {
              throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
                  envelope,
                  "analyticsSpecs cannot be added; only instanceSize, diskSizeGB, diskIOPS,"
                      + " diskThroughput, and ebsVolumeType updates are allowed");
            }
            validateHardwareOnlyInstanceSizeChange(
                dedicatedReq.getAnalyticsSpecs(),
                exRegion.getAnalyticsSpecs(),
                envelope,
                "analytics");
          }
        } else {
          // Only dedicated region configs are supported for disaggregated clusters
          throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
              envelope, "Only dedicated region configs are supported for disaggregated clusters");
        }
      }
    }
  }

  private void validateHardwareOnlyInstanceSizeChange(
      final ApiAtlasHardwareSpec20240805View requested,
      final HardwareSpecView existing,
      final Boolean envelope,
      final String nodeTypeLabel) {
    if (requested == null) {
      return; // no change requested
    }

    // Node count cannot be changed for dedicated hardware specs
    if (requested instanceof ApiAtlasDedicatedHardwareSpec20240805View dedicatedRequested) {
      if (dedicatedRequested.getNodeCount() != null
          && !dedicatedRequested.getNodeCount().equals(existing.getNodeCount())) {
        throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
            envelope, nodeTypeLabel + " nodeCount cannot be changed");
      }
    }
  }

  private void setAdminApiTelemetryAttributes(
      final HttpServletRequest request,
      final String name,
      @Nullable final ObjectId clusterId,
      final Organization organization,
      final Group group) {
    if (FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ENABLE_API_TELEMETRY_CUSTOM_FIELDS, appSettings, organization, group)) {
      apiTelemetryAttributeSvc.putStandardAttribute(request, CLUSTER_NAME, name);
      if (clusterId != null) {
        apiTelemetryAttributeSvc.putCustomAttribute(
            request, CustomTelemetryFieldKey.CLUSTER_ID, clusterId.toString());
      }
    }
  }
}
