package com.xgen.cloud.nds.azure._public.svc;

import static java.time.format.DateTimeFormatter.ISO_ZONED_DATE_TIME;
import static org.apache.http.HttpStatus.SC_ACCEPTED;
import static org.apache.http.HttpStatus.SC_CREATED;
import static org.apache.http.HttpStatus.SC_OK;

import com.azure.core.credential.TokenCredential;
import com.azure.core.credential.TokenRequestContext;
import com.azure.core.http.HttpClient;
import com.azure.core.http.policy.ExponentialBackoffOptions;
import com.azure.core.http.policy.RetryOptions;
import com.azure.core.http.rest.PagedIterable;
import com.azure.core.http.rest.PagedResponse;
import com.azure.core.management.AzureEnvironment;
import com.azure.core.management.exception.ManagementException;
import com.azure.core.management.profile.AzureProfile;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.azure.resourcemanager.AzureResourceManager;
import com.azure.resourcemanager.authorization.models.ServicePrincipal;
import com.azure.resourcemanager.compute.fluent.models.CapacityReservationGroupInner;
import com.azure.resourcemanager.compute.fluent.models.CapacityReservationInner;
import com.azure.resourcemanager.compute.fluent.models.SnapshotInner;
import com.azure.resourcemanager.compute.models.AvailabilitySet;
import com.azure.resourcemanager.compute.models.AvailabilitySetSkuTypes;
import com.azure.resourcemanager.compute.models.CachingTypes;
import com.azure.resourcemanager.compute.models.CapacityReservationUpdate;
import com.azure.resourcemanager.compute.models.CreationData;
import com.azure.resourcemanager.compute.models.Disk;
import com.azure.resourcemanager.compute.models.DiskCreateOption;
import com.azure.resourcemanager.compute.models.DiskSkuTypes;
import com.azure.resourcemanager.compute.models.PowerState;
import com.azure.resourcemanager.compute.models.Sku;
import com.azure.resourcemanager.compute.models.Snapshot;
import com.azure.resourcemanager.compute.models.SnapshotSkuType;
import com.azure.resourcemanager.compute.models.StorageAccountTypes;
import com.azure.resourcemanager.compute.models.VirtualMachine;
import com.azure.resourcemanager.keyvault.models.Key;
import com.azure.resourcemanager.keyvault.models.Keys;
import com.azure.resourcemanager.keyvault.models.Vault;
import com.azure.resourcemanager.keyvault.models.Vaults;
import com.azure.resourcemanager.monitor.models.EventData;
import com.azure.resourcemanager.monitor.models.Metric;
import com.azure.resourcemanager.monitor.models.MetricCollection;
import com.azure.resourcemanager.monitor.models.MetricDefinition;
import com.azure.resourcemanager.monitor.models.MetricDefinition.MetricsQueryDefinitionStages.WithMetricsQueryExecute;
import com.azure.resourcemanager.network.fluent.models.InboundNatRuleInner;
import com.azure.resourcemanager.network.fluent.models.LoadBalancerInner;
import com.azure.resourcemanager.network.fluent.models.NetworkInterfaceInner;
import com.azure.resourcemanager.network.fluent.models.PrivateDnsZoneGroupInner;
import com.azure.resourcemanager.network.fluent.models.PrivateEndpointConnectionInner;
import com.azure.resourcemanager.network.fluent.models.PrivateEndpointInner;
import com.azure.resourcemanager.network.fluent.models.PrivateLinkServiceInner;
import com.azure.resourcemanager.network.models.ApplicationSecurityGroup;
import com.azure.resourcemanager.network.models.LoadBalancer;
import com.azure.resourcemanager.network.models.Network;
import com.azure.resourcemanager.network.models.NetworkInterface;
import com.azure.resourcemanager.network.models.NetworkPeering;
import com.azure.resourcemanager.network.models.NetworkSecurityGroup;
import com.azure.resourcemanager.network.models.PublicIPSkuType;
import com.azure.resourcemanager.network.models.PublicIpAddress;
import com.azure.resourcemanager.privatedns.fluent.models.PrivateZoneInner;
import com.azure.resourcemanager.privatedns.fluent.models.VirtualNetworkLinkInner;
import com.azure.resourcemanager.resources.fluentcore.arm.AvailabilityZoneId;
import com.azure.resourcemanager.resources.fluentcore.arm.models.HasName;
import com.azure.resourcemanager.resources.models.ForceDeletionResourceType;
import com.azure.resourcemanager.resources.models.GenericResource;
import com.azure.resourcemanager.resources.models.ResourceGroup;
import com.azure.security.keyvault.keys.cryptography.CryptographyAsyncClient;
import com.azure.security.keyvault.keys.cryptography.CryptographyClientBuilder;
import com.azure.security.keyvault.keys.cryptography.models.DecryptResult;
import com.azure.security.keyvault.keys.cryptography.models.EncryptResult;
import com.azure.security.keyvault.keys.cryptography.models.EncryptionAlgorithm;
import com.azure.storage.blob.batch.BlobBatchStorageException;
import com.azure.storage.blob.models.AccessTier;
import com.azure.storage.blob.models.BlobContainerItem;
import com.azure.storage.blob.models.BlobItem;
import com.azure.storage.blob.specialized.BlobInputStream;
import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.common.http.url._public.UrlUtils;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.util.DaemonThreadFactory;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureAvailabilityZoneName;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureErrorCode;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardware;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureNDSDefaults;
import com.xgen.cloud.nds.azure._public.model.AzureNetworkSecurityRule;
import com.xgen.cloud.nds.azure._public.model.AzurePeerNetwork;
import com.xgen.cloud.nds.azure._public.model.AzurePhysicalZoneId;
import com.xgen.cloud.nds.azure._public.model.AzureProvisioningState;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureReportExecution;
import com.xgen.cloud.nds.azure._public.model.AzureSubscription;
import com.xgen.cloud.nds.azure._public.model.NDSAzureMetricDefinition;
import com.xgen.cloud.nds.azure._public.model.error.AzureApiException;
import com.xgen.cloud.nds.azure._public.model.ui.NDSAzureActivityLogEntryView;
import com.xgen.cloud.nds.azure._public.model.ui.NDSAzureTempCredentialsView;
import com.xgen.cloud.nds.azure._public.svc.AzureApiClientSvc.NDSAzureResourceManager;
import com.xgen.cloud.nds.azure._public.svc.AzureResourceObservable.AzureResource;
import com.xgen.cloud.nds.azure._public.svc.AzureResourceObservable.Type;
import com.xgen.cloud.nds.azure._public.util.AzureBackupSnapshotUtil;
import com.xgen.cloud.nds.azure._public.util.InnerSupportsGetOrNull;
import com.xgen.cloud.nds.azure._public.util.SupportsGettingByIdOrNull;
import com.xgen.cloud.nds.azure._public.util.SupportsGettingByNameOrNull;
import com.xgen.cloud.nds.azure._public.util.SupportsGettingByResourceGroupOrNull;
import com.xgen.cloud.nds.azure._public.util.SupportsListingByResourceGroupOrEmpty;
import com.xgen.cloud.nds.azure._public.util.SupportsListingByResourceOrEmpty;
import com.xgen.cloud.nds.azure._public.util.SupportsListingOrEmpty;
import com.xgen.cloud.nds.cloudprovider._public.model.cloudproviderconsole.NDSCloudProviderConsoleMetricDefinition;
import com.xgen.cloud.nds.cloudprovider._public.model.cloudproviderconsole.NDST2CSVRow;
import com.xgen.cloud.nds.cloudprovider._public.model.resourceobserver.ResourceEvent;
import com.xgen.cloud.nds.cloudprovider._public.model.ui.NDSCloudProviderEventView;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.svc.mms.util.http.HttpResponseRecord;
import com.xgen.svc.mms.util.http.HttpUtils;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import jakarta.annotation.Nullable;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.YearMonth;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.function.TriFunction;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.client.utils.DateUtils;
import org.bson.types.ObjectId;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class AzureApiSvc {

  private static final Logger LOG = LoggerFactory.getLogger(AzureApiSvc.class);
  public static final String AZURE_WEB_IDENTITY_TOKEN_FILE = "AZURE_WEB_IDENTITY_TOKEN_FILE";
  public static final String AZURE_WEB_IDENTITY_TOKEN_ENABLED = "AZURE_WEB_IDENTITY_TOKEN_ENABLED";
  public static final String AZURE_WEB_IDENTITY_TOKEN_CLIENT_ID =
      "AZURE_WEB_IDENTITY_TOKEN_CLIENT_ID";
  public static final String AZURE_WEB_IDENTITY_TOKEN_TENANT_ID =
      "AZURE_WEB_IDENTITY_TOKEN_TENANT_ID";
  public static final String XGEN_APP = "XGEN_APP";
  private static final EncryptionAlgorithm ENCRYPTION_ALGORITHM = EncryptionAlgorithm.RSA_OAEP_256;

  private static final DateTimeFormatter ISO_DATE_TIME_WITH_OPTIONAL_OFFSET =
      new DateTimeFormatterBuilder()
          .append(DateTimeFormatter.ISO_LOCAL_DATE)
          .appendLiteral('T')
          .append(DateTimeFormatter.ISO_LOCAL_TIME)
          .optionalStart()
          .appendOffsetId()
          .optionalEnd()
          .toFormatter();

  private static final String DISK_FORMAT =
      "/subscriptions/%s/resourceGroups/%s/providers/Microsoft.Compute/snapshots/%s";

  private static final Counter AZURE_DISK_API_CALLS_TOTAL =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_azure_disk_api_call_total",
          "Count of disk-related Azure API calls",
          "request_name",
          "region",
          "disk_type");

  private static final Counter AZURE_DISK_API_ERROR_TOTAL =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_azure_disk_api_error_total",
          "Count of failed disk-related Azure API calls",
          "request_name",
          "region",
          "disk_type",
          "error_code",
          "error_code_expected");

  private static final Histogram AZURE_DISK_API_CALL_LATENCY_SECONDS =
      NDSPromMetricsSvc.registerHistogram(
          "mms_nds_azure_disk_api_call_latency_seconds",
          "latency for making disk-related Azure API calls",
          PromMetricsSvc.getHistogramExpBucket(0.001, 2, 15),
          "request_name",
          "region",
          "disk_type",
          "error_code",
          "error_code_expected");

  private static final String AZURE_DISK_NOT_FOUND_ERROR_MESSAGE =
      "Data disk not found with that subscription ID, resource group, and disk name.";
  private static final Duration ASYNC_STOP_START_DEALLOCATE_TIMEOUT = Duration.ofMinutes(10);

  @VisibleForTesting
  protected static final Duration ASYNC_API_CALL_TIMEOUT = Duration.ofMinutes(10);

  private static final String DATE_FORMAT = "yyyy-MM-dd";
  private static final String COST_DETAILS_VERSION = "2022-05-01";
  private static final String COST_MANAGEMENT_EXPORT_VERSION = "2021-10-01";
  private static final String COST_MANAGEMENT_EXPORT_2023_VERSION = "2023-07-01-preview";
  private static final String COST_MANAGEMENT_EXPORT_REGISTER_VERSION = "2021-04-01";
  private static final String BILLING_PRICE_SHEET_VERSION = "2020-01-01-preview";
  private static final String DEFAULT_COST_DETAILS_PATH_SUFFIX = "generateCostDetailsReport";
  private static final String COST_DETAILS_HOST_NAME = "https://management.azure.com";
  private static final String COST_MANAGEMENT_EXPORT_HOST_NAME = "https://management.azure.com";
  private static final String BILLING_METADATA_HOST_NAME = "https://management.azure.com";

  private static final String AZURE_SNAPSHOT_TYPE = "Microsoft.Compute/snapshots";

  private static final String BILLING_EXPORT_NAME = "mongodb-atlas-billing-v2";

  private final AzureSubscriptionDao _subscriptionDao;
  private final AzureResourceObservable _resourceObservable;
  private final AzureApiClientSvc _azureApiClientSvc;
  private final AzureCredentialsManager _credentialsManager;
  private final AzureSubscriptionSvc _azureSubscriptionSvc;
  private final AzureApiErrorSvc _azureApiErrorSvc;
  private final AzureApiNetworkSvc _azureApiNetworkSvc;
  private final AzureApiStorageSvc _azureApiStorageSvc;

  private static final String CREDENTIALS_VALIDATION_REPORT_TITLE =
      "VALIDATE_AZURE_SUBSCRIPTION_CREDENTIALS";
  private static final int CREDENTIALS_VALIDATION_CHECK_INTERVAL_MINS = 1;
  private final ScheduledExecutorService _credentialsValidationScheduler =
      Executors.newScheduledThreadPool(
          1, new DaemonThreadFactory("AzureApiSvcCredentialsValidation"));

  private static final int AZURE_PV2_LOGICAL_SECTOR_SIZE = 512;

  @Inject
  public AzureApiSvc(
      final AzureSubscriptionDao pSubscriptionDao,
      final AzureResourceObservable pResourceObservable,
      final AzureApiClientSvc pAzureApiClientSvc,
      final AzureCredentialsManager pCredentialsManager,
      final AzureSubscriptionSvc pAzureSubscriptionSvc,
      final AzureApiErrorSvc pAzureApiErrorSvc,
      final AzureApiStorageSvc pAzureApiStorageSvc,
      final AzureApiNetworkSvc pAzureApiNetworkSvc) {
    _subscriptionDao = pSubscriptionDao;
    _resourceObservable = pResourceObservable;
    _azureApiClientSvc = pAzureApiClientSvc;
    _credentialsManager = pCredentialsManager;
    _azureSubscriptionSvc = pAzureSubscriptionSvc;
    _azureApiErrorSvc = pAzureApiErrorSvc;
    _azureApiStorageSvc = pAzureApiStorageSvc;
    _azureApiNetworkSvc = pAzureApiNetworkSvc;

    // Start credential validation scheduler
    _credentialsValidationScheduler.scheduleAtFixedRate(
        this::validateAllSubscriptionCredentialsIfNecessary,
        CREDENTIALS_VALIDATION_CHECK_INTERVAL_MINS,
        CREDENTIALS_VALIDATION_CHECK_INTERVAL_MINS,
        TimeUnit.MINUTES);
  }

  @PreDestroy
  public void stop() {
    _credentialsValidationScheduler.shutdownNow();
  }

  public AzureApiErrorSvc getAzureApiErrorSvc() {
    return _azureApiErrorSvc;
  }

  public AzureApiClientSvc getAzureApiClientSvc() {
    return _azureApiClientSvc;
  }

  public AzureApiStorageSvc getAzureApiStorageSvc() {
    return _azureApiStorageSvc;
  }

  public AzureApiNetworkSvc getAzureApiNetworkSvc() {
    return _azureApiNetworkSvc;
  }

  public AzureSubscription getAzureSubscription(final ObjectId pAzureSubscriptionId) {
    return Optional.ofNullable(pAzureSubscriptionId)
        .map(_azureSubscriptionSvc::getAzureSubscription)
        .orElseThrow(
            () ->
                new AzureApiException(
                    String.format(
                        "Failed to find Azure Subscription by id (%s)", pAzureSubscriptionId),
                    NDSErrorCode.INVALID_ARGUMENT));
  }

  TokenCredential getCloudProviderAccessAzureCredentials(final String pTargetTenantId) {
    return _credentialsManager.getCloudProviderAccessAzureCredentials(pTargetTenantId);
  }

  public void authenticateServicePrincipalForCloudProviderAccess(
      final String pTargetTenantId, final String pServicePrincipalId, final Logger pLogger) {
    final String prettyError =
        "Problem authorizing multi-tenant service principal for cloud provider access";
    final String requestName = "AuthenticateServicePrincipal";

    final AzureSubscription subscription =
        _subscriptionDao
            .findCloudProviderAccessSubscription()
            .orElseThrow(
                () ->
                    new AzureApiException(
                        "Unable to retrieve cloud provider access Azure subscription.",
                        NDSErrorCode.INTERNAL));

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(
            Set.of(
                AzureErrorCode.INVALID_REQUEST,
                CommonErrorCode.INVALID_PARAMETER,
                CommonErrorCode.NO_AUTHORIZATION))
        .makeNonIndexableResourceApiCall(
            () -> {
              final AzureProfile profile =
                  new AzureProfile(
                      pTargetTenantId,
                      subscription.getSubscriptionId(),
                      subscription.getSupportedAzureEnvironment().getAzureEnvironment());

              final TokenCredential tokenCredential =
                  getCloudProviderAccessAzureCredentials(pTargetTenantId);

              // do not retry auth failure
              ExponentialBackoffOptions backoffOptions = new ExponentialBackoffOptions();
              backoffOptions.setMaxRetries(0);
              RetryOptions doNotRetry = new RetryOptions(backoffOptions);

              // Check if HTTP client configuration has changed and clear caches if needed
              getAzureApiClientSvc().checkAndUpdateHttpClientConfiguration();

              final HttpClient customHttpClient = getAzureApiClientSvc().createCustomHttpClient();
              final AzureResourceManager.Configurable configurable =
                  AzureResourceManager.configure()
                      .withLogLevel(getAzureApiClientSvc().getHttpLogDetailLevel());

              // Only set custom HTTP client if it's enabled and available
              if (customHttpClient != null) {
                configurable.withHttpClient(customHttpClient);
              }

              final AzureResourceManager resourceManager =
                  configurable
                      .withRetryOptions(doNotRetry)
                      .authenticate(tokenCredential, profile)
                      .withSubscription(subscription.getSubscriptionId());

              final ServicePrincipal servicePrincipal =
                  resourceManager
                      .accessManagement()
                      .servicePrincipals()
                      .getById(pServicePrincipalId);
              return servicePrincipal.name();
            });
  }

  // TODO: Remove this method once debugging is done
  public JSONObject doAuthedGetJSONObject(final ObjectId pAzureSubscriptionId, final String pLink)
      throws IOException {
    return doAuthedGetJSONObject(pAzureSubscriptionId, pLink, true);
  }

  public JSONObject doAuthedGetJSONObject(
      final ObjectId pAzureSubscriptionId, final String pLink, final boolean logVerbose)
      throws IOException {
    // To get the token, the resource name must exactly match.
    final TokenRequestContext tokenRequestContext =
        new TokenRequestContext().addScopes(AzureNDSDefaults.RESOURCE_URL_WITH_DEFAULT_SCOPE);
    final String token =
        _credentialsManager
            .getAzureResourceManagerCredentialsAndProfile(pAzureSubscriptionId)
            .getLeft()
            .getTokenSync(tokenRequestContext)
            .getToken();

    final Pair<Integer, JSONObject> result =
        HttpUtils.getInstance()
            .doAuthGetJson(pLink, "Authorization", "Bearer " + token, logVerbose);

    if (result.getLeft() != SC_OK) {
      // If there is a better exception that I can exclusively use for this type of errors, please
      // let me know.
      throw new IOException(
          "Non-200 status from Azure API call: url=" + pLink + " status=" + result.getRight());
    }
    return result.getRight();
  }

  public HttpResponseRecord doAuthedGetJSONObjectV2(
      final ObjectId pAzureSubscriptionId, final String pLink, final boolean logVerbose)
      throws IOException {
    // To get the token, the resource name must exactly match.
    final TokenRequestContext tokenRequestContext =
        new TokenRequestContext().addScopes(AzureNDSDefaults.RESOURCE_URL_WITH_DEFAULT_SCOPE);
    final String token =
        _credentialsManager
            .getAzureResourceManagerCredentialsAndProfile(pAzureSubscriptionId)
            .getLeft()
            .getTokenSync(tokenRequestContext)
            .getToken();

    final HttpResponseRecord result =
        HttpUtils.getInstance()
            .doAuthGetJsonV2(pLink, "Authorization", "Bearer " + token, logVerbose);

    if (result.getStatusCode() != SC_OK
        && result.getStatusCode() != SC_ACCEPTED
        && result.getStatusCode() != SC_CREATED) {
      // If there is a better exception that I can exclusively use for this type of errors, please
      // let me know.
      throw new IOException(
          "Non-200 status from Azure API call: url="
              + pLink
              + " status="
              + result.getResponseBody()
              + " responseCode="
              + result.getStatusCode());
    }
    return result;
  }

  private HttpResponseRecord doAuthedPostJSONObject(
      final ObjectId azureSubscriptionId, final String link, final String body) throws IOException {
    return doAuthedPostJSONObject(
        azureSubscriptionId,
        link,
        !StringUtils.isEmpty(body) ? new JSONObject(body) : new JSONObject());
  }

  private HttpResponseRecord doAuthedPostJSONObject(
      final ObjectId azureSubscriptionId, final String link, final JSONObject body)
      throws IOException {
    // To get the token, the resource name must exactly match.
    final TokenRequestContext tokenRequestContext =
        new TokenRequestContext().addScopes(AzureNDSDefaults.RESOURCE_URL_WITH_DEFAULT_SCOPE);
    final String token =
        _credentialsManager
            .getAzureResourceManagerCredentialsAndProfile(azureSubscriptionId)
            .getLeft()
            .getTokenSync(tokenRequestContext)
            .getToken();
    return HttpUtils.getInstance()
        .doAuthPostJsonExtraHeadersV2(link, body, "Authorization", "Bearer " + token, true);
  }

  private JSONObject doAuthedPostJSON(
      final ObjectId azureSubscriptionId, final String link, final JSONObject body) {
    // To get the token, the resource name must exactly match.
    final TokenRequestContext tokenRequestContext =
        new TokenRequestContext().addScopes(AzureNDSDefaults.RESOURCE_URL_WITH_DEFAULT_SCOPE);
    final String token =
        _credentialsManager
            .getAzureResourceManagerCredentialsAndProfile(azureSubscriptionId)
            .getLeft()
            .getTokenSync(tokenRequestContext)
            .getToken();
    return HttpUtils.getInstance()
        .doAuthPostJson(link, body, 200, "Authorization", "Bearer " + token);
  }

  /*
  private HttpResponseRecord doAuthedPutJSONObject(
      final ObjectId azureSubscriptionId, final String link, final String body) throws IOException {
    return doAuthedPutJSONObject(azureSubscriptionId, link, new JSONObject(body));
  }
   */

  private String extractSkipToken(final String pNextLinkWithSkipToken) {
    return StringUtils.substringAfter(pNextLinkWithSkipToken, "meterDetails&");
  }

  private String getConsumptionUsageDetailsLink(
      final String pSubscriptionId,
      final String pHost,
      final String pApiVersion,
      final String pStartDate,
      final String pEndDate,
      final String pNextLink) {

    final String filterAndExpand =
        UrlUtils.urlEncode("$filter")
            + "="
            + UrlUtils.urlEncode("properties/usageStart eq '")
            + pStartDate
            + UrlUtils.urlEncode("' and properties/usageEnd eq '")
            + pEndDate
            + UrlUtils.urlEncode("'")
            + "&"
            + UrlUtils.urlEncode("$expand")
            + "="
            + UrlUtils.urlEncode("meterDetails");

    String path =
        String.format(
            "%s/subscriptions/%s/providers/Microsoft.Consumption/usageDetails?api-version=%s&%s",
            pHost, pSubscriptionId, pApiVersion, filterAndExpand);

    if (StringUtils.isNotBlank(pNextLink)) {
      path += "&" + extractSkipToken(pNextLink);
    }
    return path;
  }

  private String getCostDetailsBody(final Date pReportedStartTime, final Date pReportedEndTime) {
    return "{\n"
        + "  \"metric\": \"ActualCost\",\n"
        + "  \"timePeriod\": {\n"
        + "    \"start\": \""
        + DateUtils.formatDate(pReportedStartTime, DATE_FORMAT)
        + "\",\n"
        + "    \"end\": \""
        + DateUtils.formatDate(pReportedEndTime, DATE_FORMAT)
        + "\"\n"
        + "  }\n"
        + "}";
  }

  private String getCostManagementExportLink(final String subscriptionId, final String exportName) {
    return String.format(
        "%s/subscriptions/%s/providers/Microsoft.CostManagement/exports/%s?api-version=%s",
        COST_MANAGEMENT_EXPORT_HOST_NAME,
        subscriptionId,
        exportName,
        COST_MANAGEMENT_EXPORT_VERSION);
  }

  private String getCostManagementExportRunLink(
      final String subscriptionId, final String exportName) {
    return String.format(
        "%s/subscriptions/%s/providers/Microsoft.CostManagement/exports/%s/run?api-version=%s",
        COST_MANAGEMENT_EXPORT_HOST_NAME,
        subscriptionId,
        exportName,
        COST_MANAGEMENT_EXPORT_2023_VERSION);
  }

  private String getCostManagementExportRunHistoryLink(
      final String subscriptionId, final String exportName) {
    return String.format(
        "%s/subscriptions/%s/providers/Microsoft.CostManagement/exports/%s/runHistory?api-version=%s",
        COST_MANAGEMENT_EXPORT_HOST_NAME,
        subscriptionId,
        exportName,
        COST_MANAGEMENT_EXPORT_2023_VERSION);
  }

  private String getCostManagementExportRegisterLink(final String subscriptionId) {
    return String.format(
        "%s/subscriptions/%s/providers/Microsoft.CostManagementExports/register?api-version=%s",
        COST_MANAGEMENT_EXPORT_HOST_NAME, subscriptionId, COST_MANAGEMENT_EXPORT_REGISTER_VERSION);
  }

  private String getCostDetailsLink(final String subscriptionId, final String resultsSuffix) {
    return String.format(
        "%s/subscriptions/%s/providers/Microsoft.CostManagement/%s?api-version=%s",
        COST_DETAILS_HOST_NAME,
        subscriptionId,
        StringUtils.isEmpty(resultsSuffix) ? DEFAULT_COST_DETAILS_PATH_SUFFIX : resultsSuffix,
        COST_DETAILS_VERSION);
  }

  private String getUsageAggregatesLink(
      final String pSubscriptionId,
      final String pHost,
      final String pApiVersion,
      final Date pReportedStartTime,
      final Date pReportedEndTime,
      final String pGranularity,
      final boolean pShowDetails,
      final String pContinuationToken) {
    String path =
        String.format(
            "%s/subscriptions/%s/providers/Microsoft.Commerce/UsageAggregates?api-version=%s&"
                + "reportedStartTime=%s&reportedEndTime=%s&aggregationGranularity=%s&showDetails=%s",
            pHost,
            pSubscriptionId,
            pApiVersion,
            UrlUtils.urlEncode(TimeUtils.toISOString(pReportedStartTime)),
            UrlUtils.urlEncode(TimeUtils.toISOString(pReportedEndTime)),
            pGranularity,
            pShowDetails);

    if (StringUtils.isNotBlank(pContinuationToken)) {
      path += "&continuationToken=" + pContinuationToken;
    }
    return path;
  }

  public JSONObject getUsageAggregates(
      final ObjectId pSubscriptionId,
      final Date pStartTime,
      final Date pEndTime,
      final String pNextLink)
      throws IOException {
    final AzureSubscription azureSubscription = getAzureSubscription(pSubscriptionId);
    final String link =
        StringUtils.isNotBlank(pNextLink)
            ? pNextLink
            : getUsageAggregatesLink(
                azureSubscription.getSubscriptionId(),
                "https://management.azure.com",
                "2015-06-01-preview",
                pStartTime,
                pEndTime,
                "Daily",
                true,
                null);

    return doAuthedGetJSONObject(pSubscriptionId, link);
  }

  public HttpResponseRecord retrieveCostDetailsUsageAsync(
      final ObjectId subscriptionId, final Date startTime, final Date endTime) throws IOException {
    final AzureSubscription azureSubscription = getAzureSubscription(subscriptionId);
    final String link = getCostDetailsLink(azureSubscription.getSubscriptionId(), null);
    final String body = getCostDetailsBody(startTime, endTime);

    return doAuthedPostJSONObject(subscriptionId, link, body);
  }

  public HttpResponseRecord getCostDetailsUsageResult(
      final ObjectId subscriptionId, final String resultsSuffix) throws IOException {
    final AzureSubscription azureSubscription = getAzureSubscription(subscriptionId);
    final String link = getCostDetailsLink(azureSubscription.getSubscriptionId(), resultsSuffix);
    return doAuthedGetJSONObjectV2(subscriptionId, link, true);
  }

  /**
   * Register a given subscription id for the Cost Management Export API. Without doing this, we can
   * observe errors like this: "RP Not Registered. Register destination storage account subscription
   * with Microsoft.CostManagementExports"
   *
   * @param subscriptionId ID / primary key of nds.config.nds.azureSubscriptions collection
   * @return Results of HTTP response.
   */
  public HttpResponseRecord registerForCostManagementExport(final ObjectId subscriptionId)
      throws IOException {
    final AzureSubscription azureSubscription = getAzureSubscription(subscriptionId);
    final String link = getCostManagementExportRegisterLink(azureSubscription.getSubscriptionId());
    return doAuthedPostJSONObject(subscriptionId, link, new JSONObject());
  }

  /**
   * Runs the export for a given subscription.
   *
   * @param subscription the Azure subscription
   * @param month the month to export
   */
  public void costManagementRunExport(final AzureSubscription subscription, final YearMonth month)
      throws IOException {
    final String link =
        getCostManagementExportRunLink(subscription.getSubscriptionId(), BILLING_EXPORT_NAME);

    doAuthedPostJSON(subscription.getId(), link, new JSONObject(getExportTimeBody(month)));
  }

  private String getExportTimeBody(final YearMonth month) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    final LocalDate begin = month.atDay(1);
    final LocalDate end = month.atEndOfMonth();

    return String.format(
        """
{
    "timePeriod": {
        "from": "%s",
        "to": "%s"
    }
}
""",
        begin.format(formatter), end.format(formatter));
  }

  public JSONObject getPriceSheetResult(final ObjectId subscriptionId, final int skip)
      throws IOException {
    final AzureSubscription subscription = getAzureSubscription(subscriptionId);
    final String link = getPriceSheetDownloadLink(subscription.getSubscriptionId(), skip);
    return doAuthedGetJSONObject(subscriptionId, link);
  }

  private String getPriceSheetDownloadLink(final String subscriptionId, final int skip) {
    return String.format(
        "%s/subscriptions/%s/providers/Microsoft.Consumption/pricesheets/default?api-version=%s%s",
        BILLING_METADATA_HOST_NAME,
        subscriptionId,
        BILLING_PRICE_SHEET_VERSION,
        skip > 0 ? "&$skip=" + skip : "");
  }

  public JSONObject getConsumptionUsageDetails(
      final ObjectId pSubscriptionId,
      final String pStartDate,
      final String pEndDate,
      final String pApiVersion,
      final String pNextLink)
      throws IOException {
    final AzureSubscription azureSubscription = getAzureSubscription(pSubscriptionId);
    final String link =
        getConsumptionUsageDetailsLink(
            azureSubscription.getSubscriptionId(),
            "https://management.azure.com",
            pApiVersion,
            pStartDate,
            pEndDate,
            pNextLink);

    return doAuthedGetJSONObject(pSubscriptionId, link, true);
  }

  public List<AzureSubscription> findAllSubscriptions() {
    return _subscriptionDao.findAllAsBasicList().stream()
        .map(AzureSubscription::new)
        .collect(Collectors.toList());
  }

  public ResourceGroup createResourceGroup(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    final String prettyError = "Problem creating resource group";
    final String requestName = "CreateResourceGroup";

    final ResourceGroup resourceGroup =
        getAzureApiCallBuilder(prettyError, pLogger, requestName)
            .expectedErrorCodes(Set.of(CommonErrorCode.INVALID_PARAMETER, AzureErrorCode.CONFLICT))
            .recordAndPublishQuotaCapacityError(pSubscriptionId, pAzureRegionName)
            .makeAsynchronousApiCall(
                String.format(
                    "createResourceGroup - subscriptionId: %s, regionName: %s, resourceGroupName:"
                        + " %s",
                    pSubscriptionId, pAzureRegionName, pResourceGroupName),
                getAzureApiClientSvc()
                        .getCachedAzureResourceManager(pSubscriptionId)
                        .resourceGroups()
                        .define(pResourceGroupName)
                        .withRegion(pAzureRegionName.getValue())
                        .withTags(pTags)
                    ::createAsync,
                () -> findResourceGroup(pSubscriptionId, pResourceGroupName, pLogger));

    _resourceObservable.notify(
        new AzureResource(
            pSubscriptionId, pResourceGroupName, Type.RESOURCE_GROUP, pResourceGroupName),
        ResourceEvent.CREATED);

    return resourceGroup;
  }

  public ResourceGroup findResourceGroup(
      final ObjectId pSubscriptionId, final String pResourceGroupName, final Logger pLogger) {
    final String prettyError = "Problem finding resource group";
    final String requestName = "FindResourceGroup";

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeSynchronousApiCall(
            String.format(
                "findResourceGroup - subscriptionId: %s, resourceGroupName: %s",
                pSubscriptionId, pResourceGroupName),
            () ->
                SupportsGettingByNameOrNull.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .resourceGroups())
                    .getByNameOrNull(pResourceGroupName));
  }

  public void deleteResourceGroup(
      final ObjectId pSubscriptionId, final String pResourceGroupName, final Logger pLogger) {
    final String prettyError = "Problem deleting resource group";
    final String requestName = "DeleteResourceGroup";
    final Collection<ForceDeletionResourceType> resourceTypesToForceDelete = Set.of();

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeSynchronousApiCall(
            String.format(
                "deleteResourceGroup - subscriptionId: %s, resourceGroupName: %s",
                pSubscriptionId, pResourceGroupName),
            () -> {
              getAzureApiClientSvc()
                  .getCachedAzureResourceManager(pSubscriptionId)
                  .resourceGroups()
                  .deleteByName(pResourceGroupName, resourceTypesToForceDelete);
              return null;
            });
  }

  public void tagResourceGroup(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    final String prettyError = "Problem deleting resource group";
    final String requestName = "TagResourceGroup";

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeSynchronousApiCall(
            String.format(
                "tagResourceGroup - subscriptionId: %s, resourceGroupName: %s, tags: %s",
                pSubscriptionId, pResourceGroupName, pTags.toString()),
            () -> {
              final ResourceGroup resourceGroup =
                  SupportsGettingByNameOrNull.wrap(
                          getAzureApiClientSvc()
                              .getCachedAzureResourceManager(pSubscriptionId)
                              .resourceGroups())
                      .getByNameOrNull(pResourceGroupName);

              final Map<String, String> tags = new HashMap<>(pTags);

              if (resourceGroup.tags() != null) {
                tags.putAll(resourceGroup.tags());
              }

              resourceGroup.update().withTags(tags).apply();
              return null;
            });
  }

  public PagedIterable<GenericResource> getResourcesInResourceGroup(
      final ObjectId pSubscriptionId, final String pResourceGroupName, final Logger pLogger) {
    return SupportsListingByResourceGroupOrEmpty.wrap(
            getAzureApiClientSvc()
                .getCachedAzureResourceManager(pSubscriptionId)
                .genericResources())
        .listByResourceGroupOrEmpty(pResourceGroupName);
  }

  public PagedIterable<Disk> getDisksInResourceGroup(
      final ObjectId pSubscriptionId, final String pResourceGroupName) {
    return SupportsListingByResourceGroupOrEmpty.wrap(
            getAzureApiClientSvc().getCachedAzureResourceManager(pSubscriptionId).disks())
        .listByResourceGroupOrEmpty(pResourceGroupName);
  }

  public PagedIterable<VirtualMachine> getVirtualMachinesInResourceGroup(
      final ObjectId pSubscriptionId, final String pResourceGroupName) {
    return SupportsListingByResourceGroupOrEmpty.wrap(
            getAzureApiClientSvc().getCachedAzureResourceManager(pSubscriptionId).virtualMachines())
        .listByResourceGroupOrEmpty(pResourceGroupName);
  }

  public PagedIterable<NetworkInterface> getNetworkInterfacesInResourceGroup(
      final ObjectId pSubscriptionId, final String pResourceGroupName) {
    return getAzureApiNetworkSvc()
        .getNetworkInterfacesInResourceGroup(pSubscriptionId, pResourceGroupName);
  }

  public PagedIterable<Snapshot> getSnapshotsInResourceGroup(
      final ObjectId pSubscriptionId, final String pResourceGroupName) {
    return SupportsListingByResourceGroupOrEmpty.wrap(
            getAzureApiClientSvc().getCachedAzureResourceManager(pSubscriptionId).snapshots())
        .listByResourceGroupOrEmpty(pResourceGroupName);
  }

  public boolean deleteResourceGroupIfHasNoSensitiveResources(
      final ObjectId pSubscriptionId, final String pResourceGroupName, final Logger pLogger) {
    final AzureResourceManager azureResourceManager =
        getAzureApiClientSvc().getCachedAzureResourceManager(pSubscriptionId);

    final ResourceGroup rg = findResourceGroup(pSubscriptionId, pResourceGroupName, pLogger);
    if (rg == null) {
      pLogger.debug("Resource group {} does not exist. Nothing to do.", pResourceGroupName);
      return true;
    }

    final List<String> resourcesForGroup = new ArrayList<>();
    resourcesForGroup.addAll(
        getDisksInResourceGroup(pSubscriptionId, pResourceGroupName).stream()
            .map(HasName::name)
            .collect(Collectors.toList()));
    resourcesForGroup.addAll(
        getVirtualMachinesInResourceGroup(pSubscriptionId, pResourceGroupName).stream()
            .map(HasName::name)
            .collect(Collectors.toList()));
    resourcesForGroup.addAll(
        getSnapshotsInResourceGroup(pSubscriptionId, pResourceGroupName).stream()
            .map(HasName::name)
            .collect(Collectors.toList()));

    // Don't allow deletion of resource groups that still contain disks, vms, or snapshots
    if (!resourcesForGroup.isEmpty()) {
      final String resources = resourcesForGroup.stream().collect(Collectors.joining(","));
      throw new AzureApiException(
          NDSErrorCode.CANNOT_DELETE_NON_EMPTY_RESOURCE_GROUP.formatMessage(
              pResourceGroupName, resources),
          NDSErrorCode.CANNOT_DELETE_NON_EMPTY_RESOURCE_GROUP);
    }

    pLogger.debug("Making async delete request for resource group {}.", pResourceGroupName);
    azureResourceManager.resourceGroups().beginDeleteByName(pResourceGroupName);
    // Because we are not verifying that the resource is deleted in the call above,
    // we want to return false here so that the item does not get immediately deleted
    // from the queue
    return false;
  }

  public boolean deleteResourceGroupIfEmpty(
      final ObjectId pSubscriptionId, final String pResourceGroupName, final Logger pLogger) {
    final AzureResourceManager azureResourceManager =
        getAzureApiClientSvc().getCachedAzureResourceManager(pSubscriptionId);

    final ResourceGroup rg = findResourceGroup(pSubscriptionId, pResourceGroupName, pLogger);
    if (rg == null) {
      pLogger.debug("Resource group {} does not exist. Nothing to do.", pResourceGroupName);
      return true;
    }

    final PagedIterable<NetworkSecurityGroup> securityGroupsForGroup =
        SupportsListingByResourceGroupOrEmpty.wrap(azureResourceManager.networkSecurityGroups())
            .listByResourceGroupOrEmpty(pResourceGroupName);
    final PagedIterable<Snapshot> snapshotsForGroup =
        SupportsListingByResourceGroupOrEmpty.wrap(azureResourceManager.snapshots())
            .listByResourceGroupOrEmpty(pResourceGroupName);
    final PagedIterable<GenericResource> resourcesForGroup =
        SupportsListingByResourceGroupOrEmpty.wrap(azureResourceManager.genericResources())
            .listByResourceGroupOrEmpty(pResourceGroupName);

    // CLOUDP-141376 the azure api seems to be returning stale snapshots as part of the
    // genericResources api.  The idea is to filter out these snapshots if they are not part of the
    // snapshotsForGroup set in order to allow proper deletion of the parent resource group
    final Set<String> snapshotIdSet =
        new HashSet<>(
            snapshotsForGroup.stream().map(snapshot -> snapshot.id()).collect(Collectors.toList()));

    final List<GenericResource> filteredResourcesForGroup =
        resourcesForGroup.stream()
            .filter(
                resource ->
                    !(resource.type().equals(AZURE_SNAPSHOT_TYPE)
                        && !snapshotIdSet.contains(resource.id())))
            .collect(Collectors.toList());

    // For Azure, a resource group without a security group is exposed to the internet
    // by default. Because of that, we bundle the deletion of a security group with a
    // resource group to ensure that a resource group is always secure. Therefore, a
    // resource group containing only security groups is considered empty.

    // There is currently a consistency issue on Azure where deleted resources can take
    // 12+ hours to actually stop returning from the `listByResourceGroup` call. We handle
    // that by allowing for a buffer of 2 resources to exist and still be willing to delete
    // the resource group. This is still safe enough because 2 resources can not be a functioning
    // cluster/instance.
    if (filteredResourcesForGroup.size() > IterableUtils.size(securityGroupsForGroup) + 2
        || IterableUtils.size(snapshotsForGroup) > 0) {
      final String resources =
          filteredResourcesForGroup.stream().map(HasName::name).collect(Collectors.joining(","));
      throw new AzureApiException(
          NDSErrorCode.CANNOT_DELETE_NON_EMPTY_RESOURCE_GROUP.formatMessage(
              pResourceGroupName, resources),
          NDSErrorCode.CANNOT_DELETE_NON_EMPTY_RESOURCE_GROUP);
    }

    pLogger.debug("Making async delete request for resource group {}.", pResourceGroupName);
    azureResourceManager.resourceGroups().beginDeleteByName(pResourceGroupName);
    // Because we are not verifying that the resource is deleted in the call above,
    // we want to return false here so that the item does not get immediately deleted
    // from the queue
    return false;
  }

  public String listVirtualNetworksAndApplyForNextToken(
      final ObjectId pSubscriptionId,
      final Logger pLogger,
      final String pNextToken,
      final Consumer<List<Network>> pResultConsumer) {
    return getAzureApiNetworkSvc()
        .listVirtualNetworksAndApplyForNextToken(
            pSubscriptionId, pLogger, pNextToken, pResultConsumer);
  }

  public Network createVirtualNetwork(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pAddressSpace,
      final String pResourceGroupName,
      final String pName,
      final String pSubnetName,
      final String pSubnetCidr,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createVirtualNetwork(
            pSubscriptionId,
            pAzureRegionName,
            pAddressSpace,
            pResourceGroupName,
            pName,
            pSubnetName,
            pSubnetCidr,
            pTags,
            pLogger);
  }

  public Network findVirtualNetwork(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findVirtualNetwork(pSubscriptionId, pResourceGroupName, pName, pLogger);
  }

  public void deleteVirtualNetwork(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deleteVirtualNetwork(pSubscriptionId, pResourceGroupName, pName, pLogger);
  }

  public ApplicationSecurityGroup createApplicationSecurityGroup(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final AzureRegionName pAzureRegionName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem creating application security group";
    final String requestName = "CreateApplicationSecurityGroup";

    final ApplicationSecurityGroup.DefinitionStages.WithCreate createRequest =
        getAzureApiClientSvc()
            .getCachedAzureResourceManager(pSubscriptionId)
            .applicationSecurityGroups()
            .define(pName)
            .withRegion(pAzureRegionName.getValue())
            .withExistingResourceGroup(pResourceGroupName);

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .recordAndPublishQuotaCapacityError(pSubscriptionId, pAzureRegionName)
        .makeSynchronousApiCall(
            String.format(
                "CreateApplicationSecurityGroup - subscriptionId: %s, resourceGroupName: %s,"
                    + " azureRegionName: %s",
                pSubscriptionId, pResourceGroupName, pAzureRegionName),
            createRequest::create);
  }

  public ApplicationSecurityGroup findApplicationSecurityGroup(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final AzureRegionName pAzureRegionName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem finding application security group";
    final String requestName = "FindApplicationSecurityGroup";

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeSynchronousApiCall(
            String.format(
                "FindApplicationSecurityGroup - subscriptionId: %s, resourceGroupName: %s,"
                    + " azureRegionName: %s",
                pSubscriptionId, pResourceGroupName, pAzureRegionName),
            () ->
                SupportsGettingByResourceGroupOrNull.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .applicationSecurityGroups())
                    .getByResourceGroupOrNull(pResourceGroupName, pName));
  }

  public void deleteApplicationSecurityGroup(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem deleting application security group";
    final String requestName = "DeleteApplicationSecurityGroup";

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeSynchronousApiCall(
            String.format(
                "deleteApplicationSecurityGroup - subscriptionId: %s, resourceGroupName: %s, name:"
                    + " %s",
                pSubscriptionId, pResourceGroupName, pName),
            () -> {
              getAzureApiClientSvc()
                  .getCachedAzureResourceManager(pSubscriptionId)
                  .applicationSecurityGroups()
                  .deleteByResourceGroup(pResourceGroupName, pName);
              return null;
            });
  }

  public NetworkInterfaceInner removeExistingApplicationSecurityGroupFromNetworkInterface(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final ApplicationSecurityGroup pApplicationSecurityGroup,
      final String pNetworkInterfaceName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .removeExistingApplicationSecurityGroupFromNetworkInterface(
            pSubscriptionId,
            pResourceGroupName,
            pApplicationSecurityGroup,
            pNetworkInterfaceName,
            pLogger);
  }

  public NetworkInterfaceInner attachApplicationSecurityGroupToNetworkInterface(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final ApplicationSecurityGroup pApplicationSecurityGroup,
      final String pNetworkInterfaceName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .attachApplicationSecurityGroupToNetworkInterface(
            pSubscriptionId,
            pResourceGroupName,
            pApplicationSecurityGroup,
            pNetworkInterfaceName,
            pLogger);
  }

  public NetworkSecurityGroup createNetworkSecurityGroup(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createNetworkSecurityGroup(
            pSubscriptionId, pAzureRegionName, pResourceGroupName, pName, pTags, pLogger);
  }

  public NetworkSecurityGroup findNetworkSecurityGroup(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findNetworkSecurityGroup(pSubscriptionId, pResourceGroupName, pName, pLogger);
  }

  public void deleteNetworkSecurityGroup(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deleteNetworkSecurityGroup(pSubscriptionId, pResourceGroupName, pName, pLogger);
  }

  public void createNetworkPeer(
      final AzureCloudProviderContainer pContainer,
      final AzurePeerNetwork pPeerNetwork,
      final String pPeeringName,
      final boolean pIsReciprocal,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .createNetworkPeer(pContainer, pPeerNetwork, pPeeringName, pIsReciprocal, pLogger);
  }

  public NetworkPeering findNetworkPeer(
      final AzureCloudProviderContainer pContainer,
      final AzurePeerNetwork pPeerNetwork,
      final Logger pLogger) {
    return getAzureApiNetworkSvc().findNetworkPeer(pContainer, pPeerNetwork, pLogger);
  }

  public JSONObject findNetworkPeerHttp(
      final AzureCloudProviderContainer pContainer,
      final AzurePeerNetwork pPeerNetwork,
      final String pPeeringName,
      final boolean pIsReciprocal,
      final Logger pLogger)
      throws AzureApiException {
    return getAzureApiNetworkSvc()
        .findNetworkPeerHttp(pContainer, pPeerNetwork, pPeeringName, pIsReciprocal, pLogger);
  }

  public JSONArray listReciprocalNetworkPeers(
      final AzurePeerNetwork pPeerNetwork, final Logger pLogger) throws AzureApiException {
    return getAzureApiNetworkSvc().listReciprocalNetworkPeers(pPeerNetwork, pLogger);
  }

  // Uses the Azure SDK instead of HTTP API call to delete network peer
  public void deleteNetworkPeer(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVnetName,
      final String pPeerId,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deleteNetworkPeer(pSubscriptionId, pResourceGroupName, pVnetName, pPeerId, pLogger);
  }

  public void deleteNetworkPeer(
      final AzureCloudProviderContainer pContainer,
      final AzurePeerNetwork pPeerNetwork,
      final String pPeeringName,
      final boolean pIsReciprocal,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deleteNetworkPeer(pContainer, pPeerNetwork, pPeeringName, pIsReciprocal, pLogger);
  }

  public Map<AzureRegionName, Map<AzureAvailabilityZoneName, AzurePhysicalZoneId>>
      getAvailabilityZoneMappings(final ObjectId pSubscriptionId, final Logger pLogger) {
    final String prettyError = "Problem finding subscription location";
    final String requestName = "GetLocationByRegion";
    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeNonIndexableResourceApiCall(
            () -> getCurrentSubscription(pSubscriptionId, pLogger).listLocations())
        .stream()
        .filter((location) -> AzureRegionName.findByValue(location.region().name()).isPresent())
        .collect(
            Collectors.toMap(
                (location) -> AzureRegionName.findByValue(location.region().name()).orElseThrow(),
                (location) ->
                    Optional.ofNullable(location.innerModel().availabilityZoneMappings())
                        .map(
                            mappings -> {
                              final HashMap<AzureAvailabilityZoneName, AzurePhysicalZoneId>
                                  zoneMap = new HashMap<>();
                              mappings.forEach(
                                  mapping -> {
                                    final Optional<AzureAvailabilityZoneName> azName =
                                        AzureAvailabilityZoneName.findById(
                                            AvailabilityZoneId.fromString(mapping.logicalZone()));
                                    if (azName.isPresent()) {
                                      zoneMap.put(
                                          azName.get(),
                                          new AzurePhysicalZoneId(mapping.physicalZone()));
                                    } else {
                                      LOG.warn(
                                          "Error parsing logical zone name {} in region {}",
                                          mapping.logicalZone(),
                                          location.region().name());
                                    }
                                  });
                              return zoneMap;
                            })
                        .orElse(new HashMap<>())));
  }

  @VisibleForTesting
  protected com.azure.resourcemanager.resources.models.Subscription getCurrentSubscription(
      final ObjectId pSubscriptionId, final Logger pLogger) {
    final String prettyError = "Problem finding current subscription";
    final String requestName = "GetCurrentSubscription";
    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeSynchronousApiCall(
            String.format("%s - subscriptionId: %s", requestName, pSubscriptionId),
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureResourceManager(pSubscriptionId)
                    .getCurrentSubscription());
  }

  public NetworkSecurityGroup addSecurityGroupRules(
      final NetworkSecurityGroup pNetworkSecurityGroup,
      final Set<AzureNetworkSecurityRule> pRules,
      final Logger pLogger) {
    return getAzureApiNetworkSvc().addSecurityGroupRules(pNetworkSecurityGroup, pRules, pLogger);
  }

  public NetworkSecurityGroup deleteSecurityGroupRules(
      final NetworkSecurityGroup pNetworkSecurityGroup,
      final Set<AzureNetworkSecurityRule> pRules,
      final Logger pLogger) {
    return getAzureApiNetworkSvc().deleteSecurityGroupRules(pNetworkSecurityGroup, pRules, pLogger);
  }

  public AvailabilitySet createAvailabilitySet(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem creating availability set";
    final String requestName = "CreateAvailabilitySet";

    final AvailabilitySet.DefinitionStages.WithCreate availabilitySets =
        getAzureApiClientSvc()
            .getCachedAzureResourceManager(pSubscriptionId)
            .availabilitySets()
            .define(pName)
            .withRegion(pAzureRegionName.getValue())
            .withExistingResourceGroup(pResourceGroupName)
            .withSku(AvailabilitySetSkuTypes.ALIGNED)
            .withFaultDomainCount(pAzureRegionName.faultDomainCount);

    final AvailabilitySet availabilitySet =
        getAzureApiCallBuilder(prettyError, pLogger, requestName)
            .expectedErrorCodes(
                Set.of(
                    CommonErrorCode.INVALID_PARAMETER,
                    CommonErrorCode.TIMEOUT,
                    AzureErrorCode.CONFLICT))
            .recordAndPublishQuotaCapacityError(pSubscriptionId, pAzureRegionName)
            .makeAsynchronousApiCall(
                String.format(
                    "createAvailabilitySet - subscriptionId: %s, name: %s, resourceGroupName: %s",
                    pSubscriptionId, pName, pResourceGroupName),
                availabilitySets::createAsync,
                () -> findAvailabilitySet(pSubscriptionId, pResourceGroupName, pName, pLogger));

    _resourceObservable.notify(
        new AzureResource(pSubscriptionId, pResourceGroupName, Type.AVAILABILITY_SET, pName),
        ResourceEvent.CREATED);

    return availabilitySet;
  }

  public AvailabilitySet findAvailabilitySet(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem finding availability set";
    final String requestName = "FindAvailabilitySet";

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeSynchronousApiCall(
            String.format(
                "findAvailabilitySet - subscriptionId: %s, resourceGroupName: %s, name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () ->
                SupportsGettingByResourceGroupOrNull.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .availabilitySets())
                    .getByResourceGroupOrNull(pResourceGroupName, pName));
  }

  public PagedIterable<AvailabilitySet> findAvailabilitySets(
      final ObjectId pSubscriptionId, final String pResourceGroupName) {
    return SupportsListingByResourceGroupOrEmpty.wrap(
            getAzureApiClientSvc()
                .getCachedAzureResourceManager(pSubscriptionId)
                .availabilitySets())
        .listByResourceGroupOrEmpty(pResourceGroupName);
  }

  public boolean deleteAvailabilitySetWithVerification(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {

    final AvailabilitySet availabilitySet =
        findAvailabilitySet(pSubscriptionId, pResourceGroupName, pName, pLogger);
    if (availabilitySet == null) {
      return true;
    }

    deleteAvailabilitySet(pSubscriptionId, pResourceGroupName, pName, pLogger);
    return false;
  }

  public void deleteAvailabilitySet(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem deleting availability set";
    final String requestName = "DeleteAvailabilitySet";

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeSynchronousApiCall(
            String.format(
                "deleteAvailabilitySet - subscriptionId: %s, resourceGroupName: %s, name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () -> {
              getAzureApiClientSvc()
                  .getCachedAzureResourceManager(pSubscriptionId)
                  .availabilitySets()
                  .deleteByResourceGroup(pResourceGroupName, pName);
              return null;
            });
  }

  public CapacityReservationInner createCapacityReservation(
      final ObjectId subscriptionId,
      final String resourceGroupName,
      final String capacityReservationGroupName,
      final String capacityReservationName,
      final AzureRegionName regionName,
      final String instanceType,
      final long numInstances,
      final String azName,
      final Map<String, String> tags,
      final Logger log) {
    final String prettyError = "Could not create capacity reservation";
    final String requestName = "CreateCapacityReservation";

    final CapacityReservationInner parameters =
        new CapacityReservationInner()
            .withSku(new Sku().withName(instanceType).withCapacity(numInstances))
            .withLocation(regionName.getValue())
            .withZones(List.of(azName))
            .withTags(tags);

    return getAzureApiCallBuilder(prettyError, log, requestName)
        .recordAndPublishQuotaCapacityError(subscriptionId, regionName)
        .makeNonIndexableResourceApiCall(
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureComputeManager(subscriptionId)
                    .serviceClient()
                    .getCapacityReservations()
                    .createOrUpdate(
                        resourceGroupName,
                        capacityReservationGroupName,
                        capacityReservationName,
                        parameters));
  }

  public CapacityReservationInner updateCapacityReservationSize(
      final ObjectId subscriptionId,
      final String resourceGroupName,
      final String capacityReservationGroupName,
      final String capacityReservationName,
      final long capacity,
      final Logger log) {
    final String prettyError = "Could not update capacity reservation size";
    final String requestName = "UpdateCapacityReservationSize";

    final CapacityReservationInner capacityReservation =
        findCapacityReservation(
            subscriptionId,
            resourceGroupName,
            capacityReservationGroupName,
            capacityReservationName,
            log);

    if (capacityReservation == null) {
      throw new AzureApiException("capacity reservation name", NDSErrorCode.INVALID_ARGUMENT);
    }

    final CapacityReservationUpdate parameters =
        new CapacityReservationUpdate()
            .withSku(new Sku().withName(capacityReservation.sku().name()).withCapacity(capacity));

    return getAzureApiCallBuilder(prettyError, log, requestName)
        .makeNonIndexableResourceApiCall(
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureComputeManager(subscriptionId)
                    .serviceClient()
                    .getCapacityReservations()
                    .update(
                        resourceGroupName,
                        capacityReservationGroupName,
                        capacityReservationName,
                        parameters));
  }

  public CapacityReservationInner findCapacityReservation(
      final ObjectId subscriptionId,
      final String resourceGroupName,
      final String capacityReservationGroupName,
      final String capacityReservationName,
      final Logger log) {
    final String prettyError = "Could not find capacity reservation";
    final String requestName = "FindCapacityReservation";

    return getAzureApiCallBuilder(prettyError, log, requestName)
        .makeNonIndexableResourceApiCall(
            () -> {
              try {
                return getAzureApiClientSvc()
                    .getCachedAzureComputeManager(subscriptionId)
                    .serviceClient()
                    .getCapacityReservations()
                    .get(resourceGroupName, capacityReservationGroupName, capacityReservationName);
              } catch (Exception e) {
                if (!(e instanceof ManagementException)
                    || ((ManagementException) e).getResponse().getStatusCode() != 404) {
                  throw new AzureApiException(
                      NDSErrorCode.PROBLEM_ACCESSING_RESOURCE_ON_AZURE.formatMessage(
                          "resource group name: "
                              + resourceGroupName
                              + ", capacity reservation group: "
                              + capacityReservationGroupName
                              + ", capacity reservation: "
                              + capacityReservationName),
                      e,
                      NDSErrorCode.PROBLEM_ACCESSING_RESOURCE_ON_AZURE);
                }
                return null;
              }
            });
  }

  public boolean deleteCapacityReservationWithVerification(
      final ObjectId subscriptionId,
      final String resourceGroupName,
      final String capacityReservationGroupName,
      final String capacityReservationName,
      final Logger log) {
    final CapacityReservationInner capacityReservation =
        findCapacityReservation(
            subscriptionId,
            resourceGroupName,
            capacityReservationGroupName,
            capacityReservationName,
            log);
    if (capacityReservation == null) {
      return true;
    }
    deleteCapacityReservation(
        subscriptionId,
        resourceGroupName,
        capacityReservationGroupName,
        capacityReservationName,
        log);
    return false;
  }

  public void deleteCapacityReservation(
      final ObjectId subscriptionId,
      final String resourceGroupName,
      final String capacityReservationGroupName,
      final String capacityReservationName,
      final Logger log) {
    final String prettyError = "Could not delete capacity reservation";
    final String requestName = "DeleteCapacityReservation";

    getAzureApiCallBuilder(prettyError, log, requestName)
        .makeNonIndexableResourceApiCall(
            () -> {
              getAzureApiClientSvc()
                  .getCachedAzureComputeManager(subscriptionId)
                  .serviceClient()
                  .getCapacityReservations()
                  .delete(resourceGroupName, capacityReservationGroupName, capacityReservationName);
              return null;
            });
  }

  public CapacityReservationGroupInner createCapacityReservationGroup(
      final ObjectId pSubscriptionId,
      final String resourceGroupName,
      final String capacityReservationGroupName,
      final AzureRegionName regionName,
      final List<String> azNames,
      final Map<String, String> tags,
      final Logger log) {
    final String prettyError = "Could not create capacity reservation group";
    final String requestName = "CreateCapacityReservationGroup";

    final CapacityReservationGroupInner parameters =
        new CapacityReservationGroupInner()
            .withLocation(regionName.getValue())
            .withZones(azNames)
            .withTags(tags);

    return getAzureApiCallBuilder(prettyError, log, requestName)
        .makeNonIndexableResourceApiCall(
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureComputeManager(pSubscriptionId)
                    .serviceClient()
                    .getCapacityReservationGroups()
                    .createOrUpdate(resourceGroupName, capacityReservationGroupName, parameters));
  }

  public CapacityReservationGroupInner findCapacityReservationGroup(
      final ObjectId subscriptionId,
      final String resourceGroupName,
      final String capacityReservationGroupName,
      final Logger log) {
    final String prettyError = "Could not find capacity reservation group";
    final String requestName = "FindCapacityReservationGroup";

    return getAzureApiCallBuilder(prettyError, log, requestName)
        .makeNonIndexableResourceApiCall(
            () ->
                InnerSupportsGetOrNull.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureComputeManager(subscriptionId)
                            .serviceClient()
                            .getCapacityReservationGroups())
                    .getByResourceGroupOrNull(resourceGroupName, capacityReservationGroupName));
  }

  public boolean deleteCapacityReservationGroupWithVerification(
      final ObjectId subscriptionId,
      final String resourceGroupName,
      final String capacityReservationGroupName,
      final Logger log) {
    final CapacityReservationGroupInner capacityReservationGroup =
        findCapacityReservationGroup(
            subscriptionId, resourceGroupName, capacityReservationGroupName, log);
    if (capacityReservationGroup == null) {
      return true;
    }
    deleteCapacityReservationGroup(
        subscriptionId, resourceGroupName, capacityReservationGroupName, log);
    return false;
  }

  public void deleteCapacityReservationGroup(
      final ObjectId subscriptionId,
      final String resourceGroupName,
      final String capacityReservationGroupName,
      final Logger log) {
    final String prettyError = "Could not delete capacity reservation group";
    final String requestName = "DeleteCapacityReservationGroup";

    getAzureApiCallBuilder(prettyError, log, requestName)
        .makeNonIndexableResourceApiCall(
            () -> {
              try {
                getAzureApiClientSvc()
                    .getCachedAzureComputeManager(subscriptionId)
                    .serviceClient()
                    .getCapacityReservationGroups()
                    .delete(resourceGroupName, capacityReservationGroupName);
              } catch (Exception pE) {
                if (!(pE instanceof ManagementException)
                    || ((ManagementException) pE).getResponse().getStatusCode() != 202) {
                  // NB: unclear why but this call will return a 202 but the SDK will throw an
                  // exception with no error message. The Azure docs don't list 202 as a response
                  // https://learn.microsoft.com/en-us/rest/api/compute/capacity-reservation-groups/delete?view=rest-compute-2024-03-01&tabs=HTTP#response
                  // so the assumption here is this is a bug in the Azure Java SDK
                  throw new AzureApiException(
                      "could not delete capacity reservation group: "
                          + capacityReservationGroupName
                          + " under resource group name: "
                          + resourceGroupName,
                      NDSErrorCode.PROBLEM_ACCESSING_RESOURCE_ON_AZURE);
                }
              }
              return null;
            });
  }

  private VirtualMachine.DefinitionStages.WithManagedCreate createVirtualMachineInnerStage(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final AzureAvailabilityZoneName pAvailabilityZone,
      final String pNetworkInterfaceName,
      final String pImageId,
      final String pUserData,
      final Logger pLogger) {
    final NetworkInterface networkInterface =
        findNetworkInterface(pSubscriptionId, pResourceGroupName, pNetworkInterfaceName, pLogger);
    if (networkInterface == null) {
      throw new AzureApiException("network interface name", NDSErrorCode.INVALID_ARGUMENT);
    }

    final VirtualMachine.DefinitionStages.WithFromImageCreateOptionsManaged innerStage =
        getAzureApiClientSvc()
            .getCachedAzureResourceManager(pSubscriptionId)
            .virtualMachines()
            .define(pName)
            .withRegion(pAzureRegionName.getValue())
            .withExistingResourceGroup(pResourceGroupName)
            .withExistingPrimaryNetworkInterface(networkInterface)
            .withGeneralizedLinuxCustomImage(pImageId)
            .withRootUsername("dummyUsername")
            // We append "aA1!" to a randomly generated alphanumeric string of 8 characters
            // in order to satisfy Azure's virtual machine password requirements:
            //   * Passwords must be 6 - 72 characters in length and meet 3 out of the
            //     following 4 complexity requirements:
            //       - Have lower characters
            //       - Have upper characters
            //       - Have a digit
            //       - Have a special character (Regex match [\W_])
            // Source:
            // https://docs.microsoft.com/en-us/azure/virtual-machines/linux/faq#what-are-the-password-requirements-when-creating-a-vm
            .withRootPassword(EncryptionUtils.randomAlphanumeric(8) + "aA1!")
            .withCustomData(pUserData);

    return pAvailabilityZone == null
        ? innerStage
        : innerStage.withAvailabilityZone(pAvailabilityZone.availabilityZoneId());
  }

  private VirtualMachine.DefinitionStages.WithCreate createVirtualMachineOuterStage(
      final VirtualMachine.DefinitionStages.WithManagedCreate pInnerStage,
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pAvailabilitySetName,
      final AzureInstanceSize pAzureInstanceSize,
      final AzureInstanceFamily pAzureInstanceFamily,
      final String pDiskName,
      @Nullable final String pCapacityReservationGroupId,
      final Map<String, String> pTags,
      final Logger pLogger) {
    final VirtualMachine.DefinitionStages.WithCreate outerStage =
        pInnerStage
            .withOSDiskStorageAccountType(StorageAccountTypes.PREMIUM_LRS)
            .withOSDiskName(pDiskName)
            .withEncryptionAtHost()
            .withSize(pAzureInstanceSize.getInstanceFamilies().get(pAzureInstanceFamily))
            .withTags(pTags);

    if (pCapacityReservationGroupId != null) {
      outerStage.withCapacityReservationGroup(pCapacityReservationGroupId);

      pLogger.info(
          "Attempting to use capacity reservation group id {} in instance VM creation",
          pCapacityReservationGroupId);
    }

    if (pAzureInstanceSize.isNVMe()) {
      outerStage.withOSDiskCaching(CachingTypes.NONE);
    }

    if (pAvailabilitySetName == null) {
      return outerStage;
    } else {
      final AvailabilitySet availabilitySet =
          findAvailabilitySet(pSubscriptionId, pResourceGroupName, pAvailabilitySetName, pLogger);

      if (availabilitySet == null) {
        throw new AzureApiException("availability set name", NDSErrorCode.INVALID_ARGUMENT);
      }

      return outerStage.withExistingAvailabilitySet(availabilitySet);
    }
  }

  private VirtualMachine createVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final String pOSDiskName,
      final VirtualMachine.DefinitionStages.WithCreate pStage,
      final AzureInstanceFamily pInstanceFamily,
      final AzureInstanceSize pInstanceSize,
      final AzureRegionName pRegionName,
      final Optional<AzureAvailabilityZoneName> pZoneName,
      final Logger pLogger) {

    final String prettyError = "Problem creating virtual machine";
    final String requestName = "CreateVirtualMachine";

    try {
      final VirtualMachine vm =
          getAzureApiCallBuilder(prettyError, pLogger, requestName)
              .expectedErrorCodes(
                  Set.of(
                      CommonErrorCode.INVALID_PARAMETER,
                      CommonErrorCode.TIMEOUT,
                      AzureErrorCode.CONFLICT,
                      AzureErrorCode.IN_USE))
              .recordAndPublishQuotaCapacityError(pSubscriptionId, pRegionName)
              .makeAsynchronousApiCall(
                  String.format(
                      "createVirtualMachine - subscriptionId: %s, resourceGroupName: %s, name: %s,"
                          + " instanceSize: %s, regionName: %s",
                      pSubscriptionId, pResourceGroupName, pName, pInstanceSize, pRegionName),
                  pStage::createAsync,
                  () -> findVirtualMachine(pSubscriptionId, pResourceGroupName, pName, pLogger));

      _resourceObservable.notify(
          new AzureResource(pSubscriptionId, pResourceGroupName, Type.VIRTUAL_MACHINE, pName),
          ResourceEvent.CREATED);
      _resourceObservable.notify(
          new AzureResource(pSubscriptionId, pResourceGroupName, Type.DISK, pOSDiskName),
          ResourceEvent.CREATED);

      return vm;
    } catch (final AzureApiException e) {
      if (NDSErrorCode.NO_CAPACITY.equals(e.getErrorCode())) {
        pLogger.debug(
            "Cannot create virtual machine: Azure out of capacity for instance size {} in region {}"
                + " in subscription {}.",
            pInstanceSize,
            pRegionName,
            pSubscriptionId);
      }
      throw e;
    }
  }

  public VirtualMachine createVirtualMachine(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final String pAvailabilitySetName,
      final String pOSDiskName,
      final String pNetworkInterfaceName,
      final AzureInstanceSize pAzureInstanceSize,
      final AzureInstanceFamily pAzureInstanceFamily,
      final String pImageId,
      final String pUserData,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return createVirtualMachine(
        pSubscriptionId,
        pResourceGroupName,
        pName,
        pOSDiskName,
        createVirtualMachineOuterStage(
            createVirtualMachineInnerStage(
                pSubscriptionId,
                pAzureRegionName,
                pResourceGroupName,
                pName,
                null,
                pNetworkInterfaceName,
                pImageId,
                pUserData,
                pLogger),
            pSubscriptionId,
            pResourceGroupName,
            pAvailabilitySetName,
            pAzureInstanceSize,
            pAzureInstanceFamily,
            pOSDiskName,
            null,
            pTags,
            pLogger),
        pAzureInstanceFamily,
        pAzureInstanceSize,
        pAzureRegionName,
        Optional.empty(),
        pLogger);
  }

  public VirtualMachine createVirtualMachine(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pVMName,
      final AzureAvailabilityZoneName pAvailabilityZone,
      final String pOSDiskName,
      final String pNetworkInterfaceName,
      final AzureInstanceSize pAzureInstanceSize,
      final AzureInstanceFamily pAzureInstanceFamily,
      final String pImageId,
      final String pUserData,
      @Nullable final String pCapacityReservationGroupId,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return createVirtualMachine(
        pSubscriptionId,
        pResourceGroupName,
        pVMName,
        pOSDiskName,
        createVirtualMachineOuterStage(
            createVirtualMachineInnerStage(
                pSubscriptionId,
                pAzureRegionName,
                pResourceGroupName,
                pVMName,
                pAvailabilityZone,
                pNetworkInterfaceName,
                pImageId,
                pUserData,
                pLogger),
            pSubscriptionId,
            pResourceGroupName,
            null,
            pAzureInstanceSize,
            pAzureInstanceFamily,
            pOSDiskName,
            pCapacityReservationGroupId,
            pTags,
            pLogger),
        pAzureInstanceFamily,
        pAzureInstanceSize,
        pAzureRegionName,
        Optional.of(pAvailabilityZone),
        pLogger);
  }

  public VirtualMachine updateVirtualMachineSize(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final AzureInstanceSize pAzureInstanceSize,
      final AzureInstanceFamily pAzureInstanceFamily,
      final String pVirtualMachineName,
      @Nullable final String pCapacityReservationGroupId,
      final Logger pLogger) {
    final String prettyErr = "Problem updating instance size";
    final String requestName = "UpdateVirtualMachineSize";
    try {
      final VirtualMachine virtualMachine =
          findVirtualMachine(pSubscriptionId, pResourceGroupName, pVirtualMachineName, pLogger);

      if (virtualMachine == null) {
        throw new AzureApiException("virtual machine name", NDSErrorCode.INVALID_ARGUMENT);
      }

      final VirtualMachine.Update update =
          virtualMachine
              .update()
              .withSize(pAzureInstanceSize.getInstanceFamilies().get(pAzureInstanceFamily));

      if (pCapacityReservationGroupId != null) {
        pLogger.info(
            "Attempting to use capacity reservation group id {} in instance size update",
            pCapacityReservationGroupId);

        update.withCapacityReservationGroup(pCapacityReservationGroupId);
      }

      return getAzureApiCallBuilder(prettyErr, pLogger, requestName)
          .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT))
          .makeAsynchronousApiCall(
              String.format(
                  "updateVirtualMachineSize - subscriptionId: %s, resourceGroupName: %s,"
                      + " azureInstanceSize: %s, azureInstanceFamily: %s, virtualMachineName: %s",
                  pSubscriptionId,
                  pResourceGroupName,
                  pAzureInstanceSize,
                  pAzureInstanceFamily,
                  pVirtualMachineName),
              update::applyAsync,
              () ->
                  findVirtualMachine(
                      pSubscriptionId, pResourceGroupName, pVirtualMachineName, pLogger));
    } catch (final AzureApiException e) {
      throw e;
    }
  }

  public void tagVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    final String prettyError = "Problem updating virtual machine tags";
    final String requestName = "updateTagsForVirtualMachine";

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeSynchronousApiCall(
            String.format(
                "tagVirtualMachine - subscriptionId: %s, resourceGroupName: %s, name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () -> {
              final VirtualMachine virtualMachine =
                  findVirtualMachine(pSubscriptionId, pResourceGroupName, pName, pLogger);

              if (virtualMachine == null) {
                throw new AzureApiException("virtual machine name", NDSErrorCode.INVALID_ARGUMENT);
              }

              virtualMachine.update().withTags(pTags).apply();
              return null;
            });
  }

  public VirtualMachine findVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem finding virtual machine";
    final String requestName = "FindVirtualMachine";

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeSynchronousApiCall(
            String.format(
                "findVirtualMachine - subscriptionId: %s, resourceGroupName: %s, name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () ->
                SupportsGettingByResourceGroupOrNull.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .virtualMachines())
                    .getByResourceGroupOrNull(pResourceGroupName, pName));
  }

  public PagedIterable<VirtualMachine> listVirtualMachines(
      final ObjectId pSubscriptionId, final Logger pLogger) {
    final String requestName = "ListVirtualMachines";

    return getAzureApiCallBuilder("Problem listing virtual machines", pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeListingSynchronousApiCall(
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureResourceManager(pSubscriptionId)
                    .virtualMachines()
                    .list());
  }

  public PagedIterable<NetworkInterface> listNetworkInterfaces(
      final ObjectId pSubscriptionId, final Logger pLogger) {
    return getAzureApiNetworkSvc().listNetworkInterfaces(pSubscriptionId, pLogger);
  }

  public Optional<NetworkInterface> getPrimaryNetworkInterface(
      final VirtualMachine pVm, final Logger pLogger) {
    return getAzureApiNetworkSvc().getPrimaryNetworkInterface(pVm, pLogger);
  }

  public String listNetworkInterfacesAndApplyForNextToken(
      final ObjectId pSubscriptionId,
      final Logger pLogger,
      final String pNextToken,
      final Consumer<List<NetworkInterface>> pResultConsumer) {
    return getAzureApiNetworkSvc()
        .listNetworkInterfacesAndApplyForNextToken(
            pSubscriptionId, pLogger, pNextToken, pResultConsumer);
  }

  public String listVirtualMachinesAndApplyForNextToken(
      final ObjectId pSubscriptionId,
      final Logger pLogger,
      final String pNextToken,
      final Consumer<List<VirtualMachine>> pResultConsumer) {
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final PagedIterable<VirtualMachine> cursor = listVirtualMachines(pSubscriptionId, pLogger);

    PagedResponse<VirtualMachine> page =
        cursor.iterableByPage().iterator().hasNext()
            ? cursor.iterableByPage().iterator().next()
            : null;
    if (!pNextToken.equals(AzureNDSDefaults.TOKEN_DONE)
        && !pNextToken.equals(AzureNDSDefaults.TOKEN_START)) {
      try {
        page = cursor.iterableByPage(pNextToken).iterator().next();
      } catch (final Exception pE) {
        if (pE instanceof NoSuchElementException) {
          return AzureNDSDefaults.TOKEN_DONE;
        }
        throw new AzureApiException(
            "Problem retrieving next page from next token", pE, AzureErrorCode.NEXT_PAGE_FAILURE);
      }
    }
    if (page == null) {
      return AzureNDSDefaults.TOKEN_DONE;
    }

    pResultConsumer.accept(page.getValue());

    final String newNextToken = page.getContinuationToken();
    return newNextToken == null ? AzureNDSDefaults.TOKEN_DONE : newNextToken;
  }

  public PagedIterable<VirtualMachine> getVMsInResourceGroup(
      final ObjectId pSubscriptionId, final String pResourceGroup, final Logger pLogger) {
    final String requestName = "GetVMsInResourceGroup";

    return getAzureApiCallBuilder(
            "Problem listing virtual machines in resource group", pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT))
        .makeListingSynchronousApiCall(
            () ->
                SupportsListingByResourceGroupOrEmpty.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .virtualMachines())
                    .listByResourceGroupOrEmpty(pResourceGroup));
  }

  public PagedIterable<Disk> listDisks(final ObjectId pSubscriptionId, final Logger pLogger) {
    final String requestName = "ListDisks";

    return getAzureApiCallBuilder("Problem listing disks", pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeListingSynchronousApiCall(
            () ->
                SupportsListingOrEmpty.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .disks())
                    .listOrEmpty());
  }

  public String listDisksAndApplyForNextToken(
      final ObjectId pSubscriptionId,
      final Logger pLogger,
      final String pNextToken,
      final Consumer<List<Disk>> pResultConsumer) {
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }
    final PagedIterable<Disk> cursor = listDisks(pSubscriptionId, pLogger);
    PagedResponse<Disk> page =
        cursor.iterableByPage().iterator().hasNext()
            ? cursor.iterableByPage().iterator().next()
            : null;

    if (!pNextToken.equals(AzureNDSDefaults.TOKEN_DONE)
        && !pNextToken.equals(AzureNDSDefaults.TOKEN_START)) {
      try {
        page = cursor.iterableByPage(pNextToken).iterator().next();
      } catch (final Exception pE) {
        if (pE instanceof NoSuchElementException) {
          return AzureNDSDefaults.TOKEN_DONE;
        }
        throw new AzureApiException(
            "Problem retrieving next page from next token", pE, AzureErrorCode.NEXT_PAGE_FAILURE);
      }
    }
    if (page == null) {
      return AzureNDSDefaults.TOKEN_DONE;
    }

    pResultConsumer.accept(page.getValue());

    final String newNextToken = page.getContinuationToken();
    return newNextToken == null ? AzureNDSDefaults.TOKEN_DONE : newNextToken;
  }

  public PagedIterable<ResourceGroup> listResourceGroups(
      final ObjectId pSubscriptionId, final Logger pLogger) {
    final String requestName = "ListResourceGroups";

    return getAzureApiCallBuilder("Problem listing resource groups", pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT))
        .makeListingSynchronousApiCall(
            () ->
                SupportsListingOrEmpty.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .resourceGroups())
                    .listOrEmpty());
  }

  public String listResourceGroupsAndApplyForNextToken(
      final ObjectId pSubscriptionId,
      final Logger pLogger,
      final String pNextToken,
      final Consumer<List<ResourceGroup>> pResultConsumer) {
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final PagedIterable<ResourceGroup> cursor = listResourceGroups(pSubscriptionId, pLogger);
    PagedResponse<ResourceGroup> page =
        cursor.iterableByPage().iterator().hasNext()
            ? cursor.iterableByPage().iterator().next()
            : null;

    if (!pNextToken.equals(AzureNDSDefaults.TOKEN_DONE)
        && !pNextToken.equals(AzureNDSDefaults.TOKEN_START)) {
      try {
        page = cursor.iterableByPage(pNextToken).iterator().next();
      } catch (final Exception pE) {
        if (pE instanceof NoSuchElementException) {
          return AzureNDSDefaults.TOKEN_DONE;
        }
        throw new AzureApiException(
            "Problem retrieving next page from next token", pE, AzureErrorCode.NEXT_PAGE_FAILURE);
      }
    }
    if (page == null) {
      return AzureNDSDefaults.TOKEN_DONE;
    }

    pResultConsumer.accept(page.getValue());

    final String newNextToken = page.getContinuationToken();
    return newNextToken == null ? AzureNDSDefaults.TOKEN_DONE : newNextToken;
  }

  public PagedIterable<Snapshot> listSnapshots(
      final ObjectId pSubscriptionId, final Logger pLogger) {
    final String requestName = "ListSnapshots";

    return getAzureApiCallBuilder("Problem listing snapshots", pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeListingSynchronousApiCall(
            () ->
                SupportsListingOrEmpty.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .snapshots())
                    .listOrEmpty());
  }

  public String listSnapshotsAndApplyForNextToken(
      final ObjectId pSubscriptionId,
      final Logger pLogger,
      final String pNextToken,
      final Consumer<List<Snapshot>> pResultConsumer) {
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final PagedIterable<Snapshot> cursor = listSnapshots(pSubscriptionId, pLogger);

    PagedResponse<Snapshot> page =
        cursor.iterableByPage().iterator().hasNext()
            ? cursor.iterableByPage().iterator().next()
            : null;
    if (!pNextToken.equals(AzureNDSDefaults.TOKEN_DONE)
        && !pNextToken.equals(AzureNDSDefaults.TOKEN_START)) {
      try {
        page = cursor.iterableByPage(pNextToken).iterator().next();
      } catch (final Exception pE) {
        if (pE instanceof NoSuchElementException) {
          return AzureNDSDefaults.TOKEN_DONE;
        }
        throw new AzureApiException(
            "Problem retrieving next page from next token", pE, AzureErrorCode.NEXT_PAGE_FAILURE);
      }
    }
    if (page == null) {
      return AzureNDSDefaults.TOKEN_DONE;
    }

    pResultConsumer.accept(page.getValue());

    final String newNextToken = page.getContinuationToken();
    return newNextToken == null ? AzureNDSDefaults.TOKEN_DONE : newNextToken;
  }

  public PagedIterable<PublicIpAddress> listPublicIps(
      final ObjectId pSubscriptionId, final Logger pLogger) {
    return getAzureApiNetworkSvc().listPublicIps(pSubscriptionId, pLogger);
  }

  public String listPublicIpsAndApplyForNextToken(
      final ObjectId pSubscriptionId,
      final Logger pLogger,
      final String pNextToken,
      final Consumer<List<PublicIpAddress>> pResultConsumer) {
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final PagedIterable<PublicIpAddress> cursor = listPublicIps(pSubscriptionId, pLogger);

    PagedResponse<PublicIpAddress> page =
        cursor.iterableByPage().iterator().hasNext()
            ? cursor.iterableByPage().iterator().next()
            : null;
    if (!pNextToken.equals(AzureNDSDefaults.TOKEN_DONE)
        && !pNextToken.equals(AzureNDSDefaults.TOKEN_START)) {
      try {
        page = cursor.iterableByPage(pNextToken).iterator().next();
      } catch (final Exception pE) {
        if (pE instanceof NoSuchElementException) {
          return AzureNDSDefaults.TOKEN_DONE;
        }
        throw new AzureApiException(
            "Problem retrieving next page from next token", pE, AzureErrorCode.NEXT_PAGE_FAILURE);
      }
    }
    if (page == null) {
      return AzureNDSDefaults.TOKEN_DONE;
    }

    pResultConsumer.accept(page.getValue());

    final String newNextToken = page.getContinuationToken();
    return newNextToken == null ? AzureNDSDefaults.TOKEN_DONE : newNextToken;
  }

  public PagedIterable<CapacityReservationGroupInner> listCapacityReservationGroups(
      final ObjectId pSubscriptionId, final Logger pLogger) {
    final String prettyError = "Problem listing capacity reservation groups";
    final String requestName = "ListCapacityReservationGroups";

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeNonIndexableResourceApiCall(
            () ->
                SupportsListingByResourceGroupOrEmpty.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureComputeManager(pSubscriptionId)
                            .serviceClient()
                            .getCapacityReservationGroups())
                    .list());
  }

  public PagedIterable<CapacityReservationGroupInner> listCapacityReservationGroups(
      final ObjectId subscriptionId, final String resourceGroupName, final Logger logger) {
    final String prettyError = "Problem listing capacity reservation groups";
    final String requestName = "ListCapacityReservationGroups";

    return getAzureApiCallBuilder(prettyError, logger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeNonIndexableResourceApiCall(
            () ->
                SupportsListingByResourceGroupOrEmpty.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureComputeManager(subscriptionId)
                            .serviceClient()
                            .getCapacityReservationGroups())
                    .listByResourceGroupOrEmpty(resourceGroupName));
  }

  public String listCapacityReservationGroupsAndApplyForNextToken(
      final ObjectId pSubscriptionId,
      final Logger pLogger,
      final String pNextToken,
      final Consumer<List<CapacityReservationGroupInner>> pResultConsumer) {
    if (pNextToken == null) {
      throw new IllegalArgumentException("pNextToken should not be null");
    }

    final PagedIterable<CapacityReservationGroupInner> cursor =
        listCapacityReservationGroups(pSubscriptionId, pLogger);

    PagedResponse<CapacityReservationGroupInner> page =
        cursor.iterableByPage().iterator().hasNext()
            ? cursor.iterableByPage().iterator().next()
            : null;
    if (!pNextToken.equals(AzureNDSDefaults.TOKEN_DONE)
        && !pNextToken.equals(AzureNDSDefaults.TOKEN_START)) {
      try {
        page = cursor.iterableByPage(pNextToken).iterator().next();
      } catch (final Exception pE) {
        if (pE instanceof NoSuchElementException) {
          return AzureNDSDefaults.TOKEN_DONE;
        }
        throw new AzureApiException(
            "Problem retrieving next page from next token", pE, AzureErrorCode.NEXT_PAGE_FAILURE);
      }
    }
    if (page == null) {
      return AzureNDSDefaults.TOKEN_DONE;
    }

    pResultConsumer.accept(page.getValue());

    final String newNextToken = page.getContinuationToken();
    return newNextToken == null ? AzureNDSDefaults.TOKEN_DONE : newNextToken;
  }

  public VirtualMachine attachDataDiskToVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualMachineName,
      final String pDataDiskName,
      final boolean pIsNvmeInstance,
      final Logger pLogger) {
    final VirtualMachine virtualMachine =
        findVirtualMachine(pSubscriptionId, pResourceGroupName, pVirtualMachineName, pLogger);
    if (virtualMachine == null) {
      throw new AzureApiException("virtual machine name", NDSErrorCode.INVALID_ARGUMENT);
    }

    final Disk dataDisk = findDisk(pSubscriptionId, pResourceGroupName, pDataDiskName, pLogger);
    if (dataDisk == null) {
      throw new AzureApiException(
          AZURE_DISK_NOT_FOUND_ERROR_MESSAGE, NDSErrorCode.INVALID_ARGUMENT);
    }

    final String prettyError = "Problem attaching data disk to virtual machine";
    final String requestName = "AttachDataDiskToVirtualMachine";

    final AzureDiskType realDiskType =
        AzureDiskType.getForSizeGB(dataDisk.sizeInGB(), dataDisk.sku());
    //  Only Disk CachingType 'None' is supported for disk with size greater than 4095 GB.
    final CachingTypes cachingType =
        realDiskType.equals(AzureDiskType.P60)
                || pIsNvmeInstance
                || realDiskType.equals(AzureDiskType.V2)
            ? CachingTypes.NONE
            : CachingTypes.READ_ONLY;

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT))
        .makeAsynchronousApiCall(
            String.format(
                "attachDataDiskToVirtualMachine - subscriptionId: %s, resourceGroupName: %s,"
                    + " virtualMachineName: %s, dataDiskName: %s, cachingType: %s",
                pSubscriptionId,
                pResourceGroupName,
                pVirtualMachineName,
                pDataDiskName,
                cachingType),
            virtualMachine.update().withExistingDataDisk(dataDisk, -1, cachingType)::applyAsync,
            () ->
                findVirtualMachine(
                    pSubscriptionId, pResourceGroupName, pVirtualMachineName, pLogger),
            getBeforeHookForDiskRelatedApiCall(requestName, dataDisk.regionName(), realDiskType),
            getAfterHookForDiskRelatedApiCall(requestName, dataDisk.regionName(), realDiskType));
  }

  public void detachDataDiskFromVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualMachineName,
      final String pDataDiskName,
      final Logger pLogger) {
    final VirtualMachine virtualMachine =
        findVirtualMachine(pSubscriptionId, pResourceGroupName, pVirtualMachineName, pLogger);
    if (virtualMachine == null) {
      throw new AzureApiException("virtual machine name", NDSErrorCode.INVALID_ARGUMENT);
    }

    final Disk dataDisk = findDisk(pSubscriptionId, pResourceGroupName, pDataDiskName, pLogger);
    if (dataDisk == null) {
      throw new AzureApiException(
          AZURE_DISK_NOT_FOUND_ERROR_MESSAGE, NDSErrorCode.INVALID_ARGUMENT);
    } else if (!dataDisk.isAttachedToVirtualMachine()) {
      pLogger.info(
          "dataDisk with id={} is not attached to any VM. Not doing any work.", dataDisk.id());
      return;
    } else if (!virtualMachine.id().equals(dataDisk.virtualMachineId())) {
      throw new AzureApiException(
          String.format(
              "data disk id=%s and virtual machine id=%s mismatch",
              dataDisk.virtualMachineId(), virtualMachine.id()),
          NDSErrorCode.INVALID_ARGUMENT);
    }

    final String prettyError = "Problem detaching disk from virtual machine";
    final String requestName = "DetachDataDiskFromVirtualMachine";

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT))
        .makeSynchronousApiCall(
            String.format(
                "detachDataDiskFromVirtualMachine - subscriptionId: %s, resourceGroupName: %s,"
                    + " virtualMachineName: %s, dataDiskName: %s",
                pSubscriptionId, pResourceGroupName, pVirtualMachineName, pDataDiskName),
            virtualMachine.update().withoutDataDisk(0)::apply);
  }

  public boolean deleteVirtualMachineWithVerification(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final VirtualMachine vm =
        findVirtualMachine(pSubscriptionId, pResourceGroupName, pName, pLogger);
    if (vm == null) {
      return true;
    }
    deleteVirtualMachine(pSubscriptionId, pResourceGroupName, pName, pLogger);
    return false;
  }

  // Deletes a virtual machine and returns true if successful, false otherwise.
  //
  // The deletion process follows a retry pattern:
  // 1. Attempt initial deletion via API call
  // 2. If initial attempt fails, verify if VM still exists
  // 3. If VM exists, retry deletion once
  // 4. If retry fails, verify final VM state
  //
  // This approach handles race conditions where the VM may have been deleted
  // despite the API call failing.
  public boolean deleteVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    try {
      pLogger.info(
          "Attempting to delete virtual machine - subscriptionId: {}, resourceGroupName: {}, name:"
              + " {}",
          pSubscriptionId,
          pResourceGroupName,
          pName);

      makeDeleteVirtualMachineAPICall(pSubscriptionId, pResourceGroupName, pName, pLogger);

      return true;
    } catch (final AzureApiException e) {
      pLogger.warn(
          "First attempt to delete virtual machine failed - VM Name: {}, Error Code: {}, Error"
              + " Message: {}",
          pName,
          e.getErrorCode(),
          e.getMessage());

      final VirtualMachine vm =
          findVirtualMachine(pSubscriptionId, pResourceGroupName, pName, pLogger);
      if (vm == null) {
        pLogger.info(
            "Virtual machine not found after deletion error, considering deletion successful - VM"
                + " Name: {}",
            pName);
        return true;
      }

      try {
        pLogger.info("Retrying virtual machine deletion - VM Name: {}", pName);

        makeDeleteVirtualMachineAPICall(pSubscriptionId, pResourceGroupName, pName, pLogger);

        return true;
      } catch (final AzureApiException retryException) {
        pLogger.error(
            "Retry attempt to delete virtual machine failed - VM Name: {}, Error Code: {}, Error"
                + " Message: {}",
            pName,
            retryException.getErrorCode(),
            retryException.getMessage());

        final VirtualMachine vm2 =
            findVirtualMachine(pSubscriptionId, pResourceGroupName, pName, pLogger);
        if (vm2 == null) {
          pLogger.info(
              "Virtual machine not found after deletion error in retry attempt, considering"
                  + " deletion successful - VM Name: {}",
              pName);
          return true;
        }

        return false;
      }
    }
  }

  private void makeDeleteVirtualMachineAPICall(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem deleting virtual machine";
    final String requestName = "DeleteVirtualMachine";
    final boolean forceDelete = false;

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeAsynchronousDeleteCall(
            pName,
            String.format(
                "deleteVirtualMachine - subscriptionId: %s, resourceGroupName: %s, name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureResourceManager(pSubscriptionId)
                    .virtualMachines()
                    .deleteByResourceGroupAsync(pResourceGroupName, pName, forceDelete),
            () -> findVirtualMachine(pSubscriptionId, pResourceGroupName, pName, pLogger),
            vm ->
                AzureProvisioningState.fromValue(vm.provisioningState())
                    == AzureProvisioningState.DELETING);
  }

  // Create snapshot from a data disk in the same resource group.
  public Snapshot createSnapshot(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final String pDataDiskName,
      final boolean pIncremental,
      final boolean pUseStandardStorage,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return createSnapshot(
        pSubscriptionId,
        pAzureRegionName,
        pResourceGroupName,
        pName,
        null,
        pDataDiskName,
        pIncremental,
        pUseStandardStorage,
        pTags,
        pLogger);
  }

  // Create snapshot from a data disk in a different resource group.
  // This is used in resurrection, in which the data disk and the new cluster belong to
  // different resource groups.
  public Snapshot createSnapshot(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final String pDataDiskResourceGroupName,
      final String pDataDiskName,
      final boolean pIncremental,
      final boolean pUseStandardStorage,
      final Map<String, String> pTags,
      final Logger pLogger) {
    final Disk dataDisk =
        findDisk(
            pSubscriptionId,
            Optional.ofNullable(pDataDiskResourceGroupName).orElse(pResourceGroupName),
            pDataDiskName,
            pLogger);
    if (dataDisk == null) {
      throw new AzureApiException(
          AZURE_DISK_NOT_FOUND_ERROR_MESSAGE, NDSErrorCode.INVALID_ARGUMENT);
    }
    return createSnapshot(
        pSubscriptionId,
        pAzureRegionName,
        pResourceGroupName,
        pName,
        dataDisk,
        pIncremental,
        pUseStandardStorage,
        pTags,
        pLogger);
  }

  /**
   * Create snapshot from an azure data disk
   *
   * @param pSubscriptionId: Azure subscription id
   * @param pAzureRegionName: Azure region
   * @param pResourceGroupName: The resource group for the snapshot
   * @param pName: The snapshot name
   * @param pDataDisk: The data disk to create snapshot from
   */
  private Snapshot createSnapshot(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final Disk pDataDisk,
      final boolean pIncremental,
      final boolean pUseStandardStorage,
      final Map<String, String> pTags,
      final Logger pLogger) {
    final String prettyError = "Problem creating azure snapshot";
    final String requestName = "CreateSnapshot";

    final DiskSkuTypes dataDiskSku = pDataDisk.sku();
    // Only incremental snapshots are supported for disks of Sku PremiumV2_LRS
    final boolean incremental = dataDiskSku == DiskSkuTypes.PREMIUM_V2_LRS || pIncremental;
    // incremental snapshots require standard storage
    final boolean useStandardStorage = incremental || pUseStandardStorage;
    final Snapshot.DefinitionStages.WithCreate stage =
        getAzureApiClientSvc()
            .getCachedAzureResourceManager(pSubscriptionId)
            .snapshots()
            .define(pName)
            .withRegion(pAzureRegionName.getValue())
            .withExistingResourceGroup(pResourceGroupName)
            .withDataFromDisk(pDataDisk)
            .withIncremental(incremental);

    final AzureDiskType realDiskType =
        AzureDiskType.getForSizeGB(pDataDisk.sizeInGB(), dataDiskSku);
    if (useStandardStorage) {
      if (realDiskType.equals(AzureDiskType.V2)
          && AzureBackupSnapshotUtil.SSDV2_ZRS_SUPPORTED_REGIONS.contains(pAzureRegionName)) {
        stage.withSku(SnapshotSkuType.STANDARD_ZRS);
      } else if (AzureBackupSnapshotUtil.STANDARD_ZRS_SUPPORTED_REGIONS.contains(
          pAzureRegionName)) {
        stage.withSku(SnapshotSkuType.STANDARD_ZRS);
      } else {
        stage.withSku(SnapshotSkuType.STANDARD_LRS);
      }
    } else {
      final SnapshotSkuType snapshotSkuType = AzureDiskType.getSnapshotSkuForDisk(dataDiskSku);
      stage.withSku(snapshotSkuType);
    }

    if (pTags != null) {
      stage.withTags(pTags);
    }

    final Snapshot snapshot =
        getAzureApiCallBuilder(prettyError, pLogger, requestName)
            .expectedErrorCodes(
                Set.of(
                    CommonErrorCode.INVALID_PARAMETER,
                    CommonErrorCode.TIMEOUT,
                    CommonErrorCode.SERVER_ERROR,
                    AzureErrorCode.CONFLICT))
            .recordAndPublishQuotaCapacityError(pSubscriptionId, pAzureRegionName)
            .makeAsynchronousApiCall(
                String.format(
                    "createSnapshot - subscriptionId: %s, azureRegionName: %s, resourceGroupName:"
                        + " %s, name: %s, dataDiskName: %s",
                    pSubscriptionId,
                    pAzureRegionName,
                    pResourceGroupName,
                    pName,
                    Optional.of(pDataDisk).map(Disk::name).orElse(null)),
                stage::createAsync,
                () -> findSnapshot(pSubscriptionId, pResourceGroupName, pName, pLogger),
                getBeforeHookForDiskRelatedApiCall(
                    requestName, pAzureRegionName.getValue(), realDiskType),
                getAfterHookForDiskRelatedApiCall(
                    requestName, pAzureRegionName.getValue(), realDiskType));

    _resourceObservable.notify(
        new AzureResource(pSubscriptionId, pResourceGroupName, Type.SNAPSHOT, pName),
        ResourceEvent.CREATED);

    return snapshot;
  }

  public SnapshotInner copySnapshotAcrossRegions(
      final ObjectId pTargetSubscriptionId,
      final String pSourceSubscriptionId,
      final String pSourceResourceId,
      final AzureRegionName pAzureRegionName,
      final String pSourceResourceGroup,
      final String pTargetResourceGroup,
      final String pNewSnapshotName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    try {
      final SnapshotInner snapshot =
          getAzureApiClientSvc()
              .getCachedAzureResourceManager(pTargetSubscriptionId)
              .virtualMachines()
              .manager()
              .serviceClient()
              .getSnapshots()
              .createOrUpdate(
                  pTargetResourceGroup,
                  pNewSnapshotName,
                  new SnapshotInner()
                      .withLocation(pAzureRegionName.getValue())
                      .withIncremental(true)
                      .withCreationData(
                          new CreationData()
                              .withCreateOption(DiskCreateOption.COPY_START)
                              .withSourceResourceId(
                                  String.format(
                                      DISK_FORMAT,
                                      pSourceSubscriptionId,
                                      pSourceResourceGroup,
                                      pSourceResourceId)))
                      .withTags(pTags));

      _resourceObservable.notify(
          new AzureResource(
              pTargetSubscriptionId, pTargetResourceGroup, Type.SNAPSHOT, pNewSnapshotName),
          ResourceEvent.CREATED);

      return snapshot;
    } catch (final RuntimeException rE) {
      throw getAzureApiErrorSvc()
          .handleApiRuntimeException(
              rE, "Problem copying snapshot across regions", Set.of(), pLogger)
          .getLeft();
    }
  }

  public SnapshotInner findSnapshot(
      final ObjectId pTargetSubscriptionId,
      final String pTargetResourceGroup,
      final String pNewSnapshotName) {
    return getAzureApiClientSvc()
        .getCachedAzureResourceManager(pTargetSubscriptionId)
        .virtualMachines()
        .manager()
        .serviceClient()
        .getSnapshots()
        .getByResourceGroup(pTargetResourceGroup, pNewSnapshotName);
  }

  /**
   * Attempt to delete a snapshot from Azure. If it already does not exist, this returns true. If it
   * does exist at the time this is called, and we issued a delete snapshot call, this returns
   * false.
   *
   * @return boolean truthy if the snapshot does not exist at the time of calling this method.
   */
  public boolean deleteSnapshotWithVerification(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final Snapshot snapshot = findSnapshot(pSubscriptionId, pResourceGroupName, pName, pLogger);
    if (snapshot == null) {
      return true;
    }
    deleteSnapshot(pSubscriptionId, pResourceGroupName, pName, pLogger);
    return false;
  }

  public void deleteSnapshot(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem deleting snapshot";
    final String requestName = "DeleteSnapshot";

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeAsynchronousDeleteCall(
            pName,
            String.format(
                "deleteSnapshot - subscriptionId: %s, resourceGroupName: %s," + " name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureResourceManager(pSubscriptionId)
                    .snapshots()
                    .deleteByResourceGroupAsync(pResourceGroupName, pName),
            () -> findSnapshot(pSubscriptionId, pResourceGroupName, pName, pLogger),
            snapshot ->
                AzureProvisioningState.fromValue(snapshot.innerModel().provisioningState())
                    == AzureProvisioningState.DELETING);
  }

  public Snapshot findSnapshot(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem finding snapshot";
    final String requestName = "FindSnapshot";

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeSynchronousApiCall(
            String.format(
                "findSnapshot - subscriptionId: %s, resourceGroupName: %s, name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () ->
                SupportsGettingByResourceGroupOrNull.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .snapshots())
                    .getByResourceGroupOrNull(pResourceGroupName, pName));
  }

  public void tagSnapshot(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    final String prettyError = "Problem updating snapshot tags";
    final String requestName = "updateTagsForSnapshot";

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeSynchronousApiCall(
            String.format(
                "tagSnapshot - subscriptionId: %s, resourceGroupName: %s, name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () -> {
              final Snapshot snapshot =
                  findSnapshot(pSubscriptionId, pResourceGroupName, pName, pLogger);

              if (snapshot == null) {
                throw new AzureApiException(
                    String.format(
                        "Snapshot cannot be found (name: %s, resource group: %s, subscription ID:"
                            + " %s)",
                        pName, pResourceGroupName, pSubscriptionId),
                    NDSErrorCode.INVALID_ARGUMENT);
              }

              // update tags to be any existing tags, plus the new tags passed in
              // if a tag already exists on the snapshot and is provided in pTags,
              // then the tag in pTags will replace it
              final Map<String, String> tags;
              if (snapshot.tags() != null) {
                tags = new HashMap<>(snapshot.tags());
                tags.putAll(pTags);
              } else {
                tags = new HashMap<>(pTags);
              }

              snapshot.update().withTags(tags).apply();
              return null;
            });
  }

  public void restartVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem restarting virtual machine";
    final String requestName = "RestartVirtualMachine";

    pLogger.debug(
        "Requesting restart virtual machine for {} in resource group {}",
        pName,
        pResourceGroupName);

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT))
        .makeAsynchronousUpdatedCall(
            pName,
            String.format(
                "restartVirtualMachine - subscriptionId: %s, vm: %s, resource group: %s",
                pSubscriptionId, pName, pResourceGroupName),
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureResourceManager(pSubscriptionId)
                    .virtualMachines()
                    .restartAsync(pResourceGroupName, pName)
                    .timeout(ASYNC_STOP_START_DEALLOCATE_TIMEOUT),
            () -> true
            // This one starts and stops in the same state so we always return true to avoid missing
            // a desired state change
            );
  }

  public void startVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final AzureRegionName pRegionName,
      final AzureInstanceSize pInstanceSize,
      final Logger pLogger) {
    final String prettyError = "Problem starting virtual machine";
    final String requestName = "StartVirtualMachine";
    pLogger.debug(
        "Requesting start virtual machine for {} in resource group {}", pName, pResourceGroupName);
    try {
      getAzureApiCallBuilder(prettyError, pLogger, requestName)
          .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT))
          .recordAndPublishQuotaCapacityError(pSubscriptionId, pRegionName)
          .makeAsynchronousUpdatedCall(
              pName,
              String.format(
                  "startVirtualMachine - subscriptionId: %s, vm: %s, resource group: %s",
                  pSubscriptionId, pName, pResourceGroupName),
              () ->
                  getAzureApiClientSvc()
                      .getCachedAzureResourceManager(pSubscriptionId)
                      .virtualMachines()
                      .startAsync(pResourceGroupName, pName)
                      .timeout(ASYNC_STOP_START_DEALLOCATE_TIMEOUT),
              () ->
                  isVMPowerStateChanged(
                      pSubscriptionId, pResourceGroupName, pName, PowerState.STOPPED, pLogger));

    } catch (final AzureApiException e) {
      if (NDSErrorCode.NO_CAPACITY.equals(e.getErrorCode())) {
        pLogger.debug(
            "Cannot start virtual machine with id {}: Virtual machine out of capacity for instance"
                + " size {} in region {}.",
            pSubscriptionId,
            pInstanceSize,
            pRegionName);
      }
      throw e;
    }
  }

  public void stopVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem stopping virtual machine";
    final String requestName = "StopVirtualMachine";

    pLogger.debug(
        "Requesting stop virtual machine for {} in resource group {}", pName, pResourceGroupName);

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT))
        .makeAsynchronousUpdatedCall(
            pName,
            String.format(
                "stopVirtualMachine - subscriptionId: %s, vm: %s, resource group: %s",
                pSubscriptionId, pName, pResourceGroupName),
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureResourceManager(pSubscriptionId)
                    .virtualMachines()
                    .powerOffAsync(pResourceGroupName, pName)
                    .timeout(ASYNC_STOP_START_DEALLOCATE_TIMEOUT),
            () -> true);
  }

  private boolean isVMPowerStateChanged(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final PowerState pStartPowerState,
      final Logger pLogger) {
    return Optional.ofNullable(
                findVirtualMachine(pSubscriptionId, pResourceGroupName, pName, pLogger))
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        String.format(
                            "Unable to find virtual machine %s in resource group %s in"
                                + " subscription %s",
                            pName, pResourceGroupName, pSubscriptionId)))
            .powerState()
        != pStartPowerState;
  }

  public void deallocateVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem deallocating virtual machine";
    final String requestName = "DeallocateVirtualMachine";

    pLogger.debug(
        "Requesting deallocate virtual machine for {} in resource group {}",
        pName,
        pResourceGroupName);

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT))
        .makeAsynchronousUpdatedCall(
            pName,
            String.format(
                "deallocateVirtualMachine - subscriptionId: %s, vm: %s, resource group: %s",
                pSubscriptionId, pName, pResourceGroupName),
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureResourceManager(pSubscriptionId)
                    .virtualMachines()
                    .deallocateAsync(pResourceGroupName, pName)
                    .timeout(ASYNC_STOP_START_DEALLOCATE_TIMEOUT),
            () -> true);
  }

  public Disk createDataDisk(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final AzureDiskType pDiskSizeType,
      final Integer pDiskSizeGB,
      final Integer pDiskIOPS,
      final Integer pDiskThroughput,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return this.createDataDisk(
        pSubscriptionId,
        pAzureRegionName,
        pResourceGroupName,
        null,
        pName,
        pDiskSizeType,
        pDiskSizeGB,
        pDiskIOPS,
        pDiskThroughput,
        pTags,
        pLogger);
  }

  public NDSAzureTempCredentialsView generateStorageContainerSasToken(
      final String pAccountName,
      final String pAccountKey,
      final String pContainerName,
      final String pPermissionString) {
    return getAzureApiStorageSvc()
        .generateStorageContainerSasToken(
            pAccountName, pAccountKey, pContainerName, pPermissionString);
  }

  public NDSAzureTempCredentialsView generateStorageContainerSasTokenForCloudProviderAccess(
      final String pTargetTenantId,
      final String pServiceUrl,
      final String pContainerName,
      final String pPermissionString,
      final Logger pLogger) {
    return generateStorageContainerSasTokenForCloudProviderAccess(
        pTargetTenantId,
        pServiceUrl,
        pContainerName,
        pPermissionString,
        Duration.ofHours(1),
        pLogger);
  }

  public NDSAzureTempCredentialsView generateStorageContainerSasTokenForCloudProviderAccess(
      final String pTargetTenantId,
      final String pServiceUrl,
      final String pContainerName,
      final String pPermissionString,
      final Duration pDuration,
      final Logger pLogger) {
    return getAzureApiStorageSvc()
        .generateStorageContainerSasTokenForSubscription(
            _subscriptionDao
                .findCloudProviderAccessSubscription()
                .orElseThrow(
                    () ->
                        new AzureApiException(
                            "Unable to retrieve cloud provider access Azure subscription.",
                            NDSErrorCode.INTERNAL)),
            pTargetTenantId,
            pServiceUrl,
            pContainerName,
            pPermissionString,
            pDuration,
            pLogger);
  }

  public NDSAzureTempCredentialsView generateStorageContainerSasTokenForSubscription(
      final ObjectId pSubscriptionObjectId,
      final String pTargetTenantId,
      final String pServiceUrl,
      final String pContainerName,
      final String pPermissionString,
      final Duration pDuration,
      final Logger pLogger) {
    return getAzureApiStorageSvc()
        .generateStorageContainerSasTokenForSubscription(
            getAzureSubscription(pSubscriptionObjectId),
            pTargetTenantId,
            pServiceUrl,
            pContainerName,
            pPermissionString,
            pDuration,
            pLogger);
  }

  public NDSAzureTempCredentialsView generateStorageAccountSasToken(
      final String pAccountName, final String pAccountKey, final String pPermissionString) {
    return getAzureApiStorageSvc()
        .generateStorageAccountSasToken(pAccountName, pAccountKey, pPermissionString);
  }

  public void downloadFile(
      final String pClientId,
      final String pClientSecret,
      final String pTenantId,
      final String pAccountName,
      final String pContainerName,
      final String pBlobPath,
      final String pLocalPath,
      final Logger pLogger) {
    getAzureApiStorageSvc()
        .downloadFile(
            pClientId,
            pClientSecret,
            pTenantId,
            pAccountName,
            pContainerName,
            pBlobPath,
            pLocalPath,
            pLogger);
  }

  /**
   * Creates a blob container in the specified storage account if it does not already exist.
   *
   * <p>Note: User delegation SAS tokens do not have the ability to create the blob container
   * they're scoped to. This method creates the container via a subscription's linked application.
   *
   * @param pSubscriptionObjectId The object ID of the Azure subscription.
   * @param pStorageAccountEndpoint The endpoint of the storage account.
   * @param pContainerName The name of the container to create.
   * @param pLogger The logger to use.
   * @return {@code true} if the container was newly created, {@code false} if it already existed.
   */
  public boolean createBlobContainerIfNotExists(
      final ObjectId pSubscriptionObjectId,
      final String pStorageAccountEndpoint,
      final String pContainerName,
      final Logger pLogger) {
    return getAzureApiStorageSvc()
        .createBlobContainerIfNotExists(
            pSubscriptionObjectId, pStorageAccountEndpoint, pContainerName, pLogger);
  }

  public boolean createBlobContainerIfNotExists(
      final String pContainerName, final String pSasUri, final String pSasToken, Logger pLogger) {
    return getAzureApiStorageSvc()
        .createBlobContainerIfNotExists(pContainerName, pSasUri, pSasToken, pLogger);
  }

  public boolean deleteBlobContainerIfExists(
      final String pContainerName,
      final String pSasUri,
      final String pSasToken,
      final Logger pLogger) {
    return getAzureApiStorageSvc()
        .deleteBlobContainerIfExists(pContainerName, pSasUri, pSasToken, pLogger);
  }

  public List<BlobItem> listBlobs(
      final NDSAzureTempCredentialsView pTempCredentials,
      final String pContainerName,
      final String pPrefix,
      final Integer pMaxKeys,
      final Logger pLogger) {
    return getAzureApiStorageSvc()
        .listBlobs(pTempCredentials, pContainerName, pPrefix, pMaxKeys, pLogger);
  }

  public List<BlobItem> listBlobs(
      final NDSAzureTempCredentialsView pTempCredentials,
      final String pContainerName,
      final String pPrefix,
      final Logger pLogger) {
    return getAzureApiStorageSvc().listBlobs(pTempCredentials, pContainerName, pPrefix, pLogger);
  }

  public BlobInputStream downloadBlob(
      final NDSAzureTempCredentialsView pTempCredentials,
      final String pContainerName,
      final String pPrefix,
      final Logger pLogger) {
    return getAzureApiStorageSvc().downloadBlob(pTempCredentials, pContainerName, pPrefix, pLogger);
  }

  public List<BlobContainerItem> listContainers(
      final String pAccountName, final String pAccountKey, final Logger pLogger) {
    return getAzureApiStorageSvc().listContainers(pAccountName, pAccountKey, pLogger);
  }

  public boolean doesContainerExist(
      final NDSAzureTempCredentialsView pTempCredentials,
      final String pContainerName,
      final Logger pLogger) {
    return getAzureApiStorageSvc().doesContainerExist(pTempCredentials, pContainerName, pLogger);
  }

  public void uploadBlob(
      final String pContainerName,
      final String pSasUri,
      final String pSasToken,
      final String pBlobPath,
      final InputStream pStream,
      Logger pLogger) {
    getAzureApiStorageSvc()
        .uploadBlob(pContainerName, pSasUri, pSasToken, pBlobPath, pStream, pLogger);
  }

  public String copyBlob(
      final String pContainerName,
      final String pSasUri,
      final String pSasToken,
      final String pSourceBlobPath,
      final String pDestinationBlobPath,
      Logger pLogger) {
    return getAzureApiStorageSvc()
        .copyBlob(
            pContainerName, pSasUri, pSasToken, pSourceBlobPath, pDestinationBlobPath, pLogger);
  }

  public boolean deleteBlob(
      final String pContainerName,
      final String pSasUri,
      final String pSasToken,
      final String pBlobPath,
      Logger pLogger) {
    return getAzureApiStorageSvc()
        .deleteBlob(pContainerName, pSasUri, pSasToken, pBlobPath, pLogger);
  }

  // we return an optional batch exception here because AzureApiCallBuilder does not properly handle
  // batch exceptions
  public Optional<BlobBatchStorageException> deleteBlobs(
      final String pContainerName,
      final String pSasUri,
      final String pSasToken,
      final List<String> pBlobsToDelete,
      final Logger pLogger) {
    return getAzureApiStorageSvc()
        .deleteBlobs(pContainerName, pSasUri, pSasToken, pBlobsToDelete, pLogger);
  }

  public void setBlobAccessTier(
      final NDSAzureTempCredentialsView pCreds,
      final String pContainerName,
      final String pBlobPath,
      final AccessTier pAccessTier,
      final Logger pLogger) {
    getAzureApiStorageSvc()
        .setBlobAccessTier(pCreds, pContainerName, pBlobPath, pAccessTier, pLogger);
  }

  public List<BlobItem> getBlobItemsInContainerDirectory(
      final String clientId,
      final String clientSecret,
      final String tenantId,
      final String accountName,
      final String containerName,
      final String directory,
      final Logger log) {
    return getAzureApiStorageSvc()
        .getBlobItemsInContainerDirectory(
            clientId, clientSecret, tenantId, accountName, containerName, directory, log);
  }

  private Callable<Void> getBeforeHookForDiskRelatedApiCall(
      final String pRequestName, final String pRegionName, final AzureDiskType pDiskType) {
    return () -> {
      incrementCounter(AZURE_DISK_API_CALLS_TOTAL, pRequestName, pRegionName, pDiskType.toString());
      return null;
    };
  }

  private TriFunction<Date, ErrorCode, Boolean, Void> getAfterHookForDiskRelatedApiCall(
      final String pRequestName, final String pRegionName, final AzureDiskType pDiskType) {
    return (startTime, errorCode, isExpected) -> {
      if (errorCode != CommonErrorCode.NONE) {
        incrementCounter(
            AZURE_DISK_API_ERROR_TOTAL,
            pRequestName,
            pRegionName,
            pDiskType.toString(),
            errorCode.name(),
            String.valueOf(isExpected));
      }
      NDSPromMetricsSvc.recordTimer(
          AZURE_DISK_API_CALL_LATENCY_SECONDS,
          startTime,
          TimeUnit.SECONDS,
          pRequestName,
          pRegionName,
          pDiskType.toString(),
          errorCode.name(),
          String.valueOf(isExpected));
      return null;
    };
  }

  public Disk createDataDisk(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final AzureAvailabilityZoneName pAzureAvailabilityZoneName,
      final String pName,
      final AzureDiskType pDiskType,
      final Integer pDiskSizeGB,
      final Integer pDiskIOPS,
      final Integer pDiskThroughput,
      final Map<String, String> pTags,
      final Logger pLogger) {
    // prom counter
    final String prettyError = "Problem creating data disk";
    final String requestName = "CreateDataDisk";

    final AzureDiskType realDiskType = AzureNDSDefaults.getRealAzureDiskType(pDiskType);
    final int diskSizeGB = pDiskType == AzureDiskType.V2 ? pDiskSizeGB : realDiskType.getSizeGB();

    final Disk.DefinitionStages.WithCreate definitionStage =
        getAzureApiClientSvc()
            .getCachedAzureResourceManager(pSubscriptionId)
            .disks()
            .define(pName)
            .withRegion(pAzureRegionName.getValue())
            .withExistingResourceGroup(pResourceGroupName)
            .withData()
            .withSizeInGB(diskSizeGB)
            .withSku(pDiskType.getDiskSkuType())
            .withTags(pTags);

    if (pAzureAvailabilityZoneName != null) {
      definitionStage.withAvailabilityZone(pAzureAvailabilityZoneName.availabilityZoneId());
    }
    if (pDiskType == AzureDiskType.V2) {
      ((Disk) definitionStage)
          .innerModel()
          .withDiskIopsReadWrite((long) pDiskIOPS)
          .withDiskMBpsReadWrite((long) pDiskThroughput);
      definitionStage.withLogicalSectorSizeInBytes(AZURE_PV2_LOGICAL_SECTOR_SIZE);
    }

    final Disk disk =
        getAzureApiCallBuilder(prettyError, pLogger, requestName)
            .expectedErrorCodes(
                Set.of(
                    CommonErrorCode.INVALID_PARAMETER,
                    CommonErrorCode.TIMEOUT,
                    CommonErrorCode.SERVER_ERROR,
                    AzureErrorCode.CONFLICT))
            .recordAndPublishQuotaCapacityError(pSubscriptionId, pAzureRegionName)
            .makeAsynchronousApiCall(
                String.format(
                    "createDataDisk - subscriptionId: %s, azureRegionName: %s, resourceGroupName:"
                        + " %s, azureAvailabilityZone: %s, name: %s, requestedDiskType: %s,"
                        + " realDiskType: %s",
                    pSubscriptionId,
                    pAzureRegionName,
                    pResourceGroupName,
                    Optional.ofNullable(pAzureAvailabilityZoneName)
                        .map(AzureAvailabilityZoneName::name)
                        .orElse(null),
                    pName,
                    pDiskType.name(),
                    realDiskType.name()),
                definitionStage::createAsync,
                () -> findDisk(pSubscriptionId, pResourceGroupName, pName, pLogger),
                getBeforeHookForDiskRelatedApiCall(
                    requestName, pAzureRegionName.getValue(), realDiskType),
                getAfterHookForDiskRelatedApiCall(
                    requestName, pAzureRegionName.getValue(), realDiskType));

    _resourceObservable.notify(
        new AzureResource(pSubscriptionId, pResourceGroupName, Type.DISK, pName),
        ResourceEvent.CREATED);

    return disk;
  }

  public Disk createDataDiskFromSnapshot(
      final ObjectId pSubscriptionId,
      final ObjectId pSnapshotSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pDataDiskResourceGroupName,
      final String pSnapshotResourceGroupName,
      final AzureAvailabilityZoneName pAzureAvailabilityZoneName,
      final String pDataDiskResourceName,
      final String pSnapshotName,
      final AzureDiskType pDiskType,
      final Map<String, String> pTags,
      final Logger pLogger) {
    final String prettyError = "Problem creating data disk";
    final String requestName = "CreateDataDiskFromSnapshot";
    final AzureResourceManager azureResourceManager =
        getAzureApiClientSvc().getCachedAzureResourceManager(pSubscriptionId);
    final Snapshot snapshot =
        findSnapshot(pSnapshotSubscriptionId, pSnapshotResourceGroupName, pSnapshotName, pLogger);
    if (snapshot == null) {
      throw new AzureApiException(
          String.format(
              "Snapshot cannot be found (name: %s, resource group: %s, subscription ID: %s)",
              pSnapshotName, pSnapshotResourceGroupName, pSnapshotSubscriptionId),
          NDSErrorCode.INVALID_ARGUMENT);
    }

    final Disk.DefinitionStages.WithCreateAndSize withCreateAndSize =
        azureResourceManager
            .disks()
            .define(pDataDiskResourceName)
            .withRegion(pAzureRegionName.getValue())
            .withExistingResourceGroup(pDataDiskResourceGroupName)
            .withLinuxFromSnapshot(snapshot);

    AzureDiskType realDiskType = null;
    if (pDiskType != null) {
      realDiskType = AzureNDSDefaults.getRealAzureDiskType(pDiskType);
      withCreateAndSize.withSizeInGB(realDiskType.getSizeGB());

      if (pAzureAvailabilityZoneName != null) {
        withCreateAndSize.withAvailabilityZone(pAzureAvailabilityZoneName.availabilityZoneId());
      }
    }

    // We need to set the sku to be PREMIUM_LRS for snapshots that are stored in standard zrs to
    // avoid errors.
    // Setting the sku as PREMIUM_LRS for the old snapshots is an no op as the disk for those
    // snapshots were already created implicitly as PREMIUN_LRS
    withCreateAndSize.withSku(DiskSkuTypes.PREMIUM_LRS);

    final Disk disk =
        getAzureApiCallBuilder(prettyError, pLogger, requestName)
            .expectedErrorCodes(
                Set.of(
                    CommonErrorCode.INVALID_PARAMETER,
                    CommonErrorCode.TIMEOUT,
                    CommonErrorCode.SERVER_ERROR,
                    AzureErrorCode.CONFLICT))
            .recordAndPublishQuotaCapacityError(pSubscriptionId, pAzureRegionName)
            .makeAsynchronousApiCall(
                String.format(
                    "createDataDiskFromSnapshot - subscriptionId: %s, azureRegionName: %s,"
                        + " dataDiskResourceGroupName: %s, snapshotResourceGroupName: %s,"
                        + " azureAvailabilityZone: %s, name: %s, snapshotName: %s,"
                        + " requestedDiskType: %s, realDiskType: %s",
                    pSubscriptionId,
                    pAzureRegionName,
                    pDataDiskResourceGroupName,
                    pSnapshotResourceGroupName,
                    pAzureAvailabilityZoneName,
                    pDataDiskResourceName,
                    pSnapshotName,
                    pDiskType,
                    realDiskType),
                withCreateAndSize.withTags(pTags)::createAsync,
                () ->
                    findDisk(
                        pSubscriptionId,
                        pDataDiskResourceGroupName,
                        pDataDiskResourceName,
                        pLogger),
                getBeforeHookForDiskRelatedApiCall(
                    requestName, pAzureRegionName.getValue(), realDiskType),
                getAfterHookForDiskRelatedApiCall(
                    requestName, pAzureRegionName.getValue(), realDiskType));

    _resourceObservable.notify(
        new AzureResource(
            pSubscriptionId, pDataDiskResourceGroupName, Type.DISK, pDataDiskResourceName),
        ResourceEvent.CREATED);

    return disk;
  }

  public Disk createV2DataDiskFromSnapshot(
      final ObjectId pSubscriptionId,
      final ObjectId pSnapshotSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pDataDiskResourceGroupName,
      final String pSnapshotResourceGroupName,
      final AzureAvailabilityZoneName pAzureAvailabilityZoneName,
      final String pDataDiskResourceName,
      final String pSnapshotName,
      final int pDiskSizeGB,
      final long pIops,
      final long pThroughput,
      final Map<String, String> pTags,
      final Logger pLogger) {
    final String prettyError = "Problem creating SSDV2 data disk";
    final String requestName = "CreateV2DataDiskFromSnapshot";
    final AzureResourceManager azureResourceManager =
        getAzureApiClientSvc().getCachedAzureResourceManager(pSubscriptionId);
    final Snapshot snapshot =
        findSnapshot(pSnapshotSubscriptionId, pSnapshotResourceGroupName, pSnapshotName, pLogger);
    if (snapshot == null) {
      throw new AzureApiException(
          String.format(
              "Snapshot cannot be found (name: %s, resource group: %s, subscription ID: %s)",
              pSnapshotName, pSnapshotResourceGroupName, pSnapshotSubscriptionId),
          NDSErrorCode.INVALID_ARGUMENT);
    }

    if (pAzureAvailabilityZoneName == null) {
      throw new AzureApiException(
          "Availability zone is required for creating SSDV2 data disk",
          NDSErrorCode.INVALID_ARGUMENT);
    }

    // When restoring a 4TB Pv1 cluster snapshot the snapshot size can be slightly greater
    // than the disk size from cluster description (4097 GB vs 4095 GB).
    final int diskSizeGB;
    final int snapshotSizeInGB = snapshot.sizeInGB();
    if (snapshotSizeInGB >= AzureDiskType.P50.getSizeGB()
        && snapshotSizeInGB <= AzureDiskType.P60.getSizeGB()
        && pDiskSizeGB >= AzureDiskType.P50.getSizeGB()
        && pDiskSizeGB <= AzureDiskType.P60.getSizeGB()) {
      diskSizeGB = Math.max(snapshot.sizeInGB(), pDiskSizeGB);
    } else {
      diskSizeGB = pDiskSizeGB;
    }

    final Disk.DefinitionStages.WithCreate withCreate =
        azureResourceManager
            .disks()
            .define(pDataDiskResourceName)
            .withRegion(pAzureRegionName.getValue())
            .withExistingResourceGroup(pDataDiskResourceGroupName)
            .withLinuxFromSnapshot(snapshot)
            .withSizeInGB(diskSizeGB)
            .withSku(DiskSkuTypes.PREMIUM_V2_LRS)
            .withAvailabilityZone(pAzureAvailabilityZoneName.availabilityZoneId())
            .withLogicalSectorSizeInBytes(AZURE_PV2_LOGICAL_SECTOR_SIZE);

    ((Disk) withCreate)
        .innerModel()
        .withDiskIopsReadWrite(pIops)
        .withDiskMBpsReadWrite(pThroughput);

    final Disk disk =
        getAzureApiCallBuilder(prettyError, pLogger, requestName)
            .expectedErrorCodes(
                Set.of(
                    CommonErrorCode.INVALID_PARAMETER,
                    CommonErrorCode.TIMEOUT,
                    CommonErrorCode.SERVER_ERROR,
                    AzureErrorCode.CONFLICT))
            .recordAndPublishQuotaCapacityError(pSubscriptionId, pAzureRegionName)
            .makeAsynchronousApiCall(
                String.format(
                    "createV2DataDiskFromSnapshot - subscriptionId: %s, azureRegionName: %s,"
                        + " dataDiskResourceGroupName: %s, snapshotResourceGroupName: %s,"
                        + " azureAvailabilityZone: %s, name: %s, snapshotName: %s,",
                    pSubscriptionId,
                    pAzureRegionName,
                    pDataDiskResourceGroupName,
                    pSnapshotResourceGroupName,
                    pAzureAvailabilityZoneName,
                    pDataDiskResourceName,
                    pSnapshotName),
                withCreate.withTags(pTags)::createAsync,
                () ->
                    findDisk(
                        pSubscriptionId,
                        pDataDiskResourceGroupName,
                        pDataDiskResourceName,
                        pLogger),
                getBeforeHookForDiskRelatedApiCall(
                    requestName, pAzureRegionName.getValue(), AzureDiskType.V2),
                getAfterHookForDiskRelatedApiCall(
                    requestName, pAzureRegionName.getValue(), AzureDiskType.V2));

    _resourceObservable.notify(
        new AzureResource(
            pSubscriptionId, pDataDiskResourceGroupName, Type.DISK, pDataDiskResourceName),
        ResourceEvent.CREATED);

    return disk;
  }

  public Disk findDisk(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem finding disk";
    final String requestName = "FindDisk";

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeSynchronousApiCall(
            String.format(
                "findDisk - subscriptionId: %s, resourceGroupName: %s, name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () ->
                SupportsGettingByResourceGroupOrNull.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .disks())
                    .getByResourceGroupOrNull(pResourceGroupName, pName));
  }

  public Disk updateDisk(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final AzureDiskType pDiskType,
      final int pDiskSizeGB,
      final Integer pDiskIOPS,
      final Integer pDiskThroughput,
      final String pDiskName,
      final Logger pLogger,
      final boolean pUseSyncApiCall) {
    final String prettyErr = "Problem updating disk size";
    final String requestName = "UpdateDisk";

    final Disk disk = findDisk(pSubscriptionId, pResourceGroupName, pDiskName, pLogger);
    if (disk == null) {
      throw new AzureApiException("disk name", NDSErrorCode.INVALID_ARGUMENT);
    }
    final AzureDiskType realDiskType = AzureNDSDefaults.getRealAzureDiskType(pDiskType);
    final int diskSizeGb = getRealAzureDiskSizeGB(disk, realDiskType, pDiskSizeGB);

    if (pDiskType == AzureDiskType.V2) {
      disk.innerModel()
          .withDiskIopsReadWrite((long) pDiskIOPS)
          .withDiskMBpsReadWrite((long) pDiskThroughput);
    }

    final String operationDesc =
        String.format(
            "updateDisk%s - subscriptionId: %s, resourceGroupName: %s, "
                + "requestedDiskType: %s, realDiskType: %s, diskName: %s, "
                + "diskSizeGB: %s, diskIOPS: %s, diskThroughput: %s",
            pUseSyncApiCall ? "Sync" : "Async",
            pSubscriptionId,
            pResourceGroupName,
            pDiskType,
            realDiskType,
            pDiskName,
            diskSizeGb,
            pDiskIOPS,
            pDiskThroughput);

    var builder =
        getAzureApiCallBuilder(prettyErr, pLogger, requestName)
            .expectedErrorCodes(Set.of(CommonErrorCode.TIMEOUT));
    if (pUseSyncApiCall) {
      // synchronous call is used for disk iops / throughput modification since the rate limited
      // error may be returned after a few seconds. This will be swallowed in the async call as
      // the discovery request will make the api call return immediately.
      // Actual error: com.azure.core.management.exception.ManagementException: Long running
      //  operation is Failed or Cancelled.
      return builder.makeSynchronousApiCall(
          operationDesc,
          disk.update().withSizeInGB(diskSizeGb).withSku(realDiskType.getDiskSkuType())::apply,
          getBeforeHookForDiskRelatedApiCall(requestName, disk.regionName(), realDiskType),
          getAfterHookForDiskRelatedApiCall(requestName, disk.regionName(), realDiskType));
    }
    return builder.makeAsynchronousApiCall(
        operationDesc,
        disk.update().withSizeInGB(diskSizeGb).withSku(realDiskType.getDiskSkuType())::applyAsync,
        () -> findDisk(pSubscriptionId, pResourceGroupName, pDiskName, pLogger),
        getBeforeHookForDiskRelatedApiCall(requestName, disk.regionName(), realDiskType),
        getAfterHookForDiskRelatedApiCall(requestName, disk.regionName(), realDiskType));
  }

  public int getRealAzureDiskSizeGB(
      final Disk pExistingDisk, final AzureDiskType pRealDiskType, final int pDiskSizeGB) {
    if (pRealDiskType != AzureDiskType.V2) {
      return pRealDiskType.getSizeGB();
    }
    final boolean isMigratedFrom4TB =
        pDiskSizeGB >= AzureDiskType.P50.getSizeGB()
            && pDiskSizeGB <= AzureDiskType.P60.getSizeGB()
            && pExistingDisk.sizeInGB() == AzureDiskType.P60.getSizeGB();
    return isMigratedFrom4TB ? AzureDiskType.P60.getSizeGB() : pDiskSizeGB;
  }

  public boolean deleteDiskWithVerification(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final Disk disk = findDisk(pSubscriptionId, pResourceGroupName, pName, pLogger);
    if (disk == null) {
      return true;
    }
    deleteDisk(pSubscriptionId, pResourceGroupName, pName, pLogger);
    return false;
  }

  public void deleteDisk(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    final String prettyError = "Problem deleting disk";
    final String requestName = "DeleteDisk";

    getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .expectedErrorCodes(Set.of(CommonErrorCode.NOT_FOUND, CommonErrorCode.TIMEOUT))
        .makeAsynchronousDeleteCall(
            pName,
            String.format(
                "deleteDisk - subscriptionId: %s, resourceGroupName: %s," + " name: %s",
                pSubscriptionId, pResourceGroupName, pName),
            () ->
                getAzureApiClientSvc()
                    .getCachedAzureResourceManager(pSubscriptionId)
                    .disks()
                    .deleteByResourceGroupAsync(pResourceGroupName, pName),
            () -> findDisk(pSubscriptionId, pResourceGroupName, pName, pLogger),
            disk ->
                AzureProvisioningState.fromValue(disk.innerModel().provisioningState())
                    == AzureProvisioningState.DELETING);
  }

  public Key findKey(
      final String pClientId,
      final String pTenantId,
      final String pSecret,
      final AzureEnvironment pEnvironment,
      final String pResourceGroupName,
      final String pSubscriptionId,
      final String pKeyVaultName,
      final String pKeyIdentifier,
      final Logger pLogger) {
    final String prettyError = "Problem finding key";
    final String requestName = "FindKey";

    try (final NDSAzureResourceManager ndsAzure =
        getAzureApiClientSvc()
            .getAzureResourceManager(
                pClientId, pTenantId, pSecret, pEnvironment, pSubscriptionId)) {
      return getAzureApiCallBuilderForKeyVaultResource(prettyError, pLogger, requestName)
          .makeSynchronousApiCall(
              String.format(
                  "findKey - subscriptionId: %s, resourceGroupName: %s, keyVaultName: %s,"
                      + " keyIdentifier: %s",
                  pSubscriptionId, pResourceGroupName, pKeyVaultName, pKeyIdentifier),
              () -> {
                final Vaults vaults = ndsAzure.getAzureResourceManager().vaults();
                if (vaults == null) {
                  pLogger.warn("Azure Key Vaults not found. subscriptionId: {}", pSubscriptionId);
                  throw new AzureApiException(
                      "Azure Key Vaults not found", CommonErrorCode.NOT_FOUND);
                }

                final Vault vaultByResourceGroup =
                    SupportsGettingByResourceGroupOrNull.wrap(vaults)
                        .getByResourceGroupOrNull(pResourceGroupName, pKeyVaultName);
                if (vaultByResourceGroup == null) {
                  pLogger.warn(
                      "Azure resource group related to key vault not found. subscriptionId: {},"
                          + " resourceGroupName: {}, keyVaultName: {}",
                      pSubscriptionId,
                      pResourceGroupName,
                      pKeyVaultName);
                  throw new AzureApiException(
                      "Azure resource group related to key vault not found",
                      CommonErrorCode.NOT_FOUND);
                }

                final Keys keys = vaultByResourceGroup.keys();
                if (keys == null) {
                  pLogger.warn(
                      "Azure Key Vault keys not found. subscriptionId: {}, resourceGroupName: {},"
                          + " keyVaultName: {}",
                      pSubscriptionId,
                      pResourceGroupName,
                      pKeyVaultName);
                  throw new AzureApiException(
                      "Azure Key Vault keys not found", CommonErrorCode.NOT_FOUND);
                }

                return SupportsGettingByIdOrNull.wrap(keys).getByIdOrNull(pKeyIdentifier);
              });
    } catch (final IOException e) {
      throw new RuntimeException(e);
    }
  }

  public Vault findVault(
      final String pClientId,
      final String pTenantId,
      final String pSecret,
      final AzureEnvironment pEnvironment,
      final String pResourceGroupName,
      final String pSubscriptionId,
      final String pKeyVaultName,
      final Logger pLogger) {
    final String prettyError = "Problem finding vault";
    final String requestName = "FindVault";

    try (final NDSAzureResourceManager ndsAzure =
        getAzureApiClientSvc()
            .getAzureResourceManager(
                pClientId, pTenantId, pSecret, pEnvironment, pSubscriptionId)) {
      return getAzureApiCallBuilderForKeyVaultResource(prettyError, pLogger, requestName)
          .makeSynchronousApiCall(
              String.format(
                  "findVault - subscriptionId: %s, resourceGroupName: %s, keyVaultName: %s,",
                  pSubscriptionId, pResourceGroupName, pKeyVaultName),
              () -> {
                final Vaults vaults = ndsAzure.getAzureResourceManager().vaults();
                if (vaults == null) {
                  throw new AzureApiException("Azure Key Vaults", CommonErrorCode.NOT_FOUND);
                }
                final Vault vaultByResourceGroup =
                    SupportsGettingByResourceGroupOrNull.wrap(vaults)
                        .getByResourceGroupOrNull(pResourceGroupName, pKeyVaultName);
                if (vaultByResourceGroup == null) {
                  throw new AzureApiException(
                      "Azure Key Vault by resource group", CommonErrorCode.NOT_FOUND);
                }
                return vaultByResourceGroup;
              });
    } catch (final IOException e) {
      throw new RuntimeException(e);
    }
  }

  public byte[] encryptDataWithKey(
      final Key pKey,
      final String pClientId,
      final String pSecret,
      final String pTenantId,
      final byte[] pData,
      final Logger pLogger) {
    final String prettyError = "Problem encrypting with key";
    final TokenCredential credentials =
        new ClientSecretCredentialBuilder()
            .clientId(pClientId)
            .clientSecret(pSecret)
            .tenantId(pTenantId)
            .build();
    try {
      final CryptographyAsyncClient cryptoClient =
          new CryptographyClientBuilder()
              .keyIdentifier(pKey.id())
              .credential(credentials)
              .buildAsyncClient();
      return cryptoClient
          .encrypt(ENCRYPTION_ALGORITHM, pData)
          .map(EncryptResult::getCipherText)
          .block();
    } catch (final RuntimeException pE) {
      throw getAzureApiErrorSvc()
          .handleKeyVaultEncryptDecryptRuntimeException(pE, prettyError, pLogger);
    }
  }

  public byte[] encryptStringWithKey(
      final Key pKey,
      final String pClientId,
      final String pSecret,
      final String pTenantId,
      final String pContent,
      final Logger pLogger) {
    final String prettyError = "Problem encrypting with key";
    final TokenCredential credentials =
        new ClientSecretCredentialBuilder()
            .clientId(pClientId)
            .clientSecret(pSecret)
            .tenantId(pTenantId)
            .build();

    try {
      final CryptographyAsyncClient cryptoClient =
          new CryptographyClientBuilder()
              .keyIdentifier(pKey.id())
              .credential(credentials)
              .buildAsyncClient();
      return cryptoClient
          .encrypt(ENCRYPTION_ALGORITHM, pContent.getBytes())
          .map(EncryptResult::getCipherText)
          .block();
    } catch (final RuntimeException pE) {
      throw getAzureApiErrorSvc()
          .handleKeyVaultEncryptDecryptRuntimeException(pE, prettyError, pLogger);
    }
  }

  public byte[] decryptStringWithKey(
      final Key pKey,
      final String pClientId,
      final String pSecret,
      final String pTenantId,
      final byte[] pContent,
      final Logger pLogger) {
    final String prettyError = "Problem decrypting with key";
    final TokenCredential credentials =
        new ClientSecretCredentialBuilder()
            .clientId(pClientId)
            .clientSecret(pSecret)
            .tenantId(pTenantId)
            .build();
    try {
      final CryptographyAsyncClient cryptoClient =
          new CryptographyClientBuilder()
              .keyIdentifier(pKey.id())
              .credential(credentials)
              .buildAsyncClient();
      return cryptoClient
          .decrypt(ENCRYPTION_ALGORITHM, pContent)
          .map(DecryptResult::getPlainText)
          .block();
    } catch (final RuntimeException pE) {
      throw getAzureApiErrorSvc()
          .handleKeyVaultEncryptDecryptRuntimeException(pE, prettyError, pLogger);
    }
  }

  public NetworkInterface createNetworkInterface(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final String pVirtualNetworkName,
      final String pSubnetName,
      final String pNetworkSecurityGroupName,
      final Optional<String> pPublicIPAddressNameOpt,
      final Map<String, String> pTags,
      final boolean pShouldEnableAcceleratedNetworking,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createNetworkInterface(
            pSubscriptionId,
            pAzureRegionName,
            pResourceGroupName,
            pName,
            pVirtualNetworkName,
            pSubnetName,
            pNetworkSecurityGroupName,
            pPublicIPAddressNameOpt,
            pTags,
            pShouldEnableAcceleratedNetworking,
            pLogger);
  }

  public NetworkInterface findNetworkInterface(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findNetworkInterface(pSubscriptionId, pResourceGroupName, pName, pLogger);
  }

  public NetworkInterface findNetworkInterfaceById(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pResourceId,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findNetworkInterfaceById(pSubscriptionId, pResourceGroupName, pResourceId, pLogger);
  }

  public void deleteNetworkInterface(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deleteNetworkInterface(pSubscriptionId, pResourceGroupName, pName, pLogger);
  }

  public PublicIpAddress createPublicIPAddress(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final String pName,
      final Map<String, String> pTags,
      final PublicIPSkuType pSkuType,
      final Logger pLogger) {
    return this.createPublicIPAddress(
        pSubscriptionId,
        pAzureRegionName,
        pResourceGroupName,
        null,
        pName,
        pTags,
        pSkuType,
        pLogger);
  }

  public PublicIpAddress createPublicIPAddress(
      final ObjectId pSubscriptionId,
      final AzureRegionName pAzureRegionName,
      final String pResourceGroupName,
      final AzureAvailabilityZoneName pAzureAvailabilityZoneName,
      final String pName,
      final Map<String, String> pTags,
      final PublicIPSkuType pSkuType,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createPublicIPAddress(
            pSubscriptionId,
            pAzureRegionName,
            pResourceGroupName,
            pAzureAvailabilityZoneName,
            pName,
            pTags,
            pSkuType,
            pLogger);
  }

  public void tagPublicIPAddress(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .tagPublicIPAddress(pSubscriptionId, pResourceGroupName, pName, pTags, pLogger);
  }

  public PublicIpAddress findPublicIPAddress(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findPublicIPAddress(pSubscriptionId, pResourceGroupName, pName, pLogger);
  }

  public void deletePublicIPAddress(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deletePublicIPAddress(pSubscriptionId, pResourceGroupName, pName, pLogger);
  }

  public NetworkInterface attachPublicIPToNetworkInterface(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pNetworkInterfaceName,
      final String pPublicIPName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .attachPublicIPToNetworkInterface(
            pSubscriptionId, pResourceGroupName, pNetworkInterfaceName, pPublicIPName, pLogger);
  }

  public void detachPublicIPFromVirtualMachine(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pNetworkInterfaceName,
      final NetworkInterface pNetworkInterface,
      final PublicIpAddress pAttachedPublicIPAddress,
      final String pTargetPublicIPAddress,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .detachPublicIPFromVirtualMachine(
            pSubscriptionId,
            pResourceGroupName,
            pNetworkInterfaceName,
            pNetworkInterface,
            pAttachedPublicIPAddress,
            pTargetPublicIPAddress,
            pLogger);
  }

  public void disableSubnetPrivateLinkServiceNetworkPolicies(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualNetworkName,
      final String pSubnetName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .disableSubnetPrivateLinkServiceNetworkPolicies(
            pSubscriptionId, pResourceGroupName, pVirtualNetworkName, pSubnetName, pLogger);
  }

  public PagedIterable<MetricDefinition> findMetricDefinitionsForResource(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualNetworkName,
      final String pResourceId,
      final Logger pLogger) {
    final Network network =
        findVirtualNetwork(pSubscriptionId, pResourceGroupName, pVirtualNetworkName, pLogger);

    if (network == null) {
      throw new AzureApiException("virtual network name", NDSErrorCode.INVALID_ARGUMENT);
    }

    final String prettyError = "Problem finding metric definitions for resource.";
    final String requestName = "FindMetricDefinitionsForResource";

    return getAzureApiCallBuilder(prettyError, pLogger, requestName)
        .makeNonIndexableResourceApiCall(
            () ->
                SupportsListingByResourceOrEmpty.wrap(
                        getAzureApiClientSvc()
                            .getCachedAzureResourceManager(pSubscriptionId)
                            .metricDefinitions())
                    .listByResourceOrEmpty(pResourceId));
  }

  public LoadBalancerInner createPublicLoadBalancer(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualNetworkName,
      final String pLoadBalancerName,
      final String pLoadBalancingRuleName,
      final String pPublicIPAddressId,
      final AzureRegionName pAzureRegionName,
      final Map<String, String> pTags,
      final Boolean pEnableFloatingIP,
      final int pBackendInstances,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createPublicLoadBalancer(
            pSubscriptionId,
            pResourceGroupName,
            pVirtualNetworkName,
            pLoadBalancerName,
            pLoadBalancingRuleName,
            pPublicIPAddressId,
            pAzureRegionName,
            pTags,
            pEnableFloatingIP,
            pBackendInstances,
            pLogger);
  }

  public LoadBalancerInner createLoadBalancer(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualNetworkName,
      final String pSubnetName,
      final String pLoadBalancerName,
      final AzureRegionName pAzureRegionName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createLoadBalancer(
            pSubscriptionId,
            pResourceGroupName,
            pVirtualNetworkName,
            pSubnetName,
            pLoadBalancerName,
            pAzureRegionName,
            pTags,
            pLogger);
  }

  public LoadBalancerInner findLoadBalancerInner(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pLoadBalancerName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findLoadBalancerInner(pSubscriptionId, pResourceGroupName, pLoadBalancerName, pLogger);
  }

  public LoadBalancer findLoadBalancer(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pLoadBalancerName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findLoadBalancer(pSubscriptionId, pResourceGroupName, pLoadBalancerName, pLogger);
  }

  public LoadBalancerInner addFrontendIPConfigurationToLoadBalancer(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPublicIPAddressId,
      final String pPrivateLinkServiceName,
      final LoadBalancerInner pLoadBalancer,
      final AzureRegionName pRegionName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .addFrontendIPConfigurationToLoadBalancer(
            pSubscriptionId,
            pResourceGroupName,
            pPublicIPAddressId,
            pPrivateLinkServiceName,
            pLoadBalancer,
            pRegionName,
            pLogger);
  }

  public LoadBalancer removeFrontendIPConfigurationFromLoadBalancer(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateLinkServiceName,
      final LoadBalancer pLoadBalancer,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .removeFrontendIPConfigurationFromLoadBalancer(
            pSubscriptionId, pResourceGroupName, pPrivateLinkServiceName, pLoadBalancer, pLogger);
  }

  public void deleteLoadBalancer(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pLoadBalancerName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deleteLoadBalancer(pSubscriptionId, pResourceGroupName, pLoadBalancerName, pLogger);
  }

  public PrivateLinkServiceInner createPrivateLinkService(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualNetworkName,
      final String pSubnetName,
      final String pPrivateLinkServiceName,
      final AzureRegionName pAzureRegionName,
      final LoadBalancerInner pLoadBalancer,
      final Boolean pEnableProxyProtocol,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createPrivateLinkService(
            pSubscriptionId,
            pResourceGroupName,
            pVirtualNetworkName,
            pSubnetName,
            pPrivateLinkServiceName,
            pAzureRegionName,
            pLoadBalancer,
            pEnableProxyProtocol,
            pTags,
            pLogger);
  }

  public PrivateLinkServiceInner findPrivateLinkService(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateLinkServiceName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findPrivateLinkService(
            pSubscriptionId, pResourceGroupName, pPrivateLinkServiceName, pLogger);
  }

  public void deletePrivateLinkService(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateLinkServiceName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deletePrivateLinkService(
            pSubscriptionId, pResourceGroupName, pPrivateLinkServiceName, pLogger);
  }

  public void deletePrivateEndpointConnection(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateLinkServiceName,
      final String pPrivateEndpointConnectionName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deletePrivateEndpointConnection(
            pSubscriptionId,
            pResourceGroupName,
            pPrivateLinkServiceName,
            pPrivateEndpointConnectionName,
            pLogger);
  }

  public PrivateEndpointConnectionInner acceptPrivateEndpoint(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateLinkServiceName,
      final String pPrivateEndpointName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .acceptPrivateEndpoint(
            pSubscriptionId,
            pResourceGroupName,
            pPrivateLinkServiceName,
            pPrivateEndpointName,
            pLogger);
  }

  public InboundNatRuleInner findInboundNATRule(
      final LoadBalancer pLoadBalancer, final String pInboundNATRuleName) {
    return getAzureApiNetworkSvc().findInboundNATRule(pLoadBalancer, pInboundNATRuleName);
  }

  public LoadBalancer addInboundNATRule(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pLoadBalancerName,
      final String pFrontendName,
      final String pInboundNATRuleName,
      final int pFrontendPort,
      final int pBackendPort,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .addInboundNATRule(
            pSubscriptionId,
            pResourceGroupName,
            pLoadBalancerName,
            pFrontendName,
            pInboundNATRuleName,
            pFrontendPort,
            pBackendPort,
            pLogger);
  }

  public LoadBalancer deleteInboundNATRule(
      final LoadBalancer pLoadBalancer, final String pInboundNATRuleName, final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .deleteInboundNATRule(pLoadBalancer, pInboundNATRuleName, pLogger);
  }

  public NetworkInterface removeInboundNATRulesFromVM(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualMachineName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .removeInboundNATRulesFromVM(
            pSubscriptionId,
            pResourceGroupName,
            findVirtualMachine(pSubscriptionId, pResourceGroupName, pVirtualMachineName, pLogger),
            pLogger);
  }

  public NetworkInterface configureBackendTargetForInboundNATRule(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pLoadBalancerName,
      final String pVirtualMachineName,
      final String pInboundNATRuleName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .configureBackendTargetForInboundNATRule(
            pSubscriptionId,
            pResourceGroupName,
            pLoadBalancerName,
            findVirtualMachine(pSubscriptionId, pResourceGroupName, pVirtualMachineName, pLogger),
            pInboundNATRuleName,
            pLogger);
  }

  public LoadBalancer addVirtualMachineToLoadBalancerBackendPool(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pLoadBalancerName,
      final String pVirtualMachineName,
      final String pBackendName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .addVirtualMachineToLoadBalancerBackendPool(
            pSubscriptionId,
            pResourceGroupName,
            pLoadBalancerName,
            findVirtualMachine(pSubscriptionId, pResourceGroupName, pVirtualMachineName, pLogger),
            pBackendName,
            pLogger);
  }

  @VisibleForTesting
  protected Set<String> addInstanceResourceIds(final VirtualMachine pVirtualMachine) {
    final String virtualMachineId = pVirtualMachine.id();
    final String osDiskId = pVirtualMachine.osDiskId();
    final Set<String> resourceIds = new HashSet<>();
    resourceIds.add(virtualMachineId);
    resourceIds.add(osDiskId);

    // if there are no data disks, that indicates this is a data bearing machine for an NVMe cluster
    if (pVirtualMachine.dataDisks().size() > 0) {
      pVirtualMachine.dataDisks().values().stream()
          .map(disk -> disk.id())
          .forEach(id -> resourceIds.add(id));
    }

    return resourceIds;
  }

  public List<NDSCloudProviderEventView> getActivityLogsForInstance(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final AzureInstanceHardware pInstanceHardware,
      final Date pStartTime,
      final Date pEndTime,
      final Logger pLogger) {
    final AzureResourceManager azureResourceManager =
        getAzureApiClientSvc().getCachedAzureResourceManager(pSubscriptionId);

    final VirtualMachine virtualMachine =
        findVirtualMachine(
            pSubscriptionId,
            pResourceGroupName,
            pInstanceHardware.getAzureInstanceName().get(),
            pLogger);

    if (virtualMachine == null) {
      throw new AzureApiException("virtual machine not found", AzureErrorCode.VM_NOT_FOUND);
    }

    final Set<String> instanceResourceIds = addInstanceResourceIds(virtualMachine);

    final OffsetDateTime startingFrom = pStartTime.toInstant().atOffset(ZoneOffset.UTC);
    final OffsetDateTime endsBefore = pEndTime.toInstant().atOffset(ZoneOffset.UTC);

    try {
      final PagedIterable<EventData> activityLogsForResourceGroup =
          azureResourceManager
              .activityLogs()
              .defineQuery()
              .startingFrom(startingFrom)
              .endsBefore(endsBefore)
              .withAllPropertiesInResponse()
              .filterByResourceGroup(pResourceGroupName)
              .execute();
      return activityLogsForResourceGroup.stream()
          .filter(activityLog -> instanceResourceIds.contains(activityLog.resourceId()))
          .map(NDSAzureActivityLogEntryView::new)
          .collect(Collectors.toList());
    } catch (final RuntimeException rE) {
      throw getAzureApiErrorSvc()
          .handleApiRuntimeException(
              rE,
              "Problem getting activity logs for instance",
              Set.of(NDSErrorCode.INVALID_START_DATE),
              pLogger)
          .getLeft();
    }
  }

  public Map<String, Map<NDSCloudProviderConsoleMetricDefinition, List<NDST2CSVRow>>>
      getMetricsAsCSVForInstance(
          final ObjectId pSubscriptionId,
          final String pResourceGroupName,
          final String pVirtualMachineName,
          final Date pStartTime,
          final Date pEndTime,
          final Set<NDSCloudProviderConsoleMetricDefinition>
              pNDSCloudProviderConsoleMetricDefinitions,
          final Logger pLogger) {
    final AzureResourceManager azureResourceManager =
        getAzureApiClientSvc().getCachedAzureResourceManager(pSubscriptionId);

    final VirtualMachine virtualMachine =
        findVirtualMachine(pSubscriptionId, pResourceGroupName, pVirtualMachineName, pLogger);

    if (virtualMachine == null) {
      throw new AzureApiException("virtual machine not found", AzureErrorCode.VM_NOT_FOUND);
    }

    final String virtualMachineId = virtualMachine.id();
    final String osDiskId = virtualMachine.osDiskId();
    final List<String> dataDiskIds =
        virtualMachine.dataDisks().values().stream()
            .map(val -> val.id())
            .collect(Collectors.toList());
    final Set<String> resourceIds = new HashSet<>();
    resourceIds.add(virtualMachineId);
    resourceIds.add(osDiskId);
    resourceIds.addAll(dataDiskIds);

    final DateTime startingFrom = new DateTime(pStartTime.getTime());
    final DateTime endsBefore = new DateTime(pEndTime.getTime());

    final Set<String> metricDefinitionNames =
        pNDSCloudProviderConsoleMetricDefinitions.stream()
            .map(NDSCloudProviderConsoleMetricDefinition::getMetric)
            .collect(Collectors.toSet());

    return resourceIds.stream()
        .collect(
            Collectors.toMap(
                Function.identity(),
                resourceId ->
                    getMetricsForResourceId(
                        azureResourceManager,
                        resourceId,
                        metricDefinitionNames,
                        startingFrom,
                        endsBefore)));
  }

  private Map<NDSCloudProviderConsoleMetricDefinition, List<NDST2CSVRow>> getMetricsForResourceId(
      final AzureResourceManager pAzureResourceManager,
      final String pResourceId,
      final Set<String> pMetricDefinitionNames,
      final DateTime pStartingFrom,
      final DateTime pEndsBefore)
      throws AzureApiException {
    final Map<NDSCloudProviderConsoleMetricDefinition, List<NDST2CSVRow>> metrics = new HashMap<>();

    final PagedIterable<MetricDefinition> metricDefinitionsForResource =
        SupportsListingByResourceOrEmpty.wrap(pAzureResourceManager.metricDefinitions())
            .listByResourceOrEmpty(pResourceId);

    metricDefinitionsForResource.stream()
        .filter(md -> pMetricDefinitionNames.contains(md.name().value()))
        .forEach(
            metricDefinition -> {
              final NDSAzureMetricDefinition ndsMetricDefinition =
                  NDSAzureMetricDefinition.findByMetric(metricDefinition.name().value())
                      .orElse(null);
              if (ndsMetricDefinition == null) {
                return;
              }
              final MetricCollection metricCollection =
                  metricDefinition
                      .defineQuery()
                      .startingFrom(pStartingFrom.toDate().toInstant().atOffset(ZoneOffset.UTC))
                      .endsBefore(pEndsBefore.toDate().toInstant().atOffset(ZoneOffset.UTC))
                      .withAggregation(ndsMetricDefinition.getStatistic())
                      .withInterval(Duration.ofMinutes(5))
                      .execute();
              final List<NDST2CSVRow> csvValues =
                  metricCollection.metrics().stream()
                      .flatMap(
                          metric ->
                              Stream.concat(
                                  Stream.of(NDST2CSVRow.headerRow(ndsMetricDefinition)),
                                  metric.timeseries().stream()
                                      .flatMap(timeseries -> timeseries.data().stream())
                                      .map(
                                          metricValue ->
                                              new NDST2CSVRow(
                                                  String.valueOf(
                                                      metricValue
                                                              .timestamp()
                                                              .toInstant()
                                                              .toEpochMilli()
                                                          / 1000L),
                                                  metricValue.maximum() == null
                                                      ? null
                                                      : String.valueOf(metricValue.maximum())))
                                      .sorted(Comparator.comparing(NDST2CSVRow::getTime))))
                      .toList();
              metrics.put(ndsMetricDefinition, csvValues);
            });

    return metrics;
  }

  public List<Metric> getAzureLoadBalancerMetrics(
      final ObjectId pAzureSubscriptionId,
      final String pResourceGroupName,
      final String pLoadBalancerName,
      final Map<String, String> pMetricAndStatMap,
      final Date pStartTime,
      final Date pEndTime,
      final Logger pLogger)
      throws AzureApiException {
    final AzureResourceManager azureResourceManager =
        getAzureApiClientSvc().getCachedAzureResourceManager(pAzureSubscriptionId);
    final LoadBalancer loadBalancer =
        findLoadBalancer(pAzureSubscriptionId, pResourceGroupName, pLoadBalancerName, pLogger);
    if (loadBalancer == null) {
      throw new AzureApiException("Load Balancer not found", AzureErrorCode.LB_NOT_FOUND);
    }
    final DateTime startingFrom = new DateTime(pStartTime.getTime());
    final DateTime endsBefore = new DateTime(pEndTime.getTime());
    final PagedIterable<MetricDefinition> metricDefinitionsForResource =
        SupportsListingByResourceOrEmpty.wrap(azureResourceManager.metricDefinitions())
            .listByResourceOrEmpty(loadBalancer.id());
    final List<Metric> metrics = new ArrayList<>();

    metricDefinitionsForResource.stream()
        .filter(md -> pMetricAndStatMap.containsKey(md.name().value()))
        .forEach(
            metricDefinition -> {
              final Optional<NDSAzureMetricDefinition> ndsMetricDefinition =
                  NDSAzureMetricDefinition.findByMetric(metricDefinition.name().value());
              if (ndsMetricDefinition.isEmpty()) {
                pLogger.debug("Metric ({}) not found", metricDefinition.name().value());
                return;
              }
              final WithMetricsQueryExecute metricQuery =
                  metricDefinition
                      .defineQuery()
                      .startingFrom(startingFrom.toDate().toInstant().atOffset(ZoneOffset.UTC))
                      .endsBefore(endsBefore.toDate().toInstant().atOffset(ZoneOffset.UTC))
                      .withAggregation(pMetricAndStatMap.get(ndsMetricDefinition.get().getMetric()))
                      .withInterval(Duration.ofMinutes(1));
              if (ndsMetricDefinition
                  .get()
                  .getMetric()
                  .equals(NDSAzureMetricDefinition.SNAT_CONNECTION_COUNT.getMetric())) {
                final String stateFilter =
                    "ConnectionState eq 'Successful' or ConnectionState eq 'Failed'";
                metricQuery.withOdataFilter(stateFilter);
              }
              final MetricCollection metricCollection = metricQuery.execute();
              metrics.addAll(metricCollection.metrics());
            });

    return metrics;
  }

  public PrivateEndpointInner createPrivateEndpointForKeyVaultAccess(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualNetworkName,
      final String pSubnetName,
      final String pEndpointName,
      final String pKeyVaultId,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createPrivateEndpointForKeyVaultAccess(
            pSubscriptionId,
            pResourceGroupName,
            pVirtualNetworkName,
            pSubnetName,
            pEndpointName,
            pKeyVaultId,
            pTags,
            pLogger);
  }

  public PrivateEndpointInner createPrivateEndpoint(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualNetworkName,
      final String pSubnetName,
      final String pEndpointName,
      final String privateLinkServiceId,
      final List<String> pGroupIds,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createPrivateEndpoint(
            pSubscriptionId,
            pResourceGroupName,
            pVirtualNetworkName,
            pSubnetName,
            pEndpointName,
            privateLinkServiceId,
            pGroupIds,
            pTags,
            pLogger);
  }

  public String getIpAddressForPrivateEndpoint(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pEndpointName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .getIpAddressForPrivateEndpoint(
            pSubscriptionId, pResourceGroupName, pEndpointName, pLogger);
  }

  public PrivateEndpointInner findPrivateEndpoint(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pEndpointName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findPrivateEndpoint(pSubscriptionId, pResourceGroupName, pEndpointName, pLogger);
  }

  public void deletePrivateEndpoint(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pEndpointName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deletePrivateEndpoint(pSubscriptionId, pResourceGroupName, pEndpointName, pLogger);
  }

  public PrivateZoneInner createPrivateDnsZone(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateZoneName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createPrivateDnsZone(
            pSubscriptionId, pResourceGroupName, pPrivateZoneName, pTags, pLogger);
  }

  public PrivateZoneInner findPrivateDnsZone(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateZoneName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findPrivateDnsZone(pSubscriptionId, pResourceGroupName, pPrivateZoneName, pLogger);
  }

  public void deletePrivateDnsZone(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateZoneName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deletePrivateDnsZone(pSubscriptionId, pResourceGroupName, pPrivateZoneName, pLogger);
  }

  public VirtualNetworkLinkInner createVirtualNetworkLink(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pVirtualNetworkName,
      final String pPrivateZoneName,
      final String pVirtualNetworkLinkName,
      final Map<String, String> pTags,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createVirtualNetworkLink(
            pSubscriptionId,
            pResourceGroupName,
            pVirtualNetworkName,
            pPrivateZoneName,
            pVirtualNetworkLinkName,
            pTags,
            pLogger);
  }

  public VirtualNetworkLinkInner findVirtualNetworkLink(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateZoneName,
      final String pVirtualNetworkLinkName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findVirtualNetworkLink(
            pSubscriptionId,
            pResourceGroupName,
            pPrivateZoneName,
            pVirtualNetworkLinkName,
            pLogger);
  }

  public void deleteVirtualNetworkLink(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateZoneName,
      final String pVirtualNetworkLinkName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deleteVirtualNetworkLink(
            pSubscriptionId,
            pResourceGroupName,
            pPrivateZoneName,
            pVirtualNetworkLinkName,
            pLogger);
  }

  public PrivateDnsZoneGroupInner createPrivateDnsZoneGroupForPrivateEndpoint(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateEndpointName,
      final String pPrivateZoneName,
      final String pPrivateDnsZoneGroupName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .createPrivateDnsZoneGroupForPrivateEndpoint(
            pSubscriptionId,
            pResourceGroupName,
            pPrivateEndpointName,
            pPrivateZoneName,
            pPrivateDnsZoneGroupName,
            pLogger);
  }

  public PrivateDnsZoneGroupInner findPrivateDnsZoneGroupForPrivateEndpoint(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateEndpointName,
      final String pPrivateDnsZoneGroupName,
      final Logger pLogger) {
    return getAzureApiNetworkSvc()
        .findPrivateDnsZoneGroupForPrivateEndpoint(
            pSubscriptionId,
            pResourceGroupName,
            pPrivateEndpointName,
            pPrivateDnsZoneGroupName,
            pLogger);
  }

  public void deletePrivateDnsZoneGroup(
      final ObjectId pSubscriptionId,
      final String pResourceGroupName,
      final String pPrivateEndpointName,
      final String pPrivateDnsZoneGroupName,
      final Logger pLogger) {
    getAzureApiNetworkSvc()
        .deletePrivateDnsZoneGroup(
            pSubscriptionId,
            pResourceGroupName,
            pPrivateEndpointName,
            pPrivateDnsZoneGroupName,
            pLogger);
  }

  @VisibleForTesting
  public void approveKeyVaultPrivateEndpointForE2ETest(
      final String pClientId,
      final String pTenantId,
      final String pSecret,
      final AzureEnvironment pEnvironment,
      final String pResourceGroupName,
      final String pSubscriptionId,
      final String pKeyVaultName,
      final String pEndpointConnectionName,
      final Logger pLogger) {
    final Vault vault =
        findVault(
            pClientId,
            pTenantId,
            pSecret,
            pEnvironment,
            pResourceGroupName,
            pSubscriptionId,
            pKeyVaultName,
            pLogger);
    vault.approvePrivateEndpointConnection(pEndpointConnectionName);
  }

  @VisibleForTesting
  protected AzureApiCallBuilder getAzureApiCallBuilder(
      final String pPrettyError, final Logger pLogger, final String pRequestName) {
    return new AzureApiCallBuilder(
        getAzureApiErrorSvc(), pPrettyError, pLogger, pRequestName, false);
  }

  @VisibleForTesting
  protected AzureApiCallBuilder getAzureApiCallBuilderForKeyVaultResource(
      final String pPrettyError, final Logger pLogger, final String pRequestName) {
    return new AzureApiCallBuilder(
        getAzureApiErrorSvc(), pPrettyError, pLogger, pRequestName, true);
  }

  /**
   * Returns the export history for a given subscription.
   *
   * @param id the document ID
   * @param subscriptionId the Azure subscription ID
   * @return a JSONObject containing the export history
   * @throws IOException if there is an error communicating with the Azure API
   */
  @VisibleForTesting
  protected JSONObject costManagementGetExecutionHistory(
      final ObjectId id, final String subscriptionId) throws IOException {
    final String link = getCostManagementExportRunHistoryLink(subscriptionId, BILLING_EXPORT_NAME);

    return doAuthedGetJSONObject(id, link);
  }

  /**
   * Returns the export history for a given subscription.
   *
   * @param subscription the Azure subscription
   * @return a list of report executions
   */
  public List<AzureReportExecution> costManagementGetExecutionHistory(
      final AzureSubscription subscription) throws IOException {

    final JSONObject exportHistory =
        costManagementGetExecutionHistory(subscription.getId(), subscription.getSubscriptionId());
    final JSONArray exports = exportHistory.getJSONArray("value");

    return IntStream.range(0, exports.length())
        .mapToObj(exports::getJSONObject)
        .map(jsonObject -> jsonObject.getJSONObject("properties"))
        .map(
            jsonObject -> {
              AzureReportExecution.Status status;
              try {
                status = AzureReportExecution.Status.valueOf(jsonObject.getString("status"));
              } catch (final Exception e) {
                // If the status is not recognized, default to Failed
                status = AzureReportExecution.Status.Failed;
              }

              final LocalDate startDate =
                  LocalDateTime.parse(
                          jsonObject.getString("startDate"), ISO_DATE_TIME_WITH_OPTIONAL_OFFSET)
                      .toLocalDate();
              final LocalDate endDate =
                  LocalDateTime.parse(
                          jsonObject.getString("endDate"), ISO_DATE_TIME_WITH_OPTIONAL_OFFSET)
                      .toLocalDate();
              final LocalDateTime submittedTime =
                  ZonedDateTime.parse(jsonObject.getString("submittedTime"), ISO_ZONED_DATE_TIME)
                      .toLocalDateTime();

              return new AzureReportExecution(status, startDate, endDate, submittedTime);
            })
        .toList();
  }

  protected void incrementCounter(final Counter pCounter, final String... pLabelValues) {
    NDSPromMetricsSvc.incrementCounter(pCounter, pLabelValues);
  }

  /**
   * Validates all subscription credentials if necessary based on timing and configuration changes.
   */
  private void validateAllSubscriptionCredentialsIfNecessary() {
    try {
      if (_credentialsManager.shouldValidateCredentials()) {
        validateAllSubscriptionCredentials();
      }
    } catch (final Exception pE) {
      LOG.warn("{}: credentials validation failed", CREDENTIALS_VALIDATION_REPORT_TITLE, pE);
    }
  }

  /**
   * Validates credentials for all enabled Azure subscriptions.
   *
   * @return List of subscription-exception pairs for any validation failures
   */
  @VisibleForTesting
  protected List<Pair<AzureSubscription, Exception>> validateAllSubscriptionCredentials() {
    try {
      // validate credentials only for subscriptions with regions and assignmentEnabled
      final List<AzureSubscription> subscriptions =
          _subscriptionDao.findAllSubscriptions().stream()
              .filter(AzureSubscription::getAssignmentEnabled)
              .filter(a -> !a.getRegions().isEmpty())
              .sorted(Comparator.comparing(AzureSubscription::getName))
              .toList();
      final List<Pair<AzureSubscription, Exception>> errors =
          subscriptions.stream()
              .map(
                  subscription -> {
                    try {
                      // do a trial API request
                      getCurrentSubscription(subscription.getId(), LOG);
                      return null;
                    } catch (final Exception pE) {
                      LOG.warn(
                          "{}: Failed to validate credentials. azureSubscription={}",
                          CREDENTIALS_VALIDATION_REPORT_TITLE,
                          subscription.getName(),
                          pE);
                      return Pair.of(subscription, pE);
                    }
                  })
              .filter(Objects::nonNull)
              .toList();
      if (errors.isEmpty()) {
        LOG.info(
            "{}: Successfully validated credentials for {} subscriptions",
            CREDENTIALS_VALIDATION_REPORT_TITLE,
            subscriptions.size());
      } else {
        LOG.error(
            "{}: Invalid credentials detected:\n{}",
            CREDENTIALS_VALIDATION_REPORT_TITLE,
            errors.stream()
                .map(p -> String.format("%s: %s", p.getLeft().getName(), p.getRight().getMessage()))
                .collect(Collectors.joining("\n")));
      }
      return errors;
    } catch (final Exception pE) {
      LOG.warn("{}: credentials validation failed", CREDENTIALS_VALIDATION_REPORT_TITLE, pE);
    }
    return null;
  }

  public enum PrivateEndpointAcceptanceStatus {
    APPROVED("Approved"),
    REJECTED("Rejected"),
    PENDING("Pending"),
    DISCONNECTED("Disconnected");
    private final String _status;

    PrivateEndpointAcceptanceStatus(final String pStatus) {
      _status = pStatus;
    }

    public String getStatus() {
      return _status;
    }

    public String toString() {
      return getStatus();
    }

    public static PrivateEndpointAcceptanceStatus find(final String pStatus) {
      return Arrays.stream(PrivateEndpointAcceptanceStatus.values())
          .filter(s -> s.getStatus().equalsIgnoreCase(pStatus))
          .findFirst()
          .orElseThrow(
              () ->
                  new IllegalArgumentException(
                      "unrecognized azure private endpoint connection acceptance status:"
                          + pStatus));
    }
  }
}
