package com.xgen.cloud.nds.activity._public.event.audit;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mongodb.DBObject;
import com.xgen.cloud.activity._public.model.audit.BaseAudit;
import com.xgen.cloud.activity._public.model.event.EventCategory;
import com.xgen.cloud.activity._public.model.event.EventScope;
import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.activity._public.model.event.EventTypeCategory;
import com.xgen.cloud.activity._public.model.event.EventTypeInfo;
import com.xgen.cloud.common.eventseverity._public.model.EventSeverity;
import com.xgen.svc.mms.model.grouptype.GroupType;
import dev.morphia.annotations.Property;
import org.bson.types.ObjectId;

public class ADFAAdminAudit extends BaseAudit {
  public static final String TYPE_NAME = "ADFA_ADMIN_AUDIT";

  @JsonProperty(EVENT_TYPE_FIELD)
  @Property(EVENT_TYPE_FIELD)
  private ADFAAdminAudit.Type _eventType;

  @JsonProperty(FieldDefs.TENANT_ID)
  @Property(FieldDefs.TENANT_ID)
  private ObjectId _tenantId;

  @JsonProperty(FieldDefs.DATA_FIELD)
  @Property(FieldDefs.DATA_FIELD)
  private DBObject _data;

  public static class FieldDefs {
    public static final String TENANT_ID = "tenantId";
    public static final String DATA_FIELD = "data";
  }

  public ADFAAdminAudit() {
    super(TYPE_NAME);
  }

  protected ADFAAdminAudit(Builder pBuilder) {
    super(pBuilder);
    _eventType = pBuilder.eventType;
    _tenantId = pBuilder.tenantId;
    _data = pBuilder.data;
  }

  @Override
  public ADFAAdminAudit.Type getEventType() {
    return _eventType;
  }

  public ObjectId getTenantId() {
    return _tenantId;
  }

  public DBObject getData() {
    return _data;
  }

  public enum Type implements EventType {
    ADFA_LIST_DATA_SETS_ACTION_COMPLETED(
        info().description("ADFA admin performed list data sets action")),
    ADFA_LIST_ANDON_CORDS_ACTION_COMPLETED(
        info().description("ADFA admin performed list andon cords action")),
    ADFA_CREATE_OR_UPDATE_ANDON_CORD_ACTION_COMPLETED(
        info().description("ADFA admin performed create or update andon cord action")),
    ADFA_TENANT_GET_SETTINGS_ACTION_COMPLETED(
        info().description("ADFA admin performed tenant get settings action")),
    ADFA_TENANT_SET_SETTINGS_ACTION_COMPLETED(
        info().description("ADFA admin performed tenant set settings action")),
    ADFA_TENANT_GET_STORAGE_CONFIG_ACTION_COMPLETED(
        info().description("ADFA admin performed tenant get storage config action")),
    ADFA_TENANT_DELETE_STORAGE_CONFIG_ACTION_COMPLETED(
        info().description("ADFA admin performed tenant delete storage config action")),
    ADFA_TENANT_GET_CONFIG_ACTION_COMPLETED(
        info().description("ADFA admin performed tenant get config action")),
    ADFA_LIST_CURRENT_OPS_ACTION_COMPLETED(
        info().description("ADFA admin performed list current ops action"));

    private final EventTypeInfo _info;

    Type(final EventTypeInfo.Builder pBuilder) {
      _info = pBuilder.build();
    }

    public EventTypeInfo getInfo() {
      return _info;
    }

    private static EventTypeInfo.Builder info() {
      return new EventTypeInfo.Builder()
          .category(EventCategory.NDS)
          .typeCategory(EventTypeCategory.AUDIT)
          .severity(EventSeverity.INFO)
          .groupTypes(GroupType.NDS)
          .scopes(EventScope.GLOBAL);
    }
  }

  @Override
  public Builder copier() {
    return new Builder(this);
  }

  public static Builder newBuilder() {
    return new Builder();
  }

  public static class Builder extends BaseAudit.Builder {
    private ADFAAdminAudit.Type eventType;
    private ObjectId tenantId;
    private DBObject data;

    private Builder(final String pTypeName, final ADFAAdminAudit.Type pEventType) {
      super(pTypeName);
      eventType = pEventType;
    }

    public Builder(final ADFAAdminAudit.Type pEventType) {
      this(TYPE_NAME, pEventType);
    }

    public Builder(final ADFAAdminAudit pCopy) {
      super(pCopy);
      this.eventType = pCopy.getEventType();
      this.tenantId = pCopy.getTenantId();
      this.data = pCopy.getData();
    }

    public Builder() {
      super(TYPE_NAME);
    }

    @Override
    public Builder eventType(final EventType eventType) {
      this.eventType = (Type) eventType;
      return this;
    }

    public Builder tenantId(final ObjectId pTenantId) {
      this.tenantId = pTenantId;
      return this;
    }

    public Builder data(final DBObject pData) {
      this.data = pData;
      return this;
    }

    public ADFAAdminAudit build() {
      // ADFA admin audit events are always hidden.
      this.hidden(true);
      return new ADFAAdminAudit(this);
    }
  }
}
