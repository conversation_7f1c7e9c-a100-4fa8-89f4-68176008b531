package com.xgen.cloud.nds.mongotune.policies.writeblock._public.svc;

import com.xgen.cloud.nds.common._public.model.INDSDefaults;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyArgVersion;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyArgs;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicySerializationContext;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyArgVersion;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockV1DefaultArgs;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockV1DefaultArgs.WriteBlockThreshold;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockV1DefaultArgs.WriteBlockThresholdType;
import jakarta.inject.Singleton;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * Constructs a map from {@link DiskWriteBlockPolicyArgVersion} to default policy arguments for the
 * Disk Write Block policy. All arg versions must be registered below (verified by unit test).
 */
@Singleton
public class DiskWriteBlockDefaultArgGeneratorSvc {
  private static final double BYTES_IN_MB = 1024 * 1024;
  private static final double BYTES_IN_GB = 1024 * BYTES_IN_MB;

  private final Map<PolicyArgVersion, PolicyArgs<? extends PolicySerializationContext>>
      diskWriteBlockDefaultArgs;

  public DiskWriteBlockDefaultArgGeneratorSvc() {
    // initialization of a map from argument version to default policy arguments.
    diskWriteBlockDefaultArgs = new HashMap<>();
    populateMap();
  }

  public Map<PolicyArgVersion, PolicyArgs<? extends PolicySerializationContext>>
      getDiskWriteBlockDefaultArgs() {
    return diskWriteBlockDefaultArgs;
  }

  private void populateMap() {
    Arrays.stream(DiskWriteBlockPolicyArgVersion.values())
        .forEach(
            version -> {
              final PolicyArgs<? extends PolicySerializationContext> args =
                  generatePolicyArgs(version);
              diskWriteBlockDefaultArgs.put(version, args);
            });
  }

  private PolicyArgs<? extends PolicySerializationContext> generatePolicyArgs(
      final DiskWriteBlockPolicyArgVersion version) {
    return switch (version) {
      case V1 -> generateV1Args();
      default ->
          throw new IllegalArgumentException(
              "Unsupported Disk Write Block policy arg version: " + version.getVersion());
    };
  }

  private DiskWriteBlockV1DefaultArgs generateV1Args() {
    final Integer v1_tickInterval = 100;
    final Integer v1_slowTickThreshold = 500;

    final Function<Double, WriteBlockThreshold> blockThresholdCalc =
        (diskSizeGb) -> {
          if (diskSizeGb >= 1250.0) {
            return new WriteBlockThreshold(50.0 * BYTES_IN_GB, WriteBlockThresholdType.ABSOLUTE);
          } else if (diskSizeGb >= 20.0) {
            return new WriteBlockThreshold(1 - 0.04, WriteBlockThresholdType.RELATIVE);
          } else {
            return new WriteBlockThreshold(600 * BYTES_IN_MB, WriteBlockThresholdType.ABSOLUTE);
          }
        };

    // Unblock thresholds are calculated based on the block thresholds, but with an additional
    // buffer of free space to ensure that the disk is not immediately blocked again after
    // unblocking.
    final double additionalFreeSpaceForUnblockAsPercentage = 1.5;
    final Function<Double, WriteBlockThreshold> unblockThresholdCalc =
        (diskSizeGb) -> {
          if (diskSizeGb >= 1250.0) {
            return new WriteBlockThreshold(
                50.0 * BYTES_IN_GB * additionalFreeSpaceForUnblockAsPercentage,
                WriteBlockThresholdType.ABSOLUTE);
          } else if (diskSizeGb >= 20.0) {
            return new WriteBlockThreshold(
                1 - (0.04 * additionalFreeSpaceForUnblockAsPercentage),
                WriteBlockThresholdType.RELATIVE);
          } else {
            return new WriteBlockThreshold(
                600 * BYTES_IN_MB * additionalFreeSpaceForUnblockAsPercentage,
                WriteBlockThresholdType.ABSOLUTE);
          }
        };

    return new DiskWriteBlockV1DefaultArgs(
        blockThresholdCalc,
        unblockThresholdCalc,
        INDSDefaults.DATA_MOUNT,
        v1_tickInterval,
        v1_slowTickThreshold,
        false /* dryRun */);
  }
}
