package com.xgen.cloud.nds.dataexfiltrationprevention._private.dao;

import com.mongodb.BasicDBObject;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import com.mongodb.client.model.ReturnDocument;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.db.mongo._public.index.MongoIndex;
import com.xgen.cloud.common.metrics._public.annotations.PromMethodMetrics;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.DaoOpType;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.DaoOperation;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.dataexfiltrationprevention._private.model.AWSDataExfiltrationPrefixList;
import com.xgen.cloud.nds.dataexfiltrationprevention._private.model.AWSDataExfiltrationPrefixList.FieldDefs;
import com.xgen.cloud.nds.dataexfiltrationprevention._private.model.PrefixListEntry;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.types.ObjectId;
import software.amazon.awssdk.services.ec2.model.ManagedPrefixList;

@Singleton
public class AWSDataExfiltrationPrefixListDao extends BaseDao<AWSDataExfiltrationPrefixList> {

  public static final String DATABASE_NAME = "nds";
  public static final String COLLECTION_NAME =
      "config.nds.dataExfiltrationPrevention.awsPrefixLists";
  public static final String METHOD_CALL_DURATION =
      "mms_nds_data_exfil_prevention_prefix_list_dao_method_invocation_duration_seconds";
  public static final String METHOD_CALL_DURATION_DESCRIPTION =
      "run time for AWSDataExfiltrationPrefixListDao method";

  @Inject
  public AWSDataExfiltrationPrefixListDao(
      MongoClientContainer container, CodecRegistry codecRegistry) {
    super(container, DATABASE_NAME, COLLECTION_NAME, codecRegistry);
  }

  @Override
  public List<MongoIndex> getIndexes() {
    return List.of(
        MongoIndex.builder().key(FieldDefs.AWS_ACCOUNT_ID).key(FieldDefs.REGION).unique().build());
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<AWSDataExfiltrationPrefixList> findByAWSAccountIdAndRegion(
      ObjectId awsAccountId, AWSRegionName region) {
    return findOne(
        new BasicDBObject()
            .append(FieldDefs.AWS_ACCOUNT_ID, awsAccountId)
            .append(FieldDefs.REGION, region));
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<AWSDataExfiltrationPrefixList> updatePrefixListAndEntriesFromAWS(
      ObjectId existingPrefixListId,
      ManagedPrefixList newPrefixList,
      Set<PrefixListEntry> entries) {
    // Only persist update if new version is greater than or equal to the current version
    final BasicDBObject query =
        new BasicDBObject()
            .append(FieldDefs.ID, existingPrefixListId)
            .append(FieldDefs.VERSION, new BasicDBObject(BaseDao.LTE, newPrefixList.version()));
    final BasicDBObject update =
        new BasicDBObject()
            .append(
                BaseDao.SET,
                new BasicDBObject()
                    .append(FieldDefs.VERSION, newPrefixList.version())
                    .append(FieldDefs.STATE, newPrefixList.state())
                    .append(FieldDefs.LAST_MODIFIED_DATE, new Date())
                    .append(FieldDefs.NAME, newPrefixList.prefixListName())
                    .append(FieldDefs.MAX_ENTRIES, newPrefixList.maxEntries())
                    .append(FieldDefs.ENTRIES, entries)
                    .append(
                        FieldDefs.HASH,
                        AWSDataExfiltrationPrefixList.computeHash(
                            entries, newPrefixList.maxEntries())));
    final FindOneAndUpdateOptions options =
        new FindOneAndUpdateOptions().upsert(false).returnDocument(ReturnDocument.AFTER);
    return findOneAndUpdateMajority(query, update, options);
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Map<Integer, Integer> getPrefixListHashDistribution() {
    final String countField = "count";
    final List<Document> pipeline =
        List.of(
            new Document(
                BaseDao.GROUP,
                new Document(FieldDefs.ID, "$" + FieldDefs.HASH)
                    .append(countField, new Document(BaseDao.SUM, 1))));

    // Note: HashMap supports null keys. If a prefix list does not have a hash (for some reason), it
    // will still be added to the map (under the "null" key).
    final Map<Integer, Integer> hashDistribution = new HashMap<>();
    try (var cursor =
        getCollection().withDocumentClass(Document.class).aggregate(pipeline).cursor()) {
      cursor.forEachRemaining(
          (doc) -> {
            final Integer hash = doc.getInteger(FieldDefs.ID);
            final Integer count = doc.getInteger(countField);
            if (count != null) {
              hashDistribution.put(hash, count);
            }
          });
    }

    return hashDistribution;
  }

  @PromMethodMetrics(
      name = METHOD_CALL_DURATION,
      help = METHOD_CALL_DURATION_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public long getTotalPrefixListCount() {
    return count(new BasicDBObject());
  }
}
