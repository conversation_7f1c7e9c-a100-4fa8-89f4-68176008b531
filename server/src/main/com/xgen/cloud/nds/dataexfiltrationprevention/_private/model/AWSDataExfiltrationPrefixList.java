package com.xgen.cloud.nds.dataexfiltrationprevention._private.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;
import software.amazon.awssdk.services.ec2.model.PrefixListState;

/**
 * <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_ManagedPrefixList.html">AWS
 * documentation for ManagedPrefixList</a>
 *
 * <p>Constructor used only for (de)serialization purposes. This should not be called directly.
 *
 * @param id Unique MDB ID for the prefix list document
 * @param awsAccountId Foreign key to {@link com.xgen.cloud.nds.aws._public.model.AWSAccount}
 * @param region The region in which the prefix list is created.
 * @param awsPrefixListId AWS-specified ID for the prefix list. Used for lookups against the AWS
 *     API. (1:1 with the AWS model)
 * @param version AWS-specified version identifier for the prefix list. Used for optimistic locking.
 *     (1:1 with the AWS model)
 * @param name Custom name for the prefix list. This should match the name of the prefix list in
 *     AWS. (1:1 with the AWS model)
 * @param addressFamily Must be "IPv4" or "IPv6" according to <a
 *     href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_CreateManagedPrefixList.html">AWS
 *     documentation</a>. (1:1 with the AWS model)
 * @param arn Amazon Resource Name (ARN) for the prefix list. (1:1 with the AWS model)
 * @param maxEntries Maximum number of entries allowed in the prefix list. (1:1 with the AWS model)
 * @param state State of the prefix list. (1:1 with the AWS model)
 * @param entries Set of entries in the prefix list. (1:1 with the AWS model)
 * @param lastModifiedDate Last modified date for the prefix list.
 * @param tags List of tags associated with the prefix list. (1:1 with the AWS model)
 * @param hash Hash of prefix list (entries and max entries). Used for metrics.
 */
public record AWSDataExfiltrationPrefixList(
    @BsonId @JsonProperty(FieldDefs.ID) ObjectId id,
    @BsonProperty(FieldDefs.AWS_ACCOUNT_ID) @JsonProperty(FieldDefs.AWS_ACCOUNT_ID)
        ObjectId awsAccountId,
    @BsonProperty(FieldDefs.REGION) @JsonProperty(FieldDefs.REGION) AWSRegionName region,
    @BsonProperty(FieldDefs.AWS_PREFIX_LIST_ID) @JsonProperty(FieldDefs.AWS_PREFIX_LIST_ID)
        String awsPrefixListId,
    @BsonProperty(FieldDefs.VERSION) @JsonProperty(FieldDefs.VERSION) long version,
    @BsonProperty(FieldDefs.NAME) @JsonProperty(FieldDefs.NAME) String name,
    @BsonProperty(FieldDefs.ADDRESS_FAMILY) @JsonProperty(FieldDefs.ADDRESS_FAMILY)
        String addressFamily,
    @BsonProperty(FieldDefs.ARN) @JsonProperty(FieldDefs.ARN) String arn,
    @BsonProperty(FieldDefs.MAX_ENTRIES) @JsonProperty(FieldDefs.MAX_ENTRIES) int maxEntries,
    @BsonProperty(FieldDefs.STATE) @JsonProperty(FieldDefs.STATE) PrefixListState state,
    @BsonProperty(FieldDefs.ENTRIES) @JsonProperty(FieldDefs.ENTRIES) Set<PrefixListEntry> entries,
    @BsonProperty(FieldDefs.LAST_MODIFIED_DATE) @JsonProperty(FieldDefs.LAST_MODIFIED_DATE)
        Date lastModifiedDate,
    @BsonProperty(FieldDefs.TAGS) @JsonProperty(FieldDefs.TAGS) List<AWSTag> tags,
    @BsonProperty(FieldDefs.HASH) @JsonProperty(FieldDefs.HASH) Integer hash) {

  /**
   * Public constructor for creating a new prefix list document.
   *
   * <p><strong>This should be the entry point for creating a new prefix list document.</strong>
   *
   * @param id Unique MDB ID for the prefix list document
   * @param awsAccountId Foreign key to {@link com.xgen.cloud.nds.aws._public.model.AWSAccount}
   * @param region The region in which the prefix list is created.
   * @param awsPrefixListId AWS-specified ID for the prefix list. Used for lookups against the AWS
   *     API. (1:1 with the AWS model)
   * @param version AWS-specified version identifier for the prefix list. Used for optimistic
   *     locking. (1:1 with the AWS model)
   * @param name Custom name for the prefix list. This should match the name of the prefix list in
   *     AWS. (1:1 with the AWS model)
   * @param addressFamily Must be "IPv4" or "IPv6" according to <a
   *     href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_CreateManagedPrefixList.html">AWS
   *     documentation</a>. (1:1 with the AWS model)
   * @param arn Amazon Resource Name (ARN) for the prefix list. (1:1 with the AWS model)
   * @param maxEntries Maximum number of entries allowed in the prefix list. (1:1 with the AWS
   *     model)
   * @param state State of the prefix list. (1:1 with the AWS model)
   * @param entries Set of entries in the prefix list. (1:1 with the AWS model)
   * @param lastModifiedDate Last modified date for the prefix list.
   * @param tags List of tags associated with the prefix list. (1:1 with the AWS model)
   */
  public AWSDataExfiltrationPrefixList(
      ObjectId id,
      ObjectId awsAccountId,
      AWSRegionName region,
      String awsPrefixListId,
      long version,
      String name,
      String addressFamily,
      String arn,
      int maxEntries,
      PrefixListState state,
      Set<PrefixListEntry> entries,
      Date lastModifiedDate,
      List<AWSTag> tags) {
    this(
        id,
        awsAccountId,
        region,
        awsPrefixListId,
        version,
        name,
        addressFamily,
        arn,
        maxEntries,
        state,
        entries,
        lastModifiedDate,
        tags,
        computeHash(entries, maxEntries));
  }

  public static Integer computeHash(Set<PrefixListEntry> entries, int maxEntries) {
    return new HashCodeBuilder(17, 37).append(entries).append(maxEntries).toHashCode();
  }

  public static class FieldDefs {
    public static final String ID = "_id";
    public static final String AWS_ACCOUNT_ID = "awsAccountId";
    public static final String REGION = "region";
    public static final String AWS_PREFIX_LIST_ID = "awsPrefixListId";
    public static final String VERSION = "version";
    public static final String NAME = "name";
    public static final String ADDRESS_FAMILY = "addressFamily";
    public static final String ARN = "arn";
    public static final String MAX_ENTRIES = "maxEntries";
    public static final String STATE = "state";
    public static final String ENTRIES = "entries";
    public static final String LAST_MODIFIED_DATE = "lastModifiedDate";
    public static final String TAGS = "tags";
    public static final String HASH = "hash";
  }

  public static class Constants {
    public static final String ADDRESS_FAMILY = "IPv4";
    public static final String PREFIX_LIST_NAME = "authorized-destinations-prefix-list";
  }
}
