package com.xgen.cloud.nds.dataexfiltrationprevention._private.svc;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.pointsofpresence._public.exception.PointsOfPresenceSvcException;
import com.xgen.cloud.nds.pointsofpresence._public.svc.PointsOfPresenceSvc;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermission;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermissionList;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Service for determining CIDR exclusions for the data exfiltration prevention sidecar. These
 * exclusions define network ranges that should bypass the sidecar proxy.
 */
@Singleton
public class CidrExclusionsSvc {

  private static final Logger LOG = LoggerFactory.getLogger(CidrExclusionsSvc.class);
  public static final List<String> PRIVATE_IP_CIDRS =
      List.of("10.0.0.0/8", "**********/12", "***********/16");
  public static final List<String> CDN_IP_CIDRS =
      List.of(
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32",
          "*************/32");
  // Note: These control plane IPs are not updated often, but may need to be updated if issues arise
  // during local development.
  public static final List<String> CONTROL_PLANE_IP_CIDRS_FOR_LOCAL_DEVELOPMENT =
      List.of(
          // repo-atlas.cloud-dev.10gen.cc (pl-0853bdaa1272fa8e1 - dep-poc-repo-atlas)
          // good test is yum clean metadata && yum update
          "*************/32",
          "*************/32",
          "*************/32",
          "************/32",
          "************/32",
          "**************/32",
          "************/32",
          // atlas control plane (really ngrok) (pl-087bab29540e83375 -
          // dep-poc-atlas-control-plane)
          "************/32",
          "**********/32",
          "*************/32",
          "************/32",
          "************/32",
          "**********/32",
          "**************/32",
          "**********/32",
          "************/32",
          "*************/32",
          "***********/32",
          "*************/32");

  private final ReplicaSetHardwareDao _replicaSetHardwareDao;
  private final PointsOfPresenceSvc _pointsOfPresenceSvc;
  private final AppSettings _appSettings;

  @Inject
  public CidrExclusionsSvc(
      final ReplicaSetHardwareDao pReplicaSetHardwareDao,
      final PointsOfPresenceSvc pPointsOfPresenceSvc,
      final AppSettings pAppSettings) {
    _replicaSetHardwareDao = pReplicaSetHardwareDao;
    _pointsOfPresenceSvc = pPointsOfPresenceSvc;
    _appSettings = pAppSettings;
  }

  /** Get global CIDR exclusions that apply to all nodes. */
  public List<Exclusion> getGlobalCidrExclusions() throws PointsOfPresenceSvcException {
    LOG.debug("Getting global CIDR exclusions");
    final List<Exclusion> globalCidrExclusions = new ArrayList<>();
    globalCidrExclusions.addAll(
        getControlPlaneCidrExclusions().stream()
            .map(ip -> new Exclusion(ip, ExclusionType.ATLAS_CONTROL_PLANE))
            .toList());
    globalCidrExclusions.addAll(
        getCDNCidrExclusions().stream()
            .map(ip -> new Exclusion(ip, ExclusionType.MONGODB_BINARY_CDN))
            .toList());
    globalCidrExclusions.addAll(
        PRIVATE_IP_CIDRS.stream()
            .map(ip -> new Exclusion(ip, ExclusionType.PRIVATE_IP_RANGE))
            .toList());
    LOG.info("Returning {} global CIDR exclusions", globalCidrExclusions.size());
    return globalCidrExclusions;
  }

  // List of required control plane IPs in the env, including the Proxy Service front LBs.
  @VisibleForTesting
  protected List<String> getControlPlaneCidrExclusions() throws PointsOfPresenceSvcException {
    // For local development environments, we use hardcoded IPs since the PoP service is not
    // available.
    if (_appSettings.getAppEnv().isLocal()) {
      LOG.debug("Using hardcoded control plane IPs for local development environment");
      return CONTROL_PLANE_IP_CIDRS_FOR_LOCAL_DEVELOPMENT;
    }

    final List<String> controlPlaneIps =
        _pointsOfPresenceSvc.getControlPlaneIpAddressesFromLoadBalancers();
    LOG.debug(
        "Successfully fetched {} control plane IPs dynamically from load balancers",
        controlPlaneIps.size());
    return controlPlaneIps;
  }

  // List of static CDN IPs serving MongoDB-owned binaries (internal-downloads.mongodb.com)
  private List<String> getCDNCidrExclusions() {
    return CDN_IP_CIDRS;
  }

  // List of public IPs for nodes of other clusters in group, excluding the requesting container.
  public List<String> getCidrExclusionsForContainer(
      final NDSGroup pNdsGroup, final ObjectId pContainerId) {
    LOG.debug(
        "Getting CIDR exclusions for container {} in group {}",
        pContainerId,
        pNdsGroup.getGroupId());
    final List<ReplicaSetHardware> allReplicaSets =
        new ArrayList<>(_replicaSetHardwareDao.findByGroup(pNdsGroup.getGroupId()));
    if (allReplicaSets.isEmpty()) {
      LOG.warn(
          "No replica sets found for group {} when getting exclusions for container {} - this may"
              + " indicate a configuration issue or timing problem",
          pNdsGroup.getGroupId(),
          pContainerId);
    }
    final NDSNetworkPermissionList ndsNetworkPermissionList =
        NDSGroup.getCombinedNDSNetworkPermissionListExcludingContainer(
            pContainerId, allReplicaSets, pNdsGroup.getNetworkPermissionList());
    final List<String> containerExclusions =
        new ArrayList<>(
            ndsNetworkPermissionList.getNetworkPermissions().stream()
                .map(NDSNetworkPermission::getValue)
                .toList());
    LOG.info(
        "Returning {} CIDR exclusions for container {} in group {}",
        containerExclusions.size(),
        pContainerId,
        pNdsGroup.getGroupId());
    return containerExclusions;
  }

  public record Exclusion(String cidr, @Nullable ExclusionType exclusionType) {}

  public enum ExclusionType {
    ATLAS_CONTROL_PLANE("Atlas Control Plane"),
    MONGODB_BINARY_CDN("MongoDB Binary CDN"),
    PRIVATE_IP_RANGE("Private IP Range");

    private final String description;

    ExclusionType(String description) {
      this.description = description;
    }

    public String getDescription() {
      return description;
    }
  }
}
