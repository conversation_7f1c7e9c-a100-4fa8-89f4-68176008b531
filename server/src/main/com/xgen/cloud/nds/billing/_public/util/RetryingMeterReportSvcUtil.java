package com.xgen.cloud.nds.billing._public.util;

import com.xgen.cloud.billingplatform.common._public.model.Result;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.module.metering.client.svc.IMeterReportSvc;
import com.xgen.module.metering.client.svc.RetryableSupplier;
import com.xgen.module.metering.common.exception.MeterErrorCode;
import com.xgen.module.metering.common.exception.MeterSvcException;
import com.xgen.module.metering.common.model.MeterUsage;
import java.util.List;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class RetryingMeterReportSvcUtil {

  private RetryingMeterReportSvcUtil() {}

  public static IMeterReportSvc from(final IMeterReportSvc pMeterReportSvc) {
    return new RetryingMeterReportSvc(pMeterReportSvc);
  }

  private static class RetryingMeterReportSvc implements IMeterReportSvc {

    private static final Logger LOG = LoggerFactory.getLogger(RetryingMeterReportSvc.class);
    private static final Set<ErrorCode> RETRYABLE_ERROR_CODES =
        Set.of(MeterErrorCode.UNAVAILABLE, MeterErrorCode.UNKNOWN);

    private final IMeterReportSvc delegateMeterReportSvc;

    private RetryingMeterReportSvc(final IMeterReportSvc pDelegateMeterReportSvc) {
      delegateMeterReportSvc = pDelegateMeterReportSvc;
    }

    @Override
    public void submitMeterUsage(
        final List<MeterUsage> pMeterUsages, final String pIngestionJobName) {
      new RetryableSupplier<Boolean, MeterSvcException>(
              () -> {
                try {
                  delegateMeterReportSvc.submitMeterUsage(pMeterUsages, pIngestionJobName);
                } catch (final MeterSvcException pE) {
                  return Result.error(pE);
                }

                // Returning a boolean because a value is needed for the RetryableSupplier
                return Result.of(true);
              },
              (meterSvcException -> {
                final boolean isRetryableCode =
                    RETRYABLE_ERROR_CODES.contains(meterSvcException.getErrorCode());

                if (isRetryableCode) {
                  LOG.warn("failed with errorCode {} retrying", meterSvcException.getErrorCode());
                }

                return isRetryableCode;
              }))
          .get()
          .orElseThrow(RuntimeException::new);
    }
  }
}
