package com.xgen.cloud.nds.checkmetadataconsistency._public.svc;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;

import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.dao.codec._public.encrypted.string.EncryptedString;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSCheckMetadataConsistencyEvent;
import com.xgen.cloud.nds.activity._public.event.audit.NDSCheckMetadataConsistencyEvent.Type;
import com.xgen.cloud.nds.checkmetadataconsistency._private.dao.CheckShardedMetadataConsistencyResultDao;
import com.xgen.cloud.nds.checkmetadataconsistency._private.dao.CheckShardedMetadataConsistencyRunDao;
import com.xgen.cloud.nds.checkmetadataconsistency._public.model.CheckShardedMetadataConsistencyResult;
import com.xgen.cloud.nds.checkmetadataconsistency._public.model.CheckShardedMetadataConsistencyRun;
import com.xgen.cloud.nds.checkmetadataconsistency._public.model.CheckShardedMetadataConsistencyRun.ConsistencyCheckResult;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.corruptiondetection.preflightcheck._public.model.PreflightErrorCode;
import com.xgen.cloud.nds.corruptiondetection.preflightcheck._public.svc.CorruptionDetectionPreflightCheckSvc;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionOperationOrigin;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionRunResult;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.module.common.planner.PlanExecutorJobHandler;
import com.xgen.module.common.planner.PlanPriority;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.svc.PlanSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.bson.Document;
import org.bson.types.ObjectId;

@Singleton
public class NDSCheckMetadataConsistencySvc {

  private final ClusterDescriptionDao clusterDescriptionDao;
  private final GroupSvc groupSvc;
  private final AppSettings appSettings;
  private final CheckShardedMetadataConsistencyRunDao runDao;
  private final CheckShardedMetadataConsistencyResultDao resultDao;
  private final PlanSvc planSvc;
  private final CorruptionDetectionPreflightCheckSvc preflightCheckSvc;
  private final AuditSvc auditSvc;

  @Inject
  public NDSCheckMetadataConsistencySvc(
      ClusterDescriptionDao clusterDescriptionDao,
      GroupSvc groupSvc,
      AppSettings appSettings,
      CheckShardedMetadataConsistencyRunDao runDao,
      CheckShardedMetadataConsistencyResultDao resultDao,
      PlanSvc planSvc,
      CorruptionDetectionPreflightCheckSvc preflightCheckSvc,
      AuditSvc auditSvc) {
    this.clusterDescriptionDao = clusterDescriptionDao;
    this.groupSvc = groupSvc;
    this.appSettings = appSettings;
    this.runDao = runDao;
    this.resultDao = resultDao;
    this.planSvc = planSvc;
    this.preflightCheckSvc = preflightCheckSvc;
    this.auditSvc = auditSvc;
  }

  public List<CheckShardedMetadataConsistencyRun> findActiveRunsForGroup(ObjectId groupId) {
    return runDao.findActiveCheckShardedMetadataConsistencyRunsForGroup(groupId);
  }

  public void runCheckMetadataConsistencyFromPlan(
      Plan plan, ShardedClusterDescription clusterDescription, Group group) throws SvcException {
    preflightCheckSvc.validateNoExistingCheckPresent(clusterDescription);

    if (!isValidClusterForCheckMetadataConsistency(clusterDescription)) {
      throw new SvcException(NDSErrorCode.INVALID_CHECK_METADATA_CONSISTENCY_CONFIGURATION);
    }

    // Pre-flight checks.
    if (!clusterDescription.getCheckMetadataConsistency().skipPreFlightChecks()) {
      try {
        preflightCheckSvc.runPreflightChecks(clusterDescription, group);
      } catch (RuntimeException e) {
        throw new SvcException(PreflightErrorCode.UNABLE_TO_COMPUTE_CPU_USAGE, e);
      }
    }

    planSvc.savePlanAndInsertJob(
        plan, PlanExecutorJobHandler.getNewJobForNDSPlan(plan.getId(), PlanPriority.LOW));

    unsetNeedsCheckMetadataConsistencyAfter(
        clusterDescription.getGroupId(), clusterDescription.getName());
  }

  public void setNeedsCheckMetadataConsistencyAfter(
      ObjectId groupId,
      String clusterName,
      boolean bypassMaintenanceWindow,
      boolean skipPreflightChecks,
      CorruptionDetectionOperationOrigin operationOrigin)
      throws SvcException {
    final Group group = groupSvc.findById(groupId);

    final ClusterDescription clusterDescription =
        clusterDescriptionDao
            .findByName(groupId, clusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    if (!clusterDescription.getClusterType().isSharded()) {
      throw new SvcException(NDSErrorCode.INVALID_CHECK_METADATA_CONSISTENCY_CONFIGURATION);
    }

    if (isFeatureFlagEnabled(
            FeatureFlag.ATLAS_OPT_OUT_PERIODIC_CORRUPTION_DETECTION, appSettings, null, group)
        && operationOrigin != CorruptionDetectionOperationOrigin.MANUAL) {
      auditCheckMetadataConsistency(
          Type.CLUSTER_OPTED_OUT_OF_CHECK_METADATA_CONSISTENCY, clusterDescription);
      return;
    }

    clusterDescriptionDao.setShardingMetadataConsistencyCheck(
        groupId,
        clusterName,
        new Date(),
        bypassMaintenanceWindow,
        skipPreflightChecks,
        operationOrigin);

    auditCheckMetadataConsistency(
        Type.CHECK_METADATA_CONSISTENCY_SCHEDULED_FOR_CLUSTER, clusterDescription);
  }

  private boolean isValidClusterForCheckMetadataConsistency(ClusterDescription clusterDescription) {
    final boolean isActive =
        clusterDescription.getState() != State.DELETED
            && !clusterDescription.isDeleteRequested()
            && !clusterDescription.isPaused();
    return isActive
        && clusterDescription.getClusterType().isSharded()
        && VersionUtils.supportsCheckMetadataConsistency(clusterDescription.getMongoDBVersion());
  }

  boolean unsetNeedsCheckMetadataConsistencyAfter(ObjectId groupId, String clusterName) {
    return clusterDescriptionDao.unsetShardingMetadataConsistencyCheck(groupId, clusterName);
  }

  public boolean postponeNeedsCheckMetadataConsistencyAfter(
      ObjectId groupId, String clusterName, Date postponeDate) {
    return clusterDescriptionDao.updateNeedsCheckMetadataConsistencyAfter(
        groupId, clusterName, postponeDate);
  }

  public boolean startCheckRun(
      ObjectId checkId,
      ClusterDescription clusterDescription,
      ObjectId planId,
      CorruptionDetectionOperationOrigin operationOrigin) {
    return runDao
        .insertMajority(
            new CheckShardedMetadataConsistencyRun(
                checkId,
                clusterDescription.getGroupId(),
                clusterDescription.getName(),
                new Date(),
                null,
                planId,
                clusterDescription.getMongoDBVersion(),
                CorruptionDetectionRunResult.NOT_DONE,
                ConsistencyCheckResult.NOT_DONE,
                operationOrigin))
        .wasAcknowledged();
  }

  public boolean isRunFinished(ObjectId checkId) {
    return runDao
        .getCheckShardedMetadataConsistencyRunByMetadataCheckId(checkId)
        .map(
            run ->
                run.getResult() == CorruptionDetectionRunResult.SUCCESS
                    || run.getResult() == CorruptionDetectionRunResult.FAILED)
        .orElse(false);
  }

  public boolean updateCheckRunResults(
      ObjectId checkId,
      CorruptionDetectionRunResult result,
      ConsistencyCheckResult consistencyCheckResult) {
    return runDao.updateCheckShardedMetadataConsistencyRunResults(
        checkId, result, consistencyCheckResult);
  }

  public boolean insertResultBatch(ObjectId checkId, List<Document> resultBatch) {
    final String resultString =
        "[" + resultBatch.stream().map(Document::toJson).collect(Collectors.joining(",")) + "]";
    return resultDao
        .insertMajority(
            new CheckShardedMetadataConsistencyResult(checkId, new EncryptedString(resultString)))
        .wasAcknowledged();
  }

  private void auditCheckMetadataConsistency(Type eventType, ClusterDescription cd) {
    final NDSCheckMetadataConsistencyEvent.Builder builder =
        new NDSCheckMetadataConsistencyEvent.Builder(eventType);
    builder.groupId(cd.getGroupId());
    builder.clusterId(cd.getUniqueId());
    builder.clusterName(cd.getName());
    builder.hidden(true);
    auditSvc.saveAuditEvent(builder.build());
  }
}
