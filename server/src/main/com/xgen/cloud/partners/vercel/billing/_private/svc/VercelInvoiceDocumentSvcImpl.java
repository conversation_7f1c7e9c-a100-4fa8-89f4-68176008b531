package com.xgen.cloud.partners.vercel.billing._private.svc;

import com.xgen.cloud.common.retry._public.RetryBackoffUtil;
import com.xgen.cloud.partners.vercel.billing._private.dao.VercelInvoiceDocumentDao;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInvoiceDocumentSvc;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoice200Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoiceRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoice;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoiceState;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import reactor.util.retry.RetryBackoffSpec;

@Singleton
public final class VercelInvoiceDocumentSvcImpl
    implements VercelInvoiceDocumentSvc, RetryBackoffUtil {

  private static final int RETRY_MAX_ATTEMPTS = 3;
  private static final Duration RETRY_MIN_BACKOFF = Duration.ofSeconds(1);

  private final VercelInvoiceDocumentDao vercelInvoiceDocumentDao;

  @Inject
  public VercelInvoiceDocumentSvcImpl(VercelInvoiceDocumentDao vercelInvoiceDocumentDao) {
    this.vercelInvoiceDocumentDao = vercelInvoiceDocumentDao;
  }

  @Override
  public Optional<VercelInvoiceDocument> findByInvoiceId(String invoiceId) {
    Map<String, Object> logContext = Map.of("invoiceId", invoiceId);
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.findByInvoiceId(invoiceId),
        retryBackoffSpec("Retried findByInvoiceId", logContext));
  }

  @Override
  public Optional<VercelInvoiceDocument> findByInvoiceExternalId(String externalId) {
    Map<String, Object> logContext = Map.of("externalId", externalId);
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.findByInvoiceExternalId(externalId),
        retryBackoffSpec("Retried findByInvoiceExternalId", logContext));
  }

  @Override
  public List<VercelInvoiceDocument> findByInstallationId(String installationId) {
    Map<String, Object> logContext = Map.of("installationId", installationId);
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.findByInstallationId(installationId),
        retryBackoffSpec("Retried findByInstallationId", logContext));
  }

  @Override
  public Optional<VercelInvoiceDocument> findByRequestExternalId(String externalId) {
    Map<String, Object> logContext = Map.of("externalId", externalId);
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.findByRequestExternalId(externalId),
        retryBackoffSpec("Retried findByRequestExternalId", logContext));
  }

  @Override
  public Stream<VercelInvoiceDocument> findAllByRequestExternalId(Collection<String> externalId) {
    Map<String, Object> logContext = Map.of("externalIdCount", externalId.size());
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.findAllByRequestExternalIds(externalId),
        retryBackoffSpec("Retried findAllByRequestExternalId", logContext));
  }

  @Override
  public Stream<VercelInvoiceDocument> findAllPendingSubmission() {
    Map<String, Object> logContext = Map.of();
    return callWithRetryBackoff(
        vercelInvoiceDocumentDao::findAllPendingSubmission,
        retryBackoffSpec("Retried findAllPendingSubmission", logContext));
  }

  @Override
  public Optional<VercelInvoiceDocument> upsert(
      String installationId, SubmitInvoiceRequest submitInvoiceRequest) {
    Map<String, Object> logContext = Map.of("installationId", installationId);
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.upsert(installationId, submitInvoiceRequest),
        retryBackoffSpec("Retried upsert", logContext));
  }

  @Override
  public Optional<VercelInvoiceDocument> updateResponse(
      ObjectId documentId, SubmitInvoice200Response response) {
    Map<String, Object> logContext = Map.of("documentId", documentId);
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.updateResponse(documentId, response),
        retryBackoffSpec("Retried updateResponse", logContext));
  }

  @Override
  public Optional<VercelInvoiceDocument> updateInvoice(ObjectId paymentId, VercelInvoice invoice) {
    Map<String, Object> logContext = Map.of("paymentId", paymentId);
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.updateInvoice(paymentId, invoice),
        retryBackoffSpec("Retried updateInvoice", logContext));
  }

  @Override
  public Optional<VercelInvoiceDocument> updateInvoiceState(
      String invoiceId, VercelInvoiceState state) {
    Map<String, Object> logContext = Map.of("invoiceId", invoiceId, "state", state);
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.updateInvoiceState(invoiceId, state),
        retryBackoffSpec("Retried updateInvoiceState", logContext));
  }

  @Override
  public Optional<VercelInvoiceDocument> updateForFailure(
      ObjectId documentId, String errorMessage) {
    Map<String, Object> logContext = Map.of("documentId", documentId);
    return callWithRetryBackoff(
        () -> vercelInvoiceDocumentDao.updateForFailure(documentId, errorMessage),
        retryBackoffSpec("Retried updateForFailure", logContext));
  }

  private RetryBackoffSpec retryBackoffSpec(String message, Map<String, Object> logContext) {
    return defaultRetryBackoffSpec(RETRY_MAX_ATTEMPTS, RETRY_MIN_BACKOFF, message, logContext);
  }
}
