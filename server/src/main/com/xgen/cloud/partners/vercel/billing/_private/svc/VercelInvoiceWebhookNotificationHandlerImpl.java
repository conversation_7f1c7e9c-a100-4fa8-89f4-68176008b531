package com.xgen.cloud.partners.vercel.billing._private.svc;

import static net.logstash.logback.argument.StructuredArguments.e;

import com.nimbusds.oauth2.sdk.token.BearerAccessToken;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.billing._public.svc.IPaymentProcessedSvc;
import com.xgen.cloud.billing._public.svc.IPaymentSvc;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.partners.common._public.activity.PartnerAudit;
import com.xgen.cloud.partners.common._public.activity.PartnerEvent;
import com.xgen.cloud.partners.common._public.activity.PartnerEvent.Type;
import com.xgen.cloud.partners.vercel.billing._public.config.VercelBillingConfig;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelPaymentEventMessage;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelPaymentEventType;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelPaymentTransitionResult.ResultType;
import com.xgen.cloud.partners.vercel.billing._public.model.event.VercelInternalEvent;
import com.xgen.cloud.partners.vercel.billing._public.model.event.VercelInternalEventDetail;
import com.xgen.cloud.partners.vercel.billing._public.model.event.VercelInternalEventType;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInstallationSvc;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInvoiceDocumentSvc;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInvoiceWebhookNotificationHandler;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelPaymentStateMachine;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelPaymentSvc;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelRefundSvc;
import com.xgen.cloud.partners.vercel.billing._public.svc.event.VercelInternalEventPublisher;
import com.xgen.cloud.partners.vercel.sdk.client._public.svc.VercelMarketplaceApiClient;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoiceState;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceCreatedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceNotPaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoicePaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceRefundedNotification;
import com.xgen.cloud.payments.events._public.model.VercelChargeFailedEvent;
import com.xgen.cloud.payments.events._public.model.VercelChargeSuccessfulEvent;
import com.xgen.svc.mms.model.billing.Payment;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Callable;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@SuppressWarnings("LoggingSimilarMessage")
@Singleton
public final class VercelInvoiceWebhookNotificationHandlerImpl
    implements VercelInvoiceWebhookNotificationHandler {

  private static final Logger LOG =
      LoggerFactory.getLogger(VercelInvoiceWebhookNotificationHandlerImpl.class);

  private final AuditSvc auditSvc;
  private final IPaymentSvc paymentSvc;
  private final IPaymentProcessedSvc paymentProcessedSvc;
  private final VercelBillingConfig vercelBillingConfig;
  private final VercelInstallationSvc vercelInstallationSvc;
  private final VercelInternalEventPublisher vercelInternalEventPublisher;
  private final VercelInvoiceDocumentSvc vercelInvoiceDocumentSvc;
  private final VercelMarketplaceApiClient vercelMarketplaceApiClient;
  private final VercelPaymentStateMachine vercelPaymentStateMachine;
  private final VercelPaymentSvc vercelPaymentSvc;
  private final VercelRefundSvc vercelRefundSvc;

  @Inject
  public VercelInvoiceWebhookNotificationHandlerImpl(
      AuditSvc auditSvc,
      IPaymentSvc paymentSvc,
      IPaymentProcessedSvc paymentProcessedSvc,
      VercelBillingConfig vercelBillingConfig,
      VercelInstallationSvc vercelInstallationSvc,
      VercelInternalEventPublisher vercelInternalEventPublisher,
      VercelInvoiceDocumentSvc vercelInvoiceDocumentSvc,
      VercelMarketplaceApiClient vercelMarketplaceApiClient,
      VercelPaymentStateMachine vercelPaymentStateMachine,
      VercelPaymentSvc vercelPaymentSvc,
      VercelRefundSvc vercelRefundSvc) {
    this.auditSvc = auditSvc;
    this.paymentSvc = paymentSvc;
    this.paymentProcessedSvc = paymentProcessedSvc;
    this.vercelBillingConfig = vercelBillingConfig;
    this.vercelInstallationSvc = vercelInstallationSvc;
    this.vercelInternalEventPublisher = vercelInternalEventPublisher;
    this.vercelInvoiceDocumentSvc = vercelInvoiceDocumentSvc;
    this.vercelMarketplaceApiClient = vercelMarketplaceApiClient;
    this.vercelPaymentStateMachine = vercelPaymentStateMachine;
    this.vercelPaymentSvc = vercelPaymentSvc;
    this.vercelRefundSvc = vercelRefundSvc;
  }

  /** Helper for wrapping blocking calls to be run concurrently on a separate scheduler. */
  private static <T> Mono<T> getMono(Callable<T> callable) {
    return Mono.fromCallable(callable).subscribeOn(Schedulers.boundedElastic());
  }

  private static Map<String, Object> getLogContext(
      String installationId, String vercelInvoiceId, String paymentId) {
    Map<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("installationId", installationId);
    logContext.put("vercelInvoiceId", vercelInvoiceId);
    logContext.put("paymentId", paymentId);
    return logContext;
  }

  /** Null-safe method to coalesce String to ObjectId. */
  @Nullable
  private static ObjectId objectIdFromString(@Nullable String objectIdString) {
    return objectIdString != null && ObjectId.isValid(objectIdString)
        ? new ObjectId(objectIdString)
        : null;
  }

  /** {@inheritDoc} */
  @Override
  public Mono<Void> handle(MarketplaceInvoiceCreatedNotification notification) {
    String installationId = notification.getPayload().installationId();
    String paymentId = notification.getPayload().externalInvoiceId();
    String vercelInvoiceId = notification.getPayload().invoiceId();
    Map<String, Object> logContext = getLogContext(installationId, vercelInvoiceId, paymentId);

    return getInstallation(installationId)
        // Create internal audit event
        .doOnNext(
            installation ->
                vercelInternalEventPublisher.publish(
                    VercelInternalEvent.of(
                        VercelInternalEventType.MARKETPLACE_INVOICE_CREATED,
                        VercelInternalEventDetail.builder()
                            .vercelInstallationId(installationId)
                            .paymentId(objectIdFromString(paymentId))
                            .vercelInvoiceId(vercelInvoiceId)
                            .build())))
        // Update the VercelInvoiceDocument with the updated invoice (in case invoice.state changed)
        .flatMap(
            installation ->
                fetchAndUpdateVercelInvoice(
                        installation, installationId, vercelInvoiceId, VercelInvoiceState.INVOICED)
                    .doOnNext(ignored -> LOG.info("Updated invoice document. {}", e(logContext)))
                    .thenReturn(installation))
        // Create Vercel Invoice Created activity feed event
        .doOnNext(
            installation ->
                saveAuditEvent(
                    installation.orgId(),
                    installation.installationId(),
                    vercelInvoiceId,
                    PartnerEvent.Type.VERCEL_INVOICE_CREATED))
        .doOnError(
            e ->
                LOG.error(
                    "Failed to handle invoice created notification: {} {}",
                    e.getMessage(),
                    e(logContext),
                    e))
        .then();
  }

  /** {@inheritDoc} */
  @Override
  public Mono<Void> handle(MarketplaceInvoiceNotPaidNotification notification) {
    String installationId = notification.getPayload().installationId();
    String paymentId = notification.getPayload().externalInvoiceId();
    String vercelInvoiceId = notification.getPayload().invoiceId();
    Map<String, Object> logContext = getLogContext(installationId, vercelInvoiceId, paymentId);

    return getInstallation(installationId)
        // Create internal audit event
        .doOnNext(
            installation ->
                vercelInternalEventPublisher.publish(
                    VercelInternalEvent.of(
                        VercelInternalEventType.MARKETPLACE_INVOICE_NOTPAID,
                        VercelInternalEventDetail.builder()
                            .vercelInstallationId(installationId)
                            .paymentId(objectIdFromString(paymentId))
                            .vercelInvoiceId(vercelInvoiceId)
                            .build())))
        // Update the VercelInvoiceDocument with the updated invoice (invoice.state changed)
        .flatMap(
            installation ->
                fetchAndUpdateVercelInvoice(
                        installation, installationId, vercelInvoiceId, VercelInvoiceState.NOTPAID)
                    .doOnNext(ignored -> LOG.info("Updated invoice document. {}", e(logContext)))
                    .flatMap(
                        invoiceDocument -> {
                          VercelInvoiceState state =
                              invoiceDocument
                                  .map(VercelInvoiceDocument::invoice)
                                  .orElseThrow()
                                  .state();
                          if (state != VercelInvoiceState.NOTPAID) {
                            // Protects against race conditions and/or bad actors
                            return Mono.error(
                                new IllegalStateException("Invoice state is not NOTPAID"));
                          }
                          return Mono.just(installation);
                        }))
        // Create Vercel Invoice Not Paid activity feed event.
        .doOnNext(
            installation ->
                saveAuditEvent(
                    installation.orgId(),
                    installation.installationId(),
                    vercelInvoiceId,
                    PartnerEvent.Type.VERCEL_INVOICE_NOT_PAID))
        // Update the Payment status to FAILED.
        .flatMap(
            installation ->
                vercelPaymentStateMachine.sendEvent(
                    VercelPaymentEventMessage.builder(
                            VercelPaymentEventType.VERCEL_INVOICE_NOT_PAID)
                        .paymentId(new ObjectId(paymentId))
                        .auditInfo(AuditInfoHelpers.fromPartnerIntegration())
                        .build()))
        .doOnNext(
            transitionResult -> {
              if (transitionResult.resultType() == ResultType.DENIED) {
                LOG.error(
                    "Failed to transition payment state for invoice not paid notification: {}. This"
                        + " could mean the payment is already in a terminal state (PAID, REFUNDED,"
                        + " etc.). Processing will continue. This log is for visibility only and"
                        + " may be changed to WARN. {}",
                    transitionResult.message(),
                    e(logContext));
              }
            })
        // create VercelChargeFailedEvent and send to paymentProcessedSvc
        .doOnSuccess(
            ignored -> {
              Payment payment = paymentSvc.findById(new ObjectId(paymentId));
              String code = "invoice_not_paid_webhook";
              paymentProcessedSvc.handleEvent(new VercelChargeFailedEvent(payment, code));
            })
        .doOnError(
            e ->
                LOG.error(
                    "Failed to handle invoice not paid notification: {} {}",
                    e.getMessage(),
                    e(logContext),
                    e))
        .then();
  }

  /** {@inheritDoc} */
  @Override
  public Mono<Void> handle(MarketplaceInvoicePaidNotification notification) {
    String installationId = notification.getPayload().installationId();
    String paymentId = notification.getPayload().externalInvoiceId();
    String vercelInvoiceId = notification.getPayload().invoiceId();
    Map<String, Object> logContext = getLogContext(installationId, vercelInvoiceId, paymentId);

    return getInstallation(installationId)
        // Create internal audit event
        .doOnNext(
            installation ->
                vercelInternalEventPublisher.publish(
                    VercelInternalEvent.of(
                        VercelInternalEventType.MARKETPLACE_INVOICE_PAID,
                        VercelInternalEventDetail.builder()
                            .vercelInstallationId(installationId)
                            .paymentId(objectIdFromString(paymentId))
                            .vercelInvoiceId(vercelInvoiceId)
                            .build())))
        // Update the VercelInvoiceDocument with the updated invoice (invoice.state changed)
        .flatMap(
            installation ->
                fetchAndUpdateVercelInvoice(
                        installation, installationId, vercelInvoiceId, VercelInvoiceState.PAID)
                    .doOnNext(ignored -> LOG.info("Updated invoice document. {}", e(logContext)))
                    .flatMap(
                        invoiceDocument -> {
                          VercelInvoiceState state =
                              invoiceDocument
                                  .map(VercelInvoiceDocument::invoice)
                                  .orElseThrow()
                                  .state();
                          if (state != VercelInvoiceState.PAID) {
                            // Protects against race conditions and/or bad actors
                            return Mono.error(
                                new IllegalStateException("Invoice state is not PAID"));
                          }
                          return Mono.just(installation);
                        }))
        // Create Vercel Invoice Paid activity feed event.
        .doOnNext(
            installation ->
                saveAuditEvent(
                    installation.orgId(),
                    installation.installationId(),
                    vercelInvoiceId,
                    PartnerEvent.Type.VERCEL_INVOICE_PAID))
        // Update the Payment status to PAID.
        .flatMap(
            installation ->
                vercelPaymentStateMachine.sendEvent(
                    VercelPaymentEventMessage.builder(VercelPaymentEventType.VERCEL_INVOICE_PAID)
                        .paymentId(new ObjectId(paymentId))
                        .auditInfo(AuditInfoHelpers.fromPartnerIntegration())
                        .build()))
        .doOnNext(
            transitionResult -> {
              if (transitionResult.resultType() == ResultType.DENIED) {
                LOG.error(
                    "Failed to transition payment state for invoice paid notification: {}. This"
                        + " could mean the payment is already in a terminal state (PAID, REFUNDED,"
                        + " etc.). Processing will continue. This log is for visibility only and"
                        + " may be changed to WARN. {}",
                    transitionResult.message(),
                    e(logContext));
              }
            })
        // create VercelChargeSuccessfulEvent and send to paymentProcessedSvc
        .doOnSuccess(
            ignored -> {
              Payment payment = paymentSvc.findById(new ObjectId(paymentId));
              long amount =
                  notification
                      .getPayload()
                      .invoiceTotal()
                      .multiply(BigDecimal.valueOf(100))
                      .longValue();
              if (payment.getStatus() == Payment.Status.PAID) {
                // Update the amount paid if the payment is not already in a refunded state
                vercelPaymentSvc.updateAmountPaid(new ObjectId(paymentId), amount);
              }
              paymentProcessedSvc.handleEvent(new VercelChargeSuccessfulEvent(payment, amount));
            })
        .doOnError(
            e ->
                LOG.error(
                    "Failed to handle invoice paid notification: {} {}",
                    e.getMessage(),
                    e(logContext),
                    e))
        .then();
  }

  /** {@inheritDoc} */
  @Override
  public Mono<Void> handle(MarketplaceInvoiceRefundedNotification notification) {
    String installationId = notification.getPayload().installationId();
    String paymentId = notification.getPayload().externalInvoiceId();
    String vercelInvoiceId = notification.getPayload().invoiceId();
    Map<String, Object> logContext = getLogContext(installationId, vercelInvoiceId, paymentId);
    LocalDateTime notificationTime = notification.getCreatedAt().toLocalDateTime();

    return getInstallation(installationId)
        // Create internal audit event
        .doOnNext(
            installation ->
                vercelInternalEventPublisher.publish(
                    VercelInternalEvent.of(
                        VercelInternalEventType.MARKETPLACE_INVOICE_REFUNDED,
                        VercelInternalEventDetail.builder()
                            .vercelInstallationId(installationId)
                            .paymentId(objectIdFromString(paymentId))
                            .vercelInvoiceId(vercelInvoiceId)
                            .build())))
        // Update the VercelInvoiceDocument with the updated invoice (invoice.state changed)
        .flatMap(
            installation ->
                fetchAndUpdateVercelInvoice(
                        installation, installationId, vercelInvoiceId, VercelInvoiceState.REFUNDED)
                    .doOnNext(ignored -> LOG.info("Updated invoice document. {}", e(logContext)))
                    .flatMap(
                        invoiceDocument -> {
                          VercelInvoiceState state =
                              invoiceDocument
                                  .map(VercelInvoiceDocument::invoice)
                                  .orElseThrow()
                                  .state();
                          if (state != VercelInvoiceState.REFUNDED) {
                            // Protects against race conditions and/or bad actors
                            return Mono.error(
                                new IllegalStateException("Invoice state is not REFUNDED"));
                          }
                          return Mono.just(installation);
                        }))
        // Create Vercel Invoice Refunded activity feed event.
        .doOnNext(
            installation ->
                saveAuditEvent(
                    installation.orgId(),
                    installation.installationId(),
                    vercelInvoiceId,
                    PartnerEvent.Type.VERCEL_INVOICE_REFUNDED))
        // Update the Payment status to REFUNDED unless it's already marked FORGIVEN or CANCELLED
        .flatMap(
            installation -> {
              AuditInfo auditInfo = AuditInfoHelpers.fromPartnerIntegration();
              String reason = "Webhook notification reason: " + notification.getPayload().reason();
              VercelPaymentEventMessage eventMessage =
                  VercelPaymentEventMessage.builder(VercelPaymentEventType.VERCEL_INVOICE_REFUNDED)
                      .paymentId(new ObjectId(paymentId))
                      .auditInfo(auditInfo)
                      .noteOrReason(reason)
                      .date(notificationTime)
                      .build();
              return vercelPaymentStateMachine.sendEvent(eventMessage);
            })
        .doOnNext(
            transitionResult -> {
              if (transitionResult.resultType() == ResultType.DENIED) {
                LOG.error(
                    "Failed to transition payment state for invoice refunded notification: {}. This"
                        + " could mean the payment is already in a terminal state (PAID, REFUNDED,"
                        + " etc.). Processing will continue. This log is for visibility only and"
                        + " may be changed to WARN. {}",
                    transitionResult.message(),
                    e(logContext));
              }
            })
        // Set vercel.refundConfirmedAt on Payment.
        .then(
            getMono(
                    () ->
                        vercelPaymentSvc.updateVercelRefundConfirmedAt(
                            new ObjectId(paymentId), notificationTime))
                .flatMap(Mono::justOrEmpty)
                .doOnSuccess(
                    ignored ->
                        LOG.info("Updated payment vercel.refundConfirmedAt. {}", e(logContext))))
        // Set vercel.refundConfirmedAt on Refund.
        .then(
            getMono(
                    () ->
                        vercelRefundSvc.updateVercelRefundConfirmedAt(
                            new ObjectId(paymentId), notificationTime))
                .flatMap(Mono::justOrEmpty)
                .doOnSuccess(
                    ignored ->
                        LOG.info("Updated refund vercel.refundConfirmedAt. {}", e(logContext))))
        .doOnError(
            e ->
                LOG.error(
                    "Failed to handle invoice refunded notification: {} {}",
                    e.getMessage(),
                    e(logContext),
                    e))
        .then();
  }

  private Mono<VercelInstallation> getInstallation(String installationId) {
    return getMono(() -> vercelInstallationSvc.findByInstallationId(installationId))
        .map(Optional::orElseThrow);
  }

  private Mono<Optional<VercelInvoiceDocument>> fetchAndUpdateVercelInvoice(
      VercelInstallation installation,
      String installationId,
      String vercelInvoiceId,
      VercelInvoiceState targetState) {
    Map<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("installationId", installationId);
    logContext.put("vercelInvoiceId", vercelInvoiceId);
    logContext.put("targetState", targetState);
    if (vercelBillingConfig.isInvoiceFinalizeEnabled()) {
      // When feature flag is enabled, skip API fetch and update state directly
      // Requests to query Vercel API for any installation data after finalization will fail
      LOG.info("Invoice finalize enabled, updating state directly. {}", e(logContext));
      return getMono(
          () -> vercelInvoiceDocumentSvc.updateInvoiceState(vercelInvoiceId, targetState));
    }
    // If finalization is not enabled, fetch the latest invoice data from Vercel API
    LOG.info("Invoice finalize disabled, fetching from API. {}", e(logContext));
    return vercelMarketplaceApiClient
        .getInvoice(installationId, vercelInvoiceId, getAccessToken(installation))
        .map(
            invoice ->
                vercelInvoiceDocumentSvc.updateInvoice(
                    new ObjectId(invoice.externalId()), invoice));
  }

  private BearerAccessToken getAccessToken(VercelInstallation installation) {
    return new BearerAccessToken(
        installation.accessTokenDecrypted(vercelBillingConfig.getAccessTokenKey()));
  }

  private void saveAuditEvent(
      ObjectId orgId, String installationId, String vercelInvoiceId, Type eventType) {
    PartnerAudit.Builder event = new PartnerAudit.Builder(eventType);
    event.auditInfo(AuditInfoHelpers.fromPartnerIntegration());
    event.orgId(orgId);
    event.vercelInstallationId(installationId);
    event.vercelInvoiceId(vercelInvoiceId);
    auditSvc.saveAuditEvent(event.build());
  }
}
