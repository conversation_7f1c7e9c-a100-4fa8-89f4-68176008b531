package com.xgen.cloud.partners.vercel.billing._private.svc;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.offsetDateTimeOf;
import static net.logstash.logback.argument.StructuredArguments.e;

import com.nimbusds.oauth2.sdk.token.AccessToken;
import com.nimbusds.oauth2.sdk.token.BearerAccessToken;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.billingshared.common.util._public.svc.SchedulerFactory;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.constants._public.model.metrics.UsageReportingResultLabel;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.partners.usage.report._public.model.ErrorUpdateRequest;
import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReport.Status;
import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReportDeleteReason;
import com.xgen.cloud.partners.usage.report._public.model.UsageReportSubmissionResult;
import com.xgen.cloud.partners.usage.report._public.model.VercelUsageReport;
import com.xgen.cloud.partners.usage.report._public.svc.VercelUsageReportSvc;
import com.xgen.cloud.partners.usage.reporting._public.config.VercelUsageReportingConfig;
import com.xgen.cloud.partners.vercel.billing._public.config.VercelBillingConfig;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInstallationSvc;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelUsageReportSubmissionSvc;
import com.xgen.cloud.partners.vercel.sdk.client._public.exception.VercelApiRequestException;
import com.xgen.cloud.partners.vercel.sdk.client._public.svc.VercelMarketplaceApiClient;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.billingdata.SubmitBillingDataRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelInstallation;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

@Singleton
public final class VercelUsageReportSubmissionSvcImpl implements VercelUsageReportSubmissionSvc {

  private static final Logger LOG =
      LoggerFactory.getLogger(VercelUsageReportSubmissionSvcImpl.class);
  private static final Scheduler SCHEDULER =
      SchedulerFactory.newBoundedElastic("VercelUsageReportSubmissionSvcImpl");
  private static final Counter VERCEL_PROCESSING_RESULT_COUNTER =
      Counter.build()
          .name("payments_vercel_usage_reporting_result_total")
          .help(
              "Metric for tracking the health of individual Vercel usage reports by report status.")
          .labelNames(UsageReportingResultLabel.RESULT_LABEL)
          .register();

  private static final BigDecimal HUNDRED_IN_BIG_DECIMAL = new BigDecimal("100");

  /**
   * Number of reports per batch for data loading operations. Used for operations that primarily
   * involve database queries without heavy processing.
   */
  private static final int BATCH_SIZE_DATA_LOADING = 100;

  private static final int VERCEL_CONCURRENCY_LOAD = 25;
  private static final String VALIDATION_FAILED = "VALIDATION_FAILED";

  private final Clock clock;
  private final VercelBillingConfig vercelBillingConfig;
  private final VercelInstallationSvc vercelInstallationSvc;
  private final VercelMarketplaceApiClient vercelMarketplaceApiClient;
  private final VercelUsageReportingConfig vercelUsageReportingConfig;
  private final VercelUsageReportSvc vercelUsageReportSvc;
  private final AuditSvc auditSvc;

  @Inject
  public VercelUsageReportSubmissionSvcImpl(
      Clock clock,
      VercelBillingConfig vercelBillingConfig,
      VercelInstallationSvc vercelInstallationSvc,
      VercelMarketplaceApiClient vercelMarketplaceApiClient,
      VercelUsageReportingConfig vercelUsageReportingConfig,
      VercelUsageReportSvc vercelUsageReportSvc,
      AuditSvc auditSvc) {
    this.clock = clock;
    this.vercelBillingConfig = vercelBillingConfig;
    this.vercelInstallationSvc = vercelInstallationSvc;
    this.vercelMarketplaceApiClient = vercelMarketplaceApiClient;
    this.vercelUsageReportingConfig = vercelUsageReportingConfig;
    this.vercelUsageReportSvc = vercelUsageReportSvc;
    this.auditSvc = auditSvc;
  }

  /**
   * Helper method for wrapping blocking calls to be executed on a dedicated thread pool.
   *
   * <p>This utility method converts blocking operations (such as database calls) into reactive
   * {@code Mono} objects that execute on a bounded elastic scheduler. This prevents blocking the
   * main reactive event loop and allows for proper concurrent execution of potentially blocking
   * operations.
   *
   * <p><strong>Thread Pool Configuration:</strong> Uses a bounded elastic scheduler specifically
   * configured for this service with:
   *
   * <p><strong>Usage:</strong> Wrap any blocking database or service calls with this method to
   * ensure they don't block reactive streams processing.
   *
   * @param <T> the type of result returned by the callable
   * @param callable the blocking operation to execute
   * @return Mono that will execute the callable on the dedicated thread pool
   */
  private static <T> Mono<T> getMono(Callable<T> callable) {
    return Mono.fromCallable(callable).subscribeOn(SCHEDULER);
  }

  /** {@inheritDoc} */
  @Override
  public Mono<Void> findAndSubmitUsageReports(List<ObjectId> reportIds) {
    // Use current time as reportingTime for backward compatibility
    return Flux.fromIterable(reportIds)
        .transform(this::executeBatchProcessingPipeline)
        .doOnSubscribe(
            subscription ->
                LOG.info(
                    "Starting batch processing pipeline for {} Vercel usage reports",
                    reportIds.size()))
        .doOnComplete(
            () ->
                LOG.info(
                    "Completed batch processing pipeline for {} Vercel usage reports",
                    reportIds.size()))
        .doOnError(
            error ->
                LOG.error(
                    "Error in batch processing pipeline for {} Vercel usage reports",
                    reportIds.size(),
                    error))
        .then();
  }

  /** {@inheritDoc} */
  @Override
  public Mono<Void> findAndSubmitUsageReports() {
    return getMono(vercelUsageReportSvc::findAllPendingSubmission)
        .flatMapMany(Flux::fromStream)
        .transform(this::executeBatchProcessingPipeline)
        .doOnSubscribe(
            subscription ->
                LOG.info("Starting batch processing pipeline for pending Vercel usage reports"))
        .doOnComplete(
            () -> LOG.info("Completed batch processing pipeline for pending Vercel usage reports"))
        .doOnError(
            error ->
                LOG.error(
                    "Error in batch processing pipeline for pending Vercel usage reports", error))
        .then();
  }

  /**
   * Validates whether a usage report should be processed for submission.
   *
   * <p>This method performs several validation checks to determine if a usage report should be
   * submitted:
   *
   * <ul>
   *   <li>Ensures the report hasn't already been successfully submitted
   *   <li>Verifies that the report isn't too old (older than 30 days from creation)
   *   <li>Confirms that the installation is not deleted
   * </ul>
   *
   * @param usageReport the usage report to validate
   * @param installation the associated Vercel installation
   * @return true if the report should be processed, false if it should be skipped
   */
  boolean isValidToProcess(VercelUsageReport usageReport, VercelInstallation installation) {
    Map<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("installationId", usageReport.getVercelInstallationId());
    logContext.put("usageReportId", usageReport.getId());

    // Check if already successfully submitted
    if (usageReport.getStatus() == Status.SUCCESSFUL) {
      LOG.warn("Usage report already submitted! Skipping. {}", e(logContext));
      return false;
    }

    // Check installation status
    LocalDateTime now = LocalDateTime.now(clock);
    boolean maxDurationForRetriesElapsed =
        usageReport.getCreated() != null
            && usageReport
                .getRequest()
                .eod()
                .toLocalDateTime()
                .isBefore(now.minus(vercelUsageReportingConfig.getUsageReportingCutoffDuration()));

    if (maxDurationForRetriesElapsed) {
      LOG.warn(
          "Usage report is older than the cutoff duration. Skipping and self healing {}",
          e(logContext));
      vercelUsageReportSvc.softDeleteUsageReportById(
          usageReport.getId(),
          PartnerUsageReportDeleteReason.VERCEL_SELF_SERVE_LATE_SUBMITTED_USAGE_REPORT);
      return false;
    }

    // This shouldn't happen since we report usage reports when they delete their installation, but
    // we need to guard it in case it does
    boolean installationDeleted = installation.deletedAt() != null;

    if (installationDeleted) {
      LOG.warn("Skipping usage report due to installation deleted. {}", e(logContext));
      return false;
    }
    return true;
  }

  /**
   * Execute the batch processing pipeline with batch installation loading and batch DB updates
   *
   * <p>This is the core batch processing engine that transforms a stream of ObjectIds into
   * efficiently processed usage reports. The method implements a sophisticated three-phase pipeline
   * designed for optimal database efficiency and concurrent processing.
   *
   * <p><strong>Three-Phase Processing Pipeline:</strong>
   *
   * <ol>
   *   <li><strong>Phase 1 - Lock and Fetch:</strong>
   *       <ul>
   *         <li>Buffer ObjectIds into batches
   *         <li>Attempt to lock each batch using tryMarkVercelUsageReportsAsProcessing()
   *         <li>Fetch full VercelUsageReport objects ONLY for successfully locked IDs
   *         <li>This prevents processing reports already being handled by other instances
   *       </ul>
   *   <li><strong>Phase 2 - Batch Processing:</strong>
   *       <ul>
   *         <li>Buffer locked reports into processing batches
   *         <li>Batch load Vercel installations
   *         <li>Submit reports to Vercel API concurrently
   *         <li>Collect submission results without updating database yet
   *       </ul>
   *   <li><strong>Phase 3 - Batch Database Updates:</strong>
   *       <ul>
   *         <li>Group results by success/failure status using reactive groupBy()
   *         <li>Batch update successful reports with completion timestamps
   *         <li>Batch update failed reports with individual error codes and messages
   *         <li>Use MongoDB bulk write operations for maximum efficiency
   *       </ul>
   * </ol>
   *
   * @param reportIds the flux of usage report ObjectIds to submit
   * @return Mono that completes when all processing is finished (void result for batch operations)
   */
  Mono<Void> executeBatchProcessingPipeline(Flux<ObjectId> reportIds) {
    return reportIds
        .buffer(BATCH_SIZE_DATA_LOADING)
        .flatMap(this::lockAndFetchReportsBatch, 25)
        .buffer(BATCH_SIZE_DATA_LOADING)
        .flatMap(this::submitReportsToVercelApi, 25)
        // Phase 2: Group by success/failure and batch update
        .groupBy(UsageReportSubmissionResult::isSuccess)
        .flatMap(
            group -> {
              boolean isSuccess = group.key();
              if (isSuccess) {
                return group
                    .buffer(BATCH_SIZE_DATA_LOADING)
                    .flatMap(
                        batchSuccesses -> {
                          List<ObjectId> successIds =
                              batchSuccesses.stream()
                                  .map(UsageReportSubmissionResult::reportId)
                                  .toList();
                          Date reportedAtDate = batchSuccesses.get(0).reportedAtDate();
                          Date reportedStartDate = batchSuccesses.get(0).reportedStartDate();

                          vercelUsageReportSvc.updateSuccessFieldsForVercelUsageReportIdsInBatch(
                              successIds, reportedAtDate, reportedStartDate);

                          VERCEL_PROCESSING_RESULT_COUNTER
                              .labels(UsageReportingResultLabel.SUCCESS)
                              .inc(successIds.size());
                          LOG.info("Batch updated {} successful reports", successIds.size());
                          return Mono.empty();
                        },
                        25);
              }
              // else: failure
              return group
                  .buffer(BATCH_SIZE_DATA_LOADING)
                  .flatMap(
                      batchFailures -> {
                        List<ErrorUpdateRequest> errorUpdates =
                            batchFailures.stream()
                                .map(
                                    result ->
                                        new ErrorUpdateRequest(
                                            result.reportId(),
                                            result.errorCode(),
                                            result.errorMessage(),
                                            result.reportedAtDate()))
                                .toList();
                        vercelUsageReportSvc.updateErrorFieldsForVercelUsageReportsInBatch(
                            errorUpdates);
                        long failureCountForMetrics =
                            batchFailures.stream()
                                .filter(
                                    result ->
                                        !Objects.equals(result.errorCode(), VALIDATION_FAILED))
                                .count();
                        VERCEL_PROCESSING_RESULT_COUNTER
                            .labels(UsageReportingResultLabel.FAILURE)
                            .inc(failureCountForMetrics);
                        LOG.info(
                            "Batch updated {} failed reports with individual error details",
                            errorUpdates.size());
                        return Mono.empty();
                      },
                      25);
            },
            2)
        .then();
  }

  /**
   * Phase 1: Lock a batch of ObjectIds and fetch actual reports for locked IDs only.
   *
   * <p>This method implements the critical first phase of our batch processing pipeline, ensuring
   * safe concurrent processing by acquiring locks before fetching data.
   *
   * <p><strong>Processing Steps:</strong>
   *
   * <ol>
   *   <li><strong>Batch Locking:</strong> Attempts to lock all ObjectIds in the batch using {@code
   *       tryMarkVercelUsageReportsAsProcessing()}. This prevents other instances from processing
   *       the same reports concurrently.
   *   <li><strong>Lock Validation:</strong> Checks if any locks were successfully acquired. If no
   *       locks are obtained, returns empty to avoid unnecessary processing.
   *   <li><strong>Selective Data Fetching:</strong> Fetches full {@code VercelUsageReport} objects
   *       ONLY for successfully locked IDs using {@code findUsageReportsByIdsAsStream()}. This
   *       ensures we don't waste resources loading data we can't process.
   * </ol>
   *
   * @param reportIds list of usage report ObjectIds to lock and fetch
   * @return Flux of VercelUsageReport objects for successfully locked reports (may be fewer than
   *     input)
   */
  private Flux<VercelUsageReport> lockAndFetchReportsBatch(List<ObjectId> reportIds) {
    LOG.info("Phase 1: Locking and fetching batch of {} usage reports", reportIds.size());

    // Try to lock the reports synchronously
    Date lockDate = Date.from(clock.instant());
    List<ObjectId> lockedIds =
        vercelUsageReportSvc.tryMarkVercelUsageReportsAsProcessing(reportIds, lockDate);

    if (lockedIds.isEmpty()) {
      LOG.warn("Could not acquire locks for any reports in batch of {}", reportIds.size());
      return Flux.empty(); // No reports locked
    }

    if (lockedIds.size() < reportIds.size()) {
      LOG.warn("Acquired locks for {}/{} reports in batch", lockedIds.size(), reportIds.size());
    }

    // Fetch actual usage reports for locked IDs only
    return getMono(() -> vercelUsageReportSvc.findUsageReportsByIdsAsStream(lockedIds))
        .flatMapMany(Flux::fromStream);
  }

  /**
   * Phase 2: Submit reports to Vercel API with batch installation loading
   *
   * <p>This method implements the core processing logic of our batch pipeline, focusing on
   * efficient installation loading and concurrent API submissions. It operates on reports that have
   * already been successfully locked by the previous phase.
   *
   * <p><strong>Processing Steps:</strong>
   *
   * <ol>
   *   <li><strong>Installation ID Extraction:</strong> Extracts unique Vercel installation IDs from
   *       the batch of reports. Since there's typically one report per installation, this usually
   *       results in the same number of installation IDs as reports.
   *   <li><strong>Batch Installation Loading:</strong> Performs a single database query to fetch
   *       all required {@code VercelInstallation} objects using {@code findByInstallationIds()}.
   *   <li><strong>Concurrent Report Processing:</strong> Processes each report concurrently (up to
   *       10 concurrent submissions per batch) by:
   *       <ul>
   *         <li>Validating the report and its associated installation
   *         <li>Submitting the report to Vercel's API via {@code submitUsageReportToVercel()}
   *         <li>Collecting results without updating the database (deferred to Phase 3)
   *       </ul>
   * </ol>
   *
   * <p><strong>No Database Updates:</strong> This method intentionally does not update the database
   * with submission results. All results are collected and returned for batch database updates in
   * Phase 3, which provides better performance and transactional consistency.
   *
   * @param batch list of usage reports to process
   * @return Flux of BatchSubmissionResult objects containing success/failure status and error
   *     details
   */
  private Flux<UsageReportSubmissionResult> submitReportsToVercelApi(
      List<VercelUsageReport> batch) {
    if (batch.isEmpty()) {
      return Flux.empty();
    }

    LOG.info("Phase 2: Processing batch of {} locked usage reports", batch.size());

    // Extract installation IDs from reports (reports are already locked)
    List<String> installationIds =
        batch.stream().map(VercelUsageReport::getVercelInstallationId).toList();

    // Load installations for reports
    return getMono(() -> vercelInstallationSvc.findAndGroupByInstallationIds(installationIds))
        .flatMapMany(
            installationMap -> {
              // Process each report with pre-loaded installation data
              return Flux.fromIterable(batch)
                  .flatMap(
                      report -> {
                        VercelInstallation installation =
                            installationMap.get(report.getVercelInstallationId());

                        if (installation == null) {
                          LinkedHashMap<String, Object> logContext = new LinkedHashMap<>();
                          logContext.put("reportId", report.getId());
                          logContext.put("installationId", report.getVercelInstallationId());

                          LOG.warn("Installation not found {}", e(logContext));
                          return Mono.just(
                              UsageReportSubmissionResult.validationFailure(
                                  report.getId(),
                                  "INSTALLATION_NOT_FOUND",
                                  "Installation not found"));
                        }

                        if (!isValidToProcess(report, installation)) {
                          return Mono.just(
                              UsageReportSubmissionResult.validationFailure(
                                  report.getId(), VALIDATION_FAILED, "Report failed validation"));
                        }
                        return submitUsageReportToVercel(report, installation);
                      },
                      VERCEL_CONCURRENCY_LOAD); // Limit concurrency within batch
            })
        .onErrorResume(
            e -> {
              LOG.error("Error in batch installation loading for {} reports", batch.size(), e);
              // Return failure results for all reports in the batch
              return Flux.fromIterable(batch)
                  .map(
                      report ->
                          UsageReportSubmissionResult.validationFailure(
                              report.getId(), "INSTALLATION_LOAD_ERROR", e.getMessage()));
            });
  }

  /**
   * Submit a single usage report to Vercel's API
   *
   * <p>This method handles the actual submission of usage data to Vercel's marketplace API while
   * deferring database updates for batch processing efficiency. It's designed to be called
   * concurrently within batch processing operations.
   *
   * <p><strong>Processing Steps:</strong>
   *
   * <ol>
   *   <li><strong>Authentication Setup:</strong> Creates a Bearer access token using the
   *       installation's decrypted access token for API authentication
   *   <li><strong>API Submission:</strong> Submits the usage report data to Vercel's {@code
   *       submitBillingData} endpoint using the marketplace API client
   *   <li><strong>Result Collection:</strong> Captures the submission result (success/failure)
   *       along with timestamp information for later database updates
   *   <li><strong>Error Handling:</strong> Converts API exceptions into structured error results
   *       with appropriate error codes and messages
   * </ol>
   *
   * <p><strong>No Database Updates:</strong> Intentionally does not update the database with
   * submission results. This allows for efficient batch database updates in a later phase, reducing
   * database load and improving overall performance.
   *
   * <p><strong>Logging:</strong> Provides structured logging with installation ID, credit ID, and
   * usage report ID for comprehensive traceability and debugging.
   *
   * @param report the usage report to submit to Vercel's API
   * @param installation the Vercel installation containing authentication credentials
   * @return Mono of BatchSubmissionResult containing success/failure status and error details
   */
  private Mono<UsageReportSubmissionResult> submitUsageReportToVercel(
      VercelUsageReport report, VercelInstallation installation) {

    String installationId = report.getVercelInstallationId();
    AccessToken accessToken =
        new BearerAccessToken(
            installation.accessTokenDecrypted(vercelBillingConfig.getAccessTokenKey()));
    Date reportedAtDate = Date.from(clock.instant());

    LinkedHashMap<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("installationId", installationId);
    logContext.put("creditId", report.getCreditId());

    Date reportTimestamp =
        DateTimeUtils.dateOf(vercelUsageReportSvc.calculateUsageReportTimestamp());

    SubmitBillingDataRequest request =
        report.getRequest().toBuilder().timestamp(offsetDateTimeOf(reportTimestamp)).build();
    VercelUsageReport updatedReport = report.toBuilder().request(request).build();

    return vercelMarketplaceApiClient
        .submitBillingData(installationId, updatedReport.getRequest(), accessToken)
        .then(
            Mono.fromCallable(
                () -> {
                  LOG.info("Successfully submitted usage report. {}", e(logContext));

                  // Extract dayValue and periodValue from the usage request
                  BigDecimal dayValue = updatedReport.getRequest().usage().get(0).dayValue();
                  BigDecimal periodValue = updatedReport.getRequest().usage().get(0).periodValue();

                  saveDailyUsageReportedAudit(
                      updatedReport.getOrgId(),
                      installationId,
                      installation.installationUrl(),
                      dayValue,
                      periodValue);

                  return UsageReportSubmissionResult.success(
                      updatedReport.getId(), reportedAtDate, reportTimestamp);
                }))
        .onErrorResume(
            e -> {
              String errorCode = "UNKNOWN";
              String errorMessage = e.getMessage();

              if (e instanceof VercelApiRequestException exception) {
                errorCode = exception.getErrorCode();
                errorMessage = exception.getMessage();
                logContext.put("errorCode", errorCode);
                logContext.put("statusCode", exception.getStatusCode());
              }

              LOG.error("Failed to submit usage report {}", e(logContext), e);
              return Mono.just(
                  UsageReportSubmissionResult.failure(
                      updatedReport.getId(), errorCode, errorMessage, reportedAtDate));
            });
  }

  private void saveDailyUsageReportedAudit(
      ObjectId orgId,
      String installationId,
      String installationUrl,
      BigDecimal dayValue,
      BigDecimal periodValue) {
    BillingAudit.Builder vercelEvent =
        new BillingAudit.Builder(BillingEvent.Type.VERCEL_USAGE_REPORTED, ObjectId.get());
    vercelEvent.auditInfo(AuditInfoHelpers.fromSystem());
    vercelEvent.orgId(orgId);
    vercelEvent.vercelInstallationId(installationId);
    vercelEvent.vercelInstallationUrl(installationUrl);
    vercelEvent.partnerMarketplaceDayValueCents(
        dayValue.multiply(HUNDRED_IN_BIG_DECIMAL).longValue());
    vercelEvent.partnerMarketplacePeriodValueCents(
        periodValue.multiply(HUNDRED_IN_BIG_DECIMAL).longValue());
    vercelEvent.hidden(true);
    auditSvc.saveAuditEvent(vercelEvent.build());
  }
}
