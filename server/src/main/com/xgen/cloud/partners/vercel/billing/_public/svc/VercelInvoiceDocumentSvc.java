package com.xgen.cloud.partners.vercel.billing._public.svc;

import com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoice200Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoiceRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoice;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoiceState;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.types.ObjectId;

public interface VercelInvoiceDocumentSvc {

  Optional<VercelInvoiceDocument> findByInvoiceId(String invoiceId);

  Optional<VercelInvoiceDocument> findByInvoiceExternalId(String externalId);

  List<VercelInvoiceDocument> findByInstallationId(String installationId);

  Optional<VercelInvoiceDocument> findByRequestExternalId(String externalId);

  Stream<VercelInvoiceDocument> findAllByRequestExternalId(Collection<String> externalId);

  Stream<VercelInvoiceDocument> findAllPendingSubmission();

  Optional<VercelInvoiceDocument> upsert(
      String installationId, SubmitInvoiceRequest submitInvoiceRequest);

  Optional<VercelInvoiceDocument> updateResponse(
      ObjectId documentId, SubmitInvoice200Response response);

  Optional<VercelInvoiceDocument> updateInvoice(ObjectId paymentId, VercelInvoice invoice);

  Optional<VercelInvoiceDocument> updateInvoiceState(String invoiceId, VercelInvoiceState state);

  Optional<VercelInvoiceDocument> updateForFailure(ObjectId documentId, String errorMessage);
}
