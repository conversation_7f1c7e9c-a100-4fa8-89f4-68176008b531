package com.xgen.cloud.partners.vercel.billing._public.svc;

import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceCreatedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceNotPaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoicePaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceRefundedNotification;
import reactor.core.publisher.Mono;

/**
 * Handles Vercel marketplace invoice webhook notifications.
 *
 * <p>Implementations should:
 *
 * <ul>
 *   <li>Publish internal events for observability and analytics
 *   <li>Update VercelInvoiceDocument from Vercel API or via invoice finalization feature flag
 *   <li>Create activity feed audit events for user visibility
 *   <li>Drive payment state transitions and emit payment processing events
 * </ul>
 */
public interface VercelInvoiceWebhookNotificationHandler {

  /**
   * Handles marketplace invoice created webhook notifications.
   *
   * <p>Processing steps:
   *
   * <ol>
   *   <li>Retrieve the Vercel installation by installation ID
   *   <li>Publish internal event MARKETPLACE_INVOICE_CREATED (installationId, paymentId,
   *       vercelInvoiceId)
   *   <li>Update the VercelInvoiceDocument with the latest invoice
   *       <ul>
   *         <li>If invoice finalization feature flag is enabled, update state directly to INVOICED
   *         <li>Otherwise, fetch from Vercel API and update the document
   *       </ul>
   *   <li>Log successful invoice document update
   *   <li>Create a VERCEL_INVOICE_CREATED activity feed audit event
   * </ol>
   *
   * @param notification the marketplace invoice created notification containing installation ID,
   *     external invoice ID (payment ID), and Vercel invoice ID
   * @return a Mono that completes when all processing steps are finished
   */
  Mono<Void> handle(MarketplaceInvoiceCreatedNotification notification);

  /**
   * Handles marketplace invoice not paid webhook notifications.
   *
   * <p>Processing steps:
   *
   * <ol>
   *   <li>Retrieve the Vercel installation by installation ID
   *   <li>Publish internal event MARKETPLACE_INVOICE_NOTPAID (installationId, paymentId,
   *       vercelInvoiceId)
   *   <li>Update the VercelInvoiceDocument with the latest invoice
   *       <ul>
   *         <li>If invoice finalization feature flag is enabled, update state directly to NOTPAID
   *         <li>Otherwise, fetch from Vercel API and update the document
   *       </ul>
   *   <li>Validate invoice.state is NOTPAID to protect against races/bad input
   *   <li>Create a VERCEL_INVOICE_NOT_PAID activity feed audit event
   *   <li>Send VERCEL_INVOICE_NOT_PAID event to payment state machine (transition to FAILED)
   *   <li>Create and send VercelChargeFailedEvent to payment processed service
   * </ol>
   *
   * <p>Note: If the payment state transition is denied (e.g., payment already in terminal state),
   * an error is logged but processing continues.
   *
   * @param notification the marketplace invoice not paid notification containing installation ID,
   *     external invoice ID (payment ID), and Vercel invoice ID
   * @return a Mono that completes when all processing steps are finished
   */
  Mono<Void> handle(MarketplaceInvoiceNotPaidNotification notification);

  /**
   * Handles marketplace invoice paid webhook notifications.
   *
   * <p>Processing steps:
   *
   * <ol>
   *   <li>Retrieve the Vercel installation by installation ID
   *   <li>Publish internal event MARKETPLACE_INVOICE_PAID (installationId, paymentId,
   *       vercelInvoiceId)
   *   <li>Update the VercelInvoiceDocument with the latest invoice
   *       <ul>
   *         <li>If invoice finalization feature flag is enabled, update state directly to PAID
   *         <li>Otherwise, fetch from Vercel API and update the document
   *       </ul>
   *   <li>Validate invoice.state is PAID to protect against races/bad input
   *   <li>Create a VERCEL_INVOICE_PAID activity feed audit event
   *   <li>Send VERCEL_INVOICE_PAID event to payment state machine (transition to PAID)
   *   <li>On success, find Payment and compute amount from invoice total (in cents)
   *   <li>If payment is in PAID state, update amountPaid; then emit VercelChargeSuccessfulEvent
   * </ol>
   *
   * <p>Note: If the payment state transition is denied (e.g., payment already in terminal state),
   * an error is logged but processing continues.
   *
   * @param notification the marketplace invoice paid notification containing installation ID,
   *     external invoice ID (payment ID), Vercel invoice ID, and invoice total
   * @return a Mono that completes when all processing steps are finished
   */
  Mono<Void> handle(MarketplaceInvoicePaidNotification notification);

  /**
   * Handles marketplace invoice refunded webhook notifications.
   *
   * <p>Processing steps:
   *
   * <ol>
   *   <li>Retrieve the Vercel installation by installation ID
   *   <li>Publish internal event MARKETPLACE_INVOICE_REFUNDED (installationId, paymentId,
   *       vercelInvoiceId)
   *   <li>Update the VercelInvoiceDocument with the latest invoice
   *       <ul>
   *         <li>If invoice finalization feature flag is enabled, update state directly to REFUNDED
   *         <li>Otherwise, fetch from Vercel API and update the document
   *       </ul>
   *   <li>Validate invoice.state is REFUNDED to protect against races/bad input
   *   <li>Create a VERCEL_INVOICE_REFUNDED activity feed audit event
   *   <li>Send VERCEL_INVOICE_REFUNDED to payment state machine (transition to REFUNDED unless
   *       already FORGIVEN or CANCELLED)
   *   <li>Set payment.vercel.refundConfirmedAt to notification.createdAt
   *   <li>Set refund.vercel.refundConfirmedAt to notification.createdAt
   * </ol>
   *
   * <p>Note: If the payment state transition is denied (e.g., payment already in terminal state),
   * an error is logged but processing continues.
   *
   * @param notification the marketplace invoice refunded notification containing installation ID,
   *     external invoice ID (payment ID), Vercel invoice ID, refund reason, and creation timestamp
   * @return a Mono that completes when all processing steps are finished
   */
  Mono<Void> handle(MarketplaceInvoiceRefundedNotification notification);
}
