package com.xgen.cloud.partners.vercel.webhook.runtime.res;

import static com.xgen.cloud.partners.vercel.billing._public.config.VercelBillingConstants.VERCEL_RATE_LIMIT_POLICY_PREFIX;
import static com.xgen.cloud.partners.vercel.billing._public.config.VercelBillingConstants.VERCEL_WEBHOOK_RATE_LIMIT_NAME;
import static net.logstash.logback.argument.StructuredArguments.entries;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.billingshared.common.util._public.svc.SchedulerFactory;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited.Type;
import com.xgen.cloud.partners.vercel.webhook._public.model.AbstractWebhookNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.IntegrationConfigurationRemovedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceCreatedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceNotPaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoicePaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceRefundedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.UnknownWebhookNotification;
import com.xgen.cloud.partners.vercel.webhook._public.svc.VercelWebhookAuthSvc;
import com.xgen.cloud.partners.vercel.webhook._public.svc.VercelWebhookNotificationRouter;
import com.xgen.cloud.payments.standalone.partners.common._public.view.response.PartnerApiError;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

@Path("/billing/partners/notification/vercel")
@Singleton
public final class VercelWebhookResource {

  private static final Logger LOG = LoggerFactory.getLogger(VercelWebhookResource.class);
  private static final Scheduler SCHEDULER = SchedulerFactory.newBoundedElastic("vercelWebhook");

  private final VercelWebhookNotificationRouter vercelWebhookNotificationRouter;
  private final VercelWebhookAuthSvc vercelWebhookAuthSvc;
  private final ObjectMapper objectMapper;

  @Inject
  public VercelWebhookResource(
      VercelWebhookNotificationRouter vercelWebhookNotificationRouter,
      VercelWebhookAuthSvc vercelWebhookAuthSvc,
      ObjectMapper objectMapper) {
    this.vercelWebhookNotificationRouter = vercelWebhookNotificationRouter;
    this.vercelWebhookAuthSvc = vercelWebhookAuthSvc;
    this.objectMapper = objectMapper;
  }

  @POST
  @Path("/webhook")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(
      summary = "Receives a notification message from Vercel webhook",
      description =
          "Endpoint to receive POSTed notification messages from partner cloud provider webhook",
      requestBody =
          @RequestBody(
              description = "Vercel webhook notification request body.",
              required = true,
              content =
                  @Content(
                      schema =
                          @Schema(
                              oneOf = {
                                IntegrationConfigurationRemovedNotification.class,
                                MarketplaceInvoiceCreatedNotification.class,
                                MarketplaceInvoiceNotPaidNotification.class,
                                MarketplaceInvoicePaidNotification.class,
                                MarketplaceInvoiceRefundedNotification.class,
                                UnknownWebhookNotification.class
                              }))),
      responses = {
        @ApiResponse(responseCode = "200", description = "No response body for successful request"),
        @ApiResponse(
            responseCode = "400",
            description = "400 Bad Request error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "401",
            description = "401 Unauthorized error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class))),
        @ApiResponse(
            responseCode = "500",
            description = "500 Internal Server error response body.",
            content = @Content(schema = @Schema(implementation = PartnerApiError.class)))
      })
  @RateLimited(
      policyPrefix = VERCEL_RATE_LIMIT_POLICY_PREFIX,
      name = VERCEL_WEBHOOK_RATE_LIMIT_NAME,
      types = {Type.SUSPENDABLE_IP})
  public Response postWebhook(
      @RequestBody String requestBody, @Context HttpServletRequest request) {
    Map<String, Object> logContext = new HashMap<>();
    logContext.put("rawRequestBody", requestBody);
    if (!vercelWebhookAuthSvc.authenticate(request, requestBody)) {
      LOG.error("Failed to authenticate Vercel webhook request. {}", entries(logContext));
      return Response.status(Response.Status.UNAUTHORIZED).build();
    }
    // Have to manually deserialize since we need the body for auth
    AbstractWebhookNotification notification;
    try {
      notification = objectMapper.readValue(requestBody, AbstractWebhookNotification.class);
      logContext.put("notification", notification);
    } catch (JsonProcessingException e) {
      LOG.error("Failed to deserialize Vercel webhook request. {}", entries(logContext), e);
      return Response.status(Response.Status.BAD_REQUEST).build();
    }

    LOG.info("Received Vercel webhook message. {}", entries(logContext));
    return vercelWebhookNotificationRouter
        .persistAndRouteToHandler(notification)
        .subscribeOn(SCHEDULER)
        .doOnError(
            e ->
                LOG.error(
                    "Error processing Vercel webhook: {} {}",
                    e.getMessage(),
                    entries(logContext),
                    e))
        .doOnSuccess(
            ignored ->
                LOG.info("Successfully processed Vercel webhook message. {}", entries(logContext)))
        .then(Mono.just(Response.ok().build()))
        .onErrorReturn(Response.serverError().build())
        .block();
  }
}
