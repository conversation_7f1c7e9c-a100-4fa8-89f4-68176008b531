package com.xgen.cloud.partners.vercel.billing._private.dao;

import com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoice200Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoiceRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoice;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoiceState;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.types.ObjectId;

public interface VercelInvoiceDocumentDao {

  Optional<VercelInvoiceDocument> findByInvoiceId(String invoiceId);

  Optional<VercelInvoiceDocument> findByInvoiceExternalId(String externalId);

  List<VercelInvoiceDocument> findByInstallationId(String installationId);

  Optional<VercelInvoiceDocument> findByRequestExternalId(String externalId);

  Stream<VercelInvoiceDocument> findAllByRequestExternalIds(Collection<String> externalId);

  Stream<VercelInvoiceDocument> findAllPendingSubmission();

  /**
   * Upserts a VercelInvoiceDocument based on the submit request. Use this method to create the
   * initial VercelInvoiceDocument.
   *
   * @param installationId the installation ID
   * @param submitInvoiceRequest the submit invoice request
   * @return the upserted document
   */
  Optional<VercelInvoiceDocument> upsert(
      String installationId, SubmitInvoiceRequest submitInvoiceRequest);

  Optional<VercelInvoiceDocument> updateResponse(
      ObjectId documentId, SubmitInvoice200Response response);

  Optional<VercelInvoiceDocument> updateInvoice(ObjectId paymentId, VercelInvoice invoice);

  /**
   * Updates the state of the embedded invoice for the given invoiceId.
   *
   * @param invoiceId Vercel Marketplace Invoice ID
   * @param state new invoice state
   * @return updated document if found
   */
  Optional<VercelInvoiceDocument> updateInvoiceState(String invoiceId, VercelInvoiceState state);

  Optional<VercelInvoiceDocument> updateForFailure(ObjectId documentId, String errorMessage);
}
