package com.xgen.cloud.partners.vercel.billing._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.inc;
import static com.mongodb.client.model.Updates.set;
import static com.mongodb.client.model.Updates.setOnInsert;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.ATTEMPTED_AT;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.COUNT_FAILURES;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.CREATED_AT;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.ERROR_MESSAGE;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.INSTALLATION_ID;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.INVOICE;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.REQUEST;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.RESPONSE;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.SUCCEEDED_AT;
import static com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument.UPDATED_AT;

import com.mongodb.client.model.FindOneAndUpdateOptions;
import com.mongodb.client.model.ReturnDocument;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.db.mongo._public.index.MongoIndex;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoice200Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoiceRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoice;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoiceState;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/** VercelInvoiceDocument DAO implementation. */
@Singleton
public final class VercelInvoiceDocumentDaoImpl extends BaseDao<VercelInvoiceDocument>
    implements VercelInvoiceDocumentDao {

  private final Clock clock;

  @Inject
  public VercelInvoiceDocumentDaoImpl(
      MongoClientContainer container, CodecRegistry codecRegistry, Clock clock) {
    super(
        container,
        VercelInvoiceDocument.DB_NAME,
        VercelInvoiceDocument.COLLECTION_NAME,
        codecRegistry);
    this.clock = clock;
  }

  @Override
  public List<MongoIndex> getIndexes() {
    return List.of(
        MongoIndex.builder().key(INSTALLATION_ID).build(),
        MongoIndex.builder().key(path(INVOICE, VercelInvoice.INVOICE_ID)).unique().sparse().build(),
        MongoIndex.builder()
            .key(path(INVOICE, VercelInvoice.EXTERNAL_ID))
            .unique()
            .sparse()
            .build(),
        MongoIndex.builder().key(path(INVOICE, VercelInvoice.INVOICE_NUMBER)).sparse().build(),
        MongoIndex.builder()
            .key(path(REQUEST, SubmitInvoiceRequest.EXTERNAL_ID))
            .unique()
            .sparse()
            .build(),
        MongoIndex.builder()
            .key(path(RESPONSE, SubmitInvoice200Response.INVOICE_ID))
            .sparse()
            .build());
  }

  @Override
  public Optional<VercelInvoiceDocument> findByInvoiceId(String invoiceId) {
    Bson filter = eq(path(INVOICE, VercelInvoice.INVOICE_ID), invoiceId);
    return Optional.ofNullable(getCollection().find(filter).first());
  }

  @Override
  public Optional<VercelInvoiceDocument> findByInvoiceExternalId(String externalId) {
    Bson filter = eq(path(INVOICE, VercelInvoice.EXTERNAL_ID), externalId);
    return Optional.ofNullable(getCollection().find(filter).first());
  }

  @Override
  public List<VercelInvoiceDocument> findByInstallationId(String installationId) {
    Bson filter = eq(INSTALLATION_ID, installationId);
    return getCollection().find(filter).into(new ArrayList<>());
  }

  @Override
  public Optional<VercelInvoiceDocument> findByRequestExternalId(String externalId) {
    Bson filter = eq(path(REQUEST, SubmitInvoiceRequest.EXTERNAL_ID), externalId);
    return Optional.ofNullable(getCollection().find(filter).first());
  }

  @Override
  public Stream<VercelInvoiceDocument> findAllByRequestExternalIds(Collection<String> externalId) {
    Bson filter = in(path(REQUEST, SubmitInvoiceRequest.EXTERNAL_ID), externalId);
    return stream(getCollection().find(filter).cursor());
  }

  @Override
  public Stream<VercelInvoiceDocument> findAllPendingSubmission() {
    Bson filter =
        and(
            eq(INVOICE, null),
            ne(REQUEST, null),
            ne(path(REQUEST, SubmitInvoiceRequest.EXTERNAL_ID), null));
    return stream(getCollection().find(filter).cursor());
  }

  @Override
  public Optional<VercelInvoiceDocument> upsert(
      String installationId, SubmitInvoiceRequest submitInvoiceRequest) {
    Bson filter =
        eq(path(REQUEST, SubmitInvoiceRequest.EXTERNAL_ID), submitInvoiceRequest.externalId());
    LocalDateTime now = LocalDateTime.now(clock);

    Bson updates =
        combine(
            setOnInsert(INSTALLATION_ID, installationId),
            set(REQUEST, submitInvoiceRequest),
            setOnInsert(ATTEMPTED_AT, null),
            set(UPDATED_AT, now),
            setOnInsert(CREATED_AT, now),
            setOnInsert(INVOICE, null),
            setOnInsert(RESPONSE, null),
            setOnInsert(SUCCEEDED_AT, null),
            setOnInsert(COUNT_FAILURES, 0),
            setOnInsert(ERROR_MESSAGE, null));

    FindOneAndUpdateOptions options =
        new FindOneAndUpdateOptions().upsert(true).returnDocument(ReturnDocument.AFTER);

    return Optional.ofNullable(getCollection().findOneAndUpdate(filter, updates, options));
  }

  @Override
  public Optional<VercelInvoiceDocument> updateResponse(
      ObjectId documentId, SubmitInvoice200Response response) {
    // Find document by _id to update with response
    Bson filter = eq(ID_FIELD, documentId);
    LocalDateTime now = LocalDateTime.now(clock);

    Bson updates =
        combine(
            set(RESPONSE, response),
            set(SUCCEEDED_AT, now),
            set(ATTEMPTED_AT, now),
            set(UPDATED_AT, now));

    FindOneAndUpdateOptions options =
        new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER);

    return Optional.ofNullable(getCollection().findOneAndUpdate(filter, updates, options));
  }

  @Override
  public Optional<VercelInvoiceDocument> updateInvoice(ObjectId paymentId, VercelInvoice invoice) {
    // Find document by request.externalId (which contains the paymentId) to update with invoice
    Bson filter = eq(path(REQUEST, SubmitInvoiceRequest.EXTERNAL_ID), paymentId.toHexString());
    LocalDateTime now = LocalDateTime.now(clock);

    Bson updates = combine(set(INVOICE, invoice), set(UPDATED_AT, now));

    FindOneAndUpdateOptions options =
        new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER);

    return Optional.ofNullable(getCollection().findOneAndUpdate(filter, updates, options));
  }

  @Override
  public Optional<VercelInvoiceDocument> updateInvoiceState(
      String invoiceId, VercelInvoiceState state) {
    // Find document by invoice.invoiceId and update only the state field
    Bson filter = eq(path(INVOICE, VercelInvoice.INVOICE_ID), invoiceId);
    LocalDateTime now = LocalDateTime.now(clock);

    Bson updates = combine(set(path(INVOICE, VercelInvoice.STATE), state), set(UPDATED_AT, now));

    FindOneAndUpdateOptions options =
        new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER);

    return Optional.ofNullable(getCollection().findOneAndUpdate(filter, updates, options));
  }

  @Override
  public Optional<VercelInvoiceDocument> updateForFailure(
      ObjectId documentId, String errorMessage) {
    // Find document by _id to update with error message
    Bson filter = eq(ID_FIELD, documentId);
    LocalDateTime now = LocalDateTime.now(clock);

    Bson updates =
        combine(
            set(ERROR_MESSAGE, errorMessage),
            inc(COUNT_FAILURES, 1),
            set(ATTEMPTED_AT, now),
            set(UPDATED_AT, now));

    FindOneAndUpdateOptions options =
        new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER);

    return Optional.ofNullable(getCollection().findOneAndUpdate(filter, updates, options));
  }
}
