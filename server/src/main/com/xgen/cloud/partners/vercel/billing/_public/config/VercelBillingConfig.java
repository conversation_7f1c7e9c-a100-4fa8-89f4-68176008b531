package com.xgen.cloud.partners.vercel.billing._public.config;

import java.time.Duration;

public interface VercelBillingConfig {

  int getMaxRetries();

  Duration getRetryMinBackoff();

  /** Secret used to encrypt the customer's installation access token before persisting it. */
  String getAccessTokenKey();

  /**
   * Set true to finalize invoices when installation is deleted.
   *
   * <p><strong>Warning</strong>: This will prevent fetching invoices via the get invoice api used
   * by the webhook notification handler.
   */
  boolean isInvoiceFinalizeEnabled();
}
