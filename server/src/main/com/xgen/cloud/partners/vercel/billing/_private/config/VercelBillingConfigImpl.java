package com.xgen.cloud.partners.vercel.billing._private.config;

import com.xgen.cloud.partners.vercel.billing._public.config.VercelBillingConfig;
import configservicesdk.com.xgen.devtools.configservicesdk.Secret;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import org.apache.commons.configuration2.ImmutableConfiguration;

@Singleton
public final class VercelBillingConfigImpl implements VercelBillingConfig {

  private final ImmutableConfiguration configuration;

  @Inject
  public VercelBillingConfigImpl(ImmutableConfiguration configuration) {
    this.configuration = configuration;
  }

  @Override
  public int getMaxRetries() {
    return configuration.getInt("mms.payments.vercel.billing.maxRetries", 5);
  }

  @Override
  public Duration getRetryMinBackoff() {
    long defaultDurationMillis = Duration.ofSeconds(2).toMillis();
    return Duration.ofMillis(
        configuration.getLong(
            "mms.payments.vercel.billing.minBackoffMillis", defaultDurationMillis));
  }

  @Override
  public String getAccessTokenKey() {
    return configuration
        .get(Secret.class, "mms.payments.vercel.billing.accessTokenKey")
        .toPlainTextValue();
  }

  @Override
  public boolean isInvoiceFinalizeEnabled() {
    return configuration.getBoolean("mms.payments.vercel.billing.invoice.finalize.enabled", true);
  }
}
