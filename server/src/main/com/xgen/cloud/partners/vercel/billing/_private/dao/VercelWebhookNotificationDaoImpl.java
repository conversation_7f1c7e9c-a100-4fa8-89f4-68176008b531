package com.xgen.cloud.partners.vercel.billing._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.exists;
import static com.mongodb.client.model.Sorts.descending;
import static com.xgen.cloud.partners.vercel.webhook._public.model.AbstractWebhookNotification.COLLECTION_NAME;
import static com.xgen.cloud.partners.vercel.webhook._public.model.AbstractWebhookNotification.DB_NAME;
import static com.xgen.cloud.partners.vercel.webhook._public.model.AbstractWebhookNotification.PAYLOAD;
import static com.xgen.cloud.partners.vercel.webhook._public.model.AbstractWebhookNotification.WEBHOOK_ID_BSON;
import static com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceCreatedPayload.EXTERNAL_INVOICE_ID;
import static com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceCreatedPayload.INSTALLATION_ID;
import static com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceCreatedPayload.INVOICE_ID;
import static net.logstash.logback.argument.StructuredArguments.e;
import static org.bson.codecs.pojo.Conventions.ANNOTATION_CONVENTION;
import static org.bson.codecs.pojo.Conventions.CLASS_AND_PROPERTY_CONVENTION;
import static org.bson.codecs.pojo.Conventions.OBJECT_ID_GENERATORS;

import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.dao.codec._public.provider.CodecRegistryProvider;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.db.mongo._public.index.MongoIndex;
import com.xgen.cloud.partners.vercel.webhook._public.model.AbstractWebhookNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.IntegrationConfigurationRemovedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceCreatedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceNotPaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoicePaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceRefundedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.UnknownWebhookNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.VercelWebhookEventType;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.Convention;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public final class VercelWebhookNotificationDaoImpl extends BaseDao<AbstractWebhookNotification>
    implements VercelWebhookNotificationDao {

  private static final Logger LOG = LoggerFactory.getLogger(VercelWebhookNotificationDaoImpl.class);

  static final List<Class<?>> WEBHOOK_NOTIFICATION_CLASSES =
      List.of(
          AbstractWebhookNotification.class,
          IntegrationConfigurationRemovedNotification.class,
          MarketplaceInvoiceCreatedNotification.class,
          MarketplaceInvoiceNotPaidNotification.class,
          MarketplaceInvoicePaidNotification.class,
          MarketplaceInvoiceRefundedNotification.class,
          UnknownWebhookNotification.class);

  /**
   * Custom conventions that exclude SET_PRIVATE_FIELDS_CONVENTION to prevent conflicts
   * with @BsonExtraElements and @JsonAnySetter annotations in UnknownWebhookPayload.
   */
  private static final List<Convention> CUSTOM_CONVENTIONS =
      List.of(CLASS_AND_PROPERTY_CONVENTION, ANNOTATION_CONVENTION, OBJECT_ID_GENERATORS);

  static final CodecRegistry CODEC_REGISTRY =
      new CodecRegistryProvider(WEBHOOK_NOTIFICATION_CLASSES, CUSTOM_CONVENTIONS, true).get();

  @Inject
  public VercelWebhookNotificationDaoImpl(MongoClientContainer container) {
    super(container, DB_NAME, COLLECTION_NAME, CODEC_REGISTRY);
    // Workaround until https://jira.mongodb.org/browse/JAVA-5565 is solved.
    WEBHOOK_NOTIFICATION_CLASSES.forEach(c -> this.getCodecRegistry().get(c));
  }

  @Override
  public List<MongoIndex> getIndexes() {
    return List.of(
        MongoIndex.builder()
            .key(WEBHOOK_ID_BSON)
            .unique()
            .partialFilterExpression(exists(WEBHOOK_ID_BSON))
            .build(),
        MongoIndex.builder()
            .key(path(PAYLOAD, INSTALLATION_ID))
            .partialFilterExpression(exists(path(PAYLOAD, INSTALLATION_ID)))
            .build(),
        MongoIndex.builder()
            .key(path(PAYLOAD, INVOICE_ID))
            .partialFilterExpression(exists(path(PAYLOAD, INVOICE_ID)))
            .build(),
        MongoIndex.builder()
            .key(path(PAYLOAD, EXTERNAL_INVOICE_ID))
            .partialFilterExpression(exists(path(PAYLOAD, EXTERNAL_INVOICE_ID)))
            .build());
  }

  @Override
  public void insertOne(AbstractWebhookNotification notification) {
    // prevent duplicate inserts
    if (findOne(WEBHOOK_ID_BSON, notification.getWebhookId()).isPresent()) {
      LOG.warn(
          "Received duplicate webhook notification."
              + " This is likely caused by a retry from Vercel due to a previous failure. {}",
          e(Map.of("notification", notification)));
      return;
    }
    insertMajority(notification);
  }

  @Override
  public <T> Optional<T> findLatestByExternalInvoiceIdAndType(
      String externalInvoiceId, VercelWebhookEventType type, Class<T> clazz) {
    return Optional.ofNullable(
        getCollection()
            .withDocumentClass(clazz)
            .find(
                and(
                    eq(path(PAYLOAD, EXTERNAL_INVOICE_ID), externalInvoiceId),
                    eq(AbstractWebhookNotification.TYPE, type)))
            .sort(descending(AbstractWebhookNotification.CREATED_AT))
            .first());
  }
}
