package com.xgen.cloud.partners.usage.report._private.svc;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.localDateOf;
import static java.util.stream.Collectors.toSet;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.constants._public.model.partners.PartnerType;
import com.xgen.cloud.partners.usage.report._public.model.MarketplaceCreditData;
import com.xgen.cloud.partners.usage.report._public.model.MarketplaceUsageCsvRow;
import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReportSummariesByPaymentMethod;
import com.xgen.cloud.partners.usage.report._public.svc.MarketplaceUsageChartSvc;
import com.xgen.cloud.partners.usage.report._public.svc.MarketplaceUsageCsvWriter;
import com.xgen.cloud.payments.paymentview._public.model.PaymentView;
import com.xgen.svc.mms.model.billing.Credit;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.core.StreamingOutput;
import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.text.DecimalFormat;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.supercsv.cellprocessor.FmtNumber;
import org.supercsv.cellprocessor.constraint.NotNull;
import org.supercsv.cellprocessor.ift.CellProcessor;
import org.supercsv.io.CsvMapWriter;
import org.supercsv.prefs.CsvPreference;

/** Writes customer-facing CSVs for marketplace usage. */
@Singleton
public final class MarketplaceUsageCsvWriterImpl implements MarketplaceUsageCsvWriter {

  private static final String[] HEADER_COLUMNS =
      new String[] {
        MarketplaceUsageCsvRow.ORGANIZATION_ID,
        MarketplaceUsageCsvRow.MARKETPLACE_CHARGE_DATE,
        MarketplaceUsageCsvRow.DAILY_CHARGES_USD,
        MarketplaceUsageCsvRow.PAYMENT_METHOD,
        MarketplaceUsageCsvRow.PARTNER_TYPE
      };

  private static final CellProcessor[] CELL_PROCESSORS =
      new CellProcessor[] {
        new NotNull(), // orgId
        new NotNull(), // marketplace charge date
        new NotNull(new FmtNumber(new DecimalFormat("0.00"))), // daily charges usd
        new NotNull(), // payment method
        new NotNull(), // partner type
      };

  private final MarketplaceUsageChartSvc marketplaceUsageChartSvc;
  private final ObjectMapper objectMapper;

  @Inject
  public MarketplaceUsageCsvWriterImpl(
      MarketplaceUsageChartSvc marketplaceUsageChartSvc, ObjectMapper objectMapper) {
    this.marketplaceUsageChartSvc = marketplaceUsageChartSvc;
    this.objectMapper = objectMapper;
  }

  @Override
  public StreamingOutput exportMarketplaceUsageCsv(Invoice invoice) {
    return outputStream -> {
      try (BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(outputStream);
          OutputStreamWriter outWriter = new OutputStreamWriter(bufferedOutputStream);
          BufferedWriter buffWriter = new BufferedWriter(outWriter);
          CsvMapWriter csvWriter =
              new CsvMapWriter(buffWriter, CsvPreference.STANDARD_PREFERENCE)) {

        csvWriter.writeHeader(HEADER_COLUMNS);
        TypeReference<Map<String, Object>> typeReference = new TypeReference<>() {};
        getCsvRowData(invoice).stream()
            // convert pojo to map
            .map(rowPojo -> objectMapper.convertValue(rowPojo, typeReference))
            // write csv row
            .forEach(
                rowMap -> {
                  try {
                    csvWriter.write(rowMap, HEADER_COLUMNS, CELL_PROCESSORS);
                  } catch (IOException e) {
                    throw new RuntimeException(e);
                  }
                });
      }
    };
  }

  private List<MarketplaceUsageCsvRow> getCsvRowData(Invoice invoice) {
    // fetch PaymentViews to use for displayName and credits
    List<PaymentView> paymentViews =
        marketplaceUsageChartSvc.getEligibleMarketplacePaymentViews(invoice);

    List<PartnerUsageReportSummariesByPaymentMethod> summariesByPaymentMethods =
        marketplaceUsageChartSvc.aggregateUsageByPaymentView(paymentViews, invoice);

    Set<Credit> credits =
        summariesByPaymentMethods.stream()
            .map(PartnerUsageReportSummariesByPaymentMethod::credit)
            .collect(toSet());

    List<MarketplaceCreditData> marketplaceCreditDataList =
        marketplaceUsageChartSvc.buildMarketplaceCreditData(
            credits,
            localDateOf(invoice.getEndDate()),
            paymentViews,
            // Pass Map.of() as reportedAmountCents is not needed, defaults to 0
            Map.of());

    // Build the map from filterField -> displayName (handles duplicates with suffixes)
    Map<String, String> filterFieldToDisplayName =
        marketplaceCreditDataList.stream()
            .collect(
                Collectors.toMap(
                    MarketplaceCreditData::filterField, MarketplaceCreditData::displayName));

    return summariesByPaymentMethods.stream()
        .flatMap(
            summariesByPayment -> {
              Credit credit = summariesByPayment.credit();
              PartnerType partnerType = credit.getPartnerType();

              // Lookup the potentially suffixed display name from the map
              String displayName = summariesByPayment.displayName();
              String filterField =
                  marketplaceUsageChartSvc.buildFilterFieldValue(credit.getId(), displayName);
              String finalDisplayName =
                  filterFieldToDisplayName.getOrDefault(filterField, displayName);

              return summariesByPayment.summaries().stream()
                  .map(
                      summary ->
                          MarketplaceUsageCsvRow.builder()
                              .orgId(invoice.getOrgId())
                              .partnerType(partnerType)
                              .paymentMethodDisplayName(finalDisplayName)
                              .marketplaceChargeDate(summary.reportedStartDate())
                              .dailyChargesUsd(summary.reportedAmountCents() / 100.0)
                              .build());
            })
        .sorted(Comparator.comparing(MarketplaceUsageCsvRow::marketplaceChargeDate))
        .toList();
  }
}
