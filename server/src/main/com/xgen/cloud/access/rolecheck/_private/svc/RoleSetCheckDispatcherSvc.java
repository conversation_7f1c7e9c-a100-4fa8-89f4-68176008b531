package com.xgen.cloud.access.rolecheck._private.svc;

import static java.util.Map.entry;
import static java.util.Map.ofEntries;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._public.model.AppUser;
import jakarta.inject.Inject;
import java.util.Map;
import java.util.function.BiPredicate;
import java.util.function.Predicate;
import org.bson.types.ObjectId;

/**
 * This service helps {@link com.xgen.cloud.access.rolecheck._public.svc.RoleSetSvc} make RoleSet
 * checks based on the type of role check (global, org, or group) and any specific authorization
 * strategy needed (role-based or the implementation currently bound to {@link AuthzSvc}). Do not
 * use outside of {@link com.xgen.cloud.access.rolecheck._public.svc.RoleSetSvc}.
 */
public class RoleSetCheckDispatcherSvc {
  private final Map<RoleSet, GlobalRoleSetChecker> mapGlobalRoleSetChecks;
  private final Map<RoleSet, OrgRoleSetChecker> mapOrgRoleSetChecks;
  private final Map<RoleSet, GroupRoleSetChecker> mapProjectRoleSetChecks;

  @Inject
  public RoleSetCheckDispatcherSvc(AuthzSvc authzSvc, RoleBasedAuthzSvc roleBasedAuthzSvc) {
    this.mapGlobalRoleSetChecks =
        Map.<RoleSet, GlobalRoleSetChecker>ofEntries(
            entry(
                RoleSet.GLOBAL_ATLAS_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasAdmin, authzSvc::isGlobalAtlasAdmin)),
            entry(
                RoleSet.GLOBAL_ATLAS_ENGINEERING_OPERATOR,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasEngineeringOperator,
                    authzSvc::isGlobalAtlasEngineeringOperator)),
            entry(
                RoleSet.GLOBAL_ATLAS_OPERATOR,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasOperator, authzSvc::isGlobalAtlasOperator)),
            entry(
                RoleSet.GLOBAL_ATLAS_AUTOMATED_OPERATOR,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasAutomatedOperator,
                    authzSvc::isGlobalAtlasAutomatedOperator)),
            entry(
                RoleSet.GLOBAL_ATLAS_TSE,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasTse, authzSvc::isGlobalAtlasTse)),
            entry(
                RoleSet.GLOBAL_ATLAS_LOCALIZED_SSH_REQUEST_ACCESS,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasLocalizedSshRequestAccess,
                    authzSvc::isGlobalAtlasLocalizedSshRequestAccess)),
            entry(
                RoleSet.GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasSoftwareVersionRolloutAdmin,
                    authzSvc::isGlobalAtlasSoftwareVersionRolloutAdmin)),
            entry(
                RoleSet.GLOBAL_ATLAS_CUSTOM_BUILD_MANAGER,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasCustomBuildManager,
                    authzSvc::isGlobalAtlasCustomBuildManager)),
            entry(
                RoleSet.GLOBAL_ATLAS_FLEET_ATTRIBUTES_READER,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasFleetAttributesReader,
                    authzSvc::isGlobalAtlasFleetAttributesReader)),
            entry(
                RoleSet.GLOBAL_ATLAS_DATA_VALIDATOR,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasDataValidator,
                    authzSvc::isGlobalAtlasDataValidator)),
            entry(
                RoleSet.GLOBAL_AUTOMATION_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAutomationAdmin, authzSvc::isGlobalAutomationAdmin)),
            entry(
                RoleSet.GLOBAL_BACKUP_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBackupAdmin, authzSvc::isGlobalBackupAdmin)),
            entry(
                RoleSet.GLOBAL_BACKUP_COMPLIANCE_POLICY_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBackupCompliancePolicyAdmin,
                    authzSvc::isGlobalBackupCompliancePolicyAdmin)),
            entry(
                RoleSet.GLOBAL_METRICS_INTERNAL_USER,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalMetricsInternalUser,
                    authzSvc::isGlobalMetricsInternalUser)),
            entry(
                RoleSet.GLOBAL_SERVERLESS_LIVENESS_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalServerlessLivenessAdmin,
                    authzSvc::isGlobalServerlessLivenessAdmin)),
            entry(
                RoleSet.GLOBAL_METERING_USER,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalMeteringUser, authzSvc::isGlobalMeteringUser)),
            entry(
                RoleSet.GLOBAL_MONITORING_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalMonitoringAdmin, authzSvc::isGlobalMonitoringAdmin)),
            entry(
                RoleSet.GLOBAL_OWNER,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalOwner, authzSvc::isGlobalOwner)),
            entry(
                RoleSet.GLOBAL_READ_ONLY,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalReadOnly, authzSvc::isGlobalReadOnly)),
            entry(
                RoleSet.GLOBAL_USER_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalUserAdmin, authzSvc::isGlobalUserAdmin)),
            entry(
                RoleSet.GLOBAL_USER_READ_ONLY,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalUserReadOnly, authzSvc::isGlobalUserReadOnly)),
            entry(
                RoleSet.GLOBAL_ACCOUNT_SUSPENSION_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAccountSuspensionAdmin,
                    authzSvc::isGlobalAccountSuspensionAdmin)),
            entry(
                RoleSet.GLOBAL_BILLING_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBillingAdmin, authzSvc::isGlobalBillingAdmin)),
            entry(
                RoleSet.GLOBAL_BILLING_READ_ONLY,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBillingReadOnly, authzSvc::isGlobalBillingReadOnly)),
            entry(
                RoleSet.GLOBAL_BILLING_HELP_TOOLING,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBillingHelpTooling,
                    authzSvc::isGlobalBillingHelpTooling)),
            entry(
                RoleSet.GLOBAL_BILLING_FEATURE_TEAM_USER,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalFeatureTeamBillingUser,
                    authzSvc::isGlobalFeatureTeamBillingUser)),
            entry(
                RoleSet.GLOBAL_METERING_FEATURE_TEAM_USER,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalFeatureTeamMeteringUser,
                    authzSvc::isGlobalFeatureTeamMeteringUser)),
            entry(
                RoleSet.GLOBAL_CHARTS_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalChartsAdmin, authzSvc::isGlobalChartsAdmin)),
            entry(
                RoleSet.GLOBAL_LEGAL_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalLegalAdmin, authzSvc::isGlobalLegalAdmin)),
            entry(
                RoleSet.GLOBAL_FEATURE_FLAG_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalFeatureFlagAdmin,
                    authzSvc::isGlobalFeatureFlagAdmin)),
            entry(
                RoleSet.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAtlasCapacityReservationAdmin,
                    authzSvc::isGlobalAtlasCapacityReservationAdmin)),
            entry(
                RoleSet.GLOBAL_APP_SETTING_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAppSettingAdmin, authzSvc::isGlobalAppSettingAdmin)),
            entry(
                RoleSet.GLOBAL_CRON_JOBS_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalCronJobsAdmin, authzSvc::isGlobalCronJobsAdmin)),
            entry(
                RoleSet.GLOBAL_SECURITY_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalSecurityAdmin, authzSvc::isGlobalSecurityAdmin)),
            entry(
                RoleSet.GLOBAL_BAAS_INTERNAL_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBaasInternalAdmin,
                    authzSvc::isGlobalBaasInternalAdmin)),
            entry(
                RoleSet.GLOBAL_ADFA_ADMIN_WRITE,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAdfaAdminWrite, authzSvc::isGlobalAdfaAdminWrite)),
            entry(
                RoleSet.GLOBAL_ADFA_ADMIN_READ,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAdfaAdminRead, authzSvc::isGlobalAdfaAdminRead)),
            entry(
                RoleSet.GLOBAL_ADFA_ADMIN_SENSITIVE,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAdfaAdminSensitive,
                    authzSvc::isGlobalAdfaAdminSensitive)),
            entry(
                RoleSet.GLOBAL_CCS_INTERNAL_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalCcsInternalAdmin,
                    authzSvc::isGlobalCcsInternalAdmin)),
            entry(
                RoleSet.GLOBAL_BAAS_X509_CERTIFICATE,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBaasX509CertificateAdmin,
                    authzSvc::isGlobalBaasX509CertificateAdmin)),
            entry(
                RoleSet.GLOBAL_PROACTIVE_SUPPORT_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalProactiveSupportAdmin,
                    authzSvc::isGlobalProactiveSupportAdmin)),
            entry(
                RoleSet.GLOBAL_QUERY_ENGINE_INTERNAL_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalDataLakeAdmin, authzSvc::isGlobalDataLakeAdmin)),
            entry(
                RoleSet.GLOBAL_INFRASTRUCTURE_INTERNAL_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalInfrastructureAdmin,
                    authzSvc::isGlobalInfrastructureAdmin)),
            entry(
                RoleSet.GLOBAL_SALESFORCE_ACCOUNT_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalSalesforceAccountAdmin,
                    authzSvc::isGlobalSalesforceAccountAdmin)),
            entry(
                RoleSet.GLOBAL_SALESFORCE_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalSalesforceAdmin, authzSvc::isGlobalSalesforceAdmin)),
            entry(
                RoleSet.GLOBAL_SALESFORCE_READ_ONLY,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalSalesforceReadOnly,
                    authzSvc::isGlobalSalesforceReadOnly)),
            entry(
                RoleSet.GLOBAL_BAAS_FEATURE_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBaasFeatureAdmin,
                    authzSvc::isGlobalBaasFeatureAdmin)),
            entry(
                RoleSet.GLOBAL_BAAS_SUPPORT,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBaasSupport, authzSvc::isGlobalBaasSupport)),
            entry(
                RoleSet.GLOBAL_EVENT_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalEventAdmin, authzSvc::isGlobalEventAdmin)),
            entry(
                RoleSet.PII_AUDITABLE,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isPiiAuditable, authzSvc::isPiiAuditable)),
            entry(
                RoleSet.GLOBAL_EXPERIMENT_ASSIGNMENT_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalExperimentAssignmentAdmin,
                    authzSvc::isGlobalExperimentAssignmentAdmin)),
            entry(
                RoleSet.GLOBAL_EXPERIMENT_OVERRIDE_ASSIGNMENT_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalExperimentOverrideAssignmentAdmin,
                    authzSvc::isGlobalExperimentOverrideAssignmentAdmin)),
            entry(
                RoleSet.GLOBAL_APP_SERVICES_CLUSTER_DEBUG_DATA_ACCESS,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAppServicesDebugUser,
                    authzSvc::isGlobalAppServicesDebugUser)),
            entry(
                RoleSet.GLOBAL_PARTNER_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalPartnerAdmin, authzSvc::isGlobalPartnerAdmin)),
            entry(
                RoleSet.GLOBAL_CRASH_LOG_ANALYST,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalCrashLogAnalyst, authzSvc::isGlobalCrashLogAnalyst)),
            entry(
                RoleSet.GLOBAL_ADMIN_READ_ONLY,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAdminReadOnly, authzSvc::isGlobalAdminReadOnly)),
            entry(
                RoleSet.GLOBAL_ORG_READ_ONLY,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalOrgReadOnly, authzSvc::isGlobalOrgReadOnly)),
            entry(
                RoleSet.GLOBAL_APP_LIMIT_OVERRIDE_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAppLimitOverrideAdmin,
                    authzSvc::isGlobalAppLimitOverrideAdmin)),
            entry(
                RoleSet.GLOBAL_EMPLOYEE_MFA_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalEmployeeMfaAdmin,
                    authzSvc::isGlobalEmployeeMfaAdmin)),
            entry(
                RoleSet.GLOBAL_MFA_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalMfaAdmin, authzSvc::isGlobalMfaAdmin)),
            entry(
                RoleSet.GLOBAL_VERSION_MANAGER_READ_ONLY,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalVersionManagerReadOnly,
                    authzSvc::isGlobalVersionManagerReadOnly)),
            entry(
                RoleSet.GLOBAL_EOL_EXTENSION_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalEOLExtensionAdmin,
                    authzSvc::isGlobalEOLExtensionAdmin)),
            entry(
                RoleSet.GLOBAL_ANIS_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAnisAdmin, authzSvc::isGlobalAnisAdmin)),
            entry(
                RoleSet.GLOBAL_SSH_BASTION,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalSshBastion, authzSvc::isGlobalSshBastion)),
            entry(
                RoleSet.GLOBAL_SERVICE_ACCOUNT_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalServiceAccountAdmin,
                    authzSvc::isGlobalServiceAccountAdmin)),
            entry(
                RoleSet.GLOBAL_INTERNAL_TOOLS_READ_ONLY,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalInternalToolsReadOnly,
                    authzSvc::isGlobalInternalToolsReadOnly)),
            entry(
                RoleSet.GLOBAL_UNIFORM_FRONTEND_ENVOY,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalUniformFrontendEnvoy,
                    authzSvc::isGlobalUniformFrontendEnvoy)),
            entry(
                RoleSet.GLOBAL_BAAS_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalBaasAdmin, authzSvc::isGlobalBaasAdmin)),
            entry(
                RoleSet.GLOBAL_SYSTEM_ALERTS_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isSystemAlertsAdmin, authzSvc::isSystemAlertsAdmin)),
            entry(
                RoleSet.GLOBAL_GLOBAL_ALERTS_ADMIN,
                new GlobalRoleSetChecker(
                    roleBasedAuthzSvc::isGlobalAlertsAdmin, authzSvc::isSystemAlertsAdmin)));
    mapOrgRoleSetChecks =
        ofEntries(
            entry(
                RoleSet.ORG_MEMBER,
                new OrgRoleSetChecker(roleBasedAuthzSvc::isOrgMember, authzSvc::isOrgMember)),
            entry(
                RoleSet.ORG_ANY_ADMIN,
                new OrgRoleSetChecker(roleBasedAuthzSvc::isOrgAnyAdmin, authzSvc::isOrgAnyAdmin)),
            entry(
                RoleSet.ORG_READ_ONLY,
                new OrgRoleSetChecker(roleBasedAuthzSvc::isOrgReadOnly, authzSvc::isOrgReadOnly)),
            entry(
                RoleSet.ORG_BILLING_ADMIN,
                new OrgRoleSetChecker(
                    roleBasedAuthzSvc::isOrgBillingAdmin, authzSvc::isOrgBillingAdmin)),
            entry(
                RoleSet.ORG_BILLING_READ_ONLY,
                new OrgRoleSetChecker(
                    roleBasedAuthzSvc::isOrgBillingReadOnly, authzSvc::isOrgBillingReadOnly)),
            entry(
                RoleSet.ORG_STREAM_PROCESSING_ADMIN,
                new OrgRoleSetChecker(
                    roleBasedAuthzSvc::isOrgStreamProcessingAdmin,
                    authzSvc::isOrgStreamProcessingAdmin)),
            entry(
                RoleSet.ORG_GROUP_CREATOR,
                new OrgRoleSetChecker(
                    roleBasedAuthzSvc::isOrgProjectCreator, authzSvc::isOrgProjectCreator)),
            entry(
                RoleSet.ORG_USER_ADMIN,
                new OrgRoleSetChecker(roleBasedAuthzSvc::isOrgUserAdmin, authzSvc::isOrgUserAdmin)),
            entry(
                RoleSet.ORG_TEAM_MEMBERS_ADMIN,
                new OrgRoleSetChecker(
                    roleBasedAuthzSvc::isOrgTeamMembersAdmin, authzSvc::isOrgTeamMembersAdmin)),
            entry(
                RoleSet.ORG_OWNER,
                new OrgRoleSetChecker(roleBasedAuthzSvc::isOrgOwner, authzSvc::isOrgOwner)),
            entry(
                RoleSet.EXPLICIT_ORG_OWNER,
                new OrgRoleSetChecker(
                    roleBasedAuthzSvc::isExplicitOrgOwner, authzSvc::isExplicitOrgOwner)));

    mapProjectRoleSetChecks =
        ofEntries(
            entry(
                RoleSet.GROUP_READ_ONLY,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectReadOnly, authzSvc::isProjectReadOnly)),
            entry(
                RoleSet.GROUP_CLUSTER_MANAGER,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectClusterManager, authzSvc::isProjectClusterManager)),
            entry(
                RoleSet.GROUP_ATLAS_ADMIN,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectAtlasAdmin, authzSvc::isProjectAtlasAdmin)),
            entry(
                RoleSet.GROUP_AUTOMATION_ADMIN,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectAutomationAdmin,
                    authzSvc::isProjectAutomationAdmin)),
            entry(
                RoleSet.GROUP_BACKUP_ADMIN,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectBackupAdmin, authzSvc::isProjectBackupAdmin)),
            entry(
                RoleSet.GROUP_MONITORING_MANAGER,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectMonitoringManager,
                    authzSvc::isProjectMonitoringManager)),
            entry(
                RoleSet.GROUP_MONITORING_ADMIN,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectMonitoringAdmin,
                    authzSvc::isProjectMonitoringAdmin)),
            entry(
                RoleSet.GROUP_OWNER,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectOwner, authzSvc::isProjectOwner)),
            entry(
                RoleSet.GROUP_USER_ADMIN,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectUserAdmin, authzSvc::isProjectUserAdmin)),
            entry(
                RoleSet.GROUP_BILLING_ADMIN,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectBillingAdmin, authzSvc::isProjectBillingAdmin)),
            entry(
                RoleSet.GROUP_DATA_ACCESS_ADMIN,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectDataAccessAdmin,
                    authzSvc::isProjectDataAccessAdmin)),
            entry(
                RoleSet.GROUP_DATA_ACCESS_WRITE,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectDataAccessWrite,
                    authzSvc::isProjectDataAccessWrite)),
            entry(
                RoleSet.GROUP_DATA_ACCESS_READ_ONLY,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectDataAccessReadOnly,
                    authzSvc::isProjectDataAccessReadOnly)),
            entry(
                RoleSet.GROUP_DATA_ACCESS_ANY,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::hasProjectDataAccess, authzSvc::hasProjectDataAccess)),
            entry(
                RoleSet.GROUP_CHARTS_ADMIN,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectChartsAdmin, authzSvc::isProjectChartsAdmin)),
            entry(
                RoleSet.GROUP_EXPLICIT_ACCESS,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::hasProjectExplicitAccess,
                    authzSvc::hasProjectExplicitAccess)),
            entry(
                RoleSet.GROUP_SEARCH_INDEX_EDITOR,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectSearchIndexEditor,
                    authzSvc::isProjectSearchIndexEditor)),
            entry(
                RoleSet.GROUP_STREAM_PROCESSING_OWNER,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectStreamProcessingOwner,
                    authzSvc::isProjectStreamProcessingOwner)),
            entry(
                RoleSet.GROUP_BACKUP_MANAGER,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectBackupManager, authzSvc::isProjectBackupManager)),
            entry(
                RoleSet.GROUP_OBSERVABILITY_VIEWER,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectObservabilityViewer,
                    authzSvc::isProjectObservabilityViewer)),
            entry(
                RoleSet.GROUP_DATABASE_ACCESS_ADMIN,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::isProjectDatabaseAccessAdmin,
                    authzSvc::isProjectDatabaseAccessAdmin)),
            entry(
                RoleSet.PII_VIEW_ACCESS,
                new GroupRoleSetChecker(
                    roleBasedAuthzSvc::hasPiiViewAccess, authzSvc::hasPiiViewAccess)));
  }

  public boolean doGlobalRoleSetCheck(RoleSet roleSet, AppUser user) {
    GlobalRoleSetChecker checker = mapGlobalRoleSetChecks.get(roleSet);
    if (checker == null) {
      throw new IllegalArgumentException("No mapped role check for RoleSet." + roleSet.name());
    }
    return checker.doRoleSetCheckUsingAuthzSvc.test(user);
  }

  public boolean doGlobalRoleSetCheckRbac(RoleSet roleSet, AppUser user) {
    GlobalRoleSetChecker checker = mapGlobalRoleSetChecks.get(roleSet);
    if (checker == null) {
      throw new IllegalArgumentException("No mapped role check for RoleSet." + roleSet.name());
    }
    return checker.doRoleSetCheckUsingRoleBasedAuthzSvc.test(user);
  }

  public boolean doOrgRoleSetCheck(RoleSet roleSet, AppUser user, ObjectId orgId) {
    OrgRoleSetChecker checker = mapOrgRoleSetChecks.get(roleSet);
    if (checker == null) {
      throw new IllegalArgumentException("No mapped role check for RoleSet." + roleSet.name());
    }
    return checker.doRoleSetCheckUsingAuthzSvc.test(user, orgId);
  }

  public boolean doOrgRoleSetCheckRbac(RoleSet roleSet, AppUser user, ObjectId orgId) {
    OrgRoleSetChecker checker = mapOrgRoleSetChecks.get(roleSet);
    if (checker == null) {
      throw new IllegalArgumentException("No mapped role check for RoleSet." + roleSet.name());
    }
    return checker.doRoleSetCheckUsingRoleBasedAuthzSvc.test(user, orgId);
  }

  public boolean doGroupRoleSetCheck(RoleSet roleSet, AppUser user, Group group) {
    GroupRoleSetChecker checker = mapProjectRoleSetChecks.get(roleSet);
    if (checker == null) {
      throw new IllegalArgumentException("No mapped role check for RoleSet." + roleSet.name());
    }
    return checker.doRoleSetCheckUsingAuthzSvc.test(user, group);
  }

  public boolean doGroupRoleSetCheckRbac(RoleSet roleSet, AppUser user, Group group) {
    GroupRoleSetChecker checker = mapProjectRoleSetChecks.get(roleSet);
    if (checker == null) {
      throw new IllegalArgumentException("No mapped role check for RoleSet." + roleSet.name());
    }
    return checker.doRoleSetCheckUsingRoleBasedAuthzSvc.test(user, group);
  }

  record GlobalRoleSetChecker(
      Predicate<AppUser> doRoleSetCheckUsingRoleBasedAuthzSvc,
      Predicate<AppUser> doRoleSetCheckUsingAuthzSvc) {}

  record OrgRoleSetChecker(
      BiPredicate<AppUser, ObjectId> doRoleSetCheckUsingRoleBasedAuthzSvc,
      BiPredicate<AppUser, ObjectId> doRoleSetCheckUsingAuthzSvc) {}

  record GroupRoleSetChecker(
      BiPredicate<AppUser, Group> doRoleSetCheckUsingRoleBasedAuthzSvc,
      BiPredicate<AppUser, Group> doRoleSetCheckUsingAuthzSvc) {}
}
