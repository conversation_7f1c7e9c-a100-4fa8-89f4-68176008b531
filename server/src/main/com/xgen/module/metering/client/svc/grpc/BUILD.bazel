load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "grpc",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main/com/xgen/module/metering/client:__pkg__",
        "//server/src/main/com/xgen/module/metering/module:__pkg__",
        "//server/src/main/com/xgen/module/metering/server/res:__pkg__",
        "//server/src/test/com/xgen/module/metering/client/svc/grpc:__pkg__",
        "//server/src/unit/com/xgen/module/metering/client/svc/grpc:__pkg__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/common",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/authn",
        "//server/src/main/com/xgen/cloud/services/core/client",
        "//server/src/main/com/xgen/module/metering/client/svc",
        "//server/src/main/com/xgen/module/metering/client/svc/grpc/mapper",
        "//server/src/main/com/xgen/module/metering/common/exception",
        "//server/src/main/com/xgen/module/metering/common/model",
        "//server/src/main/com/xgen/module/metering/common/utils",
        "//systems/common/proto/com/xgen/common/bson/v1:com_xgen_common_bson_v1_java_library",  #keep
        "//systems/metering:meter_usages_grpc_v1",  #keep
        "//systems/metering:meter_usages_java_v1",  #keep
        "//third_party:guava",
        "@com_google_protobuf//java/core",
        "@io_grpc_grpc_java//api",
        "@io_grpc_grpc_java//protobuf",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:net_logstash_logback_logstash_logback_encoder",
        "@maven//:org_mongodb_bson",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
