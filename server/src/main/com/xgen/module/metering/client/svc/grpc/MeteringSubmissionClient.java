package com.xgen.module.metering.client.svc.grpc;

import static com.xgen.module.metering.client.svc.MeteringServiceClient.BATCH_SIZE_PROP_V1;
import static com.xgen.module.metering.client.svc.MeteringServiceClient.BATCH_SIZE_PROP_V2;
import static com.xgen.module.metering.client.svc.MeteringServiceClient.DEFAULT_BATCH_SIZE_V1;
import static com.xgen.module.metering.client.svc.MeteringServiceClient.DEFAULT_BATCH_SIZE_V2;
import static com.xgen.module.metering.client.svc.MeteringServiceClient.METER_REPORT_SVC_INGESTION_SIZE;
import static com.xgen.module.metering.client.svc.MeteringServiceClient.METER_REPORT_SVC_RUNTIME;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.xgen.cloud.billingplatform.common._public.model.Result;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.authn._public.svc.AuthnOAuthClient;
import com.xgen.cloud.services.core.client._public.impl.AbstractGrpcClient;
import com.xgen.metering.meterusages.grpc.v1.MeterSubmissionServiceGrpc;
import com.xgen.metering.meterusages.grpc.v1.MeterSubmissionServiceGrpc.MeterSubmissionServiceBlockingStub;
import com.xgen.metering.meterusages.v1.MeterSubmissionError;
import com.xgen.metering.meterusages.v1.SubmissionError;
import com.xgen.metering.meterusages.v1.SubmitMeterUsageRequest;
import com.xgen.metering.meterusages.v1.SubmitMeterUsageResponse;
import com.xgen.module.metering.client.svc.RetryableSupplier;
import com.xgen.module.metering.client.svc.RetryableSupplier.RetryablePolicy;
import com.xgen.module.metering.client.svc.grpc.mapper.MeterUsageMapper;
import com.xgen.module.metering.common.exception.MeterErrorCode;
import com.xgen.module.metering.common.exception.MeterSvcException;
import com.xgen.module.metering.common.model.MeterUsage;
import io.grpc.Channel;
import io.grpc.Status;
import io.grpc.Status.Code;
import io.grpc.StatusRuntimeException;
import io.grpc.protobuf.StatusProto;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class MeteringSubmissionClient
    extends AbstractGrpcClient<MeterSubmissionServiceBlockingStub> {

  private static final Logger LOG = LoggerFactory.getLogger(MeteringSubmissionClient.class);

  private static final String METER_USAGES_SERVICE_NAME = "meterusages";
  private static final String PROM_CLIENT_TYPE_GRPC = "grpc";

  public static final Set<Code> RETRYABLE_CODES = Set.of(Code.UNKNOWN, Code.UNAVAILABLE);

  private static final RetryablePolicy<StatusRuntimeException> RETRYABLE_EXCEPTION_POLICY =
      exception -> RETRYABLE_CODES.contains(exception.getStatus().getCode());
  public static final Duration TIMEOUT_DURATION = Duration.ofSeconds(60);

  private final AppSettings appSettings;
  private final MeterUsageMapper meterUsageMapper;

  @Inject
  public MeteringSubmissionClient(
      AppSettings appSettings, MeterUsageMapper meterUsageMapper, AuthnOAuthClient authnClient) {
    super(METER_USAGES_SERVICE_NAME, authnClient, appSettings);
    this.appSettings = appSettings;
    this.meterUsageMapper = meterUsageMapper;
  }

  @Override
  protected MeterSubmissionServiceBlockingStub createStub(Channel channel) {
    return MeterSubmissionServiceGrpc.newBlockingStub(channel);
  }

  public void submitMeterUsageV1(final List<MeterUsage> meterUsages, final String ingestionJobName)
      throws MeterSvcException {
    submitMeterUsage(
        meterUsages,
        false,
        ingestionJobName,
        appSettings.getIntProp(BATCH_SIZE_PROP_V1, DEFAULT_BATCH_SIZE_V1));
  }

  public void submitMeterUsageV2(final List<MeterUsage> meterUsages, final String ingestionJobName)
      throws MeterSvcException {
    submitMeterUsage(
        meterUsages,
        true,
        ingestionJobName,
        appSettings.getIntProp(BATCH_SIZE_PROP_V2, DEFAULT_BATCH_SIZE_V2));
  }

  private void submitMeterUsage(
      List<MeterUsage> meterUsages, boolean sessionBulkSave, String ingestionJobName, int batchSize)
      throws MeterSvcException {
    METER_REPORT_SVC_INGESTION_SIZE
        .labels(ingestionJobName, PROM_CLIENT_TYPE_GRPC)
        .set(meterUsages.size());
    for (final List<MeterUsage> featureMetricsPartition : Lists.partition(meterUsages, batchSize)) {

      Result<SubmitMeterUsageResponse, StatusRuntimeException> result =
          METER_REPORT_SVC_RUNTIME
              .labels(ingestionJobName, PROM_CLIENT_TYPE_GRPC)
              .time(() -> sendMeterUsages(featureMetricsPartition, sessionBulkSave));

      processSubmissionResult(result);
    }
  }

  private void processSubmissionResult(
      Result<SubmitMeterUsageResponse, StatusRuntimeException> result) throws MeterSvcException {
    if (result.hasError()) {

      var statusRuntimeException = result.getError();
      var status = statusRuntimeException.getStatus();
      var statusProto =
          StatusProto.fromStatusAndTrailers(status, statusRuntimeException.getTrailers());

      var meterSubmissionError =
          statusProto.getDetailsList().stream()
              .dropWhile(any -> !any.is(MeterSubmissionError.class))
              .findFirst()
              .flatMap(MeteringSubmissionClient::unpackMeterSubmissionError);

      if (meterSubmissionError.isPresent()) {
        throw new MeterSvcException(
            mapErrorCode(meterSubmissionError.get().getErrorCode()),
            status.asException(),
            meterSubmissionError.get().getDetailsList().toArray());
      } else if (status.getCode() == Code.UNAVAILABLE) {
        LOG.error("Meter submission service unavailable", status.asException());
        throw new MeterSvcException(MeterErrorCode.UNAVAILABLE, status.asException());

      } else {
        LOG.error("Unknown new meter submission client error", status.asException());
        throw new MeterSvcException(MeterErrorCode.UNKNOWN, status.asException());
      }
    }
  }

  private static Optional<MeterSubmissionError> unpackMeterSubmissionError(Any any) {
    if (any.is(MeterSubmissionError.class)) {
      try {
        return Optional.of(any.unpack(MeterSubmissionError.class));
      } catch (InvalidProtocolBufferException e) {
        LOG.error("Failed to unpack meter submission error", e);
      }
    }
    return Optional.empty();
  }

  private MeterErrorCode mapErrorCode(SubmissionError errorCode) {
    return switch (errorCode) {
      case SUBMISSION_ERROR_VALIDATION_FAILURE -> MeterErrorCode.DESERIALIZING_VALIDATION_FAILURE;
      case SUBMISSION_ERROR_CONFLICTS_WITH_PREVIOUS_SAVED ->
          MeterErrorCode.CONFLICTS_WITH_PREVIOUS_SAVED;
      case SUBMISSION_ERROR_CONFLICTS_WITH_ROLLBACK -> MeterErrorCode.CONFLICTS_WITH_ROLLBACK;
      case SUBMISSION_ERROR_USAGE_START_TIME_NOT_MATCHING_FREQUENCY ->
          MeterErrorCode.USAGE_START_TIME_NOT_MATCHING_FREQUENCY;
      case SUBMISSION_ERROR_USAGE_END_TIME_NOT_MATCHING_FREQUENCY ->
          MeterErrorCode.USAGE_END_TIME_NOT_MATCHING_FREQUENCY;
      case SUBMISSION_ERROR_USAGE_END_TIME_BEFORE_START_TIME ->
          MeterErrorCode.USAGE_END_TIME_BEFORE_START_TIME;
      case SUBMISSION_ERROR_USAGE_REPORTED_TIME_BEFORE_END_TIME ->
          MeterErrorCode.USAGE_REPORTED_TIME_BEFORE_END_TIME;
      case SUBMISSION_ERROR_USAGE_START_TIME_END_TIME_NOT_ON_SAME_DAY ->
          MeterErrorCode.USAGE_START_TIME_END_TIME_NOT_ON_SAME_DAY;
      case SUBMISSION_ERROR_USAGE_START_TIME_END_TIME_ARE_SAME ->
          MeterErrorCode.USAGE_START_TIME_END_TIME_ARE_SAME;
      case SUBMISSION_ERROR_USAGE_START_TIME_TOO_FAR_IN_PAST ->
          MeterErrorCode.USAGE_START_TIME_TOO_FAR_IN_PAST;
      case SUBMISSION_ERROR_INCOMPATIBLE_USAGE_UNIT -> MeterErrorCode.INCOMPATIBLE_USAGE_UNIT;
      case SUBMISSION_ERROR_UNKNOWN -> MeterErrorCode.UNKNOWN;
      case SUBMISSION_ERROR_UNSPECIFIED, UNRECOGNIZED -> {
        LOG.warn("Unknown meter submission error received {}", kv("errorCode", errorCode));
        yield MeterErrorCode.UNKNOWN;
      }
    };
  }

  private Result<SubmitMeterUsageResponse, StatusRuntimeException> sendMeterUsages(
      List<MeterUsage> meterUsages, boolean sessionBulkSave) {

    var requestResult = mapRequest(meterUsages, sessionBulkSave);
    if (requestResult.hasError()) {
      return Result.error(requestResult.getError());
    } else {
      return new RetryableSupplier<>(
              () -> {
                try {
                  SubmitMeterUsageResponse submitMeterUsageResponse =
                      callSubmitMeterUsageGrpc(requestResult.getValue());
                  return Result.of(submitMeterUsageResponse);
                } catch (StatusRuntimeException t) {
                  return Result.error(t);
                } catch (Throwable t) {
                  return Result.error(new StatusRuntimeException(Status.UNKNOWN.withCause(t)));
                }
              },
              RETRYABLE_EXCEPTION_POLICY)
          .get();
    }
  }

  private Result<SubmitMeterUsageRequest, StatusRuntimeException> mapRequest(
      List<MeterUsage> meterUsages, boolean sessionBulkSave) {
    try {
      var builder = SubmitMeterUsageRequest.newBuilder();
      builder.setSessionBulkSave(sessionBulkSave);
      for (MeterUsage meterUsage : meterUsages) {
        builder.addMeterUsages(meterUsageMapper.mapToSubmitMeterUsage(meterUsage));
      }
      return Result.of(builder.build());
    } catch (Throwable t) {
      return Result.error(new StatusRuntimeException(Status.UNKNOWN.withCause(t)));
    }
  }

  @VisibleForTesting
  protected SubmitMeterUsageResponse callSubmitMeterUsageGrpc(
      SubmitMeterUsageRequest submitMeterUsageRequest) {
    return getStub(TIMEOUT_DURATION).submitMeterUsage(submitMeterUsageRequest);
  }
}
