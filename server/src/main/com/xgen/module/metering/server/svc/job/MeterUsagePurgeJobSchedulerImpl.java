package com.xgen.module.metering.server.svc.job;

import static com.xgen.module.metering.server.svc.job.MeterUsagePurgeJob.CUTOFF_WINDOW_BUFFER_MONTHS;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

import com.google.common.annotations.VisibleForTesting;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.model.JobHandler;
import com.xgen.cloud.common.jobqueue._public.svc.JobHandlerSvc;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.module.metering.common.utils.MeteringUtils;
import com.xgen.module.metering.server.dao.MeterUsageDao;
import com.xgen.module.metering.server.svc.PurgeJobRuntimeWindowSvc;
import com.xgen.module.metering.server.svc.intf.MeterUsagePurgeJobScheduler;
import io.prometheus.client.Gauge;
import io.prometheus.client.Histogram;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.configuration2.ImmutableConfiguration;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class MeterUsagePurgeJobSchedulerImpl implements MeterUsagePurgeJobScheduler {

  private static final int JOB_MAX_DURATION_MINUTES = 5;
  private static final int DEFAULT_BATCH_SIZE = 100;
  private static final int DEFAULT_MAX_RUNTIME_SECONDS = 60;
  private static final int DEFAULT_SLEEP_BETWEEN_DELETION_MILLIS = 1000;

  public static final String PURGE_JOB_BATCH_SIZE = "mms.metering.purge.batchSize";
  public static final String PURGE_JOB_MAX_RUNTIME_SECONDS = "mms.metering.purge.maxRuntimeSeconds";
  public static final String PURGE_JOB_SLEEP_BETWEEN_DELETION_MILLIS =
      "mms.metering.purge.sleepBetweenDeletionMillis";

  private static final String PURGE_JOB_CUTOFF_LAG_GAUGE_NAME =
      "metering_purge_job_lag_milliseconds";
  private static final Gauge PURGE_JOB_CUTOFF_LAG_GAUGE =
      Gauge.build()
          .name(PURGE_JOB_CUTOFF_LAG_GAUGE_NAME)
          .help(
              "Current lag of the metering purge job in milliseconds compared to the current cutoff"
                  + " date")
          .register();

  private static final String PURGE_JOB_TOTAL_LAG_GAUGE_NAME =
      "metering_purge_job_total_lag_milliseconds";
  private static final Gauge PURGE_JOB_TOTAL_LAG_GAUGE =
      Gauge.build()
          .name(PURGE_JOB_TOTAL_LAG_GAUGE_NAME)
          .help(
              "Current lag of the metering purge job in milliseconds compared to the current"
                  + " date-time")
          .register();

  private static final String PURGE_JOB_STEADY_STATE_LAG_GAUGE_NAME =
      "metering_purge_job_steady_state_lag_milliseconds";
  private static final Gauge PURGE_JOB_STEADY_STATE_LAG_GAUGE =
      Gauge.build()
          .name(PURGE_JOB_STEADY_STATE_LAG_GAUGE_NAME)
          .help(
              "Current lag of the metering purge job in milliseconds compared to the steady state"
                  + " (the cutoff date + purge job 2 month buffer)")
          .register();

  private static final String PURGE_JOB_BURST_TIME_TAKEN_NAME =
      "metering_purge_job_burst_time_taken_seconds";
  private static final Histogram PURGE_JOB_BURST_TIME_TAKEN =
      Histogram.build()
          .name(PURGE_JOB_BURST_TIME_TAKEN_NAME)
          .help("Histogram to track how long each burst of MeterUsagePurgeJob takes in seconds.")
          .exponentialBuckets(0.001, 2, 14)
          .register();

  private final ImmutableConfiguration configuration;
  private final JobsProcessorSvc jobsProcessorSvc;

  @Inject
  public MeterUsagePurgeJobSchedulerImpl(
      ImmutableConfiguration configuration, JobsProcessorSvc jobsProcessorSvc) {
    this.configuration = configuration;
    this.jobsProcessorSvc = jobsProcessorSvc;
  }

  /**
   * {@inheritDoc}
   *
   * <p>The purge job will delete documents in batches of size specified in {@link
   * MeterUsagePurgeJobSchedulerImpl#getBatchSize()} and will run for maximum number of seconds
   * specified in {@link MeterUsagePurgeJobSchedulerImpl#getMaxRuntimeSeconds()}. There will be a
   * {@link MeterUsagePurgeJobSchedulerImpl#getSleepBetweenDeletionMillis()} millisecond delay
   * between each batch while the job is running.
   */
  @Override
  public ObjectId schedulePurgeJob() {
    return jobsProcessorSvc.submitJob(buildJob(new Date()));
  }

  @VisibleForTesting
  public ObjectId schedulePurgeJob(Date date) {
    return jobsProcessorSvc.submitJob(buildJob(date));
  }

  private Job buildJob(Date date) {
    BasicDBObject parameters =
        new BasicDBObject()
            .append(PURGE_JOB_BATCH_SIZE, getBatchSize())
            .append(PURGE_JOB_MAX_RUNTIME_SECONDS, getMaxRuntimeSeconds())
            .append(PURGE_JOB_SLEEP_BETWEEN_DELETION_MILLIS, getSleepBetweenDeletionMillis())
            .append("date", date);
    return new Job.Builder(MeterUsagePurgeJobHandler.class, parameters)
        .retriesRemaining(1)
        .intervalUntilRerun(Duration.ofMinutes(JOB_MAX_DURATION_MINUTES))
        .tag(MeteringJobTags.GENERIC_BILLING)
        .build();
  }

  private int getBatchSize() {
    return configuration.getInt(PURGE_JOB_BATCH_SIZE, DEFAULT_BATCH_SIZE);
  }

  private int getMaxRuntimeSeconds() {
    return configuration.getInt(PURGE_JOB_MAX_RUNTIME_SECONDS, DEFAULT_MAX_RUNTIME_SECONDS);
  }

  private long getSleepBetweenDeletionMillis() {
    return configuration.getLong(
        PURGE_JOB_SLEEP_BETWEEN_DELETION_MILLIS, DEFAULT_SLEEP_BETWEEN_DELETION_MILLIS);
  }

  @Singleton
  public static class MeterUsagePurgeJobHandler implements JobHandler {

    private static final Logger LOG = LoggerFactory.getLogger(MeterUsagePurgeJobHandler.class);

    private final MeterUsagePurgeJob meterUsagePurgeJob;
    private final MeterUsageDao meterUsageDao;
    private final JobHandlerSvc jobHandlerSvc;
    private final PurgeJobRuntimeWindowSvc purgeJobRuntimeWindowSvc;

    @Inject
    public MeterUsagePurgeJobHandler(
        MeterUsagePurgeJob meterUsagePurgeJob,
        MeterUsageDao meterUsageDao,
        JobHandlerSvc jobHandlerSvc,
        PurgeJobRuntimeWindowSvc purgeJobRuntimeWindowSvc) {
      this.meterUsagePurgeJob = meterUsagePurgeJob;
      this.meterUsageDao = meterUsageDao;
      this.jobHandlerSvc = jobHandlerSvc;
      this.purgeJobRuntimeWindowSvc = purgeJobRuntimeWindowSvc;
    }

    @Override
    public void handleWork(BasicDBObject parameters, ObjectId jobId) {
      Date date = parameters.getDate("date");
      LocalTime timeNow = LocalTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
      if (!purgeJobRuntimeWindowSvc.withinPurgeJobRuntimeWindow(timeNow)) {
        LOG.info("Skipping meter usage purge job because it's outside any runtime windows.");
        jobHandlerSvc.processJobAsComplete(jobId);
        return;
      }

      int batchSize = parameters.getInt(PURGE_JOB_BATCH_SIZE);
      int maxRuntimeSeconds = parameters.getInt(PURGE_JOB_MAX_RUNTIME_SECONDS);
      long sleepBetweenDeletionMillis = parameters.getLong(PURGE_JOB_SLEEP_BETWEEN_DELETION_MILLIS);

      LOG.info(
          "Starting meter usage purge job, {} {} {} {}",
          keyValue("date", date),
          keyValue("batchSize", batchSize),
          keyValue("maxRuntimeSeconds", maxRuntimeSeconds),
          keyValue("sleepBetweenDeletionMillis", sleepBetweenDeletionMillis));

      try {
        long start = System.currentTimeMillis();

        while (timeTakenMillis(start) < Duration.ofSeconds(maxRuntimeSeconds).toMillis()) {
          PURGE_JOB_BURST_TIME_TAKEN.time(() -> meterUsagePurgeJob.run(batchSize));
          // End early if the job would've ended after sleeping the thread
          if (timeLeftMillis(start, maxRuntimeSeconds) <= sleepBetweenDeletionMillis) {
            break;
          }
          Thread.sleep(sleepBetweenDeletionMillis);
        }

        recordMetrics(date);

        LOG.info(
            "Ending meter usage purge job {}", keyValue("timeTakenMillis", timeTakenMillis(start)));

        jobHandlerSvc.processJobAsComplete(jobId);
      } catch (Exception e) {
        LOG.warn("Error while running meter usage purge job {}", keyValue("jobId", jobId), e);
        jobHandlerSvc.handleError(jobId, CommonErrorCode.SERVER_ERROR, e);
      }
    }

    private void recordMetrics(Date date) {
      Date cutoffDate = MeteringUtils.getIngestionWindowCutoffDate(date);
      Optional<Date> oldestStartTime = meterUsageDao.findOldestMeterUsageStartTime();

      // Record metric for the difference between the oldest meter usage and the cutoff date
      long purgeJobCutoffLag =
          oldestStartTime.map(value -> cutoffDate.getTime() - value.getTime()).orElse(0L);
      LOG.debug(
          "Current meter usage purge job lag compared to cutoff date is {}ms", purgeJobCutoffLag);
      // Negative value means the cutoff date is before the oldest meter usage date
      PURGE_JOB_CUTOFF_LAG_GAUGE.set(purgeJobCutoffLag < 0 ? 0 : purgeJobCutoffLag);

      // Record metric for the difference between the oldest meter usage and the current date
      long purgeJobTotalLag =
          oldestStartTime.map(value -> date.getTime() - value.getTime()).orElse(0L);
      LOG.debug(
          "Current meter usage purge job lag compared to current date-time is {}ms",
          purgeJobTotalLag);
      PURGE_JOB_TOTAL_LAG_GAUGE.set(purgeJobTotalLag);

      // Record metric for the difference between the oldest meter usage and the (cutoff date +
      // buffer time)
      Date cutoffDatePlusBuffer = DateUtils.addMonths(cutoffDate, -1 * CUTOFF_WINDOW_BUFFER_MONTHS);
      long purgeJobSteadyStateLag =
          oldestStartTime.map(value -> cutoffDatePlusBuffer.getTime() - value.getTime()).orElse(0L);
      LOG.debug(
          "Current meter usage purge job lag compared to steady state is {}ms",
          purgeJobSteadyStateLag);
      // Negative value means the cutoff date is before the oldest meter usage date
      PURGE_JOB_STEADY_STATE_LAG_GAUGE.set(purgeJobSteadyStateLag < 0 ? 0 : purgeJobSteadyStateLag);
    }

    private static long timeTakenMillis(long start) {
      return System.currentTimeMillis() - start;
    }

    private static long timeLeftMillis(long start, int maxRuntimeSeconds) {
      return Duration.ofSeconds(maxRuntimeSeconds).toMillis() - timeTakenMillis(start);
    }
  }
}
