package com.xgen.module.liveimport.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.annotations.VisibleForTesting;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.common.model._public.annotation.GenEncryptMetadata;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.module.liveimport.common.LiveImportErrorCode;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;

public abstract class LiveImport {
  private static final int SECONDARIES_LAG_TOLERANCE = 10;
  public static final int MAX_RESTART_ATTEMPT = 5;
  public static final int MONGOSYNC_MAX_RESTART_ATTEMPT = 1;
  public static final Set<State> IN_PROGRESS_IMPORT_STATES = Set.of(State.NEW, State.WORKING);
  public static final Set<State> TERMINAL_IMPORT_STATES =
      Set.of(State.COMPLETE, State.EXPIRED, State.FAILED);

  private final ObjectId _id;
  private final ObjectId _groupId;
  private final Date _createDate;
  private final Date _stopDate; // Only used for admin dashboard
  private final Date _expireDate;
  private final Date _cutoverReachedDate;
  private final boolean _isCancelled;
  private final boolean _isCutoverComplete;
  private final Boolean _verificationEnabled;
  private final boolean _isAcknowledged;
  private final State _state;
  private final ErrorDetails _errorDetails;
  private final List<? extends MigrationStatus<? extends Stage>> _migrationStatuses;
  private final Set<Integer> _shardIndexesNeedingRestart;
  private final Map<String, Integer> _shardIndexesRestartCounts;
  private final Set<Integer> _shardIndexesNeedingResync;
  private final Source _src;
  private final Destination _destination;
  private final ObjectId _liveImportLocation;
  private final ObjectId _requesterUserId;
  private final boolean _dropEnabled;
  private final String _migrationToolVersion;
  private final Map<Integer, Long> _replicationLagPerShard;
  private final Double _oplogBatchSize;
  private final Double _collStatsThreshold;
  private final String _importSucceededWithWarning;
  private final LiveImportType _type;
  private final MigrationToolType _migrationToolType;
  private final MigrationType _migrationType;
  private final Long _migrationDataSize;
  private final Date _estimatedInitialSyncCompleteDate;
  private final Date _initialSyncStartDate;
  private final Date _oplogSyncStartDate;
  private final MigrationToolParameters _migrationToolParameters;
  private final Date _cutoverClickedDate;
  private final Integer _estimatedDowntimeSeconds;
  private final String lastLogTimestamp;
  private final Boolean sourceWriteBlockingEnabled;

  protected LiveImport(
      final ObjectId pId,
      final ObjectId pGroupId,
      final Date pCreateDate,
      final Date pStopDate,
      final Date pExpireDate,
      final Date pCutoverReachedDate,
      final boolean pIsCancelled,
      final boolean pIsCutoverComplete,
      final Boolean pVerificationEnabled,
      final boolean pIsAcknowledged,
      final State pState,
      final ErrorDetails pErrorDetails,
      final List<? extends MigrationStatus<? extends Stage>> pMigrationStatuses,
      final Set<Integer> pShardIndexesNeedingRestart,
      final Map<String, Integer> pShardIndexesRestartCounts,
      final Set<Integer> pShardIndexesNeedingResync,
      final Source pSrc,
      final Destination pDestination,
      final ObjectId pLiveImportLocation,
      final ObjectId pRequesterUserId,
      final boolean pDropEnabled,
      final String pMigrationToolVersion,
      final MigrationToolType pMigrationToolType,
      final Map<Integer, Long> pReplicationLagPerShard,
      final Double pOplogBatchSize,
      final Double pCollStatsThreshold,
      final String pImportSucceededWithWarning,
      final LiveImportType pType) {
    _id = pId;
    _groupId = pGroupId;
    _createDate = pCreateDate;
    _stopDate = pStopDate;
    _expireDate = pExpireDate;
    _cutoverReachedDate = pCutoverReachedDate;
    _isCancelled = pIsCancelled;
    _isCutoverComplete = pIsCutoverComplete;
    _verificationEnabled = pVerificationEnabled;
    _isAcknowledged = pIsAcknowledged;
    _state = pState;
    _errorDetails = pErrorDetails;
    _migrationStatuses = pMigrationStatuses;
    _shardIndexesNeedingRestart = pShardIndexesNeedingRestart;
    _shardIndexesRestartCounts = pShardIndexesRestartCounts;
    _shardIndexesNeedingResync = pShardIndexesNeedingResync;
    _src = pSrc;
    _destination = pDestination;
    _liveImportLocation = pLiveImportLocation;
    _requesterUserId = pRequesterUserId;
    _dropEnabled = pDropEnabled;
    _migrationToolVersion = pMigrationToolVersion;
    _migrationToolType = pMigrationToolType;
    _replicationLagPerShard = pReplicationLagPerShard;
    _oplogBatchSize = pOplogBatchSize;
    _collStatsThreshold = pCollStatsThreshold;
    _importSucceededWithWarning = pImportSucceededWithWarning;
    _type = pType;
    _migrationDataSize = null;
    _estimatedInitialSyncCompleteDate = null;
    _initialSyncStartDate = null;
    _oplogSyncStartDate = null;
    _migrationToolParameters = null;
    _cutoverClickedDate = null;
    _estimatedDowntimeSeconds = null;
    lastLogTimestamp = "";
    _migrationType = MigrationType.LEGACY_MIGRATION;
    sourceWriteBlockingEnabled = null;
  }

  public LiveImport(
      final ObjectId pGroupId,
      final String pClusterName,
      final ObjectId pClusterUniqueId,
      final Boolean pIsDestinationSharded,
      final List<? extends MigrationStatus<? extends Stage>> pStatuses,
      final ObjectId pLiveImportLocation,
      final ObjectId pRequesterUserId,
      final boolean pDropEnabled,
      final String pMigrationToolVersion,
      final MigrationToolType pMigrationToolType,
      final Double pOplogBatchSize,
      final Double pCollStatsThreshold,
      final LiveImportType pType,
      final Source pSource) {
    _id = new ObjectId();
    _groupId = pGroupId;
    _src = pSource;
    _destination = new Destination(pClusterName, pClusterUniqueId, pIsDestinationSharded);
    _isCancelled = false;
    _isCutoverComplete = false;
    _verificationEnabled = null;
    _isAcknowledged = false;
    _state = State.NEW;
    _errorDetails = null;
    _migrationStatuses = pStatuses;
    _shardIndexesNeedingRestart = new HashSet<>();
    _shardIndexesRestartCounts = new HashMap<>();
    _shardIndexesNeedingResync = new HashSet<>();
    _createDate = new Date();
    _stopDate = null;
    _expireDate = null;
    _cutoverReachedDate = null;
    _liveImportLocation = pLiveImportLocation;
    _requesterUserId = pRequesterUserId;
    _dropEnabled = pDropEnabled;
    _migrationToolVersion = pMigrationToolVersion;
    _migrationToolType = pMigrationToolType;
    _replicationLagPerShard = new HashMap<>();
    _oplogBatchSize = pOplogBatchSize;
    _collStatsThreshold = pCollStatsThreshold;
    _importSucceededWithWarning = null;
    _type = pType;
    _migrationDataSize = null;
    _estimatedInitialSyncCompleteDate = null;
    _initialSyncStartDate = null;
    _oplogSyncStartDate = null;
    _migrationToolParameters = null;
    _cutoverClickedDate = null;
    _estimatedDowntimeSeconds = null;
    lastLogTimestamp = "";
    _migrationType = MigrationType.LEGACY_MIGRATION;
    sourceWriteBlockingEnabled = null;
  }

  public void validateAtlasToAtlas() throws SvcException {
    if (StringUtils.isEmpty(getDestinationClusterName())) {
      throw new SvcException(
          LiveImportErrorCode.INVALID_ARGUMENT, "Destination cluster name is required");
    }
    if (StringUtils.isEmpty(getSourceClusterName())) {
      throw new SvcException(
          LiveImportErrorCode.INVALID_ARGUMENT, "Source cluster name is required");
    }
  }

  public void validateSelfToAtlas() throws SvcException {
    if (StringUtils.isEmpty(getDestinationClusterName())) {
      throw new SvcException(
          LiveImportErrorCode.INVALID_ARGUMENT, "Destination cluster name is required");
    }
    if (StringUtils.isEmpty(getSourceConnectionString())) {
      throw new SvcException(
          LiveImportErrorCode.INVALID_ARGUMENT, "Source connection string is required");
    }
  }

  public void validate() throws SvcException {
    if (StringUtils.isEmpty(getDestinationClusterName())) {
      throw new SvcException(
          LiveImportErrorCode.INVALID_ARGUMENT, "Destination cluster name is required");
    }
    if (StringUtils.isEmpty(getSource().getHostname())) {
      throw new SvcException(
          LiveImportErrorCode.INVALID_ARGUMENT, "Source cluster hostname is required");
    }
    final boolean hasUsername = !StringUtils.isEmpty(getSource().getUsername());
    final boolean hasPassword = !StringUtils.isEmpty(getSource().getPassword());
    if ((!hasUsername && hasPassword) || (hasUsername && !hasPassword)) {
      throw new SvcException(
          LiveImportErrorCode.INVALID_ARGUMENT,
          "Must supply both username and password or neither.");
    }
  }

  public LiveImport(final BasicDBObject pDBObject) {
    _id = pDBObject.getObjectId(FieldDefs.ID);
    _groupId = pDBObject.getObjectId(FieldDefs.GROUP_ID);
    _createDate = pDBObject.getDate(FieldDefs.CREATE_DATE);
    _stopDate = pDBObject.getDate(FieldDefs.STOP_DATE);
    _expireDate = pDBObject.getDate(FieldDefs.EXPIRE_DATE);
    _cutoverReachedDate = pDBObject.getDate(FieldDefs.CUTOVER_REACHED_DATE);
    _isCancelled = pDBObject.getBoolean(FieldDefs.IS_CANCELLED);
    _isCutoverComplete = pDBObject.getBoolean(FieldDefs.IS_CUTOVER_COMPLETE);
    _verificationEnabled = (Boolean) pDBObject.get(FieldDefs.VERIFICATION_ENABLED);
    _isAcknowledged = pDBObject.getBoolean(FieldDefs.IS_ACKNOWLEDGED);
    _state = State.valueOf(pDBObject.getString(FieldDefs.STATE));
    _errorDetails =
        pDBObject.get(FieldDefs.ERROR_DETAILS) != null
            ? new ErrorDetails((BasicDBObject) pDBObject.get(FieldDefs.ERROR_DETAILS))
            : null;
    _shardIndexesNeedingRestart =
        ((BasicDBList) pDBObject.get(FieldDefs.SHARD_INDEXES_NEEDING_RESTART))
            .stream().map(Integer.class::cast).collect(Collectors.toSet());
    _shardIndexesRestartCounts =
        ((BasicDBObject) pDBObject.get(FieldDefs.SHARD_INDEXES_RESTART_COUNTS))
            .entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> (Integer) entry.getValue()));
    _shardIndexesNeedingResync =
        ((BasicDBList) pDBObject.get(FieldDefs.SHARD_INDEXES_NEEDING_RESYNC))
            .stream().map(Integer.class::cast).collect(Collectors.toSet());
    _src = new Source((BasicDBObject) pDBObject.get(FieldDefs.SOURCE));
    _destination = new Destination((BasicDBObject) pDBObject.get(FieldDefs.DESTINATION));
    _liveImportLocation = pDBObject.getObjectId(FieldDefs.LIVE_IMPORT_LOCATION);
    _requesterUserId = pDBObject.getObjectId(FieldDefs.REQUESTER_USER_ID);
    _dropEnabled = pDBObject.getBoolean(FieldDefs.DROP_ENABLED);
    _migrationType =
        MigrationType.valueOf(
            pDBObject.getString(FieldDefs.MIGRATION_TYPE, MigrationType.LEGACY_MIGRATION.name()));
    _migrationToolType =
        MigrationToolType.valueOf(
            pDBObject.getString(
                FieldDefs.MIGRATION_TOOL_TYPE, MigrationToolType.MONGOMIRROR.name()));
    _migrationToolVersion =
        _migrationToolType == MigrationToolType.MONGOSYNC
            ? pDBObject.getString(FieldDefs.MONGOSYNC_VERSION)
            : pDBObject.getString(FieldDefs.MONGO_MIRROR_VERSION);
    _migrationStatuses =
        _migrationToolType == MigrationToolType.MONGOSYNC
            ? ((BasicDBList) pDBObject.get(FieldDefs.MONGOSYNC_STATUSES))
                .stream()
                    .map(o -> MongosyncStatus.parse((BasicDBObject) o))
                    .collect(Collectors.toList())
            : ((BasicDBList) pDBObject.get(FieldDefs.MONGO_MIRROR_STATUSES))
                .stream()
                    .map(o -> MongoMirrorStatus.parse((BasicDBObject) o))
                    .collect(Collectors.toList());
    final BasicDBObject lagMap =
        (BasicDBObject)
            pDBObject.getOrDefault(FieldDefs.REPLICATION_LAG_PER_SHARD, new BasicDBObject());
    _replicationLagPerShard =
        lagMap.entrySet().stream()
            .collect(
                HashMap::new,
                (newMap, dbEntry) ->
                    newMap.put(
                        Integer.parseInt(dbEntry.getKey()),
                        // Value can be null
                        dbEntry.getValue() != null
                            ? ((Number) dbEntry.getValue()).longValue()
                            : null),
                HashMap::putAll);
    _oplogBatchSize = (Double) pDBObject.getOrDefault(FieldDefs.OPLOG_BATCH_SIZE, null);
    _collStatsThreshold = (Double) pDBObject.getOrDefault(FieldDefs.COLLSTATS_THRESHOLD, null);
    _importSucceededWithWarning = pDBObject.getString(FieldDefs.IMPORT_SUCCEEDED_WITH_WARNING);
    _type = LiveImportType.valueOf(pDBObject.getString(FieldDefs.TYPE, LiveImportType.PULL.name()));
    _migrationDataSize = (Long) pDBObject.getOrDefault(FieldDefs.MIGRATION_DATA_SIZE_BYTES, null);
    _estimatedInitialSyncCompleteDate =
        pDBObject.getDate(FieldDefs.ESTIMATED_INITIAL_SYNC_COMPLETE_DATE, null);
    _initialSyncStartDate = pDBObject.getDate(FieldDefs.INITIAL_SYNC_START_DATE);
    _oplogSyncStartDate = pDBObject.getDate(FieldDefs.OPLOG_SYNC_START_DATE);
    _migrationToolParameters =
        _migrationToolType == MigrationToolType.MONGOSYNC
                && pDBObject.get(FieldDefs.MONGOSYNC_SHARDING_PARAMS) != null
            ? new MongosyncShardingParams(
                (BasicDBObject) pDBObject.get(FieldDefs.MONGOSYNC_SHARDING_PARAMS))
            : null;
    _cutoverClickedDate = pDBObject.getDate(FieldDefs.CUTOVER_CLICKED_DATE);
    _estimatedDowntimeSeconds =
        (Integer) pDBObject.getOrDefault(FieldDefs.ESTIMATED_DOWNTIME_SECONDS, null);
    lastLogTimestamp = pDBObject.getString(FieldDefs.LAST_LOG_TIMESTAMP, "");
    sourceWriteBlockingEnabled = null;
  }

  public LiveImport(final Builder<?, ?, ?> pBuilder) {
    _id = pBuilder.id == null ? ObjectId.get() : pBuilder.id;
    _groupId = pBuilder.groupId == null ? ObjectId.get() : pBuilder.groupId;
    _createDate = pBuilder.createDate == null ? new Date() : pBuilder.createDate;
    _stopDate = pBuilder.stopDate;
    _expireDate = pBuilder.expireDate;
    _cutoverReachedDate = pBuilder.cutoverReachedDate;
    _isCancelled = pBuilder.isCancelled;
    _isCutoverComplete = pBuilder.isCutoverComplete;
    _verificationEnabled = pBuilder.verificationEnabled;
    _isAcknowledged = pBuilder.isAcknowledged;
    _state = pBuilder.state == null ? State.NEW : pBuilder.state;
    _errorDetails = pBuilder.errorDetails;
    _migrationStatuses =
        pBuilder.migrationStatuses == null && pBuilder.migrationToolType != null
            ? (pBuilder.migrationToolType == MigrationToolType.MONGOSYNC
                ? List.of(MongosyncStatus.getInitialStatus())
                : List.of(MongoMirrorStatus.getInitialStatus()))
            : pBuilder.migrationStatuses;
    _shardIndexesNeedingRestart =
        pBuilder.shardIndexesNeedingRestart == null
            ? Set.of()
            : pBuilder.shardIndexesNeedingRestart;
    _shardIndexesRestartCounts =
        pBuilder.shardIndexesRestartCounts == null ? Map.of() : pBuilder.shardIndexesRestartCounts;
    _shardIndexesNeedingResync =
        pBuilder.shardIndexesNeedingResync == null ? Set.of() : pBuilder.shardIndexesNeedingResync;
    _src = pBuilder.src;
    _destination = pBuilder.destination;
    _liveImportLocation = pBuilder.liveImportLocation;
    _requesterUserId = pBuilder.requesterUserId;
    _dropEnabled = pBuilder.dropEnabled;
    _migrationToolVersion = pBuilder.migrationToolVersion;
    _migrationToolType = pBuilder.migrationToolType;
    _migrationType =
        pBuilder.migrationType == null ? MigrationType.LEGACY_MIGRATION : pBuilder.migrationType;
    _replicationLagPerShard =
        pBuilder.replicationLagPerShard == null ? Map.of() : pBuilder.replicationLagPerShard;
    _oplogBatchSize = pBuilder.oplogBatchSize;
    _collStatsThreshold = pBuilder.collStatsThreshold;
    _importSucceededWithWarning = pBuilder.importSucceededWithWarning;
    _type = pBuilder.type;
    _migrationDataSize = null;
    _estimatedInitialSyncCompleteDate = null;
    _initialSyncStartDate = null;
    _oplogSyncStartDate = null;
    _migrationToolParameters = pBuilder.migrationToolParameters;
    _cutoverClickedDate = pBuilder.cutoverClickedDate;
    _estimatedDowntimeSeconds = pBuilder.estimatedDowntimeSeconds;
    lastLogTimestamp = "";
    sourceWriteBlockingEnabled = pBuilder.isSourceWriteBlockingEnabled;
  }

  public DBObject toDBObject() {
    final BasicDBList statuses = new BasicDBList();
    getMigrationStatuses().forEach(status -> statuses.add(status.toDBObject()));
    final BasicDBList shardsNeedingRestart = new BasicDBList();
    shardsNeedingRestart.addAll(getShardIndexesNeedingRestart());
    final BasicDBObject shardIndexesRestartCounts = new BasicDBObject(_shardIndexesRestartCounts);
    final BasicDBList shardsNeedingResync = new BasicDBList();
    shardsNeedingResync.addAll(getShardIndexesNeedingResync());
    final BasicDBObject replicationLagPerShard = new BasicDBObject();
    getReplicationLagPerShard().forEach((k, v) -> replicationLagPerShard.append(k.toString(), v));
    final BasicDBObject basicDBObject =
        new BasicDBObject()
            .append(FieldDefs.ID, getId())
            .append(FieldDefs.GROUP_ID, getGroupId())
            .append(FieldDefs.CREATE_DATE, getCreateDate())
            .append(FieldDefs.STOP_DATE, getStopDate())
            .append(FieldDefs.EXPIRE_DATE, getExpireDate())
            .append(FieldDefs.CUTOVER_REACHED_DATE, getCutoverReachedDate())
            .append(FieldDefs.IS_CANCELLED, isCancelled())
            .append(FieldDefs.IS_CUTOVER_COMPLETE, isCutoverComplete())
            .append(FieldDefs.VERIFICATION_ENABLED, isVerificationEnabled().orElse(null))
            .append(FieldDefs.IS_ACKNOWLEDGED, isAcknowledged())
            .append(FieldDefs.STATE, getState().name())
            .append(FieldDefs.SOURCE, getSource().toDBObject())
            .append(FieldDefs.SHARD_INDEXES_NEEDING_RESTART, shardsNeedingRestart)
            .append(FieldDefs.SHARD_INDEXES_RESTART_COUNTS, shardIndexesRestartCounts)
            .append(FieldDefs.SHARD_INDEXES_NEEDING_RESYNC, shardsNeedingResync)
            .append(FieldDefs.DESTINATION, getDestination().toDBObject())
            .append(FieldDefs.LIVE_IMPORT_LOCATION, getLiveImportLocation())
            .append(FieldDefs.DROP_ENABLED, isDropEnabled())
            .append(
                FieldDefs.REQUESTER_USER_ID,
                getRequesterUserId().isPresent() ? getRequesterUserId().get() : null)
            .append(FieldDefs.REPLICATION_LAG_PER_SHARD, replicationLagPerShard)
            .append(FieldDefs.OPLOG_BATCH_SIZE, _oplogBatchSize)
            .append(FieldDefs.COLLSTATS_THRESHOLD, _collStatsThreshold)
            .append(
                FieldDefs.IMPORT_SUCCEEDED_WITH_WARNING,
                getImportSucceededWithWarning().orElse(null))
            .append(FieldDefs.TYPE, getType().name())
            .append(FieldDefs.MIGRATION_TOOL_TYPE, getMigrationToolType().name())
            .append(FieldDefs.MIGRATION_TYPE, getMigrationType().name())
            .append(FieldDefs.MIGRATION_DATA_SIZE_BYTES, getMigrationDataSize())
            .append(
                FieldDefs.ESTIMATED_INITIAL_SYNC_COMPLETE_DATE,
                getEstimatedInitialSyncCompleteDate())
            .append(FieldDefs.INITIAL_SYNC_START_DATE, getInitialSyncStartDate())
            .append(FieldDefs.CUTOVER_CLICKED_DATE, getCutoverClickedDate())
            .append(FieldDefs.ESTIMATED_DOWNTIME_SECONDS, getEstimatedDowntimeSeconds())
            .append(FieldDefs.OPLOG_SYNC_START_DATE, getOplogSyncStartDate())
            .append(FieldDefs.LAST_LOG_TIMESTAMP, lastLogTimestamp);
    final String migrationToolVersion = getMigrationToolVersion().orElse(null);
    if (getMigrationToolType() == MigrationToolType.MONGOSYNC) {
      basicDBObject
          .append(FieldDefs.MONGOSYNC_VERSION, migrationToolVersion)
          .append(FieldDefs.MONGOSYNC_STATUSES, statuses);
      if (getMigrationToolParameters() != null) {
        basicDBObject.append(FieldDefs.MONGOSYNC_SHARDING_PARAMS, getMigrationToolParameters());
      }
    } else {
      basicDBObject
          .append(FieldDefs.MONGO_MIRROR_VERSION, migrationToolVersion)
          .append(FieldDefs.MONGO_MIRROR_STATUSES, statuses);
    }
    return basicDBObject;
  }

  public ObjectId getId() {
    return _id;
  }

  public ObjectId getGroupId() {
    return _groupId;
  }

  public Date getCreateDate() {
    return _createDate;
  }

  public Date getStopDate() {
    return _stopDate;
  }

  public Date getExpireDate() {
    return _expireDate;
  }

  public Date getCutoverReachedDate() {
    return _cutoverReachedDate;
  }

  public boolean hasCutOverReachedDate() {
    return _cutoverReachedDate != null;
  }

  public boolean hasExpireDate() {
    return _expireDate != null;
  }

  public boolean isCancelled() {
    return _isCancelled;
  }

  public boolean isCutoverComplete() {
    return _isCutoverComplete;
  }

  public Optional<Boolean> isVerificationEnabled() {
    return Optional.ofNullable(_verificationEnabled);
  }

  public boolean importFailedForSomeShard() {
    return getMigrationStatuses().stream()
        .anyMatch(status -> StringUtils.isNotEmpty(status.getErrorMessage()));
  }

  public MigrationToolType getMigrationToolType() {
    return _migrationToolType;
  }

  public MigrationType getMigrationType() {
    return _migrationType;
  }

  public boolean isReadyForCutover() {
    return !importFailedForSomeShard()
        && isOplogSyncedForAllShards()
        && replicationLagHealthyForAllShards();
  }

  public boolean isOplogSyncedForAllShards() {
    return IntStream.range(0, getMigrationStatuses().size())
        .allMatch(i -> getMigrationStatuses().get(i).isOplogSynced(isCSRSStatus(i)));
  }

  public boolean replicationLagHealthyForAllShards() {
    return getReplicationLagPerShard().size() == getMigrationStatuses().size()
        && getReplicationLagPerShard().values().stream()
            .allMatch(v -> v != null && v < SECONDARIES_LAG_TOLERANCE);
  }

  public abstract boolean isCSRSStatus(final int index);

  public abstract long getInitialMigrationDataSize();

  public Date getEstimatedInitialSyncCompleteDate() {
    return _estimatedInitialSyncCompleteDate;
  }

  public boolean isAcknowledged() {
    return _isAcknowledged;
  }

  public State getState() {
    return _state;
  }

  public ErrorDetails getErrorDetails() {
    return _errorDetails;
  }

  public Set<Integer> getShardIndexesNeedingRestart() {
    return _shardIndexesNeedingRestart;
  }

  public Map<String, Integer> getShardIndexesRestartCounts() {
    return _shardIndexesRestartCounts;
  }

  public Integer getShardIndexRestartCount(final int pShardIndex) {
    return _shardIndexesRestartCounts.getOrDefault(String.valueOf(pShardIndex), 0);
  }

  public Set<Integer> getShardIndexesNeedingResync() {
    return _shardIndexesNeedingResync;
  }

  public Source getSource() {
    return _src;
  }

  public ObjectId getLiveImportLocation() {
    return _liveImportLocation;
  }

  public boolean isInProgress() {
    return IN_PROGRESS_IMPORT_STATES.contains(_state);
  }

  public Destination getDestination() {
    return _destination;
  }

  public Optional<ObjectId> getRequesterUserId() {
    return Optional.ofNullable(_requesterUserId);
  }

  public boolean isExpired() {
    return _expireDate != null && _expireDate.before(new Date());
  }

  public boolean isDropEnabled() {
    return _dropEnabled;
  }

  @Nullable
  public String getSourceClusterName() {
    return getSource().getClusterName().orElse(null);
  }

  @Nullable
  public String getDestinationClusterName() {
    return Optional.ofNullable(_destination)
        .map(LiveImport.Destination::getClusterName)
        .orElse(null);
  }

  public ObjectId getDestinationGroupId() {
    return _destination.getGroupId();
  }

  public ObjectId getSourceGroupId() {
    return _src.getGroupId().get();
  }

  @Nullable
  public String getSourceConnectionString() {
    return getSource().getConnectionString().orElse(null);
  }

  // Config replicaset is the last host in the Source._primariesHostnames
  public boolean isConfigReplicasetIndex(final int pShardIndex) {
    final int size =
        !this.getSource().getPrimariesHostnames().isEmpty()
            ? this.getSource().getPrimariesHostnames().size()
            : this.getSource().getReplicaSetsUris().size();
    return size > 1 && pShardIndex == size - 1;
  }

  public boolean isSharded() {
    return getSource().getPrimariesHostnames().size() > 1
        || getSource().getReplicaSetsUris().size() > 1;
  }

  public Optional<String> getMigrationToolVersion() {
    return Optional.ofNullable(_migrationToolVersion);
  }

  public int getShardIndexForMigrationStatus(
      final MigrationStatus<? extends Stage> pMigrationStatus) {
    final List<? extends MigrationStatus<? extends Stage>> statuses = getMigrationStatuses();
    return IntStream.range(0, statuses.size())
        .filter(i -> statuses.get(i).equals(pMigrationStatus))
        .findFirst()
        .orElseThrow(
            () ->
                new NoSuchElementException(
                    "LiveImport#getMigrationStatuses() does not contain argument"));
  }

  public Optional<Double> getOplogBatchSize() {
    return Optional.ofNullable(_oplogBatchSize);
  }

  public Optional<Double> getCollStatsThreshold() {
    return Optional.ofNullable(_collStatsThreshold);
  }

  public Optional<String> getImportSucceededWithWarning() {
    return Optional.ofNullable(_importSucceededWithWarning);
  }

  public Map<Integer, Long> getReplicationLagPerShard() {
    return _replicationLagPerShard;
  }

  public LiveImportType getType() {
    return _type;
  }

  public Long getMigrationDataSize() {
    return _migrationDataSize;
  }

  public Date getInitialSyncStartDate() {
    return _initialSyncStartDate;
  }

  public Date getOplogSyncStartDate() {
    return _oplogSyncStartDate;
  }

  public String getTypeName() {
    return _type == null ? "" : _type.name();
  }

  public List<? extends MigrationStatus<? extends Stage>> getMigrationStatuses() {
    return _migrationStatuses;
  }

  public MigrationToolParameters getMigrationToolParameters() {
    return _migrationToolParameters;
  }

  public Date getCutoverClickedDate() {
    return _cutoverClickedDate;
  }

  public boolean hasCutoverClickedDate() {
    return _cutoverClickedDate != null;
  }

  public Integer getEstimatedDowntimeSeconds() {
    return _estimatedDowntimeSeconds;
  }

  public boolean hasEstimatedDowntimeSeconds() {
    return _estimatedDowntimeSeconds != null;
  }

  public String getLastLogTimestamp() {
    return lastLogTimestamp;
  }

  public boolean hasMongosyncShardingParams() {
    return getMigrationToolType() == MigrationToolType.MONGOSYNC
        && _migrationToolParameters != null;
  }

  public enum State {
    NEW,
    WORKING,
    FAILED,
    COMPLETE,
    EXPIRED;

    public static boolean isValidState(final String pState) {
      try {
        State.valueOf(pState);
        return true;
      } catch (final IllegalArgumentException e) {
        return false;
      }
    }

    public boolean isStopped() {
      return this == FAILED || this == COMPLETE || this == EXPIRED;
    }
  }

  public boolean isSourceSslEnabled() {
    return getSource().sslEnabled();
  }

  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MongosyncLog {

    public static final Set<String> ERROR_LEVELS = Set.of("fatal", "error");

    private final String _level;
    private final String _message;
    private final Error _error;
    private final Error _handledError;

    @JsonCreator
    public MongosyncLog(
        @JsonProperty(value = "level", required = true) final String pLevel,
        @JsonProperty(value = "message") final String pMessage,
        @JsonProperty(value = "error") final Error pError,
        @JsonProperty(value = "handledError") final Error pHandledError) {
      _level = pLevel;
      _message = pMessage;
      _error = pError;
      _handledError = pHandledError;
    }

    public MongosyncLog(final BasicDBObject pDBObject) {
      _level = pDBObject.getString(FieldDefs.LEVEL);
      _message = pDBObject.getString(FieldDefs.MESSAGE);
      _error =
          pDBObject.get(FieldDefs.ERROR) != null
              ? new Error((BasicDBObject) pDBObject.get(FieldDefs.ERROR))
              : null;
      _handledError =
          pDBObject.get(FieldDefs.HANDLED_ERROR) != null
              ? new Error((BasicDBObject) pDBObject.get(FieldDefs.HANDLED_ERROR))
              : null;
    }

    public DBObject toDBObject() {
      return new BasicDBObject()
          .append(FieldDefs.LEVEL, getLevel())
          .append(FieldDefs.MESSAGE, getMessage())
          .append(FieldDefs.ERROR, getError() != null ? getError().toDBObject() : null)
          .append(
              FieldDefs.HANDLED_ERROR,
              getHandledError() != null ? getHandledError().toDBObject() : null);
    }

    public String getLevel() {
      return _level;
    }

    public String getMessage() {
      return _message;
    }

    public Error getError() {
      return _error;
    }

    public Error getHandledError() {
      return _handledError;
    }

    public Error getErrorOrHandledError() {
      return _error != null ? _error : _handledError;
    }

    public boolean isErrorLevel() {
      return _level != null && ERROR_LEVELS.contains(_level);
    }

    public boolean isErrorLabelled() {
      return _error != null && _error._message != null;
    }

    public static class FieldDefs {
      public static final String LEVEL = "level";
      public static final String MESSAGE = "message";
      public static final String ERROR = "error";
      public static final String HANDLED_ERROR = "handledError";
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Error {
      private final List<String> _errorLabels;
      private final String _clientType;
      private final String _database;
      private final String _collection;
      private final String _message;

      @JsonCreator
      public Error(
          @JsonProperty(value = "msErrorLabels") final List<String> pErrorLabels,
          @JsonProperty(value = "clientType") final String pClientType,
          @JsonProperty(value = "database") final String pDatabase,
          @JsonProperty(value = "collection") final String pCollection,
          @JsonProperty(value = "message") final String pMessage) {
        _errorLabels = pErrorLabels;
        _clientType = pClientType;
        _database = pDatabase;
        _collection = pCollection;
        _message = pMessage;
      }

      public Error(final BasicDBObject pDBObject) {
        _errorLabels =
            pDBObject.get(FieldDefs.MS_ERROR_LABELS) != null
                ? ((BasicDBList) pDBObject.get(FieldDefs.MS_ERROR_LABELS))
                    .stream().map(String.class::cast).collect(Collectors.toList())
                : new ArrayList<>();
        _clientType = pDBObject.getString(FieldDefs.CLIENT_TYPE);
        _message = pDBObject.getString(FieldDefs.MESSAGE);
        _database = pDBObject.getString(FieldDefs.DATABASE);
        _collection = pDBObject.getString(FieldDefs.COLLECTION);
      }

      public DBObject toDBObject() {
        return new BasicDBObject()
            .append(FieldDefs.MS_ERROR_LABELS, getErrorLabels())
            .append(FieldDefs.CLIENT_TYPE, getClientType())
            .append(FieldDefs.MESSAGE, getMessage())
            .append(FieldDefs.DATABASE, getDatabase())
            .append(FieldDefs.COLLECTION, getCollection());
      }

      public List<String> getErrorLabels() {
        return _errorLabels;
      }

      public String getClientType() {
        return _clientType;
      }

      public String getDatabase() {
        return _database;
      }

      public String getCollection() {
        return _collection;
      }

      public String getMessage() {
        return _message;
      }

      public static class FieldDefs {
        public static final String MS_ERROR_LABELS = "msErrorLabels";
        public static final String CLIENT_TYPE = "clientType";
        public static final String DATABASE = "database";
        public static final String COLLECTION = "collection";
        public static final String MESSAGE = "message";
      }
    }
  }

  public static class ErrorDetails {
    private final String _name;
    private final Integer _code;
    private final String _message;
    private final String _externalMessage;
    private final MongosyncLog _mongosyncFatalLog;

    public ErrorDetails(final BasicDBObject pDBObject) {
      _name = pDBObject.getString(ErrorDetails.FieldDefs.NAME);
      _code = pDBObject.getInt(ErrorDetails.FieldDefs.CODE);
      _message = pDBObject.getString(ErrorDetails.FieldDefs.MESSAGE);
      _externalMessage = pDBObject.getString(ErrorDetails.FieldDefs.EXTERNAL_MESSAGE);
      _mongosyncFatalLog =
          pDBObject.get(FieldDefs.MONGOSYNC_FATAL_LOG) != null
              ? new MongosyncLog(
                  (BasicDBObject) pDBObject.get(ErrorDetails.FieldDefs.MONGOSYNC_FATAL_LOG))
              : null;
    }

    public DBObject toDBObject() {
      return new BasicDBObject()
          .append(ErrorDetails.FieldDefs.NAME, getName())
          .append(ErrorDetails.FieldDefs.CODE, getCode())
          .append(ErrorDetails.FieldDefs.MESSAGE, getMessage())
          .append(ErrorDetails.FieldDefs.EXTERNAL_MESSAGE, getExternalMessage())
          .append(
              ErrorDetails.FieldDefs.MONGOSYNC_FATAL_LOG,
              getMongosyncFatalLog() != null ? getMongosyncFatalLog().toDBObject() : null);
    }

    public ErrorDetails(
        final String pName,
        final Integer pCode,
        final String pMessage,
        final String pExternalMessage,
        final MongosyncLog pMongosyncLog) {
      _name = pName;
      _code = pCode;
      _message = pMessage;
      _externalMessage = pExternalMessage;
      _mongosyncFatalLog = pMongosyncLog;
    }

    public String getName() {
      return _name;
    }

    public Integer getCode() {
      return _code;
    }

    public String getMessage() {
      return _message;
    }

    public String getExternalMessage() {
      return _externalMessage;
    }

    public MongosyncLog getMongosyncFatalLog() {
      return _mongosyncFatalLog;
    }

    public static class FieldDefs {
      public static final String NAME = "name";
      public static final String CODE = "code";
      public static final String MESSAGE = "message";
      public static final String EXTERNAL_MESSAGE = "externalMessage";
      public static final String MONGOSYNC_FATAL_LOG = "mongosyncFatalLog";
    }
  }

  public static class Source {
    private final String _hostname;
    private final List<String> _primariesHostnames;
    private final List<String> _replicaSetsUris;
    private final String _username;
    private final String _password;
    private final boolean _sslEnabled;
    private final ObjectId _groupId;
    private final ObjectId _clusterId;
    private final String _clusterName;
    private final Double _totalSizeBytes;
    // Used for PULL type live migrations
    private final String _caFile;
    private final Optional<List<String>> _shardHorizons;
    private final List<String> _shardIds;
    // Used for PUSH type live migrations
    private final String _caFilePath;
    private final VersionUtils.Version _mongoDBVersion;
    private final String _featureCompatibilityVersion;
    private final String _displayLabel;
    private final Boolean _isSharded;
    private final String _connectionString;

    public Source(final String pClusterName, final ObjectId pClusterId, final ObjectId pGroupId) {
      this(
          null,
          new ArrayList<>(),
          new ArrayList<>(),
          null,
          null,
          null,
          false,
          null,
          null,
          null,
          new ArrayList<>(),
          pGroupId,
          pClusterId,
          pClusterName,
          null,
          null);
    }

    public Source(final String pConnectionString, final boolean pSslEnabled, final String pCaFile) {
      this(
          null,
          new ArrayList<>(),
          new ArrayList<>(),
          null,
          null,
          null,
          pSslEnabled,
          pCaFile,
          null,
          null,
          new ArrayList<>(),
          null,
          null,
          null,
          null,
          pConnectionString);
    }

    public Source(
        final String pUsername,
        final String pPassword,
        final String pConnectionString,
        final boolean pSslEnabled,
        final String pCaFile) {
      this(
          null,
          new ArrayList<>(),
          new ArrayList<>(),
          null,
          pUsername,
          pPassword,
          pSslEnabled,
          pCaFile,
          null,
          null,
          new ArrayList<>(),
          null,
          null,
          null,
          null,
          pConnectionString);
    }

    public Source(
        final String pHostname,
        final String pDisplayLabel,
        final String pUsername,
        final String pPassword,
        final boolean pSslEnabled,
        final String pCaFile) {
      this(
          pHostname,
          new ArrayList<>(),
          new ArrayList<>(),
          pDisplayLabel,
          pUsername,
          pPassword,
          pSslEnabled,
          pCaFile,
          "",
          null,
          new ArrayList<>());
    }

    public Source(
        final String pHostname,
        final String pDisplayLabel,
        final String pUsername,
        final String pPassword,
        final boolean pSslEnabled,
        final String pCaFile,
        final String pCaFilePath) {
      this(
          pHostname,
          new ArrayList<>(),
          new ArrayList<>(),
          pDisplayLabel,
          pUsername,
          pPassword,
          pSslEnabled,
          pCaFile,
          pCaFilePath,
          null,
          new ArrayList<>());
    }

    // Constructor primarily used by pull live imports
    public Source(
        final String pHostname,
        final List<String> pPrimariesHostnames,
        final List<String> pReplicaSetsUris,
        final String pDisplayLabel,
        final String pUsername,
        final String pPassword,
        final boolean pSslEnabled,
        final String pCaFile,
        final String pCaFilePath,
        final List<String> pShardHorizons,
        final List<String> pShardIds) {
      this(
          pHostname,
          pPrimariesHostnames,
          pReplicaSetsUris,
          pDisplayLabel,
          pUsername,
          pPassword,
          pSslEnabled,
          pCaFile,
          pCaFilePath,
          pShardHorizons,
          pShardIds,
          null,
          null,
          null,
          null,
          null);
    }

    // Constructor primarily used by push live imports
    // Hostname and clusterName are the same for push live imports
    public Source(
        final String pHostname,
        final String pDisplayLabel,
        final String pUsername,
        final String pPassword,
        final boolean pSslEnabled,
        final String pCaFilePath,
        final ObjectId pSourceGroupId,
        final ObjectId pSourceClusterId) {
      this(
          pHostname,
          new ArrayList<>(),
          new ArrayList<>(),
          pDisplayLabel,
          pUsername,
          pPassword,
          pSslEnabled,
          null,
          pCaFilePath,
          null,
          new ArrayList<>(),
          pSourceGroupId,
          pSourceClusterId,
          pHostname,
          null,
          null);
    }

    // Base constructor
    Source(
        final String pHostname,
        final List<String> pPrimariesHostnames,
        final List<String> pReplicaSetsUris,
        final String pDisplayLabel,
        final String pUsername,
        final String pPassword,
        final boolean pSslEnabled,
        final String pCaFile,
        final String pCaFilePath,
        final List<String> pShardHorizons,
        final List<String> pShardIds,
        final ObjectId pGroupId,
        final ObjectId pClusterId,
        final String pClusterName,
        final Double pSourceSizeTotalBytes,
        final String pConnectionString) {
      _hostname = pHostname;
      _username = pUsername;
      _password = pPassword;
      _displayLabel = pDisplayLabel;
      _sslEnabled = pSslEnabled;
      _caFile = pCaFile;
      _mongoDBVersion = null;
      _featureCompatibilityVersion = null;
      _primariesHostnames = pPrimariesHostnames;
      _replicaSetsUris = pReplicaSetsUris;
      _caFilePath = pCaFilePath;
      _shardHorizons = Optional.ofNullable(pShardHorizons);
      _shardIds = pShardIds;
      _isSharded = null;
      _groupId = pGroupId;
      _clusterId = pClusterId;
      _clusterName = pClusterName;
      _totalSizeBytes = pSourceSizeTotalBytes;
      _connectionString = pConnectionString;
    }

    public Source(final BasicDBObject pDBObject) {
      _hostname = pDBObject.getString(FieldDefs.HOSTNAME);
      _primariesHostnames =
          ((BasicDBList) pDBObject.get(FieldDefs.PRIMARIES_HOSTNAMES))
              .stream().map(String.class::cast).collect(Collectors.toList());
      _replicaSetsUris =
          pDBObject.get(FieldDefs.REPLICA_SETS_URIS) != null
              ? ((BasicDBList) pDBObject.get(FieldDefs.REPLICA_SETS_URIS))
                  .stream().map(String.class::cast).collect(Collectors.toList())
              : new ArrayList<>();
      _displayLabel = pDBObject.getString(FieldDefs.DISPLAY_LABEL);
      _username = pDBObject.getString(FieldDefs.USERNAME);
      try {
        _password = EncryptionUtils.genDecryptStr(pDBObject.getString(FieldDefs.PASSWORD));
      } catch (final Exception e) {
        throw new IllegalStateException("Error decrypting password", e);
      }
      _sslEnabled = pDBObject.getBoolean(FieldDefs.SSL_ENABLED);
      _caFile = pDBObject.getString(FieldDefs.CA_FILE);
      _caFilePath = pDBObject.getString(FieldDefs.CA_FILE_PATH);
      _mongoDBVersion =
          pDBObject.get(FieldDefs.MONGODB_VERSION) != null
              ? VersionUtils.parse(pDBObject.getString(FieldDefs.MONGODB_VERSION))
              : null;
      _featureCompatibilityVersion = pDBObject.getString(FieldDefs.FEATURE_COMPATIBILITY_VERSION);
      _shardHorizons =
          Optional.ofNullable(pDBObject.get(FieldDefs.SHARD_HORIZONS))
              .map(BasicDBList.class::cast)
              .map(
                  horizons ->
                      horizons.stream().map(String.class::cast).collect(Collectors.toList()));
      _shardIds =
          Optional.ofNullable(pDBObject.get(FieldDefs.SHARD_IDS))
              .map(BasicDBList.class::cast)
              .map(
                  shardIds ->
                      shardIds.stream().map(String.class::cast).collect(Collectors.toList()))
              .orElse(new ArrayList<>());
      _isSharded =
          pDBObject.get(FieldDefs.IS_SHARDED) != null
              ? pDBObject.getBoolean(FieldDefs.IS_SHARDED)
              : null;
      _totalSizeBytes =
          pDBObject.get(FieldDefs.TOTAL_SIZE_BYTES) != null
              ? pDBObject.getDouble(FieldDefs.TOTAL_SIZE_BYTES)
              : null;
      _groupId = pDBObject.getObjectId(FieldDefs.GROUP_ID);
      _clusterId = pDBObject.getObjectId(FieldDefs.CLUSTER_ID);
      _clusterName = pDBObject.getString(FieldDefs.CLUSTER_NAME);
      _connectionString = pDBObject.getString(FieldDefs.CONNECTION_STRING);
    }

    public BasicDBObject toDBObject() {
      final BasicDBList hostnames = new BasicDBList();
      hostnames.addAll(getPrimariesHostnames());
      final BasicDBList uris = new BasicDBList();
      uris.addAll(getReplicaSetsUris());
      final BasicDBList shardHorizons =
          getShardHorizons()
              .map(
                  horizons -> {
                    final BasicDBList horizonsDoc = new BasicDBList();
                    horizonsDoc.addAll(horizons);
                    return horizonsDoc;
                  })
              .orElse(null);
      final BasicDBList shardIds = new BasicDBList();
      shardIds.addAll(getShardIds());
      final BasicDBObject doc =
          new BasicDBObject()
              .append(FieldDefs.HOSTNAME, getHostname())
              .append(FieldDefs.PRIMARIES_HOSTNAMES, hostnames)
              .append(FieldDefs.REPLICA_SETS_URIS, uris)
              .append(FieldDefs.DISPLAY_LABEL, getDisplayLabel())
              .append(FieldDefs.USERNAME, getUsername())
              .append(FieldDefs.SSL_ENABLED, sslEnabled())
              .append(FieldDefs.CA_FILE, getCaFile())
              .append(FieldDefs.CA_FILE_PATH, getCaFilePath())
              .append(
                  FieldDefs.MONGODB_VERSION,
                  getMongoDBVersion().map(Version::getMaintenanceVersionString).orElse(null))
              .append(
                  FieldDefs.FEATURE_COMPATIBILITY_VERSION,
                  getFeatureCompatibilityVersion().orElse(null))
              .append(FieldDefs.SHARD_HORIZONS, shardHorizons)
              .append(FieldDefs.SHARD_IDS, shardIds)
              .append(FieldDefs.IS_SHARDED, isSharded().orElse(null))
              .append(FieldDefs.TOTAL_SIZE_BYTES, getTotalSizeBytes())
              .append(FieldDefs.GROUP_ID, getGroupId().orElse(null))
              .append(FieldDefs.CLUSTER_ID, getClusterId().orElse(null))
              .append(FieldDefs.CLUSTER_NAME, getClusterName().orElse(null))
              .append(FieldDefs.CONNECTION_STRING, getConnectionString().orElse(null));

      try {
        @GenEncryptMetadata(
            DB = "nds",
            Collection = "config.nds.liveImports",
            Field = "src.password")
        final String encPassword = EncryptionUtils.genEncryptStr(getPassword());
        doc.append(FieldDefs.PASSWORD, encPassword);
      } catch (final Exception e) {
        throw new IllegalStateException("Error encrypting password", e);
      }
      return doc;
    }

    public String getHostname() {
      return _hostname;
    }

    public List<String> getPrimariesHostnames() {
      return _primariesHostnames;
    }

    public List<String> getReplicaSetsUris() {
      return _replicaSetsUris;
    }

    public String getDisplayLabel() {
      return _displayLabel;
    }

    public String getUsername() {
      return _username;
    }

    public String getPassword() {
      return _password;
    }

    public boolean sslEnabled() {
      return _sslEnabled;
    }

    public Optional<List<String>> getShardHorizons() {
      return _shardHorizons;
    }

    public List<String> getShardIds() {
      return _shardIds;
    }

    /**
     * For {@code LiveImportType.PULL} users provide their CA file content, this method uses that
     * information
     *
     * @return the CA file content
     */
    public String getCaFile() {
      return _caFile;
    }

    /**
     * For {@code LiveImportType.PUSH} users provide the path where the automation agent can access
     * the CA file, this method uses that information
     *
     * @return the CA file path
     */
    public String getCaFilePath() {
      return _caFilePath;
    }

    public Optional<VersionUtils.Version> getMongoDBVersion() {
      return Optional.ofNullable(_mongoDBVersion);
    }

    public Optional<String> getFeatureCompatibilityVersion() {
      return Optional.ofNullable(_featureCompatibilityVersion);
    }

    public Optional<Boolean> isSharded() {
      return Optional.ofNullable(_isSharded);
    }

    public Double getTotalSizeBytes() {
      return _totalSizeBytes;
    }

    public Optional<ObjectId> getGroupId() {
      return Optional.ofNullable(_groupId);
    }

    public Optional<ObjectId> getClusterId() {
      return Optional.ofNullable(_clusterId);
    }

    public Optional<String> getClusterName() {
      return Optional.ofNullable(_clusterName);
    }

    public Optional<String> getConnectionString() {
      return Optional.ofNullable(_connectionString);
    }

    public String getClusterTypeName() {
      return this.isSharded().orElse(false) ? "SHARDED" : "REPLICASET";
    }

    public static class FieldDefs {
      public static final String HOSTNAME = "hostname";
      public static final String PRIMARIES_HOSTNAMES = "primariesHostnames";
      public static final String REPLICA_SETS_URIS = "replicaSetsUris";
      public static final String DISPLAY_LABEL = "displayLabel";
      public static final String USERNAME = "username";
      public static final String MIGRATION_TYPE = "migrationType";
      public static final String PASSWORD = "password";
      public static final String SSL_ENABLED = "sslEnabled";
      public static final String CA_FILE = "caFile";
      public static final String CA_FILE_PATH = "caFilePath";
      public static final String MONGODB_VERSION = "mongoDBVersion";
      public static final String FEATURE_COMPATIBILITY_VERSION = "featureCompatibilityVersion";
      public static final String CLUSTER_ID = "clusterId";
      public static final String CONFIG_SERVER_HORIZON = "configServerHorizon";
      public static final String SHARD_HORIZONS = "shardHorizons";
      public static final String SHARD_IDS = "shardIds";
      public static final String TOTAL_SIZE_BYTES = "totalSizeBytes";
      public static final String IS_SHARDED = "isSharded";
      public static final String GROUP_ID = "groupId";
      public static final String CLUSTER_NAME = "clusterName";
      public static final String CONNECTION_STRING = "connectionString";
    }
  }

  public static class Hosts {}

  public static class FieldDefs {
    public static final String ID = "_id";
    public static final String GROUP_ID = "groupId";
    public static final String CREATE_DATE = "createDate";
    public static final String STOP_DATE = "stopDate";
    public static final String EXPIRE_DATE = "expireDate";
    public static final String CUTOVER_REACHED_DATE = "cutoverReachedDate";
    public static final String PORTS = "ports";
    public static final String IS_CANCELLED = "isCancelled";
    public static final String IS_CUTOVER_COMPLETE = "isCutoverComplete";
    public static final String IS_ACKNOWLEDGED = "isAcknowledged";
    public static final String STATE = "state";
    public static final String ERROR_DETAILS = "errorDetails";
    public static final String VERIFICATION_ENABLED = "verificationEnabled";
    public static final String MONGO_MIRROR_STATUSES = "mongoMirrorStatuses";
    public static final String MONGOSYNC_STATUSES = "mongosyncStatuses";
    public static final String SHARD_INDEXES_NEEDING_RESTART = "shardIndexesNeedingRestart";
    public static final String SHARD_INDEXES_RESTART_COUNTS = "shardIndexesRestartCounts";
    public static final String SHARD_INDEXES_NEEDING_RESYNC = "shardIndexesNeedingResync";
    public static final String SOURCE = "src";
    public static final String DESTINATION = "dest";
    public static final String LIVE_IMPORT_LOCATION = "liveImportLocation";
    public static final String REQUESTER_USER_ID = "requesterUserId";
    public static final String DROP_ENABLED = "dropEnabled";
    public static final String MONGO_MIRROR_VERSION = "mongoMirrorVersion";
    public static final String MONGOSYNC_VERSION = "mongosyncVersion";
    public static final String REPLICATION_LAG_PER_SHARD = "replicationLagPerShard";
    public static final String OPLOG_BATCH_SIZE = "oplogBatchSize";
    public static final String COLLSTATS_THRESHOLD = "collStatsThreshold";
    public static final String IMPORT_SUCCEEDED_WITH_WARNING = "importSucceededWithWarning";
    public static final String PINNED_TO_SERVER = "pinnedToServer";
    public static final String TYPE = "type";
    public static final String MIGRATION_TOOL_TYPE = "migrationToolType";
    public static final String MIGRATION_TYPE = "migrationType";
    public static final String SHARD_INDEXES_NEEDING_MONGOSYNC_RESTART =
        "shardIndexesNeedingMongosyncRestart";
    public static final String MIGRATION_DATA_SIZE_BYTES = "migrationDataSizeBytes";
    public static final String ESTIMATED_INITIAL_SYNC_COMPLETE_DATE =
        "estimatedInitialSyncCompleteDate";
    public static final String INITIAL_SYNC_START_DATE = "initialSyncStartDate";
    public static final String OPLOG_SYNC_START_DATE = "oplogSyncStartDate";
    public static final String MONGOSYNC_SHARDING_PARAMS = "mongosyncShardingParams";
    public static final String CUTOVER_CLICKED_DATE = "cutoverClickedDate";
    public static final String ESTIMATED_DOWNTIME_SECONDS = "estimatedDowntimeSeconds";
    public static final String LAST_LOG_TIMESTAMP = "lastLogTimestamp";
    public static final String USER_WRITE_BLOCKING = "userWriteBlocking";
  }

  public static class Destination {
    private final String _clusterName;
    private final ObjectId _clusterUniqueId;
    private final String _username;
    private final String _password;
    private final VersionUtils.Version _mongoDBVersion;
    private final String _featureCompatibilityVersion;
    private final Boolean _isSharded;
    private ObjectId _groupId;

    /**
     * @deprecated Please use {@link
     *     com.xgen.module.liveimport.model.LiveImport.Destination#Destination(String pClusterName,
     *     ObjectId pClusterUniqueId, Boolean pIsSharded) } to declare if Destination is sharded or
     *     replica set
     */
    @Deprecated
    @VisibleForTesting
    public Destination(final String pClusterName, final ObjectId pClusterUniqueId) {
      this(pClusterName, pClusterUniqueId, null);
    }

    public Destination(
        final String pClusterName,
        final ObjectId pClusterUniqueId,
        final Boolean pIsSharded,
        final ObjectId pGroupId) {
      _clusterName = pClusterName;
      _clusterUniqueId = pClusterUniqueId;
      _username = null;
      _password = null;
      _mongoDBVersion = null;
      _featureCompatibilityVersion = null;
      _isSharded = pIsSharded;
      _groupId = pGroupId;
    }

    public Destination(
        final String pClusterName, final ObjectId pClusterUniqueId, final Boolean pIsSharded) {
      _clusterName = pClusterName;
      _clusterUniqueId = pClusterUniqueId;
      _username = null;
      _password = null;
      _mongoDBVersion = null;
      _featureCompatibilityVersion = null;
      _isSharded = pIsSharded;
      _groupId = null;
    }

    public Destination(
        final String pClusterName,
        final ObjectId pClusterUniqueId,
        final Boolean pIsSharded,
        final String pUsername,
        final String pPassword) {
      _clusterName = pClusterName;
      _clusterUniqueId = pClusterUniqueId;
      _username = pUsername;
      _password = pPassword;
      _mongoDBVersion = null;
      _featureCompatibilityVersion = null;
      _isSharded = pIsSharded;
      _groupId = null;
    }

    public Destination(final BasicDBObject pDBObject) {
      _clusterName = pDBObject.getString(FieldDefs.CLUSTER_NAME);
      _clusterUniqueId = pDBObject.getObjectId(FieldDefs.CLUSTER_UNIQUE_ID);
      _groupId = pDBObject.getObjectId(FieldDefs.GROUP_ID);
      _username = pDBObject.getString(FieldDefs.USERNAME);
      try {
        _password = EncryptionUtils.genDecryptStr(pDBObject.getString(FieldDefs.PASSWORD));
      } catch (final Exception e) {
        throw new IllegalStateException("Problem decrypting password. Error: " + e.getMessage(), e);
      }
      _mongoDBVersion =
          pDBObject.get(FieldDefs.MONGODB_VERSION) != null
              ? VersionUtils.parse(pDBObject.getString(FieldDefs.MONGODB_VERSION))
              : null;
      _featureCompatibilityVersion = pDBObject.getString(FieldDefs.FEATURE_COMPATIBILITY_VERSION);
      _isSharded =
          pDBObject.get(Destination.FieldDefs.IS_SHARDED) != null
              ? pDBObject.getBoolean(Destination.FieldDefs.IS_SHARDED)
              : null;
    }

    public DBObject toDBObject() {
      final BasicDBObject doc =
          new BasicDBObject()
              .append(FieldDefs.CLUSTER_NAME, getClusterName())
              .append(FieldDefs.GROUP_ID, getGroupId())
              .append(FieldDefs.CLUSTER_UNIQUE_ID, getClusterUniqueId())
              .append(FieldDefs.USERNAME, getUsername())
              .append(FieldDefs.PASSWORD, getPassword())
              .append(
                  FieldDefs.MONGODB_VERSION,
                  getMongoDBVersion().map(Version::getMaintenanceVersionString).orElse(null))
              .append(
                  FieldDefs.FEATURE_COMPATIBILITY_VERSION,
                  getFeatureCompatibilityVersion().orElse(null))
              .append(FieldDefs.IS_SHARDED, isSharded().orElse(null));
      try {
        @GenEncryptMetadata(
            DB = "nds",
            Collection = "config.nds.liveImports",
            Field = "dest.password")
        final String encPassword = EncryptionUtils.genEncryptStr(getPassword());
        doc.append(FieldDefs.PASSWORD, encPassword);
      } catch (final Exception e) {
        throw new IllegalStateException("Problem encrypting password. Error: " + e.getMessage(), e);
      }
      return doc;
    }

    public String getClusterName() {
      return _clusterName;
    }

    public ObjectId getGroupId() {
      return _groupId;
    }

    public ObjectId getClusterUniqueId() {
      return _clusterUniqueId;
    }

    public String getUsername() {
      return _username;
    }

    public String getPassword() {
      return _password;
    }

    public Optional<VersionUtils.Version> getMongoDBVersion() {
      return Optional.ofNullable(_mongoDBVersion);
    }

    public Optional<String> getFeatureCompatibilityVersion() {
      return Optional.ofNullable(_featureCompatibilityVersion);
    }

    public Optional<Boolean> isSharded() {
      return Optional.ofNullable(_isSharded);
    }

    public static class FieldDefs {
      public static final String CLUSTER_NAME = "clusterName";
      public static final String CLUSTER_UNIQUE_ID = "clusterUniqueId";
      public static final String USERNAME = "username";
      public static final String PASSWORD = "password";
      public static final String MONGODB_VERSION = "mongoDBVersion";
      public static final String FEATURE_COMPATIBILITY_VERSION = "featureCompatibilityVersion";
      public static final String IS_SHARDED = "isSharded";
      public static final String GROUP_ID = "groupId";
    }
  }

  public enum LiveImportType {
    PUSH,
    PULL
  }

  public enum MigrationToolType {
    MONGOMIRROR,
    MONGOSYNC
  }

  public enum MigrationType {
    SELF_TO_ATLAS,
    ATLAS_TO_ATLAS,
    LEGACY_MIGRATION;
    public static final MigrationType DEFAULT = LEGACY_MIGRATION;

    public static MigrationType fromString(String text) {
      if (text != null) {
        for (MigrationType b : MigrationType.values()) {
          if (text.equalsIgnoreCase(b.name())) {
            return b;
          }
        }
      }
      // Return the default value if the string is null or no match is found
      return DEFAULT;
    }
  }

  public abstract static class Builder<
      B extends Builder<B, T, D>, T extends LiveImport, D extends Destination> {
    protected ObjectId id;

    protected ObjectId groupId;

    protected Date createDate;

    protected Date stopDate;

    protected Date expireDate;

    protected Date cutoverReachedDate;

    protected boolean isCancelled;

    protected boolean isCutoverComplete;

    protected boolean isAcknowledged;

    protected State state;

    protected ErrorDetails errorDetails;

    protected Boolean verificationEnabled;

    protected List<? extends MigrationStatus<? extends Stage>> migrationStatuses;

    protected Set<Integer> shardIndexesNeedingRestart;

    protected Map<String, Integer> shardIndexesRestartCounts;

    protected Set<Integer> shardIndexesNeedingResync;

    protected Source src;

    protected Destination destination;

    protected ObjectId liveImportLocation;

    protected ObjectId requesterUserId;

    protected boolean dropEnabled;

    protected String migrationToolVersion;

    protected MigrationToolType migrationToolType;

    protected MigrationType migrationType;

    protected MigrationToolParameters migrationToolParameters;

    protected Map<Integer, Long> replicationLagPerShard;

    protected Double oplogBatchSize;

    protected Double collStatsThreshold;

    protected String importSucceededWithWarning;

    protected LiveImportType type;

    protected Long migrationDataSize;

    protected Date estimatedInitialSyncCompleteDate;

    protected Date initialSyncStartDate;

    protected Date oplogSyncStartDate;

    protected Date cutoverClickedDate;

    protected Integer estimatedDowntimeSeconds;

    protected boolean isSourceWriteBlockingEnabled;

    public Builder() {}

    public B id(final ObjectId id) {
      this.id = id;
      return self();
    }

    public B groupId(final ObjectId groupId) {
      this.groupId = groupId;
      return self();
    }

    public B createDate(final Date createDate) {
      this.createDate = createDate;
      return self();
    }

    public B stopDate(final Date stopDate) {
      this.stopDate = stopDate;
      return self();
    }

    public B expireDate(final Date expireDate) {
      this.expireDate = expireDate;
      return self();
    }

    public B cutoverReachedDate(final Date cutoverReachedDate) {
      this.cutoverReachedDate = cutoverReachedDate;
      return self();
    }

    public B isCancelled(final boolean isCancelled) {
      this.isCancelled = isCancelled;
      return self();
    }

    public B isCutoverComplete(final boolean isCutoverComplete) {
      this.isCutoverComplete = isCutoverComplete;
      return self();
    }

    public B isAcknowledged(final boolean isAcknowledged) {
      this.isAcknowledged = isAcknowledged;
      return self();
    }

    public B state(final State state) {
      this.state = state;
      return self();
    }

    public B errorDetails(final ErrorDetails errorDetails) {
      this.errorDetails = errorDetails;
      return self();
    }

    public B verificationEnabled(final Boolean verificationEnabled) {
      this.verificationEnabled = verificationEnabled;
      return self();
    }

    public B shardIndexesNeedingRestart(final Set<Integer> shardIndexesNeedingRestart) {
      this.shardIndexesNeedingRestart = shardIndexesNeedingRestart;
      return self();
    }

    public B shardIndexesRestartCounts(final Map<String, Integer> shardIndexesRestartCounts) {
      this.shardIndexesRestartCounts = shardIndexesRestartCounts;
      return self();
    }

    public B shardIndexesNeedingResync(final Set<Integer> shardIndexesNeedingResync) {
      this.shardIndexesNeedingResync = shardIndexesNeedingResync;
      return self();
    }

    public B src(final Source src) {
      this.src = src;
      return self();
    }

    public B destination(final D destination) {
      this.destination = destination;
      return self();
    }

    public B migrationType(final MigrationType migrationType) {
      this.migrationType = migrationType;
      return self();
    }

    public B liveImportLocation(final ObjectId liveImportLocation) {
      this.liveImportLocation = liveImportLocation;
      return self();
    }

    public B requesterUserId(final ObjectId requesterUserId) {
      this.requesterUserId = requesterUserId;
      return self();
    }

    public B dropEnabled(final boolean dropEnabled) {
      this.dropEnabled = dropEnabled;
      return self();
    }

    public B migrationToolVersion(final String migrationToolVersion) {
      this.migrationToolVersion = migrationToolVersion;
      return self();
    }

    public B replicationLagPerShard(final Map<Integer, Long> replicationLagPerShard) {
      this.replicationLagPerShard = replicationLagPerShard;
      return self();
    }

    public B oplogBatchSize(final Double oplogBatchSize) {
      this.oplogBatchSize = oplogBatchSize;
      return self();
    }

    public B collStatsThreshold(final Double collStatsThreshold) {
      this.collStatsThreshold = collStatsThreshold;
      return self();
    }

    public B importSucceededWithWarning(final String importSucceededWithWarning) {
      this.importSucceededWithWarning = importSucceededWithWarning;
      return self();
    }

    public B type(final LiveImportType type) {
      this.type = type;
      return self();
    }

    public B migrationDataSize(final Long pMigrationDataSize) {
      this.migrationDataSize = pMigrationDataSize;
      return self();
    }

    public B estimatedInitialSyncCompleteDate(final Date pDate) {
      this.estimatedInitialSyncCompleteDate = pDate;
      return self();
    }

    public B initialSyncStartDate(final Date pDate) {
      this.initialSyncStartDate = pDate;
      return self();
    }

    public B oplogSyncStartDate(final Date pDate) {
      this.oplogSyncStartDate = pDate;
      return self();
    }

    public B cutoverClickedDate(final Date pDate) {
      this.cutoverClickedDate = pDate;
      return self();
    }

    public B estimatedDowntimeSeconds(final Integer pEstimatedDowntimeSeconds) {
      this.estimatedDowntimeSeconds = pEstimatedDowntimeSeconds;
      return self();
    }

    public B isSourceWriteBlockingEnabled(final boolean isSourceWriteBlockingEnabled) {
      this.isSourceWriteBlockingEnabled = isSourceWriteBlockingEnabled;
      return self();
    }

    public abstract T build();

    protected abstract B self();
  }
}
