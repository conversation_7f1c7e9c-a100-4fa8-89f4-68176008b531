package com.xgen.module.account.res;

import static com.xgen.cloud.common.http.url._public.UrlUtils.clearRegistrationQueryParams;
import static com.xgen.cloud.common.http.url._public.UrlUtils.mergeQueryParamsAndBuild;
import static com.xgen.cloud.common.http.url._public.UrlUtils.urlEncode;
import static com.xgen.cloud.common.metrics._public.constants.MonitoringConstants.ATLAS_GROWTH_PROM_NAMESPACE;
import static com.xgen.cloud.common.metrics._public.constants.MonitoringConstants.IAM_PROM_NAMESPACE;
import static com.xgen.cloud.common.model._public.email.EmailValidation.isDevQaAllowedEmailAddress;
import static com.xgen.cloud.common.util._public.json.JsonUtils.getJSONParam;
import static com.xgen.cloud.partnerintegrations.common._public.model.VercelUserToken.NOT_PROVIDED;
import static com.xgen.cloud.user._public.model.AppUser.PHONE_NUMBER_FIELD;
import static com.xgen.module.account.svc.AccountMultiFactorAuthSvcImpl.REMEMBER_DEVICE_COOKIE_EXPIRE_IN_SECONDS;
import static com.xgen.module.account.svc.AccountMultiFactorAuthSvcImpl.REMEMBER_DEVICE_COOKIE_FORMAT;
import static com.xgen.module.account.svc.AccountMultiFactorAuthSvcImpl.REMEMBER_DEVICE_COOKIE_NAME;
import static com.xgen.module.account.svc.AccountMultiFactorAuthSvcImpl.SHOULD_NOT_REMEMBER_DEVICE_FLAG;
import static com.xgen.module.account.svc.AccountSessionSvc.ACCOUNT_AUTH_COOKIE;
import static com.xgen.module.account.svc.OAuthSvc.generateErrorUrl;
import static com.xgen.svc.mms.svc.user.UserLoginSvc.createQuery;
import static com.xgen.svc.mms.svc.user.UserLoginSvc.sanitizeFromURI;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.okta.sdk.resource.model.AuthenticationProviderType;
import com.okta.sdk.resource.model.User;
import com.okta.sdk.resource.model.UserCredentials;
import com.xgen.cloud.access.authn._public.model.State;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.recaptcha._public.action.ReCaptchaAction;
import com.xgen.cloud.access.recaptcha._public.svc.RecaptchaSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.auditinfosvc._public.svc.AuditInfoSvc;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.constants._public.model.partners.PartnerType;
import com.xgen.cloud.common.driverwrappers._public.common.DbMaintenanceException;
import com.xgen.cloud.common.filter._public.FilterUtils;
import com.xgen.cloud.common.hometemplate._public.svc.HomeTemplateSvc;
import com.xgen.cloud.common.http.url._public.UrlUtils;
import com.xgen.cloud.common.logging._public.context.LoggingContext;
import com.xgen.cloud.common.model._public.email.EmailValidation;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.passwordreset._public.model.AuthType;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.security._public.model.IgnoreGenEncryptMetadata;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.common.util._public.cookie.CookieHelper;
import com.xgen.cloud.common.util._public.cookie.CookieHelper.CookieWriter;
import com.xgen.cloud.common.util._public.cookie.SameSite;
import com.xgen.cloud.common.util._public.util.EnumUtils;
import com.xgen.cloud.common.util._public.util.UserAgentUtils;
import com.xgen.cloud.externalanalytics._public.model.AzureNativeUserPasswordSetEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.federation._public.errorcode.FederationErrorCode;
import com.xgen.cloud.federation._public.svc.FederationSettingsSvc;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited.Type;
import com.xgen.cloud.organization._public.model.VercelIntegrationInfo;
import com.xgen.cloud.organization._public.svc.VercelIntegrationSvc;
import com.xgen.cloud.partnerintegrations.common._public.model.IntegrationType;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import com.xgen.cloud.partnerintegrations.common._public.model.TentativePartnerAccountLink;
import com.xgen.cloud.partnerintegrations.common._public.model.VercelUserToken;
import com.xgen.cloud.partnerintegrations.common._public.svc.PartnerIntegrationEmailSvc;
import com.xgen.cloud.partnerintegrations.common._public.svc.TentativePartnerAccountLinkSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelResourceInfo;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelAuthSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeAccountSvc;
import com.xgen.cloud.partners.registration._public.constants.MarketplaceRegistrationConstants;
import com.xgen.cloud.partners.registration._public.svc.PartnerMarketplaceRegistrationSvc;
import com.xgen.cloud.partners.registration._public.svc.PartnerMarketplaceRegistrationSvcFactory;
import com.xgen.cloud.passwordreset._public.svc.PasswordResetSvc;
import com.xgen.cloud.user._private.svc.UserSvcOkta;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.PartnerIntegrationUserSource;
import com.xgen.cloud.user._public.model.UserRegistrationForm;
import com.xgen.cloud.user._public.svc.LegacyUsernameSvc;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.module.account.AccountAuditLogEntry;
import com.xgen.module.account.annotation.AccountUiCall;
import com.xgen.module.account.common.AccountAuthResourceConstants;
import com.xgen.module.account.common.AuthRedirectPages;
import com.xgen.module.account.common.VercelPartnerLinkMetrics;
import com.xgen.module.account.model.AccountSession;
import com.xgen.module.account.model.AccountUser;
import com.xgen.module.account.model.IdpOAuthMethod;
import com.xgen.module.account.model.SessionTokenOAuthMethod;
import com.xgen.module.account.okta.common.OktaErrorCode;
import com.xgen.module.account.okta.model.OktaAuthTransaction.Status;
import com.xgen.module.account.okta.model.OktaLoginTransaction;
import com.xgen.module.account.okta.model.OktaMfaAuthTransaction;
import com.xgen.module.account.okta.model.factors.FactorType;
import com.xgen.module.account.okta.model.factors.OktaMfaFactor;
import com.xgen.module.account.okta.model.tokenhook.OktaSessionData;
import com.xgen.module.account.okta.model.tokenhook.TokenHookCommand;
import com.xgen.module.account.okta.model.tokenhook.TokenHookOperation;
import com.xgen.module.account.okta.model.tokenhook.TokenHookRequest;
import com.xgen.module.account.okta.model.tokenhook.TokenHookResponse;
import com.xgen.module.account.res.view.OktaMfaFactorVerificationRequestView;
import com.xgen.module.account.res.view.VercelNativeLinkConfirmationView;
import com.xgen.module.account.svc.AccountMultiFactorAuthSvc;
import com.xgen.module.account.svc.AccountSessionSvc;
import com.xgen.module.account.svc.AccountUserMfaHistorySvc;
import com.xgen.module.account.svc.AccountUserSvc;
import com.xgen.module.account.svc.OAuthSvc;
import com.xgen.module.account.view.AcceptTosView;
import com.xgen.module.iam.config.IamAppSettings;
import com.xgen.svc.common.logger.AccessLogCode;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.form.UserLoginForm;
import com.xgen.svc.mms.model.auth.UiAuthMethod;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.svc.analytics.EloquaSvc;
import com.xgen.svc.mms.svc.user.UserLoginSvc;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriBuilder;
import jakarta.ws.rs.core.UriInfo;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import net.logstash.logback.argument.StructuredArguments;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/account")
@Singleton
public class AccountAuthResource extends BaseAccountAppResource {

  private static final Logger LOG = LoggerFactory.getLogger(AccountAuthResource.class);

  private static final Counter AWS_MP_REDIRECT_SUCCESS_COUNTER =
      Counter.build()
          .name("accountAuthResource_aws_mp_redirect_success_total")
          .help(
              "Account Auth Resource: Count of successful redirects after resolving AWS customer"
                  + " data")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private static final Counter AWS_MP_REDIRECT_RESOLVE_CUSTOMER_ERROR_COUNTER =
      Counter.build()
          .name("accountAuthResource_aws_mp_redirect_resolve_customer_error_total")
          .help(
              "Account Auth Resource: Count of errors that occurred while fetching AWS customer"
                  + " data")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private static final Counter AWS_MP_REDIRECT_MISSING_TOKEN_ERROR_COUNTER =
      Counter.build()
          .name("accountAuthResource_aws_mp_redirect_missing_token_error_total")
          .help(
              "Account Auth Resource: Count of errors that occurred due to temp token not being"
                  + " included in POST request from AWS")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private static final Counter GCP_MP_REDIRECT_SUCCESS_COUNTER =
      Counter.build()
          .name("accountAuthResource_gcp_mp_redirect_success_total")
          .help(
              "Account Auth Resource: Count of successful redirects after resolving GCP account"
                  + " data")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private static final Counter GCP_MP_REDIRECT_ALREADY_SUBSCRIBED_COUNTER =
      Counter.build()
          .name("accountAuthResource_gcp_mp_redirect_already_subscribed_total")
          .help(
              "Account Auth Resource: Count of redirects requests for gcp accounts that were"
                  + " already subscribed.")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private static final Counter GCP_MP_REDIRECT_MISSING_TOKEN_ERROR =
      Counter.build()
          .name("accountAuthResource_gcp_mp_redirect_missing_token_error_total")
          .help(
              "Account Auth Resource: Count of errors that occurred due to temp token not being"
                  + " included in POST request from GCP")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private static final Counter GCP_MP_REDIRECT_TOKEN_VERIFICATION_FAILURE =
      Counter.build()
          .name("accountAuthResource_gcp_mp_redirect_token_verification_error_total")
          .help(
              "Account Auth Resource: Count of errors that occurred while verifying token"
                  + " included in POST request from GCP")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private static final Counter OKTA_ADD_SESSION_ID_CALLED_COUNTER =
      Counter.build()
          .name("accountAuthResource_okta_add_session_id_called_total")
          .help(
              "Account Auth Resource: Count of times the addSessionId token inline hook endpoint is"
                  + " called")
          .namespace(IAM_PROM_NAMESPACE)
          .register();

  private static final Counter OKTA_ADD_SESSION_ID_SESSION_DATA_NULL_COUNTER =
      Counter.build()
          .name("accountAuthResource_okta_add_session_id_session_data_null_total")
          .help(
              "Account Auth Resource: Count of null Okta session data objects passed into request")
          .namespace(IAM_PROM_NAMESPACE)
          .register();

  private static final Counter OKTA_ADD_SESSION_ID_NULL_COUNTER =
      Counter.build()
          .name("accountAuthResource_okta_add_session_id_null_total")
          .help("Account Auth Resource: Count of null Okta session IDs passed into request")
          .namespace(IAM_PROM_NAMESPACE)
          .register();

  private static final Counter OKTA_ENROLL_MFA_ON_LOGIN_ATTEMPT_COUNTER =
      Counter.build()
          .name("accountAuthResource_okta_enroll_mfa_on_login_attempt_total")
          .help(
              "Account Auth Resource: Count of attempts to enroll user in email MFA factor and"
                  + " prompt with MFA challenge on login")
          .namespace(IAM_PROM_NAMESPACE)
          .register();

  private static final Counter OKTA_ENROLL_MFA_ON_LOGIN_FAILURE_COUNTER =
      Counter.build()
          .name("accountAuthResource_okta_enroll_mfa_on_login_failure_total")
          .help(
              "Account Auth Resource: Count of failures to prompt user with MFA challenge after"
                  + " enrolling email MFA factor on login")
          .namespace(IAM_PROM_NAMESPACE)
          .register();

  private static final Counter OKTA_ENROLL_MFA_ON_LOGIN_SUCCESS_COUNTER =
      Counter.build()
          .name("accountAuthResource_okta_enroll_mfa_on_login_success_total")
          .help(
              "Account Auth Resource: Count of successful attempts to enroll user in email MFA"
                  + " factor and prompt with MFA challenge on login")
          .namespace(IAM_PROM_NAMESPACE)
          .register();

  private static final Counter OKTA_MFA_RESULT_TOTAL =
      Counter.build()
          .name("accountAuthResource_okta_mfa_result_total")
          .help(
              "Counts of MFA skips or requires labelled by expected result. If "
                  + "mfa_expected is true, then result should NOT be SUCCESS")
          .namespace(IAM_PROM_NAMESPACE)
          .labelNames("mfa_expected", "result")
          .register();

  private final UserSvc userSvc;
  private final UserLoginSvc userLoginSvc;
  private final AccountUserSvc accountUserSvc;
  private final OAuthSvc oAuthSvc;
  private final LegacyUsernameSvc legacyUsernameSvc;
  private final EloquaSvc eloquaSvc;
  private final FederationSettingsSvc federationSettingsSvc;
  private final PasswordResetSvc passwordResetSvc;
  private final VercelIntegrationSvc vercelIntegrationSvc;
  private final VercelAuthSvc vercelAuthSvc;
  private final PartnerMarketplaceRegistrationSvcFactory partnerMarketplaceRegistrationSvcFactory;
  private final AccountSessionSvc accountSessionSvc;
  private final AccountMultiFactorAuthSvc accountMultiFactorAuthSvc;
  private final RecaptchaSvc recaptchaSvc;
  private final AccountUserMfaHistorySvc accountUserMfaHistorySvc;
  private final PartnerIntegrationEmailSvc partnerIntegrationEmailSvc;
  private final SegmentEventSvc segmentEventSvc;
  private final AuthzSvc authzSvc;
  private final AuditInfoSvc auditInfoSvc;
  private final VercelNativeAccountSvc vercelNativeAccountSvc;
  private final TentativePartnerAccountLinkSvc tentativePartnerAccountLinkSvc;
  private final ObjectMapper objectMapper;

  @Inject
  public AccountAuthResource(
      UserSvc pUserSvc,
      UserLoginSvc pUserLoginSvc,
      AccountUserSvc pAccountUserSvc,
      OAuthSvc pOAuthSvc,
      LegacyUsernameSvc pLegacyUsernameSvc,
      EloquaSvc pEloquaSvc,
      FederationSettingsSvc pFederationSettingsSvc,
      PasswordResetSvc pPasswordResetSvc,
      AppSettings pAppSettings,
      IamAppSettings pIamAppSettings,
      HomeTemplateSvc pHomeTemplateSvc,
      VercelIntegrationSvc pVercelIntegrationSvc,
      VercelAuthSvc pVercelAuthSvc,
      PartnerMarketplaceRegistrationSvcFactory pPartnerMarketplaceRegistrationSvcFactory,
      AccountMultiFactorAuthSvc pAccountMultiFactorAuthSvc,
      AccountSessionSvc pAccountSessionSvc,
      RecaptchaSvc pRecaptchaSvc,
      AccountUserMfaHistorySvc pAccountUserMfaHistorySvc,
      PartnerIntegrationEmailSvc pPartnerIntegrationEmailSvc,
      SegmentEventSvc pSegmentEventSvc,
      AuthzSvc pAuthzSvc,
      AuditInfoSvc pAuditInfoSvc,
      VercelNativeAccountSvc pVercelNativeAccountSvc,
      TentativePartnerAccountLinkSvc pTentativePartnerAccountLinkSvc,
      ObjectMapper pObjectMapper) {
    super(pAppSettings, pIamAppSettings, pHomeTemplateSvc);
    userSvc = pUserSvc;
    userLoginSvc = pUserLoginSvc;
    accountUserSvc = pAccountUserSvc;
    oAuthSvc = pOAuthSvc;
    legacyUsernameSvc = pLegacyUsernameSvc;
    eloquaSvc = pEloquaSvc;
    federationSettingsSvc = pFederationSettingsSvc;
    passwordResetSvc = pPasswordResetSvc;
    vercelIntegrationSvc = pVercelIntegrationSvc;
    vercelAuthSvc = pVercelAuthSvc;
    partnerMarketplaceRegistrationSvcFactory = pPartnerMarketplaceRegistrationSvcFactory;
    accountMultiFactorAuthSvc = pAccountMultiFactorAuthSvc;
    accountSessionSvc = pAccountSessionSvc;
    recaptchaSvc = pRecaptchaSvc;
    accountUserMfaHistorySvc = pAccountUserMfaHistorySvc;
    partnerIntegrationEmailSvc = pPartnerIntegrationEmailSvc;
    segmentEventSvc = pSegmentEventSvc;
    authzSvc = pAuthzSvc;
    auditInfoSvc = pAuditInfoSvc;
    vercelNativeAccountSvc = pVercelNativeAccountSvc;
    tentativePartnerAccountLinkSvc = pTentativePartnerAccountLinkSvc;
    objectMapper = pObjectMapper;
  }

  @GET
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response index(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/login")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response loginPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/login/mfa")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response mfaPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/login/vercel")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response vercelLoginPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/oauth")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response oauth(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/tos")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response tosPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/register")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response registerPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/register/cli")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response registerViaCliPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/register/success")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response registrationSuccessPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/reset/password")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response resetPasswordStartPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/reset/password/{tempId}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response resetPasswordEndPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/reset/university/password")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response resetUniversityPasswordPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/reset/mfa/{planType}/{tempId}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response mfaResetPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/support/reset/password/redirect")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false)
  public Response getPasswordResetPageForSupport(@QueryParam("email") final String email)
      throws URISyntaxException {
    final URIBuilder uriBuilder = new URIBuilder("/account/reset/password");
    if (StringUtils.trimToNull(email) != null) {
      uriBuilder.addParameter("email", email);
    }
    return seeOther(uriBuilder.build().toString());
  }

  @GET
  @Path("/security/mfa")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response securityMfaSubresources(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/auth/verify")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response verifyAccountOnSsoLoginPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/connect")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response deviceAuthPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/connect/success")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response deviceAuthSuccessPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/encourage/mfa")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response encourageMfaPage(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/vercel/{subResources: .+}")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response vercelPages(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  /**
   * Note this resource method definition has a general catch-all regex used for path matching. This
   * is to allow pushState navigation in the account UI and avoid using fragments for nav. Because
   * this regex is so general, it's important that this endpoint require auth and that any
   * unauthenticated endpoints are explicitly defined instead of just falling under this general
   * regex. This is because there is a process that analyzes our unauthenticated endpoints and
   * authenticate requests at our loadbalancer that will allow almost any request if this regex gets
   * evaluated first. Making this an authenticated endpoints removes that concern.
   */
  @GET
  @Path("{subResources: .+}")
  @Produces({MediaType.TEXT_HTML})
  @AccountUiCall
  public Response indexSubResources(
      @Context RequestParams pRequestParams, @Context HttpServletRequest pRequest) {
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/register/vercel")
  @Produces({MediaType.TEXT_HTML})
  @UiCall(auth = false, account = true)
  public Response registerVercel(
      @Context RequestParams pRequestParams,
      @Context HttpServletRequest pRequest,
      @QueryParam(AccountAuthResourceConstants.CODE_PARAM_VALUE) String pCode,
      @QueryParam(AccountAuthResourceConstants.VERCEL_CONFIGURATION_ID_QUERY_PARAM)
          String pConfigurationId,
      @QueryParam(AccountAuthResourceConstants.VERCEL_NEXT_QUERY_PARAM) String pNext,
      @QueryParam("teamId") @DefaultValue("") String pTeamId
      // only present if integration is at team level on Vercel side
      ) {
    if (appSettings.isVercelIntegrationEnabled()) {
      if (StringUtils.isNotBlank(pCode)
          && StringUtils.isNotBlank(pConfigurationId)
          && StringUtils.isNotBlank(pNext)) {

        try {
          VercelIntegrationInfo vercelIntegrationInfo =
              vercelIntegrationSvc.getIntegrationInfo(pCode, pConfigurationId, pTeamId);
          pRequestParams.setVercelUserId(vercelIntegrationInfo.getUserId());
          pRequestParams.setVercelEmail(vercelIntegrationInfo.getEmail());
          pRequestParams.setVercelTeamName(vercelIntegrationInfo.getTeamName());
          pRequestParams.setVercelTeamId(vercelIntegrationInfo.getTeamId());
        } catch (SvcException pE) {
          // swallowing the exception here for graceful error handling on client side
          return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
        }
      }
    }
    return serveSinglePageApp(pRequestParams, UserAgentUtils.isMobileBrowser(pRequest));
  }

  @GET
  @Path("/params")
  @Produces({MediaType.APPLICATION_JSON})
  @AccountUiCall
  public Response params(@Context HttpServletRequest pRequest, @Context AccountUser pAccountUser) {
    RequestParams params = getRequestParams(pRequest);

    String csrfTime = String.valueOf(System.currentTimeMillis());
    String csrfToken = FilterUtils.generateCsrfToken(pAccountUser.getOktaUserId(), csrfTime);

    params.setCsrfTime(csrfTime);
    params.setCsrfToken(csrfToken);

    return Response.ok(params).build();
  }

  @POST
  @Path("/addSessionId")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response getSessionIdForIdToken(TokenHookRequest pTokenHookRequest) {
    // Note: this endpoint does not authenticate who is making the request since it doesn't return
    // any information from MMS, just information from the service making the request. Inline hooks
    // can authenticate a request actually comes from Okta, but that would be unnecessary here
    OKTA_ADD_SESSION_ID_CALLED_COUNTER.inc();
    OktaSessionData oktaSessionData =
        pTokenHookRequest
            .getTokenHookRequestData()
            .getTokenHookRequestContext()
            .getOktaSessionData();
    if (oktaSessionData == null) {
      // This should never happen and would indicate the token inline hook is being invoked by a
      // call to Okta we're not accounting for
      // TODO: TH CLOUDP-304040 Convert to error log once token inline hook is used in practice
      LOG.info("No Okta session data found in request");
      OKTA_ADD_SESSION_ID_SESSION_DATA_NULL_COUNTER.inc();
      return SimpleApiResponse.badRequest(CommonErrorCode.BAD_REQUEST).build();
    }

    String oktaSessionId = oktaSessionData.getId();
    if (oktaSessionId == null) {
      // This should never happen and would indicate an issue or change in the request body that
      // Okta is passing in
      // TODO: TH CLOUDP-304040 Convert to error log once token inline hook is used in practice
      LOG.info("No Okta session id found in request");
      OKTA_ADD_SESSION_ID_NULL_COUNTER.inc();
      return SimpleApiResponse.badRequest(CommonErrorCode.BAD_REQUEST).build();
    }
    List<TokenHookCommand> commands =
        List.of(
            new TokenHookCommand(
                TokenHookCommand.Type.ID_PATCH,
                List.of(
                    new TokenHookOperation(
                        TokenHookOperation.Type.ADD, "/claims/sessionId", oktaSessionId))));
    return Response.ok(new TokenHookResponse(commands)).build();
  }

  @POST
  @Path("/auth")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response auth(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AuditInfo pAuditInfo,
      UserLoginForm pUserLoginForm)
      throws Exception {
    // Make sure to log the authentication attempt before an error is triggered
    LOG.info(
        AccountAuditLogEntry.AUTH_ATTEMPT_LOG_TEMPLATE,
        AccountAuditLogEntry.formatUsername(pUserLoginForm.getUsername()),
        AccountAuditLogEntry.formatRemoteAddress(pRequest.getRemoteAddr()),
        AccountAuditLogEntry.formatAuthMethod(UiAuthMethod.OKTA));
    LoggingContext.with(AccessLogCode.USER_ID, pUserLoginForm.getUsername());

    if (appSettings.getAppEnv().isDevOrQA()
        || appSettings.getAppEnv().isStage()
        || appSettings.getAppEnv().isDevGov()
        || appSettings.getAppEnv().isQAGov()) {
      if (!EmailValidation.isDevQaAllowedEmailAddress(pUserLoginForm.getUsername())) {
        LOG.info("Preventing login attempt from username={}", pUserLoginForm.getUsername());
        throw new SvcException(AppUserErrorCode.INVALID_USERNAME);
      }
    }

    if (iamAppSettings.isDbMaintenanceErrorEnabled()) {
      throw new DbMaintenanceException();
    }

    checkIsUserAgentBlocked(pRequest);
    validateRecaptcha(
        pRequest,
        pResponse,
        pUserLoginForm,
        ReCaptchaAction.LOGIN,
        iamAppSettings.getRecaptchaEnterpriseLoginEnabled(),
        iamAppSettings.getRecaptchaLoginMinScore());

    String username = pUserLoginForm.getUsername();

    if (pUserLoginForm.isSsoDebug()) {
      if (!federationSettingsSvc.isUserPermittedToDebugSso(pUserLoginForm.getIdpId(), username)) {
        throw new SvcException(FederationErrorCode.SSO_DEBUG_PROHIBITED);
      }
      LOG.info(
          "SSO debug enabled for {} at {}",
          AccountAuditLogEntry.formatUsername(username),
          AccountAuditLogEntry.formatRemoteAddress(pRequest.getRemoteAddr()));
    }

    // If the username is associated with an account that is currently soft-deleted (in the
    // process of being deleted via GDPR erasure), show the default invalid user auth error.
    // This extra validation here (on top of the one in /oidc/callback) is necessary to avoid
    // showing the "Verify your identity" page that would be otherwise displayed to some users
    // due to the auto-enrolled email verification method that might happen during account deletion
    if (accountUserSvc.isAccountBeingDeleted(pUserLoginForm.getUsername())) {
      throw new SvcException(AppUserErrorCode.CANNOT_REGISTER_ACCOUNT_BEING_DELETED);
    }

    try {
      String deviceToken = null;
      var mfaRequiredExpected = true;
      if (iamAppSettings.getMfaRememberDeviceEnabled()) {
        Optional<String> currentValidDeviceToken =
            accountMultiFactorAuthSvc.getDecryptedDeviceTokenIfShouldRemember(
                CookieHelper.getCookieValue(pRequest, REMEMBER_DEVICE_COOKIE_NAME), username);

        mfaRequiredExpected = currentValidDeviceToken.isEmpty();

        // create new device token to remember MFA device if current token is invalid or doesn't
        // exist
        deviceToken = currentValidDeviceToken.orElse(new ObjectId().toString());
        if (currentValidDeviceToken.isEmpty()) {
          @IgnoreGenEncryptMetadata(
              description = "stored in cookie on user's browser and not persisted to database")
          String encryptedDeviceToken =
              EncryptionUtils.genEncryptStr(
                  String.format(
                      REMEMBER_DEVICE_COOKIE_FORMAT, deviceToken, SHOULD_NOT_REMEMBER_DEVICE_FLAG));

          CookieWriter.builder()
              .name(REMEMBER_DEVICE_COOKIE_NAME)
              .value(encryptedDeviceToken)
              .response(pResponse)
              .path("/")
              .maxAge(REMEMBER_DEVICE_COOKIE_EXPIRE_IN_SECONDS)
              .secure(true)
              .httpOnly(true)
              .build()
              .write();
        }
      }

      return SimpleApiResponse.ok()
          .customField(
              AccountAuthResourceConstants.LOGIN_REDIRECT_PROPERTY,
              getLoginRedirect(
                  pResponse,
                  username,
                  pUserLoginForm.getPassword(),
                  pAuditInfo,
                  pUserLoginForm.getClientState(),
                  deviceToken,
                  mfaRequiredExpected))
          .build();
    } catch (SvcException e) {
      // if errorCode is INSECURE_PASSWORD, take the user to reset password page
      if (e.getErrorCode().equals(OktaErrorCode.INSECURE_PASSWORD)) {
        LOG.debug("Okta primary authentication failed: {}:{}", e.getErrorCode(), e.getMessage());
        String token =
            userLoginSvc.createAccountPasswordResetTokenPath(username, pRequest.getRemoteAddr());
        return SimpleApiResponse.badRequest(e.getErrorCode()).resource(token).build();
      } else {
        if (e.getErrorCode().equals(OktaErrorCode.INVALID_USER_AUTH)) {
          boolean usernameExists = accountUserSvc.findByUsername(username).isPresent();

          boolean isSocialIdpUser = false;
          try {
            if (usernameExists) {
              // TODO: MB CLOUDP-320730 avoid this lookup by using new data on account user
              //  https://jira.mongodb.org/browse/CLOUDP-320730
              final User oktaUser = accountUserSvc.getOktaUser(username);
              final UserCredentials oktaUserCredentials = oktaUser.getCredentials();
              isSocialIdpUser =
                  oktaUserCredentials != null
                      && oktaUserCredentials.getProvider() != null
                      && oktaUserCredentials.getProvider().getType()
                          == AuthenticationProviderType.SOCIAL;
            }
          } catch (Exception getOktaUserE) {
            LOG.info(
                "accountUserSvc.getOktaUser failed for {}: ",
                AccountAuditLogEntry.formatUsername(username),
                getOktaUserE);
          }

          LOG.info(
              "Invalid login attempt for {}, {}, {}, {}",
              AccountAuditLogEntry.formatUsername(username),
              AccountAuditLogEntry.formatRemoteAddress(pRequest.getRemoteAddr()),
              usernameExists ? "" : kv("error", "username does not exist"),
              StructuredArguments.kv("isSocialIdpUser", isSocialIdpUser));
        } else {
          LOG.debug("Okta primary authentication failed: {}:{}", e.getErrorCode(), e.getMessage());
        }
        return SimpleApiResponse.badRequest(e.getErrorCode()).build();
      }
    }
  }

  @GET
  @Path("/auth/mfa/{stateToken}")
  @Produces(MediaType.APPLICATION_JSON)
  @AccountUiCall(auth = false)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "getAuthMfaState",
      types = {Type.IP})
  public Response getAuthMfaState(@PathParam("stateToken") String pStateToken) throws Exception {
    OktaLoginTransaction transaction = getUserSvcOkta().getAuthMfaState(pStateToken);
    return Response.ok(transaction).build();
  }

  /**
   * verifyAuthMfa is used to both 1) issue an MFA challenge and 2) verify the completion of an MFA
   * challenge. Therefore, if the transaction's status is not equal to
   * OktaMfaAuthTransaction.Status.SUCCESS, the transaction's status could still be valid so we
   * should return it.
   *
   * @param pRequest
   * @param pResponse
   * @param pVerificationRequest
   * @return Response
   * @throws Exception
   */
  @POST
  @Path("/auth/mfa/verify")
  @Produces(MediaType.APPLICATION_JSON)
  @AccountUiCall(auth = false)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "verifyAuthMfa",
      types = {Type.IP})
  public Response verifyAuthMfa(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Valid OktaMfaFactorVerificationRequestView pVerificationRequest)
      throws Exception {
    String requestOktaUserId = null;
    Map<String, Object> clientState = pVerificationRequest.getClientState();
    String socialProvider = getSocialProviderName(clientState);

    if (clientState != null
        && clientState.containsKey(AccountAuthResourceConstants.OKTA_USER_ID_PARAM)) {
      requestOktaUserId =
          clientState.get(AccountAuthResourceConstants.OKTA_USER_ID_PARAM).toString();
    }

    // If the OTP is for an SMS factor, verify that the user is not rate limited,
    // create a document to track their request, and update their backoff level
    int updatedBackoffLevel = 0;
    if (shouldEnforceBackoffLevelSystem(
        requestOktaUserId,
        pVerificationRequest.getFactorType(),
        pVerificationRequest.getPasscode())) {
      updatedBackoffLevel =
          accountUserMfaHistorySvc.applyLoginRateLimitsAndTrackRequest(requestOktaUserId);
    }

    OktaMfaAuthTransaction transaction =
        accountMultiFactorAuthSvc.verifyAuthMfa(pVerificationRequest);
    String transactionOktaUsername = transaction.getUserLogin();

    validateMfaRequestAndSetCountryCode(
        transaction,
        transactionOktaUsername,
        requestOktaUserId,
        pVerificationRequest.getFactorType(),
        pVerificationRequest.getFactorId(),
        pVerificationRequest.getPasscode());

    if (transaction.getStatus() == OktaMfaAuthTransaction.Status.SUCCESS) {
      // Upon a successful MFA verification (with any factor type),
      // reset the user's SMS backoff level
      if (requestOktaUserId != null) {
        accountUserMfaHistorySvc.createBackoffLevelReset(requestOktaUserId, FactorType.SMS);
      }

      LOG.info(
          "Successful MFA verification from {} at {} with rememberDevice {}",
          AccountAuditLogEntry.formatUsername(transactionOktaUsername),
          AccountAuditLogEntry.formatRemoteAddress(pRequest.getRemoteAddr()),
          pVerificationRequest.rememberDevice());

      UiAuthMethod socialProviderValue = null;
      if (!socialProvider.isEmpty()) {
        try {
          accountUserSvc.convertUserToSocial(requestOktaUserId);
          LOG.info(
              "User {} successfully connected their MongoDB and {} accounts after verifying"
                  + " their password",
              AccountAuditLogEntry.formatUsername(transactionOktaUsername),
              socialProvider);

          socialProviderValue =
              EnumUtils.tryValueOf(UiAuthMethod.class).apply(socialProvider.toUpperCase());
        } catch (SvcException e) {
          LOG.debug(
              "Failed to convert user to social after verifying auth for {}: {}:{}",
              AccountAuditLogEntry.formatUsername(transactionOktaUsername),
              e.getErrorCode(),
              e.getMessage(),
              e);
          return SimpleApiResponse.badRequest(e.getErrorCode()).build();
        }
      }

      if (iamAppSettings.getMfaRememberDeviceEnabled()) {
        if (pVerificationRequest.rememberDevice()) {
          Optional<String> updatedCookieValue =
              accountMultiFactorAuthSvc.updateRememberAndGetEncryptedDeviceToken(
                  CookieHelper.getCookieValue(pRequest, REMEMBER_DEVICE_COOKIE_NAME),
                  transactionOktaUsername);
          updatedCookieValue.ifPresent(
              cookieValue ->
                  CookieWriter.builder()
                      .name(REMEMBER_DEVICE_COOKIE_NAME)
                      .value(cookieValue)
                      .response(pResponse)
                      .path("/")
                      .maxAge(REMEMBER_DEVICE_COOKIE_EXPIRE_IN_SECONDS)
                      .secure(true)
                      .httpOnly(true)
                      .build()
                      .write());
        } else {
          CookieWriter.builder()
              .name(REMEMBER_DEVICE_COOKIE_NAME)
              .value("") // rewrite to clear any previous remember-mfa-device cookies
              .response(pResponse)
              .path("/")
              .maxAge(0) // set cookie to immediately expire
              .secure(true)
              .httpOnly(true)
              .build()
              .write();
        }
      }

      // We want to use the user's legacy username if they have one instead of their Okta
      // username. Otherwise, our OIDC init flow will think there is a user session mismatch
      String normalizedUsername =
          legacyUsernameSvc
              .getLegacyUsernameFromOktaUsername(transactionOktaUsername)
              .orElse(transactionOktaUsername)
              .toLowerCase();

      return SimpleApiResponse.ok()
          .customField(
              AccountAuthResourceConstants.LOGIN_REDIRECT_PROPERTY,
              oAuthSvc.getAccountLoginRedirect(
                  normalizedUsername,
                  pResponse,
                  // If a user is linking to a social provider, we redirect them to that provider
                  // rather than utilize the existing session token.
                  // This allows the user to be treated as SOCIAL rather than OKTA in OIE.
                  socialProviderValue != null
                      ? new IdpOAuthMethod(accountUserSvc.getSocialIdpId(socialProviderValue))
                      : new SessionTokenOAuthMethod(transaction.getSessionToken()),
                  pVerificationRequest.getClientState()))
          .build();
    }

    // After an SMS OTP is successfully sent, rate limit the user for a time
    // corresponding to their updated backoff level
    if (shouldEnforceBackoffLevelSystem(
        requestOktaUserId,
        pVerificationRequest.getFactorType(),
        pVerificationRequest.getPasscode())) {
      accountUserMfaHistorySvc.throwLoginRateLimitedWithSecondsException(updatedBackoffLevel);
    }

    return Response.ok(transaction).build();
  }

  @POST
  @Path("/auth/mfa/verify/resend")
  @Produces(MediaType.APPLICATION_JSON)
  @AccountUiCall(auth = false)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "resendAuthMfa",
      types = {Type.IP})
  public Response resendAuthMfa(OktaMfaFactorVerificationRequestView pVerificationRequest)
      throws Exception {
    String requestOktaUserId = null;
    Map<String, Object> clientState = pVerificationRequest.getClientState();

    if (clientState != null
        && clientState.containsKey(AccountAuthResourceConstants.OKTA_USER_ID_PARAM)) {
      requestOktaUserId =
          clientState.get(AccountAuthResourceConstants.OKTA_USER_ID_PARAM).toString();
    }

    // If the OTP is for an SMS factor, verify that the user is not rate limited,
    // then create a document to track their request
    int updatedBackoffLevel = 0;
    if (shouldEnforceBackoffLevelSystem(
        requestOktaUserId,
        pVerificationRequest.getFactorType(),
        pVerificationRequest.getPasscode())) {
      updatedBackoffLevel =
          accountUserMfaHistorySvc.applyLoginRateLimitsAndTrackRequest(requestOktaUserId);
    }

    OktaMfaAuthTransaction transaction =
        accountMultiFactorAuthSvc.resendAuthMfa(pVerificationRequest);
    String transactionOktaUsername = transaction.getUserLogin();

    validateMfaRequestAndSetCountryCode(
        transaction,
        transactionOktaUsername,
        requestOktaUserId,
        pVerificationRequest.getFactorType(),
        pVerificationRequest.getFactorId(),
        pVerificationRequest.getPasscode());

    // After an SMS OTP is successfully sent, rate limit the user for a time
    // corresponding to their updated backoff level
    if (shouldEnforceBackoffLevelSystem(
        requestOktaUserId,
        pVerificationRequest.getFactorType(),
        pVerificationRequest.getPasscode())) {
      accountUserMfaHistorySvc.throwLoginRateLimitedWithSecondsException(updatedBackoffLevel);
    }

    return Response.ok(transaction).build();
  }

  @POST
  @Path("/auth/verify")
  @Produces(MediaType.APPLICATION_JSON)
  @AccountUiCall(auth = false)
  public Response verifyAuth(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AuditInfo pAuditInfo,
      UserLoginForm pUserLoginForm)
      throws SvcException {
    String username = pUserLoginForm.getUsername();
    String socialProvider = getSocialProviderName(pUserLoginForm.getClientState());
    var socialProviderValue =
        EnumUtils.tryValueOf(UiAuthMethod.class).apply(socialProvider.toUpperCase());
    var socialIdpId =
        socialProviderValue != null ? accountUserSvc.getSocialIdpId(socialProviderValue) : "";

    validateRecaptcha(
        pRequest,
        pResponse,
        pUserLoginForm,
        ReCaptchaAction.LINK_EXTERNAL_ACCOUNT,
        iamAppSettings.getRecaptchaLinkExternalAccountEnabled(),
        iamAppSettings.getRecaptchaLinkExternalAccountMinScore());

    try {
      AppUser appUser = getUserSvcOkta().findByUsername(username);

      if (appUser == null) {
        throw new SvcException(AppUserErrorCode.INVALID_USERNAME);
      }

      boolean shouldUseSessionToken = true;

      // if user has legacy mfa setup, then add a query param indicating that user needs
      // verification
      if (appUser.hasMultiFactorAuth()) {
        pUserLoginForm.getClientState().put("needsMFAVerification", "true");
      } else {
        shouldUseSessionToken = false;
      }

      var loginRedirect = "";
      if (!iamAppSettings.getMfaRequiredForAllUsers()) {
        String oktaSessionToken =
            getUserSvcOkta()
                .getUserSessionToken(username, pUserLoginForm.getPassword(), pAuditInfo);
        loginRedirect =
            oAuthSvc.getAccountLoginRedirect(
                username,
                pResponse,
                shouldUseSessionToken
                    // If a user is linking to a social provider, we redirect them to that
                    // provider
                    // rather than utilize the existing session token.
                    // This allows the user to be treated as SOCIAL rather than OKTA in OIE.
                    ? new SessionTokenOAuthMethod(oktaSessionToken)
                    : new IdpOAuthMethod(socialIdpId),
                pUserLoginForm.getClientState());

        accountUserSvc.convertUserToSocial(appUser.getOktaUserId());
        LOG.info(
            "User {} successfully connected their MongoDB and {} accounts after verifying"
                + " their password",
            AccountAuditLogEntry.formatUsername(username),
            getSocialProviderName(pUserLoginForm.getClientState()));
      } else {
        loginRedirect =
            getLoginRedirect(
                pResponse,
                username,
                pUserLoginForm.getPassword(),
                pAuditInfo,
                pUserLoginForm.getClientState());
      }

      return SimpleApiResponse.ok()
          .customField(AccountAuthResourceConstants.LOGIN_REDIRECT_PROPERTY, loginRedirect)
          .build();
    } catch (SvcException e) {
      LOG.debug(
          "Okta primary authentication failed for verifying auth for {}: {}:{}",
          AccountAuditLogEntry.formatUsername(username),
          e.getErrorCode(),
          e.getMessage(),
          e);
      return SimpleApiResponse.badRequest(e.getErrorCode()).build();
    }
  }

  @DELETE
  @Path("/auth/verify/cancel")
  @Produces(MediaType.APPLICATION_JSON)
  @AccountUiCall(auth = false)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "cancelAuthVerify",
      types = {Type.IP, Type.PAYLOAD})
  public Response cancelAuthVerify(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AuditInfo pAuditInfo,
      String pParams) {
    JSONObject params = new JSONObject(pParams);

    String username = params.optString(AccountAuthResourceConstants.USERNAME_PARAM, null);
    String socialProviderParam =
        params.optString(AccountAuthResourceConstants.SOCIAL_PROVIDER_PARAM, null);

    String errorPrefix = "Failed to cancel the linking of Okta user: ";

    if (username == null || socialProviderParam == null) {
      String paramsMustBeDefined =
          AccountAuthResourceConstants.USERNAME_PARAM
              + " and "
              + AccountAuthResourceConstants.SOCIAL_PROVIDER_PARAM
              + "must be defined";
      LOG.debug(errorPrefix + paramsMustBeDefined);
      return SimpleApiResponse.badRequest(CommonErrorCode.INVALID_PARAMETER, paramsMustBeDefined)
          .build();
    }

    UiAuthMethod socialProvider = UiAuthMethod.valueOf(socialProviderParam.toUpperCase());
    String socialIdpId = accountUserSvc.getSocialIdpId(socialProvider);

    try {
      accountUserSvc.cancelLinkingToSocialIdpByUsername(username, socialIdpId);
      return SimpleApiResponse.ok().build();
    } catch (SvcException e) {
      LOG.debug(
          errorPrefix + "linking to {} Idp failed: {}:{}",
          socialProvider.name(),
          e.getErrorCode(),
          e.getMessage());
      return SimpleApiResponse.badRequest(e.getErrorCode()).build();
    }
  }

  @POST
  @Path("/resetPasswordRequest")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false, groupSource = UiCall.GroupSource.NONE)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "resetPasswordRequestSend",
      types = {Type.SUSPENDABLE_IP, Type.PAYLOAD})
  public Response resetPasswordRequestSend(@Context HttpServletRequest pRequest, String pParams)
      throws Exception {
    JSONObject params = new JSONObject(pParams);
    String normalizedUsername =
        accountUserSvc.normalizeAndValidateUsername(params.optString("username", null));
    if (shouldPreventEmployeePasswordReset(normalizedUsername)) {
      LOG.info("Ineligible employee user={} tried to initiate password reset", normalizedUsername);
      throw new SvcException(AppUserErrorCode.CANNOT_SEND_RESET_EMAIL);
    }

    if (shouldPreventNonEmployeePasswordReset(normalizedUsername)) {
      LOG.info(
          "Ineligible non-employee user={} tried to initiate password reset", normalizedUsername);
      throw new SvcException(AppUserErrorCode.CANNOT_SEND_RESET_EMAIL);
    }

    boolean isAtlas = params.optBoolean("nds", false);

    try {
      // Ensure there is a local user to get their email and for auditing of requests. If the
      // Okta user doesn't exist, an exception is thrown and the call will be a no-op.
      AccountUser accountUser = accountUserSvc.ensureLocalUserFromOkta(normalizedUsername);
      AppUser appUser = userSvc.findByUsernameOrBackup(normalizedUsername);

      boolean userIsSoftDeleted =
          accountUser.isAccountDeleted() || (appUser != null && appUser.isDeleted());

      if (userIsSoftDeleted) {
        // Log but do not fail the request for security purposes, and let it return 200 OK
        LOG.info("Deleted user={} tried to initiate password reset", normalizedUsername);
      } else {
        // If the user is a pending Partner Integration user, send a different email
        PartnerIntegrationsData partnerIntegrationsData =
            Optional.ofNullable(appUser).map(AppUser::getPartnerIntegrationsData).orElse(null);
        if (partnerIntegrationsData != null && partnerIntegrationsData.isPending()) {
          String tempId =
              params.optString(
                  "tempId",
                  passwordResetSvc.createEntry(
                      AuthType.PASSWORD, normalizedUsername, pRequest.getRemoteAddr(), false));
          partnerIntegrationEmailSvc.sendSetPasswordEmail(
              normalizedUsername, tempId, partnerIntegrationsData.getIntegrationType());
        } else {
          passwordResetSvc.sendResetEmail(
              AuthType.PASSWORD,
              normalizedUsername,
              // The account app is timezone agnostic, so there's no way to pass this info
              null,
              auditInfoSvc.fromUiCall(null, pRequest.getRemoteAddr()),
              isAtlas);
        }
      }
    } catch (SvcException e) {
      // Log but do not fail the request for security purposes, and let it return 200 OK
      LOG.info("Failed to initiate password reset for user={}", normalizedUsername, e);
    }

    return SimpleApiResponse.ok().resource(pRequest.getRequestURI()).build();
  }

  @POST
  @Path("/createPasswordResetLinkAsAdmin")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_USER_ADMIN, groupSource = GroupSource.NONE)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "createPasswordResetLinkAsAdmin",
      types = {Type.SUSPENDABLE_IP, Type.PAYLOAD})
  public Response createPasswordResetLinkAsAdmin(
      @Context HttpServletRequest request,
      @Context AppUser adminUser,
      @Context AuditInfo auditInfo,
      String requestParams)
      throws Exception {
    JSONObject params = new JSONObject(requestParams);
    String username = StringUtils.trimToNull(params.optString("username", null));
    String helpTicket = StringUtils.trimToNull(params.optString("helpTicket", null));

    String resetLink;
    try {
      resetLink =
          passwordResetSvc.createPasswordResetLinkAsAdmin(
              AuthType.PASSWORD, username, auditInfo, adminUser.getUsername(), helpTicket);
      LOG.info(
          "Admin={} generated reset password link for HELP ticket={} and user={}",
          adminUser.getUsername(),
          helpTicket,
          username);
    } catch (SvcException e) {
      LOG.info("Failed to generate password reset link", e);
      throw e;
    }

    return SimpleApiResponse.ok()
        .customField(AccountAuthResourceConstants.RESET_LINK_PARAM, resetLink)
        .resource("Password reset link has been generated")
        .build();
  }

  @POST
  @Path("/resetPasswordComplete")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false, groupSource = UiCall.GroupSource.NONE)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "resetComplete",
      types = {Type.PAYLOAD})
  public Response resetComplete(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AuditInfo pAuditInfo,
      String pParams)
      throws Exception {
    JSONObject params = new JSONObject(pParams);
    String username = StringUtils.trimToEmpty(getJSONParam("username", params::getString));
    String password = StringUtils.trimToNull(getJSONParam("password", params::getString));
    String passwordConfirm =
        StringUtils.trimToNull(getJSONParam("passwordConfirm", params::getString));
    String tempId = StringUtils.trimToNull(getJSONParam("tempId", params::getString));
    boolean isPendingUser = params.optBoolean("isPendingUser", false);
    LOG.info("Completing Password reset for {}", kv("userId", username));

    accountUserSvc.resetPassword(username.toLowerCase(), password, passwordConfirm, tempId);

    AppUser user = getUserSvcOkta().findByUsername(username);
    String primaryEmail =
        accountUserSvc
            .findByUsername(username)
            .map(account -> account.getEmail())
            .orElse(user == null ? null : user.getPrimaryEmail());

    passwordResetSvc.completePasswordReset(
        username,
        primaryEmail,
        null,
        tempId,
        isPendingUser,
        auditInfoSvc.fromUiCall(user, pRequest.getRemoteAddr()));

    // track ANIS users account setup completion
    final Map<String, Object> nextUrlMap;
    if (isPendingUser) {
      Optional.ofNullable(user)
          .map(AppUser::getPartnerIntegrationsData)
          .filter(partnerData -> partnerData.getIntegrationType().equals(IntegrationType.ANIS))
          .ifPresent(
              partnerData -> {
                segmentEventSvc.submitEvent(
                    AzureNativeUserPasswordSetEvent.builder()
                        .email(primaryEmail)
                        // we're making a best guess here to determine if the user is a creator
                        // since we don't have relevant org information at this point
                        .isCreator(authzSvc.hasAnyOrgOwnerRole(user))
                        .build());
              });
      // Ensure partner integration redirect param is set so we attempt to redirect
      nextUrlMap = Map.of("partnerIntegrationRedirect", "true");
    } else {
      // Only redirect to account profile page if the user wasn't pending
      nextUrlMap = Map.of("n", "/account/profile/info");
    }

    String loginRedirect = getLoginRedirect(pResponse, username, password, pAuditInfo, nextUrlMap);
    return SimpleApiResponse.ok()
        .customField(AccountAuthResourceConstants.LOGIN_REDIRECT_PROPERTY, loginRedirect)
        .build();
  }

  @GET
  @Path("/sso/{socialProvider}")
  @Produces({MediaType.TEXT_HTML})
  @AccountUiCall(auth = false)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "socialSSO",
      types = {Type.IP})
  public Response socialSSO(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context UriInfo pUriInfo,
      @PathParam(AccountAuthResourceConstants.SOCIAL_PROVIDER_PARAM) String pSocialProvider,
      @QueryParam(AccountAuthResourceConstants.SIGNUP_SOURCE_PARAM) String pSignupSource)
      throws URISyntaxException {
    var socialProviderValue =
        EnumUtils.tryValueOf(UiAuthMethod.class).apply(pSocialProvider.toUpperCase());

    if (socialProviderValue == null) {
      LOG.debug("socialProvider={} path param is invalid", pSocialProvider);
      return Response.seeOther(new URI(AuthRedirectPages.LOGIN_PAGE.getPath())).build();
    }

    var socialIdpId = accountUserSvc.getSocialIdpId(socialProviderValue);

    if (!appSettings.isSocialProviderEnabled(pSocialProvider) || StringUtils.isEmpty(socialIdpId)) {
      return Response.seeOther(new URI(AuthRedirectPages.LOGIN_PAGE.getPath())).build();
    }

    // Note that this will only cover attempts initiated on our application, and not attempts
    // initiated from the identity provider. To view all idp login attempts, use the Okta logs.
    // We don't have access to the username from this request because that is directly inputted
    // on the identity provider page – the username is set to unknown to be consistent with the
    // authentication attempt logs for other auth providers
    LOG.info(
        AccountAuditLogEntry.AUTH_ATTEMPT_LOG_TEMPLATE,
        AccountAuditLogEntry.formatUsername(null),
        AccountAuditLogEntry.formatRemoteAddress(pRequest.getRemoteAddr()),
        AccountAuditLogEntry.formatAuthMethod(socialProviderValue));

    Optional<String> signupSourceOpt = Optional.ofNullable(StringUtils.trimToNull(pSignupSource));

    var queryParams = new HashMap<String, Object>();
    pUriInfo.getQueryParameters().forEach((k, v) -> queryParams.put(k, v.get(0)));
    queryParams.put("signupMethod", pSocialProvider);
    var oidcInitUrl =
        oAuthSvc.getAccountLoginRedirect(
            null, pResponse, new IdpOAuthMethod(socialIdpId), queryParams);

    if (signupSourceOpt.isEmpty()) {
      LOG.warn(
          "signupSource is null for SSO flow={} and queryParams={}",
          pSocialProvider,
          pUriInfo.getRequestUri().getQuery());
    }

    return Response.seeOther(new URI(oidcInitUrl)).build();
  }

  @GET
  @Produces({MediaType.TEXT_HTML})
  @Path("/link/partner/vercel")
  @UiCall(auth = false, account = true)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "vercelPartnerLink",
      types = {Type.IP},
      maxHitsPerPeriod = 5,
      minutesPeriod = 10,
      enabled = true)
  public Response vercelPartnerLink(
      @Context HttpServletRequest request,
      @Context HttpServletResponse response,
      @Context AuditInfo auditInfo,
      @QueryParam(AccountAuthResourceConstants.CODE_PARAM_VALUE) String code,
      @QueryParam(AccountAuthResourceConstants.STATE_PARAM_VALUE) String state,
      @QueryParam(AccountAuthResourceConstants.VERCEL_RESOURCE_ID_PARAM) String targetResource)
      throws URISyntaxException {
    if (!iamAppSettings.isVercelNativeEnabled()) {
      LOG.warn("Vercel integration is disabled, redirecting to login page");
      VercelPartnerLinkMetrics.recordVercelPartnerLinkFailure(
          VercelPartnerLinkMetrics.VERCEL_PARTNER_LINK_STEP_ACCOUNT_LINK,
          VercelPartnerLinkMetrics.ERROR_INTEGRATION_DISABLED);
      return Response.seeOther(new URI(AuthRedirectPages.LOGIN_PAGE.getPath())).build();
    }

    if (invalidVercelPartnerLinkRequest(code, state)) {
      VercelPartnerLinkMetrics.recordVercelPartnerLinkFailure(
          VercelPartnerLinkMetrics.VERCEL_PARTNER_LINK_STEP_ACCOUNT_LINK,
          VercelPartnerLinkMetrics.ERROR_INVALID_REQUEST);
      return seeOther(
          generateErrorUrl(
              AuthRedirectPages.LOGIN_PAGE.getPath(), CommonErrorCode.BAD_REQUEST.name()));
    }

    try {
      VercelUserToken userToken = vercelAuthSvc.retrieveVercelIdToken(code, state);

      String username = userToken.email();
      if (StringUtils.isBlank(username)) {
        LOG.warn("Missing required Vercel user email, redirecting to login page");
        VercelPartnerLinkMetrics.recordVercelPartnerLinkFailure(
            VercelPartnerLinkMetrics.VERCEL_PARTNER_LINK_STEP_ACCOUNT_LINK,
            VercelPartnerLinkMetrics.ERROR_MISSING_EMAIL);
        return seeOther(
            generateErrorUrl(
                AuthRedirectPages.LOGIN_PAGE.getPath(), CommonErrorCode.BAD_REQUEST.name()));
      }

      writeVercelResourceCookie(response, targetResource, userToken);

      // Check if user exists using UserSvc
      Optional<AppUser> existingUser = userSvc.getAppUserByOktaUsername(username, auditInfo);

      if (existingUser.isPresent()) {

        return handleExistingUserFlow(existingUser.get(), response, userToken, auditInfo);
      }
      return handleNewUserFlow(response, userToken, auditInfo);

    } catch (SvcException e) {
      LOG.error("Error during Vercel Native Account linking", e);
      VercelPartnerLinkMetrics.recordVercelPartnerLinkFailure(
          VercelPartnerLinkMetrics.VERCEL_PARTNER_LINK_STEP_ACCOUNT_LINK, e.getErrorCode().name());

      return seeOther(
          generateErrorUrl(AuthRedirectPages.LOGIN_PAGE.getPath(), e.getErrorCode().name()));
    } catch (Exception e) {
      LOG.error("Unexpected error Vercel Native Account linking", e);
      VercelPartnerLinkMetrics.recordVercelPartnerLinkFailure(
          VercelPartnerLinkMetrics.VERCEL_PARTNER_LINK_STEP_ACCOUNT_LINK,
          VercelPartnerLinkMetrics.ERROR_UNKNOWN);
      return seeOther(
          generateErrorUrl(
              AuthRedirectPages.LOGIN_PAGE.getPath(), CommonErrorCode.SERVER_ERROR.name()));
    }
  }

  private void writeVercelResourceCookie(
      HttpServletResponse response, String targetResource, VercelUserToken userToken) {
    // Write target resource and installation ID to cookie for post-auth redirect
    try {
      var resourceInfoString =
          objectMapper.writeValueAsString(
              new VercelResourceInfo(userToken.installationId(), targetResource));

      writeVercelNativeCookie(
          response,
          AccountAuthResourceConstants.VERCEL_PARTNER_RESOURCE_COOKIE_NAME,
          AccountAuthResourceConstants.VERCEL_PARTNER_LINK_COOKIE_EXPIRY_IN_SECONDS,
          URLEncoder.encode(resourceInfoString, StandardCharsets.UTF_8));
    } catch (Exception e) {
      VercelPartnerLinkMetrics.recordVercelPartnerLinkFailure(
          VercelPartnerLinkMetrics.VERCEL_PARTNER_LINK_STEP_ACCOUNT_LINK,
          VercelPartnerLinkMetrics.ERROR_COOKIE_WRITE);
      LOG.warn(
          "Failed to serialize VercelResourceInfo to JSON, proceeding without resource cookie", e);
    }
  }

  private Response handleExistingUserFlow(
      AppUser user, HttpServletResponse response, VercelUserToken userToken, AuditInfo auditInfo)
      throws URISyntaxException {
    LOG.debug(
        "Found existing user for Vercel account-linking flow: {}",
        AccountAuditLogEntry.formatUsername(userToken.email()));

    // Track existing user flow started
    VercelPartnerLinkMetrics.recordVercelPartnerLinkFlowStarted(
        VercelPartnerLinkMetrics.EXISTING_USER_TYPE);

    // Check if the user email already has a mapping for this installationId
    if (vercelNativeAccountSvc.hasExistingPartnerIdentityMapping(
        userToken.email(), userToken.installationId())) {
      LOG.info(
          "User {} already has partner identity mapping for installationId {}, redirecting directly"
              + " to login",
          AccountAuditLogEntry.formatUsername(userToken.email()),
          userToken.installationId());

      // Redirect directly to login page, skipping consent page
      URI loginPageUri =
          new URIBuilder(AuthRedirectPages.LOGIN_PAGE.getPath())
              .addParameter(AccountAuthResourceConstants.USERNAME_PARAM, userToken.email())
              .addParameter(AccountAuthResourceConstants.PARTNER_INTEGRATION_REDIRECT_PARAM, "true")
              .build();
      return Response.seeOther(loginPageUri).build();
    }

    // Check if user is already linked to a different integration type and if so, redirect to login
    // page w/ error message
    if (vercelNativeAccountSvc.isUserLinkedToOtherIntegrationType(userToken.email())) {
      return handleUserLinkedToOtherIntegrationType(userToken);
    }

    // Write the link details to the tentative link storage w/ consent set to false
    TentativePartnerAccountLink tentativePartnerAccountLink =
        tentativePartnerAccountLinkSvc.createTentativeLink(
            userToken.email(), userToken.userId(), userToken.installationId(), false);

    // store sessionId in cookie for use in later steps
    writeVercelNativeCookie(
        response,
        AccountAuthResourceConstants.VERCEL_PARTNER_LINK_COOKIE_NAME,
        AccountAuthResourceConstants.VERCEL_PARTNER_LINK_COOKIE_EXPIRY_IN_SECONDS,
        tentativePartnerAccountLink.sessionId());

    // If user is pending, redirect to password reset page
    if (user.getPartnerIntegrationsData() != null
        && user.getPartnerIntegrationsData().isPending()) {
      URI passwordResetPageUri =
          new URIBuilder(AuthRedirectPages.PASSWORD_RESET_START_PAGE.getPath())
              .addParameter("email", userToken.email())
              .addParameter("shouldRedirect", "true")
              .build();
      return Response.seeOther(passwordResetPageUri).build();
    }

    // Redirect to the account-link consent page with user's email address as a query param
    URI consentPageUri =
        new URIBuilder(AuthRedirectPages.ACCOUNT_LINK_CONSENT_PAGE.getPath())
            .addParameter("email", userToken.email())
            .build();
    return Response.seeOther(consentPageUri).build();
  }

  private Response handleNewUserFlow(
      HttpServletResponse response, VercelUserToken userToken, AuditInfo auditInfo)
      throws URISyntaxException, SvcException {
    LOG.debug(
        "No existing user found for Vercel account-linking flow: {}",
        AccountAuditLogEntry.formatUsername(userToken.email()));

    // Track new user flow started
    VercelPartnerLinkMetrics.recordVercelPartnerLinkFlowStarted(
        VercelPartnerLinkMetrics.NEW_USER_TYPE);

    // Write the link details to the tentative link storage w/ consent set to true
    TentativePartnerAccountLink tentativePartnerAccountLink =
        tentativePartnerAccountLinkSvc.createTentativeLink(
            userToken.email(), userToken.userId(), userToken.installationId(), true);

    // store sessionId in cookie for use in later steps
    writeVercelNativeCookie(
        response,
        AccountAuthResourceConstants.VERCEL_PARTNER_LINK_COOKIE_NAME,
        AccountAuthResourceConstants.VERCEL_PARTNER_LINK_COOKIE_EXPIRY_IN_SECONDS,
        tentativePartnerAccountLink.sessionId());

    // create user in a pending state with ver
    userSvc.registerFromPartner(
        populateUserRegistrationFromVercelToken(userToken),
        auditInfo,
        PartnerIntegrationUserSource.VERCEL_NATIVE_ACCOUNT_LINK_USER);

    // Redirect to the password reset page
    URI passwordResetPageUri =
        new URIBuilder(AuthRedirectPages.PASSWORD_RESET_START_PAGE.getPath())
            .addParameter("email", userToken.email())
            // shouldRedirect is used for all partner integration reset flows
            .addParameter("shouldRedirect", "true")
            .build();

    return Response.seeOther(passwordResetPageUri).build();
  }

  private static UserRegistrationForm populateUserRegistrationFromVercelToken(
      VercelUserToken userToken) {
    UserRegistrationForm userRegistrationForm = new UserRegistrationForm();
    userRegistrationForm.setUsername(userToken.email());
    userRegistrationForm.setFirstName(
        StringUtils.defaultIfBlank(userToken.firstName(), "Not Provided"));
    userRegistrationForm.setLastName(
        StringUtils.defaultIfBlank(userToken.lastName(), "Not Provided"));
    userRegistrationForm.setPhoneNumber("Not Provided");
    userRegistrationForm.setPhoneNumber(NOT_PROVIDED);
    userRegistrationForm.setCountry(NOT_PROVIDED);
    userRegistrationForm.setJobResponsibility(NOT_PROVIDED);
    return userRegistrationForm;
  }

  private static boolean invalidVercelPartnerLinkRequest(String code, String state) {
    // Validate required parameters
    if (StringUtils.isBlank(code) || StringUtils.isBlank(state)) {
      LOG.warn(
          "Missing required Vercel auth parameters: code={}, state={}",
          StringUtils.isNotBlank(code) ? "[PRESENT]" : "[MISSING]",
          StringUtils.isNotBlank(state) ? "[PRESENT]" : "[MISSING]");
      return true;
    }
    return false;
  }

  private void writeVercelNativeCookie(
      HttpServletResponse response, String name, int expiryInSeconds, String cookieValue) {

    boolean isLocal = appSettings.isLocal();
    CookieWriter.builder()
        .response(response)
        .name(name)
        .value(cookieValue)
        .path("/")
        .maxAge(expiryInSeconds)
        .secure(!isLocal)
        .domain(getCookieDomainFromCentralUrl())
        .sameSite(isLocal ? SameSite.LAX : SameSite.STRICT)
        .httpOnly(true)
        .build()
        .write();
  }

  /**
   * Handles the case where a user is already linked to a different integration type. Logs the
   * details, records a failure metric, and redirects to the login page with an error message.
   *
   * @param userToken The Vercel user token containing user and installation information
   * @return Response redirecting to login page with error details
   * @throws URISyntaxException if there's an error building the redirect URI
   */
  private Response handleUserLinkedToOtherIntegrationType(VercelUserToken userToken)
      throws URISyntaxException {
    LOG.info(
        "User {} attempted to link Vercel Native integration {} but is already linked to"
            + "another partner integration type.",
        AccountAuditLogEntry.formatUsername(userToken.email()),
        userToken.installationId());

    // Record failure metric
    VercelPartnerLinkMetrics.recordVercelPartnerLinkFailure(
        VercelPartnerLinkMetrics.EXISTING_USER_TYPE,
        VercelPartnerLinkMetrics.ERROR_USER_LINKED_TO_OTHER_INTEGRATION_TYPE);

    // Redirect to login page with error message
    URI loginPageUri =
        new URIBuilder(AuthRedirectPages.LOGIN_PAGE.getPath())
            .addParameter(
                "error", VercelPartnerLinkMetrics.ERROR_USER_LINKED_TO_OTHER_INTEGRATION_TYPE)
            .addParameter(
                "errorMessage",
                URLEncoder.encode(
                    "Your account is already linked to a different partner integration. Please"
                        + " contact support if you need to change your linked integration.",
                    StandardCharsets.UTF_8))
            .build();
    return Response.seeOther(loginPageUri).build();
  }

  @POST
  @Path("/tos/accept")
  @Produces({MediaType.APPLICATION_JSON})
  @AccountUiCall
  public Response acceptTOS(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AuditInfo pAuditInfo,
      @Context AccountUser pAccountUser,
      @QueryParam(AccountAuthResourceConstants.NEW_ACCOUNT_USER_QUERY_PARAM)
          boolean pIsNewAccountUser,
      AcceptTosView tos)
      throws Exception {
    State state;
    try {
      state = oAuthSvc.extractStateFromCookie(pRequest);
    } catch (SvcException e) {
      return seeOther(
          generateErrorUrl(AuthRedirectPages.LOGIN_PAGE.getPath(), e.getErrorCode().name()));
    }

    String username = pAccountUser.getUsername();

    if (!tos.getTosChecked()) {
      LOG.warn("oktaUser={} did not accept TOS but request was still made", username);
      throw new SvcException(AppUserErrorCode.NEEDS_TOS_ACCEPTANCE);
    }

    accountUserSvc.acceptTOS(pAccountUser.getOktaUserId());
    LOG.info("Marked oktaUser={} with acceptedTOS=true", username);

    UriBuilder builder = createQuery(state);

    String queryString = pRequest.getQueryString();
    if (queryString != null) {
      Stream.of(queryString.split("&"))
          .forEach(
              pair -> {
                var token = pair.split("=");
                if (token.length != 2 || state.getQueryParams().has(token[0])) {
                  return;
                }
                builder.queryParam(token[0], token[1]);
              });
    }
    builder.replaceQueryParam(AccountAuthResourceConstants.NEW_ACCOUNT_USER_QUERY_PARAM);
    return SimpleApiResponse.ok()
        .customField(
            "redirectURI",
            createPostAuthRedirectUri(state.getFromURI(), builder, pIsNewAccountUser))
        .build();
  }

  @POST
  @Path("/eloqua")
  @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
  @Produces(MediaType.TEXT_HTML)
  @AccountUiCall(auth = false)
  public Response postToEloqua(@Context HttpServletRequest pRequest, String pParams) {
    if (appSettings.isAnalyticsEnabled()) {
      eloquaSvc.postRegistrationParams(pParams);
    }
    return Response.ok().build();
  }

  @POST
  @Path("/logout")
  @Produces(MediaType.APPLICATION_JSON)
  @AccountUiCall(auth = false)
  public Response logout(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @Context AccountUser pAccountUser,
      @Context AccountSession pAccountSession) {
    if (pAccountUser == null || pAccountSession == null) {
      final String remoteAddr = pRequest.getRemoteAddr();
      LOG.info(
          "Received unauthenticated account logout request from {}",
          StructuredArguments.kv("remoteIP", remoteAddr));
      return Response.ok().build();
    }

    LOG.info(
        "Account /logout request from {}, {}",
        AccountAuditLogEntry.formatUsername(pAccountUser.getUsername()),
        AccountAuditLogEntry.formatRemoteAddress(pRequest.getRemoteAddr()));

    pRequest.removeAttribute(ACCOUNT_AUTH_COOKIE);
    pResponse.addHeader("Clear-Site-Data", "\"cache\"");

    LOG.info(
        "Account /logout accountSessionSvc.deleteSessionCookies for {}, {}",
        AccountAuditLogEntry.formatUsername(pAccountUser.getUsername()),
        AccountAuditLogEntry.formatRemoteAddress(pRequest.getRemoteAddr()));

    accountSessionSvc.deleteSessionCookies(pRequest, pResponse);

    accountUserSvc.signOut(pAccountSession.getAccountSession());

    LOG.info(
        "Account /logout complete for {}, {}",
        AccountAuditLogEntry.formatUsername(pAccountUser.getUsername()),
        AccountAuditLogEntry.formatRemoteAddress(pRequest.getRemoteAddr()));

    return Response.ok().build();
  }

  @POST
  @Path("/checkPassword")
  @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
  @Produces(MediaType.APPLICATION_JSON)
  @AccountUiCall
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "checkPassword",
      types = {Type.USER})
  public Response checkPassword(
      @Context HttpServletRequest pRequest,
      @Context AccountUser pAccountUser,
      @Context AccountSession pAccountSession,
      @FormParam("password") String pPassword)
      throws SvcException {
    accountUserSvc.checkPassword(pAccountUser, pPassword, pAccountSession.getAccountSession());

    return Response.ok().build();
  }

  private String buildCloudAuthorizeUriAfterAuth(UriBuilder pBuilder) {
    return oAuthSvc.getCloudStartOidcLink(pBuilder);
  }

  @POST
  @Path("/awsMpRegistrationRedirect")
  @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
  @AccountUiCall(auth = false)
  @AllowCORS({KnownCrossOrigin.AWS_MARKETPLACE})
  public Response awsMpRegistrationRedirect(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @FormParam(MarketplaceRegistrationConstants.AWS_MARKETPLACE_TOKEN_NAME) @DefaultValue("")
          String pAwsMpToken)
      throws URISyntaxException {

    if (pAwsMpToken == null || pAwsMpToken.isEmpty()) {
      String referer = pRequest.getHeader("referer");
      LOG.error(
          "x-amzn-marketplace-token missing from request body. Might be due to bot traffic."
              + " Referer: {}",
          referer);
      AWS_MP_REDIRECT_MISSING_TOKEN_ERROR_COUNTER.inc();
      return seeOther(
          generateErrorUrl(
              AuthRedirectPages.LOGIN_PAGE.getPath(),
              AppUserErrorCode.MISSING_AWS_MP_TOKEN.name()));
    }

    String encodedRegistrationData;
    try {
      encodedRegistrationData =
          partnerMarketplaceRegistrationSvcFactory
              .get(PartnerType.AWS)
              .getRegistrationCookieContent(pAwsMpToken);
    } catch (Exception e) {
      LOG.error("An error occurred while trying to resolve AWS customer information", e);
      AWS_MP_REDIRECT_RESOLVE_CUSTOMER_ERROR_COUNTER.inc();
      return seeOther(
          generateErrorUrl(
              AuthRedirectPages.LOGIN_PAGE.getPath(),
              AppUserErrorCode.AWS_MP_RESOLVE_CUSTOMER_FAILED.name()));
    }

    if (encodedRegistrationData == null) {
      AWS_MP_REDIRECT_RESOLVE_CUSTOMER_ERROR_COUNTER.inc();
      return seeOther(
          generateErrorUrl(
              AuthRedirectPages.LOGIN_PAGE.getPath(),
              AppUserErrorCode.AWS_MP_RESOLVE_CUSTOMER_FAILED.name()));
    }

    String cookieDomain = getCookieDomainFromCentralUrl();

    boolean isLocal = appSettings.isLocal();

    CookieWriter.Builder cookieWriter =
        CookieWriter.builder()
            .response(pResponse)
            .name(MarketplaceRegistrationConstants.SESSION_COOKIE_NAME)
            .value(encodedRegistrationData)
            .path("/")
            .maxAge(3600)
            .secure(!isLocal)
            .httpOnly(false);

    if (!isLocal) {
      cookieWriter.domain(cookieDomain).sameSite(SameSite.LAX);
    }

    cookieWriter.build().write();

    AWS_MP_REDIRECT_SUCCESS_COUNTER.inc();

    return Response.seeOther(
            new URI(
                "/account/login?utm_campaign=aws_marketplace&utm_source=aws_marketplace&utm_medium=referral"))
        .build();
  }

  @POST
  @Path("/gcpMpRegistrationRedirect")
  @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
  @AccountUiCall(auth = false)
  @AllowCORS({KnownCrossOrigin.GCP_MARKETPLACE})
  public Response gcpMpRegistrationRedirect(
      @Context HttpServletRequest pRequest,
      @Context HttpServletResponse pResponse,
      @FormParam(MarketplaceRegistrationConstants.GCP_MARKETPLACE_TOKEN_NAME) @DefaultValue("")
          String pGcpMpToken)
      throws URISyntaxException {
    if (pGcpMpToken == null || pGcpMpToken.isEmpty()) {
      String referer = pRequest.getHeader("referer");
      LOG.warn(
          "x-gcp-marketplace-token missing from request body. Might be due to bot traffic."
              + " Referer: {}",
          referer);
      GCP_MP_REDIRECT_MISSING_TOKEN_ERROR.inc();
      return seeOther(
          generateErrorUrl(
              AuthRedirectPages.LOGIN_PAGE.getPath(),
              AppUserErrorCode.MISSING_GCP_MP_TOKEN.name()));
    }

    String encodedRegistrationData;
    try {
      PartnerMarketplaceRegistrationSvc registrationSvc =
          partnerMarketplaceRegistrationSvcFactory.get(PartnerType.GCP);

      // for gcp we redirect already subscribed users to this endpoint as well as new users.
      // so we need to check if they are already subscribed or not. If not, we simply redirect
      // to the usual login url without setting a cookie, bypassing the link-org flow.
      if (registrationSvc.isAlreadySubscribed(pGcpMpToken)) {
        GCP_MP_REDIRECT_ALREADY_SUBSCRIBED_COUNTER.inc();
        return Response.seeOther(new URI(AuthRedirectPages.LOGIN_PAGE.getPath())).build();
      }

      encodedRegistrationData = registrationSvc.getRegistrationCookieContent(pGcpMpToken);
    } catch (Exception e) {
      LOG.error("exception extracting registration data from gcp marketplace token.", e);
      GCP_MP_REDIRECT_TOKEN_VERIFICATION_FAILURE.inc();
      return seeOther(
          generateErrorUrl(
              AuthRedirectPages.LOGIN_PAGE.getPath(),
              AppUserErrorCode.GCP_FAILED_TO_VALIDATE_JWT.name()));
    }

    if (encodedRegistrationData == null) {
      GCP_MP_REDIRECT_TOKEN_VERIFICATION_FAILURE.inc();
      return seeOther(
          generateErrorUrl(
              AuthRedirectPages.LOGIN_PAGE.getPath(),
              AppUserErrorCode.GCP_FAILED_TO_VALIDATE_JWT.name()));
    }

    String cookieDomain = getCookieDomainFromCentralUrl();

    boolean isLocal = appSettings.isLocal();

    CookieWriter.Builder cookieWriter =
        CookieWriter.builder()
            .response(pResponse)
            .name(MarketplaceRegistrationConstants.SESSION_COOKIE_NAME)
            .value(encodedRegistrationData)
            .path("/")
            .maxAge(3600)
            .secure(!isLocal)
            .httpOnly(false);

    if (!isLocal) {
      cookieWriter.domain(cookieDomain).sameSite(SameSite.LAX);
    }

    cookieWriter.build().write();

    GCP_MP_REDIRECT_SUCCESS_COUNTER.inc();
    return Response.seeOther(
            new URI(
                "/account/login?utm_campaign=gcp_marketplace&utm_source=gcp_marketplace&utm_medium=referral"))
        .build();
  }

  @GET
  @Path("/link/partner/consent")
  @Produces({MediaType.TEXT_HTML})
  @AccountUiCall(auth = false)
  public Response vercelNativeLinkConfirmationPage(
      @Context RequestParams requestParams,
      @Context HttpServletRequest request,
      @QueryParam("email") String email)
      throws URISyntaxException {
    if (StringUtils.isEmpty(email)) {
      return Response.seeOther(new URI(AuthRedirectPages.LOGIN_PAGE.getPath())).build();
    }
    return serveSinglePageApp(requestParams, UserAgentUtils.isMobileBrowser(request));
  }

  @POST
  @Path("/link/partner/consent")
  @Produces(MediaType.APPLICATION_JSON)
  @AccountUiCall(auth = false)
  @RateLimited(
      policyPrefix = AccountAuthResourceConstants.RATE_LIMIT_POLICY_PREFIX,
      name = "vercelNativeLinkConfirmation",
      types = {Type.IP},
      maxHitsPerPeriod = 5,
      minutesPeriod = 10,
      enabled = true)
  public Response vercelNativeLinkConfirmation(
      @Context HttpServletRequest request,
      @Valid VercelNativeLinkConfirmationView confirmationView) {
    Optional<Cookie> vercelLinkCookie =
        CookieHelper.getCookie(
            request, AccountAuthResourceConstants.VERCEL_PARTNER_LINK_COOKIE_NAME);
    return vercelLinkCookie
        .map(Cookie::getValue)
        .map(
            sessionId -> {
              try {
                vercelNativeAccountSvc.confirmVercelLink(confirmationView.username(), sessionId);
                return SimpleApiResponse.ok().build();
              } catch (SvcException e) {
                return invalidVercelLinkResponse(e);
              }
            })
        .orElseGet(
            () ->
                invalidVercelLinkResponse(
                    new SvcException(CommonErrorCode.BAD_REQUEST, "missing or empty link cookie")));
  }

  private Response invalidVercelLinkResponse(SvcException e) {
    // Purposefully don't expose too much info - log exception, return generic error
    VercelPartnerLinkMetrics.recordVercelPartnerLinkFailure(
        VercelPartnerLinkMetrics.VERCEL_NATIVE_PARTNER_LINK_STEP_CONSENT, e.getErrorCode().name());
    LOG.warn("Error confirming Vercel partner link", e);
    return SimpleApiResponse.badRequest(AppUserErrorCode.INVALID_VERCEL_NATIVE_LINK).build();
  }

  private String getSocialProviderName(Map<String, Object> clientState) {
    var signupMethod = clientState != null ? clientState.get("signupMethod") : "";
    String socialProvider =
        signupMethod != null && !signupMethod.equals("") && !signupMethod.equals("form")
            ? UiAuthMethod.valueOf(signupMethod.toString().toUpperCase()).name()
            : "";
    return socialProvider;
  }

  private String getCookieDomainFromCentralUrl() {
    String[] accountUrlParts = appSettings.getAccountCentralUrl().split("//");
    String cookieDomain = accountUrlParts[1];
    return cookieDomain;
  }

  private String createPostAuthRedirectUri(
      String pFromUri, UriBuilder pBuilder, boolean pIsNewAccountUser) {
    Optional<String> sanitizedFromUri =
        sanitizeFromURI(pFromUri, iamAppSettings.isFromUriAllowsLocalhost());
    // Remove fromURI now that it has been used
    pBuilder.replaceQueryParam("fromURI");
    return sanitizedFromUri
        .map(
            uri -> {
              // If the user is new, show them the registration success page
              if (pIsNewAccountUser) {
                UriBuilder newUriBuilder = clearRegistrationQueryParams(pBuilder);
                // Create the fromURI string to include all present query params
                // The fromURI will be decoded when queryString.parse is called on the FE.
                // Since the queryString behavior cannot be changed, just make sure to encode
                // it so the fromURI works properly when decoded:
                // https://github.com/sindresorhus/query-string#parsestring-options
                String fromUri = urlEncode(uri) + newUriBuilder.build().toString();
                newUriBuilder.queryParam("fromURI", fromUri);
                newUriBuilder.path(UserLoginSvc.REGISTRATION_SUCCESS_PAGE_PATH);
                return newUriBuilder.build().toString();
              }
              return mergeQueryParamsAndBuild(UriBuilder.fromUri(uri), pBuilder);
            })
        .orElse(buildCloudAuthorizeUriAfterAuth(pBuilder));
  }

  private String createMultiFactorAuthRedirectUri(
      String pStateToken, Map<String, Object> pClientState, String pUsername) {
    UriBuilder builder =
        UriBuilder.fromPath(AccountAuthResourceConstants.ACCOUNT_MULTI_FACTOR_AUTH_URI);
    if (pClientState != null) {
      pClientState.forEach(
          (key, value) -> {
            if (key == null) {
              LOG.warn("Null query param in client state");
            }
            if (value == null) {
              LOG.warn("Query param {} has null value", key);
            }
            if (key != null && value != null) {
              Object encodedValue =
                  value instanceof String
                      ? UrlUtils.encodeTemplateCharacters((String) value)
                      : value;
              builder.queryParam(key, encodedValue);
            }
          });
    }

    if (pUsername != null) {
      Optional<AccountUser> user = accountUserSvc.findByUsername(pUsername);
      if (user.isEmpty()) {
        LOG.warn(
            "AccountUser collection does not contain user with {}",
            AccountAuditLogEntry.formatUsername(pUsername));
      } else {
        builder.queryParam(
            AccountAuthResourceConstants.OKTA_USER_ID_PARAM, user.get().getOktaUserId());
      }
    }

    builder.queryParam("stateToken", pStateToken);
    return builder.build().toString();
  }

  private String getLoginRedirect(
      HttpServletResponse pResponse,
      String pUsername,
      String pPassword,
      AuditInfo pAuditInfo,
      Map<String, Object> pClientState)
      throws SvcException {
    return getLoginRedirect(pResponse, pUsername, pPassword, pAuditInfo, pClientState, null, true);
  }

  private String getLoginRedirect(
      HttpServletResponse pResponse,
      String pUsername,
      String pPassword,
      AuditInfo pAuditInfo,
      Map<String, Object> pClientState,
      String deviceToken,
      Boolean mfaRequiredExpected)
      throws SvcException {
    if (pUsername == null) {
      throw new SvcException(AppUserErrorCode.MISSING_CREDENTIALS);
    }

    AppUser appUser = userSvc.findByUsername(pUsername);
    AccountUser accountUser = accountUserSvc.findByUsername(pUsername).orElse(null);
    if (iamAppSettings.getMfaRequiredForAllUsers()
        && appUser != null
        && accountUser != null
        && !appUser.hasAccountMultiFactorAuth()
        && !appUser.isMfaSetupRequiredAtLogin()) {
      OKTA_ENROLL_MFA_ON_LOGIN_ATTEMPT_COUNTER.inc();

      // appUser's oktaUserId can unexpectedly be unset despite 1) the corresponding accountUser
      // having an oktaUserId and 2) the user existing in Okta -- use accountUser's oktaUserId to
      // unblock users in this state.
      getUserSvcOkta()
          .setHasAccountMultiFactorAuth(appUser.getUsername(), accountUser.getOktaUserId(), true);
    }

    OktaLoginTransaction transaction =
        getUserSvcOkta()
            .authenticate(
                pUsername,
                pPassword,
                pAuditInfo,
                deviceToken,
                // ensures that MFA_ENROLL case is only handled if user is Atlas user and
                // IAM_MFA_REQUIRED_FOR_ALL_USERS is true
                iamAppSettings.getMfaRequiredForAllUsers() && appUser != null);

    OKTA_MFA_RESULT_TOTAL
        .labels(mfaRequiredExpected.toString(), transaction.getStatus().name())
        .inc();

    return switch (transaction.getStatus()) {
      case SUCCESS -> {
        if (mfaRequiredExpected) {
          LOG.warn(
              "{} received unexpected SUCCESS transaction status",
              AccountAuditLogEntry.formatUsername(pUsername));
        }
        yield oAuthSvc.getAccountLoginRedirect(
            pUsername,
            pResponse,
            new SessionTokenOAuthMethod(transaction.getSessionToken()),
            pClientState);
      }
      case MFA_ENROLL -> {
        accountMultiFactorAuthSvc.enrollAndActivateEmailMfaFactor(
            accountUser.getOktaUserId(), pUsername);

        // initiate a new transaction instead of leveraging Okta's MFA_ENROLL_ACTIVATE transaction
        // status since the work to do so is non-trivial
        OktaLoginTransaction enrolledTransaction =
            getUserSvcOkta().authenticate(pUsername, pPassword, pAuditInfo, deviceToken, true);

        if (enrolledTransaction.getStatus() != Status.MFA_REQUIRED) {
          LOG.info(
              "{} enrolled in MFA but received {} transaction status",
              AccountAuditLogEntry.formatUsername(pUsername),
              enrolledTransaction.getStatus());
          OKTA_ENROLL_MFA_ON_LOGIN_FAILURE_COUNTER.inc();
          throw new SvcException(CommonErrorCode.SERVER_ERROR);
        }

        LOG.info(
            "Successful primary login and MFA enrollment from {} at {}. Proceeding with MFA"
                + " verification",
            AccountAuditLogEntry.formatUsername(pUsername),
            AccountAuditLogEntry.formatRemoteAddress(pAuditInfo.getRemoteAddr()));
        OKTA_ENROLL_MFA_ON_LOGIN_SUCCESS_COUNTER.inc();
        yield createMultiFactorAuthRedirectUri(
            enrolledTransaction.getStateToken(), pClientState, pUsername);
      }
      case MFA_REQUIRED -> {
        LOG.info(
            "Successful primary login from {} at {}. Proceeding with MFA verification",
            AccountAuditLogEntry.formatUsername(pUsername),
            AccountAuditLogEntry.formatRemoteAddress(pAuditInfo.getRemoteAddr()));
        yield createMultiFactorAuthRedirectUri(
            transaction.getStateToken(), pClientState, pUsername);
      }
      default -> {
        LOG.error(
            "Okta primary authentication returned with invalid state: {}", transaction.getStatus());
        throw new SvcException(CommonErrorCode.SERVER_ERROR);
      }
    };
  }

  private UserSvcOkta getUserSvcOkta() {
    // All methods calling this getter are really expecting Okta to be on. If it's not and this cast
    // fails, it's actually a good thing this throws an exception
    return (UserSvcOkta) userSvc.asRuntimeInstance();
  }

  private boolean shouldPreventEmployeePasswordReset(String username) {
    boolean isMongoDBEmployee = userSvc.isMongoDBEmployee(username);
    boolean isEmployeeBypassUsername = userSvc.isEmployeeFederationBypassUser(username);
    return isMongoDBEmployee && !isEmployeeBypassUsername;
  }

  private boolean shouldPreventNonEmployeePasswordReset(String username) {
    if (appSettings.getAppEnv().isNonProd() || appSettings.getAppEnv().isNonProdGov()) {
      return !isDevQaAllowedEmailAddress(username);
    }
    return false;
  }

  private void checkIsUserAgentBlocked(HttpServletRequest pRequest) throws SvcException {
    String userAgent =
        pRequest.getHeader(HttpHeaders.USER_AGENT) != null
            ? pRequest.getHeader(HttpHeaders.USER_AGENT)
            : "null";

    if (appSettings.getBlockedUserAgents().stream().anyMatch(userAgent::equalsIgnoreCase)) {
      LOG.warn(
          "Login attempt from blocked user agent: {}",
          StructuredArguments.kv("userAgent", userAgent));
      throw new SvcException(AppUserErrorCode.INVALID_USER_AUTH);
    }
  }

  /**
   * Determines if ReCaptcha validation is required based on application configuration, user state,
   * and {@link ReCaptchaAction}. If validation is required and fails, an exception is raised.
   */
  private void validateRecaptcha(
      HttpServletRequest request,
      HttpServletResponse response,
      UserLoginForm userLoginForm,
      ReCaptchaAction action,
      boolean enabled,
      double minScore)
      throws SvcException {

    final String username = userLoginForm.getUsername();
    boolean requireRecaptchaValidation =
        isRecaptchaValidationRequired(request, response, username, enabled);

    if (requireRecaptchaValidation) {
      boolean validReCaptchaEnterpriseAssessment =
          recaptchaSvc.validateCaptchaEnterpriseAssessment(
              userLoginForm.getReCaptchaToken(), username, action, minScore);
      if (!validReCaptchaEnterpriseAssessment) {
        throw new SvcException(AppUserErrorCode.INVALID_CAPTCHA);
      }
    }
  }

  /**
   * Determines whether ReCaptcha validation is required for the given user. Note this method has a
   * side effect of updating the given HttpServletResponse such that the device cookie is cleared if
   * it is found to be invalid
   *
   * @param pRequest the HTTP request
   * @param pResponse the HTTP Response
   * @param pUsername the username
   * @return true if ReCaptcha validation is required for the given user, false otherwise
   */
  private boolean isRecaptchaValidationRequired(
      HttpServletRequest pRequest,
      HttpServletResponse pResponse,
      String pUsername,
      boolean recaptchaEnterpriseEnabled) {
    String validationNotRequiredTemplate = "{}. ReCaptcha validation not required for {}";

    if (!recaptchaEnterpriseEnabled) {
      LOG.info(validationNotRequiredTemplate, "ReCaptcha not enabled", kv("username", pUsername));
      return false;
    }

    boolean hasValidRememberDeviceCookie =
        userLoginSvc.hasValidRememberDeviceCookie(pRequest, pResponse, pUsername);

    LOG.info("Device for username={} is remembered={}", pUsername, hasValidRememberDeviceCookie);

    // Check for validation bypass based on device cookie first to avoid user db hit
    if (hasValidRememberDeviceCookie) {
      LOG.info(
          validationNotRequiredTemplate, "Valid device cookie found", kv("username", pUsername));
      return false;
    }

    AppUser appUser = userSvc.findByUsername(pUsername);
    boolean hasAccountMfa = appUser != null && appUser.hasAccountMultiFactorAuth();

    if (hasAccountMfa) {
      LOG.info(validationNotRequiredTemplate, "Account MFA configured", kv("username", pUsername));
      return false;
    }

    return true;
  }

  // Determines whether the backoff level system to mitigate fraud during the SMS MFA
  // flow should be enforced or not
  private boolean shouldEnforceBackoffLevelSystem(
      String oktaUserId, FactorType factorType, String passcode) {
    return oktaUserId != null
        && iamAppSettings.getAccountMultiFactorAuthSmsBackoffLevelsEnabled()
        && accountUserMfaHistorySvc.isSmsFactor(factorType)
        && passcode == null;
  }

  // Notify us when the Factor Type associated with the request
  // is different from the Factor Type in the transaction
  private void verifyFactorTypeIsValid(
      FactorType requestFactorType, FactorType transactionFactorType, String oktaUserId) {
    if (requestFactorType != null && !requestFactorType.equals(transactionFactorType)) {
      LOG.warn(
          "Validation exception in AccountAuthResource: Request factor type {} did not match the"
              + " transaction factor type {} for Okta User ID {}",
          requestFactorType,
          transactionFactorType,
          oktaUserId);
    }
  }

  // Notify us when the Okta User ID associated with the request
  // is different from the Okta User ID in the transaction
  private void verifyOktaUserIdMatchesFactorId(
      String transactionOktaUsername, String requestOktaUserId, String factorId) {
    Optional<AccountUser> accountUser = accountUserSvc.findByUsername(transactionOktaUsername);
    String transactionOktaUserId = "";

    if (accountUser.isPresent()) {
      transactionOktaUserId = accountUser.get().getOktaUserId();

      if (requestOktaUserId != null && !requestOktaUserId.contains(transactionOktaUserId)) {
        LOG.warn(
            "Validation exception in AccountAuthResource: Request Okta User ID {} did not match the"
                + " transaction Okta User ID {} during verification of factor ID {}",
            requestOktaUserId,
            transactionOktaUserId,
            factorId);
      }
    }
  }

  // Calls verifyOktaUserIdMatchesFactorId() and verifyFactorTypeIsValid() to validate the MFA
  // request, then sets the user's country code if the OTP was for SMS MFA
  private void validateMfaRequestAndSetCountryCode(
      OktaMfaAuthTransaction transaction,
      String transactionOktaUsername,
      String requestOktaUserId,
      FactorType requestFactorType,
      String requestFactorId,
      String requestPasscode) {
    verifyOktaUserIdMatchesFactorId(transactionOktaUsername, requestOktaUserId, requestFactorId);

    if (transaction.getEmbedded() != null && transaction.getEmbedded().getFactor() != null) {
      OktaMfaFactor factor = transaction.getEmbedded().getFactor();

      verifyFactorTypeIsValid(requestFactorType, factor.getFactorType(), requestOktaUserId);

      if (shouldEnforceBackoffLevelSystem(requestOktaUserId, requestFactorType, requestPasscode)
          && factor.getProfile() != null) {
        accountUserMfaHistorySvc.setCountryCode(
            requestOktaUserId, factor.getProfile().getOrDefault(PHONE_NUMBER_FIELD, "").toString());
      }
    }
  }
}
