package com.xgen.module.federation.res;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.okta._public.svc.OktaApiSvc;
import com.xgen.cloud.common.okta._public.svc.provider.OktaApiSvcProvider;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.externalanalytics._public.model.IdentityProviderEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.federation._public.errorcode.FederationErrorCode;
import com.xgen.cloud.federation._public.model.ConnectedOrgConfig;
import com.xgen.cloud.federation._public.model.Domain;
import com.xgen.cloud.federation._public.model.FederationSettings;
import com.xgen.cloud.federation._public.model.IdentityProvider;
import com.xgen.cloud.federation._public.svc.ConnectedOrgConfigSvc;
import com.xgen.cloud.federation._public.svc.FederatedUserSvc;
import com.xgen.cloud.federation._public.svc.FederationAppIdentityProvidersSvc;
import com.xgen.cloud.federation._public.svc.FederationSettingsSvc;
import com.xgen.cloud.federation._public.svc.IdentityProviderSvc;
import com.xgen.cloud.federation._public.svc.OidcIdentityProviderSvc;
import com.xgen.cloud.federation._public.view.ApiIdentityProviderRequestView;
import com.xgen.cloud.federation._public.view.ApiIdentityProviderResponseView;
import com.xgen.cloud.federation._public.view.FederatedUserView;
import com.xgen.cloud.federation._public.view.IdentityProviderUpdate;
import com.xgen.cloud.federation._public.view.OidcIssuerUri;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.sandbox._public.model.SandboxEnabledFederation;
import com.xgen.cloud.sandbox._public.svc.SandboxEnabledFederationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.federation.view.DomainPatch;
import com.xgen.module.federation.view.FederatedUsersView;
import com.xgen.module.federation.view.RestrictOrgMembershipView;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.NotFoundException;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;

/**
 * Resource for managing federation settings, identity providers, and associated domains. Endpoints
 * in this resource are protected by {@link
 * com.xgen.module.federation.filter.FederationSettingsAccessFilter} but are also annotated with
 * {@link UiCall} so that non-authorization filters still get applied and endpoint actions are
 * generated.
 */
@Path("/federationSettings")
@Singleton
public class FederationSettingsResource extends BaseResource {
  protected static final short ALLOWED_HOSTNAME_LENGTH = 255;
  protected static final byte ALLOWED_DISPLAY_NAME_LENGTH = 50;
  protected static final short MAX_USER_RETURN_LIMIT = 1000;

  private static final String DISPLAY_NAME_REQUIREMENTS =
      "%s cannot be blank and must not exceed " + ALLOWED_DISPLAY_NAME_LENGTH + " characters.";
  private static final Pattern HOSTNAME_PATTERN =
      Pattern.compile("^(?:[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*\\.)+[a-zA-Z]{2,}$");

  private final FederationSettingsSvc federationSettingsSvc;
  private final FederationAppIdentityProvidersSvc federationAppIdentityProvidersSvc;
  private final OktaApiSvcProvider oktaApiSvcProvider;
  private final FederatedUserSvc federatedUserSvc;
  private final AuthzSvc authzSvc;
  private final ConnectedOrgConfigSvc connectedOrgConfigSvc;
  private final FeatureFlagSvc featureFlagSvc;
  private final IdentityProviderSvc<
          IdentityProvider,
          IdentityProviderUpdate,
          ApiIdentityProviderRequestView,
          ApiIdentityProviderResponseView>
      identityProviderSvc;
  private final OrganizationSvc organizationSvc;
  private final OidcIdentityProviderSvc oidcIdentityProviderSvc;
  private final SegmentEventSvc segmentEventSvc;
  private final SandboxEnabledFederationSvc sandboxEnabledFederationSvc;

  @Inject
  public FederationSettingsResource(
      final FederationSettingsSvc federationSettingsSvc,
      final FederationAppIdentityProvidersSvc federationAppIdentityProvidersSvc,
      final OktaApiSvcProvider oktaApiSvcProvider,
      final FederatedUserSvc federatedUserSvc,
      final AuthzSvc authzSvc,
      final ConnectedOrgConfigSvc connectedOrgConfigSvc,
      final FeatureFlagSvc featureFlagSvc,
      final IdentityProviderSvc<
              IdentityProvider,
              IdentityProviderUpdate,
              ApiIdentityProviderRequestView,
              ApiIdentityProviderResponseView>
          identityProviderSvc,
      final OrganizationSvc organizationSvc,
      final OidcIdentityProviderSvc oidcIdentityProviderSvc,
      final SegmentEventSvc segmentEventSvc,
      final SandboxEnabledFederationSvc sandboxEnabledFederationSvc) {
    this.federationSettingsSvc = federationSettingsSvc;
    this.federationAppIdentityProvidersSvc = federationAppIdentityProvidersSvc;
    this.oktaApiSvcProvider = oktaApiSvcProvider;
    this.federatedUserSvc = federatedUserSvc;
    this.authzSvc = authzSvc;
    this.connectedOrgConfigSvc = connectedOrgConfigSvc;
    this.featureFlagSvc = featureFlagSvc;
    this.identityProviderSvc = identityProviderSvc;
    this.organizationSvc = organizationSvc;
    this.oidcIdentityProviderSvc = oidcIdentityProviderSvc;
    this.segmentEventSvc = segmentEventSvc;
    this.sandboxEnabledFederationSvc = sandboxEnabledFederationSvc;
  }

  @GET
  @Path("/{federationSettingsId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_READ_ONLY},
      groupSource = UiCall.GroupSource.NONE)
  public Response getById(@PathParam("federationSettingsId") final ObjectId federationSettingsId) {
    return federationSettingsSvc
        .findById(federationSettingsId)
        .map(settings -> Response.ok().entity(settings).build())
        .orElse(Response.status(HttpStatus.SC_NOT_FOUND).build());
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response create(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      final FederationSettings pFederationSettings)
      throws SvcException {
    if (pFederationSettings == null) {
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "Request body cannot be null.");
    }

    final var orgIds =
        pFederationSettings.getConnectedOrgConfigs().stream()
            .map(ConnectedOrgConfig::getOrgId)
            .collect(toSet());
    if (!canUserModifyOrgSettings(pUser, orgIds)) {
      return Response.status(HttpStatus.SC_UNAUTHORIZED).build();
    }

    final boolean allRolesAreOrgRoles =
        pFederationSettings.getConnectedOrgConfigs().stream()
            .flatMap(config -> config.getPostAuthRoleGrants().stream())
            .allMatch(Role::isOrgSpecific);
    if (!allRolesAreOrgRoles) {
      throw new SvcException(FederationErrorCode.ONLY_ORG_ROLES_ALLOWED);
    }

    getFederationSettingsSvc().create(pFederationSettings, pAuditInfo);
    orgIds.stream()
        .forEach(
            orgId ->
                getConnectedOrgConfigSvc()
                    .createOrgIdpCertificateAboutToExpireAlertConfig(orgId, pAuditInfo));

    return Response.ok(pFederationSettings).build();
  }

  @DELETE
  @Path("/{federationSettingsId}")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "wrongggg")
  public Response delete(
      @Context final AuditInfo pAuditInfo,
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId)
      throws SvcException {
    if (getFederationSettingsSvc().findById(pFederationSettingsId).isEmpty()) {
      return Response.status(HttpStatus.SC_NOT_FOUND).build();
    }

    getFederationSettingsSvc()
        .findById(pFederationSettingsId)
        .get()
        .getConnectedOrgConfigs()
        .stream()
        .map(ConnectedOrgConfig::getOrgId)
        .forEach(
            orgId ->
                getConnectedOrgConfigSvc()
                    .deleteOrgIdpCertificateAboutToExpireAlertConfig(orgId, pAuditInfo));
    getFederationSettingsSvc().delete(pFederationSettingsId, pAuditInfo);

    return Response.status(Response.Status.NO_CONTENT).build();
  }

  @PUT
  @Path("/{federationSettingsId}/restrictOrgMembership")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  @Auth(endpointAction = "wrongggg2")
  public Response setRestrictOrgMembership(
      @Context final AuditInfo pAuditInfo,
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      final RestrictOrgMembershipView pRestrictOrgMembershipView)
      throws SvcException {
    if (pRestrictOrgMembershipView == null) {
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "The request body cannot be empty.");
    }

    final var federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    return Response.ok(
            getFederationSettingsSvc()
                .updateOrgMembershipRestrictionSetting(
                    federationSettings,
                    pRestrictOrgMembershipView.isRestrictOrgMembershipEnabled(),
                    pAuditInfo))
        .build();
  }

  @POST
  @Path("/{federationSettingsId}/identityProviders")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response createIdentityProvider(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      final IdentityProvider pIdentityProvider)
      throws SvcException {
    if (pIdentityProvider == null) {
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "The request body cannot be empty.");
    }
    final FederationSettings federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    final Map<String, String> validationErrors =
        identityProviderSvc.validateIdentityProvider(pIdentityProvider, federationSettings);

    if (!validationErrors.isEmpty()) {
      return SimpleApiResponse.badRequest(CommonErrorCode.VALIDATION_ERROR)
          .customField("errors", validationErrors)
          .build();
    }

    final IdentityProvider newIdentityProvider =
        identityProviderSvc.createIdentityProvider(
            pIdentityProvider, federationSettings, pAuditInfo);

    submitIdentityProviderSegmentEvent(
        IdentityProviderEvent.IDENTITY_PROVIDER_CREATED_EVENT_TYPE,
        newIdentityProvider,
        pFederationSettingsId);

    return Response.ok().entity(newIdentityProvider).build();
  }

  @GET
  @Path("/{federationSettingsId}/identityProviders/{oktaIdpId}/metadata.xml")
  @Produces({MediaType.APPLICATION_OCTET_STREAM})
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_READ_ONLY},
      groupSource = UiCall.GroupSource.NONE)
  public Response getIdentityProviderMetadata(
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      @PathParam("oktaIdpId") final String pOktaIdpId)
      throws SvcException {
    final var federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    federationSettings
        .getSamlIdentityProviderByOktaIdpId(pOktaIdpId)
        .orElseThrow(NotFoundException::new);

    final String metadata = getOktaApiSvc().getIdentityProviderMetadata(pOktaIdpId);

    return Response.ok(metadata)
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=metadata.xml")
        .build();
  }

  @PATCH
  @Path("/{federationSettingsId}/identityProviders/{idpId}")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response updateIdentityProvider(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      @PathParam("idpId") final ObjectId pIdpId,
      final IdentityProviderUpdate pIdentityProviderUpdate)
      throws SvcException {
    if (pIdentityProviderUpdate == null) {
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "The request body cannot be empty.");
    }

    final FederationSettings federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    final IdentityProvider existingIdentityProvider =
        federationSettings.getIdentityProviderById(pIdpId).orElseThrow(NotFoundException::new);

    final IdentityProvider identityProviderWithUpdates =
        identityProviderSvc.createIdentityProviderFromUpdate(
            pIdentityProviderUpdate, existingIdentityProvider);
    final Map<String, String> validationErrors =
        identityProviderSvc.validateIdentityProvider(
            identityProviderWithUpdates, federationSettings);

    if (!validationErrors.isEmpty()) {
      return SimpleApiResponse.badRequest(CommonErrorCode.VALIDATION_ERROR)
          .customField("errors", validationErrors)
          .build();
    }

    final IdentityProvider updatedIdentityProvider =
        identityProviderSvc.updateIdentityProvider(
            identityProviderWithUpdates, existingIdentityProvider, federationSettings, pAuditInfo);

    return Response.ok().entity(updatedIdentityProvider).build();
  }

  @POST
  @Path("/{federationSettingsId}/identityProviders/{oktaIdpId}/activate")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response activateIdentityProvider(
      @Context final AuditInfo pAuditInfo, @PathParam("oktaIdpId") final String pOktaIdpId)
      throws SvcException {

    getFederationAppIdentityProvidersSvc()
        .activateSamlIdentityProvider(pOktaIdpId, pAuditInfo)
        .orElseThrow(NotFoundException::new);

    return Response.noContent().build();
  }

  @POST
  @Path("/{federationSettingsId}/identityProviders/{oktaIdpId}/deactivate")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response deactivateIdentityProvider(
      @Context final AuditInfo pAuditInfo, @PathParam("oktaIdpId") final String pOktaIdpId)
      throws SvcException {
    getFederationAppIdentityProvidersSvc()
        .deactivateSamlIdentityProvider(pOktaIdpId, pAuditInfo)
        .orElseThrow(NotFoundException::new);

    return Response.noContent().build();
  }

  @PUT
  @Path("/{federationSettingsId}/identityProviders/{idpId}/associatedDomains")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response addAssociatedDomains(
      @Context final AuditInfo pAuditInfo,
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      @PathParam("idpId") final ObjectId pIdpId,
      final Set<String> pAssociatedDomains)
      throws SvcException {
    final FederationSettings federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    federationSettings.getIdentityProviderById(pIdpId).orElseThrow(NotFoundException::new);

    final IdentityProvider updatedIdentityProvider =
        getFederationAppIdentityProvidersSvc()
            .associateDomainsToIdentityProvider(
                federationSettings, pIdpId, pAssociatedDomains, pAuditInfo);
    return Response.ok().entity(updatedIdentityProvider).build();
  }

  @DELETE
  @Path("/{federationSettingsId}/identityProviders/{idpId}")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response deleteIdentityProvider(
      @Context final AuditInfo pAuditInfo,
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      @PathParam("idpId") final ObjectId pIdpId)
      throws SvcException {
    final FederationSettings federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    final IdentityProvider identityProvider =
        federationSettings.getIdentityProviderById(pIdpId).orElseThrow(NotFoundException::new);

    getFederationAppIdentityProvidersSvc()
        .deleteIdentityProvider(federationSettings, identityProvider, pAuditInfo);

    submitIdentityProviderSegmentEvent(
        IdentityProviderEvent.IDENTITY_PROVIDER_DELETED_EVENT_TYPE,
        identityProvider,
        pFederationSettingsId);

    return Response.noContent().build();
  }

  @POST
  @Path("/{federationSettingsId}/identityProviders/{idpId}/revokeJwks")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response revokeOidcJwks(
      @Context final AuditInfo auditInfo,
      @PathParam("federationSettingsId") final ObjectId federationSettingsId,
      @PathParam("idpId") final ObjectId idpId)
      throws SvcException {
    final FederationSettings federationSettings =
        getFederationSettingsSvc()
            .findById(federationSettingsId)
            .orElseThrow(NotFoundException::new);
    oidcIdentityProviderSvc.revokeJwks(auditInfo, federationSettings, idpId);
    return Response.status(HttpStatus.SC_NO_CONTENT).build();
  }

  @POST
  @Path("/{federationSettingsId}/domains")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response createDomain(
      @Context final AuditInfo pAuditInfo,
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      final Domain pDomain)
      throws SvcException {

    if (pDomain == null) {
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "The request body cannot be empty.");
    }

    final var federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    final var validationErrors = validateDomain(pDomain);
    if (!validationErrors.isEmpty()) {
      return SimpleApiResponse.badRequest(CommonErrorCode.VALIDATION_ERROR)
          .customField("errors", validationErrors)
          .build();
    }

    final var domain =
        getFederationSettingsSvc().createDomain(federationSettings, pDomain, pAuditInfo);
    return Response.ok().entity(domain).build();
  }

  @DELETE
  @Path("/{federationSettingsId}/domains/{domainId}")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response deleteDomain(
      @Context final AuditInfo pAuditInfo,
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      @PathParam("domainId") final ObjectId pDomainId)
      throws SvcException {
    final var federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    final var domain =
        federationSettings.getDomains().stream()
            .filter(ed -> ed.getId().equals(pDomainId))
            .findFirst()
            .orElseThrow(NotFoundException::new);

    getFederationSettingsSvc().deleteDomain(federationSettings, domain, pAuditInfo);

    return Response.noContent().build();
  }

  @PATCH
  @Path("/{federationSettingsId}/domains/{domainId}")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response verifyDomain(
      @Context final AuditInfo pAuditInfo,
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      @PathParam("domainId") final ObjectId pDomainId,
      final DomainPatch pDomainPatch)
      throws SvcException {
    final var federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    final var domain =
        federationSettings.getDomains().stream()
            .filter(ed -> ed.getId().equals(pDomainId))
            .findFirst()
            .orElseThrow(NotFoundException::new);

    if (pDomainPatch.getOp() == DomainPatch.Op.VERIFY) {
      final Domain verifiedDomain =
          getFederationSettingsSvc().verifyDomain(pFederationSettingsId, domain, pAuditInfo);
      return Response.ok().entity(verifiedDomain).build();
    } else {
      return Response.status(HttpStatus.SC_BAD_REQUEST).build();
    }
  }

  @GET
  @Path("/{federationSettingsId}/users")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_READ_ONLY},
      groupSource = UiCall.GroupSource.NONE)
  public Response getFederatedUsers(
      @PathParam("federationSettingsId") final ObjectId pFederationSettingsId,
      @QueryParam("limit") @DefaultValue("100") final int pLimit,
      @QueryParam("after") final ObjectId pAfterUserId,
      @QueryParam("filter") final UserFilter pFilter) {
    final var federationSettings =
        getFederationSettingsSvc()
            .findById(pFederationSettingsId)
            .orElseThrow(NotFoundException::new);

    final var requestLimit = Math.min(pLimit, MAX_USER_RETURN_LIMIT);
    // Query for one more than requested so we can indicated if there are more users
    final var queryLimit = requestLimit + 1;

    final List<AppUser> federatedUsers;

    if (pFilter != null) {
      if (pFilter == UserFilter.ORG_RESTRICTION_CONFLICTS) {
        federatedUsers =
            getFederatedUserSvc()
                .findFederatedUsersInConflictWithOrgRestrictions(
                    federationSettings, queryLimit, pAfterUserId);
      } else {
        return Response.status(Response.Status.BAD_REQUEST).entity("Unrecognized filter").build();
      }
    } else {
      federatedUsers =
          getFederatedUserSvc().findFederatedUsers(federationSettings, queryLimit, pAfterUserId);
    }

    final List<FederatedUserView> userViews =
        federatedUsers.stream()
            .map(
                appUser ->
                    new FederatedUserView(
                        appUser.getId(),
                        appUser.getUsername(),
                        appUser.getFirstName(),
                        appUser.getLastName(),
                        federationSettings.getId()))
            .limit(requestLimit)
            .collect(toList());

    return Response.ok(
            new FederatedUsersView(
                userViews,
                federatedUsers.size() > requestLimit
                    ? federatedUsers.get(requestLimit).getId()
                    : null))
        .build();
  }

  @POST
  @Path("/verifyIdpIssuerUri")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_USER_ADMIN},
      groupSource = UiCall.GroupSource.NONE)
  public Response verifyIdpIssuerUri(final OidcIssuerUri oidcIssuerUri) throws SvcException {
    identityProviderSvc.validateIssuerUri(oidcIssuerUri.getIssuerUri());
    return Response.ok().build();
  }

  @GET
  @Path("/{federationSettingsId}/featureFlags/{featureFlagName}/status")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_READ_ONLY},
      groupSource = GroupSource.NONE)
  public Response getFeatureFlagStatus(
      @PathParam("federationSettingsId") final ObjectId federationSettingsId,
      @PathParam("featureFlagName") final String featureFlagName)
      throws SvcException {
    final FederationSettings federationSettings =
        getFederationSettingsSvc()
            .findById(federationSettingsId)
            .orElseThrow(NotFoundException::new);

    final FeatureFlag featureFlag;
    try {
      featureFlag = FeatureFlag.valueOf(featureFlagName);
    } catch (final IllegalArgumentException e) {
      throw new SvcException(CommonErrorCode.FEATURE_FLAG_NOT_FOUND, featureFlagName);
    }

    final JSONObject response = new JSONObject();
    response.put("enabled", isFeatureFlagEnabled(federationSettings, featureFlag));

    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/{federationSettingsId}/isSandboxCreationEnabled")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_READ_ONLY},
      groupSource = UiCall.GroupSource.NONE)
  public Response getIsSanboxCreationEnabled(
      @PathParam("federationSettingsId") final ObjectId federationSettingsId) {
    boolean isEnabled =
        sandboxEnabledFederationSvc
            .findByFederationId(federationSettingsId)
            .map(SandboxEnabledFederation::isSandboxOrgCreationEnabled)
            .orElse(false);
    return Response.ok(isEnabled).build();
  }

  private Map<String, String> validateDomain(final Domain pDomain) {
    final var errors = new HashMap<String, String>();
    if (StringUtils.isEmpty(pDomain.getDisplayName())
        || pDomain.getDisplayName().length() > ALLOWED_DISPLAY_NAME_LENGTH) {
      errors.put(
          Domain.FieldDefs.DISPLAY_NAME,
          String.format(DISPLAY_NAME_REQUIREMENTS, Domain.FieldDefs.DISPLAY_NAME));
    }

    if (StringUtils.isEmpty(pDomain.getHostName())
        || !HOSTNAME_PATTERN.matcher(pDomain.getHostName()).matches()) {
      errors.put(Domain.FieldDefs.HOSTNAME, pDomain.getHostName() + " is not a valid hostname");
    }

    if (pDomain.getHostName().length() > ALLOWED_HOSTNAME_LENGTH) {
      errors.put(
          Domain.FieldDefs.HOSTNAME,
          String.format("Hostname cannot exceed %d characters", ALLOWED_HOSTNAME_LENGTH));
    }

    return errors;
  }

  private boolean canUserModifyOrgSettings(
      final AppUser pAppUser, final Collection<ObjectId> pOrgIds) {
    return getAuthzSvc().isGlobalUserAdmin(pAppUser)
        || pOrgIds.size() == 0
        || pOrgIds.stream().allMatch(orgId -> getAuthzSvc().isOrgOwner(pAppUser, orgId));
  }

  private boolean isFeatureFlagEnabled(
      final FederationSettings federationSettings, final FeatureFlag featureFlag) {
    return federationSettings.getConnectedOrgConfigs().stream()
        .map(connectedOrgConfig -> organizationSvc.findById(connectedOrgConfig.getOrgId()))
        .anyMatch(org -> featureFlagSvc.isFeatureFlagEnabled(featureFlag, org, null));
  }

  private FederationSettingsSvc getFederationSettingsSvc() {
    return federationSettingsSvc;
  }

  private FederationAppIdentityProvidersSvc getFederationAppIdentityProvidersSvc() {
    return federationAppIdentityProvidersSvc;
  }

  private OktaApiSvcProvider getOktaApiSvcProvider() {
    return oktaApiSvcProvider;
  }

  private FederatedUserSvc getFederatedUserSvc() {
    return federatedUserSvc;
  }

  private OktaApiSvc getOktaApiSvc() {
    return getOktaApiSvcProvider().get();
  }

  private AuthzSvc getAuthzSvc() {
    return authzSvc;
  }

  private ConnectedOrgConfigSvc getConnectedOrgConfigSvc() {
    return connectedOrgConfigSvc;
  }

  private void submitIdentityProviderSegmentEvent(
      final String eventType,
      final IdentityProvider identityProvider,
      final ObjectId federationSettingsId) {
    segmentEventSvc.submitEvent(
        IdentityProviderEvent.builder(eventType)
            .accessType(identityProvider.getAccessType().toString())
            .displayName(identityProvider.getDisplayName())
            .federationSettingsId(federationSettingsId)
            .identityProviderId(identityProvider.getId())
            .idpType(identityProvider.getIdpType().toString())
            .protocol(identityProvider.getProtocol().getProtocol())
            .build());
  }

  public enum UserFilter {
    ORG_RESTRICTION_CONFLICTS
  }
}
