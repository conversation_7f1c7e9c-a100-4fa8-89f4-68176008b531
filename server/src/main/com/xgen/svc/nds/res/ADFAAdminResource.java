package com.xgen.svc.nds.res;

import com.mongodb.DBObject;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.nds.activity._public.event.audit.ADFAAdminAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ADFAAdminAudit.Type;
import com.xgen.svc.mms.api.view.atlas._private.dataLake.ApiPrivateAtlasDataLakeTenantConfigView;
import com.xgen.svc.nds.model.ui.AndonCordContainer;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeAndonCordView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCurrentOpView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeDataSetView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantSettingsView;
import com.xgen.svc.nds.svc.NDSDataLakePrivateSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import org.bson.types.ObjectId;

@Path("/nds/adfa/admin/")
@Singleton
public class ADFAAdminResource extends BaseResource {
  private final NDSDataLakePrivateSvc _ndsDataLakePrivateSvc;
  private final AuditSvc _auditSvc;

  @Inject
  public ADFAAdminResource(
      final NDSDataLakePrivateSvc pNDSDataLakePrivateSvc, final AuditSvc pAuditSvc) {
    _ndsDataLakePrivateSvc = pNDSDataLakePrivateSvc;
    _auditSvc = pAuditSvc;
  }

  @GET
  @Path("/listAndonCords")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADFA_ADMIN_READ},
      groupSource = GroupSource.NONE)
  public Response listAndonCords(
      @Context final AuditInfo pAuditInfo,
      @QueryParam("region") final String pRegion,
      @QueryParam("name") final String pName,
      @QueryParam("state") final String pState,
      @QueryParam("skip") @DefaultValue("0") final int pSkip,
      @QueryParam("limit") @DefaultValue("100") final int pLimit)
      throws Exception {
    List<NDSDataLakeAndonCordView> andonCords =
        _ndsDataLakePrivateSvc.listAndonCords(pRegion, pName, pState, pLimit + 1, pSkip);

    final List<NDSDataLakeAndonCordView> data =
        andonCords.size() < pLimit ? andonCords : andonCords.subList(0, pLimit);
    final boolean isLastPage = andonCords.isEmpty() || andonCords.size() <= pLimit;
    final boolean isFirstPage = pSkip <= 0;
    final AndonCordContainer result = new AndonCordContainer(data, isFirstPage, isLastPage);

    ADFAAdminAudit.Builder builder =
        new ADFAAdminAudit.Builder(Type.ADFA_LIST_ANDON_CORDS_ACTION_COMPLETED);
    builder.auditInfo(pAuditInfo);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok(result).build();
  }

  @PUT
  @Path("/createOrUpdateAndonCord")
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADFA_ADMIN_WRITE},
      groupSource = GroupSource.NONE)
  public Response createOrUpdateAndonCord(
      @Context final AuditInfo pAuditInfo,
      @QueryParam("override") @DefaultValue("false") final boolean pOverride,
      final NDSDataLakeCreateOrUpdateAndonCordView pAndonCordView)
      throws Exception {
    _ndsDataLakePrivateSvc.createOrUpdateAndonCord(pAndonCordView, pOverride);

    ADFAAdminAudit.Builder builder =
        new ADFAAdminAudit.Builder(Type.ADFA_CREATE_OR_UPDATE_ANDON_CORD_ACTION_COMPLETED);
    builder.auditInfo(pAuditInfo);

    DBObject data = pAndonCordView.toDBObject();
    data.put("override", pOverride);

    builder.data(data);

    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @GET
  @Path("/getTenantSettings")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADFA_ADMIN_READ},
      groupSource = GroupSource.NONE)
  public Response getTenantSettings(
      @Context final AuditInfo pAuditInfo, @QueryParam("tenantId") final String pTenantId)
      throws Exception {
    ObjectId tenantIdVerified = new ObjectId(pTenantId);
    NDSDataLakeTenantSettingsView tenantSettings =
        _ndsDataLakePrivateSvc.getTenantSettings(tenantIdVerified);

    ADFAAdminAudit.Builder builder =
        new ADFAAdminAudit.Builder(Type.ADFA_TENANT_GET_SETTINGS_ACTION_COMPLETED);
    builder.auditInfo(pAuditInfo);
    builder.tenantId(tenantIdVerified);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok(tenantSettings).build();
  }

  @PUT
  @Path("/setTenantSettings")
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADFA_ADMIN_WRITE},
      groupSource = GroupSource.NONE)
  public Response setTenantSettings(
      @Context final AuditInfo pAuditInfo,
      @QueryParam("tenantId") final String pTenantId,
      final NDSDataLakeTenantSettingsView pSettingsView)
      throws Exception {
    ObjectId tenantIdVerified = new ObjectId(pTenantId);
    _ndsDataLakePrivateSvc.setTenantSettings(tenantIdVerified, pSettingsView);

    ADFAAdminAudit.Builder builder =
        new ADFAAdminAudit.Builder(Type.ADFA_TENANT_SET_SETTINGS_ACTION_COMPLETED);
    builder.auditInfo(pAuditInfo);
    builder.tenantId(tenantIdVerified);

    DBObject data = pSettingsView.toDBObject();

    builder.data(data);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @GET
  @Path("/tenantStorageConfig")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADFA_ADMIN_READ},
      groupSource = GroupSource.NONE)
  public Response getTenantStorageConfig(
      @Context final AuditInfo pAuditInfo, @QueryParam("tenantId") final String pTenantId)
      throws Exception {
    ObjectId tenantIdVerified = new ObjectId(pTenantId);
    NDSDataLakeStorageV1View tenantStorageConfig =
        _ndsDataLakePrivateSvc.getTenantStorageConfig(tenantIdVerified);

    ADFAAdminAudit.Builder builder =
        new ADFAAdminAudit.Builder(Type.ADFA_TENANT_GET_STORAGE_CONFIG_ACTION_COMPLETED);
    builder.auditInfo(pAuditInfo);
    builder.tenantId(tenantIdVerified);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok(tenantStorageConfig).build();
  }

  @DELETE
  @Path("/tenantStorageConfig")
  @UiCall(
      roles = {RoleSet.GLOBAL_ADFA_ADMIN_WRITE},
      groupSource = GroupSource.NONE)
  public Response deleteTenantStorageConfig(
      @Context final AuditInfo pAuditInfo, @QueryParam("tenantId") final String pTenantId)
      throws Exception {
    ObjectId tenantIdVerified = new ObjectId(pTenantId);
    _ndsDataLakePrivateSvc.deleteTenantStorageConfig(tenantIdVerified);

    ADFAAdminAudit.Builder builder =
        new ADFAAdminAudit.Builder(Type.ADFA_TENANT_DELETE_STORAGE_CONFIG_ACTION_COMPLETED);
    builder.auditInfo(pAuditInfo);
    builder.tenantId(tenantIdVerified);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok().build();
  }

  @GET
  @Path("/tenantConfig")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADFA_ADMIN_READ},
      groupSource = GroupSource.NONE)
  public Response getTenantConfig(
      @Context final AuditInfo pAuditInfo, @QueryParam("tenantId") final String pTenantId)
      throws Exception {
    ObjectId tenantIdVerified = new ObjectId(pTenantId);
    ApiPrivateAtlasDataLakeTenantConfigView tenantConfig =
        _ndsDataLakePrivateSvc.getTenantConfig(tenantIdVerified);

    ADFAAdminAudit.Builder builder =
        new ADFAAdminAudit.Builder(Type.ADFA_TENANT_GET_CONFIG_ACTION_COMPLETED);
    builder.auditInfo(pAuditInfo);
    builder.tenantId(tenantIdVerified);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok(tenantConfig).build();
  }

  @GET
  @Path("/dataSets")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADFA_ADMIN_READ},
      groupSource = GroupSource.NONE)
  public Response listDataSets(
      @Context final AuditInfo pAuditInfo, @QueryParam("projectId") final String pProjectId)
      throws Exception {
    final List<NDSDataLakeDataSetView> dataSets = _ndsDataLakePrivateSvc.listDataSets(pProjectId);

    final ADFAAdminAudit.Builder builder =
        new ADFAAdminAudit.Builder(Type.ADFA_LIST_DATA_SETS_ACTION_COMPLETED);
    builder.auditInfo(pAuditInfo);
    _auditSvc.saveAuditEvent(builder.build());

    return Response.ok(dataSets).build();
  }

  @GET
  @Path("/currentOps")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GLOBAL_ADFA_ADMIN_READ},
      groupSource = GroupSource.NONE)
  public Response listCurrentOps(
      @Context final AuditInfo pAuditInfo, @QueryParam("tenantId") final String pTenantId)
      throws Exception {
    ObjectId tenantIdVerified = new ObjectId(pTenantId);
    List<NDSDataLakeCurrentOpView> currentOps =
        _ndsDataLakePrivateSvc.listCurrentOps(pAuditInfo, tenantIdVerified);

    return Response.ok(currentOps).build();
  }
}
