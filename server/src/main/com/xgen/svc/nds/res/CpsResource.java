package com.xgen.svc.nds.res;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;

import com.mongodb.ReadPreference;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.auditinfosvc._public.svc.AuditInfoSvc;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.util._public.util.NetUtils;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob.AutoExportSettings;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.CopySetting;
import com.xgen.cloud.cps.backupjob._public.model.CustomerCollectionMetadata;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings;
import com.xgen.cloud.cps.backupjob._public.model.ExtraRetentionSetting;
import com.xgen.cloud.cps.backupjob._public.model.Policy;
import com.xgen.cloud.cps.backupjob._public.ui.AutoExportPolicyView;
import com.xgen.cloud.cps.backupjob._public.ui.AwsCrossProjectRestoreView;
import com.xgen.cloud.cps.backupjob._public.ui.AzureExtendedStorageView;
import com.xgen.cloud.cps.backupjob._public.ui.BackupJobView;
import com.xgen.cloud.cps.backupjob._public.ui.BackupScheduleView;
import com.xgen.cloud.cps.backupjob._public.ui.BackupSnapshotRetentionView;
import com.xgen.cloud.cps.backupjob._public.ui.CopySettingView;
import com.xgen.cloud.cps.backupjob._public.ui.CustomerCollectionMetadataView;
import com.xgen.cloud.cps.backupjob._public.ui.DataProtectionClusterView;
import com.xgen.cloud.cps.backupjob._public.ui.DataProtectionSettingsView;
import com.xgen.cloud.cps.backupjob._public.ui.ExtraRetentionSettingView;
import com.xgen.cloud.cps.backupjob._public.ui.ServerlessBackupJobView;
import com.xgen.cloud.cps.restore._public.model.AWSExportBucket;
import com.xgen.cloud.cps.restore._public.model.BackupRestoreJobView;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreJob;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreJobView;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreState;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.CanceledReason;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.UserRequestType;
import com.xgen.cloud.cps.restore._public.model.ExportBucket;
import com.xgen.cloud.cps.restore._public.model.ExportBucket.BucketType;
import com.xgen.cloud.cps.restore._public.model.ExportStatus;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.RestoreTargetCluster;
import com.xgen.cloud.cps.restore._public.model.ServerlessRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.TopologyHandler;
import com.xgen.cloud.cps.restore._public.ui.BackupCursorFileListResponseView;
import com.xgen.cloud.cps.restore._public.ui.BackupSnapshotView;
import com.xgen.cloud.cps.restore._public.ui.CollectionRestoreRequestView;
import com.xgen.cloud.cps.restore._public.ui.CpsPitRestoreRangeView;
import com.xgen.cloud.cps.restore._public.ui.UnifiedRestoreJobView;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._public.svc.FlexMigrationSvc;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermission;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermissionList;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantRestore;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.nds.model.ui.IpAllowedAccessListResponseView;
import com.xgen.svc.nds.svc.NDSTenantBackupSvc;
import com.xgen.svc.nds.svc.ServerlessBackupSvc;
import com.xgen.svc.nds.svc.cps.CpsCollectionMetadataBackupSvc;
import com.xgen.svc.nds.svc.cps.CpsCollectionRestoreJobSvc;
import com.xgen.svc.nds.svc.cps.CpsExportSvc;
import com.xgen.svc.nds.svc.cps.CpsPolicySvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/*
 * Routes for Atlas Backup
 */
@Path("/nds/backup")
@Singleton
public class CpsResource extends NDSBaseResource {

  private final CpsPolicySvc _cpsPolicySvc;
  private final CpsSvc _cpsSvc;
  private final CpsExportSvc _cpsExportSvc;
  private final NDSUISvc _ndsUiSvc;
  private final NDSClusterSvc _ndsClusterSvc;
  private final NDSTenantBackupSvc _ndsTenantBackupSvc;
  private final GroupSvc _groupSvc;
  private final ServerlessBackupSvc _serverlessBackupSvc;
  private final AuthzSvc _authzSvc;
  private final NDSGroupSvc _ndsGroupSvc;
  private final AppSettings _appSettings;
  private final FlexMigrationSvc _flexMigrationSvc;
  private final CpsCollectionMetadataBackupSvc _cpsCollectionMetadataBackupSvc;
  private final CpsCollectionRestoreJobSvc _cpsCollectionRestoreJobSvc;
  private final AuditInfoSvc _auditInfoSvc;

  private static final Logger LOG = LoggerFactory.getLogger(CpsResource.class);

  @Inject
  public CpsResource(
      final CpsPolicySvc pCpsPolicySvc,
      final CpsSvc pCpsSvc,
      final CpsExportSvc pCpsExportSvc,
      final NDSUISvc pNdsUiSvc,
      final GroupSvc pGroupSvc,
      final NDSClusterSvc pClusterSvc,
      final NDSTenantBackupSvc pNDSTenantBackupSvc,
      final ServerlessBackupSvc pServerlessBackupSvc,
      final AuthzSvc pAuthzSvc,
      final NDSGroupSvc pNdsGroupSvc,
      final AppSettings pAppSettings,
      final FlexMigrationSvc pFlexMigrationSvc,
      final CpsCollectionMetadataBackupSvc cpsCollectionMetadataBackupSvc,
      final CpsCollectionRestoreJobSvc cpsCollectionRestoreJobSvc,
      final AuditInfoSvc auditInfoSvc) {
    _cpsPolicySvc = pCpsPolicySvc;
    _cpsSvc = pCpsSvc;
    _cpsExportSvc = pCpsExportSvc;
    _ndsUiSvc = pNdsUiSvc;
    _groupSvc = pGroupSvc;
    _ndsClusterSvc = pClusterSvc;
    _ndsTenantBackupSvc = pNDSTenantBackupSvc;
    _serverlessBackupSvc = pServerlessBackupSvc;
    _authzSvc = pAuthzSvc;
    _ndsGroupSvc = pNdsGroupSvc;
    _appSettings = pAppSettings;
    _flexMigrationSvc = pFlexMigrationSvc;
    _cpsCollectionMetadataBackupSvc = cpsCollectionMetadataBackupSvc;
    _cpsCollectionRestoreJobSvc = cpsCollectionRestoreJobSvc;
    _auditInfoSvc = auditInfoSvc;
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/job")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getBackupJob(
      @Context final Group pGroup, @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId)
      throws Exception {
    final BackupJobView backupJobView = getBackupJobView(pGroup.getId(), pClusterUniqueId);
    return Response.ok(backupJobView).build();
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/ssdV2ExtendedStorage")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getExtendedStorage(
      @Context final Group pGroup, @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId) {
    final ClusterDescription clusterDescription =
        _ndsClusterSvc.getActiveClusterDescription(null, pClusterUniqueId).orElseThrow();
    final ObjectId groupId = clusterDescription.getGroupId();
    final List<RegionName> availableSsdV2Regions =
        new ArrayList<>(_ndsClusterSvc.getAzureSsdV2Regions(groupId));
    final AzureExtendedStorageView azureExtendedStorageView =
        new AzureExtendedStorageView(clusterDescription, availableSsdV2Regions);
    return Response.ok(azureExtendedStorageView).build();
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/canOptInForAwsCrossProjectRestore")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getCanOptInForAwsCrossProjectRestore(
      @Context final Group pGroup, @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId)
      throws Exception {
    final ClusterDescription clusterDescription =
        _ndsClusterSvc
            .getActiveClusterDescription(null, pClusterUniqueId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));
    final AwsCrossProjectRestoreView awsCrossProjectRestoreView =
        new AwsCrossProjectRestoreView(_cpsSvc.needMigrateToNewCMK(clusterDescription));
    return Response.ok(awsCrossProjectRestoreView).build();
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/snapshots")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getSnapshots(
      @Context final Group pGroup, @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId) {
    final List<BackupSnapshot> snapshots =
        _cpsSvc.getBackupSnapshots(pGroup.getId(), pClusterUniqueId);

    final List<BackupSnapshotView> views =
        snapshots.stream().map(_cpsSvc::getBackupSnapshotView).collect(Collectors.toList());
    return Response.ok(views).build();
  }

  @POST
  @Path("/{groupId}/{clusterName}/rollingNodeReplacement")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response createRollingReplacementJobs(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    try {
      _cpsSvc.createRollingReplacementJobs(pGroup.getId(), pUser.getPrimaryEmail(), pClusterName);
      _cpsSvc.createAuditForStartReplacementJob(pGroup.getId(), pClusterName);
    } catch (SvcException svcException) {
      throw new SvcException(NDSErrorCode.CANNOT_CREATE_ROLLING_REPLACEMENT_JOB);
    }
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/{groupId}/snapshot/{snapshotId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getSnapshot(
      @Context final Group pGroup, @PathParam("snapshotId") final ObjectId pSnapshotId)
      throws Exception {
    final BackupSnapshot snapshot = _cpsSvc.getBackupSnapshot(pGroup.getId(), pSnapshotId);
    if (snapshot == null) {
      throw new SvcException(NDSErrorCode.SNAPSHOT_NOT_FOUND);
    }
    return Response.ok(_cpsSvc.getBackupSnapshotView(snapshot)).build();
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/baseSnapshotForPit")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getBaseSnapshotForPIT(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @QueryParam("pitUTCSeconds") final Integer pPitUTCSeconds,
      @QueryParam("pitOplogTs") final Integer pPitOplogTs,
      @QueryParam("pitOplogInc") final Integer pPitOplogInc)
      throws Exception {
    final BSONTimestamp timestamp =
        CpsSvc.getPitTimestamp(pPitUTCSeconds, pPitOplogTs, pPitOplogInc);

    final BackupSnapshot snapshot =
        _cpsSvc.getBaseSnapshotForPit(pGroupId, pClusterUniqueId, timestamp);

    return Response.ok(_cpsSvc.getBackupSnapshotView(snapshot)).build();
  }

  @DELETE
  @Path("/{groupId}/snapshot/{snapshotId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response deleteSnapshot(
      @Context final Group pGroup,
      @Context final AppUser pUser,
      @Context final AuditInfo pAuditInfo,
      @PathParam("snapshotId") final ObjectId pSnapshotId)
      throws Exception {
    final BackupSnapshot snapshot = _cpsSvc.getBackupSnapshot(pGroup.getId(), pSnapshotId);
    if (snapshot == null) {
      throw new SvcException(NDSErrorCode.SNAPSHOT_NOT_FOUND);
    }
    _cpsSvc.deleteBackupSnapshotAndItsCopies(snapshot, false, pAuditInfo);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/{groupId}/{clusterUniqueId}/snapshotsAffectedByPolicy")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getSnapshotsAffectedByPolicyChange(
      @Context final Group pGroup,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      final BackupScheduleView pBackupScheduleView)
      throws Exception {
    final BackupJob existingBackupJob = _cpsSvc.getBackupJob(pGroup.getId(), pClusterUniqueId);
    final List<Policy> existingPolicyList = existingBackupJob.getPolicies();
    final List<Policy> newPolicyList = pBackupScheduleView.getPolicyObjects();
    final List<BackupSnapshotRetentionView> snapshotsAffectedByPolicyChange =
        _cpsPolicySvc.getSnapshotsAffectedByPolicyChange(
            pGroup.getId(), pClusterUniqueId, existingPolicyList, newPolicyList);
    return Response.ok(snapshotsAffectedByPolicyChange).build();
  }

  @PATCH
  @Path("/{groupId}/snapshot/{snapshotId}/editRetention")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response modifySnapshotRetention(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("snapshotId") final ObjectId pSnapshotId,
      @FormParam("retentionValue") final int pRetentionValue,
      @FormParam("retentionUnit") final String pRetentionUnit)
      throws Exception {
    final BackupSnapshot snapshot = _cpsSvc.getBackupSnapshot(pGroup.getId(), pSnapshotId);
    if (snapshot == null) {
      throw new SvcException(NDSErrorCode.SNAPSHOT_NOT_FOUND);
    }
    final BackupRetentionUnit backupRetentionUnit = BackupRetentionUnit.fromValue(pRetentionUnit);
    _cpsSvc.overrideRetention(pGroup, snapshot, pRetentionValue, backupRetentionUnit, pAuditInfo);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/restoreJobs")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getRestoreJobs(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId) {
    final List<TopologyHandler> restoreJobs =
        _cpsSvc.getTopLevelRestoreJobs(
            pGroup.getId(), pClusterUniqueId, false, ReadPreference.primary());

    final boolean isGroupAtlasAdmin = _authzSvc.isProjectBackupManager(pUser, pGroup);
    final List<BackupRestoreJobView> views =
        restoreJobs.stream()
            .map(job -> job.toUIView(isGroupAtlasAdmin))
            .collect(Collectors.toList());

    return Response.ok(views).build();
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/manualDownloads")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getManualDownloads(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId) {
    final List<TopologyHandler> restoreJobs =
        _cpsSvc.getTopLevelRestoreJobs(
            pGroup.getId(), pClusterUniqueId, false, ReadPreference.primary());

    final boolean isGroupAtlasAdmin = _authzSvc.isProjectBackupManager(pUser, pGroup);
    final List<BackupRestoreJobView> views =
        restoreJobs.stream().map(job -> job.toUIView(isGroupAtlasAdmin)).toList();

    final List<BackupRestoreJobView> filteredViews =
        views.stream()
            .filter(view -> view.getDeliveryType().equals("MANUAL_DOWNLOAD"))
            .collect(Collectors.toList());

    return Response.ok(filteredViews).build();
  }

  @POST
  @Path("/groups/{groupId}/clusters/{clusterName}/backup/exports/")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response createVmExportRestoreJob(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final Organization pOrg,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("snapshotId") final ObjectId pSnapshotId,
      @FormParam("exportBucketId") final ObjectId pExportBucketId)
      throws SvcException {
    // TODO: Passing customData from UI request: https://jira.mongodb.org/browse/CLOUDP-85777
    final ObjectId exportRestoreJobId =
        _cpsExportSvc.createExportRestoreJob(
            pOrg, pGroup, pUser, pClusterName, pSnapshotId, pExportBucketId, null);
    final TopologyHandler job = _cpsSvc.getExportRestoreJob(pGroup.getId(), exportRestoreJobId);
    final boolean isGroupAtlasAdmin = _authzSvc.isProjectBackupManager(pUser, pGroup);
    return Response.ok(job.toUIView(isGroupAtlasAdmin)).build();
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/exportRestoreJobs")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getVmExportRestoreJobs(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId) {
    final List<TopologyHandler> restoreJobs =
        _cpsSvc.getTopLevelRestoreJobs(
            pGroup.getId(), pClusterUniqueId, true, ReadPreference.primary());

    final boolean isGroupAtlasAdmin = _authzSvc.isProjectBackupManager(pUser, pGroup);
    final List<BackupRestoreJobView> views =
        restoreJobs.stream()
            .map(job -> job.toUIView(isGroupAtlasAdmin))
            .collect(Collectors.toList());

    return Response.ok(views).build();
  }

  @GET
  @Path("/{groupId}/restoreJob/{restoreJobId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getRestoreJob(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("restoreJobId") final ObjectId pRestoreJobId)
      throws SvcException {
    final TopologyHandler restoreJob =
        _cpsSvc.getNonExportBackupRestoreJob(pGroup.getId(), pRestoreJobId);
    if (restoreJob == null) {
      throw new SvcException(CommonErrorCode.NOT_FOUND);
    }
    final boolean isGroupAtlasAdmin = _authzSvc.isProjectBackupManager(pUser, pGroup);
    return Response.ok(restoreJob.toUIView(isGroupAtlasAdmin)).build();
  }

  @GET
  @Path("/{groupId}/collectionRestoreJob/{jobId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getCollectionRestoreJob(@PathParam("jobId") final ObjectId pJobId)
      throws SvcException {
    final CollectionRestoreJob job = _cpsCollectionRestoreJobSvc.getCollectionRestoreJob(pJobId);
    final List<CollectionRestoreState> states =
        _cpsCollectionRestoreJobSvc.findRestoreStatesByJobId(pJobId);
    final CollectionRestoreJobView view = new CollectionRestoreJobView(job, states);
    return Response.ok(view).build();
  }

  @DELETE
  @Path("/{groupId}/restoreJob/{restoreJobId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response cancelRestoreJob(
      @Context final Group group, @PathParam("restoreJobId") final ObjectId restoreJobId)
      throws SvcException {
    final TopologyHandler restoreJob =
        _cpsSvc.getNonExportBackupRestoreJob(group.getId(), restoreJobId);
    if (restoreJob == null) {
      throw new SvcException(CommonErrorCode.NOT_FOUND);
    }
    _cpsSvc.cancelBackupRestoreJob(restoreJob, CanceledReason.FROM_UI);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/pitRestoreRange")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getPitRestoreRange(
      @Context final Group group, @PathParam("clusterUniqueId") final ObjectId clusterUniqueId)
      throws Exception {
    final long start = System.currentTimeMillis();
    LOG.info(
        "timing_step=start timing_component=pitRestoreRange timing_method=getPitRestoreRange ({},"
            + " {})",
        group.getId(),
        clusterUniqueId);

    final BackupJob backupJob;

    final ClusterDescription clusterDescription =
        _ndsClusterSvc
            .getActiveClusterDescription(group.getId(), clusterUniqueId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    if (clusterDescription.isServerlessTenantCluster()) {
      ServerlessMTMCluster mtmCluster =
          _serverlessBackupSvc.getMtmClusterByTenantId(group.getId(), clusterUniqueId);
      backupJob = _cpsSvc.getActiveBackupJob(mtmCluster.getGroupId(), mtmCluster.getName());
    } else {
      backupJob = _cpsSvc.getBackupJob(group.getId(), clusterUniqueId);
    }

    LOG.info(
        "timing_step=1 timing_component=pitRestoreRange timing_method=getPitRestoreRange ({},"
            + " {}), timing_method_ms={}",
        group.getId(),
        clusterUniqueId,
        System.currentTimeMillis() - start);

    final Pair<BSONTimestamp, BSONTimestamp> tsRange =
        _cpsSvc.findPitRestoreableRange(new Date(), backupJob);
    LOG.info(
        "timing_step=2 timing_component=pitRestoreRange timing_method=getPitRestoreRange ({},"
            + " {}), timing_method_ms={}",
        group.getId(),
        clusterUniqueId,
        System.currentTimeMillis() - start);

    final List<Pair<BSONTimestamp, BSONTimestamp>> validOplogRanges =
        _cpsSvc.getValidOplogRanges(backupJob);

    LOG.info(
        "timing_step=3 timing_component=pitRestoreRange timing_method=getPitRestoreRange ({},"
            + " {}), timing_method_ms={}",
        group.getId(),
        clusterUniqueId,
        System.currentTimeMillis() - start);

    final List<Pair<BSONTimestamp, BSONTimestamp>> validPitRanges =
        _cpsSvc.getPitRestoreableOplogRanges(backupJob, validOplogRanges);

    LOG.info(
        "timing_step=4 timing_component=pitRestoreRange timing_method=getPitRestoreableOplogRanges"
            + " ({}, {}), timing_method_ms={}",
        group.getId(),
        clusterUniqueId,
        System.currentTimeMillis() - start);

    final CpsPitRestoreRangeView cpsPitRestoreRangeView;
    if (tsRange == null) {
      cpsPitRestoreRangeView =
          new CpsPitRestoreRangeView(null, null, validOplogRanges, validPitRanges);
    } else {
      cpsPitRestoreRangeView =
          new CpsPitRestoreRangeView(
              tsRange.getLeft(), tsRange.getRight(), validOplogRanges, validPitRanges);
    }

    // Logging this to keep tabs on how often we see it from the UI. This is a probably confusing
    // situation for customers since a few things might cause gaps in the oplog.
    if (validPitRanges.size() > 1) {
      LOG.warn(
          "Multiple restoreable PIT ranges returned from GET /pitRestoreRange: groupId={},"
              + " clusterUniqueId={}",
          group.getId(),
          clusterUniqueId);
    }

    LOG.info(
        "timing_step=end timing_component=pitRestoreRange timing_method=getPitRestoreRange ({},"
            + " {}), timing_method_ms={}",
        group.getId(),
        clusterUniqueId,
        System.currentTimeMillis() - start);
    return Response.ok(cpsPitRestoreRangeView).build();
  }

  @POST
  @Path("/{groupId}/restoreJob")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response createRestoreJob(
      @Context final AppUser user,
      @Context final Organization org,
      @Context final Group sourceGroup,
      @FormParam("deliveryMethod") final String deliveryMethodStr,
      @FormParam("pointInTimeUTCSeconds") final Integer pointInTimeUTCSeconds,
      @FormParam("oplogTs") final Integer oplogTs,
      @FormParam("oplogInc") final Integer oplogInc,
      @FormParam("snapshotId") final ObjectId snapshotId,
      @FormParam("sourceClusterName") final String sourceClusterName,
      @FormParam("targetGroupId") final ObjectId targetGroupId,
      @FormParam("targetClusterName") final String targetClusterName)
      throws Exception {
    final UserRequestType deliveryType = UserRequestType.fromName(deliveryMethodStr);

    if (deliveryType == null) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "delivery method: " + deliveryMethodStr);
    }

    _cpsSvc.validateGroupRegionRestriction(sourceGroup.getId(), targetGroupId);

    final Optional<Group> targetGroup = Optional.ofNullable(getGroupSvc().findById(targetGroupId));

    final ObjectId restoreJobId;
    try {
      restoreJobId =
          _cpsSvc.createRestoreJob(
              user,
              org,
              sourceGroup,
              deliveryType,
              pointInTimeUTCSeconds,
              oplogTs,
              oplogInc,
              snapshotId,
              sourceClusterName,
              targetClusterName,
              targetGroup.orElse(null));
    } catch (final SvcException pE) {
      LOG.warn(
          "Failed to create a restore job: requestType={}, snapshotId={}, sourceGroup={}, "
              + "sourceClusterName={}, targetGroup={}, targetClusterName={}",
          deliveryType.getRequestName(),
          snapshotId,
          sourceGroup.getId(),
          sourceClusterName,
          targetGroup.map(g -> g.getId().toString()).orElse(""),
          targetClusterName,
          pE);

      if (pE.getErrorCode() == NDSErrorCode.CANNOT_PERFORM_RESTORE_ON_SERVERLESS_INSTANCE) {
        throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
      }

      throw pE;
    }

    return Response.ok(
            Collections.singletonMap("restoreJobIds", Collections.singletonList(restoreJobId)))
        .build();
  }

  @POST
  @Path("/{groupId}/{clusterUniqueId}/collectionRestoreJob")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response createCollectionRestoreJob(
      @Context final AppUser appUser,
      @Context final Organization org,
      @Context final AuditInfo auditInfo,
      @PathParam("groupId") final ObjectId sourceGroupId,
      @PathParam("clusterUniqueId") final ObjectId sourceClusterUniqueId,
      final CollectionRestoreRequestView collectionRestoreRequestView)
      throws Exception {
    final BSONTimestamp pitTime;
    if (collectionRestoreRequestView.getPointInTimeUTCSeconds() != null
        || collectionRestoreRequestView.getOplogTs() != null) {
      pitTime =
          CpsSvc.getPitTimestamp(
              collectionRestoreRequestView.getPointInTimeUTCSeconds(),
              collectionRestoreRequestView.getOplogTs(),
              collectionRestoreRequestView.getOplogInc());
    } else {
      pitTime = null;
    }

    final ObjectId collectionRestoreJobId =
        _cpsCollectionRestoreJobSvc.validateAndCreateCollectionRestoreJob(
            org,
            appUser,
            pitTime,
            collectionRestoreRequestView.toCollectionRestoreRequest(),
            auditInfo,
            sourceGroupId,
            sourceClusterUniqueId,
            collectionRestoreRequestView.getSnapshotId());

    return Response.ok(
            Collections.singletonMap(
                "collectionRestoreJobId", Collections.singletonList(collectionRestoreJobId)))
        .build();
  }

  @POST
  @Path("/{groupId}/{clusterUniqueId}/collectionRestoreJob/validation")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response validateCollectionRestoreJob(
      @PathParam("groupId") final ObjectId sourceGroupId,
      @PathParam("clusterUniqueId") final ObjectId sourceClusterUniqueId,
      final CollectionRestoreRequestView collectionRestoreRequestView)
      throws Exception {

    final BSONTimestamp pitTime;
    if (collectionRestoreRequestView.getPointInTimeUTCSeconds() != null
        || collectionRestoreRequestView.getOplogTs() != null) {
      pitTime =
          CpsSvc.getPitTimestamp(
              collectionRestoreRequestView.getPointInTimeUTCSeconds(),
              collectionRestoreRequestView.getOplogTs(),
              collectionRestoreRequestView.getOplogInc());
    } else {
      pitTime = null;
    }

    _cpsCollectionRestoreJobSvc.validateCollectionRestoreAtRequestTime(
        _cpsSvc.getAndValidateBackupSnapshot(
            sourceGroupId,
            sourceClusterUniqueId,
            collectionRestoreRequestView.getSnapshotId(),
            pitTime),
        sourceGroupId,
        pitTime,
        collectionRestoreRequestView.toCollectionRestoreRequest());

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/{groupId}/{clusterUniqueId}/backupPolicy")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response updateBackupPolicy(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      final BackupScheduleView pBackupScheduleView,
      @Context final AuditInfo pAuditInfo)
      throws Exception {

    final AutoExportSettings autoExportSettings =
        pBackupScheduleView.getAutoExportPolicy() != null
            ? pBackupScheduleView.getAutoExportPolicy().toAutoExportSettings()
            : null;

    if (BooleanUtils.isTrue(pBackupScheduleView.getAutoExportEnabled())) {
      validateAutoExportPolicyView(pBackupScheduleView.getAutoExportPolicy(), pGroupId);
    }

    final List<CopySetting> copySettingList =
        Optional.ofNullable(pBackupScheduleView.getCopySettingViews())
            .map(cs -> cs.stream().map(CopySettingView::toCopySetting).collect(Collectors.toList()))
            .orElse(null);

    final List<ExtraRetentionSetting> extraRetentionSettings =
        Optional.ofNullable(pBackupScheduleView.getExtraRetentionSettingViews())
            .map(
                cs ->
                    cs.stream()
                        .map(ExtraRetentionSettingView::toExtraRetentionSetting)
                        .collect(Collectors.toList()))
            .orElse(null);

    _cpsPolicySvc.updateBackupPolicy(
        pGroupId,
        pClusterUniqueId,
        pBackupScheduleView.getReferenceTimeInMins(),
        pBackupScheduleView.getRestoreWindowDays() * 1.0,
        pAuditInfo,
        pBackupScheduleView.getPolicyObjects(),
        new Date(),
        pBackupScheduleView.updateSnapshots(),
        pBackupScheduleView.getAutoExportEnabled(),
        null,
        autoExportSettings,
        copySettingList,
        pBackupScheduleView.getDeleteCopiedBackupsViews(),
        extraRetentionSettings);
    final BackupJobView backupJobView = getBackupJobView(pGroupId, pClusterUniqueId);
    return Response.ok(backupJobView).build();
  }

  // Auth for this endpoint is handled in the service methods since the restore server doesn't use
  // agent api keys
  @POST
  @Path("/restoreJob/{jobId}/status/{authKey}")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  public Response updateRestoreStatus(
      @Context final HttpServletRequest pRequest,
      @PathParam("jobId") final ObjectId pJobId,
      @PathParam("authKey") final String pAuthKey,
      final CpsRestoreMetadata.Status pStatus)
      throws SvcException {
    _cpsSvc.updateRestoreStatus(pJobId, pStatus, pAuthKey, pRequest.getRemoteAddr());
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  // Auth for this endpoint is handled in the service methods since the restore server doesn't use
  // agent api keys
  @POST
  @Path("/restoreJob/{jobId}/exportStatus/{authKey}")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  public Response updateExportStatus(
      @Context final HttpServletRequest pRequest,
      @PathParam("jobId") final ObjectId pJobId,
      @PathParam("authKey") final String pAuthKey,
      final ExportStatus pExportStatus)
      throws SvcException {
    _cpsExportSvc.updateExportStatus(pJobId, pExportStatus, pAuthKey, pRequest.getRemoteAddr());
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/{groupId}/restoreJob/seen")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response markRestoreJobsAsSeen(
      @FormParam("restoreJobIds[]") final List<ObjectId> pRestoreJobIds) {
    _ndsUiSvc.updateRestoreJobsSeen(pRestoreJobIds);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  private GroupSvc getGroupSvc() {
    return _groupSvc;
  }

  @POST
  @Path("/{groupId}/snapshots")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response takeSnapshot(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @FormParam("clusterName") final String pClusterName,
      @FormParam("retentionInDays") final int pRetentionInDays,
      @FormParam("snapshotDescription") final String pSnapshotDescription,
      @Context final AuditInfo pAuditInfo)
      throws Exception {
    final Duration pRetention = Duration.ofDays(pRetentionInDays);

    _cpsSvc.validateOnDemandSnapshotRetention(pRetention, pGroup.getId());
    _cpsSvc.createQueuedOnDemandSnapshot(
        pGroup.getId(), pClusterName, pRetention, pSnapshotDescription, pUser, pAuditInfo);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  /**
   * This is called by the atlas restore VM for Wired Tiger Checkpoint (4.2+).
   *
   * <p>IMPORTANT: each file returned here is a relative path to the dbPath
   *
   * <p>eg) if absolute path is /srv/mongodb/gg-shard-0-node-0/journal/WiredTigerLog.0000000001,
   * then the path in the response is journal/WiredTigerLog.0000000001
   *
   * @param verificationKey - the verification key of the restoreJob
   * @param restoreJobId - the restore job id of the WTC snapshot
   */
  @GET
  @Path("/files/{verificationKey}/{restoreJobId}")
  @Produces({MediaType.APPLICATION_JSON})
  public Response getWtcFileList(
      @Context final HttpServletRequest request,
      @Context final HttpServletResponse response,
      @PathParam("verificationKey") final String verificationKey,
      @PathParam("restoreJobId") final ObjectId restoreJobId)
      throws SvcException {
    final List<String> fileList = _cpsSvc.getBackupCursorFileList(restoreJobId, verificationKey);
    final BackupCursorFileListResponseView.Builder viewBuilder =
        new BackupCursorFileListResponseView.Builder();
    viewBuilder.files(fileList);
    return Response.ok(viewBuilder.build()).build();
  }

  /**
   * callback for the restore server to indicate that it is running and the restore can proceed to
   * the next step
   *
   * @param verificationKey - the verification key of the restoreJob
   * @param restoreJobId - the restore job id that the restore server belongs to
   */
  @GET
  @Path("/serverRunning/{verificationKey}/{restoreJobId}")
  public Response restoreServerRunning(
      @PathParam("verificationKey") final String verificationKey,
      @PathParam("restoreJobId") final ObjectId restoreJobId)
      throws Exception {
    _cpsSvc.setRestoreJobRunning(restoreJobId, verificationKey);
    return Response.ok().build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/ipAllowedAccessList/{verificationKey}/{restoreJobId}")
  public Response ipAllowedAccessList(
      @PathParam("verificationKey") final String verificationKey,
      @PathParam("restoreJobId") final ObjectId restoreJobId)
      throws Exception {
    ReplicaSetBackupRestoreJob restoreJob =
        _cpsSvc.getReplicaSetBackupRestoreJob(restoreJobId, verificationKey);

    List<String> allowedIps = new ArrayList<>();
    switch (restoreJob.getMetadata().getDeliveryType()) {
      case MANUAL_DOWNLOAD:
        NDSGroup group = _ndsGroupSvc.find(restoreJob.getProjectId()).orElseThrow();
        NDSNetworkPermissionList permissionList = group.getNetworkPermissionList();
        allowedIps =
            permissionList.getNetworkPermissions().stream()
                .filter(entry -> entry.getType() == NDSNetworkPermission.Type.IP)
                .map(entry -> getCidrNotationIp(entry.getValue(), restoreJobId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        break;
      case POINT_IN_TIME:
      case AUTOMATION_PULL:
        ObjectId targetGroupId;
        String targetClusterName;
        switch (restoreJob.getStrategyType()) {
          case SERVERLESS_DEDICATED:
            final RestoreTargetCluster targetCluster = restoreJob.getMetadata().getTarget();
            targetGroupId = targetCluster.getTargetProjectId();
            targetClusterName = targetCluster.getTargetClusterName();
            break;
          case SERVERLESS_STREAMING:
          case TENANT_UPGRADE_TO_SERVERLESS:
            final ClusterDescription targetClusterDescription;
            try {
              targetClusterDescription =
                  _serverlessBackupSvc.getServerlessClusterDescription(
                      restoreJob.getTarget().getTargetProjectId(),
                      restoreJob.getTarget().getTargetClusterName());
            } catch (final SvcException e) {
              throw new SvcException(NDSErrorCode.NOT_SERVERLESS_TENANT_CLUSTER);
            }

            final ServerlessMTMCluster targetMtmCluster =
                _serverlessBackupSvc.getMtmClusterByTenantId(
                    targetClusterDescription.getGroupId(), targetClusterDescription.getUniqueId());
            targetGroupId = targetMtmCluster.getGroupId();
            targetClusterName = targetMtmCluster.getName();
            break;
          default:
            throw new SvcException(NDSErrorCode.NOT_SERVERLESS_RESTORE_JOB);
        }

        List<ReplicaSetHardware> replicaSets =
            _cpsSvc.findReplicaSetHardwareByCluster(targetGroupId, targetClusterName);
        allowedIps =
            replicaSets.stream()
                .map(ReplicaSetHardware::getProvisionedPublicIPs)
                .flatMap(List::stream)
                .map(entry -> getCidrNotationIp(entry, restoreJobId))
                .collect(Collectors.toList());
        allowedIps.addAll(Arrays.asList(NetUtils.PRIVATE_RANGES));
        break;
    }

    final IpAllowedAccessListResponseView.Builder viewBuilder =
        new IpAllowedAccessListResponseView.Builder();

    viewBuilder.ips(allowedIps);
    return Response.ok(viewBuilder.build()).build();
  }

  private static String getCidrNotationIp(final String pValue, final ObjectId pRestoreJobId) {
    try {
      return NDSNetworkPermission.toCidrNotation(pValue);
    } catch (SvcException e) {
      LOG.warn("Invalid IP address format {} for restoreJob {}", pValue, pRestoreJobId);
      return null;
    }
  }

  @GET
  @Path("/tenant/{groupId}/{clusterUniqueId}/snapshots")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getTenantBackupSnapshots(
      @Context final Group pGroup, @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId)
      throws SvcException {
    return Response.ok(_ndsUiSvc.getTenantBackupSnapshots(pGroup.getId(), pClusterUniqueId))
        .build();
  }

  @GET
  @Path("/tenant/{groupId}/{clusterUniqueId}/restores")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getTenantRestores(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @Context final AuditInfo pAuditInfo)
      throws SvcException {
    return Response.ok(_ndsUiSvc.getTenantBackupRestores(pGroup.getId(), pClusterUniqueId)).build();
  }

  @GET
  @Path("/tenant/{groupId}/restore/{restoreId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getTenantRestore(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("restoreId") final ObjectId pRestoreId,
      @Context final AuditInfo pAuditInfo)
      throws SvcException {
    return Response.ok(_ndsUiSvc.getTenantRestore(pGroup.getId(), pRestoreId)).build();
  }

  @POST
  @Path("/tenant/{groupId}/restore")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response createTenantRestore(
      @Context final AppUser pUser,
      @Context final Organization pOrganization,
      @Context final Group pSourceGroup,
      @FormParam("snapshotId") final ObjectId pSnapshotId,
      @FormParam("deliveryMethod") final TenantRestore.DeliveryType pDeliveryType,
      @FormParam("sourceClusterName") final String pSourceClusterName,
      @FormParam("targetGroupId") final ObjectId pTargetGroupId,
      @FormParam("targetClusterName") final String pTargetClusterName)
      throws Exception {
    final ObjectId restoreId;
    if (pDeliveryType == TenantRestore.DeliveryType.DOWNLOAD) {
      LOG.debug("Requested a manual download of tenant snapshot {}.", pSnapshotId);
      restoreId =
          _ndsTenantBackupSvc
              .createDownloadRestore(
                  pSourceGroup,
                  pUser,
                  pSnapshotId,
                  _auditInfoSvc.fromUiCall(pUser, pUser.getLastAuthAddr()))
              .getId();
    } else {
      LOG.debug(
          "Requested an automated restore of tenant snapshot {} from {} to {}.",
          pSnapshotId,
          pSourceClusterName,
          pTargetClusterName);

      final Group targetGroup = getGroupSvc().findById(pTargetGroupId);
      if (targetGroup.useCNRegionsOnly() != pSourceGroup.useCNRegionsOnly()) {
        throw new SvcException(NDSErrorCode.BACKUP_RESTORE_TO_AWS_CN_ONLY_GROUP_INVALID);
      }

      if (targetGroup.isAtlas()) {
        try {
          _flexMigrationSvc.verifyFlexMigrationOrRollbackNotInProgress(
              pTargetClusterName, pTargetGroupId, "createSharedClusterBackupRestoreJob", false);
          restoreId =
              _ndsTenantBackupSvc
                  .validateAndRestoreTenantSnapshot(
                      pSourceGroup,
                      pSourceClusterName,
                      targetGroup,
                      pTargetClusterName,
                      pSnapshotId,
                      pUser,
                      _auditInfoSvc.fromUiCall(pUser, pUser.getLastAuthAddr()))
                  .getId();
        } catch (final SvcException pE) {
          if (pE.getErrorCode()
              == NDSErrorCode.CANNOT_TAKE_TENANT_SNAPSHOT_OF_SERVERLESS_INSTANCE) {
            throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
          }
          throw pE;
        }

      } else {
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "group id: " + pTargetGroupId);
      }
    }

    return Response.ok(
            Collections.singletonMap("restoreJobIds", Collections.singletonList(restoreId)))
        .build();
  }

  @GET
  @Path("/tenant/{groupId}/{clusterUniqueId}/backup")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getTenantBackupMetadata(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @Context final AuditInfo pAuditInfo)
      throws SvcException {
    return Response.ok(_ndsUiSvc.getTenantBackup(pGroup.getId(), pClusterUniqueId)).build();
  }

  @GET
  @Path("/serverless/{groupId}/{clusterUniqueId}/job")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getServerlessBackupJob(
      @Context final Group pGroup,
      @Context final Organization pOrganization,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId)
      throws Exception {
    _serverlessBackupSvc.getServerlessClusterDescription(pGroup.getId(), pClusterUniqueId);
    final Optional<BackupSnapshot> snapshot =
        _serverlessBackupSvc.getLatestSnapshotByMtmTenantId(pClusterUniqueId);

    final ServerlessMTMCluster mtmCluster =
        _serverlessBackupSvc.getMtmClusterByTenantId(pGroup.getId(), pClusterUniqueId);
    final ClusterDescription mtmClusterDescription =
        _ndsClusterSvc
            .getActiveClusterDescription(mtmCluster.getGroupId(), mtmCluster.getName())
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    final BackupJobView backupJobView =
        getBackupJobView(
            mtmClusterDescription.getGroupId(),
            mtmClusterDescription.getUniqueId(),
            snapshot,
            true);
    return Response.ok(backupJobView).build();
  }

  @GET
  @Path("/serverless/{groupId}/{clusterUniqueId}/snapshots")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getServerlessBackupSnapshots(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @Context final AuditInfo pAuditInfo)
      throws SvcException {

    final ClusterDescription clusterDescription =
        _serverlessBackupSvc.getServerlessClusterDescription(pGroup.getId(), pClusterUniqueId);

    final List<BackupSnapshot> snapshots =
        _serverlessBackupSvc.getSnapshotsByServerlessMtmTenant(clusterDescription, pUser);

    final List<BackupSnapshotView> views =
        snapshots.stream()
            .map(
                snapshot ->
                    _cpsSvc.fromClusterDescriptionAndBackupSnapshot(clusterDescription, snapshot))
            .collect(Collectors.toList());
    return Response.ok(views).build();
  }

  @GET
  @Path("/serverless/{groupId}/{clusterUniqueId}/restores")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getServerlessRestoreJobs(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final Organization pOrg,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId)
      throws SvcException {
    final ClusterDescription clusterDescription =
        _serverlessBackupSvc.getServerlessClusterDescription(pGroup.getId(), pClusterUniqueId);
    return getRestoreJobs(pUser, pGroup, clusterDescription.getUniqueId());
  }

  @GET
  @Path("/serverless/{groupId}/restore/{restoreJobId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getServerlessRestoreJob(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final Organization pOrg,
      @PathParam("restoreJobId") final ObjectId pRestoreJobId)
      throws SvcException {
    return getRestoreJob(pUser, pGroup, pRestoreJobId);
  }

  @POST
  @Path("/serverless/{groupId}/restore")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response createServerlessRestoreJob(
      @Context final AppUser user,
      @Context final Organization org,
      @Context final Group sourceGroup,
      @FormParam("deliveryMethod") final String deliveryMethodStr,
      @FormParam("pointInTimeUTCSeconds") final Integer pointInTimeUTCSeconds,
      @FormParam("oplogTs") final Integer oplogTs,
      @FormParam("oplogInc") final Integer oplogInc,
      @FormParam("snapshotId") final ObjectId snapshotId,
      @FormParam("sourceClusterName") final String sourceClusterName,
      @FormParam("targetGroupId") final ObjectId targetGroupId,
      @FormParam("targetClusterName") final String targetClusterName)
      throws Exception {
    _flexMigrationSvc.verifyFlexMigrationOrRollbackNotInProgress(
        sourceClusterName, sourceGroup.getId(), "createServerlessClusterBackupRestoreJob", false);
    _flexMigrationSvc.verifyFlexMigrationOrRollbackNotInProgress(
        targetClusterName, targetGroupId, "createServerlessClusterBackupRestoreJob", false);
    return createRestoreJob(
        user,
        org,
        sourceGroup,
        deliveryMethodStr,
        pointInTimeUTCSeconds,
        oplogTs,
        oplogInc,
        snapshotId,
        sourceClusterName,
        targetGroupId,
        targetClusterName);
  }

  @DELETE
  @Path("/serverless/{groupId}/restore/{restoreJobId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response cancelServerlessRestoreJob(
      @Context final Group group, @PathParam("restoreJobId") final ObjectId restoreJobId)
      throws SvcException {
    return cancelRestoreJob(group, restoreJobId);
  }

  @GET
  @Path("/serverless/{groupId}/{clusterUniqueId}/pitRestoreRange")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getServerlessPitRestoreRange(
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId groupId,
      @PathParam("clusterUniqueId") final ObjectId clusterUniqueId)
      throws Exception {

    List<Pair<BSONTimestamp, BSONTimestamp>> validOplogRanges = new ArrayList<>();
    List<Pair<BSONTimestamp, BSONTimestamp>> validPitRanges = new ArrayList<>();

    final ClusterDescription clusterDescription =
        _ndsClusterSvc
            .getActiveClusterDescription(groupId, clusterUniqueId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));

    final List<BackupSnapshot> snapshots =
        _serverlessBackupSvc.getSnapshotsByServerlessMtmTenant(clusterDescription, pUser);
    final ServerlessMTMCluster mtmCluster =
        _serverlessBackupSvc.getMtmClusterByTenantId(groupId, clusterUniqueId);
    final long restoreWindowInMins =
        _cpsSvc
            .getBackupJob(mtmCluster.getGroupId(), mtmCluster.getMtmClusterDescriptionUniqueId())
            .getRestoreWindowInMins();

    if (!snapshots.isEmpty()) {
      long currentTimestampMillis = System.currentTimeMillis();
      long timestampBeforeRestoreWindow =
          currentTimestampMillis - (restoreWindowInMins * 60L * 1000L);
      BSONTimestamp endTime = new BSONTimestamp((int) (currentTimestampMillis / 1000), 0);
      BSONTimestamp startTime = new BSONTimestamp((int) (timestampBeforeRestoreWindow / 1000), 0);
      BackupSnapshot snapshot = null;
      BackupSnapshot oldestSnapshot = snapshots.get(snapshots.size() - 1);
      if (oldestSnapshot.getPitSentinelOptime().compareTo(startTime) >= 0) {
        snapshot = oldestSnapshot;
      } else {
        for (BackupSnapshot backupSnapshot : snapshots) {
          if (backupSnapshot.getPitSentinelOptime().compareTo(startTime) > 0) {
            continue;
          }
          snapshot = backupSnapshot;
          break;
        }
        if (snapshot == null) {
          throw new SvcException(NDSErrorCode.UNABLE_TO_FIND_SNAPSHOT_PRIOR_TO_PIT_RESTORE_OPTIME);
        }
      }

      final ServerlessRestoreMetadata.PITRestoreSourceMTM sourceMTM =
          _cpsSvc.getPITRestoreSourceMtm(snapshot, endTime);

      if (Optional.ofNullable(sourceMTM).isEmpty()
          || !sourceMTM
              .getClusterUniqueId()
              .equals(mtmCluster.getMtmClusterDescriptionUniqueId())) {
        // should never happen, fallback to retrieve oplog ranges on current MTM
        validOplogRanges = _cpsSvc.getValidOplogRangeForMTM(mtmCluster);
        return buildGetServerlessPitRestoreRangeResponse(validOplogRanges, validPitRanges);
      }

      final Optional<BackupJob> backupJobOpt =
          _cpsSvc.getBackupJob(sourceMTM.getGroupId(), sourceMTM.getClusterName());

      if (backupJobOpt.isPresent()) {
        BackupJob backupJob = backupJobOpt.get();
        final List<Pair<BSONTimestamp, BSONTimestamp>> oplogRanges =
            _cpsSvc.getServerlessOplogRanges(
                backupJob, sourceMTM.getStartTime(), sourceMTM.getEndTime());

        if (!oplogRanges.isEmpty()) {

          int lastIdx = validOplogRanges.size() - 1;
          Pair<BSONTimestamp, BSONTimestamp> currFirst = oplogRanges.get(0);

          if (!validOplogRanges.isEmpty()
              && validOplogRanges.get(lastIdx).getRight().compareTo(currFirst.getLeft()) >= 0
              && validOplogRanges.get(lastIdx).getLeft().compareTo(currFirst.getLeft()) <= 0) {
            // If previous oplogs last range overlaps with current oplogs first range, then merge
            // them
            Pair<BSONTimestamp, BSONTimestamp> prevLast = validOplogRanges.remove(lastIdx);
            validOplogRanges.add(Pair.of(prevLast.getLeft(), currFirst.getRight()));
            validOplogRanges.addAll(oplogRanges.subList(1, oplogRanges.size()));
          } else {
            validOplogRanges.addAll(oplogRanges);
          }
        }
      }

      // construct validPitRanges from the intersection of snapshots with validOplogRanges
      for (var validRange : validOplogRanges) {
        for (int snapshotIndex = snapshots.size() - 1; snapshotIndex >= 0; snapshotIndex--) {
          BSONTimestamp pitSentinalOpTime = snapshots.get(snapshotIndex).getPitSentinelOptime();

          if (pitSentinalOpTime != null
              && pitSentinalOpTime.compareTo(validRange.getLeft()) > 0
              && pitSentinalOpTime.compareTo(validRange.getRight()) <= 0) {

            validPitRanges.add(Pair.of(pitSentinalOpTime, validRange.getRight()));
            break;
          }
        }
      }
    } else {
      // if there is no snapshot, no need to calculate valid pit ranges
      validOplogRanges = _cpsSvc.getValidOplogRangeForMTM(mtmCluster);
    }
    return buildGetServerlessPitRestoreRangeResponse(validOplogRanges, validPitRanges);
  }

  private Response buildGetServerlessPitRestoreRangeResponse(
      List<Pair<BSONTimestamp, BSONTimestamp>> validOplogRanges,
      List<Pair<BSONTimestamp, BSONTimestamp>> validPitRanges) {
    BSONTimestamp pitRestoreRangeStart = null;
    BSONTimestamp pitRestoreRangeEnd = null;
    if (!validPitRanges.isEmpty()) {
      pitRestoreRangeStart = validPitRanges.get(0).getLeft();
      pitRestoreRangeEnd = validPitRanges.get(validPitRanges.size() - 1).getRight();
    }
    CpsPitRestoreRangeView cpsPitRestoreRangeView =
        new CpsPitRestoreRangeView(
            pitRestoreRangeStart, pitRestoreRangeEnd, validOplogRanges, validPitRanges);
    return Response.ok(cpsPitRestoreRangeView).build();
  }

  @GET
  @Path("/groups/{groupId}/backup/exportBuckets/{exportBucketId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getExportBucketById(
      @Context final Group pGroup, @PathParam("exportBucketId") final ObjectId pExportBucketId)
      throws SvcException {
    final Optional<ExportBucket> exportBucketOpt = _cpsExportSvc.findExportBucket(pExportBucketId);
    if (!exportBucketOpt.isPresent()) {
      throw new SvcException(NDSErrorCode.EXPORT_BUCKET_NOT_FOUND, pExportBucketId);
    }

    final ExportBucket exportBucket = exportBucketOpt.get();
    if (!exportBucket.getProjectId().equals(pGroup.getId())) {
      throw new SvcException(NDSErrorCode.EXPORT_BUCKET_NOT_FOUND, pExportBucketId);
    }
    return Response.ok(exportBucket).build();
  }

  @GET
  @Path("/groups/{groupId}/backup/exportBuckets/")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getAllExportBucketsByGroup(@Context final Group pGroup) {
    return Response.ok(_cpsExportSvc.findAWSExportBucketsByProjectId(pGroup.getId())).build();
  }

  @POST
  @Path("/groups/{groupId}/backup/exportBuckets/")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response addExportBucket(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("iamRoleId") final ObjectId pIamRoleId,
      @FormParam("bucketName") final String pBucketName)
      throws SvcException {
    final AWSExportBucket exportBucket =
        AWSExportBucket.builder()
            .withProjectId(pGroup.getId())
            .withBucketName(pBucketName)
            .withIamRoleId(pIamRoleId)
            .withBucketType(BucketType.CUSTOMER)
            .withId(ObjectId.get())
            .build();
    _cpsExportSvc.createExportBucket(exportBucket, pAuditInfo);
    return Response.ok(exportBucket).build();
  }

  @DELETE
  @Path("/groups/{groupId}/backup/exportBuckets/{exportBucketId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_BACKUP_MANAGER)
  public Response deleteExportBucket(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @PathParam("exportBucketId") final ObjectId pExportBucketId)
      throws SvcException {
    final Optional<ExportBucket> exportBucketOpt = _cpsExportSvc.findExportBucket(pExportBucketId);
    if (!exportBucketOpt.isPresent()) {
      throw new SvcException(NDSErrorCode.EXPORT_BUCKET_NOT_FOUND, pExportBucketId);
    }

    final ExportBucket exportBucket = exportBucketOpt.get();
    if (!exportBucket.getProjectId().equals(pGroup.getId())) {
      throw new SvcException(NDSErrorCode.EXPORT_BUCKET_NOT_FOUND, pExportBucketId);
    }
    _cpsExportSvc.deleteExportBucket(exportBucket, pAuditInfo);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/{groupId}/dataProtectionClusterList")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  public Response getDataProtectionClusterList(@Context final Group pGroup) throws Exception {
    final List<DataProtectionClusterView> views;
    try {
      views = _cpsSvc.getAllDataProtectionClusterViews(pGroup);
    } catch (Exception pE) {
      throw new RuntimeException(pE);
    }

    return Response.ok(views).build();
  }

  @GET
  @Path("/{groupId}/dataProtection")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY, plan = PlanTypeSet.NDS)
  public Response getDataProtectionSettings(@Context final Group pGroup) throws Exception {
    final Optional<DataProtectionSettings> dataProtectionSettings =
        _cpsSvc.getDataProtectionSettings(pGroup.getId());

    return Response.ok(
            dataProtectionSettings.isPresent()
                ? new DataProtectionSettingsView(dataProtectionSettings.get())
                : EMPTY_JSON_OBJECT)
        .build();
  }

  @PATCH
  @Path("/{groupId}/dataProtection")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GROUP_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_ADMIN},
      plan = PlanTypeSet.NDS)
  public Response saveDataProtectionSettings(
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      final DataProtectionSettingsView pDataProtectionSettingsView)
      throws Exception {

    _cpsPolicySvc.saveDataProtectionSettings(
        pGroup.getId(),
        pDataProtectionSettingsView.toDataProtectionSettingsBuilder().build(),
        pAppUser,
        pAuditInfo);

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/{groupId}/dataProtection/validate")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {RoleSet.GROUP_ATLAS_ADMIN, RoleSet.GLOBAL_ATLAS_ADMIN},
      plan = PlanTypeSet.NDS)
  public Response validateDataProtectionSettings(
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      final DataProtectionSettingsView pDataProtectionSettingsView)
      throws SvcException {

    final DataProtectionSettings dataProtectionSettings =
        pDataProtectionSettingsView.toDataProtectionSettingsBuilder().build();

    final boolean hasInternalRolePermission =
        _authzSvc.isGlobalBackupCompliancePolicyAdmin(pAppUser) || pAppUser == AppUser.SYSTEM_USER;

    // the validation is submitted at the first page of the UI modal, where the user has not
    // entered the email, so we skip validating the email.
    _cpsPolicySvc.validateDataProtectionSettings(
        pGroup.getId(), dataProtectionSettings, hasInternalRolePermission, true);

    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  // sets the state to "DISABLED" planner picks it up and deletes the document from the DB
  @DELETE
  @Path("/{groupId}/dataProtection")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = {
        RoleSet.GLOBAL_ATLAS_ADMIN,
        RoleSet.GROUP_ATLAS_ADMIN,
        RoleSet.GLOBAL_BACKUP_COMPLIANCE_POLICY_ADMIN
      })
  public Response disableDataProtectionSettings(
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo)
      throws Exception {
    _cpsPolicySvc.disableDataProtectionSettings(pGroup, pAppUser, pAuditInfo);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  // custom endpoint that sets markedForDisablementDate to current time or null
  // within 24 hours of when markedForDisablementDate is set, the project owner has permission to
  // delete the BCP document
  @POST
  @Path("/{groupId}/dataProtection:markForDisablement")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {RoleSet.GLOBAL_ATLAS_ADMIN, RoleSet.GLOBAL_BACKUP_COMPLIANCE_POLICY_ADMIN})
  public Response markForDisablementDataProtectionSettings(
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @FormParam("markForDisablement") final Boolean markForDisablement)
      throws Exception {
    _cpsPolicySvc.markForDisablementDataProtectionSettings(
        pGroup, pAppUser, pAuditInfo, markForDisablement);
    return Response.ok(BaseResource.EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/{groupId}/snapshot/{snapshotId}/collectionMetadata")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = {RoleSet.GROUP_DATA_ACCESS_ANY})
  public Response getCustomerCollectionMetadata(
      @PathParam("snapshotId") final ObjectId snapshotId, @Context final Group group)
      throws Exception {
    final BackupSnapshot snapshot = _cpsSvc.getBackupSnapshot(group.getId(), snapshotId);

    final Optional<CustomerCollectionMetadata> customerCollectionMetadata =
        _cpsCollectionMetadataBackupSvc.getCustomerCollectionMetadata(snapshot);

    return Response.ok(
            customerCollectionMetadata.isPresent()
                ? new CustomerCollectionMetadataView(customerCollectionMetadata.get())
                : EMPTY_JSON_OBJECT)
        .build();
  }

  private BackupJobView getBackupJobView(final ObjectId pProjectId, final ObjectId pClusterUniqueId)
      throws SvcException {
    final Optional<BackupSnapshot> lastSnapshot =
        _cpsSvc.getLastSnapshot(pProjectId, pClusterUniqueId);
    return getBackupJobView(pProjectId, pClusterUniqueId, lastSnapshot, false);
  }

  private BackupJobView getBackupJobView(
      final ObjectId pProjectId,
      final ObjectId pClusterUniqueId,
      final Optional<BackupSnapshot> pSnapshot,
      final boolean pIsServerlessBackupJob)
      throws SvcException {
    final BackupJob backupJob = _cpsSvc.getBackupJob(pProjectId, pClusterUniqueId);
    final BackupJobView backupJobView =
        pIsServerlessBackupJob
            ? new ServerlessBackupJobView(backupJob)
            : new BackupJobView(backupJob);
    if (pSnapshot.isPresent()) {
      final BackupSnapshot backupSnapshot = pSnapshot.get();
      backupJobView.setLastSnapshotDate(backupSnapshot.getSnapshotInitiationDate());
    }
    if (!_cpsSvc.isBackupCompliancePolicyPostGaFeatureFlagEnabled(pProjectId)) {
      backupJobView.setExtraRetentionSettingViews(null);
    }
    return backupJobView;
  }

  private void validateAutoExportPolicyView(
      final AutoExportPolicyView pAutoExportPolicyView, final ObjectId pGroupId)
      throws SvcException {

    final Optional<Group> pGroup = Optional.ofNullable(getGroupSvc().findById(pGroupId));

    if (pAutoExportPolicyView != null) {
      if (!isFeatureFlagEnabled(
          FeatureFlag.CPS_SNAPSHOT_EXPORT_HIGH_FREQUENCY, _appSettings, null, pGroup.get())) {
        final BackupFrequencyType frequencyType =
            BackupFrequencyType.fromValue(pAutoExportPolicyView.getFrequencyType());
        // CPS_SNAPSHOT_EXPORT_HIGH_FREQUENCY - to allow for as often as 1 day scheduled exports.
        // Without the feature flag, the minimum is 1 month.
        if (!BackupFrequencyType.MONTHLY.equals(frequencyType)
            && !BackupFrequencyType.YEARLY.equals(frequencyType)) {
          throw new SvcException(
              NDSErrorCode.INTERNAL,
              "CPS_SNAPSHOT_EXPORT_HIGH_FREQUENCY flag must be turned on for high"
                  + " export frequency.");
        }
      }
    }
  }

  @GET
  @Path("/{groupId}/{clusterUniqueId}/allRestoreJobs")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getAllRestoreJobs(
      @Context final AppUser user,
      @Context final Group group,
      @PathParam("clusterUniqueId") final ObjectId clusterUniqueId)
      throws SvcException {
    final List<UnifiedRestoreJobView> unifiedViews = new ArrayList<>();

    // get regular restore jobs and add to unifiedViews list
    final List<TopologyHandler> restoreJobs =
        _cpsSvc.getTopLevelRestoreJobs(
            group.getId(), clusterUniqueId, false, ReadPreference.primary());

    final boolean isGroupAtlasAdmin = _authzSvc.isProjectBackupManager(user, group);

    restoreJobs.stream()
        .map(job -> job.toUIView(isGroupAtlasAdmin))
        .filter(view -> !UserRequestType.MANUAL_DOWNLOAD.name().equals(view.getDeliveryType()))
        .map(UnifiedRestoreJobView::new)
        .forEach(unifiedViews::add);

    // get collection restore jobs add to unifiedViews list
    final List<CollectionRestoreJob> collectionJobs =
        _cpsCollectionRestoreJobSvc.findBySourceCluster(group.getId(), clusterUniqueId);

    collectionJobs.stream()
        .map(
            job ->
                new CollectionRestoreJobView(
                    job, _cpsCollectionRestoreJobSvc.findRestoreStatesByJobId(job.getId())))
        .map(UnifiedRestoreJobView::new)
        .forEach(unifiedViews::add);

    // sort all jobs by _id
    unifiedViews.sort((a, b) -> b.getId().compareTo(a.getId()));

    return Response.ok(unifiedViews).build();
  }
}
