package com.xgen.svc.nds.gcp.planner;

import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.svc.nds.datavalidation.planner.GCPDataValidationMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPDestroyContainerMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPEnsureNetworkPermissionsAppliedMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPProvisionContainerMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPSyncClusterWithPrivateServiceConnectMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPSyncPeeringConnectionMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPSyncPrivateServiceConnectRegionGroupMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPUpdateContainerSubnetsMove;
import com.xgen.svc.nds.gcp.planner.snapshot.GCPAdminBackupSnapshotMove;
import com.xgen.svc.nds.gcp.planner.snapshot.GCPCleanUpDirectAttachRestoreMove;
import com.xgen.svc.nds.gcp.planner.snapshot.GCPDestroyRestoreMachineMove;
import com.xgen.svc.nds.gcp.planner.snapshot.GCPDestroyServerlessRestoreMachineMove;
import com.xgen.svc.nds.gcp.planner.snapshot.GCPDirectAttachRestoreMove;
import com.xgen.svc.nds.gcp.planner.snapshot.GCPOptimizedDirectAttachRestoreMove;
import com.xgen.svc.nds.gcp.planner.snapshot.GCPProvisionRestoreMachineMove;
import com.xgen.svc.nds.gcp.planner.snapshot.GCPProvisionServerlessRestoreMachineMove;
import com.xgen.svc.nds.planner.CreateResilientSnapshotMove;
import com.xgen.svc.nds.planner.MoveProvider;
import com.xgen.svc.nds.planner.snapshot.CpsReplSetSnapshotMove;
import com.xgen.svc.nds.planner.snapshot.CpsSLSRestoreMove;
import com.xgen.svc.nds.planner.snapshot.CpsSLSSnapshotMove;
import com.xgen.svc.nds.planner.snapshot.CpsShardedSnapshotMove;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

public class GCPMoveProvider extends MoveProvider {

  public GCPMoveProvider(final PlanContext pContext) {
    super(pContext);
  }

  public Move getProvisionContainerMove(
      final Map<String, String> pTags,
      final ObjectId pContainerId,
      final List<RegionName> pRegionsNeedingSubnets) {
    return GCPProvisionContainerMove.factoryCreate(
        getContext(),
        pTags,
        pContainerId,
        pRegionsNeedingSubnets.stream()
            .map(GCPRegionName.class::cast)
            .collect(Collectors.toList()));
  }

  @Override
  public Move getDestroyContainerMove(final ObjectId pContainerId) {
    return GCPDestroyContainerMove.factoryCreate(getContext(), pContainerId);
  }

  @Override
  public Move getEnsureIpWhitelistAppliedMove(
      final Map<String, String> pTags, final ObjectId pContainerId) {
    return GCPEnsureNetworkPermissionsAppliedMove.factoryCreate(getContext(), pTags, pContainerId);
  }

  @Override
  public Move getEnsureServerlessNetworkPermissionsAppliedMove(final ObjectId pContainerId) {
    return GCPEnsureServerlessNetworkPermissionsAppliedMove.factoryCreate(
        getContext(), pContainerId);
  }

  @Override
  public Move getEnsureKafkaNetworkPermissionsAppliedMove(ObjectId pContainerId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getSyncPeeringConnectionMove(final ObjectId pContainerId, final ObjectId pPeerId) {
    return GCPSyncPeeringConnectionMove.factoryCreate(getContext(), pContainerId, pPeerId);
  }

  @Override
  public Move getSyncDataLakePrivateEndpointHostnameMove() {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getSyncDataLakePrivateLinkConnectionMove() {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getProvisionMachineMove(
      final Map<String, String> pTags,
      final String pClusterName,
      final ObjectId pInstanceId,
      final boolean pIsCapacityAwareAZSelectionFFEnabled) {
    return GCPProvisionMachineMove.factoryCreate(
        getContext(), pTags, pClusterName, pInstanceId, pIsCapacityAwareAZSelectionFFEnabled);
  }

  @Override
  public Move getDestroyMachineMove(
      final Map<String, String> pTags,
      final String pClusterName,
      final ObjectId pInstanceId,
      final boolean pReduceMongodShutdownWaitTime) {
    return GCPDestroyMachineMove.factoryCreate(
        getContext(), pTags, pClusterName, pInstanceId, pReduceMongodShutdownWaitTime);
  }

  @Override
  public Move getCreateBackupSnapshotMove(final ClusterDescription pClusterDescription) {
    if (pClusterDescription.isDisaggregatedStorageSystem()) {
      return CpsSLSSnapshotMove.factoryCreate(getContext(), pClusterDescription.getName());
    }
    if (pClusterDescription.getClusterType().isSharded()) {
      return CpsShardedSnapshotMove.factoryCreate(getContext(), pClusterDescription.getName());
    }
    return CpsReplSetSnapshotMove.factoryCreate(getContext(), pClusterDescription.getName());
  }

  @Override
  public Move getUpdateMachineSizeMove(
      final Map<String, String> pTags,
      final String pClusterName,
      final ObjectId pInstanceId,
      final boolean pShouldIncreaseReplicaSetMemberPriority) {
    return GCPUpdateInstanceSizeMove.factoryCreate(
        getContext(), pTags, pClusterName, pInstanceId, pShouldIncreaseReplicaSetMemberPriority);
  }

  @Override
  public Move getPowerCycleMachineMove(
      final Map<String, String> pTags,
      final String pClusterName,
      final ObjectId pInstanceId,
      final boolean pReduceMongodShutdownWaitTime) {
    return GCPInstancePowerCycleMove.factoryCreate(
        getContext(), pClusterName, pInstanceId, pReduceMongodShutdownWaitTime);
  }

  @Override
  public Move getReplaceDiskMove(
      final Map<String, String> pTags, final String pClusterName, final ObjectId pInstanceId) {
    return GCPReplaceDiskMove.factoryCreate(getContext(), pClusterName, pInstanceId);
  }

  @Override
  public Move getModifyDiskMove(
      final Map<String, String> pTags, final String pClusterName, final ObjectId pInstanceId) {
    return GCPModifyDiskMove.factoryCreate(getContext(), pTags, pClusterName, pInstanceId);
  }

  @Override
  public boolean requireNodeHealthyCheckAfterModifyDiskMove() {
    return false;
  }

  @Override
  public Move getSyncPauseStateMove(final String pClusterName, final ObjectId pInstanceId) {
    return GCPSyncPauseStateMove.factoryCreate(getContext(), pClusterName, pInstanceId);
  }

  @Override
  public Move getUpdateContainerSubnetsMove(
      final ObjectId pContainerId,
      final List<RegionName> pSubnetRegionsToCreate,
      final List<RegionName> pSubnetRegionsToDelete) {
    return GCPUpdateContainerSubnetsMove.factoryCreate(
        getContext(), pSubnetRegionsToCreate, pSubnetRegionsToDelete);
  }

  @Override
  public Move getStopStartInstanceMove(
      final String pClusterName, final ObjectId pInstanceId, final ObjectId pNDSAdminJobId) {
    return GCPStopStartInstanceMove.factoryCreate(
        getContext(), pClusterName, pInstanceId, pNDSAdminJobId);
  }

  @Override
  public Move getAdminUpdateInstanceSizeMove(
      final String pClusterName, final ObjectId pInstanceId, final ObjectId pNDSAdminJobId) {
    return GCPAdminUpdateInstanceSizeMove.factoryCreate(
        getContext(), pClusterName, pInstanceId, pNDSAdminJobId);
  }

  @Override
  public Move getProvisionServerlessNATGatewayMove(
      final ObjectId pContainerId, final ObjectId pDeploymentId) {
    return GCPProvisionEnvoyNATGatewayMove.factoryCreate(getContext(), pContainerId, pDeploymentId);
  }

  @Override
  public Move getDestroyServerlessNATGatewayMove(
      final ObjectId pContainerId, final ObjectId pDeploymentId) {
    return GCPDestroyEnvoyNATGatewayMove.factoryCreate(getContext(), pContainerId, pDeploymentId);
  }

  @Override
  public Move getProvisionServerlessCloudLoadBalancerMove(
      ObjectId pContainerId, final ObjectId pServerlessLoadBalancingDeploymentId) {
    return GCPProvisionServerlessCloudLoadBalancerMove.factoryCreate(
        getContext(), pContainerId, pServerlessLoadBalancingDeploymentId);
  }

  @Override
  public Move getDestroyServerlessCloudLoadBalancerMove(
      ObjectId pContainerId, final ObjectId pServerlessLoadBalancingDeploymentId) {
    return GCPDestroyCloudLoadBalancerMove.factoryCreate(
        getContext(), pContainerId, pServerlessLoadBalancingDeploymentId);
  }

  @Override
  public Move getProvisionEnvoyInstanceMove(
      final ObjectId pEnvoyInstanceId,
      final ObjectId pContainerId,
      final ObjectId pServerlessDeploymentId) {
    return GCPProvisionEnvoyInstanceMove.factoryCreate(
        getContext(), pEnvoyInstanceId, pContainerId, pServerlessDeploymentId);
  }

  @Override
  public Move getDestroyEnvoyInstanceMove(
      final ObjectId pEnvoyInstanceId,
      final ObjectId pContainerId,
      final ObjectId pServerlessDeploymentId) {
    return GCPDestroyEnvoyInstanceMove.factoryCreate(
        getContext(), pEnvoyInstanceId, pContainerId, pServerlessDeploymentId);
  }

  @Override
  public boolean isUpdateMachineSizeSupported(final ClusterDescription pClusterDescription) {
    return true;
  }

  @Override
  public boolean isReplaceDiskSupported(final ClusterDescription pClusterDescription) {
    return true;
  }

  @Override
  public boolean isModifyDiskSupported(final ClusterDescription pClusterDescription) {
    return true;
  }

  @Override
  public boolean isIpWhitelistSupported() {
    return true;
  }

  @Override
  public boolean isPeeringSupported() {
    return true;
  }

  @Override
  public Move getRestartServerMove(final String pClusterName, final ObjectId pInstanceId) {
    return GCPRestartServerMove.factoryCreate(getContext(), pClusterName, pInstanceId);
  }

  @Override
  public Move getOptionalRestartServerMove(final String pClusterName, final ObjectId pInstanceId) {
    return GCPOptionalRestartServerMove.factoryCreate(getContext(), pClusterName, pInstanceId);
  }

  @Override
  public Move getAdminRestartServerMove(
      final String pClusterName, final ObjectId pInstanceId, final ObjectId pAdminJobId) {
    return GCPAdminRestartServerMove.factoryCreate(
        getContext(), pClusterName, pInstanceId, pAdminJobId);
  }

  @Override
  public Move getAdminBackupSnapshotMove(
      final String pClusterName, final ObjectId pInstanceId, final ObjectId pAdminJobId) {
    return GCPAdminBackupSnapshotMove.factoryCreate(
        getContext(), pClusterName, pInstanceId, pAdminJobId);
  }

  @Override
  public Move getRestartServerWithoutProcessesMove(
      final String pClusterName, final ObjectId pInstanceId) {
    return GCPRestartServerWithoutProcessesMove.factoryCreate(
        getContext(), pClusterName, pInstanceId);
  }

  @Override
  public Move getProvisionRestoreMachineMove(final ObjectId pRestoreJobId) {
    return GCPProvisionRestoreMachineMove.factoryCreate(getContext(), pRestoreJobId);
  }

  @Override
  public Move getDestroyRestoreMachineMove(
      final Map<String, String> pTags, final ObjectId pRestoreJobId) {
    return GCPDestroyRestoreMachineMove.factoryCreate(getContext(), pTags, pRestoreJobId);
  }

  @Override
  public Move getDirectAttachRestoreMove(
      final String pClusterName, final ObjectId pInstanceId, final ObjectId pRestoreJobId) {
    return GCPDirectAttachRestoreMove.factoryCreate(
        getContext(), pClusterName, pInstanceId, pRestoreJobId);
  }

  @Override
  public Move getOptimizedDirectAttachRestoreMove(
      final String pClusterName, final ObjectId pInstanceId, final ObjectId pRestoreJobId) {
    return GCPOptimizedDirectAttachRestoreMove.factoryCreate(
        getContext(), pClusterName, pInstanceId, pRestoreJobId);
  }

  @Override
  public Move getCleanUpDirectAttachRestoreMove(final ObjectId pRestoreJobId) {
    return GCPCleanUpDirectAttachRestoreMove.factoryCreate(getContext(), pRestoreJobId);
  }

  @Override
  public Move getSLSRestoreMove(final ObjectId pRestoreJobId, final String pClusterName) {
    return CpsSLSRestoreMove.factoryCreate(getContext(), pRestoreJobId, pClusterName);
  }

  @Override
  public Move getDataValidationMove(final ObjectId pValidationRecordId) {
    return GCPDataValidationMove.factoryCreate(getContext(), pValidationRecordId);
  }

  @Override
  public Move getSwapMachineMove(final String pClusterName, final ObjectId pInstanceId) {
    return GCPSwapMachineMove.factoryCreate(getContext(), pClusterName, pInstanceId);
  }

  @Override
  public Move getSyncEndpointServiceConnectionMove(
      final ObjectId pContainerId, final ObjectId pEndpointServiceId) {
    return GCPSyncPrivateServiceConnectRegionGroupMove.factoryCreate(
        getContext(), pContainerId, pEndpointServiceId);
  }

  @Override
  public Move getSyncClusterPrivateEndpointMove(final String pClusterName) {
    return GCPSyncClusterWithPrivateServiceConnectMove.factoryCreate(getContext(), pClusterName);
  }

  @Override
  public Move getResilientSnapshotMove(final String pClusterName, final ObjectId pSnapshotId) {
    return CreateResilientSnapshotMove.factoryCreate(getContext(), pClusterName, pSnapshotId);
  }

  @Override
  public Move getProvisionServerlessRestoreMachineMove(final ObjectId pRestoreJobId) {
    return GCPProvisionServerlessRestoreMachineMove.factoryCreate(getContext(), pRestoreJobId);
  }

  @Override
  public boolean isPrivateEndpointSupported() {
    return true;
  }

  @Override
  public Move getDestroyServerlessRestoreMachineMove(ObjectId pRestoreJobId) {
    return GCPDestroyServerlessRestoreMachineMove.factoryCreate(getContext(), pRestoreJobId);
  }

  @Override
  public Move getRestartEnvoyServerMove(
      final ObjectId pEnvoyInstanceId,
      final ObjectId pContainerId,
      final ObjectId pServerlessDeploymentId) {
    return GCPRestartEnvoyServerMove.factoryCreate(
        getContext(), pEnvoyInstanceId, pContainerId, pServerlessDeploymentId);
  }

  @Override
  public Move getEnsureClusterConnectionStringsMove(final String pClusterName) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getTenantProducerCreatePrivateEndpointServiceMove(
      final ObjectId pContainerId, final ObjectId pNDSTenantEndpointServiceId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getTenantProducerDeletePrivateEndpointServiceMove(
      final ObjectId pContainerId, final ObjectId pNDSTenantEndpointServiceId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getTenantConsumerReserveEndpointServiceSlotMove(
      final String pTenantName, final ObjectId pNDSTenantEndpointId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getTenantConsumerAcceptPrivateEndpointMove(
      final String pTenantName, final ObjectId pNDSTenantEndpointId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getTenantConsumerRejectPrivateEndpointMove(
      final String pTenantName, final ObjectId pNDSTenantEndpointId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getSyncEncryptionAtRestPrivateEndpointMove(final ObjectId pContainerId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getTenantPreProvisionFastRecordResourcesMove(final ObjectId pRecordId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getTenantDestroyPreProvisionFastRecordResourcesMove(final ObjectId pRecordId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getCompactionMove(final Map<String, String> pTags, final ObjectId pUniqueId) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Move getFlexMigrationMove(
      final String pSharedTenantClusterName,
      final ObjectId pSharedTenantUniqueId,
      final FlexTenantMigration pFlexTenantMigration) {
    throw new UnsupportedOperationException();
  }

  /**
   * Move to Provision Cloud Provider Access Role, supports gcp for now and can be extended for
   * other providers.
   *
   * @param pTags A map of tags associated with the move.
   * @param pContainerId The ID of the cloud provider container.
   * @return A new instance of GCPServiceAccountSetupMove.
   */
  @Override
  public Move getCloudProviderAccessRoleSetupMove(
      final Map<String, String> pTags, final ObjectId pContainerId) {
    return GCPServiceAccountSetupMove.factoryCreate(getContext(), pContainerId, pTags);
  }

  @Override
  public Move getObjectStoragePrivateEndpointMove(final ObjectId pContainerId) {
    throw new UnsupportedOperationException("Operation not supported for GCP");
  }

  /**
   * Creates a move to set up Prometheus monitoring over private networking for the specified
   * container.
   *
   * @param pContainerId the ID of the container for which to create Prometheus private networking
   * @return never returns - always throws UnsupportedOperationException
   * @throws UnsupportedOperationException always, as GCP does not support Prometheus private
   *     networking
   */
  @Override
  public Move getPrometheusPrivateNetworkingCreateMove(final ObjectId pContainerId) {
    throw new UnsupportedOperationException("Operation not supported for GCP");
  }
}
