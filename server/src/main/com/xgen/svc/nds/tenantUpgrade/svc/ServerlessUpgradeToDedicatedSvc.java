package com.xgen.svc.nds.tenantUpgrade.svc;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xgen.cloud.activity._public.svc.alert.InformationalAlertSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.common.model._public.email.TemplateMap;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.deployment._public.model.diff.ItemDiff;
import com.xgen.cloud.email._public.model.EmailMsg;
import com.xgen.cloud.email._public.svc.EmailSvc;
import com.xgen.cloud.email._public.svc.GroupEmailSvc;
import com.xgen.cloud.email._public.svc.template.HandlebarsTemplateSvc;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventJobSubmissionSvc;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.metrics._public.svc.serverless.ServerlessMetricsSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSAutoScaling;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSComputeAutoScaling;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.autoscaling.AzureAutoScaling;
import com.xgen.cloud.nds.azure._public.model.autoscaling.AzureComputeAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec.Builder;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.ShardRegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoIndexing;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaling;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.common._public.util.NDSUtil;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.autoscaling.GCPAutoScaling;
import com.xgen.cloud.nds.gcp._public.model.autoscaling.GCPComputeAutoScaling;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSTemplate;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.temporarydownloadlinks._public.svc.TemporaryDownloadLinkSvc;
import com.xgen.cloud.nds.tenant._public.model.TenantCloudProviderContainer;
import com.xgen.cloud.nds.tenantupgrade._private.dao.ServerlessUpgradeLocationDao;
import com.xgen.cloud.nds.tenantupgrade._private.dao.ServerlessUpgradeToDedicatedStatusDao;
import com.xgen.cloud.nds.tenantupgrade._private.dao.TenantClusterDescriptionUpgradeDao;
import com.xgen.cloud.nds.tenantupgrade._private.dao.TenantUpgradeLogDao;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeLocation;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeToDedicatedStatus;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.PlanExecutorJobHandler;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.svc.PlanSvc;
import com.xgen.module.liveimport.dao.LiveImportLocationDao;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.security.svc.NDSACMESvc;
import com.xgen.svc.nds.svc.MTMClusterSvc;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import com.xgen.svc.nds.svc.NDSClusterConversionSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.svc.nds.tenant.svc.privatenetworking.NDSTenantEndpointSvc;
import com.xgen.svc.nds.tenantUpgrade.TenantUpgradeErrorCode;
import com.xgen.svc.nds.tenantUpgrade.model.MigrateChartsServerlessToDedicatedRequestView;
import com.xgen.svc.nds.tenantUpgrade.model.MigrateChartsServerlessToDedicatedResponseView;
import com.xgen.svc.nds.tenantUpgrade.planner.DoServerlessUpgradeToDedicatedMove;
import com.xgen.svc.nds.util.ClusterValidationUtil;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ServerlessUpgradeToDedicatedSvc
    extends BaseTenantUpgradeSvc<ServerlessUpgradeToDedicatedStatus> {

  public static final String SERVERLESS_FORCED_UPGRADE_TO_DEDICATED_CRON_NAME =
      "runServerlessForcedUpgradeToDedicated";
  private static final Logger LOG = LoggerFactory.getLogger(ServerlessUpgradeToDedicatedSvc.class);
  protected static final String MIGRATE_SERVERLESS_CHARTS_ENDPOINT = "migrateServerlessCharts";
  public static final Long FLEX_DATA_SIZE_LIMIT_BYTES = 5L * 1024 * 1024 * 1024; // 5GB
  public static final Long CONCURRENT_UPGRADES_DATA_SIZE_LIMIT = 50L * 1024 * 1024 * 1024; // 50GB
  public static final int NUM_CONCURRENT_UPGRADES_THRESHOLD = 2;
  public static final String FORCED_UPGRADE_FROM_EMAIL_ADDR = "<EMAIL>";

  // Prometheus metrics
  private static final String GROUP_ID_PROM_LABEL = "group_id";
  private static final String CLUSTER_NAME_PROM_LABEL = "cluster_name";
  private static final String SOURCE_TENANT_UNIQUE_ID_PROM_LABEL = "source_tenant_unique_id";
  private static final String SOURCE_MTM_GROUP_ID_PROM_LABEL = "source_mtm_group_id";
  private static final String SOURCE_MTM_CLUSTER_NAME_PROM_LABEL = "source_mtm_cluster_name";
  private static final Counter SERVERLESS_FORCED_UPGRADE_TO_DEDICATED_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_serverless_forced_upgrade_to_dedicated_total",
          "Number of forced upgrades from serverless to dedicated scheduled",
          GROUP_ID_PROM_LABEL,
          CLUSTER_NAME_PROM_LABEL,
          SOURCE_TENANT_UNIQUE_ID_PROM_LABEL,
          SOURCE_MTM_GROUP_ID_PROM_LABEL,
          SOURCE_MTM_CLUSTER_NAME_PROM_LABEL);
  private static final Counter LONG_RUNNING_SERVERLESS_UPGRADE_TO_DEDICATED_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_long_running_serverless_upgrade_to_dedicated_total",
          "Number of long-running serverless upgrades to dedicated",
          GROUP_ID_PROM_LABEL,
          CLUSTER_NAME_PROM_LABEL,
          SOURCE_TENANT_UNIQUE_ID_PROM_LABEL);

  private final ServerlessUpgradeToDedicatedStatusDao _serverlessUpgradeToDedicatedStatusDao;
  private final ServerlessUpgradeLocationDao _serverlessUpgradeLocationDao;
  private final ClusterDescriptionDao _clusterDescriptionDao;
  private final NDSTenantEndpointSvc _ndsTenantEndpointSvc;
  private final ServerlessMetricsSvc _serverlessMetricsSvc;
  private final MTMClusterSvc _mtmClusterSvc;
  private final NDSClusterConversionSvc _ndsClusterConversionSvc;
  private final NDSUISvc _ndsUISvc;
  private final OrganizationSvc _organizationSvc;
  private final GroupSvc _groupSvc;
  private final GroupEmailSvc _groupEmailSvc;
  private final EmailSvc _emailSvc;
  private final HandlebarsTemplateSvc _templateSvc;
  private final Cache<ObjectId, Boolean> _forcedUpgradeNotNeededForTenantsCache;

  @Inject
  public ServerlessUpgradeToDedicatedSvc(
      final TenantClusterDescriptionUpgradeDao pTenantClusterDescriptionUpgradeDao,
      final ClusterValidationUtil pClusterValidationUtil,
      final NDSClusterSvc pNDSClusterSvc,
      final NDSGroupDao pNDSGroupDao,
      final AWSAccountDao pAWSAccountDao,
      final AWSApiSvc pAWSApiSvc,
      final TemporaryDownloadLinkSvc pTemporaryDownloadLinkSvc,
      final PlanSvc pPlanSvc,
      final JobsProcessorSvc pJobSvc,
      final AuditSvc pAuditSvc,
      final InformationalAlertSvc pInformationalAlertSvc,
      final AppSettings pAppSettings,
      final TenantUpgradeLogDao pTenantUpgradeLogDao,
      final LiveImportLocationDao pLiveImportLocationDao,
      final GroupDao pGroupDao,
      final SegmentEventJobSubmissionSvc pSegmentEventJobSubmissionSvc,
      final SegmentEventSvc pSegmentEventSvc,
      final NDSACMESvc pNdsACMESvc,
      final ReplicaSetHardwareDao pReplicaSetHardwareDao,
      final FeatureFlagSvc pFeatureFlagSvc,
      final NDSCloudProviderContainerSvc pNDSCloudProviderContainerSvc,
      final NDSClusterConversionSvc pNDSClusterConversionSvc,
      final NDSGroupSvc pNDSGroupSvc,
      final ServerlessUpgradeToDedicatedStatusDao pServerlessUpgradeToDedicatedStatusDao,
      final ServerlessUpgradeLocationDao pServerlessUpgradeLocationDao,
      final HttpUtils pHttpUtils,
      final ClusterDescriptionDao pClusterDescriptionDao,
      final NDSTenantEndpointSvc pNdsTenantEndpointSvc,
      final ServerlessMetricsSvc pServerlessMetricsSvc,
      final MTMClusterSvc pMtmClusterSvc,
      final NDSClusterConversionSvc pNdsClusterConversionSvc,
      final NDSUISvc pNdsUISvc,
      final OrganizationSvc pOrganizationSvc,
      final GroupSvc pGroupSvc,
      final GroupEmailSvc pGroupEmailSvc,
      final EmailSvc pEmailSvc,
      final HandlebarsTemplateSvc pTemplateSvc) {
    super(
        pTenantClusterDescriptionUpgradeDao,
        pClusterValidationUtil,
        pNDSClusterSvc,
        pNDSGroupDao,
        pAWSAccountDao,
        pAWSApiSvc,
        pTemporaryDownloadLinkSvc,
        pPlanSvc,
        pJobSvc,
        pAuditSvc,
        pInformationalAlertSvc,
        pAppSettings,
        pTenantUpgradeLogDao,
        pLiveImportLocationDao,
        pGroupDao,
        pSegmentEventJobSubmissionSvc,
        pSegmentEventSvc,
        pNdsACMESvc,
        pReplicaSetHardwareDao,
        pFeatureFlagSvc,
        pNDSCloudProviderContainerSvc,
        pNDSClusterConversionSvc,
        pNDSGroupSvc,
        pHttpUtils);
    _serverlessUpgradeToDedicatedStatusDao = pServerlessUpgradeToDedicatedStatusDao;
    _serverlessUpgradeLocationDao = pServerlessUpgradeLocationDao;
    _clusterDescriptionDao = pClusterDescriptionDao;
    _ndsTenantEndpointSvc = pNdsTenantEndpointSvc;
    _serverlessMetricsSvc = pServerlessMetricsSvc;
    _mtmClusterSvc = pMtmClusterSvc;
    _ndsClusterConversionSvc = pNdsClusterConversionSvc;
    _ndsUISvc = pNdsUISvc;
    _organizationSvc = pOrganizationSvc;
    _groupSvc = pGroupSvc;
    _groupEmailSvc = pGroupEmailSvc;
    _emailSvc = pEmailSvc;
    _templateSvc = pTemplateSvc;
    _forcedUpgradeNotNeededForTenantsCache =
        Caffeine.newBuilder().expireAfterWrite(Duration.ofHours(24)).build();
  }

  @Override
  public ClusterDescription getNewClusterDescription(
      final ClusterDescriptionView pClusterDescriptionView,
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final ClusterDescription pExistingCluster)
      throws SvcException {
    final String mongodbVersion =
        ensureMongoDBVersion(
            pNDSGroup.getGroupId(),
            pGroup.getOrgId(),
            pClusterDescriptionView,
            pClusterDescriptionView.getVersionReleaseSystem());

    return getNewClusterDescription(
        pClusterDescriptionView, pGroup, pNDSGroup, pExistingCluster, mongodbVersion);
  }

  @Override
  protected ServerlessUpgradeToDedicatedStatus createUpgradeStatus(
      final ClusterDescriptionView pClusterDescriptionView,
      final ObjectId pGroupId,
      final boolean pIsForcedServerlessUpgradeToDedicated)
      throws SvcException {
    // Fetch updated ClusterDescription for Serverless instance.
    final Pair<ClusterDescription, ? extends TenantCloudProviderContainer> tenantInfo =
        getNDSClusterSvc().getTenantClusterInfo(pGroupId, pClusterDescriptionView.getName());

    final ServerlessUpgradeToDedicatedStatus serverlessUpgradeToDedicatedStatus =
        new ServerlessUpgradeToDedicatedStatus(
            pClusterDescriptionView.getName(),
            pGroupId,
            tenantInfo.getRight().getClusterName(),
            tenantInfo.getRight().getGroupId(),
            tenantInfo.getLeft().getUniqueId(),
            new Date(),
            findUpgradeLocation(tenantInfo.getLeft()),
            tenantInfo.getLeft(),
            null,
            pIsForcedServerlessUpgradeToDedicated);

    _serverlessUpgradeToDedicatedStatusDao.save(serverlessUpgradeToDedicatedStatus);
    return serverlessUpgradeToDedicatedStatus;
  }

  @Override
  protected void startUpgradePlan(
      final ServerlessUpgradeToDedicatedStatus pServerlessUpgradeToDedicatedStatus) {
    final Plan plan = BaseTenantUpgradeSvc.getNewPlan(pServerlessUpgradeToDedicatedStatus);
    final Move doServerlessUpgradeToDedicatedMove =
        DoServerlessUpgradeToDedicatedMove.factoryCreate(
            plan.getPlanContext(), pServerlessUpgradeToDedicatedStatus.getId());
    plan.addMove(doServerlessUpgradeToDedicatedMove);
    _planSvc.savePlanAndInsertJob(
        plan,
        PlanExecutorJobHandler.getJobWithTagForPlan(
            plan.getId(),
            pServerlessUpgradeToDedicatedStatus.getUpgradeLocation().getJobQueueTag()));
  }

  @Override
  public void startClusterUpgrade(
      final Organization pOrganization,
      final ClusterDescriptionView pClusterDescriptionView,
      final ObjectId pGroupId,
      final AppUser pUser,
      final AuditInfo pAuditInfo,
      final HttpServletRequest pRequest)
      throws SvcException {
    startClusterUpgrade(
        pOrganization, pClusterDescriptionView, pGroupId, pUser, pAuditInfo, pRequest, false);
  }

  public void startClusterUpgrade(
      final Organization pOrganization,
      final ClusterDescriptionView pClusterDescriptionView,
      final ObjectId pGroupId,
      final AppUser pUser,
      final AuditInfo pAuditInfo,
      final HttpServletRequest pRequest,
      final boolean pIsForcedServerlessUpgradeToDedicated)
      throws SvcException {

    final ClusterDescription existingClusterDescription =
        getExistingClusterDescription(pClusterDescriptionView, pGroupId);
    // Note: We must use another svc to start the cluster upgrade if the source is not a Serverless
    // instance.
    if (!existingClusterDescription.isServerlessTenantCluster()) {
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED,
          "Source cluster for the migration must be a serverless instance.");
    }

    super.startClusterUpgrade(
        pOrganization,
        pClusterDescriptionView,
        existingClusterDescription,
        pGroupId,
        pUser,
        pAuditInfo,
        pRequest,
        ClusterCreateContext.forTenantUpgrade(),
        pIsForcedServerlessUpgradeToDedicated);
  }

  public void startForcedClusterUpgrade(
      final Organization pOrganization,
      final ClusterDescriptionView pClusterDescriptionView,
      final ObjectId pGroupId,
      final AppUser pUser,
      final AuditInfo pAuditInfo,
      final HttpServletRequest pRequest)
      throws SvcException {
    final ClusterDescription existingClusterDescription =
        getExistingClusterDescription(pClusterDescriptionView, pGroupId);
    // Note: We must use another svc to start the cluster upgrade if the source is not a Serverless
    // instance.
    if (!existingClusterDescription.isServerlessTenantCluster()) {
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED,
          "Source cluster for the migration must be a serverless instance.");
    }

    final Optional<ServerlessUpgradeToDedicatedStatus> tenantUpgradeStatus =
        getTenantUpgradeStatusDao()
            .findInProgressByName(pGroupId, pClusterDescriptionView.getName());
    if (tenantUpgradeStatus.isPresent()) {
      throw new SvcException(NDSErrorCode.CLUSTER_UPGRADE_IN_PROGRESS);
    }

    final ClusterDescription newCluster =
        _ndsClusterConversionSvc.createProjectAgnosticDefaultClusterDescription(
            pClusterDescriptionView);

    final Collection<ClusterDescription> allClusterDescriptions =
        getNDSClusterSvc().getAllClusterDescriptions(pGroupId);

    final boolean isFirstDedicatedClusterInGroup =
        newCluster.isDedicatedCluster()
            && allClusterDescriptions.stream().noneMatch(ClusterDescription::isDedicatedCluster);

    if (isFirstDedicatedClusterInGroup) {
      getNdsGroupSvc()
          .performUpdatesForNewDedicatedTierGroup(
              pGroupId, newCluster.getDataProcessingRegion(), pAuditInfo);
    }

    final ItemDiff clusterDescriptionDiff =
        requestClusterUpgrade(
            pOrganization,
            pClusterDescriptionView,
            existingClusterDescription,
            pGroupId,
            pUser,
            pAuditInfo,
            pRequest,
            ClusterCreateContext.forTenantUpgrade(),
            true);
    final ServerlessUpgradeToDedicatedStatus upgradeStatus =
        createUpgradeStatus(pClusterDescriptionView, pGroupId, true);

    // If the new cluster is also a shared cluster, then we need to
    // set the new root cert type on the other shared clusters in the project.
    if (!existingClusterDescription.getRootCertType().equals(newCluster.getRootCertType())
        && newCluster.isTenantCluster()) {
      processRootCertTypeChange(pGroupId, newCluster);
    }

    startUpgradePlan(upgradeStatus);
    saveAuditEvent(
        existingClusterDescription.isServerlessTenantCluster()
            ? NDSAudit.Type.SERVERLESS_UPGRADE_STARTED
            : existingClusterDescription.isSharedTenantCluster()
                ? NDSAudit.Type.FREE_UPGRADE_STARTED
                : NDSAudit.Type.FLEX_UPGRADE_STARTED,
        upgradeStatus,
        pAuditInfo,
        clusterDescriptionDiff);
  }

  @Override
  protected ServerlessUpgradeToDedicatedStatusDao getTenantUpgradeStatusDao() {
    return _serverlessUpgradeToDedicatedStatusDao;
  }

  @Override
  protected List<String> getServerlessMTMClusterIPAddresses(
      final ObjectId pTenantGroupId, final String pTenantClusterName) {
    throw new NotImplementedException("This method is not supported");
  }

  @Override
  Logger getLogger() {
    return LOG;
  }

  private ServerlessUpgradeLocation findUpgradeLocation(
      final ClusterDescription pSourceClusterDescription) {
    final RegionName upgradeRegion = pSourceClusterDescription.getRegionName();

    return _serverlessUpgradeLocationDao.findAllLocations().stream()
        .map(
            location -> {
              final double distanceToCluster =
                  NDSUtil.calculateDistanceBetweenPointsInKM(
                      location.getRegion().getLatitude(),
                      upgradeRegion.getLatitude(),
                      location.getRegion().getLongitude(),
                      upgradeRegion.getLongitude());

              return Pair.of(distanceToCluster, location);
            })
        .min(Comparator.comparing(Pair::getLeft))
        .orElse(
            Pair.of(
                0d,
                new ServerlessUpgradeLocation(
                    new ObjectId(),
                    AWSRegionName.US_EAST_1,
                    "TENANT_UPGRADE_US_EAST_1",
                    getS3TenantDataBucketName())))
        .getRight();
  }

  public Optional<ServerlessUpgradeToDedicatedStatus> findCompletedByTargetUniqueId(
      final ObjectId pGroupId, final ObjectId pTargetUniqueId) {
    return _serverlessUpgradeToDedicatedStatusDao.findCompletedByTargetUniqueId(
        pGroupId, pTargetUniqueId);
  }

  public boolean migrateCharts(
      final ObjectId pGroupId,
      final String pServerlessDeploymentName,
      final String pClusterDeploymentName)
      throws SvcException {
    if (getAppSettings().isLocal()) {
      // If calling this method locally, don't attempt to query Charts API
      return true;
    }

    final MigrateChartsServerlessToDedicatedRequestView chartsRequest =
        new MigrateChartsServerlessToDedicatedRequestView(
            pGroupId, pServerlessDeploymentName, pClusterDeploymentName);

    final MigrateChartsServerlessToDedicatedResponseView chartsResponse =
        getMigrateChartsServerlessToDedicatedResponse(chartsRequest);

    final Integer numOfDatasourcesFound = chartsResponse.getNumOfDatasourcesFound();
    final Integer numOfDatasourcesMigrated = chartsResponse.getNumOfDatasourcesMigrated();

    getLogger()
        .info(
            String.format(
                "Number of datasources found: %d, number of datasources migrated: %d",
                numOfDatasourcesFound, numOfDatasourcesMigrated));

    return Objects.equals(numOfDatasourcesMigrated, numOfDatasourcesFound);
  }

  protected MigrateChartsServerlessToDedicatedResponseView
      getMigrateChartsServerlessToDedicatedResponse(
          final MigrateChartsServerlessToDedicatedRequestView pChartsRequest) throws SvcException {
    final HttpPost httpRequest =
        getChartsHttpRequest(
            pChartsRequest, ServerlessUpgradeToDedicatedSvc.MIGRATE_SERVERLESS_CHARTS_ENDPOINT);
    try (final CloseableHttpResponse response = getHttpUtils().getClient().execute(httpRequest)) {
      if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
        throw new SvcException(
            TenantUpgradeErrorCode.UNEXPECTED_CHARTS_MIGRATION_FAILURE,
            response.getStatusLine().getStatusCode(),
            pChartsRequest.getServerlessDeploymentName(),
            pChartsRequest.getGroupId());
      }
      return _objectMapper.readValue(
          getResponseString(response), MigrateChartsServerlessToDedicatedResponseView.class);
    } catch (final IOException e) {
      throw new SvcException(TenantUpgradeErrorCode.CHARTS_REQUEST_FAILURE);
    }
  }

  public Optional<ServerlessUpgradeToDedicatedStatus> getCompletedForcedUpgradeForCluster(
      final ObjectId pGroupId, final String pClusterName) {
    final Optional<Date> clusterCreateDate =
        getNDSClusterSvc()
            .getActiveClusterDescription(pGroupId, pClusterName)
            .map(ClusterDescription::getCreateDate);
    if (clusterCreateDate.isEmpty()) {
      return Optional.empty();
    }
    return getTenantUpgradeStatusDao()
        .findCompletedForcedUpgradeForCluster(pGroupId, pClusterName, clusterCreateDate.get());
  }

  public boolean markForcedUpgradesAcknowledgedForCluster(
      final ObjectId pGroupId, final String pClusterName) {
    return getTenantUpgradeStatusDao()
        .markForcedUpgradesAcknowledgedForCluster(pGroupId, pClusterName);
  }

  public boolean enableForcedUpgradeRetryOverrideForCluster(final ObjectId pTenantUpgradeId) {
    return getTenantUpgradeStatusDao().enableForcedUpgradeRetryOverrideForCluster(pTenantUpgradeId);
  }

  public void serverlessForcedUpgradeToDedicatedRunner() {
    getLogger().info("Running serverless forced upgrade to dedicated cron job");

    final Map<ObjectId, List<ServerlessMTMCluster>> serverlessMTMClustersByGroupId =
        _mtmClusterSvc.findAllServerlessClusters().stream()
            .collect(Collectors.groupingBy(ServerlessMTMCluster::getGroupId));

    serverlessMTMClustersByGroupId.forEach(
        (groupId, mtmClusters) -> {
          final Group group = getGroupDao().findById(groupId);
          if (!getFeatureFlagSvc()
              .isFeatureFlagEnabled(
                  FeatureFlag.SERVERLESS_MTM_ELIGIBLE_FOR_UPGRADE_TO_DEDICATED, null, group)) {
            getLogger()
                .info(
                    "Group with ID {} is not eligible for forced upgrades to dedicated. Skipping"
                        + " all associated MTM clusters...",
                    groupId);
            return;
          }

          mtmClusters.forEach(
              mtmCluster -> {
                final List<ServerlessUpgradeToDedicatedStatus> inProgressUpgrades =
                    _serverlessUpgradeToDedicatedStatusDao
                        .getInProgressTenantUpgradesBySourceMTMCluster(
                            mtmCluster.getGroupId(), mtmCluster.getName());

                if (inProgressUpgrades.size() >= NUM_CONCURRENT_UPGRADES_THRESHOLD) {
                  getLogger()
                      .info(
                          "MTM cluster {} has {} in-progress upgrades in progress. Skipping...",
                          mtmCluster.getName(),
                          inProgressUpgrades.size());
                  return;
                }

                // For the in-progress upgrades, find out the total data size being transferred
                // to figure out if we should schedule more upgrades.
                final List<ObjectId> inProgressTenantUniqueIds =
                    inProgressUpgrades.stream()
                        .map(ServerlessUpgradeToDedicatedStatus::getSourceTenantUniqueId)
                        .collect(Collectors.toList());
                final long totalInProgressDataSizeBytes =
                    _serverlessMetricsSvc
                        .getDataSizeInBytesForTenants(inProgressTenantUniqueIds, Instant.now())
                        .values()
                        .stream()
                        .mapToLong(Long::longValue)
                        .sum();

                if (totalInProgressDataSizeBytes >= CONCURRENT_UPGRADES_DATA_SIZE_LIMIT) {
                  getLogger()
                      .info(
                          "MTM cluster {} has {} GB in progress. Skipping...",
                          mtmCluster.getName(),
                          Units.BYTES.convertTo(totalInProgressDataSizeBytes, Units.GIGABYTES));
                  return;
                }

                final AtomicLong remainingUpgradeBytes =
                    new AtomicLong(
                        CONCURRENT_UPGRADES_DATA_SIZE_LIMIT - totalInProgressDataSizeBytes);
                final AtomicLong remainingUpgradeSlots =
                    new AtomicLong(NUM_CONCURRENT_UPGRADES_THRESHOLD - inProgressUpgrades.size());

                final List<ObjectId> tenantUniqueIds =
                    mtmCluster.getProxyVersions().keySet().stream()
                        .map(ObjectId::new)
                        // Note: we filter out tenants that are already in-progress to avoid
                        // re-querying their data and attempting to schedule another upgrade
                        .filter(
                            tenantUniqueId -> !inProgressTenantUniqueIds.contains(tenantUniqueId))
                        .toList();

                final Map<ObjectId, Long> serverlessTenantsDataSizes =
                    _serverlessMetricsSvc.getDataSizeInBytesForTenants(
                        tenantUniqueIds, Instant.now());

                final List<ObjectId> tenantUniqueIdsSortedByDataSize =
                    serverlessTenantsDataSizes.entrySet().stream()
                        .sorted(Map.Entry.comparingByValue())
                        .map(Map.Entry::getKey)
                        .toList();

                tenantUniqueIdsSortedByDataSize.stream()
                    // Note: only continue to process if we have the capacity to schedule more
                    // upgrades.
                    .takeWhile(
                        tenantUniqueId ->
                            remainingUpgradeSlots.get() > 0 && remainingUpgradeBytes.get() > 0)
                    .forEach(
                        tenantUniqueId -> {
                          if (isForcedUpgradeNotNeededForTenant(tenantUniqueId)) {
                            getLogger()
                                .info(
                                    "Cluster description with unique id {} recently marked"
                                        + " as not needed. Skipping...",
                                    tenantUniqueId);
                            return;
                          }

                          if (tenantUniqueId.equals(
                              mtmCluster.getMtmClusterDescriptionUniqueId())) {
                            getLogger().info("Skipping MTM cluster proxy account");
                            return;
                          }

                          if (!mtmCluster
                              .getDedicatedMigrationAllowedTenantIds()
                              .contains(tenantUniqueId)) {
                            getLogger()
                                .info(
                                    "Skipping tenant for migration. The tenant id has not been"
                                        + " added to the allow list via maintenance.");
                            return;
                          }

                          final Optional<ServerlessUpgradeToDedicatedStatus>
                              previousForcedUpgradeFailureOpt =
                                  _serverlessUpgradeToDedicatedStatusDao
                                      .findLatestFailedWithNonRetriableErrorForForcedUpgrade(
                                          tenantUniqueId);

                          if (previousForcedUpgradeFailureOpt.isPresent()) {
                            if (!previousForcedUpgradeFailureOpt
                                .orElseThrow()
                                .isForcedUpgradeRetryOverrideEnabled()) {
                              getLogger()
                                  .info(
                                      "Skipping forced upgrade for tenant {} due to past"
                                          + " non-retriable failure with error class: {}",
                                      tenantUniqueId,
                                      previousForcedUpgradeFailureOpt.get().getErrorClass());
                              return;
                            } else {
                              getLogger()
                                  .info(
                                      "Previously failed forced upgrade for tenant {} has retry"
                                          + " override enabled.",
                                      tenantUniqueId);
                            }
                          }

                          final Optional<ClusterDescription> cdOpt =
                              _clusterDescriptionDao.findByUniqueId(null, tenantUniqueId);
                          if (cdOpt.isEmpty()) {
                            getLogger()
                                .info(
                                    "Cluster description with unique id {} not found. Assuming"
                                        + " deleted or being upgraded.",
                                    tenantUniqueId);
                            return;
                          }

                          final ClusterDescription cd = cdOpt.get();

                          if (cd.isDeletedOrDeleteRequested()) {
                            getLogger()
                                .info(
                                    "Serverless instance with unique id {} is being requested for"
                                        + " deletion or deleted. Skipping...",
                                    tenantUniqueId);
                            return;
                          }

                          if (shouldUpgradeToDedicated(cd, serverlessTenantsDataSizes)) {
                            final long tenantSize = serverlessTenantsDataSizes.get(tenantUniqueId);
                            if (attemptScheduleForcedUpgrade(cd, tenantSize)) {
                              incrementServerlessForcedUpgradeToDedicatedPromCounter(
                                  cd, mtmCluster);
                              remainingUpgradeSlots.decrementAndGet();
                              remainingUpgradeBytes.addAndGet(-tenantSize);
                            }
                          } else {
                            setForcedUpgradeNotNeededForTenant(tenantUniqueId);
                          }
                        });
              });
        });
  }

  // Note: The eligibility criteria for being forcefully migrated to Dedicated are as follows:
  // 1) The clusters logical data size currently exceeds 5GB
  // 2) The cluster has a private endpoint configured
  public boolean shouldUpgradeToDedicated(
      final ClusterDescription pClusterDescription,
      final Map<ObjectId, Long> pServerlessTenantDataSizesBytes) {
    if (!pServerlessTenantDataSizesBytes.containsKey(pClusterDescription.getUniqueId())) {
      getLogger()
          .debug(
              "Tenant data size is missing for serverless instance with unique ID {}. Assuming"
                  + " deleted.",
              pClusterDescription.getUniqueId());
      return false;
    }

    if (pServerlessTenantDataSizesBytes.get(pClusterDescription.getUniqueId())
        > FLEX_DATA_SIZE_LIMIT_BYTES) {
      getLogger()
          .info(
              "Serverless instance with unique ID {} has logical data size greater than threshold"
                  + " ({}) GB. Eligible for upgrade, scheduling...",
              pClusterDescription.getUniqueId(),
              Units.BYTES.convertTo(
                  pServerlessTenantDataSizesBytes.get(pClusterDescription.getUniqueId()),
                  Units.GIGABYTES));
      return true;
    }

    if (!pClusterDescription.getBackingProvider().equals(CloudProvider.GCP)
        && !_ndsTenantEndpointSvc.getEndpointsForTenant(pClusterDescription).isEmpty()) {
      getLogger()
          .info(
              "Serverless instance with unique ID {} has private endpoint. Eligible for upgrade,"
                  + " scheduling...",
              pClusterDescription.getUniqueId());
      return true;
    }

    getLogger()
        .debug(
            "Serverless instance with unique ID {} does not need to upgrade to dedicated.",
            pClusterDescription.getUniqueId());
    return false;
  }

  public boolean attemptScheduleForcedUpgrade(
      final ClusterDescription pClusterDescription, final Long pTenantDataSizeBytes) {
    try {
      final NDSGroup ndsGroup =
          getNdsGroupDao().find(pClusterDescription.getGroupId()).orElseThrow();
      final Group group = getGroupDao().findById(pClusterDescription.getGroupId());
      final Organization org = _organizationSvc.findById(group.getOrgId());

      // Get recommended instance size based on cluster's current data size
      final ClusterDescriptionView recommendedDestinationClusterDescriptionView =
          getRecommendedDestinationClusterDescription(
              pClusterDescription, ndsGroup, group, pTenantDataSizeBytes);

      startForcedClusterUpgrade(
          org,
          recommendedDestinationClusterDescriptionView,
          group.getId(),
          AppUser.SYSTEM_USER,
          AuditInfoHelpers.fromSystem(),
          null);
    } catch (Exception e) {
      if (e instanceof SvcException
          && ((SvcException) e).getErrorCode().equals(NDSErrorCode.CLUSTER_UPGRADE_IN_PROGRESS)) {
        getLogger()
            .warn(
                "Upgrade already in-progress for tenant with unique ID {}",
                pClusterDescription.getUniqueId(),
                e);
      } else {
        getLogger()
            .error(
                "Failed to schedule forced upgrade for tenant with unique ID {}",
                pClusterDescription.getUniqueId(),
                e);

        // Ensure the destination cluster description is removed to allow rescheduling.
        getTenantClusterDescriptionUpgradeDao()
            .removeByName(pClusterDescription.getGroupId(), pClusterDescription.getName());
      }

      return false;
    }
    getLogger()
        .info(
            "Scheduled forced upgrade successfully for tenant with unique ID {}",
            pClusterDescription.getUniqueId());
    return true;
  }

  public NDSInstanceSize getRecommendedInstanceSizeForUpgrade(
      final CloudProvider pProvider, final double pTenantDataSizeGB) {
    if (pTenantDataSizeGB >= 400.0) {
      if (pProvider == CloudProvider.AWS) {
        return AWSNDSInstanceSize.M140;
      } else if (pProvider == CloudProvider.GCP) {
        return GCPNDSInstanceSize.M140;
      } else {
        return AzureNDSInstanceSize.M200;
      }
    } else if (pTenantDataSizeGB >= 100.0) {
      if (pProvider == CloudProvider.AWS) {
        return AWSNDSInstanceSize.M80;
      } else if (pProvider == CloudProvider.GCP) {
        return GCPNDSInstanceSize.M80;
      } else {
        return AzureNDSInstanceSize.M80;
      }
    } else if (pTenantDataSizeGB >= 50.0) {
      if (pProvider == CloudProvider.AWS) {
        return AWSNDSInstanceSize.M50;
      } else if (pProvider == CloudProvider.GCP) {
        return GCPNDSInstanceSize.M50;
      } else {
        return AzureNDSInstanceSize.M50;
      }
    } else if (pTenantDataSizeGB >= 25.0) {
      if (pProvider == CloudProvider.AWS) {
        return AWSNDSInstanceSize.M40;
      } else if (pProvider == CloudProvider.GCP) {
        return GCPNDSInstanceSize.M40;
      } else {
        return AzureNDSInstanceSize.M40;
      }
    } else if (pTenantDataSizeGB >= 10.0) {
      if (pProvider == CloudProvider.AWS) {
        return AWSNDSInstanceSize.M30;
      } else if (pProvider == CloudProvider.GCP) {
        return GCPNDSInstanceSize.M30;
      } else {
        return AzureNDSInstanceSize.M30;
      }
    } else {
      if (pProvider == CloudProvider.AWS) {
        return AWSNDSInstanceSize.M20;
      } else if (pProvider == CloudProvider.GCP) {
        return GCPNDSInstanceSize.M20;
      } else {
        return AzureNDSInstanceSize.M20;
      }
    }
  }

  public NDSInstanceSize getMinInstanceSizeForAutoscaling(
      final CloudProvider pProvider,
      final NDSInstanceSize pNDSInstanceSize,
      final double pDiskSizeGB) {
    final NDSInstanceSize[] availableInstanceSizes =
        switch (pProvider) {
          case AWS -> AWSNDSInstanceSize.values();
          case GCP -> GCPNDSInstanceSize.values();
          case AZURE -> AzureNDSInstanceSize.values();
          default -> throw new IllegalStateException("Unexpected value: " + pProvider);
        };

    // Get the minimum standard instance size based the max disk size supported.
    // Otherwise, return the recommended instance size.
    return Arrays.stream(availableInstanceSizes)
        .filter(
            i -> !i.isLowCPU() && !i.isNVMe() && i.getMaxAllowedDiskSizeGB(false) >= pDiskSizeGB)
        .min(Comparator.comparingInt(i -> i.getMaxAllowedDiskSizeGB(false)))
        .orElse(pNDSInstanceSize);
  }

  public ClusterDescriptionView getRecommendedDestinationClusterDescription(
      final ClusterDescription pClusterDescription,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Long pTenantDataSizeBytes)
      throws SvcException {
    final CloudProvider provider = pClusterDescription.getBackingProvider();

    final double tenantDataSizeGB = Units.BYTES.convertTo(pTenantDataSizeBytes, Units.GIGABYTES);
    final NDSInstanceSize recommendedInstanceSize =
        getRecommendedInstanceSizeForUpgrade(provider, tenantDataSizeGB);

    // Note: The following calculation was developed by Product Analytics to estimate the on-disk
    // data size for a Serverless instance from the logical data size after compression.
    final double estimatedDiskUsage = 0.60 * tenantDataSizeGB + 3;

    // Note: We want to use a disk size such that the on disk data size is less than 50% of it.
    // Otherwise, we chose the default disk size for the recommended instance size as it's included
    // in the instance sizes price.
    final double recommendedDiskSizeGB =
        Math.max(Math.ceil(estimatedDiskUsage * 2), recommendedInstanceSize.getDefaultDiskSizeGB());

    // Create base cluster
    final ClusterDescription baseCluster =
        _ndsClusterConversionSvc.createProjectAgnosticDefaultClusterDescription(
            _ndsUISvc.getDefaultReplicaSet(
                provider,
                pNDSGroup,
                recommendedInstanceSize,
                false,
                pClusterDescription.getMongoDBMajorVersion()));

    // Create autoscaling configuration
    final AutoScaling autoScaling =
        createAutoScalingForProvider(provider, recommendedInstanceSize, recommendedDiskSizeGB);

    // Update replication spec
    final ReplicationSpec updatedSpec =
        getUpdatedReplicationSpec(
            baseCluster.getReplicationSpecsWithShardData().get(0),
            autoScaling,
            pClusterDescription.getRegionName());

    // Build common cluster config
    final ClusterDescription.Builder<?, ?> updatedClusterBuilder =
        baseCluster
            .copy()
            .setDiskSizeGB(recommendedDiskSizeGB)
            .setReplicationSpecList(Collections.singletonList(updatedSpec))
            .setAutoScalingForProvider(provider, autoScaling, NodeTypeFamily.BASE)
            .setMongoDBMajorVersion(pClusterDescription.getMongoDBMajorVersion())
            .setVersionReleaseSystem(VersionReleaseSystem.LTS)
            .setGroupId(pNDSGroup.getGroupId())
            .setName(pClusterDescription.getName());

    // sync with data protection settings
    getNDSClusterSvc().syncWithDataProtection(pGroup, updatedClusterBuilder);

    ClusterDescription updatedCluster = updatedClusterBuilder.build();

    // Handle Azure-specific updates
    if (provider == CloudProvider.AZURE) {
      final int diskSize = (int) updatedCluster.getDiskSizeGB();
      final boolean withSsdV2 = getNDSClusterSvc().isAzureSsdV2Available(pGroup, updatedCluster);
      final Builder hardwareSpecUpdate =
          ((AzureHardwareSpec) updatedCluster.getOnlyHardwareSpec(NodeType.ELECTABLE).orElseThrow())
              .buildDiskTypeRelatedUpdatesForNewCluster(diskSize, withSsdV2);

      if (hardwareSpecUpdate.getNodeCount().isPresent()) {
        throw new IllegalStateException("about to apply an instance node count update");
      }

      updatedCluster =
          updatedCluster
              .copy()
              .updateHardwareForProvider(
                  CloudProvider.AZURE, hardwareSpecUpdate, updatedSpec.getId())
              .build();
    }

    return new ClusterDescriptionView(updatedCluster);
  }

  private AutoScaling createAutoScalingForProvider(
      final CloudProvider pProvider,
      final NDSInstanceSize pNDSInstanceSize,
      final double pDiskSizeGB) {
    final NDSInstanceSize minInstanceSize =
        getMinInstanceSizeForAutoscaling(pProvider, pNDSInstanceSize, pDiskSizeGB);
    final AutoIndexing autoIndexing = new AutoIndexing(false);

    switch (pProvider) {
      case AWS:
        return AWSAutoScaling.builder()
            .compute(
                AWSComputeAutoScaling.builder()
                    .enabled(true)
                    .scaleDownEnabled(true)
                    .minInstanceSize((AWSNDSInstanceSize) minInstanceSize)
                    .maxInstanceSize((AWSNDSInstanceSize) pNDSInstanceSize)
                    .build())
            .autoIndexing(autoIndexing)
            .build();
      case GCP:
        return GCPAutoScaling.builder()
            .compute(
                GCPComputeAutoScaling.builder()
                    .enabled(true)
                    .scaleDownEnabled(true)
                    .minInstanceSize((GCPNDSInstanceSize) minInstanceSize)
                    .maxInstanceSize((GCPNDSInstanceSize) pNDSInstanceSize)
                    .build())
            .autoIndexing(autoIndexing)
            .build();
      case AZURE:
        return AzureAutoScaling.builder()
            .compute(
                AzureComputeAutoScaling.builder()
                    .enabled(true)
                    .scaleDownEnabled(true)
                    .minInstanceSize((AzureNDSInstanceSize) minInstanceSize)
                    .maxInstanceSize((AzureNDSInstanceSize) pNDSInstanceSize)
                    .build())
            .autoIndexing(autoIndexing)
            .build();
      default:
        throw new IllegalArgumentException("Unsupported cloud provider: " + pProvider);
    }
  }

  private ReplicationSpec getUpdatedReplicationSpec(
      final ReplicationSpec pExistingSpec,
      final AutoScaling pAutoscaling,
      final RegionName pRegionName) {
    final ShardRegionConfig existingConfig =
        (ShardRegionConfig) pExistingSpec.getHighestPriorityRegionConfig();

    final ShardRegionConfig newConfig =
        new ShardRegionConfig(
            pRegionName,
            pRegionName.getProvider(),
            pAutoscaling,
            null,
            existingConfig.getPriority(),
            existingConfig.getElectableSpecs(),
            existingConfig.getAnalyticsSpecs(),
            existingConfig.getReadOnlySpecs(),
            existingConfig.getHiddenSecondarySpecs(),
            existingConfig.getAZBalancingRequirementOverride().orElse(null),
            null,
            null,
            null);

    return new ReplicationSpec(
        pExistingSpec.getId(),
        pExistingSpec.getExternalId(),
        pExistingSpec.getZoneId(),
        pExistingSpec.getZoneName(),
        pExistingSpec.getNumShards(),
        List.of(newConfig));
  }

  public void incrementServerlessForcedUpgradeToDedicatedPromCounter(
      final ClusterDescription pClusterDescription, final ServerlessMTMCluster pMtmCluster) {
    NDSPromMetricsSvc.incrementCounter(
        SERVERLESS_FORCED_UPGRADE_TO_DEDICATED_COUNTER,
        pClusterDescription.getGroupId().toString(),
        pClusterDescription.getName(),
        pClusterDescription.getUniqueId().toString(),
        pMtmCluster.getGroupId().toString(),
        pMtmCluster.getName());
  }

  public boolean sendForcedUpgradeEmail(
      final ServerlessUpgradeToDedicatedStatus pServerlessUpgradeToDedicatedStatus) {
    if (getAppSettings().isLocal()) {
      getLogger().debug("Sending emails is not available in local env. Returning done...");
      return true;
    }

    final Group group = _groupSvc.findById(pServerlessUpgradeToDedicatedStatus.getGroupId());

    final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM dd, yyyy");
    final AtomicBoolean emailSent = new AtomicBoolean(false);
    final List<AppUser> usersWithPrimaryEmail =
        _groupSvc.getAllUsersWithTeams(group).stream()
            .filter(user -> user.getPrimaryEmail() != null)
            .toList();
    usersWithPrimaryEmail.forEach(
        user -> {
          try {
            final TemplateMap params = _groupEmailSvc.generateTemplateParameters(group);
            params.put(
                "date",
                formatter.format(
                    pServerlessUpgradeToDedicatedStatus
                        .getCompleteDate()
                        .toInstant()
                        .atZone(ZoneId.of("UTC"))
                        .toLocalDate()));
            params.put("clusterName", pServerlessUpgradeToDedicatedStatus.getClusterName());
            _emailSvc.send(
                new EmailMsg.Builder(getAppSettings())
                    .recipient(user.getPrimaryEmail())
                    .fromAddr(FORCED_UPGRADE_FROM_EMAIL_ADDR)
                    .subject(
                        "MongoDB Alert: Your Serverless cluster has been converted to a Dedicated"
                            + " cluster")
                    .htmlTemplate(NDSTemplate.SERVERLESS_FORCED_UPGRADE_TO_DEDICATED)
                    .templateParams(params)
                    .group(group)
                    .build(_templateSvc));
            if (!emailSent.get()) {
              emailSent.set(true);
            }
          } catch (final Exception pE) {
            getLogger().error("Error sending email to {}", user.getPrimaryEmail(), pE);
          }
        });

    if (!emailSent.get() && usersWithPrimaryEmail.isEmpty()) {
      getLogger()
          .info("Group {} has no users with a primary email. Returning done...", group.getId());
      return true;
    }

    return emailSent.get();
  }

  public Cache<ObjectId, Boolean> getForcedUpgradeNotNeededForTenantsCache() {
    return _forcedUpgradeNotNeededForTenantsCache;
  }

  public boolean isForcedUpgradeNotNeededForTenant(final ObjectId pTenantUniqueId) {
    return _forcedUpgradeNotNeededForTenantsCache.getIfPresent(pTenantUniqueId) != null;
  }

  public void setForcedUpgradeNotNeededForTenant(final ObjectId pTenantUniqueId) {
    _forcedUpgradeNotNeededForTenantsCache.put(pTenantUniqueId, true);
  }

  public boolean setMigrationHostname(final ObjectId pId, final String pMigrationHostname) {
    return _serverlessUpgradeToDedicatedStatusDao.setMigrationHostname(pId, pMigrationHostname);
  }

  @Override
  public <T extends BaseTenantUpgradeStatus> void incrementLongRunningTenantUpgradePromCounter(
      final T pTenantUpgradeStatus) {
    NDSPromMetricsSvc.incrementCounter(
        LONG_RUNNING_SERVERLESS_UPGRADE_TO_DEDICATED_COUNTER,
        pTenantUpgradeStatus.getGroupId().toString(),
        pTenantUpgradeStatus.getClusterName(),
        pTenantUpgradeStatus.getSourceTenantUniqueId().toString());
  }
}
