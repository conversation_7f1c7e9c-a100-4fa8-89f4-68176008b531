package com.xgen.svc.nds.model.ui;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.svc.mms.res.view.cluster.HostView;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

@JsonIgnoreProperties(ignoreUnknown = true)
public class HostClusterView {

  public static final String CLUSTER_ID_FIELD = "clusterId";
  public static final String GROUP_ID_FIELD = "groupId";
  public static final String TYPE_CODE_FIELD = "typeCode";
  public static final String NAME_FIELD = "name";
  public static final String CREATED_FIELD = "created";
  public static final String UPDATED_FIELD = "updated";
  public static final String HEARTBEAT_FIELD = "heartbeat";
  public static final String HOST_IDS_FIELD = "hostIds";
  public static final String HOSTS_FIELD = "hosts";
  public static final String REPLICA_SET_IDS_FIELD = "replicaSetIds";
  public static final String CONFIG_SERVER_REPLICA_SET_ID_FIELD = "configServerReplicaSetId";
  public static final String REPLICA_SETS_FIELD = "replicaSets";
  public static final String SHARD_IDS_FIELD = "shardIds";
  public static final String PARENT_CLUSTER_ID_FIELD = "parentClusterId";
  public static final String ACTIVE_FIELD = "active";
  public static final String BALANCER_STATUS_FIELD = "balancerStatus";

  @JsonProperty(CLUSTER_ID_FIELD)
  private ObjectId _clusterId;

  @JsonProperty(GROUP_ID_FIELD)
  private ObjectId _groupId;

  @JsonProperty(TYPE_CODE_FIELD)
  private int _typeCode;

  @JsonProperty(NAME_FIELD)
  private String _name;

  @JsonProperty(CREATED_FIELD)
  private Date _created;

  @JsonProperty(UPDATED_FIELD)
  private Date _updated;

  @JsonProperty(HEARTBEAT_FIELD)
  private Date _heartbeat;

  @JsonProperty(HOST_IDS_FIELD)
  private Set<String> _hostIds;

  @JsonProperty(HOSTS_FIELD)
  private List<HostView> _hosts;

  @JsonProperty(REPLICA_SET_IDS_FIELD)
  private Set<String> _replicaSetIds;

  @JsonProperty(REPLICA_SETS_FIELD)
  private Set<ReplicaSetView> _replicaSets;

  @JsonProperty(CONFIG_SERVER_REPLICA_SET_ID_FIELD)
  private String _configServerReplicaSetId;

  @JsonProperty(SHARD_IDS_FIELD)
  private Set<String> _shardIds;

  @JsonProperty(PARENT_CLUSTER_ID_FIELD)
  private ObjectId _parentClusterId;

  @JsonProperty(ACTIVE_FIELD)
  private boolean _active;

  @JsonProperty(BALANCER_STATUS_FIELD)
  private BalancerStatusView _balancerStatus;

  public HostClusterView() {}

  public HostClusterView(final HostCluster pHostCluster, final List<HostView> pHostViews) {
    _clusterId = pHostCluster.getClusterId();
    _groupId = pHostCluster.getGroupId();
    _typeCode = pHostCluster.getTypeCode();
    _name = pHostCluster.getName();
    _created = pHostCluster.getCreated();
    _updated = pHostCluster.getUpdated();
    _heartbeat = pHostCluster.getHeartbeat();
    _hostIds = pHostCluster.getHostIds();
    if (pHostViews != null) {
      _hosts = pHostViews;
    }
    _replicaSetIds = pHostCluster.getReplicaSetIds();
    if (pHostCluster.getReplicaSets() != null) {
      _replicaSets =
          pHostCluster.getReplicaSets().stream()
              .map(ReplicaSetView::new)
              .collect(Collectors.toSet());
    }
    _configServerReplicaSetId = pHostCluster.getConfigServerReplicaSetId();
    _shardIds = pHostCluster.getShardIds();
    _parentClusterId = pHostCluster.getParentClusterId();
    _active = pHostCluster.isActive();
    if (pHostCluster.getBalancerStatus() != null) {
      _balancerStatus = new BalancerStatusView(pHostCluster.getBalancerStatus());
    }
  }

  public ObjectId getClusterId() {
    return _clusterId;
  }

  public ObjectId getGroupId() {
    return _groupId;
  }

  public int getTypeCode() {
    return _typeCode;
  }

  public String getName() {
    return _name;
  }

  public Date getCreated() {
    return _created;
  }

  public Date getUpdated() {
    return _updated;
  }

  public Date getHeartbeat() {
    return _heartbeat;
  }

  public Set<String> getHostIds() {
    return _hostIds;
  }

  public List<HostView> getHosts() {
    return _hosts;
  }

  public Set<String> getReplicaSetIds() {
    return _replicaSetIds;
  }

  public Set<ReplicaSetView> getReplicaSets() {
    return _replicaSets;
  }

  public String getConfigServerReplicaSetId() {
    return _configServerReplicaSetId;
  }

  public Set<String> getShardIds() {
    return _shardIds;
  }

  public ObjectId getParentClusterId() {
    return _parentClusterId;
  }

  public boolean isActive() {
    return _active;
  }

  public BalancerStatusView getBalancerStatus() {
    return _balancerStatus;
  }
}
