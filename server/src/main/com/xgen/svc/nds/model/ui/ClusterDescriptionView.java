package com.xgen.svc.nds.model.ui;

import com.amazonaws.services.ec2.model.VolumeType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.cps.restore._public.ui.ServerlessBackupOptionsView;
import com.xgen.cloud.deployment._public.model.IndexConfig;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.AutoScalingView;
import com.xgen.cloud.nds.common._public.model.ConfigServerType;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.ProxyProtocolForPrivateLinkMode;
import com.xgen.cloud.nds.common._public.model.ReplicaSetScalingStrategy;
import com.xgen.cloud.nds.common._public.view.NDSLabelView;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex._public.model.FlexTenantProviderOptions;
import com.xgen.cloud.nds.flex._public.model.FlexTenantProviderOptions.Builder;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.AutoSharding;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterProvisionType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.DiskWarmingMode;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.InternalClusterRole;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.LogRetention;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ProcessRestartAllowedState;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.RestoreJobType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.RootCertType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.StorageSystem;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.ConfigServerManagementMode;
import com.xgen.cloud.nds.project._public.model.DeleteClusterReason;
import com.xgen.cloud.nds.project._public.model.EffectiveFields;
import com.xgen.cloud.nds.project._public.model.FlexTenantMigrationState;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.project._public.util.InstanceSizeUtil;
import com.xgen.cloud.nds.project._public.view.EmployeeAccessGrantView;
import com.xgen.cloud.nds.sampledataset._public.model.SampleDatasetLoadStatus.SampleDataset;
import com.xgen.cloud.nds.serverless._public.model.ServerlessBackupOptions;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.cloud.nds.serverless._public.model.ServerlessTenantProviderOptions;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessFreeMigrationStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeToDedicatedStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeStatus;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import com.xgen.cloud.search.decoupled.api._public.view.api.ApiSearchDeploymentRequestView;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ClusterDescriptionView {

  private static final Logger LOG = LoggerFactory.getLogger(ClusterDescriptionView.class);

  @JsonProperty("@provider")
  protected String _cloudProvider;

  @JsonProperty("name")
  protected String _name;

  @JsonProperty("groupId")
  protected ObjectId _groupId;

  @JsonProperty("uniqueId")
  private ObjectId _uniqueId;

  @JsonProperty("createDate")
  private Date _createDate;

  @JsonProperty("groupName")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private String _groupName;

  @JsonProperty("mongoDBVersion")
  private String _mongoDBVersion;

  @JsonProperty("mongoDBMajorVersion")
  private String _mongoDBMajorVersion;

  @JsonProperty("fixedMongoDBFCV")
  private String _fixedMongoDBFCV;

  @JsonProperty("fixedMongoDBFCVExpiration")
  private Date _fixedMongoDBFCVExpiration;

  @JsonProperty("fixedMongoDBFCVPinnedDate")
  private Date _fixedMongoDBFCVPinnedDate;

  @JsonProperty("clusterType")
  private String _clusterType;

  @JsonProperty("internalClusterRole")
  private String _internalClusterRole = InternalClusterRole.NONE.name();

  @JsonProperty("isMTM")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private Boolean _isMTM;

  @JsonProperty("isMTMSentinel")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private Boolean _isMTMSentinel;

  @JsonProperty(FieldDefs.REPLICATION_SPEC_LIST)
  protected List<ReplicationSpecView> _replicationSpecList;

  @JsonProperty(FieldDefs.MAX_INCOMING_CONNECTIONS)
  protected Integer _maxIncomingConns;

  @JsonProperty("diskSizeGB")
  protected Double _diskSizeGB;

  @JsonProperty("customerProvidedDiskSizeGB")
  protected Double _customerProvidedDiskSizeGB;

  @JsonProperty("useEffectiveClusterFields")
  protected EffectiveFields _useEffectiveClusterFields;

  @JsonProperty("mongoDBUriHosts")
  private String[] _mongoDBUriHosts;

  @JsonProperty("privateMongoDBUriHosts")
  private String[] _privateMongoDBUriHosts;

  @JsonProperty("privateSrvAddress")
  private String _privateSrvAddress;

  @JsonProperty("privateLinkMongoDBUriHosts")
  private Map<String, List<String>> _privateLinkMongoDBUriHosts;

  @JsonProperty("privateLinkSrvAddresses")
  private Map<String, String> _privateLinkSrvAddresses;

  @JsonProperty("mongoDBUriHostsLastUpdateDate")
  private Date _mongoDBUriHostsLastUpdateDate;

  @JsonProperty("backupEnabled")
  protected Boolean _backupEnabled;

  @JsonProperty("diskBackupEnabled")
  protected Boolean _diskBackupEnabled;

  @JsonProperty("pitEnabled")
  private Boolean _pitEnabled;

  @JsonProperty("state")
  private State _state;

  @JsonProperty("deleteAfterDate")
  private Date _deleteAfterDate;

  @JsonProperty("needsMongoDBConfigPublishAfter")
  private Date _needsMongoDBConfigPublishAfter;

  @JsonProperty("tenantUpgrading")
  private Boolean _tenantUpgrading;

  @JsonProperty("tenantUpgradingServerlessToDedicated")
  private Boolean _tenantUpgradingServerlessToDedicated;

  @JsonProperty("hasDedicatedMetricsAvailableForServerlessToDedicated")
  private Boolean _hasDedicatedMetricsAvailableForServerlessToDedicated;

  @JsonProperty("tenantDowngradingServerlessToFree")
  private Boolean _tenantDowngradingServerlessToFree;

  @JsonProperty("tenantAccessRevokedForPause")
  private Boolean _tenantAccessRevokedForPause;

  @JsonProperty("userNotifiedAboutPauseDate")
  private Date _userNotifiedAboutPauseDate;

  @JsonProperty("isPaused")
  private Boolean _isPaused;

  @JsonProperty("isUnderCompaction")
  private Boolean _isUnderCompaction;

  @JsonProperty("biConnector")
  private BiConnectorView _biConnector;

  @JsonProperty("dataProcessingRegion")
  private DataProcessingRegionView _dataProcessingRegion;

  @JsonProperty("lastUpdateDate")
  private Date _lastUpdateDate;

  @JsonProperty("srvAddress")
  private String _srvAddress;

  @JsonProperty("clusterTags")
  private Set<String> _clusterTags;

  @JsonProperty("geoSharding")
  private GeoShardingView _geoShardingView;

  @JsonProperty("encryptionAtRestProvider")
  protected EncryptionAtRestProviderView _encryptionAtRestProvider;

  @JsonProperty("isNvme")
  private Boolean _isNvme;

  @JsonProperty("labels")
  protected List<NDSLabelView> _labels;

  @JsonProperty("isMonitoringPaused")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  protected Boolean _isMonitoringPaused;

  @JsonProperty("deploymentItemName")
  protected String _deploymentItemName;

  @JsonProperty("deploymentClusterName")
  protected String _deploymentClusterName;

  @JsonProperty("forceReplicaSetReconfig")
  private Boolean _forceReplicaSetReconfig;

  @JsonProperty("acceptDataRisksAndForceReplicaSetReconfig")
  protected Date _acceptDataRisksAndForceReplicaSetReconfig;

  @JsonProperty("resurrectRequested")
  private Date _resurrectRequested;

  @JsonProperty("resurrectOptions")
  private ResurrectOptionsView _resurrectOptions;

  @JsonProperty("hostnameSubdomainLevel")
  protected String _hostnameSubdomainLevel;

  @JsonProperty("hostnameSchemeForAgents")
  protected String _hostnameSchemeForAgents;

  @JsonProperty("isCrossCloudCluster")
  protected Boolean _isCrossCloudCluster;

  @JsonProperty("isMongoDBVersionFixed")
  protected Boolean _isMongoDBVersionFixed;

  @JsonProperty("rootCertType")
  protected RootCertType _rootCertType;

  @JsonProperty("versionReleaseSystem")
  protected VersionReleaseSystem _versionReleaseSystem;

  @JsonProperty(value = "continuousDeliveryFCV", access = Access.READ_ONLY)
  private String _continuousDeliveryFCV;

  @JsonProperty(value = "endpointToLoadBalancedSRVConnectionURI")
  private Map<String, String> _endpointToLoadBalancedSRVConnectionURI;

  @JsonProperty("serverlessBackupOptions")
  protected ServerlessBackupOptionsView _serverlessBackupOptionsView;

  @JsonProperty("isFastProvisioned")
  protected boolean _isFastProvisioned;

  @JsonProperty("terminationProtectionEnabled")
  protected Boolean _terminationProtectionEnabled;

  @JsonProperty("needsSampleDataLoadAfter")
  protected Date _needsSampleDataLoadAfter;

  @JsonProperty("createSampleSearchIndex")
  protected boolean _createSampleSearchIndex;

  @JsonProperty("sampleDatasetToLoad")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  protected SampleDataset _sampleDatasetToLoad;

  @JsonProperty("replicaSetScalingStrategy")
  protected ReplicaSetScalingStrategy _replicaSetScalingStrategy;

  /**
   * If true, it will show a note about retaining snapshots on the cluster card while the cluster is
   * terminating. This is a UI-only field.
   */
  @JsonProperty("retainBackupsForDeleting")
  protected Boolean _retainBackupsForDeleting;

  @JsonProperty(FieldDefs.SEARCH_DEPLOYMENT_REQUEST_FIELD)
  protected ApiSearchDeploymentRequestView _searchDeploymentRequest;

  @JsonProperty("diskWarmingMode")
  protected DiskWarmingMode _diskWarmingMode;

  @JsonProperty("configServerType")
  private ConfigServerType _configServerType;

  @JsonProperty("configServerManagementMode")
  private ConfigServerManagementMode _configServerManagementMode;

  @JsonProperty("employeeAccessGrant")
  private EmployeeAccessGrantView _employeeAccessGrant;

  @JsonProperty("osPolicyVersion")
  private String _osPolicyVersion;

  @JsonProperty("redactClientLogData")
  private Boolean _redactClientLogData;

  @JsonProperty("shardsDraining")
  private List<String> _shardsDraining;

  // TODO: CLOUDP-289233  remove atlas proxy cluster level metrics flag
  @JsonProperty("hasClusterLevelMetrics")
  private boolean _hasClusterLevelMetrics;

  @JsonProperty("storageSystem")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private StorageSystem _storageSystem;

  @JsonIgnoreProperties("flexTenantMigrationState")
  private FlexTenantMigrationState _flexTenantMigrationState;

  @JsonProperty("freeFromServerless")
  private Boolean _freeFromServerless;

  @JsonProperty("advancedConfiguration")
  private AdvancedConfigurationView _advancedConfiguration;

  @JsonProperty("restoreJobIds")
  private List<ObjectId> _restoreJobIds;

  @JsonProperty("pendingIndexes")
  private List<IndexConfig> _pendingIndexes;

  @JsonProperty("fixedFeatureCompatibilityVersion")
  private FixedVersionView _fixedFeatureCompatibilityVersionView;

  @JsonProperty("fixedCpuArch")
  private FixedVersionView _fixedCpuArchView;

  @JsonProperty("fixedMongoDBVersion")
  private FixedVersionView _fixedMongoDBVersionView;

  @JsonProperty("fixedOs")
  private FixedVersionView _fixedOsView;

  @JsonProperty("fixedACMEProvider")
  private FixedVersionView _fixedACMEProviderView;

  @JsonProperty("bumperFileOverrides")
  private List<BumperFileOverrideView> _bumperFileOverrides;

  @JsonProperty("deleteRequested")
  private boolean _deleteRequested;

  @JsonProperty("deleteReason")
  private DeleteClusterReason _deleteReason;

  @JsonProperty("restoreJobType")
  private RestoreJobType _restoreJobType;

  @JsonProperty("deletedDate")
  private Date _deletedDate;

  @JsonProperty("ensureClusterConnectivityAfter")
  private Date _ensureClusterConnectivityAfter;

  @JsonProperty("needsMongoDBConfigPublishRestartAllowed")
  private ProcessRestartAllowedState _needsMongoDBConfigPublishRestartAllowed;

  @JsonProperty("pausedDate")
  private Date _pausedDate;

  @JsonProperty("logRetention")
  private LogRetention _logRetention;

  @JsonProperty("stopContinuousBackup")
  private boolean _stopContinuousBackup;

  @JsonProperty("forceReplicaSetReconfigHostsToSkip")
  private List<String> _forceReplicaSetReconfigHostsToSkip;

  @JsonProperty("replicaSetVersionOverride")
  private Integer _replicaSetVersionOverride;

  @JsonProperty("migrateFromAvailabilitySets")
  private Set<String> _migrateFromAvailabilitySets;

  @JsonProperty("loadBalancedHostname")
  private String _loadBalancedHostname;

  @JsonProperty("loadBalancedMeshHostname")
  private String _loadBalancedMeshHostname;

  @JsonProperty("cancelShardDrainRequested")
  private Date _cancelShardDrainRequested;

  @JsonProperty("osTunedFileOverrides")
  private OsTunedFileOverridesView _osTunedFileOverridesView;

  @JsonProperty("dnsPin")
  private String _dnsPin;

  @JsonProperty("clusterNamePrefix")
  private String _clusterNamePrefix;

  @JsonProperty("clusterProvisionType")
  private ClusterProvisionType _clusterProvisionType;

  @JsonProperty("needsServerlessSnapshotForPit")
  private Date _needsServerlessSnapshotForPit;

  @JsonProperty("needsEnvoySyncAfter")
  private Date _needsEnvoySyncAfter;

  @JsonProperty("lastDataValidationDate")
  private Date _lastDataValidationDate;

  @JsonProperty("lastDbCheckDate")
  private Date _lastDbCheckDate;

  @JsonProperty("needsDbCheckAfter")
  private Date _needsDbCheckAfter;

  @JsonProperty("dbCheckPreflightRetryCount")
  private int _dbCheckPreflightRetryCount;

  @JsonProperty("isCriticalOSPolicyRelease")
  private Boolean _isCriticalOSPolicyRelease;

  @JsonProperty("cpuSocketBinding")
  private List<Integer> _cpuSocketBinding;

  @JsonProperty("needsPrioritiesResetForPriorityTakeoverAfter")
  private Date _needsPrioritiesResetForPriorityTakeoverAfter;

  @JsonProperty("proxyProtocolForPrivateLinkMode")
  private ProxyProtocolForPrivateLinkMode _proxyProtocolForPrivateLinkMode;

  @JsonProperty("partnerIntegrationsData")
  @Nullable
  private PartnerIntegrationsData _partnerIntegrationsData;

  @JsonProperty("isServerlessTenantBlocked")
  private Boolean _isServerlessTenantBlocked;

  @JsonProperty("isWriteBlocked")
  private Boolean _isWriteBlocked;

  @JsonProperty("useAwsTimeBasedSnapshotCopyForFastInitialSync")
  private Boolean _useAwsTimeBasedSnapshotCopyForFastInitialSync;

  public static final String DEFAULT_TENANT_VERSION = "0.0.0.0";

  @JsonUnwrapped
  protected TenantOptionsView _cloudProviderOptions =
      // An empty options object by default when deserializing
      new TenantOptionsView();

  // Note: No @JsonProperty for now, only used for update view comparisons
  private AutoScalingMode _autoScalingMode;

  @JsonProperty("autoSharding")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private AutoSharding _autoSharding;

  public ClusterDescriptionView() {}

  public ClusterDescriptionView(final ClusterDescription pClusterDescription) {
    this(pClusterDescription, null, null);
  }

  public ClusterDescriptionView(
      final ClusterDescription pClusterDescription,
      final String pGroupName,
      final BaseTenantUpgradeStatus pTenantUpgradeStatus) {
    this(pClusterDescription, pGroupName, pTenantUpgradeStatus, null, false);
  }

  public ClusterDescriptionView(
      final ClusterDescription pClusterDescription,
      final String pGroupName,
      final BaseTenantUpgradeStatus pTenantUpgradeStatus,
      final Boolean pIsMonitoringPaused) {
    this(pClusterDescription, pGroupName, pTenantUpgradeStatus, pIsMonitoringPaused, false);
  }

  public ClusterDescriptionView(
      final ClusterDescription pClusterDescription,
      final String pGroupName,
      final BaseTenantUpgradeStatus pTenantUpgradeStatus,
      final Boolean pIsMonitoringPaused,
      final Boolean pIsServerlessTenantBlocked) {
    _isServerlessTenantBlocked = pIsServerlessTenantBlocked;
    _isMonitoringPaused = pIsMonitoringPaused;

    final Map<String, List<String>> privateUriHostsMap =
        new HashMap<>(pClusterDescription.getPrivateMongoDBUriHostsMap());
    final Map<String, String> privateSrvAddressMap =
        new HashMap<>(pClusterDescription.getPrivateSrvAddressMap());

    _cloudProvider = pClusterDescription.getCloudProviders().iterator().next().name();
    _name = pClusterDescription.getName();
    _groupId = pClusterDescription.getGroupId();
    _uniqueId = pClusterDescription.getUniqueId();
    _createDate = pClusterDescription.getCreateDate();
    _groupName = pGroupName;
    _mongoDBVersion = pClusterDescription.getMongoDBVersion().toString();
    _mongoDBMajorVersion = pClusterDescription.getMongoDBMajorVersion();
    _fixedMongoDBFCV =
        pClusterDescription
            .getFixedFeatureCompatibilityVersion()
            .map(FixedVersion::getVersion)
            .orElse(null);
    _fixedMongoDBFCVExpiration =
        pClusterDescription
            .getFixedFeatureCompatibilityVersion()
            .map(FixedVersion::getExpirationDate)
            .orElse(null);
    _fixedMongoDBFCVPinnedDate =
        pClusterDescription
            .getFixedFeatureCompatibilityVersion()
            .map(FixedVersion::getPinnedDate)
            .orElse(null);
    _diskSizeGB = pClusterDescription.getDiskSizeGB();
    _customerProvidedDiskSizeGB = pClusterDescription.getDiskSizeGB();
    _useEffectiveClusterFields = pClusterDescription.getUseEffectiveClusterFields();
    _mongoDBUriHosts = pClusterDescription.getMongoDBUriHosts();
    _privateMongoDBUriHosts = pClusterDescription.getPrivateMongoDBUriHosts();
    _privateSrvAddress = pClusterDescription.getPrivateSrvAddress();
    _privateLinkMongoDBUriHosts = privateUriHostsMap;
    _privateLinkSrvAddresses = privateSrvAddressMap;
    _mongoDBUriHostsLastUpdateDate = pClusterDescription.getMongoDBUriHostsLastUpdateDate();
    _backupEnabled = pClusterDescription.isBackupEnabled();
    _diskBackupEnabled = pClusterDescription.isDiskBackupEnabled();
    _deleteAfterDate = pClusterDescription.getDeleteAfterDate().orElse(null);
    _needsMongoDBConfigPublishAfter =
        pClusterDescription.getNeedsMongoDBConfigPublishAfter().orElse(null);
    _tenantUpgrading =
        pTenantUpgradeStatus != null
            && pTenantUpgradeStatus.getState() == TenantUpgradeStatus.State.WORKING;
    _tenantUpgradingServerlessToDedicated =
        pTenantUpgradeStatus != null
            && pTenantUpgradeStatus.getClass() == ServerlessUpgradeToDedicatedStatus.class;
    _hasDedicatedMetricsAvailableForServerlessToDedicated =
        pTenantUpgradeStatus instanceof ServerlessUpgradeToDedicatedStatus
            && ((ServerlessUpgradeToDedicatedStatus) pTenantUpgradeStatus)
                    .getTargetClusterUniqueId()
                != null;
    _tenantDowngradingServerlessToFree =
        pTenantUpgradeStatus != null
            && pTenantUpgradeStatus.getClass() == ServerlessFreeMigrationStatus.class;

    _clusterType = pClusterDescription.getClusterType().name();
    _internalClusterRole = pClusterDescription.getInternalClusterRole().name();
    _isMTM = pClusterDescription.isMTM();
    _isMTMSentinel = pClusterDescription.isMTMSentinel();
    _replicationSpecList =
        pClusterDescription.getReplicationSpecsWithShardData().stream()
            .map(
                replicationSpec ->
                    new ReplicationSpecView(
                        replicationSpec,
                        pClusterDescription.getDiskSizeGB(),
                        pClusterDescription.getCustomerProvidedDiskSizeGB()))
            .collect(Collectors.toList());
    _maxIncomingConns =
        pClusterDescription
            .getMaxInstanceSize(NodeType.ELECTABLE)
            .flatMap(NDSInstanceSize::getMaxIncomingConnections)
            .orElse(null);
    _isPaused = pClusterDescription.isPaused();
    _biConnector = new BiConnectorView(pClusterDescription.getBiConnector());
    _lastUpdateDate = pClusterDescription.getLastUpdateDate();
    _srvAddress = pClusterDescription.getSRVAddress().orElse(null);
    _geoShardingView = new GeoShardingView(pClusterDescription.getGeoSharding());
    _encryptionAtRestProvider =
        EncryptionAtRestProviderView.valueOf(pClusterDescription.getEncryptionAtRestProvider());
    _clusterTags =
        pClusterDescription.getClusterTags().stream()
            .map(ClusterDescription.ClusterTag::name)
            .collect(Collectors.toSet());
    _labels =
        pClusterDescription.getLabels().stream()
            .map(NDSLabelView::new)
            .collect(Collectors.toList());
    _isNvme = pClusterDescription.isNVMe(NodeType.ELECTABLE);
    _pitEnabled = pClusterDescription.isPitEnabled();
    _forceReplicaSetReconfig = pClusterDescription.getForceReplicaSetReconfig();
    _acceptDataRisksAndForceReplicaSetReconfig =
        pClusterDescription.getForceReplicaSetReconfigVersion().orElse(null);
    _resurrectRequested = pClusterDescription.getResurrectRequested().orElse(null);
    _resurrectOptions =
        pClusterDescription.getResurrectOptions().map(ResurrectOptionsView::new).orElse(null);
    _hostnameSubdomainLevel =
        pClusterDescription.getHostnameSubdomainLevel() == null
            ? ""
            : pClusterDescription.getHostnameSubdomainLevel().name();
    _hostnameSchemeForAgents =
        pClusterDescription.getHostnameSchemeForAgents().map(Enum::name).orElse("");
    _isCrossCloudCluster = pClusterDescription.isCrossCloudCluster();
    _isMongoDBVersionFixed = pClusterDescription.isMongoDBVersionFixed();
    _rootCertType = pClusterDescription.getRootCertType();
    _versionReleaseSystem = pClusterDescription.getVersionReleaseSystem();
    _continuousDeliveryFCV = pClusterDescription.getContinuousDeliveryFCV().orElse(null);
    _serverlessBackupOptionsView =
        pClusterDescription.isServerlessTenantCluster()
            ? new ServerlessBackupOptionsView(
                ((ServerlessBackupOptions) (pClusterDescription.getServerlessBackupOptions()))
                    .isServerlessContinuousBackupEnabled())
            : null;
    _isFastProvisioned =
        pClusterDescription.getClusterProvisionType().equals(ClusterProvisionType.FAST);
    _terminationProtectionEnabled = pClusterDescription.isTerminationProtectionEnabled();
    _needsSampleDataLoadAfter = pClusterDescription.getNeedsSampleDataLoadAfter();
    _createSampleSearchIndex = pClusterDescription.getCreateSampleSearchIndex();
    _sampleDatasetToLoad = pClusterDescription.getSampleDatasetToLoad().orElse(null);

    _retainBackupsForDeleting = null;
    _diskWarmingMode = pClusterDescription.getDiskWarmingMode().orElse(null);

    _replicaSetScalingStrategy = pClusterDescription.getReplicaSetScalingStrategy().orElse(null);

    _employeeAccessGrant =
        pClusterDescription.getEmployeeAccessGrant().map(EmployeeAccessGrantView::new).orElse(null);

    _osPolicyVersion = pClusterDescription.getOSPolicyVersion().orElse(null);

    _redactClientLogData = pClusterDescription.getRedactClientLogData().orElse(false);

    _shardsDraining = pClusterDescription.getShardsDraining();
    _flexTenantMigrationState = pClusterDescription.getFlexTenantMigrationState().orElse(null);
    _freeFromServerless = pClusterDescription.isFreeFromServerless();

    if (pClusterDescription.getGroupId() == null
        || pClusterDescription.getHostnameSchemeForAgents().isEmpty()) {
      // The above conditions can occur during a translation from a view to ClusterDescription
      // during cluster creation or update from the public API, and should never be the case for
      // when we are creating views for the UI / Public API from saved ClusterDescriptions.
      _deploymentItemName = null;
      _deploymentClusterName = null;
    } else {
      _deploymentItemName =
          pClusterDescription.getClusterType().isSharded()
              ? pClusterDescription.getDeploymentClusterName()
              : NDSDefaults.getReplicaSetNameForUnshardedCluster(pClusterDescription);
      _deploymentClusterName = pClusterDescription.getDeploymentClusterName();
    }

    _cloudProviderOptions = TenantOptionsView.fromClusterDescription(pClusterDescription);

    final boolean tenantResuming;
    if (pClusterDescription
        .getInstanceSizes(NodeType.ELECTABLE)
        .equals(Set.of(FreeInstanceSize.M0))) {
      _tenantAccessRevokedForPause =
          Optional.ofNullable(
                      pClusterDescription.getFreeTenantProviderOptions().getNdsAccessRevokedDate())
                  .isPresent()
              && !pClusterDescription.isPaused();
      tenantResuming =
          ((FreeTenantProviderOptions) pClusterDescription.getFreeTenantProviderOptions())
              .isNeedsUnpauseTenantRestore();
      _userNotifiedAboutPauseDate =
          Optional.ofNullable(
                  pClusterDescription
                      .getFreeTenantProviderOptions()
                      .getUserNotifiedAboutPauseDate())
              .orElse(null);
      _isUnderCompaction =
          ((FreeTenantProviderOptions) pClusterDescription.getFreeTenantProviderOptions())
              .isUnderCompaction();
    } else {
      _tenantAccessRevokedForPause = false;
      tenantResuming = false;
      _userNotifiedAboutPauseDate = null;
      _isUnderCompaction = false;
    }

    if (pClusterDescription.getClusterType().isSharded()) {
      ShardedClusterDescription shardedClusterDescription =
          (ShardedClusterDescription) pClusterDescription;
      _endpointToLoadBalancedSRVConnectionURI =
          shardedClusterDescription.getShardedLoadBalancedSRVAddressMap();
    }

    if (_tenantUpgrading || _tenantAccessRevokedForPause) {
      _state = State.UPDATING;
      return;
    }

    if (tenantResuming) {
      if (_mongoDBUriHosts.length == 0) {
        _state = State.CREATING;
      } else {
        _state = State.UPDATING;
      }
      return;
    }

    switch (pClusterDescription.getState()) {
      case IDLE:
        _state = State.IDLE;
        break;
      case DELETED:
        _state = State.DELETED;
        break;
      case WORKING:
        if (pClusterDescription.isDeleteRequested()) {
          _state = State.DELETING;
        } else if (_mongoDBUriHosts.length == 0) {
          _state = State.CREATING;
        } else {
          _state = State.UPDATING;
        }
        break;
      case REPAIRING:
        _state = State.REPAIRING;
        break;
    }
    if (pClusterDescription.getClusterType().isSharded()) {
      _configServerType = ((ShardedClusterDescription) pClusterDescription).getConfigServerType();
    } else {
      _configServerType = null;
    }

    if (pClusterDescription.getClusterType().isSharded()) {
      _configServerManagementMode = pClusterDescription.getConfigServerManagementMode();
    } else {
      // hide this field in the UI/API for non-sharded clusters
      _configServerManagementMode = null;
    }
    _hasClusterLevelMetrics = pClusterDescription.isServerlessTenantCluster();
    _autoScalingMode = pClusterDescription.getAutoScalingMode();
    _restoreJobIds = pClusterDescription.getRestoreJobIds();
    _pendingIndexes = pClusterDescription.getPendingIndexes();
    _fixedFeatureCompatibilityVersionView =
        pClusterDescription
            .getFixedFeatureCompatibilityVersion()
            .map(FixedVersionView::new)
            .orElse(null);
    _fixedCpuArchView =
        pClusterDescription.getFixedCpuArch().map(FixedVersionView::new).orElse(null);
    _fixedMongoDBVersionView =
        pClusterDescription.getFixedMongoDBVersion().map(FixedVersionView::new).orElse(null);
    _fixedOsView = pClusterDescription.getFixedOs().map(FixedVersionView::new).orElse(null);
    _fixedACMEProviderView =
        pClusterDescription.getFixedACMEProvider().map(FixedVersionView::new).orElse(null);
    _bumperFileOverrides =
        pClusterDescription.getBumperFileOverrides().stream()
            .map(BumperFileOverrideView::new)
            .collect(Collectors.toList());
    _deleteRequested = pClusterDescription.isDeleteRequested();
    _deleteReason = pClusterDescription.getDeleteReason().orElse(null);
    _restoreJobType = pClusterDescription.getRestoreJobType().orElse(null);
    _deletedDate = pClusterDescription.getDeletedDate().orElse(null);
    _ensureClusterConnectivityAfter =
        pClusterDescription.getEnsureClusterConnectivityAfter().orElse(null);
    _needsMongoDBConfigPublishRestartAllowed =
        pClusterDescription.getNeedsMongoDBConfigPublishRestartAllowed();
    _pausedDate = pClusterDescription.getPausedDate().orElse(null);
    _logRetention = pClusterDescription.getLogRetention();
    _stopContinuousBackup = pClusterDescription.isStopContinuousBackup();
    _forceReplicaSetReconfigHostsToSkip =
        pClusterDescription.getForceReplicaSetReconfigHostsToSkip();
    _replicaSetVersionOverride = pClusterDescription.getReplicaSetVersionOverride().orElse(null);
    _migrateFromAvailabilitySets = pClusterDescription.getMigrateFromAvailabilitySets();
    _loadBalancedHostname = pClusterDescription.getLoadBalancedHostname().orElse(null);
    _loadBalancedMeshHostname = pClusterDescription.getLoadBalancedMeshHostname().orElse(null);
    _cancelShardDrainRequested = pClusterDescription.getCancelShardDrainRequested().orElse(null);
    _osTunedFileOverridesView =
        new OsTunedFileOverridesView(
            pClusterDescription.getOsTunedFileOverrides().getShardOverrides(),
            pClusterDescription.getOsTunedFileOverrides().getConfigOverrides(),
            null);
    _dnsPin = pClusterDescription.getDnsPin();
    _clusterNamePrefix = pClusterDescription.getClusterNamePrefix();
    _clusterProvisionType = pClusterDescription.getClusterProvisionType();
    _needsServerlessSnapshotForPit = pClusterDescription.getNeedsServerlessSnapshotForPit();
    _needsEnvoySyncAfter = pClusterDescription.getNeedsEnvoySyncAfter().orElse(null);
    _lastDataValidationDate = pClusterDescription.getLastDataValidationDate();
    _lastDbCheckDate = pClusterDescription.getLastDbCheckDate();
    _needsDbCheckAfter = pClusterDescription.getNeedsDbCheckAfter();
    _dbCheckPreflightRetryCount = pClusterDescription.getDbCheckPreflightRetryCount();
    _isCriticalOSPolicyRelease = pClusterDescription.getIsCriticalOSPolicyRelease().orElse(null);
    _cpuSocketBinding = pClusterDescription.getCpuSocketBinding();
    _needsPrioritiesResetForPriorityTakeoverAfter =
        pClusterDescription.getNeedsPrioritiesResetForPriorityTakeoverAfter().orElse(null);
    _proxyProtocolForPrivateLinkMode =
        pClusterDescription
            .getProxyProtocolForPrivateLinkMode()
            .orElse(ProxyProtocolForPrivateLinkMode.STANDARD);
    _partnerIntegrationsData = pClusterDescription.getPartnerIntegrationsData().orElse(null);
    _isWriteBlocked = pClusterDescription.isWriteBlocked();
    _useAwsTimeBasedSnapshotCopyForFastInitialSync =
        pClusterDescription.getUseAwsTimeBasedSnapshotCopyForFastInitialSync();
  }

  private ClusterDescriptionView(
      final String pCloudProvider,
      final String pName,
      final ObjectId pGroupId,
      final ObjectId pUniqueId,
      final Date pCreateDate,
      final String pGroupName,
      final String pMongoDBVersion,
      final String pMongoDBMajorVersion,
      final String pFixedMongoDBFCV,
      final Date pFixedMongoDBFCVExpiration,
      final Date pFixedMongoDBFCVPinnedDate,
      final String pClusterType,
      final String pInternalClusterRole,
      final Boolean pIsMTM,
      final Boolean pIsMTMSentinel,
      final List<ReplicationSpecView> pReplicationSpecList,
      final Integer pMaxIncomingConns,
      final Double pDiskSizeGB,
      final String[] pMongoDBUriHosts,
      final String[] pPrivateMongoDBUriHosts,
      final String pPrivateSrvAddress,
      final Map<String, List<String>> pPrivateLinkMongoDBUriHosts,
      final Map<String, String> pPrivateLinkSrvAddresses,
      final Date pMongoDBUriHostsLastUpdateDate,
      final Boolean pBackupEnabled,
      final Boolean pDiskBackupEnabled,
      final Boolean pPitEnabled,
      final State pState,
      final Date pDeleteAfterDate,
      final Date pNeedsMongoDBConfigPublishAfter,
      final Boolean pTenantUpgrading,
      final Boolean pTenantUpgradingServerlessToDedicated,
      final Boolean pIsServerlessTenantBlocked,
      final Boolean pHasDedicatedMetricsAvailableForServerlessToDedicated,
      final Boolean pTenantDowngradingServerlessToFree,
      final Boolean pTenantAccessRevokedForPause,
      final Date pUserNotifiedAboutPauseDate,
      final Boolean pIsPaused,
      final Boolean pIsUnderCompaction,
      final BiConnectorView pBiConnector,
      final DataProcessingRegionView pDataProcessingRegion,
      final Date pLastUpdateDate,
      final String pSrvAddress,
      final Set<String> pClusterTags,
      final GeoShardingView pGeoShardingView,
      final EncryptionAtRestProviderView pEncryptionAtRestProvider,
      final Boolean pIsNvme,
      final List<NDSLabelView> pLabels,
      final Boolean pIsMonitoringPaused,
      final String pDeploymentItemName,
      final String pDeploymentClusterName,
      final Boolean pForceReplicaSetReconfig,
      final Date pAcceptDataRisksAndForceReplicaSetReconfig,
      final Date pResurrectRequested,
      final ResurrectOptionsView pResurrectOptions,
      final String pHostnameSubdomainLevel,
      final String pHostnameSchemeForAgents,
      final Boolean pIsCrossCloudCluster,
      final Boolean pIsMongoDBVersionFixed,
      final RootCertType pRootCertType,
      final VersionReleaseSystem pVersionReleaseSystem,
      final String pContinuousDeliveryFCV,
      final Map<String, String> pEndpointToLoadBalancedSRVConnectionURI,
      final ServerlessBackupOptionsView pServerlessBackupOptionsView,
      final boolean pIsFastProvisioned,
      final Boolean pTerminationProtectionEnabled,
      final Date pNeedsSampleDataLoadAfter,
      final Boolean pCreateSampleSearchIndex,
      final SampleDataset pSampleDatasetToLoad,
      final Boolean pRetainBackupsForDeleting,
      final ApiSearchDeploymentRequestView pSearchDeploymentRequest,
      final DiskWarmingMode pDiskWarmingMode,
      final ConfigServerType pConfigServerType,
      final ConfigServerManagementMode pConfigServerManagementMode,
      final TenantOptionsView pCloudProviderOptions,
      final ReplicaSetScalingStrategy pReplicaSetScalingStrategy,
      final EmployeeAccessGrantView pEmployeeAccessGrant,
      final String pOSPolicyVersion,
      final Boolean pRedactClientLogData,
      final List<String> pShardsDraining,
      final FlexTenantMigrationState pFlexTenantMigrationState,
      final boolean pHasClusterLevelMetrics,
      final StorageSystem pStorageSystem,
      final AutoScalingMode pAutoScalingMode,
      final Boolean pFreeFromServerless,
      final List<ObjectId> pRestoreJobIds,
      final List<IndexConfig> pPendingIndexes,
      final FixedVersionView pFixedFeatureCompatibilityVersionView,
      final FixedVersionView pFixedCpuArchView,
      final FixedVersionView pFixedMongoDBVersionView,
      final FixedVersionView pFixedOsView,
      final FixedVersionView pFixedACMEProviderView,
      final List<BumperFileOverrideView> pBumperFileOverrides,
      final boolean pDeleteRequested,
      final DeleteClusterReason pDeleteReason,
      final RestoreJobType pRestoreJobType,
      final Date pDeletedDate,
      final Date pEnsureClusterConnectivityAfter,
      final ProcessRestartAllowedState pNeedsMongoDBConfigPublishRestartAllowed,
      final Date pPausedDate,
      final LogRetention pLogRetention,
      final boolean pStopContinuousBackup,
      final List<String> pForceReplicaSetReconfigHostsToSkip,
      final Integer pReplicaSetVersionOverride,
      final Set<String> pMigrateFromAvailabilitySets,
      final String pLoadBalancedHostname,
      final String pLoadBalancedMeshHostname,
      final Date pCancelShardDrainRequested,
      final OsTunedFileOverridesView pOsTunedFileOverridesView,
      final String pDnsPin,
      final String pClusterNamePrefix,
      final ClusterProvisionType pClusterProvisionType,
      final Date pNeedsServerlessSnapshotForPit,
      final Date pNeedsEnvoySyncAfter,
      final Date pLastDataValidationDate,
      final Date pLastDbCheckDate,
      final Date pNeedsDbCheckAfter,
      final int pDbCheckPreflightRetryCount,
      final Boolean pIsCriticalOSPolicyRelease,
      final List<Integer> pCpuSocketBinding,
      final Date pNeedsPrioritiesResetForPriorityTakeoverAfter,
      final ProxyProtocolForPrivateLinkMode pProxyProtocolForPrivateLinkMode,
      final @Nullable PartnerIntegrationsData pPartnerIntegrationsData,
      final Boolean pIsWriteBlocked,
      final AutoSharding pAutoSharding,
      final Boolean pUseAwsTimeBasedSnapshotCopyForFastInitialSync,
      final Double pCustomerProvidedDiskSizeGB,
      final EffectiveFields pUseEffectiveClusterFields) {
    _cloudProvider = pCloudProvider;
    _name = pName;
    _groupId = pGroupId;
    _uniqueId = pUniqueId;
    _createDate = pCreateDate;
    _groupName = pGroupName;
    _mongoDBVersion = pMongoDBVersion;
    _mongoDBMajorVersion = pMongoDBMajorVersion;
    _fixedMongoDBFCV = pFixedMongoDBFCV;
    _fixedMongoDBFCVExpiration = pFixedMongoDBFCVExpiration;
    _fixedMongoDBFCVPinnedDate = pFixedMongoDBFCVPinnedDate;
    _clusterType = pClusterType;
    _internalClusterRole = pInternalClusterRole;
    _isMTM = pIsMTM;
    _isMTMSentinel = pIsMTMSentinel;
    _replicationSpecList = pReplicationSpecList;
    _maxIncomingConns = pMaxIncomingConns;
    _diskSizeGB = pDiskSizeGB;
    _mongoDBUriHosts = pMongoDBUriHosts;
    _privateMongoDBUriHosts = pPrivateMongoDBUriHosts;
    _privateSrvAddress = pPrivateSrvAddress;
    _privateLinkMongoDBUriHosts = pPrivateLinkMongoDBUriHosts;
    _privateLinkSrvAddresses = pPrivateLinkSrvAddresses;
    _mongoDBUriHostsLastUpdateDate = pMongoDBUriHostsLastUpdateDate;
    _backupEnabled = pBackupEnabled;
    _diskBackupEnabled = pDiskBackupEnabled;
    _pitEnabled = pPitEnabled;
    _state = pState;
    _deleteAfterDate = pDeleteAfterDate;
    _needsMongoDBConfigPublishAfter = pNeedsMongoDBConfigPublishAfter;
    _tenantUpgrading = pTenantUpgrading;
    _isServerlessTenantBlocked = pIsServerlessTenantBlocked;
    _tenantUpgradingServerlessToDedicated = pTenantUpgradingServerlessToDedicated;
    _hasDedicatedMetricsAvailableForServerlessToDedicated =
        pHasDedicatedMetricsAvailableForServerlessToDedicated;
    _tenantDowngradingServerlessToFree = pTenantDowngradingServerlessToFree;
    _tenantAccessRevokedForPause = pTenantAccessRevokedForPause;
    _userNotifiedAboutPauseDate = pUserNotifiedAboutPauseDate;
    _isPaused = pIsPaused;
    _isUnderCompaction = pIsUnderCompaction;
    _biConnector = pBiConnector;
    _dataProcessingRegion = pDataProcessingRegion;
    _lastUpdateDate = pLastUpdateDate;
    _srvAddress = pSrvAddress;
    _clusterTags = pClusterTags;
    _geoShardingView = pGeoShardingView;
    _encryptionAtRestProvider = pEncryptionAtRestProvider;
    _isNvme = pIsNvme;
    _labels = pLabels;
    _isMonitoringPaused = pIsMonitoringPaused;
    _deploymentItemName = pDeploymentItemName;
    _deploymentClusterName = pDeploymentClusterName;
    _forceReplicaSetReconfig = pForceReplicaSetReconfig;
    _acceptDataRisksAndForceReplicaSetReconfig = pAcceptDataRisksAndForceReplicaSetReconfig;
    _resurrectRequested = pResurrectRequested;
    _resurrectOptions = pResurrectOptions;
    _hostnameSubdomainLevel = pHostnameSubdomainLevel;
    _hostnameSchemeForAgents = pHostnameSchemeForAgents;
    _isCrossCloudCluster = pIsCrossCloudCluster;
    _isMongoDBVersionFixed = pIsMongoDBVersionFixed;
    _rootCertType = pRootCertType;
    _versionReleaseSystem = pVersionReleaseSystem;
    _continuousDeliveryFCV = pContinuousDeliveryFCV;
    _endpointToLoadBalancedSRVConnectionURI = pEndpointToLoadBalancedSRVConnectionURI;
    _serverlessBackupOptionsView = pServerlessBackupOptionsView;
    _isFastProvisioned = pIsFastProvisioned;
    _terminationProtectionEnabled = pTerminationProtectionEnabled;
    _needsSampleDataLoadAfter = pNeedsSampleDataLoadAfter;
    _createSampleSearchIndex = pCreateSampleSearchIndex;
    _sampleDatasetToLoad = pSampleDatasetToLoad;
    _retainBackupsForDeleting = pRetainBackupsForDeleting;
    _searchDeploymentRequest = pSearchDeploymentRequest;
    _diskWarmingMode = pDiskWarmingMode;
    _configServerType = pConfigServerType;
    _configServerManagementMode = pConfigServerManagementMode;
    _cloudProviderOptions = pCloudProviderOptions;
    _replicaSetScalingStrategy = pReplicaSetScalingStrategy;
    _employeeAccessGrant = pEmployeeAccessGrant;
    _osPolicyVersion = pOSPolicyVersion;
    _redactClientLogData = pRedactClientLogData;
    _shardsDraining = pShardsDraining;
    _flexTenantMigrationState = pFlexTenantMigrationState;
    _hasClusterLevelMetrics = pHasClusterLevelMetrics;
    _storageSystem = pStorageSystem;
    _autoScalingMode = pAutoScalingMode;
    _freeFromServerless = pFreeFromServerless;
    _restoreJobIds = pRestoreJobIds;
    _pendingIndexes = pPendingIndexes;
    _fixedFeatureCompatibilityVersionView = pFixedFeatureCompatibilityVersionView;
    _fixedCpuArchView = pFixedCpuArchView;
    _fixedMongoDBVersionView = pFixedMongoDBVersionView;
    _fixedOsView = pFixedOsView;
    _fixedACMEProviderView = pFixedACMEProviderView;
    _bumperFileOverrides = pBumperFileOverrides;
    _deleteRequested = pDeleteRequested;
    _deleteReason = pDeleteReason;
    _restoreJobType = pRestoreJobType;
    _deletedDate = pDeletedDate;
    _ensureClusterConnectivityAfter = pEnsureClusterConnectivityAfter;
    _needsMongoDBConfigPublishRestartAllowed = pNeedsMongoDBConfigPublishRestartAllowed;
    _pausedDate = pPausedDate;
    _logRetention = pLogRetention;
    _stopContinuousBackup = pStopContinuousBackup;
    _forceReplicaSetReconfigHostsToSkip = pForceReplicaSetReconfigHostsToSkip;
    _replicaSetVersionOverride = pReplicaSetVersionOverride;
    _migrateFromAvailabilitySets = pMigrateFromAvailabilitySets;
    _loadBalancedHostname = pLoadBalancedHostname;
    _loadBalancedMeshHostname = pLoadBalancedMeshHostname;
    _cancelShardDrainRequested = pCancelShardDrainRequested;
    _osTunedFileOverridesView = pOsTunedFileOverridesView;
    _dnsPin = pDnsPin;
    _clusterNamePrefix = pClusterNamePrefix;
    _clusterProvisionType = pClusterProvisionType;
    _needsServerlessSnapshotForPit = pNeedsServerlessSnapshotForPit;
    _needsEnvoySyncAfter = pNeedsEnvoySyncAfter;
    _lastDataValidationDate = pLastDataValidationDate;
    _lastDbCheckDate = pLastDbCheckDate;
    _needsDbCheckAfter = pNeedsDbCheckAfter;
    _dbCheckPreflightRetryCount = pDbCheckPreflightRetryCount;
    _isCriticalOSPolicyRelease = pIsCriticalOSPolicyRelease;
    _cpuSocketBinding = pCpuSocketBinding;
    _needsPrioritiesResetForPriorityTakeoverAfter = pNeedsPrioritiesResetForPriorityTakeoverAfter;
    _proxyProtocolForPrivateLinkMode = pProxyProtocolForPrivateLinkMode;
    _partnerIntegrationsData = pPartnerIntegrationsData;
    _isWriteBlocked = pIsWriteBlocked;
    _useEffectiveClusterFields = pUseEffectiveClusterFields;
    _customerProvidedDiskSizeGB = pCustomerProvidedDiskSizeGB;
    _autoSharding = pAutoSharding;
    _useAwsTimeBasedSnapshotCopyForFastInitialSync = pUseAwsTimeBasedSnapshotCopyForFastInitialSync;
  }

  protected void setDefaults(final String pMongoDBVersion) {
    _name = NDSDefaults.CLUSTER_NAME;
    _groupId = null;
    _uniqueId = null;
    _groupName = null;
    _mongoDBVersion = pMongoDBVersion;
    _mongoDBMajorVersion =
        pMongoDBVersion == null
            ? null
            : VersionUtils.Version.fromString(pMongoDBVersion).getMajorVersionString();
    _fixedMongoDBFCV = null;
    _fixedMongoDBFCVExpiration = null;
    _fixedMongoDBFCVPinnedDate = null;
    _diskSizeGB = NDSDefaults.DISK_SIZE_GB;
    _customerProvidedDiskSizeGB = _diskSizeGB;
    _useEffectiveClusterFields = EffectiveFields.DISABLED;
    _clusterType = ClusterDescription.ClusterType.REPLICASET.name();
    _internalClusterRole = InternalClusterRole.NONE.name();
    _isMTM = false;
    _isMTMSentinel = false;
    _mongoDBUriHosts = new String[0];
    _mongoDBUriHostsLastUpdateDate = new Date();
    _backupEnabled = false;
    _diskBackupEnabled = false;
    _biConnector = BiConnectorView.getDefaultBiConnectorView();
    _state = State.IDLE;
    _isPaused = false;
    _srvAddress = null;
    _clusterTags = Collections.emptySet();
    _geoShardingView = GeoShardingView.getDefaultGeoShardingView();
    _encryptionAtRestProvider = EncryptionAtRestProviderView.NONE;
    _isNvme = false;
    _pitEnabled = false;
    _labels = Collections.emptyList();
    _deploymentItemName = null;
    _serverlessBackupOptionsView = null;
    _terminationProtectionEnabled = NDSDefaults.TERMINATION_PROTECTION_ENABLED;
    _needsSampleDataLoadAfter = null;
    _createSampleSearchIndex = false;
    _sampleDatasetToLoad = null;
    _retainBackupsForDeleting = null;
    _isUnderCompaction = false;
    _configServerType = null;
    _configServerManagementMode = null;
    _redactClientLogData = false;
    _shardsDraining = Collections.emptyList();
    _flexTenantMigrationState = null;
    _hasClusterLevelMetrics = false;
    _storageSystem = StorageSystem.LOCAL;
    _freeFromServerless = false;
    _restoreJobIds = Collections.emptyList();
    _pendingIndexes = Collections.emptyList();
    _fixedFeatureCompatibilityVersionView = null;
    _fixedCpuArchView = null;
    _fixedMongoDBVersionView = null;
    _fixedOsView = null;
    _fixedACMEProviderView = null;
    _bumperFileOverrides = Collections.emptyList();
    _deleteRequested = false;
    _deleteReason = null;
    _restoreJobType = null;
    _deletedDate = new Date();
    _ensureClusterConnectivityAfter = new Date();
    _needsMongoDBConfigPublishRestartAllowed = ProcessRestartAllowedState.NONE;
    _pausedDate = new Date();
    _logRetention = null;
    _stopContinuousBackup = false;
    _forceReplicaSetReconfigHostsToSkip = Collections.emptyList();
    _replicaSetVersionOverride = null;
    _migrateFromAvailabilitySets = Collections.emptySet();
    _loadBalancedHostname = null;
    _loadBalancedMeshHostname = null;
    _cancelShardDrainRequested = new Date();
    _osTunedFileOverridesView = null;
    _dnsPin = null;
    _clusterNamePrefix = null;
    _clusterProvisionType = null;
    _needsServerlessSnapshotForPit = new Date();
    _needsEnvoySyncAfter = new Date();
    _lastDataValidationDate = new Date();
    _lastDbCheckDate = new Date();
    _needsDbCheckAfter = new Date();
    _dbCheckPreflightRetryCount = 0;
    _isCriticalOSPolicyRelease = false;
    _cpuSocketBinding = Collections.emptyList();
    _needsPrioritiesResetForPriorityTakeoverAfter = new Date();
    _proxyProtocolForPrivateLinkMode = ProxyProtocolForPrivateLinkMode.STANDARD;
    _partnerIntegrationsData = null;
    _useAwsTimeBasedSnapshotCopyForFastInitialSync =
        NDSDefaults.USE_AWS_TIME_BASED_SNAPSHOT_COPY_FOR_FAST_INITIAL_SYNC;
  }

  protected void setReplicationSpecViews(final List<ReplicationSpecView> pReplicationSpecs) {
    _replicationSpecList = pReplicationSpecs;
  }

  public void setReplicationSpecList(final List<ReplicationSpecView> pSpecs) {
    _replicationSpecList = pSpecs;
  }

  public void setGeoShardingView(final GeoShardingView pGeoShardingView) {
    _geoShardingView = pGeoShardingView;
  }

  public void setNeedsMongoDBConfigPublishAfter() {
    _needsMongoDBConfigPublishAfter = new Date();
  }

  public void setNeedsSampleDataLoadAfter(Date date) {
    _needsSampleDataLoadAfter = date;
  }

  public void setCreateSampleSearchIndex(boolean createSampleSearchIndex) {
    _createSampleSearchIndex = createSampleSearchIndex;
  }

  public void setSampleDatasetToLoad(SampleDataset sampleDataset) {
    _sampleDatasetToLoad = sampleDataset;
  }

  public void setUniqueId(final ObjectId uniqueId) {
    _uniqueId = uniqueId;
  }

  public ReplicationSpecView getReplicationSpecViewById(final ObjectId pSpecId) {
    return getReplicationSpecList().stream()
        .filter(spec -> spec.getId().equals(pSpecId))
        .findFirst()
        .orElse(null);
  }

  public List<ReplicationSpecView> getReplicationSpecViewsByZone(final String pZoneName) {
    return getReplicationSpecList().stream()
        .filter(spec -> spec.getZoneName().equals(pZoneName))
        .collect(Collectors.toList());
  }

  public List<ReplicationSpecView> getReplicationSpecViewsInZone(final ObjectId pZoneId) {
    return getReplicationSpecList().stream()
        .filter(spec -> spec.getZoneId().equals(pZoneId))
        .collect(Collectors.toList());
  }

  public void setPITEnabled(final boolean pPITEnabled) {
    _pitEnabled = pPITEnabled;
  }

  public void setPaused(final boolean pIsPaused) {
    _isPaused = pIsPaused;
  }

  public ApiSearchDeploymentRequestView getSearchDeploymentRequest() {
    return _searchDeploymentRequest;
  }

  public FreeTenantProviderOptions getFreeCloudProviderOptions(
      final FreeInstanceSize pInstanceSize, final Date pNow) {
    final FreeTenantProviderOptions.Builder builder = new FreeTenantProviderOptions.Builder();

    final FreeInstanceSize.LimitsProfile limitsProfile =
        Optional.ofNullable(getLimitsProfile()).orElse(FreeInstanceSize.LimitsProfile.NORMAL);

    // A user does not get to configure these values so we should always
    // create cluster descriptions with the default values for the
    // instance size.
    builder.setLimitsProfile(limitsProfile);
    builder.setConnectionLimit(pInstanceSize.getMaxIncomingConnections(limitsProfile));
    builder.setOperationsPerSecondLimit(pInstanceSize.getMaxOperationsPerSecond(limitsProfile));
    builder.setDatabaseLimit(pInstanceSize.getMaxDatabases());
    builder.setCollectionLimit(pInstanceSize.getMaxCollections());
    builder.setGBPerWeekInLimit(pInstanceSize.getMaxGBPerWeekIn());
    builder.setGBPerWeekOutLimit(pInstanceSize.getMaxGBPerWeekOut(limitsProfile));
    builder.setThrottledKBPerSecondLimit(pInstanceSize.getThrottledKBPerSecond());
    builder.setQueryUtilizationBucketWidthSeconds(
        pInstanceSize.getQueryUtilizationBucketWidthSeconds());
    builder.setQueryUtilizationWindowLengthSeconds(
        pInstanceSize.getQueryUtilizationWindowLengthSeconds());
    builder.setQueryUtilizationTimeThreshold(pInstanceSize.getQueryUtilizationTimeThreshold());
    builder.setQueryUtilizationMaxSleepTimeSeconds(
        pInstanceSize.getQueryUtilizationMaxSleepTimeSeconds());

    final boolean shouldEnableTenantBackup = !pInstanceSize.equals(FreeInstanceSize.M0);
    builder.setNextBackupDate(shouldEnableTenantBackup ? DateUtils.addHours(pNow, 24) : null);
    builder.setTenantBackupEnabled(shouldEnableTenantBackup);

    return builder.build();
  }

  public ServerlessTenantProviderOptions getServerlessCloudProviderOptions() {
    final TenantOptionsView providerOptionsView = _cloudProviderOptions;

    final ServerlessInstanceSize instanceSize =
        ServerlessInstanceSize.fromInstanceSize(getInstanceSize());

    return new ServerlessTenantProviderOptions.Builder()
        .setInstanceSize(instanceSize)
        .setDiskSizeGBLimit(instanceSize.getMaxAllowedDiskSizeGBForServerless())
        .setConnectionLimit(instanceSize.getMaxIncomingConnections().get())
        .setDatabaseLimit(instanceSize.getMaxDatabases())
        .setCollectionLimit(instanceSize.getMaxCollections())
        .setExcludeFromPool(providerOptionsView._excludeFromPool)
        //        .setAccessRevokedTemporarily(false)
        .build();
  }

  public FlexTenantProviderOptions getFlexCloudProviderOptions(final Date pNow) {
    final FlexInstanceSize instanceSize = FlexInstanceSize.fromInstanceSize(getInstanceSize());

    return new Builder()
        .setConnectionLimit(instanceSize.getMaxIncomingConnections().orElseThrow())
        .setCollectionLimit(instanceSize.getMaxCollections())
        .setOperationsPerSecondLimit(instanceSize.getMaxOperationsPerSecond())
        .setDatabaseLimit(instanceSize.getMaxDatabases())
        .setDiskSizeGBLimit(instanceSize.getDefaultDiskSizeGB())
        .setNextBackupDate(DateUtils.addHours(pNow, 24))
        .build();
  }

  public Optional<FlexTenantMigrationState> getFlexTenantMigrationState() {
    return Optional.ofNullable(_flexTenantMigrationState);
  }

  public Boolean isFreeFromServerless() {
    return Optional.ofNullable(_freeFromServerless).orElse(false);
  }

  public Optional<Map<String, String>> getEndpointToLoadBalancedSRVConnectionURI() {
    return Optional.ofNullable(_endpointToLoadBalancedSRVConnectionURI);
  }

  public ServerlessBackupOptionsView getServerlessBackupOptionsView() {
    return _serverlessBackupOptionsView;
  }

  // The following method gets only the serverless backup options stored in the view, if any.
  // Fields like serverlessContinuousBackupEnabledAt will need to be set/updated in the DAO layer
  public Optional<ServerlessBackupOptions> getPartialServerlessBackupOptions() {
    return _serverlessBackupOptionsView == null
        ? Optional.empty()
        : Optional.of(
            new ServerlessBackupOptions(
                _serverlessBackupOptionsView.isServerlessContinuousBackupEnabled(),
                Optional.empty()));
  }

  /**
   * @return @Deprecated
   *     <p>We can no longer rely on the @provider value of the ClusterDescriptionView since we
   *     longer ensure that this value is updated in the UI. We should now be using {@link
   *     #getCloudProviders() getCloudProviders} to grab the providers.
   */
  @Deprecated
  private CloudProvider getCloudProvider() {
    LOG.trace(
        "getCloudProvider function should not be called. This only happens when there is not a"
            + " vaid replicationSpecList.");
    return Optional.ofNullable(_cloudProvider).map(CloudProvider::valueOf).orElse(null);
  }

  public Set<CloudProvider> getCloudProviders() {
    if (hasReplicationSpecList()) {
      return getReplicationSpecList().stream()
          .flatMap(rsv -> rsv.getRegionConfigs().stream())
          .map(RegionConfigView::getCloudProvider)
          .map(CloudProvider::valueOf)
          .collect(Collectors.toSet());
    }

    if (getCloudProvider() != null) {
      return Set.of(getCloudProvider());
    }

    return Set.of();
  }

  // This method that returns the backing cloud providers and is compatible with all cluster types
  public Set<CloudProvider> getBackingProviders() {
    if (isTenantCluster()) {
      return getReplicationSpecList().stream()
          .map(ReplicationSpecView::getRegionConfigs)
          .flatMap(Collection::stream)
          .map(RegionConfigView::getElectableSpecs)
          .map(HardwareSpecView::getBackingCloudProvider)
          .collect(Collectors.toSet());
    }

    return getCloudProviders();
  }

  public String getName() {
    return _name;
  }

  /** Should not be used outside the case where you're creating a new cluster. */
  public void setName(final String pName) {
    _name = pName;
  }

  public ObjectId getGroupId() {
    return _groupId;
  }

  public Date getCreateDate() {
    return _createDate;
  }

  public ObjectId getUniqueId() {
    return _uniqueId;
  }

  public String getGroupName() {
    return _groupName;
  }

  public String getMongoDBVersion() {
    return _mongoDBVersion;
  }

  public String getMongoDBMajorVersion() {
    return _mongoDBMajorVersion;
  }

  public String getClusterType() {
    return _clusterType;
  }

  public void setClusterType(final String pClusterType) {
    _clusterType = pClusterType;
  }

  public ClusterDescription.ClusterType getClusterDescriptionClusterType() {
    return getClusterType() != null
        ? ClusterDescription.ClusterType.valueOf(getClusterType())
        : null;
  }

  public String getInternalClusterRole() {
    return _internalClusterRole;
  }

  public void setInternalClusterRole(final String pInternalClusterRole) {
    _internalClusterRole = pInternalClusterRole;
  }

  public InternalClusterRole getClusterDescriptionInternalClusterRole() {
    return getInternalClusterRole() != null
        ? InternalClusterRole.valueOf(getInternalClusterRole())
        : null;
  }

  public Boolean isMTM() {
    return _isMTM;
  }

  public void setIsMTM(final boolean pIsMTM) {
    _isMTM = pIsMTM;
  }

  public Boolean isMTMSentinel() {
    return _isMTMSentinel;
  }

  public void setIsMTMSentinel(final boolean pIsMTMSentinel) {
    _isMTMSentinel = pIsMTMSentinel;
  }

  public boolean hasReplicationSpecList() {
    return getReplicationSpecList() != null;
  }

  /**
   * Checks whether all ReplicationSpecs in the ClusterDescription are already split
   *
   * @return true if all ReplicationSpecs are split, false otherwise
   */
  public boolean hasSplitReplicationSpecs() {
    return _replicationSpecList.stream().allMatch(rs -> rs.getNumShards() == 1);
  }

  public boolean hasDifferentAnalyticsAndElectableSpecs() {
    if (getReplicationSpecList() == null) {
      return false;
    }

    final long numAnalyticsNodes =
        getReplicationSpecList().stream()
            .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
            .mapToInt(regionConfig -> regionConfig.getAnalyticsSpecs().getNodeCount())
            .sum();
    if (numAnalyticsNodes == 0) {
      return false;
    }

    final Pair<String, String> baseCloudProviderAndInstanceSize =
        getReplicationSpecList().stream()
            .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
            .map(
                regionConfig ->
                    Pair.of(
                        regionConfig.getCloudProvider(),
                        regionConfig.getElectableSpecs().getInstanceSize()))
            .findFirst()
            .orElse(Pair.of(null, null));

    final Pair<String, String> analyticsCloudProviderAndInstanceSize =
        getReplicationSpecList().stream()
            .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
            .map(
                regionConfig ->
                    Pair.of(
                        regionConfig.getCloudProvider(),
                        regionConfig.getAnalyticsSpecs().getInstanceSize()))
            .findFirst()
            .orElse(Pair.of(null, null));
    return !analyticsCloudProviderAndInstanceSize.equals(baseCloudProviderAndInstanceSize);
  }

  public boolean hasAsymmetricHardwareSpecs() {
    if (getReplicationSpecList() == null) {
      return false;
    }

    final Map<String, Set<String>> baseCloudProviderSpecMap =
        getReplicationSpecList().stream()
            .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
            .collect(
                Collectors.groupingBy(
                    RegionConfigView::getCloudProvider,
                    Collectors.mapping(
                        r -> r.getElectableSpecs().getInstanceSize(), Collectors.toSet())));

    if (baseCloudProviderSpecMap.values().stream().anyMatch(s -> s.size() > 1)) {
      return true;
    }

    final Map<String, Set<String>> analyticsCloudProviderSpecMap =
        getReplicationSpecList().stream()
            .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
            .collect(
                Collectors.groupingBy(
                    RegionConfigView::getCloudProvider,
                    Collectors.mapping(
                        r -> r.getAnalyticsSpecs().getInstanceSize(), Collectors.toSet())));

    if (analyticsCloudProviderSpecMap.values().stream().anyMatch(s -> s.size() > 1)) {
      return true;
    }

    return false;
  }

  public boolean isPausedNVMeCluster() {
    if (getReplicationSpecList() == null) {
      return false;
    }

    final boolean isNVMe =
        getReplicationSpecList().stream()
            .flatMap(replicationSpecView -> replicationSpecView.getRegionConfigs().stream())
            .flatMap(regionConfigView -> regionConfigView.getInstanceSizes().stream())
            .anyMatch(InstanceSize::isNVMe);

    return isNVMe && isPaused();
  }

  public List<ReplicationSpecView> getReplicationSpecList() {
    return _replicationSpecList;
  }

  public List<NDSLabelView> getLabels() {
    return _labels == null ? Collections.emptyList() : _labels;
  }

  public Double getDiskSizeGB() {
    return _diskSizeGB;
  }

  public String[] getMongoDBURIHosts() {
    return _mongoDBUriHosts;
  }

  public Date getMongoDBURIHostsLastUpdateDate() {
    return _mongoDBUriHostsLastUpdateDate;
  }

  public State getState() {
    return _state;
  }

  public Date getNeedsMongoDBConfigPublishAfter() {
    return _needsMongoDBConfigPublishAfter;
  }

  public Boolean isBackupEnabled() {
    return _backupEnabled;
  }

  public Boolean isDiskBackupEnabled() {
    return _diskBackupEnabled;
  }

  public Boolean isPitEnabled() {
    return _pitEnabled == null ? false : _pitEnabled;
  }

  public Optional<Date> getDeleteAfterDate() {
    return Optional.ofNullable(_deleteAfterDate);
  }

  public void setDeleteAfterDate(final Date pDeleteAfterDate) {
    _deleteAfterDate = pDeleteAfterDate;
  }

  public Boolean isTenantUpgrading() {
    return _tenantUpgrading != null && _tenantUpgrading;
  }

  public Boolean isTenantUpgradingServerlessToDedicated() {
    return _tenantUpgradingServerlessToDedicated != null && _tenantUpgradingServerlessToDedicated;
  }

  public Boolean isServerlessTenantBlocked() {
    return _isServerlessTenantBlocked;
  }

  public Boolean hasDedicatedMetricsAvailableForServerlessToDedicated() {
    return _hasDedicatedMetricsAvailableForServerlessToDedicated != null
        && _hasDedicatedMetricsAvailableForServerlessToDedicated;
  }

  public Boolean isTenantDowngradingServerlessToFree() {
    return _tenantDowngradingServerlessToFree != null && _tenantDowngradingServerlessToFree;
  }

  public Boolean isTenantAccessRevokedForPause() {
    return Optional.ofNullable(_tenantAccessRevokedForPause).orElse(false);
  }

  public Date getUserNotifiedAboutPauseDate() {
    return Optional.ofNullable(_userNotifiedAboutPauseDate).orElse(null);
  }

  public Boolean isUnderCompaction() {
    return _isUnderCompaction;
  }

  public Boolean isPaused() {
    return _isPaused == null ? false : _isPaused;
  }

  public void setAutoScalingView(final AutoScalingView pAutoScalingView) {
    if (getReplicationSpecList() != null) {
      getReplicationSpecList()
          .forEach(
              spec -> spec.getRegionConfigs().forEach(r -> r.setBaseAutoScaling(pAutoScalingView)));
    }
  }

  public Boolean shouldRetainBackupsForDeleting() {
    return _retainBackupsForDeleting;
  }

  public void setRetainBackupsForDeleting(Boolean pRetainBackupsForDeleting) {
    _retainBackupsForDeleting = pRetainBackupsForDeleting;
  }

  @Nullable
  public AutoScalingView getAutoScalingView() {
    if (hasReplicationSpecList()) {
      return getReplicationSpecList().stream()
          .flatMap(spec -> spec.getRegionConfigs().stream())
          .filter(Objects::nonNull)
          .map(RegionConfigView::getBaseAutoScaling)
          .filter(Objects::nonNull)
          .findFirst()
          .orElse(
              AutoScalingView.getDisabledAutoScalingView(
                  getReplicationSpecList().get(0).getCloudProvider()));
    }
    return AutoScalingView.getDisabledAutoScalingView(getCloudProvider());
  }

  public Optional<AutoScalingView> getAutoScalingView(final NodeTypeFamily pNodeTypeFamily) {
    switch (pNodeTypeFamily) {
      case BASE:
        return Optional.ofNullable(getAutoScalingView());
      case ANALYTICS:
        return getAnalyticsAutoScalingView();
      default:
        throw new IllegalArgumentException("Unknown node type family " + pNodeTypeFamily);
    }
  }

  private Optional<AutoScalingView> getAnalyticsAutoScalingView() {
    if (hasReplicationSpecList()) {
      return getReplicationSpecList().stream()
          .flatMap(spec -> spec.getRegionConfigs().stream())
          .map(RegionConfigView::getAnalyticsAutoScaling)
          .filter(Objects::nonNull)
          .findFirst();
    }
    return Optional.empty();
  }

  public AutoScalingMode getAutoScalingMode() {
    return _autoScalingMode;
  }

  /**
   * Gets the auto-sharding configuration for this cluster.
   *
   * @return the auto-sharding configuration, or empty if not configured
   */
  public Optional<AutoSharding> getAutoSharding() {
    return Optional.ofNullable(_autoSharding);
  }

  /**
   * Sets the auto-sharding configuration for this cluster.
   *
   * @param pAutoSharding the auto-sharding configuration to set
   */
  public void setAutoSharding(final AutoSharding pAutoSharding) {
    _autoSharding = pAutoSharding;
  }

  public BiConnectorView getBiConnectorView() {
    return _biConnector;
  }

  public DataProcessingRegionView getDataProcessingRegion() {
    return _dataProcessingRegion;
  }

  public void setDataProcessingRegion(final DataProcessingRegionView pDataProcessingRegionView) {
    _dataProcessingRegion = pDataProcessingRegionView;
  }

  public GeoShardingView getGeoShardingView() {
    return _geoShardingView;
  }

  public EncryptionAtRestProviderView getEncryptionAtRestProvider() {
    return _encryptionAtRestProvider;
  }

  public String getSRVAddress() {
    return _srvAddress;
  }

  public Boolean isMonitoringPaused() {
    return _isMonitoringPaused;
  }

  public String getDeploymentItemName() {
    return _deploymentItemName;
  }

  public String[] getPrivateMongoDBUriHosts() {
    return _privateMongoDBUriHosts;
  }

  public String getPrivateSrvAddress() {
    return _privateSrvAddress;
  }

  public Map<String, List<String>> getPrivateLinkMongoDBUriHosts() {
    return _privateLinkMongoDBUriHosts;
  }

  public Map<String, String> getPrivateLinkSrvAddresses() {
    return _privateLinkSrvAddresses;
  }

  public Set<RegionName> getRegionsInUse() {
    return getReplicationSpecList().stream()
        .flatMap(
            replSpec ->
                getCloudProviders().stream()
                    .filter(provider -> !provider.isTenantProvider())
                    .flatMap(provider -> replSpec.getRegionConfigs().stream())
                    .map(RegionConfigView::getCloudProviderRegionName))
        .collect(Collectors.toSet());
  }

  public Set<RegionName> getRegionsIncludingTenants() {
    return getReplicationSpecList().stream()
        .flatMap(
            replSpec ->
                getCloudProviders().stream()
                    .flatMap(provider -> replSpec.getRegionConfigs().stream())
                    .map(RegionConfigView::getCloudProviderRegionName))
        .collect(Collectors.toSet());
  }

  public String getDeploymentClusterName() {
    return _deploymentClusterName;
  }

  public InstanceHostname.SubdomainLevel getHostnameSubdomainLevel() {
    return Optional.ofNullable(_hostnameSubdomainLevel)
        .filter(l -> !l.isEmpty())
        .map(InstanceHostname.SubdomainLevel::valueOf)
        .orElse(null);
  }

  public InstanceHostname.HostnameScheme getHostnameSchemeForAgents() {
    return Optional.ofNullable(_hostnameSchemeForAgents)
        .filter(l -> !l.isEmpty())
        .map(InstanceHostname.HostnameScheme::valueOf)
        .orElse(null);
  }

  public Boolean getForceReplicaSetReconfig() {
    return _forceReplicaSetReconfig;
  }

  public Optional<Date> getAcceptDataRisksAndForceReplicaSetReconfig() {
    return Optional.ofNullable(_acceptDataRisksAndForceReplicaSetReconfig);
  }

  @VisibleForTesting
  public void setAcceptDataRisksAndForceReplicaSetReconfig(
      final Date pAcceptDataRisksAndForceReplicaSetReconfig) {
    _acceptDataRisksAndForceReplicaSetReconfig = pAcceptDataRisksAndForceReplicaSetReconfig;
  }

  public Optional<Date> getResurrectRequested() {
    return Optional.ofNullable(_resurrectRequested);
  }

  public Optional<ResurrectOptionsView> getResurrectOptions() {
    return Optional.ofNullable(_resurrectOptions);
  }

  public Set<InstanceSize> getInstanceSizes() {
    if (hasReplicationSpecList() && !getReplicationSpecList().isEmpty()) {
      return getReplicationSpecList().stream()
          .flatMap((replicationSpec) -> replicationSpec.getRegionConfigs().stream())
          .flatMap((regionConfig) -> regionConfig.getInstanceSizes().stream())
          .collect(Collectors.toSet());
    } else {
      return Optional.ofNullable(getInstanceSize()).map(Set::of).orElse(Set.of());
    }
  }

  public InstanceSize getInstanceSize() {
    final CloudProvider provider = getCloudProvider();
    if (hasReplicationSpecList() && !getReplicationSpecList().isEmpty()) {
      return getReplicationSpecList().stream()
          .findFirst()
          .flatMap(spec -> spec.getRegionConfigs().stream().findFirst())
          .flatMap(
              rc ->
                  InstanceSizeUtil.findByName(
                      CloudProvider.valueOf(rc.getCloudProvider()),
                      rc.getElectableSpecs().getInstanceSize()))
          .orElse(null);
    } else if (provider == null) {
      // No cloud provider means no cloudProviderOptions. The instance size will specified in the
      // replication spec
      return null;
    }
    return null;
  }

  public VolumeType getEBSVolumeType() {
    return getReplicationSpecList().get(0).getRegionConfigs().stream()
        .filter(r -> CloudProvider.valueOf(r.getCloudProvider()) == CloudProvider.AWS)
        .findFirst()
        .get()
        .getElectableSpecs()
        ._volumeType
        .getVolumeType();
  }

  public int getDiskIOPSForSubmit(
      final AWSNDSInstanceSize pInstanceSize,
      final Double pDiskSizeGB,
      final VolumeType pVolumeType) {
    // NVMe instances always get max IOPs for the provisioned IOPs backup disk. The UI may request a
    // larger value, this is the value of the in-chassis SSD
    if (pInstanceSize.isNVMe()) {
      return pInstanceSize.getMaxEBSIOPS();
    } else {
      if (VolumeType.Io1.equals(pVolumeType) || VolumeType.Io2.equals(pVolumeType)) {
        return pInstanceSize.getMinEBSIOPS(pDiskSizeGB);
      } else if (VolumeType.Gp3.equals(pVolumeType)) {
        return pInstanceSize.getGP3StandardEBSIOPS(pDiskSizeGB);
      }
      return pInstanceSize.getStandardEBSIOPS(pDiskSizeGB);
    }
  }

  public VolumeType getEBSVolumeTypeForSubmit(
      final AWSInstanceSize pTargetInstanceSize,
      final AWSRegionName pRegion,
      final VolumeType pTargetVolumeType) {
    // NVMe Instances get a provisioned IOPs volume for the backup disk
    if (pTargetInstanceSize.isNVMe()) {
      return pRegion.supportsIo2Volumes() ? VolumeType.Io2 : VolumeType.Io1;
    }
    if ((pTargetInstanceSize.equals(AWSNDSInstanceSize.M10)
            || pTargetInstanceSize.equals(AWSNDSInstanceSize.M20))
        && (pTargetVolumeType.equals(VolumeType.Io1) || pTargetVolumeType.equals(VolumeType.Io2))) {
      return VolumeType.Gp3;
    }
    if (pTargetVolumeType == VolumeType.Io2 && !pRegion.supportsIo2Volumes()) {
      return VolumeType.Io1;
    }
    return pTargetVolumeType;
  }

  public boolean isTenantBackupEnabled() {
    return Optional.ofNullable(_cloudProviderOptions._tenantBackupEnabled).orElse(false);
  }

  public Date getNextBackupDate() {
    return _cloudProviderOptions._nextBackupDate;
  }

  public Date getNDSAccessRevokedDate() {
    return _cloudProviderOptions._ndsAccessRevokedDate;
  }

  public FreeInstanceSize.LimitsProfile getLimitsProfile() {
    return _cloudProviderOptions._limitsProfile;
  }

  public Integer getDiskSizeGBLimit() {
    return _cloudProviderOptions._diskSizeGBLimit;
  }

  public Boolean getExcludeFromPool() {
    return _cloudProviderOptions._excludeFromPool;
  }

  public Boolean isCrossCloudCluster() {
    return _isCrossCloudCluster;
  }

  public CloudProvider getEffectiveProviderFromReplicationSpec() {
    if (isTenantCluster()) {
      return getReplicationSpecList()
          .get(0)
          .getRegionConfigs()
          .get(0)
          .getElectableSpecs()
          .getBackingCloudProvider();
    }

    return getProviderFromReplicationSpec();
  }

  public CloudProvider getProviderFromReplicationSpec() {
    if (hasReplicationSpecList()) {
      return getReplicationSpecList().get(0).getCloudProvider();
    }
    return getReplicationSpecList().get(0).getCloudProvider();
  }

  public String getRegionNameFromReplicationSpec() {
    return getReplicationSpecList().get(0).getRegionConfigs().get(0).getRegionName();
  }

  public boolean isFreeOrSharedTierTenantCluster() {
    return getCloudProviders().equals(Set.of(CloudProvider.FREE));
  }

  /**
   * Based on {@link ClusterDescription#getFeatureCompatibilityVersion()}. Due to the fields in this
   * class not being immutable we derive the featureCompatibilityVersion based on the fields in this
   * class.
   */
  public String getFeatureCompatibilityVersion() {
    if (!Strings.isNullOrEmpty(_fixedMongoDBFCV)) {
      return _fixedMongoDBFCV;
    }

    if (VersionReleaseSystem.CONTINUOUS == _versionReleaseSystem
        && _continuousDeliveryFCV != null
        && !_isMongoDBVersionFixed) {
      return _continuousDeliveryFCV;
    }

    return Optional.ofNullable(_mongoDBVersion)
        .map(VersionUtils::getDefaultFeatureCompatibilityVersionForMongoDBVersion)
        .orElse(getMongoDBMajorVersion());
  }

  public boolean isFixedFCV() {
    return !Strings.isNullOrEmpty(_fixedMongoDBFCV);
  }

  public Date getFeatureCompatibilityVersionExpirationDate() {
    if (!Strings.isNullOrEmpty(_fixedMongoDBFCV)) {
      return _fixedMongoDBFCVExpiration;
    } else {
      return null;
    }
  }

  public boolean isServerlessTenantCluster() {
    return getCloudProviders().equals(Set.of(CloudProvider.SERVERLESS));
  }

  public boolean isMigratedOrApiShimCreatedServerlessTenantCluster() {
    if (_flexTenantMigrationState == null) {
      return false;
    }
    final Optional<CloudProvider> apiCloudProvider =
        _flexTenantMigrationState.getTenantApiCloudProvider();
    final Optional<CloudProvider> formerCloudProvider =
        _flexTenantMigrationState.getFormerCloudProvider();
    return apiCloudProvider.map(CloudProvider.SERVERLESS::equals).orElse(false)
        || formerCloudProvider.map(CloudProvider.SERVERLESS::equals).orElse(false);
  }

  public boolean isFlexTenantCluster() {
    return getCloudProviders().equals(Set.of(CloudProvider.FLEX));
  }

  public boolean isTenantCluster() {
    return isFreeOrSharedTierTenantCluster()
        || isServerlessTenantCluster()
        || isFlexTenantCluster();
  }

  public CloudProvider getTenantType() {
    return getCloudProviders().stream()
        .filter(CloudProvider::isTenantProvider)
        .findFirst()
        .orElse(CloudProvider.NONE);
  }

  public RootCertType getRootCertType() {
    return _rootCertType;
  }

  public VersionReleaseSystem getVersionReleaseSystem() {
    return _versionReleaseSystem;
  }

  public Boolean isTerminationProtectionEnabled() {
    return _terminationProtectionEnabled;
  }

  public Date getNeedsSampleDataLoadAfter() {
    return _needsSampleDataLoadAfter;
  }

  public Boolean getCreateSampleSearchIndex() {
    return _createSampleSearchIndex;
  }

  public SampleDataset getSampleDatasetToLoad() {
    return _sampleDatasetToLoad;
  }

  public DiskWarmingMode getDiskWarmingMode() {
    return _diskWarmingMode;
  }

  public ConfigServerType getConfigServerType() {
    return _configServerType;
  }

  public Optional<ConfigServerManagementMode> getConfigServerManagementMode() {
    return Optional.ofNullable(_configServerManagementMode);
  }

  public Optional<EmployeeAccessGrantView> getEmployeeAccessGrant() {
    return Optional.ofNullable(_employeeAccessGrant);
  }

  Optional<String> getOSPolicyVersion() {
    return Optional.ofNullable(_osPolicyVersion);
  }

  public List<String> getShardsDraining() {
    return _shardsDraining;
  }

  /**
   * Whether the cluster is using AWS time-based snapshot copies for optimized initial sync across
   * regions.
   *
   * @return true if the cluster is using AWS time-based snapshot copies for optimized initial sync
   *     across regions
   */
  public Boolean getUseAwsTimeBasedSnapshotCopyForFastInitialSync() {
    return _useAwsTimeBasedSnapshotCopyForFastInitialSync;
  }

  public void setDiskSizeGB(final Double pDiskSizeGB) {
    _diskSizeGB = pDiskSizeGB;
  }

  public void setDiskBackupEnabled(boolean pDiskBackupEnabled) {
    _diskBackupEnabled = pDiskBackupEnabled;
  }

  public void setBackupEnabled(boolean pBackupEnabled) {
    _backupEnabled = pBackupEnabled;
  }

  public void setMongoDBVersion(String pMongoDBVersion) {
    _mongoDBVersion = pMongoDBVersion;
  }

  public void setMongoDBMajorVersion(String pMongoDBMajorVersion) {
    _mongoDBMajorVersion = pMongoDBMajorVersion;
  }

  public void setEncryptionAtRestProvider(EncryptionAtRestProviderView pEncryptionAtRestProvider) {
    _encryptionAtRestProvider = pEncryptionAtRestProvider;
  }

  public void setConfigServerType(ConfigServerType pConfigServerType) {
    _configServerType = pConfigServerType;
  }

  public void setConfigServerManagementMode(
      ConfigServerManagementMode pConfigServerManagementMode) {
    _configServerManagementMode = pConfigServerManagementMode;
  }

  public ReplicaSetScalingStrategy getReplicaSetScalingStrategy() {
    return _replicaSetScalingStrategy;
  }

  public void setReplicaSetScalingStrategy(
      final ReplicaSetScalingStrategy pReplicaSetScalingStrategy) {
    _replicaSetScalingStrategy = pReplicaSetScalingStrategy;
  }

  public Boolean getRedactClientLogData() {
    return _redactClientLogData;
  }

  public void setRedactClientLogData(final Boolean pRedactClientLogData) {
    _redactClientLogData = pRedactClientLogData;
  }

  public void setPartnerIntegrationsData(final PartnerIntegrationsData pPartnerIntegrationsData) {
    _partnerIntegrationsData = pPartnerIntegrationsData;
  }

  public void setFlexTenantMigrationState(
      final FlexTenantMigrationState pFlexTenantMigrationState) {
    _flexTenantMigrationState = pFlexTenantMigrationState;
  }

  public boolean hasClusterLevelMetrics() {
    return _hasClusterLevelMetrics;
  }

  public void setStorageSystem(final StorageSystem pStorageSystem) {
    _storageSystem = pStorageSystem;
  }

  public StorageSystem getStorageSystem() {
    return _storageSystem;
  }

  public List<ObjectId> getRestoreJobIds() {
    return _restoreJobIds;
  }

  public List<IndexConfig> getPendingIndexes() {
    return _pendingIndexes;
  }

  public FixedVersionView getFixedFeatureCompatibilityVersionView() {
    return _fixedFeatureCompatibilityVersionView;
  }

  public FixedVersionView getFixedCpuArchView() {
    return _fixedCpuArchView;
  }

  public FixedVersionView getFixedMongoDBVersionView() {
    return _fixedMongoDBVersionView;
  }

  public FixedVersionView getFixedOsView() {
    return _fixedOsView;
  }

  public FixedVersionView getFixedACMEProviderView() {
    return _fixedACMEProviderView;
  }

  public List<BumperFileOverrideView> getBumperFileOverrides() {
    return _bumperFileOverrides;
  }

  public boolean isDeleteRequested() {
    return _deleteRequested;
  }

  public DeleteClusterReason getDeleteReason() {
    return _deleteReason;
  }

  public RestoreJobType getRestoreJobType() {
    return _restoreJobType;
  }

  public Date getDeletedDate() {
    return _deletedDate;
  }

  public Date getEnsureClusterConnectivityAfter() {
    return _ensureClusterConnectivityAfter;
  }

  public ProcessRestartAllowedState getNeedsMongoDBConfigPublishRestartAllowed() {
    return _needsMongoDBConfigPublishRestartAllowed;
  }

  public Date getPausedDate() {
    return _pausedDate;
  }

  public LogRetention getLogRetention() {
    return _logRetention;
  }

  public boolean isStopContinuousBackup() {
    return _stopContinuousBackup;
  }

  public List<String> getForceReplicaSetReconfigHostsToSkip() {
    return _forceReplicaSetReconfigHostsToSkip;
  }

  public Integer getReplicaSetVersionOverride() {
    return _replicaSetVersionOverride;
  }

  public Set<String> getMigrateFromAvailabilitySets() {
    return _migrateFromAvailabilitySets;
  }

  public String getLoadBalancedHostname() {
    return _loadBalancedHostname;
  }

  public String getLoadBalancedMeshHostname() {
    return _loadBalancedMeshHostname;
  }

  public Date getCancelShardDrainRequested() {
    return _cancelShardDrainRequested;
  }

  public OsTunedFileOverridesView getOsTunedFileOverridesView() {
    return _osTunedFileOverridesView;
  }

  public String getDnsPin() {
    return _dnsPin;
  }

  public String getClusterNamePrefix() {
    return _clusterNamePrefix;
  }

  public ClusterProvisionType getClusterProvisionType() {
    return _clusterProvisionType;
  }

  public Date getNeedsServerlessSnapshotForPit() {
    return _needsServerlessSnapshotForPit;
  }

  public Date getNeedsEnvoySyncAfter() {
    return _needsEnvoySyncAfter;
  }

  public Date getLastDataValidationDate() {
    return _lastDataValidationDate;
  }

  public Date getLastDbCheckDate() {
    return _lastDbCheckDate;
  }

  public Date getNeedsDbCheckAfter() {
    return _needsDbCheckAfter;
  }

  public int getDbCheckPreflightRetryCount() {
    return _dbCheckPreflightRetryCount;
  }

  public Boolean isCriticalOSPolicyRelease() {
    return _isCriticalOSPolicyRelease;
  }

  public List<Integer> getCpuSocketBinding() {
    return _cpuSocketBinding;
  }

  public Date getNeedsPrioritiesResetForPriorityTakeoverAfter() {
    return _needsPrioritiesResetForPriorityTakeoverAfter;
  }

  public ProxyProtocolForPrivateLinkMode getProxyProtocolForPrivateLinkMode() {
    return _proxyProtocolForPrivateLinkMode;
  }

  /**
   * Gets the customer-provided disk size in gigabytes for the cluster.
   *
   * @return the disk size in GB as specified by the customer, may be null
   */
  public Double getCustomerProvidedDiskSizeGB() {
    return _customerProvidedDiskSizeGB;
  }

  /**
   * Gets whether effective cluster fields should be used in cluster operations.
   *
   * <p>Effective cluster fields provide a mechanism to override or supplement the standard cluster
   * configuration with computed or derived values. When enabled, the system will use effective
   * field values that may be calculated based on current cluster state, policies, or other dynamic
   * factors. When disabled, only the standard cluster configuration fields are used.
   *
   * @return the effective fields setting - ENABLED to use computed values, DISABLED to use only
   *     standard configuration fields
   */
  public EffectiveFields getUseEffectiveClusterFields() {
    return _useEffectiveClusterFields;
  }

  @Nullable
  public PartnerIntegrationsData getPartnerIntegrationsData() {
    return _partnerIntegrationsData;
  }

  public Set<String> getClusterTags() {
    return _clusterTags;
  }

  public enum State {
    IDLE,
    CREATING,
    UPDATING,
    DELETING,
    DELETED,
    REPAIRING
  }

  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class TenantOptionsView {

    @JsonProperty(FieldDefs.LIMITS_PROFILE)
    protected FreeInstanceSize.LimitsProfile _limitsProfile;

    @JsonProperty(FieldDefs.TENANT_BACKUP_ENABLED)
    protected Boolean _tenantBackupEnabled;

    @JsonProperty(FieldDefs.NEXT_BACKUP_DATE)
    protected Date _nextBackupDate;

    @JsonProperty(FieldDefs.NDS_ACCESS_REVOKED_DATE)
    protected Date _ndsAccessRevokedDate;

    @JsonProperty(FieldDefs.DISK_SIZE_GB_LIMIT)
    protected Integer _diskSizeGBLimit;

    @JsonProperty(value = FieldDefs.EXCLUDE_FROM_POOL, access = Access.WRITE_ONLY)
    protected Boolean _excludeFromPool;

    public TenantOptionsView() {}

    public static TenantOptionsView fromFreeTenantProviderOptions(
        FreeTenantProviderOptions pOptions) {
      final TenantOptionsView view = new TenantOptionsView();
      view._limitsProfile = pOptions.getLimitsProfile();
      view._tenantBackupEnabled = pOptions.isTenantBackupEnabled();
      view._nextBackupDate = pOptions.getNextBackupDate();
      view._ndsAccessRevokedDate = pOptions.getNdsAccessRevokedDate();
      return view;
    }

    public static TenantOptionsView fromServerlessTenantProviderOptions(
        ServerlessTenantProviderOptions pOptions) {
      final TenantOptionsView view = new TenantOptionsView();
      view._diskSizeGBLimit = pOptions.getDiskSizeGBLimit();
      view._tenantBackupEnabled = pOptions.isTenantBackupEnabled();
      view._nextBackupDate = pOptions.getNextBackupDate();
      view._excludeFromPool = pOptions.isExcludeFromPool();
      return view;
    }

    public static TenantOptionsView fromFlexTenantProviderOptions(
        final FlexTenantProviderOptions pOptions) {
      final TenantOptionsView view = new TenantOptionsView();
      view._diskSizeGBLimit = pOptions.getDiskSizeGBLimit();
      view._tenantBackupEnabled = pOptions.isTenantBackupEnabled();
      view._nextBackupDate = pOptions.getNextBackupDate();
      return view;
    }

    public static TenantOptionsView fromClusterDescription(
        final ClusterDescription pClusterDescription) {
      if (!pClusterDescription.isTenantCluster()) {
        return null;
      }

      if (pClusterDescription.getCloudProviders().contains(CloudProvider.FREE)) {
        final FreeTenantProviderOptions options =
            (FreeTenantProviderOptions) pClusterDescription.getFreeTenantProviderOptions();
        return fromFreeTenantProviderOptions(options);
      } else if (pClusterDescription.getCloudProviders().contains(CloudProvider.SERVERLESS)) {
        final ServerlessTenantProviderOptions options =
            (ServerlessTenantProviderOptions)
                pClusterDescription.getServerlessTenantProviderOptions();
        return fromServerlessTenantProviderOptions(options);
      } else if (pClusterDescription.getCloudProviders().contains(CloudProvider.FLEX)) {
        final FlexTenantProviderOptions options =
            (FlexTenantProviderOptions) pClusterDescription.getFlexTenantProviderOptions();
        return fromFlexTenantProviderOptions(options);
      } else {
        throw new IllegalArgumentException(
            "Unknown provider"
                + pClusterDescription.getCloudProviders().stream().findFirst().get());
      }
    }
  }

  public static class FieldDefs {
    public static final String TENANT_BACKUP_ENABLED = "tenantBackupEnabled";
    public static final String NEXT_BACKUP_DATE = "nextBackupDate";
    public static final String NDS_ACCESS_REVOKED_DATE = "ndsAccessRevokedDate";
    public static final String LIMITS_PROFILE = "limitsProfile";
    public static final String MONGODB_MAJOR_VERSION = "mongoDBMajorVersion";
    public static final String DISK_SIZE_GB_LIMIT = "diskSizeGBLimit";
    public static final String MAX_INCOMING_CONNECTIONS = "maxIncomingConns";
    public static final String EXCLUDE_FROM_POOL = "excludeFromPool";
    public static final String ROOT_CERT_TYPE = "rootCertType";
    public static final String SEARCH_DEPLOYMENT_REQUEST_FIELD = "searchDeploymentRequest";
    public static final String REPLICATION_SPEC_LIST = "replicationSpecList";
  }

  public static ClusterDescriptionViewBuilder builder() {
    return new ClusterDescriptionViewBuilder();
  }

  public ClusterDescriptionViewBuilder toBuilder() {
    return new ClusterDescriptionViewBuilder(this);
  }

  public static class ClusterDescriptionViewBuilder {
    private String _cloudProvider;
    private String _name;
    private ObjectId _groupId;
    private ObjectId _uniqueId;
    private Date _createDate;
    private String _groupName;
    private String _mongoDBVersion;
    private String _mongoDBMajorVersion;
    private String _fixedMongoDBFCV;
    private Date _fixedMongoDBFCVExpiration;
    private Date _fixedMongoDBFCVPinnedDate;
    private String _clusterType;
    private String _internalClusterRole = InternalClusterRole.NONE.name();
    private Boolean _isMTM;
    private Boolean _isMTMSentinel;
    private List<ReplicationSpecView> _replicationSpecList;
    private Integer _maxIncomingConns;
    private Double _diskSizeGB;
    private String[] _mongoDBUriHosts;
    private String[] _privateMongoDBUriHosts;
    private String _privateSrvAddress;
    private Map<String, List<String>> _privateLinkMongoDBUriHosts;
    private Map<String, String> _privateLinkSrvAddresses;
    private Date _mongoDBUriHostsLastUpdateDate;
    private Boolean _backupEnabled;
    private Boolean _diskBackupEnabled;
    private Boolean _pitEnabled;
    private State _state;
    private Date _deleteAfterDate;
    private Date _needsMongoDBConfigPublishAfter;
    private Boolean _tenantUpgrading;
    private Boolean _tenantUpgradingServerlessToDedicated;
    private Boolean _hasDedicatedMetricsAvailableForServerlessToDedicated;
    private Boolean _tenantDowngradingServerlessToFree;
    private Boolean _tenantAccessRevokedForPause;
    private Date _userNotifiedAboutPauseDate;
    private Boolean _isPaused;
    private Boolean _isUnderCompaction;
    private BiConnectorView _biConnector;
    private DataProcessingRegionView _dataProcessingRegion;
    private Date _lastUpdateDate;
    private String _srvAddress;
    private Set<String> _clusterTags;
    private GeoShardingView _geoShardingView;
    private EncryptionAtRestProviderView _encryptionAtRestProvider;
    private Boolean _isNvme;
    private List<NDSLabelView> _labels;
    private Boolean _isMonitoringPaused;
    private String _deploymentItemName;
    private String _deploymentClusterName;
    private Boolean _forceReplicaSetReconfig;
    private Date _acceptDataRisksAndForceReplicaSetReconfig;
    private Date _resurrectRequested;
    private ResurrectOptionsView _resurrectOptions;
    private String _hostnameSubdomainLevel;
    private String _hostnameSchemeForAgents;
    private Boolean _isCrossCloudCluster;
    private Boolean _isMongoDBVersionFixed;
    private RootCertType _rootCertType;
    private VersionReleaseSystem _versionReleaseSystem;
    private String _continuousDeliveryFCV;
    private Map<String, String> _endpointToLoadBalancedSRVConnectionURI;
    private ServerlessBackupOptionsView _serverlessBackupOptionsView;
    private boolean _isFastProvisioned;
    private Boolean _terminationProtectionEnabled;
    private Date _needsSampleDataLoadAfter;
    private Boolean _createSampleSearchIndex = false;
    private SampleDataset _sampleDatasetToLoad;
    private Boolean _retainBackupsForDeleting;
    private ApiSearchDeploymentRequestView _searchDeploymentRequest;
    private DiskWarmingMode _diskWarmingMode;
    private ConfigServerType _configServerType;
    private ConfigServerManagementMode _configServerManagementMode;
    private TenantOptionsView _cloudProviderOptions = new TenantOptionsView();
    private ReplicaSetScalingStrategy _replicaSetScalingStrategy;
    private EmployeeAccessGrantView _employeeAccessGrant;
    private String _osPolicyVersion;
    private Boolean _redactClientLogData;
    private List<String> _shardsDraining;
    private FlexTenantMigrationState _flexTenantMigrationState;
    private boolean _hasClusterLevelMetrics;
    private StorageSystem _storageSystem;
    private AutoScalingMode _autoScalingMode;
    private AutoSharding _autoSharding;
    private Boolean _freeFromServerless;
    private List<ObjectId> _restoreJobIds;
    private List<IndexConfig> _pendingIndexes;
    private FixedVersionView _fixedFeatureCompatibilityVersionView;
    private FixedVersionView _fixedCpuArchView;
    private FixedVersionView _fixedMongoDBVersionView;
    private FixedVersionView _fixedOsView;
    private FixedVersionView _fixedACMEProviderView;
    private List<BumperFileOverrideView> _bumperFileOverrides;
    private boolean _deleteRequested;
    private DeleteClusterReason _deleteReason;
    private RestoreJobType _restoreJobType;
    private Date _deletedDate;
    private Date _ensureClusterConnectivityAfter;
    private ProcessRestartAllowedState _needsMongoDBConfigPublishRestartAllowed;
    private Date _pausedDate;
    private LogRetention _logRetention;
    private boolean _stopContinuousBackup;
    private List<String> _forceReplicaSetReconfigHostsToSkip;
    private Integer _replicaSetVersionOverride;
    private Set<String> _migrateFromAvailabilitySets;
    private String _loadBalancedHostname;
    private String _loadBalancedMeshHostname;
    private Date _cancelShardDrainRequested;
    private OsTunedFileOverridesView _osTunedFileOverridesView;
    private String _dnsPin;
    private String _clusterNamePrefix;
    private ClusterProvisionType _clusterProvisionType;
    private Date _needsServerlessSnapshotForPit;
    private Date _needsEnvoySyncAfter;
    private Date _lastDataValidationDate;
    private Date _lastDbCheckDate;
    private Date _needsDbCheckAfter;
    private int _dbCheckPreflightRetryCount;
    private Boolean _isCriticalOSPolicyRelease;
    private List<Integer> _cpuSocketBinding;
    private Date _needsPrioritiesResetForPriorityTakeoverAfter;
    private ProxyProtocolForPrivateLinkMode _proxyProtocolForPrivateLinkMode;
    private PartnerIntegrationsData _partnerIntegrationsData;
    private Boolean _isServerlessTenantBlocked;
    private Boolean _isWriteBlocked;
    private Double _customerProvidedDiskSizeGB;
    private EffectiveFields _useEffectiveClusterFields;
    private Boolean _useAwsTimeBasedSnapshotCopyForFastInitialSync;

    private ClusterDescriptionViewBuilder() {}

    private ClusterDescriptionViewBuilder(ClusterDescriptionView pClusterDescriptionView) {
      _cloudProvider = pClusterDescriptionView._cloudProvider;
      _name = pClusterDescriptionView._name;
      _groupId = pClusterDescriptionView._groupId;
      _uniqueId = pClusterDescriptionView._uniqueId;
      _createDate = pClusterDescriptionView._createDate;
      _groupName = pClusterDescriptionView._groupName;
      _mongoDBVersion = pClusterDescriptionView._mongoDBVersion;
      _mongoDBMajorVersion = pClusterDescriptionView._mongoDBMajorVersion;
      _fixedMongoDBFCV = pClusterDescriptionView._fixedMongoDBFCV;
      _fixedMongoDBFCVExpiration = pClusterDescriptionView._fixedMongoDBFCVExpiration;
      _fixedMongoDBFCVPinnedDate = pClusterDescriptionView._fixedMongoDBFCVPinnedDate;
      _clusterType = pClusterDescriptionView._clusterType;
      _internalClusterRole = pClusterDescriptionView._internalClusterRole;
      _isMTM = pClusterDescriptionView._isMTM;
      _isMTMSentinel = pClusterDescriptionView._isMTMSentinel;
      _replicationSpecList = pClusterDescriptionView._replicationSpecList;
      _maxIncomingConns = pClusterDescriptionView._maxIncomingConns;
      _diskSizeGB = pClusterDescriptionView._diskSizeGB;
      _mongoDBUriHosts = pClusterDescriptionView._mongoDBUriHosts;
      _privateMongoDBUriHosts = pClusterDescriptionView._privateMongoDBUriHosts;
      _privateSrvAddress = pClusterDescriptionView._privateSrvAddress;
      _privateLinkMongoDBUriHosts = pClusterDescriptionView._privateLinkMongoDBUriHosts;
      _privateLinkSrvAddresses = pClusterDescriptionView._privateLinkSrvAddresses;
      _mongoDBUriHostsLastUpdateDate = pClusterDescriptionView._mongoDBUriHostsLastUpdateDate;
      _backupEnabled = pClusterDescriptionView._backupEnabled;
      _diskBackupEnabled = pClusterDescriptionView._diskBackupEnabled;
      _pitEnabled = pClusterDescriptionView._pitEnabled;
      _state = pClusterDescriptionView._state;
      _deleteAfterDate = pClusterDescriptionView._deleteAfterDate;
      _needsMongoDBConfigPublishAfter = pClusterDescriptionView._needsMongoDBConfigPublishAfter;
      _tenantUpgrading = pClusterDescriptionView._tenantUpgrading;
      _tenantUpgradingServerlessToDedicated =
          pClusterDescriptionView._tenantUpgradingServerlessToDedicated;
      _isServerlessTenantBlocked = pClusterDescriptionView._isServerlessTenantBlocked;
      _hasDedicatedMetricsAvailableForServerlessToDedicated =
          pClusterDescriptionView._hasDedicatedMetricsAvailableForServerlessToDedicated;
      _tenantDowngradingServerlessToFree =
          pClusterDescriptionView._tenantDowngradingServerlessToFree;
      _tenantAccessRevokedForPause = pClusterDescriptionView._tenantAccessRevokedForPause;
      _userNotifiedAboutPauseDate = pClusterDescriptionView._userNotifiedAboutPauseDate;
      _isPaused = pClusterDescriptionView._isPaused;
      _isUnderCompaction = pClusterDescriptionView._isUnderCompaction;
      _biConnector = pClusterDescriptionView._biConnector;
      _dataProcessingRegion = pClusterDescriptionView._dataProcessingRegion;
      _lastUpdateDate = pClusterDescriptionView._lastUpdateDate;
      _srvAddress = pClusterDescriptionView._srvAddress;
      _clusterTags = pClusterDescriptionView._clusterTags;
      _geoShardingView = pClusterDescriptionView._geoShardingView;
      _encryptionAtRestProvider = pClusterDescriptionView._encryptionAtRestProvider;
      _isNvme = pClusterDescriptionView._isNvme;
      _labels = pClusterDescriptionView._labels;
      _isMonitoringPaused = pClusterDescriptionView._isMonitoringPaused;
      _deploymentItemName = pClusterDescriptionView._deploymentItemName;
      _deploymentClusterName = pClusterDescriptionView._deploymentClusterName;
      _forceReplicaSetReconfig = pClusterDescriptionView._forceReplicaSetReconfig;
      _acceptDataRisksAndForceReplicaSetReconfig =
          pClusterDescriptionView._acceptDataRisksAndForceReplicaSetReconfig;
      _resurrectRequested = pClusterDescriptionView._resurrectRequested;
      _resurrectOptions = pClusterDescriptionView._resurrectOptions;
      _hostnameSubdomainLevel = pClusterDescriptionView._hostnameSubdomainLevel;
      _hostnameSchemeForAgents = pClusterDescriptionView._hostnameSchemeForAgents;
      _isCrossCloudCluster = pClusterDescriptionView._isCrossCloudCluster;
      _isMongoDBVersionFixed = pClusterDescriptionView._isMongoDBVersionFixed;
      _rootCertType = pClusterDescriptionView._rootCertType;
      _versionReleaseSystem = pClusterDescriptionView._versionReleaseSystem;
      _continuousDeliveryFCV = pClusterDescriptionView._continuousDeliveryFCV;
      _endpointToLoadBalancedSRVConnectionURI =
          pClusterDescriptionView._endpointToLoadBalancedSRVConnectionURI;
      _serverlessBackupOptionsView = pClusterDescriptionView._serverlessBackupOptionsView;
      _isFastProvisioned = pClusterDescriptionView._isFastProvisioned;
      _terminationProtectionEnabled = pClusterDescriptionView._terminationProtectionEnabled;
      _needsSampleDataLoadAfter = pClusterDescriptionView._needsSampleDataLoadAfter;
      _createSampleSearchIndex = pClusterDescriptionView._createSampleSearchIndex;
      _sampleDatasetToLoad = pClusterDescriptionView._sampleDatasetToLoad;
      _retainBackupsForDeleting = pClusterDescriptionView._retainBackupsForDeleting;
      _searchDeploymentRequest = pClusterDescriptionView._searchDeploymentRequest;
      _diskWarmingMode = pClusterDescriptionView._diskWarmingMode;
      _configServerType = pClusterDescriptionView._configServerType;
      _configServerManagementMode = pClusterDescriptionView._configServerManagementMode;
      _cloudProviderOptions = pClusterDescriptionView._cloudProviderOptions;
      _replicaSetScalingStrategy = pClusterDescriptionView._replicaSetScalingStrategy;
      _employeeAccessGrant = pClusterDescriptionView._employeeAccessGrant;
      _osPolicyVersion = pClusterDescriptionView._osPolicyVersion;
      _redactClientLogData = pClusterDescriptionView._redactClientLogData;
      _shardsDraining = pClusterDescriptionView._shardsDraining;
      _flexTenantMigrationState = pClusterDescriptionView._flexTenantMigrationState;
      _hasClusterLevelMetrics = pClusterDescriptionView._hasClusterLevelMetrics;
      _autoScalingMode = pClusterDescriptionView._autoScalingMode;
      _autoSharding = pClusterDescriptionView._autoSharding;
      _freeFromServerless = pClusterDescriptionView._freeFromServerless;
      _restoreJobIds = pClusterDescriptionView._restoreJobIds;
      _pendingIndexes = pClusterDescriptionView._pendingIndexes;
      _fixedFeatureCompatibilityVersionView =
          pClusterDescriptionView._fixedFeatureCompatibilityVersionView;
      _fixedCpuArchView = pClusterDescriptionView._fixedCpuArchView;
      _fixedMongoDBVersionView = pClusterDescriptionView._fixedMongoDBVersionView;
      _fixedOsView = pClusterDescriptionView._fixedOsView;
      _fixedACMEProviderView = pClusterDescriptionView._fixedACMEProviderView;
      _bumperFileOverrides = pClusterDescriptionView._bumperFileOverrides;
      _deleteRequested = pClusterDescriptionView._deleteRequested;
      _deleteReason = pClusterDescriptionView._deleteReason;
      _restoreJobType = pClusterDescriptionView._restoreJobType;
      _deletedDate = pClusterDescriptionView._deletedDate;
      _ensureClusterConnectivityAfter = pClusterDescriptionView._ensureClusterConnectivityAfter;
      _needsMongoDBConfigPublishRestartAllowed =
          pClusterDescriptionView._needsMongoDBConfigPublishRestartAllowed;
      _pausedDate = pClusterDescriptionView._pausedDate;
      _logRetention = pClusterDescriptionView._logRetention;
      _stopContinuousBackup = pClusterDescriptionView._stopContinuousBackup;
      _forceReplicaSetReconfigHostsToSkip =
          pClusterDescriptionView._forceReplicaSetReconfigHostsToSkip;
      _replicaSetVersionOverride = pClusterDescriptionView._replicaSetVersionOverride;
      _migrateFromAvailabilitySets = pClusterDescriptionView._migrateFromAvailabilitySets;
      _loadBalancedHostname = pClusterDescriptionView._loadBalancedHostname;
      _loadBalancedMeshHostname = pClusterDescriptionView._loadBalancedMeshHostname;
      _cancelShardDrainRequested = pClusterDescriptionView._cancelShardDrainRequested;
      _osTunedFileOverridesView = pClusterDescriptionView._osTunedFileOverridesView;
      _dnsPin = pClusterDescriptionView._dnsPin;
      _clusterNamePrefix = pClusterDescriptionView._clusterNamePrefix;
      _clusterProvisionType = pClusterDescriptionView._clusterProvisionType;
      _needsServerlessSnapshotForPit = pClusterDescriptionView._needsServerlessSnapshotForPit;
      _needsEnvoySyncAfter = pClusterDescriptionView._needsEnvoySyncAfter;
      _lastDataValidationDate = pClusterDescriptionView._lastDataValidationDate;
      _lastDbCheckDate = pClusterDescriptionView._lastDbCheckDate;
      _needsDbCheckAfter = pClusterDescriptionView._needsDbCheckAfter;
      _dbCheckPreflightRetryCount = pClusterDescriptionView._dbCheckPreflightRetryCount;
      _isCriticalOSPolicyRelease = pClusterDescriptionView._isCriticalOSPolicyRelease;
      _cpuSocketBinding = pClusterDescriptionView._cpuSocketBinding;
      _needsPrioritiesResetForPriorityTakeoverAfter =
          pClusterDescriptionView._needsPrioritiesResetForPriorityTakeoverAfter;
      _proxyProtocolForPrivateLinkMode = pClusterDescriptionView._proxyProtocolForPrivateLinkMode;
      _partnerIntegrationsData = pClusterDescriptionView._partnerIntegrationsData;
      _isWriteBlocked = pClusterDescriptionView._isWriteBlocked;
      _customerProvidedDiskSizeGB = pClusterDescriptionView.getCustomerProvidedDiskSizeGB();
      _useEffectiveClusterFields = pClusterDescriptionView.getUseEffectiveClusterFields();
      _useAwsTimeBasedSnapshotCopyForFastInitialSync =
          pClusterDescriptionView._useAwsTimeBasedSnapshotCopyForFastInitialSync;
    }

    public ClusterDescriptionViewBuilder cloudProvider(final String pCloudProvider) {
      _cloudProvider = pCloudProvider;
      return this;
    }

    public ClusterDescriptionViewBuilder name(final String pName) {
      _name = pName;
      return this;
    }

    public ClusterDescriptionViewBuilder groupId(final ObjectId pGroupId) {
      _groupId = pGroupId;
      return this;
    }

    public ClusterDescriptionViewBuilder uniqueId(final ObjectId pUniqueId) {
      _uniqueId = pUniqueId;
      return this;
    }

    public ClusterDescriptionViewBuilder createDate(final Date pCreateDate) {
      _createDate = pCreateDate;
      return this;
    }

    public ClusterDescriptionViewBuilder groupName(final String pGroupName) {
      _groupName = pGroupName;
      return this;
    }

    public ClusterDescriptionViewBuilder mongoDBVersion(final String pMongoDBVersion) {
      _mongoDBVersion = pMongoDBVersion;
      return this;
    }

    public ClusterDescriptionViewBuilder mongoDBMajorVersion(String pMongoDBMajorVersion) {
      _mongoDBMajorVersion = pMongoDBMajorVersion;
      return this;
    }

    public ClusterDescriptionViewBuilder fixedMongoDBFCV(String pFixedMongoDBFCV) {
      _fixedMongoDBFCV = pFixedMongoDBFCV;
      return this;
    }

    public ClusterDescriptionViewBuilder fixedMongoDBFCVExpiration(
        Date pFixedMongoDBFCVExpiration) {
      _fixedMongoDBFCVExpiration = pFixedMongoDBFCVExpiration;
      return this;
    }

    public ClusterDescriptionViewBuilder fixedMongoDBFCVPinnedDate(
        Date pFixedMongoDBFCVPinnedDate) {
      _fixedMongoDBFCVPinnedDate = pFixedMongoDBFCVPinnedDate;
      return this;
    }

    public ClusterDescriptionViewBuilder clusterType(String pClusterType) {
      _clusterType = pClusterType;
      return this;
    }

    public ClusterDescriptionViewBuilder internalClusterRole(String pInternalClusterRole) {
      _internalClusterRole = pInternalClusterRole;
      return this;
    }

    public ClusterDescriptionViewBuilder isMTM(Boolean pIsMTM) {
      _isMTM = pIsMTM;
      return this;
    }

    public ClusterDescriptionViewBuilder isMTMSentinel(Boolean pIsMTMSentinel) {
      _isMTMSentinel = pIsMTMSentinel;
      return this;
    }

    public ClusterDescriptionViewBuilder replicationSpecList(
        List<ReplicationSpecView> pReplicationSpecList) {
      _replicationSpecList = pReplicationSpecList;
      return this;
    }

    public ClusterDescriptionViewBuilder maxIncomingConns(Integer pMaxIncomingConns) {
      _maxIncomingConns = pMaxIncomingConns;
      return this;
    }

    public ClusterDescriptionViewBuilder diskSizeGB(Double pDiskSizeGB) {
      _diskSizeGB = pDiskSizeGB;
      return this;
    }

    public ClusterDescriptionViewBuilder mongoDBUriHosts(String[] pMongoDBUriHosts) {
      _mongoDBUriHosts = pMongoDBUriHosts;
      return this;
    }

    public ClusterDescriptionViewBuilder privateMongoDBUriHosts(String[] pPrivateMongoDBUriHosts) {
      _privateMongoDBUriHosts = pPrivateMongoDBUriHosts;
      return this;
    }

    public ClusterDescriptionViewBuilder privateSrvAddress(String pPrivateSrvAddress) {
      _privateSrvAddress = pPrivateSrvAddress;
      return this;
    }

    public ClusterDescriptionViewBuilder privateLinkMongoDBUriHosts(
        Map<String, List<String>> pPrivateLinkMongoDBUriHosts) {
      _privateLinkMongoDBUriHosts = pPrivateLinkMongoDBUriHosts;
      return this;
    }

    public ClusterDescriptionViewBuilder privateLinkSrvAddresses(
        Map<String, String> pPrivateLinkSrvAddresses) {
      _privateLinkSrvAddresses = pPrivateLinkSrvAddresses;
      return this;
    }

    public ClusterDescriptionViewBuilder mongoDBUriHostsLastUpdateDate(
        Date pMongoDBUriHostsLastUpdateDate) {
      _mongoDBUriHostsLastUpdateDate = pMongoDBUriHostsLastUpdateDate;
      return this;
    }

    public ClusterDescriptionViewBuilder backupEnabled(Boolean pBackupEnabled) {
      _backupEnabled = pBackupEnabled;
      return this;
    }

    public ClusterDescriptionViewBuilder diskBackupEnabled(Boolean pDiskBackupEnabled) {
      _diskBackupEnabled = pDiskBackupEnabled;
      return this;
    }

    public ClusterDescriptionViewBuilder pitEnabled(Boolean pPitEnabled) {
      _pitEnabled = pPitEnabled;
      return this;
    }

    public ClusterDescriptionViewBuilder state(State pState) {
      _state = pState;
      return this;
    }

    public ClusterDescriptionViewBuilder deleteAfterDate(Date pDeleteAfterDate) {
      _deleteAfterDate = pDeleteAfterDate;
      return this;
    }

    public ClusterDescriptionViewBuilder needsMongoDBConfigPublishAfter(
        Date pNeedsMongoDBConfigPublishAfter) {
      _needsMongoDBConfigPublishAfter = pNeedsMongoDBConfigPublishAfter;
      return this;
    }

    public ClusterDescriptionViewBuilder tenantUpgrading(Boolean pTenantUpgrading) {
      _tenantUpgrading = pTenantUpgrading;
      return this;
    }

    public ClusterDescriptionViewBuilder tenantUpgradingServerlessToDedicated(
        Boolean pTenantUpgradingServerlessToDedicated) {
      _tenantUpgradingServerlessToDedicated = pTenantUpgradingServerlessToDedicated;
      return this;
    }

    public ClusterDescriptionViewBuilder hasDedicatedMetricsAvailableForServerlessToDedicated(
        Boolean pHasDedicatedMetricsAvailableForServerlessToDedicated) {
      _hasDedicatedMetricsAvailableForServerlessToDedicated =
          pHasDedicatedMetricsAvailableForServerlessToDedicated;
      return this;
    }

    public ClusterDescriptionViewBuilder tenantDowngradingServerlessToFree(
        Boolean pTenantDowngradingServerlessToFree) {
      _tenantDowngradingServerlessToFree = pTenantDowngradingServerlessToFree;
      return this;
    }

    public ClusterDescriptionViewBuilder tenantAccessRevokedForPause(
        Boolean pTenantAccessRevokedForPause) {
      _tenantAccessRevokedForPause = pTenantAccessRevokedForPause;
      return this;
    }

    public ClusterDescriptionViewBuilder userNotifiedAboutPauseDate(
        Date pUserNotifiedAboutPauseDate) {
      _userNotifiedAboutPauseDate = pUserNotifiedAboutPauseDate;
      return this;
    }

    public ClusterDescriptionViewBuilder isPaused(Boolean pisPaused) {
      _isPaused = pisPaused;
      return this;
    }

    public ClusterDescriptionViewBuilder underCompaction(Boolean pUnderCompaction) {
      _isUnderCompaction = pUnderCompaction;
      return this;
    }

    public ClusterDescriptionViewBuilder biConnector(BiConnectorView pBiConnector) {
      _biConnector = pBiConnector;
      return this;
    }

    public ClusterDescriptionViewBuilder dataProcessingRegion(
        DataProcessingRegionView pDataProcessingRegion) {
      _dataProcessingRegion = pDataProcessingRegion;
      return this;
    }

    public ClusterDescriptionViewBuilder lastUpdateDate(Date pLastUpdateDate) {
      _lastUpdateDate = pLastUpdateDate;
      return this;
    }

    public ClusterDescriptionViewBuilder srvAddress(String pSrvAddress) {
      _srvAddress = pSrvAddress;
      return this;
    }

    public ClusterDescriptionViewBuilder clusterTags(Set<String> pClusterTags) {
      _clusterTags = pClusterTags;
      return this;
    }

    public ClusterDescriptionViewBuilder geoShardingView(GeoShardingView pGeoShardingView) {
      _geoShardingView = pGeoShardingView;
      return this;
    }

    public ClusterDescriptionViewBuilder encryptionAtRestProvider(
        EncryptionAtRestProviderView pEncryptionAtRestProvider) {
      _encryptionAtRestProvider = pEncryptionAtRestProvider;
      return this;
    }

    public ClusterDescriptionViewBuilder nvme(Boolean pNvme) {
      _isNvme = pNvme;
      return this;
    }

    public ClusterDescriptionViewBuilder labels(List<NDSLabelView> pLabels) {
      _labels = pLabels;
      return this;
    }

    public ClusterDescriptionViewBuilder monitoringPaused(Boolean pMonitoringPaused) {
      _isMonitoringPaused = pMonitoringPaused;
      return this;
    }

    public ClusterDescriptionViewBuilder deploymentItemName(String pDeploymentItemName) {
      _deploymentItemName = pDeploymentItemName;
      return this;
    }

    public ClusterDescriptionViewBuilder deploymentClusterName(String pDeploymentClusterName) {
      _deploymentClusterName = pDeploymentClusterName;
      return this;
    }

    public ClusterDescriptionViewBuilder forceReplicaSetReconfig(Boolean pForceReplicaSetReconfig) {
      _forceReplicaSetReconfig = pForceReplicaSetReconfig;
      return this;
    }

    public ClusterDescriptionViewBuilder acceptDataRisksAndForceReplicaSetReconfig(
        Date pAcceptDataRisksAndForceReplicaSetReconfig) {
      _acceptDataRisksAndForceReplicaSetReconfig = pAcceptDataRisksAndForceReplicaSetReconfig;
      return this;
    }

    public ClusterDescriptionViewBuilder resurrectRequested(Date pResurrectRequested) {
      _resurrectRequested = pResurrectRequested;
      return this;
    }

    public ClusterDescriptionViewBuilder resurrectOptions(ResurrectOptionsView pResurrectOptions) {
      _resurrectOptions = pResurrectOptions;
      return this;
    }

    public ClusterDescriptionViewBuilder hostnameSubdomainLevel(String pHostnameSubdomainLevel) {
      _hostnameSubdomainLevel = pHostnameSubdomainLevel;
      return this;
    }

    public ClusterDescriptionViewBuilder hostnameSchemeForAgents(String pHostnameSchemeForAgents) {
      _hostnameSchemeForAgents = pHostnameSchemeForAgents;
      return this;
    }

    public ClusterDescriptionViewBuilder crossCloudCluster(Boolean pCrossCloudCluster) {
      _isCrossCloudCluster = pCrossCloudCluster;
      return this;
    }

    public ClusterDescriptionViewBuilder mongoDBVersionFixed(Boolean pMongoDBVersionFixed) {
      _isMongoDBVersionFixed = pMongoDBVersionFixed;
      return this;
    }

    public ClusterDescriptionViewBuilder rootCertType(RootCertType pRootCertType) {
      _rootCertType = pRootCertType;
      return this;
    }

    public ClusterDescriptionViewBuilder versionReleaseSystem(
        VersionReleaseSystem pVersionReleaseSystem) {
      _versionReleaseSystem = pVersionReleaseSystem;
      return this;
    }

    public ClusterDescriptionViewBuilder continuousDeliveryFCV(String pContinuousDeliveryFCV) {
      _continuousDeliveryFCV = pContinuousDeliveryFCV;
      return this;
    }

    public ClusterDescriptionViewBuilder endpointToLoadBalancedSRVConnectionURI(
        Map<String, String> pEndpointToLoadBalancedSRVConnectionURI) {
      _endpointToLoadBalancedSRVConnectionURI = pEndpointToLoadBalancedSRVConnectionURI;
      return this;
    }

    public ClusterDescriptionViewBuilder serverlessBackupOptionsView(
        ServerlessBackupOptionsView pServerlessBackupOptionsView) {
      _serverlessBackupOptionsView = pServerlessBackupOptionsView;
      return this;
    }

    public ClusterDescriptionViewBuilder fastProvisioned(boolean pFastProvisioned) {
      _isFastProvisioned = pFastProvisioned;
      return this;
    }

    public ClusterDescriptionViewBuilder terminationProtectionEnabled(
        Boolean pTerminationProtectionEnabled) {
      _terminationProtectionEnabled = pTerminationProtectionEnabled;
      return this;
    }

    public ClusterDescriptionViewBuilder needsSampleDataLoadAfter(Date pNeedsSampleDataLoadAfter) {
      _needsSampleDataLoadAfter = pNeedsSampleDataLoadAfter;
      return this;
    }

    public ClusterDescriptionViewBuilder createSampleSearchIndex(boolean pCreateSampleSearchIndex) {
      _createSampleSearchIndex = pCreateSampleSearchIndex;
      return this;
    }

    public ClusterDescriptionViewBuilder sampleDatasetToLoad(SampleDataset pSampleDatasetToLoad) {
      _sampleDatasetToLoad = pSampleDatasetToLoad;
      return this;
    }

    public ClusterDescriptionViewBuilder retainBackupsForDeleting(
        Boolean pRetainBackupsForDeleting) {
      _retainBackupsForDeleting = pRetainBackupsForDeleting;
      return this;
    }

    public ClusterDescriptionViewBuilder searchDeploymentRequest(
        ApiSearchDeploymentRequestView pSearchDeploymentRequest) {
      _searchDeploymentRequest = pSearchDeploymentRequest;
      return this;
    }

    public ClusterDescriptionViewBuilder diskWarmingMode(DiskWarmingMode pDiskWarmingMode) {
      _diskWarmingMode = pDiskWarmingMode;
      return this;
    }

    public ClusterDescriptionViewBuilder configServerType(ConfigServerType pConfigServerType) {
      _configServerType = pConfigServerType;
      return this;
    }

    public ClusterDescriptionViewBuilder configServerManagementMode(
        ConfigServerManagementMode pConfigServerManagementMode) {
      _configServerManagementMode = pConfigServerManagementMode;
      return this;
    }

    public ClusterDescriptionViewBuilder cloudProviderOptions(
        final TenantOptionsView pCloudProviderOptions) {
      _cloudProviderOptions = pCloudProviderOptions;
      return this;
    }

    public ClusterDescriptionViewBuilder replicaSetScalingStrategy(
        final ReplicaSetScalingStrategy pReplicaSetScalingStrategy) {
      _replicaSetScalingStrategy = pReplicaSetScalingStrategy;
      return this;
    }

    public ClusterDescriptionViewBuilder employeeAccessGrant(
        final EmployeeAccessGrantView pEmployeeAccessGrant) {
      _employeeAccessGrant = pEmployeeAccessGrant;
      return this;
    }

    public ClusterDescriptionViewBuilder redactClientLogData(final Boolean pRedactClientLogData) {
      _redactClientLogData = pRedactClientLogData;
      return this;
    }

    public ClusterDescriptionViewBuilder shardsDraining(final List<String> pShardsDraining) {
      _shardsDraining = pShardsDraining;
      return this;
    }

    public ClusterDescriptionViewBuilder flexTenantMigrationState(
        final FlexTenantMigrationState pFlexTenantMigrationState) {
      _flexTenantMigrationState = pFlexTenantMigrationState;
      return this;
    }

    public ClusterDescriptionViewBuilder hasClusterLevelMetrics(
        final boolean pHasClusterLevelMetrics) {
      _hasClusterLevelMetrics = pHasClusterLevelMetrics;
      return this;
    }

    public ClusterDescriptionViewBuilder storageSystem(final StorageSystem pStorageSystem) {
      _storageSystem = pStorageSystem;
      return this;
    }

    public ClusterDescriptionViewBuilder autoScalingMode(final AutoScalingMode pAutoScalingMode) {
      _autoScalingMode = pAutoScalingMode;
      return this;
    }

    /**
     * Sets the auto-sharding configuration for the cluster being built.
     *
     * @param pAutoSharding the auto-sharding configuration
     * @return this builder instance for method chaining
     */
    public ClusterDescriptionViewBuilder autoSharding(final AutoSharding pAutoSharding) {
      _autoSharding = pAutoSharding;
      return this;
    }

    public ClusterDescriptionViewBuilder freeFromServerless(final Boolean pFreeFromServerless) {
      _freeFromServerless = pFreeFromServerless;
      return this;
    }

    public ClusterDescriptionViewBuilder partnerIntegrationsData(
        final PartnerIntegrationsData pPartnerIntegrationsData) {
      _partnerIntegrationsData = pPartnerIntegrationsData;
      return this;
    }

    /** Builds a ClusterDescriptionView with the given isWriteBlocked value. */
    public ClusterDescriptionViewBuilder isWriteBlocked(final Boolean pIsWriteBlocked) {
      _isWriteBlocked = pIsWriteBlocked;
      return this;
    }

    /**
     * Sets the customer-provided disk size in gigabytes for the cluster.
     *
     * @param pCustomerProvidedDiskSizeGB the disk size in GB as specified by the customer, may be
     *     null
     * @return this builder instance for method chaining
     */
    public ClusterDescriptionViewBuilder customerProvidedDiskSizeGB(
        final Double pCustomerProvidedDiskSizeGB) {
      _customerProvidedDiskSizeGB = pCustomerProvidedDiskSizeGB;
      return this;
    }

    /**
     * Sets whether effective cluster fields should be used in cluster operations.
     *
     * <p>Effective cluster fields provide a mechanism to override or supplement the standard
     * cluster configuration with computed or derived values. When enabled, the system will use
     * effective field values that may be calculated based on current cluster state, policies, or
     * other dynamic factors. When disabled, only the standard cluster configuration fields are
     * used.
     *
     * @param pUseEffectiveClusterFields the effective fields setting - ENABLED to use computed
     *     values, DISABLED to use only standard configuration fields
     * @return this builder instance for method chaining
     */
    public ClusterDescriptionViewBuilder useEffectiveClusterFields(
        final EffectiveFields pUseEffectiveClusterFields) {
      _useEffectiveClusterFields = pUseEffectiveClusterFields;
      return this;
    }

    /**
     * Builds a ClusterDescriptionView with the given useAwsTimeBasedSnapshotCopyForFastInitialSync
     * value.
     */
    public ClusterDescriptionViewBuilder useAwsTimeBasedSnapshotCopyForFastInitialSync(
        final Boolean pUseAwsTimeBasedSnapshotCopyForFastInitialSync) {
      _useAwsTimeBasedSnapshotCopyForFastInitialSync =
          pUseAwsTimeBasedSnapshotCopyForFastInitialSync;
      return this;
    }

    public ClusterDescriptionView build() {
      return new ClusterDescriptionView(
          _cloudProvider,
          _name,
          _groupId,
          _uniqueId,
          _createDate,
          _groupName,
          _mongoDBVersion,
          _mongoDBMajorVersion,
          _fixedMongoDBFCV,
          _fixedMongoDBFCVExpiration,
          _fixedMongoDBFCVPinnedDate,
          _clusterType,
          _internalClusterRole,
          _isMTM,
          _isMTMSentinel,
          _replicationSpecList,
          _maxIncomingConns,
          _diskSizeGB,
          _mongoDBUriHosts,
          _privateMongoDBUriHosts,
          _privateSrvAddress,
          _privateLinkMongoDBUriHosts,
          _privateLinkSrvAddresses,
          _mongoDBUriHostsLastUpdateDate,
          _backupEnabled,
          _diskBackupEnabled,
          _pitEnabled,
          _state,
          _deleteAfterDate,
          _needsMongoDBConfigPublishAfter,
          _tenantUpgrading,
          _tenantUpgradingServerlessToDedicated,
          _isServerlessTenantBlocked,
          _hasDedicatedMetricsAvailableForServerlessToDedicated,
          _tenantDowngradingServerlessToFree,
          _tenantAccessRevokedForPause,
          _userNotifiedAboutPauseDate,
          _isPaused,
          _isUnderCompaction,
          _biConnector,
          _dataProcessingRegion,
          _lastUpdateDate,
          _srvAddress,
          _clusterTags,
          _geoShardingView,
          _encryptionAtRestProvider,
          _isNvme,
          _labels,
          _isMonitoringPaused,
          _deploymentItemName,
          _deploymentClusterName,
          _forceReplicaSetReconfig,
          _acceptDataRisksAndForceReplicaSetReconfig,
          _resurrectRequested,
          _resurrectOptions,
          _hostnameSubdomainLevel,
          _hostnameSchemeForAgents,
          _isCrossCloudCluster,
          _isMongoDBVersionFixed,
          _rootCertType,
          _versionReleaseSystem,
          _continuousDeliveryFCV,
          _endpointToLoadBalancedSRVConnectionURI,
          _serverlessBackupOptionsView,
          _isFastProvisioned,
          _terminationProtectionEnabled,
          _needsSampleDataLoadAfter,
          _createSampleSearchIndex,
          _sampleDatasetToLoad,
          _retainBackupsForDeleting,
          _searchDeploymentRequest,
          _diskWarmingMode,
          _configServerType,
          _configServerManagementMode,
          _cloudProviderOptions,
          _replicaSetScalingStrategy,
          _employeeAccessGrant,
          _osPolicyVersion,
          _redactClientLogData,
          _shardsDraining,
          _flexTenantMigrationState,
          _hasClusterLevelMetrics,
          _storageSystem,
          _autoScalingMode,
          _freeFromServerless,
          _restoreJobIds,
          _pendingIndexes,
          _fixedFeatureCompatibilityVersionView,
          _fixedCpuArchView,
          _fixedMongoDBVersionView,
          _fixedOsView,
          _fixedACMEProviderView,
          _bumperFileOverrides,
          _deleteRequested,
          _deleteReason,
          _restoreJobType,
          _deletedDate,
          _ensureClusterConnectivityAfter,
          _needsMongoDBConfigPublishRestartAllowed,
          _pausedDate,
          _logRetention,
          _stopContinuousBackup,
          _forceReplicaSetReconfigHostsToSkip,
          _replicaSetVersionOverride,
          _migrateFromAvailabilitySets,
          _loadBalancedHostname,
          _loadBalancedMeshHostname,
          _cancelShardDrainRequested,
          _osTunedFileOverridesView,
          _dnsPin,
          _clusterNamePrefix,
          _clusterProvisionType,
          _needsServerlessSnapshotForPit,
          _needsEnvoySyncAfter,
          _lastDataValidationDate,
          _lastDbCheckDate,
          _needsDbCheckAfter,
          _dbCheckPreflightRetryCount,
          _isCriticalOSPolicyRelease,
          _cpuSocketBinding,
          _needsPrioritiesResetForPriorityTakeoverAfter,
          _proxyProtocolForPrivateLinkMode,
          _partnerIntegrationsData,
          _isWriteBlocked,
          _autoSharding,
          _useAwsTimeBasedSnapshotCopyForFastInitialSync,
          _customerProvidedDiskSizeGB,
          _useEffectiveClusterFields);
    }
  }
}
