package com.xgen.svc.nds.model.ui;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSGroupMaintenanceWindow;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;

public record NDSGroupMaintenanceWindowAdminView(
    @JsonProperty("dayOfWeek") Integer dayOfWeek,
    @JsonProperty("hourOfDay") Integer hourOfDay,
    @JsonProperty("numberOfDeferrals") Integer numberOfDeferrals,
    @JsonProperty("isUserDefined") Boolean isUserDefined,
    @JsonProperty("isAutoDeferEnabled") Boolean isAutoDeferEnabled,
    @JsonProperty("deferralGrantJiraTicket") String deferralGrantJiraTicket,
    @JsonProperty("deferralRequestDate") Date deferralRequestDate,
    @JsonProperty("advanceNotificationSendDate") Date advanceNotificationSendDate,
    @JsonProperty("previousAdvancedNotificationSendDate") Date previousAdvancedNotificationSendDate,
    @JsonProperty("maintenanceStartedNotificationSentDate")
        Date maintenanceStartedNotificationSentDate,
    @JsonProperty("lastMaintenanceCompletionDate") Date lastMaintenanceCompletionDate,
    @JsonProperty("lastMaintenanceDate") Date lastMaintenanceDate,
    @JsonProperty("lastMaintenanceStartedDate") Date lastMaintenanceStartedDate,
    @JsonProperty("lastMaintenanceDurationMinutes") Long lastMaintenanceDurationMinutes,
    @JsonProperty("groupTimeZoneId") String groupTimeZoneId,
    @JsonProperty("groupId") ObjectId groupId,
    @JsonProperty("groupName") String groupName,
    @JsonProperty("schedulingMetadata") List<SchedulingMetadataView> schedulingMetadata) {

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private Integer dayOfWeek;
    private Integer hourOfDay;
    private Integer numberOfDeferrals;
    private Boolean isUserDefined;
    private Boolean isAutoDeferEnabled;
    private String deferralGrantJiraTicket;
    private Date deferralRequestDate;
    private Date advanceNotificationSendDate;
    private Date previousAdvancedNotificationSendDate;
    private Date maintenanceStartedNotificationSentDate;
    private Date lastMaintenanceCompletionDate;
    private Date lastMaintenanceDate;
    private String groupTimeZoneId;
    private ObjectId groupId;
    private String groupName;
    private List<SchedulingMetadataView> schedulingMetadata;
    private Date lastMaintenanceStartedDate;
    private Long lastMaintenanceDurationMinutes;

    private Builder() {}

    public Builder from(
        final ObjectId groupId, final NDSGroupMaintenanceWindow ndsGroupMaintenanceWindow) {
      this.dayOfWeek = ndsGroupMaintenanceWindow.getDayOfWeek();
      this.hourOfDay =
          ndsGroupMaintenanceWindow.isUserDefined()
              ? ndsGroupMaintenanceWindow.getHourOfDay()
              : NDSGroup.getMaintenanceSystemHourOfDay(groupId, ndsGroupMaintenanceWindow);
      this.numberOfDeferrals = ndsGroupMaintenanceWindow.getNumberOfDeferrals();
      this.isUserDefined = ndsGroupMaintenanceWindow.isUserDefined();
      this.isAutoDeferEnabled = ndsGroupMaintenanceWindow.getAutoDeferEnabled();
      this.deferralGrantJiraTicket =
          ndsGroupMaintenanceWindow.getDeferralGrantJiraTicket().orElse(null);
      this.deferralRequestDate = ndsGroupMaintenanceWindow.getDeferralRequestDate().orElse(null);
      this.advanceNotificationSendDate =
          ndsGroupMaintenanceWindow.getAdvanceNotificationSendDate().orElse(null);
      this.previousAdvancedNotificationSendDate =
          ndsGroupMaintenanceWindow.getPreviousAdvanceNotificationSendDate().orElse(null);
      this.lastMaintenanceDate = ndsGroupMaintenanceWindow.getLastMaintenanceDate().orElse(null);
      this.lastMaintenanceCompletionDate =
          ndsGroupMaintenanceWindow.getLastMaintenanceCompletionDate().orElse(null);
      this.lastMaintenanceStartedDate =
          ndsGroupMaintenanceWindow.getLastMaintenanceStartedDate().orElse(null);
      this.lastMaintenanceDurationMinutes =
          ndsGroupMaintenanceWindow.getLastMaintenanceDurationMinutes().orElse(null);
      return this;
    }

    public Builder setDayOfWeek(final Integer dayOfWeek) {
      this.dayOfWeek = dayOfWeek;
      return this;
    }

    public Builder setHourOfDay(final Integer hourOfDay) {
      this.hourOfDay = hourOfDay;
      return this;
    }

    public Builder setNumberOfDeferrals(final Integer numberOfDeferrals) {
      this.numberOfDeferrals = numberOfDeferrals;
      return this;
    }

    public Builder setIsUserDefined(final Boolean isUserDefined) {
      this.isUserDefined = isUserDefined;
      return this;
    }

    public Builder setIsAutoDeferEnabled(final Boolean isAutoDeferEnabled) {
      this.isAutoDeferEnabled = isAutoDeferEnabled;
      return this;
    }

    public Builder setDeferralGrantJiraTicket(final String deferralGrantJiraTicket) {
      this.deferralGrantJiraTicket = deferralGrantJiraTicket;
      return this;
    }

    public Builder setDeferralRequestDate(final Date deferralRequestDate) {
      this.deferralRequestDate = deferralRequestDate;
      return this;
    }

    public Builder setAdvanceNotificationSendDate(final Date advanceNotificationSendDate) {
      this.advanceNotificationSendDate = advanceNotificationSendDate;
      return this;
    }

    public Builder setPreviousAdvancedNotificationSendDate(
        final Date previousAdvancedNotificationSendDate) {
      this.previousAdvancedNotificationSendDate = previousAdvancedNotificationSendDate;
      return this;
    }

    public Builder setMaintenanceStartedNotificationSentDate(
        final Date maintenanceStartedNotificationSentDate) {
      this.maintenanceStartedNotificationSentDate = maintenanceStartedNotificationSentDate;
      return this;
    }

    public Builder setLastMaintenanceCompletionDate(final Date latestMaintenanceCompletionDate) {
      this.lastMaintenanceCompletionDate = latestMaintenanceCompletionDate;
      return this;
    }

    public Builder setLastMaintenanceDate(final Date latestMaintenanceDate) {
      this.lastMaintenanceDate = latestMaintenanceDate;
      return this;
    }

    public Builder setGroupId(final ObjectId groupId) {
      this.groupId = groupId;
      return this;
    }

    public Builder setGroupName(final String groupName) {
      this.groupName = groupName;
      return this;
    }

    public Builder setGroupTimeZoneId(final String pGroupTimeZoneId) {
      groupTimeZoneId = pGroupTimeZoneId;
      return this;
    }

    public Builder setSchedulingMetadata(final List<SchedulingMetadataView> pSchedulingMetadata) {
      schedulingMetadata = pSchedulingMetadata;
      return this;
    }

    public NDSGroupMaintenanceWindowAdminView build() {
      return new NDSGroupMaintenanceWindowAdminView(
          dayOfWeek,
          hourOfDay,
          numberOfDeferrals,
          isUserDefined,
          isAutoDeferEnabled,
          deferralGrantJiraTicket,
          deferralRequestDate,
          advanceNotificationSendDate,
          previousAdvancedNotificationSendDate,
          maintenanceStartedNotificationSentDate,
          lastMaintenanceCompletionDate,
          lastMaintenanceDate,
          lastMaintenanceStartedDate,
          lastMaintenanceDurationMinutes,
          groupTimeZoneId,
          groupId,
          groupName,
          schedulingMetadata);
    }
  }
}
