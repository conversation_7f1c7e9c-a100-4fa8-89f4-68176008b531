package com.xgen.svc.nds.model.ui;

import com.amazonaws.services.ec2.model.VolumeType;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType.PreferredStorageType;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionNameHelper;
import com.xgen.cloud.nds.cloudprovider._public.model.ShardRegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.AutoScalingView;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.flex._public.model.FlexHardwareSpec;
import com.xgen.cloud.nds.free._public.model.FreeHardwareSpec;
import com.xgen.cloud.nds.gcp._public.model.GCPHardwareSpec;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._public.model.RegionSpec;
import com.xgen.cloud.nds.project._public.util.InstanceSizeUtil;
import com.xgen.cloud.nds.serverless._public.model.ServerlessHardwareSpec;
import jakarta.annotation.Nullable;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public class RegionConfigView {

  public static final int DEFAULT_ELECTABLE_NODES = 3;
  public static final int DEFAULT_READ_ONLY_NODES = 0;
  public static final int DEFAULT_ANALYTICS_NODES = 0;

  @JsonProperty(FieldDefs.REGION_NAME)
  private String _regionName;

  @JsonProperty(FieldDefs.CLOUD_PROVIDER)
  private String _cloudProvider;

  @JsonProperty(FieldDefs.AUTOSCALING)
  protected AutoScalingView _baseAutoScaling;

  @JsonProperty(FieldDefs.ANALYTICS_AUTOSCALING)
  protected AutoScalingView _analyticsAutoScaling;

  @JsonProperty(FieldDefs.PRIORITY)
  private Integer _priority;

  @JsonProperty(FieldDefs.ELECTABLE_SPECS)
  protected HardwareSpecView _electableSpecs;

  @JsonProperty(FieldDefs.ANALYTICS_SPECS)
  protected HardwareSpecView _analyticsSpecs;

  @JsonProperty(FieldDefs.READ_ONLY_SPECS)
  protected HardwareSpecView _readOnlySpecs;

  @JsonProperty(FieldDefs.REGION_VIEW)
  private RegionView _regionView;

  @JsonProperty(FieldDefs.CUSTOMER_PROVIDED_ELECTABLE_SPECS)
  @Nullable
  private HardwareSpecView _customerProvidedElectableSpecs;

  @JsonProperty(FieldDefs.CUSTOMER_PROVIDED_ANALYTICS_SPECS)
  @Nullable
  private HardwareSpecView _customerProvidedAnalyticsSpecs;

  @JsonProperty(FieldDefs.CUSTOMER_PROVIDED_READ_ONLY_SPECS)
  @Nullable
  private HardwareSpecView _customerProvidedReadOnlySpecs;

  public RegionConfigView() {}

  @VisibleForTesting
  // We should not use this in UI / API facing calls -- diskSize is necessary below to correctly
  // calculate GCP IOPS which are not persisted on the hardware
  public RegionConfigView(final RegionConfig pRegionConfig) {
    this(pRegionConfig, null, null);
  }

  /**
   * Constructs a RegionConfigView from a RegionConfig with disk size information.
   *
   * <p>This constructor creates a view representation of a region configuration, including hardware
   * specifications for different node types (electable, analytics, read-only). The disk size
   * parameters are used to correctly calculate cloud provider-specific metrics like GCP IOPS which
   * are not persisted in the hardware specifications.
   *
   * @param pRegionConfig the region configuration to convert to a view
   * @param pDiskSize the current disk size in GB used for calculating hardware metrics, may be null
   * @param pCustomerProvidedDiskSize the original customer-specified disk size in GB, may be null
   */
  public RegionConfigView(
      final RegionConfig pRegionConfig,
      final Double pDiskSize,
      @Nullable final Double pCustomerProvidedDiskSize) {
    _regionName = pRegionConfig.getRegionName().getName();
    _cloudProvider = pRegionConfig.getCloudProvider().name();
    _baseAutoScaling =
        Optional.ofNullable(pRegionConfig.getBaseAutoScaling())
            .map(AutoScaling::toAutoScalingView)
            .orElseGet(
                () -> AutoScalingView.getDisabledAutoScalingView(pRegionConfig.getCloudProvider()));
    _analyticsAutoScaling =
        Optional.ofNullable(pRegionConfig.getAnalyticsAutoScaling())
            .map(AutoScaling::toAutoScalingView)
            .orElse(null);
    _priority = pRegionConfig.getPriority();
    _electableSpecs = new HardwareSpecView(pRegionConfig.getElectableSpecs(), pDiskSize);
    if (pRegionConfig instanceof ShardRegionConfig shardRegionConfig) {
      _analyticsSpecs = new HardwareSpecView(shardRegionConfig.getAnalyticsSpecs(), pDiskSize);
      _readOnlySpecs = new HardwareSpecView(shardRegionConfig.getReadOnlySpecs(), pDiskSize);
      _customerProvidedElectableSpecs =
          (shardRegionConfig.getCustomerProvidedElectableSpecs() == null)
              ? null
              : new HardwareSpecView(
                  shardRegionConfig.getCustomerProvidedElectableSpecs(), pCustomerProvidedDiskSize);
      _customerProvidedAnalyticsSpecs =
          (shardRegionConfig.getCustomerProvidedAnalyticsSpecs() == null)
              ? null
              : new HardwareSpecView(
                  shardRegionConfig.getCustomerProvidedAnalyticsSpecs(), pCustomerProvidedDiskSize);
      _customerProvidedReadOnlySpecs =
          (shardRegionConfig.getCustomerProvidedReadOnlySpecs() == null)
              ? null
              : new HardwareSpecView(
                  shardRegionConfig.getCustomerProvidedReadOnlySpecs(), pCustomerProvidedDiskSize);
    } else {
      _analyticsSpecs =
          new HardwareSpecView(
              pRegionConfig.getElectableSpecs().copy().setNodeCount(0).build(), pDiskSize);
      _readOnlySpecs =
          new HardwareSpecView(
              pRegionConfig.getElectableSpecs().copy().setNodeCount(0).build(), pDiskSize);
    }
    _regionView =
        switch (pRegionConfig.getCloudProvider()) {
          case AWS -> new AWSRegionView((AWSRegionName) pRegionConfig.getRegionName());
          case GCP -> new GCPRegionView((GCPRegionName) pRegionConfig.getRegionName());
          default -> new RegionView(pRegionConfig.getRegionName());
        };
  }

  public RegionConfigView(
      final CloudProvider pCloudProvider,
      final RegionName pRegionName,
      final AutoScalingView pAutoScalingView,
      final AutoScalingView pAnalyticsAutoScalingView,
      final Integer pPriority,
      final HardwareSpecView pElectableSpecs,
      final HardwareSpecView pAnalyticsSpecs,
      final HardwareSpecView pReadOnlySpecs,
      @Nullable final HardwareSpecView pCustomerProvidedElectableSpecs,
      @Nullable final HardwareSpecView pCustomerProvidedAnalyticsSpecs,
      @Nullable final HardwareSpecView pCustomerProvidedReadOnlySpecs) {
    _regionName = pRegionName.getName();
    _cloudProvider = pCloudProvider.name();
    _baseAutoScaling = pAutoScalingView;
    _analyticsAutoScaling = pAnalyticsAutoScalingView;
    _priority = pPriority;
    _electableSpecs = pElectableSpecs;
    _analyticsSpecs = pAnalyticsSpecs;
    _readOnlySpecs = pReadOnlySpecs;
    _customerProvidedElectableSpecs = pCustomerProvidedElectableSpecs;
    _customerProvidedAnalyticsSpecs = pCustomerProvidedAnalyticsSpecs;
    _customerProvidedReadOnlySpecs = pCustomerProvidedReadOnlySpecs;
    _regionView =
        switch (pCloudProvider) {
          case AWS -> new AWSRegionView((AWSRegionName) pRegionName);
          case GCP -> new GCPRegionView((GCPRegionName) pRegionName);
          default -> new RegionView(pRegionName);
        };
  }

  public static RegionConfigView getDefaultRegionConfigView(
      final RegionName pRegionName,
      final Map<NodeType, InstanceSize> pInstanceSizeMap,
      final boolean pEnableComputeAutoScale) {
    return getDefaultRegionConfigView(
        pRegionName,
        pInstanceSizeMap,
        DEFAULT_ELECTABLE_NODES,
        DEFAULT_ANALYTICS_NODES,
        DEFAULT_READ_ONLY_NODES,
        RegionConfig.MAX_PRIORITY,
        pEnableComputeAutoScale);
  }

  public static RegionConfigView getDefaultRegionConfigView(
      final RegionName pRegionName,
      final Map<NodeType, InstanceSize> pInstanceSizeMap,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pPriority,
      final boolean pEnableComputeAutoScale) {
    final InstanceSize baseInstanceSize = pInstanceSizeMap.get(NodeType.ELECTABLE);
    final InstanceSize analyticsInstanceSize =
        pInstanceSizeMap.getOrDefault(NodeType.ANALYTICS, baseInstanceSize);
    final AutoScalingView analyticsView =
        pEnableComputeAutoScale
            ? AutoScalingView.getDefaultAutoScalingViewWithComputeEnabled(
                analyticsInstanceSize.getCloudProvider(), analyticsInstanceSize)
            : AutoScalingView.getDefaultAutoScalingView(analyticsInstanceSize.getCloudProvider());

    if (baseInstanceSize.getCloudProvider().isTenantProvider()) {
      return new RegionConfigView(
          baseInstanceSize.getCloudProvider(),
          pRegionName,
          AutoScalingView.getDefaultAutoScalingView(baseInstanceSize.getCloudProvider()),
          analyticsView,
          pPriority,
          HardwareSpecViewUtils.getDefaultTenantHardwareSpecView(
              pRegionName.getProvider(), pElectableNodes, baseInstanceSize),
          HardwareSpecViewUtils.getDefaultTenantHardwareSpecView(
              pRegionName.getProvider(), pAnalyticsNodes, baseInstanceSize),
          HardwareSpecViewUtils.getDefaultTenantHardwareSpecView(
              pRegionName.getProvider(), pReadOnlyNodes, baseInstanceSize),
          null,
          null,
          null);
    } else {
      return new RegionConfigView(
          baseInstanceSize.getCloudProvider(),
          pRegionName,
          pEnableComputeAutoScale
              ? AutoScalingView.getDefaultAutoScalingViewWithComputeEnabled(
                  baseInstanceSize.getCloudProvider(), baseInstanceSize)
              : AutoScalingView.getDefaultAutoScalingView(baseInstanceSize.getCloudProvider()),
          analyticsView,
          pPriority,
          HardwareSpecViewUtils.getDefaultDedicatedHardwareSpecView(
              pElectableNodes, baseInstanceSize, pRegionName),
          HardwareSpecViewUtils.getDefaultDedicatedHardwareSpecView(
              pAnalyticsNodes, analyticsInstanceSize, pRegionName),
          HardwareSpecViewUtils.getDefaultDedicatedHardwareSpecView(
              pReadOnlyNodes, baseInstanceSize, pRegionName),
          null,
          null,
          null);
    }
  }

  @VisibleForTesting
  protected ShardRegionConfig getAWSRegionConfig(
      final CpuArchitecture pPreferredCpuArch, final double pDiskSizeGB) {
    if (_regionName.isEmpty()) {
      throw new IllegalArgumentException("Region Name is empty.");
    }
    final AWSRegionName regionName =
        AWSRegionName.findByName(_regionName)
            .orElseThrow(() -> new IllegalArgumentException("Invalid Region Name: " + _regionName));
    final AWSHardwareSpec electableSpec =
        _electableSpecs.toAWSHardwareSpec(pPreferredCpuArch, pDiskSizeGB, regionName);
    final AWSHardwareSpec readOnlySpec =
        _readOnlySpecs.toAWSHardwareSpec(pPreferredCpuArch, pDiskSizeGB, regionName);
    final AWSHardwareSpec analyticsSpec =
        _analyticsSpecs.toAWSHardwareSpec(pPreferredCpuArch, pDiskSizeGB, regionName);
    final AWSNDSInstanceSize awsInstanceSize = electableSpec.getInstanceSize();
    final int hiddenSecondaryCount =
        awsInstanceSize != null && awsInstanceSize.isNVMe() && _priority == RegionSpec.MAX_PRIORITY
            ? 1
            : 0;

    final AWSHardwareSpec hiddenSecSpec =
        new AWSHardwareSpec(
            hiddenSecondaryCount,
            awsInstanceSize,
            null,
            null,
            pPreferredCpuArch,
            awsInstanceSize.getBackupDiskIOPS().orElse(0),
            500, // Set throughput to 500 for all AWS hidden node instance sizes
            VolumeType.Gp3,
            true);

    return new ShardRegionConfig(
        regionName,
        CloudProvider.AWS,
        _baseAutoScaling.toAutoScaling(CloudProvider.AWS),
        _analyticsAutoScaling != null
            ? _analyticsAutoScaling.toAutoScaling(CloudProvider.AWS)
            : null,
        _priority,
        electableSpec,
        analyticsSpec,
        readOnlySpec,
        hiddenSecSpec,
        null,
        null,
        null,
        null);
  }

  @VisibleForTesting
  protected ShardRegionConfig getAzureRegionConfig(
      final double pDiskSizeGB,
      final boolean pSupportsAzureSsdV2,
      final PreferredStorageType pPreferredStorageType) {
    if (_regionName.isEmpty()) {
      throw new IllegalArgumentException("Region Name is empty.");
    }
    final RegionName regionName =
        AzureRegionName.findByName(_regionName)
            .orElseThrow(() -> new IllegalArgumentException("Invalid Region Name: " + _regionName));
    final AzureHardwareSpec electableSpec =
        _electableSpecs.toAzureHardwareSpec(
            pDiskSizeGB, pSupportsAzureSsdV2, pPreferredStorageType);
    final AzureHardwareSpec readOnlySpec =
        _readOnlySpecs.toAzureHardwareSpec(pDiskSizeGB, pSupportsAzureSsdV2, pPreferredStorageType);
    final AzureHardwareSpec analyticsSpec =
        _analyticsSpecs.toAzureHardwareSpec(
            pDiskSizeGB, pSupportsAzureSsdV2, pPreferredStorageType);

    final AzureNDSInstanceSize azureInstanceSize = electableSpec.getInstanceSize();
    final int hiddenSecondaryCount =
        azureInstanceSize != null
                && azureInstanceSize.isNVMe()
                && _priority == RegionSpec.MAX_PRIORITY
            ? 1
            : 0;

    final AzureHardwareSpec hiddenSecSpec =
        electableSpec.copy().setNodeCount(hiddenSecondaryCount).build();

    return new ShardRegionConfig(
        regionName,
        CloudProvider.AZURE,
        _baseAutoScaling.toAutoScaling(CloudProvider.AZURE),
        _analyticsAutoScaling != null
            ? _analyticsAutoScaling.toAutoScaling(CloudProvider.AZURE)
            : null,
        _priority,
        electableSpec,
        analyticsSpec,
        readOnlySpec,
        hiddenSecSpec,
        null,
        null,
        null,
        null);
  }

  @VisibleForTesting
  protected ShardRegionConfig getGCPRegionConfig(final CpuArchitecture pPreferredCpuArch) {
    if (_regionName.isEmpty()) {
      throw new IllegalArgumentException("Region Name is empty.");
    }
    final RegionName regionName =
        GCPRegionName.findByName(_regionName)
            .orElseThrow(() -> new IllegalArgumentException("Invalid Region Name: " + _regionName));
    final GCPHardwareSpec electableSpec = _electableSpecs.toGCPHardwareSpec(pPreferredCpuArch);
    final GCPHardwareSpec readOnlySpec = _readOnlySpecs.toGCPHardwareSpec(pPreferredCpuArch);
    final GCPHardwareSpec analyticsSpec = _analyticsSpecs.toGCPHardwareSpec(pPreferredCpuArch);
    final GCPHardwareSpec hiddenSecSpec = electableSpec.copy().setNodeCount(0).build();
    return new ShardRegionConfig(
        regionName,
        CloudProvider.GCP,
        _baseAutoScaling.toAutoScaling(CloudProvider.GCP),
        _analyticsAutoScaling != null
            ? _analyticsAutoScaling.toAutoScaling(CloudProvider.GCP)
            : null,
        _priority,
        electableSpec,
        analyticsSpec,
        readOnlySpec,
        hiddenSecSpec,
        null,
        null,
        null,
        null);
  }

  private ShardRegionConfig getFreeRegionConfig() {
    if (_electableSpecs._backingCloudProvider == null) {
      throw new IllegalArgumentException("Must specify backing provider for tenant clusters");
    }

    final RegionName regionName =
        RegionNameHelper.findByNameOrElseThrow(_electableSpecs._backingCloudProvider, _regionName);
    final FreeHardwareSpec electableSpec = _electableSpecs.toFreeHardwareSpec();

    final FreeHardwareSpec readOnlySpec = electableSpec.copy().setNodeCount(0).build();
    final FreeHardwareSpec analyticsSpec = electableSpec.copy().setNodeCount(0).build();
    final FreeHardwareSpec hiddenSecSpec = electableSpec.copy().setNodeCount(0).build();

    return new ShardRegionConfig(
        regionName,
        CloudProvider.FREE,
        _baseAutoScaling.toAutoScaling(CloudProvider.FREE),
        null,
        _priority,
        electableSpec,
        analyticsSpec,
        readOnlySpec,
        hiddenSecSpec,
        null,
        null,
        null,
        null);
  }

  private ShardRegionConfig getServerlessRegionConfig() {
    if (_electableSpecs._backingCloudProvider == null) {
      throw new IllegalArgumentException(
          "Must specify backing provider for serverless tenant clusters");
    }

    final RegionName regionName =
        RegionNameHelper.findByNameOrElseThrow(_electableSpecs._backingCloudProvider, _regionName);
    final ServerlessHardwareSpec electableSpec = _electableSpecs.toServerlessHardwareSpec();
    final ServerlessHardwareSpec readOnlySpec = _readOnlySpecs.toServerlessHardwareSpec();
    final ServerlessHardwareSpec analyticsSpec = _analyticsSpecs.toServerlessHardwareSpec();
    final ServerlessHardwareSpec hiddenSecSpec = electableSpec.copy().setNodeCount(0).build();

    return new ShardRegionConfig(
        regionName,
        CloudProvider.SERVERLESS,
        _baseAutoScaling.toAutoScaling(CloudProvider.SERVERLESS),
        null,
        _priority,
        electableSpec,
        analyticsSpec,
        readOnlySpec,
        hiddenSecSpec,
        null,
        null,
        null,
        null);
  }

  private ShardRegionConfig getFlexRegionConfig() {
    if (_electableSpecs._backingCloudProvider == null) {
      throw new IllegalArgumentException("Must specify backing provider for tenant clusters");
    }

    final RegionName regionName =
        RegionNameHelper.findByNameOrElseThrow(_electableSpecs._backingCloudProvider, _regionName);
    final FlexHardwareSpec electableSpec = _electableSpecs.toFlexHardwareSpec();
    final FlexHardwareSpec readOnlySpec = electableSpec.copy().setNodeCount(0).build();
    final FlexHardwareSpec analyticsSpec = electableSpec.copy().setNodeCount(0).build();
    final FlexHardwareSpec hiddenSecSpec = electableSpec.copy().setNodeCount(0).build();

    return new ShardRegionConfig(
        regionName,
        CloudProvider.FLEX,
        _baseAutoScaling.toAutoScaling(CloudProvider.FLEX),
        null,
        _priority,
        electableSpec,
        analyticsSpec,
        readOnlySpec,
        hiddenSecSpec,
        null,
        null,
        null,
        null);
  }

  public RegionConfig toShardRegionConfig(
      final CpuArchitecture pPreferredCpuArch,
      final double pDiskSizeGB,
      final boolean pSupportsAzureSsdV2,
      final PreferredStorageType pPreferredStorageType) {
    final CloudProvider provider = CloudProvider.valueOf(_cloudProvider);

    switch (provider) {
      case AWS:
        return getAWSRegionConfig(pPreferredCpuArch, pDiskSizeGB);
      case AZURE:
        return getAzureRegionConfig(pDiskSizeGB, pSupportsAzureSsdV2, pPreferredStorageType);
      case GCP:
        return getGCPRegionConfig(pPreferredCpuArch);
      case FREE:
        return getFreeRegionConfig();
      case SERVERLESS:
        return getServerlessRegionConfig();
      case FLEX:
        return getFlexRegionConfig();
      default:
        throw new IllegalStateException("Unknown Provider " + provider);
    }
  }

  public String getCloudProvider() {
    return _cloudProvider;
  }

  public String getRegionName() {
    return _regionName;
  }

  public HardwareSpecView getElectableSpecs() {
    return _electableSpecs;
  }

  public void setElectableSpecs(HardwareSpecView pElectableSpecView) {
    _electableSpecs = pElectableSpecView;
  }

  public HardwareSpecView getReadOnlySpecs() {
    return _readOnlySpecs;
  }

  public void setReadOnlySpecs(HardwareSpecView pReadOnlySpecs) {
    _readOnlySpecs = pReadOnlySpecs;
  }

  public HardwareSpecView getAnalyticsSpecs() {
    return _analyticsSpecs;
  }

  public void setAnalyticsSpecs(HardwareSpecView pAnalyticsSpecs) {
    _analyticsSpecs = pAnalyticsSpecs;
  }

  public RegionView getRegionView() {
    return _regionView;
  }

  public void setRegionViewAndName(final RegionView pRegionView) {
    _regionName = pRegionView.getKey();
    _regionView = pRegionView;
  }

  public AutoScalingView getBaseAutoScaling() {
    return _baseAutoScaling;
  }

  public AutoScalingView getAnalyticsAutoScaling() {
    return _analyticsAutoScaling;
  }

  public void setBaseAutoScaling(final AutoScalingView pAutoScaling) {
    _baseAutoScaling = pAutoScaling;
  }

  public Integer getPriority() {
    return _priority;
  }

  public RegionName getCloudProviderRegionName() {
    CloudProvider providerToUse = CloudProvider.valueOf(_cloudProvider);
    if (providerToUse.isTenantProvider()) {
      providerToUse = getElectableSpecs().getBackingCloudProvider();
    }
    switch (providerToUse) {
      case AWS:
        return AWSRegionName.valueOf(_regionName);
      case AZURE:
        return AzureRegionName.valueOf(_regionName);
      case GCP:
        return GCPRegionName.valueOf(_regionName);
      default:
        throw new IllegalArgumentException();
    }
  }

  public Set<InstanceSize> getInstanceSizes() {
    final Set<InstanceSize> instanceSizes = new HashSet<>();
    final CloudProvider cloudProvider = CloudProvider.valueOf(getCloudProvider());
    if (getElectableSpecs() != null) {
      final Optional<? extends InstanceSize> electableInstanceSize =
          InstanceSizeUtil.findByName(cloudProvider, getElectableSpecs().getInstanceSize());
      electableInstanceSize.ifPresent(instanceSizes::add);
    }
    if (getReadOnlySpecs() != null) {
      final Optional<? extends InstanceSize> readOnlyInstanceSize =
          InstanceSizeUtil.findByName(cloudProvider, getReadOnlySpecs().getInstanceSize());
      readOnlyInstanceSize.ifPresent(instanceSizes::add);
    }
    if (getAnalyticsSpecs() != null) {
      final Optional<? extends InstanceSize> analyticsInstanceSize =
          InstanceSizeUtil.findByName(cloudProvider, getAnalyticsSpecs().getInstanceSize());
      analyticsInstanceSize.ifPresent(instanceSizes::add);
    }
    return instanceSizes;
  }

  public Optional<? extends InstanceSize> getInstanceSizeByNodeType(final NodeType nodeType) {
    return switch (nodeType) {
      case ELECTABLE ->
          InstanceSizeUtil.findByName(
              CloudProvider.valueOf(getCloudProvider()), getElectableSpecs().getInstanceSize());
      case ANALYTICS ->
          InstanceSizeUtil.findByName(
              CloudProvider.valueOf(getCloudProvider()), getAnalyticsSpecs().getInstanceSize());
      case READ_ONLY ->
          InstanceSizeUtil.findByName(
              CloudProvider.valueOf(getCloudProvider()), getReadOnlySpecs().getInstanceSize());
      default -> throw new IllegalArgumentException("Invalid Node Type: " + nodeType);
    };
  }

  /**
   * Gets the customer-provided hardware specifications for electable nodes in this region.
   *
   * <p>Electable nodes are MongoDB replica set members that can participate in elections and become
   * primary nodes. These specifications define the instance size, node count, disk IOPS, and other
   * hardware characteristics as originally specified by the customer.
   *
   * @return the customer-provided hardware specifications for electable nodes, may be null
   */
  public HardwareSpecView getCustomerProvidedElectableSpecs() {
    return _customerProvidedElectableSpecs;
  }

  /**
   * Gets the customer-provided hardware specifications for analytics nodes in this region.
   *
   * <p>Analytics nodes are specialized MongoDB replica set members optimized for read-heavy
   * analytical workloads. These specifications define the instance size, node count, disk IOPS, and
   * other hardware characteristics as originally specified by the customer.
   *
   * @return the customer-provided hardware specifications for analytics nodes, may be null
   */
  public HardwareSpecView getCustomerProvidedAnalyticsSpecs() {
    return _customerProvidedAnalyticsSpecs;
  }

  /**
   * Gets the customer-provided hardware specifications for read-only nodes in this region.
   *
   * <p>Read-only nodes are MongoDB replica set members that serve read operations but cannot
   * participate in elections or become primary nodes. These specifications define the instance
   * size, node count, disk IOPS, and other hardware characteristics as originally specified by the
   * customer.
   *
   * @return the customer-provided hardware specifications for read-only nodes, may be null
   */
  public HardwareSpecView getCustomerProvidedReadOnlySpecs() {
    return _customerProvidedReadOnlySpecs;
  }

  public static class FieldDefs {
    public static final String REGION_NAME = "regionName";
    public static final String CLOUD_PROVIDER = "cloudProvider";
    public static final String AUTOSCALING = "autoScaling";
    public static final String ANALYTICS_AUTOSCALING = "analyticsAutoScaling";
    public static final String PRIORITY = "priority";
    public static final String ELECTABLE_SPECS = "electableSpecs";
    public static final String ANALYTICS_SPECS = "analyticsSpecs";
    public static final String READ_ONLY_SPECS = "readOnlySpecs";
    public static final String REGION_VIEW = "regionView";
    public static final String CUSTOMER_PROVIDED_ELECTABLE_SPECS = "customerProvidedElectableSpecs";
    public static final String CUSTOMER_PROVIDED_ANALYTICS_SPECS = "customerProvidedAnalyticsSpecs";
    public static final String CUSTOMER_PROVIDED_READ_ONLY_SPECS = "customerProvidedReadOnlySpecs";
  }
}
