package com.xgen.svc.nds.planner;

import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.deployment._public.model.AuthUser;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.MaintainedMongotuneConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.deployment._public.util.ClusterDeploymentProcessUtil;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionProcessArgsDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.MongotuneProcessArgs;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchInstanceSvc;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;

/**
 * The GenerateClusterMongotuneAutomationConfigStep class is responsible for generating and managing
 * the automation configuration specific to mongotune settings within a cluster. It extends the
 * GenerateAutomationConfigBaseStep to leverage base functionality for automation configuration
 * handling.
 *
 * <p>The main tasks handled by this class include: - Generating updated mongotune configurations
 * based on the current cluster state and process arguments. - Updating the deployment configuration
 * with new mongotune configurations.
 */
public class GenerateClusterMongotuneAutomationConfigStep extends GenerateAutomationConfigBaseStep {

  private final ClusterDescriptionProcessArgsDao _clusterDescriptionProcessArgsDao;
  private final Cluster _cluster;

  /**
   * Constructor for GenerateClusterMongotuneAutomationConfigStep.
   *
   * @param pContext The PlanContext containing the context for the plan.
   * @param pState The state of the step.
   * @param pNDSGroupMaintenanceSvc The service for NDS group maintenance.
   * @param pNDSGroupDao The DAO for NDS groups.
   * @param pNDSGroup The NDS group associated with this step.
   * @param pGroup The group associated with this step.
   * @param pExistingConfig The existing automation configuration to be updated.
   * @param pSearchInstanceSvc The service for search instances.
   * @param cluster The cluster for which the mongotune configurations are being generated.
   * @param clusterDescriptionProcessArgsDao DAO for cluster description process arguments.
   */
  public GenerateClusterMongotuneAutomationConfigStep(
      final PlanContext pContext,
      final State pState,
      final NDSGroupMaintenanceSvc pNDSGroupMaintenanceSvc,
      final NDSGroupDao pNDSGroupDao,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final AutomationConfig pExistingConfig,
      final SearchInstanceSvc pSearchInstanceSvc,
      final Cluster cluster,
      final ClusterDescriptionProcessArgsDao clusterDescriptionProcessArgsDao) {
    super(
        pContext,
        pState,
        pNDSGroupDao,
        pNDSGroupMaintenanceSvc,
        pNDSGroup,
        pGroup,
        pExistingConfig,
        pSearchInstanceSvc);
    this._cluster = cluster;
    this._clusterDescriptionProcessArgsDao = clusterDescriptionProcessArgsDao;
  }

  @Override
  public Pair<Result<Result.NoData>, Deployment> getDeployment() {
    final AutomationConfig automationConfig = getExistingAutomationConfig();
    return getDeploymentInternal(automationConfig);
  }

  private Pair<Result<Result.NoData>, Deployment> getDeploymentInternal(
      final AutomationConfig existingAutomationConfig) {
    final List<MaintainedMongotuneConfig> groupMongotuneConfigsExceptTargetCluster =
        NDSDefaults.filterOutMongotunesByCluster(
            existingAutomationConfig.getDeployment().getMaintainedMongotunes(),
            _cluster.getReplicaSets());

    final List<MaintainedMongotuneConfig> updatedMongotuneConfigsForTargetCluster =
        getUpdatedMongotuneConfigsForCluster(existingAutomationConfig);

    groupMongotuneConfigsExceptTargetCluster.addAll(updatedMongotuneConfigsForTargetCluster);
    Deployment existingDeployment = existingAutomationConfig.getDeployment();
    existingDeployment.setMaintainedMongotunes(groupMongotuneConfigsExceptTargetCluster);
    existingAutomationConfig.setDeployment(existingDeployment);
    return Pair.of(Result.done(), existingAutomationConfig.getDeployment());
  }

  private List<MaintainedMongotuneConfig> getUpdatedMongotuneConfigsForCluster(
      final AutomationConfig existingConfig) {
    final ClusterDescription clusterDescription = _cluster.getClusterDescription();
    if (clusterDescription.getMongotuneStatus().isEmpty()) {
      getLogger()
          .atWarn()
          .setMessage("Cluster's mongotune status is null. Will not generate mongotune configs.")
          .addKeyValue("clusterName", clusterDescription.getName())
          .log();
      return List.of();
    }

    final boolean isDisabled =
        clusterDescription
            .getMongotuneStatus()
            .map(status -> status.state().isDisabled())
            .orElse(true);

    final Optional<ClusterDescriptionProcessArgs> cdpa =
        _clusterDescriptionProcessArgsDao.findMerged(
            clusterDescription.getName(), clusterDescription.getGroupId());
    final MongotuneProcessArgs mongotuneArgs =
        cdpa.flatMap(ClusterDescriptionProcessArgs::getMongotuneArg)
            .orElse(new MongotuneProcessArgs());

    return _cluster.getAllProvisionedInstances().stream()
        .filter(i -> i.getHostnameForAgents().isPresent())
        .map(
            i ->
                ClusterDeploymentProcessUtil.createBaseMaintainedMongotuneConfig(
                    i.getHostnameForAgents().get(),
                    getNDSPlanContext().getAppSettings(),
                    mongotuneArgs,
                    isDisabled,
                    existingConfig,
                    _cluster))
        .peek(
            // We do not store the mongotune user password in automation config collection.
            // Instead, we get the mongotune user password from the existing config and set it
            // in the new config.
            config -> {
              final Auth existingAuth = existingConfig.getDeployment().getAuth();
              final Optional<AuthUser> mongotuneAuthUser =
                  existingAuth.findUserWanted(AuthUser.MONGOTUNE_USER, AuthUser.ADMIN_DB);
              mongotuneAuthUser.ifPresent(
                  authUser -> config.setMongotuneUserPwd(authUser.getInitPwd()));
            })
        .toList();
  }
}
