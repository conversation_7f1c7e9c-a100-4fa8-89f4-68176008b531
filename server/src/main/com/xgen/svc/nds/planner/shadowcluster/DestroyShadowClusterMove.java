package com.xgen.svc.nds.planner.shadowcluster;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._public.model.ShadowClusterJob;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure.Status;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowCluster;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterJobSvc;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterSvc;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.nds.planner.MoveProvider;
import com.xgen.svc.nds.planner.NDSMove;
import com.xgen.svc.nds.planner.WaitForSystemClusterDeletionStep;
import com.xgen.svc.nds.planner.WaitForSystemProjectDeletionStep;
import com.xgen.svc.nds.planner.WaitForUnusedContainerDeletionStep;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import com.xgen.svc.nds.svc.SystemClusterJobSvc;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;

/**
 * Move to destroy a shadow cluster. This move is responsible for deleting the shadow cluster
 * associated with a given shadow cluster job. It waits for the deletion to complete and updates the
 * state accordingly.
 */
public class DestroyShadowClusterMove extends NDSMove {

  private final ObjectId shadowClusterJobId;
  private final ShadowClusterJobSvc shadowClusterJobSvc;
  private final SystemClusterJobSvc systemClusterJobSvc;
  private final ShadowClusterSvc shadowClusterSvc;
  private final NDSCloudProviderContainerSvc ndsCloudProviderContainerSvc;

  /**
   * Constructor for creating a DestroyShadowClusterMove with a PlanContext and shadow cluster job
   * ID. Expected to be used when creating moves from the planner.
   *
   * @param pContext the plan context
   * @param pShadowClusterJobId the ID of the shadow cluster job to be destroyed
   * @param pShadowClusterJobSvc service to manage shadow cluster jobs
   * @param pSystemClusterJobSvc service to manage system cluster jobs
   * @param pShadowClusterSvc service to manage shadow clusters
   */
  @AssistedInject
  public DestroyShadowClusterMove(
      @Assisted("planContext") final PlanContext pContext,
      @Assisted("shadowClusterJobId") final ObjectId pShadowClusterJobId,
      final ShadowClusterJobSvc pShadowClusterJobSvc,
      final SystemClusterJobSvc pSystemClusterJobSvc,
      final ShadowClusterSvc pShadowClusterSvc,
      final NDSCloudProviderContainerSvc pNdsCloudProviderContainerSvc) {
    super(pContext);
    this.shadowClusterJobId = pShadowClusterJobId;
    this.shadowClusterJobSvc = pShadowClusterJobSvc;
    this.systemClusterJobSvc = pSystemClusterJobSvc;
    this.shadowClusterSvc = pShadowClusterSvc;
    this.ndsCloudProviderContainerSvc = pNdsCloudProviderContainerSvc;
  }

  /**
   * Constructor for creating a DestroyShadowClusterMove with all parameters. Expected to be used
   * when creating moves from the internal database.
   *
   * @param pId the ID of the move
   * @param pPredecessors the set of predecessor move IDs
   * @param pSuccessors the set of successor move IDs
   * @param pContext the plan context
   * @param pState the state of the move
   * @param pSystemClusterJobId the ID of the system cluster job associated with this move
   * @param pShadowClusterJobSvc service to manage shadow cluster jobs
   * @param pSystemClusterJobSvc service to manage system cluster jobs
   * @param pShadowClusterSvc service to manage shadow clusters
   */
  @AssistedInject
  public DestroyShadowClusterMove(
      @Assisted("pId") final ObjectId pId,
      @Assisted("predecessors") final Set<ObjectId> pPredecessors,
      @Assisted("successors") final Set<ObjectId> pSuccessors,
      @Assisted("planContext") final PlanContext pContext,
      @Assisted("state") final Move.State pState,
      @Assisted("systemClusterJobId") final ObjectId pSystemClusterJobId,
      final ShadowClusterJobSvc pShadowClusterJobSvc,
      final SystemClusterJobSvc pSystemClusterJobSvc,
      final ShadowClusterSvc pShadowClusterSvc,
      final NDSCloudProviderContainerSvc pNdsCloudProviderContainerSvc) {
    super(pId, pPredecessors, pSuccessors, pContext, pState);
    this.shadowClusterJobId = pSystemClusterJobId;
    this.shadowClusterJobSvc = pShadowClusterJobSvc;
    this.systemClusterJobSvc = pSystemClusterJobSvc;
    this.shadowClusterSvc = pShadowClusterSvc;
    this.ndsCloudProviderContainerSvc = pNdsCloudProviderContainerSvc;
  }

  /**
   * Factory method to create a DestroyShadowClusterMove. This method is used by the planner.
   *
   * @param pPlanContext the plan context for the move
   * @param pShadowClusterJobId the ID of the shadow cluster job to be destroyed
   * @return a new instance of DestroyShadowClusterMove
   */
  public static DestroyShadowClusterMove factoryCreate(
      final PlanContext pPlanContext, final ObjectId pShadowClusterJobId) {
    return MoveProvider.createMoveFromFactory(
        DestroyShadowClusterMove.class, List.of(pPlanContext, pShadowClusterJobId));
  }

  /**
   * Returns the cloud provider for this move. Since this move is not associated with any specific
   * cloud provider, it returns CloudProvider.NONE.
   *
   * @return the cloud provider for this move
   */
  @Override
  public CloudProvider getCloudProvider() {
    return CloudProvider.NONE;
  }

  /**
   * Performs the internal logic of the move. It:
   *
   * <ol>
   *   <li>Checks if the shadow cluster job exists.
   *   <li>If the shadow cluster job is already deleted, it logs a message and returns done.
   *   <li>Calls the method to wait for shadow cluster deletion.
   *   <li>If the deletion is complete, it updates the state to indicate that shadow cluster
   *       deletion is complete.
   * </ol>
   *
   * @return a Result indicating the status of the move
   */
  @Override
  protected Result<NoData> performInternal() {
    final ShadowClusterJob shadowClusterJob;
    try {
      shadowClusterJob = shadowClusterJobSvc.getShadowClusterJob(shadowClusterJobId, getLogger());
    } catch (final SvcException e) {
      return Result.failed("Failed to find shadow cluster job with ID: " + shadowClusterJobId);
    }

    final Optional<Exposure> maybeExposure =
        shadowClusterSvc.getExposure(shadowClusterJob.getExposureId());

    if (maybeExposure.isEmpty()) {
      getLogger()
          .atWarn()
          .setMessage("Failed to find exposure for shadow cluster job.")
          .addKeyValue("shadowClusterJobId", shadowClusterJobId)
          .log();
      return Result.failed(
          "Failed to find exposure for shadow cluster job with ID: " + shadowClusterJobId);
    }

    if (shadowClusterJob.isSystemProjectDeleted()) {
      getLogger()
          .atInfo()
          .setMessage("Shadow cluster and project already deleted.")
          .addKeyValue("shadowClusterJobId", shadowClusterJobId)
          .log();
      return updateShadowClusterExposureJobStatusIfAllClustersAreDeleted(
          shadowClusterJob, maybeExposure.get());
    }

    final Result<Result.NoData> shadowClusterDeletionResult =
        waitForShadowClusterDeletionStep(shadowClusterJob);

    if (!shadowClusterDeletionResult.getStatus().isDone()) {
      return shadowClusterDeletionResult;
    }

    runAtLeastOnce(
        () -> {
          shadowClusterJob.markAsShadowClusterDeleted("Shadow cluster deleted successfully");
          shadowClusterJobSvc.saveShadowClusterJob(shadowClusterJob);
        },
        StateFields.SYSTEM_CLUSTER_DELETION_COMPLETE);

    final Result<Result.NoData> containerDeletionResult =
        waitForUnusedContainerDeletionStep(shadowClusterJob);
    if (!containerDeletionResult.getStatus().isDone()) {
      return containerDeletionResult;
    }

    final Result<Result.NoData> systemProjectDeletionResult =
        waitForSystemProjectDeletionStep(shadowClusterJob);

    if (!systemProjectDeletionResult.getStatus().isDone()) {
      return systemProjectDeletionResult;
    }

    runAtLeastOnce(
        () -> {
          shadowClusterJob.markSystemProjectDeleted();
          shadowClusterJobSvc.saveShadowClusterJob(shadowClusterJob);
        },
        StateFields.SYSTEM_PROJECT_DELETION_COMPLETE);

    return updateShadowClusterExposureJobStatusIfAllClustersAreDeleted(
        shadowClusterJob, maybeExposure.get());
  }

  private Result<NoData> waitForUnusedContainerDeletionStep(
      final ShadowClusterJob pShadowClusterJob) {
    if (pShadowClusterJob.getContainerId() == null
        && pShadowClusterJob.getSystemProjectId() == null) {
      getLogger()
          .atWarn()
          .setMessage(
              "Container ID and system project ID are both null. Assuming container never existed.")
          .addKeyValue("shadowClusterJobId", pShadowClusterJob.getId())
          .log();
      return Result.done();
    }
    return new WaitForUnusedContainerDeletionStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.WAIT_FOR_CONTAINER_DELETION),
            ndsCloudProviderContainerSvc,
            pShadowClusterJob.getSystemProjectId(),
            pShadowClusterJob.getContainerId())
        .perform();
  }

  private Result<NoData> updateShadowClusterExposureJobStatusIfAllClustersAreDeleted(
      final ShadowClusterJob shadowClusterJob, final Exposure pExposure) {
    boolean areAllClustersDeleted;
    try {
      areAllClustersDeleted =
          shadowClusterSvc.areAllClustersFromExposureInStatus(
              shadowClusterJob.getExposureId(), ShadowCluster.Status.DELETED);
    } catch (SvcException pE) {
      getLogger().warn("Failed to retrieve all clusters status", pE);
      return Result.failed("Failed to retrieve all clusters status: " + pE.getMessage());
    }

    if (areAllClustersDeleted) {
      try {
        shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
            shadowClusterJobSvc.getShadowClusterExposureJob(
                pExposure.getCurrentJobId(), getLogger()),
            Status.COMPLETED,
            "Finished deleting all shadow clusters");
      } catch (SvcException pE) {
        getLogger().warn("Failed to mark shadow cluster exposure as COMPLETED.", pE);
        return Result.failed(
            "Failed to mark shadow cluster exposure status as COMPLETED: " + pE.getMessage());
      }
    }
    return Result.done();
  }

  private Result<Result.NoData> waitForSystemProjectDeletionStep(
      final ShadowClusterJob pShadowClusterJob) {
    return new WaitForSystemProjectDeletionStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.WAIT_FOR_SYSTEM_PROJECT_DELETION),
            pShadowClusterJob,
            systemClusterJobSvc)
        .perform();
  }

  private Result<Result.NoData> waitForShadowClusterDeletionStep(
      final ShadowClusterJob pShadowClusterJob) {
    return new WaitForSystemClusterDeletionStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.WAIT_FOR_SHADOW_CLUSTER_DELETION),
            pShadowClusterJob,
            systemClusterJobSvc)
        .perform();
  }

  @Override
  protected Object[] getArguments() {
    return new Object[] {shadowClusterJobId};
  }

  /**
   * Returns the ID of the shadow cluster job associated with this move.
   *
   * @return the ID of the shadow cluster job
   */
  public ObjectId getShadowClusterJobId() {
    return shadowClusterJobId;
  }

  private static class StepNumber {
    public static final int WAIT_FOR_SHADOW_CLUSTER_DELETION = 0;
    public static final int WAIT_FOR_CONTAINER_DELETION = 1;
    public static final int WAIT_FOR_SYSTEM_PROJECT_DELETION = 2;
  }

  public static class StateFields {
    public static final String SYSTEM_CLUSTER_DELETION_COMPLETE = "systemClusterDeletionComplete";
    public static final String SYSTEM_PROJECT_DELETION_COMPLETE = "systemProjectDeletionComplete";
  }
}
