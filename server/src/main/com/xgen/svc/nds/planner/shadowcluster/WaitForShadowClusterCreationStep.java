package com.xgen.svc.nds.planner.shadowcluster;

import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.cps.export._public.util.SystemClusterJobPlanLogs;
import com.xgen.cloud.cps.restore._public.model.ShadowClusterJob;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.AutoScalingView;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.DiskGBAutoScalingView;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionProcessArgsDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ConfigOverride;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.model.ui.BiConnectorView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.EncryptionAtRestProviderView;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.svc.nds.model.ui.HardwareSpecView;
import com.xgen.svc.nds.model.ui.RegionConfigView;
import com.xgen.svc.nds.model.ui.ReplicationSpecView;
import com.xgen.svc.nds.planner.NDSStep;
import com.xgen.svc.nds.svc.BackupSvc;
import com.xgen.svc.nds.svc.NDSClusterConversionSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

/**
 * A step that waits for a shadow cluster to be created. This is just a wrapper around
 * WaitForSystemClusterCreationStep for now.
 *
 * <p>This will be modified to actually create a copy of the customer cluster in Milestone 2 of the
 * Shadow Clusters project.
 */
public class WaitForShadowClusterCreationStep extends NDSStep<NoData> {
  private static final int SHADOW_CLUSTER_INTERNAL_QUERY_STAT_RATE_LIMIT = -1; // unlimited

  private final ShadowClusterJob _shadowClusterJob;
  private final ShadowClusterSvc _shadowClusterSvc;
  private final OrganizationSvc _organizationSvc;
  private final NDSUISvc _ndsUISvc;
  private final NDSClusterSvc _ndsClusterSvc;
  private final NDSClusterConversionSvc _ndsClusterConversionSvc;
  private final ClusterDescriptionProcessArgsDao _clusterDescriptionProcessArgsDao;
  private final CpsSvc _cpsSvc;
  private final FeatureFlagSvc _featureFlagSvc;
  private final GroupSvc _groupSvc;
  private final NDSGroupDao _ndsGroupDao;
  private final BackupSvc _backupSvc;

  public WaitForShadowClusterCreationStep(
      final PlanContext pContext,
      final Step.State pState,
      final ShadowClusterJob pShadowClusterJob,
      final ShadowClusterSvc shadowClusterSvc,
      final OrganizationSvc pOrganizationSvc,
      final NDSUISvc pNDSUISvc,
      final NDSClusterSvc pNDSClusterSvc,
      final NDSClusterConversionSvc pNDSClusterConversionSvc,
      final ClusterDescriptionProcessArgsDao pClusterDescriptionProcessArgsDao,
      final CpsSvc pCpsSvc,
      final FeatureFlagSvc pFeatureFlagSvc,
      final GroupSvc pGroupSvc,
      final NDSGroupDao pNDSGroupDao,
      final BackupSvc pBackupSvc) {
    super(pContext, pState);
    _shadowClusterJob = pShadowClusterJob;
    _shadowClusterSvc = shadowClusterSvc;
    _organizationSvc = pOrganizationSvc;
    _ndsUISvc = pNDSUISvc;
    _ndsClusterSvc = pNDSClusterSvc;
    _ndsClusterConversionSvc = pNDSClusterConversionSvc;
    _clusterDescriptionProcessArgsDao = pClusterDescriptionProcessArgsDao;
    _cpsSvc = pCpsSvc;
    _featureFlagSvc = pFeatureFlagSvc;
    _groupSvc = pGroupSvc;
    _ndsGroupDao = pNDSGroupDao;
    _backupSvc = pBackupSvc;
  }

  @Override
  protected Result<Result.NoData> performInternal() {
    if (_shadowClusterJob.isSystemClusterCreated()) {
      return Result.done();
    }

    final String systemClusterName = _shadowClusterJob.getSystemClusterName();
    try {
      final ClusterDescription clusterDescription = createSystemCluster(systemClusterName);
      final Result<Result.NoData> clusterCreationResult =
          waitForClusterReady(clusterDescription.getGroupId(), clusterDescription.getUniqueId());

      if (clusterCreationResult.getStatus().isInProgress()) {
        return clusterCreationResult;
      }

      if (clusterCreationResult.getStatus().isFailed()) {
        getLogger()
            .atWarn()
            .setMessage("Failed to create a shadow cluster")
            .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(_shadowClusterJob))
            .log();
        return Result.failed(
            "Failed to create a shadow cluster for job " + _shadowClusterJob.getId());
      }

      // If we're here, it's done
    } catch (final SvcException e) {
      getLogger()
          .atWarn()
          .setMessage("Failed to create a shadow cluster on exception")
          .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(_shadowClusterJob))
          .setCause(e)
          .log();
      return Result.failed(
          "Failed to create a shadow cluster for job " + _shadowClusterJob.getId());
    }

    return Result.done();
  }

  ClusterDescription createSystemCluster(final String systemClusterName) throws SvcException {
    // First, check if we've already created the cluster
    final ObjectId systemProjectId = _shadowClusterJob.getSystemProjectId();
    final Optional<ClusterDescription> cd =
        _ndsClusterSvc.getMergedClusterDescription(systemProjectId, systemClusterName);
    if (cd.isPresent()) {
      return cd.get();
    }

    final Organization org = _organizationSvc.findById(_shadowClusterJob.getOrgId());
    if (org == null) {
      throw new SvcException(NDSErrorCode.RESOURCE_NOT_FOUND, "Organization not found");
    }

    final Group systemProjectGroup = _groupSvc.findById(systemProjectId);
    if (systemProjectGroup == null) {
      throw new SvcException(
          NDSErrorCode.RESOURCE_NOT_FOUND, "System project " + systemProjectId + " not found");
    }

    final Optional<Exposure> maybeExposure =
        _shadowClusterSvc.getExposure(_shadowClusterJob.getExposureId());
    if (maybeExposure.isEmpty()) {
      throw new SvcException(NDSErrorCode.RESOURCE_NOT_FOUND, "Exposure not found");
    }
    final Exposure exposure = maybeExposure.get();

    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    getLogger()
        .atInfo()
        .setMessage("Request to create a cluster in the system project ")
        .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(_shadowClusterJob))
        .log();

    Optional<ConfigOverride> maybeConfigOverride = exposure.getOptionalConfigOverride();

    // Build the cluster description view by copying the source cluster and overwriting
    // specific fields
    final ClusterDescriptionView cdView =
        buildClusterDescriptionView(systemClusterName, maybeConfigOverride);

    // Create the process args for the shadow cluster by copying the source cluster's process args
    // and adding the internalQueryStatsRateLimit param
    createInitialProcessArgs(cdView, systemProjectGroup, systemClusterName, maybeConfigOverride);

    // Copy the source cluster's feature flags to the shadow cluster
    try {
      enableLegacyFeatureFlagsForShadowCluster(
          systemProjectGroup, org, exposure.getSourceGroupId(), maybeConfigOverride);
    } catch (final IllegalArgumentException e) {
      // Catch any exceptions from invalid feature flags and fail the job
      getLogger()
          .atError()
          .setMessage("Failed to enable legacy feature flags for shadow cluster")
          .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(_shadowClusterJob))
          .setCause(e)
          .log();
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
    }

    return _ndsUISvc.createCluster(
        org,
        systemProjectId,
        cdView,
        ClusterCreateContext.forBackupSystemCluster(),
        AppUser.SYSTEM_USER,
        auditInfo,
        null,
        null);
  }

  ClusterDescriptionView buildClusterDescriptionView(
      final String pClusterName, final Optional<ConfigOverride> maybeConfigOverride)
      throws SvcException {
    final Optional<ClusterDescription> cd =
        _ndsClusterSvc.getMergedClusterDescription(
            _shadowClusterJob.getSourceProjectId(), _shadowClusterJob.getSourceClusterName());
    if (cd.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.CLUSTER_NOT_FOUND,
          _shadowClusterJob.getSourceClusterName(),
          _shadowClusterJob.getSourceProjectId());
    }

    final ClusterDescriptionView.ClusterDescriptionViewBuilder builder =
        getShadowClusterClusterDescription(cd.get(), pClusterName, maybeConfigOverride);

    return builder.build();
  }

  /**
   * Creates a ClusterDescriptionViewBuilder using the ClusterDescriptionView constructor to copy
   * ALL attributes from the source cluster, then applies targeted overrides via the builder
   * pattern. This ensures that shadow clusters inherit the source cluster's configuration (MongoDB
   * version, disk size, instance sizes, cloud providers, etc.) while having specific settings
   * overridden for testing purposes.
   *
   * @param sourceCluster the source cluster to copy attributes from
   * @param clusterName the name for the shadow cluster
   * @return builder with all attributes copied from source and specific overrides applied
   */
  protected ClusterDescriptionView.ClusterDescriptionViewBuilder getShadowClusterClusterDescription(
      final ClusterDescription sourceCluster,
      final String clusterName,
      final Optional<ConfigOverride> maybeConfigOverride)
      throws SvcException {

    // Do some basic validation on the source CD
    if (sourceCluster.isEncryptionAtRestEnabled()
        || sourceCluster.isCrossCloudCluster()
        || sourceCluster.isPaused()
        || sourceCluster.getClusterType().isSharded()
        || sourceCluster.getOnlyCloudProvider().isPresent()
            && sourceCluster.getOnlyCloudProvider().get() != CloudProvider.AWS) {
      throw new SvcException(
          NDSErrorCode.INVALID_CLUSTER,
          "Source cluster is not supported for shadow cluster creation");
    }

    Optional<String> maybeInstanceSize =
        maybeConfigOverride.flatMap(ConfigOverride::getInstanceSize);
    Optional<VersionUtils.Version> maybeMongoVersion =
        maybeConfigOverride.flatMap(ConfigOverride::getMongoVersion);

    ClusterDescriptionView.ClusterDescriptionViewBuilder builder =
        new ClusterDescriptionView(sourceCluster)
            .toBuilder()
                .name(clusterName)
                .groupId(_shadowClusterJob.getSystemProjectId())
                .groupName(_shadowClusterJob.getSystemProjectName())
                .internalClusterRole(
                    ClusterDescription.InternalClusterRole.INTERNAL_SHADOW_CLUSTER.name())
                .backupEnabled(false)
                .diskBackupEnabled(false)
                .pitEnabled(false)
                .monitoringPaused(false)
                .isPaused(false)
                .mongoDBUriHosts(new String[0])
                .mongoDBUriHostsLastUpdateDate(new Date())
                .state(ClusterDescriptionView.State.IDLE)
                .biConnector(BiConnectorView.getBiConnectorDisabledView())
                .clusterTags(Set.of(_shadowClusterJob.getExecutionOption().name()))
                .terminationProtectionEnabled(false)
                .geoShardingView(GeoShardingView.getDefaultGeoShardingView(false))
                .crossCloudCluster(false)
                .encryptionAtRestProvider(EncryptionAtRestProviderView.NONE)
                .replicationSpecList(
                    sourceCluster.getReplicationSpecsWithShardData() != null
                        ? sourceCluster.getReplicationSpecsWithShardData().stream()
                            .map(
                                spec ->
                                    createReplicationSpecViewWithDisabledComputeAutoscaling(
                                        spec, maybeInstanceSize))
                            .collect(java.util.stream.Collectors.toList())
                        : java.util.Collections.emptyList());

    maybeMongoVersion.ifPresent(version -> builder.mongoDBVersion(version.toString()));

    return builder;
  }

  /*
   * Creates a ReplicationSpecView from the source cluster's replication spec while ensuring that
   * compute /disk autoscaling is explicitly disabled for the shadow cluster.
   */
  private ReplicationSpecView createReplicationSpecViewWithDisabledComputeAutoscaling(
      final ReplicationSpec sourceSpec, final Optional<String> maybeInstanceSize) {
    final ReplicationSpecView sourceView = new ReplicationSpecView(sourceSpec);

    final List<RegionConfigView> modifiedRegionConfigs =
        sourceView.getRegionConfigs().stream()
            .map(region -> disableAutoscalingInRegionConfig(region, maybeInstanceSize))
            .collect(Collectors.toList());

    return sourceView.toBuilder()
        .id(new ObjectId())
        .externalId(new ObjectId())
        .regionConfigs(modifiedRegionConfigs)
        .build();
  }

  /*
   * Creates a RegionConfigView with compute / disk autoscaling disabled while preserving all other
   * configuration from the source region config.
   */
  private RegionConfigView disableAutoscalingInRegionConfig(
      final RegionConfigView regionConfig, final Optional<String> maybeInstanceSize) {
    // Get current autoscaling settings
    final AutoScalingView baseAutoScaling = regionConfig.getBaseAutoScaling();
    final AutoScalingView analyticsAutoScaling = regionConfig.getAnalyticsAutoScaling();

    // Create disabled autoscaling for base
    final AutoScalingView modifiedBaseAutoScaling =
        baseAutoScaling != null
            ? baseAutoScaling.toBuilder()
                .compute(
                    baseAutoScaling.getCompute().toBuilder()
                        .enabled(false)
                        .scaleDownEnabled(false)
                        .predictiveEnabled(false)
                        .minInstanceSize((InstanceSize) null)
                        .maxInstanceSize((InstanceSize) null)
                        .build())
                .diskGB(DiskGBAutoScalingView.getDisabledDiskGBAutoScalingView())
                .build()
            : null;

    // Create disabled autoscaling for analytics (if exists)
    final AutoScalingView modifiedAnalyticsAutoScaling =
        analyticsAutoScaling != null
            ? analyticsAutoScaling.toBuilder()
                .compute(
                    analyticsAutoScaling.getCompute().toBuilder()
                        .enabled(false)
                        .scaleDownEnabled(false)
                        .predictiveEnabled(false)
                        .minInstanceSize((InstanceSize) null)
                        .maxInstanceSize((InstanceSize) null)
                        .build())
                .diskGB(DiskGBAutoScalingView.getDisabledDiskGBAutoScalingView())
                .build()
            : null;

    // When instance size override is explicitly provided, set electable instance size accordingly
    final HardwareSpecView electableSpecsToUse =
        maybeInstanceSize
            .map(s -> regionConfig.getElectableSpecs().copy().setInstanceSize(s).build())
            .orElseGet(regionConfig::getElectableSpecs);

    // Create new RegionConfigView with modified autoscaling and possibly overridden electable specs
    return new RegionConfigView(
        CloudProvider.valueOf(regionConfig.getCloudProvider()),
        regionConfig.getCloudProviderRegionName(),
        modifiedBaseAutoScaling,
        modifiedAnalyticsAutoScaling,
        regionConfig.getPriority(),
        electableSpecsToUse,
        regionConfig.getAnalyticsSpecs(),
        regionConfig.getReadOnlySpecs(),
        null,
        null,
        null);
  }

  private void createInitialProcessArgs(
      final ClusterDescriptionView cdView,
      final Group systemGroup,
      final String systemClusterName,
      final Optional<ConfigOverride> maybeConfigOverride)
      throws SvcException {
    // Only add process args if they dont exist already. We only want to create process args once
    if (_clusterDescriptionProcessArgsDao
        .findMerged(systemClusterName, systemGroup.getId())
        .isPresent()) {
      return;
    }

    final ClusterDescriptionProcessArgs sourceProcessArgs =
        _ndsClusterSvc
            .getProcessArgs(
                _shadowClusterJob.getSourceProjectId(), _shadowClusterJob.getSourceClusterName())
            .orElseThrow(
                () ->
                    new SvcException(
                        NDSErrorCode.RESOURCE_NOT_FOUND,
                        "Source cluster process args not found for "
                            + _shadowClusterJob.getSourceClusterUniqueId()));

    final Optional<NDSGroup> maybeNDSGroup = _ndsGroupDao.find(systemGroup.getId());
    if (maybeNDSGroup.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.RESOURCE_NOT_FOUND, "NDS group " + systemGroup.getId() + " not found");
    }

    final ClusterDescriptionProcessArgsView clusterDescriptionProcessArgsView =
        new ClusterDescriptionProcessArgsView(
            sourceProcessArgs, Optional.ofNullable(cdView.getMongoDBVersion()));

    // Initialize shard set parameter map with the required shadow cluster parameter
    final Map<String, Object> shardSetParameterMap =
        clusterDescriptionProcessArgsView
            .getShardSetParameterMap()
            .map(HashMap::new) // Create mutable copy
            .orElseGet(HashMap::new);

    shardSetParameterMap.put(
        "internalQueryStatsRateLimit", SHADOW_CLUSTER_INTERNAL_QUERY_STAT_RATE_LIMIT);

    // Merge any additional parameters from config override
    maybeConfigOverride
        .flatMap(ConfigOverride::getServerParams)
        .flatMap(ClusterDescriptionProcessArgs::getShardArg)
        .map(ProcessArguments2_6::getSetParameterMap)
        .ifPresent(shardSetParameterMap::putAll);

    clusterDescriptionProcessArgsView.setShardSetParameterMap(shardSetParameterMap);

    _ndsUISvc.createProcessArgs(
        systemGroup,
        systemClusterName,
        clusterDescriptionProcessArgsView,
        cdView.getDiskSizeGB(),
        cdView.getMongoDBVersion(),
        cdView.getInstanceSize().name(),
        null,
        AppUser.SYSTEM_USER);
  }

  protected void enableLegacyFeatureFlagsForShadowCluster(
      final Group systemProject,
      final Organization org,
      final ObjectId sourceGroupId,
      final Optional<ConfigOverride> maybeConfigOverride) {
    final Group sourceGroup = _groupSvc.findById(sourceGroupId);

    _featureFlagSvc.getEnabledFeatureFlags(org, sourceGroup).stream()
        .filter(FeatureFlag.NON_CONFIG_SERVICE_FEATURE_FLAGS::contains)
        .forEach(
            flag -> _featureFlagSvc.enableNonConfigServiceFeatureFlag(systemProject, org, flag));

    maybeConfigOverride
        .flatMap(ConfigOverride::getLegacyFeatureFlags)
        .ifPresent(
            flags -> {
              flags.forEach(
                  (flagName, enabled) -> {
                    if (!enabled) {
                      return;
                    }
                    final FeatureFlag flag = FeatureFlag.valueOf(flagName);
                    _featureFlagSvc.enableNonConfigServiceFeatureFlag(systemProject, org, flag);
                  });
            });
  }

  private Result<Result.NoData> waitForClusterReady(
      final ObjectId pProjectId, final ObjectId pClusterUniqueId) {
    final Optional<ClusterDescription> cd =
        _ndsClusterSvc.getActiveClusterDescription(pProjectId, pClusterUniqueId);
    // The cluster is ready when there is a MongoDBUriHost
    final boolean isClusterReady = cd.isPresent() && NDSClusterSvc.hasUriHost(cd.get());
    return evaluateAttempt(
        isClusterReady,
        "waitForClusterReady",
        AttemptLimits.WAIT_FOR_CLUSTER_READY,
        "wait for cluster ready");
  }

  private static class AttemptLimits {
    public static final Duration WAIT_FOR_CLUSTER_READY = Duration.ofMinutes(60);
  }
}
