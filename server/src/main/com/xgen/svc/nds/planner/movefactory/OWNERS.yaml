version: 1.0.0
aliases:
  - //.github/CODEOWNERS-aliases.yml
filters:
  - "*":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "/*SyncClusterWithPrivateLinkMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/*OSPolicyMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/CheckMetadataConsistencyMove*.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/CleanupFromRestoreMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/CollectionRestoreFromSystemClusterMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/*CopySnapshotMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/Cps*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/CreateResilientSnapshotMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/RecordAndSnapshotForShadowClusterMove*.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/*DestroyEnvoyInstanceMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/*DestroyServerlessCloudLoadBalancerMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/DestroySystemClusterMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/*DataProtectionMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/DoServerless*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/DoSnapshot*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/DoTenant*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/DoUnpauseTenantRestoreMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/EndRegionalOutageMove*.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/EnsureConnectivityForTopologyChangeMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/FastFlex*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/FastFree*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/Flex*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/Free*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/HealResync*.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/PostCopySnapshotMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/PreCopySnapshotMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/PreprocessRestoreMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/ProcessAutomationConfig*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "/*ProvisionEnvoyInstanceMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/ProvisionShadowClusterMove*.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/DestroyShadowClusterMove*.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/ProvisionSystemClusterMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/RefreshOidcKeysMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/ReloadSslOnProcessesMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/Resync*Move*.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/RotateAgentAPIKeysMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/Serverless*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/StartRegionalOutageMove*.java":
    approvers:
      - 10gen/code-review-team-acad
  - "/SyncBackup*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/*TenantProducer*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/*TenantConsumer*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/UpdateEnvoy*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/UpdateHostBumperFilesAdminMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-performance
  - "/UploadSnapshotExport*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/WaitForEnvoy*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-iii
  - "/WaitForRestore*.java":
    approvers:
      - 10gen/code-review-team-atlas-backup
  - "/GCPServiceAccountSetupMove*.java":
    approvers:
      - atlas-clusters-security-ii
  - "/*Mongotune*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-performance
  - "/RecordAndSnapshotForShadowClusterMoveFactory*.java":
    approvers:
      - 10gen/code-review-team-acad
  - "AWSPrometheusPrivateNetworking*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-ii
