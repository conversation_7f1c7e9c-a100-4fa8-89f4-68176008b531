package com.xgen.svc.nds.planner.shadowcluster;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.export._public.util.SystemClusterJobPlanLogs;
import com.xgen.cloud.cps.restore._public.model.ShadowClusterJob;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Permutation;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Permutation.PermutationType;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowCluster;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowCluster.Status;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowClusterExposureJob;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterJobSvc;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterSvc;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.aws.model.ui.AWSCloudProviderContainerView;
import com.xgen.svc.nds.planner.MoveProvider;
import com.xgen.svc.nds.planner.NDSMove;
import com.xgen.svc.nds.planner.WaitForSystemClusterDeletionStep;
import com.xgen.svc.nds.planner.WaitForSystemClusterRestoreStep;
import com.xgen.svc.nds.planner.WaitForSystemProjectDeletionStep;
import com.xgen.svc.nds.planner.WaitForUnusedContainerDeletionStep;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import com.xgen.svc.nds.svc.SystemClusterJobSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;

/**
 * Represents a move for provisioning a new shadow cluster for a specific permutation within a
 * shadow cluster exposure.
 *
 * <p>This move depends on {@link RecordAndSnapshotForShadowClusterMove} to have already been
 * completed
 *
 * <p>This move orchestrates the various steps required to set up an individual shadow cluster,
 * including:
 *
 * <ul>
 *   <li>Retrieving the associated {@link ShadowClusterJob}.
 *   <li>Validating the job and exposure status.
 *   <li>Creating a {@link ShadowCluster} database record if one doesn't already exist for the given
 *       exposure and permutation. The ID of this new or existing shadow cluster is stored in the
 *       move's state.
 *   <li>Updating the exposure status to {@link Exposure.Status#PROVISIONING}.
 *   <li>Executing sub-steps for:
 *       <ul>
 *         <li>{@link #createSystemProject(ShadowClusterJob, ObjectId)}: Creating the necessary
 *             system project.
 *         <li>{@link #waitForContainerAndClusterCreate(ShadowClusterJob, ObjectId)}: Provisioning
 *             the required container and creating the system cluster in parallel. Container
 *             creation is initiated first to ensure the container exists, then both container
 *             provisioning and cluster creation proceed concurrently.
 *         <li>{@link #restoreToSystemCluster(ShadowClusterJob)}: Restoring the snapshot.
 *       </ul>
 *   <li>If all steps succeed, it updates the {@link ShadowCluster}'s status to {@link
 *       ShadowCluster.Status#PROVISIONED}.
 *   <li>Finally, it calls {@link #updateExposureStatusIfAllClustersCreated(ShadowClusterJob)} to
 *       check if all shadow clusters for the exposure are provisioned, and if so, updates the
 *       overall {@link Exposure} status to {@link Exposure.Status#PROVISIONED}.
 * </ul>
 *
 * <p>The {@code rollbackInternal} method handles the cleanup process in case of failures. It
 * attempts to:
 *
 * <ul>
 *   <li>Update the exposure status to {@link Exposure.Status#FAILED}.
 *   <li>Clean up network permissions.
 *   <li>Delete the container.
 *   <li>Delete the shadow cluster.
 *   <li>Delete the system project.
 * </ul>
 *
 * <p>The factory for this move is {@link
 * com.xgen.svc.nds.planner.movefactory.ProvisionShadowClusterMoveFactory}.
 *
 * @see RecordAndSnapshotForShadowClusterMove
 * @see ShadowClusterJob
 * @see ShadowClusterSvc
 * @see SystemClusterJobSvc
 * @see Exposure
 * @see ShadowCluster
 * @see Permutation
 * @see com.xgen.svc.nds.planner.movefactory.ProvisionShadowClusterMoveFactory
 */
public class ProvisionShadowClusterMove extends NDSMove {
  private final ObjectId systemClusterJobId;
  private final ObjectId permutationId;

  private final SystemClusterJobSvc systemClusterJobSvc;
  private final ShadowClusterJobSvc shadowClusterJobSvc;
  private final ShadowClusterSvc shadowClusterSvc;
  private final NDSCloudProviderContainerSvc ndsCloudProviderContainerSvc;
  private final NDSGroupDao ndsGroupDao;
  private final ClusterDescriptionDao clusterDescriptionDao;
  private final NDSClusterSvc ndsClusterSvc;
  private final CpsSvc cpsSvc;
  private final WaitForShadowClusterCreationStepFactory waitForShadowClusterCreationStepFactory;

  @AssistedInject
  public ProvisionShadowClusterMove(
      @Assisted("planContext") final PlanContext context,
      @Assisted("systemClusterJobId") final ObjectId systemClusterJobId,
      @Assisted("permutationId") final ObjectId permutationId,
      final SystemClusterJobSvc systemClusterJobSvc,
      final ShadowClusterJobSvc shadowClusterJobSvc,
      final ShadowClusterSvc shadowClusterSvc,
      final NDSCloudProviderContainerSvc ndsCloudProviderContainerSvc,
      final NDSGroupDao ndsGroupDao,
      final ClusterDescriptionDao clusterDescriptionDao,
      final NDSClusterSvc ndsClusterSvc,
      final CpsSvc cpsSvc,
      final WaitForShadowClusterCreationStepFactory waitForShadowClusterCreationStepFactory) {
    super(context);
    this.systemClusterJobId = systemClusterJobId;
    this.permutationId = permutationId;
    this.systemClusterJobSvc = systemClusterJobSvc;
    this.shadowClusterJobSvc = shadowClusterJobSvc;
    this.shadowClusterSvc = shadowClusterSvc;
    this.ndsCloudProviderContainerSvc = ndsCloudProviderContainerSvc;
    this.ndsGroupDao = ndsGroupDao;
    this.clusterDescriptionDao = clusterDescriptionDao;
    this.ndsClusterSvc = ndsClusterSvc;
    this.cpsSvc = cpsSvc;
    this.waitForShadowClusterCreationStepFactory = waitForShadowClusterCreationStepFactory;
  }

  @AssistedInject
  public ProvisionShadowClusterMove(
      @Assisted("pId") final ObjectId pId,
      @Assisted("predecessors") final Set<ObjectId> predecessors,
      @Assisted("successors") final Set<ObjectId> successors,
      @Assisted("planContext") final PlanContext context,
      @Assisted("state") final State state,
      @Assisted("systemClusterJobId") final ObjectId systemClusterJobId,
      @Assisted("permutationId") final ObjectId permutationId,
      final SystemClusterJobSvc systemClusterJobSvc,
      final ShadowClusterJobSvc shadowClusterJobSvc,
      final ShadowClusterSvc shadowClusterSvc,
      final NDSCloudProviderContainerSvc ndsCloudProviderContainerSvc,
      final NDSGroupDao ndsGroupDao,
      final ClusterDescriptionDao clusterDescriptionDao,
      final NDSClusterSvc ndsClusterSvc,
      final CpsSvc cpsSvc,
      final WaitForShadowClusterCreationStepFactory waitForShadowClusterCreationStepFactory) {
    super(pId, predecessors, successors, context, state);
    this.systemClusterJobId = systemClusterJobId;
    this.permutationId = permutationId;
    this.systemClusterJobSvc = systemClusterJobSvc;
    this.shadowClusterJobSvc = shadowClusterJobSvc;
    this.shadowClusterSvc = shadowClusterSvc;
    this.ndsCloudProviderContainerSvc = ndsCloudProviderContainerSvc;
    this.ndsGroupDao = ndsGroupDao;
    this.clusterDescriptionDao = clusterDescriptionDao;
    this.ndsClusterSvc = ndsClusterSvc;
    this.cpsSvc = cpsSvc;
    this.waitForShadowClusterCreationStepFactory = waitForShadowClusterCreationStepFactory;
  }

  public static ProvisionShadowClusterMove factoryCreate(
      final PlanContext planContext,
      final ObjectId systemClusterJobId,
      final ObjectId permutationId) {
    return MoveProvider.createMoveFromFactory(
        ProvisionShadowClusterMove.class, List.of(planContext, systemClusterJobId, permutationId));
  }

  @Override
  protected Result<?> performInternal() {
    getLogger()
        .atInfo()
        .setMessage("Starting ProvisionShadowClusterMove")
        .addKeyValue("systemClusterJobId", systemClusterJobId)
        .addKeyValue("permutationId", permutationId)
        .log();

    final ShadowClusterJob shadowClusterJob;
    try {
      shadowClusterJob = shadowClusterJobSvc.getShadowClusterJob(systemClusterJobId, getLogger());
    } catch (final SvcException e) {
      return Result.failed("Failed getting shadow cluster job for jobId: " + systemClusterJobId);
    }

    if (shadowClusterJob.getJobStatus().equals(ShadowCluster.Status.FAILED)) {
      return Result.failed();
    }

    if (shadowClusterJob.targetDeletionDateReached()) {
      getLogger()
          .atWarn()
          .setMessage("Aborting provisioning because target deletion date has been reached")
          .addKeyValue("shadowClusterJobId", systemClusterJobId)
          .addKeyValue("permutationId", permutationId)
          .addKeyValue("targetDeletionDate", shadowClusterJob.getTargetDeletionDate())
          .log();
      shadowClusterJobSvc.updateShadowClusterJobStatus(
          shadowClusterJob,
          Status.FAILED,
          "Target deletion date has been reached while provisioning");
      return Result.failed("Target deletion date has been reached");
    }

    ObjectId shadowClusterId =
        getState().getValue(StateFields.SHADOW_CLUSTER_ID).map(ObjectId.class::cast).orElse(null);
    if (shadowClusterId == null) {
      getLogger()
          .atInfo()
          .setMessage("Creating new shadow cluster record")
          .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
          .log();

      final Optional<Exposure> exposure =
          shadowClusterSvc.getExposure(shadowClusterJob.getExposureId());
      final Optional<PermutationType> permutationType =
          exposure.map(Exposure::getPermutations).orElse(List.of()).stream()
              .filter(p -> p.getId().equals(permutationId))
              .map(Permutation::getType)
              .findFirst();

      if (permutationType.isEmpty()) {
        return Result.failed("Failed to get permutation type for permutationId: " + permutationId);
      }

      shadowClusterId =
          shadowClusterSvc.createShadowCluster(
              shadowClusterJob.getExposureId(),
              permutationId,
              shadowClusterJob.getSourceProjectId(),
              shadowClusterJob.getSourceClusterName(),
              shadowClusterJob.getSourceClusterUniqueId(),
              permutationType.get(),
              shadowClusterJob.getId());
      getState().setValue(StateFields.SHADOW_CLUSTER_ID, shadowClusterId);
    } else {
      getLogger()
          .atInfo()
          .setMessage("Resuming with existing shadow cluster")
          .addKeyValue("shadowClusterId", shadowClusterId)
          .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
          .log();
    }

    runAtLeastOnce(
        () ->
            shadowClusterJobSvc.updateShadowClusterJobStatus(
                shadowClusterJob, Status.PROVISIONING, "Starting provisioning of clusters"),
        StateFields.PROVISIONING_STATUS_SET);

    final Result<Result.NoData> createSystemProjectResult =
        createSystemProject(shadowClusterJob, shadowClusterId);
    if (!createSystemProjectResult.getStatus().isDone()) {
      return createSystemProjectResult;
    }

    final Result<Result.NoData> createContainerAndClusterResult =
        waitForContainerAndClusterCreate(shadowClusterJob, shadowClusterId);
    if (!createContainerAndClusterResult.getStatus().isDone()) {
      return createContainerAndClusterResult;
    }

    final Result<Result.NoData> restoreToSystemClusterResult =
        restoreToSystemCluster(shadowClusterJob);
    if (!restoreToSystemClusterResult.getStatus().isDone()) {
      return restoreToSystemClusterResult;
    }

    runAtLeastOnce(
        () ->
            shadowClusterJobSvc.updateShadowClusterJobStatus(
                shadowClusterJob, Status.PROVISIONED, "Finished provisioning shadow cluster"),
        StateFields.PROVISIONED_STATUS_SET);

    try {
      updateExposureStatusIfAllClustersCreated(shadowClusterJob);
    } catch (final SvcException e) {
      return Result.failed("Failed to update exposure status after provisioning shadow cluster");
    }

    getLogger()
        .atInfo()
        .setMessage("Shadow cluster provisioning completed successfully")
        .addKeyValue("shadowClusterId", shadowClusterId)
        .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
        .log();

    return Result.done();
  }

  @Override
  protected Result<?> rollbackInternal() {
    getLogger()
        .atWarn()
        .setMessage("Starting shadow cluster provisioning rollback")
        .addKeyValue("shadowClusterJobId", systemClusterJobId)
        .addKeyValue("permutationId", permutationId)
        .log();

    final ShadowClusterJob shadowClusterJob;
    try {
      shadowClusterJob = shadowClusterJobSvc.getShadowClusterJob(systemClusterJobId, getLogger());
    } catch (final SvcException e) {
      return Result.failed(
          "Failed getting shadow cluster job during rollback for jobId: " + systemClusterJobId);
    }

    final Optional<Exposure> maybeExposure =
        shadowClusterSvc.getExposure(shadowClusterJob.getExposureId());

    if (maybeExposure.isEmpty()) {
      getLogger()
          .atWarn()
          .setMessage("Failed to find exposure for shadow cluster job.")
          .addKeyValue("shadowClusterJobId", systemClusterJobId)
          .addKeyValue("exposureId", shadowClusterJob.getExposureId())
          .log();
      return Result.failed(
          "Failed to find exposure for shadow cluster job with ID: " + systemClusterJobId);
    }

    final ShadowClusterExposureJob shadowClusterExposureJob;
    try {
      shadowClusterExposureJob =
          shadowClusterJobSvc.getShadowClusterExposureJob(
              maybeExposure.get().getCurrentJobId(), getLogger());
    } catch (SvcException pE) {
      getLogger()
          .atWarn()
          .setMessage("Failed to find shadow cluster exposure job.")
          .setCause(pE)
          .addKeyValue("exposureJobId", maybeExposure.get().getCurrentJobId())
          .addKeyValue("exposureId", maybeExposure.get().getId())
          .log();
      return Result.failed(
          "Failed to find exposure job with id: " + maybeExposure.get().getCurrentJobId());
    }

    runAtLeastOnce(
        () -> {
          shadowClusterJobSvc.updateShadowClusterJobStatus(
              shadowClusterJob, ShadowCluster.Status.FAILED, "Shadow cluster provisioning failed.");
          shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
              shadowClusterExposureJob, Exposure.Status.FAILED, "Cluster provisioning failed.");
        },
        StateFields.ROLLBACK_MARK_JOBS_FAILED);

    if (!shadowClusterJob.isShadowClusterDeleted()) {
      final Result<Result.NoData> shadowClusterDeletionStep =
          waitForShadowClusterDeletionStep(shadowClusterJob);
      if (!shadowClusterDeletionStep.getStatus().isDone()) {
        return shadowClusterDeletionStep;
      }
    }

    final Result<Result.NoData> containerDeletionResult =
        waitForContainerDeletionStep(shadowClusterJob);
    if (!containerDeletionResult.getStatus().isDone()) {
      return containerDeletionResult;
    }

    if (!shadowClusterJob.isSystemProjectDeleted()) {
      final Result<Result.NoData> systemProjectDeletionResult =
          waitForSystemProjectDeletionStep(shadowClusterJob);
      if (!systemProjectDeletionResult.getStatus().isDone()) {
        return systemProjectDeletionResult;
      }
    }

    getLogger()
        .atWarn()
        .setMessage("Shadow cluster provisioning rollback completed")
        .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
        .log();

    return Result.done();
  }

  protected Result<Result.NoData> createSystemProject(
      final ShadowClusterJob shadowClusterJob, final ObjectId shadowClusterId) {
    final NDSGroup group;
    try {
      group = systemClusterJobSvc.createSystemProject(getLogger(), shadowClusterJob, false);
    } catch (final Exception e) {
      getLogger()
          .atWarn()
          .setMessage("Failed to create a system project")
          .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
          .setCause(e)
          .log();

      setStatusForFailure(
          shadowClusterJob,
          "Failed to create a system project for permutationId: " + permutationId);
      return Result.failed("Failed to create a system project for permutationId: " + permutationId);
    }

    runAtLeastOnce(
        () -> {
          getLogger()
              .atInfo()
              .setMessage("Successfully created a system project")
              .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
              .log();

          shadowClusterJob.markSystemProjectCreated(group.getGroupId());
          systemClusterJobSvc.saveSystemClusterJob(shadowClusterJob);
          shadowClusterSvc.updateShadowClusterGroupId(shadowClusterId, group.getGroupId());
          shadowClusterJobSvc.updateShadowClusterJobStatus(
              shadowClusterJob, Status.CREATED_SYSTEM_PROJECT, "Created system project");
        },
        StateFields.SYSTEM_PROJECT_STATUS_SET);

    return Result.done();
  }

  /**
   * Executes container creation and cluster creation in parallel.
   *
   * <p>This method first initiates container creation to ensure the container exists, then starts
   * both container provisioning and cluster creation in parallel. Container creation must complete
   * (container must be present) before cluster creation can begin. However, the VPC itself won't be
   * created until there is a cluster present.
   *
   * @param shadowClusterJob the shadow cluster job
   * @param shadowClusterId the shadow cluster ID
   * @return Result.done() when both operations are complete, Result.inProgress() while waiting,
   *     Result.failed() if either operation fails
   */
  protected Result<Result.NoData> waitForContainerAndClusterCreate(
      final ShadowClusterJob shadowClusterJob, final ObjectId shadowClusterId) {
    getLogger()
        .atInfo()
        .setMessage("Starting parallel container and cluster creation")
        .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
        .log();

    // Step 1: Initiate container creation (get container ID, start provisioning)
    final Result<Result.NoData> containerInitResult = initiateContainerCreation(shadowClusterJob);
    if (!containerInitResult.getStatus().isDone()) {
      return containerInitResult;
    }

    // Step 2: Execute both operations in parallel
    final Result<Result.NoData> containerProvisioningResult =
        waitForContainerProvisioning(shadowClusterJob);
    final Result<Result.NoData> clusterCreationResult =
        createSystemCluster(shadowClusterJob, shadowClusterId);

    // Step 3: Wait for both to complete
    return awaitAll(containerProvisioningResult, clusterCreationResult);
  }

  /**
   * Initiates container creation and returns when the container exists (but may not be
   * provisioned).
   *
   * @param shadowClusterJob the shadow cluster job
   * @return Result.done() if container exists, Result.inProgress() if creation initiated,
   *     Result.failed() if creation failed
   */
  protected Result<Result.NoData> initiateContainerCreation(
      final ShadowClusterJob shadowClusterJob) {

    if (shadowClusterJob.getContainerId() != null) {
      return Result.done();
    }

    final Result<Result.NoData> creationResult =
        requestContainerCreation(shadowClusterJob, shadowClusterJob.getSystemProjectId());

    if (creationResult.getStatus().isFailed()) {
      setStatusForFailure(
          shadowClusterJob,
          "Failed to initiate container creation for permutationId: " + permutationId);
      return creationResult;
    }

    return Result.done();
  }

  /**
   * Waits for container to be fully provisioned.
   *
   * @param shadowClusterJob the shadow cluster job
   * @return Result.done() when container is provisioned, Result.inProgress() while waiting,
   *     Result.failed() if provisioning failed
   */
  protected Result<Result.NoData> waitForContainerProvisioning(
      final ShadowClusterJob shadowClusterJob) {

    final ObjectId containerId = shadowClusterJob.getContainerId();
    if (containerId == null) {
      return Result.failed("Container not initialized");
    }

    final Optional<CloudProviderContainer> container;
    try {
      container =
          ndsCloudProviderContainerSvc.getContainerById(
              shadowClusterJob.getSystemProjectId(), containerId);
    } catch (final SvcException e) {
      setStatusForFailure(
          shadowClusterJob, "Failed to get container for permutationId: " + permutationId);
      return Result.failed("Failed to get container: " + e.getMessage());
    }

    if (container.map(CloudProviderContainer::isProvisioned).orElse(false)) {
      getLogger()
          .atInfo()
          .setMessage("Successfully provisioned container")
          .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
          .log();

      runAtLeastOnce(
          () ->
              shadowClusterJobSvc.updateShadowClusterJobStatus(
                  shadowClusterJob, Status.CREATED_CONTAINER, "Created container"),
          StateFields.CONTAINER_STATUS_SET);

      return Result.done();
    }

    return Result.inProgress();
  }

  protected Result<Result.NoData> createSystemCluster(
      final ShadowClusterJob shadowClusterJob, final ObjectId shadowClusterId) {
    final Result<Result.NoData> systemClusterCreationResult =
        waitForSystemClusterCreationStep(shadowClusterJob);

    if (systemClusterCreationResult.getStatus().isInProgress()) {
      return systemClusterCreationResult;
    }

    if (systemClusterCreationResult.getStatus().isFailed()) {
      setStatusForFailure(
          shadowClusterJob,
          "Failed to create a system cluster for permutationId: " + permutationId);
      return systemClusterCreationResult;
    }

    getLogger()
        .atInfo()
        .setMessage("Successfully created a system cluster")
        .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
        .log();

    final ObjectId uniqueId =
        getClusterUniqueId(
            shadowClusterJob.getSystemProjectId(), shadowClusterJob.getSystemClusterName());
    shadowClusterJob.markSystemClusterCreated(uniqueId);
    systemClusterJobSvc.saveSystemClusterJob(shadowClusterJob);
    shadowClusterSvc.updateShadowClusterDetails(
        shadowClusterId, uniqueId, shadowClusterJob.getSystemClusterName());
    return Result.done();
  }

  protected Result<Result.NoData> restoreToSystemCluster(final ShadowClusterJob shadowClusterJob) {
    final Result<Result.NoData> restoreResult = waitForSystemClusterRestoreStep(shadowClusterJob);
    if (restoreResult.getStatus().isInProgress()) {
      return restoreResult;
    }

    if (restoreResult.getStatus().isFailed()) {
      setStatusForFailure(
          shadowClusterJob,
          "Failed to restore to Shadow Cluster for permutationId: " + permutationId);
      return restoreResult;
    }

    getLogger()
        .atInfo()
        .setMessage("Successfully restored to Shadow Cluster")
        .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
        .log();

    shadowClusterJob.markRestoreCompleted();
    systemClusterJobSvc.saveSystemClusterJob(shadowClusterJob);

    runAtLeastOnce(
        () ->
            shadowClusterJobSvc.updateShadowClusterJobStatus(
                shadowClusterJob, Status.RESTORED, "Finished restoring to Shadow Cluster"),
        StateFields.RESTORED_STATUS_SET);
    return Result.done();
  }

  protected void updateExposureStatusIfAllClustersCreated(final ShadowClusterJob shadowClusterJob)
      throws SvcException {
    final Optional<Exposure> maybeExposure =
        shadowClusterSvc.getExposure(shadowClusterJob.getExposureId());

    if (maybeExposure.isEmpty()) {
      throw new SvcException(NDSErrorCode.RESOURCE_NOT_FOUND, "Exposure not found");
    }

    if (shadowClusterSvc.areAllClustersFromExposureInStatus(
        shadowClusterJob.getExposureId(), ShadowCluster.Status.PROVISIONED)) {

      shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
          shadowClusterJobSvc.getShadowClusterExposureJob(
              maybeExposure.get().getCurrentJobId(), getLogger()),
          Exposure.Status.PROVISIONED,
          "Finished provisioning all shadow clusters");
    }
  }

  /**
   * Creates a cloud container in the same region as the source cluster.
   *
   * <p>This method validates that the source cluster exists in exactly one AWS region, then calls
   * {@link NDSCloudProviderContainerSvc#upsertCloudContainer} to provision the container. After
   * successful creation, it triggers immediate planning via {@link NDSGroupDao#setPlanASAP} to
   * begin the actual container provisioning process.
   *
   * @param shadowClusterJob the shadow cluster job containing source cluster information
   * @param groupId the system project's group ID where the container will be created
   * @return {@link Result#inProgress()} if container creation was successfully initiated, {@link
   *     Result#failed(String)} if validation fails or container creation errors
   */
  Result<Result.NoData> requestContainerCreation(
      final ShadowClusterJob shadowClusterJob, final ObjectId groupId) {

    final Optional<ClusterDescription> sourceClusterDescription =
        clusterDescriptionDao.findByUniqueId(
            shadowClusterJob.getSourceProjectId(), shadowClusterJob.getSourceClusterUniqueId());
    if (sourceClusterDescription.isEmpty()) {
      return Result.failed("Source cluster description not found");
    }

    final Set<RegionName> regionNames = sourceClusterDescription.get().getRegionNames();
    if (regionNames.size() != 1) {
      return Result.failed("Source cluster must have exactly one region");
    }

    final Optional<AWSRegionName> region =
        regionNames.stream()
            .findFirst()
            .map(RegionName::getName)
            .flatMap(AWSRegionName::findByName);

    if (region.isEmpty()) {
      return Result.failed("Source cluster region is not a valid AWS region");
    }

    final ObjectId containerId;
    try {
      containerId =
          ndsCloudProviderContainerSvc.upsertCloudContainer(
              groupId, new AWSCloudProviderContainerView(null, region.get()));
    } catch (final SvcException e) {
      return Result.failed("Failed to create container: " + e.getMessage());
    }

    shadowClusterJob.setContainerId(containerId);
    systemClusterJobSvc.saveSystemClusterJob(shadowClusterJob);

    ndsGroupDao.setPlanASAP(groupId);

    return Result.inProgress();
  }

  protected Result<Result.NoData> waitForContainerDeletionStep(
      final ShadowClusterJob pShadowClusterJob) {
    return new WaitForUnusedContainerDeletionStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.ROLLBACK_WAIT_FOR_CONTAINER_DELETION),
            ndsCloudProviderContainerSvc,
            pShadowClusterJob.getSystemProjectId(),
            pShadowClusterJob.getContainerId())
        .perform();
  }

  protected Result<Result.NoData> waitForSystemClusterCreationStep(
      final ShadowClusterJob shadowClusterJob) {
    return waitForShadowClusterCreationStepFactory
        .create(
            getNDSPlanContext(),
            getState().forStep(StepNumber.WAIT_FOR_SHADOW_CLUSTER_CREATION),
            shadowClusterJob)
        .perform();
  }

  protected Result<Result.NoData> waitForSystemClusterRestoreStep(
      final SystemClusterJob systemClusterJob) {
    return new WaitForSystemClusterRestoreStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.WAIT_FOR_SHADOW_CLUSTER_RESTORE),
            systemClusterJob,
            systemClusterJobSvc,
            cpsSvc)
        .perform();
  }

  protected Result<Result.NoData> waitForShadowClusterDeletionStep(
      final ShadowClusterJob pShadowClusterJob) {
    return new WaitForSystemClusterDeletionStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.ROLLBACK_WAIT_FOR_SHADOW_CLUSTER_DELETION),
            pShadowClusterJob,
            systemClusterJobSvc)
        .perform();
  }

  protected Result<Result.NoData> waitForSystemProjectDeletionStep(
      final SystemClusterJob pShadowClusterJob) {
    return new WaitForSystemProjectDeletionStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.ROLLBACK_WAIT_FOR_SYSTEM_PROJECT_DELETION),
            pShadowClusterJob,
            systemClusterJobSvc)
        .perform();
  }

  private void setStatusForFailure(final ShadowClusterJob shadowClusterJob, final String reason) {
    getLogger()
        .atError()
        .setMessage("Setting shadow cluster job to failed status")
        .addKeyValue("reason", reason)
        .addKeyValue("systemClusterJobId", systemClusterJobId)
        .addKeyValue("permutationId", permutationId)
        .addKeyValue("logEntries", SystemClusterJobPlanLogs.fromJob(shadowClusterJob))
        .log();
    shadowClusterJobSvc.updateShadowClusterJobStatus(shadowClusterJob, Status.FAILED, reason);
  }

  @Override
  protected Object[] getArguments() {
    return new Object[] {systemClusterJobId, permutationId};
  }

  @Override
  public CloudProvider getCloudProvider() {
    return CloudProvider.NONE;
  }

  protected ObjectId getClusterUniqueId(
      final ObjectId systemGroupId, final String systemClusterName) {
    return ndsClusterSvc
        .getActiveCluster(systemGroupId, systemClusterName)
        .orElseThrow(
            () ->
                new IllegalStateException(
                    "Failed to find SystemCluster with name: " + systemClusterName))
        .getClusterDescription()
        .getUniqueId();
  }

  protected static class StateFields {
    public static final String SHADOW_CLUSTER_ID = "shadowClusterId";

    // Status tracking fields to prevent repeated status updates
    public static final String PROVISIONING_STATUS_SET = "provisioningStatusSet";
    public static final String SYSTEM_PROJECT_STATUS_SET = "systemProjectStatusSet";
    public static final String CONTAINER_STATUS_SET = "containerStatusSet";
    public static final String RESTORED_STATUS_SET = "restoredStatusSet";
    public static final String PROVISIONED_STATUS_SET = "provisionedStatusSet";
    public static final String ROLLBACK_MARK_JOBS_FAILED = "rollbackMarkJobsFailed";
  }

  private static class StepNumber {

    private static final int WAIT_FOR_SHADOW_CLUSTER_CREATION = 0;
    private static final int WAIT_FOR_SHADOW_CLUSTER_RESTORE = 1;
    private static final int ROLLBACK_WAIT_FOR_SHADOW_CLUSTER_DELETION = 2;
    private static final int ROLLBACK_WAIT_FOR_CONTAINER_DELETION = 3;
    private static final int ROLLBACK_WAIT_FOR_SYSTEM_PROJECT_DELETION = 4;
  }
}
