package com.xgen.svc.nds.planner;

import static com.xgen.cloud.cps.backupjob._public.model.Policy.DEFAULT_RETENTION;

import com.amazonaws.services.ec2.model.VolumeType;
import com.google.common.collect.Lists;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.ExtraRetentionSetting;
import com.xgen.cloud.cps.backupjob._public.model.Policy;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.core._public.util.CpsMetricsUtil;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import com.xgen.cloud.cps.pit._public.model.ProviderStorage;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._private.dao.CpsBackupCursorFileListsDao;
import com.xgen.cloud.cps.restore._public.model.BackupReplicaSetType;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Attempt;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.PlanningType;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.RequestingUser;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.SnapshotAttemptedInstance;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Status;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Type;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ShardedClusterBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ShardedClusterBackupSnapshot.SnapshotMember;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.BackupRestoreShardIdRestoreMap;
import com.xgen.cloud.deployment._public.model.BalancerSettings;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.EncryptionProviderType;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessType;
import com.xgen.cloud.deployment._public.model.ReplicaSet;
import com.xgen.cloud.deployment._public.model.ReplicaSetMember;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.metrics._public.model.MetricsException;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionNameHelper;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.PlanAbandonedException;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.planner.movetypes.CreateSnapshotMove;
import com.xgen.svc.nds.planner.snapshot.CpsReplSetSnapshotMove;
import com.xgen.svc.nds.svc.cps.CpsPitSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.util.DiskBackupUtil;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.BsonTimestamp;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BackupSnapshotUtils {
  private static final Logger LOG = LoggerFactory.getLogger(BackupSnapshotUtils.class);
  public static final int MAX_DISK_IOPS_FOR_OPTIMIZED_AWS_DA_RESTORE = 16000;
  public static final int IOPS_PER_GB_FOR_IO2 = 1000;
  public static final int IOPS_PER_GB_FOR_IO1 = 50;

  public static final Histogram CPS_SNAPSHOT_SPEED =
      new Histogram.Builder()
          .name("mms_nds_backup_snapshot_speed_mega_bytes_per_second")
          .help("Speed of cloud provider snapshots")
          .labelNames("provider", "region")
          .exponentialBuckets(.25, 2, 10)
          .register();
  public static final Counter CPS_SNAPSHOT_INSTANCE_SELECTIONS =
      new Counter.Builder()
          .name("mms_cps_snapshot_instance_selections_total")
          .help(
              "Selections of instances to snapshot. "
                  + "retry=true if attempting to snapshot an instance we tried previously. "
                  + "same_disk=true if using the same disk as the prior snapshot.")
          .labelNames("retry", "same_disk", "provider")
          .register();
  public static final Counter CPS_SNAPSHOT_INSTANCE_UNHEALTHY_SKIPS =
      new Counter.Builder()
          .name("mms_cps_snapshot_instance_unhealthy_skips_total")
          .help("Counts every time we skip snapshotting an instance because it is unhealthy.")
          .labelNames("provider")
          .register();

  /**
   * Verify that encryption details on the sharded cluster snapshot are still correct. There is a
   * possibility of a race condition. For individual shards this is already handled in the
   * individual Create Snapshot Step.
   */
  public static BackupSnapshotEncryptionCredentials validateAndGetCommonEncryption(
      final Logger pLogger, final List<BackupSnapshotEncryptionCredentials> credentials) {

    final List<BackupSnapshotEncryptionCredentials> uniqueCredentials =
        credentials.stream().distinct().toList();

    if (uniqueCredentials.isEmpty()) {
      throw new IllegalStateException("No snapshot encryption credential");
    }

    if (uniqueCredentials.size() == 1) {
      return uniqueCredentials.get(0);
    }

    pLogger.info(
        "Encryption at Rest keys changed while sharded cluster snapshot was requested. Rolling"
            + " back.");

    pLogger.info(
        "Note that encrypting the same key now yields different results. Please verify the"
            + " unencrypted fields. In other words, different values for the encrypted keys"
            + " between two objects doesn't necessarily mean they are different values when"
            + " unencrypted.");

    pLogger.info(
        "inconsistent credentials: {} vs {}",
        uniqueCredentials.get(0).toDBObject(),
        uniqueCredentials.get(1).toDBObject());

    throw new PlanAbandonedException(
        NDSErrorCode.ENCRYPTION_AT_REST_KEYS_POSSIBLY_INCONSISTENT_WITH_SNAPSHOT);
  }

  public Optional<ObjectId> findInstanceIdOfPrimary(
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final Logger pLogger,
      final ObjectId pGroupId,
      final Cluster pCluster,
      final String pRsId) {
    final List<HostCluster> hostClusterList =
        pBackupDependenciesProvider
            .getHostClusterLifecycleSvc()
            .findHostClustersByGroupId(pGroupId, true);
    final Optional<HostCluster> hostClusterForReplicaSet =
        hostClusterList.stream()
            .filter(HostCluster::isReplicaSet)
            .filter(h -> h.getReplicaSetIds().contains(pRsId))
            .findFirst();
    if (hostClusterForReplicaSet.isEmpty()) {
      pLogger.debug("finding InstanceId of Primary: hostClusterForReplicaSet is empty");
    }

    final Optional<InstanceHardware> primaryInstanceHardware =
        hostClusterForReplicaSet.flatMap(
            hc -> DiskBackupUtil.getPrimaryForReplicaSet(hc, pCluster, pLogger));

    ObjectId primaryInstanceId = null;
    if (primaryInstanceHardware.isPresent()) {
      primaryInstanceId = primaryInstanceHardware.get().getInstanceId();
    }
    return Optional.ofNullable(primaryInstanceId);
  }

  public static long getUsedDiskSpace(
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final Group pGroup,
      final String pClusterName,
      final InstanceHardware pInstanceHardware,
      final long pDurationMillis) {
    final long usedDiskSpace;
    try {
      usedDiskSpace =
          getUsedDiskSpaceInBytes(
              pBackupDependenciesProvider,
              pGroup,
              pInstanceHardware.getHostnameForAgents().get(),
              pDurationMillis);
    } catch (final MetricsException me) {
      LOG.warn(
          "Error getting the metrics for used disk space for cluster={} with error={}"
              + " errorMessage={}",
          pClusterName,
          me.getErrorCode().getMessage(),
          me.getMessage());
      throw new IllegalStateException(
          String.format("Cannot determine used disk size for cluster %s", pClusterName), me);
    }
    return usedDiskSpace;
  }

  private static long getUsedDiskSpaceInBytes(
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final Group pGroup,
      final String pHostname,
      final long pDurationMillis)
      throws MetricsException {
    return DiskBackupUtil.getUsedDiskSpaceInBytes(
        pGroup,
        pBackupDependenciesProvider.getAutomationConfigSvc(),
        pBackupDependenciesProvider.getMetricsSvc(),
        pHostname,
        pDurationMillis);
  }

  public static void calculateAndSetTotalUsedSpace(
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ShardedClusterBackupSnapshot rootSnapshot,
      final List<ReplicaSetBackupSnapshot> members) {
    long totalUsedDiskSpace = 0L;
    for (final BackupSnapshot snapshot : members) {
      totalUsedDiskSpace += snapshot.getUsedDiskSpace();
    }

    final SnapshotUpdate snapshotUpdate = new SnapshotUpdate().setUsedDiskSpace(totalUsedDiskSpace);

    pBackupDependenciesProvider
        .getCpsSvc()
        .updateBackupSnapshot(rootSnapshot.getId(), snapshotUpdate);
  }

  /**
   * Retrieves the saved snapshotId or finds the 'earliest' snapshot that is queued for this
   * cluster.
   */
  public static ObjectId findEarliestQueuedSnapshot(
      final ObjectId pGroupId,
      final ObjectId pUniqueId,
      final BackupSnapshotDao pBackupSnapshotDao) {
    // Retrieve only queued snapshots or in progress
    final List<BackupSnapshot> snapshots =
        pBackupSnapshotDao.findActionableNonCopyByCluster(pGroupId, pUniqueId);

    if (snapshots.isEmpty()) {
      return null;
    }

    final BackupSnapshot earliestQueuedSnapshot =
        snapshots.stream()
            .filter(
                snapshot ->
                    snapshot.getSnapshotStatus().equals(Status.QUEUED)
                        && !snapshot.getType().equals(Type.FALLBACK))
            .min(Comparator.comparing(BackupSnapshot::getScheduledCreationDate))
            .orElse(null);

    if (earliestQueuedSnapshot == null) {
      return null;
    }

    return earliestQueuedSnapshot.getId();
  }

  public static void updateGeoShardedShardWithRegion(
      final CpsSvc pCpsSvc, final ObjectId pShardSnapshotId) {
    final SnapshotUpdate snapshotUpdate = new SnapshotUpdate().setIsGeoSharded(true);
    pCpsSvc.updateBackupSnapshot(pShardSnapshotId, snapshotUpdate);
  }

  public static ObjectId queueScheduledSnapshot(
      final Date pNow,
      final ClusterDescription pCluster,
      final BackupJob pBackupJob,
      final BackupSnapshotDao pBackupSnapshotDao,
      final boolean createConcurrentSnapshots,
      final boolean createProtectedSnapshot) {
    final List<PolicyItem> readyPolicyItems =
        pBackupJob.getPolicies().get(0).getPolicyItems().stream()
            .filter(
                policyItem ->
                    pNow.after(policyItem.getNextSnapshotDate())
                        || pNow.equals(policyItem.getNextSnapshotDate()))
            .collect(Collectors.toList());

    final List<ObjectId> policyItemIds =
        readyPolicyItems.stream().map(PolicyItem::getId).collect(Collectors.toList());

    final PolicyItem mainPolicyItem = getPolicyItemWithLongestRetention(readyPolicyItems);
    final ObjectId mainPolicyItemId = mainPolicyItem.getId();

    LOG.trace(
        "queueing snapshot for group {} cluster {} for date {}",
        pBackupJob.getProjectId(),
        pBackupJob.getClusterName(),
        pBackupJob.getNextSnapshotDate());
    return queueSnapshot(
        pCluster,
        pBackupSnapshotDao,
        pBackupJob.getProjectId(),
        BackupSnapshot.Type.SCHEDULED,
        pBackupJob.getNextSnapshotDate(),
        null,
        null,
        null,
        mainPolicyItem.getRetentionUnit(),
        mainPolicyItem.getFrequencyType(),
        policyItemIds,
        mainPolicyItemId,
        null,
        null,
        null,
        createConcurrentSnapshots,
        createProtectedSnapshot);
  }

  public static void updateSnapshotFailedForRollback(
      final CpsSvc cpsSvc, final ObjectId snapshotId) {
    final SnapshotUpdate snapshotUpdate = getSnapshotUpdateForFailedSnapshot();
    updateSnapshotFieldForRelatedSnapshots(snapshotId, cpsSvc, snapshotUpdate);
  }

  /**
   * We add 1 day buffer to allow rollback() to properly cleanup first, then if it still hasn't been
   * purged after a day, we'll have CpsGcSvc as a "fallback" to pick it up
   *
   * @return snapshotUpdate object to set the status to failed and scheduledDeletionDate to 1 day
   *     later
   */
  public static SnapshotUpdate getSnapshotUpdateForFailedSnapshot() {
    final Date scheduledDeletionDate = DateUtils.addDays(new Date(), 1);
    return new SnapshotUpdate()
        .setStatus(Status.FAILED)
        .setScheduledDeletionDate(scheduledDeletionDate);
  }

  public static void queueReplicaResilientSnapshot(
      final ClusterDescription cd,
      final BackupSnapshotDao dao,
      final ObjectId failedSnapshotId,
      final boolean createConcurrentSnapshots,
      final boolean createProtectedSnapshot) {
    if (cd.isDeleteRequested()
        || cd.getState() == ClusterDescription.State.DELETED
        || !cd.isDiskBackupEnabled()) {
      LOG.info(
          "Not queueing snapshot because cluster is deleted or has backups disabled, cluster name:"
              + " {}",
          cd.getName());
      return;
    }

    final ReplicaSetBackupSnapshot snap = dao.findReplicaSetSnapshotById(failedSnapshotId).get();

    final ObjectId replSnapshotId = new ObjectId();
    final String rsId = snap.getRsId();

    final SnapshotUpdate baseSnapshotUpdate = makeBaseUpdateForFallbackSnapshots(cd, snap);
    baseSnapshotUpdate.setPlanningType(
        createConcurrentSnapshots
            ? BackupSnapshot.PlanningType.CONCURRENT
            : BackupSnapshot.PlanningType.BLOCKING);
    baseSnapshotUpdate.setProtected(createProtectedSnapshot);
    baseSnapshotUpdate.setBackupReplicaSetType(snap.getBackupReplicaSetType());
    baseSnapshotUpdate.setId(replSnapshotId).setRsId(rsId).setEmptySnapshotField();
    dao.addBackupSnapshot(baseSnapshotUpdate);
  }

  public static void queueShardedResilientSnapshot(
      final ClusterDescription cd,
      final CpsSvc cpsSvc,
      final BackupSnapshotDao dao,
      final ShardedClusterBackupSnapshot snap,
      final boolean createConcurrentSnapshots,
      final boolean createProtectedSnapshots) {
    if (cd.isDeleteRequested()
        || cd.getState() == ClusterDescription.State.DELETED
        || !cd.isDiskBackupEnabled()) {
      LOG.info(
          "Not queueing snapshot because cluster is deleted or has backups disabled, cluster name:"
              + " {}",
          cd.getName());
      return;
    }

    final List<ShardedClusterBackupSnapshot.SnapshotMember> members = new ArrayList<>();
    final ObjectId parentSnapshotId = new ObjectId();
    final PlanningType planningType =
        createConcurrentSnapshots ? PlanningType.CONCURRENT : PlanningType.BLOCKING;

    final List<ReplicaSetHardware> shards =
        getAllReplicaSetHardware(cpsSvc, cd.getGroupId(), cd.getName());
    for (final ReplicaSetHardware replicaSetHardware : shards) {
      final ObjectId shardSnapshotId = new ObjectId();
      final String rsId = replicaSetHardware.getRsId();

      members.add(new ShardedClusterBackupSnapshot.SnapshotMember(shardSnapshotId, rsId, null));

      final SnapshotUpdate baseSnapshotUpdate = makeBaseUpdateForFallbackSnapshots(cd, snap);

      baseSnapshotUpdate
          .setId(shardSnapshotId)
          .setRsId(rsId)
          .setShard(true)
          .setEmptySnapshotField()
          .setBackupReplicaSetType(getBackupReplicaSetType(replicaSetHardware))
          .setParentSnapshotId(parentSnapshotId)
          .setProtected(createProtectedSnapshots)
          .setPlanningType(planningType);

      dao.addBackupSnapshot(baseSnapshotUpdate);
    }

    final SnapshotUpdate baseSnapshotUpdate = makeBaseUpdateForFallbackSnapshots(cd, snap);
    baseSnapshotUpdate.setPlanningType(planningType);
    baseSnapshotUpdate.setProtected(createProtectedSnapshots);
    baseSnapshotUpdate.setId(parentSnapshotId).setSnapshotMembers(members);
    dao.addBackupSnapshot(baseSnapshotUpdate);
  }

  private static List<ReplicaSetHardware> getAllReplicaSetHardware(
      final CpsSvc pCpsSvc, final ObjectId pGroupId, final String pClusterName) {
    return pCpsSvc
        .getClusterSvc()
        .getActiveCluster(pGroupId, pClusterName)
        .get()
        .getLiveReplicaSets();
  }

  private static SnapshotUpdate makeBaseUpdateForFallbackSnapshots(
      final ClusterDescription cd, final BackupSnapshot snap) {
    final Date now = new Date();
    final Date newScheduledDeletionDate =
        snap.getScheduledDeletionDate() != null
            ? computeNewScheduledDeletionDate(
                snap.getScheduledDeletionDate(), snap.getScheduledCreationDate(), now)
            : null;
    return new SnapshotUpdate()
        .setProjectId(cd.getGroupId())
        .setClusterName(cd.getName())
        .setDeploymentClusterName(cd.getDeploymentClusterName())
        .setClusterUniqueId(cd.getUniqueId())
        .setScheduledCreationDate(now)
        .setSnapshotInitiationDate(now)
        .setCloudProviders(Collections.emptyList())
        .setDeleted(false)
        .setPurged(false)
        .setScheduledDeletionDate(newScheduledDeletionDate)
        .setUsedDiskSpace(0L)
        .setEncryptionDetails(null)
        .setStatus(Status.QUEUED)
        .setType(Type.FALLBACK)
        .setFrequencyType(snap.getFrequencyType())
        .setOverrideRetentionPolicy(false)
        .setBackupRetentionUnit(snap.getRetentionUnit())
        .setFailedSnapshotId(snap.getId())
        .setPolicyItemIds(snap.getPolicyItems());
  }

  // If there are multiple policy with the same retention, then we pick the less frequent policy
  static PolicyItem getPolicyItemWithLongestRetention(final List<PolicyItem> pPolicyItemList) {
    if (pPolicyItemList == null || pPolicyItemList.isEmpty()) {
      return null;
    }
    return Collections.max(pPolicyItemList);
  }

  public static ObjectId queueSnapshot(
      final ClusterDescription pCluster,
      final BackupSnapshotDao pBackupSnapshotDao,
      final ObjectId pGroupId,
      final Type pBackupSnapshotType,
      final Date pScheduledCreationDate,
      final Date pScheduledDeletionDate,
      final String pDescription,
      final AppUser pRequestingUser,
      final BackupRetentionUnit retentionUnit,
      final BackupFrequencyType frequencyType,
      final List<ObjectId> policyItemIds,
      final ObjectId mainPolicyItemId,
      final ObjectId retriedSnapshotId,
      final List<Attempt> attempts,
      final Long previousAttemptCursorId,
      final boolean createConcurrentSnapshots,
      final boolean createProtectedSnapshot) {
    final ObjectId backupSnapshotId = new ObjectId();
    final String pRsId = NDSDefaults.getReplicaSetNameForUnshardedCluster(pCluster);

    if (pCluster.isServerlessTenantCluster()) {
      throw new IllegalStateException("Serverless cluster backup snapshot cannot be queued.");
    }

    if (!pCluster.isFlexOrSharedTenantCluster() && !pCluster.getClusterType().isSharded()) {
      pBackupSnapshotDao.addBackupSnapshot(
          new SnapshotUpdate()
              .setId(backupSnapshotId)
              .setBackupReplicaSetType(BackupReplicaSetType.REPLICA_SET)
              .setProjectId(pGroupId)
              .setClusterName(pCluster.getName())
              .setDeploymentClusterName(pCluster.getDeploymentClusterName())
              .setRsId(pRsId)
              .setClusterUniqueId(pCluster.getUniqueId())
              .setScheduledCreationDate(pScheduledCreationDate)
              .setScheduledDeletionDate(pScheduledDeletionDate)
              .setDeleted(false)
              .setPurged(false)
              .setEmptySnapshotField()
              .setCloudProviders(Collections.emptyList())
              .setStatus(Status.QUEUED)
              .setType(pBackupSnapshotType)
              .setDescription(pDescription)
              .setRequestingUser(new RequestingUser(pRequestingUser))
              .setFrequencyType(frequencyType)
              .setPolicyItemIds(policyItemIds)
              .setMainPolicyItemId(mainPolicyItemId)
              .setOverrideRetentionPolicy(false)
              .setBackupRetentionUnit(retentionUnit)
              .setPlanningType(
                  createConcurrentSnapshots
                      ? BackupSnapshot.PlanningType.CONCURRENT
                      : BackupSnapshot.PlanningType.BLOCKING)
              .setProtected(createProtectedSnapshot)
              .setRetriedSnapshotId(retriedSnapshotId)
              .setAttempts(attempts)
              .setPreviousAttemptCursorId(previousAttemptCursorId)
              .setDisaggregatedStorageSystem(pCluster.isDisaggregatedStorageSystem()));
    } else if (pCluster instanceof ShardedClusterDescription shardedClusterDescription) {
      pBackupSnapshotDao.addShardedClusterBackupSnapshot(
          backupSnapshotId,
          pGroupId,
          pCluster.getName(),
          pCluster.getDeploymentClusterName(),
          pCluster.getUniqueId(),
          pScheduledCreationDate,
          null,
          pScheduledDeletionDate,
          null,
          null,
          0L,
          Collections.emptyList(),
          null,
          Collections.emptyMap(),
          pBackupSnapshotType,
          pDescription,
          Status.QUEUED,
          pRequestingUser,
          frequencyType,
          policyItemIds,
          mainPolicyItemId,
          retentionUnit,
          shardedClusterDescription.getConfigServerType(),
          createConcurrentSnapshots
              ? BackupSnapshot.PlanningType.CONCURRENT
              : BackupSnapshot.PlanningType.BLOCKING,
          retriedSnapshotId,
          attempts,
          createProtectedSnapshot);
    }
    return backupSnapshotId;
  }

  public static AppUser convertToAppUser(RequestingUser requestingUser) {
    if (requestingUser == null) {
      return null;
    }
    AppUser appUser = new AppUser();
    appUser.setUsername(requestingUser.getUserName());
    appUser.setPrimaryEmail(requestingUser.getEmail());
    return appUser;
  }

  /**
   * If there are more than one optime in pLinearizableReadOptimeList, this function picks the
   * earliest optime to save in the snapshot metadata
   */
  public static void savePitSentinelOptime(
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ObjectId pRootSnapshotId,
      final List<BsonTimestamp> pLinearizableReadOptimeList) {
    final BsonTimestamp minLinearizedReadOptime =
        pLinearizableReadOptimeList.stream()
            .min(BsonTimestamp::compareTo)
            .orElseThrow(
                () -> new RuntimeException("Expecting at least one linearizable read optime."));
    final BSONTimestamp minLinearizedReadOptimeBSONTimestamp =
        new BSONTimestamp(minLinearizedReadOptime.getTime(), minLinearizedReadOptime.getInc());
    pBackupDependenciesProvider
        .getCpsSvc()
        .updateBackupSnapshot(
            pRootSnapshotId,
            new SnapshotUpdate().setPitSentinelOptime(minLinearizedReadOptimeBSONTimestamp));
  }

  public static void syncPitSentinelOptimeFromWtc(
      final BackupDependenciesProvider pBackupDependenciesProvider, final ObjectId snapshotId) {

    final BackupSnapshot backupSnapshot =
        pBackupDependenciesProvider
            .getBackupSnapshotDao()
            .findById(snapshotId)
            .orElseThrow(() -> new RuntimeException("snapshot not found: " + snapshotId));
    if (!backupSnapshot.isWtcSnapshot()) {
      return;
    }
    if (backupSnapshot.isShardedCluster()) {
      final List<BsonTimestamp> checkpointBsonTsList = new ArrayList<>();
      final ShardedClusterBackupSnapshot shardedClusterBackupSnapshot =
          (ShardedClusterBackupSnapshot) backupSnapshot;
      for (final ObjectId shardSnapshotId : shardedClusterBackupSnapshot.getMemberIds()) {
        final ReplicaSetBackupSnapshot shardSnapshot =
            pBackupDependenciesProvider
                .getBackupSnapshotDao()
                .findReplicaSetSnapshotById(shardSnapshotId)
                .orElseThrow(
                    () -> new RuntimeException("shard snapshot not found: " + shardSnapshotId));
        final BsonTimestamp checkpointBsonTs =
            syncShardPitSentinelOptimeFromWtc(pBackupDependenciesProvider, shardSnapshot);
        checkpointBsonTsList.add(checkpointBsonTs);
      }
      savePitSentinelOptime(pBackupDependenciesProvider, snapshotId, checkpointBsonTsList);
    } else {
      syncShardPitSentinelOptimeFromWtc(
          pBackupDependenciesProvider, (ReplicaSetBackupSnapshot) backupSnapshot);
    }
  }

  private static BsonTimestamp syncShardPitSentinelOptimeFromWtc(
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ReplicaSetBackupSnapshot replicaSetBackupSnapshot) {
    final ReplicaSetBackupSnapshot.CpsWtCheckpoint wtCheckpoint =
        replicaSetBackupSnapshot.getCpsWtCheckpoint();
    final BSONTimestamp checkpointTs =
        wtCheckpoint.isExtended()
            ? wtCheckpoint.getExtendedToTimestamp()
            : wtCheckpoint.getCheckpointTimestamp();
    final BsonTimestamp checkPointBsonTs =
        new BsonTimestamp(checkpointTs.getTime(), checkpointTs.getInc());
    savePitSentinelOptime(
        pBackupDependenciesProvider,
        replicaSetBackupSnapshot.getId(),
        Collections.singletonList(checkPointBsonTs));
    return checkPointBsonTs;
  }

  // NOTE: Powermockito doesn't work well with overloaded methods. So, we named the similar
  // methods different: getScheduledDeletionDateBackup and getScheduledDeletionDatePolicy
  public static Pair<Date, BackupRetentionUnit> getScheduledDeletionDateBackup(
      final BackupJob pBackupJob,
      final BackupSnapshot backupSnapshot,
      final Date pSnapshotCompletedDate) {
    return getScheduledDeletionDatePolicy(
        pBackupJob.getPolicies().get(0), backupSnapshot, pSnapshotCompletedDate);
  }

  public static BackupReplicaSetType getBackupReplicaSetType(
      final ReplicaSetHardware replicaSetHardware) {
    if (replicaSetHardware.isConfigShard()) {
      return BackupReplicaSetType.CONFIG_SHARD;
    } else if (replicaSetHardware.containsConfigData()) {
      return BackupReplicaSetType.CONFIG;
    } else {
      return BackupReplicaSetType.SHARD;
    }
  }

  public static Pair<Date, BackupRetentionUnit> getScheduledDeletionDatePolicy(
      final Policy pPolicy,
      final BackupSnapshot backupSnapshot,
      final Date pSnapshotCompletedDate) {
    if (backupSnapshot.isOnDemand()) {
      if (backupSnapshot.getScheduledDeletionDate() == null
          || backupSnapshot.getScheduledCreationDate() == null) {
        LOG.error(
            "The scheduledDeletionDate or scheduledCreationDate is null for the on-demand snapshot."
                + " Use default retention. snapshotId: {}",
            backupSnapshot.getId());
        return Pair.of(
            new Date(pSnapshotCompletedDate.getTime() + DEFAULT_RETENTION.toMillis()),
            backupSnapshot.getRetentionUnit());
      }
      final Date scheduledDeletionDate =
          computeNewScheduledDeletionDate(
              backupSnapshot.getScheduledDeletionDate(),
              backupSnapshot.getScheduledCreationDate(),
              pSnapshotCompletedDate);
      return Pair.of(scheduledDeletionDate, backupSnapshot.getRetentionUnit());
    } else {
      final Pair<Duration, BackupRetentionUnit> maxRetention =
          pPolicy.getMaxRetention(backupSnapshot.getPolicyItems());
      final Duration scheduledRetention = maxRetention.getLeft();
      final Date scheduledDeletionDate =
          new Date(pSnapshotCompletedDate.getTime() + scheduledRetention.toMillis());
      return Pair.of(scheduledDeletionDate, maxRetention.getRight());
    }
  }

  // NOTE: this computation of retention time makes assumptions on how scheduled deletion date
  // was computed for on-demand snapshots. If implementation changes, this will become invalid.
  private static Date computeNewScheduledDeletionDate(
      final Date pScheduledDeletionDate,
      final Date pScheduledCreationDate,
      final Date pSnapshotBaseDate) {
    final long existingDeletionTime = pScheduledDeletionDate.getTime();
    final long creationTime = pScheduledCreationDate.getTime();
    final long computedRetentionTime = existingDeletionTime - creationTime;
    return new Date(computedRetentionTime + pSnapshotBaseDate.getTime());
  }

  protected static String formatBalancerTime(final String time) {
    final String[] timeTokens = time.split(":", 2);
    final LocalTime localTime =
        LocalTime.of(Integer.parseInt(timeTokens[0]), Integer.parseInt(timeTokens[1]));
    final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
    return localTime.format(dateTimeFormatter);
  }

  public static Optional<ObjectId> findInstanceIdOfHiddenNode(
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ObjectId pGroupId,
      final String clusterName,
      final String rsId) {
    final Optional<Cluster> cluster =
        pBackupDependenciesProvider.getNDSClusterSvc().getActiveCluster(pGroupId, clusterName);

    final ReplicaSetHardware replicaSetHardware =
        cluster.get().getReplicaSets().stream()
            .filter(rsh -> rsh.containsShardData() && rsh.getRsId().equals(rsId))
            .findFirst()
            .get();

    final Optional<InstanceHardware> nvmeHardware =
        replicaSetHardware.getInternalHardware().stream()
            .filter(InstanceHardware::isNVMe)
            .findFirst();
    return nvmeHardware.map(InstanceHardware::getInstanceId);
  }

  public static Optional<Host> findHost(
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ObjectId pGroupId,
      final String hostname,
      final int port) {
    final List<Host> list =
        pBackupDependenciesProvider.getHostSvc().findHostsByGroupId(pGroupId, false, false, false);

    return list.stream()
        .filter(h -> h.getName().equals(hostname) && h.getPort() == port)
        .findFirst();
  }

  public static AutomationConfig clearDirectAttachRestoreConfigForCluster(
      final AutomationConfig automationConfig, final ClusterDescription clusterDescription) {

    NDSDefaults.filterProcessesByClusterDescription(
            automationConfig.getDeployment().getProcesses(), clusterDescription)
        .stream()
        .filter(p -> p.getProcessType() == ProcessType.MONGOD)
        .filter(p -> p.getDirectAttachVerificationKey() != null)
        .forEach(
            p -> {
              p.setBackupRestoreRsVersion(null);
              p.setBackupRestoreElectionTerm(null);
              p.setBackupRestoreCheckpointTime(null);
              p.setBackupRestoreJobId(null);
              p.setBackupRestoreSystemUsersUUID(null);
              p.setBackupRestoreSystemRolesUUID(null);
              p.setDirectAttachVerificationKey(null);
              p.setDirectAttachSourceClusterName(null);
              p.setDirectAttachFilterByFilelists(null);
              p.setDirectAttachPreWarmGlobs(null);
              p.setDirectAttachPreWarmStrategy(null);
              p.setDirectAttachPreWarmConcurrency(null);
              p.setDirectAttachPreWarmBlockSize(null);
              p.setDirectAttachPreWarmBlockReadSize(null);
              p.setDirectAttachReplWriterThreadCount(null);
              p.setDirectAttachPreWarmShouldUseGoBinary(null);
              p.setDirectAttachPreWarmShouldWarmEmptyBlocks(null);
              p.setDirectAttachPreWarmShouldSkipNonPriority(null);
              p.setOplogCollectionFileName(null);
              p.setBackupPitRestoreType(null);
              p.setBackupRestoreDesiredTime(null);
              p.setBackupShardIdRestoreMaps(null);
              p.setBackupRestoreIsConfigShard(null);
              p.setBackupRestoreBalancerSettings(null);
              p.setBackupRestoreConfigSettingsUUID(null);
              p.setBackupRestoreIsSuccessiveUpgrade(false);
            });
    return automationConfig;
  }

  public static AutomationConfig setBalancerSettingsForCluster(
      final AutomationConfig automationConfig,
      final ClusterDescription clusterDescription,
      final BalancerSettings balancerSettings,
      final String configSettingsUUID) {

    NDSDefaults.filterProcessesByClusterDescription(
            automationConfig.getDeployment().getProcesses(), clusterDescription)
        .stream()
        .filter(Process::isConfigSvr)
        .forEach(
            p -> {
              p.setBackupRestoreBalancerSettings(balancerSettings);
              p.setBackupRestoreConfigSettingsUUID(configSettingsUUID);
            });

    return automationConfig;
  }

  public static void syncRootSnapshotInitiationDate(
      final BackupDependenciesProvider pBackupDependenciesProvider, final ObjectId rootSnapshotId) {
    final BackupSnapshot rootSnapshot =
        pBackupDependenciesProvider
            .getBackupSnapshotDao()
            .findById(rootSnapshotId)
            .orElseThrow(() -> new RuntimeException("snapshot not found: " + rootSnapshotId));
    final ShardedClusterBackupSnapshot shardedSnapshot =
        (ShardedClusterBackupSnapshot) rootSnapshot;
    Date maxSnapshotInitiationDate = new Date(0);
    for (final ShardedClusterBackupSnapshot.SnapshotMember member :
        shardedSnapshot.getSnapshotMembers()) {
      final ObjectId childSnapshotId = member.getSnapshotId();
      final BackupSnapshot childSnapshot =
          pBackupDependenciesProvider
              .getBackupSnapshotDao()
              .findById(childSnapshotId)
              .orElseThrow(() -> new RuntimeException("snapshot not found: " + childSnapshotId));
      if (childSnapshot.getSnapshotInitiationDate().after(maxSnapshotInitiationDate)) {
        maxSnapshotInitiationDate = childSnapshot.getSnapshotInitiationDate();
      }
    }
    pBackupDependenciesProvider
        .getCpsSvc()
        .updateBackupSnapshot(
            rootSnapshotId,
            new SnapshotUpdate().setSnapshotInitiationDate(maxSnapshotInitiationDate));
  }

  public static void addRootSnapshotCloudProviders(
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ObjectId rootSnapshotId,
      final CloudProvider cloudProvider) {
    final BackupSnapshot rootSnapshot =
        pBackupDependenciesProvider
            .getBackupSnapshotDao()
            .findById(rootSnapshotId)
            .orElseThrow(() -> new RuntimeException("snapshot not found: " + rootSnapshotId));
    final ShardedClusterBackupSnapshot shardedSnapshot =
        (ShardedClusterBackupSnapshot) rootSnapshot;
    final Set<CloudProvider> cloudProviderSet = shardedSnapshot.getProviders();
    cloudProviderSet.add(cloudProvider);

    pBackupDependenciesProvider
        .getCpsSvc()
        .updateBackupSnapshot(
            rootSnapshotId,
            new SnapshotUpdate().setCloudProviders(new ArrayList<>(cloudProviderSet)));
  }

  /**
   * Applies the snapshotUpdate to the given snapshot as well as it's siblings/parents/children.
   * NOTE: The exception is copy snapshots, where we only update the given snapshotId
   */
  public static void updateSnapshotFieldForRelatedSnapshots(
      final ObjectId snapshotId, final CpsSvc cpsSvc, final SnapshotUpdate snapshotUpdate) {
    final Optional<BackupSnapshot> snapshotOpt = cpsSvc.getBackupSnapshotDao().findById(snapshotId);
    if (snapshotOpt.isEmpty()) {
      throw new IllegalStateException("Snapshot is missing: id=" + snapshotId);
    }

    final BackupSnapshot snapshot = snapshotOpt.get();

    if (snapshot instanceof ReplicaSetBackupSnapshot replicaSetBackupSnapshot) {
      if (replicaSetBackupSnapshot.isShard() && !replicaSetBackupSnapshot.isCopy()) {
        updateSnapshotFieldForRelatedSnapshots(
            replicaSetBackupSnapshot.getParentSnapshotId(), cpsSvc, snapshotUpdate);
      } else {
        cpsSvc.updateBackupSnapshot(replicaSetBackupSnapshot.getId(), snapshotUpdate);
      }
    } else if (snapshot instanceof ShardedClusterBackupSnapshot shardedClusterBackupSnapshot) {
      cpsSvc.updateBackupSnapshot(shardedClusterBackupSnapshot.getId(), snapshotUpdate);
      for (final ObjectId memberSnapshotId : shardedClusterBackupSnapshot.getMemberIds()) {
        cpsSvc.updateBackupSnapshot(memberSnapshotId, snapshotUpdate);
      }
    } else {
      throw new UnsupportedOperationException("Snapshot type unsupported for update");
    }
  }

  public static Map<String, ReplicaSetHardware> getRsIdToReplicaSetHardware(
      final List<ReplicaSetHardware> replicaSetHardwareList) {
    final Map<String, ReplicaSetHardware> rsIdToReplicaSetHardware = new HashMap<>();

    for (final ReplicaSetHardware replicaSetHardware : replicaSetHardwareList) {
      rsIdToReplicaSetHardware.put(replicaSetHardware.getRsId(), replicaSetHardware);
    }

    return rsIdToReplicaSetHardware;
  }

  public static VolumeType getVolumeType(final AWSInstanceHardware awsInstanceHardware) {
    return VolumeType.fromValue(awsInstanceHardware.getNVMePrioritizedEBSVolumeType().get());
  }

  public static Integer getIopsForAwsDirectAttach(
      final AWSInstanceHardware awsInstanceHardware, final Integer newDiskSize) {
    final VolumeType volumeType = getVolumeType(awsInstanceHardware);

    Integer diskIops = null;
    if (volumeType.equals(VolumeType.Gp2)) {
      final AWSNDSInstanceSize instanceSize =
          AWSNDSInstanceSize.valueOf(awsInstanceHardware.getInstanceSize().get());
      diskIops = instanceSize.getStandardEBSIOPS(newDiskSize);
    } else if (volumeType.equals(VolumeType.Gp3)) {
      final AWSNDSInstanceSize instanceSize =
          AWSNDSInstanceSize.valueOf(awsInstanceHardware.getInstanceSize().get());
      diskIops = instanceSize.getGP3StandardEBSIOPS(newDiskSize);
    } else if (volumeType.equals(VolumeType.Io1) || volumeType.equals(VolumeType.Io2)) {
      diskIops = awsInstanceHardware.getNVMePrioritizedDiskIOPS().get();
    }

    return diskIops;
  }

  public static int getOptimizedAwsDirectAttachDiskIOPS(
      final VolumeType volumeType,
      final int newDiskSize,
      final AWSInstanceHardware instanceHardware) {

    final int instanceIOPS = instanceHardware.getNVMePrioritizedDiskIOPS().orElse(0);
    final String instanceVolumeType = instanceHardware.getNVMePrioritizedEBSVolumeType().get();

    if (volumeType == VolumeType.Io2 || volumeType == VolumeType.Io1) {
      final int iopsPerGb =
          volumeType == VolumeType.Io2 ? IOPS_PER_GB_FOR_IO2 : IOPS_PER_GB_FOR_IO1;
      final int diskIopsForDiskSize =
          Math.min(newDiskSize * iopsPerGb, MAX_DISK_IOPS_FOR_OPTIMIZED_AWS_DA_RESTORE);

      // if the instance volume type is the same as the desired volume type, we take the max of the
      // instance IOPS and the disk IOPS for the disk size. Otherwise, we just use the disk IOPS for
      // the disk size.
      return (instanceVolumeType.equalsIgnoreCase(volumeType.name()))
          ? Math.max(instanceIOPS, diskIopsForDiskSize)
          : diskIopsForDiskSize;
    } else {
      // for gp3
      return getIopsForAwsDirectAttach(instanceHardware, newDiskSize);
    }
  }

  public static Integer getThroughputForAwsDirectAttach(
      final AWSInstanceHardware awsInstanceHardware, final Integer newDiskSize) {
    final AWSNDSInstanceSize instanceSize =
        AWSNDSInstanceSize.valueOf(awsInstanceHardware.getInstanceSize().get());
    return instanceSize.getMinThroughput(newDiskSize);
  }

  public static EncryptionProviderType getEncryptionProviderTypeOfCluster(
      final ClusterDescription cd, final AutomationConfig automationConfig) {
    final Deployment deployment = automationConfig.getDeployment();

    final List<Process> processes =
        NDSDefaults.filterProcessesByClusterDescription(deployment.getProcesses(), cd);
    final Optional<Process> mongodProcess =
        processes.stream().filter(p -> p.getProcessType().equals(ProcessType.MONGOD)).findFirst();

    return mongodProcess.map(Process::getEncryptionProviderType).orElse(null);
  }

  protected static InstanceHardware getInstanceHardware(
      final BackupDependenciesProvider backupDependenciesProvider,
      final ObjectId groupId,
      final String clusterName,
      final ObjectId instanceId) {
    final ReplicaSetHardware replicaSetHardware =
        backupDependenciesProvider
            .getReplicaSetHardwareDao()
            .findReplicaSetHardwareForInstance(groupId, clusterName, instanceId);
    return replicaSetHardware
        .getById(instanceId)
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    String.format(
                        "No Instance Hardware found by id (%s) for replica set hardware in cluster"
                            + " (%s)",
                        instanceId, replicaSetHardware.getClusterName())));
  }

  /**
   * This loops through all the possible instances to snapshot and chooses the first that's healthy.
   * possibleInstanceIdsToSnapshot is already sorted by preference at this point.
   */
  public static <T extends NDSMove & CreateSnapshotMove>
      Pair<Result<Result.NoData>, Optional<ObjectId>> pickInstanceIdToSnapshot(
          final BackupDependenciesProvider backupDependenciesProvider,
          final NDSPlanContext ndsPlanContext,
          final String clusterName,
          final String priorDiskName,
          final List<ObjectId> possibleInstanceIdsToSnapshot,
          final T snapshotMove,
          final BackupSnapshot snapshot,
          final Function<Integer, Integer> waitForProcessHealthyStepGetter) {
    final AutomationConfig automationConfig =
        backupDependenciesProvider
            .getAutomationConfigSvc()
            .findPublished(ndsPlanContext.getGroupId());

    final ClusterDescription clusterDescription =
        backupDependenciesProvider
            .getNDSClusterSvc()
            .getActiveClusterDescription(ndsPlanContext.getGroupId(), clusterName)
            .get();

    final Duration healthyTimeout;
    if (snapshot.getPlanningType() == PlanningType.CONCURRENT) {
      healthyTimeout = CpsReplSetSnapshotMove.AttemptLimits.REPLICATION_LAG_HEALTHY_CONCURRENT;
    } else {
      healthyTimeout =
          clusterDescription.isNVMe(NodeType.ELECTABLE)
              ? CpsReplSetSnapshotMove.AttemptLimits.REPLICATION_LAG_HEALTHY
              : CpsReplSetSnapshotMove.AttemptLimits.NON_NVME_REPLICATION_LAG_HEALTHY;
    }

    // Given the possible instance ids to snapshot, we check the health of each.
    // In the case that the first instance was not healthy, we don't fail the move - instead, we
    // continue to see if other possible instances are healthy.
    for (final ObjectId id : possibleInstanceIdsToSnapshot) {
      final InstanceHardware instanceHardware =
          getInstanceHardware(
              backupDependenciesProvider, ndsPlanContext.getGroupId(), clusterName, id);

      final String snapshotTargetHostname = instanceHardware.getHostnameForAgents().get();

      final List<Process> snapshotTargetProcesses =
          automationConfig.getDeployment().getProcessesByHostname(snapshotTargetHostname);

      // we don't use runIfReady here to avoid an exception being thrown when step fails
      final Result<?> waitForCMProcessesHealthyStepResult =
          snapshotMove
              .getWaitForProcessHealthyStep(
                  snapshotMove.getContext(),
                  snapshotMove
                      .getState()
                      .forStep(
                          waitForProcessHealthyStepGetter.apply(instanceHardware.getMemberIndex())),
                  ndsPlanContext.getGroupId(),
                  snapshotTargetProcesses,
                  true,
                  healthyTimeout,
                  CpsSvc.MAX_SNAPSHOT_REPLICATION_LAG,
                  backupDependenciesProvider)
              .perform();

      if (waitForCMProcessesHealthyStepResult.getStatus().isInProgress()) {
        return Pair.of(Result.inProgress(), Optional.empty());
      }
      if (waitForCMProcessesHealthyStepResult.getStatus().isDone()) {
        CPS_SNAPSHOT_INSTANCE_SELECTIONS
            .labels(
                String.valueOf(snapshot.haveAttemptedOnInstance(id)),
                String.valueOf(getDiskName(instanceHardware).orElse("none").equals(priorDiskName)),
                CpsMetricsUtil.getCloudProviderLabel(clusterDescription.getCloudProviders()))
            .inc();
        return Pair.of(Result.done(), Optional.of(id));
      }

      CPS_SNAPSHOT_INSTANCE_UNHEALTHY_SKIPS
          .labels(CpsMetricsUtil.getCloudProviderLabel(clusterDescription.getCloudProviders()))
          .inc();
    }

    ndsPlanContext
        .getLogger()
        .atError()
        .setMessage("No eligible hardware to take a snapshot from")
        .addKeyValue("Cluster name", clusterName)
        .log();
    return Pair.of(Result.failed(), Optional.empty());
  }

  /** Return a list of hardware instance ids sorted by order of preference. */
  public List<ObjectId> findAndSortSnapshottableInstanceIds(
      final Logger logger,
      final String clusterName,
      final ReplicaSetHardware replicaSetHardware,
      final ObjectId groupId,
      final BackupDependenciesProvider backupDependenciesProvider,
      final String priorDiskName,
      final Set<ObjectId> lastFailedInstanceIds,
      final BackupJob backupJob) {
    final Optional<Cluster> cluster =
        backupDependenciesProvider.getNDSClusterSvc().getActiveCluster(groupId, clusterName);

    if (cluster.isEmpty()) {
      logger.info("Failed getting active cluster to snapshot from {}", clusterName);
      return new ArrayList<>();
    }

    final ClusterDescription clusterDescription = cluster.get().getClusterDescription();
    final BackupReplicaSetType replicaSetType = getBackupReplicaSetType(replicaSetHardware);

    if (isNvme(replicaSetType, clusterDescription)) {
      final List<ObjectId> instanceIds =
          findInstanceIdOfHiddenNode(
              clusterName, replicaSetHardware.getRsId(), groupId, backupDependenciesProvider);
      if (instanceIds.isEmpty()) {
        logger.warn(
            "Failed getting NVMe hidden node instance ID from groupId: {}, clusterName: {}",
            groupId,
            clusterName);
      }
      return instanceIds;
    }

    return getInstanceIdsInOrderOfPreference(
        cluster.get(),
        replicaSetHardware,
        backupDependenciesProvider,
        priorDiskName,
        lastFailedInstanceIds,
        logger,
        backupJob);
  }

  public static Map<BackupFrequencyType, Integer> getFrequencyTypeToExtraRetention(
      final BackupJobDao backupJobDao, final ObjectId groupId, final ObjectId clusterUniqueId) {
    final Optional<BackupJob> backupJob =
        backupJobDao.findByClusterUniqueId(groupId, clusterUniqueId);
    if (backupJob.isEmpty() || backupJob.get().getExtraRetentionSettings() == null) {
      return Collections.emptyMap();
    }
    return backupJob.get().getExtraRetentionSettings().stream()
        .collect(
            Collectors.toMap(
                ExtraRetentionSetting::getFrequencyType, ExtraRetentionSetting::getRetentionDays));
  }

  protected static List<ObjectId> findInstanceIdOfHiddenNode(
      final String clusterName,
      final String rsId,
      final ObjectId groupId,
      final BackupDependenciesProvider backupDependenciesProvider) {
    final Optional<ObjectId> instanceId =
        BackupSnapshotUtils.findInstanceIdOfHiddenNode(
            backupDependenciesProvider, groupId, clusterName, rsId);
    if (instanceId.isEmpty()) {
      return new ArrayList<>();
    }
    return Lists.newArrayList(instanceId.get());
  }

  /**
   * Returns the instanceIds in the replica set ordered by our preference for choosing a node to
   * snapshot: 1. volume ids set as preferred (manual operation action) 2. region priority
   * descending 3. avoiding primary 4. priority ascending 5. snapshots keeping incrementality 6.
   * hostname ascending
   */
  protected List<ObjectId> getInstanceIdsInOrderOfPreference(
      final Cluster cluster,
      final ReplicaSetHardware replicaSetHardware,
      final BackupDependenciesProvider backupDependenciesProvider,
      final String priorDiskName,
      final Set<ObjectId> lastFailedInstanceIds,
      final Logger logger,
      final BackupJob backupJob) {
    final ClusterDescription clusterDescription = cluster.getClusterDescription();
    final ObjectId groupId = clusterDescription.getGroupId();
    final String rsId = replicaSetHardware.getRsId();

    final int prevSnapshottedNodeIndex = getPreviousNodeIndex(replicaSetHardware, priorDiskName);

    final int primaryIndex =
        findPrimaryIndex(backupDependenciesProvider, logger, groupId, cluster, replicaSetHardware);

    final AutomationConfig automationConfig =
        backupDependenciesProvider.getAutomationConfigSvc().findPublished(groupId);

    final Optional<ReplicaSet> replicaSetOpt =
        automationConfig.getDeployment().getReplicaSetByName(rsId);

    if (replicaSetOpt.isEmpty()) {
      logger.info("Failed getting active replicaSet to snapshot from {}", rsId);
      return new ArrayList<>();
    }
    final ReplicaSet replicaSet = replicaSetOpt.get();

    final Optional<ReplicationSpec> replicationSpecOpt =
        clusterDescription.getReplicationSpecById(replicaSetHardware.getReplicationSpecId());

    final Comparator<ReplicaSetMember> comparator =
        (final ReplicaSetMember member1, final ReplicaSetMember member2) -> {

          // Prefer members whose volume is preferred.
          final boolean member1HasPreferredVolume =
              hasPreferredVolume(
                  replicaSetHardware, member1.getId(), backupJob.getPreferredSnapshotVolumes());
          final boolean member2HasPreferredVolume =
              hasPreferredVolume(
                  replicaSetHardware, member2.getId(), backupJob.getPreferredSnapshotVolumes());
          final int volumeCompare =
              Boolean.compare(member2HasPreferredVolume, member1HasPreferredVolume);
          if (volumeCompare != 0) {
            return volumeCompare;
          }

          // region priority descending
          if (replicationSpecOpt.isPresent()) {
            final int regionPriority1 =
                replicationSpecOpt.get().getRegionPriority(member1).orElse(0);
            final int regionPriority2 =
                replicationSpecOpt.get().getRegionPriority(member2).orElse(0);
            final int regionCompare = Integer.compare(regionPriority2, regionPriority1);
            if (regionCompare != 0) {
              return regionCompare;
            }
          }

          // Snapshots not from a primary
          final boolean member1IsPrimary = isMemberPrimary(member1, primaryIndex);
          final boolean member2IsPrimary = isMemberPrimary(member2, primaryIndex);
          final int primaryCompare = Boolean.compare(member1IsPrimary, member2IsPrimary);
          if (primaryCompare != 0) {
            return primaryCompare;
          }

          // Then priority ascending
          final float priority1 = member1.getPriority();
          final float priority2 = member2.getPriority();
          int priorityCompare = Float.compare(priority1, priority2);
          if (priorityCompare != 0) {
            return priorityCompare;
          }

          // Snapshots keeping incrementality from one snapshot to the next
          final boolean lastSnapshotTakenOnMember1Disk =
              lastSnapshotTakenOnMember(member1, prevSnapshottedNodeIndex);
          final boolean lastSnapshotTakenOnMember2Disk =
              lastSnapshotTakenOnMember(member2, prevSnapshottedNodeIndex);
          final int incrementalityCompare =
              Boolean.compare(lastSnapshotTakenOnMember2Disk, lastSnapshotTakenOnMember1Disk);
          if (incrementalityCompare != 0) {
            return incrementalityCompare;
          }

          // Then host lexicographically
          final String host1 = member1.getHost();
          final String host2 = member2.getHost();
          return StringUtils.compare(host1, host2);
        };

    final List<ObjectId> instanceIds =
        replicaSet.getMembers().stream()
            .sorted(comparator)
            .map(
                member -> {
                  final Optional<Process> process =
                      automationConfig.getDeployment().getProcessByName(member.getHost());
                  return process.flatMap(
                      proc -> {
                        final Optional<InstanceHardware> hardwareOpt =
                            replicaSetHardware.getByHostname(proc.getHostname());
                        return hardwareOpt.map(InstanceHardware::getInstanceId);
                      });
                })
            .filter(Optional::isPresent)
            .map(Optional::get)
            .filter(id -> lastFailedInstanceIds == null || !lastFailedInstanceIds.contains(id))
            .collect(Collectors.toList());

    // Add lastFailedInstanceIds to the end of the list.
    if (lastFailedInstanceIds != null) {
      instanceIds.addAll(lastFailedInstanceIds);
    }

    return instanceIds;
  }

  protected boolean hasPreferredVolume(
      final ReplicaSetHardware replicaSetHardware,
      final int memberId,
      final Set<String> preferredVolumes) {
    final Optional<InstanceHardware> instanceHardware =
        replicaSetHardware.getCloudProviderHardwareByMemberIndex(memberId);
    if (instanceHardware.isEmpty() || preferredVolumes == null) return false;
    return getDiskName(instanceHardware.get()).map(preferredVolumes::contains).orElse(false);
  }

  public static Map<ObjectId, String> getShardedRsIdMap(
      final ClusterDescription targetClusterDescription,
      final List<ShardedClusterBackupSnapshot.SnapshotMember> snapshotMembers,
      final ReplicaSetHardwareDao replicaSetHardwareDao) {
    final Map<ObjectId, String> map = new HashMap<>();
    final List<ReplicaSetHardware> replicaSetHardwareList =
        replicaSetHardwareDao.findByCluster(
            targetClusterDescription.getGroupId(), targetClusterDescription.getName());

    final List<String> targetRsIdOrdering =
        replicaSetHardwareList.stream().map(ReplicaSetHardware::getRsId).sorted().toList();

    final List<SnapshotMember> sortedMemberList =
        snapshotMembers.stream().sorted(Comparator.comparing(SnapshotMember::getRsId)).toList();

    for (int i = 0; i < targetRsIdOrdering.size(); i++) {
      map.put(sortedMemberList.get(i).getSnapshotId(), targetRsIdOrdering.get(i));
    }

    return map;
  }

  int findPrimaryIndex(
      final BackupDependenciesProvider backupDependenciesProvider,
      final Logger logger,
      final ObjectId groupId,
      final Cluster cluster,
      final ReplicaSetHardware rh) {
    final String rsId = rh.getRsId();
    final Optional<ObjectId> instanceIdOfPrimary =
        findInstanceIdOfPrimary(backupDependenciesProvider, logger, groupId, cluster, rsId);

    if (instanceIdOfPrimary.isEmpty()) {
      return -1;
    }

    final Optional<InstanceHardware> ihOpt =
        getInstanceHardwareFromReplicasetHardwarte(rh, instanceIdOfPrimary.get());

    if (ihOpt.isEmpty()) {
      return -1;
    }

    final InstanceHardware ih = ihOpt.get();
    return ih.getMemberIndex();
  }

  Optional<InstanceHardware> getInstanceHardwareFromReplicasetHardwarte(
      final ReplicaSetHardware rh, final ObjectId idToGet) {
    return rh.getById(idToGet);
  }

  /** Returns the disk name that was snapshot previously. */
  public Optional<String> findPreviousDiskName(
      final ObjectId groupId,
      final ObjectId clusterUniqueId,
      final ReplicaSetHardware rh,
      final BackupDependenciesProvider backupDependenciesProvider) {
    if (groupId == null || clusterUniqueId == null || rh == null) return Optional.empty();

    final String rsId = rh.getRsId();
    final Optional<BackupSnapshot> lastCompletedActiveReplSnapshotOpt =
        backupDependenciesProvider
            .getBackupSnapshotDao()
            .findLastCompletedActiveNonCopyByClusterUniqueIdAndRsId(groupId, clusterUniqueId, rsId);

    if (lastCompletedActiveReplSnapshotOpt.isEmpty()) {
      return Optional.empty();
    }

    final String diskName =
        ((ReplicaSetBackupSnapshot) lastCompletedActiveReplSnapshotOpt.get()).getDiskName();
    return Optional.ofNullable(diskName);
  }

  /** Returns the member index of the node matching the provided diskName. -1 if none found. */
  int getPreviousNodeIndex(final ReplicaSetHardware rh, final String diskName) {
    if (rh == null || diskName == null) return -1;

    for (final InstanceHardware ih : rh.getAllHardware().toList()) {
      final Optional<String> diskNameOpt = getDiskName(ih);
      if (diskNameOpt.isPresent() && diskName.equals(diskNameOpt.get())) {
        return ih.getMemberIndex();
      }
    }
    return -1;
  }

  static Optional<String> getDiskName(final InstanceHardware ih) {
    return switch (ih.getCloudProvider()) {
      case AWS -> ((AWSInstanceHardware) ih).getEBSId();
      case GCP -> ((GCPInstanceHardware) ih).getDiskName();
      case AZURE -> ((AzureInstanceHardware) ih).getDataDiskName();
      default -> Optional.empty();
    };
  }

  boolean lastSnapshotTakenOnMember(
      final ReplicaSetMember member, final int prevSnapshottedNodeIndex) {
    return member.getId() == prevSnapshottedNodeIndex;
  }

  boolean isMemberPrimary(final ReplicaSetMember member, final int primaryIndex) {
    return member.getId() == primaryIndex;
  }

  private static boolean isNvme(
      final BackupReplicaSetType replicaSetType, final ClusterDescription clusterDescription) {
    return replicaSetType != BackupReplicaSetType.CONFIG
        && clusterDescription.isNVMe(NodeType.ELECTABLE);
  }

  // NOTE: also returns IN_PROGRESS if input results is empty
  public static Result<Result.NoData> checkAllResultsDone(final Collection<Result<?>> results) {
    final Set<Result.Status> resultStatuses =
        results.stream().map(Result::getStatus).collect(Collectors.toSet());
    return CpsWtcMoveUtils.checkAllResultsDone(resultStatuses);
  }

  public static List<BackupRestoreShardIdRestoreMap> getCPSShardIdRestoreMaps(
      final List<? extends ReplicaSetBackupRestoreJob> restoreJobList) {
    return restoreJobList.stream()
        .filter(ReplicaSetBackupRestoreJob::snapshotContainsShardData)
        .map(
            rj ->
                rj.getBackupReplicaSetType() == BackupReplicaSetType.CONFIG_SHARD
                    ? new BackupRestoreShardIdRestoreMap("config", "config")
                    : new BackupRestoreShardIdRestoreMap(rj.getRsId(), rj.getTargetRsId()))
        .collect(Collectors.toList());
  }

  public static boolean checkIfRegionsDiffer(
      final List<ProviderStorage> providerStorages,
      final PitSetting pitSetting,
      final List<PitStorage> mergedCopyStorages) {
    final Set<String> regionsFromAgent = new HashSet<>();

    for (ProviderStorage p : providerStorages) {
      regionsFromAgent.add(p.getRegion());
    }

    final Set<String> regionsInPitSettings = new HashSet<>();

    if (pitSetting != null) {
      for (PitStorage p : pitSetting.getNonCopyPitStorages()) {
        regionsInPitSettings.add(p.getBlobStoreRegionName());
      }
    }

    if (mergedCopyStorages != null && !mergedCopyStorages.isEmpty()) {
      for (PitStorage p : mergedCopyStorages) {
        regionsInPitSettings.add(p.getBlobStoreRegionName());
      }
    }

    return !regionsFromAgent.equals(regionsInPitSettings);
  }

  /**
   * Determines if billing should be skipped for oplog migration. If feature flags are configured to
   * skip billing and a migration is occurring, then skip billing.
   *
   * @param storageRegionName region name where oplogs are stored
   * @param pitSetting pitSettings containing oplog migration metadata
   * @param appSettings App configuration settings for default feature flag values
   * @param cpsPitSvc Service for getting feature flag state
   * @param groupId ID of group
   * @return true if billing should be skipped
   */
  public static boolean shouldSkipBilling(
      final String storageRegionName,
      final PitSetting pitSetting,
      final AppSettings appSettings,
      final CpsPitSvc cpsPitSvc,
      final ObjectId groupId) {
    if (pitSetting.getCopyStoragesMigration().stream()
        .anyMatch(
            copyStorageMigration ->
                storageRegionName.equals(copyStorageMigration.getBlobStoreRegionName()))) {
      return true;
    }
    if (pitSetting.getMigrationStorage() == null
        || !pitSetting.getMigrationStorage().getBlobStoreRegionName().equals(storageRegionName)) {
      return false;
    }
    if (appSettings.getBoolProp("mms.atlas.backup.skipBillingOnOplogMigrationDestination", true)) {
      return true;
    }
    final RegionName primaryOplogRegionName =
        RegionNameHelper.findByValue(pitSetting.getRegionName());
    final RegionName oplogMigrationRegionName =
        RegionNameHelper.findByValue(pitSetting.getMigrationStorage().getBlobStoreRegionName());

    CloudProvider primaryOplogCloudProvider = primaryOplogRegionName.getProvider();
    CloudProvider migrationOplogCloudProvider = oplogMigrationRegionName.getProvider();
    // skip billing when migrating from AWS to GCP
    if (primaryOplogCloudProvider == CloudProvider.AWS
        && migrationOplogCloudProvider == CloudProvider.GCP
        && cpsPitSvc.isOplogInGcpEnabled(groupId)
        && appSettings.getBoolProp(
            "mms.atlas.backup.skipBillingOplogMigrationForGcpOplogStore", false)) {
      return true;
    }
    // skip billing when migrating from AWS to AZURE
    return primaryOplogCloudProvider == CloudProvider.AWS
        && migrationOplogCloudProvider == CloudProvider.AZURE
        && cpsPitSvc.isOplogInAzureEnabled(groupId)
        && appSettings.getBoolProp(
            "mms.atlas.backup.skipBillingOplogMigrationForAzureOplogStore", false);
  }

  // WARNING!!!
  // This function should be used for the purpose of snapshot distribution only
  // In reality, there is no replication spec id for config servers.
  // In snapshot distribution, the copy settings will be identical for the config server and the
  // shard(s) in the first zone
  // This translates to the oth Replication Spec in the Replication Spec List.
  public static ObjectId getZoneIdForCopy(
      final ClusterDescription cd,
      final List<ReplicaSetHardware> replicaSetHardwareList,
      final String rsId) {
    return getRsIdToReplicationSpecMapping(cd, replicaSetHardwareList).get(rsId).getZoneId();
  }

  // WARNING!!!
  // This function should be used for the purpose of snapshot distribution only
  // In reality, there is no replication spec id for config servers.
  // In snapshot distribution, the copy settings will be identical for the config server and the
  // shard(s) in the first zone
  // This translates to the 0th Replication Spec in the Replication Spec List.
  // @TODO: replace with getRsIdToReplicationSpecMappingV2 after asymmetric sharding
  public static Map<String, ReplicationSpec> getRsIdToReplicationSpecMapping(
      final ClusterDescription cd, final List<ReplicaSetHardware> replicaSetHardwareList) {
    final Map<String, ReplicationSpec> map = new HashMap<>();
    for (final ReplicaSetHardware rh : replicaSetHardwareList) {
      final String rsId = rh.getRsId();
      if (rh.isDedicatedConfig()) {
        // map to a shard replicationSpec because the config copy settings will be identical
        map.put(rsId, cd.getReplicationSpecsWithShardData().get(0));
      } else if (cd.getReplicationSpecById(rh.getReplicationSpecId()).isPresent()) {
        map.put(rsId, cd.getReplicationSpecById(rh.getReplicationSpecId()).get());
      }
    }
    return map;
  }

  public static void deleteFileLists(
      final CpsBackupCursorFileListsDao fileListDao, final ReplicaSetBackupSnapshot rsSnapshot) {
    if (!rsSnapshot.isCopy()) {
      // always perform the delete even if might not be a WT Snapshot.
      fileListDao.removeBySnapshotId(rsSnapshot.getId());
    }
  }

  public static Optional<Host> getMongodHost(
      final ObjectId groupId,
      final InstanceHardware instanceHardware,
      final AutomationConfig automationConfig,
      final BackupDependenciesProvider backupDependenciesProvider,
      final Logger planLogger) {
    Optional<Host> mongodHost = Optional.empty();
    final Optional<String> hostName = instanceHardware.getHostnameForAgents();
    if (hostName.isEmpty()) {
      planLogger.error("Empty instance hardware hostname when getting MongodHost");
      return mongodHost;
    }

    final Process targetProcess =
        automationConfig.getDeployment().getProcessesByHostname(hostName.get()).stream()
            .filter(Process::isMongod)
            .findFirst()
            .get();
    mongodHost =
        BackupSnapshotUtils.findHost(
            backupDependenciesProvider,
            groupId,
            targetProcess.getHostname(),
            targetProcess.getPort());

    if (mongodHost.isEmpty()) {
      planLogger.error("Cannot get MongodHost");
    }

    return mongodHost;
  }

  /**
   * Return the set of instance ids of VMs to avoid snapshotting on. We do this to avoid VMs we
   * assume to be unhealthy. Because we filter on rsId and expect only one entry per replica set,
   * this method will return 0 or 1 instanceIds.
   */
  public static Set<ObjectId> getFailedInstancesToAvoidRetryingOn(
      final BackupSnapshot snapshot, final String rsId) {
    final List<Attempt> attempts = snapshot.getAttempts();
    if (attempts == null || attempts.isEmpty()) {
      return Set.of();
    }

    final Attempt lastAttempt = attempts.get(attempts.size() - 1);
    return lastAttempt.getInstances().stream()
        .filter(i -> rsId.equals(i.getRsId()))
        .filter(SnapshotAttemptedInstance::getAvoidRetryingOnThisInstance)
        .map(SnapshotAttemptedInstance::getInstanceId)
        // the previous attempt might have failed before picking instance
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }

  public static Set<ObjectId> getFailedInstancesToAvoidRetryingOn(
      final ReplicaSetBackupSnapshot snapshot) {
    return getFailedInstancesToAvoidRetryingOn(snapshot, snapshot.getRsId());
  }

  public static double calculateSpeed(final Long pBytes, final Instant pStart, final Instant pEnd) {
    final Duration duration = Duration.between(pStart, pEnd);
    final double megabytes = Units.BYTES.convertTo(pBytes, Units.MEGABYTES);
    final double mbPerSec = megabytes / duration.getSeconds();
    return mbPerSec;
  }
}
