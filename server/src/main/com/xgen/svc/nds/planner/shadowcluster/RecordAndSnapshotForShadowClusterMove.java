package com.xgen.svc.nds.planner.shadowcluster;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.mongodb.WriteResult;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._private.dao.ShadowClusterJobDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ShadowClusterJob;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure.Status;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowClusterExposureJob;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterJobSvc;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.nds.planner.MoveProvider;
import com.xgen.svc.nds.planner.NDSMove;
import com.xgen.svc.nds.planner.movefactory.RecordAndSnapshotForShadowClusterMoveFactory;
import com.xgen.svc.nds.svc.SystemClusterJobSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;

/**
 * Represents a move to create a snapshot for a shadow cluster exposure.
 *
 * <p>This move is responsible for creating and managing snapshots at the start of a shadow cluster
 * exposure. It interacts with {@link SystemClusterJobSvc} to manage the state of the {@link
 * ShadowClusterJob} and with {@link ShadowClusterSvc} to update the status of the underlying {@link
 * Exposure} and its associated snapshot details.
 *
 * <p>The {@code performInternal} method handles the core logic:
 *
 * <ul>
 *   <li>Retrieves the {@link ShadowClusterJob}.
 *   <li>Validates the current job status against the exposure status in the DAO.
 *   <li>Checks if a snapshot already exists (either on the job or in the exposure details). If so,
 *       it updates the status to {@link Exposure.Status#SNAPSHOT_READY} and completes.
 *   <li>If no snapshot exists, it updates the status to {@link Exposure.Status#SNAPSHOTTING}.
 *   <li>Calls {@code waitForSnapshotStep} to manage the snapshot creation workflow.
 *   <li>Upon successful snapshot creation, it updates the status to {@link
 *       Exposure.Status#SNAPSHOT_READY}, saves the new snapshot ID to the {@link ShadowClusterJob},
 *       and updates the exposure with the new snapshot ID.
 *   <li>If snapshot creation fails, it updates the status to {@link Exposure.Status#FAILED}.
 * </ul>
 *
 * <p>The factory for this move is {@link RecordAndSnapshotForShadowClusterMoveFactory}.
 *
 * @see ShadowClusterJob
 * @see ShadowClusterSvc
 * @see SystemClusterJobSvc
 * @see Exposure
 * @see CpsSvc
 * @see RecordAndSnapshotForShadowClusterMoveFactory
 */
public class RecordAndSnapshotForShadowClusterMove extends NDSMove {
  private final ObjectId shadowClusterExposureJobId;

  private final ShadowClusterJobSvc shadowClusterJobSvc;
  private final ShadowClusterSvc shadowClusterSvc;
  private final CpsSvc cpsSvc;
  private final ShadowClusterJobDao shadowClusterJobDao;

  @AssistedInject
  public RecordAndSnapshotForShadowClusterMove(
      @Assisted("planContext") final PlanContext context,
      @Assisted("shadowClusterExposureJobId") final ObjectId shadowClusterExposureJobId,
      final ShadowClusterJobSvc shadowClusterJobSvc,
      final ShadowClusterSvc shadowClusterSvc,
      final CpsSvc cpsSvc,
      final ShadowClusterJobDao pShadowClusterJobDao) {
    super(context);
    this.shadowClusterExposureJobId = shadowClusterExposureJobId;
    this.shadowClusterJobSvc = shadowClusterJobSvc;
    this.shadowClusterSvc = shadowClusterSvc;
    this.cpsSvc = cpsSvc;
    this.shadowClusterJobDao = pShadowClusterJobDao;
  }

  @AssistedInject
  public RecordAndSnapshotForShadowClusterMove(
      @Assisted("pId") final ObjectId pId,
      @Assisted("predecessors") final Set<ObjectId> predecessors,
      @Assisted("successors") final Set<ObjectId> successors,
      @Assisted("planContext") final PlanContext context,
      @Assisted("state") final State state,
      @Assisted("shadowClusterExposureJobId") final ObjectId shadowClusterExposureJobId,
      final ShadowClusterJobSvc shadowClusterJobSvc,
      final ShadowClusterSvc shadowClusterSvc,
      final CpsSvc cpsSvc,
      final ShadowClusterJobDao pShadowClusterJobDao) {
    super(pId, predecessors, successors, context, state);
    this.shadowClusterExposureJobId = shadowClusterExposureJobId;
    this.shadowClusterJobSvc = shadowClusterJobSvc;
    this.shadowClusterSvc = shadowClusterSvc;
    this.cpsSvc = cpsSvc;
    this.shadowClusterJobDao = pShadowClusterJobDao;
  }

  public static RecordAndSnapshotForShadowClusterMove factoryCreate(
      final PlanContext planContext, final ObjectId shadowClusterExposureJobId) {
    return MoveProvider.createMoveFromFactory(
        RecordAndSnapshotForShadowClusterMove.class,
        List.of(planContext, shadowClusterExposureJobId));
  }

  @Override
  protected Result<?> performInternal() {
    getLogger()
        .atInfo()
        .setMessage("Starting RecordAndSnapshotForShadowClusterMove")
        .addKeyValue("shadowClusterExposureJobId", shadowClusterExposureJobId)
        .log();

    final ShadowClusterExposureJob shadowClusterExposureJob;
    try {
      shadowClusterExposureJob =
          shadowClusterJobSvc.getShadowClusterExposureJob(shadowClusterExposureJobId, getLogger());
    } catch (final SvcException e) {
      return Result.failed(
          "Failed getting shadow cluster exposure job for jobId: " + shadowClusterExposureJobId);
    }

    if (shadowClusterExposureJob.getJobStatus().equals(Exposure.Status.FAILED)) {
      return Result.failed();
    }

    if (shadowClusterExposureJob.targetDeletionDateReached()) {
      shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
          shadowClusterExposureJob, Status.FAILED, "Exposure deleted while provisioning");
      return Result.failed(
          "Target deletion date "
              + shadowClusterExposureJob.getTargetDeletionDate()
              + "has been reached for jobId: "
              + shadowClusterExposureJobId
              + ". Triggering rollback.");
    }

    if (shadowClusterExposureJob.getSourceSnapshotId() != null) {
      getLogger()
          .atInfo()
          .setMessage("Snapshot already exists, proceeding to job scheduling")
          .addKeyValue("shadowClusterExposureJobId", shadowClusterExposureJobId)
          .addKeyValue("sourceSnapshotId", shadowClusterExposureJob.getSourceSnapshotId())
          .log();

      runAtLeastOnce(
          () ->
              shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
                  shadowClusterExposureJob,
                  Exposure.Status.SNAPSHOT_READY,
                  "Snapshot already created with id: "
                      + shadowClusterExposureJob.getSourceSnapshotId()),
          StateFields.SNAPSHOT_READY_STATUS_SET);
      return tryScheduleShadowClusterJobs(shadowClusterExposureJob);
    }

    runAtLeastOnce(
        () ->
            shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
                shadowClusterExposureJob, Exposure.Status.SNAPSHOTTING, "Creating snapshot"),
        StateFields.SNAPSHOTTING_STATUS_SET);

    final Result<NoData> snapshotResult;
    try {
      snapshotResult = waitForSnapshotStep(shadowClusterExposureJob);
    } catch (final SvcException e) {
      getLogger()
          .atError()
          .setMessage("Failed to create snapshot")
          .addKeyValue("shadowClusterExposureJobId", shadowClusterExposureJobId)
          .setCause(e)
          .log();
      return Result.failed("Failed to create snapshot: " + e.getMessage());
    }

    if (snapshotResult.getStatus().isInProgress()) {
      return snapshotResult;
    }

    if (snapshotResult.getStatus().isFailed()) {
      shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
          shadowClusterExposureJob,
          Exposure.Status.FAILED,
          "Failed to create snapshot: " + snapshotResult.getMessage());
      return snapshotResult;
    }

    if (!snapshotResult.getStatus().isDone()) {
      return snapshotResult;
    }

    runAtLeastOnce(
        () ->
            shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
                shadowClusterExposureJob,
                Status.SNAPSHOT_READY,
                "Snapshot created successfully with id: "
                    + shadowClusterExposureJob.getSourceSnapshotId()),
        StateFields.SNAPSHOT_READY_STATUS_SET);

    getLogger()
        .atInfo()
        .setMessage("Shadow cluster exposure snapshot creation completed successfully")
        .addKeyValue("shadowClusterExposureJobId", shadowClusterExposureJobId)
        .addKeyValue("sourceSnapshotId", shadowClusterExposureJob.getSourceSnapshotId())
        .log();

    return tryScheduleShadowClusterJobs(shadowClusterExposureJob);
  }

  private Result<NoData> tryScheduleShadowClusterJobs(
      final ShadowClusterExposureJob shadowClusterExposureJob) {
    try {
      scheduleShadowClusterJobs(shadowClusterExposureJob);
    } catch (final SvcException e) {
      shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
          shadowClusterExposureJob,
          Exposure.Status.FAILED,
          "Failed to schedule shadow cluster jobs: " + e.getMessage());

      return Result.failed("Failed to schedule shadow cluster jobs: " + e.getMessage());
    }
    return Result.done();
  }

  @Override
  protected Result<?> rollbackInternal() {
    getLogger()
        .atWarn()
        .setMessage("Starting shadow cluster exposure snapshot rollback")
        .addKeyValue("shadowClusterExposureJobId", shadowClusterExposureJobId)
        .log();

    final ShadowClusterExposureJob shadowClusterExposureJob;
    try {
      shadowClusterExposureJob =
          shadowClusterJobSvc.getShadowClusterExposureJob(shadowClusterExposureJobId, getLogger());
    } catch (final SvcException e) {
      return Result.failed(
          "Failed getting shadow cluster exposure job during rollback for jobId: "
              + shadowClusterExposureJobId);
    }

    // In other places in the codebase, we don't do rollbacks for a snapshot provision.
    // Simply mark the exposure as failed and return.
    shadowClusterJobSvc.updateShadowClusterExposureJobStatus(
        shadowClusterExposureJob, Exposure.Status.FAILED, "Snapshot creation failed");

    return Result.done();
  }

  protected Result<NoData> waitForSnapshotStep(
      final ShadowClusterExposureJob shadowClusterExposureJob) throws SvcException {

    final ObjectId snapshotId = shadowClusterExposureJob.getSourceSnapshotId();
    if (snapshotId == null) {
      queueSnapshot(shadowClusterExposureJob);
      return Result.inProgress();
    }

    final BackupSnapshot snapshot =
        cpsSvc.getBackupSnapshot(shadowClusterExposureJob.getSourceProjectId(), snapshotId);

    if (snapshot == null) {
      return Result.failed("Snapshot not found");
    }

    switch (snapshot.getSnapshotStatus()) {
      case QUEUED, IN_PROGRESS -> {
        return Result.inProgress();
      }
      case FAILED -> {
        final String errMsg = logFailedSnapshotInstances(snapshot);
        return Result.failed("Snapshot failed: " + errMsg);
      }
      case COMPLETED -> {
        return Result.done();
      }
    }

    return Result.failed("Unexpected snapshot status: " + snapshot.getSnapshotStatus());
  }

  protected void scheduleShadowClusterJobs(final ShadowClusterExposureJob shadowClusterExposureJob)
      throws SvcException {
    final Optional<Exposure> maybeExposure =
        shadowClusterSvc.getExposure(shadowClusterExposureJob.getExposureId());

    if (maybeExposure.isEmpty()) {
      throw new SvcException(CommonErrorCode.NOT_FOUND, shadowClusterExposureJob.getExposureId());
    }

    final Exposure exposure = maybeExposure.get();

    final List<ObjectId> jobIds =
        shadowClusterJobSvc.createShadowClusterJobsForExposure(
            exposure, shadowClusterExposureJob.getSourceSnapshotId());

    getLogger()
        .atInfo()
        .setMessage("Successfully scheduled shadow cluster jobs")
        .addKeyValue("shadowClusterExposureJobId", shadowClusterExposureJob.getId())
        .addKeyValue("exposureId", shadowClusterExposureJob.getExposureId())
        .addKeyValue("jobCount", jobIds.size())
        .log();

    /*
     * We'll now verify that the shadow cluster exposure job target deletion date hasn't been
     * reached since that'd mean that concurrently to this process, the customer has deleted the
     * shadow cluster exposure.
     *
     * If that's the case, then we have to mark all the recently scheduled jobs as deleted to
     * avoid having them running in vain.
     */
    final ShadowClusterExposureJob updatedShadowClusterExposureJob =
        shadowClusterJobSvc.getShadowClusterExposureJob(shadowClusterExposureJobId, getLogger());

    if (updatedShadowClusterExposureJob.targetDeletionDateReached()) {
      final WriteResult updateShadowClusterJobResult =
          shadowClusterJobDao.updateTargetDeletionDate(jobIds, new Date());
      if (!updateShadowClusterJobResult.wasAcknowledged()
          || updateShadowClusterJobResult.getN() != jobIds.size()) {
        getLogger()
            .atError()
            .setMessage("Failed to mark shadow cluster jobs as deleted")
            .addKeyValue("shadowClusterExposureJobId", shadowClusterExposureJob.getId())
            .addKeyValue("exposureId", shadowClusterExposureJob.getExposureId())
            .addKeyValue("jobsList", jobIds)
            .log();
        throw new SvcException(
            NDSErrorCode.INTERNAL,
            "Failed to mark jobs as deleted. Check the logs for more details.");
      }
    }
  }

  protected void queueSnapshot(final ShadowClusterExposureJob shadowClusterExposureJob)
      throws SvcException {
    // createQueuedOnDemandSnapshot is the entrypoint for creating an on-demand snapshot.
    // Resources and other moves call into this method to create a snapshot (e.g.
    // SyncBackupSettingsMove, CPSResource, etc.)
    final ObjectId snapshotId =
        cpsSvc.createQueuedOnDemandSnapshot(
            shadowClusterExposureJob.getSourceProjectId(),
            shadowClusterExposureJob.getSourceClusterName(),
            Duration.ofDays(ShadowClusterJobSvc.MAX_RETENTION_DAYS),
            "Snapshot triggered due to shadow cluster exposure creation",
            AppUser.SYSTEM_USER,
            null);

    shadowClusterExposureJob.setSourceSnapshotId(snapshotId);
    shadowClusterJobSvc.saveShadowClusterExposureJob(shadowClusterExposureJob);
  }

  /**
   * Logs details for all failed instances across all attempts in the snapshot. This method extracts
   * failed instances (those with non-null failedAt timestamps), logs their instance IDs and error
   * messages for debugging purposes, and returns the error messages as a string.
   */
  private String logFailedSnapshotInstances(final BackupSnapshot snapshot) {
    final StringBuilder errMsg = new StringBuilder();

    snapshot.getAttempts().stream()
        .flatMap(attempt -> attempt.getInstances().stream())
        .filter(instance -> instance.getFailedAt() != null)
        .forEach(
            instance -> {
              getLogger()
                  .atError()
                  .setMessage("Snapshot failed")
                  .addKeyValue("instanceId", instance.getInstanceId())
                  .addKeyValue("errorMsg", instance.getErrorMsg())
                  .addKeyValue("failedAt", instance.getFailedAt())
                  .log();

              if (!errMsg.isEmpty()) {
                errMsg.append("; ");
              }
              errMsg
                  .append("Instance: ")
                  .append(instance.getInstanceId())
                  .append(" - ")
                  .append(instance.getErrorMsg());
            });

    return errMsg.toString();
  }

  protected static class StateFields {
    // Status tracking fields to prevent repeated status updates
    public static final String SNAPSHOTTING_STATUS_SET = "snapshottingStatusSet";
    public static final String SNAPSHOT_READY_STATUS_SET = "snapshotReadyStatusSet";
  }

  @Override
  protected Object[] getArguments() {
    return new Object[] {shadowClusterExposureJobId};
  }

  @Override
  public CloudProvider getCloudProvider() {
    return CloudProvider.NONE;
  }
}
