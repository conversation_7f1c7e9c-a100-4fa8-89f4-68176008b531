package com.xgen.svc.nds.planner.movefactory;

import com.google.inject.assistedinject.Assisted;
import com.xgen.module.common.planner.FromDBConstructor;
import com.xgen.module.common.planner.PlanningConstructor;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.svc.nds.security.planner.SLSIssueCertMove;
import java.util.Set;
import org.bson.types.ObjectId;

public interface SLSIssueCertMoveFactory {

  @FromDBConstructor
  SLSIssueCertMove create(
      @Assisted("pId") final ObjectId pId,
      @Assisted("predecessors") final Set<ObjectId> pPredecessors,
      @Assisted("successors") final Set<ObjectId> pSuccessors,
      @Assisted("planContext") final PlanContext pPlanContext,
      @Assisted("state") final Move.State pState,
      @Assisted("requestId") final ObjectId pRequestId);

  @PlanningConstructor
  SLSIssueCertMove create(
      @Assisted("planContext") final PlanContext pPlanContext,
      @Assisted("requestId") final ObjectId pRequestId);
}
