package com.xgen.svc.nds.util;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Sets;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoNamespace;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSIndexConfigStats;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSIndexStatusMap;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSynonymMappingDefinition;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndex;
import com.xgen.cloud.nds.project._public.model.BasicDBObjectUtil;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.search.api._public.view.api_2025_09_10.ApiAtlasTextSearchIndexDefinitionView;
import com.xgen.cloud.search.api._public.view.api_2025_09_10.ApiAtlasVectorSearchIndexDefinitionView;
import com.xgen.cloud.search.common._public.svc.SearchClusterConnectionSvc;
import com.xgen.cloud.search.decoupled.autoembedding._public.model.AutoEmbeddingV1;
import com.xgen.cloud.search.decoupled.autoembedding._public.svc.AutoEmbeddingV1Svc;
import com.xgen.cloud.search.decoupled.config._public.model.Migration;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.search.indexmanagement.model.IndexView;
import com.xgen.svc.search.indexmanagement.model.IndexView.FieldDefs;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.bson.json.JsonMode;
import org.bson.json.JsonWriterSettings;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class FTSIndexConfigUtil {

  private final SearchClusterConnectionSvc _searchClusterConnectionSvc;
  private final NDSClusterSvc _ndsClusterSvc;
  private final SearchDeploymentDescriptionSvc _searchDeploymentDescriptionSvc;
  private final FeatureFlagSvc _featureFlagSvc;
  private final AutoEmbeddingV1Svc _autoEmbeddingV1Svc;
  private static final Logger LOG = LoggerFactory.getLogger(FTSIndexConfigUtil.class);
  private static final Set<String> VALID_QUANTIZATION_VALUES = Set.of("none", "scalar", "binary");

  // We will not have an accurate optime for these statuses, so we do not want to reject deletes
  // based on the optime being too old.
  private static final Set<FTSIndexHostStat.StatusCode> STATUS_CODES_WITHOUT_OPTIME_CHECK =
      Set.of(
          FTSIndexHostStat.StatusCode.NOT_STARTED,
          FTSIndexHostStat.StatusCode.INITIAL_SYNC,
          FTSIndexHostStat.StatusCode.FAILED);

  private static final Set<String> ALLOWED_CONFIGURABLE_DYNAMIC_INDEXING_TYPES =
      Set.of(
          "autocomplete",
          "boolean",
          "date",
          "geo",
          "number",
          "objectId",
          "string",
          "token",
          "uuid");

  @Inject
  public FTSIndexConfigUtil(
      final SearchClusterConnectionSvc pSearchClusterConnectionSvc,
      final NDSClusterSvc pNdsClusterSvc,
      final SearchDeploymentDescriptionSvc pSearchDeploymentDescriptionSvc,
      final FeatureFlagSvc pFeatureFlagSvc,
      final AutoEmbeddingV1Svc pAutoEmbeddingV1Svc) {
    _searchClusterConnectionSvc = pSearchClusterConnectionSvc;
    _searchDeploymentDescriptionSvc = pSearchDeploymentDescriptionSvc;
    _ndsClusterSvc = pNdsClusterSvc;
    _featureFlagSvc = pFeatureFlagSvc;
    _autoEmbeddingV1Svc = pAutoEmbeddingV1Svc;
  }

  public static boolean shouldChangeIndexStatusToMigrating(
      final ClusterDescription clusterDescription) {
    if (clusterDescription.isPaused()) {
      return false;
    }
    return true;
  }

  public static boolean shouldChangeIndexStatusToSteady(
      final FTSIndex ftsIndex,
      final Set<String> primaryHostnames,
      final ClusterDescription clusterDescription) {
    if (clusterDescription.isPaused() || ftsIndex.getStats().isEmpty()) {
      return false;
    }
    final FTSIndexStatusMap<? extends FTSIndexHostStat> ftsIndexStatsMap =
        ftsIndex.getStats().get();
    final Optional<String> steadyHostOpt =
        primaryHostnames.stream()
            .filter(
                hostname ->
                    ftsIndexStatsMap.containsKey(hostname)
                        && ftsIndexStatsMap.get(hostname).isSteady())
            .findFirst();

    final boolean statusIsSteady =
        steadyHostOpt.isPresent()
            && primaryHostnames.stream()
                .filter(hostname -> !steadyHostOpt.get().equals(hostname))
                .allMatch(
                    hostname ->
                        ftsIndexStatsMap.containsKey(hostname)
                            && (ftsIndexStatsMap.get(hostname).isSteady()
                                || ftsIndexStatsMap.get(hostname).isDoesNotExist()));

    return !ftsIndex.getStatus().equals(FTSIndex.Status.STEADY) && statusIsSteady;
  }

  public static boolean shouldChangeIndexStatusToSteady(
      FTSIndex ftsIndex,
      Map<String, FTSIndexConfigStats> hostToConfigStats,
      Set<String> mongotHostnames,
      ClusterDescription clusterDescription) {
    if (clusterDescription.isPaused()) {
      return false;
    }
    Optional<String> steadyHostOpt =
        mongotHostnames.stream()
            .filter(
                hostname ->
                    hostToConfigStats.containsKey(hostname)
                        && hostToConfigStats.get(hostname).getStats().isSteady())
            .findFirst();

    boolean statusIsSteady =
        steadyHostOpt.isPresent()
            && mongotHostnames.stream()
                .filter(hostname -> !steadyHostOpt.get().equals(hostname))
                .allMatch(
                    hostname ->
                        hostToConfigStats.containsKey(hostname)
                            && (hostToConfigStats.get(hostname).getStats().isSteady()
                                || hostToConfigStats.get(hostname).getStats().isDoesNotExist()));

    return !ftsIndex.getStatus().equals(FTSIndex.Status.STEADY) && statusIsSteady;
  }

  /**
   * Return the failure message if index should be transitioned to FAILED, otherwise return
   * Optional.empty().
   */
  public static Optional<String> shouldChangeIndexStatusToFailed(
      final FTSIndex ftsIndex,
      final Set<String> primaryHostnames,
      final ClusterDescription clusterDescription) {
    if (clusterDescription.isPaused()
        || ftsIndex.getStats().isEmpty()
        || ftsIndex.getStatus().equals(FTSIndex.Status.FAILED)) {
      return Optional.empty();
    }
    final FTSIndexStatusMap<? extends FTSIndexHostStat> ftsIndexStatsMap =
        ftsIndex.getStats().get();
    Optional<String> errorMessage =
        primaryHostnames.stream()
            .filter(
                hostname ->
                    ftsIndexStatsMap.containsKey(hostname)
                        && ftsIndexStatsMap.get(hostname).isFailed())
            .map(hostname -> ftsIndexStatsMap.get(hostname).getErrorMessage().orElse(""))
            .findFirst();

    return errorMessage;
  }

  /**
   * Return the failure message if index should be transitioned to FAILED, otherwise return
   * Optional.empty().
   */
  public static Optional<String> shouldChangeIndexStatusToFailed(
      FTSIndex ftsIndex,
      Map<String, FTSIndexConfigStats> hostToConfigStats,
      Set<String> mongotHostnames,
      ClusterDescription clusterDescription) {
    if (clusterDescription.isPaused() || ftsIndex.getStatus().equals(FTSIndex.Status.FAILED)) {
      return Optional.empty();
    }

    return mongotHostnames.stream()
        .filter(
            hostname ->
                hostToConfigStats.containsKey(hostname)
                    && hostToConfigStats.get(hostname).getStats().isFailed())
        .map(hostname -> hostToConfigStats.get(hostname).getStats().getErrorMessage().orElse(""))
        .findFirst();
  }

  public static boolean shouldChangeIndexStatusToInProgress(
      final FTSIndex ftsIndex,
      final Set<String> primaryHostnames,
      final ClusterDescription clusterDescription) {
    if (clusterDescription.isPaused() || ftsIndex.getStats().isEmpty()) {
      return false;
    }
    final FTSIndexStatusMap<? extends FTSIndexHostStat> ftsIndexStatsMap =
        ftsIndex.getStats().get();
    final boolean anyPrimaryInProgress =
        primaryHostnames.stream()
            .anyMatch(
                hostname ->
                    !ftsIndexStatsMap.containsKey(hostname)
                        || ftsIndexStatsMap.containsKey(hostname)
                            && ftsIndexStatsMap.get(hostname).isInProgress());

    return !ftsIndex.getStatus().equals(FTSIndex.Status.IN_PROGRESS) && anyPrimaryInProgress;
  }

  public static boolean shouldChangeIndexStatusToInProgress(
      FTSIndex ftsIndex,
      Map<String, FTSIndexConfigStats> hostToConfigStats,
      Set<String> mongotHostnames,
      ClusterDescription clusterDescription) {
    if (clusterDescription.isPaused()) {
      return false;
    }
    boolean anyMongotInProgress =
        mongotHostnames.stream()
            .anyMatch(
                hostname ->
                    !hostToConfigStats.containsKey(hostname)
                        || (hostToConfigStats.containsKey(hostname)
                            && hostToConfigStats.get(hostname).getStats().isInProgress()));
    return !ftsIndex.getStatus().equals(FTSIndex.Status.IN_PROGRESS) && anyMongotInProgress;
  }

  public static boolean shouldChangeIndexStatusToStale(
      FTSIndex ftsIndex, Set<String> primaryHostnames, ClusterDescription clusterDescription) {
    if (clusterDescription.isPaused() || ftsIndex.getStats().isEmpty()) {
      return false;
    }
    final FTSIndexStatusMap<? extends FTSIndexHostStat> ftsIndexStatsMap =
        ftsIndex.getStats().get();
    final boolean anyStaleHosts =
        primaryHostnames.stream()
            .anyMatch(
                hostname ->
                    ftsIndexStatsMap.containsKey(hostname)
                        && ftsIndexStatsMap.get(hostname).isStale());

    final boolean allOtherSteady =
        primaryHostnames.stream()
            .filter(
                hostname ->
                    ftsIndexStatsMap.containsKey(hostname)
                        && !ftsIndexStatsMap.get(hostname).isStale())
            .allMatch(
                hostname ->
                    ftsIndexStatsMap.containsKey(hostname)
                        && ftsIndexStatsMap.get(hostname).isSteady());

    return !ftsIndex.getStatus().equals(FTSIndex.Status.STALE) && anyStaleHosts && allOtherSteady;
  }

  /*
   * Return true if there is a STALE host and all other hosts are STEADY.
   */
  public static boolean shouldChangeIndexStatusToStale(
      FTSIndex ftsIndex,
      Map<String, FTSIndexConfigStats> hostToConfigStats,
      Set<String> mongotHostnames,
      ClusterDescription clusterDescription) {
    if (clusterDescription.isPaused()) {
      return false;
    }
    boolean anyStaleHosts =
        mongotHostnames.stream()
            .anyMatch(
                hostname ->
                    hostToConfigStats.containsKey(hostname)
                        && hostToConfigStats.get(hostname).getStats().isStale());

    boolean allOtherSteady =
        mongotHostnames.stream()
            .filter(
                hostname ->
                    hostToConfigStats.containsKey(hostname)
                        && !hostToConfigStats.get(hostname).getStats().isStale())
            .allMatch(
                hostname ->
                    hostToConfigStats.containsKey(hostname)
                        && hostToConfigStats.get(hostname).getStats().isSteady());

    return !ftsIndex.getStatus().equals(FTSIndex.Status.STALE) && anyStaleHosts && allOtherSteady;
  }

  public static Optional<String> getStaleIndexMessage(
      FTSIndex ftsIndex, Set<String> primaryHostnames) {
    if (ftsIndex.getStats().isEmpty()) {
      return Optional.empty();
    }
    final FTSIndexStatusMap<? extends FTSIndexHostStat> ftsIndexStatsMap =
        ftsIndex.getStats().get();
    return primaryHostnames.stream()
        .filter(
            hostname ->
                ftsIndexStatsMap.containsKey(hostname) && ftsIndexStatsMap.get(hostname).isStale())
        .map(hostname -> ftsIndexStatsMap.get(hostname).getErrorMessage().orElse(""))
        .findFirst();
  }

  public static Optional<String> getStaleIndexMessage(
      Map<String, FTSIndexConfigStats> hostToConfigStats, Set<String> mongotHostnames) {
    return mongotHostnames.stream()
        .filter(
            hostname ->
                hostToConfigStats.containsKey(hostname)
                    && hostToConfigStats.get(hostname).getStats().isStale())
        .map(hostname -> hostToConfigStats.get(hostname).getStats().getErrorMessage().orElse(""))
        .findFirst();
  }

  private static boolean isSystemDatabase(final String databaseName) {
    return List.of("admin", "local", "config").contains(databaseName.toLowerCase());
  }

  /**
   * Validates the quantization value for vector fields.
   *
   * @param pDBObject The field document containing atlas vector search index definition.
   * @throws SvcException if the quantization value is invalid or if numDimensions is not a multiple
   *     of 8 for binary quantization.
   */
  public static void validateQuantization(BasicDBObject pDBObject) throws SvcException {
    if (pDBObject.containsKey("type") && "vector".equals(pDBObject.getString("type"))) {
      if (pDBObject.containsKey("quantization")) {
        String quantization = pDBObject.getString("quantization");
        if (!VALID_QUANTIZATION_VALUES.contains(quantization)) {
          throw new SvcException(
              NDSErrorCode.INVALID_ARGUMENT,
              String.format(
                  "value for quantization: %s. Allowed values are: none, scalar, binary.",
                  quantization));
        }

        if ((quantization.equals("binary")
            && pDBObject.containsKey("numDimensions")
            && pDBObject.getInt("numDimensions") % 8 != 0)) {
          throw new SvcException(
              NDSErrorCode.INVALID_ARGUMENT,
              "numDimensions: it must be a multiple of 8 for binary quantization");
        }
      }
    }
  }

  private void validateFTSIndexUpdate(
      final Group group, final String clusterName, final FTSIndex ftsIndex, boolean isTenantCluster)
      throws SvcException {
    if (!StringUtils.isBlank(ftsIndex.getDatabase()) && isSystemDatabase(ftsIndex.getDatabase())) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "database");
    }
    if (ftsIndex.getActualType() == FTSIndex.Type.SEARCH) {
      FTSSearchIndex textSearchIndex = ftsIndex.toTextSearchIndex();
      BasicDBObject mappings =
          textSearchIndex
              .getMappings()
              .orElseThrow(() -> new SvcException(NDSErrorCode.MISSING_ATTRIBUTE, "mappings"));
      if (mappings.containsField(IndexView.FieldDefs.FIELDS)) {
        var fields = mappings.get(IndexView.FieldDefs.FIELDS);
        var invalidFields = String.join(", ", getInvalidIndexMappingFields(fields));
        if (StringUtils.isNotBlank(invalidFields)) {
          throw new SvcException(
              NDSErrorCode.INVALID_ARGUMENT,
              "field mappings: %s. Dollar ($) prefixed fields are not allowed."
                  .formatted(invalidFields));
        }
      }
      validateTypeSets(textSearchIndex);

      Optional<List<FTSSynonymMappingDefinition>> synonyms =
          ftsIndex.toTextSearchIndex().getSynonyms();
      if (synonyms.isPresent()) {
        for (int i = 0; i < synonyms.get().size(); i++) {
          if (synonyms.get().get(i).getName() == null) {
            throw new SvcException(
                NDSErrorCode.MISSING_ATTRIBUTE, String.format("synonyms[%d].name", i));
          }
          if (synonyms.get().get(i).getAnalyzer() == null) {
            throw new SvcException(
                NDSErrorCode.MISSING_ATTRIBUTE, String.format("synonyms[%d].analyzer", i));
          }
          if (synonyms.get().get(i).getSource().getCollection() == null) {
            throw new SvcException(
                NDSErrorCode.MISSING_ATTRIBUTE, String.format("synonyms[%d].source.collection", i));
          }
        }
      }
      try {
        // The additional `toNormalizedBasicDBObject` call is to keep class type compatible with
        // `ApiAtlasTextSearchIndexDefinitionView`.
        new ApiAtlasTextSearchIndexDefinitionView(
            new FTSSearchIndex(toNormalizedBasicDBObject(ftsIndex)).toIndexDefinition());
      } catch (Exception e) {
        LOG.warn("Index definition is invalid", e);
        throw new SvcException(
            NDSErrorCode.INVALID_ARGUMENT, "index definition: " + e.getMessage());
      }
    }
    if (ftsIndex.getActualType() == FTSIndex.Type.VECTOR_SEARCH) {
      validateVectorSearchIndex(ftsIndex.toVectorSearchIndex());
      throwIfNotValidAutoEmbeddingIndex(ftsIndex.toVectorSearchIndex(), isTenantCluster, group);
      try {
        // The additional `toNormalizedBasicDBObject` call is to keep class type compatible with
        // `ApiAtlasVectorSearchIndexDefinitionView`.
        new ApiAtlasVectorSearchIndexDefinitionView(
            new FTSVectorSearchIndex(toNormalizedBasicDBObject(ftsIndex)).toIndexDefinition());
      } catch (Exception e) {
        LOG.warn("Index definition is invalid", e);
        throw new SvcException(
            NDSErrorCode.INVALID_ARGUMENT, "index definition: " + e.getMessage());
      }
    }

    if (ftsIndex.getName() != null && !ValidationUtils.isValidSearchIndexName(ftsIndex.getName())) {
      throw new SvcException(NDSErrorCode.INVALID_SEARCH_INDEX_NAME, ftsIndex.getName());
    }
    if (ftsIndex.getNumPartitions().isPresent()) {
      if (isTenantCluster
          || !_featureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.ATLAS_SEARCH_MULTIPLE_SUB_INDEXES, null, group)) {
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "numPartitions");
      }
      Set<Integer> allowedValuesForNumPartitions =
          _featureFlagSvc.isFeatureFlagEnabled(
                  FeatureFlag.ATLAS_SEARCH_INDEX_PARTITIONING_LOOSE_RESTRICTION, null, group)
              ? FTSIndex.ALLOWED_VALUES_FOR_NUM_PARTITIONS_LOOSE_RESTRICTION
              : FTSIndex.ALLOWED_VALUES_FOR_NUM_PARTITIONS;
      if (!allowedValuesForNumPartitions.contains(ftsIndex.getNumPartitions().get())) {
        throw new SvcException(
            NDSErrorCode.INVALID_ARGUMENT,
            String.format(
                "numPartitions - allowed values are %s",
                allowedValuesForNumPartitions.stream().sorted().toList()));
      }
      ClusterDescription clusterDescription =
          _ndsClusterSvc
              .getActiveClusterDescription(group.getId(), clusterName)
              .orElseThrow(
                  () ->
                      new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, clusterName, group.getId()));
      var partitioningAllowedOnCoupled =
          _featureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.ATLAS_SEARCH_INDEX_PARTITIONING_COLOCATED, null, group);
      Optional<SearchDeploymentDescription> searchDeploymentDescription =
          _searchDeploymentDescriptionSvc.findNonDeletedDeploymentBySourceMongoCluster(
              clusterDescription.getUniqueId());
      if (!partitioningAllowedOnCoupled) {
        if (searchDeploymentDescription.isEmpty()
            || searchDeploymentDescription.get().isDeleteRequested()) {
          throw new SvcException(
              NDSErrorCode.INVALID_ARGUMENT, "numPartitions - only allowed for Search nodes");
        }
        Optional<Migration> migration =
            searchDeploymentDescription.flatMap(SearchDeploymentDescription::getMigrationOptional);
        if (migration.isPresent() && Migration.isMigrationInProgress(migration.get())) {
          throw new SvcException(
              NDSErrorCode.FTS_DEPLOYMENT_DESCRIPTION_MIGRATION_IN_PROGRESS,
              "Cannot set numPartitions during search deployment migration");
        }
      }
    }
    if (isTenantCluster) {
      if (ftsIndex
              .toDBObject()
              .toJson(JsonWriterSettings.builder().outputMode(JsonMode.STRICT).build())
              .getBytes()
              .length
          > 3 * 1024) {
        throw new SvcException(NDSErrorCode.TENANT_FTS_INDEX_TOO_LARGE);
      }
    }
  }

  @VisibleForTesting
  void validateTypeSets(FTSSearchIndex textSearchIndex) throws SvcException {
    if (textSearchIndex.getTypeSets().isEmpty()) {
      return;
    }
    var typeSets = textSearchIndex.getTypeSets().get();
    var typeNames = new HashSet<String>();
    for (int i = 0; i < typeSets.size(); i++) {
      // Check for missing name
      var typeSet = typeSets.get(i);
      if (typeSet.getName() == null) {
        throw new SvcException(
            NDSErrorCode.MISSING_ATTRIBUTE, String.format("typeSets[%d].name", i));
      }
      // Check for duplicate type names
      if (!typeNames.add(typeSet.getName())) {
        throw new SvcException(
            NDSErrorCode.INVALID_ARGUMENT,
            String.format("typeSets[%d].name - \"%s\" is not unique", i, typeSet.getName()));
      }
      // Check for missing types
      if (typeSet.getTypes() == null || typeSet.getTypes().isEmpty()) {
        throw new SvcException(
            NDSErrorCode.MISSING_ATTRIBUTE, String.format("typeSets[%d].types", i));
      }
      // Check for invalid types
      var types = new HashSet<String>();
      for (int j = 0; j < typeSet.getTypes().size(); j++) {
        var type = typeSet.getTypes().get(j).getString("type");
        if (type == null || type.isEmpty()) {
          throw new SvcException(
              NDSErrorCode.MISSING_ATTRIBUTE, String.format("typeSets[%d].types[%d].type", i, j));
        }
        if (!ALLOWED_CONFIGURABLE_DYNAMIC_INDEXING_TYPES.contains(type)) {
          var sortedAllowedTypes =
              ALLOWED_CONFIGURABLE_DYNAMIC_INDEXING_TYPES.stream()
                  .sorted()
                  .collect(Collectors.joining(", ", "[", "]"));
          throw new SvcException(
              NDSErrorCode.INVALID_ARGUMENT,
              String.format(
                  "typeSets[%d].types[%d].type - \"%s\" is not a valid type, allowed types are %s",
                  i, j, type, sortedAllowedTypes));
        }
        if (!types.add(type)) {
          throw new SvcException(
              NDSErrorCode.INVALID_ARGUMENT,
              String.format("typeSets[%d].types[%d].type - \"%s\" is not unique", i, j, type));
        }
      }
    }
  }

  private void validateVectorSearchIndex(FTSVectorSearchIndex ftsVectorSearchIndex)
      throws SvcException {
    List<BasicDBObject> fields =
        ftsVectorSearchIndex
            .getFields()
            .orElseThrow(() -> new SvcException(NDSErrorCode.MISSING_ATTRIBUTE, "fields"));

    for (BasicDBObject field : fields) {
      BasicDBObject normalizedField = toNormalizedBasicDBObject(field);
      FTSIndexConfigUtil.validateQuantization(normalizedField);
    }
  }

  public void validateFTSIndexUpdate(
      final Group group, final String clusterName, final FTSIndex ftsIndex) throws SvcException {
    validateFTSIndexUpdate(group, clusterName, ftsIndex, false);
  }

  public void validateTenantFTSIndexUpdate(
      final Group group, final String clusterName, final FTSIndex ftsIndex) throws SvcException {
    validateFTSIndexUpdate(group, clusterName, ftsIndex, true);
  }

  @VisibleForTesting
  static List<String> getInvalidIndexMappingFields(final Object object) {
    if (object instanceof Map<?, ?> map) {
      return getInvalidIndexMappingFields(map, new ArrayList<>(), "");
    } else {
      return List.of();
    }
  }

  /**
   * Returns list of invalid field names. The field name is invalid if it starts with $ (dollar
   * sign). If invalid field has subfields then they are skipped. This is a recursive method.
   */
  private static List<String> getInvalidIndexMappingFields(
      final Map<?, ?> map, List<String> fields, String keyPrefix) {
    map.keySet()
        .forEach(
            key -> {
              var fullKey = keyPrefix + key;
              if (key.toString().startsWith("$")) {
                fields.add(fullKey);
              } else {
                var value = map.get(key);
                if (value instanceof Map<?, ?> bsonObject) {
                  if ("document".equals(bsonObject.get(FieldDefs.TYPE))) {
                    var embedded = bsonObject.get(IndexView.FieldDefs.FIELDS);
                    if (embedded instanceof Map<?, ?> embeddedBsonObject) {
                      getInvalidIndexMappingFields(embeddedBsonObject, fields, fullKey + ".");
                    }
                  }
                }
              }
            });
    return fields;
  }

  public void validateFTSIndex(
      final Group group, final String clusterName, final FTSIndex ftsIndex, boolean isTenantCluster)
      throws SvcException {
    validateFTSIndexUpdate(group, clusterName, ftsIndex, isTenantCluster);
    if (StringUtils.isBlank(ftsIndex.getName())) {
      throw new SvcException(NDSErrorCode.MISSING_ATTRIBUTE, "name");
    }
    if (StringUtils.isBlank(ftsIndex.getDatabase())) {
      throw new SvcException(NDSErrorCode.MISSING_ATTRIBUTE, "database");
    }
    if (StringUtils.isBlank(ftsIndex.getLastObservedCollectionName())) {
      throw new SvcException(NDSErrorCode.MISSING_ATTRIBUTE, "collectionName");
    }

    try {
      MongoNamespace.checkDatabaseNameValidity(ftsIndex.getDatabase());
    } catch (IllegalArgumentException e) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "database");
    }
  }

  public void validateFTSIndex(
      final Group group, final ClusterDescription clusterDescription, final FTSIndex ftsIndex)
      throws SvcException {
    validateFTSIndex(
        group, clusterDescription.getName(), ftsIndex, clusterDescription.isTenantCluster());
  }

  public static List<FTSIndex> getIndexDeletionCandidates(
      List<FTSIndex> indexesInConfig,
      Map<ObjectId, FTSIndex> mongotIndexIdToIndexMap,
      String hostname) {
    return indexesInConfig.stream()
        .filter(
            index ->
                index.getDeleteRequestedDate().isPresent()
                    && FTSIndexConfigUtil.needToResolveIndexDeletion(
                        index,
                        Optional.ofNullable(mongotIndexIdToIndexMap.get(index.getIndexId())),
                        hostname))
        .toList();
  }

  public static boolean needToResolveIndexDeletion(
      final FTSIndex indexToDelete,
      final Optional<FTSIndex> ftsIndexFromMongot,
      final String hostname) {
    final Optional<? extends FTSIndexHostStat> configHostStat = indexToDelete.getHostStat(hostname);
    final Optional<? extends FTSIndexHostStat> mongotHostStat =
        ftsIndexFromMongot.flatMap(index -> index.getHostStat(hostname));

    if (configHostStat.isPresent() && mongotHostStat.isPresent()) {
      final long optimeInConfig = indexToDelete.getOptime();
      final long optimeFromMongot = mongotHostStat.map(FTSIndexHostStat::getOptime).orElse(0L);

      final Optional<Date> hostLastUpdateDate = configHostStat.get().getLastUpdateDate();
      final Optional<Date> indexDeleteRequestedDate = indexToDelete.getDeleteRequestedDate();

      final FTSIndexHostStat.StatusCode statusFromMongot =
          mongotHostStat.flatMap(FTSIndexHostStat::getStatusCode).orElse(null);

      // The last updated must be after the delete requested date to ensure that there has been a
      // previous conf call. Also, the optime must be after the most recent collection rename so we
      // do not block legitimate deletes
      return hostLastUpdateDate.isPresent()
          && indexDeleteRequestedDate.isPresent()
          && hostLastUpdateDate.get().after(indexDeleteRequestedDate.get())
          && (STATUS_CODES_WITHOUT_OPTIME_CHECK.contains(statusFromMongot)
              || optimeInConfig <= optimeFromMongot);
    }
    return false;
  }

  public static List<FTSIndex> getIndexDeletionCandidates(
      List<FTSIndex> indexesInConfig,
      Map<ObjectId, FTSIndex> mongotIndexIdToIndexMap,
      List<FTSIndexConfigStats> configStatsInDb,
      String hostname) {
    var indexIdToConfigStats = groupIndexConfigStats(configStatsInDb);

    return indexesInConfig.stream()
        .filter(
            index -> {
              Optional<? extends FTSIndexHostStat> configHostStat =
                  Optional.ofNullable(indexIdToConfigStats.get(index.getIndexId()))
                      .map(statsByHost -> statsByHost.get(hostname))
                      .map(FTSIndexConfigStats::getStats);
              Optional<? extends FTSIndexHostStat> mongotHostStat =
                  Optional.ofNullable(mongotIndexIdToIndexMap.get(index.getIndexId()))
                      .flatMap(i -> i.getHostStat(hostname));

              if (configHostStat.isPresent() && mongotHostStat.isPresent()) {
                long optimeInConfig = index.getOptime();
                long optimeFromMongot = mongotHostStat.map(FTSIndexHostStat::getOptime).orElse(0L);

                Optional<Date> hostLastUpdateDate = configHostStat.get().getLastUpdateDate();
                Optional<Date> indexDeleteRequestedDate = index.getDeleteRequestedDate();

                final FTSIndexHostStat.StatusCode statusFromMongot =
                    mongotHostStat.flatMap(FTSIndexHostStat::getStatusCode).orElse(null);

                // The last updated must be after the delete requested date to ensure that there has
                // been a previous conf call. Also, the optime must be after the most recent
                // collection rename so we do not block legitimate deletes
                return hostLastUpdateDate.isPresent()
                    && indexDeleteRequestedDate.isPresent()
                    && hostLastUpdateDate.get().after(indexDeleteRequestedDate.get())
                    && (STATUS_CODES_WITHOUT_OPTIME_CHECK.contains(statusFromMongot)
                        || optimeInConfig <= optimeFromMongot);
              }
              return false;
            })
        .toList();
  }

  public static boolean needToResolveCollectionUUID(
      final FTSIndex ftsIndex, final FTSIndex ftsIndexFromMongot) {
    final Optional<UUID> collectionUUIDOpt = ftsIndex.getCollectionUUID();
    final Optional<UUID> collectionUUIDFromMongotOpt = ftsIndexFromMongot.getCollectionUUID();
    // mongot expects collection UUID never change once set
    return collectionUUIDOpt.isEmpty() && collectionUUIDFromMongotOpt.isPresent();
  }

  public static boolean needToResolveCollectionName(
      final String hostname, final FTSIndex ftsIndex, final FTSIndex ftsIndexFromMongot) {
    final long optimeInConfig = ftsIndex.getOptime();
    final long optimeFromMongot =
        ftsIndexFromMongot.getHostStat(hostname).map(FTSIndexHostStat::getOptime).orElse(0l);

    return (!ftsIndex
            .getLastObservedCollectionName()
            .equals(ftsIndexFromMongot.getLastObservedCollectionName()))
        && optimeInConfig <= optimeFromMongot;
  }

  public static boolean violatingUniqueIndexNamePerCollectionUUID(
      final List<FTSIndex> ftsIndexes, final String indexName, final UUID pNewCollectionUUID) {
    return ftsIndexes.stream()
        .filter(ftsIndex -> ftsIndex.getCollectionUUID().isPresent())
        .anyMatch(
            ftsIndex ->
                ftsIndex.getCollectionUUID().get().equals(pNewCollectionUUID)
                    && ftsIndex.getName().equals(indexName));
  }

  public static boolean violatingUniqueIndexNamePerCollectionName(
      final List<FTSIndex> ftsIndexes,
      final String indexName,
      final String collectionName,
      final String database) {
    return ftsIndexes.stream()
        .anyMatch(
            ftsIndex ->
                ftsIndex.getDatabase().equals(database)
                    && ftsIndex.getLastObservedCollectionName().equals(collectionName)
                    && ftsIndex.getName().equals(indexName));
  }

  public static boolean hasNoCollectionNameChange(
      final FTSIndex indexToDelete, final Map<ObjectId, FTSIndex> mongotIndexesMap) {
    if (mongotIndexesMap.containsKey(indexToDelete.getIndexId())) {
      final FTSIndex matchingMongotIndex = mongotIndexesMap.get(indexToDelete.getIndexId());
      boolean noCollectionNameChange =
          matchingMongotIndex
              .getLastObservedCollectionName()
              .equals(indexToDelete.getLastObservedCollectionName());
      if (!noCollectionNameChange) {
        LOG.info(
            "Unsetting index delete request for index {} on database {} as collection {} was"
                + " renamed to {}",
            indexToDelete.getName(),
            indexToDelete.getDatabase(),
            indexToDelete.getLastObservedCollectionName(),
            matchingMongotIndex.getLastObservedCollectionName());
      }
      return noCollectionNameChange;
    }
    return false;
  }

  public static List<FTSIndex> getIndexesToGarbageCollect(
      List<FTSIndex> indexesFromMongot,
      Map<ObjectId, FTSIndex> indexMapFromConfig,
      String hostname,
      List<String> clusterHostnames) {
    return indexesFromMongot.stream()
        .filter(
            index -> {
              FTSIndex indexInDb = indexMapFromConfig.get(index.getIndexId());
              boolean mongotStatusNotFound =
                  index.getHostStat(hostname).map(stat -> stat.getStatusCode().get()).orElse(null)
                      == FTSIndexHostStat.StatusCode.DOES_NOT_EXIST;
              boolean existingStatusAllNotFound =
                  clusterHostnames.stream()
                      .filter(h -> !h.equals(hostname))
                      .map(indexInDb::getHostStat)
                      .allMatch(
                          stat ->
                              stat.isPresent()
                                  && stat.get().getStatusCode().orElse(null)
                                      == FTSIndexHostStat.StatusCode.DOES_NOT_EXIST);
              return mongotStatusNotFound && existingStatusAllNotFound;
            })
        .toList();
  }

  public static List<FTSIndex> getIndexesToGarbageCollect(
      List<FTSIndex> indexesFromMongot,
      List<FTSIndexConfigStats> configStatsInDb,
      String hostname,
      List<String> clusterHostnames) {
    var indexIdToConfigStats = groupIndexConfigStats(configStatsInDb);

    return indexesFromMongot.stream()
        .filter(
            index -> {
              Map<String, FTSIndexConfigStats> hostToConfigStats =
                  indexIdToConfigStats.getOrDefault(index.getIndexId(), Collections.emptyMap());
              boolean mongotStatusNotFound =
                  index.getHostStat(hostname).map(stat -> stat.getStatusCode().get()).orElse(null)
                      == FTSIndexHostStat.StatusCode.DOES_NOT_EXIST;
              boolean existingStatusAllNotFound =
                  clusterHostnames.stream()
                      .filter(h -> !h.equals(hostname))
                      .map(hostToConfigStats::get)
                      .allMatch(
                          stat ->
                              stat != null
                                  && stat.getStats().getStatusCode().orElse(null)
                                      == FTSIndexHostStat.StatusCode.DOES_NOT_EXIST);
              return mongotStatusNotFound && existingStatusAllNotFound;
            })
        .toList();
  }

  /**
   * New synonyms collections should have valid name. For more, see:
   * https://www.mongodb.com/docs/manual/reference/limits/#mongodb-limit-Restriction-on-Collection-Names
   *
   * @param synonymsCollectionNamesToCreate - list of new synonyms collections
   */
  public static void validateNewSynonymsCollectionsNames(
      final List<String> synonymsCollectionNamesToCreate) throws SvcException {
    for (String synonymsCollectionName : synonymsCollectionNamesToCreate) {
      if (!ValidationUtils.isValidCollectionName(synonymsCollectionName)) {
        throw new SvcException(NDSErrorCode.COLLECTION_NAME_INVALID, synonymsCollectionName);
      }
    }
  }

  /**
   * New synonyms collections should be referenced in the Index synonyms mapping.
   *
   * @param synonymsCollectionNamesToCreate - list of new synonyms collections
   * @param synonymsSourceCollectionNames - list of synonyms collections mentioned in the Index
   *     definition "synonyms" mapping
   */
  public static void validateNewSynonymsCollectionsMapping(
      final List<String> synonymsCollectionNamesToCreate,
      final List<String> synonymsSourceCollectionNames)
      throws SvcException {
    for (String synonymsCollectionName : synonymsCollectionNamesToCreate) {
      if (!synonymsSourceCollectionNames.contains(synonymsCollectionName)) {
        throw new SvcException(
            NDSErrorCode.INVALID_NEW_SYNONYM_COLLECTION_MAPPING, synonymsCollectionName);
      }
    }
  }

  /**
   * New synonyms collections should not exist in database.
   *
   * @param groupId - group ID
   * @param clusterName - cluster name
   * @param databaseName - database name where new collections will be created
   * @param synonymsCollectionNamesToCreate - list of new empty collections that must be created
   *     prior creating/updating the index
   * @param auditInfo - audit info
   */
  public void validateNewSynonymsCollectionsAreAbsent(
      final ObjectId groupId,
      final String clusterName,
      final String databaseName,
      final List<String> synonymsCollectionNamesToCreate,
      final AuditInfo auditInfo)
      throws SvcException {
    if (synonymsCollectionNamesToCreate.isEmpty()) {
      return;
    }

    try {
      Set<String> existingCollections =
          new HashSet<>(
              _searchClusterConnectionSvc.listCollectionNames(
                  groupId, clusterName, databaseName, auditInfo));

      for (String synonymsCollectionName : synonymsCollectionNamesToCreate) {
        if (existingCollections.contains(synonymsCollectionName)) {
          throw new SvcException(
              NDSErrorCode.INVALID_SYNONYM_COLLECTION_EXISTS, synonymsCollectionName);
        }
      }
    } catch (final SvcException e) {
      if (e.getErrorCode().equals(NDSErrorCode.INVALID_SYNONYM_COLLECTION_EXISTS)) {
        throw e;
      }

      LOG.error("Error retrieving list of collections names", e);
      throw new SvcException(NDSErrorCode.CANNOT_VALIDATE_NEW_SYNONYMS_COLLECTIONS);
    }
  }

  /**
   * Utility method returns true if any of the vector indexes are an autoembedding index. This is
   * done by looking at the "type" field of the index to check if it's of the type "TEXT"
   *
   * @param vectorIndices List of vector indices that have been configured
   * @return true if there one of the vector indexes are an autoembedding index
   */
  public static boolean autoEmbeddingIndexExists(List<FTSVectorSearchIndex> vectorIndices) {
    // This predicate goes over all fields in a vector index and returns true if any one of them
    // are of type TEXT which indicates that this is an auto-embedding index
    final Predicate<List<BasicDBObject>> countIndexPredicate =
        fields ->
            fields.stream()
                .map(
                    field ->
                        BasicDBObjectUtil.getOptionalField(
                            field, FTSIndex.FieldDefs.TYPE, String.class))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .anyMatch(fieldType -> fieldType.equalsIgnoreCase("TEXT"));

    return vectorIndices.stream()
        .map(FTSVectorSearchIndex::getFields)
        .filter(Optional::isPresent)
        .map(Optional::get)
        .anyMatch(countIndexPredicate);
  }

  private BasicDBObject toNormalizedBasicDBObject(FTSIndex ftsIndex) {
    // The encoding and decoding calls can normalize class type. e.g. BsonBoolean -> Boolean.
    // Without these calls, some class types cannot be handled correctly.
    return toNormalizedBasicDBObject(ftsIndex.toDBObject());
  }

  private BasicDBObject toNormalizedBasicDBObject(BasicDBObject basicDBObject) {
    return BasicDBObject.parse(
        basicDBObject.toJson(JsonWriterSettings.builder().outputMode(JsonMode.EXTENDED).build()));
  }

  // TODO(CLOUDP-335687): Reuse this validation in confcall handler or
  // VectorSearchIndexDetailedStatusesView to enable faster error detection.
  private void throwIfNotValidAutoEmbeddingIndex(
      FTSIndex index, boolean isTenantCluster, Group group) throws SvcException {
    Set<String> modelRequested = getAllEmbeddingModelsRequestedFromIndex(index);
    if (modelRequested.isEmpty()) {
      return;
    }
    if (isTenantCluster) {
      throw new SvcException(
          NDSErrorCode.FTS_AUTO_EMBEDDING_INDEX_INVALID,
          String.format(
              "AutoEmbedding index: %s is not supported in free/flex tier. Please upgrade your"
                  + " tier.",
              index.getName()));
    }

    if (!_featureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_SEARCH_AUTO_EMBEDDING_JSON_EDITOR, null, group)) {
      throw new SvcException(
          NDSErrorCode.FTS_AUTO_EMBEDDING_INDEX_INVALID,
          String.format(
              "AutoEmbedding feature for index: %s is not supported in your project."
                  + " Please contact your admin to enable.",
              index.getName()));
    }

    Set<String> supportedModels =
        _autoEmbeddingV1Svc.getAllAutoEmbeddings().stream()
            .map(autoEmbeddingV1 -> autoEmbeddingV1.id().embeddingModelName().toLowerCase())
            .collect(Collectors.toSet());
    Set<String> unsupportedModels = Sets.difference(modelRequested, supportedModels);
    if (!unsupportedModels.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.FTS_AUTO_EMBEDDING_INDEX_INVALID,
          String.format(
              "Unsupported models: %s found in index: %s, supported models are: %s",
              unsupportedModels, index.getName(), supportedModels));
    }
  }

  private Set<String> getAllEmbeddingModelsRequestedFromIndex(FTSIndex index) {
    if (index.getActualType() != FTSIndex.Type.VECTOR_SEARCH) {
      return Collections.emptySet();
    }
    var ftsVectorIndex = index.toVectorSearchIndex();
    return ftsVectorIndex.getFields().stream()
        .flatMap(basicDBObjectList -> basicDBObjectList.stream())
        .filter(
            basicDBObject ->
                BasicDBObjectUtil.getOptionalField(
                        basicDBObject, FTSIndex.FieldDefs.TYPE, String.class)
                    .filter(fieldType -> fieldType.equalsIgnoreCase("TEXT"))
                    .isPresent())
        .map(
            basicDBObject ->
                BasicDBObjectUtil.getOptionalField(basicDBObject, "model", String.class)
                    .orElse(AutoEmbeddingV1.DEFAULT_MODEL_NAME)
                    .toLowerCase())
        .collect(Collectors.toSet());
  }

  private static Map<ObjectId, Map<String, FTSIndexConfigStats>> groupIndexConfigStats(
      List<FTSIndexConfigStats> configStats) {
    // Group config stats by indexId and then by hostname.
    return configStats.stream()
        .collect(
            Collectors.groupingBy(
                stats -> stats.getId().getIndexId(),
                Collectors.toMap(stats -> stats.getId().getHostname(), Function.identity())));
  }
}
