package com.xgen.svc.nds.util;

import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.diskwarming._public.model.DiskWarmingInstance;
import com.xgen.cloud.nds.diskwarming._public.svc.DiskWarmingSvc;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.slf4j.Logger;

@Singleton
public class DiskWarmingUtil {
  private final DiskWarmingSvc _diskWarmingSvc;

  @Inject
  public DiskWarmingUtil(final DiskWarmingSvc pDiskWarmingSvc) {
    _diskWarmingSvc = pDiskWarmingSvc;
  }

  public Optional<ObjectId> startDiskWarmingIfNeeded(
      final boolean isVolumeCreatedFromSnapshot,
      final InstanceHardware pInstanceHardware,
      final ClusterDescription pClusterDescription,
      final String pHostname,
      final String pCloudProviderInstanceId,
      final Logger pPlanLogger)
      throws IllegalStateException {
    final boolean isDiskWarmingModeSet = pClusterDescription.getDiskWarmingMode().isPresent();

    final List<DiskWarmingInstance> activeWarmingInstances =
        _diskWarmingSvc.findActiveWarmingInstanceByHostname(
            pClusterDescription.getGroupId(), pClusterDescription.getName(), pHostname);

    if (activeWarmingInstances.stream()
        .anyMatch(ins -> ins.getCloudProviderInstanceId().equals(pCloudProviderInstanceId))) {
      throw new IllegalStateException(
          String.format(
              "disk warming instance with the same hostname %s and the same cloudProviderInstanceId"
                  + " %s is getting reused",
              pHostname, pCloudProviderInstanceId));
    }
    // There shouldn't exist other in_progress warming doc with the same hostname but different
    // cloudProviderId. In this case, we will cancel all the existing ones and create a new one
    // with the current CPid
    activeWarmingInstances.stream()
        .filter(ins -> !ins.getCloudProviderInstanceId().equals(pCloudProviderInstanceId))
        .forEach(
            ins -> {
              _diskWarmingSvc.setWarmingDocToCancelledWithCloudProviderId(
                  pHostname, ins.getCloudProviderInstanceId());

              pPlanLogger
                  .atWarn()
                  .setMessage("Setting stale warming instance to CANCELLED state")
                  .addKeyValue("hostname", pHostname)
                  .addKeyValue("cloudProviderInstanceId", ins.getCloudProviderInstanceId())
                  .log();
            });

    if (isVolumeCreatedFromSnapshot && isDiskWarmingModeSet) {
      pPlanLogger
          .atInfo()
          .setMessage("Creating disk warming doc")
          .addKeyValue("hostname", pHostname)
          .addKeyValue("cloudProviderInstanceId", pCloudProviderInstanceId)
          .log();
      return _diskWarmingSvc.createInitialWarmingInstance(
          pClusterDescription,
          pHostname,
          pInstanceHardware.getInstanceId(),
          pCloudProviderInstanceId);
    }

    return Optional.empty();
  }

  public Optional<DiskWarmingInstance> findLatestDiskWarmingInstance(
      final String pHostname, final String pCloudProviderInstanceId) {
    final Optional<DiskWarmingInstance> instanceOpt =
        _diskWarmingSvc.findLatestWarmingInstanceByHostname(
            pHostname, Optional.of(pCloudProviderInstanceId));
    return instanceOpt;
  }

  public Optional<DiskWarmingInstance> findDiskWarmingInstanceByDocId(final ObjectId pId) {
    return _diskWarmingSvc.findInstanceByDocId(pId);
  }

  public boolean setWarmingInstanceToFail(
      final String pHostname, final Optional<String> pCloudProviderInstanceId) {

    if (pCloudProviderInstanceId.isEmpty()) {
      // instance was failed to be created, no warming instance doc exists
      return true;
    }
    return _diskWarmingSvc.setWarmingDocToFailedWithCloudProviderId(
        pHostname, pCloudProviderInstanceId.get());
  }

  public void setWarmingInstanceToFail(final ObjectId pInstanceId) {

    final Optional<DiskWarmingInstance> instanceOpt =
        _diskWarmingSvc.findInstanceByInstanceId(pInstanceId);

    if (instanceOpt.isPresent()) {
      _diskWarmingSvc.setWarmingDocToFailedWithCloudProviderId(
          instanceOpt.get().getHostname(), instanceOpt.get().getCloudProviderInstanceId());
    }
  }

  public void cancelOngoingDiskWarming(final ObjectId pId) {

    final Optional<DiskWarmingInstance> instanceOpt = _diskWarmingSvc.findInstanceByDocId(pId);

    if (instanceOpt.isPresent()) {
      _diskWarmingSvc.setWarmingDocToCancelledWithCloudProviderId(
          instanceOpt.get().getHostname(), instanceOpt.get().getCloudProviderInstanceId());
    }
  }

  public boolean setNodePriority(
      final String pHostname, final String pCloudProviderInstanceId, final float pNodePriority) {

    final boolean result =
        _diskWarmingSvc.setNodePriority(pHostname, pCloudProviderInstanceId, pNodePriority);

    return result;
  }

  public boolean moreThanThreeFailedDiskWarmingWithin24Hours(final String pHostname) {

    return _diskWarmingSvc.findNumberOfFailedDiskWarmingWithinOneDay(pHostname) > 2;
  }
}
