package com.xgen.svc.nds.svc.project;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.cloud.nds.project._public.constants.NDSMaintenanceConstants.FORCE_AGENT_UPGRADE_DAYS_SINCE_RELEASE_THRESHOLD;
import static com.xgen.cloud.nds.project._public.constants.NDSMaintenanceConstants.IN_ADVANCED_NOTIFICATION_HOURS;
import static com.xgen.cloud.nds.project._public.constants.NDSMaintenanceConstants.MAX_NUM_DEFERRALS;
import static com.xgen.cloud.nds.project._public.constants.NDSMaintenanceConstants.SYSTEM_DEFINED_MAINTENANCE_INTERVAL_IN_DAYS;
import static com.xgen.cloud.nds.project._public.constants.NDSMaintenanceConstants.USER_DEFINED_MAINTENANCE_INTERVAL_IN_DAYS;
import static com.xgen.cloud.nds.project._public.model.ClusterDescription.State.IDLE;
import static com.xgen.cloud.nds.project._public.model.MaintenanceRelease.MAINTENANCE_WINDOW;
import static com.xgen.cloud.nds.project._public.model.MaintenanceRelease.RESPECT_PROTECTED_HOURS;
import static com.xgen.cloud.nds.project._public.model.MaintenanceRelease.criticalRelease;
import static com.xgen.svc.nds.svc.NDSEncryptionAtRestSvc.KMIP_MASTER_KEY_ROTATION_PERIOD_DAYS;
import static java.util.stream.Collectors.toList;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.alert.InformationalAlertSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.alerts.defaults._public.svc.DefaultAlertConfigSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.model._public.misc.AuditDescription;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.util._public.time.TZUtils;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.common.util._public.util.AgentVersion;
import com.xgen.cloud.deployment._public.model.AbstractAgentConfig;
import com.xgen.cloud.deployment._public.model.AutomationAgentConfig;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.BiConnectorTemplate;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6Comparable;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.deployment._public.model.diff.ItemDiff;
import com.xgen.cloud.deployment._public.model.diff.ItemDiffs;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSCheckMetadataConsistencyEvent;
import com.xgen.cloud.nds.activity._public.event.audit.NDSDbCheckEvent;
import com.xgen.cloud.nds.activity._public.event.audit.NDSMaintenanceWindowAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSMaintenanceWindowAudit.Builder;
import com.xgen.cloud.nds.activity._public.event.audit.NDSMaintenanceWindowAudit.Type;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.checkmetadataconsistency._public.svc.NDSCheckMetadataConsistencySvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.ZoneDistributionStatus;
import com.xgen.cloud.nds.common._public.model.MongoDBConfigType;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.common._public.util.NDSUtil;
import com.xgen.cloud.nds.dbcheck._public.model.DbCheck;
import com.xgen.cloud.nds.dbcheck._public.model.RunStatus;
import com.xgen.cloud.nds.dbcheck._public.svc.DbCheckSvc;
import com.xgen.cloud.nds.fts._public.svc.SearchIndexConfigSvc;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.ifr._public.model.IFREvent;
import com.xgen.cloud.nds.ifr._public.model.IFREvent.IFREventType;
import com.xgen.cloud.nds.ifr._public.svc.IFRSvc;
import com.xgen.cloud.nds.maintenance._public.model.NDSInternalMaintenanceRollout;
import com.xgen.cloud.nds.maintenance._public.svc.NDSInternalMaintenanceRolloutSvc;
import com.xgen.cloud.nds.metrics._public.svc.NDSComputeClusterMetricsSvc;
import com.xgen.cloud.nds.mongotune.binary._public.svc.MongotuneCompatibilityValidationSvc;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyStatus;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.mongotune.policies.util._public.svc.MongotunePolicyInitializationSvc;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionProcessArgsDao;
import com.xgen.cloud.nds.project._private.dao.EolVersionUpgradeHistoryDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupMaintenanceDao;
import com.xgen.cloud.nds.project._private.dao.QueuedAdminActionDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.CheckMetadataConsistency;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgsUpdatable;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionOperationOrigin;
import com.xgen.cloud.nds.project._public.model.EolVersionUpgradeHistory;
import com.xgen.cloud.nds.project._public.model.EolVersionUpgradeHistory.Status;
import com.xgen.cloud.nds.project._public.model.EolVersionUpgradeHistory.UpgradeType;
import com.xgen.cloud.nds.project._public.model.MaintenanceCheckResult;
import com.xgen.cloud.nds.project._public.model.MaintenanceHistoryMatchCriteria;
import com.xgen.cloud.nds.project._public.model.MaintenanceProtectedHours;
import com.xgen.cloud.nds.project._public.model.MaintenanceRelease;
import com.xgen.cloud.nds.project._public.model.MaintenanceRelease.ReleaseTimeslot;
import com.xgen.cloud.nds.project._public.model.MaintenanceType;
import com.xgen.cloud.nds.project._public.model.MongotuneProcessArgs;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSGroupMaintenanceWindow;
import com.xgen.cloud.nds.project._public.model.NdsMaintenanceHistory;
import com.xgen.cloud.nds.project._public.model.NdsMaintenanceHistory.InstanceHardwareDetails;
import com.xgen.cloud.nds.project._public.model.QueuedAdminAction;
import com.xgen.cloud.nds.project._public.model.QueuedAdminAction.State;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.SchedulingBehavior;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.SystemProjectType;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.ElevatedHealthMonitoring;
import com.xgen.cloud.nds.project._public.model.versions.FixedAgentVersion;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.project._public.model.versions.IFRState;
import com.xgen.cloud.nds.project._public.model.versions.PhasedReleaseCriteria;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.PhasedVersionFactory.MongoDBEOLPhasedVersion;
import com.xgen.cloud.nds.project._public.model.versions.ReleaseMode;
import com.xgen.cloud.nds.project._public.svc.INDSGroupMaintenanceSvc;
import com.xgen.cloud.nds.project._public.svc.NDSClusterSamplingSvc;
import com.xgen.cloud.nds.project._public.svc.NdsMaintenanceHistorySvc;
import com.xgen.cloud.nds.project._public.svc.VersionDeprecationSettingsSvc;
import com.xgen.cloud.nds.project._public.svc.admin.CriticalMaintenanceRunChunkJobStateSvc;
import com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringResultSvc;
import com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringSvc;
import com.xgen.cloud.nds.project._public.svc.versions.PhasedVersionSvc;
import com.xgen.cloud.nds.project._public.util.NDSMaintenanceDateCalculationUtil;
import com.xgen.cloud.nds.resourcepolicy._public.model.AtlasResourcePolicyAuthResponse;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.spothealthcheck._public.svc.ElevatedHealthMonitoringHealthCheckSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchDeploymentSvc;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchDeploymentTargets;
import com.xgen.cloud.search.settings._public.settings.SearchAppSettings;
import com.xgen.cloud.search.util._public.version.MongotVersion;
import com.xgen.cloud.streams._public.model.VPCProxyInstanceDescription;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.planner.CheckMetadataConsistencyMove;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.svc.AZSelectionSvc;
import com.xgen.svc.nds.svc.CustomMongoDbBuildSvc;
import com.xgen.svc.nds.svc.NDSResourcePolicySvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.text.SimpleDateFormat;
import java.time.Clock;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class NDSGroupMaintenanceSvc implements INDSGroupMaintenanceSvc {

  private static final Logger LOG = LoggerFactory.getLogger(NDSGroupMaintenanceSvc.class);

  private final int NO_EXTRA_MAINTENANCE_ADVANCED_NOTIFICATION_DATE_BUFFER_IN_MINUTES = 1;

  private static final Counter CORRUPTION_DETECTION_SUCCESSFUL_INITATION_RATE =
      PromMetricsSvc.registerCounter(
          "mms_nds_corruption_detection_maintenance_svc_dbcheck_total",
          "Number of dbchecks/checkMetadataConsistency runs submitted via the maintenance service"
              + " and that passed all preflight checks",
          "validationType",
          "operationOrigin");

  private static final Counter CORRUPTION_DETECTION_FAILED_INITIATION_RATE =
      PromMetricsSvc.registerCounter(
          "mms_nds_corruption_detection_maintenance_svc_dbcheck_preflight_failures_total",
          "Number of dbchecks/checkMetadataConsistency runs submitted via the maintenance service"
              + " and that failed preflight checks, so were deferred",
          "validationType",
          "failureType");

  private static final Counter PRIOR_TARGET_VERSION =
      PromMetricsSvc.registerCounter(
          "mms_nds_maintenance_prior_target_version_total",
          "Number of maintenances rolled out with a prior target version",
          "maintenanceType");

  private static final Counter NEEDED_MAINTENANCES_BY_TYPE =
      PromMetricsSvc.registerCounter(
          "mms_nds_needed_maintenance_type_total",
          "Number of needed maintenances by type",
          "maintenanceType");

  private static final Counter MAINTENANCES_QUEUED =
      PromMetricsSvc.registerCounter(
          "mms_nds_maintenance_queued_total", "Number of maintenances queued");

  private static final Histogram MAINTENANCE_DURATION_MINUTES =
      PromMetricsSvc.registerHistogram(
          "mms_nds_maintenance_duration_minutes",
          "Approximately how long it takes to perform maintenance in minutes",
          PromMetricsSvc.getHistogramExpBucket(5, 1.5, 10));

  private static final Histogram MAINTENANCE_DURATION_PAST_WINDOW_START_MINUTES =
      PromMetricsSvc.registerHistogram(
          "mms_nds_maintenance_duration_past_window_start_minutes",
          "How long into the maintenance window does maintenance start in minutes",
          PromMetricsSvc.getHistogramExpBucket(1, 1.6, 10));

  private static final Counter AZ_MAINTENANCE_STARTED =
      PromMetricsSvc.registerCounter(
          "mms_nds_az_maintenance_started_total",
          "Number of AZ maintenances started",
          "scheduledBehavior",
          "causedBy");

  private static final Histogram IFR_WAVE_MEMBERSHIP_CHECK_DURATION =
      Histogram.build()
          .name("mms_nds_ifr_wave_membership_check_duration")
          .help("Histogram of ifr wave membership check in seconds")
          .labelNames("waveNumber")
          .register();

  private final NDSGroupMaintenanceDao _ndsGroupMaintenanceDao;
  private final NDSGroupDao _ndsGroupDao;
  private final GroupSvc _groupSvc;
  private final NDSGroupSvc _ndsGroupSvc;
  private final AppSettings _appSettings;
  private final ClusterDescriptionDao _clusterDescriptionDao;
  private final ClusterDescriptionProcessArgsDao _clusterDescriptionProcessArgsDao;
  private final PhasedVersionSvc _phasedVersionSvc;
  private final NDSClusterSvc _clusterSvc;
  private final AuditSvc _auditSvc;
  private final CustomMongoDbBuildSvc _customMongoDbBuildSvc;
  private final SearchIndexConfigSvc _searchIndexConfigSvc;
  private final InformationalAlertSvc _informationalAlertSvc;
  private final MTMClusterDao _mtmClusterDao;
  private final CpsSvc _cpsSvc;
  private final DefaultAlertConfigSvc _defaultAlertConfigSvc;
  private final NdsMaintenanceHistorySvc _ndsMaintenanceHistorySvc;
  private final NDSClusterSamplingSvc _ndsClusterSamplingSvc;
  private final DbCheckSvc _dbCheckSvc;
  private final CriticalMaintenanceRunChunkJobStateSvc _criticalMaintenanceRunChunkJobStateSvc;
  private Clock _clock;
  private final QueuedAdminActionDao _queuedAdminActionDao;
  private final SearchDeploymentSvc _searchDeploymentSvc;
  private final NDSCheckMetadataConsistencySvc _ndsCheckMetadataConsistencySvc;
  private final VersionDeprecationSettingsSvc _versionDeprecationSettingsSvc;
  private final OrganizationSvc _organizationSvc;
  private final NDSMaintenanceDateCalculationUtil _ndsMaintenanceDateCalculationUtil;
  private final NDSInternalMaintenanceRolloutSvc _ndsInternalMaintenanceRolloutSvc;
  private final ElevatedHealthMonitoringSvc _elevatedHealthMonitoringSvc;
  private final AZSelectionSvc _azSelectionSvc;
  private final ReplicaSetHardwareDao _replicaSetHardwareDao;
  private final ElevatedHealthMonitoringHealthCheckSvc _elevatedHealthMonitoringHealthCheckSvc;
  private final ElevatedHealthMonitoringResultSvc _elevatedHealthMonitoringResultSvc;
  private final NDSResourcePolicySvc _ndsResourcePolicySvc;
  private final MongotuneCompatibilityValidationSvc _mongotuneCompatibilityValidationSvc;
  private final MongotunePolicyInitializationSvc _mongotunePolicyInitializationSvc;
  private final EolVersionUpgradeHistoryDao _eolVersionUpgradeHistoryDao;
  private final IFRSvc _ifrSvc;
  private final NDSComputeClusterMetricsSvc _ndsComputeClusterMetricsSvc;

  @Inject
  public NDSGroupMaintenanceSvc(
      final NDSGroupMaintenanceDao pNDSGroupMaintenanceDao,
      final NDSGroupDao pNDSGroupDao,
      final GroupSvc pGroupSvc,
      final NDSGroupSvc pNDSGroupSvc,
      final AppSettings pAppSettings,
      final ClusterDescriptionDao pClusterDescriptionDao,
      final ClusterDescriptionProcessArgsDao pClusterDescriptionProcessArgsDao,
      final PhasedVersionSvc pPhasedVersionSvc,
      final NDSClusterSvc pClusterSvc,
      final AuditSvc pAuditSvc,
      final CustomMongoDbBuildSvc pCustomMongoDbBuildSvc,
      final SearchIndexConfigSvc pSearchIndexConfigSvc,
      final InformationalAlertSvc pInformationalAlertSvc,
      final MTMClusterDao pMtmClusterDao,
      final CpsSvc pCpsSvc,
      final DefaultAlertConfigSvc pDefaultAlertConfigSvc,
      final NdsMaintenanceHistorySvc pNdsMaintenanceHistorySvc,
      final NDSClusterSamplingSvc pNdsClusterSamplingSvc,
      final DbCheckSvc pDbCheckSvc,
      final CriticalMaintenanceRunChunkJobStateSvc pCriticalMaintenanceRunChunkJobStateSvc,
      final NDSMaintenanceDateCalculationUtil pNDSMaintenanceDateCalculationUtil,
      final QueuedAdminActionDao pQueuedAdminActionDao,
      final SearchDeploymentSvc pSearchDeploymentSvc,
      final NDSCheckMetadataConsistencySvc pNDSCheckMetadataConsistencySvc,
      final VersionDeprecationSettingsSvc pVersionDeprecationSettingsSvc,
      final OrganizationSvc pOrganizationSvc,
      final NDSInternalMaintenanceRolloutSvc pNDSInternalMaintenanceRolloutSvc,
      final ElevatedHealthMonitoringSvc pElevatedHealthMonitoringSvc,
      final AZSelectionSvc pAZSelectionSvc,
      final ReplicaSetHardwareDao pReplicaSetHardwareDao,
      final ElevatedHealthMonitoringHealthCheckSvc pElevatedHealthMonitoringHealthCheckSvc,
      final ElevatedHealthMonitoringResultSvc pElevatedHealthMonitoringResultSvc,
      final NDSResourcePolicySvc pNDSResourcePolicySvc,
      final MongotuneCompatibilityValidationSvc pMongotuneCompatibilityValidationSvc,
      final MongotunePolicyInitializationSvc pMongotunePolicyInitializationSvc,
      final EolVersionUpgradeHistoryDao pEolVersionUpgradeHistoryDao,
      final IFRSvc pIfrSvc,
      final NDSComputeClusterMetricsSvc pNDSComputeClusterMetricsSvc) {
    _ndsGroupMaintenanceDao = pNDSGroupMaintenanceDao;
    _ndsGroupDao = pNDSGroupDao;
    _groupSvc = pGroupSvc;
    _ndsGroupSvc = pNDSGroupSvc;
    _appSettings = pAppSettings;
    _clusterDescriptionDao = pClusterDescriptionDao;
    _clusterDescriptionProcessArgsDao = pClusterDescriptionProcessArgsDao;
    _phasedVersionSvc = pPhasedVersionSvc;
    _clusterSvc = pClusterSvc;
    _auditSvc = pAuditSvc;
    _customMongoDbBuildSvc = pCustomMongoDbBuildSvc;
    _searchIndexConfigSvc = pSearchIndexConfigSvc;
    _informationalAlertSvc = pInformationalAlertSvc;
    _mtmClusterDao = pMtmClusterDao;
    _cpsSvc = pCpsSvc;
    _defaultAlertConfigSvc = pDefaultAlertConfigSvc;
    _ndsMaintenanceHistorySvc = pNdsMaintenanceHistorySvc;
    _ndsClusterSamplingSvc = pNdsClusterSamplingSvc;
    _dbCheckSvc = pDbCheckSvc;
    _criticalMaintenanceRunChunkJobStateSvc = pCriticalMaintenanceRunChunkJobStateSvc;
    _ndsMaintenanceDateCalculationUtil = pNDSMaintenanceDateCalculationUtil;
    _clock = Clock.systemUTC();
    _queuedAdminActionDao = pQueuedAdminActionDao;
    _searchDeploymentSvc = pSearchDeploymentSvc;
    _ndsCheckMetadataConsistencySvc = pNDSCheckMetadataConsistencySvc;
    _versionDeprecationSettingsSvc = pVersionDeprecationSettingsSvc;
    _organizationSvc = pOrganizationSvc;
    _ndsInternalMaintenanceRolloutSvc = pNDSInternalMaintenanceRolloutSvc;
    _elevatedHealthMonitoringSvc = pElevatedHealthMonitoringSvc;
    _azSelectionSvc = pAZSelectionSvc;
    _replicaSetHardwareDao = pReplicaSetHardwareDao;
    _elevatedHealthMonitoringHealthCheckSvc = pElevatedHealthMonitoringHealthCheckSvc;
    _elevatedHealthMonitoringResultSvc = pElevatedHealthMonitoringResultSvc;
    _ndsResourcePolicySvc = pNDSResourcePolicySvc;
    _mongotuneCompatibilityValidationSvc = pMongotuneCompatibilityValidationSvc;
    _mongotunePolicyInitializationSvc = pMongotunePolicyInitializationSvc;
    _eolVersionUpgradeHistoryDao = pEolVersionUpgradeHistoryDao;
    _ifrSvc = pIfrSvc;
    _ndsComputeClusterMetricsSvc = pNDSComputeClusterMetricsSvc;
  }

  public NDSGroupSvc getNDSGroupSvc() {
    return _ndsGroupSvc;
  }

  public Map<String, String> getMongoDBMajorVersionToFullVersionMapping() throws SvcException {
    final Map<SoftwareType, PhasedVersion> cache =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes();
    Map<String, String> majorVersionToFullVersionMapping =
        PhasedVersion.VALID_MONGODB_MAJOR_VERSIONS.entrySet().stream()
            .filter(e -> cache.containsKey(e.getValue()))
            .map(e -> Pair.of(e.getKey(), cache.get(e.getValue()).getTargetVersion()))
            .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));
    final String targetCDVersion =
        cache.get(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION).getTargetVersion();
    if (Version.fromString(targetCDVersion).versionHasThreeDigitsWithExtra()) {
      majorVersionToFullVersionMapping.put("CONTINUOUS_DELIVERY_MONGODB_VERSION", targetCDVersion);
    } else {
      if (PhasedVersion.getSoftwareType(targetCDVersion) == null) {
        throw new SvcException(NDSErrorCode.INVALID_MONGODB_VERSION_CONFIG);
      }
      majorVersionToFullVersionMapping.put(
          "CONTINUOUS_DELIVERY_MONGODB_VERSION",
          cache.get(PhasedVersion.getSoftwareType(targetCDVersion)).getTargetVersion());
    }
    return majorVersionToFullVersionMapping;
  }

  public String getVersionForCluster(
      final NDSGroup pNDSGroup,
      final SoftwareType pSoftwareType,
      final VersionUtils.VersionContainer pCurrentVersion,
      final VersionUtils.VersionContainer pMinimumVersion) {
    // Get the Fixed Agent Version if any
    final Optional<FixedAgentVersion> fixedAgentOpt =
        FixedAgentVersion.getFixedAgent(pNDSGroup, pSoftwareType);
    if (fixedAgentOpt.isPresent()) {
      return fixedAgentOpt.get().getVersion();
    }

    final PhasedVersion phasedVersion =
        _phasedVersionSvc
            .findBySoftwareType(pSoftwareType)
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        pSoftwareType.name() + " default version is not present"));

    if (pCurrentVersion == null) {
      return phasedVersion.getTargetVersion();
    }

    return getVersionForCluster(pNDSGroup, null, phasedVersion, pCurrentVersion, pMinimumVersion);
  }

  public boolean groupDoesNotHaveClusters(final NDSGroup pNDSGroup) {
    return _clusterDescriptionDao.countByGroup(pNDSGroup.getGroupId(), true) == 0;
  }

  public String getAgentVersionForGroup(
      final NDSGroup pNDSGroup,
      final SoftwareType pSoftwareType,
      final AgentVersion pCurrentVersion,
      final AgentVersion pMinimumVersion) {

    final Pair<String, MaintenanceRelease> agentTargetVersion =
        getAgentTargetVersion(pNDSGroup, pCurrentVersion, pSoftwareType, pMinimumVersion);

    if (pCurrentVersion == null) {
      return agentTargetVersion.getLeft();
    }

    final boolean agentOutDated = !pCurrentVersion.toString().equals(agentTargetVersion.getLeft());
    if (agentOutDated) {
      if (shouldStartMaintenanceForGroup(
          pNDSGroup,
          agentTargetVersion.getRight().getSchedulingBehavior(),
          pSoftwareType.name(),
          LOG)) {
        return agentTargetVersion.getLeft();
      }
    }

    return pCurrentVersion.toString();
  }

  public void generateMaintenanceStartedAuditsIfNecessary(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList) {
    final boolean needsMaintenance =
        pMaintenanceCheckResultList.stream().anyMatch(MaintenanceCheckResult::needMaintenance);

    final boolean allCriticalReleases =
        pMaintenanceCheckResultList.stream()
            .allMatch(
                maintenanceCheckState ->
                    maintenanceCheckState.needMaintenance()
                        && maintenanceCheckState.isCriticalRelease());

    final NDSGroupMaintenanceWindow maintenanceWindow = pNDSGroup.getMaintenanceWindow();

    if (!needsMaintenance
        || allCriticalReleases
        || maintenanceWindow.getMaintenanceStartedNotificationSent().isPresent()) {
      return;
    }

    // Generate audits for non-protected and protected hours maintenances
    final Map<Boolean, List<MaintenanceCheckResult>> partitionedCheckStates =
        pMaintenanceCheckResultList.stream()
            .filter(MaintenanceCheckResult::needMaintenance)
            .collect(
                Collectors.partitioningBy(
                    checkState ->
                        checkState.getMaintenanceRelease().getSchedulingBehavior()
                            == SchedulingBehavior.RESPECT_PROTECTED_HOURS));

    final List<MaintenanceCheckResult> nonProtectedHoursCheckStates =
        partitionedCheckStates.get(false);
    if (!nonProtectedHoursCheckStates.isEmpty()
        && shouldStartMaintenanceForGroup(
            pNDSGroup,
            pGroup,
            SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
            NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE.name(),
            LOG)) {
      emitMaintenanceCountMetrics(nonProtectedHoursCheckStates);
      saveProjectMaintenanceAudit(
          pNDSGroup,
          nonProtectedHoursCheckStates,
          NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE,
          false);
    }

    final List<MaintenanceCheckResult> resProtectedHoursMaintenanceCheckResults =
        partitionedCheckStates.get(true);
    if (!resProtectedHoursMaintenanceCheckResults.isEmpty()
        && shouldStartMaintenanceForGroup(
            pNDSGroup,
            SchedulingBehavior.RESPECT_PROTECTED_HOURS,
            NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE_OUTSIDE_OF_PROTECTED_HOURS.name(),
            LOG)) {
      saveProjectMaintenanceAudit(
          pNDSGroup,
          resProtectedHoursMaintenanceCheckResults,
          NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE_OUTSIDE_OF_PROTECTED_HOURS,
          true);
    }
  }

  private void saveProjectMaintenanceAudit(
      final NDSGroup pNDSGroup,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final NDSAudit.Type pAuditType,
      final boolean pIsHidden) {

    final List<MaintenanceCheckResult> relevantMaintenanceCheckStatesList =
        pMaintenanceCheckResultList.stream()
            .filter(maintenanceCheckState -> maintenanceCheckState.maintenanceType() != null)
            .filter(maintenanceCheckState -> !maintenanceCheckState.maintenanceType().isExternal())
            .filter(MaintenanceCheckResult::needMaintenance)
            .toList();

    final List<AuditDescription> auditDescriptions =
        Stream.concat(
                // Sort and collect project-level descriptions
                relevantMaintenanceCheckStatesList.stream()
                    .filter(state -> state.clusterDescription() == null)
                    .map(
                        state ->
                            new AuditDescription(
                                "All Clusters", state.maintenanceType().getDescription()))
                    .sorted(),
                // Sort and collect cluster-level descriptions
                relevantMaintenanceCheckStatesList.stream()
                    .filter(state -> state.clusterDescription() != null)
                    .map(
                        state ->
                            new AuditDescription(
                                state.clusterDescription().getName(),
                                state.maintenanceType().getDescription()))
                    .sorted())
            .toList();

    final NDSAudit.Builder auditBuilder = new NDSAudit.Builder(pAuditType);
    auditBuilder.groupId(pNDSGroup.getGroupId());
    auditBuilder.auditDescription(auditDescriptions);
    auditBuilder.hidden(pIsHidden);

    _auditSvc.saveAuditEvent(auditBuilder.build());
  }

  SimpleDateFormat getDateFormatter(final Group pGroup) {
    return getDateFormatter(getGroupUserMaintenanceTimeZoneId(pGroup));
  }

  private SimpleDateFormat getDateFormatter(final String pTimeZoneId) {
    final SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd 'at' hh:mm a z");
    dateFormatter.setTimeZone(TimeZone.getTimeZone(pTimeZoneId));
    return dateFormatter;
  }

  public void setStartASAP(final ObjectId pGroupId) {
    _ndsGroupMaintenanceDao.setStartASAP(pGroupId);
  }

  @VisibleForTesting
  public void setUserDefinedMaintenanceWindow(
      final ObjectId pGroupId,
      final NDSGroupMaintenanceWindow pNdsGroupMaintenanceWindow,
      final AuditInfo pAuditInfo)
      throws SvcException {
    final Group group =
        Optional.ofNullable(_groupSvc.findById(pGroupId))
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
    setUserDefinedMaintenanceWindow(group, pNdsGroupMaintenanceWindow, pAuditInfo);
  }

  public void setUserDefinedMaintenanceWindow(
      final Group pGroup,
      final NDSGroupMaintenanceWindow pNewNdsGroupMaintenanceWindow,
      final AuditInfo pAuditInfo)
      throws SvcException {
    final AtlasResourcePolicyAuthResponse authResponse =
        _ndsResourcePolicySvc.isProjectMaintenanceWindowRequestAuthorized(
            pGroup, pNewNdsGroupMaintenanceWindow.isUserDefined(), pAuditInfo);

    if (authResponse.decision().isDeny()) {
      throw new SvcException(
          NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED,
          _ndsResourcePolicySvc.getDeniedResourcePolicyAuthView(authResponse));
    }

    final ObjectId groupId = pGroup.getId();
    final String timeZoneId = getGroupUserMaintenanceTimeZoneId(pGroup);
    final NDSGroup ndsGroup =
        _ndsGroupMaintenanceDao
            .find(groupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    validateMaintenanceWindow(pNewNdsGroupMaintenanceWindow);
    validateMaintenanceWindowUpdate(ndsGroup, pNewNdsGroupMaintenanceWindow, timeZoneId);

    final NDSGroupMaintenanceWindow existingMaintenanceWindow = ndsGroup.getMaintenanceWindow();
    if (!pNewNdsGroupMaintenanceWindow.equalsOnUserDefinedParts(existingMaintenanceWindow)) {
      _ndsGroupMaintenanceDao.setUserDefinedMaintenanceWindow(
          groupId,
          pNewNdsGroupMaintenanceWindow.getDayOfWeek(),
          pNewNdsGroupMaintenanceWindow.getHourOfDay(),
          pNewNdsGroupMaintenanceWindow.getAutoDeferEnabled());

      getMaintenanceHistorySvc()
          .updateGroupMaintenanceHistoriesTargetMaintenanceDates(
              pNewNdsGroupMaintenanceWindow, pGroup, getNewDate());

      generateMaintenanceWindowAuditAndSetupDefaultAlerts(
          pGroup, pNewNdsGroupMaintenanceWindow, pAuditInfo, ndsGroup);
    }

    _ndsGroupMaintenanceDao.setProtectedHours(
        groupId, pNewNdsGroupMaintenanceWindow.getProtectedHours());
    generateAuditForProtectedHours(
        ndsGroup, pNewNdsGroupMaintenanceWindow.getProtectedHours(), pAuditInfo);
  }

  private void generateMaintenanceWindowAuditAndSetupDefaultAlerts(
      final Group pGroup,
      final NDSGroupMaintenanceWindow pNewNdsGroupMaintenanceWindow,
      final AuditInfo pAuditInfo,
      final NDSGroup ndsGroup) {
    final NDSAudit.Type auditType =
        ndsGroup.getMaintenanceWindow().isUserDefined()
            ? NDSAudit.Type.MAINTENANCE_WINDOW_MODIFIED
            : NDSAudit.Type.MAINTENANCE_WINDOW_ADDED;

    saveAuditEvent(
        ndsGroup,
        pAuditInfo,
        auditType,
        Collections.singletonList(
            new AuditDescription(
                "Maintenance window",
                String.format(
                    "%s on %ss",
                    pNewNdsGroupMaintenanceWindow.getHourOfDayText(),
                    pNewNdsGroupMaintenanceWindow.getDayOfWeekText()))));
    _defaultAlertConfigSvc.addMaintenanceWindowAlertConfigs(pGroup, pAuditInfo);
  }

  private void generateAuditForProtectedHours(
      final NDSGroup pNDSGroup,
      final MaintenanceProtectedHours pProtectedHours,
      final AuditInfo pAuditInfo) {

    if (pProtectedHours == null) {
      saveAuditEvent(
          pNDSGroup,
          pAuditInfo,
          NDSAudit.Type.MAINTENANCE_PROTECTED_HOURS_REMOVED,
          Collections.emptyList());
      return;
    }

    if (!hasValidHours(pProtectedHours)) {
      return;
    }

    final NDSAudit.Type auditType =
        pNDSGroup.getMaintenanceWindow().getProtectedHours().isUserDefined()
            ? NDSAudit.Type.MAINTENANCE_PROTECTED_HOURS_MODIFIED
            : NDSAudit.Type.MAINTENANCE_PROTECTED_HOURS_CREATED;

    saveAuditEvent(
        pNDSGroup,
        pAuditInfo,
        auditType,
        Collections.singletonList(
            new AuditDescription(
                "Maintenance protected hours",
                String.format(
                    "%s - %s",
                    NDSUtil.formatHourOfDayText(pProtectedHours.getStartHourOfDay()),
                    NDSUtil.formatHourOfDayText(pProtectedHours.getEndHourOfDay())))));
  }

  private boolean hasValidHours(final MaintenanceProtectedHours pHours) {
    return pHours.getStartHourOfDay() != null && pHours.getEndHourOfDay() != null;
  }

  private void saveAuditEvent(
      final NDSGroup pNDSGroup,
      final AuditInfo pAuditInfo,
      final NDSAudit.Type pAuditType,
      final List<AuditDescription> pAuditDescriptions) {
    final NDSAudit.Builder auditBuilder = new NDSAudit.Builder(pAuditType);
    auditBuilder.groupId(pNDSGroup.getGroupId());
    auditBuilder.auditInfo(pAuditInfo);
    auditBuilder.auditDescription(pAuditDescriptions);

    _auditSvc.saveAuditEvent(auditBuilder.build());
  }

  private void validateMaintenanceWindowUpdate(
      final NDSGroup pNDSGroup,
      final NDSGroupMaintenanceWindow pUpdatedWindow,
      final String pTimeZoneId)
      throws SvcException {
    final NDSGroupMaintenanceWindow existingWindow = pNDSGroup.getMaintenanceWindow();

    // if there is no existing user-defined window, then we don't need to validate the change
    // assuming the new window itself is valid
    if (!existingWindow.isUserDefined()) {
      return;
    }

    // only validate if the window is actually being updated
    if (!existingWindow.equalsOnUserDefinedParts(pUpdatedWindow)) {
      // maintenance window updates are not allowed after the advance notification has been sent
      // (protected hours changes are still ok)
      if (existingWindow.getAdvanceNotificationSendDate().isPresent()) {
        throw new SvcException(NDSErrorCode.MAINTENANCE_ALREADY_SCHEDULED);
      }

      // Make sure an update does not push the maintenance outside the period in which we
      // must apply a particular set of maintenance as specified by our compliance certifications
      // (e.g. OS security patches must be done in X days). This is currently set to 21 days to
      // correspond to the 2 allowed deferrals
      final Optional<Date> maintenancePerformByDate =
          pNDSGroup.getMaintenanceWindow().getMaintenancePerformedByDate();

      if (maintenancePerformByDate.isPresent()) {
        final Calendar performByDateAsCalendar =
            DateUtils.toCalendar(maintenancePerformByDate.get());

        final Calendar nextScheduledMaintenanceDate =
            getDateCalculationUtil()
                .getNextUserDefinedMaintenanceStartDateTime(pUpdatedWindow, pTimeZoneId)
                .orElseThrow(
                    () -> new SvcException(NDSErrorCode.INVALID_ARGUMENT, "isUserDefined"));

        if (nextScheduledMaintenanceDate.after(performByDateAsCalendar)) {
          throw new SvcException(
              NDSErrorCode.NON_COMPLIANT_MAINTENANCE_WINDOW,
              getDateFormatter(pTimeZoneId).format(performByDateAsCalendar.getTime()));
        }
      }
    }
  }

  public NDSGroupMaintenanceWindow getNDSGroupMaintenanceWindow(final NDSGroup pNDSGroup) {
    return pNDSGroup.getMaintenanceWindow();
  }

  public void startMaintenanceASAP(final ObjectId pGroupId, final AuditInfo pAuditInfo) {
    _ndsGroupMaintenanceDao.setStartASAP(pGroupId);
    final NDSAudit.Builder builder = new NDSAudit.Builder(NDSAudit.Type.MAINTENANCE_START_ASAP);
    builder.groupId(pGroupId);
    builder.auditInfo(pAuditInfo);
    _auditSvc.saveAuditEvent(builder.build());
  }

  public void scheduleMaintenanceForTheNextWindow(
      final ObjectId pGroupId, final AuditInfo pAuditInfo) throws SvcException {

    final NDSGroup ndsGroup =
        _ndsGroupMaintenanceDao
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    final NDSGroupMaintenanceWindow maintenanceWindow = ndsGroup.getMaintenanceWindow();

    if (maintenanceWindow.getAdvanceNotificationSendDate().isEmpty()) {
      // Schedule pending maintenance for the next window, even if there is not 72 hours notice
      // (fake the advancedNotificationSent date on the group.maintenance document)
      setInAdvancedNotificationSentForGroup(ndsGroup, getNewDate());
    }

    final NDSAudit.Builder builder =
        new NDSAudit.Builder(NDSAudit.Type.MAINTENANCE_SCHEDULED_FOR_NEXT_WINDOW);
    builder.groupId(ndsGroup.getGroupId());
    builder.auditInfo(pAuditInfo);
    _auditSvc.saveAuditEvent(builder.build());
  }

  public void setMaintenanceWindowForGroupForTest(
      final ObjectId pGroupId, final NDSGroupMaintenanceWindow pNDSGroupMaintenanceWindow)
      throws SvcException {
    if (pNDSGroupMaintenanceWindow.isUserDefined()) {
      _ndsGroupMaintenanceDao
          .find(pGroupId)
          .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
      validateMaintenanceWindow(pNDSGroupMaintenanceWindow);
      _ndsGroupMaintenanceDao.setUserDefinedMaintenanceWindow(
          pGroupId,
          pNDSGroupMaintenanceWindow.getDayOfWeek(),
          pNDSGroupMaintenanceWindow.getHourOfDay(),
          pNDSGroupMaintenanceWindow.getAutoDeferEnabled());
      _ndsGroupMaintenanceDao.setProtectedHours(
          pGroupId, pNDSGroupMaintenanceWindow.getProtectedHours());
    } else {
      _ndsGroupMaintenanceDao.overrideSystemMaintenanceWindowForTest(
          pGroupId, pNDSGroupMaintenanceWindow.getSystemHourOfDayOverride());
    }
  }

  public void resetMaintenanceWindowForGroup(final Group pGroup, final AuditInfo pInfo)
      throws SvcException {
    _ndsGroupMaintenanceDao
        .find(pGroup.getId())
        .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    final AtlasResourcePolicyAuthResponse authResponse =
        _ndsResourcePolicySvc.isProjectMaintenanceWindowRequestAuthorized(pGroup, false, pInfo);
    if (authResponse.decision().isDeny()) {
      throw new SvcException(
          NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED,
          _ndsResourcePolicySvc.getDeniedResourcePolicyAuthView(authResponse));
    }

    _ndsGroupMaintenanceDao.resetToSystemDefaultWindow(pGroup.getId());

    // create audit events for maintenance & maintenance pH
    final Collection<Event> auditEvents =
        Stream.of(
                createMaintenanceWindowRemoveAuditEvent(
                    pGroup.getId(), NDSAudit.Type.MAINTENANCE_WINDOW_REMOVED, pInfo),
                createMaintenanceWindowRemoveAuditEvent(
                    pGroup.getId(), NDSAudit.Type.MAINTENANCE_PROTECTED_HOURS_REMOVED, pInfo))
            .filter(Objects::nonNull)
            .collect(toList());

    _auditSvc.saveAuditEvents(auditEvents);
  }

  private NDSAudit createMaintenanceWindowRemoveAuditEvent(
      final ObjectId pGroupId, final NDSAudit.Type pAuditType, final AuditInfo pInfo) {
    final NDSAudit.Builder builder = new NDSAudit.Builder(pAuditType);
    builder.groupId(pGroupId);
    builder.auditInfo(pInfo);
    return builder.build();
  }

  public boolean isUpcomingMaintenanceDeferred(final NDSGroup pNDSGroup, final Group pGroup) {
    final Optional<Date> deferralRequestDate =
        pNDSGroup.getMaintenanceWindow().getDeferralRequestDate();

    if (!pNDSGroup.getMaintenanceWindow().isUserDefined() || deferralRequestDate.isEmpty()) {
      return false;
    }

    return isDeferringNextMaintenanceAllowedOnDate(pNDSGroup, pGroup, deferralRequestDate.get());
  }

  public void deferMaintenanceOneWeek(
      final ObjectId pGroupId, final AuditInfo pInfo, final Date pDateOfDeferralRequest)
      throws SvcException {
    final NDSGroup ndsGroup =
        _ndsGroupMaintenanceDao
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    final NDSGroupMaintenanceWindow window = ndsGroup.getMaintenanceWindow();

    if (!window.isUserDefined()) {
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED,
          "Cannot defer maintenance if project does not have a user defined maintenance window.");
    }

    if (window.getAdvanceNotificationSendDate().isEmpty()) {
      throw new SvcException(NDSErrorCode.MAINTENANCE_NOT_SCHEDULED);
    }

    if (window.getNumberOfDeferrals() == MAX_NUM_DEFERRALS) {
      throw new SvcException(NDSErrorCode.NUM_MAINTENANCE_DEFERRALS_EXCEEDED);
    }

    final Group group =
        Optional.ofNullable(_groupSvc.findById(pGroupId))
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    if (!isDeferringNextMaintenanceAllowedOnDate(ndsGroup, group, pDateOfDeferralRequest)) {
      throw new SvcException(NDSErrorCode.MAINTENANCE_DEFERRAL_NOT_ALLOWED);
    }

    _ndsGroupMaintenanceDao.deferMaintenanceOneWeekForGroup(
        pGroupId,
        window.getNumberOfDeferrals() + 1,
        pDateOfDeferralRequest,
        window.getAdvanceNotificationSendDate().orElse(null));

    final Date targetMaintenance = calculateTargetMaintenanceDateForDeferral(ndsGroup, new Date());
    getMaintenanceHistorySvc()
        .updateDeferredMaintenanceFields(pGroupId, pDateOfDeferralRequest, targetMaintenance);

    final NDSAudit.Builder builder = new NDSAudit.Builder(NDSAudit.Type.MAINTENANCE_DEFERRED);
    builder.groupId(pGroupId);
    builder.auditInfo(pInfo);
    _auditSvc.saveAuditEvent(builder.build());
  }

  private boolean isDeferringNextMaintenanceAllowedOnDate(
      final NDSGroup pNDSGroup, final Group pGroup, final Date pDeferralRequestDate) {
    final Calendar nextMaintenanceDate =
        getNextMaintenanceStartDateTime(
            pNDSGroup, pGroup, SchedulingBehavior.DURING_MAINTENANCE_WINDOW);

    // Maintenance for a given week can only be deferred in the 73 hours
    // (IN_ADVANCED_NOTIFICATION_HOURS + 1) preceding maintenance. That is in between when the
    // advance notification was sent and when maintenance begins.
    final Calendar advancedNotificationWindowStartDate = (Calendar) nextMaintenanceDate.clone();
    advancedNotificationWindowStartDate.add(
        Calendar.HOUR_OF_DAY, -1 * (IN_ADVANCED_NOTIFICATION_HOURS + 1));

    return pDeferralRequestDate.after(advancedNotificationWindowStartDate.getTime())
        && pDeferralRequestDate.before(nextMaintenanceDate.getTime());
  }

  public void autoDeferMaintenanceOneWeek(
      final NDSGroup pNDSGroup, final Group pGroup, final Logger pLogger) {
    if (!shouldAutoDefer(pNDSGroup.getMaintenanceWindow())) {
      return;
    }

    final Date deferralRequestDate = getNewDate();

    _ndsGroupMaintenanceDao.deferMaintenanceOneWeekForGroup(
        pNDSGroup.getGroupId(),
        1,
        deferralRequestDate,
        pNDSGroup.getMaintenanceWindowAdvancedNotificationSendDate().orElse(null));

    final Date targetMaintenanceDate =
        calculateTargetMaintenanceDateForDeferral(pNDSGroup, deferralRequestDate);

    getMaintenanceHistorySvc()
        .updateDeferredMaintenanceFields(
            pNDSGroup.getGroupId(), deferralRequestDate, targetMaintenanceDate);
    sendMaintenanceAutoDeferredNotificationEmail(pNDSGroup, pGroup, targetMaintenanceDate, pLogger);
  }

  public void grantExtraMaintenanceDeferral(
      final ObjectId pGroupId, final String pDeferralGrantJiraTicket, final AuditInfo pAuditInfo)
      throws SvcException {
    final NDSGroup group =
        _ndsGroupMaintenanceDao
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
    final NDSGroupMaintenanceWindow window = group.getMaintenanceWindow();
    final int currentNumberOfDeferrals = window.getNumberOfDeferrals();

    if (!window.isUserDefined()) {
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED,
          "Cannot grant extra deferral if project does not have a user defined maintenance"
              + " window.");
    }

    if (currentNumberOfDeferrals < MAX_NUM_DEFERRALS) {
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED,
          "Cannot grant extra deferral if project still has remaining deferrals.");
    }

    _ndsGroupMaintenanceDao.decrementNumberOfDeferralsForGroup(
        pGroupId, pDeferralGrantJiraTicket, currentNumberOfDeferrals - 1);

    createExtraMaintenanceDeferralGrantedAuditEvent(pGroupId, pAuditInfo);
  }

  void createExtraMaintenanceDeferralGrantedAuditEvent(
      final ObjectId pGroupId, final AuditInfo pInfo) {
    final NDSAudit.Builder builder =
        new NDSAudit.Builder(NDSAudit.Type.EXTRA_MAINTENANCE_DEFERRAL_GRANTED);
    builder.auditInfo(pInfo);
    builder.groupId(pGroupId);
    _auditSvc.saveAuditEvent(builder.build());
  }

  public void toggleAutoDeferEnabled(final ObjectId pGroupId, final AuditInfo pAuditInfo)
      throws SvcException {

    final NDSGroup ndsGroup =
        _ndsGroupMaintenanceDao
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
    final NDSGroupMaintenanceWindow window = ndsGroup.getMaintenanceWindow();

    if (!window.isUserDefined()) {
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED,
          "Cannot auto defer maintenance if project does not have a user defined maintenance"
              + " window.");
    }

    final boolean newAutoDeferEnabled = !window.getAutoDeferEnabled();

    _ndsGroupMaintenanceDao.setAutoDeferEnabled(pGroupId, newAutoDeferEnabled);
    final NDSAudit.Type auditType =
        newAutoDeferEnabled
            ? NDSAudit.Type.MAINTENANCE_AUTO_DEFER_ENABLED
            : NDSAudit.Type.MAINTENANCE_AUTO_DEFER_DISABLED;
    final NDSAudit.Builder builder = new NDSAudit.Builder(auditType);
    builder.groupId(pGroupId);
    builder.auditInfo(pAuditInfo);
    _auditSvc.saveAuditEvent(builder.build());
  }

  private void validateMaintenanceWindow(final NDSGroupMaintenanceWindow pNdsGroupMaintenanceWindow)
      throws SvcException {
    if (pNdsGroupMaintenanceWindow.isUserDefined()) {
      if (!NDSGroupMaintenanceWindow.DAY_OF_WEEK.containsKey(
          pNdsGroupMaintenanceWindow.getDayOfWeek())) {
        throw new SvcException(NDSErrorCode.INVALID_MAINTENANCE_WINDOW_DAY_OF_WEEK);
      }
      if (!NDSGroupMaintenanceWindow.HOUR_OF_DAY_RANGE.contains(
          pNdsGroupMaintenanceWindow.getHourOfDay())) {
        throw new SvcException(NDSErrorCode.INVALID_MAINTENANCE_WINDOW_HOUR_OF_DAY);
      }

      if (pNdsGroupMaintenanceWindow.getProtectedHours().isUserDefined()) {
        validateProtectedHours(pNdsGroupMaintenanceWindow.getProtectedHours());
      }
    } else {
      // protected hours can only be set if the maintenance window itself is user defined
      if (pNdsGroupMaintenanceWindow.getProtectedHours().isUserDefined()) {
        throw new SvcException(NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS);
      }
    }
  }

  private static final Duration MIN_PROTECTED_HOUR_WINDOW = Duration.ofHours(4);
  private static final Duration MAX_PROTECTED_HOUR_WINDOW = Duration.ofHours(18);

  @VisibleForTesting
  public void validateProtectedHours(final MaintenanceProtectedHours pProtectedHours)
      throws SvcException {
    final Duration protectedHourWindow = getProtectedHoursDuration(pProtectedHours);

    if (protectedHourWindow.compareTo(MIN_PROTECTED_HOUR_WINDOW) < 0) {
      throw new SvcException(
          NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS,
          "startHourOfDay, endHourOfDay",
          "Difference between start and end hours must be at least 4 hours");
    }

    if (protectedHourWindow.compareTo(MAX_PROTECTED_HOUR_WINDOW) > 0) {
      throw new SvcException(
          NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS,
          "startHourOfDay, endHourOfDay",
          "Difference between start and end hours cannot exceed 18 hours");
    }
  }

  private Duration getProtectedHoursDuration(final MaintenanceProtectedHours pProtectedHours)
      throws SvcException {
    final Integer startHourOfDay = pProtectedHours.getStartHourOfDay();
    final Integer endHourOfDay = pProtectedHours.getEndHourOfDay();

    if (isNotValidHourOfDay(startHourOfDay)) {
      throw new SvcException(
          NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS,
          "startHourOfDay",
          "Field not within valid range [0,23]");
    }

    if (isNotValidHourOfDay(endHourOfDay)) {
      throw new SvcException(
          NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS,
          "endHourOfDay",
          "Field not within valid range [0,23]");
    }

    // adjust for next day if window overlaps next day
    final Duration endHourOfDayDuration =
        Duration.ofHours(endHourOfDay >= startHourOfDay ? endHourOfDay : endHourOfDay + 24);
    final Duration startHourOfDayDuration = Duration.ofHours(startHourOfDay);
    return endHourOfDayDuration.minus(startHourOfDayDuration).abs();
  }

  private static boolean isNotValidHourOfDay(final Integer pHourOfDay) {
    return pHourOfDay == null || pHourOfDay < 0 || pHourOfDay > 23;
  }

  protected void setInAdvancedNotificationSentForGroup(
      final NDSGroup pNDSGroup, final Date pNotificationSentDate) {
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(
        pNDSGroup.getGroupId(), pNotificationSentDate);
  }

  protected void setMaintenanceStartedNotificationSentForGroup(
      final NDSGroup pNDSGroup, final Date pMaintenanceNotificationSentDate) {
    _ndsGroupMaintenanceDao.setMaintenanceStartedNotificationSentForGroup(
        pNDSGroup.getGroupId(), pMaintenanceNotificationSentDate);
  }

  public void resetMaintenanceAfterCompletionForGroup(
      final NDSGroup pNDSGroup, final Logger pLogger) {
    final Date currentWeekCompletedScheduledMaintenanceDate =
        Date.from(
            _ndsMaintenanceDateCalculationUtil
                .getMaintenanceStartDateTimeForCurrentWeek(
                    pNDSGroup, getGroupUserMaintenanceTimeZoneId(pNDSGroup))
                .toInstant());

    resetMaintenanceAfterCompletionForGroup(
        pNDSGroup, currentWeekCompletedScheduledMaintenanceDate, pLogger);
  }

  @VisibleForTesting
  void resetMaintenanceAfterCompletionForGroup(
      final NDSGroup pNDSGroup, final Date lastMaintenanceDate, final Logger pLogger) {
    pLogger.info(
        "Maintenance complete for group {}. Resetting maintenance document.",
        pNDSGroup.getGroupId());

    _ndsGroupMaintenanceDao.resetMaintenanceAfterCompletionForGroup(
        pNDSGroup, lastMaintenanceDate, getNewDate());
  }

  protected List<MaintenanceCheckResult> refreshBIConnectorVersion(
      final NDSGroup pNDSGroup,
      final List<Cluster> pClusters,
      final Date pNeedsPublishDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final AutomationConfig pAutomationConfig,
      final Logger pLogger) {
    final ObjectId groupId = pNDSGroup.getGroupId();
    final MaintenanceCheckResult maintenanceCheck =
        biConnectorMaintenanceCheck(pNDSGroup, pAutomationConfig, pLogger);
    final boolean needToPublishForBiConnector =
        maintenanceCheck.needMaintenance()
            && shouldStartMaintenanceForGroup(
                pNDSGroup,
                maintenanceCheck.getSchedulingBehavior(),
                MaintenanceType.BI_CONNECTOR_VERSION_UPDATED.name(),
                MaintenanceHistoryMatchCriteria.fromCheckResult(maintenanceCheck),
                pLogger);

    final List<ClusterDescription> targetClusters =
        pClusters.stream()
            .map(Cluster::getClusterDescription)
            .filter(c -> c.getMongoDBConfigType() == MongoDBConfigType.AUTOMATION)
            .filter(c -> c.getBiConnector().isEnabled())
            .toList();

    if (targetClusters.isEmpty()) {
      return Collections.emptyList();
    }

    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(SoftwareType.BI_CONNECTOR);

    final MaintenanceCheckResult.Builder updatedCheckResultBd =
        maintenanceCheck.toBuilder()
            .maintenanceType(MaintenanceType.BI_CONNECTOR_VERSION_UPDATED)
            .maintenanceRelease(
                new MaintenanceRelease(
                    groupId,
                    new ReleaseTimeslot(
                        phasedVersion.getCriticalReleaseDurationHours(),
                        phasedVersion.getReleaseDate()),
                    phasedVersion.getSchedulingBehavior()));

    if (needToPublishForBiConnector) {
      updatedCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(pNDSGroup.getGroupId(), updatedCheckResultBd.build(), LOG);
    }

    return targetClusters.stream()
        .peek(
            c -> {
              // BI Connector version upgrades are handled by
              // ProcessAutomationConfigPerClusterMove
              if (needToPublishForBiConnector) {
                _clusterDescriptionDao.setNeedsPublishDateForCluster(
                    c.getGroupId(), c.getName(), pNeedsPublishDate);
                if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
                  addMonitoringAndFetchBaselineData(
                      phasedVersion.getTargetVersion(),
                      MaintenanceType.BI_CONNECTOR_VERSION_UPDATED,
                      pNDSGroup,
                      c,
                      pNumMonitoringAddedMapMutable,
                      pLogger);
                }
              }
            })
        .map(c -> updatedCheckResultBd.build())
        .toList();
  }

  private Optional<MaintenanceCheckResult> refreshAgentVersions(
      final NDSGroup pNDSGroup,
      final Date pNeedsPublishDate,
      final AutomationAgentConfig pAutomationAgentConfig,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger) {
    return agentsMaintenanceCheck(pNDSGroup, pAutomationAgentConfig, pLogger)
        .map(
            agentMaintenanceCheck -> {
              final boolean agentsOutOfDate = agentMaintenanceCheck.needMaintenance();
              final PhasedVersion phasedVersion =
                  _phasedVersionSvc
                      .getCachedVersionForSoftwareTypes()
                      .get(SoftwareType.AUTOMATION_AGENT);
              final boolean shouldStartMaintenance =
                  shouldStartMaintenanceForGroup(
                      pNDSGroup,
                      agentMaintenanceCheck.getSchedulingBehavior(),
                      agentMaintenanceCheck.maintenanceType().name(),
                      pLogger);
              final boolean needCriticalReleaseForAgents =
                  agentsOutOfDate && shouldStartMaintenance;

              pLogger.trace(
                  "Refreshing agent versions, shouldStartMaintenance={}, agentsOutOfDate={},"
                      + " needCriticalReleaseForAgents={}",
                  shouldStartMaintenance,
                  agentsOutOfDate,
                  needCriticalReleaseForAgents);

              final MaintenanceCheckResult.Builder updatedCheckResultBd =
                  agentMaintenanceCheck.toBuilder();

              // Agent version upgrades are handled by the ProcessAutomationConfigPerGroupMove
              if (needCriticalReleaseForAgents) {
                updatedCheckResultBd.isStarted(true);
                setStartedDateOnPendingMaintenance(
                    pNDSGroup.getGroupId(), agentMaintenanceCheck, LOG);

                _ndsGroupMaintenanceDao.setNeedsPublishForGroup(
                    pNDSGroup.getGroupId(), pNeedsPublishDate);

                // add monitoring for group if EHM is enabled
                if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
                  addMonitoringForGroupIfEligible(
                      pNDSGroup,
                      pNumMonitoringAddedMapMutable,
                      phasedVersion.getTargetVersion(),
                      MaintenanceType.AUTOMATION_AGENT_VERSION_UPDATED,
                      pLogger);
                }
              }

              return updatedCheckResultBd.build();
            });
  }

  public List<MaintenanceCheckResult> checkExternalMaintenance(final NDSGroup pNDSGroup) {
    return pNDSGroup.getMaintenanceWindow().getExternalMaintenanceNames().stream()
        .map(
            externalMaintenanceName ->
                MaintenanceCheckResult.builder()
                    .maintenanceType(MaintenanceType.EXTERNAL_MAINTENANCE)
                    .needMaintenance(true)
                    .externalMaintenanceNames(List.of(externalMaintenanceName))
                    .maintenanceRelease(MAINTENANCE_WINDOW)
                    .build())
        .toList();
  }

  public List<MaintenanceCheckResult> refreshProjectSoftwareVersions(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<Cluster> pClusters,
      final Date pNeedsPublishDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger,
      final AutomationConfig pAutomationConfig) {
    // There is no automation config for groups that are free-only or no dedicated clusters yet
    // A group that has an automation config, M0s, but no dedicated clusters should still perform
    // the below refreshes
    if (pAutomationConfig == null || pClusters.isEmpty()) {
      return Collections.emptyList();
    }

    final Stream<MaintenanceCheckResult> refreshOSVersions =
        pClusters.stream()
            .map(
                cluster ->
                    refreshOSPolicyVersions(
                        pNDSGroup, pGroup, cluster, pNumMonitoringAddedMapMutable, pLogger));

    return Stream.of(
            refreshOSVersions,
            refreshAgentVersions(
                pNDSGroup,
                pNeedsPublishDate,
                pAutomationConfig.getDeployment().getAgentVersion(),
                pNumMonitoringAddedMapMutable,
                pLogger)
                .stream(),
            refreshBIConnectorVersion(
                pNDSGroup,
                pClusters,
                pNeedsPublishDate,
                pNumMonitoringAddedMapMutable,
                pAutomationConfig,
                pLogger)
                .stream(),
            Stream.of(
                refreshMongoshVersion(
                    pNDSGroup,
                    pNeedsPublishDate,
                    pNumMonitoringAddedMapMutable,
                    pAutomationConfig,
                    pLogger)))
        .flatMap(Function.identity())
        .toList();
  }

  public Optional<MaintenanceCheckResult> checkAndRunAZBalancingMaintenance(
      final Group pGroup, final NDSGroup pNDSGroup, final Cluster pCluster) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    final List<ReplicaSetHardware> replicaSetHardware = pCluster.getLiveReplicaSets();

    if (!clusterDescription.isDedicatedCluster()) {
      return Optional.empty();
    }

    final List<MaintenanceCheckResult> maintenanceResult = new ArrayList<>();
    for (final ReplicaSetHardware rsh : replicaSetHardware) {
      if (clusterDescription.getReplicationSpecById(rsh.getReplicationSpecId()).isEmpty()) {
        continue;
      }

      clusterDescription
          .getReplicationSpecById(rsh.getReplicationSpecId())
          .get()
          .getRegions()
          .stream()
          .flatMap(
              (regionName ->
                  Arrays.stream(NodeType.values())
                      .map(
                          nodeType ->
                              checkAndRunAZBalancingMaintenance(
                                  pGroup,
                                  pNDSGroup,
                                  clusterDescription,
                                  rsh,
                                  regionName,
                                  nodeType))))
          .filter(Optional::isPresent)
          .map(Optional::get)
          .findFirst()
          .ifPresent(maintenanceResult::add);
    }

    return maintenanceResult.stream().findFirst();
  }

  public Optional<MaintenanceCheckResult> checkAndRunAZBalancingMaintenance(
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final ClusterDescription pClusterDescription,
      final ReplicaSetHardware pReplicaSetHardware,
      final RegionName pRegionName,
      final NodeType pNodeType) {
    final boolean azBalancingFFEnabledForRegion;
    if (pRegionName instanceof AWSRegionName) {
      azBalancingFFEnabledForRegion =
          FeatureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.AWS_CAPACITY_AWARE_AZ_SELECTION, _appSettings, null, pGroup);
    } else if (pRegionName instanceof AzureRegionName) {
      azBalancingFFEnabledForRegion =
          FeatureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.AZURE_CAPACITY_AWARE_AZ_SELECTION, _appSettings, null, pGroup);
    } else if (pRegionName instanceof GCPRegionName) {
      azBalancingFFEnabledForRegion =
          FeatureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.GCP_CAPACITY_AWARE_AZ_SELECTION, _appSettings, null, pGroup);
    } else {
      azBalancingFFEnabledForRegion = false;
    }

    final ZoneDistributionStatus azStatus =
        _azSelectionSvc.getZoneDistributionStatus(
            pNDSGroup, pClusterDescription, pReplicaSetHardware, pRegionName, pNodeType);

    if (azStatus.status() != ZoneDistributionStatus.Status.IDEAL) {
      LOG.atInfo()
          .addKeyValue("clusterName", pClusterDescription.getName())
          .addKeyValue("groupId", pGroup.getId())
          .addKeyValue("regionName", pRegionName.getName())
          .addKeyValue("nodeType", pNodeType.name())
          .log(
              "A cluster's hardware AZ distribution was found to be {}, due to reason {}",
              azStatus.status(),
              azStatus.reason());
    }

    if (!azBalancingFFEnabledForRegion
        || azStatus.status() == ZoneDistributionStatus.Status.IDEAL
        || pClusterDescription.isDisaggregatedStorageSystem()) {
      return Optional.empty();
    }

    final SchedulingBehavior scheduledBehavior = SchedulingBehavior.DURING_MAINTENANCE_WINDOW;
    final MaintenanceCheckResult needsMaintenanceCheckResult =
        new MaintenanceCheckResult(
            MaintenanceType.AZ_BALANCING_REQUIRED,
            pClusterDescription,
            true,
            new MaintenanceRelease(pNDSGroup.getGroupId(), null, scheduledBehavior));

    final boolean inMaintenanceWindow =
        shouldStartMaintenanceForGroup(
            pNDSGroup,
            pGroup,
            SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
            MaintenanceType.AZ_BALANCING_REQUIRED.name(),
            MaintenanceHistoryMatchCriteria.builder()
                .setMaintenanceType(MaintenanceType.AZ_BALANCING_REQUIRED)
                .setClusterName(pClusterDescription.getName())
                .build(),
            LOG);

    if (!inMaintenanceWindow) {
      return Optional.of(needsMaintenanceCheckResult);
    }

    LOG.atInfo()
        .addKeyValue("clusterName", pClusterDescription.getName())
        .addKeyValue("groupId", pGroup.getId())
        .addKeyValue("regionName", pRegionName.getName())
        .addKeyValue("nodeType", pNodeType.name())
        .log("A cluster's hardware is eligible for AZ re-balancing, replacing non-ideal nodes");

    final MaintenanceCheckResult.Builder updatedNeedsMaintenanceCheckResultBd =
        needsMaintenanceCheckResult.toBuilder().isStarted(true);
    setStartedDateOnPendingMaintenance(
        pNDSGroup.getGroupId(), updatedNeedsMaintenanceCheckResultBd.build(), LOG);

    for (Optional<InstanceHardware> nodeNeedingMigration =
            _azSelectionSvc.getNextNodeNeedingZoneMigration(
                pNDSGroup, pClusterDescription, pReplicaSetHardware, pRegionName, pNodeType);
        nodeNeedingMigration.isPresent();
        nodeNeedingMigration =
            _azSelectionSvc.getNextNodeNeedingZoneMigration(
                pNDSGroup,
                pClusterDescription,
                _replicaSetHardwareDao
                    .findById(pReplicaSetHardware.getId())
                    .orElseThrow(
                        () ->
                            new IllegalStateException(
                                "ReplicaSetHardware was deleted while planning")),
                pRegionName,
                pNodeType)) {
      final boolean isInternalInstance =
          pReplicaSetHardware.isInstanceInternal(nodeNeedingMigration.get().getInstanceId());

      _replicaSetHardwareDao.setInstanceForceReplacement(
          pReplicaSetHardware.getId(),
          nodeNeedingMigration.get().getInstanceId(),
          isInternalInstance);
    }

    PromMetricsSvc.incrementCounter(
        AZ_MAINTENANCE_STARTED, scheduledBehavior.name(), azStatus.reason().name());
    return Optional.of(updatedNeedsMaintenanceCheckResultBd.build());
  }

  private Optional<MaintenanceCheckResult> agentsMaintenanceCheck(
      final NDSGroup pNDSGroup,
      final AutomationAgentConfig pAutomationAgentConfig,
      final Logger pLogger) {
    return Optional.ofNullable(pAutomationAgentConfig)
        .map(
            a -> {
              pLogger.trace("Running agent maintenance check with currentVersion={}", a);
              return a;
            })
        .map(AbstractAgentConfig::getVersion)
        .flatMap(
            currentAgentVersion -> {
              final Pair<String, MaintenanceRelease> targetAutomationAgentVersion =
                  getAgentTargetVersion(
                      pNDSGroup,
                      new AgentVersion(currentAgentVersion, AgentType.AUTOMATION),
                      SoftwareType.AUTOMATION_AGENT,
                      _appSettings.getAutomationAgentMinimumVersion());
              pLogger.trace("Target automation agent version {}", targetAutomationAgentVersion);
              final String targetVersion = targetAutomationAgentVersion.getLeft();
              final MaintenanceRelease targetMaintenanceRelease =
                  targetAutomationAgentVersion.getRight();

              return !currentAgentVersion.equals(targetVersion)
                  ? Optional.of(
                      MaintenanceCheckResult.builder(
                              MaintenanceType.AUTOMATION_AGENT_VERSION_UPDATED)
                          .needMaintenance(true)
                          .maintenanceRelease(targetMaintenanceRelease)
                          .fromVersion(currentAgentVersion)
                          .toVersion(targetVersion)
                          .build())
                  : Optional.empty();
            });
  }

  protected MaintenanceCheckResult biConnectorMaintenanceCheck(
      final NDSGroup pNDSGroup, final AutomationConfig pAutomationConfig, final Logger pLogger) {
    MaintenanceCheckResult biConnectorMaintenanceCheck =
        MaintenanceCheckResult.builder(MaintenanceType.BI_CONNECTOR_VERSION_UPDATED)
            .maintenanceRelease(MAINTENANCE_WINDOW)
            .build();
    final BiConnectorTemplate biConnectorTemplate = pAutomationConfig.getBiConnectorTemplate();

    if (biConnectorTemplate != null) {
      final Pair<String, MaintenanceRelease> targetBiConnectorVersion =
          getSoftwareTargetVersion(
              pNDSGroup,
              VersionUtils.parse(biConnectorTemplate.getVersion()),
              SoftwareType.BI_CONNECTOR,
              _appSettings.getBiConnectorMinimumVersion());

      if (!biConnectorTemplate.getVersion().equals(targetBiConnectorVersion.getLeft())) {
        biConnectorMaintenanceCheck =
            MaintenanceCheckResult.builder(MaintenanceType.BI_CONNECTOR_VERSION_UPDATED)
                .needMaintenance(true)
                .maintenanceRelease(targetBiConnectorVersion.getRight())
                .fromVersion(biConnectorTemplate.getVersion())
                .toVersion(targetBiConnectorVersion.getLeft())
                .build();
        if (!pAutomationConfig.getDeployment().getMongosqlds().isEmpty()) {
          pLogger.info(
              "BI Connector out of date. Needs upgrade to {}", targetBiConnectorVersion.getLeft());
        }
      }
    }

    return biConnectorMaintenanceCheck;
  }

  private void sendMongoDBVersionChangeStartedEvent(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pVersionFrom,
      final String pVersionTo) {

    final ItemDiff clusterDescriptionDiff =
        ItemDiff.forMongoDbVersion(FieldDefs.FIXED_MONGODB_VERSION, ItemDiff.Status.MODIFIED);

    clusterDescriptionDiff.addItem(
        FieldDefs.FIXED_MONGODB_VERSION, "MongoDB Version Fixed", pVersionFrom, pVersionTo);

    _clusterSvc.clusterChangesStarted(
        pGroupId, pClusterName, clusterDescriptionDiff, null, null, false);
  }

  private void sendIFRUpdateDecisionEvent(ObjectId experimentId, ObjectId clusterId, int wave) {
    _ifrSvc.saveEvent(
        new IFREvent(
            new ObjectId(),
            experimentId,
            clusterId,
            IFREventType.UPDATE_DECISION,
            wave,
            false,
            new Date()));
  }

  private void continuousDeliveryVersionChangeStarted(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pVersionFrom,
      final String pVersionTo) {

    final ItemDiff clusterDescriptionDiff =
        ItemDiff.forContinuousDeliveryFCV(
            ClusterDescription.FieldDefs.CONTINUOUS_DELIVERY_FCV, ItemDiff.Status.MODIFIED);

    clusterDescriptionDiff.addItem(
        ClusterDescription.FieldDefs.CONTINUOUS_DELIVERY_FCV,
        "Continuous Delivery FCV Updated",
        pVersionFrom,
        pVersionTo);

    _clusterSvc.clusterChangesStarted(
        pGroupId, pClusterName, clusterDescriptionDiff, null, null, false);
  }

  public MaintenanceCheckResult refreshMongotVersion(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger,
      final AutomationConfig pAutomationConfig) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    final boolean hasMongots =
        _searchIndexConfigSvc.isFTSEnabledForCluster(
                clusterDescription.getGroupId(), clusterDescription.getName())
            || _searchDeploymentSvc
                .getDeployedSearchTargetsForMongoCluster(
                    pCluster.getClusterDescription().getUniqueId())
                .contains(SearchDeploymentTargets.DEDICATED_NODES);

    if (!hasMongots) {
      return new MaintenanceCheckResult(
          MaintenanceType.MONGOT_VERSION_UPDATED, clusterDescription, false, MAINTENANCE_WINDOW);
    }

    // Only querying AutomationConfig after confirming FTS is enabled for cluster
    // since this will be the less common case generally.
    if (pAutomationConfig == null || pAutomationConfig.getMongotTemplate() == null) {
      return new MaintenanceCheckResult(
          MaintenanceType.MONGOT_VERSION_UPDATED, clusterDescription, false, MAINTENANCE_WINDOW);
    }

    final MongotVersion currentVersion =
        MongotVersion.parseBackwardsCompatible(pAutomationConfig.getMongotTemplate().getVersion());
    final Pair<String, MaintenanceRelease> targetMongotVersion =
        getSoftwareTargetVersion(
            pNDSGroup, currentVersion, SoftwareType.MONGOT, _appSettings.getMongotMinimumVersion());

    if (currentVersion.getVersion().equals(targetMongotVersion.getLeft())) {
      return new MaintenanceCheckResult(
          MaintenanceType.MONGOT_VERSION_UPDATED, clusterDescription, false, MAINTENANCE_WINDOW);
    }

    pLogger.debug(
        "Cluster {} needs mongot version upgraded from {} to {}",
        clusterDescription.getName(),
        pAutomationConfig.getMongotTemplate().getVersion(),
        targetMongotVersion.getLeft());

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        new MaintenanceCheckResult(
                MaintenanceType.MONGOT_VERSION_UPDATED,
                clusterDescription,
                true,
                targetMongotVersion.getRight(),
                currentVersion.getVersion(),
                targetMongotVersion.getLeft())
            .toBuilder();

    if (shouldStartMaintenanceForGroup(
        pNDSGroup,
        pGroup,
        targetMongotVersion.getRight().getSchedulingBehavior(),
        MaintenanceType.MONGOT_VERSION_UPDATED.name(),
        MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResultBd.build()),
        pLogger)) {
      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

      _clusterDescriptionDao.setNeedsPublishDateForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName(), pDate);

      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringAndFetchBaselineData(
            targetMongotVersion.getLeft(),
            MaintenanceType.MONGOT_VERSION_UPDATED,
            pNDSGroup,
            clusterDescription,
            pNumMonitoringAddedMapMutable,
            pLogger);
      }
    }

    return needsMaintenanceCheckResultBd.build();
  }

  public MaintenanceCheckResult refreshSearchEnvoyVersion(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger,
      final AutomationConfig pAutomationConfig) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    if (pAutomationConfig == null || pAutomationConfig.getSearchEnvoyTemplate() == null) {
      return new MaintenanceCheckResult(
          MaintenanceType.SEARCH_ENVOY_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    // Do not refresh Search Envoy for shared-tier and serverless clusters.
    // The Search Envoy for these clusters will be updated from the holder cluster.
    if (pCluster.getClusterDescription().isTenantCluster()) {
      return new MaintenanceCheckResult(
          MaintenanceType.SEARCH_ENVOY_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    final VersionUtils.Version currentVersion =
        Version.fromString(pAutomationConfig.getSearchEnvoyTemplate().getVersion());
    final Pair<String, MaintenanceRelease> targetSearchEnvoyVersion =
        getSoftwareTargetVersion(
            pNDSGroup,
            currentVersion,
            SoftwareType.SEARCH_ENVOY,
            new SearchAppSettings(_appSettings).getSearchEnvoyMinimumVersion());

    if (currentVersion.getVersion().equals(targetSearchEnvoyVersion.getLeft())) {
      return new MaintenanceCheckResult(
          MaintenanceType.SEARCH_ENVOY_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    pLogger.debug(
        "Cluster {} needs Search Envoy version upgraded from {} to {}",
        clusterDescription.getName(),
        pAutomationConfig.getSearchEnvoyTemplate().getVersion(),
        targetSearchEnvoyVersion.getLeft());

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        new MaintenanceCheckResult(
                MaintenanceType.SEARCH_ENVOY_VERSION_UPDATED,
                clusterDescription,
                true,
                targetSearchEnvoyVersion.getRight(),
                currentVersion.getVersion(),
                targetSearchEnvoyVersion.getLeft())
            .toBuilder();

    if (shouldStartMaintenanceForGroup(
        pNDSGroup,
        pGroup,
        targetSearchEnvoyVersion.getRight().getSchedulingBehavior(),
        MaintenanceType.SEARCH_ENVOY_VERSION_UPDATED.name(),
        MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResultBd.build()),
        pLogger)) {
      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

      _clusterDescriptionDao.setNeedsPublishDateForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName(), pDate);

      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringAndFetchBaselineData(
            targetSearchEnvoyVersion.getLeft(),
            MaintenanceType.SEARCH_ENVOY_VERSION_UPDATED,
            pNDSGroup,
            clusterDescription,
            pNumMonitoringAddedMapMutable,
            pLogger);
      }
    }

    return needsMaintenanceCheckResultBd.build();
  }

  public List<MaintenanceCheckResult> refreshMongotune(
      final NDSGroup ndsGroup,
      final Group group,
      final Cluster cluster,
      final Date date,
      final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable,
      final Logger logger,
      @Nullable final AutomationConfig automationConfig) {
    return List.of(
        refreshMongotuneVersion(
            ndsGroup, group, cluster, date, numMonitoringAddedMapMutable, logger, automationConfig),
        refreshMongotunePolicies(ndsGroup, group, cluster, date, logger));
  }

  @VisibleForTesting
  public MaintenanceCheckResult refreshMongotuneVersion(
      final NDSGroup ndsGroup,
      final Group group,
      final Cluster cluster,
      final Date date,
      final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable,
      final Logger logger,
      @Nullable final AutomationConfig automationConfig) {
    final ClusterDescription clusterDescription = cluster.getClusterDescription();
    final MaintenanceCheckResult.Builder maintenanceCheckResultBuilder =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.MONGOTUNE_VERSION_UPDATED)
            .clusterDescription(clusterDescription)
            .maintenanceRelease(MAINTENANCE_WINDOW);

    final boolean hasMongotuneEnabled =
        isFeatureFlagEnabled(FeatureFlag.ENABLE_MONGOTUNE, _appSettings, null, group);
    if (!hasMongotuneEnabled) {
      throw new IllegalStateException(
          "Attempted to call refreshMongotuneVersion on cluster "
              + clusterDescription.getName()
              + " with Mongotune disabled. This should not be possible.");
    }

    final Optional<AgentVersion> currentAgentVersionOpt =
        Optional.ofNullable(automationConfig)
            .flatMap(ac -> Optional.ofNullable(ac.getDeployment()))
            .flatMap(d -> Optional.ofNullable(d.getAgentVersion()))
            .map(av -> new AgentVersion(av.getVersion(), AgentType.AUTOMATION));

    if (!_mongotuneCompatibilityValidationSvc.isAgentVersionCompatible(currentAgentVersionOpt)) {
      logger.info(
          "Cluster {} use agent version {} that doesn't support mongotune. Skip mongotune version"
              + " upgrade.",
          clusterDescription.getName(),
          currentAgentVersionOpt);
      return maintenanceCheckResultBuilder.needMaintenance(false).build();
    }

    // In the first planning round after enabling the feature flag, MongotuneStatus may
    // not be present yet on the cluster description.
    final Optional<MongotuneStatus> existingMongotuneStatus =
        clusterDescription.getMongotuneStatus();

    final VersionUtils.Version currentVersion =
        existingMongotuneStatus
            .map(mongotuneStatus -> Version.fromString(mongotuneStatus.version()))
            .orElse(null);
    final Pair<String, MaintenanceRelease> targetMongotuneVersion =
        getSoftwareTargetVersion(
            ndsGroup,
            null,
            currentVersion,
            SoftwareType.MONGOTUNE,
            null,
            false // do not do critical release if current version is null
            );

    if (currentVersion != null
        && currentVersion.getVersion().equals(targetMongotuneVersion.getLeft())) {
      return maintenanceCheckResultBuilder.needMaintenance(false).build();
    }

    logger.debug(
        "Cluster {} needs mongotune version upgraded from {} to {}",
        clusterDescription.getName(),
        currentVersion,
        targetMongotuneVersion.getLeft());

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        maintenanceCheckResultBuilder
            .needMaintenance(true)
            .fromVersion(currentVersion != null ? currentVersion.getVersion() : null)
            .toVersion(targetMongotuneVersion.getLeft())
            .maintenanceRelease(targetMongotuneVersion.getRight());

    // If mongotune process is disabled, update the version to ensure automation config is correct
    // when the process is re-enabled.
    if (existingMongotuneStatus.map(status -> status.state().isDisabled()).orElse(false)) {
      _clusterSvc.updateMongotuneVersion(clusterDescription, targetMongotuneVersion.getLeft());
      return needsMaintenanceCheckResultBd.build();
    }

    final boolean shouldStartMaintenance =
        shouldStartMaintenanceForGroup(
            ndsGroup,
            group,
            targetMongotuneVersion.getRight().getSchedulingBehavior(),
            MaintenanceType.MONGOTUNE_VERSION_UPDATED.name(),
            logger);

    if (shouldStartMaintenance) {
      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          ndsGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), logger);

      // Leverage PACPCM instead of standalone UpdateClusterMaintainedMongotuneConfigMove to ensure
      // mongot disk monitor thresholds get updated as the disk policy is added.
      _clusterDescriptionDao.setNeedsPublishDateForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName(), date);

      _clusterSvc.updateMongotuneStatus(
          clusterDescription,
          targetMongotuneVersion.getLeft(),
          MongotuneStatus.State.ENABLE.name());

      // add EHM for mongotune version updates
      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringAndFetchBaselineData(
            targetMongotuneVersion.getLeft(),
            MaintenanceType.MONGOTUNE_VERSION_UPDATED,
            ndsGroup,
            clusterDescription,
            numMonitoringAddedMapMutable,
            logger);
      }
    }
    return needsMaintenanceCheckResultBd.build();
  }

  @VisibleForTesting
  public MaintenanceCheckResult refreshMongotunePolicies(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Date date,
      final Logger logger) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    final MaintenanceCheckResult.Builder maintenanceCheckResultBuilder =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.MONGOTUNE_POLICIES_UPDATED)
            .clusterDescription(clusterDescription)
            .maintenanceRelease(MAINTENANCE_WINDOW);

    final Optional<MongotuneStatus> mongotuneStatus = clusterDescription.getMongotuneStatus();

    // Mongotune binary is not enabled on this cluster, so no policies to refresh.
    if (mongotuneStatus.isEmpty()) {
      return maintenanceCheckResultBuilder.needMaintenance(false).build();
    }

    final MongotuneStatus mongotuneStatusValue = mongotuneStatus.get();

    // We track whether writes are blocked on the cluster description via log ingestion. As a
    // backstop to self-correct false positives, do a sanity check to ensure metadata remains
    // accurate by unsetting the flag if disk util < 90%.
    final Optional<Integer> minProvisionedDiskSize =
        pCluster.getReplicaSets().stream()
            .flatMap(ReplicaSetHardware::getAllHardware)
            .map(InstanceHardware::getDiskSizeGB)
            .flatMap(Optional::stream)
            .min(Double::compare);
    if (clusterDescription.isWriteBlocked() && minProvisionedDiskSize.isPresent()) {
      final Optional<Double> diskUtil =
          _ndsComputeClusterMetricsSvc.getMinFreeDiskSpaceForCluster(clusterDescription);

      // Check whether a write block override is present. If so, don't unset the write blocked flag.
      // Keep the check to the override, not specific fields on the override because it's
      // operator-set and not commonly expected.
      final Optional<ClusterDescriptionProcessArgs> processArgs =
          _clusterSvc.getProcessArgs(clusterDescription.getGroupId(), clusterDescription.getName());
      final boolean writeBlockOverridePresent =
          processArgs
              .flatMap(ClusterDescriptionProcessArgs::getMongotuneArg)
              .map(MongotuneProcessArgs::getWriteBlockOverrides)
              .isPresent();
      if (!writeBlockOverridePresent
          && diskUtil.isPresent()
          && (Units.BYTES.convertTo(diskUtil.get(), Units.GIGABYTES) / minProvisionedDiskSize.get())
              <= 0.9) {
        logger.info(
            "Cluster {} is write blocked but disk util is below 90% (disk util (GB){}, min"
                + " provisioned disk size (GB) {}). Unsetting write blocked flag.",
            Units.BYTES.convertTo(diskUtil.get(), Units.GIGABYTES),
            minProvisionedDiskSize.get(),
            clusterDescription.getName());
        _clusterSvc.updateMongotuneWriteBlockStatus(
            clusterDescription.getGroupId(), clusterDescription.getName(), false);
      }
    }

    final Map<PolicyType, PolicyStatus> existingPolicies =
        Optional.ofNullable(mongotuneStatusValue.policies()).orElse(Map.of());
    final Map<PolicyType, PolicyStatus> allPolicies =
        _mongotunePolicyInitializationSvc.getInitialMongotunePoliciesWithoutEligibleSet(
            pGroup, pNDSGroup);

    final Map<PolicyType, PolicyStatus> newPolicies =
        allPolicies.entrySet().stream()
            .filter(entry -> !existingPolicies.containsKey(entry.getKey()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    if (newPolicies.isEmpty()) {
      return maintenanceCheckResultBuilder.needMaintenance(false).build();
    }

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        maintenanceCheckResultBuilder.needMaintenance(true).maintenanceRelease(MAINTENANCE_WINDOW);

    logger.info(
        "Cluster {} requires new policies to be added. New policies: {},",
        clusterDescription.getName(),
        newPolicies.keySet());

    final boolean shouldStartMaintenance =
        shouldStartMaintenanceForGroup(
            pNDSGroup,
            pGroup,
            SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
            MaintenanceType.MONGOTUNE_POLICIES_UPDATED.name(),
            logger);

    final Map<PolicyType, PolicyStatus> updatedPolicies = new HashMap<>(existingPolicies);
    updatedPolicies.putAll(newPolicies);

    if (shouldStartMaintenance) {
      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), logger);
      _clusterSvc.updateMongotunePolicies(clusterDescription, updatedPolicies);
      // Trigger PACPCM rather than just UpdateClusterMaintainedMongotuneConfigMove to ensure
      // disk monitor thresholds on mongot get updated as well.
      _clusterDescriptionDao.setNeedsPublishDateForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName(), date);
      _auditSvc.saveAuditEvent(
          buildMongotunePoliciesUpdatedAuditEvent(
              pGroup, clusterDescription.getName(), existingPolicies, updatedPolicies));
    }
    return needsMaintenanceCheckResultBd.build();
  }

  private NDSAudit buildMongotunePoliciesUpdatedAuditEvent(
      final Group pGroup,
      final String clusterName,
      final Map<PolicyType, PolicyStatus> originalPolicies,
      final Map<PolicyType, PolicyStatus> updatedPolicies) {
    final NDSAudit.Builder builder = new NDSAudit.Builder(NDSAudit.Type.MONGOTUNE_POLICIES_UPDATED);
    builder.groupId(pGroup.getId());
    builder.hidden(true);

    final ItemDiffs diffs = new ItemDiffs();
    final ItemDiff mongotunePolicyDiff =
        ItemDiff.forClusterDescription(clusterName, ItemDiff.Status.MODIFIED);
    mongotunePolicyDiff.addItem(
        FieldDefs.MONGOTUNE,
        "Mongotune Policies",
        originalPolicies.toString(),
        updatedPolicies.toString());
    diffs.addDiff(mongotunePolicyDiff);
    builder.genericNDSItemDiff(diffs);

    builder.auditInfo(AuditInfoHelpers.fromSystem());
    return builder.build();
  }

  public MaintenanceCheckResult refreshAtlasProxyVersion(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger,
      final AutomationConfig pAutomationConfig) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    if (!clusterDescription.isMTM()) {
      return new MaintenanceCheckResult(
          MaintenanceType.ATLAS_PROXY_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    // Only querying AutomationConfig after confirming cluster is MTM
    // since this will be the less common case generally.
    // CLOUDP-89059: AutomationConfig can be null for serverless MTM clusters because
    //   they set it to MTM before its finished provisioning
    if (pAutomationConfig == null) {
      return new MaintenanceCheckResult(
          MaintenanceType.ATLAS_PROXY_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }
    if (pAutomationConfig.getAtlasProxyTemplate() == null) {
      return new MaintenanceCheckResult(
          MaintenanceType.ATLAS_PROXY_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    String currentBaseUrl = "";
    final Optional<String> currentBaseUrlOpt =
        pAutomationConfig.getAtlasProxyTemplate().getBaseUrl();
    if (currentBaseUrlOpt.isPresent()) {
      currentBaseUrl = currentBaseUrlOpt.get();
    }
    final Optional<FixedAgentVersion> fixedAgentOpt =
        FixedAgentVersion.getFixedAgent(pNDSGroup, SoftwareType.ATLAS_PROXY);
    String targetBaseUrl = "";
    if (fixedAgentOpt.isPresent()) {
      targetBaseUrl = fixedAgentOpt.get().getBaseUrl();
    }
    if (targetBaseUrl == null) {
      targetBaseUrl = "";
    }
    final boolean baseUrlEquals = targetBaseUrl.equals(currentBaseUrl);

    final VersionUtils.Version currentVersion =
        Version.fromString(pAutomationConfig.getAtlasProxyTemplate().getVersion());
    final Pair<String, MaintenanceRelease> targetAtlasProxyVersion =
        getSoftwareTargetVersion(
            pNDSGroup,
            currentVersion,
            SoftwareType.ATLAS_PROXY,
            _appSettings.getAtlasProxyMinimumVersion());

    boolean versionEquals = currentVersion.getVersion().equals(targetAtlasProxyVersion.getLeft());
    if (versionEquals && baseUrlEquals) {
      return new MaintenanceCheckResult(
          MaintenanceType.ATLAS_PROXY_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    if (versionEquals) {
      pLogger.debug(
          "Cluster {} needs Atlas Proxy base url changed from {} to {}",
          clusterDescription.getName(),
          currentBaseUrl,
          targetBaseUrl);
    } else {
      pLogger.debug(
          "Cluster {} needs Atlas Proxy version changed from {} to {}",
          clusterDescription.getName(),
          pAutomationConfig.getAtlasProxyTemplate().getVersion(),
          targetAtlasProxyVersion.getLeft());
    }

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        new MaintenanceCheckResult(
                MaintenanceType.ATLAS_PROXY_VERSION_UPDATED,
                clusterDescription,
                true,
                targetAtlasProxyVersion.getRight(),
                currentVersion.getVersion(),
                targetAtlasProxyVersion.getLeft())
            .toBuilder();

    if (!baseUrlEquals
        || shouldStartMaintenanceForGroup(
            pNDSGroup,
            pGroup,
            targetAtlasProxyVersion.getRight().getSchedulingBehavior(),
            MaintenanceType.ATLAS_PROXY_VERSION_UPDATED.name(),
            MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResultBd.build()),
            pLogger)) {
      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

      _clusterDescriptionDao.setNeedsPublishDateForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName(), pDate);

      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringAndFetchBaselineData(
            targetAtlasProxyVersion.getLeft(),
            MaintenanceType.ATLAS_PROXY_VERSION_UPDATED,
            pNDSGroup,
            clusterDescription,
            pNumMonitoringAddedMapMutable,
            pLogger);
      }
    }

    return needsMaintenanceCheckResultBd.build();
  }

  @VisibleForTesting
  protected void setTenantClustersToPlanASAP(
      final ClusterDescription pMTMClusterDescription, final Logger pLogger) {
    final List<MTMCluster> mtmClusters =
        _mtmClusterDao.findByName(
            pMTMClusterDescription.getGroupId(), pMTMClusterDescription.getName());

    if (mtmClusters.isEmpty()) {
      throw new IllegalStateException(
          "MTM Cluster "
              + pMTMClusterDescription.getName()
              + " with group ID "
              + pMTMClusterDescription.getGroupId()
              + " could not be found "
              + "by MTMClusterDao during critical maintenance update.");
    }

    mtmClusters.forEach(
        mtmCluster -> {
          final List<ObjectId> tenantGroupIds =
              _clusterDescriptionDao
                  .findClusterDescriptionIdsByUniqueIds(mtmCluster.getTenantClusterUniqueIds())
                  .entrySet()
                  .stream()
                  .map(
                      entry -> {
                        final Optional<ClusterDescriptionId> clusterDescriptionId =
                            entry.getValue();
                        if (clusterDescriptionId.isPresent()) {
                          return clusterDescriptionId.get().getGroupId();
                        } else {
                          pLogger.warn(
                              "Tenant Cluster with unique ID {} is returned by"
                                  + " tenantClusterUniqueIds() method of MTM Cluster {} but cluster"
                                  + " description ID cannot be found by ClusterDescriptionDao"
                                  + " during MTM Cluster critical  maintenance version update.",
                              entry.getKey(),
                              pMTMClusterDescription.getName());
                          return null;
                        }
                      })
                  .filter(Objects::nonNull)
                  .distinct() // gets rid of duplicate groups
                  .collect(toList());

          _ndsGroupDao.setPlanASAPForGroups(tenantGroupIds);
        });
  }

  public static boolean isInAllowedInstanceSizes(
      final ClusterDescription clusterDescription, final PhasedVersion phasedVersion) {
    final List<String> allowedInstanceSizes =
        phasedVersion
            .getPhasedReleaseCriteria()
            .flatMap(PhasedReleaseCriteria::getAllowedInstanceSizes)
            .orElse(List.of());
    if (!allowedInstanceSizes.isEmpty()) {
      return Stream.of(NodeType.values())
          .flatMap(pNodeType -> clusterDescription.getInstanceSizes(pNodeType).stream())
          .allMatch(instanceSize -> allowedInstanceSizes.contains(instanceSize.name()));
    }
    // if allowedInstanceSizes or phased release criteria is not specified return true
    return true;
  }

  final Optional<MongoDBEOLPhasedVersion> getMongoDBEOLPhasedVersionIfNeedsUpgrade(
      final Group group,
      final VersionUtils.Version currentClusterVersion,
      final String clusterName,
      final Logger logger) {
    final Optional<MongoDBEOLPhasedVersion> mongodbEOLPhasedVersion =
        _phasedVersionSvc.getMongoDBEOLPhasedVersion();
    final boolean clusterVersionIsDeprecated =
        _versionDeprecationSettingsSvc.isVersionDeprecated(currentClusterVersion);
    final boolean orgHasActiveMongoDBEolExtensionDate =
        _organizationSvc.hasActiveMongoDBEolExtensionDate(group.getOrgId());
    final boolean isValidVersionUpgrade =
        mongodbEOLPhasedVersion
            .map(
                eolVersion ->
                    VersionUtils.isValidVersionChange(
                        currentClusterVersion,
                        VersionUtils.parse(eolVersion.getEffectiveVersion())))
            .orElse(false);
    final boolean atlasAllowDeprecatedVersionsFFEnabled =
        isFeatureFlagEnabled(
            FeatureFlag.ATLAS_ALLOW_DEPRECATED_VERSIONS, _appSettings, null, group);

    final boolean shouldUpgradeFromEOLVersion =
        clusterVersionIsDeprecated
            && isValidVersionUpgrade
            && !orgHasActiveMongoDBEolExtensionDate
            && !atlasAllowDeprecatedVersionsFFEnabled;
    if (shouldUpgradeFromEOLVersion) {
      logger.info(
          "clusterName={} in groupId={} with mongodbVersion={} should be upgraded from EOL version."
              + " clusterVersionIsDeprecated={},isValidVersionUpgrade={},orgHasActiveMongoDBEolExtensionDate={},atlasAllowDeprecatedVersionsFFEnabled={}",
          clusterName,
          group.getId(),
          currentClusterVersion.getVersion(),
          clusterVersionIsDeprecated,
          isValidVersionUpgrade,
          orgHasActiveMongoDBEolExtensionDate,
          atlasAllowDeprecatedVersionsFFEnabled);
      return mongodbEOLPhasedVersion;
    }
    return Optional.empty();
  }

  boolean isProjectEligibleForMongoDbEolVersionUpgrade(final Group group) {
    return !_organizationSvc.hasActiveMongoDBEolExtensionDate(group.getOrgId())
        && !isFeatureFlagEnabled(
            FeatureFlag.ATLAS_ALLOW_DEPRECATED_VERSIONS, _appSettings, null, group);
  }

  boolean isSingleVersionUpgradeEnforcedForGroup(final Group group) {
    final Organization organization = _organizationSvc.findById(group.getOrgId());
    return isFeatureFlagEnabled(
        FeatureFlag.ATLAS_ENFORCE_EOL_SINGLE_VERSION_UPGRADE_FOR_ORG,
        _appSettings,
        organization,
        null);
  }

  Optional<TargetMongoDbEolVersion> getMongoDbEolVersionUpgradeTarget(
      final Group group,
      final ClusterDescription clusterDescription,
      final MongoDBEOLPhasedVersion eolPhasedVersion) {
    final Version currentVersion = clusterDescription.getMongoDBVersion();

    if (_versionDeprecationSettingsSvc.isVersionDeprecated(currentVersion)) {
      final Version selectedTargetVersion = eolPhasedVersion.getFirstTargetVersion();
      final UpgradeType upgradeType;
      final Version sequenceFinalVersion;
      if (isSingleVersionUpgradeEnforcedForGroup(group)
          || !eolPhasedVersion.isConsecutiveVersionUpgradeSupported()
          || clusterDescription.isFeatureCompatibilityVersionFixed()) {
        upgradeType = UpgradeType.SINGLE_VERSION;
        sequenceFinalVersion = null;
      } else {
        upgradeType = UpgradeType.CONSECUTIVE_VERSION;
        sequenceFinalVersion = eolPhasedVersion.getLastTargetVersion();
      }
      return Optional.of(
          new TargetMongoDbEolVersion(selectedTargetVersion, upgradeType, sequenceFinalVersion));
    } else if (eolPhasedVersion.isConsecutiveVersionUpgradeSupported()) {
      final int currentMajorVersion = currentVersion.getMajor();
      final Optional<EolVersionUpgradeHistory> latestHistoryOpt =
          _eolVersionUpgradeHistoryDao.findLatestForCluster(
              clusterDescription.getGroupId(), clusterDescription.getUniqueId());

      // ensure the latest history record indicates Atlas has completed the auto-upgrade to the
      // cluster's current major version and that this was part of a series of consecutive version
      // upgrades that has not yet reached the final target major version
      final boolean isMatchingHistory =
          latestHistoryOpt
              .map(
                  h ->
                      h.toMajorVersion() == currentMajorVersion
                          && h.status() == EolVersionUpgradeHistory.Status.COMPLETED
                          && h.upgradeType() == UpgradeType.CONSECUTIVE_VERSION
                          && h.sequenceFinalVersion() != null
                          && h.sequenceFinalMajorVersion() > currentMajorVersion)
              .orElse(false);

      if (isMatchingHistory) {
        final EolVersionUpgradeHistory matchingCompletedHistory = latestHistoryOpt.get();
        final Date completedDate = matchingCompletedHistory.completedDate();
        final boolean hasMonitoringPeriodPassed =
            completedDate != null
                && new Date()
                    .after(
                        DateUtils.addHours(
                            completedDate,
                            _appSettings.getMongodbEolVersionUpgradeMonitoringPeriodHours()));
        if (hasMonitoringPeriodPassed) {
          final Optional<Version> selectedTargetVersion =
              eolPhasedVersion.getTargetVersionForMajorVersion(currentMajorVersion + 1);
          if (selectedTargetVersion.isPresent()) {
            return Optional.of(
                new TargetMongoDbEolVersion(
                    selectedTargetVersion.get(),
                    UpgradeType.CONSECUTIVE_VERSION,
                    VersionUtils.parse(matchingCompletedHistory.sequenceFinalVersion())));
          }
        }
      }
    }
    return Optional.empty();
  }

  private Optional<TargetMongoDbEolVersion> getMongoDbEolVersionUpgradeTargetIfMaintenanceNeeded(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final ClusterDescription clusterDescription,
      @Nullable final MongoDBEOLPhasedVersion eolPhasedVersion,
      final Logger pLogger) {

    final boolean eolVersionUpgradeMaintenanceEnabled =
        isFeatureFlagEnabled(
            FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE, _appSettings, null, pGroup);

    if (!eolVersionUpgradeMaintenanceEnabled
        || !isProjectEligibleForMongoDbEolVersionUpgrade(pGroup)
        || clusterDescription.isMongoDBVersionFixed()
        || eolPhasedVersion == null
        || eolPhasedVersion.getReleaseMode() != ReleaseMode.PHASED
        || !isInAllowedInstanceSizes(clusterDescription, eolPhasedVersion)
        || !isInVersionReleaseCohort(pNDSGroup, eolPhasedVersion)) {
      return Optional.empty();
    }

    final Version currentVersion = clusterDescription.getMongoDBVersion();
    final Optional<TargetMongoDbEolVersion> targetVersionOpt =
        getMongoDbEolVersionUpgradeTarget(pGroup, clusterDescription, eolPhasedVersion);

    if (targetVersionOpt.isEmpty()
        || !VersionUtils.isValidVersionChange(currentVersion, targetVersionOpt.get().version)) {
      return Optional.empty();
    }

    // skip if maintenance for MONGODB_VERSION_UPDATED already in progress
    final Optional<NdsMaintenanceHistory> startedMaintenanceHistoryForConflictingMaintenance =
        _ndsMaintenanceHistorySvc.findStartedMaintenanceByGroupIdClusterNameAndType(
            pNDSGroup.getGroupId(),
            clusterDescription.getName(),
            MaintenanceType.MONGODB_VERSION_UPDATED);

    if (startedMaintenanceHistoryForConflictingMaintenance.isPresent()) {
      pLogger.info(
          "EOL version upgrade maintenance will not run for cluster {} in group {} because there is"
              + " a conflicting MONGODB_VERSION_UPDATED maintenance which is already in-progress",
          clusterDescription.getName(),
          pNDSGroup.getGroupId());
      return Optional.empty();
    }

    return targetVersionOpt;
  }

  public MaintenanceCheckResult refreshMongoDbEolVersions(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger) {

    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    final Version currentVersion = clusterDescription.getMongoDBVersion();

    final MaintenanceCheckResult.Builder maintenanceCheckResultBuilder =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.MONGODB_EOL_VERSION_UPGRADE)
            .clusterDescription(clusterDescription)
            .maintenanceRelease(MAINTENANCE_WINDOW);

    final Optional<MongoDBEOLPhasedVersion> eolPhasedVersionOpt =
        _phasedVersionSvc.getMongoDBEOLPhasedVersion();

    final Optional<TargetMongoDbEolVersion> targetVersionOpt =
        getMongoDbEolVersionUpgradeTargetIfMaintenanceNeeded(
            pNDSGroup, pGroup, clusterDescription, eolPhasedVersionOpt.orElse(null), pLogger);
    if (eolPhasedVersionOpt.isEmpty() || targetVersionOpt.isEmpty()) {
      return maintenanceCheckResultBuilder.needMaintenance(false).build();
    }

    final MongoDBEOLPhasedVersion eolPhasedVersion = eolPhasedVersionOpt.get();
    final TargetMongoDbEolVersion targetVersion = targetVersionOpt.get();

    final MaintenanceRelease maintenanceRelease =
        new MaintenanceRelease(
            pGroup.getId(),
            new ReleaseTimeslot(
                eolPhasedVersion.getCriticalReleaseDurationHours(),
                eolPhasedVersion.getReleaseDate()),
            eolPhasedVersion.getSchedulingBehavior());

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        maintenanceCheckResultBuilder
            .needMaintenance(true)
            .maintenanceRelease(maintenanceRelease)
            .fromVersion(currentVersion.getVersion())
            .toVersion(targetVersion.version.getVersion());

    final boolean shouldStartMaintenance =
        shouldStartMaintenanceForGroup(
            pNDSGroup,
            pGroup,
            eolPhasedVersion.getSchedulingBehavior(),
            MaintenanceType.MONGODB_EOL_VERSION_UPGRADE.name(),
            MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResultBd.build()),
            pLogger);

    if (shouldStartMaintenance) {
      pLogger.info(
          "Group {} is included in the {} deprecated version upgrade from {} to {} because it is"
              + " inside this release cohort, (project cohort: {}, release percentage: {})",
          clusterDescription.getGroupId(),
          eolPhasedVersion.getSoftwareType(),
          clusterDescription.getMongoDBVersion(),
          targetVersion.version.getVersion(),
          pNDSGroup.getReleaseCohorts().getMongodbVersionCohort(),
          eolPhasedVersion.getPhasedReleaseVersionPercent());

      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

      _eolVersionUpgradeHistoryDao.save(
          EolVersionUpgradeHistory.builder()
              .groupId(pNDSGroup.getGroupId())
              .orgId(pGroup.getOrgId())
              .clusterUniqueId(clusterDescription.getUniqueId())
              .clusterName(clusterDescription.getName())
              .status(Status.STARTED)
              .upgradeType(targetVersion.upgradeType)
              .fromVersion(currentVersion.getVersion())
              .toVersion(targetVersion.version.getVersion())
              .startedDate(new Date())
              .sequenceFinalVersion(
                  Optional.ofNullable(targetVersion.sequenceFinalVersion)
                      .map(Version::getVersion)
                      .orElse(null))
              .build());

      _clusterDescriptionDao.setNeedsPublishWithRestartForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName(), pDate);

      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringAndFetchBaselineData(
            eolPhasedVersion.getTargetVersion(),
            MaintenanceType.MONGODB_EOL_VERSION_UPGRADE,
            pNDSGroup,
            clusterDescription,
            pNumMonitoringAddedMapMutable,
            pLogger);
      }

      sendMongoDBVersionChangeStartedEvent(
          pNDSGroup.getGroupId(),
          clusterDescription.getName(),
          currentVersion.getVersion(),
          targetVersion.version.getVersion());

      updateMongoDBVersionForCluster(pNDSGroup, clusterDescription, targetVersion.version, pLogger);
    }

    return needsMaintenanceCheckResultBd.build();
  }

  public MaintenanceCheckResult refreshMongoDBVersions(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    final Version currentVersion =
        VersionUtils.parse(clusterDescription.getMongoDBVersion().getVersion());

    final TargetMongoDBVersion targetMongoDbVersion =
        getTargetMongoDbVersion(
            pNDSGroup, pGroup, pCluster, currentVersion, clusterDescription, pLogger);

    final Version targetVersion = targetMongoDbVersion.targetMongoDbVersion();
    final PhasedVersion phasedVersion = targetMongoDbVersion.phasedTargetVersion();

    final MaintenanceCheckResult.Builder maintenanceCheckResultBuilder =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
            .clusterDescription(clusterDescription)
            .maintenanceRelease(MAINTENANCE_WINDOW);

    if (isCustomBuild(phasedVersion, clusterDescription)
        || phasedVersion == null
        || clusterDescription.isMongoDBVersionFixed()
        || currentVersion.equals(targetVersion)) {
      return maintenanceCheckResultBuilder.needMaintenance(false).build();
    }

    if (isFeatureFlagEnabled(
        FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE, _appSettings, null, pGroup)) {
      // skip maintenance if conflicting maintenance MONGODB_EOL_VERSION_UPGRADE type is already in
      // progress for the cluster
      final Optional<NdsMaintenanceHistory> startedMaintenanceHistoryForConflictingMaintenance =
          _ndsMaintenanceHistorySvc.findStartedMaintenanceByGroupIdClusterNameAndType(
              pNDSGroup.getGroupId(),
              clusterDescription.getName(),
              MaintenanceType.MONGODB_EOL_VERSION_UPGRADE);
      if (startedMaintenanceHistoryForConflictingMaintenance.isPresent()) {
        pLogger.info(
            "Skipping MongoDB version upgrade maintenance for cluster {} in group {} because there"
                + " is a conflicting MONGODB_EOL_VERSION_UPGRADE maintenance which is already in"
                + " progress",
            clusterDescription.getName(),
            pNDSGroup.getGroupId());
        return maintenanceCheckResultBuilder.needMaintenance(false).build();
      }
    }

    if (phasedVersion.getReleaseMode() == ReleaseMode.IFR
        && phasedVersion.getIfrState().isPresent()) {
      sendIFRUpdateDecisionEvent(
          phasedVersion.getIfrState().get().experimentId(),
          clusterDescription.getUniqueId(),
          phasedVersion.getIfrState().get().wave());
    }

    final MaintenanceRelease maintenanceRelease =
        new MaintenanceRelease(
            pGroup.getId(),
            new ReleaseTimeslot(
                phasedVersion.getCriticalReleaseDurationHours(), phasedVersion.getReleaseDate()),
            phasedVersion.getSchedulingBehavior());

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        maintenanceCheckResultBuilder
            .needMaintenance(true)
            .maintenanceRelease(maintenanceRelease)
            .fromVersion(currentVersion.getVersion())
            .toVersion(targetVersion.getVersion());

    final boolean shouldStartMaintenance =
        shouldStartMaintenanceForGroup(
            pNDSGroup,
            pGroup,
            phasedVersion.getSchedulingBehavior(),
            MaintenanceType.MONGODB_VERSION_UPDATED.name(),
            MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResultBd.build()),
            pLogger);

    if (shouldStartMaintenance) {
      final boolean isPhasedVersionReleaseAllowed =
          isInAllowedInstanceSizes(clusterDescription, phasedVersion)
              && (isInVersionReleaseCohort(pNDSGroup, phasedVersion)
                  || targetMongoDbVersion.isTargetPreviousVersionAndSafeToRollout());

      if (phasedVersion.getReleaseMode() == ReleaseMode.PHASED && !isPhasedVersionReleaseAllowed) {
        return maintenanceCheckResultBuilder.needMaintenance(false).build();
      }

      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

      if (targetMongoDbVersion.isTargetPreviousVersionAndSafeToRollout()) {
        PromMetricsSvc.incrementCounter(
            PRIOR_TARGET_VERSION, MaintenanceType.MONGODB_VERSION_UPDATED.name());
      }

      pLogger.info(
          "Group {} is included in the {} version upgrade from {} to {} because {}, (project"
              + " cohort: {}, release percentage: {})",
          clusterDescription.getGroupId(),
          phasedVersion.getSoftwareType(),
          clusterDescription.getMongoDBVersion(),
          targetVersion.getVersion(),
          targetMongoDbVersion.isTargetPreviousVersionAndSafeToRollout()
              ? "it is a safe previous version outside of this release cohort"
              : "it is inside this release cohort",
          pNDSGroup.getReleaseCohorts().getMongodbVersionCohort(),
          phasedVersion.getPhasedReleaseVersionPercent());

      _clusterDescriptionDao.setNeedsPublishWithRestartForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName(), pDate);

      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringAndFetchBaselineData(
            phasedVersion.getTargetVersion(),
            MaintenanceType.MONGODB_VERSION_UPDATED,
            pNDSGroup,
            clusterDescription,
            pNumMonitoringAddedMapMutable,
            pLogger);
      }

      sendMongoDBVersionChangeStartedEvent(
          pNDSGroup.getGroupId(),
          clusterDescription.getName(),
          currentVersion.getVersion(),
          targetVersion.getVersion());

      updateMongoDBVersionForCluster(pNDSGroup, clusterDescription, targetVersion, pLogger);
    }

    return needsMaintenanceCheckResultBd.build();
  }

  private TargetMongoDBVersion getTargetMongoDbVersion(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster cluster,
      final Version currentVersion,
      final ClusterDescription clusterDescription,
      final Logger pLogger) {
    final boolean isEolVersionUpgradeMaintenanceEnabled =
        FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE, _appSettings, null, pGroup);

    final PhasedVersion phasedVersionForMajorVersion =
        getPhasedVersionForMajorVersion(
            clusterDescription.getMongoDBMajorVersion(),
            clusterDescription.getVersionReleaseSystem());

    if (isEolVersionUpgradeMaintenanceEnabled) {
      final Version targetVersion =
          getVersionForCluster(
              pNDSGroup,
              cluster,
              phasedVersionForMajorVersion,
              VersionUtils.parse(currentVersion.getVersion()));
      return new TargetMongoDBVersion(phasedVersionForMajorVersion, targetVersion);
    }

    final Optional<PhasedVersion> mongoDbEOLPhasedVersionIfNeedsUpgrade =
        getMongoDBEOLPhasedVersionIfNeedsUpgrade(
                pGroup, currentVersion, clusterDescription.getName(), pLogger)
            .map(PhasedVersion.class::cast);

    final PhasedVersion phasedVersion =
        mongoDbEOLPhasedVersionIfNeedsUpgrade.orElse(phasedVersionForMajorVersion);

    // This case is here to solely account for a forced upgrade from a deprecated MongoDB major
    // version by an Atlas admin. A user requested upgrade is processed immediately in
    // NDSClusterSvc::updateCluster
    final boolean isMajorVersionChange =
        !currentVersion.getMajorVersionString().equals(clusterDescription.getMongoDBMajorVersion());

    final Version targetVersion =
        isMajorVersionChange
            ? VersionUtils.parse(phasedVersion.getTargetVersion())
            : getVersionForCluster(
                pNDSGroup, cluster, phasedVersion, VersionUtils.parse(currentVersion.getVersion()));

    return new TargetMongoDBVersion(phasedVersion, targetVersion);
  }

  private void updateMongoDBVersionForCluster(
      final NDSGroup pNDSGroup,
      final ClusterDescription clusterDescription,
      final Version targetVersion,
      final Logger pLogger) {
    if (clusterDescription.isMTM()) {
      updateMongoDBVersionForMTM(pNDSGroup, clusterDescription, targetVersion, pLogger);
    } else {
      updateMongoDBVersionForNonMTM(pNDSGroup, clusterDescription, targetVersion);
    }
  }

  private void updateMongoDBVersionForMTM(
      final NDSGroup pNDSGroup,
      final ClusterDescription clusterDescription,
      final Version targetVersion,
      final Logger pLogger) {
    // Update the major version string values if needed
    if (clusterDescription.getVersionReleaseSystem() == VersionReleaseSystem.CONTINUOUS
        && !clusterDescription
            .getMongoDBMajorVersion()
            .equals(targetVersion.getMajorVersionString())) {
      _clusterSvc.updateMongoDBMajorVersion(
          pNDSGroup.getGroupId(),
          clusterDescription.getName(),
          targetVersion.getMajorVersionString());

      if (clusterDescription.isMTM()) {
        _mtmClusterDao.updateMongoDBMajorVersionForCluster(
            pNDSGroup.getGroupId(),
            clusterDescription.getName(),
            targetVersion.getMajorVersionString());
        _mtmClusterDao.setMongoDBMajorVersionChangeInProgress(
            pNDSGroup.getGroupId(), clusterDescription.getName(), true);
      }
    }

    _clusterSvc.updateMongoDBMajorAndFullVersions(clusterDescription, targetVersion);

    // for MTM clusters, set next planning date of all tenants to setPlanASAP so the version
    // information of the tenant clusters will be up-to-date
    if (clusterDescription.isMTM()
        && _criticalMaintenanceRunChunkJobStateSvc
            .isNDSCriticalMaintenanceTenantClusterUpdateEnabled()) {
      setTenantClustersToPlanASAP(clusterDescription, pLogger);
    }
  }

  private void updateMongoDBVersionForNonMTM(
      final NDSGroup pNDSGroup,
      final ClusterDescription clusterDescription,
      final Version targetVersion) {
    if (clusterDescription.getVersionReleaseSystem() == VersionReleaseSystem.CONTINUOUS
        && !clusterDescription
            .getMongoDBMajorVersion()
            .equals(targetVersion.getMajorVersionString())) {
      _clusterSvc.updateMongoDBMajorVersion(
          pNDSGroup.getGroupId(),
          clusterDescription.getName(),
          targetVersion.getMajorVersionString());
    }

    _clusterSvc.updateMongoDBMajorAndFullVersions(clusterDescription, targetVersion);
  }

  /**
   * WARNING: when adding new calls to this method, please ensure it uses
   * numMonitoringAddedMapMutable variable of updateMaintenanceCheckState in {@link
   * com.xgen.svc.nds.svc.planning.NDSPlanningSvc}
   */
  @VisibleForTesting
  protected boolean addMonitoringAndFetchBaselineData(
      final String pTargetVersion,
      final MaintenanceType pMaintenanceType,
      final NDSGroup pNDSGroup,
      final ClusterDescription pClusterDescription,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger) {
    final Optional<NDSInternalMaintenanceRollout> internalMaintenanceRolloutOptional =
        _ndsInternalMaintenanceRolloutSvc.findByToVersionAndMaintenanceType(
            pTargetVersion, pMaintenanceType);
    if (internalMaintenanceRolloutOptional.isEmpty()) {
      pLogger.warn(
          "No internal maintenance found for cluster {} that needs {} to {}.",
          pClusterDescription.getName(),
          pMaintenanceType.getDescription(),
          pTargetVersion);

      return false;
    }

    final Optional<ElevatedHealthMonitoring> ehmOpt =
        _elevatedHealthMonitoringSvc.findMostRecentByActionId(
            internalMaintenanceRolloutOptional.get().getActionId());
    if (ehmOpt.isEmpty()) {
      return false;
    }

    final ElevatedHealthMonitoring elevatedHealthMonitoring = ehmOpt.get();
    final AtomicInteger numMonitoringAdded =
        getNumMonitoringAddedFromMap(
            pNumMonitoringAddedMapMutable, elevatedHealthMonitoring.getActionId());

    return _elevatedHealthMonitoringSvc.addMonitoringDocsForClusterIfEligible(
        elevatedHealthMonitoring,
        pNDSGroup,
        pClusterDescription,
        () ->
            _elevatedHealthMonitoringHealthCheckSvc.getBaselineDataForCluster(
                pClusterDescription, elevatedHealthMonitoring),
        // TODO CLOUDP-327622: optimize usages of feature flag
        isAllowListFeatureFlagEnabled(pNDSGroup),
        numMonitoringAdded,
        pLogger);
  }

  private AtomicInteger getNumMonitoringAddedFromMap(
      final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable, final ObjectId actionId) {
    return numMonitoringAddedMapMutable.computeIfAbsent(actionId, k -> new AtomicInteger(0));
  }

  private boolean isAllowListFeatureFlagEnabled(final NDSGroup pNDSGroup) {
    return isFeatureFlagEnabled(
        FeatureFlag.EHM_ALLOW_LIST_ENABLED,
        _appSettings,
        null,
        _groupSvc.findById(pNDSGroup.getGroupId()));
  }

  @VisibleForTesting
  Optional<String> getRefreshedOsPolicyVersion(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final PhasedVersion pPhasedVersion,
      final ClusterDescription pClusterDescription) {
    final Optional<String> currentOSPolicyVersion = pClusterDescription.getOSPolicyVersion();

    // Check if this cluster is not a dedicated cluster
    // Check if current desired version (clusterDescription) == desired version from version
    // manager, then we will not refresh OS policy version
    if (!pClusterDescription.isDedicatedCluster()
        || (currentOSPolicyVersion.isPresent()
            && currentOSPolicyVersion.get().equals(pPhasedVersion.getEffectiveVersion()))) {
      return Optional.empty();
    }

    if (pPhasedVersion.getReleaseMode() != ReleaseMode.PHASED) {
      return Optional.of(pPhasedVersion.getEffectiveVersion());
    }

    final boolean shouldUsePreviousVersion =
        !isInVersionReleaseCohort(pNDSGroup, pPhasedVersion)
            && currentOSPolicyVersion.isPresent()
            && pPhasedVersion.isPreviousVersionSafe()
            && pPhasedVersion.getPreviousVersion().isPresent()
            && Integer.parseInt(pPhasedVersion.getPreviousVersion().get())
                > Integer.parseInt(currentOSPolicyVersion.get());

    if (shouldUsePreviousVersion) {
      return Optional.of(pPhasedVersion.getPreviousVersion().get());
    } else if (isInVersionReleaseCohort(pNDSGroup, pPhasedVersion)) {
      return Optional.of(pPhasedVersion.getEffectiveVersion());
    } else {
      return Optional.empty();
    }
  }

  public MaintenanceCheckResult refreshOSPolicyVersions(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    final Optional<String> currentOSPolicyVersion = clusterDescription.getOSPolicyVersion();
    final String clusterName = clusterDescription.getName();
    final ObjectId groupId = clusterDescription.getGroupId();
    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(SoftwareType.OS_POLICY);

    final MaintenanceRelease maintenanceRelease =
        new MaintenanceRelease(
            pGroup.getId(),
            new ReleaseTimeslot(
                phasedVersion.getCriticalReleaseDurationHours(), phasedVersion.getReleaseDate()),
            phasedVersion.getSchedulingBehavior());

    final Optional<String> refreshedOSPolicyVersion =
        getRefreshedOsPolicyVersion(pNDSGroup, pGroup, phasedVersion, clusterDescription);

    if (refreshedOSPolicyVersion.isPresent()) {
      final MaintenanceCheckResult.Builder needMaintenanceCheckResultBd =
          new MaintenanceCheckResult(
                  MaintenanceType.OS_POLICY_VERSION_UPDATED,
                  clusterDescription,
                  true,
                  maintenanceRelease,
                  currentOSPolicyVersion.orElse(null),
                  refreshedOSPolicyVersion.get())
              .toBuilder();

      // Check if we are in a maintenance window or OS policy release is critical release
      if (shouldStartMaintenanceForGroup(
          pNDSGroup,
          pGroup,
          phasedVersion.getSchedulingBehavior(),
          MaintenanceType.OS_POLICY_VERSION_UPDATED.name(),
          MaintenanceHistoryMatchCriteria.fromCheckResult(needMaintenanceCheckResultBd.build()),
          pLogger)) {
        needMaintenanceCheckResultBd.isStarted(true);
        setStartedDateOnPendingMaintenance(
            pNDSGroup.getGroupId(), needMaintenanceCheckResultBd.build(), pLogger);

        _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
            groupId,
            clusterName,
            refreshedOSPolicyVersion.get(),
            phasedVersion.isCriticalRelease());

        if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
          addMonitoringAndFetchBaselineData(
              phasedVersion.getTargetVersion(),
              MaintenanceType.OS_POLICY_VERSION_UPDATED,
              pNDSGroup,
              clusterDescription,
              pNumMonitoringAddedMapMutable,
              pLogger);
        }

        final boolean isPreviousSafeVersion =
            refreshedOSPolicyVersion.get().equals(phasedVersion.getPreviousVersion().orElse(null));

        pLogger.info(
            "Cluster {} in group {} successfully updated desired OS policy version to {} {}",
            clusterName,
            groupId,
            isPreviousSafeVersion ? "previous version" : "version",
            refreshedOSPolicyVersion.get());
      }

      return needMaintenanceCheckResultBd.build();
    } else {
      return new MaintenanceCheckResult(
          MaintenanceType.OS_POLICY_VERSION_UPDATED, clusterDescription, false, MAINTENANCE_WINDOW);
    }
  }

  public MaintenanceCheckResult refreshMongoDBParameters(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    final VersionUtils.Version currentVersion =
        VersionUtils.Version.fromString(clusterDescription.getMongoDBVersion().getVersion());

    final PhasedVersion phasedVersion =
        getPhasedVersionForMajorVersion(
            clusterDescription.getMongoDBMajorVersion(),
            clusterDescription.getVersionReleaseSystem());

    // Check for a custom version and return no maintenance is required if that is the case
    if (isCustomBuild(phasedVersion, clusterDescription)) {
      return new MaintenanceCheckResult(
          MaintenanceType.MONGODB_PARAMETERS_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    final VersionUtils.Version expectedVersion =
        getVersionForCluster(pNDSGroup, pCluster, phasedVersion, currentVersion);
    if (shouldStartMaintenanceForGroup(
        pNDSGroup,
        pGroup,
        phasedVersion.getSchedulingBehavior(),
        MaintenanceType.MONGODB_PARAMETERS_UPDATED.name(),
        pLogger)) {
      if (phasedVersion.getReleaseMode().equals(ReleaseMode.PHASED)
          && phasedVersion.hasMongoDBParameters()) {
        if (!isInParameterReleaseCohort(pNDSGroup, phasedVersion)) {
          pLogger.debug(
              "Group {} is not included in the MongoDB parameter update because it is"
                  + " outside this release cohort, (project cohort: {}, release percentage: {})",
              clusterDescription.getGroupId(),
              pNDSGroup.getReleaseCohorts().getMongodbParameterCohort(),
              phasedVersion
                  .getPhasedReleaseCriteria()
                  .map(PhasedReleaseCriteria::getParameterPercent)
                  .orElse(null));
          return new MaintenanceCheckResult(
              MaintenanceType.MONGODB_PARAMETERS_UPDATED,
              clusterDescription,
              false,
              MAINTENANCE_WINDOW);
        } else if (!isInAllowedInstanceSizes(clusterDescription, phasedVersion)) {
          pLogger.debug(
              "Cluster {} in group {} is not included in the MongoDB parameter update because it"
                  + " has an instance size not included in the allowedInstanceSize list,"
                  + " (allowedInstanceSizes: {})",
              clusterDescription.getName(),
              clusterDescription.getGroupId(),
              phasedVersion
                  .getPhasedReleaseCriteria()
                  .map(PhasedReleaseCriteria::getAllowedInstanceSizes)
                  .orElse(null));
          return new MaintenanceCheckResult(
              MaintenanceType.MONGODB_PARAMETERS_UPDATED,
              clusterDescription,
              false,
              MAINTENANCE_WINDOW);
        } else if (!currentVersion.equals(expectedVersion)) {
          pLogger.info(
              "Cluster {} not eligible for parameter updates: needs version {}, has {}",
              clusterDescription.getName(),
              expectedVersion,
              currentVersion);
          return new MaintenanceCheckResult(
              MaintenanceType.MONGODB_PARAMETERS_UPDATED,
              clusterDescription,
              false,
              MAINTENANCE_WINDOW);
        } else if (!Objects.equals(
            currentVersion.getVersion(), phasedVersion.getEffectiveVersion())) {
          pLogger.info(
              "Cluster {} not eligible for parameter updates: parameters are for version {}, has"
                  + " {}",
              clusterDescription.getName(),
              expectedVersion,
              phasedVersion.getEffectiveVersion());
          return new MaintenanceCheckResult(
              MaintenanceType.MONGODB_PARAMETERS_UPDATED,
              clusterDescription,
              false,
              MAINTENANCE_WINDOW);
        }

        final Optional<ClusterDescriptionProcessArgsUpdatable> clusterProcessArgsOpt =
            _clusterDescriptionProcessArgsDao.findForUpdate(
                clusterDescription.getName(),
                pGroup.getId(),
                ClusterDescriptionProcessArgs.Type.STANDARD);

        // Treat the lack of clusterProcessArgs specially. It is not an expected scenario in
        // production, but there are no strong guarantees the document will not be missing.
        if (clusterProcessArgsOpt.isEmpty()) {
          pLogger.info(
              "Cluster {} does not have a clusterDescriptionArgs document, skipping parameter "
                  + "updates",
              clusterDescription.getName());
          return new MaintenanceCheckResult(
              MaintenanceType.MONGODB_PARAMETERS_UPDATED,
              clusterDescription,
              false,
              MAINTENANCE_WINDOW);
        }

        final Optional<ProcessArguments2_6> currentArgsOpt =
            clusterProcessArgsOpt.flatMap(ClusterDescriptionProcessArgs::getPhasedVersionShardArg);

        final Optional<Map<String, Object>> setParametersOpt =
            phasedVersion
                .getPhasedReleaseCriteria()
                .flatMap(PhasedReleaseCriteria::getSetParameter);

        final ProcessArguments2_6 newProcessArgs = new ProcessArguments2_6();
        newProcessArgs.setSetParameterMap(setParametersOpt.orElse(new HashMap<>()));

        final ItemDiff processArgsDiff =
            new ProcessArguments2_6Comparable(
                    clusterDescription.getName(),
                    newProcessArgs,
                    currentArgsOpt.orElse(new ProcessArguments2_6()))
                .diff();

        if (!processArgsDiff.getItems().isEmpty()) {
          _clusterDescriptionDao.setNeedsPublishWithRestartForCluster(
              clusterDescription.getGroupId(), clusterDescription.getName(), pDate);

          final ClusterDescriptionProcessArgsUpdatable newClusterDescProcessArgs =
              clusterProcessArgsOpt.get().copy().setPhasedShardArgs(newProcessArgs).build();
          _clusterDescriptionProcessArgsDao.save(newClusterDescProcessArgs);
          pLogger.info(
              "Cluster {} will have new mongod parameters applied: {}",
              clusterDescription.getName(),
              processArgsDiff);

          final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
              MaintenanceCheckResult.builder()
                  .maintenanceType(MaintenanceType.MONGODB_PARAMETERS_UPDATED)
                  .clusterDescription(clusterDescription)
                  .needMaintenance(true)
                  .maintenanceRelease(
                      new MaintenanceRelease(
                          pGroup.getId(),
                          new ReleaseTimeslot(
                              phasedVersion.getCriticalReleaseDurationHours(),
                              phasedVersion.getReleaseDate()),
                          phasedVersion.getSchedulingBehavior()))
                  .processArgsDiff(processArgsDiff);

          needsMaintenanceCheckResultBd.isStarted(true);
          setStartedDateOnPendingMaintenance(
              pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

          if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
            addMonitoringAndFetchBaselineData(
                phasedVersion.getTargetVersion(),
                MaintenanceType.MONGODB_PARAMETERS_UPDATED,
                pNDSGroup,
                clusterDescription,
                pNumMonitoringAddedMapMutable,
                pLogger);
          }

          return needsMaintenanceCheckResultBd.build();
        }
      }
    }
    return new MaintenanceCheckResult(
        MaintenanceType.MONGODB_PARAMETERS_UPDATED, clusterDescription, false, MAINTENANCE_WINDOW);
  }

  public PhasedVersion getPhasedVersionForMajorVersion(
      final String pMongoDBMajorVersion, final VersionReleaseSystem pVersionReleaseSystem) {
    final SoftwareType softwareType =
        pVersionReleaseSystem == VersionReleaseSystem.CONTINUOUS
            ? SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION
            : PhasedVersion.getSoftwareType(pMongoDBMajorVersion);

    return _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(softwareType);
  }

  private boolean isCustomBuild(
      final PhasedVersion pPhasedVersion, final ClusterDescription pClusterDescription)
      throws IllegalStateException {
    // If no phased version is returned, check for a custom version and return no maintenance is
    // required if that is the case
    if (pPhasedVersion == null) {
      if (_customMongoDbBuildSvc
          .getCustomBuild(pClusterDescription.getMongoDBVersion().getVersion())
          .isEmpty()) {
        throw new IllegalStateException(
            "No phased version present and the specified Mongo DB version does not represent a"
                + " custom build.");
      }
      return true;
    }
    return false;
  }

  @VisibleForTesting
  protected void addMonitoringForGroupIfEligible(
      final NDSGroup pNDSGroup,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final String pTargetVersion,
      final MaintenanceType pMaintenanceType,
      final Logger pLogger) {
    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroup(pNDSGroup.getGroupId());

    for (final ClusterDescription clusterDescription : clusterDescriptions) {
      addMonitoringAndFetchBaselineData(
          pTargetVersion,
          pMaintenanceType,
          pNDSGroup,
          clusterDescription,
          pNumMonitoringAddedMapMutable,
          pLogger);
    }
  }

  public MaintenanceCheckResult refreshMongoDBFeatureCompatibilityVersion(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    final Optional<VersionUtils.Version> currentFCV =
        clusterDescription.getContinuousDeliveryFCV().map(Version::fromString);
    final VersionUtils.Version phasedVersionContinuousDeliveryMongoDBVersion =
        VersionUtils.parse(
            _phasedVersionSvc
                .getCachedVersionForSoftwareTypes()
                .get(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION)
                .getTargetVersion());
    final PhasedVersion phasedVersionContinuousDeliveryFCV =
        _phasedVersionSvc
            .getCachedVersionForSoftwareTypes()
            .get(SoftwareType.CONTINUOUS_DELIVERY_FCV);

    if (clusterDescription.isFeatureCompatibilityVersionFixed()) {
      pLogger.info(
          "Cluster {} in group {} is fixed to MongoDB FCV version {} with reason: {}. Skipping FCV "
              + "upgrade maintenance for the cluster.",
          clusterDescription.getName(),
          clusterDescription.getGroupId(),
          clusterDescription.getFeatureCompatibilityVersion(),
          clusterDescription.getFixedFeatureCompatibilityVersion().get().getReason());
      return new MaintenanceCheckResult(
          MaintenanceType.CONTINUOUS_DELIVERY_FCV_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    if (clusterDescription.getVersionReleaseSystem()
        == ClusterDescription.VersionReleaseSystem.CONTINUOUS) {
      // Do not update FCV iff:
      // * Phased CD mongodb major version does not match phased CD FCV
      // * or current cluster's FCV matches phased CD FCV
      if (!phasedVersionContinuousDeliveryFCV
              .getTargetVersion()
              .equals(phasedVersionContinuousDeliveryMongoDBVersion.getMajorVersionString())
          || (currentFCV.isPresent()
              && currentFCV
                  .get()
                  .getVersion()
                  .equals(phasedVersionContinuousDeliveryFCV.getTargetVersion()))) {
        return new MaintenanceCheckResult(
            MaintenanceType.CONTINUOUS_DELIVERY_FCV_UPDATED,
            clusterDescription,
            false,
            MAINTENANCE_WINDOW);
      }

      pLogger.debug(
          "Cluster {} needs MongoDB Continuous Delivery FCV upgraded from {} to {}",
          clusterDescription.getName(),
          currentFCV.map(Version::getVersion).orElse(""),
          phasedVersionContinuousDeliveryFCV.getTargetVersion());

      // Only need to publish on clusters backed by automation
      if (clusterDescription.getMongoDBConfigType() == MongoDBConfigType.AUTOMATION) {
        final VersionUtils.Version targetFCVVersion =
            VersionUtils.parse(phasedVersionContinuousDeliveryFCV.getTargetVersion());

        // getVersionForGroup takes care of handling paused status and cohort selection.
        final VersionUtils.Version effectiveFCVVersion =
            VersionUtils.parse(
                getVersionForCluster(
                    pNDSGroup,
                    pCluster,
                    phasedVersionContinuousDeliveryFCV,
                    // If there is no FCV set for the cluster (AIUI it is unlikely scenario in
                    // production) assume the target phased version as current version.
                    currentFCV.orElse(targetFCVVersion),
                    null));
        // If effective FCV is the existing version, it means the cluster has been denied by the
        // phased rollout selection logic.
        if (effectiveFCVVersion.equals(currentFCV.orElse(targetFCVVersion))) {
          return new MaintenanceCheckResult(
              MaintenanceType.CONTINUOUS_DELIVERY_FCV_UPDATED,
              clusterDescription,
              false,
              MAINTENANCE_WINDOW);
        }

        final MaintenanceCheckResult.Builder needMaintenanceCheckResultBd =
            new MaintenanceCheckResult(
                    MaintenanceType.CONTINUOUS_DELIVERY_FCV_UPDATED,
                    clusterDescription,
                    true,
                    new MaintenanceRelease(
                        pNDSGroup.getGroupId(),
                        new ReleaseTimeslot(
                            phasedVersionContinuousDeliveryFCV.getCriticalReleaseDurationHours(),
                            phasedVersionContinuousDeliveryFCV.getReleaseDate()),
                        phasedVersionContinuousDeliveryFCV.getSchedulingBehavior()),
                    currentFCV.map(Version::getVersion).orElse(""),
                    phasedVersionContinuousDeliveryFCV.getTargetVersion())
                .toBuilder();

        if (shouldStartMaintenanceForGroup(
            pNDSGroup,
            pGroup,
            phasedVersionContinuousDeliveryFCV.getSchedulingBehavior(),
            MaintenanceType.CONTINUOUS_DELIVERY_FCV_UPDATED.name(),
            MaintenanceHistoryMatchCriteria.fromCheckResult(needMaintenanceCheckResultBd.build()),
            pLogger)) {
          needMaintenanceCheckResultBd.isStarted(true);
          setStartedDateOnPendingMaintenance(
              pNDSGroup.getGroupId(), needMaintenanceCheckResultBd.build(), pLogger);

          _clusterDescriptionDao.setNeedsPublishWithRestartForCluster(
              clusterDescription.getGroupId(), clusterDescription.getName(), pDate);

          continuousDeliveryVersionChangeStarted(
              pNDSGroup.getGroupId(),
              clusterDescription.getName(),
              currentFCV.map(Version::getVersion).orElse(""),
              phasedVersionContinuousDeliveryFCV.getTargetVersion());

          _clusterSvc.updateContinuousDeliveryFCV(
              clusterDescription, phasedVersionContinuousDeliveryFCV.getTargetVersion());

          // add elevated health monitoring
          if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
            addMonitoringAndFetchBaselineData(
                phasedVersionContinuousDeliveryFCV.getTargetVersion(),
                MaintenanceType.CONTINUOUS_DELIVERY_FCV_UPDATED,
                pNDSGroup,
                clusterDescription,
                pNumMonitoringAddedMapMutable,
                pLogger);
          }
        }

        return needMaintenanceCheckResultBd.build();
      }
    }

    return new MaintenanceCheckResult(
        MaintenanceType.CONTINUOUS_DELIVERY_FCV_UPDATED,
        clusterDescription,
        false,
        MAINTENANCE_WINDOW);
  }

  private <V> MaintenanceCheckResult refreshTenantClusterDescriptionCosmeticValue(
      final Cluster pTenantCluster,
      final Map<String, ClusterDescription> pTenantToMtmMap,
      final MaintenanceType pMaintenanceType,
      final String pValueName,
      final Function<ClusterDescription, V> pGetValue,
      final BiConsumer<ClusterDescription, V> pUpdateValue,
      final Logger pLogger) {
    final ClusterDescription tenantClusterDesc = pTenantCluster.getClusterDescription();

    if (!pTenantToMtmMap.containsKey(tenantClusterDesc.getName())) {
      pLogger.debug("Tenant Cluster {} has no backing MTM Cluster", tenantClusterDesc.getName());
      return new MaintenanceCheckResult(
          pMaintenanceType, tenantClusterDesc, false, MAINTENANCE_WINDOW);
    }

    final ClusterDescription mtmClusterDesc = pTenantToMtmMap.get(tenantClusterDesc.getName());

    // The tenant and the MTM values are already same
    if (pGetValue.apply(tenantClusterDesc).equals(pGetValue.apply(mtmClusterDesc))) {
      return new MaintenanceCheckResult(
          pMaintenanceType, tenantClusterDesc, false, MAINTENANCE_WINDOW);
    }

    if (!tenantClusterDesc.getState().equals(ClusterDescription.State.IDLE)
        || !mtmClusterDesc.getState().equals(ClusterDescription.State.IDLE)) {
      pLogger.debug(
          "Tenant Cluster ({}) or Backing MTM ({}) has changes in progress",
          tenantClusterDesc.getName(),
          mtmClusterDesc.getName());
      return new MaintenanceCheckResult(
          pMaintenanceType, tenantClusterDesc, false, MAINTENANCE_WINDOW);
    }

    pLogger.debug(
        "Tenant Cluster {} {} is updating to {} to match its backing MTM version",
        tenantClusterDesc.getName(),
        pValueName,
        pGetValue.apply(mtmClusterDesc).toString());

    pUpdateValue.accept(tenantClusterDesc, pGetValue.apply(mtmClusterDesc));

    // No maintenance or setting needsMongoDBConfigPublishAfter is needed since
    // the tenant cluster's value we are updating is only cosmetic.
    return new MaintenanceCheckResult(
        pMaintenanceType, tenantClusterDesc, false, MAINTENANCE_WINDOW);
  }

  public MaintenanceCheckResult refreshTenantMongoDBVersions(
      final Cluster pTenantCluster,
      final Map<String, ClusterDescription> pTenantToMtmMap,
      final Logger pLogger) {
    return refreshTenantClusterDescriptionCosmeticValue(
        pTenantCluster,
        pTenantToMtmMap,
        MaintenanceType.MONGODB_VERSION_UPDATED,
        "mongoDB version",
        ClusterDescription::getMongoDBVersion,
        _clusterSvc::updateMongoDBMajorAndFullVersions,
        pLogger);
  }

  public MaintenanceCheckResult refreshTenantMongoDBFeatureCompatibilityVersion(
      final Cluster pTenantCluster,
      final Map<String, ClusterDescription> pTenantToMtmMap,
      final Logger pLogger) {

    return refreshTenantClusterDescriptionCosmeticValue(
        pTenantCluster,
        pTenantToMtmMap,
        MaintenanceType.CONTINUOUS_DELIVERY_FCV_UPDATED,
        "continuous delivery FCV",
        clusterDescription -> clusterDescription.getContinuousDeliveryFCV().orElse(""),
        _clusterSvc::updateContinuousDeliveryFCV,
        pLogger);
  }

  public MaintenanceCheckResult refreshMongoDbToolsVersions(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger,
      final AutomationConfig pAutomationConfig) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    if (!pCluster.getClusterDescription().isMTM()) {
      return new MaintenanceCheckResult(
          MaintenanceType.MONGODB_TOOLS_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    if (Optional.ofNullable(pAutomationConfig)
        .map(AutomationConfig::getMongoDbToolsTemplate)
        .isEmpty()) {
      return new MaintenanceCheckResult(
          MaintenanceType.MONGODB_TOOLS_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    final VersionUtils.Version currentVersion =
        Version.fromString(pAutomationConfig.getMongoDbToolsTemplate().getVersion());
    final String versionInAppSettings = _appSettings.getMongoDbToolsVersion();
    final Pair<String, MaintenanceRelease> targetVersion =
        getSoftwareTargetVersion(
            pNDSGroup,
            currentVersion,
            SoftwareType.MONGODB_TOOLS,
            VersionUtils.parse(versionInAppSettings));

    if (currentVersion.getVersion().equals(targetVersion.getLeft())) {
      return new MaintenanceCheckResult(
          MaintenanceType.MONGODB_TOOLS_VERSION_UPDATED,
          clusterDescription,
          false,
          MAINTENANCE_WINDOW);
    }

    pLogger.debug(
        "Cluster {} needs MongoDB Tools version upgraded from {} to {}",
        clusterDescription.getName(),
        currentVersion.getVersion(),
        targetVersion.getLeft());

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        new MaintenanceCheckResult(
                MaintenanceType.MONGODB_TOOLS_VERSION_UPDATED,
                clusterDescription,
                true,
                targetVersion.getRight(),
                currentVersion.getVersion(),
                targetVersion.getLeft())
            .toBuilder();

    if (shouldStartMaintenanceForGroup(
        pNDSGroup,
        pGroup,
        targetVersion.getRight().getSchedulingBehavior(),
        MaintenanceType.MONGODB_TOOLS_VERSION_UPDATED.name(),
        MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResultBd.build()),
        pLogger)) {
      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

      _clusterDescriptionDao.setNeedsPublishDateForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName(), pDate);

      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringAndFetchBaselineData(
            targetVersion.getLeft(),
            MaintenanceType.MONGODB_TOOLS_VERSION_UPDATED,
            pNDSGroup,
            clusterDescription,
            pNumMonitoringAddedMapMutable,
            pLogger);
      }
    }

    return needsMaintenanceCheckResultBd.build();
  }

  public MaintenanceCheckResult refreshMongoshVersion(
      final NDSGroup pNDSGroup,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final AutomationConfig pAutomationConfig,
      final Logger pLogger) {

    if (Optional.ofNullable(pAutomationConfig)
        .map(AutomationConfig::getMongoshTemplate)
        .isEmpty()) {
      return MaintenanceCheckResult.builder(MaintenanceType.MONGOSH_VERSION_UPDATED)
          .maintenanceRelease(MAINTENANCE_WINDOW)
          .build();
    }

    final String currentVersion = pAutomationConfig.getMongoshTemplate().getVersion();

    final Pair<String, MaintenanceRelease> targetVersionRelease =
        getSoftwareTargetVersion(
            pNDSGroup, VersionUtils.parse(currentVersion), SoftwareType.MONGOSH, null);
    final String targetVersion = targetVersionRelease.getLeft();
    final MaintenanceRelease maintenanceRelease = targetVersionRelease.getRight();

    if (currentVersion.equals(targetVersion)) {
      return MaintenanceCheckResult.builder(MaintenanceType.MONGOSH_VERSION_UPDATED)
          .maintenanceRelease(MAINTENANCE_WINDOW)
          .build();
    }

    pLogger.debug(
        "Project needs mongosh version upgraded from {} to {}", currentVersion, targetVersion);

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        MaintenanceCheckResult.builder(MaintenanceType.MONGOSH_VERSION_UPDATED)
            .needMaintenance(true)
            .maintenanceRelease(maintenanceRelease)
            .fromVersion(currentVersion)
            .toVersion(targetVersion);

    final boolean needsMaintenance =
        shouldStartMaintenanceForGroup(
            pNDSGroup,
            maintenanceRelease.getSchedulingBehavior(),
            MaintenanceType.MONGOSH_VERSION_UPDATED.name(),
            MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResultBd.build()),
            pLogger);

    if (needsMaintenance) {
      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

      _ndsGroupMaintenanceDao.setNeedsPublishForGroup(pNDSGroup.getGroupId(), pDate);

      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringForGroupIfEligible(
            pNDSGroup,
            pNumMonitoringAddedMapMutable,
            targetVersion,
            MaintenanceType.MONGOSH_VERSION_UPDATED,
            pLogger);
      }
    }

    return needsMaintenanceCheckResultBd.build();
  }

  public MaintenanceCheckResult refreshUserIdentityServiceVersion(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger,
      final AutomationConfig pAutomationConfig) {
    if (Optional.ofNullable(pAutomationConfig)
        .map(AutomationConfig::getAtlasUserIdentityServiceTemplate)
        .isEmpty()) {
      return new MaintenanceCheckResult(
          MaintenanceType.USER_IDENTITY_SERVICE_VERSION_UPDATED, false, MAINTENANCE_WINDOW);
    }

    final String currentVersion =
        pAutomationConfig.getAtlasUserIdentityServiceTemplate().getVersion();

    final Pair<String, MaintenanceRelease> targetVersion =
        getSoftwareTargetVersion(
            pNDSGroup,
            VersionUtils.parse(currentVersion),
            SoftwareType.USER_IDENTITY_SERVICE,
            null);

    if (currentVersion.equals(targetVersion.getLeft())) {
      return new MaintenanceCheckResult(
          MaintenanceType.USER_IDENTITY_SERVICE_VERSION_UPDATED, false, MAINTENANCE_WINDOW);
    }

    pLogger.debug(
        "Project needs User Identity Service version upgraded from {} to {}",
        currentVersion,
        targetVersion.getLeft());

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        new MaintenanceCheckResult(
                MaintenanceType.USER_IDENTITY_SERVICE_VERSION_UPDATED,
                null,
                true,
                targetVersion.getRight(),
                currentVersion,
                targetVersion.getLeft())
            .toBuilder();

    if (shouldStartMaintenanceForGroup(
        pNDSGroup,
        pGroup,
        targetVersion.getRight().getSchedulingBehavior(),
        MaintenanceType.USER_IDENTITY_SERVICE_VERSION_UPDATED.name(),
        MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResultBd.build()),
        pLogger)) {
      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

      _ndsGroupMaintenanceDao.setNeedsPublishForGroup(pNDSGroup.getGroupId(), pDate);
      pLogger.debug(
          "User Identity Service maintenance scheduled for group {}, isCritical={}",
          pNDSGroup.getGroupId(),
          targetVersion.getRight());

      // add monitoring for group if EHM is enabled
      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringForGroupIfEligible(
            pNDSGroup,
            pNumMonitoringAddedMapMutable,
            targetVersion.getLeft(),
            MaintenanceType.USER_IDENTITY_SERVICE_VERSION_UPDATED,
            pLogger);
      }
    }

    return needsMaintenanceCheckResultBd.build();
  }

  @VisibleForTesting
  boolean shouldRefreshOSPolicyVersionsForLoadBalancingDeployment(
      final NDSGroup pNDSGroup,
      final ServerlessLoadBalancingDeployment pLoadBalancingDeployment,
      final PhasedVersion pPhasedVersion) {
    // We are okay with updating OS policy version outside maintenance window for serverless
    // load balancing deployment
    final String currentDesiredOSPolicyVersion =
        pLoadBalancingDeployment.getDesiredEnvoyInstanceOSPolicyVersion();

    if (currentDesiredOSPolicyVersion != null
        && currentDesiredOSPolicyVersion.equals(pPhasedVersion.getEffectiveVersion())) {
      return false;
    }
    return pPhasedVersion.getReleaseMode() != ReleaseMode.PHASED
        || isInVersionReleaseCohort(pNDSGroup, pPhasedVersion);
  }

  public MaintenanceCheckResult refreshOSPolicyVersionsForLoadBalancingDeployment(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Logger pLogger,
      final ServerlessLoadBalancingDeploymentDao pServerlessLoadBalancingDeploymentDao) {
    final ObjectId ndsGroupId = pNDSGroup.getGroupId();
    boolean needMaintenance = false;

    final List<ServerlessLoadBalancingDeployment> loadBalancingDeployments =
        pServerlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(ndsGroupId);
    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(SoftwareType.OS_POLICY);
    final MaintenanceRelease maintenanceRelease =
        new MaintenanceRelease(
            pGroup.getId(),
            new ReleaseTimeslot(
                phasedVersion.getCriticalReleaseDurationHours(), phasedVersion.getReleaseDate()),
            phasedVersion.getSchedulingBehavior());

    String oldOSPolicyVersion = null;

    for (ServerlessLoadBalancingDeployment loadBalancingDeployment : loadBalancingDeployments) {
      if (shouldRefreshOSPolicyVersionsForLoadBalancingDeployment(
          pNDSGroup, loadBalancingDeployment, phasedVersion)) {
        oldOSPolicyVersion = loadBalancingDeployment.getDesiredEnvoyInstanceOSPolicyVersion();
        needMaintenance = true;
        final ObjectId deploymentId = loadBalancingDeployment.getId();
        pServerlessLoadBalancingDeploymentDao.setDesiredEnvoyInstanceOSPolicyVersion(
            deploymentId, phasedVersion.getEffectiveVersion());
        pLogger.info(
            "Serverless Load Balancing Deployment with id {} in group {} needs OS policy version"
                + "update to version {}",
            deploymentId,
            pGroup.getId(),
            phasedVersion.getEffectiveVersion());
      }
    }

    // Check if at least one load balancing deployment refreshed its desired OS policy version
    if (needMaintenance) {
      return new MaintenanceCheckResult(
          MaintenanceType.OS_POLICY_VERSION_UPDATED,
          null,
          needMaintenance,
          maintenanceRelease,
          oldOSPolicyVersion,
          phasedVersion.getEffectiveVersion());
    } else {
      return new MaintenanceCheckResult(
          MaintenanceType.OS_POLICY_VERSION_UPDATED, needMaintenance, MAINTENANCE_WINDOW);
    }
  }

  public static boolean shouldRefreshOSPolicyVersionsForVPCProxyInstance(
      final NDSGroup pNDSGroup,
      final VPCProxyInstanceDescription pProxyInstance,
      final PhasedVersion pPhasedVersion) {
    // VPC Proxy instances can update OS policy outside of MW.
    final String currentAppliedPolicyVersion = pProxyInstance.getAppliedOSPolicyVersion();
    if (currentAppliedPolicyVersion != null
        && currentAppliedPolicyVersion.equals(pPhasedVersion.getEffectiveVersion())) {
      return false;
    }

    return pPhasedVersion.getReleaseMode() != ReleaseMode.PHASED
        || isInVersionReleaseCohort(pNDSGroup, pPhasedVersion);
  }

  public MaintenanceCheckResult refreshServerlessProxyVersion(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Date pDate,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger,
      final AutomationConfig pAutomationConfig) {
    if (Optional.ofNullable(pAutomationConfig)
        .map(AutomationConfig::getMaintainedEnvoyTemplate)
        .isEmpty()) {
      return new MaintenanceCheckResult(
          MaintenanceType.SERVERLESS_PROXY_VERSION_UPDATED, false, MAINTENANCE_WINDOW);
    }

    final String currentVersion = pAutomationConfig.getMaintainedEnvoyTemplate().getVersion();

    final Pair<String, MaintenanceRelease> targetVersion =
        getSoftwareTargetVersion(
            pNDSGroup, VersionUtils.parse(currentVersion), SoftwareType.SERVERLESS_PROXY, null);

    if (currentVersion.equals(targetVersion.getLeft())) {
      return new MaintenanceCheckResult(
          MaintenanceType.SERVERLESS_PROXY_VERSION_UPDATED, false, MAINTENANCE_WINDOW);
    }

    pLogger.debug(
        "Project needs Serverless Proxy version upgraded from {} to {}",
        currentVersion,
        targetVersion.getLeft());

    final MaintenanceCheckResult.Builder needsMaintenanceCheckResultBd =
        new MaintenanceCheckResult(
                MaintenanceType.SERVERLESS_PROXY_VERSION_UPDATED,
                null,
                true,
                targetVersion.getRight(),
                currentVersion,
                targetVersion.getLeft())
            .toBuilder();

    if (shouldStartMaintenanceForGroup(
        pNDSGroup,
        pGroup,
        targetVersion.getRight().getSchedulingBehavior(),
        MaintenanceType.SERVERLESS_PROXY_VERSION_UPDATED.name(),
        MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResultBd.build()),
        pLogger)) {
      needsMaintenanceCheckResultBd.isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), needsMaintenanceCheckResultBd.build(), pLogger);

      _ndsGroupMaintenanceDao.setNeedsPublishForGroup(pNDSGroup.getGroupId(), pDate);
      pLogger.debug(
          "Serverless proxy maintenance scheduled for group {}, isCritical={}",
          pNDSGroup.getGroupId(),
          targetVersion.getRight());

      // add monitoring for group if EHM is enabled
      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        addMonitoringForGroupIfEligible(
            pNDSGroup,
            pNumMonitoringAddedMapMutable,
            targetVersion.getLeft(),
            MaintenanceType.SERVERLESS_PROXY_VERSION_UPDATED,
            pLogger);
      }
    }

    return needsMaintenanceCheckResultBd.build();
  }

  public List<MaintenanceCheckResult> refreshGroupExpiredFixedVersions(
      final NDSGroup pNDSGroup, final Group pGroup) {
    final List<MaintenanceCheckResult> expiredFixedVersionMaintenanceChecks = new ArrayList<>();

    pNDSGroup.getFixedAgentVersions().stream()
        .filter(FixedVersion::isExpired)
        .forEach(
            fixedAgentVersion -> {
              final MaintenanceType maintenanceType =
                  MaintenanceType.mapAgentTypeToUnfixMaintenanceType(
                      fixedAgentVersion.getAgentType());

              if (shouldStartMaintenanceForGroup(
                  pNDSGroup,
                  pGroup,
                  SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
                  maintenanceType.name(),
                  MaintenanceHistoryMatchCriteria.builder()
                      .setMaintenanceType(maintenanceType)
                      .build(),
                  LOG)) {
                final MaintenanceCheckResult.Builder notNeededMaintenanceCheckResultBd =
                    new MaintenanceCheckResult(maintenanceType, null, false, MAINTENANCE_WINDOW)
                        .toBuilder();

                notNeededMaintenanceCheckResultBd.isStarted(true);
                setStartedDateOnPendingMaintenance(
                    pNDSGroup.getGroupId(), notNeededMaintenanceCheckResultBd.build(), LOG);

                _ndsGroupSvc.unfixAgentVersion(
                    pNDSGroup.getGroupId(),
                    fixedAgentVersion.getAgentType(),
                    AuditInfoHelpers.fromSystem());

                expiredFixedVersionMaintenanceChecks.add(notNeededMaintenanceCheckResultBd.build());
              } else {
                expiredFixedVersionMaintenanceChecks.add(
                    new MaintenanceCheckResult(maintenanceType, null, true, MAINTENANCE_WINDOW));
              }
            });

    return expiredFixedVersionMaintenanceChecks;
  }

  public List<MaintenanceCheckResult> refreshClusterExpiredFixedVersions(
      final NDSGroup pNDSGroup, final Group pGroup, final Cluster pCluster, final Logger pLogger) {
    final List<MaintenanceCheckResult> expiredFixedVersionMaintenanceChecks = new ArrayList<>();
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    clusterDescription.getFixedVersionTypes().stream()
        .filter(type -> clusterDescription.getFixedVersionByType(type).get().isExpired())
        .forEach(
            type -> {
              final MaintenanceType maintenanceType =
                  MaintenanceType.mapClusterFixedVersionTypeToUnfixMaintenanceType(type);

              if (shouldStartMaintenanceForGroup(
                  pNDSGroup,
                  pGroup,
                  SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
                  maintenanceType.name(),
                  MaintenanceHistoryMatchCriteria.builder()
                      .setMaintenanceType(maintenanceType)
                      .setClusterName(clusterDescription.getName())
                      .build(),
                  pLogger)) {
                final MaintenanceCheckResult.Builder notNeededMaintenanceCheckResultBd =
                    new MaintenanceCheckResult(
                            maintenanceType, clusterDescription, false, MAINTENANCE_WINDOW)
                        .toBuilder();

                notNeededMaintenanceCheckResultBd.isStarted(true);
                setStartedDateOnPendingMaintenance(
                    pNDSGroup.getGroupId(), notNeededMaintenanceCheckResultBd.build(), pLogger);

                try {
                  _clusterSvc.unfixClusterVersion(
                      pNDSGroup.getGroupId(),
                      clusterDescription.getName(),
                      type,
                      AuditInfoHelpers.fromSystem());
                  expiredFixedVersionMaintenanceChecks.add(
                      notNeededMaintenanceCheckResultBd.build());
                } catch (SvcException e) {
                  pLogger.error(
                      "Encountered error unfixing version for type {} for cluster {} in group {}",
                      type.name(),
                      clusterDescription.getName(),
                      pNDSGroup.getGroupId(),
                      e);
                }
              } else {
                expiredFixedVersionMaintenanceChecks.add(
                    new MaintenanceCheckResult(
                        maintenanceType, clusterDescription, true, MAINTENANCE_WINDOW));
              }
            });

    return expiredFixedVersionMaintenanceChecks;
  }

  public List<MaintenanceCheckResult> rotateKmipMasterKey(
      final NDSGroup pNDSGroup, final Group pGroup) {
    final ObjectId groupId = pNDSGroup.getGroupId();
    final NDSEncryptionAtRest encryptionAtRest = pNDSGroup.getEncryptionAtRest();
    final Date eightyThreeDaysAgo =
        DateUtils.addDays(new Date(), -KMIP_MASTER_KEY_ROTATION_PERIOD_DAYS);
    final Map<ClusterDescription.EncryptionAtRestProvider, Date>
        encryptionAtRestProvidersNeedingPublishAndExpiration = new HashMap<>();

    final List<MaintenanceCheckResult.Builder> keyRotationUpdatedStateCheckResultBds =
        new ArrayList<>();

    if (encryptionAtRest.getAWSKMS().isEnabled()
        && encryptionAtRest
            .getAWSKMS()
            .getLastKmipMasterKeyRotation()
            .get()
            .before(eightyThreeDaysAgo)) {
      encryptionAtRestProvidersNeedingPublishAndExpiration.put(
          ClusterDescription.EncryptionAtRestProvider.AWS,
          encryptionAtRest.getAWSKMS().getLastKmipMasterKeyRotation().get());
    }
    if (encryptionAtRest.getAzureKeyVault().isEnabled()
        && encryptionAtRest
            .getAzureKeyVault()
            .getLastKmipMasterKeyRotation()
            .get()
            .before(eightyThreeDaysAgo)) {
      encryptionAtRestProvidersNeedingPublishAndExpiration.put(
          ClusterDescription.EncryptionAtRestProvider.AZURE,
          encryptionAtRest.getAzureKeyVault().getLastKmipMasterKeyRotation().get());
    }
    if (encryptionAtRest.getGoogleCloudKMS().isEnabled()
        && encryptionAtRest
            .getGoogleCloudKMS()
            .getLastKmipMasterKeyRotation()
            .get()
            .before(eightyThreeDaysAgo)) {
      encryptionAtRestProvidersNeedingPublishAndExpiration.put(
          ClusterDescription.EncryptionAtRestProvider.GCP,
          encryptionAtRest.getGoogleCloudKMS().getLastKmipMasterKeyRotation().get());
    }

    final boolean needsMaintenance =
        !encryptionAtRestProvidersNeedingPublishAndExpiration.isEmpty();

    final List<ClusterDescription> allClusterDescriptions =
        new ArrayList<>(_clusterSvc.getMergedClusterDescriptions(groupId));

    // add maintenance check state to list
    allClusterDescriptions.forEach(
        cd -> {
          final boolean requiresRotation =
              encryptionAtRestProvidersNeedingPublishAndExpiration.containsKey(
                  cd.getEncryptionAtRestProvider());

          keyRotationUpdatedStateCheckResultBds.add(
              new MaintenanceCheckResult(
                      MaintenanceType.KMIP_MASTER_KEY_ROTATION,
                      cd,
                      requiresRotation,
                      MaintenanceRelease.MAINTENANCE_WINDOW)
                  .toBuilder());
        });

    if (needsMaintenance) {
      final List<String> clusterNamesRequiringRotation =
          allClusterDescriptions.stream()
              .filter(
                  cd ->
                      encryptionAtRestProvidersNeedingPublishAndExpiration.containsKey(
                          cd.getEncryptionAtRestProvider()))
              .map(ClusterDescription::getName)
              .toList();
      LOG.info(
          "Group {} needs KMIP Keys rotated with cloud provider and expiration dates: {} for"
              + " encrypted clusters {}",
          groupId,
          encryptionAtRestProvidersNeedingPublishAndExpiration,
          clusterNamesRequiringRotation);

      if (shouldStartMaintenanceForGroup(
          pNDSGroup,
          pGroup,
          SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
          MaintenanceType.KMIP_MASTER_KEY_ROTATION.name(),
          MaintenanceHistoryMatchCriteria.fromType(MaintenanceType.KMIP_MASTER_KEY_ROTATION),
          LOG)) {
        keyRotationUpdatedStateCheckResultBds.forEach(
            mainCheckResult -> {
              mainCheckResult.isStarted(true);
              setStartedDateOnPendingMaintenance(
                  pNDSGroup.getGroupId(), mainCheckResult.build(), LOG);
            });

        _ndsGroupDao.touchEncryptionAtRestLastKmipMasterKeyRotation(
            groupId, encryptionAtRestProvidersNeedingPublishAndExpiration.keySet());
        _clusterSvc.setNeedsPublishWithRestartForClusters(groupId, clusterNamesRequiringRotation);
        _auditSvc.saveAuditEvent(buildKMIPKeyRotationAuditEvent(pGroup));
      }
    }
    return keyRotationUpdatedStateCheckResultBds.stream()
        .map(MaintenanceCheckResult.Builder::build)
        .toList();
  }

  private NDSAudit buildKMIPKeyRotationAuditEvent(final Group pGroup) {
    final NDSAudit.Builder builder =
        new NDSAudit.Builder(NDSAudit.Type.KMIP_KEY_ROTATION_SCHEDULED);
    builder.groupId(pGroup.getId());
    builder.hidden(false);
    builder.auditInfo(AuditInfoHelpers.fromSystem());
    return builder.build();
  }

  public MaintenanceCheckResult scheduleDbCheck(
      final NDSGroup pNDSGroup, final Group pGroup, final Cluster pCluster, final Logger pLogger) {
    return scheduleDbCheck(pNDSGroup, pGroup, pCluster, pLogger, false, false);
  }

  public List<MaintenanceCheckResult> reloadSslOnProcesses(
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMapMutable,
      final Logger pLogger) {
    final List<MaintenanceCheckResult> maintenanceCheckResults = new ArrayList<>();

    pCluster
        .getReplicaSets()
        .forEach(
            rsh ->
                rsh.getAllHardware()
                    .forEach(
                        h -> {
                          final boolean needMaintenance =
                              h.getReloadSslOnProcessesRequestedDate().isPresent();

                          final MaintenanceCheckResult.Builder maintenanceCheckResultBd =
                              new MaintenanceCheckResult(
                                      MaintenanceType.TLS_CERTIFICATE_ROTATED,
                                      pCluster.getClusterDescription(),
                                      needMaintenance,
                                      RESPECT_PROTECTED_HOURS,
                                      new InstanceHardwareDetails(
                                          h.getHostnameForAgents().orElse(""),
                                          h.getNextRestartOrRebootRequestedDate().orElse(null),
                                          List.of(),
                                          null,
                                          null))
                                  .toBuilder();

                          if (needMaintenance
                              && shouldStartMaintenanceForGroup(
                                  pNDSGroup,
                                  SchedulingBehavior.RESPECT_PROTECTED_HOURS,
                                  MaintenanceType.TLS_CERTIFICATE_ROTATED.name(),
                                  LOG)) {

                            maintenanceCheckResultBd.isStarted(true);
                            setStartedDateOnPendingMaintenance(
                                pNDSGroup.getGroupId(), maintenanceCheckResultBd.build(), LOG);
                            _clusterSvc.setNeedsReloadSslOnProcesses(rsh, h);

                            addMonitoringAndFetchBaselineData(
                                // TLS cert rotations do not have a target version
                                "",
                                MaintenanceType.TLS_CERTIFICATE_ROTATED,
                                pNDSGroup,
                                pCluster.getClusterDescription(),
                                pNumMonitoringAddedMapMutable,
                                pLogger);
                          }

                          maintenanceCheckResults.add(maintenanceCheckResultBd.build());
                        }));

    return maintenanceCheckResults;
  }

  /**
   * Calling this method with the ability to skip preflight checks (pSkipPreflightChecks = true)
   * should only be done from testing contexts. Otherwise, please use the public wrapper just above.
   */
  MaintenanceCheckResult scheduleDbCheck(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final Logger pLogger,
      final boolean pSkipPreflightCheck,
      final boolean pSkipAgentChecks) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    // If needsDbCheckAfter is unset or in the future, don't run dbCheck.
    if (clusterDescription.getNeedsDbCheckAfter() == null
        || clusterDescription.getNeedsDbCheckAfter().after(Date.from(_clock.instant()))) {
      return new MaintenanceCheckResult(
          MaintenanceType.DBCHECK_AUTOMATED, clusterDescription, false, MAINTENANCE_WINDOW);
    }

    final MaintenanceCheckResult needsMaintenanceCheckResult =
        new MaintenanceCheckResult(
            MaintenanceType.DBCHECK_AUTOMATED, clusterDescription, true, MAINTENANCE_WINDOW);

    if (!shouldStartMaintenanceForGroup(
        pNDSGroup,
        pGroup,
        SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
        MaintenanceType.DBCHECK_AUTOMATED.name(),
        MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResult),
        pLogger)) {
      return needsMaintenanceCheckResult;
    }

    try {
      _dbCheckSvc.requestDbCheck(
          clusterDescription.getUniqueId(),
          clusterDescription.getName(),
          pNDSGroup.getGroupId(),
          RunStatus.NEW,
          null,
          null,
          null,
          null,
          pSkipPreflightCheck,
          pSkipAgentChecks,
          CorruptionDetectionOperationOrigin.AUTOMATED,
          DbCheck.DEFAULT_REPLICATION_LAG_THRESHOLD,
          AuditInfoHelpers.fromSystem(),
          null);
      _ndsClusterSamplingSvc.setNeedsDbCheckAfter(
          clusterDescription.getGroupId(), clusterDescription.getName(), null);
      incrementCorruptionDetectionAutomatedScheduleCounter(
          ValidationType.DB_CHECK, CorruptionDetectionOperationOrigin.AUTOMATED);

      final MaintenanceCheckResult.Builder updatedNeedsMaintenanceCheckResultBd =
          needsMaintenanceCheckResult.toBuilder().isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), updatedNeedsMaintenanceCheckResultBd.build(), pLogger);

      return updatedNeedsMaintenanceCheckResultBd.build();
    } catch (final Exception pE) {
      final Date postponeDate;
      final CorruptionDetectionFailedInitiationState deferralType;
      final int newNumRetries =
          _ndsClusterSamplingSvc.incrementDbCheckPreflightRetryCount(clusterDescription);
      if (newNumRetries == 0) {
        // If retries was reset to 0, this means the max retry limit was hit.
        final int minDaysBetweenClusterValidations =
            _appSettings.getDbCheckMinDaysBetweenClusterValidations();
        postponeDate =
            Date.from(_clock.instant().plus(minDaysBetweenClusterValidations, ChronoUnit.DAYS));
        deferralType = CorruptionDetectionFailedInitiationState.FATAL;
      } else {
        postponeDate = Date.from(_clock.instant().plus(1, ChronoUnit.HOURS));
        deferralType = CorruptionDetectionFailedInitiationState.DEFERRED;
      }
      final NDSDbCheckEvent.Builder builder =
          new NDSDbCheckEvent.Builder(NDSDbCheckEvent.Type.DB_CHECK_DEFERRED_FOR_CLUSTER);
      builder.groupId(pNDSGroup.getGroupId());
      builder.clusterId(clusterDescription.getUniqueId());
      builder.clusterName(clusterDescription.getName());
      builder.hidden(true);

      _auditSvc.saveAuditEvent(builder.build());
      pLogger.warn(
          "Encountered error when automatically scheduling dbCheck. Postponing needsDbCheckAfter to"
              + " {}",
          postponeDate,
          pE);
      incrementCorruptionDetectionAutomatedScheduleFailedCounter(
          ValidationType.DB_CHECK, deferralType);

      _ndsClusterSamplingSvc.setNeedsDbCheckAfter(
          clusterDescription.getGroupId(), clusterDescription.getName(), postponeDate);

      return new MaintenanceCheckResult(
          MaintenanceType.DBCHECK_AUTOMATED, clusterDescription, false, MAINTENANCE_WINDOW);
    }
  }

  public MaintenanceCheckResult scheduleCheckMetadataConsistency(
      final NDSGroup pNDSGroup, final Group pGroup, final Cluster pCluster, final Logger pLogger) {
    final ShardedClusterDescription clusterDescription =
        (ShardedClusterDescription) pCluster.getClusterDescription();
    final CheckMetadataConsistency checkMetadataConsistency =
        clusterDescription.getCheckMetadataConsistency();

    final MaintenanceCheckResult.Builder maintenanceCheckStateBuilder =
        MaintenanceCheckResult.Builder.builder()
            .maintenanceType(MaintenanceType.CHECK_METADATA_CONSISTENCY_AUTOMATED)
            .clusterDescription(clusterDescription)
            .maintenanceRelease(MAINTENANCE_WINDOW);

    if (checkMetadataConsistency.needsCheckAfter() == null
        || checkMetadataConsistency.needsCheckAfter().after(Date.from(_clock.instant()))) {
      return maintenanceCheckStateBuilder.needMaintenance(false).build();
    }

    final MaintenanceCheckResult needsMaintenanceCheckResult =
        maintenanceCheckStateBuilder.needMaintenance(true).build();

    if (!shouldStartMaintenanceForGroup(
            pNDSGroup,
            pGroup,
            SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
            MaintenanceType.CHECK_METADATA_CONSISTENCY_AUTOMATED.name(),
            MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResult),
            pLogger)
        && !checkMetadataConsistency.bypassMaintenanceWindow()) {
      return maintenanceCheckStateBuilder.needMaintenance(true).build();
    }

    final Plan plan =
        CheckMetadataConsistencyMove.getNewPlan(
            clusterDescription.getGroupId(),
            clusterDescription.getName(),
            checkMetadataConsistency.operationOrigin());
    try {
      final MaintenanceCheckResult.Builder updatedNeedsMaintenanceCheckResultBd =
          needsMaintenanceCheckResult.toBuilder().isStarted(true);
      setStartedDateOnPendingMaintenance(
          pNDSGroup.getGroupId(), updatedNeedsMaintenanceCheckResultBd.build(), pLogger);

      _ndsCheckMetadataConsistencySvc.runCheckMetadataConsistencyFromPlan(
          plan, clusterDescription, pGroup);
      incrementCorruptionDetectionAutomatedScheduleCounter(
          ValidationType.CHECK_SHARD_METADATA_CONSISTENCY,
          checkMetadataConsistency.operationOrigin());
      return updatedNeedsMaintenanceCheckResultBd.build();
    } catch (final SvcException pE) {
      final Date postponeDate;
      final CorruptionDetectionFailedInitiationState deferralType;
      final int newNumRetries =
          _ndsClusterSamplingSvc.incrementCheckMetadataConsistencyPreflightRetryCount(
              clusterDescription);
      if (newNumRetries == 0) {
        // If retries was reset to 0, this means the max retry limit was hit.
        final int minDaysBetweenClusterValidations =
            _appSettings.getCheckMetadataConsistencyMinDaysBetweenClusterValidations();
        postponeDate =
            Date.from(_clock.instant().plus(minDaysBetweenClusterValidations, ChronoUnit.DAYS));
        deferralType = CorruptionDetectionFailedInitiationState.FATAL;
      } else {
        postponeDate = Date.from(_clock.instant().plus(1, ChronoUnit.HOURS));
        deferralType = CorruptionDetectionFailedInitiationState.DEFERRED;
      }

      final NDSCheckMetadataConsistencyEvent.Builder builder =
          new NDSCheckMetadataConsistencyEvent.Builder(
              NDSCheckMetadataConsistencyEvent.Type
                  .CHECK_METADATA_CONSISTENCY_DEFERRED_FOR_CLUSTER);
      builder.groupId(pNDSGroup.getGroupId());
      builder.clusterId(clusterDescription.getUniqueId());
      builder.clusterName(clusterDescription.getName());
      builder.hidden(true);
      _auditSvc.saveAuditEvent(builder.build());

      pLogger.warn(
          "Encountered error when automatically scheduling checkMetadataConsistency. Postponing"
              + " checkMetadataConsistency to {}",
          postponeDate,
          pE);
      incrementCorruptionDetectionAutomatedScheduleFailedCounter(
          ValidationType.CHECK_SHARD_METADATA_CONSISTENCY, deferralType);

      _ndsCheckMetadataConsistencySvc.postponeNeedsCheckMetadataConsistencyAfter(
          clusterDescription.getGroupId(), clusterDescription.getName(), postponeDate);

      return maintenanceCheckStateBuilder.needMaintenance(false).build();
    }
  }

  @VisibleForTesting
  void incrementCorruptionDetectionAutomatedScheduleCounter(
      final ValidationType pValidationType,
      final CorruptionDetectionOperationOrigin pOperationOrigin) {
    PromMetricsSvc.incrementCounter(
        CORRUPTION_DETECTION_SUCCESSFUL_INITATION_RATE,
        pValidationType.name(),
        pOperationOrigin.name());
  }

  @VisibleForTesting
  void incrementCorruptionDetectionAutomatedScheduleFailedCounter(
      final ValidationType pValidationType,
      final CorruptionDetectionFailedInitiationState pDeferralType) {
    PromMetricsSvc.incrementCounter(
        CORRUPTION_DETECTION_FAILED_INITIATION_RATE, pValidationType.name(), pDeferralType.name());
  }

  public Pair<String, MaintenanceRelease> getAgentTargetVersion(
      final NDSGroup pNDSGroup,
      final AgentVersion pCurrentVersion,
      final SoftwareType pSoftwareType,
      final AgentVersion pMinimumVersion) {
    final ObjectId groupId = pNDSGroup.getGroupId();
    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(pSoftwareType);

    LOG.trace(
        "Found phased version for agent to be {}, gid={}", phasedVersion, pNDSGroup.getGroupId());

    // Check to see if the Group is pinned with an Agent Version
    final Optional<String> fixedAgentVersionOpt =
        FixedAgentVersion.getFixedAgentVersion(pNDSGroup, pSoftwareType);
    if (fixedAgentVersionOpt.isPresent()) {
      LOG.trace(
          "Found fixed agent version to be {}, gid={}",
          fixedAgentVersionOpt.get(),
          pNDSGroup.getGroupId());

      // Groups with Fixed Agent Versions should not have a release timeslot
      return Pair.of(fixedAgentVersionOpt.get(), criticalRelease(groupId));
    }

    if (pCurrentVersion == null) {
      LOG.trace("Current version is null, using target version, gid={}", pNDSGroup.getGroupId());
      return Pair.of(phasedVersion.getTargetVersion(), criticalRelease(groupId));
    }

    final String targetVersion =
        getVersionForCluster(pNDSGroup, phasedVersion, pCurrentVersion, pMinimumVersion).toString();

    // An agent version update is considered critical if either the version itself is marked as
    // critical or the current version on the group is below the minimum supported version
    // If curVersion is less than minVersion, immediately apply new version without waiting for a
    // release timeslot
    final boolean isOlder = pCurrentVersion.isOlderThan(pMinimumVersion);
    final boolean shouldSetReleaseTimeslot = phasedVersion.isCriticalRelease() && !isOlder;
    final MaintenanceRelease criticalRelease =
        new MaintenanceRelease(
            pNDSGroup.getGroupId(),
            shouldSetReleaseTimeslot
                ? new ReleaseTimeslot(
                    phasedVersion.getCriticalReleaseDurationHours(), phasedVersion.getReleaseDate())
                : null,
            isOlder ? SchedulingBehavior.CRITICAL : phasedVersion.getSchedulingBehavior());

    return Pair.of(targetVersion, criticalRelease);
  }

  // This function will trigger the critical release if the currentVersion is null.
  public Pair<String, MaintenanceRelease> getSoftwareTargetVersion(
      final NDSGroup pNDSGroup,
      @Nullable final VersionUtils.VersionContainer pCurrentVersion,
      final SoftwareType pSoftwareType,
      final VersionUtils.VersionContainer pMinimumVersion) {
    return getSoftwareTargetVersion(
        pNDSGroup,
        null,
        pCurrentVersion,
        pSoftwareType,
        pMinimumVersion,
        true // default behavior is to apply critical release on null current version
        );
  }

  Pair<String, MaintenanceRelease> getSoftwareTargetVersion(
      final NDSGroup pNDSGroup,
      @Nullable final Cluster pCluster,
      @Nullable final VersionUtils.VersionContainer pCurrentVersion,
      final SoftwareType pSoftwareType,
      @Nullable final VersionUtils.VersionContainer pMinimumVersion,
      final boolean criticalReleaseOnNullCurrentVersion) {
    final ObjectId groupId = pNDSGroup.getGroupId();
    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(pSoftwareType);
    // Check to see if the Group is pinned with an Agent Version
    final Optional<String> fixedAgentVersionOpt =
        FixedAgentVersion.getFixedAgentVersion(pNDSGroup, pSoftwareType);
    if (fixedAgentVersionOpt.isPresent()) {
      // Groups with Fixed Agent Versions should not have a release timeslot
      return Pair.of(fixedAgentVersionOpt.get(), criticalRelease(groupId));
    }

    if (pCurrentVersion == null && criticalReleaseOnNullCurrentVersion) {
      // If curVersion is null, immediately apply new version without waiting for a release timeslot
      // only if criticalReleaseOnNullCurrentVersion is true
      return Pair.of(phasedVersion.getTargetVersion(), criticalRelease(groupId));
    }

    // A version update is considered critical if either the version itself is marked as
    // critical or the current version on the cluster is below the minimum supported version
    final boolean isOlder =
        (pMinimumVersion != null
            && (pCurrentVersion == null || pCurrentVersion.isLessThan(pMinimumVersion)));
    final boolean shouldSetReleaseTimeslot = phasedVersion.isCriticalRelease() && !isOlder;
    final MaintenanceRelease criticalRelease =
        new MaintenanceRelease(
            pNDSGroup.getGroupId(),
            shouldSetReleaseTimeslot
                ? new ReleaseTimeslot(
                    phasedVersion.getCriticalReleaseDurationHours(), phasedVersion.getReleaseDate())
                : null,
            isOlder ? SchedulingBehavior.CRITICAL : phasedVersion.getSchedulingBehavior());

    // if current version is null, use target version.
    final String targetVersion =
        pCurrentVersion == null
            ? phasedVersion.getTargetVersion()
            : getVersionForCluster(
                pNDSGroup, pCluster, phasedVersion, pCurrentVersion, pMinimumVersion);

    return Pair.of(targetVersion, criticalRelease);
  }

  public VersionUtils.Version getVersionForCluster(
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final PhasedVersion pPhasedVersion,
      final VersionUtils.Version pCurrentVersion) {
    return VersionUtils.parse(
        getVersionForCluster(pNDSGroup, pCluster, pPhasedVersion, pCurrentVersion, null));
  }

  public String getVersionForCluster(
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final PhasedVersion pPhasedVersion,
      final VersionUtils.VersionContainer pCurrentVersion,
      final VersionUtils.VersionContainer pMinimumVersion) {
    final Group group = _groupSvc.findById(pNDSGroup.getGroupId());
    return getVersionForCluster(
        pNDSGroup, group, pCluster, pPhasedVersion, pCurrentVersion, pMinimumVersion);
  }

  private String getVersionForCluster(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Cluster pCluster,
      final PhasedVersion pPhasedVersion,
      final VersionUtils.VersionContainer pCurrentVersion,
      final VersionUtils.VersionContainer pMinimumVersion) {
    // in the case of a custom build, phased version can be null - return the current version as no
    // maintenance is needed
    if (pPhasedVersion == null) {
      if (_customMongoDbBuildSvc.getCustomBuild(pCurrentVersion.getVersion()).isEmpty()) {
        throw new IllegalStateException(
            "No phased version present and the specified Mongo DB version does not represent a"
                + " custom build.");
      }
      return pCurrentVersion.getVersion();
    }

    // If the phased rollout is paused, there is nothing to be done. Log and return.
    if (pPhasedVersion.isPaused()) {
      LOG.warn(
          "{} rollout for {} version {} is paused, skipping group {}",
          pPhasedVersion.getReleaseMode().toString(),
          pPhasedVersion.getSoftwareType().toString(),
          pPhasedVersion.getTargetVersion(),
          pNDSGroup.getGroupId());
      return pCurrentVersion.getVersion();
    }

    // Critical release logic
    if (pPhasedVersion.isCriticalRelease()) {
      if (pPhasedVersion.inPhase(pNDSGroup.getGroupId())) {
        return pPhasedVersion.getTargetVersion();
      }

      // Agents enforce minimum versions due to deprecated features, bugs, etc.
      // If the group is on a deprecated version upgrade them anyway.
      if (pMinimumVersion != null && pCurrentVersion.isLessThan(pMinimumVersion)) {
        return pPhasedVersion.getTargetVersion();
      }

      // returning the original version implies no update is needed.
      return pCurrentVersion.getVersion();
    }

    return switch (pPhasedVersion.getReleaseMode()) {
      case PHASED:
        final Optional<String> targetSafePreviousVersion =
            getSafePreviousVersionForGroup(pGroup, pPhasedVersion, pCurrentVersion);
        if (isInVersionReleaseCohort(pNDSGroup, pPhasedVersion)) {
          LOG.debug(
              "Group {} is included in the {} version upgrade from {} to {} because it is"
                  + " inside of this release cohort, (project cohort: {}, release percentage: {})",
              pNDSGroup.getGroupId(),
              pPhasedVersion.getSoftwareType(),
              pCurrentVersion,
              pPhasedVersion.getTargetVersion(),
              pNDSGroup.getReleaseCohorts().getMongodbVersionCohort(),
              pPhasedVersion.getPhasedReleaseVersionPercent());

          yield pPhasedVersion.getTargetVersion();
        } else if (targetSafePreviousVersion.isPresent()
            && pPhasedVersion.isPreviousVersionSafe()) {
          LOG.info(
              "Group {} is included in the {} version upgrade from {} to {} because it is"
                  + " multiple versions behind and outside the current cohort, (project cohort:"
                  + " {}, release percentage: {})",
              pNDSGroup.getGroupId(),
              pPhasedVersion.getSoftwareType(),
              pCurrentVersion,
              targetSafePreviousVersion.get(),
              pNDSGroup.getReleaseCohorts().getMongodbVersionCohort(),
              pPhasedVersion.getPhasedReleaseVersionPercent());

          yield targetSafePreviousVersion.get();
        } else {
          LOG.info(
              "Group {} is excluded in the {} version upgrade from {} to {} because it is"
                  + " outside of this release cohort, (project cohort: {}, release percentage: {})",
              pNDSGroup.getGroupId(),
              pPhasedVersion.getSoftwareType(),
              pCurrentVersion,
              pPhasedVersion.getTargetVersion(),
              pNDSGroup.getReleaseCohorts().getMongodbVersionCohort(),
              pPhasedVersion.getPhasedReleaseVersionPercent());

          yield pCurrentVersion.getVersion();
        }
      case IFR:
        if (pPhasedVersion.getIfrState().isEmpty()) {
          LOG.error("IFR rollout with empty ifrState: {}", pPhasedVersion);
          yield pCurrentVersion.getVersion();
        }
        if (pCluster == null) {
          LOG.error(
              "IFR rollout with null cluster, cannot determine if group is eligible for IFR: {}",
              pPhasedVersion);
          yield pCurrentVersion.getVersion();
        }

        final IFRState ifrState = pPhasedVersion.getIfrState().get();
        // During the reconciliation phase all clusters in an IFR rollout are eligible for update
        // regardless of the wave allocation.
        if (ifrState.isReconciliationPhase()) {
          yield pPhasedVersion.getTargetVersion();
        }

        final ObjectId clusterId = pCluster.getClusterDescription().getUniqueId();
        final boolean isEligibleForWave =
            isClusterEligibleForWave(clusterId, ifrState.experimentId(), ifrState.wave());
        // isEligibleForWave is the ordinary wave membership check. The shouldProceedWithRollout
        // method ensure that the existing IFR experiment metadta is sufficient to proceed with the
        // rollout, e.g. waves should have been finalized, etc.
        if (isEligibleForWave && ifrState.shouldProceedWithRollout()) {
          LOG.info(
              "Cluster {} in group {} is eligible for IFR rollout with experiment id {} at wave {}",
              clusterId,
              pNDSGroup.getGroupId(),
              ifrState.experimentId(),
              ifrState.wave());
          yield pPhasedVersion.getTargetVersion();
        }
        yield pCurrentVersion.getVersion();
      default:
        yield pPhasedVersion.getTargetVersion();
    };
  }

  @SuppressWarnings("try")
  private boolean isClusterEligibleForWave(
      ObjectId clusterId, ObjectId experimentId, int waveNumber) {
    try (final var timer =
        IFR_WAVE_MEMBERSHIP_CHECK_DURATION.labels(String.valueOf(waveNumber)).startTimer()) {
      return _ifrSvc.eligibleForWave(clusterId, experimentId, waveNumber);
    }
  }

  public Optional<String> getSafePreviousVersionForGroup(
      final Group pGroup,
      final PhasedVersion pPhasedVersion,
      final VersionUtils.VersionContainer pCurrentVersion) {
    final Optional<String> previousVersion = pPhasedVersion.getPreviousVersion();

    if (pPhasedVersion.getSoftwareType().isMongoDB()
        && pCurrentVersion instanceof Version
        && previousVersion.isPresent()) {
      final Version targetPrevVersion = VersionUtils.parse(previousVersion.get());
      final boolean isPreviousSafeMongoDbVersionCompatibleWithCurrent =
          ((Version) pCurrentVersion)
                  .getMajorVersionString()
                  .equals(targetPrevVersion.getMajorVersionString())
              && targetPrevVersion.isGreaterThan((Version) pCurrentVersion);

      if (isPreviousSafeMongoDbVersionCompatibleWithCurrent) {
        return Optional.of(targetPrevVersion.getVersion());
      }
    } else if (pPhasedVersion.getSoftwareType() == SoftwareType.MONGOT
        && previousVersion.isPresent()) {
      final MongotVersion targetPrevVersion = MongotVersion.parse(previousVersion.get());
      final boolean isPreviousSafeMongoDbVersionCompatibleWithCurrent =
          targetPrevVersion.isGreaterThan((MongotVersion) pCurrentVersion);

      if (isPreviousSafeMongoDbVersionCompatibleWithCurrent) {
        return Optional.of(targetPrevVersion.getVersion());
      }
    } else if (pPhasedVersion.getSoftwareType() == SoftwareType.MONGOTUNE
        && previousVersion.isPresent()) {
      final Version targetPrevVersion = VersionUtils.parse(previousVersion.get());
      final boolean isPreviousSafeMongotuneVersionCompatibleWithCurrent =
          targetPrevVersion.isGreaterThan((Version) pCurrentVersion);
      if (isPreviousSafeMongotuneVersionCompatibleWithCurrent) {
        return Optional.of(targetPrevVersion.getVersion());
      }
    }

    return Optional.empty();
  }

  public AgentVersion getVersionForCluster(
      final NDSGroup pNDSGroup,
      final PhasedVersion pPhasedVersion,
      final AgentVersion pCurrentVersion,
      final AgentVersion pMinimumVersion) {
    // CLOUDP-84329 Don't do an agent upgrade if snapshot or restore is in progress
    // (Updated in CLOUDP-156455 to only skip an upgrade if pPhasedVersion was released less than 4
    // days ago. In other words, we force an agent version upgrade to go through if the version was
    // released more than 4 days ago, even if a snapshot or restore is in progress.
    // This will put a hard deadline in place where the agent upgrade will go through but also still
    // help most cases of not interrupting restores.)
    if (_cpsSvc.isAgentDoingSnapshotOrRestoreOperation(pNDSGroup.getGroupId())
        && pCurrentVersion != null
        && getNewDate()
            .toInstant()
            .minus(Duration.ofDays(FORCE_AGENT_UPGRADE_DAYS_SINCE_RELEASE_THRESHOLD))
            .isBefore(pPhasedVersion.getReleaseDate().toInstant())) {
      LOG.trace(
          "Using old agent version as it is doing a snapshot or restore operation, gid={}",
          pNDSGroup.getGroupId());
      return pCurrentVersion;
    }

    // We don't want system projects to do agent upgrades. They are ephemeral and upgrading the
    // agent will terminate the in progress export or collection level restore.
    if (pNDSGroup.isSystemProject()) {
      LOG.trace(
          "Using old agent version as this is the system project, gid={}", pNDSGroup.getGroupId());
      return pCurrentVersion;
    }

    // Critical release logic
    if (pPhasedVersion.isCriticalRelease()) {
      if (pPhasedVersion.inPhase(pNDSGroup.getGroupId())) {
        return new AgentVersion(pPhasedVersion.getTargetVersion(), AgentType.AUTOMATION);
      }

      // Agents enforce minimum versions due to deprecated features, bugs, etc.
      // If the group is on a deprecated version upgrade them anyway.
      if (pMinimumVersion != null && pCurrentVersion.isOlderThan(pMinimumVersion)) {
        return new AgentVersion(pPhasedVersion.getTargetVersion(), AgentType.AUTOMATION);
      }

      // returning the original version implies no update is needed.
      return pCurrentVersion;
    }

    LOG.trace("Using latest agent target version, gid={}", pNDSGroup.getGroupId());
    return new AgentVersion(pPhasedVersion.getTargetVersion(), AgentType.AUTOMATION);
  }

  public Date getNewDate() {
    return getDateCalculationUtil().getNewDate();
  }

  @VisibleForTesting
  public NdsMaintenanceHistorySvc getMaintenanceHistorySvc() {
    return _ndsMaintenanceHistorySvc;
  }

  @VisibleForTesting
  public NDSMaintenanceDateCalculationUtil getDateCalculationUtil() {
    return _ndsMaintenanceDateCalculationUtil;
  }

  public void sendInAdvancedNotificationIfNecessary(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final Logger pLogger) {
    final ObjectId groupId = pNDSGroup.getGroupId();
    final List<SchedulingBehavior> maintenanceBehaviors =
        pMaintenanceCheckResultList.stream()
            .filter(MaintenanceCheckResult::needMaintenance)
            .map(MaintenanceCheckResult::getSchedulingBehavior)
            .toList();

    final boolean doesNotNeedMaintenance =
        pMaintenanceCheckResultList.stream().noneMatch(MaintenanceCheckResult::needMaintenance);
    if (doesNotNeedMaintenance) {
      pLogger.trace("Skipping - does not require maintenance");
      return;
    }

    // Only consider the maintenance state that actually needs a maintenance performed
    if (maintenanceBehaviors.stream().allMatch(b -> b == SchedulingBehavior.CRITICAL)) {
      pLogger.trace("Skipping - all maintenances are critical");
      return;
    }

    final NDSGroupMaintenanceWindow maintenanceWindow = pNDSGroup.getMaintenanceWindow();
    final Date newDate = getNewDate();

    pLogger.info(
        "Maintenance is needed with the following maintenance check states: {}",
        neededMaintenanceAsString(pMaintenanceCheckResultList));

    // mark maintenance needed scheduling behavior is default maintenance window
    if (maintenanceWindow.getMaintenanceNeededDate().isEmpty()
        && maintenanceBehaviors.stream()
            .anyMatch(b -> b == SchedulingBehavior.DURING_MAINTENANCE_WINDOW)) {
      pLogger.trace(
          "Setting maintenanceNeededDate for Maintenance Window on {} for {}", groupId, newDate);
      _ndsGroupMaintenanceDao.setMaintenanceNeededDate(groupId, newDate);
    }

    // mark maintenance needed for protected hours if respect protected hours
    if (maintenanceWindow.getProtectedHours().getMaintenanceNeededDate().isEmpty()
        && maintenanceBehaviors.stream()
            .anyMatch(b -> b == SchedulingBehavior.RESPECT_PROTECTED_HOURS)) {
      pLogger.trace(
          "Setting maintenanceNeededDate for Protected Hours on {} for {}", groupId, newDate);
      _ndsGroupMaintenanceDao.setProtectedHoursMaintenanceNeededDate(groupId, newDate);
    }

    if (maintenanceBehaviors.stream()
        .allMatch(b -> b == SchedulingBehavior.RESPECT_PROTECTED_HOURS)) {
      pLogger.trace("Maintenance required for protected skipping advanced hours {}", groupId);
      return;
    }

    if (maintenanceWindow.isUserDefined()
        && maintenanceWindow.getAdvanceNotificationSendDate().isEmpty()
        && isInAdvancedNotificationWindow(pNDSGroup, pGroup)) {
      if (shouldAutoDefer(maintenanceWindow)) {
        autoDeferMaintenanceOneWeek(pNDSGroup, pGroup, pLogger);
      } else {
        sendInAdvancedNotificationEmail(pNDSGroup, pGroup, pLogger);
        setInAdvancedNotificationSentForGroup(pNDSGroup, getNewDate());
      }
    }
  }

  public void sendMaintenanceStartedIfNecessary(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final Logger pLogger) {
    final boolean needMaintenance =
        pMaintenanceCheckResultList.stream().anyMatch(MaintenanceCheckResult::needMaintenance);
    final boolean allCriticalReleases =
        pMaintenanceCheckResultList.stream()
            .allMatch(
                maintenanceCheckState ->
                    maintenanceCheckState.needMaintenance()
                        && maintenanceCheckState.isCriticalRelease());

    final NDSGroupMaintenanceWindow maintenanceWindow = pNDSGroup.getMaintenanceWindow();

    if (!needMaintenance
        || allCriticalReleases
        || !maintenanceWindow.isUserDefined()
        || maintenanceWindow.getMaintenanceStartedNotificationSent().isPresent()) {
      return;
    }

    if (pMaintenanceCheckResultList.stream()
        .filter(MaintenanceCheckResult::needMaintenance)
        .map(MaintenanceCheckResult::getSchedulingBehavior)
        .allMatch(b -> b == SchedulingBehavior.RESPECT_PROTECTED_HOURS)) {
      pLogger.trace("All maintenances are within respected hours, skipping");
      return;
    }

    if (shouldStartMaintenanceForGroup(
        pNDSGroup,
        pGroup,
        SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
        "sendMaintenanceStartedIfNecessary",
        pLogger)) {
      logStartedMaintenanceInfo(pNDSGroup, pMaintenanceCheckResultList, pLogger);

      _ndsGroupMaintenanceDao.setInitialScheduledMaintenanceDate(
          pNDSGroup.getGroupId(),
          getNextMaintenanceStartDateTime(
                  pNDSGroup, pGroup, SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
              .getTime());
      sendMaintenanceStartedInformationalAlert(pNDSGroup.getGroupId(), pGroup, pLogger);
      setMaintenanceStartedNotificationSentForGroup(pNDSGroup, getNewDate());
    }
  }

  private void logStartedMaintenanceInfo(
      final NDSGroup pNDSGroup,
      final List<MaintenanceCheckResult> pMaintenanceCheckResults,
      final Logger pLogger) {
    pLogger.info(
        "Starting maintenance. Current maintenance states: {}",
        neededMaintenanceAsString(pMaintenanceCheckResults));

    final Optional<Date> advanceNotificationSendDate =
        pNDSGroup.getMaintenanceWindow().getAdvanceNotificationSendDate();
    if (advanceNotificationSendDate.isEmpty()) {
      return;
    }

    final List<NdsMaintenanceHistory> pendingMaintenanceHistories =
        _ndsMaintenanceHistorySvc.findAllByGroupIdAndStateNotCompleted(pNDSGroup.getGroupId());

    final Date advancedNotificationSendDateWithBuffer =
        DateUtils.addMinutes(
            advanceNotificationSendDate.get(),
            NO_EXTRA_MAINTENANCE_ADVANCED_NOTIFICATION_DATE_BUFFER_IN_MINUTES);

    final List<NdsMaintenanceHistory> historiesAfterAdvanceNotifDate =
        pendingMaintenanceHistories.stream()
            .filter(
                h ->
                    h.getCreatedAt().equals(advancedNotificationSendDateWithBuffer)
                        || h.getCreatedAt().after(advancedNotificationSendDateWithBuffer))
            .toList();

    final int totalBeforeAdvanceNotifDate =
        pendingMaintenanceHistories.size() - historiesAfterAdvanceNotifDate.size();

    pLogger.info(
        "Maintenance activity started summary. {} {} {} {}.",
        kv("totalAfterAdv", historiesAfterAdvanceNotifDate.size()),
        kv("totalBeforeAdv", totalBeforeAdvanceNotifDate),
        kv("totalPending", pendingMaintenanceHistories.size()),
        kv(
            "overallStatus",
            totalBeforeAdvanceNotifDate == pendingMaintenanceHistories.size()
                ? "ALL_BEFORE_ADV"
                : historiesAfterAdvanceNotifDate.size() == pendingMaintenanceHistories.size()
                    ? "ALL_AFTER_ADV"
                    : totalBeforeAdvanceNotifDate == 0 ? "NONE" : "SOME_BEFORE_AFTER_ADV"));
  }

  @VisibleForTesting
  public String neededMaintenanceAsString(
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList) {
    return pMaintenanceCheckResultList.stream()
        .filter(MaintenanceCheckResult::needMaintenance)
        .toList()
        .toString();
  }

  public void maintenanceProtectedHoursCompletionCleanup(
      final ObjectId pGroupId,
      final MaintenanceProtectedHours pMaintenanceProtectedHours,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final List<ClusterDescription> pAllActiveClusters,
      final Logger pLogger) {
    if (!isRespectProtectedHoursMaintenanceCompleted(
        pMaintenanceProtectedHours, pMaintenanceCheckResultList, pAllActiveClusters)) {
      return;
    }

    pLogger.info(
        "Respect Protected Hours Maintenance complete for group {}. Resetting maintenance"
            + " protected hours document",
        pGroupId);
    _ndsGroupMaintenanceDao.resetRespectProtectedHoursMaintenanceAfterCompletionForGroup(pGroupId);
  }

  public void maintenanceCompletionCleanup(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final List<ClusterDescription> pAllActiveClusters,
      final Logger pLogger) {
    if (isMaintenanceCompleted(pNDSGroup, pMaintenanceCheckResultList, pAllActiveClusters)) {
      emitMaintenanceCompletedMetrics(pNDSGroup);
      resetMaintenanceAfterCompletionForGroup(pNDSGroup, pLogger);

      final NDSGroupMaintenanceWindow maintenanceWindow = pNDSGroup.getMaintenanceWindow();
      if (maintenanceWindow.isUserDefined()) {
        final boolean betweenNotificationAndScheduledMaintenance =
            maintenanceWindow
                .getAdvanceNotificationSendDate()
                .map(
                    date ->
                        getNewDate()
                            .before(DateUtils.addHours(date, IN_ADVANCED_NOTIFICATION_HOURS)))
                .orElse(false);

        final boolean betweenDeferralAndNextMaintenanceCheck =
            maintenanceWindow.getAdvanceNotificationSendDate().isEmpty()
                && maintenanceWindow.getDeferralRequestDate().isPresent();

        if (betweenNotificationAndScheduledMaintenance || betweenDeferralAndNextMaintenanceCheck) {
          sendMaintenanceNoLongerRequiredEmail(pNDSGroup, pGroup, pLogger);
        }
      }
    }
  }

  private void emitMaintenanceCompletedMetrics(final NDSGroup group) {
    final Optional<Date> maintenanceStartedNotificationDate =
        group.getMaintenanceWindow().getMaintenanceStartedNotificationSent();

    if (maintenanceStartedNotificationDate.isEmpty()) {
      return;
    }

    final long maintenanceTimeMinutes =
        Duration.between(
                maintenanceStartedNotificationDate.get().toInstant(), getNewDate().toInstant())
            .toMinutes();

    PromMetricsSvc.observeHistogram(MAINTENANCE_DURATION_MINUTES, maintenanceTimeMinutes);

    final long maintenanceTimePastWindowStartMinutes =
        Duration.between(
                getMaintenanceStartDateTimeForCurrentWeek(group).toInstant(),
                maintenanceStartedNotificationDate.get().toInstant())
            .toMinutes();

    PromMetricsSvc.observeHistogram(
        MAINTENANCE_DURATION_PAST_WINDOW_START_MINUTES, maintenanceTimePastWindowStartMinutes);
  }

  private void emitMaintenanceCountMetrics(
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList) {
    pMaintenanceCheckResultList.stream()
        .filter(MaintenanceCheckResult::needMaintenance)
        .collect(
            Collectors.groupingBy(
                checkResult -> checkResult.maintenanceType().name(), Collectors.counting()))
        .forEach(
            (maintenanceType, count) ->
                PromMetricsSvc.incrementCounter(
                    NEEDED_MAINTENANCES_BY_TYPE, count, maintenanceType));
  }

  private boolean allClustersIdle(final List<ClusterDescription> pAllActiveClusters) {
    return pAllActiveClusters.stream()
        .allMatch(
            cluster ->
                cluster.getNeedsMongoDBConfigPublishAfter().isEmpty()
                    && cluster.getState() == IDLE);
  }

  protected boolean isRespectProtectedHoursMaintenanceCompleted(
      final MaintenanceProtectedHours pProtectedHours,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final List<ClusterDescription> pAllActiveClusters) {
    return pProtectedHours.getMaintenanceNeededDate().isPresent()
        && requiredMaintenanceTypeCompleted(
            pMaintenanceCheckResultList, SchedulingBehavior.RESPECT_PROTECTED_HOURS)
        && allClustersIdle(pAllActiveClusters);
  }

  protected boolean isMaintenanceCompleted(
      final NDSGroup pNDSGroup,
      final List<MaintenanceCheckResult> pAllMaintenanceCheckResultList,
      final List<ClusterDescription> pAllActiveClusters) {
    final boolean maintenanceWindowWasPreviouslyPlannedFor =
        pNDSGroup.getMaintenanceWindow().wasPreviouslyPlannedFor();
    final boolean allClustersIdle = allClustersIdle(pAllActiveClusters);
    final boolean allMaintenancesCompletedForSchedulingBehavior =
        requiredMaintenanceTypeCompleted(
            filterMaintenanceCheckResultsForWindow(pNDSGroup, pAllMaintenanceCheckResultList),
            SchedulingBehavior.DURING_MAINTENANCE_WINDOW);

    final boolean isMaintenanceCompleted =
        maintenanceWindowWasPreviouslyPlannedFor
            && allClustersIdle
            && allMaintenancesCompletedForSchedulingBehavior;
    final boolean shouldLogWarning =
        pNDSGroup
            .getMaintenanceWindow()
            .getMaintenanceStartedNotificationSent()
            .map(
                startedDate ->
                    Duration.between(startedDate.toInstant(), getNewDate().toInstant()).toMinutes()
                        > 30)
            .orElse(false);
    if (!isMaintenanceCompleted && shouldLogWarning) {
      LOG.warn(
          "Maintenance not yet complete for group {} where"
              + " maintenanceWindowWasPreviouslyPlannedFor={}, allClustersIdle={},"
              + " allMaintenancesCompletedForSchedulingBehavior={}",
          pNDSGroup.getGroupId(),
          maintenanceWindowWasPreviouslyPlannedFor,
          allClustersIdle,
          allMaintenancesCompletedForSchedulingBehavior);
    }
    return isMaintenanceCompleted;
  }

  /**
   * User defined windows support a feature termed no-extra maintenances. This means that only
   * maintenance activity that came in previous the advanced notification window will be applied
   * during the maintenance window. As such when closing the window, we only consider the
   * maintenance that was queued before the advanced notification was sent.
   *
   * <p>System defined windows do not support no-extra the maintenance feature, so we consider all
   * maintenance activity.
   */
  private List<MaintenanceCheckResult> filterMaintenanceCheckResultsForWindow(
      final NDSGroup pNDSGroup, final List<MaintenanceCheckResult> pMaintenanceCheckResultList) {
    if (pNDSGroup.getMaintenanceWindow().isUserDefined()) {
      return pMaintenanceCheckResultList.stream()
          .filter(
              c ->
                  queuedBeforeAdvancedNotificationSendDate(
                      pNDSGroup.getGroupId(),
                      pNDSGroup.getMaintenanceWindow(),
                      MaintenanceHistoryMatchCriteria.fromCheckResult(c)))
          .toList();
    } else {
      return pMaintenanceCheckResultList;
    }
  }

  private boolean requiredMaintenanceTypeCompleted(
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final SchedulingBehavior pSchedulingBehavior) {
    final Set<SchedulingBehavior> requiredMaintenanceTypes =
        pMaintenanceCheckResultList.stream()
            .filter(MaintenanceCheckResult::needMaintenance)
            .map(MaintenanceCheckResult::getSchedulingBehavior)
            .collect(Collectors.toSet());
    return !requiredMaintenanceTypes.contains(pSchedulingBehavior);
  }

  public boolean isInAdvancedNotificationWindow(final NDSGroup pNDSGroup, final Group pGroup) {
    if (isUpcomingMaintenanceDeferred(pNDSGroup, pGroup)) {
      return false;
    }

    final Calendar nextMaintenanceStartDate =
        getNextMaintenanceStartDateTime(
            pNDSGroup, pGroup, SchedulingBehavior.DURING_MAINTENANCE_WINDOW);

    final long minutesTillNextMaintenance =
        Duration.between(getNewDate().toInstant(), nextMaintenanceStartDate.toInstant())
            .toMinutes();

    // Allow advanced notification to be sent between 72 - 47 hours within maintenance start date
    return minutesTillNextMaintenance
            <= Duration.ofHours(IN_ADVANCED_NOTIFICATION_HOURS).toMinutes()
        && minutesTillNextMaintenance
            >= Duration.ofHours(IN_ADVANCED_NOTIFICATION_HOURS - (24 + 1)).toMinutes();
  }

  public boolean queuedBeforeAdvancedNotificationSendDate(
      final ObjectId pGroupId,
      final NDSGroupMaintenanceWindow pNDSGroupMaintenanceWindow,
      final MaintenanceHistoryMatchCriteria pHistoryMatchCriteria) {
    final Group group = _groupSvc.findById(pGroupId);
    if (group == null
        || !isFeatureFlagEnabled(
            FeatureFlag.ATLAS_NO_EXTRA_INTERNAL_MAINTENANCES, _appSettings, null, group)) {
      return true; // Return true to allow the default functionality
    }

    if (pNDSGroupMaintenanceWindow.getAdvanceNotificationSendDate().isEmpty()) {
      return false;
    }

    final List<NdsMaintenanceHistory> matchingHistories =
        _ndsMaintenanceHistorySvc
            .findAllNotCompletedByGroupIdAndType(
                pGroupId, pHistoryMatchCriteria.getMaintenanceType())
            .stream()
            .filter(pHistoryMatchCriteria::matchesHistory)
            .toList();

    // It's expected Group level maintenance can have multiple entries
    if (!pHistoryMatchCriteria.getMaintenanceType().isGroupLevelMaintenance()
        && matchingHistories.size() > 1) {
      LOG.warn(
          "Check queued maintenance tasks before advancedNotificationDate. Found multiple non-group"
              + " maintenance histories for {}",
          kv("maintenanceType", pHistoryMatchCriteria.getMaintenanceType()));
    }

    final Date advanceNotificationSendDateWithBuffer =
        DateUtils.addMinutes(
            pNDSGroupMaintenanceWindow.getAdvanceNotificationSendDate().get(),
            NO_EXTRA_MAINTENANCE_ADVANCED_NOTIFICATION_DATE_BUFFER_IN_MINUTES);

    final boolean allMaintenanceCreatedBeforeAdvancedSendDate =
        matchingHistories.stream()
            .allMatch(h -> h.getCreatedAt().before(advanceNotificationSendDateWithBuffer));

    if (matchingHistories.isEmpty()) {
      LOG.debug(
          "Check queued maintenance tasks before advancedNotificationDate. Blocked maintenance"
              + " matching criteria {} from running because no history entry was found",
          pHistoryMatchCriteria);
    } else if (!allMaintenanceCreatedBeforeAdvancedSendDate) {
      LOG.debug(
          "Check queued maintenance tasks before advancedNotificationDate. Blocked maintenance"
              + " matching criteria {} from running because not all history entries were created"
              + " before the advanced notification date {}",
          pHistoryMatchCriteria,
          pNDSGroupMaintenanceWindow.getAdvanceNotificationSendDate().get());
    }

    return !matchingHistories.isEmpty() && allMaintenanceCreatedBeforeAdvancedSendDate;
  }

  public void setStartedDateOnPendingMaintenance(
      final ObjectId pGroupId, final MaintenanceCheckResult checkResult, final Logger logger) {
    final List<ObjectId> matchingHistoryIds =
        _ndsMaintenanceHistorySvc
            .findAllNotCompletedByGroupIdAndType(pGroupId, checkResult.maintenanceType())
            .stream()
            .filter(MaintenanceHistoryMatchCriteria.fromCheckResult(checkResult)::matchesHistory)
            .map(NdsMaintenanceHistory::getId)
            .toList();

    if (matchingHistoryIds.isEmpty()) {
      logger.warn(
          "Could not find a history entry for maintenance {} with groupId {} maintenanceCheckResult"
              + " {}",
          checkResult.maintenanceType(),
          pGroupId,
          checkResult);
    } else {
      _ndsMaintenanceHistorySvc.updateMaintenancesStartedDate(matchingHistoryIds, new Date());
    }
  }

  public boolean shouldStartMaintenanceForGroup(
      final NDSGroup pNDSGroup,
      final SchedulingBehavior pSchedulingBehavior,
      final String pMaintenanceReason,
      final Logger pLogger) {
    return shouldStartMaintenanceForGroup(
        pNDSGroup, pSchedulingBehavior, pMaintenanceReason, null, pLogger);
  }

  public boolean shouldStartMaintenanceForGroup(
      final NDSGroup pNDSGroup,
      final SchedulingBehavior pSchedulingBehavior,
      final String pMaintenanceReason,
      @Nullable final MaintenanceHistoryMatchCriteria pHistoryMatchCriteria,
      final Logger pLogger) {
    return shouldStartMaintenanceForGroup(
        pNDSGroup,
        getGroupUserMaintenanceTimeZoneId(pNDSGroup),
        pSchedulingBehavior,
        pMaintenanceReason,
        pHistoryMatchCriteria,
        pLogger);
  }

  public boolean shouldStartMaintenanceForGroup(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final SchedulingBehavior pSchedulingBehavior,
      final String pMaintenanceReason,
      final Logger pLogger) {
    return shouldStartMaintenanceForGroup(
        pNDSGroup, pGroup, pSchedulingBehavior, pMaintenanceReason, null, pLogger);
  }

  public boolean shouldStartMaintenanceForGroup(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final SchedulingBehavior pSchedulingBehavior,
      final String pMaintenanceReason,
      @Nullable final MaintenanceHistoryMatchCriteria pHistoryMatchCriteria,
      final Logger pLogger) {
    return shouldStartMaintenanceForGroup(
        pNDSGroup,
        getGroupUserMaintenanceTimeZoneId(pGroup),
        pSchedulingBehavior,
        pMaintenanceReason,
        pHistoryMatchCriteria,
        pLogger);
  }

  private boolean shouldStartMaintenanceForGroup(
      final NDSGroup pNDSGroup,
      final String pGroupTimezoneId,
      final SchedulingBehavior pSchedulingBehavior,
      final String pMaintenanceReason,
      @Nullable final MaintenanceHistoryMatchCriteria pHistoryMatchCriteria,
      final Logger pLogger) {
    if (pNDSGroup.isSystemProject()
        && pNDSGroup.getSystemProjectType() != SystemProjectType.SHADOW_CLUSTER) {
      pLogger.debug(
          "Skip maintenance for the {} system project {}. Metadata: [maintenance event: {},"
              + " scheduling behavior: {}]",
          pNDSGroup.getSystemProjectType(),
          pNDSGroup.getGroupId(),
          pMaintenanceReason,
          pSchedulingBehavior.name());
      return false;
    }

    switch (pSchedulingBehavior) {
      case CRITICAL -> {
        pLogger.debug(
            "Returning true from shouldStartMaintenanceForGroup. Reason: Maintenance is critical."
                + " Metadata: [maintenance event: {}, scheduling behavior: {}]",
            pMaintenanceReason,
            pSchedulingBehavior.name());
        return true;
      }
      case DURING_MAINTENANCE_WINDOW -> {
        final NDSGroupMaintenanceWindow maintenanceWindow = pNDSGroup.getMaintenanceWindow();
        final Calendar calendar = getDateCalculationUtil().getCalendarInstance(pGroupTimezoneId);

        // return true when maintenance is configured to run asap
        if (maintenanceWindow.getStartASAP()) {
          pLogger.debug(
              "Returning true from shouldStartMaintenanceForGroup. Reason: Maintenance is"
                  + " configured to run ASAP and maintenance was queued before"
                  + " AdvancedNotificationDate. Metadata: [maintenance event: {}, scheduling"
                  + " behavior: {}]",
              pMaintenanceReason,
              pSchedulingBehavior.name());
          return true;
        }

        // User defined maintenance window
        if (maintenanceWindow.isUserDefined()) {
          final boolean isInMaintenanceWindow =
              getDateCalculationUtil().isDuringUserDefinedMaintenanceWindow(calendar, pNDSGroup)
                  && maintenanceWindow.getAdvanceNotificationSendDate().isPresent();
          if (!isInMaintenanceWindow) {
            pLogger.debug(
                "isInMaintenanceWindow: {}; calendar day of week: {}, calendar hour of day: {};"
                    + " maintenanceWindow day of week: {}, maintenanceWindow hour of day: {}"
                    + " advance notification sent: {}",
                isInMaintenanceWindow,
                calendar.get(Calendar.DAY_OF_WEEK),
                calendar.get(Calendar.HOUR_OF_DAY),
                maintenanceWindow.getDayOfWeek(),
                maintenanceWindow.getHourOfDay(),
                maintenanceWindow.getAdvanceNotificationSendDate().isPresent());
          }

          final boolean isPastScheduledMaintenance =
              maintenanceWindow
                  .getAdvanceNotificationSendDate()
                  .map(
                      date ->
                          getNewDate()
                              .after(DateUtils.addHours(date, IN_ADVANCED_NOTIFICATION_HOURS + 1)))
                  .orElse(false);

          final boolean isMomentOutsideProtectedHours =
              !pNDSGroup
                  .getMaintenanceWindow()
                  .getProtectedHours()
                  .isMomentInProtectedHours(calendar);

          final boolean wasMaintenanceQueuedBeforeAdvancedNotificationSendDate =
              pHistoryMatchCriteria == null
                  || queuedBeforeAdvancedNotificationSendDate(
                      pNDSGroup.getGroupId(), maintenanceWindow, pHistoryMatchCriteria);

          final boolean shouldStartMaintenance =
              wasMaintenanceQueuedBeforeAdvancedNotificationSendDate
                  && (isInMaintenanceWindow
                      || (isPastScheduledMaintenance && isMomentOutsideProtectedHours));

          pLogger.debug(
              "Returning {} from shouldStartMaintenanceForGroup. Conditions [isInMaintenanceWindow:"
                  + " {}, isPastScheduledMaintenance: {}, isMomentOutsideProtectedHours: {},"
                  + " wasMaintenanceQueuedBeforeAdvancedNotificationSendDate: {}]. Metadata:"
                  + " [maintenanceType: {}, maintenance event: {}, scheduling behavior: {}]",
              shouldStartMaintenance,
              isInMaintenanceWindow,
              isPastScheduledMaintenance,
              isMomentOutsideProtectedHours,
              wasMaintenanceQueuedBeforeAdvancedNotificationSendDate,
              Optional.ofNullable(pHistoryMatchCriteria)
                  .map(MaintenanceHistoryMatchCriteria::getMaintenanceType)
                  .orElse(null),
              pMaintenanceReason,
              pSchedulingBehavior.name());

          return shouldStartMaintenance;
        }

        final boolean isInMaintenanceWindow =
            getDateCalculationUtil().isDuringSystemDefinedMaintenanceWindow(calendar, pNDSGroup);

        // Maintenance is behind if system maintenance has been needed for more than 24 hours, which
        // indicate that a maintenance window has been skipped
        final boolean isMaintenanceBehind =
            maintenanceWindow.getMaintenanceNeededDate().isPresent()
                && getNewDate()
                    .after(
                        DateUtils.addHours(maintenanceWindow.getMaintenanceNeededDate().get(), 24));

        final boolean shouldStartMaintenance = isInMaintenanceWindow || isMaintenanceBehind;

        pLogger.debug(
            "Returning {} from shouldStartMaintenanceForGroup. Conditions [isInMaintenanceWindow:"
                + " {}, isMaintenanceBehind: {}]. Metadata: [maintenance event: {}, scheduling"
                + " behavior: {}]",
            shouldStartMaintenance,
            isInMaintenanceWindow,
            isMaintenanceBehind,
            pMaintenanceReason,
            pSchedulingBehavior.name());

        return shouldStartMaintenance;
      }
      case RESPECT_PROTECTED_HOURS -> {
        final Optional<MaintenanceProtectedHours> protectedHours =
            Optional.ofNullable(pNDSGroup.getMaintenanceWindow().getProtectedHours());
        final boolean isMaintenancePastDue =
            protectedHours
                .flatMap(MaintenanceProtectedHours::getMaintenanceNeededDate)
                .filter(d -> getNewDate().after(DateUtils.addDays(d, 7)))
                .isPresent();

        final Calendar calendar = getDateCalculationUtil().getCalendarInstance(pGroupTimezoneId);

        if (protectedHours.map(MaintenanceProtectedHours::isUserDefined).orElse(false)) {
          // should start maintenance if the current time is NOT in the protected hours window
          final boolean isInProtectedHours =
              protectedHours.orElseThrow().isMomentInProtectedHours(calendar);
          final boolean shouldStartMaintenance = isMaintenancePastDue || !isInProtectedHours;
          pLogger.debug(
              "Returning {} from shouldStartMaintenanceForGroup. Conditions [current time is NOT in"
                  + " the protected hours window: {}, isMaintenanceBehind: {}]. Metadata:"
                  + " [maintenance event: {}, scheduling behavior: {}]",
              shouldStartMaintenance,
              !isInProtectedHours,
              isMaintenancePastDue,
              pMaintenanceReason,
              pSchedulingBehavior.name());
          return shouldStartMaintenance;
        } else {
          final boolean isDuringSystemDefinedMaintenanceWindow =
              getDateCalculationUtil().isDuringSystemDefinedMaintenanceWindow(calendar, pNDSGroup);

          // should start maintenance during systemHourOfDay
          final boolean shouldStartMaintenance =
              isMaintenancePastDue || isDuringSystemDefinedMaintenanceWindow;

          pLogger.debug(
              "Returning {} from shouldStartMaintenanceForGroup. Conditions [during"
                  + " systemHourOfDay: {}, isMaintenanceBehind: {}]. Metadata: [maintenance event:"
                  + " {}, scheduling behavior: {}]",
              shouldStartMaintenance,
              isDuringSystemDefinedMaintenanceWindow,
              isMaintenancePastDue,
              pMaintenanceReason,
              pSchedulingBehavior.name());
          return shouldStartMaintenance;
        }
      }
      default -> throw new IllegalArgumentException("Unexpected value: " + pSchedulingBehavior);
    }
  }

  public static boolean isInVersionReleaseCohort(
      final NDSGroup pNDSGroup, final PhasedVersion pPhasedVersion) {

    return pPhasedVersion
        .getPhasedReleaseCriteria()
        .map(
            criteria ->
                (pNDSGroup.getReleaseCohorts().getMongodbVersionCohort()
                        <= criteria.getVersionPercent()
                    && isGroupIdIncludedBasedOnCriteriaGroupIdLists(pNDSGroup, criteria)))
        .orElse(false);
  }

  public boolean getIsInOngoingPhasedVersionRelease(
      final ObjectId pGroupId, final String pClusterName) throws SvcException {
    return getIsInOngoingPhasedVersionRelease(pGroupId, pClusterName, false);
  }

  public boolean getIsInOngoingPhasedVersionRelease(
      final ObjectId pGroupId,
      final String pClusterName,
      final boolean pShouldInvalidateVersionCache)
      throws SvcException {
    final ClusterDescription clusterDescription =
        _clusterSvc.getMergedCluster(pGroupId, pClusterName).getClusterDescription();
    final Optional<NDSGroup> group = _ndsGroupSvc.find(pGroupId);
    if (group.isEmpty()) {
      return false;
    }

    final String majorVersion = clusterDescription.getMongoDBMajorVersion();
    final boolean isContinuous =
        clusterDescription.getVersionReleaseSystem() == VersionReleaseSystem.CONTINUOUS;

    Optional<SoftwareType> softwareTypeOpt;
    if (isContinuous) {
      softwareTypeOpt = Optional.of(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);
    } else {
      softwareTypeOpt = Optional.ofNullable(PhasedVersion.getSoftwareType(majorVersion));
    }

    if (softwareTypeOpt.isEmpty()) {
      return false;
    }

    if (pShouldInvalidateVersionCache) {
      // for resource endpoint integration tests only, where we might be interested to modify the
      // underlying PhasedVersion rapidly between test cases
      _phasedVersionSvc.invalidateVersionCache();
    }

    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(softwareTypeOpt.get());

    return !phasedVersion.isPaused()
        && isInVersionReleaseCohort(group.get(), phasedVersion)
        && isInAllowedInstanceSizes(clusterDescription, phasedVersion);
  }

  private boolean isInParameterReleaseCohort(
      final NDSGroup pNDSGroup, final PhasedVersion pPhasedVersion) {
    return pPhasedVersion
        .getPhasedReleaseCriteria()
        .map(
            criteria ->
                (pNDSGroup.getReleaseCohorts().getMongodbParameterCohort()
                        <= criteria.getParameterPercent()
                    && isGroupIdIncludedBasedOnCriteriaGroupIdLists(pNDSGroup, criteria)))
        .orElse(false);
  }

  private static boolean isGroupIdIncludedBasedOnCriteriaGroupIdLists(
      final NDSGroup pNDSGroup, final PhasedReleaseCriteria pPhasedReleaseCriteria) {
    if (pPhasedReleaseCriteria.getIncludedProjects().isPresent()) {
      return pPhasedReleaseCriteria.getIncludedProjects().get().stream()
          .anyMatch(x -> x.equals(pNDSGroup.getGroupId()));
    }
    if (pPhasedReleaseCriteria.getExcludedProjects().isPresent()) {
      return pPhasedReleaseCriteria.getExcludedProjects().get().stream()
          .noneMatch(x -> x.equals(pNDSGroup.getGroupId()));
    }
    return true;
  }

  public String getGroupUserMaintenanceTimeZoneId(final NDSGroup pNDSGroup) {
    final Group group = _groupSvc.findById(pNDSGroup.getGroupId());
    return getGroupUserMaintenanceTimeZoneId(group);
  }

  @Override
  public String getGroupUserMaintenanceTimeZoneId(final Group pGroup) {
    return Optional.ofNullable(pGroup).map(Group::getDefaultTimeZoneId).orElse("UTC");
  }

  public Calendar getNextMaintenanceStartDateTimeForNonCriticalMaintenance(
      final NDSGroup pNDSGroup, final Group pGroup) {
    return getDateCalculationUtil()
        .getNextMaintenanceStartDateTimeForNonCriticalMaintenance(
            pNDSGroup, getGroupUserMaintenanceTimeZoneId(pGroup));
  }

  // TODO: refactor to move usages to calculation svc
  public Calendar getNextMaintenanceStartDateTime(
      final NDSGroup pNDSGroup, final Group pGroup, final SchedulingBehavior pSchedulingBehavior) {
    final String tzId = getGroupUserMaintenanceTimeZoneId(pGroup);
    return getDateCalculationUtil()
        .getNextMaintenanceStartDateTimeForSchedulingBehavior(pNDSGroup, tzId, pSchedulingBehavior);
  }

  public Calendar getMaintenanceStartDateTimeForCurrentWeek(final NDSGroup pNDSGroup) {
    return getDateCalculationUtil()
        .getMaintenanceStartDateTimeForCurrentWeek(
            pNDSGroup, getGroupUserMaintenanceTimeZoneId(pNDSGroup));
  }

  protected void sendInAdvancedNotificationEmail(
      final NDSGroup pNDSGroup, final Group pGroup, final Logger pLogger) {
    final Calendar maintenanceWindowDatetime =
        getNextMaintenanceStartDateTime(
            pNDSGroup, pGroup, SchedulingBehavior.DURING_MAINTENANCE_WINDOW);
    pLogger.info("Sending maintenance notice email to users in group {}", pNDSGroup.getGroupId());

    final NDSMaintenanceWindowAudit.Builder builder = new Builder(Type.MAINTENANCE_IN_ADVANCED);
    builder.groupId(pNDSGroup.getGroupId());
    builder.hidden(false);
    builder.auditInfo(AuditInfoHelpers.fromSystem());
    builder.groupName(pGroup.getName());
    builder.maintenanceDateTime(
        getDateFormatter(pGroup).format(maintenanceWindowDatetime.getTime()));
    builder.maintenanceHour(pNDSGroup.getMaintenanceWindow().getHourOfDayText());
    builder.maintenanceDay(pNDSGroup.getMaintenanceWindow().getDayOfWeekText());
    builder.maintenanceTimeZone(
        TZUtils.getTimeZoneDisplayShort(getGroupUserMaintenanceTimeZoneId(pGroup)));
    final Event event = builder.build();
    _auditSvc.saveAuditEvent(event);
    _informationalAlertSvc.enqueueEvent(event);
  }

  protected Date calculateTargetMaintenanceDateForDeferral(
      final NDSGroup pNDSGroup, final Date pToday) {
    /* If the advanced notification has been sent that means we are within 72 hours of maintenance starting.
    getNextMaintenanceStartDateTime() will return that aforementioned Date. However, since we are
    deferring maintenance by a week, we need to add 7 days to that Date.
    */
    final Calendar maintenanceCalendar =
        getDateCalculationUtil()
            .getMaintenanceStartDateTimeForCurrentWeek(
                pNDSGroup, getGroupUserMaintenanceTimeZoneId(pNDSGroup));

    if (maintenanceCalendar.toInstant().isAfter(pToday.toInstant())) {
      return DateUtils.addDays(maintenanceCalendar.getTime(), 7);
    } else {
      return DateUtils.addDays(maintenanceCalendar.getTime(), 14);
    }
  }

  private Date returnMaintenanceCalendarInFuture(
      final Calendar pMaintenanceCalendar,
      final Date pToday,
      final int pDaysToAdd,
      final int pNumDeferrals) {
    /* helper to predict next maintenance's date with different scenarios:
     * for user defined:
     * when the next maintenance will happen in the following 7 days,
     * the maintenance cal could have a date in the past, e.g. today is a Wednesday,
     * two scenarios to consider:
     * 1) maintenance calendar is set in the past, Tuesday,
     * then the next maintenance should add 7 days to maintenance calendar (which is one unit of interval
     * between each maintenance for a user-defined maintenance window), so we get next Tuesday
     * 2) maintenance is a weekday day after Wednesday, then return as is
     *
     * same case if the next maintenance is deferred by a week, so we are pushing the next expected
     * maintenance date to be 7 days or 14 days from the maintenance calendar
     *
     * for System defined: the interval is 1 day, and same logic for number of deferrals,
     * if it is a datetime before the present, we add 1 day to it
     */
    if (pMaintenanceCalendar.toInstant().isAfter(pToday.toInstant())) {
      return DateUtils.addDays(pMaintenanceCalendar.getTime(), pDaysToAdd * (pNumDeferrals));
    } else {
      return DateUtils.addDays(pMaintenanceCalendar.getTime(), pDaysToAdd * (pNumDeferrals + 1));
    }
  }

  public Date calculateNextMaintenanceDate(final NDSGroup pNDSGroup) {
    final NDSGroupMaintenanceWindow window = pNDSGroup.getMaintenanceWindow();
    final Calendar maintenanceCalendar =
        getDateCalculationUtil()
            .getMaintenanceStartDateTimeForCurrentWeek(
                pNDSGroup, getGroupUserMaintenanceTimeZoneId(pNDSGroup));
    Date todayDateTime = getNewDate();
    if (window.isUserDefined()) {
      /*
        for maintenance that got deferred,
        for manual: with in the context "current week",
        if user deferred the maintenance during adv notification period (max 72 hours prior to
        the maintenance calendar, then if we are calculating next maintenance date, any time
        after the deferral, it would be pushed to next week
        for auto: only defer by one week when this boolean was first set
      */
      final boolean maintenanceWasDeferred =
          window.getDeferralRequestDate().isPresent()
              && DateUtils.addDays(
                      maintenanceCalendar.getTime(),
                      -(int) TimeUnit.HOURS.toDays(IN_ADVANCED_NOTIFICATION_HOURS))
                  .before(todayDateTime);
      final boolean autoDeferNeedsToBeApplied =
          window.getAutoDeferEnabled() && window.getNumberOfDeferrals() == 0;
      if (window.getAdvanceNotificationSendDate().isEmpty()
          && (maintenanceWasDeferred || autoDeferNeedsToBeApplied)) {
        return returnMaintenanceCalendarInFuture(
            maintenanceCalendar, todayDateTime, USER_DEFINED_MAINTENANCE_INTERVAL_IN_DAYS, 1);
      }
      /*
        any other case will not defer by a week
        e.g. 1) if the maintenance was deferred last week but now we are coming into a new week,
        maintenance calendar will return a date this week and we can just directly use it
        or 2) maintenance wasn't deferred at all
      */
      return returnMaintenanceCalendarInFuture(
          maintenanceCalendar, todayDateTime, USER_DEFINED_MAINTENANCE_INTERVAL_IN_DAYS, 0);
    } else {
      // system defined
      return returnMaintenanceCalendarInFuture(
          maintenanceCalendar, todayDateTime, SYSTEM_DEFINED_MAINTENANCE_INTERVAL_IN_DAYS, 0);
    }
  }

  protected void sendMaintenanceAutoDeferredNotificationEmail(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Date pMaintenanceDate,
      final Logger pLogger) {
    pLogger.info(
        "Sending maintenance auto deferral notice email to users in group {}",
        pNDSGroup.getGroupId());

    final NDSMaintenanceWindowAudit.Builder builder = new Builder(Type.MAINTENANCE_AUTO_DEFERRED);
    builder.groupId(pNDSGroup.getGroupId());
    builder.hidden(false);
    builder.auditInfo(AuditInfoHelpers.fromSystem());
    builder.groupName(pGroup.getName());
    builder.maintenanceDateTime(getDateFormatter(pGroup).format(pMaintenanceDate.getTime()));
    builder.maintenanceHour(pNDSGroup.getMaintenanceWindow().getHourOfDayText());
    builder.maintenanceDay(pNDSGroup.getMaintenanceWindow().getDayOfWeekText());
    builder.maintenanceTimeZone(
        TZUtils.getTimeZoneDisplayShort(getGroupUserMaintenanceTimeZoneId(pGroup)));
    final Event event = builder.build();
    _auditSvc.saveAuditEvent(event);
    _informationalAlertSvc.enqueueEvent(event);
  }

  protected void sendMaintenanceNoLongerRequiredEmail(
      final NDSGroup pNDSGroup, final Group pGroup, final Logger pLogger) {
    final Calendar maintenanceWindowDatetime =
        getNextMaintenanceStartDateTime(
            pNDSGroup, pGroup, SchedulingBehavior.DURING_MAINTENANCE_WINDOW);
    pLogger.info(
        "Sending maintenance no longer needed email to users in group {}", pNDSGroup.getGroupId());

    final NDSMaintenanceWindowAudit.Builder builder =
        new Builder(Type.MAINTENANCE_NO_LONGER_NEEDED);
    builder.groupId(pNDSGroup.getGroupId());
    builder.hidden(false);
    builder.auditInfo(AuditInfoHelpers.fromSystem());
    builder.groupName(pGroup.getName());
    builder.maintenanceDateTime(
        getDateFormatter(pGroup).format(maintenanceWindowDatetime.getTime()));
    final Event event = builder.build();
    _auditSvc.saveAuditEvent(event);
    _informationalAlertSvc.enqueueEvent(event);
  }

  protected NDSMaintenanceWindowAudit getMaintenanceStartedAuditEvent(
      final ObjectId pGroupId, final String pGroupName, final String pMaintenanceDateTime) {
    final NDSMaintenanceWindowAudit.Builder builder = new Builder(Type.MAINTENANCE_STARTED);
    builder.groupId(pGroupId);
    builder.hidden(false);
    builder.auditInfo(AuditInfoHelpers.fromSystem());
    builder.groupName(pGroupName);
    builder.maintenanceDateTime(pMaintenanceDateTime);
    return builder.build();
  }

  @VisibleForTesting
  protected void sendMaintenanceStartedInformationalAlert(
      final ObjectId pGroupId, final Group pGroup, final Logger pLogger) {
    pLogger.info("Sending maintenance started email to users in group {}", pGroupId);
    final String maintenanceDateTime = getFormattedDateTime(pGroup, getNewDate());
    final Event event =
        getMaintenanceStartedAuditEvent(pGroupId, pGroup.getName(), maintenanceDateTime);
    _auditSvc.saveAuditEvent(event);
    _informationalAlertSvc.enqueueEvent(event);
  }

  @VisibleForTesting
  public String getFormattedDateTime(final Group pGroup, final Date pDate) {
    return getDateFormatter(pGroup).format(pDate);
  }

  public MaintenanceCheckResult refreshQueuedAdminActions(final ObjectId pGroupId) {
    final List<QueuedAdminAction> actions =
        _queuedAdminActionDao.findByGroupId(pGroupId, State.QUEUED, false);

    return actions.isEmpty()
        ? new MaintenanceCheckResult(
            MaintenanceType.SCHEDULED_ADMIN_ACTION, false, MAINTENANCE_WINDOW)
        : new MaintenanceCheckResult(
            MaintenanceType.SCHEDULED_ADMIN_ACTION, true, MAINTENANCE_WINDOW);
  }

  public MongoDbEolPhasedVersionReleaseStatus getMongoDbEolPhasedVersionReleaseStatus(
      final Group pGroup, final ObjectId pClusterUniqueId) {
    final Optional<NDSGroup> ndsGroupOpt = _ndsGroupDao.find(pGroup.getId());
    final Optional<ClusterDescription> clusterDescriptionOpt =
        _clusterDescriptionDao.findByUniqueId(pGroup.getId(), pClusterUniqueId);

    final MongoDbEolPhasedVersionReleaseStatus notNeededStatus =
        new MongoDbEolPhasedVersionReleaseStatus(false, null);

    if (ndsGroupOpt.isEmpty() || clusterDescriptionOpt.isEmpty()) {
      return notNeededStatus;
    }

    final Optional<TargetMongoDbEolVersion> targetVersionOpt =
        getMongoDbEolVersionUpgradeTargetIfMaintenanceNeeded(
            ndsGroupOpt.get(),
            pGroup,
            clusterDescriptionOpt.get(),
            (MongoDBEOLPhasedVersion)
                _phasedVersionSvc
                    .getCachedVersionForSoftwareTypes()
                    .get(SoftwareType.MONGODB_EOL_VERSION),
            LOG);
    return targetVersionOpt
        .map(
            targetMongoDbEolVersion ->
                new MongoDbEolPhasedVersionReleaseStatus(true, targetMongoDbEolVersion))
        .orElse(notNeededStatus);
  }

  public record MaintenancePromMetrics(
      Map<String, Long> totalCountsByMaintenanceType,
      Map<String, Long> neededCountsByMaintenanceType,
      long queuedMaintenances,
      @Nullable Long maintenanceDurationMinutes,
      @Nullable Long maintenanceDurationPastWindowStartMinutes) {

    public MaintenancePromMetrics(
        Map<String, Long> totalCountsByMaintenanceType,
        Map<String, Long> neededCountsByMaintenanceType,
        long queuedMaintenances) {
      this(
          totalCountsByMaintenanceType,
          neededCountsByMaintenanceType,
          queuedMaintenances,
          null,
          null);
    }

    public Optional<Long> getMaintenanceDurationMinutes() {
      return Optional.ofNullable(maintenanceDurationMinutes);
    }

    public Optional<Long> getMaintenanceDurationPastWindowStartMinutes() {
      return Optional.ofNullable(maintenanceDurationPastWindowStartMinutes);
    }
  }

  @VisibleForTesting
  void setClock(final Clock pClock) {
    _clock = pClock;
  }

  /**
   * Auto deferral should only kick in when it is enabled and the number of deferrals is 0. Only
   * deferring when the number of deferrals is zero ensures that we do not defer an already deferred
   * maintenance.
   */
  private static boolean shouldAutoDefer(final NDSGroupMaintenanceWindow pWindow) {
    return pWindow.getAutoDeferEnabled() && pWindow.getNumberOfDeferrals() == 0;
  }

  @VisibleForTesting
  enum ValidationType {
    DB_CHECK,
    CHECK_SHARD_METADATA_CONSISTENCY,
  }

  @VisibleForTesting
  enum CorruptionDetectionFailedInitiationState {
    FATAL,
    DEFERRED
  }

  record TargetMongoDBVersion(
      @Nullable PhasedVersion phasedTargetVersion, Version targetMongoDbVersion) {

    boolean isTargetPreviousVersionAndSafeToRollout() {
      return phasedTargetVersion != null
          && phasedTargetVersion.isPreviousVersionSafe()
          && phasedTargetVersion.getPreviousVersion().isPresent()
          && VersionUtils.parse(phasedTargetVersion.getPreviousVersion().get())
              .equals(targetMongoDbVersion);
    }
  }

  public record TargetMongoDbEolVersion(
      Version version, UpgradeType upgradeType, @Nullable Version sequenceFinalVersion) {}

  public record MongoDbEolPhasedVersionReleaseStatus(
      boolean clusterInPhasedVersionRelease,
      @Nullable TargetMongoDbEolVersion targetMongoDbEolVersion) {}
}
