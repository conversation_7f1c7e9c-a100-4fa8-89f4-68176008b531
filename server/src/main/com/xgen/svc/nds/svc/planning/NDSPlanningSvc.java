package com.xgen.svc.nds.svc.planning;

import static com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.ATLAS_AUTOHEAL_REDUCED_THRESHOLD;
import static com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.ATLAS_AUTOHEAL_REMOVE_ICMP_PING;
import static com.xgen.cloud.common.jobqueue._public.model.Job.Status.FINISHED_STATUSES;
import static com.xgen.cloud.common.jobqueue._public.model.Job.Status.IN_PROGRESS_STATUSES;
import static com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.getSummaryQuantile;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type.AUTO_HEALING_CANNOT_REPAIR_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type.AUTO_HEALING_CANNOT_RESYNC_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type.AUTO_HEALING_FIXED_INTERNAL;
import static com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.AutoHealMessage.CANNOT_REPAIR_NO_DATA;
import static com.xgen.cloud.nds.planning.tracing._public.model.NDSPlanningTraceSpanData.getSpanAttributesByClusterName;
import static com.xgen.cloud.nds.planning.tracing._public.model.NDSPlanningTraceSpanData.getSpanAttributesByGroupId;
import static com.xgen.cloud.nds.planning.tracing._public.model.NDSPlanningTraceSpanData.getSpanAttributesByPlanId;
import static com.xgen.cloud.nds.project._public.constants.NDSMaintenanceConstants.IN_ADVANCED_NOTIFICATION_HOURS;
import static com.xgen.cloud.nds.project._public.model.MaintenanceRelease.MAINTENANCE_WINDOW;
import static com.xgen.cloud.nds.project._public.model.NDSGroup.DEFAULT_SNAPSHOT_DELETION_BUFFER;
import static com.xgen.cloud.nds.project._public.model.NDSGroup.NextPlanningDateSetBy.PLANNER_EXCEPTION_DURING_ROUND;
import static com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc.getDesiredNumberOfNonConfigShards;
import static com.xgen.cloud.nds.tenant._public.model.TenantNDSDefaults.ACME_TENANT_CERT_EXPIRY_ROTATION_THRESHOLD;
import static com.xgen.svc.mms.svc.GroupSvcCloud.getNextPlanningDateForDeadGroups;
import static com.xgen.svc.nds.security.svc.NDSACMESvc.groupCertsByDnsPin;
import static com.xgen.svc.nds.security.svc.NDSACMESvc.groupCertsBySubdomain;
import static com.xgen.svc.nds.security.svc.NDSACMESvc.groupClustersByDnsPin;
import static com.xgen.svc.nds.security.svc.NDSACMESvc.groupClustersBySubdomainLevel;
import static com.xgen.svc.nds.svc.NDSAutoResumeSvc.AUTO_RESUME_DAYS;
import static com.xgen.svc.nds.util.EncryptionAtRestUtil.isEncryptionAtRestEnabledAndInvalid;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.MultimapBuilder;
import com.google.common.collect.SetMultimap;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoInterruptedException;
import com.mongodb.ReadPreference;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.alert.InformationalAlertSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.db.mongo._public.util.ExceptionUtils.Static;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.svc.JobParkingSvc;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.model._public.misc.AuditDescription;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.planning._public.util.PlanningUtil;
import com.xgen.cloud.common.util._public.json.JsonOptional;
import com.xgen.cloud.common.util._public.util.MathUtils;
import com.xgen.cloud.common.util._public.util.SimpleBatchProcessor;
import com.xgen.cloud.common.util._public.util.SplitTimer;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._private.dao.SystemClusterJobDao;
import com.xgen.cloud.cps.restore._public.model.BackupRestoreJobPlanUnit;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob;
import com.xgen.cloud.cron._private.dao.CronStateDao;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.MaintainedMongotuneConfig;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ReplicaSet;
import com.xgen.cloud.deployment._public.model.ReplicaSetMember;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.deployment._public.model.diff.ItemDiff;
import com.xgen.cloud.externalanalytics._public.model.ClusterProvisionedEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.GroupStatus;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.agent._public.svc.AgentPingSvc;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.HostUtils;
import com.xgen.cloud.monitoring.topology._public.model.ping.PingUtils;
import com.xgen.cloud.monitoring.topology._public.svc.HostLastPingSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Builder;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type;
import com.xgen.cloud.nds.admin._public.model.AdminClusterLock;
import com.xgen.cloud.nds.admin._public.svc.AdminClusterLockSvc;
import com.xgen.cloud.nds.autohealing.resync._public.svc.AutoHealResyncSvc;
import com.xgen.cloud.nds.autoscaling.context._private.dao.AutoScalingContextDao;
import com.xgen.cloud.nds.autoscaling.context._public.model.AutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.autosharding.ShardingStatus;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ClusterComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ShardComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.util.ComputeAutoScalingContextUtil;
import com.xgen.cloud.nds.autoscaling.predictive._public.model.PredictiveAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.predictive._public.model.PredictiveAutoScalingResult;
import com.xgen.cloud.nds.autoscaling.predictive._public.model.compute.ClusterComputePredictiveAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.predictive._public.model.compute.ComputePredictiveAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.predictive._public.model.compute.ShardComputePredictiveAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.predictive._public.svc.NDSPredictiveAutoScaleSvc;
import com.xgen.cloud.nds.autoscaling.predictive._public.svc.PredictiveAutoScalingContextSvc;
import com.xgen.cloud.nds.autoscaling.predictive._public.svc.PredictiveAutoScalingTriggerSvc;
import com.xgen.cloud.nds.autoscaling.predictive._public.svc.PredictiveAutoScalingTriggerSvc.TriggerSkippedReason;
import com.xgen.cloud.nds.autoscaling.predictive._public.util.ComputePredictiveAutoScalingContextUtil;
import com.xgen.cloud.nds.aws._private.dao.privatelink.AWSMultiTargetConnectionRuleDao;
import com.xgen.cloud.nds.aws._private.dao.privatelink.AWSPrivateLinkTargetGroupDao;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware.MigrateIpAction;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSCheckResult;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSInstanceCapacitySpec;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkConnection;
import com.xgen.cloud.nds.azure._private.dao.privatelink.AzurePrivateLinkConnectionInboundNATRuleDao;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureCheckResult;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureInstanceCapacitySpec;
import com.xgen.cloud.nds.capacity._public.model.CloudProviderAvailability;
import com.xgen.cloud.nds.capacity._public.model.azure.AzureCapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.svc.AzureCapacityDenylistSvc;
import com.xgen.cloud.nds.capacity._public.svc.CloudProviderAvailabilityFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.BaseMultiTargetConnectionRule;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.ContainerPeer;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.Action;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.AutoHealMessage;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.HealingConfiguration;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.ScheduledAction;
import com.xgen.cloud.nds.cloudprovider._public.model.KeyManagementConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster.MTMClusterType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.BaseEndpointService.Status;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.PrivateLinkConnectionRule;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.TenantEndpoint;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.TenantEndpointServiceDeployment;
import com.xgen.cloud.nds.cloudprovider._public.svc.IMTMClusterSvc;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.SubdomainLevel;
import com.xgen.cloud.nds.common._public.model.NDSProcessType;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipelineRun;
import com.xgen.cloud.nds.deployment._public.util.ClusterDeploymentProcessUtil;
import com.xgen.cloud.nds.dns._public.util.DNSRecordUtil;
import com.xgen.cloud.nds.events._private.dao.NDSEventDao;
import com.xgen.cloud.nds.flex._private.dao.FastFlexPreAllocatedRecordsDao;
import com.xgen.cloud.nds.flex._public.model.FlexMTMCluster;
import com.xgen.cloud.nds.flex._public.model.FlexMTMClusterMigration;
import com.xgen.cloud.nds.flex._public.model.FlexMTMHolderGroupMigration;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration;
import com.xgen.cloud.nds.flex._public.model.MigrationStatus;
import com.xgen.cloud.nds.flex._public.svc.FlexMigrationSvc;
import com.xgen.cloud.nds.free._private.dao.FastSharedPreAllocatedRecordsDao;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.free._public.model.SharedMTMCluster;
import com.xgen.cloud.nds.gcp._private.dao.privatelink.GCPPrivateServiceConnectionRuleDao;
import com.xgen.cloud.nds.hostname._public.svc.NDSHostnameSvc;
import com.xgen.cloud.nds.mongotune.settings._public.svc.MongotuneAppSettings;
import com.xgen.cloud.nds.mtmcompaction._public.model.MTMCompaction;
import com.xgen.cloud.nds.mtmcompaction._public.svc.MTMCompactionSvc;
import com.xgen.cloud.nds.mtmcompaction._public.util.MTMCompactionUtil;
import com.xgen.cloud.nds.planning.common._public.model.PlanSummary;
import com.xgen.cloud.nds.planning.common._public.model.PlanSummary.ResourceType;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction.ActionType;
import com.xgen.cloud.nds.planning.common._public.svc.NDSPlanExecutorJobPrioritySvc;
import com.xgen.cloud.nds.planning.common._public.util.PlanSummaryUtil;
import com.xgen.cloud.nds.planning.summary._public.model.NDSPlanningSummary;
import com.xgen.cloud.nds.planning.summary._public.model.NDSProjectPlanningSummaryBuilder;
import com.xgen.cloud.nds.planning.summary._public.model.cluster.ClusterMaintenanceDecisions;
import com.xgen.cloud.nds.planning.tracing._public.svc.NDSPlanningTracingSvc;
import com.xgen.cloud.nds.planning.tracing._public.svc.NDSPlanningTracingSvc.NDSPlanningTraceWrapper;
import com.xgen.cloud.nds.project._private.dao.EolVersionUpgradeHistoryDao;
import com.xgen.cloud.nds.project._private.dao.NDSContainerPeerDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.NDSLogDao;
import com.xgen.cloud.nds.project._private.dao.QueuedAdminActionDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterTag;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionValidationContext;
import com.xgen.cloud.nds.project._public.model.DeleteClusterReason;
import com.xgen.cloud.nds.project._public.model.EolVersionUpgradeHistory;
import com.xgen.cloud.nds.project._public.model.MaintenanceCheckResult;
import com.xgen.cloud.nds.project._public.model.MaintenanceHistoryMatchCriteria;
import com.xgen.cloud.nds.project._public.model.MaintenanceRelease;
import com.xgen.cloud.nds.project._public.model.MaintenanceType;
import com.xgen.cloud.nds.project._public.model.MongotuneProcessArgs;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSGroup.NDSState;
import com.xgen.cloud.nds.project._public.model.NDSGroup.NextPlanningDateSetBy;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.NdsMaintenanceHistory;
import com.xgen.cloud.nds.project._public.model.NdsMaintenanceHistory.InstanceHardwareDetails;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.SchedulingBehavior;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.dbusers.NDSDBUser;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermission;
import com.xgen.cloud.nds.project._public.svc.NdsMaintenanceHistorySvc;
import com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringSvc;
import com.xgen.cloud.nds.project._public.util.ClusterDescriptionValidationUtil;
import com.xgen.cloud.nds.project._public.util.NDSMaintenanceDateCalculationUtil;
import com.xgen.cloud.nds.project._public.util.ReplicationSpecListUtils;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.nds.rollingresync._public.model.RollingResync;
import com.xgen.cloud.nds.rollingresync._public.model.SyncSourceStrategy;
import com.xgen.cloud.nds.rollingresync._public.model.SyncSourceStrategy.Strategy;
import com.xgen.cloud.nds.rollingresync._public.svc.RollingResyncSvc;
import com.xgen.cloud.nds.security._public.model.NDSACMECert;
import com.xgen.cloud.nds.serverless._private.dao.FastServerlessPreAllocatedRecordsDao;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.pool.ServerlessMTMPool;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.serverless._public.util.logging.NDSServerlessAutoScalingLogger;
import com.xgen.cloud.nds.shadowcluster._private.dao.ShadowClusterExposureJobDao;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowClusterExposureJob;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ClusterOutageSimulation;
import com.xgen.cloud.nds.streams._private.dao.StreamsPrivateLinkDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsTransitGatewayResourceSharesDao;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLink;
import com.xgen.cloud.nds.streams._public.model.StreamsTransitGateway;
import com.xgen.cloud.nds.tenant._public.model.FastTenantPreAllocatedRecord;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantRestore;
import com.xgen.cloud.nds.tenantupgrade._private.dao.ServerlessUpgradeToDedicatedStatusDao;
import com.xgen.cloud.nds.tenantupgrade._private.dao.TenantUpgradeStatusDao;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeToDedicatedStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeStatus;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchInstanceSvc;
import com.xgen.cloud.streams._private.dao.VPCPeeringConnectionDao;
import com.xgen.cloud.streams._public.model.VPCPeeringConnection;
import com.xgen.module.common.planner.PlanExecutorJobHandler;
import com.xgen.module.common.planner.PlanFailureContextBuilder;
import com.xgen.module.common.planner.PlanFailureSentryEvent;
import com.xgen.module.common.planner.PlanPriority;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Plan.ExecutionStage;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.svc.PlanSvc;
import com.xgen.module.liveimport.model.LiveImport;
import com.xgen.module.mdc.MDCUtil;
import com.xgen.svc.mms.dao.uniformfrontend.UniformFrontendEnvoyConfigurationDao;
import com.xgen.svc.mms.svc.AtlasPremiumMonitoringSvc;
import com.xgen.svc.mms.svc.AutoIndexingSvc;
import com.xgen.svc.nds.aws.svc.AWSTenantEndpointServiceDeploymentSvc;
import com.xgen.svc.nds.aws.svc.admincapacity.AWSCheckCapacityRequestSvc;
import com.xgen.svc.nds.azure.svc.AzureTenantEndpointServiceDeploymentSvc;
import com.xgen.svc.nds.azure.svc.admincapacity.AzureCheckCapacityRequestSvc;
import com.xgen.svc.nds.gcp.svc.GCPPrivateServiceConnectSvc;
import com.xgen.svc.nds.liveimport.svc.LiveImportSvc;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.planner.ResyncBaseMove;
import com.xgen.svc.nds.planner.movetypes.ReplaceDiskMove;
import com.xgen.svc.nds.planner.movetypes.UpdateMachineSizeMove;
import com.xgen.svc.nds.security.svc.NDSACMESvc;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.serverless.svc.ServerlessLoadBalancingDeploymentSvc;
import com.xgen.svc.nds.serverless.svc.ServerlessMTMPoolSvc;
import com.xgen.svc.nds.simulateregionoutage.svc.RegionalOutageSvc;
import com.xgen.svc.nds.svc.AdminActionSvc;
import com.xgen.svc.nds.svc.FastTenantPreAllocatedRecordDaoProvider;
import com.xgen.svc.nds.svc.IngestionPipelineSvc;
import com.xgen.svc.nds.svc.InstanceHostnameSvc;
import com.xgen.svc.nds.svc.NDSAutoRebootSvc;
import com.xgen.svc.nds.svc.NDSAutoResumeSvc;
import com.xgen.svc.nds.svc.NDSContainerPeerSvc;
import com.xgen.svc.nds.svc.NDSEncryptionAtRestSvc;
import com.xgen.svc.nds.svc.NDSTenantPauseSvc;
import com.xgen.svc.nds.svc.NDSTenantSnapshotRestoreSvc;
import com.xgen.svc.nds.svc.PushBasedLogExportSvc;
import com.xgen.svc.nds.svc.cps.CpsPitSvc;
import com.xgen.svc.nds.svc.cps.CpsPolicySvc;
import com.xgen.svc.nds.svc.cps.CpsSnapshotEngine;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSReactiveAutoScaleSvc;
import com.xgen.svc.nds.tenant.svc.privatenetworking.MultiTenantEndpointServiceSvc;
import com.xgen.svc.nds.tenant.svc.privatenetworking.NDSTenantEndpointSvc;
import com.xgen.svc.nds.tenant.svc.privatenetworking.TenantEndpointServiceDeploymentSvc;
import com.xgen.svc.nds.util.OptimizedInitialSyncUtil;
import com.xgen.svc.security.acme.model.ACMECert;
import io.opentelemetry.api.common.Attributes;
import io.prometheus.client.Collector;
import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.Counter;
import io.prometheus.client.GaugeMetricFamily;
import io.prometheus.client.Histogram;
import io.prometheus.client.Summary;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.BasicBSONObject;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.slf4j.event.Level;

@Singleton
public class NDSPlanningSvc {

  public static final String CRON_NAME = "ndsPlanning";
  public static final String POPULATE_PROM_METRICS_CRON_NAME = "populatePlannerPromMetrics";
  public static final String CHECK_STUCK_PLAN_GENERATION_CRON_NAME = "checkStuckPlanGeneration";
  public static final String NDS_PLANNER_CONSIDER_DELETE_AFTER_DATE_ROLLOUT_PERCENTAGE =
      "nds.planner.deleteAfterDate.rollout.percentage";
  public static final String NDS_PLANNER_GROUP_INTERVAL_DISTRIBUTED_PLANNING_START =
      "nds.planner.group.interval.distributedPlanning.start";
  public static final String NDS_PLANNER_TRANSIENT_EXCEPTION_INTERVAL =
      "nds.planner.transientException.interval";
  public static final String NDS_PLANNER_UNHANDLED_EXCEPTION_INTERVAL =
      "nds.planner.unhandledException.interval";
  public static final Long DEFAULT_TRANSIENT_EXCEPTION_PLANNING_INTERVAL =
      Duration.ofMinutes(1).toMillis();
  public static final Long DEFAULT_UNHANDLED_EXCEPTION_PLANNING_INTERVAL =
      Duration.ofMinutes(5).toMillis();
  public static final String NDS_PLANNER_SNAPSHOT_DELETION_BUFFER_INTERVAL =
      "nds.planner.snapshotDeletionBuffer.interval";
  public static final String
      NDS_PLANNER_NEXT_GROUP_PLANNING_DATE_UNABLE_TO_PLAN_FOR_CLUSTER_INTERVAl =
          "nds.planner.nextGroupPlanningDate.unableToPlanForCluster.interval";
  public static final Long DEFAULT_UNABLE_TO_PLAN_FOR_CLUSTER_INTERVAL =
      Duration.ofMinutes(15).toMillis();

  private static final Logger LOG = LoggerFactory.getLogger(NDSPlanningSvc.class);
  private static final Duration MONITORING_RECENT_PING_THRESHOLD = Duration.ofMinutes(3);
  protected static final String MAX_CONCURRENT_PLANNING_GROUPS_PARAM =
      "nds.planning.maxConcurrentGroups";
  private static final String JOB_QUEUE_BATCHING_GROUP_SIZE_PARAM =
      "nds.planning.jobQueue.batchingGroupSize";
  private static final String JOB_QUEUE_IN_PROGRESS_JOB_SCHEDULED_FOR_LOOKBACK_MINUTES =
      "nds.planning.jobQueue.inProgressJobScheduledForLookbackMinutes";
  private static final String PLANNING_TYPE_CRON_JOB_LABEL_VALUE = "CRON_JOB";
  private static final String TYPE_EXISTING_LABEL_VALUE = "EXISTING";
  private static final String TYPE_CURRENT_ROUND_LABEL_VALUE = "CURRENT_ROUND";
  private static final String LABEL_NAME_JOB_ID_NOT_FOUND = "JOB_ID_NOT_FOUND";
  private static final String LABEL_NAME_JOB_NOT_FOUND = "JOB_NOT_FOUND";

  public static final String
      NDS_PLANNER_NEXT_GROUP_PLANNING_DATE_ALL_SNAPSHOTS_PURGED_DATE_ENABLED =
          "nds.planner.nextGroupPlanningDate.allSnapshotsPurgedDate.enabled";
  private static final Duration FRESH_PING_DURATION = Duration.ofMinutes(6);
  private static final int AWS_RELEASE_IP_THRESHOLD_DAYS = 5;

  private final JobsProcessorSvc _jobSvc;
  private final JobParkingSvc _jobParkingSvc;
  private final NDSClusterSvc _clusterSvc;
  private final ReplicaSetHardwareDao _hardwareDao;
  private final NDSGroupDao _ndsGroupDao;
  private final PlanDao _planDao;
  private final PlanSvc _planSvc;
  private final AgentPingSvc _agentPingSvc;
  private final HostSvc _hostSvc;
  private final HostClusterLifecycleSvc _hostClusterLifecycleSvc;
  private final GroupSvc _groupSvc;
  private final NDSLogDao _ndsLogDao;
  private final AppSettings _appSettings;
  private final AuditSvc _auditSvc;
  private final InformationalAlertSvc _alertSvc;
  private final NDSEventDao _ndsEventDao;
  private final ReplicaSetHardwareSvc _hardwareSvc;
  private final NDSReactiveAutoScaleSvc _autoScaleSvc;
  private final NDSAutoResumeSvc _autoResumeSvc;
  private final NDSAutoRebootSvc _autoRebootSvc;
  private final NDSContainerPeerSvc _containerPeerSvc;
  private final BackupSnapshotDao _snapshotDao;
  private final QueuedAdminActionDao _queuedAdminActionDao;
  private final CpsSvc _cpsSvc;
  private final CpsPitSvc _cpsPitSvc;
  private final NDSContainerPeerDao _containerPeerDao;
  private final BackupJobDao _backupJobDao;
  private final SystemClusterJobDao _systemClusterJobDao;
  private final NDSEncryptionAtRestSvc _ndsEncryptionAtRestSvc;
  private final NDSGroupSvc _ndsGroupSvc;
  private final LiveImportSvc _liveImportSvc;
  private final NDSGroupMaintenanceSvc _ndsGroupMaintenanceSvc;
  private final NDSMaintenanceDateCalculationUtil _maintenanceDateCalculationUtil;
  private final CpsPolicySvc _cpsPolicySvc;
  private final NDSTenantSnapshotRestoreSvc _tenantSnapshotRestoreSvc;
  private final IngestionPipelineSvc _ingestionPipelineSvc;
  private final NDSACMESvc _ndsACMESvc;
  private final AWSMultiTargetConnectionRuleDao _awsMultiTargetConnectionRuleDao;
  private final AWSPrivateLinkTargetGroupDao _awsPrivateLinkTargetGroupDao;
  private final AzurePrivateLinkConnectionInboundNATRuleDao
      _azurePrivateLinkConnectionInboundNATRuleDao;
  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final NDSTenantPauseSvc _ndsTenantPauseSvc;
  private final AutoIndexingSvc _autoIndexingSvc;
  private final CronStateDao _cronStateDao;
  private final NDSPlanExecutorCallbackSvc _planCallbackSvc;
  private final ServerlessLoadBalancingDeploymentDao _serverlessLoadBalancingDeploymentDao;
  private final ServerlessLoadBalancingDeploymentSvc _serverlessLoadBalancingDeploymentSvc;
  private final ServerlessMTMPoolSvc _serverlessMTMPoolSvc;
  private final GCPPrivateServiceConnectSvc _gcpPrivateServiceConnectSvc;
  private final AtlasPremiumMonitoringSvc _atlasPremiumMonitoringSvc;
  private final GCPPrivateServiceConnectionRuleDao _gcpPrivateServiceConnectionRuleDao;
  private final FastSharedPreAllocatedRecordsDao _fastSharedPreAllocatedRecordsDao;
  private final FastFlexPreAllocatedRecordsDao _fastFlexPreAllocatedRecordsDao;
  private final FastServerlessPreAllocatedRecordsDao _fastServerlessPreAllocatedRecordsDao;
  private final AWSTenantEndpointServiceDeploymentSvc _awsTenantEndpointServiceDeploymentSvc;
  private final AzureTenantEndpointServiceDeploymentSvc _azureTenantEndpointServiceDeploymentSvc;
  private final MultiTenantEndpointServiceSvc _multiTenantEndpointServiceSvc;
  private final NDSTenantEndpointSvc _ndsTenantEndpointSvc;
  private final NDSPlanExecutorJobPrioritySvc _ndsPlanExecutorJobPrioritySvc;
  private final SegmentEventSvc _segmentEventSvc;
  private final RegionalOutageSvc _regionalOutageSvc;
  private final SearchDeploymentDescriptionSvc _searchDeploymentSvc;
  private final SearchInstanceSvc _searchInstanceSvc;
  private final NDSPlanningTracingSvc _ndsPlanningTracingSvc;
  private final NdsMaintenanceHistorySvc _ndsMaintenanceHistorySvc;
  private final AdminClusterLockSvc _adminClusterLockSvc;
  private final AdminActionSvc _adminActionSvc;
  private final MTMCompactionSvc _mtmCompactionSvc;
  private final TenantUpgradeStatusDao _tenantUpgradeStatusDao;
  private final RollingResyncSvc _rollingResyncSvc;
  private final AWSCheckCapacityRequestSvc _awsCheckCapacityRequestSvc;
  private final AzureCheckCapacityRequestSvc _azureCheckCapacityRequestSvc;
  private final FastTenantPreAllocatedRecordDaoProvider _fastTenantPreAllocatedRecordDaoProvider;
  private final UniformFrontendEnvoyConfigurationDao _uniformFrontendEnvoyConfigurationDao;
  private final AutoScalingContextDao _autoScalingContextDao;
  private final PredictiveAutoScalingContextSvc _predictiveAutoScalingContextSvc;
  private final OrganizationSvc _organizationSvc;
  private final StreamsPrivateLinkDao _streamsPrivateLinkDao;
  private final StreamsTransitGatewayResourceSharesDao _streamsTransitGatewayResourceSharesDao;
  private final VPCPeeringConnectionDao _vpcPeeringConnectionDao;
  private final FeatureFlagSvc _featureFlagSvc;
  private final ClusterDescriptionValidationUtil _clusterDescriptionValidationUtil;
  private final ServerlessUpgradeToDedicatedStatusDao _serverlessUpgradeToDedicatedStatusDao;
  public final ElevatedHealthMonitoringSvc _elevatedHealthMonitoringSvc;
  private final PredictiveAutoScalingTriggerSvc _predictiveAutoScalingTriggerSvc;
  private final AzureCapacityDenylistSvc _azureCapacityDenylistSvc;
  private final NDSPredictiveAutoScaleSvc _predictiveAutoScaleSvc;
  private final FlexMigrationSvc _flexMigrationSvc;
  private final CloudProviderAvailabilityFactory _cloudProviderAvailabilityFactory;
  private final AutoHealResyncSvc _autoHealResyncSvc;
  private final HostLastPingSvc _hostLastPingSvc;
  private final EolVersionUpgradeHistoryDao _eolVersionUpgradeHistoryDao;
  private final ShadowClusterExposureJobDao _shadowClusterExposureJobDao;
  private final MongotuneAppSettings _mongotuneAppSettings;

  /*
   * _planningLogger should only be used when initializing the logger prior to the group doPlanning.
   * Otherwise, reuse the pLogger passed into doPlanning as initializing a logger is fairly
   * expensive. See https://jira.mongodb.org/browse/CLOUDP-27907.
   */
  private final Logger _planningLogger;

  private static final String PLANNING_ROUND_GROUPS_GAUGE_NAME =
      "mms_nds_planner_round_size_groups";
  private static final String PLANNING_ROUND_GROUPS_GAUGE_DESCRIPTION =
      "Group IDs checked per planning round as gauge";
  private static final List<String> PLANNING_ROUND_GROUPS_GAUGE_LABELS =
      Collections.singletonList("type");

  private static final String TOTAL_GROUPS_NEED_PLANNING_GAUGE_NAME =
      "mms_nds_planner_groups_need_planning_total";
  private static final String TOTAL_GROUPS_NEED_PLANNING_GAUGE_DESCRIPTION =
      "Count of total groups that need planning, grouped by whether it has container and its state";
  private static final List<String> TOTAL_GROUPS_NEED_PLANNING_GAUGE_LABELS =
      List.of("has_container", "state");

  private static final String TOTAL_PLANS_ACTIVE_GAUGE_NAME = "mms_nds_planner_plans_active_total";
  private static final String TOTAL_PLANS_ACTIVE_GAUGE_DESCRIPTION =
      "Count of total plans are that are active (in progress or not started)";
  private static final List<String> TOTAL_PLANS_ACTIVE_GAUGE_LABELS =
      Collections.singletonList("status");

  private static final Counter MOVES_CREATED_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_moves_created_total",
          "Moves created by NDSPlanningSvc",
          "move_name",
          "next_planning_date_set_by");
  private static final Counter PLANS_CREATED_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_plans_created_total",
          "Plans created by NDSPlanningSvc",
          "action_type",
          "resource_type",
          "next_planning_date_set_by");
  private static final Counter PLAN_COMPLETED_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_plans_completed_total",
          "Completed plans and whether they failed, rolled back, etc.",
          "result",
          "rollback",
          "failure_code",
          "action_type",
          "resource_type");
  private static final Counter PLANNING_FAILURE_COUNTER =
      NDSPromMetricsSvc.registerStandardErrorCounter(
          "mms_nds_planner_planning_failures_total", "Failures while generating a plan");
  private static final Summary PLANNING_DURATION_SUMMARY =
      NDSPromMetricsSvc.registerStandardCronLatencySummary(
          "mms_nds_planner_planning_duration_seconds", "Time taken to plan for a group");
  private static final Summary PLANNING_LATENCY_SUMMARY =
      NDSPromMetricsSvc.registerSummary(
          "mms_nds_planner_planning_latency_seconds",
          "Lag behind schedule to plan for a group",
          List.of(
              getSummaryQuantile(0.99, 0.001),
              getSummaryQuantile(0.95, 0.01),
              getSummaryQuantile(0.50, 0.05)),
          "container_types");
  public static final Summary PLANNING_ROUND_DURATION_SUMMARY =
      NDSPromMetricsSvc.registerSummary(
          "mms_nds_planner_round_duration_seconds",
          "Time taken to complete one planning round",
          List.of(getSummaryQuantile(0.99, 0.001)),
          "planning_type");
  private static final Counter PLANNING_ROUND_GROUPS_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_round_groups_processed_total",
          "Total Group IDs checked by planning round");
  private static final Counter PLANNING_ROUND_THROTTLED_PLANS_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_round_plans_throttled_total", "Plans throttled in planning round");
  private static final Counter PLANNING_ROUND_GROUPS_NOT_PLANNED_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_round_groups_not_planned_total",
          "Count of Group IDs for which planner could not plan per planning round",
          "next_planning_date_set_by");
  private static final Counter MAINTENANCE_HISTORY_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_maintenance_history_total",
          "Count of maintenance history documents created",
          "maintenance_type",
          "state");
  private static final Histogram INSTANCE_HARDWARE_ACTION_DURATION_HISTOGRAM =
      NDSPromMetricsSvc.registerHistogram(
          "mms_nds_instance_hardware_action_duration_seconds",
          "Time spent in existing action before transitioning to updated action",
          PromMetricsSvc.getHistogramExpBucket(60, 2, 17),
          "existing_action",
          "updated_action",
          "isActionOptimized");
  private static final Counter PLANNING_JOB_QUEUE_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_job_queue_total", "Count of groups planned using job queue");
  private static final Counter PLANNING_JOB_ID_MISMATCH_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_job_id_mismatch_total",
          "Count of planning groups have mismatched jobId");

  protected static final Counter STUCK_PLAN_GENERATION_UNSET_TOTAL =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_stuck_plan_generation_unset_total",
          "Count of groups with stuck plan generation job unset");

  private static final Counter WORKING_PLAN_WITHOUT_ASSOCIATED_JOB_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_planner_working_plan_without_associated_job_total",
          "Count of working plans without associated job");

  private static final Counter CPS_SNAPSHOT_SKIPPED =
      NDSPromMetricsSvc.registerCounter(
          "mms_nds_cps_snapshots_skipped_total", "Count of total snapshots skipped", "reason");

  private static final Counter UNHANDLED_PLANNER_ERROR_COUNTER =
      NDSPromMetricsSvc.registerStandardErrorCounter(
          "mms_nds_planner_unhandled_planner_error_total", "Count of unhandled planner exceptions");

  private static final String LIVE_IMPORT = "LIVE_IMPORT";
  private static final String DO_PLANNING_METHOD_NAME = "doPlanning";
  private static final String PLAN_GENERATION_FAILURE_LABEL = "Plan Generation Failure";

  private static volatile NDSPlanningSvcCollector _ndsPlanningSvcCollector;

  protected static final Cache<String, Integer> _jobStatusToStuckCountCache =
      CacheBuilder.newBuilder()
          // +2 because we have two extra job status as label: JOB_ID_NOT_FOUND and JOB_NOT_FOUND
          .maximumSize(Job.Status.values().length + 2)
          .expireAfterWrite(Duration.ofMinutes(6))
          .build();
  protected static final Cache<String, Integer> _latestPlanningRoundGroupsGaugeCache =
      CacheBuilder.newBuilder()
          .maximumSize(2) // Two labels: EXISTING and CURRENT_ROUND
          .expireAfterWrite(Duration.ofMinutes(1))
          .build();
  protected static final Cache<Pair<String, String>, Integer>
      _latestTotalGroupsNeedPlanningGaugeCacheGrouped =
          CacheBuilder.newBuilder()
              .maximumSize(
                  NDSState.values().length * 2) // # of nds states x (has container or no container)
              .expireAfterWrite(Duration.ofMinutes(1))
              .build();

  protected static final Cache<String, Integer> _latestTotalPlansActiveGaugeCache =
      CacheBuilder.newBuilder()
          .maximumSize(2) // Two labels: IN_PROGRESS and NOT_STARTED
          .expireAfterWrite(Duration.ofMinutes(1))
          .build();

  @Inject
  public NDSPlanningSvc(
      final JobsProcessorSvc pJobSvc,
      final JobParkingSvc pJobParkingSvc,
      final NDSClusterSvc pClusterSvc,
      final ReplicaSetHardwareDao pHardwareDao,
      final NDSGroupDao pNDSGroupDao,
      final PlanDao pPlanDao,
      final PlanSvc pPlanSvc,
      final AgentPingSvc pAgentPingSvc,
      final HostSvc pHostSvc,
      final HostClusterLifecycleSvc pHostClusterLifecycleSvc,
      final GroupSvc pGroupSvc,
      final NDSLogDao pNDSLogDao,
      final AppSettings pAppSettings,
      final AuditSvc pAuditSvc,
      final InformationalAlertSvc pAlertSvc,
      final NDSEventDao pNDSEventDao,
      final ReplicaSetHardwareSvc pHardwareSvc,
      final NDSReactiveAutoScaleSvc pAutoScaleSvc,
      final NDSAutoResumeSvc pAutoResumeSvc,
      final NDSAutoRebootSvc pAutoRebootSvc,
      final NDSContainerPeerSvc pContainerPeerSvc,
      final BackupSnapshotDao pSnapshotDao,
      final QueuedAdminActionDao pQueuedAdminActionDao,
      final CpsSvc pCpsSvc,
      final CpsPitSvc pCpsPitSvc,
      final NDSContainerPeerDao pContainerPeerDao,
      final BackupJobDao pBackupJobDao,
      final SystemClusterJobDao pSystemClusterJobDao,
      final NDSEncryptionAtRestSvc pNDSEncryptionAtRestSvc,
      final NDSGroupSvc pNDSGroupSvc,
      final LiveImportSvc pLiveImportSvc,
      final NDSGroupMaintenanceSvc pNDSGroupMaintenanceSvc,
      final NDSMaintenanceDateCalculationUtil pMaintenanceDateCalculationUtil,
      final CpsPolicySvc pCpsPolicySvc,
      final NDSTenantSnapshotRestoreSvc pTenantSnapshotRestoreSvc,
      final IngestionPipelineSvc pIngestionPipelineSvc,
      final NDSACMESvc pNDSACMESvc,
      final AWSMultiTargetConnectionRuleDao pAWSMultiTargetConnectionRuleDao,
      final AWSPrivateLinkTargetGroupDao pAWSPrivateLinkTargetGroupDao,
      final AzurePrivateLinkConnectionInboundNATRuleDao
          pAzurePrivateLinkConnectionInboundNATRuleDao,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final NDSTenantPauseSvc pNdsTenantPauseSvc,
      final AutoIndexingSvc pAutoIndexingSvc,
      final CronStateDao pCronStateDao,
      final NDSPlanExecutorCallbackSvc pPlanCallbackSvc,
      final ServerlessLoadBalancingDeploymentDao pServerlessLoadBalancingDeploymentDao,
      final ServerlessLoadBalancingDeploymentSvc pServerlessLoadBalancingDeploymentSvc,
      final ServerlessMTMPoolSvc pServerlessMTMPoolSvc,
      final GCPPrivateServiceConnectSvc pGcpPrivateServiceConnectSvc,
      final AtlasPremiumMonitoringSvc pAtlasPremiumMonitoringSvc,
      final GCPPrivateServiceConnectionRuleDao pGcpPrivateServiceConnectionRuleDao,
      final FastSharedPreAllocatedRecordsDao pFastSharedPreAllocatedRecordsDao,
      final FastFlexPreAllocatedRecordsDao pFastFlexPreAllocatedRecordsDao,
      final FastServerlessPreAllocatedRecordsDao pFastServerlessPreAllocatedRecordsDao,
      final AWSTenantEndpointServiceDeploymentSvc pAWSTenantEndpointServiceDeploymentSvc,
      final AzureTenantEndpointServiceDeploymentSvc pAzureTenantEndpointServiceDeploymentSvc,
      final MultiTenantEndpointServiceSvc pMultiTenantEndpointServiceSvc,
      final NDSTenantEndpointSvc pNdsTenantEndpointSvc,
      final NDSPlanExecutorJobPrioritySvc pNDSPlanExecutorJobPrioritySvc,
      final SegmentEventSvc pSegmentEventSvc,
      final RegionalOutageSvc pRegionalOutageSvc,
      final SearchDeploymentDescriptionSvc pSearchDeploymentDescriptionSvc,
      final SearchInstanceSvc pSearchInstanceSvc,
      final NDSPlanningTracingSvc pNDSPlanningTracingSvc,
      final NdsMaintenanceHistorySvc pNdsMaintenanceHistorySvc,
      final AdminClusterLockSvc pAdminClusterLockSvc,
      final MTMCompactionSvc pMTMCompactionSvc,
      final TenantUpgradeStatusDao pTenantUpgradeStatusDao,
      final AWSCheckCapacityRequestSvc pAWSCheckCapacityRequestSvc,
      final RollingResyncSvc pRollingResyncSvc,
      final AzureCheckCapacityRequestSvc pAzureCheckCapacityRequestSvc,
      final FastTenantPreAllocatedRecordDaoProvider pFastTenantPreAllocatedRecordDaoProvider,
      final UniformFrontendEnvoyConfigurationDao pUniformFrontendEnvoyConfigurationDao,
      final AutoScalingContextDao pAutoScalingContextDao,
      final PredictiveAutoScalingContextSvc pPredictiveAutoScalingContextSvc,
      final AdminActionSvc pAdminActionSvc,
      final OrganizationSvc pOrganizationSvc,
      final StreamsPrivateLinkDao pStreamsPrivateLinkDao,
      final StreamsTransitGatewayResourceSharesDao pStreamsTransitGatewayResourceSharesDao,
      final VPCPeeringConnectionDao pStreamsVpcPeeringConnectionDao,
      final FeatureFlagSvc pFeatureFlagSvc,
      final ClusterDescriptionValidationUtil pClusterDescriptionValidationUtil,
      final ServerlessUpgradeToDedicatedStatusDao pServerlessUpgradeToDedicatedStatusDao,
      final ElevatedHealthMonitoringSvc pElevatedHealthMonitoringSvc,
      final PredictiveAutoScalingTriggerSvc pPredictiveAutoScalingTriggerSvc,
      final AzureCapacityDenylistSvc pAzureCapacityDenylistSvc,
      final NDSPredictiveAutoScaleSvc pPredictiveAutoScaleSvc,
      final FlexMigrationSvc pFlexMigrationSvc,
      final IMTMClusterSvc pMTMClusterSvc,
      final CloudProviderAvailabilityFactory pCloudProviderAvailabilityFactory,
      final AutoHealResyncSvc pAutoHealResyncSvc,
      final HostLastPingSvc pHostLastPingSvc,
      final EolVersionUpgradeHistoryDao pEolVersionUpgradeHistoryDao,
      final ShadowClusterExposureJobDao pShadowClusterExposureJobDao,
      final MongotuneAppSettings pMongotuneAppSettings) {
    _jobSvc = pJobSvc;
    _jobParkingSvc = pJobParkingSvc;
    _clusterSvc = pClusterSvc;
    _hardwareDao = pHardwareDao;
    _ndsGroupDao = pNDSGroupDao;
    _planDao = pPlanDao;
    _planSvc = pPlanSvc;
    _agentPingSvc = pAgentPingSvc;
    _hostSvc = pHostSvc;
    _hostClusterLifecycleSvc = pHostClusterLifecycleSvc;
    _groupSvc = pGroupSvc;
    _ndsLogDao = pNDSLogDao;
    _appSettings = pAppSettings;
    _alertSvc = pAlertSvc;
    _auditSvc = pAuditSvc;
    _ndsEventDao = pNDSEventDao;
    _hardwareSvc = pHardwareSvc;
    _containerPeerSvc = pContainerPeerSvc;
    _autoScaleSvc = pAutoScaleSvc;
    _autoResumeSvc = pAutoResumeSvc;
    _autoRebootSvc = pAutoRebootSvc;
    _snapshotDao = pSnapshotDao;
    _queuedAdminActionDao = pQueuedAdminActionDao;
    _cpsSvc = pCpsSvc;
    _cpsPitSvc = pCpsPitSvc;
    _containerPeerDao = pContainerPeerDao;
    _backupJobDao = pBackupJobDao;
    _systemClusterJobDao = pSystemClusterJobDao;
    _ndsEncryptionAtRestSvc = pNDSEncryptionAtRestSvc;
    _ndsGroupSvc = pNDSGroupSvc;
    _liveImportSvc = pLiveImportSvc;
    _ndsGroupMaintenanceSvc = pNDSGroupMaintenanceSvc;
    _maintenanceDateCalculationUtil = pMaintenanceDateCalculationUtil;
    _cpsPolicySvc = pCpsPolicySvc;
    _tenantSnapshotRestoreSvc = pTenantSnapshotRestoreSvc;
    _ingestionPipelineSvc = pIngestionPipelineSvc;
    _ndsACMESvc = pNDSACMESvc;
    _awsMultiTargetConnectionRuleDao = pAWSMultiTargetConnectionRuleDao;
    _awsPrivateLinkTargetGroupDao = pAWSPrivateLinkTargetGroupDao;
    _azurePrivateLinkConnectionInboundNATRuleDao = pAzurePrivateLinkConnectionInboundNATRuleDao;
    _automationConfigSvc = pAutomationConfigSvc;
    _ndsTenantPauseSvc = pNdsTenantPauseSvc;
    _autoIndexingSvc = pAutoIndexingSvc;
    _cronStateDao = pCronStateDao;
    _planCallbackSvc = pPlanCallbackSvc;
    _serverlessLoadBalancingDeploymentDao = pServerlessLoadBalancingDeploymentDao;
    _serverlessLoadBalancingDeploymentSvc = pServerlessLoadBalancingDeploymentSvc;
    _serverlessMTMPoolSvc = pServerlessMTMPoolSvc;
    _gcpPrivateServiceConnectSvc = pGcpPrivateServiceConnectSvc;
    _atlasPremiumMonitoringSvc = pAtlasPremiumMonitoringSvc;
    _gcpPrivateServiceConnectionRuleDao = pGcpPrivateServiceConnectionRuleDao;
    _fastSharedPreAllocatedRecordsDao = pFastSharedPreAllocatedRecordsDao;
    _fastFlexPreAllocatedRecordsDao = pFastFlexPreAllocatedRecordsDao;
    _fastServerlessPreAllocatedRecordsDao = pFastServerlessPreAllocatedRecordsDao;
    _awsTenantEndpointServiceDeploymentSvc = pAWSTenantEndpointServiceDeploymentSvc;
    _azureTenantEndpointServiceDeploymentSvc = pAzureTenantEndpointServiceDeploymentSvc;
    _multiTenantEndpointServiceSvc = pMultiTenantEndpointServiceSvc;
    _ndsTenantEndpointSvc = pNdsTenantEndpointSvc;
    _ndsPlanExecutorJobPrioritySvc = pNDSPlanExecutorJobPrioritySvc;
    _segmentEventSvc = pSegmentEventSvc;
    _regionalOutageSvc = pRegionalOutageSvc;
    _searchDeploymentSvc = pSearchDeploymentDescriptionSvc;
    _searchInstanceSvc = pSearchInstanceSvc;
    _ndsPlanningTracingSvc = pNDSPlanningTracingSvc;
    _ndsMaintenanceHistorySvc = pNdsMaintenanceHistorySvc;
    _adminClusterLockSvc = pAdminClusterLockSvc;
    _mtmCompactionSvc = pMTMCompactionSvc;
    _tenantUpgradeStatusDao = pTenantUpgradeStatusDao;
    _awsCheckCapacityRequestSvc = pAWSCheckCapacityRequestSvc;
    _rollingResyncSvc = pRollingResyncSvc;
    _azureCheckCapacityRequestSvc = pAzureCheckCapacityRequestSvc;
    _fastTenantPreAllocatedRecordDaoProvider = pFastTenantPreAllocatedRecordDaoProvider;
    _uniformFrontendEnvoyConfigurationDao = pUniformFrontendEnvoyConfigurationDao;
    _autoScalingContextDao = pAutoScalingContextDao;
    _predictiveAutoScalingContextSvc = pPredictiveAutoScalingContextSvc;
    _adminActionSvc = pAdminActionSvc;
    _organizationSvc = pOrganizationSvc;
    _streamsPrivateLinkDao = pStreamsPrivateLinkDao;
    _streamsTransitGatewayResourceSharesDao = pStreamsTransitGatewayResourceSharesDao;
    _vpcPeeringConnectionDao = pStreamsVpcPeeringConnectionDao;
    _featureFlagSvc = pFeatureFlagSvc;
    _clusterDescriptionValidationUtil = pClusterDescriptionValidationUtil;
    _serverlessUpgradeToDedicatedStatusDao = pServerlessUpgradeToDedicatedStatusDao;
    _elevatedHealthMonitoringSvc = pElevatedHealthMonitoringSvc;
    _predictiveAutoScalingTriggerSvc = pPredictiveAutoScalingTriggerSvc;
    _azureCapacityDenylistSvc = pAzureCapacityDenylistSvc;
    _predictiveAutoScaleSvc = pPredictiveAutoScaleSvc;
    _flexMigrationSvc = pFlexMigrationSvc;
    _cloudProviderAvailabilityFactory = pCloudProviderAvailabilityFactory;
    _autoHealResyncSvc = pAutoHealResyncSvc;
    _hostLastPingSvc = pHostLastPingSvc;
    _eolVersionUpgradeHistoryDao = pEolVersionUpgradeHistoryDao;
    _shadowClusterExposureJobDao = pShadowClusterExposureJobDao;
    _mongotuneAppSettings = pMongotuneAppSettings;

    // NOTE: The NDSMongoAppender was removed to reduce load on the ndslogs database
    _planningLogger = LoggerFactory.getLogger(NDSPlanningSvc.class.getName() + "-GroupPlanning");
  }

  @PostConstruct
  public void start() {
    if (!_appSettings.isMMSCronEnabled()) {
      // No need to register the collector with Prometheus if there's no chance this JVM will run
      // the planner.
      return;
    }

    // Register with Prom
    if (_ndsPlanningSvcCollector == null) {
      _ndsPlanningSvcCollector =
          new NDSPlanningSvcCollector(_cronStateDao, _jobParkingSvc, _appSettings).register();
    }
  }

  @PreDestroy
  public void stop() {
    // Unregister from Prom
    if (_ndsPlanningSvcCollector != null) {
      CollectorRegistry.defaultRegistry.unregister(_ndsPlanningSvcCollector);
    }
  }

  public void doPlanning() {
    final int jobQueueBatchingGroupSize =
        _appSettings.getIntProp(JOB_QUEUE_BATCHING_GROUP_SIZE_PARAM, 100);
    final SimpleBatchProcessor<ObjectId> batchProcessor =
        new SimpleBatchProcessor<>(
            _planningLogger, jobQueueBatchingGroupSize, this::batchSubmitGroupsForPlanning);

    try (final SimpleBatchProcessor<ObjectId>.Batcher batcher = batchProcessor.startBatch()) {
      LOG.debug("Starting a planning round");
      final Date planningRoundStartDate = new Date();

      final Set<ObjectId> groupIds = getGroupsNeedPlanning(planningRoundStartDate);

      for (final ObjectId groupId : groupIds) {
        // submit to job queue
        batcher.offer(groupId);
      }
      _latestPlanningRoundGroupsGaugeCache.put(TYPE_CURRENT_ROUND_LABEL_VALUE, groupIds.size());

      final long runtimeMS = System.currentTimeMillis() - planningRoundStartDate.getTime();
      NDSPromMetricsSvc.recordTimer(
          PLANNING_ROUND_DURATION_SUMMARY, runtimeMS / 1000.0, PLANNING_TYPE_CRON_JOB_LABEL_VALUE);

      LOG.debug(
          "Completed a round for submitting {} groups for planning in {} milliseconds",
          groupIds.size(),
          runtimeMS);
    } catch (final MongoInterruptedException e) {
      Thread.currentThread().interrupt();
      LOG.debug("Interrupted during planning, shutting down early", e);
    } catch (final RuntimeException e) {
      if (e instanceof IllegalStateException && e.getMessage().contains("Injector closed")) {
        LOG.debug("Handled exception during planning", e);
      } else {
        LOG.error("Unhandled exception during planning", e);
        NDSPromMetricsSvc.incrementStandardErrorCounter(UNHANDLED_PLANNER_ERROR_COUNTER, e);
      }
    }
  }

  @VisibleForTesting
  public void doPlanningForGroup(
      final Date pPlanningRoundStartDate,
      final ObjectId pGroupId,
      @Nullable final ObjectId pPlanGenerationJobId) {
    try {
      MDC.put(MDCUtil.GROUP_ID, pGroupId.toString());
      final NDSGroup group = _ndsGroupDao.find(pGroupId).get();
      final Date currentPlanningRoundDate = group.getNextPlanningDate();
      final Date currentGroupPlanningStartDate = new Date();

      if (!Objects.equals(pPlanGenerationJobId, group.getPlanGenerationJobId().orElse(null))) {
        _planningLogger.error(
            "PlanGenerationJobId {} in group does not match passed in jobId {}. Skipping planning.",
            group.getPlanGenerationJobId().orElse(null),
            pPlanGenerationJobId);
        NDSPromMetricsSvc.incrementCounter(PLANNING_JOB_ID_MISMATCH_COUNTER);
        return;
      }

      emitPlanningLatency(
          group, _planningLogger, currentGroupPlanningStartDate, currentPlanningRoundDate);
      NDSPromMetricsSvc.incrementCounter(PLANNING_ROUND_GROUPS_COUNTER);

      NextPlanningDateContext.Builder nextPlanningDateContextBuilder =
          new NextPlanningDateContext.Builder(group);
      try {
        nextPlanningDateContextBuilder =
            withNDSPlanningTrace(getSpanAttributesByGroupId(pGroupId))
                .run(
                    () -> doPlanning(group, _planningLogger, pPlanGenerationJobId), "nds-planning");
      } catch (final MongoInterruptedException e) {
        Thread.currentThread().interrupt();
        _planningLogger.info("Interrupted during planning, shutting down early", e);
        return;
      } catch (final Throwable e) {
        if (e instanceof IllegalStateException && e.getMessage().contains("Injector closed")) {
          LOG.debug("Handled exception during planning", e);
          return;
        } else if (Static.isTransientMongoException(e)) {
          LOG.warn("Encountered transient mongo exception", e);
          savePlanningFailureAudit(pGroupId, e);

          nextPlanningDateContextBuilder.setExceptionDuringPlanningRoundInterval(
              getNDSPlannerTransientExceptionInterval());
        } else {
          _planningLogger.error("Unhandled exception while planning", e);
          savePlanningFailureAudit(pGroupId, e);

          NDSPromMetricsSvc.incrementStandardErrorCounter(PLANNING_FAILURE_COUNTER, e);

          nextPlanningDateContextBuilder.setExceptionDuringPlanningRoundInterval(
              getNDSPlannerUnhandledExceptionInterval());

          sendEventToSentry(pGroupId, e);
        }
      }

      completePlanningWithNextPlanningDate(
          pGroupId,
          nextPlanningDateContextBuilder.build(),
          pPlanningRoundStartDate,
          currentPlanningRoundDate,
          pPlanGenerationJobId);
    } finally {
      MDC.remove(MDCUtil.GROUP_ID);
    }
  }

  private void completePlanningWithNextPlanningDate(
      final ObjectId pGroupId,
      final NextPlanningDateContext pNextPlanningDateContext,
      final Date pPlanningRoundStartDate,
      final Date currentPlanningRoundDate,
      final ObjectId pPlanGenerationJobId) {
    try {
      final Pair<Date, NextPlanningDateSetBy> nextGroupPlanningAndSetBy =
          getNextGroupPlanningDate(
              pNextPlanningDateContext, pPlanningRoundStartDate, _planningLogger);

      final boolean modified =
          _ndsGroupDao.completePlanning(
              pGroupId,
              currentPlanningRoundDate,
              nextGroupPlanningAndSetBy.getLeft(),
              nextGroupPlanningAndSetBy.getRight());
      _planningLogger.debug(
          "{} planning for  jobId: {}",
          modified ? "Successfully completed" : "Attempted but skipped completing",
          pPlanGenerationJobId);
    } catch (final Throwable e) {
      final boolean modified =
          _ndsGroupDao.completePlanning(
              pGroupId,
              currentPlanningRoundDate,
              new Date(
                  currentPlanningRoundDate.getTime() + getNDSPlannerUnhandledExceptionInterval()),
              PLANNER_EXCEPTION_DURING_ROUND);
      _planningLogger.warn(
          "{} planning after first failed try for jobId: {}",
          modified ? "Successfully completed" : "Attempted but skipped completing",
          pPlanGenerationJobId,
          e);
    }
  }

  @VisibleForTesting
  void sendEventToSentry(final ObjectId groupId, final Throwable e) {
    final PlanFailureSentryEvent sentryEvent =
        PlanFailureContextBuilder.from(e)
            .withGroupId(groupId.toString())
            .withLogClass(NDSPlanningSvc.class.getSimpleName())
            .withLogMethod(DO_PLANNING_METHOD_NAME)
            .withLogBaseMessage(PLAN_GENERATION_FAILURE_LABEL)
            .buildSentryEvent();
    sentryEvent.sendToSentry();
  }

  @VisibleForTesting
  void savePlanningFailureAudit(final ObjectId groupId, final Throwable e) {
    try {
      saveAudit(
          Type.PLANNING_FAILURE,
          groupId,
          Result.PlanFailureCode.NONE,
          Optional.empty(),
          e.toString());
    } catch (final Throwable t) {
      _planningLogger.error("Unhandled exception while saving planning failure", t);
    }
  }

  protected Optional<Date> getTenantCertsNeedUpdateDate(
      final NextPlanningDateContext pNextPlanningDateContext) {
    final List<Cluster> activeClusters = pNextPlanningDateContext.getActiveClusters();
    final NDSGroup ndsGroup = pNextPlanningDateContext.getNdsGroup();
    final List<Cluster> proxiedClusters =
        NDSClusterSvc.getProxiedClustersForACMECertUpdate(ndsGroup, activeClusters);
    final List<Cluster> certificateEligibleTenantClusters =
        NDSClusterSvc.getCertificateEligibleTenantClusters(proxiedClusters);

    final List<NDSACMECert> acmeCertificatesForTenantClusters =
        pNextPlanningDateContext.getAcmeCertificatesForTenantClusters();

    if (certificateEligibleTenantClusters.isEmpty()
        || acmeCertificatesForTenantClusters.isEmpty()) {
      return Optional.empty();
    }
    final Map<SubdomainLevel, List<Cluster>> clustersBySubdomain =
        groupClustersBySubdomainLevel(certificateEligibleTenantClusters);
    final Map<SubdomainLevel, List<NDSACMECert>> groupCertsBySubdomain =
        groupCertsBySubdomain(acmeCertificatesForTenantClusters, _appSettings);

    final AtomicReference<Optional<Date>> minimumCertExpirationDate =
        new AtomicReference<>(Optional.empty());
    clustersBySubdomain.forEach(
        (subdomainLevel, clusters) -> {
          final Map<String, List<Cluster>> clustersByDnsPin =
              groupClustersByDnsPin(clusters, ndsGroup.getDNSPin());
          final Map<String, List<NDSACMECert>> certsByDnsPin =
              groupCertsByDnsPin(groupCertsBySubdomain.getOrDefault(subdomainLevel, List.of()));

          clustersByDnsPin
              .keySet()
              .forEach(
                  dnsPin -> {
                    final Optional<Date> mostRecentlyIssuedCertExpirationDate =
                        certsByDnsPin.getOrDefault(dnsPin, List.of()).stream()
                            .max(Comparator.comparing(ACMECert::getExpiresAt))
                            .map(ACMECert::getExpiresAt);
                    mostRecentlyIssuedCertExpirationDate.ifPresent(
                        certExpirationDate ->
                            minimumCertExpirationDate.set(
                                minimumCertExpirationDate
                                    .get()
                                    .filter(d -> d.before(certExpirationDate))
                                    .or(() -> mostRecentlyIssuedCertExpirationDate)));
                  });
        });

    return minimumCertExpirationDate.get();
  }

  @VisibleForTesting
  protected Pair<Date, NextPlanningDateSetBy> getNextGroupPlanningDate(
      final NextPlanningDateContext pNextPlanningDateContext,
      final Date pNow,
      final Logger pGroupLogger) {
    final NDSGroup ndsGroupAfterPlanning = pNextPlanningDateContext.getNdsGroup();
    final Optional<Group> group = pNextPlanningDateContext.getGroup();

    final Pair<NextPlanningDateSetBy, Long> nextPlanningInterval =
        getNextPlanningInterval(pNextPlanningDateContext, pNow, pGroupLogger);
    final Date nextPlanningIntervalDate =
        new Date(pNow.getTime() + nextPlanningInterval.getRight());

    final Optional<Date> nextSnapshotDate =
        ndsGroupAfterPlanning.hasInUseCloudProviderContainerNotOfTypes(
                Set.of(CloudProvider.FREE, CloudProvider.SERVERLESS))
            ? _backupJobDao.getNextSnapshotDateForProject(ndsGroupAfterPlanning.getGroupId(), pNow)
            : Optional.empty();

    final Optional<Date> minUserExpirationDate =
        ndsGroupAfterPlanning.getDatabaseUsers().stream()
            .map(NDSDBUser::getDeleteAfterDate)
            .flatMap(Optional::stream)
            .min(Date::compareTo);

    final Optional<Date> minIPWhitelistExpirationDate =
        ndsGroupAfterPlanning.getNetworkPermissionList().getNetworkPermissions().stream()
            .map(NDSNetworkPermission::getDeleteAfterDate)
            .flatMap(JsonOptional::stream)
            .min(Date::compareTo);

    // Take the next maintenance date into consideration as well for the next planning date.
    Optional<Date> nextMaintenanceDate = Optional.empty();
    if (group.isPresent()) {
      final boolean hasNonTenantContainersInUse =
          ndsGroupAfterPlanning.hasInUseCloudProviderContainerNotOfTypes(
              Arrays.stream(CloudProvider.values())
                  .filter(CloudProvider::isTenantProvider)
                  .collect(Collectors.toSet()));
      if (hasNonTenantContainersInUse) {
        nextMaintenanceDate = getNextMaintenanceDate(pNow, pNextPlanningDateContext);
      }
    }

    // In some cases when a cluster is un-paused after some time of inactivity there is a
    // possibility that getTenantCertsNeedUpdateDate returns a date in the past. We don't want to
    // consider this date in the past since a plan would already be in place to address that and
    // this would report incorrect planning lag as well. As we create the cert 14 days in advance of
    // its expiry the next planning date thus should be adjusted accordingly
    final Optional<Date> tenantCertsNeedUpdateDateInFuture =
        getTenantCertsNeedUpdateDate(pNextPlanningDateContext)
            .map(
                date -> {
                  final Date certExpiryRotationDate =
                      Date.from(date.toInstant().minus(ACME_TENANT_CERT_EXPIRY_ROTATION_THRESHOLD));
                  return certExpiryRotationDate.before(pNow) ? null : certExpiryRotationDate;
                });

    final Optional<Date> nextPlanningDateForCertExpiration =
        getNextPlanningDateForCertExpiration(pNextPlanningDateContext);
    final Optional<Date> nextClusterAccessRevokeDate =
        getNextClusterAccessRevokeDate(pNextPlanningDateContext, pNow);
    final Optional<Date> nextMinimumNodeRebootRequestedDate =
        getNextMinimumNodeRebootRequestedDate(pNextPlanningDateContext);
    final Optional<Date> advancedNotificationSendDate =
        getAdvancedNotificationSendDate(ndsGroupAfterPlanning, group);
    final Optional<Date> minDeleteAfterDate =
        MathUtils.isHashedIdWithinPercentage(
                ndsGroupAfterPlanning.getGroupId(),
                _appSettings.getDoubleProp(
                    NDS_PLANNER_CONSIDER_DELETE_AFTER_DATE_ROLLOUT_PERCENTAGE, 0))
            ? getFutureDeleteAfterDate(pNextPlanningDateContext)
            : Optional.empty();
    final var atlasKmipKeyRotationInMaintenanceWindow =
        FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_KMIP_KEY_ROTATION_IN_MAINTENANCE_WINDOW,
            _appSettings,
            null,
            group.orElse(null));
    final Optional<Date> nextKmipMasterKeyRotationDate =
        atlasKmipKeyRotationInMaintenanceWindow
            ? Optional.empty()
            : getNextKmipMasterKeyRotationDate(pNextPlanningDateContext);
    final Optional<Date> nextAutoResumeDate =
        getNextAutoResumeDate(pNextPlanningDateContext, pNow, pGroupLogger);
    final Optional<Date> nextAllSnapshotsPurgedDate =
        getNDSPlannerNextGroupPlanningDateAllSnapshotsPurgedDateEnabled()
            ? getNextAllSnapshotsPurgedDate(pNextPlanningDateContext, pNow, pGroupLogger)
            : Optional.empty();
    final Optional<Date> nextPlanningDateForAutoScaling =
        getNextPlanningDateForShardAutoScaling(
            ndsGroupAfterPlanning.getGroupId(), pNextPlanningDateContext.getActiveClusters());
    final Optional<Date> minQueuedAdminActionScheduledByDate =
        _appSettings.isQueuedAdminActionsEnabled()
            ? getMinQueuedAdminActionScheduledByDate(
                ndsGroupAfterPlanning.getGroupId(), pGroupLogger)
            : Optional.empty();
    final Optional<Date> nextPlanningDateForPredictiveAutoScaling =
        getNextPredictiveAutoScalingTime(pNextPlanningDateContext, pNow);

    Date minDate =
        Stream.of(
                Optional.of(nextPlanningIntervalDate),
                minDeleteAfterDate,
                tenantCertsNeedUpdateDateInFuture,
                nextSnapshotDate,
                minUserExpirationDate,
                minIPWhitelistExpirationDate,
                nextMaintenanceDate,
                nextClusterAccessRevokeDate,
                nextKmipMasterKeyRotationDate,
                nextAutoResumeDate,
                nextAllSnapshotsPurgedDate,
                nextPlanningDateForCertExpiration,
                nextMinimumNodeRebootRequestedDate,
                advancedNotificationSendDate,
                nextPlanningDateForAutoScaling,
                minQueuedAdminActionScheduledByDate,
                nextPlanningDateForPredictiveAutoScaling)
            .flatMap(Optional::stream)
            .min(Date::compareTo)
            .get();

    NextPlanningDateSetBy nextPlanningDateSetBy = nextPlanningInterval.getLeft();
    if (minDate.before(nextPlanningIntervalDate)) {
      nextPlanningDateSetBy = NextPlanningDateSetBy.PLANNER_FUTURE_TASKS;
    }

    // consider any critical maintenance work that needs to be scheduled
    final Optional<Date> minCriticalReleaseTimeSlot =
        pNextPlanningDateContext.getMinCriticalReleaseTimeSlot();
    if (minCriticalReleaseTimeSlot.isPresent()
        && minCriticalReleaseTimeSlot.get().before(minDate)) {
      minDate = minCriticalReleaseTimeSlot.get();
      nextPlanningDateSetBy = NextPlanningDateSetBy.CRITICAL_MAINTENANCE;
    }

    // If the group is dead, set the next planning date to a far future date to avoid it getting
    // picked up by the planner since DEAD groups are inactive groups
    if (ndsGroupAfterPlanning.getState() == NDSState.DEAD) {
      minDate = getNextPlanningDateForDeadGroups();
      pGroupLogger.info(
          "Setting next planning date far in the future to {} for dead group.", minDate);
    }

    pGroupLogger.info(
        "Group's nextPlanningDate was set to {} by {}. ", minDate, nextPlanningDateSetBy.name());

    final Map<String, Object> traceLogParams = new HashMap<>();
    traceLogParams.put("minCriticalReleaseTimeSlot", minCriticalReleaseTimeSlot);
    traceLogParams.put("nextPlanningIntervalDate", nextPlanningIntervalDate);
    traceLogParams.put("minDeleteAfterDate", minDeleteAfterDate);

    traceLogParams.put("tenantCertsNeedUpdateDateInFuture", tenantCertsNeedUpdateDateInFuture);
    traceLogParams.put("nextSnapshotDate", nextSnapshotDate);
    traceLogParams.put("minUserExpirationDate", minUserExpirationDate);

    traceLogParams.put("minIPWhitelistExpirationDate", minIPWhitelistExpirationDate);
    traceLogParams.put("nextMaintenanceDate", nextMaintenanceDate);
    traceLogParams.put("nextClusterAccessRevokeDate", nextClusterAccessRevokeDate);

    if (!atlasKmipKeyRotationInMaintenanceWindow) {
      traceLogParams.put("nextKmipMasterKeyRotationDate", nextKmipMasterKeyRotationDate);
    }

    traceLogParams.put("nextAutoResumeDate", nextAutoResumeDate);

    traceLogParams.put("nextAllSnapshotsPurgedDate", nextAllSnapshotsPurgedDate);
    traceLogParams.put("nextPlanningDateForCertExpiration", nextPlanningDateForCertExpiration);
    traceLogParams.put("nextMinimumNodeRebootRequestedDate", nextMinimumNodeRebootRequestedDate);

    traceLogParams.put("advancedNotificationSendDate", advancedNotificationSendDate);
    traceLogParams.put("nextPlanningDateForAutoscaling", nextPlanningDateForAutoScaling);
    traceLogParams.put(
        "nextPlanningDateForPredictiveAutoscaling", nextPlanningDateForPredictiveAutoScaling);
    traceLogParams.put("minQueuedAdminActionScheduledByDate", minQueuedAdminActionScheduledByDate);

    pGroupLogger.trace("Group's nextPlanningDate calculation considered: {}", traceLogParams);

    return Pair.of(minDate, nextPlanningDateSetBy);
  }

  protected Optional<Date> getNextMaintenanceDate(
      final Date pNow, final NextPlanningDateContext pContext) {

    final NDSGroup ndsGroup = pContext.getNdsGroup();
    final Group group = pContext.getGroup().orElseThrow();
    if (_ndsGroupMaintenanceSvc.shouldStartMaintenanceForGroup(
        ndsGroup,
        group,
        SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
        "GET_NEXT_MAINTENANCE_DATE",
        LOG)) {
      // inside the maintenance window, so next maintenance date is now + splay
      return Optional.of(getNextPlannerGroupIntervalMaintenanceDate(pNow));
    }

    if (pContext.getRespectProtectedHoursMaintenanceNeeded()
        && _ndsGroupMaintenanceSvc.shouldStartMaintenanceForGroup(
            ndsGroup,
            group,
            SchedulingBehavior.RESPECT_PROTECTED_HOURS,
            "GET_NEXT_MAINTENANCE_DATE",
            LOG)) {
      // protected hours maintenance is able to start now, so next maintenance date is now + splay
      return Optional.of(getNextPlannerGroupIntervalMaintenanceDate(pNow));
    }

    return Optional.of(
        new Date(
            _ndsGroupMaintenanceSvc
                    .getNextMaintenanceStartDateTimeForNonCriticalMaintenance(ndsGroup, group)
                    .getTimeInMillis()
                + addSplayToIntervalForNextMaintenanceDate(0)));
  }

  private Date getNextPlannerGroupIntervalMaintenanceDate(final Date pNow) {
    return new Date(
        pNow.getTime()
            + addSplayToIntervalForNextMaintenanceDate(
                _appSettings.getNdsPlannerGroupIntervalWhileUnderMaintenance()));
  }

  @VisibleForTesting
  protected Optional<Date> getFutureDeleteAfterDate(
      final NextPlanningDateContext pNextPlanningDateContext) {
    return pNextPlanningDateContext.getActiveClusters().stream()
        .map(Cluster::getClusterDescription)
        .filter(clusterDescription -> !clusterDescription.isDeleteRequested())
        .filter(clusterDescription -> clusterDescription.getDeleteAfterDate().isPresent())
        .filter(
            clusterDescription -> {
              boolean isDeleteAfterDateInPast =
                  clusterDescription.getDeleteAfterDate().get().before(new Date());
              if (isDeleteAfterDateInPast) {
                LOG.error(
                    "deleteAfterDate is in the past for group {}, and is not delete requested",
                    pNextPlanningDateContext.getNdsGroup().getGroupId());
              }
              return !isDeleteAfterDateInPast;
            })
        .map(cluster -> cluster.getDeleteAfterDate().get())
        .min(Date::compareTo);
  }

  @VisibleForTesting
  protected Optional<Date> getAdvancedNotificationSendDate(
      final NDSGroup pNdsGroupAfterPlanning, final Optional<Group> pGroup) {
    if (pGroup.isEmpty()) {
      return Optional.empty();
    }
    final Calendar nextMaintenanceStartDateTime =
        _ndsGroupMaintenanceSvc.getNextMaintenanceStartDateTime(
            pNdsGroupAfterPlanning, pGroup.get(), SchedulingBehavior.DURING_MAINTENANCE_WINDOW);

    // Go back 72 hours to get the time when an advanced notification can be sent
    nextMaintenanceStartDateTime.set(
        Calendar.HOUR_OF_DAY,
        nextMaintenanceStartDateTime.get(Calendar.HOUR_OF_DAY) - IN_ADVANCED_NOTIFICATION_HOURS);

    if (nextMaintenanceStartDateTime.getTime().before(new Date())) {
      return Optional.empty();
    }
    return Optional.of(nextMaintenanceStartDateTime.getTime());
  }

  @VisibleForTesting
  protected Optional<Date> getNextMinimumNodeRebootRequestedDate(
      final NextPlanningDateContext nextPlanningDateContext) {
    Optional<Date> minNodeRebootRequested =
        nextPlanningDateContext.getActiveClusters().stream()
            .flatMap(cluster -> cluster.getReplicaSets().stream())
            .flatMap(ReplicaSetHardware::getAllHardware)
            .map(InstanceHardware::getNextRestartOrRebootRequestedDate)
            .flatMap(Optional::stream)
            .min(Date::compareTo);

    if (minNodeRebootRequested.isEmpty()) {
      return Optional.empty();
    }

    final Date nextPlanningDate =
        DateUtils.addMinutes(minNodeRebootRequested.get(), NDSAutoRebootSvc.AUTO_REBOOT_MINUTES);
    if (nextPlanningDate.before(new Date())) {
      return Optional.empty();
    }

    return Optional.of(nextPlanningDate);
  }

  @VisibleForTesting
  protected Optional<Date> getNextKmipMasterKeyRotationDate(
      final NextPlanningDateContext pNextPlanningDateContext) {
    final NDSEncryptionAtRest encryptionAtRest =
        pNextPlanningDateContext.getNdsGroup().getEncryptionAtRest();

    final List<KeyManagementConfig> kmsConfigs =
        List.of(
            encryptionAtRest.getAWSKMS(),
            encryptionAtRest.getAzureKeyVault(),
            encryptionAtRest.getGoogleCloudKMS());

    return kmsConfigs.stream()
        .filter(KeyManagementConfig::isEnabled)
        .map(KeyManagementConfig::getLastKmipMasterKeyRotation)
        .flatMap(Optional::stream)
        .map(
            date ->
                DateUtils.addDays(
                    date, NDSEncryptionAtRestSvc.KMIP_MASTER_KEY_ROTATION_PERIOD_DAYS_OLD))
        .min(Date::compareTo);
  }

  @VisibleForTesting
  protected Optional<Date> getMinQueuedAdminActionScheduledByDate(
      final ObjectId pGroupId, final Logger pGroupLogger) {
    final Date now = new Date();
    final Optional<Date> minScheduledForDate = _queuedAdminActionDao.findMinScheduledFor(pGroupId);
    // minScheduledForDate should never be empty if bypass is true due to checks  in the svc layer
    if (minScheduledForDate.isEmpty()) {
      return Optional.empty();
    } else if (minScheduledForDate.get().after(now)) {
      return minScheduledForDate;
    } else {
      pGroupLogger.warn("queuedAdminAction scheduledFor is in the past for {}.", pGroupId);
      return Optional.of(now);
    }
  }

  @VisibleForTesting
  protected Optional<Date> getNextAutoResumeDate(
      final NextPlanningDateContext pNextPlanningDateContext,
      final Date pNow,
      final Logger pGroupLogger) {
    return pNextPlanningDateContext.getActiveClusters().stream()
        .map(Cluster::getClusterDescription)
        // automatically paused clusters will not be auto-resumed (see autoResumeNecessary
        // in NDSAutoResumeSvc)
        .filter(
            clusterDescription ->
                !clusterDescription.isAutomaticallyPaused()
                    && clusterDescription.isPaused()
                    && clusterDescription.getPausedDate().isPresent())
        // retain cluster name (needed for logs) and calculate auto resume day
        // (+AUTO_RESUME_DAYS from paused date)
        .map(
            clusterDescription ->
                Pair.of(
                    clusterDescription.getName(),
                    DateUtils.addDays(clusterDescription.getPausedDate().get(), AUTO_RESUME_DAYS)))
        // filter out dates that are in the past
        .filter(
            autoResumePair -> {
              final Date autoResumeDate = autoResumePair.getRight();
              final boolean isAutoResumeDateInPast = autoResumeDate.before(pNow);

              if (isAutoResumeDateInPast) {
                pGroupLogger.error(
                    "autoResumeDate {} for this cluster {} is in the past (before {}).",
                    autoResumeDate,
                    autoResumePair.getLeft(),
                    pNow);
                return false;
              }

              return true;
            })
        .map(Pair::getRight)
        // find the earliest remaining date
        .min(Date::compareTo);
  }

  @VisibleForTesting
  protected Optional<Date> getNextAllSnapshotsPurgedDate(
      final NextPlanningDateContext pNextPlanningDateContext,
      final Date pNow,
      final Logger pGroupLogger) {
    final NDSGroup ndsGroup = pNextPlanningDateContext.getNdsGroup();
    final Set<RegionName> regionNames =
        pNextPlanningDateContext.getRegionsWithSnapshotsAndSnapshotDistribution();

    if (regionNames == null) {
      return Optional.empty();
    }

    final List<Optional<Date>> finalScheduledDeletions = new ArrayList<>(regionNames.size());

    regionNames.forEach(
        regionName -> {
          final List<BackupSnapshot> backupSnapshots =
              _snapshotDao.findAllActiveSnapshotsByProjectAndRegion(
                  ndsGroup.getGroupId(), regionName);

          final Optional<Date> finalScheduledDeletionDate =
              backupSnapshots.stream()
                  .filter(
                      snapshot -> {
                        if (snapshot.getScheduledDeletionDate() == null) {
                          pGroupLogger.warn(
                              "Snapshot (id: {}) has null getScheduledDeletionDate",
                              snapshot.getId());
                        }
                        return snapshot.getScheduledDeletionDate() != null;
                      })
                  .map(
                      snapshot -> {
                        // if scheduled deletion date is in the past, then snapshot has not had the
                        // chance to be purged yet, so record a warning and record earliest deletion
                        // date as now, so that we will plan again the buffer period (default: 15
                        // minutes) after now
                        if (pNow.after(snapshot.getScheduledDeletionDate())) {
                          pGroupLogger.warn(
                              "Snapshot (id: {}) has scheduled deletion date ({}) in "
                                  + "past, but is still active (has not been purged). Region: {}, "
                                  + "Cloud Provider: {}.",
                              snapshot.getId(),
                              snapshot.getScheduledDeletionDate(),
                              regionName.getName(),
                              regionName.getProvider());
                          return pNow;
                        }
                        return snapshot.getScheduledDeletionDate();
                      })
                  .max(Date::compareTo);

          finalScheduledDeletions.add(finalScheduledDeletionDate);
        });

    // return the earliest of the final scheduled deletions because the container associated
    // with that region and group should be ready for deletion
    return finalScheduledDeletions.stream()
        .flatMap(Optional::stream)
        .min(Date::compareTo)
        .map(date -> new Date(date.getTime() + getNDSPlannerSnapshotDeletionBufferInterval()));
  }

  // For a free cluster access needs to be revoked 7 days after the cluster pause email was sent.
  // Return the minimum of all such "access to be revoked" dates after "now" - the planner will have
  // already generated a plan to revoke access if the date is before "now"
  @VisibleForTesting
  protected Optional<Date> getNextClusterAccessRevokeDate(
      final NextPlanningDateContext nextPlanningDateContext, final Date pNow) {
    return nextPlanningDateContext.getActiveClusters().stream()
        .filter(cluster -> cluster.getClusterDescription().isFreeTenantCluster())
        .map(cluster -> cluster.getClusterDescription().getAccessToBeRevokedDateForM0Cluster())
        .flatMap(Optional::stream)
        .filter(date -> date.after(pNow))
        .min(Comparator.naturalOrder());
  }

  @VisibleForTesting
  protected Optional<Date> getNextPredictiveAutoScalingTime(
      final NextPlanningDateContext nextPlanningDateContext, final Date now) {
    final List<ObjectId> clusterUniqueIdsWithPredictiveEnabled =
        nextPlanningDateContext.getActiveClusters().stream()
            .filter(
                cluster ->
                    cluster
                        .getClusterDescription()
                        .isPredictiveAutoScalingEnabled(NodeTypeFamily.BASE))
            .map(cluster -> cluster.getClusterDescription().getUniqueId())
            .toList();
    if (clusterUniqueIdsWithPredictiveEnabled.isEmpty()) {
      return Optional.empty();
    }
    return _predictiveAutoScalingTriggerSvc.getNextEarliestTriggerScalingTime(
        nextPlanningDateContext._ndsGroup.getGroupId(), clusterUniqueIdsWithPredictiveEnabled, now);
  }

  @VisibleForTesting
  protected Optional<Date> minimumAllowedAfterForFailedPlans(final NDSGroup pNDSGroup) {
    return pNDSGroup.getFailedPlans().stream()
        .map(
            planSummary ->
                planSummary.getLastFailureDate().getTime()
                    + NDSGroup.getBackoffDuration(planSummary.getFailedCount()).toMillis())
        .min(Comparator.naturalOrder())
        .map(Date::new);
  }

  // Get the minimum certificate expiry date of all the FastSharedPreAllocatedRecords to consider
  // for next planning date. We subtract some
  // threshold number of days from actual expiry date when we want to renew certs
  @VisibleForTesting
  protected Optional<Date> getNextPlanningDateForCertExpiration(
      final NextPlanningDateContext nextPlanningDateContext) {
    return nextPlanningDateContext.getFastTenantPreAllocatedRecords().stream()
        .map(FastTenantPreAllocatedRecord::getCertExpirationDate)
        .flatMap(Optional::stream)
        .min(Comparator.naturalOrder())
        .map(
            minCertExpiry ->
                new Date(
                    minCertExpiry.getTime()
                        - ACME_TENANT_CERT_EXPIRY_ROTATION_THRESHOLD.toMillis()));
  }

  /// For shard autoscaling, needsCheck may have been set on other shards during an autoscaling
  // round. In this case, we'll need to plan again soon to ensure that autoscaling happens as
  // quickly as possible
  @VisibleForTesting
  Optional<Date> getNextPlanningDateForShardAutoScaling(
      final ObjectId pGroupId, final List<Cluster> pClusters) {

    final Set<String> clusterNamesEligibleForAutoScaling =
        pClusters.stream()
            .map(Cluster::getClusterDescription)
            .filter(ClusterDescription::isAutoScaleSupported)
            .filter(cd -> cd.getAutoScalingMode() == AutoScalingMode.SHARD)
            .filter(
                cd -> {
                  final AutoScaling baseAutoScaling = cd.getAutoScaling(NodeTypeFamily.BASE);
                  final AutoScaling analyticsAutoScaling =
                      cd.getAutoScaling(NodeTypeFamily.ANALYTICS);
                  return (baseAutoScaling != null && baseAutoScaling.isComputeEnabled())
                      || (analyticsAutoScaling != null && analyticsAutoScaling.isComputeEnabled());
                })
            .map(ClusterDescription::getName)
            .collect(Collectors.toSet());

    if (clusterNamesEligibleForAutoScaling.isEmpty()) {
      return Optional.empty();
    }

    final boolean anyNeedsCheckSetInGroup =
        _autoScalingContextDao.findAllInGroup(pGroupId).stream()
            .filter(
                pAutoScalingContext ->
                    clusterNamesEligibleForAutoScaling.contains(
                        pAutoScalingContext.getId().getClusterName()))
            .filter(
                pAutoScalingContext ->
                    pAutoScalingContext.getBaseComputeContext()
                            instanceof ShardComputeAutoScalingContext
                        && pAutoScalingContext.getAnalyticsComputeContext()
                            instanceof ShardComputeAutoScalingContext)
            .flatMap(
                pAutoScalingContext ->
                    Stream.of(
                        pAutoScalingContext.getBaseComputeContext(),
                        pAutoScalingContext.getAnalyticsComputeContext()))
            .anyMatch(ComputeAutoScalingContext::isAnyNeedsCheckSet);

    if (anyNeedsCheckSetInGroup) {
      return Optional.of(Date.from(Instant.now().plus(2, ChronoUnit.MINUTES)));
    } else {
      return Optional.empty();
    }
  }

  protected Pair<NextPlanningDateSetBy, Long> getNextPlanningInterval(
      final NextPlanningDateContext pNextPlanningDateContext,
      final Date pNow,
      final Logger pGroupLogger) {
    final Optional<Pair<NextPlanningDateSetBy, Long>> failedPlanInterval;

    try {
      final NDSGroup ndsGroup = pNextPlanningDateContext.getNdsGroup();

      if (!ndsGroup.getFailedPlans().isEmpty() && ndsGroup.getCurrentPlans().isEmpty()) {
        final Optional<Date> minimumAllowedAfter = minimumAllowedAfterForFailedPlans(ndsGroup);
        if (minimumAllowedAfter.isEmpty()) {
          pGroupLogger.warn("Expecting a backoff interval to wait for when there are failed plans");
          failedPlanInterval =
              Optional.of(
                  Pair.of(
                      NextPlanningDateSetBy.PLANNER_FAILED_BACKOFF_INTERVAL,
                      _appSettings.getGroupPlanningIntervalLastFailed()));
        } else {
          final long backOffInterval = minimumAllowedAfter.get().getTime() - pNow.getTime();
          failedPlanInterval =
              Optional.of(
                  Pair.of(
                      NextPlanningDateSetBy.PLANNER_FAILED_BACKOFF_INTERVAL,
                      Math.max(
                          backOffInterval, _appSettings.getGroupPlanningIntervalLastFailed())));
        }
      } else {
        failedPlanInterval = Optional.empty();
      }

      final Optional<Pair<NextPlanningDateSetBy, Long>> exceptionDuringPlanningRoundInterval =
          pNextPlanningDateContext
              .getExceptionDuringPlanningRoundInterval()
              .map(interval -> Pair.of(PLANNER_EXCEPTION_DURING_ROUND, interval));

      final Optional<Pair<NextPlanningDateSetBy, Long>> unableToPlanInterval =
          !ndsGroup.getUnableToPlanClusterNames().isEmpty()
              ? Optional.of(
                  Pair.of(
                      NextPlanningDateSetBy.PLANNER_UNABLE_TO_PLAN_FOR_CLUSTER_INTERVAL,
                      getNDSPlannerNextGroupPlanningDateUnableToPlanInterval()))
              : Optional.empty();

      final Optional<Pair<NextPlanningDateSetBy, Long>> minInterval =
          Stream.of(failedPlanInterval, exceptionDuringPlanningRoundInterval, unableToPlanInterval)
              .flatMap(Optional::stream) // filters out empty intervals
              .min(Comparator.comparingLong(Pair::getRight)); // finds min of present intervals

      // if any of intervals (failed plan, exception thrown, or unable to plan) are present, returns
      // the interval with the earliest date
      if (minInterval.isPresent()) {
        return minInterval.get();
      }

      final List<CloudProviderContainer> provisionedContainers =
          ndsGroup.getCloudProviderContainers().stream()
              .filter(CloudProviderContainer::isProvisioned)
              .toList();
      final boolean hasProvisionedContainers = !provisionedContainers.isEmpty();

      final boolean hasActiveFreeTenantContainersOnly =
          hasProvisionedContainers
              && provisionedContainers.stream()
                  .allMatch(container -> CloudProvider.FREE.equals(container.getCloudProvider()));

      if (hasActiveFreeTenantContainersOnly) {
        long planningInterval =
            ThreadLocalRandom.current()
                .nextLong(
                    _appSettings.getGroupPlanningIntervalMedium(),
                    _appSettings.getGroupPlanningIntervalLong());
        return Pair.of(NextPlanningDateSetBy.PLANNER_DEFAULT_INTERVAL, planningInterval);
      }

      final boolean isActive =
          hasProvisionedContainers
              || ndsGroup.getCloudProviderContainers().stream()
                      .mapToLong(c -> c.getContainerPeers().size())
                      .sum()
                  > 0
              || _clusterSvc.hasActiveClustersByGroupIdExcludingPausedFree(ndsGroup.getGroupId());

      // if the cluster is active, default interval should be random value between [0, short)
      // if it is not active, default interval should be random value between [short, long)
      long splayInterval =
          isActive
              ? ThreadLocalRandom.current()
                  .nextLong(
                      getNDSPlannerGroupIntervalDistributedPlanningStart(),
                      _appSettings.getGroupPlanningIntervalShort())
              : ThreadLocalRandom.current()
                  .nextLong(
                      _appSettings.getGroupPlanningIntervalShort(),
                      _appSettings.getGroupPlanningIntervalLong());
      return Pair.of(NextPlanningDateSetBy.PLANNER_DEFAULT_INTERVAL, splayInterval);
    } catch (final RuntimeException e) {
      pGroupLogger.warn("Failed to find group planning interval", e);
      return Pair.of(
          NextPlanningDateSetBy.PLANNER_FAILURE_TO_FIND_GROUP_PLANNING_INTERVAL,
          _appSettings.getGroupPlanningIntervalShort());
    }
  }

  protected long addSplayToIntervalForNextMaintenanceDate(final long pInterval) {
    return pInterval + Duration.ofMinutes(RandomUtils.nextInt(15)).toMillis();
  }

  @VisibleForTesting
  protected boolean isDryRunMode() {
    return _appSettings.getBoolProp(AppSettings.Fields.NDS_DRY_RUN_MODE_ENABLED.value, false);
  }

  // getAwsReleaseAwsIpThresholdDays fetches the "aws.releaseAwsIpThreshold" field in app settings
  // that determines the threshold (in days) after which
  // the Aws owned Ip will be released so that it can no longer be rolled back.
  public int getAwsReleaseAwsIpThresholdDays() {
    return _appSettings.getIntProp(
        Fields.AWS_RELEASE_AWS_IP_THRESHOLD_DAYS.value, AWS_RELEASE_IP_THRESHOLD_DAYS);
  }

  protected List<Cluster> getActiveButNotWorkingClusters(
      final ObjectId pGroupId, final List<PlanSummary> pCurrentPlans) {
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(pGroupId);

    final Set<String> workingClusters =
        pCurrentPlans.stream()
            // We should include clusters whose snapshots are undergoing SNAPSHOT_RESTORE (the
            // source clusters of snapshot restores) in pre-plan version refreshes
            .filter(p -> !p.getResourceType().equals(ResourceType.SNAPSHOT_RESTORE))
            // We should include clusters doing concurrent snapshots because it was designed
            // to not block other cluster changes
            .filter(p -> !p.getResourceType().equals(ResourceType.CONCURRENT_SNAPSHOT))
            // We should include clusters doing copy snapshots because it was designed
            // to not block other cluster changes
            .filter(p -> !p.getResourceType().equals(ResourceType.COPY_SNAPSHOT))
            // We should include clusters doing compaction because it was designed to not block
            // other cluster changes like FreeDestroyMachineMove
            .filter(p -> !p.getResourceType().equals(ResourceType.FREE_COMPACTION))
            .map(PlanSummary::getClusterName)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .collect(Collectors.toSet());
    return clusters.stream()
        .filter(cluster -> !workingClusters.contains(cluster.getClusterDescription().getName()))
        .collect(Collectors.toList());
  }

  @VisibleForTesting
  boolean isServerlessPrivateNetworkingSupportedAndEnabled(
      final NDSGroup pNDSGroup, final ServerlessMTMPool pServerlessMTMPool) {
    return pNDSGroup.getCloudProviderContainerWithTenantEndpointServiceDeployment().isPresent()
        || (pServerlessMTMPool != null
            && !pServerlessMTMPool.isDeleteRequested()
            && pNDSGroup.isServerlessMTMHolder()
            && NDSSettings.getSupportedCloudProvidersForServerlessPrivateNetworking(_appSettings)
                .contains(pServerlessMTMPool.getCloudProvider())
            && _appSettings.isServerlessEnabled());
  }

  @VisibleForTesting
  protected void syncConsumerTenantPrivateNetworkingForGroup(final List<Cluster> pClusters) {
    pClusters.stream()
        .map(Cluster::getClusterDescription)
        .filter(
            pClusterDescription ->
                pClusterDescription.isServerlessTenantCluster()
                    && NDSSettings.getSupportedCloudProvidersForServerlessPrivateNetworking(
                            _appSettings)
                        .contains(pClusterDescription.getBackingProvider()))
        .forEach(
            pClusterDescription ->
                _ndsTenantEndpointSvc.syncEndpoints(
                    pClusterDescription, AuditInfoHelpers.fromSystem()));
  }

  @VisibleForTesting
  protected void syncProducerTenantPrivateNetworkingForGroup(
      final NDSGroup pNDSGroup,
      final ServerlessMTMPool pPool,
      final Logger pLogger,
      final Group pGroup) {
    // NOTE: it is assumed that the caller here has verified that:
    //          - the group is a serverless MTM holder
    //          - a pool is present or an endpoint service deployment is present (handles the
    //            delete case)
    //          - serverless private networking is supported for the cloud provider
    //          - load balancing is enabled

    final CloudProvider provider =
        Optional.ofNullable(pPool)
            .map(ServerlessMTMPool::getCloudProvider)
            .orElseGet(
                () ->
                    pNDSGroup
                        .getCloudProviderContainerWithTenantEndpointServiceDeployment()
                        .map(CloudProviderContainer::getCloudProvider)
                        .orElse(null));
    if (provider == null) {
      throw new IllegalStateException(
          String.format(
              "Unable to determine private networking cloud provider for group %s",
              pNDSGroup.getGroupId()));
    }

    final TenantEndpointServiceDeploymentSvc tenantEndpointServiceDeploymentSvc =
        switch (provider) {
          case AWS -> _awsTenantEndpointServiceDeploymentSvc;
          case AZURE -> _azureTenantEndpointServiceDeploymentSvc;
          case GCP -> null;
          default ->
              throw new IllegalArgumentException(
                  String.format(
                      "Received unsupported cloud provider %s for producer tenant private"
                          + " networking sync",
                      pPool.getCloudProvider()));
        };

    if (tenantEndpointServiceDeploymentSvc == null) {
      return;
    }

    pNDSGroup
        .getCloudProviderContainerWithTenantEndpointServiceDeployment()
        .ifPresentOrElse(
            container -> {
              try {
                final TenantEndpointServiceDeployment deployment =
                    container.getTenantEndpointServiceDeployment().orElseThrow();

                if (deployment.getDeleteRequestedDate() != null
                    && deployment.getEndpointServices().isEmpty()) {
                  // delete the deployment if it is delete requested and contains no endpoint
                  // services (since all the endpoint services will have been deleted in previous
                  // pre-planning runs)
                  tenantEndpointServiceDeploymentSvc.deleteDeployment(
                      pNDSGroup.getGroupId(), container.getId(), AuditInfoHelpers.fromSystem());
                } else {
                  // check if more endpoint services are needed
                  final int numDesiredEndpointServices =
                      tenantEndpointServiceDeploymentSvc.calculateNumDesiredTenantEndpointServices(
                          pNDSGroup.getOperationalLimits());
                  if (numDesiredEndpointServices != deployment.getNumDesiredEndpointServices()) {
                    tenantEndpointServiceDeploymentSvc.increaseNumDesiredTenantEndpointServices(
                        pNDSGroup.getGroupId(),
                        numDesiredEndpointServices,
                        AuditInfoHelpers.fromSystem());
                  }
                  final boolean isDsv5Esv5FlagEnabledForAzure =
                      isFeatureFlagEnabled(
                          FeatureFlag.ATLAS_AZURE_DSV5_AND_ESV5_INSTANCE_FAMILIES,
                          _appSettings,
                          null,
                          pGroup);
                  final boolean shouldExcludeAzureUsEast2FromDsv5Esv5Families =
                      isFeatureFlagEnabled(
                          FeatureFlag.ATLAS_EXCLUDE_REGION_USEAST2_FROM_AZURE_DSV5_ESV5_FAMILIES,
                          _appSettings,
                          null,
                          pGroup);

                  // sync the deployment if it exists (add additional endpoint services or request
                  // them for deletion)
                  tenantEndpointServiceDeploymentSvc.syncDeployment(
                      pNDSGroup.getGroupId(),
                      container.getId(),
                      deployment,
                      AuditInfoHelpers.fromSystem(),
                      isDsv5Esv5FlagEnabledForAzure,
                      shouldExcludeAzureUsEast2FromDsv5Esv5Families);
                }
              } catch (final SvcException pE) {
                pLogger.error(
                    "Encountered error syncing tenant endpoint service deployment for group {}",
                    pNDSGroup.getGroupId(),
                    pE);
              }
            },
            () -> {
              // create the deployment if it doesn't exist
              try {
                final boolean createDeploymentResult =
                    tenantEndpointServiceDeploymentSvc.createDeployment(
                        pNDSGroup.getGroupId(), AuditInfoHelpers.fromSystem());
                if (!createDeploymentResult) {
                  pLogger.warn(
                      "Failed to create tenant endpoint service deployment for group {}, will "
                          + "attempt again on the next round",
                      pNDSGroup.getGroupId());
                } else {
                  pLogger.info(
                      "Successfully created tenant endpoint service deployment for group {}",
                      pNDSGroup.getGroupId());
                }
              } catch (final SvcException pE) {
                pLogger.error(
                    "Encountered error creating tenant endpoint service deployment for group {}",
                    pNDSGroup.getGroupId(),
                    pE);
              }
            });

    if (pNDSGroup.getCloudProviderContainerWithTenantEndpointServiceDeployment().isPresent()) {
      // update any existing multi-tenant endpoint services with capacity, adding additional
      // multi-tenant endpoint services for existing endpoint services if necessary. note, this
      // will only consider increasing capacity for endpoint services with existing multi-tenant
      // endpoint services, i.e., it will not create the first multi-tenant endpoint service for
      // an endpoint service.
      _multiTenantEndpointServiceSvc.syncMultiTenantEndpointServiceCapacityForGroup(pNDSGroup);
    }

    // push current capacity metrics to prometheus if there is at least one available endpoint
    // service
    pNDSGroup.getCloudProviderContainerWithTenantEndpointServiceDeployment().stream()
        .flatMap(container -> container.getTenantEndpointServices().stream())
        .filter(
            tenantEndpointService ->
                tenantEndpointService.getStatus() == Status.AVAILABLE
                    && !tenantEndpointService.isDeleteRequested())
        .findFirst()
        .ifPresent(
            tenantEndpointService ->
                _multiTenantEndpointServiceSvc.updateTenantEndpointCapacityMetrics(pNDSGroup));
  }

  boolean isClusterProcessesOnTargetMajorVersion(
      final ObjectId groupId,
      final ClusterDescription clusterDescription,
      final AutomationConfig automationConfig,
      final Version targetVersion) {

    if (automationConfig == null) {
      return false;
    }

    final List<String> hostIds =
        automationConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getCluster().equals(clusterDescription.getDeploymentClusterName()))
            .filter(Process::isMongod)
            .limit(5) // for performance reasons, only check 5 hosts at a maximum
            .map(p -> HostUtils.assembleHostId(groupId, p.getHostname(), p.getPort()))
            .toList();

    if (hostIds.isEmpty()) {
      return false;
    }

    for (final String hostId : hostIds) {
      final BasicBSONObject lastPing = _hostLastPingSvc.getLastPing(hostId);
      final Optional<String> hostVersion =
          Optional.ofNullable(PingUtils.extractHostVersion(lastPing));
      if (hostVersion.isEmpty()
          || VersionUtils.parse(hostVersion.get()).getMajor() != targetVersion.getMajor()) {
        return false;
      }
    }
    return true;
  }

  void syncEolVersionUpgradeHistory(
      final ObjectId groupId,
      final AutomationConfig automationConfig,
      final List<Cluster> clusters,
      final Logger logger) {
    for (final Cluster cluster : clusters) {
      final ClusterDescription clusterDescription = cluster.getClusterDescription();

      final Optional<EolVersionUpgradeHistory> latestHistoryOpt =
          _eolVersionUpgradeHistoryDao.findLatestForCluster(
              groupId, clusterDescription.getUniqueId());
      if (latestHistoryOpt.isEmpty()) {
        continue;
      }

      final EolVersionUpgradeHistory latestHistory = latestHistoryOpt.get();
      // If there is an upgrade history in STARTED status, mark it completed if we observe that
      // cluster nodes are running on the expected target major version
      if (latestHistory.status() == EolVersionUpgradeHistory.Status.STARTED
          && isClusterProcessesOnTargetMajorVersion(
              groupId,
              clusterDescription,
              automationConfig,
              VersionUtils.parse(latestHistory.toVersion()))) {
        _eolVersionUpgradeHistoryDao.markCompleted(latestHistory.id(), new Date());
      } else if (latestHistory.status() == EolVersionUpgradeHistory.Status.COMPLETED
          && clusterDescription.getMongoDBVersion().getMajor() < latestHistory.toMajorVersion()) {
        // Manual intervention may have occurred either by the customer or an Admin operator to
        // downgrade the cluster to a LOWER major version than previously auto-upgraded by Atlas.
        // In this case, the pre-existing history is no longer reliable and we should delete the
        // history records for this cluster
        logger.warn(
            "Current cluster major version is lower than the target major version in"
                + " the latest EOL version upgrade history record in COMPLETED state."
                + " EOL version upgrade history will be deleted for cluster with"
                + " name {} and uniqueId {} in group {}",
            clusterDescription.getName(),
            clusterDescription.getUniqueId(),
            groupId);
        _eolVersionUpgradeHistoryDao.deleteForCluster(groupId, clusterDescription.getUniqueId());
      }
    }
  }

  public NextPlanningDateContext.Builder doPlanning(
      final NDSGroup pNDSGroup,
      final Logger pLogger,
      @Nullable final ObjectId pPlanGenerationJobId) {
    final NextPlanningDateContext.Builder nextPlanningDateContextBuilder =
        new NextPlanningDateContext.Builder(pNDSGroup);

    pLogger.debug("Starting planning");
    final Date now = new Date();

    final NDSProjectPlanningSummaryBuilder planningSummaryBuilder =
        new NDSProjectPlanningSummaryBuilder();

    final AutomationConfigContext automationConfigContext =
        new AutomationConfigContext(_automationConfigSvc, pNDSGroup.getGroupId());
    processCurrentPlans(pNDSGroup, pLogger, automationConfigContext);

    if (!shouldPlan(pNDSGroup, pLogger)) {
      pLogger.debug("Group doesn't need planning right now");
      return nextPlanningDateContextBuilder;
    }

    final List<MaintenanceCheckResult> maintenanceCheckResultList =
        new ArrayList<>(_ndsGroupMaintenanceSvc.checkExternalMaintenance(pNDSGroup));

    final ObjectId groupId = pNDSGroup.getGroupId();
    final boolean hasNonTenantContainersInUse =
        pNDSGroup.hasInUseCloudProviderContainerNotOfTypes(
            Arrays.stream(CloudProvider.values())
                .filter(CloudProvider::isTenantProvider)
                .collect(Collectors.toSet()));
    final boolean hasTenantContainersInUse =
        pNDSGroup.hasInUseCloudProviderContainerNotOfTypes(
            Arrays.stream(CloudProvider.values())
                .filter(CloudProvider::isSuitableTenantBackingProvider)
                .collect(Collectors.toSet()));

    final List<PrivateLinkConnectionRule> privateLinkConnectionRules =
        withNDSPlanningTrace()
            .run(
                () -> getPrivateLinkConnectionRules(groupId),
                "nds-planning-get-private-link-connection-rules");

    final List<BaseMultiTargetConnectionRule> multiTargetConnectionRules =
        new ArrayList<>(
            _awsMultiTargetConnectionRuleDao.getProvisionedMultiTargetConnectionRulesForGroup(
                groupId));

    // Get active clusters & checkAutoscale/checkAutoResume/
    // checkM0AutomaticPause prior to mergePendingClusterChanges to be applied for this plan round.

    // The idea of this ndsGroup optimization is given that we're in the planner now,
    // then if the state from when we first queried pGroup at the beginning is IDLE then
    // it must also be true that getCurrentPlans() has not changed out from under us.
    // (Otherwise, if say the state is WORKING, then it's possible some current plans
    // may have been updated earlier, so we will requery.)
    final Group group = _groupSvc.findById(groupId);
    if (group == null) {
      pLogger.warn("Unable to plan for the empty group {}", groupId);
      return nextPlanningDateContextBuilder;
    }
    if (!group.isAtlas()) {
      pLogger.warn(
          "Unable to plan for the group {} with groupType {}", groupId, group.getGroupType());
      return nextPlanningDateContextBuilder;
    }

    Optional<GroupStatus.Type> groupStatusType =
        Optional.ofNullable(group).map(Group::getStatus).map(GroupStatus::getStatus);

    if (group.isSystemProject()
        && groupStatusType.isPresent()
        && GroupStatus.CLOSED_TYPES.contains(groupStatusType.get())) {
      pLogger.warn(
          "Unable to plan for the deleted system group {} with status {}",
          groupId,
          group.getStatus().getStatus());
      return nextPlanningDateContextBuilder;
    }

    nextPlanningDateContextBuilder.setGroup(group);

    // Note: Pools are currently 1:1 with an MTM holder group, so there should only be one MTM
    // pool to reference.
    final Optional<ServerlessMTMPool> serverlessMTMPool =
        Optional.of(pNDSGroup)
            .filter(ndsGroup -> ndsGroup.isServerlessMTMHolder() || ndsGroup.isFlexMTMHolder())
            .flatMap(ndsGroup -> _serverlessMTMPoolSvc.findPoolByGroupId(ndsGroup.getGroupId()));

    final List<Cluster> activeButNotWorkingClusters = new ArrayList<>();

    if (hasTenantContainersInUse || hasNonTenantContainersInUse) {
      final NDSGroup prePlanNDSGroup =
          pNDSGroup.getState() == NDSState.IDLE ? pNDSGroup : _ndsGroupDao.find(groupId).get();

      // Handle ingestion pipelines
      if (hasNonTenantContainersInUse) {
        withNDSPlanningTrace()
            .run(
                () -> doIngestionPipelinePrePlanning(groupId, pLogger),
                "nds-planning-do-ingestion-pipeline-pre-planning");
      }

      final List<PlanSummary> currentPlans = prePlanNDSGroup.getCurrentPlans();
      activeButNotWorkingClusters.addAll(getActiveButNotWorkingClusters(groupId, currentPlans));

      withNDSPlanningTrace()
          .run(
              () ->
                  doAutoSystemsPrePlanning(
                      activeButNotWorkingClusters,
                      hasNonTenantContainersInUse,
                      hasTenantContainersInUse,
                      pNDSGroup,
                      group,
                      maintenanceCheckResultList,
                      planningSummaryBuilder,
                      automationConfigContext,
                      pLogger),
              "nds-planning-do-auto-systems-pre-planning");
    }

    // Pre-plan state updates / maintenance
    final Map<ObjectId, ItemDiff> appliedUpdates =
        withNDSPlanningTrace()
            .run(
                // activeButNotWorkingClusters may be empty and that is safe since if no containers
                // are
                // in use there should be no active cluster
                () ->
                    doPrePlanStateUpdate(
                        group,
                        pNDSGroup,
                        activeButNotWorkingClusters,
                        maintenanceCheckResultList,
                        pLogger),
                "nds-planning-do-pre-plan-state-update");

    final Date mongoDBPublishAfterDate = new Date();
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(pNDSGroup.getGroupId());

    // Adding a periodic check during planning to ensure our resources are keeping a
    // consistent healthy state
    validateProjectInvariants(pLogger, clusters);

    // Correcting the reactive autoscaling context in case of scaling strategy conversions
    doReactiveAutoScalingContextConversions(groupId, clusters, pLogger);

    // Correcting the predictive autoscaling context in case of scaling strategy conversions
    doPredictiveAutoScalingContextConversion(groupId, clusters, pLogger);

    final Long lastMonitoringAgentPingTime;
    final List<HostCluster> hostClusters;

    if (hasNonTenantContainersInUse) {
      refreshExpiredContainerPeers(group, pNDSGroup, clusters, pLogger);

      _autoIndexingSvc.syncFromClusterDescriptions(
          group.getId(),
          clusters.stream().map(Cluster::getClusterDescription).collect(Collectors.toList()),
          group.getOrgId());

      _atlasPremiumMonitoringSvc.checkAndEnablePremiumMonitoring(pNDSGroup, group, clusters);

      lastMonitoringAgentPingTime = _agentPingSvc.findLastPingTimeByGroupId(pNDSGroup.getGroupId());
      hostClusters =
          _hostClusterLifecycleSvc.findHostClustersByGroupId(pNDSGroup.getGroupId(), true);
    } else {
      // Tenant containers don't heal/upgrade so they do not need host information
      lastMonitoringAgentPingTime = 0L;
      hostClusters = Collections.emptyList();
    }

    if (isServerlessPrivateNetworkingSupportedAndEnabled(
        pNDSGroup, serverlessMTMPool.orElse(null))) {
      syncProducerTenantPrivateNetworkingForGroup(
          pNDSGroup, serverlessMTMPool.orElse(null), pLogger, group);
    }

    syncConsumerTenantPrivateNetworkingForGroup(clusters);

    syncEolVersionUpgradeHistory(
        groupId, automationConfigContext.getAutomationConfig().orElse(null), clusters, pLogger);

    // Since the "pre-plan" updates can modify the group, we want a fresh copy for updating
    // maintenance check states
    final NDSGroup ndsGroup = _ndsGroupDao.find(pNDSGroup.getGroupId()).get();
    nextPlanningDateContextBuilder.setNdsGroup(ndsGroup);

    withNDSPlanningTrace()
        .run(
            () ->
                updateMaintenanceCheckState(
                    maintenanceCheckResultList,
                    ndsGroup,
                    group,
                    mongoDBPublishAfterDate,
                    pLogger,
                    hostClusters,
                    lastMonitoringAgentPingTime,
                    automationConfigContext,
                    hasNonTenantContainersInUse,
                    clusters,
                    nextPlanningDateContextBuilder,
                    planningSummaryBuilder),
            "nds-planning-update-maintenance-check-state");

    // Runs PACPGM to clean up stale project software versions and exclude system projects
    final AutomationConfig config = automationConfigContext.getAutomationConfig().orElse(null);
    if (!ndsGroup.isSystemProject()
        && config != null
        && clusters.isEmpty()
        && ndsGroup.getCloudProviderContainers().isEmpty()
        && SoftwareType.hasNonNullSoftwareVersions(config)) {
      _ndsGroupSvc.setNeedsPublishForGroup(ndsGroup.getGroupId());
    }

    // Retrieve any non-replica set instance hardware. Right now this is just restore machines.
    final List<BackupRestoreJobPlanUnit> restoreJobs = new ArrayList<>();
    final Set<RegionName> regionsWithSnapshotsAndSnapshotDistribution = new HashSet<>();
    final List<BackupJob> backupJobs = new ArrayList<>();
    withNDSPlanningTrace()
        .run(
            () ->
                updateRegionsRestoreAndBackupJobs(
                    hasNonTenantContainersInUse,
                    ndsGroup,
                    group,
                    clusters,
                    pLogger,
                    regionsWithSnapshotsAndSnapshotDistribution,
                    backupJobs,
                    restoreJobs),
            "nds-planning-region-restore-and-backup-jobs");
    nextPlanningDateContextBuilder.setRegionsWithSnapshotsAndSnapshotDistribution(
        regionsWithSnapshotsAndSnapshotDistribution);

    // Retrieve tenant snapshot restore requests.
    final List<TenantRestore> tenantRestores =
        _tenantSnapshotRestoreSvc.findActiveRestoresForGroup(ndsGroup.getGroupId());

    // Retrieve serverless snapshot restore requests.
    final List<BackupRestoreJobPlanUnit> serverlessRestoreJobs =
        _cpsSvc.getServerlessRestoreJobsForPlanning(pNDSGroup.getGroupId());
    restoreJobs.addAll(serverlessRestoreJobs);

    // If there is at least one serverless container in the group, check if there are any
    // serverless upgrades to dedicated in progress that require maintaining the container.
    final List<ServerlessUpgradeToDedicatedStatus> serverlessUpgradeToDedicatedStatuses =
        ndsGroup.getCloudProviderContainersByTypes(Set.of(CloudProvider.SERVERLESS)).isEmpty()
            ? Collections.emptyList()
            : _serverlessUpgradeToDedicatedStatusDao.getInProgressTenantUpgradesForGroup(
                ndsGroup.getGroupId());

    // Retrieve tenant ACME certificates for project.
    final List<ClusterDescription> clusterDescriptions =
        clusters.stream().map(Cluster::getClusterDescription).collect(Collectors.toList());
    final List<NDSACMECert> acmeCertificatesForTenantClusters =
        _ndsACMESvc.findTenantCertificates(clusterDescriptions, ReadPreference.primary());
    nextPlanningDateContextBuilder.setAcmeCertificatesForTenantClusters(
        acmeCertificatesForTenantClusters);

    ensureGroupForUniformFrontend(group);

    final List<FastTenantPreAllocatedRecord.State> statesList = new ArrayList<>();
    statesList.add(FastTenantPreAllocatedRecord.State.CREATING);
    statesList.add(FastTenantPreAllocatedRecord.State.AVAILABLE);
    statesList.add(FastTenantPreAllocatedRecord.State.NEEDS_CLEANUP);
    // Retrieve CREATING and AVAILABLE and NEEDS_CLEANUP FastTenantPreAllocatedRecord for project
    final List<FastTenantPreAllocatedRecord> fastTenantPreAllocatedRecords;
    if (pNDSGroup.isFreeMTMHolder() || pNDSGroup.isFlexMTMHolder()) {
      fastTenantPreAllocatedRecords =
          _fastSharedPreAllocatedRecordsDao
              .findRecordsInProfileByMTMGroupAndState(pNDSGroup.getGroupId(), statesList)
              .stream()
              .map(FastTenantPreAllocatedRecord.class::cast)
              .collect(Collectors.toCollection(ArrayList::new));

      fastTenantPreAllocatedRecords.addAll(
          _fastFlexPreAllocatedRecordsDao
              .findRecordsInProfileByMTMGroupAndState(pNDSGroup.getGroupId(), statesList)
              .stream()
              .map(FastTenantPreAllocatedRecord.class::cast)
              .collect(Collectors.toCollection(ArrayList::new)));

    } else if (pNDSGroup.isServerlessMTMHolder()) {
      final Optional<ObjectId> poolId = serverlessMTMPool.map(ServerlessMTMPool::getId);
      if (poolId.isPresent()) {
        fastTenantPreAllocatedRecords =
            _fastServerlessPreAllocatedRecordsDao
                .findRecordsByPoolAndState(poolId.get(), statesList)
                .stream()
                .map(FastTenantPreAllocatedRecord.class::cast)
                .collect(Collectors.toList());
      } else {
        // during pool deletion (such as during local e2e test teardown), we may not have a poolId
        // associated with an MTM holder group
        fastTenantPreAllocatedRecords = List.of();
        pLogger.info(
            "Skipping plan generation for fast serverless provisioning as no pool is found for"
                + " MTM holder group.");
      }
    } else {
      fastTenantPreAllocatedRecords = List.of();
    }

    nextPlanningDateContextBuilder.setFastTenantPreAllocatedRecords(fastTenantPreAllocatedRecords);

    // Get fresh copies of group/clusters as they may have been modified above for maintenance, etc.
    final List<Cluster> reloadedClusters = _clusterSvc.getAllActiveClusters(ndsGroup.getGroupId());

    nextPlanningDateContextBuilder.setActiveClusters(reloadedClusters);

    final NDSGroup reloadedNdsGroup = _ndsGroupDao.find(ndsGroup.getGroupId()).get();
    nextPlanningDateContextBuilder.setNdsGroup(reloadedNdsGroup);

    withNDSPlanningTrace()
        .run(
            () ->
                doMaintenanceTasksPrePlanning(
                    reloadedNdsGroup, group, maintenanceCheckResultList, pLogger, reloadedClusters),
            "nds-planning-do-maintenance-tasks-pre-planning");

    withNDSPlanningTrace()
        .run(
            () ->
                updateMaintenanceHistories(
                    maintenanceCheckResultList, reloadedNdsGroup, group, reloadedClusters),
            "nds-planning-update-maintenance-history");

    if (pNDSGroup.isFreeMTMHolder() && _appSettings.isMTMCompactionEnabled()) {
      withNDSPlanningTrace()
          .run(
              () -> doMTMCompactionPrePlanning(clusters, pLogger),
              "nds-planning-do-mtm-compaction-pre-planning");
    }

    if (pNDSGroup.isFreeMTMHolder()
        || pNDSGroup.isServerlessMTMHolder()
        || pNDSGroup.isFlexMTMHolder()) {
      withNDSPlanningTrace()
          .run(
              () -> doFlexMTMClusterMigrationPrePlanning(clusters, pLogger),
              "nds-planning-do-flex-mtm-cluster-migration-pre-planning");
      withNDSPlanningTrace()
          .run(
              () -> doFlexMtmHolderGroupMigrationPrePlanning(reloadedNdsGroup),
              "nds-planning-do-flex-mtm-holder-group-pre-planning");
    }

    final CpsSnapshotEngine cpsSnapshotEngine =
        new CpsSnapshotEngine.Builder()
            .backupSnapshotDao(_snapshotDao)
            .cpsSvc(_cpsSvc)
            .cpsPitSvc(_cpsPitSvc)
            .ndsBackupPolicySvc(_cpsPolicySvc)
            .logger(pLogger)
            .build();

    final List<ServerlessLoadBalancingDeployment> serverlessLoadBalancingDeployments;
    if (ndsGroup.isServerlessMTMHolder() || ndsGroup.isFlexMTMHolder()) {
      serverlessLoadBalancingDeployments =
          _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(groupId);
      if (!serverlessLoadBalancingDeployments.isEmpty()) {
        // Add or remove Envoy Instances if needed
        _serverlessLoadBalancingDeploymentSvc.syncNumDesiredNodes(groupId);
      }
    } else {
      serverlessLoadBalancingDeployments = Collections.emptyList();
    }

    final List<IngestionPipelineRun> pipelineRunsForIngestion =
        hasNonTenantContainersInUse
            ? _ingestionPipelineSvc.findPipelineRunsReadyForIngestion(groupId)
            : List.of();

    final List<ObjectId> m0UniqueIdsForCompaction =
        reloadedClusters.stream()
            .filter(c -> MTMCompactionUtil.isClusterUnderCompaction(c.getClusterDescription()))
            .map(c -> c.getClusterDescription().getUniqueId())
            .collect(Collectors.toList());
    final List<MTMCompaction> processingMTMCompactions =
        m0UniqueIdsForCompaction.isEmpty()
            ? List.of()
            : _mtmCompactionSvc.findProcessingCompactionsByCompactingTenants(
                m0UniqueIdsForCompaction);
    final List<CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>>
        heldAWSCapacityRequests =
            _awsCheckCapacityRequestSvc.findCloudProviderHeldRequestsByGroupId(groupId);
    final List<CheckCapacityRequest<AzureInstanceCapacitySpec, AzureCheckResult>>
        activeAzureCapacityRequests =
            _azureCheckCapacityRequestSvc.findActiveCapacityRequests(groupId);

    final List<AzureCapacityDenyListEntry> azureDenylistEntriesNeedingCapacityCheck =
        _azureCapacityDenylistSvc.getDenylistEntriesNeedingCapacityCheck();

    Set<ObjectId> containersWithActiveDedicatedSearch = new HashSet<>();
    if (isFeatureFlagEnabled(FeatureFlag.DEDICATED_ATLAS_SEARCH_NODES, _appSettings, null, group)) {
      final SetMultimap<CloudProvider, RegionName> searchUsedProviders =
          MultimapBuilder.enumKeys(CloudProvider.class).hashSetValues().build();

      _searchDeploymentSvc
          .findNonDeletedDeployments(groupId)
          .forEach(
              searchDeployment -> {
                searchDeployment
                    .getNodeHardwareDescriptions()
                    .forEach(
                        pNodeHardwareDescription ->
                            searchUsedProviders.put(
                                pNodeHardwareDescription.getCloudProvider(),
                                pNodeHardwareDescription.getRegionName()));
              });

      _searchInstanceSvc
          .mapSearchInstancesByRegionFromGroupId(groupId)
          .forEach(
              (regionName, searchInstances) ->
                  searchInstances.forEach(
                      searchInstanceHardware -> {
                        CloudProvider provider = searchInstanceHardware.getCloudProvider();
                        if (!searchUsedProviders.get(provider).contains(regionName)) {
                          searchUsedProviders.put(provider, regionName);
                          LOG.info(
                              "Found {} search instance {} in {} in {} "
                                  + "without associated non-deleted Search DeploymentDescription! "
                                  + "Will inhibit corresponding cloud provider container deletion.",
                              provider.getDescription(),
                              kv("searchInstanceId", searchInstanceHardware.getInstanceId()),
                              regionName,
                              kv("groupId", groupId));
                        }
                      }));

      searchUsedProviders.asMap().entrySet().stream()
          .flatMap(
              entry -> {
                var regionNames = new HashSet<>(entry.getValue());
                return ndsGroup.getCloudProviderContainersByType(entry.getKey()).stream()
                    .filter(container -> container.isResponsibleForAnyOfTheseRegions(regionNames))
                    .map(CloudProviderContainer::getId);
              })
          .forEach(containersWithActiveDedicatedSearch::add);
    }

    final List<SystemClusterJob> systemClusterJobs =
        _systemClusterJobDao.findActiveJobsForProject(group.getId());

    final boolean hasStreamsPrivateNetworking = hasStreamsPrivateNetworking(group.getId());

    final List<TenantUpgradeStatus> tenantUpgradeStatuses =
        _tenantUpgradeStatusDao.getInProgressTenantUpgradesForGroup(groupId);

    final List<FlexTenantMigration> flexTenantMigrations =
        _flexMigrationSvc.getTenantMigrationsByTenantGroup(groupId);

    final List<ShadowClusterExposureJob> shadowClusterExposureJobs =
        _shadowClusterExposureJobDao.findActiveJobsForProject(group.getId());

    final PlanResult planResult =
        withNDSPlanningTrace()
            .run(
                () ->
                    buildPlan(
                        reloadedNdsGroup,
                        group,
                        automationConfigContext.getAutomationConfigOrEmptyConfig(),
                        reloadedClusters,
                        hostClusters,
                        backupJobs,
                        restoreJobs,
                        systemClusterJobs,
                        regionsWithSnapshotsAndSnapshotDistribution,
                        cpsSnapshotEngine,
                        tenantRestores,
                        acmeCertificatesForTenantClusters,
                        privateLinkConnectionRules,
                        multiTargetConnectionRules,
                        pLogger,
                        _appSettings,
                        serverlessLoadBalancingDeployments,
                        serverlessMTMPool.orElse(null),
                        fastTenantPreAllocatedRecords,
                        pipelineRunsForIngestion,
                        getTenantEndpointsMappedByCluster(reloadedClusters),
                        _regionalOutageSvc.findActiveByGroup(pNDSGroup.getGroupId()),
                        processingMTMCompactions,
                        heldAWSCapacityRequests,
                        activeAzureCapacityRequests,
                        containersWithActiveDedicatedSearch,
                        hasStreamsPrivateNetworking,
                        serverlessUpgradeToDedicatedStatuses,
                        planningSummaryBuilder,
                        azureDenylistEntriesNeedingCapacityCheck,
                        tenantUpgradeStatuses,
                        flexTenantMigrations,
                        shadowClusterExposureJobs),
                "nds-planning-build-plan");

    // Remove plans that conflict with each other or currently executing plans
    final List<PlanSummary> executingPlans = reloadedNdsGroup.getCurrentPlans();
    final List<Pair<PlanSummary, Plan>> nonConflicting =
        getNonConflictingToExecutePlans(
            reloadedNdsGroup.getGroupId(),
            reloadedNdsGroup.getLimits().getMaxConcurrentPlans(),
            planResult.getPlanSummaryPairs(),
            executingPlans,
            pLogger);

    // Remove plans that failed their last run and have some time to wait before they
    // are allowed to run again
    final List<Pair<PlanSummary, Plan>> readyPlans =
        getPlansWithoutBackoff(nonConflicting, reloadedNdsGroup.getFailedPlans(), pLogger);
    if (readyPlans.isEmpty()) {
      NDSPromMetricsSvc.incrementCounter(
          PLANNING_ROUND_GROUPS_NOT_PLANNED_COUNTER,
          reloadedNdsGroup.getNextPlanningDateSetBy().name());
    }
    if (isDryRunMode()) {
      return handleDryRunMode(
          pLogger, readyPlans, pNDSGroup.getGroupId(), nextPlanningDateContextBuilder);
    }
    if (pNDSGroup.getSkippedPlan()) {
      _ndsGroupDao.setSkippedPlan(pNDSGroup.getGroupId(), false);
      nextPlanningDateContextBuilder.setShouldRefreshNdsGroup(true);
    }

    final List<Cluster> unableToPlanClustersWithoutOngoingPlan =
        filterOutClustersWithOngoingPlan(
            planResult.getUnableToPlanClusters(), executingPlans, pLogger);

    // add unable-to-plan clusters to group's corresponding list.
    // remove succeed-to-plan clusters from the list
    withNDSPlanningTrace()
        .run(
            () ->
                updateUnableToPlanClusters(
                    pNDSGroup,
                    unableToPlanClustersWithoutOngoingPlan,
                    nextPlanningDateContextBuilder),
            "nds-planning-update-unable-to-plan-cluster");

    // ensure non-working clusters are set to idle
    ensureIdleClustersState(pLogger, reloadedNdsGroup, clusters, planResult);

    // Nothing else to do if the project has no current plan and no new plan was generated
    if (verifyIdleProject(
        now, pLogger, reloadedNdsGroup, planResult, nextPlanningDateContextBuilder)) {
      refreshNdsGroupIfNeeded(reloadedNdsGroup.getGroupId(), nextPlanningDateContextBuilder);
      return nextPlanningDateContextBuilder;
    }
    for (final Pair<PlanSummary, Plan> planSummaryPlanPair : readyPlans) {
      final Plan plan = planSummaryPlanPair.getRight();
      final PlanSummary planSummary = planSummaryPlanPair.getLeft();
      withNDSPlanningTrace(getSpanAttributesByPlanId(plan.getId()))
          .run(
              () ->
                  doReadyPlanPlanning(
                      plan,
                      planSummary,
                      planResult,
                      reloadedNdsGroup,
                      pLogger,
                      clusters,
                      nextPlanningDateContextBuilder,
                      pPlanGenerationJobId,
                      appliedUpdates),
              "nds-planning-do-ready-plan-planning");
    }

    if (_elevatedHealthMonitoringSvc.isEhmEnabled()) {
      if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
        _elevatedHealthMonitoringSvc.tiePlanToMonitoringWhenEligible(
            readyPlans, reloadedNdsGroup, pLogger);
      }
    }
    refreshNdsGroupIfNeeded(reloadedNdsGroup.getGroupId(), nextPlanningDateContextBuilder);
    final long runtimeMS = System.currentTimeMillis() - now.getTime();
    pLogger.debug("Completed planning in {} milliseconds", runtimeMS);
    NDSPromMetricsSvc.recordTimer(PLANNING_DURATION_SUMMARY, runtimeMS / 1000.0);
    return nextPlanningDateContextBuilder;
  }

  private void ensureGroupForUniformFrontend(final Group pGroup) {
    // Ensure group is tracked by all UFE configs if UFE feature flag is enabled. Due to the nature
    // of the POC (CLOUDP-222933), this should only occur in local or dev.
    final boolean isLocalOrDev =
        _appSettings.getAppEnv().isLocal() || _appSettings.getAppEnv().isDev();
    if (isLocalOrDev
        && FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_SHARDED_CLUSTERS_BEHIND_UNIFORM_FRONTEND,
            _appSettings,
            null,
            pGroup)) {
      _uniformFrontendEnvoyConfigurationDao
          .findAll()
          .forEach(
              deployment ->
                  _uniformFrontendEnvoyConfigurationDao.addGroupId(
                      deployment.getId(), pGroup.getId()));
    }
  }

  private boolean useReconfigureForCpuArchitectureSwap(final Group pGroup) {
    // Can use Reconfigure since instance size also need to change for CPU arch change. This also
    // allows modifying disks in the same plan, before instance size change.
    // OS_SWAP action only allows a plan to change instance. Disk modifications are pushed to
    // a following plan.
    return FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ATLAS_CLUSTER_SCALING_IMPROVEMENTS_PHASE1, _appSettings, null, pGroup);
  }

  @VisibleForTesting
  protected PlanResult buildPlan(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final AutomationConfig pAutomationConfig,
      final List<Cluster> pClusters,
      final List<HostCluster> pHostClusters,
      final List<BackupJob> pBackupJobs,
      final List<BackupRestoreJobPlanUnit> pRestoreJobs,
      final List<SystemClusterJob> pSystemClusterJobs,
      final Set<RegionName> pRegionsWithSnapshotsAndSnapshotDistribution,
      final CpsSnapshotEngine pCpsSnapshotEngine,
      final List<TenantRestore> pTenantRestores,
      final List<NDSACMECert> pACMEProxyCertificates,
      final List<? extends PrivateLinkConnectionRule> pPrivateLinkConnectionRules,
      final List<BaseMultiTargetConnectionRule> pMultiTargetConnectionRules,
      final Logger pLogger,
      final AppSettings pAppSettings,
      final List<ServerlessLoadBalancingDeployment> pServerlessLoadBalancingDeployments,
      final ServerlessMTMPool pServerlessMTMPool,
      final List<FastTenantPreAllocatedRecord> pFastTenantPreAllocatedRecords,
      final List<IngestionPipelineRun> pPipelineRunsReadyForIngestion,
      final Map<ObjectId, List<TenantEndpoint>> pTenantEndpointsMappedByCluster,
      final List<ClusterOutageSimulation> pActiveClusterOutageSimulations,
      final List<MTMCompaction> pProcessingMTMCompactions,
      final List<CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>>
          pHeldAWSCapacityRequests,
      final List<CheckCapacityRequest<AzureInstanceCapacitySpec, AzureCheckResult>>
          pActiveAzureCapacityRequests,
      final Set<ObjectId> pContainersWithActiveDedicatedSearch,
      final boolean pHasStreamsPrivateNetworking,
      final List<ServerlessUpgradeToDedicatedStatus> pServerlessUpgradeToDedicatedStatuses,
      final NDSProjectPlanningSummaryBuilder pPlanningSummaryBuilder,
      final List<AzureCapacityDenyListEntry> pAzureDenylistEntriesNeedingCapacityCheck,
      final List<TenantUpgradeStatus> pTenantUpgradeStatuses,
      final List<FlexTenantMigration> pFlexTenantMigrations,
      final List<ShadowClusterExposureJob> pShadowClusterExposureJobs) {
    final NDSGroup updatedNdsGroup =
        _ndsGroupDao
            .find(pNDSGroup.getGroupId())
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        "NdsGroupDao call failed for " + pNDSGroup.getGroupId().toString()));

    final Map<ObjectId, ClusterDescriptionProcessArgs> processArgsMappedByCluster =
        _clusterSvc.getProcessArgsForClusters(pClusters);

    final Map<ObjectId, CloudProviderAvailability> containerIdToAvailabilityMap =
        pNDSGroup.getCloudProviderContainers().stream()
            .collect(
                Collectors.toMap(
                    CloudProviderContainer::getId,
                    _cloudProviderAvailabilityFactory::getCloudProviderAvailability));

    return PlanResult.build(
        this::withNDSPlanningTrace,
        updatedNdsGroup,
        pGroup,
        pAutomationConfig,
        pClusters,
        pHostClusters,
        pBackupJobs,
        pRestoreJobs,
        pSystemClusterJobs,
        pRegionsWithSnapshotsAndSnapshotDistribution,
        pCpsSnapshotEngine,
        pTenantRestores,
        pACMEProxyCertificates,
        pPrivateLinkConnectionRules,
        pMultiTargetConnectionRules,
        pLogger,
        pAppSettings,
        pServerlessLoadBalancingDeployments,
        pServerlessMTMPool,
        pFastTenantPreAllocatedRecords,
        pPipelineRunsReadyForIngestion,
        pTenantEndpointsMappedByCluster,
        pActiveClusterOutageSimulations,
        pProcessingMTMCompactions,
        pHeldAWSCapacityRequests,
        pActiveAzureCapacityRequests,
        pContainersWithActiveDedicatedSearch,
        processArgsMappedByCluster,
        pHasStreamsPrivateNetworking,
        pServerlessUpgradeToDedicatedStatuses,
        pPlanningSummaryBuilder,
        pTenantUpgradeStatuses,
        pFlexTenantMigrations,
        pAzureDenylistEntriesNeedingCapacityCheck,
        containerIdToAvailabilityMap,
        pShadowClusterExposureJobs,
        _hostLastPingSvc);
  }

  @VisibleForTesting
  protected List<Pair<PlanSummary, Plan>> getNonConflictingToExecutePlans(
      final ObjectId pProjectId,
      final int pMaxConcurrentPlansPerProject,
      final List<Pair<PlanSummary, Plan>> pSummaryPairs,
      final List<PlanSummary> pExecutingPlans,
      final Logger pLogger) {
    return PlanSummaryUtil.getNonConflictingToExecutePlans(
        pProjectId, pMaxConcurrentPlansPerProject, pSummaryPairs, pExecutingPlans, pLogger);
  }

  @VisibleForTesting
  protected Map<ObjectId, ItemDiff> doPrePlanStateUpdate(
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final List<Cluster> pActiveButNotWorkingClusters,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final Logger pLogger) {
    // Unfix expired fixed versions
    pMaintenanceCheckResultList.addAll(
        _ndsGroupMaintenanceSvc.refreshGroupExpiredFixedVersions(pNDSGroup, pGroup));
    pActiveButNotWorkingClusters.forEach(
        cluster ->
            pMaintenanceCheckResultList.addAll(
                _ndsGroupMaintenanceSvc.refreshClusterExpiredFixedVersions(
                    pNDSGroup, pGroup, cluster, pLogger)));

    // Cluster expiration needs to be marked before merging changes
    try {
      _clusterSvc.markExpiredClustersForDeleteByGroup(pNDSGroup.getGroupId());
      final Map<ObjectId, ItemDiff> mergedChangeMap =
          _clusterSvc.mergePendingClusterChanges(pNDSGroup);
      final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

      if (isFeatureFlagEnabled(
          FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, _appSettings, null, pGroup)) {
        _clusterSvc.splitReplicationSpecs(pGroup.getId(), auditInfo);
        _clusterSvc.validateAllReplicationSpecsSplit(pGroup.getId());
      } else {
        _clusterSvc.mergeReplicationSpecs(pGroup.getId(), auditInfo);
      }

      if (_appSettings.isQueuedAdminActionsEnabled()) {
        final Organization org = _organizationSvc.findById(pGroup.getOrgId());

        final boolean inMaintenanceWindow =
            _ndsGroupMaintenanceSvc.shouldStartMaintenanceForGroup(
                pNDSGroup,
                pGroup,
                SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
                MaintenanceType.SCHEDULED_ADMIN_ACTION.name(),
                MaintenanceHistoryMatchCriteria.fromType(MaintenanceType.SCHEDULED_ADMIN_ACTION),
                pLogger);

        if (inMaintenanceWindow) {
          _ndsGroupMaintenanceSvc.setStartedDateOnPendingMaintenance(
              pNDSGroup.getGroupId(),
              MaintenanceCheckResult.builder()
                  .maintenanceType(MaintenanceType.SCHEDULED_ADMIN_ACTION)
                  .build(),
              LOG);
        }

        for (final Cluster cluster : pActiveButNotWorkingClusters) {
          _adminActionSvc.runScheduledAdminActions(
              pGroup,
              cluster.getClusterDescription(),
              org,
              auditInfo,
              pNDSGroup,
              inMaintenanceWindow);
        }
      }

      if (_featureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.ATLAS_KMIP_KEY_ROTATION_IN_MAINTENANCE_WINDOW, null, pGroup)) {
        pMaintenanceCheckResultList.addAll(
            _ndsGroupMaintenanceSvc.rotateKmipMasterKey(pNDSGroup, pGroup));

      } else {
        _ndsEncryptionAtRestSvc.checkLastKmipMasterKeyRotation(pNDSGroup);
      }
      _ndsGroupSvc.deleteExpiredDatabaseUsers(pNDSGroup, auditInfo);
      _ndsGroupSvc.deleteExpiredNetworkPermissions(pNDSGroup, auditInfo);

      // Make necessary updates to Push Based Log Export based on mongot feature flag changes.
      updatePushBasedLogExportEnabledMongotForGroup(pGroup, pNDSGroup);

      // Check and update gateway router eligibility
      checkAndUpdateGatewayRouterEligibility(pGroup, pActiveButNotWorkingClusters, pLogger);
      return mergedChangeMap;
    } catch (final SvcException pE) {
      throw new UncheckedSvcException(pE);
    }
  }

  /**
   * Checks and updates gateway router eligibility for clusters. Updates stored state if eligibility
   * has changed.
   */
  private void checkAndUpdateGatewayRouterEligibility(
      final Group pGroup, final List<Cluster> pClusters, final Logger pLogger) {
    final Date now = new Date();

    final boolean isDataExfiltrationSidecarEnabled =
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.DATA_EXFILTRATION_SIDECAR_ENABLED, null, pGroup);

    for (final Cluster cluster : pClusters) {
      final ClusterDescription clusterDescription = cluster.getClusterDescription();

      // Determine current eligibility - AWS-only, non-MTM clusters are eligible
      final boolean currentEligible =
          isDataExfiltrationSidecarEnabled
              && clusterDescription.getCloudProviders().equals(Set.of(CloudProvider.AWS))
              && !clusterDescription.isMTM();

      // Check if we need to update stored state
      final Optional<Boolean> previousEligible = clusterDescription.getGatewayRouterEligible();

      if (previousEligible.isEmpty() || !previousEligible.orElseThrow().equals(currentEligible)) {
        pLogger.debug(
            "Updating gateway router eligibility for cluster {} from {} to" + " {}",
            clusterDescription.getName(),
            previousEligible.orElse(null),
            currentEligible);

        this._clusterSvc.setGatewayRouterEligibilityState(
            pGroup.getId(), clusterDescription.getName(), currentEligible, now);
      }
    }
  }

  @VisibleForTesting
  protected void updateRegionsRestoreAndBackupJobs(
      final boolean pHasNonTenantContainersInUse,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<Cluster> pClusters,
      final Logger pLogger,
      final Set<RegionName> pRegionsWithSnapshotsAndSnapshotDistribution,
      final List<BackupJob> pBackupJobs,
      final List<BackupRestoreJobPlanUnit> pRestoreJobs) {
    pBackupJobs.addAll(_backupJobDao.findForProject(pNDSGroup.getGroupId()));

    if (!pHasNonTenantContainersInUse) {
      return;
    }

    final List<BackupRestoreJobPlanUnit> allRestores =
        _cpsSvc.getRestoreJobsForPlanning(pNDSGroup.getGroupId());

    pRegionsWithSnapshotsAndSnapshotDistribution.addAll(
        _snapshotDao.getUnpurgedSnapshotRegions(pNDSGroup.getGroupId()));

    pRegionsWithSnapshotsAndSnapshotDistribution.addAll(
        getSnapshotDistributionRegionNames(pBackupJobs));

    // Only run backups and restores on clusters that are not the target of a live import
    final Set<String> liveImportDestinationClusters =
        _liveImportSvc.getActiveLiveImportsForGroup(pNDSGroup.getGroupId()).stream()
            .map(LiveImport::getDestinationClusterName)
            .collect(Collectors.toSet());

    final List<BackupRestoreJobPlanUnit> restoreJobsNotFromGroupAndLiveImport =
        allRestores.stream()
            .filter(
                // only filtering out the restore job if the restore job is from this
                // Group, and it's a live import destination.
                r ->
                    !(pNDSGroup.getGroupId().equals(r.getSourceProjectId())
                        && liveImportDestinationClusters.contains(
                            r.getClusterNameForBuildingPlan())))
            .collect(Collectors.toList());

    final List<BackupRestoreJobPlanUnit> allNonExportsAndFilteredExportsJobs =
        getAllNonExportsAndFilteredExports(restoreJobsNotFromGroupAndLiveImport);
    pRestoreJobs.addAll(allNonExportsAndFilteredExportsJobs);

    // For all clusters, create a queued snapshot if snapshot schedule is ready
    queueSnapshotsIfScheduleIsReady(
        pGroup, pClusters, pBackupJobs, pLogger, liveImportDestinationClusters);

    _gcpPrivateServiceConnectSvc.ensurePrivateServiceConnections(pNDSGroup);
  }

  private List<PrivateLinkConnectionRule> getPrivateLinkConnectionRules(final ObjectId pGroupId) {
    final List<PrivateLinkConnectionRule> privateLinkConnectionRules = new ArrayList<>();
    privateLinkConnectionRules.addAll(
        _awsPrivateLinkTargetGroupDao.getProvisionedSingleTargetConnectionRulesForGroup(pGroupId));
    privateLinkConnectionRules.addAll(
        _azurePrivateLinkConnectionInboundNATRuleDao
            .getProvisionedSingleTargetConnectionRulesForGroup(pGroupId));
    privateLinkConnectionRules.addAll(
        _gcpPrivateServiceConnectionRuleDao.getProvisionedSingleTargetConnectionRulesForGroup(
            pGroupId));
    return privateLinkConnectionRules;
  }

  @VisibleForTesting
  protected void doMTMCompactionPrePlanning(final List<Cluster> pClusters, final Logger pLogger) {
    for (final Cluster cluster : pClusters) {
      final ClusterDescriptionId clusterDescriptionId =
          cluster.getClusterDescription().getClusterDescriptionId();
      _mtmCompactionSvc
          .findProcessingCompactionByMTMClusterDescriptionId(clusterDescriptionId)
          .filter(
              compaction ->
                  compaction.getCompactingTenants().size()
                      < _appSettings.getMaxTenantCompactionConcurrent())
          .ifPresent(
              // existing processing compaction means we need to process compaction for this cluster
              compaction -> {
                try {
                  final SharedMTMCluster mtmCluster =
                      _clusterSvc.getSharedMTMCluster(
                          clusterDescriptionId.getGroupId(), clusterDescriptionId.getClusterName());
                  processMTMCompaction(
                      cluster.getClusterDescription(), mtmCluster, compaction, pLogger);
                } catch (SvcException e) {
                  pLogger.error(
                      "Share MTM cluster not found with clusterDescriptionId: {}",
                      clusterDescriptionId);
                }
              });
    }
  }

  @VisibleForTesting
  protected void processMTMCompaction(
      final ClusterDescription pMTMClusterDescription,
      final SharedMTMCluster pMTMCluster,
      final MTMCompaction pMTMCompaction,
      final Logger pLogger) {
    pLogger.info(
        "Checking compaction for MTM groupId: {}, name: {}",
        pMTMCluster.getGroupId(),
        pMTMCluster.getName());

    final List<ObjectId> tenantsInMTMCluster =
        pMTMCluster.getProxyVersions().keySet().stream()
            .map(ObjectId::new)
            .filter(uniqueId -> !uniqueId.equals(pMTMClusterDescription.getUniqueId()))
            .toList();

    final List<ClusterDescription> tenantsToCompact =
        tenantsInMTMCluster.stream()
            .map(
                uniqueId ->
                    getEligibleTenantForCompaction(
                        uniqueId, pMTMClusterDescription, pMTMCompaction, pLogger))
            .flatMap(Optional::stream)
            .limit(
                _appSettings.getMaxTenantCompactionConcurrent()
                    - pMTMCompaction.getCompactingTenants().size())
            .toList();
    pLogger.info(
        "Compaction for MTM Cluster with name: {} has {} tenants left, trying to compact {} tenants"
            + " in this round",
        pMTMCluster.getName(),
        tenantsInMTMCluster.size(),
        tenantsToCompact.size());

    // we delete the mtmCluster if all tenants have been migrated and there's no tenant under
    // compaction.
    // Note here we are NOT checking tenantsToCompact because there might be more
    // eligible tenants for compaction later.
    if (tenantsInMTMCluster.isEmpty() && pMTMCompaction.getFailedAndCompactingTenants().isEmpty()) {
      pLogger.info(
          "Attempting to delete MTM cluster {} since there's no more tenants left",
          pMTMCluster.getName());

      if (!pMTMCluster.getLinkedContainerIds().isEmpty()) {
        // if there are still linked containerId without provisioned tenants in MTM cluster, we
        // know those are fast provision records, delete those
        pLogger.warn("Found linked container IDs, cleaning FastSharedPreAllocatedRecords..");
        _mtmCompactionSvc.cleanupFastSharedRecordsForMTMCompaction(pMTMCluster, pLogger);
        return;
      }
      try {
        _clusterSvc.deleteSharedMTMCluster(pMTMCluster.getGroupId(), pMTMCluster.getName());
        _clusterSvc.requestDeleteCluster(
            pMTMCluster.getGroupId(),
            pMTMCluster.getName(),
            AuditInfoHelpers.fromSystem(),
            null,
            DeleteClusterReason.SYSTEM,
            true);
        _mtmCompactionSvc.completeCompaction(pMTMCompaction);
      } catch (final SvcException e) {
        pLogger.error("Failed to delete MTM cluster with name {}", pMTMCluster.getName(), e);
      }
    } else if (!tenantsToCompact.isEmpty()) {
      tenantsToCompact.forEach(
          (tenantCD) -> {
            pLogger.info("Initiating compaction for tenant: {}", tenantCD.getUniqueId());
            _mtmCompactionSvc.addCompactingTenant(pMTMCompaction.getId(), tenantCD.getUniqueId());
            _clusterSvc.setFreeTenantUnderCompaction(
                tenantCD.getGroupId(), tenantCD.getUniqueId(), true);
            _ndsGroupSvc.setPlanningNow(tenantCD.getGroupId());
          });
    } else {
      pLogger.info(
          "Found {} compacting or failed tenants, waiting for those to complete",
          pMTMCompaction.getFailedAndCompactingTenants().size());
    }
  }

  @VisibleForTesting
  protected Optional<ClusterDescription> getEligibleTenantForCompaction(
      final ObjectId pTenantUniqueId,
      final ClusterDescription pMTMClusterDescription,
      final MTMCompaction pMTMCompaction,
      final Logger pLogger) {
    final Optional<ClusterDescription> tenantClusterDescription =
        _clusterSvc.getMergedClusterDescription(null, pTenantUniqueId);
    if (tenantClusterDescription.isEmpty()) {
      pLogger.error(
          "Tenant ClusterDescription not found for compaction, uniqueId: {}", pTenantUniqueId);
      return Optional.empty();
    }
    final TenantUpgradeStatus upgradeStatus =
        _tenantUpgradeStatusDao
            .findInProgressByName(
                tenantClusterDescription.get().getGroupId(),
                tenantClusterDescription.get().getName())
            .orElse(null);
    if (MTMCompactionUtil.isTenantEligibleForCompaction(
        tenantClusterDescription.get(),
        pMTMClusterDescription,
        pMTMCompaction,
        upgradeStatus,
        pLogger)) {
      return tenantClusterDescription;
    } else {
      return Optional.empty();
    }
  }

  @VisibleForTesting
  protected void doFlexMTMClusterMigrationPrePlanning(
      final List<Cluster> pClusters, final Logger pLogger) {
    for (final Cluster cluster : pClusters) {
      final ClusterDescription clusterDescription = cluster.getClusterDescription();
      final Optional<FlexMTMClusterMigration> mtmClusterMigrationOpt =
          _flexMigrationSvc
              .getMtmClusterMigration(clusterDescription.getUniqueId())
              .filter(
                  migration ->
                      Set.of(MigrationStatus.MIGRATION_PENDING, MigrationStatus.ROLLBACK_PENDING)
                          .contains(migration.getStatus()));

      if (mtmClusterMigrationOpt.isPresent()) {
        final FlexMTMClusterMigration mtmClusterMigration = mtmClusterMigrationOpt.orElseThrow();
        try {
          if (mtmClusterMigration.getStatus() == MigrationStatus.MIGRATION_PENDING) {
            final MTMCluster sourceMTMCluster =
                mtmClusterMigration.getSourceTenantProvider().equals(CloudProvider.FREE)
                    ? _clusterSvc.getSharedMTMCluster(
                        clusterDescription.getGroupId(), clusterDescription.getName())
                    : _clusterSvc.getServerlessMTMCluster(
                        clusterDescription.getGroupId(), clusterDescription.getName());
            final List<ObjectId> tenantsInMTMCluster =
                sourceMTMCluster.getProxyVersions().keySet().stream()
                    .map(ObjectId::new)
                    .filter(uniqueId -> !uniqueId.equals(clusterDescription.getUniqueId()))
                    .toList();
            if (tenantsInMTMCluster.isEmpty()
                && sourceMTMCluster.getLinkedContainerIds().isEmpty()) {
              _flexMigrationSvc.migrateSourceMtmToFlexMtmCluster(sourceMTMCluster);
            } else {
              pLogger.error(
                  "{} MTM {}:{} still has tenants connected to it ({} proxy version entries,"
                      + " {} linked container IDs). Can't migrate yet",
                  sourceMTMCluster.getMTMClusterType().toString(),
                  sourceMTMCluster.getGroupId(),
                  sourceMTMCluster.getName(),
                  tenantsInMTMCluster.size(),
                  sourceMTMCluster.getLinkedContainerIds().size());
            }
          } else if (mtmClusterMigration.getStatus() == MigrationStatus.ROLLBACK_PENDING) {
            final FlexMTMCluster flexMTMCluster =
                _clusterSvc.getFlexMTMCluster(
                    clusterDescription.getGroupId(), clusterDescription.getName());
            _flexMigrationSvc.rollbackSourceMtmToFlexMtmCluster(
                flexMTMCluster, mtmClusterMigration.getSourceTenantProvider());
          }
        } catch (final SvcException pE) {
          pLogger.error(
              "MTM cluster not found with clusterDescriptionId: {}",
              cluster.getClusterDescription().getClusterDescriptionId());
        }
      }
    }
  }

  @VisibleForTesting
  protected void doFlexMtmHolderGroupMigrationPrePlanning(final NDSGroup pGroup) {
    final Optional<FlexMTMHolderGroupMigration> mtmHolderGroupMigrationOpt =
        _flexMigrationSvc
            .getMtmHolderGroupMigration(pGroup.getGroupId())
            .filter(
                migration ->
                    Set.of(MigrationStatus.MIGRATION_PENDING, MigrationStatus.ROLLBACK_PENDING)
                        .contains(migration.getStatus()));
    if (mtmHolderGroupMigrationOpt.isPresent()) {
      final FlexMTMHolderGroupMigration mtmHolderGroupMigration =
          mtmHolderGroupMigrationOpt.orElseThrow();
      if (mtmHolderGroupMigration.getStatus() == MigrationStatus.MIGRATION_PENDING) {
        _flexMigrationSvc.migrateSharedOrServerlessToFlexMtmHolderGroup(pGroup);
      } else if (mtmHolderGroupMigration.getStatus() == MigrationStatus.ROLLBACK_PENDING) {
        _flexMigrationSvc.rollbackSharedOrServerlessToFlexMtmHolderGroup(
            pGroup, mtmHolderGroupMigration.getSourceTenantProvider());
      }
    }
  }

  private void doAutoSystemsPrePlanning(
      final List<Cluster> pActiveButNotWorkingClusters,
      final boolean pHasNonTenantContainersInUse,
      final boolean pHasTenantContainersInUse,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final NDSProjectPlanningSummaryBuilder pPlanningSummaryBuilder,
      final AutomationConfigContext automationConfigContext,
      final Logger pLogger) {
    for (final Cluster cluster : pActiveButNotWorkingClusters) {
      if (pHasNonTenantContainersInUse) {
        final PredictiveAutoScalingResult predictiveAutoScalingResult =
            withNDSPlanningTrace(
                    getSpanAttributesByClusterName(cluster.getClusterDescription().getName()))
                .run(
                    () ->
                        _predictiveAutoScaleSvc.checkAndPerformAutoScale(
                            pNDSGroup.getGroupId(),
                            cluster.getClusterDescription().getName(),
                            pPlanningSummaryBuilder
                                .getClusterPlanningSummaryBuilder(
                                    cluster.getClusterDescription().getUniqueId())
                                .getAutoScaleDecisionsBuilder()),
                    "nds-check-predictive-auto-scale");
        withNDSPlanningTrace(
                getSpanAttributesByClusterName(cluster.getClusterDescription().getName()))
            .run(
                () ->
                    _autoScaleSvc.checkAutoScale(
                        pNDSGroup.getGroupId(),
                        cluster.getClusterDescription().getName(),
                        pPlanningSummaryBuilder
                            .getClusterPlanningSummaryBuilder(
                                cluster.getClusterDescription().getUniqueId())
                            .getAutoScaleDecisionsBuilder(),
                        predictiveAutoScalingResult),
                "nds-check-auto-scale");
        withNDSPlanningTrace(
                getSpanAttributesByClusterName(cluster.getClusterDescription().getName()))
            .run(() -> _autoResumeSvc.checkAutoResume(cluster, pLogger), "nds-check-auto-resume");

        if (!cluster.getClusterDescription().isPaused()) {
          final List<MaintenanceCheckResult> rebootCheckStates =
              withNDSPlanningTrace(
                      getSpanAttributesByClusterName(cluster.getClusterDescription().getName()))
                  .run(
                      () -> {
                        final boolean shouldStartMaintenanceForGroup =
                            _ndsGroupMaintenanceSvc.shouldStartMaintenanceForGroup(
                                pNDSGroup,
                                pGroup,
                                SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
                                MaintenanceType.SERVER_RESTART.name()
                                    + "_OR_"
                                    + MaintenanceType.INSTANCE_REBOOT.name(),
                                pLogger);

                        return _autoRebootSvc.checkAutoReboot(
                            pNDSGroup.getMaintenanceWindow(),
                            shouldStartMaintenanceForGroup,
                            cluster,
                            pLogger);
                      },
                      "nds-check-auto-reboot");
          pMaintenanceCheckResultList.addAll(rebootCheckStates);
        }

        if (cluster.getClusterDescription().getMongotuneStatus().isPresent()
            && _mongotuneAppSettings.isPlannerCheckConfigUpdateEnabled()) {
          // Check if we need to refresh mongotune config for an active but not working cluster.
          // If the cluster is not idle, there might be a concurrent plan that is updating the
          // mongotune config.
          withNDSPlanningTrace(
                  getSpanAttributesByClusterName(cluster.getClusterDescription().getName()))
              .run(
                  () ->
                      checkForMaintainedMongotuneConfigUpdate(
                          cluster, automationConfigContext, pLogger),
                  "nds-check-mongotune-config-update");
        }
      }

      if (pHasTenantContainersInUse) {
        if (cluster.getClusterDescription().isFreeTenantCluster()
            && !cluster.getReplicaSets().isEmpty()
            && !cluster.getClusterDescription().isDeleteRequested()
            // MTM compaction is handled in doMTMCompactionPrePlanning
            && !((FreeTenantProviderOptions)
                    cluster.getClusterDescription().getFreeTenantProviderOptions())
                .isUnderCompaction()) {
          withNDSPlanningTrace(
                  getSpanAttributesByClusterName(cluster.getClusterDescription().getName()))
              .run(
                  () ->
                      _ndsTenantPauseSvc.checkM0AutomaticPause(
                          cluster.getClusterDescription(),
                          cluster.getReplicaSets().get(0),
                          pGroup,
                          pNDSGroup,
                          pLogger),
                  "nds-check-M0-automatic-pause");
        }
      }
    }
  }

  /**
   * Validates that the maintained Mongotune config for each provisioned instance in the cluster is
   * up to date.
   *
   * <p>This is a remediation step to ensure Mongotune config accuracy in case it was not properly
   * updated during earlier disk size changes or instance reconfigurations. Ideally, the Mongotune
   * config is updated during planning when such changes occur (see {@link
   * com.xgen.svc.nds.svc.planning.PlannedActionFactory#shouldPublishMongotuneConfigOnInstanceReconfigure}).
   * However, if the update was missed or incomplete, this method checks for discrepancies between
   * the expected Mongotune config and what is present in the automation config.
   *
   * <p>If a discrepancy is found (e.g., missing or duplicated config, or config mismatch), the
   * method marks the cluster as needing an mongotune config update. A follow-up action (e.g.,
   * scheduling an {@code UpdateClusterMaintainedMongotuneConfigMove}) will be taken to resolve the
   * issue (see PlanResult:buildPlanEntryForCluster).
   *
   * @param cluster the cluster whose Mongotune config should be checked
   * @param automationConfigContext the context containing the current automation config
   */
  @VisibleForTesting
  void checkForMaintainedMongotuneConfigUpdate(
      final Cluster cluster,
      final AutomationConfigContext automationConfigContext,
      final Logger pLogger) {
    final ClusterDescription clusterDescription = cluster.getClusterDescription();

    if (clusterDescription.isDeleteRequested()
        || clusterDescription.isPaused()
        || clusterDescription.isTenantCluster()) {
      return;
    }

    if (clusterDescription.getMongotuneStatus().isEmpty()
        || clusterDescription.getMongotuneStatus().get().state().isDisabled()) {
      // mongotune not enabled for this cluster. Skip the check.
      return;
    }

    if (clusterDescription.getNeedsMongotuneConfigPublishAfter().isPresent()) {
      // this has been set. no need to check again
      return;
    }

    final MongotuneProcessArgs mongotuneProcessArgs =
        Optional.ofNullable(
                _clusterSvc
                    .getProcessArgsForClusters(List.of(cluster))
                    .get(cluster.getClusterDescription().getUniqueId()))
            .flatMap(ClusterDescriptionProcessArgs::getMongotuneArg)
            .orElse(null);

    final Optional<InstanceHardware> instanceNeedsMongotuneConfigUpdate =
        // check all provisioned instances in the cluster, including hidden nodes
        cluster.getAllProvisionedInstances().stream()
            .filter(i -> i.getHostnameForAgents().isPresent())
            .filter(
                i -> {
                  final String hostname = i.getHostnameForAgents().get();

                  final List<MaintainedMongotuneConfig> existingMaintainedMongotuneConfig =
                      automationConfigContext
                          .getAutomationConfigOrEmptyConfig()
                          .getDeployment()
                          .getMaintainedMongotunesByHostname(hostname);

                  if (existingMaintainedMongotuneConfig.isEmpty()) {
                    // the host does not have any maintained mongotune config, so we need to update
                    // it.
                    pLogger.error(
                        "No maintained mongotune config found for agent hostname {}. This is"
                            + " unexpected and may lead to issues. Schedule"
                            + " UpdateClusterMaintainedMongotuneConfigMove to resolve this.",
                        hostname);
                    return true;
                  }

                  if (existingMaintainedMongotuneConfig.size() > 1) {
                    pLogger.error(
                        "Multiple ({}) maintained mongotune configs found for agent hostname {}."
                            + " This is unexpected and may lead to issues. Schedule"
                            + " UpdateClusterMaintainedMongotuneConfigMove to resolve this.",
                        existingMaintainedMongotuneConfig.size(),
                        hostname);
                    // we should run UpdateClusterMaintainedMongotuneConfigMove, which will remove
                    // duplicated config.
                    return true;
                  }

                  final MaintainedMongotuneConfig goalMongotuneConfig =
                      ClusterDeploymentProcessUtil.createBaseMaintainedMongotuneConfig(
                          hostname,
                          _appSettings,
                          mongotuneProcessArgs,
                          clusterDescription.getMongotuneStatus().get().state().isDisabled(),
                          automationConfigContext.getAutomationConfigOrEmptyConfig(),
                          cluster);
                  // unset the password before comparison, since the field is marked as @Transient,
                  // meaning it is not stored in automation config.
                  // Instead, we pass the password to automation agent in
                  // AutomationConfigForAgentSvc
                  // when we send the config to the agent.
                  goalMongotuneConfig.setMongotuneUserPwd(null);

                  final boolean configChanged =
                      !goalMongotuneConfig.equalsForPlanning(
                          existingMaintainedMongotuneConfig.get(0));
                  if (configChanged) {
                    final List<String> diffs =
                        goalMongotuneConfig.getDiffFieldNames(
                            existingMaintainedMongotuneConfig.get(0));
                    pLogger.info(
                        "Mongotune config mismatch detected for agent hostname {}, "
                            + "Fields '{}' has a differing value. Update is required.",
                        i.getHostnameForAgents().get(),
                        diffs);
                  }
                  return configChanged;
                })
            .findAny();

    if (instanceNeedsMongotuneConfigUpdate.isPresent()) {
      // at least one instance needs mongotune config update
      // Mark the cluster as needing an update to its maintained Mongotune config.
      _clusterSvc.setNeedsMaintainedMongotuneConfigUpdateDateForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName());
    }
  }

  private void doIngestionPipelinePrePlanning(final ObjectId pGroupId, final Logger pLogger) {
    if (_appSettings.getBoolProp("nds.dataFederation.rejectWrites", false)) {
      pLogger.info(
          "Data Lake writes are disabled due to system maintenance.  Skipping pipeline planning for"
              + " groupId: {}",
          pGroupId);
    } else {
      _ingestionPipelineSvc.createPipelineRunsAndBackupExportJobForProjectIfNeeded(pGroupId);
      _ingestionPipelineSvc.updatePipelineRunState(pGroupId);
    }
  }

  private NextPlanningDateContext.Builder handleDryRunMode(
      final Logger pLogger,
      final List<Pair<PlanSummary, Plan>> pReadyPlans,
      final ObjectId pGroupId,
      final NextPlanningDateContext.Builder pNextPlanningDateContextBuilder) {
    pLogger.info("PlanningSvc running in dry run mode.");
    if (!pReadyPlans.isEmpty()) {
      pLogger.info("The following plans were generated but will not be executed.");
      pReadyPlans.forEach(p -> pLogger.info(p.getRight().toString()));
    }
    _ndsGroupDao.setSkippedPlan(pGroupId, true);
    pNextPlanningDateContextBuilder.setShouldRefreshNdsGroup(true);
    refreshNdsGroupIfNeeded(pGroupId, pNextPlanningDateContextBuilder);

    return pNextPlanningDateContextBuilder;
  }

  @VisibleForTesting
  protected void updateMaintenanceCheckState(
      final List<MaintenanceCheckResult> pMaintenanceCheckResults,
      final NDSGroup pNdsGroup,
      final Group pGroup,
      final Date pMongoDBPublishAfterDate,
      final Logger pLogger,
      final List<HostCluster> pHostClusters,
      final Long pLastMonitoringAgentPingTime,
      final AutomationConfigContext pAutomationConfigContext,
      final boolean pHasNonTenantContainersInUse,
      final List<Cluster> pClusters,
      final NextPlanningDateContext.Builder pNextPlanningDateContextBuilder,
      final NDSProjectPlanningSummaryBuilder pPlanningSummaryBuilder) {

    final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable = new HashMap<>();

    if (pNdsGroup.isServerlessMTMHolder()) {
      pMaintenanceCheckResults.add(
          _ndsGroupMaintenanceSvc.refreshServerlessProxyVersion(
              pNdsGroup,
              pGroup,
              pMongoDBPublishAfterDate,
              numMonitoringAddedMapMutable,
              pLogger,
              pAutomationConfigContext.getAutomationConfig().orElse(null)));
      pMaintenanceCheckResults.add(
          (_ndsGroupMaintenanceSvc.refreshOSPolicyVersionsForLoadBalancingDeployment(
              pNdsGroup, pGroup, pLogger, _serverlessLoadBalancingDeploymentDao)));
      if (isFeatureFlagEnabled(FeatureFlag.ATLAS_DEPLOYS_UIS, _appSettings, null, pGroup)) {
        pMaintenanceCheckResults.add(
            _ndsGroupMaintenanceSvc.refreshUserIdentityServiceVersion(
                pNdsGroup,
                pGroup,
                pMongoDBPublishAfterDate,
                numMonitoringAddedMapMutable,
                pLogger,
                pAutomationConfigContext.getAutomationConfig().orElse(null)));
      }
    }

    if (pHasNonTenantContainersInUse) {
      pMaintenanceCheckResults.addAll(
          _ndsGroupMaintenanceSvc.refreshProjectSoftwareVersions(
              pNdsGroup,
              pGroup,
              pClusters,
              pMongoDBPublishAfterDate,
              numMonitoringAddedMapMutable,
              pLogger,
              pAutomationConfigContext.getAutomationConfig().orElse(null)));
    }

    if (_appSettings.isQueuedAdminActionsEnabled()) {
      pMaintenanceCheckResults.add(
          _ndsGroupMaintenanceSvc.refreshQueuedAdminActions(pGroup.getId()));
    }

    final Map<String, ClusterDescription> tenantToMtmMap = _clusterSvc.getTenantToMtmMap(pNdsGroup);
    getActiveButNotWorkingClusters(pNdsGroup.getGroupId(), pNdsGroup.getCurrentPlans())
        .forEach(
            cluster -> {
              List<MaintenanceCheckResult> clusterMaintenanceCheckResult =
                  withNDSPlanningTrace(
                          getSpanAttributesByClusterName(cluster.getClusterDescription().getName()))
                      .run(
                          () ->
                              doClusterMaintenance(
                                  cluster,
                                  tenantToMtmMap,
                                  pNdsGroup,
                                  pGroup,
                                  pMongoDBPublishAfterDate,
                                  pLogger,
                                  pHostClusters,
                                  pLastMonitoringAgentPingTime,
                                  pAutomationConfigContext,
                                  pPlanningSummaryBuilder
                                      .getClusterPlanningSummaryBuilder(
                                          cluster.getClusterDescription().getUniqueId())
                                      .getClusterMaintenanceDecisionsBuilder(),
                                  numMonitoringAddedMapMutable),
                          "nds-cluster-maintenance");

              pMaintenanceCheckResults.addAll(clusterMaintenanceCheckResult);
            });

    if (pClusters != null) {
      pMaintenanceCheckResults.addAll(
          pClusters.stream()
              .flatMap(
                  c ->
                      _ndsGroupMaintenanceSvc
                          .reloadSslOnProcesses(pNdsGroup, c, numMonitoringAddedMapMutable, pLogger)
                          .stream())
              .toList());
    }

    if (_elevatedHealthMonitoringSvc.getNDSEHMExtensionEnabled()) {
      final boolean isAllowListEnabled =
          isFeatureFlagEnabled(
              FeatureFlag.EHM_ALLOW_LIST_ENABLED,
              _appSettings,
              null,
              _groupSvc.findById(pNdsGroup.getGroupId()));

      if (!isAllowListEnabled) {
        numMonitoringAddedMapMutable.forEach(
            (key, value) -> {
              _elevatedHealthMonitoringSvc.incrementTotalFleetMonitoringsConfigured(
                  key, value.get());
            });
      }
    }

    getEarliestCriticalReleaseTimeForMaintenance(pMaintenanceCheckResults)
        .ifPresent(pNextPlanningDateContextBuilder::setMinCriticalReleaseTimeSlot);

    final boolean isAnyMaintenanceScheduledDuringProtectedHours =
        pMaintenanceCheckResults.stream()
            .filter(c -> c.getSchedulingBehavior() == SchedulingBehavior.RESPECT_PROTECTED_HOURS)
            .anyMatch(MaintenanceCheckResult::needMaintenance);

    pNextPlanningDateContextBuilder.setRespectProtectedHoursMaintenanceNeeded(
        isAnyMaintenanceScheduledDuringProtectedHours);
  }

  private List<MaintenanceCheckResult> doClusterMaintenance(
      final Cluster pCluster,
      final Map<String, ClusterDescription> tenantToMtmMap,
      final NDSGroup pNdsGroup,
      final Group pGroup,
      final Date pMongoDBPublishAfterDate,
      final Logger pLogger,
      final List<HostCluster> pHostClusters,
      final Long pLastMonitoringAgentPingTime,
      final AutomationConfigContext pAutomationConfigContext,
      final ClusterMaintenanceDecisions.Builder pClusterMaintenanceDecisionsBuilder,
      final Map<ObjectId, AtomicInteger> pNumMonitoringAddedMap) {
    try (final SplitTimer timer =
        SplitTimer.start("doClusterMaintenance()", pLogger).slowMs(1000).json()) {
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList = new ArrayList<>();
      final List<RollingResync> rollingResyncs;
      if (pCluster.getClusterDescription().isTenantCluster()) {
        timer.split("refreshTenantMongoDBVersions()");
        pMaintenanceCheckResultList.add(
            _ndsGroupMaintenanceSvc.refreshTenantMongoDBVersions(
                pCluster, tenantToMtmMap, pLogger));
        timer.split("refreshTenantMongoDBFeatureCompatibilityVersion()");
        pMaintenanceCheckResultList.add(
            _ndsGroupMaintenanceSvc.refreshTenantMongoDBFeatureCompatibilityVersion(
                pCluster, tenantToMtmMap, pLogger));
        rollingResyncs = List.of();
      } else {
        if (isFeatureFlagEnabled(
            FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE, _appSettings, null, pGroup)) {
          timer.split("refreshMongoDbEolVersions()");
          pMaintenanceCheckResultList.add(
              _ndsGroupMaintenanceSvc.refreshMongoDbEolVersions(
                  pNdsGroup,
                  pGroup,
                  pCluster,
                  pMongoDBPublishAfterDate,
                  pNumMonitoringAddedMap,
                  pLogger));
        }
        timer.split("refreshMongoDBVersions()");
        pMaintenanceCheckResultList.add(
            _ndsGroupMaintenanceSvc.refreshMongoDBVersions(
                pNdsGroup,
                pGroup,
                pCluster,
                pMongoDBPublishAfterDate,
                pNumMonitoringAddedMap,
                pLogger));
        timer.split("refreshMongoDBParameters()");
        pMaintenanceCheckResultList.add(
            _ndsGroupMaintenanceSvc.refreshMongoDBParameters(
                pNdsGroup,
                pGroup,
                pCluster,
                pMongoDBPublishAfterDate,
                pNumMonitoringAddedMap,
                pLogger));
        timer.split("refreshMongoDBFeatureCompatibilityVersion()");
        pMaintenanceCheckResultList.add(
            _ndsGroupMaintenanceSvc.refreshMongoDBFeatureCompatibilityVersion(
                pNdsGroup,
                pGroup,
                pCluster,
                pMongoDBPublishAfterDate,
                pNumMonitoringAddedMap,
                pLogger));

        timer.split("refreshRollingResyncActions()");
        rollingResyncs =
            withNDSPlanningTrace(
                    getSpanAttributesByClusterName(pCluster.getClusterDescription().getName()))
                .run(
                    () -> {
                      final ClusterDescription clusterDescription =
                          pCluster.getClusterDescription();
                      return _rollingResyncSvc.refreshRollingResyncActions(
                          pNdsGroup,
                          clusterDescription.getName(),
                          pHostClusters,
                          pGroup,
                          pCluster.getReplicaSets(),
                          AuditInfoHelpers.fromSystem(),
                          clusterDescription.getMongoDBVersion(),
                          clusterDescription.isEncryptionAtRestEnabled());
                    },
                    "nds-check-rolling-resync");
        if (_cpsSvc.isRestoreCrossProjectAwsNewCmkEnabled(pNdsGroup.getGroupId())
            && _cpsSvc.isRestoreCrossProjectAwsMigrationEnabled(pNdsGroup.getGroupId())) {
          timer.split("refreshRollingForceReplacementActions()");
          withNDSPlanningTrace(
                  getSpanAttributesByClusterName(pCluster.getClusterDescription().getName()))
              .run(
                  () ->
                      _cpsSvc.refreshRollingForceReplacementActions(
                          pCluster.getClusterDescription(), pNdsGroup, pGroup, pCluster),
                  "nds-check-rolling-force-replacement");
        }

        if (pCluster.getClusterDescription().getClusterType().isSharded()) {
          timer.split("scheduleCheckMetadataConsistency()");
          pMaintenanceCheckResultList.add(
              _ndsGroupMaintenanceSvc.scheduleCheckMetadataConsistency(
                  pNdsGroup, pGroup, pCluster, pLogger));
        }
        if (isFeatureFlagEnabled(FeatureFlag.ENABLE_MONGOTUNE, _appSettings, null, pGroup)) {
          timer.split("refreshMongotune()");
          pMaintenanceCheckResultList.addAll(
              _ndsGroupMaintenanceSvc.refreshMongotune(
                  pNdsGroup,
                  pGroup,
                  pCluster,
                  pMongoDBPublishAfterDate,
                  pNumMonitoringAddedMap,
                  pLogger,
                  pAutomationConfigContext.getAutomationConfig().orElse(null)));
        }
      }

      timer.split("refreshClusterHardwareActions()");
      pMaintenanceCheckResultList.addAll(
          refreshClusterHardwareActions(
              pNdsGroup,
              pGroup,
              pHostClusters,
              pLastMonitoringAgentPingTime,
              pLogger,
              pCluster,
              rollingResyncs,
              pAutomationConfigContext,
              pClusterMaintenanceDecisionsBuilder));
      timer.split("refreshMongotVersion()");
      pMaintenanceCheckResultList.add(
          _ndsGroupMaintenanceSvc.refreshMongotVersion(
              pNdsGroup,
              pGroup,
              pCluster,
              pMongoDBPublishAfterDate,
              pNumMonitoringAddedMap,
              pLogger,
              pAutomationConfigContext.getAutomationConfig().orElse(null)));
      timer.split("refreshSearchEnvoyVersion()");
      pMaintenanceCheckResultList.add(
          _ndsGroupMaintenanceSvc.refreshSearchEnvoyVersion(
              pNdsGroup,
              pGroup,
              pCluster,
              pMongoDBPublishAfterDate,
              pNumMonitoringAddedMap,
              pLogger,
              pAutomationConfigContext.getAutomationConfig().orElse(null)));
      timer.split("refreshAtlasProxyVersion()");
      pMaintenanceCheckResultList.add(
          _ndsGroupMaintenanceSvc.refreshAtlasProxyVersion(
              pNdsGroup,
              pGroup,
              pCluster,
              pMongoDBPublishAfterDate,
              pNumMonitoringAddedMap,
              pLogger,
              pAutomationConfigContext.getAutomationConfig().orElse(null)));
      timer.split("refreshMongoDbToolsVersions()");
      pMaintenanceCheckResultList.add(
          _ndsGroupMaintenanceSvc.refreshMongoDbToolsVersions(
              pNdsGroup,
              pGroup,
              pCluster,
              pMongoDBPublishAfterDate,
              pNumMonitoringAddedMap,
              pLogger,
              pAutomationConfigContext.getAutomationConfig().orElse(null)));

      return pMaintenanceCheckResultList;
    }
  }

  private void doMaintenanceTasksPrePlanning(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<MaintenanceCheckResult> pMaintenanceCheckResultList,
      final Logger pLogger,
      final List<Cluster> pReloadedClusters) {
    _ndsGroupMaintenanceSvc.generateMaintenanceStartedAuditsIfNecessary(
        pNDSGroup, pGroup, pMaintenanceCheckResultList);
    _ndsGroupMaintenanceSvc.sendInAdvancedNotificationIfNecessary(
        pNDSGroup, pGroup, pMaintenanceCheckResultList, pLogger);
    _ndsGroupMaintenanceSvc.sendMaintenanceStartedIfNecessary(
        pNDSGroup, pGroup, pMaintenanceCheckResultList, pLogger);
    final List<ClusterDescription> clusterDescriptions =
        pReloadedClusters.stream().map(Cluster::getClusterDescription).toList();

    _ndsGroupMaintenanceSvc.maintenanceCompletionCleanup(
        pNDSGroup, pGroup, pMaintenanceCheckResultList, clusterDescriptions, pLogger);

    _ndsGroupMaintenanceSvc.maintenanceProtectedHoursCompletionCleanup(
        pNDSGroup.getGroupId(),
        pNDSGroup.getMaintenanceWindow().getProtectedHours(),
        pMaintenanceCheckResultList,
        clusterDescriptions,
        pLogger);
  }

  @VisibleForTesting
  void updateMaintenanceHistories(
      final List<MaintenanceCheckResult> pMaintenanceCheckResults,
      final NDSGroup pNdsGroup,
      final Group pGroup,
      final List<Cluster> pActiveClusters) {
    final List<NdsMaintenanceHistory> pendingMaintenanceHistories =
        new ArrayList<>(
            _ndsMaintenanceHistorySvc.findAllByGroupIdAndStateNotCompleted(pNdsGroup.getGroupId()));

    final List<MaintenanceCheckResultWithHistory> maintenanceCheckResultsWithHistory =
        pMaintenanceCheckResults.stream()
            .map(
                maintenanceCheckState -> {
                  final NdsMaintenanceHistory matchingHistory =
                      pendingMaintenanceHistories.stream()
                          .filter(
                              MaintenanceHistoryMatchCriteria.fromCheckResult(maintenanceCheckState)
                                  ::matchesHistory)
                          .findFirst()
                          .orElse(null);

                  return new MaintenanceCheckResultWithHistory(
                      maintenanceCheckState, matchingHistory);
                })
            .toList();

    final List<NdsMaintenanceHistory> newMaintenanceHistories =
        maintenanceCheckResultsWithHistory.stream()
            .filter(m -> m.checkResult().needMaintenance() && !m.hasHistory())
            .map(m -> newMaintenanceHistory(m.checkResult(), pNdsGroup, pGroup))
            .toList();
    _ndsMaintenanceHistorySvc.savePendingMaintenances(newMaintenanceHistories);

    maintenanceCheckResultsWithHistory.stream()
        .filter(m -> m.checkResult().needMaintenance() && m.hasHistory())
        .forEach(
            m -> {
              final NdsMaintenanceHistory history = m.history();
              if (m.checkResult().isMetadataUpdatedForInstanceRebootOrServerRestart(history)) {
                pendingMaintenanceHistories.remove(history);
                _ndsMaintenanceHistorySvc.moveToNoLongerRequiredState(List.of(history.getId()));
                _ndsMaintenanceHistorySvc.savePendingMaintenances(
                    List.of(newMaintenanceHistory(m.checkResult(), pNdsGroup, pGroup)));
              } else if (m.checkResult().isVersionOrSchedulingBehaviorFieldUpdated(history)) {
                _ndsMaintenanceHistorySvc.updateFieldsToVersionAndSchedulingBehavior(
                    history.getId(),
                    m.checkResult().getToVersion(),
                    m.checkResult().getSchedulingBehavior());
              }
            });

    final List<ObjectId> noLongerNeededMaintenanceHistoryIds =
        maintenanceCheckResultsWithHistory.stream()
            .filter(m -> !m.checkResult().needMaintenance() && m.hasHistory())
            .map(MaintenanceCheckResultWithHistory::history)
            .map(NdsMaintenanceHistory::getId)
            .toList();

    final List<ObjectId> noLongerRequiredMaintenanceHistoryIds =
        getNoLongerRequiredMaintenanceHistoryIds(
            pendingMaintenanceHistories, pMaintenanceCheckResults, pActiveClusters);

    _ndsMaintenanceHistorySvc.moveToNoLongerRequiredState(
        Stream.concat(
                noLongerNeededMaintenanceHistoryIds.stream(),
                noLongerRequiredMaintenanceHistoryIds.stream())
            .toList());
  }

  @VisibleForTesting
  static List<ObjectId> getNoLongerRequiredMaintenanceHistoryIds(
      final List<NdsMaintenanceHistory> pPendingMaintenanceHistories,
      final List<MaintenanceCheckResult> pMaintenanceCheckResults,
      final List<Cluster> pActiveClusters) {
    return pPendingMaintenanceHistories.stream()
        .<NdsMaintenanceHistory>mapMulti(
            (history, c) -> {
              if (StringUtils.isNotBlank(history.getClusterName())) {
                final boolean anyCheckResultMatchesHistory =
                    pMaintenanceCheckResults.stream()
                        .anyMatch(
                            cResult ->
                                MaintenanceHistoryMatchCriteria.fromCheckResult(cResult)
                                    .matchesHistory(history));

                final boolean isMaintenanceOnActiveCluster =
                    pActiveClusters.stream()
                        .map(Cluster::getClusterDescription)
                        .map(ClusterDescription::getName)
                        .anyMatch(history.getClusterName()::equals);

                if (!isMaintenanceOnActiveCluster || !anyCheckResultMatchesHistory) {
                  c.accept(history);
                }
              } else if (history.getMaintenanceType() == MaintenanceType.EXTERNAL_MAINTENANCE) {
                final boolean anyCheckResultMatchesHistory =
                    pMaintenanceCheckResults.stream()
                        .anyMatch(
                            cResult ->
                                MaintenanceHistoryMatchCriteria.fromCheckResult(cResult)
                                    .matchesHistory(history));

                if (!anyCheckResultMatchesHistory) {
                  c.accept(history);
                }
              } else if (history.getMaintenanceType().isGroupLevelMaintenance()) {
                final boolean checkResultsHaveAnyWithSameMaintenanceTypeAsHistory =
                    pMaintenanceCheckResults.stream()
                        .filter(MaintenanceCheckResult::needMaintenance)
                        .map(MaintenanceCheckResult::maintenanceType)
                        .anyMatch(history.getMaintenanceType()::equals);

                if (!checkResultsHaveAnyWithSameMaintenanceTypeAsHistory) {
                  c.accept(history);
                }
              }
            })
        .map(NdsMaintenanceHistory::getId)
        .toList();
  }

  @VisibleForTesting
  NdsMaintenanceHistory newMaintenanceHistory(
      final MaintenanceCheckResult pMaintenanceCheckResult,
      final NDSGroup pNdsGroup,
      final Group pGroup) {
    final Date targetMaintenanceDate =
        _maintenanceDateCalculationUtil
            .getNextMaintenanceStartDateTimeForHistory(
                pNdsGroup, pGroup, pMaintenanceCheckResult.getSchedulingBehavior())
            .getTime();

    final NdsMaintenanceHistory.Builder maintenanceHistoryBd =
        NdsMaintenanceHistory.builder()
            .setState(NdsMaintenanceHistory.State.PENDING)
            .setGroupId(pNdsGroup.getGroupId())
            .setTargetMaintenanceDate(targetMaintenanceDate)
            .setIsCritical(pMaintenanceCheckResult.isCriticalRelease())
            .setMaintenanceType(pMaintenanceCheckResult.maintenanceType())
            .setSchedulingBehavior(pMaintenanceCheckResult.getSchedulingBehavior())
            .setFromVersion(pMaintenanceCheckResult.getFromVersion())
            .setToVersion(pMaintenanceCheckResult.getToVersion())
            .setMaintenanceStartedDate(pMaintenanceCheckResult.isStarted() ? new Date() : null)
            .setClusterName(
                Optional.ofNullable(pMaintenanceCheckResult.clusterDescription())
                    .map(ClusterDescription::getName)
                    .orElse(null));

    if (pMaintenanceCheckResult.maintenanceType() == MaintenanceType.EXTERNAL_MAINTENANCE) {
      maintenanceHistoryBd.setExternalMaintenanceNames(
          pMaintenanceCheckResult.getExternalMaintenanceNames());
    }

    if (pMaintenanceCheckResult.getInstanceHardwareDetails() != null) {
      // Update builder for maintenance of type Instance Reboot
      // find previous maintenance history which is NO_LONGER_REQUIRED and has a
      // rebootRequestedChefCommitHash value
      final Optional<String> previousChefCommitHash =
          _ndsMaintenanceHistorySvc.findPreviousChefCommitHash(
              pGroup.getId(),
              pMaintenanceCheckResult.clusterDescription().getName(),
              pMaintenanceCheckResult.getInstanceHardwareDetails().getHostname());

      maintenanceHistoryBd.setInstanceHardwareDetails(
          new InstanceHardwareDetails(
              pMaintenanceCheckResult.getInstanceHardwareDetails().getHostname(),
              pMaintenanceCheckResult.getInstanceHardwareDetails().getRebootRequestedDate(),
              Collections.emptyList(),
              pMaintenanceCheckResult
                  .getInstanceHardwareDetails()
                  .getRebootRequestedChefCommitHash(),
              previousChefCommitHash.orElse(null)));
    }

    return maintenanceHistoryBd.build();
  }

  @VisibleForTesting
  protected void doReadyPlanPlanning(
      final Plan pPlan,
      final PlanSummary pPlanSummary,
      final PlanResult pPlanResult,
      final NDSGroup pNDSGroup,
      final Logger pLogger,
      final List<Cluster> pClusters,
      final NextPlanningDateContext.Builder pNextPlanningDateContextBuilder,
      @Nullable final ObjectId pPlanGenerationJobId,
      final Map<ObjectId, ItemDiff> pClusterDescriptionDiff) {
    if (_planCallbackSvc.isThrottled(pPlan, pPlanSummary, pNDSGroup, pLogger)) {
      pLogger.info(
          "The generated plan {} for group {} is throttled, so will not be executed at this"
              + " time.",
          pPlan.getId(),
          pNDSGroup.getGroupId());
      NDSPromMetricsSvc.incrementCounter(PLANNING_ROUND_THROTTLED_PLANS_COUNTER);
      return;
    }
    pLogger.info("Starting new plan {}", pPlan.getId());
    saveAuditEvent(NDSAudit.Type.PLAN_STARTED, pNDSGroup.getGroupId(), pPlanSummary);
    try {
      // NB: the plan must be recorded on the group _prior_ to the plan being scheduled.
      // This ensures that a plan cannot be scheduled without a future call to processCurrentPlans
      // handling the result of the plan
      _ndsGroupDao.startPlan(pNDSGroup.getGroupId(), pPlanSummary, pPlanGenerationJobId);
      pNextPlanningDateContextBuilder.setShouldRefreshNdsGroup(true);
    } catch (final IllegalStateException e) {
      final NDSGroup ndsGroupWithUpdatedCurrentPlans =
          _ndsGroupDao.find(pNDSGroup.getGroupId()).get();
      pLogger.debug(
          "Currently executing plans: {}", ndsGroupWithUpdatedCurrentPlans.getCurrentPlans());
      pLogger.debug(
          "Current NDSGroup state: {}", ndsGroupWithUpdatedCurrentPlans.getState().name());
      pLogger.debug("Failed to start plan: {}", pPlanSummary);
      throw e;
    }

    pClusters.stream()
        .map(Cluster::getClusterDescription)
        .filter(
            clusterDescription ->
                clusterDescription.getName().equals(pPlanSummary.getClusterName().orElse(null)))
        .forEach(
            clusterDescription -> {
              _clusterSvc.clusterChangesStarted(
                  pNDSGroup.getGroupId(),
                  clusterDescription.getName(),
                  pClusterDescriptionDiff.getOrDefault(clusterDescription.getUniqueId(), null),
                  pPlan.getAuditDescription(),
                  pPlan.getId(),
                  pPlanSummary.getIsMaterialClusterPlan());

              if (pPlanResult
                  .getInvolvedRepairingClusters()
                  .contains(clusterDescription.getName())) {
                _clusterSvc.setState(pNDSGroup.getGroupId(), clusterDescription, State.REPAIRING);
              } else {
                _clusterSvc.setState(pNDSGroup.getGroupId(), clusterDescription, State.WORKING);
              }
            });

    final PlanPriority priority =
        _appSettings.isNDSPlanExecutorJobPriorityEnabled()
                && _ndsPlanExecutorJobPrioritySvc.containsDeprioritizedMove(pPlan.getMoves())
            ? PlanPriority.LOW
            : PlanExecutorJobHandler.DEFAULT_PRIORITY;
    _planSvc.savePlanAndInsertJob(
        pPlan, PlanExecutorJobHandler.getNewJobForNDSPlan(pPlan.getId(), priority));

    NDSPlanningSummary.tryHandleClusterNameForAutoShardingPlan(
        pPlan,
        clusterName -> {
          pLogger.info(
              "Associating auto-sharding plan {} with context for cluster {} in group {},"
                  + " setting status to ADDING_SHARD",
              pPlan.getId(),
              clusterName,
              pPlan.getGroupId());
          _autoScalingContextDao.setAutoSharding(
              pPlan.getGroupId(), clusterName, pPlan.getId(), ShardingStatus.ADDING_SHARD, null);
        });

    if (pNDSGroup
        .getNextPlanningDateSetBy()
        .equals(NextPlanningDateSetBy.PLANNER_DEFAULT_INTERVAL)) {
      tryLogMovesWithDefaultPlanningInterval(pPlan, pLogger);
    } else {
      pLogger.info("Plan {} saved", pPlan.getId());
    }
    countCreatedPlan(pPlanSummary, pNDSGroup);
    countCreatedMovesWithinPlan(pPlan, pNDSGroup);

    // If there is a node(InstanceHardware) that was forced to be primary we need
    // to clear its state in the database now when planning round is complete.
    // Force selection is done when a replica set does not have any primary or secondary,
    // and one node is explicitly selected as primary from the admin console only for the
    // purpose of planning to continue.
    clearForcePlanningPrimary(pClusters, pPlanSummary);
  }

  protected void refreshNdsGroupIfNeeded(
      final ObjectId pNdsGroupId, final NextPlanningDateContext.Builder pBuilder) {
    if (pBuilder._shouldRefreshNDSGroup) {
      final NDSGroup updatedNdsGroup = _ndsGroupDao.find(pNdsGroupId).get();
      pBuilder.setNdsGroup(updatedNdsGroup);
    }
  }

  private void tryLogMovesWithDefaultPlanningInterval(final Plan pPlan, final Logger pLogger) {
    final String allMoves =
        pPlan.getMoves().stream()
            .map(move -> move.getClass().getSimpleName())
            .collect(Collectors.joining(", "));
    pLogger.debug(
        "Plan {} saved using default planning interval. Moves are: {}", pPlan.getId(), allMoves);
  }

  private void clearForcePlanningPrimary(
      final List<Cluster> clusters, final PlanSummary planSummary) {
    clusters.stream()
        .filter(
            c ->
                c.getClusterDescription()
                    .getName()
                    .equals(planSummary.getClusterName().orElse(null)))
        .forEach(
            cluster ->
                cluster
                    .getReplicaSets()
                    .forEach(
                        rs ->
                            rs.getInternalHardware().stream()
                                .filter(InstanceHardware::isForcePlanningPrimary)
                                .forEach(
                                    iH ->
                                        _hardwareSvc.setForcePrimaryForHost(
                                            rs, iH.getInstanceId(), false))));
  }

  Set<RegionName> getSnapshotDistributionRegionNames(final List<BackupJob> backupJobs) {
    return backupJobs.stream()
        .map(BackupJob::getSnapshotDistributionRegionNames)
        .flatMap(Collection::stream)
        .collect(Collectors.toSet());
  }

  Map<ObjectId, List<TenantEndpoint>> getTenantEndpointsMappedByCluster(
      final List<Cluster> pClusters) {
    return pClusters.stream()
        .map(Cluster::getClusterDescription)
        .filter(
            pClusterDescription ->
                pClusterDescription.isServerlessTenantCluster()
                    && NDSSettings.getSupportedCloudProvidersForServerlessPrivateNetworking(
                            _appSettings)
                        .contains(pClusterDescription.getBackingProvider()))
        .collect(
            Collectors.toMap(
                ClusterDescription::getUniqueId, _ndsTenantEndpointSvc::getEndpointsForTenant));
  }

  @VisibleForTesting
  protected void updateUnableToPlanClusters(
      final NDSGroup pNDSGroup,
      final List<Cluster> pUnableToPlanClusters,
      final NextPlanningDateContext.Builder pNextPlanningDateContextBuilder) {
    final List<String> clustersToAdd = new ArrayList<>();
    final Set<String> clustersToRemove = new HashSet<>(pNDSGroup.getUnableToPlanClusterNames());

    for (Cluster cluster : pUnableToPlanClusters) {
      final String clusterName = cluster.getClusterDescription().getName();
      // if the cluster is still unable to plan, we want to keep it in the UnableToPlanClusterNames
      // list, therefore not removing it from the list.
      if (clustersToRemove.contains(clusterName)) {
        clustersToRemove.remove(clusterName);
      } else {
        clustersToAdd.add(clusterName);
      }
    }

    if (!clustersToAdd.isEmpty()) {
      _ndsGroupDao.addUnableToPlanClusters(pNDSGroup.getGroupId(), clustersToAdd);
    }

    if (!clustersToRemove.isEmpty()) {
      _ndsGroupDao.removeUnableToPlanClusters(
          pNDSGroup.getGroupId(), new ArrayList<>(clustersToRemove));
    }
    if (!clustersToAdd.isEmpty() || !clustersToRemove.isEmpty()) {
      pNextPlanningDateContextBuilder.setShouldRefreshNdsGroup(true);
    }
  }

  boolean verifyIdleProject(
      final Date pPlanningRoundStartTime,
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final PlanResult pNewPlans,
      final NextPlanningDateContext.Builder pNextPlaningDateContextBuilder) {
    if (!pNewPlans.getPlanSummaryPairs().isEmpty() || !pNDSGroup.getCurrentPlans().isEmpty()) {
      return false;
    }

    final long runtimeMS = System.currentTimeMillis() - pPlanningRoundStartTime.getTime();
    pLogger.trace("No plan needed in {} milliseconds.", runtimeMS);

    // Since it is possible something failed and then was made no longer necessary because
    // of a user change, always clear the failed plans if there is nothing to do.
    if (!pNDSGroup.getFailedPlans().isEmpty()) {
      pLogger.debug("Clearing failed plans");
      _ndsGroupDao.clearFailedPlans(pNDSGroup.getGroupId());
      pNextPlaningDateContextBuilder.setShouldRefreshNdsGroup(true);
    }
    return true;
  }

  void ensureIdleClustersState(
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final List<Cluster> pClusters,
      final PlanResult pNewPlans) {
    // create a list of PlanSummary consisted of new plans generated and existing plans in the group
    final List<PlanSummary> allPlans =
        Stream.concat(
                pNewPlans.getPlanSummaryPairs().stream().map(Pair::getLeft),
                pNDSGroup.getCurrentPlans().stream())
            .toList();

    final Set<String> clustersWithPlans =
        allPlans.stream().flatMap(p -> p.getClusterName().stream()).collect(Collectors.toSet());

    // set non-working clusters to idle
    pClusters.stream()
        .filter(
            c ->
                !clustersWithPlans.contains(c.getClusterDescription().getName())
                    && c.getClusterDescription().getState() != ClusterDescription.State.IDLE)
        .forEach(
            c -> {
              pLogger.debug("Setting cluster {} as IDLE", c.getClusterDescription().getName());
              _clusterSvc.setState(pNDSGroup.getGroupId(), c.getClusterDescription(), State.IDLE);
            });
  }

  private void queueSnapshotsIfScheduleIsReady(
      final Group group,
      final List<Cluster> clusters,
      final List<BackupJob> backupJobs,
      final Logger logger,
      final Set<String> liveImportDestinationClusters) {

    for (final Cluster cluster : clusters) {
      final ClusterDescription clusterDescription = cluster.getClusterDescription();
      final BackupJob backupJob =
          backupJobs.stream()
              .filter(b -> clusterDescription.getUniqueId().equals(b.getClusterUniqueId()))
              .findFirst()
              .orElse(null);
      if (clusterDescription.isDiskBackupEnabled() && backupJob == null) {
        logger.warn(
            "Cluster has disk backups enabled but does not have backup job. projectId={},"
                + " clusterName={}",
            clusterDescription.getGroupId(),
            clusterDescription.getName());
      }

      if (!liveImportDestinationClusters.contains(clusterDescription.getName())) {
        queueSnapshotForClusterIfReady(group, clusterDescription, backupJob, logger);
      } else {
        LOG.info(
            "Skipping queuing of snapshot due to active live import. projectId={} clusterName={}",
            clusterDescription.getGroupId(),
            clusterDescription.getName());
        NDSPromMetricsSvc.incrementCounter(CPS_SNAPSHOT_SKIPPED, LIVE_IMPORT);
      }
    }
  }

  private void queueSnapshotForClusterIfReady(
      final Group group,
      final ClusterDescription cluster,
      final BackupJob job,
      final Logger logger) {
    final Date now = new Date();

    final CpsSnapshotEngine cpsSnapshotEngine =
        new CpsSnapshotEngine.Builder()
            .cpsPitSvc(_cpsPitSvc)
            .cpsSvc(_cpsSvc)
            .ndsBackupPolicySvc(_cpsPolicySvc)
            .backupSnapshotDao(_snapshotDao)
            .logger(logger)
            .build();

    cpsSnapshotEngine.tryQueue(now, group, job, cluster);
  }

  protected boolean planHasResyncMove(final Plan pPlan) {
    return pPlan.getMoves().stream().anyMatch(move -> move instanceof ResyncBaseMove);
  }

  protected boolean planHasUpdateMachineOrReplaceDiskMove(final Plan pPlan) {
    return pPlan.getMoves().stream()
        .anyMatch(move -> move instanceof UpdateMachineSizeMove || move instanceof ReplaceDiskMove);
  }

  @VisibleForTesting
  protected List<Pair<PlanSummary, Plan>> getPlansWithoutBackoff(
      final List<Pair<PlanSummary, Plan>> pNewPlans,
      final List<PlanSummary> pFailedPlans,
      final Logger pLogger) {
    return pNewPlans.stream()
        .filter(
            newPlan -> {
              final Optional<PlanSummary> overlappingPlan =
                  pFailedPlans.stream()
                      .filter(failedPlan -> failedPlan.sharesResource(newPlan.getLeft(), null))
                      .findFirst();

              // If an overlapping plan is present, we should ignore the backoff period if the new
              // action differs
              // from the failing action. For example, there is no reason to wait for the backoff
              // period if we
              // want to delete a resource that is failing to create, update, etc.
              if (overlappingPlan.isEmpty()
                  || !overlappingPlan
                      .get()
                      .getActionType()
                      .equals(newPlan.getLeft().getActionType())) {
                return true;
              }

              final Date allowedAfter =
                  new Date(
                      overlappingPlan.get().getLastFailureDate().getTime()
                          + NDSGroup.getBackoffDuration(overlappingPlan.get().getFailedCount())
                              .toMillis());

              if (!new Date().after(allowedAfter)) {
                pLogger.info(
                    "Skipping plan {}/{} because has not met the backoff period after failure."
                        + " A retry will be allowed after {}",
                    newPlan.getLeft().getClusterName().orElse(""),
                    newPlan.getLeft().getContainerIds().stream()
                        .map(ObjectId::toString)
                        .collect(Collectors.joining(" ")),
                    allowedAfter);
                return false;
              }

              return true;
            })
        .collect(Collectors.toList());
  }

  /**
   * Delete the serverless MTM pool for the given group iff there are no {@link
   * ResourceType#SERVERLESS_LOAD_BALANCING_DEPLOYMENT} plans in progress or in the failed state,
   * and the pool is requested for deletion
   *
   * <p>NB: when there are multiple serverless load balancing deployments in a group, and they are
   * being deleted, it is possible to end up with more than one plan to delete a serverless load
   * balancing deployment.
   */
  protected void handleServerlessMTMPoolDeletionRequest(
      final NDSGroup pNDSGroup, final List<Plan> pPlans, final Logger pLogger) {
    if (!pNDSGroup.isServerlessMTMHolder()) {
      return;
    }

    if (pPlans.stream()
        .filter(p -> !p.getLastResult().isDone() || p.wasFailure())
        .map(Plan::getId)
        .map(pNDSGroup::getCurrentPlanById)
        .flatMap(Optional::stream)
        .anyMatch(
            ps ->
                ps.getResourceType() == ResourceType.SERVERLESS_LOAD_BALANCING_DEPLOYMENT
                    && ps.getActionType() == ActionType.DELETE)) {
      return;
    }

    final ServerlessMTMPool pool =
        _serverlessMTMPoolSvc
            .findPoolByGroupId(pNDSGroup.getGroupId())
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        String.format(
                            "Missing Serverless MTM Pool for group %s", pNDSGroup.getGroupId())));

    if (pool.isDeleteRequested()) {
      final List<ServerlessLoadBalancingDeployment> deployments =
          _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(pNDSGroup.getGroupId());

      final boolean deploymentsAllUnprovisioned =
          deployments.stream().noneMatch(ServerlessLoadBalancingDeployment::isProvisioned);

      if (deploymentsAllUnprovisioned) {
        try {
          _serverlessMTMPoolSvc.deletePool(pool.getId());
          _ndsGroupSvc.unsetServerlessMTMHolder(pNDSGroup.getGroupId());
        } catch (final SvcException pE) {
          pLogger.error("Failed to delete pool {}", pool.getId(), pE);
        }
      }
    }
  }

  protected void setFastTenantPreAllocatedRecordState(
      final ObjectId pContainerId, final NDSGroup pNDSGroup) {
    // In this special case where the group has both M0 and migrated Flex MTM clusters, we need to
    // check both our Shared and FLex Fast Tenant Record Daos
    if (pNDSGroup.isFreeFlexMTMHolder()) {
      if (!_fastTenantPreAllocatedRecordDaoProvider
          .getByMTMClusterType(MTMClusterType.SHARED)
          .setStateByContainerId(
              pContainerId,
              FastTenantPreAllocatedRecord.State.AVAILABLE,
              List.of(FastTenantPreAllocatedRecord.State.CREATING))) {
        _fastTenantPreAllocatedRecordDaoProvider
            .getByMTMClusterType(MTMClusterType.FLEX)
            .setStateByContainerId(
                pContainerId,
                FastTenantPreAllocatedRecord.State.AVAILABLE,
                List.of(FastTenantPreAllocatedRecord.State.CREATING));
      }
    } else {
      _fastTenantPreAllocatedRecordDaoProvider
          .getByMTMHolderGroup(pNDSGroup)
          .setStateByContainerId(
              pContainerId,
              FastTenantPreAllocatedRecord.State.AVAILABLE,
              List.of(FastTenantPreAllocatedRecord.State.CREATING));
    }
  }

  protected void setClusterDescriptionState(
      final NDSGroup pNDSGroup,
      final Plan pPlan,
      final String pClusterName,
      final Logger pLogger,
      final List<ReplicaSet> replicaSets) {
    final Optional<Cluster> clusterOpt =
        pPlan.wasSuccessful()
            ? _clusterSvc.getActiveCluster(pNDSGroup.getGroupId(), pClusterName)
            : Optional.empty();
    if (pPlan.wasSuccessful()) {
      // Generate the SRV and BI Connector addresses here instead of passing the plan
      // as an argument to the NDSClusterSvc.
      final Cluster cluster =
          clusterOpt.orElseThrow(
              () -> new IllegalStateException("Missing cluster description for " + pClusterName));
      final ClusterDescription clusterDescription = cluster.getClusterDescription();
      final String standardSrvAddress;
      final String privateSrvAddress;

      // we currently don't generate SRV addresses for cross-cloud clusters where subdomain level is
      // PROVIDER
      if (clusterDescription.getHostnameSubdomainLevel() == InstanceHostname.SubdomainLevel.MONGODB
          || !clusterDescription.isCrossCloudCluster()) {
        final CloudProvider provider =
            clusterDescription.isTenantCluster()
                ? clusterDescription.getBackingProvider()
                // If cluster is cross cloud at the MONGODB subdomain level, the provider doesn't
                // matter to getInstanceDomainName anyway, so just pick the first one
                : clusterDescription.getLegacyProvider();

        standardSrvAddress =
            DNSRecordUtil.getStandardClusterAddress(
                pNDSGroup,
                cluster,
                replicaSets,
                NDSSettings.getInstanceDomainName(
                    _appSettings, provider, clusterDescription.getHostnameSubdomainLevel()));

        privateSrvAddress =
            DNSRecordUtil.getPrivateClusterAddress(
                clusterDescription,
                pNDSGroup,
                NDSSettings.getInstanceDomainName(
                    _appSettings, provider, clusterDescription.getHostnameSubdomainLevel()));
      } else {
        standardSrvAddress = null;
        privateSrvAddress = null;
      }
      final Optional<InstanceHardware> hardwareForBiConnector =
          cluster.getLiveReplicaSets().stream()
              .flatMap(rs -> rs.getHardware().stream())
              .filter(h -> h.getBiConnector().isEnabled())
              .findFirst();

      final Hostnames biConnectorHostnames;
      if (hardwareForBiConnector.isPresent()) {
        biConnectorHostnames =
            NDSHostnameSvc.getDesiredHostnamesForBiConnector(
                clusterDescription,
                pNDSGroup,
                NDSSettings.getInstanceDomainName(
                    _appSettings,
                    hardwareForBiConnector.get().getCloudProvider(),
                    clusterDescription.getHostnameSubdomainLevel()),
                _appSettings);
      } else {
        biConnectorHostnames = new Hostnames();
      }
      final ObjectId planId;
      if (pPlan != null) {
        planId = pPlan.getId();
      } else {
        planId = null;
      }
      _clusterSvc.clusterChangesCompleted(
          pNDSGroup,
          clusterDescription.getName(),
          standardSrvAddress,
          privateSrvAddress,
          biConnectorHostnames,
          planId);
    }

    final State newState;
    if (pPlan.wasSuccessful()) {
      final Cluster cluster =
          clusterOpt.orElseThrow(
              () -> new IllegalStateException("Missing cluster description for " + pClusterName));
      final ClusterDescription clusterDescription = cluster.getClusterDescription();
      final ObjectId groupId = clusterDescription.getGroupId();
      final String clusterName = clusterDescription.getName();
      if (clusterDescription.isDeleteRequested()) {
        final boolean hardwareUnprovisioned =
            cluster.getReplicaSets().stream()
                .flatMap(ReplicaSetHardware::getAllHardware)
                .noneMatch(InstanceHardware::isProvisioned);
        if (hardwareUnprovisioned) {
          _hardwareSvc.cleanUnProvisionedHardwareForDeletedCluster(groupId, clusterName);
          _autoScalingContextDao.remove(groupId, clusterName);
          _predictiveAutoScalingContextSvc.deleteContextAndSkipTriggers(
              groupId, clusterDescription.getUniqueId(), TriggerSkippedReason.CLUSTER_DELETED);
          _adminClusterLockSvc.releaseLockForDeletedCluster(
              groupId, clusterName, AuditInfoHelpers.fromSystem());
          final long numberOfRollingResyncsCancelled =
              _rollingResyncSvc.cancelActiveRollingResyncsForCluster(groupId, clusterName);
          if (numberOfRollingResyncsCancelled > 0) {
            pLogger.info(
                "Cancelled {} rolling resyncs for ({},{}) due to cluster being deleted",
                numberOfRollingResyncsCancelled,
                groupId,
                clusterName);
          }
          newState = State.DELETED;

          if (clusterDescription.getClusterProvisionType().hasPreallocatedCapacity()) {
            updateFastRecordMetadataForDeletedCluster(clusterDescription);
          }

        } else {
          pLogger.error(
              "Detected provisioned hardware(s) on deleted cluster ({},{})", groupId, clusterName);
          newState = State.IDLE;
        }
      } else {
        deleteUnprovisionedAutoscalingShardContexts(cluster);
        _hardwareSvc.deleteUnProvisionedHardwares(pNDSGroup.getGroupId(), clusterName, pLogger);
        newState = State.IDLE;

        if (new ClusterDescriptionView(clusterDescription)
            .getState()
            .equals(ClusterDescriptionView.State.CREATING)) {
          _segmentEventSvc.submitEvent(getClusterProvisionedEvent(clusterDescription));
        }
      }
    } else {
      // Do not want to change the state on failure, since we want the state
      // to persist while we are waiting for the backoff period.
      return;
    }

    _clusterSvc.setState(
        pNDSGroup.getGroupId(), clusterOpt.get().getClusterDescription(), newState);
  }

  private void deleteUnprovisionedAutoscalingShardContexts(final Cluster cluster) {
    final List<ReplicaSetHardware> allUnprovisionedReplicaSets =
        cluster.getReplicaSets().stream()
            .filter(rh -> rh.getAllHardware().noneMatch(InstanceHardware::isProvisioned))
            .toList();

    // Handle for reactive autoscaling
    final List<String> allUnprovisionedRsIds =
        allUnprovisionedReplicaSets.stream().map(ReplicaSetHardware::getRsId).toList();
    final ClusterDescription clusterDescription = cluster.getClusterDescription();
    _autoScalingContextDao.deleteShardsFromShardContext(
        clusterDescription.getGroupId(), clusterDescription.getName(), allUnprovisionedRsIds);

    // Handle for predictive autoscaling
    if (clusterDescription.isPredictiveAutoScalingEnabled(NodeTypeFamily.BASE)
        && clusterDescription.getAutoScalingMode() == AutoScalingMode.SHARD) {
      _predictiveAutoScalingContextSvc.updateContextForDeletedShards(
          clusterDescription.getGroupId(), cluster);
    }
  }

  protected void updateFastRecordMetadataForDeletedCluster(
      final ClusterDescription pClusterDescription) {

    if (!pClusterDescription.isTenantCluster() || !pClusterDescription.isFastProvisioned()) {
      return;
    }

    final Optional<? extends FastTenantPreAllocatedRecord> existingFastRecord =
        _fastTenantPreAllocatedRecordDaoProvider
            .getByTenantCluster(pClusterDescription)
            .findByPinAndDeploymentName(
                pClusterDescription.getDnsPin(), pClusterDescription.getDeploymentClusterName());

    if (existingFastRecord.isPresent()
        && existingFastRecord.get().getState() == FastTenantPreAllocatedRecord.State.CLAIMED) {
      // if the instance never came up, we want to clean up the fast record here as we never went
      // through the cluster creation or deletion flow
      _fastTenantPreAllocatedRecordDaoProvider
          .getByTenantCluster(pClusterDescription)
          .setStateByRecordId(
              existingFastRecord.get().getId(),
              FastTenantPreAllocatedRecord.State.NEEDS_CLEANUP,
              List.of(FastTenantPreAllocatedRecord.State.CLAIMED));
    } else {
      _fastTenantPreAllocatedRecordDaoProvider
          .getByTenantCluster(pClusterDescription)
          .markDeletedByPinAndDeploymentName(
              pClusterDescription.getDnsPin(), pClusterDescription.getDeploymentClusterName());
    }
  }

  protected ClusterProvisionedEvent getClusterProvisionedEvent(
      final ClusterDescription pClusterDescription) {
    final ObjectId orgId = _groupSvc.findById(pClusterDescription.getGroupId()).getOrgId();

    final Optional<ClusterDescriptionProcessArgs> processArgs =
        _clusterSvc.getProcessArgs(pClusterDescription.getGroupId(), pClusterDescription.getName());

    return ClusterProvisionedEvent.forCluster(pClusterDescription, orgId, processArgs);
  }

  /**
   * If the group is in the {@link NDSState#WORKING} state, log any in-progress plans and finalize
   * any plans that have completed since the last planning round for this group
   */
  private void processCurrentPlans(
      NDSGroup pNDSGroup, Logger pLogger, AutomationConfigContext automationConfigContext) {
    if (pNDSGroup.getState() != NDSState.WORKING) {
      return;
    }

    final List<ObjectId> currentPlanIds = pNDSGroup.getCurrentPlanIds();
    if (currentPlanIds.isEmpty()) {
      throw new IllegalStateException("State is WORKING, but there are no current plans");
    }

    final List<Plan> plans = _planDao.find(currentPlanIds);
    if (plans.size() != currentPlanIds.size()) {
      for (final ObjectId pid : currentPlanIds) {
        if (plans.stream().noneMatch(p -> p.getId().equals(pid))) {
          // plan may have been set on the group, but failed to be scheduled
          // clear it and rely on this planning round to reschedule the plan
          pLogger.debug(
              "Plan id={} is set on the group but the plan was not found. Will remove from"
                  + " group currentPlans.",
              pid);
          final boolean isCleared =
              _ndsGroupDao.clearCurrentPlan(
                  pNDSGroup.getGroupId(), pNDSGroup.getCurrentPlanById(pid).get());
          if (!isCleared) {
            pLogger.debug(
                "Failed to remove the invalid plan {}, will retry next planning round", pid);
          }
        }
      }
    }

    // log working plans
    plans.stream()
        .filter(p -> !p.getLastResult().isDone())
        .forEach(
            pPlan -> {
              final ObjectId planId = pPlan.getId();
              pLogger.debug("State is WORKING, and plan {} is still in progress", planId);
              if (pPlan.getLastResult() == Plan.PlanResult.NOT_STARTED) {
                // Temp logging for https://jira.mongodb.org/browse/CLOUDP-213386
                final Job job = _jobSvc.findJobById(pPlan.getId());
                if (job == null) {
                  pLogger.error(
                      "No job exists for working plan. Need to manually fail the plan {} to unblock"
                          + " planning for the group",
                      planId);
                  NDSPromMetricsSvc.incrementCounter(WORKING_PLAN_WITHOUT_ASSOCIATED_JOB_COUNTER);
                }
              }
            });

    handleServerlessMTMPoolDeletionRequest(pNDSGroup, plans, pLogger);

    // process completed plans
    plans.stream()
        .filter(p -> p.getLastResult().isDone())
        .forEach(
            plan -> {
              pLogger.info("Plan {} is complete", plan.getId());
              final Optional<PlanSummary> inProgressPlanOpt =
                  pNDSGroup.getCurrentPlanById(plan.getId());

              if (inProgressPlanOpt.isPresent()) {
                final PlanSummary inProgressPlan = inProgressPlanOpt.get();
                if (inProgressPlan.getResourceType() == ResourceType.CLUSTER) {
                  setClusterDescriptionState(
                      pNDSGroup,
                      plan,
                      inProgressPlan.getClusterName().get(),
                      pLogger,
                      automationConfigContext
                          .getAutomationConfigOrEmptyConfig()
                          .getDeployment()
                          .getReplicaSets());
                }
                if (inProgressPlan.getResourceType() == ResourceType.FAST_PROVISION
                    && plan.wasSuccessful()
                    && inProgressPlan.getActionType() == ActionType.CREATE) {
                  setFastTenantPreAllocatedRecordState(
                      inProgressPlan.getContainerIds().get(0), pNDSGroup);
                }

                _ndsGroupDao.completePlan(
                    pNDSGroup.getGroupId(), inProgressPlan, plan.wasSuccessful());
                saveAuditEvent(
                    Type.PLAN_COMPLETED, pNDSGroup.getGroupId(), inProgressPlanOpt.get());
              }

              if (plan.wasFailure()) {
                // Prioritize errors that occur during the perform stage of a plan
                final Optional<BasicDBObject> lastError =
                    _ndsLogDao
                        .findLastError(pNDSGroup.getGroupId(), plan.getId(), ExecutionStage.PERFORM)
                        .or(() -> _ndsLogDao.findLastError(pNDSGroup.getGroupId(), plan.getId()));
                saveAudit(
                    Type.PLAN_FAILURE,
                    pNDSGroup.getGroupId(),
                    plan.getFailureCode(),
                    inProgressPlanOpt,
                    lastError.map(NDSLogDao::formatLog).orElse(null));
              }
              countCompletedPlan(plan, inProgressPlanOpt);
            });
  }

  /** Indicates whether the given group is eligible for planning */
  protected boolean shouldPlan(final NDSGroup pNDSGroup, final Logger pLogger) {
    return switch (pNDSGroup.getState()) {
      case IDLE -> {
        pLogger.trace("State is IDLE");
        yield true;
      }
      case PAUSED -> {
        pLogger.debug("State is PAUSED");
        yield false;
      }
      case CLOSED -> {
        pLogger.debug("State is CLOSED");
        yield false;
      }
      case DEAD -> {
        pLogger.debug("State is DEAD");
        yield true;
      }
      case WORKING -> true;
    };
  }

  public void refreshClusterInstanceNeedsHostnameChange(
      final Cluster pCluster, final AutomationConfigContext pAutomationConfigContext) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    for (final ReplicaSetHardware replicaSetHardware : pCluster.getReplicaSets()) {
      for (final InstanceHardware instanceHardware : replicaSetHardware.getAllHardware().toList()) {
        if (instanceHardware.needsHostnameChange()
            || instanceHardware.getCloudProvider().isTenantProvider()) {
          continue;
        }

        final SubdomainLevel clusterSubdomainLevel =
            pCluster.getClusterDescription().getHostnameSubdomainLevel();

        final CloudProvider desiredCloudProvider =
            getInstanceDesiredCloudProvider(
                clusterDescription, replicaSetHardware, instanceHardware);

        final SubdomainLevel desiredSubdomainLevel =
            InstanceHostnameSvc.effectiveSubdomainLevelForProvider(
                desiredCloudProvider, clusterSubdomainLevel);

        final AutomationConfig automationConfig =
            pAutomationConfigContext.getAutomationConfigOrEmptyConfig();

        final Optional<String> memberNameForReplicaSetMember =
            automationConfig
                .getDeployment()
                .getReplicaSetByName(replicaSetHardware.getRsId())
                .flatMap(rs -> rs.getMember(instanceHardware.getMemberIndex()))
                .map(ReplicaSetMember::getHost);

        final boolean subdomainLevelChange =
            memberNameForReplicaSetMember.stream()
                .flatMap(
                    memberName ->
                        automationConfig.getDeployment().getProcessByName(memberName).stream())
                .map(
                    process ->
                        NDSSettings.getEffectiveHostnameSubdomainLevelOfHostname(
                            _appSettings, process.getHostname()))
                .anyMatch(level -> level != desiredSubdomainLevel);

        if (subdomainLevelChange) {
          _hardwareDao.setInstanceField(
              replicaSetHardware.getId(),
              instanceHardware.getInstanceId(),
              replicaSetHardware.isInstanceInternal(instanceHardware.getInstanceId()),
              InstanceHardware.FieldDefs.NEEDS_HOSTNAME_CHANGE,
              true);
        }
      }
    }
  }

  CloudProvider getInstanceDesiredCloudProvider(
      final ClusterDescription pClusterDescription,
      final ReplicaSetHardware replicaSetHardware,
      final InstanceHardware instanceHardware) {
    return ReplicationSpecListUtils.getInstanceDesiredCloudProvider(
        pClusterDescription, replicaSetHardware, instanceHardware.getInstanceId());
  }

  public List<MaintenanceCheckResult> refreshClusterHardwareActions(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<HostCluster> pHostClusters,
      final Long lastMonitoringAgentPingTime,
      final Logger pLogger,
      final Cluster pCluster,
      final List<RollingResync> pRollingResyncs,
      final AutomationConfigContext pAutomationConfigContext,
      final ClusterMaintenanceDecisions.Builder pClusterMaintenanceDecisionsBuilder) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    // No maintenance is required for paused M0 clusters; cleanup ReplicaSetHardware data if it
    // still exists; exit to ensure ReplicaSetHardware data is not recreated
    if (clusterDescription.isAutomaticallyPaused()
        && pNDSGroup
            .getCloudProviderContainer(
                CloudProvider.FREE,
                clusterDescription.getRegionName(),
                clusterDescription.getName())
            .isEmpty()) {
      if (!pCluster.getReplicaSets().isEmpty()) {
        _hardwareSvc.cleanUnProvisionedHardwareForDeletedCluster(
            clusterDescription.getGroupId(), clusterDescription.getName());
      }
      return List.of();
    }
    final boolean isNewCluster = pCluster.getReplicaSets().isEmpty();
    if (ReplicaSetHardwareSvc.isTransitioningFromReplicaSetToEmbeddedConfig(
        isNewCluster, pCluster)) {
      _clusterSvc.setNeedsPublishWithRestartForCluster(
          clusterDescription.getGroupId(), clusterDescription.getName());
    }

    final Cluster cluster =
        Cluster.getCluster(
            clusterDescription, _hardwareSvc.ensureHardware(pNDSGroup, pCluster, pLogger, pGroup));

    if (cluster.getClusterDescription().getAutoScalingMode() == AutoScalingMode.SHARD
        && !clusterDescription.isTenantCluster()) {
      if (pCluster.getReplicaSets().size() < cluster.getReplicaSets().size()) {
        addNewHardwareToShardAutoScalingContext(pGroup.getId(), cluster, pLogger);
      }
      // Reactive context includes config servers, but we explicitly exclude them for predictive
      if (cluster.getClusterDescription().isPredictiveAutoScalingEnabled(NodeTypeFamily.BASE)) {
        _predictiveAutoScalingContextSvc.updateContextForNewShards(pGroup.getId(), cluster);
      }
    }

    // Must be called with post-ensureHardware ReplicaSetHardwares to enable comparison between goal
    // state
    // reflected in ClusterDescription and current state reflected in ReplicaSetHardwares
    refreshClusterInstanceNeedsHostnameChange(cluster, pAutomationConfigContext);
    final Map<String, RollingResync> rsIdToResync =
        pRollingResyncs.stream()
            .collect(Collectors.toMap(RollingResync::getRsId, Function.identity()));

    final boolean isTransitioningToEmbeddedConfig;
    final List<MaintenanceCheckResult> maintenanceCheckResults = new ArrayList<>();

    _ndsGroupMaintenanceSvc
        .checkAndRunAZBalancingMaintenance(pGroup, pNDSGroup, cluster)
        .ifPresent(maintenanceCheckResults::add);

    if (clusterDescription.getClusterType().isSharded()) {
      final ReplicaSetHardware config = cluster.getReplicaSetWithConfigData().get();

      final Optional<ReplicationSpec> configServerReplicationSpec =
          clusterDescription.getReplicationSpecById(config.getReplicationSpecId());

      final boolean shouldDeleteReplicaSetHardware =
          shouldDeleteReplicaSetHardware(clusterDescription, config, false, false, false, pLogger);

      maintenanceCheckResults.addAll(
          refreshReplicaSetInstanceHardwareAction(
              clusterDescription,
              configServerReplicationSpec,
              pGroup,
              pNDSGroup,
              shouldDeleteReplicaSetHardware,
              config,
              Optional.ofNullable(rsIdToResync.get(config.getRsId())),
              pHostClusters,
              lastMonitoringAgentPingTime,
              pLogger));

      final ReplicaSetHardware configReplicaSetHardwareWithLatestInstanceHardwareActions =
          _hardwareSvc
              .getConfigReplicaSetHardware(
                  pGroup.getId(), cluster.getClusterDescription().getName())
              .get();

      final ReplicaSetHardware.Action updatedConfigReplicaSetHardwareAction =
          refreshReplicaSetHardwareAction(
              cluster,
              configReplicaSetHardwareWithLatestInstanceHardwareActions,
              shouldDeleteReplicaSetHardware,
              pAutomationConfigContext,
              pLogger);
      pClusterMaintenanceDecisionsBuilder.setIsConfigServerTypeTransitioning(
          updatedConfigReplicaSetHardwareAction, config.getAction());
      isTransitioningToEmbeddedConfig =
          updatedConfigReplicaSetHardwareAction.isTransitioningToEmbeddedConfig();
    } else {
      isTransitioningToEmbeddedConfig = false;
    }

    cluster
        .getAllReplicationSpecIdsToAllHardwareMap()
        .forEach(
            (specId, rhList) -> {
              final List<ReplicaSetHardware> nonConfigReplicaSets =
                  rhList.stream()
                      .filter(rsh -> !rsh.containsConfigData())
                      .collect(Collectors.toList());
              final Optional<ReplicationSpec> spec =
                  clusterDescription.getReplicationSpecById(specId);
              for (int index = 0; index < nonConfigReplicaSets.size(); index++) {
                final ReplicaSetHardware rh = nonConfigReplicaSets.get(index);

                final boolean isShardHardwareWithNoReplicationSpec = spec.isEmpty();
                final boolean shouldDeleteReplicaSetHardware =
                    shouldDeleteReplicaSetHardware(
                        clusterDescription,
                        rh,
                        isTransitioningToEmbeddedConfig,
                        isShardIndexOutOfBounds(index, clusterDescription, spec),
                        isShardHardwareWithNoReplicationSpec,
                        pLogger);

                maintenanceCheckResults.addAll(
                    refreshReplicaSetInstanceHardwareAction(
                        clusterDescription,
                        spec,
                        pGroup,
                        pNDSGroup,
                        shouldDeleteReplicaSetHardware,
                        rh,
                        Optional.ofNullable(rsIdToResync.get(rh.getRsId())),
                        pHostClusters,
                        lastMonitoringAgentPingTime,
                        pLogger));

                final ReplicaSetHardware.Action updatedReplicaSetHardwareAction =
                    refreshReplicaSetHardwareAction(
                        cluster,
                        rh,
                        shouldDeleteReplicaSetHardware,
                        pAutomationConfigContext,
                        pLogger);
                if (updatedReplicaSetHardwareAction == ReplicaSetHardware.Action.DELETE
                    && !clusterDescription.isDeleteRequested()) {
                  pClusterMaintenanceDecisionsBuilder.setWillDrainShard(rh.getRsId());
                }
              }
            });

    return maintenanceCheckResults;
  }

  public void addNewHardwareToShardAutoScalingContext(
      final ObjectId groupId, final Cluster goalCluster, final Logger pLogger) {
    final Optional<AutoScalingContext> scalingContext =
        _autoScalingContextDao.find(groupId, goalCluster.getClusterDescription().getName());

    if (scalingContext.isEmpty()) {
      pLogger
          .atLevel(Level.WARN)
          .addKeyValue("Cluster", goalCluster.getClusterDescription().getName())
          .addKeyValue("Group", goalCluster.getClusterDescription().getGroupId())
          .log("Cluster has no autoscaling context to convert");
      return;
    }

    final ShardComputeAutoScalingContext baseContext =
        (ShardComputeAutoScalingContext) scalingContext.get().getBaseComputeContext();
    final ShardComputeAutoScalingContext analyticsContext =
        (ShardComputeAutoScalingContext) scalingContext.get().getAnalyticsComputeContext();

    pLogger
        .atLevel(Level.DEBUG)
        .addKeyValue("Cluster", goalCluster.getClusterDescription().getName())
        .addKeyValue("Group", goalCluster.getClusterDescription().getGroupId())
        .log("Adding additional shards to cluster's ShardContext in AutoScalingContext");

    final AutoScalingContext newContext =
        scalingContext.get().toBuilder()
            .baseComputeContext(
                ComputeAutoScalingContextUtil.getUpdatedContextWithNewShards(
                    baseContext, goalCluster))
            .analyticsComputeContext(
                ComputeAutoScalingContextUtil.getUpdatedContextWithNewShards(
                    analyticsContext, goalCluster))
            .build();
    _autoScalingContextDao.save(newContext);
  }

  public void checkStuckPlanGeneration() {
    LOG.debug("The checkStuckPlanGeneration cron job is running.");

    final Map<ObjectId, List<ObjectId>> jobIdToGroupIdsForUnset = new HashMap<>();
    final Date fifteenMinutesAgo = DateUtils.addMinutes(new Date(), -15);
    final Map<String, Integer> jobStatusToStuckGroups = new HashMap<>();

    final List<NDSGroup> groups = _ndsGroupDao.getGroupsWithStuckPlanGeneration(fifteenMinutesAgo);
    groups.forEach(
        group -> {
          if (group.getPlanGenerationJobId().isEmpty()) {
            jobStatusToStuckGroups.merge(LABEL_NAME_JOB_ID_NOT_FOUND, 1, Integer::sum);
            LOG.error(
                "NDSGroup {} included in stuckPlanGeneration query, but has no planGenerationJobId"
                    + " set",
                group.getGroupId());
            return;
          }

          final ObjectId jobId = group.getPlanGenerationJobId().get();
          final Job job = _jobSvc.findJobById(jobId);

          if (job == null) {
            final Date oneMinuteAgo = DateUtils.addMinutes(new Date(), -1);
            // We allow cases where a job is just constructed but not inserted into DB yet. Such
            // cases are identified by checking if the jobId timestamp is less than one minute old
            if (jobId.getDate().after(oneMinuteAgo)) {
              return;
            }

            jobIdToGroupIdsForUnset
                .computeIfAbsent(jobId, k -> new ArrayList<>())
                .add(group.getGroupId());
            jobStatusToStuckGroups.merge(LABEL_NAME_JOB_NOT_FOUND, 1, Integer::sum);
            LOG.error(
                "Plan generation for NDSGroup {} with nextPlanningDate = {} and planGenerationJobId"
                    + " = {} is stuck. No job exists with planGenerationJobId, so it is unset for"
                    + " the group.",
                group.getGroupId(),
                group.getNextPlanningDate(),
                group.getPlanGenerationJobId());
          } else if (FINISHED_STATUSES.contains(job.getStatus())
              && job.getRetriesRemaining() == 0) {
            // Double check planGenerationJobId on completed job to avoid the race condition where
            // job completes and unsets planGenerationJobId on group right after this check begins
            final Optional<NDSGroup> refreshedGroup = _ndsGroupSvc.find(group.getGroupId());

            // if the current jobId on refreshed group is different from the previous retrieved
            // jobId OR there's no jobId set anymore, that means group has been picked up for skip
            // unsetting jobId
            if (refreshedGroup
                .flatMap(NDSGroup::getPlanGenerationJobId)
                .map(currentJobId -> !currentJobId.equals(jobId))
                .orElse(true)) {
              LOG.debug(
                  "Plan generation for NDSGroup {} with nextPlanningDate = {} and"
                      + " planGenerationJobId = {} is NOT stuck because jobId is updated to {}. Job"
                      + " finished with status {}",
                  group.getGroupId(),
                  group.getNextPlanningDate(),
                  group.getPlanGenerationJobId(),
                  refreshedGroup.flatMap(NDSGroup::getPlanGenerationJobId),
                  job.getStatus());
              return;
            }

            jobIdToGroupIdsForUnset
                .computeIfAbsent(jobId, k -> new ArrayList<>())
                .add(group.getGroupId());
            jobStatusToStuckGroups.merge(job.getStatus().name(), 1, Integer::sum);
            LOG.error(
                "Plan generation for NDSGroup {} with nextPlanningDate = {} and planGenerationJobId"
                    + " = {} is stuck. Job finished with status {}, and has no retries left, so"
                    + " planGenerationJobId is unset.",
                group.getGroupId(),
                group.getNextPlanningDate(),
                group.getPlanGenerationJobId(),
                job.getStatus());
          } else if (IN_PROGRESS_STATUSES.contains(job.getStatus())) {
            final Date scheduledForLookbackDate =
                DateUtils.addMinutes(
                    new Date(),
                    -1
                        * _appSettings.getIntProp(
                            JOB_QUEUE_IN_PROGRESS_JOB_SCHEDULED_FOR_LOOKBACK_MINUTES, 10));
            // We don't care about in progress jobs with PlanGenerationJobId that has
            // nextPlanningDate in the past (due to plan ASAP) unless it has been scheduled for a
            // while. Don't reset jobId for NEW jobs because it should get picked up.
            if (new Date(job.getScheduledFor()).before(scheduledForLookbackDate)) {
              jobStatusToStuckGroups.merge(job.getStatus().name(), 1, Integer::sum);
              LOG.error(
                  "Plan generation for NDSGroup {} with nextPlanningDate = {} and"
                      + " planGenerationJobId = {} is stuck. Job is in {} status and scheduledFor"
                      + " {}, not resetting planGenerationJobId, please check why this job is not"
                      + " picked up yet",
                  group.getGroupId(),
                  group.getNextPlanningDate(),
                  group.getPlanGenerationJobId(),
                  job.getStatus(),
                  job.getScheduledFor());
            }
          } else {
            jobStatusToStuckGroups.merge(job.getStatus().name(), 1, Integer::sum);
            LOG.error(
                "Plan generation for NDSGroup {} with nextPlanningDate = {} and planGenerationJobId"
                    + " = {} is stuck. Job has status {} with {} retries remaining, so"
                    + " planGenerationJobId is not unset.",
                group.getGroupId(),
                group.getNextPlanningDate(),
                group.getPlanGenerationJobId(),
                job.getStatus(),
                job.getRetriesRemaining());
          }
        });

    final AtomicInteger unsetCount = new AtomicInteger();
    if (!jobIdToGroupIdsForUnset.isEmpty()) {
      jobIdToGroupIdsForUnset.forEach(
          (jobId, groupIds) ->
              unsetCount.addAndGet(_ndsGroupSvc.unsetPlanGenerationJobIds(groupIds, jobId)));
    }

    LOG.info(
        "Successfully unset PlanGenerationJobId from {} groups to unblock planning",
        unsetCount.get());
    NDSPromMetricsSvc.incrementCounter(STUCK_PLAN_GENERATION_UNSET_TOTAL, unsetCount.get());

    jobStatusToStuckGroups.forEach(_jobStatusToStuckCountCache::put);
  }

  public void validateProjectInvariants(final Logger pLogger, final List<Cluster> pClusters) {
    for (Cluster cluster : pClusters) {
      try {
        final ClusterDescriptionValidationContext clusterDescriptionValidationContext =
            ClusterDescriptionValidationContext.builder()
                .clusterDescription(cluster.getClusterDescription())
                .appSettings(_appSettings)
                .build();
        _clusterDescriptionValidationUtil.validate(clusterDescriptionValidationContext);
      } catch (final Throwable pE) {
        pLogger
            .atLevel(Level.WARN)
            .addKeyValue("groupId", cluster.getClusterDescription().getGroupId())
            .addKeyValue("clusterName", cluster.getClusterDescription().getName())
            .setCause(pE)
            .log("Validation of cluster description failed during planning");
      }
    }
  }

  @VisibleForTesting
  ReplicaSetHardware.Action refreshReplicaSetHardwareAction(
      final Cluster pCluster,
      final ReplicaSetHardware pReplicaSetHardware,
      final boolean pShouldDeleteReplicaSetHardware,
      final AutomationConfigContext pAutomationConfigContext,
      final Logger pLogger) {
    final ReplicaSetHardware.Action replicaSetHardwareAction =
        getReplicaSetHardwareAction(
            pCluster,
            pReplicaSetHardware,
            pShouldDeleteReplicaSetHardware,
            pAutomationConfigContext);

    if (!Objects.equals(pReplicaSetHardware.getAction(), replicaSetHardwareAction)) {
      _hardwareSvc.updateAction(pReplicaSetHardware, replicaSetHardwareAction);
      pLogger.info(
          "Updated replica set action to {}. gid={} clusterName={} rsID={}",
          replicaSetHardwareAction,
          pCluster.getClusterDescription().getGroupId(),
          pCluster.getClusterDescription().getName(),
          pReplicaSetHardware.getRsId());
    }

    return replicaSetHardwareAction;
  }

  @VisibleForTesting
  static boolean shouldDeleteReplicaSetHardware(
      final ClusterDescription pClusterDescription,
      final ReplicaSetHardware pReplicaSetHardware,
      final boolean pIsTransitioningToEmbeddedConfig,
      final boolean pIsShardIndexOutOfBounds,
      final boolean pIsShardHardwareWithNoReplicationSpec,
      final Logger pLogger) {
    if (pClusterDescription.isDeleteRequested()) {
      pLogger.info(
          "Replica set hardware with rsId={} is no longer needed as the cluster {} is "
              + "being deleted, setting action to {}",
          pReplicaSetHardware.getRsId(),
          pClusterDescription.getName(),
          ReplicaSetHardware.Action.DELETE);
      return true;
    }

    // If this is a dedicated cluster that is currently unpausing, defer deletion until it
    // has finished un-pausing to avoid a situation where a replica set is marked for deletion
    // (i.e. due to a shard being deleted) and the shard draining must wait for the cluster to
    // unpause which will never happen because the SYNC_PAUSE_STATE action will be superseded by the
    // DELETE action.
    if (pClusterDescription.isDedicatedCluster() && !pClusterDescription.isPaused()) {
      for (final InstanceHardware instanceHardware :
          pReplicaSetHardware.getAllHardware().toList()) {
        if (instanceHardware.isPaused()) {
          pLogger.info(
              "Deferring deletion of Replica set hardware with rsId={} as the cluster {} has at"
                  + " least one instance which has not finished un-pausing",
              pReplicaSetHardware.getRsId(),
              pClusterDescription.getName());
          return false;
        }
      }
    }

    if (pClusterDescription.isAutomaticallyPaused()) {
      pLogger.info(
          "Replica set hardware with rsId={} is no longer needed as the cluster {} is "
              + "a tenant cluster that has been automatically paused, setting action to"
              + " {}",
          pReplicaSetHardware.getRsId(),
          pClusterDescription.getName(),
          ReplicaSetHardware.Action.DELETE);
      return true;
    }

    if (!pIsTransitioningToEmbeddedConfig && pIsShardIndexOutOfBounds) {
      // Don't delete if we are transitioning to embedded because we will have to
      // drain an additional shard after the transition is complete, so it is better to
      // wait for the config shard to be able to accept data before draining shards
      pLogger.info(
          "Replica set hardware with rsId={} is no longer needed as the shard isn't "
              + "needed, setting action to {}",
          pReplicaSetHardware.getRsId(),
          ReplicaSetHardware.Action.DELETE);
      return true;
    }

    if (pIsShardHardwareWithNoReplicationSpec) {
      pLogger.info(
          "Replica set hardware with rsId={} is no longer needed because the "
              + "replication spec {} has been removed, setting action to {}",
          pReplicaSetHardware.getRsId(),
          pReplicaSetHardware.getReplicationSpecId(),
          ReplicaSetHardware.Action.DELETE);
      return true;
    }

    return false;
  }

  @VisibleForTesting
  ReplicaSetHardware.Action getReplicaSetHardwareAction(
      final Cluster pCluster,
      final ReplicaSetHardware pReplicaSetHardware,
      final boolean pShouldDeleteReplicaSetHardware,
      final AutomationConfigContext pAutomationConfigContext) {
    if (pShouldDeleteReplicaSetHardware) {
      return ReplicaSetHardware.Action.DELETE;
    }

    if (!pReplicaSetHardware.containsConfigData()) {
      return ReplicaSetHardware.Action.NONE;
    }

    final ShardedClusterDescription shardedClusterDescription =
        (ShardedClusterDescription) pCluster.getClusterDescription();

    final Optional<ReplicationSpec> configServerReplicationSpecToSyncWith =
        shardedClusterDescription.getReplicationSpecById(
            pReplicaSetHardware.getReplicationSpecId());

    if (configServerReplicationSpecToSyncWith.isEmpty()) {
      // if we get a null ReplicationSpec, that means the zone was removed/changed,
      // currently this can only happen for dedicated config cluster.
      if (((ShardedClusterDescription) pCluster.getClusterDescription())
              .getConfigServerType()
              .isDedicated()
          && pReplicaSetHardware.isDedicatedConfig()) {
        return ReplicaSetHardware.Action.NONE;
      }
      throw new IllegalStateException(
          "Geo sharded cluster is trying to remove/change config zone and involves embedded"
              + " config.");
    }

    final boolean isSyncingWithDedicatedSpec =
        pCluster
            .getClusterDescription()
            .getDedicatedConfigServerReplicationSpec()
            .map(rs -> rs.getId().equals(configServerReplicationSpecToSyncWith.get().getId()))
            .orElse(false);

    if (isSyncingWithDedicatedSpec) {
      if (pReplicaSetHardware.isDedicatedConfig()) {
        // steady state for dedicated
        return ReplicaSetHardware.Action.NONE;
      }

      throw new IllegalStateException(
          "Syncing with a dedicated spec without dedicated config hardware is not an expected "
              + "state.");
    } else {
      if (pReplicaSetHardware.isConfigShard()) {
        if (shardedClusterDescription.getConfigServerType().isDedicated()) {
          final boolean allShardsProvisioned = pCluster.isAllHardwareProvisioned();
          if (!allShardsProvisioned) {
            return ReplicaSetHardware.Action.WAIT_FOR_MISSING_SHARDS_BEFORE_DRAIN_CONFIG_SHARD;
          }
          return ReplicaSetHardware.Action.SET_DEDICATED_CONFIG_ON_AGENT;
        }
        // steady state for embedded
        return ReplicaSetHardware.Action.NONE;
      } else {
        // transitioning to embedded

        final boolean pConfigHardwareHasActions =
            pReplicaSetHardware.getAllHardware().anyMatch(ih -> ih.getAction() != Action.NONE);
        final Optional<VersionUtils.Version> currentFCVVersion =
            pAutomationConfigContext
                .getAutomationConfigOrEmptyConfig()
                .getDeployment()
                .getMinimumFeatureCompatibilityVersionForCluster(
                    pCluster.getClusterDescription().getDeploymentClusterName());
        final boolean fcvSupportsEmbeddedConfig =
            currentFCVVersion
                .map(
                    fcv ->
                        _appSettings
                            .getEmbeddedConfigMinimumMongoDBVersion()
                            .isLessThanOrEqualTo(fcv))
                .orElse(true);
        if (pConfigHardwareHasActions || !fcvSupportsEmbeddedConfig) {
          // wait for config hardware to resize or update mdb version before transitioning
          return ReplicaSetHardware.Action.RECONFIGURE_CONFIG_SHARD;
        }

        return ReplicaSetHardware.Action.SET_CONFIG_SHARD_ON_AGENT;
      }
    }
  }

  @VisibleForTesting
  List<MaintenanceCheckResult> refreshReplicaSetInstanceHardwareAction(
      final ClusterDescription pClusterDescription,
      final Optional<ReplicationSpec> pReplicationSpec,
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final boolean pShouldDeleteReplicaSetHardware,
      final ReplicaSetHardware pHardware,
      final Optional<RollingResync> pRollingResync,
      final List<HostCluster> pHostClusters,
      final Long lastMonitoringAgentPingTime,
      final Logger pLogger) {
    final boolean isReplicaSetMemberWithData =
        isReplicaSetMemberWithData(pHardware, pHostClusters, lastMonitoringAgentPingTime);

    final List<CloudProviderContainer> containers =
        pClusterDescription.getCloudProviders().stream()
            .flatMap(
                cloudProvider -> pNDSGroup.getCloudProviderContainersByType(cloudProvider).stream())
            .collect(Collectors.toList());
    // Paused M0's will not have a container
    if (containers.isEmpty() && !pClusterDescription.isAutomaticallyPaused()) {
      throw new IllegalStateException("Expected to find cloud provider container for group");
    }

    final List<InstanceHardware> allInstanceHardwareList = pHardware.getAllHardware().toList();
    final Map<ObjectId, RegionName> instanceIdToRegion;
    final Set<ObjectId> hardwareNeedingMigration;

    if (pReplicationSpec.isEmpty()) {
      instanceIdToRegion = new HashMap<>();
      hardwareNeedingMigration = Collections.emptySet();
    } else {
      final List<RegionConfig> regionsConfig = pReplicationSpec.get().getRegionConfigs();

      instanceIdToRegion = pHardware.getInstanceIdToRegionNameMap(regionsConfig);
      hardwareNeedingMigration =
          _clusterSvc.checkHardwareNeedsContainerZoneMigration(
              pHardware,
              allInstanceHardwareList,
              instanceIdToRegion,
              pClusterDescription,
              pNDSGroup,
              pGroup);
    }

    final List<MaintenanceCheckResult> maintenanceCheckResults = new ArrayList<>();

    // Get actions for instances linked to each container.
    final List<InstanceHardware> instanceHardwareList = pHardware.getHardware();
    for (int instanceIdx = 0; instanceIdx < instanceHardwareList.size(); instanceIdx++) {
      final InstanceHardware instance = instanceHardwareList.get(instanceIdx);
      final Optional<SyncSourceStrategy> resyncStrategy =
          pRollingResync.flatMap(
              rs -> instance.getHostnameForAgents().flatMap(rs::getNodeRunningStrategy));
      maintenanceCheckResults.add(
          refreshInstanceHardwareAction(
              instance,
              instanceIdx,
              pShouldDeleteReplicaSetHardware,
              pHardware,
              pReplicationSpec,
              pClusterDescription,
              pNDSGroup,
              pGroup,
              instanceIdToRegion,
              isReplicaSetMemberWithData,
              pLogger,
              hardwareNeedingMigration.contains(instance.getInstanceId()),
              resyncStrategy));
    }
    final List<InstanceHardware> internalInstanceHardwareList = pHardware.getInternalHardware();
    for (int instanceIdx = 0; instanceIdx < internalInstanceHardwareList.size(); instanceIdx++) {
      final InstanceHardware instance = internalInstanceHardwareList.get(instanceIdx);
      final Optional<SyncSourceStrategy> resyncStrategy =
          pRollingResync.flatMap(
              rs -> instance.getHostnameForAgents().flatMap(rs::getNodeRunningStrategy));
      maintenanceCheckResults.add(
          refreshInstanceHardwareAction(
              instance,
              instanceIdx,
              pShouldDeleteReplicaSetHardware,
              pHardware,
              pReplicationSpec,
              pClusterDescription,
              pNDSGroup,
              pGroup,
              instanceIdToRegion,
              isReplicaSetMemberWithData,
              pLogger,
              hardwareNeedingMigration.contains(instance.getInstanceId()),
              resyncStrategy));
    }

    return maintenanceCheckResults;
  }

  @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
  static boolean isShardIndexOutOfBounds(
      final int pIndex,
      final ClusterDescription pClusterDescription,
      final Optional<ReplicationSpec> pReplicationSpec) {
    return pReplicationSpec
        .map(rs -> pIndex >= getDesiredNumberOfNonConfigShards(rs, pClusterDescription))
        .orElse(false);
  }

  protected MaintenanceCheckResult refreshInstanceHardwareAction(
      final InstanceHardware pInstance,
      final int pInstanceIndex,
      final boolean pShouldDeleteReplicaSetHardware,
      final ReplicaSetHardware pReplicaSetHardware,
      final Optional<ReplicationSpec> pReplicationSpec,
      final ClusterDescription pClusterDescription,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Map<ObjectId, RegionName> pInstanceIdToRegion,
      final boolean pIsReplicaSetMemberWithData,
      final Logger pLogger,
      final boolean pHardwareNeedsMigration,
      final Optional<SyncSourceStrategy> pResyncStrategy) {
    final boolean isInternalInstance =
        pReplicaSetHardware.isInstanceInternal(pInstance.getInstanceId());

    final ActionContext instanceAction =
        getActionForInstance(
            pInstance,
            pInstanceIndex,
            pShouldDeleteReplicaSetHardware,
            pReplicaSetHardware,
            pReplicationSpec,
            pClusterDescription,
            pNDSGroup,
            pGroup,
            pInstanceIdToRegion,
            pResyncStrategy,
            pHardwareNeedsMigration,
            pIsReplicaSetMemberWithData,
            isInternalInstance,
            pLogger);

    final Pair<ActionContext, MaintenanceCheckResult> actionMaintenanceCheckStatePair =
        ensureCloudProviderMaintenanceFitsMaintenanceWindow(
            instanceAction, pNDSGroup, pGroup, pInstance);
    final MaintenanceCheckResult maintenanceCheckResult =
        actionMaintenanceCheckStatePair.getRight();
    final MaintenanceCheckResult actionMaintenanceCheckResultPair_mcsWithEventAndCluster =
        new MaintenanceCheckResult(
            maintenanceCheckResult.maintenanceType(),
            pClusterDescription,
            maintenanceCheckResult.needMaintenance(),
            // This is driven by the health-check system. If it is critical, it will go into
            // heal repair immediately. If it is not critical, it will be included in the next
            // maintenance window
            new MaintenanceRelease(
                pNDSGroup.getGroupId(), null, maintenanceCheckResult.getSchedulingBehavior()),
            maintenanceCheckResult.getInstanceHardwareDetails());

    final ActionContext finalActionToTake;
    final List<Pair<String, Object>> instanceHardwareFieldsToUpdate = new ArrayList<>();
    if (!pInstance.isNVMe()) {
      final Pair<ActionContext, List<Pair<String, Object>>>
          optimizedActionAndAdditionalInstanceFieldsToUpdate =
              optimizeHealingAction(
                  actionMaintenanceCheckStatePair.getLeft(),
                  pInstance,
                  pNDSGroup,
                  pReplicaSetHardware,
                  pClusterDescription,
                  pResyncStrategy,
                  pLogger);
      finalActionToTake = optimizedActionAndAdditionalInstanceFieldsToUpdate.getLeft();
      instanceHardwareFieldsToUpdate.addAll(
          optimizedActionAndAdditionalInstanceFieldsToUpdate.getRight());
    } else {
      finalActionToTake = actionMaintenanceCheckStatePair.getLeft();
    }

    if (finalActionToTake.action() == pInstance.getAction()) {
      return actionMaintenanceCheckResultPair_mcsWithEventAndCluster;
    }

    final Instant actionTransitionInstant = getInstantNow();
    final Date actionTransitionDate = Date.from(actionTransitionInstant);
    if (pInstance.getAction() != null && pInstance.getActionLastModifiedDate().isPresent()) {
      emitPrometheusEvent(
          actionTransitionDate,
          pInstance,
          finalActionToTake.action(),
          actionMaintenanceCheckStatePair.getLeft().action(),
          pLogger);
    }

    instanceHardwareFieldsToUpdate.add(
        Pair.of(InstanceHardware.FieldDefs.ACTION_LAST_MODIFIED_DATE, actionTransitionDate));
    instanceHardwareFieldsToUpdate.add(
        Pair.of(InstanceHardware.FieldDefs.ACTION, finalActionToTake.action().name()));

    // updating all instance hardware fields in one write operation,
    // without modifying lastUpdateDate, since auto-healing is an 'internal' operation
    _hardwareDao.setInstanceFields(
        pReplicaSetHardware.getId(),
        pInstance.getInstanceId(),
        isInternalInstance,
        instanceHardwareFieldsToUpdate,
        false);

    pLogger.info(
        "Updated action from {} to {} for instance {}.",
        pInstance.getAction().name(),
        finalActionToTake.action().name(),
        pInstance.getInstanceId());

    if (InstanceHardware.Action.ALL_HEAL_STATES.contains(finalActionToTake.action())
        && !finalActionToTake.action().equals(Action.HEAL_WAIT)) {
      saveFinalAutoHealingAuditEvent(
          pClusterDescription.getGroupId(),
          pClusterDescription.getName(),
          pInstance,
          finalActionToTake,
          actionTransitionInstant,
          false);

      // save internal audit event for auto-healing with more detailed reasons
      saveFinalAutoHealingAuditEvent(
          pClusterDescription.getGroupId(),
          pClusterDescription.getName(),
          pInstance,
          finalActionToTake,
          actionTransitionInstant,
          true);
    }

    Set<Action> HEAL_NON_ACTIONABLE_ACTIONS =
        Set.of(Action.HEAL_CANNOT_REPAIR, Action.HEAL_CANNOT_RESYNC);
    if (HEAL_NON_ACTIONABLE_ACTIONS.contains(pInstance.getAction())
        && Action.NONE == finalActionToTake.action()) {
      saveAutoHealingFixedInternalAuditEvent(
          pClusterDescription.getGroupId(), pClusterDescription.getName());
    }

    if (InstanceHardware.Action.ALL_HEAL_STATES.contains(finalActionToTake.action())
        && finalActionToTake.action().equals(Action.HEAL_WAIT)) {
      // If we're entering HEAL_WAIT, we want to temporarily enable increased frequency FTDC pushes
      // to proactively offload the FTDC logs from the node
      _ndsGroupSvc.enableIncreasedFrequencyFTDCPushes(
          pNDSGroup.getGroupId(), pClusterDescription.getName(), AuditInfoHelpers.fromSystem());
    }

    if (pInstance.getHostnameForAgents().isPresent()) {
      Arrays.stream(pClusterDescription.getMongoDBUriHosts())
          .filter(
              hostPort ->
                  HostUtils.extractHostname(hostPort)
                      .equals(pInstance.getHostnameForAgents().get()))
          .findFirst()
          .ifPresent(
              hostPort ->
                  _ndsEventDao.updateTargetStatus(
                      new ClusterDescriptionId(pClusterDescription),
                      hostPort,
                      finalActionToTake.action()));
    }

    return actionMaintenanceCheckResultPair_mcsWithEventAndCluster;
  }

  protected void saveAutoHealingFixedInternalAuditEvent(
      final ObjectId pGroupId, final String pClusterName) {
    final NDSAudit.Builder builder = new Builder(AUTO_HEALING_FIXED_INTERNAL);
    builder.groupId(pGroupId);
    builder.hidden(true);
    builder.auditInfo(AuditInfoHelpers.fromSystem());
    builder.clusterName(pClusterName);
    builder.clusterId(getClusterId(pGroupId, pClusterName));
    _auditSvc.saveAuditEvent(builder.build());
    _alertSvc.enqueueEvent(builder.build());
  }

  // needed for testing

  protected Instant getInstantNow() {
    return Instant.now();
  }

  protected void emitPrometheusEvent(
      final Date pNow,
      final InstanceHardware pInstance,
      final Action pActionToTake,
      final Action pActionBeforeOptimized,
      final Logger pLogger) {
    final double timeSpentInExistingAction =
        (pNow.getTime() - pInstance.getActionLastModifiedDate().get().getTime()) / 1000.0;
    PromMetricsSvc.recordTimer(
        INSTANCE_HARDWARE_ACTION_DURATION_HISTOGRAM,
        timeSpentInExistingAction,
        pInstance.getAction().toString(),
        pActionToTake.toString(),
        String.valueOf(pActionBeforeOptimized != pActionToTake));

    pLogger.debug(
        "Instance hardware {} spent {} seconds in action {}, starting at {}."
            + " Setting new action to {}, effective {}.",
        pInstance.getInstanceId(),
        timeSpentInExistingAction,
        pInstance.getAction().name(),
        pInstance.getActionLastModifiedDate().get(),
        pActionToTake.name(),
        pNow);
  }

  protected Pair<ActionContext, List<Pair<String, Object>>> optimizeHealingAction(
      final ActionContext pActionContextSoFar,
      final InstanceHardware pInstance,
      final NDSGroup pNDSGroup,
      final ReplicaSetHardware pReplicaSetHardware,
      final ClusterDescription pClusterDescription,
      final Optional<SyncSourceStrategy> pResyncStrategy,
      final Logger pLogger) {
    ActionContext actionToTake = pActionContextSoFar;
    final List<Pair<String, Object>> instanceHardwareFieldsToUpdate = new ArrayList<>();

    // prefer immediate power cycle to host replacement, if possible
    if (!pInstance.needsForceReplacement()
        && actionToTake.action().equals(Action.HEAL_REPAIR)
        && pResyncStrategy.isEmpty()
        && !pInstance.powerCycleAttempted()) {
      actionToTake =
          new ActionContext(
              Action.HEAL_POWER_CYCLE_CRITICAL, AutoHealMessage.POWER_CYCLE_OPTIMIZATION);
      pLogger.info(
          "Optimizing action from HEAL_REPAIR to HEAL_POWER_CYCLE_CRITICAL for instance {}.",
          pInstance.getInstanceId());

      instanceHardwareFieldsToUpdate.add(
          Pair.of(InstanceHardware.FieldDefs.POWER_CYCLE_ATTEMPTED, true));
      pLogger.debug("powerCycleAttempted set to true for instance {}.", pInstance.getInstanceId());
    }

    // reset power cycle attempted when healing is complete
    if (actionToTake.action().equals(Action.NONE)
        && pInstance.powerCycleAttempted()
        && Action.ALL_HEAL_STATES.contains(pInstance.getAction())) {
      instanceHardwareFieldsToUpdate.add(
          Pair.of(InstanceHardware.FieldDefs.POWER_CYCLE_ATTEMPTED, false));
      pLogger.debug("powerCycleAttempted set to false for instance {}.", pInstance.getInstanceId());
    }

    // prefer optimized initial sync to resync, where available.
    // Forced server resyncs should skip this optimization
    if (actionToTake.action().equals(Action.HEAL_RESYNC) && !pInstance.needsForceServerResync()) {

      boolean isOISAvailable =
          validateOISAvailable(
              pNDSGroup, pInstance, pClusterDescription, pReplicaSetHardware, pResyncStrategy);

      if (isOISAvailable && pResyncStrategy.isEmpty()) {
        pLogger.info(
            "Optimizing action from HEAL_RESYNC to HEAL_REPAIR for instance {}."
                + " Performing optimized initial sync.",
            pInstance.getInstanceId());
        actionToTake =
            new ActionContext(Action.HEAL_REPAIR, AutoHealMessage.OPTIMIZED_HEALING_ACTION);
      }
    }
    return Pair.of(actionToTake, instanceHardwareFieldsToUpdate);
  }

  protected boolean validateOISAvailable(
      final NDSGroup pNDSGroup,
      final InstanceHardware pInstance,
      final ClusterDescription pClusterDescription,
      final ReplicaSetHardware pReplicaSetHardware,
      final Optional<SyncSourceStrategy> pResyncStrategy) {
    final List<Host> hosts =
        _hostSvc.findHealthyDataBearingHostsByGroupIdWithPrimariesSortedFirst(
            pNDSGroup.getGroupId(), pReplicaSetHardware.getRsId(), FRESH_PING_DURATION);

    return snapshotSourceExistsForInstance(
        pNDSGroup, pInstance, pClusterDescription, pReplicaSetHardware, hosts, pResyncStrategy);
  }

  protected Pair<ActionContext, MaintenanceCheckResult>
      ensureCloudProviderMaintenanceFitsMaintenanceWindow(
          final ActionContext pActionContext,
          final NDSGroup pNDSGroup,
          final Group pGroup,
          final InstanceHardware pInstance) {
    final MaintenanceCheckResult blankCheckState =
        new MaintenanceCheckResult(
            MaintenanceType.CLOUD_PROVIDER_SCHEDULED_MAINTENANCE_PERFORMED,
            false,
            MAINTENANCE_WINDOW);

    if (pActionContext.action() == Action.HEAL_POWER_CYCLE) {
      final Calendar actionDate = Calendar.getInstance();

      final Optional<Date> scheduledShutDownDate = pInstance.getScheduledShutDownDate();
      if (scheduledShutDownDate.isEmpty()) {
        throw new IllegalStateException("Expected cloud provider event schedule date to be set");
      }

      actionDate.setTime(scheduledShutDownDate.get());

      final Calendar nextMaintenanceWindowDate =
          _ndsGroupMaintenanceSvc.getNextMaintenanceStartDateTime(
              pNDSGroup, pGroup, SchedulingBehavior.DURING_MAINTENANCE_WINDOW);
      final Calendar secondMaintenanceWindowDate =
          _ndsGroupMaintenanceSvc.getNextMaintenanceStartDateTime(
              pNDSGroup, pGroup, SchedulingBehavior.DURING_MAINTENANCE_WINDOW);
      secondMaintenanceWindowDate.add(Calendar.DATE, 7);

      // We can fit the cloud provider maintenance request into the maintenance window if it's
      // scheduled on / after the expected maintenance start date and if we have enough time to send
      // a notification to the customers or if a notification has already been sent
      final boolean canFitIntoNextMaintenanceWindow =
          !actionDate.before(nextMaintenanceWindowDate)
              && (_maintenanceDateCalculationUtil.hasTimeToSendNotification(pNDSGroup, pGroup)
                  || pNDSGroup.getMaintenanceWindow().getAdvanceNotificationSendDate().isPresent());

      final boolean canFitIntoSecondMaintenanceWindow =
          !actionDate.before(secondMaintenanceWindowDate);

      // The maintenance is considered critical if we can't fit it into the next 2 maintenance
      // period
      final boolean isCritical =
          !(canFitIntoNextMaintenanceWindow || canFitIntoSecondMaintenanceWindow);
      final boolean isSystemDefined = !pNDSGroup.getMaintenanceWindow().isUserDefined();

      final MaintenanceCheckResult needsMaintenanceCheckResult =
          new MaintenanceCheckResult(
              MaintenanceType.CLOUD_PROVIDER_SCHEDULED_MAINTENANCE_PERFORMED,
              null,
              true,
              // A maintenance window exists for this action. Accordingly: if it is critical, it
              // will be planned immediately, if not, it goes into the maintenance window
              new MaintenanceRelease(
                  pNDSGroup.getGroupId(),
                  null,
                  isCritical
                      ? SchedulingBehavior.CRITICAL
                      : blankCheckState.getSchedulingBehavior()),
              getInstanceHardwareDetails(pInstance));

      final boolean isInShouldStartMaintenanceWindow =
          _ndsGroupMaintenanceSvc.shouldStartMaintenanceForGroup(
              pNDSGroup,
              pGroup,
              SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
              MaintenanceType.CLOUD_PROVIDER_SCHEDULED_MAINTENANCE_PERFORMED.name(),
              MaintenanceHistoryMatchCriteria.fromCheckResult(needsMaintenanceCheckResult),
              LOG);

      final boolean canStartMaintenance =
          isCritical || isSystemDefined || isInShouldStartMaintenanceWindow;

      if (canStartMaintenance) {
        _ndsGroupMaintenanceSvc.setStartedDateOnPendingMaintenance(
            pGroup.getId(), needsMaintenanceCheckResult, LOG);
      }

      // Only wait for the maintenance window if it is user defined and the repair isn't
      // deemed critical
      final ActionContext desiredActionContext =
          canStartMaintenance ? pActionContext : new ActionContext(Action.NONE);

      return Pair.of(desiredActionContext, needsMaintenanceCheckResult);
    }

    return Pair.of(pActionContext, blankCheckState);
  }

  private InstanceHardwareDetails getInstanceHardwareDetails(final InstanceHardware pInstance) {
    return new InstanceHardwareDetails(
        pInstance.getHostnames().getPublicHostname().orElse(null),
        pInstance.getRebootRequestedDate().orElse(null),
        Collections.emptyList(),
        null,
        null);
  }

  private static final Duration IDLE_CONTAINER_EXPIRATION = Duration.ofDays(60);

  void refreshExpiredContainerPeers(
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final List<Cluster> pClusters,
      final Logger pLogger) {

    final List<CloudProviderContainer> containers = pNDSGroup.getCloudProviderContainers();
    for (final CloudProviderContainer container : containers) {
      if (container.getContainerPeers().size() == 0) {
        continue;
      }

      if (!container.isProvisioned()) {
        container.getContainerPeers().stream()
            .filter(ContainerPeer::isDeleteRequested)
            .forEach(
                p ->
                    _containerPeerDao.deleteContainerPeer(
                        pNDSGroup.getGroupId(), container.getId(), p.getId()));
      }

      final List<ClusterDescription> clusters =
          pClusters.stream().map(Cluster::getClusterDescription).collect(Collectors.toList());

      if (clusters.stream().anyMatch(c -> !c.getState().equals(ClusterDescription.State.DELETED))) {
        pLogger.debug(
            "There are live clusters in container {}, skipping check for expired peers",
            container.getId());
        continue;
      }

      final Date lastClusterUpdate =
          clusters.stream()
              .map(ClusterDescription::getLastUpdateDate)
              .max(Date::compareTo)
              .orElse(new Date(0));
      final Date idleDate =
          new Date(System.currentTimeMillis() - IDLE_CONTAINER_EXPIRATION.toMillis());
      if (lastClusterUpdate.after(idleDate) || container.getLastUpdate().after(idleDate)) {
        pLogger.debug(
            "The container {} has not been idle long enough to clean up peers", container.getId());
        continue;
      }

      final List<ContainerPeer> peersNeedDeleting =
          container.getContainerPeers().stream()
              .filter(p -> !p.isDeleteRequested())
              .collect(Collectors.toList());
      for (final ContainerPeer p : peersNeedDeleting) {
        pLogger.info(
            "Marking peer {} in container {} as delete requested", p.getId(), container.getId());
        try {
          _containerPeerSvc.requestDeleteContainerPeer(
              pGroup, p.getId(), AuditInfoHelpers.fromSystem());
        } catch (final SvcException pE) {
          throw new UncheckedSvcException(pE);
        }
      }
    }
  }

  private boolean isReplicaSetMemberWithData(
      final ReplicaSetHardware pReplicaSetHardware,
      final List<HostCluster> pHostClusters,
      final Long lastMonitoringAgentPingTime) {
    final String replSetName = pReplicaSetHardware.getRsId();
    final Date now = new Date();

    final boolean hasRecentPrimaryOrSecondary =
        pHostClusters.stream()
            .filter(HostCluster::isReplicaSet)
            .filter(c -> c.getHosts() != null)
            .flatMap(c -> c.getHosts().stream())
            .filter(h -> h.getReplicaSetId() != null && h.getReplicaSetId().equals(replSetName))
            .filter(
                h ->
                    h.getLastPing() != null
                        && h.getLastPing()
                            .after(
                                new Date(
                                    now.getTime() - MONITORING_RECENT_PING_THRESHOLD.toMillis())))
            .anyMatch(h -> h.getIsPrimary() || h.getIsSecondary());

    final boolean hasRecentMonitoringAgentPing =
        lastMonitoringAgentPingTime != null
            && new Date(lastMonitoringAgentPingTime)
                .after(new Date(now.getTime() - MONITORING_RECENT_PING_THRESHOLD.toMillis()));

    return hasRecentPrimaryOrSecondary && hasRecentMonitoringAgentPing;
  }

  ActionContext getActionForInstance(
      final InstanceHardware pInstanceHardware,
      final int pInstanceIdx,
      final boolean pShouldDeleteReplicaSetHardware,
      final ReplicaSetHardware pReplicaSetHardware,
      final Optional<ReplicationSpec> pReplicationSpecOpt,
      final ClusterDescription pClusterDescription,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final Map<ObjectId, RegionName> pInstanceIdToRegionMap,
      final Optional<SyncSourceStrategy> pResyncStrategy,
      final boolean pNeedsIntraContainerMigration,
      final boolean isReplicaSetMemberWithData,
      final boolean pIsInternalInstance,
      final Logger pLogger) {
    final String memberName =
        pReplicaSetHardware.getReplicaSetMemberName(
            pClusterDescription.getDeploymentClusterName(),
            pInstanceHardware.getMemberIndex(),
            pIsInternalInstance);

    final boolean instanceIndexOutOfBound =
        pReplicationSpecOpt
            .map(
                pReplicationSpec ->
                    pInstanceIdx
                        >= (pIsInternalInstance
                            ? pReplicationSpec.getTotalHiddenSecondaryNodes()
                            : pReplicationSpec.getTotalVisibleNodes()))
            .orElse(false);

    if (!pInstanceHardware.isProvisioned()) {
      if (instanceIndexOutOfBound || pShouldDeleteReplicaSetHardware) {
        pLogger.debug(
            "Instance {} (instanceId={}) in {} is not provisioned and is not needed; no action"
                + " needed.",
            memberName,
            pInstanceHardware.getInstanceId(),
            pClusterDescription.getName());
        return new ActionContext(Action.NONE);
      }
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs to be provisioned. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.CREATE.name());
      return new ActionContext(Action.CREATE);
    }

    if (instanceIndexOutOfBound) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} no longer needed for its replica set. Setting action"
              + " to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.DELETE.name());
      return new ActionContext(Action.DELETE);
    }

    if (pShouldDeleteReplicaSetHardware) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} no longer needed as the replica set hardware is no"
              + " longer needed. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.DELETE.name());
      return new ActionContext(Action.DELETE);
    }

    if (pInstanceHardware.needsForceReplacement()) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs forced replacement. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.HEAL_REPAIR.name());
      return new ActionContext(Action.HEAL_REPAIR);
    }

    if (!pInstanceHardware.isPaused() == pClusterDescription.isPaused()) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} is being {}. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          pClusterDescription.isPaused() ? "paused" : "resumed",
          Action.SYNC_PAUSE_STATE.name());
      return new ActionContext(Action.SYNC_PAUSE_STATE);
    } else if (pClusterDescription.isPaused()) {
      pLogger.debug(
          "Instance {} (instanceId={}) in {} is paused; no action can be taken.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName());
      return new ActionContext(Action.NONE);
    }
    if (pInstanceHardware.getCloudProvider().equals(CloudProvider.AWS)) {
      AWSInstanceHardware awsInstanceHardware = (AWSInstanceHardware) pInstanceHardware;
      if (pClusterDescription.getReserveIpamIpRequested()
          && !awsInstanceHardware.getIsUsingIpamIp()
          && awsInstanceHardware.getFutureEipId().isEmpty()) {
        return new ActionContext(Action.RESERVE_IPAM_IP);
      }
    }

    final NodeType nodeType =
        pReplicaSetHardware
            .getNodeTypeForInstance(pClusterDescription, pInstanceHardware.getInstanceId())
            .orElse(NodeType.ELECTABLE);

    // if it's non-NVMe to NVMe upgrade, RECONFIGURE is prioritized
    // given node replacement is needed anyway.
    // Also, prioritize RECONFIGURE over OS_SWAP when the instance needs migration, as that
    // will likely make the OS_SWAP redundant
    if (!isNonNVMeToNVMeUpdate(pInstanceHardware, pClusterDescription, nodeType)
        && !pNeedsIntraContainerMigration
        && instanceNeedsCpuArchitectureSwap(
            pNDSGroup, pInstanceHardware, pClusterDescription, pReplicaSetHardware)
        && !useReconfigureForCpuArchitectureSwap(pGroup)) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs OS Swap. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.OS_SWAP.name());
      return new ActionContext(Action.OS_SWAP);
    }

    if (pInstanceHardware.needsForceServerRestart()
        && !pInstanceHardware.getNextRestartOrRebootRequestedDate().isPresent()) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs force server restart. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.HEAL_RESTART.name());
      return new ActionContext(Action.HEAL_RESTART);
    }
    if (pInstanceHardware.needsForceServerResync()
        && pInstanceHardware.getNextRestartOrRebootRequestedDate().isEmpty()) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs force resync. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.HEAL_RESYNC.name());
      return new ActionContext(Action.HEAL_RESYNC);
    }

    // Check if RECONFIGURE should be prioritized for disk size increase during shard draining
    final ActionContext diskSizeReconfigureAction =
        checkForDiskSizeReconfigureDuringShardDraining(
            pInstanceHardware, pReplicaSetHardware, pClusterDescription, memberName, pLogger);
    if (diskSizeReconfigureAction.action() != Action.NONE) {
      return diskSizeReconfigureAction;
    }

    // The chef config update action should not be performed on tenant providers and only
    // be performed if the hardware is provisioned.
    PushBasedLogExport pushBasedLogExport = pNDSGroup.getPushBasedLogExport();
    if (!pInstanceHardware.getCloudProvider().isTenantProvider()
        && pInstanceHardware.isProvisioned()) {

      // agent key rotation will update the firstboot.json / cloud config as well, so should take
      // precedence.
      if (pInstanceHardware.doesAgentAPIKeyNeedUpdate()) {
        pLogger.info(
            "Instance {} (instanceId={}) in {} needs agent API key rotation. Setting action to {}.",
            memberName,
            pInstanceHardware.getInstanceId(),
            pClusterDescription.getName(),
            Action.ROTATE_AGENT_API_KEY.name());
        return new ActionContext(Action.ROTATE_AGENT_API_KEY);
      }

      // Check for gateway router eligibility changes
      if (pInstanceHardware.isBeforeGatewayRouterLastUpdateDate(
          pClusterDescription.getGatewayRouterEligibilityLastUpdateDate())) {
        pLogger.info(
            "Instance {} (instanceId={}) in {} needs config update due to gateway router"
                + " eligibility change. Setting action to {}.",
            memberName,
            pInstanceHardware.getInstanceId(),
            pClusterDescription.getName(),
            Action.UPDATE_CONFIG.name());
        return new ActionContext(Action.UPDATE_CONFIG);
      }

      // Check for a more recent enabled mongot update date, indicating a feature flag change.
      if (pInstanceHardware.isBeforeEnabledMongotLastUpdateDate(
          pushBasedLogExport.getEnabledMongotLastUpdateDate())) {
        pLogger.info(
            "Instance {} (instanceId={}) in {} needs config update due to search feature flag"
                + " change. Setting action to {}.",
            memberName,
            pInstanceHardware.getInstanceId(),
            pClusterDescription.getName(),
            Action.UPDATE_CONFIG.name());
        return new ActionContext(Action.UPDATE_CONFIG);
      }

      if (PlanningUtil.shouldUpdateInstanceConfig(pNDSGroup, pInstanceHardware)) {
        pLogger.info(
            "Instance {} (instanceId={}) in {} needs config update. Setting action to {}.",
            memberName,
            pInstanceHardware.getInstanceId(),
            pClusterDescription.getName(),
            Action.UPDATE_CONFIG.name());
        return new ActionContext(Action.UPDATE_CONFIG);
      }
    }

    if (pInstanceHardware.needsForceStopStartVM()
        && pInstanceHardware.isProvisioned()
        && !pInstanceHardware.isNVMe()) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs force stop start VM. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.STOP_START_VM.name());
      return new ActionContext(Action.STOP_START_VM);
    }

    // disk compaction re-sync should take priority over re-config in case the re-config is
    // shrinking disk; in which case, re-syncing might be necessary to reclaim disk space first.
    // Re-syncing shouldhave lower priority than deletion, which removes the necessity to re-sync.
    if (pInstanceHardware.getNeedsDiskCompactionResyncDate().isPresent()
        && !pInstanceHardware.getNeedsDiskCompactionResyncDate().get().after(new Date())) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs disk compaction re-sync. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.DISK_COMPACTION_RESYNC.name());
      return new ActionContext(Action.DISK_COMPACTION_RESYNC);
    }

    final RegionName desiredRegion = pInstanceIdToRegionMap.get(pInstanceHardware.getInstanceId());

    if (pReplicationSpecOpt.isPresent()
        && (!pClusterDescription
                .getRegionName(
                    pInstanceHardware,
                    pNDSGroup.getCloudProviderContainer(pInstanceHardware.getCloudContainerId()))
                .equals(desiredRegion)
            || pInstanceHardware.needsHostnameChange()
            || !doesHardwareSpecMatchInstanceHardware(
                pReplicationSpecOpt.get(),
                pClusterDescription,
                pReplicaSetHardware,
                pInstanceHardware,
                nodeType))) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} no longer matches the desired hardware. Setting action"
              + " to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.RECONFIGURE.name());
      return new ActionContext(Action.RECONFIGURE);
    }

    if (pResyncStrategy.isPresent()) {
      final Strategy strategy = pResyncStrategy.get().getStrategy();
      pLogger.info(
          "Instance {} (instanceId={}) in {} has resync strategy {}",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          strategy);
      switch (strategy) {
        case OIS:
          return new ActionContext(Action.HEAL_REPAIR);
        case LOGICAL:
        case FILECOPY:
          return new ActionContext(Action.HEAL_RESYNC);
        default:
          throw new UnsupportedOperationException(
              String.format("Don't know how to handle strategy %s", strategy));
      }
    }

    final ActionContext healAction =
        shouldHealInstance(
            pInstanceHardware,
            pNDSGroup,
            pGroup,
            pClusterDescription.getClusterDescriptionId(),
            pNeedsIntraContainerMigration,
            isReplicaSetMemberWithData,
            pLogger,
            pReplicaSetHardware,
            pClusterDescription);
    final Set<Action> nonBlockingActions = Set.of(Action.NONE, Action.HEAL_WAIT);
    final boolean isEncryptionAtRestEnabledAndInvalid =
        isEncryptionAtRestEnabledAndInvalid(pNDSGroup, pClusterDescription);
    final boolean clusterInRegionalOutageSimulation =
        pClusterDescription.getClusterTags().contains(ClusterTag.REGION_OUTAGE_SIMULATION);
    final boolean plannerCanHeal =
        !isEncryptionAtRestEnabledAndInvalid && !clusterInRegionalOutageSimulation;

    if (!nonBlockingActions.contains(healAction.action())) {
      if (plannerCanHeal) {
        pLogger.info(
            "Instance {} (instanceId={}) in {} needs healing. Setting action to {}.",
            memberName,
            pInstanceHardware.getInstanceId(),
            pClusterDescription.getName(),
            healAction.action().name());
        return healAction;
      } else if (clusterInRegionalOutageSimulation) {
        pLogger.info(
            "Instance {} (instanceId={}) in {} would require healing action {} but regional outage"
                + " simulation in progress. Skipping this choice.",
            memberName,
            pInstanceHardware.getInstanceId(),
            pClusterDescription.getName(),
            healAction.action().name());
      }
    }

    if (instanceNeedsOSSwap(
        pNDSGroup, pGroup, pInstanceHardware, pClusterDescription, pReplicaSetHardware)) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs OS Swap. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.OS_SWAP.name());
      return new ActionContext(Action.OS_SWAP);
    }

    // This block is before checking reboot because OS_POLICY_UPDATE_AND_REBOOT may also triger a
    // reboot, and we don't want to restart twice.
    if (shouldInstanceUpdateOSPolicyAndReboot(pClusterDescription, pInstanceHardware)) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs to update OS policy and reboot. Setting action"
              + " to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.OS_POLICY_UPDATE_AND_REBOOT.name());
      return new ActionContext(Action.OS_POLICY_UPDATE_AND_REBOOT);
    }

    // This block is after healing because if the machine is in need of repair that
    // has to happen before the reboot or the server isn't going to get to goal state
    // after the reboot.
    // The one exception to this is a HEAL_WAIT action should not block a reboot.
    // It is also after OS_SWAP as the swap will imply a reboot anyway.
    if (pInstanceHardware.needsForceServerRestart()
        && pInstanceHardware.getNextRestartOrRebootRequestedDate().isPresent()) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs force server restart. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.REQUESTED_REBOOT.name());
      return new ActionContext(Action.REQUESTED_REBOOT);
    }

    final List<NDSProcessType> processesWithRequestedRestarts =
        pInstanceHardware.getProcessRestartRequestedDates().entrySet().stream()
            .filter(e -> e.getValue() != null)
            .filter(e -> e.getValue().before(new Date()))
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
    if (!processesWithRequestedRestarts.isEmpty()) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs a process restart by user / admin request."
              + " Processes requested: {}., Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          processesWithRequestedRestarts.stream()
              .map(NDSProcessType::getType)
              .collect(Collectors.joining(",")),
          Action.REQUESTED_PROCESS_RESTART.name());
      return new ActionContext(Action.REQUESTED_PROCESS_RESTART);
    }

    if (pInstanceHardware.needsReloadSslOnProcesses()) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs ssl certificate reloaded. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.REQUESTED_RELOAD_SSL_ON_PROCESSES.name());
      return new ActionContext(Action.REQUESTED_RELOAD_SSL_ON_PROCESSES);
    }

    if (pInstanceHardware.getCloudProvider().equals(CloudProvider.AWS)) {
      AWSInstanceHardware awsInstanceHardware = (AWSInstanceHardware) pInstanceHardware;
      if (!awsInstanceHardware.getIsUsingIpamIp()
          && awsInstanceHardware.getMigrateIpAction() == MigrateIpAction.FUTURE_IP
          && awsInstanceHardware.getFutureEipId().isPresent()) {
        return new ActionContext(Action.SWAP_FUTURE_IP);
      }
      if (awsInstanceHardware.getIsUsingIpamIp()
          && awsInstanceHardware.getMigrateIpAction() == MigrateIpAction.PAST_IP
          && awsInstanceHardware.getPastEipId().isPresent()) {
        return new ActionContext(Action.SWAP_PAST_IP);
      }

      final Instant today = Instant.now();
      if (awsInstanceHardware.getIpSwappedDate() != null) {
        final Instant ipSwappedLocalDate = awsInstanceHardware.getIpSwappedDate().toInstant();
        final int releaseThreshold = getAwsReleaseAwsIpThresholdDays();
        final long days = ChronoUnit.DAYS.between(ipSwappedLocalDate, today);
        if (awsInstanceHardware.getIsUsingIpamIp()
            && awsInstanceHardware.getPastEipId().isPresent()
            && days > releaseThreshold) {
          return new ActionContext(Action.RELEASE_AWS_IP);
        }
      }
    }

    // Heal Wait should occur at the end so it doesn't block other processes
    if (healAction.action() == Action.HEAL_WAIT && plannerCanHeal) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} needs healing. Setting action to {}.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          healAction.action().name());
      return healAction;
    } else if (clusterInRegionalOutageSimulation) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} would require healing action {} but regional outage"
              + " simulation in progress. Skipping this choice.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          healAction.action().name());
    }

    // for serverless instances the hostnames were not necessary to connect to the cluster so DNS
    // records were not created for them as part of provisioning. For serverless -> flex clusters,
    // we need these hostnames.
    if (pClusterDescription.wasMigratedFromServerlessToFlex()
        && !pClusterDescription.isClusterCreatedFromLegacyApi()
        && pInstanceHardware.getPublicIP().isEmpty()) {
      pLogger.info(
          "Instance {} (instanceId={}) in {} is a flex instance converted from serverless, and"
              + " needs hostname DNS records to be created. Setting action to {} to kick off"
              + " provision machine moves.",
          memberName,
          pInstanceHardware.getInstanceId(),
          pClusterDescription.getName(),
          Action.CREATE.name());
      return new ActionContext(Action.CREATE);
    }

    pLogger.trace(
        "Instance {} (instanceId={}) in {} is provisioned; no action needed.",
        memberName,
        pInstanceHardware.getInstanceId(),
        pClusterDescription.getName());
    return new ActionContext(Action.NONE);
  }

  /**
   * Checks if RECONFIGURE action should be prioritized for disk size increase during shard
   * draining. This method implements the logic to prioritize disk size increases when: 1. Shards
   * are draining (shardsDraining field in clusterDescription is not empty) 2. The diskSizeGB in
   * clusterDescription is larger than the one in instanceHardware RECONFIGURE will create a plan to
   * increase disk size. Put this before UPDATE_CONFIG action.
   */
  private ActionContext checkForDiskSizeReconfigureDuringShardDraining(
      final InstanceHardware pInstanceHardware,
      final ReplicaSetHardware pReplicaSetHardware,
      final ClusterDescription pClusterDescription,
      final String pMemberName,
      final Logger pLogger) {

    // Check if shards are draining
    final List<String> shardsDraining = pClusterDescription.getShardsDraining();
    if (shardsDraining.isEmpty()) {
      return new ActionContext(Action.NONE);
    }

    // Check if disk size in cluster description is larger than instance hardware disk size
    final double clusterDiskSizeGB = pClusterDescription.getDiskSizeGB();
    final Optional<Integer> instanceDiskSizeGBOpt = pInstanceHardware.getDiskSizeGB();
    if (instanceDiskSizeGBOpt.isEmpty()) {
      // If instance doesn't have disk size set, we can't compare, so skip this optimization
      return new ActionContext(Action.NONE);
    }
    final double instanceDiskSizeGB = instanceDiskSizeGBOpt.get().doubleValue();
    if (clusterDiskSizeGB <= instanceDiskSizeGB) {
      return new ActionContext(Action.NONE);
    }

    pLogger.info(
        "Instance {} in {} needs disk size increase during shard draining. Cluster disk size: {}GB,"
            + " Instance disk size: {}GB, . Setting action to {}.",
        pMemberName,
        pClusterDescription.getName(),
        clusterDiskSizeGB,
        instanceDiskSizeGB,
        Action.RECONFIGURE.name());
    return new ActionContext(Action.RECONFIGURE);
  }

  // Performs updates to the Push Based Log Export feature (if active) on Atlas Search
  // mongot log feature flag changes.

  public void updatePushBasedLogExportEnabledMongotForGroup(
      final Group pGroup, final NDSGroup pNDSGroup) {
    final PushBasedLogExport pushBasedLogExport = pNDSGroup.getPushBasedLogExport();

    // Only make an update to mongot if Push Based Log Export is enabled for the group.
    if (pushBasedLogExport.getState() != PushBasedLogExport.State.ACTIVE) {
      return;
    }

    final boolean isMongotEnabled =
        PushBasedLogExportSvc.isMongotPushBasedLogExportFeatureFlagEnabled(
            this._appSettings, pGroup);

    // Mis-matching enabled mongot values means the feature flag was toggled so update
    // the PushBasedLogExport enabled mongot boolean and date fields.
    if (pushBasedLogExport.getEnabledMongot() != isMongotEnabled) {
      this._ndsGroupSvc.updateEnabledMongotFieldsForPushBasedLogExport(
          pGroup.getId(), isMongotEnabled);
    }
  }

  public boolean shouldInstanceUpdateOSPolicyAndReboot(
      final ClusterDescription pClusterDescription, final InstanceHardware pInstanceHardware) {
    return pClusterDescription.isDedicatedCluster()
        && pClusterDescription
            .getOSPolicyVersion()
            .map(
                goalOSPolicyVersionStr -> {
                  final int goalOSPolicyVersion = Integer.parseInt(goalOSPolicyVersionStr);
                  final int currentOSPolicyVersion =
                      pInstanceHardware
                          .getAppliedOSPolicyVersion()
                          .map(Integer::parseInt)
                          .orElse(0);
                  return currentOSPolicyVersion < goalOSPolicyVersion;
                })
            // needed for initial rollout since existing clusters doesn't have osPolicyVersion set
            // in CD yet.
            .orElse(false);
  }

  private static boolean doesHardwareSpecMatchInstanceHardware(
      final ReplicationSpec pReplicationSpec,
      final ClusterDescription pClusterDescription,
      final ReplicaSetHardware pReplicaSetHardware,
      final InstanceHardware pInstanceHardware,
      final NodeType pNodeType) {
    final CloudProvider cloudProvider = pInstanceHardware.getCloudProvider();
    final HardwareSpec hardwareSpec =
        pReplicationSpec.getHardwareSpecByProviderAndNodeType(cloudProvider, pNodeType);
    final double diskSizeGB =
        pClusterDescription.getDiskSizeGBWithReplicationSpecId(pReplicationSpec.getId());

    if (pReplicaSetHardware.containsShardData()) {
      return pInstanceHardware.doesShardHardwareMatch(
          hardwareSpec, diskSizeGB, pClusterDescription.getCloudProviders());
    } else {
      return pInstanceHardware.doesConfigServerHardwareMatch(
          hardwareSpec,
          diskSizeGB,
          pClusterDescription.getCloudProviders(),
          pClusterDescription.getClusterType().isSharded());
    }
  }

  private static boolean isNonNVMeToNVMeUpdate(
      final InstanceHardware pInstanceHardware,
      final ClusterDescription pClusterDescription,
      final NodeType pNodeType) {
    return !pInstanceHardware.isNVMe() && pClusterDescription.isNVMe(pNodeType);
  }

  public void doReactiveAutoScalingContextConversions(
      final ObjectId groupId, final List<Cluster> pClusters, final Logger pLogger) {
    final List<Cluster> nonTenantClusters =
        pClusters.stream().filter(c -> !c.getClusterDescription().isTenantCluster()).toList();
    final Map<String, AutoScalingContext> clusterToContextMap =
        _autoScalingContextDao.findAllInGroup(groupId).stream()
            .collect(Collectors.toMap(c -> c.getId().getClusterName(), c -> c));

    for (final Cluster cluster : nonTenantClusters) {
      final Optional<AutoScalingContext> scalingContext =
          Optional.ofNullable(clusterToContextMap.get(cluster.getClusterDescription().getName()));

      if (scalingContext.isEmpty()) {
        pLogger
            .atLevel(Level.WARN)
            .addKeyValue("Cluster", cluster.getClusterDescription().getName())
            .addKeyValue("Group", cluster.getClusterDescription().getGroupId())
            .log("Cluster has no autoscaling context to convert");
        continue;
      }

      final boolean needsConversion =
          needsReactiveAutoScalingContextConversion(cluster, scalingContext.get());
      if (!needsConversion) {
        continue;
      }

      final ComputeAutoScalingContext newBaseComputeContext;
      final ComputeAutoScalingContext newAnalyticsComputeContext;
      if (scalingContext.get().getBaseComputeContext()
          instanceof final ClusterComputeAutoScalingContext clusterContext) {
        newBaseComputeContext =
            ComputeAutoScalingContextUtil.convertClusterToShardComputeAutoScalingContext(
                clusterContext, cluster);
        newAnalyticsComputeContext =
            ComputeAutoScalingContextUtil.convertClusterToShardComputeAutoScalingContext(
                (ClusterComputeAutoScalingContext)
                    scalingContext.get().getAnalyticsComputeContext(),
                cluster);
      } else if (scalingContext.get().getBaseComputeContext()
          instanceof final ShardComputeAutoScalingContext clusterContext) {
        newBaseComputeContext =
            ComputeAutoScalingContextUtil.convertShardToClusterComputeAutoScalingContext(
                clusterContext, cluster);
        newAnalyticsComputeContext =
            ComputeAutoScalingContextUtil.convertShardToClusterComputeAutoScalingContext(
                (ShardComputeAutoScalingContext) scalingContext.get().getAnalyticsComputeContext(),
                cluster);
      } else {
        throw new IllegalStateException(
            "AutoScalingContext documents does not conform to either type");
      }

      pLogger
          .atLevel(Level.INFO)
          .addKeyValue("Cluster", cluster.getClusterDescription().getName())
          .addKeyValue("Group", cluster.getClusterDescription().getGroupId())
          .addKeyValue("NewStrategy", cluster.getClusterDescription().getAutoScalingMode())
          .log("Cluster autoscaling context will be converted to match new strategy");

      final AutoScalingContext newScalingContext =
          scalingContext.get().toBuilder()
              .analyticsComputeContext(newAnalyticsComputeContext)
              .baseComputeContext(newBaseComputeContext)
              .build();
      _autoScalingContextDao.save(newScalingContext);
    }
  }

  private static boolean needsReactiveAutoScalingContextConversion(
      Cluster cluster, AutoScalingContext scalingContext) {
    final AutoScalingMode currentMode = cluster.getClusterDescription().getAutoScalingMode();
    if (scalingContext.getBaseComputeContext() instanceof ShardComputeAutoScalingContext) {
      return currentMode.equals(AutoScalingMode.CLUSTER);
    }

    // instance of ClusterComputeAutoScalingContext
    return currentMode.equals(AutoScalingMode.SHARD);
  }

  public void doPredictiveAutoScalingContextConversion(
      final ObjectId groupId, final List<Cluster> clusters, final Logger logger) {
    final List<Cluster> nonTenantClusters =
        clusters.stream().filter(c -> !c.getClusterDescription().isTenantCluster()).toList();

    final Map<String, PredictiveAutoScalingContext> clusterToContextMap =
        _predictiveAutoScalingContextSvc.findByGroup(groupId).stream()
            .collect(
                Collectors.toMap(PredictiveAutoScalingContext::clusterName, Function.identity()));

    for (final Cluster cluster : nonTenantClusters) {
      final Optional<PredictiveAutoScalingContext> predictiveAutoScalingContext =
          Optional.ofNullable(clusterToContextMap.get(cluster.getClusterDescription().getName()));

      if (predictiveAutoScalingContext.isEmpty()) {
        // PredictiveAutoScalingContext only created when feature enabled, not by default
        continue;
      }

      final boolean needsConversion =
          needsPredictiveAutoScalingContextConversion(cluster, predictiveAutoScalingContext.get());

      if (!needsConversion) {
        continue;
      }

      // We want to be notified when a customer has mismatched autoscaling modes and uses
      // predictive scaling criteria overrides. Manual intervention required to understand
      // the overrides and their implications under ISS.
      // Predictive autoscaling will be disabled for this cluster until TS removes the overrides.
      if (predictiveAutoScalingContext
          .get()
          .baseComputeContext()
          .hasAnyScalingCriteriaOverrides()) {
        logger
            .atLevel(Level.ERROR)
            .addKeyValue("Cluster", cluster.getClusterDescription().getName())
            .addKeyValue("Group", cluster.getClusterDescription().getGroupId())
            .log(
                "Cluster has mismatched auto-scaling modes and predictive autoscaling context"
                    + " overrides.");
        continue;
      }

      // Predictive auto-scaling only support base nodes
      final ComputePredictiveAutoScalingContext newBaseComputeContext =
          getConvertedComputePredictiveAutoScalingContext(
              cluster, predictiveAutoScalingContext.get());
      logger
          .atLevel(Level.INFO)
          .addKeyValue("Cluster", cluster.getClusterDescription().getName())
          .addKeyValue("Group", cluster.getClusterDescription().getGroupId())
          .addKeyValue("NewAutoScalingMode", cluster.getClusterDescription().getAutoScalingMode())
          .log("Predictive autoscaling context converted to match new autoscaling mode");

      final PredictiveAutoScalingContext updatedPredictiveContext =
          predictiveAutoScalingContext.get().toBuilder()
              .baseComputeContext(newBaseComputeContext)
              .build();
      _predictiveAutoScalingContextSvc.saveUpdatedContext(updatedPredictiveContext);
    }
  }

  private static ComputePredictiveAutoScalingContext
      getConvertedComputePredictiveAutoScalingContext(
          Cluster cluster, PredictiveAutoScalingContext predictiveAutoScalingContext) {
    final ComputePredictiveAutoScalingContext newBaseComputeContext;
    if (predictiveAutoScalingContext.baseComputeContext()
        instanceof final ClusterComputePredictiveAutoScalingContext clusterContext) {
      newBaseComputeContext =
          ComputePredictiveAutoScalingContextUtil
              .convertClusterToShardComputePredictiveAutoScalingContext(clusterContext, cluster);
    } else if (predictiveAutoScalingContext.baseComputeContext()
        instanceof final ShardComputePredictiveAutoScalingContext shardContext) {
      newBaseComputeContext =
          ComputePredictiveAutoScalingContextUtil
              .convertShardToClusterComputePredictiveAutoScalingContext(shardContext, cluster);
    } else {
      throw new IllegalStateException(
          "PredictiveAutoScalingContext document does not conform to either type");
    }
    return newBaseComputeContext;
  }

  private static boolean needsPredictiveAutoScalingContextConversion(
      Cluster cluster, PredictiveAutoScalingContext scalingContext) {
    final AutoScalingMode currentMode = cluster.getClusterDescription().getAutoScalingMode();
    if (scalingContext.baseComputeContext() instanceof ShardComputePredictiveAutoScalingContext) {
      return currentMode.equals(AutoScalingMode.CLUSTER);
    }

    // instance of ClusterComputePredictiveAutoScalingContext
    return currentMode.equals(AutoScalingMode.SHARD);
  }

  protected boolean instanceNeedsOSSwap(
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final InstanceHardware pInstanceHardware,
      final ClusterDescription pClusterDescription,
      final ReplicaSetHardware pReplicaSetHardware) {

    if (pInstanceHardware.needsOSSwap()) {
      return true;
    }

    if (!pInstanceHardware.getCloudProvider().isDedicatedProvider()) {
      return false;
    }

    final Optional<OS> instanceHardwareOSOptional = pInstanceHardware.getOS();

    if (instanceHardwareOSOptional.isEmpty()) {
      return false;
    }

    final OS instanceHardwareOS = instanceHardwareOSOptional.orElseThrow();

    final RegionName regionName =
        pClusterDescription.getRegionName(
            pInstanceHardware,
            pNDSGroup.getCloudProviderContainer(pInstanceHardware.getCloudContainerId()));

    if (regionName == null) {
      return false;
    }

    final Optional<NodeType> nodeTypeOptional =
        pReplicaSetHardware.getNodeTypeForInstance(
            pClusterDescription, pInstanceHardware.getInstanceId());

    if (nodeTypeOptional.isEmpty()) {
      return false;
    }

    final NodeType nodeType = nodeTypeOptional.orElseThrow();

    final OS desiredOS =
        pClusterDescription.getOS(pReplicaSetHardware.getReplicationSpecId(), regionName, nodeType);

    if (desiredOS == null) {
      return false;
    }

    final boolean isAWSAL2023ForceMigrateFlagEnabled =
        FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.AWS_AL2023_FORCE_MIGRATE_NVME, _appSettings, null, pGroup);
    final boolean isAzureAL2023ForceMigrateFlagEnabled =
        FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.AZURE_AL2023_FORCE_MIGRATE_NVME, _appSettings, null, pGroup);

    if (desiredOS == OS.AL2023 && pInstanceHardware.isNVMe()) {
      if (!isAWSAL2023ForceMigrateFlagEnabled
          && pInstanceHardware.getCloudProvider().equals(CloudProvider.AWS)) {
        return false;
      } else if (!isAzureAL2023ForceMigrateFlagEnabled
          && pInstanceHardware.getCloudProvider().equals(CloudProvider.AZURE)) {
        return false;
      }
    }

    return !instanceHardwareOS.equals(desiredOS);
  }

  protected boolean instanceNeedsCpuArchitectureSwap(
      final NDSGroup pNDSGroup,
      final InstanceHardware pInstanceHardware,
      final ClusterDescription pClusterDescription,
      final ReplicaSetHardware pReplicaSetHardware) {

    final RegionName regionName =
        pClusterDescription.getRegionName(
            pInstanceHardware,
            pNDSGroup.getCloudProviderContainer(pInstanceHardware.getCloudContainerId()));

    if (regionName == null) {
      return false;
    }

    final Optional<NodeType> nodeTypeOptional =
        pReplicaSetHardware.getNodeTypeForInstance(
            pClusterDescription, pInstanceHardware.getInstanceId());

    if (nodeTypeOptional.isEmpty()) {
      return false;
    }

    final NodeType nodeType = nodeTypeOptional.orElseThrow();

    final Optional<HardwareSpec> hardwareSpecOptional =
        pClusterDescription.getHardwareSpec(
            pReplicaSetHardware.getReplicationSpecId(), regionName, nodeType);

    if (hardwareSpecOptional.isEmpty()) {
      return false;
    }

    final HardwareSpec hardwareSpec = hardwareSpecOptional.orElseThrow();
    final InstanceFamily instanceFamily = hardwareSpec.getInstanceFamily();

    if (instanceFamily == null) {
      return false;
    }

    return pInstanceHardware.needsCpuArchitectureSwap(instanceFamily.getCpuArchitecture());
  }

  protected ActionContext shouldHealInstance(
      final InstanceHardware pInstanceHardware,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final ClusterDescriptionId pClusterDescriptionId,
      final boolean pNeedsIntraContainerMigration,
      final boolean pIsReplicaSetMemberWithData,
      final Logger pLogger,
      final ReplicaSetHardware pReplicaSetHardware,
      final ClusterDescription pClusterDescription) {

    final boolean isNewAutoHealResyncAlgoEnabled =
        isFeatureFlagEnabled(
            FeatureFlag.ATLAS_AUTOHEAL_RESYNC_IMPROVEMENTS, _appSettings, null, pGroup);

    // check if admin cluster lock is set to disable autohealing
    final Optional<AdminClusterLock> adminClusterLock =
        _adminClusterLockSvc.findByClusterDescriptionId(pClusterDescriptionId);
    if (adminClusterLock.isPresent() && adminClusterLock.get().getDisableAutoHealing()) {
      pLogger.info(
          "Instance ({}, instanceId: {}) in {} is not being healed because auto-healing is disabled"
              + " by admin lock",
          pInstanceHardware.getHostnameForAgents().get(),
          pInstanceHardware.getInstanceId(),
          pClusterDescriptionId);
      return new ActionContext(Action.NONE);
    }

    final boolean removeIcmpPing =
        isFeatureFlagEnabled(ATLAS_AUTOHEAL_REMOVE_ICMP_PING, _appSettings, null, pGroup);
    final boolean reduceAutohealThreashold =
        isFeatureFlagEnabled(ATLAS_AUTOHEAL_REDUCED_THRESHOLD, _appSettings, null, pGroup);
    final ScheduledAction actionFromInstancesView =
        pInstanceHardware.needsHealing(
            pLogger,
            new HealingConfiguration(
                removeIcmpPing, isNewAutoHealResyncAlgoEnabled, reduceAutohealThreashold));

    if (!pNeedsIntraContainerMigration
        && !Action.ACTIONABLE_HEAL_STATES.contains(actionFromInstancesView.getAction())) {
      pLogger.info(
          "Intermediary action (without action optimization or considering user-defined"
              + " maintenance window) to take on instance {}: {}. Reason: {}",
          pInstanceHardware.getInstanceId(),
          actionFromInstancesView.getAction().name(),
          actionFromInstancesView.getReason());
      return new ActionContext(
          actionFromInstancesView.getAction(), actionFromInstancesView.getAuditEventMessage());
    }

    if (pIsReplicaSetMemberWithData && pNeedsIntraContainerMigration) {
      if (!verifyPrivateLinkConnectionForInstanceMigration(pInstanceHardware, pNDSGroup, pLogger)) {
        return new ActionContext(
            actionFromInstancesView.getAction(), actionFromInstancesView.getAuditEventMessage());
      }

      pLogger.info(
          "Instance ({}, instanceId: {}) needs migration within container.",
          pInstanceHardware.getHostnameForAgents().get(),
          pInstanceHardware.getInstanceId());
      // TODO: This is a workaround to make sure that zone migrations caused by replication spec
      // changes are RECONFIGURE and not HEAL_REPAIR.
      return new ActionContext(Action.RECONFIGURE);
    }

    if (pIsReplicaSetMemberWithData) {
      if (isNewAutoHealResyncAlgoEnabled
          && actionFromInstancesView.getAction() == Action.HEAL_RESYNC) {

        final boolean isOISAvailable =
            validateOISAvailable(
                pNDSGroup,
                pInstanceHardware,
                pClusterDescription,
                pReplicaSetHardware,
                Optional.empty());

        final boolean isEncryptionAtRestEnabled =
            isEncryptionAtRestEnabledAndInvalid(pNDSGroup, pClusterDescription);

        final ScheduledAction updatedAction =
            _autoHealResyncSvc.getScheduledActionForAutoHealResync(
                pClusterDescription,
                pReplicaSetHardware.getRsId(),
                pInstanceHardware,
                isOISAvailable,
                isEncryptionAtRestEnabled,
                actionFromInstancesView,
                pLogger);

        pLogger.info(
            "Intermediary action (without action optimization or considering user-defined"
                + " maintenance window) to take on instance {}: {}. Reason: {}",
            pInstanceHardware.getInstanceId(),
            updatedAction.getAction().name(),
            updatedAction.getReason());
        return new ActionContext(
            updatedAction.getAction(),
            updatedAction.getAction().isActionable()
                ? AutoHealMessage.NODE_RESYNC
                : AutoHealMessage.CANNOT_RESYNC);
      }

      pLogger.info(
          "Intermediary action (without action optimization or considering user-defined"
              + " maintenance window) to take on instance {}: {}. Reason: {}",
          pInstanceHardware.getInstanceId(),
          actionFromInstancesView.getAction().name(),
          actionFromInstancesView.getReason());

      return new ActionContext(
          actionFromInstancesView.getAction(), actionFromInstancesView.getAuditEventMessage());
    }

    pLogger.info(
        "Instance ({}, instanceId: {}) associated with replica set either has no primary or is not"
            + " sending pings. Cannot safely repair",
        pInstanceHardware.getHostnameForAgents().get(),
        pInstanceHardware.getInstanceId());
    return new ActionContext(Action.HEAL_CANNOT_REPAIR, CANNOT_REPAIR_NO_DATA);
  }

  protected boolean verifyPrivateLinkConnectionForInstanceMigration(
      final InstanceHardware pInstanceHardware, final NDSGroup pNDSGroup, final Logger pLogger) {
    // In AWS, before migrating instances, we need to check that private link connection is not
    // pending an update. When a new subnet is added, private link connection needs to be updated in
    // order to see it. If an instance gets migrated to the new subnet before the private link can
    // see it, it will be impossible to connect to that instance via the private link.
    switch (pInstanceHardware.getCloudProvider()) {
      case AWS:
        AWSCloudProviderContainer container =
            (AWSCloudProviderContainer)
                pNDSGroup.getCloudProviderContainer(pInstanceHardware.getCloudContainerId()).get();
        final Optional<AWSPrivateLinkConnection> endpointServiceOpt =
            container.getEndpointService();

        if (endpointServiceOpt.isPresent()) {
          final AWSPrivateLinkConnection endpointService = endpointServiceOpt.get();

          if (endpointService.isActive() && endpointService.getNeedsUpdateAfter().isPresent()) {
            pLogger.info(
                "Instance {} needs migration within container, but endpoint service with id {}"
                    + " has a pending update, will not migrate the instance at this time",
                pInstanceHardware.getHostnameForAgents().get(),
                endpointService.getId());
            return false;
          }
        }
    }

    return true;
  }

  protected boolean snapshotSourceExistsForInstance(
      final NDSGroup pNDSGroup,
      final InstanceHardware pInstanceHardware,
      final ClusterDescription pClusterDescription,
      final ReplicaSetHardware pReplicaSetHardware,
      final List<Host> pHosts,
      final Optional<SyncSourceStrategy> pStrategy) {
    final List<Host> hostsValidForBecomingSyncSource =
        pHosts.stream()
            .filter(h -> !pInstanceHardware.getHostnameForAgents().equals(Optional.of(h.getName())))
            .collect(Collectors.toList());
    switch (pInstanceHardware.getCloudProvider()) {
      case AWS:
      case AZURE:
      case GCP:
        return OptimizedInitialSyncUtil.findSnapshotSourceHostIdAndInstanceHardware(
                pReplicaSetHardware,
                pClusterDescription,
                pClusterDescription.getRegionName(
                    pInstanceHardware,
                    pNDSGroup.getCloudProviderContainer(pInstanceHardware.getCloudContainerId())),
                pNDSGroup,
                hostsValidForBecomingSyncSource,
                pStrategy,
                pClusterDescription.getDiskSizeGBWithReplicationSpecId(
                    pReplicaSetHardware.getReplicationSpecId()))
            .isPresent();
      default:
        throw new IllegalArgumentException("Wrong cloud provider.");
    }
  }

  private NDSServerlessAutoScalingLogger getNDSServerlessAutoScalingLogger(final Logger pLogger) {
    return new NDSServerlessAutoScalingLogger(_appSettings, pLogger);
  }

  private ObjectId getClusterId(final ObjectId projectId, final String clusterName) {
    if (projectId == null || StringUtils.isBlank(clusterName)) {
      return null;
    }
    final Optional<ClusterDescription> clusterDescription =
        _clusterSvc.getMergedClusterDescription(projectId, clusterName);
    if (clusterDescription.isPresent()) {
      return clusterDescription.get().getUniqueId();
    }
    return null;
  }

  protected void saveFinalAutoHealingAuditEvent(
      final ObjectId pGroupId,
      final String pClusterName,
      final InstanceHardware pInstanceHardware,
      final ActionContext pUpdatedAction,
      final Instant pActionTransitionInstant,
      final boolean pIsInternal) {

    if (!pIsInternal && !pUpdatedAction.action.isActionable()) {
      return;
    }

    final Pair<Type, String> eventType =
        pIsInternal
            ? pUpdatedAction.getTypeAndDescriptionForAutoHealingAuditEventInternal()
            : pUpdatedAction.getTypeAndDescriptionForAutoHealingAuditEvent();
    final NDSAudit.Builder builder = new Builder(eventType.getLeft());
    builder.groupId(pGroupId);
    builder.hidden(pIsInternal || !eventType.getLeft().getInfo().isSeverityInfo());
    builder.auditInfo(AuditInfoHelpers.fromSystem());
    builder.clusterName(pClusterName);
    builder.clusterId(getClusterId(pGroupId, pClusterName));

    // this is to allow list modification below
    final List<AuditDescription> auditDescriptionEntries =
        new ArrayList<>(
            List.of(
                new AuditDescription("Instance ID", pInstanceHardware.getInstanceId().toString()),
                new AuditDescription(
                    "Internal Hostname", pInstanceHardware.getHostnameForAgents().orElse(null)),
                new AuditDescription("Existing Action", pInstanceHardware.getAction().name()),
                new AuditDescription("Final Updated Action", pUpdatedAction.action().name()),
                new AuditDescription("Reason", eventType.getRight()),
                new AuditDescription("UTC Timestamp", pActionTransitionInstant.toString())));

    final Optional<String> splunkIndex = NDSDefaults.getSplunkIndex(_appSettings);
    if (splunkIndex.isPresent()) {
      final String splunkQueryURL =
          generateSplunkQueryURL(
              pInstanceHardware.getInstanceId(), pActionTransitionInstant, splunkIndex.get());
      auditDescriptionEntries.add(new AuditDescription("Related Splunk Query", splunkQueryURL));
    }

    builder.auditDescription(auditDescriptionEntries);

    _auditSvc.saveAuditEvent(builder.build());
    Set<Type> alertTypes =
        Set.of(AUTO_HEALING_CANNOT_REPAIR_INTERNAL, AUTO_HEALING_CANNOT_RESYNC_INTERNAL);

    if (alertTypes.contains(eventType.getLeft())) _alertSvc.enqueueEvent(builder.build());
  }

  protected String generateSplunkQueryURL(
      final ObjectId pInstanceId, final Instant pEventTimestamp, final String pSplunkIndex) {
    final long earliestTimestamp = pEventTimestamp.minus(5, ChronoUnit.MINUTES).getEpochSecond();
    final long latestTimestamp = pEventTimestamp.plus(2, ChronoUnit.HOURS).getEpochSecond();

    return String.format(
        "https://splunk.corp.mongodb.com/en-US/app/search/search?"
            + "q=search%%20index%%3D%%22%s%%22%%20sourcetype%%3D%%22xgen_service%%22%%20HEAL_*%%20%s"
            + "&display.page.search.mode=smart&dispatch.sample_ratio=1&earliest=%s&latest=%s",
        pSplunkIndex, pInstanceId.toString(), earliestTimestamp, latestTimestamp);
  }

  private void saveAuditEvent(
      final NDSAudit.Type pAuditType, final ObjectId pGroupId, final PlanSummary pPlanSummary) {
    final NDSAudit.Builder builder = new NDSAudit.Builder(pAuditType);
    builder.groupId(pGroupId);
    builder.hidden(true);
    builder.auditInfo(AuditInfoHelpers.fromSystem());
    builder.planId(pPlanSummary.getPlanId());
    if (pPlanSummary.getClusterName().isPresent()) {
      builder.clusterName(pPlanSummary.getClusterName().get());
      builder.clusterId(getClusterId(pGroupId, pPlanSummary.getClusterName().get()));
    }
    _auditSvc.saveAuditEvent(builder.build());
  }

  private void saveAudit(
      final NDSAudit.Type pAuditType,
      final ObjectId pGroupId,
      final Result.PlanFailureCode pFailureCode,
      final Optional<PlanSummary> pPlanSummary,
      final String pLastError) {
    // Don't care about sending notifications when someone
    // deletes a cluster while a plan is in progress.
    if (pFailureCode == Result.PlanFailureCode.PLAN_ABANDONED) {
      return;
    }

    String clusterName = null;
    if (pPlanSummary.isPresent() && pPlanSummary.get().getClusterName().isPresent()) {
      clusterName = pPlanSummary.get().getClusterName().get();
    }

    final Optional<ObjectId> planId = pPlanSummary.map(PlanSummary::getPlanId);

    final NDSAudit.Builder builder = new NDSAudit.Builder(pAuditType);
    builder.groupId(pGroupId);
    builder.hidden(true);
    builder.failureCode(pFailureCode);
    if (pLastError != null) {
      builder.lastError(pLastError);
    }
    builder.auditInfo(AuditInfoHelpers.fromSystem());
    builder.clusterName(clusterName);
    builder.clusterId(getClusterId(pGroupId, clusterName));
    builder.planId(planId.orElse(null));
    final Event event = builder.build();
    _auditSvc.saveAuditEvent(event);
    _alertSvc.enqueueEvent(event);
  }

  static class NDSPlanningSvcCollector extends Collector {

    private final CronStateDao _cronStateDao;
    private final JobParkingSvc _jobParkingSvc;
    private final AppSettings _appSettings;

    NDSPlanningSvcCollector(
        final CronStateDao pCronStateDao,
        final JobParkingSvc pJobParkingSvc,
        final AppSettings pAppSettings) {
      _cronStateDao = pCronStateDao;
      _jobParkingSvc = pJobParkingSvc;
      _appSettings = pAppSettings;
    }

    @Override
    public List<MetricFamilySamples> collect() {
      final List<MetricFamilySamples> mfs = new ArrayList<>();

      final boolean isPlanningCronDisabled = _cronStateDao.getManuallyDisabled(CRON_NAME);
      final boolean isPlanExecutionDisabled =
          _jobParkingSvc.getAllFilteredHandlers(false).stream()
              .anyMatch(i -> i.getJobHandler().equals(PlanExecutorJobHandler.class));
      final boolean isPlannerPaused = isPlanningCronDisabled || isPlanExecutionDisabled;
      final boolean isDryRunModeEnabled =
          _appSettings.getBoolProp(AppSettings.Fields.NDS_DRY_RUN_MODE_ENABLED.value, false);

      final int gaugeValue;
      if (isPlannerPaused) {
        gaugeValue = 1;
      } else if (isDryRunModeEnabled) {
        gaugeValue = 2;
      } else {
        gaugeValue = 0;
      }

      mfs.add(
          new GaugeMetricFamily(
              "mms_nds_planner_mode",
              "Planner mode as gauge. 0=RUNNING, 1=PAUSED, 2=DRY_RUN",
              gaugeValue));

      final GaugeMetricFamily stuckPlanGenerationGauge =
          new GaugeMetricFamily(
              "mms_nds_planner_stuck_plan_generation_total",
              "Count of groups with stuck plan generation",
              Collections.singletonList("job_status"));

      // only existing cache entries will be instrumented as Gauge value, stale data point (older
      // than 6 min) will be discarded automatically by cache
      _jobStatusToStuckCountCache
          .asMap()
          .forEach(
              (status, count) ->
                  stuckPlanGenerationGauge.addMetric(Collections.singletonList(status), count));
      mfs.add(stuckPlanGenerationGauge);

      final GaugeMetricFamily planningRoundGroupsGauge =
          new GaugeMetricFamily(
              PLANNING_ROUND_GROUPS_GAUGE_NAME,
              PLANNING_ROUND_GROUPS_GAUGE_DESCRIPTION,
              PLANNING_ROUND_GROUPS_GAUGE_LABELS);
      _latestPlanningRoundGroupsGaugeCache
          .asMap()
          .forEach(
              (label, count) ->
                  planningRoundGroupsGauge.addMetric(Collections.singletonList(label), count));
      mfs.add(planningRoundGroupsGauge);

      final GaugeMetricFamily totalGroupsNeedPlanningGauge =
          new GaugeMetricFamily(
              TOTAL_GROUPS_NEED_PLANNING_GAUGE_NAME,
              TOTAL_GROUPS_NEED_PLANNING_GAUGE_DESCRIPTION,
              TOTAL_GROUPS_NEED_PLANNING_GAUGE_LABELS);

      _latestTotalGroupsNeedPlanningGaugeCacheGrouped
          .asMap()
          .forEach(
              (hasContainerAndState, count) -> {
                totalGroupsNeedPlanningGauge.addMetric(
                    List.of(hasContainerAndState.getLeft(), hasContainerAndState.getRight()),
                    count);
              });
      mfs.add(totalGroupsNeedPlanningGauge);

      final GaugeMetricFamily activePlansGauge =
          new GaugeMetricFamily(
              TOTAL_PLANS_ACTIVE_GAUGE_NAME,
              TOTAL_PLANS_ACTIVE_GAUGE_DESCRIPTION,
              TOTAL_PLANS_ACTIVE_GAUGE_LABELS);

      _latestTotalPlansActiveGaugeCache
          .asMap()
          .forEach(
              (label, count) ->
                  activePlansGauge.addMetric(Collections.singletonList(label), count));

      mfs.add(activePlansGauge);

      return mfs;
    }
  }

  public long getNDSPlannerSnapshotDeletionBufferInterval() {
    return _appSettings.getLongProp(
        NDS_PLANNER_SNAPSHOT_DELETION_BUFFER_INTERVAL, DEFAULT_SNAPSHOT_DELETION_BUFFER.toMillis());
  }

  public Long getNDSPlannerNextGroupPlanningDateUnableToPlanInterval() {
    return _appSettings.getLongProp(
        NDS_PLANNER_NEXT_GROUP_PLANNING_DATE_UNABLE_TO_PLAN_FOR_CLUSTER_INTERVAl,
        DEFAULT_UNABLE_TO_PLAN_FOR_CLUSTER_INTERVAL);
  }

  public boolean getNDSPlannerNextGroupPlanningDateAllSnapshotsPurgedDateEnabled() {
    return _appSettings.getBoolProp(
        NDS_PLANNER_NEXT_GROUP_PLANNING_DATE_ALL_SNAPSHOTS_PURGED_DATE_ENABLED, true);
  }

  private void countCreatedPlan(final PlanSummary pSummary, final NDSGroup pNdsGroup) {
    NDSPromMetricsSvc.incrementCounter(
        PLANS_CREATED_COUNTER,
        pSummary.getActionType().name(),
        pSummary.getResourceType().name(),
        pNdsGroup.getNextPlanningDateSetBy().name());
  }

  private void countCreatedMovesWithinPlan(final Plan pPlan, final NDSGroup pNdsGroup) {
    pPlan
        .getMoves()
        .forEach(
            move ->
                NDSPromMetricsSvc.incrementCounter(
                    MOVES_CREATED_COUNTER,
                    move.getClass().getSimpleName(),
                    pNdsGroup.getNextPlanningDateSetBy().name()));
  }

  private void countCompletedPlan(final Plan pPlan, final Optional<PlanSummary> pSummary) {
    NDSPromMetricsSvc.incrementCounter(
        PLAN_COMPLETED_COUNTER,
        pPlan.getLastResult().name(),
        Boolean.valueOf(pPlan.isRollback()).toString(),
        pPlan.getFailureCode().name(),
        pSummary.map(s -> s.getActionType().name()).orElse("NOT_FOUND"),
        pSummary.map(s -> s.getResourceType().name()).orElse("NOT_FOUND"));
  }

  List<BackupRestoreJobPlanUnit> getAllNonExportsAndFilteredExports(
      List<BackupRestoreJobPlanUnit> pRestores) {

    final int maximumConcurrentExports = _appSettings.getBackupRestoreMaximumConcurrentExports();

    // get a list of all export jobs and sort them by jobId to prevent starvation
    final List<BackupRestoreJobPlanUnit> exportJobs =
        pRestores.stream()
            .filter(r -> r.getPlanStrategy().equals(CpsRestoreMetadata.StrategyName.EXPORT))
            .sorted(Comparator.comparing(BackupRestoreJobPlanUnit::getJobIdForPlanSummary))
            .toList();

    // first filter out all export restore jobs, then add back the first five eligible export jobs
    final List<BackupRestoreJobPlanUnit> plannedRestoreJobs =
        pRestores.stream()
            .filter(r -> !r.getPlanStrategy().equals(CpsRestoreMetadata.StrategyName.EXPORT))
            .collect(Collectors.toList());

    int totalRestoreExportJobs = 0;

    final List<BackupRestoreJobPlanUnit> exportJobsToBePlanned = new ArrayList<>();
    for (BackupRestoreJobPlanUnit e : exportJobs) {
      exportJobsToBePlanned.add(e);
      totalRestoreExportJobs += e.getReplSetJobs().size();

      if (totalRestoreExportJobs >= maximumConcurrentExports) {
        break;
      }
    }
    plannedRestoreJobs.addAll(exportJobsToBePlanned);

    return plannedRestoreJobs;
  }

  @VisibleForTesting
  void emitPlanningLatency(
      final NDSGroup pNDSGroup,
      final Logger pLogger,
      final Date pStartPlanningDate,
      final Date pPlanningDate) {
    final List<CloudProviderContainer> provisionedContainers =
        pNDSGroup.getCloudProviderContainers().stream()
            .filter(CloudProviderContainer::isProvisioned)
            .collect(Collectors.toList());
    final boolean hasEmptyContainersOnly = provisionedContainers.isEmpty();
    final boolean hasActiveTenantContainersOnly =
        !hasEmptyContainersOnly
            && provisionedContainers.stream()
                .allMatch(container -> container.getCloudProvider().isTenantProvider());

    final Date planASAPDate =
        new Date(pStartPlanningDate.getTime() - NDSGroup.PLAN_ASAP_BUFFER.toMillis());
    final Date requestedPlanningDate =
        pPlanningDate.before(planASAPDate)
            ? new Date(pPlanningDate.getTime() + NDSGroup.PLAN_ASAP_BUFFER.toMillis())
            : pPlanningDate;
    final double latencyInSeconds =
        (pStartPlanningDate.getTime() - requestedPlanningDate.getTime()) / 1000.0;
    final String containerTypesLabel;
    if (hasEmptyContainersOnly) {
      containerTypesLabel = "EMPTY";
    } else if (hasActiveTenantContainersOnly) {
      containerTypesLabel = "TENANT";
    } else {
      containerTypesLabel = "DEDICATED";
    }
    pLogger.debug(
        "Planning latency for group {} is {} seconds", pNDSGroup.getGroupId(), latencyInSeconds);
    NDSPromMetricsSvc.recordTimer(PLANNING_LATENCY_SUMMARY, latencyInSeconds, containerTypesLabel);
  }

  @VisibleForTesting
  NDSPlanningTraceWrapper withNDSPlanningTrace(final Attributes pAttributes) {
    return _ndsPlanningTracingSvc.startTracing(NDSPlanningSvc.class, pAttributes);
  }

  private NDSPlanningTraceWrapper withNDSPlanningTrace() {
    return _ndsPlanningTracingSvc.startTracing(NDSPlanningSvc.class);
  }

  private void batchSubmitGroupsForPlanning(final Collection<ObjectId> pGroupIds) {
    NDSPromMetricsSvc.incrementCounter(PLANNING_JOB_QUEUE_COUNTER, pGroupIds.size());
    final Job job = PlanGeneratorJobHandler.getNewJobForGroups(pGroupIds);
    _ndsGroupSvc.setPlanGenerationJobIdAndInsertJob(pGroupIds, job);
    if (_appSettings.getAppEnv().isDevOrQA()) {
      LOG.debug("Successfully submitted groups {} for job {}", pGroupIds, job.getId());
    }
  }

  @VisibleForTesting
  Set<ObjectId> getGroupsNeedPlanning(final Date pPlanningRoundStartDate) {
    final int maxPlanningGroups =
        _appSettings.getIntProp(MAX_CONCURRENT_PLANNING_GROUPS_PARAM, 3000);
    final int planningGroupsCount = _ndsGroupDao.getPlanningGroupsCount();

    _latestPlanningRoundGroupsGaugeCache.put(TYPE_EXISTING_LABEL_VALUE, planningGroupsCount);

    final int limit = maxPlanningGroups - planningGroupsCount;

    // only query groups for planning if limit is positive because limit(0) means no limit
    // and limit(negative) will still return documents
    if (limit > 0) {
      return _ndsGroupDao.getGroupsNeedPlanning(pPlanningRoundStartDate, limit);
    }
    return Set.of();
  }

  public long getNDSPlannerGroupIntervalDistributedPlanningStart() {
    return _appSettings.getLongProp(NDS_PLANNER_GROUP_INTERVAL_DISTRIBUTED_PLANNING_START, 0L);
  }

  public long getNDSPlannerTransientExceptionInterval() {
    return _appSettings.getLongProp(
        NDS_PLANNER_TRANSIENT_EXCEPTION_INTERVAL,
        NDSPlanningSvc.DEFAULT_TRANSIENT_EXCEPTION_PLANNING_INTERVAL);
  }

  public long getNDSPlannerUnhandledExceptionInterval() {
    return _appSettings.getLongProp(
        NDS_PLANNER_UNHANDLED_EXCEPTION_INTERVAL,
        NDSPlanningSvc.DEFAULT_UNHANDLED_EXCEPTION_PLANNING_INTERVAL);
  }

  /*
   * Remove clusters with a matching cluster-level plan that is currently executing
   * from the list of unable-plan-clusters.
   */
  @VisibleForTesting
  protected static List<Cluster> filterOutClustersWithOngoingPlan(
      final List<Cluster> pUnableToPlanClusters,
      final List<PlanSummary> pExecutingPlans,
      final Logger pLogger) {
    return pUnableToPlanClusters.stream()
        .filter(
            unableToPlanCluster -> {
              final ClusterDescription clusterDescription =
                  unableToPlanCluster.getClusterDescription();
              final Optional<PlanSummary> matchingPlan =
                  pExecutingPlans.stream()
                      .filter(
                          planSummary ->
                              planSummary.getResourceType().equals(ResourceType.CLUSTER)
                                  && clusterDescription
                                      .getName()
                                      .equals(planSummary.getClusterName().orElse(null)))
                      .findAny();

              matchingPlan.ifPresent(
                  pPlanSummary ->
                      pLogger.debug(
                          "Will not mark cluster {} to unable-to-plan because it has an ongoing"
                              + " plan.",
                          clusterDescription.getName()));

              return matchingPlan.isEmpty();
            })
        .toList();
  }

  private static Optional<Date> getEarliestCriticalReleaseTimeForMaintenance(
      final List<MaintenanceCheckResult> pMaintenanceCheckResults) {
    final Date now = new Date();
    return pMaintenanceCheckResults.stream()
        .filter(r -> r.isCriticalRelease() && r.getGroupCriticalReleaseSlot().isPresent())
        .flatMap(r -> r.getGroupCriticalReleaseSlot().stream())
        .filter(s -> s.after(now))
        .min(Date::compareTo);
  }

  @VisibleForTesting
  public static class AutomationConfigContext {

    private final AutomationConfigPublishingSvc _automationConfigSvc;
    private Optional<AutomationConfig> _automationConfig;
    private final ObjectId _groupId;

    public AutomationConfigContext(
        final AutomationConfigPublishingSvc automationConfigSvc, final ObjectId pGroupId) {
      _automationConfigSvc = automationConfigSvc;
      _groupId = pGroupId;
    }

    public Optional<AutomationConfig> getAutomationConfig() {
      if (_automationConfig == null) {
        final AutomationConfig automationConfig = _automationConfigSvc.findPublished(_groupId);
        _automationConfig = Optional.ofNullable(automationConfig);
      }

      return _automationConfig;
    }

    public AutomationConfig getAutomationConfigOrEmptyConfig() {
      return getAutomationConfig().orElse(_automationConfigSvc.getEmptyConfig(_groupId));
    }
  }

  protected static class NextPlanningDateContext {
    private final List<Cluster> _activeClusters;
    private final List<FastTenantPreAllocatedRecord> _fastTenantPreAllocatedRecords;
    private final NDSGroup _ndsGroup;
    private final Group _group;
    private final List<NDSACMECert> _acmeCertificatesForTenantClusters;
    @Nullable private final Date _minCriticalReleaseTimeSlot;
    @Nullable final Long _exceptionDuringPlanningRoundInterval;
    @Nullable private final Set<RegionName> _regionsWithSnapshotsAndSnapshotDistribution;
    private final boolean _respectProtectedHoursMaintenanceNeeded;

    public NextPlanningDateContext(
        final NDSGroup pNdsGroup,
        final Group pGroup,
        final List<FastTenantPreAllocatedRecord> pFastTenantPreAllocatedRecords,
        final List<Cluster> pClusters,
        final List<NDSACMECert> pAcmeCertificatesForTenantClusters,
        @Nullable final Date pMinCriticalReleaseTimeSlot,
        @Nullable final Long pExceptionDuringPlanningRoundInterval,
        final Set<RegionName> pRegionsWithSnapshotsAndSnapshotDistribution,
        boolean pRespectProtectedHoursMaintenanceNeeded) {
      _ndsGroup = pNdsGroup;
      _group = pGroup;
      _fastTenantPreAllocatedRecords = pFastTenantPreAllocatedRecords;
      _activeClusters = pClusters;
      _acmeCertificatesForTenantClusters = pAcmeCertificatesForTenantClusters;
      _minCriticalReleaseTimeSlot = pMinCriticalReleaseTimeSlot;
      _exceptionDuringPlanningRoundInterval = pExceptionDuringPlanningRoundInterval;
      _regionsWithSnapshotsAndSnapshotDistribution = pRegionsWithSnapshotsAndSnapshotDistribution;
      _respectProtectedHoursMaintenanceNeeded = pRespectProtectedHoursMaintenanceNeeded;
    }

    public NDSGroup getNdsGroup() {
      return _ndsGroup;
    }

    public Optional<Group> getGroup() {
      return Optional.ofNullable(_group);
    }

    public List<FastTenantPreAllocatedRecord> getFastTenantPreAllocatedRecords() {
      return _fastTenantPreAllocatedRecords;
    }

    public List<Cluster> getActiveClusters() {
      return _activeClusters;
    }

    public List<NDSACMECert> getAcmeCertificatesForTenantClusters() {
      return _acmeCertificatesForTenantClusters;
    }

    public Optional<Date> getMinCriticalReleaseTimeSlot() {
      return Optional.ofNullable(_minCriticalReleaseTimeSlot);
    }

    public Optional<Long> getExceptionDuringPlanningRoundInterval() {
      return Optional.ofNullable(_exceptionDuringPlanningRoundInterval);
    }

    public Set<RegionName> getRegionsWithSnapshotsAndSnapshotDistribution() {
      return _regionsWithSnapshotsAndSnapshotDistribution;
    }

    public boolean getRespectProtectedHoursMaintenanceNeeded() {
      return _respectProtectedHoursMaintenanceNeeded;
    }

    public Builder copy() {
      return new Builder(this);
    }

    public static class Builder {
      private List<Cluster> _activeClusters = new ArrayList<>();
      private List<FastTenantPreAllocatedRecord> _fastTenantPreAllocatedRecords = new ArrayList<>();
      private NDSGroup _ndsGroup;
      private Group _group;
      private List<NDSACMECert> _acmeCertificatesForTenantClusters = new ArrayList<>();
      private boolean _shouldRefreshNDSGroup;
      @Nullable private Date _minCriticalReleaseTimeSlot;
      @Nullable private Long _exceptionDuringPlanningRoundInterval;
      private Set<RegionName> _regionsWithSnapshotsAndSnapshotDistribution;
      private boolean _respectProtectedHoursMaintenanceNeeded;

      public Builder(final NDSGroup pNdsGroup) {
        this._ndsGroup = pNdsGroup;
      }

      public Builder(final NextPlanningDateContext pNextPlanningDateContext) {
        setActiveClusters(pNextPlanningDateContext.getActiveClusters());
        setFastTenantPreAllocatedRecords(
            pNextPlanningDateContext.getFastTenantPreAllocatedRecords());
        setNdsGroup(pNextPlanningDateContext.getNdsGroup());
        setGroup(pNextPlanningDateContext.getGroup().orElse(null));
        setAcmeCertificatesForTenantClusters(
            pNextPlanningDateContext.getAcmeCertificatesForTenantClusters());
        setMinCriticalReleaseTimeSlot(
            pNextPlanningDateContext.getMinCriticalReleaseTimeSlot().orElse(null));
        setExceptionDuringPlanningRoundInterval(
            pNextPlanningDateContext.getExceptionDuringPlanningRoundInterval().orElse(null));
        setRegionsWithSnapshotsAndSnapshotDistribution(
            pNextPlanningDateContext.getRegionsWithSnapshotsAndSnapshotDistribution());
        setRespectProtectedHoursMaintenanceNeeded(
            pNextPlanningDateContext.getRespectProtectedHoursMaintenanceNeeded());
      }

      public NextPlanningDateContext.Builder setAcmeCertificatesForTenantClusters(
          final List<NDSACMECert> pAcmeCertificatesForTenantClusters) {
        _acmeCertificatesForTenantClusters = pAcmeCertificatesForTenantClusters;
        return this;
      }

      public NextPlanningDateContext.Builder setFastTenantPreAllocatedRecords(
          final List<FastTenantPreAllocatedRecord> pFastTenantPreAllocatedRecords) {
        _fastTenantPreAllocatedRecords = pFastTenantPreAllocatedRecords;
        return this;
      }

      public NextPlanningDateContext.Builder setNdsGroup(final NDSGroup pNdsGroup) {
        _ndsGroup = pNdsGroup;
        return this;
      }

      public NextPlanningDateContext.Builder setGroup(final Group pGroup) {
        _group = pGroup;
        return this;
      }

      public NextPlanningDateContext.Builder setShouldRefreshNdsGroup(
          final boolean pShouldRefreshNDSGroup) {
        _shouldRefreshNDSGroup = pShouldRefreshNDSGroup;
        return this;
      }

      public NextPlanningDateContext.Builder setActiveClusters(List<Cluster> pClusters) {
        _activeClusters = pClusters;
        return this;
      }

      public NextPlanningDateContext.Builder setMinCriticalReleaseTimeSlot(
          final Date pMinCriticalReleaseTimeSlot) {
        _minCriticalReleaseTimeSlot = pMinCriticalReleaseTimeSlot;
        return this;
      }

      public NextPlanningDateContext.Builder setExceptionDuringPlanningRoundInterval(
          final Long pExceptionDuringPlanningRoundInterval) {
        _exceptionDuringPlanningRoundInterval = pExceptionDuringPlanningRoundInterval;
        return this;
      }

      public NextPlanningDateContext.Builder setRegionsWithSnapshotsAndSnapshotDistribution(
          final Set<RegionName> pRegionsWithSnapshotsAndSnapshotDistribution) {
        _regionsWithSnapshotsAndSnapshotDistribution = pRegionsWithSnapshotsAndSnapshotDistribution;
        return this;
      }

      public NextPlanningDateContext.Builder setRespectProtectedHoursMaintenanceNeeded(
          final boolean pRespectProtectedHoursMaintenanceNeeded) {
        _respectProtectedHoursMaintenanceNeeded = pRespectProtectedHoursMaintenanceNeeded;
        return this;
      }

      public NextPlanningDateContext build() {
        return new NextPlanningDateContext(
            _ndsGroup,
            _group,
            _fastTenantPreAllocatedRecords,
            _activeClusters,
            _acmeCertificatesForTenantClusters,
            _minCriticalReleaseTimeSlot,
            _exceptionDuringPlanningRoundInterval,
            _regionsWithSnapshotsAndSnapshotDistribution,
            _respectProtectedHoursMaintenanceNeeded);
      }
    }
  }

  public record ActionContext(Action action, AutoHealMessage reason) {
    public ActionContext(Action action) {
      this(action, AutoHealMessage.NONE);
    }

    private Pair<Type, String> getTypeAndDescriptionForAutoHealingAuditEventInternal() {
      final Action action = this.action();
      AutoHealMessage auditReason = this.reason();
      Type auditType;
      switch (action) {
        case HEAL_REPAIR:
          auditType = Type.AUTO_HEALING_REQUESTED_INSTANCE_REPLACEMENT;
          break;
        case HEAL_CANNOT_RESYNC:
          auditType = AUTO_HEALING_CANNOT_RESYNC_INTERNAL;
          break;
        case HEAL_CANNOT_REPAIR:
          auditType = AUTO_HEALING_CANNOT_REPAIR_INTERNAL;
          break;
        default:
          auditType = Type.AUTO_HEALING_ACTION;
          auditReason = AutoHealMessage.NONE;
      }

      return Pair.of(auditType, auditReason.getAuditEventMessage());
    }

    private Pair<Type, String> getTypeAndDescriptionForAutoHealingAuditEvent() {
      final Action action = this.action();
      AutoHealMessage auditReason = this.reason();
      Type auditType = Type.AUTO_HEALING_ACTION;
      switch (action) {
        case HEAL_POWER_CYCLE_CRITICAL:
          auditType = Type.AUTO_HEALING_REQUESTED_CRITICAL_INSTANCE_POWER_CYCLE;
          if (this.reason() == AutoHealMessage.NONE) {
            auditReason = AutoHealMessage.CRITICAL_POWER_CYCLE;
          }
          break;
        case HEAL_REPAIR:
          auditType = Type.AUTO_HEALING_REQUESTED_INSTANCE_REPLACEMENT;
          auditReason = AutoHealMessage.INSTANCE_REPLACEMENT_REQUESTED;
          break;
        case HEAL_RESYNC:
        case HEAL_RESYNC_FILECOPY:
        case HEAL_RESYNC_LOGICAL:
        case HEAL_RESYNC_OIS:
          auditType = Type.AUTO_HEALING_REQUESTED_NODE_RESYNC;
          auditReason = AutoHealMessage.NODE_RESYNC;
          break;
      }

      return Pair.of(auditType, auditReason.getAuditEventMessage());
    }
  }

  private record MaintenanceCheckResultWithHistory(
      MaintenanceCheckResult checkResult, NdsMaintenanceHistory history) {
    boolean hasHistory() {
      return history != null;
    }
  }

  /**
   * Gets the count of groups needing planning and updates the gauge metric cache with the results.
   * The cache metrics are used to expose the data to Prometheus.
   */
  public void populateTotalGroupsNeedPlanningCount() {
    try {
      final Map<Boolean, Map<String, Integer>> aggregateCounts =
          _ndsGroupDao.getTotalGroupsNeedPlanningGroupByHasContainerAndState(new Date());
      aggregateCounts.forEach(
          (hasContainer, stateCounts) -> {
            stateCounts.forEach(
                (state, count) -> {
                  _latestTotalGroupsNeedPlanningGaugeCacheGrouped.put(
                      Pair.of(hasContainer ? "true" : "false", state), count);
                });
          });
    } catch (final Exception pE) {
      LOG.error("Exception when calling getTotalGroupsNeedPlanningGroupByHasContainerAndState", pE);
    }
  }

  /**
   * Gets the count of plans active by status and updates the gauge metric cache with the results.
   * The cache metrics are used to expose the data to Prometheus.
   */
  public void populateTotalPlansActiveCount() {
    try {
      final Map<String, Integer> results = _planDao.getCountActivePlansByStatus();
      results.forEach(_latestTotalPlansActiveGaugeCache::put);
    } catch (final Exception pE) {
      LOG.error("Exception when calling getCountActivePlansByStatus", pE);
    }
  }

  /** Populates planner related metrics which are exposed to Prometheus. */
  public void populatePlannerPromMetrics() {
    populateTotalGroupsNeedPlanningCount();
    populateTotalPlansActiveCount();
  }

  /** Determines if a group has any active Stream Processing private networking configurations. */
  public boolean hasStreamsPrivateNetworking(ObjectId groupId) {
    final List<StreamsPrivateLink> privateLinks = _streamsPrivateLinkDao.findByGroupId(groupId);
    final Optional<StreamsTransitGateway> tgwResourceShares =
        _streamsTransitGatewayResourceSharesDao.findByGroupId(groupId);
    final List<VPCPeeringConnection> vpcPeeringConnections =
        _vpcPeeringConnectionDao.findByGroupId(groupId);

    if (tgwResourceShares.isPresent() && !tgwResourceShares.get().getResourceShares().isEmpty()
        || !privateLinks.isEmpty()
        || !vpcPeeringConnections.isEmpty()) {
      return true;
    }
    return false;
  }
}
