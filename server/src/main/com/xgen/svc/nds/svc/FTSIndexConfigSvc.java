package com.xgen.svc.nds.svc;

import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.DUPLICATE_KEY_ERROR_CODE;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoException;
import com.mongodb.MongoWriteException;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.ResourceId;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.alerts.defaults._public.svc.DefaultAlertConfigSvc;
import com.xgen.cloud.atm.core._public.svc.AutomationConfigQuerySvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfo._public.model.EventSource;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.MongotTemplate;
import com.xgen.cloud.externalanalytics._public.model.SearchAnalyzerDefinitionUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchIndexCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchIndexDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchIndexUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.fts.activity._public.audit.FTSIndexAudit;
import com.xgen.cloud.fts.activity._public.audit.FTSIndexAudit.IndexDetails;
import com.xgen.cloud.fts.activity._public.audit.FTSIndexAudit.Type;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.mongod._public.model.MongoDbCollectionInfo;
import com.xgen.cloud.mongod._public.model.MongoDbCollectionInfo.DbView;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.SearchTenantInstanceSize;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.fts._private.dao.FTSIndexConfigDao;
import com.xgen.cloud.nds.fts._private.dao.FTSIndexConfigStatsDao;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSIndexConfig;
import com.xgen.cloud.nds.fts._public.model.FTSIndexConfigStats;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostDetailedStatuses;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSIndexStatus;
import com.xgen.cloud.nds.fts._public.model.FTSIndexStatusMap;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndex;
import com.xgen.cloud.nds.fts._public.model.IndexedView;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.BasicDBObjectUtil;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.TenantClusterInfo;
import com.xgen.cloud.nds.project._public.svc.INDSClusterSvc;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.search.common._public.svc.SearchClusterConnectionSvc;
import com.xgen.cloud.search.common._public.util.BsonByteCounterOutput;
import com.xgen.cloud.search.common._public.util.HostnameUtil;
import com.xgen.cloud.search.common._public.util.SearchPromMetrics;
import com.xgen.cloud.search.util._public.version.MongotVersion;
import com.xgen.cloud.search.validation._public.validation.IndexedViewTransformer;
import com.xgen.cloud.search.validation._public.validation.ViewValidator;
import com.xgen.svc.nds.model.ui.search.SearchUpsertIndexView.FTSCreateSynonymsCollectionView;
import com.xgen.svc.nds.model.ui.search.SearchUpsertIndexView.FTSCreateSynonymsCollectionView.ServerAction;
import com.xgen.svc.nds.model.ui.search.TextSearchIndexView;
import com.xgen.svc.nds.sampleDatasetLoad.svc.NDSSampleDatasetLoadSvc;
import com.xgen.svc.nds.util.FTSAnalyzerUtil;
import com.xgen.svc.nds.util.FTSIndexConfigUtil;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.prometheus.client.Counter;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.BsonBinaryWriter;
import org.bson.codecs.EncoderContext;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This class contains some logic around Atlas Search. Try to avoid adding new methods here. If the
 * caller does not already have an instance of this class, consider a new Svc-style class that only
 * has that logic and those dependencies. See the related classes for examples.
 *
 * @see com.xgen.cloud.nds.fts._public.svc.SearchIndexConfigSvc
 * @see com.xgen.svc.nds.svc.SearchForGroupAlertSvc
 * @see com.xgen.svc.nds.svc.MongotConfigSvc
 */
@Singleton
public class FTSIndexConfigSvc {

  // Index feature version should be 4 if mongot version is 1.54 or more
  public static final MongotVersion MIN_MONGOT_VERSION_WITH_FEATURE_4 =
      MongotVersion.parseBackwardsCompatible("1.54.0.0");

  // In versions lower than this, MMS will pretend that the user-provided collection
  // is a root collection. mongot expects that the user-provided collection is a root collection;
  // if it's a view, mongot rejects the index definition.
  public static final VersionUtils.Version MIN_VERSION_FOR_VIEW_SUPPORT =
      VersionUtils.parse("8.0.0");

  public static final int OLDER_INDEX_FEATURE_VERSION = 3;
  public static final int LATEST_INDEX_FEATURE_VERSION = 4;

  public static final int MAX_CONFIG_SIZE_IN_BYTES = 10_000_000;

  private static final Logger LOG = LoggerFactory.getLogger(FTSIndexConfigSvc.class);

  private final FTSIndexConfigDao _ftsIndexConfigDao;
  private final FTSIndexConfigStatsDao _ftsIndexConfigStatsDao;
  private final NDSGroupDao _ndsGroupDao;
  private final INDSClusterSvc _ndsClusterSvc;
  private final AuditSvc _auditSvc;
  private final GroupDao _groupDao;
  private final SegmentEventSvc _segmentEventSvc;
  private final ReplicaSetHardwareSvc _replicaSetHardwareSvc;
  private final DefaultAlertConfigSvc _defaultAlertConfigSvc;
  private final FTSIndexConfigUtil _ftsIndexConfigUtil;
  private final NDSSampleDatasetLoadSvc _ndsSampleDatasetLoadSvc;
  private final AutomationConfigQuerySvc _automationConfigQuerySvc;
  private final AppSettings _appSettings;
  private final IndexedViewTransformer _indexedViewTransformer;
  private final SearchClusterConnectionSvc _searchClusterConnectionSvc;

  private static final Counter SEARCH_TENANT_INDEX_MIGRATION_STARTED_COUNTER =
      PromMetricsSvc.registerCounter(
          SearchPromMetrics.getMetricNameWithSearchPrefix("tenant_index_migration_started_total"),
          "Number of search indexes where the tenant migration process was started",
          "groupId");

  private static final Counter SEARCH_TENANT_INDEX_MIGRATION_COMPLETED_COUNTER =
      PromMetricsSvc.registerCounter(
          SearchPromMetrics.getMetricNameWithSearchPrefix("tenant_index_migration_completed_total"),
          "Number of search indexes where the tenant migration process was completed",
          "groupId",
          "migrationType");

  @VisibleForTesting public static final String SEARCH_INDEX_DELETE_LABEL = "deletionType";
  @VisibleForTesting public static final String SEARCH_INDEX_ASYNC_DELETE_VALUE = "ASYNC";
  @VisibleForTesting public static final String SEARCH_INDEX_SYNC_DELETE_VALUE = "SYNC";

  @VisibleForTesting
  public static final String SEARCH_INDEX_DELETE_COUNTER_NAME = "mms_nds_search_index_delete_total";

  private static final Counter SEARCH_INDEX_DELETE_COUNTER =
      PromMetricsSvc.registerCounter(
          SEARCH_INDEX_DELETE_COUNTER_NAME,
          "Number of search indexes deleted",
          SEARCH_INDEX_DELETE_LABEL);

  @Inject
  public FTSIndexConfigSvc(
      final FTSIndexConfigDao ftsIndexConfigDao,
      final FTSIndexConfigStatsDao ftsIndexConfigStatsDao,
      final NDSGroupDao ndsGroupDao,
      final INDSClusterSvc ndsClusterSvc,
      final AuditSvc auditSvc,
      final GroupDao groupDao,
      final SegmentEventSvc segmentEventSvc,
      final ReplicaSetHardwareSvc gSetHardwareSvc,
      final DefaultAlertConfigSvc defaultAlertConfigSvc,
      final FTSIndexConfigUtil ftsIndexConfigUtil,
      final NDSSampleDatasetLoadSvc ndsSampleDatasetLoadSvc,
      final AutomationConfigQuerySvc automationConfigQuerySvc,
      final AppSettings appSettings,
      final IndexedViewTransformer indexedViewTransformer,
      final SearchClusterConnectionSvc searchClusterConnectionSvc) {
    _ftsIndexConfigDao = ftsIndexConfigDao;
    _ftsIndexConfigStatsDao = ftsIndexConfigStatsDao;
    _ndsGroupDao = ndsGroupDao;
    _ndsClusterSvc = ndsClusterSvc;
    _auditSvc = auditSvc;
    _groupDao = groupDao;
    _segmentEventSvc = segmentEventSvc;
    _replicaSetHardwareSvc = gSetHardwareSvc;
    _defaultAlertConfigSvc = defaultAlertConfigSvc;
    _ftsIndexConfigUtil = ftsIndexConfigUtil;
    _ndsSampleDatasetLoadSvc = ndsSampleDatasetLoadSvc;
    _automationConfigQuerySvc = automationConfigQuerySvc;
    _appSettings = appSettings;
    _indexedViewTransformer = indexedViewTransformer;
    _searchClusterConnectionSvc = searchClusterConnectionSvc;
  }

  private void validateConfigSmallerThanMax(FTSIndexConfig indexConfig) throws SvcException {
    var serializable = indexConfig.copyWithoutStats().toDBObject();
    var bsonOutput = new BsonByteCounterOutput();
    _ftsIndexConfigDao
        .getCodecRegistry()
        .get(BasicDBObject.class)
        .encode(new BsonBinaryWriter(bsonOutput), serializable, EncoderContext.builder().build());
    if (bsonOutput.getPosition() > MAX_CONFIG_SIZE_IN_BYTES) {
      if (isFeatureFlagEnabled(
          FeatureFlag.ATLAS_SEARCH_ENFORCE_MAX_INDEX_SIZE,
          _appSettings,
          null,
          _groupDao.findById(indexConfig.getGroupId()))) {
        throw new SvcException(NDSErrorCode.FTS_TOO_MANY_INDEXES);
      } else {
        LOG.info(
            "Max config size of {} exceeded limit of {} and would throw {};  {} {}",
            bsonOutput.getPosition(),
            MAX_CONFIG_SIZE_IN_BYTES,
            NDSErrorCode.FTS_TOO_MANY_INDEXES,
            kv("groupId", indexConfig.getGroupId()),
            kv("clusterName", indexConfig.getClusterName()));
      }
    }
  }

  public FTSIndexConfig ensureFTSIndexConfig(final ObjectId groupId, final String clusterName)
      throws SvcException {

    final Optional<FTSIndexConfig> ftsIndexConfigOptional = getFTSIndexConfig(groupId, clusterName);

    if (ftsIndexConfigOptional.isPresent()) {
      return ftsIndexConfigOptional.get();
    }

    verifyGroupAndCluster(groupId, clusterName);

    try {
      _ftsIndexConfigDao.create(groupId, clusterName);
    } catch (MongoWriteException e) {
      if (e.getError().getCode() != DUPLICATE_KEY_ERROR_CODE) {
        throw e;
      }
    }
    return getFTSIndexConfig(groupId, clusterName).get();
  }

  public ClusterDescription verifyGroupAndCluster(final ObjectId groupId, final String clusterName)
      throws SvcException {
    _ndsGroupDao.find(groupId).orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
    return fetchSearchPermittedClusterDescription(groupId, clusterName);
  }

  public boolean isFTSEnabledForTenant(
      final ObjectId mtmGroupId, final String mtmClusterName, final String tenantId) {
    return getMTMIndexesWithLegacyStats(mtmGroupId, mtmClusterName, tenantId).count() > 0;
  }

  public Optional<FTSIndexConfig> getFTSIndexConfig(
      final ObjectId groupId, final String clusterName) {
    return _ftsIndexConfigDao.find(groupId, clusterName);
  }

  public Optional<FTSIndexConfig> getFTSIndexConfigProjectedByTenant(
      final ObjectId mtmGroupId, final String mtmClusterName, final String tenantId) {
    return _ftsIndexConfigDao.findProjectedByTenant(mtmGroupId, mtmClusterName, tenantId);
  }

  public List<BasicDBObject> getAnalyzers(final ObjectId groupId, final String clusterName) {
    return getFTSIndexConfig(groupId, clusterName)
        .map(FTSIndexConfig::getAnalyzers)
        .orElse(Collections.emptyList());
  }

  public void setAnalyzers(
      final ObjectId groupId,
      final String clusterName,
      final List<BasicDBObject> analyzers,
      final AuditInfo auditInfo)
      throws SvcException {
    FTSAnalyzerUtil.validate(analyzers);
    ensureFTSIndexConfig(groupId, clusterName);
    _ftsIndexConfigDao.setAnalyzers(groupId, clusterName, analyzers);
    submitSegmentEvent(groupId, clusterName, auditInfo);
  }

  public void setIndexesAndAnalyzers(
      final ObjectId groupId,
      final String clusterName,
      final List<BasicDBObject> analyzers,
      final List<FTSIndex> ftsIndices)
      throws SvcException {
    final List<IndexDetails> indexDetails =
        ftsIndices.stream().map(IndexDetails::new).collect(Collectors.toList());
    try {
      final Group group = _groupDao.findById(groupId);
      final var cluster = fetchSearchPermittedClusterDescription(groupId, clusterName);
      final FTSIndexConfig existingIndexConfig = ensureFTSIndexConfig(groupId, clusterName);
      FTSAnalyzerUtil.validate(analyzers);
      final List<FTSIndex> newFTSIndices = new ArrayList<>();
      for (FTSIndex ftsIndex : ftsIndices) {
        _ftsIndexConfigUtil.validateFTSIndex(group, cluster, ftsIndex);
        // set the definitionVersion to the latest from existingIndexConfig
        final Optional<FTSIndex> existingFTSIndex =
            existingIndexConfig.getIndexByRootCollection(
                ftsIndex.getDatabase(),
                ftsIndex.getLastObservedCollectionName(),
                ftsIndex.getName());

        long newVersion =
            existingFTSIndex
                // increment definition version if index exists
                .map(index -> index.getDefinitionVersion().orElse(0L) + 1)
                // set definition version to 0 because we are adding a new index
                .orElse(0L);

        newFTSIndices.add(ftsIndex.copy().setDefinitionVersion(newVersion).build());
      }

      _defaultAlertConfigSvc.addFTSIndexAlertConfigs(group);
      _ftsIndexConfigDao.setIndexesAndAnalyzers(groupId, clusterName, newFTSIndices, analyzers);
      saveAuditEvent(
          groupId,
          clusterName,
          indexDetails,
          Type.FTS_INDEXES_RESTORED,
          AuditInfoHelpers.fromSystem());
      // Run plan immediately if bringing up/down mongot.
      if (ftsIndices.isEmpty() ^ existingIndexConfig.getIndexesForAllTypes().isEmpty()) {
        _ndsClusterSvc.setNeedsPublishDateForCluster(groupId, clusterName);
        _ndsGroupDao.setPlanASAP(groupId);
      }
    } catch (SvcException e) {
      saveAuditEvent(
          groupId,
          clusterName,
          indexDetails,
          Type.FTS_INDEXES_RESTORE_FAILED,
          AuditInfoHelpers.fromSystem());
      throw e;
    }
    logFTSIndexConfigSize(groupId, clusterName);
  }

  @WithSpan
  public void addFTSIndex(
      final ObjectId groupId,
      final String clusterName,
      final FTSIndex ftsIndex,
      final AuditInfo auditInfo)
      throws SvcException {
    var group = _groupDao.findById(groupId);
    var clusterDescription = fetchSearchPermittedClusterDescription(groupId, clusterName);
    addFTSIndex(group, clusterDescription, ftsIndex, auditInfo, Optional.empty());
  }

  @WithSpan
  public void addFTSIndex(
      final ObjectId groupId,
      final String clusterName,
      final FTSIndex ftsIndex,
      final AuditInfo auditInfo,
      final boolean isPreloadedSampleSearchIndex)
      throws SvcException {
    var group = _groupDao.findById(groupId);
    var clusterDescription = fetchSearchPermittedClusterDescription(groupId, clusterName);
    addFTSIndex(
        group,
        clusterDescription,
        ftsIndex,
        auditInfo,
        Optional.empty(),
        isPreloadedSampleSearchIndex);
  }

  @WithSpan
  public void addFTSIndex(
      final Group group,
      final ClusterDescription clusterDescription,
      final FTSIndex inputIndex,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    addFTSIndex(group, clusterDescription, inputIndex, auditInfo, editor, false);
  }

  @WithSpan
  public void addFTSIndex(
      final Group group,
      final ClusterDescription clusterDescription,
      final FTSIndex inputIndex,
      final AuditInfo auditInfo,
      final Optional<String> editor,
      final boolean isPreloadedSampleSearchIndex)
      throws SvcException {
    var groupId = group.getId();
    var clusterName = clusterDescription.getName();
    FTSIndex transformedIndex =
        validateAndTransform(group, clusterDescription, inputIndex, auditInfo);

    // Add an alert config to the group when the user add the first FTS index
    _defaultAlertConfigSvc.addFTSIndexAlertConfigs(group);
    final FTSIndexConfig ftsIndexConfig = ensureFTSIndexConfig(groupId, clusterName);
    validateConfigSmallerThanMax(ftsIndexConfig);
    throwIfNotUniqueName(transformedIndex, ftsIndexConfig);
    saveAuditEvent(
        groupId, clusterName, transformedIndex, FTSIndexAudit.Type.FTS_INDEX_CREATED, auditInfo);
    FTSIndex newIndex = updateIndexFeatureVersion(groupId, transformedIndex);
    newIndex = appendSimilarityMethodIfNeeded(newIndex, groupId);

    final int totalIndexes = _ftsIndexConfigDao.addFTSIndex(groupId, clusterName, newIndex);
    if (totalIndexes == 1) {
      _ndsClusterSvc.setNeedsPublishDateForCluster(groupId, clusterName);
      _ndsGroupDao.setPlanASAP(groupId);
    }
    submitSegmentIndexEvent(
        SearchIndexCreatedEvent.EVENT_TYPE,
        groupId,
        clusterName,
        Optional.of(clusterDescription.getUniqueId()),
        auditInfo,
        transformedIndex.getActualType(),
        looksLikeSampleDataNamespace(transformedIndex),
        editor,
        Optional.of(isPreloadedSampleSearchIndex));
    logFTSIndexConfigSize(groupId, clusterName);
  }

  /**
   * Return the list of collections in the specified group, cluster and database.
   *
   * @param groupId: Group to check for collections.
   * @param clusterName: Cluster name to check for collections.
   * @param databaseName: Database to check for collections.
   * @param auditInfo: Audit information for tracking this operation.
   * @return List of collections in the specified group, cluster and database. Returns null if a
   *     problem occurred fetching collections.
   */
  @VisibleForTesting
  @Nullable
  public List<MongoDbCollectionInfo> getCollections(
      ObjectId groupId, String clusterName, String databaseName, AuditInfo auditInfo) {
    List<MongoDbCollectionInfo> collections;
    try {
      collections =
          _searchClusterConnectionSvc.listCollections(
              groupId, clusterName, databaseName, auditInfo);
    } catch (Exception e) {
      LOG.info(
          "An exception occurred when calling listCollections: {} {} {} {}",
          kv("groupId", groupId),
          kv("clusterName", clusterName),
          kv("database", databaseName),
          kv("exception", e.getMessage()));
      return null;
    }
    return collections;
  }

  private FTSIndex validateAndTransform(
      Group group, ClusterDescription clusterDescription, FTSIndex inputIndex, AuditInfo auditInfo)
      throws SvcException {
    _ftsIndexConfigUtil.validateFTSIndex(group, clusterDescription, inputIndex);

    // we only query the user's database if the Feature Flag is enabled
    if (!isFeatureFlagEnabled(FeatureFlag.ATLAS_SEARCH_RESOLVE_VIEWS, _appSettings, null, group)) {
      return inputIndex;
    }

    // we can't query if audit credentials are missing
    if (auditInfo == null || auditInfo.getEventSource() == EventSource.DATABASE_COMMAND) {
      return inputIndex;
    }

    // we don't need to query if they already defined the view information
    if (inputIndex.getView().isPresent()) {
      // ... but we do need to validate it
      validateViewSupport(clusterDescription, inputIndex.getView().get().name());
      ViewValidator.validate(inputIndex.getView().orElseThrow().pipelineDecoded());
      return inputIndex;
    }

    List<MongoDbCollectionInfo> collections =
        getCollections(
            group.getId(), clusterDescription.getName(), inputIndex.getDatabase(), auditInfo);
    if (collections == null) {
      return inputIndex;
    }

    boolean isView =
        collections.stream()
            .filter(coll -> coll.name().equals(inputIndex.getLastObservedCollectionName()))
            .anyMatch(coll -> coll instanceof DbView);
    if (isView) {
      validateViewSupport(clusterDescription, inputIndex.getLastObservedCollectionName());
    }

    return _indexedViewTransformer.apply(inputIndex, collections);
  }

  @WithSpan
  public List<FTSIndex> createSearchIndexManagementCommand(
      final ObjectId groupId,
      final String clusterName,
      final List<FTSIndex> inputIndexes,
      final AuditInfo auditInfo)
      throws SvcException {
    var clusterDescription = fetchSearchPermittedClusterDescription(groupId, clusterName);

    if (clusterDescription.isFlexOrSharedTenantCluster()) {
      return addTenantFTSIndexes(
          groupId, clusterDescription, inputIndexes, auditInfo, Optional.empty());
    } else {
      var group = _groupDao.findById(groupId);
      return addFTSIndexes(group, clusterDescription, inputIndexes, auditInfo);
    }
  }

  private List<FTSIndex> addFTSIndexes(
      final Group group,
      final ClusterDescription clusterDescription,
      final List<FTSIndex> inputIndexes,
      final AuditInfo auditInfo)
      throws SvcException {
    var indexesToReturn = new ArrayList<FTSIndex>();
    var clusterName = clusterDescription.getName();
    for (final FTSIndex index : inputIndexes) {
      indexesToReturn.add(validateAndTransform(group, clusterDescription, index, auditInfo));
    }

    // Add an alert config to the group when the user add the first FTS index
    _defaultAlertConfigSvc.addFTSIndexAlertConfigs(group);
    final FTSIndexConfig ftsIndexConfig = ensureFTSIndexConfig(group.getId(), clusterName);
    validateConfigSmallerThanMax(ftsIndexConfig);
    checkForDuplicateIndexNames(indexesToReturn);
    replaceFTSIndexIfExists(
        indexesToReturn,
        index ->
            ftsIndexConfig.getIndexByRootCollection(
                index.getDatabase(), index.getCollectionUUID().get(), index.getName()));

    final List<FTSIndex> indexesToAdd =
        indexesToReturn.stream()
            .filter(ftsIndex -> ftsIndex.getCreateDate().isEmpty())
            .map(index -> updateIndexFeatureVersion(group.getId(), index))
            .map(index -> appendSimilarityMethodIfNeeded(index, group.getId()))
            .collect(Collectors.toList());

    if (!indexesToAdd.isEmpty()) {
      final List<FTSIndexAudit.IndexDetails> indexDetails =
          indexesToAdd.stream().map(FTSIndexAudit.IndexDetails::new).collect(Collectors.toList());

      saveAuditEvent(
          group.getId(),
          clusterName,
          indexDetails,
          FTSIndexAudit.Type.FTS_INDEX_CREATED,
          auditInfo);

      // If there are no indexes to add, skip building the query
      final int totalIndexes =
          _ftsIndexConfigDao.addFTSIndexes(group.getId(), clusterName, indexesToAdd);
      if (totalIndexes == indexesToAdd.size()) {
        _ndsClusterSvc.setNeedsPublishDateForCluster(group.getId(), clusterName);
        _ndsGroupDao.setPlanASAP(group.getId());
      }

      for (FTSIndex index : indexesToAdd) {
        submitSegmentIndexEvent(
            SearchIndexCreatedEvent.EVENT_TYPE,
            group.getId(),
            clusterName,
            Optional.of(clusterDescription.getUniqueId()),
            auditInfo,
            index.getActualType(),
            looksLikeSampleDataNamespace(index),
            Optional.empty(),
            Optional.empty());
      }
    }

    logFTSIndexConfigSize(group.getId(), clusterName);

    return indexesToReturn;
  }

  private static void throwDuplicateIndexException(FTSIndex index) throws SvcException {
    throw new SvcException(
        NDSErrorCode.FTS_DUPLICATE_INDEX, index.getName(), index.getLastObservedCollectionName());
  }

  private static void throwIfNotUniqueName(FTSIndex index, FTSIndexConfig ftsIndexConfig)
      throws SvcException {
    final Optional<FTSIndex> ftsIndexOptional =
        ftsIndexConfig.getIndexByRootCollection(
            index.getDatabase(), index.getLastObservedCollectionName(), index.getName());
    if (ftsIndexOptional.isPresent()) {
      throwDuplicateIndexException(index);
    }
  }

  private static void checkForDuplicateIndexNames(List<FTSIndex> indexList) throws SvcException {
    Set<String> indexNames = new HashSet<>();
    for (FTSIndex index : indexList) {
      // Make sure index names in this request are unique
      if (!indexNames.add(index.getName())) {
        throwDuplicateIndexException(index);
      }
    }
  }

  private static void replaceFTSIndexIfExists(
      List<FTSIndex> indexList, Function<FTSIndex, Optional<FTSIndex>> findByIndexFunction)
      throws SvcException {

    for (ListIterator<FTSIndex> iterator = indexList.listIterator(); iterator.hasNext(); ) {
      FTSIndex index = iterator.next();
      Optional<FTSIndex> ftsIndexOptional = findByIndexFunction.apply(index);

      if (ftsIndexOptional.isPresent()) {
        FTSIndex ftsIndex = ftsIndexOptional.get();
        if (!areIndexesDefinitionEqual(ftsIndex, index)
            || !Objects.equals(ftsIndex.getView(), index.getView())) {
          throwDuplicateIndexException(index);
        } else {
          iterator.set(ftsIndex);
        }
      }
    }
  }

  private static boolean areIndexesDefinitionEqual(FTSIndex oldIndex, FTSIndex newIndex) {
    return oldIndex.toIndexDefinition().equals(newIndex.toIndexDefinition());
  }

  private TenantClusterInfo fetchTenantClusterInfo(
      final ObjectId tenantGroupId, final String tenantClusterName) throws SvcException {
    final var tenantClusterInfo =
        _ndsClusterSvc.fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    if (tenantClusterInfo.serverlessTenantCluster()) {
      throw new SvcException(NDSErrorCode.SERVERLESS_INSTANCE_FTS_NOT_SUPPORTED);
    }

    return tenantClusterInfo;
  }

  public List<FTSIndex> getTenantFTSIndexes(
      final ObjectId tenantGroupId, final String tenantClusterName) throws SvcException {
    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    final List<FTSIndex> mtmIndexes = getMTMIndexesWithLegacyStats(tenantInfo).toList();

    if (mtmIndexes.isEmpty()) {
      return new ArrayList<>();
    }

    final Map<String, String> hostMap =
        mtmTenantHostMapping(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), tenantGroupId, tenantClusterName);

    return maybeMergeIndexStats(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), mtmIndexes)
        .stream()
        .map(ftsIndex -> mtmIndextoTenantIndex(ftsIndex, hostMap))
        .collect(Collectors.toList());
  }

  public void addTenantFTSIndex(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final FTSIndex ftsIndex,
      final AuditInfo auditInfo)
      throws SvcException {
    addTenantFTSIndex(tenantGroupId, tenantClusterName, ftsIndex, auditInfo, Optional.empty());
  }

  public void addTenantFTSIndex(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final FTSIndex ftsIndex,
      final AuditInfo auditInfo,
      final boolean isPreloadedSampleSearchIndex)
      throws SvcException {
    addTenantFTSIndex(
        tenantGroupId,
        tenantClusterName,
        ftsIndex,
        auditInfo,
        Optional.empty(),
        isPreloadedSampleSearchIndex);
  }

  public void addTenantFTSIndex(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final FTSIndex index,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    addTenantFTSIndex(tenantGroupId, tenantClusterName, index, auditInfo, editor, false);
  }

  public void addTenantFTSIndex(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final FTSIndex index,
      final AuditInfo auditInfo,
      final Optional<String> editor,
      final boolean isPreloadedSampleSearchIndex)
      throws SvcException {
    final Group group = _groupDao.findById(tenantGroupId);
    final var cluster = fetchSearchPermittedClusterDescription(tenantGroupId, tenantClusterName);

    FTSIndex ftsIndex = updateIndexFeatureVersion(tenantGroupId, index);
    ftsIndex = validateAndTransform(group, cluster, ftsIndex, auditInfo);

    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    if (willTenantExceedMaximumIndexes(tenantInfo, 1)) {
      throw new SvcException(NDSErrorCode.MAXIMUM_INDEXES_FOR_TENANT_EXCEEDED);
    }

    // construct the actual index
    final FTSIndex mtmIndex = tenantIndexToMTMIndex(ftsIndex, tenantInfo.tenantId());

    // Add an alert config to the group when the user add the first FTS index
    _defaultAlertConfigSvc.addFTSIndexAlertConfigs(group);

    final FTSIndexConfig ftsIndexConfig =
        ensureFTSIndexConfig(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
    final Optional<FTSIndex> ftsIndexOptional =
        ftsIndexConfig.getTenantIndexByRootCollection(
            tenantInfo.tenantId(),
            mtmIndex.getDatabase(),
            mtmIndex.getLastObservedCollectionName(),
            mtmIndex.getName());
    if (ftsIndexOptional.isPresent()) {
      throwDuplicateIndexException(ftsIndex);
    }

    saveAuditEvent(
        tenantGroupId,
        tenantClusterName,
        ftsIndex,
        FTSIndexAudit.Type.FTS_INDEX_CREATED,
        auditInfo);
    final int totalIndexes =
        _ftsIndexConfigDao.addTenantFTSIndex(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), tenantInfo.tenantId(), mtmIndex);
    if (totalIndexes == 1) {
      _ndsClusterSvc.setNeedsPublishDateForCluster(
          tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
      _ndsGroupDao.setPlanASAP(tenantInfo.mtmGroupId());
    }

    submitSegmentIndexEvent(
        SearchIndexCreatedEvent.EVENT_TYPE,
        tenantGroupId,
        tenantClusterName,
        Optional.of(cluster.getUniqueId()),
        auditInfo,
        ftsIndex.getActualType(),
        looksLikeSampleDataNamespace(ftsIndex),
        editor,
        Optional.of(isPreloadedSampleSearchIndex));
    logFTSIndexConfigSize(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
    logNumberOfTenantFTSIndexes(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
  }

  // Used by search index management commands. Assumes all the indexes are defined in the same
  // collection, not ready for general usages.
  @VisibleForTesting
  public List<FTSIndex> addTenantFTSIndexes(
      final ObjectId tenantGroupId,
      final ClusterDescription cluster,
      final List<FTSIndex> inputIndexes,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    final Group group = _groupDao.findById(tenantGroupId);
    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, cluster.getName());
    var mtmIndexList = new ArrayList<FTSIndex>();

    for (final FTSIndex ftsIndex : inputIndexes) {
      FTSIndex tenantIndex =
          validateAndTransform(
              group, cluster, updateIndexFeatureVersion(tenantGroupId, ftsIndex), auditInfo);
      FTSIndex mtmIndex = tenantIndexToMTMIndex(tenantIndex, tenantInfo.tenantId());
      mtmIndexList.add(mtmIndex);
    }

    _defaultAlertConfigSvc.addFTSIndexAlertConfigs(group);

    final FTSIndexConfig ftsIndexConfig =
        ensureFTSIndexConfig(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
    checkForDuplicateIndexNames(mtmIndexList);
    replaceFTSIndexIfExists(
        mtmIndexList,
        index ->
            ftsIndexConfig.getTenantIndexByRootCollection(
                tenantInfo.tenantId(),
                index.getDatabase(),
                index.getCollectionUUID().get(),
                index.getName()));

    final List<FTSIndex> mtmIndexesToAdd =
        mtmIndexList.stream()
            .filter(ftsIndex -> ftsIndex.getCreateDate().isEmpty())
            .collect(Collectors.toList());

    if (willTenantExceedMaximumIndexes(tenantInfo, mtmIndexesToAdd.size())) {
      throw new SvcException(NDSErrorCode.MAXIMUM_INDEXES_FOR_TENANT_EXCEEDED);
    }

    final List<FTSIndex> indexesToReturn =
        mtmIndexList.stream()
            .map(
                index ->
                    index
                        .copy()
                        .setDatabase(mtmDatabaseToTenantDatabase(index.getDatabase()))
                        .build())
            .collect(Collectors.toList());

    if (!mtmIndexesToAdd.isEmpty()) {
      final List<FTSIndexAudit.IndexDetails> indexDetails =
          mtmIndexesToAdd.stream()
              .map(
                  index ->
                      index
                          .copy()
                          .setDatabase(mtmDatabaseToTenantDatabase(index.getDatabase()))
                          .build())
              .map(FTSIndexAudit.IndexDetails::new)
              .collect(Collectors.toList());

      saveAuditEvent(
          tenantGroupId,
          cluster.getName(),
          indexDetails,
          FTSIndexAudit.Type.FTS_INDEX_CREATED,
          auditInfo);
      final int totalIndexes =
          _ftsIndexConfigDao.addTenantFTSIndexes(
              tenantInfo.mtmGroupId(),
              tenantInfo.mtmClusterName(),
              tenantInfo.tenantId(),
              mtmIndexesToAdd);
      if (totalIndexes == mtmIndexesToAdd.size()) {
        _ndsClusterSvc.setNeedsPublishDateForCluster(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
        _ndsGroupDao.setPlanASAP(tenantInfo.mtmGroupId());
      }

      for (FTSIndex index : indexesToReturn) {
        submitSegmentIndexEvent(
            SearchIndexCreatedEvent.EVENT_TYPE,
            tenantGroupId,
            cluster.getName(),
            Optional.of(cluster.getUniqueId()),
            auditInfo,
            index.getActualType(),
            looksLikeSampleDataNamespace(index),
            editor,
            Optional.empty());
      }
    }

    logFTSIndexConfigSize(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
    logNumberOfTenantFTSIndexes(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());

    return indexesToReturn;
  }

  @VisibleForTesting
  protected boolean willTenantExceedMaximumIndexes(
      final TenantClusterInfo tenantInfo, final int numberOfIndexesToAdd) {
    int numTenantIndexes = (int) getMTMIndexesWithLegacyStats(tenantInfo).count();

    final int maxIndex =
        ((SearchTenantInstanceSize) tenantInfo.onlyInstanceSize().orElseThrow()).maxTenantIndexes();

    return numTenantIndexes + numberOfIndexesToAdd > maxIndex;
  }

  private Optional<ClusterDescription> fetchClusterDescription(
      ObjectId groupId, String clusterName) {
    return _ndsClusterSvc.getActiveClusterDescription(groupId, clusterName);
  }

  public ClusterDescription fetchSearchPermittedClusterDescription(
      ObjectId groupId, String clusterName) throws SvcException {
    var clusterDescription =
        fetchClusterDescription(groupId, clusterName)
            .orElseThrow(
                () -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, clusterName, groupId));

    if (clusterDescription.isServerlessTenantCluster()) {
      throw new SvcException(NDSErrorCode.SERVERLESS_INSTANCE_FTS_NOT_SUPPORTED);
    }

    if (clusterDescription.isPaused()) {
      throw new SvcException(NDSErrorCode.SEARCH_ON_PAUSED_CLUSTER_NOT_SUPPORTED);
    }

    return clusterDescription;
  }

  @WithSpan
  public boolean isSharedOrFlexTenantCluster(final ObjectId groupId, final String clusterName) {
    return fetchClusterDescription(groupId, clusterName)
        .map(ClusterDescription::isFlexOrSharedTenantCluster)
        .orElse(false);
  }

  @WithSpan
  public boolean isServerlessTenantCluster(final ObjectId groupId, final String clusterName) {
    return fetchClusterDescription(groupId, clusterName)
        .map(ClusterDescription::isServerlessTenantCluster)
        .orElse(false);
  }

  /**
   * Request to delete a tenant index.
   *
   * @param groupId: Group that contains the index.
   * @param clusterName: Cluster that contains the index.
   * @param indexId: ID of index to delete.
   * @param auditInfo: Audit info synonyms collections.
   */
  @WithSpan
  public void requestDeleteFTSIndex(
      final ObjectId groupId,
      final String clusterName,
      final ObjectId indexId,
      final AuditInfo auditInfo)
      throws SvcException {
    final FTSIndex existingIndex =
        getFTSIndexConfig(groupId, clusterName)
            .flatMap(ftsIndexConfig -> ftsIndexConfig.getFTSIndex(indexId))
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.NOT_FOUND, String.format("Search index %s", indexId)));
    if (existingIndex.getDeleteRequestedDate().isPresent()) {
      throw new SvcException(NDSErrorCode.FTS_INDEX_DELETION_ALREADY_REQUESTED);
    }

    submitAuditAndSegmentIndexEvents(groupId, clusterName, existingIndex, auditInfo);

    if (syncIndexDeletionEnabled(groupId)) {
      countSynchronousDeletion(existingIndex, groupId);
      final int totalIndexes =
          _ftsIndexConfigDao.deleteFTSIndex(groupId, clusterName, existingIndex.getIndexId());
      if (totalIndexes == 0) {
        _ndsClusterSvc.setNeedsPublishDateForCluster(groupId, clusterName);
        _ndsGroupDao.setPlanASAP(groupId);
        logFTSIndexConfigSize(groupId, clusterName);
      }
    } else {
      // If sync delete is not enabled, perform an async delete.
      countAsynchronousDeletion(existingIndex, groupId);
      _ftsIndexConfigDao.requestDeleteFTSIndex(groupId, clusterName, indexId);
    }
  }

  private Optional<ObjectId> getClusterId(Event event) {
    return event.getResourceIds().stream()
        .filter(resourceId -> resourceId.getType() == ResourceId.ResourceType.CLUSTER)
        .findFirst()
        .map(ResourceId::getId);
  }

  /**
   * Sets the index definition version directly, if the main/live index in the stats has the same
   * version. The check can be skipped if needed (eg if stats cannot be retrieved).
   */
  public void setIndexDefinitionVersion(
      final ObjectId groupId,
      final String clusterName,
      final ObjectId indexId,
      final long definitionVersion,
      final boolean skipMainIndexVerification)
      throws SvcException {
    getFTSIndex(groupId, clusterName, indexId)
        .orElseThrow(
            () ->
                new SvcException(
                    CommonErrorCode.NOT_FOUND, String.format("Search index " + indexId)));
    // If no hosts have the definition version in the main index, then error.
    if (!skipMainIndexVerification) {
      var indexConfigStats =
          _ftsIndexConfigStatsDao.findByClusterNameAndIndexIds(
              groupId, clusterName, List.of(indexId));
      if (indexConfigStats.isEmpty()) {
        throw new SvcException(CommonErrorCode.BAD_REQUEST, "index stats unavailable");
      }
      if (!indexConfigStats.stream()
          .map(stats -> stats.getDetailedStatuses().getMainIndex())
          .flatMap(Optional::stream)
          .anyMatch(
              index ->
                  index
                      .getDefinition()
                      .getDefinitionVersion()
                      .equals(Optional.of(definitionVersion)))) {
        throw new SvcException(
            CommonErrorCode.BAD_REQUEST,
            String.format(
                "Definition version %d does not match any main index", definitionVersion));
      }
    }

    _ftsIndexConfigDao.setIndexDefinitionVersion(groupId, clusterName, indexId, definitionVersion);
  }

  /**
   * Request to delete an index. Initial attempt is synchronous, but if the index collection UUID
   * does not match the mongod collection UUID, the delete falls back to an asynchronous delete
   * attempt.
   *
   * @param groupId: Group that contains the index.
   * @param clusterName: Cluster that contains the index.
   * @param databaseName: Database that contains the index.
   * @param leafCollection: Expected name of collection containing the index.
   * @param collectionUUID: Collection UUID containing the index.
   * @param indexName: Name of index to delete.
   * @param auditInfo: Audit info synonyms collections.
   */
  @WithSpan
  public void requestDeleteFTSIndex(
      final ObjectId groupId,
      final String clusterName,
      final String databaseName,
      final String leafCollection,
      final Optional<UUID> collectionUUID,
      final String indexName,
      final AuditInfo auditInfo)
      throws SvcException {
    final FTSIndex existingIndex =
        getFTSIndexConfig(groupId, clusterName)
            .flatMap(
                ftsIndexConfig ->
                    (collectionUUID.isPresent())
                        ? ftsIndexConfig.getIndexByRootCollection(
                            databaseName, collectionUUID.get(), indexName)
                        : ftsIndexConfig.getIndexByLeafCollection(
                            databaseName, leafCollection, indexName))
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.NOT_FOUND,
                        String.format(
                            "Search index %s.%s.%s", databaseName, leafCollection, indexName)));
    if (existingIndex.getDeleteRequestedDate().isPresent()) {
      throw new SvcException(NDSErrorCode.FTS_INDEX_DELETION_ALREADY_REQUESTED);
    }
    submitAuditAndSegmentIndexEvents(groupId, clusterName, existingIndex, auditInfo);
    if (syncIndexDeletionEnabled(groupId)
        && validCollection(
            collectionUUID, existingIndex, groupId, leafCollection, databaseName, auditInfo)) {
      countSynchronousDeletion(existingIndex, groupId);
      int totalIndexes =
          _ftsIndexConfigDao.deleteFTSIndex(groupId, clusterName, existingIndex.getIndexId());
      if (totalIndexes == 0) {
        _ndsClusterSvc.setNeedsPublishDateForCluster(groupId, clusterName);
        _ndsGroupDao.setPlanASAP(groupId);
        logFTSIndexConfigSize(groupId, clusterName);
      }
    } else {
      // If sync delete is not enabled, perform an async delete.
      countAsynchronousDeletion(existingIndex, groupId);
      _ftsIndexConfigDao.requestDeleteFTSIndex(groupId, clusterName, existingIndex.getIndexId());
    }
  }

  /**
   * Request to delete a tenant index.
   *
   * @param groupId: Group that contains the index.
   * @param clusterName: Cluster that contains the index.
   * @param databaseName: Database that contains the index.
   * @param leafCollection: Expected name of collection containing the index.
   * @param collectionUUID: Expected collection UUID containing the index.
   * @param indexName: Name of index to delete.
   * @param auditInfo: Audit info synonyms collections.
   */
  @WithSpan
  public void requestDeleteTenantFTSIndex(
      final ObjectId groupId,
      final String clusterName,
      final String databaseName,
      final String leafCollection,
      final Optional<UUID> collectionUUID,
      final String indexName,
      final AuditInfo auditInfo)
      throws SvcException {
    final var tenantInfo = fetchTenantClusterInfo(groupId, clusterName);

    final FTSIndex existingIndex =
        (collectionUUID.isPresent()
                ? getMTMIndexesWithLegacyStats(
                    tenantInfo, databaseName, collectionUUID.get(), Optional.of(indexName))
                : getMTMIndexesWithLegacyStats(
                    tenantInfo, databaseName, leafCollection, Optional.of(indexName)))
            .map(
                mtmIndex ->
                    // We don't need to care about stats here.
                    mtmIndex
                        .copy()
                        .setDatabase(mtmDatabaseToTenantDatabase(mtmIndex.getDatabase()))
                        .build())
            .findFirst()
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.NOT_FOUND,
                        String.format(
                            "Search index %s.%s.%s", databaseName, leafCollection, indexName)));
    if (existingIndex.getDeleteRequestedDate().isPresent()) {
      throw new SvcException(NDSErrorCode.FTS_INDEX_DELETION_ALREADY_REQUESTED);
    }

    if (syncIndexDeletionEnabled(tenantInfo.mtmGroupId())
        && validCollection(
            collectionUUID,
            existingIndex,
            tenantInfo.mtmGroupId(),
            leafCollection,
            databaseName,
            auditInfo)) {
      countSynchronousDeletion(existingIndex, tenantInfo.mtmGroupId());
      int totalIndexes =
          _ftsIndexConfigDao.deleteTenantFTSIndex(
              tenantInfo.mtmGroupId(),
              tenantInfo.mtmClusterName(),
              tenantInfo.tenantId(),
              existingIndex.getIndexId());
      if (totalIndexes == 0) {
        _ndsClusterSvc.setNeedsPublishDateForCluster(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
        _ndsGroupDao.setPlanASAP(tenantInfo.mtmGroupId());
        logFTSIndexConfigSize(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
        logNumberOfTenantFTSIndexes(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
      }
    } else {
      // If sync delete is not enabled, perform an async delete.
      countAsynchronousDeletion(existingIndex, tenantInfo.mtmGroupId());
      _ftsIndexConfigDao.requestDeleteTenantFTSIndex(
          tenantInfo.mtmGroupId(),
          tenantInfo.mtmClusterName(),
          tenantInfo.tenantId(),
          existingIndex.getIndexId());
    }
  }

  /**
   * Request to delete a tenant index.
   *
   * @param groupId: Group that contains the index.
   * @param clusterName: Cluster that contains the index.
   * @param indexId: ID of index to delete.
   * @param auditInfo: Audit info synonyms collections
   */
  @WithSpan
  public void requestDeleteTenantFTSIndex(
      final ObjectId groupId,
      final String clusterName,
      final ObjectId indexId,
      final AuditInfo auditInfo)
      throws SvcException {
    final var tenantInfo = fetchTenantClusterInfo(groupId, clusterName);

    final FTSIndex existingIndex =
        getMTMIndexesWithLegacyStats(tenantInfo, indexId)
            .map(
                mtmIndex ->
                    // We don't need to care about stats here.
                    mtmIndex
                        .copy()
                        .setDatabase(mtmDatabaseToTenantDatabase(mtmIndex.getDatabase()))
                        .build())
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.NOT_FOUND, String.format("Search index %s", indexId)));
    if (existingIndex.getDeleteRequestedDate().isPresent()) {
      throw new SvcException(NDSErrorCode.FTS_INDEX_DELETION_ALREADY_REQUESTED);
    }
    submitAuditAndSegmentIndexEvents(groupId, clusterName, existingIndex, auditInfo);
    if (syncIndexDeletionEnabled(tenantInfo.mtmGroupId())) {
      countSynchronousDeletion(existingIndex, tenantInfo.mtmGroupId());
      int totalIndexes =
          _ftsIndexConfigDao.deleteTenantFTSIndex(
              tenantInfo.mtmGroupId(),
              tenantInfo.mtmClusterName(),
              tenantInfo.tenantId(),
              existingIndex.getIndexId());
      if (totalIndexes == 0) {
        _ndsClusterSvc.setNeedsPublishDateForCluster(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
        _ndsGroupDao.setPlanASAP(tenantInfo.mtmGroupId());
        logFTSIndexConfigSize(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
        logNumberOfTenantFTSIndexes(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());
      }
    } else {
      // If collection UUID doesn't match, fall back to async delete.
      countAsynchronousDeletion(existingIndex, tenantInfo.mtmGroupId());
      _ftsIndexConfigDao.requestDeleteTenantFTSIndex(
          tenantInfo.mtmGroupId(),
          tenantInfo.mtmClusterName(),
          tenantInfo.tenantId(),
          existingIndex.getIndexId());
    }
  }

  public void initTenantFTSIndexesMigration(
      final ObjectId mtmGroupId, final String mtmClusterName, final String tenantId)
      throws SvcException {
    final Stream<FTSIndex> existingMTMIndexes =
        getMTMIndexesWithLegacyStats(mtmGroupId, mtmClusterName, tenantId);

    final ClusterDescription clusterDescription =
        fetchClusterDescription(mtmGroupId, mtmClusterName)
            .orElseThrow(() -> new SvcException(CommonErrorCode.NOT_FOUND));
    if (FTSIndexConfigUtil.shouldChangeIndexStatusToMigrating(clusterDescription)) {
      existingMTMIndexes.forEach(
          ftsIndex -> {
            _ftsIndexConfigDao.updateTenantFTSIndexStatus(
                mtmGroupId,
                mtmClusterName,
                tenantId,
                ftsIndex.getIndexId(),
                FTSIndex.Status.MIGRATING);
            SEARCH_TENANT_INDEX_MIGRATION_STARTED_COUNTER.labels(mtmGroupId.toString()).inc();
          });
    }
  }

  /**
   * Migrates FTS index definition from tenant to dedicated cluster. It copies all index config
   * parameters including field mapping, analyzers, synonyms, etc.
   *
   * <p>NOTE: It also updates index feature version to the current latest version.
   */
  @WithSpan
  public void migrateTenantToDedicatedFTSIndexesDefinitions(
      final ObjectId sourceMTMGroupId,
      final String sourceMTMClusterName,
      final String tenantId,
      final ObjectId groupId,
      final String clusterName)
      throws SvcException {
    final List<FTSIndex> targetIndexes =
        getMTMIndexesWithLegacyStats(sourceMTMGroupId, sourceMTMClusterName, tenantId)
            .map(index -> tenantToDedicatedFTSIndexMigrationTarget(index, groupId))
            .collect(Collectors.toList());
    // create target index config
    ensureFTSIndexConfig(groupId, clusterName);
    final int totalIndexes = _ftsIndexConfigDao.setFTSIndexes(groupId, clusterName, targetIndexes);

    if (totalIndexes == targetIndexes.size()) {
      _ndsClusterSvc.setNeedsPublishDateForCluster(groupId, clusterName);
      _ndsGroupDao.setPlanASAP(groupId);
    }

    // remove tenant from source
    deleteTenantFTSIndexes(sourceMTMGroupId, sourceMTMClusterName, tenantId);

    LOG.info(
        "Migrated {} indexes from MTM {} {} {} to cluster {} {}.",
        targetIndexes.size(),
        kv("sourceGroupId", sourceMTMGroupId),
        kv("sourceClusterName", sourceMTMClusterName),
        kv("sourceTenantId", tenantId),
        kv("targetGroupId", groupId),
        kv("targetClusterName", clusterName));
    SEARCH_TENANT_INDEX_MIGRATION_COMPLETED_COUNTER
        .labels(groupId.toString(), "TENANT_TO_DEDICATED")
        .inc(targetIndexes.size());
  }

  /**
   * Migrates FTS from one tenant cluster to another one. It copies all index config parameters
   * including field mapping, analyzers, synonyms, etc.
   *
   * <p>NOTE: It also updates index feature version to the current latest version.
   */
  @WithSpan
  public void migrateTenantToTenantFTSIndexesDefinitions(
      final ObjectId sourceMTMGroupId,
      final String sourceMTMClusterName,
      final String sourceTenantId,
      final ObjectId tenantGroupId,
      final String tenantClusterName)
      throws SvcException {
    final var targetInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    final List<FTSIndex> targetIndexes =
        getMTMIndexesWithLegacyStats(sourceMTMGroupId, sourceMTMClusterName, sourceTenantId)
            .map(
                index ->
                    tenantToTenantFTSIndexMigrationTarget(
                        index, targetInfo.tenantId(), tenantGroupId))
            .collect(Collectors.toList());

    // create target index config
    ensureFTSIndexConfig(targetInfo.mtmGroupId(), targetInfo.mtmClusterName());

    int totalIndexes =
        _ftsIndexConfigDao.setTenantFTSIndexesForNewTenant(
            targetInfo.mtmGroupId(),
            targetInfo.mtmClusterName(),
            targetInfo.tenantId(),
            targetIndexes);

    if (totalIndexes == targetIndexes.size()) {
      _ndsClusterSvc.setNeedsPublishDateForCluster(
          targetInfo.mtmGroupId(), targetInfo.mtmClusterName());
      _ndsGroupDao.setPlanASAP(targetInfo.mtmGroupId());
    }

    // remove tenant from source
    deleteTenantFTSIndexes(sourceMTMGroupId, sourceMTMClusterName, sourceTenantId);

    LOG.info(
        "Migrated {} indexes from MTM {} {} {} to MTM {} {} {}.",
        targetIndexes.size(),
        kv("sourceGroupId", sourceMTMGroupId),
        kv("sourceClusterName", sourceMTMClusterName),
        kv("sourceTenantId", sourceTenantId),
        kv("targetGroupId", targetInfo.mtmGroupId()),
        kv("targetClusterName", targetInfo.mtmClusterName()),
        kv("targetTenantId", targetInfo.tenantId()));
    SEARCH_TENANT_INDEX_MIGRATION_COMPLETED_COUNTER
        .labels(tenantGroupId.toString(), "TENANT_TO_TENANT")
        .inc(targetIndexes.size());
  }

  @WithSpan
  public void deleteTenantFTSIndexes(
      final ObjectId mtmGroupId, final String mtmClusterName, final String tenantId) {
    _ftsIndexConfigDao.deleteTenantFTSIndexes(mtmGroupId, mtmClusterName, tenantId);
  }

  private FTSIndex tenantToDedicatedFTSIndexMigrationTarget(
      final FTSIndex ftsIndex, final ObjectId groupId) {
    return tenantToMigrationTarget(
        ftsIndex, mtmDatabaseToTenantDatabase(ftsIndex.getDatabase()), groupId);
  }

  private FTSIndex tenantToTenantFTSIndexMigrationTarget(
      final FTSIndex ftsIndex, final String targetTenantId, final ObjectId groupId) {
    return tenantToMigrationTarget(
        ftsIndex, replaceTenantInDatabase(ftsIndex.getDatabase(), targetTenantId), groupId);
  }

  private FTSIndex tenantToMigrationTarget(
      final FTSIndex ftsIndex, final String database, final ObjectId groupId) {
    FTSIndex.Builder<?> builder;
    switch (ftsIndex.getActualType()) {
      case SEARCH:
        FTSSearchIndex textSearchIndex = ftsIndex.toTextSearchIndex();
        FTSSearchIndex.Builder textSearchBuilder =
            new FTSSearchIndex.Builder()
                .setMappings(textSearchIndex.getMappings().orElse(null))
                .setAnalyzer(textSearchIndex.getAnalyzer().orElse(null))
                .setAnalyzers(textSearchIndex.getAnalyzers().orElse(null))
                .setSearchAnalyzer(textSearchIndex.getSearchAnalyzer().orElse(null))
                .setSynonyms(textSearchIndex.getSynonyms().orElse(null))
                .setStoredSource(textSearchIndex.getStoredSource().orElse(null))
                .setIndexFeatureVersion(getMaxPossibleIndexFeatureVersion(groupId));
        if (textSearchIndex.getType().equals(Optional.of(FTSIndex.Type.SEARCH))) {
          textSearchBuilder.setTypeExplicitly();
        }
        builder = textSearchBuilder;
        break;
      case VECTOR_SEARCH:
        FTSVectorSearchIndex vectorSearchIndex = ftsIndex.toVectorSearchIndex();
        builder =
            new FTSVectorSearchIndex.Builder()
                .setFields(vectorSearchIndex.getFields().orElse(null))
                .setIndexFeatureVersion(getMaxPossibleIndexFeatureVersion(groupId));
        break;
      default:
        throw new IllegalArgumentException("Unknown index type: " + ftsIndex.getActualType());
    }
    return builder
        .setIndexId(new ObjectId())
        .setName(ftsIndex.getName())
        .setDatabase(database)
        .setLastObservedCollectionName(ftsIndex.getLastObservedCollectionName())
        .setView(ftsIndex.getView().orElse(null))
        .build();
  }

  @WithSpan
  public Optional<FTSIndex> getFTSIndex(
      final ObjectId groupId, final String clusterName, final ObjectId indexId) {
    return getFTSIndexConfig(groupId, clusterName)
        .flatMap(ftsIndexConfig -> ftsIndexConfig.getFTSIndex(indexId))
        .map(ftsIndex -> maybeMergeIndexStats(groupId, clusterName, ftsIndex));
  }

  public Optional<FTSIndex> getFTSIndex(
      final ObjectId groupId,
      final String clusterName,
      final String database,
      final String leafCollection,
      final String name) {

    return getFTSIndexConfig(groupId, clusterName)
        .flatMap(
            ftsIndexConfig ->
                ftsIndexConfig.getIndexByLeafCollection(database, leafCollection, name))
        .map(ftsIndex -> maybeMergeIndexStats(groupId, clusterName, ftsIndex));
  }

  @WithSpan
  public Optional<FTSIndex> getTenantFTSIndex(
      final ObjectId tenantGroupId, final String tenantClusterName, final ObjectId indexId)
      throws SvcException {
    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    final Optional<FTSIndex> mtmIndex = getMTMIndexesWithLegacyStats(tenantInfo, indexId);
    if (mtmIndex.isEmpty()) {
      return Optional.empty();
    }

    final Map<String, String> hostMap =
        mtmTenantHostMapping(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), tenantGroupId, tenantClusterName);

    return Optional.of(
        mtmIndextoTenantIndex(
            maybeMergeIndexStats(
                tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), mtmIndex.get()),
            hostMap));
  }

  public Optional<FTSIndex> getTenantFTSIndex(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final String database,
      final String leafCollection,
      final String name)
      throws SvcException {
    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    final Optional<FTSIndex> mtmIndex =
        getMTMIndexesWithLegacyStats(tenantInfo, database, leafCollection, Optional.of(name))
            .findFirst();
    if (mtmIndex.isEmpty()) {
      return Optional.empty();
    }

    final Map<String, String> hostMap =
        mtmTenantHostMapping(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), tenantGroupId, tenantClusterName);

    return Optional.of(
        mtmIndextoTenantIndex(
            maybeMergeIndexStats(
                tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), mtmIndex.get()),
            hostMap));
  }

  @WithSpan
  public FTSIndex updateUserDefinedFTSIndexFields(
      final ObjectId groupId,
      final String clusterName,
      final ObjectId indexId,
      final Function<FTSIndex.Type, FTSIndex> indexUpdateProducer,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    Supplier<SvcException> notFoundErrorCreator =
        () ->
            new SvcException(
                CommonErrorCode.NOT_FOUND, String.format("Search index with id %s", indexId));

    final FTSIndexConfig ftsIndexConfig = ensureFTSIndexConfig(groupId, clusterName);
    validateConfigSmallerThanMax(ftsIndexConfig);
    FTSIndex existingIndex = ftsIndexConfig.getFTSIndex(indexId).orElseThrow(notFoundErrorCreator);

    final FTSIndex newIndex;
    try {
      newIndex =
          populateIndexUpdateWithExistingIndexProperties(
              indexUpdateProducer.apply(existingIndex.getActualType()), existingIndex);
    } catch (IllegalArgumentException e) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
    }

    return updateUserDefinedFTSIndexFields(
            groupId, clusterName, existingIndex, newIndex, auditInfo, editor)
        .orElse(ftsIndexConfig)
        .getFTSIndex(indexId)
        .map(ftsIndex -> maybeMergeIndexStats(groupId, clusterName, ftsIndex))
        .orElseThrow(notFoundErrorCreator);
  }

  @WithSpan
  public FTSIndex updateUserDefinedFTSIndexFields(
      final ObjectId groupId,
      final String clusterName,
      final String database,
      final String leafCollectionName,
      final String name,
      final Function<FTSIndex.Type, FTSIndex> indexUpdateProducer,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    Supplier<SvcException> notFoundErrorCreator =
        () ->
            new SvcException(
                CommonErrorCode.NOT_FOUND,
                String.format("Search index %s.%s.%s", database, leafCollectionName, name));
    final FTSIndexConfig ftsIndexConfig = ensureFTSIndexConfig(groupId, clusterName);
    validateConfigSmallerThanMax(ftsIndexConfig);
    FTSIndex existingIndex =
        ftsIndexConfig
            .getIndexByLeafCollection(database, leafCollectionName, name)
            .orElseThrow(notFoundErrorCreator);

    final FTSIndex newIndex;
    try {
      newIndex =
          populateIndexUpdateWithExistingIndexProperties(
              indexUpdateProducer.apply(existingIndex.getActualType()), existingIndex);
    } catch (IllegalArgumentException e) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
    }

    return updateUserDefinedFTSIndexFields(
            groupId, clusterName, existingIndex, newIndex, auditInfo, editor)
        .orElse(ftsIndexConfig)
        .getIndexByLeafCollection(database, leafCollectionName, name)
        .map(ftsIndex -> maybeMergeIndexStats(groupId, clusterName, ftsIndex))
        .orElseThrow(notFoundErrorCreator);
  }

  public FTSIndexConfig updateUserDefinedFTSIndexFields(
      final ObjectId groupId,
      final String clusterName,
      final String leafCollectionName,
      final FTSIndex ftsIndex,
      final AuditInfo auditInfo)
      throws SvcException {
    return updateUserDefinedFTSIndexFields(
        groupId, clusterName, leafCollectionName, ftsIndex, auditInfo, Optional.empty());
  }

  public FTSIndexConfig updateUserDefinedFTSIndexFields(
      final ObjectId groupId,
      final String clusterName,
      final String leafCollectionName,
      final FTSIndex ftsIndex,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    final FTSIndexConfig ftsIndexConfig = ensureFTSIndexConfig(groupId, clusterName);
    validateConfigSmallerThanMax(ftsIndexConfig);
    FTSIndex existingFTSIndex;
    FTSIndex newIndex;
    if (ftsIndex.getIndexId() == null) {
      existingFTSIndex =
          ftsIndexConfig
              .getIndexByLeafCollection(
                  ftsIndex.getDatabase(), leafCollectionName, ftsIndex.getName())
              .orElseThrow(
                  () ->
                      new SvcException(
                          CommonErrorCode.NOT_FOUND,
                          String.format(
                              "Search index %s.%s.%s",
                              ftsIndex.getDatabase(), leafCollectionName, ftsIndex.getName())));
      newIndex = ftsIndex.copy().setIndexId(existingFTSIndex.getIndexId()).build();
    } else {
      newIndex = ftsIndex;
      existingFTSIndex =
          ftsIndexConfig
              .getFTSIndex(ftsIndex.getIndexId())
              .orElseThrow(
                  () ->
                      new SvcException(
                          CommonErrorCode.NOT_FOUND,
                          String.format("Search index with id %s", ftsIndex.getIndexId())));
    }
    if (existingFTSIndex.getActualType() != newIndex.getActualType()) {
      throw new SvcException(NDSErrorCode.FTS_INVALID_INDEX_UPDATE, "type cannot be changed");
    }

    return updateUserDefinedFTSIndexFields(
            groupId, clusterName, existingFTSIndex, newIndex, auditInfo, editor)
        .orElse(ftsIndexConfig);
  }

  private Optional<FTSIndexConfig> updateUserDefinedFTSIndexFields(
      ObjectId groupId,
      String clusterName,
      FTSIndex existingIndex,
      FTSIndex newIndex,
      AuditInfo auditInfo,
      Optional<String> editor)
      throws SvcException {
    Group group = this._groupDao.findById(groupId);
    this._ftsIndexConfigUtil.validateFTSIndexUpdate(group, clusterName, newIndex);

    if (existingIndex.getDeleteRequestedDate().isPresent()) {
      throw new SvcException(NDSErrorCode.FTS_INDEX_DELETION_ALREADY_REQUESTED);
    }

    if (newIndex.getActualType() == FTSIndex.Type.SEARCH) {
      newIndex =
          newIndex
              .toTextSearchIndex()
              .copy()
              .setIndexFeatureVersion(getMaxPossibleIndexFeatureVersion(groupId))
              .build();
    } else {
      newIndex =
          newIndex
              .toVectorSearchIndex()
              .copy()
              .setIndexFeatureVersion(getMaxPossibleIndexFeatureVersion(groupId))
              .build();
    }

    if (!isIndexModified(newIndex, existingIndex)) {
      return Optional.empty();
    }
    try {
      newIndex = appendSimilarityMethodIfNeeded(newIndex, groupId);
      return Optional.of(
          this._ftsIndexConfigDao
              .updateUserDefinedFTSIndexFields(groupId, clusterName, newIndex)
              .orElseThrow(
                  () ->
                      new SvcException(
                          CommonErrorCode.NOT_FOUND,
                          String.format("Search index with id %s", existingIndex.getIndexId()))));
    } finally {
      var event = saveAuditEvent(groupId, clusterName, newIndex, Type.FTS_INDEX_UPDATED, auditInfo);
      submitSegmentIndexEvent(
          SearchIndexUpdatedEvent.EVENT_TYPE,
          groupId,
          clusterName,
          getClusterId(event),
          auditInfo,
          newIndex.getActualType(),
          looksLikeSampleDataNamespace(existingIndex),
          editor,
          Optional.empty());
    }
  }

  @WithSpan
  public FTSIndex updateTenantUserDefinedFTSIndexFields(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final ObjectId indexId,
      final Function<FTSIndex.Type, FTSIndex> indexUpdateProducer,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    final Supplier<SvcException> notFoundErrorCreator =
        () ->
            new SvcException(
                CommonErrorCode.NOT_FOUND, String.format("Search index with id %s", indexId));

    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    final FTSIndexConfig ftsIndexConfig =
        ensureFTSIndexConfig(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());

    final FTSIndex existingIndex =
        ftsIndexConfig
            .getTenantFTSIndex(tenantInfo.tenantId(), indexId)
            .orElseThrow(notFoundErrorCreator);

    final FTSIndex newIndex = updateExisting(indexUpdateProducer, existingIndex, tenantGroupId);

    final Map<String, String> hostMap =
        mtmTenantHostMapping(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), tenantGroupId, tenantClusterName);
    return mtmIndextoTenantIndex(
        updateTenantUserDefinedFTSIndexFields(
                tenantGroupId, tenantClusterName, existingIndex, newIndex, auditInfo, editor)
            .orElse(ftsIndexConfig)
            .getTenantFTSIndex(tenantInfo.tenantId(), indexId)
            .map(
                tenantFtsIndex ->
                    maybeMergeIndexStats(
                        tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), tenantFtsIndex))
            .orElseThrow(notFoundErrorCreator),
        hostMap);
  }

  @WithSpan
  public FTSIndex updateTenantUserDefinedFTSIndexFields(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final String databaseName,
      final String leafCollection,
      final String indexName,
      final Function<FTSIndex.Type, FTSIndex> indexUpdateProducer,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    final Supplier<SvcException> notFoundErrorCreator =
        () ->
            new SvcException(
                CommonErrorCode.NOT_FOUND,
                String.format("Search index at %s.%s.%s", databaseName, leafCollection, indexName));

    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    final FTSIndexConfig ftsIndexConfig =
        ensureFTSIndexConfig(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());

    final FTSIndex existingIndex =
        ftsIndexConfig
            .getTenantIndexByLeafCollection(
                tenantInfo.tenantId(),
                tenantDatabaseToMTMDatabase(databaseName, tenantInfo.tenantId()),
                leafCollection,
                indexName)
            .orElseThrow(notFoundErrorCreator);

    final FTSIndex newIndex = updateExisting(indexUpdateProducer, existingIndex, tenantGroupId);

    final Map<String, String> hostMap =
        mtmTenantHostMapping(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), tenantGroupId, tenantClusterName);
    return mtmIndextoTenantIndex(
        updateTenantUserDefinedFTSIndexFields(
                tenantGroupId, tenantClusterName, existingIndex, newIndex, auditInfo, editor)
            .orElse(ftsIndexConfig)
            .getTenantFTSIndex(tenantInfo.tenantId(), existingIndex.getIndexId())
            .map(
                tenantFtsIndex ->
                    maybeMergeIndexStats(
                        tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), tenantFtsIndex))
            .orElseThrow(notFoundErrorCreator),
        hostMap);
  }

  private FTSIndex updateExisting(
      Function<FTSIndex.Type, FTSIndex> indexUpdateProducer,
      FTSIndex existingIndex,
      ObjectId groupId)
      throws SvcException {
    final FTSIndex updatedFields;
    try {
      updatedFields = indexUpdateProducer.apply(existingIndex.getActualType());
    } catch (IllegalArgumentException | ClassCastException e) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e, "request");
    }

    try {
      var populatedIndex =
          populateIndexUpdateWithExistingIndexProperties(updatedFields, existingIndex);
      return updateIndexFeatureVersion(groupId, populatedIndex);
    } catch (IllegalArgumentException e) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e, "request");
    }
  }

  public FTSIndexConfig updateTenantUserDefinedFTSIndexFields(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final FTSIndex ftsIndex,
      final AuditInfo auditInfo)
      throws SvcException {
    return updateTenantUserDefinedFTSIndexFields(
        tenantGroupId, tenantClusterName, ftsIndex, auditInfo, Optional.empty());
  }

  public FTSIndexConfig updateTenantUserDefinedFTSIndexFields(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final FTSIndex index,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    FTSIndex ftsIndex = updateIndexFeatureVersion(tenantGroupId, index);

    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    final FTSIndexConfig ftsIndexConfig =
        ensureFTSIndexConfig(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName());

    final FTSIndex existingFTSIndex =
        ftsIndexConfig
            .getTenantFTSIndex(tenantInfo.tenantId(), ftsIndex.getIndexId())
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.NOT_FOUND,
                        String.format("Search index with id %s", ftsIndex.getIndexId())));
    if (existingFTSIndex.getActualType() != ftsIndex.getActualType()) {
      throw new SvcException(NDSErrorCode.FTS_INVALID_INDEX_UPDATE, "type cannot be changed");
    }
    return updateTenantUserDefinedFTSIndexFields(
            tenantGroupId, tenantClusterName, existingFTSIndex, ftsIndex, auditInfo, editor)
        .orElse(ftsIndexConfig);
  }

  private Optional<FTSIndexConfig> updateTenantUserDefinedFTSIndexFields(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final FTSIndex existingIndex,
      final FTSIndex newIndex,
      final AuditInfo auditInfo,
      final Optional<String> editor)
      throws SvcException {
    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    final Group group = _groupDao.findById(tenantGroupId);
    _ftsIndexConfigUtil.validateTenantFTSIndexUpdate(group, tenantClusterName, newIndex);
    if (existingIndex.getDeleteRequestedDate().isPresent()) {
      throw new SvcException(NDSErrorCode.FTS_INDEX_DELETION_ALREADY_REQUESTED);
    }

    if (!isIndexModified(newIndex, existingIndex)) {
      return Optional.empty();
    }

    try {
      return Optional.of(
          _ftsIndexConfigDao
              .updateTenantUserDefinedFTSIndexFields(
                  tenantInfo.mtmGroupId(),
                  tenantInfo.mtmClusterName(),
                  tenantInfo.tenantId(),
                  newIndex)
              .orElseThrow(
                  () ->
                      new SvcException(
                          CommonErrorCode.NOT_FOUND,
                          String.format(
                              "Search index at %s.%s.%s",
                              newIndex.getDatabase(),
                              newIndex.getLastObservedCollectionName(),
                              newIndex.getName()))));
    } finally {
      var event =
          saveAuditEvent(
              tenantGroupId,
              tenantClusterName,
              newIndex,
              FTSIndexAudit.Type.FTS_INDEX_UPDATED,
              auditInfo);
      submitSegmentIndexEvent(
          SearchIndexUpdatedEvent.EVENT_TYPE,
          tenantGroupId,
          tenantClusterName,
          getClusterId(event),
          auditInfo,
          newIndex.getActualType(),
          looksLikeSampleDataNamespace(existingIndex),
          editor,
          Optional.empty());
    }
  }

  private FTSIndex populateIndexUpdateWithExistingIndexProperties(
      final FTSIndex indexUpdate, final FTSIndex existingIndex) {
    return indexUpdate
        .copy()
        .setIndexId(existingIndex.getIndexId())
        .setDatabase(existingIndex.getDatabase())
        .setLastObservedCollectionName(existingIndex.getLastObservedCollectionName())
        .setView(existingIndex.getView().orElse(null))
        .setName(existingIndex.getName())
        .setType(existingIndex.getType().orElse(null))
        .build();
  }

  @WithSpan
  public List<FTSIndex> getFTSIndexesForNamespace(
      final ObjectId groupId,
      final String clusterName,
      final String databaseName,
      final String leafCollection) {
    List<FTSIndex> ftsIndexes =
        getFTSIndexConfig(groupId, clusterName).stream()
            .map(FTSIndexConfig::getIndexesForAllTypes)
            .flatMap(List::stream)
            .filter(
                ftsIndex ->
                    ftsIndex.getDatabase().equals(databaseName)
                        && ftsIndex.getLeafCollectionName().equals(leafCollection))
            .toList();
    return maybeMergeIndexStats(groupId, clusterName, ftsIndexes);
  }

  @WithSpan
  public List<FTSIndex> getTenantFTSIndexesForNamespace(
      final ObjectId tenantGroupId,
      final String tenantClusterName,
      final String databaseName,
      final String leafCollection)
      throws SvcException {
    final var tenantInfo = fetchTenantClusterInfo(tenantGroupId, tenantClusterName);

    final List<FTSIndex> mtmIndexes =
        getMTMIndexesWithLegacyStats(tenantInfo, databaseName, leafCollection, Optional.empty())
            .toList();

    if (mtmIndexes.isEmpty()) {
      return new ArrayList<>();
    }

    final Map<String, String> hostMap =
        mtmTenantHostMapping(
            tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), tenantGroupId, tenantClusterName);

    return maybeMergeIndexStats(tenantInfo.mtmGroupId(), tenantInfo.mtmClusterName(), mtmIndexes)
        .stream()
        .map(ftsIndex -> mtmIndextoTenantIndex(ftsIndex, hostMap))
        .collect(Collectors.toList());
  }

  private static boolean isIndexModified(
      final FTSIndex newFTSIndex, final FTSIndex existingFTSIndex) {
    Preconditions.checkState(newFTSIndex.getActualType() == existingFTSIndex.getActualType());
    // If the type field is changed from missing to existing, we still consider this to be modified.
    if (!newFTSIndex.getType().equals(existingFTSIndex.getType())) {
      return true;
    }
    if (!newFTSIndex.getNumPartitions().equals(existingFTSIndex.getNumPartitions())) {
      return true;
    }
    switch (existingFTSIndex.getActualType()) {
      case SEARCH:
        FTSSearchIndex newTextSearchIndex = newFTSIndex.toTextSearchIndex();
        FTSSearchIndex existingTextSearchIndex = existingFTSIndex.toTextSearchIndex();
        return !newTextSearchIndex.getAnalyzer().equals(existingTextSearchIndex.getAnalyzer())
            || !newTextSearchIndex.getAnalyzers().equals(existingTextSearchIndex.getAnalyzers())
            || !newTextSearchIndex
                .getSearchAnalyzer()
                .equals(existingTextSearchIndex.getSearchAnalyzer())
            || !newTextSearchIndex.getMappings().equals(existingTextSearchIndex.getMappings())
            || !newTextSearchIndex.getSynonyms().equals(existingTextSearchIndex.getSynonyms())
            || !newTextSearchIndex
                .getStoredSource()
                .equals(existingTextSearchIndex.getStoredSource())
            || !newTextSearchIndex
                .getIndexFeatureVersion()
                .equals(existingTextSearchIndex.getIndexFeatureVersion());
      case VECTOR_SEARCH:
        FTSVectorSearchIndex newVectorSearchIndex = newFTSIndex.toVectorSearchIndex();
        FTSVectorSearchIndex existingVectorSearchIndex = existingFTSIndex.toVectorSearchIndex();
        return !newVectorSearchIndex.getFields().equals(existingVectorSearchIndex.getFields())
            || !newVectorSearchIndex
                .getIndexFeatureVersion()
                .equals(existingVectorSearchIndex.getIndexFeatureVersion());
      default:
        throw new IllegalArgumentException(
            "Unknown index type: " + existingFTSIndex.getActualType());
    }
  }

  public int getNumIndexes(final ObjectId groupId, final String clusterName) {
    return getFTSIndexConfig(groupId, clusterName)
        .map(FTSIndexConfig::getIndexesForAllTypes)
        .orElse(Collections.emptyList())
        .size();
  }

  public List<FTSIndex> getFTSIndexes(final ObjectId groupId, final String clusterName) {
    List<FTSIndex> ftsIndexes =
        getFTSIndexConfig(groupId, clusterName)
            .map(FTSIndexConfig::getIndexesForAllTypes)
            .orElse(Collections.emptyList());
    if (ftsIndexes.isEmpty()
        || !isFeatureFlagEnabled(
            FeatureFlag.ATLAS_SEARCH_READ_INDEX_STATS_FROM_NEW_COLLECTION,
            _appSettings,
            null,
            _groupDao.findById(groupId))) {
      return ftsIndexes;
    }
    return mergeIndexStats(
        ftsIndexes, _ftsIndexConfigStatsDao.findByClusterName(groupId, clusterName));
  }

  private static FTSIndex mergeIndexStats(
      FTSIndex ftsIndex, List<FTSIndexConfigStats> ftsIndexConfigStatsList) {
    if (ftsIndexConfigStatsList.isEmpty()) {
      return ftsIndex.copy().setStats(null).setDetailedStatuses(null).build();
    }

    final FTSIndexStatusMap<FTSIndexHostStat> stats = new FTSIndexStatusMap<>();
    final FTSIndexStatusMap<FTSIndexHostDetailedStatuses> detailedStatuses =
        new FTSIndexStatusMap<>();
    ftsIndexConfigStatsList.forEach(
        ftsIndexConfigStats -> {
          Preconditions.checkState(
              ftsIndex.getIndexId().equals(ftsIndexConfigStats.getId().getIndexId()));
          stats.put(ftsIndexConfigStats.getId().getHostname(), ftsIndexConfigStats.getStats());
          detailedStatuses.put(
              ftsIndexConfigStats.getId().getHostname(), ftsIndexConfigStats.getDetailedStatuses());
        });
    return ftsIndex.copy().setStats(stats).setDetailedStatuses(detailedStatuses).build();
  }

  @VisibleForTesting
  protected static List<FTSIndex> mergeIndexStats(
      List<FTSIndex> ftsIndexes, List<FTSIndexConfigStats> ftsIndexConfigStatsList) {
    var ftsIndexConfigStatsByIndexId =
        ftsIndexConfigStatsList.stream()
            .collect(
                Collectors.groupingBy(
                    ftsIndexConfigStats -> ftsIndexConfigStats.getId().getIndexId()));
    return ftsIndexes.stream()
        .map(
            ftsIndex ->
                mergeIndexStats(
                    ftsIndex,
                    ftsIndexConfigStatsByIndexId.getOrDefault(ftsIndex.getIndexId(), List.of())))
        .toList();
  }

  public boolean mtmHasNoTenantFTSIndexes(final ObjectId mtmGroupId, final String mtmClusterName) {
    return getFTSIndexConfig(mtmGroupId, mtmClusterName)
        .map(config -> !config.hasTenantIndexesForAnyType())
        .orElse(true);
  }

  // For testing purpose: manually set stats for given index id.
  @SuppressWarnings("TestOnlyProblems") // This method calls a method that is also test-only
  @VisibleForTesting
  public void setStatsDefinition(
      final ObjectId groupId,
      final String clusterName,
      final ObjectId indexId,
      final String hostname,
      final FTSIndexHostStat ftsIndexHostStat)
      throws SvcException {
    if (getFTSIndexConfig(groupId, clusterName)
        .flatMap(ftsIndexConfig -> ftsIndexConfig.getFTSIndex(indexId))
        .isEmpty()) {
      throw new SvcException(
          CommonErrorCode.NOT_FOUND, String.format("Search index with id %s", indexId));
    }

    _ftsIndexConfigDao.setStatsDefinition(
        groupId,
        clusterName,
        indexId,
        HostnameUtil.getHostnameWithoutDomain(hostname),
        ftsIndexHostStat);
  }

  // For testing purpose: get stats for given index id and hostname.
  @VisibleForTesting
  public Optional<? extends FTSIndexHostStat> getStatsDefinition(
      final ObjectId groupId,
      final String clusterName,
      final ObjectId indexId,
      final String hostname) {
    return getFTSIndexConfig(groupId, clusterName)
        .flatMap(ftsIndexConfig -> ftsIndexConfig.getFTSIndex(indexId))
        .flatMap(FTSIndex::getStats)
        .map(ftsIndexStats -> ftsIndexStats.get(hostname));
  }

  /**
   * Creates new empty collections for the synonyms.
   *
   * @param groupId - group id
   * @param clusterName - cluster name
   * @param databaseName - database name where collections will be created
   * @param auditInfo - audit info
   * @param createSynonymsCollectionView - list of collection to create
   * @param indexDefinition - index definition that should contain appropriate mapping for the new
   *     synonyms collections
   */
  public void createNewSynonymsCollections(
      final ObjectId groupId,
      final String clusterName,
      final String databaseName,
      final AuditInfo auditInfo,
      final List<FTSCreateSynonymsCollectionView> createSynonymsCollectionView,
      final TextSearchIndexView indexDefinition)
      throws SvcException {
    List<String> newCollectionNamesToCreate =
        createSynonymsCollectionView.stream()
            .map(FTSCreateSynonymsCollectionView::getCollectionName)
            .collect(Collectors.toList());
    validateNewSynonymsCollections(
        groupId, clusterName, databaseName, auditInfo, newCollectionNamesToCreate, indexDefinition);

    for (final FTSCreateSynonymsCollectionView collection : createSynonymsCollectionView) {
      createNewEmptySynonymCollection(
          groupId, clusterName, databaseName, auditInfo, collection.getCollectionName());

      if (collection.getServerAction().equals(ServerAction.CREATE_SAMPLE_COLLECTION)) {
        loadSynonymSampleDataset(
            groupId, clusterName, databaseName, auditInfo, collection.getCollectionName());
      }
    }
  }

  /**
   * Validate collection name, mapping and that database does not have collection with the same name
   *
   * @param groupId - group id
   * @param clusterName - cluster name
   * @param databaseName - database name where collections will be created
   * @param auditInfo - audit info
   * @param collectionNames - list of collection names to validate
   * @param indexDefinition - index definition that should have synonyms mapping with the new
   *     collections
   */
  private void validateNewSynonymsCollections(
      final ObjectId groupId,
      final String clusterName,
      final String databaseName,
      final AuditInfo auditInfo,
      final List<String> collectionNames,
      final TextSearchIndexView indexDefinition)
      throws SvcException {
    // Validate synonym collections name and proper mapping
    FTSIndexConfigUtil.validateNewSynonymsCollectionsNames(collectionNames);

    // Validate synonym collections proper mapping
    FTSIndexConfigUtil.validateNewSynonymsCollectionsMapping(
        collectionNames, indexDefinition.getSynonymsSourceCollectionNames());

    // Validate that new synonyms collections do not exist in the database
    _ftsIndexConfigUtil.validateNewSynonymsCollectionsAreAbsent(
        groupId, clusterName, databaseName, collectionNames, auditInfo);
  }

  /**
   * Creates new empty collection
   *
   * @param groupId - group id
   * @param clusterName - cluster name
   * @param databaseName - database where collection will be created
   * @param auditInfo - audit info
   * @param collectionName - collection name to create
   */
  private void createNewEmptySynonymCollection(
      final ObjectId groupId,
      final String clusterName,
      final String databaseName,
      final AuditInfo auditInfo,
      final String collectionName)
      throws SvcException {
    try {
      _searchClusterConnectionSvc.createCollection(
          groupId, clusterName, databaseName, collectionName, auditInfo);
    } catch (final MongoException pE) {
      if (pE.getCode() == DbUtils.NAMESPACE_EXISTS_ERROR_CODE) {
        throw new SvcException(NDSErrorCode.INVALID_SYNONYM_COLLECTION_EXISTS, pE, collectionName);
      }

      throw new SvcException(
          NDSErrorCode.CREATE_COLLECTION_FAILED, pE, collectionName, databaseName);
    }
  }

  /**
   * Load synonyms sample dataset to the existing collection
   *
   * @param groupId - group id
   * @param clusterName - cluster name
   * @param databaseName - database name
   * @param auditInfo - audit info
   * @param collectionName - collection name to load sample dataset
   */
  private void loadSynonymSampleDataset(
      final ObjectId groupId,
      final String clusterName,
      final String databaseName,
      final AuditInfo auditInfo,
      final String collectionName)
      throws SvcException {
    final String destinationNs = String.format("%s.%s", databaseName, collectionName);
    try {
      _ndsSampleDatasetLoadSvc.loadSearchSynonymSampleDataset(
          groupId, clusterName, auditInfo, destinationNs);
    } catch (final SvcException pE) {
      throw new SvcException(
          NDSErrorCode.CANNOT_LOAD_SAMPLE_DATASET, pE, collectionName, databaseName);
    }
  }

  private Event saveAuditEvent(
      final ObjectId groupId,
      final String clusterName,
      final FTSIndex index,
      final FTSIndexAudit.Type alertType,
      final AuditInfo auditInfo) {
    final List<FTSIndexAudit.IndexDetails> indexDetails =
        Stream.of(index).map(FTSIndexAudit.IndexDetails::new).collect(Collectors.toList());
    return saveAuditEvent(groupId, clusterName, indexDetails, alertType, auditInfo);
  }

  public Event saveAuditEvent(
      final ObjectId groupId,
      final String clusterName,
      final List<FTSIndexAudit.IndexDetails> indexDetails,
      final FTSIndexAudit.Type alertType,
      final AuditInfo auditInfo) {
    final FTSIndexAudit.Builder builder = new FTSIndexAudit.Builder(alertType);
    builder.groupId(groupId);
    builder.hidden(false);
    builder.auditInfo(auditInfo);
    if (groupId != null && clusterName != null) {
      _ndsClusterSvc
          .getActiveCluster(groupId, clusterName)
          .ifPresent(cluster -> builder.clusterId(cluster.getClusterDescription().getUniqueId()));
    }
    builder.clusterName(clusterName);
    builder.affectedIndexes(indexDetails);
    final Event event = builder.build();
    _auditSvc.saveAuditEvent(event);
    return event;
  }

  public int getMaxPossibleIndexFeatureVersion(ObjectId pGroupId) {
    if (!isFeatureFlagEnabled(
        FeatureFlag.ATLAS_SEARCH_INDEX_FEATURE_VERSION_FOUR,
        _appSettings,
        null,
        _groupDao.findById(pGroupId))) {
      return OLDER_INDEX_FEATURE_VERSION;
    }
    final MongotVersion mongotVersion = getMongotVersionForGroupId(pGroupId);
    if (mongotVersion == null || mongotVersion.isLessThan(MIN_MONGOT_VERSION_WITH_FEATURE_4)) {
      return OLDER_INDEX_FEATURE_VERSION;
    }
    return LATEST_INDEX_FEATURE_VERSION;
  }

  public boolean isIndexFeatureVersionIncrementable(ObjectId pGroupId, FTSIndex pIndex) {
    var currentVersion =
        pIndex.getActualType() == FTSIndex.Type.SEARCH
            ? pIndex.toTextSearchIndex().getIndexFeatureVersion()
            : pIndex.toVectorSearchIndex().getIndexFeatureVersion();
    return currentVersion < getMaxPossibleIndexFeatureVersion(pGroupId);
  }

  @Nullable
  public MongotVersion getMongotVersionForGroupId(final ObjectId groupId) {
    final AutomationConfig config = _automationConfigQuerySvc.findPublished(groupId);
    MongotTemplate mongotTemplate = config == null ? null : config.getMongotTemplate();
    return mongotTemplate == null
        ? null
        : MongotVersion.parseBackwardsCompatible(mongotTemplate.getVersion());
  }

  @WithSpan
  public boolean isIndexDetailedStatusesReported(ObjectId groupId) {
    return !isFeatureFlagEnabled(
        FeatureFlag.ATLAS_SEARCH_SKIP_DETAILED_STATUS_WRITE,
        _appSettings,
        null,
        _groupDao.findById(groupId));
  }

  @WithSpan
  public static FTSIndex mtmIndextoTenantIndex(
      final FTSIndex mtmFtsIndex, final Map<String, String> mtmTenantHostMap) {
    final FTSIndex.Builder<?> tenantIndex = mtmFtsIndex.copy();
    // unprefix database
    tenantIndex.setDatabase(mtmDatabaseToTenantDatabase(mtmFtsIndex.getDatabase()));

    // change mtm host names to tenant host names
    final Optional<? extends FTSIndexStatusMap<? extends FTSIndexHostStat>> stats =
        mtmFtsIndex.getStats();
    stats.ifPresent(
        ftsIndexStats ->
            tenantIndex.setStats(mtmStatsToTenantStatuses(ftsIndexStats, mtmTenantHostMap)));
    final Optional<? extends FTSIndexStatusMap<? extends FTSIndexHostDetailedStatuses>>
        detailedStatuses = mtmFtsIndex.getDetailedStatuses();
    detailedStatuses.ifPresent(
        ftsIndexDetailedStatuses ->
            tenantIndex.setDetailedStatuses(
                mtmStatsToTenantStatuses(ftsIndexDetailedStatuses, mtmTenantHostMap)));
    return tenantIndex.build();
  }

  private static String mtmDatabaseToTenantDatabase(final String mtmDatabaseName) {
    return mtmDatabaseName.split("_", 2)[1];
  }

  private static String replaceTenantInDatabase(
      final String mtmDatabaseName, final String newTenantId) {
    return tenantDatabaseToMTMDatabase(mtmDatabaseToTenantDatabase(mtmDatabaseName), newTenantId);
  }

  private static <T extends FTSIndexStatus> FTSIndexStatusMap<T> mtmStatsToTenantStatuses(
      final FTSIndexStatusMap<T> mtmStats, final Map<String, String> mtmTenantHostMap) {
    final FTSIndexStatusMap<T> tenantStats = new FTSIndexStatusMap<>();
    for (final Map.Entry<String, T> hostStat : mtmStats.entrySet()) {
      final String mtmHost = hostStat.getKey();
      final String tenantHost = mtmTenantHostMap.get(mtmHost);
      tenantStats.put(tenantHost, hostStat.getValue());
    }

    return tenantStats;
  }

  public Map<String, String> mtmTenantHostMapping(
      final ObjectId mtmGroupId,
      final String mtmClusterName,
      final ObjectId tenantGroupId,
      final String tenantClusterName)
      throws SvcException {

    final Map<Integer, String> tenantHostMap =
        clusterMemberIndexHostnameMap(tenantGroupId, tenantClusterName);

    final Map<Integer, String> mtmHostMap =
        clusterMemberIndexHostnameMap(mtmGroupId, mtmClusterName);

    return mtmTenantHostMapping(tenantHostMap, mtmHostMap);
  }

  @VisibleForTesting
  static Map<String, String> mtmTenantHostMapping(
      Map<Integer, String> tenantHostMap, Map<Integer, String> mtmHostMap) throws SvcException {
    for (final Entry<Integer, String> mtmHostEntry : mtmHostMap.entrySet()) {
      if (!tenantHostMap.containsKey(mtmHostEntry.getKey())) {
        throw new SvcException(
            NDSErrorCode.FTS_TRANSIENT_ERROR,
            String.format(
                "Tenant host for mtm host %s (memberIndex = %s) not found",
                mtmHostEntry.getValue(), mtmHostEntry.getKey()));
      }
    }
    return mtmHostMap.entrySet().stream()
        .collect(Collectors.toMap(Entry::getValue, entry -> tenantHostMap.get(entry.getKey())));
  }

  private Map<Integer, String> clusterMemberIndexHostnameMap(
      final ObjectId groupId, final String clusterName) {
    return _replicaSetHardwareSvc.getReplicaSetHardware(groupId, clusterName).stream()
        .flatMap(ReplicaSetHardware::getAllHardware)
        .filter(ih -> ih.getHostnameForAgents().isPresent())
        .collect(
            Collectors.toMap(
                InstanceHardware::getMemberIndex,
                ih -> HostnameUtil.getHostnameWithoutDomain(ih.getHostnameForAgents().get())));
  }

  public static FTSIndex tenantIndexToMTMIndex(final FTSIndex tenantIndex, final String tenantId) {
    // prefix database
    final FTSIndex.Builder<?> result = tenantIndex.copy();
    result.setDatabase(tenantDatabaseToMTMDatabase(tenantIndex.getDatabase(), tenantId));

    return result.build();
  }

  private static String tenantDatabaseToMTMDatabase(final String database, final String tenantId) {
    // prefix database
    return String.format("%s_%s", tenantId, database);
  }

  public void logFTSIndexConfigSize(final ObjectId groupId, final String clusterName) {
    // Log FTSIndexConfig BSON size for analytics purposes (Splunk)

    final int ftsIndexConfigSizeBytes =
        _ftsIndexConfigDao.getFTSIndexConfigSizeBytes(groupId, clusterName);

    LOG.info(
        "groupId={} clusterName={} ftsIndexConfigSizeBytes={}",
        groupId,
        clusterName,
        ftsIndexConfigSizeBytes);
  }

  public void logNumberOfTenantFTSIndexes(final ObjectId mtmGroupId, final String mtmClusterName) {
    // Log number of tenant FTS indexes for analytics purposes (Splunk)

    final FTSIndexConfig ftsIndexConfig = getFTSIndexConfig(mtmGroupId, mtmClusterName).get();
    final long numberOfTenantFTSIndexes =
        ftsIndexConfig.getTenantIndexesForAllTypes().values().stream().mapToLong(List::size).sum();

    LOG.info(
        "groupId={} clusterName={} tenantIndexes={}",
        mtmGroupId,
        mtmClusterName,
        numberOfTenantFTSIndexes);
  }

  private Stream<FTSIndex> getMTMIndexesWithLegacyStats(TenantClusterInfo tenantClusterInfo) {
    return getMTMIndexesWithLegacyStats(
        tenantClusterInfo.mtmGroupId(),
        tenantClusterInfo.mtmClusterName(),
        tenantClusterInfo.tenantId());
  }

  private Stream<FTSIndex> getMTMIndexesWithLegacyStats(
      ObjectId mtmGroupId, String mtmClusterName, String tenantId) {
    return getFTSIndexConfigProjectedByTenant(mtmGroupId, mtmClusterName, tenantId).stream()
        .flatMap(
            ftsIndexConfig ->
                ftsIndexConfig.getTenantIndexesByTenantIdForAllTypes(tenantId).stream());
  }

  @VisibleForTesting
  protected Optional<FTSIndex> getMTMIndexesWithLegacyStats(
      TenantClusterInfo tenantClusterInfo, ObjectId indexId) {
    return getMTMIndexesWithLegacyStats(tenantClusterInfo)
        .filter(index -> Objects.equals(index.getIndexId(), indexId))
        .findFirst();
  }

  @VisibleForTesting
  protected Stream<FTSIndex> getMTMIndexesWithLegacyStats(
      TenantClusterInfo tenantClusterInfo,
      String tenantDatabaseName,
      Optional<UUID> collectionUUID,
      Optional<String> indexName) {
    String mtmDatabaseName =
        tenantDatabaseToMTMDatabase(tenantDatabaseName, tenantClusterInfo.tenantId());
    return getMTMIndexesWithLegacyStats(
        tenantClusterInfo,
        indexName,
        (index) ->
            Objects.equals(index.getDatabase(), mtmDatabaseName)
                && Objects.equals(index.getCollectionUUID().get(), collectionUUID));
  }

  @VisibleForTesting
  private Stream<FTSIndex> getMTMIndexesWithLegacyStats(
      TenantClusterInfo tenantClusterInfo,
      Optional<String> indexName,
      Predicate<FTSIndex> indexFilter) {
    return getMTMIndexesWithLegacyStats(tenantClusterInfo)
        .filter(
            index -> {
              if (indexName.isPresent() && !Objects.equals(index.getName(), indexName.get())) {
                return false;
              }
              return indexFilter.test(index);
            });
  }

  @VisibleForTesting
  protected Stream<FTSIndex> getMTMIndexesWithLegacyStats(
      TenantClusterInfo tenantClusterInfo,
      String tenantDatabaseName,
      String leafCollection,
      Optional<String> indexName) {
    String mtmDatabaseName =
        tenantDatabaseToMTMDatabase(tenantDatabaseName, tenantClusterInfo.tenantId());
    return getMTMIndexesWithLegacyStats(
        tenantClusterInfo,
        indexName,
        (index) ->
            Objects.equals(index.getDatabase(), mtmDatabaseName)
                && Objects.equals(index.getLeafCollectionName(), leafCollection));
  }

  /**
   * Finds MTM indexes.
   *
   * @param tenantClusterInfo: Information of MTM cluster.
   * @param tenantDatabaseName: Database that contains the MTM indexes.
   * @param collectionUUID: UUID of collection that contains the MTM indexes.
   * @param indexName: Name of MTM indexes.
   */
  @VisibleForTesting
  protected Stream<FTSIndex> getMTMIndexesWithLegacyStats(
      TenantClusterInfo tenantClusterInfo,
      String tenantDatabaseName,
      UUID collectionUUID,
      Optional<String> indexName) {
    String mtmDatabaseName =
        tenantDatabaseToMTMDatabase(tenantDatabaseName, tenantClusterInfo.tenantId());
    return getMTMIndexesWithLegacyStats(
        tenantClusterInfo,
        indexName,
        (index) ->
            Objects.equals(index.getDatabase(), mtmDatabaseName)
                && Objects.equals(index.getCollectionUUID().get(), collectionUUID));
  }

  private void submitSegmentIndexEvent(
      final String eventType,
      final ObjectId groupId,
      final String clusterName,
      final Optional<ObjectId> clusterId,
      final AuditInfo auditInfo,
      final FTSIndex.Type type,
      final boolean looksLikeSampleData,
      final Optional<String> editor,
      final Optional<Boolean> isPreloadedSampleSearchIndex) {
    final Group group = _groupDao.findById(groupId);
    switch (eventType) {
      case SearchIndexCreatedEvent.EVENT_TYPE:
        _segmentEventSvc.submitEvent(
            SearchIndexCreatedEvent.builder()
                .indexType(type.getStringValue())
                .userId(auditInfo != null ? auditInfo.getAppUserId() : null)
                .groupId(groupId)
                .organizationId(group.getOrgId())
                .clusterName(clusterName)
                .clusterId(clusterId.orElse(null))
                .eventSource(auditInfo != null ? auditInfo.getEventSource().getDisplayText() : null)
                .looksLikeSampleData(looksLikeSampleData)
                .editor(editor.orElse(null))
                .isPreloadedSampleSearchIndex(isPreloadedSampleSearchIndex.orElse(false))
                .build());
        break;
      case SearchIndexUpdatedEvent.EVENT_TYPE:
        _segmentEventSvc.submitEvent(
            SearchIndexUpdatedEvent.builder()
                .indexType(type.getStringValue())
                .userId(auditInfo != null ? auditInfo.getAppUserId() : null)
                .groupId(groupId)
                .organizationId(group.getOrgId())
                .clusterName(clusterName)
                .clusterId(clusterId.orElse(null))
                .eventSource(auditInfo != null ? auditInfo.getEventSource().getDisplayText() : null)
                .looksLikeSampleData(looksLikeSampleData)
                .editor(editor.orElse(null))
                .build());
        break;
      case SearchIndexDeletedEvent.EVENT_TYPE:
        _segmentEventSvc.submitEvent(
            SearchIndexDeletedEvent.builder()
                .indexType(type.getStringValue())
                .userId(auditInfo != null ? auditInfo.getAppUserId() : null)
                .groupId(groupId)
                .organizationId(group.getOrgId())
                .clusterName(clusterName)
                .clusterId(clusterId.orElse(null))
                .eventSource(auditInfo != null ? auditInfo.getEventSource().getDisplayText() : null)
                .looksLikeSampleData(looksLikeSampleData)
                .build());
        break;
      case SearchAnalyzerDefinitionUpdatedEvent.EVENT_TYPE:
        _segmentEventSvc.submitEvent(
            SearchAnalyzerDefinitionUpdatedEvent.builder()
                .userId(auditInfo != null ? auditInfo.getAppUserId() : null)
                .groupId(groupId)
                .organizationId(group.getOrgId())
                .clusterName(clusterName)
                .eventSource(auditInfo != null ? auditInfo.getEventSource().getDisplayText() : null)
                .build());
        break;
      default:
        throw new IllegalArgumentException("Unknown segment index event type: " + eventType);
    }
  }

  private void submitSegmentEvent(
      final ObjectId groupId, final String clusterName, final AuditInfo auditInfo) {
    final Group group = _groupDao.findById(groupId);
    _segmentEventSvc.submitEvent(
        SearchAnalyzerDefinitionUpdatedEvent.builder()
            .userId(auditInfo != null ? auditInfo.getAppUserId() : null)
            .groupId(groupId)
            .organizationId(group.getOrgId())
            .clusterName(clusterName)
            .eventSource(auditInfo != null ? auditInfo.getEventSource().getDisplayText() : null)
            .build());
  }

  private FTSIndex updateIndexFeatureVersion(final ObjectId groupId, final FTSIndex index) {
    if (index.getActualType() == FTSIndex.Type.SEARCH) {
      return index
          .toTextSearchIndex()
          .copy()
          .setIndexFeatureVersion(getMaxPossibleIndexFeatureVersion(groupId))
          .build();
    }
    return index
        .toVectorSearchIndex()
        .copy()
        .setIndexFeatureVersion(getMaxPossibleIndexFeatureVersion(groupId))
        .build();
  }

  private static boolean looksLikeSampleDataNamespace(FTSIndex index) {
    return index.getDatabase().startsWith("sample_");
  }

  private FTSIndex maybeMergeIndexStats(ObjectId groupId, String clusterName, FTSIndex ftsIndex) {
    if (!isFeatureFlagEnabled(
        FeatureFlag.ATLAS_SEARCH_READ_INDEX_STATS_FROM_NEW_COLLECTION,
        _appSettings,
        null,
        _groupDao.findById(groupId))) {
      return ftsIndex;
    }
    return mergeIndexStats(
        ftsIndex,
        _ftsIndexConfigStatsDao.findByClusterNameAndIndexIds(
            groupId, clusterName, List.of(ftsIndex.getIndexId())));
  }

  private List<FTSIndex> maybeMergeIndexStats(
      ObjectId groupId, String clusterName, List<FTSIndex> ftsIndexes) {
    if (ftsIndexes.isEmpty()) {
      return ftsIndexes;
    }
    if (!isFeatureFlagEnabled(
        FeatureFlag.ATLAS_SEARCH_READ_INDEX_STATS_FROM_NEW_COLLECTION,
        _appSettings,
        null,
        _groupDao.findById(groupId))) {
      return ftsIndexes;
    }
    return mergeIndexStats(
        ftsIndexes,
        _ftsIndexConfigStatsDao.findByClusterNameAndIndexIds(
            groupId, clusterName, ftsIndexes.stream().map(FTSIndex::getIndexId).toList()));
  }

  public void updateIndexObservedState(
      FTSIndexConfig ftsIndexConfig,
      Optional<String> tenantId,
      FTSIndex ftsIndex,
      IndexedView observedIndexedView) {
    _ftsIndexConfigDao.updateIndexedView(ftsIndexConfig, tenantId, ftsIndex, observedIndexedView);
  }

  private void validateViewSupport(ClusterDescription clusterDescription, String viewName)
      throws SvcException {
    // If the index is on a view and the MongoDB version doesn't support views, we throw an error
    if (clusterDescription.getMongoDBVersion().isLessThan(MIN_VERSION_FOR_VIEW_SUPPORT)) {
      throw new SvcException(
          NDSErrorCode.INVALID_VIEW_PIPELINE, viewName, "the MongoDB version is below 8.0");
    }
  }

  /**
   * Attempt to delete the index synchronously.
   *
   * @param existingIndex: ID of index to delete.
   * @param groupId: Group of index to delete.
   * @param leafCollection: Name of collection that should contain index.
   * @param databaseName: Name of database that contains index.
   * @return Whether the collection UUID matches the index's collection UUID.
   */
  private boolean collectUuidMatches(
      FTSIndex existingIndex,
      ObjectId groupId,
      String leafCollection,
      String databaseName,
      AuditInfo auditInfo) {
    List<MongoDbCollectionInfo> collections =
        getCollections(groupId, leafCollection, databaseName, auditInfo);
    if (collections == null) {
      return false;
    }

    Map<String, MongoDbCollectionInfo> collectionNameToCollectionInfo =
        collections.stream()
            .collect(Collectors.toMap(MongoDbCollectionInfo::name, Function.identity()));
    MongoDbCollectionInfo collectionInfo = collectionNameToCollectionInfo.get(leafCollection);
    if (collectionInfo == null) {
      return false;
    }

    // Perform the synchronous delete only when the collection UUID matches the one stored in the
    // index.
    return (collectionInfo instanceof MongoDbCollectionInfo.DbCollection rootCollection
        && existingIndex.getCollectionUUID().isPresent()
        && existingIndex.getCollectionUUID().get().equals(rootCollection.info().uuid()));
  }

  /**
   * Check whether deleting indexes synchronously is allowed.
   *
   * @param groupId: Group that contains the index.
   * @return Whether indexes in the group can be deleted synchronously.
   */
  @VisibleForTesting
  public boolean syncIndexDeletionEnabled(ObjectId groupId) {
    return isFeatureFlagEnabled(
        FeatureFlag.ENABLE_SYNC_INDEX_DELETION, _appSettings, null, _groupDao.findById(groupId));
  }

  /**
   * Checks whether similarity method in auto-embedding index needs to be migrated.
   *
   * @return true if autoEmbeddingSimilarityMigration are enabled for this group, false otherwise
   */
  @VisibleForTesting
  public boolean isSimilarityMethodMigrationEnabled(ObjectId groupId) {
    if (_appSettings.isFeatureFlagInEnabledState(
        FeatureFlag.ATLAS_SEARCH_AUTO_EMBEDDING_SIMILARITY_MIGRATION)) {
      return true;
    }
    var group = _groupDao.findById(groupId);
    if (group == null) {
      return false;
    }
    return isFeatureFlagEnabled(
        FeatureFlag.ATLAS_SEARCH_AUTO_EMBEDDING_SIMILARITY_MIGRATION,
        _appSettings,
        null, // no need to use for ATLAS_SEARCH_AUTO_EMBEDDING_SIMILARITY_MIGRATION
        group);
  }

  /**
   * Appends dot product similarity method in auto-embedding vector search index config if
   * isSimilarityMethodMigrationEnabled is true.
   *
   * @return modified FTSIndex if isSimilarityMethodMigrationEnabled is true, or original index if
   *     false.
   */
  @VisibleForTesting
  public FTSIndex appendSimilarityMethodIfNeeded(FTSIndex newIndex, ObjectId groupId) {
    if (newIndex.getActualType() != FTSIndex.Type.VECTOR_SEARCH) {
      return newIndex;
    }
    if (!isSimilarityMethodMigrationEnabled(groupId)) {
      return newIndex;
    }
    var ftsVectorSearchIndex = newIndex.toVectorSearchIndex();
    if (!FTSIndexConfigUtil.autoEmbeddingIndexExists(List.of(ftsVectorSearchIndex))) {
      return newIndex;
    }
    if (ftsVectorSearchIndex.getFields().isEmpty()) {
      return newIndex;
    }
    FTSVectorSearchIndex modifiedVectorSearchIndex =
        ftsVectorSearchIndex
            .copy()
            .setFields(
                ftsVectorSearchIndex.getFields().get().stream()
                    .map(basicDBObject -> (BasicDBObject) basicDBObject.copy())
                    .toList())
            .build();
    modifiedVectorSearchIndex.getFields().stream()
        .flatMap(dbObjects -> dbObjects.stream())
        .filter(
            basicDBObject ->
                BasicDBObjectUtil.getOptionalField(
                        basicDBObject, FTSIndex.FieldDefs.TYPE, String.class)
                    .filter(fieldType -> fieldType.equalsIgnoreCase("TEXT"))
                    .isPresent())
        .forEach(basicDBObject -> basicDBObject.append("similarity", "dotProduct"));
    return modifiedVectorSearchIndex;
  }

  private void countSynchronousDeletion(FTSIndex existingIndex, ObjectId pGroupId) {
    LOG.info("Synchronously deleting index {} in group {}", existingIndex.getIndexId(), pGroupId);
    SEARCH_INDEX_DELETE_COUNTER.labels(SEARCH_INDEX_SYNC_DELETE_VALUE).inc();
  }

  private void countAsynchronousDeletion(FTSIndex existingIndex, ObjectId pGroupId) {
    LOG.info("Asynchronously deleting index {} in group {}", existingIndex.getIndexId(), pGroupId);
    SEARCH_INDEX_DELETE_COUNTER.labels(SEARCH_INDEX_ASYNC_DELETE_VALUE).inc();
  }

  private void submitAuditAndSegmentIndexEvents(
      ObjectId pGroupId, String pClusterName, FTSIndex existingIndex, AuditInfo pAuditInfo) {
    var event =
        saveAuditEvent(
            pGroupId,
            pClusterName,
            existingIndex,
            FTSIndexAudit.Type.FTS_INDEX_DELETED,
            pAuditInfo);
    submitSegmentIndexEvent(
        SearchIndexDeletedEvent.EVENT_TYPE,
        pGroupId,
        pClusterName,
        getClusterId(event),
        pAuditInfo,
        existingIndex.getActualType(),
        looksLikeSampleDataNamespace(existingIndex),
        Optional.empty(),
        Optional.empty());
  }

  private boolean validCollection(
      Optional<UUID> collectionUUID,
      FTSIndex existingIndex,
      ObjectId groupId,
      String leafCollection,
      String databaseName,
      AuditInfo auditInfo) {
    return collectionUUID.isPresent()
        || collectUuidMatches(existingIndex, groupId, leafCollection, databaseName, auditInfo);
  }
}
