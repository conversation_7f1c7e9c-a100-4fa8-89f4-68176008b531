package com.xgen.svc.nds.svc;

import static com.xgen.cloud.access.role._public.model.RoleSet.GROUP_DATA_ACCESS_ANY;
import static com.xgen.cloud.access.role._public.model.RoleSet.GROUP_STREAM_PROCESSING_OWNER;
import static com.xgen.cloud.access.role._public.model.RoleSet.ORG_STREAM_PROCESSING_ADMIN;
import static com.xgen.cloud.common.model._public.error.CommonErrorCode.INVALID_OPTIONS;
import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.DUPLICATE_KEY_ERROR_CODE;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.INVALID_ARGUMENT;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.STREAM_NOT_AWS_CONNECTION;
import static com.xgen.cloud.nds.datalake._public.util.DataLakeTenantUtil.getStreamTenantName;
import static com.xgen.cloud.nds.streams._public.model.StreamsConnectionType.AWSKinesisDataStreams;
import static com.xgen.cloud.nds.streams._public.model.StreamsConnectionType.AWSLambda;
import static com.xgen.cloud.nds.streams._public.model.StreamsConnectionType.Cluster;
import static com.xgen.cloud.nds.streams._public.model.StreamsConnectionType.Kafka;
import static com.xgen.cloud.nds.streams._public.model.StreamsConnectionType.S3;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.services.ec2.model.CreateTransitGatewayVpcAttachmentResult;
import com.amazonaws.services.ec2.model.Route;
import com.amazonaws.services.ec2.model.RouteTable;
import com.amazonaws.services.ram.model.GetResourceShareInvitationsResult;
import com.amazonaws.services.ram.model.ResourceShareInvitation;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ListBucketsPaginatedRequest;
import com.amazonaws.services.s3.model.ListBucketsPaginatedResult;
import com.mongodb.ErrorCategory;
import com.mongodb.MongoException;
import com.mongodb.MongoWriteException;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.access.rolecheck._public.svc.RoleSetSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.authn._public.svc.AuthnOAuthClient;
import com.xgen.cloud.common.aws._public.AwsUtils;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.NetUtils;
import com.xgen.cloud.customermetrics._public.model.query.InstantQueryRequest;
import com.xgen.cloud.customermetrics._public.model.query.RangeQueryRequest;
import com.xgen.cloud.customermetrics._public.svc.CustomerMetricsQuerySvc;
import com.xgen.cloud.customermetrics._public.view.PromQLResponseView;
import com.xgen.cloud.customermetrics._public.view.PromQLResponseView.Data;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorConnectionCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorConnectionDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorConnectionUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstanceCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstanceDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstanceUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.aws._public.model.NDSAWSTempCredentials;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.util.ModelValidationUtils;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.DataLakeType;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeTenantId;
import com.xgen.cloud.nds.datalake._public.model.ui.NDSDataLakeDataProcessRegionView;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.streams._private.dao.AWSLambdaConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsAWSKinesisDataStreamsConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsClusterConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsHttpsConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsKafkaConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsPrivateLinkDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsS3ConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsSampleConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsTenantDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsTransitGatewayAttachmentsDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsTransitGatewayResourceSharesDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsTransitGatewayRouteDao;
import com.xgen.cloud.nds.streams._public.model.AWSKinesisDataStreamsConnection;
import com.xgen.cloud.nds.streams._public.model.AWSLambdaConnection;
import com.xgen.cloud.nds.streams._public.model.ClusterConnection;
import com.xgen.cloud.nds.streams._public.model.DBRoleType;
import com.xgen.cloud.nds.streams._public.model.HttpsConnection;
import com.xgen.cloud.nds.streams._public.model.KafkaConnection;
import com.xgen.cloud.nds.streams._public.model.ProxyInfo;
import com.xgen.cloud.nds.streams._public.model.S3Connection;
import com.xgen.cloud.nds.streams._public.model.SampleConnection;
import com.xgen.cloud.nds.streams._public.model.StreamConfig;
import com.xgen.cloud.nds.streams._public.model.StreamsConnection;
import com.xgen.cloud.nds.streams._public.model.StreamsConnection.StreamsConnectionState;
import com.xgen.cloud.nds.streams._public.model.StreamsConnectionType;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLink;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLinkProviderType;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLinkState;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLinkVendorType;
import com.xgen.cloud.nds.streams._public.model.StreamsTenant;
import com.xgen.cloud.nds.streams._public.model.StreamsTenantAndProjectId;
import com.xgen.cloud.nds.streams._public.model.StreamsTransitGateway;
import com.xgen.cloud.nds.streams._public.model.StreamsTransitGatewayAttachment;
import com.xgen.cloud.nds.streams._public.model.StreamsTransitGatewayRoute;
import com.xgen.cloud.nds.streams._public.model.Tier;
import com.xgen.cloud.nds.streams._public.model.ui.AWSLambdaConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.ClusterConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.DLQ;
import com.xgen.cloud.nds.streams._public.model.ui.StreamConfigView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamProcessorCreateView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamProcessorGetView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamProcessorView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsAWSKinesisDataStreamsConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsConnectionView.FieldDefs;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsHttpsConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsS3ConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsSampleConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsTenantView;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.StreamsKafkaConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.authentication.NDSDataLakeKafkaAuth;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.NDSDataLakeStreamsNetworking;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.security.NDSDataLakeKafkaSecurityProtocol;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.security.NDSDataLakeKafkaSecurityProtocol.Protocols;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.security.NDSDataLakeKafkaSecuritySASLSSL;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.security.NDSDataLakeKafkaSecuritySSL;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.payments.standalone.common._public.gateway.PaymentMethodGateway;
import com.xgen.cloud.streams._public.model.NodeHardwareDescription;
import com.xgen.cloud.streams._public.model.StreamsMetric;
import com.xgen.cloud.streams._public.model.VPCPeeringConnection;
import com.xgen.cloud.streams._public.model.VPCPeeringConnection.Status;
import com.xgen.cloud.streams._public.model.VPCProxyDeploymentDescription;
import com.xgen.cloud.streams._public.model.VPCProxyInstanceDescription;
import com.xgen.cloud.streams._public.model.aws.AWSNodeHardwareDescription;
import com.xgen.cloud.streams._public.model.aws.AWSStreamsInstanceSize;
import com.xgen.cloud.streams._public.model.azure.AzureNodeHardwareDescription;
import com.xgen.cloud.streams._public.model.azure.AzureStreamsInstanceSize;
import com.xgen.cloud.streams._public.model.gcp.GCPNodeHardwareDescription;
import com.xgen.cloud.streams._public.model.gcp.GCPStreamsInstanceSize;
import com.xgen.cloud.streams._public.model.view.ApiStreamsListResourceShareView;
import com.xgen.cloud.streams._public.model.view.ApiStreamsPrivateLinkView;
import com.xgen.cloud.streams._public.model.view.ApiStreamsResourceShareView;
import com.xgen.cloud.streams._public.model.view.tgw.ApiStreamsTransitGatewayAttachmentRequestView;
import com.xgen.cloud.streams._public.model.view.tgw.ApiStreamsTransitGatewayResourceShareView;
import com.xgen.cloud.streams._public.model.view.tgw.ApiStreamsTransitGatewayRouteRequestView;
import com.xgen.cloud.streams._public.svc.IVPCProxyDeploymentDescriptionSvc;
import com.xgen.cloud.streams._public.svc.VPCPeeringConnectionSvc;
import com.xgen.cloud.streams._public.svc.VPCProxyInstanceDescriptionSvc;
import com.xgen.cloud.streams._public.util.StreamsConstants;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.mhouse.services.logs.v1.Models;
import com.xgen.svc.nds.factory.VPCProxyFactory;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.svc.nds.model.ui.StreamsMetricsView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessAWSIAMRoleView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.svc.streams.planner.StreamsUtil;
import io.prometheus.client.Counter;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.core.StreamingOutput;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.net.util.SubnetUtils;
import org.bson.BsonDateTime;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class StreamsSvc {

  private static final String NDS_STREAMS_INSTANCE_EXCEPTION_COUNT_NAME =
      "mms_nds_streams_svc_instance_error_total";
  private static final String NDS_STREAMS_INSTANCE_EXCEPTION_COUNT_HELP =
      "Exceptions encountered with Streams Processor Instances";
  private static final Counter NDS_STREAMS_INSTANCE_EXCEPTION_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          NDS_STREAMS_INSTANCE_EXCEPTION_COUNT_NAME,
          NDS_STREAMS_INSTANCE_EXCEPTION_COUNT_HELP,
          "type",
          "method");

  private static final String NDS_STREAMS_CONNECTION_EXCEPTION_COUNT_NAME =
      "mms_nds_streams_svc_connection_error_total";
  private static final String NDS_STREAMS_CONNECTION_EXCEPTION_COUNT_HELP =
      "Exceptions encountered with Streams Processor Connections";
  private static final Counter NDS_STREAMS_CONNECTION_EXCEPTION_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          NDS_STREAMS_CONNECTION_EXCEPTION_COUNT_NAME,
          NDS_STREAMS_CONNECTION_EXCEPTION_COUNT_HELP,
          "type",
          "method");

  private static final String BUILT_IN_ROLE_ATLAS_ADMIN = "atlasAdmin";
  private static final String BUILT_IN_ROLE_READ_ANY_DATABASE = "readAnyDatabase";
  private static final String BUILT_IN_ROLE_READ_WRITE_ANY_DATABASE = "readWriteAnyDatabase";
  private static final List<String> ALLOWED_ROUTES_IN_DEFAULT_ROUTABLE_IN_ATLAS_VPC =
      List.of("0.0.0.0/0");
  private static final String AZURE_CONFLUENT_PRIVATE_LINK_RESOURCE_ID_PATTERN =
      "^/subscriptions/.*/resourceGroups/.*/providers/Microsoft.Network/privateLinkServices/.*-privatelink-\\d+$";
  private static final String GCP_SERVICE_ATTACHMENT_URI_PATTERN =
      "^projects/[^/]+/regions/[^/]+/serviceAttachments/[^/]+$";

  private static final StreamsConnectionView DEFAULT_SAMPLE_SOLAR_CONNECTION =
      new StreamsSampleConnectionView("sample_stream_solar");

  private static final String AWS_ERROR_CODE_ACCESS_DENIED = "AccessDenied";

  private static final Logger LOG = LoggerFactory.getLogger(StreamsSvc.class);
  private static final int retryMax = 5;

  private final NDSGroupSvc _ndsGroupSvc;
  private final GroupSvc _groupSvc;
  private final RoleSetSvc _roleSetSvc;
  private final ClusterDescriptionDao _clusterDescriptionDao;
  private final StreamsTenantDao _streamsTenantDao;
  private final StreamsKafkaConnectionDao _kafkaConnectionDao;
  private final StreamsClusterConnectionDao _clusterConnectionDao;
  private final StreamsSampleConnectionDao _sampleConnectionDao;
  private final StreamsHttpsConnectionDao _httpsConnectionDao;
  private final StreamsPrivateLinkDao _privateLinkDao;
  private final StreamsTransitGatewayRouteDao _transitGatewayRouteDao;
  private final StreamsTransitGatewayAttachmentsDao _transitGatewayAttachmentsDao;
  private final AWSLambdaConnectionDao _awsLambdaConnectionDao;
  private final StreamsS3ConnectionDao _s3ConnectionDao;
  private final StreamsTransitGatewayResourceSharesDao _transitGatewayResourceSharesDao;
  private final StreamsAWSKinesisDataStreamsConnectionDao _kinesisConnectionDao;

  private final AWSAccountDao _awsAccountDao;
  private final AzureSubscriptionDao _azureSubscriptionDao;
  private final StreamsProcessManagerSvc _spmSvc;
  private final SegmentEventSvc _segmentEventSvc;
  private final FeatureFlagSvc _featureFlagSvc;
  private final StreamsDataLakePrivateSvc _streamsDataLakePrivateSvc;
  private final IVPCProxyDeploymentDescriptionSvc _vpcDeploymentProxy;
  private final NDSCloudProviderContainerSvc _containerSvc;
  private final CustomerMetricsQuerySvc _customerMetricsQuerySvc;
  private final AppEnv _appEnv;

  private final AuditSvc _auditSvc;

  private final NDSUISvc _ndsUISvc;
  private final VPCProxyInstanceDescriptionSvc _vpcProxyInstanceDescriptionSvc;

  private final VPCPeeringConnectionSvc _vpcPeeringConnectionSvc;

  private final AWSApiSvc _awsApiSvc;

  private final PaymentMethodGateway _paymentMethodGateway;

  private final OrganizationSvc _organizationSvc;

  @Inject
  public StreamsSvc(
      final NDSGroupSvc pNdsGroupSvc,
      final GroupSvc pGroupSvc,
      final RoleSetSvc pRoleSetSvc,
      final NDSDataLakePublicSvc pDataLakePublicSvc,
      final NDSDataLakeTenantSvc pDataLakeTenantSvc,
      final ClusterDescriptionDao pClusterSvc,
      final StreamsTenantDao pStreamsTenantDao,
      final StreamsKafkaConnectionDao pKafkaConnectionDao,
      final StreamsClusterConnectionDao pClusterConnectionDao,
      final StreamsSampleConnectionDao pSampleConnectionDao,
      final StreamsHttpsConnectionDao pHttpsConnectionDao,
      final StreamsPrivateLinkDao pPrivateLinkDao,
      final AWSLambdaConnectionDao pAwsLambdaConnectionDao,
      final StreamsS3ConnectionDao pS3ConnectionDao,
      final StreamsAWSKinesisDataStreamsConnectionDao pKinesisConnectionDao,
      final AWSAccountDao pAwsAccountDao,
      final AzureSubscriptionDao pAzureSubscriptionDao,
      final StreamsTransitGatewayResourceSharesDao pTransitGatewayResourceSharesDao,
      final StreamsTransitGatewayRouteDao pStreamsTransitGatewayRouteDao,
      final StreamsTransitGatewayAttachmentsDao pStreamsTransitGatewayAttachmentsDao,
      final StreamsProcessManagerSvc pSpmSvc,
      final SegmentEventSvc pSegmentEventSvc,
      final AuditSvc pAuditSvc,
      final NDSUISvc pNdsUISvc,
      final FeatureFlagSvc pFeatureFlagSvc,
      final VPCProxyInstanceDescriptionSvc pVPCProxyInstanceDescriptionSvc,
      final VPCPeeringConnectionSvc pVPCPeeringConnectionSvc,
      final VPCProxyFactory pVpcProxyFactory,
      final NDSCloudProviderContainerSvc pContainerSvc,
      final CustomerMetricsQuerySvc pCustomerMetricsQuerySvc,
      final AWSApiSvc pAWSApiSvc,
      final AppSettings pAppSettings,
      final PaymentMethodGateway pPaymentMethodGateway,
      final OrganizationSvc organizationSvc) {
    _streamsTenantDao = pStreamsTenantDao;
    _streamsDataLakePrivateSvc =
        new StreamsDataLakePrivateSvc(retryMax, pDataLakePublicSvc, pDataLakeTenantSvc);
    _clusterDescriptionDao = pClusterSvc;
    _ndsGroupSvc = pNdsGroupSvc;
    _groupSvc = pGroupSvc;
    _roleSetSvc = pRoleSetSvc;
    _clusterConnectionDao = pClusterConnectionDao;
    _kafkaConnectionDao = pKafkaConnectionDao;
    _sampleConnectionDao = pSampleConnectionDao;
    _httpsConnectionDao = pHttpsConnectionDao;
    _awsLambdaConnectionDao = pAwsLambdaConnectionDao;
    _s3ConnectionDao = pS3ConnectionDao;
    _transitGatewayResourceSharesDao = pTransitGatewayResourceSharesDao;
    _transitGatewayRouteDao = pStreamsTransitGatewayRouteDao;
    _transitGatewayAttachmentsDao = pStreamsTransitGatewayAttachmentsDao;
    _kinesisConnectionDao = pKinesisConnectionDao;
    _spmSvc = pSpmSvc;
    _segmentEventSvc = pSegmentEventSvc;
    _auditSvc = pAuditSvc;
    _ndsUISvc = pNdsUISvc;
    _featureFlagSvc = pFeatureFlagSvc;
    _vpcProxyInstanceDescriptionSvc = pVPCProxyInstanceDescriptionSvc;
    _vpcPeeringConnectionSvc = pVPCPeeringConnectionSvc;
    _vpcDeploymentProxy = pVpcProxyFactory.createProxy();
    _privateLinkDao = pPrivateLinkDao;
    _awsAccountDao = pAwsAccountDao;
    _azureSubscriptionDao = pAzureSubscriptionDao;
    _containerSvc = pContainerSvc;
    _customerMetricsQuerySvc = pCustomerMetricsQuerySvc;
    _appEnv = pAppSettings.getAppEnv();
    _awsApiSvc = pAWSApiSvc;
    _paymentMethodGateway = pPaymentMethodGateway;
    _organizationSvc = organizationSvc;
  }

  private List<StreamsConnectionDao<? extends StreamsConnection>> getAllConnectionDaos() {
    return Arrays.stream(StreamsConnectionType.values())
        .map(
            t ->
                switch (t) {
                  case Kafka -> _kafkaConnectionDao;
                  case Cluster -> _clusterConnectionDao;
                  case Sample -> _sampleConnectionDao;
                  case Https -> _httpsConnectionDao;
                  case AWSLambda -> _awsLambdaConnectionDao;
                  case S3 -> _s3ConnectionDao;
                  case AWSKinesisDataStreams -> _kinesisConnectionDao;
                })
        .collect(Collectors.toList());
  }

  public record BulkDeleteSummary(int found, int deleted, int errors) {
    public int getFound() {
      return found;
    }

    public int getDeleted() {
      return deleted;
    }

    public int getErrors() {
      return errors;
    }
  }

  public BulkDeleteSummary bulkDeleteConnections(
      final ObjectId groupId,
      final ObjectId tenantIdOrNull,
      final String name,
      final boolean isPrefix,
      final AppUser pAppUser)
      throws SvcException {
    LOG.atInfo()
        .setMessage(
            String.format(
                "BulkDeleteConnections: Bulk deleting connections for groupId=%s, tenantId=%s,"
                    + " connectionName=%s, isPrefix=%s, initiated by user=%s",
                groupId,
                tenantIdOrNull == null ? "omitted" : tenantIdOrNull,
                name,
                isPrefix,
                pAppUser == null ? "unknown" : pAppUser.getAuid()))
        .log();
    final List<ObjectId> tenantIds;
    if (tenantIdOrNull != null) {
      tenantIds = java.util.List.of(tenantIdOrNull);
    } else {
      tenantIds =
          _streamsTenantDao.findByGroupId(groupId).stream()
              .map(com.xgen.cloud.nds.streams._public.model.StreamsTenant::getId)
              .toList();
    }
    int found = 0;
    int deleted = 0;
    int errors = 0;
    for (ObjectId tid : tenantIds) {
      // Delete across all connection types we manage (compile-time enforced by exhaustive switch)
      for (var dao : getAllConnectionDaos()) {
        StreamsSvc.DeleteRun deleteRun =
            deleteConnectionByNameOrPrefix(dao, groupId, tid, name, isPrefix, pAppUser);
        found += deleteRun.found;
        deleted += deleteRun.deleted;
        errors += deleteRun.errors;
      }
    }
    LOG.atInfo()
        .setMessage(
            String.format(
                "BulkDeleteConnections: Bulk deleting connections for groupId=%s, tenantId=%s,"
                    + " connectionName=%s with isPrefix=%s returned %d connections, %d deletes and"
                    + " %d errors",
                groupId,
                tenantIdOrNull == null ? "omitted" : tenantIdOrNull,
                name,
                isPrefix,
                found,
                deleted,
                errors))
        .log();
    return new BulkDeleteSummary(found, deleted, errors);
  }

  private static final class DeleteRun {
    int found;
    int deleted;
    int errors;
  }

  private <T extends com.xgen.cloud.nds.streams._public.model.StreamsConnection>
      DeleteRun deleteConnectionByNameOrPrefix(
          final com.xgen.cloud.nds.streams._private.dao.StreamsConnectionDao<T> dao,
          final ObjectId groupId,
          final ObjectId tenantId,
          final String name,
          final boolean isPrefix,
          final AppUser pAppUser) {
    final var matches = dao.findByTenantAndNameOrPrefix(tenantId, name, isPrefix);
    DeleteRun run = new DeleteRun();
    run.found = matches.size();
    if (matches.isEmpty()) {
      return run;
    }
    final Group group = _groupSvc.findById(groupId);
    if (group == null) {
      run.errors += matches.size();
      LOG.atWarn()
          .setMessage(
              String.format(
                  "BulkDeleteConnection: Group not found for groupId=%s, counting %d matches as"
                      + " errors",
                  groupId, matches.size()))
          .log();
      return run;
    }
    final String tenantName;
    final StreamsTenant streamsTenant;
    try {
      streamsTenant = getStreamsTenantById(groupId, tenantId);
      tenantName = streamsTenant.getName();
    } catch (SvcException e) {
      // Count all matches towards errors for this tenant since tenant lookup failed
      run.errors += matches.size();
      LOG.atWarn()
          .setMessage(
              String.format(
                  "BulkDeleteConnection: tenant lookup failed for groupId=%s, tenantId=%s, counting"
                      + " %d matches as errors; errorCode=%s",
                  groupId, tenantId, matches.size(), e.getErrorCode().name()))
          .log();
      return run;
    }
    for (final var conn : matches) {
      try {
        // Route deletion through the existing service to ensure full cleanup and auditing
        deleteConnection(
            conn.getName(), streamsTenant, group, AuditInfoHelpers.fromInternal(), pAppUser);
        run.deleted++;
      } catch (SvcException e) {
        // If ADL store is missing, the local DB entry was already removed above. Count as deleted.
        if (e.getErrorCode() == NDSErrorCode.STREAM_CONNECTION_NOT_FOUND
            || e.getErrorCode() == NDSErrorCode.STREAM_TENANT_NOT_FOUND_FOR_NAME) {
          LOG.atWarn()
              .setMessage(
                  String.format(
                      "BulkDeleteConnection: ADL store missing for connection=%s, tenantName=%s,"
                          + " groupId=%s, counting as deleted",
                      conn.getName(), tenantName, group.getId()))
              .log();
          run.deleted++;
          continue;
        }
        run.errors++;
        LOG.atWarn()
            .setMessage(
                String.format(
                    "BulkDeleteConnection: SvcException deleting connection=%s, tenantName=%s,"
                        + " groupId=%s, errorCode=%s, message=%s",
                    conn.getName(),
                    tenantName,
                    group.getId(),
                    e.getErrorCode().name(),
                    e.getMessage()))
            .log();
      } catch (Exception e) {
        run.errors++;
        LOG.atError()
            .setMessage(
                String.format(
                    "BulkDeleteConnection: Unexpected exception deleting connection=%s,"
                        + " tenantName=%s, groupId=%s, message=%s",
                    conn.getName(), tenantName, group.getId(), e.getMessage()))
            .log();
      }
    }
    return run;
  }

  private static Supplier<SvcException> tenantNotFound(ObjectId pGroupId, String pTenantName) {
    NDSErrorCode errorCode = NDSErrorCode.STREAM_TENANT_NOT_FOUND_FOR_NAME;
    return () -> new SvcException(errorCode, pGroupId, pTenantName);
  }

  private static Supplier<SvcException> connectionNotFound(
      String pTenantName, String pConnectionName) {
    NDSErrorCode errorCode = NDSErrorCode.STREAM_CONNECTION_NOT_FOUND;
    return () -> new SvcException(errorCode, pTenantName, pConnectionName);
  }

  public NDSGroup getNdsGroupByTenant(final StreamsTenant tenant) throws SvcException {
    return getNdsGroupById(tenant.getGroupId());
  }

  public NDSGroup getNdsGroupById(final ObjectId groupId) throws SvcException {
    try {

      return _ndsGroupSvc
          .find(groupId)
          .orElseThrow(
              () ->
                  new IllegalArgumentException(
                      String.format("No NDS Group found for id (%s)", groupId)));
    } catch (IllegalArgumentException e) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
    }
  }

  public RegionName getRegionName(final StreamsTenant tenant, final CloudProvider cloudProvider)
      throws SvcException {
    return switch (cloudProvider) {
      case AWS -> getAwsRegionName(getDataLakeTenant(tenant));
      case AZURE -> getAzureRegionName(getDataLakeTenant(tenant));
      case GCP -> getGCPRegionName(getDataLakeTenant(tenant));
      default -> throw new SvcException(NDSErrorCode.REGION_NOT_SUPPORTED);
    };
  }

  public AWSRegionName getAwsRegionName(final StreamsTenant tenant) throws SvcException {
    return getAwsRegionName(getDataLakeTenant(tenant));
  }

  public AWSRegionName getAwsRegionName(final NDSDataLakeTenant dataLakeTenant)
      throws SvcException {
    Optional<RegionName> regionName =
        dataLakeTenant.getDataProcessRegion().getDataProcessRegionAsRegionName();

    if (regionName.isEmpty()
        || regionName.get().getProvider() != CloudProvider.AWS
        || !(regionName.get() instanceof AWSRegionName)) {
      throw new SvcException(NDSErrorCode.STREAM_SUPPORTED_REGIONS_NOT_LOADED);
    }

    return (AWSRegionName) regionName.get();
  }

  public AzureRegionName getAzureRegionName(final StreamsTenant tenant) throws SvcException {
    return getAzureRegionName(getDataLakeTenant(tenant));
  }

  public AzureRegionName getAzureRegionName(final NDSDataLakeTenant dataLakeTenant)
      throws SvcException {
    Optional<RegionName> regionName =
        dataLakeTenant.getDataProcessRegion().getDataProcessRegionAsRegionName();

    if (regionName.isEmpty()
        || regionName.get().getProvider() != CloudProvider.AZURE
        || !(regionName.get() instanceof AzureRegionName)) {
      throw new SvcException(NDSErrorCode.STREAM_SUPPORTED_REGIONS_NOT_LOADED);
    }

    return (AzureRegionName) regionName.get();
  }

  public GCPRegionName getGCPRegionName(final StreamsTenant tenant) throws SvcException {
    return getGCPRegionName(getDataLakeTenant(tenant));
  }

  public GCPRegionName getGCPRegionName(final NDSDataLakeTenant dataLakeTenant)
      throws SvcException {
    Optional<RegionName> regionName =
        dataLakeTenant.getDataProcessRegion().getDataProcessRegionAsRegionName();

    if (regionName.isEmpty()
        || regionName.get().getProvider() != CloudProvider.GCP
        || !(regionName.get() instanceof GCPRegionName)) {
      throw new SvcException(NDSErrorCode.STREAM_SUPPORTED_REGIONS_NOT_LOADED);
    }

    return (GCPRegionName) regionName.get();
  }

  public List<RegionView> getAvailableRegions(@Nullable Group pGroup) throws SvcException {
    try {
      return _streamsDataLakePrivateSvc
          .noLockDataLakeTenantSvc()
          .getDataLakeCloudProviderRegions()
          .stream()
          .map(RegionView::new)
          .filter(
              regionView -> {
                CloudProvider cloudProvider = CloudProvider.valueOf(regionView.getProvider());
                try {
                  switch (cloudProvider) {
                    case AWS ->
                        StreamsConstants.validateRegion(
                            cloudProvider,
                            regionView.getKey(),
                            _appEnv,
                            _featureFlagSvc.isFeatureFlagEnabled(
                                FeatureFlag.STREAMS_ENABLE_ADDITIONAL_REGIONS, null, pGroup));
                    case AZURE -> {
                      if (!_featureFlagSvc.isFeatureFlagEnabled(
                          FeatureFlag.STREAMS_ENABLE_AZURE, null, pGroup)) {
                        return false;
                      }
                      StreamsConstants.validateRegion(
                          cloudProvider,
                          regionView.getKey(),
                          _appEnv,
                          _featureFlagSvc.isFeatureFlagEnabled(
                              FeatureFlag.STREAMS_ENABLE_ADDITIONAL_AZURE_REGIONS, null, pGroup));
                    }
                    case GCP ->
                        StreamsConstants.validateRegion(
                            cloudProvider, regionView.getKey(), _appEnv, false);
                    default -> {
                      return false;
                    }
                  }
                  return true;
                } catch (SvcException e) {
                  // Region is not supported.
                  return false;
                }
              })
          .collect(Collectors.toList());

    } catch (SvcException e) {
      if (e.getErrorCode() == NDSErrorCode.DATA_FEDERATION_SUPPORTED_REGIONS_NOT_LOADED) {
        throw new SvcException(NDSErrorCode.STREAM_SUPPORTED_REGIONS_NOT_LOADED);
      }
      throw e;
    }
  }

  public List<StreamsTenantView> findByGroupId(final ObjectId pGroupId) throws SvcException {
    final Map<ObjectId, NDSDataLakeTenant> dataLakeMap =
        _streamsDataLakePrivateSvc
            .noLockDataLakeTenantSvc()
            .findTenantsByGroupAndType(pGroupId, DataLakeType.STREAM)
            .stream()
            .collect(Collectors.toMap(NDSDataLakeTenant::getTenantId, Function.identity()));
    List<StreamsTenant> streamsTenants = _streamsTenantDao.findByGroupId(pGroupId);
    return streamsTenants.stream()
        .peek(
            tenant -> {
              // Skip the stream tenant if there is no data lake entry for it.
              if (!dataLakeMap.containsKey(tenant.getId())) {
                LOG.warn("No data lake entry for tenant {}, skipping tenant", tenant.getId());
              }
            })
        .filter(tenant -> dataLakeMap.containsKey(tenant.getId()))
        .map(tenant -> toNDSStreamsTenantView(tenant, dataLakeMap.get(tenant.getId())))
        .collect(Collectors.toList());
  }

  public boolean isMissingTenant(final ObjectId pGroup, final String pName) {
    return _streamsTenantDao.findByGroupAndName(pGroup, pName).isEmpty();
  }

  public StreamsTenantView findTenantOnly(final ObjectId pGroupId, final String pName)
      throws SvcException {
    final StreamsTenant streamsTenant = getStreamsTenant(pGroupId, pName);
    final NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);
    return toNDSStreamsTenantView(streamsTenant, dataLakeTenant);
  }

  public String findTenantRegion(final ObjectId pGroupId, final ObjectId pTenantId)
      throws SvcException {
    final StreamsTenant streamsTenant = getStreamsTenantById(pGroupId, pTenantId);
    final NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);
    return dataLakeTenant.getDataProcessRegion().getRegion();
  }

  private Map<String, StreamsConnection> getAllConnections(final ObjectId pTenantId) {
    List<StreamsConnection> allConnections = getConnectionList(pTenantId);
    return allConnections.stream()
        .collect(Collectors.toMap(StreamsConnection::getName, Function.identity()));
  }

  private List<StreamsConnection> getConnectionList(final ObjectId pTenantId) {
    List<StreamsConnection> connections = new ArrayList<>();
    connections.addAll(_kafkaConnectionDao.fetchByTenantIdAndType(pTenantId));
    connections.addAll(_clusterConnectionDao.fetchByTenantIdAndType(pTenantId));
    connections.addAll(_sampleConnectionDao.fetchByTenantIdAndType(pTenantId));
    connections.addAll(_httpsConnectionDao.fetchByTenantIdAndType(pTenantId));
    connections.addAll(_awsLambdaConnectionDao.fetchByTenantIdAndType(pTenantId));
    connections.addAll(_s3ConnectionDao.fetchByTenantIdAndType(pTenantId));
    connections.addAll(_kinesisConnectionDao.fetchByTenantIdAndType(pTenantId));
    return connections;
  }

  public Map<String, String> getConnectionIds(final ObjectId pTenantId) {
    List<StreamsConnection> connections = getConnectionList(pTenantId);
    Map<String, String> connectionIds = new HashMap<>();
    for (StreamsConnection connection : connections) {
      connectionIds.put(connection.getName(), connection.getId().toString());
    }
    return connectionIds;
  }

  public int getVPCProxyInstanceCount(ObjectId pTenantId) throws SvcException {
    List<VPCProxyInstanceDescription> instances =
        _vpcProxyInstanceDescriptionSvc.findByTenantId(pTenantId);

    return instances.size();
  }

  public StreamsConnectionView unsetProxyInfo(StreamsConnectionView view) {
    if (view != null && view.isPrivateNetworking()) {
      if (view.getProxyInfo() != null) {
        view.getProxyInfo().setAuthKey("");
        view.getProxyInfo().setDnsName("");
      }
    }
    return view;
  }

  public void unsetProxyInfoInList(List<StreamsConnectionView> views) {
    for (StreamsConnectionView view : views) {
      unsetProxyInfo(view);
    }
  }

  public StreamsTenantView findTenantWithConnections(
      final Group pGroup,
      final String pName,
      final boolean pIncludeConnectionDetails,
      final AuditInfo pAuditInfo,
      final AppUser pAppUser)
      throws SvcException {

    final StreamsTenant streamsTenant = getStreamsTenant(pGroup.getId(), pName);
    final NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);
    final List<Document> stores =
        _streamsDataLakePrivateSvc
            .noLockDataLakeTenantSvc()
            .getStorageConfig(dataLakeTenant)
            .getStores();

    final List<StreamProcessorView> processors =
        _spmSvc.getStreamProcessors(
            streamsTenant.getId(),
            dataLakeTenant.getCloudProvider(),
            dataLakeTenant.getDataProcessRegion().getRegion(),
            false,
            false,
            true);
    final Set<String> connectionNamesUsed = _spmSvc.getSourceConnectionNames(processors);

    Map<String, StreamsConnection> allConnections = getAllConnections(dataLakeTenant.getTenantId());
    Map<String, StreamsConnection> visibleConnections = new HashMap<>();
    for (StreamsConnection conn : allConnections.values()) {
      if (conn.getConnectionType() != Cluster) {
        visibleConnections.put(conn.getName(), conn);
        continue;
      }
      try {
        validateUserCrossGroupAccess(pGroup, pAppUser, (ClusterConnection) conn);
        visibleConnections.put(conn.getName(), conn);
      } catch (Exception ex) {
        LOG.info(
            "STREAMS: connection {} is not visible for the user because of the error",
            conn.getName(),
            ex);
      }
    }

    final List<String> idleProxies =
        findDeployingVpcKafkaProxyNames(streamsTenant.getId(), pGroup.getId());

    List<StreamsConnectionView> connections =
        stores.stream()
            .filter(store -> visibleConnections.containsKey(store.get(FieldDefs.NAME).toString()))
            .map(
                store ->
                    StreamsConnectionView.fromStoreAndModel(
                        store,
                        visibleConnections.get(store.get(FieldDefs.NAME).toString()),
                        idleProxies,
                        connectionNamesUsed.contains(store.get(FieldDefs.NAME).toString()),
                        pIncludeConnectionDetails))
            .toList();

    // Add ARN to AWS connections as they aren't saved in the connection store.
    for (StreamsConnectionView connectionView : connections) {
      switch (connectionView.getType()) {
        case AWSLambda -> {
          AWSLambdaConnectionView awsLambdaConnectionView =
              (AWSLambdaConnectionView) connectionView;
          String roleArn =
              getRoleArn(pGroup.getId(), awsLambdaConnectionView.getAwsConfig().getRoleId());
          awsLambdaConnectionView.getAwsConfig().setRoleArn(roleArn);
        }
        case S3 -> {
          StreamsS3ConnectionView view = (StreamsS3ConnectionView) connectionView;
          if (view.getAwsConfig() != null && view.getAwsConfig().getRoleId() != null) {
            String roleArn = getRoleArn(pGroup.getId(), view.getAwsConfig().getRoleId());
            view.getAwsConfig().setRoleArn(roleArn);
          }
        }
        case AWSKinesisDataStreams -> {
          StreamsAWSKinesisDataStreamsConnectionView view =
              (StreamsAWSKinesisDataStreamsConnectionView) connectionView;
          if (view.getAwsConfig() != null && view.getAwsConfig().getRoleId() != null) {
            String roleArn = getRoleArn(pGroup.getId(), view.getAwsConfig().getRoleId());
            view.getAwsConfig().setRoleArn(roleArn);
          }
        }
      }
    }
    final String connectionNames =
        connections.stream().map(StreamsConnectionView::getName).toList().toString();

    final NDSAudit.Builder builder = new NDSAudit.Builder(Type.STREAM_TENANT_CONNECTIONS_LISTED);
    builder.groupId(streamsTenant.getGroupId());
    builder.auditInfo(pAuditInfo);
    builder.streamTenantId(streamsTenant.getId());
    builder.streamTenantName(streamsTenant.getName());
    builder.streamConnectionNames(connectionNames);
    _auditSvc.saveAuditEvent(builder.build());

    return toNDSStreamsTenantView(streamsTenant, dataLakeTenant, connections);
  }

  private void verifyCloudProviderAndRegion(
      final NDSDataLakeDataProcessRegionView pDataProcessRegion, Group pGroup) throws SvcException {

    final CloudProvider cloudProvider = pDataProcessRegion.getCloudProvider();
    final String region = pDataProcessRegion.getRegion();

    if (!Arrays.asList(CloudProvider.AZURE, CloudProvider.GCP, CloudProvider.AWS)
        .contains(cloudProvider)) {
      throw new SvcException(NDSErrorCode.STREAM_TENANT_INVALID_PROVIDER, cloudProvider);
    }

    if (cloudProvider.equals(CloudProvider.AZURE)
        && (!_featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.STREAMS_ENABLE_AZURE, null, pGroup))) {
      throw new SvcException(NDSErrorCode.STREAM_TENANT_INVALID_PROVIDER, cloudProvider);
    }

    final boolean allowAdditionalAWSRegions =
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.STREAMS_ENABLE_ADDITIONAL_REGIONS, null, pGroup);
    if (cloudProvider.equals(CloudProvider.AWS)) {
      StreamsConstants.validateRegion(
          CloudProvider.AWS, region, _appEnv, allowAdditionalAWSRegions);
    }

    final boolean allowAdditionalAzureRegions =
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.STREAMS_ENABLE_ADDITIONAL_AZURE_REGIONS, null, pGroup);
    if (cloudProvider.equals(CloudProvider.AZURE)) {
      StreamsConstants.validateRegion(
          CloudProvider.AZURE, region, _appEnv, allowAdditionalAzureRegions);
    }
  }

  public StreamsTenantView createTenant(
      final StreamsTenantView pStreamTenantView,
      final Group pGroup,
      final AuditInfo pAuditInfo,
      final boolean createSampleSolarConnection)
      throws SvcException {
    final ObjectId tenantId = new ObjectId();
    final boolean workspaceFfEnabled =
        _featureFlagSvc.isFeatureFlagEnabled(FeatureFlag.STREAMS_ENABLE_WORKSPACES, null, pGroup);

    if (!this._paymentMethodGateway.hasEffectivePaymentMethod(pGroup.getOrgId())) {
      throw new SvcException(BillingErrorCode.PAYMENT_METHOD_MISSING);
    }

    final NDSDataLakeDataProcessRegionView cloudProviderAndRegion =
        pStreamTenantView.getDataProcessRegion();

    verifyCloudProviderAndRegion(cloudProviderAndRegion, pGroup);

    if (!_featureFlagSvc.isFeatureFlagEnabled(FeatureFlag.ATLAS_STREAMS_SP10_TIER, null, pGroup)
        && pStreamTenantView.getStreamConfig() != null
        && pStreamTenantView.getStreamConfig().getTier().equals(Tier.SP10)) {
      throw new SvcException(
          NDSErrorCode.STREAM_TENANT_INVALID_TIER,
          pStreamTenantView.getStreamConfig().getTier().toString());
    }

    if (!workspaceFfEnabled
        && pStreamTenantView.getStreamConfig() != null
        && (pStreamTenantView.getStreamConfig().getDefaultTier() != null
            || pStreamTenantView.getStreamConfig().getMaxTierSize() != null)) {
      throw new SvcException(
          NDSErrorCode.STREAM_WORKSPACE_FEATURE_FLAG_MISSING,
          pStreamTenantView.getStreamConfig().toString());
    }

    // create stream tenant
    StreamsTenant streamsTenant;
    try {
      final Date now = new Date();
      streamsTenant =
          new StreamsTenant(
              tenantId,
              pStreamTenantView.getName(),
              pGroup.getId(),
              pStreamTenantView.isDefault(),
              now,
              now,
              pStreamTenantView.getStreamConfig() == null
                  ? new StreamConfig(Tier.SP30)
                  : new StreamConfig(
                      pStreamTenantView.getStreamConfig().getTier(),
                      pStreamTenantView.getStreamConfig().getDefaultTier(),
                      pStreamTenantView.getStreamConfig().getMaxTierSize()));
      _streamsTenantDao.save(streamsTenant);

      _segmentEventSvc.submitEvent(
          StreamProcessorInstanceCreatedEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .groupId(pGroup.getId())
              .organizationId(pGroup.getOrgId())
              .tenant(streamsTenant.getId())
              .tenantName(streamsTenant.getName())
              .region(cloudProviderAndRegion.getRegion())
              .provider(cloudProviderAndRegion.getCloudProvider().getDescription())
              .instanceTier(streamsTenant.getStreamConfig().getTier().name())
              .build());

    } catch (MongoException e) {
      if (ErrorCategory.fromErrorCode(e.getCode()) == ErrorCategory.DUPLICATE_KEY) {
        throw new SvcException(
            NDSErrorCode.STREAM_TENANT_NAME_ALREADY_EXISTS, pStreamTenantView.getName());
      }

      // We expect to see duplicate key errors, but we should track everything else
      incrementInstanceErrorCounter(e, Methods.CREATE);
      throw e;
    }

    final NDSDataLakeTenant dataLakeTenant =
        _streamsDataLakePrivateSvc.performDataLakeAction(
            (handle) -> {
              // create data lake tenant
              try {
                String dataLakeTenantName = getStreamTenantName(tenantId);
                handle
                    .getDataLakeTenantSvc()
                    .createTenant(
                        NDSDataLakeTenant.builder()
                            .tenantId(tenantId)
                            .dataLakeType(DataLakeType.STREAM)
                            .id(new NDSDataLakeTenantId(pGroup.getId(), dataLakeTenantName))
                            .domainLabelName(dataLakeTenantName.toLowerCase())
                            .dataProcessRegion(
                                pStreamTenantView
                                    .getDataProcessRegion()
                                    .toNDSDataLakeDataProcessRegion())
                            .build(),
                        null,
                        pAuditInfo);
              } catch (Exception e) {
                incrementInstanceErrorCounter(e, Methods.CREATE);

                _streamsTenantDao.delete(tenantId);

                if (e instanceof SvcException) {
                  throw e;
                } else {
                  throw new SvcException(NDSErrorCode.STREAM_PROCESSOR_UNEXPECTED_ERROR, e);
                }
              }

              return handle.getDataLakeTenantSvc().findByTenantId(tenantId).orElseThrow();
            });

    final NDSAudit.Builder builder = new NDSAudit.Builder(Type.STREAM_TENANT_CREATED);
    builder.groupId(streamsTenant.getGroupId());
    builder.auditInfo(pAuditInfo);
    builder.streamTenantId(tenantId);
    builder.streamTenantName(streamsTenant.getName());
    builder.streamTenantDiff(
        streamsTenant.makeNewDiffStreamTenant(
            dataLakeTenant.getDataProcessRegion(), dataLakeTenant.getCloudProviderConfig()));
    _auditSvc.saveAuditEvent(builder.build());

    // Add default sample solar connection.
    if (createSampleSolarConnection) {
      try {
        createConnection(
            DEFAULT_SAMPLE_SOLAR_CONNECTION, pStreamTenantView.getName(), pGroup, pAuditInfo, null);
      } catch (SvcException e) {
        LOG.error("Failed to add default sample connection.", e);
      }
    }

    return toNDSStreamsTenantView(streamsTenant, dataLakeTenant);
  }

  public StreamsTenantView createTenantV1(
      final StreamsTenantView pStreamTenantView,
      final Group pGroup,
      final AuditInfo pAuditInfo,
      final boolean createSampleSolarConnection)
      throws SvcException {
    final ObjectId tenantId = new ObjectId();

    final NDSDataLakeDataProcessRegionView cloudProviderAndRegion =
        pStreamTenantView.getDataProcessRegion();

    verifyCloudProviderAndRegion(cloudProviderAndRegion, pGroup);

    if (!_featureFlagSvc.isFeatureFlagEnabled(FeatureFlag.ATLAS_STREAMS_SP10_TIER, null, pGroup)
        && pStreamTenantView.getStreamConfig() != null
        && pStreamTenantView.getStreamConfig().getTier().equals(Tier.SP10)) {
      throw new SvcException(
          NDSErrorCode.STREAM_TENANT_INVALID_TIER,
          pStreamTenantView.getStreamConfig().getTier().toString());
    }

    // create stream tenant
    StreamsTenant streamsTenant;
    try {
      final Date now = new Date();
      streamsTenant =
          new StreamsTenant(
              tenantId,
              pStreamTenantView.getName(),
              pGroup.getId(),
              pStreamTenantView.isDefault(),
              now,
              now,
              pStreamTenantView.getStreamConfig() == null
                  ? new StreamConfig(Tier.SP30)
                  : new StreamConfig(pStreamTenantView.getStreamConfig().getTier()));
      _streamsTenantDao.save(streamsTenant);

      _segmentEventSvc.submitEvent(
          StreamProcessorInstanceCreatedEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .groupId(pGroup.getId())
              .organizationId(pGroup.getOrgId())
              .tenant(streamsTenant.getId())
              .tenantName(streamsTenant.getName())
              .region(cloudProviderAndRegion.getRegion())
              .provider(cloudProviderAndRegion.getCloudProvider().getDescription())
              .instanceTier(streamsTenant.getStreamConfig().getTier().name())
              .build());

    } catch (MongoException e) {
      if (ErrorCategory.fromErrorCode(e.getCode()) == ErrorCategory.DUPLICATE_KEY) {
        throw new SvcException(
            NDSErrorCode.STREAM_TENANT_NAME_ALREADY_EXISTS, pStreamTenantView.getName());
      }

      // We expect to see duplicate key errors, but we should track everything else
      incrementInstanceErrorCounter(e, Methods.CREATE);
      throw e;
    }

    final NDSDataLakeTenant dataLakeTenant =
        _streamsDataLakePrivateSvc.performDataLakeAction(
            (handle) -> {
              // create data lake tenant
              try {
                String dataLakeTenantName = getStreamTenantName(tenantId);
                handle
                    .getDataLakeTenantSvc()
                    .createTenant(
                        NDSDataLakeTenant.builder()
                            .tenantId(tenantId)
                            .dataLakeType(DataLakeType.STREAM)
                            .id(new NDSDataLakeTenantId(pGroup.getId(), dataLakeTenantName))
                            .domainLabelName(dataLakeTenantName.toLowerCase())
                            .dataProcessRegion(
                                pStreamTenantView
                                    .getDataProcessRegion()
                                    .toNDSDataLakeDataProcessRegion())
                            .build(),
                        null,
                        pAuditInfo);
              } catch (Exception e) {
                incrementInstanceErrorCounter(e, Methods.CREATE);

                _streamsTenantDao.delete(tenantId);

                if (e instanceof SvcException) {
                  throw e;
                } else {
                  throw new SvcException(NDSErrorCode.STREAM_PROCESSOR_UNEXPECTED_ERROR, e);
                }
              }

              return handle.getDataLakeTenantSvc().findByTenantId(tenantId).orElseThrow();
            });

    final NDSAudit.Builder builder = new NDSAudit.Builder(Type.STREAM_TENANT_CREATED);
    builder.groupId(streamsTenant.getGroupId());
    builder.auditInfo(pAuditInfo);
    builder.streamTenantId(tenantId);
    builder.streamTenantName(streamsTenant.getName());
    builder.streamTenantDiff(
        streamsTenant.makeNewDiffStreamTenant(
            dataLakeTenant.getDataProcessRegion(), dataLakeTenant.getCloudProviderConfig()));
    _auditSvc.saveAuditEvent(builder.build());

    // Add default sample solar connection.
    if (createSampleSolarConnection) {
      try {
        createConnection(
            DEFAULT_SAMPLE_SOLAR_CONNECTION, pStreamTenantView.getName(), pGroup, pAuditInfo, null);
      } catch (SvcException e) {
        LOG.error("Failed to add default sample connection.", e);
      }
    }

    return toNDSStreamsTenantView(streamsTenant, dataLakeTenant);
  }

  public StreamsTenantView updateTenant(
      final Group pGroup,
      final String pName,
      final NDSDataLakeDataProcessRegionView pDataProcessingRegionView,
      final AuditInfo pAuditInfo)
      throws SvcException {

    verifyCloudProviderAndRegion(pDataProcessingRegionView, pGroup);

    // Check if the new region is legitimate
    NDSDataLakeTenant.NDSDataLakeDataProcessRegion ndsDataLakeDataProcessRegion =
        pDataProcessingRegionView.toNDSDataLakeDataProcessRegion();
    ndsDataLakeDataProcessRegion.validate();

    try {
      StreamsTenant streamsTenant = getStreamsTenant(pGroup.getId(), pName);
      NDSDataLakeTenant dlTenant = getDataLakeTenant(streamsTenant);

      // Sanity checks before we allow updating a tenant
      verifyTenantForModification(streamsTenant, dlTenant);

      // Replacing the old data process region with a new one
      NDSDataLakeTenant newTenant =
          dlTenant.toBuilder()
              .dataProcessRegion(ndsDataLakeDataProcessRegion)
              .lastUpdatedDate(Date.from(Instant.now()))
              .build();

      NDSGroup ndsGroup =
          _ndsGroupSvc
              .find(pGroup.getId())
              .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

      // use returned updated data lake tenant as hostnames are regenerated
      NDSDataLakeTenant updatedDlTenant =
          _streamsDataLakePrivateSvc.performDataLakeAction(
              (handle) ->
                  handle
                      .getDataLakeTenantSvc()
                      .updateTenant(
                          newTenant,
                          handle.getDataLakeTenantSvc().getStorageConfig(dlTenant),
                          ndsGroup,
                          pAuditInfo));

      _segmentEventSvc.submitEvent(
          StreamProcessorInstanceUpdatedEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .groupId(pGroup.getId())
              .organizationId(pGroup.getOrgId())
              .tenant(streamsTenant.getId())
              .region(dlTenant.getDataProcessRegion().getRegion())
              .provider(dlTenant.getDataProcessRegion().getCloudProvider().getDescription())
              .instanceTier(streamsTenant.getStreamConfig().getTier().name())
              .build());

      final NDSAudit.Builder builder = new NDSAudit.Builder(Type.STREAM_TENANT_UPDATED);
      builder.groupId(streamsTenant.getGroupId());
      builder.auditInfo(pAuditInfo);
      // Stream DL tenants have the same tenantId as the Stream tenant
      builder.streamTenantId(dlTenant.getTenantId());
      builder.streamTenantName(streamsTenant.getName());
      builder.streamTenantDiff(
          streamsTenant.makeUpdatedDiffStreamTenant(updatedDlTenant, dlTenant));
      _auditSvc.saveAuditEvent(builder.build());

      return toNDSStreamsTenantView(streamsTenant, updatedDlTenant);
    } catch (SvcException ex) {
      incrementInstanceErrorCounter(ex, Methods.UPDATE);

      throw ex;
    }
  }

  public void deleteTenant(
      final Group pGroup,
      final String pName,
      final AuditInfo pAuditInfo,
      boolean dropStreamProcessors)
      throws SvcException {
    final ObjectId correlationId = new ObjectId();
    LOG.atInfo()
        .setMessage(
            String.format(
                "STREAMS: Deleting streams instance for tenant %s in group %s",
                pName, pGroup.getId().toHexString()))
        .addKeyValue("correlationId", correlationId)
        .addKeyValue("groupId", pGroup.getId())
        .addKeyValue("tenantName", pName)
        .log();
    try {
      StreamsTenant tenant = getStreamsTenant(pGroup.getId(), pName);
      _streamsDataLakePrivateSvc.performDataLakeAction(
          (handle) -> {
            NDSDataLakeTenant dlTenant = null;

            // Delete the ADF tenant
            try {
              dlTenant = getDataLakeTenant(tenant);
              if (dropStreamProcessors) {
                dropExistingStreamProcessors(tenant, dlTenant);
              } else {
                verifyTenantHasNoProcessors(tenant, dlTenant);
              }

              String dlTenantName = getStreamTenantName(tenant.getId());
              handle.getDataLakePublicSvc().deleteByGroupAndName(pGroup, dlTenantName, pAuditInfo);
            } catch (SvcException ex) {
              // We want to ignore DL not found errors in case the MMS document has persisted
              // without it's
              // paired data lake tenant. This allows for a manual cleanup by users.
              final List<NDSErrorCode> errorsToIgnore =
                  List.of(
                      // getDataLakeTenant
                      NDSErrorCode.STREAM_TENANT_NOT_FOUND_FOR_NAME,
                      // deleteByGroupAndName
                      NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME);

              if (!errorsToIgnore.contains((NDSErrorCode) ex.getErrorCode())) {
                throw ex;
              }
            }

            final List<StreamsConnection> allPotentialPrivateNetworkingConns = new ArrayList<>();
            allPotentialPrivateNetworkingConns.addAll(
                _kafkaConnectionDao.fetchByTenantIdAndType(tenant.getId()).stream().toList());
            allPotentialPrivateNetworkingConns.addAll(
                _kinesisConnectionDao.fetchByTenantIdAndType(tenant.getId()).stream().toList());
            allPotentialPrivateNetworkingConns.addAll(
                _s3ConnectionDao.fetchByTenantIdAndType(tenant.getId()).stream().toList());

            // Check to see if there are Private Networking connections that need their VPC Proxy
            // cloud instances shut down and delete them if they exist
            for (StreamsConnection conn : allPotentialPrivateNetworkingConns) {
              if (conn.isPrivateNetworking()) {
                try {
                  LOG.atInfo()
                      .setMessage(
                          "STREAMS: Deleting deployment description for private networking"
                              + " connection during tenant deletion")
                      .addKeyValue("correlationId", correlationId)
                      .addKeyValue("tenantId", tenant.getId())
                      .addKeyValue("connectionName", conn.getName())
                      .addKeyValue("groupId", pGroup.getId())
                      .log();
                  deleteVPCProxyDeploymentDescription(conn.getName(), tenant, correlationId);
                } catch (SvcException ex) {
                  LOG.atWarn()
                      .setMessage(
                          "STREAMS: encountered error while deleting deployment description for"
                              + " private networking connection during tenant deletion")
                      .addKeyValue("correlationId", correlationId)
                      .addKeyValue("connectionName", conn.getName())
                      .addKeyValue("tenantName", tenant.getName())
                      .addKeyValue("tenantId", tenant.getId())
                      .addKeyValue("groupId", pGroup.getId())
                      .setCause(ex)
                      .log();
                }
              }
            }

            // Even though we are using ClusterConnectionDao here, it will retrieve all connections
            // in a given tenant
            // because the collection used by different connection DAOs is the same.
            final List<StreamsConnection> allConnections =
                new ArrayList<>(
                    _clusterConnectionDao.fetchByTenantId(tenant.getId()).stream().toList());
            List<String> connectionNames =
                allConnections.stream()
                    .map(StreamsConnection::getName)
                    .collect(Collectors.toList());

            List<ObjectId> connectionIds =
                allConnections.stream().map(StreamsConnection::getId).toList();

            _segmentEventSvc.submitEvent(
                StreamProcessorConnectionDeletedEvent.builder()
                    .userId(pAuditInfo.getAppUserId())
                    .groupId(pGroup.getId())
                    .organizationId(pGroup.getOrgId())
                    .tenant(tenant.getId())
                    .tenantName(tenant.getName())
                    .connectionNames(connectionNames)
                    .connectionIds(connectionIds)
                    .connectionsDeleted(connectionNames.size())
                    .cascadeDelete(true)
                    .build());

            // Delete all the connections
            _clusterConnectionDao.deleteAll(tenant.getId());

            // Delete the tenant document via the dao
            _streamsTenantDao.delete(tenant.getId());

            // Track that we're deleting a tenant
            _segmentEventSvc.submitEvent(
                StreamProcessorInstanceDeletedEvent.builder()
                    .userId(pAuditInfo.getAppUserId())
                    .groupId(pGroup.getId())
                    .organizationId(pGroup.getOrgId())
                    .tenant(tenant.getId())
                    .tenantName(tenant.getName())
                    .region(dlTenant == null ? "" : dlTenant.getDataProcessRegion().getRegion())
                    .provider(
                        dlTenant == null
                            ? ""
                            : dlTenant.getDataProcessRegion().getCloudProvider().getDescription())
                    .instanceTier(tenant.getStreamConfig().getTier().name())
                    .build());

            final NDSAudit.Builder builder = new NDSAudit.Builder(Type.STREAM_TENANT_DELETED);
            builder.groupId(pGroup.getId());
            builder.auditInfo(pAuditInfo);
            // Stream DL tenants have the same tenantId as the Stream tenant
            builder.streamTenantId(tenant.getId());
            builder.streamTenantName(tenant.getName());
            if (dlTenant != null) {
              builder.streamTenantDiff(
                  tenant.makeRemovedDiffStreamTenant(
                      dlTenant.getDataProcessRegion(), dlTenant.getCloudProviderConfig()));
            }

            _auditSvc.saveAuditEvent(builder.build());

            return null;
          });

      LOG.atInfo()
          .setMessage(
              String.format(
                  "STREAMS: Successfully deleted streams instance %s for group %s",
                  pName, pGroup.getId().toHexString()))
          .addKeyValue("correlationId", correlationId)
          .log();
    } catch (Exception ex) {
      incrementInstanceErrorCounter(ex, Methods.DELETE);
      LOG.atError()
          .setMessage(
              String.format(
                  "STREAMS: Error deleting streams instance %s for group %s",
                  pName, pGroup.getId().toHexString()))
          .setCause(ex)
          .addKeyValue("correlationId", correlationId)
          .log();
      throw ex;
    }
  }

  private void dropExistingStreamProcessors(
      final StreamsTenant tenant, final NDSDataLakeTenant dlTenant) throws SvcException {
    List<StreamProcessorView> processors =
        getStreamTenantProcessors(tenant, dlTenant, false, false, true);
    if (!processors.isEmpty()) {
      List<String> processorNames = processors.stream().map(StreamProcessorView::getName).toList();

      for (String processorName : processorNames) {
        _spmSvc.deleteStreamProcessor(
            tenant.getId(),
            tenant.getGroupId(),
            dlTenant.getCloudProvider(),
            dlTenant.getDataProcessRegion().getRegion(),
            processorName,
            false);
      }
    }
  }

  public void dropStreamProcessor(final Group pGroup, final String pTenantName, final String pName)
      throws SvcException {

    StreamsTenant tenant = getStreamsTenant(pGroup.getId(), pTenantName);
    NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(tenant);

    _spmSvc.deleteStreamProcessor(
        tenant.getId(),
        pGroup.getId(),
        dataLakeTenant.getCloudProvider(),
        dataLakeTenant.getDataProcessRegion().getRegion(),
        pName,
        true);
  }

  public void startStreamProcessor(
      final Group pGroup,
      final String pTenantName,
      final String pName,
      final String username,
      final Date startAtOperationTime)
      throws SvcException {
    startStreamProcessorWithDataFlowOptions(
        pGroup, pTenantName, pName, username, startAtOperationTime, true);
  }

  public void startStreamProcessorWithDataFlowOptions(
      final Group pGroup,
      final String pTenantName,
      final String pName,
      final String username,
      final Date startAtOperationTime,
      final boolean isEnableDataFlow)
      throws SvcException {
    StreamsTenant tenant = getStreamsTenant(pGroup.getId(), pTenantName);
    NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(tenant);

    _spmSvc.startStreamProcessorWithDataFlowOptions(
        tenant.getId(),
        pGroup.getId(),
        dataLakeTenant.getCloudProvider(),
        dataLakeTenant.getDataProcessRegion().getRegion(),
        pName,
        username,
        (startAtOperationTime != null)
            ? new BsonDateTime(startAtOperationTime.toInstant().toEpochMilli())
            : null,
        isEnableDataFlow);
  }

  public void stopStreamProcessor(final Group pGroup, final String pTenantName, final String pName)
      throws SvcException {

    StreamsTenant tenant = getStreamsTenant(pGroup.getId(), pTenantName);
    NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(tenant);

    _spmSvc.stopStreamProcessor(
        tenant.getId(),
        pGroup.getId(),
        dataLakeTenant.getCloudProvider(),
        dataLakeTenant.getDataProcessRegion().getRegion(),
        pName);
  }

  public void verifyTenantForModification(
      final StreamsTenant pStreamsTenant, final NDSDataLakeTenant pDataLakeTenant)
      throws SvcException {
    verifyTenantHasNoProcessors(pStreamsTenant, pDataLakeTenant);
  }

  /**
   * A check to block changes to a streams tenant if there are active processors. Used for
   * Updates/Deletes.
   *
   * @param pStreamsTenant Streams tenant to check
   * @param pDataLakeTenant Companion data lake tenant
   * @throws SvcException If there are any processors, throw that processors are present and we
   *     can't continue
   */
  public void verifyTenantHasNoProcessors(
      final StreamsTenant pStreamsTenant, final NDSDataLakeTenant pDataLakeTenant)
      throws SvcException {
    List<StreamProcessorView> processors =
        getStreamTenantProcessors(pStreamsTenant, pDataLakeTenant, false, false, true);
    if (!processors.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.STREAM_TENANT_HAS_STREAM_PROCESSORS, pStreamsTenant.getName());
    }
  }

  private String getRoleArn(ObjectId pGroupId, String roleId) throws SvcException {
    NDSCloudProviderAccessView ndsCloudProviderAccessView =
        _ndsUISvc.getCloudProviderAccess(pGroupId);

    return ndsCloudProviderAccessView.getNDSCloudProviderAccessAWSIAMRoleViews().stream()
        .filter(item -> item.getRoleId().toHexString().equals(roleId))
        .map(NDSCloudProviderAccessAWSIAMRoleView::getIamAssumedRoleArn)
        .findFirst()
        .orElseThrow(() -> new SvcException(NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND));
  }

  private String getRoleId(ObjectId pGroupId, String roleArn) throws SvcException {
    NDSCloudProviderAccessView ndsCloudProviderAccessView =
        _ndsUISvc.getCloudProviderAccess(pGroupId);
    if (ndsCloudProviderAccessView == null) {
      throw new SvcException(NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND);
    }

    ObjectId roleId =
        ndsCloudProviderAccessView.getNDSCloudProviderAccessAWSIAMRoleViews().stream()
            .filter(
                item ->
                    item.getIamAssumedRoleArn() != null
                        && item.getIamAssumedRoleArn().equals(roleArn))
            .map(NDSCloudProviderAccessAWSIAMRoleView::getRoleId)
            .findFirst() // It should only have <=1 aws iam role match
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND));
    return roleId.toHexString();
  }

  public StreamsConnectionView findConnection(
      final Group pGroup,
      final String pTenantName,
      final String pConnectionName,
      final AppUser pAppUser)
      throws SvcException {

    final StreamsTenant streamsTenant = getStreamsTenant(pGroup.getId(), pTenantName);

    ClusterConnection clusterConn =
        _clusterConnectionDao
            .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
            .orElseThrow(connectionNotFound(pTenantName, pConnectionName));

    switch (clusterConn.getConnectionType()) {
      case Cluster -> {
        validateUserCrossGroupAccess(pGroup, pAppUser, clusterConn);
        return new ClusterConnectionView(
            pConnectionName,
            clusterConn.getCluster(),
            clusterConn.getDBRoleToExecute(),
            clusterConn.getClusterGroupId());
      }
      case Sample -> {
        return new StreamsSampleConnectionView(pConnectionName);
      }
      case Https -> {
        HttpsConnection httpsConn =
            _httpsConnectionDao
                .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
                .orElseThrow(connectionNotFound(streamsTenant.getName(), pConnectionName));
        return new StreamsHttpsConnectionView(
            pConnectionName, httpsConn.getUrl(), httpsConn.getHeaders());
      }
      case Kafka -> {
        KafkaConnection kafkaConn =
            _kafkaConnectionDao
                .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
                .orElseThrow(connectionNotFound(pTenantName, pConnectionName));
        final Document kafkaStore = getDataLakeStore(pConnectionName, streamsTenant);
        return new StreamsKafkaConnectionView(
            kafkaStore.get(FieldDefs.NAME).toString(),
            String.join(",", kafkaStore.getList(FieldDefs.BOOTSTRAP_SERVERS, String.class)),
            kafkaConn.getSecurity(),
            kafkaConn.getAuthentication(),
            kafkaConn.getConfiguration(),
            kafkaConn.getNetworking(),
            kafkaConn.getProxyInfo());
      }
      case AWSLambda -> {
        AWSLambdaConnection lambdaConnection =
            _awsLambdaConnectionDao
                .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
                .orElseThrow(connectionNotFound(pTenantName, pConnectionName));
        String roleArn =
            getRoleArn(pGroup.getId(), lambdaConnection.getAwsConnection().getRoleId());
        lambdaConnection.getAwsConnection().setRoleArn(roleArn);
        return new AWSLambdaConnectionView(pConnectionName, lambdaConnection.getAwsConnection());
      }
      case S3 -> {
        NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);

        boolean shouldIncludeStats = false;
        boolean isFromAdminApi = false;
        boolean shouldGetPipelineConnectionInfoOnly = true;

        final List<StreamProcessorView> processors =
            _spmSvc.getStreamProcessors(
                streamsTenant.getId(),
                dataLakeTenant.getCloudProvider(),
                dataLakeTenant.getDataProcessRegion().getRegion(),
                shouldIncludeStats,
                isFromAdminApi,
                shouldGetPipelineConnectionInfoOnly);

        final Set<String> connectionNamesUsedByProcessors =
            _spmSvc.getSourceConnectionNames(processors);

        S3Connection connection =
            _s3ConnectionDao
                .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
                .orElseThrow(connectionNotFound(pTenantName, pConnectionName));

        String roleArn = getRoleArn(pGroup.getId(), connection.getAWS().getRoleId());
        connection.getAWS().setRoleArn(roleArn);

        return new StreamsS3ConnectionView(
            pConnectionName,
            connection.getAWS(),
            connection.getNetworking(),
            connection.getProxyInfo(),
            connectionNamesUsedByProcessors.contains(pConnectionName));
      }
      case AWSKinesisDataStreams -> {
        AWSKinesisDataStreamsConnection connection =
            _kinesisConnectionDao
                .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
                .orElseThrow(connectionNotFound(pTenantName, pConnectionName));

        String roleArn = getRoleArn(pGroup.getId(), connection.getAWS().getRoleId());
        connection.getAWS().setRoleArn(roleArn);

        return new StreamsAWSKinesisDataStreamsConnectionView(
            pConnectionName,
            connection.getAWS(),
            connection.getNetworking(),
            connection.getProxyInfo());
      }
      default ->
          throw new SvcException(CommonErrorCode.VALIDATION_ERROR, "Invalid connection type");
    }
  }

  public StreamsConnection findConnectionModel(
      final ObjectId pGroupId, final String pTenantName, final String pConnectionName)
      throws SvcException {

    final StreamsTenant streamsTenant = getStreamsTenant(pGroupId, pTenantName);

    ClusterConnection clusterConn =
        _clusterConnectionDao
            .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
            .orElseThrow(connectionNotFound(pTenantName, pConnectionName));

    return switch (clusterConn.getConnectionType()) {
      case Cluster -> clusterConn;
      case Sample ->
          new SampleConnection(
              clusterConn.getId(),
              clusterConn.getTenantId(),
              pConnectionName,
              clusterConn.getCreated(),
              clusterConn.getLastModified());
      case Https ->
          _httpsConnectionDao
              .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
              .orElseThrow(connectionNotFound(pTenantName, pConnectionName));
      case Kafka ->
          _kafkaConnectionDao
              .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
              .orElseThrow(connectionNotFound(pTenantName, pConnectionName));
      case AWSLambda ->
          _awsLambdaConnectionDao
              .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
              .orElseThrow(connectionNotFound(pTenantName, pConnectionName));
      case S3 ->
          _s3ConnectionDao
              .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
              .orElseThrow(connectionNotFound(pTenantName, pConnectionName));
      case AWSKinesisDataStreams ->
          _kinesisConnectionDao
              .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
              .orElseThrow(connectionNotFound(pTenantName, pConnectionName));
    };
  }

  public Document getDataLakeStore(String pConnectionName, StreamsTenant streamsTenant)
      throws SvcException {
    final NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);

    return _streamsDataLakePrivateSvc
        .noLockDataLakeTenantSvc()
        .getStorageConfig(dataLakeTenant)
        .getStore(pConnectionName)
        .orElseThrow(connectionNotFound(streamsTenant.getName(), pConnectionName));
  }

  public NDSAWSTempCredentials getAwsAssumeRoleTempCredentials(
      final Group pGroup,
      ObjectId tenantId,
      String connectionName,
      StreamsConnectionType connectionType)
      throws SvcException {
    ObjectId roleId;

    if (connectionType == StreamsConnectionType.AWSLambda) {
      AWSLambdaConnection connection =
          _awsLambdaConnectionDao
              .fetchByTenantIdAndName(tenantId, connectionName)
              .orElseThrow(connectionNotFound(getStreamTenantName(tenantId), connectionName));
      roleId = new ObjectId(connection.getAwsConnection().getRoleId());
    } else if (connectionType == StreamsConnectionType.S3) {
      S3Connection s3Connection =
          _s3ConnectionDao
              .fetchByTenantIdAndName(tenantId, connectionName)
              .orElseThrow(connectionNotFound(getStreamTenantName(tenantId), connectionName));
      roleId = new ObjectId(s3Connection.getAWS().getRoleId());
    } else if (connectionType == StreamsConnectionType.AWSKinesisDataStreams) {
      AWSKinesisDataStreamsConnection AWSKinesisDataStreamsConnection =
          _kinesisConnectionDao
              .fetchByTenantIdAndName(tenantId, connectionName)
              .orElseThrow(connectionNotFound(getStreamTenantName(tenantId), connectionName));
      roleId = new ObjectId(AWSKinesisDataStreamsConnection.getAWS().getRoleId());
    } else {
      throw new SvcException(STREAM_NOT_AWS_CONNECTION, connectionName);
    }

    try {
      return _ndsUISvc.getAWSAssumeRoleTempCredentials(pGroup.getId(), roleId);
    } catch (SvcException ex) {
      throw new SvcException(NDSErrorCode.STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED);
    }
  }

  private void validateUserCrossGroupAccess(
      final Group pGroup, final AppUser pAppUser, ClusterConnection conn) throws SvcException {
    if (conn.getClusterGroupId() == null
        || conn.getClusterGroupId().isEmpty()
        || conn.getClusterGroupId().equals(pGroup.getId().toHexString())) {
      return;
    }

    Organization org = _organizationSvc.findById(pGroup.getOrgId());
    if (org == null) {
      throw new SvcException(NDSErrorCode.STREAM_INVALID_ORG_ID, pGroup.getOrgId());
    }
    if (org.getStreamsCrossGroupEnabled() == null || !org.getStreamsCrossGroupEnabled()) {
      throw new SvcException(NDSErrorCode.STREAM_CROSS_PROJECT_NOT_ENABLED);
    }
    if (pAppUser == null) {
      throw new SvcException(NDSErrorCode.STREAM_USER_TYPE_NOT_SUPPORTED, "null");
    }
    if (pAppUser.getType().isLocal()) { // UI user
      _groupSvc.getVisibleGroups(pGroup.getOrgId(), pAppUser).stream()
          .filter(group -> group.getId().toHexString().equals(conn.getClusterGroupId()))
          .findFirst()
          .orElseThrow(
              () ->
                  new SvcException(
                      NDSErrorCode.STREAM_CLUSTER_PROJECT_ID_NOT_FOUND, conn.getClusterGroupId()));
      if (!_roleSetSvc.doRoleCheck(
              RoleSet.GROUP_STREAM_PROCESSING_OWNER, pAppUser, pGroup.getOrgId(), pGroup)
          && !_roleSetSvc.doRoleCheck(
              RoleSet.ORG_STREAM_PROCESSING_ADMIN, pAppUser, pGroup.getOrgId(), null)) {
        throw new SvcException(
            NDSErrorCode.STREAM_CLUSTER_PROJECT_ID_NOT_FOUND, conn.getClusterGroupId());
      }
    } else if (pAppUser.getType().isApi()) { // API user
      if (!_roleSetSvc.doRoleCheck(
          RoleSet.ORG_STREAM_PROCESSING_ADMIN, pAppUser, pGroup.getOrgId(), null)) {
        throw new SvcException(NDSErrorCode.STREAM_PROJECT_NOT_ALLOWED, conn.getClusterGroupId());
      }
    } else {
      throw new SvcException(NDSErrorCode.STREAM_USER_TYPE_NOT_SUPPORTED, pAppUser.getType());
    }
  }

  private void validateConnectionForUpdate(
      final Group pGroup,
      final String tenantName,
      final StreamsConnection mergedConnection,
      final StreamsConnection newConnection,
      final StreamsConnection oldConnection,
      final AppUser pAppUser)
      throws SvcException {
    if (mergedConnection.getConnectionType() != Cluster) {
      validateConnection(pGroup, tenantName, mergedConnection, pAppUser);
      return;
    }
    mergedConnection.validate();

    ClusterConnection mergedConn = (ClusterConnection) mergedConnection;
    ClusterConnection oldConn = (ClusterConnection) oldConnection;
    ClusterConnection newConn = (ClusterConnection) newConnection;

    if (mergedConn.getDBRoleToExecute() == null) {
      throw new SvcException(NDSErrorCode.ROLE_NOT_PROVIDED);
    }

    final DBRoleType roleType = mergedConn.getDBRoleToExecute().getType();

    if (roleType == DBRoleType.CUSTOM) {
      _ndsUISvc.getNDSCustomDBRole(pGroup.getId(), mergedConn.getDBRoleToExecute().getRole());
    }

    if (roleType == DBRoleType.BUILT_IN) {
      switch (mergedConn.getDBRoleToExecute().getRole()) {
        case BUILT_IN_ROLE_ATLAS_ADMIN,
            BUILT_IN_ROLE_READ_ANY_DATABASE,
            BUILT_IN_ROLE_READ_WRITE_ANY_DATABASE:
          break;
        default:
          throw new SvcException(
              NDSErrorCode.UNSUPPORTED_ROLE,
              String.format("Unsupported role: %s", mergedConn.getDBRoleToExecute().getRole()));
      }
    }

    validateUserCrossGroupAccess(pGroup, pAppUser, oldConn);

    validateUserCrossGroupAccess(pGroup, pAppUser, newConn);

    // Validate whether the cluster exists in the new connection's project
    String projectId =
        newConn.getClusterGroupId() != null
            ? newConn.getClusterGroupId()
            : pGroup.getId().toHexString();
    if (_clusterDescriptionDao
        .findByName(new ObjectId(projectId), newConn.getCluster())
        .isEmpty()) {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, mergedConn.getCluster(), projectId);
    }
  }

  public void validateConnection(
      final Group pGroup,
      final String tenantName,
      final StreamsConnection pConnection,
      final AppUser pAppUser)
      throws SvcException {
    pConnection.validate();

    // If the connection can support private networking, we need to include special validation if
    // the connection has private networking information
    if (pConnection.getConnectionType().supportsPrivateNetworking()) {
      Optional<NDSDataLakeStreamsNetworking> networkingOpt =
          Optional.ofNullable(pConnection.getNetworking());
      if (networkingOpt.isPresent()) {
        NDSDataLakeStreamsNetworking networking = networkingOpt.get();
        networking.validate();
        if (networking.getAccess().getType()
            == com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Type.PRIVATE_LINK) {
          ObjectId connectionId = networking.getAccess().getConnectionId();
          Optional<StreamsPrivateLink> plOpt =
              _privateLinkDao.findByGroupIdAndId(pGroup.getId(), connectionId);
          if (plOpt.isEmpty()) {
            throw new SvcException(NDSErrorCode.STREAM_PRIVATE_LINK_NOT_FOUND, connectionId);
          }

          StreamsPrivateLink pl = plOpt.get();
          StreamsPrivateLinkVendorType plVendor = pl.getVendor();
          if (!IsPLVendorCompatibleWithConnectionType(plVendor, pConnection.getConnectionType())) {
            throw new SvcException(
                NDSErrorCode.STREAM_PRIVATE_LINK_VENDOR_INCOMPATIBLE,
                plVendor.getVendor(),
                connectionId.toHexString(),
                pConnection.getName());
          }
        }
      }
    }

    // Special check for clusters - does the cluster we're specifying exist?
    if (pConnection.getConnectionType() == Cluster) {
      ClusterConnection clusterConn = (ClusterConnection) pConnection;

      if (clusterConn.getDBRoleToExecute() == null) {
        throw new SvcException(NDSErrorCode.ROLE_NOT_PROVIDED);
      }

      final DBRoleType roleType = clusterConn.getDBRoleToExecute().getType();

      if (roleType == DBRoleType.CUSTOM) {
        _ndsUISvc.getNDSCustomDBRole(pGroup.getId(), clusterConn.getDBRoleToExecute().getRole());
      }

      if (roleType == DBRoleType.BUILT_IN) {
        switch (clusterConn.getDBRoleToExecute().getRole()) {
          case BUILT_IN_ROLE_ATLAS_ADMIN,
              BUILT_IN_ROLE_READ_ANY_DATABASE,
              BUILT_IN_ROLE_READ_WRITE_ANY_DATABASE:
            break;
          default:
            throw new SvcException(
                NDSErrorCode.UNSUPPORTED_ROLE,
                String.format("Unsupported role: %s", clusterConn.getDBRoleToExecute().getRole()));
        }
      }

      validateUserCrossGroupAccess(pGroup, pAppUser, clusterConn);
      // Validate whether the cluster exists in the new connection's project
      String projectId =
          clusterConn.getClusterGroupId() != null
              ? clusterConn.getClusterGroupId()
              : pGroup.getId().toHexString();
      if (_clusterDescriptionDao
          .findByName(new ObjectId(projectId), clusterConn.getCluster())
          .isEmpty()) {
        throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, clusterConn.getCluster(), projectId);
      }
    }

    if (pConnection.getConnectionType() == AWSLambda) {
      AWSLambdaConnection awsConn = (AWSLambdaConnection) pConnection;
      // Retrieve roleId for further processing and persistance
      awsConn
          .getAwsConnection()
          .setRoleId(getRoleId(pGroup.getId(), awsConn.getAwsConnection().getRoleArn()));

      try {
        _ndsUISvc.getAWSAssumeRoleTempCredentials(
            pGroup.getId(), new ObjectId(awsConn.getAwsConnection().getRoleId()));
      } catch (SvcException ex) {
        throw new SvcException(NDSErrorCode.STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED);
      }
    }

    if (pConnection.getConnectionType() == S3) {

      S3Connection s3Conn = (S3Connection) pConnection;
      if (s3Conn.getNetworking().getAccess().getType()
              == com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Type.PRIVATE_LINK
          && !_featureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.STREAMS_AWS_S3_PRIVATE_LINK, null, pGroup))
        throw new SvcException(
            NDSErrorCode.STREAM_S3_PRIVATE_LINK_IS_NOT_SUPPORTED, pConnection.getName());

      s3Conn.getAWS().setRoleId(getRoleId(pGroup.getId(), s3Conn.getAWS().getRoleArn()));

      // If defined, check if the IAM role has access to the configured testBucket.
      String testBucket = s3Conn.getAWS().getTestBucket();
      if (testBucket != null) {
        validateAWSBucket(testBucket, pGroup.getId(), s3Conn.getAWS().getRoleArn(), tenantName);
      }
    }

    if (pConnection.getConnectionType() == AWSKinesisDataStreams) {
      AWSKinesisDataStreamsConnection kinesisConn = (AWSKinesisDataStreamsConnection) pConnection;
      // Retrieve roleId for further processing and persistence
      kinesisConn.getAWS().setRoleId(getRoleId(pGroup.getId(), kinesisConn.getAWS().getRoleArn()));
      NDSDataLakeStreamsNetworking kinesisNetworking = kinesisConn.getNetworking();
      if (kinesisNetworking != null && kinesisNetworking.getAccess() != null) {
        if (kinesisNetworking.getAccess().getType()
                == com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Type.PRIVATE_LINK
            && !_featureFlagSvc.isFeatureFlagEnabled(
                FeatureFlag.STREAMS_AWS_KINESIS_PRIVATE_LINK, null, pGroup))
          throw new SvcException(
              NDSErrorCode.STREAM_KINESIS_PRIVATE_LINK_IS_NOT_SUPPORTED, pConnection.getName());

        // For other forms of private networking, throw a different error
        if (kinesisNetworking.getAccess().getType()
                != com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Type.PRIVATE_LINK
            && kinesisNetworking.getAccess().isPrivateNetworking()) {
          throw new SvcException(
              NDSErrorCode.STREAM_INVALID_NETWORKING_ACCESS_TYPE,
              kinesisNetworking.getAccess().getType(),
              pConnection.getConnectionType());
        }
      }

      try {
        _ndsUISvc.getAWSAssumeRoleTempCredentials(
            pGroup.getId(), new ObjectId(kinesisConn.getAWS().getRoleId()));
      } catch (SvcException ex) {
        throw new SvcException(NDSErrorCode.STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED);
      }
    }
  }

  public void validateAWSBucket(
      final String testBucket,
      final ObjectId groupId,
      final String roleArn,
      final String tenantName)
      throws SvcException {
    ObjectId roleId = new ObjectId(getRoleId(groupId, roleArn));
    // Ensure that the IAM role is assumable
    NDSAWSTempCredentials tempCreds;
    try {
      tempCreds = _ndsUISvc.getAWSAssumeRoleTempCredentials(groupId, roleId);
    } catch (SvcException ex) {
      throw new SvcException(NDSErrorCode.STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED);
    }
    AWSCredentials sessionCredentials =
        new BasicSessionCredentials(
            tempCreds.getAccessKey(), tempCreds.getAccessSecret(), tempCreds.getSessionToken());

    AmazonS3 s3Client = _awsApiSvc.getS3Client(sessionCredentials, false);

    ListBucketsPaginatedRequest req = new ListBucketsPaginatedRequest();
    req.putCustomQueryParameter("prefix", testBucket);

    ListBucketsPaginatedResult result;
    boolean hasAccessToTestBucket;
    try {
      do {
        result = s3Client.listBuckets(req);
        hasAccessToTestBucket =
            result.getBuckets().stream().anyMatch(bucket -> bucket.getName().equals(testBucket));
      } while (!hasAccessToTestBucket && result.getContinuationToken() != null);
      if (!hasAccessToTestBucket) {
        throw new SvcException(NDSErrorCode.STREAM_AWS_BUCKET_NOT_AVAILABLE, testBucket);
      }
    } catch (AmazonServiceException ase) {
      LOG.atError()
          .setMessage(
              "STREAMS: Encountered AWS error when listing buckets while validating bucket name")
          .addKeyValue("groupId", groupId)
          .addKeyValue("tenantName", tenantName)
          .setCause(ase)
          .log();

      if (ase.getErrorCode().equals(AWS_ERROR_CODE_ACCESS_DENIED)) {
        // the assumed role doesn't have ListAllMyBuckets access
        throw new SvcException(NDSErrorCode.STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED, ase.getErrorCode());
      }

      int statusCode = ase.getStatusCode();
      if (statusCode >= 400 && statusCode < 500) {
        throw new SvcException(NDSErrorCode.STREAM_BAD_REQUEST_AWS_ERROR, ase.getErrorCode());
      } else if (statusCode >= 500) {
        throw new SvcException(NDSErrorCode.STREAM_UPSTREAM_SERVICE_FAILURE_ERROR, "AWS S3");
      }
    }
  }

  public static boolean IsPLVendorCompatibleWithConnectionType(
      StreamsPrivateLinkVendorType vendorType, StreamsConnectionType connectionType) {
    switch (connectionType) {
      case AWSKinesisDataStreams -> {
        return vendorType == StreamsPrivateLinkVendorType.KINESIS;
      }
      case S3 -> {
        return vendorType == StreamsPrivateLinkVendorType.S3;
      }
      case Kafka -> {
        return vendorType == StreamsPrivateLinkVendorType.CONFLUENT
            || vendorType == StreamsPrivateLinkVendorType.MSK
            || vendorType == StreamsPrivateLinkVendorType.GENERIC
            || vendorType == StreamsPrivateLinkVendorType.EVENTHUB;
      }
      default -> {
        return false;
      }
    }
  }

  public StreamsConnectionView createConnection(
      StreamsConnectionView pConnectionView,
      String pTenantName,
      final Group pGroup,
      final AuditInfo pAuditInfo,
      final AppUser pAppUser)
      throws SvcException {

    if (pConnectionView.getType() == StreamsConnectionType.Kafka) {
      StreamsKafkaConnectionView kafkaConnectionView = (StreamsKafkaConnectionView) pConnectionView;
      if (kafkaConnectionView
              .getAuthentication()
              .getMechanism()
              .equals(NDSDataLakeKafkaAuth.Mechanisms.OAUTHBEARER)
          && !_featureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.STREAMS_ENABLE_KAFKA_OIDC, null, pGroup)) {
        throw new SvcException(NDSErrorCode.STREAM_AUTH_OIDC_NOT_SUPPORTED);
      }
    }

    StreamsTenant streamsTenant = getStreamsTenant(pGroup.getId(), pTenantName);
    final ObjectId connectionId = new ObjectId();
    final ObjectId correlationId = new ObjectId();

    LOG.info(
        "STREAMS: correlationId: {}. Create connection for groupId: {}, tenantName: {},"
            + " connectionId: {}, connectionName: {}, connectionType: {}",
        correlationId,
        pGroup.getId(),
        pTenantName,
        connectionId,
        pConnectionView.getName(),
        pConnectionView.getType());

    return _streamsDataLakePrivateSvc.performDataLakeAction(
        (handle) -> {
          // save to stream connection collection first
          final Date now = new Date();
          CloudProvider cloudProvider = CloudProvider.NONE;
          int networkingMode =
              com.xgen.mhouse.services.streamprocessormanager.v1.Models.ProxyType
                  .PROXY_TYPE_UNSPECIFIED_VALUE;

          StreamsConnection connection =
              pConnectionView.toModel(connectionId, streamsTenant.getId(), now, now);

          // Populate authKey for private networking connections. The authKey is an AES 256-bit
          // symmetric key hence the client side (mongostream) key and server side (gwproxy) key is
          // the same.
          if (connection.isPrivateNetworking()) {
            try {
              // If the connection is a private networking connection, then we need to generate an
              // authKey
              // for the connection's proxyInfo which will be used to authenticate "mongostream"
              // with
              // "gwproxy" running in Streams proxy nodes in the Atlas Data plane. This proxy auth
              // key
              // will be passed onto Chef and will be stored in gwproxy's config file on the proxy
              // nodes.
              ProxyInfo proxyInfo = new ProxyInfo();
              proxyInfo.setAuthKey(ProxyInfo.generateAESKey());
              connection.setProxyInfo(proxyInfo);
              // Note: dnsName will be updated async via AWSProvisionVPCProxyRoute53Move.
              // The AWSProvisionVPCProxyRoute53Move move runs after the proxy nodes' IP addresses
              // have been determined. We don't want to populate a non-resolvable dnsName here.
            } catch (Exception e) {
              LOG.error("Failed to generate auth key for private networking connection", e);
              throw new SvcException(NDSErrorCode.INTERNAL, "Failed to generate auth key");
            }
          }

          NDSDataLakeTenant dataLakeTenant;

          try {
            validateConnection(pGroup, pTenantName, connection, pAppUser);

            dataLakeTenant = getDataLakeTenant(streamsTenant);

            if (connection.getConnectionType().supportsPrivateNetworking()
                && pConnectionView.isPrivateNetworking()) {
              cloudProvider = dataLakeTenant.getCloudProvider();
              networkingMode = getNetworkingMode(cloudProvider, connection.getNetworking());
              Optional<VPCProxyDeploymentDescription> desc =
                  _vpcDeploymentProxy.findDeploymentByTenantIdAndConnectionName(
                      streamsTenant.getId(), pConnectionView.getName());
              if (desc.isPresent()) {
                LOG.atInfo()
                    .setMessage(
                        String.format(
                            "createConnection: Reusing VPCProxyDeploymentDescription for id=%s, "
                                + "groupId=%s, tenantId=%s, connectionName=%s",
                            desc.get().getId(),
                            pGroup.getId(),
                            streamsTenant.getId(),
                            pConnectionView.getName()))
                    .log();
                if (Boolean.TRUE.equals(desc.get().isDeleteRequested())) {
                  throw new SvcException(NDSErrorCode.STREAM_VPC_PROXY_DEPLOYMENT_IS_DELETING);
                }
              }
            }

            if (connection.getConnectionType() == AWSLambda
                && dataLakeTenant.getCloudProvider() != CloudProvider.AWS) {
              throw new SvcException(
                  NDSErrorCode.STREAM_AWSLAMBDA_NOT_SUPPORTED_IN_CLOUD_PROVIDER,
                  dataLakeTenant.getCloudProvider().getDescription());
            }

            final List<StreamProcessorView> processors =
                _spmSvc.getStreamProcessors(
                    streamsTenant.getId(),
                    dataLakeTenant.getCloudProvider(),
                    dataLakeTenant.getDataProcessRegion().getRegion(),
                    false,
                    false,
                    true);

            final Set<String> connectionNamesUsedByProcessors =
                _spmSvc.getSourceConnectionNames(processors);
            pConnectionView.setInUse(
                connectionNamesUsedByProcessors.contains(pConnectionView.getName()));

          } catch (SvcException ex) {
            // separate catch for any validation errors
            incrementConnectionErrorCounter(ex, Methods.CREATE);
            throw ex;
          }

          // Insert operation to the streams connection collection and ADF storageConfig is
          // performed within a transaction.
          // We've intentionally limited the scope to ensure that any rollback affects only the
          // streamsConnectionDao and ADF storage config.
          // Note that any svcException arising from the prior validation will not manage the
          // cleanup of the MMS connection.
          try {
            switch (connection.getConnectionType()) {
              case Kafka, S3, AWSKinesisDataStreams -> {
                if (connection.isPrivateNetworking()) {
                  connection.setState(StreamsConnectionState.PENDING);
                } else {
                  connection.setState(StreamsConnectionState.READY);
                }
              }
              case Cluster -> {
                ClusterConnection clusterConn = (ClusterConnection) connection;
                ObjectId clusterGroupId =
                    clusterConn.getClusterGroupId() != null
                        ? new ObjectId(clusterConn.getClusterGroupId())
                        : pGroup.getId();
                Optional<ClusterDescription> cd =
                    _clusterDescriptionDao.findByName(clusterGroupId, clusterConn.getCluster());
                if (cd.isPresent() && cd.get().getState() == State.IDLE) {
                  connection.setState(StreamsConnectionState.READY);
                } else {
                  connection.setState(StreamsConnectionState.PENDING);
                }
              }
              case Sample, Https, AWSLambda -> connection.setState(StreamsConnectionState.READY);
            }

            getStreamsConnectionDao(connection).save(connection);

            NDSDataLakeStorageV1View storage =
                handle.getDataLakeTenantSvc().getStorageConfig(dataLakeTenant);
            Document newStore = pConnectionView.toDocument(pGroup.getId());
            // save to data lake store
            _streamsDataLakePrivateSvc.updateStorageConfig(
                dataLakeTenant, storage, newStore, null, pAuditInfo);

            if (pConnectionView.getType().supportsPrivateNetworking()
                && pConnectionView.isPrivateNetworking()) {
              // This step causes the creation of the GWProxy nodes for supporting private
              // networking.
              createVPCProxyDeploymentDescription(
                  pConnectionView,
                  connection,
                  dataLakeTenant,
                  streamsTenant,
                  cloudProvider,
                  correlationId,
                  networkingMode);
            }
          } catch (MongoWriteException ex) {
            if (ex.getError().getCode() == DUPLICATE_KEY_ERROR_CODE) {
              throw new SvcException(
                  NDSErrorCode.STREAM_CONNECTION_NAME_ALREADY_EXISTS, pConnectionView.getName());
            }

            // We expect to see duplicate key errors, but we should track everything else
            incrementConnectionErrorCounter(ex, Methods.CREATE);

            throw ex;
          } catch (SvcException ex) {
            incrementConnectionErrorCounter(ex, Methods.CREATE);

            // this should only happen if the update to ADF storageConfig fails
            getStreamsConnectionDao(connection)
                .delete(streamsTenant.getId(), pConnectionView.getName());
            throw ex;
          }

          trackConnectionCreatedEvent(
              pGroup, pAuditInfo, streamsTenant, connection, pConnectionView);

          return pConnectionView;
        });
  }

  private void trackConnectionCreatedEvent(
      final Group pGroup,
      final AuditInfo pAuditInfo,
      final StreamsTenant pStreamsTenant,
      final StreamsConnection pConnection,
      final StreamsConnectionView pConnectionView) {
    StreamProcessorConnectionCreatedEvent.Builder connectionEvent =
        StreamProcessorConnectionCreatedEvent.builder();

    connectionEvent.connectionName(pConnectionView.getName());
    connectionEvent.connectionId(pConnection.getId());
    connectionEvent.connectionType(pConnectionView.getType().name());

    // Kafka requires special handling
    // We track:
    // - Security (SSL/PLAIN_TEXT)
    // - Was a custom SSL cert used
    // - Authentication (PLAIN/SCRAM)
    if (pConnectionView.getType().equals(Kafka)
        && pConnection instanceof KafkaConnection kafkaConnection) {
      NDSDataLakeKafkaSecurityProtocol<?> security = kafkaConnection.getSecurity();

      connectionEvent.kafkaSecurity(security.getProtocol());
      if (security.getProtocol().equals(Protocols.SSL.toString())) {
        connectionEvent.pemPresent(
            !StringUtils.isEmpty(
                ((NDSDataLakeKafkaSecuritySSL) security).getConfig().getBrokerPublicCertificate()));
      }

      if (security.getProtocol().equals(Protocols.SASL_SSL.toString())) {
        connectionEvent.pemPresent(
            !StringUtils.isEmpty(
                ((NDSDataLakeKafkaSecuritySASLSSL) security)
                    .getConfig()
                    .getBrokerPublicCertificate()));
      }

      connectionEvent.kafkaAuthentication(kafkaConnection.getAuthentication().getMechanism());
      connectionEvent.kafkaAccessType(
          kafkaConnection.getNetworking().getAccess().getType().toString());
    }

    _segmentEventSvc.submitEvent(
        connectionEvent
            .userId(pAuditInfo.getAppUserId())
            .groupId(pGroup.getId())
            .organizationId(pGroup.getOrgId())
            .tenant(pStreamsTenant.getId())
            .tenantName(pStreamsTenant.getName())
            .build());

    final NDSAudit.Builder builder = new NDSAudit.Builder(Type.STREAM_TENANT_CONNECTION_CREATED);
    builder.groupId(pStreamsTenant.getGroupId());
    builder.auditInfo(pAuditInfo);
    builder.streamTenantId(pStreamsTenant.getId());
    builder.streamTenantName(pStreamsTenant.getName());
    builder.streamConnectionDiff(pConnectionView.makeNewDiffStreamsConnection());
    builder.streamConnectionName(pConnectionView.getName());
    builder.streamConnectionType(pConnectionView.getType().toString());
    _auditSvc.saveAuditEvent(builder.build());
  }

  /** Builds a region-appropriate NodeHardwareDescription for a streams proxy environment. */
  private NodeHardwareDescription buildVPCProxyNodeHardwareDescription(
      final NDSDataLakeTenant dataLakeTenant) throws SvcException {
    Optional<CloudProvider> cloudProvider = Optional.ofNullable(dataLakeTenant.getCloudProvider());
    if (cloudProvider.isEmpty()) {
      // This should not be null, in general, as the upstream methods default to AWS.
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }

    return switch (cloudProvider.get()) {
      case AWS ->
          new AWSNodeHardwareDescription(
              new ObjectId(),
              AWSStreamsInstanceSize.STREAMS_T4G,
              getAwsRegionName(dataLakeTenant),
              2);
      case AZURE ->
          new AzureNodeHardwareDescription(
              new ObjectId(),
              AzureStreamsInstanceSize.STREAMS_B_PROXYNODE,
              getAzureRegionName(dataLakeTenant),
              2);
      case GCP ->
          new GCPNodeHardwareDescription(
              new ObjectId(),
              GCPStreamsInstanceSize.STREAMS_C4A_HIGHCPU_1,
              getGCPRegionName(dataLakeTenant),
              2);
      default -> throw new SvcException(NDSErrorCode.REGION_NOT_SUPPORTED);
    };
  }

  private void createVPCProxyDeploymentDescription(
      final StreamsConnectionView pConnectionView,
      final StreamsConnection connection,
      final NDSDataLakeTenant dataLakeTenant,
      final StreamsTenant streamsTenant,
      final CloudProvider cloudProvider,
      final ObjectId correlationId,
      final Integer networkingMode)
      throws SvcException {
    _vpcDeploymentProxy.createVPCProxyDeploymentDescription(
        new ObjectId(),
        streamsTenant.getId(),
        connection.getName(),
        streamsTenant.getGroupId(),
        List.of(buildVPCProxyNodeHardwareDescription(dataLakeTenant)),
        cloudProvider,
        correlationId,
        networkingMode,
        pConnectionView.getType().toProtoConnectionType().getNumber());
  }

  /** Builds inputs and creates a VPC Proxy deployment description for API consumers. */
  public VPCProxyDeploymentDescription createVPCProxyDeploymentDescriptionForApi(
      final ObjectId deploymentId,
      final ObjectId groupId,
      final ObjectId tenantId,
      final String connectionName)
      throws SvcException {
    final StreamsTenant streamsTenant = this.getStreamsTenantById(groupId, tenantId);
    final NDSDataLakeTenant dataLakeTenant = this.getDataLakeTenant(streamsTenant);

    final CloudProvider cloudProvider = dataLakeTenant.getCloudProvider();
    final List<NodeHardwareDescription> nodes =
        List.of(buildVPCProxyNodeHardwareDescription(dataLakeTenant));

    final StreamsConnection connection =
        this.findConnectionModel(groupId, streamsTenant.getName(), connectionName);
    final int networkingMode = this.getNetworkingMode(cloudProvider, connection.getNetworking());
    final int connectionType =
        switch (connection.getConnectionType()) {
          case Kafka ->
              com.xgen.mhouse.services.streamprocessormanager.v1.Models.ConnectionType
                  .CONNECTION_TYPE_KAFKA_VALUE;
          case S3 ->
              com.xgen.mhouse.services.streamprocessormanager.v1.Models.ConnectionType
                  .CONNECTION_TYPE_S3_VALUE;
          case AWSKinesisDataStreams ->
              com.xgen.mhouse.services.streamprocessormanager.v1.Models.ConnectionType
                  .CONNECTION_TYPE_KINESIS_VALUE;
          default ->
              com.xgen.mhouse.services.streamprocessormanager.v1.Models.ConnectionType
                  .CONNECTION_TYPE_UNSPECIFIED_VALUE;
        };

    final ObjectId correlationId = new ObjectId();
    return _vpcDeploymentProxy.createVPCProxyDeploymentDescription(
        deploymentId,
        streamsTenant.getId(),
        connectionName,
        groupId,
        nodes,
        cloudProvider,
        correlationId,
        networkingMode,
        connectionType);
  }

  public StreamsConnectionView updateConnection(
      StreamsConnectionView pStreamsConnectionView,
      String pConnectionName,
      String pTenantName,
      final Group pGroup,
      final AuditInfo pAuditInfo,
      final AppUser pAppUser)
      throws SvcException {

    final ObjectId correlationId = new ObjectId();
    LOG.info(
        "STREAMS: correlationId: {}. Update connection for groupId: {}, tenantName: {},"
            + " connectionName: {}, connectionType: {}",
        correlationId,
        pGroup.getId(),
        pTenantName,
        pConnectionName,
        pStreamsConnectionView.getType());

    StreamsTenant streamsTenant = getStreamsTenant(pGroup.getId(), pTenantName);

    return _streamsDataLakePrivateSvc.performDataLakeAction(
        (handle) -> {
          NDSDataLakeTenant dataLakeTenant;
          NDSDataLakeStorageV1View storage;
          Document store;
          final Set<String> connectionNamesUsedByProcessors;
          try {
            dataLakeTenant = getDataLakeTenant(streamsTenant);
            storage = handle.getDataLakeTenantSvc().getStorageConfig(dataLakeTenant);
            store =
                storage
                    .getStore(pConnectionName)
                    .orElseThrow(connectionNotFound(streamsTenant.getName(), pConnectionName));

            final List<StreamProcessorView> processors =
                _spmSvc.getStreamProcessors(
                    streamsTenant.getId(),
                    dataLakeTenant.getCloudProvider(),
                    dataLakeTenant.getDataProcessRegion().getRegion(),
                    false,
                    false,
                    true);

            connectionNamesUsedByProcessors = _spmSvc.getSourceConnectionNames(processors);
          } catch (SvcException ex) {
            incrementConnectionErrorCounter(ex, Methods.UPDATE);
            throw ex;
          }

          if (connectionNamesUsedByProcessors.contains(pConnectionName)) {
            throw new SvcException(
                NDSErrorCode.STREAM_CONNECTION_HAS_STREAM_PROCESSORS, pConnectionName);
          }

          List<String> idleKafkaDeployments =
              findDeployingVpcKafkaProxyNames(streamsTenant.getId(), pGroup.getId());

          if (idleKafkaDeployments.contains(pConnectionName)) {
            throw new SvcException(
                NDSErrorCode.STREAM_KAFKA_CONNECTION_IS_DEPLOYING, pConnectionName);
          }

          StreamsConnection oldConnection =
              findConnectionModel(pGroup.getId(), pTenantName, pConnectionName);

          if (!oldConnection
              .getConnectionType()
              .name()
              .equals(pStreamsConnectionView.getType().name())) {
            throw new SvcException(
                NDSErrorCode.STREAM_CONNECTION_TYPE_CANNOT_BE_MODIFIED, oldConnection.getName());
          }

          StreamsConnectionView oldConnectionView =
              StreamsConnectionView.fromStoreAndModel(store, oldConnection, List.of(), false, true);
          StreamsConnectionView mergedView = pStreamsConnectionView.merge(oldConnectionView);

          final Date now = new Date();
          StreamsConnection mergedConnection =
              mergedView.toModel(oldConnection.getId(), streamsTenant.getId(), now, now);

          StreamsConnection newConnection =
              pStreamsConnectionView.toModel(
                  oldConnection.getId(), streamsTenant.getId(), now, now);

          validateConnectionForUpdate(
              pGroup, pTenantName, mergedConnection, newConnection, oldConnection, pAppUser);

          if ((oldConnection.getNetworking() != null && mergedConnection.getNetworking() == null)
              || (oldConnection.getNetworking() == null && mergedConnection.getNetworking() != null)
              || (oldConnection.getNetworking() != null
                  && mergedConnection.getNetworking() != null
                  && oldConnection.getNetworking().getAccess().getType()
                      != mergedConnection.getNetworking().getAccess().getType())) {
            throw new SvcException(NDSErrorCode.STREAM_NETWORKING_ACCESS_TYPE_CANNOT_BE_MODIFIED);
          }

          getStreamsConnectionDao(mergedConnection).update(mergedConnection);

          try {
            Document newStore = mergedView.toDocument(pGroup.getId());

            _streamsDataLakePrivateSvc.updateStorageConfig(
                dataLakeTenant, storage, newStore, pConnectionName, pAuditInfo);

            List<String> diff = oldConnectionView.difference(mergedView);

            _segmentEventSvc.submitEvent(
                StreamProcessorConnectionUpdatedEvent.builder()
                    .userId(pAuditInfo.getAppUserId())
                    .groupId(pGroup.getId())
                    .organizationId(pGroup.getOrgId())
                    .tenant(streamsTenant.getId())
                    .tenantName(streamsTenant.getName())
                    .changedFields(diff)
                    .connectionId(mergedConnection.getId())
                    .connectionName(pConnectionName)
                    .build());

            // This is used to generate proper diff message for audit service
            if (oldConnectionView instanceof AWSLambdaConnectionView oldAWSLambdaConnectionView) {
              oldAWSLambdaConnectionView
                  .getAwsConfig()
                  .setRoleArn(
                      getRoleArn(
                          pGroup.getId(), oldAWSLambdaConnectionView.getAwsConfig().getRoleId()));
            }

            final NDSAudit.Builder builder =
                new NDSAudit.Builder(Type.STREAM_TENANT_CONNECTION_UPDATED);
            builder.groupId(streamsTenant.getGroupId());
            builder.auditInfo(pAuditInfo);
            builder.streamTenantId(streamsTenant.getId());
            builder.streamTenantName(streamsTenant.getName());
            builder.streamConnectionDiff(
                mergedView.makeUpdatedDiffStreamsConnection(oldConnectionView));
            builder.streamConnectionName(mergedView.getName());
            builder.streamConnectionType(mergedView.getType().toString());
            _auditSvc.saveAuditEvent(builder.build());
          } catch (SvcException ex) {
            incrementConnectionErrorCounter(ex, Methods.UPDATE);
            boolean updated =
                getStreamsConnectionDao(oldConnection)
                    .update(oldConnection); // Restore the connection
            LOG.atWarn()
                .setMessage(
                    "STREAMS: Restore old connection after failed update to datalake" + " storage")
                .addKeyValue("restorationSuccess", updated)
                .addKeyValue("connectionName", oldConnection.getName())
                .addKeyValue("tenantId", streamsTenant.getId())
                .addKeyValue("groupId", streamsTenant.getGroupId())
                .addKeyValue("correlationId", correlationId)
                .log();
            throw ex;
          }

          return pStreamsConnectionView;
        });
  }

  public List<String> findDeployingVpcKafkaProxyNames(
      final ObjectId tenantId, final ObjectId groupId) {
    return _vpcDeploymentProxy.findIdleDeployments(groupId, tenantId).stream()
        .map(VPCProxyDeploymentDescription::getConnectionName)
        .collect(Collectors.toList());
  }

  public void deleteConnection(
      String pConnectionName,
      String pTenantName,
      final Group pGroup,
      final AuditInfo pAuditInfo,
      final AppUser pAppUser)
      throws SvcException {
    final StreamsTenant streamsTenant = getStreamsTenant(pGroup.getId(), pTenantName);
    deleteConnection(pConnectionName, streamsTenant, pGroup, pAuditInfo, pAppUser);
  }

  public void deleteConnection(
      String pConnectionName,
      StreamsTenant streamsTenant,
      final Group pGroup,
      final AuditInfo pAuditInfo,
      final AppUser pAppUser)
      throws SvcException {
    final ObjectId correlationId = new ObjectId();
    LOG.info(
        "STREAMS: correlationId: {}. Delete connection for groupId: {}, tenantName: {},"
            + " connectionName: {}",
        correlationId,
        pGroup.getId(),
        streamsTenant.getName(),
        pConnectionName);
    _streamsDataLakePrivateSvc.performDataLakeAction(
        (handle) -> {
          final NDSDataLakeTenant dataLakeTenant;
          final Set<String> connectionNamesUsedByProcessors;

          try {
            dataLakeTenant = getDataLakeTenant(streamsTenant);

            final List<StreamProcessorView> processors =
                _spmSvc.getStreamProcessors(
                    streamsTenant.getId(),
                    dataLakeTenant.getCloudProvider(),
                    dataLakeTenant.getDataProcessRegion().getRegion(),
                    false,
                    false,
                    false);

            connectionNamesUsedByProcessors = _spmSvc.getSourceConnectionNames(processors);

            if (connectionNamesUsedByProcessors.contains(pConnectionName)) {
              throw new SvcException(
                  NDSErrorCode.STREAM_CONNECTION_HAS_STREAM_PROCESSORS, pConnectionName);
            }
          } catch (SvcException ex) {
            incrementConnectionErrorCounter(ex, Methods.DELETE);

            throw ex;
          }

          List<String> idleKafkaDeployments =
              findDeployingVpcKafkaProxyNames(streamsTenant.getId(), pGroup.getId());

          if (idleKafkaDeployments.contains(pConnectionName)) {
            throw new SvcException(
                NDSErrorCode.STREAM_KAFKA_CONNECTION_IS_DEPLOYING, pConnectionName);
          }

          final StreamsConnection connectionToDelete =
              findConnectionModel(pGroup.getId(), streamsTenant.getName(), pConnectionName);
          if (connectionToDelete.getConnectionType() == Cluster) {
            validateUserCrossGroupAccess(pGroup, pAppUser, (ClusterConnection) connectionToDelete);
          }

          if (connectionToDelete.getConnectionType().supportsPrivateNetworking()) {
            try {
              if (connectionToDelete.isPrivateNetworking()) {
                LOG.atInfo()
                    .setMessage(
                        "STREAMS: Deleting deployment description for private networking connection"
                            + " during connection deletion")
                    .addKeyValue("correlationId", correlationId)
                    .addKeyValue("tenantId", streamsTenant.getId())
                    .addKeyValue("connectionName", connectionToDelete.getName())
                    .addKeyValue("groupId", pGroup.getId())
                    .log();
                deleteVPCProxyDeploymentDescription(
                    connectionToDelete.getName(), streamsTenant, correlationId);
              }
            } catch (SvcException ex) {
              LOG.atWarn()
                  .setMessage(
                      "STREAMS: encountered error while deleting deployment description for private"
                          + " networking connection during connection deletion")
                  .addKeyValue("correlationId", correlationId)
                  .addKeyValue("tenantId", streamsTenant.getId())
                  .addKeyValue("connectionName", connectionToDelete.getName())
                  .addKeyValue("groupId", pGroup.getId())
                  .setCause(ex)
                  .log();
            } catch (Exception ex) {
              // This shouldn't happen unless there's an issue with updating the backing cluster for
              // the deployment description or planning the deployment description's deletion
              LOG.atError()
                  .setMessage(
                      "STREAMS: Unexpected error while deleting deployment description for private"
                          + " networking connection")
                  .addKeyValue("correlationId", correlationId)
                  .addKeyValue("tenantId", streamsTenant.getId())
                  .addKeyValue("connectionName", connectionToDelete.getName())
                  .addKeyValue("groupId", pGroup.getId())
                  .setCause(ex)
                  .log();
              incrementConnectionErrorCounter(ex, Methods.DELETE);
              throw ex;
            }
          }

          StreamsConnectionView connectionView;
          try {
            // Delete from local DB
            getStreamsConnectionDao(connectionToDelete)
                .delete(dataLakeTenant.getTenantId(), pConnectionName);

            NDSDataLakeStorageV1View storage =
                handle.getDataLakeTenantSvc().getStorageConfig(dataLakeTenant);

            Optional<Document> storeOpt = storage.getStore(pConnectionName);

            if (storeOpt.isPresent()) {
              Document store = storeOpt.get();
              // Fetch the view before any deletion; need this for auditing / segment.
              connectionView =
                  StreamsConnectionView.fromStoreAndModel(
                      store, connectionToDelete, List.of(), false, true);

              // Delete from ADL
              _streamsDataLakePrivateSvc.updateStorageConfig(
                  dataLakeTenant, storage, null, pConnectionName, pAuditInfo);
            } else {
              throw new SvcException(
                  NDSErrorCode.STREAM_CONNECTION_NOT_FOUND,
                  streamsTenant.getName(),
                  pConnectionName);
            }
          } catch (SvcException ex) {
            if (_streamsDataLakePrivateSvc.isRetryable(ex)) {
              if (_clusterConnectionDao
                  .fetchByTenantIdAndName(streamsTenant.getId(), pConnectionName)
                  .isEmpty()) { // Restore the connection
                getStreamsConnectionDao(connectionToDelete).save(connectionToDelete);
              }
            }

            incrementConnectionErrorCounter(ex, Methods.DELETE);

            throw ex;
          } catch (MongoException ex) {
            incrementConnectionErrorCounter(ex, Methods.DELETE);

            throw ex;
          }

          // This is used to generate proper diff message for audit service
          if (connectionView instanceof AWSLambdaConnectionView awsLambdaConnectionView) {
            awsLambdaConnectionView
                .getAwsConfig()
                .setRoleArn(
                    getRoleArn(pGroup.getId(), awsLambdaConnectionView.getAwsConfig().getRoleId()));
          }

          final NDSAudit.Builder builder =
              new NDSAudit.Builder(Type.STREAM_TENANT_CONNECTION_DELETED);
          builder.groupId(streamsTenant.getGroupId());
          builder.auditInfo(pAuditInfo);
          builder.streamTenantId(streamsTenant.getId());
          builder.streamTenantName(streamsTenant.getName());
          builder.streamConnectionDiff(connectionView.makeRemovedDiffStreamsConnection());
          builder.streamConnectionName(connectionView.getName());
          builder.streamConnectionType(connectionView.getType().toString());
          _auditSvc.saveAuditEvent(builder.build());

          _segmentEventSvc.submitEvent(
              StreamProcessorConnectionDeletedEvent.builder()
                  .userId(pAuditInfo.getAppUserId())
                  .groupId(pGroup.getId())
                  .organizationId(pGroup.getOrgId())
                  .tenant(streamsTenant.getId())
                  .tenantName(streamsTenant.getName())
                  .cascadeDelete(false)
                  .connectionNames(List.of(pConnectionName))
                  .connectionIds(List.of(connectionToDelete.getId()))
                  .build());

          return null;
        });
  }

  private void deleteVPCProxyDeploymentDescription(
      final String pConnectionName, final StreamsTenant streamsTenant, final ObjectId correlationId)
      throws SvcException {
    Optional<VPCProxyDeploymentDescription> vpcProxyDeploymentDescription =
        _vpcDeploymentProxy.findDeploymentByTenantIdAndConnectionName(
            streamsTenant.getId(), pConnectionName);

    if (vpcProxyDeploymentDescription.isEmpty()) {
      LOG.atWarn()
          .setMessage(
              "STREAMS: Attempting to delete deployment description that does not exist --"
                  + " skipping")
          .addKeyValue("tenantId", streamsTenant.getId())
          .addKeyValue("connectionName", pConnectionName)
          .addKeyValue("correlationId", correlationId)
          .addKeyValue("groupId", streamsTenant.getGroupId())
          .log();
      throw new SvcException(NDSErrorCode.STREAM_VPC_PROXY_DEPLOYMENT_DESCRIPTION_NOT_FOUND);
    }
    _vpcDeploymentProxy.requestDeploymentDelete(
        vpcProxyDeploymentDescription.get().getId(), correlationId);
  }

  public boolean isNetworkingTypeVPC(final NDSDataLakeStreamsNetworking networking) {
    // Type conflicts with another package and hence need to use full package name.
    return networking != null
        && networking.getAccess() != null
        && networking.getAccess().getType() != null
        && (networking.getAccess().getType()
            == com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Type.VPC);
  }

  public boolean isNetworkingTypePrivateLink(final NDSDataLakeStreamsNetworking networking) {
    // Type conflicts with another package and hence need to use full package name.
    return networking != null
        && networking.getAccess() != null
        && networking.getAccess().getType() != null
        && (networking.getAccess().getType()
            == com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Type.PRIVATE_LINK);
  }

  public int getNetworkingMode(
      final CloudProvider pCloudProvider, final NDSDataLakeStreamsNetworking pNetworking)
      throws SvcException {
    if (pNetworking == null
        || pNetworking.getAccess() == null
        || pNetworking.getAccess().getType() == null) {
      return com.xgen.mhouse.services.streamprocessormanager.v1.Models.ProxyType
          .PROXY_TYPE_UNSPECIFIED_VALUE;
    }
    com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Type accessType =
        pNetworking.getAccess().getType();
    return switch (accessType) {
      case PRIVATE_LINK ->
          switch (pCloudProvider) {
            case AWS ->
                com.xgen.mhouse.services.streamprocessormanager.v1.Models.ProxyType
                    .PROXY_TYPE_AWS_PRIVATE_LINK_VALUE;
            case AZURE ->
                com.xgen.mhouse.services.streamprocessormanager.v1.Models.ProxyType
                    .PROXY_TYPE_AZURE_PRIVATE_LINK_VALUE;
            case GCP ->
                com.xgen.mhouse.services.streamprocessormanager.v1.Models.ProxyType
                    .PROXY_TYPE_GCP_PRIVATE_LINK_VALUE;
            default ->
                throw new SvcException(
                    CommonErrorCode.VALIDATION_ERROR,
                    String.format("Invalid cloud provider %s for PRIVATE_LINK", pCloudProvider));
          };
      case VPC ->
          switch (pCloudProvider) {
            case AWS ->
                com.xgen.mhouse.services.streamprocessormanager.v1.Models.ProxyType
                    .PROXY_TYPE_AWS_VPC_PEERING_VALUE;
            default ->
                throw new SvcException(
                    CommonErrorCode.VALIDATION_ERROR,
                    String.format("Invalid cloud provider %s for VPC_PEERING", pCloudProvider));
          };
      case TRANSIT_GATEWAY ->
          switch (pCloudProvider) {
            case AWS ->
                com.xgen.mhouse.services.streamprocessormanager.v1.Models.ProxyType
                    .PROXY_TYPE_AWS_TRANSIT_GATEWAY_VALUE;
            default ->
                throw new SvcException(
                    CommonErrorCode.VALIDATION_ERROR,
                    String.format("Invalid cloud provider %s for TRANSIT_GATEWAY", pCloudProvider));
          };
      default ->
          throw new SvcException(
              CommonErrorCode.VALIDATION_ERROR,
              String.format(
                  "Invalid networking access type %s for retrieving private networking mode for"
                      + " cloud provider %s",
                  accessType, pCloudProvider));
    };
  }

  public void viewConnectionRegistryEvent(
      final String pConnectionName,
      final String pTenantName,
      final Group pGroup,
      AuditInfo pAuditInfo)
      throws SvcException {
    try {
      final StreamsTenant streamsTenant = getStreamsTenant(pGroup.getId(), pTenantName);

      final NDSAudit.Builder builder = new NDSAudit.Builder(Type.STREAM_TENANT_CONNECTION_VIEWED);
      builder.groupId(pGroup.getId());
      builder.auditInfo(pAuditInfo);
      builder.streamTenantId(streamsTenant.getId());
      builder.streamTenantName(pTenantName);
      builder.streamConnectionName(pConnectionName);
      _auditSvc.saveAuditEvent(builder.build());
    } catch (SvcException ex) {
      incrementConnectionErrorCounter(ex, Methods.VIEW);
      throw ex;
    }
  }

  private StreamsConnectionDao<? extends StreamsConnection> getStreamsConnectionDao(
      StreamsConnection pConnection) throws SvcException {
    return switch (pConnection.getConnectionType()) {
      case Kafka -> _kafkaConnectionDao;
      case Cluster -> _clusterConnectionDao;
      case Sample -> _sampleConnectionDao;
      case Https -> _httpsConnectionDao;
      case AWSLambda -> _awsLambdaConnectionDao;
      case S3 -> _s3ConnectionDao;
      case AWSKinesisDataStreams -> _kinesisConnectionDao;
    };
  }

  public StreamsTenant getStreamsTenant(ObjectId pGroupId, String pTenantName) throws SvcException {
    return _streamsTenantDao
        .findByGroupAndName(pGroupId, pTenantName)
        .orElseThrow(tenantNotFound(pGroupId, pTenantName));
  }

  public StreamsTenant getStreamsTenantById(ObjectId pGroupId, ObjectId pTenantId)
      throws SvcException {
    return _streamsTenantDao
        .findByGroupAndTenantId(pGroupId, pTenantId)
        .orElseThrow(tenantNotFound(pGroupId, getStreamTenantName(pTenantId)));
  }

  public List<StreamsTenant> getStreamsTenantsByIds(
      List<StreamsTenantAndProjectId> tenantAndProjectIds) {
    return _streamsTenantDao.findTenantsByIds(tenantAndProjectIds);
  }

  public NDSDataLakeTenant getDataLakeTenant(StreamsTenant pTenant) throws SvcException {
    return _streamsDataLakePrivateSvc
        .noLockDataLakeTenantSvc()
        .findByTenantId(pTenant.getId())
        .orElseThrow(tenantNotFound(pTenant.getGroupId(), pTenant.getName()));
  }

  public StreamingOutput getAuditLogsStream(
      final HttpServletResponse pResponse,
      final AuditInfo pAuditInfo,
      final Group pGroup,
      final String pTenant,
      final Instant pStartTimeStamp,
      final Instant pEndTimeStamp)
      throws SvcException {

    StreamsTenant streamsTenant = getStreamsTenant(pGroup.getId(), pTenant);
    NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);

    // Track that we attempted to download the audit logs as an activity feed event.
    final NDSAudit.Builder builder = new NDSAudit.Builder(Type.STREAM_TENANT_AUDIT_LOGS);
    builder.groupId(streamsTenant.getGroupId());
    builder.auditInfo(pAuditInfo);
    builder.streamTenantId(streamsTenant.getId());
    builder.streamTenantName(streamsTenant.getName());
    builder.hidden(false);
    _auditSvc.saveAuditEvent(builder.build());

    return _streamsDataLakePrivateSvc
        .noLockDataLakePublicSvc()
        .streamingLogs(
            pResponse,
            dataLakeTenant,
            pTenant,
            pStartTimeStamp,
            pEndTimeStamp,
            Models.BucketType.BUCKET_TYPE_AUDIT);
  }

  public void deleteAuditLogs(final AuditInfo pAuditInfo, final Group pGroup, final String pTenant)
      throws SvcException {
    StreamsTenant streamsTenant = getStreamsTenant(pGroup.getId(), pTenant);

    NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);

    final NDSAudit.Builder builder = new NDSAudit.Builder(Type.STREAM_TENANT_AUDIT_LOGS_DELETED);
    builder.groupId(streamsTenant.getGroupId());
    builder.auditInfo(pAuditInfo);
    builder.streamTenantId(streamsTenant.getId());
    builder.streamTenantName(streamsTenant.getName());
    builder.hidden(false);
    _auditSvc.saveAuditEvent(builder.build());

    _streamsDataLakePrivateSvc
        .noLockDataLakePublicSvc()
        .deleteLogs(dataLakeTenant, Models.BucketType.BUCKET_TYPE_AUDIT);
  }

  public boolean hasActiveStreamsResources(final ObjectId pGroupId) throws SvcException {
    List<StreamsTenantView> tenants = findByGroupId(pGroupId);
    if (tenants != null && !tenants.isEmpty()) {
      return true;
    }

    // We're using the find functions for these connections calculating the size server-side instead
    // of using an aggregation to free up the collection sooner during calls to this function
    List<StreamsPrivateLink> plConnections = getPrivateLinkConnections(pGroupId);
    if (plConnections != null
        && !plConnections.isEmpty()
        && plConnections.stream().anyMatch(StreamsPrivateLink::isActive)) {
      return true;
    }

    List<VPCPeeringConnection> vpcPeeringConnections = getVPCPeeringConnections(pGroupId);
    if (vpcPeeringConnections != null
        && !vpcPeeringConnections.isEmpty()
        && vpcPeeringConnections.stream().anyMatch(VPCPeeringConnection::isActive)) {
      return true;
    }

    Optional<StreamsTransitGateway> tgwResourceShares = getTGWResourceShares(pGroupId);
    return tgwResourceShares.isPresent() && !tgwResourceShares.get().getResourceShares().isEmpty();
  }

  public StreamsMetricsView getStreamTenantMetrics(final ObjectId pGroupId, final String pName)
      throws SvcException {
    final StreamsTenant streamsTenant =
        _streamsTenantDao.findByGroupAndName(pGroupId, pName).orElseThrow();

    final NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);

    return _spmSvc.getMetrics(
        streamsTenant.getId(),
        dataLakeTenant.getCloudProvider(),
        dataLakeTenant.getDataProcessRegion().getRegion());
  }

  public StreamProcessorGetView modifyStreamProcessor(
      final AuditInfo auditInfo,
      final ObjectId groupId,
      final String tenantName,
      final String processorName,
      final List<Document> pipeline,
      final Boolean resumeFromCheckpoint,
      final DLQ dlq,
      final String newName)
      throws SvcException {
    final StreamsTenant streamsTenant = getStreamsTenant(groupId, tenantName);
    final NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);
    _spmSvc.modifyStreamProcessor(
        auditInfo.getUsername(),
        streamsTenant,
        dataLakeTenant.getCloudProvider(),
        dataLakeTenant.getDataProcessRegion().getRegion(),
        processorName,
        pipeline,
        resumeFromCheckpoint,
        dlq,
        newName);
    return getStreamProcessor(groupId, tenantName, newName != null ? newName : processorName);
  }

  public StreamProcessorCreateView createStreamProcessor(
      final AuditInfo pAuditInfo,
      final ObjectId pGroupId,
      final String pTenantName,
      final String pProcessorName,
      final List<Document> pPipeline,
      final DLQ pDlq)
      throws SvcException {

    final StreamsTenant streamsTenant = getStreamsTenant(pGroupId, pTenantName);
    final NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);

    return _spmSvc.createStreamProcessor(
        pAuditInfo.getUsername(),
        streamsTenant,
        dataLakeTenant.getCloudProvider(),
        dataLakeTenant.getDataProcessRegion().getRegion(),
        pProcessorName,
        pPipeline,
        pDlq);
  }

  public StreamProcessorGetView getStreamProcessor(
      final ObjectId pGroupId, final String pTenantName, final String pProcessorName)
      throws SvcException {
    StreamsTenant streamsTenant = getStreamsTenant(pGroupId, pTenantName);
    NDSDataLakeTenant dataLakeTenant = getDataLakeTenant(streamsTenant);
    return _spmSvc.getStreamProcessor(
        streamsTenant,
        pProcessorName,
        dataLakeTenant.getCloudProvider(),
        dataLakeTenant.getDataProcessRegion().getRegion());
  }

  public List<StreamProcessorView> getStreamTenantProcessors(
      final ObjectId pGroupId,
      final String pName,
      boolean includeStats,
      boolean isFromAdminApi,
      boolean getPipelineConnectionInfoOnly)
      throws SvcException {
    StreamsTenant streamsTenant = getStreamsTenant(pGroupId, pName);
    return getStreamTenantProcessors(
        streamsTenant,
        getDataLakeTenant(streamsTenant),
        includeStats,
        isFromAdminApi,
        getPipelineConnectionInfoOnly);
  }

  public List<StreamProcessorView> getStreamTenantProcessors(
      final StreamsTenant pStreamsTenant,
      final NDSDataLakeTenant dataLakeTenant,
      final boolean includeStats,
      final boolean isFromAdminApi,
      final boolean getPipelineConnectionInfoOnly)
      throws SvcException {

    return _spmSvc.getStreamProcessors(
        pStreamsTenant.getId(),
        dataLakeTenant.getCloudProvider(),
        dataLakeTenant.getDataProcessRegion().getRegion(),
        includeStats,
        isFromAdminApi,
        getPipelineConnectionInfoOnly);
  }

  public List<VPCPeeringConnection> getVPCPeeringConnections(ObjectId groupId) {
    return this._vpcPeeringConnectionSvc.findByGroupId(groupId);
  }

  public void requestDeleteVPCPeeringConnection(ObjectId groupId, String name) {
    Optional<VPCPeeringConnection> connOpt = _vpcPeeringConnectionSvc.findByName(groupId, name);
    if (connOpt.isEmpty() || connOpt.get().getLocalStatus() == Status.REQUEST_DELETE) {
      return;
    }

    _vpcPeeringConnectionSvc.updateLocalStatus(connOpt.get().getId(), Status.REQUEST_DELETE);
  }

  public Optional<StreamsTransitGateway> getTGWResourceShares(ObjectId pGroupId) {
    return _transitGatewayResourceSharesDao.findByGroupId(pGroupId);
  }

  public void requestAcceptVPCPeeringConnection(
      ObjectId groupId, String name, String requesterVpcId, String allowedAccountId) {
    Optional<VPCPeeringConnection> connOpt = _vpcPeeringConnectionSvc.findByName(groupId, name);
    if (!(connOpt.isPresent()
        && connOpt.get().getRequesterAccountId() != null
        && connOpt.get().getRequesterAccountId().equals(allowedAccountId)
        && connOpt.get().getRequesterVpcId() != null
        && connOpt.get().getRequesterVpcId().equals(requesterVpcId)
        && connOpt.get().getCloudStatus() != null)) {
      return;
    }

    VPCPeeringConnection conn = connOpt.get();
    if (conn.getRequesterVpcId().equals(requesterVpcId)
        && conn.getRequesterAccountId().equals(allowedAccountId)
        && conn.getCloudStatus().equals("pending-acceptance")
        && conn.getLocalStatus() == Status.NONE) {
      _vpcPeeringConnectionSvc.updateLocalStatus(conn.getId(), Status.REQUEST_ACCEPT);
    }
  }

  public void requestRejectVPCPeeringConnection(ObjectId groupId, String name) {
    Optional<VPCPeeringConnection> connOpt = _vpcPeeringConnectionSvc.findByName(groupId, name);
    if (!(connOpt.isPresent()
        && connOpt.get().getCloudStatus() != null
        && connOpt.get().getCloudStatus().equals("pending-acceptance")
        && connOpt.get().getLocalStatus() != Status.REQUEST_REJECT)) {
      return;
    }
    _vpcPeeringConnectionSvc.updateLocalStatus(connOpt.get().getId(), Status.REQUEST_REJECT);
  }

  public ObjectId createTransitGatewayRoute(
      final Group pGroup,
      final ApiStreamsTransitGatewayRouteRequestView pTransitGatewayRouteView,
      ObjectId correlationId)
      throws SvcException {

    if (!_featureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.STREAMS_TRANSIT_GATEWAY_ENABLED, null, pGroup)) {
      throw new SvcException(NDSErrorCode.STREAM_AWS_TRANSIT_GATEWAY_IS_NOT_SUPPORTED);
    }

    validateAWSTransitGatewayRoute(pTransitGatewayRouteView);

    ObjectId id = new ObjectId();
    String tgwId = pTransitGatewayRouteView.getTgwId();
    String destinationCidr = pTransitGatewayRouteView.getDestinationCidr();
    String region = pTransitGatewayRouteView.getRegion();
    StreamsTransitGatewayRoute transitGatewayRoute =
        new StreamsTransitGatewayRoute(id, pGroup.getId(), tgwId, destinationCidr, region);

    final NDSGroup ndsGroup =
        _ndsGroupSvc
            .find(pGroup.getId())
            .orElseThrow(
                () ->
                    new SvcException(
                        INVALID_ARGUMENT,
                        String.format("group id. No Group found for id '%s'", pGroup.getId())));

    LOG.atInfo()
        .setMessage(
            String.format(
                "STREAMS: correlationId: %s. Creating route for Transit Gateway", correlationId))
        .addKeyValue("correlationId", correlationId)
        .addKeyValue("group", pGroup.getId())
        .addKeyValue("tgwId", tgwId)
        .addKeyValue("destinationCidr", destinationCidr)
        .addKeyValue("region", region)
        .log();

    AWSCloudProviderContainer container = getAWSCloudProviderContainer(ndsGroup, region);
    String vpcId = getVPCIdFromContainer(ndsGroup, container, region);

    LOG.atDebug()
        .setMessage(
            String.format("STREAMS: correlationId: %s. Found vpc for atlas account", correlationId))
        .addKeyValue("correlationId", correlationId)
        .addKeyValue("group", pGroup.getId())
        .addKeyValue("vpcId", vpcId)
        .addKeyValue("containerId", container.getId())
        .log();

    RouteTable routeTable = getMainRouteTableForVPC(container, vpcId);

    LOG.atDebug()
        .setMessage(
            String.format(
                "STREAMS: correlationId: %s. Found main route table for atlas aws vpc",
                correlationId))
        .addKeyValue("correlationId", correlationId)
        .addKeyValue("group", pGroup.getId())
        .addKeyValue("vpcId", vpcId)
        .addKeyValue("containerId", container.getId())
        .addKeyValue("routeTableId", routeTable.getRouteTableId())
        .log();

    validateTransitGatewayRouteCreationInRouteTable(destinationCidr, routeTable, container);

    _awsApiSvc.createRouteForTransitGatewayRouteTable(
        container.getAWSAccountId(),
        container.getRegion(),
        LOG,
        tgwId,
        destinationCidr,
        routeTable.getRouteTableId());

    // If the route creation is successful in AWS, then insert a document in the backing
    // collection and return the object ID of the inserted document.
    return _transitGatewayRouteDao.create(transitGatewayRoute);
  }

  // Remove the pipeline information if the current user does not have the admin permissions for
  // the given org and group
  public List<StreamProcessorView> filterPipelineInfo(
      AppUser pAppUser, ObjectId orgId, ObjectId groupId, List<StreamProcessorView> processors) {
    Set<Role> adminRoles = new HashSet<>();
    adminRoles.addAll(GROUP_STREAM_PROCESSING_OWNER.getRoles());
    adminRoles.addAll(GROUP_DATA_ACCESS_ANY.getRoles());
    adminRoles.addAll(ORG_STREAM_PROCESSING_ADMIN.getRoles());

    if (pAppUser.getRoles().stream()
        .filter(
            r ->
                r.getRole().isGlobal()
                    || orgId.equals(r.getOrgId())
                    || groupId.equals(r.getGroupId()))
        .noneMatch(r -> adminRoles.contains(r.getRole()))) {
      return processors.stream().map(spv -> spv.setPipeline(List.of())).toList();
    }

    return processors;
  }

  private AWSCloudProviderContainer getAWSCloudProviderContainer(NDSGroup pNDSGroup, String pRegion)
      throws SvcException {
    AWSCloudProviderContainer container;
    Optional<AWSCloudProviderContainer> containerOpt =
        StreamsUtil.getAWSContainer(pNDSGroup, AWSRegionName.valueOf(pRegion));
    if (containerOpt.isPresent()) {
      container = containerOpt.get();
    } else {
      throw new SvcException(
          NDSErrorCode.INTERNAL,
          new Exception(
              String.format(
                  "No AWS cloud container found for group: '%s' in region: '%s'",
                  pNDSGroup.getGroupId(), pRegion)));
    }
    return container;
  }

  private String getVPCIdFromContainer(
      NDSGroup pNdsGroup, AWSCloudProviderContainer pContainer, String pRegion)
      throws SvcException {
    String vpcId;
    Optional<String> vpcIdOpt = pContainer.getVpcId();
    if (vpcIdOpt.isPresent()) {
      vpcId = vpcIdOpt.get();
    } else {
      throw new SvcException(
          NDSErrorCode.INTERNAL,
          new Exception(
              String.format(
                  "No AWS VPC found for group: '%s' in region: '%s' using container: '%s'",
                  pNdsGroup.getGroupId(), pRegion, pContainer.getId())));
    }
    return vpcId;
  }

  private RouteTable getMainRouteTableForVPC(AWSCloudProviderContainer pContainer, String pVPCId)
      throws SvcException {
    RouteTable routeTable;
    Optional<RouteTable> routeTableOpt =
        _awsApiSvc.findMainRouteTableForVpc(
            pContainer.getAWSAccountId(), pContainer.getRegion(), LOG, pVPCId);

    if (routeTableOpt.isPresent()) {
      routeTable = routeTableOpt.get();
    } else {
      throw new SvcException(
          NDSErrorCode.INTERNAL,
          new Exception(
              String.format(
                  "No route table found for vpc: '%s' and region: '%s' in AWS account: '%s'",
                  pVPCId, pContainer.getRegion().getName(), pContainer.getAWSAccountId())));
    }
    return routeTable;
  }

  public Optional<StreamsTransitGatewayRoute> getTransitGatewayRoute(
      final Group pGroup, ObjectId tgwRouteId) throws SvcException {
    if (!_featureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.STREAMS_TRANSIT_GATEWAY_ENABLED, null, pGroup)) {
      throw new SvcException(NDSErrorCode.STREAM_AWS_TRANSIT_GATEWAY_IS_NOT_SUPPORTED);
    }

    if (tgwRouteId == null || tgwRouteId.toHexString().equalsIgnoreCase("0".repeat(24))) {
      throw new SvcException(
          INVALID_ARGUMENT,
          String.format(
              "tgwRouteId: '%s'. tgwRouteId cannot be null or empty object Id.",
              tgwRouteId == null ? "null" : tgwRouteId.toHexString()));
    }
    return _transitGatewayRouteDao.findByGroupIdAndId(pGroup.getId(), tgwRouteId);
  }

  public List<StreamsTransitGatewayRoute> listTransitGatewayRoutes(final Group pGroup)
      throws SvcException {
    if (!_featureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.STREAMS_TRANSIT_GATEWAY_ENABLED, null, pGroup)) {
      throw new SvcException(NDSErrorCode.STREAM_AWS_TRANSIT_GATEWAY_IS_NOT_SUPPORTED);
    }

    return _transitGatewayRouteDao.findByGroupId(pGroup.getId());
  }

  public void deleteTransitGatewayRoute(
      final Group pGroup, ObjectId tgwRouteId, ObjectId correlationId) throws SvcException {

    if (tgwRouteId == null || tgwRouteId.toHexString().equalsIgnoreCase("0".repeat(24))) {
      throw new SvcException(INVALID_ARGUMENT, "tgwRouteId. Please provide a valid tgwRouteId");
    }

    Optional<StreamsTransitGatewayRoute> routeOpt = getTransitGatewayRoute(pGroup, tgwRouteId);
    if (routeOpt.isEmpty()) {
      throw new SvcException(
          CommonErrorCode.NOT_FOUND,
          String.format(
              "Transit gateway route with id '%s' under group '%s'", tgwRouteId, pGroup.getId()));
    }

    if (isTransitGatewayRouteInUse(tgwRouteId)) {
      throw new SvcException(NDSErrorCode.STREAM_TRANSIT_GATEWAY_ROUTE_IN_USE);
    }

    StreamsTransitGatewayRoute route = routeOpt.get();
    String region = route.getRegion();
    String destinationCidr = route.getDestinationCidr();
    String tgwId = route.getTgwId();

    final NDSGroup ndsGroup =
        _ndsGroupSvc
            .find(pGroup.getId())
            .orElseThrow(
                () ->
                    new SvcException(
                        INVALID_ARGUMENT,
                        String.format("group id. No Group found for id '%s'", pGroup.getId())));

    LOG.atInfo()
        .setMessage(
            String.format(
                "STREAMS: correlationId: %s. Deletion route for Transit Gateway", correlationId))
        .addKeyValue("correlationId", correlationId)
        .addKeyValue("group", pGroup.getId())
        .addKeyValue("tgwRouteId", tgwRouteId.toHexString())
        .addKeyValue("tgwId", tgwId)
        .addKeyValue("destinationCidr", destinationCidr)
        .addKeyValue("region", region)
        .log();

    AWSCloudProviderContainer container = getAWSCloudProviderContainer(ndsGroup, region);
    String vpcId = getVPCIdFromContainer(ndsGroup, container, region);

    LOG.atDebug()
        .setMessage(
            String.format("STREAMS: correlationId: %s. Found vpc for atlas account", correlationId))
        .addKeyValue("correlationId", correlationId)
        .addKeyValue("group", pGroup.getId())
        .addKeyValue("vpcId", vpcId)
        .addKeyValue("containerId", container.getId())
        .log();

    RouteTable routeTable = getMainRouteTableForVPC(container, vpcId);

    LOG.atDebug()
        .setMessage(
            String.format(
                "STREAMS: correlationId: %s. Found main route table for atlas aws vpc",
                correlationId))
        .addKeyValue("correlationId", correlationId)
        .addKeyValue("group", pGroup.getId())
        .addKeyValue("vpcId", vpcId)
        .addKeyValue("containerId", container.getId())
        .addKeyValue("routeTableId", routeTable.getRouteTableId())
        .log();

    _awsApiSvc.removeRouteForRouteTable(
        container.getAWSAccountId(),
        container.getRegion(),
        LOG,
        destinationCidr,
        routeTable.getRouteTableId());

    _transitGatewayRouteDao.delete(tgwRouteId);
  }

  public ObjectId createPrivateLinkConnection(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final Group pGroup, ObjectId correlationId)
      throws SvcException {

    CloudProvider cloudProvider = CloudProvider.findByDescription(pPrivateLinkView.getProvider());
    if (cloudProvider == null) {
      throw new SvcException(
          NDSErrorCode.INVALID_CLOUD_PROVIDER,
          pPrivateLinkView.getProvider() == null ? "null" : pPrivateLinkView.getProvider());
    }

    Optional<StreamsPrivateLinkVendorType> vendor =
        StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor());
    if (vendor.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format("vendor. vendor cannot be '%s'", pPrivateLinkView.getVendor()));
    }

    if (!_featureFlagSvc.isFeatureFlagEnabled(FeatureFlag.STREAMS_AZURE_PRIVATE_LINK, null, pGroup)
        && cloudProvider == CloudProvider.AZURE) {
      throw new SvcException(NDSErrorCode.STREAM_PRIVATE_LINK_IS_NOT_SUPPORTED);
    }

    if (!_featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.STREAMS_AZURE_CONFLUENT_PRIVATE_LINK, null, pGroup)
        && cloudProvider == CloudProvider.AZURE
        && vendor.get() == StreamsPrivateLinkVendorType.CONFLUENT) {
      throw new SvcException(NDSErrorCode.STREAM_PRIVATE_LINK_IS_NOT_SUPPORTED);
    }

    if (cloudProvider == CloudProvider.AWS) {
      if (vendor.get() == StreamsPrivateLinkVendorType.KINESIS) {
        if (!_featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.STREAMS_AWS_KINESIS_PRIVATE_LINK, null, pGroup)) {
          throw new SvcException(NDSErrorCode.STREAM_KINESIS_PRIVATE_LINK_IS_NOT_SUPPORTED);
        }
      } else if (vendor.get() == StreamsPrivateLinkVendorType.S3
          && !_featureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.STREAMS_AWS_S3_PRIVATE_LINK, null, pGroup)) {
        throw new SvcException(NDSErrorCode.STREAM_S3_PRIVATE_LINK_IS_NOT_SUPPORTED);
      } else if (!_featureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.STREAMS_AWS_PRIVATE_LINK, null, pGroup)) {
        throw new SvcException(NDSErrorCode.STREAM_PRIVATE_LINK_IS_NOT_SUPPORTED);
      }
    }

    if (cloudProvider == CloudProvider.GCP) {
      if (!_featureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.STREAMS_GCP_PRIVATE_LINK, null, pGroup)) {
        throw new SvcException(NDSErrorCode.STREAM_PRIVATE_LINK_IS_NOT_SUPPORTED);
      }
      if (vendor.get() == StreamsPrivateLinkVendorType.CONFLUENT) {
        if (!_featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.STREAMS_GCP_CONFLUENT_PRIVATE_LINK, null, pGroup)) {
          throw new SvcException(NDSErrorCode.STREAM_PRIVATE_LINK_IS_NOT_SUPPORTED);
        }
      }
    }

    LOG.atInfo()
        .setMessage("STREAMS: Creating private link connection")
        .addKeyValue("correlationId", correlationId)
        .addKeyValue("groupId", pGroup.getId())
        .addKeyValue("cloudProvider", cloudProvider)
        .log();

    ObjectId pGroupId = pGroup.getId();
    return switch (cloudProvider) {
      case AWS -> createAWSPrivateLinkConnection(pPrivateLinkView, pGroupId, correlationId);
      case AZURE -> createAzurePrivateLinkConnection(pPrivateLinkView, pGroupId, correlationId);
      case GCP -> createGCPPrivateLinkConnection(pPrivateLinkView, pGroupId, correlationId);
      default -> throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    };
  }

  public ObjectId createGCPPrivateLinkConnection(
      final ApiStreamsPrivateLinkView pPrivateLinkView,
      final ObjectId pGroupId,
      ObjectId correlationId)
      throws SvcException {
    try {
      validateGCPPrivateLinkConnection(pPrivateLinkView, pGroupId);

      StreamsPrivateLinkVendorType vendor =
          StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor()).orElseThrow();

      GCPRegionName region =
          GCPRegionName.findByNameOrValue(pPrivateLinkView.getRegion()).orElseThrow();

      LOG.atInfo()
          .setMessage("STREAMS: Ensuring container in Streams Private Link")
          .addKeyValue("correlationId", correlationId)
          .addKeyValue("groupId", pGroupId)
          .addKeyValue("region", region)
          .log();
      this._containerSvc.ensureCloudContainer(pGroupId, region);

      final ObjectId id = new ObjectId();

      final StreamsPrivateLink privateLink =
          new StreamsPrivateLink(
              id,
              pGroupId,
              vendor,
              StreamsPrivateLinkProviderType.findByValue(pPrivateLinkView.getProvider())
                  .orElseThrow(),
              null,
              null,
              null,
              pPrivateLinkView.getGcpServiceAttachmentUris(), // serviceAttachmentUris
              null,
              pPrivateLinkView.getDnsDomain(),
              null,
              null,
              null,
              new Date(),
              StreamsPrivateLinkState.IDLE,
              null,
              0,
              null,
              Instant.now(),
              region.getValue(),
              null,
              null);

      return _privateLinkDao.create(privateLink);
    } catch (final MongoWriteException ex) {
      incrementConnectionErrorCounter(ex, Methods.CREATE);
      throw new SvcException(INVALID_OPTIONS, "Failed to create Private Link");
    }
  }

  public ObjectId createAWSPrivateLinkConnection(
      final ApiStreamsPrivateLinkView pPrivateLinkView,
      final ObjectId pGroupId,
      ObjectId correlationId)
      throws SvcException {
    try {
      validateAWSPrivateLinkConnection(pPrivateLinkView, pGroupId);

      StreamsPrivateLinkVendorType vendor =
          StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor()).orElseThrow();

      AWSRegionName region;
      if (vendor == StreamsPrivateLinkVendorType.MSK) {
        region = getAWSRegionNameFromARN(pPrivateLinkView.getAmazonResourceName());
      } else {
        region = AWSRegionName.findByNameOrValue(pPrivateLinkView.getRegion()).orElseThrow();
      }

      String serviceEndpointId = pPrivateLinkView.getServiceEndpointId();

      LOG.info(
          "STREAMS: correlationId: {}. Ensuring container in Streams Private Link for groupId: {}"
              + " and region: {}",
          correlationId,
          pGroupId,
          region);
      this._containerSvc.ensureCloudContainer(pGroupId, region);

      final ObjectId id = new ObjectId();

      final StreamsPrivateLink privateLink =
          new StreamsPrivateLink(
              id,
              pGroupId,
              vendor,
              StreamsPrivateLinkProviderType.valueOf(pPrivateLinkView.getProvider().toUpperCase()),
              serviceEndpointId,
              pPrivateLinkView.getAmazonResourceName(),
              pPrivateLinkView.getAzureResourceIds(),
              null, // serviceAttachmentUris
              null, // vpcConnectionArn
              pPrivateLinkView.getDnsDomain(),
              pPrivateLinkView.getDnsSubDomain(),
              null,
              null,
              new Date(),
              StreamsPrivateLinkState.IDLE,
              null,
              0,
              null,
              Instant.now(),
              region.getValue(),
              null,
              null);

      return _privateLinkDao.create(privateLink);
    } catch (final MongoWriteException ex) {
      incrementConnectionErrorCounter(ex, Methods.CREATE);
      throw new SvcException(INVALID_OPTIONS, "Failed to create Private Link");
    }
  }

  private static String getRegionalServiceEndpointId(
      StreamsPrivateLinkVendorType vendor, AWSRegionName region) throws SvcException {
    String serviceEndpointId;
    switch (vendor) {
      case S3 -> serviceEndpointId = String.format("com.amazonaws.%s.s3", region.getValue());
      case KINESIS ->
          serviceEndpointId = String.format("com.amazonaws.%s.kinesis-streams", region.getValue());
      default ->
          throw new SvcException(
              INVALID_ARGUMENT,
              String.format(
                  "vendor. vendor '%s' does not support regional private link endpoint.", vendor));
    }
    return serviceEndpointId;
  }

  public ObjectId createAzurePrivateLinkConnection(
      final ApiStreamsPrivateLinkView pPrivateLinkView,
      final ObjectId pGroupId,
      ObjectId correlationId)
      throws SvcException {

    try {
      LOG.info(
          "STREAMS: correlationId: {}. Validating Azure PrivateLink Connection for group: {}",
          correlationId,
          pGroupId);
      validateAzurePrivateLinkConnection(pPrivateLinkView, pGroupId);

      LOG.info(
          "STREAMS: correlationId: {}. Ensuring container in Streams Private Link for group: {}",
          correlationId,
          pGroupId);
      this._containerSvc.ensureCloudContainer(
          pGroupId, AzureRegionName.findByNameOrValue(pPrivateLinkView.getRegion()).orElseThrow());

      final ObjectId id = new ObjectId();

      final StreamsPrivateLink privateLink =
          new StreamsPrivateLink(
              id,
              pGroupId,
              StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor()).orElseThrow(),
              StreamsPrivateLinkProviderType.valueOf(pPrivateLinkView.getProvider().toUpperCase()),
              pPrivateLinkView.getServiceEndpointId(),
              pPrivateLinkView.getAmazonResourceName(),
              pPrivateLinkView.getAzureResourceIds(),
              null, // serviceAttachmentUris
              null, // vpcConnectionArn
              pPrivateLinkView.getDnsDomain(),
              pPrivateLinkView.getDnsSubDomain(),
              null,
              null,
              new Date(),
              StreamsPrivateLinkState.IDLE,
              null,
              0,
              null,
              Instant.now(),
              pPrivateLinkView.getRegion(),
              null,
              null);

      return _privateLinkDao.create(privateLink);
    } catch (final MongoWriteException ex) {
      incrementConnectionErrorCounter(ex, Methods.CREATE);
      throw new SvcException(
          INVALID_OPTIONS,
          String.format(
              "STREAMS: correlationId: %s. Failed to create Azure Private Link for group: %s",
              correlationId.toHexString(), pGroupId.toHexString()));
    }
  }

  public void deletePrivateLinkConnection(final ObjectId pGroupId, final ObjectId pPrivateLinkId)
      throws SvcException {
    try {
      if (isPrivateLinkConnectionInUse(pPrivateLinkId)) {
        throw new SvcException(NDSErrorCode.STREAM_PRIVATE_LINK_IN_USE);
      }
      _privateLinkDao.requestDeletion(pGroupId, pPrivateLinkId);
    } catch (final MongoWriteException ex) {
      incrementConnectionErrorCounter(ex, Methods.DELETE);
      throw new SvcException(INVALID_OPTIONS, "Failed to delete Private Link");
    }
  }

  public boolean isPrivateLinkConnectionInUse(ObjectId pConnectionId) {
    return !_kafkaConnectionDao.fetchByPrivateLinkConnectionId(pConnectionId).isEmpty();
  }

  public boolean isTransitGatewayRouteInUse(ObjectId tgwRouteId) {
    return !_kafkaConnectionDao.fetchByTransitGatewayRouteId(tgwRouteId).isEmpty();
  }

  public void isPrivateLinkValid(
      ObjectId pGroupId,
      KafkaConnection kConn,
      String bootStrapServers,
      StreamsTenant streamsTenant)
      throws SvcException {
    ObjectId pConnectionId = kConn.getNetworking().getAccess().getConnectionId();
    Optional<StreamsPrivateLink> pl = _privateLinkDao.findByGroupIdAndId(pGroupId, pConnectionId);
    if (pl.isEmpty()) {
      throw new SvcException(NDSErrorCode.STREAM_PRIVATE_LINK_NOT_FOUND, pConnectionId);
    }
    if (pl.get().getState() != StreamsPrivateLinkState.DONE) {
      throw new SvcException(NDSErrorCode.STREAM_PRIVATE_LINK_IS_NOT_READY, pConnectionId);
    }

    // Ensure that the tenant SPI for the connection being created matches the cloud
    // provider that the PrivateLink connection resides in.
    NDSDataLakeTenant dlTenant = getDataLakeTenant(streamsTenant);
    if (dlTenant.getCloudProvider() != pl.get().getProvider().getCloudProvider()) {
      throw new SvcException(NDSErrorCode.STREAM_PRIVATE_NETWORK_PROVIDER_MISMATCH);
    }

    if (pl.get().getVendor() == StreamsPrivateLinkVendorType.CONFLUENT) {
      String domainTopLevel = getTopLevelDomain(pl.get().getDnsDomain());
      String brokerWithoutPort = bootStrapServers.split(":")[0];

      if (!domainTopLevel.equals(getTopLevelDomain(brokerWithoutPort))) {
        throw new SvcException(NDSErrorCode.STREAM_TENANT_INVALID_BOOTSTRAP_SERVER, domainTopLevel);
      }
    }
  }

  public static String getTopLevelDomain(String fqdn) throws SvcException {
    String[] fqdnArray = fqdn.split("\\.");
    if (fqdnArray.length < 2) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "domain name");
    }

    return String.format("%s.%s", fqdnArray[fqdnArray.length - 2], fqdnArray[fqdnArray.length - 1])
        .toLowerCase();
  }

  public Optional<StreamsPrivateLink> getPrivateLinkConnection(
      final ObjectId pGroupId, final ObjectId pPrivateLinkId) throws SvcException {
    try {
      return _privateLinkDao.findByGroupIdAndId(pGroupId, pPrivateLinkId);
    } catch (final MongoWriteException ex) {
      incrementConnectionErrorCounter(ex, Methods.VIEW);
      throw new SvcException(INVALID_OPTIONS, "Failed to get Private Link connection");
    }
  }

  public List<StreamsPrivateLink> getPrivateLinkConnections(final ObjectId pGroupId)
      throws SvcException {
    try {
      return _privateLinkDao.findByGroupId(pGroupId);
    } catch (final MongoWriteException ex) {
      incrementConnectionErrorCounter(ex, Methods.VIEW);
      throw new SvcException(INVALID_OPTIONS, "Failed to get Private Link connections");
    }
  }

  public Optional<AWSCloudProviderContainer> getAwsCloudProviderContainer(
      final ObjectId pGroupId, final String pAwsRegionName) throws SvcException {
    NDSGroup ndsGroup =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    return StreamsUtil.getAWSContainer(ndsGroup, AWSRegionName.valueOf(pAwsRegionName));
  }

  public Optional<AzureCloudProviderContainer> getAzureCloudProviderContainer(
      final ObjectId pGroupId, final String pAzureRegionName) throws SvcException {
    NDSGroup ndsGroup =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    return StreamsUtil.getAzureContainer(ndsGroup, AzureRegionName.valueOf(pAzureRegionName));
  }

  public Optional<GCPCloudProviderContainer> getGcpCloudProviderContainer(
      final ObjectId pGroupId, final String pGcpRegionName) throws SvcException {
    NDSGroup ndsGroup =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    return StreamsUtil.getGCPContainer(ndsGroup, GCPRegionName.valueOf(pGcpRegionName));
  }

  public String getGcpProjectId(GCPCloudProviderContainer container) throws SvcException {
    try {
      return container.getGcpProjectId().orElseThrow();
    } catch (Exception e) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
  }

  public String getNativeAwsAccountId(AWSCloudProviderContainer container) throws SvcException {
    try {
      final AWSAccount awsAccount = _awsAccountDao.find(container.getAWSAccountId()).orElseThrow();
      final String rootOrRoleArn = awsAccount.getAssumeRoleARN().orElse(awsAccount.getRootARN());

      return AwsUtils.getAccountIdFromArn(rootOrRoleArn);
    } catch (Exception e) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
  }

  public String getAzureSubscriptionId(AzureCloudProviderContainer container) throws SvcException {
    try {
      return _azureSubscriptionDao
          .find(container.getAzureSubscriptionId())
          .orElseThrow()
          .getSubscriptionId();
    } catch (Exception e) {
      throw new SvcException(NDSErrorCode.INVALID_CLOUD_PROVIDER);
    }
  }

  private void validateAWSTransitGatewayRoute(
      final ApiStreamsTransitGatewayRouteRequestView pTransitGatewayRouteView) throws SvcException {
    String destinationCidr = pTransitGatewayRouteView.getDestinationCidr();
    if (destinationCidr == null || destinationCidr.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "%s. Please provide a non-empty value.",
              ApiStreamsTransitGatewayRouteRequestView.FieldDefs.DESTINATION_CIDR));
    } else if (!NetUtils.isValidIPv4CidrNotation(destinationCidr)) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "%s. The destination CIDR '%s' is not a valid IPv4 CIDR.",
              ApiStreamsTransitGatewayRouteRequestView.FieldDefs.DESTINATION_CIDR,
              destinationCidr));
    }

    String tgwId = pTransitGatewayRouteView.getTgwId();
    if (tgwId == null || tgwId.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "%s. Please provide a non-empty value.",
              ApiStreamsTransitGatewayRouteRequestView.FieldDefs.TGW_ID));
    } else if (!tgwId.matches("^(tgw-)[0-9a-zA-Z]+$")) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "%s. The transit gateway Id '%s' is not a valid AWS transit gateway Id.",
              ApiStreamsTransitGatewayRouteRequestView.FieldDefs.TGW_ID, tgwId));
    }

    String region = pTransitGatewayRouteView.getRegion();
    if (region == null || region.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "%s. Please provide a non-empty value.",
              ApiStreamsTransitGatewayRouteRequestView.FieldDefs.REGION));
    } else if (!ModelValidationUtils.isValidRegionName(region, CloudProvider.AWS)) {
      throw new SvcException(NDSErrorCode.INVALID_REGION, region, CloudProvider.AWS);
    }
  }

  public static void validateTransitGatewayRouteCreationInRouteTable(
      String destinationCidr, RouteTable routeTable, AWSCloudProviderContainer container)
      throws SvcException {
    // Basic validation criteria:
    // - The CIDR provided should be valid
    // - It should not be a default route (eg. should not match 0.0.0.0/0)
    // - It should not overlap with the MongoDB netblock
    // - It will overlap with the default route, which is allowable
    if (!NetUtils.isValidIPv4CidrNotation(destinationCidr)) {
      throw new SvcException(
          INVALID_ARGUMENT,
          String.format(
              "%s. The provided destination CIDR: '%s' is not a valid IPv4 CIDR.",
              StreamsTransitGatewayRoute.FieldDefs.DESTINATION_CIDR, destinationCidr));
    }

    SubnetUtils subnetUtils = new SubnetUtils(destinationCidr);
    if (subnetUtils.getInfo().getAddress().equals("0.0.0.0")) {
      throw new SvcException(
          INVALID_ARGUMENT,
          String.format(
              "%s. The provided destination CIDR: '%s' is the default route which is not allowed.",
              StreamsTransitGatewayRoute.FieldDefs.DESTINATION_CIDR, destinationCidr));
    }

    if (NetUtils.areOverlappingIpv4CidrBlocks(destinationCidr, container.getAtlasCidr())) {
      throw new SvcException(
          INVALID_ARGUMENT,
          String.format(
              "%s. Invalid destination CIDR provided: %s, overlaps with Atlas CIDR block: %s",
              StreamsTransitGatewayRoute.FieldDefs.DESTINATION_CIDR,
              destinationCidr,
              container.getAtlasCidr()));
    }

    String overlappingRoutes =
        routeTable.getRoutes().stream()
            .map(Route::getDestinationCidrBlock)
            .filter(
                existingRoute ->
                    NetUtils.areOverlappingIpv4CidrBlocks(destinationCidr, existingRoute))
            .filter(
                existingRoute ->
                    !ALLOWED_ROUTES_IN_DEFAULT_ROUTABLE_IN_ATLAS_VPC.contains(existingRoute))
            .collect(Collectors.joining(","));

    if (!overlappingRoutes.isEmpty()) {
      throw new SvcException(
          INVALID_ARGUMENT,
          String.format(
              "%s. Invalid destination CIDR provided (%s), overlaps existing peering route(s): %s",
              StreamsTransitGatewayRoute.FieldDefs.DESTINATION_CIDR,
              destinationCidr,
              overlappingRoutes));
    }
  }

  private void validateAWSPrivateLinkConnection(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final ObjectId pGroupId)
      throws SvcException {
    Optional<StreamsPrivateLinkVendorType> vendor =
        StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor());
    if (vendor.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format("vendor. vendor cannot be '%s'", pPrivateLinkView.getVendor()));
    }
    switch (vendor.get()) {
      case CONFLUENT -> validateAWSConfluentPrivateLink(pPrivateLinkView, pGroupId);
      case MSK -> validateMSKPrivateLink(pPrivateLinkView, pGroupId);
      case KINESIS, S3 -> validateAWSRegionalStreamPrivateLink(pPrivateLinkView, pGroupId);
      default -> throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "vendor");
    }
  }

  // This will validate stream private link objects such that there cannot be more than one per
  // region per group. This is only applicable to AWS private link implementations so far
  public void validateAWSRegionalStreamPrivateLink(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final ObjectId pGroupId)
      throws SvcException {
    String vendor = pPrivateLinkView.getVendor();

    if (vendor == null || vendor.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "vendor. Vendor is missing.");
    }

    Optional<StreamsPrivateLinkVendorType> vendorType =
        StreamsPrivateLinkVendorType.findByValue(vendor.toUpperCase());

    if (!StreamsPrivateLinkVendorType.regionalPrivateLinkVendors.contains(
        vendorType.orElseThrow())) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          "vendor. Vendor must be one of "
              + StreamsPrivateLinkVendorType.regionalPrivateLinkVendors);
    }

    String provider = pPrivateLinkView.getProvider();

    if (provider == null
        || StreamsPrivateLinkProviderType.findByValue(provider.toUpperCase()).isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "provider");
    }

    Optional<StreamsPrivateLinkProviderType> providerType =
        StreamsPrivateLinkProviderType.findByValue(provider.toUpperCase());

    if (providerType.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "provider");
    }

    if (!providerType
        .get()
        .getProvider()
        .equalsIgnoreCase(StreamsPrivateLinkProviderType.AWS.getProvider())) {
      throw new SvcException(
          INVALID_ARGUMENT,
          String.format(
              "provider. %s Private Link is supported for 'AWS' provider only", vendorType.get()));
    }

    Optional<AWSRegionName> region = AWSRegionName.findByNameOrValue(pPrivateLinkView.getRegion());
    if (region.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "region. Region must be one of %s", AWSRegionName.getRegionNamesAsString()));
    }

    String serviceEndpointId = pPrivateLinkView.getServiceEndpointId();
    if (serviceEndpointId == null || serviceEndpointId.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "serviceEndpointId. serviceEndpointId is missing.");
    }

    String expectedServiceEndpointId = getRegionalServiceEndpointId(vendorType.get(), region.get());
    if (!serviceEndpointId.equals(expectedServiceEndpointId)) {
      throw new SvcException(
          INVALID_ARGUMENT,
          String.format(
              "serviceEndpointId '%s'. ServiceEndpointId should be '%s'",
              serviceEndpointId, expectedServiceEndpointId));
    }

    if (_privateLinkDao.alreadyExistsVendorAndRegion(
        pGroupId, vendorType.get().toString(), region.get())) {
      throw new SvcException(
          NDSErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS_IN_REGION,
          pPrivateLinkView.getRegion(),
          pPrivateLinkView.getVendor());
    }
  }

  private void validateMSKPrivateLink(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final ObjectId pGroupId)
      throws SvcException {
    String arn = pPrivateLinkView.getAmazonResourceName();
    if (!arn.startsWith("arn:aws:kafka")) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "arn");
    }

    if (_privateLinkDao.alreadyExistsArn(pGroupId, pPrivateLinkView.getAmazonResourceName())) {
      throw new SvcException(
          NDSErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS,
          pPrivateLinkView.getAmazonResourceName());
    }
  }

  private void validateAWSConfluentPrivateLink(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final ObjectId pGroupId)
      throws SvcException {
    Optional<AWSRegionName> region = AWSRegionName.findByNameOrValue(pPrivateLinkView.getRegion());
    if (region.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "region. Region must be one of %s", AWSRegionName.getRegionNamesAsString()));
    }

    // Pattern that we're matching: "com.amazonaws.vpce.us-east-1.vpce-svc-03bc1ff023623a033"
    String regex = "^com\\.amazonaws\\.vpce\\.[a-z0-9-]+\\.vpce-svc-[a-z0-9]+$";
    String serviceEndpointId = pPrivateLinkView.getServiceEndpointId();

    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(serviceEndpointId);

    if (!matcher.matches()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "serviceEndpointId");
    }

    String dnsDomain = pPrivateLinkView.getDnsDomain();

    List<String> dnsSubDomains = getDnsSubdomains(pPrivateLinkView, dnsDomain);

    for (String subDomain : dnsSubDomains) {
      if (!subDomain.contains(dnsDomain)) {
        throw new SvcException(
            NDSErrorCode.INVALID_ARGUMENT, "dnsSubDomain: should contain dnsDomain");
      }
    }

    Optional<StreamsPrivateLinkProviderType> providerType =
        StreamsPrivateLinkProviderType.findByValue(pPrivateLinkView.getProvider().toUpperCase());
    if (providerType.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "provider");
    }

    if (_privateLinkDao.alreadyExistsServiceEndpointIdAndRegion(
        pGroupId, pPrivateLinkView.getServiceEndpointId(), region.get())) {
      throw new SvcException(
          NDSErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS, pPrivateLinkView.getServiceEndpointId());
    }
  }

  private static List<String> getDnsSubdomains(
      ApiStreamsPrivateLinkView pPrivateLinkView, String dnsDomain) throws SvcException {
    if (!dnsDomain.contains(StreamsUtil.CONFLUENT_HOSTNAME_BASE)) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "dnsDomain");
    }

    boolean isDedicated = StreamsUtil.isConfluentDedicated(dnsDomain);

    List<String> dnsSubDomains =
        pPrivateLinkView.getDnsSubDomain() == null
            ? new ArrayList<>()
            : pPrivateLinkView.getDnsSubDomain();

    if (isDedicated && dnsSubDomains.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "dnsSubDomain");
    }

    if (dnsDomain.contains(".private") && !dnsSubDomains.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          "dnsSubDomain: serverless domains cannot support sub domains");
    }
    return dnsSubDomains;
  }

  private static AWSRegionName getAWSRegionNameFromARN(String arn) throws SvcException {
    String[] arnParts = arn.split(":");

    if (arnParts.length > 3) {
      // The region is index 3 of the ARN:
      // arn:aws:kafka:us-east-1:XXX
      String region = arnParts[3];
      Optional<AWSRegionName> awsRegionName = AWSRegionName.findByNameOrValue(region);
      if (awsRegionName.isPresent()) {
        return awsRegionName.get();
      }
    }

    throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "arn");
  }

  public Map<String, List<StreamsPrivateLink.DNSRecord>> getPrivateLinkDnsRecordsForGroup(
      ObjectId pGroupId) {
    Map<String, List<StreamsPrivateLink.DNSRecord>> dnsRecords = new HashMap<>();
    List<StreamsPrivateLink> privateLinks = _privateLinkDao.findByGroupId(pGroupId);
    for (StreamsPrivateLink spl : privateLinks) {
      var domain = spl.getDnsDomain();
      var records = spl.getDnsRecords();
      // MSK will be empty because AWS MSK uses MSK managed connections.
      // Behind the scenes AWS will add records to resolve connections to Kafka brokers at the VPC
      // level.
      if (domain != null && records != null) {
        dnsRecords.put(domain, records);
      }
    }

    return dnsRecords;
  }

  public String createTransitGatewayVpcAttachment(
      final ObjectId pGroupId,
      final ApiStreamsTransitGatewayAttachmentRequestView pTransitGatewayAttachmentsView)
      throws SvcException {
    try {
      CloudProvider provider =
          CloudProvider.findByNameIgnoreCase(pTransitGatewayAttachmentsView.getCloudProvider());
      if (provider != CloudProvider.AWS) {
        throw new IllegalArgumentException(
            "cloud provider: " + pTransitGatewayAttachmentsView.getCloudProvider());
      }

      Optional<AWSRegionName> regionName =
          AWSRegionName.findByNameOrValue(pTransitGatewayAttachmentsView.getRegionName());
      if (regionName.isEmpty()) {
        throw new IllegalArgumentException(
            "region name: " + pTransitGatewayAttachmentsView.getRegionName());
      }

      Optional<StreamsTransitGateway> existingResourceShares =
          _transitGatewayResourceSharesDao.findByGroupId(pGroupId);
      if (existingResourceShares.isEmpty()) {
        throw new IllegalArgumentException("group " + pGroupId + " has no resource shares");
      }

      final String tgwId = pTransitGatewayAttachmentsView.getTgwId();

      if (existingResourceShares.get().getResourceShares().stream()
          .filter(rs -> Objects.equals(rs.getTgwId(), tgwId))
          .findFirst()
          .isEmpty()) {
        throw new IllegalArgumentException(
            "group "
                + pGroupId
                + " did not accept a resource share invitation with the transit gateway id "
                + tgwId);
      }

      ObjectId containerId = _containerSvc.ensureCloudContainer(pGroupId, regionName.get());
      CloudProviderContainer container =
          _containerSvc.getContainerById(pGroupId, containerId).orElseThrow();

      AWSCloudProviderContainer awsContainer = (AWSCloudProviderContainer) container;

      CreateTransitGatewayVpcAttachmentResult createResult =
          _awsApiSvc.createTransitGatewayVpcAttachmentRequest(
              awsContainer.getAWSAccountId(),
              regionName.get(),
              pTransitGatewayAttachmentsView.getVpcId(),
              pTransitGatewayAttachmentsView.getTgwId(),
              Arrays.stream(awsContainer.getSubnets()).map(AWSSubnet::getSubnetId).toList(),
              LOG);

      final String attachmentId =
          createResult.getTransitGatewayVpcAttachment().getTransitGatewayAttachmentId();

      final StreamsTransitGatewayAttachment attachment =
          new StreamsTransitGatewayAttachment(new ObjectId(), pGroupId, tgwId, attachmentId);
      _transitGatewayAttachmentsDao.create(attachment);

      return attachmentId;
    } catch (IllegalArgumentException e) {
      LOG.warn("Invalid arguments creating transit gateway attachment", e);
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
    } catch (SvcException e) {
      LOG.error(
          "Service error creating transit gateway attachment for group {} in cloud provider {} in"
              + " region {}",
          pGroupId.toHexString(),
          pTransitGatewayAttachmentsView.getCloudProvider(),
          pTransitGatewayAttachmentsView.getRegionName(),
          e);
      throw e;
    } catch (AWSApiException e) {
      LOG.error(
          "AWS error creating transit gateway attachment for group {} with transit gateway id {}"
              + " and vpc id {}",
          pGroupId.toHexString(),
          pTransitGatewayAttachmentsView.getTgwId(),
          pTransitGatewayAttachmentsView.getVpcId(),
          e);
      throw new SvcException(
          e.getErrorCode(), "Error creating transit gateway attachment in AWS: " + e.getMessage());
    }
  }

  private Pair<ObjectId, AWSRegionName> getAwsApiInformation(
      ObjectId pGroupId, String pCloudProvider, String pRegionName) throws SvcException {

    try {
      CloudProvider provider = CloudProvider.findByName(pCloudProvider);
      if (provider != CloudProvider.AWS) {
        // We don't have implementations for non-AWS implementations of Transit Gateway
        throw new IllegalArgumentException("cloud provider is not supported");
      }

      Optional<AWSRegionName> regionName = AWSRegionName.findByNameOrValue(pRegionName);
      if (regionName.isEmpty()) {
        throw new IllegalArgumentException("region name is invalid");
      }

      ObjectId containerId = _containerSvc.ensureCloudContainer(pGroupId, regionName.get());
      CloudProviderContainer container =
          _containerSvc.getContainerById(pGroupId, containerId).orElseThrow();

      AWSCloudProviderContainer awsContainer = (AWSCloudProviderContainer) container;

      return Pair.of(awsContainer.getAWSAccountId(), regionName.get());

    } catch (IllegalArgumentException e) {
      LOG.warn("Invalid arguments fetching AWS cloud provider container", e);
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
    } catch (SvcException e) {
      LOG.error(
          "Could not find {} container for group {} and region {}",
          pCloudProvider,
          pGroupId.toHexString(),
          pRegionName,
          e);
      throw e;
    }
  }

  protected ResourceShareInvitation getResourceShareInvitation(
      ObjectId pGroupId,
      ObjectId pAwsAccountId,
      AWSRegionName pRegionName,
      String pTgwId,
      String pTgwResourceShareArn)
      throws SvcException {
    try {
      GetResourceShareInvitationsResult getResult =
          _awsApiSvc.getResourceShareInvitations(
              pAwsAccountId, pRegionName, pTgwResourceShareArn, LOG);

      List<ResourceShareInvitation> invitations = getResult.getResourceShareInvitations();

      if (invitations.isEmpty()) {
        throw new IllegalArgumentException(
            "No resource share invitations found for ARN: " + pTgwResourceShareArn);
      }

      LOG.info(
          "Found {} resource share invitation(s) for ARN {}: {}",
          invitations.size(),
          pTgwResourceShareArn,
          invitations.stream()
              .map(ResourceShareInvitation::getResourceShareInvitationArn)
              .collect(Collectors.joining(", ")));

      return invitations.stream()
          .max(Comparator.comparing(ResourceShareInvitation::getInvitationTimestamp))
          .orElseThrow();

    } catch (IllegalArgumentException e) {
      LOG.error(
          "Could not find any resource share invitations for group {} with transit gateway id {}",
          pGroupId.toHexString(),
          pTgwId);
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, e.getMessage());
    } catch (AWSApiException e) {
      LOG.error(
          "Could not get resource share invitation for group {} with transit gateway id {}: {}",
          pGroupId.toHexString(),
          pTgwId,
          e.getMessage());
      throw new SvcException(
          e.getErrorCode(), "Error getting resource share in AWS: " + e.getMessage());
    }
  }

  public void acceptTransitGatewayResourceShare(
      ObjectId pGroupId, ApiStreamsTransitGatewayResourceShareView pTransitGatewayResourceShareView)
      throws SvcException {

    try {
      Pair<ObjectId, AWSRegionName> awsApiInfo =
          getAwsApiInformation(
              pGroupId,
              pTransitGatewayResourceShareView.getCloudProvider(),
              pTransitGatewayResourceShareView.getRegionName());

      ObjectId awsAccountId = awsApiInfo.getLeft();
      AWSRegionName regionName = awsApiInfo.getRight();

      ResourceShareInvitation invitation =
          getResourceShareInvitation(
              pGroupId,
              awsAccountId,
              regionName,
              pTransitGatewayResourceShareView.getTgwId(),
              pTransitGatewayResourceShareView.getTgwResourceShareArn());

      _awsApiSvc.acceptResourceShareInvitation(
          awsAccountId, regionName, invitation.getResourceShareInvitationArn(), LOG);

      Optional<StreamsTransitGateway> groupResourceShares =
          _transitGatewayResourceSharesDao.findByGroupId(pGroupId);

      if (groupResourceShares.isEmpty()) {
        List<StreamsTransitGateway.ResourceShare> resourceShares = new ArrayList<>();
        resourceShares.add(
            new StreamsTransitGateway.ResourceShare(
                pTransitGatewayResourceShareView.getTgwId(),
                pTransitGatewayResourceShareView.getTgwResourceShareArn(),
                pTransitGatewayResourceShareView.getTgwResourceShareInvitationArn()));
        _transitGatewayResourceSharesDao.create(
            new StreamsTransitGateway(new ObjectId(), pGroupId, resourceShares));
      } else {
        _transitGatewayResourceSharesDao.addResourceShare(
            groupResourceShares.get().getId(),
            new StreamsTransitGateway.ResourceShare(
                pTransitGatewayResourceShareView.getTgwId(),
                pTransitGatewayResourceShareView.getTgwResourceShareArn(),
                pTransitGatewayResourceShareView.getTgwResourceShareInvitationArn()));
      }

    } catch (AWSApiException e) {
      LOG.error(
          "Could not accept resource share invitation for group {} with transit gateway id {}: {}",
          pGroupId.toHexString(),
          pTransitGatewayResourceShareView.getTgwId(),
          e.getMessage());
      throw new SvcException(
          e.getErrorCode(), "Error accepting resource share in AWS: " + e.getMessage());
    }
  }

  public void rejectTransitGatewayResourceShare(
      ObjectId pGroupId, ApiStreamsTransitGatewayResourceShareView pTransitGatewayResourceShareView)
      throws SvcException {
    try {
      Pair<ObjectId, AWSRegionName> awsApiInfo =
          getAwsApiInformation(
              pGroupId,
              pTransitGatewayResourceShareView.getCloudProvider(),
              pTransitGatewayResourceShareView.getRegionName());

      ObjectId awsAccountId = awsApiInfo.getLeft();
      AWSRegionName regionName = awsApiInfo.getRight();

      ResourceShareInvitation invitation =
          getResourceShareInvitation(
              pGroupId,
              awsAccountId,
              regionName,
              pTransitGatewayResourceShareView.getTgwId(),
              pTransitGatewayResourceShareView.getTgwResourceShareArn());

      _awsApiSvc.rejectResourceShareInvitation(
          awsAccountId, regionName, invitation.getResourceShareInvitationArn(), LOG);

      Optional<StreamsTransitGateway> groupResourceSharesOpt =
          _transitGatewayResourceSharesDao.findByGroupId(pGroupId);

      if (groupResourceSharesOpt.isEmpty()) {
        LOG.warn(
            "Rejected resource share invitation for group {} with no existing resource shares",
            pGroupId.toHexString());
        return;
      }

      StreamsTransitGateway groupResourceShares = groupResourceSharesOpt.get();
      if (groupResourceShares.getResourceShares().size() == 1
          && Objects.equals(
              groupResourceShares.getResourceShares().get(0).getTgwResourceShareArn(),
              pTransitGatewayResourceShareView.getTgwResourceShareArn())) {
        // If there's only one resource share left for a group, and we're going to remove it, then
        // we should fully remove this entry from the database
        _transitGatewayResourceSharesDao.delete(groupResourceShares.getId());
      } else {
        if (!_transitGatewayResourceSharesDao.removeResourceShareByTgwInfo(
            pGroupId,
            pTransitGatewayResourceShareView.getTgwId(),
            pTransitGatewayResourceShareView.getTgwResourceShareArn())) {
          LOG.warn(
              "Did not find resource share invitation to remove for group {} with transit gateway"
                  + " id {} and arn {}",
              pGroupId,
              pTransitGatewayResourceShareView.getTgwId(),
              pTransitGatewayResourceShareView.getTgwResourceShareArn());
        }
      }

    } catch (AWSApiException e) {
      LOG.error(
          "Could not reject resource share invitation for group {} with transit gateway id {}: {}",
          pGroupId.toHexString(),
          pTransitGatewayResourceShareView.getTgwId(),
          e.getMessage());
      throw new SvcException(
          e.getErrorCode(), "Error rejecting resource share in AWS: " + e.getMessage());
    }
  }

  public boolean deleteTransitGatewayResourceShare(ObjectId pGroupId, String pResourceShareArn) {
    Optional<StreamsTransitGateway> groupResourceSharesOpt =
        _transitGatewayResourceSharesDao.findByGroupId(pGroupId);

    if (groupResourceSharesOpt.isEmpty()) {
      return false;
    }

    StreamsTransitGateway groupResourceShares = groupResourceSharesOpt.get();

    // Find the specific resource share to delete
    Optional<StreamsTransitGateway.ResourceShare> resourceShareToDelete =
        groupResourceShares.getResourceShares().stream()
            .filter(rs -> rs.getTgwResourceShareArn().equals(pResourceShareArn))
            .findFirst();

    if (resourceShareToDelete.isEmpty()) {
      return false;
    }

    ApiStreamsResourceShareView view =
        new ApiStreamsResourceShareView(
            resourceShareToDelete.get().getTgwId(),
            resourceShareToDelete.get().getTgwResourceShareArn(),
            resourceShareToDelete.get().getTgwResourceShareInvitationArn());

    return _transitGatewayResourceSharesDao.removeResourceShareByTgwInfo(
        pGroupId, view.getTgwId(), view.getTgwResourceShareArn());
  }

  public ApiStreamsListResourceShareView getResourceShares(ObjectId pGroupId) {
    return new ApiStreamsListResourceShareView(
        _transitGatewayResourceSharesDao.findByGroupId(pGroupId).stream()
            .flatMap(
                stg ->
                    stg.getResourceShares().stream()
                        .map(
                            rs ->
                                new ApiStreamsResourceShareView(
                                    rs.getTgwId(),
                                    rs.getTgwResourceShareArn(),
                                    rs.getTgwResourceShareInvitationArn())))
            .toList());
  }

  public Optional<ApiStreamsResourceShareView> getResourceShare(
      ObjectId pGroupId, String pResourceShareArn) {
    return _transitGatewayResourceSharesDao.findByGroupId(pGroupId).stream()
        .flatMap(stg -> stg.getResourceShares().stream())
        .filter(rs -> rs.getTgwResourceShareArn().equals(pResourceShareArn))
        .findFirst()
        .map(
            rs ->
                new ApiStreamsResourceShareView(
                    rs.getTgwId(),
                    rs.getTgwResourceShareArn(),
                    rs.getTgwResourceShareInvitationArn()));
  }

  public List<StreamsTransitGatewayAttachment> getTransitGatewayAttachments(
      final ObjectId pGroupId) {
    return _transitGatewayAttachmentsDao.findByGroupId(pGroupId);
  }

  public Optional<StreamsTransitGatewayAttachment> getTransitGatewayAttachment(
      final ObjectId groupId, final String attachmentId) {
    return _transitGatewayAttachmentsDao.findByGroupIdAndAttachmentId(groupId, attachmentId);
  }

  public void deleteTransitGatewayAttachment(ObjectId pGroupId, String pTgwAttachmentId) {
    _transitGatewayAttachmentsDao.deleteByGroupIdAndAttachmentId(pGroupId, pTgwAttachmentId);
  }

  final Set<String> allowedFunctions = Set.of("sum", "avg", "max", "increase", "rate");

  // Id generated by Intel for the streams usecase, found in streams-usecase.yaml
  private static final UUID streamsMaaSUsecaseID =
      UUID.fromString("ff6b9a5f-39e0-5d18-9248-b29c9b4a97a9");

  public String buildMetricsQuery(
      final String groupId,
      final String rawMetricName,
      final List<String> processorIds,
      final String function,
      final String lookback)
      throws SvcException {
    StreamsMetric streamsMetric;
    try {
      streamsMetric = StreamsMetric.from(rawMetricName);
    } catch (IllegalArgumentException e) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "metricName");
    }

    String metricName = streamsMetric.getMetricName();

    // No function (null) is also allowed.
    if (function != null && !allowedFunctions.contains(function)) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format("function must be one of [%s]", String.join(", ", allowedFunctions)));
    }

    for (String processorId : processorIds) {
      if (processorId == null || !processorId.matches("^[a-z0-9]{24}$")) {
        throw new SvcException(
            NDSErrorCode.INVALID_ARGUMENT, String.format("processorId %s is invalid", processorId));
      }
    }

    if (groupId == null || !groupId.matches("^[a-z0-9]{24}$")) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, String.format("groupId %s is invalid", groupId));
    }

    String query = metricName;
    if (processorIds.isEmpty()) {
      query += String.format("{group_id=\"%s\"}", groupId);
    } else {
      String processorIdSelector = StringUtils.join(processorIds, "|");
      query +=
          String.format("{group_id=\"%s\", processor_id=~\"%s\"}", groupId, processorIdSelector);
    }

    if (function != null && !function.isBlank()) {
      String rangeSelector = "";
      if (lookback != null && !lookback.isBlank()) {
        rangeSelector = String.format("[%s]", lookback);
      }
      query = String.format("%s(%s%s)", function, query, rangeSelector);
    }

    return query;
  }

  public PromQLResponseView.Data getMetricsInstantQuery(
      final String groupId,
      final String metricName,
      final List<String> processorIds,
      final String function,
      final String lookback,
      final String time)
      throws SvcException {
    String query = buildMetricsQuery(groupId, metricName, processorIds, function, lookback);
    try {
      final InstantQueryRequest instantQueryRequest =
          new InstantQueryRequest.Builder(query, streamsMaaSUsecaseID).withTime(time).build();
      final PromQLResponseView<Data> responseView =
          _customerMetricsQuerySvc.getMetricsViaInstantQuery(instantQueryRequest);
      return responseView.getData();
    } catch (AuthnOAuthClient.UnableToRetrieveCloudJwtException | IOException e) {
      throw new SvcException(NDSErrorCode.STREAM_PROCESSOR_UNEXPECTED_ERROR, e);
    }
  }

  public PromQLResponseView.Data getMetricsRangeQuery(
      final String groupId,
      final String metricName,
      final List<String> processorIds,
      final String function,
      final String lookback,
      final String start,
      final String end,
      final String step)
      throws SvcException {
    String query = buildMetricsQuery(groupId, metricName, processorIds, function, lookback);
    try {
      final RangeQueryRequest rangeQueryRequest =
          new RangeQueryRequest.Builder(query, streamsMaaSUsecaseID, start)
              .withEnd(end)
              .withStep(step)
              .build();
      final PromQLResponseView<Data> responseView =
          _customerMetricsQuerySvc.getMetricsViaRangeQuery(rangeQueryRequest);
      return responseView.getData();
    } catch (AuthnOAuthClient.UnableToRetrieveCloudJwtException | IOException e) {
      throw new SvcException(NDSErrorCode.STREAM_PROCESSOR_UNEXPECTED_ERROR, e);
    }
  }

  private void validateGCPPrivateLinkConnection(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final ObjectId pGroupId)
      throws SvcException {
    Optional<StreamsPrivateLinkVendorType> vendor =
        StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor());
    if (vendor.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format("vendor. vendor cannot be '%s'", pPrivateLinkView.getVendor()));
    }

    switch (vendor.get()) {
      case CONFLUENT -> validateGCPConfluentPrivateLink(pPrivateLinkView, pGroupId);
      default -> throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "vendor");
    }
  }

  public void validateGCPConfluentPrivateLink(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final ObjectId pGroupId)
      throws SvcException {
    // Validate provider
    Optional<StreamsPrivateLinkProviderType> providerType =
        StreamsPrivateLinkProviderType.findByValue(pPrivateLinkView.getProvider());
    if (providerType.isEmpty() || providerType.get() != StreamsPrivateLinkProviderType.GCP) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "provider");
    }

    // Validate vendor
    Optional<StreamsPrivateLinkVendorType> vendorType =
        StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor());
    if (vendorType.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format("vendor. vendor cannot be '%s'", pPrivateLinkView.getVendor()));
    }

    if (vendorType.get() != StreamsPrivateLinkVendorType.CONFLUENT) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "vendor: " + vendorType.get().getVendor());
    }

    // Validate region
    Optional<GCPRegionName> region = GCPRegionName.findByNameOrValue(pPrivateLinkView.getRegion());
    if (region.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "region. Region must be one of %s", GCPRegionName.getRegionNamesAsString()));
    }

    // Validate dnsDomain ends with "gcp.confluent.cloud"
    String dnsDomain = pPrivateLinkView.getDnsDomain();
    if (dnsDomain == null || !dnsDomain.endsWith(StreamsUtil.GCP_CONFLUENT_HOSTNAME_BASE)) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "dnsDomain");
    }

    // Validate gcpServiceAttachmentUris contains exactly three elements.
    List<String> gcpServiceAttachmentUris = pPrivateLinkView.getGcpServiceAttachmentUris();
    if (gcpServiceAttachmentUris == null || gcpServiceAttachmentUris.size() != 3) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          "gcpServiceAttachmentUris must contain exactly 3 elements");
    }

    // Validate each service attachment URI format using regex
    Pattern compiledPattern = Pattern.compile(GCP_SERVICE_ATTACHMENT_URI_PATTERN);
    for (String serviceAttachmentUri : gcpServiceAttachmentUris) {
      Matcher matcher = compiledPattern.matcher(serviceAttachmentUri);
      if (!matcher.matches()) {
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "gcpServiceAttachmentUris");
      }
    }

    // Check for existing private link with same service attachment URIs and dnsDomain to prevent
    // duplicates
    if (_privateLinkDao.alreadyExistsGcpServiceAttachmentUris(
        pGroupId, gcpServiceAttachmentUris, dnsDomain, region.get())) {
      throw new SvcException(
          NDSErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS,
          "GCP service attachment URIs and dnsDomain already in use");
    }
  }

  private void validateAzurePrivateLinkConnection(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final ObjectId pGroupId)
      throws SvcException {
    Optional<StreamsPrivateLinkVendorType> vendor =
        StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor());
    if (vendor.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format("vendor. vendor cannot be '%s'", pPrivateLinkView.getVendor()));
    }

    switch (vendor.get()) {
      case GENERIC, EVENTHUB ->
          validateAzureEventHubPrivateLinkConnection(pPrivateLinkView, pGroupId);
      case CONFLUENT -> validateAzureConfluentPrivateLink(pPrivateLinkView, pGroupId);
      default -> throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "vendor");
    }
  }

  private void validateAzureEventHubPrivateLinkConnection(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final ObjectId pGroupId)
      throws SvcException {
    /*
    Azure connection validation criteria:
      - This currently only supports EventHub specific connectivity (not Confluent)
      - Must have a dnsDomain specified
      - DNSDomain should contain the Privatelink FQDN (eg. not be empty at least)
      - DNSDomain should (always?) end in servicebus.windows.net
      - serviceEndpointId should look like a valid Azure endpoint, eg.
          /subscriptions/fd01adff-b37e-4693-8497-83ecf183a145/resourceGroups/erikbeebe-rg/providers/Microsoft.EventHub/namespaces/erik-kafka-namespace
      - For EventHub, Vendor should be unset, or set to GENERIC
      - Provider should exist and be Azure
      - Region should exist and be a valid Azure region
      - DNSSubDomains should be empty
      - There should not already be an existing PrivateLink endpoint for this groupId
        pointing to the same service endpoint.
     */
    Optional<AzureRegionName> region =
        AzureRegionName.findByNameOrValue(pPrivateLinkView.getRegion());
    if (region.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "region. Region must be one of %s", AzureRegionName.getRegionNamesAsString()));
    }

    // Ensure the service endpoint appears to be a valid endpoint which matches EventHub.
    String serviceEndpointPattern =
        "^/subscriptions/.*/resourceGroups/.*/providers/Microsoft.EventHub/namespaces/.*$";
    String serviceEndpointId = pPrivateLinkView.getServiceEndpointId();
    Pattern compiledPattern = Pattern.compile(serviceEndpointPattern);
    Matcher matcher = compiledPattern.matcher(serviceEndpointId);

    if (!matcher.matches()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "serviceEndpointId");
    }

    // Ensure the dnsDomain looks reasonable.
    String dnsDomain = pPrivateLinkView.getDnsDomain();
    if (!dnsDomain.endsWith(StreamsUtil.AZURE_HOSTNAME_BASE)) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "dnsDomain");
    }

    // DNS Subdomains should be empty.
    List<String> dnsSubDomains = pPrivateLinkView.getDnsSubDomain();
    if (dnsSubDomains != null && !dnsSubDomains.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "dnsSubDomain");
    }

    Optional<StreamsPrivateLinkProviderType> providerType =
        StreamsPrivateLinkProviderType.findByValue(pPrivateLinkView.getProvider());
    if (providerType.isEmpty() || providerType.get() != StreamsPrivateLinkProviderType.AZURE) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "provider");
    }

    Optional<StreamsPrivateLinkVendorType> vendorType =
        StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor());
    if (vendorType.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format("vendor. vendor cannot be '%s'", pPrivateLinkView.getVendor()));
    }

    if (!((vendorType.get() == StreamsPrivateLinkVendorType.GENERIC)
        || (vendorType.get() == StreamsPrivateLinkVendorType.EVENTHUB))) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "vendor: " + vendorType.get().getVendor());
    }

    if (_privateLinkDao.alreadyExistsServiceEndpointIdAndRegion(
        pGroupId, pPrivateLinkView.getServiceEndpointId(), region.get())) {
      throw new SvcException(
          NDSErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS, pPrivateLinkView.getServiceEndpointId());
    }
  }

  private void validateAzureConfluentPrivateLink(
      final ApiStreamsPrivateLinkView pPrivateLinkView, final ObjectId pGroupId)
      throws SvcException {
    Optional<AzureRegionName> region =
        AzureRegionName.findByNameOrValue(pPrivateLinkView.getRegion());
    if (region.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format(
              "region. Region must be one of %s", AzureRegionName.getRegionNamesAsString()));
    }

    // Ensure at least one azure resource ID was provided.
    if (pPrivateLinkView.getAzureResourceIds() == null
        || pPrivateLinkView.getAzureResourceIds().isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "azureResourceIds is required");
    }

    // Ensure the resource IDs appear to be a valid endpoint.
    Pattern compiledPattern = Pattern.compile(AZURE_CONFLUENT_PRIVATE_LINK_RESOURCE_ID_PATTERN);
    for (String resourceId : pPrivateLinkView.getAzureResourceIds()) {
      Matcher matcher = compiledPattern.matcher(resourceId);
      if (!matcher.matches()) {
        throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "azureResourceIds");
      }
    }

    // Ensure the dnsDomain looks reasonable.
    String dnsDomain = pPrivateLinkView.getDnsDomain();
    if (dnsDomain == null || !dnsDomain.endsWith(StreamsUtil.AZURE_CONFLUENT_HOSTNAME_BASE)) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "dnsDomain");
    }

    // DNS Subdomains should be empty.
    List<String> dnsSubDomains = pPrivateLinkView.getDnsSubDomain();
    if (dnsSubDomains != null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "dnsSubDomain");
    }

    Optional<StreamsPrivateLinkProviderType> providerType =
        StreamsPrivateLinkProviderType.findByValue(pPrivateLinkView.getProvider());
    if (providerType.isEmpty() || providerType.get() != StreamsPrivateLinkProviderType.AZURE) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "provider");
    }

    Optional<StreamsPrivateLinkVendorType> vendorType =
        StreamsPrivateLinkVendorType.findByValue(pPrivateLinkView.getVendor());
    if (vendorType.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format("vendor. vendor cannot be '%s'", pPrivateLinkView.getVendor()));
    }

    if (vendorType.get() != StreamsPrivateLinkVendorType.CONFLUENT) {
      throw new SvcException(
          NDSErrorCode.INVALID_ARGUMENT, "vendor: " + vendorType.get().getVendor());
    }

    // Service endpoint ID should be empty.
    if (pPrivateLinkView.getServiceEndpointId() != null) {
      throw new SvcException(NDSErrorCode.INVALID_ARGUMENT, "serviceEndpointId");
    }

    if (_privateLinkDao.alreadyExistsAzureResourceIds(
        pGroupId, pPrivateLinkView.getAzureResourceIds(), region.get())) {
      throw new SvcException(
          NDSErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS, pPrivateLinkView.getAzureResourceIds());
    }
  }

  private StreamsTenantView toNDSStreamsTenantView(
      final StreamsTenant streamsTenant, final NDSDataLakeTenant dataLakeTenant) {
    return toNDSStreamsTenantView(streamsTenant, dataLakeTenant, null);
  }

  private StreamsTenantView toNDSStreamsTenantView(
      final StreamsTenant streamsTenant,
      final NDSDataLakeTenant dataLakeTenant,
      final List<StreamsConnectionView> storage) {

    return new StreamsTenantView(
        dataLakeTenant.getTenantId(),
        streamsTenant.getGroupId(),
        streamsTenant.getName(),
        streamsTenant.getIsDefault(),
        new StreamConfigView(streamsTenant.getStreamConfig().getTier()),
        dataLakeTenant.getHostnames(),
        new NDSDataLakeDataProcessRegionView(dataLakeTenant.getDataProcessRegion()),
        storage);
  }

  public void incrementInstanceErrorCounter(final Throwable ex, Methods m) {
    NDSPromMetricsSvc.incrementCounter(
        NDS_STREAMS_INSTANCE_EXCEPTION_COUNTER, ex.getClass().getSimpleName(), m.name());
  }

  public void incrementConnectionErrorCounter(final Throwable ex, Methods m) {
    NDSPromMetricsSvc.incrementCounter(
        NDS_STREAMS_CONNECTION_EXCEPTION_COUNTER, ex.getClass().getSimpleName(), m.name());
  }

  public enum Methods {
    CREATE,
    UPDATE,
    DELETE,
    VIEW
  }
}
