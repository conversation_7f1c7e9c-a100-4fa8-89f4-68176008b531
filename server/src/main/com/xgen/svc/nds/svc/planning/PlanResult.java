package com.xgen.svc.nds.svc.planning;

import static com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.ATLAS_AUTOHEAL_REDUCE_SHUTDOWN_TIME;
import static com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.ATLAS_AUTOHEAL_REDUCE_SHUTDOWN_TIME_HEAL_REPAIR;
import static com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.ATLAS_AUTOHEAL_REMOVE_ICMP_PING;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.cloud.nds.planning.tracing._public.model.NDSPlanningTraceSpanData.getSpanAttributesByClusterName;
import static com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessGCPServiceAccount.ServiceAccountProvisionStatus;
import static com.xgen.svc.nds.util.EncryptionAtRestUtil.isEncryptionAtRestEnabledAndInvalid;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.brs.core._public.svc.BackupSvc;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.brs._public.model.BackupConfig;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.misc.AuditDescription;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.util.BaseHostUtils;
import com.xgen.cloud.common.util._public.util.SetUtils;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupRestoreJobPlanUnit;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.PlanningType;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreJob;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.RestorePlanning;
import com.xgen.cloud.cps.restore._public.model.ShadowClusterJob;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob;
import com.xgen.cloud.cps.restore._public.model.TenantUpgradeToServerlessBackupRestoreJob;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.EncryptionProviderType;
import com.xgen.cloud.deployment._public.model.EncryptionProviders;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ReplicaSetMember;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.HostUtils;
import com.xgen.cloud.monitoring.topology._public.model.ReplicaSet;
import com.xgen.cloud.monitoring.topology._public.model.ReplicaSet.Member;
import com.xgen.cloud.monitoring.topology._public.model.ReplicaSet.Member.State;
import com.xgen.cloud.monitoring.topology._public.model.ping.Ping;
import com.xgen.cloud.monitoring.topology._public.svc.HostLastPingSvc;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSCheckResult;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSInstanceCapacitySpec;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureCheckResult;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureInstanceCapacitySpec;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.model.CloudProviderAvailability;
import com.xgen.cloud.nds.capacity._public.model.azure.AzureCapacityDenyListEntry;
import com.xgen.cloud.nds.cloudprovider._public.model.BaseMultiTargetConnectionRule;
import com.xgen.cloud.nds.cloudprovider._public.model.BaseMultiTargetConnectionRule.NdsProcessPortTyped;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.Action;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.LoadBalancerIngressRule;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionalDedicatedCloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.AWSS3PrivateEndpoint;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.BaseEndpointService.Status;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.CloudProviderPrivateEndpoint;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.DedicatedEndpointService;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.Endpoint;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.EndpointPrometheusStatus;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.PrivateLinkConnectionRule;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.TenantEndpoint;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.TenantEndpointServiceDeployment;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.ContainerProvider.ContainerSubnetsToUpdate;
import com.xgen.cloud.nds.common._public.model.BiConnectorReadPreference;
import com.xgen.cloud.nds.common._public.model.ConfigServerType;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.SubdomainLevel;
import com.xgen.cloud.nds.common._public.model.error.MissingReplicationSpecException;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipelineRun;
import com.xgen.cloud.nds.flex._public.model.FastFlexPreAllocatedRecord;
import com.xgen.cloud.nds.flex._public.model.FlexCloudProviderContainer;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration;
import com.xgen.cloud.nds.flex._public.model.MigrationStatus;
import com.xgen.cloud.nds.free._public.model.FastSharedPreAllocatedRecord;
import com.xgen.cloud.nds.free._public.model.FreeCloudProviderContainer;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.mtmcompaction._public.model.MTMCompaction;
import com.xgen.cloud.nds.planning.common._public.model.PlanSummary;
import com.xgen.cloud.nds.planning.common._public.model.PlanSummary.ResourceType;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction.ActionType;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction.MoveType;
import com.xgen.cloud.nds.planning.common._public.model.RestoreSummary;
import com.xgen.cloud.nds.planning.common._public.model.ServerlessPlanResult;
import com.xgen.cloud.nds.planning.common._public.util.NDSMoveTags;
import com.xgen.cloud.nds.planning.common._public.util.PlanResultUtil;
import com.xgen.cloud.nds.planning.common._public.util.PlanSummaryUtil;
import com.xgen.cloud.nds.planning.summary._public.model.NDSPlanningSummaryBuilder;
import com.xgen.cloud.nds.planning.summary._public.model.NDSProjectPlanningSummaryBuilder;
import com.xgen.cloud.nds.planning.summary._public.model.cluster.MachineActionsDecisions;
import com.xgen.cloud.nds.planning.summary._public.model.cluster.NDSClusterPlanningSummary.Builder;
import com.xgen.cloud.nds.planning.tracing._public.svc.NDSPlanningTracingSvc.NDSPlanningTraceWrapper;
import com.xgen.cloud.nds.privatelink._public.util.PrivateLinkUtil;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.BiConnector;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.SystemProjectType;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccess;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessGCPServiceAccount;
import com.xgen.cloud.nds.project._public.model.privatelink.ClusterContainerGroup;
import com.xgen.cloud.nds.project._public.model.privatenetwork.NDSPrivateNetworkEndpointIdEntry;
import com.xgen.cloud.nds.project._public.util.ClusterContainerGroupUtil;
import com.xgen.cloud.nds.project._public.util.ClusterDescriptionUtil;
import com.xgen.cloud.nds.security._public.model.NDSACMECert;
import com.xgen.cloud.nds.serverless._public.model.FastServerlessPreAllocatedRecord;
import com.xgen.cloud.nds.serverless._public.model.ServerlessCloudProviderContainer;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.pool.ServerlessMTMPool;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.EnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.GCPCloudNATGateway;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowClusterExposureJob;
import com.xgen.cloud.nds.simulateregionoutage._public.model.ClusterOutageSimulation;
import com.xgen.cloud.nds.tenant._public.model.FastTenantPreAllocatedRecord;
import com.xgen.cloud.nds.tenant._public.model.LinkedMTM;
import com.xgen.cloud.nds.tenant._public.model.TenantCloudProviderContainer;
import com.xgen.cloud.nds.tenant._public.model.TenantNDSDefaults;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantRestore;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeToDedicatedStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeStatus;
import com.xgen.module.common.planner.model.IPlanContextFactory;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.svc.nds.azure.svc.AzurePrivateLinkSvc;
import com.xgen.svc.nds.planner.BackupSnapshotUtils;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.movetypes.ProcessMongoDBConfigMove;
import com.xgen.svc.nds.sampleDatasetLoad.planner.EnqueueSampleDatasetLoadMove;
import com.xgen.svc.nds.security.planner.NDSACMEPlanContext;
import com.xgen.svc.nds.security.svc.NDSACMESvc;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import com.xgen.svc.nds.svc.NDSPrometheusEndpointSvc;
import com.xgen.svc.nds.svc.NDSPrometheusEndpointSvc.PrometheusEndpointForContainer;
import com.xgen.svc.nds.svc.cps.CpsSnapshotEngine;
import com.xgen.svc.nds.svc.planning.rules.ConfigBeforeEnqueueLoadSampleDataset;
import com.xgen.svc.nds.svc.planning.rules.CreateContainerBeforeCreateMachineForFreeCluster;
import com.xgen.svc.nds.svc.planning.rules.DeleteBackupsBeforeMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.DependencyRule;
import com.xgen.svc.nds.svc.planning.rules.DestroyEnvoyMachineBeforeLoadBalancerDelete;
import com.xgen.svc.nds.svc.planning.rules.DirectAttachRestoreBeforeCleanUp;
import com.xgen.svc.nds.svc.planning.rules.DisableServerlessAccessAroundServerlessRestore;
import com.xgen.svc.nds.svc.planning.rules.DisaggregatedStorageConfigBeforeNewMachines;
import com.xgen.svc.nds.svc.planning.rules.DoCopySnapshotMovesBeforePostCopySnapshot;
import com.xgen.svc.nds.svc.planning.rules.DoPreCopySnapshotMoveBeforeCopySnapshot;
import com.xgen.svc.nds.svc.planning.rules.DoSnapshotExportFromSystemClusterBeforeUploadSnapshotExportCompleteFileFromSystemCluster;
import com.xgen.svc.nds.svc.planning.rules.DoSnapshotExportMoveBeforeUploadSnapshotExportCompleteFile;
import com.xgen.svc.nds.svc.planning.rules.EndOutageBeforeEverythingElse;
import com.xgen.svc.nds.svc.planning.rules.EnsureConnectivityBeforeDeleteMachines;
import com.xgen.svc.nds.svc.planning.rules.EnsureConnectivityForScaleOutBeforeScaleUp;
import com.xgen.svc.nds.svc.planning.rules.EnvoyInstanceReplacementsUpgradeDowngradeMustBeRolling;
import com.xgen.svc.nds.svc.planning.rules.ExpandBackupConfigAfterMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.FastProvisionMachinesBeforeConfig;
import com.xgen.svc.nds.svc.planning.rules.FlagLastAwsSwapIpMove;
import com.xgen.svc.nds.svc.planning.rules.FreeCompactionAfterRevokeAccess;
import com.xgen.svc.nds.svc.planning.rules.MachinesHealthyBeforeMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.ModifyDiskBeforeMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.ModifyDiskBeforeUpdateClusterMongotuneConfig;
import com.xgen.svc.nds.svc.planning.rules.ModifyDiskBeforeUpdateMachine;
import com.xgen.svc.nds.svc.planning.rules.MongoDBConfigBeforeDeleteMachines;
import com.xgen.svc.nds.svc.planning.rules.MongoDBConfigBeforeEnsureClusterConnectionStrings;
import com.xgen.svc.nds.svc.planning.rules.MongoDBConfigBeforeEnsureConnectivity;
import com.xgen.svc.nds.svc.planning.rules.MongoDBConfigScaleOutBeforeScaleUp;
import com.xgen.svc.nds.svc.planning.rules.NewContainerBeforeContainerConfig;
import com.xgen.svc.nds.svc.planning.rules.NewMachinesBeforeEnsureConnectivity;
import com.xgen.svc.nds.svc.planning.rules.NewMachinesBeforeMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.NewMachinesBeforeUpdateMachines;
import com.xgen.svc.nds.svc.planning.rules.OtherMachinesHealthyBeforeSwapOS;
import com.xgen.svc.nds.svc.planning.rules.PerformSnapshotLast;
import com.xgen.svc.nds.svc.planning.rules.PlannedActionMovesAreChains;
import com.xgen.svc.nds.svc.planning.rules.PreprocessRestoreBeforeRestore;
import com.xgen.svc.nds.svc.planning.rules.ProvisionBiConnectorAfterMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.ProvisionMachineBeforeRestore;
import com.xgen.svc.nds.svc.planning.rules.ProvisionServerlessLoadBalancerBeforeEnvoy;
import com.xgen.svc.nds.svc.planning.rules.ProvisionServerlessNATGatewayBeforeEnvoy;
import com.xgen.svc.nds.svc.planning.rules.ResetPriorityBeforeMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.RestoreConfigAroundMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.SyncBackupConfigAfterMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.SyncBiConnectorAfterClusterResume;
import com.xgen.svc.nds.svc.planning.rules.SyncClusterPrivateEndpointBeforeEnsureConnectivity;
import com.xgen.svc.nds.svc.planning.rules.SyncClusterPrivateEndpointBeforeMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.TenantBackupRestoreBeforeMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.TenantUnpauseRestoreAfterMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.TransitionConfigServerBeforeMongoDBConfig;
import com.xgen.svc.nds.svc.planning.rules.TransitionConfigServerBeforeSyncClusterPrivateEndpoint;
import com.xgen.svc.nds.svc.planning.rules.UpdateContainerBeforeDeleteContainer;
import com.xgen.svc.nds.svc.planning.rules.UpdateMachinesBeforeUpdateClusterMongotuneConfig;
import com.xgen.svc.nds.svc.planning.rules.UpdatesAndRepairsMustBeRolling;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.util.NDSTenantPauseUtil;
import com.xgen.svc.nds.util.PitEncryptionUtil;
import com.xgen.svc.security.acme.model.ACMECert;
import io.opentelemetry.api.common.Attributes;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.BasicBSONObject;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.slf4j.Logger;

/*
In CLOUDP-114549 we moved part of rules in com.xgen.svc.nds.svc.planning.rules to
com.xgen.cloud.nds.planning.common._public.util.rules.
Since for nds we are maintaining util -> model dependency, that means when we modularize the remaining rules,
we want to move their usage here to util as well. And that means we need to separate out all
private, static, build related methods into PlanResultUtil under util.
 */
public class PlanResult {
  protected static final List<InstanceHardware.Action>
      INVALID_ENCRYPTION_AT_REST_PERMITTED_ACTIONS =
          List.of(
              InstanceHardware.Action.DELETE,
              InstanceHardware.Action.REQUESTED_REBOOT,
              InstanceHardware.Action.UPDATE_CONFIG,
              InstanceHardware.Action.ROTATE_AGENT_API_KEY,
              InstanceHardware.Action.OS_SWAP,
              InstanceHardware.Action.OS_POLICY_UPDATE_AND_REBOOT);

  private final List<Pair<PlanSummary, Plan>> _planSummaryPairs;
  private final List<Cluster> _unableToPlanClusters;

  public PlanResult() {
    _planSummaryPairs = new ArrayList<>();
    _unableToPlanClusters = new ArrayList<>();
  }

  public PlanResult(
      final List<Pair<PlanSummary, Plan>> pPlanSummaryPairs,
      final List<Cluster> pUnableToPlanClusters) {
    _planSummaryPairs = pPlanSummaryPairs;
    _unableToPlanClusters = pUnableToPlanClusters;
  }

  public List<Plan> getPlans() {
    return _planSummaryPairs.stream().map(p -> p.getRight()).collect(Collectors.toList());
  }

  public List<Pair<PlanSummary, Plan>> getPlanSummaryPairs() {
    return _planSummaryPairs;
  }

  public List<Cluster> getUnableToPlanClusters() {
    return _unableToPlanClusters;
  }

  public Set<String> getInvolvedClusters() {
    return _planSummaryPairs.stream()
        .filter(
            p -> {
              final PlanSummary summary = p.getLeft();
              return summary.getClusterName().isPresent()
                  && summary.getActionType() != PlannedAction.ActionType.REPAIR;
            })
        .map(p -> p.getLeft().getClusterName().get())
        .collect(Collectors.toSet());
  }

  public Set<String> getInvolvedRepairingClusters() {
    return _planSummaryPairs.stream()
        .filter(
            p -> {
              final PlanSummary summary = p.getLeft();
              return summary.getClusterName().isPresent()
                  && summary.getActionType() == PlannedAction.ActionType.REPAIR;
            })
        .map(p -> p.getLeft().getClusterName().get())
        .collect(Collectors.toSet());
  }

  public static Map<CloudProvider, List<BackupRestoreJobPlanUnit>> getRestoreJobsByProvider(
      final List<BackupRestoreJobPlanUnit> pRestoreJobs,
      final NDSGroup pNDSGroup,
      final boolean isRestoreCrossProjectGCPEnabled,
      final boolean isRestoreCrossProjectAzureEnabled,
      final boolean isRestoreCrossProjectAwsEnabled) {
    final Map<CloudProvider, List<BackupRestoreJobPlanUnit>> restoreJobsByProvider =
        pRestoreJobs.stream()
            .filter(restoreJob -> (pNDSGroup.getGroupId().equals(restoreJob.getSourceProjectId())))
            .collect(Collectors.groupingBy(BackupRestoreJobPlanUnit::getCloudProvider));

    if (isRestoreCrossProjectGCPEnabled) {
      for (final BackupRestoreJobPlanUnit restoreJob : pRestoreJobs) {
        if ((!pNDSGroup.getGroupId().equals(restoreJob.getSourceProjectId())
            && (restoreJob.getPlanStrategy().isDirectAttach()
                && restoreJob.getCloudProvider() == CloudProvider.GCP))) {
          restoreJobsByProvider.putIfAbsent(CloudProvider.GCP, new ArrayList<>());
          restoreJobsByProvider.get(CloudProvider.GCP).add(restoreJob);
        }
      }
    }
    if (isRestoreCrossProjectAzureEnabled) {
      for (final BackupRestoreJobPlanUnit restoreJob : pRestoreJobs) {
        if ((!pNDSGroup.getGroupId().equals(restoreJob.getSourceProjectId())
            && (restoreJob.getPlanStrategy().isDirectAttach()
                && restoreJob.getCloudProvider() == CloudProvider.AZURE))) {
          restoreJobsByProvider.putIfAbsent(CloudProvider.AZURE, new ArrayList<>());
          restoreJobsByProvider.get(CloudProvider.AZURE).add(restoreJob);
        }
      }
    }
    if (isRestoreCrossProjectAwsEnabled) {
      for (final BackupRestoreJobPlanUnit restoreJob : pRestoreJobs) {
        if ((!pNDSGroup.getGroupId().equals(restoreJob.getSourceProjectId())
            && (restoreJob.getPlanStrategy().isDirectAttach()
                && restoreJob.getCloudProvider() == CloudProvider.AWS))) {
          restoreJobsByProvider.putIfAbsent(CloudProvider.AWS, new ArrayList<>());
          restoreJobsByProvider.get(CloudProvider.AWS).add(restoreJob);
        }
      }
    }
    return restoreJobsByProvider;
  }

  public static PlanResult build(
      final Function<Attributes, NDSPlanningTraceWrapper> pNDSPlanningTraceWrapperSupplier,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final AutomationConfig pAutomationConfig,
      final List<Cluster> pClusters,
      final List<HostCluster> pHostClusters,
      final List<BackupJob> pBackupJobs,
      final List<BackupRestoreJobPlanUnit> pRestoreJobs,
      final List<SystemClusterJob> pSystemClusterJobs,
      final Set<RegionName> pRegionsWithSnapshotsAndSnapshotDistribution,
      final CpsSnapshotEngine pCpsSnapshotEngine,
      final List<TenantRestore> pTenantRestores,
      final List<NDSACMECert> pACMEProxyCertificates,
      final List<? extends PrivateLinkConnectionRule> pPrivateLinkConnectionRules,
      final List<BaseMultiTargetConnectionRule> pMultiTargetConnectionRules,
      final Logger pLogger,
      final AppSettings pAppSettings,
      final List<ServerlessLoadBalancingDeployment> pServerlessLoadBalancingDeployments,
      final ServerlessMTMPool pServerlessMTMPool,
      final List<FastTenantPreAllocatedRecord> pFastTenantPreAllocatedRecords,
      final List<IngestionPipelineRun> pPipelineRunsReadyForIngestion,
      final Map<ObjectId, List<TenantEndpoint>> pTenantEndpointsMappedByCluster,
      final List<ClusterOutageSimulation> pActiveClusterOutageSimulations,
      final List<MTMCompaction> pProcessingMTMCompactions,
      final List<CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>>
          pHeldAWSCapacityRequests,
      final List<CheckCapacityRequest<AzureInstanceCapacitySpec, AzureCheckResult>>
          pActiveAzureCapacityRequests,
      final Set<ObjectId> pContainersWithActiveDedicatedSearch,
      final Map<ObjectId, ClusterDescriptionProcessArgs> pProcessArgsMappedByCluster,
      final boolean pHasStreamsPrivateNetworking,
      final List<ServerlessUpgradeToDedicatedStatus> pServerlessUpgradeToDedicatedStatuses,
      final NDSProjectPlanningSummaryBuilder pPlanningSummaryBuilder,
      final List<TenantUpgradeStatus> pTenantUpgradeStatuses,
      final List<FlexTenantMigration> pFlexTenantMigrations,
      final List<AzureCapacityDenyListEntry> pAzureDenylistEntriesNeedingCapacityCheck,
      final Map<ObjectId, CloudProviderAvailability> pContainerIdToAvailabilityMap,
      final List<ShadowClusterExposureJob> pShadowClusterExposureJobs,
      final HostLastPingSvc pHostLastPingSvc) {
    final List<Host> hosts = new ArrayList<>();
    for (final HostCluster hc : pHostClusters) {
      if (!hc.isReplicaSet()) {
        continue;
      }
      final List<Host> hostList = hc.getHosts();
      if (hostList == null) {
        pLogger.warn(
            "HostCluster ({}) in group ({}) has no associated monitoring hosts",
            hc.getClusterId(),
            pGroup.getId());
      } else {
        hosts.addAll(hostList);
      }
    }

    final List<Pair<PlanSummary, Plan>> plans = new ArrayList<>();
    final List<Cluster> unableToPlanClusters = new ArrayList<>();

    pNDSPlanningTraceWrapperSupplier
        .apply(null)
        .run(
            () -> {
              pLogger.trace("Checking if project level plan is needed");
              // Plan for project
              final Pair<PlanSummary, Plan> projectPair =
                  PlanResult.buildPlanEntryForProject(pLogger, pNDSGroup, pCpsSnapshotEngine);
              if (projectPair.getLeft() != null) {
                plans.add(projectPair);
              }
            },
            "build-plan-entry-for-project");

    pNDSPlanningTraceWrapperSupplier
        .apply(null)
        .run(
            () -> {
              // Plan for project-level ACME configuration
              pLogger.trace("Checking if project level ACME plan is needed");
              final List<Cluster> proxiedClusters =
                  NDSClusterSvc.getProxiedClustersForACMECertUpdate(pNDSGroup, pClusters);
              final List<Pair<PlanSummary, Plan>> projectACMEPairs =
                  PlanResult.buildPlanEntryForProjectACME(
                      pLogger,
                      pNDSGroup,
                      pACMEProxyCertificates,
                      pNDSGroup.getCloudProviderContainers(),
                      proxiedClusters);
              plans.addAll(projectACMEPairs);
            },
            "build-plan-entry-for-project-acme");

    // Plan for FastTenantPreAllocatedRecord
    final Map<ClusterDescriptionId, ClusterDescription> clustersById =
        pClusters.stream()
            .map(Cluster::getClusterDescription)
            .collect(
                Collectors.toMap(
                    cd -> new ClusterDescriptionId(cd.getName(), cd.getGroupId()), cd -> cd));
    for (final FastTenantPreAllocatedRecord record : pFastTenantPreAllocatedRecords) {
      pNDSPlanningTraceWrapperSupplier
          .apply(Attributes.builder().put("recordId", record.getId().toHexString()).build())
          .run(
              () -> {
                if (pNDSGroup.isFreeMTMHolder() || pNDSGroup.isFlexMTMHolder()) {
                  final LinkedMTM mtm =
                      record.getFastRecordType() == CloudProvider.FREE
                          ? ((FastSharedPreAllocatedRecord) record).getMtm()
                          : ((FastFlexPreAllocatedRecord) record).getMtm();
                  final ClusterDescription cd =
                      clustersById.getOrDefault(
                          new ClusterDescriptionId(mtm.getClusterName(), mtm.getGroupId()), null);
                  if (cd == null
                      || cd.getMongoDBUriHosts() == null
                      || cd.getMongoDBUriHosts().length == 0) {
                    pLogger.info(
                        "Skipping plan generation for fast provision record {} - mtm {} still"
                            + " provisioning",
                        record.getId(),
                        mtm.getClusterName());
                    return;
                  }
                } else if (pNDSGroup.isServerlessMTMHolder()) {
                  final ServerlessLoadBalancingDeployment loadBalancingDeployment =
                      pServerlessLoadBalancingDeployments.stream()
                          .filter(
                              lbd ->
                                  lbd.getId()
                                      .equals(
                                          ((FastServerlessPreAllocatedRecord) record)
                                              .getLoadBalancer()
                                              .getLoadBalancingDeploymentId()))
                          .findFirst()
                          .orElseThrow(
                              () ->
                                  new IllegalStateException(
                                      "Serverless record load balancing deployment associated with"
                                          + " record does not exist."));
                  if (!loadBalancingDeployment.isProvisioned()) {
                    pLogger.info(
                        "Skipping plan generation for fast provision record {} - load balancing"
                            + " deployment {} still provisioning",
                        record.getId(),
                        ((FastServerlessPreAllocatedRecord) record)
                            .getLoadBalancer()
                            .getLoadBalancingDeploymentId());
                    return;
                  }
                }
                final Pair<PlanSummary, Plan> recordPair =
                    PlanResult.buildPlanEntryForFastTenantRecord(
                        pLogger, pNDSGroup, record.getContainerId(), record);
                if (recordPair.getLeft() != null) {
                  plans.add(recordPair);
                }
              },
              "build-plan-entry-for-fast-tenant-record");
    }

    // Plan for containers
    for (final CloudProviderContainer container : pNDSGroup.getCloudProviderContainers()) {
      pNDSPlanningTraceWrapperSupplier
          .apply(Attributes.builder().put("containerId", container.getId().toHexString()).build())
          .run(
              () -> {
                final Pair<PlanSummary, Plan> containerPair =
                    PlanResult.buildPlanEntryForContainer(
                        pLogger,
                        pNDSGroup,
                        pGroup,
                        pClusters,
                        pRegionsWithSnapshotsAndSnapshotDistribution,
                        container,
                        pAppSettings,
                        pServerlessLoadBalancingDeployments,
                        pServerlessMTMPool,
                        pBackupJobs,
                        pRestoreJobs,
                        pHeldAWSCapacityRequests,
                        pActiveAzureCapacityRequests,
                        pContainersWithActiveDedicatedSearch,
                        pHasStreamsPrivateNetworking,
                        pServerlessUpgradeToDedicatedStatuses,
                        pAzureDenylistEntriesNeedingCapacityCheck);
                if (containerPair.getLeft() != null) {
                  plans.add(containerPair);
                }
              },
              "build-plan-entry-for-container");
    }

    pNDSPlanningTraceWrapperSupplier
        .apply(null)
        .run(
            () -> {
              final List<FastServerlessPreAllocatedRecord> fastServerlessPreAllocatedRecords =
                  pFastTenantPreAllocatedRecords.stream()
                      .filter(record -> record instanceof FastServerlessPreAllocatedRecord)
                      .map(FastServerlessPreAllocatedRecord.class::cast)
                      .collect(Collectors.toList());

              // Plans for serverless load balancing deployments
              PlanResult.buildServerlessPlanResult(
                      pNDSGroup,
                      pServerlessMTMPool,
                      pServerlessLoadBalancingDeployments,
                      fastServerlessPreAllocatedRecords,
                      pLogger,
                      pAppSettings,
                      pGroup)
                  .getPlanSummaryPairs()
                  .stream()
                  .filter(planSummaryPair -> planSummaryPair.getLeft() != null)
                  .forEach(plans::add);
            },
            "build-plan-entry-for-serverless-load-balancing-deployments");

    // Plans for clusters
    for (final Cluster cluster : pClusters) {
      final Attributes spanAttributesForClusterName =
          getSpanAttributesByClusterName(cluster.getClusterDescription().getName());

      final AtomicReference<List<CloudProviderContainer>> containersForCluster =
          new AtomicReference<>();
      final AtomicReference<BackupJob> backupJob = new AtomicReference<>();
      final AtomicReference<BackupRestoreJobPlanUnit> restoreTarget = new AtomicReference<>();
      final AtomicReference<TenantRestore> tenantRestore = new AtomicReference<>();
      final AtomicReference<List<TenantEndpoint>> tenantEndpointsForCluster =
          new AtomicReference<>();
      final AtomicReference<ClusterOutageSimulation> outageSimulationForCluster =
          new AtomicReference<>();
      final AtomicReference<MTMCompaction> mtmCompactionForCluster = new AtomicReference<>();
      final AtomicReference<TenantUpgradeStatus> tenantUpgradeStatus = new AtomicReference<>();
      final AtomicReference<FlexTenantMigration> flexTenantMigration = new AtomicReference<>();

      pNDSPlanningTraceWrapperSupplier
          .apply(spanAttributesForClusterName)
          .run(
              () -> {
                containersForCluster.set(getContainersForCluster(pNDSGroup, cluster));
                backupJob.set(
                    pBackupJobs.stream()
                        .filter(job -> !job.isRetaining())
                        .filter(
                            b ->
                                cluster
                                    .getClusterDescription()
                                    .getName()
                                    .equals(b.getClusterName()))
                        .findFirst()
                        .orElse(null));
                restoreTarget.set(
                    pRestoreJobs.stream()
                        .filter(job -> job.involvesCluster(cluster.getClusterDescription()))
                        .findFirst()
                        .orElse(null));
                tenantRestore.set(
                    pTenantRestores.stream()
                        .filter(
                            r ->
                                cluster
                                    .getClusterDescription()
                                    .getName()
                                    .equals(r.getRestoreDelivery().getTargetDeploymentItemName()))
                        .findFirst()
                        .orElse(null));
                tenantEndpointsForCluster.set(
                    pTenantEndpointsMappedByCluster.getOrDefault(
                        cluster.getClusterDescription().getUniqueId(), Collections.emptyList()));
                outageSimulationForCluster.set(
                    pActiveClusterOutageSimulations.stream()
                        .filter(
                            cos ->
                                cos.getClusterName()
                                    .equals(cluster.getClusterDescription().getName()))
                        .findFirst()
                        .orElse(null));
                mtmCompactionForCluster.set(
                    pProcessingMTMCompactions.stream()
                        .filter(
                            c ->
                                c.hasPendingPauseTenant(
                                    cluster.getClusterDescription().getUniqueId()))
                        .findFirst()
                        .orElse(null));
                tenantUpgradeStatus.set(
                    pTenantUpgradeStatuses.stream()
                        .filter(
                            tenantUpgrade ->
                                tenantUpgrade
                                    .getSourceTenantUniqueId()
                                    .equals(cluster.getClusterDescription().getUniqueId()))
                        .findFirst()
                        .orElse(null));
                flexTenantMigration.set(
                    pFlexTenantMigrations.stream()
                        .filter(
                            flexMigration ->
                                flexMigration
                                        .getTenantUniqueId()
                                        .equals(cluster.getClusterDescription().getUniqueId())
                                    && MigrationStatus.isMigrationRunningOrRollingBack(
                                        flexMigration.getStatus()))
                        .findFirst()
                        .orElse(null));
              },
              "build-plan-entry-per-cluster-setup");

      pNDSPlanningTraceWrapperSupplier
          .apply(spanAttributesForClusterName)
          .run(
              () -> {
                final Pair<PlanSummary, Plan> freeCompactionPair =
                    PlanResult.buildPlanEntryForFreeCompaction(
                        pLogger,
                        mtmCompactionForCluster.get(),
                        cluster.getClusterDescription(),
                        pNDSGroup);
                if (freeCompactionPair.getLeft() != null) {
                  plans.add(freeCompactionPair);
                }
              },
              "build-plan-entry-for-free-compaction");

      pNDSPlanningTraceWrapperSupplier
          .apply(spanAttributesForClusterName)
          .run(
              () -> {
                if (pCpsSnapshotEngine.shouldCopySnapshotNow(cluster.getClusterDescription())) {
                  final Pair<PlanSummary, Plan> pair =
                      PlanResult.buildPlanEntryForCopySnapshot(
                          pLogger,
                          pNDSGroup,
                          containersForCluster.get(),
                          restoreTarget.get(),
                          cluster.getClusterDescription(),
                          pCpsSnapshotEngine);
                  if (pair.getLeft() != null) {
                    plans.add(pair);
                  }
                }
              },
              "build-plan-entry-for-copy-snapshot");

      pNDSPlanningTraceWrapperSupplier
          .apply(spanAttributesForClusterName)
          .run(
              () -> {
                final Pair<PlanSummary, Plan> concurrentSnapshotPair =
                    PlanResult.buildPlanEntryForConcurrentSnapshot(
                        pLogger,
                        pGroup,
                        pNDSGroup,
                        cluster,
                        containersForCluster.get(),
                        restoreTarget.get(),
                        pCpsSnapshotEngine);
                if (concurrentSnapshotPair.getLeft() != null) {
                  plans.add(concurrentSnapshotPair);
                }
              },
              "build-plan-entry-for-concurrent-snapshot");

      pNDSPlanningTraceWrapperSupplier
          .apply(spanAttributesForClusterName)
          .run(
              () -> {
                final Pair<PlanSummary, Plan> flexMigrationPair =
                    PlanResult.buildPlanEntryForFlexMigration(
                        pLogger,
                        cluster.getClusterDescription(),
                        containersForCluster.get(),
                        flexTenantMigration.get(),
                        tenantUpgradeStatus.get(),
                        pNDSGroup);
                if (flexMigrationPair.getLeft() != null) {
                  plans.add(flexMigrationPair);
                }
              },
              "build-plan-entry-for-flex-migration");

      pNDSPlanningTraceWrapperSupplier
          .apply(spanAttributesForClusterName)
          .run(
              () -> {
                try {
                  final Pair<PlanSummary, Plan> pair =
                      PlanResult.buildPlanEntryForCluster(
                          pLogger,
                          pNDSGroup,
                          pGroup,
                          pAutomationConfig,
                          hosts,
                          pHostClusters,
                          containersForCluster.get(),
                          backupJob.get(),
                          restoreTarget.get(),
                          tenantRestore.get(),
                          cluster,
                          pCpsSnapshotEngine,
                          pPrivateLinkConnectionRules,
                          pMultiTargetConnectionRules,
                          tenantEndpointsForCluster.get(),
                          outageSimulationForCluster.get(),
                          pAppSettings,
                          pProcessArgsMappedByCluster,
                          pPlanningSummaryBuilder.getClusterPlanningSummaryBuilder(
                              cluster.getClusterDescription().getUniqueId()),
                          flexTenantMigration.get(),
                          pContainerIdToAvailabilityMap,
                          pHostLastPingSvc);
                  if (pair.getLeft() != null) {
                    plans.add(pair);
                  }
                } catch (final UnableToPlanException e) {
                  pLogger.warn(
                      "Unable to generate plan for {} in current state",
                      cluster.getClusterDescription().getName(),
                      e);
                  unableToPlanClusters.add(cluster);
                }
              },
              "build-plan-entry-for-cluster");
    }

    final List<BackupJob> retainedBackupJobs =
        pBackupJobs.stream().filter(BackupJob::isRetaining).toList();

    for (final BackupJob retainedBackupJob : retainedBackupJobs) {
      pNDSPlanningTraceWrapperSupplier
          .apply(getSpanAttributesByClusterName(retainedBackupJob.getClusterName()))
          .run(
              () -> {
                pLogger.info(
                    "Generating plan for retained backupjob for deleted cluster {}",
                    retainedBackupJob.getClusterName());
                try {
                  final Pair<PlanSummary, Plan> pair =
                      PlanResult.buildPlanEntryForRetainedBackupJob(
                          retainedBackupJob, pCpsSnapshotEngine, pLogger);

                  if (pair.getLeft() != null) {
                    plans.add(pair);
                  }
                } catch (final Exception e) {
                  pLogger.error(
                      "Error generating plan for retained backupjob for deleted cluster {}.",
                      retainedBackupJob.getClusterName(),
                      e);
                }
              },
              "build-plan-entry-for-retained-backup-job");
    }

    for (SystemClusterJob systemClusterJob : pSystemClusterJobs) {
      final Pair<PlanSummary, Plan> systemClusterDeploymentPair;

      if (SystemProjectType.SHADOW_CLUSTER.equals(systemClusterJob.getSystemProjectType())) {
        systemClusterDeploymentPair =
            buildPlanEntryForShadowClusterDeployment(pLogger, (ShadowClusterJob) systemClusterJob);
      } else {
        systemClusterDeploymentPair =
            buildPlanEntryForSystemClusterDeployment(
                pLogger,
                systemClusterJob,
                pCpsSnapshotEngine
                    .getCpsSvc()
                    .isCpsSkipSystemClusterDestroyEnabled(systemClusterJob.getSourceProjectId()));
      }

      if (systemClusterDeploymentPair.getLeft() != null) {
        plans.add(systemClusterDeploymentPair);
      }
      final Pair<PlanSummary, Plan> systemClusterExecutionPair =
          buildPlanEntryForSystemClusterExecution(pLogger, systemClusterJob);
      if (systemClusterExecutionPair.getLeft() != null) {
        plans.add(systemClusterExecutionPair);
      }

      final Pair<PlanSummary, Plan> systemClusterCleanupPair =
          buildPlanEntryForSystemClusterCleanup(pLogger, systemClusterJob);
      if (systemClusterCleanupPair.getLeft() != null) {
        plans.add(systemClusterCleanupPair);
      }
    }

    for (final ShadowClusterExposureJob shadowClusterExposureJob : pShadowClusterExposureJobs) {
      final Pair<PlanSummary, Plan> shadowClusterExposurePair =
          buildPlanEntryForShadowClusterExposure(pLogger, shadowClusterExposureJob);
      if (shadowClusterExposurePair.getLeft() != null) {
        plans.add(shadowClusterExposurePair);
      }
    }

    // The pRestoreJobs collection can contain BackupRestoreJobPlanUnit's where this project
    // is either the source project *or* the destination project of an automated restore.
    // If feature flag is on and Cloud Provider is GCP or AZURE we want to allow cross project
    // restores.
    // Otherwise, we want to only generate restore plans
    // here for jobs where this project is a source (such that we avoid planner rounds for
    // both the source project *and* the destination projects generating the same plans.)
    final boolean isRestoreCrossProjectGCPEnabled =
        pCpsSnapshotEngine.getCpsSvc().isRestoreCrossProjectGCPEnabled(pGroup);
    final boolean isRestoreCrossProjectAzureEnabled =
        pCpsSnapshotEngine.getCpsSvc().isRestoreCrossProjectAzureEnabled(pGroup);
    final boolean isRestoreCrossProjectAwsEnabled =
        pCpsSnapshotEngine.getCpsSvc().isRestoreCrossProjectAwsEnabled(pGroup);
    final Map<CloudProvider, List<BackupRestoreJobPlanUnit>> sourceRestoreJobsByProvider =
        getRestoreJobsByProvider(
            pRestoreJobs,
            pNDSGroup,
            isRestoreCrossProjectGCPEnabled,
            isRestoreCrossProjectAzureEnabled,
            isRestoreCrossProjectAwsEnabled);

    for (final Map.Entry<CloudProvider, List<BackupRestoreJobPlanUnit>> entry :
        sourceRestoreJobsByProvider.entrySet()) {
      pNDSPlanningTraceWrapperSupplier
          .apply(null)
          .run(
              () -> {
                final CloudProvider cloudProvider = entry.getKey();
                final List<BackupRestoreJobPlanUnit> sourceRestoreJobs =
                    sourceRestoreJobsByProvider.getOrDefault(
                        cloudProvider, Collections.emptyList());

                for (final BackupRestoreJobPlanUnit job : sourceRestoreJobs) {
                  pNDSPlanningTraceWrapperSupplier
                      .apply(
                          Attributes.builder()
                              .put("sourceClusterName", job.getSourceClusterName())
                              .build())
                      .run(
                          () -> {
                            boolean shouldAllowRestore = true;
                            if (job.getPlanStrategy().isDirectAttach()
                                && job.getTargetClusterName().isPresent()) {
                              // block direct attach restore until cluster upgrade/snapshot is
                              // completed
                              // TODO: this logic likely needs to be updated once cross-project
                              // direct attach restore is supported
                              final Optional<Cluster> targetCluster =
                                  pClusters.stream()
                                      .filter(
                                          cluster ->
                                              cluster
                                                  .getClusterDescription()
                                                  .getName()
                                                  .equals(job.getTargetClusterName().get()))
                                      .findFirst();
                              if (targetCluster.isPresent()
                                  && targetCluster
                                      .get()
                                      .getClusterDescription()
                                      .getRestoreJobIds()
                                      .isEmpty()) {
                                shouldAllowRestore = false;
                              }
                            }

                            if (!shouldAllowRestore) {
                              return;
                            }

                            final Set<RegionName> regionNames = new HashSet<>();
                            final BackupSnapshotDao snapshotDao =
                                pCpsSnapshotEngine.getCpsSvc().getBackupSnapshotDao();

                            for (final RestorePlanning pJob : job.getReplSetJobs()) {
                              if (pJob instanceof TenantUpgradeToServerlessBackupRestoreJob sJob) {
                                regionNames.add(sJob.getRegionName());
                              } else if (pJob instanceof ReplicaSetBackupRestoreJob rsJob) {
                                regionNames.add(rsJob.getRegionName());
                                final ReplicaSetBackupSnapshot replSetSnapshot =
                                    snapshotDao
                                        .findReplicaSetSnapshotById(rsJob.getSnapshotId())
                                        .get();

                                final List<ObjectId> copySnapshotIds =
                                    replSetSnapshot.getCopySnapshotIds();
                                final List<ReplicaSetBackupSnapshot> copySnapshots =
                                    snapshotDao.findReplicaSetSnapshotByIds(copySnapshotIds);
                                copySnapshots.stream()
                                    .filter(
                                        s -> s.isCompleted() && !s.getDeleted() && !s.getPurged())
                                    .forEach(s -> regionNames.add(s.getRegion()));
                              } else {
                                pLogger.error(
                                    "Unexpected RestorePlanning class: {}",
                                    job.getClass().getName());
                              }
                            }

                            final List<CloudProviderContainer> containers =
                                pNDSGroup.getCloudProviderContainersByType(cloudProvider).stream()
                                    .filter(c -> c.isResponsibleForAnyOfTheseRegions(regionNames))
                                    .collect(Collectors.toList());

                            final boolean pIsCpsSnapshotConsistentExportEnabledFeatureFlagOn =
                                pCpsSnapshotEngine
                                    .getCpsSvc()
                                    .isCpsSnapshotConsistentExportEnabled(pGroup);

                            final boolean isCpsOptimizedDaRestoreEnabled =
                                pCpsSnapshotEngine
                                    .getCpsSvc()
                                    .isCpsOptimizedDaRestoreEnabled(pGroup, job.getCloudProvider());

                            final Pair<PlanSummary, Plan> pair =
                                PlanResult.buildPlanEntryForRestoreJob(
                                    pLogger,
                                    pNDSGroup,
                                    containers,
                                    job,
                                    pIsCpsSnapshotConsistentExportEnabledFeatureFlagOn,
                                    isCpsOptimizedDaRestoreEnabled);
                            if (pair.getLeft() != null) {
                              plans.add(pair);
                            }
                          },
                          "build-plan-entry-for-restore-job");
                }
              },
              String.format("source-restore-jobs-%s", entry.getKey().name().toLowerCase()));
    }

    // Plans for ingestion pipeline runs
    for (final IngestionPipelineRun pipelineRun : pPipelineRunsReadyForIngestion) {
      pNDSPlanningTraceWrapperSupplier
          .apply(
              Attributes.builder().put("pipelineRunId", pipelineRun.getId().toHexString()).build())
          .run(
              () -> {
                final Pair<PlanSummary, Plan> pair =
                    buildPlanEntryForIngestionPipelineRun(
                        pLogger, pNDSGroup, CloudProvider.AWS, pipelineRun);
                plans.add(pair);
              },
              "build-plan-entry-for-ingestion-pipeline-run");
    }

    // Apply filters sequentially
    List<Pair<PlanSummary, Plan>> currentFilteredPlans = plans;

    currentFilteredPlans =
        AllowedMovesForSystemExport.filterPlansInSystemProject(
            pNDSGroup, currentFilteredPlans, pLogger);
    currentFilteredPlans =
        AllowedMovesForCollectionRestore.filterPlansInSystemProject(
            pNDSGroup, currentFilteredPlans, pLogger);
    currentFilteredPlans =
        AllowedMovesForShadowClusters.filterPlansInSystemProject(
            pNDSGroup, currentFilteredPlans, pLogger);

    if (currentFilteredPlans.isEmpty()) {
      pLogger.trace("No cloud providers need a plan after all filters.");
    }

    return new PlanResult(currentFilteredPlans, unableToPlanClusters);
  }

  private static ServerlessPlanResult buildServerlessPlanResult(
      final NDSGroup pNDSGroup,
      final ServerlessMTMPool pServerlessMTMPool,
      final List<ServerlessLoadBalancingDeployment> pServerlessLoadBalancingDeployments,
      final List<FastServerlessPreAllocatedRecord> pFastServerlessPreAllocatedRecords,
      final Logger pLogger,
      final AppSettings pAppSettings,
      final Group pGroup) {

    if (pServerlessLoadBalancingDeployments.isEmpty()) {
      return new ServerlessPlanResult(List.of());
    }

    final Optional<CloudProviderContainer> container =
        pNDSGroup.getCloudProviderContainer(
            pServerlessLoadBalancingDeployments.get(0).getCloudProvider(),
            pServerlessLoadBalancingDeployments.get(0).getProviderRegionName(),
            null);

    if (container.isEmpty()) {
      pLogger.info(
          "No container found for serverless load balancing deployment. Skipping deployment"
              + " actions.");

      return new ServerlessPlanResult(List.of());
    }

    final List<Pair<PlanSummary, Plan>> serverlessPlanSummaryPairs =
        pServerlessLoadBalancingDeployments.stream()
            .map(
                serverlessLoadBalancingDeployment -> {
                  final List<FastServerlessPreAllocatedRecord> preAllocatedRecordsForDeployment =
                      pFastServerlessPreAllocatedRecords.stream()
                          .filter(
                              record ->
                                  serverlessLoadBalancingDeployment
                                      .getId()
                                      .equals(
                                          record.getLoadBalancer().getLoadBalancingDeploymentId()))
                          .collect(Collectors.toList());

                  return PlanResult.buildPlanEntryForServerlessLoadBalancingDeployment(
                      pLogger,
                      pNDSGroup,
                      container.get(),
                      serverlessLoadBalancingDeployment,
                      preAllocatedRecordsForDeployment,
                      pServerlessMTMPool,
                      pAppSettings,
                      pGroup);
                })
            .filter(planSummaryPair -> planSummaryPair.getLeft() != null)
            .collect(Collectors.toList());

    return new ServerlessPlanResult(serverlessPlanSummaryPairs);
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForProject(
      final Logger pLogger, final NDSGroup pNDSGroup, final CpsSnapshotEngine pSnapshotEngine) {
    final Plan plan = PlanResult.getNewPlan(pNDSGroup.getGroupId());
    final List<PlannedAction> projectActions = new ArrayList<>();
    final PlannedActionFactory factory = new PlannedActionFactory(plan.getPlanContext());

    if (pNDSGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent()) {
      projectActions.add(factory.forSyncMongoDBConfigForProject(plan.getPlanContext()));
    }

    if (pNDSGroup.getNeedsJWKSRefreshAfter().isPresent()) {
      projectActions.add(factory.forRefreshOidcKeys(plan.getPlanContext()));
    }

    if (pNDSGroup.getPrivateNetworkSettings() != null
        && pNDSGroup.getPrivateNetworkSettings().getNeedsHostnameSyncAfter() != null
        && pNDSGroup
            .getPrivateNetworkSettings()
            .getNeedsHostnameSyncAfter()
            .after(TimeUtils.getStartOfDayAsDate(new Date()))) {
      final Set<CloudProvider> cloudProviders =
          pNDSGroup.getPrivateNetworkSettings().getEndpointIds().stream()
              .map(NDSPrivateNetworkEndpointIdEntry::getProvider)
              .collect(Collectors.toSet());
      for (final CloudProvider provider : cloudProviders) {
        projectActions.add(factory.forSyncDataLakePrivateEndpointHostname(provider));
      }
    }

    if (pNDSGroup.getPrivateNetworkSettings() != null
        && pNDSGroup.getPrivateNetworkSettings().getEndpointIds().stream()
            .anyMatch(
                e ->
                    e.getProvider() == CloudProvider.AZURE
                        && (e.getStatus() == NDSPrivateNetworkEndpointIdEntry.Status.PENDING
                            || e.getStatus()
                                == NDSPrivateNetworkEndpointIdEntry.Status.DELETING))) {
      projectActions.add(factory.forSyncDataLakePrivateLinkConnection());
    }

    final Optional<DataProtectionSettings> dataProtectionSettings =
        pSnapshotEngine.getCpsSvc().getDataProtectionSettings(pNDSGroup.getGroupId());
    if (dataProtectionSettings.isPresent()) {
      if (dataProtectionSettings.get().getState() == DataProtectionSettings.State.ENABLING) {
        projectActions.add(factory.forEnabledDataProtection(plan.getPlanContext()));
      } else if (dataProtectionSettings.get().getState()
          == DataProtectionSettings.State.DISABLING) {
        projectActions.add(factory.forDisableDataProtection(plan.getPlanContext()));
      } else if (dataProtectionSettings.get().getState() == DataProtectionSettings.State.UPDATING) {
        projectActions.add(factory.forUpdateDataProtection(plan.getPlanContext()));
      }
    }

    return PlanResult.buildPlanEntry(
        pLogger,
        projectActions,
        Collections.emptyList(),
        plan,
        Collections.emptyList(),
        null,
        null,
        null,
        null,
        null,
        PlanSummary.ResourceType.PROJECT,
        "project");
  }

  private static Optional<CloudProvider> getBackingCloudProviderForTenantContainer(
      final CloudProviderContainer pCloudProviderContainer,
      final Map<String, Cluster> pNameToCluster,
      final CloudProvider pProvider) {
    if (!pProvider.isTenantProvider()) {
      throw new IllegalArgumentException("Received unexpected cloud provider: " + pProvider.name());
    } else if (!pProvider.equals(pCloudProviderContainer.getCloudProvider())
        || ((pProvider == CloudProvider.FREE
                && !(pCloudProviderContainer instanceof FreeCloudProviderContainer))
            || (pProvider == CloudProvider.SERVERLESS
                && !(pCloudProviderContainer instanceof ServerlessCloudProviderContainer))
            || (pProvider == CloudProvider.FLEX
                && !(pCloudProviderContainer instanceof FlexCloudProviderContainer)))) {
      throw new IllegalArgumentException(
          String.format(
              "Received unexpected cloud provider of type %s and container of type %s",
              pProvider.name(), pCloudProviderContainer.getClass().getSimpleName()));
    }

    return Optional.of(pCloudProviderContainer)
        .map(TenantCloudProviderContainer.class::cast)
        .map(TenantCloudProviderContainer::getTenantClusterName)
        .map(pNameToCluster::get)
        .map(Cluster::getClusterDescription)
        .filter(ClusterDescription::isTenantCluster)
        .map(ClusterDescription::getBackingProvider);
  }

  protected static Optional<CloudProvider> getBackingCloudProviderForFreeContainer(
      final CloudProviderContainer pCloudProviderContainer,
      final Map<String, Cluster> pNameToCluster) {
    return getBackingCloudProviderForTenantContainer(
        pCloudProviderContainer, pNameToCluster, CloudProvider.FREE);
  }

  protected static Optional<CloudProvider> getBackingCloudProviderForServerlessContainer(
      final CloudProviderContainer pCloudProviderContainer,
      final Map<String, Cluster> pNameToCluster) {
    return getBackingCloudProviderForTenantContainer(
        pCloudProviderContainer, pNameToCluster, CloudProvider.SERVERLESS);
  }

  protected static Optional<CloudProvider> getBackingCloudProviderForFlexContainer(
      final CloudProviderContainer pCloudProviderContainer,
      final Map<String, Cluster> pNameToCluster) {
    return getBackingCloudProviderForTenantContainer(
        pCloudProviderContainer, pNameToCluster, CloudProvider.FLEX);
  }

  private static Map<CloudProvider, List<CloudProviderContainer>>
      groupTenantContainersByBackingCloudProvider(
          final List<CloudProviderContainer> pCloudProviderContainers,
          final List<Cluster> pListOfTenantClusters,
          final Set<CloudProvider> pProviders) {
    if (pProviders.isEmpty() || !pProviders.stream().allMatch(CloudProvider::isTenantProvider)) {
      throw new IllegalArgumentException(
          String.format("Received unexpected cloud provider(s): %s", pProviders));
    }

    final Map<CloudProvider, List<CloudProviderContainer>> groupByCloudProvider = new HashMap<>();
    final Map<String, Cluster> nameToCluster =
        pListOfTenantClusters.stream()
            .collect(
                Collectors.toMap(c -> c.getClusterDescription().getName(), Function.identity()));

    for (final CloudProviderContainer cloudProviderContainer : pCloudProviderContainers) {
      if (pProviders.contains(cloudProviderContainer.getCloudProvider())) {
        getBackingCloudProviderForTenantContainer(
                cloudProviderContainer, nameToCluster, cloudProviderContainer.getCloudProvider())
            .ifPresent(
                pCloudProvider -> {
                  groupByCloudProvider.putIfAbsent(pCloudProvider, new ArrayList<>());
                  groupByCloudProvider.get(pCloudProvider).add(cloudProviderContainer);
                });
      }
    }
    return groupByCloudProvider;
  }

  protected static Map<CloudProvider, List<CloudProviderContainer>>
      groupFreeContainersByBackingCloudProvider(
          final List<CloudProviderContainer> pCloudProviderContainers,
          final List<Cluster> pListOfTenantClusters) {
    return groupTenantContainersByBackingCloudProvider(
        pCloudProviderContainers, pListOfTenantClusters, Set.of(CloudProvider.FREE));
  }

  protected static Map<CloudProvider, List<CloudProviderContainer>>
      groupServerlessContainersByBackingCloudProvider(
          final List<CloudProviderContainer> pCloudProviderContainers,
          final List<Cluster> pListOfTenantClusters) {
    return groupTenantContainersByBackingCloudProvider(
        pCloudProviderContainers, pListOfTenantClusters, Set.of(CloudProvider.SERVERLESS));
  }

  protected static Map<CloudProvider, List<CloudProviderContainer>>
      groupFlexContainersByBackingCloudProvider(
          final List<CloudProviderContainer> pCloudProviderContainers,
          final List<Cluster> pListOfTenantClusters) {
    return groupTenantContainersByBackingCloudProvider(
        pCloudProviderContainers, pListOfTenantClusters, Set.of(CloudProvider.FLEX));
  }

  protected static Map<CloudProvider, List<CloudProviderContainer>>
      groupTenantContainersByBackingCloudProvider(
          final List<CloudProviderContainer> pCloudProviderContainers,
          final List<Cluster> pListOfTenantClusters) {
    return groupTenantContainersByBackingCloudProvider(
        pCloudProviderContainers,
        pListOfTenantClusters,
        Set.of(CloudProvider.FREE, CloudProvider.SERVERLESS, CloudProvider.FLEX));
  }

  private static List<Pair<PlanSummary, Plan>> buildPlanEntryForProjectACME(
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final List<NDSACMECert> pACMECertificatesForTenantClusters,
      final List<CloudProviderContainer> pCloudProviderContainers,
      final List<Cluster> pTenantClusters) {
    final List<Pair<PlanSummary, Plan>> planList = new ArrayList<>();
    final Plan plan = PlanResult.getNewACMEPlan(pNDSGroup.getGroupId());

    final List<Cluster> certificateEligibleTenantClusters =
        NDSClusterSvc.getCertificateEligibleTenantClusters(pTenantClusters);

    final Map<SubdomainLevel, List<Cluster>> clustersBySubdomain =
        NDSACMESvc.groupClustersBySubdomainLevel(certificateEligibleTenantClusters);
    final Map<SubdomainLevel, List<NDSACMECert>> groupCertsBySubdomain =
        NDSACMESvc.groupCertsBySubdomain(
            pACMECertificatesForTenantClusters, plan.getPlanContext().getAppSettings());
    final Map<CloudProvider, List<CloudProviderContainer>> clusterContainersUpdated =
        getContainersUnderUpdate(
            pNDSGroup, pCloudProviderContainers, certificateEligibleTenantClusters);

    final PlannedActionFactory factory = new PlannedActionFactory(plan.getPlanContext());
    for (final Map.Entry<SubdomainLevel, List<Cluster>> clustersByCertProvider :
        clustersBySubdomain.entrySet()) {
      final SubdomainLevel subdomainLevel = clustersByCertProvider.getKey();
      final Map<String, List<Cluster>> clustersByDnsPin =
          NDSACMESvc.groupClustersByDnsPin(
              clustersByCertProvider.getValue(), pNDSGroup.getDNSPin());
      final Map<String, List<NDSACMECert>> certsByDnsPin =
          NDSACMESvc.groupCertsByDnsPin(
              groupCertsBySubdomain.getOrDefault(subdomainLevel, List.of()));

      List<CloudProviderContainer> containersForClusters;
      for (final Map.Entry<String, List<Cluster>> clusterByPin : clustersByDnsPin.entrySet()) {
        final String pin = clusterByPin.getKey();
        final Optional<NDSACMECert> mostRecentlyIssuedCert =
            certsByDnsPin.getOrDefault(pin, List.of()).stream()
                .max(Comparator.comparing(ACMECert::getExpiresAt));

        // when multiple clusters share the same group pin, simply get containers by provider
        if (clusterByPin.getValue().size() > 1) {
          final Set<CloudProvider> clustersBackingProviders =
              clustersByCertProvider.getValue().stream()
                  .map(Cluster::getClusterDescription)
                  .map(c -> c.isTenantCluster() ? c.getBackingProvider() : c.getCloudProvider())
                  .collect(Collectors.toSet());
          containersForClusters =
              clustersBackingProviders.stream()
                  .flatMap(
                      provider ->
                          clusterContainersUpdated.getOrDefault(provider, List.of()).stream())
                  .collect(Collectors.toList());
        } else {
          // find the specific container used by the cluster with unique pin
          final Cluster cluster = clusterByPin.getValue().get(0);
          containersForClusters = getContainersForCluster(pNDSGroup, cluster);
        }

        final boolean needNewCertificate =
            mostRecentlyIssuedCert.isEmpty()
                || mostRecentlyIssuedCert
                    .get()
                    .willExpireWithinWindow(
                        TenantNDSDefaults.ACME_TENANT_CERT_EXPIRY_ROTATION_THRESHOLD)
                || mostRecentlyIssuedCert.get().isNeedsRotation();

        if (needNewCertificate && !certificateEligibleTenantClusters.isEmpty()) {

          final Plan currentPlan = PlanResult.getNewACMEPlan(pNDSGroup.getGroupId());
          final List<PlannedAction> projectActions = new ArrayList<>();

          projectActions.add(
              factory.forNDSACMEIssueTenantCertMove(
                  currentPlan.getPlanContext(), subdomainLevel, pin));

          final Pair<PlanSummary, Plan> buildPlan =
              PlanResult.buildPlanEntry(
                  pLogger,
                  projectActions,
                  Collections.emptyList(),
                  currentPlan,
                  containersForClusters.stream()
                      .map(CloudProviderContainer::getId)
                      .collect(Collectors.toList()),
                  null,
                  null,
                  null,
                  null,
                  null,
                  PlanSummary.ResourceType.PROJECT_ACME_CERTIFICATE,
                  "projectACME");
          if (buildPlan.getLeft() != null) {
            planList.add(buildPlan);
          }
        }
      }
    }
    return planList;
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForFastTenantRecord(
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final ObjectId pContainerId,
      final FastTenantPreAllocatedRecord pFastTenantPreAllocatedRecord) {
    final Plan plan = PlanResult.getNewPlan(pNDSGroup.getGroupId());

    final PlannedActionFactory factory = new PlannedActionFactory(plan.getPlanContext());

    final List<PlannedAction> projectActions;
    if (pFastTenantPreAllocatedRecord.getState()
        == FastTenantPreAllocatedRecord.State.NEEDS_CLEANUP) {
      projectActions =
          factory.forDestroyFastProvision(
              pFastTenantPreAllocatedRecord, pFastTenantPreAllocatedRecord.getFastRecordType());
    } else {
      projectActions =
          factory.forFastProvision(
              (NDSPlanContext) plan.getPlanContext(),
              pFastTenantPreAllocatedRecord,
              pFastTenantPreAllocatedRecord.getFastRecordType());
    }

    return PlanResult.buildPlanEntry(
        pLogger,
        projectActions,
        Collections.emptyList(),
        plan,
        List.of(pContainerId),
        null,
        null,
        null,
        null,
        null,
        ResourceType.FAST_PROVISION,
        "fastProvision");
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForFreeCompaction(
      final Logger pLogger,
      final MTMCompaction pProcessingMTMCompactionForCluster,
      final ClusterDescription pTenantClusterDescription,
      final NDSGroup pNDSGroup) {
    final Plan plan = PlanResult.getNewPlan(pNDSGroup.getGroupId());

    // don't start another compaction for tenant if it's not under compaction, not null also means
    // we are dealing with M0 cluster.
    if (pProcessingMTMCompactionForCluster == null) {
      return Pair.of(null, plan);
    }

    final FreeTenantProviderOptions providerOptions =
        (FreeTenantProviderOptions) pTenantClusterDescription.getFreeTenantProviderOptions();
    // don't start another compaction for tenant if its access is already revoked
    if (providerOptions.getNdsAccessRevokedDate() != null) {
      return Pair.of(null, plan);
    }

    // existing mtmCompaction means the current tenant is under compaction, perform compaction
    final List<PlannedAction> actions = new ArrayList<>();
    final PlannedActionFactory factory = new PlannedActionFactory(plan.getPlanContext());

    final PlannedAction revokeNDSAccessForCluster =
        factory.forRevokeNDSAccessForTenantCluster(pTenantClusterDescription);
    actions.add(revokeNDSAccessForCluster);

    final PlannedAction mtmCompactionForTenant =
        factory.forFreeCompactionMove(pTenantClusterDescription);
    actions.add(mtmCompactionForTenant);

    return PlanResult.buildPlanEntry(
        pLogger,
        actions,
        Collections.emptyList(),
        plan,
        List.of(),
        pTenantClusterDescription.getName(),
        null,
        null,
        null,
        null,
        ResourceType.FREE_COMPACTION,
        "compaction for FREE tenant");
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForFlexMigration(
      final Logger pLogger,
      final ClusterDescription pTenantClusterDescription,
      final List<CloudProviderContainer> pCloudProviderContainers,
      final FlexTenantMigration pFlexTenantMigration,
      final TenantUpgradeStatus pTenantUpgradeStatus,
      final NDSGroup pNDSGroup) {
    final Plan plan = PlanResult.getNewPlan(pNDSGroup.getGroupId());
    final Optional<CloudProvider> tenantCloudProvider =
        pTenantClusterDescription.getCloudProviders().stream()
            .filter(CloudProvider::isTenantProvider)
            .findFirst();
    final Optional<TenantCloudProviderContainer> tenantCloudProviderContainer =
        pCloudProviderContainers.stream()
            .filter(
                c -> c.isProvisioned() && c.getCloudProvider() == tenantCloudProvider.orElse(null))
            .map(TenantCloudProviderContainer.class::cast)
            .findFirst();

    if (!isFlexTenantMigrationPlanGenerationValid(
        pFlexTenantMigration,
        pTenantUpgradeStatus,
        pTenantClusterDescription,
        tenantCloudProviderContainer)) {
      return Pair.of(null, plan);
    }

    final List<PlannedAction> actions = new ArrayList<>();
    final PlannedActionFactory factory = new PlannedActionFactory(plan.getPlanContext());

    final PlannedAction migrationForTenant =
        factory.forFlexMigrationMove(pTenantClusterDescription, pFlexTenantMigration);
    actions.add(migrationForTenant);
    final String formerCloudProvider =
        pTenantClusterDescription
            .getFlexTenantMigrationState()
            .orElseThrow()
            .getFormerCloudProvider()
            .orElseThrow()
            .name();

    return PlanResult.buildPlanEntry(
        pLogger,
        actions,
        Collections.emptyList(),
        plan,
        List.of(),
        pTenantClusterDescription.getName(),
        null,
        null,
        null,
        null,
        ResourceType.FLEX_MIGRATION,
        String.format(
            pFlexTenantMigration.getStatus() == MigrationStatus.MIGRATION_PENDING
                ? "%s to Flex Migration for %s tenant"
                : "%s to Flex Rollback for %s tenant",
            formerCloudProvider,
            pTenantClusterDescription.getName()));
  }

  @VisibleForTesting
  protected static boolean isFlexTenantMigrationPlanGenerationValid(
      final FlexTenantMigration pFlexTenantMigration,
      final TenantUpgradeStatus pTenantUpgradeStatus,
      final ClusterDescription pTenantClusterDescription,
      final Optional<TenantCloudProviderContainer> pTenantCloudProviderContainer) {
    return pFlexTenantMigration != null
        && ((pFlexTenantMigration.getStatus() == MigrationStatus.MIGRATION_PENDING
                && (pTenantClusterDescription.getCloudProviders().equals(Set.of(CloudProvider.FREE))
                    || pTenantClusterDescription
                        .getCloudProviders()
                        .equals(Set.of(CloudProvider.SERVERLESS))))
            || (pFlexTenantMigration.getStatus() == MigrationStatus.ROLLBACK_PENDING
                && pTenantClusterDescription
                    .getCloudProviders()
                    .equals(Set.of(CloudProvider.FLEX))))
        && pTenantUpgradeStatus == null
        && pTenantCloudProviderContainer.isPresent();
  }

  private static Map<CloudProvider, List<CloudProviderContainer>> getContainersUnderUpdate(
      final NDSGroup pNDSGroup,
      final List<CloudProviderContainer> pCloudProviderContainers,
      final List<Cluster> pCertificateEligibleTenantClusters) {
    final Map<CloudProvider, List<CloudProviderContainer>> clusterContainersUpdated;
    if (pNDSGroup.isMTMHolder()) {
      clusterContainersUpdated =
          pCloudProviderContainers.stream()
              .filter(CloudProviderContainer::isProvisioned)
              .collect(Collectors.groupingBy(CloudProviderContainer::getCloudProvider));
    } else {
      clusterContainersUpdated =
          groupTenantContainersByBackingCloudProvider(
              pCloudProviderContainers, pCertificateEligibleTenantClusters);
    }
    return clusterContainersUpdated;
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForServerlessLoadBalancingDeployment(
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final CloudProviderContainer pContainer,
      final ServerlessLoadBalancingDeployment pServerlessLoadBalancingDeployment,
      final List<FastServerlessPreAllocatedRecord> pFastServerlessPreAllocatedRecords,
      final ServerlessMTMPool pServerlessMTMPool,
      final AppSettings pAppSettings,
      final Group pGroup) {
    final Plan plan = getNewPlan(pNDSGroup.getGroupId());

    final PlannedActionFactory serverlessDeploymentActionFactory =
        new PlannedActionFactory(plan.getPlanContext());

    final List<PlannedAction> serverlessLoadBalancingDeploymentActions =
        PlanResult.getServerlessLoadBalancingDeploymentActions(
            pNDSGroup,
            pContainer,
            pServerlessLoadBalancingDeployment,
            pFastServerlessPreAllocatedRecords,
            pServerlessMTMPool,
            serverlessDeploymentActionFactory,
            pLogger,
            pAppSettings,
            pGroup,
            plan.getPlanContext());

    return PlanResult.buildPlanEntry(
        pLogger,
        serverlessLoadBalancingDeploymentActions,
        Collections.emptyList(),
        plan,
        Collections.singletonList(pContainer.getId()),
        null,
        null,
        null,
        null,
        pServerlessLoadBalancingDeployment.getId(),
        ResourceType.SERVERLESS_LOAD_BALANCING_DEPLOYMENT,
        String.format("group %s", pNDSGroup.getGroupId().toString()));
  }

  private static boolean getClusterNeedsConnectivitySyncForUniformFrontend(
      final Cluster pCluster,
      final Group pGroup,
      final AppSettings pAppSettings,
      final Logger pLogger) {
    if (!pCluster.getClusterDescription().getClusterType().isSharded()) {
      return false;
    }

    final boolean isLocalOrDev =
        Set.of(AppEnv.LOCAL, AppEnv.DEV).contains(pAppSettings.getAppEnv());
    if (!isLocalOrDev) {
      return false;
    }

    final boolean isUniformFrontendFeatureFlagEnabled =
        FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_SHARDED_CLUSTERS_BEHIND_UNIFORM_FRONTEND, pAppSettings, null, pGroup);
    if (!isUniformFrontendFeatureFlagEnabled) {
      return false;
    }

    final boolean isProvisioned =
        pCluster.isAllHardwareProvisioned()
            && !pCluster.getClusterDescription().isDeletedOrDeleteRequested();
    final boolean isMissingLoadBalancedHostname =
        pCluster.getClusterDescription().getLoadBalancedHostname().isEmpty();

    pLogger.debug(
        "Cluster connectivity sync required for uniform frontend: {},"
            + "(isProvisioned:{}, isMissingLoadBalancedHostname:{})",
        isProvisioned && isMissingLoadBalancedHostname,
        isProvisioned,
        isMissingLoadBalancedHostname);

    return isProvisioned && isMissingLoadBalancedHostname;
  }

  protected static List<PlannedAction> getTenantEndpointActions(
      final Cluster pCluster,
      final List<TenantEndpoint> pTenantEndpoints,
      final PlannedActionFactory pPlannedActionFactory,
      final Logger pLogger) {
    if (pTenantEndpoints.isEmpty()) {
      pLogger.info(
          "No tenant endpoints present for cluster {}.",
          pCluster.getClusterDescription().getName());
      return Collections.emptyList();
    }

    final Set<ObjectId> endpointIdsNeedingRejection = new HashSet<>();
    final Set<ObjectId> endpointIdsNeedingAcceptance = new HashSet<>();
    final Set<ObjectId> endpointIdsNeedingReservation = new HashSet<>();

    pTenantEndpoints.forEach(
        pTenantEndpoint -> {
          if (pTenantEndpoint.needsDeletion()) {
            endpointIdsNeedingRejection.add(pTenantEndpoint.getId());
          } else if (pTenantEndpoint.needsAcceptance()) {
            endpointIdsNeedingAcceptance.add(pTenantEndpoint.getId());
          } else if (pTenantEndpoint.needsReservation()) {
            endpointIdsNeedingReservation.add(pTenantEndpoint.getId());
          }
        });

    // Note: can't return a PlannedAction with no moves.
    if (endpointIdsNeedingRejection.isEmpty()
        && endpointIdsNeedingAcceptance.isEmpty()
        && endpointIdsNeedingReservation.isEmpty()) {
      return Collections.emptyList();
    }

    return List.of(
        pPlannedActionFactory.forTenantConsumerSyncPrivateEndpoints(
            pCluster.getClusterDescription(),
            endpointIdsNeedingAcceptance,
            endpointIdsNeedingRejection,
            endpointIdsNeedingReservation));
  }

  protected static List<PlannedAction> getTenantEndpointServiceDeploymentActions(
      final CloudProviderContainer pContainer,
      final PlannedActionFactory pPlannedActionFactory,
      final Logger pLogger) {
    if (pContainer.getTenantEndpointServices().isEmpty()) {
      pLogger.info(
          "No tenant endpoint services present for container with ID {}.", pContainer.getId());
      return List.of();
    }

    final Set<ObjectId> endpointServiceIdsNeedingDeletion = new HashSet<>();
    final Set<ObjectId> endpointServiceIdsNeedingCreation = new HashSet<>();

    pContainer
        .getTenantEndpointServices()
        .forEach(
            pEndpointService -> {
              if (pEndpointService.isDeleteRequested()) {
                endpointServiceIdsNeedingDeletion.add(pEndpointService.getId());
              } else if (pEndpointService.getLoadBalancingDeploymentId().isPresent()
                  && pEndpointService.getStatus().equals(Status.INITIATING)) {
                endpointServiceIdsNeedingCreation.add(pEndpointService.getId());
              }
            });

    if (endpointServiceIdsNeedingCreation.isEmpty()
        && endpointServiceIdsNeedingDeletion.isEmpty()) {
      return Collections.emptyList();
    }

    return List.of(
        pPlannedActionFactory.forTenantProducerSyncPrivateEndpointServices(
            pContainer, endpointServiceIdsNeedingCreation, endpointServiceIdsNeedingDeletion));
  }

  protected static List<PlannedAction> getServerlessNATGatewayActions(
      final NDSGroup pNDSGroup,
      final CloudProviderContainer pContainer,
      final ServerlessLoadBalancingDeployment pServerlessLoadBalancingDeployment,
      final ServerlessMTMPool pServerlessMTMPool,
      final PlannedActionFactory pPlannedActionFactory,
      final boolean pCanTearDownDeploymentResources) {
    if (pServerlessLoadBalancingDeployment.getCloudProvider() != CloudProvider.GCP) {
      return List.of();
    }

    final GCPCloudNATGateway cloudNATGateway =
        (GCPCloudNATGateway) pServerlessLoadBalancingDeployment.getCloudNATGateway();

    if (!cloudNATGateway.isProvisioned()) {
      final boolean canProvision =
          pNDSGroup.isServerlessMTMHolder()
              && Optional.ofNullable(pServerlessMTMPool).isPresent()
              && !pServerlessMTMPool.isDeleteRequested()
              && pServerlessLoadBalancingDeployment.getDeleteRequestedDate() == null;
      if (canProvision) {
        return List.of(
            pPlannedActionFactory.forCreateServerlessNATGateway(
                pContainer.getId(),
                pContainer.getCloudProvider(),
                pServerlessLoadBalancingDeployment.getId()));
      }
    }

    final boolean shouldTearDownNATGateway =
        pServerlessLoadBalancingDeployment.isDeleteRequested() && pCanTearDownDeploymentResources;
    if (shouldTearDownNATGateway) {
      return List.of(
          pPlannedActionFactory.forDestroyServerlessNATGateway(
              pContainer.getId(),
              pContainer.getCloudProvider(),
              pServerlessLoadBalancingDeployment.getId()));
    }

    return List.of();
  }

  static List<PlannedAction> getServerlessCloudLoadBalancerActions(
      final NDSGroup pNDSGroup,
      final CloudProviderContainer pContainer,
      final ServerlessMTMPool pServerlessMTMPool,
      final ServerlessLoadBalancingDeployment pServerlessLoadBalancingDeployment,
      final PlannedActionFactory pPlannedActionFactory,
      final boolean pCanTearDownDeploymentResources) {

    final boolean canProvisionCloudLoadBalancer =
        pNDSGroup.isServerlessMTMHolder()
            && Optional.ofNullable(pServerlessMTMPool).isPresent()
            && !pServerlessMTMPool.isDeleteRequested();
    final boolean isCloudLoadBalancerProvisioned =
        pServerlessLoadBalancingDeployment.getCloudProviderLoadBalancer().isProvisioned();

    final boolean shouldProvisionCloudLoadBalancer =
        canProvisionCloudLoadBalancer && !isCloudLoadBalancerProvisioned;
    final boolean shouldDestroyCloudLoadBalancer =
        pServerlessLoadBalancingDeployment.isDeleteRequested()
            && isCloudLoadBalancerProvisioned
            && pCanTearDownDeploymentResources;

    if (shouldProvisionCloudLoadBalancer) {
      return List.of(
          pPlannedActionFactory.forCreateServerlessCloudLoadBalancer(
              pContainer.getId(),
              pContainer.getCloudProvider(),
              pServerlessLoadBalancingDeployment.getId()));
    }

    if (shouldDestroyCloudLoadBalancer) {
      return List.of(
          pPlannedActionFactory.forDestroyServerlessCloudLoadBalancer(
              pContainer.getId(),
              pContainer.getCloudProvider(),
              pServerlessLoadBalancingDeployment.getId()));
    }

    return List.of();
  }

  static List<PlannedAction> getServerlessEnvoyInstanceActions(
      final NDSGroup pNDSGroup,
      final CloudProviderContainer pContainer,
      final ServerlessMTMPool pServerlessMTMPool,
      final ServerlessLoadBalancingDeployment pServerlessLoadBalancingDeployment,
      final PlannedActionFactory pPlannedActionFactory,
      final boolean pCanTearDownDeploymentResources,
      final Logger pLogger,
      final AppSettings pAppSettings,
      final Group pGroup,
      final PlanContext pPlanContext) {

    final List<PlannedAction> envoyInstanceActions =
        pServerlessLoadBalancingDeployment.getEnvoyInstances().stream()
            .map(
                envoyInstance ->
                    getEnvoyInstanceAction(
                        envoyInstance,
                        pServerlessLoadBalancingDeployment,
                        pServerlessMTMPool,
                        pNDSGroup,
                        pContainer,
                        pPlannedActionFactory,
                        pCanTearDownDeploymentResources,
                        pLogger,
                        pAppSettings,
                        pGroup,
                        pPlanContext))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .collect(Collectors.toList());

    final boolean envoyMachineUpdateRequiresWaitForHealthy =
        envoyInstanceActions.stream()
            .anyMatch(
                plannedAction ->
                    plannedAction.getMoveType().equals(MoveType.SERVERLESS_ENVOY)
                        && plannedAction.getActionType().equals(ActionType.UPDATE));

    final List<PlannedAction> waitForHealthyActions = new ArrayList<>();
    if (envoyMachineUpdateRequiresWaitForHealthy) {
      for (final EnvoyInstance envoyInstance :
          pServerlessLoadBalancingDeployment.getEnvoyInstances()) {

        // skip any envoy instances that are not desired
        if (!pServerlessLoadBalancingDeployment.isEnvoyInstanceDesired(envoyInstance)) {
          continue;
        }

        final boolean isEnvoyInstanceUndergoingUpdate =
            envoyInstance.isReplacementRequested()
                || !StringUtils.equalsIgnoreCase(
                    envoyInstance.getInstanceSize().map(InstanceSize::name).orElse(null),
                    pServerlessLoadBalancingDeployment.getDesiredInstanceSize())
                || envoyInstance.getChefConfigNeedsUpdateAfterDate().isPresent();

        // skip any envoy instances that are undergoing updates that will already check for healthy
        if (isEnvoyInstanceUndergoingUpdate) {
          continue;
        }

        waitForHealthyActions.add(
            pPlannedActionFactory.forWaitForHealthyEnvoyInstance(
                envoyInstance.getId(),
                pServerlessLoadBalancingDeployment.getCloudProvider(),
                pServerlessLoadBalancingDeployment.getId()));
      }
    }

    return Stream.concat(envoyInstanceActions.stream(), waitForHealthyActions.stream())
        .collect(Collectors.toList());
  }

  static Optional<PlannedAction> getEnvoyInstanceAction(
      final EnvoyInstance pEnvoyInstance,
      final ServerlessLoadBalancingDeployment pServerlessLoadBalancingDeployment,
      final ServerlessMTMPool pServerlessMTMPool,
      final NDSGroup pNDSGroup,
      final CloudProviderContainer pContainer,
      final PlannedActionFactory pPlannedActionFactory,
      final boolean pCanTearDownDeploymentResources,
      final Logger pLogger,
      final AppSettings pAppSettings,
      final Group pGroup,
      final PlanContext pPlanContext) {

    final boolean isEnvoyInstanceDesired =
        pServerlessLoadBalancingDeployment.isEnvoyInstanceDesired(pEnvoyInstance);

    if (!pEnvoyInstance.isProvisioned()) {
      if (pServerlessLoadBalancingDeployment.isDeleteRequested()) {
        pLogger.debug(
            "Envoy instance {} in deployment {} and group {} is not provisioned and deployment is"
                + " going to be deleted; no action needed.",
            pEnvoyInstance.getId(),
            pServerlessLoadBalancingDeployment.getId(),
            pNDSGroup.getGroupId());
        return Optional.empty();
      }

      final boolean canProvisionEnvoyInstances =
          pNDSGroup.isServerlessMTMHolder()
              && Optional.ofNullable(pServerlessMTMPool).isPresent()
              && !pServerlessMTMPool.isDeleteRequested();
      if (!canProvisionEnvoyInstances) {
        pLogger.debug(
            "Envoy instance {} in deployment {} and group {} is not provisioned. NDS group is not a"
                + " serverless MTM holder or the serverless pool does not exist; no action needed.",
            pEnvoyInstance.getId(),
            pServerlessLoadBalancingDeployment.getId(),
            pNDSGroup.getGroupId());
        return Optional.empty();
      }

      if (!isEnvoyInstanceDesired) {
        pLogger.debug(
            "Envoy instance {} in deployment {} and group {} is not provisioned and it is not"
                + " desired; no action needed.",
            pEnvoyInstance.getId(),
            pServerlessLoadBalancingDeployment.getId(),
            pNDSGroup.getGroupId());
        return Optional.empty();
      }

      pLogger.info(
          "Envoy instance {} in deployment {} and group {} needs to be provisioned.",
          pEnvoyInstance.getId(),
          pServerlessLoadBalancingDeployment.getId(),
          pNDSGroup.getGroupId());
      return Optional.of(
          pPlannedActionFactory.forCreateServerlessEnvoyInstance(
              pEnvoyInstance.getId(),
              pContainer.getId(),
              pServerlessLoadBalancingDeployment.getId(),
              pServerlessLoadBalancingDeployment.getCloudProvider()));
    }

    final boolean shouldDestroyLoadBalancingDeployment =
        pServerlessLoadBalancingDeployment.isDeleteRequested() && pCanTearDownDeploymentResources;

    if (shouldDestroyLoadBalancingDeployment) {
      pLogger.info(
          "Deployment {} in group {} is being deleted. Deleting envoy instance {}.",
          pServerlessLoadBalancingDeployment.getId(),
          pNDSGroup.getGroupId(),
          pEnvoyInstance.getId());
      return Optional.of(
          pPlannedActionFactory.forDestroyServerlessEnvoyInstance(
              pEnvoyInstance.getId(),
              pContainer.getId(),
              pServerlessLoadBalancingDeployment.getId(),
              pServerlessLoadBalancingDeployment.getCloudProvider()));
    } else if (!isEnvoyInstanceDesired) {
      pLogger.info(
          "Envoy instance {} in deployment {} and group {} is no longer desired and will be"
              + " deleted.",
          pEnvoyInstance.getId(),
          pServerlessLoadBalancingDeployment.getId(),
          pNDSGroup.getGroupId());
      return Optional.of(
          pPlannedActionFactory.forDestroyServerlessEnvoyInstance(
              pEnvoyInstance.getId(),
              pContainer.getId(),
              pServerlessLoadBalancingDeployment.getId(),
              pServerlessLoadBalancingDeployment.getCloudProvider()));
    }

    final boolean shouldUpdateEnvoyInstanceSize =
        !StringUtils.equalsIgnoreCase(
            pEnvoyInstance.getInstanceSize().map(InstanceSize::name).orElse(null),
            pServerlessLoadBalancingDeployment.getDesiredInstanceSize());

    if (pEnvoyInstance.isReplacementRequested()) {
      pLogger.info(
          "Envoy instance {} in deployment {} and group {} needs replacement and will be replaced.",
          pEnvoyInstance.getId(),
          pServerlessLoadBalancingDeployment.getId(),
          pNDSGroup.getGroupId());
      return Optional.of(
          pPlannedActionFactory.forUpdateServerlessEnvoyInstance(
              pEnvoyInstance.getId(),
              pContainer.getId(),
              pServerlessLoadBalancingDeployment.getId(),
              pServerlessLoadBalancingDeployment.getCloudProvider()));
    } else if (shouldUpdateEnvoyInstanceSize) {
      pLogger.info(
          "Envoy instance {} in deployment {} and group {} no longer matches desired hardware and"
              + " will be updated.",
          pEnvoyInstance.getId(),
          pServerlessLoadBalancingDeployment.getId(),
          pNDSGroup.getGroupId());
      return Optional.of(
          pPlannedActionFactory.forUpdateServerlessEnvoyInstance(
              pEnvoyInstance.getId(),
              pContainer.getId(),
              pServerlessLoadBalancingDeployment.getId(),
              pServerlessLoadBalancingDeployment.getCloudProvider()));
    }

    if (shouldUpdateOSPolicyAndReboot(
        pServerlessLoadBalancingDeployment, pEnvoyInstance, pAppSettings, pGroup)) {
      pLogger.info(
          "Envoy instance {} in deployment {} and group {} needs to update OS policy and reboot.",
          pEnvoyInstance.getId(),
          pServerlessLoadBalancingDeployment.getId(),
          pNDSGroup.getGroupId());
      return Optional.of(
          pPlannedActionFactory.forUpdateEnvoyInstanceOSPolicy(
              pPlanContext, pEnvoyInstance, pServerlessLoadBalancingDeployment));
    }

    if (pEnvoyInstance.getRebootRequestedDate().isPresent()) {
      pLogger.info(
          "Envoy instance {} in deployment {} and group {} needs to be rebooted and will be"
              + " rebooted.",
          pEnvoyInstance.getId(),
          pServerlessLoadBalancingDeployment.getId(),
          pNDSGroup.getGroupId());
      return Optional.of(
          pPlannedActionFactory.forRestartEnvoyServer(
              pEnvoyInstance.getId(),
              pContainer.getId(),
              pServerlessLoadBalancingDeployment.getId(),
              pServerlessLoadBalancingDeployment.getCloudProvider()));
    }

    if (pEnvoyInstance.getChefConfigNeedsUpdateAfterDate().isPresent()) {
      pLogger.info(
          "Envoy instance {} in deployment {} and group {} needs chef config to be updated.",
          pEnvoyInstance.getId(),
          pServerlessLoadBalancingDeployment.getId(),
          pNDSGroup.getGroupId());
      return Optional.of(
          pPlannedActionFactory.forUpdateEnvoyChefConfig(
              pEnvoyInstance.getId(),
              pServerlessLoadBalancingDeployment.getId(),
              pContainer.getId(),
              pServerlessLoadBalancingDeployment.getCloudProvider()));
    }

    pLogger.debug(
        "Envoy instance {} in deployment {} and group {} is provisioned; no action needed.",
        pEnvoyInstance.getId(),
        pServerlessLoadBalancingDeployment.getId(),
        pNDSGroup.getGroupId());
    return Optional.empty();
  }

  static boolean shouldUpdateOSPolicyAndReboot(
      final ServerlessLoadBalancingDeployment pServerlessLoadBalancingDeployment,
      final EnvoyInstance pEnvoyInstance,
      final AppSettings pAppSettings,
      final Group pGroup) {
    return pServerlessLoadBalancingDeployment
            .getOptionalDesiredEnvoyInstanceOSPolicyVersion()
            .isPresent()
        && !pServerlessLoadBalancingDeployment
            .getOptionalDesiredEnvoyInstanceOSPolicyVersion()
            .equals(pEnvoyInstance.getAppliedOSPolicyVersion());
  }

  protected static List<PlannedAction> getServerlessLoadBalancingDeploymentActions(
      final NDSGroup pNDSGroup,
      final CloudProviderContainer pContainer,
      final ServerlessLoadBalancingDeployment pServerlessLoadBalancingDeployment,
      final List<FastServerlessPreAllocatedRecord> pFastServerlessPreAllocatedRecords,
      final ServerlessMTMPool pServerlessMTMPool,
      final PlannedActionFactory pPlannedActionFactory,
      final Logger pLogger,
      final AppSettings pAppSettings,
      final Group pGroup,
      final PlanContext pPlanContext) {
    if (pServerlessLoadBalancingDeployment.getCloudProvider().isTenantProvider()) {
      throw new IllegalArgumentException("Tenant cloud provider is not supported.");
    }

    final boolean canTearDownDeploymentResources =
        pFastServerlessPreAllocatedRecords.isEmpty()
            && !hasActiveTenantEndpointServiceDeployment(pContainer);

    final List<PlannedAction> plannedActions = new ArrayList<>();

    plannedActions.addAll(
        getServerlessNATGatewayActions(
            pNDSGroup,
            pContainer,
            pServerlessLoadBalancingDeployment,
            pServerlessMTMPool,
            pPlannedActionFactory,
            canTearDownDeploymentResources));

    plannedActions.addAll(
        getServerlessCloudLoadBalancerActions(
            pNDSGroup,
            pContainer,
            pServerlessMTMPool,
            pServerlessLoadBalancingDeployment,
            pPlannedActionFactory,
            canTearDownDeploymentResources));

    plannedActions.addAll(
        getServerlessEnvoyInstanceActions(
            pNDSGroup,
            pContainer,
            pServerlessMTMPool,
            pServerlessLoadBalancingDeployment,
            pPlannedActionFactory,
            canTearDownDeploymentResources,
            pLogger,
            pAppSettings,
            pGroup,
            pPlanContext));

    return plannedActions;
  }

  private static boolean hasActiveTenantEndpointServiceDeployment(
      final CloudProviderContainer pContainer) {
    if (pContainer.getTenantEndpointServiceDeployment().isEmpty()) {
      return false;
    }

    final TenantEndpointServiceDeployment tenantEndpointServiceDeployment =
        pContainer.getTenantEndpointServiceDeployment().orElseThrow();
    return tenantEndpointServiceDeployment.getDeleteRequestedDate() == null
        || (tenantEndpointServiceDeployment.getDeleteRequestedDate() != null
            && !tenantEndpointServiceDeployment.getEndpointServices().isEmpty());
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForContainer(
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<Cluster> pClusters,
      final Set<RegionName> pRegionsWithSnapshotsAndSnapshotDistribution,
      final CloudProviderContainer pContainer,
      final AppSettings pAppSettings,
      final List<ServerlessLoadBalancingDeployment> pServerlessLoadBalancingDeployments,
      final ServerlessMTMPool pServerlessMTMPool,
      final List<BackupJob> pBackupJobs,
      final List<BackupRestoreJobPlanUnit> pRestoreJobs,
      final List<CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>>
          pHeldAWSCapacityRequests,
      final List<CheckCapacityRequest<AzureInstanceCapacitySpec, AzureCheckResult>>
          pActiveAzureCapacityRequests,
      final Set<ObjectId> pContainersWithActiveDedicatedSearch,
      final boolean pHasStreamsPrivateNetworking,
      final List<ServerlessUpgradeToDedicatedStatus> pServerlessUpgradeToDedicatedStatuses,
      final List<AzureCapacityDenyListEntry> pAzureDenylistEntriesNeedingCapacityCheck) {
    final Plan plan = getNewPlan(pNDSGroup.getGroupId());
    final List<PlannedAction> containerActions = new ArrayList<>();
    final PlannedActionFactory containerActionFactory =
        new PlannedActionFactory(plan.getPlanContext());

    containerActions.addAll(
        PlanResult.getContainerActions(
            pAppSettings,
            pNDSGroup,
            pGroup,
            pClusters,
            pRegionsWithSnapshotsAndSnapshotDistribution,
            containerActionFactory,
            pContainer,
            pServerlessLoadBalancingDeployments,
            pServerlessMTMPool,
            pBackupJobs,
            pRestoreJobs,
            pHeldAWSCapacityRequests,
            pActiveAzureCapacityRequests,
            pContainersWithActiveDedicatedSearch,
            pHasStreamsPrivateNetworking,
            pServerlessUpgradeToDedicatedStatuses,
            pAzureDenylistEntriesNeedingCapacityCheck,
            pLogger));
    return PlanResult.buildPlanEntry(
        pLogger,
        containerActions,
        Collections.emptyList(),
        plan,
        Collections.singletonList(pContainer.getId()),
        null,
        null,
        null,
        null,
        null,
        PlanSummary.ResourceType.CONTAINER,
        String.format("containers %s", pContainer.getId().toString()));
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForConcurrentSnapshot(
      final Logger pLogger,
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final List<CloudProviderContainer> pContainers,
      final BackupRestoreJobPlanUnit pRestoreTarget,
      final CpsSnapshotEngine pCpsSnapshotEngine) {
    final Plan plan = getNewPlan(pNDSGroup.getGroupId());
    final PlannedActionFactory actionFactory = new PlannedActionFactory(plan.getPlanContext());

    final List<PlannedAction> actions = new ArrayList<>();
    final List<AuditDescription> descriptions = new ArrayList<>();

    final Pair<List<PlannedAction>, List<AuditDescription>> diskBackupResult =
        PlanResult.getDiskBackupActionForCluster(
            pCluster.getClusterDescription(),
            actionFactory,
            pCpsSnapshotEngine,
            pGroup,
            pNDSGroup,
            PlanningType.CONCURRENT,
            pLogger);

    if (diskBackupResult != null) {
      actions.addAll(diskBackupResult.getLeft());
      descriptions.addAll(diskBackupResult.getRight());
    }

    final RestoreSummary summary = RestoreSummaryFactory.getRestoreSummary(pRestoreTarget);

    return PlanResult.buildPlanEntry(
        pLogger,
        actions,
        descriptions,
        plan,
        pContainers.stream().map(CloudProviderContainer::getId).collect(Collectors.toList()),
        pCluster.getClusterDescription().getName(),
        null, // intentionally no restoreJobId on cluster plans
        summary,
        null,
        null,
        ResourceType.CONCURRENT_SNAPSHOT,
        String.format("snapshot for cluster %s", pCluster.getClusterDescription().getName()));
  }

  /**
   * Handles planning for retained backupJobs. For cluster deletion, skip planning if active
   * snapshots exist, otherwise if no active snapshots exist, terminate the retaining backupJob.
   *
   * @param pBackupJob retained backupJob
   * @param pCpsSnapshotEngine contains references to clusterDescription and backupSnapshotDao
   * @return no plan for active snapshots, DeleteBackupConfig for no active snapshots, DiskSnapshot
   *     for resurrect
   */
  private static Pair<PlanSummary, Plan> buildPlanEntryForRetainedBackupJob(
      final BackupJob pBackupJob,
      final CpsSnapshotEngine pCpsSnapshotEngine,
      final Logger pLogger) {
    final ObjectId groupId = pBackupJob.getProjectId();
    final String clusterName = pBackupJob.getClusterName();

    final Plan plan = getNewPlan(groupId);
    final PlannedActionFactory actionFactory = new PlannedActionFactory(plan.getPlanContext());

    final Optional<ClusterDescription> clusterDescriptionOpt =
        pCpsSnapshotEngine
            .getCpsSvc()
            .getClusterSvc()
            .getClusterDescriptionIncludingDeleted(groupId, clusterName);
    if (clusterDescriptionOpt.isEmpty()) {
      pLogger.error("Cluster is missing for retained backup job.");
      return Pair.of(null, plan);
    }
    final ClusterDescription clusterDescription = clusterDescriptionOpt.get();

    if (!clusterDescription.isDeleteRequested()
        && clusterDescription.getResurrectRequested().isEmpty()) {
      pLogger.error(
          "Cluster is not deleted or resurrecting for retained backupJob for {}", clusterName);
      return Pair.of(null, plan);
    }

    final BackupSnapshotDao snapshotDao = pCpsSnapshotEngine.getCpsSvc().getBackupSnapshotDao();
    final Optional<BackupSnapshot> activeSnapshot =
        snapshotDao.findOldestCompletedActiveByCluster(groupId, pBackupJob.getClusterUniqueId());
    if (activeSnapshot.isPresent()) {
      pLogger.info("Retained backupjob for {} has active snapshots, skip planning", clusterName);
      return Pair.of(null, plan);
    }

    // BackupJob is in retaining state and no active snapshots, need to terminate it
    final PlannedAction plannedAction = actionFactory.forDeleteBackupConfig(clusterDescription);

    return PlanResult.buildPlanEntry(
        pLogger,
        List.of(plannedAction),
        List.of(),
        plan,
        List.of(),
        clusterName,
        null, // intentionally no restoreJobId on cluster plans
        null,
        null,
        null,
        ResourceType.BACKUPJOB,
        String.format("retained backupJob for %s", clusterName));
  }

  private enum ExpectedPlanActionType {
    MATERIAL_CLUSTER_UPDATE, // Expected to significantly affect how a cluster operates - see
    // https://jira.mongodb.org/browse/CLOUDP-228035
    OTHER // Not expected to significantly affect how a cluster operates
  }

  /**
   * Checks if the sharded cluster's running FCV is different from the target FCV in the
   * ClusterDescription. The method returns true if any host in the cluster matches the condition.
   *
   * @param pHostLastPingSvc
   * @param pGroupId
   * @param pClusterDescription
   * @param pAutomationConfig
   * @return returns true if any host in the cluster has a different FCV than the target
   */
  @VisibleForTesting
  protected static boolean isShardedClusterFCVUpdated(
      final HostLastPingSvc pHostLastPingSvc,
      final ObjectId pGroupId,
      final ClusterDescription pClusterDescription,
      final AutomationConfig pAutomationConfig) {

    if (!pClusterDescription.getClusterType().isSharded() || pAutomationConfig == null) {
      return false;
    }

    final List<String> hostIds =
        pAutomationConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getCluster().equals(pClusterDescription.getDeploymentClusterName()))
            .filter(Process::isMongod)
            .limit(5) // for performance reasons, only check 5 hosts at a maximum
            .map(p -> HostUtils.assembleHostId(pGroupId, p.getHostname(), p.getPort()))
            .toList();

    if (hostIds.isEmpty()) {
      return false;
    }

    final Version targetVersion =
        VersionUtils.parse(pClusterDescription.getFeatureCompatibilityVersion());

    for (final String hostId : hostIds) {
      final BasicBSONObject lastPing = pHostLastPingSvc.getLastPing(hostId);
      final Optional<String> mongoDbFcv =
          Optional.ofNullable(lastPing)
              .map(ping -> (BasicBSONObject) ping.get(Ping.ALL_PARAMETERS.field))
              .map(
                  allParameters ->
                      (BasicBSONObject) allParameters.get(Ping.FEATURE_COMPATIBILITY_VERSION.field))
              .map(fcvWrapper -> fcvWrapper.getString(Ping.VERSION.field));

      if (mongoDbFcv.isEmpty()) {
        continue;
      }

      if (!VersionUtils.parse(mongoDbFcv.get())
          .getMajorVersion()
          .equals(targetVersion.getMajorVersion())) {
        return true;
      }
    }
    return false;
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForCluster(
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final AutomationConfig pAutomationConfig,
      final List<Host> pHosts,
      final List<HostCluster> pHostClusters,
      final List<CloudProviderContainer> pContainers,
      final BackupJob pBackupJob,
      final BackupRestoreJobPlanUnit pRestoreTarget,
      final TenantRestore pTenantRestore,
      final Cluster pCluster,
      final CpsSnapshotEngine pCpsSnapshotEngine,
      final List<? extends PrivateLinkConnectionRule> pPrivateLinkConnectionRules,
      final List<BaseMultiTargetConnectionRule> pMultiTargetConnectionRules,
      final List<TenantEndpoint> pTenantEndpoints,
      final ClusterOutageSimulation pClusterOutageSimulation,
      final AppSettings pAppSettings,
      final Map<ObjectId, ClusterDescriptionProcessArgs> pProcessArgsMappedByCluster,
      final Builder pClusterPlanningSummaryBuilder,
      final FlexTenantMigration pFlexTenantMigration,
      final Map<ObjectId, CloudProviderAvailability> pContainerIdToAvailabilityMap,
      final HostLastPingSvc pHostLastPingSvc) {
    final Plan plan = getNewPlan(pNDSGroup.getGroupId());
    final PlannedActionFactory actionFactory = new PlannedActionFactory(plan.getPlanContext());
    final List<AuditDescription> descriptions = new ArrayList<>();
    final boolean isPrioritizePrimaryEnabled =
        isFeatureFlagEnabled(
            FeatureFlag.ATLAS_PRIORITIZE_HAVING_PRIMARY_ON_UPSCALED_NODE,
            pAppSettings,
            null,
            pGroup);

    final Set<CloudProvider> cloudProvidersWithAZFF =
        Stream.of(
                Pair.of(CloudProvider.AWS, FeatureFlag.AWS_CAPACITY_AWARE_AZ_SELECTION),
                Pair.of(CloudProvider.AZURE, FeatureFlag.AZURE_CAPACITY_AWARE_AZ_SELECTION),
                Pair.of(CloudProvider.GCP, FeatureFlag.GCP_CAPACITY_AWARE_AZ_SELECTION))
            .filter(pair -> isFeatureFlagEnabled(pair.getRight(), pAppSettings, null, pGroup))
            .map(Pair::getLeft)
            .collect(Collectors.toSet());

    final boolean reduceShutdownTimeEnabled =
        isFeatureFlagEnabled(ATLAS_AUTOHEAL_REDUCE_SHUTDOWN_TIME, pAppSettings, null, pGroup);
    final boolean reduceShutdownTimeHealRepairEnabled =
        isFeatureFlagEnabled(
            ATLAS_AUTOHEAL_REDUCE_SHUTDOWN_TIME_HEAL_REPAIR, pAppSettings, null, pGroup);

    final List<PlannedAction> actions = new ArrayList<>();
    final Pair<List<PlannedAction>, Boolean> machineActionsAndBaaSCategorization =
        PlanResult.getMachineActions(
            pHosts,
            pHostClusters,
            pCluster,
            pNDSGroup,
            actionFactory,
            pLogger,
            pAutomationConfig,
            isPrioritizePrimaryEnabled,
            pProcessArgsMappedByCluster,
            pClusterPlanningSummaryBuilder.getMachineActionsDecisionsBuilder(),
            pContainerIdToAvailabilityMap,
            cloudProvidersWithAZFF,
            reduceShutdownTimeEnabled,
            reduceShutdownTimeHealRepairEnabled);
    actions.addAll(machineActionsAndBaaSCategorization.getLeft());
    final boolean isChangeMaterialToBaas = machineActionsAndBaaSCategorization.getRight();

    // Add container provisioning actions for Free Tenant clusters when the feature is enabled
    addFreeTierContainerProvisioningActions(
        pAppSettings,
        pCluster,
        pContainers,
        pNDSGroup,
        machineActionsAndBaaSCategorization.getLeft(),
        actionFactory,
        actions);

    final Pair<List<PlannedAction>, List<AuditDescription>> backupActions =
        PlanResult.getBackupActions(
            pGroup,
            pNDSGroup,
            pCluster,
            pHostClusters,
            pBackupJob,
            actionFactory,
            pCpsSnapshotEngine,
            pAutomationConfig,
            pAppSettings,
            pLogger,
            // need to call after getMachineActions, including forCreateMachine, forDestroyMachine,
            // forPauseMachine
            actions.stream()
                .anyMatch(PlannedAction::getNeedsEnsureConnectivityForClusterTopologyUpdate));
    actions.addAll(backupActions.getLeft());
    descriptions.addAll(backupActions.getRight());

    actions.addAll(
        PlanResult.getRestoreActions(
            pNDSGroup,
            pCluster,
            pRestoreTarget,
            actionFactory,
            pCpsSnapshotEngine,
            pFlexTenantMigration,
            pLogger));
    actions.addAll(
        PlanResult.getTenantRestoreActions(
            pNDSGroup, pCluster, pTenantRestore, actionFactory, pLogger, pFlexTenantMigration));

    final boolean removeIcmpPing =
        isFeatureFlagEnabled(ATLAS_AUTOHEAL_REMOVE_ICMP_PING, pAppSettings, null, pGroup);
    actions.addAll(
        PlanResult.getBiConnectorActions(
            pNDSGroup, pCluster, pHostClusters, actionFactory, pLogger, removeIcmpPing));
    actions.addAll(
        PlanResult.getPrivateLinkActions(
            pGroup,
            pNDSGroup,
            pAutomationConfig,
            pPrivateLinkConnectionRules,
            pCluster,
            pMultiTargetConnectionRules,
            actionFactory,
            pLogger,
            pAppSettings));
    actions.addAll(
        PlanResult.getClusterOutageSimulationActions(
            pCluster, pClusterOutageSimulation, actionFactory));
    actions.addAll(PlanResult.getTransitionConfigServerActions(pCluster, actionFactory));

    if (pCluster.getClusterDescription().isServerlessTenantCluster()
        && NDSSettings.getSupportedCloudProvidersForServerlessPrivateNetworking(pAppSettings)
            .contains(pCluster.getClusterDescription().getBackingProvider())) {
      actions.addAll(
          PlanResult.getTenantEndpointActions(pCluster, pTenantEndpoints, actionFactory, pLogger));
    }

    if (PlanResult.clusterNeedsPriorityReset(pCluster, pGroup, pAppSettings)) {
      actions.add(
          actionFactory.forResetPrimaryPriority(
              plan.getPlanContext(), pCluster.getClusterDescription()));
    }

    // Check if all the actions so far are scaling
    // All actions added before this line will impact scaling determination
    // Any actions added later (e.g., config actions) will not impact scaling determination
    final boolean isScalingPlan = actionsExistAndAreScalingOnly(actions);
    final boolean isUpscalingPlan = areScalingActionsUpscalingOnly(actions) && isScalingPlan;

    final List<String> excludedAnalyticsNodesHostnames =
        getExcludedAnalyticsNodesHostnames(pCluster, isScalingPlan);

    final boolean allowAbandoningWaitForMachineHealthyMove = isScalingPlan;
    final boolean needsDefaultRWConcernManagementFlagFlip =
        pCluster.getClusterDescription().getAlwaysManagedDefaultRWConcernSince().isEmpty()
            && isFeatureFlagEnabled(
                FeatureFlag.ATLAS_ALWAYS_MANAGED_DEFAULT_RW_CONCERN, pAppSettings, null, pGroup);
    final boolean isShardedClusterFCVUpdate =
        isShardedClusterFCVUpdated(
            pHostLastPingSvc, pGroup.getId(), pCluster.getClusterDescription(), pAutomationConfig);

    final PlannedAction mongoDBConfigAction =
        actionFactory.forSyncMongoDBConfigForCluster(
            pCluster.getClusterDescription(),
            excludedAnalyticsNodesHostnames,
            isUpscalingPlan,
            needsDefaultRWConcernManagementFlagFlip,
            isShardedClusterFCVUpdate);

    final boolean clusterNeedsPublish =
        PlanResult.clusterNeedsPublish(pCluster, mongoDBConfigAction);

    final boolean actionsNeedPublish =
        actions.stream().anyMatch(PlannedAction::getNeedsMongoDBConfigPublish);
    final boolean actionsNeedEnsureConnectivityForTopologyUpdate =
        actions.stream()
            .anyMatch(PlannedAction::getNeedsEnsureConnectivityForClusterTopologyUpdate);
    final boolean m0ClusterNeedsNDSAccessRevoked =
        PlanResult.m0ClusterNeedsNDSAccessRevoked(pCluster.getClusterDescription());
    final boolean isClusterConnectivityDateInPast =
        pCluster.getClusterDescription().getEnsureClusterConnectivityAfter().isPresent()
            && pCluster
                .getClusterDescription()
                .getEnsureClusterConnectivityAfter()
                .get()
                .before(new Date());
    final boolean needsMongoDbConfigAction = (clusterNeedsPublish || actionsNeedPublish);

    if (m0ClusterNeedsNDSAccessRevoked) {
      // revoke NDS access for cluster move is a light wrapper around Free process config per
      // cluster move - it's safe to prioritize this move over a regular publish
      final PlannedAction revokeNDSAccessForCluster =
          actionFactory.forRevokeNDSAccessForTenantCluster(pCluster.getClusterDescription());
      actions.add(revokeNDSAccessForCluster);
    } else if (needsMongoDbConfigAction) {
      actions.add(mongoDBConfigAction);
    }

    final boolean actionsNeedUpdateClusterMongotuneConfig =
        actions.stream().anyMatch(PlannedAction::needsUpdateClusterMongotuneConfig);
    final boolean clusterNeedsMongotuneConfigUpdate =
        pCluster.getClusterDescription().getNeedsMongotuneConfigPublishAfter().isPresent()
            && pCluster
                .getClusterDescription()
                .getNeedsMongotuneConfigPublishAfter()
                .get()
                .before(new Date());
    if (actionsNeedUpdateClusterMongotuneConfig || clusterNeedsMongotuneConfigUpdate) {
      pLogger.info(
          "Add plannedAction to update cluster {} Mongotune config due to:"
              + "actionsNeedUpdateClusterMongotuneConfig: {}, clusterNeedsMongotuneConfigUpdate {}",
          pCluster.getClusterDescription().getName(),
          actionsNeedUpdateClusterMongotuneConfig,
          clusterNeedsMongotuneConfigUpdate);
      final PlannedAction needsUpdateClusterMongotuneConfigAction =
          actionFactory.forUpdateClusterMongotuneConfig(pCluster.getClusterDescription());
      actions.add(needsUpdateClusterMongotuneConfigAction);
    }

    final boolean clusterNeedsConnectivitySyncforUniformFrontend =
        getClusterNeedsConnectivitySyncForUniformFrontend(pCluster, pGroup, pAppSettings, pLogger);

    if (actionsNeedEnsureConnectivityForTopologyUpdate
        || isClusterConnectivityDateInPast
        || clusterNeedsConnectivitySyncforUniformFrontend) {
      final PlannedAction clusterEnsureConnectivityAction =
          actionFactory.forEnsureConnectivityForClusterTopologyUpdate(
              plan.getPlanContext(), pCluster.getClusterDescription().getName());
      actions.add(clusterEnsureConnectivityAction);
    }

    final RestoreSummary summary = RestoreSummaryFactory.getRestoreSummary(pRestoreTarget);

    if (pCluster.getClusterDescription().getNeedsSampleDataLoadAfter() != null
        && pCluster.getClusterDescription().getNeedsSampleDataLoadAfter().before(new Date())) {
      final PlannedAction enqueueLoadSampleDatasetAction =
          new PlannedAction.Builder()
              .moves(
                  new Move[] {
                    EnqueueSampleDatasetLoadMove.factoryCreate(
                        plan.getPlanContext(), pCluster.getClusterDescription().getName())
                  })
              .moveType(MoveType.ENQUEUE_LOAD_SAMPLE_DATASET)
              .actionType(ActionType.UPDATE)
              .clusterName(pCluster.getClusterDescription().getName())
              .build();

      actions.add(enqueueLoadSampleDatasetAction);
    }

    final boolean chainPauseMoves =
        isFeatureFlagEnabled(FeatureFlag.ATLAS_CHAIN_PAUSE_MOVES, pAppSettings, null, pGroup);

    if (pCluster.getClusterDescription().needsToConfigureDisaggregatedStorage()) {
      actions.add(
          actionFactory.forConfigureDisaggregatedStorageCluster(
              plan.getPlanContext(), pCluster.getClusterDescription().getName()));
    }

    return PlanResult.buildPlanEntry(
        pLogger,
        actions,
        descriptions,
        plan,
        pContainers.stream().map(CloudProviderContainer::getId).collect(Collectors.toList()),
        pCluster.getClusterDescription().getName(),
        null, // intentionally no restoreJobId on cluster plans
        summary,
        null,
        null,
        PlanSummary.ResourceType.CLUSTER,
        String.format("cluster %s", pCluster.getClusterDescription().getName()),
        Optional.of(pClusterPlanningSummaryBuilder),
        isChangeMaterialToBaas,
        allowAbandoningWaitForMachineHealthyMove,
        chainPauseMoves);
  }

  @VisibleForTesting
  static List<String> getExcludedAnalyticsNodesHostnames(
      final Cluster pCluster, final boolean pAreAllActionsScaling) {
    if (!pAreAllActionsScaling) {
      return Collections.emptyList();
    }

    // Assumes URI hosts list is up-to-date.
    final List<String> analyticsNodeHostnames =
        pCluster.getHostnameForAgentsToInstanceHardwareMap().keySet().stream()
            .filter(
                hostname ->
                    pCluster
                        .getNodeTypeForHostname(hostname)
                        .equals(Optional.of(NodeType.ANALYTICS)))
            .toList();
    return analyticsNodeHostnames;
  }

  @VisibleForTesting
  static boolean actionsExistAndAreScalingOnly(final List<PlannedAction> pActions) {
    return pActions.stream().allMatch(action -> action.getReplicaSetScalingStrategy().isPresent())
        && !pActions.isEmpty();
  }

  @VisibleForTesting
  static boolean areScalingActionsUpscalingOnly(final List<PlannedAction> pActions) {
    final Set<Boolean> scalingCategorizations;
    scalingCategorizations =
        pActions.stream()
            .map(PlannedAction::getIsUpscaling)
            .flatMap(Optional::stream)
            .collect(Collectors.toUnmodifiableSet());
    return scalingCategorizations.equals(Set.of(true));
  }

  private static List<PlannedAction> getClusterOutageSimulationActions(
      final Cluster pCluster,
      final ClusterOutageSimulation pClusterOutageSimulation,
      final PlannedActionFactory actionFactory) {

    if (pClusterOutageSimulation == null) {
      return List.of();
    } else if (pClusterOutageSimulation.getState().isStartRequested()) {
      return List.of(
          actionFactory.forStartClusterOutageSimulation(
              pCluster.getClusterDescription(), pClusterOutageSimulation));
    } else if (pClusterOutageSimulation.getState().isRecoveryRequested()) {
      return List.of(
          actionFactory.forEndClusterOutageSimulation(
              pCluster.getClusterDescription(), pClusterOutageSimulation));
    }

    return List.of();
  }

  static List<PlannedAction> getTransitionConfigServerActions(
      final Cluster pCluster, final PlannedActionFactory actionFactory) {
    if (pCluster.getReplicaSetWithConfigData().isEmpty()) {
      return List.of();
    }

    if (pCluster.getReplicaSetWithConfigData().get().getAction().isOnAgentAction()) {
      final ConfigServerType goalConfigServerType =
          pCluster
                  .getReplicaSetWithConfigData()
                  .get()
                  .getAction()
                  .isTransitioningToDedicatedConfig()
              ? ConfigServerType.DEDICATED
              : ConfigServerType.EMBEDDED;
      return List.of(
          actionFactory.forTransitionConfigServerMove(
              pCluster.getClusterDescription(), goalConfigServerType));
    }

    return List.of();
  }

  protected static List<PlannedAction> getPrivateLinkActions(
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final AutomationConfig pAutomationConfig,
      final List<? extends PrivateLinkConnectionRule> pPrivateLinkConnectionRulesForGroup,
      final Cluster pCluster,
      final List<BaseMultiTargetConnectionRule> pMultiTargetConnectionRules,
      final PlannedActionFactory pActionFactory,
      final Logger pLogger,
      final AppSettings pAppSettings) {
    // We avoid scheduling any Private Link Moves for System Projects that should never need them.
    //
    // System Projects copy over Containers from Source to System Projects. That's required for
    // performance, cost, and quota reasons. What that means is that Containers and their fields
    // from the Source Project might trigger moves in the System Project so long as the System
    // Project lives. We want to avoid that: the System Projects are throw-way, one-off Projects
    // and Clusters for ephemeral jobs; we do not want to impact the Source Project's networking
    // configuration.
    //
    // System Project wiki: https://wiki.corp.mongodb.com/pages/viewpage.action?pageId=306454664
    if (pNDSGroup.isSystemProject()) return List.of();

    final boolean atlasAdvancedRegionalizedModeEnabled;
    if (Objects.nonNull(pGroup) && Objects.nonNull(pAppSettings)) {
      atlasAdvancedRegionalizedModeEnabled =
          FeatureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.ATLAS_ADVANCED_REGIONALIZED_PRIVATE_ENDPOINTS,
              pAppSettings,
              null,
              pGroup);
    } else {
      atlasAdvancedRegionalizedModeEnabled = false;
    }

    // Our decision to sync privatelink should be based on whether changes to the group or cluster
    // need privatelink to be enabled or disabled for the cluster.
    // When privatelink is being established for an existing cluster, we should sync to enable it.
    // When nodes being added makes privatelink unusable, we should sync to disable it on all nodes.
    // When nodes are being added or removed but privatelink is staying on or off,
    // the provision and destroy moves should handle configuration changes.
    final ClusterContainerGroup clusterContainerGroup =
        ClusterContainerGroupUtil.getClusterContainerGroupByClusterDescription(
            pNDSGroup, pCluster.getClusterDescription());

    final List<CloudProviderContainer> clusterContainersWithActivePrivateLink =
        clusterContainerGroup.getClusterContainers().stream()
            .filter(
                container ->
                    container.getEndpointServices().stream()
                        .anyMatch(DedicatedEndpointService::isActive))
            .collect(Collectors.toList());

    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    if (!PrivateLinkUtil.clusterDescriptionSupportsPrivateLink(clusterDescription)
        && clusterContainersWithActivePrivateLink.isEmpty()) {
      pLogger.debug(
          "Cluster does not support PrivateLink and does not have it enabled in any regions."
              + " Skipping PrivateLink sync actions for cluster.");
      return Collections.emptyList();
    }

    // do not run a SyncClusterWithPrivateLinkMove if one of the cluster instances is about to
    // undergo a replacement or deletion. Specifically in cases where a machine destroy fails, the
    // instance and connection rule may no longer exist, but the db metadata only reflects that the
    // connection rule has been destroyed. This can lead to a sync move being improperly run.
    final Set<InstanceHardware.Action> actionsToSkip =
        Set.of(
            InstanceHardware.Action.DELETE,
            InstanceHardware.Action.CREATE,
            InstanceHardware.Action.RECONFIGURE,
            InstanceHardware.Action.HEAL_REPAIR);

    final boolean instancesNeedReconfig =
        pCluster.getLiveReplicaSets().stream()
            .flatMap(ReplicaSetHardware::getAllHardware)
            .filter(InstanceHardware::isProvisioned)
            .anyMatch(instance -> actionsToSkip.contains(instance.getAction()));

    if (instancesNeedReconfig) {
      pLogger.info(
          "Cluster instances require reconfiguration. Skipping PrivateLink sync actions for"
              + " cluster.");
      return Collections.emptyList();
    }

    final boolean azureInstancesCanSupportPrivateLink =
        AzurePrivateLinkSvc.isPrivateLinkSupportedByHardware(pCluster);

    if (!azureInstancesCanSupportPrivateLink && !pNDSGroup.isRegionalizedPrivateLinkEnabled()) {
      pLogger.info(
          "Cluster Azure instances do not support PrivateLink. Skipping PrivateLink "
              + "sync actions for cluster.");
      return Collections.emptyList();
    }

    // check for any instances that need to have a connection rule created
    final List<InstanceHardware> existingLogicInstancesNeedingSingleTargetConnectionRule =
        clusterContainersWithActivePrivateLink.stream()
            .flatMap(
                container ->
                    NDSCloudProviderContainerSvc
                        .getClusterInstancesNeedingSingleTargetConnectionRule(
                            pCluster, container, pPrivateLinkConnectionRulesForGroup)
                        .stream())
            .filter(
                instanceHardware ->
                    instanceHardware.getCloudProvider() != CloudProvider.AZURE
                        || azureInstancesCanSupportPrivateLink)
            .toList();

    // NDSCloudProviderContainerSvc::getClusterInstancesNeedingSingleTargetConnectionRule will be
    // replaced  in CLOUDP-293312. After that is done, we can remove this kludge, because the
    // logic will be updated to respect the cluster connection string configuration.
    final List<InstanceHardware> instancesNeedingSingleTargetConnectionRule;
    if (atlasAdvancedRegionalizedModeEnabled) {
      instancesNeedingSingleTargetConnectionRule =
          clusterContainerGroup.clusterShouldHaveSingleTargetRules(true)
              ? existingLogicInstancesNeedingSingleTargetConnectionRule
              : List.of();
    } else {
      instancesNeedingSingleTargetConnectionRule =
          existingLogicInstancesNeedingSingleTargetConnectionRule;
    }

    final boolean clusterRegionsCanSupportPrivateEndpoints =
        clusterContainerGroup.clusterRegionsCanSupportPrivateEndpoints(
            atlasAdvancedRegionalizedModeEnabled);

    final boolean shouldSyncForInstancesNeedingConnectionRule =
        clusterRegionsCanSupportPrivateEndpoints
            && !instancesNeedingSingleTargetConnectionRule.isEmpty();

    final boolean shouldRemoveAllSingleTargetRulesForCluster =
        !clusterContainerGroup.clusterShouldHaveSingleTargetRules(
                atlasAdvancedRegionalizedModeEnabled)
            && clusterDescription.hasSingleTargetPrivateEndpointStrings();

    final List<InstanceHardware> provisionedInstancesWithSingleTargetConnectionRule =
        new ArrayList<>();
    final List<InstanceHardware> provisionedInstancesWithConnectionRule = new ArrayList<>();

    // check for any instances that need to have their connection rule destroyed
    final Set<ObjectId> instanceIdsWithSingleTargetConnectionRule =
        PrivateLinkUtil.getConnectionRuleInstanceIDs(pPrivateLinkConnectionRulesForGroup);

    final Set<ObjectId> instanceIdsWithOptimizedConnectionRule =
        PrivateLinkUtil.getConnectionRuleInstanceIDs(pMultiTargetConnectionRules);

    pCluster.getLiveReplicaSets().stream()
        .flatMap(ReplicaSetHardware::getAllHardware)
        .filter(InstanceHardware::isProvisioned)
        .forEach(
            instance -> {
              if (instanceIdsWithOptimizedConnectionRule.contains(instance.getInstanceId())) {
                provisionedInstancesWithConnectionRule.add(instance);
              }
              if (instanceIdsWithSingleTargetConnectionRule.contains(instance.getInstanceId())) {
                provisionedInstancesWithSingleTargetConnectionRule.add(instance);
                provisionedInstancesWithConnectionRule.add(instance);
              }
            });

    final Set<ObjectId> provisionedInstanceIDsWithSingleTargetConnectionRule =
        provisionedInstancesWithSingleTargetConnectionRule.stream()
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toUnmodifiableSet());

    final Function<List<? extends LoadBalancerIngressRule>, Set<CloudProvider>>
        getProvidersWithStaleRules =
            (privateEndpointRules) ->
                privateEndpointRules.stream()
                    .filter(
                        privateEndpointRule ->
                            provisionedInstanceIDsWithSingleTargetConnectionRule.containsAll(
                                privateEndpointRule.getInstanceIds()))
                    .filter(
                        rule ->
                            PrivateLinkUtil.privateEndpointRuleIsStale(
                                rule, clusterContainersWithActivePrivateLink))
                    .map(LoadBalancerIngressRule::getCloudProvider)
                    .collect(Collectors.toUnmodifiableSet());
    final Set<CloudProvider> providersWithStaleSingleTargetRules =
        getProvidersWithStaleRules.apply(pPrivateLinkConnectionRulesForGroup);

    // Note: we only find stale single-target rules here because we find stale multi-target rules in
    // NDSCloudProviderContainerSvc.getContainerAndProcessPortTypeToInstanceHardwaresNeedingSetup
    // later
    final boolean shouldSyncForStaleSingleTargetRules =
        !providersWithStaleSingleTargetRules.isEmpty();

    final Set<ObjectId> containerIdsWithActivePrivateLink =
        clusterContainersWithActivePrivateLink.stream()
            .map(CloudProviderContainer::getId)
            .collect(Collectors.toUnmodifiableSet());
    final List<InstanceHardware> instancesNeedingConnectionRuleCleanup = new ArrayList<>();
    if (pNDSGroup.isRegionalizedPrivateLinkEnabled()) {
      final List<InstanceHardware> instancesWithConnectionRuleAndNoPrivateLink =
          provisionedInstancesWithConnectionRule.stream()
              .filter(
                  hardware ->
                      !containerIdsWithActivePrivateLink.contains(hardware.getCloudContainerId()))
              .toList();

      instancesNeedingConnectionRuleCleanup.addAll(instancesWithConnectionRuleAndNoPrivateLink);
    } else if (!clusterRegionsCanSupportPrivateEndpoints) {
      instancesNeedingConnectionRuleCleanup.addAll(provisionedInstancesWithConnectionRule);
    }
    // clear connection rule for dedicated config instances
    final List<InstanceHardware> dedicatedConfigInstancesWithConnectionRule =
        PrivateLinkUtil.getRemovableConfigInstanceConnectionRules(
            pCluster, pPrivateLinkConnectionRulesForGroup);
    if (!dedicatedConfigInstancesWithConnectionRule.isEmpty()) {
      instancesNeedingConnectionRuleCleanup.addAll(dedicatedConfigInstancesWithConnectionRule);
    }

    final boolean shouldSyncForConnectionRuleCleanup =
        !instancesNeedingConnectionRuleCleanup.isEmpty();

    // check for any instances that need to have the connection rule process port updated
    final boolean shouldCheckForProcessPortMismatch =
        !clusterDescription.isDeleteRequested()
            && pAutomationConfig
                .getDeployment()
                .getShardedCluster(clusterDescription.getDeploymentClusterName())
                .isPresent();

    final List<InstanceHardware> instancesWithMismatchedConnectionRulePort = new ArrayList<>();
    if (shouldCheckForProcessPortMismatch) {
      final List<PrivateLinkConnectionRule> activePrivateLinkConnectionRulesForCluster =
          PrivateLinkUtil.getActivePrivateLinkConnectionRulesForClusterMongoProcesses(
              pCluster, pPrivateLinkConnectionRulesForGroup, pNDSGroup);
      final Set<ObjectId> connectionRuleInstanceIdsWithMismatchedPort =
          activePrivateLinkConnectionRulesForCluster.stream()
              .filter(connectionRule -> !connectionRule.supportsMultipleProcesses())
              .filter(
                  connectionRule ->
                      !connectionRule.getNdsProcessPort().equals(NDSDefaults.MONGOS_PUBLIC_PORT))
              .map(PrivateLinkConnectionRule::getInstanceId)
              .collect(Collectors.toSet());

      instancesWithMismatchedConnectionRulePort.addAll(
          provisionedInstancesWithConnectionRule.stream()
              .filter(
                  hardware ->
                      connectionRuleInstanceIdsWithMismatchedPort.contains(
                          hardware.getInstanceId()))
              .toList());
    }

    // check for any instances that need to have the connection rule port updated due to proxy
    // protocol
    final boolean isPrivateLinkProxyProtocolForAwsEnabled =
        FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS, pAppSettings, null, pGroup);

    // Filter connection rules to only those for this cluster's instances
    final Set<ObjectId> provisionedAndActiveInstanceIDsWithSingleTargetConnectionRule =
        provisionedInstancesWithSingleTargetConnectionRule.stream()
            .filter(instance -> !instance.isPaused())
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toUnmodifiableSet());
    final List<? extends PrivateLinkConnectionRule> privateLinkConnectionRulesForCluster =
        pPrivateLinkConnectionRulesForGroup.stream()
            .filter(
                privateEndpointRule ->
                    provisionedAndActiveInstanceIDsWithSingleTargetConnectionRule.containsAll(
                        privateEndpointRule.getInstanceIds()))
            .collect(Collectors.toList());

    final boolean shouldSyncForProxyProtocolForAws =
        !PrivateLinkUtil.getConnectionRulesWithMismatchedProxyProtocolPortForAws(
                pNDSGroup,
                clusterDescription,
                privateLinkConnectionRulesForCluster,
                PrivateLinkUtil.clusterProcessesHaveCorrectParametersForProxyProtocol(
                    pAutomationConfig, clusterDescription),
                isPrivateLinkProxyProtocolForAwsEnabled,
                atlasAdvancedRegionalizedModeEnabled)
            .isEmpty();

    final boolean shouldSyncForInstanceProcessPortMismatch =
        !instancesWithMismatchedConnectionRulePort.isEmpty() || shouldSyncForProxyProtocolForAws;

    // check for any endpoint that needs to have its horizon created
    final List<Endpoint> endpointsNeedingHorizonSync =
        PrivateLinkUtil.activePrivateEndpointsNeedHorizons(
            clusterContainerGroup, clusterDescription, pAutomationConfig);
    final boolean shouldSyncForEndpointsMissingHorizon = !endpointsNeedingHorizonSync.isEmpty();

    final Map<Pair<ObjectId, NdsProcessPortTyped>, Set<InstanceHardware>>
        containerAndProcessPortTypeToInstanceHardwaresNeedingMultiTargetSetup =
            NDSCloudProviderContainerSvc
                .getContainerAndProcessPortTypeToInstanceHardwaresNeedingSetup(
                    pCluster,
                    pNDSGroup,
                    CloudProvider.AWS,
                    pMultiTargetConnectionRules,
                    atlasAdvancedRegionalizedModeEnabled);

    final boolean clusterNeedsMultiTargetSetup =
        !containerAndProcessPortTypeToInstanceHardwaresNeedingMultiTargetSetup.isEmpty();

    final List<PlannedAction> actions = new ArrayList<>();

    if (shouldSyncForInstancesNeedingConnectionRule
        || shouldSyncForConnectionRuleCleanup
        || shouldSyncForInstanceProcessPortMismatch
        || shouldSyncForEndpointsMissingHorizon
        || clusterNeedsMultiTargetSetup
        || shouldRemoveAllSingleTargetRulesForCluster
        || shouldSyncForStaleSingleTargetRules) {
      pLogger.info(
          "Cluster needs PrivateLink sync. (shouldSyncForInstancesNeedingConnectionRule:"
              + " {},shouldSyncForConnectionRuleCleanup:"
              + " {},shouldSyncForInstanceProcessPortMismatch:"
              + " {},shouldSyncForEndpointsMissingHorizon: {},clusterNeedsMultiTargetSetup:"
              + " {},shouldRemoveAllSingleTargetRules: {},shouldSyncForStaleSingleTargetRules: {})",
          shouldSyncForInstancesNeedingConnectionRule,
          shouldSyncForConnectionRuleCleanup,
          shouldSyncForInstanceProcessPortMismatch,
          shouldSyncForEndpointsMissingHorizon,
          clusterNeedsMultiTargetSetup,
          shouldRemoveAllSingleTargetRulesForCluster,
          shouldSyncForStaleSingleTargetRules);

      final Set<CloudProvider> providersOfSingleTargetRulesNeedingRemoval =
          shouldRemoveAllSingleTargetRulesForCluster
              ? new HashSet<>(
                  provisionedInstancesWithSingleTargetConnectionRule.stream()
                      .map(InstanceHardware::getCloudProvider)
                      .collect(Collectors.toSet()))
              : new HashSet<>();

      final Set<CloudProvider> providersNeedingSync =
          Stream.of(
                  containerAndProcessPortTypeToInstanceHardwaresNeedingMultiTargetSetup
                      .keySet()
                      .stream()
                      .map(Pair::getLeft)
                      .map(
                          (final ObjectId pRuleContainerId) ->
                              pNDSGroup
                                  .getCloudProviderContainer(pRuleContainerId)
                                  .orElseThrow(
                                      () ->
                                          new IllegalStateException(
                                              String.format(
                                                  "Have rule with container %s but group %s has no"
                                                      + " such container",
                                                  pRuleContainerId, pNDSGroup.getGroupId())))
                                  .getCloudProvider())
                      .collect(Collectors.toSet()),
                  instancesNeedingSingleTargetConnectionRule.stream()
                      .map(InstanceHardware::getCloudProvider)
                      .collect(Collectors.toSet()),
                  instancesNeedingConnectionRuleCleanup.stream()
                      .map(InstanceHardware::getCloudProvider)
                      .collect(Collectors.toSet()),
                  instancesWithMismatchedConnectionRulePort.stream()
                      .map(InstanceHardware::getCloudProvider)
                      .collect(Collectors.toSet()),
                  endpointsNeedingHorizonSync.stream()
                      .map(Endpoint::getCloudProvider)
                      .collect(Collectors.toSet()),
                  providersOfSingleTargetRulesNeedingRemoval,
                  providersWithStaleSingleTargetRules)
              .flatMap(Set::stream)
              .collect(Collectors.toSet());

      if (shouldSyncForProxyProtocolForAws) {
        providersNeedingSync.add(CloudProvider.AWS);
      }

      providersNeedingSync.forEach(
          provider ->
              actions.add(
                  pActionFactory.forSyncClusterPrivateEndpointConnection(
                      clusterDescription, provider)));
    }

    return actions;
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForIngestionPipelineRun(
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final CloudProvider pCloudProvider,
      final IngestionPipelineRun pPipelineRun) {

    final Plan plan = PlanResult.getNewPlan(pNDSGroup.getGroupId());
    final PlannedActionFactory factory = new PlannedActionFactory(plan.getPlanContext());
    final PlannedAction action =
        factory.forIngestionPipelineRun(pCloudProvider, pPipelineRun.getId());

    final ObjectId pipelineId = pPipelineRun.getPipelineId();
    return PlanResult.buildPlanEntry(
        pLogger,
        List.of(action),
        Collections.emptyList(),
        plan,
        List.of(),
        null,
        null,
        null,
        pipelineId,
        null,
        ResourceType.PIPELINE,
        String.format("pipeline %s", pipelineId.toString()));
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForRestoreJob(
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final List<CloudProviderContainer> pContainers,
      final BackupRestoreJobPlanUnit pRestoreJob,
      final boolean pIsCpsSnapshotConsistentExportEnabledFeatureFlagOn,
      final boolean isCpsOptimizedDaRestoreEnabled) {
    final Plan plan = getNewPlan(pNDSGroup.getGroupId());
    final PlannedActionFactory actionFactory = new PlannedActionFactory(plan.getPlanContext());

    final List<PlannedAction> actions =
        BackupRestoreActionSvc.getRestoreJobActions(
            pRestoreJob,
            actionFactory,
            pIsCpsSnapshotConsistentExportEnabledFeatureFlagOn,
            isCpsOptimizedDaRestoreEnabled);

    return PlanResult.buildPlanEntry(
        pLogger,
        actions,
        Collections.emptyList(),
        plan,
        pContainers.stream().map(CloudProviderContainer::getId).collect(Collectors.toList()),
        null, // intentionally no top-level cluster name
        pRestoreJob.getJobIdForPlanSummary(),
        RestoreSummaryFactory.getRestoreSummary(pRestoreJob),
        null,
        null,
        PlanSummary.ResourceType.SNAPSHOT_RESTORE,
        "snapshot_restore");
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForCopySnapshot(
      final Logger pLogger,
      final NDSGroup pNDSGroup,
      final List<CloudProviderContainer> pContainers,
      final BackupRestoreJobPlanUnit restoreTarget,
      final ClusterDescription cd,
      final CpsSnapshotEngine pCpsSnapshotEngine) {
    final Plan plan = getNewPlan(pNDSGroup.getGroupId());
    final PlannedActionFactory actionFactory = new PlannedActionFactory(plan.getPlanContext());

    final Pair<List<PlannedAction>, List<AuditDescription>> actions =
        pCpsSnapshotEngine.getCopyDiskBackupActions(cd, actionFactory);

    final RestoreSummary summary = RestoreSummaryFactory.getRestoreSummary(restoreTarget);
    final List<AuditDescription> descriptions = new ArrayList<>();
    descriptions.addAll(actions.getRight());

    return PlanResult.buildPlanEntry(
        pLogger,
        actions.getLeft(),
        descriptions,
        plan,
        pContainers.stream().map(CloudProviderContainer::getId).collect(Collectors.toList()),
        cd.getName(),
        null, // intentionally no restoreJobId on cluster plans
        summary,
        null,
        null,
        PlanSummary.ResourceType.COPY_SNAPSHOT,
        "copy_snapshot");
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForSystemClusterDeployment(
      final Logger pLogger,
      final SystemClusterJob pSystemClusterJob,
      final boolean pIsCpsSkipSystemClusterDestroyEnabled) {
    final Plan planInSourceProject = getNewPlan(pSystemClusterJob.getSourceProjectId());
    final PlannedActionFactory actionFactoryInSourceProject =
        new PlannedActionFactory(planInSourceProject.getPlanContext());

    final List<PlannedAction> plannedActions = new ArrayList<>();
    if (pSystemClusterJob.getSystemProjectCreationStatus()
        == SystemClusterJob.SystemClusterJobStatus.NOT_STARTED) {
      plannedActions.add(
          actionFactoryInSourceProject.forProvisionSystemCluster(
              planInSourceProject.getPlanContext(),
              pSystemClusterJob.getId(),
              pSystemClusterJob.getSourceClusterName()));
    } else if (pSystemClusterJob.isCompleted()
        && !pSystemClusterJob.isSystemProjectDeleted()
        && !pIsCpsSkipSystemClusterDestroyEnabled) {
      plannedActions.add(
          actionFactoryInSourceProject.forDestroySystemCluster(
              planInSourceProject.getPlanContext(),
              pSystemClusterJob.getId(),
              pSystemClusterJob.getSourceClusterName()));
    }

    return PlanResult.buildPlanEntry(
        pLogger,
        plannedActions,
        List.of(),
        planInSourceProject,
        List.of(),
        pSystemClusterJob.getSourceClusterName(),
        pSystemClusterJob.getId(),
        null,
        null,
        null,
        PlanSummary.ResourceType.SYSTEM_CLUSTER,
        "system_cluster");
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForShadowClusterExposure(
      final Logger pLogger, final ShadowClusterExposureJob pShadowClusterExposureJob) {
    final Plan planInSourceProject = getNewPlan(pShadowClusterExposureJob.getSourceProjectId());
    final PlannedActionFactory actionFactoryInSourceProject =
        new PlannedActionFactory(planInSourceProject.getPlanContext());

    final List<PlannedAction> plannedActions = new ArrayList<>();

    switch (pShadowClusterExposureJob.getJobStatus()) {
      case REQUESTED:
        plannedActions.add(
            actionFactoryInSourceProject.forCreateSnapshotForShadowCluster(
                planInSourceProject.getPlanContext(),
                pShadowClusterExposureJob.getId(),
                pShadowClusterExposureJob.getSourceClusterName()));
        break;
      default:
        // No action needed for other statuses
    }

    return PlanResult.buildPlanEntry(
        pLogger,
        plannedActions,
        List.of(),
        planInSourceProject,
        List.of(),
        pShadowClusterExposureJob.getSourceClusterName(),
        null,
        null,
        null,
        null,
        PlanSummary.ResourceType.SYSTEM_CLUSTER,
        "shadow_cluster");
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForShadowClusterDeployment(
      final Logger pLogger, final ShadowClusterJob pShadowClusterJob) {

    final Plan planInSourceProject = getNewPlan(pShadowClusterJob.getSourceProjectId());
    final PlannedActionFactory actionFactoryInSourceProject =
        new PlannedActionFactory(planInSourceProject.getPlanContext());

    final List<PlannedAction> plannedActions = new ArrayList<>();

    if (!pShadowClusterJob.getJobStatus().isTerminal()
        && pShadowClusterJob.targetDeletionDateReached()) {
      plannedActions.add(
          actionFactoryInSourceProject.forDestroyShadowCluster(
              planInSourceProject.getPlanContext(),
              pShadowClusterJob,
              pShadowClusterJob.getSystemClusterName()));
    } else {
      switch (pShadowClusterJob.getJobStatus()) {
        case PROVISIONING -> {
          plannedActions.add(
              actionFactoryInSourceProject.forProvisionShadowCluster(
                  planInSourceProject.getPlanContext(),
                  pShadowClusterJob,
                  pShadowClusterJob.getSourceClusterName()));
        }
      }
    }

    return PlanResult.buildPlanEntry(
        pLogger,
        plannedActions,
        List.of(),
        planInSourceProject,
        List.of(),
        pShadowClusterJob.getSourceClusterName(),
        pShadowClusterJob.getId(),
        null,
        null,
        null,
        PlanSummary.ResourceType.SYSTEM_CLUSTER,
        "shadow_cluster");
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForSystemClusterExecution(
      final Logger pLogger, final SystemClusterJob pSystemClusterJob) {
    final boolean readyToStartExecution =
        pSystemClusterJob.getSystemProjectId() != null
            && pSystemClusterJob.getRestoreStatus()
                == SystemClusterJob.SystemClusterJobStatus.SUCCESSFUL
            && pSystemClusterJob.getExecutionStatus()
                == SystemClusterJob.SystemClusterJobStatus.NOT_STARTED;
    if (!readyToStartExecution) {
      return Pair.of(null, null);
    }
    final Plan planInSystemProject = getNewPlan(pSystemClusterJob.getSystemProjectId());
    final PlannedActionFactory actionFactoryInSystemProject =
        new PlannedActionFactory(planInSystemProject.getPlanContext());

    final List<PlannedAction> plannedActions = new ArrayList<>();
    if (pSystemClusterJob.getExecutionOption() == SystemClusterJob.ExecutionOption.BACKUP_EXPORT) {
      plannedActions.add(
          actionFactoryInSystemProject.forDoSnapshotExportFromSystemCluster(
              planInSystemProject.getPlanContext(),
              pSystemClusterJob.getId(),
              pSystemClusterJob.getSystemClusterName()));
      plannedActions.add(
          actionFactoryInSystemProject.forUploadSnapshotExportCompleteFileFromSystemCluster(
              planInSystemProject.getPlanContext(),
              pSystemClusterJob.getId(),
              pSystemClusterJob.getSystemClusterName()));
    } else if (pSystemClusterJob.getExecutionOption()
        == SystemClusterJob.ExecutionOption.DB_COLLECTION_RESTORE) {
      plannedActions.add(
          actionFactoryInSystemProject.forCollectionRestoreFromSystemCluster(
              planInSystemProject.getPlanContext(),
              pSystemClusterJob.getId(),
              pSystemClusterJob.getSystemClusterName()));
    } else if (pSystemClusterJob.getExecutionOption()
        == SystemClusterJob.ExecutionOption.SHADOW_CLUSTER) {
      return Pair.of(null, null);
    }

    return PlanResult.buildPlanEntry(
        pLogger,
        plannedActions,
        List.of(),
        planInSystemProject,
        List.of(),
        pSystemClusterJob.getSourceClusterName(),
        pSystemClusterJob.getId(),
        null,
        null,
        null,
        PlanSummary.ResourceType.SYSTEM_CLUSTER,
        "system_cluster");
  }

  private static Pair<PlanSummary, Plan> buildPlanEntryForSystemClusterCleanup(
      final Logger pLogger, final SystemClusterJob pSystemClusterJob) {
    final boolean readyToStartCleanup =
        pSystemClusterJob.getSystemProjectId() != null
            && pSystemClusterJob.getExecutionStatus()
                == SystemClusterJob.SystemClusterJobStatus.IN_PROGRESS;
    if (!readyToStartCleanup) {
      return Pair.of(null, null);
    }

    // only DB_COLLECTION_RESTORE system cluster jobs need cleanup
    if (pSystemClusterJob.getExecutionOption()
        != SystemClusterJob.ExecutionOption.DB_COLLECTION_RESTORE) {
      return Pair.of(null, null);
    }

    final Plan planInSystemProject = getNewPlan(pSystemClusterJob.getSystemProjectId());
    final PlannedActionFactory actionFactoryInSystemProject =
        new PlannedActionFactory(planInSystemProject.getPlanContext());

    final CollectionRestoreJob collectionRestoreJob = (CollectionRestoreJob) pSystemClusterJob;
    if (collectionRestoreJob.getCollectionRestore().needsCleanup()) {
      final List<PlannedAction> plannedActions =
          List.of(
              actionFactoryInSystemProject.forCollectionRestoreCleanup(
                  planInSystemProject.getPlanContext(),
                  pSystemClusterJob.getId(),
                  pSystemClusterJob.getSystemClusterName()));
      return PlanResult.buildPlanEntry(
          pLogger,
          plannedActions,
          List.of(),
          planInSystemProject,
          List.of(),
          pSystemClusterJob.getSourceClusterName(),
          pSystemClusterJob.getId(),
          null,
          null,
          null,
          PlanSummary.ResourceType.SYSTEM_CLUSTER,
          "system_cluster");
    }

    return Pair.of(null, null);
  }

  private static Pair<PlanSummary, Plan> buildPlanEntry(
      final Logger pLogger,
      final List<PlannedAction> pActions,
      final List<AuditDescription> pDescriptions,
      final Plan pPlan,
      final List<ObjectId> pContainerIds,
      final String pClusterName,
      final ObjectId pRestoreJobId,
      final RestoreSummary pRestoreSummary,
      final ObjectId pPipelineId,
      final ObjectId pServerlessDeploymentId,
      final ResourceType pResourceType,
      final String pResourceString) {
    return buildPlanEntry(
        pLogger,
        pActions,
        pDescriptions,
        pPlan,
        pContainerIds,
        pClusterName,
        pRestoreJobId,
        pRestoreSummary,
        pPipelineId,
        pServerlessDeploymentId,
        pResourceType,
        pResourceString,
        Optional.empty(),
        false,
        false,
        false);
  }

  private static Pair<PlanSummary, Plan> buildPlanEntry(
      final Logger pLogger,
      final List<PlannedAction> pActions,
      final List<AuditDescription> pDescriptions,
      final Plan pPlan,
      final List<ObjectId> pContainerIds,
      final String pClusterName,
      final ObjectId pRestoreJobId,
      final RestoreSummary pRestoreSummary,
      final ObjectId pPipelineId,
      final ObjectId pServerlessDeploymentId,
      final ResourceType pResourceType,
      final String pResourceString,
      final Optional<NDSPlanningSummaryBuilder> pPlanningSummaryBuilder,
      final boolean pIsForMaterialClusterPlan,
      final boolean pAllowAbandoningWaitForMachineHealthyMove,
      final boolean pChainPauseMoves) {
    if (pActions.isEmpty()) {
      return Pair.of(null, pPlan);
    }

    final List<PlannedAction> diskModificationUpdateActions =
        pActions.stream()
            .filter(
                p ->
                    p.getMoveType() == MoveType.DISK_MODIFICATION
                        && p.getActionType() == ActionType.UPDATE
                        && p.getInstanceId().isPresent())
            .toList();
    final boolean machineUpdatesBeforeDiskModifications =
        !diskModificationUpdateActions.isEmpty()
            && diskModificationUpdateActions.stream()
                .allMatch(PlannedAction::getIsBlockedAndWithinMandatoryCooldownPeriod);
    pLogger.debug("Actions are needed. Generating a plan for {}", pResourceString);
    PlanResult.getDependencyRules(
            machineUpdatesBeforeDiskModifications,
            pAllowAbandoningWaitForMachineHealthyMove,
            pChainPauseMoves)
        .forEach(rule -> rule.apply(pActions));

    for (final PlannedAction action : pActions) {
      Arrays.stream(action.getMoves()).forEach(pPlan::addMove);
    }

    pDescriptions.forEach(pPlan::addAuditDescription);

    pLogger.info(
        "Built a potential plan with {} actions(s) and {} move(s) for {}:\n{}",
        pActions.size(),
        pPlan.getMoves().size(),
        pResourceString,
        pPlan);

    if (PlanResult.planContainsCycle(pActions)) {
      throw new IllegalStateException("Cycle detected in generated plan");
    }

    pPlanningSummaryBuilder
        .map(builder -> builder.build(pActions))
        .ifPresent(pPlan::setPlanningSummary);

    return Pair.of(
        new PlanSummary(
            pResourceType,
            PlanSummaryUtil.condenseActionTypes(pActions),
            pClusterName,
            pContainerIds,
            pRestoreJobId,
            pRestoreSummary,
            pPipelineId,
            pPlan.getId(),
            pServerlessDeploymentId,
            pIsForMaterialClusterPlan),
        pPlan);
  }

  protected static boolean planContainsCycle(final List<PlannedAction> pActions) {
    final Map<ObjectId, Move> planGraph = new HashMap<>();
    pActions.forEach(a -> Arrays.stream(a.getMoves()).forEach(m -> planGraph.put(m.getId(), m)));
    final Set<Move> startingMoves =
        planGraph.entrySet().stream()
            .map(Map.Entry::getValue)
            .filter(m -> m.getPredecessors().isEmpty())
            .collect(Collectors.toSet());

    // If there is no move without predecessors in the graph then it contains a cycle
    if (!planGraph.isEmpty() && startingMoves.isEmpty()) {
      return true;
    }

    // Treat each starting point as a separate graph and perform DFS on each.
    // If any such graph contains a cycle then the plan contains a cycle.
    final Set<Move> seen = new HashSet<>();
    for (final Move m : startingMoves) {
      if (PlanResult.containsCycle(planGraph, seen, new HashSet<>(), m)) {
        return true;
      }
    }

    // If all nodes were not discovered from a valid starting node then
    // we have a disconnected cyclic graph
    return seen.size() != planGraph.size();
  }

  // Check if there's a cycle in a graph from a particular starting point
  private static boolean containsCycle(
      final Map<ObjectId, Move> pMoves,
      final Set<Move> pSeen,
      final Set<Move> pPath,
      final Move pMove) {
    if (!pSeen.contains(pMove)) {
      pSeen.add(pMove);
      pPath.add(pMove);
      final Set<ObjectId> successors = new HashSet<>(pMove.getSuccessors());
      final Set<Move> moves = successors.stream().map(pMoves::get).collect(Collectors.toSet());
      for (final Move m : moves) {
        if (!pSeen.contains(m)) {
          // Recurse on the sub-graph if this move hasn't been seen
          // before. If the sub-graph contains a cycle then the
          // whole graph contains a cycle
          if (PlanResult.containsCycle(pMoves, pSeen, pPath, m)) {
            return true;
          }
        } else if (pPath.contains(m)) {
          // If this move is previously found in the recursive path then it
          // completes a cycle
          return true;
        }
      }
    }

    // Back track and remove completely visited moves from the path
    pPath.remove(pMove);
    return false;
  }

  protected static Pair<List<PlannedAction>, List<AuditDescription>> getBackupActions(
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final List<HostCluster> pHostClusters,
      final BackupJob pBackupJob,
      final PlannedActionFactory pActionFactory,
      final CpsSnapshotEngine pCpsSnapshotEngine,
      final AutomationConfig pAutomationConfig,
      final AppSettings pAppSettings,
      final Logger pLogger,
      final boolean actionsNeedEnsureConnectivityForTopologyUpdate) {
    final List<PlannedAction> actions = new ArrayList<>();
    final List<AuditDescription> descriptions = new ArrayList<>();
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    // We need to look for both types of host clusters since we could always be in between a
    // topology change
    final Optional<HostCluster> hostClusterForReplicaSet =
        pHostClusters.stream()
            .filter(HostCluster::isReplicaSet)
            .filter(
                h ->
                    h.getReplicaSetIds()
                        .contains(
                            NDSDefaults.getReplicaSetNameForUnshardedCluster(clusterDescription)))
            .findFirst();

    final Optional<HostCluster> hostClusterForShardedCluster =
        pHostClusters.stream()
            .filter(HostCluster::isShardedReplicaSet)
            .filter(h -> h.getName().equals(clusterDescription.getDeploymentClusterName()))
            .findFirst();

    final Pair<PlannedAction, List<AuditDescription>> result =
        PlanResult.getBackupActionForCluster(
            pCluster,
            hostClusterForReplicaSet
                .map((hostCluster) -> BackupSvc.getBackupConfigForGroup(pGroup, hostCluster))
                .orElse(null),
            hostClusterForShardedCluster
                .map((hostCluster) -> BackupSvc.getBackupConfigForGroup(pGroup, hostCluster))
                .orElse(null),
            pBackupJob,
            pAutomationConfig,
            pAppSettings,
            pGroup,
            pActionFactory,
            pCpsSnapshotEngine,
            new Date(),
            pLogger,
            actionsNeedEnsureConnectivityForTopologyUpdate);

    if (result != null) {
      actions.add(result.getLeft());
      descriptions.addAll(result.getRight());
    }

    final Pair<List<PlannedAction>, List<AuditDescription>> diskBackupResult =
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            pActionFactory,
            pCpsSnapshotEngine,
            pGroup,
            pNDSGroup,
            PlanningType.BLOCKING,
            pLogger);

    if (diskBackupResult != null) {
      actions.addAll(diskBackupResult.getLeft());
      descriptions.addAll(diskBackupResult.getRight());
    }

    return Pair.of(actions, descriptions);
  }

  protected static boolean combineFreeTierContainerAndClusterProvision(AppSettings appSettings) {
    return appSettings.getBoolProp(
        "nds.combineFreeTierContainerAndClusterProvision.enabled", false);
  }

  private static boolean isGCPServiceAccountSetupRequired(
      final CloudProviderContainer pContainer, final NDSGroup pNDSGroup) {
    if (!CloudProvider.GCP.equals(pContainer.getCloudProvider())
        || isGCPProjectFolderFound(pNDSGroup)) {
      return false;
    }

    return Optional.ofNullable(pNDSGroup.getCloudProviderAccess())
        .map(NDSCloudProviderAccess::getGcpServiceAccounts)
        .stream()
        .flatMap(List::stream)
        .map(NDSCloudProviderAccessGCPServiceAccount::getStatus)
        .anyMatch(ServiceAccountProvisionStatus.IN_PROGRESS::equals);
  }

  private static boolean isGCPProjectFolderFound(final NDSGroup pNDSGroup) {
    return pNDSGroup
        .getCloudProviderContainerByType(CloudProvider.GCP)
        .filter(GCPCloudProviderContainer.class::isInstance)
        .map(GCPCloudProviderContainer.class::cast)
        .flatMap(GCPCloudProviderContainer::getGcpProjectId)
        .isPresent();
  }

  /**
   * Adds container provisioning actions for Free Tenant clusters when the feature is enabled.
   *
   * <p>This method implements "front-loading" container provisioning for free tier clusters. When
   * the combineFreeTierContainerAndClusterProvision feature flag is enabled, we provision
   * containers as part of the cluster build plan rather than in separate container plans. This
   * optimization combines container and cluster provisioning into a single operation, reducing the
   * overall provisioning time for free tier users.
   *
   * <p>The logic checks: 1. The feature is enabled 2. Cluster is a Free Tenant cluster 3. All
   * machine actions are CREATE (indicating new cluster creation) 4. Container is not yet
   * provisioned but is necessary for the cluster
   *
   * @param pAppSettings application settings to check feature is enabled
   * @param pCluster the cluster being planned
   * @param pContainers list of containers associated with the cluster
   * @param pNDSGroup the NDS group containing the cluster
   * @param pMachineActions list of machine actions from cluster planning
   * @param pActionFactory factory for creating planned actions
   * @param pActions mutable list to add container provisioning actions to
   */
  private static void addFreeTierContainerProvisioningActions(
      final AppSettings pAppSettings,
      final Cluster pCluster,
      final List<CloudProviderContainer> pContainers,
      final NDSGroup pNDSGroup,
      final List<PlannedAction> pMachineActions,
      final PlannedActionFactory pActionFactory,
      final List<PlannedAction> pActions) {

    // Only apply this optimization for Free Tenant clusters when the feature is enabled
    if (!combineFreeTierContainerAndClusterProvision(pAppSettings)
        || !pCluster.getClusterDescription().isFreeTenantCluster()) {
      return;
    }

    // Check if we are creating a new cluster (all machine actions are CREATE)
    final boolean createNewCluster =
        pMachineActions.stream().allMatch(a -> a.getActionType() == ActionType.CREATE);

    if (!createNewCluster) {
      return;
    }

    // Find the first unprovisioned container that is necessary for this cluster
    // and add a container creation action to front-load the provisioning
    pContainers.stream()
        .filter(c -> !c.isProvisioned())
        .filter(
            c -> NDSCloudProviderContainerSvc.isContainerNecessary(pNDSGroup, List.of(pCluster), c))
        .findFirst()
        .ifPresent(c -> pActions.add(pActionFactory.forCreateContainer(Map.of(), c, List.of())));
  }

  protected static List<PlannedAction> getContainerActions(
      final AppSettings pAppSettings,
      final NDSGroup pNDSGroup,
      final Group pGroup,
      final List<Cluster> pClusters,
      final Set<RegionName> pRegionsWithSnapshotsAndSnapshotDistribution,
      final PlannedActionFactory pActionFactory,
      final CloudProviderContainer pContainer,
      final List<ServerlessLoadBalancingDeployment> pServerlessLoadBalancingDeployments,
      final ServerlessMTMPool pServerlessMTMPool,
      final List<BackupJob> pBackupJobs,
      final List<BackupRestoreJobPlanUnit> pRestoreJobs,
      final List<CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>>
          pHeldAWSCapacityRequests,
      final List<CheckCapacityRequest<AzureInstanceCapacitySpec, AzureCheckResult>>
          pActiveAzureCapacityRequests,
      final Set<ObjectId> pContainersWithActiveDedicatedSearch,
      final boolean pHasStreamsPrivateNetworking,
      final List<ServerlessUpgradeToDedicatedStatus> pServerlessUpgradeToDedicatedStatuses,
      final List<AzureCapacityDenyListEntry> pAzureDenylistEntriesNeedingCapacityCheck,
      final Logger pLogger) {
    final List<PlannedAction> actions = new ArrayList<>();

    // If this is a system project, its container(s) are copies of a source project's containers,
    // and we skip any actions that might break or misconfigure the source project containers.
    if (pNDSGroup.getContainsCopiedContainers()) return actions;

    // serverless deployment is active and matches container region
    final boolean hasServerlessDeployments =
        pServerlessMTMPool != null
            && !pServerlessLoadBalancingDeployments.isEmpty()
            && pContainer.isResponsibleForThisRegion(
                pServerlessLoadBalancingDeployments.get(0).getProviderRegionName());

    final boolean serverlessDeploymentsUnprovisioned =
        !hasServerlessDeployments
            || !pServerlessLoadBalancingDeployments.stream()
                .allMatch(ServerlessLoadBalancingDeployment::isProvisioned);

    final boolean provisionServerlessMTMContainer =
        hasServerlessDeployments && pNDSGroup.isServerlessMTMHolder();

    final boolean hasPrivateNetworkingResources = !pContainer.getTenantEndpointServices().isEmpty();

    final boolean containerNecessary =
        NDSCloudProviderContainerSvc.isContainerNecessary(pNDSGroup, pClusters, pContainer);

    final boolean copySnapshotsRequiresContainer =
        copySnapshotsRequiresContainer(pBackupJobs, pContainer);

    // Currently an AWS/Azure only feature, but easy to add an abstraction later if that changes
    final CloudProvider cloudProvider = pContainer.getCloudProvider();
    final boolean hasHeldAWSCapacityRequests =
        (pContainer instanceof RegionalDedicatedCloudProviderContainer)
            && pHeldAWSCapacityRequests.stream()
                .flatMap(cr -> cr.getInstanceSpecs().stream())
                .anyMatch(
                    spec ->
                        spec.getRegionName()
                                .equals(
                                    ((RegionalDedicatedCloudProviderContainer) pContainer)
                                        .getRegion())
                            && spec.getAccountId().equals(pContainer.getCloudProviderAccountId()));

    // NB: azure container provisioning is a prerequisite to be able to make capacity reservations
    final boolean hasActiveAzureCapacityRequests =
        (pContainer instanceof final AzureCloudProviderContainer azureContainer)
            && pActiveAzureCapacityRequests.stream()
                .flatMap(cr -> cr.getInstanceSpecs().stream())
                .anyMatch(
                    spec ->
                        spec.getRegionName().equals((azureContainer.getRegion()))
                            && spec.getSubscriptionId()
                                .equals(azureContainer.getAzureSubscriptionId()));

    final boolean requiresContainerForCapacityRequests =
        cloudProvider == CloudProvider.AWS && hasHeldAWSCapacityRequests
            || (cloudProvider == CloudProvider.AZURE && hasActiveAzureCapacityRequests);

    final boolean isInternalCapacityReservationProject =
        pAppSettings
            .getCapacityReservationCronInternalProjectId()
            .equals(Optional.of(pNDSGroup.getGroupId()));
    final boolean matchesRegionForAZCapacityCheck =
        isInternalCapacityReservationProject
            && azureCapacityCheckerRequiresContainer(
                pAzureDenylistEntriesNeedingCapacityCheck, pContainer);

    // Don't delete a container if it still contains backup snapshots
    final boolean allSnapshotsPurged;
    RegionName regionName = null;
    if (cloudProvider == CloudProvider.AZURE) {
      regionName = ((AzureCloudProviderContainer) pContainer).getRegion();
      allSnapshotsPurged = !pRegionsWithSnapshotsAndSnapshotDistribution.contains(regionName);
    } else if (cloudProvider == CloudProvider.AWS) {
      regionName = ((AWSCloudProviderContainer) pContainer).getRegion();
      allSnapshotsPurged = !pRegionsWithSnapshotsAndSnapshotDistribution.contains(regionName);
    } else if (cloudProvider == CloudProvider.GCP) {
      // A single GCP container contains subnet for all regions for the project.
      // If there is a single GCP snapshot for a project, we don't delete the GCP container for that
      // project.
      allSnapshotsPurged =
          !pRegionsWithSnapshotsAndSnapshotDistribution.stream()
              .anyMatch(r -> r.getProvider().equals(CloudProvider.GCP));
    } else {
      allSnapshotsPurged = true;
    }

    boolean hasEncryptionAtRestPrivateEndpoint = false;
    final boolean isEARPrivateNetworkingEnabled =
        Stream.of(
                FeatureFlag.ENCRYPTION_AT_REST_AZURE_KEY_VAULT_PRIVATE_ENDPOINT,
                FeatureFlag.ENCRYPTION_AT_REST_AWS_KMS_PRIVATE_ENDPOINT)
            .anyMatch(flag -> isFeatureFlagEnabled(flag, pAppSettings, null, pGroup));

    if (isEARPrivateNetworkingEnabled) {
      hasEncryptionAtRestPrivateEndpoint =
          pNDSGroup.getEncryptionAtRest().getPrivateEndpointForRegion(regionName).isPresent();
    }

    final boolean hasObjectStoragePrivateEndpoint =
        pNDSGroup
            .getObjectStoragePrivateEndpointForRegion(regionName, CloudProvider.AWS)
            .isPresent();

    final boolean provisionContainerNecessary =
        containerNecessary
            || provisionServerlessMTMContainer
            || copySnapshotsRequiresContainer
            || requiresContainerForCapacityRequests
            || hasObjectStoragePrivateEndpoint
            || hasEncryptionAtRestPrivateEndpoint
            || (pHasStreamsPrivateNetworking && !pContainer.getCloudProvider().isTenantProvider())
            || matchesRegionForAZCapacityCheck;

    final Map<String, String> tags =
        NDSMoveTags.builder()
            .setCloudProvider(pContainer.getCloudProvider())
            .setRegionName(regionName)
            .build();

    final ContainerSubnetsToUpdate subnetUpdates =
        NDSCloudProviderContainerSvc.getSubnetRegionsToUpdate(
            pContainer, pNDSGroup, pClusters, pServerlessMTMPool, pRestoreJobs);

    final boolean containerHasSearchDeployment =
        pContainersWithActiveDedicatedSearch.contains(pContainer.getId());

    final boolean containerForServerlessInstanceUpgradingToDedicated =
        pContainer.getCloudProvider().isServerless()
            && pServerlessUpgradeToDedicatedStatuses.stream()
                .anyMatch(
                    status ->
                        status
                            .getClusterName()
                            .equals(
                                ((ServerlessCloudProviderContainer) pContainer)
                                    .getTenantClusterName()));

    if (isGCPServiceAccountSetupRequired(pContainer, pNDSGroup)) {
      actions.add(pActionFactory.forSyncCloudProviderAccessRoleSetupMove(pContainer, tags));
    }

    if (pContainer.isProvisioned()
        && !containerNecessary
        && serverlessDeploymentsUnprovisioned
        && !hasPrivateNetworkingResources
        && allSnapshotsPurged
        && !requiresContainerForCapacityRequests
        && !containerHasSearchDeployment
        && !hasObjectStoragePrivateEndpoint
        && !hasEncryptionAtRestPrivateEndpoint
        && !pHasStreamsPrivateNetworking
        && !containerForServerlessInstanceUpgradingToDedicated
        && !matchesRegionForAZCapacityCheck) {
      // Destroy a container no longer needed
      pContainer
          .getContainerPeers()
          .forEach(p -> actions.add(pActionFactory.forSyncPeeringConnection(pContainer, p)));
      actions.add(pActionFactory.forDestroyContainer(pContainer));
      return actions;
    } else if (!pContainer.isProvisioned() && provisionContainerNecessary) {
      final boolean skipFreeTierContainerProvision =
          CloudProvider.FREE == pContainer.getCloudProvider()
              && combineFreeTierContainerAndClusterProvision(pAppSettings);
      if (!skipFreeTierContainerProvision) {
        actions.add(
            pActionFactory.forCreateContainer(
                tags, pContainer, subnetUpdates.subnetRegionsToCreate));
      }
    } else if (!pContainer.isProvisioned()) {
      // Not provisioned and not needed
      return actions;
    }

    // create container subnet if needed
    if (pContainer.isProvisioned() && !subnetUpdates.subnetRegionsToCreate.isEmpty()) {
      actions.add(
          pActionFactory.forUpdateContainerSubnets(
              pContainer,
              subnetUpdates.subnetRegionsToCreate,
              subnetUpdates.subnetRegionsToDelete,
              ActionType.CREATE));
    }

    final boolean serverlessToFlexEnvoyBypassUpdateNeeded =
        hasServerlessDeployments
            && pServerlessMTMPool.getEnvoyBypassRequestedDate() != null
            && pServerlessMTMPool.getEnvoyBypassNetworkingChangesCompletedDate() == null;

    final boolean serverlessToFlexEnvoyBypassRollbackNeeded =
        hasServerlessDeployments
            && pServerlessMTMPool.getEnvoyBypassRollbackRequestedDate() != null
            && pServerlessMTMPool.getEnvoyBypassRequestedDate() == null;

    // Update a provisioned container if needed
    final boolean containerNetworkPermissionNeedsUpdate =
        !subnetUpdates.subnetRegionsToCreate.isEmpty()
            || !pContainer
                .getNetworkPermissionListLastUpdated()
                .orElse(new Date(0))
                .equals(pNDSGroup.getNetworkPermissionList().getLastUpdated());

    final boolean serverlessToFlexBypassNetworkUpdateNeeded =
        serverlessToFlexEnvoyBypassUpdateNeeded || serverlessToFlexEnvoyBypassRollbackNeeded;

    // Update IP whitelisting.
    if (containerNetworkPermissionNeedsUpdate || serverlessToFlexBypassNetworkUpdateNeeded) {
      if (pNDSGroup.isServerlessMTMHolder() || hasServerlessDeployments) {
        actions.add(pActionFactory.forEnsureServerlessNetworkPermissions(pContainer));
        return actions;
      } else if (pActionFactory
          .getMoveProvider(pContainer.getCloudProvider())
          .isIpWhitelistSupported()) {
        actions.add(pActionFactory.forUpdateWhitelist(tags, pContainer));

        // Ensure that the Kafka ingress ports are still valid if the group has Streams Private Link
        // enabled.
        if (pHasStreamsPrivateNetworking && pContainer.getCloudProvider() == CloudProvider.AWS) {
          actions.add(pActionFactory.forEnsureKafkaNetworkPermissions(pContainer));
        }
        return actions;
      }
    }

    if (pActionFactory.getMoveProvider(pContainer.getCloudProvider()).isPeeringSupported()) {
      pContainer.getContainerPeers().stream()
          // Container needs to be updated or deleted
          .filter(p -> p.getNeedsUpdateAfter().isPresent())
          .forEach(p -> actions.add(pActionFactory.forSyncPeeringConnection(pContainer, p)));
    }

    if (pActionFactory
        .getMoveProvider(pContainer.getCloudProvider())
        .isPrivateEndpointSupported()) {
      pContainer
          .getEndpointServices()
          .forEach(
              endpointService -> {
                if (endpointService.getNeedsUpdateAfter().isPresent()
                    || endpointService.isDeleteRequested()) {
                  actions.add(
                      pActionFactory.forSyncEndpointServiceConnection(pContainer, endpointService));
                }
              });
      if (hasEncryptionAtRestPrivateEndpoint) {
        pNDSGroup
            .getEncryptionAtRest()
            .getPrivateEndpointForRegion(regionName)
            .ifPresent(
                pe -> {
                  final boolean needsUpdate =
                      pe.getNeedsUpdateAfter().map(d -> d.before(new Date())).orElse(false);
                  if (needsUpdate || pe.getDeleteRequestedDate().isPresent()) {
                    try {
                      actions.add(
                          pActionFactory.forSyncEncryptionAtRestPrivateEndpoint(pContainer));
                    } catch (final UnsupportedOperationException ignored) {
                      // currently only support AZURE, ignore other providers
                    }
                  }
                });
      }
      pNDSGroup
          .getObjectStoragePrivateEndpointForRegion(regionName, CloudProvider.AWS)
          .filter(ep -> ep instanceof AWSS3PrivateEndpoint)
          .map(AWSS3PrivateEndpoint.class::cast)
          .ifPresent(
              ep -> {
                boolean deleteRequested = ep.getDeleteRequestedDate().isPresent();

                boolean stillProvisioning =
                    ep.getStatus() == CloudProviderPrivateEndpoint.Status.INITIATING;

                boolean retryAfterFailure =
                    ep.getStatus() == CloudProviderPrivateEndpoint.Status.FAILED
                        && ep.getNeedsUpdateAfter().map(d -> d.before(new Date())).orElse(false);

                if (deleteRequested || stillProvisioning || retryAfterFailure) {
                  actions.add(pActionFactory.forObjectStoragePrivateEndpoint(pContainer));
                }
              });
    }

    // Check for Prometheus private networking that needs infrastructure synchronization
    if (pContainer.getCloudProvider() == CloudProvider.AWS) {
      final boolean prometheusNeedsSync =
          pGroup.hasPrometheusIntegration()
              && pGroup.getPromConfig().getNeedsSync() != null
              && pGroup.getPromConfig().getNeedsSync().before(new Date());

      if (prometheusNeedsSync) {
        final Optional<PrometheusEndpointForContainer> prometheusEndpoint =
            NDSPrometheusEndpointSvc.getPrometheusEndpointForContainer(pContainer);

        // Only trigger if this container has a Prometheus endpoint with PENDING status
        if (prometheusEndpoint
            .filter(e -> e.status() == EndpointPrometheusStatus.PENDING)
            .isPresent()) {
          actions.add(pActionFactory.forPrometheusPrivateNetworkingCreate(pContainer));
        }
      }
    }

    if (pNDSGroup.isServerlessMTMHolder()) {
      actions.addAll(
          getTenantEndpointServiceDeploymentActions(pContainer, pActionFactory, pLogger));
    }

    if (actions.stream()
            .allMatch(plannedAction -> plannedAction.getActionType().equals(ActionType.DELETE))
        && subnetUpdates.subnetRegionsToCreate.isEmpty()
        && !subnetUpdates.subnetRegionsToDelete.isEmpty()) {
      // only delete container subnets if no other constructive action types are scheduled. We don't
      // want the DeleteContainerLast dependency rule to prevent other container updates from being
      // scheduled
      actions.add(
          pActionFactory.forUpdateContainerSubnets(
              pContainer,
              Collections.emptyList(),
              subnetUpdates.subnetRegionsToDelete,
              ActionType.DELETE));
    }

    return actions;
  }

  /**
   * this method returns true when pContainer's region matches one of those in the azure denylist to
   * be checked (see {@link
   * com.xgen.cloud.nds.capacity._public.svc.AzureCapacityDenylistSvc#getDenylistEntriesNeedingCapacityCheck})
   * *
   */
  static boolean azureCapacityCheckerRequiresContainer(
      final List<AzureCapacityDenyListEntry> pDenylistEntries,
      final CloudProviderContainer pContainer) {
    return pDenylistEntries.stream()
        .map(AzureCapacityDenyListEntry::getRegionName)
        .anyMatch(pContainer::isResponsibleForThisRegion);
  }

  static boolean copySnapshotsRequiresContainer(
      final List<BackupJob> pBackupJobs, final CloudProviderContainer pContainer) {
    return pBackupJobs.stream()
        .filter(job -> !job.isRetaining())
        .filter(job -> job.getCopySettings() != null && job.getCopySettings().size() > 0)
        .anyMatch(
            job ->
                job.getCopySettings().stream()
                    .anyMatch(
                        copySetting ->
                            pContainer.isResponsibleForThisRegion(copySetting.getRegionName())));
  }

  static boolean isCpsPitOutOfSync(final ClusterDescription pCluster, final BackupJob pBackupJob) {
    return pBackupJob != null
        && ((pCluster.isPitEnabled() && !pBackupJob.isPitEnabled())
            || (!pCluster.isPitEnabled() && pBackupJob.isPitEnabled()));
  }

  static boolean isCpsCopySettingsOutOfSync(
      final BackupJob pBackupJob,
      final ClusterDescription pClusterDescription,
      final CpsSnapshotEngine pCpsSnapshotEngine) {
    return pBackupJob != null
        && pBackupJob.isPitEnabled()
        && pBackupJob.getCopySettings() != null
        && pCpsSnapshotEngine
            .getCpsPitSvc()
            .isPitStorageCopySettingsOutOfSync(pBackupJob, pClusterDescription);
  }

  protected static Pair<PlannedAction, List<AuditDescription>> getBackupActionForCluster(
      final Cluster pCluster,
      final BackupConfig pBackupConfigReplicaSet,
      final BackupConfig pBackupConfigShardedCluster,
      final BackupJob pBackupJob,
      final AutomationConfig pAutomationConfig,
      final AppSettings pAppSettings,
      final Group pGroup,
      final PlannedActionFactory pActionFactory,
      final CpsSnapshotEngine pCpsSnapshotEngine,
      final Date now,
      final Logger pLogger,
      final boolean actionsNeedEnsureConnectivityForTopologyUpdate) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    if (clusterDescription.isTenantCluster()) {
      pLogger.trace(
          "Cluster {} does not support continuous or cps backup", clusterDescription.getName());
      return null;
    }

    final boolean replicaSetActive =
        pBackupConfigReplicaSet != null && !pBackupConfigReplicaSet.getState().isInactive();
    final boolean shardedClusterActive =
        pBackupConfigShardedCluster != null && !pBackupConfigShardedCluster.getState().isInactive();
    final boolean daemonBackupActive = replicaSetActive || shardedClusterActive;
    final boolean diskBackupActive = pBackupJob != null && !pBackupJob.isRetaining();
    final boolean isShardedCluster = clusterDescription instanceof ShardedClusterDescription;

    pLogger.debug(
        "Computing backup actions for cluster projectId={} clusterName={} replicaSetActive={}"
            + " shardedClusterActive={} daemonBackupActive={} diskBackupActive={}"
            + " isShardedCluster={}",
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        replicaSetActive,
        shardedClusterActive,
        daemonBackupActive,
        diskBackupActive,
        isShardedCluster);

    // Case 1: Deleting a cluster
    if (clusterDescription.isDeleteRequested() && (daemonBackupActive || diskBackupActive)) {
      return Pair.of(
          pActionFactory.forDeleteBackupConfig(clusterDescription), Collections.emptyList());
    }

    if (clusterDescription.isDeleteRequested()) {
      return null;
    }

    // Case 2:  Topology change including forCreateMachine, forDestroyMachine, forPauseMachine
    // actions.
    if ((clusterDescription.isBackupEnabled() || clusterDescription.isDiskBackupEnabled())
        && actionsNeedEnsureConnectivityForTopologyUpdate) {
      return Pair.of(
          pActionFactory.forExpandBackupConfig(clusterDescription), Collections.emptyList());
    }

    // Case 3: Out of sync settings
    // The expressions for out of sync represent the general state of backup for a cluster and since
    // we have covered all other specific cases prior to here, we can just add the rest of the moves
    // here that require no context.
    final boolean replicaSetStarted =
        pBackupConfigReplicaSet != null && pBackupConfigReplicaSet.getState().isStarted();

    final boolean shardedClusterStarted =
        pBackupConfigShardedCluster != null && pBackupConfigShardedCluster.getState().isStarted();

    final boolean daemonBackupStarted =
        isShardedCluster ? shardedClusterStarted : replicaSetStarted;

    final boolean enabledButOutOfSync =
        clusterDescription.isBackupEnabled() && !daemonBackupStarted;

    final boolean disabledButOutOfSync =
        !clusterDescription.isBackupEnabled()
            && !clusterDescription.isStopContinuousBackup()
            && daemonBackupActive;

    final boolean diskBackupOutOfSync =
        (clusterDescription.isDiskBackupEnabled() && pBackupJob == null)
            || (!clusterDescription.isDiskBackupEnabled() && pBackupJob != null);

    final boolean cpsPitOutOfSync = isCpsPitOutOfSync(clusterDescription, pBackupJob);
    final boolean cpsPitRegionOrMigrationOutOfSync =
        pBackupJob != null
            && pBackupJob.isPitEnabled()
            && clusterDescription.isPitEnabled()
            && pCpsSnapshotEngine
                .getCpsPitSvc()
                .isPitRegionOrMigrationOutOfSync(
                    pGroup,
                    clusterDescription,
                    pCluster.getReplicaSets(),
                    pBackupJob,
                    pAppSettings,
                    now);
    final boolean cpsPitCopySettingsOutOfSync =
        isCpsCopySettingsOutOfSync(pBackupJob, clusterDescription, pCpsSnapshotEngine);

    final boolean daemonBackupNeedStop =
        (clusterDescription.isPaused() || clusterDescription.isStopContinuousBackup())
            && daemonBackupStarted;

    final boolean diskBackupNeedsPaused =
        clusterDescription.isPaused()
            && clusterDescription.isDiskBackupEnabled()
            && pBackupJob != null
            && !pBackupJob.isPaused();

    final boolean backupNeedsPaused = daemonBackupNeedStop || diskBackupNeedsPaused;

    final boolean diskBackupPauseOutOfSync =
        clusterDescription.isDiskBackupEnabled() && pBackupJob != null && pBackupJob.isPaused();

    final boolean oplogEncryptionSettingOutOfSync =
        oplogEncryptionSettingOutOfSync(
            clusterDescription, pAutomationConfig, pBackupJob, pGroup, pAppSettings);

    pLogger.debug(
        "Sync backup config for cluster projectId={} clusterName={}, clusterPaused = {},"
            + " enabledButOutOfSync={}, disabledButOutOfSync={}, diskBackupOutOfSync = {},"
            + " cpsPitOutOfSync={}, cpsPitRegionOrMigrationOutOfSync={},"
            + " diskBackupPauseOutOfSync={}, oplogEncryptionSettingOutOfSync={},"
            + " cpsPitCopySettingsOutOfSync={}",
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        clusterDescription.isPaused(),
        enabledButOutOfSync,
        disabledButOutOfSync,
        diskBackupOutOfSync,
        cpsPitOutOfSync,
        cpsPitRegionOrMigrationOutOfSync,
        diskBackupPauseOutOfSync,
        oplogEncryptionSettingOutOfSync,
        cpsPitCopySettingsOutOfSync);

    final boolean backupOutOfSync =
        diskBackupOutOfSync
            || enabledButOutOfSync
            || disabledButOutOfSync
            || diskBackupPauseOutOfSync
            || cpsPitOutOfSync
            || cpsPitCopySettingsOutOfSync
            || cpsPitRegionOrMigrationOutOfSync
            || oplogEncryptionSettingOutOfSync;
    if (backupNeedsPaused || (!clusterDescription.isPaused() && backupOutOfSync)) {
      return Pair.of(
          pActionFactory.forSyncBackupConfig(clusterDescription), Collections.emptyList());
    }
    return null;
  }

  static boolean oplogEncryptionSettingOutOfSync(
      final ClusterDescription pClusterDescription,
      final AutomationConfig pAutomationConfig,
      final BackupJob pBackupJob,
      final Group pGroup,
      final AppSettings pAppSettings) {
    if (pBackupJob == null || !pBackupJob.isPitEnabled()) {
      return false;
    }

    final EncryptionProviderType automationConfigEncryptionProviderType =
        BackupSnapshotUtils.getEncryptionProviderTypeOfCluster(
            pClusterDescription, pAutomationConfig);

    final EncryptionProviders automationAgentConfigEncryptionProviders =
        pAutomationConfig.getDeployment().getEncryptionProviders();

    final boolean shouldEnableOplogEncryption =
        PitEncryptionUtil.shouldEnableOplogEncryption(
            pGroup,
            automationAgentConfigEncryptionProviders,
            automationConfigEncryptionProviderType);

    final String pitSettingCmk =
        EncryptionProviderType.GCP == automationConfigEncryptionProviderType
            ? pBackupJob.getVersionedCmk()
            : pBackupJob.getCmk();

    return isOplogEncryptionSettingOutOfSync(
        pitSettingCmk,
        automationAgentConfigEncryptionProviders,
        automationConfigEncryptionProviderType,
        shouldEnableOplogEncryption);
  }

  static boolean isOplogEncryptionSettingOutOfSync(
      final String pitSettingCmk,
      final EncryptionProviders automationAgentConfigEncryptionProviders,
      final EncryptionProviderType automationConfigEncryptionProviderType,
      final boolean shouldEnableOplogEncryption) {

    if (!shouldEnableOplogEncryption) {
      return pitSettingCmk != null;
    }

    final String updatedCmk =
        EncryptionProviderType.GCP == automationConfigEncryptionProviderType
            ? PitEncryptionUtil.getVersionedCmk(
                automationAgentConfigEncryptionProviders, automationConfigEncryptionProviderType)
            : PitEncryptionUtil.getCmk(
                automationAgentConfigEncryptionProviders, automationConfigEncryptionProviderType);

    // State where kms is enabled for the cluster
    // 1. pitSettings encryption is disabled
    // 2a. pitSettings encryption is enabled but consistent with automation config
    // 2b. pitSettings encryption is enabled but different with automation config
    return !updatedCmk.equals(pitSettingCmk);
  }

  protected static Pair<List<PlannedAction>, List<AuditDescription>> getDiskBackupActionForCluster(
      final ClusterDescription pClusterDescription,
      final PlannedActionFactory pActionFactory,
      final CpsSnapshotEngine pCPSSnapshotEngine,
      final Group pGroup,
      final NDSGroup pNDSGroup,
      final PlanningType pPlanningType,
      final Logger pLogger) {
    if (isEncryptionAtRestEnabledAndInvalid(pNDSGroup, pClusterDescription)) {
      pLogger.info(
          "Cluster has encryption at rest enabled, and the provider settings are no longer valid."
              + " No backup actions will be processed.");
      return null;
    }
    if (pCPSSnapshotEngine.shouldTakeSnapshotNow(pGroup, pClusterDescription, pPlanningType)) {
      return pCPSSnapshotEngine.getTakeDiskBackupActions(pClusterDescription, pActionFactory);
    } else {
      return null;
    }
  }

  protected static List<PlannedAction> getTenantRestoreActions(
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final TenantRestore pTenantRestore,
      final PlannedActionFactory pActionFactory,
      final Logger pLogger,
      final FlexTenantMigration pFlexTenantMigration) {
    if (isEncryptionAtRestEnabledAndInvalid(pNDSGroup, pCluster.getClusterDescription())) {
      pLogger.info(
          "Cluster has encryption at rest enabled, and the provider settings are no longer valid."
              + " No tenant restore actions will be processed.");
      return List.of();
    }

    // Do not create a tenant restore action if:
    // 1. The restore action is not applicable - there is no tenant restore for this target cluster
    // and the cluster does not need a restore.
    // 2. The target cluster has running backup restore jobs.
    // 3. Target cluster is delete requested or deleted.
    // 4. Target cluster is currently migrating/rolling back to flex
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    final boolean m0ClusterIsBeingResumed =
        clusterDescription.isFreeTenantCluster()
            && ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
                .isNeedsUnpauseTenantRestore();
    final boolean restoreActionIsApplicable = pTenantRestore != null || m0ClusterIsBeingResumed;
    if (!restoreActionIsApplicable
        || !clusterDescription.getRestoreJobIds().isEmpty()
        || clusterDescription.isDeleteRequested()
        || ClusterDescription.State.DELETED.equals(clusterDescription.getState())
        || pFlexTenantMigration != null) {
      return List.of();
    }

    if (clusterDescription.isServerlessTenantCluster()) {
      return List.of();
    }

    final List<PlannedAction> actions = new ArrayList<>();

    // M0 resume actions - prioritized over backup restore actions
    if (m0ClusterIsBeingResumed) {
      actions.add(pActionFactory.forDoUnpauseTenantRestoreMove(clusterDescription));
      return actions;
    }

    // M2/M5 and flex backup restore actions
    if (pTenantRestore.getFinishedDate() == null) {
      if (clusterDescription.isFlexOrSharedTenantCluster()) {
        actions.add(pActionFactory.forDoTenantToTenantRestore(pTenantRestore));
      } else {
        actions.add(pActionFactory.forDoTenantRestore(pTenantRestore));
      }
    }
    return actions;
  }

  private static BSONTimestamp getLastWriteOpTimeForHost(
      final HostCluster pHostCluster, final Host pHost, final Logger pLogger) {
    if (pHostCluster != null && pHost != null) {
      final Optional<Member> member = pHostCluster.getReplicaSetMemberForHostId(pHost.getId());
      if (member.isPresent()) {
        return member.get().getLastWriteOpTime();
      } else {
        pLogger.error("Failed to find replica set member state for host ({})", pHost.getName());
      }
    }
    return null;
  }

  private static Optional<ReplicaSetMember> getReplicaSetMember(
      final AutomationConfig pAutomationConfig,
      final ReplicaSetHardware pReplicaSetHardware,
      final InstanceHardware pInstanceHardware) {
    return pAutomationConfig
        .getDeployment()
        .getReplicaSetByName(pReplicaSetHardware.getRsId())
        .flatMap(rs -> rs.getMember(pInstanceHardware.getMemberIndex()));
  }

  @VisibleForTesting
  static Pair<List<PlannedAction>, Boolean> getMachineActions(
      final List<Host> pGroupHosts,
      final List<HostCluster> pHostClusters,
      final Cluster pCluster,
      final NDSGroup pNDSGroup,
      final PlannedActionFactory pActionFactory,
      final Logger pLogger,
      final AutomationConfig pAutomationConfig,
      final boolean pPrioritizePrimaryFeatureFlagOn,
      final Map<ObjectId, ClusterDescriptionProcessArgs> pProcessArgsMappedByCluster,
      final MachineActionsDecisions.Builder pMachineActionsDecisionsBuilder,
      final Map<ObjectId, CloudProviderAvailability> pContainerIdToAvailabilityMap,
      final Set<CloudProvider> pCloudProvidersWithAZFF,
      final boolean pReduceShutdownTimeFeatureFlagOn,
      final boolean pReduceShutdownTimeHealRepairFeatureFlagOn) {

    final List<PlannedAction> actions = new ArrayList<>();
    // this boolean should be set when app services needs to be alerted of a cluster change that
    // it needs to respond to.  When alerted, app services will re-poll the cluster connection
    // information, as well as cluster size information. Cluster size is used to update app limits.
    // It is unclear why, but app services also needs to respond when the cluster upgrades from
    // shared tier to dedicated.  This is related to change stream issues with triggers.  Reach out
    // to the app services team to get input on new actions if you suspect they might indicate that
    // app services needs to refresh.
    boolean hasActionJustifyingBAASRefresh = false;
    final Map<String, Host> hostLookup = PlanResultUtil.getNameToHostMapWithRecentPing(pGroupHosts);

    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    final boolean isEncryptionAtRestEnabledAndInvalid =
        isEncryptionAtRestEnabledAndInvalid(pNDSGroup, clusterDescription);

    // Check if we have any "delete", "requested reboot", or "update config" actions present. These
    // are the only valid actions in the case that encryption at rest is enabled and invalid. If
    // none of these actions are present, then bail out.
    if (isEncryptionAtRestEnabledAndInvalid) {
      final boolean hasPermittedActions =
          pCluster.getReplicaSets().stream()
              .flatMap(ReplicaSetHardware::getAllHardware)
              .map(InstanceHardware::getAction)
              .anyMatch(INVALID_ENCRYPTION_AT_REST_PERMITTED_ACTIONS::contains);
      if (!hasPermittedActions) {
        pLogger.warn(
            "Cluster has encryption at rest enabled, and the provider settings are no longer"
                + " valid. Only the following actions are permitted: {}.",
            INVALID_ENCRYPTION_AT_REST_PERMITTED_ACTIONS.stream()
                .map(InstanceHardware.Action::name)
                .collect(Collectors.joining(", ")));
        return Pair.of(List.of(), hasActionJustifyingBAASRefresh);
      }
    }

    for (final ReplicaSetHardware replicaSet : pCluster.getReplicaSets()) {

      final List<PlannedAction> replSetActions = new ArrayList<>();
      boolean hasPrimaryOrSecondary = false;
      int destructiveHealCount = 0;
      int nondestructiveHealCount = 0;
      int reconfigureCount = 0;

      final Optional<ReplicationSpec> matchingSpecOpt =
          clusterDescription.getReplicationSpecById(replicaSet.getReplicationSpecId());

      // If there is no matching replication spec for a shard replica set then the only expected
      // action on the hardware is DELETE if the hardware is provisioned and NONE if it is not
      // provisioned. Everything else is an unexpected error
      // Note: matchingSpecOpt can also be not present for a config shard that is transitioning to
      // dedicated config and the original shard spec has been deleted, or if the config shard is
      // switching zones.
      if (matchingSpecOpt.isEmpty() && !replicaSet.containsConfigData()) {
        for (final InstanceHardware instanceHardware : replicaSet.getAllHardware().toList()) {
          switch (instanceHardware.getAction()) {
            case DELETE:
              // using empty tags because we can't get RegionName here since matchingSpecOpt is
              // empty
              replSetActions.add(
                  pActionFactory.forDestroyMachine(Map.of(), replicaSet, instanceHardware));
              break;
            default:
              if (instanceHardware.getAction() == InstanceHardware.Action.NONE
                  && !instanceHardware.isProvisioned()) {
                break;
              }
              throw new IllegalStateException(
                  String.format(
                      "Expected action DELETE for hardwares with no matching replication spec."
                          + " Found %s",
                      instanceHardware.getAction().name()));
          }
        }
        actions.addAll(replSetActions);
        continue;
      }
      final ReplicationSpec matchingSpec = matchingSpecOpt.get();

      final Map<ObjectId, RegionConfig> instanceIdToRegionConfig =
          replicaSet.getInstanceIdToRegionConfigMap(matchingSpec.getRegionConfigs());
      final HostCluster hostCluster =
          pHostClusters.stream()
              .filter(hc -> hc.getReplicaSetIds().contains(replicaSet.getRsId()))
              .findAny()
              .orElse(null);

      for (final InstanceHardware instanceHardware : replicaSet.getAllHardware().toList()) {
        // If encryption at rest is enabled and invalid, filter all actions except "delete",
        // "requested reboot", and "update config".
        if (isEncryptionAtRestEnabledAndInvalid
            && !INVALID_ENCRYPTION_AT_REST_PERMITTED_ACTIONS.contains(
                instanceHardware.getAction())) {
          continue;
        }

        final CloudProviderContainer desiredInstanceContainer;
        final RegionName desiredRegion;
        final Optional<RegionConfig> regionConfigOpt =
            Optional.ofNullable(instanceIdToRegionConfig.get(instanceHardware.getInstanceId()));

        if (regionConfigOpt.isPresent()) {
          final Optional<CloudProviderContainer> container =
              pNDSGroup.getCloudProviderContainer(
                  regionConfigOpt.get().getCloudProvider(),
                  regionConfigOpt.get().getRegionName(),
                  clusterDescription.getName());
          final boolean isUnderCompaction =
              clusterDescription.isFreeTenantCluster()
                  && ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
                      .isUnderCompaction();

          if (container.isEmpty()
              && !clusterDescription.isAutomaticallyPaused()
              && !isUnderCompaction) {
            throw new IllegalStateException("Cloud provider container does not exist for cluster");
          }

          desiredRegion = regionConfigOpt.get().getRegionName();
          desiredInstanceContainer = container.orElse(null);
        } else {
          desiredRegion = null;
          desiredInstanceContainer = null;
        }

        final boolean isGoalStateCapacityConstrained =
            isGoalStateCapacityConstrained(
                clusterDescription,
                replicaSet,
                instanceHardware,
                desiredInstanceContainer,
                pContainerIdToAvailabilityMap,
                pCloudProvidersWithAZFF);

        final Host host =
            instanceHardware.getHostnameForAgents().isEmpty()
                ? null
                : hostLookup.get(instanceHardware.getHostnameForAgents().get());
        hasPrimaryOrSecondary =
            hasPrimaryOrSecondary
                || instanceHardware.isForcePlanningPrimary()
                || (host != null && (host.getIsPrimary() || host.getIsSecondary()));

        final Map<String, String> tags =
            NDSMoveTags.builder()
                .setCloudProvider(regionConfigOpt.map(RegionConfig::getCloudProvider).orElse(null))
                .setRegionName(regionConfigOpt.map(RegionConfig::getRegionName).orElse(null))
                .build();
        switch (instanceHardware.getAction()) {
          case CREATE:
            replSetActions.add(
                pActionFactory.forCreateMachine(
                    replicaSet,
                    instanceHardware,
                    desiredInstanceContainer.getId(),
                    tags,
                    getReplicaSetMember(pAutomationConfig, replicaSet, instanceHardware)
                        .orElse(null),
                    regionConfigOpt
                        .map(RegionConfig::getCloudProvider)
                        .filter(pCloudProvidersWithAZFF::contains)
                        .isPresent()));
            hasActionJustifyingBAASRefresh = true;
            break;
          case DELETE:
            hasActionJustifyingBAASRefresh = true;
            replSetActions.add(
                pActionFactory.forDestroyMachine(tags, replicaSet, instanceHardware));
            break;
          case SYNC_PAUSE_STATE:
            replSetActions.add(
                pActionFactory.forPauseMachine(
                    replicaSet,
                    instanceHardware,
                    host,
                    clusterDescription.isPaused(),
                    isGoalStateCapacityConstrained));
            pMachineActionsDecisionsBuilder.setWillSyncPauseState(clusterDescription.isPaused());
            break;
          case STOP_START_VM:
            replSetActions.add(
                pActionFactory.forStopStartInstance(
                    replicaSet, instanceHardware, isGoalStateCapacityConstrained));
            break;
          case OS_SWAP:
            hasActionJustifyingBAASRefresh = true;
            reconfigureCount++;
            replSetActions.add(
                pActionFactory.forOSSwap(
                    clusterDescription,
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger),
                    tags,
                    regionConfigOpt
                        .map(RegionConfig::getCloudProvider)
                        .filter(pCloudProvidersWithAZFF::contains)
                        .isPresent()));
            pMachineActionsDecisionsBuilder.setWillSwapOS(
                instanceHardware.getCloudProvider(), instanceHardware.isNVMe());
            break;
          case DISK_COMPACTION_RESYNC:
            hasActionJustifyingBAASRefresh = false;
            reconfigureCount++;
            replSetActions.add(
                pActionFactory.forDiskCompactionResyncMachine(
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            pMachineActionsDecisionsBuilder.setWillDiskCompactInitialSync(replicaSet.getRsId());
            break;
          case RECONFIGURE:
            final Float priority =
                Optional.ofNullable(pAutomationConfig)
                    .filter(o -> pPrioritizePrimaryFeatureFlagOn)
                    .flatMap(a -> getReplicaSetMember(a, replicaSet, instanceHardware))
                    .map(ReplicaSetMember::getPriority)
                    .orElse(null);
            reconfigureCount++;
            replSetActions.addAll(
                pActionFactory.forUpdateMachine(
                    pNDSGroup,
                    clusterDescription,
                    replicaSet,
                    instanceHardware,
                    desiredRegion,
                    desiredInstanceContainer,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger),
                    tags,
                    pLogger,
                    pProcessArgsMappedByCluster,
                    priority,
                    pPrioritizePrimaryFeatureFlagOn,
                    isGoalStateCapacityConstrained,
                    pContainerIdToAvailabilityMap,
                    regionConfigOpt
                        .map(RegionConfig::getCloudProvider)
                        .filter(pCloudProvidersWithAZFF::contains)
                        .isPresent()));
            hasActionJustifyingBAASRefresh = true;
            break;
          case REQUESTED_PROCESS_RESTART:
            reconfigureCount++;

            // add a move for each requested process type
            instanceHardware.getProcessRestartRequestedDates().entrySet().stream()
                .filter(e -> e.getValue() != null)
                .filter(e -> e.getValue().before(new Date()))
                .map(Map.Entry::getKey)
                .forEach(
                    processType ->
                        replSetActions.add(
                            pActionFactory.forRestartProcess(
                                clusterDescription,
                                replicaSet,
                                instanceHardware,
                                host,
                                processType,
                                getLastWriteOpTimeForHost(hostCluster, host, pLogger))));
            break;
          case REQUESTED_REBOOT:
            reconfigureCount++;
            if (isEncryptionAtRestEnabledAndInvalid) {
              replSetActions.add(
                  pActionFactory.forRestartServerWithoutProcesses(
                      clusterDescription,
                      replicaSet,
                      instanceHardware,
                      host,
                      true,
                      getLastWriteOpTimeForHost(hostCluster, host, pLogger),
                      isGoalStateCapacityConstrained));
            } else {
              replSetActions.add(
                  pActionFactory.forRestartServer(
                      clusterDescription,
                      replicaSet,
                      instanceHardware,
                      host,
                      true,
                      getLastWriteOpTimeForHost(hostCluster, host, pLogger),
                      isGoalStateCapacityConstrained));
            }
            break;
          case HEAL_RESTART:
            nondestructiveHealCount++;
            replSetActions.add(
                pActionFactory.forRestartServer(
                    clusterDescription,
                    replicaSet,
                    instanceHardware,
                    host,
                    false,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger),
                    isGoalStateCapacityConstrained));
            break;
          case HEAL_REPAIR:
            destructiveHealCount++;
            replSetActions.add(
                pActionFactory.forRepairMachine(
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger),
                    tags,
                    regionConfigOpt
                        .map(RegionConfig::getCloudProvider)
                        .filter(pCloudProvidersWithAZFF::contains)
                        .isPresent(),
                    false,
                    pReduceShutdownTimeHealRepairFeatureFlagOn));
            break;
          case UPDATE_CONFIG:
            reconfigureCount++;
            replSetActions.add(
                pActionFactory.forUpdateConfig(
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
          case ROTATE_AGENT_API_KEY:
            reconfigureCount++;
            replSetActions.add(
                pActionFactory.forRotateAgentAPIKeys(
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
          case OS_POLICY_UPDATE_AND_REBOOT:
            reconfigureCount++;
            replSetActions.add(
                pActionFactory.forUpdateOSPolicy(
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
          case RESERVE_IPAM_IP:
            replSetActions.add(
                pActionFactory.forReserveIpamIp(
                    replicaSet.getClusterName(),
                    instanceHardware.getInstanceId(),
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
          case SWAP_FUTURE_IP:
            reconfigureCount++;
            replSetActions.add(
                pActionFactory.forAwsSwapIpMove(
                    Action.SWAP_FUTURE_IP,
                    replicaSet.getClusterName(),
                    instanceHardware.getInstanceId(),
                    replicaSet,
                    instanceHardware,
                    host,
                    tags,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
          case SWAP_PAST_IP:
            reconfigureCount++;
            replSetActions.add(
                pActionFactory.forAwsSwapIpMove(
                    Action.SWAP_PAST_IP,
                    replicaSet.getClusterName(),
                    instanceHardware.getInstanceId(),
                    replicaSet,
                    instanceHardware,
                    host,
                    tags,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
          case RELEASE_AWS_IP:
            replSetActions.add(
                pActionFactory.forAwsReleaseAwsIpMove(
                    replicaSet.getClusterName(),
                    instanceHardware.getInstanceId(),
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
          case HEAL_POWER_CYCLE:
          case HEAL_POWER_CYCLE_CRITICAL:
            nondestructiveHealCount++;
            replSetActions.add(
                pActionFactory.forPowerCycleMachine(
                    tags,
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger),
                    isGoalStateCapacityConstrained,
                    pReduceShutdownTimeFeatureFlagOn
                        && instanceHardware.getAction()
                            == InstanceHardware.Action.HEAL_POWER_CYCLE_CRITICAL));
            break;
          case HEAL_RESYNC:
            destructiveHealCount++;
            replSetActions.add(
                pActionFactory.forResyncMachine(
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
          case HEAL_RESYNC_LOGICAL:
          case HEAL_RESYNC_FILECOPY:
            destructiveHealCount++;
            replSetActions.add(
                pActionFactory.forHealResyncMachine(
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
          case HEAL_RESYNC_OIS:
            destructiveHealCount++;
            replSetActions.add(
                pActionFactory.forRepairMachine(
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger),
                    tags,
                    regionConfigOpt
                        .map(RegionConfig::getCloudProvider)
                        .filter(pCloudProvidersWithAZFF::contains)
                        .isPresent(),
                    true,
                    false));
            break;
          case REQUESTED_RELOAD_SSL_ON_PROCESSES:
            reconfigureCount++;
            replSetActions.add(
                pActionFactory.forReloadSslOnProcesses(
                    clusterDescription,
                    replicaSet,
                    instanceHardware,
                    host,
                    getLastWriteOpTimeForHost(hostCluster, host, pLogger)));
            break;
        }
      }
      if (nondestructiveHealCount + destructiveHealCount + reconfigureCount > 0) {
        // mongoDBUriHosts is only populated after cluster provision
        if (!hasPrimaryOrSecondary) {
          if (clusterDescription.getMongoDBUriHosts().length == 0) {
            pLogger.info(
                "Allow replacement plan for cluster {} because it was never fully provisioned.",
                clusterDescription.getName());
          } else if (isEncryptionAtRestEnabledAndInvalid) {
            pLogger.info(
                "Allow replacement plan for cluster {} with encryption at rest enabled but"
                    + " invalid.",
                clusterDescription.getName());
          } else {
            throw new UnableToPlanException(
                String.format(
                    "Cannot find primary or secondary for replica set (%s). Skipping"
                        + " replacement moves",
                    replicaSet.getId()));
          }
        }

        final boolean hasDiskModifications =
            replSetActions.stream()
                .anyMatch(
                    plannedAction ->
                        plannedAction.getMoveType().equals(MoveType.DISK_MODIFICATION));

        final boolean isScalingOnlyPlan =
            replSetActions.stream()
                .flatMap(
                    plannedAction ->
                        plannedAction.getMoves() != null
                            ? Arrays.stream(plannedAction.getMoves())
                            : Stream.of())
                .allMatch(PlannedActionFactory::moveQualifiesForScalingPlan);

        for (InstanceHardware instanceHardware : replicaSet.getAllHardware().toList()) {
          if (instanceHardware.getAction() == InstanceHardware.Action.DELETE
              || instanceHardware.getAction() == InstanceHardware.Action.SYNC_PAUSE_STATE
              || instanceHardware.getAction() == InstanceHardware.Action.RECONFIGURE
              || instanceHardware.getAction() == InstanceHardware.Action.REQUESTED_REBOOT
              || instanceHardware.getAction() == InstanceHardware.Action.REQUESTED_PROCESS_RESTART
              || instanceHardware.getAction() == InstanceHardware.Action.HEAL_REPAIR
              || instanceHardware.getAction() == InstanceHardware.Action.HEAL_POWER_CYCLE
              || instanceHardware.getAction() == InstanceHardware.Action.HEAL_POWER_CYCLE_CRITICAL
              || instanceHardware.getAction() == InstanceHardware.Action.HEAL_RESYNC
              || instanceHardware.getAction() == InstanceHardware.Action.HEAL_RESYNC_OIS
              || instanceHardware.getAction() == InstanceHardware.Action.HEAL_RESYNC_FILECOPY
              || instanceHardware.getAction() == InstanceHardware.Action.HEAL_RESYNC_LOGICAL
              || instanceHardware.getAction() == InstanceHardware.Action.HEAL_RESTART
              || instanceHardware.getAction() == InstanceHardware.Action.UPDATE_CONFIG
              || instanceHardware.getAction() == InstanceHardware.Action.ROTATE_AGENT_API_KEY
              || instanceHardware.getAction()
                  == InstanceHardware.Action.REQUESTED_RELOAD_SSL_ON_PROCESSES
              || instanceHardware.getAction() == InstanceHardware.Action.STOP_START_VM) {
            continue;
          }

          // Don't need to check new node health if there are disk modifications and this is a net
          // new node to the replica set
          // todo CLOUDP-338266: revisit whether hasDiskModifications condition can be removed
          if (instanceHardware.getAction().equals(InstanceHardware.Action.CREATE)
              && hasDiskModifications
              && ClusterDescriptionUtil.getInstanceReplicaSetMember(
                      pAutomationConfig, clusterDescription, replicaSet, instanceHardware)
                  .isEmpty()) {
            continue;
          }

          // Don't need to wait for unused hardware
          if (!instanceIdToRegionConfig.containsKey(instanceHardware.getInstanceId())) {
            continue;
          }

          // Don't need to wait for hardware with processes that have been shutdown because of
          // revoked KMS keys
          if (isEncryptionAtRestEnabledAndInvalid) {
            continue;
          }

          final boolean canSkipAnalyticsNodeWFMHM =
              replicaSet
                  .getNodeTypeForInstance(clusterDescription, instanceHardware.getInstanceId())
                  .map(ntf -> isScalingOnlyPlan && ntf == NodeType.ANALYTICS)
                  .orElse(false);
          if (canSkipAnalyticsNodeWFMHM) {
            continue;
          }

          replSetActions.add(pActionFactory.forWaitForHealthyMachine(replicaSet, instanceHardware));
        }
      }

      // This is a check that deserves historical context. We guard against replacing all the nodes
      // in this replica set of the cluster, even though all moves triggered by a heal action would
      // have a WaitForMachineHealthyMove scheduled. This is for two paranoid reasons:
      // 1) This is a rare event and there could be a bug that erroneously resulted in this.
      // 2) There could be a bug that circumvents the WaitFormachineHealthyMove, wherein we subject
      // the cluster to potential data loss / downtime.
      // We should continue to keep this check in until we can safeguard against the above in
      // equally invulnerable fashion.
      if (destructiveHealCount >= matchingSpec.getTotalNodes()) {
        throw new UnableToPlanException(
            String.format("Cannot repair an entire replica set (%s)", replicaSet.getId()));
      }

      actions.addAll(replSetActions);
    }
    return Pair.of(actions, hasActionJustifyingBAASRefresh);
  }

  protected static List<PlannedAction> getRestoreActions(
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final BackupRestoreJobPlanUnit pRestoreTarget,
      final PlannedActionFactory pActionFactory,
      final CpsSnapshotEngine pCpsSnapshotEngine,
      final FlexTenantMigration pFlexTenantMigration,
      final Logger pLogger) {
    if (isEncryptionAtRestEnabledAndInvalid(pNDSGroup, pCluster.getClusterDescription())) {
      pLogger.info(
          "Cluster has encryption at rest enabled, and the provider settings are no longer valid."
              + " No restore actions will be processed.");
      return List.of();
    }

    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    if (clusterDescription.isDeleteRequested()
        || clusterDescription.getState().equals(ClusterDescription.State.DELETED)
        || clusterDescription.isPaused()) {
      return Collections.emptyList();
    }

    final List<ObjectId> restoreJobIds = pCluster.getClusterDescription().getRestoreJobIds();
    if (restoreJobIds.isEmpty()) {
      return Collections.emptyList();
    }

    if (pRestoreTarget != null && pRestoreTarget.getPlanStrategy().isSLS()) {
      return Collections.emptyList();
    }

    if (clusterDescription.isServerlessTenantCluster()) {
      if (pFlexTenantMigration != null) {
        pLogger.info(
            "Cluster has an in progress serverless to flex migration. No restore actions wil be"
                + " processed.");
        return Collections.emptyList();
      }
      return pActionFactory.forIncomingServerlessRestore(
          pCluster.getClusterDescription(),
          Optional.ofNullable(pRestoreTarget)
              .map(BackupRestoreJobPlanUnit::getPlanStrategy)
              .map(CpsRestoreMetadata.StrategyName::isTenantUpgradeToServerless)
              .orElse(false));
    }

    return pActionFactory.forSyncRestoreConfig(
        pCluster.getClusterDescription(), pCpsSnapshotEngine);
  }

  protected static List<PlannedAction> getBiConnectorActions(
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final List<HostCluster> pHostClusters,
      final PlannedActionFactory pActionFactory,
      final Logger pLogger,
      final boolean removeIcmpPing) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();

    if (clusterDescription.getState().equals(ClusterDescription.State.DELETED)
        || clusterDescription.isPaused()) {
      return List.of();
    }

    if (clusterDescription.isDeleteRequested()) {
      if (Optional.ofNullable(clusterDescription.getBiConnector())
          .map(BiConnector::isEnabled)
          .orElse(false)) {
        return List.of(pActionFactory.forSyncBiConnector(clusterDescription));
      } else {
        return List.of();
      }
    }

    final boolean biConnectorEnabledOnCluster = clusterDescription.getBiConnector().isEnabled();
    final boolean biConnectorIsAnalytics =
        clusterDescription
            .getBiConnector()
            .getReadPreference()
            .equals(BiConnectorReadPreference.ANALYTICS);

    final List<InstanceHardware> biConnectorInstances =
        pCluster.getReplicaSets().stream()
            .flatMap(rsh -> rsh.getHardware().stream())
            .filter(InstanceHardware::isProvisioned)
            .filter(ih -> ih.getBiConnector().isEnabled())
            .collect(Collectors.toList());

    final boolean biConnectorShouldRelocateForAnalytics =
        biConnectorShouldRelocateForAnalytics(
            pCluster, biConnectorInstances, biConnectorIsAnalytics);

    final boolean biConnectorEnabledOnInstance = biConnectorInstances.size() > 0;

    final boolean biConnectorHostUnavailable =
        biConnectorInstances.stream()
            .noneMatch(ih -> ih.canHostBiConnector(pLogger, removeIcmpPing));

    final boolean biConnectorReadPreferenceChanged =
        biConnectorEnabledOnInstance
            && !biConnectorInstances.stream()
                .filter(ih -> ih.getBiConnector().isEnabled())
                .map(ih -> ih.getBiConnector().getReadPreference())
                .allMatch(rp -> rp.equals(clusterDescription.getBiConnector().getReadPreference()));

    final boolean biConnectorHardwareAvailable =
        biConnectorHardwareAvailable(
            pNDSGroup,
            pCluster,
            pHostClusters,
            biConnectorEnabledOnCluster,
            biConnectorIsAnalytics,
            pLogger,
            removeIcmpPing);

    final boolean shouldEnableBiConnector =
        biConnectorEnabledOnCluster && !biConnectorEnabledOnInstance;
    final boolean shouldUpdateBiConnector =
        biConnectorEnabledOnCluster
            && (biConnectorHostUnavailable
                || biConnectorReadPreferenceChanged
                || biConnectorShouldRelocateForAnalytics);

    if ((shouldEnableBiConnector || shouldUpdateBiConnector) && !biConnectorHardwareAvailable) {
      pLogger.info(
          "BI Connector on cluster {} needs syncing but there is currently no healthy eligible"
              + " hardware.",
          pCluster.getClusterDescription().getName());
      return Collections.emptyList();
    }

    final boolean shouldDisableBiConnector =
        !biConnectorEnabledOnCluster && biConnectorEnabledOnInstance;
    final boolean needsSync = clusterDescription.getBiConnector().getNeedsSync().isPresent();

    final boolean shouldAddBiConnectorAction =
        shouldEnableBiConnector || shouldUpdateBiConnector || shouldDisableBiConnector || needsSync;

    if (shouldAddBiConnectorAction) {
      pLogger.info(
          "BI Connector requires action.  (biConnectorEnabledOnCluster: {},"
              + " biConnectorEnabledOnInstance: {}, biConnectorIsAnalytics: {},"
              + " biConnectorShouldRelocateForAnalytics: {}, biConnectorHostUnavailable:"
              + " {}, biConnectorReadPreferenceChanged: {}, biConnectorHardwareAvailable: {},"
              + " needsSync: {}, bicInstances:"
              + " [{}])",
          biConnectorEnabledOnCluster,
          biConnectorEnabledOnInstance,
          biConnectorIsAnalytics,
          biConnectorShouldRelocateForAnalytics,
          biConnectorHostUnavailable,
          biConnectorReadPreferenceChanged,
          biConnectorHardwareAvailable,
          needsSync,
          biConnectorInstances.stream()
              .map(InstanceHardware::getHostnames)
              .map(h -> h.getPublicHostname().or(h::getLegacyHostname))
              .flatMap(Optional::stream)
              .collect(Collectors.joining(",")));

      return List.of(pActionFactory.forSyncBiConnector(clusterDescription));
    }

    return List.of();
  }

  protected static boolean biConnectorShouldRelocateForAnalytics(
      final Cluster pCluster,
      final List<InstanceHardware> pBIConnectorInstances,
      final boolean pBIConnectorIsAnalytics) {
    if (!pBIConnectorIsAnalytics) {
      return false;
    }

    final Set<ObjectId> analyticsInstanceIds =
        pCluster.getReplicaSets().stream()
            .filter(ReplicaSetHardware::containsShardData)
            .flatMap(
                rsh -> {
                  final List<RegionConfig> regionConfigs =
                      pCluster
                          .getClusterDescription()
                          .getReplicationSpecById(rsh.getReplicationSpecId())
                          .orElseThrow(MissingReplicationSpecException::new)
                          .getRegionConfigs();
                  return rsh.getAnalyticsHardware(regionConfigs).stream();
                })
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toSet());
    final Set<ObjectId> biConnectorInstanceIds =
        pBIConnectorInstances.stream()
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toSet());
    return !analyticsInstanceIds.containsAll(biConnectorInstanceIds);
  }

  protected static boolean biConnectorHardwareAvailable(
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final List<HostCluster> pHostClusters,
      final boolean pBIConnectorEnabledOnCluster,
      final boolean pBIConnectorIsAnalytics,
      final Logger pLogger,
      final boolean removeIcmpPing) {
    if (!pBIConnectorEnabledOnCluster) {
      return false;
    }

    final List<InstanceHardware> eligibleHosts;
    if (pBIConnectorIsAnalytics) {
      pLogger.debug("Checking for eligible analytics node for BI Connector.");
      eligibleHosts =
          pCluster.getReplicaSets().stream()
              .filter(rsh -> rsh.containsShardData())
              .flatMap(
                  rsh -> {
                    final List<RegionConfig> regionConfigs =
                        pCluster
                            .getClusterDescription()
                            .getReplicationSpecById(rsh.getReplicationSpecId())
                            .orElseThrow(MissingReplicationSpecException::new)
                            .getRegionConfigs();
                    return rsh.getAnalyticsHardware(regionConfigs).stream();
                  })
              .filter(InstanceHardware::isProvisioned)
              .filter(ih -> ih.getHostnameForAgents().isPresent())
              .filter(ih -> ih.canHostBiConnector(pLogger, removeIcmpPing))
              .collect(Collectors.toList());
    } else {
      final String clusterName = pCluster.getClusterDescription().getName();
      // New clusters can always host BI Connector
      if (pCluster.getReplicaSets().stream()
          .flatMap(rsh -> rsh.getHardware().stream())
          .allMatch(ih -> ih.getAction() == Action.CREATE)) {
        pLogger.debug(
            "Cluster {} is newly created. Assuming any secondary can host the BI Connector",
            clusterName);
        return true;
      }

      // For existing cluster, attempt to find a healthy secondary to host the BI Connector
      pLogger.debug("Checking for eligible secondary node for BI Connector.");
      final Set<String> secondaryHostnames =
          pHostClusters.stream()
              .filter(hc -> hc.getGroupId().equals(pNDSGroup.getGroupId()) && hc.isReplicaSet())
              .flatMap(hc -> Optional.ofNullable(hc.getReplicaSets()).orElse(Set.of()).stream())
              .flatMap(rs -> rs.getMembers().stream())
              .filter(Objects::nonNull)
              .filter(m -> m.getState() == State.SECONDARY)
              .map(ReplicaSet.Member::getHost)
              .map(BaseHostUtils::getHostnameWithoutPort)
              .filter(
                  h -> h.matches(NDSDefaults.getHostMatchRegex(pCluster.getClusterDescription())))
              .collect(Collectors.toSet());

      if (secondaryHostnames.isEmpty()) {
        pLogger.debug("Cannot find any instance in SECONDARY state for cluster {}.", clusterName);
        return false;
      }

      eligibleHosts =
          pCluster.getReplicaSets().stream()
              .flatMap(rsh -> rsh.getHardware().stream())
              .filter(InstanceHardware::isProvisioned)
              // Never put a BI Connector on a primary so we exclude them from the eligible list of
              // hardware
              .filter(ih -> secondaryHostnames.contains(ih.getHostnameForAgents().get()))
              .filter(
                  ih ->
                      !InstanceHardware.Action.BI_CONNECTOR_EXCLUDE_STATES.contains(ih.getAction()))
              .collect(Collectors.toList());
    }
    eligibleHosts.forEach(
        ih ->
            pLogger.debug(
                "Instance with hostname={} and action={} can host BI Connector.",
                ih.getHostnameForAgents().get(),
                ih.getAction().name()));
    return eligibleHosts.size() > 0;
  }

  @VisibleForTesting
  static boolean clusterNeedsPublish(
      final Cluster pCluster, final PlannedAction pMongoDBConfigAction) {

    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    if (clusterDescription.getNeedsMongoDBConfigPublishAfter().isEmpty()) {
      return false;
    }
    return Arrays.stream(pMongoDBConfigAction.getMoves())
        .anyMatch(
            m ->
                (m instanceof ProcessMongoDBConfigMove pmdbc)
                    && pmdbc
                        .getMongoDBConfigType()
                        .equals(clusterDescription.getMongoDBConfigType()));
  }

  static boolean clusterNeedsPriorityReset(
      final Cluster pCluster, final Group pGroup, final AppSettings pAppSettings) {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    final Optional<Date> retentionDate =
        clusterDescription.getNeedsPrioritiesResetForPriorityTakeoverAfter();
    if (retentionDate.isEmpty()) {
      return false;
    }

    return retentionDate.get().before(new Date())
        && FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_AUTOMATE_PRIORITY_TAKEOVER, pAppSettings, null, pGroup);
  }

  static boolean m0ClusterNeedsNDSAccessRevoked(final ClusterDescription pClusterDescription) {
    if (!pClusterDescription
            .getInstanceSizes(NodeType.ELECTABLE)
            .equals(Set.of(FreeInstanceSize.M0))
        || pClusterDescription.isPaused()) {
      return false;
    }

    final FreeTenantProviderOptions providerOptions =
        (FreeTenantProviderOptions) pClusterDescription.getFreeTenantProviderOptions();
    final Date ndsAccessRevoked = providerOptions.getNdsAccessRevokedDate();
    if (ndsAccessRevoked != null) {
      return false;
    }

    // if the cluster is undergoing compaction, we will not check auto pause at all to avoid
    // over-stepping
    if (providerOptions.isUnderCompaction()) {
      return false;
    }

    return NDSTenantPauseUtil.hasUserNotifiedAboutPauseDatePassedEmailWarningThreshold(
        pClusterDescription.getFreeTenantProviderOptions().getUserNotifiedAboutPauseDate());
  }

  public static Plan getNewPlan(final ObjectId pGroupId) {
    final IPlanContextFactory factory = AppConfig.getInstance(NDSPlanContext.Factory.class);
    return new Plan(pGroupId, factory);
  }

  public static Plan getNewACMEPlan(final ObjectId pGroupId) {
    final IPlanContextFactory factory = AppConfig.getInstance(NDSACMEPlanContext.Factory.class);
    return new Plan(pGroupId, factory);
  }

  protected static class UnableToPlanException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public UnableToPlanException(final String pMessage) {
      super(pMessage);
    }
  }

  private static List<CloudProviderContainer> getContainersForCluster(
      final NDSGroup pNDSGroup, final Cluster pCluster) {
    final Set<CloudProviderContainer> containersForClusterDescription =
        NDSCloudProviderContainerSvc.getContainersInUse(
            pNDSGroup, pCluster.getClusterDescription());

    final Set<CloudProviderContainer> containersForInstanceHardware =
        pCluster.getReplicaSets().stream()
            .flatMap(ReplicaSetHardware::getAllHardware)
            .filter(InstanceHardware::isProvisioned)
            .map(InstanceHardware::getCloudContainerId)
            .map(containerId -> pNDSGroup.getCloudProviderContainer(containerId).get())
            .collect(Collectors.toSet());
    return new ArrayList<>(
        SetUtils.union(containersForClusterDescription, containersForInstanceHardware));
  }

  private static List<DependencyRule> getDependencyRules(
      final boolean pMachineUpdatesBeforeDiskModifications,
      final boolean pAllowAbandoningWaitForMachineHealthyMove,
      final boolean pChainPauseMoves) {
    return Arrays.asList(
        new MongoDBConfigBeforeDeleteMachines(),
        new EnsureConnectivityBeforeDeleteMachines(),
        new MongoDBConfigBeforeEnsureClusterConnectionStrings(),
        new DeleteBackupsBeforeMongoDBConfig(),
        new ExpandBackupConfigAfterMongoDBConfig(),
        new SyncBackupConfigAfterMongoDBConfig(),
        new MachinesHealthyBeforeMongoDBConfig(),
        new NewContainerBeforeContainerConfig(),
        new NewMachinesBeforeMongoDBConfig(),
        new NewMachinesBeforeUpdateMachines(),
        new NewMachinesBeforeEnsureConnectivity(),
        new MongoDBConfigBeforeEnsureConnectivity(),
        new PlannedActionMovesAreChains(),
        new RestoreConfigAroundMongoDBConfig(),
        new PreprocessRestoreBeforeRestore(),
        new ModifyDiskBeforeMongoDBConfig(),
        new ModifyDiskBeforeUpdateMachine(pMachineUpdatesBeforeDiskModifications),
        new MongoDBConfigScaleOutBeforeScaleUp(pMachineUpdatesBeforeDiskModifications),
        new EnsureConnectivityForScaleOutBeforeScaleUp(),
        new UpdatesAndRepairsMustBeRolling(
            pMachineUpdatesBeforeDiskModifications,
            pAllowAbandoningWaitForMachineHealthyMove,
            pChainPauseMoves),
        new OtherMachinesHealthyBeforeSwapOS(),
        new UpdateContainerBeforeDeleteContainer(),
        new ProvisionBiConnectorAfterMongoDBConfig(),
        new SyncBiConnectorAfterClusterResume(),
        new PerformSnapshotLast(),
        new ProvisionMachineBeforeRestore(),
        new TenantBackupRestoreBeforeMongoDBConfig(),
        new TenantUnpauseRestoreAfterMongoDBConfig(),
        new DirectAttachRestoreBeforeCleanUp(),
        new SyncClusterPrivateEndpointBeforeMongoDBConfig(),
        new TransitionConfigServerBeforeMongoDBConfig(),
        new TransitionConfigServerBeforeSyncClusterPrivateEndpoint(),
        new SyncClusterPrivateEndpointBeforeEnsureConnectivity(),
        new DoSnapshotExportMoveBeforeUploadSnapshotExportCompleteFile(),
        new DoSnapshotExportFromSystemClusterBeforeUploadSnapshotExportCompleteFileFromSystemCluster(),
        new ProvisionServerlessLoadBalancerBeforeEnvoy(),
        new DisableServerlessAccessAroundServerlessRestore(),
        new EnvoyInstanceReplacementsUpgradeDowngradeMustBeRolling(),
        new DestroyEnvoyMachineBeforeLoadBalancerDelete(),
        new FastProvisionMachinesBeforeConfig(),
        new ConfigBeforeEnqueueLoadSampleDataset(),
        new ProvisionServerlessNATGatewayBeforeEnvoy(),
        new EndOutageBeforeEverythingElse(),
        new FreeCompactionAfterRevokeAccess(),
        new FlagLastAwsSwapIpMove(),
        new ResetPriorityBeforeMongoDBConfig(),
        new DoPreCopySnapshotMoveBeforeCopySnapshot(),
        new DoCopySnapshotMovesBeforePostCopySnapshot(),
        new DisaggregatedStorageConfigBeforeNewMachines(),
        new CreateContainerBeforeCreateMachineForFreeCluster(),
        new ModifyDiskBeforeUpdateClusterMongotuneConfig(),
        new UpdateMachinesBeforeUpdateClusterMongotuneConfig());
  }

  private static boolean isGoalStateCapacityConstrained(
      final ClusterDescription pClusterDescription,
      final ReplicaSetHardware pReplicaSet,
      final InstanceHardware pInstanceHardware,
      final CloudProviderContainer pContainer,
      final Map<ObjectId, CloudProviderAvailability> pContainerIdToAvailabilityMap,
      final Set<CloudProvider> pCloudProvidersWithAZFF) {
    // These base conditions are checking if we are doing an in-place action
    // any node creation or container swap will result in a replacement with
    // smart capacity selection so we ignore these cases. Also included
    // are basic sanity checks.
    if (pContainer == null
        || !pInstanceHardware.isProvisioned()
        || !pContainer.getId().equals(pInstanceHardware.getCloudContainerId())
        || !pContainerIdToAvailabilityMap.containsKey(pContainer.getId())
        || pInstanceHardware.getPhysicalZoneId().isEmpty()
        || !pCloudProvidersWithAZFF.contains(pContainer.getCloudProvider())) {
      return false;
    }

    final RegionName regionName = pInstanceHardware.getRegion(Optional.of(pContainer));
    final NodeType nodeType =
        pReplicaSet
            .getNodeTypeForInstance(pClusterDescription, pInstanceHardware.getInstanceId())
            .orElse(null);

    if (nodeType == null) {
      return false;
    }

    final HardwareSpec hardwareSpec =
        pClusterDescription
            .getHardwareSpec(pReplicaSet.getReplicationSpecId(), regionName, nodeType)
            .orElse(null);

    if (hardwareSpec == null) {
      return false;
    }

    final CloudProviderAvailability cloudProviderAvailability =
        pContainerIdToAvailabilityMap.get(pContainer.getId());

    final boolean isGoalStateConstrained =
        cloudProviderAvailability.getCapacityDenyList(regionName).stream()
            .filter(
                entry -> entry.getPhysicalZoneId().equals(pInstanceHardware.getPhysicalZoneId()))
            .filter(entry -> entry.getInstanceSize().equals(hardwareSpec.getInstanceSize()))
            .filter(entry -> entry.getInstanceFamily().equals(hardwareSpec.getInstanceFamily()))
            .anyMatch(CapacityDenyListEntry::isUnavailable);

    return isGoalStateConstrained;
  }
}
