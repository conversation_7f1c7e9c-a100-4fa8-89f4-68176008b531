package com.xgen.svc.nds.svc.adl;

import static com.xgen.svc.nds.svc.adl.DataLakeGrpcClient.REQUEST_ID_HEADER;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.protobuf.Any;
import com.google.protobuf.ByteString;
import com.google.protobuf.Int32Value;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.ProtocolStringList;
import com.google.protobuf.Timestamp;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.logging._public.context.LoggingContext;
import com.xgen.cloud.common.metrics._public.svc.NDSPromMetricsSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.util._public.util.LogUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStoreReadConcern;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStoreReadConcern.NDSDataLakeAtlasStoreReadConcernBuilder;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStoreReadPreference;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStoreReadPreference.NDSDataLakeAtlasStoreReadPreferenceBuilder;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStoreReadPreferenceTag;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAzureBlobStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAzurePrivateLinkServiceNames;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeCollection;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeDLSAWSStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeDLSAzureStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeDatabase;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeHTTPStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeInMemoryStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeKafkaStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeOnlineArchiveStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeS3Store;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeS3Store.StorageClass;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeStorage;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeDataProcessRegion;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeView;
import com.xgen.cloud.nds.datalake._public.model.dls.DataSetMetricsResult;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSet;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSetDLS;
import com.xgen.mhouse.backend.grpc.Mhmetrics.GetDataSetMetricsRequest;
import com.xgen.mhouse.backend.grpc.Mhmetrics.GetDataSetMetricsRequest.Builder;
import com.xgen.mhouse.backend.grpc.Mhmetrics.GetDataSetMetricsResponse;
import com.xgen.mhouse.backend.grpc.Models.BlobLocation;
import com.xgen.mhouse.backend.grpc.Models.BlobSourceAzure;
import com.xgen.mhouse.backend.grpc.Models.BlobSourceGCS;
import com.xgen.mhouse.backend.grpc.Models.BlobSourceS3;
import com.xgen.mhouse.backend.grpc.Models.DataSetRequest;
import com.xgen.mhouse.backend.grpc.Models.DeregisterOAFilesRequest;
import com.xgen.mhouse.backend.grpc.Models.DeregisterOAFilesResponse;
import com.xgen.mhouse.backend.grpc.Models.DestroyDataSetsResponse;
import com.xgen.mhouse.backend.grpc.Models.DestroyOADataSetRequest;
import com.xgen.mhouse.backend.grpc.Models.DisableDataSetsResponse;
import com.xgen.mhouse.backend.grpc.Models.DisableOADataSetRequest;
import com.xgen.mhouse.backend.grpc.Models.EnableDataSetsResponse;
import com.xgen.mhouse.backend.grpc.Models.EnableOADataSetRequest;
import com.xgen.mhouse.backend.grpc.Models.GetJobErrorsRequest;
import com.xgen.mhouse.backend.grpc.Models.GetJobErrorsResponse;
import com.xgen.mhouse.backend.grpc.Models.GetJobProgressRequest;
import com.xgen.mhouse.backend.grpc.Models.GetJobProgressResponse;
import com.xgen.mhouse.backend.grpc.Models.InitializeDataSetRequest;
import com.xgen.mhouse.backend.grpc.Models.InitializeDataSetResponse;
import com.xgen.mhouse.backend.grpc.Models.InitializeOADataSetRequest;
import com.xgen.mhouse.backend.grpc.Models.ListDataSetsRequest;
import com.xgen.mhouse.backend.grpc.Models.ListDataSetsResponse;
import com.xgen.mhouse.backend.grpc.Models.Location;
import com.xgen.mhouse.backend.grpc.Models.LocationS3;
import com.xgen.mhouse.backend.grpc.Models.MergeDataSetsRequest;
import com.xgen.mhouse.backend.grpc.Models.MetadataLocation;
import com.xgen.mhouse.backend.grpc.Models.ReconcileDataSetsResponse;
import com.xgen.mhouse.backend.grpc.Models.RegisterOAFilesRequest;
import com.xgen.mhouse.backend.grpc.Models.UploadDataFilesRequest;
import com.xgen.mhouse.backend.grpc.Models.UploadDataFilesResponse;
import com.xgen.mhouse.backend.grpc.StorageServiceGrpc;
import com.xgen.mhouse.common.bson.v1.Bson;
import com.xgen.mhouse.common.tenant.v1.Tenant.Settings;
import com.xgen.mhouse.common.v1.NamespaceOuterClass.Namespace;
import com.xgen.mhouse.common.v1.ProjectId.ProjectID;
import com.xgen.mhouse.common.v1.TenantId;
import com.xgen.mhouse.common.v1.TenantId.TenantID;
import com.xgen.mhouse.services.andoncord.grpc.v1.AndonCordServiceGrpc;
import com.xgen.mhouse.services.andoncord.v1.Models.AndonCord;
import com.xgen.mhouse.services.andoncord.v1.Models.AndonCordState;
import com.xgen.mhouse.services.andoncord.v1.Models.AndonCordValue;
import com.xgen.mhouse.services.andoncord.v1.Models.CreateOrUpdateRequest;
import com.xgen.mhouse.services.andoncord.v1.Models.DataSet;
import com.xgen.mhouse.services.billinglimits.grpc.v1.UsageLimitsServiceGrpc.UsageLimitsServiceBlockingStub;
import com.xgen.mhouse.services.billinglimits.v1.Models.DataScanningLimitStatus;
import com.xgen.mhouse.services.billinglimits.v1.Models.DeleteUsageLimitRequest;
import com.xgen.mhouse.services.billinglimits.v1.Models.GetUsageLimitsRequest;
import com.xgen.mhouse.services.billinglimits.v1.Models.GetUsageLimitsResponse;
import com.xgen.mhouse.services.billinglimits.v1.Models.SetUsageLimitRequest;
import com.xgen.mhouse.services.billinglimits.v1.Models.UsageLimit;
import com.xgen.mhouse.services.logs.grpc.v1.LogsServiceGrpc;
import com.xgen.mhouse.services.logs.v1.Models;
import com.xgen.mhouse.services.logs.v1.Models.DeleteLogsRequest;
import com.xgen.mhouse.services.logs.v1.Models.DeleteLogsResponse;
import com.xgen.mhouse.services.logs.v1.Models.DownloadLogsRequest;
import com.xgen.mhouse.services.logs.v1.Models.DownloadLogsResponse;
import com.xgen.mhouse.services.op.grpc.v1.OpServiceGrpc.OpServiceBlockingStub;
import com.xgen.mhouse.services.op.v1.Models.CurrentOp;
import com.xgen.mhouse.services.privatelink.v1.Models.AcceptPrivateEndpointRequest;
import com.xgen.mhouse.services.privatelink.v1.Models.DeletePrivateEndpointRequest;
import com.xgen.mhouse.services.privatelink.v1.Models.GetServiceNamesRequest;
import com.xgen.mhouse.services.privatelink.v1.Models.GetServiceNamesResponse;
import com.xgen.mhouse.services.privatelink.v1.Models.RegionServiceName;
import com.xgen.mhouse.services.querymetrics.admin.v1.Models.CalculateTenantMetricsRequest;
import com.xgen.mhouse.services.querymetrics.admin.v1.Models.CalculateTenantMetricsResponse;
import com.xgen.mhouse.services.regions.v1.Models.GetRegionsRequest;
import com.xgen.mhouse.services.regions.v1.Models.Region;
import com.xgen.mhouse.services.schema.grpc.v1.SchemaServiceGrpc;
import com.xgen.mhouse.services.schema.v1.Models.CreateScheduledUpdateRequest;
import com.xgen.mhouse.services.schema.v1.Models.DeleteAllSchemasRequest;
import com.xgen.mhouse.services.schema.v1.Models.DeleteScheduledUpdateRequest;
import com.xgen.mhouse.services.schema.v1.Models.DeleteSchemaRequest;
import com.xgen.mhouse.services.schema.v1.Models.Frequency;
import com.xgen.mhouse.services.schema.v1.Models.GenerateAllSchemasRequest;
import com.xgen.mhouse.services.schema.v1.Models.GenerateAllSchemasResponse;
import com.xgen.mhouse.services.schema.v1.Models.GenerateSchemasOptions;
import com.xgen.mhouse.services.schema.v1.Models.GenerateSchemasRequest;
import com.xgen.mhouse.services.schema.v1.Models.GenerateSchemasResponse;
import com.xgen.mhouse.services.schema.v1.Models.GetAllSchemasRequest;
import com.xgen.mhouse.services.schema.v1.Models.GetAllSchemasResponse;
import com.xgen.mhouse.services.schema.v1.Models.GetScheduledUpdateRequest;
import com.xgen.mhouse.services.schema.v1.Models.GetScheduledUpdateResponse;
import com.xgen.mhouse.services.schema.v1.Models.GetSchemasRequest;
import com.xgen.mhouse.services.schema.v1.Models.GetSchemasResponse;
import com.xgen.mhouse.services.schema.v1.Models.ModifyScheduledUpdateRequest;
import com.xgen.mhouse.services.schema.v1.Models.Schema;
import com.xgen.mhouse.services.schema.v1.Models.SchemaWithMetadata;
import com.xgen.mhouse.services.schema.v1.Models.SchemaWithMetadata.Source;
import com.xgen.mhouse.services.schema.v1.Models.SetSchemaRequest;
import com.xgen.mhouse.services.storageconfig.grpc.v1.StorageConfigServiceGrpc;
import com.xgen.mhouse.services.storageconfig.v1.Models.DeleteStorageRequest;
import com.xgen.mhouse.services.storageconfig.v1.Models.GetStorageRequest;
import com.xgen.mhouse.services.storageconfig.v1.Models.GetStorageResponse;
import com.xgen.mhouse.services.storageconfig.v1.Models.SetStorageConfigInvalidError;
import com.xgen.mhouse.services.storageconfig.v1.Models.SetStorageConfigOutdatedError;
import com.xgen.mhouse.services.storageconfig.v1.Models.SetStorageRequest;
import com.xgen.mhouse.services.storageconfig.v1.Models.Storage;
import com.xgen.mhouse.services.storageconfig.v1.Models.ValidateStorageRequest;
import com.xgen.mhouse.services.storageconfig.v1.Models.ValidateStorageResponse;
import com.xgen.mhouse.services.tenant.grpc.v1.TenantServiceGrpc;
import com.xgen.mhouse.services.tenant.v1.TenantStorage;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.Insecure;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.ReadPreference.Mode;
import com.xgen.mhouse.services.tenantsettings.grpc.v1.TenantSettingsServiceGrpc.TenantSettingsServiceBlockingStub;
import com.xgen.mhouse.services.tenantsettings.v1.Models.GetSettingsRequest;
import com.xgen.mhouse.services.tenantsettings.v1.Models.GetSettingsResponse;
import com.xgen.mhouse.services.tenantsettings.v1.Models.SetSettingsRequest;
import com.xgen.svc.common.logger.AccessLogCode;
import com.xgen.svc.nds.model.onlinearchiveagentview.GetJobProgressResponseView;
import com.xgen.svc.nds.model.onlinearchiveagentview.OnlineArchiveUploadDataFilesRequestView;
import com.xgen.svc.nds.model.onlinearchiveagentview.OnlineArchiveUploadDataFilesRequestView.ManifestLocationView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeAndonCordValueDataSetView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeAndonCordValueView.NDSDataLakeAndonCordValueViewBuilder;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeAndonCordView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordValueDataSetView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordValueView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCurrentOpUserView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCurrentOpView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCurrentOpView.NDSDataLakeCurrentOpViewBuilder;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeDataSetView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeMetricsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeNamespaceView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLAllSchemasView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLScheduledUpdateView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLSchemaStatus;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLSchemaUpdateMetadataView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLSchemaView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLSchemaWithMetadataView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageValidationErrorsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantSettingsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantSettingsView.NDSDataLakeTenantSettingsViewBuilder;
import com.xgen.svc.nds.util.DataLakeLogsUtil;
import com.xgen.svc.nds.util.DataLakeLogsUtil.OAFieldDefs;
import io.grpc.ClientInterceptor;
import io.grpc.Context;
import io.grpc.Context.CancellableContext;
import io.grpc.Metadata;
import io.grpc.Status;
import io.grpc.Status.Code;
import io.grpc.StatusRuntimeException;
import io.grpc.protobuf.StatusProto;
import io.grpc.stub.MetadataUtils;
import io.grpc.stub.StreamObserver;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import io.prometheus.client.Histogram.Timer;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.bson.BSONObject;
import org.bson.BasicBSONDecoder;
import org.bson.BsonBinaryReader;
import org.bson.BsonBinaryWriter;
import org.bson.Document;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.DocumentCodec;
import org.bson.codecs.EncoderContext;
import org.bson.io.BasicOutputBuffer;
import org.bson.types.ObjectId;
import org.eclipse.jetty.server.HttpOutput;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class DataLakeAdminApiClient implements AutoCloseable {
  public static final String CLOUD_PROVIDER_AWS = CloudProvider.PROVIDER_AWS.toLowerCase();
  public static final String CLOUD_PROVIDER_AZURE = CloudProvider.PROVIDER_AZURE.toLowerCase();
  public static final String CLOUD_PROVIDER_GCP = CloudProvider.PROVIDER_GCP.toLowerCase();

  public static final String REGION_UNKNOWN = "UNKNOWN";

  public static final String CLOUD_PROVIDER = "cloudProvider";
  public static final String REGION = "region";

  public static final String DataLakeTypeFieldKey = "dataLakeType";
  public static final String DataLakeTypeStream = "STREAM";

  private static final Logger LOG = LoggerFactory.getLogger(DataLakeAdminApiClient.class);
  private static final long DATA_SETS_OPERATION_DEADLINE_SECONDS = 300;
  private static final long DOWNLOAD_LOGS_DEADLINE_SECONDS = 60 * 60;
  private static final long GET_ALL_SCHEMAS_DEADLINE_SECONDS = 60 * 60;

  // GENERATE_ALL_SCHEMAS_DEADLINE_SECONDS has a long deadline since it's a long-running async
  // request
  private static final long GENERATE_ALL_SCHEMAS_DEADLINE_SECONDS = 60 * 60 * 12;
  private static final long MERGE_DATASETS_DEADLINE_SECONDS = 60 * 30;
  private static final long GET_DLS_JOB_PROGRESS_OR_ERROR_SECONDS = 60;
  private static final long DESTROY_DATA_SETS_DEADLINE_SECONDS = 60 * 15;
  private static final long INITIALIZE_DATA_SETS_DEADLINE_SECONDS = 60 * 5;
  private static final long ACCEPT_PRIVATE_ENDPOINT_DEADLINE_SECONDS = 60 * 5;
  private static final long DELETE_PRIVATE_ENDPOINT_DEADLINE_SECONDS = 60 * 5;
  private static final long GET_SERVICE_NAMES_DEADLINE_SECONDS = 30;
  // prometheus related
  private static final String ADMIN_API_EXCEPTION_COUNT_NAME =
      "mms_nds_datalake_admin_api_error_total";

  private static final String ADMIN_API_EXCEPTION_COUNT_NAME_V3 =
      "mms_nds_datalake_admin_api_error_v3_total";
  private static final String ADMIN_API_EXCEPTION_COUNT_HELP =
      "Exceptions encountered communicating with Data Lake Admin API";
  private static final Counter ADMIN_API_EXCEPTION_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          ADMIN_API_EXCEPTION_COUNT_NAME,
          ADMIN_API_EXCEPTION_COUNT_HELP,
          "type",
          "method",
          "status");

  private static final Counter ADMIN_API_EXCEPTION_COUNTER_V3 =
      NDSPromMetricsSvc.registerCounter(
          ADMIN_API_EXCEPTION_COUNT_NAME_V3,
          ADMIN_API_EXCEPTION_COUNT_HELP,
          "type",
          "method",
          "status",
          CLOUD_PROVIDER,
          REGION);

  private static final String ADMIN_API_REQUEST_COUNT_NAME =
      "mms_nds_datalake_admin_api_request_total";
  private static final String ADMIN_API_REQUEST_COUNT_HELP =
      "Count of requests to the Data Lake Admin API";
  private static final Counter ADMIN_API_REQUEST_COUNTER =
      NDSPromMetricsSvc.registerCounter(
          ADMIN_API_REQUEST_COUNT_NAME, ADMIN_API_REQUEST_COUNT_HELP, "method");
  private static final String ADMIN_API_REQUEST_DURATION_HIST_NAME =
      "mms_nds_datalake_admin_api_duration_seconds";
  private static final String ADMIN_API_REQUEST_DURATION_HIST_NAME_V3 =
      "mms_nds_datalake_admin_api_duration_v3_seconds";
  private static final String ADMIN_API_REQUEST_DURATION_HIST_HELP =
      "Duration histogram for requests to the Data Lake Admin API";
  private static final Histogram ADMIN_API_REQUEST_DURATION_HISTOGRAM =
      NDSPromMetricsSvc.registerHistogram(
          ADMIN_API_REQUEST_DURATION_HIST_NAME,
          ADMIN_API_REQUEST_DURATION_HIST_HELP,
          // These are the default Histogram.Builder buckets.
          new double[] {
            .005, .01, .025, .05, .075, .1, .25, .5, .75, 1, 2.5, 5, 7.5, 10, 30, 60, 300, 3600
          },
          "method");

  private static final Histogram ADMIN_API_REQUEST_DURATION_HISTOGRAM_V3 =
      NDSPromMetricsSvc.registerHistogram(
          ADMIN_API_REQUEST_DURATION_HIST_NAME_V3,
          ADMIN_API_REQUEST_DURATION_HIST_HELP,
          // These are the default Histogram.Builder buckets.
          new double[] {
            .005, .01, .025, .05, .075, .1, .25, .5, .75, 1, 2.5, 5, 7.5, 10, 30, 60, 300, 3600
          },
          "method",
          CLOUD_PROVIDER,
          REGION);

  private static final String ANDON_CORD_DEFAULT_VALUE_KEY = "defaultValue";
  private static final String ANDON_CORD_DATA_SET_VALUE_KEY = "value";

  public static final int DEFAULT_SCHEMA_GENERATION_OPTIONS_SAMPLE_SIZE = 10;
  public static final int DEFAULT_SCHEMA_VERSION = 1;
  private final AppSettings _appSettings;
  private final GroupSvc _groupSvc;
  private Map<CloudProvider, DataLakeGrpcClient> _dataLakeGrpcClients;

  @Inject
  public DataLakeAdminApiClient(final AppSettings pAppSettings, final GroupSvc pGroupSvc) {
    this(
        getSupportedProviders(pAppSettings).stream()
            .collect(
                Collectors.toUnmodifiableMap(p -> p, p -> new DataLakeGrpcClient(pAppSettings, p))),
        pAppSettings,
        pGroupSvc);
  }

  public DataLakeAdminApiClient(
      final Map<CloudProvider, DataLakeGrpcClient> pDataLakeGrpcClients,
      final AppSettings pAppSettings,
      final GroupSvc pGroupSvc) {
    _dataLakeGrpcClients = pDataLakeGrpcClients;
    _appSettings = pAppSettings;
    _groupSvc = pGroupSvc;
    tuneGrpcManagedChannelOrphanLogging(Level.OFF);
  }

  private static Collection<CloudProvider> getSupportedProviders(final AppSettings pAppSettings) {
    return new ArrayList<>(List.of(CloudProvider.AWS, CloudProvider.AZURE, CloudProvider.GCP));
  }

  @VisibleForTesting
  protected static Date toDate(final Timestamp pTimestamp) {
    return Date.from(Instant.ofEpochSecond(pTimestamp.getSeconds(), pTimestamp.getNanos()));
  }

  private static Date toDateHandleZeroTimestamp(final Timestamp pTimestamp) {
    if (pTimestamp.getSeconds() == 0 && pTimestamp.getNanos() == 0) {
      return null;
    }
    return toDate(pTimestamp);
  }

  public static TenantStorage.Storage toADLStorage(final NDSDataLakeStorage pNDSStorage)
      throws DataLakeAdminApiException {
    assertPresent(pNDSStorage, "storage");
    final TenantStorage.Storage.Builder builder = TenantStorage.Storage.newBuilder();
    if (pNDSStorage.getStores() != null) {
      for (final NDSDataLakeStore s : pNDSStorage.getStores()) {
        builder.addStores(toADLStore(s));
      }
    }
    if (pNDSStorage.getDatabases() != null) {
      for (final NDSDataLakeDatabase d : pNDSStorage.getDatabases()) {
        builder.addDatabases(toADLDatabase(d));
      }
    }
    return builder.build();
  }

  private static TenantStorage.Store toADLStore(final NDSDataLakeStore pNDSStore)
      throws DataLakeAdminApiException {
    assertPresent(pNDSStore, "store");
    assertPresent(pNDSStore.getName(), "store name");
    final TenantStorage.Store.Builder builder =
        TenantStorage.Store.newBuilder().setName(pNDSStore.getName());
    switch (pNDSStore.getProvider()) {
      case S3:
        final NDSDataLakeS3Store s3Store = (NDSDataLakeS3Store) pNDSStore;
        assertPresent(s3Store.getBucket(), "bucket");
        final TenantStorage.S3Provider.Builder s3Builder =
            TenantStorage.S3Provider.newBuilder()
                .setBucket(s3Store.getBucket())
                .setIncludeTags(Optional.ofNullable(s3Store.isIncludeTags()).orElse(false));
        s3Builder.setPublic(s3Store.isPublic());
        if (s3Store.getRegion() != null) {
          s3Builder.setDataRegion(s3Store.getRegion().getValue());
        }
        if (s3Store.getPrefix() != null) {
          s3Builder.setPrefix(s3Store.getPrefix());
        }
        if (s3Store.getDelimiter() != null) {
          s3Builder.setDelimiter(s3Store.getDelimiter());
        }
        if (s3Store.getReplacementDelimiter() != null) {
          s3Builder.setReplacementDelimiter(s3Store.getReplacementDelimiter());
        }
        if (s3Store.getDocumentCountMetadataKey() != null) {
          s3Builder.setDocumentCountMetadataKey(s3Store.getDocumentCountMetadataKey());
        }
        if (s3Store.getAdditionalStorageClasses() != null) {
          s3Builder.addAllAdditionalStorageClasses(
              s3Store.getAdditionalStorageClasses().stream()
                  .map(NDSDataLakeS3Store.StorageClass::name)
                  .collect(Collectors.toList()));
        }
        builder.setS3(s3Builder);
        break;
      case ONLINE_ARCHIVE:
        final NDSDataLakeOnlineArchiveStore store = (NDSDataLakeOnlineArchiveStore) pNDSStore;
        assertPresent(store.getProjectId(), "projectId");
        assertPresent(store.getClusterId(), "clusterId");
        assertPresent(store.getClusterName(), "clusterName");
        final TenantStorage.OnlineArchiveProvider.Builder onlineArchiveBuilder =
            TenantStorage.OnlineArchiveProvider.newBuilder()
                .setProjectId(store.getProjectId())
                .setClusterId(store.getClusterId())
                .setClusterName(store.getClusterName());
        builder.setOnlineArchive(onlineArchiveBuilder);
        break;
      case HTTP:
        final NDSDataLakeHTTPStore httpStore = (NDSDataLakeHTTPStore) pNDSStore;
        final TenantStorage.HTTPProvider.Builder httpBuilder =
            TenantStorage.HTTPProvider.newBuilder();
        if (httpStore.getUrls() != null) {
          httpBuilder.addAllUrls(httpStore.getUrls());
        }
        if (httpStore.getAllowInsecure() != null) {
          httpBuilder.setInsecure(
              httpStore.getAllowInsecure()
                  ? TenantStorage.Insecure.ALLOW
                  : TenantStorage.Insecure.DISALLOW);
        }
        if (httpStore.getDefaultFormat() != null) {
          httpBuilder.setDefaultFormat(httpStore.getDefaultFormat());
        }
        builder.setHttp(httpBuilder);
        break;
      case ATLAS:
        final NDSDataLakeAtlasStore atlasStore = (NDSDataLakeAtlasStore) pNDSStore;
        assertPresent(atlasStore.getClusterName(), "clusterName");
        final TenantStorage.AtlasProvider.Builder atlasBuilder =
            TenantStorage.AtlasProvider.newBuilder()
                .setClusterName(atlasStore.getClusterName())
                .setProjectId(
                    Optional.ofNullable(atlasStore.getProjectId())
                        .map(ObjectId::toHexString)
                        .orElse(""));
        if (atlasStore.getReadPreference() != null) {
          final NDSDataLakeAtlasStoreReadPreference readPreference = atlasStore.getReadPreference();
          final TenantStorage.ReadPreference.Builder readPrefBuilder =
              TenantStorage.ReadPreference.newBuilder();
          if (!readPreference.getMode().isEmpty()) {
            try {
              readPrefBuilder.setMode(
                  TenantStorage.ReadPreference.Mode.valueOf(
                      readPreference.getMode().toUpperCase()));
            } catch (final IllegalArgumentException pE) {
              throw new DataLakeAdminApiException(
                  NDSErrorCode.INVALID_ARGUMENT, "readPreference.mode");
            }
          }
          if (!readPreference.getTagSets().isEmpty()) {
            readPrefBuilder.addAllTagSets(toADLReadPreferenceTagSet(readPreference.getTagSets()));
          }
          if (readPreference.getMaxStalenessSeconds() != null) {
            readPrefBuilder.setMaxStalenessSeconds(
                Int32Value.of(readPreference.getMaxStalenessSeconds()));
          }
          atlasBuilder.setReadPreference(readPrefBuilder.build());
        }
        if (atlasStore.getReadConcern() != null) {
          final NDSDataLakeAtlasStoreReadConcern readConcern = atlasStore.getReadConcern();
          if (!readConcern.getLevel().isEmpty()) {
            try {
              atlasBuilder.setReadConcern(
                  TenantStorage.ReadConcern.newBuilder()
                      .setLevel(
                          TenantStorage.ReadConcern.Level.valueOf(
                              readConcern.getLevel().toUpperCase())));
            } catch (final IllegalArgumentException pE) {
              throw new DataLakeAdminApiException(
                  NDSErrorCode.INVALID_ARGUMENT, "readConcern.level");
            }
          }
        }
        builder.setAtlas(atlasBuilder);
        break;
      case DLS_AWS:
        final NDSDataLakeDLSAWSStore dlsAWSStore = (NDSDataLakeDLSAWSStore) pNDSStore;
        assertPresent(dlsAWSStore.getRegion(), "region");
        final TenantStorage.DLSAWSProvider.Builder dlsAWSBuilder =
            TenantStorage.DLSAWSProvider.newBuilder()
                .setDataRegion(dlsAWSStore.getRegion().getValue());
        builder.setDlsaws(dlsAWSBuilder);
        break;
      case DLS_AZURE:
        final NDSDataLakeDLSAzureStore dlsAzureStore = (NDSDataLakeDLSAzureStore) pNDSStore;
        assertPresent(dlsAzureStore.getRegion(), "region");
        final TenantStorage.DLSAzureProvider.Builder dlsAzureBuilder =
            TenantStorage.DLSAzureProvider.newBuilder()
                .setDataRegion(dlsAzureStore.getRegion().getValue());
        builder.setDlsAzure(dlsAzureBuilder);
        break;
      case AZURE_BLOB_STORAGE:
        final NDSDataLakeAzureBlobStore azureBlobStore = (NDSDataLakeAzureBlobStore) pNDSStore;
        assertPresent(azureBlobStore.getServiceUrl(), "serviceUrl");
        assertPresent(azureBlobStore.getContainerName(), "containerName");
        final TenantStorage.AzureProvider.Builder azureBuilder =
            TenantStorage.AzureProvider.newBuilder()
                .setServiceUrl(azureBlobStore.getServiceUrl())
                .setContainerName(azureBlobStore.getContainerName());
        azureBuilder.setPublic(azureBlobStore.isPublic());
        if (azureBlobStore.getRegion() != null) {
          azureBuilder.setDataRegion(azureBlobStore.getRegion().getValue());
        }
        if (azureBlobStore.getPrefix() != null) {
          azureBuilder.setPrefix(azureBlobStore.getPrefix());
        }
        if (azureBlobStore.getDelimiter() != null) {
          azureBuilder.setDelimiter(azureBlobStore.getDelimiter());
        }
        if (azureBlobStore.getReplacementDelimiter() != null) {
          azureBuilder.setReplacementDelimiter(azureBlobStore.getReplacementDelimiter());
        }
        builder.setAzure(azureBuilder);
        break;
      case KAFKA:
        final NDSDataLakeKafkaStore kafkaStore = (NDSDataLakeKafkaStore) pNDSStore;
        assertPresent(kafkaStore.getBootstrapServers(), "bootstrapServers");
        final TenantStorage.KafkaProvider.Builder kafkaBuilder =
            TenantStorage.KafkaProvider.newBuilder()
                .addAllBootstrapServers(List.of(kafkaStore.getBootstrapServers().split(",")));
        builder.setKafka(kafkaBuilder);
        break;
      case HTTPS:
        break;
      case IN_MEMORY:
        final NDSDataLakeInMemoryStore inMemoryStore = (NDSDataLakeInMemoryStore) pNDSStore;
        assertPresent(inMemoryStore.getFormat(), "format");
        final TenantStorage.InMemoryProvider.Builder inMemoryBuilder =
            TenantStorage.InMemoryProvider.newBuilder()
                .setFormat(inMemoryStore.getFormat())
                .setPath("/");
        builder.setInmemory(inMemoryBuilder);
        break;
    }
    return builder.build();
  }

  private static List<TenantStorage.TagSet> toADLReadPreferenceTagSet(
      final List<List<NDSDataLakeAtlasStoreReadPreferenceTag>> pNDSReadPreferenceTagSet) {
    return pNDSReadPreferenceTagSet.stream()
        .map(
            tagSet ->
                tagSet.stream()
                    .map(
                        tag ->
                            TenantStorage.Tag.newBuilder()
                                .setName(tag.getName())
                                .setValue(tag.getValue())
                                .build())
                    .collect(Collectors.toList()))
        .map(adlTagSet -> TenantStorage.TagSet.newBuilder().addAllTagSet(adlTagSet).build())
        .collect(Collectors.toList());
  }

  private static TenantStorage.Database toADLDatabase(final NDSDataLakeDatabase pNDSDatabase)
      throws DataLakeAdminApiException {
    assertPresent(pNDSDatabase.getName(), "database name");
    final TenantStorage.Database.Builder builder =
        TenantStorage.Database.newBuilder().setName(pNDSDatabase.getName());
    if (pNDSDatabase.getCollections() != null) {
      for (final NDSDataLakeCollection c : pNDSDatabase.getCollections()) {
        builder.addCollections(toADLCollection(c));
      }
    }
    if (pNDSDatabase.getViews() != null) {
      for (final NDSDataLakeView v : pNDSDatabase.getViews()) {
        builder.addViews(toADLView(v));
      }
    }
    if (pNDSDatabase.getMaxWildcardCollections() != null) {
      builder.setMaxWildcardCollections(Int32Value.of(pNDSDatabase.getMaxWildcardCollections()));
    }
    return builder.build();
  }

  private static TenantStorage.Collection toADLCollection(
      final NDSDataLakeCollection pNDSCollection) throws DataLakeAdminApiException {
    assertPresent(pNDSCollection.getName(), "collection name");
    final TenantStorage.Collection.Builder builder =
        TenantStorage.Collection.newBuilder().setName(pNDSCollection.getName());
    builder.addAllDataSources(pNDSCollection.getDataSources());
    return builder.build();
  }

  private static TenantStorage.View toADLView(final NDSDataLakeView pNDSView)
      throws DataLakeAdminApiException {
    assertPresent(pNDSView.getName(), "view name");
    final TenantStorage.View.Builder builder =
        TenantStorage.View.newBuilder().setName(pNDSView.getName());
    if (pNDSView.getSource() != null) {
      builder.setSource(pNDSView.getSource());
    }
    if (pNDSView.getPipeline() != null) {
      builder.setPipeline(pNDSView.getPipeline());
    }
    return builder.build();
  }

  public static Storage toADLStorageV1(final NDSDataLakeStorageV1View pNDSStorage)
      throws DataLakeAdminApiException {
    assertPresent(pNDSStorage, "storage");
    try {
      final DocumentCodec dc = new DocumentCodec();
      final BasicOutputBuffer buffer = new BasicOutputBuffer();
      dc.encode(
          new BsonBinaryWriter(buffer), pNDSStorage.getConfig(), EncoderContext.builder().build());

      final ByteArrayOutputStream os = new ByteArrayOutputStream();
      buffer.pipe(os);

      final Bson.Type schemaType = Bson.Type.TYPE_EMBEDDED_DOCUMENT;
      final Bson.Value.Builder newBuilder = Bson.Value.newBuilder().setType(schemaType);
      final Bson.Value configValue =
          newBuilder.setData(ByteString.copyFrom(os.toByteArray())).build();

      return Storage.newBuilder().setConfig(configValue).build();
    } catch (final IOException pE) {
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    }
  }

  private static NDSDataLakeStorage toNDSStorage(final TenantStorage.Storage pADLStorage) {
    return NDSDataLakeStorage.builder()
        .stores(
            pADLStorage.getStoresList().stream()
                .map(DataLakeAdminApiClient::toNDSStore)
                .flatMap(Optional::stream)
                .collect(Collectors.toList()))
        .databases(
            pADLStorage.getDatabasesList().stream()
                .map(DataLakeAdminApiClient::toNDSDatabase)
                .collect(Collectors.toList()))
        .build();
  }

  private static Optional<NDSDataLakeStore> toNDSStore(final TenantStorage.Store pADLStore) {
    final NDSDataLakeStore.NDSDataLakeStoreBuilder builder;
    if (pADLStore.hasS3()) {
      final TenantStorage.S3Provider s3 = pADLStore.getS3();
      final AWSRegionName regionName = AWSRegionName.findByValue(s3.getDataRegion()).orElse(null);
      final List<StorageClass> additionalStorageClasses =
          s3.getAdditionalStorageClassesList().isEmpty()
              ? null
              : s3.getAdditionalStorageClassesList().stream()
                  .map(NDSDataLakeS3Store.StorageClass::valueOf)
                  .collect(Collectors.toList());
      builder =
          NDSDataLakeS3Store.builder()
              .region(regionName)
              .bucket(s3.getBucket())
              .includeTags(falseToNull(s3.getIncludeTags()))
              .prefix(emptyToNull(s3.getPrefix()))
              .delimiter(emptyToNull(s3.getDelimiter()))
              .replacementDelimiter(emptyToNull(s3.getReplacementDelimiter()))
              .documentCountMetadataKey(emptyToNull(s3.getDocumentCountMetadataKey()))
              .additionalStorageClasses(additionalStorageClasses)
              .setPublic(s3.getPublic());
    } else if (pADLStore.hasOnlineArchive()) {
      final TenantStorage.OnlineArchiveProvider oa = pADLStore.getOnlineArchive();
      builder =
          NDSDataLakeOnlineArchiveStore.builder()
              .projectId(oa.getProjectId())
              .clusterId(oa.getClusterId())
              .clusterName(oa.getClusterName());
    } else if (pADLStore.hasHttp()) {
      final TenantStorage.HTTPProvider http = pADLStore.getHttp();
      final Boolean allowInsecure =
          http.getInsecure() == Insecure.UNDEFINED
              ? null
              : http.getInsecure() == TenantStorage.Insecure.ALLOW;
      builder =
          NDSDataLakeHTTPStore.builder()
              .urls(http.getUrlsList())
              .allowInsecure(allowInsecure)
              .defaultFormat(emptyToNull(http.getDefaultFormat()));
    } else if (pADLStore.hasAtlas()) {
      final TenantStorage.AtlasProvider atlas = pADLStore.getAtlas();
      final NDSDataLakeAtlasStoreReadPreferenceBuilder readPreferenceBuilder =
          NDSDataLakeAtlasStoreReadPreference.builder();
      final NDSDataLakeAtlasStoreReadConcernBuilder readConcernBuilder =
          NDSDataLakeAtlasStoreReadConcern.builder();
      if (atlas.hasReadPreference()) {
        final TenantStorage.ReadPreference ADLReadPreference = atlas.getReadPreference();
        if (ADLReadPreference.getMode() != Mode.UNRECOGNIZED) {
          final String mode = toNDSMode(ADLReadPreference.getMode());
          readPreferenceBuilder.mode(mode);
        }
        if (ADLReadPreference.getTagSetsCount() > 0) {
          readPreferenceBuilder.tagSets(
              toNDSReadPreferenceTagSet(ADLReadPreference.getTagSetsList()));
        }
        if (ADLReadPreference.hasMaxStalenessSeconds()) {
          readPreferenceBuilder.maxStalenessSeconds(
              ADLReadPreference.getMaxStalenessSeconds().getValue());
        }
      }
      if (atlas.hasReadConcern()) {
        readConcernBuilder.level(atlas.getReadConcern().getLevel().toString().toLowerCase());
      }
      builder =
          NDSDataLakeAtlasStore.builder()
              .projectId(DbUtils.parseObjectId(atlas.getProjectId()))
              .clusterName(atlas.getClusterName())
              .readPreference(atlas.hasReadPreference() ? readPreferenceBuilder.build() : null)
              .readConcern(atlas.hasReadConcern() ? readConcernBuilder.build() : null);
    } else if (pADLStore.hasDlsaws()) {
      final TenantStorage.DLSAWSProvider dlsAWS = pADLStore.getDlsaws();
      final AWSRegionName regionName =
          AWSRegionName.findByNameOrValue(dlsAWS.getDataRegion())
              .orElseThrow(
                  () ->
                      new IllegalStateException(
                          "Invalid AWS region name encountered while gathering Data Lake usage: "
                              + dlsAWS.getDataRegion()));
      builder = NDSDataLakeDLSAWSStore.builder().region(regionName);
    } else if (pADLStore.hasDlsAzure()) {
      final TenantStorage.DLSAzureProvider dlsAzure = pADLStore.getDlsAzure();
      final AzureRegionName regionName =
          AzureRegionName.findByNameOrValue(dlsAzure.getDataRegion())
              .orElseThrow(
                  () ->
                      new IllegalStateException(
                          "Invalid Azure region name encountered while gathering Data Lake usage: "
                              + dlsAzure.getDataRegion()));
      builder = NDSDataLakeDLSAzureStore.builder().region(regionName);
    } else if (pADLStore.hasAzure()) {
      final TenantStorage.AzureProvider azureProvider = pADLStore.getAzure();
      final AzureRegionName regionName =
          AzureRegionName.findByValue(azureProvider.getDataRegion()).orElse(null);
      builder =
          NDSDataLakeAzureBlobStore.builder()
              .region(regionName)
              .serviceUrl(azureProvider.getServiceUrl())
              .containerName(azureProvider.getContainerName())
              .prefix(emptyToNull(azureProvider.getPrefix()))
              .delimiter(emptyToNull(azureProvider.getDelimiter()))
              .replacementDelimiter(emptyToNull(azureProvider.getReplacementDelimiter()))
              .setPublic(azureProvider.getPublic());
    } else if (pADLStore.hasKafka()) {
      final TenantStorage.KafkaProvider kafka = pADLStore.getKafka();
      builder =
          NDSDataLakeKafkaStore.builder()
              .bootstrapServers(String.join(",", kafka.getBootstrapServersList()));
    } else if (pADLStore.hasInmemory()) {
      final TenantStorage.InMemoryProvider inMemory = pADLStore.getInmemory();
      builder = NDSDataLakeInMemoryStore.builder().format(inMemory.getFormat());
    } else {
      // silently ignore unrecognized providers
      return Optional.empty();
    }
    return Optional.of(builder.name(pADLStore.getName()).build());
  }

  private static String toNDSMode(final TenantStorage.ReadPreference.Mode mode) {
    return switch (mode) {
      case PRIMARY -> "primary";
      case PRIMARYPREFERRED -> "primaryPreferred";
      case SECONDARY -> "secondary";
      case SECONDARYPREFERRED -> "secondaryPreferred";
      case NEAREST -> "nearest";
      default -> "";
    };
  }

  private static List<List<NDSDataLakeAtlasStoreReadPreferenceTag>> toNDSReadPreferenceTagSet(
      final List<TenantStorage.TagSet> pADLTagSet) {
    return pADLTagSet.stream()
        .map(
            tagSet ->
                tagSet.getTagSetList().stream()
                    .map(
                        tag ->
                            new NDSDataLakeAtlasStoreReadPreferenceTag(
                                tag.getName(), tag.getValue()))
                    .collect(Collectors.toList()))
        .collect(Collectors.toList());
  }

  private static NDSDataLakeDatabase toNDSDatabase(final TenantStorage.Database pADLDatabase) {
    final Integer maxWildcardCollections =
        pADLDatabase.hasMaxWildcardCollections()
            ? pADLDatabase.getMaxWildcardCollections().getValue()
            : null;
    return NDSDataLakeDatabase.builder()
        .name(pADLDatabase.getName())
        .maxWildcardCollections(maxWildcardCollections)
        .collections(
            pADLDatabase.getCollectionsList().stream()
                .map(DataLakeAdminApiClient::toNDSCollection)
                .collect(Collectors.toList()))
        .views(
            pADLDatabase.getViewsList().stream()
                .map(DataLakeAdminApiClient::toNDSView)
                .collect(Collectors.toList()))
        .build();
  }

  private static NDSDataLakeCollection toNDSCollection(
      final TenantStorage.Collection pADLCollection) {
    return NDSDataLakeCollection.builder()
        .name(pADLCollection.getName())
        .dataSources(pADLCollection.getDataSourcesList())
        .build();
  }

  private static NDSDataLakeView toNDSView(final TenantStorage.View pADLView) {
    return NDSDataLakeView.builder()
        .name(pADLView.getName())
        .source(pADLView.getSource())
        .pipeline(pADLView.getPipeline())
        .build();
  }

  private static NDSDataLakeStorageV1View toNDSStorageV1View(final Storage pADLStorage) {
    final DocumentCodec dc = new DocumentCodec();
    final ByteString rawConfig = pADLStorage.getConfig().getData();

    final Document config =
        dc.decode(
            new BsonBinaryReader(rawConfig.asReadOnlyByteBuffer()),
            DecoderContext.builder().build());

    return NDSDataLakeStorageV1View.builder().config(config).build();
  }

  private static void assertPresent(final Object pValue, final String pPropertyName)
      throws DataLakeAdminApiException {
    if (pValue == null) {
      throw new DataLakeAdminApiException(NDSErrorCode.INVALID_ARGUMENT, pPropertyName);
    }
  }

  private static void assertPresent(final String pValue, final String pPropertyName)
      throws DataLakeAdminApiException {
    if (Strings.isNullOrEmpty(pValue)) {
      throw new DataLakeAdminApiException(NDSErrorCode.INVALID_ARGUMENT, pPropertyName);
    }
  }

  private static void assertPresent(final Collection<?> pValue, final String pPropertyName)
      throws DataLakeAdminApiException {
    if (pValue == null || pValue.isEmpty()) {
      throw new DataLakeAdminApiException(NDSErrorCode.INVALID_ARGUMENT, pPropertyName);
    }
  }

  private static String emptyToNull(final String pValue) {
    return StringUtils.isEmpty(pValue) ? null : pValue;
  }

  private static <T> List<T> emptyToNull(final List<T> pValue) {
    return Optional.ofNullable(pValue).filter(l -> !l.isEmpty()).orElse(null);
  }

  private static Boolean falseToNull(final boolean pValue) {
    return pValue ? true : null;
  }

  public static String getDLSAcceptedCloudProvider(final CloudProvider pCloudProvider) {
    return pCloudProvider.name().toLowerCase();
  }

  @Override
  @PreDestroy
  public void close() {
    _dataLakeGrpcClients.values().forEach(DataLakeGrpcClient::close);
    tuneGrpcManagedChannelOrphanLogging(Level.SEVERE);
  }

  // to remove noise from not closing grpc channel in local and test - CLOUDP-207959
  // this is because the class might get instantiated multiple times at the same time in these
  // environments.
  private void tuneGrpcManagedChannelOrphanLogging(final Level pLevel) {
    if (_appSettings.isLocal() || _appSettings.isTest()) {
      java.util.logging.Logger.getLogger("io.grpc.internal.ManagedChannelOrphanWrapper")
          .setLevel(pLevel);
    }
  }

  public Collection<CloudProvider> getSupportedProviders() {
    return _dataLakeGrpcClients.keySet();
  }

  public void setGrpcClientsInTestEnv(final AppSettings pAppSettings) {
    if (pAppSettings.getAppEnv() != AppEnv.TEST) {
      return;
    }
    _dataLakeGrpcClients =
        getSupportedProviders(pAppSettings).stream()
            .collect(
                Collectors.toUnmodifiableMap(p -> p, p -> new DataLakeGrpcClient(pAppSettings, p)));
  }

  public boolean isActive() {
    if (_dataLakeGrpcClients == null) {
      return false;
    }
    return _dataLakeGrpcClients.values().stream().anyMatch(DataLakeGrpcClient::isActive);
  }

  private DataLakeGrpcClient getDataLakeGrpcClient(final String pCloudProvider)
      throws DataLakeAdminApiException {
    final String cloudProvider = getDLSAcceptedCloudProvider(pCloudProvider);
    return _dataLakeGrpcClients.get(CloudProvider.findByNameIgnoreCase(cloudProvider));
  }

  private DataLakeGrpcClient getDataLakeGrpcClient(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    return getDataLakeGrpcClient(getDLSAcceptedCloudProvider(pTenant));
  }

  @VisibleForTesting
  protected String getDLSAcceptedCloudProvider(final String pCloudProvider)
      throws DataLakeAdminApiException {
    if (pCloudProvider == null) {
      throw new DataLakeAdminApiException(NDSErrorCode.INVALID_CLOUD_PROVIDER, "null");
    }
    if (pCloudProvider.equalsIgnoreCase(CloudProvider.PROVIDER_AWS)) {
      return CLOUD_PROVIDER_AWS;
    } else if (pCloudProvider.equalsIgnoreCase(CloudProvider.PROVIDER_AZURE)) {
      return CLOUD_PROVIDER_AZURE;
    } else if (pCloudProvider.equalsIgnoreCase(CloudProvider.PROVIDER_GCP)) {
      return CLOUD_PROVIDER_GCP;
    }
    throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER);
  }

  @VisibleForTesting
  protected String getDLSAcceptedCloudProvider(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    final String cloudProvider =
        Optional.ofNullable(pTenant.getDataProcessRegion())
            .map(NDSDataLakeDataProcessRegion::getCloudProvider)
            .orElse(CloudProvider.AWS)
            .toString()
            .toLowerCase();
    return getDLSAcceptedCloudProvider(cloudProvider);
  }

  @VisibleForTesting
  protected void incrementAdminApiExceptionCounter(
      final Throwable pException, final String pMethod, final Status pStatus) {
    NDSPromMetricsSvc.incrementCounter(
        ADMIN_API_EXCEPTION_COUNTER,
        pException.getClass().getSimpleName(),
        pMethod,
        pStatus.getCode().name());
  }

  @VisibleForTesting
  protected void incrementAdminApiExceptionCounterV3(
      final Throwable pException,
      final String pMethod,
      final Status pStatus,
      final String pCloudProvider,
      final String pRegion) {
    NDSPromMetricsSvc.incrementCounter(
        ADMIN_API_EXCEPTION_COUNTER_V3,
        pException.getClass().getSimpleName(),
        pMethod,
        pStatus.getCode().name(),
        pCloudProvider,
        pRegion);
  }

  @VisibleForTesting
  protected void incrementAdminApiRequestCounter(final String pMethod) {
    NDSPromMetricsSvc.incrementCounter(ADMIN_API_REQUEST_COUNTER, pMethod);
  }

  @VisibleForTesting
  protected Timer startDurationTimer(final String pMethod) {
    return NDSPromMetricsSvc.startTimer(ADMIN_API_REQUEST_DURATION_HISTOGRAM, pMethod);
  }

  @VisibleForTesting
  protected Timer startDurationTimerV3(
      final String pMethod, final String pDataCenter, final String pRegion) {
    return NDSPromMetricsSvc.startTimer(
        ADMIN_API_REQUEST_DURATION_HISTOGRAM_V3, pMethod, pDataCenter, pRegion);
  }

  public Optional<Date> getStorageConfigLastUpdatedDate(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    final Optional<GetStorageResponse> resp =
        findStorageConfigInternal(
            pTenant.getTenantId(), pTenant.getGroupId(), getDLSAcceptedCloudProvider(pTenant));
    return resp.filter(GetStorageResponse::hasStorage)
        .map(GetStorageResponse::getLastModifiedAt)
        .map(DataLakeAdminApiClient::toDate);
  }

  public Optional<NDSDataLakeStorageV1View> findStorageConfig(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    final Optional<GetStorageResponse> resp =
        findStorageConfigInternal(
            pTenant.getTenantId(), pTenant.getGroupId(), getDLSAcceptedCloudProvider(pTenant));
    return resp.filter(GetStorageResponse::hasStorage)
        .map(GetStorageResponse::getStorage)
        .map(DataLakeAdminApiClient::toNDSStorageV1View);
  }

  private Optional<GetStorageResponse> findStorageConfigInternal(
      final ObjectId pTenantId, final ObjectId pGroupId, final String pCloudProvider)
      throws DataLakeAdminApiException {
    assertPresent(pTenantId, "tenantId");
    assertPresent(pCloudProvider, "cloudProvider");
    final GetStorageRequest req =
        GetStorageRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(pTenantId.toString()).build())
            .build();
    final StorageConfigServiceGrpc.StorageConfigServiceBlockingStub stub =
        getDataLakeGrpcClient(pCloudProvider).getStorageConfigServiceStub();
    final Timer timer = startDurationTimer(Methods.GET_STORAGE);
    final Timer timerV3 = startDurationTimerV3(Methods.GET_STORAGE, pCloudProvider, REGION_UNKNOWN);
    try {
      incrementAdminApiRequestCounter(Methods.GET_STORAGE);
      return Optional.of(stub.getStorage(req));
    } catch (final StatusRuntimeException pE) {
      final Status status = pE.getStatus();

      // Equality on Statuses is not well-defined. Instead, doing comparison based on their Code
      if (status.getCode() == Status.NOT_FOUND.getCode()) {
        // NOT_FOUND is not something unexpected when we're checking if config exists,
        // so we're not incrementing exception counter here
        return Optional.empty();
      }
      incrementAdminApiExceptionCounter(pE, Methods.GET_STORAGE, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
    } catch (final Exception pE) {
      incrementAdminApiExceptionCounter(pE, Methods.GET_STORAGE, Status.UNKNOWN);
      incrementAdminApiExceptionCounterV3(
          pE, Methods.GET_STORAGE, Status.UNKNOWN, pCloudProvider, REGION_UNKNOWN);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
  }

  private void updateStorageConfigV0Internal(
      final ObjectId pTenantId,
      final ObjectId pGroupId,
      final String pCloudProvider,
      final NDSDataLakeStorage pStorage,
      final Date pLastModifiedAt,
      final String pModifiedBy,
      final String pCertificateToken)
      throws DataLakeAdminApiException {
    assertPresent(pTenantId, "tenantId");
    assertPresent(pCloudProvider, "cloudProvider");
    assertPresent(pStorage, "storage");
    final TenantStorage.SetStorageRequest.Builder reqBuilder =
        TenantStorage.SetStorageRequest.newBuilder()
            .setStorage(toADLStorage(pStorage))
            .setTenantId(pTenantId.toHexString());
    if (pModifiedBy != null) {
      reqBuilder.setModifiedBy(pModifiedBy);
    }
    if (pLastModifiedAt != null) {
      final Instant lastModifiedAtInstant = pLastModifiedAt.toInstant();
      reqBuilder.setLastModifiedAt(
          Timestamp.newBuilder()
              .setSeconds(lastModifiedAtInstant.getEpochSecond())
              .setNanos(lastModifiedAtInstant.getNano())
              .build());
    }
    if (pCertificateToken != null) {
      reqBuilder.setAuthzToken(pCertificateToken);
    }

    final TenantServiceGrpc.TenantServiceBlockingStub stub =
        getDataLakeGrpcClient(pCloudProvider).getTenantServiceStub();
    final Timer timer = startDurationTimer(Methods.SET_STORAGE);
    final TenantStorage.SetStorageRequest req = reqBuilder.build();
    try {
      incrementAdminApiRequestCounter(Methods.SET_STORAGE);
      stub.setStorage(req);
    } catch (final StatusRuntimeException pE) {
      final Status status = translateExceptionIntoStatus(pE);

      LOG.warn(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID, pGroupId,
              DataLakeLogsUtil.FieldDefs.TENANT_ID, pTenantId),
          "Failed to set storage with data lake error for tenant",
          pE);

      // check for concurrency error when updating outdated tenant storage config before refreshed
      if (status.getCode().equals(Status.NOT_FOUND.getCode())) {
        final com.google.rpc.Status statusMessage = StatusProto.fromThrowable(pE);
        final List<Any> detailsList = statusMessage.getDetailsList();
        final Optional<Any> configOutdatedError =
            detailsList.stream()
                .filter(any -> any.is(TenantStorage.SetStorageConfigOutdatedError.class))
                .findFirst();
        if (configOutdatedError.isPresent()) {
          try {
            final TenantStorage.SetStorageConfigOutdatedError errorMessage =
                configOutdatedError.get().unpack(TenantStorage.SetStorageConfigOutdatedError.class);
            incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.NOT_FOUND);
            throw new DataLakeAdminApiException(
                NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_OUTDATED,
                String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId),
                errorMessage.getLastModifiedAt());
          } catch (InvalidProtocolBufferException e) {
            LOG.error(
                DataLakeLogsUtil.LOG_FORMAT,
                LogUtils.entries(
                    DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                    DataLakeLogsUtil.FieldDefs.GROUP_ID, pGroupId,
                    DataLakeLogsUtil.FieldDefs.TENANT_ID, pTenantId),
                "Failed to unpack storage config outdated validation error",
                e);
            incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT);
            throw new DataLakeAdminApiException(
                NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
                pE,
                String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
          }
        }
      }

      // Equality on Statuses is not well defined. Instead, doing comparison based on their Code
      if (status.getCode() != Status.INVALID_ARGUMENT.getCode()) {
        incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, status);
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
            pE,
            String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
      }

      final com.google.rpc.Status statusMessage = StatusProto.fromThrowable(pE);
      if (statusMessage == null) {
        incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT);
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
            pE,
            String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
      }

      final List<Any> detailsList = statusMessage.getDetailsList();
      if (detailsList.isEmpty()) {
        incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT);
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
            pE,
            String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
      }

      final Optional<Any> details =
          detailsList.stream()
              .filter(any -> any.is(TenantStorage.SetStorageConfigInvalidError.class))
              .findFirst();
      if (details.isPresent()) {
        try {
          final TenantStorage.SetStorageConfigInvalidError errorMessage =
              details.get().unpack(TenantStorage.SetStorageConfigInvalidError.class);
          // Not incrementing admin api exception counter as this is user error
          final String errorsList = errorMessage.getErrorsList().toString();
          throw new DataLakeAdminApiException(
              NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_INVALID, errorsList);
        } catch (InvalidProtocolBufferException e) {
          LOG.error(
              DataLakeLogsUtil.LOG_FORMAT,
              LogUtils.entries(
                  DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                  DataLakeLogsUtil.FieldDefs.GROUP_ID, pGroupId,
                  DataLakeLogsUtil.FieldDefs.TENANT_ID, pTenantId),
              "Failed to unpack storage config validation errors",
              e);
          incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT);
          throw new DataLakeAdminApiException(
              NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
              pE,
              String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
        }
      }
    } catch (final Exception pE) {
      LOG.error(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID, pGroupId,
              DataLakeLogsUtil.FieldDefs.TENANT_ID, pTenantId),
          "Failed to set storage with data lake error for tenant",
          pE);
      incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.UNKNOWN);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public void updateStorageConfig(
      final NDSDataLakeTenant pTenant,
      final NDSDataLakeStorageV1View pStorage,
      final Date pLastModifiedAt,
      final String pModifiedBy,
      final String pCertificateToken)
      throws DataLakeAdminApiException {
    updateStorageConfigInternal(
        pTenant.getTenantId(),
        pTenant.getGroupId(),
        getDLSAcceptedCloudProvider(pTenant),
        pStorage,
        pLastModifiedAt,
        pModifiedBy,
        pCertificateToken,
        false);
  }

  public void updateStorageConfigForStreams(
      final NDSDataLakeTenant pTenant,
      final NDSDataLakeStorageV1View pStorage,
      final Date pLastModifiedAt,
      final String pModifiedBy,
      final String pCertificateToken)
      throws DataLakeAdminApiException {
    updateStorageConfigInternal(
        pTenant.getTenantId(),
        pTenant.getGroupId(),
        getDLSAcceptedCloudProvider(pTenant),
        pStorage,
        pLastModifiedAt,
        pModifiedBy,
        pCertificateToken,
        true);
  }

  private void updateStorageConfigInternal(
      final ObjectId pTenantId,
      final ObjectId pGroupId,
      final String pCloudProvider,
      final NDSDataLakeStorageV1View pStorage,
      final Date pLastModifiedAt,
      final String pModifiedBy,
      final String pCertificateToken,
      final boolean isForStreams)
      throws DataLakeAdminApiException {
    assertPresent(pTenantId, "tenantId");
    assertPresent(pCloudProvider, "cloudProvider");
    assertPresent(pStorage, "storage");
    final SetStorageRequest.Builder reqBuilder =
        SetStorageRequest.newBuilder()
            .setStorage(toADLStorageV1(pStorage))
            .setTenantId(TenantId.TenantID.newBuilder().setValue(pTenantId.toString()).build());
    if (pModifiedBy != null) {
      reqBuilder.setModifiedBy(pModifiedBy);
    }
    if (pLastModifiedAt != null) {
      final Instant lastModifiedAtInstant = pLastModifiedAt.toInstant();
      reqBuilder.setLastModifiedAt(
          Timestamp.newBuilder()
              .setSeconds(lastModifiedAtInstant.getEpochSecond())
              .setNanos(lastModifiedAtInstant.getNano())
              .build());
    }
    if (pCertificateToken != null) {
      reqBuilder.setAuthzToken(pCertificateToken);
    }

    final StorageConfigServiceGrpc.StorageConfigServiceBlockingStub stub;
    if (isForStreams) {
      // Add gRPC Metadata to the request
      Metadata metadata = new Metadata();
      Metadata.Key<String> dataLakeTypeKey =
          io.grpc.Metadata.Key.of(DataLakeTypeFieldKey, Metadata.ASCII_STRING_MARSHALLER);
      metadata.put(dataLakeTypeKey, DataLakeTypeStream);
      if (LoggingContext.hasKey(AccessLogCode.REQUEST_ID)) {
        metadata.put(
            Metadata.Key.of(REQUEST_ID_HEADER, Metadata.ASCII_STRING_MARSHALLER),
            LoggingContext.get(AccessLogCode.REQUEST_ID));
      }

      // Attach metadata to the stub
      final ClientInterceptor metadataInterceptor =
          MetadataUtils.newAttachHeadersInterceptor(metadata);
      stub = getDataLakeGrpcClient(pCloudProvider).getStorageConfigServiceStub(metadataInterceptor);
    } else {
      stub = getDataLakeGrpcClient(pCloudProvider).getStorageConfigServiceStub();
    }

    final Timer timer = startDurationTimer(Methods.SET_STORAGE);
    final Timer timerV3 = startDurationTimerV3(Methods.SET_STORAGE, pCloudProvider, REGION_UNKNOWN);
    final SetStorageRequest req = reqBuilder.build();
    try {
      incrementAdminApiRequestCounter(Methods.SET_STORAGE);
      stub.setStorage(req);
    } catch (final StatusRuntimeException pE) {
      final Status status = translateExceptionIntoStatus(pE);

      LOG.warn(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID, pGroupId,
              DataLakeLogsUtil.FieldDefs.TENANT_ID, pTenantId),
          "Failed to set storage with data lake error for tenant",
          pE);

      // check for concurrency error when updating outdated tenant storage config before refreshed
      if (status.getCode().equals(Status.NOT_FOUND.getCode())) {
        final com.google.rpc.Status statusMessage = StatusProto.fromThrowable(pE);
        final List<Any> detailsList = statusMessage.getDetailsList();
        final Optional<Any> configOutdatedError =
            detailsList.stream()
                .filter(any -> any.is(SetStorageConfigOutdatedError.class))
                .findFirst();
        if (configOutdatedError.isPresent()) {
          try {
            final SetStorageConfigOutdatedError errorMessage =
                configOutdatedError.get().unpack(SetStorageConfigOutdatedError.class);
            incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.NOT_FOUND);
            incrementAdminApiExceptionCounterV3(
                pE, Methods.SET_STORAGE, Status.NOT_FOUND, pCloudProvider, REGION_UNKNOWN);
            throw new DataLakeAdminApiException(
                NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_OUTDATED,
                String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId),
                errorMessage.getLastModifiedAt());
          } catch (InvalidProtocolBufferException e) {
            LOG.error(
                DataLakeLogsUtil.LOG_FORMAT,
                LogUtils.entries(
                    DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                    DataLakeLogsUtil.FieldDefs.GROUP_ID, pGroupId,
                    DataLakeLogsUtil.FieldDefs.TENANT_ID, pTenantId),
                "Failed to unpack storage config outdated validation error",
                e);
            incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT);
            incrementAdminApiExceptionCounterV3(
                pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT, pCloudProvider, REGION_UNKNOWN);
            throw new DataLakeAdminApiException(
                NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
                pE,
                String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
          }
        }
      }

      // Equality on Statuses is not well defined. Instead, doing comparison based on their Code
      if (status.getCode() != Status.INVALID_ARGUMENT.getCode()) {
        incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, status);
        incrementAdminApiExceptionCounterV3(
            pE, Methods.SET_STORAGE, status, pCloudProvider, REGION_UNKNOWN);
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
            pE,
            String.format(
                "Tenant ID: %s, Group ID: %s  (%s)", pTenantId, pGroupId, status.getCode()));
      }

      final com.google.rpc.Status statusMessage = StatusProto.fromThrowable(pE);
      if (statusMessage == null) {
        incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT);
        incrementAdminApiExceptionCounterV3(
            pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT, pCloudProvider, REGION_UNKNOWN);
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
            pE,
            String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
      }

      final List<Any> detailsList = statusMessage.getDetailsList();
      if (detailsList.isEmpty()) {
        incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT);
        incrementAdminApiExceptionCounterV3(
            pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT, pCloudProvider, REGION_UNKNOWN);
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
            pE,
            String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
      }

      final Optional<Any> details =
          detailsList.stream()
              .filter(any -> any.is(SetStorageConfigInvalidError.class))
              .findFirst();
      if (details.isPresent()) {
        try {
          final SetStorageConfigInvalidError errorMessage =
              details.get().unpack(SetStorageConfigInvalidError.class);
          // Not incrementing admin api exception counter as this is user error
          final String errorsList = errorMessage.getErrorsList().toString();
          throw new DataLakeAdminApiException(
              NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_INVALID, errorsList);
        } catch (InvalidProtocolBufferException e) {
          LOG.error(
              DataLakeLogsUtil.LOG_FORMAT,
              LogUtils.entries(
                  DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                  DataLakeLogsUtil.FieldDefs.GROUP_ID, pGroupId,
                  DataLakeLogsUtil.FieldDefs.TENANT_ID, pTenantId),
              "Failed to unpack storage config validation errors",
              e);
          incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT);
          incrementAdminApiExceptionCounterV3(
              pE, Methods.SET_STORAGE, Status.INVALID_ARGUMENT, pCloudProvider, REGION_UNKNOWN);
          throw new DataLakeAdminApiException(
              NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
              pE,
              String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
        }
      }
    } catch (final Exception pE) {
      LOG.error(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID, pGroupId,
              DataLakeLogsUtil.FieldDefs.TENANT_ID, pTenantId),
          "Failed to set storage with data lake error for tenant",
          pE);
      incrementAdminApiExceptionCounter(pE, Methods.SET_STORAGE, Status.UNKNOWN);
      incrementAdminApiExceptionCounterV3(
          pE, Methods.SET_STORAGE, Status.UNKNOWN, pCloudProvider, REGION_UNKNOWN);

      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
  }

  public NDSDataLakeStorageValidationErrorsView validateStorageConfig(
      final ObjectId pTenantId,
      final String pCloudProvider,
      final NDSDataLakeStorageV1View pStorage)
      throws DataLakeAdminApiException {
    assertPresent(pTenantId, "tenantId");
    assertPresent(pStorage, "storage");
    final ValidateStorageRequest req =
        ValidateStorageRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(pTenantId.toString()).build())
            .setStorage(toADLStorageV1(pStorage))
            .build();
    final ValidateStorageResponse resp;
    final StorageConfigServiceGrpc.StorageConfigServiceBlockingStub stub =
        getDataLakeGrpcClient(pCloudProvider).getStorageConfigServiceStub();
    final Timer timer = startDurationTimer(Methods.VALIDATE_STORAGE);
    final Timer timerV3 =
        startDurationTimerV3(Methods.VALIDATE_STORAGE, pCloudProvider, REGION_UNKNOWN);
    try {
      incrementAdminApiRequestCounter(Methods.VALIDATE_STORAGE);
      resp = stub.validateStorage(req);
    } catch (final Exception pE) {
      incrementAdminApiExceptionCounter(pE, Methods.VALIDATE_STORAGE, Status.UNKNOWN);
      incrementAdminApiExceptionCounterV3(
          pE, Methods.VALIDATE_STORAGE, Status.UNKNOWN, pCloudProvider, REGION_UNKNOWN);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
    final ProtocolStringList errorsList = resp.getErrorsList();
    return new NDSDataLakeStorageValidationErrorsView(errorsList, errorsList.size());
  }

  public void deleteStorageConfigV0(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    deleteStorageConfigV0Internal(
        pTenant.getTenantId(), pTenant.getGroupId(), getDLSAcceptedCloudProvider(pTenant));
  }

  private void deleteStorageConfigV0Internal(
      final ObjectId pTenantId, final ObjectId pGroupId, final String pCloudProvider)
      throws DataLakeAdminApiException {
    assertPresent(pTenantId, "tenantId");
    assertPresent(pCloudProvider, "cloudProvider");
    final TenantStorage.DeleteStorageRequest req =
        TenantStorage.DeleteStorageRequest.newBuilder()
            .setTenantId(pTenantId.toHexString())
            .build();
    final TenantServiceGrpc.TenantServiceBlockingStub stub =
        getDataLakeGrpcClient(pCloudProvider).getTenantServiceStub();
    final Timer timer = startDurationTimer(Methods.DELETE_STORAGE);
    try {
      incrementAdminApiRequestCounter(Methods.DELETE_STORAGE);
      stub.deleteStorage(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.DELETE_STORAGE, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public void deleteStorageConfig(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    deleteStorageConfigInternal(
        pTenant.getTenantId(), pTenant.getGroupId(), getDLSAcceptedCloudProvider(pTenant));
  }

  private void deleteStorageConfigInternal(
      final ObjectId pTenantId, final ObjectId pGroupId, final String pCloudProvider)
      throws DataLakeAdminApiException {
    assertPresent(pTenantId, "tenantId");
    assertPresent(pCloudProvider, "cloudProvider");
    final DeleteStorageRequest req =
        DeleteStorageRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(pTenantId.toString()).build())
            .build();
    final StorageConfigServiceGrpc.StorageConfigServiceBlockingStub stub =
        getDataLakeGrpcClient(pCloudProvider).getStorageConfigServiceStub();
    final Timer timer = startDurationTimer(Methods.DELETE_STORAGE);
    final Timer timerV3 =
        startDurationTimerV3(Methods.DELETE_STORAGE, pCloudProvider, REGION_UNKNOWN);
    try {
      incrementAdminApiRequestCounter(Methods.DELETE_STORAGE);
      stub.deleteStorage(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.DELETE_STORAGE, status);
      incrementAdminApiExceptionCounterV3(
          pE, Methods.DELETE_STORAGE, status, pCloudProvider, REGION_UNKNOWN);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s, Group ID: %s", pTenantId, pGroupId));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
  }

  public void initializeOADataSet(
      final ObjectId pGroupId,
      final String pDatasetName,
      final String pMetadataProvider,
      final String pMetadataRegion,
      final String pBucket,
      final List<String> pPartitionSetKeys)
      throws DataLakeAdminApiException {
    final InitializeOADataSetRequest req =
        InitializeOADataSetRequest.newBuilder()
            .setProjectId(pGroupId.toString())
            .setDataSetName(pDatasetName)
            .setMetadataLocation(
                MetadataLocation.newBuilder()
                    .setProvider(pMetadataProvider)
                    .setRegion(pMetadataRegion)
                    .build())
            .setPartitionSetLocation(
                Location.newBuilder()
                    .setS3(
                        LocationS3.newBuilder()
                            .setBucket(pBucket)
                            .setRegion(pMetadataRegion)
                            .build())
                    .build())
            .addAllPartitionSetKeys(pPartitionSetKeys)
            .build();

    final var stub = getDataLakeGrpcClient(pMetadataProvider).getStorageServiceStub();
    final Timer timer = startDurationTimer(Methods.INITIALIZE_OA_DATASET);
    try {
      incrementAdminApiRequestCounter(Methods.INITIALIZE_OA_DATASET);
      stub.initializeOADataSet(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.INITIALIZE_OA_DATASET, status);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public InitializeDataSetResponse initializeDataSet(
      final ObjectId pGroupId,
      final String pDatasetName,
      final String pMetadataProvider,
      final String pMetadataRegion,
      final String pPartitionSetName,
      final List<String> pPartitionSetKeys,
      final boolean pIsTimeSeries)
      throws DataLakeAdminApiException {
    assertPresent(pGroupId, "groupId");
    assertPresent(pDatasetName, "datasetName");
    assertPresent(pMetadataProvider, "metadataProvider");
    assertPresent(pMetadataRegion, "metadataRegion");
    assertPresent(pPartitionSetKeys, "partitionSetKeys");

    final InitializeDataSetRequest request =
        InitializeDataSetRequest.newBuilder()
            .setProjectId(pGroupId.toString())
            .setDataSetName(pDatasetName)
            .setDataSetLocation(
                MetadataLocation.newBuilder()
                    .setProvider(pMetadataProvider)
                    .setRegion(pMetadataRegion)
                    .build())
            .setPartitionSetName(pPartitionSetName)
            .addAllPartitionSetKeys(pPartitionSetKeys)
            .setIsTimeSeries(pIsTimeSeries)
            .build();

    final var stub =
        getDataLakeGrpcClient(pMetadataProvider)
            .getStorageServiceStub(INITIALIZE_DATA_SETS_DEADLINE_SECONDS);
    final Timer timer = startDurationTimer(Methods.INITIALIZE_DATASET);
    final Timer timerV3 =
        startDurationTimerV3(Methods.INITIALIZE_DATASET, pMetadataProvider, pMetadataRegion);
    try {
      incrementAdminApiRequestCounter(Methods.INITIALIZE_DATASET);
      return stub.initializeDataSet(request);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      // ignore ALREADY_EXISTS for the dataSet initialization operation
      if (status.getCode() != Status.ALREADY_EXISTS.getCode()) {
        incrementAdminApiExceptionCounter(pE, Methods.INITIALIZE_DATASET, status);
        incrementAdminApiExceptionCounterV3(
            pE, Methods.INITIALIZE_DATASET, status, pMetadataProvider, pMetadataRegion);
        throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
      }
      return InitializeDataSetResponse.getDefaultInstance();
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
  }

  public void enableOADataSet(
      final ObjectId pGroupId,
      final String pDatasetName,
      final String pMetadataProvider,
      final String pMetadataRegion)
      throws DataLakeAdminApiException {

    assertPresent(pGroupId, "groupId");
    assertPresent(pDatasetName, "datasetName");
    assertPresent(pMetadataProvider, "metadataProvider");
    assertPresent(pMetadataRegion, "metadataRegion");

    final EnableOADataSetRequest req =
        EnableOADataSetRequest.newBuilder()
            .setProjectId(pGroupId.toString())
            .setDataSetName(pDatasetName)
            .setMetadataLocation(
                MetadataLocation.newBuilder()
                    .setProvider(pMetadataProvider)
                    .setRegion(pMetadataRegion)
                    .build())
            .build();
    final var stub = getDataLakeGrpcClient(pMetadataProvider).getStorageServiceStub();
    final Timer timer = startDurationTimer(Methods.ENABLE_OA_DATASET);
    try {
      incrementAdminApiRequestCounter(Methods.ENABLE_OA_DATASET);
      stub.enableOADataSet(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.ENABLE_OA_DATASET, status);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public void mergeDatasets(
      final ObjectId pGroupId,
      final String pSourceDatasetName,
      final String pDestinationDatasetName,
      final String pMetadataProvider,
      final String pMetadataRegion)
      throws DataLakeAdminApiException {

    assertPresent(pGroupId, "groupId");
    assertPresent(pSourceDatasetName, "sourceDatasetName");
    assertPresent(pDestinationDatasetName, "destinationDatasetName");
    assertPresent(pMetadataProvider, "metadataProvider");
    assertPresent(pMetadataRegion, "metadataRegion");

    final var stub =
        getDataLakeGrpcClient(pMetadataProvider)
            .getStorageServiceStub(MERGE_DATASETS_DEADLINE_SECONDS);
    final MergeDataSetsRequest req =
        MergeDataSetsRequest.newBuilder()
            .setProjectId(pGroupId.toString())
            .setSourceDataSetName(pSourceDatasetName)
            .setDestinationDataSetName(pDestinationDatasetName)
            .setDataSetLocation(
                MetadataLocation.newBuilder()
                    .setProvider(pMetadataProvider)
                    .setRegion(pMetadataRegion)
                    .build())
            .build();

    final Timer timer = startDurationTimer(Methods.MERGE_DATA_SETS);
    final Timer timerV3 =
        startDurationTimerV3(Methods.MERGE_DATA_SETS, pMetadataProvider, pMetadataRegion);
    try {
      incrementAdminApiRequestCounter(Methods.MERGE_DATA_SETS);
      stub.mergeDataSets(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.MERGE_DATA_SETS, status);
      incrementAdminApiExceptionCounterV3(
          pE, Methods.MERGE_DATA_SETS, status, pMetadataProvider, pMetadataRegion);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
  }

  private List<DataSetRequest> makeDataSetRequests(
      final ObjectId pGroupId, final List<? extends NDSDataSetDLS> pDataSets)
      throws DataLakeAdminApiException {
    assertPresent(pDataSets, "dataSets");

    // assemble request first to validate the passed fields
    return pDataSets.stream()
        .filter(this::isValidDLSDataSet)
        .filter(dataSet -> dataSet.getGroupId().equals(pGroupId))
        .map(
            dataSet ->
                makeDataSetRequest(
                    dataSet.getGroupId(),
                    dataSet.getName(),
                    dataSet.getMetadataProvider(),
                    dataSet.getMetadataRegion()))
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private void performDataSetValidation(final NDSDataSetDLS pDataSet)
      throws DataLakeAdminApiException {
    assertPresent(pDataSet, "dataSet");
    assertPresent(pDataSet.getGroupId(), "groupId");
    assertPresent(pDataSet.getName(), "datasetName");
    assertPresent(pDataSet.getMetadataProvider(), "metadataProvider");
    assertPresent(pDataSet.getMetadataRegion(), "metadataRegion");
  }

  private boolean isValidDLSDataSet(final NDSDataSetDLS pDataSet) {
    try {
      performDataSetValidation(pDataSet);
      return true;
    } catch (final DataLakeAdminApiException pException) {
      // do nothing here -> fall through to returning false
    }
    return false;
  }

  private DataSetRequest makeDataSetRequest(
      final ObjectId pGroupId,
      final String pDatasetName,
      final String pMetadataProvider,
      final String pMetadataRegion) {
    try {
      return DataSetRequest.newBuilder()
          .setProjectId(pGroupId.toString())
          .setDataSetName(pDatasetName)
          .setDataSetLocation(
              MetadataLocation.newBuilder()
                  .setProvider(getDLSAcceptedCloudProvider(pMetadataProvider))
                  .setRegion(pMetadataRegion)
                  .build())
          .build();
    } catch (final SvcException pException) {
      LOG.error(
          "({})",
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID, pGroupId,
              DataLakeLogsUtil.FieldDefs.DATA_SET_NAME, pDatasetName),
          pException);
    }
    return null;
  }

  @VisibleForTesting
  protected String getCloudProvider(final List<DataSetRequest> pRequests)
      throws DataLakeAdminApiException {
    final List<String> cloudProviders =
        pRequests.stream()
            .map(r -> r.getDataSetLocation().getProvider())
            .distinct()
            .collect(Collectors.toList());
    if (cloudProviders.size() != 1) {
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_SETS_REQUEST_INVALID_CLOUD_PROVIDER);
    }
    return cloudProviders.get(0);
  }

  /** Gets the first region in the list of requests. Returns unknown region by default. */
  @VisibleForTesting
  protected String getFirstRegion(final List<DataSetRequest> pRequests) {
    return pRequests.stream()
        .map(r -> r.getDataSetLocation().getRegion())
        .findFirst()
        .orElse(REGION_UNKNOWN);
  }

  public DataLakeAdminApiOperationOutcome enableDataSets(
      final ObjectId pGroupId, final List<NDSDataSetDLS> pDataSets)
      throws DataLakeAdminApiException {
    final List<DataSetRequest> requests = makeDataSetRequests(pGroupId, pDataSets);
    final CountDownLatch latch = new CountDownLatch(1);
    final DataSetOperationResponseObserver<EnableDataSetsResponse> observer =
        new DataSetOperationResponseObserver<>(
            Methods.ENABLE_DATA_SET, pGroupId, requests.size(), latch, LOG);
    return enableDataSets(pGroupId, requests, latch, observer);
  }

  public DataLakeAdminApiOperationOutcome disableDataSets(
      final ObjectId pGroupId, final List<? extends NDSDataSetDLS> pDataSets)
      throws DataLakeAdminApiException {
    final List<? extends NDSDataSetDLS> notDeletedDataSets =
        pDataSets.stream().filter(ds -> !ds.getState().isDeleted()).collect(Collectors.toList());
    final int alreadyDeletedCount = pDataSets.size() - notDeletedDataSets.size();
    if (alreadyDeletedCount > 0) {
      LOG.info(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM,
              DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID,
              pGroupId),
          String.format("Skipping disabling %s deleted data sets for group", alreadyDeletedCount));
    }
    if (notDeletedDataSets.isEmpty()) {
      LOG.info(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM,
              DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID,
              pGroupId),
          "skipping disabling as all data sets has been filtered out for group");
      return DataLakeAdminApiOperationOutcome.ok();
    }

    final List<DataSetRequest> requests = makeDataSetRequests(pGroupId, notDeletedDataSets);
    final CountDownLatch latch = new CountDownLatch(1);
    final DataSetOperationResponseObserver<DisableDataSetsResponse> observer =
        new DataSetOperationResponseObserver<>(
            Methods.DISABLE_DATA_SET, pGroupId, requests.size(), latch, LOG);
    return disableDataSets(pGroupId, requests, latch, observer);
  }

  public List<DataLakeAdminApiOperationOutcome> destroyDataSets(
      final ObjectId pGroupId, final List<NDSDataSetDLS> pDataSets)
      throws DataLakeAdminApiException {
    final List<NDSDataSetDLS> dateSetsNotInDeletingState =
        pDataSets.stream()
            .filter(ds -> !NDSDataSet.State.DELETING.equals(ds.getState()))
            .collect(Collectors.toList());
    if (!dateSetsNotInDeletingState.isEmpty()) {
      LOG.warn(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM,
              DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID,
              pGroupId),
          "the following data sets are not in DELETING state. This is generally not an issue."
              + " Will proceed to destroy and log them here.");
      dateSetsNotInDeletingState.forEach(
          ds ->
              LOG.warn(
                  DataLakeLogsUtil.LOG_FORMAT,
                  LogUtils.entries(
                      DataLakeLogsUtil.FieldDefs.TEAM,
                      DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                      DataLakeLogsUtil.FieldDefs.GROUP_ID,
                      pGroupId,
                      DataLakeLogsUtil.FieldDefs.DATA_SET_NAME,
                      ds.getName()),
                  String.format(
                      "destroying data set %s in state %s", ds.getName(), ds.getState())));
    }

    return processDataSetsByProvider(
        "Deleting",
        pDataSets,
        dataSets -> {
          final List<DataSetRequest> requests = makeDataSetRequests(pGroupId, dataSets);
          final CountDownLatch latch = new CountDownLatch(1);
          final DataSetOperationResponseObserver<DestroyDataSetsResponse> observer =
              new DataSetOperationResponseObserver<>(
                  Methods.DESTROY_DATA_SET, pGroupId, requests.size(), latch, LOG);
          return destroyDataSets(pGroupId, requests, latch, observer);
        });
  }

  // Group data sets by metadata provider and run the provided function separately for each batch
  private List<DataLakeAdminApiOperationOutcome> processDataSetsByProvider(
      final String pOperation,
      final List<? extends NDSDataSetDLS> pDataSets,
      final DataSetProcessor pFn)
      throws DataLakeAdminApiException {
    final List<DataLakeAdminApiOperationOutcome> outcomes = new ArrayList<>();
    for (final Entry<String, ? extends List<? extends NDSDataSetDLS>> entry :
        pDataSets.stream()
            .collect(Collectors.groupingBy(NDSDataSetDLS::getMetadataProvider))
            .entrySet()) {
      LOG.info(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM),
          String.format(
              "%s %s data sets in %s", pOperation, entry.getValue().size(), entry.getKey()));
      outcomes.add(pFn.apply(entry.getValue()));
    }
    return outcomes;
  }

  public DataLakeAdminApiOperationOutcome reconcileDataSets(
      final ObjectId pGroupId, final List<NDSDataSetDLS> pDataSets)
      throws DataLakeAdminApiException {
    final List<DataSetRequest> requests = makeDataSetRequests(pGroupId, pDataSets);
    final CountDownLatch latch = new CountDownLatch(1);
    final DataSetOperationResponseObserver<ReconcileDataSetsResponse> observer =
        new DataSetOperationResponseObserver<>(
            Methods.RECONCILE_DATA_SETS, pGroupId, requests.size(), latch, LOG);
    return reconcileDataSets(pGroupId, requests, latch, observer);
  }

  private DataLakeAdminApiOperationOutcome enableDataSets(
      final ObjectId pGroupId,
      final List<DataSetRequest> pRequests,
      final CountDownLatch pLatch,
      final DataSetOperationResponseObserver<EnableDataSetsResponse>
          pDataSetOperationResponseObserver)
      throws DataLakeAdminApiException {
    final String cloudProvider = getCloudProvider(pRequests);
    final String region = getFirstRegion(pRequests);
    final StreamObserver<DataSetRequest> requester =
        getDataLakeGrpcClient(cloudProvider)
            .getStorageServiceAsyncStub(DATA_SETS_OPERATION_DEADLINE_SECONDS)
            .enableDataSets(pDataSetOperationResponseObserver.getResponseObserver());
    return performDataSetOperation(
        Methods.ENABLE_DATA_SET,
        pGroupId,
        pRequests,
        pLatch,
        pDataSetOperationResponseObserver,
        requester,
        cloudProvider,
        region);
  }

  private DataLakeAdminApiOperationOutcome disableDataSets(
      final ObjectId pGroupId,
      final List<DataSetRequest> pRequests,
      final CountDownLatch pLatch,
      final DataSetOperationResponseObserver<DisableDataSetsResponse>
          pDataSetOperationResponseObserver)
      throws DataLakeAdminApiException {
    final String cloudProvider = getCloudProvider(pRequests);
    final String region = getFirstRegion(pRequests);
    final StreamObserver<DataSetRequest> requester =
        getDataLakeGrpcClient(cloudProvider)
            .getStorageServiceAsyncStub(DATA_SETS_OPERATION_DEADLINE_SECONDS)
            .disableDataSets(pDataSetOperationResponseObserver.getResponseObserver());
    return performDataSetOperation(
        Methods.DISABLE_DATA_SET,
        pGroupId,
        pRequests,
        pLatch,
        pDataSetOperationResponseObserver,
        requester,
        cloudProvider,
        region);
  }

  private DataLakeAdminApiOperationOutcome destroyDataSets(
      final ObjectId pGroupId,
      final List<DataSetRequest> pRequests,
      final CountDownLatch pLatch,
      final DataSetOperationResponseObserver<DestroyDataSetsResponse>
          pDataSetOperationResponseObserver)
      throws DataLakeAdminApiException {
    final String cloudProvider = getCloudProvider(pRequests);
    final String region = getFirstRegion(pRequests);
    final StreamObserver<DataSetRequest> requester =
        getDataLakeGrpcClient(cloudProvider)
            .getStorageServiceAsyncStub(DESTROY_DATA_SETS_DEADLINE_SECONDS)
            .destroyDataSets(pDataSetOperationResponseObserver.getResponseObserver());
    return performDataSetOperation(
        Methods.DESTROY_DATA_SET,
        pGroupId,
        pRequests,
        pLatch,
        pDataSetOperationResponseObserver,
        requester,
        cloudProvider,
        region);
  }

  private DataLakeAdminApiOperationOutcome reconcileDataSets(
      final ObjectId pGroupId,
      final List<DataSetRequest> pRequests,
      final CountDownLatch pLatch,
      final DataSetOperationResponseObserver<ReconcileDataSetsResponse>
          pDataSetOperationResponseObserver)
      throws DataLakeAdminApiException {
    final String cloudProvider = getCloudProvider(pRequests);
    final String region = getFirstRegion(pRequests);
    final StreamObserver<DataSetRequest> requester =
        getDataLakeGrpcClient(cloudProvider)
            .getStorageServiceAsyncStub(DATA_SETS_OPERATION_DEADLINE_SECONDS)
            .reconcileDataSets(pDataSetOperationResponseObserver.getResponseObserver());
    return performDataSetOperation(
        Methods.RECONCILE_DATA_SETS,
        pGroupId,
        pRequests,
        pLatch,
        pDataSetOperationResponseObserver,
        requester,
        cloudProvider,
        region);
  }

  private <T> DataLakeAdminApiOperationOutcome performDataSetOperation(
      final String pDataSetOperation,
      final ObjectId pGroupId,
      final List<DataSetRequest> pRequests,
      final CountDownLatch pLatch,
      final DataSetOperationResponseObserver<T> pResponseObserver,
      final StreamObserver<DataSetRequest> pRequester,
      final String pCloudProvider,
      final String pRegion)
      throws DataLakeAdminApiException {
    final Timer timer = startDurationTimer(pDataSetOperation);
    final Timer timerV3 = startDurationTimerV3(pDataSetOperation, pCloudProvider, pRegion);
    incrementAdminApiRequestCounter(pDataSetOperation);
    try {
      // send data set operation requests to DLS
      for (final DataSetRequest request : pRequests) {
        LOG.info(
            DataLakeLogsUtil.LOG_FORMAT,
            LogUtils.entries(
                DataLakeLogsUtil.FieldDefs.TEAM,
                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                DataLakeLogsUtil.FieldDefs.GROUP_ID,
                pGroupId,
                DataLakeLogsUtil.FieldDefs.DATA_SET_NAME,
                request.getDataSetName()),
            "requester onNext :: sent operation request for data set");
        pRequester.onNext(request);
      }

      // signals to DLS that we've sent all requests and DLS can begin work
      pRequester.onCompleted();
      LOG.info(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM,
              DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID,
              pGroupId),
          "requester onCompleted :: all requests have been sent for work");

      // await for DLS to respond
      if (!pLatch.await(DATA_SETS_OPERATION_DEADLINE_SECONDS, TimeUnit.SECONDS)) {
        LOG.warn(
            DataLakeLogsUtil.LOG_FORMAT,
            LogUtils.entries(
                DataLakeLogsUtil.FieldDefs.TEAM,
                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                DataLakeLogsUtil.FieldDefs.GROUP_ID,
                pGroupId),
            String.format(
                "failed to complete '%s' operation for %s data sets in project: '{}'",
                pDataSetOperation, pRequests.size()));
        return DataLakeAdminApiOperationOutcome.from(
            new StatusRuntimeException(Status.DEADLINE_EXCEEDED));
      } else {
        LOG.info(
            DataLakeLogsUtil.LOG_FORMAT,
            LogUtils.entries(
                DataLakeLogsUtil.FieldDefs.TEAM,
                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                DataLakeLogsUtil.FieldDefs.GROUP_ID,
                pGroupId),
            "countdown latch done, checking response");
        if (pResponseObserver.wasSuccessful()) {
          return DataLakeAdminApiOperationOutcome.ok();
        } else {
          throw pResponseObserver.getError();
        }
      }
    } catch (final Exception pException) {
      LOG.error(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM,
              DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID,
              pGroupId),
          String.format("encountered exception during '%s' operation", pDataSetOperation),
          pException);
      final Status status = translateExceptionIntoStatus(pException);
      incrementAdminApiExceptionCounter(pException, pDataSetOperation, status);
      incrementAdminApiExceptionCounterV3(
          pException, pDataSetOperation, status, pCloudProvider, pRegion);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pException);
    } finally {
      LOG.info(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(
              DataLakeLogsUtil.FieldDefs.TEAM,
              DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
              DataLakeLogsUtil.FieldDefs.GROUP_ID,
              pGroupId),
          "finally block for performDataSetOperation");
      NDSPromMetricsSvc.recordTimer(timer);
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
  }

  public UploadDataFilesResponse uploadDataFiles(
      final ObjectId pGroupId,
      final String pDataSetName,
      final String pDatasetProvider,
      final String pDatasetRegion,
      final String pManifestProvider,
      final String pManifestRegion,
      final String pBucket,
      final String pManifestPath,
      final String pJobId)
      throws DataLakeAdminApiException {
    return uploadDataFiles(
        pGroupId,
        pDataSetName,
        pDatasetProvider,
        pDatasetRegion,
        pManifestProvider,
        pManifestRegion,
        pBucket,
        pManifestPath,
        pJobId,
        null);
  }

  public UploadDataFilesResponse uploadDataFiles(
      final ObjectId pGroupId,
      final String pDataSetName,
      final String pDatasetProvider,
      final String pDatasetRegion,
      final String pManifestProvider,
      final String pManifestRegion,
      final String pBucket,
      final String pManifestPath,
      final String pJobId,
      final ManifestLocationView pManifestLocation)
      throws DataLakeAdminApiException {
    assertPresent(pGroupId, "groupId");
    assertPresent(pDataSetName, "dataSetName");
    assertPresent(pJobId, "jobId");

    final UploadDataFilesRequest.Builder requestBuilder =
        UploadDataFilesRequest.newBuilder()
            .setProjectId(pGroupId.toString())
            .setDataSetName(pDataSetName)
            .setJobId(pJobId);

    final String provider;
    final String region;
    // use manifestLocation field if exists to format request, otherwise format request using old
    // fields
    if (pManifestLocation != null) {
      assertPresent(pManifestLocation.getProvider(), "provider");
      assertPresent(pManifestLocation.getRegion(), "region");
      assertPresent(pManifestLocation.getBlobSource(), "blobSource");
      assertPresent(pManifestLocation.getManifest(), "manifest");

      provider = pManifestLocation.getProvider();
      region = pManifestLocation.getRegion();

      requestBuilder.setDataSetLocation(
          MetadataLocation.newBuilder().setRegion(region).setProvider(provider).build());

      // format request based on available blob source, otherwise throw exception for unknown blob
      // source
      final CloudProvider switchProvider =
          CloudProvider.findByChefProvider(provider)
              .orElseThrow(
                  () ->
                      new DataLakeAdminApiException(NDSErrorCode.INVALID_CLOUD_PROVIDER, provider));
      switch (switchProvider) {
        case AWS:
          assertPresent(pManifestLocation.getBlobSource().getBlobSourceS3(), "blobSourceS3");

          final OnlineArchiveUploadDataFilesRequestView.BlobSourceS3View blobSourceS3 =
              pManifestLocation.getBlobSource().getBlobSourceS3();

          assertPresent(blobSourceS3.getBucket(), "bucket");

          requestBuilder.setManifestLocation(
              BlobLocation.newBuilder()
                  .setProvider(provider)
                  .setS3(
                      BlobSourceS3.newBuilder()
                          .setBucket(blobSourceS3.getBucket())
                          .setRegion(region)
                          .build())
                  .setPath(pManifestLocation.getManifest())
                  .build());
          break;
        case AZURE:
          assertPresent(pManifestLocation.getBlobSource().getBlobSourceAzure(), "blobSourceAzure");

          final OnlineArchiveUploadDataFilesRequestView.BlobSourceAzureView blobSourceAzure =
              pManifestLocation.getBlobSource().getBlobSourceAzure();

          assertPresent(blobSourceAzure.getServiceUrl(), "serviceURL");
          assertPresent(blobSourceAzure.getContainerName(), "containerName");

          requestBuilder.setManifestLocation(
              BlobLocation.newBuilder()
                  .setProvider(provider)
                  .setAzure(
                      BlobSourceAzure.newBuilder()
                          .setServiceUrl(blobSourceAzure.getServiceUrl())
                          .setContainerName(blobSourceAzure.getContainerName())
                          .setRegion(region)
                          .build())
                  .setPath(pManifestLocation.getManifest())
                  .build());
          break;
        case GCP:
          assertPresent(pManifestLocation.getBlobSource().getBlobSourceGcs(), "blobSourceGcs");

          final OnlineArchiveUploadDataFilesRequestView.BlobSourceGcsView blobSourceGcs =
              pManifestLocation.getBlobSource().getBlobSourceGcs();

          assertPresent(blobSourceGcs.getBucket(), "bucket");

          requestBuilder.setManifestLocation(
              BlobLocation.newBuilder()
                  .setProvider(provider)
                  .setGcs(
                      BlobSourceGCS.newBuilder()
                          .setBucket(blobSourceGcs.getBucket())
                          .setRegion(region)
                          .build())
                  .setPath(pManifestLocation.getManifest())
                  .build());
          break;
        default:
          throw new DataLakeAdminApiException(
              NDSErrorCode.DATA_LAKE_BLOB_SOURCE_NOT_FOUND, pGroupId.toString(), provider);
      }
    } else { // old request format, TODO(CLOUDP-173353): remove logic
      assertPresent(pDatasetProvider, "metadataProvider");
      assertPresent(pDatasetRegion, "metadataRegion");
      assertPresent(pManifestProvider, "provider");
      assertPresent(pManifestRegion, "region");
      assertPresent(pBucket, "bucket");
      assertPresent(pManifestPath, "manifestPath");

      provider = pDatasetProvider;
      region = pDatasetRegion;
      requestBuilder
          .setDataSetLocation(
              MetadataLocation.newBuilder()
                  .setRegion(pDatasetRegion)
                  .setProvider(pDatasetProvider)
                  .build())
          .setProvider(pManifestProvider)
          .setRegion(pManifestRegion)
          .setBucket(pBucket)
          .setManifest(pManifestPath);
    }

    final UploadDataFilesRequest request = requestBuilder.build();

    final var stub = getDataLakeGrpcClient(provider).getStorageServiceStub();
    final Timer timer = startDurationTimer(Methods.UPLOAD_DATA_FILES);
    final Timer timerV3 = startDurationTimerV3(Methods.UPLOAD_DATA_FILES, provider, region);
    try {
      incrementAdminApiRequestCounter(Methods.UPLOAD_DATA_FILES);
      return stub.uploadDataFiles(request);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.UPLOAD_DATA_FILES, status);
      incrementAdminApiExceptionCounterV3(pE, Methods.UPLOAD_DATA_FILES, status, provider, region);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
  }

  public void disableOADataSet(
      final ObjectId pGroupId,
      final String pDatasetName,
      final String pMetadataProvider,
      final String pMetadataRegion)
      throws DataLakeAdminApiException {
    assertPresent(pGroupId, "groupId");
    assertPresent(pDatasetName, "datasetName");
    assertPresent(pMetadataProvider, "metadataProvider");
    assertPresent(pMetadataRegion, "metadataRegion");

    final DisableOADataSetRequest req =
        DisableOADataSetRequest.newBuilder()
            .setProjectId(pGroupId.toString())
            .setDataSetName(pDatasetName)
            .setMetadataLocation(
                MetadataLocation.newBuilder()
                    .setProvider(pMetadataProvider)
                    .setRegion(pMetadataRegion)
                    .build())
            .build();
    final var stub = getDataLakeGrpcClient(pMetadataProvider).getStorageServiceStub();
    final Timer timer = startDurationTimer(Methods.DISABLE_OA_DATASET);
    try {
      incrementAdminApiRequestCounter(Methods.DISABLE_OA_DATASET);
      stub.disableOADataSet(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.DISABLE_OA_DATASET, status);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public void registerOAFiles(
      final ObjectId pGroupId,
      final ObjectId pArchiveId,
      final String pJobId,
      final String pDatasetName,
      final String pMetadataProvider,
      final String pMetadataRegion,
      final String pProvider,
      final String pRegion,
      final String pBucket,
      final String pManifestPath,
      final String pCredentialSessionId)
      throws DataLakeAdminApiException {
    assertPresent(pGroupId, "groupId");
    assertPresent(pArchiveId, "archiveId");
    assertPresent(pJobId, "jobId");
    assertPresent(pDatasetName, "datasetName");
    assertPresent(pMetadataProvider, "metadataProvider");
    assertPresent(pMetadataRegion, "metadataRegion");
    assertPresent(pProvider, "provider");
    assertPresent(pRegion, "region");
    assertPresent(pBucket, "bucket");
    assertPresent(pManifestPath, "manifestPath");
    assertPresent(pCredentialSessionId, "credentialSessionId");

    LOG.info(
        DataLakeLogsUtil.LOG_FORMAT,
        LogUtils.entries(
            DataLakeLogsUtil.FieldDefs.TEAM,
            DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
            DataLakeLogsUtil.FieldDefs.GROUP_ID,
            pGroupId,
            OAFieldDefs.ARCHIVE_ID,
            pArchiveId,
            DataLakeLogsUtil.FieldDefs.DATA_SET_NAME,
            pDatasetName,
            OAFieldDefs.JOB_ID,
            pJobId,
            "metadataProvider",
            pMetadataProvider,
            "metadataRegion",
            pMetadataProvider,
            "provider",
            pProvider,
            "region",
            pRegion,
            "bucket",
            pBucket,
            "manifestPath",
            pManifestPath,
            "credentialSessionId",
            pCredentialSessionId),
        "making request to DLS to register OA files for V2 archive job");

    final RegisterOAFilesRequest req =
        RegisterOAFilesRequest.newBuilder()
            .setProjectId(pGroupId.toString())
            .setMetadataLocation(
                MetadataLocation.newBuilder()
                    .setProvider(pMetadataProvider)
                    .setRegion(pMetadataRegion)
                    .build())
            .setDataSetName(pDatasetName)
            .setArchiveId(pArchiveId.toString())
            .setJobId(pJobId)
            .setProvider(pProvider)
            .setRegion(pRegion)
            .setBucket(pBucket)
            .setManifest(pManifestPath)
            .setCredentialSessionId(pCredentialSessionId)
            .build();
    final var stub = getDataLakeGrpcClient(pMetadataProvider).getStorageServiceStub();
    final Timer timer = startDurationTimer(Methods.REGISTER_OA_FILES);
    try {
      incrementAdminApiRequestCounter(Methods.REGISTER_OA_FILES);
      stub.registerOAFiles(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.REGISTER_OA_FILES, status);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public DeregisterOAFilesResponse deregisterOAFiles(
      final ObjectId pGroupId,
      final ObjectId pArchiveId,
      final String pJobId,
      final String pDatasetName,
      final String pMetadataProvider,
      final String pMetadataRegion,
      final String pProvider,
      final String pRegion,
      final String pBucket,
      final String pManifestPath,
      final String pCredentialSessionId)
      throws DataLakeAdminApiException {
    assertPresent(pGroupId, "groupId");
    assertPresent(pArchiveId, "archiveId");
    assertPresent(pJobId, "jobId");
    assertPresent(pDatasetName, "datasetName");
    assertPresent(pMetadataProvider, "metadataProvider");
    assertPresent(pMetadataRegion, "metadataRegion");
    assertPresent(pProvider, "provider");
    assertPresent(pRegion, "region");
    assertPresent(pBucket, "bucket");
    assertPresent(pManifestPath, "manifestPath");
    assertPresent(pCredentialSessionId, "credentialSessionId");

    LOG.info(
        DataLakeLogsUtil.LOG_FORMAT,
        LogUtils.entries(
            DataLakeLogsUtil.FieldDefs.TEAM,
            DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
            DataLakeLogsUtil.FieldDefs.GROUP_ID,
            pGroupId,
            OAFieldDefs.ARCHIVE_ID,
            pArchiveId,
            DataLakeLogsUtil.FieldDefs.DATA_SET_NAME,
            pDatasetName,
            OAFieldDefs.JOB_ID,
            pJobId,
            "metadataProvider",
            pMetadataProvider,
            "metadataRegion",
            pMetadataProvider,
            "provider",
            pProvider,
            "region",
            pRegion,
            "bucket",
            pBucket,
            "manifestPath",
            pManifestPath,
            "credentialSessionId",
            pCredentialSessionId),
        "making request to DLS to deregister OA files for V2 archive job");

    final DeregisterOAFilesRequest req =
        DeregisterOAFilesRequest.newBuilder()
            .setProjectId(pGroupId.toString())
            .setMetadataLocation(
                MetadataLocation.newBuilder()
                    .setProvider(pMetadataProvider)
                    .setRegion(pMetadataRegion)
                    .build())
            .setDataSetName(pDatasetName)
            .setArchiveId(pArchiveId.toString())
            .setJobId(pJobId)
            .setProvider(pProvider)
            .setRegion(pRegion)
            .setBucket(pBucket)
            .setManifest(pManifestPath)
            .setCredentialSessionId(pCredentialSessionId)
            .build();
    final var stub = getDataLakeGrpcClient(pMetadataProvider).getStorageServiceStub();
    final Timer timer = startDurationTimer(Methods.DEREGISTER_OA_FILES);
    try {
      incrementAdminApiRequestCounter(Methods.DEREGISTER_OA_FILES);
      return stub.deregisterOAFiles(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.DEREGISTER_OA_FILES, status);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public void destroyOADataSet(
      @NotNull final ObjectId pGroupId,
      @NotNull final String pDatasetName,
      @NotNull final String pMetadataProvider,
      @NotNull final String pMetadataRegion)
      throws DataLakeAdminApiException {
    assertPresent(pGroupId, "groupId");
    assertPresent(pDatasetName, "datasetName");
    assertPresent(pMetadataProvider, "metadataProvider");
    assertPresent(pMetadataRegion, "metadataRegion");

    final DestroyOADataSetRequest req =
        DestroyOADataSetRequest.newBuilder()
            .setProjectId(pGroupId.toString())
            .setDataSetName(pDatasetName)
            .setMetadataLocation(
                MetadataLocation.newBuilder()
                    .setProvider(pMetadataProvider)
                    .setRegion(pMetadataRegion)
                    .build())
            .build();
    final var stub = getDataLakeGrpcClient(pMetadataProvider).getStorageServiceStub();
    final Timer timer = startDurationTimer(Methods.DESTROY_OA_DATASET);
    try {
      incrementAdminApiRequestCounter(Methods.DESTROY_OA_DATASET);
      stub.destroyOADataSet(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.DESTROY_OA_DATASET, status);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public GetJobProgressResponseView getJobProgress(
      final String pJobId, final String pCloudProvider, final String pRegion)
      throws DataLakeAdminApiException {
    assertPresent(pJobId, "jobId");
    assertPresent(pCloudProvider, "cloudProvider");
    assertPresent(pRegion, "region");
    final Timer timerV3 = startDurationTimerV3(Methods.GET_JOB_PROGRESS, pCloudProvider, pRegion);
    try {
      return getJobProgress(pJobId, pCloudProvider);
    } catch (final Exception e) {
      final Status status = translateExceptionIntoStatus(e);
      incrementAdminApiExceptionCounterV3(
          e, Methods.GET_JOB_PROGRESS, status, pCloudProvider, pRegion);
      throw e;
    } finally {
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
  }

  public GetJobProgressResponseView getJobProgress(final String pJobId, final String pCloudProvider)
      throws DataLakeAdminApiException {
    assertPresent(pJobId, "jobId");
    assertPresent(pCloudProvider, "cloudProvider");
    final GetJobProgressRequest req = GetJobProgressRequest.newBuilder().setJobId(pJobId).build();
    final var stub =
        getDataLakeGrpcClient(pCloudProvider)
            .getStorageServiceStub(GET_DLS_JOB_PROGRESS_OR_ERROR_SECONDS);
    final Timer timer = startDurationTimer(Methods.GET_JOB_PROGRESS);
    try {
      incrementAdminApiRequestCounter(Methods.GET_JOB_PROGRESS);
      final GetJobProgressResponse resp = stub.getJobProgress(req);
      return GetJobProgressResponseView.fromGrpcResponse(resp);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.GET_JOB_PROGRESS, status);
      // Equality on Statuses is not well defined. Instead, doing comparison based on their Code
      if (status.getCode() == Status.NOT_FOUND.getCode()) {
        throw new DataLakeAdminApiException(
            NDSErrorCode.ONLINE_ARCHIVE_JOB_ID_NOT_FOUND, pE, pJobId);
      }
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public Iterator<GetJobErrorsResponse> getJobErrors(
      final String pJobId, final String pCloudProvider, final String pRegion)
      throws DataLakeAdminApiException {
    assertPresent(pJobId, "jobId");
    assertPresent(pCloudProvider, "cloudProvider");
    assertPresent(pRegion, "region");
    final Timer timerV3 = startDurationTimerV3(Methods.GET_JOB_ERRORS, pCloudProvider, pRegion);
    try {
      return getJobErrors(pJobId, pCloudProvider);
    } catch (final Exception e) {
      final Status status = translateExceptionIntoStatus(e);
      incrementAdminApiExceptionCounterV3(
          e, Methods.GET_JOB_ERRORS, status, pCloudProvider, pRegion);
      throw e;
    } finally {
      NDSPromMetricsSvc.recordTimer(timerV3);
    }
  }

  public Iterator<GetJobErrorsResponse> getJobErrors(
      final String pJobId, final String pCloudProvider) throws DataLakeAdminApiException {
    assertPresent(pJobId, "jobId");
    assertPresent(pCloudProvider, "cloudProvider");
    final GetJobErrorsRequest req = GetJobErrorsRequest.newBuilder().setJobId(pJobId).build();
    final var stub =
        getDataLakeGrpcClient(pCloudProvider)
            .getStorageServiceStub(GET_DLS_JOB_PROGRESS_OR_ERROR_SECONDS);
    final Timer timer = startDurationTimer(Methods.GET_JOB_ERRORS);
    try {
      incrementAdminApiRequestCounter(Methods.GET_JOB_ERRORS);
      return stub.getJobErrors(req);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.GET_JOB_ERRORS, status);
      // Equality on Statuses is not well defined. Instead, doing comparison based on their Code
      if (status.getCode() == Status.NOT_FOUND.getCode()) {
        throw new DataLakeAdminApiException(
            NDSErrorCode.ONLINE_ARCHIVE_JOB_ID_NOT_FOUND, pE, pJobId);
      }
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  private NDSDataLakeSQLSchemaView parseSchemaBSON(DocumentCodec dc, ByteString rawSchema) {
    Document schema =
        dc.decode(
            new BsonBinaryReader(rawSchema.asReadOnlyByteBuffer()),
            DecoderContext.builder().build());

    // Read version as a Number to support both long and int types.
    final String version =
        schema.get(NDSDataLakeSQLSchemaView.FieldDefs.VERSION) != null
            ? ((Number)
                    schema.get(NDSDataLakeSQLSchemaView.FieldDefs.VERSION, DEFAULT_SCHEMA_VERSION))
                .toString()
            : "";

    final String jsonSchema =
        schema.get(NDSDataLakeSQLSchemaView.FieldDefs.JSON_SCHEMA) != null
            ? schema.get(NDSDataLakeSQLSchemaView.FieldDefs.JSON_SCHEMA, Document.class).toJson()
            : "";

    return NDSDataLakeSQLSchemaView.builder().version(version).jsonSchema(jsonSchema).build();
  }

  private Bson.Value parseSchemaJSONString(NDSDataLakeSQLSchemaView pSchemaView)
      throws IOException {
    DocumentCodec dc = new DocumentCodec();
    final int version = Integer.parseInt(pSchemaView.getVersion());

    final BasicOutputBuffer buffer = new BasicOutputBuffer();
    dc.encode(
        new BsonBinaryWriter(buffer),
        new Document()
            .append("version", (long) version)
            .append(
                "jsonSchema",
                Document.parse(new JSONObject(pSchemaView.getJsonSchema()).toString())),
        EncoderContext.builder().build());

    final ByteArrayOutputStream os = new ByteArrayOutputStream();
    buffer.pipe(os);

    final Bson.Type schemaType = Bson.Type.TYPE_EMBEDDED_DOCUMENT;
    Bson.Value.Builder newBuilder = Bson.Value.newBuilder().setType(schemaType);

    return newBuilder.setData(ByteString.copyFrom(os.toByteArray())).build();
  }

  public NDSDataLakeSQLScheduledUpdateView getScheduledUpdate(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");

    final String tenantId = pTenant.getTenantId().toString();
    final GetScheduledUpdateRequest request =
        GetScheduledUpdateRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
            .build();
    final SchemaServiceGrpc.SchemaServiceBlockingStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceStub();

    final Timer timer = startDurationTimer(Methods.GET_SCHEDULED_UPDATE);
    try {
      incrementAdminApiRequestCounter(Methods.GET_SCHEDULED_UPDATE);
      GetScheduledUpdateResponse response = stub.getScheduledUpdate(request);
      return new NDSDataLakeSQLScheduledUpdateView(response.getFrequency());
    } catch (final StatusRuntimeException pE) {
      final Status status = pE.getStatus();

      // Equality on Statuses is not well-defined. Instead, doing comparison based on their Code
      if (status.getCode() == Status.NOT_FOUND.getCode()) {
        // NOT_FOUND is not something unexpected when we're checking if the schedule exists,
        // so we're not incrementing exception counter here, returning an unspecified frequency.
        return new NDSDataLakeSQLScheduledUpdateView();
      }
      incrementAdminApiExceptionCounter(pE, Methods.GET_SCHEDULED_UPDATE, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE, String.format("Tenant ID: %s", tenantId));
    } catch (final Exception pE) {
      incrementAdminApiExceptionCounter(pE, Methods.GET_SCHEDULED_UPDATE, Status.UNKNOWN);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE, String.format("Tenant ID: %s", tenantId));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public NDSDataLakeSQLScheduledUpdateView createScheduledUpdate(
      final NDSDataLakeTenant pTenant, final Frequency pUpdateFrequency, final String pUsername)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");

    final String tenantId = pTenant.getTenantId().toString();
    final CreateScheduledUpdateRequest request =
        CreateScheduledUpdateRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
            .setFrequency(pUpdateFrequency)
            .setUsername(pUsername)
            .build();
    final SchemaServiceGrpc.SchemaServiceBlockingStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceStub();

    executeWithTimer(
        Methods.CREATE_SCHEDULED_UPDATE, tenantId, () -> stub.createScheduledUpdate(request));
    return new NDSDataLakeSQLScheduledUpdateView(pUpdateFrequency);
  }

  public NDSDataLakeSQLScheduledUpdateView modifyScheduledUpdate(
      final NDSDataLakeTenant pTenant, final Frequency pUpdateFrequency, final String pUsername)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");

    final String tenantId = pTenant.getTenantId().toString();
    final ModifyScheduledUpdateRequest request =
        ModifyScheduledUpdateRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
            .setFrequency(pUpdateFrequency)
            .setUsername(pUsername)
            .build();
    final SchemaServiceGrpc.SchemaServiceBlockingStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceStub();

    executeWithTimer(
        Methods.MODIFY_SCHEDULED_UPDATE, tenantId, () -> stub.modifyScheduledUpdate(request));
    return new NDSDataLakeSQLScheduledUpdateView(pUpdateFrequency);
  }

  public NDSDataLakeSQLScheduledUpdateView deleteScheduledUpdate(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");

    final String tenantId = pTenant.getTenantId().toString();
    final DeleteScheduledUpdateRequest request =
        DeleteScheduledUpdateRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
            .build();
    final SchemaServiceGrpc.SchemaServiceBlockingStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceStub();

    executeWithTimer(
        Methods.DELETE_SCHEDULED_UPDATE, tenantId, () -> stub.deleteScheduledUpdate(request));
    return new NDSDataLakeSQLScheduledUpdateView();
  }

  public void setSchema(
      final NDSDataLakeTenant pTenant,
      final String pUsername,
      final String pDb,
      final String pCollection,
      final boolean pIsGenerated,
      final NDSDataLakeSQLSchemaView pSchemaView)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");
    assertPresent(pUsername, "username");

    final String tenantId = pTenant.getTenantId().toString();

    final SchemaServiceGrpc.SchemaServiceBlockingStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceStub();

    try {
      final SetSchemaRequest request =
          SetSchemaRequest.newBuilder()
              .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
              .setUsername(pUsername)
              .setSource(pIsGenerated ? Source.SOURCE_GENERATED_IN_UI : Source.SOURCE_SET_IN_UI)
              .setSchema(
                  Schema.newBuilder()
                      .setSchema(parseSchemaJSONString(pSchemaView))
                      .setNamespace(
                          Namespace.newBuilder()
                              .setDatabaseName(pDb)
                              .setCollectionName(pCollection)
                              .build())
                      .build())
              .build();

      executeWithTimer(Methods.SET_SCHEMA, tenantId, () -> stub.setSchema(request));

    } catch (final Exception pException) {
      final Status status = translateExceptionIntoStatus(pException);
      incrementAdminApiExceptionCounter(pException, Methods.SET_SCHEMA, status);

      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pException,
          String.format("Tenant ID: %s", pTenant.getTenantId().toString()));
    }
  }

  public NDSDataLakeSQLSchemaWithMetadataView getSchema(
      final NDSDataLakeTenant pTenant, final String pDb, final String pCollection)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");
    final String tenantId = pTenant.getTenantId().toString();

    final SchemaServiceGrpc.SchemaServiceBlockingStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceStub();

    final GetSchemasRequest request =
        GetSchemasRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
            .addNamespaces(
                Namespace.newBuilder().setDatabaseName(pDb).setCollectionName(pCollection).build())
            .build();
    final GetSchemasResponse response =
        executeWithTimer(Methods.GET_SCHEMA, tenantId, () -> stub.getSchemas(request));

    if (response.getSchemasList().isEmpty()) {
      final DataLakeAdminApiException errorToThrow =
          new DataLakeAdminApiException(
              NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
              String.format("Tenant ID: %s", pTenant.getTenantId().toString()));
      incrementAdminApiExceptionCounter(errorToThrow, Methods.GET_SCHEMA, Status.UNKNOWN);
      throw errorToThrow;
    }

    return toNDSDataLakeSQLSchemaWithMetadataView(response.getSchemas(0), true);
  }

  public NDSDataLakeSQLSchemaView generateSchemas(
      final NDSDataLakeTenant pTenant,
      final String pUsername,
      final String pDb,
      final String pCollection,
      final String pCertificateToken)
      throws SvcException {
    assertPresent(pTenant, "tenant");
    assertPresent(pUsername, "username");
    DocumentCodec dc = new DocumentCodec();

    final String tenantId = pTenant.getTenantId().toString();
    final GenerateSchemasOptions options =
        buildGenerateSchemaOptions(false, pUsername, pCertificateToken);

    final GenerateSchemasRequest request =
        GenerateSchemasRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
            .addNamespaces(
                Namespace.newBuilder().setDatabaseName(pDb).setCollectionName(pCollection).build())
            .setOptions(options)
            .build();

    final SchemaServiceGrpc.SchemaServiceBlockingStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceStub();

    final Timer timer = startDurationTimer(Methods.GENERATE_SCHEMAS);
    try {
      incrementAdminApiRequestCounter(Methods.GENERATE_SCHEMAS);
      GenerateSchemasResponse response = stub.generateSchemas(request);
      if (response.getFailedNamespacesCount() > 0) {
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_FEDERATION_GENERATE_SCHEMA_ERROR,
            response.getFailedNamespaces(0).getError());
      } else if (response.getSchemasCount() == 0) {
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
            String.format("Tenant ID: %s", pTenant.getTenantId().toString()));
      } else {
        return parseSchemaBSON(dc, response.getSchemas(0).getSchema().getData());
      }
    } catch (final Exception pException) {
      final Status status = translateExceptionIntoStatus(pException);
      incrementAdminApiExceptionCounter(pException, Methods.GENERATE_SCHEMAS, status);

      if (status.getCode() == Status.DEADLINE_EXCEEDED.getCode()) {
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_LAKE_ASYNC_OPERATION_TIMED_OUT,
            pException,
            pTenant.getTenantId(),
            pTenant.getGroupId());
      }
      if (pException instanceof SvcException castedException) {
        if (castedException.getErrorCode() == NDSErrorCode.DATA_FEDERATION_GENERATE_SCHEMA_ERROR) {
          throw pException;
        }
      }

      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pException,
          String.format("Tenant ID: %s", pTenant.getTenantId().toString()));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  private GenerateSchemasOptions buildGenerateSchemaOptions(
      final boolean pSetSchemas, final String pUsername, final String pCertificateToken) {

    final GenerateSchemasOptions.Builder options =
        GenerateSchemasOptions.newBuilder()
            .setSampleSize(DEFAULT_SCHEMA_GENERATION_OPTIONS_SAMPLE_SIZE)
            .setSetSchemas(pSetSchemas)
            .setOverwriteExisting(true)
            .setIsAuto(false)
            .setShowInternalErrors(false)
            .setMergeWithExistingSchema(true)
            .setUsername(pUsername);

    addAuthZTokenToOptionsIfPresent(pCertificateToken, options);
    return options.build();
  }

  public void generateAllSchemas(
      final NDSDataLakeTenant pTenant, final String pUsername, final String pCertificateToken)
      throws SvcException {
    assertPresent(pTenant, "tenant");
    assertPresent(pUsername, "username");

    final String tenantId = pTenant.getTenantId().toString();

    final GenerateSchemasOptions options =
        buildGenerateSchemaOptions(true, pUsername, pCertificateToken);
    final GenerateAllSchemasRequest request =
        GenerateAllSchemasRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
            .setOptions(options)
            .build();

    final SchemaServiceGrpc.SchemaServiceStub stub =
        getDataLakeGrpcClient(pTenant)
            .getSchemaServiceAsyncStub(GENERATE_ALL_SCHEMAS_DEADLINE_SECONDS);

    final Timer timer = startDurationTimer(Methods.GENERATE_ALL_SCHEMAS);
    final CountDownLatch finishLatch = new CountDownLatch(1);
    final AtomicReference<Exception> observerError = new AtomicReference<>();

    try {
      var responseObserver =
          new StreamObserver<GenerateAllSchemasResponse>() {
            @Override
            public void onNext(final GenerateAllSchemasResponse value) {
              // we do not need to track progress of this operation.
            }

            @Override
            public void onError(final Throwable pE) {
              LOG.error(
                  DataLakeLogsUtil.LOG_FORMAT,
                  LogUtils.entries(
                      DataLakeLogsUtil.FieldDefs.TEAM,
                      DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                      DataLakeLogsUtil.FieldDefs.TENANT_ID,
                      pTenant.getTenantId()),
                  "error generating all schemas & namespaces for tenant",
                  pE);
              observerError.set(
                  pE instanceof Exception
                      ? (Exception) pE
                      : new StatusRuntimeException(Status.UNKNOWN));
              finishLatch.countDown();
            }

            @Override
            public void onCompleted() {
              LOG.info(
                  DataLakeLogsUtil.LOG_FORMAT,
                  LogUtils.entries(
                      DataLakeLogsUtil.FieldDefs.TEAM,
                      DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                      DataLakeLogsUtil.FieldDefs.TENANT_ID,
                      pTenant.getTenantId()),
                  "completed generating all schemas & namespaces for tenant");
              finishLatch.countDown();
            }
          };

      try (final CancellableContext context = Context.current().withCancellation()) {
        incrementAdminApiRequestCounter(Methods.GENERATE_ALL_SCHEMAS);
        context.run(() -> stub.generateAllSchemas(request, responseObserver));

        if (finishLatch.await(GENERATE_ALL_SCHEMAS_DEADLINE_SECONDS, TimeUnit.SECONDS)) {
          if (observerError.get() != null) {
            throw observerError.get();
          } else {
            LOG.debug(
                DataLakeLogsUtil.LOG_FORMAT,
                LogUtils.entries(
                    DataLakeLogsUtil.FieldDefs.TEAM,
                    DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                    DataLakeLogsUtil.FieldDefs.TENANT_ID,
                    pTenant.getTenantId()),
                "Schemas generated successfully for tenant");
          }
        } else {
          LOG.error(
              DataLakeLogsUtil.LOG_FORMAT,
              LogUtils.entries(
                  DataLakeLogsUtil.FieldDefs.TEAM,
                  DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                  DataLakeLogsUtil.FieldDefs.TENANT_ID,
                  pTenant.getTenantId()),
              "Generate All Schemas timed out for tenant");
          // timed out, cancel RPC
          final StatusRuntimeException e = new StatusRuntimeException(Status.DEADLINE_EXCEEDED);
          context.cancel(e);
          throw e;
        }
      }
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.GENERATE_ALL_SCHEMAS, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s", pTenant.getTenantId()));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  private NDSDataLakeSQLSchemaWithMetadataView toNDSDataLakeSQLSchemaWithMetadataView(
      SchemaWithMetadata pSchemaWithMetadata, boolean pIncludeSchemaBody) {
    final NDSDataLakeSQLSchemaWithMetadataView.Builder builder =
        NDSDataLakeSQLSchemaWithMetadataView.builder();

    NDSDataLakeNamespaceView namespace =
        NDSDataLakeNamespaceView.builder()
            .db(pSchemaWithMetadata.getNamespace().getDatabaseName())
            .collection(pSchemaWithMetadata.getNamespace().getCollectionName())
            .build();
    builder.namespace(namespace);

    NDSDataLakeSQLSchemaUpdateMetadataView.Builder lastUpdateBuilder =
        NDSDataLakeSQLSchemaUpdateMetadataView.builder();

    final Date lastUpdatedTime =
        toDateHandleZeroTimestamp(pSchemaWithMetadata.getLastUpdate().getLastUpdatedTime());
    final Date lastFailureTime =
        toDateHandleZeroTimestamp(pSchemaWithMetadata.getLastUpdate().getLastUpdatedTime());

    if (lastUpdatedTime != null) {
      lastUpdateBuilder.lastUpdatedTime(lastUpdatedTime);
      lastUpdateBuilder.lastUpdatedBy(pSchemaWithMetadata.getLastUpdate().getLastUpdatedBy());
    }

    if (lastFailureTime != null) {
      lastUpdateBuilder.lastFailureTime(lastFailureTime);
      lastUpdateBuilder.lastFailureDetails(
          pSchemaWithMetadata.getLastUpdate().getLastFailureDetails());
    }

    if (lastUpdatedTime != null || lastFailureTime != null) {
      builder.lastUpdate(lastUpdateBuilder.build());
    }

    builder.source(pSchemaWithMetadata.getSource());

    if (pSchemaWithMetadata.hasSchema()) {
      // pSchemaWithMetadata.hasSchema() just means something is saved, such as an empty object.
      DocumentCodec dc = new DocumentCodec();
      final NDSDataLakeSQLSchemaView parsedSchema =
          parseSchemaBSON(dc, pSchemaWithMetadata.getSchema().getData());
      builder
          .schema(pIncludeSchemaBody ? parsedSchema : null)
          .status(
              parsedSchema.getJsonSchema().isBlank()
                  ? NDSDataLakeSQLSchemaStatus.EMPTY
                  : NDSDataLakeSQLSchemaStatus.AVAILABLE);
    } else {
      builder.status(NDSDataLakeSQLSchemaStatus.EMPTY);
    }

    return builder.build();
  }

  public NDSDataLakeSQLAllSchemasView getAllSchemas(
      final NDSDataLakeTenant pTenant,
      final String pCertificateToken,
      final boolean pIncludeSchemaBody)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");

    // Response variables to create the view
    List<NDSDataLakeSQLSchemaWithMetadataView> allSchemas =
        Collections.synchronizedList(new ArrayList<>());
    AtomicReference<Boolean> hasSchemas = new AtomicReference<>(false);

    // Build the request
    final GetAllSchemasRequest.Builder request =
        GetAllSchemasRequest.newBuilder()
            .setTenantId(
                TenantId.TenantID.newBuilder().setValue(pTenant.getTenantId().toString()).build());
    if (pCertificateToken != null && !pCertificateToken.isEmpty()) {
      request.setAuthzToken(pCertificateToken);
    }

    final SchemaServiceGrpc.SchemaServiceStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceAsyncStub(GET_ALL_SCHEMAS_DEADLINE_SECONDS);

    // Prepare async timers / handlers
    final CountDownLatch latch = new CountDownLatch(1);
    final Timer timer = startDurationTimer(Methods.GET_ALL_SCHEMAS);
    final AtomicReference<Exception> observerError = new AtomicReference<>();
    try (final CancellableContext context = Context.current().withCancellation()) {
      incrementAdminApiRequestCounter(Methods.GET_ALL_SCHEMAS);
      context.run(
          () ->
              stub.getAllSchemas(
                  request.build(),
                  new StreamObserver<>() {
                    @Override
                    public void onNext(final GetAllSchemasResponse value) {
                      allSchemas.addAll(
                          value.getSchemasList().stream()
                              .map(
                                  s -> {
                                    // Set result of hasSchema to an enum to avoid having to check
                                    // in the
                                    // client. Also raise any successful check to the top level of
                                    // the response
                                    // to determine empty states.
                                    if (s.hasSchema()) {
                                      hasSchemas.set(true);
                                    }
                                    return toNDSDataLakeSQLSchemaWithMetadataView(
                                        s, pIncludeSchemaBody);
                                  })
                              .toList());
                    }

                    @Override
                    public void onError(final Throwable pE) {
                      LOG.error(
                          DataLakeLogsUtil.LOG_FORMAT,
                          LogUtils.entries(
                              DataLakeLogsUtil.FieldDefs.TEAM,
                              DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                              DataLakeLogsUtil.FieldDefs.TENANT_ID,
                              pTenant.getTenantId()),
                          "error fetching all schemas & namespaces for tenant",
                          pE);
                      observerError.set(
                          pE instanceof Exception
                              ? (Exception) pE
                              : new StatusRuntimeException(Status.UNKNOWN));
                      latch.countDown();
                    }

                    @Override
                    public void onCompleted() {
                      LOG.info(
                          DataLakeLogsUtil.LOG_FORMAT,
                          LogUtils.entries(
                              DataLakeLogsUtil.FieldDefs.TEAM,
                              DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                              DataLakeLogsUtil.FieldDefs.TENANT_ID,
                              pTenant.getTenantId()),
                          "completed fetching all schemas & namespaces for tenant");
                      latch.countDown();
                    }
                  }));

      if (latch.await(GET_ALL_SCHEMAS_DEADLINE_SECONDS, TimeUnit.SECONDS)) {
        if (observerError.get() != null) {
          // rethrow error from observer
          throw observerError.get();
        } else {
          LOG.debug(
              DataLakeLogsUtil.LOG_FORMAT,
              LogUtils.entries(
                  DataLakeLogsUtil.FieldDefs.TEAM,
                  DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                  DataLakeLogsUtil.FieldDefs.TENANT_ID,
                  pTenant.getTenantId()),
              "Schemas fetched successfully for tenant");
        }
      } else {
        LOG.error(
            DataLakeLogsUtil.LOG_FORMAT,
            LogUtils.entries(
                DataLakeLogsUtil.FieldDefs.TEAM,
                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                DataLakeLogsUtil.FieldDefs.TENANT_ID,
                pTenant.getTenantId()),
            "Schemas fetch timed out for tenant");
        // timed out, cancel RPC
        final StatusRuntimeException e = new StatusRuntimeException(Status.DEADLINE_EXCEEDED);
        context.cancel(e);
        throw e;
      }
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.GET_ALL_SCHEMAS, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s", pTenant.getTenantId()));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
    return new NDSDataLakeSQLAllSchemasView(allSchemas, hasSchemas.get());
  }

  public void deleteAllSchemas(final NDSDataLakeTenant pTenant) throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");
    final String actionName = Methods.DELETE_ALL_SCHEMAS;

    final String tenantId = pTenant.getTenantId().toString();
    final DeleteAllSchemasRequest request =
        DeleteAllSchemasRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
            .build();

    final SchemaServiceGrpc.SchemaServiceBlockingStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceStub();
    executeWithTimer(actionName, tenantId, () -> stub.deleteAllSchemas(request));
  }

  public void deleteSchema(
      final NDSDataLakeTenant pTenant, final String pDatabaseName, final String pCollectionName)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");
    assertPresent(pDatabaseName, "database name");
    assertPresent(pCollectionName, "collection name");

    final String tenantId = pTenant.getTenantId().toString();
    final Namespace namespace =
        Namespace.newBuilder()
            .setDatabaseName(pDatabaseName)
            .setCollectionName(pCollectionName)
            .build();

    final DeleteSchemaRequest request =
        DeleteSchemaRequest.newBuilder()
            .setTenantId(TenantId.TenantID.newBuilder().setValue(tenantId).build())
            .setNamespace(namespace)
            .build();

    final SchemaServiceGrpc.SchemaServiceBlockingStub stub =
        getDataLakeGrpcClient(pTenant).getSchemaServiceStub();
    executeWithTimer(Methods.DELETE_SCHEMA, tenantId, () -> stub.deleteSchema(request));
  }

  public void writeQueryLogsToOutputStream(
      final OutputStream pOut,
      final NDSDataLakeTenant pTenant,
      final Instant pStartTimestamp,
      final Instant pEndTimestamp,
      final Models.BucketType pBucketType)
      throws DataLakeAdminApiException {
    assertPresent(pOut, "outputStream");
    assertPresent(pTenant, "tenant");
    assertPresent(pStartTimestamp, "startTimestamp");
    assertPresent(pEndTimestamp, "endTimestamp");
    assertPresent(pBucketType, "bucketType");
    final CountDownLatch latch = new CountDownLatch(1);
    final AtomicReference<Exception> observerError = new AtomicReference<>();
    final DownloadLogsRequest req =
        DownloadLogsRequest.newBuilder()
            .setTenantId(
                TenantId.TenantID.newBuilder().setValue(pTenant.getTenantId().toString()).build())
            .setStartNanoTs(
                TimeUnit.NANOSECONDS.convert(pStartTimestamp.toEpochMilli(), TimeUnit.MILLISECONDS))
            .setEndNanoTs(
                TimeUnit.NANOSECONDS.convert(pEndTimestamp.toEpochMilli(), TimeUnit.MILLISECONDS))
            .setBucketType(pBucketType)
            .build();
    final LogsServiceGrpc.LogsServiceStub stub =
        getDataLakeGrpcClient(pTenant).getLogsServiceStub(DOWNLOAD_LOGS_DEADLINE_SECONDS);
    final Timer timer = startDurationTimer(Methods.DOWNLOAD_LOGS);
    try (final CancellableContext context = Context.current().withCancellation()) {
      incrementAdminApiRequestCounter(Methods.DOWNLOAD_LOGS);
      context.run(
          () ->
              stub.downloadLogs(
                  req,
                  new StreamObserver<>() {
                    @Override
                    public void onNext(final DownloadLogsResponse value) {
                      try {
                        value.getLogs().writeTo(pOut);
                      } catch (final IOException pE) {
                        context.cancel(pE);
                        onError(pE);
                      }
                    }

                    @Override
                    public void onError(final Throwable pE) {
                      LOG.error(
                          DataLakeLogsUtil.LOG_FORMAT,
                          LogUtils.entries(
                              DataLakeLogsUtil.FieldDefs.TEAM,
                              DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                              DataLakeLogsUtil.FieldDefs.TENANT_ID,
                              pTenant.getTenantId()),
                          "Error downloading logs for tenant",
                          pE);
                      try {
                        ((HttpOutput) pOut).getHttpChannel().abort(pE);
                      } catch (final Exception pAbortError) {
                        LOG.error(
                            DataLakeLogsUtil.LOG_FORMAT,
                            LogUtils.entries(
                                DataLakeLogsUtil.FieldDefs.TEAM,
                                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                                DataLakeLogsUtil.FieldDefs.TENANT_ID,
                                pTenant.getTenantId()),
                            "Error aborting query log download HTTP connection for tenant",
                            pAbortError);
                      }
                      observerError.set(
                          pE instanceof Exception
                              ? (Exception) pE
                              : new StatusRuntimeException(Status.UNKNOWN));
                      latch.countDown();
                    }

                    @Override
                    public void onCompleted() {
                      try {
                        pOut.flush();
                      } catch (final IOException pE) {
                        LOG.error(
                            DataLakeLogsUtil.LOG_FORMAT,
                            LogUtils.entries(
                                DataLakeLogsUtil.FieldDefs.TEAM,
                                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                                DataLakeLogsUtil.FieldDefs.TENANT_ID,
                                pTenant.getTenantId()),
                            "Error flushing query log download HTTP connection for tenant",
                            pE);
                      }
                      latch.countDown();
                    }
                  }));
      if (latch.await(DOWNLOAD_LOGS_DEADLINE_SECONDS, TimeUnit.SECONDS)) {
        if (observerError.get() != null) {
          // rethrow error from observer
          throw observerError.get();
        } else {
          LOG.debug(
              DataLakeLogsUtil.LOG_FORMAT,
              LogUtils.entries(
                  DataLakeLogsUtil.FieldDefs.TEAM,
                  DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                  DataLakeLogsUtil.FieldDefs.TENANT_ID,
                  pTenant.getTenantId()),
              "Query logs downloaded successfully for tenant");
        }
      } else {
        LOG.error(
            DataLakeLogsUtil.LOG_FORMAT,
            LogUtils.entries(
                DataLakeLogsUtil.FieldDefs.TEAM,
                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                DataLakeLogsUtil.FieldDefs.TENANT_ID,
                pTenant.getTenantId()),
            "Query logs download timed out for tenant");
        // timed out, cancel RPC
        final StatusRuntimeException e = new StatusRuntimeException(Status.DEADLINE_EXCEEDED);
        context.cancel(e);
        throw e;
      }
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.DOWNLOAD_LOGS, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s", pTenant.getTenantId()));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public void deleteLogs(final NDSDataLakeTenant pTenant, final Models.BucketType pBucketType)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");
    assertPresent(pBucketType, "bucketType");

    final CountDownLatch latch = new CountDownLatch(1);

    final DeleteLogsRequest req =
        DeleteLogsRequest.newBuilder()
            .setTenantId(
                TenantId.TenantID.newBuilder().setValue(pTenant.getTenantId().toString()).build())
            .setBucketType(pBucketType)
            .build();

    final LogsServiceGrpc.LogsServiceStub stub =
        getDataLakeGrpcClient(pTenant).getLogsServiceStub(DATA_SETS_OPERATION_DEADLINE_SECONDS);

    final Timer timer = startDurationTimer(Methods.DELETE_LOGS);
    try (final CancellableContext context = Context.current().withCancellation()) {
      incrementAdminApiRequestCounter(Methods.DELETE_LOGS);
      context.run(
          () ->
              stub.deleteLogs(
                  req,
                  new StreamObserver<>() {
                    @Override
                    public void onNext(DeleteLogsResponse value) {
                      // do nothing, await
                    }

                    @Override
                    public void onError(Throwable t) {
                      // we don't do anything, so can't fail
                    }

                    @Override
                    public void onCompleted() {
                      latch.countDown();
                    }
                  }));

      if (latch.await(DATA_SETS_OPERATION_DEADLINE_SECONDS, TimeUnit.SECONDS)) {
        LOG.debug(
            DataLakeLogsUtil.LOG_FORMAT,
            LogUtils.entries(
                DataLakeLogsUtil.FieldDefs.TEAM,
                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                DataLakeLogsUtil.FieldDefs.TENANT_ID,
                pTenant.getTenantId()),
            String.format("Tenant with logs of type %s successfully deleted", pBucketType));
      } else {
        LOG.error(
            DataLakeLogsUtil.LOG_FORMAT,
            LogUtils.entries(
                DataLakeLogsUtil.FieldDefs.TEAM,
                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                DataLakeLogsUtil.FieldDefs.TENANT_ID,
                pTenant.getTenantId()),
            String.format("Tenant with logs of type %s timed out when deleting", pBucketType));
        // timed out, cancel RPC
        final StatusRuntimeException e = new StatusRuntimeException(Status.DEADLINE_EXCEEDED);
        context.cancel(e);
        throw e;
      }
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.DELETE_LOGS, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s", pTenant.getTenantId()));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public DataSetMetricsResult getDataSetMetrics(
      final ObjectId pArchiveId,
      final String pProvider,
      final String pRegion,
      final ObjectId pProjectId,
      final String pDatasetName,
      final String pDateField)
      throws DataLakeAdminApiException {

    assertPresent(pArchiveId, "archiveId");
    assertPresent(pProvider, "provider");
    assertPresent(pRegion, "region");
    assertPresent(pProjectId, "projectId");
    assertPresent(pDatasetName, "datasetName");

    final Builder request =
        GetDataSetMetricsRequest.newBuilder()
            .setProvider(pProvider)
            .setRegion(pRegion)
            .setProjectId(pProjectId.toHexString())
            .setDataSetName(pDatasetName);
    if (pDateField != null) {
      request.addFields(pDateField);
    }

    try {
      final GetDataSetMetricsResponse response =
          getDataLakeGrpcClient(pProvider)
              .getMetricsServiceStub()
              .getDataSetMetrics(request.build());
      final boolean cacheMiss =
          response.getLastUpdatedAt().getNanos() == 0
              && response.getLastUpdatedAt().getSeconds() == 0;
      Object minDateField = null, maxDateField = null;
      if (!response.getFieldsList().isEmpty() && !response.getFields(0).getAttributes().isEmpty()) {
        final BSONObject minMax =
            new BasicBSONDecoder().readObject(response.getFields(0).getAttributes().toByteArray());
        minDateField = minMax.get("min");
        maxDateField = minMax.get("max");
      }

      return new DataSetMetricsResult(
          response.getTotalUncompressedSizeBytes(),
          response.getTotalDocumentCount(),
          minDateField,
          maxDateField,
          response.getIsUpdateInProgress(),
          cacheMiss);
    } catch (final Exception pException) {
      final Status status = translateExceptionIntoStatus(pException);
      incrementAdminApiExceptionCounter(pException, Methods.GET_DATA_SET_METRICS, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pException,
          String.format("ArchiveId ID: %s", pArchiveId));
    }
  }

  public NDSDataLakeMetricsView getMetrics(
      final NDSDataLakeTenant pTenant, final Date pStartDate, final Date pEndDate)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");
    assertPresent(pStartDate, "startDate");
    assertPresent(pEndDate, "endDate");
    final CalculateTenantMetricsRequest request =
        CalculateTenantMetricsRequest.newBuilder()
            .setTenantId(
                TenantId.TenantID.newBuilder()
                    .setValue(pTenant.getTenantId().toHexString())
                    .build())
            .setStartTime(toTimestamp(pStartDate))
            .setEndTime(toTimestamp(pEndDate))
            .build();
    try {
      final CalculateTenantMetricsResponse response =
          getDataLakeGrpcClient(pTenant).getMetricsV1ServiceStub().calculateTenantMetrics(request);

      return NDSDataLakeMetricsView.builder()
          .avgExecutionTime(Long.valueOf(response.getAvgQueryExecutionTimeMillis()).doubleValue())
          .totalDataReturned(response.getTotalDataReturnedBytes())
          .totalDataScanned(response.getTotalDataScannedBytes())
          .totalFailedQueries(response.getTotalFailedQueries())
          .totalSuccessfulQueries(response.getTotalSuccessfulQueries())
          .build();
    } catch (final Exception pException) {
      final Status status = translateExceptionIntoStatus(pException);
      if (status.getCode() != Status.DEADLINE_EXCEEDED.getCode()) {
        incrementAdminApiExceptionCounter(pException, Methods.GET_METRICS, status);
      }
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pException,
          String.format("Tenant ID: %s", pTenant.getTenantId()));
    }
  }

  public void setUsageLimit(final String pProvider, final UsageLimit pUsageLimit)
      throws DataLakeAdminApiException {
    final SetUsageLimitRequest request =
        SetUsageLimitRequest.newBuilder().setUsageLimit(pUsageLimit).build();

    final UsageLimitsServiceBlockingStub stub =
        getDataLakeGrpcClient(getDLSAcceptedCloudProvider(pProvider)).getUsageLimitsServiceStub();
    final Timer timer = startDurationTimer(Methods.SET_USAGE_LIMIT);
    try {
      incrementAdminApiRequestCounter(Methods.SET_USAGE_LIMIT);
      stub.setUsageLimit(request);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.SET_USAGE_LIMIT, status);
      if (status.getCode() == Status.INVALID_ARGUMENT.getCode()) {
        throw new DataLakeAdminApiException(
            NDSErrorCode.INVALID_ARGUMENT, pE, String.format("Usage Limit: %s", pUsageLimit));
      }
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Usage Limit: %s", pUsageLimit));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public void deleteUsageLimit(final String pProvider, final UsageLimit pUsageLimit)
      throws DataLakeAdminApiException {
    final DeleteUsageLimitRequest request =
        DeleteUsageLimitRequest.newBuilder().setUsageLimit(pUsageLimit).build();

    final UsageLimitsServiceBlockingStub stub =
        getDataLakeGrpcClient(getDLSAcceptedCloudProvider(pProvider)).getUsageLimitsServiceStub();
    final Timer timer = startDurationTimer(Methods.DELETE_USAGE_LIMIT);
    try {
      incrementAdminApiRequestCounter(Methods.DELETE_USAGE_LIMIT);
      stub.deleteUsageLimit(request);
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      if (status.getCode() == Status.NOT_FOUND.getCode()) {
        // ignore, already deleted
        return;
      }
      incrementAdminApiExceptionCounter(pE, Methods.DELETE_USAGE_LIMIT, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Usage Limit: %s", pUsageLimit));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  private Timestamp toTimestamp(final Date pDate) {
    final Instant instant = pDate.toInstant();
    return Timestamp.newBuilder()
        .setSeconds(instant.getEpochSecond())
        .setNanos(instant.getNano())
        .build();
  }

  private Status translateExceptionIntoStatus(final Exception pE) {
    return pE instanceof StatusRuntimeException
        ? ((StatusRuntimeException) pE).getStatus()
        : Status.UNKNOWN;
  }

  public List<DataScanningLimitStatus> getUsageLimits(
      final String pCloudProvider, final ObjectId pGroupId) throws DataLakeAdminApiException {
    assertPresent(pGroupId, "groupId");
    final GetUsageLimitsRequest req =
        GetUsageLimitsRequest.newBuilder()
            .setProjectId(ProjectID.newBuilder().setValue(pGroupId.toHexString()).build())
            .build();
    final GetUsageLimitsResponse resp = getUsageLimitsInternal(pCloudProvider, req);
    return Optional.ofNullable(resp)
        .map(GetUsageLimitsResponse::getDataScanningLimitStatusesList)
        .orElse(List.of());
  }

  public List<DataScanningLimitStatus> getTenantUsageLimits(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");
    final GetUsageLimitsRequest req =
        GetUsageLimitsRequest.newBuilder()
            .setProjectId(ProjectID.newBuilder().setValue(pTenant.getGroupId().toHexString()))
            .setTenantId(
                TenantId.TenantID.newBuilder()
                    .setValue(pTenant.getTenantId().toHexString())
                    .build())
            .build();
    final GetUsageLimitsResponse resp =
        getUsageLimitsInternal(getDLSAcceptedCloudProvider(pTenant), req);
    return Optional.ofNullable(resp)
        .map(GetUsageLimitsResponse::getDataScanningLimitStatusesList)
        .orElse(List.of());
  }

  private GetUsageLimitsResponse getUsageLimitsInternal(
      final String pProvider, final GetUsageLimitsRequest pGetUsageLimitsRequest)
      throws DataLakeAdminApiException {
    final String groupId = pGetUsageLimitsRequest.getProjectId().getValue();
    final UsageLimitsServiceBlockingStub stub =
        getDataLakeGrpcClient(getDLSAcceptedCloudProvider(pProvider)).getUsageLimitsServiceStub();
    final Timer timer = startDurationTimer(Methods.GET_USAGE_LIMITS);

    try {
      incrementAdminApiRequestCounter(Methods.GET_USAGE_LIMITS);
      return stub.getUsageLimits(pGetUsageLimitsRequest);
    } catch (final StatusRuntimeException pE) {
      final Status status = pE.getStatus();

      // Equality on Statuses is not well-defined. Instead, doing comparison based on their Code
      if (status.getCode() == Status.NOT_FOUND.getCode()) {
        return null;
      }
      incrementAdminApiExceptionCounter(pE, Methods.GET_USAGE_LIMITS, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE, String.format("Group ID: %s", groupId));
    } catch (final Exception pE) {
      incrementAdminApiExceptionCounter(pE, Methods.GET_USAGE_LIMITS, Status.UNKNOWN);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE, String.format("Group ID: %s", groupId));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public List<Region> getSupportedRegions(final String pProvider) throws DataLakeAdminApiException {
    final Timer timer = startDurationTimer(Methods.GET_REGIONS);
    try {
      incrementAdminApiRequestCounter(Methods.GET_REGIONS);
      return getDataLakeGrpcClient(getDLSAcceptedCloudProvider(pProvider))
          .getRegionsServiceStub()
          .getRegions(GetRegionsRequest.newBuilder().build())
          .getRegionsList();
    } catch (final Exception pE) {
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public List<Region> getSupportedRegions(final CloudProvider pProvider)
      throws DataLakeAdminApiException {
    final Timer timer = startDurationTimer(Methods.GET_REGIONS);
    try {
      incrementAdminApiRequestCounter(Methods.GET_REGIONS);
      return _dataLakeGrpcClients
          .get(pProvider)
          .getRegionsServiceStub()
          .getRegions(GetRegionsRequest.newBuilder().build())
          .getRegionsList();
    } catch (final Exception pE) {
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public String acceptPrivateEndpoint(final String pEndpointId, final String pRegion)
      throws DataLakeAdminApiException {
    final Timer timer = startDurationTimer(Methods.ACCEPT_PRIVATE_ENDPOINT);
    try {
      incrementAdminApiRequestCounter(Methods.ACCEPT_PRIVATE_ENDPOINT);
      return _dataLakeGrpcClients
          .get(CloudProvider.AZURE)
          .getPrivateLinkServiceStub(ACCEPT_PRIVATE_ENDPOINT_DEADLINE_SECONDS)
          .acceptPrivateEndpoint(
              AcceptPrivateEndpointRequest.newBuilder()
                  .setEndpointId(pEndpointId)
                  .setRegion(pRegion)
                  .build())
          .getLinkIdentifier();
    } catch (final Exception pE) {
      incrementAdminApiExceptionCounter(pE, Methods.ACCEPT_PRIVATE_ENDPOINT, Status.UNKNOWN);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public void deletePrivateEndpoint(final String pEndpointId, final String pRegion)
      throws DataLakeAdminApiException {
    final Timer timer = startDurationTimer(Methods.DELETE_PRIVATE_ENDPOINT);
    try {
      incrementAdminApiRequestCounter(Methods.DELETE_PRIVATE_ENDPOINT);
      _dataLakeGrpcClients
          .get(CloudProvider.AZURE)
          .getPrivateLinkServiceStub(DELETE_PRIVATE_ENDPOINT_DEADLINE_SECONDS)
          .deletePrivateEndpoint(
              DeletePrivateEndpointRequest.newBuilder()
                  .setEndpointId(pEndpointId)
                  .setRegion(pRegion)
                  .build());
    } catch (final Exception pE) {
      incrementAdminApiExceptionCounter(pE, Methods.DELETE_PRIVATE_ENDPOINT, Status.UNKNOWN);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  public NDSDataLakeAzurePrivateLinkServiceNames getAzurePrivateLinkServiceNames()
      throws DataLakeAdminApiException {
    final Timer timer = startDurationTimer(Methods.GET_SERVICE_NAMES);
    try {
      incrementAdminApiRequestCounter(Methods.GET_SERVICE_NAMES);
      final GetServiceNamesResponse resp =
          _dataLakeGrpcClients
              .get(CloudProvider.AZURE)
              .getPrivateLinkServiceStub(GET_SERVICE_NAMES_DEADLINE_SECONDS)
              .getServiceNames(GetServiceNamesRequest.newBuilder().build());
      final Map<String, String> regionServiceNames = new LinkedHashMap<>();
      for (final RegionServiceName r : resp.getRegionServiceNamesList()) {
        regionServiceNames.put(r.getRegion(), r.getServiceName());
      }
      return new NDSDataLakeAzurePrivateLinkServiceNames(
          resp.getSubscriptionId(), resp.getResourceGroupName(), regionServiceNames);
    } catch (final Exception pE) {
      incrementAdminApiExceptionCounter(pE, Methods.GET_SERVICE_NAMES, Status.UNKNOWN);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  private <T> T executeWithTimer(String actionName, String tenantId, Supplier<T> s)
      throws DataLakeAdminApiException {

    final Timer timer = startDurationTimer(actionName);
    try {
      incrementAdminApiRequestCounter(actionName);
      return s.get();
    } catch (final StatusRuntimeException pE) {
      final Status status = pE.getStatus();
      incrementAdminApiExceptionCounter(pE, actionName, status);

      // Equality on Statuses is not well-defined. Instead, doing comparison based on their Code
      if (status.getCode() == Status.NOT_FOUND.getCode()) {
        throw new DataLakeAdminApiException(
            NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID,
            pE,
            String.format("Tenant ID: %s", tenantId));
      }

      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE, String.format("Tenant ID: %s", tenantId));
    } catch (final Exception pE) {
      incrementAdminApiExceptionCounter(pE, actionName, Status.UNKNOWN);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE, String.format("Tenant ID: %s", tenantId));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  private void addAuthZTokenToOptionsIfPresent(
      final String pCertificateToken, final GenerateSchemasOptions.Builder options) {
    if (pCertificateToken != null && !pCertificateToken.isEmpty()) {
      options.setAuthzToken(pCertificateToken);
    }
  }

  @FunctionalInterface
  private interface DataSetProcessor {
    DataLakeAdminApiOperationOutcome apply(final List<? extends NDSDataSetDLS> pNDSDataSetDLS)
        throws DataLakeAdminApiException;
  }

  public List<NDSDataLakeDataSetView> listDataSets(final String projectId)
      throws DataLakeAdminApiException {
    if (projectId == null || projectId.isEmpty()) {
      throw new DataLakeAdminApiException(
          NDSErrorCode.INVALID_ARGUMENT, "Project ID must not be null or empty");
    }

    final ObjectId groupId = DbUtils.parseObjectId(projectId);
    final Group group = _groupSvc.findById(groupId);
    if (group == null) {
      throw new DataLakeAdminApiException(
          NDSErrorCode.INVALID_ARGUMENT,
          String.format("Project with ID '%s' does not exist", projectId));
    }

    List<NDSDataLakeDataSetView> dataSetViews = new ArrayList<>();
    for (CloudProvider provider : CloudProvider.DEDICATED_CLOUD_PROVIDERS) {
      final StorageServiceGrpc.StorageServiceBlockingStub stub =
          getDataLakeGrpcClient(provider.toString()).getStorageServiceStub();

      // Since Data Federation doesn't attach enabled state on the returned dataset info,
      // we have to set it ourselves. We first make a request with enabled datasets only,
      // we then make a request for all datasets and set the enabled field depending on
      // whether the dataset was found in the enabled datasets list.

      // Request for getting enabled datasets only.
      final ListDataSetsRequest.Builder enabledBuilder = ListDataSetsRequest.newBuilder();
      enabledBuilder.setProjectId(projectId);
      enabledBuilder.setEnabledOnly(true);

      final Timer enabledTimer = startDurationTimer(Methods.LIST_DATA_SETS);
      final Timer enabledTimerV3 =
          startDurationTimerV3(Methods.LIST_DATA_SETS, provider.toString(), REGION_UNKNOWN);

      Set<String> enabledDataSetNames = new HashSet<>();
      try {
        final ListDataSetsResponse enabledResp = stub.listDataSets(enabledBuilder.build());
        for (ListDataSetsResponse.DataSet dataSet : enabledResp.getDataSetsList()) {
          enabledDataSetNames.add(dataSet.getDataSetName());
        }
      } catch (final Exception pE) {
        incrementAdminApiExceptionCounter(pE, Methods.LIST_DATA_SETS, Status.UNKNOWN);
        incrementAdminApiExceptionCounterV3(
            pE, Methods.LIST_DATA_SETS, Status.UNKNOWN, provider.toString(), REGION_UNKNOWN);
        throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
      } finally {
        NDSPromMetricsSvc.recordTimer(enabledTimer);
        NDSPromMetricsSvc.recordTimer(enabledTimerV3);
      }

      // Request for getting all datasets.
      final ListDataSetsRequest.Builder allBuilder = ListDataSetsRequest.newBuilder();
      allBuilder.setProjectId(projectId);
      allBuilder.setEnabledOnly(false);

      final Timer allTimer = startDurationTimer(Methods.LIST_DATA_SETS);
      final Timer allTimerV3 =
          startDurationTimerV3(Methods.LIST_DATA_SETS, provider.toString(), REGION_UNKNOWN);

      final ListDataSetsResponse allResp;
      try {
        allResp = stub.listDataSets(allBuilder.build());
      } catch (final Exception pE) {
        incrementAdminApiExceptionCounter(pE, Methods.LIST_DATA_SETS, Status.UNKNOWN);
        incrementAdminApiExceptionCounterV3(
            pE, Methods.LIST_DATA_SETS, Status.UNKNOWN, provider.toString(), REGION_UNKNOWN);
        throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
      } finally {
        NDSPromMetricsSvc.recordTimer(allTimer);
        NDSPromMetricsSvc.recordTimer(allTimerV3);
      }

      List<ListDataSetsResponse.DataSet> dataSets = allResp.getDataSetsList();
      for (ListDataSetsResponse.DataSet dataSet : dataSets) {
        boolean isEnabled = enabledDataSetNames.contains(dataSet.getDataSetName());
        dataSetViews.add(toDataSetView(dataSet, isEnabled));
      }
    }

    return dataSetViews;
  }

  public List<NDSDataLakeAndonCordView> listAndonCords(
      final String pRegion, final String pName, final String pState)
      throws DataLakeAdminApiException, IllegalArgumentException {
    List<NDSDataLakeAndonCordView> andonCordViews = new ArrayList<>();
    for (CloudProvider provider : CloudProvider.DEDICATED_CLOUD_PROVIDERS) {
      final com.xgen.mhouse.services.andoncord.v1.Models.ListResponse resp;
      final AndonCordServiceGrpc.AndonCordServiceBlockingStub stub =
          getDataLakeGrpcClient(provider.toString()).getAndonCordServiceStub();
      final com.xgen.mhouse.services.andoncord.v1.Models.ListRequest.Builder builder =
          com.xgen.mhouse.services.andoncord.v1.Models.ListRequest.newBuilder();
      if (pRegion != null) {
        builder.setRegion(pRegion);
      }
      if (pName != null) {
        builder.setName(pName);
      }
      if (pState != null) {
        builder.setState(toAndonCordState(pState));
      }

      final Timer timer = startDurationTimer(Methods.LIST_ANDON_CORDS);
      final Timer timerV3 =
          startDurationTimerV3(Methods.LIST_ANDON_CORDS, provider.toString(), REGION_UNKNOWN);
      try {
        resp = stub.list(builder.build());
      } catch (final Exception pE) {
        incrementAdminApiExceptionCounter(pE, Methods.LIST_ANDON_CORDS, Status.UNKNOWN);
        incrementAdminApiExceptionCounterV3(
            pE, Methods.LIST_ANDON_CORDS, Status.UNKNOWN, provider.toString(), REGION_UNKNOWN);
        throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
      } finally {
        NDSPromMetricsSvc.recordTimer(timer);
        NDSPromMetricsSvc.recordTimer(timerV3);
      }

      List<AndonCord> andonCords = resp.getAndonCordsList();
      for (AndonCord andonCord : andonCords) {
        andonCordViews.add(toAndonCordView(andonCord));
      }
    }

    return andonCordViews;
  }

  private AndonCordState toAndonCordState(final String pState) throws IllegalArgumentException {
    return switch (pState) {
      case "" -> AndonCordState.ANDON_CORD_STATE_ALL;
      case "unspecified" -> AndonCordState.ANDON_CORD_STATE_UNSPECIFIED;
      case "enabled" -> AndonCordState.ANDON_CORD_STATE_ENABLED;
      case "disabled" -> AndonCordState.ANDON_CORD_STATE_DISABLED;
      case "unavailable" -> AndonCordState.ANDON_CORD_STATE_UNAVAILABLE;
      case "doesNotExist" -> AndonCordState.ANDON_CORD_STATE_DOES_NOT_EXIST;
      default -> throw new IllegalArgumentException("unknown andon cord state: " + pState);
    };
  }

  private String fromAndonCordState(final AndonCordState pState) throws DataLakeAdminApiException {
    return switch (pState) {
      case ANDON_CORD_STATE_UNSPECIFIED -> "unspecified";
      case ANDON_CORD_STATE_ENABLED -> "enabled";
      case ANDON_CORD_STATE_DISABLED -> "disabled";
      case ANDON_CORD_STATE_UNAVAILABLE -> "unavailable";
      case ANDON_CORD_STATE_DOES_NOT_EXIST -> "does_not_exist";
        // A single andon cord should not be able to have a state of "all" or
        // "unrecognized".
      default ->
          throw new DataLakeAdminApiException(
              NDSErrorCode.INVALID_ARGUMENT, String.format("Andon Cord State: %s", pState));
    };
  }

  private NDSDataLakeAndonCordView toAndonCordView(final AndonCord pAndonCord)
      throws DataLakeAdminApiException {

    AndonCordValue andonCordValue = pAndonCord.getValue();
    NDSDataLakeAndonCordValueViewBuilder valueViewBuilder =
        new NDSDataLakeAndonCordValueViewBuilder().version(andonCordValue.getVersion());
    if (andonCordValue.hasJsonDefaultValue()) {
      JSONObject defaultValueObj = new JSONObject(andonCordValue.getJsonDefaultValue());
      valueViewBuilder.defaultValue(defaultValueObj.get(ANDON_CORD_DEFAULT_VALUE_KEY));
    }

    List<NDSDataLakeAndonCordValueDataSetView> dataSetViews = new ArrayList<>();
    for (DataSet ds : andonCordValue.getDataSetsList()) {
      JSONObject dataSetValueObj = new JSONObject(ds.getJsonValue());
      Object dataSetValue = dataSetValueObj.get(ANDON_CORD_DATA_SET_VALUE_KEY);
      NDSDataLakeAndonCordValueDataSetView dataSetValueView =
          NDSDataLakeAndonCordValueDataSetView.builder()
              .projectId(new ObjectId(ds.getProjectId().getValue()))
              .dataSetName(ds.getName())
              .dataSetValue(dataSetValue)
              .build();
      dataSetViews.add(dataSetValueView);
    }
    valueViewBuilder.dataSets(dataSetViews);

    return NDSDataLakeAndonCordView.builder()
        .name(pAndonCord.getName())
        .state(fromAndonCordState(pAndonCord.getState()))
        .modifiedAt(toDate(pAndonCord.getModifiedAt()))
        .region(pAndonCord.getRegion())
        .value(valueViewBuilder.build())
        .build();
  }

  public void createOrUpdateAndonCord(
      final NDSDataLakeCreateOrUpdateAndonCordView andonCord, final boolean override)
      throws DataLakeAdminApiException {
    // Regardless of the region, we make the same gRPC request to each provider.
    // If the region isn't configured for a specific provider, the backend operation will
    // be a no-op.
    for (CloudProvider provider : CloudProvider.DEDICATED_CLOUD_PROVIDERS) {
      final AndonCordServiceGrpc.AndonCordServiceBlockingStub stub =
          getDataLakeGrpcClient(provider.toString()).getAndonCordServiceStub();
      final CreateOrUpdateRequest request = makeCreateOrUpdateRequest(andonCord, override);

      final Timer timer = startDurationTimer(Methods.CREATE_OR_UPDATE_ANDON_CORD);
      final Timer timerV3 =
          startDurationTimerV3(
              Methods.CREATE_OR_UPDATE_ANDON_CORD, provider.toString(), REGION_UNKNOWN);
      try {
        stub.createOrUpdate(request);
      } catch (final Exception pE) {
        incrementAdminApiExceptionCounter(pE, Methods.CREATE_OR_UPDATE_ANDON_CORD, Status.UNKNOWN);
        incrementAdminApiExceptionCounterV3(
            pE,
            Methods.CREATE_OR_UPDATE_ANDON_CORD,
            Status.UNKNOWN,
            provider.toString(),
            REGION_UNKNOWN);
        throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
      } finally {
        NDSPromMetricsSvc.recordTimer(timer);
        NDSPromMetricsSvc.recordTimer(timerV3);
      }
    }
  }

  @VisibleForTesting
  protected CreateOrUpdateRequest makeCreateOrUpdateRequest(
      final NDSDataLakeCreateOrUpdateAndonCordView andonCord, final boolean override)
      throws DataLakeAdminApiException, IllegalArgumentException {
    final CreateOrUpdateRequest.Builder builder = CreateOrUpdateRequest.newBuilder();
    if (andonCord.getRegion() != null) {
      builder.setRegion(andonCord.getRegion());
    }
    if (andonCord.getName() == null) {
      throw new IllegalArgumentException("Andon cord name cannot be null");
    }
    if (andonCord.getState() == null) {
      throw new IllegalArgumentException("Andon cord state cannot be null");
    }
    builder.setName(andonCord.getName());
    builder.setState(toAndonCordState(andonCord.getState()));
    builder.setOverride(override);

    final NDSDataLakeCreateOrUpdateAndonCordValueView value = andonCord.getValue();
    if (value.getDataSets() != null) {
      for (NDSDataLakeCreateOrUpdateAndonCordValueDataSetView pDataSetView : value.getDataSets()) {
        boolean isValid = ObjectId.isValid(pDataSetView.getProjectId());
        if (!isValid) {
          throw new DataLakeAdminApiException(
              NDSErrorCode.INVALID_ARGUMENT,
              String.format("Data set project ID was malformed: %s", pDataSetView.getProjectId()));
        }

        DataSet.Builder dataSetBuilder =
            DataSet.newBuilder()
                .setProjectId(ProjectID.newBuilder().setValue(pDataSetView.getProjectId()))
                .setName(pDataSetView.getDataSetName());

        if (pDataSetView.getDataSetValue() != null) {
          JSONObject dataSetValueObj = new JSONObject();
          dataSetValueObj.put(ANDON_CORD_DATA_SET_VALUE_KEY, pDataSetView.getDataSetValue());
          dataSetBuilder.setJsonValue(dataSetValueObj.toString());
        }
        builder.addDataSets(dataSetBuilder.build());
      }
    }

    if (value.getDefaultValue() != null) {
      JSONObject defaultValueObj = new JSONObject();
      defaultValueObj.put(ANDON_CORD_DEFAULT_VALUE_KEY, value.getDefaultValue());
      builder.setDefaultValue(defaultValueObj.toString());
    }

    return builder.build();
  }

  private NDSDataLakeDataSetView toDataSetView(
      final ListDataSetsResponse.DataSet pDataSet, final boolean enabled) {
    return new NDSDataLakeDataSetView(
        pDataSet.getDataSetLocation().getProvider(),
        pDataSet.getDataSetLocation().getRegion(),
        pDataSet.getDataSetName(),
        pDataSet.hasCreatedAt() ? toDate(pDataSet.getCreatedAt()) : null,
        pDataSet.hasDeletedAt() ? toDate(pDataSet.getDeletedAt()) : null,
        new ObjectId(pDataSet.getProjectId()),
        enabled);
  }

  public NDSDataLakeTenantSettingsView getTenantSettings(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    final String cloudProvider = getDLSAcceptedCloudProvider(pTenant);
    final TenantSettingsServiceBlockingStub stub =
        getDataLakeGrpcClient(cloudProvider).getTenantSettingsServiceStub();

    GetSettingsRequest.Builder builder = GetSettingsRequest.newBuilder();
    TenantID tenantID = TenantID.newBuilder().setValue(pTenant.getTenantId().toHexString()).build();
    builder.setTenantId(tenantID);

    GetSettingsResponse resp;
    final Timer timer = startDurationTimer(Methods.GET_TENANT_SETTINGS);
    final Timer timerV3 =
        startDurationTimerV3(Methods.GET_TENANT_SETTINGS, cloudProvider, REGION_UNKNOWN);
    try {
      resp = stub.getSettings(builder.build());
    } catch (final Exception pE) {
      incrementAdminApiExceptionCounter(pE, Methods.GET_TENANT_SETTINGS, Status.UNKNOWN);
      incrementAdminApiExceptionCounterV3(
          pE, Methods.GET_TENANT_SETTINGS, Status.UNKNOWN, cloudProvider, REGION_UNKNOWN);
      throw new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE);
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
      NDSPromMetricsSvc.recordTimer(timerV3);
    }

    Settings settings = resp.getSettings();
    NDSDataLakeTenantSettingsViewBuilder settingsBuilder = NDSDataLakeTenantSettingsView.builder();
    settingsBuilder
        .modifiedAt(toISOString(resp.getLastModifiedAt()))
        .maxConcurrentQueries(settings.getMaxConcurrentQueries())
        .maxConnections(settings.getMaxConnections())
        .allowUnlimitedConcurrentQueries(settings.getAllowUnlimitedConcurrentQueries())
        .parquetWriterVersion(settings.getParquetWriterVersion())
        .tracingEnabled(settings.getTracingEnabled())
        .cursorMaxFileSize(settings.getCursorMaxFileSize())
        .cursorMaxFiles(settings.getCursorMaxFiles())
        .cursorMaxWaitTimeForAvailableSpace(settings.getCursorMaxWaitTimeForAvailableSpace())
        .queryExecutionBranchingDepth(settings.getQueryExecutionBranchingDepth())
        .queryExecutionBranchingFactor(settings.getQueryExecutionBranchingFactor())
        .queryExecutionMaxConcurrency(settings.getQueryExecutionMaxConcurrency())
        .queryExecutionMaxSerialNum(settings.getQueryExecutionMaxSerialNum())
        .queryExecutionMaxSerialSize(settings.getQueryExecutionMaxSerialSize())
        .queryRoutingStrategy(settings.getQueryRoutingStrategy());
    return settingsBuilder.build();
  }

  public void setTenantSettings(
      final NDSDataLakeTenant pTenant, final NDSDataLakeTenantSettingsView pSettings)
      throws DataLakeAdminApiException {
    assertPresent(pTenant, "tenant");
    assertPresent(pSettings, "settings");

    final String cloudProvider = getDLSAcceptedCloudProvider(pTenant);
    final TenantSettingsServiceBlockingStub stub =
        getDataLakeGrpcClient(cloudProvider).getTenantSettingsServiceStub();

    final Timer timer = startDurationTimer(Methods.SET_TENANT_SETTINGS);
    try {
      incrementAdminApiRequestCounter(Methods.SET_TENANT_SETTINGS);
      stub.setSettings(makeSetSettingsRequest(pTenant, pSettings));
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.SET_TENANT_SETTINGS, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s", pTenant.getTenantId()));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }
  }

  @VisibleForTesting
  protected SetSettingsRequest makeSetSettingsRequest(
      final NDSDataLakeTenant pTenant, final NDSDataLakeTenantSettingsView pSettings)
      throws DataLakeAdminApiException {
    assertPresent(pSettings.getModifiedAt(), "settings.modifiedAt");
    final SetSettingsRequest.Builder builder = SetSettingsRequest.newBuilder();
    builder.setTenantId(
        TenantID.newBuilder().setValue(pTenant.getTenantId().toHexString()).build());
    builder.setLastModifiedAt(fromISOString(pSettings.getModifiedAt()));
    final Settings.Builder settings =
        Settings.newBuilder()
            .setMaxConcurrentQueries(pSettings.getMaxConcurrentQueries())
            .setMaxConnections(pSettings.getMaxConnections())
            .setAllowUnlimitedConcurrentQueries(pSettings.getAllowUnlimitedConcurrentQueries())
            .setParquetWriterVersion(pSettings.getParquetWriterVersion())
            .setTracingEnabled(pSettings.getTracingEnabled())
            .setCursorMaxFileSize(pSettings.getCursorMaxFileSize())
            .setCursorMaxFiles(pSettings.getCursorMaxFiles())
            .setCursorMaxWaitTimeForAvailableSpace(
                pSettings.getCursorMaxWaitTimeForAvailableSpace())
            .setQueryExecutionBranchingDepth(pSettings.getQueryExecutionBranchingDepth())
            .setQueryExecutionBranchingFactor(pSettings.getQueryExecutionBranchingFactor())
            .setQueryExecutionMaxConcurrency(pSettings.getQueryExecutionMaxConcurrency())
            .setQueryExecutionMaxSerialNum(pSettings.getQueryExecutionMaxSerialNum())
            .setQueryExecutionMaxSerialSize(pSettings.getQueryExecutionMaxSerialSize());
    if (pSettings.getQueryRoutingStrategy() != null) {
      settings.setQueryRoutingStrategy(pSettings.getQueryRoutingStrategy());
    }
    builder.setSettings(settings.build());

    return builder.build();
  }

  // toISOString is used to convert the Timestamp to a string in ISO format while preserving
  // millisecond precision, e.g. "2025-07-12T01:46:35.733Z".
  @VisibleForTesting
  protected String toISOString(Timestamp timestamp) {
    final Date date = toDate(timestamp);
    return DateTimeFormatter.ISO_INSTANT.format(date.toInstant());
  }

  // fromISOString is used to convert a string in ISO format back to a Timestamp.
  protected Timestamp fromISOString(String isoString) {
    Instant instant = Instant.parse(isoString);
    return Timestamp.newBuilder()
        .setSeconds(instant.getEpochSecond())
        .setNanos(instant.getNano())
        .build();
  }

  public List<NDSDataLakeCurrentOpView> listCurrentOps(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    if (pTenant == null) {
      throw new DataLakeAdminApiException(NDSErrorCode.INVALID_ARGUMENT, "Tenant cannot be null");
    }

    final String cloudProvider = getDLSAcceptedCloudProvider(pTenant);
    final OpServiceBlockingStub stub = getDataLakeGrpcClient(cloudProvider).getOpServiceStub();

    Iterator<com.xgen.mhouse.services.op.v1.Models.ListResponse> itr;
    com.xgen.mhouse.services.op.v1.Models.ListRequest request =
        com.xgen.mhouse.services.op.v1.Models.ListRequest.newBuilder()
            .setTenantId(
                TenantId.TenantID.newBuilder()
                    .setValue(pTenant.getTenantId().toHexString())
                    .build())
            .build();

    List<NDSDataLakeCurrentOpView> currentOps = new ArrayList<>();
    final Timer timer = startDurationTimer(Methods.LIST_CURRENT_OPS);
    try {
      incrementAdminApiRequestCounter(Methods.LIST_CURRENT_OPS);
      itr = stub.list(request);
      while (itr.hasNext()) {
        com.xgen.mhouse.services.op.v1.Models.ListResponse response = itr.next();
        for (CurrentOp op : response.getOpsList()) {
          NDSDataLakeCurrentOpView view = toNDSDataLakeCurrentOpView(op);
          currentOps.add(view);
        }

        if (!response.getComplete() && !itr.hasNext()) {
          throw new DataLakeAdminApiException(
              NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
              String.format(
                  "Received 'complete' flag as false from Data Lake but iterator has no more"
                      + " elements. Tenant ID: %s",
                  pTenant.getTenantId()));
        }
      }
    } catch (final Exception pE) {
      final Status status = translateExceptionIntoStatus(pE);
      incrementAdminApiExceptionCounter(pE, Methods.LIST_CURRENT_OPS, status);
      throw new DataLakeAdminApiException(
          NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
          pE,
          String.format("Tenant ID: %s", pTenant.getTenantId()));
    } finally {
      NDSPromMetricsSvc.recordTimer(timer);
    }

    return currentOps;
  }

  @VisibleForTesting
  protected NDSDataLakeCurrentOpView toNDSDataLakeCurrentOpView(CurrentOp op) {
    DocumentCodec dc = new DocumentCodec();
    final Document clientMetadata =
        dc.decode(
            new BsonBinaryReader(op.getClientMetadata().asReadOnlyByteBuffer()),
            DecoderContext.builder().build());

    NDSDataLakeCurrentOpUserView userView =
        NDSDataLakeCurrentOpUserView.builder()
            .name(op.getUser().getName())
            .databaseName(op.getUser().getDatabaseName())
            .build();

    NDSDataLakeCurrentOpViewBuilder builder =
        NDSDataLakeCurrentOpView.builder()
            .tenantId(op.getTenantId().getValue())
            .correlationId(op.getCorrelationId().getValue())
            .client(op.getClient())
            .clientMetadata(clientMetadata)
            .connectionId(op.getConnectionId())
            .namespace(op.getNamespace())
            .user(userView)
            .startTime(toISOString(op.getStartTime()));

    return builder.build();
  }

  public static class DataSetOperationResponseObserver<T> {
    private final StreamObserver<T> _responseObserver;
    private boolean _wasSuccessful;
    private Exception _error;

    public DataSetOperationResponseObserver(
        final String pOperation,
        final ObjectId pGroupId,
        final int pDataSetCount,
        final CountDownLatch pLatch,
        final Logger pLogger) {
      _responseObserver =
          new StreamObserver<T>() {
            private final String _operation = pOperation;
            private final ObjectId _groupId = pGroupId;
            private final int _dataSetCount = pDataSetCount;
            private final Logger _logger = pLogger;

            @Override
            public void onNext(final T pValue) {
              _logger.info(
                  "successfully completed operation: '{}' on {} data sets in project: '{}'",
                  _operation,
                  _dataSetCount,
                  _groupId);
            }

            @Override
            public void onError(final Throwable pThrowable) {
              _logger.error(
                  "encountered exception during operation: '{}', group {}",
                  _operation,
                  _groupId,
                  pThrowable);
              _error =
                  pThrowable instanceof Exception
                      ? (Exception) pThrowable
                      : new StatusRuntimeException(Status.UNKNOWN);
              pLatch.countDown();
            }

            @Override
            public void onCompleted() {
              _logger.info("completed data set operation: '{}', group {}", _operation, _groupId);
              _wasSuccessful = true;
              pLatch.countDown();
            }
          };

      _wasSuccessful = false;
    }

    public StreamObserver<T> getResponseObserver() {
      return _responseObserver;
    }

    public boolean wasSuccessful() {
      return _wasSuccessful;
    }

    public Exception getError() {
      return _error;
    }
  }

  public static class DataLakeAdminApiOperationOutcome {
    // explanation of grpc status codes:
    // https://grpc.github.io/grpc/core/md_doc_statuscodes.html
    private static final Set<Code> RETRYABLE_STATUS_CODES =
        Set.of(
            Status.UNAVAILABLE.getCode(),
            Status.ABORTED.getCode(),
            Status.DEADLINE_EXCEEDED.getCode(),
            Status.RESOURCE_EXHAUSTED.getCode(),
            Status.INTERNAL.getCode());

    private final Status _status;
    private final Boolean _isRetryable;
    private final Throwable _exception;

    public DataLakeAdminApiOperationOutcome(
        final Status pStatus, final Boolean pIsRetryable, final Throwable pException) {
      _status = pStatus;
      _isRetryable = pIsRetryable;
      _exception = pException;
    }

    public static DataLakeAdminApiOperationOutcome ok() {
      return new DataLakeAdminApiOperationOutcome(Status.OK, false, null);
    }

    public static DataLakeAdminApiOperationOutcome from(final Throwable pThrowable) {
      final Status status = translateToStatus(pThrowable);
      return new DataLakeAdminApiOperationOutcome(
          status, isRetryableFromStatus(status.getCode()), pThrowable);
    }

    private static Status translateToStatus(final Throwable pThrowable) {
      return Status.fromThrowable(pThrowable);
    }

    private static boolean isRetryableFromStatus(final Status.Code pStatusCode) {
      return RETRYABLE_STATUS_CODES.contains(pStatusCode);
    }

    public Status getStatus() {
      return _status;
    }

    public boolean isRetryable() {
      return Optional.ofNullable(_isRetryable).orElse(false);
    }

    public Optional<Throwable> getException() {
      return Optional.ofNullable(_exception);
    }
  }

  public static class Methods {
    // Storage config methods
    public static final String GET_STORAGE = "getStorage";
    public static final String SET_STORAGE = "setStorage";
    public static final String VALIDATE_STORAGE = "validateStorage";
    public static final String DELETE_STORAGE = "deleteStorage";
    public static final String DOWNLOAD_LOGS = "downloadLogs";
    public static final String DELETE_LOGS = "deleteLogs";
    // Online Archive methods
    public static final String INITIALIZE_OA_DATASET = "initializeOADataSet";
    public static final String ENABLE_OA_DATASET = "enableOADataSet";
    public static final String DISABLE_OA_DATASET = "disableOADataSet";
    public static final String DESTROY_OA_DATASET = "destroyOADataSet";
    public static final String REGISTER_OA_FILES = "registerOAFiles";
    public static final String DEREGISTER_OA_FILES = "deregisterOAFiles";
    // DLS methods
    public static final String INITIALIZE_DATASET = "initializeDataSet";
    public static final String ENABLE_DATA_SET = "enableDataSet";
    public static final String DESTROY_DATA_SET = "destroyDataSet";
    public static final String DISABLE_DATA_SET = "disableDataSet";
    public static final String RECONCILE_DATA_SETS = "reconcileDataSets";
    public static final String UPLOAD_DATA_FILES = "uploadDataFiles";
    public static final String MERGE_DATA_SETS = "mergeDataSets";
    // Common/Shared methods
    public static final String GET_JOB_PROGRESS = "getJobProgress";
    public static final String GET_JOB_ERRORS = "getJobErrors";
    // Metrics
    public static final String GET_METRICS = "getMetrics";

    public static final String GET_DATA_SET_METRICS = "getDataSetMetrics";

    // Usage Limits
    public static final String GET_USAGE_LIMITS = "getUsageLimits";
    public static final String SET_USAGE_LIMIT = "setUsageLimit";
    public static final String DELETE_USAGE_LIMIT = "deleteUsageLimit";

    // Regions
    public static final String GET_REGIONS = "getRegions";

    // SQL Schema Management
    public static final String GET_ALL_SCHEMAS = "getAllSchemas";
    public static final String GENERATE_ALL_SCHEMAS = "generateAllSchemas";
    public static final String DELETE_ALL_SCHEMAS = "deleteAllSchemas";
    public static final String DELETE_SCHEMA = "deleteSchema";
    public static final String GENERATE_SCHEMAS = "generateSchemas";
    public static final String SET_SCHEMA = "setSchema";
    public static final String GET_SCHEMA = "getSchema";
    public static final String GET_SCHEDULED_UPDATE = "getScheduledUpdate";
    public static final String CREATE_SCHEDULED_UPDATE = "createScheduledUpdate";
    public static final String MODIFY_SCHEDULED_UPDATE = "modifyScheduledUpdate";
    public static final String DELETE_SCHEDULED_UPDATE = "deleteScheduledUpdate";

    // Private Link
    public static final String ACCEPT_PRIVATE_ENDPOINT = "acceptPrivateEndpoint";
    public static final String DELETE_PRIVATE_ENDPOINT = "deletePrivateEndpoint";
    public static final String GET_SERVICE_NAMES = "getServiceNames";

    // Andon Cords
    public static final String LIST_ANDON_CORDS = "listAndonCords";
    public static final String CREATE_OR_UPDATE_ANDON_CORD = "createOrUpdateAndonCord";

    // Tenant Settings
    public static final String GET_TENANT_SETTINGS = "getTenantSettings";
    public static final String SET_TENANT_SETTINGS = "setTenantSettings";

    // Data Sets
    public static final String LIST_DATA_SETS = "listDataSets";

    // Op
    public static final String LIST_CURRENT_OPS = "listCurrentOps";
  }
}
