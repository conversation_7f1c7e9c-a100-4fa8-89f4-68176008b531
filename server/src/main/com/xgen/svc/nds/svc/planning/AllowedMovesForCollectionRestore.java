package com.xgen.svc.nds.svc.planning;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.nds.planning.common._public.model.PlanSummary;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.SystemProjectType;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.svc.nds.aws.planner.AWSDestroyContainerMove;
import com.xgen.svc.nds.aws.planner.AWSEnsureNetworkPermissionsAppliedMove;
import com.xgen.svc.nds.aws.planner.AWSProvisionContainerMove;
import com.xgen.svc.nds.azure.planner.AzureDestroyContainerMove;
import com.xgen.svc.nds.azure.planner.AzureEnsureNetworkPermissionsAppliedMove;
import com.xgen.svc.nds.azure.planner.AzureProvisionContainerMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPDestroyContainerMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPEnsureNetworkPermissionsAppliedMove;
import com.xgen.svc.nds.gcp.planner.networking.GCPProvisionContainerMove;
import com.xgen.svc.nds.planner.CollectionRestoreCleanupMove;
import com.xgen.svc.nds.planner.CollectionRestoreFromSystemClusterMove;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerGroupMove;
import io.prometheus.client.Counter;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;

/**
 * Concrete implementation of {@link AbstractAllowedMoveFilter} that defines and applies filtering
 * rules specifically for NDS groups identified as Collection Restore. System Projects are
 * ephemeral, internally visible projects used to run particular jobs. In order to perform such
 * actions, System Projects contain copied resources from their respective Source Projects. There is
 * a possibility to plan destructive actions in the System Project that may adversely affect the
 * Source Project. In order to avoid planning unexpected moves, we have defined this list here as a
 * list of allowable moves to be planned for System Projects. If you have any questions or if you
 * want to add to this list, please see this wiki
 * https://wiki.corp.mongodb.com/pages/viewpage.action?pageId=306454664 or reach out to the Atlas
 * Backup team.
 */
public class AllowedMovesForCollectionRestore extends AbstractAllowedMoveFilter {
  private static final Counter UNALLOWED_COLLECTION_RESTORE_MOVES_TOTAL =
      new Counter.Builder()
          .name("mms_cps_unallowed_collection_restore_moves_total")
          .help("Count of unallowed moves attempted to plan for collection restore")
          .labelNames("move")
          .register();

  static final Set<String> ALLOWED_MOVES =
      Set.of(
          // COLLECTION/DB LEVEL RESTORE
          CollectionRestoreFromSystemClusterMove.class.getName(),
          CollectionRestoreCleanupMove.class.getName(),

          // OTHER
          ProcessAutomationConfigPerGroupMove.class.getName());

  static final Set<String> GEO_SHARDED_ALLOWED_MOVES =
      Set.of(
          // PROVISION CONTAINERS
          AWSProvisionContainerMove.class.getName(),
          AzureProvisionContainerMove.class.getName(),
          GCPProvisionContainerMove.class.getName(),

          // NETWORKING
          AWSEnsureNetworkPermissionsAppliedMove.class.getName(),
          AzureEnsureNetworkPermissionsAppliedMove.class.getName(),
          GCPEnsureNetworkPermissionsAppliedMove.class.getName(),

          // DESTROY CONTAINER
          AWSDestroyContainerMove.class.getName(),
          AzureDestroyContainerMove.class.getName(),
          GCPDestroyContainerMove.class.getName());

  private static final String ERROR_MESSAGE_FORMAT =
      "Attempted to plan unallowed move for System Projects. Group: %s, Plan: %s,"
          + " disallowedMoves: %s, allowedMoves: %s";

  @Override
  protected Counter getCounter() {
    return UNALLOWED_COLLECTION_RESTORE_MOVES_TOTAL;
  }

  @Override
  protected String getErrorMessageFormat() {
    return ERROR_MESSAGE_FORMAT;
  }

  @Override
  protected boolean shouldApplyFilter(final NDSGroup ndsGroup) {
    // Filter only applies to Collection Restore
    return SystemProjectType.COLLECTION_RESTORE.equals(ndsGroup.getSystemProjectType());
  }

  @Override
  protected boolean isMoveAllowed(final String moveName, final NDSGroup ndsGroup) {
    final boolean containsCopiedContainers = ndsGroup.getContainsCopiedContainers();
    return isMoveAllowed(moveName, containsCopiedContainers);
  }

  @VisibleForTesting
  static boolean isMoveAllowed(String moveName, boolean containsCopiedContainers) {
    return COMMON_ALLOWED_MOVES.contains(moveName)
        || ALLOWED_MOVES.contains(moveName)
        || (!containsCopiedContainers && GEO_SHARDED_ALLOWED_MOVES.contains(moveName));
  }

  /**
   * Singleton instance of AllowedMovesForSystemExport. This allows for static calling of the
   * removeDisallowedMoves method.
   */
  private static final AllowedMovesForCollectionRestore INSTANCE =
      new AllowedMovesForCollectionRestore();

  /**
   * Filters a list of plan-summary pairs, removing plans that contain disallowed moves for system
   * projects.
   *
   * @param ndsGroup The NDS group for which plans are being filtered.
   * @param planSummaryPairs A list of pairs, each containing a PlanSummary and its corresponding
   *     Plan.
   * @param logger An Logger instance for logging.
   * @return A filtered list of plan-summary pairs, with disallowed plans removed.
   */
  public static List<Pair<PlanSummary, Plan>> filterPlansInSystemProject(
      final NDSGroup ndsGroup,
      final List<Pair<PlanSummary, Plan>> planSummaryPairs,
      final Logger logger) {
    return INSTANCE.removeDisallowedMoves(ndsGroup, planSummaryPairs, logger);
  }

  private AllowedMovesForCollectionRestore() {}
}
