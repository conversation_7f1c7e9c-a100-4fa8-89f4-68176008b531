package com.xgen.svc.nds.svc.cps;

import static com.xgen.cloud.cps.export._public.util.SystemClusterJobPlanLogs.fromCollectionRestoreJob;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.retry._public.RetryUtils;
import com.xgen.cloud.common.util._public.util.BaseHostUtils;
import com.xgen.cloud.common.util._public.util.MathUtils;
import com.xgen.cloud.cps.backupjob._public.model.CustomerCollectionMetadata;
import com.xgen.cloud.cps.backupjob._public.model.CustomerCollectionMetadata.ShardSizes;
import com.xgen.cloud.cps.core._public.config.CpsAppSettings;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.CollectionRestoreStateDao;
import com.xgen.cloud.cps.restore._private.dao.SystemClusterJobDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.CollectionRestore;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreAuthView;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreJob;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreOverallStateView;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreRequest;
import com.xgen.cloud.cps.restore._public.model.CollectionRestoreState;
import com.xgen.cloud.cps.restore._public.model.CpsCollRestoreCleanupAgentJob;
import com.xgen.cloud.cps.restore._public.model.CpsCollRestoreDataTransferAgentJob;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob.StateReason;
import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.EncryptionProviders;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.metrics._public.model.MetricsErrorCode;
import com.xgen.cloud.monitoring.metrics._public.model.MetricsException;
import com.xgen.cloud.nds.activity._public.event.DiskBackupEvent;
import com.xgen.cloud.nds.activity._public.event.audit.DiskBackupAudit;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.metrics._public.svc.NDSComputeClusterMetricsSvc;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.SystemProjectType;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.nds.tenant._private.dao.backup.TenantRestoreDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.PlanAbandonedException;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.util.CommonRestoreUtil;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class CpsCollectionRestoreJobSvc {
  private final AuditSvc auditSvc;
  private final AutomationConfigPublishingSvc automationConfigPublishingSvc;
  private final NDSClusterSvc clusterSvc;
  private final ReplicaSetHardwareSvc replicaSetHardwareSvc;
  private final SystemClusterJobDao systemClusterJobDao;
  private final NDSGroupDao ndsGroupDao;
  private final NDSGroupSvc ndsGroupSvc;
  private final CollectionRestoreStateDao collectionRestoreStateDao;
  private final NDSComputeClusterMetricsSvc ndsComputeClusterMetricsSvc;
  private final AuthzSvc authzSvc;
  private final CpsCollectionMetadataBackupSvc cpsCollectionMetadataBackupSvc;

  private final AppSettings appSettings;
  private final CpsAppSettings cpsAppSettings;
  private final CpsSvc cpsSvc;
  private final TenantRestoreDao tenantRestoreDao;
  private final BackupRestoreJobDao backupRestoreJobDao;
  private final GroupSvc groupSvc;

  private static final Logger LOG = LoggerFactory.getLogger(CpsCollectionRestoreJobSvc.class);
  private static final String MONGODB_SCHEME = "mongodb://";
  private static final String MONGODB_SRV_SCHEME = "mongodb+srv://";
  public static final String DIRECT_CONNECTION = "directConnection=true";
  public static final String REPLICA_SET = "replicaSet=";
  public static final String COLLECTION_TYPE_NOT_FOUND_DB = "not_found_database";
  public static final String COLLECTION_TYPE_NOT_FOUND_COLLECTION = "not_found_collection";

  @Inject
  public CpsCollectionRestoreJobSvc(
      final AuditSvc auditSvc,
      final AutomationConfigPublishingSvc automationConfigPublishingSvc,
      final NDSClusterSvc clusterSvc,
      final ReplicaSetHardwareSvc replicaSetHardwareSvc,
      final SystemClusterJobDao systemClusterJobDao,
      final NDSGroupDao ndsGroupDao,
      final NDSGroupSvc ndsGroupSvc,
      final AppSettings appSettings,
      final CpsAppSettings cpsAppSettings,
      final CpsSvc cpsSvc,
      final NDSComputeClusterMetricsSvc ndsComputeClusterMetricsSvc,
      final CollectionRestoreStateDao collectionRestoreStateDao,
      final AuthzSvc authzSvc,
      final TenantRestoreDao tenantRestoreDao,
      final BackupRestoreJobDao backupRestoreJobDao,
      final GroupSvc groupSvc,
      final CpsCollectionMetadataBackupSvc cpsCollectionMetadataBackupSvc) {
    this.auditSvc = auditSvc;
    this.automationConfigPublishingSvc = automationConfigPublishingSvc;
    this.clusterSvc = clusterSvc;
    this.replicaSetHardwareSvc = replicaSetHardwareSvc;
    this.systemClusterJobDao = systemClusterJobDao;
    this.ndsGroupDao = ndsGroupDao;
    this.ndsGroupSvc = ndsGroupSvc;
    this.appSettings = appSettings;
    this.cpsAppSettings = cpsAppSettings;
    this.cpsSvc = cpsSvc;
    this.collectionRestoreStateDao = collectionRestoreStateDao;
    this.ndsComputeClusterMetricsSvc = ndsComputeClusterMetricsSvc;
    this.authzSvc = authzSvc;
    this.tenantRestoreDao = tenantRestoreDao;
    this.backupRestoreJobDao = backupRestoreJobDao;
    this.groupSvc = groupSvc;
    this.cpsCollectionMetadataBackupSvc = cpsCollectionMetadataBackupSvc;
  }

  public void versionValidation(
      ClusterDescription targetCluster, Group targetGroup, BackupSnapshot snapshot)
      throws SvcException {
    if (snapshot.getMongoDbVersion().isLessThan(VersionUtils.SEVEN_ZERO_ZERO)) {
      throw new SvcException(
          NDSErrorCode.COLLECTION_RESTORE_UNSUPPORTED_VERSION, "source snapshots");
    }
    if (VersionUtils.Version.fromString(targetCluster.getFeatureCompatibilityVersion())
        .isLessThan(VersionUtils.SEVEN_ZERO_ZERO)) {
      throw new SvcException(NDSErrorCode.COLLECTION_RESTORE_UNSUPPORTED_VERSION, "target fcv");
    }
    final AutomationConfig published =
        automationConfigPublishingSvc.findPublished(targetGroup.getId());
    if (published == null) {
      throw new SvcException(CommonErrorCode.NOT_FOUND, "automation config for group");
    }
    final List<Process> processList = cpsSvc.getAllProcesses(targetCluster, published);
    for (final Process process : processList) {
      final VersionUtils.Version procVersion =
          VersionUtils.Version.fromString(process.getVersion());
      if (procVersion.isLessThan(VersionUtils.SEVEN_ZERO_ZERO)) {
        throw new SvcException(
            NDSErrorCode.COLLECTION_RESTORE_UNSUPPORTED_VERSION, "target binaries");
      }
    }
    cpsSvc.validateTargetClusterFcv(targetCluster, snapshot);
    cpsSvc.validateMongodVersionCompatibilityForTarget(targetCluster, published, snapshot);
  }

  public void validateCollectionRestoreAtRequestTime(
      BackupSnapshot snapshot,
      ObjectId sourceGroupId,
      BSONTimestamp sourcePitTime,
      CollectionRestoreRequest collectionRestoreRequest)
      throws SvcException {
    cpsSvc.validateGroupRegionRestriction(
        sourceGroupId, collectionRestoreRequest.getTargetProjectId());
    final ClusterDescription targetCluster =
        getClusterDescription(
            collectionRestoreRequest.getTargetProjectId(),
            collectionRestoreRequest.getTargetClusterUniqueId());
    // Validate that we can find the target group.
    final Group targetGroup = groupSvc.findById(collectionRestoreRequest.getTargetProjectId());
    if (targetGroup == null) {
      throw new SvcException(
          NDSErrorCode.INVALID_GROUP_ID, collectionRestoreRequest.getTargetProjectId());
    }

    // Do a series of common validations.
    CpsSvc.validateNotPausedTarget(targetCluster);
    final ClusterDescription sourceCluster =
        getClusterDescription(snapshot.getProjectId(), snapshot.getClusterUniqueId());
    CpsSvc.validateComplianceLevelMatch(
        targetGroup.getId(), targetCluster.getName(), sourceCluster, targetCluster);
    // Both target and source clusters must be dedicated.
    if (!(targetCluster.isDedicatedCluster() && sourceCluster.isDedicatedCluster())) {
      throw new SvcException(
          NDSErrorCode.UNSUPPORTED_FOR_NON_DEDICATED_CLUSTER, "collection restore");
    }
    CpsSvc.validateRestoreTargetStorageSystem(snapshot, targetCluster);
    cpsSvc.validateNoConcurrentLiveImport(targetGroup.getId(), targetCluster.getName());
    CommonRestoreUtil.validateNoOngoingRestoreIncludingCollectionLevelRestore(
        targetCluster, targetGroup, tenantRestoreDao, backupRestoreJobDao, systemClusterJobDao);
    cpsSvc.validateTargetHostClusterExists(targetGroup, targetCluster.getName(), targetCluster);

    // Validate version compatibility.
    versionValidation(targetCluster, targetGroup, snapshot);

    // Perform additional PIT specific validations.
    if (sourcePitTime != null) {
      final EncryptionProviders targetEncryptionProviders =
          cpsSvc.getEncryptionProviders(targetGroup.getId(), targetCluster.getName());
      final List<ReplicaSetBackupSnapshot> rsSnapshots = cpsSvc.getSnapshotMembers(snapshot);
      final Group sourceGroup = groupSvc.findById(sourceGroupId);
      if (sourceGroup == null) {
        throw new SvcException(NDSErrorCode.INVALID_GROUP_ID, sourceGroupId);
      }

      // This function does validation as a side effect, ignore the return value.
      cpsSvc.getPitStoragesToUseForRestore(
          sourceGroup,
          sourceCluster,
          targetCluster.getGroupId(),
          targetCluster.getName(),
          targetEncryptionProviders,
          sourcePitTime,
          rsSnapshots);
    }

    // Validate that the collection metadata exists for this request. We do this at the end as it an
    // S3 request.
    final Optional<CustomerCollectionMetadata> collectionMetadataOpt =
        cpsCollectionMetadataBackupSvc.getCustomerCollectionMetadata(snapshot);
    if (collectionMetadataOpt.isEmpty()) {
      throw new SvcException(
          NDSErrorCode.COLLECTION_RESTORE_CUSTOMER_COLLECTION_METADATA_NOT_FOUND, snapshot.getId());
    }

    // Validate the target disk space.
    // Exclude PIT restores, we cannot determine at request time how large the restore will be as
    // customers could insert / remove data and or collections between snapshots.
    if (!hasEnoughDiskSpaceBuffer(
            collectionRestoreRequest.getTargetProjectId(),
            collectionRestoreRequest.getTargetClusterUniqueId())
        || (sourcePitTime == null
            && !hasEnoughDiskSpaceToRestore(
                collectionMetadataOpt.get(), collectionRestoreRequest))) {
      throw new SvcException(NDSErrorCode.COLLECTION_RESTORE_INSUFFICIENT_DISK_SPACE);
    }
  }

  public ObjectId validateAndCreateCollectionRestoreJob(
      Organization organization,
      AppUser appUser,
      BSONTimestamp sourcePitTime,
      CollectionRestoreRequest collectionRestoreRequest,
      AuditInfo auditInfo,
      ObjectId sourceGroupId,
      ObjectId sourceClusterUniqueId,
      ObjectId snapshotId)
      throws SvcException {

    final BackupSnapshot snapshot =
        cpsSvc.getAndValidateBackupSnapshot(
            sourceGroupId, sourceClusterUniqueId, snapshotId, sourcePitTime);

    validateCollectionRestoreAtRequestTime(
        snapshot, sourceGroupId, sourcePitTime, collectionRestoreRequest);

    return create(
        organization, appUser, snapshot, sourcePitTime, collectionRestoreRequest, auditInfo);
  }

  public ObjectId create(
      Organization organization,
      AppUser appUser,
      BackupSnapshot snapshot,
      BSONTimestamp sourcePitTime,
      CollectionRestoreRequest collectionRestoreRequest,
      AuditInfo auditInfo)
      throws SvcException {
    SystemClusterJob.RequestingUser requestingUser = new SystemClusterJob.RequestingUser(appUser);
    final CollectionRestoreJob job =
        new CollectionRestoreJob(
            snapshot.getProjectId(),
            snapshot.getClusterName(),
            snapshot.getClusterUniqueId(),
            organization.getId(),
            snapshot.getId(),
            sourcePitTime,
            requestingUser,
            snapshot.getSnapshotInitiationDate(),
            snapshot.getPitSentinelOptime(),
            new CollectionRestore(collectionRestoreRequest));
    systemClusterJobDao.save(job);
    saveRestoreRequestedAudit(job.getId(), snapshot, appUser, auditInfo);
    ndsGroupDao.setPlanASAP(snapshot.getProjectId());
    return job.getId();
  }

  public Optional<CollectionRestoreJob> findById(ObjectId id) {
    return systemClusterJobDao
        .findById(id)
        .map(CollectionRestoreJob.class::cast)
        .or(Optional::empty);
  }

  public CollectionRestoreJob getCollectionRestoreJob(final ObjectId jobId) throws SvcException {
    return findById(jobId)
        .orElseThrow(() -> new SvcException(NDSErrorCode.SYSTEM_CLUSTER_JOB_NOT_FOUND, jobId));
  }

  public List<CollectionRestoreJob> findBySourceCluster(
      ObjectId sourceProjectId, ObjectId sourceClusterUniqueId) {
    return systemClusterJobDao
        .findAllJobsBySourceCluster(sourceProjectId, sourceClusterUniqueId)
        .stream()
        .filter(
            systemClusterJob ->
                SystemProjectType.COLLECTION_RESTORE.equals(
                    systemClusterJob.getSystemProjectType()))
        .map(CollectionRestoreJob.class::cast)
        .toList();
  }

  // TODO(CLOUDP-295324): Add activity feed events for collection/DB level restore
  private void saveRestoreRequestedAudit(
      ObjectId jobId, BackupSnapshot snapshot, AppUser requestingUser, AuditInfo auditInfo) {
    if (auditInfo == null) {
      return;
    }
    // Let Morphia generate the id as we may save an event for source and destination group
    final ObjectId auditId = null;

    // Basic audit info all restores have.
    final DiskBackupAudit.Builder builder =
        new DiskBackupAudit.Builder(DiskBackupEvent.Type.CPS_RESTORE_REQUESTED_AUDIT, auditId);
    builder.groupId(snapshot.getProjectId());
    builder.auditInfo(auditInfo);

    if (requestingUser != null) {
      builder.userId(requestingUser.getId());
      builder.username(requestingUser.getUsername());
      builder.isMmsAdmin(authzSvc.hasAnyGlobalRole(requestingUser));
      builder.userType(requestingUser.getType());
      builder.remoteAddr(requestingUser.getLastAuthAddr());
    }
    builder.restoreJobId(jobId);

    builder.clusterId(snapshot.getClusterUniqueId());
    builder.clusterName(snapshot.getClusterName());
    builder.snapshotId(snapshot.getId());
    builder.restoreType(CpsSvc.RESTORE_TYPE_COLLECTION_RESTORE);

    auditSvc.saveAuditEvent(builder.build());
  }

  public void saveCollectionRestoreJob(final CollectionRestoreJob job) {
    systemClusterJobDao.save(job);
  }

  public Result<Result.NoData> processResult(
      final CollectionRestoreJob job, final Result<?> result, final String jobName) {
    if (result.getStatus().isFailed()) {
      markCollectionRestoreFailed(job);
      LOG.atError()
          .setMessage("Failed to execute" + jobName + "due to" + result.getMessage())
          .log();
    } else {
      LOG.atInfo().setMessage(jobName + " is " + result.getStatus()).log();
    }

    return switch (result.getStatus()) {
      case IN_PROGRESS -> Result.inProgress();
      case DONE -> Result.done();
      case FAILED -> Result.failed(result.getMessage());
    };
  }

  /**
   * This creates agent jobs for collection level restore data transfer
   *
   * @param job collection restore job
   * @param isTargetClusterSharded true if the target cluster is a sharded cluster
   * @return a list of data transfer agent jobs sorted by target hostname
   * @throws SvcException if the system cluster is not found
   */
  public List<CpsCollRestoreDataTransferAgentJob> buildDataTransferAgentJobs(
      final CollectionRestoreJob job, final boolean isTargetClusterSharded) throws SvcException {

    final List<MongoConnectionInfo> connectionInfoList = getConnectionInfoListForJob(job);
    final CollectionRestoreRequest restoreRequest =
        job.getCollectionRestore().getCollectionRestoreRequest();
    final ObjectId batchId = new ObjectId();

    return IntStream.range(0, connectionInfoList.size())
        .mapToObj(
            i -> {
              final MongoConnectionInfo connInfo = connectionInfoList.get(i);
              return new CpsCollRestoreDataTransferAgentJob(
                  job.getId(),
                  batchId,
                  connInfo.systemMongoURI(),
                  connInfo.systemHostname(),
                  connInfo.targetMongoURI(),
                  connInfo.targetHostname(),
                  connectionInfoList.size(),
                  i,
                  restoreRequest,
                  isTargetClusterSharded);
            })
        .collect(Collectors.toList());
  }

  /**
   * This creates agent jobs for collection level restore cleanup
   *
   * @param job collection restore job
   * @param isTargetClusterSharded true if the target cluster is a sharded cluster
   * @return a list of cleanup agent jobs sorted by target hostname
   * @throws SvcException if the system cluster is not found
   */
  public List<CpsCollRestoreCleanupAgentJob> buildCleanupAgentJobs(
      final CollectionRestoreJob job, final boolean isTargetClusterSharded) throws SvcException {
    final List<MongoConnectionInfo> connectionInfoList = getConnectionInfoListForJob(job);
    final CollectionRestoreRequest restoreRequest =
        job.getCollectionRestore().getCollectionRestoreRequest();

    final ObjectId batchId = new ObjectId();
    return IntStream.range(0, connectionInfoList.size())
        .mapToObj(
            i -> {
              final MongoConnectionInfo connInfo = connectionInfoList.get(i);
              return new CpsCollRestoreCleanupAgentJob(
                  job.getId(),
                  batchId,
                  connInfo.systemMongoURI(),
                  connInfo.systemHostname(),
                  connInfo.targetMongoURI(),
                  connInfo.targetHostname(),
                  connectionInfoList.size(),
                  i,
                  restoreRequest,
                  isTargetClusterSharded,
                  job.getCollectionRestore().shouldDropTempCollections());
            })
        .collect(Collectors.toList());
  }

  /** Extracted common logic for getting connection info */
  private List<MongoConnectionInfo> getConnectionInfoListForJob(final CollectionRestoreJob job)
      throws SvcException {
    // Step 1: Getting info source cluster (system cluster)
    final ClusterDescription systemClusterDescription =
        clusterSvc
            .getClusterDescriptionByUniqueId(
                job.getSystemProjectId(), job.getSystemClusterUniqueId())
            .orElseThrow(
                () ->
                    new SvcException(
                        NDSErrorCode.CLUSTER_NOT_FOUND,
                        job.getSystemClusterName(),
                        job.getSystemProjectId()));

    // Step 2: Getting info target cluster
    final ClusterDescription targetClusterDescription =
        getClusterDescription(
            job.getCollectionRestore().getCollectionRestoreRequest().getTargetProjectId(),
            job.getCollectionRestore().getCollectionRestoreRequest().getTargetClusterUniqueId());

    final List<MongoConnectionInfo> mongoConnectionInfoList =
        getMongoConnectionInfoList(systemClusterDescription, targetClusterDescription);

    LOG.info(
        "Get connection info for collection restore job {} "
            + "to connect from target cluster {} to system cluster {}, "
            + "mongoConnectionInfoList: {}",
        job.getId(),
        targetClusterDescription.getName(),
        systemClusterDescription.getName(),
        mongoConnectionInfoList);

    return mongoConnectionInfoList;
  }

  /**
   * Get the list of MongoConnectionInfo for connection, sorted by target hostname
   *
   * @param systemClusterDescription system cluster description
   * @param targetClusterDescription target cluster description
   * @return a list of MongoConnectionInfo for connecting to the mongodb on the target cluster and
   *     the mongodb on the system cluster
   */
  @VisibleForTesting
  List<MongoConnectionInfo> getMongoConnectionInfoList(
      final ClusterDescription systemClusterDescription,
      final ClusterDescription targetClusterDescription) {
    // Get targetMongoURI
    // If the target cluster contains any GCP electable node,
    // use hostname based URI. Otherwise, use SRV based URI.
    final List<Process> targetProcessesSorted =
        getNonConfigProcesses(targetClusterDescription, true);
    final List<String> targetHostnamesSorted =
        targetProcessesSorted.stream().map(Process::getHostname).toList();
    final String targetMongoURI;
    if (targetClusterDescription.getCloudProviders().contains(CloudProvider.GCP)) {
      targetMongoURI = getHostnameBasedMongoURI(targetClusterDescription, targetProcessesSorted);
    } else {
      targetMongoURI = getSrvMongoURI(targetClusterDescription);
    }
    // Get systemMongoURI and construct the MongoConnectionInfo
    // If the connection is GCP to GCP cross-project, use public IP based URI
    // and explicitly set the serverName in TLS SNI extension.
    // This ensures the agents can connect without depending on DNS resolution,
    // which is problematic in GCP cross-project network setups.
    // Otherwise, use SRV based URI.
    if (isGcpToGcpCrossProject(targetClusterDescription, systemClusterDescription)) {
      final String systemMongoURI = getPublicIPBasedMongoURI(systemClusterDescription);
      final List<String> systemHostnames =
          getNonConfigProcesses(systemClusterDescription, false).stream()
              .map(Process::getHostname)
              .toList();
      return targetHostnamesSorted.stream()
          .map(
              targetHostname ->
                  new MongoConnectionInfo(
                      systemMongoURI,
                      // Get a random system host as tlsServername for load balancing
                      systemHostnames.get(new Random().nextInt(systemHostnames.size())),
                      targetMongoURI,
                      targetHostname))
          .toList();
    } else {
      // Not GCP to GCP, use SRV based URI, and we don't need the system hostname.
      final String systemMongoURI = getSrvMongoURI(systemClusterDescription);
      return targetHostnamesSorted.stream()
          .map(
              targetHostname ->
                  new MongoConnectionInfo(systemMongoURI, "", targetMongoURI, targetHostname))
          .toList();
    }
  }

  // The system cluster and target cluster are never in a same project.
  // Check whether they are in different projects for logical integrity.
  private static boolean isGcpToGcpCrossProject(
      final ClusterDescription targetClusterDescription,
      final ClusterDescription systemClusterDescription) {
    return systemClusterDescription.getCloudProviders().contains(CloudProvider.GCP)
        && targetClusterDescription.getCloudProviders().contains(CloudProvider.GCP)
        && !systemClusterDescription.getGroupId().equals(targetClusterDescription.getGroupId());
  }

  /**
   * Get the public IP based URI for the cluster. E.g.
   * mongodb://publicIP1:27017,publicIP2:27017,publicIP3:27017 for a replica set,
   * mongodb://publicIP1:27016,publicIP2:27016,publicIP3:27016 for a sharded. cluster If the cluster
   * is a sharded cluster, we randomly permutes the host list to load balance the mongos we connect
   * to. If the cluster is a replica set, the order doesn't matter because the driver always routes
   * all write operations to the PRIMARY.
   *
   * @param cd cluster description to get hostname:port URI.
   * @return the hostname based URI for the cluster.
   */
  @VisibleForTesting
  String getPublicIPBasedMongoURI(final ClusterDescription cd) {
    final List<ReplicaSetHardware> replicaSetHardwares =
        replicaSetHardwareSvc.getReplicaSetHardware(cd.getGroupId(), cd.getName());
    final List<String> publicIPPortList =
        replicaSetHardwares.stream()
            .filter(ReplicaSetHardware::containsShardData)
            .flatMap(rsh -> rsh.getProvisionedPublicIPs().stream())
            .map(ip -> assembleHostnameAndPort(cd, ip))
            .collect(Collectors.toList());

    if (cd.getClusterType().isReplicaSet()) {
      if (replicaSetHardwares.size() == 1) {
        // If it's a single node replica set, we need to set the directConnection option to tell the
        // MongoDB driver to connect directly to the host specified in the connection string,
        // rather than attempting to discover and connect to all nodes in a replica set.
        return MONGODB_SCHEME + StringUtils.join(publicIPPortList, ",") + "/?" + DIRECT_CONNECTION;
      } else {
        // We will not hit this branch in collection restore because the system cluster is always
        // single
        // node per replica set.
        final String replicaSetName = replicaSetHardwares.get(0).getRsId();
        return MONGODB_SCHEME
            + StringUtils.join(publicIPPortList, ",")
            + "/?"
            + REPLICA_SET
            + replicaSetName;
      }
    } else {
      // Shuffle mongos hosts for sharded clusters
      Collections.shuffle(publicIPPortList);
      return MONGODB_SCHEME + StringUtils.join(publicIPPortList, ",");
    }
  }

  private static String assembleHostnameAndPort(final ClusterDescription cd, final String ip) {
    return BaseHostUtils.assembleHostnameAndPort(
        ip,
        cd.getClusterType().isSharded()
            ? NDSDefaults.MONGOS_PUBLIC_PORT
            : NDSDefaults.MONGOD_PUBLIC_PORT);
  }

  /**
   * Get the hostname based URI for the cluster. E.g. mongodb://host1:27017,host2:27017,host3:27017
   * for a replica set, mongodb://host1:27016,host2:27016,host3:27016 for a sharded cluster. If the
   * cluster is a sharded cluster, we randomly permutes the host list to load balance the mongos we
   * connect to. If the cluster is a replica set, the order doesn't matter because the driver always
   * routes all write operations to the PRIMARY.
   *
   * @param cd cluster description to get hostname:port URI.
   * @return the hostname based URI for the cluster.
   */
  @VisibleForTesting
  String getHostnameBasedMongoURI(final ClusterDescription cd, final List<Process> processes) {
    final List<String> hostURIList =
        processes.stream().map(Process::getHostnameAndPort).collect(Collectors.toList());
    if (cd.getClusterType().isReplicaSet()) {
      if (processes.size() == 1) {
        // If it's a single node replica set, we need to set the directConnection option to tell the
        // MongoDB driver to connect directly to the host specified in the connection string,
        // rather than attempting to discover and connect to all nodes in a replica set.
        // We will not hit this branch, because Atlas does not support single node replica set yet.
        return MONGODB_SCHEME + StringUtils.join(hostURIList, ",") + "/?" + DIRECT_CONNECTION;
      } else {
        final String replicaSetName = processes.get(0).getReplSetName();
        return MONGODB_SCHEME
            + StringUtils.join(hostURIList, ",")
            + "/?"
            + REPLICA_SET
            + replicaSetName;
      }
    } else {
      // Shuffle mongos hosts for sharded clusters
      Collections.shuffle(hostURIList);
      return MONGODB_SCHEME + StringUtils.join(hostURIList, ",");
    }
  }

  /**
   * Get the mongo SRV URI for the cluster.
   *
   * @param cd cluster description to get SRV URI.
   * @return the mongodb+srv:// URI for the cluster. E.g.
   *     "mongodb+srv://multicloudcluster0-0723.jvjsio.mmscloudteam.com"
   */
  @VisibleForTesting
  static String getSrvMongoURI(final ClusterDescription cd) {
    final String systemSrvAddress =
        cd.getSRVAddress().orElseThrow(() -> new IllegalStateException("SRV Address unavailable"));
    return MONGODB_SRV_SCHEME + systemSrvAddress;
  }

  public static ProcessType getProcessType(ClusterDescription clusterDescription) {
    final ClusterDescription.ClusterType clusterType = clusterDescription.getClusterType();
    return clusterType.isSharded() ? ProcessType.MONGOS : ProcessType.MONGOD;
  }

  public List<Process> getNonConfigProcesses(
      final ClusterDescription clusterDescription, final boolean sorted) {
    final ProcessType processType = getProcessType(clusterDescription);

    // List hardware. We use this model that lets us filter to replica sets that have shard data.
    final List<ReplicaSetHardware> replicaSetHardwares =
        replicaSetHardwareSvc.getReplicaSetHardware(
            clusterDescription.getGroupId(), clusterDescription.getName());
    final Set<String> nonConfigReplicaSetIds =
        replicaSetHardwares.stream()
            .filter(ReplicaSetHardware::containsShardData)
            .map(ReplicaSetHardware::getRsId)
            .collect(Collectors.toSet());
    LOG.info("Listed replica sets with shard data: ids={}", nonConfigReplicaSetIds);

    // This shardName gets included in exported object keys. All replica set
    // clusters use this in their exported object keys. Sharded clusters only
    // use this if they fail to look up the primary shard for a db.
    if (nonConfigReplicaSetIds.isEmpty()) {
      throw new IllegalStateException(
          "no non-config ReplicaSet found for collection restore job's ShardName param");
    }

    // Get the set of agent hostnames. We create one AgentParams per agent (on non-config replica
    // sets).
    final Deployment deployment =
        Optional.ofNullable(
                automationConfigPublishingSvc.findPublished(clusterDescription.getGroupId()))
            .orElseThrow(() -> new IllegalStateException("no Deployment found for the cluster"))
            .getDeployment();
    final String deploymentClusterName = clusterDescription.getDeploymentClusterName();
    final List<Process> processesAll =
        clusterDescription.getClusterType().isSharded()
            ? deployment.getMongosByClusterName(deploymentClusterName)
            : deployment.getMongodByClusterName(deploymentClusterName);
    final Stream<Process> processStream =
        processesAll.stream()
            .filter(
                p ->
                    nonConfigReplicaSetIds.contains(p.getReplSetName())
                        || p.getReplSetName() == null);
    final List<Process> processList =
        sorted
            ? processStream.sorted(Comparator.comparing(Process::getHostname)).toList()
            : processStream.toList();
    LOG.info(
        "Listed processes for collection restore job: filtered={}, type={}, all={}",
        processList.size(),
        clusterDescription.getClusterType(),
        processesAll);
    if (processList.isEmpty()) {
      throw new IllegalStateException(
          String.format(
              "no %s Processes for %s type cluster in project's automation config:"
                  + " ProjectId=%s, deploymentClusterName=%s",
              processType,
              clusterDescription.getClusterType(),
              clusterDescription.getGroupId(),
              deploymentClusterName));
    }

    return processList;
  }

  public ClusterDescription getClusterDescription(
      ObjectId targetProjectId, ObjectId targetClusterUniqueId) {
    return clusterSvc
        .getClusterDescriptionByUniqueId(targetProjectId, targetClusterUniqueId)
        .orElseThrow(
            () -> new IllegalStateException("no ClusterDescription found for the target cluster"));
  }

  public List<CollectionRestoreState> findRestoreStatesByJobId(ObjectId jobId) {
    return collectionRestoreStateDao.findRestoreStatesByJobId(jobId);
  }

  // Get the free disk space for a given cluster. The callee function can fail if it is unable to
  // query a majority of the cluster. It returns an emtpy option in this case, which is converted
  // into an exception.
  public Double getClusterFreeDiskSpace(ClusterDescription clusterDescription)
      throws MetricsException {
    Optional<Double> freeDiskSpaceBytesOpt =
        ndsComputeClusterMetricsSvc.getMinFreeDiskSpaceForCluster(clusterDescription);
    if (freeDiskSpaceBytesOpt.isEmpty()) {
      throw new MetricsException(MetricsErrorCode.LATEST_METRICS_DATA_NOT_AVAILABLE, "Disk Space");
    }
    return freeDiskSpaceBytesOpt.get();
  }

  // Get the free disk space for a given cluster retrying 5 times before throwing.
  public double getClusterFreeDiskSpaceRetryable(ClusterDescription clusterDescription)
      throws MetricsException {
    try {
      return RetryUtils.callWithRetry(
          () -> getClusterFreeDiskSpace(clusterDescription),
          MetricsException.class::isInstance,
          5,
          LOG);
    } catch (SvcException e) {
      throw new MetricsException(MetricsErrorCode.LATEST_METRICS_DATA_NOT_AVAILABLE, "Disk Space");
    }
  }

  public boolean hasEnoughDiskSpaceToRestore(CollectionRestoreJob collectionRestoreJob) {
    final double totalSize =
        (double)
            findRestoreStatesByJobId(collectionRestoreJob.getId()).stream()
                .filter(state -> CollectionRestoreState.State.NOT_STARTED.equals(state.getState()))
                .mapToLong(CollectionRestoreState::getTotalSizeBytes)
                .sum();
    final CollectionRestoreRequest restoreRequest =
        collectionRestoreJob.getCollectionRestore().getCollectionRestoreRequest();
    final boolean validDiskSpace =
        hasEnoughDiskSpaceToRestore(
            restoreRequest.getTargetProjectId(),
            restoreRequest.getTargetClusterUniqueId(),
            totalSize);
    LOG.atError()
        .setMessage("The target cluster does not have enough free disk space to restore")
        .addKeyValue("logEntries", fromCollectionRestoreJob(collectionRestoreJob))
        .addKeyValue("total size to restore", totalSize)
        .log();
    return validDiskSpace;
  }

  // Given a request and the associated metadata file compute the total storage size of the restore.
  public double getTotalSizeFromMetadata(
      CustomerCollectionMetadata metadata, CollectionRestoreRequest request) throws SvcException {
    double dataSize = 0.0;

    // Handle database-level restores.
    for (CollectionRestoreRequest.RestoreName database : request.getDbsToRestore()) {
      String databaseName = database.getSourceName();

      // Find all collections in this database from metadata.
      for (String namespace : metadata.getNamespaceStats().keySet()) {
        if (namespace.startsWith(databaseName + ".")) {
          Map<String, ShardSizes> shardSizes = metadata.getNamespaceStats().get(namespace);
          if (shardSizes != null) {
            for (CustomerCollectionMetadata.ShardSizes sizes : shardSizes.values()) {
              dataSize += sizes.getDataStorageSize();
            }
          } else {
            throw new SvcException(NDSErrorCode.COLLECTION_RESTORE_INVALID_REQUEST);
          }
        }
      }
    }

    // Handle collection-level restores.
    for (CollectionRestoreRequest.RestoreName collection : request.getCollectionsToRestore()) {
      String namespace = collection.getSourceName();

      Map<String, CustomerCollectionMetadata.ShardSizes> shardSizes =
          metadata.getNamespaceStats().get(namespace);

      if (shardSizes != null) {
        for (CustomerCollectionMetadata.ShardSizes sizes : shardSizes.values()) {
          dataSize += sizes.getDataStorageSize();
        }
      } else {
        throw new SvcException(NDSErrorCode.COLLECTION_RESTORE_INVALID_REQUEST);
      }
    }

    if (MathUtils.fuzzyEquals(dataSize, 0.0)) {
      throw new SvcException(NDSErrorCode.COLLECTION_RESTORE_INVALID_REQUEST);
    }
    return dataSize;
  }

  public boolean hasEnoughDiskSpaceToRestore(
      CustomerCollectionMetadata metadata, CollectionRestoreRequest request) throws SvcException {
    return hasEnoughDiskSpaceToRestore(
        request.getTargetProjectId(),
        request.getTargetClusterUniqueId(),
        getTotalSizeFromMetadata(metadata, request));
  }

  /**
   * Returns true if the target cluster has enough free disk space to restore all the unstarted
   * collections.
   *
   * <p>Compare total size for not_started collections to available disk space on the target
   * cluster. We assume the sharded collection are perfectly even sharded. This will underestimate
   * the size of the collection if the sharding is not even. We will reply on the
   * hasEnoughDiskSpaceBuffer to avoid the restore run out of the disk space.
   *
   * @param targetProjectId the target project ID
   * @param targetClusterUniqueId the target cluster unique ID
   * @param totalSize the total size of the restore
   * @return true if the target cluster has enough free disk space to restore the unstarted
   *     collections.
   */
  public boolean hasEnoughDiskSpaceToRestore(
      ObjectId targetProjectId, ObjectId targetClusterUniqueId, double totalSize) {
    final ClusterDescription targetClusterDescription =
        getClusterDescription(targetProjectId, targetClusterUniqueId);
    double freeDiskSpaceBytes;
    try {
      freeDiskSpaceBytes = getClusterFreeDiskSpaceRetryable(targetClusterDescription);
    } catch (MetricsException e) {
      LOG.atWarn().setMessage("Could not get free disk space for the target cluster for job").log();
      return false;
    }

    // Total size of all not started collections needing restoration. For sharded clusters we
    // divide by the number of shards. This assumes perfect sharding, additionally all replica sets
    // are considered a single shard.
    final double totalSizePerShard = totalSize / targetClusterDescription.getNumShards();
    final double diskSpaceThreshold = getCollectionRestoreDiskSpaceThreshold();
    if ((freeDiskSpaceBytes * diskSpaceThreshold) < totalSizePerShard) {
      return false;
    }
    return true;
  }

  /**
   * Returns true if the target cluster has sufficient buffer space,
   *
   * <p>It ensures the free disk space on the target cluster exceeds a minimum free disk space to
   * avoid running out of disk space during the restore.
   *
   * @param collectionRestoreJob collection restore job
   * @return true if the target cluster has sufficient buffer space
   */
  public boolean hasEnoughDiskSpaceBuffer(CollectionRestoreJob collectionRestoreJob) {
    final CollectionRestoreRequest restoreRequest =
        collectionRestoreJob.getCollectionRestore().getCollectionRestoreRequest();
    final boolean validBuffer =
        hasEnoughDiskSpaceBuffer(
            restoreRequest.getTargetProjectId(), restoreRequest.getTargetClusterUniqueId());
    if (!validBuffer) {
      LOG.atError()
          .setMessage("Buffer check failed: insufficient free disk space")
          .addKeyValue("collectionRestoreJobId", collectionRestoreJob.getId())
          .log();
    }
    return validBuffer;
  }

  public boolean hasEnoughDiskSpaceBuffer(
      ObjectId targetProjectId, ObjectId targetClusterUniqueId) {
    final ClusterDescription targetClusterDescription =
        getClusterDescription(targetProjectId, targetClusterUniqueId);
    double freeDiskSpaceBytes;
    try {
      freeDiskSpaceBytes = getClusterFreeDiskSpaceRetryable(targetClusterDescription);
    } catch (MetricsException e) {
      LOG.atWarn().setMessage("Could not get free disk space for the target cluster for job").log();
      return false;
    }

    if (freeDiskSpaceBytes
        < Units.GIGABYTES.convertTo(
            cpsAppSettings.getCollectionRestoreFreeDiskSpaceBuffer(), Units.BYTES)) {
      return false;
    }
    return true;
  }

  public int getSplitLargeCollectionThresholdSizeMb() {
    return appSettings.getIntProp(
        "mms.cps.cpsCollectionLevelRestoreSplitLargeCollectionThresholdSizeMB", 51_200);
  }

  @Nullable
  public Auth getSystemProjectAuth(final CollectionRestoreJob job) {
    if (job.isCompleted()) {
      return null;
    }
    final AutomationConfig autoConfig =
        automationConfigPublishingSvc.findPublished(job.getSystemProjectId());
    if (autoConfig == null || autoConfig.getDeployment() == null) {
      return null;
    }
    return autoConfig.getDeployment().getAuth();
  }

  public void abandonIfTargetClusterInInvalidState(CollectionRestoreJob job) {
    final ObjectId jobId = job.getId();
    final ObjectId targetProjectId =
        job.getCollectionRestore().getCollectionRestoreRequest().getTargetProjectId();
    final String targetClusterName =
        job.getCollectionRestore().getCollectionRestoreRequest().getTargetClusterName();

    final Optional<ClusterDescription> mergedClusterDescription =
        clusterSvc.getMergedClusterDescription(targetProjectId, targetClusterName);

    if (mergedClusterDescription.isEmpty()) {
      LOG.info(
          "Target cluster: {} in target project: {} is missing, abandoning plan for collection"
              + " restore job: {}",
          targetClusterName,
          targetProjectId,
          jobId);
      markCollectionRestoreCanceled(job, new StateReason(NDSErrorCode.CLUSTER_DELETED));
      throw new PlanAbandonedException(NDSErrorCode.CLUSTER_DELETED);
    } else if (mergedClusterDescription.get().isDeleted()) {
      markCollectionRestoreCanceled(job, new StateReason(NDSErrorCode.CLUSTER_DELETED));
      LOG.info(
          "Target cluster: {} in target project: {} is deleted, abandoning plan for collection"
              + " restore job: {}",
          targetClusterName,
          targetProjectId,
          jobId);
      throw new PlanAbandonedException(NDSErrorCode.CLUSTER_DELETED);
    } else if (mergedClusterDescription.get().isDeleteRequested()) {
      markCollectionRestoreCanceled(job, new StateReason(NDSErrorCode.CLUSTER_DELETED));
      LOG.info(
          "Target cluster: {} in target project: {} is delete requested, abandoning plan for"
              + " collection restore job: {}",
          targetClusterName,
          targetProjectId,
          jobId);
      throw new PlanAbandonedException(NDSErrorCode.CLUSTER_DELETED);
    }
  }

  public int getSplitLargeCollectionRangeSizeMB() {
    return appSettings.getIntProp(
        "mms.cps.cpsCollectionLevelRestoreSplitLargeCollectionRangeSizeMB", 3_200);
  }

  public double getCollectionRestoreDiskSpaceThreshold() {
    return cpsAppSettings.getCollectionRestoreDiskSpaceThreshold();
  }

  /**
   * Update the collection restore state.
   *
   * @param collectionRestoreState collection restore state
   */
  public void updateCollectionRestoreState(final CollectionRestoreState collectionRestoreState) {
    collectionRestoreStateDao.save(collectionRestoreState);
  }

  /**
   * Update the collection restore state.
   *
   * @param collectionRestoreJobId collection restore job ID
   * @param collectionRestoreOverallStateView overall state view
   */
  public CollectionRestoreOverallStateView updateCollectionRestoreOverallState(
      final ObjectId collectionRestoreJobId,
      final CollectionRestoreOverallStateView collectionRestoreOverallStateView)
      throws SvcException {
    final CollectionRestoreJob job =
        findById(collectionRestoreJobId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.SYSTEM_CLUSTER_JOB_NOT_FOUND));
    if (job.getCollectionRestore().isFinalState()) {
      return new CollectionRestoreOverallStateView(
          job.getCollectionRestore().getOverallRestoreState(),
          job.getStateReason(),
          job.getCollectionRestore().getLastUpdatedDate());
    }
    systemClusterJobDao.updateCollectionRestoreOverallState(
        collectionRestoreJobId,
        collectionRestoreOverallStateView.state(),
        collectionRestoreOverallStateView.stateReason(),
        collectionRestoreOverallStateView.lastUpdatedDate());
    return collectionRestoreOverallStateView;
  }

  /**
   * Return the overall collection restore state.
   *
   * @param collectionRestoreJobId collection restore job ID
   * @return the collection restore state response object representing the overall in progress
   *     restore state.
   */
  public CollectionRestoreOverallStateView getCollectionRestoreOverallState(
      final ObjectId collectionRestoreJobId) throws SvcException {
    final CollectionRestoreJob job =
        findById(collectionRestoreJobId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.SYSTEM_CLUSTER_JOB_NOT_FOUND));
    return new CollectionRestoreOverallStateView(
        job.getCollectionRestore().getOverallRestoreState(),
        job.getStateReason(),
        job.getCollectionRestore().getLastUpdatedDate());
  }

  public void validateGroupRegionRestriction(
      final ObjectId sourceGroupId, final ObjectId targetGroupId) throws SvcException {
    final Optional<NDSGroup> ndsSourceGroup = ndsGroupSvc.find(sourceGroupId);
    final Optional<NDSGroup> ndsTargetGroup = ndsGroupSvc.find(targetGroupId);

    if (ndsTargetGroup.isPresent() && ndsSourceGroup.isPresent()) {
      if (ndsSourceGroup.get().useCNRegionsOnly() != ndsTargetGroup.get().useCNRegionsOnly()) {
        throw new SvcException(NDSErrorCode.BACKUP_RESTORE_TO_AWS_CN_ONLY_GROUP_INVALID);
      }

      if (!ndsSourceGroup
          .get()
          .getRegionUsageRestrictions()
          .equals(ndsTargetGroup.get().getRegionUsageRestrictions())) {
        throw new SvcException(
            NDSErrorCode.BACKUP_RESTORE_TO_GROUP_WITH_DIFFERENT_REGION_RESTRICTIONS_INVALID);
      }
    }
  }

  public void deleteCollectionRestoreStatesByJobId(ObjectId collectionRestoreJobId) {
    collectionRestoreStateDao.deleteCollectionRestoreStatesByJobId(collectionRestoreJobId);
  }

  public CollectionRestoreAuthView getCollectionRestoreAuthView(ObjectId collectionRestoreJobId)
      throws SvcException {
    final CollectionRestoreJob job =
        findById(collectionRestoreJobId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.SYSTEM_CLUSTER_JOB_NOT_FOUND));
    final Auth auth = getSystemProjectAuth(job);
    if (auth == null) {
      throw new SvcException(
          NDSErrorCode.INVALID_AUTH_SETTINGS,
          "No auth found in the system project " + job.getSystemProjectId());
    }
    return new CollectionRestoreAuthView(collectionRestoreJobId, auth.getAutoPwd());
  }

  public void initCollectionRestoreOverallStateAndNums(CollectionRestoreJob job) {
    final List<CollectionRestoreState> collectionRestoreStates =
        findRestoreStatesByJobId(job.getId());
    long totalDocNum = 0L, totalBytes = 0L;
    int unsupportedCollectionNum = 0;
    final List<String> notFoundCollections = new ArrayList<>();
    final List<String> notFoundDatabases = new ArrayList<>();
    for (final CollectionRestoreState state : collectionRestoreStates) {
      if (state.getState() == CollectionRestoreState.State.UNSUPPORTED) {
        unsupportedCollectionNum++;
      } else if (state.getState() == CollectionRestoreState.State.NOT_FOUND) {
        if (COLLECTION_TYPE_NOT_FOUND_DB.equals(state.getCollectionType())) {
          notFoundDatabases.add(state.getSourceNamespace());
        } else {
          notFoundCollections.add(state.getSourceNamespace());
        }
      } else {
        totalDocNum += state.getDocNum();
        totalBytes += state.getTotalSizeBytes();
      }
    }
    final int totalValidCollectionNum =
        collectionRestoreStates.size()
            - unsupportedCollectionNum
            - notFoundCollections.size()
            - notFoundDatabases.size();
    job.getCollectionRestore()
        .setTotalValidCollectionNum(totalValidCollectionNum)
        .setTotalDocNum(totalDocNum)
        .setTotalSizeBytes(totalBytes)
        .setUnsupportedCollectionNum(unsupportedCollectionNum)
        .setNotfoundCollectionNum(notFoundCollections.size())
        .setNotfoundDatabaseNum(notFoundDatabases.size())
        .setLastUpdatedDate(new Date());

    // Fail the restore if any collection/DB requested to restore is not found, and partial failure
    // is not allowed.
    if ((!notFoundCollections.isEmpty() || !notFoundDatabases.isEmpty())
        && !job.getCollectionRestore().getCollectionRestoreRequest().isPartialFailureAllowed()) {
      markCollectionRestoreFailed(job);
      throw new PlanAbandonedException(
          NDSErrorCode.COLLECTION_RESTORE_FAILED, "got not found collections and databases");
    }
    initCollectionRestoreMetrics(job, collectionRestoreStates);

    job.getCollectionRestore().setOverallRestoreState(CollectionRestore.OverallState.IN_PROGRESS);
    saveCollectionRestoreJob(job);
  }

  private void initCollectionRestoreMetrics(
      CollectionRestoreJob job, List<CollectionRestoreState> collectionRestoreStates) {
    long totalDataSizeBytes = 0L, totalIndexSizeBytes = 0L;
    int totalIndexNum = 0;
    for (final CollectionRestoreState state : collectionRestoreStates) {
      if (state.isValidState()) {
        totalDataSizeBytes += state.getDataSizeBytes();
        totalIndexNum += state.getIndexNum();
        totalIndexSizeBytes += state.getIndexSizeBytes();
      }
    }

    job.getCollectionRestore()
        .getCollectionRestoreMetrics()
        .setTotalDataSizeBytes(totalDataSizeBytes)
        .setTotalIndexNum(totalIndexNum)
        .setTotalIndexSizeBytes(totalIndexSizeBytes)
        .setTotalSizeBytes(totalDataSizeBytes + totalIndexSizeBytes)
        .setTransferStartedDate(new Date());
  }

  public void markCollectionRestoreCanceled(CollectionRestoreJob job, StateReason stateReason) {
    markCollectionRestoreFailedOrCanceled(job, stateReason, true);
  }

  public void markCollectionRestoreFailed(CollectionRestoreJob job, StateReason stateReason) {
    markCollectionRestoreFailedOrCanceled(job, stateReason, false);
  }

  public void markCollectionRestoreFailedOrCanceled(
      CollectionRestoreJob job, StateReason stateReason, boolean isCanceled) {
    CollectionRestore.OverallState overallState =
        isCanceled
            ? CollectionRestore.OverallState.CANCELED
            : CollectionRestore.OverallState.FAILED;
    markCollectionRestoreStatesAsRollback(job);
    job.getCollectionRestore().setOverallRestoreState(overallState);
    job.setStateReason(stateReason);
    saveCollectionRestoreJob(job);
    LOG.atWarn()
        .setMessage(
            "collection restore job %s is %s: %s"
                .formatted(job.getId(), overallState, stateReason.message()))
        .addKeyValue("logEntries", fromCollectionRestoreJob(job))
        .log();
  }

  public void markCollectionRestoreFailed(CollectionRestoreJob job) {
    if (job.getCollectionRestore().isCanceled()
        || job.getOverallStatus() == SystemClusterJob.SystemClusterJobStatus.CANCELED
        || job.getCollectionRestore().isFailed()
        || job.getOverallStatus() == SystemClusterJob.SystemClusterJobStatus.FAILED) {
      LOG.atInfo()
          .setMessage("job is already canceled or failed, skip marking it failed again.")
          .addKeyValue("logEntries", fromCollectionRestoreJob(job))
          .log();
      return;
    }
    final String failedMessage = getFailedMessage(job);
    // If all collections failed, or partial failure is not allowed, mark the job as failed
    if (job.getCollectionRestore().getFailedCollectionNum()
            == job.getCollectionRestore().getTotalValidCollectionNum()
        || !job.getCollectionRestore().getCollectionRestoreRequest().isPartialFailureAllowed()) {
      final StateReason stateReason =
          new StateReason(NDSErrorCode.COLLECTION_RESTORE_FAILED, failedMessage);
      markCollectionRestoreFailed(job, stateReason);
      return;
    }

    // Partial failure is allowed, and not all collections have failed.
    final StateReason stateReason =
        new StateReason(NDSErrorCode.COLLECTION_RESTORE_PARTIALLY_COMPLETED, failedMessage);
    markCollectionRestorePartialFailedOrPartialFailedInProgress(job, stateReason);
  }

  private void markCollectionRestorePartialFailedOrPartialFailedInProgress(
      CollectionRestoreJob job, StateReason stateReason) {
    // If failed + restore is equal to total valid number, the job has completed, mark it as partial
    // failed.
    final CollectionRestore collectionRestore = job.getCollectionRestore();
    if (collectionRestore.getFailedCollectionNum() + collectionRestore.getRestoredCollectionNum()
        == collectionRestore.getTotalValidCollectionNum()) {
      collectionRestore.setOverallRestoreState(CollectionRestore.OverallState.PARTIAL_FAILED);
      collectionRestore.getCollectionRestoreMetrics().setTransferFinishedDate(new Date());
      LOG.atWarn()
          .setMessage(
              "collection restore job %s partially failed: %s"
                  .formatted(job.getId(), stateReason.message()))
          .addKeyValue("logEntries", fromCollectionRestoreJob(job))
          .log();
    } else {
      collectionRestore.setOverallRestoreState(
          CollectionRestore.OverallState.PARTIAL_FAILED_IN_PROGRESS);
    }
    job.setStateReason(stateReason);
    saveCollectionRestoreJob(job);
  }

  public void updateCollectionRestoreOverallStateAndNums(CollectionRestoreJob job) {
    final List<CollectionRestoreState> collectionRestoreStates =
        findRestoreStatesByJobId(job.getId());
    Date lastUpdatedDate = job.getCollectionRestore().getLastUpdatedDate();
    long totalRestoreDocNum = 0L;
    int finalizingCollectionNum = 0, successfulCollectionNum = 0;
    final List<String> failedCollectionList = new ArrayList<>();
    for (final CollectionRestoreState state : collectionRestoreStates) {
      // Get the most recent updated date
      lastUpdatedDate =
          lastUpdatedDate.after(state.getLastUpdatedDate())
              ? lastUpdatedDate
              : state.getLastUpdatedDate();
      totalRestoreDocNum += state.getRestoredDocNum();
      if (state.getState() == CollectionRestoreState.State.FAILED) {
        failedCollectionList.add(state.getSourceNamespace());
      } else if (state.getState() == CollectionRestoreState.State.FINALIZING) {
        finalizingCollectionNum++;
      } else if (state.getState() == CollectionRestoreState.State.SUCCESSFUL) {
        successfulCollectionNum++;
      }
    }
    job.getCollectionRestore()
        .setFailedCollectionNum(failedCollectionList.size())
        .setRestoredCollectionNum(successfulCollectionNum + finalizingCollectionNum)
        .setRestoredDocNum(totalRestoreDocNum)
        .setLastUpdatedDate(lastUpdatedDate);

    if (successfulCollectionNum == job.getCollectionRestore().getTotalValidCollectionNum()
        && job.getCollectionRestore().getFailedCollectionNum() == 0
        && job.getCollectionRestore().getNotFoundCollectionNum() == 0
        && job.getCollectionRestore().getNotFoundDatabaseNum() == 0) {
      if (job.getCollectionRestore().getCollectionRestoreMetrics().getTransferFinishedDate()
          == null) {
        job.getCollectionRestore()
            .getCollectionRestoreMetrics()
            .setTransferFinishedDate(new Date());
      }
      job.getCollectionRestore().setOverallRestoreState(CollectionRestore.OverallState.SUCCESSFUL);
      job.markExecutionCompleted();
    } else if (finalizingCollectionNum == job.getCollectionRestore().getTotalValidCollectionNum()) {
      if (job.getCollectionRestore().getCollectionRestoreMetrics().getTransferFinishedDate()
          == null) {
        job.getCollectionRestore()
            .getCollectionRestoreMetrics()
            .setTransferFinishedDate(new Date());
      }
      job.getCollectionRestore().setOverallRestoreState(CollectionRestore.OverallState.FINALIZING);
    } else if (!failedCollectionList.isEmpty()
        || job.getCollectionRestore().getNotFoundCollectionNum() > 0
        || job.getCollectionRestore().getNotFoundDatabaseNum() > 0) {
      markCollectionRestoreFailed(job);
    }

    saveCollectionRestoreJob(job);
  }

  private static String getFailedMessage(CollectionRestoreJob job) {
    final List<String> failedMessageList = new ArrayList<>();
    // Add failed collections
    if (job.getCollectionRestore().getFailedCollectionNum() > 1) {
      failedMessageList.add(
          job.getCollectionRestore().getFailedCollectionNum() + " collections failed to restore");
    } else if (job.getCollectionRestore().getFailedCollectionNum() == 1) {
      failedMessageList.add("1 collection failed to restore");
    }
    // Add not found collections
    if (job.getCollectionRestore().getNotFoundCollectionNum() > 1) {
      failedMessageList.add(
          job.getCollectionRestore().getNotFoundCollectionNum() + " collections were not found");
    } else if (job.getCollectionRestore().getNotFoundCollectionNum() == 1) {
      failedMessageList.add("1 collection was not found");
    }
    // Add not found databases
    if (job.getCollectionRestore().getNotFoundDatabaseNum() > 1) {
      failedMessageList.add(
          job.getCollectionRestore().getNotFoundDatabaseNum() + " databases were not found");
    } else if (job.getCollectionRestore().getNotFoundDatabaseNum() == 1) {
      failedMessageList.add("1 database was not found");
    }
    if (failedMessageList.isEmpty()) {
      LOG.atError()
          .setMessage(
              "The failedMessageList is empty. The collection restore states initialization failed."
                  + " JobId: "
                  + job.getId())
          .addKeyValue("entries", fromCollectionRestoreJob(job))
          .log();
      return "the collection restore states initialization failed";
    }
    return String.join(", ", failedMessageList);
  }

  public void markFinalizingCollectionRestoreToSuccessful(CollectionRestoreJob job) {
    if (job.getCollectionRestore().getOverallRestoreState()
        != CollectionRestore.OverallState.FINALIZING) {
      return;
    }
    final List<CollectionRestoreState> restoreStates = findRestoreStatesByJobId(job.getId());
    for (final CollectionRestoreState collectionRestoreState : restoreStates) {
      if (collectionRestoreState.getState() == CollectionRestoreState.State.FINALIZING) {
        collectionRestoreState.setState(CollectionRestoreState.State.SUCCESSFUL);
        updateCollectionRestoreState(collectionRestoreState);
      }
    }
    job.getCollectionRestore().setOverallRestoreState(CollectionRestore.OverallState.SUCCESSFUL);
    saveCollectionRestoreJob(job);
  }

  @VisibleForTesting
  void markCollectionRestoreStatesAsRollback(CollectionRestoreJob job) {
    // Set the in progress restore states to failed if the job failed
    final List<CollectionRestoreState> restoreStates = findRestoreStatesByJobId(job.getId());
    int successfulCollectionNum = 0, failedCollectionNum = 0;
    long successfulDocNum = 0L;
    for (final CollectionRestoreState collectionRestoreState : restoreStates) {
      if (collectionRestoreState.getState() == CollectionRestoreState.State.NOT_STARTED) {
        collectionRestoreState.setState(CollectionRestoreState.State.NOT_RESTORED);
        updateCollectionRestoreState(collectionRestoreState);
      } else if (collectionRestoreState.getState() == CollectionRestoreState.State.IN_PROGRESS
          || collectionRestoreState.getState() == CollectionRestoreState.State.FINALIZING) {
        collectionRestoreState.setState(CollectionRestoreState.State.ROLLBACK);
        updateCollectionRestoreState(collectionRestoreState);
        LOG.atInfo()
            .setMessage(
                String.format(
                    "%s is %s, set to failed and rolling back as the job %s failed.",
                    collectionRestoreState.getSourceNamespace(),
                    collectionRestoreState.getState(),
                    collectionRestoreState.getCollectionRestoreJobId()))
            .addKeyValue("collectionRestoreStateId", collectionRestoreState.getId())
            .log();
      } else if (collectionRestoreState.getState() == CollectionRestoreState.State.SUCCESSFUL) {
        successfulCollectionNum++;
        successfulDocNum += collectionRestoreState.getRestoredDocNum();
      } else if (collectionRestoreState.getState() == CollectionRestoreState.State.FAILED) {
        failedCollectionNum++;
      }
    }
    job.getCollectionRestore().setRestoredCollectionNum(successfulCollectionNum);
    job.getCollectionRestore().setRestoredDocNum(successfulDocNum);
    job.getCollectionRestore().setFailedCollectionNum(failedCollectionNum);
    job.getCollectionRestore().getCollectionRestoreMetrics().setTransferFinishedDate(new Date());
  }

  /**
   * Wait for all agent jobs to complete (success or failure) before proceeding.
   *
   * <p>If partial failure is not allowed: return Result.failed() if any job failed; return
   * Result.done() if all jobs succeeded; otherwise return Result.inProgress(). If partial failure
   * is allowed: return Result.inProgress() if any are still in progress; return Result.failed() if
   * all jobs failed; otherwise, at least one job succeeded and none are in progress, return
   * Result.done().
   *
   * @param results list of results from agent job submissions
   * @return result of waiting for all agent jobs to complete
   */
  public static Result<Result.NoData> waitForAllAgentJobsToComplete(
      List<Result<ObjectId>> results, boolean isPartialFailureAllowed) {
    if (!isPartialFailureAllowed) {
      return Result.awaitAll(results);
    }
    int failedCount = 0;
    int successCount = 0;
    int inProgressCount = 0;

    for (final Result<ObjectId> result : results) {
      if (result.getStatus().isFailed()) {
        failedCount++;
      } else if (result.getStatus().isInProgress()) {
        inProgressCount++;
      } else if (result.getStatus().isDone()) {
        successCount++;
      }
    }

    if (inProgressCount > 0) {
      return Result.inProgress(
          String.format(
              "%d agent jobs in progress, %d completed, %d failed",
              inProgressCount, successCount, failedCount));
    }

    // All jobs are complete - return success if any succeeded, otherwise failure
    if (failedCount == results.size()) {
      return Result.failed(String.format("All %d agent jobs failed", failedCount));
    } else {
      // All jobs are complete and at least one succeeded
      LOG.atInfo()
          .setMessage("Agent jobs completed")
          .addKeyValue("successful", successCount)
          .addKeyValue("failed", failedCount)
          .log();
      return Result.done();
    }
  }

  /** This class contains the information needed to connect to the system and target clusters. */
  record MongoConnectionInfo(
      String systemMongoURI, String systemHostname, String targetMongoURI, String targetHostname) {}
}
