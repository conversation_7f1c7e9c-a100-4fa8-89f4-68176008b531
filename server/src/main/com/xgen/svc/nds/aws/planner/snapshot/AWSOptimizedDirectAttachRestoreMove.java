package com.xgen.svc.nds.aws.planner.snapshot;

import com.amazonaws.services.ec2.model.VolumeType;
import com.google.common.annotations.VisibleForTesting;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.atm.agentjobs._public.svc.AgentJobSvc;
import com.xgen.cloud.atm.core._public.svc.AutomationAgentAuditSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob.AttachStatus;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSInstanceHardwareDao;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.capacity._public.svc.AWSCapacityDenylistSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.NDSOrphanedItemDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.OrphanStrategy;
import com.xgen.cloud.nds.cloudprovider._public.model.chef.CloudChefConf;
import com.xgen.cloud.nds.cloudprovider._public.model.chef.CloudChefConf.Builder;
import com.xgen.cloud.nds.common._public.model.ResourceOperationTagType;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ChefCallbackDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.module.common.planner.PlanAbandonedException;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.module.common.planner.model.Result.Status;
import com.xgen.svc.atm.jobs.StartAtlasNodeDataJob;
import com.xgen.svc.nds.aws.planner.AWSAttachVolumeStep;
import com.xgen.svc.nds.aws.planner.AWSAttachVolumeStep.Data;
import com.xgen.svc.nds.aws.planner.AWSCreateVolumeStep;
import com.xgen.svc.nds.aws.planner.AWSDetachVolumeStep;
import com.xgen.svc.nds.aws.planner.AWSInstancePowerCycleMove;
import com.xgen.svc.nds.aws.planner.AWSOrphanVolumeStep;
import com.xgen.svc.nds.planner.BackupDependenciesProvider;
import com.xgen.svc.nds.planner.BackupSnapshotUtils;
import com.xgen.svc.nds.planner.BaseInstancePowerCycleMove;
import com.xgen.svc.nds.planner.ExecuteAutomationAgentJobStep;
import com.xgen.svc.nds.planner.GlobalAttemptLimits;
import com.xgen.svc.nds.planner.MoveProvider;
import com.xgen.svc.nds.planner.ProcessStepsUtil;
import com.xgen.svc.nds.planner.PublishAutomationConfigStep;
import com.xgen.svc.nds.planner.WaitForChefCallbackStep;
import com.xgen.svc.nds.planner.movetypes.DirectAttachRestoreMove;
import com.xgen.svc.nds.planner.snapshot.RestoreStepUtils;
import com.xgen.svc.nds.planner.snapshot.SetRestoreParametersStep;
import com.xgen.svc.nds.svc.CloudChefConfSvc;
import com.xgen.svc.nds.svc.NDSRemoteImageSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.visitor.FinishRestoreJobVisitor;
import com.xgen.svc.nds.util.CloudChefConfUtil;
import com.xgen.svc.nds.util.DiskBackupUtil;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;

public class AWSOptimizedDirectAttachRestoreMove extends AWSInstancePowerCycleMove
    implements DirectAttachRestoreMove {

  private final ObjectId _replicaSetRestoreJobId;

  private final BackupRestoreJobDao _backupRestoreJobDao;
  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final NDSClusterSvc _ndsClusterSvc;
  private final BackupSnapshotDao _backupSnapshotDao;
  private final NDSOrphanedItemDao _ndsOrphanedItemDao;
  private final CpsSvc _cpsSvc;
  private final BackupDependenciesProvider _backupDependenciesProvider;
  private final RestoreStepUtils _restoreStepUtils;
  private final AgentJobSvc _agentJobSvc;
  private final CloudChefConfSvc _cloudChefConfSvc;
  private final NDSRemoteImageSvc _ndsRemoteImageSvc;
  private final ChefCallbackDao _chefCallbackDao;
  private final NDSGroupDao _ndsGroupDao;
  private final GroupDao _groupDao;
  private static final String KMS_KEY_ID = "alias/aws/ebs";

  // Fallback volume types to use if gp3 is not available.
  private static final List<VolumeType> RESTORE_VOLUME_TYPES_FOR_OPTIMIZED_DA =
      List.of(VolumeType.Io2, VolumeType.Io1);

  // Define order: higher index = higher priority
  private static final Map<VolumeType, Integer> VOLUME_TYPE_ORDER_FOR_OPTIMIZED_DA =
      Map.of(
          VolumeType.Gp3, 0,
          VolumeType.Io1, 1,
          VolumeType.Io2, 2);

  // Possible step numbers for optimized volume creation
  private static final int[] CREATE_OPTIMIZED_VOLUME_STEP_NUMBERS = {
    StepNumber.CREATE_OPTIMIZED_VOLUME_FROM_SNAPSHOT_IO2,
    StepNumber.CREATE_OPTIMIZED_VOLUME_FROM_SNAPSHOT_IO1,
    // StepNumber.CREATE_VOLUME_FROM_SNAPSHOT_GP3 is not used in optimized DA yet
    StepNumber.CREATE_VOLUME_FROM_SNAPSHOT // Standard fallback
  };

  @AssistedInject
  public AWSOptimizedDirectAttachRestoreMove(
      @Assisted("planContext") final PlanContext pContext,
      @Assisted("clusterName") final String pClusterName,
      @Assisted("instanceId") final ObjectId pInstanceId,
      @Assisted("replicaSetRestoreJobId") final ObjectId pReplicaSetRestoreJobId,
      final BackupRestoreJobDao pBackupRestoreJobDao,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final NDSClusterSvc pNDSClusterSvc,
      final BackupSnapshotDao pBackupSnapshotDao,
      final AWSInstanceHardwareDao pAWSInstanceHardwareDao,
      final AWSApiSvc pAWSApiSvc,
      final NDSGroupDao pNDSGroupDao,
      final ReplicaSetHardwareDao pReplicaSetHardwareDao,
      final AutomationAgentAuditSvc pAutomationAgentAuditSvc,
      final NDSOrphanedItemDao pNDSOrphanedItemDao,
      final CpsSvc pCpsSvc,
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ProcessStepsUtil pProcessStepsUtil,
      final GroupDao pGroupDao,
      final RestoreStepUtils pRestoreStepUtils,
      final ReplicaSetHardwareSvc pReplicaSetHardwareSvc,
      final AgentJobSvc pAgentJobSvc,
      final CloudChefConfSvc pCloudChefConfSvc,
      final NDSRemoteImageSvc pNdsRemoteImageSvc,
      final ChefCallbackDao pChefCallbackDao,
      final AWSCapacityDenylistSvc pAWSCapacityDenylistSvc) {
    super(
        pContext,
        Map.of(),
        pClusterName,
        pInstanceId,
        false,
        pNDSClusterSvc,
        pNDSGroupDao,
        pAWSApiSvc,
        pAutomationAgentAuditSvc,
        pAutomationConfigSvc,
        pReplicaSetHardwareDao,
        pProcessStepsUtil,
        pGroupDao,
        pAWSInstanceHardwareDao,
        pReplicaSetHardwareSvc,
        pAWSCapacityDenylistSvc);

    _replicaSetRestoreJobId = pReplicaSetRestoreJobId;
    _backupRestoreJobDao = pBackupRestoreJobDao;
    _automationConfigSvc = pAutomationConfigSvc;
    _ndsClusterSvc = pNDSClusterSvc;
    _backupSnapshotDao = pBackupSnapshotDao;
    _ndsOrphanedItemDao = pNDSOrphanedItemDao;
    _cpsSvc = pCpsSvc;
    _backupDependenciesProvider = pBackupDependenciesProvider;
    _restoreStepUtils = pRestoreStepUtils;
    _agentJobSvc = pAgentJobSvc;
    _chefCallbackDao = pChefCallbackDao;
    _cloudChefConfSvc = pCloudChefConfSvc;
    _ndsRemoteImageSvc = pNdsRemoteImageSvc;
    _ndsGroupDao = pNDSGroupDao;
    _groupDao = pGroupDao;
  }

  @AssistedInject
  public AWSOptimizedDirectAttachRestoreMove(
      @Assisted("pId") final ObjectId pId,
      @Assisted("predecessors") final Set<ObjectId> pPredecessors,
      @Assisted("successors") final Set<ObjectId> pSuccessors,
      @Assisted("planContext") final PlanContext pContext,
      @Assisted("state") final State pState,
      @Assisted("clusterName") final String pClusterName,
      @Assisted("instanceId") final ObjectId pInstanceId,
      @Assisted("replicaSetRestoreJobId") final ObjectId pReplicaSetRestoreJobId,
      final BackupRestoreJobDao pBackupRestoreJobDao,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final NDSClusterSvc pNDSClusterSvc,
      final BackupSnapshotDao pBackupSnapshotDao,
      final AWSInstanceHardwareDao pAWSInstanceHardwareDao,
      final AWSApiSvc pAWSApiSvc,
      final NDSGroupDao pNDSGroupDao,
      final ReplicaSetHardwareDao pReplicaSetHardwareDao,
      final AutomationAgentAuditSvc pAutomationAgentAuditSvc,
      final NDSOrphanedItemDao pNDSOrphanedItemDao,
      final CpsSvc pCpsSvc,
      final BackupDependenciesProvider pBackupDependenciesProvider,
      final ProcessStepsUtil pProcessStepsUtil,
      final GroupDao pGroupDao,
      final RestoreStepUtils pRestoreStepUtils,
      final ReplicaSetHardwareSvc pReplicaSetHardwareSvc,
      final AgentJobSvc pAgentJobSvc,
      final CloudChefConfSvc pCloudChefConfSvc,
      final NDSRemoteImageSvc pNdsRemoteImageSvc,
      final ChefCallbackDao pChefCallbackDao,
      final AWSCapacityDenylistSvc pAWSCapacityDenylistSvc) {
    super(
        pId,
        pPredecessors,
        pSuccessors,
        pContext,
        pState,
        Map.of(),
        pClusterName,
        pInstanceId,
        pNDSClusterSvc,
        pNDSGroupDao,
        pAWSApiSvc,
        pAutomationAgentAuditSvc,
        pAutomationConfigSvc,
        pReplicaSetHardwareDao,
        pProcessStepsUtil,
        pGroupDao,
        pAWSInstanceHardwareDao,
        pReplicaSetHardwareSvc,
        pAWSCapacityDenylistSvc);

    _replicaSetRestoreJobId = pReplicaSetRestoreJobId;
    _backupRestoreJobDao = pBackupRestoreJobDao;
    _automationConfigSvc = pAutomationConfigSvc;
    _ndsClusterSvc = pNDSClusterSvc;
    _backupSnapshotDao = pBackupSnapshotDao;
    _ndsOrphanedItemDao = pNDSOrphanedItemDao;
    _cpsSvc = pCpsSvc;
    _backupDependenciesProvider = pBackupDependenciesProvider;
    _restoreStepUtils = pRestoreStepUtils;
    _agentJobSvc = pAgentJobSvc;
    _chefCallbackDao = pChefCallbackDao;
    _cloudChefConfSvc = pCloudChefConfSvc;
    _ndsRemoteImageSvc = pNdsRemoteImageSvc;
    _ndsGroupDao = pNDSGroupDao;
    _groupDao = pGroupDao;
  }

  public static AWSOptimizedDirectAttachRestoreMove factoryCreate(
      final PlanContext pContext,
      final String pClusterName,
      final ObjectId pInstanceId,
      final ObjectId pReplicaSetRestoreJobId) {
    return MoveProvider.createMoveFromFactory(
        AWSOptimizedDirectAttachRestoreMove.class,
        List.of(pContext, pClusterName, pInstanceId, pReplicaSetRestoreJobId));
  }

  @Override
  protected Result<?> rollbackInternal() {
    try {
      initialize();
    } catch (final Exception e) {
      getNDSPlanContext()
          .getLogger()
          .atError()
          .setMessage(
              "Failure initializing direct attach models, "
                  + "won't be able to orphan disk volume. This is most likely because a "
                  + "cluster terminate was wrongly allowed to interleave with this restore.")
          .addKeyValue("clusterName", _clusterName)
          .setCause(e)
          .log();

      return Result.done();
    }

    try {
      markRestoreJobFailed();
      resetMachineShutdown();

      /*
      If the job to trigger chef to execute the disk swap was initiated, we must make sure this is
      no longer in progress before stopping the instance. By allowing chef to complete, we can
      reliably determine what the correct device name for the attached disk should be.
       */
      final Result<?> ensureChefNotInProgress = runIfReady(() -> ensureChefNotInProgress());

      final Result<?> stopInstanceResult =
          runIfReady(
              () -> stopInstance(getInstanceHardware().getHostnameForAgents().get()),
              ensureChefNotInProgress);

      final Result<?> rollbackAttachVolumeResult =
          runIfReady(() -> rollbackAttachVolume(), stopInstanceResult);

      runIfReady(() -> orphanNewVolumeForRollback(), rollbackAttachVolumeResult);

      runIfReady(() -> rollbackGeneratedChefConfig(), rollbackAttachVolumeResult);

      final Result<Result.NoData> updateInstanceHardwareDiskInfoForRollbackResult =
          runIfReady(
              () -> {
                updateInstanceEbsVolumeIdForRollback();
                return Result.done();
              },
              rollbackAttachVolumeResult);

      final Result<?> rollbackDetachVolumeResult =
          runIfReady(() -> rollbackDetachVolume(), updateInstanceHardwareDiskInfoForRollbackResult);

      final Result<?> publishAutomationConfigStepResult =
          runIfReady(
              () -> {
                // generate new automation config for direct attach without restore params
                final AutomationConfig updatedConfig = clearRestoreConfig();

                _restoreStepUtils.resetProcessMdbVersion(updatedConfig, getClusterDescription());

                return publishDirectAttachAutomationConfig(
                    updatedConfig,
                    StepNumber.PUBLISH_RESTORE_VERIFICATION_KEY_AUTOMATION_CONFIG_ROLLBACK);
              },
              rollbackDetachVolumeResult);

      final Result<?> startInstanceResult =
          runIfReady(
              () -> {
                final InstanceSize instanceSize =
                    getReplicaSetHardware()
                        .getById(_instanceId)
                        .get()
                        .getNDSInstanceSize()
                        .orElseThrow();
                final String hostname = getInstanceHardware().getHostnameForAgents().get();
                return startInstance(hostname, instanceSize);
              },
              publishAutomationConfigStepResult);

      // Make sure the processes are restarted
      final Result<?> enableProcessesResult =
          runIfReady(
              () ->
                  enableProcessesAndPublishAutomationConfig(
                      StepNumber.ENABLE_PROCESS_AFTER_ROLLBACK,
                      StepNumber.PUBLISH_AUTOMATION_CONFIG_AFTER_ROLLBACK),
              startInstanceResult);

      return runIfReady(() -> waitForEnableHostGoalStateRollBack(), enableProcessesResult);
    } catch (final Exception e) {
      getNDSPlanContext()
          .getLogger()
          .atError()
          .setMessage("Force node replacement due to rollback failure")
          .setCause(e)
          .log();

      final Result<NoData> rollbackSuccessiveUpgradeResult = rollbackSuccessiveUpgrade();

      if (rollbackSuccessiveUpgradeResult.getStatus() != Status.DONE) {
        return rollbackSuccessiveUpgradeResult;
      }

      forceReplaceInstance();

      // Note that if either of these steps returns IN_PROGRESS, there's no guarantee that we
      // reenter this block since the exception might not be thrown again.
      return Result.awaitAll(
          Result.awaitAll(orphanOldVolumeForRollback(), orphanNewVolumeForRollback()));
    }
  }

  Result<NoData> rollbackSuccessiveUpgrade() {
    return Result.downgradeToNoData(
        _restoreStepUtils.publishConfigForRollbackSuccessiveUpgrade(this, getClusterDescription()));
  }

  @Override
  public Result<?> performInternal() {
    // Step 0: Initialization and Validation
    final DirectAttachReplicaSetBackupRestoreJob restoreJob = getRestoreJob();
    if (restoreJob.getCanceled()) {
      getLogger()
          .atInfo()
          .setMessage("Direct attach restore job cancelled, abandoning plan")
          .addKeyValue("jobId", _replicaSetRestoreJobId)
          .log();

      new FinishRestoreJobVisitor(
              _backupRestoreJobDao, new Date(), true, _cpsSvc::setRestoreJobFinishedDate)
          .visit(restoreJob);
      throw new PlanAbandonedException(NDSErrorCode.SNAPSHOT_RESTORE_CANCELLED);
    }

    final ObjectId groupId = getNDSPlanContext().getGroupId();
    final ObjectId sourceGroupId = restoreJob.getProjectId();
    final ObjectId targetGroupId = restoreJob.getTargetProjectId().orElse(null);

    if (!groupId.equals(sourceGroupId) && !groupId.equals(targetGroupId)) {
      getLogger()
          .atError()
          .setMessage(
              "restore job being planned is not associated with the group in the plan context")
          .log();
      throw new PlanAbandonedException(NDSErrorCode.RESTORE_JOB_GROUP_MISMATCH);
    }

    final ObjectId snapshotId = restoreJob.getSnapshotId();
    final AWSBackupSnapshot snapshot = getSnapshot(snapshotId);
    if (snapshot.getDeleted() || snapshot.getPurged()) {
      getNDSPlanContext()
          .getLogger()
          .atInfo()
          .setMessage(
              "Source snapshot is already deleted, canceling restore job and abandoning plan")
          .addKeyValue("snapshotId", snapshotId)
          .log();
      _cpsSvc.cancelBackupRestoreJob(
          restoreJob, CpsRestoreMetadata.CanceledReason.SOURCE_SNAPSHOT_DELETED);
      throw new PlanAbandonedException(NDSErrorCode.SNAPSHOT_RESTORE_CANCELLED);
    }

    // Abandon if cluster deleted
    DiskBackupUtil.verifyClusterEligibleForRestore(
        getNDSPlanContext().getLogger(),
        _ndsClusterSvc,
        _cpsSvc,
        groupId,
        _clusterName,
        restoreJob);

    initialize();

    incrementMoveActionCounter(
        getInstanceHardware().getAction(), getClass().getSimpleName(), Phase.PERFORM);

    final String hostname = getInstanceHardware().getHostnameForAgents().get();

    // Step 1: Shutdown Mongo processes
    final Result<?> shutdownResult = shutdownMongoProcess(hostname);
    if (!shutdownResult.getStatus().isDone()) {
      return shutdownResult;
    }

    setIdleRestoreToInProgress();
    saveOldVolumeData();
    saveOldVolumeKey();

    // Step 2: Create disk from snapshot
    final Result<AWSCreateVolumeStep.Data> createEbsVolumeWithSnapshotDataResult =
        createEbsVolumeWithSnapshotData();

    if (!createEbsVolumeWithSnapshotDataResult.getStatus().isDone()) {
      return createEbsVolumeWithSnapshotDataResult;
    }

    saveNewVolumeState(createEbsVolumeWithSnapshotDataResult);
    saveNewVolumeKeyState();

    // Step 3: Attach snapshot disk
    final Result<AWSAttachVolumeStep.Data> attachVolumeResult =
        attachVolume(createEbsVolumeWithSnapshotDataResult);

    if (!attachVolumeResult.getStatus().isDone()) {
      return attachVolumeResult;
    }

    final String newDeviceName = attachVolumeResult.getData().getDeviceName();
    setNewDeviceName(newDeviceName);

    // Step 4: Update chef config with then new device name, force chef to run and swap the disks,
    // wait for chef to complete
    final Result<NoData> updateChefConfigResult =
        updateChefConfigAndWaitForChef(getNewDeviceName().get());
    if (!updateChefConfigResult.getStatus().isDone()) {
      return updateChefConfigResult;
    }
    setChefCalledBack();

    // Step 5: Detach old disk
    final Result<NoData> detachVolumeResult = detachEbsVolumeForPerform();
    if (!detachVolumeResult.getStatus().isDone()) {
      return detachVolumeResult;
    }

    updateInstanceEbsVolumeIdForPerform();

    // We must sync up all nodes up to here. SetRestoreParametersStep sets the restore params on all
    // nodes. We want to avoid a situation where the first node to get to that point can set restore
    // params and trigger restore processes in the automation agent of other nodes before the other
    // node can complete their disk swap.
    if (!allDiskSwapsCompleted()) {
      return Result.inProgress();
    }

    // Step 6a: Generate new automation config for direct attach
    // Note: SetRestoreParametersStep updates the restore params for all nodes/hosts in the target
    // cluster.
    final Result<SetRestoreParametersStep.Data> setRestoreParamsResult =
        setRestoreParameters(StepNumber.GENERATE_AUTOMATION_CONFIG_FOR_DIRECT_ATTACH_RESTORE);

    if (!setRestoreParamsResult.getStatus().isDone()) {
      return setRestoreParamsResult;
    }

    // Step 6b: Publish automation config
    final Result<PublishAutomationConfigStep.Data> publishAutomationConfigStepResult =
        publishDirectAttachAutomationConfig(
            setRestoreParamsResult.getData().getAutomationConfig().get(),
            StepNumber.PUBLISH_RESTORE_VERIFICATION_KEY_AUTOMATION_CONFIG);

    if (!publishAutomationConfigStepResult.getStatus().isDone()) {
      return publishAutomationConfigStepResult;
    }

    setAgentRestoreDone();

    if (!allAgentRestoresFinished()) {
      return Result.inProgress();
    }

    // Step 7a: Restart mongo processes, publish automation config
    // Note: The underlying step to enable processes is SetHostProcessesDisabledStep. This step only
    // enables processes for the host/node its operating on and not any others
    final Result<?> enableProcessesAndPublishAutomationConfigResult =
        enableProcessesAndPublishAutomationConfig();
    if (!enableProcessesAndPublishAutomationConfigResult.getStatus().isDone()) {
      return enableProcessesAndPublishAutomationConfigResult;
    }

    // Step 7b: Wait for host goal state
    final Result<NoData> waitForHostGoalStateResult = waitForEnableHostGoalState();

    if (!waitForHostGoalStateResult.getStatus().isDone()) {
      return waitForHostGoalStateResult;
    }

    setInProgressRestoreToComplete();

    return waitForHostGoalStateResult;
  }

  @Override
  public Duration getWaitForAutomationTimeout() {
    return AttemptLimits.WAIT_FOR_AUTOMATION_AGENT;
  }

  @Override
  public Result<?> disableProcessesAndPublishAutomationConfig(String pHostname) {
    // disable processes must complete before we attach a new disk and trigger chef to swap them
    return super.disableProcessesAndPublishAutomationConfig(pHostname, false);
  }

  boolean allAgentRestoresFinished() {
    final List<DirectAttachReplicaSetBackupRestoreJob> directAttachReplicaSetBackupRestoreJobList =
        getAllReplicasetRestoreJobsForCluster();
    return directAttachReplicaSetBackupRestoreJobList.stream()
        .flatMap(job -> job.getAttachStatuses().stream())
        .allMatch(AttachStatus::agentReachedGoalStateForRestore);
  }

  void setAgentRestoreDone() {
    runAtLeastOnce(
        (() -> {
          getLogger()
              .atInfo()
              .setMessage("Agent has finished the restore steps")
              .addKeyValue("instanceId", _instanceId)
              .log();
          DA_RESTORE_SCHEDULED_TO_REBOOT_DURATION
              .labels(CloudProvider.AWS.name())
              .observe(Instant.now().getEpochSecond() - _replicaSetRestoreJobId.getTimestamp());
          _backupRestoreJobDao.setAgentRestoreDone(_replicaSetRestoreJobId, _instanceId);
        }),
        StateFields.AGENT_RESTORE_DONE);
  }

  AutomationConfig clearRestoreConfig() {
    final AutomationConfig config =
        _automationConfigSvc.findPublished(getNDSPlanContext().getGroupId());

    return BackupSnapshotUtils.clearDirectAttachRestoreConfigForCluster(
        config, getClusterDescription());
  }

  Result<SetRestoreParametersStep.Data> setRestoreParameters(final int stepNumber) {
    final DirectAttachReplicaSetBackupRestoreJob restoreJob = getRestoreJob();
    final List<DirectAttachReplicaSetBackupRestoreJob> directAttachReplicaSetBackupRestoreJobList =
        getAllReplicasetRestoreJobsForCluster();
    return new SetRestoreParametersStep(
            getNDSPlanContext(),
            _state.forStep(stepNumber),
            directAttachReplicaSetBackupRestoreJobList,
            getClusterDescription(),
            restoreJob,
            _backupDependenciesProvider,
            _restoreStepUtils)
        .perform();
  }

  private List<DirectAttachReplicaSetBackupRestoreJob> getAllReplicasetRestoreJobsForCluster() {
    final DirectAttachReplicaSetBackupRestoreJob restoreJob = getRestoreJob();
    return _backupRestoreJobDao.findInProgressDirectAttachRestoreJobsByCluster(
        restoreJob.getTarget().getTargetProjectId(), restoreJob.getTarget().getTargetClusterName());
  }

  Result<PublishAutomationConfigStep.Data> publishDirectAttachAutomationConfig(
      final AutomationConfig updatedConfig, final int stepNumber) {
    return getProcessStepsUtil()
        .publishAutomationConfig(
            getNDSPlanContext(), _state.forStep(stepNumber), updatedConfig, true);
  }

  void markRestoreJobFailed() {
    runAtLeastOnce(
        () -> {
          setAttachStatusToFailed();
          setRestoreJobFailed();
        },
        StateFields.MARK_RESTORE_JOB_FAILED);
  }

  void resetMachineShutdown() {
    getLogger()
        .atDebug()
        .setMessage("Resetting machine shutdown")
        .addKeyValue("instanceId", _instanceId)
        .log();
    _backupRestoreJobDao.resetMachineShutdown(_replicaSetRestoreJobId, _instanceId);
  }

  void updateOptimizedDiskTypeAttached(Result<AWSCreateVolumeStep.Data> createVolumeResult) {
    runAtLeastOnce(
        () -> {
          _backupRestoreJobDao.updateOptimizedDiskTypeAttached(
              _replicaSetRestoreJobId,
              _instanceId,
              createVolumeResult.getData().getEBSVolumeType());
        },
        StateFields.SAVE_ATTACHED_DISK_TYPE);
  }

  void setAttachStatusToFailed() {
    _backupRestoreJobDao.setAttachStatusToFailed(_replicaSetRestoreJobId, _instanceId);
  }

  void setRestoreJobFailed() {
    _cpsSvc.setRestoreJobFailedAndCanceledIfNeeded(_replicaSetRestoreJobId);
  }

  protected void setIdleRestoreToInProgress() {
    runAtLeastOnce(
        () ->
            _backupRestoreJobDao.setIdleDirectAttachRestoreToInProgress(
                _replicaSetRestoreJobId, _instanceId),
        StateFields.SET_STATUS_IDLE);
  }

  protected void setInProgressRestoreToComplete() {
    runAtLeastOnce(
        () ->
            DA_RESTORE_SCHEDULED_TO_PROCESS_RESTART_DURATION
                .labels(CloudProvider.AWS.name())
                .observe(Instant.now().getEpochSecond() - _replicaSetRestoreJobId.getTimestamp()),
        StateFields.PROCESS_START_DONE);

    _backupRestoreJobDao.setInProgressDirectAttachRestoreToCompleted(
        _replicaSetRestoreJobId, _instanceId);
  }

  @Override
  protected boolean shouldRollbackAfterPerform() {
    return true;
  }

  protected Result<NoData> waitForEnableHostGoalState() {
    final String hostname = getInstanceHardware().getHostnameForAgents().get();
    return waitForEnableHostGoalState(
        hostname, AWSInstancePowerCycleMove.StepNumber.WAIT_FOR_SECOND_HOST_GOAL_STATE);
  }

  protected AWSBackupSnapshot getSnapshot(final ObjectId snapshotId) {
    return (AWSBackupSnapshot) _backupSnapshotDao.findById(snapshotId).get();
  }

  /**
   * This differs from *ProvisionRestoreMoves in that it doesn't care about whether a region is
   * marked as down, or if snapshot is marked as deleted. That's because for a direct attach to
   * work, we need to attach using a snapshot in the same region as the target node. Therefore, we
   * don't have a choice but to stick with the chosen snapshot that matches direct attach criteria.
   */
  AWSBackupSnapshot getUsableAwsSnapshotForDirectAttach(final ObjectId snapshotId) {
    final AWSBackupSnapshot awsBackupSnapshot =
        (AWSBackupSnapshot) _backupSnapshotDao.findById(snapshotId).get();
    if (awsBackupSnapshot.getCopySnapshotIds().isEmpty()) {
      return awsBackupSnapshot;
    }

    if (awsBackupSnapshot.getRegion().equals(getContainer().getRegion())) {
      return awsBackupSnapshot;
    }

    final List<ReplicaSetBackupSnapshot> copies =
        _backupSnapshotDao.findReplicaSetSnapshotByIds(awsBackupSnapshot.getCopySnapshotIds());

    final AWSBackupSnapshot awsBackupSnapshotCopy =
        (AWSBackupSnapshot)
            copies.stream()
                .filter(c -> c.getRegion().equals(getContainer().getRegion()))
                .findFirst()
                .get();

    getNDSPlanContext()
        .getLogger()
        .atInfo()
        .setMessage("Using copy snapshot for direct attach move")
        .addKeyValue("regionName", getContainer().getRegion())
        .addKeyValue("provider", getContainer().getCloudProvider().name())
        .addKeyValue("snapshotId", awsBackupSnapshot.getId())
        .log();

    return awsBackupSnapshotCopy;
  }

  protected DirectAttachReplicaSetBackupRestoreJob getRestoreJob() {
    return _backupRestoreJobDao.findDirectAttachReplSetJob(_replicaSetRestoreJobId);
  }

  @Override
  protected void initialize() {
    super.initialize();
  }

  protected Result<NoData> waitForEnableHostGoalStateRollBack() {
    try {
      return waitForEnableHostGoalState(
          getInstanceHardware().getHostnameForAgents().get(),
          StepNumber.WAIT_FOR_HOST_GOAL_STATE_AFTER_ROLLBACK);
    } catch (final PlanAbandonedException pE) {
      getLogger()
          .atWarn()
          .setMessage(
              "Waiting on goal state for rollback caught abandon exception: "
                  + "("
                  + pE.getErrorCode()
                  + "). Considering rollback complete.")
          .log();
      return Result.done();
    }
  }

  void updateInstanceEbsVolumeIdForRollback() {
    final Optional<String> oldVolId = getOldVolumeId();
    if (oldVolId.isPresent()) {
      runAtLeastOnce(
          () -> updateInstanceEbsVolumeId(oldVolId.get()), StateFields.ROLLBACK_EBS_VOLUME_ID);
    }
    final Optional<String> oldVolCmkKey = getOldVolumeKey();
    if (oldVolCmkKey.isPresent()) {
      runAtLeastOnce(
          () -> updateInstanceHardwareKeyInfo(oldVolCmkKey.get()),
          StateFields.ROLLBACK_EBS_VOLUME_CMK_KEY);
    }
    final Optional<String> oldDeviceName = getOldDeviceName();
    if (oldDeviceName.isPresent()) {
      runAtLeastOnce(
          () -> {
            /*
            It is important that the device name used for the desired attached disk matched the one
            in the chef config and the instance fstab. We rely on whether chef completed the disk
            swap or not to determine what the right device name is. If chef completed (including
            swaping disks) then the device name should be that of the new disk even though we are
            rolling back using the old disk.
             */
            final String deviceName =
                getChefCalledBack() ? getNewDeviceName().get() : oldDeviceName.get();

            /*
            We want DB fields for device name in the instance hardware to match that of the
            chef config and instance fstab
            */
            updateInstanceHardwareDeviceName(deviceName);

            /*
            Reinitialize here to refresh instance hardware local variable. This is extremely
            important as it is used by the AWSDirectAttachVolumeStep.getNewDeviceName
            function to re-attach the old disk with the correct device name.
            */
            initialize();
          },
          StateFields.ROLLBACK_INSTANCE_HARDWARE_DEVICE_NAME);
    }
  }

  void updateInstanceEbsVolumeIdForPerform() {
    runAtLeastOnce(
        () -> updateInstanceEbsVolumeId(getState().getStringValue(StateFields.NEW_VOLUME_ID).get()),
        StateFields.UPDATE_EBS_VOLUME_ID);
    runAtLeastOnce(
        () -> updateInstanceHardwareDeviceName(getNewDeviceName().get()),
        StateFields.UPDATE_INSTANCE_HARDWARE_DEVICE_NAME);
    runAtLeastOnce(
        () ->
            updateInstanceHardwareKeyInfo(
                getState().getStringValue(StateFields.NEW_VOLUME_KEY).get()),
        StateFields.UPDATE_EBS_VOLUME_CMK_KEY);
  }

  protected void updateInstanceHardwareKeyInfo(String encryptionKey) {
    getInstanceHardwareDao()
        .updateEbsVolumeKey(
            getReplicaSetHardware().getId(),
            _instanceId,
            false,
            false,
            encryptionKey.equals(KMS_KEY_ID) ? null : encryptionKey);
  }

  protected void updateInstanceEbsVolumeId(final String volumeId) {
    getInstanceHardwareDao()
        .updateEbsVolumeID(
            getReplicaSetHardware().getId(),
            getInstanceHardware().getInstanceId(),
            false,
            volumeId);
  }

  protected void updateInstanceHardwareDeviceName(final String deviceName) {
    getInstanceHardwareDao()
        .updateDeviceName(
            getReplicaSetHardware().getId(),
            getInstanceHardware().getInstanceId(),
            false,
            deviceName);
  }

  private void setNewDeviceName(final String newDeviceName) {
    runAtLeastOnce(
        () -> {
          getState().setValue(StateFields.NEW_DEVICE_NAME, newDeviceName);
        },
        StateFields.SAVE_NEW_DISK_DEVICE_NAME);
  }

  protected Result<NoData> detachEbsVolumeForPerform() {
    final ClusterDescription clusterDescription = getClusterDescription();
    final String oldDiskDeviceName = getOldDeviceName().get();
    final String oldEbsVolumeId = getOldVolumeId().get();
    final String ec2InstanceId = getInstanceHardware().getEC2InstanceId().get();

    final Result<NoData> result =
        new AWSDetachVolumeStep(
                getNDSPlanContext(),
                _state.forStep(StepNumber.DETACH_VOLUME),
                getContainer(),
                getReplicaSetHardware(),
                getInstanceHardware(),
                ec2InstanceId,
                oldDiskDeviceName,
                oldEbsVolumeId,
                clusterDescription,
                getAWSApiSvc())
            .perform();

    getNDSPlanContext()
        .getLogger()
        .atInfo()
        .setMessage("Detaching ebs volume from instance")
        .addKeyValue("oldEbsVolumeId", oldEbsVolumeId)
        .addKeyValue("ec2InstanceId", ec2InstanceId)
        .addKeyValue("status", result.getStatus().toString())
        .log();

    if (result.getStatus().isDone()) {
      runAtLeastOnce(
          () ->
              _backupRestoreJobDao.setDetachedDiskId(
                  _replicaSetRestoreJobId, _instanceId, oldEbsVolumeId),
          StateFields.DETACHED_DISK_ID_PERSISTED);
    }

    return result;
  }

  protected Result<NoData> orphanNewVolumeForRollback() {
    final CpsRestoreMetadata restoreJob = getRestoreJob().getMetadata();
    final boolean isOptimizedDirectAttachEnabled = restoreJob.isAwsDirectAttachWithIo2Enabled();

    if (!isOptimizedDirectAttachEnabled) {
      // If optimization was never enabled, only rollback the standard step
      return getAWSCreateVolumeFromSnapshotStepForRollback(StepNumber.CREATE_VOLUME_FROM_SNAPSHOT)
          .rollback();
    }

    // Try rolling back all possible volume creation steps
    // For example, volumes can be created successfully from the cloud provider but failed in later
    // phase of the step, we need to roll back the volume in such case
    final Result<?>[] results =
        Arrays.stream(CREATE_OPTIMIZED_VOLUME_STEP_NUMBERS)
            .mapToObj(
                stepNumber -> getAWSCreateVolumeFromSnapshotStepForRollback(stepNumber).rollback())
            .toArray(Result[]::new);
    return awaitAllNotInProgress(results);
  }

  protected Result<NoData> orphanOldVolumeForRollback() {
    final Optional<String> oldVolumeId = getOldVolumeId();
    if (oldVolumeId.isEmpty()) {
      return Result.done();
    }
    return new AWSOrphanVolumeStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.ORPHAN_OLD_VOLUME),
            _ndsOrphanedItemDao,
            getContainer(),
            oldVolumeId.get(),
            getReplicaSetHardware(),
            getClusterDescription(),
            OrphanStrategy.UseClusterCreationDate,
            getAWSApiSvc(),
            getInstanceHardware(),
            new Date())
        .perform();
  }

  protected Result<?> rollbackDetachVolume() {
    final boolean chefCalledBack = getChefCalledBack();
    /*
    early done: no need to rollback if chef never ran and the chef config and fstab was never updated.
    If we reached this state the old disk was never detached and it matches the original and current
    chef config and fstab. No need to rollback a detach step
     */
    if (!chefCalledBack) {
      return Result.done();
    }

    final String oldEbsVolumeId = getOldVolumeId().get();
    final String ec2InstanceId = getInstanceHardware().getEC2InstanceId().get();
    final String oldDataDeviceName = getOldDeviceName().get();

    final ClusterDescription clusterDescription = getClusterDescription();

    final AWSDetachVolumeStep awsDetachVolumeStepWithOldDeviceName =
        getDetachVolumeStep(ec2InstanceId, oldDataDeviceName, oldEbsVolumeId, clusterDescription);

    /*
    If chef ran and completed a disk swap that means that we updated the chef config and fstab with
    the new device name. Therefore, whatever disk we attach has to match that. If the old disk was
    never detached we still need to force it to detach (since it's device name likely does not match
    whats in chef config and fstab) and then reattach it with the right device name.

    AWSDetachVolumeStep takes in the device name as an argument. So when performing the detach well
    use the old device name. When re-attaching it (see below) we need to use the new device name.
     */
    if (!awsDetachVolumeStepWithOldDeviceName.isPerformed()) {
      Result<?> detatchOldDiskStepResult = awsDetachVolumeStepWithOldDeviceName.perform();
      if (!detatchOldDiskStepResult.getStatus().isDone()) {
        return detatchOldDiskStepResult;
      }
    }

    /*
    At this point the old disk should be detached and we can now re-attach it with the right device
    name matching the chef config and fstab
     */
    final String newDeviceName = getNewDeviceName().get();
    final AWSDetachVolumeStep awsDetachVolumeStepWithNewDeviceName =
        getDetachVolumeStep(ec2InstanceId, newDeviceName, oldEbsVolumeId, clusterDescription);
    return awsDetachVolumeStepWithNewDeviceName.rollback();
  }

  AWSDetachVolumeStep getDetachVolumeStep(
      final String ec2InstanceId,
      final String dataDeviceName,
      final String ebsVolumeId,
      final ClusterDescription clusterDescription) {
    return new AWSDetachVolumeStep(
        getNDSPlanContext(),
        _state.forStep(StepNumber.DETACH_VOLUME),
        getContainer(),
        getReplicaSetHardware(),
        getInstanceHardware(),
        ec2InstanceId,
        dataDeviceName,
        ebsVolumeId,
        clusterDescription,
        getAWSApiSvc());
  }

  Result<Data> attachVolume(
      final Result<AWSCreateVolumeStep.Data> createEbsVolumeWithSnapshotDataResult) {
    final String ebsVolumeId = createEbsVolumeWithSnapshotDataResult.getData().getEBSVolumeId();
    final String ec2InstanceId = getInstanceHardware().getEC2InstanceId().get();

    final Result<Data> result =
        new AWSAttachVolumeStep(
                getNDSPlanContext(),
                _state.forStep(StepNumber.ATTACH_VOLUME),
                getAWSApiSvc(),
                getContainer(),
                getReplicaSetHardware(),
                getInstanceHardware(),
                ec2InstanceId,
                ebsVolumeId,
                getClusterDescription())
            .perform();

    getNDSPlanContext()
        .getLogger()
        .atInfo()
        .setMessage("Attaching volume to instance")
        .addKeyValue("ebsVolumeId", ebsVolumeId)
        .addKeyValue("ec2InstanceId", ec2InstanceId)
        .addKeyValue("status", result.getStatus().toString())
        .log();

    if (result.getStatus().isDone()) {
      runAtLeastOnce(
          () ->
              _backupRestoreJobDao.setDiskAttachedtime(
                  _replicaSetRestoreJobId, _instanceId, new Date()),
          StateFields.SET_ATTACHED_TIME);
    }

    return result;
  }

  Result<?> rollbackAttachVolume() {
    // early done: if the new volume was never created - the new volume was never attached
    final Optional<String> ebsVolumeIdOpt = getNewVolumeId();

    if (ebsVolumeIdOpt.isEmpty()) {
      return Result.done();
    }

    final String ebsVolumeId = ebsVolumeIdOpt.get();
    final String ec2InstanceId = getInstanceHardware().getEC2InstanceId().get();

    final AWSAttachVolumeStep awsAttachVolumeStep =
        getAttachVolumeStep(StepNumber.ATTACH_VOLUME, ec2InstanceId, ebsVolumeId);

    // If the attach step hasn't started we are done.
    if (!awsAttachVolumeStep.hasPerformStarted()) {
      return Result.done();
    }

    // if the move has been started but has not completed then finish attaching the volume first.
    if (awsAttachVolumeStep.hasPerformStarted() && !awsAttachVolumeStep.isPerformed()) {
      final Result<Data> attachResult = awsAttachVolumeStep.perform();

      if (attachResult.getStatus().isFailed()) {
        // disk was never attached, nothing to rollback
        return Result.done();
      }

      getNDSPlanContext()
          .getLogger()
          .atInfo()
          .setMessage("Finishing attaching volume to instance during rollback")
          .addKeyValue("ebsVolumeId", ebsVolumeId)
          .addKeyValue("ec2InstanceId", ec2InstanceId)
          .addKeyValue("status", attachResult.getStatus().toString())
          .log();

      // return if this attach step is still in progress; proceed with rollback otherwise
      if (!attachResult.getStatus().isDone()) {
        return attachResult;
      }
      setNewDeviceName(attachResult.getData().getDeviceName());
    }

    // if the volume has been attached successfully - proceed to rolling back attaching the new
    // volume
    final Result<NoData> rollbackResult = awsAttachVolumeStep.rollback();
    getNDSPlanContext()
        .getLogger()
        .atInfo()
        .setMessage("Detaching ebs volume from instance for rollback")
        .addKeyValue("ebsVolumeId", ebsVolumeId)
        .addKeyValue("ec2InstanceId", ec2InstanceId)
        .addKeyValue("status", rollbackResult.getStatus().toString())
        .log();
    return rollbackResult;
  }

  AWSAttachVolumeStep getAttachVolumeStep(
      final int stepNumber, final String ec2InstanceId, final String ebsVolumeId) {
    return new AWSAttachVolumeStep(
        getNDSPlanContext(),
        _state.forStep(stepNumber),
        getAWSApiSvc(),
        getContainer(),
        getReplicaSetHardware(),
        getInstanceHardware(),
        ec2InstanceId,
        ebsVolumeId,
        getClusterDescription());
  }

  protected Result<NoData> rollbackStopInstance() {
    final AWSInstanceSize instanceSize = getInstanceHardware().getNDSInstanceSize().orElseThrow();
    final String hostname = getInstanceHardware().getHostnameForAgents().get();
    final String ec2InstanceId = getInstanceHardware().getEC2InstanceId().get();

    return rollbackStopInstance(
        getContainer(),
        ec2InstanceId,
        instanceSize,
        hostname,
        AWSInstancePowerCycleMove.StepNumber.STOP_INSTANCE);
  }

  /**
   * Creates a new volume from snapshot with retry logic: io2 -> io1 -> default strategy
   *
   * @return Result containing the created volume data
   */
  Result<AWSCreateVolumeStep.Data> createEbsVolumeWithSnapshotData() {
    final CpsRestoreMetadata restoreJob = getRestoreJob().getMetadata();
    final ObjectId snapshotId = restoreJob.getSnapshotId();
    final AWSBackupSnapshot snapshot = getUsableAwsSnapshotForDirectAttach(snapshotId);
    final boolean isOptimizedDirectAttachEnabled = restoreJob.isAwsDirectAttachWithIo2Enabled();

    // Share snapshot to the target cluster if needed
    shareSnapshotIfNeed(snapshot);

    final VolumeType instanceVolumeType = BackupSnapshotUtils.getVolumeType(getInstanceHardware());

    if (isOptimizedDirectAttachEnabled) {
      return createOptimizedVolume(snapshot, instanceVolumeType);
    } else {
      return createStandardVolume(snapshot);
    }
  }

  private Result<AWSCreateVolumeStep.Data> createOptimizedVolume(
      final AWSBackupSnapshot snapshot, final VolumeType instanceVolumeType) {

    final AWSRegionName awsRegionName = getContainer().getRegion();

    // Try volume types in order: io2 -> io1
    for (final VolumeType volumeType : RESTORE_VOLUME_TYPES_FOR_OPTIMIZED_DA) {
      if (!isVolumeTypeAtLeast(volumeType, instanceVolumeType)) {
        // Skip volume types that are less than the instance's volume type
        break;
      }

      if (volumeType == VolumeType.Io2 && !awsRegionName.supportsIo2Volumes()) {
        getLogger()
            .atInfo()
            .setMessage("Skipping io2 volume creation as the region does not support it")
            .addKeyValue("region", awsRegionName)
            .log();
        continue;
      }

      Result<AWSCreateVolumeStep.Data> result =
          tryCreateOptimizedVolumeWithType(snapshot, volumeType);
      if (!result.getStatus().isFailed()) {
        return result;
      }
      // continue to fallback to the next volume type
    }

    // If all optimized types fail, fallback to standard volume creation
    return createStandardVolume(snapshot);
  }

  private Result<AWSCreateVolumeStep.Data> tryCreateOptimizedVolumeWithType(
      final AWSBackupSnapshot snapshot, final VolumeType volumeType) {

    AWSCreateVolumeFromSnapshotStep step = null;
    try {
      step = getAWSCreateVolumeFromSnapshotStep(snapshot, true, volumeType);

      // Don't perform the step if rollback has already started
      if (step.hasRollbackStarted()) {
        getLogger()
            .atWarn()
            .setMessage("Step rollback already started, skipping perform create volume step")
            .addKeyValue("volumeType", volumeType)
            .addKeyValue("isRolledBack", step.isRolledBack())
            .log();
        return Result.failed(); // Try next volume type
      }

      Result<AWSCreateVolumeStep.Data> result = step.perform();
      if (result.getStatus().isDone()) {
        updateOptimizedDiskTypeAttached(result);
      } else if (result.getStatus().isFailed() && step.getEBSVolumeIdFromState().isPresent()) {
        rollbackOptimizedVolumeCreateStep(step, volumeType, null);
      }
      return result;
    } catch (Exception exception) {
      if (step != null
          && step.getEBSVolumeIdFromState().isPresent()
          && !step.hasRollbackStarted()) {
        rollbackOptimizedVolumeCreateStep(step, volumeType, exception);
      }
      return Result.failed(); // Try next volume type
    }
  }

  @VisibleForTesting
  void rollbackOptimizedVolumeCreateStep(
      AWSCreateVolumeFromSnapshotStep step, VolumeType volumeType, Exception exception) {
    // If the volume was created but failed in later phase, we need to roll back the volume
    getLogger()
        .atWarn()
        .setMessage("Failed to create volume with optimized type")
        .addKeyValue("volumeType", volumeType)
        .setCause(exception)
        .log();
    step.rollback();
  }

  private Result<AWSCreateVolumeStep.Data> createStandardVolume(final AWSBackupSnapshot snapshot) {
    AWSCreateVolumeFromSnapshotStep step =
        getAWSCreateVolumeFromSnapshotStep(snapshot, false, null);
    return step.perform();
  }

  @VisibleForTesting
  void shareSnapshotIfNeed(AWSBackupSnapshot snapshot) {
    runAtLeastOnce(
        () ->
            new AWSShareSnapshotStep(
                    getNDSPlanContext(),
                    getState().forStep(StepNumber.SHARE_SNAPSHOT),
                    snapshot.getAwsAccountId(),
                    snapshot.getRegion(),
                    getContainer().getAWSAccountId(),
                    snapshot.getEbsSnapshotId(),
                    getAWSApiSvc())
                .perform(),
        StateFields.SHARE_SNAPSHOT_DONE);
  }

  /**
   * Get AWS create volume step for volume type
   *
   * @param snapshot The snapshot to create volume from
   * @param isAwsDirectAttachWithIo2Enabled Whether io2 optimization is enabled by config
   * @param forceVolumeType Force a specific volume type for this attempt
   * @return The create volume step with appropriate parameters
   */
  AWSCreateVolumeFromSnapshotStep getAWSCreateVolumeFromSnapshotStep(
      final AWSBackupSnapshot snapshot,
      final boolean isAwsDirectAttachWithIo2Enabled,
      final VolumeType forceVolumeType) {

    final AWSInstanceHardware awsInstanceHardware = getInstanceHardware();
    final String oldSubnet = awsInstanceHardware.getSubnetId().orElseThrow();
    final int diskSize = _cpsSvc.getUpgradeDiskSize(awsInstanceHardware, snapshot);

    // skip the dedicated config shard
    final boolean useOptimizedDirectAttach =
        isAwsDirectAttachWithIo2Enabled && getReplicaSetHardware().containsShardData();

    final AWSRegionName awsRegionName = getContainer().getRegion();

    if (forceVolumeType == VolumeType.Io2 && !awsRegionName.supportsIo2Volumes()) {
      throw new IllegalStateException(
          "Attempting to create an io2 volume in a region that does not support it: "
              + awsRegionName);
    }

    final VolumeType volumeType =
        useOptimizedDirectAttach
            ? forceVolumeType
            : BackupSnapshotUtils.getVolumeType(awsInstanceHardware);

    final Integer diskIops =
        useOptimizedDirectAttach
            ? BackupSnapshotUtils.getOptimizedAwsDirectAttachDiskIOPS(
                volumeType, diskSize, awsInstanceHardware)
            : BackupSnapshotUtils.getIopsForAwsDirectAttach(awsInstanceHardware, diskSize);

    final Integer diskThroughput =
        volumeType == VolumeType.Gp3
            ? BackupSnapshotUtils.getThroughputForAwsDirectAttach(awsInstanceHardware, diskSize)
            : null;

    final AWSCreateVolumeFromSnapshotStep step =
        new AWSCreateVolumeFromSnapshotStep(
            getNDSPlanContext(),
            getState().forStep(StepNumber.CREATE_VOLUME_FROM_SNAPSHOT),
            getContainer(),
            awsInstanceHardware,
            getClusterDescription(),
            oldSubnet,
            snapshot.getEbsSnapshotId(),
            getVolumeEncrypted(),
            diskSize,
            diskIops,
            diskThroughput,
            volumeType,
            getAWSApiSvc(),
            _ndsOrphanedItemDao,
            _backupDependenciesProvider.getGroupDao(),
            OrphanStrategy.UseVolumeCreationDateFirst,
            ResourceOperationTagType.NONE,
            _cpsSvc.isRestoreCrossProjectAwsNewCmkEnabled(
                getGroupDao().findById(getNDSPlanContext().getGroupId())));

    if (!step.isPerformed()) {
      getNDSPlanContext()
          .getLogger()
          .atInfo()
          .setMessage("Attempt to create EBS volume")
          .addKeyValue("volumeType", volumeType)
          .addKeyValue("diskIOPS", diskIops)
          .addKeyValue("diskThroughput", diskThroughput == null ? "N/A" : diskThroughput)
          .log();
    }

    return step;
  }

  /**
   * Check if a volume type is at least the given minimum type. Order: io2 > io1 > gp3
   *
   * @param volumeType The volume type to check
   * @param minType The minimum volume type
   * @return true if instanceType is at least minType
   */
  private boolean isVolumeTypeAtLeast(VolumeType volumeType, VolumeType minType) {
    Integer volumeRank = VOLUME_TYPE_ORDER_FOR_OPTIMIZED_DA.getOrDefault(volumeType, 0);
    Integer minRank = VOLUME_TYPE_ORDER_FOR_OPTIMIZED_DA.getOrDefault(minType, 0);
    return volumeRank >= minRank;
  }

  AWSCreateVolumeFromSnapshotStep getAWSCreateVolumeFromSnapshotStepForRollback(
      final int stepNumber) {
    return new AWSCreateVolumeFromSnapshotStep(
        getNDSPlanContext(),
        getState().forStep(stepNumber),
        getContainer(),
        null,
        getClusterDescription(),
        null,
        null,
        getVolumeEncrypted(),
        null,
        null,
        null,
        null,
        getAWSApiSvc(),
        _ndsOrphanedItemDao,
        _backupDependenciesProvider.getGroupDao(),
        OrphanStrategy.UseVolumeCreationDateFirst,
        ResourceOperationTagType.NONE,
        _cpsSvc.isRestoreCrossProjectAwsNewCmkEnabled(
            getGroupDao().findById(getNDSPlanContext().getGroupId())));
  }

  @VisibleForTesting
  boolean getVolumeEncrypted() {
    final ClusterDescription clusterDescription = getClusterDescription();
    // By default, a cluster is encrypted right now
    if (clusterDescription == null) return true;

    return clusterDescription
        .getHardwareSpecsForProvider(CloudProvider.AWS, NodeType.ELECTABLE)
        .stream()
        .filter(AWSHardwareSpec.class::isInstance)
        .map(AWSHardwareSpec.class::cast)
        .allMatch(AWSHardwareSpec::getEncryptEBSVolume);
  }

  protected Result<NoData> updateChefConfigAndWaitForChef(final String deviceName) {
    final Result<NoData> updateChefConfigResult = updateChefConfig(deviceName);
    if (!updateChefConfigResult.getStatus().isDone()) {
      return updateChefConfigResult;
    }

    // hasten chef via the atlas-node-data trigger mechanism
    final Result<NoData> startAtlasNodeDataResult = startAtlasNodeDataJob(getInstanceHardware());
    if (startAtlasNodeDataResult.getStatus().isFailed()) {
      // We can just wait for the instance timer to trigger chef.
      getLogger()
          .atInfo()
          .setMessage("Failed to start atlas-node-data but proceeding anyway")
          .log();
    } else if (startAtlasNodeDataResult.getStatus().isInProgress()) {
      return startAtlasNodeDataResult;
    }

    final Result<NoData> waitForChefResult = waitForChef();
    return waitForChefResult;
  }

  Result<NoData> startAtlasNodeDataJob(AWSInstanceHardware instanceHardware) {
    return Result.downgradeToNoData(
        executeAgentJob(getStartAtlasNodeDataJob(instanceHardware.getInstanceId())));
  }

  Result<ObjectId> executeAgentJob(final AgentJob job) {
    return new ExecuteAutomationAgentJobStep(
            getContext(),
            getState().forStep(StepNumber.TRIGGER_ATLAS_NODE_DATA),
            job,
            1,
            Duration.ofMinutes(1),
            _agentJobSvc)
        .perform();
  }

  private AgentJob getStartAtlasNodeDataJob(ObjectId instanceId) {
    final String serializedAgentJob = StateFields.SERIALIZED_AGENT_JOB_PREFIX + instanceId;
    final Optional<Object> jobDoc = getState().getValue(serializedAgentJob);
    if (jobDoc.isPresent()) {
      try {
        return AgentJob.fromDBObject((BasicDBObject) jobDoc.get());
      } catch (final ClassNotFoundException e) {
        throw new RuntimeException(e);
      }
    }
    final AgentJob job =
        StartAtlasNodeDataJob.create(
            getNDSPlanContext().getGroupId(),
            getInstanceHardware().getHostnameForAgents().get(),
            false);

    getState().setValue(serializedAgentJob, job.toDBObject());

    return job;
  }

  protected Result<NoData> waitForChef() {
    final int stepNumber = StepNumber.WAIT_FOR_CHEF;
    return new WaitForChefCallbackStep(
            getNDSPlanContext(),
            _state.forStep(stepNumber),
            _ndsClusterSvc,
            _chefCallbackDao,
            getState().getStringValue(StateFields.CHEF_CALLBACK_ID).get(),
            getClusterDescription().getName(),
            GlobalAttemptLimits.WAIT_FOR_CHEF_DIRECT_ATTACH_RESTORE_DURATION)
        .perform();
  }

  protected String getCallbackId() {
    final Optional<String> callbackIdOption =
        getState().getStringValue(StateFields.CHEF_CALLBACK_ID);
    final String callbackId;
    // Make sure we only create one callbackId per move
    if (callbackIdOption.isPresent()) {
      callbackId = callbackIdOption.get();
    } else {
      callbackId =
          _chefCallbackDao.create(
              getNDSPlanContext().getGroupId(),
              getNDSPlanContext().getPlanId(),
              this.getClass().getSimpleName());
      getState().setValue(StateFields.CHEF_CALLBACK_ID, callbackId);
    }
    return callbackId;
  }

  public String getChefCallbackUrl(final String pChefCallbackId) {
    return getNDSPlanContext().getAppSettings().getCentralUrl()
        + "/nds/chefcallback/"
        + pChefCallbackId;
  }

  protected Result<NoData> updateChefConfig(final String pNewDeviceName) {
    if (chefConfigUpdated()) {
      getLogger().atInfo().setMessage("Instance chef config already updated").log();
      return Result.done();
    }

    getLogger()
        .atInfo()
        .setMessage("Updating chef config on instance")
        .addKeyValue("instanceId", _instanceId)
        .log();
    final String callbackId = getCallbackId();
    final String chefCallbackUrl = getChefCallbackUrl(callbackId);
    final String userData = updateChefConfig(chefCallbackUrl, pNewDeviceName);

    setChefConfigUpdated();

    return Result.done();
  }

  protected String updateChefConfig(final String pChefCallbackUrl, final String pNewDeviceName) {
    // Make sure we only create one cloud chef conf per move
    final String chefConfig;
    final Optional<String> cloudChefConfIdOpt = getCloudChefConfId();
    final NDSGroup ndsGroup =
        _ndsGroupDao
            .find(getNDSPlanContext().getGroupId())
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        String.format(
                            "No NDS Group found for id (%s)", getNDSPlanContext().getGroupId())));
    final Group group = _groupDao.findById(ndsGroup.getGroupId());

    if (cloudChefConfIdOpt.isPresent()) {
      chefConfig =
          _cloudChefConfSvc.getUserData(
              cloudChefConfIdOpt.get(), getNDSPlanContext().getAppSettings().getAgentCentralUrl());
    } else {
      final String hash =
          _ndsRemoteImageSvc.getPolicyFileHashForImageVersion(
              getClusterDescription().getOSPolicyVersion().orElse(null),
              getInstanceHardware().getOS().orElse(null),
              getLogger());

      final Builder<?> cloudChefConfBuilder =
          CloudChefConfUtil.buildInstanceCloudChefConf(
              ndsGroup,
              getClusterDescription(),
              getReplicaSetHardware(),
              getInstanceHardware(),
              hash)
              .toBuilder();
      cloudChefConfBuilder.dataDevice(pNewDeviceName);
      cloudChefConfBuilder.chefCallbackUrl(pChefCallbackUrl);
      cloudChefConfBuilder.pushBasedLogExportConfig(
          _cloudChefConfSvc.generatePushBasedLogExportConfig(getNDSPlanContext().getGroupId()));
      cloudChefConfBuilder.gatewayRouterBootstrapConfig(
          _cloudChefConfSvc.generateGatewayRouterBootstrapConfig(getClusterDescription()));
      final CloudChefConf cloudChefConf = cloudChefConfBuilder.build();

      try {
        _cloudChefConfSvc.saveCloudChefConf(cloudChefConf);
      } catch (final SvcException e) {
        throw new RuntimeException("Failed to Validate CloudChefConf: " + e.getMessage(), e);
      }

      chefConfig =
          _cloudChefConfSvc.getUserData(
              cloudChefConf.getId(), getNDSPlanContext().getAppSettings().getAgentCentralUrl());

      getState().setValue(StateFields.CLOUD_CHEF_CONF_ID, cloudChefConf.getId());
    }
    return chefConfig;
  }

  private Result<?> ensureChefNotInProgress() {
    // If chef config was never updated or chef called back and completed, we have nothing to wait
    // for
    if (!chefConfigUpdated() || getChefCalledBack()) {
      return Result.done();
    }

    final ExecuteAutomationAgentJobStep executeAutomationAgentJobStep =
        new ExecuteAutomationAgentJobStep(
            getContext(),
            getState().forStep(StepNumber.TRIGGER_ATLAS_NODE_DATA),
            getStartAtlasNodeDataJob(getInstanceHardware().getInstanceId()),
            1,
            Duration.ofMinutes(1),
            _agentJobSvc);

    // If we never triggered the agent job to hasten chef then we have nothing to wait for
    if (!executeAutomationAgentJobStep.hasPerformStarted()) {
      return Result.done();
    }

    if (!executeAutomationAgentJobStep.isPerformed()) {
      final Result<?> hastenChefResult = executeAutomationAgentJobStep.perform();
      if (!hastenChefResult.getStatus().isDone()) {
        return hastenChefResult;
      }
    }

    final WaitForChefCallbackStep waitForChefCallbackStep =
        new WaitForChefCallbackStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.WAIT_FOR_CHEF),
            _ndsClusterSvc,
            _chefCallbackDao,
            getState().getStringValue(StateFields.CHEF_CALLBACK_ID).get(),
            getClusterDescription().getName(),
            GlobalAttemptLimits.WAIT_FOR_CHEF_DIRECT_ATTACH_RESTORE_DURATION);

    if (!waitForChefCallbackStep.isPerformed()) {
      final Result<?> waitForChefCallbackResult = waitForChefCallbackStep.perform();
      if (!waitForChefCallbackResult.getStatus().isDone()) {
        return waitForChefCallbackResult;
      }
    }
    setChefCalledBack();
    return Result.done();
  }

  private Result<?> rollbackGeneratedChefConfig() {
    /*
    If somehow chef never called back AND the chef conf was updated then we need to invalidate that
    chef config. This might happen if some failure happened after the chef config was updated but
    before the agent job to trigger chef was initiated.
     */
    if (!getChefCalledBack() && getCloudChefConfId().isPresent()) {
      _cloudChefConfSvc.invalidate(getCloudChefConfId().get());
    }
    return Result.done();
  }

  protected void saveNewVolumeState(final Result<AWSCreateVolumeStep.Data> result) {
    runAtLeastOnce(
        () -> {
          final AWSCreateVolumeStep.Data data = result.getData();
          getState()
              .setValue(
                  AWSDirectAttachRestoreMove.StateFields.NEW_VOLUME_ID, data.getEBSVolumeId());
        },
        StateFields.SAVE_NEW_VOLUME_ID);
  }

  protected void saveNewVolumeKeyState() {
    runAtLeastOnce(
        () -> {
          final String awsKmsKeyAliasArnString =
              _cpsSvc.isRestoreCrossProjectAwsNewCmkEnabled(getNDSPlanContext().getGroupId())
                  ? _awsApiSvc.generateCmk(
                      getContainer().getRegion(), getContainer().getAWSAccountId())
                  : getOldVolumeKey().get();
          getState().setValue(StateFields.NEW_VOLUME_KEY, awsKmsKeyAliasArnString);
        },
        StateFields.SAVE_NEW_VOLUME_KEY);
  }

  protected void saveOldVolumeData() {
    runAtLeastOnce(
        () -> {
          final String oldVolumeId = getInstanceHardware().getNVMePrioritizedEBSId().get();
          getState().setValue(StateFields.OLD_VOLUME_ID, oldVolumeId);

          final String oldDeviceName = getInstanceHardware().getDataDeviceName().get();
          getState().setValue(StateFields.OLD_DEVICE_NAME, oldDeviceName);
        },
        StateFields.SET_OLD_VOLUME_DATA);
  }

  protected void saveOldVolumeKey() {
    final String oldVolumeKey =
        getInstanceHardware().getNVMePrioritizedEBSVolumeEncryptionKey().isEmpty()
            ? KMS_KEY_ID
            : getInstanceHardware().getNVMePrioritizedEBSVolumeEncryptionKey().get();
    getState().setValue(StateFields.OLD_VOLUME_KEY, oldVolumeKey);
  }

  protected void setChefConfigUpdated() {
    getState().setValue(StateFields.CHEF_CONFIG_UPDATED, true);
  }

  protected boolean allDiskSwapsCompleted() {
    final DirectAttachReplicaSetBackupRestoreJob restoreJob =
        _backupRestoreJobDao.findDirectAttachReplSetJob(_replicaSetRestoreJobId);

    return restoreJob.getAttachStatuses().stream().allMatch(AttachStatus::getDiskSwapCompleted);
  }

  protected void setChefCalledBack() {
    runAtLeastOnce(
        (() -> {
          getState().setValue(StateFields.CHEF_CALLED_BACK, true);
          getLogger()
              .atDebug()
              .setMessage("Chef called back. Disk swap completed ")
              .addKeyValue("instanceId", _instanceId)
              .log();
          _backupRestoreJobDao.setDiskSwapCompleted(_replicaSetRestoreJobId, _instanceId);
        }),
        StateFields.DISK_SWAP_COMPLETED);
  }

  Optional<String> getOldVolumeId() {
    return getState().getStringValue(StateFields.OLD_VOLUME_ID);
  }

  Optional<String> getNewVolumeId() {
    return getState().getStringValue(StateFields.NEW_VOLUME_ID);
  }

  Optional<String> getOldVolumeKey() {
    return getState().getStringValue(StateFields.OLD_VOLUME_KEY);
  }

  Optional<String> getNewDeviceName() {
    return getState().getValue(StateFields.NEW_DEVICE_NAME).map(String.class::cast);
  }

  Optional<String> getOldDeviceName() {
    return getState().getValue(StateFields.OLD_DEVICE_NAME).map(String.class::cast);
  }

  Optional<String> getCloudChefConfId() {
    return getState().getStringValue(StateFields.CLOUD_CHEF_CONF_ID);
  }

  boolean chefConfigUpdated() {
    return getState().getValue(StateFields.CHEF_CONFIG_UPDATED).isPresent();
  }

  boolean getChefCalledBack() {
    return getState().getValue(StateFields.CHEF_CALLED_BACK).isPresent();
  }

  @Override
  protected Duration getEnableHostInGoalStateAttemptLimit() {
    return GlobalAttemptLimits.ENABLE_HOST_IN_GOAL_STATE_DIRECT_ATTACH_RESTORE;
  }

  @Override
  public Object[] getArguments() {
    return new Object[] {_clusterName, _instanceId, _replicaSetRestoreJobId};
  }

  private static class AttemptLimits {
    public static final Duration WAIT_FOR_AUTOMATION_AGENT = Duration.ofHours(1);
  }

  protected static class StateFields {

    public static final String SET_STATUS_IDLE = "setStatusIdle";
    public static final String OLD_VOLUME_ID = "oldVolumeId";
    public static final String NEW_VOLUME_ID = "newVolumeId";
    public static final String SAVE_NEW_VOLUME_ID = "saveNewVolumeId";
    public static final String OLD_VOLUME_KEY = "oldVolumeKey";
    public static final String NEW_VOLUME_KEY = "newVolumeKey";
    public static final String SAVE_NEW_VOLUME_KEY = "saveNewVolumeKey";
    public static final String SET_ATTACHED_TIME = "setAttachedTime";
    public static final String DETACHED_DISK_ID_PERSISTED = "setDetachedDiskName";
    public static final String SET_OLD_VOLUME_DATA = "setOldVolumeData";
    public static final String MARK_RESTORE_JOB_FAILED = "markRestoreJobFailed";
    public static final String AGENT_RESTORE_DONE = "agentRestoreDone";
    public static final String PROCESS_START_DONE = "processStartDone";
    public static final String SHARE_SNAPSHOT_DONE = "shareSnapshotDone";
    public static final String UPDATE_EBS_VOLUME_ID = "updateEbsVolumeId";
    public static final String ROLLBACK_EBS_VOLUME_ID = "rollbackEbsVolumeId";
    public static final String UPDATE_INSTANCE_HARDWARE_DEVICE_NAME =
        "updateInstanceHardwareDeviceName";
    public static final String ROLLBACK_INSTANCE_HARDWARE_DEVICE_NAME =
        "rollbackInstanceHardwareDeviceName";
    public static final String UPDATE_EBS_VOLUME_CMK_KEY = "updateEbsVolumeCmkKey";
    public static final String ROLLBACK_EBS_VOLUME_CMK_KEY = "rollbackEbsVolumeCmkKey";
    public static final String SAVE_ATTACHED_DISK_TYPE = "saveAttachedDiskType";
    public static final String UPDATE_IS_DEFAULT_DISK_ATTACHED = "updateIsDefaultDiskAttached";
    public static final String NEW_DEVICE_NAME = "newDeviceName";
    public static final String OLD_DEVICE_NAME = "oldDeviceName";
    public static final String SAVE_NEW_DISK_DEVICE_NAME = "saveNewDiskDeviceName";
    public static final String SERIALIZED_AGENT_JOB_PREFIX = "serializedAgentJob_";
    public static final String CHEF_CALLBACK_ID = "chefCallbackId";
    public static final String CLOUD_CHEF_CONF_ID = "cloudChefConfId";
    public static final String CHEF_CALLED_BACK = "chefCalledBack";
    public static final String DISK_SWAP_COMPLETED = "diskSwapCompleted";
    public static final String CHEF_CONFIG_UPDATED = "chefConfigUpdated";
  }

  protected static class StepNumber extends BaseInstancePowerCycleMove.StepNumber {

    public static final int DETACH_VOLUME = 10;
    public static final int CREATE_VOLUME_FROM_SNAPSHOT = 11;
    public static final int ATTACH_VOLUME = 12;
    public static final int SHARE_SNAPSHOT = 20;
    public static final int GENERATE_AUTOMATION_CONFIG_FOR_DIRECT_ATTACH_RESTORE = 16;
    public static final int PUBLISH_RESTORE_VERIFICATION_KEY_AUTOMATION_CONFIG = 17;
    public static final int WAIT_FOR_AGENT_GOAL_STATE_FOR_RESTORE = 18;

    // Roll-back steps
    public static final int ENABLE_PROCESS_AFTER_ROLLBACK = 21;
    public static final int PUBLISH_AUTOMATION_CONFIG_AFTER_ROLLBACK = 22;
    public static final int WAIT_FOR_HOST_GOAL_STATE_AFTER_ROLLBACK = 23;
    public static final int PUBLISH_RESTORE_VERIFICATION_KEY_AUTOMATION_CONFIG_ROLLBACK = 25;
    public static final int ORPHAN_OLD_VOLUME = 28;

    public static final int TRIGGER_ATLAS_NODE_DATA = 29;
    public static final int WAIT_FOR_CHEF = 30;

    // Optimized volume creation steps
    public static final int CREATE_OPTIMIZED_VOLUME_FROM_SNAPSHOT_IO2 = 111;
    public static final int CREATE_OPTIMIZED_VOLUME_FROM_SNAPSHOT_IO1 = 112;
    public static final int CREATE_OPTIMIZED_VOLUME_FROM_SNAPSHOT_GP3 = 113;
  }
}
