package com.xgen.svc.nds.aws.planner;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.xgen.cloud.chef._private.dao.ChefServerStatusDao;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.AWSEnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.AWSProviderLoadBalancer;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.svc.nds.planner.BaseRestartEnvoyServerMove;
import com.xgen.svc.nds.planner.MoveProvider;
import com.xgen.svc.nds.serverless.dao.EnvoyConfigurationDao;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;

public class AWSRestartEnvoyServerMove extends BaseRestartEnvoyServerMove {

  private final AWSApiSvc _awsApiSvc;

  @AssistedInject
  public AWSRestartEnvoyServerMove(
      @Assisted("planContext") final PlanContext pPlanContext,
      @Assisted("envoyInstanceId") final ObjectId pEnvoyInstanceId,
      @Assisted("containerId") final ObjectId pContainerId,
      @Assisted("serverlessLoadBalancingDeploymentId")
          final ObjectId pServerlessLoadBalancingDeploymentId,
      final ServerlessLoadBalancingDeploymentDao pServerlessLoadBalancingDeploymentDao,
      final AWSApiSvc pAwsApiSvc,
      final NDSGroupDao pNDSGroupDao,
      final EnvoyConfigurationDao pEnvoyConfigurationDao,
      final ChefServerStatusDao pChefServerStatusDao) {
    super(
        pPlanContext,
        pEnvoyInstanceId,
        pContainerId,
        pServerlessLoadBalancingDeploymentId,
        pServerlessLoadBalancingDeploymentDao,
        pNDSGroupDao,
        pChefServerStatusDao,
        pEnvoyConfigurationDao);
    _awsApiSvc = pAwsApiSvc;
  }

  @AssistedInject
  public AWSRestartEnvoyServerMove(
      @Assisted("pId") final ObjectId pId,
      @Assisted("predecessors") final Set<ObjectId> pPredecessors,
      @Assisted("successors") final Set<ObjectId> pSuccessors,
      @Assisted("planContext") final PlanContext pContext,
      @Assisted("state") final State pState,
      @Assisted("envoyInstanceId") final ObjectId pEnvoyInstanceId,
      @Assisted("containerId") final ObjectId pContainerId,
      @Assisted("serverlessLoadBalancingDeploymentId")
          final ObjectId pServerlessLoadBalancingDeploymentId,
      final ServerlessLoadBalancingDeploymentDao pServerlessLoadBalancingDeploymentDao,
      final AWSApiSvc pAwsApiSvc,
      final NDSGroupDao pNDSGroupDao,
      final EnvoyConfigurationDao pEnvoyConfigurationDao,
      final ChefServerStatusDao pChefServerStatusDao) {
    super(
        pId,
        pPredecessors,
        pSuccessors,
        pContext,
        pState,
        pEnvoyInstanceId,
        pContainerId,
        pServerlessLoadBalancingDeploymentId,
        pServerlessLoadBalancingDeploymentDao,
        pNDSGroupDao,
        pChefServerStatusDao,
        pEnvoyConfigurationDao);
    _awsApiSvc = pAwsApiSvc;
  }

  public static AWSRestartEnvoyServerMove factoryCreate(
      final PlanContext pContext,
      final ObjectId pEnvoyInstanceId,
      final ObjectId pContainerId,
      final ObjectId pServerlessLoadBalancingDeploymentId) {
    return MoveProvider.createMoveFromFactory(
        AWSRestartEnvoyServerMove.class,
        List.of(pContext, pEnvoyInstanceId, pContainerId, pServerlessLoadBalancingDeploymentId));
  }

  @Override
  protected Result<NoData> rebootInstance(
      final ServerlessLoadBalancingDeployment pDeployment,
      final CloudProviderContainer pContainer) {
    final AWSEnvoyInstance envoyInstance =
        (AWSEnvoyInstance) pDeployment.getEnvoyInstanceById(getEnvoyInstanceId()).get();

    final AWSRebootEnvoyInstanceStep rebootEnvoyInstanceStep =
        new AWSRebootEnvoyInstanceStep(
            getNDSPlanContext(),
            _state.forStep(StepNumber.INSTANCE_REBOOT),
            (AWSCloudProviderContainer) pContainer,
            envoyInstance,
            _awsApiSvc);
    getNDSPlanContext()
        .getLogger()
        .info("Rebooting AWS Envoy Instance, ID {}", envoyInstance.getEc2InstanceId().get());
    return rebootEnvoyInstanceStep.perform();
  }

  @Override
  protected Result<NoData> rollbackRebootInstance() {
    return Result.done();
  }

  @Override
  protected Result<NoData> deregisterInstanceFromLoadBalancer(
      ServerlessLoadBalancingDeployment pDeployment, CloudProviderContainer pContainer) {
    return new AWSDeregisterEnvoyInstanceWithLoadBalancerStep(
            getNDSPlanContext(),
            getState().forStep(StepNumber.DEREGISTER_INSTANCE_WITH_LOAD_BALANCER),
            getEc2InstanceId(pDeployment),
            ((AWSProviderLoadBalancer) pDeployment.getCloudProviderLoadBalancer())
                .getTargetGroupArn(),
            _awsApiSvc,
            (AWSCloudProviderContainer) pContainer)
        .perform();
  }

  @Override
  protected Result<NoData> rollbackDeregisterInstanceFromLoadBalancer(
      ServerlessLoadBalancingDeployment pDeployment, CloudProviderContainer pContainer) {
    return new AWSDeregisterEnvoyInstanceWithLoadBalancerStep(
            getNDSPlanContext(),
            getState().forStep(StepNumber.DEREGISTER_INSTANCE_WITH_LOAD_BALANCER),
            getEc2InstanceId(pDeployment),
            ((AWSProviderLoadBalancer) pDeployment.getCloudProviderLoadBalancer())
                .getTargetGroupArn(),
            _awsApiSvc,
            (AWSCloudProviderContainer) pContainer)
        .rollback();
  }

  @Override
  protected Result<NoData> registerInstanceWithLoadBalancer(
      ServerlessLoadBalancingDeployment pDeployment, CloudProviderContainer pContainer) {
    return new AWSRegisterEnvoyInstanceWithLoadBalancerStep(
            getNDSPlanContext(),
            getState().forStep(StepNumber.REGISTER_INSTANCE_WITH_LOAD_BALANCER),
            getEc2InstanceId(pDeployment),
            ((AWSProviderLoadBalancer) pDeployment.getCloudProviderLoadBalancer())
                .getTargetGroupArn(),
            _awsApiSvc,
            (AWSCloudProviderContainer) pContainer)
        .perform();
  }

  @Override
  protected Result<NoData> rollbackRegisterInstanceWithLoadBalancer(
      ServerlessLoadBalancingDeployment pDeployment, CloudProviderContainer pContainer) {
    return new AWSRegisterEnvoyInstanceWithLoadBalancerStep(
            getNDSPlanContext(),
            getState().forStep(StepNumber.REGISTER_INSTANCE_WITH_LOAD_BALANCER),
            getEc2InstanceId(pDeployment),
            ((AWSProviderLoadBalancer) pDeployment.getCloudProviderLoadBalancer())
                .getTargetGroupArn(),
            _awsApiSvc,
            (AWSCloudProviderContainer) pContainer)
        .rollback();
  }

  private String getEc2InstanceId(final ServerlessLoadBalancingDeployment pDeployment) {
    final AWSEnvoyInstance envoyInstance =
        (AWSEnvoyInstance) pDeployment.getEnvoyInstanceById(getEnvoyInstanceId()).get();
    return envoyInstance.getEc2InstanceId().get();
  }

  @Override
  public CloudProvider getCloudProvider() {
    return CloudProvider.AWS;
  }

  @Override
  protected Object[] getArguments() {
    return new Object[] {
      getEnvoyInstanceId(), getContainerId(), getServerlessLoadBalancingDeploymentId()
    };
  }
}
