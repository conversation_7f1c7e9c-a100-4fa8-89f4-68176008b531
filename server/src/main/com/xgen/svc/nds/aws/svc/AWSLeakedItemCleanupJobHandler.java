package com.xgen.svc.nds.aws.svc;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.jobqueue._public.svc.JobHandlerSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderAccount;
import com.xgen.cloud.nds.cloudprovider._public.model.OrphanedItem;
import com.xgen.cloud.nds.leakeditem._public.svc.LeakedItemCleanupStateSvc;
import com.xgen.svc.nds.svc.CloudProviderItemProcessorSvc;
import com.xgen.svc.nds.svc.LeakedItemCleanupJobHandler;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class AWSLeakedItemCleanupJobHandler extends LeakedItemCleanupJobHandler {

  private final AWSCloudProviderItemProcessorSvc _awsCloudProviderLeakedItemProcessorSvc;

  @Inject
  public AWSLeakedItemCleanupJobHandler(
      final LeakedItemCleanupStateSvc pLeakedItemCleanupStateSvc,
      final JobHandlerSvc pJobHandlerSvc,
      final AWSCloudProviderItemProcessorSvc pAWSCloudProviderItemProcessorSvc,
      final AppSettings pAppSettings) {
    super(pLeakedItemCleanupStateSvc, pJobHandlerSvc, pAppSettings);
    _awsCloudProviderLeakedItemProcessorSvc = pAWSCloudProviderItemProcessorSvc;
  }

  @Override
  public CloudProviderItemProcessorSvc<? extends CloudProviderAccount, ? extends OrphanedItem.Type>
      getItemProcessorSvc() {
    return _awsCloudProviderLeakedItemProcessorSvc;
  }

  @Override
  public CloudProvider getCloudProvider() {
    return CloudProvider.AWS;
  }
}
