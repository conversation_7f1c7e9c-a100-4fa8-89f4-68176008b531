package com.xgen.svc.streams.planner.gcp;

import com.xgen.cloud.streams._private.planning.svc.StreamsMoveProvider;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.svc.streams.planner.move.gcp.GCPProvisionVPCProxyInstanceMove;
import com.xgen.svc.streams.planner.move.gcp.GCPProvisionVPCProxyRoute53Move;
import jakarta.inject.Singleton;
import org.bson.types.ObjectId;

@Singleton
public class StreamsGCPMoveProvider implements StreamsMoveProvider {

  @Override
  public Move destroyVPCPeeringInstanceMove(
      PlanContext planContext,
      ObjectId deploymentDescriptionId,
      ObjectId instanceId,
      ObjectId correlationId) {
    throw new UnsupportedOperationException(
        "GCP VPC peering instance destruction not implemented yet");
  }

  @Override
  public Move provisionVPCPeeringInstanceMove(
      PlanContext planContext,
      ObjectId instanceId,
      ObjectId replacesInstanceId,
      ObjectId correlationId) {
    return GCPProvisionVPCProxyInstanceMove.factoryCreate(
        planContext, instanceId, replacesInstanceId, correlationId);
  }

  @Override
  public Move rebootVPCPeeringInstanceMove(
      PlanContext planContext, ObjectId instanceId, ObjectId correlationId) {
    throw new UnsupportedOperationException("GCP VPC peering instance reboot not implemented yet");
  }

  @Override
  public Move provisionDNSMove(PlanContext planContext, ObjectId deploymentId) {
    return GCPProvisionVPCProxyRoute53Move.factoryCreate(planContext, deploymentId);
  }

  @Override
  public Move deleteDNSMove(PlanContext planContext, ObjectId deploymentId) {
    throw new UnsupportedOperationException("GCP DNS deletion not implemented yet");
  }

  @Override
  public Move provisionInterfaceEndpointMove(PlanContext planContext, ObjectId privateLinkId) {
    throw new UnsupportedOperationException(
        "GCP interface endpoint provisioning not implemented yet");
  }

  @Override
  public Move provisionMSKManagedConnectionMove(PlanContext planContext, ObjectId privateLinkId) {
    throw new UnsupportedOperationException(
        "GCP MSK managed connection provisioning not implemented yet");
  }

  @Override
  public Move deleteMSKManagedConnectionMove(PlanContext planContext, ObjectId privateLinkId) {
    throw new UnsupportedOperationException(
        "GCP MSK managed connection deletion not implemented yet");
  }

  @Override
  public Move deleteInterfaceEndpointMove(PlanContext planContext, ObjectId privateLinkId) {
    throw new UnsupportedOperationException("GCP interface endpoint deletion not implemented yet");
  }

  @Override
  public Move provisionEnsureKafkaNetworkPermissionsAppliedMove(
      PlanContext planContext, ObjectId pContainerId) {
    throw new UnsupportedOperationException("GCP Kafka network permissions not implemented yet");
  }

  @Override
  public Move provisionVPCProxyZoneFileDNSRecordsMove(
      PlanContext planContext, ObjectId privateLinkId, String region) {
    throw new UnsupportedOperationException(
        "GCP VPC proxy zone file DNS records not implemented yet");
  }

  @Override
  public Move vpcPeeringConnScanMove(PlanContext planContext, ObjectId scannerId) {
    throw new UnsupportedOperationException("GCP VPC peering connection scan not implemented yet");
  }

  @Override
  public Move vpcPeeringConnUpdateMove(PlanContext planContext, ObjectId updateId) {
    throw new UnsupportedOperationException(
        "GCP VPC peering connection update not implemented yet");
  }

  @Override
  public Move updateOSPolicyMove(
      PlanContext planContext,
      String goalOSPolicyVersion,
      ObjectId deploymentId,
      ObjectId instanceId) {
    throw new UnsupportedOperationException("GCP OS policy update not implemented yet");
  }
}
