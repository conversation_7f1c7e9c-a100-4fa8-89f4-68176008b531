package com.xgen.svc.search.planner.step;

import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.fts._private.dao.FTSIndexConfigStatsDao;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSIndexConfigStats;
import com.xgen.cloud.nds.fts._public.model.FTSIndexGenerationDetail;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostDetailedStatuses;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat.StatusCode;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexConfigStats;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndexConfigStats;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndexHostStat;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.search.common._public.util.HostnameUtil;
import com.xgen.cloud.search.common._public.util.SearchPromMetrics;
import com.xgen.cloud.search.decoupled.blobstore._public.svc.BlobstoreFeatureSvc;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroup;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchInstance;
import com.xgen.cloud.search.decoupled.config._public.model.Migration;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.cloud.search.settings._public.settings.SearchAppSettings;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.NDSStep;
import com.xgen.svc.nds.svc.FTSIndexConfigSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import io.prometheus.client.Counter;
import java.time.Clock;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.BsonTimestamp;
import org.bson.types.ObjectId;

/**
 * A step to wait for replication catch up. This step is expected to run after {@link
 * WaitForMongotInitialSyncStep}.
 *
 * <p>Current algorithm works by ensuring that the replication state is "as good as" the existing
 * RUNNING instances in the cluster for every index in the catalog.
 *
 * <p>The algorithm operates in two main phases:
 *
 * <ol>
 *   <li><b>Baseline Calculation:</b> First, it establishes a baseline by determining the "best"
 *       known replication status for each index across all healthy, RUNNING instances. For a given
 *       index, the "best" status is chosen by:
 *       <ul>
 *         <li>Filtering out any instances that have not reported status recently (controlled by
 *             {@code mms.search.planner.maxReplicationStatusAgeMinutes})
 *         <li>Comparing the {@link StatusCode} of the remaining instances. If statuses are
 *             identical, the instance with the more recent {@code optime} (for queryable statuses)
 *             or update time (for non-queryable statuses) is chosen.
 *         <li>If the statuses are not the same, choose the status that is considered better. A
 *             status is considered "better" if it is closer to the ideal state ({@code STEADY >
 *             RECOVERING > STALE > INITIAL_SYNC > NOT_STARTED > DOES_NOT_EXIST > FAILED})
 *       </ul>
 *   <li><b>Provisioning Host Comparison:</b> Second, it compares the provisioning host's status for
 *       each index against the calculated baseline. An index on the provisioning host is considered
 *       "ready" only if all the following conditions are met:
 *       <ul>
 *         <li>Replication stats are available for the index on both the provisioning host and from
 *             the baseline.
 *         <li>If there is no baseline for comparison, then the {@code optime} of a {@code STEADY}
 *             index is compared against the current system time. If the replication lag is low,
 *             then index is considered ready.
 *         <li>Neither the provisioning host nor the baseline has a staged index for this index.
 *         <li>The index status on the provisioning host is either {@code STEADY} or {@code
 *             DOES_NOT_EXIST}.
 *         <li>If both the provisioning host and the baseline report a {@code STEADY} status, the
 *             provisioning host's {@code optime} must be within tolerance (configured via {@code
 *             mms.search.planner.replicationDeltaToleranceSeconds}) of the baseline's {@code
 *             optime}. Note that the provisioning host's optime may be ahead of the baseline.
 *         <li>A special case is handled where if the provisioning host is {@code STEADY} but the
 *             baseline is {@code NOT_STARTED}, the index is considered not ready to avoid race
 *             conditions during index state transitions.
 *       </ul>
 * </ol>
 *
 * The provisioning instance is only marked as fully ready once all of its indexes are considered
 * ready.
 */
public class WaitForMongotReplicationCatchUpStep extends NDSStep<Result.NoData> {
  private final SearchAppSettings appSettings;
  private final PartitionGroup partitionGroup;
  private final SearchInstance searchInstance;
  private final String hostname;
  private final WaitForMongotInitialSyncStep.Data waitForMongotInitialSyncStepData;
  private final FTSIndexConfigSvc ftsIndexConfigSvc;
  private final FTSIndexConfigStatsDao ftsIndexConfigStatsDao;
  private final NDSClusterSvc ndsClusterSvc;
  private final SearchDeploymentDescriptionSvc searchDeploymentDescriptionSvc;
  private final ReplicaSetHardwareSvc replicaSetHardwareSvc;
  private final FeatureFlagSvc featureFlagSvc;
  private final BlobstoreFeatureSvc blobstoreFeatureSvc;
  private final Group group;
  private final Clock clock;

  private static final Counter SEARCH_PLANNER_MONGOT_REPLICATION_CATCH_UP_TIMEOUT_TOTAL =
      PromMetricsSvc.registerCounter(
          SearchPromMetrics.getMetricNameWithSearchPrefix(
              "search_planner_mongot_replication_catch_up_timeout_total"),
          "The total count of the number of times WaitForMongotReplicationCatchUpStep in the search"
              + " planner timed out.",
          "clusterId");

  public WaitForMongotReplicationCatchUpStep(
      NDSPlanContext planContext,
      State state,
      PartitionGroup partitionGroup,
      SearchInstance searchInstance,
      String hostname,
      WaitForMongotInitialSyncStep.Data waitForMongotInitialSyncStepData,
      FTSIndexConfigSvc ftsIndexConfigSvc,
      FTSIndexConfigStatsDao ftsIndexConfigStatsDao,
      NDSClusterSvc ndsClusterSvc,
      SearchDeploymentDescriptionSvc searchDeploymentDescriptionSvc,
      ReplicaSetHardwareSvc replicaSetHardwareSvc,
      FeatureFlagSvc featureFlagSvc,
      BlobstoreFeatureSvc blobstoreFeatureSvc,
      Group group,
      SearchAppSettings appSettings) {
    this(
        planContext,
        state,
        partitionGroup,
        searchInstance,
        hostname,
        waitForMongotInitialSyncStepData,
        ftsIndexConfigSvc,
        ftsIndexConfigStatsDao,
        ndsClusterSvc,
        searchDeploymentDescriptionSvc,
        replicaSetHardwareSvc,
        featureFlagSvc,
        blobstoreFeatureSvc,
        group,
        appSettings,
        Clock.systemUTC());
  }

  public WaitForMongotReplicationCatchUpStep(
      NDSPlanContext planContext,
      State state,
      PartitionGroup partitionGroup,
      SearchInstance searchInstance,
      String hostname,
      WaitForMongotInitialSyncStep.Data waitForMongotInitialSyncStepData,
      FTSIndexConfigSvc ftsIndexConfigSvc,
      FTSIndexConfigStatsDao ftsIndexConfigStatsDao,
      NDSClusterSvc ndsClusterSvc,
      SearchDeploymentDescriptionSvc searchDeploymentDescriptionSvc,
      ReplicaSetHardwareSvc replicaSetHardwareSvc,
      FeatureFlagSvc featureFlagSvc,
      BlobstoreFeatureSvc blobstoreFeatureSvc,
      Group group,
      SearchAppSettings appSettings,
      Clock clock) {
    super(planContext, state);
    this.partitionGroup = partitionGroup;
    this.searchInstance = searchInstance;
    this.hostname = hostname;
    this.waitForMongotInitialSyncStepData = waitForMongotInitialSyncStepData;
    this.ftsIndexConfigSvc = ftsIndexConfigSvc;
    this.ftsIndexConfigStatsDao = ftsIndexConfigStatsDao;
    this.ndsClusterSvc = ndsClusterSvc;
    this.replicaSetHardwareSvc = replicaSetHardwareSvc;
    this.searchDeploymentDescriptionSvc = searchDeploymentDescriptionSvc;
    this.featureFlagSvc = featureFlagSvc;
    this.blobstoreFeatureSvc = blobstoreFeatureSvc;
    this.group = group;
    this.appSettings = appSettings;
    this.clock = clock;
  }

  @Override
  protected Result<Result.NoData> performInternal() {
    var startTime = this.waitForMongotInitialSyncStepData.readySince();
    var clusterUniqueId = partitionGroup.getSyncSource().getClusterUniqueId();
    if (isPerformed()) {
      getLogger()
          .atInfo()
          .addKeyValue("hostname", hostname)
          .addKeyValue("clusterId", clusterUniqueId)
          .addKeyValue("startTime", startTime)
          .addKeyValue("durationSeconds", startTime.until(clock.instant(), ChronoUnit.SECONDS))
          .log("Already completed this. Possibly due to the step being skipped.");
      return Result.done();
    }

    ClusterDescription clusterDescription =
        ndsClusterSvc
            .getActiveClusterDescription(this.partitionGroup.getGroupId(), clusterUniqueId)
            .orElseThrow();

    // This is a temporary timeout that we are using initially to reduce on-call load as we learn
    // how this step will perform in production. This timeout will ultimately be removed before
    // FCIS downloads are enabled. It can also be set to infinite by setting the value of
    // mms.search.planner.replicationCatchUpStepTimeoutSeconds to a negative value.
    var blobstoreFeatureFlags =
        blobstoreFeatureSvc.getBlobstoreAccessState(
            group, clusterDescription, partitionGroup, searchInstance);
    var blobstoreDownloadsAreEnabled =
        blobstoreFeatureFlags.privatePreviewAccess()
            || blobstoreFeatureFlags.blobstoreDownloadAccess();
    SearchAppSettings.Timeout timeout =
        blobstoreDownloadsAreEnabled
            ? this.appSettings.getAtlasSearchPlannerReplicationCatchUpStepTimeoutForFCIS()
            : this.appSettings.getAtlasSearchPlannerReplicationCatchUpStepTimeout();
    var deadline = timeout.deadline(startTime);
    if (clock.instant().isAfter(deadline)) {
      PromMetricsSvc.incrementCounter(
          SEARCH_PLANNER_MONGOT_REPLICATION_CATCH_UP_TIMEOUT_TOTAL, clusterUniqueId.toString());
      var log =
          getLogger()
              .atWarn()
              .addKeyValue("clusterId", clusterUniqueId)
              .addKeyValue("hostname", hostname)
              .addKeyValue("deadline", deadline)
              .addKeyValue("timeout", timeout)
              .addKeyValue("blobstoreDownloadsAreEnabled", blobstoreDownloadsAreEnabled)
              .addKeyValue("durationSeconds", startTime.until(clock.instant(), ChronoUnit.SECONDS));
      if (!blobstoreDownloadsAreEnabled
          || featureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.ATLAS_SEARCH_DISABLE_PLANNER_REPLICATION_LAG_CATCH_UP_TIMEOUT,
              null,
              group)) {
        // Blobstore downloads are DISABLED. If blobstore downloads are not enabled, it's safe to
        // time out this step.
        //
        // OR
        //
        // Feature flag is enabled for group, indicating that we intend for this group to timeout.
        log.log("Step timed out, marking the instance as ready.");
        return Result.done();
      }
      // The default for blobstore download enabled clusters is to block indefinitely. This can be
      // disabled by changing the earlier feature flag either globally or for a group.
      log.log("Step timed out, but will continue to block.");
    }
    List<FTSIndexConfigStats> stats =
        ftsIndexConfigStatsDao.findByClusterProjected(
            this.partitionGroup.getGroupId(), clusterDescription.getName());

    // If there is an in-progress migration from coupled, the stats of search nodes are not
    // written to FTSIndexConfigStatsDao. We need to get them from SearchDeploymentDescription.
    // Note: This may cause some consistency issues because the stats and the detailed status in
    // the SearchDeploymentDescription may be from different conf calls.
    SearchDeploymentDescription searchDeploymentDescription =
        searchDeploymentDescriptionSvc.getDeploymentById(partitionGroup.getSearchDeploymentId());
    List<FTSIndexConfigStats> statsFromMigration =
        searchDeploymentDescription
            .getMigrationOptional()
            .filter(Migration::isMigrationInProgress)
            .map(migration -> getStatsFromMigration(migration, clusterDescription))
            .orElse(List.of());
    stats.addAll(statsFromMigration);

    List<FTSIndex> indexesInCatalog =
        ftsIndexConfigSvc
            .getFTSIndexConfig(partitionGroup.getGroupId(), clusterDescription.getName())
            .map(config -> config.getIndexesForAllTypes().stream().toList())
            .orElse(List.of());

    Set<String> runningHosts =
        partitionGroup.getInstances().stream()
            .filter(instance -> instance.getPlanningState() == SearchInstance.State.RUNNING)
            .map(SearchInstance::getInstanceHardware)
            .map(InstanceHardware::getHostnameForAgents)
            .filter(Optional::isPresent)
            .map(Optional::get)
            // Hostname in FTSIndexConfigStats does not contain domain info.
            .map(HostnameUtil::getHostnameWithoutDomain)
            .collect(Collectors.toSet());
    if (!statsFromMigration.isEmpty()) {
      // If there's a migration in progress, we need to consider the coupled search instances as
      // RUNNING.
      replicaSetHardwareSvc
          .getReplicaSetHardware(this.partitionGroup.getGroupId(), clusterDescription.getName())
          .stream()
          .filter(partitionGroup::matchReplicaSet)
          .map(ReplicaSetHardware::getHardware)
          .flatMap(List::stream)
          .map(InstanceHardware::getHostnameForAgents)
          .filter(Optional::isPresent)
          .map(Optional::get)
          .map(HostnameUtil::getHostnameWithoutDomain)
          .forEach(runningHosts::add);
    }

    Map<ObjectId, ReplicationStats> bestReplicationDataFromRunningHosts =
        getBestReplicationDataFromRunningHosts(stats, runningHosts);
    Map<ObjectId, ReplicationStats> currentHostStats = getReplicationDataForCurrentHost(stats);

    Set<ObjectId> readyIndexes = new HashSet<>();
    Set<ObjectId> waitingIndexes = new HashSet<>();

    for (FTSIndex index : indexesInCatalog) {
      var indexId = index.getIndexId();

      if (!runningHosts.isEmpty() && !bestReplicationDataFromRunningHosts.containsKey(indexId)) {
        getLogger()
            .atDebug()
            .addKeyValue("clusterId", clusterUniqueId)
            .addKeyValue("hostname", hostname)
            .addKeyValue("indexId", indexId)
            .log("No RUNNING host has reported a status for index");
        waitingIndexes.add(indexId);
        continue;
      }
      if (!currentHostStats.containsKey(indexId)) {
        getLogger()
            .atDebug()
            .addKeyValue("clusterId", clusterUniqueId)
            .addKeyValue("hostname", hostname)
            .addKeyValue("indexId", indexId)
            .log("Current host has not reported a status for index");
        waitingIndexes.add(indexId);
        continue;
      }

      Instant now = clock.instant();
      var indexStatsFromBestRunningHost =
          bestReplicationDataFromRunningHosts.getOrDefault(
              indexId,
              // This default is used when there are no RUNNING hosts. The intent here is to compare
              // the optime of the index on the provisioning host (if it is in STEADY state) to the
              // current time.
              new ReplicationStats(
                  "dummy-stats-due-to-no-running-hosts",
                  // We pretend that there is a steady index
                  Optional.of(StatusCode.STEADY),
                  // And the optime is "now"
                  new BsonTimestamp((int) now.getEpochSecond(), now.getNano()).getValue(),
                  now,
                  false));
      var indexStatsFromThisHost = currentHostStats.get(indexId);
      var logBuilder =
          getLogger()
              .atDebug()
              .addKeyValue("clusterId", clusterUniqueId)
              .addKeyValue("hostname", hostname)
              .addKeyValue("indexId", indexId)
              .addKeyValue("currentIndexStatus", indexStatsFromThisHost.status)
              .addKeyValue("currentOptime", getInstantFromOptime(indexStatsFromThisHost.optime))
              .addKeyValue("bestIndexStatus", indexStatsFromBestRunningHost.status)
              .addKeyValue(
                  "bestIndexOptime", getInstantFromOptime(indexStatsFromBestRunningHost.optime))
              .addKeyValue("bestIndexHost", indexStatsFromBestRunningHost.hostname);

      if (indexStatsFromThisHost.status.isEmpty()) {
        logBuilder.log("Index is not ready because status is not populated");
        waitingIndexes.add(indexId);
        continue;
      }
      if (indexStatsFromThisHost.hasStagedIndex) {
        logBuilder.log("Index is not ready because there is a staged index generation");
        waitingIndexes.add(indexId);
        continue;
      }

      if (indexStatsFromThisHost.status.get() == StatusCode.STEADY) {
        if (indexStatsFromBestRunningHost.status.isEmpty()) {
          logBuilder.log("Index is not ready because no RUNNING host has reported an index status");
          waitingIndexes.add(indexId);
          continue;
        }
        if (indexStatsFromBestRunningHost.status.get() == StatusCode.STEADY) {
          // If both are STEADY, check the optimes and decide whether the index on this instance has
          // ready with RUNNING instances.
          if (indexStatsFromThisHost.optime == 0 || indexStatsFromBestRunningHost.optime == 0) {
            // Optime is not accurate. This generally indicates that the optime has not been
            // reported by this host. Waiting for another cycle would give the mongot a chance to
            // report it, or for another mongot to have reached a better state so that it becomes
            // the new "best" host for this index.
            logBuilder.log("Index is not ready because optimes are not valid");
            waitingIndexes.add(indexId);
            continue;
          }
          long tolerance = appSettings.getAtlasSearchPlannerReplicationDeltaToleranceSeconds();
          long currentEpoch = new BsonTimestamp(indexStatsFromThisHost.optime).getTime();
          long bestEpoch = new BsonTimestamp(indexStatsFromBestRunningHost.optime).getTime();
          long delta = bestEpoch - currentEpoch;
          if (delta < tolerance) {
            logBuilder
                .addKeyValue("deltaSeconds", delta)
                .addKeyValue("tolerance", tolerance)
                .log("Index is ready because optime is within tolerance of RUNNING instance");
            readyIndexes.add(indexId);
          } else {
            logBuilder
                .addKeyValue("deltaSeconds", delta)
                .addKeyValue("tolerance", tolerance)
                .log(
                    "Index is not ready because optime is not within tolerance of RUNNING"
                        + " instance");
            waitingIndexes.add(indexId);
          }
        } else if (indexStatsFromBestRunningHost.status.get() == StatusCode.NOT_STARTED) {
          // Status from the other host is not accurate. Wait for another update. See the comment
          // above for why this might not be accurate.
          logBuilder.log("Index is not ready because the status on the other host is not accurate");
          waitingIndexes.add(indexId);
        } else {
          // Status from other host is neither STEADY nor NOT_STARTED. Recall that we expect there
          // to be only one index generation for this index at this point.
          //
          // - FAILED
          // - DOES_NOT_EXIST (not possible)
          // - INITIAL_SYNC (indicates that the RUNNING host is likely behind).
          // - STALE
          // - RECOVERING
          //
          // In all of the above states in the RUNNING instance, we're safe to mark this index as
          // ready.
          logBuilder.log(
              "Index is ready because the current status is better than RUNNING instance status");
          readyIndexes.add(indexId);
        }
      } else if (indexStatsFromThisHost.status.get() == StatusCode.DOES_NOT_EXIST) {
        // Can happen for unsharded collections. An unsharded collection lives on a single shard.
        // On the shards that the collection does not live on, the status will be DOES_NOT_EXIST.
        // This can also happen for sharded collections that have no data on a particular shard.
        // TODO Double check the value on the RUNNING host?
        logBuilder.log(
            "Index is ready because index does not exist, which can happen for unsharded"
                + " collections");
        readyIndexes.add(indexId);
      } else {
        logBuilder.log(
            "Index not ready because only STEADY and DOES_NOT_EXIST are considered to be ready");
        waitingIndexes.add(indexId);
      }
    }

    if (!waitingIndexes.isEmpty()) {
      getLogger()
          .atInfo()
          .addKeyValue("clusterId", clusterUniqueId)
          .addKeyValue("hostname", hostname)
          .addKeyValue("waitingIndexes", waitingIndexes)
          .addKeyValue("readyIndexes", readyIndexes)
          .addKeyValue("waitingIndexCount", waitingIndexes.size())
          .addKeyValue("readyIndexCount", readyIndexes.size())
          .addKeyValue("totalIndexCount", indexesInCatalog.size())
          .log("Not all indexes are ready yet");
      return Result.inProgress();
    }
    getLogger()
        .atInfo()
        .addKeyValue("clusterId", clusterUniqueId)
        .addKeyValue("hostname", hostname)
        .addKeyValue("durationSeconds", startTime.until(clock.instant(), ChronoUnit.SECONDS))
        .log("All indexes are ready");
    return Result.done();
  }

  /**
   * Filters a list of index configuration stats and extracts the replication data for the current
   * host.
   *
   * <p>This method iterates through a list of {@link FTSIndexConfigStats}, selecting only those
   * that match the current instance's hostname (ignoring the domain). For each matching stat that
   * has a status code present, it creates a {@link ReplicationStats} object and maps it to its
   * corresponding index ID.
   *
   * @param stats A list of index configuration statistics from all hosts.
   * @return A {@code Map} where each key is an index {@code ObjectId} and the value is the {@link
   *     ReplicationStats} for that index on the current host. The map will not contain entries for
   *     indexes that have no reported status.
   */
  private Map<ObjectId, ReplicationStats> getReplicationDataForCurrentHost(
      List<FTSIndexConfigStats> stats) {
    Map<ObjectId, ReplicationStats> replicationData = new HashMap<>();
    String hostnameWithoutDomain = HostnameUtil.getHostnameWithoutDomain(this.hostname);
    for (FTSIndexConfigStats stat : stats) {
      if (!stat.getId().getHostname().equals(hostnameWithoutDomain)) {
        continue;
      }
      if (isIndexStatusTooOld(stat)) {
        continue;
      }
      var index = stat.getId().getIndexId();
      var lastUpdated = getLastUpdated(stat);
      boolean hasStagedIndex = stat.getDetailedStatuses().getStagedIndex().isPresent();
      var data =
          new ReplicationStats(
              hostnameWithoutDomain,
              // Note that we are using the StatusCode of the index as reported. This can be the
              // main index status if there is no staged index or the staged index status if there
              // is a staged index.
              stat.getStats().getStatusCode(),
              stat.getStats().getOptime(),
              lastUpdated,
              hasStagedIndex);
      replicationData.put(index, data);
    }
    return replicationData;
  }

  private boolean isIndexStatusTooOld(FTSIndexConfigStats stat) {
    ObjectId indexId = stat.getId().getIndexId();
    Instant lastUpdated = getLastUpdated(stat);
    Instant cutoff =
        clock.instant().minus(appSettings.getAtlasSearchPlannerMaxReplicationStatusAge());
    if (lastUpdated.isBefore(cutoff)) {
      getLogger()
          .atInfo()
          .addKeyValue("indexId", indexId)
          .addKeyValue("hostname", stat.getId().getHostname())
          .addKeyValue("lastUpdated", lastUpdated)
          .addKeyValue("cutoff", cutoff)
          .log("Index status is too old for this host");
      return true;
    }
    return false;
  }

  private Instant getLastUpdated(FTSIndexConfigStats stats) {
    var lastUpdated = stats.getStats().getLastUpdateDate();
    if (lastUpdated.isEmpty()) {
      return Instant.EPOCH;
    }
    return lastUpdated.get().toInstant();
  }

  /**
   * Calculates the "best" replication status for each index across all RUNNING hosts.
   *
   * <p>This method iterates through a comprehensive list of index statistics from all hosts in the
   * cluster but filters them to consider only the healthy, RUNNING hosts. For each index, it
   * determines which running host has the most up-to-date and "best" replication state, creating a
   * baseline map of the optimal status for every index.
   *
   * <p>A key part of the logic is how it handles hosts that have a staged index. When a running
   * host is building a new version of an index, the optime of its main index is unreliable as it is
   * reported as 0. To handle this, if a staged index is present, the method uses the current time
   * provided by the input clock as a synthetic optime. This prevents the host from being unfairly
   * considered "stale" and ensures that the comparison logic remains robust during index builds.
   *
   * @param stats A list of {@link FTSIndexConfigStats} from all hosts in the cluster.
   * @param runningHosts A set of hostnames corresponding to instances in the RUNNING state.
   * @return A {@code Map} where each key is an index {@code ObjectId} and the value is the single
   *     "best" {@link ReplicationStats} found for that index among all running hosts.
   */
  private Map<ObjectId, ReplicationStats> getBestReplicationDataFromRunningHosts(
      List<FTSIndexConfigStats> stats, Set<String> runningHosts) {
    Map<ObjectId, ReplicationStats> bestReplicationData = new HashMap<>();
    for (FTSIndexConfigStats stat : stats) {
      // Filter out data not from RUNNING hosts.
      String hostname = stat.getId().getHostname();
      if (!runningHosts.contains(hostname)) {
        continue;
      }
      if (isIndexStatusTooOld(stat)) {
        continue;
      }

      var index = stat.getId().getIndexId();
      // We explicitly use the status from the main index here. For the RUNNING hosts, all we care
      // about is the main index status. This is different from stat.getStats().getStatus() which
      // returns the main index status if there is not staged index, and the staged index status
      // otherwise.
      var mainIndexStatus =
          stat.getDetailedStatuses().getMainIndex().map(FTSIndexGenerationDetail::getStatus);
      boolean hasStagedIndex = stat.getDetailedStatuses().getStagedIndex().isPresent();
      long replicationOptime;
      if (hasStagedIndex) {
        // When there is a staged index on the running host, the reported optime will be 0. So
        // it's best to compare the optime of the provisioning instance to now().
        Instant now = clock.instant();
        replicationOptime = new BsonTimestamp((int) now.getEpochSecond(), now.getNano()).getValue();
      } else {
        replicationOptime = stat.getStats().getOptime();
      }

      var lastUpdated = getLastUpdated(stat);
      var newData =
          new ReplicationStats(
              hostname, mainIndexStatus, replicationOptime, lastUpdated, hasStagedIndex);
      bestReplicationData.merge(index, newData, ReplicationStats::bestOf);
    }
    return bestReplicationData;
  }

  private record ReplicationStats(
      String hostname,
      Optional<StatusCode> status,
      long optime,
      Instant lastUpdated,
      boolean hasStagedIndex) {
    /**
     * Determines if this ReplicationData instance represents a "better" or more up-to-date state
     * than the other provided instance.
     *
     * @param other The other ReplicationData to compare against.
     * @return true if this instance is better and should replace the other.
     */
    public boolean isBetterThan(ReplicationStats other) {
      if (status.isEmpty() && other.status.isEmpty()) {
        // If the statuses are both empty, choose the one that was updated more recently.
        return lastUpdated.isAfter(other.lastUpdated);
      }
      if (status.isEmpty() || other.status.isEmpty()) {
        // If the status is empty, it must mean the other status is not. Choose the other one.
        // If the other status is empty, choose this one.
        return other.status.isEmpty();
      }
      // Both statuses are present.
      if (status.get() == other.status.get()) {
        // If the status is queryable, then a more recent optime wins.
        if (StatusCode.isQueryable(status.get())) {
          if (this.optime == other.optime) {
            // If the optimes are exactly the same, use whether there is a staged index or not as
            // a tiebreaker. If one host has a staged index and the other does not, choose the one
            // that doesn't have a staged index as the "better" host.
            if (hasStagedIndex != other.hasStagedIndex) {
              return other.hasStagedIndex;
            }
            // Otherwise, use the host that reported in more recently.
            return lastUpdated.isAfter(other.lastUpdated);
          }
          return this.optime > other.optime;
        }
        // Otherwise, choose the one that was updated more recently.
        return lastUpdated.isAfter(other.lastUpdated);
      }
      // If the statuses are not the same, choose the one that is closer to being "ready".
      return this.status.get().isBetterThan(other.status.get());
    }

    public static ReplicationStats bestOf(ReplicationStats first, ReplicationStats second) {
      return first.isBetterThan(second) ? first : second;
    }
  }

  private Instant getInstantFromOptime(long optime) {
    var bsonTs = new BsonTimestamp(optime);
    return Instant.ofEpochSecond(bsonTs.getTime());
  }

  private List<FTSIndexConfigStats> getStatsFromMigration(
      Migration migration, ClusterDescription clusterDescription) {
    var indexStatsMap = migration.getAllMigrationIndexStats();
    var indexDetailedStatusesMap = migration.getAllMigrationIndexDetailedStatuses();
    List<FTSIndexConfigStats> statsFromMigration = new ArrayList<>();
    for (ObjectId indexId : indexStatsMap.keySet()) {
      for (String hostname : indexStatsMap.get(indexId).keySet()) {
        Optional<FTSIndexHostStat> stat =
            Optional.ofNullable(indexStatsMap.get(indexId)).flatMap(m -> m.getByHostname(hostname));
        Optional<FTSIndexHostDetailedStatuses> detailedStatuses =
            Optional.ofNullable(indexDetailedStatusesMap.get(indexId))
                .flatMap(m -> m.getByHostname(hostname));
        if (stat.isPresent() && detailedStatuses.isPresent()) {
          if (stat.get() instanceof FTSSearchIndexHostStat) {
            statsFromMigration.add(
                new FTSSearchIndexConfigStats.Builder()
                    .id(
                        new FTSIndexConfigStats.Id(
                            partitionGroup.getGroupId(),
                            clusterDescription.getName(),
                            indexId,
                            hostname))
                    .stats(stat.get())
                    .detailedStatuses(detailedStatuses.get())
                    .build());
          } else if (stat.get() instanceof FTSVectorSearchIndexHostStat) {
            statsFromMigration.add(
                new FTSVectorSearchIndexConfigStats.Builder()
                    .id(
                        new FTSIndexConfigStats.Id(
                            partitionGroup.getGroupId(),
                            clusterDescription.getName(),
                            indexId,
                            hostname))
                    .stats(stat.get())
                    .detailedStatuses(detailedStatuses.get())
                    .build());
          }
        }
      }
    }
    return statsFromMigration;
  }
}
