package com.xgen.svc.mms.api.util;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;

import com.xgen.cloud.authz.resource._public.util.ApiResourceTagUtil;
import com.xgen.cloud.authz.resource._public.view.api.ApiAtlasTagView;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.requestparams._public.ApiBooleanParam;
import com.xgen.cloud.common.res._public.util.NDSErrorCodeUtils;
import com.xgen.cloud.common.util._public.json.JsonOptional;
import com.xgen.cloud.common.util._public.util.MathUtils;
import com.xgen.cloud.deployment._public.model.MongoDbBuild;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionNameHelper;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.AutoScalingView;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ui.ComputeAutoScalingView;
import com.xgen.cloud.nds.cloudprovider._public.model.xcloud.CrossCloudInstanceSize;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.common._public.model.BiConnectorReadPreference;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.RootCertType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.StorageSystem;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.util.ClusterDescriptionUtil;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus;
import com.xgen.cloud.resourcetags._public.model.ResourceId;
import com.xgen.cloud.resourcetags._public.model.ResourceService;
import com.xgen.cloud.resourcetags._public.model.ResourceType;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAWSInstanceSizeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAWSProviderSettingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAWSVolumeTypeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAzureInstanceSizeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAzureProviderSettingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasBaseClusterDescriptionView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasBiConnectorView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasCloudProviderView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionConnectionStringsUtil;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterProviderSettingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasDedicatedHardwareSpecView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasEncryptionAtRestProviderView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasFlexProviderSettingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasFreeInstanceSizeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasFreeProviderSettingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasGCPInstanceSizeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasGCPProviderSettingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasHardwareSpecView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasInstanceSizeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasLegacyClusterDescriptionView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasLegacyClusterDescriptionView.Fields;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasLegacyReplicationSpecView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasRegionConfigView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasRegionSpecView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasReplicationSpecView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasServerlessProviderSettingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasTenantRegionConfigView;
import com.xgen.svc.mms.api.view.atlas._private.ApiPrivateAtlasLegacyClusterDescriptionView;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescription20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescriptionProcessArgs20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasReplicationSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasTenantHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasTenantRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasAutoScalingV15View;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView.ClusterDescriptionViewBuilder;
import com.xgen.svc.nds.model.ui.ClusterDescriptionViewUtils;
import com.xgen.svc.nds.model.ui.EncryptionAtRestProviderView;
import com.xgen.svc.nds.model.ui.ReplicationSpecView.FieldDefs;
import com.xgen.svc.nds.model.ui.VolumeTypeView;
import com.xgen.svc.nds.svc.CustomMongoDbBuildSvc;
import com.xgen.svc.nds.svc.NDSClusterConversionSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.svc.nds.util.ClusterValidationUtil;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class ApiAtlasClusterDescriptionUtil {

  private static final Logger LOG = LoggerFactory.getLogger(ApiAtlasClusterDescriptionUtil.class);
  private final NDSUISvc ndsUISvc;
  private final AppSettings appSettings;
  private final OnlineArchiveSvc onlineArchiveSvc;
  private final NDSGroupSvc ndsGroupSvc;

  private final FeatureFlagSvc featureFlagSvc;
  private final CpsSvc cpsSvc;
  private final ApiResourceTagUtil apiResourceTagUtil;
  private final NDSClusterConversionSvc ndsClusterConversionSvc;
  private final CustomMongoDbBuildSvc customMongoDbBuildSvc;

  @Inject
  public ApiAtlasClusterDescriptionUtil(
      final NDSUISvc ndsUISvc,
      final AppSettings appSettings,
      final OnlineArchiveSvc onlineArchiveSvc,
      final NDSGroupSvc ndsGroupSvc,
      final FeatureFlagSvc featureFlagSvc,
      final CpsSvc cpsSvc,
      final ApiResourceTagUtil apiResourceTagUtil,
      final NDSClusterConversionSvc ndsClusterConversionSvc,
      final CustomMongoDbBuildSvc customMongoDbBuildSvc) {
    this.appSettings = appSettings;
    this.ndsUISvc = ndsUISvc;
    this.onlineArchiveSvc = onlineArchiveSvc;
    this.ndsGroupSvc = ndsGroupSvc;
    this.featureFlagSvc = featureFlagSvc;
    this.cpsSvc = cpsSvc;
    this.apiResourceTagUtil = apiResourceTagUtil;
    this.ndsClusterConversionSvc = ndsClusterConversionSvc;
    this.customMongoDbBuildSvc = customMongoDbBuildSvc;
  }

  public static void validateBiConnectorForCreate(
      final Set<CloudProvider> pProviders,
      final ApiAtlasBiConnectorView pBiConnectorView,
      final int pNumAnalyticsNodes,
      final Boolean pEnvelope) {
    // bi connector is not currently supported for tenant clusters
    if (pProviders.contains(CloudProvider.FREE)) {
      throw ApiErrorCode.CLUSTER_PROVIDER_DOES_NOT_SUPPORT_BI_CONNECTOR.exception(
          pEnvelope, ApiAtlasBiConnectorView.FieldDefs.ENABLED);
    }

    // an empty biConnector object, then treat it as an optional
    if (!pBiConnectorView.hasEnabled() && !pBiConnectorView.hasReadPreference()) {
      return;
    }

    // it has the "readPreference", but missing the "enabled"
    if (!pBiConnectorView.hasEnabled() && pBiConnectorView.hasReadPreference()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasBiConnectorView.FieldDefs.ENABLED);
    }

    if (pBiConnectorView.hasReadPreference()
        && pBiConnectorView.hasEnabled()
        && pBiConnectorView.isEnabled()) {

      validateBiConnectorReadPreference(
          pBiConnectorView.getReadPreference(), pNumAnalyticsNodes, pEnvelope);
    }
  }

  public static void validateBiConnectorForUpdate(
      final ApiAtlasBiConnectorView pExistingBiConnectorView,
      final ApiAtlasBiConnectorView pNewBiConnectorView,
      final Integer analyticsNodeCount,
      final Boolean pEnvelope) {
    // an empty biConnector object, then treat it as an optional
    if (!pNewBiConnectorView.hasEnabled() && !pNewBiConnectorView.hasReadPreference()) {
      return;
    }

    // the new biConnector has the "readPreference", but missing the "enabled" in the update request
    if (!pNewBiConnectorView.hasEnabled() && pNewBiConnectorView.hasReadPreference()) {
      // check with the existing biConnector, and invalid it if the existing biConnector is
      // disabled.
      if (!pExistingBiConnectorView.hasEnabled() || !pExistingBiConnectorView.isEnabled()) {
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
            pEnvelope, ApiAtlasBiConnectorView.FieldDefs.ENABLED);
      }
    }

    final boolean isEnabled =
        (pNewBiConnectorView.hasEnabled() && pNewBiConnectorView.isEnabled())
            || (!pNewBiConnectorView.hasEnabled() && pExistingBiConnectorView.isEnabled());

    // validate the "readReference" value
    if (pNewBiConnectorView.hasReadPreference() && isEnabled) {

      validateBiConnectorReadPreference(
          pNewBiConnectorView.getReadPreference(), analyticsNodeCount, pEnvelope);
    }
  }

  private static void validateBiConnectorReadPreference(
      final String pBiConnectorReadPreference,
      final int pNumAnalyticsNodes,
      final Boolean pEnvelope) {
    final Optional<BiConnectorReadPreference> readPreference =
        BiConnectorReadPreference.fromValue(pBiConnectorReadPreference);

    final Optional<Boolean> invalidReadPreferenceState =
        readPreference.map(
            p -> BiConnectorReadPreference.ANALYTICS.equals(p) && pNumAnalyticsNodes == 0);

    if (!readPreference.isPresent()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasBiConnectorView.FieldDefs.READ_PREFERENCE);
    }

    if (invalidReadPreferenceState.get().equals(true)) {
      throw ApiErrorCode.INVALID_BI_CONNECTOR_READ_PREFERENCE_FOR_TOPOLOGY.exception(pEnvelope);
    }
  }

  public static void validateMongoDBMajorVersion(
      final String pMongoDBMajorVersion, final Boolean pEnvelope) {
    try {
      VersionUtils.parse(pMongoDBMajorVersion);
    } catch (final IllegalArgumentException e) {
      throw ApiErrorCode.MONGODB_VERSION_INVALID.exception(pEnvelope, pMongoDBMajorVersion);
    }
  }

  public ClusterDescriptionView getClusterDescriptionView(
      final Group group, final String name, final Boolean envelope, final boolean allowV15Support) {
    final ClusterDescriptionView view;
    try {
      view = ndsUISvc.getClusterDescription(group.getId(), name);
    } catch (final SvcException pE) {
      throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(envelope, name, group.getId());
    }

    if (view.isServerlessTenantCluster()) {
      if (appSettings.isServerlessEnabled()) {
        throw ApiErrorCode.CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API.exception(envelope, name);
      } else {
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(envelope, view.getName(), group.getId());
      }
    }

    if (view.isFlexTenantCluster()) {
      if (appSettings.isFlexEnabled()) {
        if (!isFlexClusterValid(view, group)) {
          throw ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.exception(envelope, name);
        }
      } else {
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(envelope, view.getName(), group.getId());
      }
    }

    if (!allowV15Support && Optional.ofNullable(view.isCrossCloudCluster()).orElse(false)) {
      throw ApiErrorCode.MULTI_CLOUD_CLUSTER_INVALID.exception(envelope);
    }

    if (!allowV15Support && view.hasDifferentAnalyticsAndElectableSpecs()) {
      throw ApiErrorCode.ASYMMETRIC_HARDWARE_INVALID.exception(envelope);
    }

    if (!allowV15Support && view.hasAsymmetricHardwareSpecs()) {
      throw ApiErrorCode.ASYMMETRIC_SHARD_UNSUPPORTED.exception(envelope);
    }

    return view;
  }

  public void isFlexClusterValidForTenantBackupApi(
      final String pName, final Group pGroup, final Boolean pEnvelope) {
    try {
      final ClusterDescriptionView view = ndsUISvc.getClusterDescription(pGroup.getId(), pName);
      if (view.isFlexTenantCluster()) {
        if (appSettings.isFlexEnabled()) {
          if (!isFlexClusterValid(view, pGroup)) {
            throw ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.exception(pEnvelope, pName);
          }
        } else {
          throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, pName, pGroup.getId());
        }
      }
      // we don't want to throw any exceptions here because we don't want to change the existing
      // behavior of ApiAtlasTenantBackupResource
    } catch (final SvcException ignored) {
    }
  }

  public boolean isFlexClusterValid(
      final ClusterDescriptionView pClusterDescriptionView, final Group pGroup) {
    return pClusterDescriptionView
        .getFlexTenantMigrationState()
        .map(
            state ->
                (state
                            .getTenantApiCloudProvider()
                            .map(size -> size.equals(CloudProvider.FREE))
                            .orElse(false)
                        || state
                            .getFormerCloudProvider()
                            .map(size -> size.equals(CloudProvider.FREE))
                            .orElse(false))
                    && isFlexShimLogicEnabled(pGroup))
        .orElse(false);
  }

  public Response getProcessArgs(
      final Group group,
      final String clusterName,
      final Boolean envelope,
      final Class<? extends ApiAtlasClusterDescriptionProcessArgs20240805View> viewClass)
      throws Exception {
    final ClusterDescriptionProcessArgsView view;

    try {
      view = ndsUISvc.getProcessArgsOrDefault(group.getId(), clusterName);
    } catch (final Exception pE) {
      throw ApiErrorCode.UNEXPECTED_ERROR.exception(envelope);
    }

    return new ApiResponseBuilder(envelope)
        .ok()
        .content(
            viewClass.getConstructor(ClusterDescriptionProcessArgsView.class).newInstance(view))
        .build();
  }

  protected void validateForCreate(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView, final Boolean envelope) {
    if (!clusterDescriptionView.hasName()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasBaseClusterDescriptionView.Fields.NAME_FIELD);
    }
  }

  public void validateClusterDescription20240805ViewForCreate(
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      final ClusterCreateContext pCreateContext,
      final Boolean pEnvelope,
      final NDSGroup pNDSGroup) {
    if (Optional.ofNullable(pClusterDescriptionView.getName()).isEmpty()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.NAME_FIELD);
    }

    if (Optional.ofNullable(pClusterDescriptionView.getReplicationSpecs()).isEmpty()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
    } else {
      // If value exists for replicationSpecs, check whether it is a serverless instance
      /*
       * Note: if a user is attempting to create a serverless instance, we should throw an
       * "INVALID_JSON_ATTRIBUTE" before even getting to this section of conditionals. The
       * "INVALID_JSON_ATTRIBUTE" should be thrown because SERVERLESS is not included as a
       * providerName option in {@link ApiAtlasRegionConfigView}. These conditionals are included
       * here as additional safeguards to mirror {@link
       * ApiAtlasLegacyClusterDescriptionResource#createCluster()}.
       */

      // Check serverlessTenant is calling regionConfigs which are never validated to exist, this
      // now checks if a value exists for regionConfigs
      if (pClusterDescriptionView.getReplicationSpecs().stream()
          .anyMatch(pReplicationSpec -> pReplicationSpec.getRegionConfigs() == null)) {
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(pEnvelope, FieldDefs.REGION_CONFIGS);
      }

      if (pClusterDescriptionView.isServerlessTenantInstance()) {
        if (appSettings.isServerlessEnabled()) {
          throw ApiErrorCode.CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API.exception(
              pEnvelope, pClusterDescriptionView.getName());
        } else {
          throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
              pEnvelope, ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
        }
      }
    }

    if (pClusterDescriptionView.isPaused()) {
      throw ApiErrorCode.CANNOT_CREATE_PAUSED_CLUSTER.exception(
          pEnvelope, pClusterDescriptionView.getName());
    }

    if (Optional.ofNullable(pClusterDescriptionView.getClusterType()).isEmpty()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD);
    }

    if (!pClusterDescriptionView.hasValidClusterType()) {
      throw ApiErrorCode.MISSING_OR_INVALID_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD);
    }

    if (pClusterDescriptionView.getRootCertType() == RootCertType.DST) {
      throw ApiErrorCode.DST_ROOT_CERT_UNSUPPORTED.exception(pEnvelope);
    }

    validateReplicationSpecs(
        pClusterDescriptionView.getReplicationSpecs(),
        pNDSGroup.getGroupId(),
        pCreateContext.supportsIndependentShardAutoScaling(),
        pEnvelope);

    if (pClusterDescriptionView.isFreeOrSharedTenantCluster()) {
      validateTenantCluster(pClusterDescriptionView, pEnvelope);
    }

    if (Optional.ofNullable(pClusterDescriptionView.getBiConnector()).isPresent()) {
      ApiAtlasClusterDescriptionUtil.validateBiConnectorForCreate(
          pClusterDescriptionView.getCloudProviders(),
          pClusterDescriptionView.getBiConnector(),
          pClusterDescriptionView.getTotalAnalyticsNodeCount(),
          pEnvelope);
    }

    if (Optional.ofNullable(pClusterDescriptionView.getMongoDBMajorVersion()).isPresent()) {
      ApiAtlasClusterDescriptionUtil.validateMongoDBMajorVersion(
          pClusterDescriptionView.getMongoDBMajorVersion(), pEnvelope);
    }
    if (!ClusterDescription.VALID_NAME_PATTERN
        .matcher(pClusterDescriptionView.getName())
        .matches()) {
      throw ApiErrorCode.CLUSTER_NAME_INVALID.exception(
          pEnvelope, pClusterDescriptionView.getName());
    }
  }

  /** Build cluster from view helpers for CREATE */
  public ClusterDescriptionView buildClusterDescriptionViewForCreate(
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      final Boolean pEnvelope,
      final NDSGroup pNDSGroup,
      final ObjectId pOrgId)
      throws SvcException {
    final ApiAtlasClusterDescription20240805View defaultCluster =
        createDefaultCluster(pClusterDescriptionView, pNDSGroup, pOrgId, pEnvelope);

    return pClusterDescriptionView
        .toUpdatedClusterDescriptionView(defaultCluster)
        .toClusterDescriptionView();
  }

  /** Build cluster from view helpers for CREATE - Disaggregated Storage */
  public ClusterDescriptionView buildClusterViewForDisaggregatedCluster(
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      final Boolean pEnvelope,
      final NDSGroup pNDSGroup,
      final ObjectId pOrgId)
      throws SvcException {
    final ApiAtlasClusterDescription20240805View defaultCluster =
        createDefaultCluster(pClusterDescriptionView, pNDSGroup, pOrgId, pEnvelope);

    final ClusterDescriptionView baseClusterDescriptionView =
        pClusterDescriptionView
            .toUpdatedClusterDescriptionView(defaultCluster)
            .toClusterDescriptionView();

    // Apply disaggregated storage specific configurations
    final ClusterDescriptionViewBuilder builder =
        baseClusterDescriptionView.toBuilder()
            .encryptionAtRestProvider(EncryptionAtRestProviderView.NONE)
            .backupEnabled(false)
            .diskBackupEnabled(true) // These cannot be disabled for DS.
            .pitEnabled(true)
            .clusterType(ClusterType.DISAGGREGATED.name())
            .storageSystem(StorageSystem.DISAGGREGATED_STORAGE);

    if (pClusterDescriptionView.getMongoDBVersion() != null) {
      builder.mongoDBVersion(pClusterDescriptionView.getMongoDBVersion());
    } else {
      final String customBuild =
          customMongoDbBuildSvc
              .findLatestDisaggCustomBuild()
              .get(MongoDbBuild.TRUE_NAME_FIELD)
              .toString();
      builder.mongoDBVersion(customBuild);
    }

    return builder.build();
  }

  /**
   * Build cluster view for disaggregated cluster UPDATE - preserves existing settings while
   * applying instance size changes
   */
  public ClusterDescriptionView buildClusterViewForDisaggregatedClusterUpdate(
      final ClusterDescriptionView pExistingCluster,
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      final NDSGroup pNDSGroup)
      throws SvcException {

    // Create existing cluster view from the current cluster (similar to
    // buildClusterDescriptionViewForUpdate)
    final ClusterDescriptionProcessArgsView processArgsView =
        ndsUISvc.getProcessArgsOrDefault(pExistingCluster.getGroupId(), pExistingCluster.getName());

    final ApiAtlasClusterDescription20240805View existingClusterView =
        new ApiAtlasClusterDescription20240805View(
            pExistingCluster,
            processArgsView,
            appSettings,
            ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
                pExistingCluster, pNDSGroup),
            pNDSGroup.getCloudProviderContainers(),
            onlineArchiveSvc.generateFederatedURI(
                pNDSGroup.getGroupId(),
                pExistingCluster.getName(),
                pExistingCluster.getMongoDBMajorVersion(),
                false));

    // Apply the requested changes to the existing cluster view (similar to
    // buildClusterDescriptionViewForUpdate)
    final ApiAtlasClusterDescription20240805View patchedView =
        pClusterDescriptionView.toUpdatedClusterDescriptionView(existingClusterView);

    // Convert back to ClusterDescriptionView, preserving disaggregated-specific configurations
    final ClusterDescriptionView updatedCluster = patchedView.toClusterDescriptionView();

    // Ensure disaggregated-specific configurations are preserved
    return updatedCluster.toBuilder()
        .encryptionAtRestProvider(EncryptionAtRestProviderView.NONE)
        .backupEnabled(false)
        .diskBackupEnabled(true) // These cannot be disabled for DS.
        .pitEnabled(true)
        .clusterType(ClusterType.DISAGGREGATED.name())
        .storageSystem(StorageSystem.DISAGGREGATED_STORAGE)
        .mongoDBVersion(
            pExistingCluster.getMongoDBVersion()) // Preserve MongoDB version from existing cluster
        .build();
  }

  public void validateLegacyClusterDescriptionViewForCreate(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final Group group,
      final Boolean envelope)
      throws SvcException {

    // name field validation
    validateForCreate(clusterDescriptionView, envelope);
    if (!ClusterDescription.VALID_NAME_PATTERN
        .matcher(clusterDescriptionView.getName())
        .matches()) {
      throw ApiErrorCode.CLUSTER_NAME_INVALID.exception(envelope, clusterDescriptionView.getName());
    }

    // shared validation for create and update of cluster
    validateCluster(clusterDescriptionView, envelope);

    if (clusterDescriptionView.getPaused()) {
      throw ApiErrorCode.CANNOT_CREATE_PAUSED_CLUSTER.exception(
          envelope, clusterDescriptionView.getName());
    }

    // Both replicationSpec and replicationSpecs fields should not be provided in the same request.
    // Only one or the other should be provided.
    if (clusterDescriptionView.hasReplicationSpecs()
        && clusterDescriptionView.hasReplicationSpec()) {
      throw ApiErrorCode.DUAL_REPLICATION_SPEC_SPECIFIED.exception(
          envelope, clusterDescriptionView.getName());
    }

    if (clusterDescriptionView.hasReplicationSpecs()) {
      if (!clusterDescriptionView.hasValidClusterType()) {
        throw ApiErrorCode.MISSING_OR_INVALID_ATTRIBUTE.exception(
            envelope, Fields.CLUSTER_TYPE_FIELD);
      }
      validateReplicationSpecs(clusterDescriptionView.getReplicationSpecs(), envelope);
    }

    if (!clusterDescriptionView.hasProviderSettings()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(envelope, Fields.PROVIDER_SETTINGS_FIELD);
    }

    if (clusterDescriptionView.getRootCertType() == RootCertType.DST) {
      throw ApiErrorCode.DST_ROOT_CERT_UNSUPPORTED.exception(envelope);
    }

    if (clusterDescriptionView.getClusterDescriptionClusterType()
        == ClusterDescription.ClusterType.GEOSHARDED) {
      if (!clusterDescriptionView.hasReplicationSpecs()) {
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(envelope, Fields.REPLICATION_SPECS_FIELD);
      }

      if (clusterDescriptionView.getReplicationSpecs().stream()
          .anyMatch(rsView -> !rsView.hasZoneName())) {
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
            envelope, ApiAtlasLegacyReplicationSpecView.ZONE_NAME_FIELD);
      }
    }

    final CloudProvider provider = clusterDescriptionView.getProviderSettings().getProviderName();

    if (clusterDescriptionView.hasBiConnector()) {
      validateBiConnectorForCreate(
          Set.of(provider),
          clusterDescriptionView.getBiConnector(),
          clusterDescriptionView.getSpecifiedNumAnalyticsNodes().orElse(0),
          envelope);
    }
    if (clusterDescriptionView.hasEncryptionAtRestProvider()) {
      validateEncryptionAtRestProvider(clusterDescriptionView, null, group, envelope);
    }
    if (clusterDescriptionView.hasMongoDBMajorVersion()) {
      validateMongoDBMajorVersion(clusterDescriptionView.getMongoDBMajorVersion(), envelope);
    }
    if (isFeatureFlagEnabled(FeatureFlag.ATLAS_CONTINUOUS_DELIVERY, appSettings, null, group)
        && VersionReleaseSystem.CONTINUOUS.equals(clusterDescriptionView.getVersionReleaseSystem())
        && clusterDescriptionView.getMongoDBMajorVersion() != null) {
      throw ApiErrorCode.CANNOT_SPECIFY_MONGODB_MAJOR_VERSION_WITH_CONTINUOUS_DELIVERY.exception(
          envelope);
    }
    validateProviderSettingsForCreate(clusterDescriptionView, group, envelope);
  }

  public ClusterDescriptionView buildClusterDescriptionViewForCreate(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final Group group,
      final NDSGroup ndsGroup,
      final Boolean envelope)
      throws SvcException {

    final ApiAtlasLegacyClusterDescriptionView defaultCluster =
        createDefaultClusterUsingProvider(
            clusterDescriptionView.getProviderSettings().getProviderName(),
            clusterDescriptionView,
            group,
            ndsGroup,
            envelope);

    final ApiAtlasLegacyClusterDescriptionView clusterDescriptionViewWithRootCertType =
        setDefaultRootCertPerEnv(defaultCluster);

    // We always enable encryptEBSVolume for AWS cluster description views regardless of user input
    // because this field has been deprecated and should not be disabled for newly created clusters.
    // Note that 'enableEncryptEBSVolumeIfNecessary' is a no-op for non-AWS cluster description
    // views.
    // Note: updating the ClusterDescriptionView here also applies the user's given values to the
    // default values created above.
    final ApiAtlasLegacyClusterDescriptionView completeView =
        enableEncryptEBSVolumeIfNecessary(
            clusterDescriptionView.toUpdatedClusterDescriptionView(
                clusterDescriptionViewWithRootCertType, envelope));

    return completeView.toClusterDescriptionView(null, envelope);
  }

  private ApiAtlasClusterDescription20240805View createDefaultCluster(
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      final NDSGroup pNDSGroup,
      final ObjectId pOrgId,
      final Boolean pEnvelope)
      throws SvcException {
    final ApiAtlasInstanceSizeView baseInstanceSizeView =
        pClusterDescriptionView.getReplicationSpecs().stream()
            .flatMap((replicationSpec) -> replicationSpec.getRegionConfigs().stream())
            .map(ApiAtlasRegionConfig20240805View::getElectableSpecs)
            .filter(Objects::nonNull)
            .map(ApiAtlasHardwareSpec20240805View::getInstanceSize)
            .findFirst()
            .orElseThrow(
                () ->
                    ApiErrorCode.MISSING_ATTRIBUTE.exception(
                        pEnvelope, ApiAtlasHardwareSpecView.INSTANCE_SIZE_FIELD));

    final ApiAtlasInstanceSizeView analyticsInstanceSizeView =
        pClusterDescriptionView.getReplicationSpecs().stream()
            .flatMap((replicationSpec) -> replicationSpec.getRegionConfigs().stream())
            .filter(
                regionConfig -> regionConfig instanceof ApiAtlasDedicatedRegionConfig20240805View)
            .filter(
                regionConfig ->
                    ((ApiAtlasDedicatedRegionConfig20240805View) regionConfig).getAnalyticsSpecs()
                        != null)
            .map(
                regionConfig ->
                    ((ApiAtlasDedicatedRegionConfig20240805View) regionConfig)
                        .getAnalyticsSpecs()
                        .getInstanceSize())
            .findFirst()
            .orElse(baseInstanceSizeView);

    final Map<NodeType, InstanceSize> instanceSizeMap = new HashMap<>();
    instanceSizeMap.putIfAbsent(NodeType.ELECTABLE, baseInstanceSizeView.getInstanceSize());
    instanceSizeMap.putIfAbsent(NodeType.ANALYTICS, analyticsInstanceSizeView.getInstanceSize());
    final Set<CloudProvider> providers = pClusterDescriptionView.getCloudProviders();

    final List<ClusterDescriptionView> defaultViews = new ArrayList<>();
    if (providers.size() > 1) {
      final CrossCloudInstanceSize baseCrossCloudInstanceSize =
          CrossCloudInstanceSize.getCrossCloudInstanceSize(baseInstanceSizeView.getInstanceSize())
              .orElseThrow(
                  () ->
                      ApiErrorCode.INSTANCE_SIZE_INVALID_FOR_PROVIDERS.exception(
                          pEnvelope, baseInstanceSizeView.getInstanceSize().toString()));
      final CrossCloudInstanceSize analyticsCrossCloudInstanceSize =
          CrossCloudInstanceSize.getCrossCloudInstanceSize(
                  analyticsInstanceSizeView.getInstanceSize())
              .orElseThrow(
                  () ->
                      ApiErrorCode.INSTANCE_SIZE_INVALID_FOR_PROVIDERS.exception(
                          pEnvelope, analyticsInstanceSizeView.getInstanceSize().toString()));
      for (final CloudProvider provider : providers) {
        final InstanceSize baseInstanceSizeCloudProviderSpecific =
            baseCrossCloudInstanceSize
                .getInstanceSizeForProvider(provider)
                .orElseThrow(
                    () ->
                        ApiErrorCode.INSTANCE_SIZE_INVALID_FOR_PROVIDERS.exception(
                            pEnvelope, baseInstanceSizeView.getInstanceSize().toString()));
        final InstanceSize analyticsInstanceSizeCloudProviderSpecific =
            analyticsCrossCloudInstanceSize
                .getInstanceSizeForProvider(provider)
                .orElseThrow(
                    () ->
                        ApiErrorCode.INSTANCE_SIZE_INVALID_FOR_PROVIDERS.exception(
                            pEnvelope, analyticsInstanceSizeView.getInstanceSize().toString()));
        final Map<NodeType, InstanceSize> crossCloudInstanceSizeMap = new HashMap<>();
        crossCloudInstanceSizeMap.putIfAbsent(
            NodeType.ELECTABLE, baseInstanceSizeCloudProviderSpecific);
        crossCloudInstanceSizeMap.putIfAbsent(
            NodeType.ANALYTICS, analyticsInstanceSizeCloudProviderSpecific);

        final ClusterDescriptionView defaultView =
            ndsUISvc.getDefaultReplicaSetForApi(
                provider, pNDSGroup, pOrgId, crossCloudInstanceSizeMap);
        defaultViews.add(defaultView);
      }
    } else {
      defaultViews.add(
          ndsUISvc.getDefaultReplicaSetForApi(
              providers.iterator().next(), pNDSGroup, pOrgId, instanceSizeMap));
    }

    final Double defaultDiskSizeGB =
        providers.contains(CloudProvider.AZURE)
            ? defaultViews.stream()
                .filter((view) -> view.getCloudProviders().contains(CloudProvider.AZURE))
                .findFirst()
                .map(ClusterDescriptionView::getDiskSizeGB)
                .get()
            : defaultViews.stream()
                .map((ClusterDescriptionView::getDiskSizeGB))
                .min(Double::compare)
                .get();

    final ClusterDescriptionView defaultView =
        defaultViews.get(0).toBuilder().diskSizeGB(defaultDiskSizeGB).build();

    final ClusterDescriptionProcessArgsView processArgsView = ndsUISvc.getDefaultProcessArgs();

    return new ApiAtlasClusterDescription20240805View(
        defaultView,
        processArgsView,
        appSettings,
        ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
            defaultView, pNDSGroup),
        pNDSGroup.getCloudProviderContainers(),
        onlineArchiveSvc.generateFederatedURI(
            pNDSGroup.getGroupId(),
            pClusterDescriptionView.getName(),
            defaultView.getMongoDBMajorVersion(),
            false));
  }

  private ApiAtlasLegacyClusterDescriptionView createDefaultClusterUsingProvider(
      final CloudProvider provider,
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final Group group,
      final NDSGroup ndsGroup,
      final Boolean envelope)
      throws SvcException {
    final ClusterDescriptionView view;
    final ClusterDescriptionProcessArgsView processArgsView = ndsUISvc.getDefaultProcessArgs();
    final ApiAtlasLegacyClusterDescriptionView newClusterDescriptionView;

    switch (provider) {
      case AWS:
        final ApiAtlasAWSProviderSettingsView awsProviderSettings =
            (ApiAtlasAWSProviderSettingsView) clusterDescriptionView.getProviderSettings();
        view =
            ndsUISvc.getDefaultReplicaSetForApi(
                provider,
                ndsGroup,
                group.getOrgId(),
                Map.of(
                    NodeType.ELECTABLE,
                    awsProviderSettings.getInstanceSize().getAWSInstanceSize()));
        newClusterDescriptionView =
            new ApiAtlasLegacyClusterDescriptionView(
                view,
                processArgsView,
                appSettings,
                ApiAtlasClusterDescriptionConnectionStringsUtil
                    .shouldExposePrivateStringsForCluster(view, ndsGroup),
                ndsGroup.getCloudProviderContainers(),
                onlineArchiveSvc.generateFederatedURI(
                    group.getId(),
                    clusterDescriptionView.getName(),
                    view.getMongoDBMajorVersion(),
                    false));
        break;
      case AZURE:
        {
          final ApiAtlasAzureProviderSettingsView azureProviderSettings =
              (ApiAtlasAzureProviderSettingsView) clusterDescriptionView.getProviderSettings();
          view =
              ndsUISvc.getDefaultReplicaSetForApi(
                  provider,
                  ndsGroup,
                  group.getOrgId(),
                  Map.of(
                      NodeType.ELECTABLE,
                      azureProviderSettings.getInstanceSize().getAzureInstanceSize()));
          newClusterDescriptionView =
              new ApiAtlasLegacyClusterDescriptionView(
                  view,
                  processArgsView,
                  appSettings,
                  ApiAtlasClusterDescriptionConnectionStringsUtil
                      .shouldExposePrivateStringsForCluster(view, ndsGroup),
                  ndsGroup.getCloudProviderContainers(),
                  onlineArchiveSvc.generateFederatedURI(
                      group.getId(),
                      clusterDescriptionView.getName(),
                      view.getMongoDBMajorVersion(),
                      false));
          break;
        }
      case GCP:
        {
          final ApiAtlasGCPProviderSettingsView gcpProviderSettings =
              (ApiAtlasGCPProviderSettingsView) clusterDescriptionView.getProviderSettings();
          view =
              ndsUISvc.getDefaultReplicaSetForApi(
                  provider,
                  ndsGroup,
                  group.getOrgId(),
                  Map.of(
                      NodeType.ELECTABLE,
                      gcpProviderSettings.getInstanceSize().getGCPInstanceSize()));
          newClusterDescriptionView =
              new ApiAtlasLegacyClusterDescriptionView(
                  view,
                  processArgsView,
                  appSettings,
                  ApiAtlasClusterDescriptionConnectionStringsUtil
                      .shouldExposePrivateStringsForCluster(view, ndsGroup),
                  ndsGroup.getCloudProviderContainers(),
                  onlineArchiveSvc.generateFederatedURI(
                      group.getId(),
                      clusterDescriptionView.getName(),
                      view.getMongoDBMajorVersion(),
                      false));
          break;
        }
      case FREE:
        final ApiAtlasFreeProviderSettingsView freeProviderSettings =
            (ApiAtlasFreeProviderSettingsView) clusterDescriptionView.getProviderSettings();
        newClusterDescriptionView =
            new ApiAtlasLegacyClusterDescriptionView(
                ndsUISvc.getDefaultReplicaSetForApi(
                    provider,
                    ndsGroup,
                    group.getOrgId(),
                    Map.of(
                        NodeType.ELECTABLE,
                        freeProviderSettings.getInstanceSize().getFreeInstanceSize())),
                processArgsView,
                appSettings,
                false,
                ndsGroup.getCloudProviderContainers());
        break;
      case FLEX:
        final ApiAtlasFlexProviderSettingsView flexProviderSettings =
            (ApiAtlasFlexProviderSettingsView) clusterDescriptionView.getProviderSettings();
        newClusterDescriptionView =
            new ApiAtlasLegacyClusterDescriptionView(
                ndsUISvc.getDefaultReplicaSetForApi(
                    provider,
                    ndsGroup,
                    group.getOrgId(),
                    Map.of(
                        NodeType.ELECTABLE,
                        flexProviderSettings.getInstanceSize().getFlexInstanceSize())),
                processArgsView,
                appSettings,
                false,
                ndsGroup.getCloudProviderContainers());
        break;
      default:
        throw ApiErrorCode.INVALID_PROVIDER.exception(envelope, provider);
    }

    return newClusterDescriptionView;
  }

  /*
   * Build cluster from view helpers for UPDATE
   */
  public ClusterDescriptionView buildClusterDescriptionViewForUpdate(
      final ClusterDescriptionView existing,
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final Group group,
      final NDSGroup ndsGroup,
      final String name,
      final Boolean envelope)
      throws Exception {
    if (existing.isServerlessTenantCluster()) {
      if (appSettings.isServerlessEnabled()) {
        throw ApiErrorCode.CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API.exception(
            envelope, existing.getName());
      } else {
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(envelope, existing.getName(), group.getId());
      }
    }

    if (existing.isFlexTenantCluster()) {
      if (!isFlexClusterValid(existing, group)) {
        if (appSettings.isFlexEnabled()) {
          throw ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.exception(
              envelope, existing.getName());
        } else {
          throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(
              envelope, existing.getName(), group.getId());
        }
      } else {
        throw ApiErrorCode.TENANT_CLUSTER_UPDATE_UNSUPPORTED.exception(envelope);
      }
    }

    if (Optional.ofNullable(existing.isCrossCloudCluster()).orElse(false)) {
      throw ApiErrorCode.MULTI_CLOUD_CLUSTER_INVALID.exception(envelope);
    }

    final ClusterDescriptionProcessArgsView processArgsView =
        ndsUISvc.getProcessArgsOrDefault(existing.getGroupId(), existing.getName());
    final ApiAtlasLegacyClusterDescriptionView existingClusterView =
        new ApiAtlasLegacyClusterDescriptionView(
            existing,
            processArgsView,
            appSettings,
            ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
                existing, ndsGroup),
            ndsGroup.getCloudProviderContainers(),
            onlineArchiveSvc.generateFederatedURI(
                group.getId(), name, existing.getMongoDBMajorVersion(), false));

    validatePauseStateRequest(clusterDescriptionView, existingClusterView, envelope);
    validateSelfManagedShardingRequest(clusterDescriptionView, existingClusterView, envelope);

    if (clusterDescriptionView.getRootCertType() == RootCertType.DST) {
      throw ApiErrorCode.DST_ROOT_CERT_UNSUPPORTED.exception(envelope);
    }

    if (clusterDescriptionView.hasName()
        && !clusterDescriptionView.getName().equals(existingClusterView.getName())) {
      throw ApiErrorCode.CLUSTER_CANNOT_CHANGE_NAME.exception(envelope);
    }

    if (clusterDescriptionView.hasReplicationSpecs()) {
      if (clusterDescriptionView.hasReplicationSpec()) {
        throw ApiErrorCode.DUAL_REPLICATION_SPEC_SPECIFIED.exception(
            envelope, clusterDescriptionView.getName());
      }

      final boolean hasRequiredReplicationSpecs =
          clusterDescriptionView.isGeoSharded()
              ? clusterDescriptionView.getReplicationSpecs().stream()
                  .allMatch(ApiAtlasLegacyReplicationSpecView::hasAllGeoShardedFields)
              : clusterDescriptionView.getReplicationSpecs().stream()
                  .allMatch(ApiAtlasLegacyReplicationSpecView::hasAllBasicFields);

      if (!hasRequiredReplicationSpecs) {
        throw ApiErrorCode.REPLICATION_SPECS_INVALID.exception(envelope);
      }
    }

    validateCluster(clusterDescriptionView, envelope);

    if (clusterDescriptionView.hasMongoDBVersion()
        && !(clusterDescriptionView instanceof ApiPrivateAtlasLegacyClusterDescriptionView)) {
      throw ApiErrorCode.ATTRIBUTE_READ_ONLY.exception(
          envelope, ApiAtlasBaseClusterDescriptionView.Fields.MONGODB_VERSION_FIELD);
    }

    if (clusterDescriptionView.hasMongoDBMajorVersion()) {
      validateMongoDBMajorVersion(clusterDescriptionView.getMongoDBMajorVersion(), envelope);
    }

    if (clusterDescriptionView.hasBiConnector()) {
      validateBiConnectorForUpdate(
          existingClusterView.getBiConnector(),
          clusterDescriptionView.getBiConnector(),
          clusterDescriptionView
              .getSpecifiedNumAnalyticsNodes()
              .orElse(existingClusterView.getSpecifiedNumAnalyticsNodes().orElse(0)),
          envelope);
    }
    // Allow termination protection to be updated even if Encryption At Rest is in a bad state so
    // that user can delete cluster if desired.
    if ((clusterDescriptionView.hasEncryptionAtRestProvider()
            || existingClusterView.hasEncryptionAtRestProvider())
        && !willUpdateOneStateOnly(
            clusterDescriptionView,
            ApiAtlasBaseClusterDescriptionView.Fields.TERMINATION_PROTECTION_ENABLED_FIELD)) {
      validateEncryptionAtRestProvider(
          clusterDescriptionView, existingClusterView, group, envelope);
    }

    validateProviderSettingsForUpdate(group, clusterDescriptionView, existingClusterView, envelope);

    final ApiAtlasLegacyClusterDescriptionView patchedView =
        clusterDescriptionView.toUpdatedClusterDescriptionView(existingClusterView, envelope);

    validateBackupSettings(patchedView, existingClusterView, envelope);

    if (patchedView.hasProviderSettings()
        && patchedView.getProviderSettings() instanceof ApiAtlasAWSProviderSettingsView) {
      validateAWSProviderSettings(patchedView, group, envelope, false);
    }
    if (patchedView.hasProviderSettings()
        && patchedView.getProviderSettings() instanceof ApiAtlasAzureProviderSettingsView) {
      validateAzureProviderSettings(patchedView, group, envelope);
    }

    return patchedView.toClusterDescriptionView(existing, envelope);
  }

  /*
   * Validation helper methods
   */
  public void validateProviderSettingsForUpdate(
      final Group group,
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final ApiAtlasLegacyClusterDescriptionView existingClusterView,
      final Boolean envelope) {
    if (clusterDescriptionView.hasProviderSettings()) {
      final CloudProvider newProviderName =
          clusterDescriptionView.getProviderSettings().getProviderName();
      final CloudProvider existingProviderName =
          existingClusterView.getProviderSettings().getProviderName();
      if (!Objects.equals(existingProviderName, newProviderName)) {
        throw ApiErrorCode.CLUSTER_CANNOT_CHANGE_PROVIDER_NAME.exception(envelope);
      }
      final ApiAtlasClusterProviderSettingsView providerSettingsView =
          clusterDescriptionView.getProviderSettings();
      final ApiAtlasClusterProviderSettingsView existingProviderSettingsView =
          existingClusterView.getProviderSettings();
      final Optional<Double> newDiskSizeGB =
          clusterDescriptionView.hasDiskSizeGB()
              ? Optional.of(clusterDescriptionView.getDiskSizeGB())
              : Optional.empty();
      if (providerSettingsView instanceof ApiAtlasAWSProviderSettingsView
          && existingProviderSettingsView instanceof ApiAtlasAWSProviderSettingsView) {
        validateAWSSettingsForUpdate(
            (ApiAtlasAWSProviderSettingsView) providerSettingsView,
            (ApiAtlasAWSProviderSettingsView) existingProviderSettingsView,
            newDiskSizeGB,
            envelope);
      } else if (providerSettingsView instanceof ApiAtlasAzureProviderSettingsView
          && existingProviderSettingsView instanceof ApiAtlasAzureProviderSettingsView) {
        validateAzureSettingsForUpdate(
            (ApiAtlasAzureProviderSettingsView) providerSettingsView,
            (ApiAtlasAzureProviderSettingsView) existingProviderSettingsView,
            envelope);
      } else if (providerSettingsView instanceof ApiAtlasGCPProviderSettingsView
          && existingProviderSettingsView instanceof ApiAtlasGCPProviderSettingsView) {
        validateGCPSettingsForUpdate(
            (ApiAtlasGCPProviderSettingsView) providerSettingsView,
            (ApiAtlasGCPProviderSettingsView) existingProviderSettingsView,
            newDiskSizeGB,
            envelope);
      } else if (providerSettingsView instanceof ApiAtlasFreeProviderSettingsView
          && existingProviderSettingsView instanceof ApiAtlasFreeProviderSettingsView) {
        throw ApiErrorCode.TENANT_CLUSTER_UPDATE_UNSUPPORTED.exception(envelope);
      } else {
        // If serverless instance is found here, we still throw CLUSTER_NOT_FOUND
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(
            envelope, clusterDescriptionView.getName(), clusterDescriptionView.getGroupId());
      }
    } else if (existingClusterView
        .getProviderSettings()
        .getProviderName()
        .equals(CloudProvider.FREE)) {
      throw ApiErrorCode.TENANT_CLUSTER_UPDATE_UNSUPPORTED.exception(envelope);
    } else if (existingClusterView
        .getProviderSettings()
        .getProviderName()
        .equals(CloudProvider.SERVERLESS)) {
      if (!appSettings.isServerlessEnabled()) {
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(
            envelope, clusterDescriptionView.getName(), clusterDescriptionView.getGroupId());
      }
    }
  }

  private ApiAtlasLegacyClusterDescriptionView enableEncryptEBSVolumeIfNecessary(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView) {
    final ApiAtlasClusterProviderSettingsView existingProviderSettings =
        clusterDescriptionView.getProviderSettings();
    if (existingProviderSettings instanceof ApiAtlasAWSProviderSettingsView) {
      final ApiAtlasAWSProviderSettingsView updatedProviderSettings =
          ((ApiAtlasAWSProviderSettingsView) existingProviderSettings).updateEncryptEBSVolume(true);
      return new ApiAtlasLegacyClusterDescriptionView.Builder(clusterDescriptionView)
          .providerSettings(updatedProviderSettings)
          .build();
    }
    return clusterDescriptionView;
  }

  private ApiAtlasLegacyClusterDescriptionView setDefaultRootCertPerEnv(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView) {
    if (clusterDescriptionView.getRootCertType() == null) {
      final RootCertType rootCertType =
          RootCertType.getRootCertTypeByString(appSettings.getDefaultRootCertType());
      return new ApiAtlasLegacyClusterDescriptionView.Builder(clusterDescriptionView)
          .rootCertType(rootCertType)
          .build();
    }
    return clusterDescriptionView;
  }

  private void validateEncryptionAtRestProvider(
      final ApiAtlasLegacyClusterDescriptionView newView,
      final ApiAtlasLegacyClusterDescriptionView existingView,
      final Group group,
      final Boolean envelope)
      throws SvcException {
    final ApiAtlasEncryptionAtRestProviderView encryptionAtRestProviderView =
        newView.hasEncryptionAtRestProvider()
            ? newView.getEncryptionAtRestProvider()
            : (existingView != null ? existingView.getEncryptionAtRestProvider() : null);
    final NDSGroup ndsGroup = ndsGroupSvc.ensureGroup(group.getId());
    if (encryptionAtRestProviderView == null
        || encryptionAtRestProviderView == ApiAtlasEncryptionAtRestProviderView.NONE) {
      return;
    }
    ClusterValidationUtil.verifyEncryptionAtRestAvailable(
        ndsGroup, encryptionAtRestProviderView.getEncryptionAtRestProvider());

    final ApiAtlasClusterProviderSettingsView providerSettingsView =
        newView.hasProviderSettings()
            ? newView.getProviderSettings()
            : existingView.getProviderSettings();
    if (providerSettingsView instanceof ApiAtlasFreeProviderSettingsView) {
      throw ApiErrorCode.CANNOT_USE_ENCRYPTION_AT_REST_TENANT.exception(envelope);
    }

    final boolean backupEnabled =
        newView.getBackupEnabled()
            ? newView.getBackupEnabled()
            : (existingView != null && existingView.getBackupEnabled());
    if (backupEnabled) {
      throw ApiErrorCode.CANNOT_USE_ENCRYPTION_AT_REST_BACKUP_TYPE.exception(envelope);
    }
  }

  /** Shared validations for creating and updating a cluster through the API */
  private void validateCluster(
      final ApiAtlasLegacyClusterDescriptionView clusterDescription, final Boolean envelope) {
    if (clusterDescription.hasMongoURI()) {
      throw ApiErrorCode.ATTRIBUTE_READ_ONLY.exception(envelope, Fields.MONGO_URI_FIELD);
    }
    if (clusterDescription.hasMongoURIWithOptions()) {
      throw ApiErrorCode.ATTRIBUTE_READ_ONLY.exception(
          envelope, Fields.MONGO_URI_WITH_OPTIONS_FIELD);
    }
    if (clusterDescription.hasMongoURIUpdated()) {
      throw ApiErrorCode.ATTRIBUTE_READ_ONLY.exception(
          envelope, Fields.MONGO_URI_LAST_UPDATED_FIELD);
    }
    if (clusterDescription.hasState()) {
      throw ApiErrorCode.ATTRIBUTE_READ_ONLY.exception(
          envelope, ApiAtlasBaseClusterDescriptionView.Fields.STATE_NAME_FIELD);
    }

    if (clusterDescription.hasNumShards()) {
      validateNumShards(clusterDescription.getNumShards(), envelope);
    }

    if (clusterDescription.hasReplicationFactor()
        && clusterDescription.getReplicationFactor() != 3
        && clusterDescription.getReplicationFactor() != 5
        && clusterDescription.getReplicationFactor() != 7) {
      throw ApiErrorCode.CLUSTER_REPLICATION_FACTOR_INVALID.exception(envelope);
    }
    if (clusterDescription.isTenantCluster() && clusterDescription.isSharded()) {
      throw ApiErrorCode.INVALID_TENANT_CLUSTER_TYPE.exception(envelope, Fields.CLUSTER_TYPE_FIELD);
    }
    if (clusterDescription.hasClusterType() && !clusterDescription.hasValidClusterType()) {
      throw ApiErrorCode.MISSING_OR_INVALID_ATTRIBUTE.exception(
          envelope, Fields.CLUSTER_TYPE_FIELD);
    }
  }

  private void validateReplicationSpecs(
      final List<ApiAtlasLegacyReplicationSpecView> apiAtlasLegacyReplicationSpecViews,
      final Boolean envelope) {
    if (apiAtlasLegacyReplicationSpecViews.isEmpty()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(envelope, Fields.REPLICATION_SPECS_FIELD);
    }
    for (final ApiAtlasLegacyReplicationSpecView replicationSpecView :
        apiAtlasLegacyReplicationSpecViews) {
      if (!replicationSpecView.hasNumShards()) {
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
            envelope, ApiAtlasLegacyReplicationSpecView.NUM_SHARDS_FIELD);
      }
      if (replicationSpecView.getRegionsConfig() == null) {
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
            envelope, ApiAtlasLegacyReplicationSpecView.REGIONS_CONFIG_FIELD);
      }
      for (final Map.Entry<String, ApiAtlasRegionSpecView> e :
          replicationSpecView.getRegionsConfig().entrySet()) {
        if (e.getKey().isEmpty()) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(envelope, "regionName");
        }
        if (!e.getValue().hasElectableNodes()) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
              envelope, ApiAtlasRegionSpecView.ELECTABLE_NODES_FIELD);
        }
        if (e.getValue().getElectableNodes() < 0) {
          throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
              envelope, ApiAtlasRegionSpecView.ELECTABLE_NODES_FIELD);
        }
        if (!e.getValue().hasReadOnlyNodes()) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
              envelope, ApiAtlasRegionSpecView.READ_ONLY_NODES_FIELD);
        }
        if (e.getValue().getReadOnlyNodes() < 0) {
          throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
              envelope, ApiAtlasRegionSpecView.READ_ONLY_NODES_FIELD);
        }
        if (!e.getValue().hasAnalyticsNodes()) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
              envelope, ApiAtlasRegionSpecView.ANALYTICS_NODES_FIELD);
        }
        if (e.getValue().getAnalyticsNodes() < 0) {
          throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
              envelope, ApiAtlasRegionSpecView.ANALYTICS_NODES_FIELD);
        }
        if (!e.getValue().hasPriority()) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
              envelope, ApiAtlasRegionSpecView.PRIORITY_FIELD);
        }
        if (e.getValue().getPriority() < 0) {
          throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
              envelope, ApiAtlasRegionSpecView.PRIORITY_FIELD);
        }
      }
    }
  }

  public void validateNumShards(final int pNumShards, final Boolean pEnvelope) {
    int maxShardsLimit = NDSDefaults.MAX_SHARDS_PER_CLUSTER;
    if (pNumShards <= 0 || pNumShards > maxShardsLimit) {
      throw ApiErrorCode.CLUSTER_NUM_SHARDS_INVALID.exception(pEnvelope, maxShardsLimit);
    }
  }

  public void validateReplicationSpecs(
      final List<ApiAtlasReplicationSpec20240805View> pApiAtlasReplicationSpecViews,
      final ObjectId groupId,
      final boolean pSupportsIndependentShardAutoscaling,
      final Boolean pEnvelope) {

    validateNumShards(pApiAtlasReplicationSpecViews.size(), pEnvelope);

    for (final ApiAtlasReplicationSpec20240805View replicationSpec :
        pApiAtlasReplicationSpecViews) {

      if (Optional.ofNullable(replicationSpec.getRegionConfigs()).isEmpty()) {
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
            pEnvelope, ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD);
      }

      validateRegionConfigs(replicationSpec.getRegionConfigs(), pEnvelope);
      validateInstanceSizes(replicationSpec.getRegionConfigs(), pEnvelope);
    }

    validateDiskSizeGB(pApiAtlasReplicationSpecViews, pEnvelope);
    validateFreeClusterCreation(pApiAtlasReplicationSpecViews, groupId, pEnvelope);
    validateAutoScalings(
        pApiAtlasReplicationSpecViews, pSupportsIndependentShardAutoscaling, pEnvelope);
  }

  public void validateInstanceSizes(
      final List<ApiAtlasRegionConfig20240805View> regionConfig, final Boolean pEnvelope) {
    final Set<String> electableInstanceSizes = new HashSet<>();
    final Set<String> readOnlyInstanceSizes = new HashSet<>();
    final Set<String> analyticsInstanceSizes = new HashSet<>();

    for (ApiAtlasRegionConfig20240805View region : regionConfig) {
      if (region instanceof ApiAtlasDedicatedRegionConfig20240805View dedicatedRegion) {

        // Validate electable instance sizes
        if (dedicatedRegion.getElectableSpecs() != null
            && dedicatedRegion.getElectableNodeCount() >= 1) {
          String instanceSize = dedicatedRegion.getElectableSpecs().getInstanceSize().name();
          electableInstanceSizes.add(instanceSize);
        }

        // Validate read-only instance sizes
        if (dedicatedRegion.getReadOnlySpecs() != null
            && dedicatedRegion.getReadOnlyNodeCount() >= 1) {
          String instanceSize = dedicatedRegion.getReadOnlySpecs().getInstanceSize().name();
          readOnlyInstanceSizes.add(instanceSize);
        }

        // Validate analytics instance sizes
        if (dedicatedRegion.getAnalyticsSpecs() != null
            && dedicatedRegion.getAnalyticsNodeCount() >= 1) {
          String instanceSize = dedicatedRegion.getAnalyticsSpecs().getInstanceSize().name();
          analyticsInstanceSizes.add(instanceSize);
        }
      }
    }

    // Validate electable instance sizes
    if (electableInstanceSizes.size() > 1) {
      throw ApiErrorCode.INSTANCE_SIZE_MUST_MATCH_IN_REGION_CONFIGS.exception(
          pEnvelope, electableInstanceSizes.size());
    }

    // Validate read-only instance sizes
    if (readOnlyInstanceSizes.size() > 1) {
      throw ApiErrorCode.READ_ONLY_NODE_SIZE_MUST_MATCH_IN_REGION_CONFIGS.exception(
          pEnvelope, readOnlyInstanceSizes.size());
    }

    // Validate analytics instance sizes
    if (analyticsInstanceSizes.size() > 1) {
      throw ApiErrorCode.ANALYTICS_NODE_SIZE_MUST_MATCH_IN_REGION_CONFIGS.exception(
          pEnvelope, analyticsInstanceSizes.size());
    }
  }

  /**
   * Later phases of Independent Shard Scaling (CLOUDP-167874) will support asymmetric disk sizes
   * for now, validate that all diskSizeGB values are consistent across hardware specs
   */
  protected void validateDiskSizeGB(
      final List<ApiAtlasReplicationSpec20240805View> pApiAtlasReplicationSpecViews,
      final Boolean pEnvelope) {
    final Set<Double> observedDiskSizeGBs =
        pApiAtlasReplicationSpecViews.stream()
            .flatMap(rs -> rs.getRegionConfigs().stream())
            .flatMap(
                rc -> {
                  final List<Double> diskSizeGBs = new ArrayList<>();

                  if (rc.getElectableSpecs() != null
                      && rc.getElectableSpecs().getDiskSizeGB() != null) {
                    diskSizeGBs.add(rc.getElectableSpecs().getDiskSizeGB());
                  }

                  if (rc instanceof final ApiAtlasDedicatedRegionConfig20240805View dedicatedRc) {
                    if (dedicatedRc.getAnalyticsSpecs() != null
                        && dedicatedRc.getAnalyticsSpecs().getDiskSizeGB() != null) {
                      diskSizeGBs.add(dedicatedRc.getAnalyticsSpecs().getDiskSizeGB());
                    }

                    if (dedicatedRc.getReadOnlySpecs() != null
                        && dedicatedRc.getReadOnlySpecs().getDiskSizeGB() != null) {
                      diskSizeGBs.add(dedicatedRc.getReadOnlySpecs().getDiskSizeGB());
                    }
                  }

                  return diskSizeGBs.stream();
                })
            .collect(Collectors.toSet());

    if (observedDiskSizeGBs.size() > 1) {
      throw ApiErrorCode.DISK_SIZE_GB_INCONSISTENT.exception(pEnvelope);
    }
  }

  protected void validateFreeClusterCreation(
      final List<ApiAtlasReplicationSpec20240805View> pReplicationSpecs,
      final ObjectId pGroupId,
      final Boolean pEnvelope) {
    final Set<String> instanceSizeNames =
        pReplicationSpecs.stream()
            .flatMap((replicationSpec) -> replicationSpec.getInstanceSizes().stream())
            .map(ApiAtlasInstanceSizeView::getInstanceSize)
            .map(InstanceSize::name)
            .collect(Collectors.toSet());

    if (instanceSizeNames.contains("M0")) {
      try {
        ndsUISvc.validateFreeClusterFromPublicApi(pGroupId);
      } catch (final SvcException pE) {
        throw ApiErrorCode.CANNOT_CREATE_FREE_CLUSTER_VIA_PUBLIC_API.exception(
            pEnvelope, pE.getMessage());
      }
    }
  }

  protected void validateAutoScalings(
      final List<ApiAtlasReplicationSpec20240805View> pReplicationSpecs,
      final boolean pSupportsIndependentShardAutoscaling,
      final Boolean pEnvelope) {
    if (pReplicationSpecs.stream().anyMatch(ApiAtlasReplicationSpec20240805View::isTenant)) {
      return;
    }

    final List<AutoScalingView> autoScalings =
        pReplicationSpecs.stream()
            .flatMap(
                (replicationSpec) ->
                    replicationSpec.getRegionConfigs().stream()
                        .map(
                            (regionConfig) ->
                                ((ApiAtlasDedicatedRegionConfig20240805View) regionConfig)
                                    .getAutoScaling())
                        .filter(Objects::nonNull))
            .map(ApiAtlasAutoScalingV15View::toAutoScalingView)
            .toList();

    final long totalRegionConfigs =
        pReplicationSpecs.stream()
            .mapToLong((replicationSpec) -> replicationSpec.getRegionConfigs().size())
            .sum();

    if (!autoScalings.isEmpty()) {
      if (autoScalings.size() != totalRegionConfigs) {
        throw ApiErrorCode.AUTO_SCALINGS_MUST_BE_IN_EVERY_REGION_CONFIG.exception(pEnvelope);
      }

      if (!autoScalings.stream()
          .allMatch((autoScaling) -> autoScaling.equals(autoScalings.get(0)))) {
        throw ApiErrorCode.AUTO_SCALINGS_MUST_MATCH.exception(pEnvelope);
      }

      final boolean anyBaseComputeScalingEnabled =
          autoScalings.stream().anyMatch(view -> view.getCompute().isEnabled());

      final Set<String> electableInstanceSizes =
          pReplicationSpecs.stream()
              .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
              .map(ApiAtlasRegionConfig20240805View::getElectableSpecs)
              .filter(Objects::nonNull)
              .map(ApiAtlasHardwareSpec20240805View::getInstanceSize)
              .map(ApiAtlasInstanceSizeView::name)
              .collect(Collectors.toSet());

      if (electableInstanceSizes.size() > 1
          && anyBaseComputeScalingEnabled
          && !pSupportsIndependentShardAutoscaling) {
        throw ApiErrorCode.ASYMMETRIC_BASE_AUTO_SCALING_UNAVAILABLE.exception(pEnvelope);
      }

      final Set<String> electableInstanceFamilyClasses =
          pReplicationSpecs.stream()
              .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
              .map(ApiAtlasRegionConfig20240805View::getElectableSpecs)
              .filter(Objects::nonNull)
              .map(ApiAtlasHardwareSpec20240805View::getInstanceSize)
              .map(ApiAtlasInstanceSizeView::getInstanceSize)
              .map(InstanceSize::getFamilyClass)
              .map(Enum::name)
              .collect(Collectors.toSet());

      if (electableInstanceFamilyClasses.size() > 1 && anyBaseComputeScalingEnabled) {
        throw ApiErrorCode.ASYMMETRIC_BASE_AUTO_SCALING_REQUIRES_SINGLE_FAMILY_CLASS.exception(
            pEnvelope,
            electableInstanceSizes.stream().sorted().collect(Collectors.joining(", ")),
            electableInstanceFamilyClasses.stream().sorted().collect(Collectors.joining(", ")));
      }
    }

    final List<AutoScalingView> analyticsAutoScalings =
        pReplicationSpecs.stream()
            .flatMap(
                (replicationSpec) ->
                    replicationSpec.getRegionConfigs().stream()
                        .map(
                            (regionConfig) ->
                                ((ApiAtlasDedicatedRegionConfig20240805View) regionConfig)
                                    .getAnalyticsAutoScaling())
                        .filter(Objects::nonNull))
            .map(ApiAtlasAutoScalingV15View::toAutoScalingView)
            .toList();

    if (!analyticsAutoScalings.isEmpty()) {
      if (analyticsAutoScalings.size() != totalRegionConfigs) {
        throw ApiErrorCode.AUTO_SCALINGS_MUST_BE_IN_EVERY_REGION_CONFIG.exception(pEnvelope);
      }

      if (!analyticsAutoScalings.stream()
          .allMatch(
              (analyticsAutoScaling) ->
                  analyticsAutoScaling.equals(analyticsAutoScalings.get(0)))) {
        throw ApiErrorCode.ANALYTICS_AUTO_SCALINGS_MUST_MATCH.exception(pEnvelope);
      }

      final boolean anyAnalyticsComputeScalingEnabled =
          analyticsAutoScalings.stream().anyMatch(view -> view.getCompute().isEnabled());

      final Set<String> analyticsInstanceSizes =
          pReplicationSpecs.stream()
              .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
              .filter(ApiAtlasDedicatedRegionConfig20240805View.class::isInstance)
              .map(ApiAtlasDedicatedRegionConfig20240805View.class::cast)
              .map(ApiAtlasDedicatedRegionConfig20240805View::getAnalyticsSpecs)
              .filter(Objects::nonNull)
              .map(ApiAtlasHardwareSpec20240805View::getInstanceSize)
              .map(ApiAtlasInstanceSizeView::name)
              .collect(Collectors.toSet());

      if (analyticsInstanceSizes.size() > 1
          && anyAnalyticsComputeScalingEnabled
          && !pSupportsIndependentShardAutoscaling) {
        throw ApiErrorCode.ASYMMETRIC_ANALYTICS_AUTO_SCALING_UNAVAILABLE.exception(pEnvelope);
      }

      final Set<String> analyticsInstanceFamilyClasses =
          pReplicationSpecs.stream()
              .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
              .filter(ApiAtlasDedicatedRegionConfig20240805View.class::isInstance)
              .map(ApiAtlasDedicatedRegionConfig20240805View.class::cast)
              .map(ApiAtlasDedicatedRegionConfig20240805View::getAnalyticsSpecs)
              .filter(Objects::nonNull)
              .map(ApiAtlasHardwareSpec20240805View::getInstanceSize)
              .map(ApiAtlasInstanceSizeView::getInstanceSize)
              .map(InstanceSize::getFamilyClass)
              .map(Enum::name)
              .collect(Collectors.toSet());

      if (analyticsInstanceFamilyClasses.size() > 1 && anyAnalyticsComputeScalingEnabled) {
        throw ApiErrorCode.ASYMMETRIC_ANALYTICS_AUTO_SCALING_REQUIRES_SINGLE_FAMILY_CLASS.exception(
            pEnvelope,
            analyticsInstanceSizes.stream().sorted().collect(Collectors.joining(", ")),
            analyticsInstanceFamilyClasses.stream().sorted().collect(Collectors.joining(", ")));
      }
    }

    // If the hardware has provisioned IOPS disks, validate the specified autoscaling limits are
    // compatible, as not all instance sizes support provisioned IOPS (see
    // AWSNDSInstanceSize.INSTANCE_TIERS_WITHOUT_PROVISION_IOPS_SUPPORT)
    // Note that provisioned IOPS are only supported in clusters where all nodes live in AWS land
    final boolean hasProvisionedIOPSDisks =
        pReplicationSpecs.stream()
            .filter(rspec -> rspec.getRegionConfigs() != null)
            .flatMap(rspec -> rspec.getRegionConfigs().stream())
            .map(ApiAtlasDedicatedRegionConfig20240805View.class::cast)
            .map(
                regionConfig ->
                    Optional.ofNullable(regionConfig.getElectableSpecs())
                        .orElse(regionConfig.getReadOnlySpecs()))
            .filter(Objects::nonNull)
            .map(ApiAtlasDedicatedHardwareSpec20240805View.class::cast)
            .anyMatch(ApiAtlasDedicatedHardwareSpec20240805View::hasProvisionedIOPS);
    if (hasProvisionedIOPSDisks) {
      validateAutoScalingLimitsCompatibleWithProvisionedIOPS(autoScalings, false, pEnvelope);
      validateAutoScalingLimitsCompatibleWithProvisionedIOPS(
          analyticsAutoScalings, true, pEnvelope);
    }
  }

  // Currently, provisioned IOPS are only supported in AWS. This method assumes AWS and is not
  // provider-agnostic
  public static void validateAutoScalingLimitsCompatibleWithProvisionedIOPS(
      List<AutoScalingView> autoScalings, boolean isAnalytics, Boolean envelope) {
    if (autoScalings.isEmpty()) {
      return;
    }

    // there is previous validation ensuring that all autoScaling items in the list are identical
    final ComputeAutoScalingView computeAutoScaling = autoScalings.get(0).getCompute();
    final JsonOptional<InstanceSize> minInstanceSize =
        computeAutoScaling.getMinInstanceSize(CloudProvider.AWS);
    final JsonOptional<InstanceSize> maxInstanceSize =
        computeAutoScaling.getMaxInstanceSize(CloudProvider.AWS);
    final boolean autoScalingLimitsCompatible =
        minInstanceSize
                .map(
                    instanceSize ->
                        !AWSNDSInstanceSize.INSTANCE_TIERS_WITHOUT_PROVISION_IOPS_SUPPORT.contains(
                            instanceSize))
                .orElse(true)
            && maxInstanceSize
                .map(
                    instanceSize ->
                        !AWSNDSInstanceSize.INSTANCE_TIERS_WITHOUT_PROVISION_IOPS_SUPPORT.contains(
                            instanceSize))
                .orElse(true);

    if (!autoScalingLimitsCompatible) {
      final String minInstanceSizeString =
          minInstanceSize.map(InstanceSize::getPrintableName).orElse("null");
      final String maxInstanceSizeString =
          maxInstanceSize.map(InstanceSize::getPrintableName).orElse("null");

      if (isAnalytics) {
        throw ApiErrorCode.ANALYTICS_AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS
            .exception(envelope, minInstanceSizeString, maxInstanceSizeString);
      } else {
        throw ApiErrorCode.AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.exception(
            envelope, minInstanceSizeString, maxInstanceSizeString);
      }
    }
  }

  protected void validateRegionConfigs(
      final List<ApiAtlasRegionConfig20240805View> pRegionConfigs, final Boolean pEnvelope) {
    for (final ApiAtlasRegionConfig20240805View regionConfig : pRegionConfigs) {
      if (Optional.ofNullable(regionConfig.getProviderName()).isEmpty()) {
        // if the cloud provider is not a valid one, an error will be thrown during deserialization
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
            pEnvelope, ApiAtlasRegionConfigView.PROVIDER_NAME_FIELD);
      }

      if (Optional.ofNullable(regionConfig.getRegionName()).isEmpty()) {
        throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
            pEnvelope, ApiAtlasRegionConfigView.REGION_NAME_FIELD);
      }

      CloudProvider provider = regionConfig.getProviderName().toCloudProvider();
      if (regionConfig instanceof ApiAtlasDedicatedRegionConfig20240805View) {
        if (regionConfig.getElectableSpecs() != null && regionConfig.getPriority() == null) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
              pEnvelope, ApiAtlasRegionConfigView.PRIORITY_FIELD);
        }
        validateHardwareSpecs(
            regionConfig.getElectableSpecs(),
            ((ApiAtlasDedicatedRegionConfig20240805View) regionConfig).getReadOnlySpecs(),
            ((ApiAtlasDedicatedRegionConfig20240805View) regionConfig).getAnalyticsSpecs(),
            pEnvelope);
      } else if (regionConfig instanceof ApiAtlasTenantRegionConfig20240805View) {
        if (regionConfig.getElectableSpecs() == null) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
              pEnvelope, ApiAtlasRegionConfigView.ELECTABLE_SPECS_FIELD);
        }
        if (((ApiAtlasTenantRegionConfig20240805View) regionConfig).getBackingProviderName()
            == null) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
              pEnvelope, ApiAtlasTenantRegionConfigView.BACKING_PROVIDER_NAME_FIELD);
        }
        validateHardwareSpecs(regionConfig.getElectableSpecs(), null, null, pEnvelope);
        provider =
            ((ApiAtlasTenantRegionConfig20240805View) regionConfig)
                .getBackingProviderName()
                .toCloudProvider();
      }

      if (RegionNameHelper.findByName(provider, regionConfig.getRegionName()).isEmpty()) {
        throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
            pEnvelope, ApiAtlasRegionConfigView.REGION_NAME_FIELD);
      }
    }

    // at least one regionConfig needs to have electableSpecs defined
    // total electable nodes must be at least 3
    final int totalElectableNodes =
        pRegionConfigs.stream()
            .map(ApiAtlasRegionConfig20240805View::getElectableNodeCount)
            .mapToInt(Integer::intValue)
            .sum();
    if (totalElectableNodes < ApiAtlasReplicationSpec20240805View.MIN_TOTAL_ELECTABLE_NODES) {
      throw ApiErrorCode.REPLICATION_SPECS_REQUIRES_ELECTABLE_SPECS.exception(pEnvelope);
    }
  }

  protected void validateHardwareSpecs(
      final ApiAtlasHardwareSpec20240805View pElectableSpecs,
      final ApiAtlasDedicatedHardwareSpec20240805View pReadOnlySpecs,
      final ApiAtlasDedicatedHardwareSpec20240805View pAnalyticsSpecs,
      final Boolean pEnvelope) {
    if (pElectableSpecs == null && pReadOnlySpecs == null && pAnalyticsSpecs == null) {
      throw ApiErrorCode.REGION_CONFIG_REQUIRES_HARDWARE_SPECS.exception(pEnvelope);
    }

    if (pElectableSpecs != null) {
      if (pElectableSpecs instanceof ApiAtlasTenantHardwareSpec20240805View) {
        if (pElectableSpecs.getInstanceSize() == null) {
          throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
              pEnvelope, ApiAtlasHardwareSpecView.INSTANCE_SIZE_FIELD);
        }
      } else {
        validateDedicatedHardwareSpec(
            (ApiAtlasDedicatedHardwareSpec20240805View) pElectableSpecs, pEnvelope);
      }
    }
    if (pReadOnlySpecs != null) {
      validateDedicatedHardwareSpec(pReadOnlySpecs, pEnvelope);
    }
    if (pAnalyticsSpecs != null) {
      validateDedicatedHardwareSpec(pAnalyticsSpecs, pEnvelope);
    }
  }

  private void validateDedicatedHardwareSpec(
      final ApiAtlasDedicatedHardwareSpec20240805View pHardwareSpec, final Boolean pEnvelope) {
    if (pHardwareSpec.getInstanceSize() == null) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasHardwareSpecView.INSTANCE_SIZE_FIELD);
    }
    if (pHardwareSpec.getNodeCount() == null) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasDedicatedHardwareSpecView.NODE_COUNT_FIELD);
    }
  }

  public void validateProviderSettingsForCreate(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final Group group,
      final Boolean envelope)
      throws SvcException {
    final ApiAtlasClusterProviderSettingsView providerSettingsView =
        clusterDescriptionView.getProviderSettings();

    if (providerSettingsView == null) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(envelope, Fields.PROVIDER_SETTINGS_FIELD);
    }

    if (providerSettingsView instanceof ApiAtlasAWSProviderSettingsView) {
      validateAWSProviderSettings(clusterDescriptionView, group, envelope, true);
    } else if (providerSettingsView instanceof ApiAtlasAzureProviderSettingsView) {
      validateAzureProviderSettings(clusterDescriptionView, group, envelope);
    } else if (providerSettingsView instanceof ApiAtlasGCPProviderSettingsView) {
      validateGCPProviderSettings(clusterDescriptionView, envelope);
    } else if (providerSettingsView instanceof ApiAtlasFreeProviderSettingsView) {
      validateFreeProviderSettings(clusterDescriptionView, group, envelope);
    } else if (providerSettingsView instanceof ApiAtlasFlexProviderSettingsView) {
      validateFlexProviderSettings(clusterDescriptionView, group, envelope);
    } else {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
          envelope, ApiAtlasServerlessProviderSettingsView.PROVIDER_NAME_FIELD);
    }
  }

  private void validateAWSProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final Group group,
      final Boolean envelope,
      final boolean isCreate)
      throws SvcException {

    final ApiAtlasAWSProviderSettingsView awsProviderSettingsView =
        (ApiAtlasAWSProviderSettingsView) clusterDescriptionView.getProviderSettings();
    if (!awsProviderSettingsView.hasRegionNameView()
        && !clusterDescriptionView.hasReplicationSpec()
        && !clusterDescriptionView.hasReplicationSpecs()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasAWSProviderSettingsView.REGION_NAME_FIELD);
    }

    if (!awsProviderSettingsView.hasInstanceSize()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasAWSProviderSettingsView.INSTANCE_SIZE_NAME_FIELD);
    }

    final ApiAtlasAWSInstanceSizeView awsInstanceSizeView =
        awsProviderSettingsView.getInstanceSize();
    final AWSNDSInstanceSize awsInstanceSize = awsInstanceSizeView.getAWSInstanceSize();

    if (awsInstanceSize.isNVMe()) {
      if (!clusterDescriptionView.getProviderBackupEnabled()) {
        throw ApiErrorCode.NVME_STORAGE_PROVIDER_BACKUP_REQUIRED.exception(envelope);
      }
      if (isBackupEnabledForGroup(group)) {
        throw ApiErrorCode.NVME_STORAGE_CONTINUOUS_BACKUP_UNSUPPORTED.exception(envelope);
      }
    }

    if (isCreate) {
      validateEncryptEBSVolume(awsProviderSettingsView, null, envelope);
    }

    final boolean hasDiskSizeGB = clusterDescriptionView.hasDiskSizeGB();

    if (hasDiskSizeGB) {
      validateDiskSize(clusterDescriptionView.getDiskSizeGB(), envelope);

      if (awsInstanceSize.isNVMe()
          && clusterDescriptionView.getDiskSizeGB() != awsInstanceSize.getDefaultDiskSizeGB()) {
        throw ApiErrorCode.CANNOT_MODIFY_DISK_SIZE_FOR_NVME_CLUSTER.exception(envelope);
      }
    }

    final int minIOPS =
        hasDiskSizeGB
            ? awsInstanceSize.getMinEBSIOPS(clusterDescriptionView.getDiskSizeGB())
            : awsInstanceSize.getMinEBSIOPS();
    final int maxIOPS =
        hasDiskSizeGB
            ? awsInstanceSize.getMaxIOPS(
                clusterDescriptionView.getDiskSizeGB(),
                Optional.ofNullable(awsProviderSettingsView.getAWSVolumeType())
                    .map(ApiAtlasAWSVolumeTypeView::getVolumeTypeView)
                    .map(VolumeTypeView::getVolumeType)
                    .orElse(null))
            : awsInstanceSize.getMaxEBSIOPS();
    final int diskSizeGB =
        hasDiskSizeGB
            ? (int) clusterDescriptionView.getDiskSizeGB()
            : awsInstanceSize.getDefaultDiskSizeGB();

    if (awsProviderSettingsView.hasDiskIOPS()) {
      final String formattedDiskSizeGB = String.format("%dGB", diskSizeGB);
      switch (awsInstanceSize) {
        case M10:
        case M20:
          if (ApiAtlasAWSVolumeTypeView.PROVISIONED.equals(
              awsProviderSettingsView.getAWSVolumeType())) {
            throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
                envelope,
                Fields.PROVIDER_SETTINGS_FIELD
                    + "."
                    + ApiAtlasAWSProviderSettingsView.VOLUME_TYPE_FIELD);
          }
          break;
        default:
          final ApiAtlasAWSVolumeTypeView ebsVolumeType =
              awsProviderSettingsView.getAWSVolumeType();
          if (awsInstanceSize.isNVMe()) {
            // For NVMe instances ignore passed diskIOPS that represents read/write diskIOPS on
            // NVMe instance sizes and not that of the typical EBS volume
          } else if (ebsVolumeType == null) {
            if (awsProviderSettingsView.getDiskIOPS() < minIOPS
                || awsProviderSettingsView.getDiskIOPS() > maxIOPS) {
              throw ApiErrorCode.CLUSTER_DISK_IOPS_INVALID.exception(
                  envelope,
                  awsProviderSettingsView.getDiskIOPS(),
                  formattedDiskSizeGB,
                  awsInstanceSize.name(),
                  "(auto)",
                  String.format(
                      "greater than the minimum required IOPS of %d and not exceed the maximum"
                          + " allowed IOPS of %d",
                      minIOPS, maxIOPS));
            }
          } else {
            // For STANDARD volumes, we will force the diskIOPS value to be
            // AWSNDSDefaults.EBS_MIN_IOPS_RATIO * diskSizeGB in the
            // AWSClusterDescriptionView::toClusterDescription or
            // AWSClusterDescriptionView::toUpdatedClusterDescription methods, therefore there is no
            // need to validate the disk IOPS for STANDARD volumes.

            if (ApiAtlasAWSVolumeTypeView.PROVISIONED.equals(ebsVolumeType)
                && (awsProviderSettingsView.getDiskIOPS() < minIOPS
                    || awsProviderSettingsView.getDiskIOPS() > maxIOPS)) {
              throw ApiErrorCode.CLUSTER_DISK_IOPS_INVALID.exception(
                  envelope,
                  awsProviderSettingsView.getDiskIOPS(),
                  formattedDiskSizeGB,
                  awsInstanceSize.name(),
                  ebsVolumeType.name(),
                  String.format(
                      "greater than the minimum required IOPS of %d and not exceed the maximum"
                          + " allowed IOPS of %d",
                      minIOPS, maxIOPS));
            }
          }
      }
    }

    if (awsProviderSettingsView.hasAWSVolumeType()) {
      if (awsInstanceSize.isNVMe()
          && awsProviderSettingsView.getAWSVolumeType() != ApiAtlasAWSVolumeTypeView.PROVISIONED) {
        throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
            envelope,
            Fields.PROVIDER_SETTINGS_FIELD
                + "."
                + ApiAtlasAWSProviderSettingsView.VOLUME_TYPE_FIELD);
      }
    }

    if (awsProviderSettingsView.hasAutoScaling()) {
      // need to validate min/max instance sizes here since we don't have this in the model
      // validations
      final ApiAtlasAWSInstanceSizeView minInstanceSizeView =
          Optional.ofNullable(awsProviderSettingsView.getAutoScaling().getCompute())
              .map(c -> c.getMinInstanceSize().orElse(null))
              .orElse(null);
      final ApiAtlasAWSInstanceSizeView maxInstanceSizeView =
          Optional.ofNullable(awsProviderSettingsView.getAutoScaling().getCompute())
              .map(c -> c.getMaxInstanceSize().orElse(null))
              .orElse(null);

      validateMinMaxInstanceSizeView(
          awsInstanceSizeView,
          minInstanceSizeView,
          maxInstanceSizeView,
          awsProviderSettingsView.getAWSVolumeType(),
          envelope);
    }
  }

  private void validateAzureProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final Group group,
      final Boolean envelope)
      throws SvcException {
    final ApiAtlasAzureProviderSettingsView azureProviderSettingsView =
        (ApiAtlasAzureProviderSettingsView) clusterDescriptionView.getProviderSettings();
    if (!azureProviderSettingsView.hasRegionNameView()
        && !clusterDescriptionView.hasReplicationSpec()
        && !clusterDescriptionView.hasReplicationSpecs()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasAzureProviderSettingsView.REGION_NAME_FIELD);
    }
    if (!azureProviderSettingsView.hasInstanceSize()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasAzureProviderSettingsView.INSTANCE_SIZE_NAME_FIELD);
    }

    final ApiAtlasAzureInstanceSizeView azureInstanceSizeView =
        azureProviderSettingsView.getInstanceSize();
    final AzureNDSInstanceSize azureInstanceSize = azureInstanceSizeView.getAzureInstanceSize();

    if (azureInstanceSize.isNVMe()) {
      if (!clusterDescriptionView.getProviderBackupEnabled()) {
        throw ApiErrorCode.NVME_STORAGE_PROVIDER_BACKUP_REQUIRED.exception(envelope);
      }
      if (isBackupEnabledForGroup(group)) {
        throw ApiErrorCode.NVME_STORAGE_CONTINUOUS_BACKUP_UNSUPPORTED.exception(envelope);
      }
    }

    if (clusterDescriptionView.hasDiskSizeGB()) {
      if (azureInstanceSize.isNVMe()
          && clusterDescriptionView.getDiskSizeGB() != azureInstanceSize.getDefaultDiskSizeGB()) {
        throw ApiErrorCode.CANNOT_MODIFY_DISK_SIZE_FOR_NVME_CLUSTER.exception(envelope);
      }
    }

    if (azureProviderSettingsView.hasDiskType()) {
      if (azureInstanceSize.isNVMe()) {
        throw ApiErrorCode.CANNOT_MODIFY_DISK_TYPE_FOR_AZURE_NVME_CLUSTER.exception(envelope);
      }
      validateDiskSize(
          azureProviderSettingsView.getDiskType().getAzureDiskType().getSizeGB(), envelope);
    }

    if (clusterDescriptionView.hasDiskSizeGB() && azureProviderSettingsView.hasDiskType()) {
      final double diskSizeGB = clusterDescriptionView.getDiskSizeGB();
      final AzureDiskType diskTypeFromSize = AzureDiskType.getApproximatePv1DiskType(diskSizeGB);
      final AzureDiskType specifiedDiskType =
          azureProviderSettingsView.getDiskType().getAzureDiskType();
      if (diskTypeFromSize != specifiedDiskType) {
        throw ApiErrorCode.DISK_SIZE_INVALID_FOR_AZURE.exception(envelope);
      }
    }

    if (azureProviderSettingsView.hasAutoScaling()) {
      final ApiAtlasAzureInstanceSizeView minInstanceSizeView =
          Optional.ofNullable(azureProviderSettingsView.getAutoScaling().getCompute())
              .map(c -> c.getMinInstanceSize().orElse(null))
              .orElse(null);
      final ApiAtlasAzureInstanceSizeView maxInstanceSizeView =
          Optional.ofNullable(azureProviderSettingsView.getAutoScaling().getCompute())
              .map(c -> c.getMaxInstanceSize().orElse(null))
              .orElse(null);

      validateMinMaxInstanceSizeView(
          azureInstanceSizeView, minInstanceSizeView, maxInstanceSizeView, envelope);
    }
  }

  private void validateGCPProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView, final Boolean envelope) {
    final ApiAtlasGCPProviderSettingsView gcpProviderSettingsView =
        (ApiAtlasGCPProviderSettingsView) clusterDescriptionView.getProviderSettings();
    if (!gcpProviderSettingsView.hasInstanceSize()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasGCPProviderSettingsView.INSTANCE_SIZE_NAME_FIELD);
    }
    if (!gcpProviderSettingsView.hasRegionNameView()
        && !clusterDescriptionView.hasReplicationSpec()
        && !clusterDescriptionView.hasReplicationSpecs()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasGCPProviderSettingsView.REGION_NAME_FIELD);
    }

    if (clusterDescriptionView.hasDiskSizeGB()) {
      validateDiskSize(clusterDescriptionView.getDiskSizeGB(), envelope);
    }

    final ApiAtlasGCPInstanceSizeView instanceSizeView = gcpProviderSettingsView.getInstanceSize();

    if (gcpProviderSettingsView.hasAutoScaling()) {
      final ApiAtlasGCPInstanceSizeView minInstanceSizeView =
          Optional.ofNullable(gcpProviderSettingsView.getAutoScaling().getCompute())
              .map(c -> c.getMinInstanceSize().orElse(null))
              .orElse(null);
      final ApiAtlasGCPInstanceSizeView maxInstanceSizeView =
          Optional.ofNullable(gcpProviderSettingsView.getAutoScaling().getCompute())
              .map(c -> c.getMaxInstanceSize().orElse(null))
              .orElse(null);

      validateMinMaxInstanceSizeView(
          instanceSizeView, minInstanceSizeView, maxInstanceSizeView, envelope);
    }
  }

  private void validateFreeProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final Group group,
      final Boolean envelope) {
    final ApiAtlasFreeProviderSettingsView freeProviderSettingsView =
        (ApiAtlasFreeProviderSettingsView) clusterDescriptionView.getProviderSettings();

    if (!freeProviderSettingsView.hasInstanceSize()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasFreeProviderSettingsView.INSTANCE_SIZE_NAME_FIELD);
    }
    if (!(clusterDescriptionView instanceof ApiPrivateAtlasLegacyClusterDescriptionView)
        && freeProviderSettingsView.getInstanceSize().equals(ApiAtlasFreeInstanceSizeView.M0)) {
      try {
        ndsUISvc.validateFreeClusterFromPublicApi(group.getOrgId(), group.getId());
      } catch (final SvcException pE) {
        throw ApiErrorCode.CANNOT_CREATE_FREE_CLUSTER_VIA_PUBLIC_API.exception(
            envelope, pE.getMessage());
      }
    }
    if (!freeProviderSettingsView.hasBackingProviderName()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasFreeProviderSettingsView.PROVIDER_NAME_FIELD);
    }
    if (!freeProviderSettingsView.hasRegionName()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasFreeProviderSettingsView.REGION_NAME_FIELD);
    }
    if (freeProviderSettingsView.hasTenantBackupEnabled()
        && !freeProviderSettingsView.getInstanceSize().equals(ApiAtlasFreeInstanceSizeView.M0)
        && !freeProviderSettingsView.getTenantBackupEnabled()) {
      throw ApiErrorCode.INVALID_TENANT_BACKUP_ENABLED.exception(
          envelope, "true", "M2/M5 clusters");
    }
    if (freeProviderSettingsView.getInstanceSize().equals(ApiAtlasFreeInstanceSizeView.M0)
        && freeProviderSettingsView.hasTenantBackupEnabled()
        && freeProviderSettingsView.getTenantBackupEnabled()) {
      throw ApiErrorCode.INVALID_TENANT_BACKUP_ENABLED.exception(envelope, "false", "M0 clusters");
    }
    if (freeProviderSettingsView.hasNextBackupDate()) {
      throw ApiErrorCode.ATTRIBUTE_READ_ONLY.exception(
          envelope, ApiAtlasFreeProviderSettingsView.NEXT_BACKUP_DATE_FIELD);
    }
    if (clusterDescriptionView.getBackupEnabled()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, Fields.BACKUP_ENABLED_FIELD);
    }
    if (clusterDescriptionView.getProviderBackupEnabled()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
          envelope, Fields.PROVIDER_BACKUP_ENABLED_FIELD);
    }
    if (clusterDescriptionView.hasReplicationFactor()
        && clusterDescriptionView.getReplicationFactor() != 3) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, Fields.REPLICATION_FACTOR_FIELD);
    }

    if (clusterDescriptionView.hasReplicationSpecs()
        && (clusterDescriptionView.getReplicationSpecs().size() != 1
            || clusterDescriptionView
                    .getReplicationSpecs()
                    .get(0)
                    .getRegionsConfig()
                    .values()
                    .iterator()
                    .next()
                    .getTotalNodes()
                != 3)) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, Fields.REPLICATION_SPECS_FIELD);
    }

    if (clusterDescriptionView.hasDiskSizeGB()) {
      if (!(clusterDescriptionView instanceof ApiPrivateAtlasLegacyClusterDescriptionView)
          && freeProviderSettingsView.getInstanceSize().equals(ApiAtlasFreeInstanceSizeView.M0)) {
        // m0 clusters valid size is 0.5, so we should not reject non-whole numbers
        validateM0DiskSize(clusterDescriptionView.getDiskSizeGB(), envelope);
      } else {
        validateDiskSize(clusterDescriptionView.getDiskSizeGB(), envelope);
      }
    }

    // auto-scaling is not supported for tenant clusters
    if (freeProviderSettingsView.hasAutoScaling()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, Fields.PROVIDER_SETTINGS_FIELD);
    }

    if (freeProviderSettingsView.hasRegionName()
        && freeProviderSettingsView.hasBackingProviderName()) {
      try {
        RegionNameHelper.findByNameOrElseThrow(
            freeProviderSettingsView.getBackingProvider(),
            freeProviderSettingsView.getRegionName());
      } catch (final IllegalStateException pE) {
        throw ApiErrorCode.INVALID_REGION.exception(
            envelope,
            freeProviderSettingsView.getRegionName(),
            freeProviderSettingsView.getBackingProviderName());
      }
    }
  }

  private void validateFlexProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final Group group,
      final Boolean envelope) {
    final ApiAtlasFlexProviderSettingsView flexProviderSettingsView =
        (ApiAtlasFlexProviderSettingsView) clusterDescriptionView.getProviderSettings();

    if (!flexProviderSettingsView.hasInstanceSize()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasFlexProviderSettingsView.INSTANCE_SIZE_NAME_FIELD);
    }
    if (!flexProviderSettingsView.hasBackingProviderName()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasFlexProviderSettingsView.PROVIDER_NAME_FIELD);
    }
    if (!flexProviderSettingsView.hasRegionName()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasFlexProviderSettingsView.REGION_NAME_FIELD);
    }
    if (flexProviderSettingsView.hasNextBackupDate()) {
      throw ApiErrorCode.ATTRIBUTE_READ_ONLY.exception(
          envelope, ApiAtlasFlexProviderSettingsView.NEXT_BACKUP_DATE_FIELD);
    }
    if (clusterDescriptionView.getBackupEnabled()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, Fields.BACKUP_ENABLED_FIELD);
    }
    if (clusterDescriptionView.getProviderBackupEnabled()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
          envelope, Fields.PROVIDER_BACKUP_ENABLED_FIELD);
    }
    if (clusterDescriptionView.hasReplicationFactor()
        && clusterDescriptionView.getReplicationFactor() != 3) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, Fields.REPLICATION_FACTOR_FIELD);
    }

    if (clusterDescriptionView.hasReplicationSpecs()
        && (clusterDescriptionView.getReplicationSpecs().size() != 1
            || clusterDescriptionView
                    .getReplicationSpecs()
                    .get(0)
                    .getRegionsConfig()
                    .values()
                    .iterator()
                    .next()
                    .getTotalNodes()
                != 3)) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, Fields.REPLICATION_SPECS_FIELD);
    }

    if (clusterDescriptionView.hasDiskSizeGB()) {
      validateDiskSize(clusterDescriptionView.getDiskSizeGB(), envelope);
    }

    // auto-scaling is not supported for tenant clusters
    if (flexProviderSettingsView.hasAutoScaling()) {
      throw ApiErrorCode.INVALID_ATTRIBUTE.exception(envelope, Fields.PROVIDER_SETTINGS_FIELD);
    }

    if (flexProviderSettingsView.hasRegionName()
        && flexProviderSettingsView.hasBackingProviderName()) {
      try {
        RegionNameHelper.findByNameOrElseThrow(
            flexProviderSettingsView.getBackingProvider(),
            flexProviderSettingsView.getRegionName());
      } catch (final IllegalStateException pE) {
        throw ApiErrorCode.INVALID_REGION.exception(
            envelope,
            flexProviderSettingsView.getRegionName(),
            flexProviderSettingsView.getBackingProviderName());
      }
    }
  }

  private void validateAWSSettingsForUpdate(
      final ApiAtlasAWSProviderSettingsView newAWSProviderSettingsView,
      final ApiAtlasAWSProviderSettingsView existingAWSProviderSettingsView,
      final Optional<Double> newDiskSizeGB,
      final Boolean envelope) {
    final ApiAtlasAWSInstanceSizeView instanceSizeView;
    if (newAWSProviderSettingsView.hasInstanceSize()) {
      instanceSizeView = newAWSProviderSettingsView.getInstanceSize();
    } else {
      instanceSizeView = existingAWSProviderSettingsView.getInstanceSize();
    }

    validateEncryptEBSVolume(newAWSProviderSettingsView, existingAWSProviderSettingsView, envelope);

    if (newAWSProviderSettingsView.hasAutoScaling()) {

      final ApiAtlasAWSInstanceSizeView minInstanceSizeView =
          Optional.ofNullable(newAWSProviderSettingsView.getAutoScaling().getCompute())
                  .map(c -> c.getMinInstanceSize().isSet())
                  .orElse(false)
              ? newAWSProviderSettingsView
                  .getAutoScaling()
                  .getCompute()
                  .getMinInstanceSize()
                  .orElse(null)
              : existingAWSProviderSettingsView
                  .getAutoScaling()
                  .getCompute()
                  .getMinInstanceSize()
                  .orElse(null);

      final ApiAtlasAWSInstanceSizeView maxInstanceSizeView =
          Optional.ofNullable(newAWSProviderSettingsView.getAutoScaling().getCompute())
                  .map(c -> c.getMaxInstanceSize().isSet())
                  .orElse(false)
              ? newAWSProviderSettingsView
                  .getAutoScaling()
                  .getCompute()
                  .getMaxInstanceSize()
                  .orElse(null)
              : existingAWSProviderSettingsView
                  .getAutoScaling()
                  .getCompute()
                  .getMaxInstanceSize()
                  .orElse(null);

      validateMinMaxInstanceSizeView(
          instanceSizeView,
          minInstanceSizeView,
          maxInstanceSizeView,
          newAWSProviderSettingsView.getAWSVolumeType(),
          envelope);
    }

    if (newDiskSizeGB.isPresent()) {
      validateDiskSize(newDiskSizeGB.get(), envelope);

      if (instanceSizeView.getAWSInstanceSize().isNVMe()
          && newDiskSizeGB.get() != instanceSizeView.getAWSInstanceSize().getDefaultDiskSizeGB()) {
        throw ApiErrorCode.CANNOT_MODIFY_DISK_SIZE_FOR_NVME_CLUSTER.exception(envelope);
      }
    }

    newDiskSizeGB.ifPresent(diskSizeGb -> validateDiskSize(diskSizeGb, envelope));
  }

  private void validateAzureSettingsForUpdate(
      final ApiAtlasAzureProviderSettingsView newAzureProviderSettings,
      final ApiAtlasAzureProviderSettingsView existingAzureProviderSettings,
      final Boolean envelope) {
    if (newAzureProviderSettings.hasRegionNameView()
        && !newAzureProviderSettings
            .getRegionName()
            .equals(existingAzureProviderSettings.getRegionName())) {
      throw ApiErrorCode.ATTRIBUTE_READ_ONLY.exception(
          envelope, ApiAtlasAWSProviderSettingsView.REGION_NAME_FIELD);
    }

    final ApiAtlasAzureInstanceSizeView instanceSizeView =
        newAzureProviderSettings.hasInstanceSize()
            ? newAzureProviderSettings.getInstanceSize()
            : existingAzureProviderSettings.getInstanceSize();

    if (newAzureProviderSettings.hasAutoScaling()) {

      final ApiAtlasAzureInstanceSizeView minInstanceSizeView =
          Optional.ofNullable(newAzureProviderSettings.getAutoScaling().getCompute())
                  .map(c -> c.getMinInstanceSize().isSet())
                  .orElse(false)
              ? newAzureProviderSettings
                  .getAutoScaling()
                  .getCompute()
                  .getMinInstanceSize()
                  .orElse(null)
              : existingAzureProviderSettings
                  .getAutoScaling()
                  .getCompute()
                  .getMinInstanceSize()
                  .orElse(null);

      final ApiAtlasAzureInstanceSizeView maxInstanceSizeView =
          Optional.ofNullable(newAzureProviderSettings.getAutoScaling().getCompute())
                  .map(c -> c.getMaxInstanceSize().isSet())
                  .orElse(false)
              ? newAzureProviderSettings
                  .getAutoScaling()
                  .getCompute()
                  .getMaxInstanceSize()
                  .orElse(null)
              : existingAzureProviderSettings
                  .getAutoScaling()
                  .getCompute()
                  .getMaxInstanceSize()
                  .orElse(null);

      validateMinMaxInstanceSizeView(
          instanceSizeView, minInstanceSizeView, maxInstanceSizeView, envelope);
    }

    if (newAzureProviderSettings.hasDiskType()) {
      if (instanceSizeView.getAzureInstanceSize().isNVMe()) {
        throw ApiErrorCode.CANNOT_MODIFY_DISK_TYPE_FOR_AZURE_NVME_CLUSTER.exception(envelope);
      }

      validateDiskSize(
          newAzureProviderSettings.getDiskType().getAzureDiskType().getSizeGB(), envelope);
    }
  }

  private void validateGCPSettingsForUpdate(
      final ApiAtlasGCPProviderSettingsView newGCPProviderSettings,
      final ApiAtlasGCPProviderSettingsView existingGCPProviderSettings,
      final Optional<Double> newDiskSizeGB,
      final Boolean envelope) {
    final ApiAtlasGCPInstanceSizeView instanceSizeView =
        newGCPProviderSettings.hasInstanceSize()
            ? newGCPProviderSettings.getInstanceSize()
            : existingGCPProviderSettings.getInstanceSize();

    if (newGCPProviderSettings.hasAutoScaling()) {

      final ApiAtlasGCPInstanceSizeView minInstanceSizeView =
          Optional.ofNullable(newGCPProviderSettings.getAutoScaling().getCompute())
                  .map(c -> c.getMinInstanceSize().isSet())
                  .orElse(false)
              ? newGCPProviderSettings
                  .getAutoScaling()
                  .getCompute()
                  .getMinInstanceSize()
                  .orElse(null)
              : existingGCPProviderSettings
                  .getAutoScaling()
                  .getCompute()
                  .getMinInstanceSize()
                  .orElse(null);

      final ApiAtlasGCPInstanceSizeView maxInstanceSizeView =
          Optional.ofNullable(newGCPProviderSettings.getAutoScaling().getCompute())
                  .map(c -> c.getMaxInstanceSize().isSet())
                  .orElse(false)
              ? newGCPProviderSettings
                  .getAutoScaling()
                  .getCompute()
                  .getMaxInstanceSize()
                  .orElse(null)
              : existingGCPProviderSettings
                  .getAutoScaling()
                  .getCompute()
                  .getMaxInstanceSize()
                  .orElse(null);

      validateMinMaxInstanceSizeView(
          instanceSizeView, minInstanceSizeView, maxInstanceSizeView, envelope);
    }

    newDiskSizeGB.ifPresent(diskSizeGB -> validateDiskSize(diskSizeGB, envelope));
  }

  private boolean isBackupEnabledForGroup(final Group group) throws SvcException {
    return ndsUISvc.getClusterDescriptions(group.getId()).stream()
        .anyMatch(ClusterDescriptionView::isBackupEnabled);
  }

  private void validateMinMaxInstanceSizeView(
      final ApiAtlasInstanceSizeView instanceSizeView,
      final ApiAtlasInstanceSizeView minInstanceSizeView,
      final ApiAtlasInstanceSizeView maxInstanceSizeView,
      final Boolean envelope) {
    validateMinMaxInstanceSizeView(
        instanceSizeView, minInstanceSizeView, maxInstanceSizeView, null, envelope);
  }

  private void validateMinMaxInstanceSizeView(
      final ApiAtlasInstanceSizeView instanceSizeView,
      final ApiAtlasInstanceSizeView minInstanceSizeView,
      final ApiAtlasInstanceSizeView maxInstanceSizeView,
      final ApiAtlasAWSVolumeTypeView awsVolumeTypeView,
      final Boolean envelope) {

    if (instanceSizeView == null) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          envelope, ApiAtlasClusterProviderSettingsView.INSTANCE_SIZE_NAME_FIELD);
    }

    final InstanceSize instanceSize = instanceSizeView.getInstanceSize();

    if (minInstanceSizeView != null) {
      final InstanceSize minInstanceSize = minInstanceSizeView.getInstanceSize();
      try {
        if (minInstanceSize.isGreaterThan(instanceSize)) {
          throw ApiErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_INVALID.exception(
              envelope, minInstanceSize.name(), instanceSize.name());
        }
      } catch (final IllegalArgumentException pE) {
        throw ApiErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_FAMILY_INVALID.exception(
            envelope, minInstanceSize.name(), instanceSize.name());
      }
    }
    if (maxInstanceSizeView != null) {
      final InstanceSize maxInstanceSize = maxInstanceSizeView.getInstanceSize();
      try {
        if (maxInstanceSize.isLessThan(instanceSize)) {
          throw ApiErrorCode.COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_INVALID.exception(
              envelope, maxInstanceSize.name(), instanceSize.name());
        }
      } catch (final IllegalArgumentException pE) {
        throw ApiErrorCode.COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_FAMILY_INVALID.exception(
            envelope, maxInstanceSize.name(), instanceSize.name());
      }
    }

    // special check for nodes in AWS with provisioned IOPS disks. In this configuration, we need to
    // confirm autoscaling limits don't include instance sizes that are not allowed according to
    // AWSNDSInstanceSize::INSTANCE_TIERS_WITHOUT_PROVISION_IOPS_SUPPORT
    // Note that provisioned IOPS is only supported in clusters where all nodes live in AWS land
    if (instanceSizeView instanceof ApiAtlasAWSInstanceSizeView) {
      if (awsVolumeTypeView == ApiAtlasAWSVolumeTypeView.PROVISIONED) {
        final Optional<ApiAtlasInstanceSizeView> minInstanceSizeViewOptional =
            Optional.ofNullable(minInstanceSizeView);
        final Optional<ApiAtlasInstanceSizeView> maxInstanceSizeViewOptional =
            Optional.ofNullable(maxInstanceSizeView);
        final boolean autoScalingLimitsCompatible =
            minInstanceSizeViewOptional
                    .map(
                        minView ->
                            !AWSNDSInstanceSize.INSTANCE_TIERS_WITHOUT_PROVISION_IOPS_SUPPORT
                                .contains(minView.getInstanceSize()))
                    .orElse(true)
                && maxInstanceSizeViewOptional
                    .map(
                        maxView ->
                            !AWSNDSInstanceSize.INSTANCE_TIERS_WITHOUT_PROVISION_IOPS_SUPPORT
                                .contains(maxView.getInstanceSize()))
                    .orElse(true);
        if (!autoScalingLimitsCompatible) {
          throw ApiErrorCode.AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.exception(
              envelope,
              minInstanceSizeViewOptional
                  .map(minView -> minView.getInstanceSize().getPrintableName())
                  .orElse("null"),
              maxInstanceSizeViewOptional
                  .map(maxView -> maxView.getInstanceSize().getPrintableName())
                  .orElse("null"));
        }
      }
    }
  }

  void validateDiskSize(final double diskSizeGB, final Boolean envelope) {
    if (diskSizeGB <= 0 || !MathUtils.isWholeNumber(diskSizeGB)) {
      throw ApiErrorCode.CLUSTER_DISK_SIZE_NOT_WHOLE_NUMBER.exception(envelope, diskSizeGB);
    }
  }

  public void validateM0DiskSize(final double diskSizeGB, final Boolean envelope) {
    if (diskSizeGB != 0.5) {
      throw ApiErrorCode.CLUSTER_DISK_SIZE_INVALID.exception(envelope, diskSizeGB);
    }
  }

  /**
   * This method should be called when {@link NDSErrorCode#CLUSTER_NAME_TOO_LONG} is thrown during
   * cluster validation. <br>
   * If the project has private IP ("peering-only") mode enabled, the cluster name is limited to the
   * unique prefix length in {@link NDSClusterSvc#verifyPrivateIpModeClusterNameLength(NDSGroup,
   * ClusterDescription)}. Otherwise, the max cluster name length is used in {@link
   * NDSClusterSvc#verifyClusterNameLength(ClusterDescription)}<br>
   * See <a href="https://www.mongodb.com/docs/atlas/reference/atlas-limits/#ref-1-id1">the docs</a>
   * for details. Note that this mode is deprecated on Atlas and this code is safe to delete once
   * there are no more projects with this setting enabled.
   *
   * @param ndsGroup the cluster's group.
   * @return the correct max cluster length for the cluster.
   */
  public int getMaxClusterLengthForError(final NDSGroup ndsGroup) {
    return ndsGroup.getPrivateIpMode()
        ? ndsGroup.getLimits().getClusterNameUniquePrefixLength()
        : ndsUISvc.getMaxClusterNameLength();
  }

  private void validateEncryptEBSVolume(
      final ApiAtlasAWSProviderSettingsView awsProviderSettingsView,
      final ApiAtlasAWSProviderSettingsView existingAWSProviderSettingsView,
      final Boolean envelope) {

    if (!awsProviderSettingsView.hasEncryptEBSVolume()) {
      return;
    }
    final boolean existingClusterHasUnencryptedVolume =
        existingAWSProviderSettingsView != null
            && !existingAWSProviderSettingsView.getEncryptEBSVolume();

    final boolean newClusterHasUnencryptedVolume = !awsProviderSettingsView.getEncryptEBSVolume();

    if (!existingClusterHasUnencryptedVolume && newClusterHasUnencryptedVolume) {
      throw ApiErrorCode.INVALID_VOLUME_ENCRYPTION.exception(
          envelope, ApiAtlasCloudProviderView.PROVIDER_AWS);
    }
  }

  /*
  Ideally, validations that are shared by the API and UI should live in a shared validation layer.
  Currently, this validation is duplicated in ApiAtlasClusterDescriptionV15Resource::validatePauseStateRequest and ClusterValidationUtil::verifyPauseState.
  CLOUDP-164994 has been created to address
  */
  private void validatePauseStateRequest(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView,
      final ApiAtlasLegacyClusterDescriptionView existingClusterView,
      final Boolean envelope)
      throws Exception {
    final boolean willUpdatePauseStateOnly =
        willUpdateOneStateOnly(clusterDescriptionView, Fields.PAUSED_FIELD);

    if (existingClusterView.getPaused() && !willUpdatePauseStateOnly) {
      throw ApiErrorCode.CANNOT_UPDATE_PAUSED_CLUSTER.exception(
          envelope, existingClusterView.getName());
    }

    if (existingClusterView.getPaused() && clusterDescriptionView.getPaused()) {
      throw ApiErrorCode.CLUSTER_ALREADY_PAUSED.exception(envelope, existingClusterView.getName());
    }

    if (!existingClusterView.getPaused()
        && clusterDescriptionView.hasPaused()
        && clusterDescriptionView.getPaused()
        && !willUpdatePauseStateOnly) {
      throw ApiErrorCode.CANNOT_UPDATE_AND_PAUSE_CLUSTER.exception(
          envelope, existingClusterView.getName());
    }
  }

  private void validateSelfManagedShardingRequest(
      final ApiAtlasLegacyClusterDescriptionView pNewClusterDescriptionView,
      final ApiAtlasLegacyClusterDescriptionView pExistingClusterDescriptionView,
      final Boolean pEnvelope)
      throws Exception {
    final boolean transitioningFromReplicaSetToGeoSharding =
        Objects.equals(
                pNewClusterDescriptionView.getClusterType(), ClusterType.GEOSHARDED.toString())
            && Objects.equals(
                pExistingClusterDescriptionView.getClusterType(),
                ClusterType.REPLICASET.toString());

    if (pNewClusterDescriptionView.getSelfManagedSharding() != null
        && pNewClusterDescriptionView.isSelfManagedSharding()
            != pExistingClusterDescriptionView.isSelfManagedSharding()
        && !transitioningFromReplicaSetToGeoSharding) {
      throw ApiErrorCode.CANNOT_MODIFY_GLOBAL_CLUSTER_MANAGEMENT_SETTING.exception(
          pEnvelope, pExistingClusterDescriptionView.getName());
    }
  }

  private boolean willUpdateOneStateOnly(
      final ApiAtlasLegacyClusterDescriptionView clusterDescriptionView, final String field)
      throws Exception {
    final JSONObject viewObject =
        new JSONObject(
            CustomJacksonJsonProvider.createObjectMapper()
                .writeValueAsString(clusterDescriptionView));

    return viewObject.length() == 1 && viewObject.has(field);
  }

  /**
   * Validate backup settings for the new cluster description.
   *
   * <p>TODO: throw a 400 for any ClusterDescription where the Storage System is
   * DISAGGREGATED_STORAGE but the cluster does not have diskBackupEnabled or pitEnabled. <a
   * href="https://jira.mongodb.org/browse/CLOUDP-303073">...</a>
   */
  private void validateBackupSettings(
      final ApiAtlasLegacyClusterDescriptionView newClusterDescriptionView,
      final ApiAtlasLegacyClusterDescriptionView existingClusterDescriptionView,
      final Boolean envelope) {

    final CloudProvider cloudProvider =
        existingClusterDescriptionView.getProviderSettings().getProviderName();

    final boolean newProviderBackupEnabled = newClusterDescriptionView.getProviderBackupEnabled();

    if (cloudProvider == CloudProvider.AWS) {
      final ApiAtlasAWSProviderSettingsView awsProviderSettings =
          (ApiAtlasAWSProviderSettingsView) newClusterDescriptionView.getProviderSettings();
      final boolean isNVMe =
          Optional.ofNullable(awsProviderSettings)
              .map(ApiAtlasAWSProviderSettingsView::getInstanceSize)
              .map(ApiAtlasAWSInstanceSizeView::getAWSInstanceSize)
              .map(AWSInstanceSize::isNVMe)
              .orElse(false);
      if (isNVMe && !newProviderBackupEnabled) {
        throw ApiErrorCode.NVME_STORAGE_PROVIDER_BACKUP_REQUIRED.exception(envelope);
      }
    }

    if (cloudProvider == CloudProvider.AZURE) {
      final ApiAtlasAzureProviderSettingsView azureProviderSettings =
          (ApiAtlasAzureProviderSettingsView) newClusterDescriptionView.getProviderSettings();
      final boolean isNVMe =
          Optional.ofNullable(azureProviderSettings)
              .map(ApiAtlasAzureProviderSettingsView::getInstanceSize)
              .map(ApiAtlasAzureInstanceSizeView::getAzureInstanceSize)
              .map(AzureNDSInstanceSize::isNVMe)
              .orElse(false);
      if (isNVMe && !newProviderBackupEnabled) {
        throw ApiErrorCode.NVME_STORAGE_PROVIDER_BACKUP_REQUIRED.exception(envelope);
      }
    }

    if (!newClusterDescriptionView.getBackupEnabled()
        && !newClusterDescriptionView.getProviderBackupEnabled()) {
      return;
    }

    final boolean newBackupEnabled = newClusterDescriptionView.getBackupEnabled();

    if (newBackupEnabled && newProviderBackupEnabled) {
      throw ApiErrorCode.ATLAS_BACKUP_CONFLICTING_OPTIONS.exception(envelope);
    }
  }

  public List<ApiAtlasTagView> saveClusterTags(
      final ClusterDescriptionView clusterDescriptionView,
      final List<ApiAtlasTagView> tags,
      final Group group,
      final AuditInfo auditInfo,
      final Boolean envelope) {
    if (tags == null
        || !featureFlagSvc.isFeatureFlagEnabled(FeatureFlag.RESOURCE_TAG_COMPONENT, null, group)) {
      return null;
    }

    final ResourceId parentResourceId =
        ResourceId.builder(ResourceService.CLOUD.toString(), ResourceType.PROJECT.toString())
            .id(group.getId().toString())
            .build();

    final ResourceId resourceId =
        ResourceId.builder(ResourceService.CLOUD.toString(), ResourceType.CLUSTER.toString())
            .id(clusterDescriptionView.getUniqueId().toString())
            .parent(parentResourceId)
            .build();

    final List<ApiAtlasTagView> newTags =
        apiResourceTagUtil.saveTags(group.getOrgId(), resourceId, tags, auditInfo, envelope);

    return newTags != null && newTags.size() == tags.size() ? tags : null;
  }

  public List<ApiAtlasTagView> getClusterTags(
      final ClusterDescriptionView clusterView, final Group group) {
    if (!featureFlagSvc.isFeatureFlagEnabled(FeatureFlag.RESOURCE_TAG_COMPONENT, null, group)) {
      return null;
    }

    final ResourceId parentResourceId =
        ResourceId.builder(ResourceService.CLOUD.toString(), ResourceType.PROJECT.toString())
            .id(group.getId().toString())
            .build();

    final ResourceId resourceId =
        ResourceId.builder(ResourceService.CLOUD.toString(), ResourceType.CLUSTER.toString())
            .id(clusterView.getUniqueId().toString())
            .parent(parentResourceId)
            .build();

    return apiResourceTagUtil.getTags(resourceId);
  }

  public void validateDeleteClusterRetainBackups(
      final ClusterDescriptionView clusterDescriptionView,
      final ApiBooleanParam retainBackups,
      final Boolean envelope)
      throws SvcException {
    if (ndsClusterConversionSvc
        .createProjectAgnosticDefaultClusterDescription(clusterDescriptionView)
        .isDedicatedCluster()) {
      if (retainBackups.getValue() == Boolean.FALSE
          && cpsSvc.isDataProtectionEnabled(clusterDescriptionView.getGroupId(), false)) {
        throw ApiErrorCode.CANNOT_DELETE_SNAPSHOT_WITH_BACKUP_COMPLIANCE_POLICY.exception(envelope);
      }
    } else {
      if (retainBackups.getValue() == Boolean.TRUE) {
        throw ApiErrorCode.INVALID_QUERY_PARAMETER.exception(envelope, "retainBackups");
      }
    }
  }

  protected void validateTenantCluster(
      final ApiAtlasClusterDescription20240805View pClusterDescriptionView,
      final Boolean pEnvelope) {
    if (pClusterDescriptionView.getBiConnector() != null) {
      throw ApiErrorCode.TENANT_ATTRIBUTE_READ_ONLY.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.BI_CONNECTOR_FIELD);
    }

    if (pClusterDescriptionView.getEncryptionAtRestProvider() != null) {
      throw ApiErrorCode.TENANT_ATTRIBUTE_READ_ONLY.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.ENCRYPTION_AT_REST_PROVIDER_FIELD);
    }

    // no hardware specs should have diskSizeGB
    final boolean anySpecsWithDiskSizeGB =
        pClusterDescriptionView.getReplicationSpecs().stream()
            .flatMap(rs -> rs.getRegionConfigs().stream())
            .anyMatch(rc -> rc.getElectableSpecs().getDiskSizeGB() != null);

    if (anySpecsWithDiskSizeGB) {
      throw ApiErrorCode.TENANT_ATTRIBUTE_READ_ONLY.exception(
          pEnvelope, ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD);
    }

    if (pClusterDescriptionView.getMongoDBMajorVersion() != null) {
      throw ApiErrorCode.TENANT_ATTRIBUTE_READ_ONLY.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.MONGODB_MAJOR_VERSION_FIELD);
    }

    if (!pClusterDescriptionView.getClusterType().equals("REPLICASET")) {
      throw ApiErrorCode.INVALID_TENANT_CLUSTER_TYPE.exception(pEnvelope);
    }

    if (pClusterDescriptionView.getReplicationSpecs().size() > 1) {
      throw ApiErrorCode.INVALID_TENANT_CONFIGURATION_REPLICATION_SPECS.exception(pEnvelope);
    }

    if (pClusterDescriptionView.getReplicationSpecs().stream()
            .mapToLong(rs -> rs.getRegionConfigs().size())
            .sum()
        > 1) {
      throw ApiErrorCode.INVALID_TENANT_CONFIGURATION_REGION_CONFIGS.exception(pEnvelope);
    }

    if (pClusterDescriptionView.getPitEnabled() != null) {
      throw ApiErrorCode.TENANT_ATTRIBUTE_READ_ONLY.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.PIT_ENABLED_FIELD);
    }

    if (pClusterDescriptionView.getBackupEnabled() != null) {
      throw ApiErrorCode.TENANT_ATTRIBUTE_READ_ONLY.exception(
          pEnvelope, ApiAtlasClusterDescription20240805View.BACKUP_ENABLED_FIELD);
    }
  }

  /**
   * @param pAllowAsymmetricClusters is always true when it's used in v0530API or when the caller is
   *     not supported in the newest api version
   * @param pIncludeDeletedClustersWithRetainedBackups
   * @return a {@link ClusterDescriptionView} for an existing cluster if {@link
   *     ClusterDescriptionView#isServerlessTenantCluster()} is false
   */
  public ClusterDescriptionView getClusterDescriptionViewV2AndAbove(
      final Group pGroup,
      final String pClusterName,
      final Boolean pEnvelope,
      final boolean pAllowAsymmetricClusters,
      boolean pIncludeDeletedClustersWithRetainedBackups) {
    final ClusterDescriptionView view;
    try {
      if (pAllowAsymmetricClusters) {
        final Pair<BaseTenantUpgradeStatus, ClusterDescription> clusterdata =
            ndsUISvc.getClusterDescriptionForApiCall(
                pGroup.getId(), pClusterName, false, pIncludeDeletedClustersWithRetainedBackups);
        view =
            ndsUISvc.getClusterDescription(
                pGroup.getId(),
                pClusterName,
                false,
                false,
                Optional.empty(),
                clusterdata.getLeft(),
                ClusterDescriptionUtil.getWithSplitReplicationSpecs(clusterdata.getRight()));
      } else {
        view = ndsUISvc.getClusterDescription(pGroup.getId(), pClusterName);
      }
    } catch (final SvcException pE) {
      throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, pClusterName, pGroup.getId());
    }

    if (view.isServerlessTenantCluster()) {
      if (appSettings.isServerlessEnabled()) {
        throw ApiErrorCode.CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API.exception(
            pEnvelope, pClusterName);
      } else {
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, view.getName(), pGroup.getId());
      }
    }

    if (view.isFlexTenantCluster()) {
      if (appSettings.isFlexEnabled()) {
        if (!isFlexClusterValid(view, pGroup)) {
          throw ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.exception(
              pEnvelope, pClusterName);
        }
      } else {
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, view.getName(), pGroup.getId());
      }
    }

    if (!pAllowAsymmetricClusters && view.hasAsymmetricHardwareSpecs()) {
      throw ApiErrorCode.ASYMMETRIC_SHARD_UNSUPPORTED.exception(pEnvelope);
    }

    return view;
  }

  /**
   * This is a layer of indirection to call {@link #getClusterDescriptionViewV2AndAbove} that always
   * merges replication specs of clusters. It is intended to be called by APIs that need to be able
   * to return symmetric clusters that have the new split schema for replication specs.
   *
   * <p>Having this layer of indirection facilitates mocking the {@link
   * ApiAtlasClusterDescriptionUtil} class in unit tests without losing type information.
   *
   * @return A ClusterDescriptionView with merged replication specs.
   */
  public ClusterDescriptionView getMergedClusterDescriptionView(
      final Group pGroup,
      final String pClusterName,
      final Boolean pEnvelope,
      final boolean pAllowAsymmetricClusters) {
    return ClusterDescriptionViewUtils.mergeReplicationSpecs(
        getClusterDescriptionViewV2AndAbove(
            pGroup, pClusterName, pEnvelope, pAllowAsymmetricClusters, false));
  }

  public void handleClusterCreateExceptions(
      final Exception pE, final String pName, final Group pGroup, final Boolean pEnvelope) {
    if (!(pE instanceof final SvcException svcException)) {
      return;
    }

    if (BillingErrorCode.PAYMENT_METHOD_MISSING.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.NO_PAYMENT_INFORMATION_FOUND.exception(pEnvelope, pGroup.getId());
    }

    if (NDSErrorCode.DUPLICATE_CLUSTER_NAME.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.DUPLICATE_CLUSTER_NAME.exception(
          pEnvelope,
          appSettings.isServerlessEnabled() ? "cluster or serverless instance" : "cluster",
          pName,
          pGroup.getId());
    }

    if (NDSErrorCode.CLUSTER_NAME_TOO_LONG.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_NAME_TOO_LONG.exception(
          pEnvelope, pName, getMaxClusterLengthForError(ndsGroupSvc.find(pGroup.getId()).get()));
    }

    if (NDSErrorCode.ATLAS_RESERVED_CLUSTER_NAME.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.ATLAS_RESERVED_CLUSTER_NAME.exception(pEnvelope, pName);
    }

    if (NDSErrorCode.DUPLICATE_CLUSTER_NAME_PREFIX.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.DUPLICATE_CLUSTER_NAME_PREFIX.exception(
          pEnvelope,
          pName,
          ndsGroupSvc.find(pGroup.getId()).get().getLimits().getClusterNameUniquePrefixLength(),
          appSettings.isServerlessEnabled() ? "cluster or serverless instance" : "cluster");
    }

    if (NDSErrorCode.CLUSTER_NAME_PREFIX_INVALID.equals(svcException.getErrorCode())) {
      final int clusterNameUniquePrefixLength =
          ndsGroupSvc.find(pGroup.getId()).get().getLimits().getClusterNameUniquePrefixLength();
      final String clusterNamePrefix =
          pName.substring(0, Math.min(clusterNameUniquePrefixLength, pName.length()));
      throw ApiErrorCode.CLUSTER_NAME_PREFIX_INVALID.exception(
          pEnvelope, pName, clusterNameUniquePrefixLength, clusterNamePrefix);
    }

    if (NDSErrorCode.CONTAINER_IN_USE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CONTAINERS_IN_USE.exception(pEnvelope, svcException.getMessage());
    }
    if (NDSErrorCode.CONTAINER_WAITING_FOR_FAST_RECORD_CLEAN_UP.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CONTAINER_WAITING_FOR_FAST_RECORD_CLEAN_UP.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.UNSUPPORTED_VERSION_FOR_LDAP_AUTHENTICATION.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.UNSUPPORTED_VERSION_FOR_LDAP_AUTHENTICATION.exception(pEnvelope);
    }

    if (NDSErrorCode.CLUSTER_VERSION_DEPRECATED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.ATLAS_CLUSTER_VERSION_DEPRECATED.exception(pEnvelope);
    }

    if (NDSErrorCode.CROSS_REGION_NETWORK_PERMISSIONS_LIMIT_EXCEEDED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CROSS_REGION_NETWORK_PERMISSIONS_LIMIT_EXCEEDED.exception(
          pEnvelope,
          ndsGroupSvc
              .find(pGroup.getId())
              .get()
              .getLimits()
              .getMaxCrossRegionNetworkPermissionEntries());
    }

    if (NDSErrorCode.NO_CAPACITY.equals(svcException.getErrorCode())) {
      LOG.debug("[CLOUDP-275770] Handling NO_CAPACITY error code", svcException);
      throw ApiErrorCode.OUT_OF_CAPACITY.exception(pEnvelope);
    }

    if (NDSErrorCode.DUPLICATE_ZONE_NAME.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.DUPLICATE_ZONE_NAME.exception(pEnvelope);
    }

    if (NDSErrorCode.UNSUPPORTED_FOR_PRIVATE_IP_MODE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.UNSUPPORTED_FOR_PRIVATE_IP_MODE.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_MONGODB_VERSION.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_MONGODB_VERSION.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_AWS_CLUSTERS.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_AWS_CLUSTERS.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_CLUSTERS.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_AWS_CLUSTERS.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.REGION_UNAVAILABLE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.REGION_UNAVAILABLE.exception(
          pEnvelope,
          svcException.getMessageParams().get(0),
          svcException.getMessageParams().get(1));
    }

    if (NDSErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION
          .exception(pEnvelope, svcException.getMessageParams());
    }

    if (NDSErrorCode.DISK_SIZE_INVALID_FOR_AZURE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.DISK_SIZE_INVALID_FOR_AZURE.exception(pEnvelope);
    }

    if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE.exception(pEnvelope);
    }

    if (NDSErrorCode.CANNOT_SET_SELF_MANAGED_SHARDING_FOR_NON_GLOBAL_CLUSTER.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_SET_SELF_MANAGED_SHARDING_FOR_NON_GLOBAL_CLUSTER.exception(
          pEnvelope, pName);
    }

    if (NDSErrorCode.DUPLICATE_REGION_CONFIGS.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.DUPLICATE_REGION_CONFIGS.exception(pEnvelope);
    }
  }

  public void handleClusterUpdateExceptions(
      final Exception pE, final String pName, final Boolean pEnvelope) {
    if (!(pE instanceof final SvcException svcException)) {
      return;
    }

    if (svcException.getErrorCode() == NDSErrorCode.CLUSTER_RESUMED_RECENTLY) {
      throw ApiErrorCode.CANNOT_PAUSE_RECENTLY_RESUMED_CLUSTER.exception(
          pEnvelope, InstanceHardware.MINIMUM_RESUME_RUNTIME.toMinutes());
    }
    if (svcException.getErrorCode() == NDSErrorCode.INSUFFICIENT_DISK_SPACE_ON_REMAINING_SHARDS) {
      throw ApiErrorCode.INSUFFICIENT_DISK_SPACE_ON_REMAINING_SHARDS.exception(pEnvelope);
    }
    if (svcException.getErrorCode()
        == NDSErrorCode.CANNOT_DECREASE_DISK_SIZE_DURING_SHARD_REMOVAL) {
      throw ApiErrorCode.CANNOT_DECREASE_DISK_SIZE_DURING_SHARD_REMOVAL.exception(pEnvelope);
    }
    if (svcException.getErrorCode()
        == NDSErrorCode.SHARDED_CLUSTERS_INCOMPATIBLE_WITH_DEVICE_SYNC_OR_PREIMAGE_TRIGGERS) {
      throw ApiErrorCode.SHARDED_CLUSTERS_INCOMPATIBLE_WITH_DEVICE_SYNC_OR_PREIMAGE_TRIGGERS
          .exception(pEnvelope);
    }
    if (svcException.getErrorCode() == NDSErrorCode.SHARED_CLUSTERS_INCOMPATIBLE_WITH_REALM_SYNC) {
      throw ApiErrorCode.SHARED_CLUSTERS_INCOMPATIBLE_WITH_REALM_SYNC.exception(pEnvelope);
    }
    if (NDSErrorCode.USER_UNAUTHORIZED_ATLAS_TERMINATION_PROTECTION.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.USER_UNAUTHORIZED_ATLAS_TERMINATION_PROTECTION.exception(pEnvelope);
    }
    if (svcException.getErrorCode() == NDSErrorCode.CRITERIA_NOT_MET_FOR_FORCE_RECONFIG) {
      throw ApiErrorCode.CRITERIA_NOT_MET_FOR_FORCE_RECONFIG.exception(pEnvelope);
    }
    if (svcException.getErrorCode() == NDSErrorCode.CANNOT_UPDATE_PAUSED_CLUSTER) {
      throw ApiErrorCode.CANNOT_UPDATE_PAUSED_CLUSTER.exception(pEnvelope);
    }
    if (svcException.getErrorCode() == NDSErrorCode.INVALID_UPGRADE_FOR_EIGHT_ZERO_ZERO) {
      throw ApiErrorCode.INVALID_UPGRADE_FOR_EIGHT_ZERO_ZERO.exception(pEnvelope);
    }
    if (svcException.getErrorCode() == NDSErrorCode.INVALID_SHARD_COUNT_FOR_SHARDING_UPGRADE) {
      throw ApiErrorCode.INVALID_SHARD_COUNT_FOR_SHARDING_UPGRADE.exception(pEnvelope, pName);
    }
    if (NDSErrorCode.DISK_SIZE_INVALID_FOR_AZURE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.DISK_SIZE_INVALID_FOR_AZURE.exception(pEnvelope);
    }

    if (NDSErrorCode.REGION_UNAVAILABLE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.REGION_UNAVAILABLE.exception(
          pEnvelope,
          svcException.getMessageParams().get(0),
          svcException.getMessageParams().get(1));
    }

    if (NDSErrorCode.ZONE_CHANGES_UNSUPPORTED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.ZONE_CHANGES_UNSUPPORTED.exception(pEnvelope);
    }

    if (NDSErrorCode.AMBIGUOUS_ZONE_NAME_UPDATE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.AMBIGUOUS_ZONE_NAME_UPDATE.exception(pEnvelope);
    }

    if (NDSErrorCode.ENCRYPTION_AT_REST_PROVIDER_NOT_SUPPORTED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.ENCRYPTION_AT_REST_PROVIDER_NOT_SUPPORTED.exception(pEnvelope);
    }

    if (NDSErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION
          .exception(pEnvelope, svcException.getMessageParams());
    }

    if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE.exception(pEnvelope);
    }

    if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_SEARCH_INDEXES.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_SEARCH_INDEXES.exception(
          pEnvelope);
    }

    if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_TIME_SERIES.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_TIME_SERIES.exception(
          pEnvelope);
    }

    if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_QUERYABLE_ENCRYPTION.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_QUERYABLE_ENCRYPTION
          .exception(pEnvelope);
    }

    if (NDSErrorCode.CUSTOM_ZONE_MAPPINGS_INVALID.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CUSTOM_ZONE_MAPPINGS_INVALID.exception(pEnvelope);
    }

    if (NDSErrorCode.DUPLICATE_REGION_CONFIGS.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.DUPLICATE_REGION_CONFIGS.exception(pEnvelope);
    }
  }

  public void handleGeneralExceptions(
      final Exception pE, final String pName, final Group pGroup, final Boolean pEnvelope) {
    if (!(pE instanceof final SvcException svcException)) {
      return;
    }

    if (NDSErrorCode.ENCRYPTION_AT_REST_NOT_ENABLED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.ENCRYPTION_AT_REST_NOT_ENABLED.exception(pEnvelope, pGroup.getId());
    }

    if (NDSErrorCode.ENCRYPTION_AT_REST_SETTINGS_INVALID.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.ENCRYPTION_AT_REST_SETTINGS_INVALID.exception(pEnvelope);
    }

    if (NDSErrorCode.ENCRYPTION_AT_REST_PROVIDER_NOT_SUPPORTED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.ENCRYPTION_AT_REST_PROVIDER_NOT_SUPPORTED.exception(pEnvelope);
    }

    if (NDSErrorCode.ENCRYPTION_AT_REST_AND_MAJOR_VERSION_UPDATE_INVALID.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.ENCRYPTION_AT_REST_AND_MAJOR_VERSION_UPDATE_INVALID.exception(pEnvelope);
    }

    if (NDSErrorCode.ENCRYPTION_AT_REST_AND_ADD_NODES_EXISTING_REGIONS_INVALID.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.ENCRYPTION_AT_REST_AND_ADD_NODES_EXISTING_REGIONS_INVALID.exception(
          pEnvelope);
    }

    if (NDSErrorCode.CLUSTER_RESTART_IN_PROGRESS.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_RESTART_IN_PROGRESS.exception(pEnvelope);
    }

    if (NDSErrorCode.CLUSTER_NOT_FOUND.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, pName, pGroup.getId());
    }

    if (NDSErrorCode.CLUSTER_RESTORE_IN_PROGRESS_CANNOT_UPDATE.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_RESTORE_IN_PROGRESS_CANNOT_UPDATE.exception(pEnvelope);
    }

    if (NDSErrorCode.SHARDED_CLUSTER_CANT_BECOME_REPLICA_SET.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.SHARDED_CLUSTER_CANT_BECOME_REPLICA_SET.exception(pEnvelope);
    }

    if (NDSErrorCode.UNSUPPORTED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_CLUSTER_CONFIGURATION.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.CANNOT_CHANGE_GEOSHARDED_CLUSTER_TYPE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_CHANGE_GEOSHARDED_CLUSTER_TYPE.exception(pEnvelope);
    }

    if (BillingErrorCode.HOURLY_BILLING_LIMIT_EXCEEDED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.HOURLY_BILLING_LIMIT_EXCEEDED.exception(pEnvelope);
    }

    if (NDSErrorCode.SHARDING_NOT_SUPPORTED.equals(svcException.getErrorCode())) {
      // The first element of message params should always be the instance size name. If no
      // parameters are present, it means that there is a new type of sharded cluster that is not
      // supported by the sharding support validation path.
      throw ApiErrorCode.SHARDING_NOT_SUPPORTED.exception(
          pEnvelope,
          Optional.ofNullable(svcException.getMessageParams())
              .orElseGet(Collections::emptyList)
              .stream()
              .findFirst()
              .orElse("unknown"));
    }

    if (NDSErrorCode.INVALID_GROUP_ID.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_GROUP_ID.exception(pEnvelope, pGroup.getId());
    }

    if (NDSErrorCode.CLUSTER_UPGRADE_INVALID.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_UPGRADE_INVALID.exception(pEnvelope);
    }

    if (NDSErrorCode.CANNOT_SKIP_MAJOR_VERSION_DOWNGRADE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_SKIP_MAJOR_VERSION_DOWNGRADE.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.CANNOT_DOWNGRADE_WITHOUT_PINNED_FCV.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_DOWNGRADE_WITHOUT_PINNED_FCV.exception(pEnvelope);
    }

    if (NDSErrorCode.CLUSTER_TYPE_NOT_SUPPORTED_FOR_FCV_PINNING.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_TYPE_NOT_SUPPORTED_FOR_FCV_PINNING.exception(pEnvelope);
    }

    if (NDSErrorCode.CLUSTER_TYPE_NOT_SUPPORTED_FOR_FCV_UNPINNING.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_TYPE_NOT_SUPPORTED_FOR_FCV_UNPINNING.exception(pEnvelope);
    }

    if (NDSErrorCode.MUST_EXTEND_PINNED_FCV_EXPIRATION_DATE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.MUST_EXTEND_PINNED_FCV_EXPIRATION_DATE.exception(pEnvelope);
    }
    if (NDSErrorCode.PINNED_FCV_EXPIRATION_DATE_TOO_FAR_IN_FUTURE.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.PINNED_FCV_EXPIRATION_DATE_TOO_FAR_IN_FUTURE.exception(pEnvelope);
    }

    if (NDSErrorCode.PINNING_FCV_FOR_RAPID_RELEASE_NOT_SUPPORTED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.PINNING_FCV_FOR_RAPID_RELEASE_NOT_SUPPORTED.exception(pEnvelope);
    }

    if (NDSErrorCode.MISMATCHED_FEATURE_COMPATIBILITY_VERSION_DOWNGRADE.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.MISMATCHED_FEATURE_COMPATIBILITY_VERSION_DOWNGRADE.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.INTERNAL.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_DECREASE_DISK_SIZE.exception(pEnvelope);
    }

    if (NDSErrorCode.DISK_SIZE_GB_TOO_SMALL.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.DISK_SIZE_GB_TOO_SMALL.exception(pEnvelope);
    }

    if (NDSErrorCode.CONTAINER_IN_USE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CONTAINERS_IN_USE.exception(pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.CONTAINER_BEING_DELETED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CONTAINER_BEING_DELETED.exception(pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.AUTO_SCALING_NOT_SUPPORTED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.AUTO_SCALING_NOT_SUPPORTED.exception(pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.ANALYTICS_AUTO_SCALING_NOT_SUPPORTED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.ANALYTICS_AUTO_SCALING_NOT_SUPPORTED.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_SCALE_DOWN_ENABLED_INVALID.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_SCALE_DOWN_ENABLED_INVALID.exception(pEnvelope);
    }

    if (NDSErrorCode.ANALYTICS_AUTO_SCALING_AMBIGUOUS.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.ANALYTICS_AUTO_SCALING_AMBIGUOUS.exception(pEnvelope);
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_REQUIRED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_REQUIRED.exception(pEnvelope);
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_REQUIRED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_REQUIRED.exception(pEnvelope);
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_TOO_SMALL.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_TOO_SMALL.exception(
          pEnvelope, ApiErrorCode.stripTrailingPeriod(svcException.getMessage()));
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_INVALID_FOR_DISABLED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_INVALID_FOR_DISABLED.exception(
          pEnvelope);
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_INVALID_FOR_DISABLED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_INVALID_FOR_DISABLED.exception(
          pEnvelope);
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_STORAGE_CONFIGURATION_INVALID.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_STORAGE_CONFIGURATION_INVALID
          .exception(pEnvelope, ApiErrorCode.stripTrailingPeriod(svcException.getMessage()));
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_INVALID_FOR_SHARDING.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_INVALID_FOR_SHARDING.exception(
          pEnvelope, ApiErrorCode.stripTrailingPeriod(svcException.getMessage()));
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_PREDICTIVE_ENABLED_INVALID.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_PREDICTIVE_ENABLED_INVALID.exception(
          pEnvelope, ApiErrorCode.stripTrailingPeriod(svcException.getMessage()));
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_PREDICTIVE_NOT_AVAILABLE.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_PREDICTIVE_NOT_AVAILABLE.exception(
          pEnvelope, ApiErrorCode.stripTrailingPeriod(svcException.getMessage()));
    }

    if (NDSErrorCode.COMPUTE_AUTO_SCALING_PREDICTIVE_UNSUPPORTED_CLUSTER.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.COMPUTE_AUTO_SCALING_PREDICTIVE_UNSUPPORTED_CLUSTER.exception(
          pEnvelope, ApiErrorCode.stripTrailingPeriod(pE.getMessage()));
    }

    if (NDSErrorCode.ANALYTICS_PREDICTED_AUTO_SCALING_UNSUPPORTED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.ANALYTICS_NODES_NO_COMPUTE_AUTO_SCALING_PREDICTIVE.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.CANNOT_PAUSE_CLUSTER_WITH_PENDING_CHANGES.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_PAUSE_CLUSTER_WITH_PENDING_CHANGES.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.CANNOT_MODIFY_CLUSTER_WITH_RUNNING_LIVE_IMPORT.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_MODIFY_CLUSTER_WITH_RUNNING_LIVE_IMPORT.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.NO_COMMON_INSTANCE_FAMILY.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.NO_COMMON_INSTANCE_FAMILY.exception(pEnvelope);
    }

    if (NDSErrorCode.CANNOT_CREATE_NVME_CLUSTER_CONTINUOUS_BACKUP_ENABLED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.NVME_STORAGE_CONTINUOUS_BACKUP_UNSUPPORTED.exception(pEnvelope);
    }

    if (NDSErrorCode.CLUSTER_REGIONS_INCOMPATIBLE_WITH_BACKUP_COPY_SETTINGS.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_REGIONS_INCOMPATIBLE_WITH_BACKUP_COPY_SETTINGS.exception(
          pEnvelope, svcException.getMessageParams());
    }

    if (NDSErrorCode.CROSS_REGION_NETWORK_PERMISSIONS_LIMIT_EXCEEDED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CROSS_REGION_NETWORK_PERMISSIONS_LIMIT_EXCEEDED.exception(
          pEnvelope,
          ndsGroupSvc
              .find(pGroup.getId())
              .get()
              .getLimits()
              .getMaxCrossRegionNetworkPermissionEntries());
    }

    if (NDSErrorCode.DUPLICATE_ZONE_NAME.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.DUPLICATE_ZONE_NAME.exception(pEnvelope);
    }

    if (NDSErrorCode.ASYMMETRIC_REGION_TOPOLOGY_IN_ZONE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.ASYMMETRIC_REGION_TOPOLOGY_IN_ZONE.exception(pEnvelope);
    }

    if (NDSErrorCode.CANNOT_CHANGE_ENCRYPTION_AT_REST_PROVIDER.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_CHANGE_ENCRYPTION_AT_REST_PROVIDER.exception(pEnvelope);
    }

    if (NDSErrorCode.INVALID_BI_CONNECTOR_READ_PREFERENCE_FOR_TOPOLOGY.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_BI_CONNECTOR_READ_PREFERENCE_FOR_TOPOLOGY.exception(pEnvelope);
    }

    if (NDSErrorCode.DISK_SIZE_EXCEEDED_INSTANCE_LIMIT == svcException.getErrorCode()
        || NDSErrorCode.DISK_SIZE_EXCEEDED_CLOUD_PROVIDER_INSTANCE_LIMIT
            == svcException.getErrorCode()) {
      throw ApiErrorCode.CLUSTER_DISK_SIZE_INVALID.exception(pEnvelope, svcException.getMessage());
    }
    if (NDSErrorCode.CANNOT_MODIFY_DISK_SIZE_FOR_NVME_CLUSTER == svcException.getErrorCode()) {
      throw ApiErrorCode.CANNOT_MODIFY_DISK_SIZE_FOR_NVME_CLUSTER.exception(pEnvelope);
    }

    if (NDSErrorCode.TENANT_CLUSTER_TEST_FAILOVER_NOT_SUPPORTED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.TENANT_CLUSTER_TEST_FAILOVER_NOT_SUPPORTED.exception(pEnvelope);
    }

    if (NDSErrorCode.SERVERLESS_INSTANCE_TEST_FAILOVER_NOT_SUPPORTED.equals(
        svcException.getErrorCode())) {
      if (appSettings.isServerlessEnabled()) {
        throw ApiErrorCode.SERVERLESS_INSTANCE_TEST_FAILOVER_NOT_SUPPORTED.exception(pEnvelope);
      } else {
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, pName, pGroup.getId());
      }
    }

    if (NDSErrorCode.OPERATION_INVALID_CLUSTER_WORKING.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.OPERATION_INVALID_CLUSTER_WORKING.exception(pEnvelope);
    }

    if (NDSErrorCode.OPERATION_INVALID_UNHEALTHY_NODES.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.OPERATION_INVALID_UNHEALTHY_NODES.exception(
          pEnvelope, svcException.getMessageParams().get(0));
    }

    if (NDSErrorCode.OPERATION_INVALID_SHARDS_NO_PRIMARY.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.OPERATION_INVALID_SHARDS_NO_PRIMARY.exception(
          pEnvelope, svcException.getMessageParams().get(0));
    }

    if (NDSErrorCode.OPERATION_INVALID_MEMBER_REPLICATION_LAG.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.OPERATION_INVALID_MEMBER_REPLICATION_LAG.exception(
          pEnvelope, svcException.getMessageParams().get(0));
    }

    if (NDSErrorCode.OPERATION_INVALID_MEMBER_OPLOG_CAPACITY.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.OPERATION_INVALID_MEMBER_OPLOG_CAPACITY.exception(
          pEnvelope, svcException.getMessageParams().get(0));
    }

    if (NDSErrorCode.OPERATION_INVALID_MEMBER_DISK_USAGE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.OPERATION_INVALID_MEMBER_DISK_USAGE.exception(
          pEnvelope,
          svcException.getMessageParams().get(0),
          svcException.getMessageParams().get(1),
          svcException.getMessageParams().get(2),
          svcException.getMessageParams().get(3));
    }

    if (NDSErrorCode.OPERATION_INVALID_INSUFFICIENT_METRICS.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.OPERATION_INVALID_INSUFFICIENT_METRICS.exception(pEnvelope);
    }

    if (NDSErrorCode.CLUSTER_INCOMPATIBLE_WITH_EXISTING_PRIVATE_ENDPOINT_SERVICE.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_INCOMPATIBLE_WITH_EXISTING_PRIVATELINK.exception(
          pEnvelope,
          svcException.getMessageParams().get(0),
          svcException.getMessageParams().get(1));
    }

    if (NDSErrorCode.AUTO_INDEXING_NOT_SUPPORTED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.AUTO_INDEXING_NOT_SUPPORTED.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.INVALID_MONGODB_VERSION_CONFIG.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.MONGODB_VERSION_INVALID.exception(pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.INVALID_MONGODB_MAJOR_VERSION_CONFIG.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.MONGODB_MAJOR_VERSION_INVALID.exception(
          pEnvelope, svcException.getMessageParams().get(0));
    }

    if (NDSErrorCode.INVALID_REGION_CONFIG_NODE_COUNT.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_REGION_CONFIG_NODE_COUNT.exception(pEnvelope);
    }

    if (NDSErrorCode.ALL_NODES_MUST_HAVE_EQUAL_NVME_INSTANCE_SIZES.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.ALL_NODES_MUST_HAVE_EQUAL_NVME_INSTANCE_SIZES.exception(pEnvelope);
    }

    if (NDSErrorCode.BASE_NODES_ASYMMETRIC_HARDWARE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.BASE_NODES_ASYMMETRIC_HARDWARE.exception(pEnvelope);
    }

    if (NDSErrorCode.ANALYTICS_NODES_ASYMMETRIC_HARDWARE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.ANALYTICS_NODES_ASYMMETRIC_HARDWARE.exception(pEnvelope);
    }

    if (NDSErrorCode.INVALID_INSTANCE_CLASS_CONFIGURATION.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_INSTANCE_CLASS_CONFIGURATION.exception(pEnvelope);
    }

    if (NDSErrorCode.OPLOG_MIN_RETENTION_HOURS_NOT_SUPPORTED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.OPLOG_MIN_RETENTION_HOURS_NOT_SUPPORTED.exception(pEnvelope);
    }

    if (NDSErrorCode.OPLOG_MIN_RETENTION_HOURS_NVME_TOO_SMALL.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.OPLOG_MIN_RETENTION_HOURS_NVME_TOO_SMALL.exception(
          pEnvelope, svcException.getMessage());
    }

    if (NDSErrorCode.OPLOG_MIN_RETENTION_HOURS_NO_DISK_AUTO_SCALING.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.OPLOG_MIN_RETENTION_HOURS_NO_DISK_AUTO_SCALING.exception(pEnvelope);
    }

    if (NDSErrorCode.ASYMMETRIC_INSTANCE_CLASS_DISK_AUTO_SCALING.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.ASYMMETRIC_INSTANCE_CLASS_DISK_AUTO_SCALING.exception(pEnvelope);
    }

    if (NDSErrorCode.CANNOT_DISABLE_CLOUD_BACKUP_WITH_BACKUP_COMPLIANCE_POLICY.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_DISABLE_CLOUD_BACKUP_WITH_BACKUP_COMPLIANCE_POLICY.exception(
          pEnvelope);
    }

    if (NDSErrorCode.CANNOT_DISABLE_PIT_WITH_BACKUP_COMPLIANCE_POLICY.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_DISABLE_PIT_WITH_BACKUP_COMPLIANCE_POLICY.exception(pEnvelope);
    }

    if (NDSErrorCode.CANNOT_DISABLE_ENCRYPTION_AT_REST_WITH_BACKUP_COMPLIANCE_POLICY.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.CANNOT_DISABLE_ENCRYPTION_AT_REST_WITH_BACKUP_COMPLIANCE_POLICY.exception(
          pEnvelope);
    }

    if (NDSErrorCode.INVALID_IOPS_CONFIGURATION.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_IOPS_CONFIGURATION.exception(pEnvelope);
    }

    if (NDSErrorCode.INVALID_IOPS_CONFIGURATION_FOR_GCP_FEATURE_DISABLED.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_IOPS_CONFIGURATION_FOR_GCP_FEATURE_DISABLED.exception(pEnvelope);
    }

    if (NDSErrorCode.INVALID_IOPS_CONFIGURATION_FOR_GCP_UNSUPPORTED_INSTANCE_FAMILY.equals(
        svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_IOPS_CONFIGURATION_FOR_GCP_UNSUPPORTED_INSTANCE_FAMILY.exception(
          pEnvelope);
    }

    if (NDSErrorCode.MAX_NUMBER_OF_REGIONS_FOR_CONTAINER.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.MAX_NUMBER_OF_REGIONS_FOR_CONTAINER.exception(
          pEnvelope,
          svcException.getMessageParams().get(0),
          svcException.getMessageParams().get(1));
    }

    if (NDSErrorCode.CLUSTER_NOT_PROVISIONED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.CLUSTER_NOT_PROVISIONED.exception(pEnvelope);
    }

    if (NDSErrorCode.TAG_KEY_BLANK.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.TAG_KEY_BLANK.exception(pEnvelope);
    }

    if (NDSErrorCode.TAG_VALUE_BLANK.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.TAG_VALUE_BLANK.exception(pEnvelope);
    }

    if (NDSErrorCode.TAG_KEY_EXCEEDED_MAX_ALLOWED_LENGTH.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.TAG_KEY_EXCEEDED_MAX_ALLOWED_LENGTH.exception(pEnvelope);
    }

    if (NDSErrorCode.TAG_VALUE_EXCEEDED_MAX_ALLOWED_LENGTH.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.TAG_VALUE_EXCEEDED_MAX_ALLOWED_LENGTH.exception(pEnvelope);
    }

    if (NDSErrorCode.TAG_KEY_CONTAINS_INVALID_CHARACTERS.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.TAG_KEY_CONTAINS_INVALID_CHARACTERS.exception(pEnvelope);
    }

    if (NDSErrorCode.TAG_VALUE_CONTAINS_INVALID_CHARACTERS.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.TAG_VALUE_CONTAINS_INVALID_CHARACTERS.exception(pEnvelope);
    }

    if (NDSErrorCode.TAG_KEY_INVALID_PREFIX.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.TAG_KEY_INVALID_PREFIX.exception(pEnvelope);
    }

    if (NDSErrorCode.MAX_GLOBAL_ZONES_EXCEEDED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.MAX_GLOBAL_ZONES_EXCEEDED.exception(
          pEnvelope, svcException.getMessageParams().toArray());
    }

    if (NDSErrorCode.INVALID_INSTANCE_SIZE_RANGE.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.INVALID_INSTANCE_SIZE_RANGE.exception(
          pEnvelope, svcException.getMessageParams().get(0));
    }

    if (NDSErrorCode.NO_FCV_PINNED.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.NO_FCV_PINNED.exception(pEnvelope);
    }

    if (NDSErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.equals(svcException.getErrorCode())) {
      throw ApiErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.exception(pEnvelope, pName);
    }

    NDSErrorCodeUtils.handleGeneralExceptions(svcException, pEnvelope);

    LOG.warn(svcException.getErrorCode().getMessage() + ": " + svcException.getMessage());
    throw ApiErrorCode.ATLAS_GENERAL_ERROR.exception(pEnvelope, svcException.getMessage());
  }

  public void handleTenantRestoreExceptions(
      final SvcException pE,
      final Group pGroup,
      final String pSourceClusterName,
      final ObjectId pTargetProjectId,
      final String pTargetDeploymentName,
      final ObjectId pSnapshotId,
      final Boolean pEnvelope) {
    if (pE.getErrorCode().equals(NDSErrorCode.CLUSTER_NOT_FOUND)) {
      throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(
          pEnvelope, pTargetDeploymentName, pTargetProjectId);
    } else if (pE.getErrorCode().equals(NDSErrorCode.INVALID_GROUP_ID)) {
      throw ApiErrorCode.GROUP_NOT_FOUND.exception(pEnvelope, pTargetProjectId);
    } else if (pE.getErrorCode().equals(NDSErrorCode.RESTORE_TARGET_PERMISSION_DENIED)) {
      throw ApiErrorCode.CANNOT_RESTORE_TO_TARGET.exception(pEnvelope);
    } else if (pE.getErrorCode().equals(NDSErrorCode.CLUSTER_RESTORE_IN_PROGRESS)) {
      throw ApiErrorCode.ATLAS_CLUSTER_RESTORE_IN_PROGRESS.exception(pEnvelope);
    } else if (pE.getErrorCode().equals(NDSErrorCode.TENANT_SNAPSHOT_NOT_FOUND)) {
      throw ApiErrorCode.TENANT_SNAPSHOT_NOT_FOUND.exception(
          pEnvelope, pSnapshotId, pSourceClusterName);
    } else if (pE.getErrorCode().equals(NDSErrorCode.INCOMPLETE_TENANT_SNAPSHOT)) {
      throw ApiErrorCode.CANNOT_START_RESTORE_JOB_FOR_INCOMPLETE_CLUSTER_SNAPSHOT.exception(
          pEnvelope);
    } else if (pE.getErrorCode().equals(NDSErrorCode.INVALID_ARGUMENT)) {
      throw ApiErrorCode.TENANT_RESTORE_VERSION_MISMATCH.exception(pEnvelope);
    } else if (pE.getErrorCode().equals(NDSErrorCode.BACKUP_RESTORE_INCOMPATIBLE_TOPOLOGY)) {
      throw ApiErrorCode.TENANT_RESTORE_INCOMPATIBLE_TOPOLOGY.exception(pEnvelope);
    } else if (pE.getErrorCode().equals(NDSErrorCode.TENANT_RESTORE_DESTINATION_ENCRYPTION)) {
      throw ApiErrorCode.TENANT_RESTORE_DESTINATION_ENCRYPTION.exception(pEnvelope);
    } else if (pE.getErrorCode().equals(NDSErrorCode.DISK_SIZE_GB_TOO_SMALL)) {
      throw ApiErrorCode.DISK_SIZE_GB_TOO_SMALL.exception(pEnvelope);
    } else if (pE.getErrorCode().equals(NDSErrorCode.TENANT_RESTORE_DESTINATION_UNAVAILABLE)) {
      throw ApiErrorCode.TENANT_RESTORE_DESTINATION_UNAVAILABLE.exception(pEnvelope);
    } else if (pE.getErrorCode()
        .equals(NDSErrorCode.CANNOT_TAKE_TENANT_SNAPSHOT_OF_SERVERLESS_INSTANCE)) {
      handleCannotTakeTenantSnapshotOfServerlessInstanceException(
          pGroup, pTargetDeploymentName, appSettings, pEnvelope);
    } else if (NDSErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.equals(pE.getErrorCode())) {
      throw ApiErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.exception(
          pEnvelope, pE.getMessageParams().get(0));
    }

    LOG.error(
        "Unhandled exception doRestore for group: {}, snapshotId: {}",
        pGroup.getId(),
        pSnapshotId,
        pE);

    throw ApiErrorCode.UNEXPECTED_ERROR.exception(pEnvelope);
  }

  public void handleSnapshotDownloadExceptions(
      final SvcException e,
      final String pClusterName,
      final Group pGroup,
      final ObjectId pSnapshotId,
      final Boolean pEnvelope) {
    if (e.getErrorCode().equals(NDSErrorCode.CLUSTER_NOT_FOUND)) {
      throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, pClusterName, pGroup.getId());
    } else if (e.getErrorCode().equals(NDSErrorCode.INVALID_GROUP_ID)) {
      throw ApiErrorCode.INVALID_GROUP_ID.exception(pEnvelope, pGroup.getId());
    } else if (e.getErrorCode().equals(NDSErrorCode.SNAPSHOT_ACCESS_DENIED)) {
      throw ApiErrorCode.TENANT_SNAPSHOT_ACCESS_DENIED.exception(pEnvelope);
    } else if (e.getErrorCode().equals(NDSErrorCode.TENANT_SNAPSHOT_NOT_FOUND)) {
      throw ApiErrorCode.TENANT_SNAPSHOT_NOT_FOUND.exception(pEnvelope, pSnapshotId, pClusterName);
    } else if (e.getErrorCode().equals(NDSErrorCode.INCOMPLETE_TENANT_SNAPSHOT)) {
      throw ApiErrorCode.CANNOT_DOWNLOAD_INCOMPLETE_SNAPSHOT.exception(pEnvelope);
    } else if (e.getErrorCode().equals(NDSErrorCode.NETWORK_PERMISSIONS_SIZE_EXCEEDED)) {
      throw ApiErrorCode.ATLAS_NETWORK_PERMISSIONS_SIZE_EXCEEDED.exception(pEnvelope);
    }
    LOG.error("Unexpected exception for download restore", e);
    throw ApiErrorCode.UNEXPECTED_ERROR.exception(pEnvelope);
  }

  public void handleCannotTakeTenantSnapshotOfServerlessInstanceException(
      final Group pGroup,
      final String pServerlessInstanceName,
      final AppSettings pSettings,
      final Boolean pEnvelope) {
    if (pSettings.isServerlessEnabled()) {
      throw ApiErrorCode.CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API.exception(
          pEnvelope, pServerlessInstanceName);
    } else {
      throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(
          pEnvelope, pServerlessInstanceName, pGroup.getId());
    }
  }

  public boolean isFlexShimLogicEnabled(final Group pGroup) {
    return appSettings.getFlexApiSharedMode().equals("DEPRECATED")
        && featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX, null, pGroup);
  }

  public boolean isFlexServerlessShimLogicEnabled(final Group pGroup) {
    return appSettings.getFlexApiServerlessMode().equals("DEPRECATED")
        && featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.LEGACY_SERVERLESS_API_SHIM_TO_FLEX, null, pGroup)
        && !featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.SERVERLESS_SHARED_UI_OPTION_ENABLED, null, pGroup);
  }

  public void handleCreateUpdateProcessArgsExceptions(
      final Exception pE,
      final String pClusterName,
      final Group pGroup,
      final Boolean pEnvelope,
      final AppSettings pAppSettings) {
    if (pE instanceof final UncheckedSvcException uncheckedSvcException) {
      if (NDSErrorCode.INVALID_ARGUMENT.equals(uncheckedSvcException.getErrorCode())) {
        throw ApiErrorCode.INVALID_PARAMETER.exception(
            pEnvelope, uncheckedSvcException.getMessageParams());
      }
    }

    if (pE instanceof final SvcException svcException) {
      if (NDSErrorCode.TENANT_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.TENANT_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED.exception(pEnvelope);
      }

      if (NDSErrorCode.SERVERLESS_INSTANCE_PROCESS_ARGS_NOT_SUPPORTED.equals(
          svcException.getErrorCode())) {
        if (pAppSettings.isServerlessEnabled()) {
          throw ApiErrorCode.SERVERLESS_INSTANCE_PROCESS_ARGS_NOT_SUPPORTED.exception(pEnvelope);
        } else {
          throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, pClusterName, pGroup.getId());
        }
      }

      if (NDSErrorCode.FLEX_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED.equals(
          svcException.getErrorCode())) {
        if (pAppSettings.isFlexEnabled()) {
          throw ApiErrorCode.FLEX_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED.exception(pEnvelope);
        } else {
          throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, pClusterName, pGroup.getId());
        }
      }

      if (NDSErrorCode.OPLOG_SIZE_MB_LESS_THAN_EQUAL_ZERO.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.OPLOG_SIZE_MB_LESS_THAN_EQUAL_ZERO.exception(pEnvelope);
      }

      if (NDSErrorCode.OPLOG_SIZE_MB_LESS_THAN_990.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.OPLOG_SIZE_MB_LESS_THAN_990.exception(pEnvelope);
      }

      if (NDSErrorCode.OPLOG_SIZE_MB_NVME_TOO_SMALL.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.OPLOG_SIZE_MB_TOO_SMALL.exception(pEnvelope, svcException.getMessage());
      }

      if (NDSErrorCode.OPLOG_MIN_RETENTION_HOURS_LESS_THAN_ZERO.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.OPLOG_MIN_RETENTION_HOURS_LESS_THAN_ZERO.exception(pEnvelope);
      }

      if (NDSErrorCode.OPLOG_MIN_RETENTION_HOURS_NVME_TOO_SMALL.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.OPLOG_MIN_RETENTION_HOURS_NVME_TOO_SMALL.exception(
            pEnvelope, svcException.getMessage());
      }

      if (NDSErrorCode.OPLOG_MIN_RETENTION_HOURS_NOT_SUPPORTED.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.OPLOG_MIN_RETENTION_HOURS_NOT_SUPPORTED.exception(pEnvelope);
      }

      if (NDSErrorCode.OPLOG_MIN_RETENTION_HOURS_NO_DISK_AUTO_SCALING.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.OPLOG_MIN_RETENTION_HOURS_NO_DISK_AUTO_SCALING.exception(pEnvelope);
      }

      if (NDSErrorCode.OPLOG_SIZE_MB_TOO_BIG.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.OPLOG_SIZE_MB_TOO_BIG.exception(pEnvelope, svcException.getMessage());
      }

      if (NDSErrorCode.INVALID_TLS_VERSION.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.INVALID_TLS_VERSION.exception(
            pEnvelope, svcException.getMessageParams());
      }

      if (NDSErrorCode.INVALID_SAMPLE_SIZE_BIC.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.INVALID_SAMPLE_SIZE_BI_CONNECTOR.exception(pEnvelope);
      }

      if (NDSErrorCode.INVALID_SAMPLE_REFRESH_INTERVAL_BIC.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.INVALID_SAMPLE_REFRESH_INTERVAL_BI_CONNECTOR.exception(pEnvelope);
      }

      if (NDSErrorCode.CLUSTER_NOT_FOUND.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, pClusterName, pGroup.getName());
      }

      if (NDSErrorCode.DEFAULT_WRITE_CONCERN_NOT_SUPPORTED.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.DEFAULT_WRITE_CONCERN_NOT_SUPPORTED.exception(pEnvelope);
      }

      if (NDSErrorCode.INVALID_DEFAULT_WRITE_CONCERN.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.INVALID_DEFAULT_WRITE_CONCERN.exception(
            pEnvelope, svcException.getMessageParams());
      }

      if (NDSErrorCode.DEFAULT_READ_CONCERN_NOT_SUPPORTED.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.DEFAULT_READ_CONCERN_NOT_SUPPORTED.exception(pEnvelope);
      }

      if (NDSErrorCode.INVALID_DEFAULT_READ_CONCERN.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.INVALID_DEFAULT_READ_CONCERN.exception(
            pEnvelope, svcException.getMessageParams());
      }
      if (NDSErrorCode.TRANSACTION_LIFETIME_LIMIT_SECONDS_LESS_THAN_ONE.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.TRANSACTION_LIFETIME_LIMIT_SECONDS_LESS_THAN_ONE.exception(pEnvelope);
      }
      if (NDSErrorCode.TRANSACTION_LIFETIME_LIMIT_SECONDS_NOT_SUPPORTED.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.TRANSACTION_LIFETIME_LIMIT_SECONDS_NOT_SUPPORTED.exception(pEnvelope);
      }
      if (NDSErrorCode.DEFAULT_READ_MAX_TIME_LESS_THAN_ZERO.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.DEFAULT_READ_MAX_TIME_LESS_THAN_ZERO.exception(pEnvelope);
      }
      if (NDSErrorCode.DEFAULT_READ_MAX_TIME_NOT_SUPPORTED.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.DEFAULT_READ_MAX_TIME_NOT_SUPPORTED.exception(pEnvelope);
      }
      if (NDSErrorCode.CHUNK_MIGRATION_CONCURRENCY_NOT_SUPPORTED.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.CHUNK_MIGRATION_CONCURRENCY_NOT_SUPPORTED.exception(
            pEnvelope, svcException.getMessageParams());
      }

      if (NDSErrorCode.QUERY_STATS_LOG_VERBOSITY_LEVEL_NOT_SUPPORTED.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.QUERY_STATS_LOG_VERBOSITY_LEVEL_NOT_SUPPORTED.exception(pEnvelope);
      }
      if (NDSErrorCode.QUERY_STATS_NOT_SUPPORTED_ON_MONGODB_VERSION.equals(
          svcException.getErrorCode())) {
        throw ApiErrorCode.QUERY_STATS_NOT_SUPPORTED_ON_MONGODB_VERSION.exception(
            pEnvelope, svcException.getMessageParams());
      }

      if (NDSErrorCode.PROCESS_ARG_NOT_SUPPORTED.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.PROCESS_ARG_NOT_SUPPORTED.exception(
            pEnvelope, svcException.getMessageParams().toArray());
      }

      if (NDSErrorCode.INVALID_CHANGE_STREAM_OPTIONS_PRE_AND_POST_IMAGES_EXPIRE_AFTER_SECONDS
          .equals(svcException.getErrorCode())) {
        throw ApiErrorCode.INVALID_CHANGE_STREAM_OPTIONS_PRE_AND_POST_IMAGES_EXPIRE_AFTER_SECONDS
            .exception(pEnvelope, svcException.getMessageParams().toArray());
      }
      if (NDSErrorCode.INVALID_TLS_CONFIGURATION.equals(svcException.getErrorCode())) {
        throw ApiErrorCode.INVALID_TLS_CONFIGURATION.exception(
            pEnvelope, svcException.getMessageParams().toArray());
      }
    }
  }
}
