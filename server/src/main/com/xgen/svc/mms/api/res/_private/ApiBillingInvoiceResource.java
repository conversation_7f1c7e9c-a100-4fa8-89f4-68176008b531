package com.xgen.svc.mms.api.res._private;

import static com.xgen.cloud.access.role._public.model.RoleSet.NAME.GLOBAL_BILLING_ADMIN;
import static com.xgen.cloud.common.constants._public.model.res.PrivateApiPathConstants.API_PRIVATE_BILLING;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingshared.common.util._public.svc.SchedulerFactory;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.view.InvoiceAggregatedUsageCollectionView;
import com.xgen.svc.mms.dao.billing.ReactiveInvoiceDao;
import com.xgen.svc.mms.svc.billing.AggregateInvoiceLineItemSvc;
import com.xgen.svc.mms.svc.billing.InvoiceSvc;
import com.xgen.svc.mms.svc.billing.PayingOrgSvc;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.Nullable;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Duration;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

/**
 * Resource class for line item aggregation operations. Provides endpoints for aggregating line
 * items by various criteria.
 */
@Path(API_PRIVATE_BILLING)
@Singleton
public class ApiBillingInvoiceResource extends ApiBaseResource {

  private static final Scheduler API_BILLING_INVOICE_RESOURCE_SCHEDULER =
      SchedulerFactory.newBoundedElastic("api-billing-invoice-resource");

  private static final Logger LOG = LoggerFactory.getLogger(ApiBillingInvoiceResource.class);

  /** Timeout for reactive streams in seconds */
  private static final int REACTIVE_TIMEOUT_SECONDS = 15;

  private final AggregateInvoiceLineItemSvc aggregateInvoiceLineItemSvc;
  private final ReactiveInvoiceDao reactiveInvoiceDao;
  private final InvoiceSvc invoiceSvc;
  private final PayingOrgSvc payingOrgSvc;

  @Inject
  public ApiBillingInvoiceResource(
      AppSettings appSettings,
      AggregateInvoiceLineItemSvc aggregateInvoiceLineItemSvc,
      ReactiveInvoiceDao reactiveInvoiceDao,
      InvoiceSvc invoiceSvc,
      PayingOrgSvc payingOrgSvc) {
    super(appSettings);
    this.aggregateInvoiceLineItemSvc = aggregateInvoiceLineItemSvc;
    this.reactiveInvoiceDao = reactiveInvoiceDao;
    this.invoiceSvc = invoiceSvc;
    this.payingOrgSvc = payingOrgSvc;
  }

  /**
   * Unified endpoint for aggregating total price cents for line items, broken down by invoice, bill
   * multiple invoice aggregation with date ranges and line item ID ranges.
   *
   * <p>https://cxf.apache.org/docs/jax-rs-project-reactor-support.html
   *
   * @param invoiceIds Set of invoice IDs to filter by
   * @param startDate Start date for the range filter (inclusive, nullable)
   * @param endDate End date for the range filter (exclusive, nullable)
   * @param minLineItemId Minimum line item ID (inclusive, nullable)
   * @param maxLineItemId Maximum line item ID (inclusive, nullable)
   * @param includeSkus Optional set of SKUs to include (if specified, only these SKUs will be
   *     included)
   * @param excludeSkus Optional set of SKUs to exclude (if specified, these SKUs will be excluded)
   * @return Response containing InvoiceAggregatedUsageCollectionView with invoices and their
   *     aggregated usage data.
   */
  @GET
  @Path("/lineItems/aggregateTotalPriceCentsByBillDateAndGroupId")
  @Produces(MediaType.APPLICATION_JSON)
  @RolesAllowed(GLOBAL_BILLING_ADMIN)
  @Operation(
      operationId = "aggregateTotalPriceCentsByBillDateAndGroupId",
      summary = "Aggregate line item total price cents by bill date and group ID",
      description =
          "Aggregates total price cents for line items, broken down by invoice, bill date, and"
              + " group ID. Supports filtering by invoice IDs, date ranges, line item ID ranges,"
              + " and SKU inclusion/exclusion.",
      tags = {"Billing"},
      parameters = {
        @Parameter(
            name = "invoiceIds",
            description = "Set of invoice IDs to filter by",
            in = ParameterIn.QUERY,
            required = false,
            array = @ArraySchema(schema = @Schema(type = "string", format = "objectid"))),
        @Parameter(
            name = "startDate",
            description = "Start date for the range filter (inclusive)",
            in = ParameterIn.QUERY,
            required = false,
            schema = @Schema(type = "string", format = "date")),
        @Parameter(
            name = "endDate",
            description = "End date for the range filter (exclusive)",
            in = ParameterIn.QUERY,
            required = false,
            schema = @Schema(type = "string", format = "date")),
        @Parameter(
            name = "minLineItemId",
            description = "Minimum line item ID (inclusive)",
            in = ParameterIn.QUERY,
            required = false,
            schema = @Schema(type = "string", format = "objectid")),
        @Parameter(
            name = "maxLineItemId",
            description = "Maximum line item ID (inclusive)",
            in = ParameterIn.QUERY,
            required = false,
            schema = @Schema(type = "string", format = "objectid")),
        @Parameter(
            name = "includeSkus",
            description =
                "Optional set of SKUs to include (if specified, only these SKUs will be included)",
            in = ParameterIn.QUERY,
            required = false,
            array = @ArraySchema(schema = @Schema(implementation = SKU.class))),
        @Parameter(
            name = "excludeSkus",
            description =
                "Optional set of SKUs to exclude (if specified, these SKUs will be excluded)",
            in = ParameterIn.QUERY,
            required = false,
            array = @ArraySchema(schema = @Schema(implementation = SKU.class)))
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully aggregated line items",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = InvoiceAggregatedUsageCollectionView.class))),
        @ApiResponse(responseCode = "400", description = "Bad request - invalid parameters"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - insufficient permissions"),
        @ApiResponse(responseCode = "500", description = "Internal server error"),
        @ApiResponse(responseCode = "408", description = "Request timeout")
      })
  public CompletableFuture<Response> aggregateTotalPriceCentsByBillDateAndGroupId(
      @Nullable @QueryParam("invoiceIds") final Set<ObjectId> invoiceIds,
      @Nullable @QueryParam("startDate") final LocalDate startDate,
      @Nullable @QueryParam("endDate") final LocalDate endDate,
      @Nullable @QueryParam("minLineItemId") final ObjectId minLineItemId,
      @Nullable @QueryParam("maxLineItemId") final ObjectId maxLineItemId,
      @Nullable @QueryParam("includeSkus") final Set<SKU> includeSkus,
      @Nullable @QueryParam("excludeSkus") final Set<SKU> excludeSkus) {

    LOG.info(
        "Aggregating total price cents for line items. InvoiceIds: {}, StartDate: {}, EndDate: {},"
            + " MinLineItemId: {}, MaxLineItemId: {}, IncludeSkus: {}, ExcludeSkus: {}",
        invoiceIds,
        startDate,
        endDate,
        minLineItemId,
        maxLineItemId,
        includeSkus,
        excludeSkus);

    return aggregateInvoiceLineItemSvc
        .aggregateTotalPriceCentsByBillDateAndGroupId(
            invoiceIds, startDate, endDate, minLineItemId, maxLineItemId, includeSkus, excludeSkus)
        .map(InvoiceAggregatedUsageCollectionView::new)
        .subscribeOn(API_BILLING_INVOICE_RESOURCE_SCHEDULER)
        .timeout(Duration.ofSeconds(REACTIVE_TIMEOUT_SECONDS)) // Graceful fail before async timeout
        .publishOn(API_BILLING_INVOICE_RESOURCE_SCHEDULER)
        .map(result -> new ApiResponseBuilder(false).ok().content(result).build())
        .doOnError(error -> LOG.error("Unexpected error in async processing", error))
        .onErrorReturn(
            new ApiResponseBuilder(false)
                .internalServerError("Failed to aggregate line items")
                .build())
        .toFuture();
  }

  @GET
  @Path("/invoice/{invoiceId}")
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed(GLOBAL_BILLING_ADMIN)
  @Operation(
      operationId = "getInvoiceById",
      summary = "Get invoice by ID",
      description = "Retrieves a single invoice by its unique identifier.",
      tags = {"Billing"},
      parameters = {
        @Parameter(
            name = "invoiceId",
            description = "Unique identifier of the invoice to retrieve",
            in = ParameterIn.PATH,
            required = true,
            schema = @Schema(type = "string", format = "objectid"))
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved invoice",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = Invoice.class))),
        @ApiResponse(responseCode = "400", description = "Bad request - invalid invoice ID format"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Invoice not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error"),
        @ApiResponse(responseCode = "408", description = "Request timeout")
      })
  public CompletableFuture<Response> getInvoice(@PathParam("invoiceId") ObjectId invoiceId) {
    return reactiveInvoiceDao
        .findById(invoiceId)
        .subscribeOn(API_BILLING_INVOICE_RESOURCE_SCHEDULER)
        .timeout(Duration.ofSeconds(REACTIVE_TIMEOUT_SECONDS)) // Graceful fail before async timeout
        .publishOn(API_BILLING_INVOICE_RESOURCE_SCHEDULER)
        .map(invoice -> new ApiResponseBuilder(false).ok().content(invoice).build())
        .doOnError(
            error -> {
              LOG.error("Unexpected error getting invoice by ID: {}", invoiceId, error);
            })
        .switchIfEmpty(
            Mono.just(new ApiResponseBuilder(false).notFound("Invoice not found").build()))
        .onErrorReturn(
            new ApiResponseBuilder(false).internalServerError("Failed to retrieve invoice").build())
        .toFuture();
  }

  @POST
  @Path("/invoice/by-ids")
  @Produces(MediaType.APPLICATION_JSON)
  @RolesAllowed(RoleSet.NAME.GLOBAL_BILLING_ADMIN)
  @Operation(
      operationId = "getInvoicesByIds",
      summary = "Get invoices by IDs",
      description =
          "Retrieves multiple invoices by their unique identifiers. This endpoint is called from"
              + " Payment Standalone service to get invoices by IDs.",
      tags = {"Billing"},
      requestBody =
          @RequestBody(
              required = true,
              description = "List of invoice IDs to retrieve",
              content =
                  @Content(
                      mediaType = MediaType.APPLICATION_JSON,
                      array =
                          @ArraySchema(schema = @Schema(type = "string", format = "objectid")))),
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved invoices",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    array = @ArraySchema(schema = @Schema(implementation = Invoice.class)))),
        @ApiResponse(
            responseCode = "400",
            description = "Bad request - invalid invoice IDs or request format"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - insufficient permissions"),
        @ApiResponse(responseCode = "500", description = "Internal server error"),
        @ApiResponse(responseCode = "408", description = "Request timeout")
      })
  public CompletableFuture<Response> getInvoices(@RequestBody Set<ObjectId> invoiceIds) {

    LOG.info("Getting {} invoices by IDs", invoiceIds.size());

    return reactiveInvoiceDao
        .findByIds(invoiceIds)
        .collectList()
        .subscribeOn(API_BILLING_INVOICE_RESOURCE_SCHEDULER)
        .timeout(Duration.ofSeconds(REACTIVE_TIMEOUT_SECONDS)) // Graceful fail before async timeout
        .publishOn(API_BILLING_INVOICE_RESOURCE_SCHEDULER)
        .map(
            invoices -> {
              LOG.info(
                  "Successfully retrieved {} invoices out of {} requested",
                  invoices.size(),
                  invoiceIds.size());
              return new ApiResponseBuilder(false).ok().content(invoices).build();
            })
        .doOnError(
            error -> {
              LOG.error("Unexpected error getting invoices by IDs: {}", invoiceIds, error);
            })
        .onErrorReturn(
            new ApiResponseBuilder(false)
                .internalServerError("Failed to retrieve invoices")
                .build())
        .toFuture();
  }

  @GET
  @Path("/invoice/linked-invoices")
  @Produces(MediaType.APPLICATION_JSON)
  @RolesAllowed(RoleSet.NAME.GLOBAL_BILLING_ADMIN)
  @Operation(
      operationId = "getAllLinkedInvoicesByInvoiceIds",
      summary = "Get all linked invoices by invoice IDs",
      description =
          "Finds all linked invoices for the given array of invoice IDs. For each invoice ID"
              + " provided, this method will find all related invoices (paying + linked) for the"
              + " same billing period, without any status restrictions.",
      tags = {"Billing"},
      parameters = {
        @Parameter(
            name = "invoiceIds",
            description = "List of invoice IDs to find linked invoices for",
            required = true,
            array = @ArraySchema(schema = @Schema(type = "string", format = "objectid")))
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved linked invoices",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    array = @ArraySchema(schema = @Schema(implementation = Invoice.class)))),
        @ApiResponse(
            responseCode = "400",
            description = "Bad request - invalid invoice IDs or request format"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - insufficient permissions"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  public Response getAllLinkedInvoicesByInvoiceIds(
      @QueryParam("invoiceIds") List<ObjectId> invoiceIds) {
    if (invoiceIds == null || invoiceIds.isEmpty()) {
      return new ApiResponseBuilder(false)
          .badRequest("Invoice IDs list cannot be null or empty")
          .build();
    }
    try {
      List<Invoice> invoices = invoiceSvc.findByIds(invoiceIds);
      Set<Invoice> result =
          invoices.stream()
              .map(payingOrgSvc::getAllLinkedInvoicesByPayingOrLinkedInvoice)
              .flatMap(Collection::stream)
              .collect(Collectors.toSet());

      return new ApiResponseBuilder(false).ok().content(result).build();
    } catch (Exception error) {
      LOG.error("Unexpected error getting linked invoices for IDs: {}", invoiceIds, error);
      return new ApiResponseBuilder(false)
          .internalServerError("Failed to retrieve linked invoices")
          .build();
    }
  }
}
