package com.xgen.svc.mms.api.view.monitoring.querystats;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.monitoring.querystats._public.model.QueryStatsSummary;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(Include.NON_NULL)
@Schema(
    name = "QueryStatsSummary",
    description = "A summary of execution statistics for a given query shape.")
public class ApiQueryStatsSummaryResponseView {
  public static final String QUERY_SHAPE_FIELD = "queryShape";
  public static final String QUERY_SHAPE_HASH_FIELD = "queryShapeHash";
  public static final String NAMESPACE_FIELD = "namespace";
  public static final String COMMAND_FIELD = "command";

  public static final String P50_EXEC_MICROS_FIELD = "p50ExecMicros";
  public static final String P90_EXEC_MICROS_FIELD = "p90ExecMicros";
  public static final String P99_EXEC_MICROS_FIELD = "p99ExecMicros";
  public static final String EXEC_COUNT_FIELD = "execCount";
  public static final String TOTAL_TIME_TO_RESPONSE_MICROS_FIELD = "totalTimeToResponseMicros";
  public static final String LAST_EXEC_MICROS_FIELD = "lastExecMicros";

  public static final String KEYS_EXAMINED_FIELD = "keysExamined";
  public static final String DOCS_RETURNED_FIELD = "docsReturned";
  public static final String DOCS_EXAMINED_FIELD = "docsExamined";
  public static final String TOTAL_WORKING_MILLIS_FIELD = "totalWorkingMillis";
  public static final String AVG_WORKING_MILLIS_FIELD = "avgWorkingMillis";
  public static final String BYTES_READ_FIELD = "bytesRead";

  public static final String KEYS_EXAMINED_RATIO_FIELD = "keysExaminedRatio";
  public static final String DOCS_EXAMINED_RATIO_FIELD = "docsExaminedRatio";
  public static final String SYSTEM_QUERY_FIELD = "systemQuery";

  @JsonProperty(QUERY_SHAPE_FIELD)
  @Schema(
      type = "string",
      description =
          "A query shape is a set of specifications that group similar queries together."
              + " Specifications can include filters, sorts, projections, aggregation pipeline"
              + " stages, a namespace, and others. Queries that have similar specifications have"
              + " the same query shape.")
  public String _queryShape;

  @JsonProperty(QUERY_SHAPE_HASH_FIELD)
  @Schema(
      type = "string",
      description = "A hexadecimal string that represents the hash of a MongoDB query shape.")
  public String _queryShapeHash;

  @JsonProperty(EXEC_COUNT_FIELD)
  @Schema(
      description =
          "Total number of times that queries with the given query shape have been executed.")
  public Double _execCount;

  @JsonProperty(NAMESPACE_FIELD)
  @Schema(
      type = "string",
      description =
          "Human-readable label that identifies the namespace on the specified host. The resource"
              + " expresses this parameter value as `<database>.<collection>`.")
  public String _namespace;

  @JsonProperty(COMMAND_FIELD)
  @Schema(
      type = "string",
      description = "The MongoDB command issued for this query shape.",
      allowableValues = {"find", "distinct", "aggregate"},
      extensions =
          @Extension(
              name = IPA_EXCEPTION,
              properties = {
                @ExtensionProperty(
                    name = "xgen-IPA-123-enum-values-must-be-upper-snake-case",
                    value = "The values returned from the database are in lower case."),
              }))
  public String _command;

  @JsonProperty(P50_EXEC_MICROS_FIELD)
  @Schema(
      description = "The 50th percentile value of execution time in microseconds.",
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Field name p50ExecMicros is camel case with numbers."),
            })
      })
  public Double _p50ExecMicros;

  @JsonProperty(P90_EXEC_MICROS_FIELD)
  @Schema(
      description = "The 90th percentile value of execution time in microseconds.",
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Field name p50ExecMicros is camel case with numbers."),
            })
      })
  public Double _p90ExecMicros;

  @JsonProperty(P99_EXEC_MICROS_FIELD)
  @Schema(
      description = "The 99th percentile value of execution time in microseconds.",
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Field name p50ExecMicros is camel case with numbers."),
            })
      })
  public Double _p99ExecMicros;

  @JsonProperty(TOTAL_TIME_TO_RESPONSE_MICROS_FIELD)
  @Schema(
      description =
          "Time in microseconds spent from the beginning of query processing to the first server"
              + " response.")
  public Double _totalTimeToResponseMicros;

  @JsonProperty(LAST_EXEC_MICROS_FIELD)
  @Schema(
      description =
          "Execution runtime in microseconds for the most recent query with the given query"
              + " shape.")
  public Double _lastExecMicros;

  @JsonProperty(KEYS_EXAMINED_FIELD)
  @Schema(
      description =
          "Total number of in-bounds and out-of-bounds index keys examined by queries with the"
              + " given query shape.")
  public Double _keysExamined;

  @JsonProperty(DOCS_RETURNED_FIELD)
  @Schema(
      description =
          "Total number of documents returned by queries with the" + " given query shape.")
  public Double _docsReturned;

  @JsonProperty(DOCS_EXAMINED_FIELD)
  @Schema(description = "Total number of documents examined by queries with the given query shape.")
  public Double _docsExamined;

  @JsonProperty(TOTAL_WORKING_MILLIS_FIELD)
  @Schema(
      description =
          "Total time in milliseconds spent running queries with the given query shape. If the"
              + " query resulted in `getMore` commands, this metric includes the time spent"
              + " processing the `getMore` requests. This metric does not include time spent"
              + " waiting for the client.")
  public Double _totalWorkingMillis;

  @JsonProperty(AVG_WORKING_MILLIS_FIELD)
  @Schema(
      description =
          "Average total time in milliseconds spent running queries with the given query shape. If"
              + " the query resulted in getMore commands, this metric includes the time spent"
              + " processing the getMore requests. This metric does not include time spent waiting"
              + " for the client.")
  public Double _avgWorkingMillis;

  @JsonProperty(BYTES_READ_FIELD)
  @Schema(
      description = "The number of bytes read by the given query shape from the disk to the cache.")
  public Double _bytesRead;

  @JsonProperty(SYSTEM_QUERY_FIELD)
  @Schema(description = "Indicates whether this query shape represents a system-initiated query.")
  public Boolean _systemQuery;

  @JsonProperty(KEYS_EXAMINED_RATIO_FIELD)
  @Schema(
      description =
          "Ratio of in-bounds and out-of-bounds index keys examined to indexes containing"
              + " documents returned by queries with the given query shape.")
  public Double _keysExaminedRatio;

  @JsonProperty(DOCS_EXAMINED_RATIO_FIELD)
  @Schema(
      description =
          "Ratio of documents examined to documents returned by queries with the given query"
              + " shape.")
  public Double _docsExaminedRatio;

  public ApiQueryStatsSummaryResponseView(final QueryStatsSummary summary) {
    _queryShape = summary.getQueryShape();
    _queryShapeHash = summary.getQueryShapeHash();
    _execCount = summary.getExecCount();
    _namespace = summary.getNamespace();
    _command = summary.getCommand();

    _p50ExecMicros = summary.getP50ExecMicros();
    _p90ExecMicros = summary.getP90ExecMicros();
    _p99ExecMicros = summary.getP99ExecMicros();
    _totalTimeToResponseMicros = summary.getTotalTimeToResponseMicros();
    _lastExecMicros = summary.getLastExecMicros();

    _keysExamined = summary.getKeysExamined();
    _docsReturned = summary.getDocsReturned();
    _docsExamined = summary.getDocsExamined();
    _totalWorkingMillis = summary.getTotalWorkingMillis();
    _avgWorkingMillis = summary.getAvgWorkingMillis();
    _bytesRead = summary.getBytesRead();

    _keysExaminedRatio = summary.getKeysExaminedRatio();
    _docsExaminedRatio = summary.getDocsExaminedRatio();
    _systemQuery = summary.getSystemQuery();
  }
}
