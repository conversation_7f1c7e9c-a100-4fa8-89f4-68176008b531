package com.xgen.svc.mms.api.res.atlas;

import static com.xgen.cloud.apiusagedata._public.view.ApiTelemetryView.FieldDefs.CLUSTER_NAME;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetryAttributeSvc;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetrySvc.CustomTelemetryFieldKey;
import com.xgen.cloud.authz.resource._public.view.api.ApiAtlasTagView;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.requestparams._public.ApiBooleanParam;
import com.xgen.cloud.common.versioning._public.annotation.Sunset;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.common.view._public.base.ApiListView;
import com.xgen.cloud.common.view._public.base.ApiView;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterUpdateContext;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex._public.svc.FlexMigrationSvc;
import com.xgen.cloud.nds.flex.runtime.svc.LegacySharedApiShimToFlexSvc;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.DeleteClusterReason;
import com.xgen.cloud.nds.project._public.model.FlexTenantMigrationState;
import com.xgen.cloud.nds.project._public.model.GeoSharding;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.resourcepolicy._public.model.AtlasResourcePolicyAuthResponse;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.openapi._public.constant.OpenApiConst.ResponseDescriptions;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.iam.annotation.RequireCustomerGrantForEmployeeAccess;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.util.ApiAtlasClusterDescriptionUtil;
import com.xgen.svc.mms.api.util.ApiAtlasServerlessInstanceDescriptionUtil;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasBaseClusterDescriptionView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionConnectionStringsUtil;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionProcessArgsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasCustomZoneMappingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasFreeProviderSettingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasGeoShardingView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasLegacyClusterDescriptionView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasLegacyTenantUpgradeClusterDescriptionView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasManagedNamespacesView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasServerlessInstanceDescriptionView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasServerlessProviderSettingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasZoneMappingView;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.mms.res.filter.AllowTemporaryApiKeys;
import com.xgen.svc.mms.res.filter.PaidInFull;
import com.xgen.svc.mms.res.filter.SubscriptionPlan;
import com.xgen.svc.nds.aws.svc.NDSAWSLogDownloadSvc;
import com.xgen.svc.nds.aws.util.NDSAWSLogDownloadSvcUtils;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionViewUtils;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.svc.nds.model.ui.RegionConfigView;
import com.xgen.svc.nds.model.ui.ReplicationSpecView;
import com.xgen.svc.nds.svc.LogFetcherSvc;
import com.xgen.svc.nds.svc.LogFetcherSvc.CallType;
import com.xgen.svc.nds.svc.NDSClusterConversionSvc;
import com.xgen.svc.nds.svc.NDSResourcePolicySvc;
import com.xgen.svc.nds.svc.TenantClusterConfigurationSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.svc.nds.tenantUpgrade.svc.TenantUpgradeSvc;
import com.xgen.svc.nds.tenantUpgrade.svc.TenantUpgradeToServerlessSvc;
import com.xgen.svc.nds.util.GeoShardingLocationUtil;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.Nullable;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import net.logstash.logback.argument.StructuredArguments;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SubscriptionPlan(PlanTypeSet.NDS)
@PaidInFull
@Path("/api/atlas/v1.0/groups/{groupId}/clusters")
@AllowNDSCNRegionsOnlyGroups
@Singleton
public class ApiAtlasLegacyClusterDescriptionResource
    extends ApiAtlasBaseLegacyClusterDescriptionResource<ApiAtlasLegacyClusterDescriptionView> {

  private final AppSettings _appSettings;
  private final ApiAtlasClusterDescriptionUtil _apiAtlasClusterDescriptionUtil;
  private final ApiAtlasServerlessInstanceDescriptionUtil
      _apiAtlasServerlessInstanceDescriptionUtil;
  private final TenantUpgradeToServerlessSvc _tenantUpgradeToServerlessSvc;
  private final LogFetcherSvc _logFetcherSvc;
  private final NDSClusterConversionSvc _ndsClusterConversionSvc;
  private final NDSResourcePolicySvc _ndsResourcePolicySvc;
  private final LegacySharedApiShimToFlexSvc _legacySharedApiShimToFlexSvc;
  private final ApiTelemetryAttributeSvc _apiTelemetryAttributeSvc;
  private final FlexMigrationSvc _flexMigrationSvc;

  private static final Logger LOG =
      LoggerFactory.getLogger(ApiAtlasLegacyClusterDescriptionResource.class);

  @Inject
  public ApiAtlasLegacyClusterDescriptionResource(
      final AppSettings pSettings,
      final NDSUISvc pNDSUISvc,
      final NDSGroupSvc pNDSGroupSvc,
      final AuditSvc pAuditSvc,
      final TenantClusterConfigurationSvc pTenantClusterConfigurationSvc,
      final OnlineArchiveSvc pOnlineArchiveSvc,
      final TenantUpgradeSvc pTenantUpgradeSvc,
      final TenantUpgradeToServerlessSvc pTenantUpgradeToServerlessSvc,
      final ApiAtlasClusterDescriptionUtil pApiAtlasClusterDescriptionUtil,
      final ApiAtlasServerlessInstanceDescriptionUtil pApiAtlasServerlessInstanceDescriptionUtil,
      final LogFetcherSvc pLogFetcherSvc,
      final NDSClusterConversionSvc pNDSClusterConversionSvc,
      final NDSResourcePolicySvc pNDSResourcePolicySvc,
      final LegacySharedApiShimToFlexSvc pLegacySharedApiShimToFlexSvc,
      final ApiTelemetryAttributeSvc pApiTelemetryAttributeSvc,
      final FlexMigrationSvc pFlexMigrationSvc) {
    super(
        pSettings,
        pNDSUISvc,
        pNDSGroupSvc,
        pAuditSvc,
        pTenantClusterConfigurationSvc,
        pOnlineArchiveSvc,
        pTenantUpgradeSvc);
    _appSettings = pSettings;
    _apiAtlasClusterDescriptionUtil = pApiAtlasClusterDescriptionUtil;
    _apiAtlasServerlessInstanceDescriptionUtil = pApiAtlasServerlessInstanceDescriptionUtil;
    _tenantUpgradeToServerlessSvc = pTenantUpgradeToServerlessSvc;
    _logFetcherSvc = pLogFetcherSvc;
    _ndsClusterConversionSvc = pNDSClusterConversionSvc;
    _ndsResourcePolicySvc = pNDSResourcePolicySvc;
    _legacySharedApiShimToFlexSvc = pLegacySharedApiShimToFlexSvc;
    _apiTelemetryAttributeSvc = pApiTelemetryAttributeSvc;
    _flexMigrationSvc = pFlexMigrationSvc;
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({
    RoleSet.NAME.GROUP_READ_ONLY,
    RoleSet.NAME.GLOBAL_BAAS_INTERNAL_ADMIN,
    RoleSet.NAME.GLOBAL_CRASH_LOG_ANALYST
  })
  @AllowTemporaryApiKeys
  @Operation(
      summary = "Return All Clusters in One Project",
      operationId = "listLegacyClusters",
      description =
          "Returns the details for all clusters in the specific project to which you have access."
              + " Clusters contain a group of hosts that maintain the same data set. The response"
              + " does not include multi-cloud clusters. To return multi-cloud clusters, use Get"
              + " All Advanced Clusters. To use this resource, the requesting Service Account or"
              + " API Key must have the Project Read Only role.\n\n"
              + "This endpoint can also be used on Flex clusters that were created using the"
              + " [createCluster](https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Clusters/operation/createCluster)"
              + " endpoint or former M2/M5 clusters that have been migrated to Flex clusters until"
              + " January 2026. Please use the listFlexClusters endpoint for Flex clusters"
              + " instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "listFlexClusters",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/listFlexClusters"),
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "includeCount"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "includeDeletedWithRetainedBackups",
            description = "Flag that indicates whether to return Clusters with retain backups.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "boolean", defaultValue = "false"))
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = PaginatedLegacyClusterView.class))),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  public Response getAllClusters(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @QueryParam("includeDeletedWithRetainedBackups")
          final boolean pIncludeDeletedWithRetainedBackups)
      throws Exception {
    final List<ClusterDescriptionView> clusters =
        getNDSUISvc()
            .getClusterDescriptions(pGroup.getId(), false, pIncludeDeletedWithRetainedBackups)
            .stream()
            .filter(
                cluster ->
                    !(cluster.isServerlessTenantCluster()
                        || Optional.ofNullable(cluster.isCrossCloudCluster()).orElse(false)
                        || cluster.hasAsymmetricHardwareSpecs()))
            // we only want to include flex clusters if our shim logic is enabled AND our flex was
            // created from our shim logic/migrated from shared
            .filter(
                cluster ->
                    !cluster.isFlexTenantCluster()
                        || _apiAtlasClusterDescriptionUtil.isFlexClusterValid(cluster, pGroup))
            .toList();

    final List<ApiAtlasLegacyClusterDescriptionView> apiAtlasLegacyClusterDescriptionViews =
        new ArrayList<>();
    clusters.forEach(
        view -> {
          if (_apiAtlasClusterDescriptionUtil.isFlexShimLogicEnabled(pGroup)
              && view.isFlexTenantCluster()) {
            final FreeInstanceSize formerInstanceSize =
                (FreeInstanceSize)
                    view.getFlexTenantMigrationState()
                        .orElseThrow()
                        .getValidMigrationInstanceSize();
            apiAtlasLegacyClusterDescriptionViews.add(
                (ApiAtlasLegacyClusterDescriptionView)
                    _legacySharedApiShimToFlexSvc.convertFlexClusterDescriptionViewToSharedResponse(
                        view,
                        formerInstanceSize,
                        pNDSGroup,
                        _apiAtlasClusterDescriptionUtil.getClusterTags(view, pGroup),
                        pEnvelope,
                        VersionMediaType.V_2023_01_01_EXTENSION_TYPE));
          } else {
            apiAtlasLegacyClusterDescriptionViews.add(
                getApiAtlasClusterDescriptionView(view, pNDSGroup, pGroup));
          }
        });

    return handlePagination(pRequest, apiAtlasLegacyClusterDescriptionViews, pEnvelope);
  }

  protected ApiAtlasLegacyClusterDescriptionView getApiAtlasClusterDescriptionView(
      final ClusterDescriptionView pCluster, final NDSGroup pNDSGroup, final Group pGroup) {
    return new ApiAtlasLegacyClusterDescriptionView(
        ClusterDescriptionViewUtils.mergeReplicationSpecs(pCluster),
        getNDSUISvc().getProcessArgsOptional(pGroup.getId(), pCluster.getName()).orElse(null),
        _appSettings,
        ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
            pCluster, pNDSGroup),
        pNDSGroup.getCloudProviderContainers(),
        getOnlineArchiveSvc()
            .generateFederatedURI(
                pGroup.getId(), pCluster.getName(), pCluster.getMongoDBMajorVersion(), false),
        _apiAtlasClusterDescriptionUtil.getClusterTags(pCluster, pGroup));
  }

  /** See https://github.com/swagger-api/swagger-core/issues/3496 */
  @Schema(name = "PaginatedLegacyClusterView")
  public static class PaginatedLegacyClusterView
      extends ApiListView<ApiAtlasLegacyClusterDescriptionView> {}

  @GET
  @Path("/{clusterName}")
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({
    RoleSet.NAME.GROUP_READ_ONLY,
    RoleSet.NAME.GLOBAL_BAAS_INTERNAL_ADMIN,
    RoleSet.NAME.GLOBAL_QUERY_ENGINE_INTERNAL_ADMIN
  })
  @AllowTemporaryApiKeys
  @Operation(
      summary = "Return One Cluster in One Project",
      operationId = "getLegacyCluster",
      description =
          "Returns the details for one cluster in the specified project. Clusters contain a group"
              + " of hosts that maintain the same data set. The response does not include"
              + " multi-cloud clusters. To return a multi-cloud cluster, use Get One Advanced"
              + " Cluster. To use this resource, the requesting Service Account or API Key must"
              + " have the Project Read Only role.\n\n"
              + "This endpoint can also be used on Flex clusters that were created using the"
              + " [createCluster](https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Clusters/operation/createCluster)"
              + " endpoint or former M2/M5 clusters that have been migrated to Flex clusters until"
              + " January 2026. Please use the getFlexCluster endpoint for Flex clusters instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "getFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/getFlexCluster"),
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class))),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  public Response getCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Parameter(hidden = true) @PathParam("clusterName") final String pName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    // Checks that the cluster is not a serverless instance or a cross cloud cluster
    final ClusterDescriptionView view =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionView(pGroup, pName, pEnvelope, false);
    setAdminApiTelemetryAttributes(pRequest, pName, view.getUniqueId());

    final ApiView returnObject;

    if (_apiAtlasClusterDescriptionUtil.isFlexShimLogicEnabled(pGroup)
        && view.isFlexTenantCluster()) {
      final FreeInstanceSize formerInstanceSize =
          (FreeInstanceSize)
              view.getFlexTenantMigrationState().orElseThrow().getValidMigrationInstanceSize();
      returnObject =
          _legacySharedApiShimToFlexSvc.convertFlexClusterDescriptionViewToSharedResponse(
              view,
              formerInstanceSize,
              pNDSGroup,
              _apiAtlasClusterDescriptionUtil.getClusterTags(view, pGroup),
              pEnvelope,
              VersionMediaType.V_2023_01_01_EXTENSION_TYPE);
    } else {
      returnObject = getApiAtlasClusterDescriptionView(view, pNDSGroup, pGroup);
    }

    return new ApiResponseBuilder(pEnvelope).ok().content(returnObject).build();
  }

  @POST
  @Path("/tenantUpgrade")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_CLUSTER_MANAGER})
  @Operation(
      summary = "Upgrade One Shared-Tier Cluster",
      operationId = "upgradeSharedCluster",
      description =
          "Upgrades a shared-tier cluster to a Flex or Dedicated (M10+) cluster in the specified"
              + " project. To use this resource, the requesting Service Account or API Key must"
              + " have the Project Cluster Manager role. Each project supports up to 25 clusters."
              + " \n\n"
              + "This endpoint can also be used to upgrade Flex clusters that were created using"
              + " the [createCluster](https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Clusters/operation/createCluster)"
              + " API or former M2/M5 clusters that have been migrated to Flex clusters, using"
              + " instanceSizeName to “M2” or “M5” until January 2026. This functionality will be"
              + " available until January 2026, after which it will only be available for M0"
              + " clusters. Please use the upgradeFlexCluster endpoint instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "upgradeFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/upgradeFlexCluster"),
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty")
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "402", ref = "paymentRequired"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Details of the shared-tier cluster upgrade in the specified project.",
              content =
                  @Content(
                      mediaType = "application/json",
                      schema =
                          @Schema(
                              implementation =
                                  ApiAtlasLegacyTenantUpgradeClusterDescriptionView.class))))
  public Response upgradeTenantCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasLegacyTenantUpgradeClusterDescriptionView pClusterDescriptionView,
      @Context final AppUser pCurrentUser) {
    // Validate that the request body is not null
    if (pClusterDescriptionView == null) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(pEnvelope, "request body");
    }

    // Validate that the cluster name is provided
    if (!pClusterDescriptionView.hasName()) {
      throw ApiErrorCode.MISSING_ATTRIBUTE.exception(
          pEnvelope, ApiAtlasBaseClusterDescriptionView.Fields.NAME_FIELD);
    }

    final ClusterDescriptionView existingView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionView(
            pGroup, pClusterDescriptionView.getName(), pEnvelope, false);
    if (!existingView.isFlexTenantCluster() && !existingView.isFreeOrSharedTierTenantCluster()) {
      throw ApiErrorCode.CANNOT_UPGRADE_NON_SHARED_CLUSTER.exception(pEnvelope);
    }

    if (pClusterDescriptionView.isSharedTenant()) {
      throw ApiErrorCode.CANNOT_UPGRADE_TO_SHARED_CLUSTER.exception(pEnvelope);
    }

    try {
      _flexMigrationSvc.verifyFlexMigrationOrRollbackNotInProgress(
          existingView.getName(), existingView.getGroupId(), "clusterUpgrade", true);
      ApiAtlasLegacyClusterDescriptionView clusterDescriptionView = pClusterDescriptionView;
      if (pClusterDescriptionView.getMongoDBMajorVersion() == null) {
        if (VersionUtils.isGreaterThan(
            existingView.getMongoDBMajorVersion(), NDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION)) {
          clusterDescriptionView =
              clusterDescriptionView.toBuilder()
                  .mongoDBMajorVersion(existingView.getMongoDBMajorVersion())
                  .build();
        }
      }
      _apiAtlasClusterDescriptionUtil.validateLegacyClusterDescriptionViewForCreate(
          clusterDescriptionView, pGroup, pEnvelope);
      final ClusterDescriptionView upgradeView =
          _apiAtlasClusterDescriptionUtil.buildClusterDescriptionViewForCreate(
              clusterDescriptionView, pGroup, pNDSGroup, pEnvelope);

      final ClusterDescriptionProcessArgsView existingProcessArgs =
          getNDSUISvc().getProcessArgsOrDefault(pGroup.getId(), pClusterDescriptionView.getName());
      final ClusterDescriptionProcessArgsView mergedProcessArgsView =
          pClusterDescriptionView
              .getAdvancedConfiguration()
              .map(
                  advancedConfiguration ->
                      advancedConfiguration.mergeAndConvertToProcessArgsViewWithoutAdminFields(
                          existingProcessArgs))
              .orElse(existingProcessArgs);
      final AtlasResourcePolicyAuthResponse authResult =
          _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
              pGroup, upgradeView, mergedProcessArgsView, pAuditInfo);
      if (authResult.decision().isDeny()) {
        return ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.response(pEnvelope);
      }

      if (upgradeView.hasDifferentAnalyticsAndElectableSpecs()) {
        throw ApiErrorCode.ASYMMETRIC_HARDWARE_INVALID.exception(pEnvelope);
      }

      final ClusterDescription upgradeDescription =
          _ndsClusterConversionSvc.createProjectAgnosticDefaultClusterDescription(upgradeView);

      if (existingView.isFlexTenantCluster() && upgradeDescription.isSharedTenantCluster()) {
        throw ApiErrorCode.CANNOT_UPGRADE_FLEX_TO_SHARED_CLUSTER.exception(pEnvelope);
      }

      if (upgradeView.isFlexTenantCluster()) {
        final boolean isFlexTenantClusterConfigurationAvailable =
            getTenantClusterConfigurationSvc()
                .isFlexTenantClusterConfigurationAvailable(
                    pGroup.getId(),
                    upgradeDescription.getRegionName(),
                    FlexInstanceSize.fromInstanceSize(
                        upgradeDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow()),
                    upgradeDescription.getMongoDBMajorVersion());

        if (!isFlexTenantClusterConfigurationAvailable) {
          throw ApiErrorCode.REGION_UNAVAILABLE.exception(
              pEnvelope,
              upgradeDescription.getRegionName().getProviderName(),
              upgradeDescription.getRegionName().getName());
        }
      }

      if (upgradeView.isFreeOrSharedTierTenantCluster()) {
        final boolean isSharedTenantClusterConfigurationAvailable =
            getTenantClusterConfigurationSvc()
                .isSharedTenantClusterConfigurationAvailable(
                    pGroup.getId(),
                    upgradeDescription.getRegionName(),
                    FreeInstanceSize.fromInstanceSize(
                        upgradeDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow()),
                    upgradeDescription.getMongoDBMajorVersion());

        if (!isSharedTenantClusterConfigurationAvailable) {
          throw ApiErrorCode.REGION_UNAVAILABLE.exception(
              pEnvelope,
              upgradeDescription.getRegionName().getProviderName(),
              upgradeDescription.getRegionName().getName());
        }
      }

      getTenantUpgradeSvc()
          .startClusterUpgrade(
              pOrganization, upgradeView, pGroup.getId(), pCurrentUser, pAuditInfo, pRequest);

      if (pClusterDescriptionView.hasTags()) {
        _apiAtlasClusterDescriptionUtil.saveClusterTags(
            existingView, pClusterDescriptionView.getTags(), pGroup, pAuditInfo, pEnvelope);
      }

      return getCluster(pRequest, pGroup, pNDSGroup, upgradeView.getName(), pEnvelope);
    } catch (final SvcException pE) {
      if (BillingErrorCode.PAYMENT_METHOD_MISSING.equals(pE.getErrorCode())) {
        throw ApiErrorCode.NO_PAYMENT_INFORMATION_FOUND.exception(pEnvelope, pGroup.getId());
      }

      handleGeneralExceptions(pE, pClusterDescriptionView.getName(), pGroup, pEnvelope);
    }
    // should never get here
    throw ApiErrorCode.UNEXPECTED_ERROR.exception(pEnvelope);
  }

  @POST
  @Path("tenantUpgradeToServerless")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({NAME.GROUP_CLUSTER_MANAGER})
  @Deprecated(since = "2025-02-05")
  @Sunset(value = "2025-02-05")
  @Operation(
      summary = "Upgrade One Shared-Tier Cluster to One Serverless Instance",
      operationId = "upgradeSharedClusterToServerless",
      description =
          "This endpoint has been deprecated as of February 2025 as we no longer support the"
              + " creation of new serverless instances. Please use the upgradeFlexCluster endpoint"
              + " to upgrade Flex clusters.\n\n"
              + " Upgrades a shared-tier cluster to a serverless instance in the specified project."
              + " To use this resource, the requesting Service Account or API Key must have the"
              + " Project Cluster Manager role.",
      externalDocs =
          @ExternalDocumentation(
              description = "upgradeFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/upgradeFlexCluster"),
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty")
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema =
                        @Schema(implementation = ApiAtlasServerlessInstanceDescriptionView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-sunset", value = "2025-02-05")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "402", ref = "paymentRequired"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Details of the shared-tier cluster upgrade in the specified project.",
              content =
                  @Content(
                      mediaType = "application/json",
                      schema =
                          @Schema(
                              implementation = ApiAtlasServerlessInstanceDescriptionView.class))),
      extensions = {
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  // ApiX recommends to set the date one day after target deployment to ensure the
                  // cron job picks up the changes
                  name = "2025-02-06",
                  value =
                      "Deprecating Tenant Upgrade to Serverless due to Flex GA. Atlas no longer"
                          + " supports the creation of new serverless instances.")
            })
      })
  public Response upgradeTenantClusterToServerless(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasServerlessInstanceDescriptionView pServerlessInstanceDescriptionView,
      @Context final AppUser pCurrentUser) {
    throw ApiErrorCode.SHARED_SERVERLESS_ENDPOINT_NOT_SUPPORTED.exception(pEnvelope);
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Create One Cluster",
      operationId = "createLegacyCluster",
      description =
          "Creates one cluster in the specific project. Clusters contain a group of hosts that"
              + " maintain the same data set. This resource does not create multi-cloud clusters."
              + " To create a multi-cloud cluster, use Create One Advanced Cluster. To use this"
              + " resource, the requesting Service Account or API Key must have the Project Owner"
              + " role.\n\n"
              + " When you deploy an M10+ dedicated cluster, Atlas creates a VPC for the selected"
              + " provider and region or regions if no existing VPC or VPC peering connection"
              + " exists for that provider and region. Atlas assigns the VPC a Classless"
              + " Inter-Domain Routing (CIDR) block.\n\n"
              + "Please note that using an instanceSize of M2 or M5 will create a Flex cluster"
              + " instead. Support for the instanceSize of M2 or M5 will be discontinued in January"
              + " 2026. We recommend using the createFlexCluster API for such configurations moving"
              + " forward.",
      externalDocs =
          @ExternalDocumentation(
              description = "createFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/createFlexCluster"),
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty")
      },
      extensions = {
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2025-06-05",
                  value =
                      "Fixed a bug that previously permitted users to configure multiple"
                          + " regionConfigs for the same region and cloud provider within a"
                          + " replicationSpec"),
            })
      },
      responses = {
        @ApiResponse(
            responseCode = "201",
            description = ResponseDescriptions.CREATED,
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "402", ref = "paymentRequired"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Cluster to create in this project.",
              content =
                  @Content(
                      mediaType = "application/json",
                      schema =
                          @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class))))
  public Response createCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasLegacyClusterDescriptionView pClusterDescriptionView,
      @Context final AppUser pCurrentUser)
      throws SvcException {
    if (pClusterDescriptionView.isServerless()) {
      if (_appSettings.isServerlessEnabled()) {
        throw ApiErrorCode.CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API.exception(
            pEnvelope, pClusterDescriptionView.getName());
      } else {
        throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
            pEnvelope, ApiAtlasServerlessProviderSettingsView.PROVIDER_NAME_FIELD);
      }
    } else if (pClusterDescriptionView.isFlexTier()) {
      if (_appSettings.isFlexEnabled()) {
        throw ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.exception(
            pEnvelope, pClusterDescriptionView.getName());
      } else {
        throw ApiErrorCode.INVALID_ATTRIBUTE.exception(
            pEnvelope, ApiAtlasServerlessProviderSettingsView.PROVIDER_NAME_FIELD);
      }
    }

    try {
      final ClusterDescriptionView view;
      _apiAtlasClusterDescriptionUtil.validateLegacyClusterDescriptionViewForCreate(
          pClusterDescriptionView, pGroup, pEnvelope);
      // Creating our flex cluster description view
      if (_apiAtlasClusterDescriptionUtil.isFlexShimLogicEnabled(pGroup)
          && pClusterDescriptionView.isSharedTenant()) {
        final ApiAtlasFreeProviderSettingsView providerSettingsView =
            (ApiAtlasFreeProviderSettingsView) pClusterDescriptionView.getProviderSettings();
        view =
            _legacySharedApiShimToFlexSvc.createFlexClusterDescriptionViewFromShared(
                pGroup,
                pNDSGroup,
                pEnvelope,
                pClusterDescriptionView.getName(),
                providerSettingsView.getBackingProvider().name(),
                providerSettingsView.getRegionName(),
                providerSettingsView.getInstanceSize().getInstanceSize(),
                pClusterDescriptionView.hasReplicationSpecs()
                        && !pClusterDescriptionView.getReplicationSpecs().isEmpty()
                    ? pClusterDescriptionView.getReplicationSpecs().get(0).getZoneName()
                    : null);
      } else {
        view =
            _apiAtlasClusterDescriptionUtil.buildClusterDescriptionViewForCreate(
                pClusterDescriptionView, pGroup, pNDSGroup, pEnvelope);
      }

      if (view.hasDifferentAnalyticsAndElectableSpecs()) {
        throw ApiErrorCode.ASYMMETRIC_HARDWARE_INVALID.exception(pEnvelope);
      }

      final ClusterDescriptionProcessArgsView existingProcessArgs =
          getNDSUISvc().getProcessArgsOrDefault(view.getGroupId(), view.getName());
      final ClusterDescriptionProcessArgsView mergedProcessArgsView =
          pClusterDescriptionView
              .getAdvancedConfiguration()
              .map(
                  advancedConfiguration ->
                      advancedConfiguration.mergeAndConvertToProcessArgsViewWithoutAdminFields(
                          existingProcessArgs))
              .orElse(existingProcessArgs);
      final AtlasResourcePolicyAuthResponse authResult =
          _ndsResourcePolicySvc.isCreateClusterRequestAuthorized(
              pGroup, view, mergedProcessArgsView, pAuditInfo);

      if (authResult.decision().isDeny()) {
        return ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.response(pEnvelope);
      }

      try {
        if (FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI,
            _appSettings,
            pOrganization,
            null)) {
          getNDSUISvc()
              .createProcessArgs(
                  pGroup,
                  pNDSGroup,
                  ClusterCreateContext.forClusterApi(false),
                  view,
                  mergedProcessArgsView,
                  pAuditInfo,
                  pCurrentUser);
        }

        getNDSUISvc().verifyClusterDescriptionV2SchemaFromPublicApi(view, pGroup.getId());
        getNDSUISvc()
            .createCluster(
                pOrganization,
                pGroup.getId(),
                view,
                ClusterCreateContext.forClusterApi(false),
                pCurrentUser,
                pAuditInfo,
                pRequest,
                pClusterDescriptionView.getTags());

      } catch (final SvcException | UncheckedSvcException pE) {
        if (NDSErrorCode.FREE_CLUSTER_LIMIT_REACHED.equals(pE.getErrorCode())) {
          throw ApiErrorCode.MAX_FREE_CLUSTERS_PER_PROJECT_EXCEEDED.exception(pEnvelope);
        }

        if (BillingErrorCode.PAYMENT_METHOD_MISSING.equals(pE.getErrorCode())) {
          throw ApiErrorCode.NO_PAYMENT_INFORMATION_FOUND.exception(pEnvelope, pGroup.getId());
        }

        if (NDSErrorCode.DUPLICATE_CLUSTER_NAME.equals(pE.getErrorCode())) {
          throw ApiErrorCode.DUPLICATE_CLUSTER_NAME.exception(
              pEnvelope,
              _appSettings.isServerlessEnabled() ? "cluster or serverless instance" : "cluster",
              view.getName(),
              pGroup.getId());
        }

        if (NDSErrorCode.DUPLICATE_REGION_CONFIGS.equals(pE.getErrorCode())) {
          throw ApiErrorCode.DUPLICATE_REGION_CONFIGS.exception(pEnvelope);
        }

        if (NDSErrorCode.CLUSTER_NAME_TOO_LONG.equals(pE.getErrorCode())) {
          throw ApiErrorCode.CLUSTER_NAME_TOO_LONG.exception(
              pEnvelope,
              view.getName(),
              _apiAtlasClusterDescriptionUtil.getMaxClusterLengthForError(pNDSGroup));
        }

        if (NDSErrorCode.ATLAS_RESERVED_CLUSTER_NAME.equals(pE.getErrorCode())) {
          throw ApiErrorCode.ATLAS_RESERVED_CLUSTER_NAME.exception(pEnvelope, view.getName());
        }

        if (NDSErrorCode.DUPLICATE_CLUSTER_NAME_PREFIX.equals(pE.getErrorCode())) {
          throw ApiErrorCode.DUPLICATE_CLUSTER_NAME_PREFIX.exception(
              pEnvelope,
              view.getName(),
              getNDSGroupSvc()
                  .find(pGroup.getId())
                  .get()
                  .getLimits()
                  .getClusterNameUniquePrefixLength(),
              _appSettings.isServerlessEnabled() ? "cluster or serverless instance" : "cluster");
        }

        if (NDSErrorCode.CLUSTER_NAME_PREFIX_INVALID.equals(pE.getErrorCode())) {
          final int clusterNameUniquePrefixLength =
              getNDSGroupSvc()
                  .find(pGroup.getId())
                  .get()
                  .getLimits()
                  .getClusterNameUniquePrefixLength();
          final String clusterNamePrefix =
              view.getName()
                  .substring(0, Math.min(clusterNameUniquePrefixLength, view.getName().length()));
          throw ApiErrorCode.CLUSTER_NAME_PREFIX_INVALID.exception(
              pEnvelope, view.getName(), clusterNameUniquePrefixLength, clusterNamePrefix);
        }

        if (NDSErrorCode.CONTAINER_IN_USE.equals(pE.getErrorCode())) {
          throw ApiErrorCode.CONTAINERS_IN_USE.exception(pEnvelope, pE.getMessage());
        }

        if (NDSErrorCode.UNSUPPORTED_VERSION_FOR_LDAP_AUTHENTICATION.equals(pE.getErrorCode())) {
          throw ApiErrorCode.UNSUPPORTED_VERSION_FOR_LDAP_AUTHENTICATION.exception(pEnvelope);
        }

        if (NDSErrorCode.CLUSTER_VERSION_DEPRECATED.equals(pE.getErrorCode())) {
          throw ApiErrorCode.ATLAS_CLUSTER_VERSION_DEPRECATED.exception(pEnvelope);
        }

        if (NDSErrorCode.CROSS_REGION_NETWORK_PERMISSIONS_LIMIT_EXCEEDED.equals(
            pE.getErrorCode())) {
          throw ApiErrorCode.CROSS_REGION_NETWORK_PERMISSIONS_LIMIT_EXCEEDED.exception(
              pEnvelope,
              getNDSGroupSvc()
                  .find(pGroup.getId())
                  .get()
                  .getLimits()
                  .getMaxCrossRegionNetworkPermissionEntries());
        }

        if (NDSErrorCode.NO_CAPACITY.equals(pE.getErrorCode())) {
          LOG.debug("[CLOUDP-275770] Handling NO_CAPACITY error code", pE);
          throw ApiErrorCode.OUT_OF_CAPACITY.exception(pEnvelope);
        }

        if (NDSErrorCode.DUPLICATE_ZONE_NAME.equals(pE.getErrorCode())) {
          throw ApiErrorCode.DUPLICATE_ZONE_NAME.exception(pEnvelope);
        }

        if (NDSErrorCode.UNSUPPORTED_FOR_PRIVATE_IP_MODE.equals(pE.getErrorCode())) {
          throw ApiErrorCode.UNSUPPORTED_FOR_PRIVATE_IP_MODE.exception(pEnvelope, pE.getMessage());
        }

        if (NDSErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_MONGODB_VERSION.equals(
            pE.getErrorCode())) {
          throw ApiErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_MONGODB_VERSION.exception(
              pEnvelope, pE.getMessage());
        }

        if (NDSErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_AWS_CLUSTERS.equals(
            pE.getErrorCode())) {
          throw ApiErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_AWS_CLUSTERS.exception(
              pEnvelope, pE.getMessage());
        }

        if (NDSErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_CLUSTERS.equals(
            pE.getErrorCode())) {
          throw ApiErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_CLUSTERS.exception(
              pEnvelope, pE.getMessage());
        }

        if (NDSErrorCode.REGION_UNAVAILABLE.equals(pE.getErrorCode())) {
          throw ApiErrorCode.REGION_UNAVAILABLE.exception(
              pEnvelope, pE.getMessageParams().get(0), pE.getMessageParams().get(1));
        }

        if (NDSErrorCode.DISK_SIZE_INVALID_FOR_AZURE.equals(pE.getErrorCode())) {
          throw ApiErrorCode.DISK_SIZE_INVALID_FOR_AZURE.exception(pEnvelope);
        }

        if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE.equals(pE.getErrorCode())) {
          throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE.exception(pEnvelope);
        }

        if (NDSErrorCode.CANNOT_SET_SELF_MANAGED_SHARDING_FOR_NON_GLOBAL_CLUSTER.equals(
            pE.getErrorCode())) {
          throw ApiErrorCode.CANNOT_SET_SELF_MANAGED_SHARDING_FOR_NON_GLOBAL_CLUSTER.exception(
              pEnvelope, pClusterDescriptionView.getName());
        }
        if (NDSErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION.equals(
            pE.getErrorCode())) {
          throw ApiErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION
              .exception(pEnvelope, pE.getMessageParams());
        }
        _apiAtlasClusterDescriptionUtil.handleCreateUpdateProcessArgsExceptions(
            pE, view.getName(), pGroup, pEnvelope, _appSettings);
        handleGeneralExceptions(pE, view.getName(), pGroup, pEnvelope);
      }

      final ClusterDescriptionView clusterDescriptionView =
          getNDSUISvc().getClusterDescription(pGroup.getId(), view.getName());

      final List<ApiAtlasTagView> tags =
          _apiAtlasClusterDescriptionUtil.saveClusterTags(
              clusterDescriptionView,
              pClusterDescriptionView.getTags(),
              pGroup,
              pAuditInfo,
              pEnvelope);

      if (_apiAtlasClusterDescriptionUtil.isFlexShimLogicEnabled(pGroup)
          && pClusterDescriptionView.isSharedTenant()) {
        if (clusterDescriptionView.isFlexTenantCluster()) {
          final FlexTenantMigrationState flexTenantMigrationState =
              clusterDescriptionView.getFlexTenantMigrationState().orElseThrow();
          final ApiAtlasLegacyClusterDescriptionView createdView =
              (ApiAtlasLegacyClusterDescriptionView)
                  _legacySharedApiShimToFlexSvc.convertFlexClusterDescriptionViewToSharedResponse(
                      clusterDescriptionView,
                      flexTenantMigrationState.getTenantApiInstanceSize().orElseThrow(),
                      pNDSGroup,
                      tags,
                      pEnvelope,
                      VersionMediaType.V_2023_01_01_EXTENSION_TYPE);
          return new ApiResponseBuilder(pEnvelope).created().content(createdView).build();
        } else {
          // If somehow our shimming logic failed and we do not end up creating a flex cluster,
          // don't throw an exception and just return the original response of our clusters API. We
          // should not be hitting this state.
          LOG.error(
              String.format(
                  "We should have created a flex cluster %s in %s but instead created a %s.",
                  clusterDescriptionView.getName(),
                  clusterDescriptionView.getGroupId(),
                  clusterDescriptionView.getInstanceSize()));
        }
      }

      final ApiAtlasLegacyClusterDescriptionView createdView =
          new ApiAtlasLegacyClusterDescriptionView(
              ClusterDescriptionViewUtils.mergeReplicationSpecs(clusterDescriptionView),
              getNDSUISvc()
                  .getProcessArgsOptional(pGroup.getId(), pClusterDescriptionView.getName())
                  .orElse(null),
              _appSettings,
              ApiAtlasClusterDescriptionConnectionStringsUtil.shouldExposePrivateStringsForCluster(
                  clusterDescriptionView, pNDSGroup),
              pNDSGroup.getCloudProviderContainers(),
              getOnlineArchiveSvc()
                  .generateFederatedURI(
                      pGroup.getId(),
                      clusterDescriptionView.getName(),
                      clusterDescriptionView.getMongoDBMajorVersion(),
                      false),
              tags);

      setAdminApiTelemetryAttributes(pRequest, createdView.getName(), createdView.getId());
      return new ApiResponseBuilder(pEnvelope).created().content(createdView).build();
    } catch (SvcException pE) {
      handleGeneralExceptions(pE, pClusterDescriptionView.getName(), pGroup, pEnvelope);
    }
    throw ApiErrorCode.UNEXPECTED_ERROR.exception(pEnvelope);
  }

  @PATCH
  @Path("/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({NAME.GROUP_CLUSTER_MANAGER})
  @Operation(
      summary = "Update Configuration of One Cluster",
      operationId = "updateClusterConfiguration",
      description =
          "Update the details for one cluster in the specified project. Clusters contain a group of"
              + " hosts that maintain the same data set. This resource does not update multi-cloud"
              + " clusters. To update a multi-cloud cluster, use Update One Advanced Cluster. You"
              + " can upgrade your cluster only one major version at a time. You can't skip any"
              + " major versions. Each major version contains some features that might not be"
              + " backward-compatible with previous versions. Check the Release Notes for changes"
              + " that might affect your applications. After you upgrade the MongoDB major version,"
              + " you will not be able to downgrade to previous versions.\n\n"
              + " As of MongoDB version 4.2, Legacy Backups are deprecated in favor of Cloud"
              + " Backups. When you upgrade to version 4.2 or later, your backup system upgrades to"
              + " Cloud Backups if it is currently set to Legacy Backup. After this upgrade, all"
              + " your existing legacy backup snapshots remain available. They expire over time in"
              + " accordance with your retention policy. Your backup policy resets to the default"
              + " schedule. If you had a custom backup policy in place with legacy backups, you"
              + " must re-create it with the procedure outlined in the [Cloud Backup"
              + " documentation](https://www.mongodb.com/docs/atlas/backup/cloud-backup/scheduling/#std-label-cloud-provider-backup-schedule).\n\n"
              + " To use this resource, the requesting Service Account or API Key must have the"
              + " Project Cluster Manager role. This feature isn't available for `M0` free"
              + " clusters, `M2` and `M5` shared-tier, and Flex clusters.\n\n"
              + " The total number of nodes in clusters spanning across regions has a specific"
              + " constraint on a per-project basis. MongoDB Cloud limits the total number of nodes"
              + " in other regions in one project to a total of 40. This total excludes Google"
              + " Cloud regions communicating with each other, free clusters, or shared clusters."
              + " The total number of nodes between any two regions must meet this constraint. For"
              + " example, if a project has nodes in clusters spread across three regions: 30 nodes"
              + " in Region A, 10 nodes in Region B, and 5 nodes in Region C, you can add only 5"
              + " more nodes to Region C because if you exclude Region C, Region A + Region B = 40."
              + " If you exclude Region B, Region A + Region C = 35, <= 40. If you exclude Region"
              + " A, Region B + Region C = 15, <= 40. Each combination of regions with the added 5"
              + " nodes still meets the per-project constraint: Region A + B = 40, Region A + C ="
              + " 40, and Region B + C = 20. You can't create a multi-region cluster in a project"
              + " if it has one or more clusters spanning 40 or more nodes in other regions.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      extensions = {
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2025-06-05",
                  value =
                      "Fixed a bug that previously permitted users to configure multiple"
                          + " regionConfigs for the same region and cloud provider within a"
                          + " replicationSpec"),
            })
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Cluster to update in the specified project.",
              content =
                  @Content(
                      mediaType = "application/json",
                      schema =
                          @Schema(implementation = ApiAtlasLegacyClusterDescriptionView.class))))
  public Response updateCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final NDSGroup pNDSGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasLegacyClusterDescriptionView pClusterDescriptionView)
      throws Exception {
    try {
      // buildClusterViewForUpdate does not allow updates of free or serverless clusters
      final ClusterDescriptionView existingClusterView =
          getNDSUISvc().getClusterDescription(pGroup.getId(), pName);
      final ClusterDescriptionView updatedClusterView =
          _apiAtlasClusterDescriptionUtil.buildClusterDescriptionViewForUpdate(
              existingClusterView, pClusterDescriptionView, pGroup, pNDSGroup, pName, pEnvelope);

      if (pClusterDescriptionView.hasTags()) {
        _apiAtlasClusterDescriptionUtil.saveClusterTags(
            existingClusterView, pClusterDescriptionView.getTags(), pGroup, pAuditInfo, pEnvelope);
      }

      if (updatedClusterView.hasDifferentAnalyticsAndElectableSpecs()) {
        throw ApiErrorCode.ASYMMETRIC_HARDWARE_INVALID.exception(pEnvelope);
      }

      if (existingClusterView != null
          && Objects.equals(existingClusterView.getAutoScalingMode(), AutoScalingMode.SHARD)) {
        final boolean requestContainsAutoScalingEnablement =
            pClusterDescriptionView.getAutoScaling() != null;
        final boolean requestContainsAutoScalingSizeBounds =
            (pClusterDescriptionView.getProviderSettings() != null
                && pClusterDescriptionView.getProviderSettings().getAutoScaling() != null);
        final RegionConfigView regionConfig =
            existingClusterView.getReplicationSpecList().get(0).getRegionConfigs().get(0);
        final boolean existingClusterAutoScalingEnabled =
            regionConfig.getBaseAutoScaling().getCompute().isEnabled()
                || (regionConfig.getAnalyticsAutoScaling() != null
                    && regionConfig.getAnalyticsAutoScaling().getCompute().isEnabled());

        getNDSUISvc()
            .getClusterDescriptionApiShardScalingModeOldApiUpdateCounter()
            .labels(
                "2023-01-01",
                (requestContainsAutoScalingEnablement || requestContainsAutoScalingSizeBounds)
                    ? "autoscaling present"
                    : "autoscaling not present",
                existingClusterAutoScalingEnabled
                    ? "existing autoscaling enabled"
                    : "existing autoscaling disabled")
            .inc();
        LOG.info(
            "updateCluster called with autoscaling present with groupId={} clusterName={}"
                + " apiVersion={} existingAutoscaling={}",
            existingClusterView.getGroupId(),
            existingClusterView.getName(),
            "2023-01-01",
            existingClusterAutoScalingEnabled);
      }

      if (updatedClusterView.isPausedNVMeCluster()) {
        throw ApiErrorCode.CANNOT_PAUSE_NVME_CLUSTER.exception(pEnvelope);
      }

      // Fall back to default process args when PA do not exist for resource policy evaluation.
      final Optional<ClusterDescriptionProcessArgsView> existingProcessArgs =
          getNDSUISvc()
              .getProcessArgsOptional(
                  updatedClusterView.getGroupId(), updatedClusterView.getName());
      if (existingProcessArgs.isEmpty()) {
        LOG.warn(
            "No process args document found. Falling back to default process args. {}",
            StructuredArguments.entries(Map.of("groupId", pGroup.getId(), "clusterName", pName)));
      }
      final ClusterDescriptionProcessArgsView mergedProcessArgsView =
          pClusterDescriptionView
              .getAdvancedConfiguration()
              .map(
                  advancedConfiguration ->
                      advancedConfiguration.mergeAndConvertToProcessArgsViewWithoutAdminFields(
                          existingProcessArgs.orElse(getNDSUISvc().getDefaultProcessArgs())))
              .orElse(existingProcessArgs.orElse(getNDSUISvc().getDefaultProcessArgs()));

      final AtlasResourcePolicyAuthResponse authResult =
          _ndsResourcePolicySvc.isUpdateClusterRequestAuthorized(
              pGroup,
              existingClusterView.getUniqueId(),
              updatedClusterView,
              mergedProcessArgsView,
              pAuditInfo);

      if (authResult.decision().isDeny()) {
        return ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.response(pEnvelope);
      }

      if (FeatureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI,
              _appSettings,
              pOrganization,
              null)
          && pClusterDescriptionView.getAdvancedConfiguration().isPresent()) {
        // Attempt to update process args if advancedConfiguration was specified
        getNDSUISvc()
            .updateProcessArgs(pGroup, pName, mergedProcessArgsView, pAuditInfo, null, pAppUser);
      }

      getNDSUISvc()
          .verifyClusterDescriptionV2SchemaFromPublicApi(updatedClusterView, pGroup.getId());
      getNDSUISvc()
          .updateCluster(
              pOrganization,
              pGroup.getId(),
              pName,
              updatedClusterView,
              pAppUser,
              pAuditInfo,
              pRequest,
              ClusterUpdateContext.forClusterApi(false));
    } catch (final SvcException pE) {
      if (pE.getErrorCode() == NDSErrorCode.CLUSTER_RESUMED_RECENTLY) {
        throw ApiErrorCode.CANNOT_PAUSE_RECENTLY_RESUMED_CLUSTER.exception(
            pEnvelope, InstanceHardware.MINIMUM_RESUME_RUNTIME.toMinutes());
      }
      if (pE.getErrorCode() == NDSErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_MONGODB_VERSION) {
        throw ApiErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_MONGODB_VERSION_UPGRADE.exception(
            pEnvelope);
      }
      if (pE.getErrorCode() == NDSErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_CLUSTER_UPDATE) {
        throw ApiErrorCode.CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_CLUSTER_UPDATE.exception(pEnvelope);
      }
      if (pE.getErrorCode() == NDSErrorCode.CANNOT_SWITCH_CPS_BACKUP_TO_CONTINUOUS) {
        throw ApiErrorCode.CANNOT_SWITCH_CPS_BACKUP_TO_CONTINUOUS.exception(pEnvelope);
      }
      if (pE.getErrorCode() == NDSErrorCode.INSUFFICIENT_DISK_SPACE_ON_REMAINING_SHARDS) {
        throw ApiErrorCode.INSUFFICIENT_DISK_SPACE_ON_REMAINING_SHARDS.exception(pEnvelope);
      }
      if (pE.getErrorCode() == NDSErrorCode.CANNOT_DECREASE_DISK_SIZE_DURING_SHARD_REMOVAL) {
        throw ApiErrorCode.CANNOT_DECREASE_DISK_SIZE_DURING_SHARD_REMOVAL.exception(pEnvelope);
      }
      if (pE.getErrorCode()
          == NDSErrorCode.SHARDED_CLUSTERS_INCOMPATIBLE_WITH_DEVICE_SYNC_OR_PREIMAGE_TRIGGERS) {
        throw ApiErrorCode.SHARDED_CLUSTERS_INCOMPATIBLE_WITH_DEVICE_SYNC_OR_PREIMAGE_TRIGGERS
            .exception(pEnvelope);
      }
      if (pE.getErrorCode() == NDSErrorCode.SHARED_CLUSTERS_INCOMPATIBLE_WITH_REALM_SYNC) {
        throw ApiErrorCode.SHARED_CLUSTERS_INCOMPATIBLE_WITH_REALM_SYNC.exception(pEnvelope);
      }
      if (pE.getErrorCode() == NDSErrorCode.CRITERIA_NOT_MET_FOR_FORCE_RECONFIG) {
        throw ApiErrorCode.CRITERIA_NOT_MET_FOR_FORCE_RECONFIG.exception(pEnvelope);
      }
      if (pE.getErrorCode() == NDSErrorCode.CANNOT_UPDATE_PAUSED_CLUSTER) {
        throw ApiErrorCode.CANNOT_UPDATE_PAUSED_CLUSTER.exception(pEnvelope);
      }
      if (pE.getErrorCode() == NDSErrorCode.INVALID_UPGRADE_FOR_EIGHT_ZERO_ZERO) {
        throw ApiErrorCode.INVALID_UPGRADE_FOR_EIGHT_ZERO_ZERO.exception(pEnvelope);
      }
      if (pE.getErrorCode() == NDSErrorCode.INVALID_SHARD_COUNT_FOR_SHARDING_UPGRADE) {
        throw ApiErrorCode.INVALID_SHARD_COUNT_FOR_SHARDING_UPGRADE.exception(pEnvelope, pName);
      }
      if (NDSErrorCode.DISK_SIZE_INVALID_FOR_AZURE.equals(pE.getErrorCode())) {
        throw ApiErrorCode.DISK_SIZE_INVALID_FOR_AZURE.exception(pEnvelope);
      }
      if (NDSErrorCode.REGION_UNAVAILABLE.equals(pE.getErrorCode())) {
        throw ApiErrorCode.REGION_UNAVAILABLE.exception(
            pEnvelope, pE.getMessageParams().get(0), pE.getMessageParams().get(1));
      }

      if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE.equals(pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE.exception(pEnvelope);
      }
      if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_SEARCH_INDEXES.equals(
          pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_SEARCH_INDEXES.exception(
            pEnvelope);
      }
      if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_TIME_SERIES.equals(
          pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_TIME_SERIES.exception(
            pEnvelope);
      }
      if (NDSErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_QUERYABLE_ENCRYPTION.equals(
          pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_QUERYABLE_ENCRYPTION
            .exception(pEnvelope);
      }
      if (NDSErrorCode.CUSTOM_ZONE_MAPPINGS_INVALID.equals(pE.getErrorCode())) {
        throw ApiErrorCode.CUSTOM_ZONE_MAPPINGS_INVALID.exception(pEnvelope);
      }

      if (NDSErrorCode.DUPLICATE_REGION_CONFIGS.equals(pE.getErrorCode())) {
        throw ApiErrorCode.DUPLICATE_REGION_CONFIGS.exception(pEnvelope);
      }

      handleGeneralExceptions(pE, pName, pGroup, pEnvelope);
    }
    return getCluster(pRequest, pGroup, pNDSGroup, pName, pEnvelope);
  }

  @POST
  @Path("/{clusterName}/restartPrimaries")
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_CLUSTER_MANAGER})
  @Operation(
      summary = "Test Failover for One Cluster",
      operationId = "testLegacyFailover",
      description =
          "Starts a failover test for the specified cluster in the specified project. Clusters"
              + " contain a group of hosts that maintain the same data set. A failover test checks"
              + " how MongoDB Cloud handles the failure of the cluster's primary node. During the"
              + " test, MongoDB Cloud shuts down the primary node and elects a new primary. To use"
              + " this resource, the requesting Service Account or API Key must have the Project"
              + " Cluster Manager role.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(responseCode = "200", description = ResponseDescriptions.OK),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  public Response restartClusterPrimaries(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    try {
      // restartPrimariesForCluster does not allow restarts of free or serverless clusters
      getNDSUISvc().restartPrimariesForCluster(pGroup, pClusterName, pAuditInfo);
    } catch (final SvcException pE) {
      handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }
    return new ApiResponseBuilder(pEnvelope).ok().build();
  }

  @GET
  @Path("/{clusterName}/globalWrites")
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY})
  @AllowTemporaryApiKeys
  @Operation(
      summary = "Return All Global Clusters Data",
      operationId = "getGeoSharding",
      description =
          "Returns all managed namespaces and custom zone mappings for the specified global"
              + " cluster. Managed namespaces identify collections using the database name, the"
              + " dot separator, and the collection name. Custom zone mappings matches ISO 3166-2"
              + " location codes to zones in your global cluster. To use this resource, the"
              + " requesting Service Account or API Key must have the Project Read Only role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ApiAtlasGeoShardingView.class))),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"))
  public Response getGeoSharding(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    // Checks that the cluster is not a serverless instance or a cross cloud cluster
    final ClusterDescriptionView view =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionView(
            pGroup, pClusterName, pEnvelope, false);

    return new ApiResponseBuilder(pEnvelope)
        .ok()
        .content(
            (new ApiAtlasGeoShardingView(view.getGeoShardingView(), view.getReplicationSpecList())))
        .build();
  }

  @POST
  @Path("/{clusterName}/globalWrites/managedNamespaces")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN})
  @Operation(
      summary = "Create One Managed Namespace in One Global Cluster",
      operationId = "createLegacyManagedNamespace",
      description =
          "Creates one managed namespace within the specified global cluster. A managed namespace"
              + " identifies a collection using the database name, the dot separator, and the"
              + " collection name. To use this resource, the requesting Service Account or API Key"
              + " must have the Project Data Access Admin role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ApiAtlasGeoShardingView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "405", ref = "methodNotAllowed"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Managed namespace to create within the specified global cluster.",
              content =
                  @Content(
                      mediaType = "application/json",
                      schema = @Schema(implementation = ApiAtlasManagedNamespacesView.class))),
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"))
  public Response addManagedNamespace(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasManagedNamespacesView pManagedNamespacesView) {
    final String db = pManagedNamespacesView.getDb();
    final String collection = pManagedNamespacesView.getCollection();
    final String customShardKey = pManagedNamespacesView.getCustomShardKey();
    final boolean isShardKeyUnique = pManagedNamespacesView.isShardKeyUnique();
    final boolean isCustomShardKeyHashed = pManagedNamespacesView.isCustomShardKeyHashed();
    final boolean presplitHashedZones = pManagedNamespacesView.isPresplitHashedZones();
    final Long numInitialChunks = pManagedNamespacesView.getNumInitialChunks();

    final List<String> missingAttributes = new ArrayList<>();
    if (db == null || db.isEmpty()) {
      missingAttributes.add(ApiAtlasManagedNamespacesView.DB_FIELD);
    }
    if (collection == null || collection.isEmpty()) {
      missingAttributes.add(ApiAtlasManagedNamespacesView.COLLECTION_FIELD);
    }
    if (customShardKey == null || customShardKey.isEmpty()) {
      missingAttributes.add(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD);
    }

    if (missingAttributes.size() > 0) {
      throw ApiErrorCode.MISSING_ATTRIBUTES.exception(pEnvelope, missingAttributes);
    }

    // Checks that the cluster is not a serverless instance or a cross cloud cluster
    final ClusterDescriptionView updatedClusterView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionView(
            pGroup, pClusterName, pEnvelope, false);

    if (!updatedClusterView
        .getClusterType()
        .equals(ClusterDescription.ClusterType.GEOSHARDED.name())) {
      throw ApiErrorCode.NOT_GLOBAL_WRITES_CLUSTER.exception(
          pEnvelope, pClusterName, pGroup.getId());
    }

    final GeoSharding updatedGeoSharding = updatedClusterView.getGeoShardingView().toGeoSharding();
    updatedGeoSharding.addManagedNamespace(
        db,
        collection,
        customShardKey,
        isShardKeyUnique,
        isCustomShardKeyHashed,
        presplitHashedZones,
        numInitialChunks);
    updatedClusterView.setGeoShardingView(new GeoShardingView(updatedGeoSharding));

    try {
      getNDSUISvc()
          .updateCluster(
              pOrganization,
              pGroup.getId(),
              pClusterName,
              updatedClusterView,
              pAppUser,
              pAuditInfo,
              pRequest,
              ClusterUpdateContext.forClusterApi(false));
    } catch (final SvcException pE) {
      if (NDSErrorCode.INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE.equals(pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE.exception(
            pEnvelope, "managed namespaces", pClusterName);
      }

      if (NDSErrorCode.DUPLICATE_MANAGED_NAMESPACE.equals(pE.getErrorCode())) {
        throw ApiErrorCode.DUPLICATE_MANAGED_NAMESPACE.exception(pEnvelope, db + "." + collection);
      }

      if (NDSErrorCode.MANAGED_NAMESPACE_CANNOT_ALREADY_BE_SHARDED.equals(pE.getErrorCode())) {
        throw ApiErrorCode.MANAGED_NAMESPACE_CANNOT_ALREADY_BE_SHARDED.exception(
            pEnvelope, db + "." + collection);
      }

      if (NDSErrorCode.INVALID_SHARD_KEY_CONFIGURATION.equals(pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_SHARD_KEY_CONFIGURATION.exception(
            pEnvelope, pE.getMessageParams().get(0));
      }

      handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }
    return getGeoSharding(pGroup, pClusterName, pEnvelope);
  }

  @DELETE
  @Path("/{clusterName}/globalWrites/managedNamespaces")
  @Produces(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN})
  @Operation(
      summary = "Remove One Managed Namespace from One Global Cluster",
      operationId = "deleteLegacyManagedNamespace",
      description =
          "Removes one managed namespace within the specified global cluster. A managed namespace"
              + " identifies a collection using the database name, the dot separator, and the"
              + " collection name. Deleting a managed namespace does not remove the associated"
              + " collection or data. To use this resource, the requesting Service Account or API"
              + " Key must have the Project Data Access Admin role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "db",
            in = ParameterIn.QUERY,
            description =
                "Human-readable label that identifies the database that contains the collection."),
        @Parameter(
            name = "collection",
            description =
                "Human-readable label that identifies the collection associated with the managed"
                    + " namespace.",
            in = ParameterIn.QUERY)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ApiAtlasGeoShardingView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"))
  public Response removeManagedNamespace(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("db") final String pDbName,
      @Parameter(hidden = true) @QueryParam("collection") final String pCollectionName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    if (pDbName == null) {
      throw ApiErrorCode.MISSING_QUERY_PARAMETER.exception(pEnvelope, "db");
    }
    if (pCollectionName == null) {
      throw ApiErrorCode.MISSING_QUERY_PARAMETER.exception(pEnvelope, "collection");
    }

    // Checks that the cluster is not a serverless instance or a cross cloud cluster
    final ClusterDescriptionView updatedClusterView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionView(
            pGroup, pClusterName, pEnvelope, false);
    final GeoSharding updatedGeoSharding = updatedClusterView.getGeoShardingView().toGeoSharding();
    updatedGeoSharding.removeManagedNamespace(pDbName, pCollectionName);
    updatedClusterView.setGeoShardingView(new GeoShardingView(updatedGeoSharding));
    updatedClusterView.setNeedsMongoDBConfigPublishAfter();

    try {
      getNDSUISvc()
          .updateCluster(
              pOrganization,
              pGroup.getId(),
              pClusterName,
              updatedClusterView,
              pAppUser,
              pAuditInfo,
              pRequest,
              ClusterUpdateContext.forClusterApi(false));
    } catch (final SvcException | UncheckedSvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleCreateUpdateProcessArgsExceptions(
          pE, pClusterName, pGroup, pEnvelope, _appSettings);
      handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }

    return getGeoSharding(pGroup, pClusterName, pEnvelope);
  }

  @POST
  @Path("/{clusterName}/globalWrites/customZoneMapping")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Add Custom Zone Mappings to One Global Cluster",
      operationId = "addAllCustomZoneMappings",
      description =
          "Add one or more custom zone mappings to the specified global cluster. A custom zone"
              + " mapping matches one ISO 3166-2 location code to a zone in your global cluster. By"
              + " default, MongoDB Cloud maps each location code to the closest geographical zone."
              + " To use this resource, the requesting Service Account or API Key must have the"
              + " Project Owner role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ApiAtlasGeoShardingView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Custom zone mapping to add to the specified global cluster.",
              content =
                  @Content(
                      mediaType = "application/json",
                      schema = @Schema(implementation = ApiAtlasCustomZoneMappingsView.class))),
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"))
  public Response addCustomZoneMappings(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasCustomZoneMappingsView pCustomZoneMappingsView) {
    // Checks that the cluster is not a serverless instance or a cross cloud cluster
    final ClusterDescriptionView updatedClusterView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionView(
            pGroup, pClusterName, pEnvelope, false);
    final GeoSharding originalGeoSharding = updatedClusterView.getGeoShardingView().toGeoSharding();
    final Map<String, ObjectId> updatedCustomZoneMappings =
        originalGeoSharding.getCustomZoneMapping();

    for (final ApiAtlasZoneMappingView mapping : pCustomZoneMappingsView.getCustomZoneMappings()) {
      final String isoCode = mapping.getLocation();
      final String zoneName = mapping.getZone();

      if (!GeoShardingLocationUtil.isValidLocationCode(isoCode)) {
        throw ApiErrorCode.INVALID_LOCATION_CODE.exception(pEnvelope, isoCode);
      }

      final List<ReplicationSpecView> replicationSpecViews =
          updatedClusterView.getReplicationSpecViewsByZone(zoneName);
      if (replicationSpecViews.isEmpty()) {
        throw ApiErrorCode.INVALID_ATTRIBUTE.exception(pEnvelope, zoneName);
      }

      updatedCustomZoneMappings.put(isoCode, replicationSpecViews.get(0).getId());
    }

    final GeoSharding updatedGeoSharding =
        new GeoSharding(
            updatedCustomZoneMappings,
            originalGeoSharding.getManagedNamespaces(),
            originalGeoSharding.isSelfManagedSharding());
    updatedClusterView.setGeoShardingView(new GeoShardingView(updatedGeoSharding));

    try {
      getNDSUISvc()
          .updateCluster(
              pOrganization,
              pGroup.getId(),
              pClusterName,
              updatedClusterView,
              pAppUser,
              pAuditInfo,
              pRequest,
              ClusterUpdateContext.forClusterApi(false));
    } catch (final SvcException pE) {
      if (NDSErrorCode.INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE.equals(pE.getErrorCode())) {
        throw ApiErrorCode.INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE.exception(
            pEnvelope, "zone mappings", pClusterName);
      }

      handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }
    return getGeoSharding(pGroup, pClusterName, pEnvelope);
  }

  @DELETE
  @Path("/{clusterName}/globalWrites/customZoneMapping")
  @Produces(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Remove All Custom Zone Mappings from One Global Cluster",
      operationId = "deleteAllLegacyCustomZoneMappings",
      description =
          "Removes all custom zone mappings for the specified global cluster. A custom zone mapping"
              + " matches one ISO 3166-2 location code to a zone in your global cluster. Removing"
              + " the custom zone mappings restores the default mapping. By default, MongoDB Cloud"
              + " maps each location code to the closest geographical zone. To use this resource,"
              + " the requesting Service Account or API Key must have the Project Owner role.",
      tags = {"Global Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies this cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ApiAtlasGeoShardingView.class))),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"))
  public Response removeCustomZoneMappings(
      @Context final HttpServletRequest pRequest,
      @Context final Organization pOrganization,
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    // Checks that the cluster is not a serverless instance or a cross cloud cluster
    final ClusterDescriptionView updatedClusterView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionView(
            pGroup, pClusterName, pEnvelope, false);
    final GeoSharding originalGeoSharding = updatedClusterView.getGeoShardingView().toGeoSharding();
    final GeoSharding updatedGeoSharding =
        new GeoSharding(
            new HashMap<>(),
            originalGeoSharding.getManagedNamespaces(),
            originalGeoSharding.isSelfManagedSharding());
    updatedClusterView.setGeoShardingView(new GeoShardingView(updatedGeoSharding));

    try {
      getNDSUISvc()
          .updateCluster(
              pOrganization,
              pGroup.getId(),
              pClusterName,
              updatedClusterView,
              pAppUser,
              pAuditInfo,
              pRequest,
              ClusterUpdateContext.forClusterApi(false));
    } catch (final SvcException pE) {
      handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
    }

    return getGeoSharding(pGroup, pClusterName, pEnvelope);
  }

  @GET
  @Path("/{clusterName}/processArgs")
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY})
  @AllowTemporaryApiKeys
  @Operation(
      summary = "Return Advanced Configuration Options for One Cluster",
      operationId = "getClusterAdvancedConfiguration",
      description =
          "Returns the advanced configuration details for one cluster in the specified project."
              + " Clusters contain a group of hosts that maintain the same data set. Advanced"
              + " configuration details include the read/write concern, index and oplog limits, and"
              + " other database settings. This feature isn't available for `M0` free clusters,"
              + " `M2` and `M5` shared-tier clusters, flex clusters, or serverless clusters. To use"
              + " this resource, the requesting Service Account or API Key must have the Project"
              + " Read Only role.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema =
                        @Schema(implementation = ApiAtlasClusterDescriptionProcessArgsView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"))
  public Response getProcessArgs(
      @Context final Group pGroup,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope)
      throws Exception {
    try {
      // If the cluster exists, check that it is not a serverless instance
      final ClusterDescriptionView view =
          getNDSUISvc().getClusterDescription(pGroup.getId(), pClusterName);

      if (view.isServerlessTenantCluster()) {
        if (_appSettings.isServerlessEnabled()) {
          throw ApiErrorCode.CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API.exception(
              pEnvelope, pClusterName);
        } else {
          // Return default process args as if serverless instance does not exist
          return new ApiResponseBuilder(pEnvelope)
              .ok()
              .content(getNDSUISvc().getDefaultProcessArgs())
              .build();
        }
      }

      if (view.isFlexTenantCluster()) {
        if (!_apiAtlasClusterDescriptionUtil.isFlexClusterValid(view, pGroup)) {
          if (_appSettings.isFlexEnabled()) {
            throw ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.exception(
                pEnvelope, pClusterName);
          } else {
            // Return default process args if flex cluster does not exist
            return new ApiResponseBuilder(pEnvelope)
                .ok()
                .content(getNDSUISvc().getDefaultProcessArgs())
                .build();
          }
        }
      }
    } catch (final SvcException pE) {
      // Ignore exception if cluster does not exist, will return default process args
    }

    return _apiAtlasClusterDescriptionUtil.getProcessArgs(
        pGroup, pClusterName, pEnvelope, ApiAtlasClusterDescriptionProcessArgsView.class);
  }

  @PATCH
  @Path("/{clusterName}/processArgs")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @RolesAllowed({NAME.GROUP_CLUSTER_MANAGER})
  @Operation(
      summary = "Update Advanced Configuration Options for One Cluster",
      operationId = "updateClusterAdvancedConfiguration",
      description =
          "Updates the advanced configuration details for one cluster in the specified project."
              + " Clusters contain a group of hosts that maintain the same data set. Advanced"
              + " configuration details include the read/write concern, index and oplog limits, and"
              + " other database settings. To use this resource, the requesting Service Account or"
              + " API Key must have the Project Cluster Manager role. This feature isn't available"
              + " for `M0` free clusters, `M2` and `M5` shared-tier clusters, flex clusters, or"
              + " serverless clusters.",
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema =
                        @Schema(implementation = ApiAtlasClusterDescriptionProcessArgsView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description =
                  "Advanced configuration details to add for one cluster in the specified"
                      + " project.",
              content =
                  @Content(
                      mediaType = "application/json",
                      schema =
                          @Schema(
                              implementation = ApiAtlasClusterDescriptionProcessArgsView.class))),
      externalDocs =
          @ExternalDocumentation(
              description = "Global Clusters",
              url = "https://www.mongodb.com/docs/atlas/global-clusters/"))
  public Response updateProcessArgs(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @Context final Organization pOrganization,
      @Parameter(hidden = true) @PathParam("clusterName") final String pClusterName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiAtlasClusterDescriptionProcessArgsView pProcessArgsView)
      throws Exception {

    try {
      final ClusterDescriptionView cdView =
          getNDSUISvc().getClusterDescription(pGroup.getId(), pClusterName);
      final AtlasResourcePolicyAuthResponse authResult =
          _ndsResourcePolicySvc.isUpdateClusterRequestAuthorized(
              pGroup,
              cdView.getUniqueId(),
              cdView,
              pProcessArgsView.toClusterDescriptionProcessArgsView(),
              pAuditInfo);

      if (authResult.decision().isDeny()) {
        return ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.response(pEnvelope);
      }
    } catch (final SvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleGeneralExceptions(pE, pClusterName, pGroup, pEnvelope);
      throw ApiErrorCode.ATLAS_GENERAL_ERROR.exception(pEnvelope, pE.getMessageParams());
    }

    try {
      // updateProcessArgs does not allow updates for free, flex, or serverless clusters
      getNDSUISvc()
          .updateProcessArgs(
              pGroup,
              pClusterName,
              pProcessArgsView.toClusterDescriptionProcessArgsView(),
              pAuditInfo,
              null,
              pAppUser);
    } catch (final SvcException | UncheckedSvcException pE) {
      _apiAtlasClusterDescriptionUtil.handleCreateUpdateProcessArgsExceptions(
          pE, pClusterName, pGroup, pEnvelope, _appSettings);
      LOG.warn(pE.getErrorCode().getMessage() + ": " + pE.getMessage());
      throw ApiErrorCode.UNEXPECTED_ERROR.exception(pEnvelope);
    }
    return _apiAtlasClusterDescriptionUtil.getProcessArgs(
        pGroup, pClusterName, pEnvelope, pProcessArgsView.getClass());
  }

  @DELETE
  @Path("/{clusterName}")
  @Produces(MediaType.APPLICATION_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_ATLAS_ADMIN})
  @Operation(
      summary = "Remove One Cluster",
      operationId = "deleteLegacyCluster",
      description =
          "Removes one cluster in the specific project. Clusters contain a group of hosts that"
              + " maintain the same data set. The cluster must have termination protection disabled"
              + " in order to be deleted. To use this resource, the requesting Service Account or"
              + " API Key must have the Project Owner role.\n\n"
              + "This endpoint can also be used on Flex clusters that were created using the"
              + " [createCluster](https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Clusters/operation/createCluster)"
              + " endpoint or former M2/M5 clusters that have been migrated to Flex clusters until"
              + " January 2026. Please use the deleteFlexCluster endpoint for Flex clusters"
              + " instead.",
      externalDocs =
          @ExternalDocumentation(
              description = "deleteFlexCluster",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Flex-Clusters/operation/deleteFlexCluster"),
      tags = {"Clusters"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "retainBackups",
            description =
                "Flag that indicates whether to retain backup snapshots for the deleted"
                    + " dedicated cluster.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "boolean"))
      },
      responses = {
        @ApiResponse(responseCode = "202", description = ResponseDescriptions.ACCEPTED),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  public Response deleteCluster(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("clusterName") final String pName,
      @Parameter(hidden = true) @QueryParam("retainBackups") final ApiBooleanParam pRetainBackups,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {

    // Check whether the cluster exists and check that it is not a serverless instance
    final ClusterDescriptionView clusterDescriptionView =
        _apiAtlasClusterDescriptionUtil.getClusterDescriptionView(pGroup, pName, pEnvelope, true);

    try {
      _flexMigrationSvc.verifyFlexMigrationOrRollbackNotInProgress(
          pName, pGroup.getId(), "deleteCluster", true);
      _apiAtlasClusterDescriptionUtil.validateDeleteClusterRetainBackups(
          clusterDescriptionView, pRetainBackups, pEnvelope);

      getNDSUISvc()
          .requestDeleteCluster(
              pGroup,
              pName,
              pAuditInfo,
              pRequest,
              false,
              DeleteClusterReason.USER_INITIATED_DELETE,
              pRetainBackups.getValue());
    } catch (final SvcException pE) {
      if (NDSErrorCode.CLUSTER_NOT_FOUND.equals(pE.getErrorCode())) {
        throw ApiErrorCode.CLUSTER_NOT_FOUND.exception(pEnvelope, pName, pGroup.getId());
      }

      if (NDSErrorCode.ALREADY_REQUESTED_DELETION.equals(pE.getErrorCode())) {
        throw ApiErrorCode.CLUSTER_ALREADY_REQUESTED_DELETION.exception(pEnvelope, pName);
      }

      if (NDSErrorCode.CANNOT_TERMINATE_CLUSTER_WITH_CONFIGURED_DATA_LAKE_PIPELINES.equals(
          pE.getErrorCode())) {
        throw ApiErrorCode.CANNOT_TERMINATE_CLUSTER_WITH_CONFIGURED_DATA_LAKE_PIPELINES.exception(
            pEnvelope, pName);
      }

      if (NDSErrorCode.CANNOT_MODIFY_CLUSTER_WITH_RUNNING_LIVE_IMPORT.equals(pE.getErrorCode())) {
        throw ApiErrorCode.CANNOT_MODIFY_CLUSTER_WITH_RUNNING_LIVE_IMPORT.exception(
            pEnvelope, pE.getMessage());
      }

      if (NDSErrorCode.CANNOT_TERMINATE_CLUSTER_WHEN_BACKUP_LOCK_MVP_ENABLED.equals(
          pE.getErrorCode())) {
        throw ApiErrorCode.CANNOT_TERMINATE_CLUSTER_WHEN_BACKUP_LOCK_MVP_ENABLED.exception(
            pEnvelope, pE.getMessage());
      }

      if (NDSErrorCode.CANNOT_TERMINATE_CLUSTER_WITH_UNDERGOING_REGIONAL_OUTAGE_SIMULATION.equals(
          pE.getErrorCode())) {
        throw ApiErrorCode.CANNOT_TERMINATE_CLUSTER_WITH_UNDERGOING_REGIONAL_OUTAGE_SIMULATION
            .exception(pEnvelope, pName, pGroup.getName());
      }

      if (NDSErrorCode.CANNOT_TERMINATE_CLUSTER_WHEN_TERMINATION_PROTECTION_ENABLED.equals(
          pE.getErrorCode())) {
        throw ApiErrorCode.CANNOT_TERMINATE_CLUSTER_WHEN_TERMINATION_PROTECTION_ENABLED.exception(
            pEnvelope, pE.getMessage());
      }

      if (NDSErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.equals(pE.getErrorCode())) {
        throw ApiErrorCode.FLEX_MIGRATION_TENANT_IN_PROGRESS.exception(pEnvelope, pName);
      }

      LOG.warn(pE.getErrorCode().getMessage() + ": " + pE.getMessage());
      throw ApiErrorCode.UNEXPECTED_ERROR.exception(pEnvelope);
    }

    setAdminApiTelemetryAttributes(
        pRequest, clusterDescriptionView.getName(), clusterDescriptionView.getUniqueId());
    return new ApiResponseBuilder(pEnvelope).accepted().build();
  }

  @GET
  @Path("/{hostName}/logs/{logName}.gz")
  @Produces("application/gzip")
  @RolesAllowed({
    RoleSet.NAME.GROUP_ATLAS_ADMIN,
    RoleSet.NAME.GROUP_DATA_ACCESS_ANY,
    // If you update these, be sure to update the matching calls in the private api
  })
  @RequireCustomerGrantForEmployeeAccess()
  @Operation(
      summary = "Download Logs for One Cluster Host in One Project",
      operationId = "downloadHostLogs",
      description =
          "Returns a compressed (.gz) log file that contains a range of log messages for the"
              + " specified host for the specified project. MongoDB updates process and audit logs"
              + " from the cluster backend infrastructure every five minutes. Logs are stored in"
              + " chunks approximately five minutes in length, but this duration may vary. If you"
              + " poll the API for log files, we recommend polling every five minutes even though"
              + " consecutive polls could contain some overlapping logs. This feature isn't"
              + " available for `M0` free clusters, `M2`, `M5`, flex, or serverless clusters. To"
              + " use this resource, the requesting Service Account or API Key must have the"
              + " Project Data Access Read Only or higher role. The API does not support direct"
              + " calls with the json response schema. You must request a gzip response schema"
              + " using an accept header of the format: \"Accept:"
              + " application/vnd.atlas.YYYY-MM-DD+gzip\".",
      tags = {"Monitoring and Logs"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "endDate",
            description =
                "Specifies the date and time for the ending point of the range of log messages to"
                    + " retrieve, in the number of seconds that have elapsed since the UNIX epoch."
                    + " This value will default to 24 hours after the start date. If the start date"
                    + " is also unspecified, the value will default to the time of the request.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "integer", minimum = OpenApiConst.EPOCH_TIME_SECONDS_MIN)),
        @Parameter(
            name = "hostName",
            description =
                "Human-readable label that identifies the host that stores the log files that you"
                    + " want to download.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.HOST_REGEX),
            required = true),
        @Parameter(
            name = "logName",
            description =
                "Human-readable label that identifies the log file that you want to return. To"
                    + " return audit logs, enable *Database Auditing* for the specified project.",
            in = ParameterIn.PATH,
            schema =
                @Schema(
                    type = "string",
                    allowableValues = {
                      "mongodb",
                      "mongos",
                      "mongodb-audit-log",
                      "mongos-audit-log"
                    },
                    externalDocs =
                        @ExternalDocumentation(
                            description = "Set up Database Auditing",
                            url = "https://docs.atlas.mongodb.com/database-auditing/")),
            required = true),
        @Parameter(
            name = "startDate",
            description =
                "Specifies the date and time for the starting point of the range of log messages to"
                    + " retrieve, in the number of seconds that have elapsed since the UNIX epoch."
                    + " This value will default to 24 hours prior to the end date. If the end date"
                    + " is also unspecified, the value will default to 24 hours prior to the time"
                    + " of the request.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "integer", minimum = OpenApiConst.EPOCH_TIME_SECONDS_MIN))
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/gzip",
                    schema =
                        @Schema(
                            type = "string",
                            description =
                                "Compressed (.gz) log file that contains a range of log messages"
                                    + " for the specified host for the specified project",
                            format = "binary"))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  public Response logsForHost(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @PathParam("hostName") final String pHostname,
      @Parameter(hidden = true) @PathParam("logName") final String pLogName,
      @Parameter(hidden = true) @QueryParam("startDate") @Nullable final Long pStartTimestamp,
      @Parameter(hidden = true) @QueryParam("endDate") @Nullable final Long pEndTimestamp,
      @Parameter(hidden = true) @QueryParam("envelope") @Nullable final Boolean pEnvelope)
      throws SvcException {

    // Customers can only access logs permitted for the group admin to download
    if (pLogName == null
        || NDSAWSLogDownloadSvcUtils.LOGS_PERMITTED_FOR_DOWNLOAD_BY_GROUP_ADMIN.stream()
            .map(NDSAWSLogDownloadSvc.SupportedLogs::getType)
            .noneMatch(pLogName::equals)) {
      throw ApiErrorCode.INVALID_PATH_PARAMETER.exception(pEnvelope, "logName");
    }
    if (pEndTimestamp != null && pStartTimestamp != null && pEndTimestamp < pStartTimestamp) {
      throw ApiErrorCode.START_DATE_AFTER_END_DATE.exception(pEnvelope);
    }

    return _logFetcherSvc.fetchLogsForHost(
        pUser,
        pGroup,
        pAuditInfo,
        pHostname,
        pLogName,
        pStartTimestamp,
        pEndTimestamp,
        CallType.API_CALL,
        pEnvelope);
  }

  private void setAdminApiTelemetryAttributes(
      final HttpServletRequest pRequest, final String pName, final ObjectId pClusterId) {
    if (FeatureFlagSvc.isFeatureFlagEnabled(
        FeatureFlag.ENABLE_API_TELEMETRY_CUSTOM_FIELDS, _appSettings, null, null)) {
      _apiTelemetryAttributeSvc.putStandardAttribute(pRequest, CLUSTER_NAME, pName);
      if (pClusterId != null) {
        _apiTelemetryAttributeSvc.putCustomAttribute(
            pRequest, CustomTelemetryFieldKey.CLUSTER_ID, pClusterId.toString());
      }
    }
  }
}
