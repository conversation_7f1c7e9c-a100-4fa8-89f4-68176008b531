package com.xgen.svc.mms.api.view.atlas;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;

import com.amazonaws.services.ec2.model.VolumeType;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.xgen.cloud.authz.resource._public.view.api.ApiAtlasTagView;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.util._public.json.JsonOptional;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSAutoScaling;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.autoscaling.AzureAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.EffectiveValues;
import com.xgen.cloud.nds.common._public.model.ConfigServerType;
import com.xgen.cloud.nds.common._public.model.ReplicaSetScalingStrategy;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._public.model.FlexHardwareSpec;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex._public.model.FlexTenantProviderOptions;
import com.xgen.cloud.nds.free._public.model.FreeHardwareSpec;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.free._public.model.autoscaling.FreeAutoScaling;
import com.xgen.cloud.nds.gcp._public.model.GCPHardwareSpec;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.autoscaling.GCPAutoScaling;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.DiskWarmingMode;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.EncryptionAtRestProvider;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.InternalClusterRole;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.RootCertType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.ConfigServerManagementMode;
import com.xgen.cloud.nds.project._public.view.EmployeeAccessGrantView;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.view.ApiAtlasClusterDescriptionStateView;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasAWSAutoScalingView;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasAutoScalingView;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasAzureAutoScalingView;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasComputeAutoScalingView;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasFreeAutoScalingView;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasGCPAutoScalingView;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasProviderAutoScalingView;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasProviderComputeAutoScalingView;
import com.xgen.svc.nds.model.ui.BiConnectorView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionView.ClusterDescriptionViewBuilder;
import com.xgen.svc.nds.model.ui.EncryptionAtRestProviderView;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.svc.nds.model.ui.ReplicationSpecView;
import com.xgen.svc.nds.model.ui.VolumeTypeView;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.AccessMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;

@Schema(
    name = "LegacyAtlasCluster",
    title = "Cluster Description",
    description = "Group of settings that configure a MongoDB cluster.")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiAtlasLegacyClusterDescriptionView extends ApiAtlasBaseClusterDescriptionView {

  public static class Fields extends ApiAtlasBaseClusterDescriptionView.Fields {
    public static final String MONGODB_MAJOR_VERSION_FIELD = "mongoDBMajorVersion";
    public static final String REPLICATION_SPECS_FIELD = "replicationSpecs";
    public static final String DISK_SIZE_GB_FIELD = "diskSizeGB";
    public static final String DISK_IOPS_FIELD = "diskIOPS";
    public static final String DISK_THROUGHPUT_FIELD = "diskThroughput";
    public static final String MONGO_URI_FIELD = "mongoURI";
    public static final String MONGO_URI_WITH_OPTIONS_FIELD = "mongoURIWithOptions";
    public static final String MONGO_URI_LAST_UPDATED_FIELD = "mongoURIUpdated";
    public static final String SRV_ADDRESS_FIELD = "srvAddress";
    public static final String BACKUP_ENABLED_FIELD = "backupEnabled";
    public static final String PROVIDER_BACKUP_ENABLED_FIELD = "providerBackupEnabled";
    public static final String PIT_ENABLED_FIELD = "pitEnabled";
    public static final String AUTO_SCALING_FIELD = "autoScaling";
    public static final String BI_CONNECTOR_FIELD = "biConnector";
    public static final String PAUSED_FIELD = "paused";
    public static final String CLUSTER_TYPE_FIELD = "clusterType";
    public static final String ENCRYPTION_AT_REST_PROVIDER_FIELD = "encryptionAtRestProvider";
    public static final String LABELS_FIELD = "labels";
    public static final String ROOT_CERT_TYPE = "rootCertType";

    // For backward compatibility
    public static final String REPLICATION_SPEC_FIELD = "replicationSpec";
    public static final String NUM_SHARDS_FIELD = "numShards";
    public static final String REPLICATION_FACTOR_FIELD = "replicationFactor";
    public static final String VERSION_RELEASE_SYSTEM = "versionReleaseSystem";
    public static final String PROVIDER_SETTINGS_FIELD = "providerSettings";
    public static final String ACCEPT_DATA_RISKS_AND_FORCE_RECONFIG =
        "acceptDataRisksAndForceReplicaSetReconfig";
    public static final String DISK_WARMING_MODE = "diskWarmingMode";
    public static final String CONFIG_SERVER_TYPE = "configServerType";
    public static final String CONFIG_SERVER_MANAGEMENT_MODE = "configServerManagementMode";
    public static final String GLOBAL_CLUSTER_SELF_MANAGED_SHARDING =
        "globalClusterSelfManagedSharding";
    public static final String MONGODB_EMPLOYEE_ACCESS_GRANT = "mongoDBEmployeeAccessGrant";
    public static final String REPLICA_SET_SCALING_STRATEGY = "replicaSetScalingStrategy";
    public static final String FEATURE_COMPATIBILITY_VERSION = "featureCompatibilityVersion";
    public static final String FEATURE_COMPATIBILITY_VERSION_EXPIRATION_DATE =
        "featureCompatibilityVersionExpirationDate";
    public static final String ADVANCED_CONFIGURATION = "advancedConfiguration";
  }

  // ---------------------------------------------------------------------------
  // Note: It is preferred to use strict Jackson field access levels, but we do
  // not here to maintain consistency with our existing cluster description API.
  // If mirroring a new View off this file, please include access levels in your
  // @JsonProperty annotations.
  // ---------------------------------------------------------------------------

  //  @JsonProperty(value = Fields.MONGODB_MAJOR_VERSION_FIELD, access = Access.READ_WRITE)
  public static final String TEST_MONGODB_VERSION = "5.0.25";
  public static final String TEST_MONGODB_MAJOR_VERSION = "5.0";

  @JsonProperty(Fields.MONGODB_MAJOR_VERSION_FIELD)
  @Schema(
      description =
          "MongoDB major version of the cluster.\n\n"
              + "On creation: Choose from the available versions of MongoDB, or leave unspecified"
              + " for the current recommended default in the MongoDB Cloud platform. The"
              + " recommended version is a recent Long Term Support version. The default is not"
              + " guaranteed to be the most recently released version throughout the entire release"
              + " cycle. For versions available in a specific project, see the linked documentation"
              + " or use the API endpoint for [project LTS versions"
              + " endpoint](#tag/Projects/operation/getProjectLTSVersions).\n\n"
              + " On update: Increase version only by 1 major version at a time. If the cluster is"
              + " pinned to a MongoDB feature compatibility version exactly one major version below"
              + " the current MongoDB version, the MongoDB version can be downgraded to the"
              + " previous major version.",
      accessMode = AccessMode.READ_WRITE,
      example = TEST_MONGODB_MAJOR_VERSION,
      externalDocs =
          @ExternalDocumentation(
              description = "Available MongoDB Versions in Atlas",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/faq/database/#which-versions-of-mongodb-do-service-clusters-use-"),
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty( // TODO CLOUDP-309177
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Schema predates IPA validation."),
              @ExtensionProperty(
                  name = "xgen-IPA-117-description-should-not-use-inline-links",
                  value = "Description has multiple links, and externalDocs only supports one."),
            })
      })
  private String _mongoDBMajorVersion;

  @JsonProperty(Fields.FEATURE_COMPATIBILITY_VERSION)
  @Schema(
      description = "Feature compatibility version of the cluster.",
      accessMode = AccessMode.READ_ONLY)
  private String _featureCompatibilityVersion;

  @JsonProperty(Fields.FEATURE_COMPATIBILITY_VERSION_EXPIRATION_DATE)
  @Schema(
      description =
          "Feature compatibility version expiration date. This parameter expresses its value in the"
              + " ISO 8601 timestamp format in UTC.",
      accessMode = AccessMode.READ_ONLY)
  private Date _featureCompatibilityVersionExpirationDate;

  private boolean _isFixedFCV;

  //  @JsonProperty(value = Fields.REPLICATION_SPECS_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.REPLICATION_SPECS_FIELD)
  @ArraySchema(
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-124-array-max-items",
                  value = "Schema predates IPA validation."),
            })
      },
      arraySchema =
          @Schema(
              description =
                  "List of settings that configure your cluster regions.\n\n"
                      + "- For Global Clusters, each object in the array represents one zone where"
                      + " MongoDB Cloud deploys your clusters nodes.\n"
                      + "- For non-Global sharded clusters and replica sets, the single"
                      + " object represents where MongoDB Cloud deploys your clusters nodes.",
              accessMode = Schema.AccessMode.READ_WRITE))
  private List<ApiAtlasLegacyReplicationSpecView> _replicationSpecs;

  //  @JsonProperty(value = Fields.DISK_SIZE_GB_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.DISK_SIZE_GB_FIELD)
  @Schema(
      description =
          "Storage capacity of instance data volumes expressed in gigabytes. Increase this number"
              + " to add capacity.\n\n"
              + " This value is not configurable on M0/M2/M5 clusters.\n\n"
              + " MongoDB Cloud requires this parameter if you set **replicationSpecs**.\n\n"
              + " If you specify a disk size below the minimum (10 GB), this parameter defaults to"
              + " the minimum disk size value. \n\n"
              + " Storage charge calculations depend on whether you choose the default value or a"
              + " custom value.\n\n"
              + " The maximum value for disk storage cannot exceed 50 times the maximum RAM for the"
              + " selected cluster. If you require more storage space, consider upgrading your"
              + " cluster to a higher tier.",
      minimum = "10",
      maximum = "4096",
      accessMode = Schema.AccessMode.READ_WRITE,
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Schema predates IPA validation.")
            })
      })
  private Double _diskSizeGB;

  @Hidden
  @JsonProperty(value = Fields.DISK_IOPS_FIELD, access = Access.WRITE_ONLY)
  private Integer _diskIOPS;

  @Hidden
  @JsonProperty(value = Fields.DISK_THROUGHPUT_FIELD, access = Access.WRITE_ONLY)
  private Integer _diskThroughput;

  //  @JsonProperty(value = Fields.MONGO_URI_FIELD, access = Access.READ_ONLY)
  @JsonProperty(Fields.MONGO_URI_FIELD)
  @Schema(
      description =
          "Base connection string that you can use to connect to the cluster. MongoDB Cloud"
              + " displays the string only after the cluster starts, not while it builds the"
              + " cluster.",
      accessMode = Schema.AccessMode.READ_ONLY,
      externalDocs =
          @ExternalDocumentation(
              description = "Connection string URI format.",
              url = "https://docs.mongodb.com/manual/reference/connection-string/"),
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Schema predates IPA validation.")
            })
      })
  private String _mongoURI;

  //  @JsonProperty(value = Fields.MONGO_URI_WITH_OPTIONS_FIELD, access = Access.READ_ONLY)
  @JsonProperty(Fields.MONGO_URI_WITH_OPTIONS_FIELD)
  @Schema(
      description =
          "Connection string that you can use to connect to the cluster including the"
              + " `replicaSet`, `ssl`, and `authSource` query parameters with values appropriate"
              + " for the cluster. You may need to add MongoDB database users. The response"
              + " returns this parameter once the cluster can receive requests, not while it"
              + " builds the cluster.",
      accessMode = Schema.AccessMode.READ_ONLY,
      externalDocs =
          @ExternalDocumentation(
              description = "Connection string URI format.",
              url = "https://docs.mongodb.com/manual/reference/connection-string/"),
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Schema predates IPA validation.")
            })
      })
  private String _mongoURIWithOptions;

  //  @JsonProperty(value = Fields.MONGO_URI_LAST_UPDATED_FIELD, access = Access.READ_ONLY)
  @JsonProperty(Fields.MONGO_URI_LAST_UPDATED_FIELD)
  @Schema(
      description =
          "Date and time when someone last updated the connection string. MongoDB Cloud "
              + "represents this timestamp in ISO 8601 format in UTC.",
      accessMode = Schema.AccessMode.READ_ONLY,
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Schema predates IPA validation.")
            })
      })
  private Date _mongoURIUpdated;

  //  @JsonProperty(value = Fields.SRV_ADDRESS_FIELD, access = Access.READ_ONLY)
  @JsonProperty(Fields.SRV_ADDRESS_FIELD)
  @Schema(
      name = Fields.SRV_ADDRESS_FIELD,
      description =
          "Connection string that you can use to connect to the cluster. The `+srv` modifier "
              + "forces the connection to use Transport Layer Security (TLS). The `mongoURI` "
              + "parameter lists additional options.",
      accessMode = Schema.AccessMode.READ_ONLY,
      externalDocs =
          @ExternalDocumentation(
              description = "Connection string URI format.",
              url = "https://docs.mongodb.com/manual/reference/connection-string/"))
  private String _srvAddress;

  //  @JsonProperty(value = Fields.BACKUP_ENABLED_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.BACKUP_ENABLED_FIELD)
  @Schema(
      description =
          "Flag that indicates whether the cluster can perform backups. If set to "
              + "`true`, the cluster can perform backups. You must set this value to "
              + "`true` for NVMe clusters. Backup uses Cloud Backups for dedicated clusters "
              + "and Shared Cluster Backups for tenant clusters. If set to `false`, the "
              + "cluster doesn't use MongoDB Cloud backups.",
      accessMode = Schema.AccessMode.READ_WRITE)
  private Boolean _backupEnabled;

  //  @JsonProperty(value = Fields.PROVIDER_BACKUP_ENABLED_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.PROVIDER_BACKUP_ENABLED_FIELD)
  @Schema(
      description =
          "Flag that indicates whether the M10 or higher cluster can perform Cloud Backups. If set"
              + " to `true`, the cluster can perform backups. If this and **backupEnabled** are set"
              + " to `false`, the cluster doesn't use MongoDB Cloud backups.",
      accessMode = Schema.AccessMode.READ_WRITE)
  private Boolean _providerBackupEnabled;

  //  @JsonProperty(value = Fields.PIT_ENABLED_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.PIT_ENABLED_FIELD)
  @Schema(
      description = "Flag that indicates whether the cluster uses continuous cloud backups.",
      accessMode = Schema.AccessMode.READ_WRITE,
      externalDocs =
          @ExternalDocumentation(
              description = "Continuous Cloud Backups",
              url = "https://docs.atlas.mongodb.com/backup/cloud-backup/overview/"))
  private Boolean _pitEnabled;

  //  @JsonProperty(value = Fields.AUTO_SCALING_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.AUTO_SCALING_FIELD)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description =
          "Group of settings that configures auto-scaling for the cluster. If you specify "
              + "this object, you must specify the **providerSettings.autoScaling** object also.",
      accessMode = Schema.AccessMode.READ_WRITE,
      externalDocs =
          @ExternalDocumentation(
              description = "Cluster Auto-Scaling",
              url = "https://docs.atlas.mongodb.com/cluster-autoscaling/"))
  private ApiAtlasAutoScalingView _autoScaling;

  //  @JsonProperty(value = Fields.BI_CONNECTOR_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.BI_CONNECTOR_FIELD)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description =
          "Group of settings that configures the MongoDB Connector for Business Intelligence "
              + "for the cluster.",
      accessMode = Schema.AccessMode.READ_WRITE,
      externalDocs =
          @ExternalDocumentation(
              description = "MongoDB Connector for Business Intelligence",
              url = "https://docs.mongodb.com/bi-connector/current/"))
  private ApiAtlasBiConnectorView _biConnector;

  //  @JsonProperty(value = Fields.PAUSED_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.PAUSED_FIELD)
  @Schema(
      description = "Flag that indicates whether the cluster is paused.",
      accessMode = Schema.AccessMode.READ_WRITE)
  private Boolean _paused;

  //  @JsonProperty(value = Fields.CLUSTER_TYPE_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.CLUSTER_TYPE_FIELD)
  @Schema(
      description = "Configuration of nodes that comprise the cluster.",
      accessMode = Schema.AccessMode.READ_WRITE,
      allowableValues = {"REPLICASET", "SHARDED", "GEOSHARDED"})
  private String _clusterType;

  //  @JsonProperty(value = Fields.ENCRYPTION_AT_REST_PROVIDER_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.ENCRYPTION_AT_REST_PROVIDER_FIELD)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description =
          "Cloud service provider that manages your customer keys to provide an additional layer"
              + " of Encryption at Rest for the cluster.",
      accessMode = Schema.AccessMode.READ_WRITE,
      externalDocs =
          @ExternalDocumentation(
              description = "Encryption at Rest using Customer Key Management",
              url = "https://www.mongodb.com/docs/atlas/security-kms-encryption/"))
  private ApiAtlasEncryptionAtRestProviderView _encryptionAtRestProvider;

  private ApiAtlasGeoShardingView _geoSharding;

  // For backward compatibility. These annotations handle deserialization.
  // Serialization are handled by getter methods annotation below
  @JsonProperty(Fields.REPLICATION_FACTOR_FIELD)
  @Schema(
      description =
          "Number of members that belong to the replica set. Each member retains a copy "
              + "of your databases, providing high availability and data redundancy. Use "
              + "**replicationSpecs** instead.",
      accessMode = Schema.AccessMode.READ_WRITE,
      deprecated = true,
      defaultValue = "3",
      allowableValues = {"3", "5", "7"})
  private Integer _replicationFactor;

  //  @JsonProperty(value = Fields.REPLICATION_SPEC_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.REPLICATION_SPEC_FIELD)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description =
          "Group of settings that you configure for each region in a multi-region cluster. "
              + "Each element in this object represents a region where your cluster deploys. "
              + "Use **replicationSpecs** instead.")
  private ApiAtlasLegacyRegionsConfigView _replicationSpec;

  //  @JsonProperty(value = Fields.NUM_SHARDS_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.NUM_SHARDS_FIELD)
  @Schema(
      description =
          "Number of shards up to 50 to deploy for a sharded cluster. The resource returns `1` "
              + "to indicate a replica set and values of `2` and higher to indicate a sharded "
              + "cluster. The returned value equals the number of shards in the cluster.",
      accessMode = Schema.AccessMode.READ_WRITE,
      defaultValue = "1",
      minimum = "1",
      maximum = "50",
      externalDocs =
          @ExternalDocumentation(
              description = "Sharding",
              url = "https://docs.mongodb.com/manual/sharding/"))
  private Integer _numShards;

  //  @JsonProperty(value = Fields.LABELS_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.LABELS_FIELD)
  @ArraySchema(
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-124-array-max-items",
                  value = "Schema predates IPA validation."),
            })
      },
      arraySchema =
          @Schema(
              description =
                  "Collection of key-value pairs between 1 to 255 characters in length that tag and"
                      + " categorize the cluster. The MongoDB Cloud console doesn't display your"
                      + " labels.\n\n"
                      + "Cluster labels are deprecated and will be removed in a future release. We"
                      + " strongly recommend that you use Resource Tags instead.",
              externalDocs =
                  @ExternalDocumentation(
                      description = "Resource Tags",
                      url = "https://dochub.mongodb.org/core/add-cluster-tag-atlas"),
              accessMode = Schema.AccessMode.READ_WRITE,
              deprecated = true))
  private List<ApiAtlasNDSLabelView> _labels;

  //  @JsonProperty(value = Fields.ROOT_CERT_TYPE, access = Access.READ_WRITE)
  @JsonProperty(Fields.ROOT_CERT_TYPE)
  @Schema(accessMode = Schema.AccessMode.READ_WRITE, defaultValue = "ISRGROOTX1")
  private RootCertType _rootCertType;

  //  @JsonProperty(value = Fields.VERSION_RELEASE_SYSTEM, access = Access.READ_WRITE)
  @JsonProperty(Fields.VERSION_RELEASE_SYSTEM)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description =
          "Method by which the cluster maintains the MongoDB versions. If value is `CONTINUOUS`, "
              + "you must not specify **mongoDBMajorVersion**.",
      accessMode = Schema.AccessMode.READ_WRITE,
      defaultValue = "LTS")
  private VersionReleaseSystem _versionReleaseSystem;

  //  @JsonProperty(value = Fields.PROVIDER_SETTINGS_FIELD, access = Access.READ_WRITE)
  @JsonProperty(Fields.PROVIDER_SETTINGS_FIELD)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description =
          "Group of cloud service provider settings that configure"
              + "the provisioned MongoDB hosts.",
      accessMode = Schema.AccessMode.READ_WRITE)
  private ApiAtlasClusterProviderSettingsView _providerSettings;

  @JsonProperty(Fields.ACCEPT_DATA_RISKS_AND_FORCE_RECONFIG)
  @Schema(
      description =
          "If reconfiguration is necessary to regain a primary due to a regional outage, submit"
              + " this field alongside your topology reconfiguration to request a new regional"
              + " outage resistant topology. Forced reconfigurations during an outage of the"
              + " majority of electable nodes carry a risk of data loss if replicated writes (even"
              + " majority committed writes) have not been replicated to the new primary node."
              + " MongoDB Atlas docs contain more information. To proceed with an operation which"
              + " carries that risk, set **acceptDataRisksAndForceReplicaSetReconfig** to the"
              + " current date. This parameter expresses its value in the ISO 8601 timestamp format"
              + " in UTC.",
      externalDocs =
          @ExternalDocumentation(
              description = "Reconfiguring a Replica Set during a regional outage",
              url = "https://dochub.mongodb.org/core/regional-outage-reconfigure-replica-set"),
      accessMode = Schema.AccessMode.READ_WRITE)
  private Date _acceptDataRisksAndForceReplicaSetReconfig;

  @JsonProperty(Fields.DISK_WARMING_MODE)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description = "Disk warming mode selection.",
      accessMode = Schema.AccessMode.READ_WRITE,
      externalDocs =
          @ExternalDocumentation(
              description = "Reduce Secondary Disk Warming Impact",
              url =
                  "https://docs.atlas.mongodb.com/reference/replica-set-tags/#reduce-secondary-disk-warming-impact"), // TODO: update this when flipping FF
      defaultValue = "FULLY_WARMED")
  private DiskWarmingMode _diskWarmingMode;

  @JsonProperty(Fields.CONFIG_SERVER_TYPE)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description = "Describes a sharded cluster's config server type.",
      accessMode = AccessMode.READ_ONLY,
      externalDocs =
          @ExternalDocumentation(
              description = "MongoDB Sharded Cluster Config Servers",
              url = "https://dochub.mongodb.org/docs/manual/core/sharded-cluster-config-servers"))
  private ConfigServerType _configServerType;

  @JsonProperty(Fields.CONFIG_SERVER_MANAGEMENT_MODE)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description =
          """
Config Server Management Mode for creating or updating a sharded cluster.

When configured as ATLAS_MANAGED, atlas may automatically switch the cluster's config server type for optimal performance and savings.

When configured as FIXED_TO_DEDICATED, the cluster will always use a dedicated config server.""",
      accessMode = AccessMode.READ_WRITE,
      externalDocs =
          @ExternalDocumentation(
              description = "MongoDB Sharded Cluster Config Servers",
              url = "https://dochub.mongodb.org/docs/manual/core/sharded-cluster-config-servers"),
      defaultValue = "ATLAS_MANAGED")
  private ConfigServerManagementMode _configServerManagementMode;

  @JsonProperty(Fields.GLOBAL_CLUSTER_SELF_MANAGED_SHARDING)
  @Schema(
      description =
          """
Set this field to configure the Sharding Management Mode when creating a new Global Cluster.

When set to false, the management mode is set to Atlas-Managed Sharding. This mode fully manages the sharding of your Global Cluster and is built to provide a seamless deployment experience.

When set to true, the management mode is set to Self-Managed Sharding. This mode leaves the management of shards in your hands and is built to provide an advanced and flexible deployment experience.

This setting cannot be changed once the cluster is deployed.""",
      accessMode = AccessMode.READ_WRITE,
      externalDocs =
          @ExternalDocumentation(
              description = "Creating a Global Cluster",
              url = "https://dochub.mongodb.org/core/global-cluster-management"))
  private Boolean _selfManagedSharding;

  private Date _createDate;

  @JsonProperty(Fields.MONGODB_EMPLOYEE_ACCESS_GRANT)
  @Schema(accessMode = AccessMode.READ_ONLY)
  private ApiAtlasEmployeeAccessGrantView _employeeAccessGrant;

  @JsonProperty(Fields.REPLICA_SET_SCALING_STRATEGY)
  @Schema(accessMode = AccessMode.READ_WRITE)
  private ReplicaSetScalingStrategy _replicaSetScalingStrategy;

  @JsonProperty(Fields.ADVANCED_CONFIGURATION)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description =
          "Group of settings that configures a subset of the advanced configuration details.",
      accessMode = Schema.AccessMode.READ_WRITE)
  private ApiAtlasClusterAdvancedConfigurationView _advancedConfiguration;

  /*
   Custom serializers for specific fields for various versions of clusters
   1. replicationSpecs for GEOSHARDED clusters
   2. replicationSpec for non-GEOSHARDED clusters
   3. numshards for non-GEOSHARDED clusters
   4. replicationFactor for non-GEOSHARDED and single region clusters
  */
  @JsonGetter(Fields.REPLICATION_SPECS_FIELD)
  public List<ApiAtlasLegacyReplicationSpecView> serializeReplicationSpecs() {
    // For now, only show the new replication specs schema for GEOSHARDED clusters
    if (isGeoSharded()) {
      return _replicationSpecs;
    }
    if (hasReplicationSpecs()) {
      return _replicationSpecs;
    }
    return null;
  }

  @JsonGetter(Fields.REPLICATION_SPEC_FIELD)
  public ApiAtlasLegacyRegionsConfigView serializeReplicationSpec() {
    if (hasReplicationSpecs() && !isGeoSharded()) {
      return _replicationSpecs.get(0).getRegionsConfig();
    }
    if (hasReplicationSpec()) {
      return _replicationSpec;
    }
    return null;
  }

  @JsonGetter(Fields.NUM_SHARDS_FIELD)
  public Integer serializeNumShards() {
    if (hasReplicationSpecs() && !isGeoSharded()) {
      return _replicationSpecs.get(0).getNumShards();
    }
    if (hasNumShards()) {
      return _numShards;
    }
    return null;
  }

  @JsonGetter(Fields.REPLICATION_FACTOR_FIELD)
  public Integer serializeReplicationFactor() {
    if (hasReplicationSpecs() && !isGeoSharded()) {
      final ApiAtlasLegacyReplicationSpecView replicationSpec = _replicationSpecs.get(0);
      return replicationSpec.getRegionsConfig().isCrossRegion()
          ? null
          : replicationSpec.getRegionsConfig().getTotalNodes();
    }
    if (hasReplicationFactor()) {
      return _replicationFactor;
    }
    return null;
  }

  public ApiAtlasLegacyClusterDescriptionView() {}

  public ApiAtlasLegacyClusterDescriptionView(
      final ObjectId pId,
      final String pName,
      final ObjectId pGroupId,
      final String pMongoDBVersion,
      final String pMongoDBMajorVersion,
      final String pFeatureCompatibilityVersion,
      final Date pFeatureCompatibilityVersionExpirationDate,
      final Boolean pIsFixedFCV,
      final List<ApiAtlasLegacyReplicationSpecView> pReplicationSpecs,
      final Double pDiskSizeGB,
      final String pMongoURI,
      final String pMongoURIWithOptions,
      final Date pMongoURIUpdated,
      final String pSrvAddress,
      final ApiAtlasClusterDescriptionConnectionStringsView pConnectionStrings,
      final Boolean pBackupEnabled,
      final Boolean pProviderBackupEnabled,
      final Boolean pPitEnabled,
      final ApiAtlasClusterDescriptionStateView pStateName,
      final ApiAtlasClusterProviderSettingsView pProviderSettings,
      final ApiAtlasAutoScalingView pAutoScaling,
      final ApiAtlasBiConnectorView pBiConnector,
      final Boolean pPaused,
      final String pClusterType,
      final ApiAtlasEncryptionAtRestProviderView pEncryptionAtRestProvider,
      final ApiAtlasGeoShardingView pGeoSharding,
      final Integer pReplicationFactor,
      final ApiAtlasLegacyRegionsConfigView pReplicationSpec,
      final Integer pNumShards,
      final List<ApiAtlasNDSLabelView> pLabels,
      final RootCertType pRootCertType,
      final VersionReleaseSystem pVersionReleaseSystem,
      final Boolean pTerminationProtectionEnabled,
      final DiskWarmingMode pDiskWarmingMode,
      final ConfigServerType pConfigServetType,
      final ConfigServerManagementMode pConfigServerManagementMode,
      final Boolean pSelfManagedSharding,
      final ApiAtlasEmployeeAccessGrantView pEmployeeAccessGrant,
      final ReplicaSetScalingStrategy pReplicaSetScalingStrategy,
      final ApiAtlasClusterAdvancedConfigurationView pAdvancedConfiguration) {
    super(
        pId,
        pName,
        pGroupId,
        pMongoDBVersion,
        pConnectionStrings,
        pStateName,
        pTerminationProtectionEnabled);
    _mongoDBMajorVersion = pMongoDBMajorVersion;
    _featureCompatibilityVersion = pFeatureCompatibilityVersion;
    _featureCompatibilityVersionExpirationDate = pFeatureCompatibilityVersionExpirationDate;
    _isFixedFCV = pIsFixedFCV;
    _replicationSpecs = pReplicationSpecs;
    _diskSizeGB = pDiskSizeGB;
    _mongoURI = pMongoURI;
    _mongoURIWithOptions = pMongoURIWithOptions;
    _mongoURIUpdated = pMongoURIUpdated;
    _srvAddress = pSrvAddress;
    _backupEnabled = pBackupEnabled;
    _providerBackupEnabled = pProviderBackupEnabled;
    _pitEnabled = pPitEnabled;
    _autoScaling = pAutoScaling;
    _biConnector = pBiConnector;
    _paused = pPaused;
    _clusterType = pClusterType;
    _encryptionAtRestProvider = pEncryptionAtRestProvider;
    _geoSharding = pGeoSharding;
    _replicationFactor = pReplicationFactor;
    _replicationSpec = pReplicationSpec;
    _providerSettings = pProviderSettings;
    _numShards = pNumShards;
    _labels = pLabels;
    _rootCertType = pRootCertType;
    _versionReleaseSystem = pVersionReleaseSystem;
    _diskWarmingMode = pDiskWarmingMode;
    _configServerType = pConfigServetType;
    _configServerManagementMode = pConfigServerManagementMode;
    _selfManagedSharding = pSelfManagedSharding;
    _employeeAccessGrant = pEmployeeAccessGrant;
    _replicaSetScalingStrategy = pReplicaSetScalingStrategy;
    _advancedConfiguration = pAdvancedConfiguration;
  }

  public ApiAtlasLegacyClusterDescriptionView(
      final ClusterDescriptionView pClusterDescription,
      final ClusterDescriptionProcessArgsView pClusterDescriptionProcessArgsView,
      final AppSettings pSettings,
      final boolean pIncludePrivateStrings,
      final List<CloudProviderContainer> pCloudProviderContainers) {
    this(
        pClusterDescription,
        pClusterDescriptionProcessArgsView,
        pSettings,
        pIncludePrivateStrings,
        pCloudProviderContainers,
        Optional.empty());
  }

  public ApiAtlasLegacyClusterDescriptionView(
      final ClusterDescriptionView pClusterDescription,
      final ClusterDescriptionProcessArgsView pClusterDescriptionProcessArgsView,
      final AppSettings pSettings,
      final boolean pIncludePrivateStrings,
      final List<CloudProviderContainer> pCloudProviderContainers,
      final Optional<String> pFederatedURI) {
    this(
        pClusterDescription,
        pClusterDescriptionProcessArgsView,
        pSettings,
        pIncludePrivateStrings,
        pCloudProviderContainers,
        pFederatedURI,
        null,
        new EffectiveValues());
  }

  public ApiAtlasLegacyClusterDescriptionView(
      final ClusterDescriptionView pClusterDescription,
      final ClusterDescriptionProcessArgsView pClusterDescriptionProcessArgsView,
      final AppSettings pSettings,
      final boolean pIncludePrivateStrings,
      final List<CloudProviderContainer> pCloudProviderContainers,
      final Optional<String> pFederatedURI,
      final List<ApiAtlasTagView> pApiAtlasTagViews) {
    this(
        pClusterDescription,
        pClusterDescriptionProcessArgsView,
        pSettings,
        pIncludePrivateStrings,
        pCloudProviderContainers,
        pFederatedURI,
        pApiAtlasTagViews,
        new EffectiveValues());
  }

  /**
   * @param pEffectiveValues class to use for adding "effective" fields to our view. Currently only
   *     used for tenant clusters to add the effective instance size field
   */
  public ApiAtlasLegacyClusterDescriptionView(
      final ClusterDescriptionView pClusterDescription,
      final ClusterDescriptionProcessArgsView pClusterDescriptionProcessArgsView,
      final AppSettings pSettings,
      final boolean pIncludePrivateStrings,
      final List<CloudProviderContainer> pCloudProviderContainers,
      final Optional<String> pFederatedURI,
      final List<ApiAtlasTagView> pTags,
      final EffectiveValues pEffectiveValues) {
    super(
        pSettings,
        pClusterDescription.getUniqueId(),
        pClusterDescription.getName(),
        pClusterDescription.getGroupId(),
        pClusterDescription.getMongoDBVersion(),
        ApiAtlasClusterDescriptionConnectionStringsView.buildConnectionStrings(
            pClusterDescription, pIncludePrivateStrings, pCloudProviderContainers, pFederatedURI),
        pClusterDescription.getCreateDate(),
        ApiAtlasClusterDescriptionStateView.valueOf(pClusterDescription.getState()),
        pClusterDescription.isTerminationProtectionEnabled(),
        pTags);
    _mongoDBMajorVersion = pClusterDescription.getMongoDBMajorVersion();
    _featureCompatibilityVersion = pClusterDescription.getFeatureCompatibilityVersion();
    _featureCompatibilityVersionExpirationDate =
        pClusterDescription.getFeatureCompatibilityVersionExpirationDate();
    _isFixedFCV = pClusterDescription.isFixedFCV();
    _diskSizeGB = pClusterDescription.getDiskSizeGB();
    _replicationSpecs =
        pClusterDescription.getReplicationSpecList().stream()
            .map(ApiAtlasLegacyReplicationSpecView::new)
            .collect(Collectors.toList());
    _providerSettings =
        ApiAtlasLegacyClusterDescriptionView.buildProviderSettings(pClusterDescription);
    final boolean isMongos =
        pClusterDescription.getClusterDescriptionClusterType().isSharded()
            || pClusterDescription.isServerlessTenantCluster();
    if (pClusterDescription.getMongoDBURIHosts().length != 0) {
      _mongoURI =
          ApiAtlasClusterDescriptionConnectionStringsUtil.getUriFromHosts(
              pClusterDescription.getMongoDBURIHosts());
      _mongoURIWithOptions =
          ApiAtlasClusterDescriptionConnectionStringsUtil.getUriWithOptions(
              isMongos, _mongoURI, pClusterDescription.getDeploymentClusterName());
      _srvAddress =
          ApiAtlasClusterDescriptionConnectionStringsUtil.getSrvUri(
              pClusterDescription.getSRVAddress());
    }
    _mongoURIUpdated = pClusterDescription.getMongoDBURIHostsLastUpdateDate();
    _backupEnabled = pClusterDescription.isBackupEnabled();
    _providerBackupEnabled = pClusterDescription.isDiskBackupEnabled();
    _pitEnabled = pClusterDescription.isPitEnabled();
    _autoScaling = new ApiAtlasAutoScalingView(pClusterDescription.getAutoScalingView());
    _biConnector = new ApiAtlasBiConnectorView(pClusterDescription.getBiConnectorView());
    _paused = pClusterDescription.isPaused();
    _clusterType = pClusterDescription.getClusterType();
    _encryptionAtRestProvider =
        ApiAtlasEncryptionAtRestProviderView.valueOf(
            pClusterDescription.getEncryptionAtRestProvider().getEncryptionAtRestProvider());
    _geoSharding =
        new ApiAtlasGeoShardingView(
            pClusterDescription.getGeoShardingView(), pClusterDescription.getReplicationSpecList());
    _labels =
        pClusterDescription.getLabels().stream()
            .map(ApiAtlasNDSLabelView::new)
            .collect(Collectors.toList());
    _rootCertType = pClusterDescription.getRootCertType();
    _versionReleaseSystem = pClusterDescription.getVersionReleaseSystem();
    _diskWarmingMode = pClusterDescription.getDiskWarmingMode();
    _configServerType = pClusterDescription.getConfigServerType();
    _configServerManagementMode = pClusterDescription.getConfigServerManagementMode().orElse(null);
    _selfManagedSharding = pClusterDescription.getGeoShardingView().isSelfManagedSharding();
    _employeeAccessGrant =
        pClusterDescription
            .getEmployeeAccessGrant()
            .map(ApiAtlasEmployeeAccessGrantView::new)
            .orElse(null);
    _replicaSetScalingStrategy = pClusterDescription.getReplicaSetScalingStrategy();

    if (pClusterDescription.isFreeOrSharedTierTenantCluster()
        && !pClusterDescription.getInstanceSizes().equals(Set.of(FreeInstanceSize.M0))
        && pEffectiveValues.getEffectiveElectableInstanceSize().isPresent()) {
      // Updating our provider settings to have the effective instance size field
      _providerSettings =
          ((ApiAtlasFreeProviderSettingsView) getProviderSettings())
              .toBuilder()
                  .effectiveInstanceSize(
                      pEffectiveValues.getEffectiveElectableInstanceSize().orElseThrow())
                  .build();
    }

    addLinks(pClusterDescription, "/clusters/");

    _advancedConfiguration =
        Optional.ofNullable(pClusterDescriptionProcessArgsView)
            .map(ApiAtlasClusterAdvancedConfigurationView::new)
            .orElse(null);
  }

  private ApiAtlasLegacyClusterDescriptionView(
      final ApiAtlasLegacyClusterDescriptionView pNew,
      final ApiAtlasLegacyClusterDescriptionView pExisting,
      final Boolean pEnvelope) {

    super(pNew, pExisting);

    _mongoDBMajorVersion =
        pNew.getVersionReleaseSystem() == VersionReleaseSystem.CONTINUOUS
                || pNew.hasMongoDBMajorVersion()
            ? pNew.getMongoDBMajorVersion()
            : pExisting.getMongoDBMajorVersion();
    _featureCompatibilityVersion = pExisting.getFeatureCompatibilityVersion();
    _featureCompatibilityVersionExpirationDate =
        pExisting.getFeatureCompatibilityVersionExpirationDate();
    _isFixedFCV = pExisting.getIsFixedFCV();
    _mongoURI = pExisting.getMongoURI();
    _mongoURIWithOptions = pExisting.getMongoURIWithOptions();
    _mongoURIUpdated = new Date();
    _backupEnabled =
        pNew.hasTheFieldNamedBackupEnabled()
            ? pNew.getBackupEnabled()
            : pExisting.getBackupEnabled();
    _providerBackupEnabled =
        pNew.hasTheFieldNamedProviderBackupEnabled()
            ? pNew.getProviderBackupEnabled()
            : pExisting.getProviderBackupEnabled();
    _pitEnabled =
        pNew.hasTheFieldNamedPitEnabled() ? pNew.getPitEnabled() : pExisting.getPitEnabled();

    _autoScaling =
        pNew.hasAutoScaling()
            ? (pExisting.hasAutoScaling()
                ? pNew.getAutoScaling().toUpdatedAutoScalingView(pExisting.getAutoScaling())
                : pNew.getAutoScaling())
            : pExisting.getAutoScaling();
    _biConnector =
        pNew.hasBiConnector()
            ? (pExisting.hasBiConnector()
                ? pNew.getBiConnector().toUpdatedBiConnectorView(pExisting.getBiConnector())
                : pNew.getBiConnector())
            : pExisting.getBiConnector();
    _paused = pNew.hasPaused() ? pNew.getPaused() : pExisting.getPaused();
    _encryptionAtRestProvider =
        pNew.hasEncryptionAtRestProvider()
            ? pNew.getEncryptionAtRestProvider()
            : pExisting.getEncryptionAtRestProvider();
    _selfManagedSharding =
        Optional.ofNullable(pNew.getSelfManagedSharding())
            .orElse(pExisting.getSelfManagedSharding());
    _geoSharding =
        isSelfManagedSharding()
            ? new ApiAtlasGeoShardingView(
                GeoShardingView.getDefaultGeoShardingView(isSelfManagedSharding()), List.of())
            : pExisting.getGeoSharding();
    _employeeAccessGrant =
        Optional.ofNullable(pNew.getEmployeeAccessGrant())
            .orElse(pExisting.getEmployeeAccessGrant());
    _replicaSetScalingStrategy =
        Optional.ofNullable(pNew.getReplicaSetScalingStrategy())
            .orElse(pExisting.getReplicaSetScalingStrategy());
    _labels = pNew.hasLabels() ? pNew.getLabels() : pExisting.getLabels();
    _rootCertType = pNew.hasRootCertType() ? pNew.getRootCertType() : pExisting.getRootCertType();
    _versionReleaseSystem =
        pNew.hasVersionReleaseSystem()
            ? pNew.getVersionReleaseSystem()
            : pExisting.getVersionReleaseSystem();
    updateProviderSettings(pNew, pExisting, pEnvelope);
    updateReplicationFields(pNew, pExisting);
    _acceptDataRisksAndForceReplicaSetReconfig =
        pNew.hasTheFieldNamedAcceptDataRisks()
            ? pNew.getAcceptDataRisksAndForceReplicaSetReconfig()
            : pExisting.getAcceptDataRisksAndForceReplicaSetReconfig();
    _diskIOPS = pNew._diskIOPS;
    _diskThroughput = pNew._diskThroughput;
    _diskWarmingMode =
        pNew.hasDiskWarmingMode() ? pNew.getDiskWarmingMode() : pExisting.getDiskWarmingMode();
    _configServerType =
        pNew.hasConfigServerType() ? pNew.getConfigServerType() : pExisting.getConfigServerType();
    _configServerManagementMode =
        pNew.hasConfigServerManagementMode()
            ? pNew.getConfigServerManagementMode()
            : pExisting.getConfigServerManagementMode();
    _replicaSetScalingStrategy =
        Optional.ofNullable(pNew.getReplicaSetScalingStrategy())
            .orElse(pExisting.getReplicaSetScalingStrategy());
    _advancedConfiguration =
        pNew.getAdvancedConfiguration().orElse(pExisting.getAdvancedConfiguration().orElse(null));
  }

  private void updateProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView pNew,
      final ApiAtlasLegacyClusterDescriptionView pExisting,
      final Boolean pEnvelope) {
    if (pExisting.getProviderSettings() instanceof ApiAtlasAWSProviderSettingsView) {
      updateAWSProviderSettings(pNew, pExisting);
    } else if (pExisting.getProviderSettings() instanceof ApiAtlasAzureProviderSettingsView) {
      updateAzureProviderSettings(pNew, pExisting, pEnvelope);
    } else if (pExisting.getProviderSettings() instanceof ApiAtlasGCPProviderSettingsView) {
      updateGCPProviderSettings(pNew, pExisting);
    } else if (pExisting.getProviderSettings() instanceof ApiAtlasFreeProviderSettingsView) {
      updateFreeProviderSettings(pNew, pExisting);
    } else if (pExisting.getProviderSettings() instanceof ApiAtlasFlexProviderSettingsView) {
      updateFlexProviderSettings(pNew, pExisting);
    } else {
      // Thrown for serverless provider here as well
      throw new IllegalArgumentException("Unrecognized cloud provider.");
    }
  }

  private static ApiAtlasClusterProviderSettingsView buildProviderSettings(
      final ClusterDescriptionView pClusterDescription) {
    final ApiAtlasClusterProviderSettingsView providerSettings;
    final Set<CloudProvider> providers = pClusterDescription.getCloudProviders();
    if (providers.equals(Set.of(CloudProvider.AWS))) {
      providerSettings = new ApiAtlasAWSProviderSettingsView(pClusterDescription);
    } else if (providers.equals(Set.of(CloudProvider.AZURE))) {
      providerSettings = new ApiAtlasAzureProviderSettingsView(pClusterDescription);
    } else if (providers.equals(Set.of(CloudProvider.GCP))) {
      providerSettings = new ApiAtlasGCPProviderSettingsView(pClusterDescription);
    } else if (providers.equals(Set.of(CloudProvider.FREE))) {
      providerSettings = new ApiAtlasFreeProviderSettingsView(pClusterDescription);
    } else if (providers.equals(Set.of(CloudProvider.FLEX))) {
      providerSettings = new ApiAtlasFlexProviderSettingsView(pClusterDescription);
    } else {
      // Thrown for serverless and flex providers here as well
      throw new IllegalArgumentException("Unrecognized cloud provider.");
    }
    return providerSettings;
  }

  public ApiAtlasLegacyRegionsConfigView getReplicationSpec() {
    return _replicationSpec;
  }

  public ClusterDescription.ClusterType getClusterDescriptionClusterType() {
    if (hasClusterType()) {
      return ClusterDescription.ClusterType.valueOf(getClusterType());
    }
    return null;
  }

  private void updateReplicationFields(
      final ApiAtlasLegacyClusterDescriptionView pNew,
      final ApiAtlasLegacyClusterDescriptionView pExisting) {
    if (pNew.hasReplicationSpecs()) {
      updateReplicationSpecsWithReplicationSpecs(pNew, pExisting);
    } else if (pNew.hasReplicationSpec()) {
      updateReplicationSpecsWithOldReplicationSpecSchema(pNew, pExisting);
    } else {
      updateReplicationSpecsWithProviderSettings(pNew, pExisting);
    }
  }

  private void updateReplicationSpecsWithReplicationSpecs(
      final ApiAtlasLegacyClusterDescriptionView pNew,
      final ApiAtlasLegacyClusterDescriptionView pExisting) {
    // Someone who's using the new replicationSpecs schema is expected to specify the cluster
    // type as well
    _replicationSpecs = pNew.getReplicationSpecs();
    _clusterType = pNew.hasClusterType() ? pNew.getClusterType() : pExisting.getClusterType();

    if (!_clusterType.equals(ClusterDescription.ClusterType.GEOSHARDED.name())
        && _replicationSpecs.size() == 1
        && !_replicationSpecs.get(0).hasZoneName()) {
      _replicationSpecs.get(0).setZoneName(pExisting.getReplicationSpecs().get(0).getZoneName());
    }
  }

  private void updateReplicationSpecsWithOldReplicationSpecSchema(
      final ApiAtlasLegacyClusterDescriptionView pNew,
      final ApiAtlasLegacyClusterDescriptionView pExisting) {
    // An existing view will always have a correctly configured replicationSpecs array
    _replicationSpecs = pExisting.getReplicationSpecs();

    // Use the provided replication spec as the new region config in the first replication spec
    _replicationSpecs.get(0).setRegionsConfig(pNew.getReplicationSpec());

    // Set the number of shards
    if (pNew.hasNumShards()) {
      _replicationSpecs.get(0).setNumShards(pNew.getNumShards());
    }

    _clusterType =
        pNew.hasClusterType()
            ? pNew.getClusterType()
            : getClusterTypeFromReplicationSpec(
                _replicationSpecs.get(0), pExisting.getClusterType());
  }

  /**
   * Maintain backward compatibility and allow users to provision a simple cluster by specifying the
   * numshards, replication factor, and a region
   */
  private void updateReplicationSpecsWithProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView pNew,
      final ApiAtlasLegacyClusterDescriptionView pExisting) {
    // An existing view will always have a correctly configured replicationSpecs array
    _replicationSpecs = pExisting.getReplicationSpecs();

    if (getProviderSettings() != null && getProviderSettings().getRegionName() != null) {
      final ApiAtlasLegacyRegionsConfigView existingConfig =
          pExisting.getReplicationSpecs().get(0).getRegionsConfig();
      final ApiAtlasRegionSpecView existingRegionSpec =
          existingConfig.entrySet().iterator().next().getValue();

      // Update the existing region config with the new region name but preserve the existing
      // region spec configurations
      final ApiAtlasLegacyRegionsConfigView configWithUpdatedRegionName =
          new ApiAtlasLegacyRegionsConfigView(
              getProviderSettings().getRegionName(), existingRegionSpec);
      _replicationSpecs.get(0).setRegionsConfig(configWithUpdatedRegionName);
    }

    // Set the numshards on the first replication spec if provided
    if (pNew.hasNumShards()) {
      _replicationSpecs.get(0).setNumShards(pNew.getNumShards());
    }

    // Set the replication factor on the first/only region spec
    if (pNew.hasReplicationFactor()) {
      _replicationSpecs
          .get(0)
          .getRegionsConfig()
          .entrySet()
          .iterator()
          .next()
          .getValue()
          .setReplicationFactor(pNew.getReplicationFactor());
    }

    _clusterType =
        pNew.hasClusterType()
            ? pNew.getClusterType()
            : (pExisting.getClusterType().equals(ClusterDescription.ClusterType.GEOSHARDED.name())
                ? ClusterDescription.ClusterType.GEOSHARDED.name()
                : getClusterTypeFromReplicationSpec(
                    _replicationSpecs.get(0), pExisting.getClusterType()));
  }

  private String getClusterTypeFromReplicationSpec(
      final ApiAtlasLegacyReplicationSpecView pReplicationSpec, final String pExistingClusterType) {
    if (pReplicationSpec.getNumShards() > 1) {
      return ClusterDescription.ClusterType.SHARDED.name();
    } else if (pReplicationSpec.getNumShards() == 1 && pExistingClusterType != null) {
      return pExistingClusterType;
    }
    return ClusterDescription.ClusterType.REPLICASET.name();
  }

  private ApiAtlasClusterProviderSettingsView updateProviderSettingsComputeAutoScalingForEnabled(
      final ApiAtlasLegacyClusterDescriptionView pNewView,
      final ApiAtlasLegacyClusterDescriptionView pExistingView,
      final ApiAtlasClusterProviderSettingsView pProviderSettings) {
    final ApiAtlasClusterProviderSettingsView newProviderSettings = pNewView.getProviderSettings();

    final Optional<ApiAtlasComputeAutoScalingView> newCompute =
        Optional.ofNullable(pNewView.getAutoScaling()).map(ApiAtlasAutoScalingView::getCompute);
    final Optional<ApiAtlasProviderComputeAutoScalingView> newProviderCompute =
        Optional.ofNullable(newProviderSettings)
            .map(ApiAtlasClusterProviderSettingsView::getAutoScaling)
            .map(ApiAtlasProviderAutoScalingView::getCompute);

    final ApiAtlasComputeAutoScalingView existingCompute =
        pExistingView.getAutoScaling().getCompute();
    final ApiAtlasProviderComputeAutoScalingView existingProviderCompute =
        pExistingView.getProviderSettings().getAutoScaling().getCompute();

    final JsonOptional<? extends ApiAtlasInstanceSizeView> maxInstanceSize;
    final boolean newMaxInstanceSizeUnset =
        newProviderCompute
            .map(ApiAtlasProviderComputeAutoScalingView::getMaxInstanceSize)
            .orElseGet(JsonOptional::unset)
            .isUnset();
    // compute auto-scaling is transitioning from enabled to disabled (explicit) and no max
    // instance size was specified, then clear max instance size
    final boolean enabledToDisabledWithNoNewMaxSet =
        existingCompute.isEnabled()
            && newCompute.map(ApiAtlasComputeAutoScalingView::hasEnabled).orElse(false)
            && !newCompute.map(ApiAtlasComputeAutoScalingView::isEnabled).orElse(false)
            && newMaxInstanceSizeUnset;
    // compute auto-scaling is disabled (explicit or implicit) and no max instance size was
    // specified, then clear max instance size
    final boolean disabledNoWithNewMaxSet =
        !existingCompute.isEnabled()
            && (!newCompute.map(ApiAtlasComputeAutoScalingView::hasEnabled).orElse(false)
                || !newCompute.map(ApiAtlasComputeAutoScalingView::isEnabled).orElse(false))
            && newMaxInstanceSizeUnset;
    // compute auto-scaling is transitioning from disabled to enabled (explicit) and no max instance
    // size was specified, then clear max instance size
    final boolean disabledToEnabledWithNoNewMaxSet =
        !existingCompute.isEnabled()
            && newCompute.map(ApiAtlasComputeAutoScalingView::hasEnabled).orElse(false)
            && newCompute.map(ApiAtlasComputeAutoScalingView::isEnabled).orElse(false)
            && newMaxInstanceSizeUnset;

    if (enabledToDisabledWithNoNewMaxSet
        || disabledNoWithNewMaxSet
        || disabledToEnabledWithNoNewMaxSet) {
      maxInstanceSize = JsonOptional.empty();
    } else {
      maxInstanceSize = pProviderSettings.getAutoScaling().getCompute().getMaxInstanceSize();
    }
    final JsonOptional<? extends ApiAtlasInstanceSizeView> minInstanceSize;
    final boolean newMinInstanceSizeUnset =
        newProviderCompute
            .map(ApiAtlasProviderComputeAutoScalingView::getMinInstanceSize)
            .orElseGet(JsonOptional::unset)
            .isUnset();
    // compute auto-scaling is transitioning from scale down enabled to scale down disabled
    // (explicit) and no min instance size was specified, then clear min instance size
    final boolean enabledToDisabledWithNoNewMinSet =
        existingCompute.isScaleDownEnabled()
            && newCompute.map(ApiAtlasComputeAutoScalingView::hasScaleDownEnabled).orElse(false)
            && !newCompute.map(ApiAtlasComputeAutoScalingView::isScaleDownEnabled).orElse(false)
            && newMinInstanceSizeUnset;
    // compute auto-scaling scale down is disabled (explicit or implicit) and no min instance size
    // was specified, then clear min instance size
    final boolean disabledWithNoNewMinSet =
        !existingCompute.isScaleDownEnabled()
            && (!newCompute.map(ApiAtlasComputeAutoScalingView::hasScaleDownEnabled).orElse(false)
                || !newCompute
                    .map(ApiAtlasComputeAutoScalingView::isScaleDownEnabled)
                    .orElse(false))
            && newMinInstanceSizeUnset;
    // compute auto-scaling is transitioning from scale down disabled to scale down enabled
    // (explicit) and no min instance size was specified, then clear min instance size
    final boolean disabledToEnabledWithNoNewMinSet =
        !existingCompute.isScaleDownEnabled()
            && newCompute.map(ApiAtlasComputeAutoScalingView::hasScaleDownEnabled).orElse(false)
            && newCompute.map(ApiAtlasComputeAutoScalingView::isScaleDownEnabled).orElse(false)
            && newMinInstanceSizeUnset;
    if (enabledToDisabledWithNoNewMinSet
        || disabledWithNoNewMinSet
        || disabledToEnabledWithNoNewMinSet) {
      minInstanceSize = JsonOptional.empty();
    } else {
      minInstanceSize = pProviderSettings.getAutoScaling().getCompute().getMinInstanceSize();
    }

    // update the provider settings
    switch (pProviderSettings.getProviderName()) {
      case AWS:
        final ApiAtlasAWSProviderSettingsView awsProviderSettingsView =
            (ApiAtlasAWSProviderSettingsView) pProviderSettings;
        @SuppressWarnings("unchecked")
        final JsonOptional<ApiAtlasAWSInstanceSizeView> awsMaxInstanceSize =
            (JsonOptional<ApiAtlasAWSInstanceSizeView>) maxInstanceSize;
        @SuppressWarnings("unchecked")
        final JsonOptional<ApiAtlasAWSInstanceSizeView> awsMinInstanceSize =
            (JsonOptional<ApiAtlasAWSInstanceSizeView>) minInstanceSize;
        return awsProviderSettingsView.updateAutoScaling(
            awsProviderSettingsView.getAutoScaling().toBuilder()
                .compute(
                    awsProviderSettingsView.getAutoScaling().getCompute().toBuilder()
                        .maxInstanceSizeView(awsMaxInstanceSize)
                        .minInstanceSizeView(awsMinInstanceSize)
                        .build())
                .build());
      case AZURE:
        final ApiAtlasAzureProviderSettingsView azureProviderSettingsView =
            (ApiAtlasAzureProviderSettingsView) pProviderSettings;
        @SuppressWarnings("unchecked")
        final JsonOptional<ApiAtlasAzureInstanceSizeView> azureMaxInstanceSize =
            (JsonOptional<ApiAtlasAzureInstanceSizeView>) maxInstanceSize;
        @SuppressWarnings("unchecked")
        final JsonOptional<ApiAtlasAzureInstanceSizeView> azureMinInstanceSize =
            (JsonOptional<ApiAtlasAzureInstanceSizeView>) minInstanceSize;
        return azureProviderSettingsView.updateAutoScaling(
            azureProviderSettingsView.getAutoScaling().toBuilder()
                .compute(
                    azureProviderSettingsView.getAutoScaling().getCompute().toBuilder()
                        .maxInstanceSizeView(azureMaxInstanceSize)
                        .minInstanceSizeView(azureMinInstanceSize)
                        .build())
                .build());
      case GCP:
        final ApiAtlasGCPProviderSettingsView gcpProviderSettingsView =
            (ApiAtlasGCPProviderSettingsView) pProviderSettings;
        @SuppressWarnings("unchecked")
        final JsonOptional<ApiAtlasGCPInstanceSizeView> gcpMaxInstanceSize =
            (JsonOptional<ApiAtlasGCPInstanceSizeView>) maxInstanceSize;
        @SuppressWarnings("unchecked")
        final JsonOptional<ApiAtlasGCPInstanceSizeView> gcpMinInstanceSize =
            (JsonOptional<ApiAtlasGCPInstanceSizeView>) minInstanceSize;
        return gcpProviderSettingsView.updateAutoScaling(
            gcpProviderSettingsView.getAutoScaling().toBuilder()
                .compute(
                    gcpProviderSettingsView.getAutoScaling().getCompute().toBuilder()
                        .maxInstanceSizeView(gcpMaxInstanceSize)
                        .minInstanceSizeView(gcpMinInstanceSize)
                        .build())
                .build());
    }
    return pProviderSettings;
  }

  private void updateAWSProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView pNew,
      final ApiAtlasLegacyClusterDescriptionView pExisting) {
    final ApiAtlasAWSProviderSettingsView newAwsProviderSettings =
        (ApiAtlasAWSProviderSettingsView) pNew.getProviderSettings();
    final ApiAtlasAWSProviderSettingsView existingAwsProviderSettings =
        (ApiAtlasAWSProviderSettingsView) pExisting.getProviderSettings();
    final ApiAtlasAWSRegionNameView regionName;
    if (pNew.hasReplicationSpecs()) {
      if (pNew.isGeoSharded()) {
        regionName = null;
      } else {
        regionName =
            pNew.getReplicationSpecs().get(0).getRegionsConfig().isCrossRegion()
                ? null
                : ApiAtlasAWSRegionNameView.valueOf(
                    AWSRegionName.valueOf(
                        pNew.getReplicationSpecs()
                            .get(0)
                            .getRegionsConfig()
                            .keySet()
                            .iterator()
                            .next()));
      }
    } else {
      regionName =
          (newAwsProviderSettings != null && newAwsProviderSettings.hasRegionNameView())
              ? newAwsProviderSettings.getRegionNameView()
              : existingAwsProviderSettings.getRegionNameView();
    }

    if (newAwsProviderSettings != null) {
      final Optional<AWSInstanceSize> newInstanceSize =
          Optional.of(newAwsProviderSettings)
              .map(ApiAtlasAWSProviderSettingsView::getInstanceSize)
              .map(ApiAtlasAWSInstanceSizeView::getAWSInstanceSize);
      if (newInstanceSize.isPresent() && newInstanceSize.get().isNVMe()) {
        _diskSizeGB = (double) newInstanceSize.get().getDefaultDiskSizeGB();
      } else {
        _diskSizeGB = pNew.hasDiskSizeGB() ? pNew.getDiskSizeGB() : pExisting.getDiskSizeGB();
      }
      setProviderSettings(
          newAwsProviderSettings.toUpdatedAWSProviderSettingsView(
              existingAwsProviderSettings, _diskSizeGB, regionName));

    } else {
      _diskSizeGB = pNew.hasDiskSizeGB() ? pNew.getDiskSizeGB() : pExisting.getDiskSizeGB();
      setProviderSettings(
          ((ApiAtlasAWSProviderSettingsView) pExisting.getProviderSettings())
              .updateDiskIOPS(_diskSizeGB)
              .updateRegionName(regionName));
    }

    setProviderSettings(
        updateProviderSettingsComputeAutoScalingForEnabled(pNew, pExisting, getProviderSettings()));
  }

  private void updateAzureProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView pNew,
      final ApiAtlasLegacyClusterDescriptionView pExisting,
      final Boolean pEnvelope) {
    final ApiAtlasAzureProviderSettingsView newAzureProviderSettings =
        (ApiAtlasAzureProviderSettingsView) pNew.getProviderSettings();
    final ApiAtlasAzureProviderSettingsView existingAzureProviderSettings =
        (ApiAtlasAzureProviderSettingsView) pExisting.getProviderSettings();
    final ApiAtlasAzureRegionNameView regionName;
    if (pNew.hasReplicationSpecs()) {
      if (pNew.isGeoSharded()) {
        regionName = null;
      } else {
        regionName =
            pNew.getReplicationSpecs().get(0).getRegionsConfig().isCrossRegion()
                ? null
                : ApiAtlasAzureRegionNameView.valueOf(
                    AzureRegionName.valueOf(
                        pNew.getReplicationSpecs()
                            .get(0)
                            .getRegionsConfig()
                            .keySet()
                            .iterator()
                            .next()));
      }
    } else {
      regionName =
          (newAzureProviderSettings != null && newAzureProviderSettings.hasRegionNameView())
              ? newAzureProviderSettings.getRegionNameView()
              : existingAzureProviderSettings.getRegionNameView();
    }

    if (newAzureProviderSettings != null) {
      setProviderSettings(
          newAzureProviderSettings.toUpdatedAzureProviderSettingsView(
              (ApiAtlasAzureProviderSettingsView) pExisting.getProviderSettings(), regionName));

      if (newAzureProviderSettings.hasInstanceSize()
          && newAzureProviderSettings.getInstanceSize().getAzureInstanceSize().isNVMe()) {
        _diskSizeGB =
            (double)
                newAzureProviderSettings
                    .getInstanceSize()
                    .getAzureInstanceSize()
                    .getDefaultDiskSizeGB();
      } else if (pNew.hasDiskSizeGB()) {
        _diskSizeGB = pNew.getDiskSizeGB();
        setProviderSettings(
            ((ApiAtlasAzureProviderSettingsView) getProviderSettings())
                .updateDiskType(getAzureDiskType(_diskSizeGB, pEnvelope)));
      } else if (newAzureProviderSettings.hasDiskType()) {
        _diskSizeGB =
            (double) newAzureProviderSettings.getDiskType().getAzureDiskType().getSizeGB();
      }
    } else {
      setProviderSettings(
          ((ApiAtlasAzureProviderSettingsView) pExisting.getProviderSettings())
              .updateRegionName(regionName));

      if (pNew.hasDiskSizeGB()) {
        _diskSizeGB = pNew.getDiskSizeGB();
        if (!existingAzureProviderSettings.getInstanceSize().getAzureInstanceSize().isNVMe()) {
          setProviderSettings(
              ((ApiAtlasAzureProviderSettingsView) getProviderSettings())
                  .updateDiskType(getAzureDiskType(_diskSizeGB, pEnvelope)));
        }
      } else {
        _diskSizeGB = pExisting.getDiskSizeGB();
      }
    }

    setProviderSettings(
        updateProviderSettingsComputeAutoScalingForEnabled(pNew, pExisting, getProviderSettings()));
  }

  private ApiAzureDiskTypeView getAzureDiskType(Double pDiskSizeGB, Boolean pEnvelope) {
    final AzureDiskType approximateDiskType = AzureDiskType.getApproximatePv1DiskType(pDiskSizeGB);
    return ApiAzureDiskTypeView.valueOf(approximateDiskType);
  }

  private void updateGCPProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView pNewView,
      final ApiAtlasLegacyClusterDescriptionView pExistingView) {
    final ApiAtlasGCPProviderSettingsView newProviderSettings =
        (ApiAtlasGCPProviderSettingsView) pNewView.getProviderSettings();
    final ApiAtlasGCPProviderSettingsView existingProviderSettings =
        (ApiAtlasGCPProviderSettingsView) pExistingView.getProviderSettings();
    final ApiAtlasGCPRegionNameView regionName;
    if (pNewView.hasReplicationSpecs()) {
      if (pNewView.isGeoSharded()) {
        regionName = null;
      } else {
        regionName =
            pNewView.getReplicationSpecs().get(0).getRegionsConfig().isCrossRegion()
                ? null
                : getValidatedGCPRegionNameView(
                    pNewView
                        .getReplicationSpecs()
                        .get(0)
                        .getRegionsConfig()
                        .keySet()
                        .iterator()
                        .next());
      }
    } else {
      regionName =
          (newProviderSettings != null && newProviderSettings.hasRegionNameView())
              ? newProviderSettings.getRegionNameView()
              : existingProviderSettings.getRegionNameView();
    }

    _diskSizeGB =
        pNewView.hasDiskSizeGB() ? pNewView.getDiskSizeGB() : pExistingView.getDiskSizeGB();
    if (newProviderSettings != null) {
      setProviderSettings(
          newProviderSettings.toUpdatedGCPProviderSettingsView(
              existingProviderSettings, regionName));
    } else {
      setProviderSettings(existingProviderSettings.updateRegionName(regionName));
    }

    setProviderSettings(
        updateProviderSettingsComputeAutoScalingForEnabled(
            pNewView, pExistingView, getProviderSettings()));
  }

  private void updateFreeProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView pNewView,
      final ApiAtlasLegacyClusterDescriptionView pExistingView) {
    final ApiAtlasFreeProviderSettingsView newProviderSettings =
        (ApiAtlasFreeProviderSettingsView) pNewView.getProviderSettings();
    final ApiAtlasFreeProviderSettingsView existingProviderSettings =
        (ApiAtlasFreeProviderSettingsView) pExistingView.getProviderSettings();

    if (pNewView.hasProviderSettings()) {
      // diskSizeGB is only updated for Free if the new view contains provider settings
      _diskSizeGB = newProviderSettings.getInstanceSize().getFreeInstanceSize().getDiskSizeGB();
      setProviderSettings(
          newProviderSettings.toUpdatedFreeProviderSettingsView(existingProviderSettings));
    } else {
      setProviderSettings(existingProviderSettings);
    }
  }

  private void updateFlexProviderSettings(
      final ApiAtlasLegacyClusterDescriptionView pNewView,
      final ApiAtlasLegacyClusterDescriptionView pExistingView) {
    final ApiAtlasFlexProviderSettingsView newProviderSettings =
        (ApiAtlasFlexProviderSettingsView) pNewView.getProviderSettings();
    final ApiAtlasFlexProviderSettingsView existingProviderSettings =
        (ApiAtlasFlexProviderSettingsView) pExistingView.getProviderSettings();

    if (pNewView.hasProviderSettings()) {
      // diskSizeGB is only updated for Flex if the new view contains provider settings
      _diskSizeGB = newProviderSettings.getInstanceSize().getFlexInstanceSize().getDiskSizeGB();
      setProviderSettings(
          newProviderSettings.toUpdatedFlexProviderSettingsView(existingProviderSettings));
    } else {
      setProviderSettings(existingProviderSettings);
    }
  }

  public ApiAtlasLegacyClusterDescriptionView toUpdatedClusterDescriptionView(
      final ApiAtlasLegacyClusterDescriptionView pExisting, final Boolean pEnvelope) {
    return new ApiAtlasLegacyClusterDescriptionView(this, pExisting, pEnvelope);
  }

  public ClusterDescriptionView toClusterDescriptionView(
      final ClusterDescriptionView pExistingView, final Boolean pEnvelope) throws SvcException {
    final ApiAtlasClusterProviderSettingsView providerSettings = this.getProviderSettings();
    if (providerSettings instanceof ApiAtlasAWSProviderSettingsView) {
      return this.toAWSClusterDescriptionView(pExistingView);
    } else if (providerSettings instanceof ApiAtlasAzureProviderSettingsView) {
      return this.toAzureClusterDescriptionView();
    } else if (providerSettings instanceof ApiAtlasGCPProviderSettingsView) {
      return this.toGCPClusterDescriptionView();
    } else if (providerSettings instanceof ApiAtlasFreeProviderSettingsView) {
      return this.toFreeClusterDescriptionView();
    } else if (providerSettings instanceof ApiAtlasFlexProviderSettingsView) {
      return this.toFlexClusterDescriptionView();
    } else {
      throw ApiErrorCode.INVALID_PROVIDER.exception(pEnvelope, providerSettings.getProviderName());
    }
  }

  // Protected (rather than private) for use in unit tests
  protected ClusterDescriptionView toAWSClusterDescriptionView(
      final ClusterDescriptionView pExistingClusterDescriptionView) throws SvcException {
    final ClusterDescriptionViewBuilder builder = ClusterDescriptionView.builder();
    final ApiAtlasAWSProviderSettingsView providerSettingsView =
        (ApiAtlasAWSProviderSettingsView) getProviderSettings();
    // note, the default provider specific auto-scaling view is initialized with the current
    // min/max instance sizes for AWS. the default provider agnostic auto-scaling view initializes
    // disk auto-scaling to enabled and compute auto-scaling to disabled
    final AWSAutoScaling autoScaling =
        (providerSettingsView.hasAutoScaling()
                ? providerSettingsView.getAutoScaling().toAutoScalingView(getAutoScaling())
                : ApiAtlasAWSAutoScalingView.getDefaultAutoScalingView()
                    .toAutoScalingView(getAutoScaling()))
            .toAutoScaling();

    setStandardClusterFields(builder);

    // Provider settings
    final AWSNDSInstanceSize instanceSize =
        Optional.ofNullable(providerSettingsView.getInstanceSize())
            .map(ApiAtlasAWSInstanceSizeView::getAWSInstanceSize)
            .get();

    if (instanceSize.isNVMe()) {
      builder.diskSizeGB(Double.valueOf(instanceSize.getDefaultDiskSizeGB()));
    } else {
      builder.diskSizeGB(getDiskSizeGB());
    }

    final Integer diskIOPS;
    final VolumeType backwardsCompatibleVolumeType =
        getBackwardsCompatibleVolumeType(providerSettingsView, pExistingClusterDescriptionView);
    if (instanceSize.isNVMe()) {
      diskIOPS = instanceSize.getMaxSSDReadIOPS().get() + instanceSize.getMaxSSDWriteIOPS().get();
    } else {
      diskIOPS = providerSettingsView.getDiskIOPS();
    }
    final AWSHardwareSpec awsHardwareSpec =
        new AWSHardwareSpec.Builder()
            .setInstanceSize(instanceSize)
            .setDiskIOPS(diskIOPS)
            .setEBSVolumeType(backwardsCompatibleVolumeType)
            .setEncryptEBSVolume(providerSettingsView.getEncryptEBSVolume())
            .build();

    final var replicationSpecViews = new ArrayList<ReplicationSpecView>();
    for (final var legacyReplicationSpecView : getReplicationSpecs()) {
      replicationSpecViews.add(
          legacyReplicationSpecView.toReplicationSpecViewFromLegacyRegionsConfig(
              CloudProvider.AWS, awsHardwareSpec, autoScaling));
    }
    builder.replicationSpecList(replicationSpecViews);

    builder.acceptDataRisksAndForceReplicaSetReconfig(
        getAcceptDataRisksAndForceReplicaSetReconfig());

    builder.cloudProvider(CloudProvider.PROVIDER_AWS);
    builder.tenantAccessRevokedForPause(false);
    builder.underCompaction(false);
    builder.hostnameSubdomainLevel("");
    builder.hostnameSchemeForAgents("");

    return builder.build();
  }

  private void setStandardClusterFields(final ClusterDescriptionViewBuilder pBuilder) {
    setStandardFields(pBuilder);

    pBuilder
        .mongoDBMajorVersion(getMongoDBMajorVersion())
        .backupEnabled(getBackupEnabled())
        .diskBackupEnabled(getProviderBackupEnabled())
        .pitEnabled(getPitEnabled())
        .clusterType(getClusterType())
        .biConnector(
            hasBiConnector()
                ? getBiConnector().toBiConnectorView()
                : BiConnectorView.getBiConnectorDisabledView())
        .isPaused(getPaused())
        .geoShardingView(
            hasGeoSharding()
                ? new GeoShardingView(getGeoSharding().toGeoSharding())
                : GeoShardingView.getDefaultGeoShardingView())
        .encryptionAtRestProvider(
            EncryptionAtRestProviderView.valueOf(
                hasEncryptionAtRestProvider()
                    ? getEncryptionAtRestProvider().getEncryptionAtRestProvider()
                    : EncryptionAtRestProvider.NONE))
        .labels(
            hasLabels()
                ? getLabels().stream()
                    .map(ApiAtlasNDSLabelView::toNDSLabelView)
                    .collect(Collectors.toList())
                : Collections.emptyList())
        .rootCertType(getRootCertType())
        .versionReleaseSystem(getVersionReleaseSystem())
        .internalClusterRole(InternalClusterRole.NONE.toString())
        .isMTMSentinel(false)
        .diskWarmingMode(getDiskWarmingMode())
        .configServerType(getConfigServerType())
        .configServerManagementMode(getConfigServerManagementMode())
        .fixedMongoDBFCV(getFeatureCompatibilityVersion())
        .fixedMongoDBFCVExpiration(getFeatureCompatibilityVersionExpirationDate())
        .employeeAccessGrant(
            Optional.ofNullable(getEmployeeAccessGrant())
                .map(
                    apiView ->
                        new EmployeeAccessGrantView(
                            apiView.getGrantType(), apiView.getExpirationTime()))
                .orElse(null))
        .replicaSetScalingStrategy(getReplicaSetScalingStrategy());
  }

  // This method was created to support gp2 -> gp3 and io1 -> io2 migration because they are
  // mapped to STANDARD AND PROVISIONED respectively.
  // If an existing cluster is already gp2 then it should remain gp2.  Same goes for io1
  private VolumeType getBackwardsCompatibleVolumeType(
      final ApiAtlasAWSProviderSettingsView pProviderSettingsView,
      final ClusterDescriptionView pExistingClusterDescriptionView) {
    if (ApiAtlasAWSVolumeTypeView.STANDARD.equals(pProviderSettingsView.getAWSVolumeType())
        && pExistingClusterDescriptionView != null
        && (VolumeType.Gp3.equals(pExistingClusterDescriptionView.getEBSVolumeType())
            || VolumeType.Gp2.equals(pExistingClusterDescriptionView.getEBSVolumeType()))) {
      return pExistingClusterDescriptionView.getEBSVolumeType();
    }

    if (ApiAtlasAWSVolumeTypeView.PROVISIONED.equals(pProviderSettingsView.getAWSVolumeType())
        && pExistingClusterDescriptionView != null
        && (VolumeType.Io2.equals(pExistingClusterDescriptionView.getEBSVolumeType())
            || VolumeType.Io1.equals(pExistingClusterDescriptionView.getEBSVolumeType()))) {
      return pExistingClusterDescriptionView.getEBSVolumeType();
    }
    return Optional.ofNullable(pProviderSettingsView.getAWSVolumeType())
        .map(ApiAtlasAWSVolumeTypeView::getVolumeTypeView)
        .map(VolumeTypeView::getVolumeType)
        .get();
  }

  private ClusterDescriptionView toAzureClusterDescriptionView() throws SvcException {
    final ClusterDescriptionViewBuilder builder = ClusterDescriptionView.builder();
    final ApiAtlasAzureProviderSettingsView providerSettingsView =
        (ApiAtlasAzureProviderSettingsView) getProviderSettings();
    // note, the default provider specific auto-scaling view is initialized with the current
    // min/max instance sizes for Azure. the default provider agnostic auto-scaling view initializes
    // disk auto-scaling to enabled and compute auto-scaling to disabled
    final AzureAutoScaling autoScaling =
        (providerSettingsView.hasAutoScaling()
                ? providerSettingsView.getAutoScaling().toAutoScalingView(getAutoScaling())
                : ApiAtlasAzureAutoScalingView.getDefaultAutoScalingView()
                    .toAutoScalingView(getAutoScaling()))
            .toAutoScaling();

    setStandardClusterFields(builder);

    AzureDiskType diskType;
    if (providerSettingsView.getInstanceSize().getAzureInstanceSize().isNVMe()) {
      builder.diskSizeGB(
          Double.valueOf(
              providerSettingsView.getInstanceSize().getAzureInstanceSize().getNVMeDiskSizeGB()));
      diskType = providerSettingsView.getInstanceSize().getAzureInstanceSize().getDefaultDiskType();
    } else if (hasDiskSizeGB()) {
      final double diskSize = getDiskSizeGB();
      diskType = AzureDiskType.getApproximatePv1DiskType(diskSize);
      builder.diskSizeGB(diskSize);
    } else {
      diskType = providerSettingsView.getDiskType().getAzureDiskType();
      builder.diskSizeGB(Double.valueOf(diskType.getSizeGB()));
    }

    // needed to propagate disk iops and throughput
    diskType = _diskIOPS != null ? AzureDiskType.V2 : diskType;

    final AzureHardwareSpec azureHardwareSpec =
        new AzureHardwareSpec.Builder()
            .setInstanceSize(providerSettingsView.getInstanceSize().getAzureInstanceSize())
            .setDiskType(diskType)
            .setDiskIOPS(_diskIOPS)
            .build();

    final var replicationSpecViews = new ArrayList<ReplicationSpecView>();
    for (final var legacyReplicationSpecView : getReplicationSpecs()) {
      replicationSpecViews.add(
          legacyReplicationSpecView.toReplicationSpecViewFromLegacyRegionsConfig(
              CloudProvider.AZURE, azureHardwareSpec, autoScaling));
    }
    builder.replicationSpecList(replicationSpecViews);

    builder.acceptDataRisksAndForceReplicaSetReconfig(
        getAcceptDataRisksAndForceReplicaSetReconfig());

    builder.cloudProvider(CloudProvider.PROVIDER_AZURE);
    builder.tenantAccessRevokedForPause(false);
    builder.underCompaction(false);
    builder.hostnameSubdomainLevel("");
    builder.hostnameSchemeForAgents("");

    return builder.build();
  }

  private ClusterDescriptionView toGCPClusterDescriptionView() throws SvcException {
    final ClusterDescriptionViewBuilder builder = ClusterDescriptionView.builder();
    final ApiAtlasGCPProviderSettingsView providerSettingsView =
        (ApiAtlasGCPProviderSettingsView) getProviderSettings();

    // note, the default provider specific auto-scaling view is initialized with the current
    // min/max instance sizes for GCP. the default provider agnostic auto-scaling view initializes
    // disk auto-scaling to enabled and compute auto-scaling to disabled
    final GCPAutoScaling autoScaling =
        (providerSettingsView.hasAutoScaling()
                ? providerSettingsView.getAutoScaling().toAutoScalingView(getAutoScaling())
                : ApiAtlasGCPAutoScalingView.getDefaultAutoScalingView()
                    .toAutoScalingView(getAutoScaling()))
            .toAutoScaling();

    builder.diskSizeGB(getDiskSizeGB());

    setStandardClusterFields(builder);

    // Provider settings
    final GCPHardwareSpec gcpHardwareSpec =
        new GCPHardwareSpec.Builder()
            .setInstanceSize(providerSettingsView.getInstanceSize().getGCPInstanceSize())
            .setDiskIOPS(_diskIOPS)
            .setDiskThroughput(_diskThroughput)
            .build();
    final var replicationSpecViews = new ArrayList<ReplicationSpecView>();
    for (final var legacyReplicationSpecView : getReplicationSpecs()) {
      replicationSpecViews.add(
          legacyReplicationSpecView.toReplicationSpecViewFromLegacyRegionsConfig(
              CloudProvider.GCP, gcpHardwareSpec, autoScaling));
    }
    builder.replicationSpecList(replicationSpecViews);

    builder.acceptDataRisksAndForceReplicaSetReconfig(
        getAcceptDataRisksAndForceReplicaSetReconfig());

    builder.cloudProvider(CloudProvider.PROVIDER_GCP);
    builder.tenantAccessRevokedForPause(false);
    builder.underCompaction(false);
    builder.hostnameSubdomainLevel("");
    builder.hostnameSchemeForAgents("");

    return builder.build();
  }

  private ClusterDescriptionView toFreeClusterDescriptionView() throws SvcException {
    ClusterDescriptionViewBuilder builder = ClusterDescriptionView.builder();
    final ApiAtlasFreeProviderSettingsView providerSettingsView =
        (ApiAtlasFreeProviderSettingsView) getProviderSettings();

    builder.diskSizeGB(getDiskSizeGB());

    setStandardClusterFields(builder);

    final CloudProvider backingCloudProvider =
        CloudProvider.valueOf(providerSettingsView.getBackingProviderName().name());

    // Note, the default provider specific auto-scaling view is initialized to null for Free. The
    // default provider agnostic auto-scaling view initializes disk auto-scaling to disabled (note,
    // this is different from the other providers except Serverless) and compute auto-scaling to
    // disabled. Currently, auto-scaling is not supported by the Free provider (this is enforced in
    // multiple validation paths). This provides parity with the other providers and supports
    // enabling this feature in the future for the Free provider.
    final FreeAutoScaling autoScaling =
        (providerSettingsView.hasAutoScaling()
                ? providerSettingsView.getAutoScaling().toAutoScalingView(getAutoScaling())
                : ApiAtlasFreeAutoScalingView.getDefaultAutoScalingView()
                    .toAutoScalingView(getAutoScaling()))
            .toAutoScaling();

    final FreeInstanceSize instanceSize =
        providerSettingsView.getInstanceSize().getFreeInstanceSize();
    final FreeHardwareSpec freeHardwareSpec =
        new FreeHardwareSpec.Builder()
            .setInstanceSize(instanceSize)
            .setBackingCloudProvider(backingCloudProvider)
            .build();

    final FreeInstanceSize.LimitsProfile limitsProfile = FreeInstanceSize.LimitsProfile.NORMAL;
    final FreeTenantProviderOptions options =
        new FreeTenantProviderOptions.Builder()
            .setLimitsProfile(limitsProfile)
            .setConnectionLimit(instanceSize.getMaxIncomingConnections(limitsProfile))
            .setOperationsPerSecondLimit(instanceSize.getMaxOperationsPerSecond(limitsProfile))
            .setDatabaseLimit(instanceSize.getMaxDatabases())
            .setCollectionLimit(instanceSize.getMaxCollections())
            .setGBPerWeekInLimit(instanceSize.getMaxGBPerWeekIn())
            .setGBPerWeekOutLimit(instanceSize.getMaxGBPerWeekOut(limitsProfile))
            .setThrottledKBPerSecondLimit(instanceSize.getThrottledKBPerSecond())
            .setQueryUtilizationBucketWidthSeconds(
                instanceSize.getQueryUtilizationBucketWidthSeconds())
            .setQueryUtilizationWindowLengthSeconds(
                instanceSize.getQueryUtilizationWindowLengthSeconds())
            .setQueryUtilizationTimeThreshold(instanceSize.getQueryUtilizationTimeThreshold())
            .setQueryUtilizationMaxSleepTimeSeconds(
                instanceSize.getQueryUtilizationMaxSleepTimeSeconds())
            .build();

    builder.cloudProviderOptions(
        ClusterDescriptionView.TenantOptionsView.fromFreeTenantProviderOptions(options));

    final var replicationSpecViews = new ArrayList<ReplicationSpecView>();
    for (final var legacyReplicationSpecView : getReplicationSpecs()) {
      replicationSpecViews.add(
          legacyReplicationSpecView.toReplicationSpecViewFromLegacyRegionsConfig(
              CloudProvider.FREE, freeHardwareSpec, autoScaling));
    }
    builder.replicationSpecList(replicationSpecViews);
    builder.cloudProvider(CloudProvider.PROVIDER_FREE);
    builder.tenantAccessRevokedForPause(false);
    builder.underCompaction(false);
    builder.hostnameSubdomainLevel("");
    builder.hostnameSchemeForAgents("");

    return builder.build();
  }

  private ClusterDescriptionView toFlexClusterDescriptionView() throws SvcException {
    ClusterDescriptionViewBuilder builder = ClusterDescriptionView.builder();
    final ApiAtlasFlexProviderSettingsView providerSettingsView =
        (ApiAtlasFlexProviderSettingsView) getProviderSettings();

    builder.diskSizeGB(getDiskSizeGB());

    setStandardClusterFields(builder);

    final CloudProvider backingCloudProvider =
        CloudProvider.valueOf(providerSettingsView.getBackingProviderName().name());

    final FlexInstanceSize instanceSize =
        providerSettingsView.getInstanceSize().getFlexInstanceSize();
    final FlexHardwareSpec flexHardwareSpec =
        new FlexHardwareSpec.Builder()
            .setInstanceSize(instanceSize)
            .setBackingCloudProvider(backingCloudProvider)
            .build();

    final Date now = new Date();
    final FlexTenantProviderOptions options =
        new FlexTenantProviderOptions.Builder()
            .setConnectionLimit(instanceSize.getMaxIncomingConnections().orElseThrow())
            .setCollectionLimit(instanceSize.getMaxCollections())
            .setOperationsPerSecondLimit(instanceSize.getMaxOperationsPerSecond())
            .setDatabaseLimit(instanceSize.getMaxDatabases())
            .setDiskSizeGBLimit(instanceSize.getDefaultDiskSizeGB())
            .setNextBackupDate(DateUtils.addHours(now, 24))
            .build();

    builder.cloudProviderOptions(
        ClusterDescriptionView.TenantOptionsView.fromFlexTenantProviderOptions(options));
    final var replicationSpecViews = new ArrayList<ReplicationSpecView>();
    for (final var legacyReplicationSpecView : getReplicationSpecs()) {
      replicationSpecViews.add(
          legacyReplicationSpecView.toReplicationSpecViewFromLegacyRegionsConfig(
              CloudProvider.FLEX, flexHardwareSpec, null));
    }
    builder.replicationSpecList(replicationSpecViews);
    builder.cloudProvider(CloudProvider.PROVIDER_FLEX);

    return builder.build();
  }

  public boolean isServerless() {
    // For this view, this value should always be false.
    // We have this method to perform validations in the Clusters resource that the API
    // is not being used for serverless.
    return hasProviderSettings()
        && getProviderSettings().getProviderName().equals(CloudProvider.SERVERLESS);
  }

  public boolean isFreeTier() {
    return hasProviderSettings()
        && getProviderSettings().getProviderName().equals(CloudProvider.FREE);
  }

  public boolean isSharedTenant() {
    if (isFreeTier()) {
      final ApiAtlasFreeProviderSettingsView providerSettingsView =
          (ApiAtlasFreeProviderSettingsView) getProviderSettings();
      return providerSettingsView.getInstanceSize().getFreeInstanceSize().isPaidTenant();
    }
    return false;
  }

  public boolean isFlexTier() {
    return hasProviderSettings()
        && getProviderSettings().getProviderName().equals(CloudProvider.FLEX);
  }

  public boolean isTenantCluster() {
    return isFreeTier() || isServerless() || isFlexTier();
  }

  public boolean hasNumShards() {
    return _numShards != null;
  }

  public Integer getNumShards() {
    return _numShards;
  }

  public Optional<Integer> getSpecifiedNumAnalyticsNodes() {
    if (hasReplicationSpecs()) {
      return Optional.of(
          getReplicationSpecs().stream()
              .flatMap(rs -> rs.getRegionsConfig().values().stream())
              .map(ApiAtlasRegionSpecView::getAnalyticsNodes)
              .reduce(0, Integer::sum));
    } else if (hasReplicationSpec()) {
      return Optional.of(getReplicationSpec().getAnalyticsNodes());
    } else {
      return Optional.empty();
    }
  }

  public boolean hasReplicationFactor() {
    return _replicationFactor != null;
  }

  public Integer getReplicationFactor() {
    return _replicationFactor;
  }

  public boolean hasReplicationSpecs() {
    return _replicationSpecs != null;
  }

  public List<ApiAtlasLegacyReplicationSpecView> getReplicationSpecs() {
    return _replicationSpecs;
  }

  public boolean hasLabels() {
    return _labels != null;
  }

  public List<ApiAtlasNDSLabelView> getLabels() {
    return _labels;
  }

  public boolean hasRootCertType() {
    return _rootCertType != null;
  }

  public RootCertType getRootCertType() {
    return _rootCertType;
  }

  public boolean hasVersionReleaseSystem() {
    return _versionReleaseSystem != null;
  }

  public VersionReleaseSystem getVersionReleaseSystem() {
    return _versionReleaseSystem;
  }

  public Date getAcceptDataRisksAndForceReplicaSetReconfig() {
    return _acceptDataRisksAndForceReplicaSetReconfig;
  }

  public boolean hasReplicationSpec() {
    return _replicationSpec != null;
  }

  public double getDiskSizeGB() {
    return _diskSizeGB;
  }

  public boolean hasDiskSizeGB() {
    return _diskSizeGB != null;
  }

  public String getMongoURI() {
    return _mongoURI;
  }

  public boolean hasMongoURI() {
    return _mongoURI != null;
  }

  public String getMongoURIWithOptions() {
    return _mongoURIWithOptions;
  }

  public boolean hasMongoURIWithOptions() {
    return _mongoURIWithOptions != null;
  }

  public Date getMongoURIUpdated() {
    return _mongoURIUpdated;
  }

  public String getSRVAddress() {
    return _srvAddress;
  }

  public DiskWarmingMode getDiskWarmingMode() {
    return _diskWarmingMode;
  }

  public boolean hasDiskWarmingMode() {
    return _diskWarmingMode != null;
  }

  public ConfigServerType getConfigServerType() {
    return _configServerType;
  }

  public boolean hasConfigServerType() {
    return _configServerType != null;
  }

  public ConfigServerManagementMode getConfigServerManagementMode() {
    return _configServerManagementMode;
  }

  public boolean hasConfigServerManagementMode() {
    return _configServerManagementMode != null;
  }

  public Boolean getSelfManagedSharding() {
    return _selfManagedSharding;
  }

  public boolean isSelfManagedSharding() {
    return _selfManagedSharding == Boolean.TRUE;
  }

  public ApiAtlasEmployeeAccessGrantView getEmployeeAccessGrant() {
    return _employeeAccessGrant;
  }

  public ReplicaSetScalingStrategy getReplicaSetScalingStrategy() {
    return _replicaSetScalingStrategy;
  }

  public Optional<ApiAtlasClusterAdvancedConfigurationView> getAdvancedConfiguration() {
    return Optional.ofNullable(_advancedConfiguration);
  }

  public boolean hasReplicaSetScalingStrategy() {
    return _replicaSetScalingStrategy != null;
  }

  @Override
  @JsonProperty(
      value = ApiAtlasBaseClusterDescriptionView.Fields.CONNECTION_STRINGS_FIELD,
      access = Access.READ_ONLY)
  @SuppressWarnings("SwaggerSchemaDescriptionNotAllowedChecker")
  @Schema(
      description =
          "List of connection strings that your applications use to connect to this cluster. "
              + "Use the parameters in this object to connect your applications to this cluster. "
              + "This resource returns these values only after MongoDB builds the cluster.",
      accessMode = Schema.AccessMode.READ_ONLY,
      externalDocs =
          @ExternalDocumentation(
              description = "Connection string URI format.",
              url = "https://docs.mongodb.com/manual/reference/connection-string/"))
  public ApiAtlasClusterDescriptionConnectionStringsView getConnectionStrings() {
    return (ApiAtlasClusterDescriptionConnectionStringsView) super.getConnectionStrings();
  }

  @Override
  @JsonProperty(ApiAtlasBaseClusterDescriptionView.Fields.MONGODB_VERSION_FIELD)
  @Schema(
      description = "Version of MongoDB that the cluster runs.",
      example = TEST_MONGODB_VERSION,
      accessMode = Schema.AccessMode.READ_WRITE,
      pattern = OpenApiConst.MONGODB_VERSION_REGEX,
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Schema predates IPA validation.")
            })
      })
  public String getMongoDBVersion() {
    return super.getMongoDBVersion();
  }

  @JsonGetter(Fields.MONGODB_MAJOR_VERSION_FIELD)
  public String getMongoDBMajorVersionForResponse() {
    return _featureCompatibilityVersion;
  }

  @JsonGetter(Fields.FEATURE_COMPATIBILITY_VERSION)
  public String getFeatureCompatibilityVersionForResponse() {
    if (!getIsFixedFCV()) {
      return null;
    }
    return _featureCompatibilityVersion;
  }

  @Override
  @JsonProperty(ApiAtlasBaseClusterDescriptionView.Fields.NAME_FIELD)
  @Schema(
      description = "Human-readable label that identifies the cluster.",
      accessMode = Schema.AccessMode.READ_WRITE,
      pattern = OpenApiConst.CLUSTER_NAME_REGEX)
  public String getName() {
    return super.getName();
  }

  @Override
  @JsonProperty(ApiAtlasBaseClusterDescriptionView.Fields.CREATE_DATE_FIELD)
  @Schema(
      description =
          "Date and time when MongoDB Cloud created this serverless instance. MongoDB Cloud"
              + " represents this timestamp in ISO 8601 format in UTC.",
      accessMode = Schema.AccessMode.READ_ONLY)
  public Date getCreateDate() {
    return super.getCreateDate();
  }

  @Override
  @JsonProperty(ApiAtlasBaseClusterDescriptionView.Fields.ID_FIELD)
  @Schema(
      description = "Unique 24-hexadecimal digit string that identifies the cluster.",
      accessMode = Schema.AccessMode.READ_ONLY)
  public ObjectId getId() {
    return super.getId();
  }

  @Override
  @JsonProperty(ApiAtlasBaseClusterDescriptionView.Fields.STATE_NAME_FIELD)
  @Schema(
      implementation = ApiAtlasClusterDescriptionStateView.class,
      accessMode = Schema.AccessMode.READ_ONLY)
  public ApiAtlasClusterDescriptionStateView getStateName() {
    return super.getStateName();
  }

  @Override
  @JsonProperty(ApiAtlasBaseClusterDescriptionView.Fields.TERMINATION_PROTECTION_ENABLED_FIELD)
  @Schema(
      description =
          "Flag that indicates whether termination protection is enabled on the cluster. If set to"
              + " `true`, MongoDB Cloud won't delete the cluster. If set to `false`, MongoDB Cloud"
              + " will delete the cluster.",
      accessMode = Schema.AccessMode.READ_WRITE,
      defaultValue = "false")
  public Boolean getTerminationProtectionEnabled() {
    return super.getTerminationProtectionEnabled();
  }

  @Override
  @JsonProperty(
      value = ApiAtlasBaseClusterDescriptionView.Fields.TAGS_FIELD,
      access = Access.READ_WRITE)
  @ArraySchema(
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-124-array-max-items",
                  value = "Schema predates IPA validation."),
            })
      },
      arraySchema =
          @Schema(
              description =
                  "List that contains key-value pairs between 1 to 255 characters in length for"
                      + " tagging and categorizing the cluster.",
              externalDocs =
                  @ExternalDocumentation(
                      description = "Resource Tags",
                      url = "https://dochub.mongodb.org/core/add-cluster-tag-atlas"),
              accessMode = Schema.AccessMode.READ_WRITE))
  public List<ApiAtlasTagView> getTags() {
    return super.getTags();
  }

  public boolean hasMongoURIUpdated() {
    return _mongoURIUpdated != null;
  }

  /**
   * @return false if the 'backup' field is null; true if the 'backup' field is not-null and has a
   *     non-null value (including NONE)
   */
  public boolean getBackupEnabled() {
    return _backupEnabled == Boolean.TRUE;
  }

  private boolean hasTheFieldNamedBackupEnabled() {
    return _backupEnabled != null;
  }

  public boolean getProviderBackupEnabled() {
    return _providerBackupEnabled == Boolean.TRUE;
  }

  private boolean hasTheFieldNamedProviderBackupEnabled() {
    return _providerBackupEnabled != null;
  }

  public boolean getPitEnabled() {
    return _pitEnabled == Boolean.TRUE;
  }

  private boolean hasTheFieldNamedPitEnabled() {
    return _pitEnabled != null;
  }

  public ApiAtlasClusterProviderSettingsView getProviderSettings() {
    return _providerSettings;
  }

  private boolean hasTheFieldNamedAcceptDataRisks() {
    return _acceptDataRisksAndForceReplicaSetReconfig != null;
  }

  public boolean hasProviderSettings() {
    return _providerSettings != null;
  }

  private void setProviderSettings(final ApiAtlasClusterProviderSettingsView pProviderSettings) {
    _providerSettings = pProviderSettings;
  }

  public String getMongoDBMajorVersion() {
    return _mongoDBMajorVersion;
  }

  public boolean hasMongoDBMajorVersion() {
    return _mongoDBMajorVersion != null;
  }

  public String getFeatureCompatibilityVersion() {
    return _featureCompatibilityVersion;
  }

  public Date getFeatureCompatibilityVersionExpirationDate() {
    return _featureCompatibilityVersionExpirationDate;
  }

  public Boolean getIsFixedFCV() {
    return _isFixedFCV;
  }

  public boolean hasAutoScaling() {
    return _autoScaling != null;
  }

  public ApiAtlasAutoScalingView getAutoScaling() {
    return _autoScaling;
  }

  public boolean hasBiConnector() {
    return _biConnector != null;
  }

  public ApiAtlasBiConnectorView getBiConnector() {
    return _biConnector;
  }

  public boolean getPaused() {
    return _paused != null ? _paused : false;
  }

  public boolean hasPaused() {
    return _paused != null;
  }

  public boolean hasClusterType() {
    return _clusterType != null;
  }

  public boolean hasValidClusterType() {
    if (!hasClusterType()) {
      return false;
    }

    try {
      ClusterDescription.ClusterType.valueOf(getClusterType());
    } catch (final IllegalArgumentException pE) {
      return false;
    }

    return true;
  }

  public String getClusterType() {
    return _clusterType;
  }

  public boolean hasEncryptionAtRestProvider() {
    return _encryptionAtRestProvider != null;
  }

  public ApiAtlasEncryptionAtRestProviderView getEncryptionAtRestProvider() {
    return _encryptionAtRestProvider;
  }

  public boolean isGeoSharded() {
    return getClusterDescriptionClusterType() == ClusterDescription.ClusterType.GEOSHARDED;
  }

  public boolean isSharded() {
    if (hasClusterType()) {
      String clusterType = getClusterType();
      return clusterType.equals(ClusterType.SHARDED.toString())
          || clusterType.equals(ClusterType.GEOSHARDED.toString());
    }
    return false;
  }

  public boolean hasGeoSharding() {
    return _geoSharding != null;
  }

  public ApiAtlasGeoShardingView getGeoSharding() {
    return _geoSharding;
  }

  public Builder toBuilder() {
    return new Builder(this);
  }

  public static class Builder extends ApiAtlasBaseClusterDescriptionView.Builder<Builder> {
    private String _mongoDBMajorVersion;
    private String _featureCompatibilityVersion;
    private Date _featureCompatibilityVersionExpirationDate;

    private final Boolean _isFixedFCV;
    private List<ApiAtlasLegacyReplicationSpecView> _replicationSpecs;
    private Double _diskSizeGB;
    private String _mongoURI;
    private String _mongoURIWithOptions;
    private Date _mongoURIUpdated;
    private String _srvAddress;
    private Boolean _backupEnabled;
    private Boolean _providerBackupEnabled;
    private Boolean _pitEnabled;
    private ApiAtlasAutoScalingView _autoScaling;
    private ApiAtlasBiConnectorView _biConnector;
    private Boolean _paused;
    private String _clusterType;
    private ApiAtlasEncryptionAtRestProviderView _encryptionAtRestProvider;
    private ApiAtlasGeoShardingView _geoSharding;
    private Integer _replicationFactor;
    private ApiAtlasLegacyRegionsConfigView _replicationSpec;
    private ApiAtlasClusterProviderSettingsView _providerSettings;
    private Integer _numShards;
    private List<ApiAtlasNDSLabelView> _labels;
    private RootCertType _rootCertType;
    private VersionReleaseSystem _versionReleaseSystem;
    private DiskWarmingMode _diskWarmingMode;
    private ConfigServerType _configServerType;
    private ConfigServerManagementMode _configServerManagementMode;
    private Boolean _selfManagedSharding;
    private ApiAtlasEmployeeAccessGrantView _employeeAccessGrant;
    private ReplicaSetScalingStrategy _replicaSetScalingStrategy;
    private ApiAtlasClusterAdvancedConfigurationView _advancedConfiguration;

    public Builder(final ApiAtlasLegacyClusterDescriptionView pExisting) {
      super(pExisting);
      _mongoDBMajorVersion = pExisting._mongoDBMajorVersion;
      _featureCompatibilityVersion = pExisting._featureCompatibilityVersion;
      _featureCompatibilityVersionExpirationDate =
          pExisting._featureCompatibilityVersionExpirationDate;
      _isFixedFCV = pExisting._isFixedFCV;
      _replicationSpecs = pExisting._replicationSpecs;
      _diskSizeGB = pExisting._diskSizeGB;
      _mongoURI = pExisting._mongoURI;
      _mongoURIWithOptions = pExisting._mongoURIWithOptions;
      _mongoURIUpdated = pExisting._mongoURIUpdated;
      _srvAddress = pExisting._srvAddress;
      _backupEnabled = pExisting._backupEnabled;
      _providerBackupEnabled = pExisting._providerBackupEnabled;
      _pitEnabled = pExisting._pitEnabled;
      _autoScaling = pExisting._autoScaling;
      _biConnector = pExisting._biConnector;
      _paused = pExisting._paused;
      _clusterType = pExisting._clusterType;
      _encryptionAtRestProvider = pExisting._encryptionAtRestProvider;
      _geoSharding = pExisting._geoSharding;
      _replicationFactor = pExisting._replicationFactor;
      _replicationSpec = pExisting._replicationSpec;
      _providerSettings = pExisting.getProviderSettings();
      _numShards = pExisting._numShards;
      _labels = pExisting._labels;
      _rootCertType = pExisting._rootCertType;
      _versionReleaseSystem = pExisting._versionReleaseSystem;
      _diskWarmingMode = pExisting._diskWarmingMode;
      _configServerType = pExisting._configServerType;
      _configServerManagementMode = pExisting._configServerManagementMode;
      _selfManagedSharding = pExisting._selfManagedSharding;
      _employeeAccessGrant = pExisting._employeeAccessGrant;
      _replicaSetScalingStrategy = pExisting._replicaSetScalingStrategy;
      _advancedConfiguration = pExisting._advancedConfiguration;
    }

    public Builder mongoDBMajorVersion(final String pMongoDBMajorVersion) {
      _mongoDBMajorVersion = pMongoDBMajorVersion;
      return self();
    }

    public Builder featureCompatibilityVersion(final String pFeatureCompatibilityVersion) {
      _featureCompatibilityVersion = pFeatureCompatibilityVersion;
      return self();
    }

    public Builder featureCompatibilityVersionExpirationDate(
        final Date pFeatureCompatibilityVersionExpirationDate) {
      _featureCompatibilityVersionExpirationDate = pFeatureCompatibilityVersionExpirationDate;
      return self();
    }

    public Builder replicationSpecs(
        final List<ApiAtlasLegacyReplicationSpecView> pReplicationSpecs) {
      _replicationSpecs = pReplicationSpecs;
      return self();
    }

    public Builder diskSizeGB(final Double pDiskSizeGB) {
      _diskSizeGB = pDiskSizeGB;
      return self();
    }

    public Builder mongoURI(final String pMongoURI) {
      _mongoURI = pMongoURI;
      return self();
    }

    public Builder mongoURIWithOptions(final String pMongoURIWithOptions) {
      _mongoURIWithOptions = pMongoURIWithOptions;
      return self();
    }

    public Builder mongoURIUpdated(final Date pMongoURIUpdated) {
      _mongoURIUpdated = pMongoURIUpdated;
      return self();
    }

    public Builder srvAddress(final String pSrvAddress) {
      _srvAddress = pSrvAddress;
      return self();
    }

    public Builder backupEnabled(final Boolean pBackupEnabled) {
      _backupEnabled = pBackupEnabled;
      return self();
    }

    public Builder providerBackupEnabled(final Boolean pProviderBackupEnabled) {
      _providerBackupEnabled = pProviderBackupEnabled;
      return self();
    }

    public Builder pitEnabled(final Boolean pPitEnabled) {
      _pitEnabled = pPitEnabled;
      return self();
    }

    public Builder autoScaling(final ApiAtlasAutoScalingView pAutoScaling) {
      _autoScaling = pAutoScaling;
      return self();
    }

    public Builder biConnector(final ApiAtlasBiConnectorView pBiConnector) {
      _biConnector = pBiConnector;
      return self();
    }

    public Builder paused(final Boolean pPaused) {
      _paused = pPaused;
      return self();
    }

    public Builder clusterType(final String pClusterType) {
      _clusterType = pClusterType;
      return self();
    }

    public Builder encryptionAtRestProvider(
        final ApiAtlasEncryptionAtRestProviderView pEncryptionAtRestProvider) {
      _encryptionAtRestProvider = pEncryptionAtRestProvider;
      return self();
    }

    public Builder geoSharding(final ApiAtlasGeoShardingView pGeoSharding) {
      _geoSharding = pGeoSharding;
      return self();
    }

    public Builder replicationFactor(final Integer pReplicationFactor) {
      _replicationFactor = pReplicationFactor;
      return self();
    }

    public Builder replicationSpec(final ApiAtlasLegacyRegionsConfigView pReplicationSpec) {
      _replicationSpec = pReplicationSpec;
      return self();
    }

    public Builder providerSettings(final ApiAtlasClusterProviderSettingsView pProviderSettings) {
      _providerSettings = pProviderSettings;
      return self();
    }

    public Builder numShards(final Integer pNumShards) {
      _numShards = pNumShards;
      return self();
    }

    public Builder labels(final List<ApiAtlasNDSLabelView> pLabels) {
      _labels = pLabels;
      return self();
    }

    public Builder rootCertType(final RootCertType pRootCertType) {
      _rootCertType = pRootCertType;
      return self();
    }

    public Builder versionReleaseSystem(final VersionReleaseSystem pVersionReleaseSystem) {
      _versionReleaseSystem = pVersionReleaseSystem;
      return self();
    }

    public Builder diskWarmingMode(final DiskWarmingMode pDiskWarmingMode) {
      _diskWarmingMode = pDiskWarmingMode;
      return self();
    }

    public Builder configServerType(final ConfigServerType pConfigServerType) {
      _configServerType = pConfigServerType;
      return self();
    }

    public Builder configServerManagementMode(
        final ConfigServerManagementMode pConfigServerManagementMode) {
      _configServerManagementMode = pConfigServerManagementMode;
      return self();
    }

    public Builder selfManagedSharding(final Boolean pSelfManagedSharding) {
      _selfManagedSharding = pSelfManagedSharding;
      return self();
    }

    public Builder employeeAccessGrant(final ApiAtlasEmployeeAccessGrantView pEmployeeAccessGrant) {
      _employeeAccessGrant = pEmployeeAccessGrant;
      return self();
    }

    public Builder replicaSetScalingStrategy(
        final ReplicaSetScalingStrategy pReplicaSetScalingStrategy) {
      _replicaSetScalingStrategy = pReplicaSetScalingStrategy;
      return self();
    }

    public Builder advancedConfiguration(
        final ApiAtlasClusterAdvancedConfigurationView pAdvancedConfiguration) {
      _advancedConfiguration = pAdvancedConfiguration;
      return self();
    }

    @Override
    public ApiAtlasLegacyClusterDescriptionView build() {
      return new ApiAtlasLegacyClusterDescriptionView(
          getId(),
          getName(),
          getGroupId(),
          getMongoDBVersion(),
          _mongoDBMajorVersion,
          _featureCompatibilityVersion,
          _featureCompatibilityVersionExpirationDate,
          _isFixedFCV,
          _replicationSpecs,
          _diskSizeGB,
          _mongoURI,
          _mongoURIWithOptions,
          _mongoURIUpdated,
          _srvAddress,
          (ApiAtlasClusterDescriptionConnectionStringsView) getConnectionStrings(),
          _backupEnabled,
          _providerBackupEnabled,
          _pitEnabled,
          getStateName(),
          _providerSettings,
          _autoScaling,
          _biConnector,
          _paused,
          _clusterType,
          _encryptionAtRestProvider,
          _geoSharding,
          _replicationFactor,
          _replicationSpec,
          _numShards,
          _labels,
          _rootCertType,
          _versionReleaseSystem,
          isTerminationProtectionEnabled(),
          _diskWarmingMode,
          _configServerType,
          _configServerManagementMode,
          _selfManagedSharding,
          _employeeAccessGrant,
          _replicaSetScalingStrategy,
          _advancedConfiguration);
    }

    @Override
    protected Builder self() {
      return this;
    }
  }

  // Helper method to validate the GCP region name enum
  private ApiAtlasGCPRegionNameView getValidatedGCPRegionNameView(String regionKey) {
    try {
      GCPRegionName gcpRegionName = GCPRegionName.valueOf(regionKey);
      // Validate that this is a valid enum by casting
      return ApiAtlasGCPRegionNameView.valueOf(gcpRegionName);
    } catch (IllegalArgumentException | NullPointerException e) {
      throw new UncheckedSvcException(
          NDSErrorCode.INVALID_REGION, regionKey, CloudProvider.GCP.name());
    }
  }
}
