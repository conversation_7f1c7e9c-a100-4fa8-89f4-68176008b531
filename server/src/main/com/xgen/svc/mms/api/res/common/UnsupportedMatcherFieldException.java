package com.xgen.svc.mms.api.res.common;

import static java.lang.String.format;

public class UnsupportedMatcherFieldException extends IllegalArgumentException {
  public static final String UNSUPPORTED_MATCHER_FIELD_MSG =
      "The provided matcher field %s was not supported by this alert type.";

  private final String fieldName;

  public UnsupportedMatcherFieldException(String fieldName) {
    super(format(UNSUPPORTED_MATCHER_FIELD_MSG, fieldName));
    this.fieldName = fieldName;
  }

  public String getFieldName() {
    return fieldName;
  }
}
