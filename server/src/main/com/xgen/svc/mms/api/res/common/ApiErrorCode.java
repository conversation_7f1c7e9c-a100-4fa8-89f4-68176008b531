package com.xgen.svc.mms.api.res.common;

import static com.xgen.svc.mms.api.res.common.UnsupportedMatcherFieldException.UNSUPPORTED_MATCHER_FIELD_MSG;
import static com.xgen.svc.mms.api.res.common.UnsupportedMatcherValueException.UNSUPPORTED_MATCHER_VALUE_MSG;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.xgen.cloud.common.model._public.error.ErrorCode;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import java.util.Arrays;
import java.util.IllegalFormatException;
import java.util.List;
import java.util.Optional;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Use via {@code ApiErrorCode.<ENUM>.response(envelop)} or {@code throw
 * ApiErrorCode.<ENUM>.exception(envelop)}, doesn't use directly as a response value.
 *
 * <p>Note: try to reuse existing codes before adding your own, for example prefer the generic
 * {@code RESOURCE_NOT_FOUND} over a new {@code <MY_RESOURCE>_NOT_FOUND}
 */
@Schema(description = "Application error code returned with this error.")
public enum ApiErrorCode implements ErrorCode {
  // HTTP 400 - Bad Request
  MALFORMED_JSON(HttpServletResponse.SC_BAD_REQUEST, "Received JSON is malformed."),
  VALIDATION_ERROR(
      HttpServletResponse.SC_BAD_REQUEST, "The request content produced the validation error: %s."),
  EXPORT_BUCKET_UNSUPPORTED_CLOUD_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST,
      "The export bucket cloud provider is unsupported for this operation."),
  BAD_REQUEST(
      HttpServletResponse.SC_BAD_REQUEST, "The request content produced validation errors."),
  INVALID_JSON(HttpServletResponse.SC_BAD_REQUEST, "Received JSON does not match expected format."),
  INVALID_JSON_ATTRIBUTE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Received JSON for the %s attribute does not match expected format."),
  INVALID_PARAMETER(HttpServletResponse.SC_BAD_REQUEST, "Invalid Parameter: %s."),
  PATH_PARAM_PARSE_ERROR(
      HttpServletResponse.SC_BAD_REQUEST,
      "One or more path parameter in the request URI %s is malformed."),
  CHARTS_STATUS_NOT_UPDATED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Charts status for tenant %s is not updated with error: %s, please try again later."),
  INVALID_NOTIFIER_ID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Received an invalid notifierId for alert configuration integration."),
  INVALID_CHARTS_TENANT_ID(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid charts tenant id %s was specified."),
  INVALID_STREAM_TENANT_ID(
      HttpServletResponse.SC_BAD_REQUEST,
      "An invalid stream processing instance tenant id %s was specified."),
  INVALID_CHARTS_TENANT_STATUS(
      HttpServletResponse.SC_BAD_REQUEST,
      "An invalid charts tenant status value %s was specified."),
  INVALID_ENUM_VALUE(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid enumeration value %s was specified."),
  INVALID_DIRECTORY(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid directory name %s was specified."),
  INVALID_MOUNT_LOCATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "An invalid mount location %s was specified. The mount location must be equal to or a parent"
          + " of %s."),
  INVALID_REGION(HttpServletResponse.SC_BAD_REQUEST, "No region %s exists for provider %s."),
  INVALID_RSYNC_REQUEST(
      HttpServletResponse.SC_BAD_REQUEST,
      "Conditions are not met for beginning Rsync Initial Sync."),
  INVALID_ZONE(HttpServletResponse.SC_BAD_REQUEST, "No zone %s exists for region %s."),
  INVALID_PROVIDER(HttpServletResponse.SC_BAD_REQUEST, "No provider %s exists."),
  INVALID_INSTANCE_SIZE(HttpServletResponse.SC_BAD_REQUEST, "No instance size %s for provider %s."),
  INVALID_AZURE_PV2_REGION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Azure Region %s currently does not support the newer disk type that supports higher IOPS,"
          + " incremental disk storage, and extended storage size. The available regions are: %s."),
  INVALID_AZURE_API_REQUEST(
      HttpServletResponse.SC_BAD_REQUEST, "One or more provided Azure parameters were invalid."),
  INVALID_ROLE_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Found invalid role in alert with event type %s, in notification type %s. See"
          + " https://www.mongodb.com/docs/atlas/reference/user-roles/ for available roles."),
  INVALID_ROLE_FOR_GROUP(HttpServletResponse.SC_BAD_REQUEST, "Role %s is invalid for group %s."),
  INVALID_ROLES_FOR_GROUP(HttpServletResponse.SC_BAD_REQUEST, "Roles %s are invalid for group %s."),
  INVALID_ROLE_FOR_ORG(HttpServletResponse.SC_BAD_REQUEST, "Role %s invalid for organization %s."),
  INVALID_ROLES_FOR_ORG(
      HttpServletResponse.SC_BAD_REQUEST, "Roles %s are invalid for organization %s."),
  INVALID_USER(HttpServletResponse.SC_BAD_REQUEST, "No user %s exists."),
  INVALID_DATABASE_NAME(HttpServletResponse.SC_BAD_REQUEST, "Invalid database name specified: %s."),
  INVALID_CLUSTER_NAMES(HttpServletResponse.SC_BAD_REQUEST, "Invalid cluster names specified: %s."),
  INVALID_UPGRADE_FOR_EIGHT_ZERO_ZERO(
      HttpServletResponse.SC_BAD_REQUEST,
      "Switching MongoDB Atlas 8.0.0 replica sets to sharded clusters is not permitted. Please wait"
          + " for version 8.0.1 in 2-3 weeks, or create a new sharded cluster on 8.0.0 if"
          + " applicable."),
  INVALID_SHARD_COUNT_FOR_SHARDING_UPGRADE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid shard count specified for %s. The cluster must first transition to a single sharded"
          + " cluster before additional shards can be added. To learn more, see:"
          + " https://docs.atlas.mongodb.com/reference/api/clusters-advanced/ or contact Support."),
  INVALID_COLLECTION_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid collection names specified: %s."),
  INVALID_NAMESPACE(
      HttpServletResponse.SC_BAD_REQUEST, "Database name + collection names too long: %s."),
  INVALID_PINNED_NAMESPACE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid namespaces were specified: %s. Cannot pin internal or malformed namespace."),
  INVALID_EMPTY_NAMESPACES_TO_UNPIN(
      HttpServletResponse.SC_BAD_REQUEST,
      "Namespaces parameter is empty. Please specify namespaces to unpin."),
  PINNED_NAMESPACES_FOR_CLUSTER_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "Group %s cluster %s has no pinned namespaces or does not contain all of the specified"
          + " namespaces."),
  PINNED_NAMESPACES_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The Pinned Namespaces limit would have been exceeded by that operation."),
  PINNED_NAMESPACES_NOT_UPDATED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Updating pinned namespaces failed. The operation did not return a valid document."),
  INVALID_DATE_FORMAT(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid date value %s was specified."),
  INVALID_DATE_FORMAT_FOR_FIELD(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid date value '%s' was specified for field '%s'."),
  INVALID_DATE_RANGE(
      HttpServletResponse.SC_BAD_REQUEST,
      "An invalid date range minDate=%s, maxDate=%s was specified: maxDate shouldn't be earlier"
          + " than minDate."),
  INVALID_TLS_VERSION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid TLS version specified. Valid options include: %s."),
  INVALID_SAMPLE_SIZE_BI_CONNECTOR(
      HttpServletResponse.SC_BAD_REQUEST,
      "Your BI Connector schema sample size must be greater than or equal to zero."),
  INVALID_SAMPLE_REFRESH_INTERVAL_BI_CONNECTOR(
      HttpServletResponse.SC_BAD_REQUEST,
      "Your BI Connector sample refresh interval must be greater than or equal to zero."),
  DEFAULT_WRITE_CONCERN_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Default Write Concern is only supported with MongoDB version 4.4 or higher."),
  DEFAULT_READ_CONCERN_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Default Read Concern is only supported with MongoDB version 4.4 or higher."),
  INVALID_DEFAULT_WRITE_CONCERN(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid Default Write Concern specified. Valid options include: %s."),
  INVALID_DEFAULT_READ_CONCERN(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid Default Read Concern specified. Valid options include: %s."),
  INVALID_GROUP_ROLE_UPDATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot update a project role for a user that will be removed from the organization."),
  BLOCKED_USERNAME(HttpServletResponse.SC_BAD_REQUEST, "The specified username %s is not allowed."),
  INVALID_SSH_KEY(HttpServletResponse.SC_BAD_REQUEST, "An invalid SSH key was specified."),
  MISSING_ATTRIBUTE(
      HttpServletResponse.SC_BAD_REQUEST, "The required attribute %s was not specified."),
  DUPLICATE_INTEGRATION_INFORMATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot specify both an integration id and separate credentials."),
  INTEGRATION_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "The specified integrationId %s does not correspond to an integration."),
  MISSING_OR_INVALID_ATTRIBUTE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The required attribute %s was incorrectly specified or omitted."),
  MISSING_ONE_OF_ATTRIBUTES(
      HttpServletResponse.SC_BAD_REQUEST,
      "Either the %s attribute or the %s attribute must be specified."),
  MISSING_ONE_OF_THREE_ATTRIBUTES(
      HttpServletResponse.SC_BAD_REQUEST,
      "Either the %s attribute, the %s attribute, or the %s attribute must be specified."),
  MISSING_ONE_OF_MANY_ATTRIBUTES(
      HttpServletResponse.SC_BAD_REQUEST,
      "One of the following required attributes may not have been specified: %s."),
  MISSING_ATTRIBUTES(
      HttpServletResponse.SC_BAD_REQUEST, "The required attributes %s were not specified."),
  MISSING_ROLE_ENTRY_IN_LDAP_MAPPING(
      HttpServletResponse.SC_BAD_REQUEST,
      "Missing %s role or missing its value in LDAP Group Mapping."),
  DUPLICATE_ROLE_ENTRY_IN_LDAP_MAPPING(
      HttpServletResponse.SC_BAD_REQUEST,
      "Each role name can only appear in one entry. %s was used more than once."),
  ATTRIBUTE_NEGATIVE(HttpServletResponse.SC_BAD_REQUEST, "The attribute %s cannot be negative."),
  ATTRIBUTE_NEGATIVE_OR_ZERO(
      HttpServletResponse.SC_BAD_REQUEST, "The attribute %s cannot be negative or zero."),
  INVALID_ATTRIBUTE(HttpServletResponse.SC_BAD_REQUEST, "Invalid attribute %s specified."),
  EXPIRED_X509_CA_CERTIFICATE(
      HttpServletResponse.SC_BAD_REQUEST, "The X.509 CA certificate has expired."),
  INVALID_X509_CERTIFICATE_TOKEN(
      HttpServletResponse.SC_BAD_REQUEST, "The X.509 certificate token is invalid."),
  INVALID_X509_CERTIFICATE_TOKEN_GROUP(
      HttpServletResponse.SC_BAD_REQUEST, "The X.509 certificate token group does not match."),
  INVALID_BI_CONNECTOR_READ_PREFERENCE_FOR_TOPOLOGY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Specifying the BI Connector Read Preference value \"analytics\" requires one or more"
          + " analytics nodes in the target cluster."),
  CLUSTER_PROVIDER_DOES_NOT_SUPPORT_BI_CONNECTOR(
      HttpServletResponse.SC_BAD_REQUEST,
      "The BI Connector is not supported with the specified cluster provider."),
  DISAGGREGATED_STORAGE_FEATURE_FLAG_MISSING(
      HttpServletResponse.SC_BAD_REQUEST, "Request is invalid due to missing feature flag."),
  DISALLOWED_ATTRIBUTE_CANNOT_UPDATE(
      HttpServletResponse.SC_BAD_REQUEST, "Updating attribute %s is not allowed."),
  DISALLOWED_ATTRIBUTE_TURN_ON_LDAP(
      HttpServletResponse.SC_BAD_REQUEST,
      "Attribute %s not allowed. To enable it, change authentication to LDAP in Ops Manager"
          + " Config."),
  MISSING_QUERY_PARAMETER(
      HttpServletResponse.SC_BAD_REQUEST, "The required query parameter %s was not specified."),
  INVALID_QUERY_PARAMETER(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid query parameter %s specified."),
  MUTUALLY_EXCLUSIVE_QUERY_PARAMETERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Either the %s query parameter or the %s query parameter but not both should be specified."),
  INVALID_ALERT_STATUS(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid alert status %s was specified."),
  GROUP_MISMATCH(
      HttpServletResponse.SC_BAD_REQUEST, "The specified group ID %s does not match the URL."),
  CANNOT_REMOVE_CALLER_FROM_ACCESS_LIST(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot remove caller's IP address %s from access list."),
  CANNOT_CLOSE_GROUP_ACTIVE_PEERING_CONNECTIONS(
      HttpServletResponse.SC_CONFLICT, "There are active peering connections in this project."),
  ROLE_NEEDS_NO_GROUP_ID(
      HttpServletResponse.SC_BAD_REQUEST, "Role %s cannot be specified with a group ID."),
  ROLE_NEEDS_GROUP_ID(HttpServletResponse.SC_BAD_REQUEST, "Role %s requires a group ID."),
  ROLE_NEEDS_NO_ORG_ID(
      HttpServletResponse.SC_BAD_REQUEST, "Role %s cannot be specified with an organization ID."),
  ROLE_NEEDS_ORG_ID(HttpServletResponse.SC_BAD_REQUEST, "Role %s requires an organization ID."),
  BAD_USERNAME_REF(HttpServletResponse.SC_BAD_REQUEST, "No user with username %s exists."),
  BAD_USERNAME_IN_GROUP_REF(HttpServletResponse.SC_BAD_REQUEST, "User %s is not in group %s."),
  INVALID_GRANULARITY(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid granularity %s was specified."),
  INVALID_PERIOD(HttpServletResponse.SC_BAD_REQUEST, "An invalid period was specified."),

  INVALID_INVOICE_ID(HttpServletResponse.SC_BAD_REQUEST, "An invalid invoice id was specified."),
  INVALID_CREDIT_ID(HttpServletResponse.SC_BAD_REQUEST, "An invalid credit id was specified."),
  INVALID_INVOICE_CREDIT_PAIR(
      HttpServletResponse.SC_BAD_REQUEST, "Credit does not apply to provided invoice."),

  INVALID_AGENT_TYPE_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid agent type name %s was specified."),
  INCOMPATIBLE_AGENT_TYPE_WITH_BASEURL(
      HttpServletResponse.SC_BAD_REQUEST, "Base URL not valid for agentType %s."),
  INVALID_AUTH_TYPE_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid authentication type name %s was specified."),
  MISSING_AUTH_ATTRIBUTES(
      HttpServletResponse.SC_BAD_REQUEST,
      "The attributes %s and %s must be specified for authentication type %s."),
  INCORRECT_PROVIDER_AUTH_CREDENTIALS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Incorrect account credentials were specified for provider %s."),
  NO_PROVIDER_SECURITY_GROUPS(
      HttpServletResponse.SC_BAD_REQUEST, "Could not retrieve security groups from %s account."),
  NO_PROVIDER_AVAILABILITY_ZONES(
      HttpServletResponse.SC_BAD_REQUEST, "Could not retrieve availability zones from %s account."),
  NO_PROVIDER_AVAILABLE_INSTANCE_TYPES(
      HttpServletResponse.SC_BAD_REQUEST,
      "Could not retrieve available instance types from %s account."),
  INVALID_PROVIDER_PARAMETERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid parameter combination specified for provider %s."),
  INCORRECT_SECURITY_GROUP_COUNT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Instance must be created with exactly one SSH-enabled security group."),
  INVALID_SECURITY_GROUP(
      HttpServletResponse.SC_BAD_REQUEST,
      "Security group %s is invalid. It must be one of the security groups returned in the machine"
          + " configuration options."),
  MACHINE_CONFIG_PARAMS_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST,
      "No machine configuration parameters exist for provider %s."),
  INVALID_MACHINE_IMAGE(
      HttpServletResponse.SC_BAD_REQUEST, "The specified machine image is invalid."),
  VOLUME_OPTIMIZATION_NOT_AVAILABLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Volume optimization is not available on instances of type %s."),
  VOLUME_ENCRYPTION_NOT_AVAILABLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Volume encryption is not available on instances of type %s."),
  INVALID_VOLUME_ENCRYPTION(
      HttpServletResponse.SC_BAD_REQUEST, "Volume encryption cannot be disabled on provider %s."),
  INVALID_IOPS_OUT_OF_BOUNDS(
      HttpServletResponse.SC_BAD_REQUEST,
      "The IOPS value %s is not valid. It must be between the minimum and maximum values returned"
          + " in the machine configuration options."),
  INVALID_IOPS_INVALID_RATIO(
      HttpServletResponse.SC_BAD_REQUEST,
      "The IOPS value %s is not valid. The maximum ratio between the IOPS value and the volume"
          + " size is 30 : 1."),
  INVALID_ROOT_VOLUME_SIZE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid root volume size %s. It must be between the minimum and maximum values returned in"
          + " the machine configuration options."),
  INVALID_INSTANCE_COUNT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid instance count %s. It must be between %s and %s."),
  CANNOT_DISTRIBUTE_SUBNETS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot distribute subnets. There must be at least one subnet available."),
  INVALID_VPC_OR_SUBNET(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid or unavailable VPC %s or subnet %s."),
  INVALID_VOLUME_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid volume name %s. It must be one of the listed volume names returned in the machine"
          + " configuration options."),
  INVALID_HOSTNAME(HttpServletResponse.SC_BAD_REQUEST, "Invalid hostname %s."),
  INVALID_HOST_PORT(HttpServletResponse.SC_BAD_REQUEST, "Invalid host port %d."),
  INVALID_URL(HttpServletResponse.SC_BAD_REQUEST, "Invalid URL %s."),
  INVALID_PATH_PARAMETER(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid path parameter %s specified."),
  UNSUPPORTED_LOG_TYPE(HttpServletResponse.SC_BAD_REQUEST, "Log type %s is unsupported."),
  INVALID_HOSTNAME_PREFIX(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid hostname prefix %s. It must contain only alphanumeric characters and hyphens, may"
          + " not begin or end with a hyphen (\"-\"), and must not be more than 63 characters"
          + " long."),
  DOMAIN_NAME_TOO_LONG(
      HttpServletResponse.SC_BAD_REQUEST,
      "The domain name for the machine is too long. Try shortening the hostname prefix."),
  INVALID_INSTANCE_TYPE_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid instance type %s. It must be one of the listed instance types returned in the"
          + " machine configuration options."),
  INVALID_REGION_RESTRICTION(
      HttpServletResponse.SC_BAD_REQUEST, "The region restriction %s is not supported."),
  ACKNOWLEDGEMENT_COMMENT_TOO_LONG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Acknowledgement comment too long. It must not exceed %s characters."),

  ACKNOWLEDGED_UNITL_DATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Must specify an acknowledgedUntil date or set unacknowledgeAlert to true."),
  @Hidden
  GLOBAL_ALERTS_ONLY(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified event type %s can only be used for global alerts."),
  EVENT_TYPE_UNSUPPORTED_FOR_GROUP_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified event type %s is not supported for the group type of the specified group."),
  UNITS_MISMATCH(
      HttpServletResponse.SC_BAD_REQUEST, "Threshold units cannot be converted to metric units."),
  MISSING_METRIC_THRESHOLD(
      HttpServletResponse.SC_BAD_REQUEST,
      "A metric threshold must be specified for host metric alerts."),
  METRIC_THRESHOLD_PRESENT(
      HttpServletResponse.SC_BAD_REQUEST,
      "The metric threshold should only be specific for host metric alerts."),
  METRIC_TYPE_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "The metric type %s is not supported."),
  METRIC_NAME_AND_EVENT_TYPE_MISMATCH(
      HttpServletResponse.SC_BAD_REQUEST, "Metric name %s is not allowed for event type %s."),
  METRIC_TYPE_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid metricName %s was specified."),
  METRIC_TYPE_NAME_AND_EVENT_TYPE_MISMATCH(
      HttpServletResponse.SC_BAD_REQUEST, "Metric type name %s is not allowed for event type %s."),
  METRIC_TYPE_NAME_NOT_DEFINED(
      HttpServletResponse.SC_BAD_REQUEST, "Metric typeName is not defined."),
  UNSUPPORTED_NOTIFICATION_TYPE(
      HttpServletResponse.SC_BAD_REQUEST, "Notification type %s is unsupported."),
  @Hidden
  NOTIFICATION_TYPE_IS_GLOBAL_ONLY(
      HttpServletResponse.SC_BAD_REQUEST,
      "At least one notification is a type that is only available for global alert"
          + " configurations."),
  MISSING_NOTIFICATIONS(
      HttpServletResponse.SC_BAD_REQUEST,
      "At least one notification must be specified for an alert configuration."),
  INTEGRATION_FIELDS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "At least one integration field is invalid."),
  INTEGRATION_INVALID(HttpServletResponse.SC_BAD_REQUEST, "Integration field %s is invalid: %s."),
  TOO_MANY_GROUP_NOTIFICATIONS(
      HttpServletResponse.SC_BAD_REQUEST,
      "At most one group notification can be specified for an alert configuration."),
  TOO_MANY_ORG_NOTIFICATIONS(
      HttpServletResponse.SC_BAD_REQUEST,
      "At most one organization notification can be specified for an alert configuration."),
  MISSING_THRESHOLD(
      HttpServletResponse.SC_BAD_REQUEST,
      "A threshold must be specified for the specified event type."),
  THRESHOLD_PRESENT(
      HttpServletResponse.SC_BAD_REQUEST,
      "A threshold should not be present for the specified event type."),
  NONZERO_DELAY_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified metric requires a nonzero delay for all notifications."),
  NOTIFICATION_INTERVAL_OUT_OF_RANGE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Notifications must have an internal of at least 5 minutes."),
  EMAIL_OR_SMS_REQUIRED_FOR_GROUP_NOTIFICATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Email and/or SMS must be enabled for group notifications."),
  MISSING_ROLES_FOR_GROUP_NOTIFICATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Group notifications cannot specify an empty list of roles."),
  NO_ORG_NOTIFICATION_FOR_GROUP_ALERT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Organization notification cannot be specified for group alert."),
  EMAIL_OR_SMS_REQUIRED_FOR_USER_NOTIFICATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Email and/or SMS must be enabled for user notifications."),
  INVALID_PAGER_DUTY_SERVICE_KEY(
      HttpServletResponse.SC_BAD_REQUEST,
      "PagerDuty service key must consist of 32 hexadecimal digits."),
  INVALID_DATADOG_API_KEY(
      HttpServletResponse.SC_BAD_REQUEST, "Datadog API key must consist of 32 hexadecimal digits."),
  DATADOG_NOT_SUPPORTED_FOR_CLOUD_MANAGER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Datadog integration is not supported for Cloud Manager projects."),
  INVALID_DATADOG_REGION(
      HttpServletResponse.SC_BAD_REQUEST, "Region %s is not supported for this environment."),
  MISSING_DATADOG_REGION(HttpServletResponse.SC_BAD_REQUEST, "Missing Datadog region."),
  HIPCHAT_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "HipChat integration is only supported for Ops Manager projects."),
  INVALID_EMAIL_ADDRESS(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid email address was specified."),
  WEBHOOK_URL_NOT_SET(
      HttpServletResponse.SC_BAD_REQUEST,
      "Webhook URL must be set before adding webhook notifications."),
  TOTAL_MODE_DEPRECATED(HttpServletResponse.SC_BAD_REQUEST, "Mode TOTAL is no longer supported."),
  INVALID_OPERATOR_FOR_EVENT_TYPE(
      HttpServletResponse.SC_BAD_REQUEST, "Operator %s is not compatible with event type %s."),
  INVALID_UNIT_FOR_EVENT_TYPE(
      HttpServletResponse.SC_BAD_REQUEST, "Unit %s is not compatible with event type %s."),
  INVALID_FILTERLIST(
      HttpServletResponse.SC_BAD_REQUEST,
      "Backup configuration cannot specify both included namespaces and excluded namespaces."),
  INVALID_SNAPSHOT_SCHEDULE(HttpServletResponse.SC_BAD_REQUEST, "Invalid snapshot schedule: %s."),
  INVALID_BACKUP_POLICY(HttpServletResponse.SC_BAD_REQUEST, "Invalid Backup Policy: %s."),
  INVALID_BACKUP_RETENTION_UNIT(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid backup retention unit: %s."),
  BACKUP_POLICY_ITEM_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "Backup policy item not found."),
  CANNOT_SET_POINT_IN_TIME_WINDOW(
      HttpServletResponse.SC_BAD_REQUEST, "Setting the point in time window is not allowed."),
  CANNOT_SET_REF_TIME_OF_DAY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Setting the reference point time of day is not allowed."),
  CANNOT_SET_CLUSTER_CHECKPOINT_INTERVAL_FOR_REPLICA_SET(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster checkpoint interval can only be set for sharded clusters, not replica sets."),

  CANNOT_SET_SELF_MANAGED_SHARDING_FOR_NON_GLOBAL_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot set selfManagedSharding for cluster %s, this field can only be set for Global"
          + " Clusters."),
  INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot set %s because cluster %s is a Self-Managed Global Cluster."),
  GETTING_AND_SETTING_PREFERRED_MEMBERS_ONLY_FOR_REPLICA_SETS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Getting and setting preferred members can only be done on a replica set."),
  SPECIFIED_MEMBER_MUST_BE_IN_THE_LIST_OF_AVAILABLE_MEMBERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified preferred member, %s, is not in the list of available members."),
  INVALID_CLUSTER_CHECKPOINT_INTERVAL(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster checkpoint interval must be 15, 30, or 60 minutes."),
  INVALID_HOURLY_SNAPSHOT_INTERVAL_OR_RETENTION_PERIOD(
      HttpServletResponse.SC_BAD_REQUEST,
      "Hourly snapshot rules must have both interval and duration."),
  INVALID_REFERENCE_TIME_OF_DAY_MISSING_FIELDS(
      HttpServletResponse.SC_BAD_REQUEST,
      "To specify snapshot schedule reference time of day, both the hour and minute must be"
          + " specified."),
  INVALID_REFERENCE_HOUR_OF_DAY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Snapshot schedule reference hour must be between 0 and 23, inclusive."),
  INVALID_REFERENCE_MINUTE_OF_HOUR(
      HttpServletResponse.SC_BAD_REQUEST,
      "Snapshot schedule reference minute must be between 0 and 59, inclusive."),
  INVALID_REFERENCE_TIMEZONE_OFFSET(
      HttpServletResponse.SC_BAD_REQUEST,
      "Snapshot schedule timezone offset must conform to ISO-8601 time offset format, such as"
          + " \"+0000\"."),
  FILTER_BY_CLUSTER_NOT_ALLOWED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Filter by cluster is not supported for Ops Manager or Cloud Manager projects."),
  /* CPS only - start*/
  CANNOT_MODIFY_IN_PROGRESS_SNAPSHOT_RETENTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Snapshot retention can only be modified once it is completed."),
  BACKUP_SNAPSHOT_RETENTION_VALUE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Retention value of snapshot is invalid."),
  INVALID_NUM_OF_SNAPSHOTS_TO_RETAIN(
      HttpServletResponse.SC_BAD_REQUEST, "Number of snapshots to retain must be greater than 0."),
  INVALID_NUM_OF_POLICIES(HttpServletResponse.SC_BAD_REQUEST, "Must have exactly one policy."),
  INVALID_NUM_OF_POLICY_ITEMS(
      HttpServletResponse.SC_BAD_REQUEST, "Must have at least one policy item."),
  INVALID_POLICY_ID(HttpServletResponse.SC_BAD_REQUEST, "The policy id %s is invalid."),
  INVALID_POLICY_ITEM_ID(
      HttpServletResponse.SC_BAD_REQUEST, "The specified policy item id %s does not exist."),
  MISSING_POLICY_ITEM_PARAMETERS(
      HttpServletResponse.SC_BAD_REQUEST, "Each policy item must contain all required fields."),
  INVALID_FREQUENCY_TYPE_CHANGE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The frequency type of a policy item must not be changed."),
  INVALID_RESTORE_WINDOW(
      HttpServletResponse.SC_BAD_REQUEST, "The restore window days should be a positive number."),
  /* CPS only - end */
  MISSING_PASSWORD(
      HttpServletResponse.SC_BAD_REQUEST,
      "Username cannot be changed without specifying password."),
  INVALID_PASSWORD(HttpServletResponse.SC_BAD_REQUEST, "Password contains invalid characters."),
  MISSING_ENCRYPTION_AT_REST_ATTRIBUTE(
      HttpServletResponse.SC_BAD_REQUEST, "At least one attribute must be specified."),
  MISSING_CREDENTIALS_FOR_AUTH_MECHANISM(
      HttpServletResponse.SC_BAD_REQUEST,
      "Authentication mechanism %s requires username and password."),
  CANNOT_SET_PASSWORD_FOR_AUTH_MECHANISM(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot change password unless authentication mechanism is MONGODB_CR or PLAIN."),
  CANNOT_SET_CREDENTIALS_FOR_AUTH_MECHANISM(
      HttpServletResponse.SC_BAD_REQUEST,
      "Username and password fields are only supported for authentication mechanism MONGODB_CR or"
          + " PLAIN."),
  INVALID_AUTH_MECHANISM(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid authentication mechanism %s."),
  AUTH_MECHANISM_REQUIRES_SSL(
      HttpServletResponse.SC_BAD_REQUEST, "Authentication mechanism %s requires SSL."),
  NOT_SHARDED(
      HttpServletResponse.SC_BAD_REQUEST, "Only sharded clusters and replica sets can be patched."),
  SHARDING_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Sharding is not supported for the selected instance size %s."),
  INSUFFICIENT_INSTANCE_SIZE(
      HttpServletResponse.SC_BAD_REQUEST, "This feature requires instance size %s or larger."),
  INVALID_GROUP_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid group name \"%s\" was specified."),
  INVALID_ORG_NAME(HttpServletResponse.SC_BAD_REQUEST, "An invalid org name \"%s\" was specified."),
  INVALID_GROUP_NAME_10GEN(
      HttpServletResponse.SC_BAD_REQUEST, "Group name cannot contain \"10gen-\" or \"-10gen\"."),
  RESERVED_GROUP_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The project name cannot start with '%s' (case-insensitive)."),
  INVALID_GROUP_TOKEN(
      HttpServletResponse.SC_BAD_REQUEST,
      "A group tag must be a string (alphanumeric, periods, underscores, and dashes) of length %d"
          + " characters or less."),
  TOO_MANY_GROUP_TOKENS(HttpServletResponse.SC_BAD_REQUEST, "Groups are limited to %d tags."),
  DATABASE_NAME_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST, "Metric %s requires a database name to be provided."),
  DEVICE_NAME_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST, "Metric %s requires a device name to be provided."),
  MISSING_MAINTENANCE_WINDOW_ALERT_TYPE_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maintenance window configurations must specify at least one alert type."),
  MISSING_MAINTENANCE_WINDOW_START_DATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maintenance window configurations must specify a start date."),
  MISSING_MAINTENANCE_WINDOW_END_DATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maintenance window configurations must specify an end date."),
  MAINTENANCE_WINDOW_START_DATE_AFTER_END_DATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maintenance window configurations must specify a start date before their end date."),
  START_DATE_AFTER_END_DATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified start date must be earlier than the specified end date."),
  INVALID_USERNAME(
      HttpServletResponse.SC_BAD_REQUEST, "The specified username is not a valid email address."),
  INVALID_DOMAIN_IN_USERNAME(
      HttpServletResponse.SC_BAD_REQUEST, "The specific username contains a reserved domain."),
  FIRST_NAME_EXCEEDS_MAX_LENGTH(
      HttpServletResponse.SC_BAD_REQUEST, "First names are limited to %s characters."),
  LAST_NAME_EXCEEDS_MAX_LENGTH(
      HttpServletResponse.SC_BAD_REQUEST, "Last names are limited to %s characters."),
  INVITATIONS_RATE_LIMITED_IP(
      HttpServletResponse.SC_BAD_REQUEST, "Rate limit of %s invitations per %s minutes exceeded."),
  RATE_LIMITED_IP(HttpServletResponse.SC_BAD_REQUEST, "Rate limit exceeded."),
  INVALID_MONGODB_USERNAME(
      HttpServletResponse.SC_BAD_REQUEST, "The username %s is not a valid MongoDB login."),
  WEAK_PASSWORD(HttpServletResponse.SC_BAD_REQUEST, "The specified password is not strong enough."),
  SHORT_PASSWORD(HttpServletResponse.SC_BAD_REQUEST, "The specified password is too short."),
  UNSUPPORTED_ROLE(HttpServletResponse.SC_BAD_REQUEST, "The provided role is not supported."),
  COMMON_PASSWORD(
      HttpServletResponse.SC_BAD_REQUEST,
      "The password provided is too weak, as it can be found in most commonly used password"
          + " lists."),
  DATABASE_NAME_INVALID_ADMIN(
      HttpServletResponse.SC_BAD_REQUEST,
      "Database name invalid. %s can only be created on the admin database."),
  DATABASE_NAME_INVALID_EXTERNAL(
      HttpServletResponse.SC_BAD_REQUEST,
      "Database name invalid. Externally authenticated users can only be created on the $external"
          + " database."),
  UNSUPPORTED_DELIVERY_METHOD(
      HttpServletResponse.SC_BAD_REQUEST, "The specified delivery method is not supported."),
  LEGACY_PIT_RESTORE_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Non-automated non-client PIT restores are not supported via API for this group."),
  CHECKPOINT_RESTORE_UNSUPPORTED_FOR_REPLICA_SETS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Checkpoint restores are not supported for replica sets."),
  OPLOG_RESTORE_ONLY_SUPPORTED_FOR_REPLICA_SETS(
      HttpServletResponse.SC_BAD_REQUEST, "Oplog restores are only supported for replica sets."),
  PIT_RESTORE_ONLY_SUPPORTED_FOR_REPLICA_SETS(
      HttpServletResponse.SC_BAD_REQUEST,
      "PIT restores are only supported for replica sets (please specify a checkpoint instead)."),
  FRACTIONAL_TIMESTAMP(
      HttpServletResponse.SC_BAD_REQUEST, "Timestamp must be whole number of seconds."),
  INVALID_EVENT_TYPE_FOR_ALERT(
      HttpServletResponse.SC_BAD_REQUEST, "Event type %s not supported for alerts."),
  DEPRECATED_EVENT_TYPE(HttpServletResponse.SC_BAD_REQUEST, "Event type %s is deprecated."),
  DUPLICATE_ADDRESSES_IN_INPUT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Two or more of the IP addresses being added to the access list are the same."),
  DUPLICATE_PRIVATE_ENDPOINT_IDS_IN_INPUT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Two or more of the private endpoint IDs being added to the private network settings are the"
          + " same."),
  CANNOT_USE_DEDICATED_PRIVATE_ENDPOINT_FOR_FEDERATED_DATABASE_INSTANCE(
      HttpServletResponse.SC_BAD_REQUEST,
      "This endpoint id is already present as a dedicated private endpoint which cannot to connect"
          + " to your federated database instance / online archive: %s."),
  AZURE_PRIVATE_ENDPOINT_CREATED_NOT_PENDING(
      HttpServletResponse.SC_BAD_REQUEST,
      "Azure private endpoint was not created in the PENDING status."),
  AZURE_PRIVATE_ENDPOINT_BAD_STATUS_TRANSITION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Disallowed status transition specified for Azure private endpoint."),
  CANNOT_MODIFY_AZURE_PRIVATE_ENDPOINT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot modify Azure private endpoint unless it is in the FAILED status."),
  UNSUPPORTED_PRIVATE_NETWORKING_SETTINGS_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Private link for Online Archive and Data Lake only supports AWS private endpoints."),
  BAD_ACCESS_LIST_ADD_REQUEST(
      HttpServletResponse.SC_BAD_REQUEST,
      "Should not specify both the IP address and the CIDR block."),
  // TODO <CLOUDP-76145>: When feature flag is removed message should read "A cluster or serverless
  // instance named %s is already present in group %s."
  DUPLICATE_CLUSTER_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "A %s named %s is already present in group %s."),
  DUPLICATE_REGION_CONFIGS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cloud provider regions must be unique across all region configs in a replication spec."),
  DUPLICATE_SERVERLESS_INSTANCE_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "A serverless instance or cluster named %s is already present in group %s."),
  CLUSTER_NAME_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The cluster name %s is invalid. The name can only contain ASCII letters, numbers, and"
          + " hyphens."),
  SERVERLESS_INSTANCE_NAME_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The serverless instance name %s is invalid. The name can only contain ASCII letters,"
          + " numbers, and hyphens."),
  NOT_SERVERLESS_TENANT_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified cluster %s in group %s is not a serverless tenant cluster."),
  INVALID_CLOUD_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified instance %s"
          + " belongs to a cloud provider that is not yet supported by this feature %s."),
  TENANT_PRIVATE_ENDPOINT_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Serverless instances will be migrated to Flex clusters in May 2025. Flex clusters do not"
          + " support private networking. If you wish to use private networking, create or migrate"
          + " to a Dedicated (M10+) cluster."),
  TENANT_PRIVATE_ENDPOINT_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST, "Private endpoint %s not found for tenant instance %s."),
  TENANT_PRIVATE_ENDPOINT_IP_ADDRESS_SET_WITHOUT_ENDPOINT_ID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The private endpoint IP address %s cannot be set without also setting the "
          + "provider private endpoint ID."),
  TENANT_PRIVATE_ENDPOINT_IP_ADDRESS_MALFORMED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified private endpoint IP Address %s is not formatted correctly. An IP Address is"
          + " required for Azure Tenant Endpoints."),
  TENANT_PRIVATE_ENDPOINT_PROVIDER_UPDATE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The private endpoint can not be updated in its current state %s."),
  TENANT_PRIVATE_ENDPOINT_ALREADY_EXISTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "This instance %s already contains an endpoint with the specified provider private"
          + " endpoint ID %s."),
  INVALID_PRIVATE_NETWORK_ENDPOINT_COMMENT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Comments must be 80 characters or less and may only use alphanumeric characters or the"
          + " symbols in the following set: {\".\", \"-\", \":\"}."),
  SERVERLESS_INSTANCE_NOT_PROVISIONED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The Serverless Instance %s must be fully provisioned before creating a private endpoint."),
  CLUSTER_NAME_TOO_LONG(
      HttpServletResponse.SC_BAD_REQUEST, "Cluster name %s is limited to %d characters."),
  DESCRIPTION_TOO_LONG(
      HttpServletResponse.SC_BAD_REQUEST, "Description %s is limited to %d characters."),
  SERVERLESS_INSTANCE_NAME_TOO_LONG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Serverless instance name %s is limited to %d characters."),
  CLUSTER_CANNOT_CHANGE_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot change name of cluster during an update."),
  // TODO <CLOUDP-76145>: When feature flag is removed message should read "Cluster name %s cannot
  // have same first %d characters as another cluster or serverless instance name in same group."
  DUPLICATE_CLUSTER_NAME_PREFIX(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster name %s cannot have same first %d characters as another %s name in same"
          + " group."),
  DUPLICATE_SERVERLESS_INSTANCE_NAME_PREFIX(
      HttpServletResponse.SC_BAD_REQUEST,
      "Serverless instance name %s cannot have same first %d characters as another serverless"
          + " instance or cluster name in same group."),
  CLUSTER_NAME_PREFIX_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster name \"%s\" is invalid. Atlas truncates cluster names to %d characters which"
          + " results in an invalid hostname due to a trailing \"-\" in the generated cluster name"
          + " prefix \"%s\"."),
  SERVERLESS_INSTANCE_NAME_PREFIX_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Serverless instance name \"%s\" is invalid. Atlas truncates serverless instance names to %d"
          + " characters which results in an invalid hostname due to a trailing \"-\" in the"
          + " generated serverless instance name prefix \"%s\"."),
  ATLAS_RESERVED_CLUSTER_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The provided cluster name %s creates a conflict with our internal hostnames, which are"
          + " generated using the pattern \"atlas-[a-z0-9]{6}\"."),
  ATLAS_RESERVED_SERVERLESS_INSTANCE_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The provided serverless instance name %s creates a conflict with our internal hostnames,"
          + " which are generated using the pattern \"atlas-[a-z0-9]{6}\"."),
  CLUSTER_RESTORE_IN_PROGRESS_CANNOT_UPDATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The update can not succeed because cluster restore is in progress. Please try again once"
          + " the restore is finished."),
  CLUSTER_ALREADY_REQUESTED_DELETION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The cluster %s has already been requested for deletion."),
  SERVERLESS_INSTANCE_ALREADY_REQUESTED_DELETION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The serverless instance %s has already been requested for deletion."),
  CANNOT_TERMINATE_CLUSTER_WITH_CONFIGURED_DATA_LAKE_PIPELINES(
      HttpServletResponse.SC_BAD_REQUEST,
      "The cluster %s cannot be terminated when there are Data Lake Pipelines configured. Please"
          + " try again once all Data Lake Pipelines for the cluster are deleted."),
  CLUSTER_RESTART_IN_PROGRESS(
      HttpServletResponse.SC_BAD_REQUEST, "A failover test is already in progress."),
  OPERATION_INVALID_CLUSTER_WORKING(
      HttpServletResponse.SC_BAD_REQUEST,
      "The operation cannot begin while cluster is working or has activity pending."),
  OPERATION_INVALID_UNHEALTHY_NODES(
      HttpServletResponse.SC_BAD_REQUEST,
      "The operation cannot begin because monitoring indicates these nodes are not in the primary"
          + " or secondary state: %s."),
  OPERATION_INVALID_SHARDS_NO_PRIMARY(
      HttpServletResponse.SC_BAD_REQUEST,
      "The operation cannot begin because monitoring indicates these shards have no primary: %s."),
  OPERATION_INVALID_MEMBER_REPLICATION_LAG(
      HttpServletResponse.SC_BAD_REQUEST,
      "The operation cannot begin because monitoring indicates these nodes have too much"
          + " replication lag: %s."),
  OPERATION_INVALID_MEMBER_OPLOG_CAPACITY(
      HttpServletResponse.SC_BAD_REQUEST,
      "The operation cannot begin because monitoring reports a primary with"
          + " insufficient oplog capacity: %s."),
  OPERATION_INVALID_MEMBER_DISK_USAGE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The operation cannot begin because monitoring indicates a node has critically high disk"
          + " usage. Used %.1fGiB/%.1fGiB (%.1f%%, max %.1f%%)."),
  OPERATION_INVALID_INSUFFICIENT_METRICS(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "The operation cannot begin because monitoring data for this cluster is delayed, retry in a"
          + " few minutes."),
  INVALID_CLUSTER_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST, "The specified cluster configuration is not valid: %s."),
  SHARDED_CLUSTER_CANT_BECOME_REPLICA_SET(
      HttpServletResponse.SC_BAD_REQUEST, "A sharded cluster cannot become a replica set."),
  CANNOT_CHANGE_GEOSHARDED_CLUSTER_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "A geo sharded cluster cannot be changed to a different cluster type."),
  CLUSTER_GROUP_ID_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "A cluster with group ID %s cannot be added to group %s."),
  CLUSTER_REPLICATION_FACTOR_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The cluster's replication factor must be either 3, 5, or 7."),
  CLUSTER_NUM_SHARDS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "The cluster's shard count must be between 1 and %s."),
  CLUSTER_DISK_SIZE_INVALID(HttpServletResponse.SC_BAD_REQUEST, "Invalid disk size: %s."),
  CLUSTER_BASE_DISK_SIZE_INVALID(HttpServletResponse.SC_BAD_REQUEST, "Invalid base disk size: %s."),
  CLUSTER_ANALYTICS_DISK_SIZE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid analytics disk size: %s."),
  DISK_SIZE_INVALID_FOR_AZURE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The disk size for a cluster with Azure nodes must correspond to an Azure disk size. The"
          + " valid Azure disk sizes are 8, 16, 32, 64, 128, 256, 512, 1024, 2048, or 4095 GB."),
  CLUSTER_DISK_SIZE_NOT_WHOLE_NUMBER(
      HttpServletResponse.SC_BAD_REQUEST,
      "The cluster's disk size of %.1f GB is invalid. The disk size must be a positive whole"
          + " number."),
  CLUSTER_DISK_IOPS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The cluster's disk IOPS of %d is invalid. For a disk of size %s on instance size %s with a"
          + " volume type of %s, the IOPS must be %s."),
  MONGODB_VERSION_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid MongoDB version %s was specified."),
  MONGODB_MAJOR_VERSION_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid MongoDB major version %s was specified."),
  ATTRIBUTE_READ_ONLY(
      HttpServletResponse.SC_BAD_REQUEST,
      "The attribute %s is read-only and cannot be changed by the user."),
  TENANT_ATTRIBUTE_READ_ONLY(
      HttpServletResponse.SC_BAD_REQUEST,
      "The attribute %s is read-only for tenant clusters and cannot be changed by the user."),
  INVALID_TENANT_CONFIGURATION_REPLICATION_SPECS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Only one replicationSpec can be specified for tenant clusters."),
  INVALID_TENANT_CONFIGURATION_REGION_CONFIGS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Only one regionConfig can be specified for tenant clusters."),
  INVALID_TENANT_CLUSTER_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The clusterType must be REPLICASET for tenant clusters."),
  CLUSTER_CANNOT_CHANGE_PROVIDER_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot change the cloud provider of a cluster."),
  TENANT_CLUSTER_TEST_FAILOVER_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Test failover not supported on tenant clusters."),
  SERVERLESS_INSTANCE_TEST_FAILOVER_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Test failover not supported on serverless instances."),
  TENANT_CLUSTER_UPDATE_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot update a M0/M2/M5 cluster through the public API."),
  TENANT_CLUSTER_LOGS_FOR_HOST_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Logs for host not supported on tenant clusters."),
  SERVERLESS_INSTANCE_LOGS_FOR_HOST_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Logs for host not supported on serverless instances."),
  SERVERLESS_INSTANCE_UPDATE_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot update a serverless instance through the public API."),
  CLUSTER_UPGRADE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster cannot be upgraded, as specified upgrade version is below the current version."),
  CANNOT_SKIP_MAJOR_VERSION_DOWNGRADE(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot downgrade cluster MongoDB version. %s."),
  CANNOT_DOWNGRADE_WITHOUT_PINNED_FCV(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot downgrade cluster MongoDB version without a pinned FCV."),
  MISMATCHED_FEATURE_COMPATIBILITY_VERSION_DOWNGRADE(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot downgrade cluster MongoDB version. %s."),
  CLUSTER_TYPE_NOT_SUPPORTED_FOR_FCV_PINNING(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot pin feature compatibility version for non-dedicated clusters."),
  CLUSTER_TYPE_NOT_SUPPORTED_FOR_FCV_UNPINNING(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot unpin feature compatibility version for non-dedicated clusters."),
  CLUSTER_TYPE_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "This cluster type is not supported yet."),
  ATLAS_USER_NOT_USER_EDITABLE(
      HttpServletResponse.SC_FORBIDDEN, "This user can only be edited by the system."),
  ATLAS_INVALID_USERNAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified username %s is not valid for an Atlas database user."),
  ATLAS_INVALID_RESOURCE(
      HttpServletResponse.SC_BAD_REQUEST,
      "One or more of the selected actions was applied to an incorrect resource. %s."),
  ATLAS_INVALID_LDAP_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The distinguished name specified in the username field, %s, is not valid according to RFC"
          + " 2253."),
  ATLAS_INVALID_X509_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The distinguished name specified in the username field, %s, is not valid according to RFC"
          + " 2253."),
  ATLAS_INVALID_DN_METACHARACTERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "The distinguished name specified in the username field, %s, contains illegal"
          + " metacharacters."),
  ATLAS_INVALID_ROLE(
      HttpServletResponse.SC_BAD_REQUEST, "The specified role %s@%s does not exist."),
  ATLAS_INVALID_ROLE_DATABASE(
      HttpServletResponse.SC_BAD_REQUEST, "The specified role %s only exists for database %s."),
  ATLAS_RESTRICTED_ROLE(
      HttpServletResponse.SC_BAD_REQUEST, "The specified role %s@%s is restricted."),
  ATLAS_RESTRICTED_COLLECTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified collection %s for the role %s@%s is restricted."),
  ATLAS_CLUSTER_VERSION_DEPRECATED(
      HttpServletResponse.SC_BAD_REQUEST, "MongoDB version is deprecated in Atlas."),
  TENANT_RESTORE_VERSION_MISMATCH(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified snapshot and target cluster have mismatched versions of MongoDB."),
  TENANT_RESTORE_INCOMPATIBLE_TOPOLOGY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Tenant backups can not be restored into sharded clusters."),
  TENANT_RESTORE_DESTINATION_UNAVAILABLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The target cluster for the tenant restore is currently unavailable. Please try again."),
  TENANT_RESTORE_DESTINATION_ENCRYPTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Tenant Snapshots cannot be restored into Dedicated clusters with Encryption at Rest."),
  INVALID_TENANT_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Instance size %s with version %s is not supported in region %s. %s Clusters with MongoDB"
          + " version %s are only supported in %s."),
  INVALID_SERVERLESS_INSTANCE_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Serverless instance with version %s is not supported in region %s. Serverless instances"
          + " with MongoDB version %s are only supported in %s."),
  INVALID_FLEX_CLUSTER_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Flex cluster with version %s is not supported in region %s. Flex clusters"
          + " with MongoDB version %s are only supported in %s."),
  DISK_SIZE_GB_TOO_SMALL(
      HttpServletResponse.SC_BAD_REQUEST,
      "The selected disk size is smaller than the amount of data currently used."),
  FIELD_UNSUPPORTED_FOR_OPS_MANAGER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Received JSON contains the %s attribute, which is not supported by Ops Manager."),
  CLOUD_PROVIDER_INVALID_PROVIDER_FOR_GOV(
      HttpServletResponse.SC_BAD_REQUEST, "AWS is the only provider supported by %s."),
  CLOUD_PROVIDER_UNSUPPORTED_FOR_GOV(
      HttpServletResponse.SC_BAD_REQUEST, "Provider %s is not supported by %s."),
  CLOUD_PROVIDER_UNSUPPORTED_FOR_CN_REGIONS_ONLY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Provider %s is not supported by MongoDB Atlas in China."),
  INVALID_COUNTRY_CODE(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid country code %s was specified."),
  INVALID_IP_ADDRESS_OR_CIDR_NOTATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The address %s is not a valid IP address or CIDR notation."),
  INVALID_CIDR_BLOCK(
      HttpServletResponse.SC_BAD_REQUEST, "The cidr block %s must be in valid CIDR notation."),
  INVALID_NETWORK_PERMISSION_COMMENT(
      HttpServletResponse.SC_BAD_REQUEST, "The comment %s must be 80 characters or less."),
  MULTIPLE_VALUES_SPECIFIED_FOR_ONE_NETWORK_PERMISSION_ENTRY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Should not specify both the IP address and the CIDR block."),
  CANNOT_ADD_IP_ADDRESS_TO_ACCESS_LIST(
      HttpServletResponse.SC_BAD_REQUEST, "The address %s cannot be added to user access lists."),
  CANNOT_ADD_IP_ADDRESS_TO_API_KEY_ACCESS_LIST(
      HttpServletResponse.SC_BAD_REQUEST, "The address %s cannot be added to access lists."),
  INVALID_ROUTE_TABLE_CIDR(
      HttpServletResponse.SC_BAD_REQUEST,
      "The address %s must be within the private ranges [\"10.0.0.0/8\", \"**********/12\","
          + " \"***********/16\"]."),
  ATLAS_INVALID_CIDR_BLOCK(
      HttpServletResponse.SC_BAD_REQUEST, "Atlas CIDR %s must be a /%s block for %s."),
  ATLAS_INVALID_CIDR_BLOCK_FOR_SMALL_CONTAINER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas CIDR %s must be a /21 to /24 block for small GCP containers."),
  ATLAS_NON_CANONICAL_CIDR_BLOCK(
      HttpServletResponse.SC_BAD_REQUEST,
      "The canonical form for the specified Atlas CIDR %s is %s."),
  ATLAS_INVALID_CIDR_RANGE(
      HttpServletResponse.SC_BAD_REQUEST, "Atlas CIDR %s must be in private range."),
  BACKUP_KMIP_CERTIFICATE_PATH_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST,
      "A KMIP Client Certificate path must be specified with the password."),
  BACKUP_INVALID_FS_PATH(HttpServletResponse.SC_BAD_REQUEST, "Incorrect path (%s)."),
  AUTO_SCALING_NOT_SUPPORTED(HttpServletResponse.SC_BAD_REQUEST, "Cannot set auto-scaling. %s."),
  ANALYTICS_AUTO_SCALING_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Configuring analytics nodes specific auto-scaling is not yet supported."),
  ASYMMETRIC_INSTANCE_CLASS_DISK_AUTO_SCALING(
      HttpServletResponse.SC_BAD_REQUEST,
      "Disk auto scaling with mixed instance classes is not yet supported."),
  INVALID_IOPS_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "If the base instance size has disk IOPS provisioned, the analytics instance size cannot be"
          + " M10 or M20."),
  INVALID_IOPS_CONFIGURATION_FOR_GCP_FEATURE_DISABLED(
      HttpServletResponse.SC_BAD_REQUEST, "Provisioned IOPS is not yet supported for GCP."),
  INVALID_IOPS_CONFIGURATION_FOR_GCP_UNSUPPORTED_INSTANCE_FAMILY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Provisioned IOPS is not supported for this instance size in this region."),
  AUTO_INDEXING_NOT_SUPPORTED(HttpServletResponse.SC_BAD_REQUEST, "Cannot set auto-indexing. %s."),
  COMPUTE_AUTO_SCALING_SCALE_DOWN_ENABLED_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Compute auto-scaling must be enabled to enable down-scaling."),
  ANALYTICS_AUTO_SCALING_AMBIGUOUS(
      HttpServletResponse.SC_BAD_REQUEST,
      "There is no analytics autoscaling configuration specified, and the base configuration is"
          + " incompatible due to having bounds not compatible with the analytics instance size or"
          + " due to the base and analytics instance families mismatching. An explicit analytics"
          + " autoscaling configuration is required."),
  COMPUTE_AUTO_SCALING_INSTANCE_SIZE_RANGE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The min instance size (%s) must be less than the max instance size (%s)."),
  COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_STORAGE_CONFIGURATION_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Compute auto-scaling configuration is invalid. %s."),
  COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The min instance size (%s) must be less than or equal to proposed instance size (%s)."),
  COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_TOO_SMALL(
      HttpServletResponse.SC_BAD_REQUEST, "Compute auto-scaling configuration is invalid. %s."),
  COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The max instance size (%s) must be greater than or equal to proposed instance size (%s)."),
  COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_FAMILY_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The min instance size (%s) must be in the same instance family as the proposed instance size"
          + " (%s)."),
  COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_FAMILY_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The max instance size (%s) must be in the same instance family as proposed instance size"
          + " (%s)."),
  COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST, "Compute auto-scaling min instance size required."),
  COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST, "Compute auto-scaling max instance size required."),
  COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_INVALID_FOR_DISABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Compute auto-scaling min instance size must be unset when scale down is disabled."),
  COMPUTE_AUTO_SCALING_MAX_INSTANCE_SIZE_INVALID_FOR_DISABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Compute auto-scaling max instance size must be unset when auto-scaling is disabled."),
  COMPUTE_AUTO_SCALING_MIN_INSTANCE_SIZE_INVALID_FOR_SHARDING(
      HttpServletResponse.SC_BAD_REQUEST, "Compute auto-scaling configuration is invalid. %s."),
  COMPUTE_AUTO_SCALING_PREDICTIVE_ENABLED_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Reactive compute auto-scaling must be enabled to enable predictive compute auto-scaling."),
  COMPUTE_AUTO_SCALING_PREDICTIVE_NOT_AVAILABLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Predictive autoscaling is not yet available for this project. Contact MongoDB "
          + "support if you have any questions."),
  COMPUTE_AUTO_SCALING_PREDICTIVE_UNSUPPORTED_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Predictive autoscaling is not yet supported for this cluster configuration."),
  ANALYTICS_NODES_NO_COMPUTE_AUTO_SCALING_PREDICTIVE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Predictive autoscaling is not available for dedicated analytics nodes."),
  PREDICTIVE_COMPUTE_AUTO_SCALING_NOT_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Predictive compute autoscaling is not enabled for the cluster yet."),
  PREDICTIVE_COMPUTE_AUTO_SCALING_CONTEXT_NOT_FOUND(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "Predictive autoscaling context is not found for cluster name %s, group id %s."),
  BACKUP_WRONG_DAEMON_CONFIG_ID(
      HttpServletResponse.SC_BAD_REQUEST, "The specified ID for Daemon Config %s is wrong."),
  CANNOT_CREATE_PAUSED_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST, "Cluster %s cannot be created in a paused state."),
  CANNOT_CREATE_PAUSED_SERVERLESS_INSTANCE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Serverless instance %s cannot be created in a paused state."),
  CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API(
      HttpServletResponse.SC_BAD_REQUEST,
      "Serverless instance %s cannot be used in the cluster API."),
  CANNOT_USE_CLUSTER_IN_SERVERLESS_INSTANCE_API(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster %s cannot be used in the serverless instance API."),
  CANNOT_USE_FLEX_CLUSTER_IN_SERVERLESS_INSTANCE_API(
      HttpServletResponse.SC_BAD_REQUEST,
      "Flex cluster %s cannot be used in the serverless instance API."),
  CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API(
      HttpServletResponse.SC_BAD_REQUEST, "Flex cluster %s cannot be used in the Cluster API."),
  CANNOT_USE_NON_FLEX_CLUSTER_IN_FLEX_API(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot use non-flex cluster %s in the flex API."),
  CANNOT_PAUSE_RECENTLY_RESUMED_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Recently resumed clusters must remain running for %d minutes to process all queued"
          + " maintenance."),
  CANNOT_ADD_ORG_ROLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Adding an organization-level role in a group is not supported."),
  CANNOT_ADD_GROUP_ROLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Adding a group-level role in an organization is not supported."),
  CANNOT_CREATE_ATLAS_ORG(
      HttpServletResponse.SC_BAD_REQUEST, "Atlas organizations cannot be created via Ops Manager."),
  DUPLICATE_DATABASE_ROLES(
      HttpServletResponse.SC_BAD_REQUEST, "Duplicate database roles specified for user %s: %s."),
  INVALID_TEAM_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "An invalid team name \"%s\" was specified."),
  INVALID_DELIVERY_SCP(HttpServletResponse.SC_BAD_REQUEST, "SCP restores are no longer supported."),
  LDAP_VERIFY_CONNECTIVITY_NO_AVAILABLE_CLUSTERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "No clusters available to perform LDAP connectivity verification for groupId: %s.  At least"
          + " one active cluster running MongoDB version 3.6 or greater is required."),
  LDAP_VERIFY_CONNECTIVITY_NO_AVAILABLE_INSTANCES(
      HttpServletResponse.SC_BAD_REQUEST,
      "No cluster nodes are available to perform LDAP connectivity verification for groupId: %s."),
  LDAP_VERIFY_CONNECTIVITY_NO_AVAILABLE_MONGODB_PACKAGE(
      HttpServletResponse.SC_BAD_REQUEST,
      "No MongoDB package available to perform LDAP connectivity verification for group: %s."),
  UNSUPPORTED_VERSION_FOR_LDAP_AUTHENTICATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "LDAP authentication requires all clusters to run MongoDB version 3.6 or higher."),
  INACTIVE_ORG(HttpServletResponse.SC_BAD_REQUEST, "Organization %s is inactive."),
  INVALID_DATE_RANGE_FOR_SALES_COMP(
      HttpServletResponse.SC_BAD_REQUEST,
      "The end date should be in past, and start date should be before end date."),
  SERVERLESS_INSTANCE_UNSUPPORTED_IN_API(
      HttpServletResponse.SC_BAD_REQUEST, "Serverless instances %s is not supported in this API."),
  SHARED_TIER_INSTANCE_UNSUPPORTED_IN_API(
      HttpServletResponse.SC_BAD_REQUEST, "Shared-tier instances %s is not supported in this API."),
  FLEX_TIER_INSTANCE_UNSUPPORTED_IN_API(
      HttpServletResponse.SC_BAD_REQUEST, "Flex instances %s is not supported in this API."),
  ATLAS_INVALID_AUTH_SETTINGS(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid authentication settings. %s."),
  ATLAS_X509_USER_CANNOT_BE_TEMPORARY(
      HttpServletResponse.SC_BAD_REQUEST, "X.509 users cannot be temporary."),
  ATLAS_X509_USER_CANNOT_USE_LDAP(
      HttpServletResponse.SC_BAD_REQUEST,
      "Users cannot be configured for X.509 and LDAP authentication."),
  ATLAS_X509_USER_CANNOT_USE_SCRAM(
      HttpServletResponse.SC_BAD_REQUEST,
      "Users cannot be configured for SCRAM and X.509 authentication."),
  ATLAS_MANAGED_X509_NAME_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The username %s is invalid for Atlas-managed X.509 user auth. "
          + "These usernames must take the form CN=<username> with no additional OIDs."),
  ATLAS_INVALID_AWS_IAM_ARN(
      HttpServletResponse.SC_BAD_REQUEST,
      "The ARN specified in the username field, %s, is not valid for AWS IAM type %s."),
  ATLAS_INVALID_FEDERATED_AUTH_USERNAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The username %s is invalid for Federated Authentication users. These usernames must take the"
          + " form of an identity provider name, followed by a slash, followed by an identity"
          + " provider group name."),
  ATLAS_GENERATED_CERTS_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The limit on generated certificates per user has been reached."),
  AZURE_CUSTOMER_NETWORK_VALIDATION_FAILED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Please ensure that the account information provided is correct and that you've "
          + "given Atlas the proper permissions to create peering connections in your account."),
  NO_COMMON_INSTANCE_FAMILY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster configuration is invalid.  There is no common supported instance family in the"
          + " selected regions."),
  PROVIDER_BACKUP_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cloud backups are not supported for this type of provider."),
  AWS_KMS_CUSTOMER_MASTER_KEY_PENDING_DELETION(
      HttpServletResponse.SC_BAD_REQUEST, "AWS KMS Customer Master Key is pending deletion."),
  AWS_KMS_CUSTOMER_MASTER_KEY_PENDING_IMPORT(
      HttpServletResponse.SC_BAD_REQUEST, "AWS KMS Customer Master Key is pending import."),
  AWS_KMS_CUSTOMER_MASTER_KEY_NOT_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST, "AWS KMS Customer Master Key is not enabled."),
  AWS_KMS_REGION_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Region is not supported by AWS KMS."),
  AWS_KMS_CREDENTIALS_AUTH_DEPRECATED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Credentials based authentication is no longer supported for AWS KMS."),
  AWS_KMS_KEY_STATE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "The AWS KMS key is in an invalid state."),
  CANNOT_CHANGE_AWS_KMS_AUTH_METHOD(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas does not permit changing from AWS IAM role-based authentication to using credentials"
          + " for KMS access."),
  INVALID_AWS_KMS_AUTH_CONFIG(
      HttpServletResponse.SC_BAD_REQUEST,
      "AWS KMS authorization config should not specify both the credentials and the Cloud Provider"
          + " Access roleId."),

  INVALID_GCP_KMS_AUTH_CONFIG(
      HttpServletResponse.SC_BAD_REQUEST,
      "GCP KMS authorization config should not specify both the credentials and the Cloud Provider"
          + " Access roleId."),
  GCP_KMS_CREDENTIALS_AUTH_DEPRECATED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Credentials based authentication is no longer supported for GCP KMS."),

  CANNOT_CHANGE_GCP_KMS_AUTH_METHOD(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas does not permit changing from GCP IAM role-based authentication to using credentials"
          + " for KMS access."),
  GOOGLE_CLOUD_KMS_MISSING_PERMISSIONS_FOR_SERVICE_ACCOUNT_EXCEPTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Missing required Google Cloud KMS access permissions for Role Id %s."),
  NVME_STORAGE_PROVIDER_BACKUP_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cloud backups must be enabled for deployments with NVMe storage."),
  NVME_STORAGE_CONTINUOUS_BACKUP_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Legacy backup is enabled for this project and is unsupported for deployments with NVMe"
          + " storage."),
  CLUSTER_REGIONS_INCOMPATIBLE_WITH_BACKUP_COPY_SETTINGS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Region configuration is not compatible with backup copy settings: %s. Please update copy"
          + " settings in order to proceed."),

  CANNOT_SWITCH_CPS_BACKUP_TO_CONTINUOUS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot switch a cluster's backup from Cloud Backup to Legacy Backup."),
  INVALID_AWS_CREDENTIALS(HttpServletResponse.SC_BAD_REQUEST, "Invalid AWS credentials."),
  AZURE_KEY_VAULT_KEY_NOT_ENABLED(HttpServletResponse.SC_BAD_REQUEST, "Key not enabled."),
  AZURE_KEY_VAULT_KEY_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST,
      "No Azure Key Vault key found for the provided credentials."),
  AZURE_KEY_VAULT_KEY_NOT_ACTIVE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The key must not be used before its \"nbf\" (not before) date."),
  AZURE_KEY_VAULT_KEY_EXPIRED(HttpServletResponse.SC_BAD_REQUEST, "The key has expired."),
  AZURE_KEY_VAULT_ENVIRONMENT_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Environment not supported for Azure Key Vault."),
  AZURE_KEY_VAULT_KEY_IDENTIFIER_INVALID_FORMAT(
      HttpServletResponse.SC_BAD_REQUEST,
      "The key identifier must be a full Azure Key Vault object identifier that includes the key"
          + " version, and the key vault name segment of the key identifier must match the key"
          + " vault name provided."),
  INVALID_AZURE_CREDENTIALS(HttpServletResponse.SC_BAD_REQUEST, "Invalid Azure credentials."),
  AZURE_KEY_VAULT_FORBIDDEN_BY_FIREWALL(
      HttpServletResponse.SC_BAD_REQUEST,
      "The Azure Key Vault has restricted network access and cannot be reached."),
  GOOGLE_CLOUD_KMS_KEY_VERSION_PENDING_GENERATION(
      HttpServletResponse.SC_BAD_REQUEST, "Key version is pending generation."),
  GOOGLE_CLOUD_KMS_KEY_VERSION_DESTROYED(
      HttpServletResponse.SC_BAD_REQUEST, "Key version is destroyed."),
  GOOGLE_CLOUD_KMS_KEY_VERSION_DESTROY_SCHEDULED(
      HttpServletResponse.SC_BAD_REQUEST, "Key version is scheduled for destruction."),
  GOOGLE_CLOUD_KMS_KEY_VERSION_DISABLED(
      HttpServletResponse.SC_BAD_REQUEST, "Key version is disabled."),
  GOOGLE_CLOUD_KMS_KEY_VERSION_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot find key version."),
  GOOGLE_CLOUD_FAILED_PRECONDITION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Failed pre-condition for Google Cloud resource.  Ensure that billing is enabled for the"
          + " project."),
  INVALID_GOOGLE_CLOUD_CREDENTIALS(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid Google Cloud credentials."),
  NO_PERMISSION_TO_ENCRYPT_DECRYPT(
      HttpServletResponse.SC_BAD_REQUEST,
      "The provided credentials do not have permission to encrypt or decrypt with the key."),
  ENCRYPTION_AT_REST_NOT_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST, "Encryption at Rest is not enabled for group %s."),
  ENCRYPTION_AT_REST_AND_MAJOR_VERSION_UPDATE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot change Encryption At Rest while making a MongoDB major version change."),
  ENCRYPTION_AT_REST_AND_ADD_NODES_EXISTING_REGIONS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot change Encryption At Rest while adding nodes to existing regions."),
  ENCRYPTION_AT_REST_SETTINGS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas is unable to access your encryption key.  You cannot change your encryption key or"
          + " create a cluster using this provider until access to your current encryption key has"
          + " been restored."),
  NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION(
      HttpServletResponse.SC_BAD_REQUEST,
      "No Encryption at Rest Private Endpoint connections "
          + "exist in the requested regions: %s."),
  ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "Encryption at Rest Private endpoint with id %s not found."),
  ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_DELETION_FAILED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Failed to delete Encryption at Rest Private endpoint with id %s. Please try again later."),
  ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_IN_USE_OR_CONFIGURING(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified Encryption at Rest Private endpoint with id %s is currently being used by at"
          + " least one cluster or is being configured."),
  CANNOT_CREATE_ENCRYPTION_AT_REST_PRIVATE_ENDPOINTS_IN_EXISTING_REGIONS(
      HttpServletResponse.SC_CONFLICT,
      "Failed to create Encryption at Rest Private Endpoint due to existing private endpoint in the"
          + " requested regions: %s."),
  CANNOT_USE_ENCRYPTION_AT_REST_TENANT(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot use Encryption at Rest with tenant clusters."),
  CANNOT_USE_ENCRYPTION_AT_REST_SERVERLESS_INSTANCE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot use Encryption at Rest with serverless instances."),
  CANNOT_ENABLE_ENCRYPTION_AT_REST_REQUIRE_PRIVATE_NETWORKING_DUE_TO_EXISTING_CROSS_CLOUD_CLUSTERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot enable %s Encryption At Rest requirePrivateNetworking due to existing cross cloud"
          + " clusters: %s."),
  CANNOT_ENABLE_ENCRYPTION_AT_REST_REQUIRE_PRIVATE_NETWORKING_DUE_TO_EXISTING_CLUSTERS_WITH_NON_MATCHING_ENCRYPTION_AT_REST_PROVIDER_AND_CLUSTER_CLOUD_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot enable %s Encryption At Rest requirePrivateNetworking due to existing clusters with "
          + "non matching encryption at rest provider and cluster cloud provider: %s."),
  CANNOT_ENABLE_ENCRYPTION_AT_REST_REQUIRE_PRIVATE_NETWORKING_DUE_TO_SINGLE_REGION_AWS_KMS_CMK(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot enable Encryption At Rest requirePrivateNetworking due to single region AWS KMS"
          + " customer master key."),
  INVALID_ENCRYPTION_AT_REST_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST,
      "An invalid Encryption at Rest provider %s was specified."),
  CANNOT_USE_ENCRYPTION_AT_REST_BACKUP_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot use Encryption at Rest with clusters running legacy backup."),
  ENCRYPTION_AT_REST_PROVIDER_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Encryption at Rest Provider cannot be configured on this cluster."),
  CANNOT_DISABLE_ENCRYPTION_AT_REST_DUE_TO_PRIVATE_ENDPOINTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Encryption at Rest cannot be disabled when private endpoints are present."),
  CANNOT_DISABLE_ENCRYPTION_AT_REST_REQUIRE_PRIVATE_NETWORKING_WHILE_PRIVATE_ENDPOINTS_EXIST(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot disable requirePrivateNetworking field when there are private endpoints."
          + " Remove private endpoints in region(s) %s to disable requirePrivateNetworking."),

  CANNOT_ENABLE_ENCRYPTION_AT_REST_REQUIRE_PRIVATE_NETWORKING_DURING_CMK_UPDATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot enable %s Encryption At Rest requirePrivateNetworking at the same time of updating"
          + " the customer master key id."),
  CANNOT_CREATE_PRIVATE_ENDPOINT_DUE_TO_REQUIRE_PRIVATE_NETWORKING_DISABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create Encryption At Rest Private endpoint due to disabled Private Networking."),
  CANNOT_UPDATE_ENCRYPTION_AT_REST_CONFIGURATION_WHEN_PRIVATE_ENDPOINTS_EXIST(
      HttpServletResponse.SC_BAD_REQUEST,
      "Failed to update Encryption At Rest %s when private endpoints exist."),
  AZURE_KEY_VAULT_KEY_OPERATION_FORBIDDEN(
      HttpServletResponse.SC_BAD_REQUEST,
      "The provided key does not have permission to perform encrypt and decrypt operations."),
  VALIDATE_AZURE_KEY_VAULT_FAILED(
      HttpServletResponse.SC_BAD_REQUEST, "The Azure Key Vault Validation Job failed."),
  VALIDATE_AZURE_KEY_VAULT_TIMED_OUT(
      HttpServletResponse.SC_BAD_REQUEST, "The Azure Key Vault Validation Job timed out."),
  CLOUD_PROVIDER_ACCESS_ROLE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Cloud Provider Access role not found."),
  DUPLICATE_MANAGED_NAMESPACE(
      HttpServletResponse.SC_BAD_REQUEST, "Managed Namespace %s already exists."),
  INVALID_SHARD_KEY_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid shard key configuration: %s."),
  MANAGED_NAMESPACE_CANNOT_ALREADY_BE_SHARDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Collection %s seems to already have been sharded. If this collection was recently deleted"
          + " and recreated, it can take some time for our system to catch up. If this is the"
          + " case, please wait a few minutes and try again."),
  DUPLICATE_ZONE_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "Zone names must be unique across all zones."),
  INVALID_LOCATION_CODE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid location code %s. A list of valid codes is available at"
          + " https://cloud.mongodb.com/static/atlas/country_iso_codes.txt."),
  DB_VERSION_NOT_SUPPORTED(HttpServletResponse.SC_BAD_REQUEST, "MongoDB version not supported."),
  AUTOMATION_AGENT_VERSION_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Automation agent version is not supported."),
  MAX_ORGS_PER_USER_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of organizations per user (%s) for %s exceeded while trying to add user to"
          + " organization."),
  MAX_TEAMS_PER_USER_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of teams per user (%s) for %s exceeded while trying to add user to team."),
  MAX_GROUPS_PER_USER_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of groups per user (%s) for %s exceeded while trying to add user to group."),
  MAX_NOTIFICATIONS_PER_ALERT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of notification methods per alert configuration (%s) exceeded while trying"
          + " to add notification methods."),
  MAX_ALERT_CONFIGS_PER_GROUP_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of alert configurations per group (%s) in %s exceeded while trying to add"
          + " alert configurations."),
  MAX_USERS_PER_GROUP_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of users per group (%s) in %s exceeded while trying to add users."),
  MAX_TEAMS_PER_GROUP_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of teams per group (%s) in %s exceeded while trying to add teams."),
  MAX_USERS_PER_TEAM_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of users per team (%s) in %s exceeded while trying to add users."),
  MAX_USERS_PER_ORG_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of users per org (%s) in %s exceeded while trying to add users."),
  MAX_API_KEYS_PER_ORG_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of API keys per org (%s) in %s exceeded while trying to add API keys."),
  MAX_TEAMS_PER_ORG_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of teams per organization (%s) in %s exceeded while trying to add team."),
  MAX_GROUPS_PER_ORG_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of groups per organization (%s) in %s exceeded while trying to add group."),
  SERVICE_ACCOUNT_NAME_TOO_LONG(
      HttpServletResponse.SC_BAD_REQUEST, "Service Account name is limited to %d characters."),
  SERVICE_ACCOUNT_DESCRIPTION_TOO_LONG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Service Account description is limited to %d characters."),
  SERVICE_ACCOUNT_INVITE_RESTRICTED(
      HttpServletResponse.SC_UNAUTHORIZED,
      "You do not have the necessary permissions. Only users with Organization Owner privileges can"
          + " invite Service Accounts."),
  DATA_CONCURRENCY_ERROR(
      HttpServletResponse.SC_CONFLICT,
      "Concurrent modification attempt has been detected for ClientID: %s. Please try again."),
  INVALID_MAX_SERVICE_ACCOUNT_SECRET_VALIDITY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid value (%d) provided for max service account secret validity setting. "
          + "The value must be minimum %d hours (%d days) and maximum %d hours (%d days)."),
  INVALID_SECURITY_CONTACT_EMAIL(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid email used for security contact."),
  INVALID_ABSOLUTE_SESSION_TIMEOUT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid value for absolute session timeout. Value must be within the accepted range or null"
          + " to unset."),
  INVALID_IDLE_SESSION_TIMEOUT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid value for idle session timeout. Value must be within the accepted range or null to"
          + " unset."),
  MAX_SERVICE_ACCOUNTS_PER_ORG_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of Service Accounts per org (%s) in %s exceeded when trying to add a new"
          + " Service Account."),
  MAX_SECRETS_PER_SERVICE_ACCOUNT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Service Account secret limit (%d) reached. Service account (%s) has %d secrets."),
  MAX_NETWORK_ADDRESSES_PER_SERVICE_ACCOUNT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Maximum number of Network Addresses per Service Account (%s) in %s exceeded when trying to"
          + " add a new Network Address to the AccessList."),
  SERVICE_ACCOUNT_ACCESS_LIST_ACCESS_DENIED(
      HttpServletResponse.SC_UNAUTHORIZED,
      "Service Account access lists are only accessible by the Service Account or by a user"
          + " administrator."),
  SERVICE_ACCOUNT_ACCESS_LIST_ENTRY_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "IP address %s is not in the access list for the Service Account %s."),
  DEFAULT_CONFIG_LIMIT_EXCEPTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The limit check failed while trying to add requested resource. Please try again."),
  DEFAULT_INVITATION_EXCEPTION(
      HttpServletResponse.SC_BAD_REQUEST, "Failed to send an invitation to (%s) to join (%s)."),
  NETWORK_PERMISSION_EXPIRATION_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Expiration dates are only supported for IP addresses."),
  RESOURCE_NOT_FOUND_FOR_JOB(
      HttpServletResponse.SC_BAD_REQUEST, "The resource %s of type %s wasn't found."),
  INVALID_SNAPSHOT_RETENTION_DURATION(
      HttpServletResponse.SC_BAD_REQUEST, "Please provide a valid number of days for retention."),
  MULTIPLE_CONCURRENT_SNAPSHOTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Another snapshot for this replica set/cluster is already in progress."),
  INVALID_LOG_REQUEST_SIZE(
      HttpServletResponse.SC_BAD_REQUEST, "Log request size has to be a positive number."),
  INVALID_LOG_REQUEST_TYPES(
      HttpServletResponse.SC_BAD_REQUEST, "The list of requested log types must not be empty."),
  ONLY_FAILED_JOB_CAN_BE_RESTARTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "A job can only be restarted if it is in the FAILED state."),
  JOB_EXPIRATION_DATE_EXCEEDS_MAX(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified expiration date can be at most %d months after the creation date."),
  JOB_EXPIRATION_DATE_IN_PAST(
      HttpServletResponse.SC_BAD_REQUEST, "The specified expiration date must be in the future."),
  CANNOT_EXTEND_EXPIRED_JOB(
      HttpServletResponse.SC_BAD_REQUEST, "An expired log collection job cannot be extended."),
  CANNOT_DOWNLOAD_EXPIRED_JOB(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot download job %s, as it has expired and the data has been deleted from the"
          + " Application Database."),
  CANNOT_DOWNLOAD_JOB_IN_PROGRESS(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot download job %s as it is in progress."),
  EXPIRATION_DATE_EXCEEDS_MAX(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified expiration date can be at most %d days in the future."),
  EXPIRATION_DATE_IN_PAST(
      HttpServletResponse.SC_BAD_REQUEST, "The specified expiration date must be in the future."),
  ALREADY_EXPIRED(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot update an entity that has already expired."),
  PERMANENT_ENTITY_CANNOT_BE_MADE_TEMPORARY(
      HttpServletResponse.SC_BAD_REQUEST, "A permanent entity cannot be made temporary."),
  DUAL_REPLICATION_SPEC_SPECIFIED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Both replicationSpec and replicationSpecs fields were specified. Only one of these two"
          + " should be provided."),
  REPLICATION_SPECS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The replicationSpecs array contains a document with missing fields. All fields must be"
          + " specified in the replicationSpecs array."),
  CANNOT_CANCEL_AUTOMATED_RESTORE(
      HttpServletResponse.SC_BAD_REQUEST, "Automated restore cannot be cancelled."),
  TENANT_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot set custom process args for tenant clusters."),
  SERVERLESS_INSTANCE_PROCESS_ARGS_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot set custom process args for serverless instances."),
  FLEX_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot set custom process args for flex clusters."),
  PROCESS_ARG_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Process arg not supported by version %s: %s."),
  INVALID_CHANGE_STREAM_OPTIONS_PRE_AND_POST_IMAGES_EXPIRE_AFTER_SECONDS(
      HttpServletResponse.SC_BAD_REQUEST,
      "The process arg `changeStreamOptionsPreAndPostImagesExpireAfterSeconds` must be either -1"
          + " (off) or a positive integer."),
  INVALID_TLS_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "This cluster does not support TLS1.0 or TLS1.1. "
          + "If you must use old TLS versions contact MongoDB support."),
  OPLOG_SIZE_MB_TOO_BIG(HttpServletResponse.SC_BAD_REQUEST, "Your oplogSizeMB is too big. %s."),
  OPLOG_SIZE_MB_LESS_THAN_EQUAL_ZERO(
      HttpServletResponse.SC_BAD_REQUEST, "Your oplog size cannot be <= 0."),
  OPLOG_SIZE_MB_LESS_THAN_990(
      HttpServletResponse.SC_BAD_REQUEST,
      "Your oplog size cannot be changed to < 990 MB on MongoDB 3.6+."),
  OPLOG_SIZE_MB_TOO_SMALL(HttpServletResponse.SC_BAD_REQUEST, "Your oplogSizeMB is too small. %s."),
  OPLOG_MIN_RETENTION_HOURS_LESS_THAN_ZERO(
      HttpServletResponse.SC_BAD_REQUEST, "Your oplogMinRetentionHours cannot be < 0."),
  OPLOG_MIN_RETENTION_HOURS_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Your oplogMinRetentionHours is only supported with MongoDB version 4.4 or higher."),
  OPLOG_MIN_RETENTION_HOURS_NO_DISK_AUTO_SCALING(
      HttpServletResponse.SC_BAD_REQUEST,
      "Your oplogMinRetentionHours cannot be configured without disk autoscaling."),
  OPLOG_MIN_RETENTION_HOURS_NVME_TOO_SMALL(
      HttpServletResponse.SC_BAD_REQUEST, "Your oplogMinRetentionHours is too small. %s."),
  TRANSACTION_LIFETIME_LIMIT_SECONDS_LESS_THAN_ONE(
      HttpServletResponse.SC_BAD_REQUEST, "Your transactionLifetimeLimitSeconds cannot be < 1."),
  TRANSACTION_LIFETIME_LIMIT_SECONDS_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "TransactionLifetimeLimitSeconds is only supported with MongoDB version 4.0 or higher."),
  DEFAULT_READ_MAX_TIME_LESS_THAN_ZERO(
      HttpServletResponse.SC_BAD_REQUEST, "Your defaultMaxTimeMS: ReadOperation cannot be < 0."),
  DEFAULT_READ_MAX_TIME_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "DefaultMaxTimeMS: ReadOperation is only supported with MongoDB version 8.0 or higher."),
  CHUNK_MIGRATION_CONCURRENCY_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "ChunkMigrationConcurrency is not supported. %s."),
  CANNOT_DOWNLOAD_SNAPSHOT_WITH_ENCRYPTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot download snapshots with Encryption at Rest enabled."),
  CANNOT_DOWNLOAD_SNAPSHOT_CONCURRENTLY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create a manual download of a snapshot when there is already an active download for"
          + " that snapshot."),
  TOO_MANY_SNAPSHOT_DOWNLOADS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create a manual download of a snapshot when the project is at its limit for active"
          + " downloads."),
  MANUAL_DOWNLOAD_NOT_SUPPORTED_FOR_SERVERLESS_BACKUP(
      HttpServletResponse.SC_BAD_REQUEST, "Manual download not supported for serverless backup."),
  CANNOT_RESTORE_SNAPSHOT_CONCURRENTLY(
      HttpServletResponse.SC_CONFLICT,
      "Cannot create an automated snapshot restore when there is already one in progress."),
  CANNOT_START_AUOTMATED_RESTORE_WHEN_AUTOMATION_BUSY(
      HttpServletResponse.SC_CONFLICT,
      "Cannot create an automated snapshot restore when automation is processing another job."),
  INVALID_SNAPSHOT_RESTORE_ENCRYPTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Encryption settings of target cluster and source snapshot are different."),
  SNAPSHOT_EXPORT_WITH_PRIVATE_LINK_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Snapshot Exports are not supported for projects with Encryption at Rest Private Endpoints."),
  CLUSTER_PAUSED_CANNOT_RESTORE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Backup restore cannot occur on a paused cluster. Please unpause the target cluster or"
          + " choose another target cluster."),
  CLUSTER_PAUSED_CANNOT_LOAD_SAMPLE_DATASET(
      HttpServletResponse.SC_BAD_REQUEST,
      "Loading sample dataset cannot occur on a paused cluster. Please unpause the target cluster"
          + " or choose another target cluster."),
  CANNOT_PERFORM_RESTORE_ON_CLUSTER_WITH_RUNNING_LIVE_IMPORT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot perform restore actions on a cluster that is the target of a live import."),
  CANNOT_PERFORM_RESTORE_DURING_IP_MIGRATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot perform restore actions on a cluster that is undergoing an IP migration. The"
          + " migration should be over shortly."),
  INVALID_PIT_RESTORE_TIME(
      HttpServletResponse.SC_BAD_REQUEST,
      "Please provide a valid UTC time or an operation time (ts)."),
  RESTORE_VALIDATION_FAILED(
      HttpServletResponse.SC_CONFLICT, "Restore validation failed due to: %s."),
  ATLAS_INVALID_CUSTOM_ROLE_ASSIGNMENT(
      HttpServletResponse.SC_BAD_REQUEST,
      "If assigned a custom DB role, a user can only be assigned one role."),
  ATLAS_INVALID_CUSTOM_ROLE_INHERITANCE(
      HttpServletResponse.SC_BAD_REQUEST,
      "One or more of the specified inherited roles do not exist."),
  ATLAS_INVALID_CUSTOM_ROLE_INHERITED_SCOPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "One or more of the specified inherited roles have an invalid scope."),
  ATLAS_INVALID_CUSTOM_ROLE_RESTRICTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create custom role %s because a built-in role with that name already exists."),
  ATLAS_RESERVED_CUSTOM_ROLE_RESTRICTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create custom role %s because the prefix \"xgen-\" is reserved for built-in roles."),
  ATLAS_INVALID_CUSTOM_ROLE_CIRCULAR_DEPENDENCY(
      HttpServletResponse.SC_BAD_REQUEST,
      "One or more of the specified inherited roles creates a circular dependency."),
  ATLAS_INVALID_CUSTOM_ROLE_SELF_REFERENTIAL(
      HttpServletResponse.SC_BAD_REQUEST, "A custom DB role cannot inherit from itself."),
  ATLAS_CUSTOM_ROLE_HAS_NO_PERMISSIONS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Custom roles must have one or more user privilege actions or inherited roles."),
  ATLAS_DUPLICATE_CUSTOM_ROLE_ACTION(
      HttpServletResponse.SC_BAD_REQUEST, "A custom role cannot have duplicate actions."),
  ATLAS_DUPLICATE_CUSTOM_ROLE_RESOURCE(
      HttpServletResponse.SC_BAD_REQUEST,
      "A custom role cannot have duplicate resources applied to the same action."),
  AUTOMATED_RESTORE_OF_SHARD_NOT_ALLOWED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Not allowed to do an automated restore of individual shard snapshots of a sharded cluster."),
  ATLAS_INVALID_MAINTENANCE_WINDOW_DAY_OF_WEEK(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid Day of Week. Value should be between 1 and 7 (Sunday = 1)."),
  ATLAS_INVALID_MAINTENANCE_WINDOW_HOUR_OF_DAY(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid Hour of Day. Value should be between 0 and 23."),
  ATLAS_MAINTENANCE_ALREADY_SCHEDULED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The upcoming maintenance has already been scheduled. The maintenance window cannot be"
          + " adjusted until current maintenance is finished."),
  ATLAS_NUM_MAINTENANCE_DEFERRALS_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Scheduled maintenance for a group cannot be deferred more than twice."),
  ATLAS_MAINTENANCE_NOT_SCHEDULED(
      HttpServletResponse.SC_BAD_REQUEST, "There is no maintenance scheduled for this project."),
  NON_COMPLIANT_ATLAS_MAINTENANCE_WINDOW(
      HttpServletResponse.SC_BAD_REQUEST,
      "Your requested maintenance time change cannot be satisfied because your project has pending"
          + " maintenance that must be done by %s."),
  ATLAS_MAINTENANCE_WINDOW_NOT_DEFINED(
      HttpServletResponse.SC_BAD_REQUEST,
      "No maintenance window defined for project. This action is only supported for projects with"
          + " user defined maintenance windows."),
  INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid protected hours. Field=%s, Reason=%s."),
  ATLAS_CUSTOM_ROLE_INVALID_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The role name, \"%s\", is invalid. A custom role name must be a non-empty string made up of"
          + " only ASCII letters, numbers, hyphens, and underscores, and should begin with a"
          + " letter or number."),
  ATLAS_USER_SCOPE_INVALID_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The user scope name field \"%s\" is invalid. The name can only contain ASCII letters,"
          + " numbers, and hyphens."),
  ATLAS_DUPLICATE_SCOPES(
      HttpServletResponse.SC_BAD_REQUEST, "Duplicate scopes specified for user \"%s\": \"%s\"."),
  /**
   * Please use ATLAS_SEARCH_DUPLICATE_INDEX; this is preserved only for backwards compatibility
   * with older APIs.
   */
  ATLAS_FTS_DUPLICATE_INDEX(HttpServletResponse.SC_BAD_REQUEST, "Duplicate Index."),
  ATLAS_FTS_ANALYZER_NAME_NOT_UNIQUE(
      HttpServletResponse.SC_BAD_REQUEST, "Analyzer name is not unique."),
  ATLAS_FTS_ANALYZER_NAME_CANNOT_REUSED(
      HttpServletResponse.SC_BAD_REQUEST, "Stock analyzer name can not be re-used."),
  ATLAS_FTS_INVALID_INDEX_UPDATE(HttpServletResponse.SC_BAD_REQUEST, "Cannot update search index."),
  ATLAS_SEARCH_TOO_MANY_INDEXES(
      HttpServletResponse.SC_BAD_REQUEST,
      "The total Atlas Search index definition size is too large. "
          + "Delete some indexes and try again."),
  ATLAS_SEARCH_COLLECTION_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST, "Collection %s was not found."),
  ATLAS_SEARCH_ROOT_COLLECTION_NOT_STANDARD_COLLECTION(
      HttpServletResponse.SC_BAD_REQUEST, "An index on %s must be based on a standard collection."),
  ATLAS_SEARCH_VIEW_DEPTH_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST, "View %s exceeds the view depth limit of %d."),
  ATLAS_SEARCH_INVALID_VIEW_PIPELINE(
      HttpServletResponse.SC_BAD_REQUEST, "View %s is not valid for search indexing because %s."),
  ATLAS_FTS_INDEX_DELETION_ALREADY_REQUESTED(
      HttpServletResponse.SC_BAD_REQUEST, "Index deletion already requested."),
  ATLAS_SEARCH_INDEX_DELETION_ALREADY_REQUESTED(
      HttpServletResponse.SC_BAD_REQUEST, "Index deletion already requested."),
  ATLAS_SEARCH_ON_PAUSED_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Search index operations are not available when a cluster is paused."),
  ATLAS_FTS_TRANSIENT_ERROR(
      HttpServletResponse.SC_SERVICE_UNAVAILABLE, "A transient error occurs."),
  ATLAS_FTS_DEPLOYMENT_ALREADY_EXISTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Search deployment already exists for specified cluster."),
  ATLAS_FTS_DEPLOYMENT_CLUSTER_IS_NOT_COMPATIBLE(
      HttpServletResponse.SC_BAD_REQUEST, "Cluster is not compatible with Search deployment. %s."),
  ATLAS_SEARCH_DEPLOYMENT_CLUSTER_IS_NOT_COMPATIBLE(
      HttpServletResponse.SC_BAD_REQUEST, "Cluster is not compatible with Search deployment. %s."),
  ATLAS_FTS_DEPLOYMENT_IS_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Search deployment is not supported on the current environment."),
  ATLAS_SEARCH_DEPLOYMENT_IS_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Search deployment is not supported on the current environment."),
  ATLAS_SEARCH_DEPLOYMENT_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST, "Search deployment is required for this operation."),
  ATLAS_FTS_DEPLOYMENT_INVALID_ATTRIBUTE(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid attribute %s specified. %s."),
  ATLAS_SEARCH_DEPLOYMENT_INVALID_ATTRIBUTE(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid attribute %s specified. %s."),
  ATLAS_FTS_DEPLOYMENT_DOES_NOT_EXIST(
      HttpServletResponse.SC_BAD_REQUEST, "Search deployment does not exist."),
  UNSUPPORTED_MONGODB_VERSION_FOR_FTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas Search is only available for clusters running MongoDB version 4.2 or higher."),
  ATLAS_SEARCH_TRANSIENT_ERROR(
      HttpServletResponse.SC_SERVICE_UNAVAILABLE, "A transient error occurs."),
  ATLAS_GENERAL_ERROR(HttpServletResponse.SC_BAD_REQUEST, "Reason: %s."),
  CLUSTER_NOT_PROVISIONED(
      HttpServletResponse.SC_BAD_REQUEST, "This cluster is not yet provisioned."),
  TAG_KEY_BLANK(HttpServletResponse.SC_BAD_REQUEST, "Tag key cannot be blank."),
  TAG_VALUE_BLANK(HttpServletResponse.SC_BAD_REQUEST, "Tag value cannot be blank."),
  TAG_KEY_EXCEEDED_MAX_ALLOWED_LENGTH(
      HttpServletResponse.SC_BAD_REQUEST,
      "Tag key exceeded the maximum allowed length of 255 characters."),
  TAG_VALUE_EXCEEDED_MAX_ALLOWED_LENGTH(
      HttpServletResponse.SC_BAD_REQUEST,
      "Tag value exceeded the maximum allowed length of 255 characters."),
  TAG_KEY_CONTAINS_INVALID_CHARACTERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Tag key contains invalid characters. Allowable characters include : letters, numbers,"
          + " spaces, semi-colon (;), at symbol (@), underscore(_), dash (-),"
          + " periods (.),  plus (+)."),
  TAG_VALUE_CONTAINS_INVALID_CHARACTERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Tag value contains invalid characters. Allowable characters include : letters, numbers,"
          + " spaces, semi-colon (;), at symbol (@), underscore(_), dash (-),"
          + " periods (.),  plus (+)."),
  TAG_KEY_INVALID_PREFIX(
      HttpServletResponse.SC_BAD_REQUEST,
      "Tag key %s contains invalid prefix \"mdb:\". This prefix is reserved."),
  INVALID_FORM_PARAMETER(HttpServletResponse.SC_BAD_REQUEST, "Invalid form parameter %s: %s."),
  UNFINISHED_ON_DEMAND_SNAPSHOT(
      HttpServletResponse.SC_BAD_REQUEST, "Another on-demand snapshot is queued or in progress."),
  API_KEY_NOT_FOUND(HttpServletResponse.SC_BAD_REQUEST, "No API key with ID %s exists."),
  API_KEY_CREDENTIALS_INVALID(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The credentials for the specified API key was invalid."),
  API_KEY_REQUIRES_DESCRIPTION(
      HttpServletResponse.SC_BAD_REQUEST, "All API keys require a non-empty description."),
  API_KEY_ALREADY_IN_GROUP(HttpServletResponse.SC_BAD_REQUEST, "API key is already in the group."),
  CANNOT_DELETE_IN_PROGRESS_SNAPSHOT(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot delete an in progress snapshot %s."),
  CANNOT_DELETE_SNAPSHOT_WITH_BACKUP_COMPLIANCE_POLICY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot delete snapshots while Backup Compliance Policy is enabled."),
  CANNOT_DECREASE_SNAPSHOT_RETENTION_WITH_BACKUP_COMPLIANCE_POLICY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot decrease snapshot retention while Backup Compliance Policy is enabled."),
  SNAPSHOT_RETENTION_LESS_THAN_BACKUP_COMPLIANCE_POLICY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create snapshot with retention less than the Backup Compliance Policy setting (%s %s)"
          + " while Backup Compliance Policy is enabled."),
  CANNOT_TAKE_SNAPSHOT_OF_CLUSTER_WITH_RUNNING_LIVE_IMPORT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot take a snapshot of a cluster that is the target of a live import."),
  CANNOT_DISABLE_CLOUD_BACKUP_WITH_BACKUP_COMPLIANCE_POLICY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot disable Cloud Backup while Backup Compliance Policy is enabled."),
  CANNOT_DISABLE_ENCRYPTION_AT_REST_WITH_BACKUP_COMPLIANCE_POLICY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Encryption at rest is required by Backup Compliance Policy."),
  CANNOT_DISABLE_PIT_WITH_BACKUP_COMPLIANCE_POLICY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Continuous Cloud Backup is required by Backup Compliance Policy."),
  CANNOT_DECREASE_PIT_WINDOW_WITH_BACKUP_COMPLIANCE_POLICY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot decrease point-in-time restore window while Backup Compliance Policy is enabled. A"
          + " support ticket is required."),
  BACKUP_POLICY_NOT_MEETING_BACKUP_COMPLIANCE_POLICY_REQUIREMENTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "The backup policy does not meet the Backup Compliance Policy requirements: %s."),
  BACKUP_POLICIES_NOT_MEETING_BACKUP_COMPLIANCE_POLICY_REQUIREMENTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "The following backup policies do not comply with the specified backup compliance policy:"
          + " %s."),
  CANNOT_DISABLE_BACKUP_COMPLIANCE_POLICY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot disable Backup Compliance Policy because %s. This can only be edited by filing a"
          + " support ticket."),
  CAN_NOT_DISABLE_ALL_ENCRYPTION_AT_REST_SETTINGS_WHILE_BACKUP_COMPLIANCE_POLICY_ENCRYPTION_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "While Backup Compliance Policy encryption is enabled, you must keep at least 1 encryption"
          + " provider enabled."),
  CANNOT_DELETE_COPY_SNAPSHOT_WITH_BACKUP_COMPLIANCE_POLICY_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot delete copy snapshot when Backup Compliance Policy copy protection is enabled."),
  BACKUP_COMPLIANCE_POLICY_SETTINGS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid Backup Compliance Policy settings: %s."),
  BACKUP_EXTRA_RETENTION_SETTINGS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid backup extra retention settings because %s."),
  CANNOT_UPDATE_BACKUP_COMPLIANCE_POLICY_SETTINGS_WITH_PENDING_ACTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot update Backup Compliance Policy settings while there is a pending action."),
  CANNOT_CLOSE_GROUP_ACTIVE_BACKUP_COMPLIANCE_POLICY(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group that has Backup Compliance Policy enabled with active snapshots; please"
          + " remove the snapshots."),
  BACKUP_JOB_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST, "Backups are not enabled for this cluster."),
  INVALID_API_KEY_DESC(
      HttpServletResponse.SC_BAD_REQUEST,
      "API key descriptions are required and must be less than %d characters."),
  MISSING_API_KEY_ROLES(
      HttpServletResponse.SC_BAD_REQUEST, "API keys must have at least one role."),
  API_KEY_ROLE_ASSIGNMENT_MISSING_ID(
      HttpServletResponse.SC_BAD_REQUEST,
      "API key role assignments must include an org or group id."),
  API_KEY_MUST_BELONG_TO_ORG(
      HttpServletResponse.SC_BAD_REQUEST, "API key must belong to an organization."),
  API_KEY_CAN_ONLY_BELONG_TO_ONE_ORG(
      HttpServletResponse.SC_BAD_REQUEST, "API keys may only belong to a single organization."),
  SERVICE_ACCOUNT_MUST_BELONG_TO_ORG(
      HttpServletResponse.SC_BAD_REQUEST, "Service Accounts must belong to an organization."),
  SERVICE_ACCOUNT_CAN_ONLY_BELONG_TO_ONE_ORG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Service Accounts may only belong to a single organization."),
  CANNOT_MODIFY_GLOBAL_CLUSTER_MANAGEMENT_SETTING(
      HttpServletResponse.SC_BAD_REQUEST,
      "The field selfManagedSharding for cluster %s cannot be modified after deployment."),
  CANNOT_UPDATE_PRIVATE_IP_MODE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Group %s has dedicated clusters, so private IP mode cannot be updated."),
  UNSUPPORTED_FOR_PRIVATE_IP_MODE(
      HttpServletResponse.SC_BAD_REQUEST, "An error regarding private IP mode: %s."),
  CANNOT_ENABLE_DEPRECATED_PRIVATE_IP_MODE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Private IP mode is deprecated for this project and cannot be enabled."),
  CANNOT_CHANGE_CONTAINER_REGION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The region of a cloud provider container cannot be changed."),
  MONGODB_BUILD_IN_USE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified MongoDB build (%s) is still in use by at least one cluster in the"
          + " environment."),
  DUPLICATE_MONGODB_BUILD_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "A MongoDB build with the given trueName value (%s) already exists."),
  INVALID_MONGODB_BUILD_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The given trueName value (%s) is not valid. The name must be 3 digits separated by \".\","
          + " with an optional String appended separated by a hyphen. For example:"
          + " \"4.0.0-ent\"."),
  INVALID_MONGODB_CUSTOM_BUILD_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The given custom build trueName value (%s) is not valid. The name must be 3 digits separated"
          + " by \".\", with an appended string separated by a hyphen. For example:"
          + " \"7.0.0-alpha-123\". Any valid mongodb version, like 7.0.0-rc0, is automatically an"
          + " invalid custom build version."),
  INVALID_MONGODB_VERSION_CONFIG(
      HttpServletResponse.SC_BAD_REQUEST, "The MongoDB version is not valid. %s."),
  IPV6_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "The entry %s is an IPv6 value and is not supported."),
  MAX_FREE_CLUSTERS_PER_PROJECT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST, "This project already has another free cluster."),
  DATA_LAKE_CANNOT_CREATE_WITH_STORAGE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake tenant cannot be created with storage."),
  DATA_LAKE_CANNOT_UPDATE_WITH_STORAGE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake tenant cannot be updated with storage."),
  DATA_LAKE_TENANT_NAME_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The Data Lake name %s is invalid. The name can only contain ASCII letters, numbers, and"
          + " hyphens."),
  DATA_LAKE_NAME_TOO_LONG(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake name %s is limited to %d characters."),
  DATA_LAKE_HOSTNAME_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake hostname (%s) invalid."),
  DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake does not support the (%s) cloud provider."),
  DATA_LAKE_DUPLICATE_STORE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake store (%s) specified multiple times."),
  DATA_LAKE_DATABASE_STORE_REFERENCE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake database store (%s) reference invalid."),
  DATA_LAKE_TENANT_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake tenant limit for project exceeded."),
  DATA_LAKE_STORAGE_CONFIG_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake storage config invalid."),
  DATA_LAKE_IAM_ROLE_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Lake tenant with S3 data stores requires a valid IAM role."),
  DATA_LAKE_CLOUD_PROVIDER_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Lake tenant does not have a cloud provider config."),
  INVALID_DATA_LAKE_AUTH_CONFIG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Lake authorization config should specify either the IAM assumed role ARN or the Cloud"
          + " Provider Access roleId."),
  UNSUPPORTED_AUTH_TYPE_FOR_DATA_LAKE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Only users with SCRAM authentication can be scoped to Data Lakes."),
  UNSUPPORTED_AUTH_TYPE_FOR_SHARED_TIER(
      HttpServletResponse.SC_BAD_REQUEST,
      "LDAP authenticated users cannot be scoped to Shared Tier Clusters."),
  UNSUPPORTED_AWS_IAM_USER_FOR_SCOPED_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "AWS IAM users can only be scoped to clusters with MongoDB version 4.4 or higher."),
  UNSUPPORTED_OIDC_GROUP_FOR_SCOPED_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "OIDC groups can only be scoped to clusters with MongoDB version 7.0 or higher."),
  SCRAM_SHA1_UNSUPPORTED_FOR_GOV_CLOUD(
      HttpServletResponse.SC_BAD_REQUEST,
      "SCRAM-SHA-1 passwords are not supported for MongoDB for Government. The user %s's password"
          + " must use SCRAM-SHA-256."),
  ORG_SETTING_UNSUPPORTED_FOR_GOV(
      HttpServletResponse.SC_BAD_REQUEST,
      "Organization setting \"%s\" is not supported for"
          + " MongoDB for Government. Cannot update this setting."),
  SSH_ACCESS_CANNOT_BE_REQUESTED_FOR_SHARED_TIER(
      HttpServletResponse.SC_BAD_REQUEST,
      "SSH access key cannot be requested for shared tier clusters."),
  SSH_ACCESS_CANNOT_BE_REQUESTED_WITH_INVALID_REASON(
      HttpServletResponse.SC_BAD_REQUEST,
      "SSH access key cannot be requested with an invalid reason. Reason must be in the format of a"
          + " valid JIRA ticket, SFDC Case ID, or Intercom conversation ID."),
  SSH_ACCESS_ALREADY_GRANTED(
      HttpServletResponse.SC_BAD_REQUEST, "SSH access for this host %s is already granted."),
  INVALID_TENANT_BACKUP_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The `tenantBackupEnabled` field must be omitted or set to %s on %s."),
  FEDERATED_USER_ATTRIBUTES_CANNOT_BE_UPDATED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Federated User attributes cannot be updated as they are managed by the Identity Provider."),
  API_KEY_CREATED_GROUPS_MUST_HAVE_ORG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Projects created by API keys must belong to an existing organization."),
  HOST_ALREADY_EXISTS_IN_ANOTHER_PHYSICAL_HOST(
      HttpServletResponse.SC_BAD_REQUEST, "A host is already used in another physical host."),
  PHYSICAL_HOST_NAME_CANNOT_BE_EMPTY(
      HttpServletResponse.SC_BAD_REQUEST, "The physical host name must not be empty."),
  PHYSICAL_HOST_ALREADY_EXISTS(
      HttpServletResponse.SC_BAD_REQUEST, "A physical host with name %s already exists."),
  PHYSICAL_HOST_CONTAINS_DUPLICATE_HOST_ENTRIES(
      HttpServletResponse.SC_BAD_REQUEST, "The physical host contains duplicate entries."),
  PHYSICAL_HOST_CONTAINS_NON_EXISTENT_HOST(
      HttpServletResponse.SC_BAD_REQUEST, "A provided host does not exist."),
  PHYSICAL_HOST_RESTRICTED_USAGE_TYPE(
      HttpServletResponse.SC_BAD_REQUEST, "Physical hosts cannot be of usage type %s."),
  PHYSICAL_HOST_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "The physical host with id %s could not be found."),
  PHYSICAL_HOST_USAGE_TYPE_CONFLICT(
      HttpServletResponse.SC_BAD_REQUEST,
      "All hosts must have the same server type as the physical host."),
  HOST_CONTAINED_IN_PHYSICAL_HOST(
      HttpServletResponse.SC_BAD_REQUEST,
      "The host cannot be modified because it is contained in a physical host."),
  INVALID_PHYSICAL_HOST_ID(
      HttpServletResponse.SC_BAD_REQUEST, "The provided physical host id is invalid."),
  INVALID_ROLE_FOR_GLOBAL_KEY(
      HttpServletResponse.SC_BAD_REQUEST, "API Key Role %s is not in the allowlist."),
  @Hidden
  GLOBAL_ACCESS_LIST_ENTRY_REQUIRES_CIDR_BLOCK(
      HttpServletResponse.SC_BAD_REQUEST,
      "All global access list entries require a non-empty cidr block."),
  @Hidden
  GLOBAL_ACCESS_LIST_ENTRY_REQUIRES_DESCRIPTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "All global access list entries require a non-empty description."),
  @Hidden
  INVALID_GLOBAL_ACCESS_LIST_DESCRIPTION(
      HttpServletResponse.SC_BAD_REQUEST, "API key descriptions must be less than %s characters."),
  IPV6_PERMISSION_ENTRY_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "IPv6 addresses are not supported for the global access list entries."),
  @Hidden
  DUPLICATE_GLOBAL_ACCESS_LIST_ENTRY(
      HttpServletResponse.SC_BAD_REQUEST,
      "The provided IP address %s already exists in another global access list entry."),
  NO_CIDR_BLOCK_OR_DESCRIPTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Editing a global access list entry requires a cidr block and/or description."),
  USER_NOT_MANAGED_X509(
      HttpServletResponse.SC_BAD_REQUEST, "The user does not have managed mode X509 enabled."),
  CANNOT_GENERATE_CERT_IF_ADVANCED_X509(
      HttpServletResponse.SC_BAD_REQUEST,
      "The user cannot create managed X.509 certs if advanced mode is on."),
  CERT_EXPIRY_CANNOT_EXCEED_TWO_YEARS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas-generated X.509 certificates have a maximum expiration of 24 months."),
  CONNECTED_ORG_CONFIG_IDP_NOT_IN_FEDERATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified Identity Provider Id (%s) does not match an Identity Provider in this"
          + " Federation."),
  POST_AUTH_ROLE_GRANTS_MUST_CONTAIN_ORG_ROLES(
      HttpServletResponse.SC_BAD_REQUEST,
      "Post auth role grants can only consist of Organization roles."),
  ORG_ALREADY_ASSOCIATED_WITH_FEDERATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "An Organization can only be associated with a single Federation."),
  INVALID_ROLE_ASSIGNMENT(
      HttpServletResponse.SC_BAD_REQUEST,
      "The provided Role Assignment was invalid for the following reason (%s)."),
  DUPLICATE_ROLE_MAPPING(
      HttpServletResponse.SC_BAD_REQUEST,
      "A role mapping already exists for the given external group name."),
  IDENTITY_PROVIDER_ALREADY_ASSOCIATED_WITH_FEDERATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "An Identity Provider can only be associated with a single Federation."),
  INVALID_INTEGRATION_SETTINGS(
      HttpServletResponse.SC_BAD_REQUEST,
      "The provided settings for the %s integration were invalid: %s."),
  INVALID_INDEX_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST, "One or more index configurations are invalid. %s."),
  INDEX_BUILD_IN_PROGRESS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot initiate rolling index build while another index build is in progress."),
  LEGACY_SLACK_CONFIGURATION_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Legacy configurations for Slack are not permitted; OAuth2 must be configured via the UI."),
  SLACK_OAUTH2_NOT_CONFIGURED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Slack OAuth2 is not yet configured, and must be configured via the UI."),
  MUST_RECONFIGURE_SLACK_OAUTH2(
      HttpServletResponse.SC_BAD_REQUEST,
      "Slack API Tokens and Teams can only be modified by reconfiguring OAuth2 via the UI."),
  CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_MONGODB_VERSION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Legacy backup is not supported on clusters with MongoDB version 4.2 or higher."),
  CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_MONGODB_VERSION_UPGRADE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Legacy backup is not supported on clusters with MongoDB version 4.2 or higher. We recommend"
          + " that you switch to Cloud Backup along with your version upgrade. Your existing"
          + " legacy backup snapshots will still be kept until they expire."),
  CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_AWS_CLUSTERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Legacy backup is no longer supported for new AWS clusters."),
  CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_NEW_CLUSTERS(
      HttpServletResponse.SC_BAD_REQUEST, "Legacy backup is no longer supported for new clusters."),
  CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_SERVERLESS_INSTANCES(
      HttpServletResponse.SC_BAD_REQUEST,
      "Legacy backup is not supported for serverless instances."),
  CONTINUOUS_BACKUP_NOT_SUPPORTED_ON_CLUSTER_UPDATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Legacy backup is no longer supported for existing clusters."),
  CANNOT_USE_AWS_SECURITY_GROUP_WITHOUT_VPC_PEERING_CONNECTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot use aws security groups as an access list entry without a vpc peering connection."),
  AZURE_NVME_CREATION_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Azure NVMe creation is unsupported."),
  AZURE_PEERING_MULTIPLE_REGIONS_NOT_ALLOWED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Azure peering is only allowed in one Atlas Azure container per project."),
  AZURE_PEERING_MULTIPLE_REGIONS_NOT_ALLOWED_IN_DEPRECATED_PEERING_ONLY_MODE(
      HttpServletResponse.SC_BAD_REQUEST,
      "This project uses Peering-Only mode which limits peering to one Atlas Azure region. Disable"
          + " Peering-Only mode to use Azure multi-region peering."),
  DOMAIN_NOT_IN_ALLOW_LIST(
      HttpServletResponse.SC_BAD_REQUEST,
      "User cannot be added to Organization because the domain in the User's username is not in"
          + " the Organization's Allow List."),
  CLUSTER_HOSTNAMES_UNAVAILABLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster hostnames are unavailable; cluster (%s) might still be provisioning."),
  CLUSTER_MISSING_MESH_HOSTNAMES(
      HttpServletResponse.SC_BAD_REQUEST,
      "Mesh hostnames missing for cluster (%s); backfill pending."),
  INCORRECT_BACKUP_API_ENDPOINT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Incorrect Public API endpoint for Cloud Backup. Please use the following end point: (%s)."),
  CHECKPOINTS_ONLY_ON_CONTINOUS_BACKUP(
      HttpServletResponse.SC_BAD_REQUEST, "Checkpoints do not exist for Cloud Backup."),
  AWS_IAM_ROLE_IN_USE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot remove the AWS IAM role because it is still in use."),
  AZURE_SERVICE_PRINCIPAL_IN_USE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot remove the Azure Service Principal because it is still in use."),
  GCP_SERVICE_ACCOUNT_IN_USE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot remove the GCP Service Account because it is still in use."),
  CANNOT_CREATE_CN_REGIONS_ONLY_GROUP(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create AWS China regions-only project. The MongoDB Atlas in China Terms of Service"
          + " have not been accepted by this organization."),
  BACKUP_RESTORE_TO_CN_REGIONS_ONLY_GROUP_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Both the source and the target project must be an AWS China regions-only project."),
  INVALID_CONTAINER_REGION(
      HttpServletResponse.SC_BAD_REQUEST,
      "A %s project cannot have cloud provider containers in %s regions."),
  INVALID_PEER_REGION(
      HttpServletResponse.SC_BAD_REQUEST, "A %s project cannot have peers in %s regions."),
  CANNOT_DELETE_GROUP_INVITATION(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot delete an invitation to a group."),
  CANNOT_DELETE_PROJECT_INTEGRATION(
      HttpServletResponse.SC_BAD_REQUEST, "Project integration cannot be deleted. Reason: \"%s\"."),
  CANNOT_DELETE_ORG_INVITATION(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot delete an invitation to an organization."),
  ISOLATION_GROUP_IDS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Some of the isolation group IDs %s provided are invalid."),
  NOT_MTM_HOLDER(HttpServletResponse.SC_BAD_REQUEST, "Group %s is not an MTM holder."),
  MTM_CLUSTER_IN_USE(
      HttpServletResponse.SC_BAD_REQUEST, "MTM Cluster (%s, %s) is currently in use."),
  MTM_EXISTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified cluster (%s, %s) is already configured as an MTM."),
  SERVERLESS_POOL_NOT_VALID_FOR_MTM(
      HttpServletResponse.SC_BAD_REQUEST,
      "Serverless pool %s cannot be used with an MTM in group %s, for the specified provider and"
          + " region."),
  CANNOT_DELETE_ORG_ACTIVE_LIVE_MIGRATION_ATLAS_ORG_LINK(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot delete organization because it has an active live migration link with Atlas."),
  @Hidden
  CANNOT_DELETE_ORG_ACTIVE_LIVE_MIGRATION_OM_OR_CM_ORG_LINK(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot delete organization because it has an active live migration link with Ops Manager or"
          + " Cloud Manager."),
  DEVICE_AUTHORIZATION_PENDING(
      HttpServletResponse.SC_BAD_REQUEST,
      "The device authorization is pending. Please try again later."),
  DEVICE_AUTHORIZATION_EXPIRED(HttpServletResponse.SC_BAD_REQUEST, "The device code has expired."),
  DEVICE_AUTHORIZATION_CANCELLED(
      HttpServletResponse.SC_BAD_REQUEST, "The device code has been cancelled."),
  DEVICE_AUTHORIZATION_ALREADY_ACTIVATED(
      HttpServletResponse.SC_BAD_REQUEST, "The device code was already activated."),
  INVALID_REFRESH_TOKEN(
      HttpServletResponse.SC_BAD_REQUEST, "The refresh token is invalid or expired."),
  INVALID_CLIENT_ID(HttpServletResponse.SC_BAD_REQUEST, "The client_id is invalid."),
  MAX_GLOBAL_ZONES_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster exceeds project limit of %d zones in Global Clusters (has %d)."),
  INVALID_SEARCH_INDEX_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "The index name (%s) is invalid. Please use a valid index name."),

  TENANT_FTS_INDEX_TOO_LARGE(
      HttpServletResponse.SC_BAD_REQUEST, "A tenant search index may not exceed 3 KB in size."),

  TENANT_SEARCH_INDEX_TOO_LARGE(
      HttpServletResponse.SC_BAD_REQUEST, "A tenant search index may not exceed 3 KB in size."),

  MAXIMUM_INDEXES_FOR_TENANT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The maximum number of search indexes has been reached for this instance size."),
  SUPPORT_TICKET_REASON_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid support ticket reason %s: %s."),

  NO_FCV_PINNED(HttpServletResponse.SC_BAD_REQUEST, "No FCV pin found for Cluster."),
  MUST_EXTEND_PINNED_FCV_EXPIRATION_DATE(
      HttpServletResponse.SC_BAD_REQUEST, "The pinned FCV expiration date must be in the future."),
  PINNED_FCV_EXPIRATION_DATE_TOO_FAR_IN_FUTURE(
      HttpServletResponse.SC_BAD_REQUEST, "An FCV may only be pinned for up to 4 weeks."),
  PINNING_FCV_FOR_RAPID_RELEASE_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster version and/or release type not supported for FCV pinning."),
  CLUSTER_PAUSED_CANNOT_REQUEST_LOG(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot request log when the cluster is paused."),

  // HTTP 401 - Unauthorized
  NO_CURRENT_USER(HttpServletResponse.SC_UNAUTHORIZED, "No current user."),
  USER_UNAUTHORIZED(
      HttpServletResponse.SC_UNAUTHORIZED,
      "Current user is not authorized to perform this action."),
  NOT_SELF(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user is not the same as the user being modified."),
  NOT_USER_ADMIN(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user does not have the user administrator role for any group, team,"
          + " or organization containing user."),
  @Hidden
  NOT_GLOBAL_USER_ADMIN(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user does not have the global user administrator role."),
  @Hidden
  NOT_GLOBAL_USER_ADMIN_OR_SELF(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user is not the same as the user being modified and does not have"
          + " the global user administrator role."),
  NOT_GROUP_USER_ADMIN(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user does not have the user administrator role in group %s."),
  NOT_GROUP_CHARTS_ADMIN(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user does not have the charts administrator role in group %s."),
  NOT_ORG_GROUP_CREATOR(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user does not have the group creator role in organization %s."),
  NOT_ORG_OWNER(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user does not have the owner role in organization %s."),
  CLI_CANNOT_PROVIDE_ORG_OWNER_ID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas CLI cannot elect a custom Organization Owner on org creation."),
  CLI_CANNOT_ISSUE_API_KEY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas CLI cannot issue a new API key upon org creation."),
  CLI_CANNOT_ISSUE_SERVICE_ACCOUNT(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas CLI cannot issue a new Service Account upon org creation."),
  NOT_ORG_USER_ADMIN(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user does not have the user administrator role in organization %s."),
  ACCESS_LIST_ACCESS_DENIED(
      HttpServletResponse.SC_UNAUTHORIZED,
      "Cannot access access list for user %s, which is not currently logged in."),
  NOT_IN_GROUP(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The current user is not in the group, or the group does not exist."),
  BILLING_UNSUPPORTED(
      HttpServletResponse.SC_UNAUTHORIZED,
      "Billing administrator roles are not supported by Ops Manager."),
  PROVIDER_AUTH_FAILED(
      HttpServletResponse.SC_UNAUTHORIZED, "Account failed to authenticate with %s."),
  USER_CANNOT_ACCESS_GROUP(HttpServletResponse.SC_UNAUTHORIZED, "User cannot access this group."),
  USER_CANNOT_ACCESS_ORG(
      HttpServletResponse.SC_UNAUTHORIZED, "User cannot access this organization."),
  NOT_API_KEY_ADMIN(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The currently logged in user does not have user administrator role in organization or group"
          + " that API key with ID %s belongs to."),
  API_KEY_ACCESS_LIST_ACCESS_DENIED(
      HttpServletResponse.SC_UNAUTHORIZED,
      "API key access lists are only accessible by the API key or by a user administrator."),
  TENANT_SNAPSHOT_ACCESS_DENIED(
      HttpServletResponse.SC_UNAUTHORIZED, "Not authorized to access specified tenant snapshot."),

  // HTTP 402 - Payment Required
  ACCOUNT_SUSPENDED(
      HttpServletResponse.SC_PAYMENT_REQUIRED,
      "Organization has an unpaid invoice that is more than 30 days old."),
  CANNOT_START_BACKUP_NO_BILLING_INFO(
      HttpServletResponse.SC_PAYMENT_REQUIRED,
      "Cannot start backup without providing billing information."),
  NO_PAYMENT_INFORMATION_FOUND(
      HttpServletResponse.SC_PAYMENT_REQUIRED, "No payment information was found for group %s."),
  CANNOT_DELETE_ORG_FAILED_PAYMENTS(
      HttpServletResponse.SC_PAYMENT_REQUIRED,
      "Cannot delete organization because it has failed payments."),
  FAILED_TO_DELETE_ORG_CHARGE_FAILED(
      HttpServletResponse.SC_PAYMENT_REQUIRED, "Charge failed attempting to delete organization."),

  RESOURCE_SHARE_INVITATION_ALREADY_ACCEPTED(
      HttpServletResponse.SC_BAD_REQUEST, "Resource Share Invitation already accepted."),
  RESOURCE_SHARE_INVITATION_ALREADY_REJECTED(
      HttpServletResponse.SC_BAD_REQUEST, "Resource Share Invitation already rejected."),
  RESOURCE_SHARE_INVITATION_EXPIRED(
      HttpServletResponse.SC_BAD_REQUEST, "Resource Share Invitation has expired."),

  // HTTP 403 - Forbidden
  ACCESS_FORBIDDEN(
      HttpServletResponse.SC_FORBIDDEN, "Access to this resource is forbidden for current user."),
  ACCESS_FORBIDDEN_FOR_ATLAS_CN_REGIONS_ONLY_PROJECT(
      HttpServletResponse.SC_FORBIDDEN,
      "Access to this resource is forbidden for AWS China regions-only project: %s."),
  INVITATION_ONLY_MODE_OR_LDAP(
      HttpServletResponse.SC_FORBIDDEN,
      "Forbidden when using an LDAP backend or when the creating subsequent users beyond the first"
          + " in invitation-only mode."),
  IP_ADDRESS_NOT_ON_ACCESS_LIST(
      HttpServletResponse.SC_FORBIDDEN, "IP address %s is not allowed to access this resource."),
  IP_ADDRESS_NOT_ON_INHERITED_ORG_UI_ACCESS_LIST(
      HttpServletResponse.SC_FORBIDDEN, "IP address %s is not on inherited UI access list."),
  IP_ADDRESS_NOT_ON_ORG_UI_ACCESS_LIST(
      HttpServletResponse.SC_FORBIDDEN, "IP address %s is not on organization's UI access list."),
  ORG_REQUIRES_ACCESS_LIST(
      HttpServletResponse.SC_FORBIDDEN,
      "This organization requires access through an access list of ip ranges."),
  ORG_ACCESS_REQUIRES_CUSTOMER_GRANT(
      HttpServletResponse.SC_FORBIDDEN,
      "The customer has enabled employee access restrictions to Atlas infrastructure. "
          + " Please request the customer grant temporary permission to access resources for this"
          + " organization."),
  LOG_ACCESS_REQUIRES_CUSTOMER_GRANT(
      HttpServletResponse.SC_FORBIDDEN,
      "The customer has enabled employee access restrictions to Atlas infrastructure which"
          + " prevent access to the selected logs types. Please request the customer grant"
          + " temporary permission to access log for this organization.  The following log types"
          + " can be requested without permission: FTDC."),
  FEATURE_UNSUPPORTED(
      HttpServletResponse.SC_FORBIDDEN, "Feature not supported by current account level."),
  USER_NOT_SCOPED_TO_ACCESS_DATA_LAKE(
      HttpServletResponse.SC_FORBIDDEN,
      "User '%s' does not have scoped access to data lake '%s' in group %s."),
  USER_NOT_SCOPED_TO_ACCESS_CLUSTER(
      HttpServletResponse.SC_FORBIDDEN,
      "User '%s' does not have scoped access to cluster '%s' in group %s."),

  CANNOT_MODIFY_MANAGED_HOST(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot modify host %s because it is managed by Automation."),

  CANNOT_DELETE_LAST_ORG_OWNER(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot remove the last owner from the organization. If you are trying to close the"
          + " organization by removing all users and teams, please delete the organization"
          + " instead."),

  CANNOT_DELETE_LAST_ADMIN(
      HttpServletResponse.SC_FORBIDDEN,
      "An organization or project needs to have at least one owner. You cannot demote or remove"
          + " the last owner."),

  CANNOT_DELETE_LAST_FEDERATED_ORG(
      HttpServletResponse.SC_FORBIDDEN,
      "The last organization associated with an instance of federation settings cannot be deleted. "
          + "Instead, delete the federation settings as a whole."),

  NO_ROLES_IN_ASSIGNMENT(
      HttpServletResponse.SC_FORBIDDEN, "You cannot remove all project roles a team has."),
  CANNOT_REMOVE_ALL_USER_ROLES(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot remove all roles from a user. Please specify at least one role."),
  CANNOT_REMOVE_LAST_ORG_ROLE(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot remove the user's only organization-level role. Add the new role before removing the"
          + " old role, or use the 'Remove One MongoDB Cloud User from One Organization' endpoint"
          + " to remove the user from the organization entirely."),
  CANNOT_REMOVE_LAST_GROUP_ROLE(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot remove the user's only project-level role. Add the new role before removing the"
          + " old role, or use the 'Remove One MongoDB Cloud User from One Project' endpoint"
          + " to remove the user from the project entirely."),
  CANNOT_DEMOTE_LAST_ORG_OWNER(
      HttpServletResponse.SC_FORBIDDEN, "Cannot demote the last owner of the organization."),

  NO_FREE_TIER_API(
      HttpServletResponse.SC_FORBIDDEN,
      "The API is not supported for the Free Tier of Cloud Manager."),

  UNSUPPORTED_FOR_CURRENT_PLAN(
      HttpServletResponse.SC_FORBIDDEN, "Operation not supported for current plan."),

  UNSUPPORTED_SET_BACKUP_STATE(
      HttpServletResponse.SC_FORBIDDEN, "Setting the backup state to %s is not supported."),

  CANNOT_CHANGE_GROUP_NAME(
      HttpServletResponse.SC_FORBIDDEN, "Current user is not authorized to change group name."),

  CANNOT_DELETE_FROM_CLUSTER_SNAPSHOT(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot individually delete a snapshot that is part of a cluster snapshot."),

  ROLES_SPECIFIED_FOR_USER(HttpServletResponse.SC_FORBIDDEN, "Roles specified for user."),

  UNSUPPORTED_FOR_CURRENT_CONFIG(
      HttpServletResponse.SC_FORBIDDEN, "Operation not supported for current configuration."),
  @Hidden
  CANNOT_ADD_GLOBAL_ROLE(
      HttpServletResponse.SC_FORBIDDEN, "Adding a global role is not supported."),

  HOURLY_BILLING_LIMIT_EXCEEDED(
      HttpServletResponse.SC_FORBIDDEN, "The hourly billing limit has been exceeded."),
  NEW_ORG_CANNOT_LINK_TO_PAYING_ORG(
      HttpServletResponse.SC_BAD_REQUEST,
      "The new organization cannot be created because it cannot be linked to the paying "
          + "organization %s. To learn more, see: "
          + "https://www.mongodb.com/docs/atlas/billing/#configure-a-paying-organization or "
          + "contact Support."),
  NEW_ORG_FEDERATION_ID_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The new organization cannot be created because the specified federation ID is "
          + "invalid %s."),
  NEW_ORG_CANNOT_LINK_TO_FEDERATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The new organization cannot be created because it cannot be linked to specified"
          + " federation %s. The following error was experienced when linking the organization to"
          + " the federation: %s."),
  API_KEY_MUST_BE_ASSOCIATED_WITH_PAYING_ORG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas can't create a new organization unless the calling API key is associated with a paying"
          + " organization. To learn more, see:"
          + " https://www.mongodb.com/docs/atlas/billing/#configure-a-paying-organization in the"
          + " MongoDB Atlas documentation or contact Support."),
  SERVICE_ACCOUNT_MUST_BE_ASSOCIATED_WITH_PAYING_ORG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas can't create a new organization unless the calling Service Account is associated with"
          + " a paying organization. To learn more, see:"
          + " https://www.mongodb.com/docs/atlas/billing/#configure-a-paying-organization in the"
          + " MongoDB Atlas documentation or contact Support."),
  NEW_ORG_OWNER_MUST_BE_ATLAS_USER(
      HttpServletResponse.SC_FORBIDDEN,
      "The new organization owner must have access to the Atlas UI."),
  NEW_ORG_OWNER_MUST_BE_IN_SAME_ORG_AS_API_KEY(
      HttpServletResponse.SC_FORBIDDEN,
      "An organization owner must be in the same organization as your API key."),
  NEW_ORG_OWNER_MUST_BE_IN_SAME_ORG_AS_SERVICE_ACCOUNT(
      HttpServletResponse.SC_FORBIDDEN,
      "An organization owner must be in the same organization as your Service Account."),
  NEW_ORG_OWNER_MUST_BE_ORG_OWNER_IN_FEDERATION(
      HttpServletResponse.SC_FORBIDDEN,
      "The new organization owner must be an organization owner in an organization linked to the"
          + " specified federation %s."),
  GROUP_USERS_LIMIT_EXCEEDED(
      HttpServletResponse.SC_FORBIDDEN, "Groups can contain at most %d database users."),

  COLLECTION_ROLES_LIMIT_EXCEEDED(
      HttpServletResponse.SC_FORBIDDEN,
      "Exceeded maximum number of collection level permissions. This group can define at most %d"
          + " collection level roles."),

  CUSTOM_ROLES_LIMIT_EXCEEDED(
      HttpServletResponse.SC_FORBIDDEN, "Exceeded maximum number of custom roles."),

  MAX_PUBLIC_API_KEY_REACHED(
      HttpServletResponse.SC_FORBIDDEN,
      "You have reached the limit of max number of API keys. You can have at most %s API keys."),

  CROSS_REGION_NETWORK_PERMISSIONS_LIMIT_EXCEEDED(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot have more than %d cross-region network permissions."),

  ATLAS_NETWORK_PERMISSIONS_LIMIT_EXCEEDED(
      HttpServletResponse.SC_FORBIDDEN,
      "Too many network permission entries. Only %d entries are supported."),

  ATLAS_NETWORK_PERMISSIONS_SIZE_EXCEEDED(
      HttpServletResponse.SC_FORBIDDEN,
      "This project's IP access list is too large for tenant backups to be downloaded. Consider"
          + " restoring to a new cluster and using mongodump or contact support."),

  LIMIT_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "Limit not found."),

  INVALID_SET_LIMIT(HttpServletResponse.SC_BAD_REQUEST, "Invalid request to set limit. %s."),

  CANNOT_MODIFY_CHARTS_USER(HttpServletResponse.SC_FORBIDDEN, "Cannot modify the Charts user."),

  // This error code will be used when someone attempts to use the API for an embargoed group.
  // This error code should NOT be documented.
  PUBLIC_API_DISABLED(
      HttpServletResponse.SC_FORBIDDEN,
      "Public API access is not enabled for this group or organization."),

  PUBLIC_API_DISABLED_DELINQUENT_ORG(
      HttpServletResponse.SC_PAYMENT_REQUIRED,
      "Public API access isn't enabled for this group or organization because it is delinquent."),

  PUBLIC_API_DISABLED_LOCKED_ORG(
      HttpStatus.SC_LOCKED,
      "Public API access isn't enabled for this group or organization because it is locked."),

  @Hidden
  GLOBAL_USER_OUTSIDE_SUBNET(
      HttpServletResponse.SC_FORBIDDEN, "Global user is from outside allowed access list entries."),

  CANNOT_MODIFY_SNAPSHOT(
      HttpServletResponse.SC_FORBIDDEN, "Cannot modify snapshot %s because of invalid fields."),

  CANNOT_MODIFY_CLUSTER_SNAPSHOT(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot individually modify a snapshot %s that is part of a cluster snapshot."),

  CANNOT_RESTORE_TO_TARGET(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot restore with insufficient permission on source snapshot or target cluster."),

  INVALID_RESTORE_TO_TARGET(
      HttpServletResponse.SC_BAD_REQUEST, "Restore to target cluster is invalid. %s."),

  BACKUP_RESTORE_TO_GROUP_WITH_DIFFERENT_COMPLIANCE_LEVEL_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Both the source and the target project must have the same compliance level."),

  BACKUP_RESTORE_INCOMPATIBLE_TOPOLOGY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Source cluster topology does not match target cluster topology."),

  IAM_ROLE_CANNOT_READ_FROM_S3_BUCKET(
      HttpServletResponse.SC_BAD_REQUEST, "The IAM role cannot read from the S3 bucket."),

  IAM_ROLE_CANNOT_WRITE_TO_S3_BUCKET(
      HttpServletResponse.SC_BAD_REQUEST, "The IAM role cannot write to the S3 bucket."),

  IAM_ROLE_INVALID_SESSION_DURATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The IAM role session duration is not configured properly."),

  PUSH_BASED_LOG_EXPORT_NOT_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Push-based log export configuration is not enabled for group %s."),

  PUSH_BASED_LOG_EXPORT_ALREADY_UNCONFIGURED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Push-based log export configuration is not configured for group %s."),

  PUSH_BASED_LOG_EXPORT_ALREADY_CONFIGURED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Push-based log export configuration is already configured (current state = %s) for group %s."
          + " Push-based log export configuration can only be created from the \"UNCONFIGURED\""
          + " state."),

  PUSH_BASED_LOG_EXPORT_FEATURE_UNAVAILABLE(
      HttpServletResponse.SC_BAD_REQUEST, "Push-based log export feature is not available."),

  PUSH_BASED_LOG_EXPORT_UPDATE_IDENTICAL_TO_EXISTING_CONFIG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Push-based log export update is identical to existing config. No changes will be made."),

  API_KEY_CANNOT_CREATE_ORG(
      HttpServletResponse.SC_FORBIDDEN, "API keys cannot create organizations."),

  CANNOT_CREATE_FIRST_USER_USERS_ALREADY_EXIST(
      HttpServletResponse.SC_FORBIDDEN,
      "Cannot create the user: there are already users in the system."),

  DATA_LAKE_AUTH_TO_ATLAS_CLUSTER_GROUP_DOES_NOT_MATCH_TENANT_GROUP(
      HttpServletResponse.SC_FORBIDDEN,
      "The tenant group (%s), does not match the group of the cluster (%s) for which user access"
          + " has been requested."),

  DATA_LAKE_AUTH_TO_ATLAS_CLUSTER_ORG_DOES_NOT_MATCH_TENANT_ORG(
      HttpServletResponse.SC_FORBIDDEN,
      "The org (%s) of the cluster group is not in the same org (%s) of the tenant group for which"
          + " user access has been requested."),

  USER_ORGS_RESTRICTED(
      HttpServletResponse.SC_FORBIDDEN,
      "This user cannot create or join organizations becaues of federation organization"
          + " restrictions."),

  ATLAS_NETWORK_PERMISSION_ENTRY_NOT_EDITABLE(
      HttpServletResponse.SC_FORBIDDEN, "IP Address %s is not editable."),

  PROCESS_RESTART_REQUEST_UNSUPPORTED_FOR_REPLICA_SET_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Process of type %s cannot be restarted on a replica set of type %s."),

  PROCESS_RESTART_REQUEST_UNSUPPORTED_FOR_CLUSTER_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Process of type %s cannot be restarted on a cluster of type %s."),

  PROCESS_RESTART_REQUEST_NOT_PERMITTED(
      HttpServletResponse.SC_FORBIDDEN, "Restart of process with type %s is not permitted."),

  // HTTP 404 - Not Found
  MONGODB_BUILD_DOES_NOT_EXIST(
      HttpServletResponse.SC_NOT_FOUND, "The specified MongoDB build (%s) does not exist."),

  RESOURCE_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "Cannot find resource %s."),

  VALIDATION_RECORD_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "Cannot find validation record."),

  CLOUD_PROVIDER_CONTAINER_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "An invalid cloud provider container ID %s was specified."),

  INVALID_ALERT_CONFIG_ID(
      HttpServletResponse.SC_NOT_FOUND, "An invalid alert configuration ID %s was specified."),

  MISSING_ALERT_CONFIG_ID(HttpServletResponse.SC_NOT_FOUND, "No alert configuration ID was found."),

  INVALID_ALERT_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid alert ID %s was specified."),

  INVALID_CHECKPOINT_ID(
      HttpServletResponse.SC_NOT_FOUND, "An invalid checkpoint ID %s was specified."),

  INVALID_CLUSTER_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid cluster ID %s was specified."),

  INVALID_GROUP_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid group ID %s was specified."),

  MISSING_SOURCE_GROUP_ID(HttpServletResponse.SC_NOT_FOUND, "Source group ID was not specified."),

  INVALID_LOG_COLLECTION_JOB_ID(
      HttpServletResponse.SC_NOT_FOUND, "An invalid log collection job ID %s was specified."),

  INVALID_JOB_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid restore job ID %s was specified."),

  INVALID_KEY_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid key ID %s was specified."),

  INVALID_MACHINE_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid machine ID %s was specified."),

  INVALID_ORG_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid organization ID %s was specified."),

  INVALID_TEAM_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid team ID %s was specified."),

  INVALID_SNAPSHOT_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid snapshot ID %s was specified."),

  INVALID_SNAPSHOT_COMPLETION_STATUS(
      HttpServletResponse.SC_BAD_REQUEST, "Completed can only be 'true', 'false', or 'all'."),

  INVALID_USER_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid user ID %s was specified."),

  INVALID_PEER_ID(HttpServletResponse.SC_NOT_FOUND, "An invalid peer ID %s was specified."),

  INVALID_ACCESS_LIST_ID(
      HttpServletResponse.SC_NOT_FOUND, "An invalid global access list ID %s was specified."),

  INVALID_WINDOW_ID(
      HttpServletResponse.SC_NOT_FOUND, "An invalid maintenance window ID %s was specified."),

  INVITATION_DOES_NOT_EXIST_IN_GROUP(
      HttpServletResponse.SC_NOT_FOUND, "No invitation for username %s in group %s exists."),

  INVALID_INVITATION_ID(
      HttpServletResponse.SC_NOT_FOUND, "An invalid invitation ID %S was specified."),

  INVITATION_DOES_NOT_EXIST_IN_ORG(
      HttpServletResponse.SC_NOT_FOUND, "No invitation for username %s in organization %s exists."),

  CRASH_LOG_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No crash log file with ID %s exists."),

  GROUP_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No group with ID %s exists."),

  DATA_SOURCE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "The requested data source could not be found."),

  DATA_SET_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "The requested data set could not be found."),

  DATA_SET_OPERATION_NOT_ALLOWED(
      HttpServletResponse.SC_METHOD_NOT_ALLOWED,
      "The requested data set operation is not allowed."),

  CLOUD_PROVIDER_UNSUPPORTED_FOR_DATA_SET_ACCESS(
      HttpServletResponse.SC_BAD_REQUEST, "Provider %s is not supported for data set access."),

  DATA_SOURCE_SESSION_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "The requested data source session could not be found."),

  GROUP_NAME_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No group with name \"%s\" exists."),

  AGENT_API_KEY_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No group with API key %s exists."),

  ORG_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No organization with ID %s exists."),

  ORG_NAME_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No organization with name %s exists."),

  QUERY_SHAPE_HASH_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "The requested query shape hash %s could not be found."),

  EXPECTED_NON_ATLAS_ORG(
      HttpServletResponse.SC_NOT_FOUND, "The organization with id %s is an Atlas organization."),
  EXPECTED_PAYING_ATLAS_ORG(
      HttpServletResponse.SC_BAD_REQUEST,
      "The paying organization %s is not an Atlas organization; only Atlas organizations can create"
          + " new organizations."),
  PROVISION_MACHINE_JOB_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No provision machine job with ID %s exists in group %s."),

  PROVISIONED_MACHINE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No provisioned machine with ID %s exists in group %s."),

  HOST_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No host with ID %s exists in group %s."),

  HOST_ID_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No host with ID %s exists."),

  HOSTNAME_AND_PORT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No host with hostname and port %s exists in group %s."),

  INVALID_METRIC_NAME(HttpServletResponse.SC_NOT_FOUND, "An invalid metric name %s was specified."),

  NOT_CONFIG_SERVER(HttpServletResponse.SC_NOT_FOUND, "Host %s is not an SCCC config server."),

  ALERT_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No alert with ID %s exists in group %s."),

  @Hidden
  GLOBAL_ALERT_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No global alert configuration with ID %s exists."),

  ALERT_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No alert configuration with ID %s exists in group %s."),

  USER_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No user with ID %s exists."),

  USERNAME_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No user with username %s exists."),

  CANNOT_ADD_PENDING_USER(
      HttpServletResponse.SC_NOT_FOUND,
      "The user cannot be added to any group or teams in this organization"
          + "as it is a pending user/the user has not accepted the invite to this organization."),
  CLUSTER_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No cluster named %s exists in group %s."),
  CLUSTER_INVALID_STATE(
      HttpServletResponse.SC_NOT_FOUND,
      "Cluster is not in a valid state for the requested operation."),
  PUBLIC_HOSTNAME_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Public host addresses not found for cluster %s."),
  CLUSTER_NOT_FOUND_FOR_HOSTNAME(
      HttpServletResponse.SC_NOT_FOUND, "No cluster found for hostname %s."),
  CLUSTER_NOT_FOUND_FOR_UNIQUE_ID(
      HttpServletResponse.SC_NOT_FOUND, "No cluster found for unique ID %s."),

  SERVERLESS_INSTANCE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No serverless instance named %s exists in group %s."),

  TENANT_PRIVATE_ENDPOINT_MAX_PER_INSTANCE_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The maximum number of private endpoints for this instance (%s) has been exceeded."),

  CLUSTER_NAME_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No cluster with name \"%s\" exists in group %s."),

  SNAPSHOT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No snapshot with ID %s exists for cluster %s."),
  SNAPSHOT_VALIDATION_JOB_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No snapshot validation job with ID %s exists."),
  GENERATE_S3_PRE_SIGNED_URL_FAILED(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "Unable to generate an S3 pre-signed URL for job with ID %s."),
  SNAPSHOT_VALIDATION_S3_BUCKET_PREFIX_INVALID(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "No S3 bucket name prefix is defined for region %s."),
  UNABLE_T0_FIND_SNAPSHOT_PRIOR_TO_PIT_RESTORE_OPTIME(
      HttpServletResponse.SC_NOT_FOUND, "Unable to find snapshot prior to pit restore optime."),

  BACKUP_PIT_RESTORE_TIME_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Chosen Point in time timestamp invalid: %s."),

  FAILED_TO_GET_ATTACH_STATS_FOR_RESTORE(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "Could not get attach stats from automation logs with error %s."),
  CONFIG_SNAPSHOT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No snapshot with ID %s exists for config server %s."),

  RESTORE_JOB_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No restore job with ID %s exists for cluster %s."),

  RESTORE_JOB_NOT_FOUND_IN_GROUP(
      HttpServletResponse.SC_NOT_FOUND, "No restore job with ID %s exists in group %s."),

  SNAPSHOT_NOT_FOUND_IN_GROUP(
      HttpServletResponse.SC_NOT_FOUND, "No snapshot with ID %s exists in project %s."),

  SNAPSHOT_HISTORY_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No snapshot history with ID %s exists for cluster %s."),

  EXPORT_BUCKET_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No export Bucket with ID %s exists."),

  EXPORT_BUCKET_NAME_INVALID(HttpServletResponse.SC_BAD_REQUEST, "Export Bucket name is invalid."),

  EXPORT_BUCKET_DELETE_FAILED(
      HttpServletResponse.SC_BAD_REQUEST, "Failed to delete export Bucket with ID %s."),

  EXPORT_BUCKET_DELETE_FAILED_BUCKET_IN_USE(
      HttpServletResponse.SC_BAD_REQUEST, "Failed to delete export Bucket with ID %s. Reason: %s."),

  SELECTED_COPY_ZONE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "ReplicationSpecId in copySettings doesn't correspond to a valid zone in the cluster."),

  COPY_REGION_DUPLICATED(
      HttpServletResponse.SC_BAD_REQUEST,
      "More than one copy setting is copying to the same destination region."),

  INCOMPATIBLE_SHARDED_CLUSTER_SCHEMA(
      HttpServletResponse.SC_BAD_REQUEST,
      "The requested resource is using a sharded cluster schema incompatible with the current API"
          + " version."),

  SELECTED_COPY_CLOUD_PROVIDER_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Selected cloud provider in copy setting doesn't match the cloud provider of the highest"
          + " priority region of the replication spec."),

  SELECTED_COPY_REGION_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Copy setting cannot copy to same region as the highest priority region."),

  OVER_MAXIMUM_COPY_COUNT(
      HttpServletResponse.SC_BAD_REQUEST, "Each zone can only have at most 5 copy settings."),

  SELECTED_COPY_CLOUD_PROVIDER_NOT_AVAILABLE(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot copy snapshots for the selected cloud provider."),

  CANNOT_ENABLE_COPY_OPLOGS(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot copy oplogs when pit is not enabled."),

  CANNOT_ENROLL_EMPTY_FREQUENCY(
      HttpServletResponse.SC_BAD_REQUEST,
      "You must specify at least 1 frequency in the copy setting."),

  SELECTED_REGIONS_TO_DELETE_BACKUPS_INVALID_COMPARED_WITH_OLD_SETTING(
      HttpServletResponse.SC_BAD_REQUEST,
      "A region you attempted to delete backups from is not in your copy settings."
          + " You can only delete copied backups from regions you remove from your copy settings."),

  SELECTED_REGIONS_TO_DELETE_BACKUPS_INVALID_COMPARED_WITH_NEW_SETTING(
      HttpServletResponse.SC_BAD_REQUEST,
      "A region you attempted to delete backups from is still in your copy settings. To delete a"
          + " region's copied backups, you must also remove the region from your copy settings."),

  FEATURE_FLAG_NOT_IN_REQUIRED_ENV_STATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Feature flag %s is not in the required env state of %s."),

  FEATURE_FLAG_ALREADY_IN_DESIRED_STATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Feature flag %s is already in the desired state of \"enabled: %s\"."),

  CONFIG_RESTORE_JOB_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No restore job with ID %s exists for config server %s."),

  CHECKPOINT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No checkpoint with ID %s exists for cluster %s."),

  ACCESS_LIST_ENTRY_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "IP address %s not on access list for user %s."),

  ATLAS_NETWORK_PERMISSION_ENTRY_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "IP Address %s not on Atlas access list for group %s."),

  USER_ALREADY_IN_GROUP(HttpServletResponse.SC_BAD_REQUEST, "User %s is already in group %s."),

  USER_NOT_IN_GROUP(HttpServletResponse.SC_NOT_FOUND, "User %s is not in group %s."),

  USER_NOT_IN_ORG(HttpServletResponse.SC_NOT_FOUND, "User %s is not in organization %s."),

  USER_NOT_IN_TEAM(HttpServletResponse.SC_NOT_FOUND, "User %s is not in team %s."),

  BACKUP_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "No backup configuration exists for cluster %s in group %s."),

  SSH_KEY_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No SSH key with ID %s exists."),

  SSH_KEY_NAME_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No SSH key with name \"%s\" exists."),

  NO_SSH_KEYS_IN_GROUP(HttpServletResponse.SC_NOT_FOUND, "No SSH keys found in group %s."),

  PROVIDER_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No provider %s exists."),

  PROVIDER_UNSUPPORTED(HttpServletResponse.SC_NOT_FOUND, "Provider %s not currently supported."),

  PROVIDER_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No provider configuration exists for provider %s."),

  PROVIDER_CONFIG_ID_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "No provider configuration with ID %s exists for provider %s."),

  MAINTENANCE_WINDOW_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No maintenance window with ID %s exists in group %s."),

  AUTOMATION_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No automation configuration exists for group %s."),

  LAST_PING_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No last ping exists for group %s."),

  NOT_DATABASE_OR_DISK_METRIC(
      HttpServletResponse.SC_NOT_FOUND, "Metric %s is neither a database nor a disk metric."),

  DEVICE_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No device with name %s exists on host %s."),

  HOST_LAST_PING_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No last ping exists for host %s in group %s."),

  ATLAS_GROUP(
      HttpServletResponse.SC_NOT_FOUND,
      "Group %s is an Atlas group; use the Atlas Public API at /api/atlas/v1.0 to access it."),

  NOT_ATLAS_GROUP(
      HttpServletResponse.SC_NOT_FOUND,
      "Group %s is not an Atlas group; use the Cloud Manager Public API at /api/public/v1.0 to"
          + " access it."),

  INVALID_ATLAS_GROUP(
      HttpServletResponse.SC_NOT_FOUND,
      "Atlas group %s is in an invalid state and cannot be loaded."),

  NOT_ATLAS_ORG(
      HttpServletResponse.SC_NOT_FOUND,
      "Organization %s is not an Atlas organization; use the Cloud Manager Public API at"
          + " /api/public/v1.0 to access it."),

  ATLAS_GROUP_TAG_NOT_SUPPORTED(
      HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Atlas group %s is does not support the tag %s."),

  NO_BACKUP_ENCRYPTION_KEY_FOR_SHARDED_CLUSTER(
      HttpServletResponse.SC_NOT_FOUND,
      "No backup encryption key is available for a sharded cluster; keys can be accessed for each"
          + " shard individually."),

  PEER_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No peer with ID %s exists in project %s."),

  AZURE_CUSTOMER_NETWORK_UNREACHABLE(
      HttpServletResponse.SC_NOT_FOUND, "External Azure subscription unreachable."),

  BACKUP_DEPLOYMENT_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "Deployment ID %s not found."),
  INVALID_BACKUP_DEPLOYMENT_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid backup deployment name. It can only contain up to 64 letters, numbers, dashes,"
          + " underscores and periods."),
  INVALID_BACKUP_DEPLOYMENT_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST, "Backup deployment configuration is invalid due to: %s."),

  DAEMON_MACHINE_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Daemon machine config %s not found."),

  S3_SNAPSHOT_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "S3 snapshot config %s not found."),

  MONGO_SNAPSHOT_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Mongo snapshot config %s not found."),

  FILESYSTEM_SNAPSHOT_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Filesystem snapshot config %s not found."),

  S3_OPLOG_CONFIG_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "S3 oplog config %s not found."),

  MONGO_OPLOG_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Mongo oplog config %s not found."),

  MONGO_SYNC_CONFIG_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "Mongo sync config %s not found."),

  ROLE_REQUIRES_SSH_AUTHORIZATION(
      HttpServletResponse.SC_UNAUTHORIZED,
      "This role requires all SSH requests to gain MPA approval. Envoy and Stream nodes do not"
          + " support MPA at this time, access to these hostnames requires elevated privileges."),
  MANA_CREDENTIAL_FAILURE(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "The provided MANA credentials failed authorization. Please reach out to cloud engineering"
          + " for further support."),
  MANA_RESOURCE_NOT_FOUND(
      HttpStatus.SC_NOT_FOUND, "The requested resource could not be found within MANA: %s."),
  MANA_SVC_ERROR(
      HttpStatus.SC_INTERNAL_SERVER_ERROR,
      "The call to MANA experienced a service failure. Please reach out to cloud engineering for"
          + " further support."),
  MANA_UNEXPECTED_ATLAS_ERROR(
      HttpStatus.SC_INTERNAL_SERVER_ERROR,
      "The Atlas MANA client experienced a service failure. Please reach out to cloud engineering"
          + " for further support."),
  MPA_NOT_AUTHORIZED(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The requested action requires multi-party approval. A request for approval has been sent out"
          + " on your behalf."),
  MPA_IS_PENDING(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The requested action requires multi-party approval. The current request is still pending."),

  LDAP_VERIFY_CONNECTIVITY_REQUEST_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "LDAP connectivity verification request %s not found for group %s."),

  TEAM_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No team with ID %s exists."),

  TEAM_NAME_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No team with name \"%s\" exists."),

  TEAM_NOT_IN_ORG(HttpServletResponse.SC_NOT_FOUND, "Team %s is not in Organization %s."),

  TEAM_NOT_IN_GROUP(
      HttpServletResponse.SC_NOT_FOUND, "The team in your request is not assigned to this group."),

  CANNOT_DELETE_TEAM_ASSIGNED_TO_PROJECT(
      HttpServletResponse.SC_NOT_FOUND,
      "You cannot delete a team that has at least one project assigned to it. Make sure to remove"
          + " the team from all project before deleting it."),

  INVOICE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No invoice with ID %s exists in organization %s."),

  PENDING_INVOICE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No pending invoice exists in organization %s."),

  EVENT_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No event with ID %s exists for group %s."),

  ORG_EVENT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No event with ID %s exists for organization %s."),

  @Hidden
  GLOBAL_EVENT_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "No event with ID %s exists."),

  AWS_CUSTOMER_MASTER_KEY_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No AWS customer master key found for group %s."),

  ATLAS_CUSTOM_ROLE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "The specified custom db role %s does not exist."),

  LOG_COLLECTION_JOB_NOT_FOUND_IN_GROUP(
      HttpServletResponse.SC_NOT_FOUND, "No log collection job with ID %s exists in group %s."),

  DATA_EXPORT_METADATA_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No Data Export Metadata exists for S3 Object Key %s."),

  API_KEY_ACCESS_LIST_ENTRY_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "IP address %s not on access list for API key %s."),

  INVALID_PROCESS_TYPE(HttpServletResponse.SC_NOT_FOUND, "Invalid process type %s."),

  FEATURE_FLAG_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "Feature flag %s not found."),

  CLUSTER_X509_ACCESS_DURATION_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster X.509 certificate access duration may not exceed 1 hour."),

  DATA_LAKE_TENANT_NOT_FOUND_FOR_ID(
      HttpServletResponse.SC_NOT_FOUND, "Data Lake tenant with tenantId %s not found."),

  DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME(
      HttpServletResponse.SC_NOT_FOUND, "Data Lake tenant for project %s and name %s not found."),

  DATA_LAKE_TENANT_INVALID_STATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Lake tenant is not in a valid state <%s> for the requested operation."),

  DATA_LAKE_CANNOT_ACCESS_TEST_S3_BUCKET(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Lake cannot access the specified test S3 bucket (%s) via the provided IAM role. Ensure"
          + " that the IAM role provides access to read the bucket's contents. %s."),

  DATA_LAKE_CANNOT_GET_S3_BUCKET_REGION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Lake cannot determine the region for the specified test S3 bucket (%s) with the"
          + " provided IAM role. Ensure that the IAM role provides access to retrieve the bucket's"
          + " location. %s."),

  DATA_LAKE_CANNOT_ASSUME_ROLE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake cannot assume the specified role (%s)."),

  DATA_LAKE_CANNOT_ACCESS_AZURE_BLOB_CONTAINER_DURING_SETUP(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Federation is unable to access the necessary Azure Blob Container via"
          + " the specified Service Principal (%s). If you just configured access via your Service"
          + " Principal, it can take a few minutes to register in Azure. Please try again."),

  DATA_LAKE_CANNOT_ACCESS_AZURE_BLOB_CONTAINER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Federation is unable to access the specified Azure Blob Container (%s)."),

  AUTH_TO_ATLAS_NOT_SUPPORTED_FOR_MONGODB_VERSION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Auth to Atlas is not supported on cluster %s in group %s because the MongoDB version, %s,"
          + " is not supported."),

  CANNOT_CREATE_EXPORT_BUCKET_PROVIDER_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot create export bucket with cloud provider %s."),

  EXPORT_BUCKET_INVALID_BUCKET(
      HttpServletResponse.SC_BAD_REQUEST,
      "The bucket with name %s does not exist or is inaccessible from the role specified."),

  EXPORT_BUCKET_CONTAINS_PERIOD(
      HttpServletResponse.SC_BAD_REQUEST,
      "Bucket with name %s contains a period, which is not allowed."),

  EXPORT_BUCKET_INCOMPATIBLE_REGION(
      HttpServletResponse.SC_BAD_REQUEST, "The bucket is in an incompatible region."),

  EXPORT_BUCKET_DUPLICATED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Export bucket with roleId %s already exists for group %s."),

  CANNOT_EXPORT_SNAPSHOT_CONCURRENTLY(
      HttpServletResponse.SC_BAD_REQUEST, "An export is already in-progress for this snapshot."),

  MAX_EXPORTS_PER_GROUP_EXCEEDED(
      429, "Maximum number of exports per project (%s) exceeded while trying to perform export."),

  CANNOT_EXPORT_FALLBACK_SNAPSHOT(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot export a fallback snapshot."),

  CANNOT_EXPORT_SNAPSHOT_FROM_ANOTHER_CLUSTER_OR_GROUP(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot export a snapshot from another cluster or group."),

  CANNOT_EXPORT_SHARD_SNAPSHOT(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot export a shard snapshot."),

  INVALID_AZURE_SERVICE_URL(
      HttpServletResponse.SC_BAD_REQUEST,
      "Specified Azure Storage Container URL %s is invalid. Correct url should contain"
          + " 'blob.core.windows.net'. For example"
          + " 'https://account.blob.core.windows.net/container'."),

  TENANT_SNAPSHOT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No tenant snapshot with ID %s exists for cluster %s."),

  TENANT_RESTORE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No tenant restore with ID %s exists for cluster %s."),

  TENANT_CANNOT_DEFINE_ANALYZERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot define custom analyzers for a shared-tier cluster."),

  TENANT_ROLLING_INDEXES_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Rolling index builds are not supported on shared-tier clusters. Upgrade to Dedicated to use"
          + " this feature."),

  SERVERLESS_INSTANCE_CANNOT_DEFINE_ANALYZERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot define custom analyzers for a serverless instance."),

  @Hidden
  GLOBAL_ACCESS_LIST_ENTRY_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "No existing global access list entries match the provided id %s."),

  IDENTITY_PROVIDER_DOES_NOT_EXIST(
      HttpServletResponse.SC_NOT_FOUND,
      "The given 'oktaIdpId' does not correspond to an existing Identity Provider in Okta."),

  CERTIFICATE_DOES_NOT_EXIST(
      HttpServletResponse.SC_NOT_FOUND,
      "Certificate does not exist. " + "It may have expired or been revoked."),

  INTEGRATION_NOT_CONFIGURED(
      HttpServletResponse.SC_NOT_FOUND,
      "The integration of type '%s' is not configured for this group."),

  INVALID_INTEGRATION_TYPE(HttpServletResponse.SC_BAD_REQUEST, "Invalid integration type."),

  SAMPLE_DATASET_NOT_LOADED(
      HttpServletResponse.SC_NOT_FOUND, "A sample dataset with id %s was not found."),

  SAMPLE_DATASET_LOAD_IN_PROGRESS(
      HttpServletResponse.SC_BAD_REQUEST,
      "A sample dataset load is already in progress for cluster %s in group %s."),

  LIVE_IMPORT_IN_PROGRESS(
      HttpServletResponse.SC_BAD_REQUEST,
      "A live migration is already in progress for cluster %s in group %s."),

  MIGRATION_MISSING_PRIVATELINK_ID(
      HttpServletResponse.SC_BAD_REQUEST,
      "PrivateLink host schema type selected but PrivateLink id was not specified."),

  PRIVATELINK_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "A PrivateLink with id %s does not exist."),

  PRIVATE_ENDPOINT_SERVICE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Private endpoint service with id %s was not found."),

  PRIVATELINK_INTERFACE_ENDPOINT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "A PrivateLink interface endpoint with id %s does not exist."),

  PRIVATE_ENDPOINT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Private endpoint with id %s was not found."),

  PRIVATELINK_INTERFACE_ENDPOINT_ID_MALFORMED(
      HttpServletResponse.SC_BAD_REQUEST,
      "AWS interface endpoint ids must begin with 'vpce-' followed by a series of alphanumeric"
          + " characters."),

  PRIVATE_ENDPOINT_ID_MALFORMED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified private endpoint id %s is not formatted correctly. %s."),

  CANNOT_ENABLE_REGIONALIZED_PRIVATE_LINK(
      HttpServletResponse.SC_CONFLICT,
      "Cannot enable regionalized private link with replica-set clusters in the project."),

  CANNOT_DISABLE_REGIONALIZED_PRIVATE_LINK(
      HttpServletResponse.SC_CONFLICT,
      "Cannot disable regionalized private link when multiple private endpoint connections exist"
          + " across multiple regions in the project."),

  INSUFFICIENT_FREE_ADDRESS_SPACE_FOR_ADDITIONAL_PSC_REGION(
      HttpServletResponse.SC_CONFLICT,
      "There is not enough subnet space not in use to enable private service connect for this"
          + " region."),

  CANNOT_ASSUME_ROLE(
      HttpServletResponse.SC_BAD_REQUEST, "Atlas cannot assume the specified role (%s)."),

  CANNOT_AUTHENTICATE_SERVICE_PRINCIPAL(
      HttpServletResponse.SC_BAD_REQUEST,
      "Atlas cannot authenticate with the service principal (%s)."),

  CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Cloud provider access role (%s) not found."),

  SERVERLESS_MTM_POOL_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Serverless MTM pool %s not found."),

  SERVERLESS_MTM_PROFILE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Serverless MTM profile %s not found."),

  CLOUD_PROVIDER_ACCESS_ROLE_ARN_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Cloud provider access role with ARN (%s) not found."),

  CLOUD_PROVIDER_ACCESS_ROLE_NOT_AUTHORIZED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified Cloud Provider Access role (%s) has not been authorized."),

  CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified Azure Service Principal (%s) contains invalid tenant id or service principal"
          + " id."),

  CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "The specified Azure Service Principal (%s) not found."),

  CLOUD_PROVIDER_ACCESS_GCP_SERVICE_ACCOUNT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "The specified GCP Service Account (%s) not found."),

  DATA_LAKE_STORAGE_CONFIG_OUTDATED(
      HttpServletResponse.SC_CONFLICT,
      "Too many concurrent operations were made to modify federated database instances. Please try"
          + " again."),

  AWS_S3_NO_SUCH_KEY(
      HttpServletResponse.SC_NOT_FOUND, "No Such Key when reading from S3, details: %s."),
  OS_DOES_NOT_EXIST(HttpServletResponse.SC_NOT_FOUND, "OS version does not exist, details: %s."),
  OS_NOT_CACHED(HttpServletResponse.SC_NOT_FOUND, "OS version is not cached yet, details: %s."),
  AWS_S3_SERVICE_NOT_AVAILABLE(
      HttpServletResponse.SC_SERVICE_UNAVAILABLE, "AWS service is temporarily unavailable."),
  SIGNATURE_VERIFICATION_FAILED(
      HttpServletResponse.SC_CONFLICT, "Failed to verify the signature for key %s."),

  ATLAS_SEARCH_DEPLOYMENT_DOES_NOT_EXIST(
      HttpServletResponse.SC_NOT_FOUND, "Search deployment does not exist."),

  RESOURCE_SHARE_INVITATION_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Resource Share Invitation not found."),

  // HTTP 405 - Method not allowed
  ATLAS_BACKUP_CANCEL_SHARD_RESTORE_JOB_NOT_ALLOWED(
      HttpServletResponse.SC_METHOD_NOT_ALLOWED,
      "Cannot cancel a restore job of an individual shard. Please cancel the entire sharded"
          + " cluster restore job."),

  WRITES_NOT_ALLOWED_DURING_APP_UPGRADES(
      HttpServletResponse.SC_METHOD_NOT_ALLOWED,
      "Create, update, and delete API endpoints are disabled while the system is being upgraded."),

  NOT_GLOBAL_WRITES_CLUSTER(
      HttpServletResponse.SC_METHOD_NOT_ALLOWED,
      "Cannot update global writes for a cluster that is not configured for global writes."),

  ENDPOINT_NOT_SUPPORTED(
      HttpServletResponse.SC_METHOD_NOT_ALLOWED,
      "Use of endpoint is not allowed by Atlas at this time."),
  ENDPOINT_ONLY_SUPPORTS_ATLAS(
      HttpServletResponse.SC_METHOD_NOT_ALLOWED,
      "This endpoint is only supported by the Atlas Public API at /api/atlas/v1.0."),

  // HTTP 409 - Conflict
  INVALID_MONITORING_STATE(
      HttpServletResponse.SC_CONFLICT, "Monitoring data for this process is not available."),

  GROUP_ALREADY_EXISTS(HttpServletResponse.SC_CONFLICT, "A group with name \"%s\" already exists."),

  USER_ALREADY_EXISTS(HttpServletResponse.SC_CONFLICT, "A user with username %s already exists."),

  USER_ALREADY_HAS_ROLE(
      HttpServletResponse.SC_CONFLICT, "The user already has the requested role."),

  USER_DOES_NOT_HAVE_ROLE(
      HttpServletResponse.SC_NOT_FOUND, "The user does not have the requested role."),

  SSH_KEY_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT, "An SSH key with the name \"%s\" already exists."),

  CANNOT_MODIFY_SHARD_BACKUP_CONFIG(
      HttpServletResponse.SC_CONFLICT,
      "Cannot modify backup configuration for individual shard; use cluster ID %s for entire"
          + " cluster."),

  CANNOT_START_BACKUP_INVALID_STATE(
      HttpServletResponse.SC_CONFLICT,
      "Cannot start backup unless the cluster is in the INACTIVE or STOPPED state."),

  CANNOT_STOP_BACKUP_INVALID_STATE(
      HttpServletResponse.SC_CONFLICT,
      "Cannot stop backup unless the cluster is in the STARTED state."),

  CANNOT_TERMINATE_BACKUP_INVALID_STATE(
      HttpServletResponse.SC_CONFLICT,
      "Cannot terminate backup unless the cluster is in the STOPPED state."),

  CANNOT_GET_BACKUP_CONFIG_INVALID_STATE(
      HttpServletResponse.SC_CONFLICT,
      "Cannot get backup configuration without cluster being monitored."),

  UPGRADE_FOR_EXCLUDED_NAMESPACES(
      HttpServletResponse.SC_CONFLICT,
      "Excluded namespaces are not supported by this Backup Agent version; please upgrade."),

  UPGRADE_FOR_INCLUDED_NAMESPACES(
      HttpServletResponse.SC_CONFLICT,
      "Included namespaces are not supported by this Backup Agent version; please upgrade."),

  MISSING_SYNC_SOURCE(
      HttpServletResponse.SC_CONFLICT,
      "Requested changes will require a sync or resync, so a sync source must be provided."),

  CANNOT_SET_BACKUP_AUTH_FOR_MANAGED_CLUSTER(
      HttpServletResponse.SC_CONFLICT,
      "Username and password cannot be manually set for a managed cluster."),

  UPGRADE_FOR_CLUSTER_CHECKPOINT_INTERVAL(
      HttpServletResponse.SC_CONFLICT,
      "Cluster checkpoint interval not supported by this Backup Agent version; please upgrade."),

  CANNOT_CLOSE_GROUP_ACTIVE_BACKUP(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while it has active backups; please terminate all backups."),

  CANNOT_CLOSE_GROUP_ACTIVE_ATLAS_CLUSTERS(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while it has active clusters; please terminate all clusters."),

  CANNOT_CLOSE_GROUP_ACTIVE_ATLAS_DATA_LAKES(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while it has active federated database instances; please terminate all"
          + " federated database instances."),

  CANNOT_CLOSE_GROUP_ACTIVE_DATA_LAKE_PIPELINES(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while it has active data lake pipelines; please terminate all"
          + " data lake pipelines."),

  CANNOT_CLOSE_GROUP_ACTIVE_ATLAS_PRIVATE_ENDPOINT_SERVICES(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while it has active private endpoint services; please terminate all"
          + " private endpoint services."),

  CANNOT_CLOSE_GROUP_ACTIVE_ATLAS_DATA_FEDERATION_PRIVATE_ENDPOINTS(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while it has active Atlas Data Federation / Online Archive private"
          + " endpoints; please terminate all private endpoints."),

  CANNOT_CLOSE_GROUP_DISABLE_DELETE_PROJECT_POLICY_ACTIVE(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while the DISABLE_DELETE_PROJECT policy is active."),

  CANNOT_CLOSE_GROUP_MANAGED_DEPLOYMENTS(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group because there are active managed deployments."),

  CANNOT_CLOSE_GROUP_ACTIVE_CHARTS_APP(
      HttpServletResponse.SC_CONFLICT, "Cannot close group because there is an active charts app."),

  NO_GROUP_SSH_KEY(HttpServletResponse.SC_CONFLICT, "No group SSH key exists for group %s."),

  CANNOT_START_RESTORE_JOB_FOR_DELETED_SNAPSHOT(
      HttpServletResponse.SC_CONFLICT, "Cannot start restore job for deleted snapshot."),

  CANNOT_START_RESTORE_JOB_FOR_DELETED_CLUSTER_SNAPSHOT(
      HttpServletResponse.SC_CONFLICT, "Cannot start restore job for deleted cluster snapshot."),

  CANNOT_START_RESTORE_JOB_FOR_INCOMPLETE_CLUSTER_SNAPSHOT(
      HttpServletResponse.SC_CONFLICT, "Cannot start restore job for incomplete cluster snapshot."),

  CANNOT_DOWNLOAD_INCOMPLETE_SNAPSHOT(
      HttpServletResponse.SC_CONFLICT, "Cannot download incomplete cluster snapshot."),

  LINK_EXPIRATION_AFTER_SNAPSHOT_DELETION(
      HttpServletResponse.SC_CONFLICT,
      "Cannot set HTTP link expiration time after snapshot deletion time."),

  NO_CHECKPOINT_FOR_PIT_RESTORE(
      HttpServletResponse.SC_CONFLICT,
      "A suitable checkpoint could not be found for the specified point-in-time restore."),

  INVALID_RESTORE_PROCESS(
      HttpServletResponse.SC_CONFLICT, "Process is not valid for automated restore."),

  RESTORE_INITIATION_FAILED(
      HttpServletResponse.SC_CONFLICT, "Restore process initiation failed: %s."),

  INVALID_RESTORE_SNAPSHOT_FILTER_LIST(
      HttpServletResponse.SC_CONFLICT,
      "The requested restore could not be started. An associated snapshot either has a blacklist"
          + " or whitelist."),

  PROVISIONED_MACHINE_COULD_NOT_TERMINATE(
      HttpServletResponse.SC_CONFLICT,
      "Provisioned machine with ID %s could not terminate because a MongoDB process, Monitoring"
          + " Agent, or Backup Agent is currently running on the machine."),

  ADDRESS_ALREADY_IN_ACCESS_LIST(
      HttpServletResponse.SC_CONFLICT, "The address %s is already in the access list."),

  CANNOT_ROTATE_KEY_ENCRYPTION_DISABLED(
      HttpServletResponse.SC_CONFLICT,
      "Cannot rotate encryption key because encryption is disabled."),

  GET_ENCRYPTION_KEY_DISABLED_FOR_WT_CHECKPOINTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Getting the encryption key for 4.2+ Backups is no longer valid. Backups will follow your"
          + " deployment encryption settings. Please view the docs for more info:"
          + " https://docs.opsmanager.mongodb.com/current/reference/api/encryption-keys/."),

  ROTATE_ENCRYPTION_KEY_DISABLED_FOR_WT_CHECKPOINTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Rotate encryption key for 4.2+ Backups is no longer valid. Backups will follow your"
          + " deployment encryption settings. Please view the docs for more info:"
          + " https://docs.opsmanager.mongodb.com/current/reference/api/encryption-keys/."),

  OVERLAPPING_CIDR_BLOCK(
      HttpServletResponse.SC_CONFLICT,
      "The CIDR block %s overlaps with another peer's CIDR block."),

  OVERLAPPING_ATLAS_CIDR_BLOCK(
      HttpServletResponse.SC_CONFLICT, "The CIDR block %s overlaps with an Atlas CIDR block."),

  PEER_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT,
      "The peer with AWS Account ID %s and VPC ID %s already exists."),

  PEER_ALREADY_EXISTS_GCP(
      HttpServletResponse.SC_CONFLICT,
      "The peer with GCP Project ID %s and network name %s already exists."),

  PEER_ALREADY_EXISTS_AZURE(
      HttpServletResponse.SC_CONFLICT,
      "The peer with Azure Subscription ID %s and Virtual Network name %s already exists."),

  PEER_ALREADY_REQUESTED_DELETION(
      HttpServletResponse.SC_CONFLICT,
      "The peer with ID %s has already been requested for deletion."),

  PEER_INVALID_STATE(
      HttpServletResponse.SC_CONFLICT,
      "The peer with ID %s in group %s cannot be updated in its current state."),

  PEER_MAXIMUM_REACHED(
      HttpServletResponse.SC_CONFLICT,
      "The maximum number of peering connections (%s) has been reached."),

  PEER_CONTAINER_CANNOT_BE_MODIFIED(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot modify container for peer with ID %s."),

  CONTAINERS_IN_USE(HttpServletResponse.SC_CONFLICT, "Cannot modify in use containers. %s."),
  CONTAINER_BEING_DELETED(
      HttpServletResponse.SC_CONFLICT, "Cannot add cloud container as it's being deleted."),
  CANNOT_DELETE_RECENTLY_CREATED_CONTAINER(
      HttpServletResponse.SC_CONFLICT,
      "Cannot delete containers created less than " + "1 second ago. %s."),

  CANNOT_UPDATE_CONTAINER_WHILE_PROVISIONING(
      HttpServletResponse.SC_CONFLICT,
      "Container is currently being provisioned. Updating container is not supported until"
          + " provision plan is finished."),

  CONTAINER_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT, "A container already exists for group %s."),

  CONTAINER_WAITING_FOR_FAST_RECORD_CLEAN_UP(
      HttpServletResponse.SC_BAD_REQUEST,
      "A transient error occurred. Please try again in a minute or use a different name."),

  NO_CAPACITY(HttpServletResponse.SC_CONFLICT, "Cannot find the %s capacity for group %s."),

  OUT_OF_CAPACITY(
      HttpServletResponse.SC_CONFLICT,
      "The requested region is currently out of capacity for the requested instance size."),

  REGION_UNAVAILABLE(
      HttpServletResponse.SC_CONFLICT, "The selected region ([%s]%s) is unavailable for use."),

  MULTIPLE_GROUPS(
      HttpServletResponse.SC_CONFLICT, "Multiple groups exist with the specified name."),

  BACKUP_GROUP_MISCONFIGURED_ENV(
      HttpServletResponse.SC_CONFLICT, "Environment is incorrectly configured (%s)."),

  BACKUP_ACTIVE_BACKUP_JOBS(
      HttpServletResponse.SC_CONFLICT,
      "This group has active backup jobs, can't update deployment id."),

  BACKUP_S3_CONNECTION_FAILED(HttpServletResponse.SC_CONFLICT, "S3 connectivity problems: %s."),

  BACKUP_S3_VALIDATION_FAILED(HttpServletResponse.SC_CONFLICT, "S3 validation problems: %s."),

  BACKUP_S3_TOS_NOT_ACCEPTED(HttpServletResponse.SC_CONFLICT, "Must accept the terms of service."),

  BACKUP_MONGO_CONNECTION_FAILED(
      HttpServletResponse.SC_CONFLICT, "MongoDB connectivity problems: %s."),

  BACKUP_CANNOT_REMOVE_S3_STORE_CONFIG(
      HttpServletResponse.SC_CONFLICT,
      "Cannot delete the s3 snapshot config. Please make sure no jobs are bound to config "
          + "and no snapshots are in it before deleting."),

  BACKUP_CANNOT_REMOVE_S3_OPLOG_STORE_CONFIG(
      HttpServletResponse.SC_CONFLICT,
      "Cannot delete the s3 oplog config. Please make sure no jobs are bound to it."),

  BACKUP_CANNOT_REMOVE_OPLOG_STORE_CONFIG(
      HttpServletResponse.SC_CONFLICT,
      "Cannot delete the mongo oplog config. Please make sure no jobs are bound to it."),

  BACKUP_CANNOT_REMOVE_SYNC_STORE_CONFIG(
      HttpServletResponse.SC_CONFLICT,
      "Cannot delete the mongo sync config. Please make sure no jobs are bound to it."),

  BACKUP_CANNOT_REMOVE_DAEMON_CONFIG(
      HttpServletResponse.SC_CONFLICT,
      "Cannot delete the daemon config. Please make sure no jobs are bound to it."),

  BACKUP_CANNOT_REMOVE_BACKUP_DEPLOYMENT(
      HttpServletResponse.SC_CONFLICT, "Error deleting backup deployment: %s."),

  BACKUP_CANNOT_REMOVE_DB_STORE_CONFIG(
      HttpServletResponse.SC_CONFLICT,
      "Cannot delete the mongo snapshot config. Please make sure no jobs are bound to config "
          + "and no snapshots are in it before deleting."),

  BACKUP_CANNOT_REMOVE_FS_STORE_CONFIG(
      HttpServletResponse.SC_CONFLICT,
      "Cannot delete the file system snapshot config. Please make sure no jobs are bound to config "
          + "and no snapshots are in it before deleting."),

  BACKUP_CANNOT_MARK_WTC_BACKUP_AS_BROKEN(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid request to mark 4.2+ backup job as a broken job."),

  CLUSTER_ALREADY_PAUSED(
      HttpServletResponse.SC_CONFLICT, "Cannot pause a cluster that is already paused."),

  CANNOT_UPDATE_PAUSED_CLUSTER(
      HttpServletResponse.SC_CONFLICT,
      "Cannot update cluster %s while it is paused or being paused."),

  CANNOT_UPDATE_AND_PAUSE_CLUSTER(
      HttpServletResponse.SC_CONFLICT, "Cannot update and pause cluster %s at the same time."),

  CANNOT_PAUSE_CLUSTER_WITH_PENDING_CHANGES(
      HttpServletResponse.SC_CONFLICT, "Cannot pause a cluster with pending changes."),

  CANNOT_PAUSE_CLUSTER_WITH_OUTAGE_SIMULATION(
      HttpServletResponse.SC_CONFLICT, "Cannot pause cluster with outage simulation."),

  CANNOT_PAUSE_NVME_CLUSTER(HttpServletResponse.SC_CONFLICT, "Cannot pause an NVMe Cluster."),

  CANNOT_MODIFY_DISK_SIZE_FOR_NVME_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot modify disk size for an NVMe cluster."),

  CANNOT_MODIFY_DISK_TYPE_FOR_AZURE_NVME_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot modify disk type for Azure NVMe cluster."),

  ALL_NODES_MUST_HAVE_EQUAL_NVME_INSTANCE_SIZES(
      HttpServletResponse.SC_BAD_REQUEST, "All nodes must have the same NVMe instance size."),

  BASE_NODES_ASYMMETRIC_HARDWARE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create or edit cluster due to a configuration mismatch between the cluster's"
          + " electable and read-only nodes."),

  ANALYTICS_NODES_ASYMMETRIC_HARDWARE(
      HttpServletResponse.SC_BAD_REQUEST, "Analytics nodes' hardware must be symmetric."),

  ASYMMETRIC_REGION_TOPOLOGY_IN_ZONE(
      HttpServletResponse.SC_BAD_REQUEST,
      "All shards in the same zone must have the same region topology."),
  INVALID_INSTANCE_CLASS_CONFIGURATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create a cluster with asymmetric hardware where one instance class is NVME and the"
          + " other is not."),

  ORG_NOT_EMPTY(
      HttpServletResponse.SC_CONFLICT,
      "Organization cannot be deleted because it still contains active groups."),

  CANNOT_MODIFY_CLUSTER_WITH_RUNNING_LIVE_IMPORT(
      HttpServletResponse.SC_CONFLICT, "Cannot modify a cluster with a running live import."),

  CANNOT_MODIFY_SERVERLESS_INSTANCE_WITH_RUNNING_LIVE_IMPORT(
      HttpServletResponse.SC_CONFLICT,
      "Cannot modify a serverless instance with a running live import."),

  TEAM_NAME_NOT_AVAILABLE(
      HttpServletResponse.SC_CONFLICT, "Cannot create a team named %s in organization %s."),

  DATABASE_USERNAME_CANNOT_BE_CHANGED(
      HttpServletResponse.SC_CONFLICT, "Cannot modify the username of an existing database user."),

  DATABASE_NAME_CANNOT_BE_CHANGED(
      HttpServletResponse.SC_CONFLICT,
      "Cannot modify the authentication database of an existing database user."),

  AUTH_TYPE_CANNOT_BE_CHANGED(
      HttpServletResponse.SC_CONFLICT,
      "Cannot modify the authentication type of an existing database user."),

  TEAM_EXISTS_IN_GROUP(
      HttpServletResponse.SC_CONFLICT, "Cannot add a team that already exists in group."),

  USER_ALREADY_IN_TEAM(
      HttpServletResponse.SC_CONFLICT, "The user with ID %s in team %s already exists."),

  ATLAS_BACKUP_CONFLICTING_OPTIONS(
      HttpServletResponse.SC_CONFLICT,
      "Cannot turn backupEnabled and providerBackupEnabled on at the same time."),

  CANNOT_ENABLE_BACKUP_WITH_ACTIVE_LEGACY_BACKUP(
      HttpServletResponse.SC_CONFLICT,
      "Cannot enable backup when legacy backup is still active. Please turn off legacy backup via"
          + " the UI before continuing."),

  CANNOT_DISABLE_ENCRYPTION_AT_REST(
      HttpServletResponse.SC_CONFLICT,
      "Cannot disable Encryption at Rest on the group because it is still enabled on one or more"
          + " clusters in the group."),

  CANNOT_CHANGE_ENCRYPTION_AT_REST_PROVIDER(
      HttpServletResponse.SC_CONFLICT,
      "To choose a different Encryption at Rest provider, please disable and re-enable this"
          + " feature. This will decrypt your data and allow you to encrypt it with a different"
          + " Encryption at Rest provider."),

  UNSUPPORTED_FEATURE(HttpServletResponse.SC_CONFLICT, "This feature is not yet supported."),

  UNSUPPORTED_FEATURES_IN_USE_DURING_TENANT_UPGRADE_TO_SERVERLESS(
      HttpServletResponse.SC_CONFLICT,
      "You cannot upgrade %s from a free or shared-tier cluster to a serverless instance because"
          + " some of your deployment's features aren't supported. Learn more here %s."),
  ATLAS_CUSTOM_ROLE_NAME_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT, "A custom role with the name %s already exists."),

  ATLAS_CUSTOM_ROLE_IN_USE_BY_USERS(
      HttpServletResponse.SC_CONFLICT,
      "Deleting specified custom role would leave the following users without a role: %s."),

  ATLAS_CUSTOM_ROLE_IN_USE_BY_ROLES(
      HttpServletResponse.SC_CONFLICT,
      "Deleting specified custom role would leave the following roles with no actions or inherited"
          + " roles: %s."),

  AUTOMATION_CONFIG_CONCURRENT_MODIFICATION(
      HttpServletResponse.SC_CONFLICT, "Another session or user has already published changes."),

  DATA_LAKE_TENANT_NAME_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT, "A Data Lake tenant with the name %s already exists."),

  ATLAS_CLUSTER_RESTORE_IN_PROGRESS(
      HttpServletResponse.SC_CONFLICT, "Cluster restore already in progress."),

  INTEGRATION_ALREADY_CONFIGURED(
      HttpServletResponse.SC_CONFLICT,
      "The integration of type '%s' is already configured for this group."),

  PRIVATELINK_ENDPOINT_SERVICE_ALREADY_EXISTS_FOR_REGION(
      HttpServletResponse.SC_CONFLICT,
      "A PrivateLink Endpoint Service already exists for AWS region %s."),

  PRIVATE_ENDPOINT_SERVICE_ALREADY_EXISTS_FOR_REGION(
      HttpServletResponse.SC_CONFLICT,
      "A private endpoint service already exists for %s region %s."),

  PRIVATELINK_INTERFACE_ENDPOINT_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT,
      "The PrivateLink already contains an interface endpoint with the specified id %s."),

  PRIVATE_ENDPOINT_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT,
      "The private endpoint service already contains the endpoint with the specified id %s."),

  PRIVATELINK_INCOMPATIBLE_TOO_MANY_NODES(
      HttpServletResponse.SC_CONFLICT,
      "Project has too many addressable nodes in region %s to use PrivateLink."),

  PRIVATE_ENDPOINT_SERVICE_INCOMPATIBLE_TOO_MANY_NODES(
      HttpServletResponse.SC_CONFLICT,
      "Project has too many addressable nodes in region %s to use the private endpoint service."),

  MULTIPLE_PRIVATE_ENDPOINTS_RESTRICTED_TO_SINGLE_REGION(
      HttpServletResponse.SC_CONFLICT,
      "Projects with multiple private endpoints in the same region cannot support private"
          + " endpoints in other regions."),

  CLUSTER_INCOMPATIBLE_WITH_EXISTING_PRIVATELINK(
      HttpServletResponse.SC_CONFLICT,
      "The requested operation would result in more addressable nodes in region %s than can be"
          + " supported by the private endpoint service. The maximum number supported is %s."),

  CANNOT_DELETE_PRIVATELINK_WITH_ENDPOINTS(
      HttpServletResponse.SC_CONFLICT,
      "A PrivateLink with endpoints cannot be deleted. Please delete all endpoints and retry."),

  CANNOT_DELETE_PRIVATE_ENDPOINT_SERVICE_WITH_ENDPOINTS(
      HttpServletResponse.SC_CONFLICT,
      "A private endpoint service with endpoints attached cannot be deleted. Please delete all"
          + " endpoints."),

  PRIVATELINK_CANNOT_ACCEPT_ENDPOINTS(
      HttpServletResponse.SC_CONFLICT,
      "The PrivateLink with the specified id %s is not ready to accept endpoint connection"
          + " requests."),

  PRIVATE_ENDPOINT_SERVICE_CANNOT_ACCEPT_ENDPOINTS(
      HttpServletResponse.SC_CONFLICT,
      "The private endpoint service with the specified id %s is not ready to accept endpoint"
          + " connection requests."),

  PRIVATELINK_DELETE_REQUESTED(
      HttpServletResponse.SC_CONFLICT,
      "The PrivateLink with id %s has been deleted and cannot accept endpoint connection"
          + " requests."),

  PRIVATE_ENDPOINT_SERVICE_DELETE_REQUESTED(
      HttpServletResponse.SC_CONFLICT,
      "The private endpoint service with the specified id %s has been deleted and cannot accept"
          + " endpoint connection requests."),

  PRIVATE_ENDPOINT_SERVICE_FORWARDING_RULES_MISSING(
      HttpServletResponse.SC_CONFLICT,
      "The private endpoint service does not have forwarding rules set up for every service"
          + " attachment."),

  DUPLICATE_ONLINE_ARCHIVE(
      HttpServletResponse.SC_CONFLICT,
      "An online archive was provisioned for this cluster before the cluster was deleted and"
          + " recreated with the same name. A new online archive cannot yet be created for this"
          + " cluster."),

  DUPLICATE_IAM_ASSUMED_ROLE_ARN(
      HttpServletResponse.SC_CONFLICT,
      "An AWS IAM Role has already been configured for the specified IAM Role ARN (%s)."),

  DUPLICATE_AZURE_SERVICE_PRINCIPAL(
      HttpServletResponse.SC_CONFLICT,
      "An Azure Service Principal has already been configured for the specified Azure Service"
          + " Principal ID (%s)."),

  SNAPSHOT_RESTORE_VERSION_MISMATCH(
      HttpServletResponse.SC_CONFLICT,
      "The specified snapshot and target project have mismatched versions of MongoDB."),

  SNAPSHOT_RESTORE_CUSTOM_DEFAULT_WRITE_CONCERN_MISMATCH(
      HttpServletResponse.SC_CONFLICT,
      "Can not restore a snapshot with a custom default write concern to a cluster that does not."),
  ATLAS_FTS_DEPLOYMENT_CONFLICT(
      HttpServletResponse.SC_CONFLICT,
      "Operation conflicts with current search deployment status: %s."),
  ATLAS_SEARCH_DEPLOYMENT_CONFLICT(
      HttpServletResponse.SC_CONFLICT,
      "Operation conflicts with current search deployment status: %s."),
  ATLAS_SEARCH_INDEX_CONFLICT(
      HttpServletResponse.SC_CONFLICT, "Operation conflicts with current search index status: %s."),
  ATLAS_SEARCH_DUPLICATE_INDEX(
      HttpServletResponse.SC_CONFLICT,
      "An index named \"%s\" is already defined for collection %s. "
          + "Index names must be unique for a source collection and all its views."),
  ATLAS_SEARCH_DEPLOYMENT_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT, "Search deployment already exists for specified cluster."),

  // HTTP 410 - Gone
  PERSONAL_API_KEYS_DEPRECATED(
      HttpServletResponse.SC_GONE,
      "You can no longer create Personal API Keys. Please create a Programmatic API Key."),

  V1_API_END_OF_LIFE_ORG(
      HttpServletResponse.SC_GONE,
      "Atlas API v1/v1.5 is no longer available for organizations created after %s. "
          + "Please migrate to v2 API. Migration guide: %s."),
  V1_API_END_OF_LIFE_PROJECT(
      HttpServletResponse.SC_GONE,
      "Atlas API v1/v1.5 is no longer available for projects created after %s. "
          + "Please migrate to v2 API. Migration guide: %s."),

  // HTTP 429 - Too Many Requests
  RATE_LIMITED(429, "Resource %s is limited to %s requests every %s minutes."),
  RATE_LIMITED_NO_REASON(429, "Rate limits are exceeded."),

  // HTTP 500 - Internal Server Error
  UNEXPECTED_ERROR(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Unexpected error."),

  PROVISIONING_FAILED_FROM_PROVIDER(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "Unable to retrieve configuration options from the provider."),

  CANNOT_GET_VOLUME_SIZE_LIMITS(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "Cannot get volume size limits for volume type %s."),

  CANNOT_DECREASE_DISK_SIZE(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "Not enough data to determine if decreasing disk size is safe."),

  TIMEOUT_WHILE_PAGING(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "Unable to calculate the total number of results. Retry with includeCount=false."),

  TIMEOUT(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Request Timeout."),

  // Use 500 as the error code to prevent breaking API changes
  CONCURRENT_IP_ACCESS_LIST_MODIFICATION_NOT_SUPPORTED(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "IP access list modifications must be made synchronously."),

  /** PRIVATE API ERROR CODES - DO NOT DOCUMENT THESE!!! */

  // HTTP 500 - Internal Server Error
  AWS_BILLING_ACCOUNT_DOESNT_EXIST(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "AWS Billing Account doesn't exist."),

  GCP_ORGANIZATION_DOESNT_EXIST(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "GCP Organization is not found."),

  AWS_REQUEST_LIMIT_EXCEEDED(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "AWS request limit exceeded."),

  DATA_EXPORT_SERVICE_UNAVAILABLE(
      HttpServletResponse.SC_SERVICE_UNAVAILABLE,
      "AWS credentials and/or bucket name for data export are not specified."),

  CLUSTER_DELETE_REQUESTED(HttpServletResponse.SC_BAD_REQUEST, "User requested cluster delete."),

  NAMESPACE_HAS_ONLINE_ARCHIVE(
      HttpServletResponse.SC_CONFLICT,
      "An active or pending online archive exists for this namespace."),

  ONLINE_ARCHIVE_MUST_BE_ACTIVE_TO_PAUSE(
      HttpServletResponse.SC_CONFLICT,
      "An archive must be in an active state in order to be paused."),

  ONLINE_ARCHIVE_CANNOT_MODIFY_FIELD(
      HttpServletResponse.SC_BAD_REQUEST,
      "The request contained modifications to immutable fields."),

  ONLINE_ARCHIVE_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT,
      "An online archive with the same configuration already exists."),

  INVALID_SF_OPPORTUNITY_LICENSE_KEY_PROVIDED(
      HttpServletResponse.SC_NOT_FOUND, "Invalid Salesforce opportunity license key provided."),

  INSUFFICIENT_DISK_SPACE_ON_REMAINING_SHARDS(
      HttpServletResponse.SC_BAD_REQUEST,
      "One or more shards are being removed that consume more disk space than that available on"
          + " the remaining shards."),

  CANNOT_DECREASE_DISK_SIZE_DURING_SHARD_REMOVAL(
      HttpServletResponse.SC_BAD_REQUEST,
      "Disk size cannot be decreased as shards are being removed."),

  ONLINE_ARCHIVE_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST, "Online archive limit for cluster exceeded."),

  ONLINE_ARCHIVE_PARTITION_FIELDS_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST, "Online archive partition fields limit exceeded. %s."),

  ONLINE_ARCHIVE_NOT_AVAILABLE_TENANT_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Online archive is only available for M10+ dedicated clusters."),

  ACTIVE_ONLINE_ARCHIVE_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST, "Active online archive limit for cluster exceeded."),

  ONLINE_ARCHIVE_NO_PARTITION_FIELDS(
      HttpServletResponse.SC_BAD_REQUEST, "Online archive must have at least one partition field."),

  DATA_LAKE_STORAGE_REGION_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Specified region is not supported in Data Lake Storage."),

  ONLINE_ARCHIVE_DATE_CRITERIA_NOT_IN_PARTITION(
      HttpServletResponse.SC_BAD_REQUEST, "Date criteria is not specified in partition fields."),

  ONLINE_ARCHIVE_CANNOT_DETERMINE_DL_REGION_FOR_MULTI_REGION_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Online Archive data lake region could not be determined for multi region cluster."),

  ONLINE_ARCHIVE_DATA_PROCESS_REGION_CLUSTER_CLOUD_PROVIDER_MISMATCH(
      HttpServletResponse.SC_BAD_REQUEST,
      "Online archive only supports data process region with the same provider as cluster."),

  ONLINE_ARCHIVE_CONCURRENT_MODIFICATIONS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Too many concurrent operations were made to modify Online Archives. Please try again."),

  ONLINE_ARCHIVE_CROSS_CLOUD_PROVIDER_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Online archive only supports one cloud provider per cluster."),

  DATA_LAKE_STORAGE_CLOUD_PROVIDER_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Specified cloud provider is not supported in Data Lake Storage."),

  INTERNAL_DATA_LAKE(
      HttpServletResponse.SC_BAD_REQUEST,
      "This data lake was provisioned internally and cannot be fetched or modified through the"
          + " API."),

  INVALID_INTERNAL_DATALAKE_DATASET_CREDENTIALS_REQUEST(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid data lake dataset credentials request: %s."),

  MULTI_CLOUD_CLUSTER_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Multi-Cloud clusters are not supported by the v1.0 API. Please use the v1.5 API instead."
          + " Documentation for the v1.5 API is available at"
          + " https://docs.atlas.mongodb.com/reference/api/clusters-advanced/."),

  ASYMMETRIC_HARDWARE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "Asymmetric hardware is not supported by the v1.0 API. Please use the v1.5 API instead."
          + " Documentation for the v1.5 API is available at"
          + " https://docs.atlas.mongodb.com/reference/api/clusters-advanced/."),
  ASYMMETRIC_SHARD_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Asymmetric sharded cluster is not supported by the current API version. Please use the"
          + " latest API instead. Documentation for the latest API is available at"
          + " https://docs.atlas.mongodb.com/reference/api/clusters-advanced/."),

  ASYMMETRIC_SHARD_BACKUP_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Asymmetric sharded cluster backup schedule is not supported by the current API version."
          + " Please use the latest API instead. Documentation for the latest API is available at"
          + " https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Cloud-Backups/."),

  CLOUD_PROVIDER_ACCESS_EXTERNAL_ID_EXCLUDED_FROM_AWS_IAM_ROLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The externalId (%s) is missing from the trust relationship of the specified role."),

  TARGET_SERVERLESS_MTM_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST,
      "Could not find an available serverless MTM to migrate to."),

  CANNOT_UPDATE_SMALL_CONTAINER(
      HttpServletResponse.SC_BAD_REQUEST,
      "GCP container %s is a small container and cannot be updated."),

  REGION_CONFIG_REQUIRES_HARDWARE_SPECS(
      HttpServletResponse.SC_BAD_REQUEST,
      "At least one of either electableSpecs, readOnlySpecs, or analyticsSpecs must be specified"
          + " for a regionConfig."),

  INVALID_REGION_CONFIG_NODE_COUNT(
      HttpServletResponse.SC_BAD_REQUEST,
      "There are one or more invalid regionConfigs. Every regionConfig must have at least 1 node."),

  REPLICATION_SPECS_REQUIRES_ELECTABLE_SPECS(
      HttpServletResponse.SC_BAD_REQUEST,
      "A replication spec requires at least three electable nodes."),

  INSTANCE_SIZES_MUST_MATCH(
      HttpServletResponse.SC_BAD_REQUEST,
      "All instance sizes specified in the hardware specs of a cluster must match."),

  INVALID_ASYMMETRIC_INSTANCE_SIZE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot have more than two instance sizes when using asymmetric hardware."),

  INSTANCE_SIZE_INVALID_FOR_PROVIDERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "The instance size %s is not available for the combination of cloud providers in your"
          + " cluster."),

  INVALID_INSTANCE_SIZE_RANGE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The difference between the smallest and largest electable instance sizes is greater than the"
          + " allowed range of %s."),

  ANALYTICS_INSTANCE_SIZE_MUST_MATCH(
      HttpServletResponse.SC_BAD_REQUEST, "Analytics nodes must all have the same instance size."),

  BASE_INSTANCE_SIZE_MUST_MATCH(
      HttpServletResponse.SC_BAD_REQUEST,
      "Electable and read-only nodes must all have the same instance size."),

  INSTANCE_SIZE_MUST_MATCH_IN_REGION_CONFIGS(
      HttpServletResponse.SC_BAD_REQUEST,
      "All electable nodes within each shard must use the same instance size. Found electable nodes"
          + " with mismatched sizes: %s."),

  READ_ONLY_NODE_SIZE_MUST_MATCH_IN_REGION_CONFIGS(
      HttpServletResponse.SC_BAD_REQUEST,
      "All read-only nodes within each shard must use the same instance size. Found read-only nodes"
          + " with mismatched sizes: %s."),

  ANALYTICS_NODE_SIZE_MUST_MATCH_IN_REGION_CONFIGS(
      HttpServletResponse.SC_BAD_REQUEST,
      "All analytics nodes within each shard must use the same instance size. Found analytics nodes"
          + " with mismatched sizes: %s."),

  AUTO_SCALINGS_MUST_BE_IN_EVERY_REGION_CONFIG(
      HttpServletResponse.SC_BAD_REQUEST,
      "If any regionConfigs specify an autoScaling object, all regionConfigs must also specify an"
          + " autoScaling object."),

  AUTO_SCALINGS_MUST_MATCH(
      HttpServletResponse.SC_BAD_REQUEST,
      "All autoScaling objects specified in the regionConfigs of a cluster must match."),

  ANALYTICS_AUTO_SCALINGS_MUST_MATCH(
      HttpServletResponse.SC_BAD_REQUEST,
      "All analyticsAutoScaling objects specified in the regionConfigs of a cluster must match."),

  ASYMMETRIC_BASE_AUTO_SCALING_REQUIRES_SINGLE_FAMILY_CLASS(
      HttpServletResponse.SC_BAD_REQUEST,
      "To enable Auto Scaling all Electable and Read-Only instance sizes must have the same"
          + " instance size class. Provided: Instance Sizes [%s], Instance Family Classes: [%s]."),
  ASYMMETRIC_ANALYTICS_AUTO_SCALING_REQUIRES_SINGLE_FAMILY_CLASS(
      HttpServletResponse.SC_BAD_REQUEST,
      "To enable Analytics Auto Scaling all Analytics instance sizes must have the same"
          + " instance size class. Provided: Instance Sizes [%s], Instance Family Classes: [%s]."),

  AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Specified autoScaling limits ([%s, %s]) refer to instance sizes incompatible with"
          + " provisioned IOPS configurations."),

  ANALYTICS_AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Specified analyticsAutoScaling limits ([%s, %s]) refer to instance sizes incompatible"
          + " with provisioned IOPS configurations."),

  CANNOT_PERFORM_RESTORE_ON_SERVERLESS_INSTANCE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Restoring from a dedicated cluster to a serverless instance is not allowed."),

  CANNOT_SPECIFY_MONGODB_MAJOR_VERSION_WITH_CONTINUOUS_DELIVERY(
      HttpServletResponse.SC_BAD_REQUEST,
      "A MongoDB major version cannot be specified when Atlas Continuous Delivery is enabled."),

  CANNOT_ENABLE_CONTINUOUS_DELIVERY(
      HttpServletResponse.SC_BAD_REQUEST,
      "In order to enable Continuous Delivery a cluster must be running the most recent LTS"
          + " mongoDB version."),

  CANNOT_DISABLE_CONTINUOUS_DELIVERY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot switch to the LTS version system because the current Continuous Delivery release is"
          + " not an LTS version."),

  DST_ROOT_CERT_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot switch to use DST certificates in Atlas clusters."),

  CANNOT_CREATE_FREE_CLUSTER_VIA_PUBLIC_API(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot create a cluster with instance size M0 via the public api: %s."),

  SHARDED_CLUSTERS_INCOMPATIBLE_WITH_DEVICE_SYNC_OR_PREIMAGE_TRIGGERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot convert replica set to sharded cluster when Device Sync or Triggers that include"
          + " document preimages are enabled."),

  SHARED_CLUSTERS_INCOMPATIBLE_WITH_REALM_SYNC(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot upgrade shared cluster while Realm Sync is setup.  Before you upgrade, first"
          + " terminate Realm Sync. Then, you can upgrade your shared tier cluster. Afterward, you"
          + " can re-enable Realm Sync."),

  INVALID_APP_SERVICE_ID(HttpServletResponse.SC_BAD_REQUEST, "Invalid App Service ID."),

  ASSIGNED_CUSTOM_ROLES_LIMIT_EXCEEDED(
      HttpServletResponse.SC_FORBIDDEN, "A user cannot be assigned more than %d custom DB roles."),

  CANNOT_UPGRADE_NON_SHARED_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "A tenant upgrade can only be performed on a tenant cluster. Use Modify a Cluster to make"
          + " changes to your dedicated or serverless cluster."),
  CANNOT_UPGRADE_TO_SHARED_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot upgrade to a shared tier cluster."),
  CANNOT_UPGRADE_FLEX_TO_SHARED_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot upgrade a flex cluster to a shared tier cluster."),
  SHARED_SERVERLESS_ENDPOINT_NOT_SUPPORTED(
      HttpServletResponse.SC_GONE,
      "Use of endpoint is not allowed by Atlas at this time. Please use %s instead."),

  // HTTP 503 - Too many MMS threads occupied by GenAI requests
  // Keep message intentionally vague to keep user from knowing MMS is overloaded
  // Language of message not set in stone yet
  SERVICE_UNAVAILABLE(HttpServletResponse.SC_SERVICE_UNAVAILABLE, "Please try again later."),

  // INGESTION PIPELINE ERROR CODES
  INGESTION_PIPELINE_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "No Data Lake Pipeline named %s in group %s."),
  INGESTION_PIPELINE_RUN_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Data Lake Pipeline run %s not found."),
  INGESTION_PIPELINE_ALREADY_EXISTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Lake Pipeline named %s in group %s already exists."),
  INGESTION_PIPELINE_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The maximum number of Data Lake Pipelines has been reached in this project. To create a Data"
          + " Lake Pipeline, remove an existing one or create a new project."),
  INGESTION_NOT_SUPPORTED_FOR_PAUSED_CLUSTERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Ingestion is not supported for paused clusters. Ingestion source cluster %s in group %s"
          + " must not be paused in order to create a Data Lake Pipeline."),
  INGESTION_NOT_SUPPORTED_FOR_SHARDED_CLUSTERS(
      HttpServletResponse.SC_BAD_REQUEST, "Ingestion is not supported for sharded clusters."),
  INGESTION_PIPELINE_RUN_INVALID_STATE_UPDATE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake Pipeline run state transition is invalid."),
  INGESTION_PIPELINE_INVALID_STATE_UPDATE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Lake Pipeline state transition is invalid."),
  INGESTION_PIPELINE_SOURCE_ALREADY_IN_USE(
      HttpServletResponse.SC_BAD_REQUEST,
      "The specified database and collection is already in use on an ingestion pipeline."),
  INGESTION_SOURCE_GROUP_ID_DOES_NOT_MATCH_PIPELINE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Ingestion source project does not match the project of the pipeline."),
  INGESTION_TRANSFORMATION_FIELD_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST, "Transformation fields limit exceeded."),
  DUPLICATE_TRANSFORMATION_FIELD_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "Transformation fields with identical field names present."),
  INGESTION_SOURCE_DEDICATED_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST, "Ingestion is only supported for dedicated clusters."),
  INGESTION_SOURCE_CLOUD_BACKUP_NOT_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST, "Ingestion source cluster does not have backup enabled."),
  INGESTION_SNAPSHOT_ID_ALREADY_INGESTED(
      HttpServletResponse.SC_BAD_REQUEST, "The specified snapshot %s has already been ingested."),
  INGESTION_PIPELINE_RUN_IN_PROGRESS(
      HttpServletResponse.SC_BAD_REQUEST,
      "This pipeline already has a run in progress. Please wait for run to complete and try"
          + " again."),
  INGESTION_SINK_NO_PARTITION_FIELDS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Lake Pipeline must have at least one partition field."),
  DATASET_RETENTION_POLICY_CANNOT_EXIST_ON_NON_SCHEDULED_PIPELINE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Dataset retention policy cannot exist on a non-scheduled pipeline."),
  INGESTION_PIPELINE_SCHEDULE_POLICY_LESS_FREQUENT_THAN_DATASET_RETENTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The schedule policy for this pipeline must be more frequent than the configured dataset"
          + " retention policy."),
  INGESTION_PIPELINES_DEPRECATED(
      HttpServletResponse.SC_METHOD_NOT_ALLOWED,
      "Creating new Data Lake Pipelines is disabled. Please see:"
          + " https://www.mongodb.com/docs/atlas/app-services/data-api/data-api-deprecation."),
  INGESTION_PIPELINES_EOL(
      HttpServletResponse.SC_GONE,
      "Data Lake Pipelines has reached end-of-life. Please see:"
          + " https://www.mongodb.com/docs/datalake/data-lake-deprecation/#std-label-data-lake-deprecation-guide."),

  // These codes replace the *DATA_LAKE* error codes.  The *DATA_LAKE* error codes will be removed
  // once all customers have switched to the new API pattern
  DATA_FEDERATION_CANNOT_CREATE_WITH_STORAGE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation tenant cannot be created with storage."),
  DATA_FEDERATION_CANNOT_UPDATE_WITH_STORAGE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation tenant cannot be updated with storage."),
  DATA_FEDERATION_TENANT_NAME_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "The Data Federation name %s is invalid. The name can only contain ASCII letters, numbers,"
          + " and hyphens."),
  DATA_FEDERATION_NAME_TOO_LONG(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation name %s is limited to %d characters."),
  DATA_FEDERATION_HOSTNAME_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation hostname (%s) invalid."),
  DATA_FEDERATION_UNSUPPORTED_CLOUD_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Federation does not support the (%s) cloud provider."),
  DATA_FEDERATION_DUPLICATE_STORE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation store (%s) specified multiple times."),
  DATA_FEDERATION_DATABASE_STORE_REFERENCE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation database store (%s) reference invalid."),
  DATA_FEDERATION_TENANT_LIMIT_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation tenant limit for project exceeded."),
  DATA_FEDERATION_STORAGE_CONFIG_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation storage config invalid."),
  DATA_FEDERATION_IAM_ROLE_REQUIRED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Federation tenant with S3 data stores requires a valid IAM role."),
  DATA_FEDERATION_CLOUD_PROVIDER_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Federation tenant does not have a cloud provider config."),
  INVALID_DATA_FEDERATION_AUTH_CONFIG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Federation authorization config should specify either the IAM assumed role ARN or the"
          + " Cloud Provider Access roleId."),
  UNSUPPORTED_AUTH_TYPE_FOR_DATA_FEDERATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Only users with SCRAM authentication can be scoped to Data Federations."),
  DATA_FEDERATION_TENANT_INVALID_STATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Federation tenant is not in a valid state <%s> for the requested operation."),
  DATA_FEDERATION_CANNOT_ACCESS_TEST_S3_BUCKET(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Federation cannot access the specified test S3 bucket (%s) via the provided IAM role."
          + " Ensure that the IAM role provides access to read the bucket's contents. %s."),
  INTERNAL_DATA_FEDERATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "This Data Federation was provisioned internally and cannot be fetched or modified through"
          + " the API."),
  DATA_FEDERATION_CANNOT_GET_S3_BUCKET_REGION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data Federation cannot determine the region for the specified test S3 bucket (%s) with the"
          + " provided IAM role. Ensure that the IAM role provides access to retrieve the bucket's"
          + " location. %s."),
  DATA_FEDERATION_CANNOT_ASSUME_ROLE(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation cannot assume the specified role (%s)."),
  USER_NOT_SCOPED_TO_ACCESS_DATA_FEDERATION(
      HttpServletResponse.SC_FORBIDDEN,
      "User '%s' does not have scoped access to Data Federation '%s' in group %s."),
  DATA_FEDERATION_AUTH_TO_ATLAS_CLUSTER_GROUP_DOES_NOT_MATCH_TENANT_GROUP(
      HttpServletResponse.SC_FORBIDDEN,
      "The tenant group (%s), does not match the group of the cluster (%s) for which user access"
          + " has been requested."),

  DATA_FEDERATION_TENANT_NOT_FOUND_FOR_ID(
      HttpServletResponse.SC_NOT_FOUND, "Data Federation tenant with tenantId %s not found."),

  DATA_FEDERATION_TENANT_NOT_FOUND_FOR_NAME(
      HttpServletResponse.SC_NOT_FOUND,
      "Data Federation tenant for project %s and name %s not found."),

  DATA_FEDERATION_TENANT_QUERY_LIMIT_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "Data Federation %s query limit not found for tenant %s in group %s."),

  STREAM_TENANT_NOT_FOUND_FOR_NAME(
      HttpServletResponse.SC_NOT_FOUND, "Stream instance for project %s and name %s not found."),

  STREAM_TENANT_NOT_FOUND_FOR_ID(
      HttpServletResponse.SC_NOT_FOUND,
      "Stream instance for project %s ane tenant id %s not found."),

  STREAM_TENANT_NAME_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT, "A Stream instance with the name %s already exists."),

  STREAM_TENANT_HAS_STREAM_PROCESSORS(
      HttpServletResponse.SC_FORBIDDEN,
      "Stream instance with name %s has active processors, and cannot be changed."),

  STREAM_VPC_PROXY_DEPLOYMENT_IS_DELETING(
      HttpServletResponse.SC_FORBIDDEN,
      "Stream connection with name %s is deleting. Please either use a different connection name or"
          + " wait."),

  STREAM_CONNECTION_NOT_FOUND_FOR_NAME(
      HttpServletResponse.SC_NOT_FOUND,
      "Stream connection with name %s for project %s and name %s not found."),

  STREAM_CONNECTION_TYPE_CANNOT_BE_MODIFIED(
      HttpServletResponse.SC_BAD_REQUEST, "Stream connection type cannot be modified."),

  STREAM_NETWORKING_ACCESS_TYPE_CANNOT_BE_MODIFIED(
      HttpServletResponse.SC_BAD_REQUEST, "Stream networking access type cannot be modified."),

  STREAM_CONNECTION_NOT_FOUND_FOR_ID(
      HttpServletResponse.SC_NOT_FOUND, "Stream connection ID not found in project %s."),

  STREAM_CONNECTION_VENDOR_NOT_COMPATIBLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Stream connection vendor for private link connection not compatible with connection type of"
          + " connection %s."),

  STREAM_CONNECTION_NAME_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT, "A Stream connection with the name %s already exists."),

  STREAM_CONNECTION_HAS_STREAM_PROCESSORS(
      HttpServletResponse.SC_FORBIDDEN,
      "Stream connection with name %s in stream instance %s has active processors, and cannot be"
          + " changed."),

  STREAM_KAFKA_CONNECTION_IS_DEPLOYING(
      HttpServletResponse.SC_CONFLICT,
      "Stream Kafka connection %s is currently deploying. Please retry the request later."),

  STREAM_AWS_IAM_ROLE_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST,
      "The AWS IAM role ARN of the connection was not found in project %s."),
  STREAM_AWS_CONNECTION_NOT_AUTHORIZED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Unable to authorize AWS connection with name %s. Please verify that the IAM role has"
          + " been configured correctly and refer to the following documentation for further"
          + " guidance: https://www.mongodb.com/docs/atlas/security/set-up-unified-aws-access/."),
  STREAM_AWSLAMBDA_NOT_SUPPORTED_IN_CLOUD_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST,
      "The AWSLambda connection is not supported in the cloud provider of the stream instance."),
  STREAM_UNSUPPORTED_CONNECTION_TYPE(
      HttpServletResponse.SC_BAD_REQUEST, "The stream connection type is not supported."),

  STREAM_AWS_BAD_REQUEST(
      HttpServletResponse.SC_BAD_REQUEST,
      "A request made to AWS failed while processing request. Please ensure valid input before"
          + " trying again. Error: %s."),

  STREAM_UPSTREAM_SERVICE_FAILURE(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "Encountered a temporary upstream service failure. Please try again."),

  STREAM_UNSUPPORTED_CLOUD_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST, "Stream does not support the (%s) cloud provider."),
  STREAM_UNSUPPORTED_INSTANCE_TIER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Stream does not support the (%s) tier for a stream instance."),

  STREAM_WORKSPACES_NOT_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Stream config cannot have maxTierSize/defaultTier with feature flag disabled."),

  STREAM_VPC_PROXY_DEPLOYMENT_IS_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Streams VPC Proxy deployment is not supported on the current environment."),

  STREAM_PRIVATE_LINK_IS_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Streams Private Link is not supported on the current environment."),

  STREAM_PROCESSOR_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND,
      "Streams Processor with this name (%s) not found in tenant (%s)."),

  STREAM_PROCESSOR_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT, "Streams Processor with this name (%s) already exists."),

  STREAM_PROCESSOR_TIMEOUT(
      HttpServletResponse.SC_GATEWAY_TIMEOUT,
      "Streams Processor with this name (%s) timed out while processing."),

  STREAM_PROCESSOR_INVALID_NAME(
      HttpServletResponse.SC_BAD_REQUEST, "Streams Processor name (%s) is invalid."),

  STREAM_PROCESSOR_GENERIC_ERROR(
      HttpServletResponse.SC_BAD_REQUEST,
      "Streams Processor with this name (%s) had a problem occur: %s."),

  STREAM_PRIVATE_LINK_ALREADY_EXISTS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Private Link with service endpoint (%s) already exists."),

  STREAM_PRIVATE_LINK_IN_USE(
      HttpServletResponse.SC_BAD_REQUEST, "Private Link with connection ID (%s) is in use."),

  STREAM_PRIVATE_NETWORK_PROVIDER_MISMATCH(
      HttpServletResponse.SC_BAD_REQUEST,
      "Private link or VPC peer must match cloud provider for Atlas Project."),

  STREAM_PRIVATE_LINK_IS_NOT_READY(
      HttpServletResponse.SC_BAD_REQUEST, "Private Link with connection ID (%s) is not ready."),

  CANNOT_CLOSE_GROUP_ACTIVE_ATLAS_DATA_FEDERATIONS(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while it has active Data Federations; please terminate all Data"
          + " Federations."),

  CANNOT_CLOSE_GROUP_ACTIVE_STREAMS_RESOURCE(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while there are Stream Processing instances or private networking"
          + " connections; please terminate all Stream Processing instances private networking"
          + " resources (e.g. Private Link, VPC Peering etc)."),

  CANNOT_CLOSE_GROUP_EAR_PRIVATE_ENDPOINT(
      HttpServletResponse.SC_CONFLICT,
      "Cannot close group while there are Encryption At Rest private endpoints; "
          + "please terminate all EAR private endpoints."),

  UNSUPPORTED_MATCHER_FIELD(HttpServletResponse.SC_BAD_REQUEST, UNSUPPORTED_MATCHER_FIELD_MSG),

  UNSUPPORTED_MATCHER_VALUE(HttpServletResponse.SC_BAD_REQUEST, UNSUPPORTED_MATCHER_VALUE_MSG),

  UNSUPPORTED_MATCHER_ON_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "This alert configuration type does not support matchers."),

  DATA_FEDERATION_TENANT_NAME_ALREADY_EXISTS(
      HttpServletResponse.SC_CONFLICT, "A Data Federation tenant with the name %s already exists."),

  DATA_FEDERATION_TENANT_TYPE_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST, "Data Federation tenant type %s not supported."),
  // End DATA_FEDERATION replacement codes

  BATCH_LIMIT_EXCEEDED(HttpServletResponse.SC_BAD_REQUEST, "Batch limit of %s exceeded."),

  VERSION_GONE(HttpServletResponse.SC_GONE, "Version %s of %s is no longer available."),
  INVALID_VERSION_DATE(
      HttpServletResponse.SC_NOT_ACCEPTABLE, "Invalid accept header or version date."),
  VERSION_DATE_IN_FUTURE(
      HttpServletResponse.SC_NOT_ACCEPTABLE,
      "Requested version should be before or equal to the current date."),
  VERSION_DATE_IN_THE_PAST(
      HttpServletResponse.SC_NOT_ACCEPTABLE,
      "The Atlas Administration API doesn't support version %s."
          + " Please migrate to the latest version: %s."),

  MEDIA_TYPE_NOT_SUPPORTED_FOR_VERSION(
      HttpServletResponse.SC_NOT_ACCEPTABLE,
      "The Atlas Administration API doesn't support media-type %s. Please use: %s."),

  BAD_VERSIONING_CONTENT_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid content-type header."
          + " Version date should match the one provided in the accept header: %s."),

  CANNOT_TERMINATE_CLUSTER_WHEN_BACKUP_LOCK_MVP_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot terminate a cluster when Backup Compliance Policy is enabled."),

  CANNOT_TERMINATE_SERVERLESS_INSTANCE_WHEN_BACKUP_LOCK_MVP_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot terminate a serverless instance when Backup Compliance Policy is enabled."),

  CANNOT_TERMINATE_CLUSTER_WITH_UNDERGOING_REGIONAL_OUTAGE_SIMULATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot terminate cluster %s in project %s because it is undergoing a regional outage"
          + " simulation. End regional outage simulation and try again."),

  CANNOT_TERMINATE_CLUSTER_WHEN_TERMINATION_PROTECTION_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot terminate a cluster when termination protection is enabled. Disable termination"
          + " protection and try again."),

  CANNOT_TERMINATE_SERVERLESS_INSTANCE_WHEN_TERMINATION_PROTECTION_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot terminate a serverless instance when termination protection is enabled. Disable"
          + " termination protection and try again."),

  USER_UNAUTHORIZED_ATLAS_TERMINATION_PROTECTION(
      HttpServletResponse.SC_UNAUTHORIZED,
      "You need to have a Project Owner role or higher to enable or disable Termination"
          + " Protection."),

  COST_EXPLORER_INVALID_DATE_RANGE(
      HttpServletResponse.SC_BAD_REQUEST, "The start date should be before the end date."),
  COST_EXPLORER_INVALID_DATE(
      HttpServletResponse.SC_BAD_REQUEST, "Start and end dates must be the first of a month."),
  COST_EXPLORER_QUERY_LENGTH_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cost explorer queries can only span the past 18 months."),
  COST_EXPLORER_TOKEN_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "The specified token cannot be found."),
  COST_EXPLORER_INVALID_ORG_TYPE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cloud Manager organizations cannot access Cost Explorer."),
  COST_EXPLORER_REQUEST_FILTER_TOO_LARGE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cost Explorer filter options can only have a maximum of 20 items."),
  COST_EXPLORER_INVALID_GROUP_BY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Group by dimension must have at least one filter present."),
  COST_EXPLORER_INVALID_SERVICE(
      HttpServletResponse.SC_BAD_REQUEST, "At least one provided service filter is invalid."),
  COST_EXPLORER_ORGANIZATION_FILTER_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST,
      "Organization filter is required and must have at least one entry."),
  COST_EXPLORER_INVALID_CLUSTER_FILTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Project filter must be present when cluster filter is present. Please add a "
          + "project filter as well."),
  USAGE_DETAILS_INVALID_CLUSTER_FILTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Project filter must be present when cluster filter is present. Please add a "
          + "project filter as well."),
  USAGE_DETAILS_INVALID_DATE_RANGE(
      HttpServletResponse.SC_BAD_REQUEST, "The start date should be before the end date."),
  USAGE_DETAILS_DATE_RANGE_PERIOD_EXCEEDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Usage details cannot query for data prior to the previous billing period."),
  USAGE_DETAILS_SORT_FIELD_AND_SORT_ORDER_MUST_BE_SPECIFIED_TOGETHER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Usage details cannot query for either sort field or sort order without specifying both."),
  BILLING_IMPORT_DATA_TRANSFER_AUDIT_INVALID_SEARCH_WINDOW(
      HttpServletResponse.SC_BAD_REQUEST,
      "Search window must be greater than 0 and not exceed maximum length."),
  BILLING_IMPORT_DATA_TRANSFER_AUDIT_CSV_DATA_NOT_COMPLETE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot audit specified usage date because CSV data is still being processed. "
          + "Please wait until both data sources have completed exporting that date and usage has "
          + "not been reported for a full day for that usage date."),
  BILLING_IMPORT_DATA_TRANSFER_AUDIT_BIG_QUERY_DATA_NOT_COMPLETE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot audit specified usage date because BigQuery data is still being processed. "
          + "Please wait until both data sources have completed exporting that date and usage has "
          + "not been reported for a full day for that usage date."),
  BILLING_IMPORT_DATA_TRANSFER_AUDIT_USAGE_MISMATCH(
      HttpServletResponse.SC_BAD_REQUEST,
      "Usage is different from CSV to BigQuery. BigQuery: %s, CSV: %s."),
  METER_CLIENT_SUBMISSION_JOB_REQUEST_NOT_READY(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot execute submission job request. Cloud provider data is not ready for given "
          + "run date."),
  EMPTY_CLUSTER_OUTAGE_SIMULATION_OUTAGE_FILTERS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster outage simulation needs to have at least one outage filter defined."),
  INVALID_CLUSTER_OUTAGE_SIMULATION_STATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Invalid cluster outage simulation state: %s, expected state: %s."),
  CRITERIA_NOT_MET_FOR_FORCE_RECONFIG(
      HttpServletResponse.SC_BAD_REQUEST,
      "Replica set reconfig cannot be forced. Please ensure a majority of nodes and at least one"
          + " region are down, and that nodes are only being added to the cluster."),
  CANNOT_RUN_OUTAGE_SIMULATION_WITH_CLUSTER_PAUSED(
      HttpServletResponse.SC_CONFLICT, "Cannot pause cluster with pending changes."),
  CANNOT_CREATE_ORGS_FROM_INTERNAL_CLIENT(
      HttpServletResponse.SC_FORBIDDEN, "Cannot create organizations from an Internal Client."),
  COULD_NOT_UPDATE_PROJECT_LABEL(
      HttpServletResponse.SC_BAD_REQUEST,
      "The human-readable label that identifies the specified project cannot be null, blank or"
          + " identical to the current name."),
  PROJECT_TAGS_DISABLED(
      HttpServletResponse.SC_FORBIDDEN, "Tagging is not enabled for this project."),
  INVALID_PROJECT_UPDATE(
      HttpServletResponse.SC_BAD_REQUEST, "All parameters cannot be null or blank."),
  MAX_NUMBER_OF_REGIONS_FOR_CONTAINER(
      HttpServletResponse.SC_FORBIDDEN,
      "Max number of regions for %s container exceeded. Limit is %d."),
  DB_CHECK_CONFIG_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "A dbCheck config with id %s could not be found."),
  DB_CHECK_STATUS_CHANGE_INVALID(
      HttpServletResponse.SC_BAD_REQUEST, "Unable to change dbcheck status. Inner message is %s."),
  DATA_VALIDATION_FOR_NVME(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data validation cannot be performed on an NVMe instance. Run data validation on the hidden"
          + " secondary instance instead."),
  DATA_VALIDATION_FOR_ENCRYPTION_AT_REST(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data validation cannot be performed on a cluster with encryption at rest (EAR)."),
  DATA_VALIDATION_FOR_UNSUPPORTED_CLOUD_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data validation cannot be performed on unsupported cloud provider."),
  DATA_VALIDATION_FOR_PAUSED_FREE_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Data validation cannot be performed on a paused free cluster."),
  HOSTNAMES_NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "Hostnames not found: %s."),
  ROLLING_RESYNC_IN_PROGRESS(
      HttpServletResponse.SC_BAD_REQUEST,
      "A rolling resync is already in progress for shard(s) %s."),
  DUPLICATE_HOSTNAMES_REQUESTED_TO_RESYNC(
      HttpServletResponse.SC_BAD_REQUEST, "Duplicate hostnames requested to resync: %s."),
  INVOICE_CSV_TAG_LIMIT_EXCEEDED(
      HttpServletResponse.SC_PARTIAL_CONTENT,
      "File generated without tags because the maximum number of tag keys for CSV export was"
          + " exceeded."),
  INVOICE_CSV_FAILED_TAG_RETRIEVAL(
      HttpServletResponse.SC_PARTIAL_CONTENT,
      "File generated without tags because we could not retrieve tagging information at this time."
          + " Please try again."),
  OPLOG_WINDOW_TOO_SMALL(
      HttpServletResponse.SC_BAD_REQUEST, "The oplogWindow is too small for nodes to be resynced."),
  CUSTOM_PRIMARY_STEP_DOWN_WINDOW_MISSING_INFO(
      HttpServletResponse.SC_BAD_REQUEST,
      "Custom primary step down window selected but window provided has missing information."),
  TOPOLOGY_CHANGE_IN_PROGRESS(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster %s in group %s is undergoing topology changes. cannot resync now."),
  CANNOT_CANCEL_ROLLING_RESYNC_INVALID_STATUS(
      HttpServletResponse.SC_CONFLICT,
      "Cannot cancel rolling resync unless resync has status of RESYNC_REQUESTED or RESYCNING."),
  NO_ELIGIBLE_SYNC_SOURCES_FOUND(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot find eligible sync sources for shard(s): %s."),
  UNEXPECTED_STEP_DOWN_WINDOW_PROVIDED(
      HttpServletResponse.SC_BAD_REQUEST,
      "The step down config was not expecting a step down window for the provided step down"
          + " timing."),
  FCBIS_NOT_SUPPORTED_ON_CLUSTER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cluster does not support file copy based initial sync, MongoDB version %s, EAR enabled=%s."),
  SYNC_SOURCES_TO_EXCLUDE_NOT_SUPPORTED_ON_MONGODB_VERSION(
      HttpServletResponse.SC_BAD_REQUEST,
      "MongoDB version %s does not support the unsupportedSyncSource server parameter, which is "
          + "required when providing sync sources to exclude."),
  INVALID_CUSTOM_STEP_DOWN_WINDOW(
      HttpServletResponse.SC_BAD_REQUEST,
      "Custom step down window start date %s has to be before the custom step down window end date"
          + " %s."),

  // Natural language query generation
  INVALID_REQUEST_PARAMETER(HttpServletResponse.SC_BAD_REQUEST, "Invalid %s in the request body."),
  INVALID_AUTH_HEADER(
      HttpServletResponse.SC_FORBIDDEN, "Invalid authorization header in the request."),

  UNAUTHORIZED(
      HttpServletResponse.SC_UNAUTHORIZED,
      "The customer Okta issued access token must be sent with the request."),

  PROMPT_TOO_LONG(HttpServletResponse.SC_REQUEST_ENTITY_TOO_LARGE, "The AI prompt is too long."),
  TOO_MANY_REQUESTS(HttpServletResponse.SC_BAD_REQUEST, "Rate limits are exceeded."),

  QUERY_GENERATION_FAILED(HttpServletResponse.SC_NOT_FOUND, "Unable to parse query content."),
  QUERY_STATS_NOT_SUPPORTED_ON_MONGODB_VERSION(
      HttpServletResponse.SC_BAD_REQUEST, "MongoDB version %s does not support query stats."),
  QUERY_STATS_LOG_VERBOSITY_LEVEL_NOT_SUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Unsupported log verbosity level provided for the query stats log component."),
  INVALID_WT_CHECKPOINT_CONFIG(
      HttpServletResponse.SC_BAD_REQUEST,
      "The block stream size must be at least 1MB greater than the block batch size. Block"
          + " stream size: %s, block batch size: %s."),

  INVALID_CONFIG_SERVER_MANAGEMENT_MODE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Config server management mode cannot be fixed to embedded."),
  INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_SEARCH_INDEXES(
      HttpServletResponse.SC_BAD_REQUEST,
      "The config server management mode cannot be changed due to search indexes existing on the"
          + " cluster."),
  INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_TIME_SERIES(
      HttpServletResponse.SC_BAD_REQUEST,
      "The config server management mode cannot be changed due to time series collections existing "
          + "on the cluster."),
  INVALID_CONFIG_SERVER_MANAGEMENT_MODE_UPDATE_QUERYABLE_ENCRYPTION(
      HttpServletResponse.SC_BAD_REQUEST,
      "The config server management mode cannot be changed due to queryable encryption collections "
          + "existing on the cluster."),

  MIXED_GOVCLOUD_COMMERCIAL_REGIONS(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot have both GovCloud and Commercial regions."),
  MIXED_CN_STANDARD_REGIONS(
      HttpServletResponse.SC_BAD_REQUEST, "Cannot have both China and non-China regions."),
  REGION_NOT_SUPPORTED(HttpServletResponse.SC_BAD_REQUEST, "Specified region %s is not supported."),
  DISK_SIZE_GB_INCONSISTENT(
      HttpServletResponse.SC_BAD_REQUEST, "Disk Size GB values must be consistent across cluster."),
  CANNOT_EXPORT_TO_UNSUPPORTED_CLOUD_PROVIDER(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot export snapshot to unsupported cloud provider: %s."),
  AZURE_ACCOUNT_SERVICE_URL_NOT_FOUND(
      HttpServletResponse.SC_BAD_REQUEST, "Storage account service url not found."),
  ZONE_CHANGES_UNSUPPORTED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Zones for global clusters can only be either added or removed in a single update. "
          + "Adding and removing zones at the same time is unsupported."),
  AMBIGUOUS_ZONE_NAME_UPDATE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Zone names cannot be renamed if replication specs are also being moved out of that zone."
          + " Please rename the zone and reorganize the replication specs in that zone in separate"
          + " updates."),
  INVALID_LOG_REQUEST_DATES(
      HttpServletResponse.SC_BAD_REQUEST,
      "Log request dates must be in the past, and chronological, and if one is populated, the other"
          + " one must be as well."),
  LOG_REQUEST_DATES_FLAG_NOT_ENABLED(
      HttpServletResponse.SC_BAD_REQUEST,
      "Must have ENABLE_LOG_REQUEST_TIMEFRAME feature flag enabled in order to provide date"
          + " range."),
  ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED(
      HttpServletResponse.SC_FORBIDDEN,
      "Invalid Configuration. Contains selections that are unavailable due to your organization's"
          + " resource policies."),
  ASYMMETRIC_BASE_AUTO_SCALING_UNAVAILABLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Autoscaling is not yet available in conjunction with independent shard scaling. Disable"
          + " autoscaling for Base nodes or select one instance size for all Base nodes."),
  ASYMMETRIC_ANALYTICS_AUTO_SCALING_UNAVAILABLE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Autoscaling is not yet available in conjunction with independent shard scaling. Disable"
          + " autoscaling for Analytics nodes or select one instance size for all Analytics"
          + " nodes."),
  SEARCH_INDEXES_INSUFFICIENT_DISK_SPACE(
      HttpServletResponse.SC_BAD_REQUEST, "Insufficient disk size: %s."),
  GROUP_DB_USER_NOT_FOUND(
      HttpServletResponse.SC_NOT_FOUND, "Database user %s cannot be found in group with ID %s."),
  CUSTOM_ZONE_MAPPINGS_INVALID(
      HttpServletResponse.SC_BAD_REQUEST,
      "One or more custom zone mappings refer to zone(s) that do not exist. When removing zones,"
          + " remove corresponding custom zone mappings first."),
  FLEX_MIGRATION_TENANT_IN_PROGRESS(
      HttpServletResponse.SC_CONFLICT,
      "Your operation can not be completed at this time because cluster %s is currently being"
          + " auto-migrated to Atlas Flex Clusters or rolling back. Please wait for the migration"
          + " to complete and try again."),
  NO_ACTIVE_ORG_MEMBER_INVITED_UPON_TEAM_CREATION(
      HttpServletResponse.SC_BAD_REQUEST,
      "Please invite at least one active user from your organization to the team you're creating."),
  PREDICTIVE_ERROR_INTERNAL(
      HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
      "An error occurred when interacting with the data serving layer: %s."),
  PREDICTIVE_ERROR_INVALID_TIME_RANGE(
      HttpServletResponse.SC_BAD_REQUEST,
      "Start time must be before or equal to end time. You may request at most 48h of data, and"
          + " cannot request data more than 7 days old."),
  PREDICTIVE_ERROR_INVALID_METRIC_NAME(
      HttpServletResponse.SC_BAD_REQUEST,
      "Cannot find statistic with the name %s. Please try again with a valid value from the list"
          + " %s."),
  FTDC_TO_S3_MIN_INSTANCE_SIZE_NOT_MET_FOR_FORCE_PUSH(
      HttpServletResponse.SC_BAD_REQUEST,
      "The configured minimum instance size has not been met by the cluster, so force-pushing is"
          + " not allowed."),
  INVALID_S3_STORE_CONFIG(
      HttpServletResponse.SC_BAD_REQUEST, "Invalid S3 store configuration: %s."),
  CLOUD_PROVIDER_ACCESS_NO_GCP_CONTAINER_PROVISIONED(
      HttpServletResponse.SC_BAD_REQUEST,
      "You must have an Atlas Cluster on GCP or GCP Network Peering configured to set up Cloud"
          + " Provider Access.");
  private static final Logger LOG = LoggerFactory.getLogger(ApiErrorCode.class);
  private final int _status;
  private final String _message;

  ApiErrorCode(final int pStatus, final String pMessage) {
    _status = pStatus;
    _message = pMessage;
  }

  public static String removeDoublePeriod(final String pValue) {
    if (pValue.endsWith("..")) {
      return pValue.substring(0, pValue.length() - 1);
    }

    return pValue;
  }

  public static String stripTrailingPeriod(final String pMessage) {
    return Optional.ofNullable(pMessage)
        .map(
            m -> {
              if (m.endsWith(".")) {
                return m.substring(0, m.length() - 1);
              }
              return m;
            })
        .orElse(pMessage);
  }

  @Nullable
  private static String getReason(final int pStatus) {
    return switch (pStatus) {
      case HttpServletResponse.SC_BAD_REQUEST -> // HTTP 400
          "Bad Request";
      case HttpServletResponse.SC_UNAUTHORIZED -> // HTTP 401
          "Unauthorized";
      case HttpServletResponse.SC_PAYMENT_REQUIRED -> // HTTP 402
          "Payment Required";
      case HttpServletResponse.SC_FORBIDDEN -> // HTTP 403
          "Forbidden";
      case HttpServletResponse.SC_NOT_FOUND -> // HTTP 404
          "Not Found";
      case HttpServletResponse.SC_METHOD_NOT_ALLOWED -> // HTTP 405
          "Method Not Allowed";
      case HttpServletResponse.SC_NOT_ACCEPTABLE -> // HTTP 406
          "Not Acceptable";
      case HttpServletResponse.SC_PROXY_AUTHENTICATION_REQUIRED -> // HTTP 407
          "Proxy Authentication Required";
      case HttpServletResponse.SC_REQUEST_TIMEOUT -> // HTTP 408
          "Request Timeout";
      case HttpServletResponse.SC_CONFLICT -> // HTTP 409
          "Conflict";
      case HttpServletResponse.SC_GONE -> // HTTP 410
          "Gone";
      case HttpServletResponse.SC_LENGTH_REQUIRED -> // HTTP 411
          "Length Required";
      case HttpServletResponse.SC_PRECONDITION_FAILED -> // HTTP 412
          "Precondition Failed";
      case HttpServletResponse.SC_REQUEST_ENTITY_TOO_LARGE -> // HTTP 413
          "Request Entity Too Large";
      case HttpServletResponse.SC_REQUEST_URI_TOO_LONG -> // HTTP 414
          "Request-URI Too Long";
      case HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE -> // HTTP 415
          "Unsupported Media Type";
      case HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE -> // HTTP 416
          "Requested Range Not Satisfiable";
      case HttpServletResponse.SC_EXPECTATION_FAILED -> // HTTP 417
          "Expectation Failed";
      case 428 -> // RFC 6585
          "Precondition Required";
      case 429 -> // RFC 6585
          "Too Many Requests";
      case 431 -> // RFC 6585
          "Request Header Fields Too Large";
      case HttpServletResponse.SC_INTERNAL_SERVER_ERROR -> // HTTP 500
          "Internal Server Error";
      case HttpServletResponse.SC_NOT_IMPLEMENTED -> // HTTP 501
          "Not Implemented";
      case HttpServletResponse.SC_BAD_GATEWAY -> // HTTP 502
          "Bad Gateway";
      case HttpServletResponse.SC_SERVICE_UNAVAILABLE -> // HTTP 503
          "Service Unavailable";
      case HttpServletResponse.SC_GATEWAY_TIMEOUT -> // HTTP 504
          "Gateway Timeout";
      case HttpServletResponse.SC_HTTP_VERSION_NOT_SUPPORTED -> // HTTP 505
          "HTTP Version Not Supported";
      case 511 -> // RFC 6585
          "Network Authentication Required";
      default -> null;
    };
  }

  public int getStatus() {
    return _status;
  }

  @Override
  public String getMessage() {
    return _message;
  }

  public Response response(
      @Nullable final Boolean pEnvelope, @Nullable final Object... pParameters) {
    String detail;
    var parameter = pParameters == null ? List.of() : Arrays.asList(pParameters);
    try {
      detail = String.format(getMessage(), pParameters);
    } catch (IllegalFormatException ex) {
      LOG.error(
          "invalid format for API error {} {} {}",
          kv("message", getMessage()),
          kv("error_code", this),
          kv("parameters", parameter),
          ex);
      detail = getMessage();
    }
    return new ApiResponseBuilder(pEnvelope)
        .status(getStatus())
        .content(new ApiError(getStatus(), getReason(getStatus()), detail, this, parameter))
        .build();
  }

  public WebApplicationException exception(
      @Nullable final Boolean pEnvelope, @Nullable final Object... pParameters) {
    return new WebApplicationException(response(pEnvelope, pParameters));
  }
}
