package com.xgen.svc.mms.api.view.atlas.privateLink;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkConnection;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkInterfaceEndpoint;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasCloudProviderView;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.stream.Collectors;

@Schema(
    name = "AWSPrivateLinkConnection",
    title = "AWS",
    description = "Group of Private Endpoint Service settings.")
public class ApiAtlasAWSPrivateLinkConnectionView extends ApiAtlasEndpointServiceView {

  @JsonProperty(FieldDefs.ENDPOINT_SERVICE_NAME)
  @Schema(
      type = "string",
      description =
          "Unique string that identifies the Amazon Web Services (AWS) PrivateLink endpoint"
              + " service. MongoDB Cloud returns null while it creates the endpoint service.",
      accessMode = Schema.AccessMode.READ_ONLY,
      pattern = OpenApiConst.AWS_ENDPOINT_SERVICE_NAME_REGEX)
  private final String _endpointServiceName;

  @JsonProperty(FieldDefs.INTERFACE_ENDPOINTS)
  @ArraySchema(
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-124-array-max-items",
                  value = "Schema predates IPA validation."),
            })
      },
      arraySchema =
          @Schema(
              name = FieldDefs.INTERFACE_ENDPOINTS,
              description =
                  "List of strings that identify private endpoint interfaces applied to the"
                      + " specified project.",
              accessMode = Schema.AccessMode.READ_ONLY,
              requiredMode = Schema.RequiredMode.REQUIRED),
      schema =
          @Schema(
              type = "string",
              description =
                  "Unique 24-hexadecimal digit string that identifies the interface endpoint.",
              accessMode = Schema.AccessMode.READ_ONLY,
              pattern = OpenApiConst.OBJECT_ID_REGEX,
              example = OpenApiConst.OBJECT_ID_EXAMPLE))
  private final List<String> _interfaceEndpoints;

  public ApiAtlasAWSPrivateLinkConnectionView() {
    super();
    _endpointServiceName = null;
    _interfaceEndpoints = List.of();
  }

  public ApiAtlasAWSPrivateLinkConnectionView(
      final AWSPrivateLinkConnection pModel,
      final String pRegion,
      final boolean pUseWaitingForUser) {
    super(pModel, pRegion, pUseWaitingForUser);
    _endpointServiceName = pModel.getEndpointServiceName().orElse(null);
    _interfaceEndpoints =
        pModel.getEndpoints().stream()
            .map(AWSPrivateLinkInterfaceEndpoint::getEndpointId)
            .collect(Collectors.toList());
  }

  public String getEndpointServiceName() {
    return _endpointServiceName;
  }

  public List<String> getInterfaceEndpoints() {
    return _interfaceEndpoints;
  }

  @Override
  public ApiAtlasCloudProviderView getCloudProvider() {
    return ApiAtlasCloudProviderView.AWS;
  }

  public static class FieldDefs {
    public static final String ENDPOINT_SERVICE_NAME = "endpointServiceName";
    public static final String INTERFACE_ENDPOINTS = "interfaceEndpoints";
  }
}
