version: 1.0.0
aliases:
  - //.github/CODEOWNERS-aliases.yml
filters:
  - "OWNERS.yaml":
    approvers:
      - 10gen/code-review-team-atlas-clusters-performance
  - "BUILD.bazel":
    approvers:
      - 10gen/code-review-team-atlas-clusters-performance
  - "AzureCapacityReservationOrphanedItemCorrectionTool.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-performance
  - "AzureSubscriptionCreationTool.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-performance
  - "ResourceConsistencyTool.java":
    approvers:
      - ace-iam-authz
  - "OnlineArchive*":
    approvers:
      - 10gen/code-review-team-online-archive
  - "authzconsistency/*":
    approvers:
      - ace-iam-authz
  - "DummyTool.java":
    approvers:
      - ace-iam-authz
  - "GenerateIFRManifestTool.java":
    approvers:
      - 10gen/code-review-team-acad
  - "*AutoScale*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-performance
  - "NaturalLanguage*":
    approvers:
      - 10gen/code-review-team-data-explorer
  - "AWSDNSReaper.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "util/AWSDNSReaperUtil.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "AWSReaper.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "GCPReaper.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "AzureReaper.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "AzureLeakedPublicIPTool.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "ExportSKURegionPricingTool.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "SearchSeedLocalEnvData.java":
    approvers:
      - 10gen/code-review-team-search-elasticity-and-recovery
  - "AWSProxyProtocolIngressInspectionTool.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "FixedVersionPinnedDateBackfillTool.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "AWSAccountCreationTool.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "SeedLocalEnvData.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform
  - "EventServiceBackfillTool.java":
    approvers:
      - 10gen/cloud-cap
  - "GenerateDataLakePrivateApiKeyTool.java":
    approvers:
      - 10gen/code-review-team-atlas-data-federation
  - "RegressionTestDataGeneratorTool.java":
    approvers:
      - 10gen/code-review-team-billing-platform
  - "MockDataSchemaTool.java":
    approvers:
      - 10gen/code-review-team-growth-feature-teams