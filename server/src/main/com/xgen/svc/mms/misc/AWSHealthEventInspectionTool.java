package com.xgen.svc.mms.misc;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;

import com.google.inject.Guice;
import com.google.inject.Injector;
import com.xgen.cloud.common.guice._public.extensions.closeable.CloseableModule;
import com.xgen.cloud.common.guice._public.extensions.jakarta.JakartaApiModule;
import com.xgen.cloud.common.util._public.json.JsonUtils;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.svc.AwsApiSvcV2;
import com.xgen.svc.mms.misc.util.MinimalModule;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.services.health.model.Event;

/** bazel run //server/src/main/com/xgen/svc/mms/misc:AWSHealthEventInspectionTool */
public class AWSHealthEventInspectionTool {

  private static final Logger LOG = LoggerFactory.getLogger(AWSHealthEventInspectionTool.class);
  private final AWSAccountDao _awsAccountDao;
  private final AwsApiSvcV2 _awsApiSvcV2;
  private static final Options COMMAND_LINE_OPTIONS;

  static {
    final Options options = new Options();
    List.of(
            Option.builder(OptionNames.HELP).longOpt("help").desc("Print help message").build(),
            Option.builder(OptionNames.START_DATE)
                .longOpt("start-date")
                .desc(
                    "filter by events that were last updated after this ISO start date. Format:"
                        + " YYYY-MM-DD")
                .hasArg(true)
                .optionalArg(true)
                .numberOfArgs(1)
                .build(),
            Option.builder(OptionNames.END_DATE)
                .longOpt("end-date")
                .desc(
                    "filter by events that were last updated before this ISO end date. Format:"
                        + " YYYY-MM-DD")
                .hasArg(true)
                .optionalArg(true)
                .numberOfArgs(1)
                .build(),
            Option.builder(OptionNames.EVENT_TYPE_CODES)
                .longOpt("event-type-codes")
                .desc(
                    "filter by event names--pass multiple event names by invoking this option"
                        + " multiple times")
                .hasArg(true)
                .optionalArg(true)
                .numberOfArgs(1)
                .build())
        .forEach(options::addOption);
    COMMAND_LINE_OPTIONS = options;
  }

  @Inject
  public AWSHealthEventInspectionTool(
      final AWSAccountDao pAWSAccountDao, final AwsApiSvcV2 pAwsApiSvcV2) {
    _awsAccountDao = pAWSAccountDao;
    _awsApiSvcV2 = pAwsApiSvcV2;
  }

  private static void printHelp() {
    final HelpFormatter formatter = new HelpFormatter();
    formatter.printHelp(
        "bazel run //server/src/main/com/xgen/svc/mms/misc:AWSHealthEventInspectionTool -- ",
        COMMAND_LINE_OPTIONS);
  }

  public void findHealthEvents(final ToolOptions pToolOptions) {
    LOG.info(
        String.format(
            "Starting to inspect AWS Health events with ToolOptions=%s", pToolOptions.toString()));

    final List<Event> allHealthEvents = new ArrayList<>();
    _awsAccountDao.findAllAccounts().stream()
        .filter(account -> account.getAssignmentEnabled())
        .forEach(
            account -> {
              LOG.info(String.format("Finding AWS Health events on account=%s", account.getName()));

              final List<Event> healthEvents = findAwsHealthEvents(account, pToolOptions);
              allHealthEvents.addAll(healthEvents);

              LOG.info(String.format("Found %d health events", healthEvents.size()));
            });

    LOG.info(String.format("Found a total of %d health events", allHealthEvents.size()));

    String json = "";
    try {
      json = JsonUtils.toJson(allHealthEvents.stream().map(e -> (Object) e).toList());
    } catch (Exception e) {
      LOG.error("Error mapping events to json: \n{}", e.getMessage());
    }

    if (!json.isEmpty()) {
      LOG.info(String.format("All events found: \n%s", json));
    }
  }

  private List<Event> findAwsHealthEvents(
      final AWSAccount pAccount, final ToolOptions pToolOptions) {

    LocalDate startLocalDate = pToolOptions.startDate;
    if (startLocalDate == null) {
      // default to 6 months ago if no start date is passed
      startLocalDate = LocalDate.now().minusMonths(6);
    }
    Date startDate = Date.from(startLocalDate.atStartOfDay().atZone(ZoneId.of("UTC")).toInstant());

    LocalDate endLocalDate = pToolOptions.endDate;
    if (endLocalDate == null) {
      // default to now if no end date is passed
      endLocalDate = LocalDate.now();
    }
    Date endDate =
        Date.from(
            // add 1 day so that we encompass all of the end date in the query
            endLocalDate.plusDays(1).atStartOfDay().atZone(ZoneId.of("UTC")).toInstant());

    try {
      return _awsApiSvcV2.getAWSHealthEvents(
          pAccount.getId(), pToolOptions.eventTypeCodes, startDate, endDate, LOG);
    } catch (final Exception e) {
      LOG.error("Error getting AWS Health events", e);
      return Collections.emptyList();
    }
  }

  public static void main(final String[] args) {
    final CommandLineParser parser = new DefaultParser();
    final CommandLine commandLine;
    try {
      commandLine = parser.parse(COMMAND_LINE_OPTIONS, args);
    } catch (final ParseException ex) {
      LOG.error("Unable to parse command", ex);
      printHelp();
      return;
    }

    if (commandLine.hasOption("help")) {
      printHelp();
      return;
    }

    final Injector injector =
        Guice.createInjector(new CloseableModule(), new JakartaApiModule(), new MinimalModule());
    final AWSHealthEventInspectionTool tool =
        injector.getInstance(AWSHealthEventInspectionTool.class);
    final ToolOptions toolOptions = new ToolOptions(commandLine);

    try {
      tool.findHealthEvents(toolOptions);
    } catch (final Exception e) {
      LOG.error("Caught unexpected error. exiting...", e);
      System.exit(1);
    }

    System.exit(0);
  }

  private static class OptionNames {
    private static final String START_DATE = "sd";
    private static final String END_DATE = "ed";
    private static final String HELP = "h";
    private static final String EVENT_TYPE_CODES = "etc";
  }

  public static class ToolOptions {

    public final LocalDate startDate;
    public final LocalDate endDate;
    public final List<String> eventTypeCodes;

    public ToolOptions(final CommandLine commandLine) {
      String startLocalDateString = commandLine.getOptionValue(OptionNames.START_DATE);
      if (startLocalDateString != null) {
        this.startDate = LocalDate.parse(startLocalDateString, ISO_LOCAL_DATE);
      } else {
        this.startDate = null;
      }

      String endLocalDateString = commandLine.getOptionValue(OptionNames.END_DATE);
      if (endLocalDateString != null) {
        this.endDate = LocalDate.parse(endLocalDateString, ISO_LOCAL_DATE);
      } else {
        this.endDate = null;
      }

      this.eventTypeCodes =
          Optional.ofNullable(commandLine.getOptionValues(OptionNames.EVENT_TYPE_CODES))
              .map(List::of)
              .orElse(Collections.emptyList());
    }

    public ToolOptions(final Builder builder) {
      startDate = builder.startDate;
      endDate = builder.endDate;
      eventTypeCodes = builder.eventTypeCodes;
    }

    @Override
    public String toString() {
      return "ToolOptions("
          + ", startDate="
          + startDate
          + ", endDate="
          + endDate
          + ", eventTypeCodes="
          + eventTypeCodes
          + ")";
    }

    public static final class Builder {

      private LocalDate startDate;
      private LocalDate endDate;
      private List<String> eventTypeCodes;

      public Builder() {}

      public Builder startDate(final LocalDate pStartDate) {
        startDate = pStartDate;
        return this;
      }

      public Builder endDate(final LocalDate pEndDate) {
        endDate = pEndDate;
        return this;
      }

      public Builder eventTypeCodes(final List<String> pEventTypeCodes) {
        eventTypeCodes = pEventTypeCodes;
        return this;
      }

      public ToolOptions build() {
        return new ToolOptions(this);
      }
    }
  }
}
