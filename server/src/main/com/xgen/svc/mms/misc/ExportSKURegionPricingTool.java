package com.xgen.svc.mms.misc;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.xgen.cloud.billingplatform.model.sku._public.model.PricingConsumers;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import com.xgen.cloud.common.guice._public.extensions.closeable.CloseableModule;
import com.xgen.cloud.common.guice._public.extensions.jakarta.JakartaApiModule;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.pricing._public.svc.SkuPricingSvc;
import com.xgen.cloud.pricing.runtime.guice.PricingModule;
import com.xgen.svc.mms.misc.util.MinimalModule;
import jakarta.inject.Inject;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;

public class ExportSKURegionPricingTool {

  private final SkuPricingSvc skuPricingSvc;

  @Inject
  public ExportSKURegionPricingTool(SkuPricingSvc skuPricingSvc) {
    this.skuPricingSvc = skuPricingSvc;
  }

  String getRegionPrices(final Optional<RegionName> pRegionName) {
    final Date effectiveDate = new Date();

    final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    final List<JsonObject> prices = new ArrayList<>();

    Map<SKU, SkuPricing> pricingBySku =
        skuPricingSvc.getPricing(List.of(SKU.values()), effectiveDate, PricingConsumers.CLUSTERS);
    for (final SKU sku : SKU.values()) {
      final SkuPricing skuPricing = pricingBySku.get(sku);

      Stream.of(AWSRegionName.values(), GCPRegionName.values(), AzureRegionName.values())
          .flatMap(Arrays::stream)
          .filter(regionName -> pRegionName.map(r -> r.equals(regionName)).orElse(true))
          .forEach(
              region -> {
                if (skuPricing.hasRegionUnitPriceDollars(region)) {
                  double unitPrice = skuPricing.getRegionUnitPriceDollars(region, sku);
                  prices.add(pricingToJson(sku, region, unitPrice));
                }
                if (skuPricing.hasTieredRegionUnitPriceDollars(region)) {
                  double[] tieredPrices = getTieredPricing(skuPricing, region, sku);
                  prices.add(pricingToJson(sku, region, tieredPrices));
                }
              });
    }

    return gson.toJson(prices);
  }

  private static JsonObject pricingToJson(
      final SKU pSku, final RegionName pRegionName, final double pUnitPrice) {
    final JsonObject obj = new JsonObject();
    obj.addProperty("sku", pSku.name());
    obj.addProperty("provider", pRegionName.getProvider().name());
    obj.addProperty("region", pRegionName.getName());
    obj.addProperty("unitPrice", pUnitPrice);
    return obj;
  }

  private static JsonObject pricingToJson(
      final SKU pSku, final RegionName pRegionName, final double[] pPrices) {
    final JsonObject obj = new JsonObject();
    obj.addProperty("sku", pSku.name());
    obj.addProperty("provider", pRegionName.getProvider().name());
    obj.addProperty("region", pRegionName.getName());

    final JsonArray prices = new JsonArray(pPrices.length);
    for (double price : pPrices) {
      prices.add(price);
    }
    obj.add("tieredPrices", prices);
    return obj;
  }

  public static double[] getTieredPricing(
      final SkuPricing pricing, final RegionName pRegionName, final SKU pSku) {
    int numTiers = pricing.getPricingTiers();
    double[] prices = new double[numTiers];
    for (int tier = 1; tier <= numTiers; tier++) {
      prices[tier - 1] = pricing.getTieredRegionUnitPriceDollars(pRegionName, tier, pSku);
    }
    return prices;
  }

  public static void main(String[] args) {
    final Options options = new Options();
    final Option helpOption = new Option("h", "help", false, "print this message");
    helpOption.setRequired(false);
    options.addOption(helpOption);
    final Option providerOption = new Option("p", "provider", true, "specify a cloud provider");
    providerOption.setRequired(false);
    options.addOption(providerOption);
    final Option regionOption = new Option("r", "region", true, "specify a region");
    regionOption.setRequired(false);
    options.addOption(regionOption);

    try {
      final PrintStream out = System.out;
      final DefaultParser parser = new DefaultParser();
      final CommandLine cmd = parser.parse(options, args);
      final HelpFormatter helpFormatter = new HelpFormatter();

      if (cmd.hasOption("help")) {
        helpFormatter.printHelp(
            "bazel run //server/src/main/com/xgen/svc/mms/misc:ExportSKURegionPricingTool",
            options);
        return;
      }

      final Optional<RegionName> regionNameParam;
      if (cmd.hasOption("provider")) {
        if (!cmd.hasOption("region")) {
          throw new ParseException("Must specify region name");
        }
        final CloudProvider provider =
            CloudProvider.findByNameIgnoreCase(cmd.getOptionValue("provider"));
        final String regionNameValue = cmd.getOptionValue("region");
        RegionName regionName;
        switch (provider) {
          case AWS ->
              regionName =
                  AWSRegionName.findByNameOrValue(regionNameValue)
                      .orElseThrow(() -> new ParseException("Invalid AWS region"));
          case GCP ->
              regionName =
                  GCPRegionName.findByNameOrValue(regionNameValue)
                      .orElseThrow(() -> new ParseException("Invalid GCP region"));
          case AZURE ->
              regionName =
                  AzureRegionName.findByNameOrValue(regionNameValue)
                      .orElseThrow(() -> new ParseException("Invalid Azure region"));
          default -> throw new ParseException("Invalid cloud provider");
        }
        regionNameParam = Optional.of(regionName);
      } else {
        if (cmd.hasOption("region")) {
          throw new ParseException("Must specify cloud provider: (aws|gcp|azure)");
        }
        regionNameParam = Optional.empty();
      }
      final Injector injector =
          Guice.createInjector(
              new CloseableModule(),
              new JakartaApiModule(),
              new MinimalModule(),
              new PricingModule());
      final ExportSKURegionPricingTool pricingTool =
          injector.getInstance(ExportSKURegionPricingTool.class);
      String body = pricingTool.getRegionPrices(regionNameParam);
      out.println(body);
      out.flush();
    } catch (final ParseException pE) {
      System.err.println(pE.getMessage());
      System.exit(1);
    }
  }
}
