/* (C) Copyright 2014, MongoDB, Inc. */

package com.xgen.svc.mms.util.res;

import static com.mongodb.ReadPreference.secondaryPreferred;
import static com.xgen.cloud.brs.core._public.model.restore.RestoreStatus.BACKUP_DIRECT_S3_RESTORE;
import static com.xgen.cloud.brs.daemon._public.grid.dao.ImplicitJobDaoV2.getJobQuery;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.AUTOMATION_VERSIONS_DIRECTORY;
import static com.xgen.cloud.monitoring.common._public.model.retention.Retention.MINUTE_DATA_RETENTION;
import static com.xgen.cloud.nds.security._public.model.NDSAcmeCAStatus.Status.ENABLED;
import static com.xgen.svc.mms.model.auth.SessionTimeoutInfo.determineIdleSessionTimeoutSeconds;
import static com.xgen.svc.nds.liveimport.util.LiveImportKubeUtil.getKubeClient;
import static com.xgen.svc.nds.liveimport.util.LiveImportKubeUtil.getKubeNamespace;
import static configservicesdk.com.xgen.devtools.configservicesdk.constants.ConfigServiceSdkConstants.GLOBAL_NAMESPACE;
import static java.util.stream.Collectors.toSet;
import static java.util.stream.Collectors.toUnmodifiableSet;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.ec2.model.InstanceState;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.securitytoken.model.Credentials;
import com.atlassian.jira.rest.client.api.RestClientException;
import com.atlassian.jira.rest.client.api.domain.Issue;
import com.atlassian.jira.rest.client.api.domain.Transition;
import com.atlassian.jira.rest.client.api.domain.input.IssueInput;
import com.atlassian.jira.rest.client.api.domain.input.IssueInputBuilder;
import com.atlassian.jira.rest.client.api.domain.input.TransitionInput;
import com.azure.core.management.AzureEnvironment;
import com.azure.resourcemanager.compute.models.PowerState;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.models.BlobErrorCode;
import com.azure.storage.blob.models.BlobStorageException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.google.cloud.storage.Blob;
import com.google.common.io.ByteStreams;
import com.google.common.io.CharStreams;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.mongodb.BasicDBObject;
import com.mongodb.BasicDBObjectBuilder;
import com.mongodb.DBObject;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.okta.sdk.resource.model.User;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.activity._public.model.alert.Alert;
import com.xgen.cloud.activity._public.model.alert.HostAlert;
import com.xgen.cloud.activity._public.model.alert.HostMetricAlert;
import com.xgen.cloud.activity._public.model.alert.MetricType;
import com.xgen.cloud.activity._public.model.alert.ReplicaSetAlert;
import com.xgen.cloud.activity._public.model.alert.state.MetricThreshold;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.activity._public.model.event.HostMetricEvent;
import com.xgen.cloud.activity._public.model.event.ReplicaSetEvent;
import com.xgen.cloud.admin._public.activity.SystemLogAlert;
import com.xgen.cloud.admin._public.activity.SystemLogEvent;
import com.xgen.cloud.agents._public.view.AgentView;
import com.xgen.cloud.agents._public.view.AgentsView;
import com.xgen.cloud.alerts.notify._public.svc.AlertTemplateGenerator;
import com.xgen.cloud.alerts.notify._public.svc.EmailNotifier;
import com.xgen.cloud.apiuser._public.svc.ApiUserSvc;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.auditinfosvc._public.svc.AuditInfoSvc;
import com.xgen.cloud.authn._public.client.AuthnClient;
import com.xgen.cloud.authn._public.client.AuthnClientProvider;
import com.xgen.cloud.authn._public.client.AuthnTestUtilsClient;
import com.xgen.cloud.authn._public.client.AuthnTestUtilsClientProvider;
import com.xgen.cloud.billingplatform.activity._public.alert.BillingAlert;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.brs.activity._public.model.BackupAlert;
import com.xgen.cloud.brs.activity._public.model.BackupBindAlert;
import com.xgen.cloud.brs.activity._public.model.BackupBusyAlert;
import com.xgen.cloud.brs.activity._public.model.BackupClustershotAlert;
import com.xgen.cloud.brs.activity._public.model.BackupEvent;
import com.xgen.cloud.brs.activity._public.model.BackupInconsistentConfigs;
import com.xgen.cloud.brs.activity._public.model.BackupSnapshotAlert;
import com.xgen.cloud.brs.activity._public.model.BackupSyncSliceAlert;
import com.xgen.cloud.brs.activity._public.model.RestoreStatusAlert;
import com.xgen.cloud.brs.activity._public.model.SystemBackupDaemonAlert;
import com.xgen.cloud.brs.activity._public.model.SystemBackupDaemonEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupResizeAlert;
import com.xgen.cloud.brs.activity._public.model.SystemBackupResizeEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupTheftAlert;
import com.xgen.cloud.brs.activity._public.model.SystemBackupTheftEvent;
import com.xgen.cloud.brs.activity._public.model.SystemDatabaseProcessAlert;
import com.xgen.cloud.brs.activity._public.model.SystemDatabaseProcessEvent.Type;
import com.xgen.cloud.brs.core._private.dao.BlockFileDao;
import com.xgen.cloud.brs.core._private.dao.ClustershotDao;
import com.xgen.cloud.brs.core._private.dao.DaemonMachineConfigDao;
import com.xgen.cloud.brs.core._private.dao.ParallelRestoreChunkDao;
import com.xgen.cloud.brs.core._private.dao.RestoreJobDao;
import com.xgen.cloud.brs.core._private.dao.S3BackupDataStoreConfigDao.S3BlockStoreConfigDao;
import com.xgen.cloud.brs.core._private.dao.S3BackupDataStoreConfigDao.S3OplogStoreConfigDao;
import com.xgen.cloud.brs.core._private.dao.SnapshotDao;
import com.xgen.cloud.brs.core._private.dao.WTFileStatsDao.WTFileStats;
import com.xgen.cloud.brs.core._public.exception.BackupException;
import com.xgen.cloud.brs.core._public.model.AuthMethod;
import com.xgen.cloud.brs.core._public.model.BackupGroupConfig;
import com.xgen.cloud.brs.core._public.model.BackupStatus;
import com.xgen.cloud.brs.core._public.model.ClusterStatus;
import com.xgen.cloud.brs.core._public.model.Clustershot;
import com.xgen.cloud.brs.core._public.model.FileSlice;
import com.xgen.cloud.brs.core._public.model.FileSystemStoreConfig;
import com.xgen.cloud.brs.core._public.model.S3DataStoreConfig;
import com.xgen.cloud.brs.core._public.model.Snapshot;
import com.xgen.cloud.brs.core._public.model.SnapshotConstants;
import com.xgen.cloud.brs.core._public.model.WtBackupStatus;
import com.xgen.cloud.brs.core._public.model.daemon.DaemonMachineConfig;
import com.xgen.cloud.brs.core._public.model.oplog.OplogSlice;
import com.xgen.cloud.brs.core._public.model.restore.AutomationRestoreProgress;
import com.xgen.cloud.brs.core._public.model.restore.RestoreStatus;
import com.xgen.cloud.brs.core._public.store.oplog.OplogStore;
import com.xgen.cloud.brs.core._public.svc.BackupDeploymentSvc;
import com.xgen.cloud.brs.core._public.svc.BackupSnapshotResourceUtil;
import com.xgen.cloud.brs.core._public.svc.WTFileStatsSvc;
import com.xgen.cloud.brs.core._public.util.SnapshotUtil;
import com.xgen.cloud.brs.daemon._public.grid.dao.ClusterStatusDao;
import com.xgen.cloud.brs.daemon._public.grid.dao.ImplicitJobDaoV2;
import com.xgen.cloud.brs.daemon._public.grid.dao.OplogSliceIntegrityCheckJobDao;
import com.xgen.cloud.brs.daemon._public.grid.svc.BackupSvc;
import com.xgen.cloud.brs.daemon._public.grid.svc.OplogSliceIntegrityCheckJobSvc;
import com.xgen.cloud.brs.web._public.svc.BackupAdminSvc;
import com.xgen.cloud.common.access._public.annotation.ApiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.agent._public.model.AgentApiKey.KeySource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.authn._public.exception.AuthnServiceErrorCode;
import com.xgen.cloud.common.authn._public.model.CloudJwt;
import com.xgen.cloud.common.authn._public.svc.SessionCookieSvc;
import com.xgen.cloud.common.authn._public.util.CloudJwtDecoder;
import com.xgen.cloud.common.aws._public.clients.AWSCredentialsUtil;
import com.xgen.cloud.common.brs._public.model.BackupDataStoreConfig;
import com.xgen.cloud.common.brs._public.model.OplogStoreType;
import com.xgen.cloud.common.brs._public.model.SnapshotSchedule;
import com.xgen.cloud.common.brs._public.model.SnapshotStoreType;
import com.xgen.cloud.common.config._public.view.UpdateFeatureFlagControlListRequestView.LIST_TYPE;
import com.xgen.cloud.common.config._public.view.UpdateFeatureFlagControlListRequestView.WRITE_TYPE;
import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.cloud.common.driverwrappers._public.legacy.MongoClient;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.Scope;
import com.xgen.cloud.common.filter._public.FilterUtils;
import com.xgen.cloud.common.groupcreation._public.svc.GroupCreationSvc;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.jira._public.config.annotation.Atlas;
import com.xgen.cloud.common.jira._public.svc.JiraIssueRestClientProxy;
import com.xgen.cloud.common.model._public.email.TemplateMap;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.common.resource._public.model.ResourceId;
import com.xgen.cloud.common.resource._public.model.ResourceId.ResourceType;
import com.xgen.cloud.common.security._public.util.TLSUtil;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.communication._public.client.CommunicationClient;
import com.xgen.cloud.communication._public.model.email.EmailPayload;
import com.xgen.cloud.communication._public.model.enums.ProviderType;
import com.xgen.cloud.config._public.client.ConfigAdminClientProvider;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.configsdk._public.wrapper.ConfigServiceSdkWrapper;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.pit._private.dao.CpsBlobStoreConfigDao;
import com.xgen.cloud.cps.pit._public.model.CpsBlobStoreConfig;
import com.xgen.cloud.cps.pit._public.model.CpsOplogSlice;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.VMBasedReplSetRestoreJob;
import com.xgen.cloud.dataexport._private.dao.DataExportConfigDao;
import com.xgen.cloud.dataexport._private.model.DataExportConfig;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.MongoDbBuild;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.deployment._public.model.VersionManifest;
import com.xgen.cloud.deployment._public.util.PackageNamer;
import com.xgen.cloud.email._public.model.EmailMsg;
import com.xgen.cloud.email._public.svc.EmailSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.activity.GroupAlert;
import com.xgen.cloud.group._public.model.activity.GroupEvent;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.agent._public.model.alert.AgentAlert;
import com.xgen.cloud.monitoring.agent._public.model.event.AgentEvent;
import com.xgen.cloud.monitoring.agent._public.svc.MonitoringAgentAuditSvc;
import com.xgen.cloud.monitoring.common._public.model.StorageEngine;
import com.xgen.cloud.monitoring.internaltestusersonly._public.annotation.InternalTestUsersOnly;
import com.xgen.cloud.monitoring.logs._private.dao.SlowQueryLogIngestionMetadataDao;
import com.xgen.cloud.monitoring.metrics._public.model.HostMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessClusterMetricsSeries;
import com.xgen.cloud.monitoring.metrics._public.svc.MetricsSvc;
import com.xgen.cloud.monitoring.metrics._public.svc.rrd.HostMeasurementSvc;
import com.xgen.cloud.monitoring.metrics._public.svc.serverless.ServerlessMetricsSvc;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostType;
import com.xgen.cloud.monitoring.topology._public.model.ReplicaSet;
import com.xgen.cloud.nds.autoscaling.context._private.dao.AutoScalingContextDao;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.privatelink.AWSMultiTargetConnectionRuleDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardwareHealth;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.NDSAWSTempCredentials;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSMultiTargetConnectionRule;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkInterfaceEndpoint;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._private.dao.AzureStorageAccountDao;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardwareHealth;
import com.xgen.cloud.nds.azure._public.model.AzureStorageAccount;
import com.xgen.cloud.nds.azure._public.model.NDSAzureKeyVault;
import com.xgen.cloud.nds.azure._public.model.ui.NDSAzureTempCredentialsView;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.svc.AWSCapacityDenylistSvc;
import com.xgen.cloud.nds.capacity._public.svc.AzureCapacityDenylistSvc;
import com.xgen.cloud.nds.capacity._public.svc.GCPCapacityDenylistSvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.BaseMultiTargetConnectionRule.NdsProcessPortTyped;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.RebootRequestedBy;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareHealth;
import com.xgen.cloud.nds.cloudprovider._public.model.KeyManagementConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster.MTMClusterType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionNameHelper;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.CloudProviderPrivateEndpoint;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterUpdateContext;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.INDSDefaults;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.Limits;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.diskwarming._public.model.DiskWarmingInstance;
import com.xgen.cloud.nds.diskwarming._public.svc.DiskWarmingSvc;
import com.xgen.cloud.nds.flex._public.model.FlexMTMCluster;
import com.xgen.cloud.nds.flex._public.model.FlexMTMClusterMigration;
import com.xgen.cloud.nds.flex._public.model.FlexMigrationServiceConfig;
import com.xgen.cloud.nds.flex._public.model.FlexMigrationServiceConfig.MigrationType;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration;
import com.xgen.cloud.nds.flex._public.model.MigrationStatus;
import com.xgen.cloud.nds.flex._public.svc.FlexMigrationSvc;
import com.xgen.cloud.nds.free._private.dao.FastSharedPreAllocatedRecordsDao;
import com.xgen.cloud.nds.free._private.dao.FreeTenantClusterDescriptionDao;
import com.xgen.cloud.nds.free._private.dao.SharedMTMClusterDao;
import com.xgen.cloud.nds.free._public.model.FastSharedPreAllocatedRecord;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.SharedMTMCluster;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardwareHealth;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceState;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.gcp._public.svc.GCPOrganizationSvc;
import com.xgen.cloud.nds.monitoring._public.model.ServerlessMTMMetrics;
import com.xgen.cloud.nds.monitoring._public.model.ServerlessTenantMetrics;
import com.xgen.cloud.nds.mpa._public.svc.MPAAuthorizationSvc;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveRunDao;
import com.xgen.cloud.nds.planning.common._public.model.PlanSummary;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionArchiveDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.CustomMongoDbBuildDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._private.dao.ReservedPortDao;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport;
import com.xgen.cloud.nds.project._public.model.ReleaseCohorts;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ReservedPort;
import com.xgen.cloud.nds.project._public.model.SecureSSHKey;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.project._public.svc.VersionDeprecationSettingsSvc;
import com.xgen.cloud.nds.project._public.view.NDSDBUserView;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.nds.sampledataset._public.model.SampleDatasetLoadStatus.SampleDataset;
import com.xgen.cloud.nds.security._public.svc.NDSACMEFailoverSvc;
import com.xgen.cloud.nds.serverless._private.dao.FastServerlessPreAllocatedRecordsDao;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMClusterDao;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.serverless._public.model.ServerlessNDSDefaults;
import com.xgen.cloud.nds.serverless._public.model.ServerlessTenantProviderOptions;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.pool.ServerlessMTMPool;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.EnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.serverless._public.svc.ServerlessEnvoySSHKeysSvc;
import com.xgen.cloud.nds.tenant._private.dao.TenantClusterDescriptionDao;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantRestore;
import com.xgen.cloud.nds.tenantupgrade._private.dao.ServerlessUpgradeToDedicatedStatusDao;
import com.xgen.cloud.nds.tenantupgrade._public.model.BaseTenantUpgradeStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeToDedicatedStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeToServerlessStatus;
import com.xgen.cloud.organization._private.dao.VercelIntegrationDao;
import com.xgen.cloud.organization._public.model.OrgPaymentStatus;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.model.VercelIntegration;
import com.xgen.cloud.organization._public.model.activity.OrgAlert;
import com.xgen.cloud.organization._public.model.activity.OrgEvent;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchInstanceSvc;
import com.xgen.cloud.services.authn.proto.v1.GenerateTokenPairResponse;
import com.xgen.cloud.services.authn.proto.v1.InvalidateTokenByIdResponse;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._private.svc.provider.UserSvcProvider;
import com.xgen.cloud.user._public.error.legacy2fa.MultiFactorAuthErrorCode;
import com.xgen.cloud.user._public.model.ApiUser;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserApiKey;
import com.xgen.cloud.user._public.model.UserRegistrationForm;
import com.xgen.cloud.user._public.model.activity.UserAlert;
import com.xgen.cloud.user._public.model.activity.UserEvent;
import com.xgen.cloud.user._public.model.legacy2fa.MultiFactorAuthSettings;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.cloud.user._public.svc.legacy2fa.MultiFactorAuthSvc;
import com.xgen.module.account.model.AccountUser;
import com.xgen.module.account.svc.AccountMultiFactorAuthSvc;
import com.xgen.module.account.svc.AccountUserSvc;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.svc.PlanTestPointSvc;
import com.xgen.module.iam.config.IamAppSettings;
import com.xgen.module.liveimport.dao.LiveImportDao;
import com.xgen.module.liveimport.model.LiveImport;
import com.xgen.module.liveimport.model.LiveImport.MigrationToolType;
import com.xgen.module.metering.common.model.MeterId;
import com.xgen.module.metering.common.model.MeterUsage;
import com.xgen.module.metering.common.model.context.MeterReadContext;
import com.xgen.module.metering.server.svc.intf.IMeterUsageSvcReadSvc;
import com.xgen.svc.atm.model.ui.CustomBuildView;
import com.xgen.svc.atm.svc.CustomBuildsSvc;
import com.xgen.svc.core.dao.base.BaseDao;
import com.xgen.svc.core.model.api.ApiVersion;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.view.ApiApiUserView;
import com.xgen.svc.mms.api.view.ApiBaseApiKeyView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasMongoDbBuildView;
import com.xgen.svc.mms.dao.pausefreetiermonitoring.NDSM0ClusterActivityDao;
import com.xgen.svc.mms.form.UserLoginForm;
import com.xgen.svc.mms.misc.OnlineArchiveRestorationTool;
import com.xgen.svc.mms.misc.OnlineArchiveV3MigrationTool;
import com.xgen.svc.mms.misc.util.OnlineArchiveBackfillUtil;
import com.xgen.svc.mms.model.auth.UiAuthCode;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.mms.model.pausefreetiermonitoring.NDSM0ClusterActivity;
import com.xgen.svc.mms.model.performanceadvisor.SlowQueryLogIngestionMetadata;
import com.xgen.svc.mms.res.filter.TestUtility;
import com.xgen.svc.mms.res.filter.WithPublicApiAuth;
import com.xgen.svc.mms.svc.NDSOrgSvc;
import com.xgen.svc.mms.svc.billing.DelinquentOrganizationEmailSvc;
import com.xgen.svc.mms.svc.billing.PlanSvc;
import com.xgen.svc.mms.svc.experiments.ExperimentsSvc;
import com.xgen.svc.mms.svc.marketing.SalesSoldDealActivationSvc;
import com.xgen.svc.mms.svc.user.UserLoginSvc;
import com.xgen.svc.mms.util.cookie.CookieUtils;
import com.xgen.svc.mms.util.metering.MeteringLineItemGeneratorUtil;
import com.xgen.svc.nds.cloudproviderobserver.TestCloudProviderResourceObserver;
import com.xgen.svc.nds.liveimport.util.DefaultMongosyncProcessUtility;
import com.xgen.svc.nds.model.TenantBackupSnapshot;
import com.xgen.svc.nds.model.ui.LimitsView;
import com.xgen.svc.nds.model.ui.MaintenanceWindowView;
import com.xgen.svc.nds.model.ui.NDSEncryptionAtRestView;
import com.xgen.svc.nds.sampleDatasetLoad.svc.NDSSampleDatasetLoadSvc;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.serverless.model.ui.metrics.ServerlessMockedMetricView;
import com.xgen.svc.nds.serverless.svc.NDSAutoScaleServerlessMTMCapacitySvc;
import com.xgen.svc.nds.serverless.svc.NDSServerlessLoadSvc;
import com.xgen.svc.nds.serverless.svc.NDSServerlessMetricsSvc;
import com.xgen.svc.nds.serverless.svc.ServerlessMTMPoolSvc;
import com.xgen.svc.nds.serverless.svc.ServerlessProxyMetricsSvc;
import com.xgen.svc.nds.serverless.view.ServerlessLoadBalancingDeploymentView;
import com.xgen.svc.nds.svc.CustomMongoDbBuildSvc;
import com.xgen.svc.nds.svc.NDSAdminSvc;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import com.xgen.svc.nds.svc.NDSEncryptionAtRestSvc;
import com.xgen.svc.nds.svc.NDSFastServerlessCronSvc;
import com.xgen.svc.nds.svc.NDSFastSharedTierCronSvc;
import com.xgen.svc.nds.svc.NDSInstanceAgentApiKeysSvc;
import com.xgen.svc.nds.svc.NDSLookupSvc;
import com.xgen.svc.nds.svc.NDSOrphanedItemSvc;
import com.xgen.svc.nds.svc.NDSProxyAuditSvc;
import com.xgen.svc.nds.svc.NDSSshKeysSvc;
import com.xgen.svc.nds.svc.NDSTenantPauseSvc;
import com.xgen.svc.nds.svc.cps.CpsConfSvc;
import com.xgen.svc.nds.svc.cps.CpsOplogSliceMetadataDaoProxy;
import com.xgen.svc.nds.svc.cps.CpsOplogStoreFactory;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import com.xgen.svc.nds.tenantUpgrade.svc.ServerlessFreeMigrationStatusSvc;
import com.xgen.svc.nds.tenantUpgrade.svc.TenantUpgradeToServerlessSvc;
import com.xgen.svc.security.acme.model.ACMEProvider;
import configservicesdk.com.xgen.devtools.configservicesdk.ConfigServiceSdkInterface;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.client.KubernetesClient;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import net.logstash.logback.argument.StructuredArguments;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.function.TriFunction;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpStatus;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bson.Document;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.eclipse.jetty.io.EofException;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.regions.Region;

@Path("/test/utils")
@Singleton
@TestUtility
public class InternalTestUtilsResource extends BaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(InternalTestUtilsResource.class);
  private static final Pattern PATTERN_MONGODB = Pattern.compile("^mongodb-.*");

  private final MultiFactorAuthSvc _multiFactorAuthSvc;
  private final UserSvcProvider _userSvcProvider;
  private final GroupSvc _groupSvc;
  private final GroupCreationSvc _groupCreationSvc;
  private final UserSvc _userSvc;
  private final EmailNotifier _emailNotifier;
  private final DelinquentOrganizationEmailSvc _delinquentOrganizationEmailSvc;
  private final MongoSvc _mongoSvc;
  private final EmailSvc _emailSvc;
  private final NDSAdminSvc _ndsAdminSvc;
  private final NDSGroupSvc _ndsGroupSvc;
  private final NDSGroupDao _ndsGroupDao;
  private final NDSGroupMaintenanceSvc _ndsGroupMaintenanceSvc;
  private final ReplicaSetHardwareSvc _replicaSetHardwareSvc;
  private final ReplicaSetHardwareDao _replicaSetHardwareDao;
  private final SearchInstanceSvc _searchInstanceSvc;
  private final NDSProxyAuditSvc _ndsProxyAuditSvc;
  private final NDSClusterSvc _ndsClusterSvc;
  private final NDSUISvc _ndsUISvc;
  private final NDSLookupSvc _ndsLookupSvc;
  private final ConfigLimitSvc _configLimitSvc;
  private final AppSettings _appSettings;
  private final IamAppSettings _iamAppSettings;
  private final OrganizationSvc _organizationSvc;
  private final NDSOrgSvc _ndsOrgSvc;
  private final PlanSvc _planSvc;
  private final PlanDao _planDao;
  private final ClusterDescriptionDao _clusterDescriptionDao;
  private final FreeTenantClusterDescriptionDao _freeTenantClusterDescriptionDao;
  private final TenantClusterDescriptionDao _tenantClusterDescriptionDao;
  private final ClusterDescriptionArchiveDao _clusterDescriptionArchiveDao;
  private final ClusterStatusDao _clusterStatusDao;
  private final UserLoginSvc _userLoginSvc;
  private final ImplicitJobDaoV2 _jobDao;
  private final BackupSvc _backupSvc;
  private final AutoScalingContextDao _autoScalingContextDao;
  private final OplogSliceIntegrityCheckJobSvc _oplogSliceIntegrityCheckJobSvc;
  private final OplogSliceIntegrityCheckJobDao _oplogSliceIntegrityCheckJobDao;
  private final CpsOplogStoreFactory _cpsOplogStoreFactory;
  private final BackupJobDao _backupJobDao;
  private final SnapshotDao _snapshotDao;
  private final UserDao _userDao;
  private final OnlineArchiveSvc _onlineArchiveSvc;
  private final OnlineArchiveRunDao _onlineArchiveRunDao;
  private final OnlineArchiveBackfillUtil _onlineArchiveBackfillUtil;
  private final AWSAccountDao _awsAccountDao;
  private final AzureStorageAccountDao _azureStorageAccountDao;
  private final GCPOrganizationSvc _gcpOrganizationSvc;
  private final CpsBlobStoreConfigDao _cpsBlobStoreConfigDao;
  private final CpsConfSvc _cpsConfSvc;
  private final AWSApiSvc _awsApiSvc;
  private final AzureApiSvc _azureApiSvc;
  private final GCPApiSvc _gcpApiSvc;
  private final RestoreJobDao _restoreJobDao;
  private final WTFileStatsSvc _wtFileStatsSvc;
  private final BackupAdminSvc _backupAdminSvc;
  private final S3BlockStoreConfigDao _s3BlockStoreConfigDao;
  private final S3OplogStoreConfigDao _s3OplogStoreConfigDao;
  private final ClustershotDao _clustershotDao;
  private final SlowQueryLogIngestionMetadataDao _slowQueryLogIngestionMetadataDao;
  private final HostMeasurementSvc _hostMeasurementSvc;
  private final DaemonMachineConfigDao _daemonMachineConfigDao;
  private final NDSM0ClusterActivityDao _ndsM0ClusterActivityDao;
  private final NDSTenantPauseSvc _ndsTenantPauseSvc;
  private final SalesSoldDealActivationSvc _salesSoldDealActivationSvc;
  private final ParallelRestoreChunkDao _parallelRestoreChunkDao;
  private final ServerlessMetricsSvc _serverlessMetricsSvc;
  private final CustomMongoDbBuildSvc _customMongoDbBuildSvc;
  private final CustomMongoDbBuildDao _customMongoDbBuildDao;
  private final NDSServerlessMetricsSvc _ndsServerlessMetricsSvc;
  private final MetricsSvc _metricsSvc;
  private final AccountUserSvc _accountUserSvc;
  private final AccountMultiFactorAuthSvc _accountMultiFactorAuthSvc;
  private final MTMClusterDao _mtmClusterDao;
  private final ServerlessMTMClusterDao _serverlessMTMClusterDao;
  private final NDSServerlessLoadSvc _ndsServerlessLoadSvc;
  private final ApiUserSvc _apiUserSvc;
  private final IMeterUsageSvcReadSvc _iMeterUsageSvcReadSvc;
  private final MeteringLineItemGeneratorUtil _meteringLineItemGeneratorUtil;
  private final AutomationMongoDbVersionSvc _automationMongoDbVersionSvc;
  private final ServerlessLoadBalancingDeploymentDao _serverlessLoadBalancingDeploymentDao;
  private final NDSFastSharedTierCronSvc _ndsFastSharedTierCronSvc;
  private final FastSharedPreAllocatedRecordsDao _fastSharedPreAllocatedRecordsDao;
  private final NDSFastServerlessCronSvc _ndsFastServerlessCronSvc;
  private final NDSSampleDatasetLoadSvc _ndsSampleDatasetLoadSvc;
  private final VercelIntegrationDao _vercelIntegrationDao;
  private final BackupRestoreJobDao _backupRestoreJobDao;
  private final NDSOrphanedItemSvc _ndsOrphanedItemSvc;
  private final TenantUpgradeToServerlessSvc _tenantUpgradeToServerlessSvc;
  private final ServerlessUpgradeToDedicatedStatusDao _serverlessUpgradeToDedicatedStatusDao;
  private final ServerlessEnvoySSHKeysSvc _serverlessEnvoySSHKeysSvc;
  private final NDSSshKeysSvc _ndsSshKeysSvc;
  private final BackupSnapshotDao _backupSnapshotDao;
  private final VersionDeprecationSettingsSvc _versionDeprecationSettingsSvc;
  private final ServerlessMTMPoolSvc _serverlessMTMPoolSvc;
  private final AWSMultiTargetConnectionRuleDao _awsMultiTargetConnectionRuleDao;
  private final TestCloudProviderResourceObserver _cloudProviderResourceObserver;
  private final NDSAutoScaleServerlessMTMCapacitySvc _ndsAutoScaleServerlessMTMCapacitySvc;
  private final ReservedPortDao _reservedPortDao;
  private final ExperimentsSvc _experimentsSvc;
  private final LiveImportDao _liveImportDao;
  private final DefaultMongosyncProcessUtility _defaultMongosyncProcessUtility;
  private final OnlineArchiveV3MigrationTool _onlineArchiveV3MigrationTool;
  private final OnlineArchiveRestorationTool _onlineArchiveRestorationTool;
  private final AuthnTestUtilsClientProvider _authnTestUtilsClientProvider;
  private final AuthnClientProvider _authnClientProvider;
  private final SessionCookieSvc _sessionCookieSvc;
  private final CloudJwtDecoder _jwtDecoder;
  private final ServerlessProxyMetricsSvc _serverlessProxyMetricsSvc;
  private final DiskWarmingSvc _diskWarmingSvc;
  private final BackupDeploymentSvc _backupDeploymentSvc;
  private final FeatureFlagSvc _featureFlagSvc;
  private final SharedMTMClusterDao _sharedMTMClusterDao;
  private final JiraIssueRestClientProxy _jiraIssueRestClientProxy;
  private final MonitoringAgentAuditSvc _monitoringAgentAuditSvc;
  private final AutomationConfigPublishingSvc _automationConfigPublishingSvc;
  private final CustomBuildsSvc _customBuildsSvc;
  private final ConfigServiceSdkWrapper _configServiceSdkWrapper;
  private final ConfigAdminClientProvider _configAdminClientProvider;
  private final DataExportConfigDao _dataExportConfigDao;
  private final MPAAuthorizationSvc _authorizationSvc;
  private final PlanTestPointSvc _planTestPointSvc;
  private final ServerlessFreeMigrationStatusSvc _serverlessFreeMigrationStatusSvc;
  private final com.xgen.module.common.planner.svc.PlanSvc _ndsPlanSvc;
  private final NDSACMEFailoverSvc _ndsacmeFailoverSvc;
  private final FlexMigrationSvc _flexMigrationSvc;
  private final NDSInstanceAgentApiKeysSvc _ndsInstanceAgentApiKeysSvc;
  private final AzureCapacityDenylistSvc _azureCapacityDenylistSvc;
  private final AWSCapacityDenylistSvc _awsCapacityDenylistSvc;
  private final GCPCapacityDenylistSvc _gcpCapacityDenylistSvc;
  private final CommunicationClient _communicationClient;
  private final AlertTemplateGenerator _alertTemplateGenerator;
  private final AuditInfoSvc _auditInfoSvc;
  private final NDSEncryptionAtRestSvc _ndsEncryptionAtRestSvc;

  @Inject
  public InternalTestUtilsResource(
      final MultiFactorAuthSvc pMultiFactorAuthSvc,
      final UserSvcProvider pUserSvcProvider,
      final GroupSvc pGroupSvc,
      final GroupCreationSvc pGroupCreationSvc,
      final UserSvc pUserSvc,
      final EmailNotifier pEmailNotifier,
      final DelinquentOrganizationEmailSvc pDelinquentOrganizationEmailSvc,
      final MongoSvc pMongoSvc,
      final EmailSvc pEmailSvc,
      final NDSAdminSvc pNDSAdminSvc,
      final NDSGroupSvc pNDSGroupSvc,
      final NDSGroupDao pNDSGroupDao,
      final ReplicaSetHardwareSvc pReplicaSetHardwareSvc,
      final ReplicaSetHardwareDao pReplicaSetHardwareDao,
      final SearchInstanceSvc pSearchInstanceSvc,
      final NDSProxyAuditSvc pNDSProxyAuditSvc,
      final NDSClusterSvc pNDSClusterSvc,
      final NDSUISvc pNDSUISvc,
      final NDSLookupSvc pNDSLookupSvc,
      final ConfigLimitSvc pConfigLimitSvc,
      final NDSGroupMaintenanceSvc pNDSGroupMaintenanceSvc,
      final AppSettings pAppSettings,
      final IamAppSettings pIamAppSettings,
      final OrganizationSvc pOrganizationSvc,
      final NDSOrgSvc pNdsOrgSvc,
      final PlanSvc pPlanSvc,
      final PlanDao pPlanDao,
      final ClusterDescriptionDao pClusterDescriptionDao,
      final FreeTenantClusterDescriptionDao pFreeTenantClusterDescriptionDao,
      final TenantClusterDescriptionDao pTenantClusterDescriptionDao,
      final ClusterDescriptionArchiveDao pClusterDescriptionArchiveDao,
      final ClusterStatusDao pClusterStatusDao,
      final UserLoginSvc pUserLoginSvc,
      final ImplicitJobDaoV2 pJobDao,
      final BackupSvc pBackupSvc,
      final OplogSliceIntegrityCheckJobSvc pOplogSliceIntegrityCheckJobSvc,
      final OplogSliceIntegrityCheckJobDao pOplogSliceIntegrityCheckJobDao,
      final AutoScalingContextDao pAutoScalingContextDao,
      final CpsOplogStoreFactory pCpsOplogStoreFactory,
      final BackupJobDao pBackupJobDao,
      final SnapshotDao pSnapshotDao,
      final UserDao pUserDao,
      final OnlineArchiveSvc pOnlineArchiveSvc,
      final OnlineArchiveRunDao pOnlineArchiveRunDao,
      final OnlineArchiveBackfillUtil pOnlineArchiveBackfillUtil,
      final AWSAccountDao awsAccountDao,
      final AzureStorageAccountDao azureStorageAccountDao,
      final GCPOrganizationSvc gcpOrganizationSvc,
      final CpsBlobStoreConfigDao cpsBlobStoreConfigDao,
      final CpsConfSvc cpsConfSvc,
      final AWSApiSvc awsApiSvc,
      final AzureApiSvc pAzureApiSvc,
      final GCPApiSvc pGcpApiSvc,
      final RestoreJobDao pRestoreJobDao,
      final S3BlockStoreConfigDao pS3BlockStoreConfigDao,
      final S3OplogStoreConfigDao pS3OplogStoreConfigDao,
      final ClustershotDao pClustershotDao,
      final BackupAdminSvc pBackupAdminSvc,
      final WTFileStatsSvc pWtFileStatsSvc,
      final SlowQueryLogIngestionMetadataDao pSlowQueryLogIngestionMetadataDao,
      final HostMeasurementSvc pHostMeasurementSvc,
      final DaemonMachineConfigDao pDaemonMachineConfigDao,
      final NDSM0ClusterActivityDao pNDSM0ClusterActivityDao,
      final NDSTenantPauseSvc pNDSTenantPauseSvc,
      final SalesSoldDealActivationSvc pSalesSoldDealActivationSvc,
      final ParallelRestoreChunkDao pParallelRestoreChunkDao,
      final ServerlessMetricsSvc pServerlessMetricsSvc,
      final CustomMongoDbBuildSvc pCustomMongoDbBuildSvc,
      final CustomMongoDbBuildDao pCustomMongoDbBuildDao,
      final NDSServerlessMetricsSvc pNDSServerlessMetricsSvc,
      final MetricsSvc pMetricsSvc,
      final AccountUserSvc pAccountUserSvc,
      final AccountMultiFactorAuthSvc pAccountMultiFactorAuthSvc,
      final MTMClusterDao pMtmClusterDao,
      final ServerlessMTMClusterDao pServerlessMTMClusterDao,
      final NDSServerlessLoadSvc pNDSServerlessLoadSvc,
      final ApiUserSvc pApiUserSvc,
      final IMeterUsageSvcReadSvc pIMeterUsageSvcReadSvc,
      final MeteringLineItemGeneratorUtil pMeteringLineItemGeneratorUtil,
      final AutomationMongoDbVersionSvc pAutomationMongoDbVersionSvc,
      final ServerlessLoadBalancingDeploymentDao pServerlessLoadBalancingDeploymentDao,
      final NDSFastSharedTierCronSvc pNDSFastSharedTierCronSvc,
      final FastSharedPreAllocatedRecordsDao pFastSharedPreAllocatedRecordsDao,
      final NDSFastServerlessCronSvc pNDSFastServerlessCronSvc,
      final FastServerlessPreAllocatedRecordsDao pFastServerlessPreAllocatedRecordsDao,
      final NDSSampleDatasetLoadSvc pNDSSampleDatasetLoadSvc,
      final VercelIntegrationDao pVercelIntegrationDao,
      final BackupRestoreJobDao pBackupRestoreJobDao,
      final NDSOrphanedItemSvc pNDSOrphanedItemSvc,
      final TenantUpgradeToServerlessSvc pTenantUpgradeToServerlessSvc,
      final ServerlessUpgradeToDedicatedStatusDao pServerlessUpgradeToDedicatedStatusDao,
      final ServerlessEnvoySSHKeysSvc pServerlessEnvoySSHKeysSvc,
      final NDSSshKeysSvc pNdsSshKeysSvc,
      final BackupSnapshotDao pBackupSnapshotDao,
      final VersionDeprecationSettingsSvc pVersionDeprecationSettingsSvc,
      final ServerlessMTMPoolSvc pServerlessMTMPoolSvc,
      final AWSMultiTargetConnectionRuleDao pAWSMultiTargetConnectionRuleDao,
      final TestCloudProviderResourceObserver pCloudProviderResourceObserver,
      final NDSAutoScaleServerlessMTMCapacitySvc pNdsAutoScaleServerlessMTMCapacitySvc,
      final ReservedPortDao pReservedPortDao,
      final ExperimentsSvc pExperimentsSvc,
      final LiveImportDao pLiveImportDao,
      final OnlineArchiveV3MigrationTool pOnlineArchiveV3MigrationTool,
      final OnlineArchiveRestorationTool pOnlineArchiveRestorationTool,
      final AuthnTestUtilsClientProvider pAuthnTestUtilsClientProvider,
      final AuthnClientProvider pAuthnClientProvider,
      final SessionCookieSvc pSessionCookieSvc,
      final CloudJwtDecoder pJwtDecoder,
      final ServerlessProxyMetricsSvc pServerlessProxyMetricsSvc,
      final DiskWarmingSvc pDiskWarmingSvc,
      final BackupDeploymentSvc pBackupDeploymentSvc,
      final FeatureFlagSvc pFeatureFlagSvc,
      final SharedMTMClusterDao pSharedMTMClusterDao,
      @Atlas final JiraIssueRestClientProxy pJiraIssueRestClientProxy,
      final MonitoringAgentAuditSvc monitoringAgentAuditSvc,
      final AutomationConfigPublishingSvc pAutomationConfigPublishingSvc,
      final CustomBuildsSvc pCustomBuildsSvc,
      final ConfigServiceSdkWrapper pConfigServiceSdkWrapper,
      final ConfigAdminClientProvider pConfigAdminClientProvider,
      final DataExportConfigDao pDataExportConfigDao,
      final MPAAuthorizationSvc pMPAAuthorizationSvc,
      final PlanTestPointSvc pPlanTestPointSvc,
      final ServerlessFreeMigrationStatusSvc pServerlessFreeMigrationStatusSvc,
      final com.xgen.module.common.planner.svc.PlanSvc pNdsPlanSvc,
      final NDSACMEFailoverSvc pNdsacmeFailoverSvc,
      final FlexMigrationSvc pFlexMigrationSvc,
      final NDSInstanceAgentApiKeysSvc pNdsInstanceAgentApiKeysSvc,
      final AzureCapacityDenylistSvc pAzureCapacityDenylistSvc,
      final AWSCapacityDenylistSvc pAwsCapacityDenylistSvc,
      final GCPCapacityDenylistSvc pGcpCapacityDenylistSvc,
      final CommunicationClient pCommunicationClient,
      AlertTemplateGenerator alertTemplateGenerator,
      final AuditInfoSvc auditInfoSvc,
      final NDSEncryptionAtRestSvc pNDSEncryptionAtRestSvc) {
    _multiFactorAuthSvc = pMultiFactorAuthSvc;
    _userSvcProvider = pUserSvcProvider;
    _groupSvc = pGroupSvc;
    _groupCreationSvc = pGroupCreationSvc;
    _userSvc = pUserSvc;
    _emailNotifier = pEmailNotifier;
    _delinquentOrganizationEmailSvc = pDelinquentOrganizationEmailSvc;
    _mongoSvc = pMongoSvc;
    _emailSvc = pEmailSvc;
    _ndsAdminSvc = pNDSAdminSvc;
    _ndsGroupSvc = pNDSGroupSvc;
    _ndsGroupDao = pNDSGroupDao;
    _replicaSetHardwareSvc = pReplicaSetHardwareSvc;
    _replicaSetHardwareDao = pReplicaSetHardwareDao;
    _searchInstanceSvc = pSearchInstanceSvc;
    _ndsProxyAuditSvc = pNDSProxyAuditSvc;
    _ndsClusterSvc = pNDSClusterSvc;
    _ndsUISvc = pNDSUISvc;
    _ndsLookupSvc = pNDSLookupSvc;
    _configLimitSvc = pConfigLimitSvc;
    _ndsGroupMaintenanceSvc = pNDSGroupMaintenanceSvc;
    _appSettings = pAppSettings;
    _iamAppSettings = pIamAppSettings;
    _organizationSvc = pOrganizationSvc;
    _ndsOrgSvc = pNdsOrgSvc;
    _planSvc = pPlanSvc;
    _planDao = pPlanDao;
    _clusterDescriptionDao = pClusterDescriptionDao;
    _freeTenantClusterDescriptionDao = pFreeTenantClusterDescriptionDao;
    _tenantClusterDescriptionDao = pTenantClusterDescriptionDao;
    _clusterDescriptionArchiveDao = pClusterDescriptionArchiveDao;
    _clusterStatusDao = pClusterStatusDao;
    _userLoginSvc = pUserLoginSvc;
    _jobDao = pJobDao;
    _backupSvc = pBackupSvc;
    _autoScalingContextDao = pAutoScalingContextDao;
    _oplogSliceIntegrityCheckJobSvc = pOplogSliceIntegrityCheckJobSvc;
    _oplogSliceIntegrityCheckJobDao = pOplogSliceIntegrityCheckJobDao;
    _cpsOplogStoreFactory = pCpsOplogStoreFactory;
    _backupJobDao = pBackupJobDao;
    _snapshotDao = pSnapshotDao;
    _userDao = pUserDao;
    _onlineArchiveSvc = pOnlineArchiveSvc;
    _onlineArchiveRunDao = pOnlineArchiveRunDao;
    _onlineArchiveBackfillUtil = pOnlineArchiveBackfillUtil;
    _awsAccountDao = awsAccountDao;
    _azureStorageAccountDao = azureStorageAccountDao;
    _gcpOrganizationSvc = gcpOrganizationSvc;
    _cpsBlobStoreConfigDao = cpsBlobStoreConfigDao;
    _cpsConfSvc = cpsConfSvc;
    _awsApiSvc = awsApiSvc;
    _azureApiSvc = pAzureApiSvc;
    _gcpApiSvc = pGcpApiSvc;
    _restoreJobDao = pRestoreJobDao;
    _wtFileStatsSvc = pWtFileStatsSvc;
    _s3BlockStoreConfigDao = pS3BlockStoreConfigDao;
    _s3OplogStoreConfigDao = pS3OplogStoreConfigDao;
    _clustershotDao = pClustershotDao;
    _backupAdminSvc = pBackupAdminSvc;
    _slowQueryLogIngestionMetadataDao = pSlowQueryLogIngestionMetadataDao;
    _hostMeasurementSvc = pHostMeasurementSvc;
    _daemonMachineConfigDao = pDaemonMachineConfigDao;
    _ndsM0ClusterActivityDao = pNDSM0ClusterActivityDao;
    _ndsTenantPauseSvc = pNDSTenantPauseSvc;
    _salesSoldDealActivationSvc = pSalesSoldDealActivationSvc;
    _parallelRestoreChunkDao = pParallelRestoreChunkDao;
    _serverlessMetricsSvc = pServerlessMetricsSvc;
    _customMongoDbBuildSvc = pCustomMongoDbBuildSvc;
    _customMongoDbBuildDao = pCustomMongoDbBuildDao;
    _ndsServerlessMetricsSvc = pNDSServerlessMetricsSvc;
    _accountUserSvc = pAccountUserSvc;
    _accountMultiFactorAuthSvc = pAccountMultiFactorAuthSvc;
    _mtmClusterDao = pMtmClusterDao;
    _serverlessMTMClusterDao = pServerlessMTMClusterDao;
    _ndsServerlessLoadSvc = pNDSServerlessLoadSvc;
    _apiUserSvc = pApiUserSvc;
    _iMeterUsageSvcReadSvc = pIMeterUsageSvcReadSvc;
    _meteringLineItemGeneratorUtil = pMeteringLineItemGeneratorUtil;
    _automationMongoDbVersionSvc = pAutomationMongoDbVersionSvc;
    _serverlessLoadBalancingDeploymentDao = pServerlessLoadBalancingDeploymentDao;
    _ndsFastSharedTierCronSvc = pNDSFastSharedTierCronSvc;
    _fastSharedPreAllocatedRecordsDao = pFastSharedPreAllocatedRecordsDao;
    _ndsFastServerlessCronSvc = pNDSFastServerlessCronSvc;
    _ndsSampleDatasetLoadSvc = pNDSSampleDatasetLoadSvc;
    _vercelIntegrationDao = pVercelIntegrationDao;
    _backupRestoreJobDao = pBackupRestoreJobDao;
    _ndsOrphanedItemSvc = pNDSOrphanedItemSvc;
    _tenantUpgradeToServerlessSvc = pTenantUpgradeToServerlessSvc;
    _serverlessUpgradeToDedicatedStatusDao = pServerlessUpgradeToDedicatedStatusDao;
    _serverlessEnvoySSHKeysSvc = pServerlessEnvoySSHKeysSvc;
    _ndsSshKeysSvc = pNdsSshKeysSvc;
    _backupSnapshotDao = pBackupSnapshotDao;
    _versionDeprecationSettingsSvc = pVersionDeprecationSettingsSvc;
    _serverlessMTMPoolSvc = pServerlessMTMPoolSvc;
    _awsMultiTargetConnectionRuleDao = pAWSMultiTargetConnectionRuleDao;
    _cloudProviderResourceObserver = pCloudProviderResourceObserver;
    _ndsAutoScaleServerlessMTMCapacitySvc = pNdsAutoScaleServerlessMTMCapacitySvc;
    _reservedPortDao = pReservedPortDao;
    _planTestPointSvc = pPlanTestPointSvc;
    _ndsPlanSvc = pNdsPlanSvc;
    _alertTemplateGenerator = alertTemplateGenerator;
    _defaultMongosyncProcessUtility = DefaultMongosyncProcessUtility.getInstance();
    _experimentsSvc = pExperimentsSvc;
    _liveImportDao = pLiveImportDao;
    _onlineArchiveV3MigrationTool = pOnlineArchiveV3MigrationTool;
    _onlineArchiveRestorationTool = pOnlineArchiveRestorationTool;
    _authnTestUtilsClientProvider = pAuthnTestUtilsClientProvider;
    _authnClientProvider = pAuthnClientProvider;
    _sessionCookieSvc = pSessionCookieSvc;
    _jwtDecoder = pJwtDecoder;
    _serverlessProxyMetricsSvc = pServerlessProxyMetricsSvc;
    _metricsSvc = pMetricsSvc;
    _diskWarmingSvc = pDiskWarmingSvc;
    _backupDeploymentSvc = pBackupDeploymentSvc;
    _featureFlagSvc = pFeatureFlagSvc;
    _sharedMTMClusterDao = pSharedMTMClusterDao;
    _jiraIssueRestClientProxy = pJiraIssueRestClientProxy;
    _monitoringAgentAuditSvc = monitoringAgentAuditSvc;
    _automationConfigPublishingSvc = pAutomationConfigPublishingSvc;
    _customBuildsSvc = pCustomBuildsSvc;
    _configServiceSdkWrapper = pConfigServiceSdkWrapper;
    _configAdminClientProvider = pConfigAdminClientProvider;
    _dataExportConfigDao = pDataExportConfigDao;
    _authorizationSvc = pMPAAuthorizationSvc;
    _serverlessFreeMigrationStatusSvc = pServerlessFreeMigrationStatusSvc;
    _ndsacmeFailoverSvc = pNdsacmeFailoverSvc;
    _flexMigrationSvc = pFlexMigrationSvc;
    _ndsInstanceAgentApiKeysSvc = pNdsInstanceAgentApiKeysSvc;
    _azureCapacityDenylistSvc = pAzureCapacityDenylistSvc;
    _awsCapacityDenylistSvc = pAwsCapacityDenylistSvc;
    _gcpCapacityDenylistSvc = pGcpCapacityDenylistSvc;
    _communicationClient = pCommunicationClient;
    _auditInfoSvc = auditInfoSvc;
    _ndsEncryptionAtRestSvc = pNDSEncryptionAtRestSvc;
  }

  private static Stream<JSONObject> mapSnapshotsToJson(
      final Group pGroup, final List<Snapshot> pSnapshots) {
    return pSnapshots.stream()
        .map(
            snapshot -> {
              final var obj = new JSONObject();
              obj.put(
                  Snapshot.SNAPSHOT_STORE_TYPE_FIELD, snapshot.getSnapshotStoreType().toString());
              obj.put(Snapshot.SNAPSHOT_STORE_ID_FIELD, snapshot.getSnapshotStoreId());
              return BackupSnapshotResourceUtil.populateSnapshotJSONFields(pGroup, snapshot, obj);
            });
  }

  private static JSONObject mapClustershotToJson(
      final Group pGroup,
      final ClusterStatus pClusterStatus,
      final Clustershot pClustershot,
      final List<Snapshot> pSnapshots) {
    final List<JSONObject> snapshotJson =
        pSnapshots.stream()
            .map(
                snapshot -> {
                  final var obj = new JSONObject();
                  obj.put(
                      Snapshot.SNAPSHOT_STORE_TYPE_FIELD,
                      snapshot.getSnapshotStoreType().toString());
                  obj.put(Snapshot.SNAPSHOT_STORE_ID_FIELD, snapshot.getSnapshotStoreId());
                  return BackupSnapshotResourceUtil.populateSnapshotJSONFields(
                      pGroup, snapshot, obj);
                })
            .collect(Collectors.toList());
    final JSONObject json = new JSONObject();

    json.put("id", pClustershot.getId().toString());
    json.put("groupId", pClustershot.getGroupId());
    json.put("clusterId", pClustershot.getClusterId());
    json.put("timestamp", pClustershot.getTimestamp().getTime());
    json.put(
        "deleteAt",
        (pClustershot.getDeleteAtTimestamp() != null)
            ? pClustershot.getDeleteAtTimestamp().getTime()
            : null);

    json.put("deletable", BackupSvc.isUserDeletable(pClustershot, pClusterStatus));

    json.put("hasBalancerStopFailure", pClustershot.hasBalancerStopFailure());
    json.put("hasOplogInsertFailure", pClustershot.hasOplogInsertFailure());
    json.put(
        "isClustershotBuilt", pClustershot.isClustershotBuilt(pClusterStatus.isWTCheckpoint()));
    json.put("shardStates", pClustershot.getShardStates());
    json.put("snapshots", new JSONArray(snapshotJson));
    return json;
  }

  /** Returns a list of mongodb releases available in the specified directory */
  private static List<java.nio.file.Path> findAvailableMongoDBReleases(final String directory)
      throws IOException {
    final java.nio.file.Path searchPath = Paths.get(directory);

    return Files.list(searchPath)
        .filter(path -> PATTERN_MONGODB.matcher(path.getFileName().toString()).matches())
        .collect(Collectors.toList());
  }

  private static BasicDBObject reservedPortToDBObject(final ReservedPort pReservedPort) {
    return new BasicDBObject()
        .append(
            "_id",
            new BasicDBObject()
                .append("port", pReservedPort.getPort())
                .append("host", pReservedPort.getHostname()))
        .append("reservedFor", pReservedPort.getReservedFor());
  }

  /**
   * A test endpoint for creating an NDS Agent API Key that has {@link KeySource#ATLAS}. This is for
   * testing requests related to Atlas Data Node configurations.
   *
   * @param httpServletRequest the current request.
   * @param pGroupId the id of the Group.
   * @param pRequestBody the request body.
   * @return the Agent API Key.
   * @throws SvcException when something goes wrong with generating the key.
   */
  @POST
  @Path("/settings/{groupId}/ndsAgentApiKey")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @InternalTestUsersOnly
  @UiCall(roles = RoleSet.GLOBAL_OWNER, csrf = false)
  public Response createNdsAgentApiKey(
      @Context HttpServletRequest httpServletRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      final String pRequestBody)
      throws SvcException {
    final JSONObject requestJson = new JSONObject(pRequestBody);
    final boolean shouldBindToIpAddr = requestJson.optBoolean("shouldBindToIpAddr");
    final AgentApiKey agentApiKey =
        _ndsInstanceAgentApiKeysSvc.generateNewApiKey(
            ObjectId.get(),
            pGroupId,
            httpServletRequest.getRemoteAddr(),
            requestJson.getString("clusterName"),
            shouldBindToIpAddr);
    return Response.status(HttpStatus.SC_CREATED)
        .entity(new JSONObject().put("key", agentApiKey.getKey()).toString())
        .build();
  }

  @POST
  @Path("/nds/{groupId}/pushBasedLogExport/forceCredentialsRotation")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response forceCredentialsRotation(@PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    _ndsGroupSvc.forcePBLECredentialsRotation(pGroupId);
    return Response.ok().build();
  }

  @GET
  @Path("/nds/{groupId}/pushBasedLogExport/tempCredentials")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getTempCredentials(@PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final PushBasedLogExport pble = _ndsGroupSvc.getPushBasedLogExport(pGroupId);
    final NDSAWSTempCredentials tc = (NDSAWSTempCredentials) pble.getTempCredentials().get();
    final String response =
        new JSONObject()
            .put("accessKey", tc.getAccessKey())
            .put("accessSecret", tc.getAccessSecret())
            .put("sessionToken", tc.getSessionToken())
            .put("expirationDate", tc.getExpirationDate().toString())
            .toString();
    return Response.ok(response).build();
  }

  @GET
  @Path("/nds/cloudProviderResourceObservers/register")
  @UiCall(auth = false)
  @Produces(MediaType.TEXT_HTML)
  public Response registerCloudProviderResourceObservers() {
    _cloudProviderResourceObserver.registerObservers();
    return Response.ok().build();
  }

  @GET
  @Path("/nds/sshKeys/{hostname}")
  @UiCall(auth = false)
  @Produces(MediaType.TEXT_HTML)
  public Response getEncryptedSshKeyForHost(@PathParam("hostname") final String pHostname) {

    ObjectId groupId = getGroupIdForHost(pHostname);

    final SecureSSHKey sshKey =
        _ndsSshKeysSvc.getToorSSHKey(
            _ndsGroupSvc
                .find(groupId)
                .orElseThrow(
                    () ->
                        new IllegalStateException(
                            String.format("Could not find group for groupId %s", groupId))));

    return Response.ok(
            String.format(
                "%s\n%s\n%s\n%s\n",
                sshKey.getEncryptedPrivateSSHKey(),
                sshKey.getOpMode(),
                sshKey.getEncryptedEncryptionKey(),
                sshKey.getIV()))
        .build();
  }

  private ObjectId getGroupIdForHost(final String pHostname) {
    final boolean isSearchHostname = _searchInstanceSvc.isSearchInstanceHostname(pHostname);

    if (!isSearchHostname) {
      return _replicaSetHardwareSvc
          .getReplicaSetHardwareForHostname(pHostname, null)
          .orElseThrow(
              () ->
                  new IllegalStateException(
                      String.format(
                          "Could not find replica set hardware for hostname %s", pHostname)))
          .getGroupId();
    } else {
      return _searchInstanceSvc
          .findNDSGroupIdForInstanceHostname(pHostname)
          .orElseThrow(
              () ->
                  new IllegalStateException(
                      String.format("Could not find search instance for hostname %s", pHostname)));
    }
  }

  @GET
  @Path("/nds/cloudProviderResourceObservers/dump")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_OCTET_STREAM)
  public Response dumpCloudProviderResourceObservers() {
    try {
      return Response.ok(_cloudProviderResourceObserver.dumpState()).build();
    } catch (final IOException e) {
      LOG.error("Failed to serialize state", e);
      return Response.serverError().build();
    }
  }

  @GET
  @Path("/forceException")
  @UiCall(auth = false)
  @Produces(MediaType.TEXT_HTML)
  public Response forceException(@QueryParam("exceptionType") final String pExceptionType)
      throws Exception {
    final String EXCEPTION_TEXT = "forced exception";

    switch (pExceptionType) {
      case "WebApplicationException":
        throw new WebApplicationException(EXCEPTION_TEXT);
      case "JsonMappingException":
        throw new JsonMappingException(null, EXCEPTION_TEXT);
      case "EofException":
        throw new EofException(EXCEPTION_TEXT);
      case "SvcException":
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, EXCEPTION_TEXT);
    }
    throw new Exception(EXCEPTION_TEXT);
  }

  @GET
  @Path("/groups/{groupId}/plannerInfo")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getPlannerInfo(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    final JSONObject response = new JSONObject();
    if (_ndsGroupDao.find(pGroupId).isPresent()) {
      final NDSGroup group = _ndsGroupDao.find(pGroupId).get();
      if (group.getNextPlanningDate().toInstant().isAfter(Instant.now())) {
        response.put(
            "planningLagInSeconds",
            Duration.between(Instant.now(), group.getNextPlanningDate().toInstant()).toSeconds());
      } else {
        response.put("planningLagInSeconds", "");
      }
      response.put(
          "failedPlanInformation",
          group.getFailedPlans().stream().map(PlanSummary::getPlanId).collect(Collectors.toList()));
    }
    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/groups/{groupId}/isContainerAlive")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response isContainerAlive(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    final JSONObject response = new JSONObject();

    response.put(
        "containerAlive",
        _ndsGroupDao
            .find(pGroupId)
            .map(NDSGroup::getCloudProviderContainers)
            .map(containers -> containers.stream().anyMatch(CloudProviderContainer::isProvisioned))
            .orElse(false));

    return Response.ok(response.toString()).build();
  }

  @POST
  @Path("/forceACMEFailover/{acmeProvider}")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response forceACMEFailover(
      @Context final HttpServletRequest pRequest,
      @PathParam("acmeProvider") final String pAcmeProvider) {
    // Check which acmeProvider this is
    Arrays.stream(ACMEProvider.values())
        .map(ACMEProvider::name)
        .filter(acmeProvider -> acmeProvider.equals(pAcmeProvider))
        .findFirst()
        .orElseThrow(() -> new IllegalArgumentException("Invalid ACME provider"));

    LOG.info("Forcing ACME failover for provider: {}", pAcmeProvider);

    // Generate at least 301 events to trigger failover for any provider
    for (int i = 0; i < 301; i++) {
      _ndsacmeFailoverSvc.generateEvent(pAcmeProvider, "failoverTest");
    }
    return Response.ok().build();
  }

  @PUT
  @Path("/resetACMEFailover")
  @UiCall(auth = false)
  public Response resetACMEFailover(@Context final HttpServletRequest pRequest) {
    Arrays.stream(ACMEProvider.values())
        .map(_ndsacmeFailoverSvc::getACMECAStatus)
        .filter(Optional::isPresent)
        .map(Optional::get)
        .forEach(status -> _ndsacmeFailoverSvc.updateStatus(status.getId(), ENABLED, null));
    return Response.ok().build();
  }

  @PUT
  @Path("/setPlannerEnabled")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = RoleSet.ANY_AUTHENTICATED_USER,
      csrf = false,
      groupSource = UiCall.GroupSource.NONE)
  public Response setPlannerEnabled(
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @FormParam("enabled") final Boolean pEnabled) {
    _ndsAdminSvc.setBackgroundTasksEnabled(pAppUser, pAuditInfo, pEnabled);
    return new ApiResponseBuilder(true).ok().build();
  }

  @DELETE
  @Path("/clearCapacityOverrides")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = RoleSet.ANY_AUTHENTICATED_USER,
      csrf = false,
      groupSource = UiCall.GroupSource.NONE)
  public Response clearCapacityOverrides() {
    _awsCapacityDenylistSvc.getAllCapacityDenylistEntries().stream()
        .map(CapacityDenyListEntry::getIdentifier)
        .forEach(_awsCapacityDenylistSvc::deleteCapacityDenyListEntry);

    _azureCapacityDenylistSvc.getAllCapacityDenylistEntries().stream()
        .map(CapacityDenyListEntry::getIdentifier)
        .forEach(_azureCapacityDenylistSvc::deleteCapacityDenyListEntry);

    _gcpCapacityDenylistSvc.getCapacityDenylistEntriesForProject(Optional.empty()).stream()
        .map(CapacityDenyListEntry::getIdentifier)
        .forEach(_gcpCapacityDenylistSvc::deleteCapacityDenyListEntry);

    return new ApiResponseBuilder(true).noContent().build();
  }

  @GET
  @Path("/ping")
  @UiCall(auth = false)
  public Response getPing(@Context final HttpServletRequest pRequest) {
    return Response.ok().entity("PONG").build();
  }

  @GET
  @Path("/forwardedIp")
  @UiCall(auth = false)
  public Response getUiCallNotRestricted(@Context final HttpServletRequest pRequest) {
    return Response.ok().entity(pRequest.getRemoteAddr()).build();
  }

  @DELETE
  @Path("/user/{username}")
  @UiCall(auth = false)
  public Response deleteUser(
      @Context final HttpServletRequest pRequest, @PathParam("username") final String pUsername) {
    final AppUser user = getUserSvc().findByUsername(pUsername);
    if (user == null) {
      return SimpleApiResponse.error(AppUserErrorCode.INVALID_USERNAME).build();
    }
    try {
      getUserSvc().hardDeleteUser(user, AuditInfoHelpers.fromSystem());
    } catch (final Exception e) {
      return SimpleApiResponse.error(CommonErrorCode.SERVER_ERROR).customField("msg", e).build();
    }
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  /** Allow tests to add users with roles that are otherwise restricted to internal systems only */
  @POST
  @Path("/nds/{groupId}/dbUsers")
  @UiCall(auth = false)
  public Response addUser(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      final NDSDBUserView pDBUser)
      throws SvcException {
    // Called by an E2E test to create role not associated with a group. Thus we cannot pass
    // Group pGroup as a context and use the dao call.
    _ndsUISvc.addDatabaseUser(_groupSvc.findById(pGroupId), pDBUser, AuditInfoHelpers.fromSystem());
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/hackInstanceOs")
  @UiCall(auth = false)
  @Consumes("application/json")
  @Produces("application/json")
  public Response hackInstanceOs(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final String pRequestBody) {
    final JSONObject body = new JSONObject(pRequestBody);
    final String from = body.getString("from");
    final String to = body.getString("to");

    final List<ReplicaSetHardware> rshardwares =
        _replicaSetHardwareSvc.getReplicaSetHardware(pGroupId, pClusterName);
    final JSONArray unable = new JSONArray();

    for (final ReplicaSetHardware rshardware : rshardwares) {
      for (final InstanceHardware instanceHardware : rshardware.getAllHardware().toList()) {
        if (!instanceHardware.isProvisioned()) {
          continue;
        }
        if (instanceHardware.getOS().isEmpty()) {
          unable.put(instanceHardware.toDBObject());
          continue;
        }
        if (!instanceHardware.getOS().get().name().equals(from)) {
          unable.put(instanceHardware.toDBObject());
          continue;
        }
        _replicaSetHardwareDao.setInstanceField(
            rshardware.getId(),
            instanceHardware.getInstanceId(),
            rshardware.isInstanceInternal(instanceHardware.getInstanceId()),
            "os",
            to);
      }
    }

    if (unable.isEmpty()) {
      _ndsGroupSvc.setPlanningNow(pGroupId);

      return Response.ok(new JSONObject().toString()).build();
    } else {
      return Response.status(HttpServletResponse.SC_CONFLICT)
          .entity(
              new JSONObject()
                  .put(
                      "error",
                      "Unable to update os, did not see correct existing os on some instances")
                  .put("instances", unable)
                  .put("from", from)
                  .put("to", to))
          .build();
    }
  }

  @PATCH
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/onlineArchive/{archiveId}")
  @UiCall(auth = false)
  public Response setOnlineArchiveToDeleteASAP(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("archiveId") final ObjectId pArchiveId)
      throws SvcException {
    _onlineArchiveSvc.setOnlineArchiveToDeleteASAP(pGroupId, pClusterName, pArchiveId);
    return Response.ok().build();
  }

  @POST
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/onlineArchive/{archiveId}/submitV3Migration")
  @UiCall(auth = false)
  public Response submitOnlineArchiveV3Migration(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("archiveId") final ObjectId pArchiveId,
      final String pBody) {
    final JSONObject body = new JSONObject(pBody);

    // get overrideShardKeys if provided
    final JSONArray keys = body.optJSONArray("overrideShardKeys");
    final List<String> overrideShardKeys;
    if (keys == null) {
      overrideShardKeys = null;
    } else {
      overrideShardKeys = new ArrayList<>();
      for (int i = 0; i < keys.length(); i++) {
        overrideShardKeys.add(keys.getString(i));
      }
    }

    try {
      _onlineArchiveV3MigrationTool.submitMigration(
          new OnlineArchiveV3MigrationTool.MigrationInput(pArchiveId, null, overrideShardKeys));
      return Response.ok().build();
    } catch (Exception pE) {
      LOG.error(
          "Fail to submit online archive v3 migration for group {}, cluster {}, archive {}. Error"
              + " {}",
          pGroupId,
          pClusterName,
          pArchiveId,
          pE.getMessage());
      return Response.serverError().build();
    }
  }

  @POST
  @Path(
      "/nds/groups/{groupId}/clusters/{clusterName}/onlineArchive/{archiveId}/submitOnlineArchiveRestoreOnlyJobIdsRestorationRequest")
  @UiCall(auth = false)
  public Response submitOnlineArchiveRestoreOnlyJobIdsRestorationRequest(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("archiveId") final ObjectId pArchiveId,
      final String pBody) {

    try {
      final String pLastJobId =
          _onlineArchiveRunDao.getLastNRunsForArchive(pArchiveId, 1).get(0).getJobId();
      _onlineArchiveRestorationTool.validateAndSaveRestoreOnlyJobIdsRestorationRequest(
          pArchiveId, List.of(pLastJobId));
      return Response.ok().build();
    } catch (Exception pE) {
      LOG.error(
          "Fail to submit restoration request for group {}, cluster {}, archive {}. Error" + " {}",
          pGroupId,
          pClusterName,
          pArchiveId,
          pE.getMessage());
      return Response.serverError().build();
    }
  }

  @POST
  @Path(
      "/nds/groups/{groupId}/clusters/{clusterName}/onlineArchive/{archiveId}/submitOnlineArchiveArchiveRestorationRequest")
  @UiCall(auth = false)
  public Response submitOnlineArchiveArchiveRestorationRequest(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("archiveId") final ObjectId pArchiveId,
      final String pBody) {

    try {
      _onlineArchiveRestorationTool.validateAndSaveArchiveRestorationRequest(pArchiveId);
      return Response.ok().build();
    } catch (Exception pE) {
      LOG.error(
          "Fail to submit restoration request for group {}, cluster {}, archive {}. Error" + " {}",
          pGroupId,
          pClusterName,
          pArchiveId,
          pE.getMessage());
      return Response.serverError().build();
    }
  }

  @POST
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/onlineArchive/{archiveId}/recoverDeletedOA")
  @UiCall(auth = false)
  public Response recoverDeletedOA(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("archiveId") final ObjectId pArchiveId,
      final String pBody) {
    try {
      _onlineArchiveSvc.recoverDeletedOnlineArchive(pArchiveId);
      return Response.ok().build();
    } catch (Exception pE) {
      LOG.error(
          "Fail to recover deleted online archive for group {}, cluster {}, archive {}. Error"
              + " {}",
          pGroupId,
          pClusterName,
          pArchiveId,
          pE.getMessage());
      return Response.serverError().build();
    }
  }

  @POST
  @Path(
      "/nds/groups/{groupId}/clusters/{clusterName}/onlineArchive/{archiveId}/backdoorCreateForBackfill")
  @UiCall(auth = false)
  public Response backdoorCreateForBackfill(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("archiveId") final ObjectId pArchiveId,
      final String pBody) {
    try {
      _onlineArchiveBackfillUtil.backdoorCreateForBackfill(pArchiveId);
      return Response.ok().build();
    } catch (Exception pE) {
      LOG.error(
          "Fail to backdoor create archive job for backfill for group {}, cluster {}, archive {}."
              + " Error {}",
          pGroupId,
          pClusterName,
          pArchiveId,
          pE.getMessage());
      return Response.serverError().build();
    }
  }

  @POST
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/onlineArchive/{archiveId}/backfillArchiveJob")
  @UiCall(auth = false)
  public Response backfillOnlineArchiveJob(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("archiveId") final ObjectId pArchiveId,
      final String pBody) {
    try {
      _onlineArchiveBackfillUtil.backfillArchiveJob(pArchiveId);
      return Response.ok().build();
    } catch (Exception pE) {
      LOG.error(
          "Fail to backfill archive job for group {}, cluster {}, archive {}. Error {}",
          pGroupId,
          pClusterName,
          pArchiveId,
          pE.getMessage());
      return Response.serverError().build();
    }
  }

  // Endpoint for serverless billing metering E2E test that generates fake line items that would
  // have been created for submitted meter usage for the sake of testing.
  // These line items are not persisted.
  @GET
  @Consumes({MediaType.APPLICATION_JSON})
  @Path("/nds/billing/generateLineItems/{groupId}")
  @UiCall(auth = false)
  public Response generateLineItemsForAutomatedBillingTest(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    final Date today = new Date();

    LOG.info(
        "Generate line items for all meter usages for org {}, up until" + " date {}.",
        pGroupId,
        today);

    List<LineItem> generatedLineItems =
        _meteringLineItemGeneratorUtil.generateLineItems(pGroupId, today);

    LOG.info(
        "Number of line items generated from meter usages for org {}, up until" + " date {}: {}",
        pGroupId,
        today,
        generatedLineItems.size());

    return Response.ok().entity(generatedLineItems).build();
  }

  @PATCH
  @Consumes({MediaType.APPLICATION_JSON})
  @Path("/nds/groups/{groupId}/nextBillingMeterCheckDate")
  @UiCall(auth = false)
  public Response setNextBillingMeterCheckDateASAP(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    _ndsGroupSvc.setNextBillingMeterCheckDate(pGroupId);
    return Response.ok().build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Path("/nds/groups/{groupId}/lastSuccessfulMeterReportDate")
  @UiCall(auth = false)
  public Response getLastSuccessfulMeterReportDate(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    final NDSGroup group = _ndsGroupSvc.find(pGroupId).get();

    final SimpleApiResponse.Builder responseBuilder = SimpleApiResponse.ok();
    return responseBuilder
        .resource(pRequest.getRequestURI())
        .customField(
            "lastSuccessfulMeterReportDate", group.getLastSuccessfulMeterReportDate().orElse(null))
        .build();
  }

  @GET
  @Consumes(MediaType.APPLICATION_JSON)
  @Path("/nds/groups/{groupId}/flexMeterUsages")
  @UiCall(auth = false)
  public Response getFlexMeterUsages(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    LOG.info("Attempting to get flex meter usages");

    MeterReadContext meterReadContext =
        new MeterReadContext.Builder()
            .appEnv(_appSettings.getAppEnv())
            .readSource("InternalTestUtilsResource")
            .build();

    final Date startDate = DateUtils.addDays(new Date(), -1);
    final Date endDate = new Date();

    final List<MeterId> meterIds =
        List.of(
            MeterId.FLEX_AZURE_100_USAGE_HOURS,
            MeterId.FLEX_AZURE_200_USAGE_HOURS,
            MeterId.FLEX_AZURE_300_USAGE_HOURS,
            MeterId.FLEX_AZURE_400_USAGE_HOURS,
            MeterId.FLEX_AZURE_500_USAGE_HOURS,
            MeterId.FLEX_GCP_100_USAGE_HOURS,
            MeterId.FLEX_GCP_200_USAGE_HOURS,
            MeterId.FLEX_GCP_300_USAGE_HOURS,
            MeterId.FLEX_GCP_400_USAGE_HOURS,
            MeterId.FLEX_GCP_500_USAGE_HOURS,
            MeterId.FLEX_AWS_100_USAGE_HOURS,
            MeterId.FLEX_AWS_200_USAGE_HOURS,
            MeterId.FLEX_AWS_300_USAGE_HOURS,
            MeterId.FLEX_AWS_400_USAGE_HOURS,
            MeterId.FLEX_AWS_500_USAGE_HOURS,
            MeterId.FLEX_AZURE_LEGACY_100_USAGE_HOURS,
            MeterId.FLEX_AZURE_LEGACY_200_USAGE_HOURS,
            MeterId.FLEX_AZURE_LEGACY_300_USAGE_HOURS,
            MeterId.FLEX_AZURE_LEGACY_400_USAGE_HOURS,
            MeterId.FLEX_AZURE_LEGACY_500_USAGE_HOURS,
            MeterId.FLEX_GCP_LEGACY_100_USAGE_HOURS,
            MeterId.FLEX_GCP_LEGACY_200_USAGE_HOURS,
            MeterId.FLEX_GCP_LEGACY_300_USAGE_HOURS,
            MeterId.FLEX_GCP_LEGACY_400_USAGE_HOURS,
            MeterId.FLEX_GCP_LEGACY_500_USAGE_HOURS,
            MeterId.FLEX_AWS_LEGACY_100_USAGE_HOURS,
            MeterId.FLEX_AWS_LEGACY_200_USAGE_HOURS,
            MeterId.FLEX_AWS_LEGACY_300_USAGE_HOURS,
            MeterId.FLEX_AWS_LEGACY_400_USAGE_HOURS,
            MeterId.FLEX_AWS_LEGACY_500_USAGE_HOURS);

    // Note: the same dates are passed for the usage and report start/end dates because they are
    // arbitrary for our test. As long
    // as the ranges include the time of the test run, they do not matter, although these will
    // differ in production.
    final List<MeterUsage> meterUsages =
        _iMeterUsageSvcReadSvc.findUsagesByStartDate(
            meterReadContext, List.of(pGroupId), meterIds, startDate, endDate, startDate, endDate);

    // Print meter usages received for debugging
    LOG.info("Number of meter usages received from the Metering Service: {}", meterUsages.size());

    return Response.ok().entity(meterUsages).build();
  }

  @GET
  @Consumes(MediaType.APPLICATION_JSON)
  @Path("/nds/groups/{groupId}/serverlessMeterUsages")
  @UiCall(auth = false)
  public Response getSubmittedServerlessMeterUsages(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {

    MeterReadContext meterReadContext =
        new MeterReadContext.Builder()
            .appEnv(_appSettings.getAppEnv())
            .readSource("InternalTestUtilsResource")
            .build();

    final Date startDate = DateUtils.addDays(new Date(), -1);
    final Date endDate = new Date();

    final List<MeterId> meterIds =
        List.of(
            MeterId.NDS_AWS_SERVERLESS_RPU,
            MeterId.NDS_AWS_SERVERLESS_WPU,
            MeterId.NDS_AWS_SERVERLESS_STORAGE,
            MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
            MeterId.NDS_AZURE_SERVERLESS_RPU,
            MeterId.NDS_AZURE_SERVERLESS_WPU,
            MeterId.NDS_AZURE_SERVERLESS_STORAGE,
            MeterId.NDS_AZURE_SERVERLESS_DATA_TRANSFER,
            MeterId.NDS_GCP_SERVERLESS_RPU,
            MeterId.NDS_GCP_SERVERLESS_WPU,
            MeterId.NDS_GCP_SERVERLESS_STORAGE,
            MeterId.NDS_GCP_SERVERLESS_DATA_TRANSFER);

    // Note: the same dates are passed for the usage and report start/end dates because they are
    // arbitrary for our test. As long
    // as the ranges include the time of the test run, they do not matter, although these will
    // differ in production.
    List<MeterUsage> meterUsages =
        _iMeterUsageSvcReadSvc.findUsagesByStartDate(
            meterReadContext, List.of(pGroupId), meterIds, startDate, endDate, startDate, endDate);

    // Print meter usages received for debugging
    LOG.info("Number of meter usages received from the Metering Service: {}", meterUsages.size());

    return Response.ok().entity(meterUsages).build();
  }

  @PATCH
  @Consumes({MediaType.APPLICATION_JSON})
  @Path("/nds/groups/{groupId}/maintenanceWindow")
  @UiCall(auth = false)
  public Response setMaintenanceWindow(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      final MaintenanceWindowView pMaintenanceWindow)
      throws SvcException {
    final NDSGroup group = _ndsGroupSvc.find(pGroupId).get();
    _ndsGroupMaintenanceSvc.setMaintenanceWindowForGroupForTest(
        pGroupId, pMaintenanceWindow.toNDSGroupMaintenanceWindow(group.getMaintenanceWindow()));
    return Response.ok().build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Path("/nds/groups/{tenantGroupId}/clusters/{tenantClusterName}/serverless/metrics/daily")
  @UiCall(auth = false)
  public Response getTenantClusterMetricsLastDay(
      @Context final HttpServletRequest pRequest,
      @PathParam("tenantGroupId") final ObjectId pTenantGroupId,
      @PathParam("tenantClusterName") final String pTenantClusterName) {
    final Optional<NDSGroup> group = _ndsGroupSvc.find(pTenantGroupId);
    if (group.isEmpty()) {
      return Response.status(
              Response.Status.NOT_FOUND.getStatusCode(),
              String.format("Group with ID %s not found", pTenantGroupId))
          .build();
    }

    final Optional<ClusterDescription> clusterDescription =
        _clusterDescriptionDao.findByName(pTenantGroupId, pTenantClusterName);
    if (clusterDescription.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "Unable to find cluster description for {groupId: %s, clusterName: %s}",
                  pTenantGroupId, pTenantClusterName))
          .build();
    }

    final ObjectId clusterUniqueId = clusterDescription.get().getUniqueId();

    final Instant now = Instant.now();

    final ServerlessClusterMetricsSeries metricsSeries =
        _serverlessMetricsSvc.findHourLevelServerlessMetrics(
            clusterUniqueId, now.minus(24, ChronoUnit.HOURS), now);
    if (metricsSeries == null) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "unable to find hour-level metrics series in past day for tenant group ID"
                      + " '%s' with tenant cluster name: '%s'",
                  pTenantGroupId, pTenantClusterName))
          .build();
    }

    return Response.ok(metricsSeries.toJSON().toString(2)).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Path("/nds/clusters/{clusterUniqueId}/serverless/metrics/{minutes}")
  @UiCall(auth = false)
  public Response getTenantClusterMetricsByClusterUniqueId(
      @Context final HttpServletRequest pRequest,
      @PathParam("clusterUniqueId") final String pClusterUniqueId,
      @PathParam("minutes") final long pMinutes) {

    final Instant now = Instant.now();

    LOG.debug("Starting to find minute level serverless metrics for cluster {}", pClusterUniqueId);
    final ServerlessClusterMetricsSeries metricsSeries =
        _serverlessMetricsSvc.findMinuteLevelServerlessMetrics(
            new ObjectId(pClusterUniqueId), now.minus(pMinutes, ChronoUnit.MINUTES), now);
    if (metricsSeries == null) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "unable to find %d minute-level metrics series for"
                      + " cluster with tenant cluster unique ID: '%s'",
                  pMinutes, pClusterUniqueId))
          .build();
    }
    LOG.debug(
        "Returned the following server metrics for cluster {}: {}",
        pClusterUniqueId,
        metricsSeries.toJSON().toString(2));

    return Response.ok(metricsSeries.toJSON().toString(2)).build();
  }

  /* Note: This endpoint exists for the tenant upgrade to serverless E2E tests. We always expect a
   * tenant upgrade status object to exist for the deployment name and group passed in.
   */
  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/clusterUpgradeToServerless/{groupId}/{deploymentName}")
  @UiCall(auth = false)
  public Response getTenantUpgradeToServerlessStatusIsComplete(
      @PathParam("groupId") final String pGroupId,
      @PathParam("deploymentName") final String pDeploymentName) {
    final Optional<TenantUpgradeToServerlessStatus> tenantUpgradeToServerlessStatus =
        _tenantUpgradeToServerlessSvc.getTenantUpgradeToServerlessStatus(
            pDeploymentName, new ObjectId(pGroupId));

    if (tenantUpgradeToServerlessStatus.isEmpty()) {
      LOG.debug(
          "Unable to find the tenant upgrade to serverless status for deployment named {} in"
              + " group {}",
          pDeploymentName,
          pGroupId);
      return Response.status(Status.BAD_REQUEST).build();
    }
    final JSONObject responseBody = new JSONObject();
    responseBody.put(
        "tenantUpgradeToServerlessIsComplete",
        tenantUpgradeToServerlessStatus.get().getState() == BaseTenantUpgradeStatus.State.COMPLETE);
    return Response.ok().entity(responseBody.toString()).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/serverlessUpgradeToDedicated/{groupId}/{clusterName}")
  @UiCall(auth = false)
  public Response getServerlessUpgradeToDedicatedStatusIsComplete(
      @PathParam("groupId") final String pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final Optional<ServerlessUpgradeToDedicatedStatus> serverlessUpgradeToDedicatedStatus =
        _serverlessUpgradeToDedicatedStatusDao.findByName(new ObjectId(pGroupId), pClusterName);

    if (serverlessUpgradeToDedicatedStatus.isEmpty()) {
      LOG.debug(
          "Unable to find the serverless upgrade to dedicated status for cluster named {} in"
              + " group {}",
          pClusterName,
          pGroupId);
      return Response.status(Status.BAD_REQUEST).build();
    }
    final JSONObject responseBody = new JSONObject();
    responseBody.put(
        "serverlessUpgradeToDedicatedStatus",
        serverlessUpgradeToDedicatedStatus.get().getState().name());
    responseBody.put(
        "serverlessUpgradeToDedicatedStatusIsComplete",
        serverlessUpgradeToDedicatedStatus.get().getState()
            == BaseTenantUpgradeStatus.State.COMPLETE);
    return Response.ok().entity(responseBody.toString()).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/serverless/instance/{groupId}/{instanceName}/metrics/{minutes}")
  @UiCall(auth = false)
  public Response getServerlessInstanceMetrics(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final String pGroupId,
      @PathParam("instanceName") final String pInstanceName,
      @PathParam("minutes") final long pMinutes)
      throws SvcException {
    final Instant until = Instant.now();
    final Instant since = until.minus(Duration.ofMinutes(pMinutes));

    LOG.debug(
        "Retrieving serverless instance metrics for gid:{} name:{} since:{} until:{}",
        pGroupId,
        pInstanceName,
        since,
        until);

    final Optional<ServerlessTenantMetrics> serverlessInstanceMetrics =
        _ndsServerlessMetricsSvc.getMetricsForTenant(
            new ObjectId(pGroupId), pInstanceName, since, until);

    if (serverlessInstanceMetrics.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "Unable to find %d worth of metrics for serverless instance with name" + " %s",
                  pMinutes, pInstanceName))
          .build();
    }

    return Response.ok(serverlessInstanceMetrics.get().toJSON().toString(2)).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/serverless/mtm/{groupId}/{clusterName}/metrics/{minutes}")
  @UiCall(auth = false)
  public Response getServerlessMTMMetrics(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final String pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("minutes") final long pMinutes)
      throws SvcException {
    final Instant until = Instant.now();
    final Instant since = until.minus(Duration.ofMinutes(pMinutes));

    LOG.debug(
        "Retrieving serverless MTM metrics for gid:{} name:{} since:{} until:{}",
        pGroupId,
        pClusterName,
        since,
        until);

    final Optional<ServerlessMTMMetrics> serverlessMTMMetrics =
        _ndsServerlessMetricsSvc.getMetricsForMTM(
            new ObjectId(pGroupId), pClusterName, since, until);

    if (serverlessMTMMetrics.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "Unable to find %d worth of metrics for serverless MTM with group ID %s and name"
                      + " %s",
                  pMinutes, pGroupId, pClusterName))
          .build();
    }

    return Response.ok(serverlessMTMMetrics.get().toJSON().toString(2)).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/usedDiskSpace/{groupId}/{clusterName}/hostname/{hostName}/metrics/{interval}")
  @UiCall(auth = false)
  public Response getHostMetrics(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final String pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("hostName") final String pHostname,
      @PathParam("interval") final long pInterval)
      throws SvcException {

    LOG.info(
        "Retrieving used disk space for cluster gid:{} name:{} hostname:{} interval:{}",
        pGroupId,
        pClusterName,
        pHostname,
        pInterval);

    final Group group = _groupSvc.findById(new ObjectId(pGroupId));
    final long useDiskSpace =
        _metricsSvc.getUsedDiskSpaceForHostnameAndPort(group, pHostname, 27017, pInterval, 0L);

    final JSONObject responseBody = new JSONObject();
    responseBody.put("usedDiskSpace", useDiskSpace);
    return Response.ok().entity(responseBody.toString()).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/diskWarming/{groupId}/{clusterName}")
  @UiCall(auth = false)
  public Response getDiskWarmingState(
      @PathParam("groupId") final String pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final List<DiskWarmingInstance> instance =
        _diskWarmingSvc.findWarmingInstanceByClusterId(new ObjectId(pGroupId), pClusterName);

    if (instance.isEmpty()) {
      LOG.debug(
          "Unable to find the warming instance for cluster {} in group {}", pClusterName, pGroupId);
      return Response.ok().entity(new JSONObject().toString()).build();
    }

    LOG.info(String.format("Found warming instance %s", instance.get(0).toString()));

    final JSONObject responseBody = new JSONObject();
    responseBody
        .put("state", instance.get(0).getState().name())
        .put("lastUpdatedDate", instance.get(0).getLastUpdatedDate().getTime());
    return Response.ok().entity(responseBody.toString()).build();
  }

  @GET
  @Path("nds/groups/{groupId}/automationConfigVersion")
  @UiCall(auth = false)
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  public Response getAutomationConfigVersion(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final AutomationConfig config = _automationConfigPublishingSvc.findPublished(pGroupId);
    if (config == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    return Response.ok()
        .entity(new JSONObject().put("currentPublishedVersion", config.getVersion()).toString())
        .build();
  }

  @PATCH
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/setMaxDiskSizeGB")
  @UiCall(auth = false)
  public Response setComputeSetDiskSizeGBWithNoAutoScalingMinSizeValidation(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {
    final Optional<ClusterDescription> clusterDescription =
        _clusterDescriptionDao.findByName(pGroupId, pClusterName);

    if (clusterDescription.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "ERROR: unable to find cluster - groupId: %s, clusterName: %s",
                  pGroupId, pClusterName))
          .build();
    }
    final Group pGroup = _groupSvc.findById(pGroupId);
    final boolean extendMaxAllowedDiskSize = _ndsGroupSvc.shouldExtendMaxAllowedDiskSize(pGroup);
    final int diskSizeGB =
        clusterDescription
            .get()
            .getMaxAllowedDiskSizeGB(
                NodeType.ELECTABLE,
                _groupSvc.getExtendedDiskRestrictions(pGroup, extendMaxAllowedDiskSize));

    final ClusterDescription.Builder<?, ClusterDescription> builder =
        clusterDescription.get().copy();

    final List<ReplicationSpec> newSpecs =
        clusterDescription.get().getReplicationSpecsWithShardData().stream()
            .map(
                rs -> {
                  final List<RegionConfig> regionConfigs =
                      rs.getRegionConfigs().stream()
                          .map(
                              rc ->
                                  switch (rc.getCloudProvider()) {
                                    case AWS -> {
                                      AWSNDSInstanceSize baseInstanceSize =
                                          (AWSNDSInstanceSize)
                                              rc.getElectableSpecs().getInstanceSize();
                                      AWSNDSInstanceSize analyticsInstanceSize =
                                          (AWSNDSInstanceSize)
                                              rc.getAnalyticsSpecs().getInstanceSize();
                                      final int baseIops =
                                          baseInstanceSize.getGP3StandardEBSIOPS(diskSizeGB);
                                      final int analyticsIops =
                                          analyticsInstanceSize.getGP3StandardEBSIOPS(diskSizeGB);
                                      final int baseThroughput =
                                          baseInstanceSize.getMinThroughput(diskSizeGB);
                                      final int analyticsThroughput =
                                          analyticsInstanceSize.getMinThroughput(diskSizeGB);

                                      yield rc.copy()
                                          .updateHardware(
                                              new AWSHardwareSpec.Builder()
                                                  .setDiskIOPS(baseIops)
                                                  .setDiskThroughput(baseThroughput),
                                              NodeTypeFamily.BASE)
                                          .updateHardware(
                                              new AWSHardwareSpec.Builder()
                                                  .setDiskIOPS(analyticsIops)
                                                  .setDiskThroughput(analyticsThroughput),
                                              NodeTypeFamily.ANALYTICS)
                                          .build();
                                    }
                                    case AZURE -> {
                                      final AzureDiskType diskType =
                                          rc.getHardwareSpecByNodeType(NodeType.ELECTABLE)
                                                  .isOnAzureSsdV2()
                                              ? AzureDiskType.V2
                                              : AzureDiskType.getForArbitrarySizeGB(diskSizeGB)
                                                  .orElseThrow();
                                      yield rc.copy()
                                          .updateHardware(
                                              new AzureHardwareSpec.Builder().setDiskType(diskType),
                                              NodeTypeFamily.BASE)
                                          .updateHardware(
                                              new AzureHardwareSpec.Builder().setDiskType(diskType),
                                              NodeTypeFamily.ANALYTICS)
                                          .build();
                                    }
                                    default -> rc;
                                  })
                          .toList();

                  return rs.copy().setRegionConfigs(regionConfigs).build();
                })
            .toList();

    _ndsClusterSvc.updateCluster(
        builder.setDiskSizeGB(diskSizeGB).setReplicationSpecList(newSpecs).build(),
        null,
        AuditInfoHelpers.fromSystem(),
        pRequest,
        ClusterUpdateContext.forIntTestAction());

    final JsonObject body = new JsonObject();
    body.addProperty("diskSizeGB", diskSizeGB);

    return Response.ok(body.toString()).build();
  }

  @POST
  @Path("/v1/getMMSTopDiskUsageStats")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getMMSTopDiskUsageStats(
      @Context final HttpServletRequest request, final String pBody) {
    final JSONObject reqBody = new JSONObject(pBody);
    String reqDir = reqBody.optString("dir", "/data");
    String reqDepth = reqBody.optString("depth", "10");
    String reqNumResults = reqBody.optString("numResults", "20");
    LOG.info("In POST /v1/getMMSTopDiskUsageStats");
    String str;
    Process process;
    int lineNum = 0;
    String[] cmd = {
      "/bin/sh",
      "-c",
      "du -d " + reqDepth + " -h " + reqDir + " | sort -rh" + " | head -n " + reqNumResults
    };
    final JsonObject respBody = new JsonObject();
    try {
      process = Runtime.getRuntime().exec(cmd);
      BufferedReader br = new BufferedReader(new InputStreamReader(process.getInputStream()));
      while ((str = br.readLine()) != null) {
        LOG.info("line: {}", str);
        respBody.addProperty(String.valueOf(lineNum++), str.replaceAll("\\s+", " "));
      }
      process.waitFor();
      LOG.info("exit: {}", process.exitValue());
      process.destroy();
    } catch (Exception e) {
      LOG.error("Error while getting disk usage", e);
    }
    return Response.ok(respBody.toString()).build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/clusters/mongoDBVersion/latest")
  @UiCall(auth = false)
  public Response getLatestMongoDBVersion(@Context final HttpServletRequest pRequest) {
    final BasicDBObject response = new BasicDBObject();
    _versionDeprecationSettingsSvc
        .getSupportedMajorVersions()
        .forEach(v -> response.put(v, _ndsClusterSvc.getDefaultMongoDBVersion(v)));
    return Response.ok(response).build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/clusters/mongoDBVersion/latest/{version}")
  @UiCall(auth = false)
  public Response getOneLatestMongoDBVersion(
      @Context final HttpServletRequest pRequest, final @PathParam("version") String version) {
    final BasicDBObject response = new BasicDBObject();
    _versionDeprecationSettingsSvc.getSupportedMajorVersions().stream()
        .filter(v -> v.equals(version))
        .forEach(v -> response.put(v, _ndsClusterSvc.getDefaultMongoDBVersion(v)));
    return Response.ok(response).build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/clusters/mongoDBVersion/latestLtsWithSuccessor")
  @UiCall(auth = false)
  public Response getLatestLtsWithSuccessorMongoDBVersion(
      @Context final HttpServletRequest pRequest) {
    final BasicDBObject response = new BasicDBObject();

    final List<String> supportedMajorVersions =
        _versionDeprecationSettingsSvc.getSupportedMajorVersions();

    final Version latestCdVersion = new Version(_ndsClusterSvc.getDefaultCDMongoDBVersion());

    final String latestLtsWithSuccessorVersion;
    if (latestCdVersion.isLTSRelease()) {
      // if the latest continuous delivery version is also an LTS version, then we know that
      // this LTS version has no current successor, so we must return the previous LTS version
      latestLtsWithSuccessorVersion = supportedMajorVersions.get(supportedMajorVersions.size() - 2);
    } else {
      latestLtsWithSuccessorVersion = supportedMajorVersions.get(supportedMajorVersions.size() - 1);
    }

    response.put(
        "latestLtsWithSuccessorMdbVersion",
        _ndsClusterSvc.getDefaultMongoDBVersion(latestLtsWithSuccessorVersion));

    return Response.ok(response).build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/clusters/mongoDBVersion/latestLts")
  @UiCall(auth = false)
  public Response getLatestLtsMongoDBVersion(@Context final HttpServletRequest pRequest) {
    final BasicDBObject response = new BasicDBObject();

    final List<String> supportedMajorVersions =
        _versionDeprecationSettingsSvc.getSupportedMajorVersions();
    final String latestLtsVersion = supportedMajorVersions.get(supportedMajorVersions.size() - 1);
    response.put("latestLtsMdbVersion", _ndsClusterSvc.getDefaultMongoDBVersion(latestLtsVersion));

    return Response.ok(response).build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/clusters/mongoDBVersion/continuousDeliveryVersion")
  @UiCall(auth = false)
  public Response getContinuousDeliveryMongoDBVersion(@Context final HttpServletRequest pRequest) {
    final BasicDBObject response = new BasicDBObject();
    response.put(
        SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION.name(),
        _ndsClusterSvc.getDefaultCDMongoDBVersion());
    response.put(
        SoftwareType.CONTINUOUS_DELIVERY_FCV.name(),
        _ndsClusterSvc.getDefaultContinuousDeliveryFCV(VersionReleaseSystem.CONTINUOUS));
    return Response.ok(response).build();
  }

  /**
   * This function is basically a copy-pasta of ApiPrivateHostsResource::requestServerReboot and
   * CloudChefConfResource::requestServerReboot. The only reason we're exposing this here is for
   * testing convenience. There is currently no facility to be able to easily access the Public API
   * via our E2E testing infrastructure. We would need 2 things: 1. The ability to generate and
   * delete API Keys 2. The ability to whitelist Evergreen hosts IP addresses
   *
   * <p>The former is easy but the latter is more difficult
   */
  @POST
  @Path("/nds/private/hosts/reboot")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response requestServerReboot(
      @Context final HttpServletRequest pRequest,
      @FormParam("groupId") final String pGroupId,
      @FormParam("hostname") final String pHostname,
      @FormParam("isCritical") final boolean pIsCritical,
      @FormParam("requestDate") final long pRequestDateMillis,
      @FormParam("rebootRequestedChefCommitHash") final String pRebootRequestedChefCommitHash)
      throws SvcException {
    LOG.info(
        "Reboot Requested for host {} (isCritical {}) by addr {} user {}",
        pHostname,
        pIsCritical,
        pRequest.getRemoteAddr(),
        pRequest.getRemoteUser());
    _ndsClusterSvc.requestServerReboot(
        new ObjectId(pGroupId),
        pHostname,
        new Date(pRequestDateMillis),
        pIsCritical,
        pRebootRequestedChefCommitHash,
        RebootRequestedBy.OTHER);
    return Response.ok(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/nds/sampleDatasetLoad/bucketData")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getSampleDatasetBucketData() {
    final String bucket = _ndsSampleDatasetLoadSvc.getS3SampleDatasetBucketName();
    final String key = SampleDataset.DEFAULT.getS3SampleDatasetNamespacesKey();
    return Response.ok(
            new JSONObject()
                .put("host", String.format("https://%s.s3.amazonaws.com", bucket))
                .put("filename", key)
                .toString())
        .build();
  }

  @GET
  @Path("/v1/userPageViewAgg/102030")
  @Produces({MediaType.APPLICATION_JSON})
  @ApiCall(version = ApiVersion.V1, auth = false)
  public String userPageViewAgg(@Context final HttpServletRequest pRequest) {
    return "{}";
  }

  @POST
  @Path("user/addServerlessMTMGroup")
  @Produces({MediaType.APPLICATION_JSON})
  @InternalTestUsersOnly
  @UiCall(
      roles = RoleSet.ANY_AUTHENTICATED_USER,
      groupSource = UiCall.GroupSource.NONE,
      bypassMfa = true,
      csrf = false,
      appSettingsSetupCall = true)
  public Response addServerlessGroup(
      @Context final AppUser pAppUser,
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      @FormParam("organizationId") final ObjectId pOrganizationId,
      @FormParam("cloudProvider") final String pCloudProvider,
      @FormParam("regionName") final String pRegionName)
      throws SvcException {
    final CloudProvider provider = CloudProvider.valueOf(pCloudProvider);
    final RegionName regionName =
        RegionNameHelper.findByName(provider, pRegionName)
            .orElseThrow(
                () -> ApiErrorCode.INVALID_REGION.exception(false, pRegionName, provider.name()));
    final Organization organization = _organizationSvc.findById(pOrganizationId);
    final String groupName =
        _ndsAutoScaleServerlessMTMCapacitySvc.getServerlessGroupName(regionName, 0);
    final ObjectId groupId = ObjectId.get();
    final Optional<Pair<Group, NDSGroup>> responsePair =
        _ndsAutoScaleServerlessMTMCapacitySvc.createNewServerlessGroup(
            groupId, organization, groupName, provider);

    if (organization.hasNDSPlan()) {
      getNdsGroupSvc().ensureGroup(groupId, false, RegionUsageRestrictions.NONE, false);
    }

    return SimpleApiResponse.ok()
        .newObjId(groupId.toString())
        .customField("groupName", responsePair.get().getLeft().getName())
        .customField("orgId", responsePair.get().getLeft().getOrgId().toString())
        .build();
  }

  @POST
  @Path("/user/addMigrationUser")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_FORM_URLENCODED})
  @UiCall(auth = false)
  public Response registerStagedUser(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @FormParam("username") final String pUsername,
      @FormParam("password") final String pPassword,
      @FormParam("ldapPasswordHash") final String pLdapPasswordHash,
      @FormParam("universityPasswordHash") final String pUniversityPasswordHash,
      @FormParam("activate") final boolean pActive)
      throws SvcException {
    if (_appSettings.isOktaEnabled()) {
      final User oktaUser =
          _userSvcProvider
              .getUserSvcOkta()
              .createUserInOkta(
                  pUsername, pPassword, pUniversityPasswordHash, pLdapPasswordHash, pActive);
      return SimpleApiResponse.ok().customField("oktaUserId", oktaUser.getId()).build();
    }
    // error otherwise. tests requiring this call should only run when okta is enabled
    return SimpleApiResponse.error(CommonErrorCode.SERVER_ERROR)
        .customField("errorMessage", "Okta is disabled")
        .build();
  }

  @POST
  @Path("/user/registerCall")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response registerCall(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final AuditInfo pAuditInfo,
      final UserRegistrationForm pForm)
      throws SvcException {
    LOG.info(
        "Call to InternalTestUtilsResource::registerCall was received for {}",
        kv("username", pForm.getUsername()));
    final AppUser user = _userSvc.register(pForm, pAuditInfo);
    LOG.info(
        "InternalTestUtilsResource::registerCall finished user registration in Okta for {}",
        kv("username", pForm.getUsername()));
    if (_appSettings.isOktaEnabled()) {
      /* For tasks that need Okta to be on, the register call above will register the user in okta, sync their profile locally but without a password set. While this is the expectation for the webapp it's not enough for E2E testing. Even when Okta is on we perform local (db) verification of password for "api logins". This call makes sure that we persist the same password from Okta in the local user document*/
      _userDao.setPasswordHash(user.getId(), user.generatePasswordHash(pForm.getPassword()));
    }

    final String csrfTime = String.valueOf(System.currentTimeMillis());
    final SimpleApiResponse.Builder responseBuilder =
        SimpleApiResponse.ok()
            .customField("csrfToken", FilterUtils.generateCsrfToken(user.getOktaUserId(), csrfTime))
            .customField("csrfTime", csrfTime);

    // First, generate token cookies with AuthN managed tokens
    final String sessionId = generateAuthnManagedSession(pRequest, pResponse, user);
    // Then, create the legacy session cookie for envs where the authn service does not run
    _userLoginSvc.createAppSession(pRequest, pResponse, user, sessionId);
    return responseBuilder
        .resource(pRequest.getRequestURI())
        .newObjId(user.getId().toString())
        .build();
  }

  /* TODO: AM BIZSYS-30300 This can be renamed to just verify a user's email once we remove code
   * from the login process
   */
  @POST
  @Path("/user/bypassEmailVerification")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_FORM_URLENCODED})
  @UiCall(auth = false)
  public Response bypassEmailVerification(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @FormParam("username") final String pUsername)
      throws SvcException {
    if (_appSettings.isOktaEnabled()) {
      final Optional<User> maybeOktaUser = _accountUserSvc.getOktaUserByUsername(pUsername);
      if (maybeOktaUser.isPresent()) {
        final User oktaUser = maybeOktaUser.get();
        _accountUserSvc.ensureLocalUser(oktaUser);
        getUserSvc().verifyEmail(oktaUser.getId());
      }
    }
    // no-op otherwise
    return SimpleApiResponse.ok().customField("username", pUsername).build();
  }

  @DELETE
  @Path("/sessions/tokens/{tokenId}")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(auth = false, groupSource = GroupSource.NONE)
  public Response deleteSessionTokenById(@PathParam("tokenId") final String tokenId)
      throws SvcException {
    final AuthnClient authnClient = _authnClientProvider.get();

    if (authnClient == null) {
      throw new SvcException(AuthnServiceErrorCode.UNEXPECTED_ERROR);
    }

    final InvalidateTokenByIdResponse invalidateTokenByIdResponse =
        authnClient.invalidateTokenById(tokenId);

    if (invalidateTokenByIdResponse.getInvalidatedCount() == 0) {
      return Response.status(Status.NOT_FOUND).build();
    }

    return Response.noContent().build();
  }

  @PATCH
  @Path("/mfa/bypassEncouragement")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_FORM_URLENCODED})
  @UiCall(auth = false)
  public Response bypassMFAEncouragement(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @FormParam("username") final String pUsername) {
    if (_appSettings.isOktaEnabled()) {
      final Optional<AccountUser> accountUser = _accountUserSvc.findByUsername(pUsername);
      accountUser.ifPresent(
          user -> _accountMultiFactorAuthSvc.setMfaEncouragementSnoozedDate(user, new Date()));
    }
    // no-op otherwise
    return SimpleApiResponse.ok().customField("username", pUsername).build();
  }

  @PATCH
  @Path("/mfa/unsnoozeMfaEncouragement")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_FORM_URLENCODED})
  @UiCall(auth = false)
  public Response unsnoozeMfaEncouragement(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @FormParam("username") final String pUsername)
      throws SvcException {
    final Optional<AccountUser> accountUser = _accountUserSvc.findByUsername(pUsername);
    if (accountUser.isPresent()) {
      // An arbitrary number to guarantee MFA page will show up during e2e test.
      final Calendar cal = Calendar.getInstance();
      cal.setTime(new Date());
      cal.set(Calendar.MONTH, -6);
      Date sixMonthsAgo = cal.getTime();
      _accountMultiFactorAuthSvc.setMfaEncouragementSnoozedDate(accountUser.get(), sixMonthsAgo);
    }
    return SimpleApiResponse.ok().customField("username", pUsername).build();
  }

  @POST
  @Path("/mfa/org/requireMfa")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_FORM_URLENCODED})
  @UiCall(auth = false)
  public Response requireMfa(
      @Context final AuditInfo pAuditInfo, @FormParam("username") final String pUsername) {
    final AppUser appUser = getUserSvc().findByUsername(pUsername);
    _organizationSvc.setMultiFactorAuthRequired(appUser.getCurrentOrgId(), true, pAuditInfo);
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/user/createGlobalApiKey")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_SERVICE_ACCOUNT_ADMIN, groupSource = GroupSource.NONE)
  public Response createGlobalApiKey(
      @Context final AuditInfo pAuditInfo,
      @QueryParam("envelope") final boolean pEnvelope,
      final ApiBaseApiKeyView pView)
      throws Exception {
    if (StringUtils.isEmpty(pView.getDesc())
        || pView.getDesc().length() > ApiUser.DESCRIPTION_MAX_LENGTH) {
      throw ApiErrorCode.INVALID_API_KEY_DESC.exception(
          pEnvelope, UserApiKey.MAX_API_KEY_DESC_LENGTH);
    }

    if (CollectionUtils.isEmpty(pView.getRoles())) {
      throw ApiErrorCode.MISSING_API_KEY_ROLES.exception(pEnvelope);
    }

    final Set<RoleAssignment> roleAssignments =
        pView.getRoles().stream()
            .map(role -> RoleAssignment.forGlobal(role.getRole()))
            .collect(toSet());
    final ApiUser apiUser =
        _apiUserSvc.createGlobalApiUser(pView.getDesc(), roleAssignments, pAuditInfo);

    LOG.info(
        "GPAK was created for testing {}",
        StructuredArguments.entries(
            Map.of(
                "username", apiUser.getUsername(),
                "roleAssignments", Arrays.toString(apiUser.getRoleAssignments().toArray()))));

    return new ApiResponseBuilder(pEnvelope)
        .created()
        .content(
            new ApiApiUserView.Builder(_appSettings)
                .id(apiUser.getUserId())
                .publicKey(apiUser.getUsername())
                .privateKey(apiUser.getPrivateKey())
                .desc(apiUser.getDesc())
                .orgId(apiUser.getOrgId())
                .roles(apiUser.getRoleAssignments())
                .isAtlas(false)
                .build())
        .build();
  }

  @DELETE
  @Path("/user/deleteGlobalApiKey/{apiKeyId}")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GLOBAL_SERVICE_ACCOUNT_ADMIN, groupSource = GroupSource.NONE)
  public Response deleteGlobalApiKey(
      @Context final AuditInfo pAuditInfo,
      @PathParam("apiKeyId") ObjectId pApiUserId,
      @QueryParam("envelope") final boolean pEnvelope) {
    final ApiUser apiUser = _apiUserSvc.findByUserId(pApiUserId);
    try {
      // hard delete backing user along with api key
      _apiUserSvc.hardDeleteApiUser(apiUser, pAuditInfo);
    } catch (SvcException e) {
      final ErrorCode errorCode = e.getErrorCode();
      if (errorCode.equals(AppUserErrorCode.API_KEY_NOT_FOUND)) {
        throw ApiErrorCode.API_KEY_NOT_FOUND.exception(pEnvelope, pApiUserId);
      } else {
        throw ApiErrorCode.UNEXPECTED_ERROR.exception(pEnvelope);
      }
    }

    LOG.info(
        "GPAK was deleted for testing {}",
        StructuredArguments.entries(Map.of("username", apiUser.getUsername())));

    return new ApiResponseBuilder(pEnvelope).noContent().build();
  }

  @POST
  @Path("/user/createCloudPerfApiKey/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response createCloudPerfApiKey(
      @Context final AuditInfo pAuditInfo,
      @PathParam("groupId") final ObjectId pGroupId,
      @QueryParam("envelope") final boolean pEnvelope)
      throws Exception {
    final ObjectId orgId =
        Optional.ofNullable(_groupSvc.findById(pGroupId))
            .map(Group::getOrgId)
            .orElseThrow(() -> ApiErrorCode.INVALID_GROUP_ID.exception(pEnvelope, pGroupId));

    final Set<RoleAssignment> roleAssignments =
        Set.of(
            RoleAssignment.forGlobal(Role.GLOBAL_ATLAS_ADMIN),
            RoleAssignment.forGlobal(Role.GLOBAL_BILLING_ADMIN),
            RoleAssignment.forOrg(Role.ORG_OWNER, orgId));
    final ApiUser cloudPerfApiUser =
        _apiUserSvc.createGlobalApiUser("Cloud Perf Testing API Key", roleAssignments, pAuditInfo);

    return new ApiResponseBuilder(pEnvelope)
        .created()
        .content(
            new ApiApiUserView.Builder(_appSettings)
                .id(cloudPerfApiUser.getUserId())
                .publicKey(cloudPerfApiUser.getUsername())
                .privateKey(cloudPerfApiUser.getPrivateKey())
                .desc(cloudPerfApiUser.getDesc())
                .orgId(cloudPerfApiUser.getOrgId())
                .roles(cloudPerfApiUser.getRoleAssignments())
                .isAtlas(true)
                .build())
        .build();
  }

  @POST
  @Path("/enableFeatureFlag/{groupId}/{featureFlag}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response enableFeatureFlag(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("featureFlag") final String pFeatureFlagName,
      @QueryParam("envelope") final boolean pEnvelope,
      @Context final AuditInfo pAuditInfo)
      throws Exception {
    final ObjectId orgId =
        Optional.ofNullable(_groupSvc.findById(pGroupId))
            .map(Group::getOrgId)
            .orElseThrow(() -> ApiErrorCode.INVALID_GROUP_ID.exception(pEnvelope, pGroupId));
    final FeatureFlag flag = FeatureFlag.valueOf(pFeatureFlagName);
    final Organization org = _organizationSvc.findById(orgId);
    final Group group = _groupSvc.findById(pGroupId);

    // There are different flows for toggling Feature Flags depending on whether they are
    // managed by the Config Service or the MMS Legacy system.
    if (!FeatureFlag.NON_CONFIG_SERVICE_FEATURE_FLAGS.contains(flag)) {
      final String flagId =
          _configServiceSdkWrapper
              .getFeatureFlagInfo(GLOBAL_NAMESPACE, flag.getEnvironmentProperty())
              .id()
              .toHexString();
      final String entityId =
          flag.getScope().equals(Scope.GROUP) ? pGroupId.toHexString() : orgId.toHexString();
      _configAdminClientProvider
          .get()
          .bulkUpdateFeatureFlagControlList(
              flagId,
              List.of(entityId),
              LIST_TYPE.ALLOW_LIST.name(),
              WRITE_TYPE.ADD.name(),
              pAuditInfo);
    } else {
      _featureFlagSvc.enableNonConfigServiceFeatureFlag(group, org, flag);
    }

    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/user/v1/auth")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response authV1(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @Context final AuditInfo pAuditInfo,
      final UserLoginForm pUserLoginForm)
      throws Exception {
    final String userAddr = pRequest.getRemoteAddr();
    final AppUser user;

    try {
      user =
          _userSvcProvider
              .getUserSvcDB()
              .authenticate(
                  pUserLoginForm.getUsername(),
                  pUserLoginForm.getPassword(),
                  pUserLoginForm.getInviteToken(),
                  true,
                  pAuditInfo);
    } catch (final SvcException e) {
      LOG.info(
          "Login attempt from addr=\"{}\" username=\"{}\" result={}",
          userAddr,
          pUserLoginForm.getUsername(),
          e.getErrorCode());
      return translateAuthErrorCode(
          pRequest, pUserLoginForm.getUsername(), userAddr, e.getErrorCode());
    }

    LOG.info(
        "Login attempt from addr=\"{}\" username=\"{}\" result={}",
        userAddr,
        pUserLoginForm.getUsername(),
        CommonErrorCode.SUCCESS);

    String sessionId = generateAuthnManagedSession(pRequest, pResponse, user);

    // Although this method is using UserSvcDb directly, the uiAuthMethod is still marked to match
    // the UserSvc that is specified in AppSettings. At this point, the login is successful
    final var successfulLoginInfo =
        _userLoginSvc.processSuccessfulLogin(
            pRequest, pResponse, user, null, null, null, sessionId, null);

    final boolean needsMfaValidated = successfulLoginInfo.isNeedsMfa();

    if (needsMfaValidated) {
      final Optional<String> uiAuthCode =
          CookieUtils.getCookieValueFromResponseHeader(
              pResponse, "Set-Cookie", "mmsa", _appSettings.getAppEnv().getCode());

      // update the user's mfa status to validated
      uiAuthCode.ifPresent(_userSvc::updateUiAuthCodeMfaValidated);
    }
    return successfulLoginInfo.getSuccessfulLoginResponse();
  }

  @POST
  @Path("/user/addUser/{groupId}")
  @Produces({MediaType.APPLICATION_JSON})
  @InternalTestUsersOnly
  @UiCall(roles = RoleSet.GROUP_USER_ADMIN, maxLastAuthMins = 5)
  public Response addUser(
      @Context final AppUser pUser,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @FormParam("username") final String pUsername,
      @FormParam("role") final String pRoleName)
      throws SvcException {
    if (getUserSvc().userExistsInGroup(pUsername, pGroup.getId())) {
      throw new SvcException(AppUserErrorCode.USER_ALREADY_IN_GROUP);
    }
    getConfigLimitSvc()
        .validateMaxUsersPerOrg(
            getUserSvc()
                .getUsersNotInOrInvitedToOrg(
                    Collections.singletonList(pUsername), pGroup.getOrgId())
                .size(),
            pGroup.getOrgId());
    getConfigLimitSvc()
        .validateMaxUsersPerGroup(
            getUserSvc()
                .getUsersNotInOrInvitedToGroup(Collections.singletonList(pUsername), pGroup.getId())
                .size(),
            pGroup.getId());
    // Validate addition of 1 org to user relative to config limits, if applicable
    getConfigLimitSvc().validateMaxOrgsPerUser(1, pUsername, pGroup.getOrgId());
    // Validate addition of 1 group to user relative to config limits, if applicable
    getConfigLimitSvc().validateMaxGroupsPerUser(1, pUsername, pGroup.getId());

    final Role role = Role.fromString(pRoleName);
    if (role == null) {
      throw new SvcException(AppUserErrorCode.INVALID_ROLE_NAME);
    }
    /* BAAS is current using this endpoint to assign roles to Programmatic API Keys that won't be
    created in Okta. Until this ticket is resolved: https://jira.mongodb.org/browse/BAAS-17101,
    we should still use UserSvcDB to add the user since we check if the user exists in our system
    of record (Okta if Okta is on) where an API key won't exist */
    _userSvcProvider
        .getUserSvcDB()
        .addUserToGroup(pUsername, pGroup.getId(), Collections.singleton(role), pAuditInfo);
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/liveImportReservedPorts/{liveImportId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response getReservedPortsForLiveImport(
      @Context final AuditInfo pAuditInfo,
      @PathParam("liveImportId") final ObjectId pLiveImportId) {
    try (ModelCursor<ReservedPort> cursor =
        _reservedPortDao.findAllReservedFor(pLiveImportId.toString())) {
      final List<BasicDBObject> reservedForLiveImport =
          StreamSupport.stream(cursor.spliterator(), false)
              .map(InternalTestUtilsResource::reservedPortToDBObject)
              .collect(Collectors.toList());
      return Response.ok().entity(reservedForLiveImport).build();
    } catch (Exception pE) {
      LOG.warn("Failed to retrieve reserved ports", pE);
      return Response.serverError().build();
    }
  }

  @PATCH
  @Path("/liveImport/{liveImportId}/errorMessage")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @UiCall(auth = false)
  public Response setLiveImportErrorMessage(
      @Context final HttpServletRequest pRequest,
      @PathParam("liveImportId") final ObjectId pLiveImportId) {
    final Optional<LiveImport> maybeLiveImport = _liveImportDao.find(pLiveImportId);
    if (maybeLiveImport.isEmpty()) {
      return Response.status(Status.NOT_FOUND).build();
    }

    try {
      final Map<String, String> requestBody =
          CustomJacksonJsonProvider.createObjectMapper()
              .readValue(pRequest.getInputStream(), new TypeReference<>() {});

      _liveImportDao.setErrorMessage(
          new ObjectId(requestBody.get("groupId")),
          new ObjectId(requestBody.get("liveImportId")),
          MigrationToolType.valueOf(requestBody.get("migrationToolType")),
          Integer.parseInt(requestBody.get("shardIndex")),
          requestBody.get("errorMessage"));
    } catch (IOException pE) {
      LOG.warn("Failed to set liveImport errorMessage", pE);
      return Response.serverError().build();
    }

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/user/checkPassword")
  @Produces({MediaType.APPLICATION_JSON})
  @InternalTestUsersOnly
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response checkPassword(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @FormParam("password") final String pPassword)
      throws SvcException {
    _userSvc.checkPassword(pUser, pPassword, _userLoginSvc.getUiAuthCode(pRequest));
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/user/currentGroup")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = UiCall.GroupSource.NONE)
  public Response currentGroup(@Context final AppUser pUser) {
    final Map<String, String> response = new HashMap<>();
    Group group = _groupSvc.findById(pUser.getCurrentGroupId());

    if (group == null) {
      return Response.ok().entity(response).build();
    }

    if (!_groupSvc.canUserAccessGroup(pUser, group)) {
      LOG.warn(
          "user: {} - trying to access group (without permission): {}",
          pUser.getId(),
          group.getId());
      Response.status(Response.Status.FORBIDDEN).build();
    }

    response.put("groupId", group.getId().toString());
    response.put("name", group.getName());
    response.put("orgId", group.getOrgId().toString());

    return Response.ok().entity(response).build();
  }

  @POST
  @Path("/group/{groupId}/access/{requestId}/expire")
  @UiCall(auth = false)
  public Response expireSshAccess(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("requestId") final ObjectId pRequestId) {
    getNdsAdminSvc()
        .setAccessValidUntilDate(
            pGroupId, pRequestId, new DateTime(new Date()).minusDays(1).toDate());
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/group/{groupId}/cluster/{clusterName}/host/{hostname}/health/expire")
  @UiCall(auth = false)
  public Response expireHostHealth(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("hostname") final String pHostname,
      @FormParam("cloudProvider") final CloudProvider pProvider) {
    final List<ReplicaSetHardware> replicaSetHardwares =
        getNdsLookupSvc().getReplicaSetHardwareForCluster(pGroupId, pClusterName);
    if (replicaSetHardwares.isEmpty()) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }
    final ReplicaSetHardware hardware = replicaSetHardwares.get(0);
    final ObjectId instanceId =
        hardware
            .getAllHardware()
            .filter(ih -> ih.getHostnameForAgents().get().equals(pHostname))
            .findFirst()
            .get()
            .getInstanceId();

    final Date nowBackTenMinutes = new DateTime(new Date()).minusMinutes(10).toDate();
    final Date threeHoursBack = new DateTime(nowBackTenMinutes).minusHours(3).toDate();

    try {
      final InstanceHardwareHealth health =
          generateExpiredInstanceHardwareHealth(pProvider, nowBackTenMinutes, threeHoursBack);

      getReplicaSetHardwareSvc()
          .updateInstanceHealth(
              hardware.getId(), instanceId, hardware.isInstanceInternal(instanceId), health);

      return Response.ok().entity(EMPTY_JSON_OBJECT).build();

    } catch (final IllegalStateException pE) {
      return Response.status(Response.Status.NOT_IMPLEMENTED).build();
    }
  }

  @POST
  @Path("/group/{groupId}/cluster/{clusterName}/host/{hostname}/lastUpdate/expire")
  @UiCall(auth = false)
  public Response expireHostLastUpdate(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("hostname") final String pHostname) {
    final List<ReplicaSetHardware> replicaSetHardwares =
        getNdsLookupSvc().getReplicaSetHardwareForCluster(pGroupId, pClusterName);
    if (replicaSetHardwares.isEmpty()) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }
    final ReplicaSetHardware hardware = replicaSetHardwares.get(0);
    final ObjectId instanceId =
        hardware
            .getAllHardware()
            .filter(ih -> ih.getHostnameForAgents().get().equals(pHostname))
            .findFirst()
            .get()
            .getInstanceId();

    final Date nowBackTenMinutes = new DateTime(new Date()).minusMinutes(10).toDate();
    final Date threeHoursBack = new DateTime(nowBackTenMinutes).minusHours(3).toDate();

    getReplicaSetHardwareSvc()
        .updateInstanceLastUpdate(
            hardware.getId(), instanceId, hardware.isInstanceInternal(instanceId), threeHoursBack);

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path(
      "/group/{groupId}/cluster/{clusterName}/hardwares/providers/{provider}/regions/{regionName}")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getHardwareHostnamesByProviderRegion(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("provider") final String pProvider,
      @PathParam("regionName") final String pRegionName)
      throws SvcException {
    final CloudProvider provider = CloudProvider.findByName(pProvider.toUpperCase(Locale.ROOT));
    final RegionName region = RegionNameHelper.findByNameOrElseThrow(provider, pRegionName);

    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(pGroupId);
    final List<ReplicaSetHardware> replicaSetHardware =
        _replicaSetHardwareSvc.getReplicaSetHardware(pGroupId, pClusterName);

    final List<JSONObject> hosts =
        replicaSetHardware.stream()
            .flatMap(rsh -> rsh.getHardware().stream())
            .filter(InstanceHardware::isProvisioned)
            .filter(ih1 -> ih1.getCloudProvider().equals(provider))
            .filter(
                ih1 ->
                    ih1.getRegion(ndsGroup.getCloudProviderContainer(ih1.getCloudContainerId()))
                        .equals(region))
            .map(
                ih -> {
                  final Hostnames h = ih.getHostnames();
                  return new JSONObject()
                      .put("hostname", h.getPublicHostname().or(h::getLegacyHostname).orElseThrow())
                      .put("publicIpAddress", ih.getPublicIP().get());
                })
            .collect(Collectors.toList());

    return Response.status(Status.OK)
        .type(MediaType.APPLICATION_JSON)
        .entity(new JSONObject().put("hosts", new JSONArray(hosts)).toString())
        .build();
  }

  @GET
  @Path("/group/{groupId}/cluster/{clusterName}/instanceHorizonsFromShardsAndConfig")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getHostnameFromReplicaSetHardware(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final List<ReplicaSetHardware> replicaSetHardware =
        _replicaSetHardwareSvc.getReplicaSetHardware(pGroupId, pClusterName);

    final JSONObject response = new JSONObject();

    final Optional<JSONObject> configServerHostnames =
        replicaSetHardware.stream()
            .filter(ReplicaSetHardware::containsConfigData)
            .map(
                shard ->
                    new JSONObject(
                        shard.getHardware().get(0).getHostnames().stream()
                            .collect(
                                Collectors.toMap(
                                    h -> h.getScheme().toString(), InstanceHostname::getHostname))))
            .findFirst();
    configServerHostnames.ifPresent(csh -> response.put("configServerHorizon", csh));

    response.put(
        "shardHorizons",
        replicaSetHardware.stream()
            .filter(ReplicaSetHardware::containsShardData)
            .map(shard -> shard.getHardware().get(0))
            .map(
                instance ->
                    new JSONObject(
                        instance.getHostnames().stream()
                            .collect(
                                Collectors.toMap(
                                    h -> h.getScheme().toString(), InstanceHostname::getHostname))))
            .toList());

    return Response.ok().entity(response.toString()).build();
  }

  @PATCH
  @Path("/group/{groupId}/cluster/{clusterName}/pausedDate/{daysToSubtract}")
  @UiCall(auth = false)
  public Response expireHostLastUpdate(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("daysToSubtract") final Integer pDaysToSubtract) {
    final Optional<ClusterDescription> clusterDescription =
        getNdsClusterSvc().getActiveClusterDescription(pGroupId, pClusterName);
    if (clusterDescription.isEmpty()) {
      Response.status(Response.Status.NOT_FOUND).build();
    }

    getNdsClusterSvc()
        .setPausedDateForCluster(
            clusterDescription.get(), DateUtils.addDays(new Date(), -pDaysToSubtract));

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/group/{groupId}/cluster/{clusterName}/createDate/{daysToSubtract}")
  @UiCall(auth = false)
  public Response setClusterCreateDate(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("daysToSubtract") final Integer pDaysToSubtract) {
    final Optional<ClusterDescription> clusterDescription =
        getNdsClusterSvc().getActiveClusterDescription(pGroupId, pClusterName);
    if (clusterDescription.isEmpty()) {
      LOG.error("Cluster not found");
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    getNdsClusterSvc()
        .setCreateDateForCluster(
            clusterDescription.get(), DateUtils.addDays(new Date(), -pDaysToSubtract));

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @PATCH
  @Path("/group/{groupId}/container/{containerId}/setContainerLastUpdateDate/{daysToSubtract}")
  @UiCall(auth = false)
  public Response setCloudContainerLastUpdateDate(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("containerId") final ObjectId pContainerId,
      @PathParam("daysToSubtract") final Integer pDaysToSubtract) {

    getNdsGroupDao()
        .setCloudContainerFields(
            pGroupId, pContainerId, DateUtils.addDays(new Date(), -pDaysToSubtract), List.of());

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/group/{groupId}/planningDate/updateNow")
  @UiCall(auth = false)
  public Response setPlanningDateToNow(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    getNdsGroupSvc().setPlanningNow(pGroupId);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("/group/{groupId}/healthCheckDate/updateNow")
  @UiCall(auth = false)
  public Response setHeathCheckDateToNow(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    getNdsGroupSvc().setHealthCheckNow(pGroupId);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/alert/html/{alertType}/{eventType}")
  @UiCall(auth = false)
  @Produces(MediaType.TEXT_HTML)
  public Response testAlertHtml(
      @PathParam("alertType") final String pAlertType,
      @PathParam("eventType") final String pEventType,
      @QueryParam("metricType") final String pMetricType,
      @QueryParam("status") final Alert.Status pStatus,
      @QueryParam("verbose") final boolean pVerbose,
      @QueryParam("groupType") final GroupType pGroupType) {
    final EmailPayload payload =
        generateTestAlertEmail(pAlertType, pEventType, pMetricType, pStatus, pVerbose, pGroupType);
    if (payload == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    return Response.ok().entity(payload.getHtml()).build();
  }

  @GET
  @Path("/alert/text/{alertType}/{eventType}")
  @UiCall(auth = false)
  @Produces(MediaType.TEXT_PLAIN)
  public Response testAlertText(
      @PathParam("alertType") final String pAlertType,
      @PathParam("eventType") final String pEventType,
      @QueryParam("metricType") final String pMetricType,
      @QueryParam("status") final Alert.Status pStatus,
      @QueryParam("verbose") final boolean pVerbose,
      @QueryParam("groupType") final GroupType pGroupType) {
    final EmailPayload payload =
        generateTestAlertEmail(pAlertType, pEventType, pMetricType, pStatus, pVerbose, pGroupType);
    if (payload == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    return Response.ok().entity(payload.getText()).build();
  }

  @GET
  @Path("/paymentStatusEmail")
  @UiCall(auth = false)
  @Produces(MediaType.TEXT_PLAIN)
  public Response testPaymentStatusEmail(
      @QueryParam("state") final String pState, @QueryParam("recipient") final String pRecipient) {
    if (StringUtils.isBlank(pRecipient)) {
      return Response.serverError().entity("ERROR: Missing required parameter `recipient`").build();
    }
    final Organization fakeOrg =
        new Organization.Builder()
            .id(ObjectId.get())
            .name("Fake Organization")
            .billingEmail(pRecipient)
            .paymentStatus(OrgPaymentStatus.ok(new Date()))
            .build();

    if ("suspend".equalsIgnoreCase(pState)) {
      getDelinquentOrganizationEmailSvc().sendSuspendedEmail(fakeOrg);
    } else if ("lock".equalsIgnoreCase(pState)) {
      getDelinquentOrganizationEmailSvc().sendLockedEmail(fakeOrg);
    } else if ("activate".equalsIgnoreCase(pState)) {
      getDelinquentOrganizationEmailSvc().sendReactivatedEmail(fakeOrg);
    } else if ("tempReactivate".equalsIgnoreCase(pState)) {
      final Date d20991231 = TimeUtils.fromISOString("2099-12-31");
      getDelinquentOrganizationEmailSvc().sendTemporarilyActivatedEmail(fakeOrg, d20991231);
    } else {
      return Response.serverError()
          .entity(
              "ERROR: Missing or invalid `state` parameter. Try suspend, lock, activate, or"
                  + " tempReactivate")
          .build();
    }
    return Response.ok()
        .entity("Email sent to " + pRecipient + " to " + pState + " organization")
        .build();
  }

  @GET
  @Path("/liveImportCount")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getApplicationDatabaseNamespaceCounts() {
    final JSONObject namespaceCounts =
        getApplicationDatabaseNamespaceCounts(_mongoSvc.getMongo("mmsdbconfig"));
    return Response.ok().entity(namespaceCounts.toString()).build();
  }

  @GET
  @Path("/systemTime")
  @UiCall(auth = false)
  @Produces(MediaType.TEXT_PLAIN)
  public Response getSystemTime() {
    return Response.ok().entity(String.valueOf(System.currentTimeMillis())).build();
  }

  @GET
  @Path("/emailByRecipient")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getEmailByRecipient(
      @Context final HttpServletRequest pRequest, @QueryParam("recipient") final String pRecipient)
      throws JSONException {
    if (pRecipient == null) {
      return Response.serverError().entity("ERROR: Missing required parameter `recipient`").build();
    }

    final JSONArray emailMsgsArray = new JSONArray();
    final JSONObject emailMsgsObject = new JSONObject();

    for (final EmailMsg email : getEmailSvc().findEmailsByRecipient(pRecipient, 0, 5)) {
      emailMsgsObject.put("subject", email.getSubject());
      emailMsgsObject.put("content", email.getContent());
      emailMsgsObject.put("recipient", email.getRecipient());
      emailMsgsObject.put("id", email.getId());
      emailMsgsObject.put("created", email.getCreated());
      emailMsgsArray.put(new JSONObject(emailMsgsObject.toString()));
    }

    return Response.ok().entity(emailMsgsArray.toString()).build();
  }

  @GET
  @Path("/communication/search")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getEmailByRecipient(
      @QueryParam("messageId") final ObjectId messageId,
      @QueryParam("organizationId") final ObjectId organizationId,
      @QueryParam("projectId") final ObjectId projectId,
      @QueryParam("email") final String email,
      @QueryParam("providerType") final ProviderType providerType,
      @QueryParam("status") final com.xgen.cloud.communication._public.model.enums.Status status,
      @QueryParam("startDate") final String startDate,
      @QueryParam("endDate") final String endDate) {
    final ResourceId resource =
        projectId != null
            ? new ResourceId(ResourceType.PROJECT, projectId)
            : (organizationId != null
                ? new ResourceId(ResourceType.ORGANIZATION, organizationId)
                : null);

    final ResourceType resourceType = resource == null ? null : resource.getType();
    final ObjectId resourceId = resource == null ? null : resource.getId();
    final String jsonString =
        new Gson()
            .toJson(
                _communicationClient.getMessages(
                    messageId,
                    resourceType,
                    resourceId,
                    email,
                    providerType,
                    status,
                    startDate,
                    endDate));
    return Response.ok().entity(jsonString).build();
  }

  @GET
  @Path("/nds/proxy/{groupId}/{clusterName}/allagentspinged")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response haveAllProxyAgentsPinged(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final String pGroupIdStr,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    final boolean allAgentsPinged =
        _ndsProxyAuditSvc.haveAllProxyAgentsPingedBack(new ObjectId(pGroupIdStr), pClusterName);
    final Map<String, Boolean> result = new HashMap<>();
    result.put("result", allAgentsPinged);
    return Response.ok(result).build();
  }

  @GET
  @Path("/nds/proxy/{groupId}/{clusterName}/agentVersion")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getProxyAgentVersion(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final String pGroupIdStr,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    final String proxyAgentVersion =
        _ndsProxyAuditSvc.getProxyAgentVersion(new ObjectId(pGroupIdStr), pClusterName);
    final Map<String, String> result = new HashMap<>();
    result.put("version", proxyAgentVersion);
    return Response.ok(result).build();
  }

  @GET
  @Path("/automation/mongodb-releases/available")
  @UiCall(auth = false)
  @Produces("application/json")
  public Response getAvailableMongoDBReleases(@Context final HttpServletRequest pRequest) {
    try {
      final String automationVersionsDirValue =
          _appSettings.getStrProp(AUTOMATION_VERSIONS_DIRECTORY.value);

      // map builds to array
      final JSONArray results = new JSONArray();
      findAvailableMongoDBReleases(automationVersionsDirValue).stream()
          .map(java.nio.file.Path::toString)
          .forEach(results::put);
      return Response.ok(results).build();

    } catch (final Exception e) {
      LOG.error("Unexpected error while retrieving available builds", e);
      return Response.serverError().entity(e.getMessage()).build();
    }
  }

  @DELETE
  @Path("/automation/mongodb-releases/available/{version}")
  @UiCall(auth = false)
  @Produces("application/json")
  public Response deleteMongoDBReleases(
      @Context final HttpServletRequest pRequest, @PathParam("version") final String pVersion) {
    try {
      final String automationVersionsDirValue =
          _appSettings.getStrProp(AUTOMATION_VERSIONS_DIRECTORY.value);

      // create a list of all paths to delete
      final List<java.nio.file.Path> pathsToDelete =
          findAvailableMongoDBReleases(automationVersionsDirValue).stream()
              .filter(path -> path.toString().contains(pVersion))
              .map(path -> Paths.get(automationVersionsDirValue).resolve(path))
              .collect(Collectors.toList());

      // then delete them
      for (final java.nio.file.Path path : pathsToDelete) {
        FileUtils.deleteQuietly(path.toFile());
      }

      // return what was deleted
      return Response.ok(pathsToDelete).build();

    } catch (final Exception e) {
      LOG.error("Unexpected error while deleting builds for {}", pVersion, e);
      return Response.serverError().entity(e.getMessage()).build();
    }
  }

  /**
   * Returns the part of the access log if it exists, the error is thrown otherwise.
   *
   * @param pSizeInBytes (optional) the size of the log
   */
  @GET
  @Path("/logs/access")
  @UiCall(auth = false)
  @Produces(MediaType.TEXT_PLAIN)
  public Response getAccessLogs(@QueryParam("sizeBytes") final Integer pSizeInBytes)
      throws IOException {
    // Let's restrict the maximum size read to 1Mb
    final int size = Math.max(pSizeInBytes == null ? 1_000_000 : pSizeInBytes, 1_000_000);

    try (final InputStream input =
            ByteStreams.limit(
                Files.newInputStream(Paths.get(System.getProperty("log_path") + "-access.log")),
                size);
        final Reader inputReader = new BufferedReader(new InputStreamReader(input))) {
      return Response.ok(CharStreams.toString(inputReader)).build();
    }
  }

  @POST
  @Path("/group/{groupId}/tenant/cluster/{clusterName}/setNextBackupDateNow")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response setNextBackupDateNow(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {

    final Optional<ClusterDescription> clusterDescriptionOpt =
        _clusterDescriptionDao.findByName(pGroupId, pClusterName);

    if (clusterDescriptionOpt.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }

    final ClusterDescription clusterDescription = clusterDescriptionOpt.orElseThrow();

    if (!clusterDescription.isFlexOrSharedTenantCluster()) {
      return Response.status(Response.Status.BAD_REQUEST).build();
    }

    _tenantClusterDescriptionDao.updateTenantClusterNextBackupDate(
        clusterDescription.getGroupId(), clusterDescription.getUniqueId(), new Date());

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/group/{groupId}/tenant/cluster/{clusterName}/removeM0ClusterActivity")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response removeM0ClusterActivity(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    try {
      _ndsM0ClusterActivityDao.removeClusterActivity(pClusterName, pGroupId);
      _ndsGroupSvc.setPlanningNow(pGroupId);
      return Response.ok().build();
    } catch (final Exception pEx) {
      return SimpleApiResponse.error(CommonErrorCode.SERVER_ERROR).build();
    }
  }

  @GET
  @Path("/group/{groupId}/tenant/cluster/{clusterUniqueId}/m0PauseSnapshot")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getTenantAutomaticPauseSnapshot(
      @Context final HttpServletRequest pRequest,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final Optional<TenantBackupSnapshot> snapshot =
        _ndsTenantPauseSvc.findMostRecentPauseSnapshotByCluster(pGroupId, pClusterUniqueId);
    if (snapshot.isEmpty()) {
      throw new SvcException(CommonErrorCode.NOT_FOUND);
    }
    final JSONObject json = new JSONObject();
    json.put("status", snapshot.get().getState().toString());
    json.put("id", snapshot.get().getId().toString());
    return Response.ok(json.toString()).build();
  }

  @GET
  @Path("/group/{groupId}/tenant/cluster/{clusterName}/m0PauseRestore")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getTenantAutomaticPauseRestore(
      @Context final HttpServletRequest pRequest,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final List<TenantRestore> restores =
        _ndsTenantPauseSvc.findRestoresByCluster(pGroupId, pClusterName);
    if (restores.isEmpty()) {
      throw new SvcException(CommonErrorCode.NOT_FOUND);
    }
    final TenantRestore restore = restores.get(0);
    final JSONObject json = new JSONObject();
    json.put("status", restore.getState());
    json.put("id", restore.getId());

    return Response.ok(json.toString()).build();
  }

  @GET
  @Path("/group/{groupId}/useCNRegionsOnly")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getUseCNRegionsOnly(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final NDSGroup ndsGroup =
        getNdsGroupDao()
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    return Response.ok(
            new JSONObject().put("useCNRegionsOnly", ndsGroup.useCNRegionsOnly()).toString())
        .build();
  }

  @GET
  @Path("/group/{groupId}/useGovRegionsOnly")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getUseGovRegionsOnly(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId)
      throws SvcException {
    final NDSGroup ndsGroup =
        getNdsGroupDao()
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    return Response.ok(
            new JSONObject()
                .put("useGovRegionsOnly", ndsGroup.getRegionUsageRestrictions().isGovRegionsOnly())
                .toString())
        .build();
  }

  @GET
  @Path("/isNDSGovUSEnabled")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getIsNDSGovUSEnabled(@Context final HttpServletRequest pRequest)
      throws SvcException {
    return Response.ok(
            new JSONObject().put("isNDSGovUSEnabled", _appSettings.getNDSGovUSEnabled()).toString())
        .build();
  }

  @POST
  @Path("/group/{groupId}/tenant/cluster/{clusterUniqueId}/setUserNotifiedAboutPauseDate")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response setUserNotifiedAboutPauseDate(
      @Context final HttpServletRequest pRequest,
      @PathParam("clusterUniqueId") final ObjectId pClusterUniqueId,
      @PathParam("groupId") final ObjectId pGroupId) {
    final Date userNotifiedDate =
        DateUtils.addDays(new Date(), -INDSDefaults.PAUSE_IDLE_M0_CLUSTER_EMAIL_WARNING_BUFFER);
    _freeTenantClusterDescriptionDao.updateFreeTenantClusterUserNotifiedAboutPauseDate(
        pGroupId, pClusterUniqueId, userNotifiedDate);
    _ndsGroupSvc.setPlanningNow(pGroupId);

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("group/{groupId}/{rsId}/setBlockstoreMinBlockSize/{blockSize}")
  @UiCall(auth = false)
  public Response setBlockstoreMinBlockSize(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId,
      @PathParam("blockSize") final int pBlockSize) {
    final BackupStatus status = _jobDao.getBackupStatus(pGroupId, pRsId);
    final SnapshotStoreType snapshotStoreType = status.getSnapshotStoreType();
    _jobDao.setBlockstoreMinBlockSize(pGroupId, pRsId, snapshotStoreType, pBlockSize);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("group/{groupId}/{rsId}/setNextWTCSnapshotTimestamp/{nextSnapshot}")
  @UiCall(auth = false)
  public Response setNextWTCSnapshotTimestamp(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId,
      @PathParam("nextSnapshot") final int pNextSnapshot) {
    final BackupStatus status = _jobDao.getBackupStatus(pGroupId, pRsId);
    final SnapshotSchedule schedule = status.getSnapshotSchedule();
    final BSONTimestamp nextSnapshotTimestamp = new BSONTimestamp(pNextSnapshot, 1);
    schedule.setReferencePoint(nextSnapshotTimestamp.getTime());
    _jobDao.setSnapshotSchedule(
        status.getGroupId(), status.getRsId(), schedule, nextSnapshotTimestamp);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/dataExport/{collectionName}/allowedFields")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getDataExportAllowedFields(
      @Context final HttpServletRequest pRequest,
      @PathParam("collectionName") final String collectionName)
      throws SvcException {
    final List<DataExportConfig> configs = _dataExportConfigDao.getAll();
    final DataExportConfig collectionConfig =
        configs.stream()
            .filter(config -> config.getCollectionName().equals(collectionName))
            .findFirst()
            .orElseThrow(() -> new SvcException(NDSErrorCode.COLLECTION_NOT_FOUND));

    final List<String> allowedFields = collectionConfig.getAllowedFields();
    final JSONArray jsonArray = new JSONArray(allowedFields);

    return Response.ok(jsonArray.toString()).build();
  }

  @GET
  @Path("/nds/sampleArchivedClusters")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getSampleArchivedClusters(@Context final HttpServletRequest pRequest) {
    final Date yesterday = DateUtils.addDays(new Date(), -1);
    final List<BasicDBObject> archivedClusters =
        _clusterDescriptionArchiveDao.findByArchiveDate(yesterday, 100);

    JSONArray jsonArray =
        new JSONArray(
            archivedClusters.stream().map(dbObj -> new JSONObject(dbObj.toMap())).toList());

    return Response.ok(jsonArray.toString()).build();
  }

  @GET
  @Path("/snapshots/{clusterId}")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getSnapshotsForShardedCluster(
      @Context final HttpServletRequest pRequest,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final ClusterStatus pStatus = _clusterStatusDao.getClusterStatus(pClusterId);

    final List<BackupStatus> backupStatuses =
        Stream.concat(pStatus.getShards().stream(), pStatus.getConfigServers().stream())
            .map(obj -> (String) obj.get("rsId"))
            .filter(Objects::nonNull)
            .distinct()
            .map(rsId -> _jobDao.getBackupStatus(pStatus.getGroupId(), rsId))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    LOG.info(
        "Found {} backup status(es) ({} shards and {} CSRS) for cluster ID {}",
        backupStatuses.size(),
        pStatus.getShards().size(),
        pStatus.getConfigServers().size(),
        pClusterId.toString());

    final JSONArray snapshotArray = new JSONArray();
    backupStatuses.stream()
        .filter(Objects::nonNull)
        .flatMap(
            backupStatus -> {
              final List<Snapshot> liveSnapshots =
                  _snapshotDao.getLiveSnapshotsForReplicaSet(backupStatus);

              backupStatus
                  .getLogger()
                  .debug("{} live snapshots found for replica set", liveSnapshots.size());

              return liveSnapshots.stream();
            })
        .map(
            snapshot ->
                BackupSnapshotResourceUtil.populateSnapshotJSONFields(
                    getGroupSvc().findById(pStatus.getGroupId()), snapshot, new JSONObject()))
        .filter(Objects::nonNull)
        .forEach(snapshotArray::put);

    final JSONObject json = new JSONObject();
    json.put("count", snapshotArray.length());
    json.put("entries", snapshotArray);

    return Response.ok(json.toString()).build();
  }

  @POST
  @Path("group/{groupId}/{userName}/customBuilds")
  @UiCall(auth = false)
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  public Response addCustomBuild(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("userName") final String pUsername,
      final CustomBuildView pBuild)
      throws SvcException {
    final Group group = _groupSvc.findById(pGroupId);
    final Organization organization = _organizationSvc.findById(group.getOrgId());
    final AppUser user = _userSvc.findByUsername(pUsername);
    final AutomationConfig config =
        _automationConfigPublishingSvc.findCurrentOrEmpty(pGroupId, user.getId());
    _customBuildsSvc.addCustomBuild(config, pBuild);
    _automationConfigPublishingSvc.saveDraft(config, user, organization, group);
    return Response.ok(pBuild).build();
  }

  @POST
  @Path("/cluster/{clusterId}/setNextWTCClustershotTimestamp/{nextClustershot}")
  @UiCall(auth = false)
  public Response setNextWTCClustershotTimestamp(
      @Context final HttpServletRequest pRequest,
      @PathParam("clusterId") final ObjectId pClusterId,
      @PathParam("nextClustershot") final int pNextClustershot) {
    final SnapshotSchedule schedule =
        _clusterStatusDao.getClusterStatus(pClusterId).getSnapshotSchedule();
    final BSONTimestamp nextClustershotTimestamp = new BSONTimestamp(pNextClustershot, 1);
    schedule.setReferencePoint(nextClustershotTimestamp.getTime());
    _clusterStatusDao.setSnapshotSchedule(pClusterId, schedule, nextClustershotTimestamp);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("group/{groupId}/{rsId}/clearTailTargetHostname")
  @UiCall(auth = false)
  public Response clearTailTargetHostname(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final DBObject update = new BasicDBObject();
    final DBObject toSet = new BasicDBObject();

    toSet.put("wtBackup.oplogTailTarget.hostname", "");

    update.put("$set", toSet);
    _jobDao.getDbCollection().update(getJobQuery(pGroupId, pRsId), update, false, false);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/wtFileStats")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getWTFileStats(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final BackupStatus backupStatus = _jobDao.getBackupStatus(pGroupId, pRsId);

    final List<Snapshot> snapshots =
        _snapshotDao.getAllSnapshotsForTests(
            backupStatus.getGroupId(), backupStatus.getRsId(), false);

    final List<JSONObject> wtFileStats =
        snapshots.stream()
            .map(Snapshot::getBackupId)
            .map(backupId -> _wtFileStatsSvc.getWTFileStats(UUID.fromString(backupId)))
            .flatMap(Collection::stream)
            .map(WTFileStats::toJSONObject)
            .collect(Collectors.toList());

    LOG.info("/group/{}/{}/wtFileStats: {wtFileStats: {}}", pGroupId, pRsId, wtFileStats);

    return Response.ok()
        .entity(new JSONObject().put("wtFileStats", wtFileStats).toString())
        .build();
  }

  @POST
  @Path("group/{groupId}/cluster/{clusterId}/clearTailTargetHostname")
  @UiCall(auth = false)
  public Response clearClusterTailTargetHostname(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final DBObject update = new BasicDBObject();
    final DBObject toSet = new BasicDBObject();

    toSet.put("wtBackup.oplogTailTarget.hostname", "");

    update.put("$set", toSet);
    _jobDao
        .getDbCollection()
        .update(
            BasicDBObjectBuilder.start()
                .add("groupId", pGroupId)
                .add("cluster.clusterId", pClusterId)
                .get(),
            update,
            false,
            true);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/oplogTailTarget")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response oplogTailTarget(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final BackupStatus s = _jobDao.getBackupStatus(pGroupId, pRsId);
    if (s.getWtBackupStatus().getOplogTail() == null) {
      return Response.ok().entity(EMPTY_JSON_OBJECT).build();
    }

    final Map<String, Object> result = new HashMap<>();
    result.put("sessionKey", s.getWtBackupStatus().getOplogTail().getSessionKey());
    result.put("hostnamePort", s.getWtBackupStatus().getOplogTail().getHostnameAndPort());
    result.put(
        "tailOwnerSince", s.getWtBackupStatus().getOplogTail().getTailOwnerSince().getTime());
    return Response.ok(Map.of("tailOwnerInfo", List.of(result))).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/snapshots")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getSnapshotsForReplicaSet(
      @PathParam("groupId") final ObjectId pGroupId, @PathParam("rsId") final String pRsId) {
    final BackupStatus backupStatus = _jobDao.getBackupStatus(pGroupId, pRsId);
    final JSONObject json = new JSONObject();
    if (backupStatus != null) {
      final List<Snapshot> liveSnapshots = _snapshotDao.getLiveSnapshotsForReplicaSet(backupStatus);
      final Group group = getGroupSvc().findById(backupStatus.getGroupId());
      final List<JSONObject> snapshotArray =
          mapSnapshotsToJson(group, liveSnapshots).collect(Collectors.toList());
      json.put("count", snapshotArray.size());
      json.put("entries", snapshotArray);
    } else {
      json.put("count", 0);
      json.put("entries", Collections.emptyList());
    }

    return Response.ok(json.toString()).build();
  }

  @GET
  @Path("group/{groupId}/clusters/{clusterId}/snapshots")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getSnapshotsForShardedCluster(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final ClusterStatus clusterStatus = _clusterStatusDao.getClusterStatus(pClusterId);
    final List<Clustershot> clusterShots =
        _clustershotDao.getClustershotsForGroupCluster(pGroupId, pClusterId, null, 0, 10);
    final List<JSONObject> clustershotsJson =
        clusterShots.stream()
            .map(
                clustershot -> {
                  final List<Snapshot> snapshots =
                      _snapshotDao.getSnapshotsForIds(clustershot.getSnapshotIds());
                  final Group group = getGroupSvc().findById(pGroupId);
                  return mapClustershotToJson(group, clusterStatus, clustershot, snapshots);
                })
            .collect(Collectors.toList());

    final JSONObject json = new JSONObject();
    json.put("count", clustershotsJson.size());
    json.put("entries", new JSONArray(clustershotsJson));

    return Response.ok(json.toString()).build();
  }

  @GET
  @Path("group/{groupId}/cluster/{clusterId}/oplogTailTarget")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response oplogClusterTailTarget(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {

    final ClusterStatus pStatus = _clusterStatusDao.getClusterStatus(pClusterId);

    final List<Object> r =
        Stream.concat(pStatus.getShards().stream(), pStatus.getConfigServers().stream())
            .map(obj -> (String) obj.get("rsId"))
            .filter(Objects::nonNull)
            .distinct()
            .map(rsId -> _jobDao.getBackupStatus(pStatus.getGroupId(), rsId))
            .map(
                s -> {
                  if (s.getWtBackupStatus().getOplogTail() == null) {
                    return Map.of();
                  }

                  final Map<String, Object> result = new HashMap<>();
                  result.put("sessionKey", s.getWtBackupStatus().getOplogTail().getSessionKey());
                  result.put(
                      "hostnamePort", s.getWtBackupStatus().getOplogTail().getHostnameAndPort());
                  result.put(
                      "tailOwnerSince",
                      s.getWtBackupStatus().getOplogTail().getTailOwnerSince().getTime());
                  return result;
                })
            .collect(Collectors.toList());

    return Response.ok(Map.of("tailOwnerInfo", r)).build();
  }

  @POST
  @Path("group/{groupId}/cluster/{clusterId}/modifyOplogMetadata")
  @UiCall(auth = false)
  public Response modifyClusterOplogMetadata(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final DBObject update = new BasicDBObject();
    final DBObject toSet = new BasicDBObject();
    toSet.put("lastOplogPush_h", 23L);
    toSet.put("lastOplogPush_t", 100L);

    update.put("$set", toSet);
    _jobDao
        .getDbCollection()
        .update(
            BasicDBObjectBuilder.start()
                .add("groupId", pGroupId)
                .add("cluster.clusterId", pClusterId)
                .get(),
            update,
            false,
            true);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("group/{groupId}/{rsId}/modifyOplogMetadata")
  @UiCall(auth = false)
  public Response modifyOplogMetadata(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final DBObject update = new BasicDBObject();
    final DBObject toSet = new BasicDBObject();
    toSet.put("lastOplogPush_h", 23L);
    toSet.put("lastOplogPush_t", 100L);

    update.put("$set", toSet);
    _jobDao.getDbCollection().update(getJobQuery(pGroupId, pRsId), update, false, false);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("group/{groupId}/cluster/{clusterId}/modifyLastOplogPushAndDropOplogStore")
  @UiCall(auth = false)
  public Response modifyLastOplogPushAndDropOplogStore(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final DBObject update = new BasicDBObject();
    final DBObject toSet = new BasicDBObject();
    toSet.put("lastOplogPush_h", 23L);
    toSet.put("lastOplogPush_t", 100L);

    update.put("$set", toSet);
    _jobDao
        .getDbCollection()
        .update(
            BasicDBObjectBuilder.start()
                .add("groupId", pGroupId)
                .add("cluster.clusterId", pClusterId)
                .get(),
            update,
            false,
            true);

    final ClusterStatus pStatus = _clusterStatusDao.getClusterStatus(pClusterId);

    Stream.concat(pStatus.getShards().stream(), pStatus.getConfigServers().stream())
        .map(obj -> (String) obj.get("rsId"))
        .filter(Objects::nonNull)
        .distinct()
        .map(rsId -> _jobDao.getBackupStatus(pStatus.getGroupId(), rsId))
        .forEach(
            status -> {
              final OplogStore oplogStore = _backupSvc.getOplogStore(status);
              oplogStore.purgeData();
            });

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("group/{groupId}/{rsId}/modifyLastOplogPushAndDropOplogStore")
  @UiCall(auth = false)
  public Response modifyLastOplogPushAndDropOplogStore(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final DBObject update = new BasicDBObject();
    final DBObject toSet = new BasicDBObject();
    toSet.put("lastOplogPush_h", 23L);
    toSet.put("lastOplogPush_t", 100L);

    update.put("$set", toSet);
    _jobDao.getDbCollection().update(getJobQuery(pGroupId, pRsId), update, false, false);

    final BackupStatus status = _jobDao.getBackupStatus(pGroupId, pRsId);
    final OplogStore oplogStore = _backupSvc.getOplogStore(status);
    oplogStore.purgeData();

    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("group/{groupId}/cluster/{clusterId}/commonPointsNeeded")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response commonPointsNeeded(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final ClusterStatus pStatus = _clusterStatusDao.getClusterStatus(pClusterId);

    final boolean commonPointsNeeded =
        Stream.concat(pStatus.getShards().stream(), pStatus.getConfigServers().stream())
            .map(obj -> (String) obj.get("rsId"))
            .filter(Objects::nonNull)
            .distinct()
            .map(rsId -> _jobDao.getBackupStatus(pStatus.getGroupId(), rsId))
            .map(BackupStatus::commonPointsNeeded)
            .allMatch(v -> v);
    final Map<String, Boolean> result = new HashMap<>();
    result.put("commonPointsNeeded", commonPointsNeeded);
    return Response.ok(result).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/commonPointsNeeded")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response commonPointsNeeded(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final BackupStatus s = _jobDao.getBackupStatus(pGroupId, pRsId);
    final Map<String, Boolean> result = new HashMap<>();
    result.put("commonPointsNeeded", s.commonPointsNeeded());
    return Response.ok(result).build();
  }

  @GET
  @Path("group/{groupId}/cluster/{clusterId}/verifyCommonPointState")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response verifyCommonPointState(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final ClusterStatus pStatus = _clusterStatusDao.getClusterStatus(pClusterId);

    final List<Object> commonPointStates =
        Stream.concat(pStatus.getShards().stream(), pStatus.getConfigServers().stream())
            .map(obj -> (String) obj.get("rsId"))
            .filter(Objects::nonNull)
            .distinct()
            .map(rsId -> _jobDao.getJobDocument(pGroupId, rsId))
            .map(
                jobDoc -> {
                  if (jobDoc.get("rollback") == Boolean.FALSE) {
                    return Map.of();
                  }

                  final DBObject rollbackObj = (DBObject) jobDoc.get("rollback");
                  final String state = (String) rollbackObj.get("state");
                  if (state == null) {
                    return Map.of();
                  }

                  if (state.equals("common point not found")) {
                    final Map<String, Object> result = new HashMap<>();
                    result.put("commonPointNotFound", true);
                    return result;
                  }

                  final Map<String, Object> result = new HashMap<>();
                  result.put("commonPointFound", true);
                  return result;
                })
            .collect(Collectors.toList());
    return Response.ok(Map.of("commonPointStates", commonPointStates)).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/verifyCommonPointState")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response verifyCommonPointState(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final DBObject jobDoc = _jobDao.getJobDocument(pGroupId, pRsId);
    if (jobDoc.get("rollback") == Boolean.FALSE) {
      return Response.ok().entity(Map.of("commonPointStates", List.of(Map.of()))).build();
    }

    final DBObject rollbackObj = (DBObject) jobDoc.get("rollback");
    final String state = (String) rollbackObj.get("state");
    if (state == null) {
      return Response.ok().entity(Map.of("commonPointStates", List.of(Map.of()))).build();
    }

    if (state.equals("common point not found")) {
      final Map<String, Object> result = new HashMap<>();
      result.put("commonPointNotFound", true);
      return Response.ok().entity(Map.of("commonPointStates", List.of(result))).build();
    }

    final Map<String, Object> result = new HashMap<>();
    result.put("commonPointFound", true);
    return Response.ok(Map.of("commonPointStates", List.of(result))).build();
  }

  @GET
  @Path("group/{groupId}/cluster/{clusterId}/getLastOplogPushAndLatestOplogSlice")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getClusterLastOplogPushAndLatestOplogSlice(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final ClusterStatus pStatus = _clusterStatusDao.getClusterStatus(pClusterId);

    final List<Object> oplogInfo =
        Stream.concat(pStatus.getShards().stream(), pStatus.getConfigServers().stream())
            .map(obj -> (String) obj.get("rsId"))
            .filter(Objects::nonNull)
            .distinct()
            .map(rsId -> _jobDao.getBackupStatus(pStatus.getGroupId(), rsId))
            .map(
                backupStatus -> {
                  final OplogSlice latestSlice =
                      getLatestOplogSlice(pGroupId, backupStatus.getRsId());

                  final Map<String, Object> result = new HashMap<>();
                  result.put("lastOplogPush", backupStatus.getLastOplogPush());
                  result.put("lastSliceEndTime", latestSlice.getEndTimestamp());
                  result.put("isCSRS", backupStatus.isCSRS());
                  return result;
                })
            .collect(Collectors.toList());
    return Response.ok(Map.of("oplogInfo", oplogInfo)).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/getLastOplogPushAndLatestOplogSlice")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getLastOplogPushAndLatestOplogSlice(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final BackupStatus status = _jobDao.getBackupStatus(pGroupId, pRsId);
    final OplogSlice latestSlice = getLatestOplogSlice(pGroupId, pRsId);

    final Map<String, Object> result = new HashMap<>();
    result.put("lastOplogPush", status.getLastOplogPush());
    result.put("lastSliceEndTime", latestSlice.getEndTimestamp());
    return Response.ok(Map.of("oplogInfo", List.of(result))).build();
  }

  @GET
  @Path("group/{groupId}/cluster/{clusterId}/getLastOplogPushAfterRollbackJob")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getLastOplogPushAfterRollbackJob(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final ClusterStatus pStatus = _clusterStatusDao.getClusterStatus(pClusterId);

    final List<Object> oplogInfo =
        Stream.concat(pStatus.getShards().stream(), pStatus.getConfigServers().stream())
            .map(obj -> (String) obj.get("rsId"))
            .filter(Objects::nonNull)
            .distinct()
            .map(rsId -> _jobDao.getBackupStatus(pStatus.getGroupId(), rsId))
            .map(
                status -> {
                  if (status.getLastOplogPush() == null) {
                    return Map.of();
                  }

                  final Map<String, Object> result = new HashMap<>();
                  result.put("lastOplogPush", status.getLastOplogPush());
                  return result;
                })
            .collect(Collectors.toList());

    return Response.ok(Map.of("oplogPushInfo", oplogInfo)).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/getLastOplogPushAfterRollbackJob")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getLastOplogPushAfterRollbackJob(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final BackupStatus status = _jobDao.getBackupStatus(pGroupId, pRsId);

    if (status.getLastOplogPush() == null) {
      return Response.ok().entity(Map.of("oplogPushInfo", List.of(Map.of()))).build();
    }

    final Map<String, Object> result = new HashMap<>();
    result.put("lastOplogPush", status.getLastOplogPush());
    return Response.ok(Map.of("oplogPushInfo", List.of(result))).build();
  }

  @POST
  @Path("group/{groupId}/cluster/{clusterId}/scheduleOplogSliceIntegrityCheckJob")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response scheduleOplogSliceIntegrityCheckJob(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final ClusterStatus pStatus = _clusterStatusDao.getClusterStatus(pClusterId);

    Stream.concat(pStatus.getShards().stream(), pStatus.getConfigServers().stream())
        .map(obj -> (String) obj.get("rsId"))
        .filter(Objects::nonNull)
        .distinct()
        .map(rsId -> _jobDao.getBackupStatus(pStatus.getGroupId(), rsId))
        .forEach(
            status -> {
              final OplogSlice latestSlice = getLatestOplogSlice(pGroupId, status.getRsId());
              final BSONTimestamp latestSliceStartTs = latestSlice.getStartTimestamp();

              final BSONTimestamp scheduleTs =
                  new BSONTimestamp(latestSliceStartTs.getTime() - 1, latestSliceStartTs.getInc());
              LOG.info(
                  "Scheduling OplogSliceIntegrityJob from Start Time: {}, to End Time : {}",
                  status.getLastSnapshotTimestamp(),
                  scheduleTs);
              _oplogSliceIntegrityCheckJobSvc.scheduleJob(
                  status, status.getLastSnapshotTimestamp(), scheduleTs);
            });
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @POST
  @Path("group/{groupId}/{rsId}/scheduleOplogSliceIntegrityCheckJob")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response scheduleOplogSliceIntegrityCheckJob(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final BackupStatus status = _jobDao.getBackupStatus(pGroupId, pRsId);
    final OplogSlice latestSlice = getLatestOplogSlice(pGroupId, pRsId);
    final BSONTimestamp latestSliceStartTs = latestSlice.getStartTimestamp();

    final BSONTimestamp scheduleTs =
        new BSONTimestamp(latestSliceStartTs.getTime() - 1, latestSliceStartTs.getInc());
    LOG.info(
        "Scheduling OplogSliceIntegrityJob from Start Time: {}, to End Time : {}",
        status.getLastSnapshotTimestamp(),
        scheduleTs);
    _oplogSliceIntegrityCheckJobSvc.scheduleJob(
        status, status.getLastSnapshotTimestamp(), scheduleTs);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  private OplogSlice getLatestOplogSlice(final ObjectId pGroupId, final String pRsId) {
    final BackupStatus status = _jobDao.getBackupStatus(pGroupId, pRsId);
    final OplogStore oplogStore = _backupSvc.getOplogStore(status);
    final OplogSlice latestSlice =
        oplogStore.findLatestSlice(status.getGroupId(), status.getRsId());
    return latestSlice;
  }

  @GET
  @Path("group/{groupId}/cluster/{clusterId}/oplogSliceIntegrityCheckResult")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response oplogSliceIntegrityCheckResult(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final ClusterStatus pStatus = _clusterStatusDao.getClusterStatus(pClusterId);

    final List<Object> integrityCheckInfo =
        Stream.concat(pStatus.getShards().stream(), pStatus.getConfigServers().stream())
            .map(obj -> (String) obj.get("rsId"))
            .filter(Objects::nonNull)
            .distinct()
            .map(rsId -> _jobDao.getBackupStatus(pStatus.getGroupId(), rsId))
            .map(
                backupJob -> {
                  final String rsId = backupJob.getRsId();
                  final List<DBObject> allSliceIntegrityJobs =
                      _oplogSliceIntegrityCheckJobDao.getAllJobs(getJobQuery(pGroupId, rsId));
                  if (allSliceIntegrityJobs.size() == 0) {
                    return Map.of();
                  }
                  final DBObject integrityJob = allSliceIntegrityJobs.get(0);
                  final String validationResult = (String) integrityJob.get("validationResult");
                  if (validationResult == null) {
                    return Map.of();
                  }
                  final Map<String, Object> result = new HashMap<>();
                  result.put("result", validationResult);
                  result.put("isCSRS", backupJob.isCSRS());
                  return result;
                })
            .collect(Collectors.toList());

    return Response.ok(Map.of("integrityCheckInfo", integrityCheckInfo)).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/oplogSliceIntegrityCheckResult")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response oplogSliceIntegrityCheckResult(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final List<DBObject> allSliceIntegrityJobs =
        _oplogSliceIntegrityCheckJobDao.getAllJobs(getJobQuery(pGroupId, pRsId));
    if (allSliceIntegrityJobs.size() == 0) {
      return Response.ok().entity(Map.of("integrityCheckInfo", List.of(Map.of()))).build();
    }

    final DBObject integrityJob = allSliceIntegrityJobs.get(0);
    final String validationResult = (String) integrityJob.get("validationResult");
    if (validationResult == null) {
      return Response.ok().entity(Map.of("integrityCheckInfo", List.of(Map.of()))).build();
    }

    final Map<String, String> result = new HashMap<>();
    result.put("result", validationResult);
    return Response.ok(Map.of("integrityCheckInfo", List.of(result))).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/wtcJustRestored")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getWTCJustRestoredJobDocument(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final DBObject jobDocument = _jobDao.getJobDocument(pGroupId, pRsId);
    final Map<String, Object> result = new HashMap<>();
    result.put("lastOplogPushIsNull", jobDocument.get("lastOplogPush") == null);

    final DBObject wtBackup = (DBObject) jobDocument.get("wtBackup");

    LOG.info("wtBackup object: {}", wtBackup);

    result.put("oplogTailTargetIsNull", wtBackup.get("oplogTailTarget") == null);
    result.put("checkpointingTargetIsNull", wtBackup.get("checkpointingTarget") == null);

    if (wtBackup.containsField("wtcJustRestored")) {
      if (wtBackup.get("wtcJustRestored").equals("rescheduleSnapshot")) {
        result.put("wtcJustRestored", "rescheduleSnapshot");
      } else if (wtBackup.get("wtcJustRestored").equals("skipValidations")) {
        result.put("wtcJustRestored", "skipValidations");
      }
    }

    return Response.ok(result).build();
  }

  @GET
  @Path(
      "group/{groupId}/verifyParallelRestore/{validationType}/{blockStoreType}/{parallelFeatureFlagOnOrOff}/{directS3RestoreFlagOnOrOff}")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  @TestUtility
  public Response verifyParallelRestore(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("validationType") final String pValidationType,
      @PathParam("blockStoreType") final String pBlockStoreType,
      @PathParam("parallelFeatureFlagOnOrOff") final String pParallelFeatureFlagOnOrOff,
      @PathParam("directS3RestoreFlagOnOrOff") final String pDirectS3RestoreFlagOnOrOff)
      throws SvcException {
    final List<DBObject> restoreJobDocs = _restoreJobDao.getLatestRestoreJobs(pGroupId, 999);
    final boolean isParallelRestoreEnabled = pParallelFeatureFlagOnOrOff.equals("on");
    final boolean isDirectS3RestoreFeatureEnabled = pDirectS3RestoreFlagOnOrOff.equals("on");
    final boolean isS3BlockStore = SnapshotStoreType.s3blockstore.name().equals(pBlockStoreType);
    final boolean res;
    if (isParallelRestoreEnabled || (isDirectS3RestoreFeatureEnabled && isS3BlockStore)) {
      res =
          restoreJobDocs.stream()
              .map(
                  (restoreJob) -> {
                    LOG.info("Verifying restore job: {}", restoreJob);
                    return isDirectS3RestoreFeatureEnabled && isS3BlockStore
                        ? verifyDirectS3Restore(restoreJob, pValidationType) == null
                        : verifyParallelRestoreState(restoreJob, pValidationType);
                  })
              .reduce((a, b) -> a && b)
              .get();
    } else {
      res =
          restoreJobDocs.stream()
              .map(this::verifySerialRestoreState)
              .reduce((a, b) -> a && b)
              .get();
    }
    return Response.ok(Map.of("success", res)).build();
  }

  @GET
  @Path("verifyParallelRestore/{validationType}/restoreJob/{restoreJobId}")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  @TestUtility
  public Response verifyParallelRestore(
      @PathParam("validationType") final String pValidationType,
      @PathParam("restoreJobId") final ObjectId pRestoreJobId) {

    final DBObject restoreJob = _restoreJobDao.getRestoreJobById(pRestoreJobId);
    final boolean res = verifyParallelRestoreState(restoreJob, pValidationType);
    return Response.ok(Map.of("success", res)).build();
  }

  @GET
  @Path("verifyDirectS3Restore/{isEnabled}/restoreJob/{restoreJobId}")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  @TestUtility
  public Response verifyDirectS3Restore(
      @PathParam("isEnabled") final Boolean pIsEnabled,
      @PathParam("restoreJobId") final ObjectId pRestoreJobId)
      throws SvcException {

    final DBObject restoreJob = _restoreJobDao.getRestoreJobById(pRestoreJobId);
    final String errMsg;
    if (!pIsEnabled) {
      boolean directS3RestoreEnabled =
          restoreJob.containsField(BACKUP_DIRECT_S3_RESTORE)
              && !Boolean.parseBoolean(restoreJob.get(BACKUP_DIRECT_S3_RESTORE).toString());
      return Response.ok(Map.of("success", directS3RestoreEnabled)).build();
    } else {
      errMsg = verifyDirectS3Restore(restoreJob, "completed");
    }
    if (errMsg == null) {
      return Response.ok(Map.of("success", true)).build();
    } else {
      LOG.error(
          "Direct S3 restore job with id {} is invalid: {}", pRestoreJobId.toHexString(), errMsg);
      throw new SvcException(CommonErrorCode.VALIDATION_ERROR, errMsg);
    }
  }

  private String verifyDirectS3Restore(final DBObject restoreJob, final String pValidationType) {
    final ObjectId restoreId = (ObjectId) restoreJob.get("_id");
    LOG.info("Verifying the direct S3 restore state for job {}", restoreId.toHexString());
    final RestoreStatus restoreStatus = new RestoreStatus(restoreJob);
    String err;
    if (!restoreStatus.isDirectS3Restore()) {
      err =
          String.format(
              "Direct S3 restore job with id %s is not a direct S3 restore job.",
              restoreId.toHexString());
      LOG.error(err);
      return err;
    }
    // We only check for DONE state for directS3 restore.
    if ("completed".equals(pValidationType)
        && !restoreStatus
            .getAutomationProgress()
            .equals(AutomationRestoreProgress.DONE.toString())) {
      err =
          String.format(
              "Direct S3 restore job with id %s is not in %s state, current state is %s.",
              restoreId.toHexString(), pValidationType, restoreStatus.getAutomationProgress());
      LOG.error(err);
      return err;
    }
    if (restoreStatus.getParallelRestoreURL() != null) {
      err =
          String.format(
              "Direct S3 restore job with id %s finished but the restore status still contain"
                  + " parallelRestoreURL.",
              restoreId.toHexString());
      LOG.error(err);
      return err;
    }
    if (restoreStatus.getNumParallelRestoreWorkers() != null) {
      err =
          String.format(
              "Direct S3 restore job with id %s finished but the restore status still contains"
                  + " numParallelRestoreWorkers.",
              restoreId.toHexString());
      LOG.error(err);
      return err;
    }
    if (restoreStatus.getNumParallelChunks() != null) {
      err =
          String.format(
              "Direct S3 restore job with id %s finished but the restore status still contains"
                  + " numParallelChunks.",
              restoreId.toHexString());
      LOG.error(err);
      return err;
    }
    if (restoreStatus.getHashes() != null && !restoreStatus.getHashes().isEmpty()) {
      err =
          String.format(
              "Direct S3 restore job with id %s finished but the restore status contains"
                  + " file hashes which indicates that it was a pull v2 restore.",
              restoreId.toHexString());
      LOG.error(err);
      return err;
    }
    return null;
  }

  private boolean verifyParallelRestoreState(
      final DBObject restoreJob, final String validationType) {
    final ObjectId restoreId = (ObjectId) restoreJob.get("_id");
    LOG.info("Verifying the {} parallel restore state for job {}", validationType, restoreId);

    final RestoreStatus restoreStatus = new RestoreStatus(restoreJob);
    if (restoreStatus.isFinished()) {
      LOG.info(
          "Restore job with id {} finished before chunk existence can be verified.",
          restoreStatus.getId());
      if (restoreStatus.getParallelRestoreURL() == null) {
        LOG.error(
            "Parallel restore job with id {} finished but the restore status does not contain"
                + " parallelRestoreURL.",
            restoreId);
        return false;
      }
      if (restoreStatus.getNumParallelRestoreWorkers() == null) {
        LOG.error(
            "Parallel restore job with id {} finished but the restore status does not contain"
                + " numParallelRestoreWorkers.",
            restoreId);
        return false;
      }
      if (restoreStatus.getNumParallelChunks() == null) {
        LOG.error(
            "Parallel restore job with id {} finished but the restore status does not contain"
                + " numParallelChunks.",
            restoreId);
        return false;
      }

      return true;
    }

    final boolean hasParallelRestoreURL = restoreJob.get("parallelRestoreURL") != null;
    boolean chunkExists;
    try {
      chunkExists = _parallelRestoreChunkDao.getParallelRestoreChunk(restoreId, 0) != null;
    } catch (final BackupException e) {
      // we don't expect the chunk to exist for completed or cancelled job
      if (validationType.equals("setup")) {
        LOG.error(
            "Failed to get parallel restore chunks for restoreJob with id {} and validation type"
                + " {}",
            restoreId,
            validationType,
            e);
      }
      chunkExists = false;
    }

    if (validationType.equals("setup")) {
      LOG.info(
          "verifyParallelRestoreState: restoreId={} hasParallelRestoreURL={} chunkExists={}",
          restoreId,
          hasParallelRestoreURL,
          chunkExists);
      return hasParallelRestoreURL && chunkExists;
    } else if (validationType.equals("completed")) {
      final DBObject progressStats = (DBObject) restoreJob.get("progress");
      final DBObject transferStats = (DBObject) progressStats.get("transfer");
      final boolean hasNumChunksRestoredInProgressTracking =
          transferStats.get("numChunksRestored") != null;

      return hasParallelRestoreURL && hasNumChunksRestoredInProgressTracking && !chunkExists;
    } else if (validationType.equals("cancelled")) {
      return hasParallelRestoreURL && !chunkExists;
    }

    throw new IllegalArgumentException(
        "Expected query param validationType to be setup, completed, or cancelled");
  }

  private boolean verifySerialRestoreState(final DBObject restoreJob) {
    final ObjectId restoreId = (ObjectId) restoreJob.get("_id");

    final boolean doesNotHaveParallelRestoreURL = restoreJob.get("parallelRestoreURL") == null;
    boolean chunkExists;
    try {
      chunkExists = _parallelRestoreChunkDao.getParallelRestoreChunk(restoreId, 0) != null;
    } catch (final BackupException e) {
      chunkExists = false;
    }

    return doesNotHaveParallelRestoreURL && !chunkExists;
  }

  @GET
  @Path("group/{groupId}/backupGroupConfig")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getBackupGroupConfig(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    _backupDeploymentSvc.ensureBackupGroupConfig(pGroupId);
    final BackupGroupConfig groupConfig = _backupDeploymentSvc.getBackupGroupConfig(pGroupId);
    if (groupConfig == null) {
      return Response.status(Status.NOT_FOUND).build();
    }

    return Response.ok(groupConfig.toJSONObject().toMap()).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/wtcBackupStatus")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getWTCBackupStatus(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final BackupStatus backupStatus = _jobDao.getBackupStatus(pGroupId, pRsId);

    final Optional<DayOfWeek> dayOfWeek;
    if (backupStatus.isClusterMember()) {
      final ClusterStatus clusterStatus =
          _clusterStatusDao.getClusterStatus(backupStatus.getClusterId());
      dayOfWeek = clusterStatus.getFullIncrementalDayOfWeek();
    } else {
      dayOfWeek = backupStatus.getWtBackupStatus().getFullIncrementalDayOfWeek();
    }

    final Map<String, Object> result = new HashMap<>();
    if (dayOfWeek.isPresent()) {
      LOG.info("WTC forced full incremental: {fullIncrementalDayOfWeek: {})", dayOfWeek.get());
      result.put("fullIncrementalDayOfWeek", dayOfWeek.get());
    } else {
      LOG.info("WTC forced full incremental: no fullIncrementalDayOfWeek found");
    }

    if (backupStatus.getWtBackupStatus().getLastFullIncrementalTs().isPresent()) {
      final Instant lastFullIncrementalTs =
          backupStatus.getWtBackupStatus().getLastFullIncrementalTs().get();
      LOG.info("WTC forced full incremental: {lastFullIncrementalTs: {})", lastFullIncrementalTs);
      result.put("lastFullIncrementalTs", lastFullIncrementalTs.toString());
    }

    return Response.ok(result).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/minBlockSize")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getBackupStatusMinBlockSize(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final BackupStatus backupStatus = _jobDao.getBackupStatus(pGroupId, pRsId);
    final int blockSize = backupStatus.getBlockstoreMinBlockSize();
    final Map<String, Object> result = new HashMap<>();
    result.put("minBlockSize", blockSize);
    return Response.ok(result).build();
  }

  @GET
  @Path("group/{groupId}/{rsId}/queryableMountStatus")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getQueryableMountStatus(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("rsId") final String pRsId) {
    final boolean isMounted =
        _restoreJobDao.getLatestRestoreJobs(pGroupId, 10).stream()
            .map(RestoreStatus::new)
            .filter(RestoreStatus::isQueryable)
            .filter(restoreStatus -> pRsId.equals(restoreStatus.getRsId()))
            .filter(restoreStatus -> restoreStatus.getMountInfo() != null)
            .findFirst()
            .isPresent();

    return Response.ok(Map.of("queryableMounted", isMounted)).build();
  }

  @GET
  @Path("group/{groupId}/cluster/{clusterId}/queryableMountStatus")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getQueryableMountStatus(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterId") final ObjectId pClusterId) {
    final boolean isMounted =
        _restoreJobDao.getLatestRestoreJobs(pGroupId, 10).stream()
            .map(RestoreStatus::new)
            .filter(RestoreStatus::isQueryable)
            .filter(restoreStatus -> pClusterId.equals(restoreStatus.getClusterId()))
            .filter(RestoreStatus::isQueryableMongoS)
            .filter(restoreStatus -> restoreStatus.getMountInfo() != null)
            .findFirst()
            .isPresent();

    return Response.ok(Map.of("queryableMounted", isMounted)).build();
  }

  @GET
  @Path("/auth/groups/{groupId}/clusters/{clusterName}/independentShardScalingMode")
  @Produces({MediaType.TEXT_PLAIN})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY})
  @WithPublicApiAuth
  public Response getIndependentShardScalingMode(
      @Context final AppUser appUser,
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final Optional<ClusterDescription> mergedClusterDescription =
        _ndsClusterSvc.getMergedClusterDescription(pGroupId, pClusterName);
    if (mergedClusterDescription.isEmpty()) {
      return Response.status(HttpStatus.SC_NOT_FOUND).build();
    }

    return Response.ok(mergedClusterDescription.get().getAutoScalingMode().name()).build();
  }

  @GET
  @Path("group/{groupId}/saveHeadAndQueryableLogs")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getSaveHeadAndQueryableLogs(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    // We assume there is only one daemon in the tests
    final List<DaemonMachineConfig> allDaemonMachineConfigs = _daemonMachineConfigDao.getAll();
    final DaemonMachineConfig daemonMachineConfig = allDaemonMachineConfigs.get(0);
    final String daemonRootDirectory =
        daemonMachineConfig.getDaemonMachine().getHeadRootDirectory();

    copyMongodLogs(daemonRootDirectory, pGroupId.toString(), "head", pGroupId.toString());
    copyMongodLogs(daemonRootDirectory, "queryable", "queryable", pGroupId.toString());

    return Response.ok().build();
  }

  private void copyMongodLogs(
      final String pDaemonRootDirectory,
      final String pSubDirectory,
      final String pHeadOrQueryable,
      final String pGroupId) {
    final String logsDirectory = pDaemonRootDirectory + "/" + pSubDirectory;
    final File logsDirectoryFile = new File(logsDirectory);
    try {
      if (logsDirectoryFile.exists() && logsDirectoryFile.isDirectory()) {
        for (final File rsDirectory : logsDirectoryFile.listFiles()) {
          if (rsDirectory.exists() && rsDirectory.isDirectory()) {
            for (final String mongodOrMongs : List.of("mongod", "mongos")) {
              final String logFile = rsDirectory.getAbsolutePath() + "/" + mongodOrMongs + ".log";
              if (new File(logFile).exists()) {
                final String renamedLogFile =
                    "server/"
                        + pHeadOrQueryable
                        + "_"
                        + pGroupId
                        + "_"
                        + rsDirectory.getName()
                        + "_"
                        + mongodOrMongs
                        + ".log";

                Files.copy(
                    Paths.get(logFile),
                    Paths.get(renamedLogFile),
                    StandardCopyOption.REPLACE_EXISTING,
                    StandardCopyOption.COPY_ATTRIBUTES);
              }
            }
          }
        }
      }
    } catch (final Exception e) {
      LOG.error("Failed to copy {} log file(s) for group {}", pHeadOrQueryable, pGroupId, e);
    }
  }

  @GET
  @Path("/namer")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getInfo(
      @Context final HttpServletRequest request, @Context final HttpServletResponse response)
      throws JSONException {
    final PackageNamer.AutomationAgentPackageNamer namer =
        new PackageNamer.AutomationAgentPackageNamer(
            _appSettings.getAutomationAgentVersion().toString(),
            false,
            false); // isAtlas:false so that we don't get non-public urls
    return Response.ok(namer.toJSON().toString()).build();
  }

  @GET
  @Path("group/{groupId}/tenant/cluster/{clusterName}/m0ClusterActivity")
  @UiCall(auth = false)
  public Response getM0ClusterActivity(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final JSONObject response = new JSONObject();
    _ndsM0ClusterActivityDao
        .findUncachedClusterActivity(pClusterName, pGroupId)
        .map(NDSM0ClusterActivity::getLastActive)
        .map(Date::getTime)
        .ifPresent(a -> response.put("lastActive", a));

    return Response.ok(response.toString()).build();
  }

  @PATCH
  @Path("group/{groupId}/encryptionAtRest")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response patchEncryptionAtRestNoValidation(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      final NDSEncryptionAtRestView pEncryptionAtRestView)
      throws SvcException {
    final NDSGroup ndsGroup =
        _ndsGroupDao
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    final NDSEncryptionAtRest existingEncryptionAtRest = ndsGroup.getEncryptionAtRest();
    final NDSEncryptionAtRest updatedEncryptionAtRest =
        pEncryptionAtRestView
            .toUpdatedEncryptionAtRestView(existingEncryptionAtRest)
            .toNDSEncryptionAtRest();

    final NDSEncryptionAtRest.NDSEncryptionAtRestBuilder updatedAndMergedEncryptionAtRestBuilder =
        updatedEncryptionAtRest.copy();

    // retain existing fields
    final KeyManagementConfig existingAwsKms = existingEncryptionAtRest.getAWSKMS();
    final var awsKmsBuilder = updatedEncryptionAtRest.getAWSKMS().copy();
    existingAwsKms.getLastUpdatedKeyID().ifPresent(awsKmsBuilder::setLastUpdatedKeyID);
    existingAwsKms
        .getLastKmipMasterKeyRotation()
        .ifPresent(awsKmsBuilder::setLastKmipMasterKeyRotation);
    awsKmsBuilder.setValid(existingAwsKms.isValid());
    updatedAndMergedEncryptionAtRestBuilder.awsKms(awsKmsBuilder.build());

    final KeyManagementConfig existingAzureKeyVault = existingEncryptionAtRest.getAzureKeyVault();
    final var azureKeyVaultBuilder = updatedEncryptionAtRest.getAzureKeyVault().copy();
    existingAzureKeyVault
        .getLastUpdatedKeyID()
        .ifPresent(azureKeyVaultBuilder::setLastUpdatedKeyID);
    existingAzureKeyVault
        .getLastKmipMasterKeyRotation()
        .ifPresent(azureKeyVaultBuilder::setLastKmipMasterKeyRotation);
    azureKeyVaultBuilder.setValid(existingAzureKeyVault.isValid());
    updatedAndMergedEncryptionAtRestBuilder.azureKeyVault(azureKeyVaultBuilder.build());

    final KeyManagementConfig existingGoogleCloudKMS = existingEncryptionAtRest.getGoogleCloudKMS();
    final var googleCloudKMSBuilder = updatedEncryptionAtRest.getGoogleCloudKMS().copy();
    existingGoogleCloudKMS
        .getLastUpdatedKeyID()
        .ifPresent(googleCloudKMSBuilder::setLastUpdatedKeyID);
    existingGoogleCloudKMS
        .getLastKmipMasterKeyRotation()
        .ifPresent(googleCloudKMSBuilder::setLastKmipMasterKeyRotation);
    googleCloudKMSBuilder.setValid(existingGoogleCloudKMS.isValid());
    updatedAndMergedEncryptionAtRestBuilder.googleCloudKMS(googleCloudKMSBuilder.build());

    final NDSEncryptionAtRest updatedAndMergedEncryptionAtRest =
        updatedAndMergedEncryptionAtRestBuilder.build();
    _ndsGroupDao.updateEncryptionAtRest(pGroupId, updatedAndMergedEncryptionAtRest);

    return Response.ok()
        .entity(new NDSEncryptionAtRestView(updatedAndMergedEncryptionAtRest))
        .build();
  }

  @POST
  @Path("group/{groupId}/encryptionAtRest/{cloudProvider}/approvePrivateEndpoints")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response approveEncryptionAtRestPrivateEndpoints(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider)
      throws SvcException {
    final NDSGroup ndsGroup =
        _ndsGroupDao
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));

    final NDSEncryptionAtRest encryptionAtRest = ndsGroup.getEncryptionAtRest();
    if (pCloudProvider != CloudProvider.AZURE) {
      throw new IllegalArgumentException(
          String.format("Unsupported cloud provider: %s", pCloudProvider));
    }
    final NDSAzureKeyVault keyVault = (NDSAzureKeyVault) encryptionAtRest.getAzureKeyVault();

    final List<? extends CloudProviderPrivateEndpoint> privateEndpoints =
        _ndsGroupSvc.getEARPrivateEndpoints(pGroupId, pCloudProvider);
    privateEndpoints.forEach(
        endpoint -> {
          _azureApiSvc.approveKeyVaultPrivateEndpointForE2ETest(
              keyVault.getClientID().orElseThrow(),
              keyVault.getTenantID().orElseThrow(),
              keyVault.getSecret().orElseThrow(),
              AzureEnvironment.AZURE,
              keyVault.getResourceGroupName().orElseThrow(),
              keyVault.getSubscriptionID().orElseThrow(),
              keyVault.getKeyVaultName().orElseThrow(),
              endpoint.getPrivateEndpointConnectionName(),
              LOG);
        });
    return Response.ok().build();
  }

  @DELETE
  @Path("/groups/{groupId}/encryptionAtRest/{cloudProvider}/privateEndpoints")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.PATH)
  public Response requestPrivateEndpointsDeletionForGroupNoValidation(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider)
      throws SvcException {
    _ndsGroupSvc.requestEARPrivateEndpointsDeletionForGroup(pGroupId, pCloudProvider);
    return Response.ok().build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/groups/{groupId}/encryptionAtRest/{cloudProvider}/privateEndpoints")
  @UiCall(roles = RoleSet.GLOBAL_ATLAS_ADMIN, groupSource = GroupSource.PATH)
  public Response getNumPrivateEndpointsForGroup(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("cloudProvider") final CloudProvider pCloudProvider)
      throws SvcException {
    final BasicDBObject response = new BasicDBObject();
    final List<? extends CloudProviderPrivateEndpoint> privateEndpoints =
        _ndsGroupSvc.getEARPrivateEndpoints(pGroupId, pCloudProvider);
    response.put("totalCount", privateEndpoints.size());
    return Response.ok(response).build();
  }

  // Returns { "hasOplogSlice": true } if all shards within the given cluster has at least one valid
  // oplog slice in the oplog store.
  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/backup/{groupId}/{clusterName}/hasOplogSlice")
  @UiCall(auth = false)
  public Response hasOplogSliceForCPS(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final BasicDBObject response = new BasicDBObject();
    final Optional<BackupJob> backupJobOpt = _backupJobDao.find(pGroupId, pClusterName);
    if (backupJobOpt.isEmpty()) {
      LOG.info("No backup job, so no slice");
      response.put("hasOplogSlice", false);
      return Response.ok(response).build();
    }
    final BackupJob backupJob = backupJobOpt.get();
    if (backupJob.getAllRsIds().isEmpty()) {
      LOG.info("backup job has no rsIds, so no slice");
      response.put("hasOplogSlice", false);
      return Response.ok(response).build();
    }
    for (final String rsId : backupJob.getAllRsIds()) {
      final CpsOplogSliceMetadataDaoProxy oplogSliceMetadataDao =
          _cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(backupJobOpt.get(), rsId);
      final CpsOplogSlice cpsOplogSlice =
          oplogSliceMetadataDao.findEarliestSlice(
              pGroupId,
              backupJob.getClusterUniqueId(),
              rsId,
              backupJob.getBlobStoreRegionByRsId(rsId));
      if (cpsOplogSlice == null) {
        LOG.info("No valid slice yet for rsId={}", rsId);
        response.put("hasOplogSlice", false);
        return Response.ok(response).build();
      }
      final PitSetting pitSetting = backupJob.getPitSettingByRsId(rsId);
      if (pitSetting.getMigrationStorage() != null) {
        LOG.info("The backup job for rsId={} contains the  migration storage", rsId);
        final CpsOplogSliceMetadataDaoProxy migratingOplogSliceMetadataDao =
            _cpsOplogStoreFactory.getCpsOplogMigrationStorageRegionSliceDaoProxy(
                backupJobOpt.get(), rsId);
        final CpsOplogSlice migratingCpsSlice =
            migratingOplogSliceMetadataDao.findEarliestSlice(
                pGroupId,
                backupJob.getClusterUniqueId(),
                rsId,
                backupJob.getBlobStoreRegionByRsId(rsId));
        if (migratingCpsSlice == null) {
          LOG.info("The oplogs for rsId={} is in migration but we were unable to find them", rsId);
          response.put("hasOplogSlice", false);
          return Response.ok(response).build();
        }
      }
      for (final PitStorage copyStorageMigration : pitSetting.getCopyStoragesMigration()) {
        final CpsOplogSliceMetadataDaoProxy copyStoragesMigrationDao =
            _cpsOplogStoreFactory.getCpsSliceDaoProxy(
                backupJob,
                backupJob.getProjectId(),
                copyStorageMigration.getMetadataStoreConfigId());
        final CpsOplogSlice copyMigrationSlice =
            copyStoragesMigrationDao.findEarliestSlice(
                pGroupId,
                backupJob.getClusterUniqueId(),
                rsId,
                copyStorageMigration.getBlobStoreRegionName());
        if (copyMigrationSlice == null) {
          LOG.info(
              "The oplogs for rsId={} have a copy storage migration configured but we were unable"
                  + " to find them",
              rsId);
          response.put("hasOplogSlice", false);
          return Response.ok(response).build();
        }
      }
      for (final PitStorage copyStorage : pitSetting.getCopyStorages()) {
        final CpsOplogSliceMetadataDaoProxy copyStoragesDao =
            _cpsOplogStoreFactory.getCpsSliceDaoProxy(
                backupJob, backupJob.getProjectId(), copyStorage.getMetadataStoreConfigId());
        final CpsOplogSlice copySlice =
            copyStoragesDao.findEarliestSlice(
                pGroupId,
                backupJob.getClusterUniqueId(),
                rsId,
                copyStorage.getBlobStoreRegionName());
        if (copySlice == null) {
          LOG.info(
              "The oplogs for rsId={} have a copy storage configured but we were unable to find"
                  + " them",
              rsId);
          response.put("hasOplogSlice", false);
          return Response.ok(response).build();
        }
      }
    }

    response.put("hasOplogSlice", true);
    return Response.ok(response).build();
  }

  // Instead of using com.xgen.svc.nds.res.CpsResource#updateBackupPolicy we are creating
  // this endpoint because updateBackupPolicy does not support updating pit window to non-integer
  // values.
  @POST
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/backup/{groupId}/{clusterName}/job/updatePitWindow/{minutes}")
  @UiCall(auth = false)
  public Response updateBackupJobPitWindow(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("minutes") final double pPitWindowInMins)
      throws Exception {
    final BasicDBObject response = new BasicDBObject();
    final Optional<BackupJob> backupJobOpt = _backupJobDao.find(pGroupId, pClusterName);
    if (backupJobOpt.isEmpty()) {
      throw new Exception("No backup job for cluster " + pClusterName);
    }
    final BackupJob backupJob = backupJobOpt.get();
    final double pitWindowInDays = pPitWindowInMins / 60 / 24;

    _backupJobDao.updatePitWindow(pGroupId, backupJob.getClusterUniqueId(), pitWindowInDays);
    return Response.ok(response).build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/backup/{groupId}/{clusterName}/hasOplogMigration")
  @UiCall(auth = false)
  public Response hasOplogMigrationForCps(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName)
      throws Exception {
    final BasicDBObject response = new BasicDBObject();
    final Optional<BackupJob> backupJobOpt = _backupJobDao.find(pGroupId, pClusterName);

    if (backupJobOpt.isEmpty()) {
      LOG.info("No backup job, so no oplog migration or copy oplog migration");
      throw new Exception("No backup job for cluster " + pClusterName);
    }
    final BackupJob backupJob = backupJobOpt.get();
    boolean hasOplogMigration = false;
    boolean hasCopyOplogMigration = false;
    for (final String rsId : backupJob.getAllRsIds()) {
      final PitSetting pitSetting = backupJob.getPitSettingByRsId(rsId);
      if (pitSetting.getOplogMigration() != null) {
        hasOplogMigration = true;
      }
      if (!pitSetting.getCopyStoragesMigration().isEmpty()) {
        hasCopyOplogMigration = true;
      }
    }
    response.put("hasOplogMigration", hasOplogMigration);
    response.put("hasCopyOplogMigration", hasCopyOplogMigration);
    return Response.ok(response).build();
  }

  @DELETE
  @Path("/{groupId}/snapshot/")
  @UiCall(auth = false)
  public Response deleteSnapshotsForProject(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    final BasicDBObject response = new BasicDBObject();
    LOG.info("Deleting all snapshots for project {}", pGroupId);
    _backupSnapshotDao.markDeletedForProject(pGroupId);
    return Response.ok(response).build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/backup/{groupId}/{clusterName}/oplogCoversPitWindow")
  @UiCall(auth = false)
  public Response oplogCoversPitWindow(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final BasicDBObject response = new BasicDBObject();
    response.put("oplogCoversPitWindow", false);
    final Optional<BackupJob> backupJobOpt = _backupJobDao.find(pGroupId, pClusterName);

    if (backupJobOpt.isEmpty()) {
      LOG.info("No backup job, so no oplogs");
      return Response.ok(response).build();
    }

    final BackupJob backupJob = backupJobOpt.get();
    final Date threeMinsAgo = DateUtils.addMinutes(new Date(), -3);
    for (final String rsId : backupJob.getAllRsIds()) {
      final CpsOplogSliceMetadataDaoProxy oplogSliceMetadataDao =
          _cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(backupJobOpt.get(), rsId);

      final CpsOplogSlice latestOplogSlice =
          oplogSliceMetadataDao.findLatestSliceByCreateTime(
              rsId, backupJob.getBlobStoreRegionByRsId(rsId));
      final Date latestOplogSliceEndTimestampInDate =
          new Date(latestOplogSlice.getEndTimestamp().getTime() * 1000L);

      if (latestOplogSliceEndTimestampInDate.before(threeMinsAgo)) {
        LOG.info("The latest oplog slice end date is from less than 3 mins ago");
        return Response.ok(response).build();
      }

      final CpsOplogSlice earliestOplogSlice =
          oplogSliceMetadataDao.findEarliestSlice(
              pGroupId,
              backupJob.getClusterUniqueId(),
              rsId,
              backupJob.getBlobStoreRegionByRsId(rsId));

      if (TimeUnit.MILLISECONDS.toMinutes(
              latestOplogSlice.getEndTimestamp().getTime()
                  - earliestOplogSlice.getStartTimestamp().getTime())
          < (backupJob.getRestoreWindowInMins())) {
        LOG.info(
            "The lastest oplog slice end timestamp minus the earliest oplog slice start time is"
                + " less that the pit window");
        return Response.ok(response).build();
      }
    }
    LOG.info("The collected oplogs cover the pit window");
    response.put("oplogCoversPitWindow", true);
    return Response.ok(response).build();
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @Path("/nds/backup/{groupId}/{clusterName}/oplogSlicesEncrypted")
  @UiCall(auth = false)
  public Response oplogSlicesEncrypted(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final BasicDBObject response = new BasicDBObject();
    final Optional<BackupJob> backupJobOpt = _backupJobDao.find(pGroupId, pClusterName);
    if (backupJobOpt.isEmpty()) {
      LOG.info("No backup job, so no slice");
      response.put("oplogSlicesEncrypted", false);
      return Response.ok(response).build();
    }
    final BackupJob backupJob = backupJobOpt.get();
    if (backupJob.getAllRsIds().isEmpty()) {
      LOG.info("backup job has no rsIds, so no slice");
      response.put("oplogSlicesEncrypted", false);
      return Response.ok(response).build();
    }

    boolean allEncrypted = true;
    for (final String rsId : backupJob.getAllRsIds()) {
      final CpsOplogSliceMetadataDaoProxy oplogSliceMetadataDao =
          _cpsOplogStoreFactory.getOriginalPrimaryRegionSliceDaoProxy(backupJobOpt.get(), rsId);

      final CpsOplogSlice cpsOplogSlice =
          oplogSliceMetadataDao.findLatestSliceByCreateTime(
              rsId, backupJob.getBlobStoreRegionByRsId(rsId));
      if (cpsOplogSlice == null) {
        LOG.info("No valid slice yet for rsId={}", rsId);
        allEncrypted = false;
        continue;
      }

      final CpsBlobStoreConfig cpsBlobStoreConfig =
          _cpsBlobStoreConfigDao.findAssignableByProviderRegion(
              cpsOplogSlice.getCloudProvider(), cpsOplogSlice.getStorageRegionName().getValue());

      LOG.info(
          "oplogSlicesEncrypted found cpsBlobStoreConfig for group={} clusterName={}"
              + " CloudProvider={}, cpsOplogSlice.getStorageCloudProvider={}, "
              + "StorageRegionName={} getStorageRegionName().getProvider()={}"
              + " cpsBlobStoreConfig={}",
          pGroupId,
          pClusterName,
          cpsOplogSlice.getCloudProvider(),
          cpsOplogSlice.getStorageCloudProvider(),
          cpsOplogSlice.getStorageRegionName().getValue(),
          cpsOplogSlice.getStorageRegionName().getProvider(),
          cpsBlobStoreConfig);

      final CloudProvider oplogStorageProvider = cpsOplogSlice.getStorageCloudProvider();

      switch (oplogStorageProvider) {
        case AWS:
          if (StringUtils.isNotEmpty(cpsOplogSlice.getKeyCipherText())) {
            continue;
          }
          //          if (isOplogEncryptedOnAws(cpsBlobStoreConfig, cpsOplogSlice)) continue;
          break;
        case GCP:
          if (isOplogEncryptedOnGcp(cpsBlobStoreConfig, cpsOplogSlice)) {
            continue;
          }
          break;
        case AZURE:
          if (isOplogEncryptedOnAzure(cpsBlobStoreConfig, cpsOplogSlice)) {
            continue;
          }
          break;
        default:
          throw new IllegalArgumentException("Unsupported Cloud Provider: " + oplogStorageProvider);
      }

      LOG.debug("Slice of {} is not yet encrypted on {}", rsId, oplogStorageProvider);
      allEncrypted = false;
    }

    response.put("oplogSlicesEncrypted", allEncrypted);
    return Response.ok(response).build();
  }

  private boolean isOplogEncryptedOnGcp(
      final CpsBlobStoreConfig pCpsBlobStoreConfig, final CpsOplogSlice pCpsOplogSlice) {
    final GCPOrganization gcpOrganization =
        _gcpOrganizationSvc
            .findContainerAssignmentAccount()
            .orElseThrow(IllegalStateException::new);
    final Blob storageBlob =
        _gcpApiSvc.getBlobObjectFromCloudStorage(
            gcpOrganization.getId(),
            pCpsBlobStoreConfig.getBucketName(),
            pCpsOplogSlice.getCpsOplogKeyString());
    return storageBlob.getCustomerEncryption() != null
        && !storageBlob.getCustomerEncryption().getKeySha256().isEmpty();
  }

  private boolean isOplogEncryptedOnAzure(
      final CpsBlobStoreConfig pCpsBlobStoreConfig, final CpsOplogSlice pCpsOplogSlice) {
    final AzureStorageAccount storageAccount =
        _azureStorageAccountDao.findByStorageAccountName(pCpsBlobStoreConfig.getBucketName()).get();
    final String permissionString = "rl"; // read and list permissions
    final NDSAzureTempCredentialsView tempCredentialsView =
        _azureApiSvc.generateStorageContainerSasToken(
            storageAccount.getStorageAccountName(),
            storageAccount.getUnencryptedStorageAccountKey(),
            "root",
            permissionString);
    final BlobClient blobClient =
        _azureApiSvc
            .getAzureApiStorageSvc()
            .getBlobClientForContainer(
                tempCredentialsView, pCpsOplogSlice.getCpsOplogKeyString(), "root");
    if (!blobClient.exists()) {
      return false;
    }

    try {
      return blobClient.getProperties() != null
          && StringUtils.isNotEmpty(blobClient.getProperties().getEncryptionKeySha256());
    } catch (BlobStorageException e) {
      LOG.debug(
          "Got BlobStorageException while trying to fetch blob properties. BlobErrorCode: {},"
              + " ServiceMessage: {}",
          e.getErrorCode(),
          e.getServiceMessage(),
          e);
      return BlobErrorCode.BLOB_USES_CUSTOMER_SPECIFIED_ENCRYPTION.equals(e.getErrorCode());
    }
  }

  private boolean isOplogEncryptedOnAws(
      final CpsBlobStoreConfig pCpsBlobStoreConfig, final CpsOplogSlice pCpsOplogSlice) {
    final AWSRegionName regionName =
        AWSRegionName.findByValue(pCpsBlobStoreConfig.getRegion()).get();

    final AWSAccount awsAccount =
        _awsAccountDao
            .findCPSOplogStoreAccount(
                regionName.getAWSEnv(),
                regionName.isCNRegion(),
                regionName.getUsageRestrictionFromRegion())
            .orElseThrow(() -> new IllegalStateException("Cannot find AWS account for CPS"));

    final Credentials creds =
        _cpsConfSvc.getAwsCredentialsForAgentSession(
            pCpsOplogSlice.getGroupId(), awsAccount, pCpsBlobStoreConfig);

    final AWSCredentials awsCredentials =
        AWSCredentialsUtil.getAWSCredentials(
            creds.getAccessKeyId(), creds.getSecretAccessKey(), creds.getSessionToken());

    // It's expected to go into the catch AWSApiException block since we are not passing in the
    // decrypted data key for encrypted slices
    try {
      _awsApiSvc.getObjectMetadata(
          awsCredentials,
          pCpsBlobStoreConfig.getBucketName(),
          pCpsOplogSlice.getCpsOplogKeyString(),
          false,
          LOG);
    } catch (final AWSApiException e) {
      LOG.info("Expected the exception", e);
      return true;
    }
    return false;
  }

  @GET
  @Path("/nds/backup/restore/{groupId}/{restoreJobId}/instanceHardware")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getBackupRestoreJobInstanceHardware(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("restoreJobId") final ObjectId pRestoreJobId) {
    final VMBasedReplSetRestoreJob job = _backupRestoreJobDao.findVMBasedReplSetJob(pRestoreJobId);
    return Response.ok(job.getInstanceHardware().toDBObject()).build();
  }

  @GET
  @Path("/backup/restore/jobs/{groupId}/{limit}")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  @TestUtility
  public Response getLatestBackupRestoreJob(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("limit") final int pLimit) {
    final List<DBObject> job = _restoreJobDao.getLatestRestoreJobs(pGroupId, pLimit);
    final JSONArray jsonArray = new JSONArray();
    for (final DBObject jobEntry : job) {
      final JSONObject jsonObject = new JSONObject();
      for (final String field : jobEntry.keySet()) {
        jsonObject.put(field, jobEntry.get(field));
      }
      jsonArray.put(jsonObject);
    }
    return Response.ok(jsonArray.toString()).build();
  }

  @GET
  @Path("/backup/web/jobs/checkpointingTarget/{groupId}/{rsId}")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  public Response getCheckpointingTarget(
      @Context final HttpServletRequest request,
      @Context final HttpServletResponse response,
      @PathParam("groupId") final String pGroupId,
      @PathParam("rsId") final String pRsId,
      @QueryParam("page") final int pageNum) {
    final RequestParams params = getRequestParams(request);
    final Group group = params.getCurrentGroup();
    final WtBackupStatus wtBackupStatus =
        _jobDao.getBackupStatus(group.getId(), pRsId).getWtBackupStatus();
    if (wtBackupStatus == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    final WtBackupStatus.TargetingSelection checkpointingTarget = wtBackupStatus.getCheckpointing();
    if (checkpointingTarget == null) {
      return Response.status(Response.Status.NOT_FOUND).build();
    }
    final DBObject checkpointingTargetDBObect = checkpointingTarget.toDBObject();
    final JSONObject jsonResponse = new JSONObject();
    for (final String field : checkpointingTargetDBObect.keySet()) {
      jsonResponse.put(field, checkpointingTargetDBObect.get(field));
    }
    return Response.ok(jsonResponse.toString()).build();
  }

  @GET
  @Path("/nds/groups/{groupId}/x509access/atlasAdmin")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response generateClusterAccess(@PathParam("groupId") final ObjectId pGroupId) {
    final String cert;
    final String key;
    try {
      final TLSUtil.GenerateCSRResult generatedCSR =
          TLSUtil.generateCSR(
              "CN=doesntmatter", null, NDSDefaults.X509_SIG_ALGORITHM, NDSDefaults.X509_KEYLENGTH);
      final NDSManagedX509.CA caToGenerateCert =
          getNdsGroupDao()
              .find(pGroupId)
              .orElseThrow(IllegalStateException::new)
              .getUserSecurity()
              .getManagedX509()
              .getCAs()
              .get(0);
      final X509CertificateHolder generatedCert =
          TLSUtil.signClientCertificateWithExtensions(
              generatedCSR.csr,
              caToGenerateCert.getCert(),
              caToGenerateCert.getPrivateKey(),
              BigInteger.ZERO,
              DateUtils.addHours(new Date(), -1),
              DateUtils.addHours(new Date(), 3),
              NDSDefaults.X509_SIG_ALGORITHM,
              List.of(
                  TLSUtil.toDbRolesCertificateExtension(
                      Stream.of(
                              _ndsGroupSvc
                                  .getNDSDBRoleSvc()
                                  .factoryCreate("admin", null, "atlasAdmin")
                                  .getRoleAndDbPair())
                          .map(p -> Pair.of(p.getLeft(), p.getRight()))
                          .collect(Collectors.toList()))));

      final TLSUtil.PEMKeyFile x509Cert =
          new TLSUtil.PEMKeyFile(Collections.singletonList(generatedCert), generatedCSR.privateKey);

      cert =
          new String(
              TLSUtil.getCertPEMBytes(x509Cert.getKeyCertificate().toASN1Structure()),
              StandardCharsets.UTF_8);
      key = new String(TLSUtil.getKeyPEMBytes(x509Cert.getKey()), StandardCharsets.UTF_8);

    } catch (final Exception e) {
      LOG.error("Unexpected error while generating cert", e);
      return Response.serverError().entity(e.getMessage()).build();
    }

    return Response.ok(cert.concat(key)).build();
  }

  @GET
  @Path("/backup/config/snapshotStores")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getSnapshotStores(
      @QueryParam("snapshotStoreType") final String pSnapshotStoreType) throws Exception {
    final SnapshotStoreType snapshotStoreType = SnapshotStoreType.fromString(pSnapshotStoreType);
    switch (snapshotStoreType) {
      case fileSystemStore:
        return Response.ok(_backupAdminSvc.getFileSystemStoreConfigs(true)).build();
      case blockstore:
        return Response.ok(_backupAdminSvc.getBlockStoreConfigs(true)).build();
      case s3blockstore:
        return Response.ok(_backupAdminSvc.getS3BlockStoreConfigsCursor(true).toList()).build();
      default:
        throw new Exception("Unknown store type " + pSnapshotStoreType);
    }
  }

  @POST
  @Path("/backup/config/snapshotStores/fileSystemStore")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response createFileSystemStore(final FileSystemStoreConfig pConfig) throws Exception {
    final FileSystemStoreConfig config;
    if (pConfig == null || pConfig.getId() == null) {
      final String fsPath;
      if (_appSettings
          .getAllPropertiesWithoutConfigService(true)
          .containsKey("mms.backup.e2e.filsystemstore.path")) {
        fsPath = _appSettings.getStrProp("mms.backup.e2e.filsystemstore.path");
      } else {
        final String tmpPath = System.getProperty("java.io.tmpdir");
        fsPath = Paths.get(tmpPath, "/test_san/" + UUID.randomUUID()).toString();
        new File(fsPath).mkdirs();
      }
      config = new FileSystemStoreConfig();
      config.setAssignmentEnabled(true);
      config.setDeploymentId("NY_NJ");
      config.setId("filesystem1");
      config.setStorePath(fsPath);
      config.setWtCompressionSetting(SnapshotConstants.CompressionSetting.NONE);
      config.setMmapv1CompressionSetting(SnapshotConstants.CompressionSetting.NONE);
    } else {
      config = pConfig;
    }
    _backupAdminSvc.createOrUpdateFileSystemStoreConfig(AuditInfoHelpers.fromSystem(), config);
    return Response.ok().build();
  }

  @POST
  @Path("/backup/config/snapshotStores/blockstore")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response createBlockStore(final BackupDataStoreConfig pConfig) throws Exception {
    _backupAdminSvc.createOrUpdateBlockStoreConfig(AuditInfoHelpers.fromSystem(), pConfig);
    return Response.ok().build();
  }

  @POST
  @Path("/backup/config/snapshotStores/s3blockstore")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response createS3BlockStore(final S3DataStoreConfig pConfig) {
    final S3DataStoreConfig config;
    final String accessKey =
        _appSettings.getStrProp("mms.backup.e2e.blockstore.aws.accesskey").strip();
    final String secretKey =
        _appSettings.getStrProp("mms.backup.e2e.blockstore.aws.secretkey").strip();
    if (pConfig == null || pConfig.getId() == null) {
      final String mongoUri = _appSettings.getMongoUri("");
      config = new S3DataStoreConfig();
      config.setAcceptedTos(true);
      config.setAssignmentEnabled(true);
      config.setDeploymentId("NY_NJ");
      config.setAwsAccessKey(accessKey);
      config.setAwsSecretKey(secretKey);
      config.setS3AuthMethod(AuthMethod.KEYS);
      config.setS3BucketEndpoint("s3.amazonaws.com");
      config.setS3BucketName("blockstore-blocks-test1");
      config.setS3MaxConnections(40);
      config.setSseEnabled(true);
      config.setPathStyleAccessEnabled(true);
      config.setDirectS3RestoreEnabled(true);
      config.setUri(mongoUri);
      config.setId("s3blockstore1");
    } else {
      config = pConfig;
      config.setAwsAccessKey(accessKey);
      config.setAwsSecretKey(secretKey);
    }
    _s3BlockStoreConfigDao.updateConfiguration(config);
    return Response.ok().build();
  }

  @POST
  @Path("/backup/config/snapshotStores/s3blockstore/immutable")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response createImmutableS3BlockStore(final String pBody) throws Exception {
    JSONObject jsonObject = new JSONObject(pBody);
    String dataStoreId = jsonObject.getString("dataStoreId");
    String bucketName = jsonObject.getString("s3Bucket");
    final String accessKey =
        _appSettings.getStrProp("mms.backup.e2e.blockstore.aws.accesskey").strip();
    final String secretKey =
        _appSettings.getStrProp("mms.backup.e2e.blockstore.aws.secretkey").strip();
    final String mongoUri = _appSettings.getMongoUri("");
    S3DataStoreConfig config = new S3DataStoreConfig();
    config.setAcceptedTos(true);
    config.setAssignmentEnabled(true);
    config.setDeploymentId("NY_NJ");
    config.setAwsAccessKey(accessKey);
    config.setAwsSecretKey(secretKey);
    config.setS3AuthMethod(AuthMethod.KEYS);
    config.setS3BucketEndpoint("s3.amazonaws.com");
    config.setS3BucketName(bucketName);
    config.setS3MaxConnections(40);
    config.setSseEnabled(true);
    config.setPathStyleAccessEnabled(true);
    config.setUri(mongoUri);
    config.setId(dataStoreId);
    // object lock enabled
    config.setObjectLockEnabled(true);
    _s3BlockStoreConfigDao.updateConfiguration(config);
    BackupDataStoreConfig backupDataStoreConfig =
        _s3BlockStoreConfigDao.getConfiguration(dataStoreId);
    return Response.ok(backupDataStoreConfig).build();
  }

  @GET
  @Path("/backup/config/snapshotStores/s3blockstore/fileBlockS3Keys")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getFileBlockS3Keys(
      @QueryParam("blockStoreId") final String pBlockStoreId,
      @QueryParam("fileName") final String pFileName,
      @QueryParam("snapshotId") String pSnapshotId)
      throws Exception {
    Snapshot snapshot = _snapshotDao.getSnapshot(new ObjectId(pSnapshotId));
    if (snapshot == null) {
      LOG.error("Can't find snapshot object by id: {}", pSnapshotId);
      return Response.status(Status.NOT_FOUND.getStatusCode(), "can't find snapshot").build();
    }
    String filepath = SnapshotUtil._normalize(pFileName);
    DBObject fileObject = snapshot.getFilePathToIdMap().get(filepath);
    if (fileObject == null) {
      LOG.error("file path doesn't exist in snapshot object. file path: {}", filepath);
      return Response.status(Status.NOT_FOUND.getStatusCode(), "can't find file path").build();
    }
    ObjectId fileId = ((BasicDBObject) fileObject).getObjectId("fileId");
    BlockFileDao blockFileDao = BlockFileDao.getForRead(pBlockStoreId, _mongoSvc, Optional.empty());
    FileSlice fileSlice = blockFileDao.get(fileId).getFileSlice();
    LOG.info("file slice raw content: {}", ((BasicDBObject) fileSlice.toDBObject()).toJson());
    ArrayList<String> s3KeyList = new ArrayList<>();
    for (DBObject block : fileSlice.getBlockInfo()) {
      String s3Key =
          String.format(
              "%s/%s_%s",
              ((BasicDBObject) block).getString("hash"),
              snapshot.getJobId().toString(),
              fileSlice.getPhase());
      s3KeyList.add(s3Key);
    }
    return Response.ok(s3KeyList).build();
  }

  @POST
  @Path("/backup/config/snapshotStores/s3blockstore/putFile")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response putFileOnS3(final String pBody) throws Exception {
    JSONObject jsonObject = new JSONObject(pBody);
    String s3Bucket = jsonObject.getString("s3Bucket");
    String s3Key = jsonObject.getString("s3Key");
    String content = jsonObject.getString("fileContent");
    boolean isOverride = jsonObject.getBoolean("isOverride");
    final String accessKey =
        _appSettings.getStrProp("mms.backup.e2e.blockstore.aws.accesskey").strip();
    final String secretKey =
        _appSettings.getStrProp("mms.backup.e2e.blockstore.aws.secretkey").strip();
    AmazonS3 s3Client =
        AmazonS3ClientBuilder.standard()
            .withCredentials(
                new AWSStaticCredentialsProvider(new BasicAWSCredentials(accessKey, secretKey)))
            .withRegion(Region.US_EAST_1.id())
            .build();
    String oldVersionId = "null";
    if (isOverride) {
      // if file not exist, this will throw exception
      ObjectMetadata oldObjectMetadata = s3Client.getObjectMetadata(s3Bucket, s3Key);
      oldVersionId = oldObjectMetadata.getVersionId();
    }
    ObjectMetadata metadata = new ObjectMetadata();
    byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
    byte[] digest = DigestUtils.md5(contentBytes);
    metadata.setContentMD5(Base64.getEncoder().encodeToString(digest));
    metadata.setContentLength(content.length());
    ByteArrayInputStream inputStream = new ByteArrayInputStream(contentBytes);
    PutObjectRequest putRequest = new PutObjectRequest(s3Bucket, s3Key, inputStream, metadata);
    PutObjectResult result = s3Client.putObject(putRequest);
    LOG.info(
        "S3 file was uploaded/modified. key: {}, old version id: {}, new version id: {}",
        s3Key,
        oldVersionId,
        result.getVersionId());
    return Response.ok(Map.of("oldVersionId", oldVersionId, "newVersionId", result.getVersionId()))
        .build();
  }

  @GET
  @Path("/backup/config/oplogStores")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getOplogStores(@QueryParam("oplogStoreType") final String pOplogStoreType)
      throws Exception {

    final OplogStoreType oplogStoreType;
    if (pOplogStoreType.contains("s3")) {
      oplogStoreType = OplogStoreType.s3OplogStore;
    } else {
      oplogStoreType = OplogStoreType.oplogStore;
    }
    switch (oplogStoreType) {
      case oplogStore:
        return Response.ok(_backupAdminSvc.getOplogStoreConfigs(true)).build();
      case s3OplogStore:
        return Response.ok(_backupAdminSvc.getS3OplogStoreConfigs(true)).build();
      default:
        throw new Exception("Unknown store type " + oplogStoreType);
    }
  }

  @POST
  @Path("/backup/config/oplogStores/s3OplogStore")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response createS3OplogStore(final S3DataStoreConfig pConfig) throws Exception {
    final S3DataStoreConfig config;
    if (pConfig == null || pConfig.getId() == null) {
      final String accessKey = _appSettings.getStrProp("mms.backup.e2e.oplogstore.aws.accesskey");
      final String secretKey = _appSettings.getStrProp("mms.backup.e2e.oplogstore.aws.secretkey");
      final String mongoUri = _appSettings.getMongoUri("");
      config = new S3DataStoreConfig();
      config.setAcceptedTos(true);
      config.setAssignmentEnabled(true);
      config.setDeploymentId("NY_NJ");
      config.setAwsAccessKey(accessKey);
      config.setAwsSecretKey(secretKey);
      config.setS3AuthMethod(AuthMethod.KEYS);
      config.setS3BucketEndpoint("s3.amazonaws.com");
      config.setS3BucketName("oplogstore-slices-test1");
      config.setS3MaxConnections(40);
      config.setSseEnabled(true);
      config.setPathStyleAccessEnabled(true);
      config.setUri(mongoUri);
      config.setId("s3oplog1");
    } else {
      config = pConfig;
    }
    _s3OplogStoreConfigDao.updateConfiguration(config);
    return Response.ok().build();
  }

  @POST
  @Path("/backup/config/oplogStores/oplogStore")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response createOplogStore(final BackupDataStoreConfig pConfig) throws Exception {
    final BackupDataStoreConfig config;
    if (pConfig == null || pConfig.getId() == null) {
      final String mongoUri = _appSettings.getMongoUri("");
      config = new BackupDataStoreConfig();
      config.setAssignmentEnabled(true);
      config.setUri(mongoUri);
      config.setId("oplog1");
    } else {
      config = pConfig;
    }
    _backupAdminSvc.createOrUpdateOplogStoreConfig(AuditInfoHelpers.fromSystem(), config);
    return Response.ok().build();
  }

  @DELETE
  @Path("/slowms/host/{hostId}/metadata")
  @UiCall(auth = false)
  public Response deleteSlowMsHostMetadata(
      @Context final HttpServletRequest pRequest, @PathParam("hostId") final String pHostId) {
    try {
      _slowQueryLogIngestionMetadataDao.delete(pHostId);
      return Response.ok().build();
    } catch (final Exception ex) {
      return SimpleApiResponse.error(CommonErrorCode.SERVER_ERROR).build();
    }
  }

  @POST
  @Path("/slowms/group/{groupId}/metadata")
  @UiCall(auth = false)
  public Response storeSlowMsHostMetadata(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("hostId") final String pHostId,
      @FormParam("hostnameAndPort") final String pHostnameAndPort,
      @FormParam("logCount") final String pLogCount) {

    try {
      final SlowQueryLogIngestionMetadata metadataEntry =
          new SlowQueryLogIngestionMetadata(pHostId, pHostnameAndPort, pGroupId);
      metadataEntry.setLogCount(Integer.parseInt(pLogCount));

      _slowQueryLogIngestionMetadataDao.save(List.of(metadataEntry));
      return Response.ok().build();
    } catch (final Exception ex) {
      return SimpleApiResponse.error(CommonErrorCode.SERVER_ERROR).build();
    }
  }

  @GET
  @Path("/slowms/group/{groupId}/host/{hostId}/hostMeasurements")
  @UiCall(auth = false)
  public Response getHostMeasurementsForHost(
      @Context final HttpServletRequest pRequest,
      @Context final HttpServletResponse pResponse,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("hostId") final String pHostId,
      @QueryParam("start") final Long pStartMillis,
      @QueryParam("end") final Long pEndMillis) {

    final JSONObject response = new JSONObject();
    final Group group = new Group();
    group.setId(pGroupId);
    final List<HostMeasurement> hostMeasurements =
        _hostMeasurementSvc.findHostMeasurements(
            group,
            MINUTE_DATA_RETENTION,
            Instant.ofEpochMilli(pStartMillis),
            Instant.ofEpochMilli(pEndMillis),
            pHostId,
            secondaryPreferred());

    response.put(
        "hostMeasurements",
        hostMeasurements.stream().map(HostMeasurement::toDBObject).collect(Collectors.toList()));
    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/govCloudActivationCode")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response generateGovCloudActivationCode(
      @Context final HttpServletRequest pRequest, @Context final HttpServletResponse pResponse)
      throws SvcException {
    if (_appSettings.getAppEnv().isGovCloud()) {
      final String activationCode =
          _salesSoldDealActivationSvc.generateTestEmployeeActivationCode(true);
      return Response.ok(new JSONObject().put("activationCode", activationCode).toString()).build();
    }

    return Response.ok().build();
  }

  @POST
  @Path("/nds/customMongoDbBuild")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  public Response addCustomMongoDbBuild(
      @Context final HttpServletRequest pRequest,
      final ApiAtlasMongoDbBuildView pApiAtlasMongoDbBuildView)
      throws SvcException {
    final Optional<MongoDbBuild> customBuild =
        _customMongoDbBuildSvc.getCustomBuild(pApiAtlasMongoDbBuildView.getTrueName()).stream()
            .filter(mdbBuild -> mdbBuild.equals(pApiAtlasMongoDbBuildView.toMongoDbBuild()))
            .findFirst();
    if (customBuild.isEmpty()
        || !customBuild.get().equalsCustomBuild(pApiAtlasMongoDbBuildView.toMongoDbBuild())) {
      _customMongoDbBuildSvc.addCustomBuild(pApiAtlasMongoDbBuildView.toMongoDbBuild());
    }
    return Response.ok().build();
  }

  @DELETE
  @Path("/nds/customMongoDbBuilds/{buildName}")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response deleteCustomMongoDbBuildByName(
      @Context final HttpServletRequest pRequest, @PathParam("buildName") final String pBuildName) {
    _customMongoDbBuildDao.delete(pBuildName);
    return Response.ok().build();
  }

  @POST
  @Path("/nds/serverless/{groupId}/{clusterName}/mockedMetrics")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  public Response insertMockedMetric(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final ServerlessMockedMetricView pServerlessMockedMetricView) {
    switch (pServerlessMockedMetricView.getType()) {
      case MTM_PING:
        _ndsServerlessMetricsSvc.setMockedMTMPingMetric(
            pGroupId,
            pClusterName,
            pServerlessMockedMetricView.getServerlessMetricName(),
            pServerlessMockedMetricView.getValue(),
            pServerlessMockedMetricView.getHostValueOverrides(),
            pServerlessMockedMetricView.getExpiresAfter().toInstant());
        return Response.ok().build();
      case MTM_SERIES:
        _ndsServerlessMetricsSvc.setMockedMTMSeriesMetric(
            pGroupId,
            pClusterName,
            pServerlessMockedMetricView.getServerlessMetricName(),
            pServerlessMockedMetricView.getValue(),
            pServerlessMockedMetricView.getHostValueOverrides(),
            pServerlessMockedMetricView.getExpiresAfter().toInstant());
        return Response.ok().build();
      case TENANT_SERIES:
        _ndsServerlessMetricsSvc.setMockedTenantSeriesMetric(
            pGroupId,
            pClusterName,
            pServerlessMockedMetricView.getServerlessMetricName(),
            pServerlessMockedMetricView.getValue(),
            pServerlessMockedMetricView.getHostValueOverrides(),
            pServerlessMockedMetricView.getExpiresAfter().toInstant());
        return Response.ok().build();
      default:
        throw new IllegalArgumentException("Invalid serverless mocked metric type");
    }
  }

  @POST
  @Path("/nds/mtm/{mtmName}/capacity")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  public Response updateMtmCapacity(
      @Context final HttpServletRequest pRequest,
      @PathParam("mtmName") final String pName,
      final String pBody) {
    final JSONObject body = new JSONObject(pBody);
    final int i = _mtmClusterDao.setMaxCapacityForTests(pName, body.getInt("capacity"));
    if (i != 1) {
      return Response.status(Status.CONFLICT)
          .entity(String.format("Expected to update 1 mtm with name %s, updated %d", pName, i))
          .build();
    }
    return Response.ok().build();
  }

  @POST
  @Path("/nds/mtm/{mtmName}/incrementCapacity")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  public Response incrementMtmCapacity(
      @Context final HttpServletRequest pRequest,
      @PathParam("mtmName") final String pName,
      final String pBody) {
    final JSONObject body = new JSONObject(pBody);
    final int i = _mtmClusterDao.incrementCapacityForTests(pName, body.getInt("increment"));
    if (i != 1) {
      return Response.status(Status.CONFLICT)
          .entity(String.format("Expected to update 1 mtm with name %s, updated %d", pName, i))
          .build();
    }
    return Response.ok().build();
  }

  @GET
  @Path("/nds/mtm/{mtmName}/capacity")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getMtmCapacity(
      @Context final HttpServletRequest pRequest, @PathParam("mtmName") final String pName) {
    final Optional<Integer> pCapacity = _mtmClusterDao.findByNameForTests(pName);
    return pCapacity
        .map(i -> Response.ok(new JSONObject().put("capacity", i).toString()).build())
        .orElse(Response.status(Status.NOT_FOUND).build());
  }

  @GET
  @Path("/nds/mtm/fastSharedRecords/{state}/count")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getFastTenantRecordsPoolCount(
      @Context final HttpServletRequest pRequest, @PathParam("state") final String pName) {
    return Response.ok(
            new JSONObject()
                .put(
                    "count",
                    _fastSharedPreAllocatedRecordsDao.countByState(
                        FastSharedPreAllocatedRecord.State.valueOf(pName.toUpperCase())))
                .toString())
        .build();
  }

  @POST
  @Path("/nds/mtm/fastSharedRecords")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response createFastSharedRecord(
      @Context final HttpServletRequest pRequest,
      @FormParam("mtmClusterName") final String pMtmClusterName,
      @FormParam("mtmGroupId") final ObjectId pMtmGroupId) {
    final Optional<SharedMTMCluster> mtm =
        _sharedMTMClusterDao.findSharedByName(pMtmGroupId, pMtmClusterName);
    if (mtm.isEmpty()) {
      return Response.status(Status.BAD_REQUEST).entity("Unknown mtm").build();
    }

    _fastSharedPreAllocatedRecordsDao.saveRecord(
        _ndsFastSharedTierCronSvc.generateRecord(
            mtm.get().getProviderName(), mtm.get().getRegion(), mtm.get().getTenantInstanceSize()));

    return Response.ok().build();
  }

  @GET
  @Path("/nds/mtm/serverless/{poolId}/loadBalancingDeployment")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getServerlessLoadBalancingDeploymentsForPool(
      @Context final HttpServletRequest pRequest, @PathParam("poolId") final ObjectId pPoolId)
      throws Exception {
    final List<ServerlessLoadBalancingDeploymentView> serverlessLoadBalancingDeploymentViews =
        _ndsFastServerlessCronSvc
            .getAllLoadBalancingDeploymentsForGroups(
                List.of(_serverlessMTMPoolSvc.getPool(pPoolId).getGroupId()))
            .stream()
            .map(ServerlessLoadBalancingDeploymentView::new)
            .collect(Collectors.toList());
    return Response.ok(serverlessLoadBalancingDeploymentViews).build();
  }

  @PATCH
  @Path("/nds/serverless/{groupId}/{clusterName}/updateLinkedMTMs")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response updateLinkedMTMs(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final Optional<ServerlessMTMPool> pool = _serverlessMTMPoolSvc.findPoolByGroupId(pGroupId);
    final Optional<ServerlessMTMCluster> mtmCluster =
        _serverlessMTMClusterDao.findServerlessCluster(pGroupId, pClusterName);

    if (pool.isPresent() && mtmCluster.isPresent()) {
      _serverlessMTMPoolSvc.decrementPoolMTMCapacity(
          pool.get().getId(), mtmCluster.get().getMtmClusterDescriptionUniqueId());
      return Response.ok().entity(EMPTY_JSON_OBJECT).build();
    } else {
      return Response.status(Status.NOT_FOUND).build();
    }
  }

  @PATCH
  @Path("/nds/serverless/{groupId}/{clusterName}/updateMTMLoad")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response updateServerlessMTMLoad(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName) {
    final Optional<ServerlessMTMCluster> serverlessMTMCluster =
        _serverlessMTMClusterDao.findServerlessByName(pGroupId, pClusterName);
    if (serverlessMTMCluster.isPresent()) {
      final Date currentLoadCheckDate = serverlessMTMCluster.get().getNextLoadCheckDate();
      _ndsServerlessLoadSvc.updateMTMLoadForCluster(pGroupId, pClusterName, currentLoadCheckDate);
      return Response.ok().entity(EMPTY_JSON_OBJECT).build();
    } else {
      return Response.status(Status.NOT_FOUND).build();
    }
  }

  @PATCH
  @Path("/nds/serverless/{groupId}/{serverlessInstanceName}/updateTenantLoad")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response updateServerlessTenantLoad(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("serverlessInstanceName") final String pServerlessInstanceName) {
    final Optional<ClusterDescription> serverlessInstanceDescription =
        _clusterDescriptionDao.findByName(pGroupId, pServerlessInstanceName);
    if (serverlessInstanceDescription.isPresent()) {
      final Date currentLoadCheckDate =
          serverlessInstanceDescription
              .get()
              .getCloudProviderOptions()
              .getDate(ServerlessTenantProviderOptions.FieldDefs.NEXT_LOAD_CHECK_DATE);
      _ndsServerlessLoadSvc.updateTenantLoadForInstance(
          pGroupId, pServerlessInstanceName, currentLoadCheckDate);
      return Response.ok().entity(EMPTY_JSON_OBJECT).build();
    } else {
      return Response.status(Status.NOT_FOUND).build();
    }
  }

  @GET
  @Path("/nds/groups/{groupId}/limits")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getCustomLimit(@PathParam("groupId") final ObjectId pGroupId) throws Exception {
    final Limits limits =
        getNdsGroupSvc()
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID))
            .getLimits();
    return Response.ok(new LimitsView(limits)).build();
  }

  @PATCH
  @Path("/nds/groups/{groupId}/limits")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response setCustomLimit(
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pUser,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("limit") final String pLimitName,
      @FormParam("value") final Long pValue)
      throws Exception {
    getNdsGroupSvc().setLimit(pGroupId, pLimitName, pValue, pAuditInfo, pUser);
    return Response.ok().build();
  }

  @GET
  @Path("/versions")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getNDSVersions() {
    return Response.ok(_ndsUISvc.getAllNDSVersions()).build();
  }

  private InstanceHardwareHealth generateExpiredInstanceHardwareHealth(
      final CloudProvider pCloudProvider, final Date pNow, final Date pPrevious) {
    final InstanceHardwareHealth.Builder builder;
    switch (pCloudProvider) {
      case AWS:
        builder =
            new AWSInstanceHardwareHealth.Builder()
                .setLastInstanceState(new InstanceHardwareHealth.HealthItem(pPrevious, "stopped"))
                .setLastVolumeStatus(new InstanceHardwareHealth.HealthItem(pPrevious, "ok"));
        break;
      case AZURE:
        builder =
            new AzureInstanceHardwareHealth.Builder()
                .setLastInstanceState(
                    new InstanceHardwareHealth.HealthItem(
                        pPrevious, PowerState.STOPPED.toString()));
        break;
      case GCP:
        builder =
            new GCPInstanceHardwareHealth.Builder()
                .setLastInstanceState(
                    new InstanceHardwareHealth.HealthItem(
                        pPrevious, GCPInstanceState.TERMINATED.name()));
        break;
      default:
        throw new IllegalStateException("Unsupported provider");
    }
    return builder
        .setCollectedForDate(pNow)
        .setLastAutomationAuditDate(pPrevious)
        .setLastMonitoringProcessPingDate(pPrevious)
        .setLastICMPPing(new InstanceHardwareHealth.HealthItem(pPrevious, "unreachable"))
        .setReplicaSetMemberState(new InstanceHardwareHealth.HealthItem(pPrevious, "DOWN"))
        .build();
  }

  private EmailPayload generateTestAlertEmail(
      final String pAlertType,
      final String pEventType,
      final String pMetricType,
      final Alert.Status pStatus,
      final boolean pVerbose,
      final GroupType pGroupType) {
    final Organization org =
        new Organization.Builder().id(new ObjectId()).name("Test Organization").build();
    final Group group = new Group();
    group.setId(new ObjectId());
    group.setName("Test Group");
    group.setGroupType(pGroupType);
    final Alert.Builder baseBuilder;
    switch (pAlertType) {
      case AgentAlert.TYPE_NAME:
        {
          final AgentAlert.Builder builder = new AgentAlert.Builder(new ObjectId());
          builder.eventType(AgentEvent.Type.valueOf(pEventType));
          builder.maximumFailedConfCalls(10);
          baseBuilder = builder;
          break;
        }
      case BackupAlert.TYPE_NAME:
        {
          final BackupAlert.Builder builder = new BackupAlert.Builder(new ObjectId());
          builder.eventType(BackupEvent.Type.valueOf(pEventType));
          builder.replicaSetId("rs1");
          builder.sourceType(BackupEvent.SourceType.REPLICA_SET);
          builder.inconsistentConfigs(
              new BackupInconsistentConfigs(
                  StorageEngine.WIRED_TIGER.getCommandLineName(),
                  new BasicDBObject(),
                  true,
                  StorageEngine.MEMORY_MAPPED.getCommandLineName(),
                  new BasicDBObject(),
                  false,
                  true));
          baseBuilder = builder;
          break;
        }
      case BackupBindAlert.TYPE_NAME:
        {
          final BackupBindAlert.Builder builder = new BackupBindAlert.Builder(new ObjectId());
          builder.eventType(BackupEvent.Type.valueOf(pEventType));
          builder.replicaSetId("rs1");
          builder.sourceType(BackupEvent.SourceType.REPLICA_SET);
          builder.bindError("An error has occurred.");
          baseBuilder = builder;
          break;
        }
      case BackupBusyAlert.TYPE_NAME:
        {
          final BackupBusyAlert.Builder builder = new BackupBusyAlert.Builder(new ObjectId());
          builder.eventType(BackupEvent.Type.valueOf(pEventType));
          builder.replicaSetId("rs1");
          builder.sourceType(BackupEvent.SourceType.REPLICA_SET);
          builder.applyOpsTimeMs(Duration.ofHours(6).toMillis());
          builder.snapshotTimeMs(Duration.ofHours(12).toMillis());
          builder.totalTimeMs(Duration.ofHours(18).toMillis());
          baseBuilder = builder;
          break;
        }
      case BackupClustershotAlert.TYPE_NAME:
        {
          final BackupClustershotAlert.Builder builder =
              new BackupClustershotAlert.Builder(new ObjectId());
          builder.eventType(BackupEvent.Type.valueOf(pEventType));
          builder.clusterName("test");
          builder.clusterId(new ObjectId());
          builder.sourceType(BackupEvent.SourceType.SHARDED_CLUSTER);
          builder.failureCount(5);
          builder.lastFailureTime(new Date());
          builder.lastFailureMessage("An error has occurred.");
          baseBuilder = builder;
          break;
        }
      case BackupSnapshotAlert.TYPE_NAME:
        {
          final BackupSnapshotAlert.Builder builder =
              new BackupSnapshotAlert.Builder(new ObjectId());
          builder.eventType(BackupEvent.Type.valueOf(pEventType));
          builder.replicaSetId("rs1");
          builder.sourceType(BackupEvent.SourceType.REPLICA_SET);
          builder.integrityCheckInProgress(true);
          builder.numFilesWritten(10);
          builder.totalFiles(20);
          builder.currentTime(new Date());
          builder.lastIntegrityCheck(DateUtils.addHours(new Date(), -1));
          builder.oldSnapshot(DateUtils.addHours(new Date(), -5));
          builder.nextSnapshot(DateUtils.addHours(new Date(), 2));
          baseBuilder = builder;
          break;
        }
      case BackupSyncSliceAlert.TYPE_NAME:
        {
          final BackupSyncSliceAlert.Builder builder =
              new BackupSyncSliceAlert.Builder(new ObjectId());
          builder.eventType(BackupEvent.Type.valueOf(pEventType));
          builder.replicaSetId("rs1");
          builder.sourceType(BackupEvent.SourceType.REPLICA_SET);
          builder.syncApproved(new Date());
          builder.syncStartTime(new Date());
          builder.lastSliceStartTime(new Date());
          baseBuilder = builder;
          break;
        }
      case RestoreStatusAlert.TYPE_NAME:
        {
          final RestoreStatusAlert.Builder builder = new RestoreStatusAlert.Builder(new ObjectId());
          builder.eventType(BackupEvent.Type.NO_DAEMON_AVAILABLE_FOR_QUERYABLE_RESTORE_JOB);
          builder.replicaSetId("rs1");
          builder.sourceType(BackupEvent.SourceType.REPLICA_SET);
          builder.restoreStatus(
              new RestoreStatus(
                  new BasicDBObject("timestamp", new BSONTimestamp())
                      .append("delivery", new BasicDBObject("method", "scp"))
                      .append(
                          "daemonFilter",
                          Arrays.asList(
                              new BasicDBObject("machine", "abc123").append("head", "/head1"),
                              new BasicDBObject("machine", "def456").append("head", "/head2")))));
          baseBuilder = builder;
          break;
        }
      case BillingAlert.TYPE_NAME:
        {
          final BillingAlert.Builder builder = new BillingAlert.Builder(new ObjectId());
          builder.eventType(BillingEvent.Type.valueOf(pEventType));
          builder.paymentMethodId(new ObjectId());
          builder.creditCardNumber("4242");
          builder.expirationDate(DateUtils.addDays(new Date(), 15));
          builder.invoiceId(ObjectId.get());
          builder.invoiceTotalCents(10000L);
          builder.lastDailyBillCents(1000L);
          baseBuilder = builder;
          break;
        }
      case GroupAlert.TYPE_NAME:
        {
          final GroupAlert.Builder builder = new GroupAlert.Builder(new ObjectId());
          final GroupEvent.Type eventType = GroupEvent.Type.valueOf(pEventType);
          builder.eventType(eventType);
          baseBuilder = builder;
          break;
        }
      case HostAlert.TYPE_NAME:
        {
          final HostAlert.Builder builder = new HostAlert.Builder(new ObjectId());
          builder.eventType(HostEvent.Type.valueOf(pEventType));
          builder.hostId(Host.getHostId(group.getId(), "host1.example.com", 27017));
          builder.hostnameAndPort("host1.example.com:27017");
          builder.hostTypeIds(Collections.singletonList(HostType.STANDALONE.getCode()));
          builder.replicaSetId("rs1");
          builder.clusterId(new ObjectId());
          baseBuilder = builder;
          break;
        }
      case HostMetricAlert.TYPE_NAME:
        {
          final HostMetricAlert.Builder builder = new HostMetricAlert.Builder(new ObjectId());
          builder.eventType(HostMetricEvent.Type.valueOf(pEventType));
          builder.hostId(Host.getHostId(group.getId(), "host1.example.com", 27017));
          builder.hostnameAndPort("host1.example.com:27017");
          builder.hostTypeIds(Collections.singletonList(HostType.STANDALONE.getCode()));
          builder.replicaSetId("rs1");
          builder.clusterId(new ObjectId());
          builder.metricThreshold(
              new MetricThreshold(
                  MetricThreshold.Mode.AVERAGE,
                  MetricType.valueOf(pMetricType),
                  MetricThreshold.Operator.LESS_THAN,
                  500.0,
                  Units.RAW));
          builder.currentValue(505.0);
          baseBuilder = builder;
          break;
        }
      case OrgAlert.TYPE_NAME:
        {
          final OrgAlert.Builder builder = new OrgAlert.Builder(new ObjectId());
          builder.eventType(OrgEvent.Type.valueOf(pEventType));
          builder.creditCardNumber("9999");
          baseBuilder = builder;
          break;
        }
      case ReplicaSetAlert.TYPE_NAME:
        {
          final ReplicaSetAlert.Builder builder = new ReplicaSetAlert.Builder(new ObjectId());
          builder.eventType(ReplicaSetEvent.Type.valueOf(pEventType));
          builder.replicaSetId("rs1");
          builder.clusterId(new ObjectId());
          builder.hostId(Host.getHostId(group.getId(), "host2.example.com", 27017));
          builder.hostnameAndPort("host2.example.com:27017");
          final Map<ReplicaSet.Member.State, Integer> currentState = new HashMap<>();
          currentState.put(ReplicaSet.Member.State.PRIMARY, 1);
          currentState.put(ReplicaSet.Member.State.SECONDARY, 2);
          builder.currentState(currentState);
          baseBuilder = builder;
          break;
        }
      case SystemBackupDaemonAlert.TYPE_NAME:
        {
          final SystemBackupDaemonAlert.Builder builder =
              new SystemBackupDaemonAlert.Builder(new ObjectId());
          final SystemBackupDaemonEvent.Type eventType =
              SystemBackupDaemonEvent.Type.valueOf(pEventType);
          builder.eventType(eventType);
          builder.machine("localhost");
          builder.head("/data/daemon");
          if (eventType == SystemBackupDaemonEvent.Type.LOW_HEAD_FREE_SPACE) {
            builder.headSpaceFree(100 * 1024 * 1024 * 1024L);
          }
          baseBuilder = builder;
          break;
        }
      case SystemBackupResizeAlert.TYPE_NAME:
        {
          final SystemBackupResizeAlert.Builder builder =
              new SystemBackupResizeAlert.Builder(new ObjectId());
          builder.eventType(SystemBackupResizeEvent.Type.valueOf(pEventType));
          builder.pitWindow((int) Duration.ofHours(5).getSeconds());
          builder.minOplogTtl((int) Duration.ofHours(10).getSeconds());
          builder.lastOplogPush(new Date());
          builder.headTime(new Date());
          builder.currentOplogTtl((int) Duration.ofHours(20).getSeconds());
          builder.newOplogTtl((int) Duration.ofHours(50).getSeconds());
          baseBuilder = builder;
          break;
        }
      case SystemBackupTheftAlert.TYPE_NAME:
        {
          final SystemBackupTheftAlert.Builder builder =
              new SystemBackupTheftAlert.Builder(new ObjectId());
          builder.eventType(SystemBackupTheftEvent.Type.valueOf(pEventType));
          builder.sourceDaemon("daemon1");
          builder.destDaemon("daemon2");
          builder.message("Theft failed!");
          baseBuilder = builder;
          break;
        }
      case SystemDatabaseProcessAlert.TYPE_NAME:
        {
          final SystemDatabaseProcessAlert.Builder builder =
              new SystemDatabaseProcessAlert.Builder(new ObjectId());
          final Type eventType = Type.valueOf(pEventType);
          builder.eventType(eventType);
          builder.dbNames(Arrays.asList("mmsdb", "mmsdbconfig"));
          builder.hostname("localhost");
          builder.port(27017);
          if (eventType == Type.BACKING_DATABASE_PROCESS_STARTUP_WARNINGS) {
            builder.startupWarnings(
                Arrays.asList("Something went wrong.", "Something else went wrong."));
          }
          baseBuilder = builder;
          break;
        }
      case SystemLogAlert.TYPE_NAME:
        {
          final SystemLogAlert.Builder builder = new SystemLogAlert.Builder(new ObjectId());
          final SystemLogEvent.Type eventType = SystemLogEvent.Type.valueOf(pEventType);
          builder.eventType(eventType);
          builder.loggerName("com.xgen.cloud.alerts.alert._public.svc.AlertSvc");
          baseBuilder = builder;
          break;
        }
      case UserAlert.TYPE_NAME:
        {
          final UserAlert.Builder builder = new UserAlert.Builder(new ObjectId());
          builder.eventType(UserEvent.Type.valueOf(pEventType));
          builder.targetUsername("<EMAIL>");
          baseBuilder = builder;
          break;
        }
      default:
        {
          return null;
        }
    }
    if (baseBuilder.build().getEventType().getInfo().getResolvingEventType() == null) {
      baseBuilder.status(Alert.Status.INFORMATIONAL);
    } else if (pStatus != null) {
      baseBuilder.status(pStatus);
    }
    final boolean isOrgAlert = pAlertType.equals(OrgAlert.TYPE_NAME);
    if (!baseBuilder.build().isSystemAlert()) {
      baseBuilder.orgId(org.getId());
      baseBuilder.orgName(org.getName());
      if (!isOrgAlert) {
        baseBuilder.groupId(group.getId());
        baseBuilder.groupName(group.getName());
      }
    }
    final Alert alert = baseBuilder.build();
    final List<Alert> alerts = Collections.singletonList(alert);
    final TemplateMap params =
        _alertTemplateGenerator.generateTemplateParameters(
            alert.isSystemAlert() ? null : org,
            alert.isSystemAlert() || isOrgAlert ? null : group,
            alerts,
            true);
    return getEmailNotifier()
        .generateHtmlEmail(
            new ObjectId(),
            "<EMAIL>",
            params,
            alert.isSystemAlert() ? null : org,
            alert.isSystemAlert() || isOrgAlert ? null : group,
            false,
            null,
            pVerbose);
  }

  @GET
  @Path("/lastPlanStartedDate")
  @UiCall(auth = false)
  public Response getLastPlanDate(@Context final HttpServletRequest pRequest) {
    final String lastPlanDate = _planDao.getLastPlanDate().map(TimeUtils::toISOString).orElse(null);

    return Response.ok().entity(lastPlanDate).build();
  }

  @GET
  @Path("/canQueryAppDb")
  @UiCall(auth = false)
  public Response canQueryApplicationDatabase(@Context final HttpServletRequest pRequest) {
    try {
      final MongoClient iamMongoClient = _mongoSvc.getMongo("iam");
      final MongoClient ndsClient = _mongoSvc.getMongo("nds");
      final MongoDatabase mmsdbconfig = iamMongoClient.getDatabase("mmsdbconfig");
      final MongoCollection<Document> mmsdbconfigCollection =
          mmsdbconfig.getCollection(mmsdbconfig.listCollectionNames().first());
      final MongoDatabase nds = ndsClient.getDatabase("nds");
      final MongoCollection<Document> ndsCollection =
          nds.getCollection(nds.listCollectionNames().first());

      mmsdbconfigCollection.find().limit(1).cursor().tryNext();
      ndsCollection.find().limit(1).cursor().tryNext();

      return Response.ok().build();
    } catch (final Exception pE) {
      return Response.serverError().entity(pE.getMessage()).build();
    }
  }

  @GET
  @Path("/sharedClusterVersion")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getSharedClusterVersion(@Context final HttpServletRequest pRequest) {
    final String defaultSharedClusterVersion =
        _appSettings.getStrProp("nds.instances.shared.version");
    final JSONObject response =
        new JSONObject().put("sharedClusterVersion", defaultSharedClusterVersion);
    return Response.ok().entity(response.toString()).build();
  }

  @GET
  @Path("/defaultTenantClusterVersions")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getDefaultTenantClusterVersions(@Context final HttpServletRequest pRequest) {
    final Map<String, String> tenantClusterVersions =
        Map.of(
            CloudProvider.FREE.name(), _appSettings.getStrProp("nds.instances.shared.version"),
            CloudProvider.SERVERLESS.name(), ServerlessNDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION,
            CloudProvider.FLEX.name(), _appSettings.getStrProp("nds.instances.flex.version"));
    final JSONObject response = new JSONObject(tenantClusterVersions);
    return Response.ok().entity(response.toString()).build();
  }

  @GET
  @Path("/latestVersion")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getLatestVersion(@Context final HttpServletRequest pRequest) {
    final VersionManifest versionManifest = _automationMongoDbVersionSvc.getAtlasVersionManifest();
    final VersionUtils.Version latestReleaseVersion =
        versionManifest.getCurrentLatestReleaseVersion();

    return Response.ok()
        .entity(
            new JSONObject()
                .put("latestVersion", latestReleaseVersion.getVersion())
                .put("latestMajorVersion", latestReleaseVersion.getMajorVersionString())
                .toString())
        .build();
  }

  @GET
  @Path("/previousLatestVersion")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getPreviousLatestVersion(@Context final HttpServletRequest pRequest) {
    final VersionManifest versionManifest = _automationMongoDbVersionSvc.getAtlasVersionManifest();
    final VersionUtils.Version latestReleaseVersion =
        versionManifest.getCurrentLatestReleaseVersion();
    final VersionUtils.Version previousLatestVersion =
        versionManifest.getPreviousLatestVersion(latestReleaseVersion);

    if (previousLatestVersion == null) {
      LOG.warn(
          "Failed to determine previous latest version from latest release version: {}",
          latestReleaseVersion.getVersion());
      return Response.serverError().build();
    }

    return Response.ok()
        .entity(
            new JSONObject()
                .put("previousLatestVersion", previousLatestVersion.getVersion())
                .put("previousLatestMajorVersion", previousLatestVersion.getMajorVersionString())
                .toString())
        .build();
  }

  @GET
  @Path("/latestVersionForMajor/{majorVersion}")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getLatestVersionForMajor(
      @Context final HttpServletRequest pRequest,
      @PathParam("majorVersion") final String pMajorVersion) {
    final VersionManifest versionManifest = _automationMongoDbVersionSvc.getAtlasVersionManifest();

    final Version latestVersion = versionManifest.getCurrentLatestReleaseVersion(pMajorVersion);

    return Response.ok()
        .entity(
            new JSONObject()
                .put("version", latestVersion.getVersion())
                .put("majorVersion", latestVersion.getMajorVersionString())
                .toString())
        .build();
  }

  @GET
  @Path("/precedingVersion/{referenceVersion}")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getPrecedingVersion(
      @Context final HttpServletRequest pRequest,
      @PathParam("referenceVersion") final String pReferenceVersion) {
    final VersionManifest versionManifest = _automationMongoDbVersionSvc.getAtlasVersionManifest();
    final VersionUtils.Version referenceVersion = VersionUtils.parse(pReferenceVersion);
    final VersionUtils.Version precedingVersion =
        versionManifest.getPreviousLatestVersion(referenceVersion);

    if (precedingVersion == null) {
      LOG.warn(
          "Failed to determine the preceding version from the given reference version: {}",
          referenceVersion.getVersion());
      return Response.serverError().build();
    }

    return Response.ok()
        .entity(
            new JSONObject()
                .put("referenceVersion", referenceVersion.getVersion())
                .put("precedingVersion", precedingVersion.getVersion())
                .put("precedingMajorVersion", precedingVersion.getMajorVersionString())
                .toString())
        .build();
  }

  @GET
  @Path("/{groupId}/serverlessLoadBalancingDeployment/isProvisioned")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response isServerlessLoadBalancingDeploymentProvisioned(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId)
      throws Exception {
    final List<ServerlessLoadBalancingDeployment> deployments =
        _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(pGroupId);

    if (deployments.isEmpty()) {
      throw new SvcException(NDSErrorCode.INVALID_SERVERLESS_LOAD_BALANCING_DEPLOYMENT);
    }

    final boolean deploymentsProvisioned =
        deployments.stream().anyMatch(ServerlessLoadBalancingDeployment::isProvisioned);

    return Response.ok(new JSONObject().put("isProvisioned", deploymentsProvisioned).toString())
        .build();
  }

  @PUT
  @Path("/vercel/createIntegration")
  @UiCall(auth = false)
  public Response createIntegration(
      @Context final HttpServletRequest request,
      @QueryParam("email") final String email,
      @QueryParam("userId") final String userId,
      @QueryParam("teamId") final String teamId,
      @QueryParam("teamName") final String teamName) {
    final VercelIntegration vercelIntegration =
        new VercelIntegration(email, userId, "fakeAccessToken", "fakeConfigId", teamId, teamName);
    _vercelIntegrationDao.upsertIntegration(vercelIntegration);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @DELETE
  @Path("/vercel/deleteIntegration")
  @UiCall(auth = false)
  public Response deleteIntegration(
      @Context final HttpServletRequest request, @QueryParam("userId") final String userId) {
    _vercelIntegrationDao.removeById(userId);
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  private JSONObject getApplicationDatabaseNamespaceCounts(final MongoClient pMongoClient) {
    final JSONObject namespaceCounts = new JSONObject();

    pMongoClient
        .listDatabaseNames()
        .iterator()
        .forEachRemaining(
            databaseName -> {
              if (databaseName.equals("local")) {
                return;
              }
              final MongoDatabase database = pMongoClient.getDatabase(databaseName);
              database
                  .listCollectionNames()
                  .iterator()
                  .forEachRemaining(
                      collectionName -> {
                        if (collectionName.startsWith("system.")) {
                          return;
                        }
                        final MongoCollection<Document> collection =
                            database.getCollection(collectionName);
                        try {
                          namespaceCounts.put(
                              collection.getNamespace().getFullName(),
                              collection.estimatedDocumentCount());
                        } catch (final JSONException ignored) {
                        }
                      });
            });

    return namespaceCounts;
  }

  private Response translateAuthErrorCode(
      final HttpServletRequest pRequest,
      final String pUsername,
      final String userAddr,
      final ErrorCode authResponse) {
    if (authResponse == AppUserErrorCode.INVALID_PASSWORD_TOO_SHORT
        || authResponse == AppUserErrorCode.INVALID_PASSWORD_EXPIRED
        || authResponse == AppUserErrorCode.PASSWORD_IS_EMPTY) {
      return SimpleApiResponse.non500Error(authResponse)
          .resource(_userLoginSvc.createPasswordResetTokenPath(pUsername, userAddr))
          .build();
    }

    return SimpleApiResponse.non500Error(authResponse).resource(pRequest.getRequestURI()).build();
  }

  @DELETE
  @Path("/nds/deleteOrphans")
  @UiCall(auth = false)
  public Response deleteOrphans(@Context final HttpServletRequest pRequest) throws SvcException {
    _ndsOrphanedItemSvc.cleanItems();
    return Response.ok().entity(EMPTY_JSON_OBJECT).build();
  }

  @GET
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/nodeType/{nodeType}")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getHostnameGivenNodeType(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("nodeType") final String pNodeType,
      @QueryParam("sharded") final boolean pIsSharded,
      @QueryParam("instanceSize") final String pInstanceSize) {
    final List<ReplicaSetHardware> replicaSetHardwareList =
        _replicaSetHardwareSvc.getReplicaSetHardware(pGroupId, pClusterName).stream()
            .filter(
                rsh ->
                    pInstanceSize == null
                        || rsh.getHardware()
                            .get(0)
                            .getInstanceSize()
                            .orElseThrow()
                            .equals(pInstanceSize))
            .toList();

    // get primary hostname
    if (pNodeType.equals("base")) {
      final List<String> primaryHostnames =
          _ndsClusterSvc.getClusterHostsFromHardware(replicaSetHardwareList).stream()
              .filter(Host::getIsPrimary)
              .filter(host -> !host.isDedicatedConfigServer())
              .map(Host::getFullHostname)
              .collect(Collectors.toList());
      if (primaryHostnames.size() > 1 & !pIsSharded) {
        return Response.status(Response.Status.NOT_FOUND)
            .entity(
                String.format(
                    "ERROR: found multiple primaries - groupId: %s, clusterName: %s",
                    pGroupId, pClusterName))
            .build();
      } else if (primaryHostnames.size() == 0) {
        return Response.status(Response.Status.NOT_FOUND)
            .entity(
                String.format(
                    "ERROR: found no primaries - groupId: %s, clusterName: %s",
                    pGroupId, pClusterName))
            .build();
      } else {
        final String primaryHostname = primaryHostnames.get(0).split(":")[0];
        return Response.ok(new JSONObject().put("hostname", primaryHostname).toString()).build();
      }
    } else if (pNodeType.equals("secondary")) {

      final Optional<ClusterDescription> clusterDescription =
          _clusterDescriptionDao.findByName(pGroupId, pClusterName);
      final Optional<String> analyticsHostname =
          _ndsClusterSvc.getAnyAnalyticsNodeHostname(clusterDescription.get());

      final List<String> secondaryHostnames =
          _ndsClusterSvc.getClusterHostsFromHardware(replicaSetHardwareList).stream()
              .filter(Host::getIsSecondary)
              .filter(host -> !host.isDedicatedConfigServer())
              .map(Host::getFullHostname)
              .filter(name -> !name.contains(analyticsHostname.orElse("none")))
              .collect(Collectors.toList());

      final String secondaryHostname = secondaryHostnames.get(0).split(":")[0];
      return Response.ok(new JSONObject().put("hostname", secondaryHostname).toString()).build();
    } else {
      final Optional<ClusterDescription> clusterDescription =
          _clusterDescriptionDao.findByName(pGroupId, pClusterName);
      if (clusterDescription.isEmpty()) {
        return Response.status(Response.Status.NOT_FOUND)
            .entity(
                String.format(
                    "Unable to find cluster description for {groupId: %s, clusterName: %s}",
                    pGroupId, pClusterName))
            .build();
      }
      final Optional<String> analyticsHostname =
          _ndsClusterSvc.getAnyAnalyticsNodeHostname(clusterDescription.get());
      if (analyticsHostname.isEmpty()) {
        return Response.status(Response.Status.NOT_FOUND)
            .entity(
                String.format(
                    "ERROR: no analytics nodes found - groupId: %s, clusterName: %s",
                    pGroupId, pClusterName))
            .build();
      } else {
        return Response.ok(
                new JSONObject().put("hostname", analyticsHostname.get().split(":")[0]).toString())
            .build();
      }
    }
  }

  @GET
  @Path(
      "/nds/groups/{groupId}/clusters/{clusterName}/cloudProvider/{cloudProvider}/region/{region}/hostnames")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getHostnameGivenRegion(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("cloudProvider") final String pCloudProvider,
      @PathParam("region") final String pRegion) {

    final Optional<RegionName> regionName =
        RegionNameHelper.findByName(CloudProvider.valueOf(pCloudProvider), pRegion);

    LOG.debug("regionName: {}", regionName.get());

    if (regionName.isEmpty()) {
      return Response.status(Status.BAD_REQUEST)
          .entity(
              String.format(
                  "ERROR: region name does not exist - cloudProvider: %s, regionName: %s",
                  pCloudProvider, pRegion))
          .build();
    }

    final Optional<ClusterDescription> clusterDescriptionOpt =
        _clusterDescriptionDao.findByName(pGroupId, pClusterName, false);

    if (clusterDescriptionOpt.isEmpty()) {
      return Response.status(Status.BAD_REQUEST)
          .entity(
              String.format(
                  "ERROR: cluster not found - groupId: %s, clusterName: %s",
                  pGroupId, pClusterName))
          .build();
    }

    final ClusterDescription clusterDescription = clusterDescriptionOpt.get();

    final List<String> hostnamesInRegion = new ArrayList<>();

    // Handle both standalone replica sets and sharded clusters
    final List<ReplicaSetHardware> replicaSetHardwares =
        _replicaSetHardwareSvc.getReplicaSetHardware(pGroupId, pClusterName);

    if (replicaSetHardwares.size() == 0) {
      return Response.status(Status.NOT_FOUND)
          .entity(
              String.format(
                  "ERROR: no replica set hardwares found - groupId: %s, clusterName: %s",
                  pGroupId, pClusterName))
          .build();
    }

    replicaSetHardwares.forEach(
        rs -> {
          hostnamesInRegion.addAll(
              addHostnamesInRegion(
                  rs,
                  regionName.get(),
                  clusterDescription
                      .getReplicationSpecById(rs.getReplicationSpecId())
                      .get()
                      .getRegionConfigs()));
        });

    return Response.ok(
            new JSONObject().put("hostnames", new JSONArray(hostnamesInRegion)).toString())
        .build();
  }

  private List<String> addHostnamesInRegion(
      final ReplicaSetHardware replicaSetHardware,
      final RegionName targetRegion,
      final List<RegionConfig> regionConfigs) {

    // Create map of instance ID to region
    final Map<ObjectId, RegionName> instanceIdToRegionMap =
        replicaSetHardware.getInstanceIdToRegionNameMap(regionConfigs);

    // Filter instances by region using the map and add hostnames
    final List<String> hostnames =
        replicaSetHardware
            .getAllHardware()
            .filter(ih -> targetRegion.equals(instanceIdToRegionMap.get(ih.getInstanceId())))
            .filter(ih -> ih.getHostnameForAgents().isPresent())
            .map(ih -> ih.getHostnameForAgents().get())
            .collect(Collectors.toList());

    LOG.debug(
        String.format(
            "shardId %s, regionName: %s, instanceIdToRegionMap:%s",
            replicaSetHardware.getRsId(), targetRegion, instanceIdToRegionMap));
    return hostnames;
  }

  @POST
  @Path("/nds/groups/{groupId}/tagResourceGroupDNR")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response tagAzureResourceGroupDnrForGroup(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("purpose") final String pPurpose) {
    final NDSGroup ndsGroup =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format("NDSGroup with groupId %s not found.", pGroupId)));

    final AzureCloudProviderContainer container =
        ndsGroup
            .getCloudProviderContainerByType(CloudProvider.AZURE)
            .map(AzureCloudProviderContainer.class::cast)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format(
                            "NDSGroup with groupId %s does not have an Azure container.",
                            pGroupId)));

    final String resourceGroupName =
        container
            .getResourceGroupName()
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format(
                            "Azure container with id %s does not have an Azure container with a"
                                + " resource group.",
                            container.getId())));

    LOG.info("Tagging resource group {} in NDS group as do not reap.", resourceGroupName);

    _azureApiSvc.tagResourceGroup(
        container.getAzureSubscriptionId(),
        resourceGroupName,
        Map.of("dnr", "true", "purpose", pPurpose),
        LOG);

    return Response.ok().build();
  }

  @GET
  @Path("/envoyInstances/{groupId}/sshPrivateKeyAndPassphrase")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getEnvoySSHPrivateKeyAndPassphrase(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    final String envoySSHPrivateKey =
        _serverlessEnvoySSHKeysSvc.getDecryptedEnvoyPrivateSshKey(pGroupId).orElse(null);
    final String envoySSHPassphrase =
        _serverlessEnvoySSHKeysSvc.getDecryptedSshKeyPassphrase(pGroupId);

    return Response.ok(
            new JSONObject()
                .put("envoySSHPrivateKey", Optional.ofNullable(envoySSHPrivateKey).orElse(""))
                .put("envoySSHPassphrase", Optional.ofNullable(envoySSHPassphrase).orElse(""))
                .toString())
        .build();
  }

  @GET
  @Path("/nds/{groupId}/aws/{awsRegionName}/privateEndpoint/{endpointId}/privateLinkHostname")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getPrivateEndpointHostnameByEndpointId(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("endpointId") final String pEndpointId,
      @PathParam("awsRegionName") final String pRegionName)
      throws SvcException {

    final AWSRegionName regionName =
        AWSRegionName.findByName(pRegionName)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format("Illegal region name: %s", pRegionName)));
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(pGroupId);

    final Optional<AWSCloudProviderContainer> awsContainer =
        ndsGroup.getCloudProviderContainers().stream()
            .filter(container -> container.getCloudProvider().equals(CloudProvider.AWS))
            .map(AWSCloudProviderContainer.class::cast)
            .filter(container -> container.getRegion().equals(regionName))
            .findFirst();
    if (awsContainer.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "Unable to find container for region %s in group ID %s.", pRegionName, pGroupId))
          .build();
    }

    final Optional<AWSPrivateLinkInterfaceEndpoint> privateEndpoint =
        awsContainer.stream()
            .map(AWSCloudProviderContainer::getEndpointService)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .flatMap(endpointService -> endpointService.getEndpoints().stream())
            .filter(endpoint -> endpoint.getEndpointId().equals(pEndpointId))
            .findFirst();
    if (privateEndpoint.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "Unable to find endpoint for endpoint ID %s in group ID %s.",
                  pEndpointId, pGroupId))
          .build();
    }

    final Optional<String> privateLinkHostname = privateEndpoint.get().getPrivateLinkHostname();

    if (privateLinkHostname.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "Unable to find privateLinkHostname for endpoint ID %s in group ID %s.",
                  pEndpointId, pGroupId))
          .build();
    }

    final JSONObject response = new JSONObject();
    response.put("privateLinkHostname", privateEndpoint.get().getPrivateLinkHostname().get());

    return Response.ok(response.toString()).build();
  }

  @GET
  @Path(
      "/nds/{groupId}/aws/{awsRegionName}/privateEndpoint/{clusterName}/targetGroupArnForOptimizedPrivateEndpoint")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getPrivateEndpointMultiTargetGroupArn(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("awsRegionName") final String pRegionName,
      @PathParam("clusterName") final String pClusterName)
      throws SvcException {

    final AWSRegionName regionName =
        AWSRegionName.findByName(pRegionName)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format("Illegal region name: %s", pRegionName)));
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(pGroupId);

    final Optional<AWSCloudProviderContainer> awsContainer =
        ndsGroup.getCloudProviderContainers().stream()
            .map(AWSCloudProviderContainer.class::cast)
            .filter(container -> container.getRegion().equals(regionName))
            .findFirst();
    if (awsContainer.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "Unable to find container for region %s in group ID %s.", pRegionName, pGroupId))
          .build();
    }

    final Optional<AWSMultiTargetConnectionRule> multiTargetRuleForContainerOpt =
        _awsMultiTargetConnectionRuleDao.findOneMultiTargetConnectionRule(
            pGroupId,
            pClusterName,
            NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
            awsContainer.get().getId());
    if (multiTargetRuleForContainerOpt.isEmpty()) {
      return Response.status(Response.Status.NOT_FOUND)
          .entity(
              String.format(
                  "Unable to find multi-target connection rule for region %s, group ID %s,"
                      + " container ID %s, cluster name %s.",
                  pRegionName, pGroupId, awsContainer.get().getId(), pClusterName))
          .build();
    }

    final AWSMultiTargetConnectionRule multiTargetRuleForContainer =
        multiTargetRuleForContainerOpt.get();

    LOG.debug(
        "Serializing metadata for multi-target connection rule with target group ARN {}, listener"
            + " ARN {}, frontend port {}, instance IDs {}, and deleted date {}",
        multiTargetRuleForContainer.getTargetGroupArn(),
        multiTargetRuleForContainer.getListenerArn(),
        multiTargetRuleForContainer.getReservedPortNumber(),
        multiTargetRuleForContainer.getInstanceIds(),
        multiTargetRuleForContainer.getDeletedDate());
    final JSONObject targetGroupARNAndPortJson = new JSONObject();
    targetGroupARNAndPortJson.put(
        "targetGroupARN", multiTargetRuleForContainer.getTargetGroupArn());
    targetGroupARNAndPortJson.put("port", multiTargetRuleForContainer.getReservedPortNumber());

    return Response.ok(targetGroupARNAndPortJson.toString()).build();
  }

  @GET
  @Path("/nds/serverless/serverlessProxy/{groupId}/allProvisioned")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response allServerlessProxyProvisioned(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final String pGroupId) {
    final JSONObject response = new JSONObject();

    final List<EnvoyInstance> allEnvoyInstances =
        _serverlessLoadBalancingDeploymentDao
            .findDeploymentsByGroupId(new ObjectId(pGroupId))
            .stream()
            .flatMap(deployment -> deployment.getEnvoyInstances().stream())
            .collect(Collectors.toList());

    final boolean allProvisioned =
        allEnvoyInstances.stream().allMatch(EnvoyInstance::isProvisioned);

    response.put("allProvisioned", allProvisioned);
    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/nds/serverless/serverlessProxy/{groupId}/allHaveMetrics")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response allServerlessProxyNodesHaveMetrics(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final String pGroupId) {
    final JSONObject response = new JSONObject();

    final Map<ObjectId, List<EnvoyInstance>> allEnvoyInstances =
        _serverlessLoadBalancingDeploymentDao
            .findDeploymentsByGroupId(new ObjectId(pGroupId))
            .stream()
            .collect(
                Collectors.toMap(
                    ServerlessLoadBalancingDeployment::getId,
                    ServerlessLoadBalancingDeployment::getEnvoyInstances));

    final boolean allHasMetrics =
        allEnvoyInstances.entrySet().stream()
            .allMatch(
                entry ->
                    entry.getValue().stream()
                        .allMatch(
                            envoyInstance ->
                                _serverlessProxyMetricsSvc
                                    .findServerlessProxyMetrics(
                                        envoyInstance.getId(), entry.getKey())
                                    .isPresent()));

    response.put("allHasMetrics", allHasMetrics);
    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/getFirstAwsRegion")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response getFirstAwsRegion(@Context final HttpServletRequest pRequest) {
    final String prop = System.getProperty("nds.cukes.defaultRegion", null);
    final String defaultRegion =
        Optional.ofNullable(prop)
            .filter(region -> region.length() > 0)
            .orElse(AWSRegionName.US_EAST_2.name());

    final JSONObject response = new JSONObject();
    response.put("regionName", defaultRegion);
    return Response.ok(response.toString()).build();
  }

  @GET
  @Path("/getNdsOktaTestAuthorizationServerApiKey")
  @UiCall(auth = false)
  @Produces(MediaType.TEXT_PLAIN)
  public Response getNdsOktaTestAuthorizationServerApiKey(
      @Context final HttpServletRequest pRequest) {
    return Response.ok(_appSettings.getNDSOktaTestOIDCAuthorizationServerApiKey()).build();
  }

  @POST
  @Path("/nds/{groupId}/refreshJwks")
  @UiCall(auth = false)
  public Response refreshJwksForGroup(
      @Context final HttpServletRequest pRequest, @PathParam("groupId") final ObjectId pGroupId) {
    _ndsGroupSvc.setNeedsJWKSRefreshNow(pGroupId);
    _ndsGroupSvc.setPlanningNow(pGroupId);
    return Response.ok().build();
  }

  @GET
  @Path("/nds/{groupId}/liveImport/{liveImportId}/pods")
  @UiCall(auth = false)
  public Response getLiveImportPods(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("liveImportId") final ObjectId pLiveImportId) {
    final String kubeNamespace = getKubeNamespace(_appSettings.getAppEnv());
    final Optional<LiveImport> liveImportOpt = _liveImportDao.find(pLiveImportId);
    if (liveImportOpt.isEmpty()) {
      LOG.warn("Live import with id={} was not found", pLiveImportId);
      return Response.status(Status.NOT_FOUND).build();
    }

    final LiveImport liveImport = liveImportOpt.get();
    final MigrationToolType toolType = liveImport.getMigrationToolType();
    final String baseJobName =
        String.format(
            "%s-job-%s",
            liveImport.getMigrationToolType().toString().toLowerCase(), liveImport.getId());
    final List<String> kubeJobNames = new ArrayList<>();

    if (toolType == MigrationToolType.MONGOSYNC) {
      kubeJobNames.add(baseJobName);
    } else { // mongomirror job name has shard index attached
      IntStream.range(0, liveImport.getMigrationStatuses().size())
          .forEach(idx -> kubeJobNames.add(String.format("%s-%d", baseJobName, idx)));
    }

    LOG.info("Filtering live import pods by the following kube job labels: " + kubeJobNames);

    try (final KubernetesClient kubeClient = getKubeClient()) {
      final List<Pod> podList =
          kubeClient
              .pods()
              .inNamespace(kubeNamespace)
              .withLabelIn("job-name", kubeJobNames.toArray(new String[] {}))
              .list()
              .getItems();
      return Response.ok().entity(podList).build();
    } catch (Exception pE) {
      LOG.warn("Failed to retrieve pods for liveImportId={}", pLiveImportId, pE);
      return Response.serverError().build();
    }
  }

  @GET
  @Path("/nds/{groupId}/liveImport/{liveImportId}/pids")
  @UiCall(auth = false)
  public Response getLiveImportPids(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("liveImportId") final ObjectId pLiveImportId) {
    final String processOwner = System.getProperty("user.name");

    final List<Long> pids = new ArrayList<>();
    try (ModelCursor<ReservedPort> cursor =
        _reservedPortDao.findAllReservedFor(pLiveImportId.toString())) {
      while (cursor.hasNext()) {
        final ReservedPort reservedPort = cursor.next();
        final Optional<Long> optionalPid =
            _defaultMongosyncProcessUtility.getPidFromPsOutput(
                processOwner, pLiveImportId.toString(), reservedPort.getPort());
        if (optionalPid.isEmpty()) {
          LOG.warn(
              "Failed to find pid for liveImportId={} reservedPort={}, skipping..",
              pLiveImportId,
              reservedPort.getPort());
          continue;
        }
        pids.add(optionalPid.get());
      }
    } catch (Exception pE) {
      LOG.warn("Failed to retrieve pids for liveImportId={}", pLiveImportId, pE);
      return Response.serverError().build();
    }
    return Response.ok().entity(pids).build();
  }

  @DELETE
  @Path("/nds/{groupId}/liveImport/{liveImportId}/pids")
  @UiCall(auth = false)
  public Response killLiveImportPids(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("liveImportId") final ObjectId pLiveImportId) {
    final String processOwner = System.getProperty("user.name");

    try (ModelCursor<ReservedPort> cursor =
        _reservedPortDao.findAllReservedFor(pLiveImportId.toString())) {
      while (cursor.hasNext()) {
        final ReservedPort reservedPort = cursor.next();
        final Optional<Long> optionalPid =
            _defaultMongosyncProcessUtility.getPidFromPsOutput(
                processOwner, pLiveImportId.toString(), reservedPort.getPort());
        if (optionalPid.isEmpty()) {
          LOG.warn(
              "Failed to find pid for liveImportId={} reservedPort={}, skipping..",
              pLiveImportId,
              reservedPort.getPort());
          continue;
        }
        final int exitCode = _defaultMongosyncProcessUtility.killProcessByPid(optionalPid.get());
        if (exitCode != 0) {
          LOG.error(
              "Failed to kill pid for liveImportId={} pid={}", pLiveImportId, optionalPid.get());
          throw new SvcException(NDSErrorCode.INTERNAL);
        }
        LOG.info(
            "Successfully killed pid for liveImportId={} pid={}", pLiveImportId, optionalPid.get());
      }
    } catch (Exception pE) {
      LOG.warn("Failed to delete pids for liveImportId={}", pLiveImportId, pE);
      return Response.serverError().build();
    }
    return Response.ok().build();
  }

  @POST
  @Path("processExperimentUpdates")
  @UiCall(auth = false)
  public Response updateExperimentConfigs() {
    _experimentsSvc.processExperimentUpdates();
    return Response.ok().build();
  }

  @GET
  @Path("/setupServerlessMTMGroupName/{cloudProvider}/{regionName}/{index}")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getSetupServerlessMTMGroupName(
      @Context final HttpServletRequest pRequest,
      @PathParam("cloudProvider") final String pCloudProvider,
      @PathParam("regionName") final String pRegionName,
      @PathParam("index") final int pIndex) {
    final CloudProvider provider = CloudProvider.valueOf(pCloudProvider);
    final RegionName regionName =
        RegionNameHelper.findByName(provider, pRegionName)
            .orElseThrow(
                () -> ApiErrorCode.INVALID_REGION.exception(false, pRegionName, provider.name()));
    final String mtmGroupName =
        _ndsAutoScaleServerlessMTMCapacitySvc.getServerlessGroupName(regionName, pIndex);
    return Response.ok(mtmGroupName).build();
  }

  @GET
  @Path("/clusterDescription/{tenantGroupId}/{tenantClusterName}/loadBalancedHostname")
  @UiCall(auth = false)
  @Produces({MediaType.APPLICATION_JSON})
  public Response getClusterDescriptionLoadBalancedHostname(
      @Context final HttpServletRequest pRequest,
      @PathParam("tenantGroupId") final ObjectId pTenantGroupId,
      @PathParam("tenantClusterName") final String pTenantClusterName) {
    final ClusterDescription clusterDescription =
        _clusterDescriptionDao.findByName(pTenantGroupId, pTenantClusterName).orElseThrow();
    return Response.ok(clusterDescription.getLoadBalancedHostname().orElseThrow()).build();
  }

  @PATCH
  @Path("/user/mfa")
  @Produces({MediaType.APPLICATION_JSON})
  @Consumes({MediaType.APPLICATION_JSON})
  @InternalTestUsersOnly
  @UiCall(
      roles = RoleSet.ANY_AUTHENTICATED_USER,
      groupSource = UiCall.GroupSource.NONE,
      maxLastAuthMins = 10,
      bypassMfa = true)
  public Response patchMultiFactorAuth(
      @Context final HttpServletRequest pRequest,
      @Context final AppUser pUser,
      @Context final AuditInfo pAuditInfo,
      final MultiFactorAuthSettings pAuthSettings)
      throws SvcException {
    if (!pUser.hasMultiFactorAuth() && _appSettings.isAccountMultiFactorAuthEnabled()) {
      throw new SvcException(MultiFactorAuthErrorCode.TWO_FACTOR_AUTH_DEPRECATED);
    }

    final var uiAuthCode = _userLoginSvc.getUiAuthCode(pRequest);
    if (pAuthSettings.getGoogleAuthenticator() || pAuthSettings.hasPhone()) {
      if (pAuthSettings.getGoogleAuthenticator()) {
        _multiFactorAuthSvc.setGoogleAuthFactor(pUser, pAuthSettings.getAuthCode(), pAuditInfo);
      } else {
        _multiFactorAuthSvc.setPhoneAuthFactor(
            pUser,
            pAuthSettings.getPhone(),
            pAuthSettings.getExtension(),
            pAuthSettings.getVoice(),
            pAuthSettings.getAuthCode(),
            pAuditInfo);
      }

      /* When user sets up MFA primary method, we immediately flag their current
      session as MFA validated, otherwise subsequent requests would redirect
      to MFA verification page */
      _userSvc.updateUiAuthCodeMfaValidated(uiAuthCode);
      _userSvc.updateLastAuthDate(pUser.getId(), uiAuthCode);
    }

    if (pAuthSettings.hasBackupPhone()) {
      _multiFactorAuthSvc.setBackup(
          pUser, pAuthSettings.getBackupPhone(), pAuthSettings.getBackupPhoneExtension());
    }

    return SimpleApiResponse.ok().build();
  }

  @PATCH
  @Path("/group/{groupId}/setcohort")
  @UiCall(auth = false)
  public Response setCohort(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @FormParam("version") @DefaultValue("0") final int pVersionCohort,
      @FormParam("parameter") @DefaultValue("0") final int pParameterCohort) {

    getNdsGroupDao()
        .overrideReleaseCohorts(pGroupId, new ReleaseCohorts(pVersionCohort, pParameterCohort));
    return Response.ok().build();
  }

  @GET
  @Path("/serverlessMTM/{mtmGroupId}/{mtmClusterName}/sentinel")
  @UiCall(auth = false)
  public Response getSentinelInformation(
      @Context final HttpServletRequest pRequest,
      @PathParam("mtmGroupId") final ObjectId pGroupId,
      @PathParam("mtmClusterName") final String pMTMClusterName) {
    final ServerlessMTMCluster mtmCluster =
        _serverlessMTMClusterDao.findServerlessByName(pGroupId, pMTMClusterName).orElseThrow();

    if (mtmCluster.getSentinel().isProvisioned()) {
      return Response.ok(
              new JSONObject()
                  .put("sentinelProvisioned", true)
                  .put("groupId", mtmCluster.getSentinel().getSentinelInstance().getGroupId())
                  .put(
                      "instanceName",
                      mtmCluster.getSentinel().getSentinelInstance().getInstanceName())
                  .toString())
          .build();
    }

    return Response.ok(new JSONObject().put("sentinelProvisioned", false).toString()).build();
  }

  @GET
  @Path("/flexMTM/{mtmGroupId}/{mtmClusterName}/sentinel")
  @UiCall(auth = false)
  public Response getFlexSentinelInformation(
      @Context final HttpServletRequest pRequest,
      @PathParam("mtmGroupId") final ObjectId pGroupId,
      @PathParam("mtmClusterName") final String pMTMClusterName) {
    final FlexMTMCluster mtmCluster =
        _mtmClusterDao.findByNameAndMTMType(pGroupId, pMTMClusterName, MTMClusterType.FLEX).stream()
            .map(FlexMTMCluster.class::cast)
            .findFirst()
            .orElseThrow();
    if (mtmCluster.getSentinel().isProvisioned()) {
      return Response.ok(
              new JSONObject()
                  .put("sentinelProvisioned", true)
                  .put("groupId", mtmCluster.getSentinel().getSentinelInstance().getGroupId())
                  .put(
                      "instanceName",
                      mtmCluster.getSentinel().getSentinelInstance().getInstanceName())
                  .put("srvAddress", mtmCluster.getSentinel().getSrvAddress().orElse(null))
                  .toString())
          .build();
    }

    return Response.ok(new JSONObject().put("sentinelProvisioned", false).toString()).build();
  }

  @POST
  @Path("/serverless/{mtmGroupId}/enableEnvoyBypass")
  @UiCall(auth = false)
  public Response enableBypassEnvoyForServerlessPool(
      @Context final HttpServletRequest pRequest,
      @PathParam("mtmGroupId") final ObjectId pGroupId) {
    final ServerlessMTMPool serverlessMTMPool =
        _serverlessMTMPoolSvc
            .findPoolByGroupId(pGroupId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format("No serverless pool found for group id %s", pGroupId)));

    _serverlessMTMPoolSvc.setEnvoyBypassRequestedDate(serverlessMTMPool.getId());
    _ndsGroupDao.setPlanASAP(pGroupId);

    return Response.ok().build();
  }

  @POST
  @Path("/serverless/{mtmGroupId}/disableEnvoyBypass")
  @UiCall(auth = false)
  public Response disableBypassEnvoyForServerlessPool(
      @Context final HttpServletRequest pRequest,
      @PathParam("mtmGroupId") final ObjectId pGroupId) {
    final ServerlessMTMPool serverlessMTMPool =
        _serverlessMTMPoolSvc
            .findPoolByGroupId(pGroupId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format("No serverless pool found for group id %s", pGroupId)));

    _serverlessMTMPoolSvc.unsetEnvoyBypassRequestedDate(serverlessMTMPool.getId());
    _serverlessMTMPoolSvc.setEnvoyBypassRollbackRequestedForGroup(pGroupId, new Date());
    _ndsGroupDao.setPlanASAP(pGroupId);

    return Response.ok().build();
  }

  @GET
  @Path("/serverless/{mtmGroupId}/envoyBypassNetworkOpened")
  @UiCall(auth = false)
  public Response getIsEnvoyBypassNetworkOpened(
      @Context final HttpServletRequest pRequest,
      @PathParam("mtmGroupId") final ObjectId pGroupId) {
    final ServerlessMTMPool serverlessMTMPool =
        _serverlessMTMPoolSvc
            .findPoolByGroupId(pGroupId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format("No serverless pool found for group id %s", pGroupId)));

    final boolean isEnvoyBypassNetworkOpened =
        serverlessMTMPool.getEnvoyBypassNetworkingChangesCompletedDate() != null;

    return Response.ok(
            new JSONObject().put("envoyBypassNetworkOpened", isEnvoyBypassNetworkOpened).toString())
        .build();
  }

  @POST
  @Path("/serverless/{mtmGroupId}/groupEnvoyBypassAllTenants")
  public Response enableBypassEnvoyForAllTenants(
      @Context final HttpServletRequest pRequest, @PathParam("mtmGroupId") final ObjectId pGroupId)
      throws SvcException {
    final ServerlessMTMPool serverlessMTMPool =
        _serverlessMTMPoolSvc
            .findPoolByGroupId(pGroupId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format("No serverless pool found for group id %s", pGroupId)));

    final List<ObjectId> tenantIds = serverlessMTMPool.getLinkedTenantIds();
    for (ObjectId tenantUid : tenantIds) {
      if (!serverlessMTMPool.getEnvoyBypassTenantIds().contains(tenantUid)) {
        _serverlessMTMPoolSvc.addServerlessTenantEnvoyBypass(serverlessMTMPool.getId(), tenantUid);
        _ndsClusterSvc.setNeedsPublishDateForClusterByUniqueId(null, tenantUid);
      }
    }
    _ndsGroupDao.setPlanASAP(pGroupId);
    return Response.ok().build();
  }

  @POST
  @Path("/serverless/{mtmGroupId}/groupDisableEnvoyBypassAllTenants")
  public Response disableBypassEnvoyForAllTenants(
      @Context final HttpServletRequest pRequest, @PathParam("mtmGroupId") final ObjectId pGroupId)
      throws SvcException {
    final ServerlessMTMPool serverlessMTMPool =
        _serverlessMTMPoolSvc
            .findPoolByGroupId(pGroupId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        String.format("No serverless pool found for group id %s", pGroupId)));

    final List<ObjectId> tenantIds = serverlessMTMPool.getLinkedTenantIds();
    for (ObjectId tenantUid : tenantIds) {
      if (serverlessMTMPool.getEnvoyBypassTenantIds().contains(tenantUid)) {
        _serverlessMTMPoolSvc.removeServerlessTenantEnvoyBypass(
            serverlessMTMPool.getId(), tenantUid);
        _ndsClusterSvc.setNeedsPublishDateForClusterByUniqueId(null, tenantUid);
      }
    }
    _ndsGroupDao.setPlanASAP(pGroupId);
    return Response.ok().build();
  }

  @POST
  @Path("/dataPlaneAccessRequests/createTestTicket")
  @UiCall(auth = false)
  public Response createTestTicket(@Context final HttpServletRequest pRequest) {
    final DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
    final String date = dateFormat.format(new Date());

    // Issue type 3 indicates task.
    final IssueInput issueInput =
        new IssueInputBuilder("ADTEST", 3L)
            .setSummary(String.format("Ticket created via Data Plane E2E Test (%s)", date))
            .build();
    try {
      final String jiraTicket =
          _jiraIssueRestClientProxy
              .createIssue(issueInput)
              .blockOptional()
              .orElseThrow(IllegalStateException::new)
              .getKey();
      return Response.ok(new JSONObject().put("jiraTicket", jiraTicket).toString()).build();
    } catch (final RestClientException pE) {
      return Response.status(pE.getStatusCode().or(500)).entity(pE.getMessage()).build();
    }
  }

  @PATCH
  @Path("/dataPlaneAccessRequests/{ticket}/closeTestTicket")
  @UiCall(auth = false)
  public Response closeTestTicket(
      @Context final HttpServletRequest pRequest, @PathParam("ticket") final String pTicket) {
    try {
      LOG.info("Received request to close ticket. Ticket={}", pTicket);
      final Issue issue =
          _jiraIssueRestClientProxy
              .getIssue(pTicket)
              .blockOptional()
              .orElseThrow(() -> new IllegalStateException("Cannot find ticket " + pTicket));

      // Transitions and options can be viewed here:
      // https://jira-staging.corp.mongodb.com/rest/api/latest/issue/<TICKET>/transitions?expand=transitions.fields
      final Integer transitionId =
          _jiraIssueRestClientProxy
              .getTransitions(issue)
              .filter(transition -> transition.getName().equalsIgnoreCase("Done"))
              .map(Transition::getId)
              .singleOrEmpty()
              .blockOptional()
              .orElseThrow(
                  () ->
                      new IllegalStateException(
                          "Cannot find transition Id with name 'Done' for ticket. Ticket="
                              + pTicket));

      final TransitionInput transitionInput = new TransitionInput(transitionId);
      _jiraIssueRestClientProxy.transition(issue, transitionInput).block();

      LOG.info("Successfully transitioned ticket to 'Done' status. Ticket={}", pTicket);
      return Response.ok().build();
    } catch (final RestClientException pE) {
      return Response.status(pE.getStatusCode().or(500)).entity(pE.getMessage()).build();
    }
  }

  @POST
  @Path("/serverlessMTM/{mtmGroupId}/{mtmClusterName}/setSentinelDelete")
  @UiCall(auth = false)
  public Response setSentinelDelete(
      @Context final HttpServletRequest pRequest,
      @PathParam("mtmGroupId") final ObjectId pGroupId,
      @PathParam("mtmClusterName") final String pMTMClusterName) {
    _mtmClusterDao.setCreateDate(
        pGroupId, pMTMClusterName, MTMClusterType.SERVERLESS, DateUtils.addDays(new Date(), -1));
    return Response.ok().build();
  }

  @PATCH
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/stopAWSInstances")
  @UiCall(auth = false)
  public Response stopAWSInstances(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @FormParam("numNodes") final int pNumNodes,
      @FormParam("nodeType") final String pNodeTypeString) {
    final NodeType nodeType = NodeType.valueOf(pNodeTypeString);
    return handleAWSInstances(
        pGroupId,
        pClusterName,
        pNumNodes,
        nodeType,
        "stopAWSInstances",
        true,
        (accountId, regionName, ec2InstanceId) -> {
          final InstanceState state =
              _awsApiSvc.stopEC2Instance(accountId, regionName, LOG, ec2InstanceId);
          LOG.info(
              "Submitted request to stop EC2 instance id {}. Currently in state \"{}\".",
              ec2InstanceId,
              state.getName());
          return new JSONObject(Map.of("ec2InstanceId", ec2InstanceId, "state", state));
        });
  }

  @GET
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/getAWSInstancesState")
  @UiCall(auth = false)
  public Response getAllAWSInstancesState(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @QueryParam("nodeType") final String pNodeTypeString) {
    final NodeType nodeType =
        Optional.ofNullable(pNodeTypeString).map(NodeType::valueOf).orElse(null);
    return handleAWSInstances(
        pGroupId,
        pClusterName,
        Integer.MAX_VALUE,
        nodeType,
        "getAllAWSInstancesState",
        false,
        (accountId, regionName, ec2InstanceId) -> {
          final String state =
              _awsApiSvc
                  .findEC2Instance(accountId, regionName, LOG, ec2InstanceId)
                  .getState()
                  .getName();
          return new JSONObject(Map.of("ec2InstanceId", ec2InstanceId, "state", state));
        });
  }

  private Response handleAWSInstances(
      final ObjectId pGroupId,
      final String pClusterName,
      final int pNumNodes,
      final NodeType pNodeType,
      final String pOperationPrefix,
      final boolean pIsStoppingNodes,
      final TriFunction<ObjectId, AWSRegionName, String, JSONObject> pAWSInstanceHandler) {
    try {
      final NDSGroup ndsGroup =
          getNdsGroupDao()
              .find(pGroupId)
              .orElseThrow(
                  () -> new IllegalStateException("Cannot find group with id=" + pGroupId));
      final Group group = Optional.ofNullable(_groupSvc.findById(pGroupId)).orElseThrow();

      final Cluster cluster =
          getNdsClusterSvc()
              .getActiveCluster(pGroupId, pClusterName)
              .orElseThrow(
                  () ->
                      new IllegalStateException(
                          String.format(
                              "Cannot find active cluster. groupId=%s, clusterName=%s",
                              pGroupId, pClusterName)));

      final AgentsView monitoringAgentsView = _monitoringAgentAuditSvc.buildAgentsView(group);
      final Set<String> monitoringAgentsHostnames =
          monitoringAgentsView.getEntries().stream()
              .map(AgentView::getHostname)
              .collect(toUnmodifiableSet());

      LOG.info(
          "[{}] Found hostnames for monitoring agents: {}",
          pOperationPrefix,
          monitoringAgentsHostnames);

      // Stop at most one node that had a monitoring agent.
      final List<InstanceHardware> instanceHardware =
          cluster.getReplicaSets().stream()
              .flatMap(
                  rs ->
                      rs.getAllHardware()
                          .filter(
                              ih ->
                                  pNodeType == null
                                      || rs.getNodeTypeForInstance(
                                              cluster.getClusterDescription(), ih.getInstanceId())
                                          .equals(Optional.of(pNodeType))))
              .toList();

      LOG.info(
          "[{}] Found all instance hardware with node type {}: {}",
          pOperationPrefix,
          pNodeType,
          instanceHardware.stream().map(InstanceHardware::getHostnameForAgents).toList());

      final List<InstanceHardware> instanceHardwareToHandle;
      if (pIsStoppingNodes) {
        final Stream<InstanceHardware> instanceHardwareWithoutMonitoringAgents =
            instanceHardware.stream()
                .filter(
                    ih ->
                        ih.getHostnameForAgents()
                            .map(hostname -> !monitoringAgentsHostnames.contains(hostname))
                            .orElse(true));
        final Stream<InstanceHardware> instanceHardwareAtMostOneMonitoringAgent =
            instanceHardware.stream()
                .filter(
                    ih ->
                        ih.getHostnameForAgents()
                            .map(monitoringAgentsHostnames::contains)
                            .orElse(false))
                .limit(1);

        final List<InstanceHardware> allHardwareNoLimit =
            Stream.concat(
                    instanceHardwareWithoutMonitoringAgents,
                    instanceHardwareAtMostOneMonitoringAgent)
                .toList();
        if (pNumNodes > allHardwareNoLimit.size()) {
          throw new IllegalArgumentException(
              String.format(
                  "Number of nodes of nodeType %s (%d) is more than number of available machines"
                      + " (%d).",
                  pNodeType, pNumNodes, allHardwareNoLimit.size()));
        }

        instanceHardwareToHandle = allHardwareNoLimit.stream().limit(pNumNodes).toList();
      } else {
        instanceHardwareToHandle = instanceHardware.stream().limit(pNumNodes).toList();
      }

      LOG.info(
          "[{}] Operating on AWS instance hardware for the nodes with the following hostnames: {}",
          pOperationPrefix,
          instanceHardwareToHandle.stream().map(InstanceHardware::getHostnameForAgents).toList());

      final List<JSONObject> instanceResults =
          instanceHardwareToHandle.stream()
              .map(
                  ih -> {
                    final AWSInstanceHardware awsInstanceHardware = (AWSInstanceHardware) ih;
                    final ObjectId containerId = ih.getCloudContainerId();
                    final AWSCloudProviderContainer cloudProviderContainer =
                        (AWSCloudProviderContainer)
                            NDSCloudProviderContainerSvc.findByIdInGroup(ndsGroup, containerId);

                    final ObjectId accountId = cloudProviderContainer.getAWSAccountId();
                    final AWSRegionName regionName =
                        (AWSRegionName) ih.getRegion(Optional.of(cloudProviderContainer));
                    final String ec2InstanceId =
                        awsInstanceHardware
                            .getEC2InstanceId()
                            .orElseThrow(
                                () -> new IllegalArgumentException("No EC2 instance ID found."));

                    return pAWSInstanceHandler.apply(accountId, regionName, ec2InstanceId);
                  })
              .toList();

      return Response.ok(instanceResults.toString()).build();
    } catch (final Exception pE) {
      LOG.error("Caught exception when handling AWS instance changes.", pE);
      throw pE;
    }
  }

  @PATCH
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @Path("/nds/groups/{groupId}/clusters/{clusterName}/autoScalingMode")
  @UiCall(auth = false)
  public Response setAutoScalingMode(
      @Context final HttpServletRequest pRequest,
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      final String pRequestBody) {

    try {
      _clusterDescriptionDao.setAutoScalingMode(
          pGroupId,
          pClusterName,
          AutoScalingMode.valueOf(new JSONObject(pRequestBody).getString("autoScalingMode")),
          _clusterDescriptionDao.findByName(pGroupId, pClusterName).get().getLastUpdateDate());
    } catch (BaseDao.StaleUpdateException e) {
      LOG.warn(e.getMessage(), e);
      return Response.status(HttpStatus.SC_CONFLICT).entity(e.getMessage()).build();
    }

    return Response.ok().build();
  }

  @GET
  @Path("/configService/refreshStore")
  @UiCall(auth = false)
  public Response refreshConfigServiceStore() throws InterruptedException {
    // NOTE: IT IS NOT ADVISED TO USE THIS PATTERN (OR POPULATELOCALSTORE) IN THE ACTUAL APP SOURCE
    // CODE!
    // Normally we would use the ConfigServiceSdkWrapper for SDK methods, but the
    // "populateLocalStore" method is a "hidden" method that we only want MMS to use for E2E tests
    // (via this endpoint). So instead of exposing it on the Wrapper class, we call the SDK directly
    // here.
    ConfigServiceSdkInterface configServiceSdkInstance =
        AppConfig.getInstance(ConfigServiceSdkInterface.class);
    configServiceSdkInstance.populateLocalStore();
    return Response.ok().build();
  }

  @POST
  @Path("/groups/{groupId}/clusters/{clusterName}/hosts/{hostName}")
  @UiCall(auth = false)
  @Produces(MediaType.APPLICATION_JSON)
  public Response updateRotateSslAfterForHost(
      @PathParam("groupId") final ObjectId pGroupId,
      @PathParam("clusterName") final String pClusterName,
      @PathParam("hostName") final String pHostname)
      throws SvcException {
    final NDSGroup group =
        _ndsGroupSvc
            .find(pGroupId)
            .orElseThrow(() -> new SvcException(ApiErrorCode.GROUP_NOT_FOUND, pGroupId));

    final Pair<ReplicaSetHardware, InstanceHardware> hardware =
        _replicaSetHardwareSvc
            .getReplicaSetHardwareAndInstanceHardware(pGroupId, pHostname, pClusterName)
            .orElseThrow(() -> new SvcException(CommonErrorCode.INVALID_PARAMETER));

    _replicaSetHardwareDao.setInstanceRotateSslAfter(
        hardware.getLeft().getId(),
        hardware.getRight().getInstanceId(),
        hardware.getLeft().isInstanceInternal(hardware.getRight().getInstanceId()),
        new Date());

    return Response.ok().build();
  }

  @POST
  @Path("/mpa/{userEmail}/approve")
  @UiCall(auth = false)
  public Response approveUsersPendingRequests(@PathParam("userEmail") final String userEmail)
      throws SvcException {
    _authorizationSvc.approvePendingMPARequestsForUser(userEmail);
    return Response.noContent().build();
  }

  @POST
  @Path("/nds/plans/setFailPoint")
  @UiCall(auth = false)
  public Response setFailPointForPlan(final String requestBody) {
    final JSONObject bodyJson = new JSONObject(requestBody);
    _planTestPointSvc.setPlanFailPoint(
        new ObjectId(bodyJson.getString("planId")), bodyJson.getString("name"));
    return Response.ok().build();
  }

  @GET
  @Path("/nds/plans/findMostRecent/{groupId}/{moveName}")
  @UiCall(auth = false)
  public Response getMostRecentPlanForGroupWithName(
      final @PathParam("groupId") ObjectId groupId, final @PathParam("moveName") String moveName) {
    final Optional<String> planId =
        _ndsPlanSvc
            .findMostRecentByGroupIdAndMoveName(groupId, moveName)
            .map(Plan::getId)
            .map(ObjectId::toHexString);
    if (planId.isPresent()) {
      return Response.ok(planId.get()).build();
    }
    return Response.status(HttpStatus.SC_NOT_FOUND).build();
  }

  @POST
  @Path("/nds/flexMigration/{migrationType}/{groupId}/initialize")
  @UiCall(auth = false)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response initializeFlexMigration(
      final @PathParam("groupId") ObjectId groupId,
      final @PathParam("migrationType") String migrationType,
      final String requestBody)
      throws Exception {

    final JSONObject body = new JSONObject(requestBody);
    final boolean autoCommit = body.getBoolean("autoCommit");

    final MigrationType flexMigrationType = MigrationType.valueOf(migrationType);

    if (flexMigrationType.equals(MigrationType.SHARED_TO_FLEX)) {
      // first we have to disable the M2/M5 MTM clusters assignment
      _sharedMTMClusterDao.findAllSharedClusters().stream()
          .filter(mtmCluster -> !mtmCluster.getTenantInstanceSize().equals(FreeInstanceSize.M0))
          .forEach(
              sharedMTMCluster -> {
                _sharedMTMClusterDao.updateCluster(
                    sharedMTMCluster.getGroupId(),
                    sharedMTMCluster.getName(),
                    sharedMTMCluster,
                    false,
                    sharedMTMCluster.getMaxCapacity(),
                    sharedMTMCluster.getCapacityRemaining(),
                    sharedMTMCluster.getTenantInstanceSize(),
                    new ArrayList<>(sharedMTMCluster.getIsolationGroupIds()));
              });
    } else {
      // SERVERLESS_TO_FLEX case
      _serverlessMTMClusterDao
          .findAllServerlessClusters()
          .forEach(
              serverlessMTMCluster ->
                  _serverlessMTMClusterDao.updateAssignmentEnabled(
                      serverlessMTMCluster.getGroupId(),
                      serverlessMTMCluster.getName(),
                      serverlessMTMCluster.getMTMClusterType(),
                      false));

      _serverlessMTMPoolSvc
          .getAllPools()
          .forEach(
              serverlessMTMPool -> {
                try {
                  _serverlessMTMPoolSvc.updatePool(
                      serverlessMTMPool.getId(),
                      serverlessMTMPool.getName(),
                      serverlessMTMPool.getUsageType(),
                      serverlessMTMPool.getGroupConfig(),
                      serverlessMTMPool.getStrategy(),
                      serverlessMTMPool.getMaxResidentMTMs(),
                      serverlessMTMPool.getMaxResidentTenants(),
                      false,
                      false,
                      serverlessMTMPool.getIsolationGroups(),
                      serverlessMTMPool.getMergeStrategy());
                } catch (SvcException pE) {
                  // do nothing
                }
              });
    }

    // Saving our service config
    final FlexMigrationServiceConfig.Limits limits = new FlexMigrationServiceConfig.Limits(1, 2, 1);
    final FlexMigrationServiceConfig.Behavior behavior =
        new FlexMigrationServiceConfig.Behavior(
            true, true, true, autoCommit, autoCommit, autoCommit, false);
    final FlexMigrationServiceConfig.CronState cronState =
        new FlexMigrationServiceConfig.CronState(groupId, null);
    _flexMigrationSvc.saveConfig(
        new FlexMigrationServiceConfig(
            flexMigrationType, false, false, limits, behavior, cronState));

    _flexMigrationSvc.initializeMigrationsForMtmHolderGroup(groupId, flexMigrationType);
    return Response.ok().build();
  }

  @POST
  @Path("/nds/flexMigration/{migrationType}/mtm/{groupId}/{clusterName}/initiate")
  @UiCall(auth = false)
  public Response initiateFlexMigrationsForMTM(
      final @PathParam("migrationType") String migrationType,
      final @PathParam("groupId") ObjectId groupId,
      final @PathParam("clusterName") String clusterName)
      throws Exception {
    final MigrationType flexMigrationType = MigrationType.valueOf(migrationType);
    final Optional<FlexMTMClusterMigration> migration =
        _flexMigrationSvc.getMtmClusterMigration(clusterName, groupId);
    if (migration.isEmpty()) {
      LOG.debug(
          "Unable to find flex migration for MTM cluster named {} in group {}",
          clusterName,
          groupId);
      return Response.status(Status.BAD_REQUEST).build();
    }

    final ObjectId mtmUniqueId = migration.orElseThrow().getMTMUniqueId();
    _flexMigrationSvc.initiateMigrationsForMtm(groupId, mtmUniqueId, flexMigrationType);

    return Response.ok().build();
  }

  @POST
  @Path("/nds/flexMigration/{migrationType}/mtm/{groupId}/{clusterName}/rollback")
  @UiCall(auth = false)
  public Response initiateFlexMigrationRollbacksForMTM(
      final @PathParam("migrationType") String migrationType,
      final @PathParam("groupId") ObjectId groupId,
      final @PathParam("clusterName") String clusterName)
      throws Exception {
    final MigrationType flexMigrationType = MigrationType.valueOf(migrationType);
    final Optional<FlexMTMClusterMigration> migration =
        _flexMigrationSvc.getMtmClusterMigration(clusterName, groupId);
    if (migration.isEmpty()) {
      LOG.debug(
          "Unable to find flex migration for MTM cluster named {} in group {}",
          clusterName,
          groupId);
      return Response.status(Status.BAD_REQUEST).build();
    }

    final ObjectId mtmUniqueId = migration.orElseThrow().getMTMUniqueId();
    _flexMigrationSvc.initiateRollbacksForMtm(groupId, mtmUniqueId, flexMigrationType, true);

    return Response.ok().build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/flexMigration/mtm/{groupId}/{clusterName}/status")
  @UiCall(auth = false)
  public Response getFlexMigrationMTMStatus(
      final @PathParam("groupId") ObjectId groupId,
      final @PathParam("clusterName") String clusterName) {
    final Optional<FlexMTMClusterMigration> migration =
        _flexMigrationSvc.getMtmClusterMigration(clusterName, groupId);
    if (migration.isEmpty()) {
      LOG.debug(
          "Unable to find flex migration for MTM cluster named {} in group {}",
          clusterName,
          groupId);
      return Response.status(Status.BAD_REQUEST).build();
    }

    final JSONObject response = new JSONObject();
    response.put("status", migration.orElseThrow().getStatus().toString());
    return Response.ok(response.toString()).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/flexMigration/tenant/{groupId}/{clusterName}/status")
  @UiCall(auth = false)
  public Response getFlexMigrationTenantStatus(
      final @PathParam("groupId") ObjectId groupId,
      final @PathParam("clusterName") String clusterName) {
    final Optional<FlexTenantMigration> migration =
        _flexMigrationSvc.getTenantMigration(clusterName, groupId);
    if (migration.isEmpty()) {
      LOG.debug(
          "Unable to find flex migration for tenant cluster named {} in group {}",
          clusterName,
          groupId);
      return Response.status(Status.BAD_REQUEST).build();
    }

    final JSONObject response = new JSONObject();
    response.put("status", migration.orElseThrow().getStatus().toString());
    return Response.ok(response.toString()).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/flexMigration/{groupId}/allMigrations")
  @UiCall(auth = false)
  public Response getAllFlexMigrationsSuccessful(final @PathParam("groupId") ObjectId groupId) {
    final boolean flexTenantMigrationsSuccessful =
        _flexMigrationSvc.getTenantMigrationsByMtmHolderGroup(groupId).stream()
            .allMatch(migration -> migration.getStatus() == MigrationStatus.MIGRATION_SUCCESS);
    final boolean flexMTMClusterMigrationsSuccessful =
        _flexMigrationSvc.getMtmClusterMigrationsByMtmHolderGroup(groupId).stream()
            .allMatch(migration -> migration.getStatus() == MigrationStatus.MIGRATION_SUCCESS);
    final boolean flexMTMHolderGroupMigrationsSuccessful =
        _flexMigrationSvc.getMtmHolderGroupMigration(groupId).orElseThrow().getStatus()
            == MigrationStatus.MIGRATION_SUCCESS;

    final JSONObject response = new JSONObject();
    response.put(
        "result",
        flexTenantMigrationsSuccessful
            && flexMTMClusterMigrationsSuccessful
            && flexMTMHolderGroupMigrationsSuccessful);
    return Response.ok(response.toString()).build();
  }

  @GET
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/nds/mongotune/version")
  @UiCall(auth = false)
  public Response getMongotuneVersion(
      @QueryParam("previousVersion") final boolean isPreviousVersion) {
    final VersionUtils.Version version =
        isPreviousVersion
            ? _appSettings.getMongotunePreviousVersion()
            : _appSettings.getMongotuneVersion();
    LOG.info(
        "Got mongotune version: {}, isPreviousVersion: {}",
        version.getVersion(),
        isPreviousVersion);
    final JSONObject response = new JSONObject();
    response.put("version", version.getVersion());
    return Response.ok(response.toString()).build();
  }

  @POST
  @Path("/nds/encryptionAtRest/validate")
  @UiCall(auth = false)
  public Response triggerEncryptionAtRestValidation() {
    try {
      _ndsEncryptionAtRestSvc.validateEncryptionAtRestForGroups();
      return Response.noContent().build();
    } catch (final Exception e) {
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity("{\"error\": \"" + e.getMessage() + "\"}")
          .build();
    }
  }

  private String generateAuthnManagedSession(
      final HttpServletRequest pRequest, final HttpServletResponse pResponse, final AppUser user) {
    try {
      /*
       * Added to support use of Authn Service minted session tokens in E2E tests.
       * Tokens will be minted for the current user and the authn token cookies will be added to
       * the response. Token creation will fail if this request is made in an environment where
       * the Authn Service is not configured to all use of its test utilities.
       */
      final AuthnTestUtilsClient authnTestUtilsClient = _authnTestUtilsClientProvider.get();
      if (authnTestUtilsClient != null) {
        final String subject =
            user.getOktaUserId() != null ? user.getOktaUserId() : user.getId().toString();
        final GenerateTokenPairResponse generateTokenPairResponse =
            authnTestUtilsClient.generateTokenPair(subject);

        final CloudJwt accessTokenJwt =
            _jwtDecoder.decodeAsAuth0CloudJwt(generateTokenPairResponse.getAccessToken());
        final String sessionId = accessTokenJwt.getSessionId();

        UiAuthCode uiAuthCode = user.findUiAuthCode(_userLoginSvc.getUiAuthCode(pRequest));

        final int idleSessionTimeout =
            determineIdleSessionTimeoutSeconds(
                _iamAppSettings.isCustomSessionTimeoutsEnabled(),
                _iamAppSettings.getIdleSessionTimeoutSeconds(),
                uiAuthCode);
        _sessionCookieSvc.writeSessionCookies(
            pRequest,
            pResponse,
            null,
            generateTokenPairResponse.getAccessToken(),
            generateTokenPairResponse.getRefreshToken(),
            idleSessionTimeout > 0
                ? idleSessionTimeout
                : _iamAppSettings.getMmsAuthCookieExpirationSeconds());

        return sessionId;
      }
    } catch (final Exception e) {
      // This error is swallowed for now since the authn service isn't being used for
      // session management across envs
      LOG.debug("Error generating authn token pair", e);
    }
    return null;
  }

  private UserSvc getUserSvc() {
    return _userSvc;
  }

  private GroupSvc getGroupSvc() {
    return _groupSvc;
  }

  private MultiFactorAuthSvc getMultiFactorAuthSvc() {
    return _multiFactorAuthSvc;
  }

  private EmailNotifier getEmailNotifier() {
    return _emailNotifier;
  }

  private DelinquentOrganizationEmailSvc getDelinquentOrganizationEmailSvc() {
    return _delinquentOrganizationEmailSvc;
  }

  private EmailSvc getEmailSvc() {
    return _emailSvc;
  }

  private NDSAdminSvc getNdsAdminSvc() {
    return _ndsAdminSvc;
  }

  private NDSGroupSvc getNdsGroupSvc() {
    return _ndsGroupSvc;
  }

  private NDSGroupDao getNdsGroupDao() {
    return _ndsGroupDao;
  }

  private ReplicaSetHardwareSvc getReplicaSetHardwareSvc() {
    return _replicaSetHardwareSvc;
  }

  private NDSClusterSvc getNdsClusterSvc() {
    return _ndsClusterSvc;
  }

  private NDSLookupSvc getNdsLookupSvc() {
    return _ndsLookupSvc;
  }

  private ConfigLimitSvc getConfigLimitSvc() {
    return _configLimitSvc;
  }

  private OrganizationSvc getOrganizationSvc() {
    return _organizationSvc;
  }

  private NDSOrgSvc getNdsOrgSvc() {
    return _ndsOrgSvc;
  }
}
