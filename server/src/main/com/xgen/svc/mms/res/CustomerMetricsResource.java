package com.xgen.svc.mms.res;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.res._public.base.BaseResource;
import com.xgen.cloud.customermetrics._private.dao.MetricsDisplayConfigDao;
import com.xgen.cloud.customermetrics._public.model.charts.MetricsDisplayConfig;
import com.xgen.cloud.customermetrics._public.model.query.RangeQueryRequest;
import com.xgen.cloud.customermetrics._public.svc.CustomerMetricsQuerySvc;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.view.customermetrics.CustomerMetricsMetadataResponseView;
import com.xgen.svc.mms.res.view.customermetrics.CustomerMetricsPointsResponseView;
import com.xgen.svc.mms.res.view.customermetrics.CustomerMetricsRequestParams;
import com.xgen.svc.mms.svc.metrics.maas.CustomerMetricsUsecaseSvc;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/v1/customermetrics")
@Hidden
@Singleton
public class CustomerMetricsResource extends BaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(CustomerMetricsResource.class);

  private static final String SERIES_LIMIT_PROP_STR = "mms.metrics.read.maxSeries";
  private static final int SERIES_LIMIT_DEFAULT = 100;

  private static final String QUERY_TIMEOUT_PROP_STR = "mms.metrics.read.maasRangeQueryTimeout";
  private static final String QUERY_TIMEOUT_DEFAULT = "5s"; // 5 seconds

  private static final String DEFAULT_STEP = "5m";
  private static final String DEFAULT_LOOKBACK = "10m";

  private final AppSettings _appSettings;
  private final CustomerMetricsQuerySvc _customerMetricsQuerySvc;
  private final CustomerMetricsUsecaseSvc _customerMetricsUsecaseSvc;
  private final MetricsDisplayConfigDao _metricsDisplayConfigDao;

  @Inject
  public CustomerMetricsResource(
      final AppSettings pAppSettings,
      final CustomerMetricsQuerySvc pCustomerMetricsQuerySvc,
      final CustomerMetricsUsecaseSvc pCustomerMetricsUsecaseSvc,
      final MetricsDisplayConfigDao pMetricsDisplayConfigDao) {
    _appSettings = pAppSettings;
    _customerMetricsQuerySvc = pCustomerMetricsQuerySvc;
    _customerMetricsUsecaseSvc = pCustomerMetricsUsecaseSvc;
    _metricsDisplayConfigDao = pMetricsDisplayConfigDao;
  }

  @GET
  @Path("/groups/{groupId}/clusters/{clusterName}/points")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @Feature(FeatureFlag.MAAS_METRICS_CHARTS)
  public Response getPoints(
      @PathParam("groupId") final String groupId,
      @PathParam("clusterName") final String clusterName,
      @QueryParam("usecaseId") final String usecaseId,
      @QueryParam("since") final Long since,
      @QueryParam("until") final Long until,
      @QueryParam("chartGroupId") final String chartGroupId,
      @QueryParam("chartId") final String chartId,
      @QueryParam("hostnamePorts[]") final List<String> hostnamePorts,
      @QueryParam("excludeHostnamePorts[]") final List<String> excludeHostnamePorts,
      @QueryParam("clusterRoles[]") final List<String> clusterRoles,
      @QueryParam("replicaStates[]") final List<String> replicaStates,
      @QueryParam("replicaSetIds[]") final List<String> replicaSetIds,
      @QueryParam("envelope") @DefaultValue("false") final Boolean pEnvelope) {

    LOG.debug(
        "getPoints called with groupId: {}, clusterName: {}, since: {}, until: {},"
            + " chartGroupId: {}, chartId: {}, hostnamePorts: {}, excludeHostnamePorts: {},"
            + " clusterRoles: {}, replicaStates: {}, replicaSetIds: {}",
        groupId,
        clusterName,
        since,
        until,
        chartGroupId,
        chartId,
        hostnamePorts,
        excludeHostnamePorts,
        clusterRoles,
        replicaStates,
        replicaSetIds);

    final MetricsDisplayConfig metricsDisplayConfig =
        _metricsDisplayConfigDao.getMetricsDisplayConfig();

    final int seriesLimit = _appSettings.getIntProp(SERIES_LIMIT_PROP_STR, SERIES_LIMIT_DEFAULT);
    final String queryTimeout =
        _appSettings.getStrProp(QUERY_TIMEOUT_PROP_STR, QUERY_TIMEOUT_DEFAULT);

    // Validate request parameters and construct parameter object
    final CustomerMetricsRequestParams params =
        new CustomerMetricsRequestParams(
            groupId,
            clusterName,
            usecaseId,
            since,
            until,
            hostnamePorts,
            excludeHostnamePorts,
            clusterRoles,
            replicaStates,
            replicaSetIds,
            pEnvelope);

    if (params.getHostnames().isEmpty()) {
      return Response.ok(CustomerMetricsPointsResponseView.empty()).build();
    }

    final Optional<MetricsDisplayConfig.ChartGroup> matchingChartGroup =
        metricsDisplayConfig.getChartGroups().stream()
            .filter(chartGroup -> chartGroup.getId().equals(chartGroupId))
            .findFirst();
    if (matchingChartGroup.isEmpty()) {
      throw ApiErrorCode.INVALID_PATH_PARAMETER.exception(false, chartGroupId);
    }

    final Optional<MetricsDisplayConfig.Chart> matchingChart =
        matchingChartGroup.get().getCharts().stream()
            .filter(chart -> chart.getId().equals(chartId))
            .findFirst();
    if (matchingChart.isEmpty()) {
      throw ApiErrorCode.INVALID_PATH_PARAMETER.exception(false, chartId);
    }

    // TODO prefer user selected granularity if provided
    // (step, lookback)
    final Pair<String, String> granularity =
        getAutoGranularity(params.getUseCaseId(), params.getSince());

    // Fill in the aggregation, filter, duration in the MetricsQL template for the given chart
    final String metricsQLTemplate = matchingChart.get().getMetricsQLTemplate();
    // TODO: Update with aggregation from UI
    final String aggregation = "host_name";
    final String populatedQueryString =
        metricsQLTemplate
            .replace("$aggregation$", aggregation)
            .replace("$filter$", params.constructFilterExpression())
            .replace(
                "$lookback$",
                granularity.getRight()); // Use step as lookback (this is also default VM behavior)

    // Ensure only this group's metrics are retrieved using extraFilters
    final Map<String, List<String>> extraFilters = Map.of("group_id", List.of(groupId));
    final UUID usecaseUuid = usecaseId != null ? UUID.fromString(usecaseId) : UUID.randomUUID();

    final String startTimestamp = String.format("%d", params.getSince().getEpochSecond());
    final String endTimestamp = String.format("%d", params.getUntil().getEpochSecond());

    final RangeQueryRequest rangeQueryRequest =
        new RangeQueryRequest.Builder(populatedQueryString, usecaseUuid, startTimestamp)
            .withEnd(endTimestamp)
            .withStep(granularity.getLeft())
            .withTimeout(queryTimeout)
            .withExtraFiltersMap(extraFilters)
            .build();

    try {
      final List<CustomerMetricsPointsResponseView.ResultView> resultViews =
          _customerMetricsQuerySvc
              .getMetricsViaRangeQuery(rangeQueryRequest)
              .getData()
              .getResult()
              .stream()
              .map(
                  result ->
                      new CustomerMetricsPointsResponseView.ResultView(
                          result.getMetric(), result.getValues()))
              .limit(seriesLimit)
              .toList();

      return Response.ok()
          .entity(new CustomerMetricsPointsResponseView(resultViews, false))
          .build();
    } catch (WebApplicationException e) {
      if (e.getResponse().getStatus() == 422
          && e.getMessage().toLowerCase().contains("too many points")) {
        LOG.debug(
            "Too many points requested for groupId: {}, clusterName: {}, chartGroupId: {}, chartId:"
                + " {}. Returning error response.",
            groupId,
            clusterName,
            chartGroupId,
            chartId);
        return Response.status(422).entity("Too many points requested").build();
      }
      LOG.error("Error fetching metrics for groupId: {}, clusterName: {}", groupId, clusterName, e);
      return Response.status(e.getResponse().getStatus()).entity("Backing datastore error").build();
    } catch (Exception e) {
      LOG.error(
          "Unexpected error fetching metrics for groupId: {}, clusterName: {}",
          groupId,
          clusterName,
          e);
      return Response.serverError().entity("Internal server error").build();
    }
  }

  // Returns a pair of (step, lookback) representing the smallest available granularity for the
  // given usecase and time range
  private Pair<String, String> getAutoGranularity(final UUID pUsecaseId, final Instant pTime) {
    final String step = _customerMetricsUsecaseSvc.getSmallestStepForTimeRange(pUsecaseId, pTime);

    if (step == null) {
      LOG.debug(
          "No step found for usecaseId: {} at time: {}. Using default step: {}",
          pUsecaseId,
          pTime,
          DEFAULT_STEP);
      return Pair.of(DEFAULT_STEP, DEFAULT_LOOKBACK);
    }

    // Set lookback to be 2x step, to ensure at least 2 data points are returned for aggregations
    // like rate(), etc
    final long stepMillis = CustomerMetricsUsecaseSvc.convertMetricsQLVectorToMillis(step);
    final String lookback =
        CustomerMetricsUsecaseSvc.convertMillisToMetricsQLVector(2 * stepMillis);

    return Pair.of(step, lookback);
  }

  @GET
  @Path("/groups/{groupId}/metadata")
  @Produces("application/json")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @Feature(FeatureFlag.MAAS_METRICS_CHARTS)
  public Response getMetadata(@PathParam("groupId") final String groupId) {
    final int seriesLimit = _appSettings.getIntProp(SERIES_LIMIT_PROP_STR, SERIES_LIMIT_DEFAULT);

    final MetricsDisplayConfig metricsDisplayConfig =
        _metricsDisplayConfigDao.getMetricsDisplayConfig();
    final CustomerMetricsMetadataResponseView responseView =
        new CustomerMetricsMetadataResponseView(
            metricsDisplayConfig.getChartGroups().stream()
                .map(
                    chartGroup ->
                        new CustomerMetricsMetadataResponseView.ChartGroupMetadata(
                            chartGroup.getId(),
                            chartGroup.getTitle(),
                            chartGroup.getHelp(),
                            chartGroup.isDefaultSelected(),
                            chartGroup.getUsecaseId(),
                            chartGroup.getCharts().stream()
                                .map(
                                    chart ->
                                        new CustomerMetricsMetadataResponseView.ChartMetadata(
                                            chart.getId(),
                                            chart.getTitle(),
                                            chart.getHelp(),
                                            chart.getUnit(),
                                            chart.isDefaultSelected()))
                                .toList()))
                .toList(),
            metricsDisplayConfig.getCategories().stream()
                .map(
                    category ->
                        new CustomerMetricsMetadataResponseView.CategoryMetadata(
                            category.getId(),
                            category.getTitle(),
                            category.getDescription(),
                            category.getChartGroups()))
                .toList(),
            seriesLimit);
    return Response.ok().entity(responseView).build();
  }

  @GET
  @Path("/groups/{groupId}/retentions")
  @Produces("application/json")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @AllowCORS(KnownCrossOrigin.CLOUD)
  @Feature(FeatureFlag.MAAS_METRICS_CHARTS)
  public Response getRetentions(
      @PathParam("groupId") final String groupId,
      @QueryParam("granularities[]") final List<String> granularities) {
    if (granularities == null || granularities.isEmpty()) {
      throw ApiErrorCode.INVALID_QUERY_PARAMETER.exception(false, "granularities");
    }

    final MetricsDisplayConfig metricsDisplayConfig =
        _metricsDisplayConfigDao.getMetricsDisplayConfig();

    final List<UUID> usecaseIds =
        metricsDisplayConfig.getChartGroups().stream()
            .map(MetricsDisplayConfig.ChartGroup::getUsecaseId)
            .distinct()
            .toList();

    final Map<String, Long> granularityToRetention =
        granularities.stream()
            .collect(
                Collectors.toMap(
                    g -> g,
                    g ->
                        _customerMetricsUsecaseSvc.getLargestRetentionForGranularity(
                            usecaseIds, g)));

    return Response.ok().entity(granularityToRetention).build();
  }
}
