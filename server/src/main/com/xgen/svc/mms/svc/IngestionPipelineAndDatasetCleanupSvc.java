package com.xgen.svc.mms.svc;

import static com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.getSummaryQuantile;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.mongodb.MongoInterruptedException;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.util.LogUtils;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._private.dao.IngestionPipelineDao;
import com.xgen.cloud.nds.datalake._private.dao.IngestionPipelineRunDao;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataSetDao;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipeline;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipeline.State;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipelineRun;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSet;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSetDLS;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSetDLSIngestion;
import com.xgen.svc.nds.svc.IngestionPipelineSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.util.DLSPromMetricsUtil;
import com.xgen.svc.nds.util.DataLakeLogsUtil;
import io.prometheus.client.Gauge;
import io.prometheus.client.Summary;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.time.Instant;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import net.logstash.logback.argument.StructuredArgument;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class IngestionPipelineAndDatasetCleanupSvc {

  public static final String DELETE_ACTIVE_OR_PAUSED_INGESTION_PIPELINES_CRON_NAME =
      "deleteActiveOrPausedIngestionPipelines";
  public static final Duration DELETE_ACTIVE_OR_PAUSED_INGESTION_PIPELINES_CRON_INTERVAL =
      Duration.ofMinutes(1);
  public static final String DELETED_INGESTION_PIPELINE_CRON_NAME =
      "deletedIngestionPipelineCleanup";
  public static final Duration DELETED_INGESTION_PIPELINE_CRON_INTERVAL = Duration.ofSeconds(10);

  public static final String EXPIRED_DATA_SET_CRON_NAME = "expiredDatasetCleanup";
  public static final Duration EXPIRED_DATA_SET_CRON_INTERVAL = Duration.ofMinutes(5);

  public static final String EXPIRED_INGESTION_PIPELINE_CRON_NAME =
      "expiredIngestionPipelineCleanup";
  public static final Duration EXPIRED_INGESTION_PIPELINE_CRON_INTERVAL = Duration.ofSeconds(10);

  public static final String ON_DEMAND_INGESTION_CRON_NAME = "onDemandPipelineIngestion";
  public static final Duration ON_DEMAND_INGESTION_CRON_INTERVAL = Duration.ofMinutes(1);
  public static final Duration ON_DEMAND_DEBOUNCE_INTERVAL = Duration.ofMinutes(5);

  private static final Logger LOG =
      LoggerFactory.getLogger(IngestionPipelineAndDatasetCleanupSvc.class);

  private static final int DELETE_ACTIVE_OR_PAUSED_INGESTION_PIPELINES_BATCH_SIZE = 10;
  public static final int DATA_SETS_DLS_OPERATION_BATCH_SIZE = 5;
  private static final int DELETED_INGESTION_PIPELINES_MAX_THREAD_DEFAULT_COUNT = 4;
  private static final int EXPIRED_INGESTION_PIPELINES_MAX_THREAD_DEFAULT_COUNT = 4;
  private static final int EXPIRED_DATA_SETS_MAX_THREAD_DEFAULT_COUNT = 4;
  private static final int DELETED_INGESTION_PIPELINES_QUERY_DEFAULT_LIMIT = 1000;
  private static final int EXPIRED_INGESTION_PIPELINES_QUERY_DEFAULT_LIMIT = 1000;
  private static final int EXPIRED_DATA_SETS_QUERY_DEFAULT_LIMIT = 1000;
  private static final int ON_DEMAND_PIPELINE_RUNS_QUERY_DEFAULT_LIMIT = 1000;

  private static final Duration DELETED_INGESTION_PIPELINE_BATCH_PROCESSING_TIMEOUT =
      Duration.ofMinutes(5);
  private static final Duration EXPIRED_INGESTION_PIPELINE_BATCH_PROCESSING_TIMEOUT =
      Duration.ofSeconds(15);
  private static final Duration EXPIRED_DATA_SET_BATCH_PROCESSING_TIMEOUT = Duration.ofMinutes(10);

  private static final String STATE_EXPIRED_LABEL_VALUE = "EXPIRED";

  private static final long DELETED_INGESTION_PIPELINE_DEFAULT_GRACE_PERIOD =
      Duration.ofDays(5).toMinutes();
  private static final long DELETED_DATA_SET_DEFAULT_GRACE_PERIOD = Duration.ofDays(5).toMinutes();

  private static final Gauge DATA_SETS_BY_STATE_GAUGE =
      PromMetricsSvc.registerGauge(
          "nds_data_sets_batch_size",
          "Batch size of data sets by state",
          DLSPromMetricsUtil.STATE_LABEL_KEY);

  private static final Summary DATA_SETS_PROCESSED_BY_STATE_DURATION_SUMMARY =
      PromMetricsSvc.registerSummary(
          "nds_data_sets_processed_duration_ms",
          "Time taken to complete processing of a batch of data sets",
          List.of(
              getSummaryQuantile(0.99, 0.001),
              getSummaryQuantile(0.95, 0.01),
              getSummaryQuantile(0.50, 0.05)),
          DLSPromMetricsUtil.STATE_LABEL_KEY);

  private static final Gauge PIPELINES_BY_STATE_GAUGE =
      PromMetricsSvc.registerGauge(
          "nds_ingestion_pipelines_batch_size",
          "Batch size of ingestion pipelines by state",
          DLSPromMetricsUtil.STATE_LABEL_KEY);

  private static final Summary PIPELINES_PROCESSED_BY_STATE_DURATION_SUMMARY =
      PromMetricsSvc.registerSummary(
          "nds_ingestion_pipelines_processed_duration_ms",
          "Time taken to complete processing of a batch of ingestion pipelines",
          List.of(
              getSummaryQuantile(0.99, 0.001),
              getSummaryQuantile(0.95, 0.01),
              getSummaryQuantile(0.50, 0.05)),
          DLSPromMetricsUtil.STATE_LABEL_KEY);

  private final AppSettings _appSettings;
  private final IngestionPipelineDao _ingestionPipelineDao;
  private final IngestionPipelineSvc _ingestionPipelineSvc;
  private final NDSDataSetDao _ndsDataSetDao;
  private final DataLakeAdminApiClient _dataLakeAdminApiClient;
  private final IngestionPipelineRunDao _ingestionPipelineRunDao;
  private final GroupSvc _groupSvc;

  @Inject
  public IngestionPipelineAndDatasetCleanupSvc(
      final AppSettings pAppSettings,
      final IngestionPipelineDao pIngestionPipelineDao,
      final IngestionPipelineSvc pIngestionPipelineSvc,
      final NDSDataSetDao pNDSDataSetDao,
      final DataLakeAdminApiClient pDataLakeAdminApiClient,
      final IngestionPipelineRunDao pIngestionPipelineRunDao,
      final GroupSvc pGroupSvc) {
    _appSettings = pAppSettings;
    _ingestionPipelineDao = pIngestionPipelineDao;
    _ndsDataSetDao = pNDSDataSetDao;
    _dataLakeAdminApiClient = pDataLakeAdminApiClient;
    _ingestionPipelineSvc = pIngestionPipelineSvc;
    _ingestionPipelineRunDao = pIngestionPipelineRunDao;
    _groupSvc = pGroupSvc;
  }

  /**
   * Entry point for the CronJob that disables the data sets associated with a DELETING pipeline.
   */
  public void disableDataSetsForDeletedPipelines() {
    final int threadPoolSize =
        _appSettings.getExpiredIngestionPipelinesThreadPoolSize(
            DELETED_INGESTION_PIPELINES_MAX_THREAD_DEFAULT_COUNT);
    final ExecutorService executor = getThreadPoolExecutor(threadPoolSize);
    final Date batchStartTime = new Date();

    try {
      final List<IngestionPipeline> deletingPipelines =
          _ingestionPipelineDao.findPipelinesMarkedDeletingWithoutDeletedDate(
              getDeletedIngestionPipelineQueryLimit());

      setPipelinesBatchByStateGauge(deletingPipelines.size(), State.DELETING.name());

      for (final IngestionPipeline pipeline : deletingPipelines) {
        executor.submit(() -> disableDataSetsAndUpdatePipelineDeletedDate(pipeline));
      }

      executor.shutdown();
      if (!executor.awaitTermination(
          DELETED_INGESTION_PIPELINE_BATCH_PROCESSING_TIMEOUT.toMillis(), TimeUnit.MILLISECONDS)) {
        LOG.warn("Thread pool did not finish within wait time");
      }

      observeIngestionPipelinesProcessedByStateDuration(batchStartTime, State.DELETING.name());
    } catch (final MongoInterruptedException | InterruptedException pException) {
      Thread.currentThread().interrupt();
      LOG.warn("Interrupted operation on DELETING pipeline => shutting down early", pException);
    } finally {
      final int skipped = executor.shutdownNow().size();
      if (skipped != 0) {
        LOG.warn("Executor did not have a clean shutdown. {} tasks were aborted.", skipped);
      }
    }

    LOG.info(
        DataLakeLogsUtil.LOG_FORMAT,
        LogUtils.entries(DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM),
        "Cron job disableDataSetsForDeletedPipelines finished running");
  }

  /*
   * Entry point for the cron job that creates export jobs for on-demand pipeline runs;
   */
  public void scheduleOnDemandExportJobs() {
    final Map<ObjectId, List<IngestionPipelineRun>> runsBySnapshotId =
        _ingestionPipelineRunDao
            .findExportPendingPipelineRuns(ON_DEMAND_PIPELINE_RUNS_QUERY_DEFAULT_LIMIT)
            .collect(Collectors.groupingBy(IngestionPipelineRun::getSnapshotId));

    for (Map.Entry<ObjectId, List<IngestionPipelineRun>> entry : runsBySnapshotId.entrySet()) {
      final ObjectId snapshotId = entry.getKey();
      final List<IngestionPipelineRun> runs = entry.getValue();
      if (isReadyForExport(runs)) {
        cleanupExpiredDataSetsForOnDemandExportJobs(runs);
        LOG.info(
            "Creating export job for {} pipeline runs for snapshotId={}", runs.size(), snapshotId);
        _ingestionPipelineSvc.createExportJobForOnDemandPipelineRuns(snapshotId, runs);
      } else {
        LOG.info(
            "Skipping export job creation for {} runs for snapshotId={} as not ready for export",
            runs.size(),
            snapshotId);
      }
    }

    LOG.info(
        DataLakeLogsUtil.LOG_FORMAT,
        LogUtils.entries(DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM),
        "Cron job scheduleOnDemandExportJob finished running");
  }

  private void cleanupExpiredDataSetsForOnDemandExportJobs(final List<IngestionPipelineRun> runs) {
    if (runs.isEmpty()) {
      return;
    }
    final ObjectId groupId = runs.get(0).getGroupId();
    final List<NDSDataSetDLS> expiredDataSets =
        _ndsDataSetDao
            .findByGroupIdAndDataSetNames(
                groupId,
                runs.stream()
                    .map(IngestionPipelineRun::getDatasetName)
                    .collect(Collectors.toList()))
            .stream()
            .filter(ds -> ds.getState().equals(NDSDataSet.State.DELETING))
            .filter(NDSDataSetDLS.class::isInstance)
            .map(NDSDataSetDLS.class::cast)
            .collect(Collectors.toList());
    if (expiredDataSets.isEmpty()) {
      return;
    }
    LOG.info(
        "Destroying expired datasets {} for on demand pipeline runs for group {}",
        expiredDataSets.stream().map(NDSDataSet::getName).collect(Collectors.joining(",")),
        groupId);
    destroyExpiredDataSets(groupId, expiredDataSets);
  }

  @VisibleForTesting
  public boolean isReadyForExport(final List<IngestionPipelineRun> runs) {
    final Optional<Instant> minCreatedDateOpt =
        runs.stream()
            .min(Comparator.comparing(IngestionPipelineRun::getCreatedDate))
            .map(IngestionPipelineRun::getCreatedDate)
            .map(Date::toInstant);
    return minCreatedDateOpt.isPresent()
        && minCreatedDateOpt.get().isBefore(Instant.now().minus(ON_DEMAND_DEBOUNCE_INTERVAL));
  }

  /**
   * Entry point for the CronJob that cleans up expired data sets. A data set is considered
   * 'expired' if it has been in DELETING state for the configured grace period.
   */
  public void cleanupExpiredDataSets() {
    final int threadPoolSize =
        _appSettings.getExpiredDatasetsThreadPoolSize(EXPIRED_DATA_SETS_MAX_THREAD_DEFAULT_COUNT);
    final ExecutorService executor = getThreadPoolExecutor(threadPoolSize);
    final Date batchStartTime = new Date();

    try {
      final List<NDSDataSetDLS> expiredDatasets =
          _ndsDataSetDao.findExpiredDLSDatasets(
              getDeletedDatasetGracePeriod(), getExpiredDatasetQueryLimit());

      setDatasetsBatchByStateGauge(expiredDatasets.size(), STATE_EXPIRED_LABEL_VALUE);

      final Map<ObjectId, List<NDSDataSetDLS>> datasetsByGroup =
          groupDatasetsByGroupId(expiredDatasets);

      for (final Map.Entry<ObjectId, List<NDSDataSetDLS>> entry : datasetsByGroup.entrySet()) {
        final Group group = _groupSvc.findById(entry.getKey());

        // Snapshots data set deletion will be halted by enabling a feature flag on
        // September 30, 2025 to account for user escalation.
        // Data set destruction will be resumed around October 15, 2025.
        final boolean isDestructionHalted =
            FeatureFlagSvc.isFeatureFlagEnabled(
                FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION,
                _appSettings,
                null,
                group);

        final StructuredArgument logEntries =
            LogUtils.entries(
                DataLakeLogsUtil.FieldDefs.TEAM,
                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                DataLakeLogsUtil.FieldDefs.GROUP_ID,
                entry.getKey());

        if (isDestructionHalted) {
          LOG.info(
              DataLakeLogsUtil.LOG_FORMAT,
              logEntries,
              "Data set destruction is halted due to feature flag"
                  + " (mms.featureFlag.dataLakeEOL.haltDataSetDestruction) enablement");
        } else {
          LOG.info(
              DataLakeLogsUtil.LOG_FORMAT,
              logEntries,
              "Data set destruction proceeding due to feature flag"
                  + " (mms.featureFlag.dataLakeEOL.haltDataSetDestruction) disablement");
          executor.submit(() -> destroyExpiredDataSets(entry.getKey(), entry.getValue()));
        }
      }

      executor.shutdown();
      if (!executor.awaitTermination(
          EXPIRED_DATA_SET_BATCH_PROCESSING_TIMEOUT.toMillis(), TimeUnit.MILLISECONDS)) {
        LOG.warn("Thread pool did not finish within wait time");
      }

      observeDatasetsProcessedByStateDuration(batchStartTime, STATE_EXPIRED_LABEL_VALUE);
    } catch (final MongoInterruptedException | InterruptedException pException) {
      Thread.currentThread().interrupt();
      LOG.warn("Interrupted operation on EXPIRED data sets => shutting down early", pException);
    } finally {
      final int skipped = executor.shutdownNow().size();
      if (skipped != 0) {
        LOG.warn("Executor did not have a clean shutdown. {} tasks were aborted.", skipped);
      }
    }

    LOG.info(
        DataLakeLogsUtil.LOG_FORMAT,
        LogUtils.entries(DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM),
        "Cron job cleanupExpiredDataSets finished running");
  }

  /**
   * Entry point for the CronJob that cleans up expired ingestion pipelines. A pipeline is
   * considered 'expired' if it has been in DELETED state for the configured grace period.
   */
  public void cleanupExpiredPipelines() {
    final int threadPoolSize =
        _appSettings.getExpiredIngestionPipelinesThreadPoolSize(
            EXPIRED_INGESTION_PIPELINES_MAX_THREAD_DEFAULT_COUNT);
    final ExecutorService executor = getThreadPoolExecutor(threadPoolSize);
    final Date batchStartTime = new Date();

    try {
      final List<IngestionPipeline> expiredIngestionPipelines =
          _ingestionPipelineDao.findExpiredPipelines(
              getDeletedIngestionPipelineGracePeriod(), getExpiredIngestionPipelineQueryLimit());

      setPipelinesBatchByStateGauge(expiredIngestionPipelines.size(), STATE_EXPIRED_LABEL_VALUE);

      for (final IngestionPipeline pipeline : expiredIngestionPipelines) {
        final Group group = _groupSvc.findById(pipeline.getGroupId());

        // Snapshots data set deletion will be halted by enabling a feature flag on
        // September 30, 2025 to account for user escalation. Since data sets will not be destroyed,
        // pipeline cleanup will not have any functional effect. We will halt the submission of
        // these tasks for execution to avoid consuming resources unnecessarily.
        // Data set destruction will be resumed around October 15, 2025.
        final boolean isDestructionHalted =
            FeatureFlagSvc.isFeatureFlagEnabled(
                FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION,
                _appSettings,
                null,
                group);

        final StructuredArgument logEntries =
            LogUtils.entries(
                DataLakeLogsUtil.FieldDefs.TEAM,
                DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
                DataLakeLogsUtil.FieldDefs.GROUP_ID,
                pipeline.getGroupId());

        if (isDestructionHalted) {
          LOG.info(
              DataLakeLogsUtil.LOG_FORMAT,
              logEntries,
              "Pipeline cleanup is halted due to feature flag"
                  + " (mms.featureFlag.dataLakeEOL.haltDataSetDestruction) enablement");
        } else {
          LOG.info(
              DataLakeLogsUtil.LOG_FORMAT,
              logEntries,
              "Pipeline cleanup proceeding due to feature flag"
                  + " (mms.featureFlag.dataLakeEOL.haltDataSetDestruction) disablement");
          executor.submit(() -> ensureNoDataSetsForExpiredPipelineAndMarkDestroyed(pipeline));
        }
      }

      executor.shutdown();
      if (!executor.awaitTermination(
          EXPIRED_INGESTION_PIPELINE_BATCH_PROCESSING_TIMEOUT.toMillis(), TimeUnit.MILLISECONDS)) {
        LOG.warn("Thread pool did not finish within wait time");
      }

      observeIngestionPipelinesProcessedByStateDuration(batchStartTime, STATE_EXPIRED_LABEL_VALUE);
    } catch (final MongoInterruptedException | InterruptedException pException) {
      Thread.currentThread().interrupt();
      LOG.warn("Interrupted operation on EXPIRED pipelines => shutting down early", pException);
    } finally {
      final int skipped = executor.shutdownNow().size();
      if (skipped != 0) {
        LOG.warn("Executor did not have a clean shutdown. {} tasks were aborted.", skipped);
      }
    }

    LOG.info(
        DataLakeLogsUtil.LOG_FORMAT,
        LogUtils.entries(DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM),
        "Cron job cleanupExpiredPipelines finished running");
  }

  /** Entry point for the CronJob that marks ACTIVE or PAUSED pipelines as DELETING. */
  public void deleteActiveOrPausedPipelines() {
    int numPipelinesProcessed = 0;
    int numPipelinesDeleted = 0;

    try {
      final List<IngestionPipeline> activeOrPausedPipelines =
          _ingestionPipelineDao.findActiveOrPaused(
              DELETE_ACTIVE_OR_PAUSED_INGESTION_PIPELINES_BATCH_SIZE);

      if (activeOrPausedPipelines.isEmpty()) {
        LOG.info("No active or paused pipelines found for deletion.");
        return;
      }

      for (final IngestionPipeline pipeline : activeOrPausedPipelines) {
        // Catch exceptions per pipeline to ensure one failure doesn't block the entire batch.
        try {
          final boolean isPipelineDeleted = markPipelineForDeletion(pipeline);
          if (isPipelineDeleted) {
            numPipelinesDeleted++;
          }
          numPipelinesProcessed++;
        } catch (Exception pException) {
          LOG.error(
              "Unexpected error while deleting ingestion pipeline {}, group {}",
              pipeline.getId(),
              pipeline.getGroupId(),
              pException);
        }
      }
    } catch (final Exception pException) {
      LOG.error("Unexpected error while deleting active or paused pipelines", pException);
    } finally {
      LOG.info(
          DataLakeLogsUtil.LOG_FORMAT,
          LogUtils.entries(DataLakeLogsUtil.FieldDefs.TEAM, DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM),
          String.format(
              "Cron job deleteAllActiveOrPausedPipeline finished running. Deleted %d out of %d"
                  + " ingestion pipelines",
              numPipelinesDeleted, numPipelinesProcessed));
    }
  }

  private boolean markPipelineForDeletion(final IngestionPipeline pIngestionPipeline)
      throws SvcException {
    final ObjectId groupId = pIngestionPipeline.getGroupId();
    final Group group = _groupSvc.findById(groupId);
    final ObjectId pipelineId = pIngestionPipeline.getId();
    final String pipelineName = pIngestionPipeline.getName();

    // Active/paused ingestion pipelines will be deleted by enabling a feature flag on
    // September 30, 2025.
    final boolean isEOLEnabled =
        FeatureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_DISABLE_PIPELINES,
            _appSettings,
            null,
            group);

    final StructuredArgument logEntries =
        LogUtils.entries(
            DataLakeLogsUtil.FieldDefs.TEAM,
            DataLakeLogsUtil.ONLINE_ARCHIVE_TEAM,
            DataLakeLogsUtil.FieldDefs.GROUP_ID,
            groupId,
            DataLakeLogsUtil.IngestionPipelineFieldDefs.PIPELINE_ID,
            pipelineId,
            DataLakeLogsUtil.IngestionPipelineFieldDefs.PIPELINE_NAME,
            pipelineName);

    if (isEOLEnabled) {
      LOG.info(
          DataLakeLogsUtil.LOG_FORMAT,
          logEntries,
          "Deleting pipeline due to Data Lake EOL feature flag"
              + " (mms.featureFlag.dataLakeEOL.disablePipelines) enablement");

      final Optional<ObjectId> deletedPipeline =
          _ingestionPipelineSvc.markIngestionPipelineAsDisabled(
              groupId, pipelineName, AuditInfoHelpers.fromInternal());

      if (deletedPipeline.isPresent()) {
        LOG.info(
            DataLakeLogsUtil.LOG_FORMAT, logEntries, "Successfully deleted ingestion pipeline");
        return true;
      } else {
        LOG.error(DataLakeLogsUtil.LOG_FORMAT, logEntries, "Failed to delete ingestion pipeline");
      }
    } else {
      LOG.info(
          DataLakeLogsUtil.LOG_FORMAT,
          logEntries,
          "Skipping pipeline deletion due to Data Lake EOL feature flag"
              + " (mms.featureFlag.dataLakeEOL.disablePipelines) disablement");
    }
    return false;
  }

  public Map<ObjectId, List<NDSDataSetDLS>> groupDatasetsByGroupId(
      final List<NDSDataSetDLS> pDatasets) {
    final Map<ObjectId, List<NDSDataSetDLS>> datasetsByGroupId = new HashMap<>();
    for (final NDSDataSetDLS dataset : pDatasets) {
      datasetsByGroupId.putIfAbsent(dataset.getGroupId(), new LinkedList<>());
      datasetsByGroupId.get(dataset.getGroupId()).add(dataset);
    }
    return datasetsByGroupId;
  }

  public void disableDataSetsAndUpdatePipelineDeletedDate(final IngestionPipeline pPipeline) {
    List<NDSDataSetDLS> batchedDataSets =
        _ndsDataSetDao.findCreatedOrActiveDLSDataSetsForPipeline(
            pPipeline.getId(), DATA_SETS_DLS_OPERATION_BATCH_SIZE);

    while (!batchedDataSets.isEmpty()) {
      LOG.info(
          "disabling a batch of {} datasets for ingestion pipeline {}, group {}",
          batchedDataSets.size(),
          pPipeline.getId(),
          pPipeline.getGroupId());
      final boolean dlsOperationSuccessful = disableDataSetsInDLS(pPipeline, batchedDataSets);
      if (!dlsOperationSuccessful) {
        LOG.warn(
            "failed to disable data sets in DLS => exiting without performing NDS operations, group"
                + " {}",
            pPipeline.getGroupId());
        return;
      }
      _ndsDataSetDao.markDataSetsAsDisabled(batchedDataSets);
      DLSPromMetricsUtil.incrementDatasetsProcessedByStateCounter(
          batchedDataSets.size(), NDSDataSet.State.DELETING);
      batchedDataSets =
          _ndsDataSetDao.findCreatedOrActiveDLSDataSetsForPipeline(
              pPipeline.getId(), DATA_SETS_DLS_OPERATION_BATCH_SIZE);
    }

    _ingestionPipelineDao.updateIngestionPipelineDeletedDate(pPipeline.getId());
    DLSPromMetricsUtil.incrementPipelinesProcessedByStateCounter(
        1, IngestionPipeline.State.DELETING);
  }

  public void ensureNoDataSetsForExpiredPipelineAndMarkDestroyed(
      final IngestionPipeline pPipeline) {
    final long countNotDeletedDataSets =
        _ndsDataSetDao.countDLSDataSetsForPipelineWithStates(
            pPipeline.getId(),
            NDSDataSet.State.CREATED,
            NDSDataSet.State.ACTIVE,
            NDSDataSet.State.DELETING);
    if (countNotDeletedDataSets > 0) {
      LOG.info(
          "expired pipeline with id: '{}' has {} non-DELETED data sets => exiting without"
              + " performing NDS operations, group {}",
          pPipeline.getId(),
          countNotDeletedDataSets,
          pPipeline.getGroupId());
      return;
    }
    LOG.info("marking pipeline DESTROYED: {}, group {}", pPipeline.getId(), pPipeline.getGroupId());
    _ingestionPipelineDao.markIngestionPipelineAsDestroyed(pPipeline.getId());
    DLSPromMetricsUtil.incrementPipelinesProcessedByStateCounter(
        1, IngestionPipeline.State.DELETED);
  }

  public void destroyExpiredDataSets(final ObjectId pGroupId, final List<NDSDataSetDLS> pDataSets) {
    final List<List<NDSDataSetDLS>> batchedDataSets =
        Lists.partition(pDataSets, DATA_SETS_DLS_OPERATION_BATCH_SIZE);

    for (List<NDSDataSetDLS> dataSets : batchedDataSets) {
      LOG.info(
          "destroying a batch of {} out of {} expired datasets for group {}",
          dataSets.size(),
          pDataSets.size(),
          pGroupId);
      final boolean dlsOperationSuccessful = destroyDataSetsInDLS(pGroupId, dataSets);
      if (!dlsOperationSuccessful) {
        LOG.warn(
            "failed to destroy data sets in DLS => exiting without performing NDS operations, group"
                + " {}",
            pGroupId);
        return;
      }
      _ndsDataSetDao.markDataSetsAsDestroyed(dataSets);
      DLSPromMetricsUtil.incrementDatasetsProcessedByStateCounter(
          dataSets.size(), NDSDataSet.State.DELETED);
    }
  }

  public boolean disableDataSet(final NDSDataSetDLSIngestion pDataSet) {
    final boolean dlsOperationSuccessful = disableDataSetInDLS(pDataSet.getGroupId(), pDataSet);
    if (!dlsOperationSuccessful) {
      LOG.warn(
          "failed to disable data set in DLS => exiting without performing NDS operations, group"
              + " {}",
          pDataSet.getGroupId());
      return false;
    }

    _ndsDataSetDao.markDataSetAsDisabled(pDataSet);
    DLSPromMetricsUtil.incrementDatasetsProcessedByStateCounter(1, NDSDataSet.State.DELETING);
    return true;
  }

  public Optional<IngestionPipelineRun> markPipelineRunForDeletion(
      final IngestionPipelineRun pIngestionPipelineRun) throws SvcException {
    final Optional<NDSDataSetDLSIngestion> dataSet =
        Optional.ofNullable(pIngestionPipelineRun.getDataSetId())
            .flatMap(_ndsDataSetDao::findById)
            .map(NDSDataSet::toDBObject)
            .map(NDSDataSetDLSIngestion::new);

    // mark data set for deletion if present
    if (dataSet.isPresent()) {
      final boolean disableSuccessful = disableDataSet(dataSet.get());
      if (!disableSuccessful) {
        throw new SvcException(
            NDSErrorCode.DLS_UNEXPECTED_ERROR,
            String.format(
                "Disabling dataset %s unsuccessful: group %s, pipeline %s, run %s",
                dataSet.get().getName(),
                pIngestionPipelineRun.getGroupId(),
                pIngestionPipelineRun.getPipelineId(),
                pIngestionPipelineRun.getId()));
      }
    } else {
      LOG.warn(
          "dataset not found: group {}, pipeline {}, run {}",
          pIngestionPipelineRun.getGroupId(),
          pIngestionPipelineRun.getPipelineId(),
          pIngestionPipelineRun.getId());
    }

    // update pipeline run state
    return _ingestionPipelineRunDao.setState(
        pIngestionPipelineRun, IngestionPipelineRun.State.DATASET_DELETED);
  }

  @VisibleForTesting
  protected boolean disableDataSetInDLS(
      final ObjectId pGroupId, final NDSDataSetDLSIngestion pDataSet) {
    if (pDataSet == null) {
      return true;
    }

    try {
      _dataLakeAdminApiClient.disableDataSets(pGroupId, List.of(pDataSet));
      return true;
    } catch (final DataLakeAdminApiException pException) {
      LOG.error(
          "encountered exception disabling data set {} for group {}",
          pDataSet.getName(),
          pDataSet.getGroupId(),
          pException);
    }
    return false;
  }

  @VisibleForTesting
  protected boolean disableDataSetsInDLS(
      final IngestionPipeline pPipeline, final List<NDSDataSetDLS> pDataSets) {
    if (pDataSets.size() == 0) {
      return true;
    }

    try {
      _dataLakeAdminApiClient.disableDataSets(pPipeline.getGroupId(), pDataSets);
      return true;
    } catch (final DataLakeAdminApiException pException) {
      LOG.error(
          "encountered exception disabling data sets for pipeline {} for group {}",
          pPipeline.getId(),
          pPipeline.getGroupId(),
          pException);
    }
    return false;
  }

  @VisibleForTesting
  protected boolean destroyDataSetsInDLS(
      final ObjectId pGroupId, final List<NDSDataSetDLS> pDataSets) {
    if (pDataSets.isEmpty()) {
      return true;
    }

    try {
      _dataLakeAdminApiClient.destroyDataSets(pGroupId, pDataSets);
      return true;
    } catch (final DataLakeAdminApiException pException) {
      LOG.error("encountered exception destroying data sets for group {}", pGroupId, pException);
    }
    return false;
  }

  private void setPipelinesBatchByStateGauge(final int pSize, final String pState) {
    PromMetricsSvc.setGauge(PIPELINES_BY_STATE_GAUGE, pSize, pState);
  }

  private void setDatasetsBatchByStateGauge(final int pSize, final String pState) {
    PromMetricsSvc.setGauge(DATA_SETS_BY_STATE_GAUGE, pSize, pState);
  }

  private void observeIngestionPipelinesProcessedByStateDuration(
      final Date pStartTime, final String pState) {
    final long runtimeMS = new Date().getTime() - pStartTime.getTime();
    PromMetricsSvc.recordTimer(PIPELINES_PROCESSED_BY_STATE_DURATION_SUMMARY, runtimeMS, pState);
  }

  private void observeDatasetsProcessedByStateDuration(final Date pStartTime, final String pState) {
    final long runtimeMS = new Date().getTime() - pStartTime.getTime();
    PromMetricsSvc.recordTimer(DATA_SETS_PROCESSED_BY_STATE_DURATION_SUMMARY, runtimeMS, pState);
  }

  private int getDeletedIngestionPipelineQueryLimit() {
    return _appSettings.getDeletedIngestionPipelinesQueryLimit(
        DELETED_INGESTION_PIPELINES_QUERY_DEFAULT_LIMIT);
  }

  private int getExpiredIngestionPipelineQueryLimit() {
    return _appSettings.getExpiredIngestionPipelinesQueryLimit(
        EXPIRED_INGESTION_PIPELINES_QUERY_DEFAULT_LIMIT);
  }

  private int getExpiredDatasetQueryLimit() {
    return _appSettings.getExpiredDatasetsQueryLimit(EXPIRED_DATA_SETS_QUERY_DEFAULT_LIMIT);
  }

  private Duration getDeletedIngestionPipelineGracePeriod() {
    if (_appSettings.getAppEnv().isProdOrProdGovOrInternal()) {
      // in prod, we don't allow overriding the grace period. it is currently always 5 days.
      return Duration.ofMinutes(DELETED_INGESTION_PIPELINE_DEFAULT_GRACE_PERIOD);
    }
    return _appSettings.getDeletedIngestionPipelineGracePeriod(
        DELETED_INGESTION_PIPELINE_DEFAULT_GRACE_PERIOD);
  }

  private Duration getDeletedDatasetGracePeriod() {
    if (_appSettings.getAppEnv().isProdOrProdGovOrInternal()) {
      // in prod, we don't allow overriding the grace period. it is currently always 5 days.
      return Duration.ofMinutes(DELETED_DATA_SET_DEFAULT_GRACE_PERIOD);
    }
    return _appSettings.getDeletedDatasetGracePeriod(DELETED_DATA_SET_DEFAULT_GRACE_PERIOD);
  }

  private ExecutorService getThreadPoolExecutor(final int pThreadPoolSize) {
    return Executors.newFixedThreadPool(pThreadPoolSize);
  }
}
