package com.xgen.svc.mms.svc.alert.check.system;

import com.xgen.cloud.alerts.checks.common._public.svc.Result;
import com.xgen.cloud.alerts.checks.system._public.svc.SystemAlertCheck;
import com.xgen.cloud.alerts.context._public.svc.AbstractSystemAlertProcessingContext;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.customermetrics._public.model.query.RangeQueryRequest;
import com.xgen.cloud.customermetrics._public.view.PromQLResponseView;
import com.xgen.cloud.customermetrics._public.view.PromQLResponseView.Data;
import com.xgen.cloud.monitoring.metrics._public.model.activity.SystemMaasAlert;
import com.xgen.cloud.monitoring.metrics._public.model.activity.SystemMaasAlert.Builder;
import com.xgen.cloud.monitoring.metrics._public.model.activity.SystemMaasAlertConfig;
import com.xgen.cloud.monitoring.metrics._public.model.activity.SystemMaasEvent;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SystemMaasQueryIsTrueAlertCheck
    extends SystemAlertCheck<
        SystemMaasAlert,
        SystemMaasAlert.Builder,
        SystemMaasAlertConfig,
        SystemMaasEvent.Type,
        Void,
        PromQLResponseView.Result> {

  private static final Logger LOG = LoggerFactory.getLogger(SystemMaasQueryIsTrueAlertCheck.class);
  private static final Counter MAAS_QUERY_FAILURES_COUNTER =
      Counter.build()
          .name("mms_monitoring_system_maas_alert_maas_queries")
          .help("Number of queries issued to MaaS by SystemMaasQueryIsTrueAlertCheck")
          .labelNames("status")
          .register();

  private static final Counter MAAS_QUERY_EVALUATION_COUNTER =
      Counter.build()
          .name("mms_monitoring_system_maas_alert_evaluations")
          .help("Number of evaluated results from MaaS by SystemMaasQueryIsTrueAlertCheck")
          .labelNames("status")
          .register();
  private static final String GRAFANA_LINK_TEMPLATE =
      "%s/explore?panes={\"1\":{\"datasource\":\"%s\",\"queries\":[{\"refId\":\"1\",\"expr\":\"%s\"}]}}&orgId=98&schemaVersion=1";

  /* Lookback multiplier ensures we find targets from previous alerts rounds so that we can close
   * them out. If we do not return the same set of targets from previous alerts rounds for an alert
   * that was opened on a previous round, the alert gets canceled instead of closed.
   */
  static final int LOOKBACK_MULTIPLIER = 3;
  static final String ALERT_QUERY_STEP = "mms.alerts.SystemMaasQueryIsTrueAlertCheck.step";
  static final String ALERT_QUERY_TIMEOUT = "mms.alerts.SystemMaasQueryIsTrueAlertCheck.timeout";
  static final String DEFAULT_STEP = "30s";
  static final String DEFAULT_TIMEOUT = "10s";

  @Inject
  protected SystemMaasQueryIsTrueAlertCheck(final AppSettings pAppSettings) {
    super(
        pAppSettings,
        SystemMaasEvent.Type.MAAS_QUERY_EVALUATED_TRUE,
        SystemMaasAlert.class,
        SystemMaasAlert.Builder.class,
        SystemMaasAlertConfig.class,
        Void.class,
        PromQLResponseView.Result.class);
  }

  @Override
  protected Collection<SystemMaasAlert> doFilterAlertsByTarget(
      final AbstractSystemAlertProcessingContext pContext,
      final Collection<SystemMaasAlert> pAlerts,
      final PromQLResponseView.Result pTarget) {
    final String targetSeriesName = getSeriesName(pTarget.getMetric());
    final Map<String, String> targetPublicLabels = getPublicLabels(pTarget.getMetric());
    return pAlerts.stream()
        .filter(
            alert ->
                (alert.getLabels() != null
                        ? alert.getLabels().equals(targetPublicLabels)
                        : targetPublicLabels == null)
                    && (alert.getSeriesName() != null
                        ? alert.getSeriesName().equals(targetSeriesName)
                        : targetSeriesName == null))
        .collect(Collectors.toList());
  }

  @Override
  protected void doInitAlertBuilder(
      final AbstractSystemAlertProcessingContext pContext,
      final Builder pBuilder,
      final SystemMaasAlertConfig pConfig,
      final PromQLResponseView.Result pTarget) {
    super.doInitAlertBuilder(pContext, pBuilder, pConfig, pTarget);
    pBuilder
        .labels(getPublicLabels(pTarget.getMetric()))
        .seriesName(getSeriesName(pTarget.getMetric()))
        .query(pConfig.getQuery())
        .grafanaLink(createGrafanaLink(pConfig))
        .title(pConfig.getTitle());
  }

  @Override
  protected Collection<PromQLResponseView.Result> doGetTargets(
      final AbstractSystemAlertProcessingContext pContext, final SystemMaasAlertConfig pConfig) {
    final long targetsLookbackSeconds = pConfig.getLookbackSeconds() * LOOKBACK_MULTIPLIER;
    final RangeQueryRequest request =
        new RangeQueryRequest.Builder(
                pConfig.getQuery(),
                UUID.fromString(pConfig.getUseCaseId()),
                Instant.now().minus(Duration.ofSeconds(targetsLookbackSeconds)).toString())
            .withEnd(Instant.now().toString())
            .withStep(_appSettings.getStrProp(ALERT_QUERY_STEP, DEFAULT_STEP))
            .withTimeout(_appSettings.getStrProp(ALERT_QUERY_TIMEOUT, DEFAULT_TIMEOUT))
            .build();

    LOG.atDebug()
        .addKeyValue("alertConfigId", pConfig.getId())
        .addKeyValue("lookbackSeconds", pConfig.getLookbackSeconds())
        .addKeyValue("query", pConfig.getQuery())
        .log("Attempting to run query for MaaS Alert Check in doGetTargets");

    try {
      final PromQLResponseView<Data> result = pContext.getMaasMetricsViaRangeQuery(request);
      LOG.atDebug()
          .addKeyValue("alertConfigId", pConfig.getId())
          .addKeyValue("targets returned", result.getData().getResult().size())
          .log("Successfully ran query for MaaS Alert Check in doGetTargets");
      MAAS_QUERY_FAILURES_COUNTER.labels("success").inc();
      return result.getData().getResult();
    } catch (Exception e) {
      /* An empty list of targets will prevent any targets from having their alerts canceled. We should
        only land here if we've been interrupted or MaaS is failing to respond in which case we don't
        want to update the state of any current alerts.
      */
      MAAS_QUERY_FAILURES_COUNTER.labels("failed").inc();
      LOG.atError()
          .addKeyValue("alertConfigId", pConfig.getId())
          .addKeyValue("exception", e)
          .log("Could not query MaaS for alertConfig");
      return List.of();
    }
  }

  @Override
  protected Result.WithoutState doRunCheck(
      final AbstractSystemAlertProcessingContext pContext,
      final SystemMaasAlertConfig pConfig,
      final PromQLResponseView.Result pTarget)
      throws Exception {
    final Date lookbackCutoff =
        new Date(
            Instant.now().minus(Duration.ofSeconds(pConfig.getLookbackSeconds())).toEpochMilli());

    final Result.WithoutState result =
        pTarget.getValues().stream()
                .anyMatch(
                    timestampAndValue -> {
                      try {
                        final Object timestamp = timestampAndValue.get(0);
                        long timestampAsEpochMillis;
                        if (timestamp instanceof Double dTimestamp) {
                          timestampAsEpochMillis =
                              (long) (dTimestamp * Duration.ofSeconds(1).toMillis());
                        } else if (timestamp instanceof Long lTimestamp) {
                          timestampAsEpochMillis = lTimestamp * Duration.ofSeconds(1).toMillis();
                        } else {
                          throw new Exception(
                              "Unrecognized timestamp format for MaaS response: "
                                  + timestamp.toString());
                        }
                        final Date date = new Date(timestampAsEpochMillis);
                        MAAS_QUERY_EVALUATION_COUNTER.labels("success").inc();
                        return date.after(lookbackCutoff);
                      } catch (Exception e) {
                        MAAS_QUERY_EVALUATION_COUNTER.labels("failed").inc();
                        LOG.atError()
                            .addKeyValue("alertConfigId", pConfig.getId())
                            .addKeyValue("exception", e)
                            .log("Could not process query results for alertConfig");
                        return false;
                      }
                    })
            ? Result.WithoutState.HAS_ALERT
            : Result.WithoutState.NO_ALERT;

    LOG.atDebug()
        .addKeyValue("alertConfigId", pConfig.getId())
        .addKeyValue("result", result)
        .addKeyValue("labels", pTarget.getMetric())
        .log("Evaluated target for MaaS alert check");

    return result;
  }

  private Map<String, String> getPublicLabels(Map<String, String> labels) {
    final Map<String, String> withoutInternalLabels = new HashMap<>();
    labels
        .keySet()
        .forEach(
            label -> {
              if (!label.startsWith("__")) {
                withoutInternalLabels.put(label, labels.get(label));
              }
            });
    return withoutInternalLabels;
  }

  private String getSeriesName(Map<String, String> labels) {
    return labels.get("__name__");
  }

  private String createGrafanaLink(final SystemMaasAlertConfig pConfig) {
    final String internalGrafanaUrl = _appSettings.getStrProp("maas.internalGrafana.url");
    final String dataSource =
        _appSettings.getStrProp(
            String.format(
                "maas.internalGrafana.usecaseId.%s.datasourceId", pConfig.getUseCaseId()));
    if (StringUtils.isEmpty(internalGrafanaUrl) || StringUtils.isEmpty(dataSource)) {
      LOG.warn(
          "Grafana URL: {} or DataSource ID: {} is not configured, returning empty Grafana link.",
          internalGrafanaUrl,
          dataSource);
      return StringUtils.EMPTY;
    }

    final String escapedQuery = StringEscapeUtils.escapeJson(pConfig.getQuery());
    return String.format(GRAFANA_LINK_TEMPLATE, internalGrafanaUrl, dataSource, escapedQuery);
  }
}
