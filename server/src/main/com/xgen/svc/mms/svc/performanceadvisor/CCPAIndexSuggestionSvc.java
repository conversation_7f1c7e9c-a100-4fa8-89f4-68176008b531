package com.xgen.svc.mms.svc.performanceadvisor;

import static com.xgen.cloud.common.model._public.error.CommonErrorCode.SERVER_ERROR;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.cloud.performanceadvisor._private.dao.PARollupShapesDao.findBusiestNamespacesSinceUntilInDao;
import static com.xgen.cloud.performanceadvisor._public.jobs.CollStatsCollectionJob.COLLSTATS_COLLECTION_TIMEOUT_MILLIS;
import static com.xgen.cloud.performanceadvisor._public.jobs.PARollupScheduler.DEFAULT_PA_ROLLUP_DELAY_BUFFER_MILLIS;
import static com.xgen.cloud.performanceadvisor._public.util.PerformanceAdvisorUtils.filterNamespaces;
import static com.xgen.svc.mms.model.explorer.JobSource.CREATE_INDEX_SUGGESTIONS;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.NO_EXPERIMENT_NAME;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_8_0_RUNS_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_8_0_SLOW_LOG_INDEXES_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_MAX_IMPACT_SCORE;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_MERGED_INDEXES_NOT_IN_SLOW_LOG_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_MERGED_SUGGESTED_INDEXES_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_QS_SUGGESTED_INDEXES_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_QUERY_STATS_INDEXES_NOT_IN_SLOW_LOG_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_RUNS_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_RUN_TIME_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_SLOW_LOG_INDEXES_NOT_IN_MERGED_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_SLOW_LOG_INDEXES_NOT_IN_QUERY_STATS_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_SLOW_LOG_SUGGESTED_INDEXES_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_ZERO_MERGED_SUGGESTIONS_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_ZERO_QS_SUGGESTIONS_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_ZERO_SL_SUGGESTIONS_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.PERFORMANCE_ADVISOR_ZERO_SUGGESTIONS_TOTAL;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.filterExcludedIndexesFromShapes;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.storeIndexSuggestions;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.storePARunResult;
import static com.xgen.svc.mms.svc.performanceadvisor.CreateIndexSuggestionsSvc.submitSegmentEvent;
import static com.xgen.svc.mms.svc.performanceadvisor.PerformanceAdvisorUtils.getMongos;
import static com.xgen.svc.mms.svc.performanceadvisor.PerformanceAdvisorUtils.getNamespacesSet;
import static com.xgen.svc.mms.svc.performanceadvisor.suggestionengine.BaseIndexSuggestionEngine.SUGGESTED_INDEX_LIMIT;
import static com.xgen.svc.mms.svc.performanceadvisor.suggestionengine.BaseIndexSuggestionEngine.getFilteredIndexSuggestions;
import static com.xgen.svc.mms.svc.performanceadvisor.suggestionengine.SizeBasedUIIndexSuggestionEngine.COLLSTATS_COLLECTION_FAILED_TOTAL;
import static java.time.Duration.ofDays;
import static java.time.Duration.ofHours;
import static java.time.Instant.now;
import static java.util.Collections.reverseOrder;
import static java.util.stream.Collectors.toList;
import static net.logstash.logback.argument.StructuredArguments.entries;

import com.mongodb.ReadPreference;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.atm.agentjobs._public.svc.AgentJobSvc;
import com.xgen.cloud.atm.core._public.svc.AutomationAgentSupportSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.explorer._public.model.CollectedIndexes;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob.Status;
import com.xgen.cloud.common.jobqueue._public.svc.AgentJobsProcessorSvc;
import com.xgen.cloud.common.model._public.annotation.MethodCallPromTimed;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.Namespace;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.sharedpool._public.sharedpool.SharedWorkerPool;
import com.xgen.cloud.common.util._public.util.AgentVersion;
import com.xgen.cloud.common.util._public.util.LogUtils;
import com.xgen.cloud.common.util._public.util.SetUtils;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.logs._private.dao.SlowQueryLogDao;
import com.xgen.cloud.monitoring.logs._public.consumers.PARollupShapeConsumer;
import com.xgen.cloud.monitoring.logs._public.consumers.QueryStatsSummaryConsumer;
import com.xgen.cloud.monitoring.logs._public.consumers.SizeBasedWeightCalculator;
import com.xgen.cloud.monitoring.logs._public.consumers.SlowQueryPiiRedactor;
import com.xgen.cloud.monitoring.logs._public.model.PARollupShape;
import com.xgen.cloud.monitoring.logs._public.svc.QueryProfilerSvc;
import com.xgen.cloud.monitoring.metrics._public.model.MetricsErrorCode;
import com.xgen.cloud.monitoring.querystats._public.model.QueryStatsSummary;
import com.xgen.cloud.monitoring.querystats._public.svc.QueryStatsSvc;
import com.xgen.cloud.monitoring.topology._public.model.ClusterType;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.svc.HostClusterSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.monitoring.tsstrategy._private.dao.AbstractDaoStrategy.RollupType;
import com.xgen.cloud.monitoring.tsstrategy._private.dao.Dao;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.performanceadvisor._private.dao.CollectionStatsDao;
import com.xgen.cloud.performanceadvisor._private.dao.PARollupShapesDao;
import com.xgen.cloud.performanceadvisor._private.dao.PARollupShapesDao.PARollupShapesDaosByGranularity;
import com.xgen.cloud.performanceadvisor._public.jobs.CollStatsCollectionJob;
import com.xgen.cloud.performanceadvisor._public.model.CollectionStats;
import com.xgen.cloud.performanceadvisor._public.model.IndexSuggestionEngineType;
import com.xgen.cloud.performanceadvisor._public.svc.PARollupAccumulator.AccumulatorType;
import com.xgen.cloud.performanceadvisor._public.svc.PerformanceAdvisorRunResultSvc;
import com.xgen.cloud.performanceadvisor._public.svc.PerformanceAdvisorTrackingSvc;
import com.xgen.cloud.performanceadvisor._public.svc.PerformanceAdvisorTrackingSvc.WorkflowType;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.view.monitoring.performanceadvisor.ApiPerformanceAdvisorResponseView;
import com.xgen.svc.mms.dao.performanceadvisor.PredicatesHashToPredicatesDao;
import com.xgen.svc.mms.dao.performanceadvisor.autoindexing.AutoIndexingConfigDao;
import com.xgen.svc.mms.model.performanceadvisor.Feedback;
import com.xgen.svc.mms.model.performanceadvisor.IndexSuggestionResults;
import com.xgen.svc.mms.model.performanceadvisor.PerformanceAdvisorIndexImpactedShapes;
import com.xgen.svc.mms.model.performanceadvisor.PerformanceAdvisorResponseMetadata;
import com.xgen.svc.mms.model.performanceadvisor.PerformanceAdvisorShape;
import com.xgen.svc.mms.model.performanceadvisor.autoindexing.AutoIndexingConfig;
import com.xgen.svc.mms.model.performanceadvisor.profiler.SuggestedIndex;
import com.xgen.svc.mms.model.performanceadvisor.profiler.SuggestedIndexesAndShapes;
import com.xgen.svc.mms.res.view.performanceadvisor.CreateIndexesResponseView;
import com.xgen.svc.mms.svc.explorer.DataExplorerSvc;
import com.xgen.svc.mms.svc.performanceadvisor.suggestionengine.BaseIndexSuggestionEngine;
import com.xgen.svc.nds.svc.NDSLookupSvc;
import io.prometheus.client.Counter;
import io.prometheus.client.Summary;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CCPAIndexSuggestionSvc {
  private static final Logger LOG = LoggerFactory.getLogger(CCPAIndexSuggestionSvc.class);

  private static final int DEFAULT_BUSIEST_NAMESPACE_LIMIT = 100;
  static final int DEFAULT_MAX_QUERY_STATS_INDEXES = 15;
  static final int DEFAULT_MAX_SLOW_LOG_INDEXES = 10;

  private final AppSettings _settings;
  private final PARollupShapesDao _paRollupShapesDao;
  private final PerformanceAdvisorSvc _performanceAdvisorSvc;
  private final PerformanceAdvisorFeedbackSvc _paFeedbackSvc;
  private final PerformanceAdvisorRunResultSvc _paRunResultSvc;
  private final PerformanceAdvisorTrackingSvc _performanceAdvisorTrackingSvc;
  private final DataExplorerSvc _dataExplorerSvc;
  private final PredicatesHashToPredicatesDao _predicatesHashToPredicatesDao;
  private final SlowQueryLogDao _slowQueryLogDao;
  private final CollectionStatsDao _collectionStatsDao;
  private final AgentJobSvc _agentJobSvc;
  private final AgentJobsProcessorSvc _agentJobsProcessorSvc;
  private final StatsCollectionSvc _statsCollectionSvc;
  private final AutomationAgentSupportSvc _automationAgentSupportSvc;
  private final HostSvc _hostSvc;
  private final HostClusterSvc _hostClusterSvc;
  private final AuthzSvc _authzSvc;
  private final NDSLookupSvc _ndsLookupSvc;
  private final AutoIndexingConfigDao _autoIndexingConfigDao;
  private final SegmentEventSvc _segmentEventSvc;
  private final SharedWorkerPool _sharedWorkerPool;
  private final AppSettings _appSettings;
  private final int _shapeLimit;
  private final Long _rollupDelayMillis;
  private final QueryStatsSvc _queryStatsSvc;
  private final QueryProfilerSvc _queryProfilerSvc;

  private static final Summary CCPA_GET_INDEX_ADVICE_TIME_TOTAL =
      Summary.build()
          .name("ccpa_get_index_advice_time_total")
          .help("CCPA total time to compute suggested indexes")
          .quantile(0.99, 0.001)
          .register();

  private static final Counter CCPA_ERROR_TOTAL =
      Counter.build()
          .name("ccpa_errors_total")
          .help("CCPA number of warns/errors total")
          .labelNames("errorType")
          .register();

  @Inject
  public CCPAIndexSuggestionSvc(
      final AppSettings pSettings,
      final PARollupShapesDao pPARollupShapesDao,
      final PerformanceAdvisorSvc pPerformanceAdvisorSvc,
      final PerformanceAdvisorFeedbackSvc pPerformanceAdvisorFeedbackSvc,
      final PerformanceAdvisorRunResultSvc pPerformanceAdvisorRunResultSvc,
      final PerformanceAdvisorTrackingSvc pPerformanceAdvisorTrackingSvc,
      final DataExplorerSvc pDataExplorerSvc,
      final PredicatesHashToPredicatesDao pPredicatesHashToPredicatesDao,
      final SlowQueryLogDao pSlowQueryLogDao,
      final CollectionStatsDao pCollectionStatsDao,
      final AgentJobSvc pAgentJobSvc,
      final AgentJobsProcessorSvc pAgentJobsProcessorSvc,
      final StatsCollectionSvc pStatsCollectionSvc,
      final AutomationAgentSupportSvc pAutomationAgentSupportSvc,
      final HostSvc pHostSvc,
      final HostClusterSvc pHostClusterSvc,
      final AuthzSvc pAuthzSvc,
      final NDSLookupSvc pNDSLookupSvc,
      final AutoIndexingConfigDao pAutoIndexingConfigDao,
      final SegmentEventSvc pSegmentEventSvc,
      final SharedWorkerPool pSharedWorkerPool,
      final QueryStatsSvc pQueryStatsSvc,
      final QueryProfilerSvc pQueryProfilerSvc) {
    _paRollupShapesDao = pPARollupShapesDao;
    _performanceAdvisorSvc = pPerformanceAdvisorSvc;
    _paFeedbackSvc = pPerformanceAdvisorFeedbackSvc;
    _paRunResultSvc = pPerformanceAdvisorRunResultSvc;
    _performanceAdvisorTrackingSvc = pPerformanceAdvisorTrackingSvc;
    _dataExplorerSvc = pDataExplorerSvc;
    _predicatesHashToPredicatesDao = pPredicatesHashToPredicatesDao;
    _slowQueryLogDao = pSlowQueryLogDao;
    _collectionStatsDao = pCollectionStatsDao;
    _agentJobSvc = pAgentJobSvc;
    _agentJobsProcessorSvc = pAgentJobsProcessorSvc;
    _statsCollectionSvc = pStatsCollectionSvc;
    _automationAgentSupportSvc = pAutomationAgentSupportSvc;
    _hostSvc = pHostSvc;
    _hostClusterSvc = pHostClusterSvc;
    _authzSvc = pAuthzSvc;
    _settings = pSettings;
    _ndsLookupSvc = pNDSLookupSvc;
    _autoIndexingConfigDao = pAutoIndexingConfigDao;
    _segmentEventSvc = pSegmentEventSvc;
    _sharedWorkerPool = pSharedWorkerPool;
    _appSettings = pSettings;
    _queryStatsSvc = pQueryStatsSvc;
    _queryProfilerSvc = pQueryProfilerSvc;

    final long flushTimeoutMillis =
        _settings.getLongProp(
            "mms.automation.settings.performanceadvisor.filebeat.mongodLogs.flushTimeoutMillis",
            300_000L);

    final long delayBufferMillis =
        _settings.getLongProp(
            "mms.monitoring.performanceadvisor.rollup.scheduler.delayBuffer",
            DEFAULT_PA_ROLLUP_DELAY_BUFFER_MILLIS);

    _rollupDelayMillis = flushTimeoutMillis + delayBufferMillis;
    _shapeLimit = pSettings.getIntProp("mms.monitoring.clusterCentricPA.maxShapes", 200_000);
  }

  private Triple<Host, ObjectId, ObjectId> getTargetHostReplSetIdAndClusterId(
      final List<Host> pHosts, final HostCluster pCluster) throws SvcException {
    final Host targetHost;
    final ObjectId replSetId;
    final ObjectId clusterId;

    // standalone
    if (pCluster == null) {
      targetHost = pHosts.get(0);
      replSetId = null;
      clusterId = null;
    } else if (pCluster.getType().equals(ClusterType.REPLICA_SET)) {
      targetHost = getTargetHostForReplicaSet(pHosts);
      replSetId = pCluster.getClusterId();
      clusterId = null;
    } else if (pCluster.getType().equals(ClusterType.SHARDED_REPLICA_SET)) {
      targetHost = getMongos(pCluster);
      replSetId = null;
      clusterId = pCluster.getClusterId();
    } else {
      CCPA_ERROR_TOTAL.labels("invalidClusterType").inc();
      LOG.error(
          "Invalid cluster type {} for cluster={}", pCluster.getType(), pCluster.getClusterId());
      return null;
    }
    return Triple.of(targetHost, replSetId, clusterId);
  }

  public IndexSuggestionResults getIndexSuggestionResults(
      final List<Host> pHosts,
      final HostCluster pCluster,
      final Host pTargetHost,
      final Group pGroup,
      final Instant pSince,
      final Instant pUntil,
      final AppUser pUser,
      final Organization pOrg,
      final IndexSuggestionEngineType pIndexSuggestionEngineType,
      final RollupType pRollupType,
      final List<String> pNamespaces,
      final boolean forceNoPII)
      throws SvcException {
    // exclude non data bearing hosts
    final List<Host> dataBearingHosts =
        pHosts.stream()
            .filter(
                host ->
                    !host.getIsArbiter() && !host.getIsMongos() && !host.isDedicatedConfigServer())
            .toList();

    final List<Host> hostsWithMatchingDBVersion =
        dataBearingHosts.stream()
            .filter(
                host -> {
                  try {
                    _performanceAdvisorSvc.checkDbVersion(host);
                    return true;
                  } catch (final SvcException e) {
                    LOG.error(
                        "Error checking db version for host {}",
                        LogUtils.entries("hostId", host.getId(), "groupId", pGroup.getId()),
                        e);
                    return false;
                  }
                })
            .toList();

    _performanceAdvisorSvc.checkAgentVersion(
        pGroup, pTargetHost, PerformanceAdvisorSvc::isSupportedAgentVersion);

    final boolean agentSupportsCollStatsCollection = checkAgentSupportsCollStatsCollection(pGroup);
    if (!agentSupportsCollStatsCollection) {
      return null;
    }

    final List<String> identifiers;
    if (pRollupType.equals(RollupType.HOST)) {
      identifiers =
          hostsWithMatchingDBVersion.stream()
              .map(host -> AccumulatorType.HOST.getKey() + "_" + host.getId())
              .collect(Collectors.toList());
    } else {
      identifiers = List.of(AccumulatorType.CLUSTER.getKey() + "_" + pCluster.getClusterId());
    }

    final List<PARollupShapesDaosByGranularity> daosByGranularity =
        _paRollupShapesDao.getRollupShapeDaosAtLowestGranularities(pSince, pUntil, pRollupType);

    final Map<String, Double> avgObjSizes;
    if (pIndexSuggestionEngineType.equals(IndexSuggestionEngineType.CCPA_ALERTS)) {
      avgObjSizes = getAvgObjSizesForAlerts(identifiers, pTargetHost, daosByGranularity, pGroup);
    } else {
      avgObjSizes =
          getAvgObjSizes(
              pUser,
              identifiers,
              pTargetHost,
              pGroup,
              pNamespaces,
              pIndexSuggestionEngineType,
              daosByGranularity);
    }

    final boolean canViewPii = !forceNoPII && _authzSvc.hasPiiViewAccess(pUser, pGroup);
    final SlowQueryPiiRedactor piiRedactor =
        new SlowQueryPiiRedactor(pUser, pGroup, canViewPii, false);

    final PARollupShapeConsumer consumer =
        new PARollupShapeConsumer(
            _predicatesHashToPredicatesDao,
            _slowQueryLogDao,
            piiRedactor,
            new SizeBasedWeightCalculator(),
            pUntil.toEpochMilli() - pSince.toEpochMilli(),
            avgObjSizes,
            _appSettings);

    final List<Future<List<PARollupShape>>> paRollupShapeListFutures = new ArrayList<>();

    daosByGranularity.forEach(
        (daos) -> {
          paRollupShapeListFutures.add(
              _sharedWorkerPool.submit(
                  () -> {
                    final var type = daos.getDaoTypeGranularity();
                    final String rollupType =
                        type.getType().name() + "_" + type.getGranularity().toString();
                    return _paRollupShapesDao.streamFor(
                        _shapeLimit,
                        daos.getDaos(),
                        rollupType,
                        daos.getStartTime(),
                        daos.getEndTime(),
                        identifiers,
                        Duration.ofSeconds(30));
                  },
                  pGroup.getId(),
                  LOG));
        });

    paRollupShapeListFutures.forEach(
        (f) -> _sharedWorkerPool.getFutureValue(f).forEach((consumer::accept)));

    final SuggestedIndexesAndShapes suggestedIndexesAndShapes = consumer.getResults();

    final List<CollectedIndexes> collectedIndexes;
    if (pIndexSuggestionEngineType.equals(IndexSuggestionEngineType.CCPA_ALERTS)) {
      collectedIndexes = getCollectedIndexesForAlerts(pTargetHost, pGroup);
    } else {
      collectedIndexes =
          getCollectedIndexesForUIRun(
              suggestedIndexesAndShapes, pGroup, pOrg, pTargetHost, pCluster, pUser);
    }
    return getFilteredIndexSuggestions(
        collectedIndexes,
        suggestedIndexesAndShapes,
        true,
        SUGGESTED_INDEX_LIMIT,
        pIndexSuggestionEngineType.name(),
        _shapeLimit);
  }

  public IndexSuggestionResults getIndexSuggestionResultsForQueryStats(
      final List<Host> pHosts,
      final HostCluster pCluster,
      final Host pTargetHost,
      final Group pGroup,
      final Instant pSince,
      final Instant pUntil,
      final AppUser pUser,
      final Organization pOrg,
      final IndexSuggestionEngineType pIndexSuggestionEngineType,
      final RollupType pRollupType,
      final List<String> pNamespaces,
      final boolean forceNoPII)
      throws SvcException {
    final boolean agentSupportsQueryStats = checkAgentSupportsQueryStats(pGroup);
    if (!agentSupportsQueryStats) {
      final AgentVersion agentVersion = _automationAgentSupportSvc.getMinAgentVersion(pGroup);
      throw new SvcException(
          MetricsErrorCode.AUTOMATION_AGENT_VERSION_NOT_SUPPORTED,
          agentVersion == null ? "N/A" : agentVersion.getAgentVersionString(),
          AgentVersion.MIN_QUERYSTATS_VERSION);
    }

    final String groupId = pGroup.getId().toString();

    // Get NDS cluster name
    String ndsClusterName = pCluster.getClusterName();
    final Optional<ClusterDescriptionId> clusterDescriptionId =
        _ndsLookupSvc.getCachedClusterDescriptionIdForHostname(pTargetHost.getName());
    if (clusterDescriptionId.isPresent()) {
      ndsClusterName = clusterDescriptionId.get().getClusterName();
    } else {
      throw new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND, ndsClusterName, groupId);
    }

    // we collect query stats from mongod hosts in replicaset,
    // but from mongos hosts in sharded clusters
    final List<Host> nonDedicatedConfigNonArbiterHosts =
        pHosts.stream()
            .filter(
                host ->
                    !host.getIsArbiter() && !host.getIsMongos() && !host.isDedicatedConfigServer())
            .toList();

    final List<String> identifiers;
    if (pRollupType.equals(RollupType.HOST)) {
      identifiers =
          nonDedicatedConfigNonArbiterHosts.stream()
              .map(host -> AccumulatorType.HOST.getKey() + "_" + host.getId())
              .collect(Collectors.toList());
    } else {
      identifiers = List.of(AccumulatorType.CLUSTER.getKey() + "_" + pCluster.getClusterId());
    }

    final List<PARollupShapesDaosByGranularity> daosByGranularity =
        _paRollupShapesDao.getRollupShapeDaosAtLowestGranularities(pSince, pUntil, pRollupType);

    final long until = pUntil.toEpochMilli();
    final long since = pSince.toEpochMilli();
    final List<String> hostNamePorts = List.of();
    final List<String> analyticHostnamePorts = List.of();
    List<QueryStatsSummary> summaries = new ArrayList<>();

    try {
      summaries =
          new ArrayList<>(
              _queryStatsSvc
                  .getQueryStatsSummaries(
                      groupId,
                      ndsClusterName,
                      since,
                      until,
                      List.of(),
                      List.of(),
                      hostNamePorts,
                      pNamespaces,
                      List.of(),
                      analyticHostnamePorts,
                      true,
                      true,
                      _appSettings.getIntProp(
                          "mms.monitoring.performanceadvisor.queryStatsSummaryLimit", 300))
                  .values());
    } catch (Exception pE) {
      throw new SvcException(SERVER_ERROR, pE);
    }

    final Set<String> namespaces = new HashSet<>();
    summaries.forEach(s -> namespaces.add(s.getNamespace()));
    namespaces.addAll(pNamespaces);

    final Map<String, Double> avgObjSizes;
    avgObjSizes =
        getAvgObjSizes(
            pUser,
            identifiers,
            pTargetHost,
            pGroup,
            namespaces.stream().toList(),
            pIndexSuggestionEngineType,
            daosByGranularity);

    final boolean canViewPii = !forceNoPII && _authzSvc.hasPiiViewAccess(pUser, pGroup);
    final SlowQueryPiiRedactor piiRedactor =
        new SlowQueryPiiRedactor(pUser, pGroup, canViewPii, false);

    final QueryStatsSummaryConsumer consumer =
        new QueryStatsSummaryConsumer(
            piiRedactor,
            new SizeBasedWeightCalculator(),
            pUntil.toEpochMilli() - pSince.toEpochMilli(),
            avgObjSizes,
            _queryProfilerSvc,
            _slowQueryLogDao,
            nonDedicatedConfigNonArbiterHosts,
            pGroup,
            pSince,
            pUntil,
            _settings);

    for (final QueryStatsSummary summary : summaries) {
      try {
        consumer.accept(summary, pGroup.getId(), pCluster.getClusterId());
      } catch (Exception e) {
        LOG.warn(
            "Error processing query stats summary row, skipping: {}",
            LogUtils.entries(
                "orgId", pOrg.getId(),
                "groupId", pGroup.getId(),
                "namespace", summary.getNamespace(),
                "clusterId", pCluster.getClusterId(),
                "queryShape", summary.getQueryShape()),
            e);
      }
    }

    final SuggestedIndexesAndShapes suggestedIndexesAndShapes = consumer.getResults();

    final List<CollectedIndexes> collectedIndexes;
    if (pIndexSuggestionEngineType.equals(IndexSuggestionEngineType.CCPA_ALERTS)) {
      collectedIndexes = getCollectedIndexesForAlerts(pTargetHost, pGroup);
    } else {
      collectedIndexes =
          getCollectedIndexesForUIRun(
              suggestedIndexesAndShapes, pGroup, pOrg, pTargetHost, pCluster, pUser);
    }
    return getFilteredIndexSuggestions(
        collectedIndexes,
        suggestedIndexesAndShapes,
        true,
        SUGGESTED_INDEX_LIMIT,
        pIndexSuggestionEngineType.name(),
        _shapeLimit);
  }

  // for non-sharded cluster, we want to pass in a random host (preferably a primary) and
  // HostCluster as null
  // for sharded cluster, we want to pass in one of the mongoses as host and a HostCluster
  public CreateIndexesResponseView getIndexCreationAdviceForCCPA(
      final List<Host> pHosts,
      final HostCluster pCluster,
      final ObjectId pTopLevelClusterId,
      final Group pGroup,
      final Instant pSince,
      final Instant pUntil,
      final AppUser pUser,
      final Organization pOrg,
      final IndexSuggestionEngineType pIndexSuggestionEngineType,
      final RollupType pRollupType,
      final List<String> pNamespaces,
      final boolean forceNoPII,
      final String pForceQueryStats)
      throws SvcException {
    final Instant start = Instant.now();

    var metadata = new PerformanceAdvisorResponseMetadata();

    final var targetHostReplSetIdClusterIdTriple =
        getTargetHostReplSetIdAndClusterId(pHosts, pCluster);
    if (targetHostReplSetIdClusterIdTriple == null) {
      return null;
    }
    final Host targetHost = targetHostReplSetIdClusterIdTriple.getLeft();
    final ObjectId replSetId = targetHostReplSetIdClusterIdTriple.getMiddle();
    final ObjectId clusterId = targetHostReplSetIdClusterIdTriple.getRight();

    final IndexSuggestionResults slowLogResults =
        getIndexSuggestionResults(
            pHosts,
            pCluster,
            targetHost,
            pGroup,
            pSince,
            pUntil,
            pUser,
            pOrg,
            pIndexSuggestionEngineType,
            pRollupType,
            pNamespaces,
            forceNoPII);

    var indexSuggestionsWithImpactedShapes = slowLogResults.getIndexSuggestions();

    List<PerformanceAdvisorIndexImpactedShapes> dismissedRecommendations = null;
    if (isFeatureFlagEnabled(FeatureFlag.AUTO_INDEXING, _settings, pOrg, pGroup)) {
      final var includedAndExcludedShapes =
          filterDismissedRecommendations(targetHost, pGroup, indexSuggestionsWithImpactedShapes);
      indexSuggestionsWithImpactedShapes = includedAndExcludedShapes.getLeft();
      dismissedRecommendations = includedAndExcludedShapes.getRight();
    }
    List<Feedback> feedback = new ArrayList<>();

    if (pIndexSuggestionEngineType.equals(IndexSuggestionEngineType.CCPA_UI)) {
      feedback =
          _paFeedbackSvc.getFilteredFeedbackForDeployment(
              pOrg,
              pGroup,
              pTopLevelClusterId,
              replSetId,
              targetHost.getId(),
              indexSuggestionsWithImpactedShapes.stream()
                  .map(PerformanceAdvisorIndexImpactedShapes::getSuggestedIndex)
                  .collect(toList()));
    }

    List<SuggestedIndex> slowLogSuggestedIndexes = new ArrayList<>();
    for (PerformanceAdvisorIndexImpactedShapes idx : indexSuggestionsWithImpactedShapes) {
      slowLogSuggestedIndexes.add(idx.getSuggestedIndex());
    }

    final boolean isUiOrPublicApiCall =
        pIndexSuggestionEngineType.equals(IndexSuggestionEngineType.CCPA_UI)
            || pIndexSuggestionEngineType.equals(IndexSuggestionEngineType.CCPA_PUBLIC_API_ATLAS);
    final boolean isAtlas = pGroup.isAtlas();
    final boolean isAtLeastMongoDb8 =
        pHosts.stream()
            .allMatch(
                host ->
                    VersionUtils.isGreaterOrEqualTo(
                        host.getVersion(), VersionUtils.EIGHT_ZERO_ZERO.toString()));
    final boolean queryStatsExectionEnabled =
        _appSettings.getBoolProp(
            "mms.monitoring.performanceadvisor.queryStatsExecutionEnabled", false);
    final boolean enableQueryStatsExperimentExecution =
        _appSettings.getBoolProp(
            "mms.monitoring.performanceadvisor.enableQueryStatsExperimentExecution", false);
    IndexSuggestionResults queryStatsResults = null;
    if (isAtLeastMongoDb8 && isUiOrPublicApiCall && isAtlas && queryStatsExectionEnabled) {
      try {
        queryStatsResults =
            getIndexSuggestionResultsForQueryStats(
                pHosts,
                pCluster,
                targetHost,
                pGroup,
                pSince,
                pUntil,
                pUser,
                pOrg,
                pIndexSuggestionEngineType,
                pRollupType,
                pNamespaces,
                forceNoPII);
        var queryStatsIndexSuggestionsWithImpactedShapes = queryStatsResults.getIndexSuggestions();
        List<SuggestedIndex> queryStatsSuggestedIndexes = new ArrayList<>();
        for (PerformanceAdvisorIndexImpactedShapes idx :
            queryStatsIndexSuggestionsWithImpactedShapes) {
          queryStatsSuggestedIndexes.add(idx.getSuggestedIndex());
        }

        // when the experiment is released, we will merge query stats with slow logs suggestions,
        // record separate prom metrics for merged suggestions, and return those to the user
        boolean isQueryStatsExperimentEnabled =
            isFeatureFlagEnabled(FeatureFlag.QUERY_STATS_PA, _settings, pOrg, pGroup);

        recordPromMetrics(
            slowLogSuggestedIndexes,
            queryStatsSuggestedIndexes,
            pIndexSuggestionEngineType,
            isQueryStatsExperimentEnabled,
            pHosts.get(0).getVersion(),
            pGroup.getId(),
            pCluster.getClusterId());
        final IndexSuggestionResults mergedResults =
            mergeIndexSuggestionResults(queryStatsResults, slowLogResults);

        // Record metrics comparing merged results against original slow log indexes
        recordMergedResultsPromMetrics(
            slowLogSuggestedIndexes,
            mergedResults.getIndexSuggestions().stream()
                .map(PerformanceAdvisorIndexImpactedShapes::getSuggestedIndex)
                .toList(),
            pIndexSuggestionEngineType,
            isQueryStatsExperimentEnabled,
            pHosts.get(0).getVersion(),
            pGroup.getId(),
            pCluster.getClusterId());

        // set algorithm description for indexes in slowLogResults and queryStatsResults
        for (int i = 0; i < slowLogResults.getIndexSuggestions().size(); i++) {
          PerformanceAdvisorIndexImpactedShapes suggestionWithShape =
              slowLogResults.getIndexSuggestions().get(i);
          if (queryStatsSuggestedIndexes.stream()
              .anyMatch(idx -> idx.equals(suggestionWithShape.getSuggestedIndex()))) {
            suggestionWithShape.setAlgorithmDescription("queryStatsAndSlowLogs");
          } else {
            suggestionWithShape.setAlgorithmDescription("slowLogsOnly");
          }
        }
        for (int i = 0; i < queryStatsResults.getIndexSuggestions().size(); i++) {
          PerformanceAdvisorIndexImpactedShapes suggestionWithShape =
              queryStatsResults.getIndexSuggestions().get(i);
          if (slowLogSuggestedIndexes.stream()
              .anyMatch(idx -> idx.equals(suggestionWithShape.getSuggestedIndex()))) {
            suggestionWithShape.setAlgorithmDescription("queryStatsAndSlowLogs");
          } else {
            suggestionWithShape.setAlgorithmDescription("queryStatsOnly");
          }
        }

        // if _qs URL param (forceQueryStats) is set, shortcut logic and
        // return either query stat only or query stats + slow log merged suggestions
        if (pForceQueryStats != null && pForceQueryStats.equals("qsOnly")) {
          return new CreateIndexesResponseView(
              queryStatsResults.getShapes(),
              queryStatsResults.getIndexSuggestions(),
              queryStatsResults.getExistingIndexes(),
              queryStatsResults.getSortedNamespaces(),
              queryStatsResults.getDurationMillis(),
              metadata,
              replSetId,
              clusterId,
              feedback,
              dismissedRecommendations);
        }
        if (pForceQueryStats != null && pForceQueryStats.equals("merged")) {
          return new CreateIndexesResponseView(
              mergedResults.getShapes(),
              mergedResults.getIndexSuggestions(),
              mergedResults.getExistingIndexes(),
              mergedResults.getSortedNamespaces(),
              mergedResults.getDurationMillis(),
              metadata,
              replSetId,
              clusterId,
              feedback,
              dismissedRecommendations);
        }

        // Check new app setting to return merged indexes when both app setting is true AND query
        // stats experiment is enabled
        final boolean returnMergedIndexes =
            _appSettings.getBoolProp(
                "mms.monitoring.performanceadvisor.returnMergedQueryStatsIndexes", false);
        if (returnMergedIndexes && isQueryStatsExperimentEnabled) {
          final var mergedView =
              new CreateIndexesResponseView(
                  mergedResults.getShapes(),
                  mergedResults.getIndexSuggestions(),
                  mergedResults.getExistingIndexes(),
                  mergedResults.getSortedNamespaces(),
                  mergedResults.getDurationMillis(),
                  metadata,
                  replSetId,
                  clusterId,
                  feedback,
                  dismissedRecommendations);

          var mergedSuggestionsWithImpactedShapes = slowLogResults.getIndexSuggestions();
          List<SuggestedIndex> mergedSuggestedIndexes = new ArrayList<>();
          for (PerformanceAdvisorIndexImpactedShapes idx : mergedSuggestionsWithImpactedShapes) {
            mergedSuggestedIndexes.add(idx.getSuggestedIndex());
          }

          submitSegmentEvent(
              pUser != null ? pUser.getId() : null,
              pGroup.getId(),
              pOrg.getId(),
              targetHost.getEntityName(),
              mergedSuggestedIndexes,
              mergedResults.getExistingIndexes(),
              mergedResults.getLogLineLimit(),
              _segmentEventSvc);

          // Store PA run result for merged indexes if there are suggestions
          if (!mergedResults.getIndexSuggestions().isEmpty()) {
            final ObjectId runId =
                storePARunResult(
                    targetHost,
                    pOrg,
                    pGroup,
                    clusterId,
                    mergedView,
                    pIndexSuggestionEngineType,
                    mergedResults.getLogLineLimit(),
                    mergedResults.getImprovementMinimum(),
                    mergedResults.getCumulativeOperationTimeMinimumSeconds(),
                    "",
                    _paRunResultSvc,
                    _settings);
            mergedView.setRunId(runId);

            // Save non-alert recommendations for index tracking (use merged results)
            if (!pIndexSuggestionEngineType.equals(IndexSuggestionEngineType.CCPA_ALERTS)
                && _settings.getBoolProp(
                    "mms.monitoring.performanceadvisor.tracking.willStoreIndexes", false)) {
              try {
                storeIndexSuggestions(
                    pOrg,
                    pGroup,
                    targetHost,
                    mergedResults,
                    slowLogResults,
                    queryStatsResults,
                    _performanceAdvisorTrackingSvc,
                    _settings);
              } catch (final Exception pE) {
                LOG.error(
                    "Error when saving PA indexes for tracking {}",
                    LogUtils.entries(
                        "orgId", pOrg.getId(),
                        "groupId", pGroup.getId(),
                        "hostId", targetHost.getId(),
                        "hostname", targetHost.getEntityName()),
                    pE);
                _performanceAdvisorTrackingSvc.incrementError(WorkflowType.SAVE_INDEXES, pE, 1);
              }
            }
          }

          return mergedView;
        }

      } catch (Exception e) {
        LOG.error(
            "Error processing query stats for performance advisor {}",
            LogUtils.entries(
                "orgId", pOrg.getId(),
                "groupId", pGroup.getId(),
                "hostId", targetHost.getId(),
                "clusterId", clusterId),
            e);
      }
    }

    submitSegmentEvent(
        pUser != null ? pUser.getId() : null,
        pGroup.getId(),
        pOrg.getId(),
        targetHost.getEntityName(),
        slowLogSuggestedIndexes,
        slowLogResults.getExistingIndexes(),
        slowLogResults.getLogLineLimit(),
        _segmentEventSvc);

    final var view =
        new CreateIndexesResponseView(
            slowLogResults.getShapes(),
            indexSuggestionsWithImpactedShapes,
            slowLogResults.getExistingIndexes(),
            slowLogResults.getSortedNamespaces(),
            slowLogResults.getDurationMillis(),
            metadata,
            replSetId,
            clusterId,
            feedback,
            dismissedRecommendations);

    if (!slowLogSuggestedIndexes.isEmpty()) {
      final ObjectId runId =
          storePARunResult(
              targetHost,
              pOrg,
              pGroup,
              clusterId,
              view,
              pIndexSuggestionEngineType,
              slowLogResults.getLogLineLimit(),
              slowLogResults.getImprovementMinimum(),
              slowLogResults.getCumulativeOperationTimeMinimumSeconds(),
              "",
              _paRunResultSvc,
              _settings);
      view.setRunId(runId);

      // We want to save non-alert PA recommendations for index tracking
      if (!pIndexSuggestionEngineType.equals(IndexSuggestionEngineType.CCPA_ALERTS)
          && _settings.getBoolProp(
              "mms.monitoring.performanceadvisor.tracking.willStoreIndexes", false)) {
        try {
          storeIndexSuggestions(
              pOrg,
              pGroup,
              targetHost,
              slowLogResults,
              slowLogResults,
              queryStatsResults,
              _performanceAdvisorTrackingSvc,
              _settings);
        } catch (final Exception pE) {
          LOG.error(
              "Error when saving PA indexes for tracking {}",
              LogUtils.entries(
                  "orgId", pOrg.getId(),
                  "groupId", pGroup.getId(),
                  "hostId", targetHost.getId(),
                  "hostname", targetHost.getEntityName()),
              pE);
          _performanceAdvisorTrackingSvc.incrementError(WorkflowType.SAVE_INDEXES, pE, 1);
        }
      }
    }

    final var numIndexSuggestions = indexSuggestionsWithImpactedShapes.size();

    if (numIndexSuggestions == 0) {
      PERFORMANCE_ADVISOR_ZERO_SUGGESTIONS_TOTAL
          .labels(pIndexSuggestionEngineType.toString(), "")
          .inc();
    } else {
      final Optional<Double> maxImpactScore =
          indexSuggestionsWithImpactedShapes.stream()
              .map(index -> index.getSuggestedIndex().getWeight())
              .max(Double::compareTo);
      maxImpactScore.ifPresent(
          aDouble ->
              PERFORMANCE_ADVISOR_MAX_IMPACT_SCORE
                  .labels(pIndexSuggestionEngineType.toString(), "")
                  .observe(aDouble));
      PERFORMANCE_ADVISOR_SLOW_LOG_SUGGESTED_INDEXES_TOTAL
          .labels(pIndexSuggestionEngineType.toString(), "")
          .inc(numIndexSuggestions);
    }

    CCPA_GET_INDEX_ADVICE_TIME_TOTAL.observe(Duration.between(start, Instant.now()).toMillis());
    PERFORMANCE_ADVISOR_RUNS_TOTAL.labels(pIndexSuggestionEngineType.toString(), "").inc();
    final Duration alertRunDuration = Duration.between(start, Instant.now());
    PERFORMANCE_ADVISOR_RUN_TIME_TOTAL
        .labels(pIndexSuggestionEngineType.toString(), "")
        .observe(alertRunDuration.toMillis());
    LOG.info(
        "Performance advisor run total for getIndexCreationAdviceForCCPA: {}",
        entries(
            Map.of(
                "groupId",
                pGroup.getId(),
                "rollupType",
                pRollupType,
                "totalTime",
                alertRunDuration.toMillis())));
    return view;
  }

  void recordPromMetrics(
      final List<SuggestedIndex> slowLogSuggestedIndexes,
      final List<SuggestedIndex> queryStatsSuggestedIndexes,
      final IndexSuggestionEngineType indexSuggestionEngineType,
      final boolean isQueryStatsExperimentEnabled,
      String mongoVersion,
      final ObjectId groupId,
      final ObjectId clusterId) {
    final String experimentName = isQueryStatsExperimentEnabled ? "queryStats" : "none";

    PERFORMANCE_ADVISOR_8_0_RUNS_TOTAL
        .labels(
            indexSuggestionEngineType.toString(),
            experimentName,
            mongoVersion == null ? "" : mongoVersion)
        .inc();

    if (queryStatsSuggestedIndexes.isEmpty()) {
      PERFORMANCE_ADVISOR_ZERO_QS_SUGGESTIONS_TOTAL
          .labels(
              indexSuggestionEngineType.toString(),
              experimentName,
              mongoVersion == null ? "" : mongoVersion)
          .inc();
    }
    if (slowLogSuggestedIndexes.isEmpty()) {
      PERFORMANCE_ADVISOR_ZERO_SL_SUGGESTIONS_TOTAL
          .labels(
              indexSuggestionEngineType.toString(),
              experimentName,
              mongoVersion == null ? "" : mongoVersion)
          .inc();
    }

    int slowLogIndexesNotInQueryStatsCountOrDistinctFalse = 0;
    int slowLogIndexesNotInQueryStatsCountOrDistinctTrue = 0;
    for (final SuggestedIndex slowLogIndex : slowLogSuggestedIndexes) {
      PERFORMANCE_ADVISOR_8_0_SLOW_LOG_INDEXES_TOTAL
          .labels(
              indexSuggestionEngineType.toString(),
              experimentName,
              mongoVersion == null ? "" : mongoVersion,
              slowLogIndex.isFromCountOrDistinct() ? "true" : "false")
          .inc();
      boolean notInQueryStats =
          queryStatsSuggestedIndexes.stream().noneMatch(qsIndex -> qsIndex.equals(slowLogIndex));
      if (!slowLogIndex.isFromCountOrDistinct() && notInQueryStats) {
        slowLogIndexesNotInQueryStatsCountOrDistinctFalse++;
      }
      if (slowLogIndex.isFromCountOrDistinct() && notInQueryStats) {
        slowLogIndexesNotInQueryStatsCountOrDistinctTrue++;
      }
    }
    LOG.info(
        "slowLogIndexesNotInQueryStats, CountOrDistinctFalse: {}, "
            + "slowLogIndexesNotInQueryStats, CountOrDistinctTrue: {}, total slow log indexes not"
            + " in query stats: {}, groupId: {}, clusterId: {}",
        slowLogIndexesNotInQueryStatsCountOrDistinctFalse,
        slowLogIndexesNotInQueryStatsCountOrDistinctTrue,
        slowLogIndexesNotInQueryStatsCountOrDistinctFalse
            + slowLogIndexesNotInQueryStatsCountOrDistinctTrue,
        groupId,
        clusterId);

    PERFORMANCE_ADVISOR_SLOW_LOG_INDEXES_NOT_IN_QUERY_STATS_TOTAL
        .labels(
            indexSuggestionEngineType.toString(),
            experimentName,
            mongoVersion == null ? "" : mongoVersion,
            "false")
        .inc(slowLogIndexesNotInQueryStatsCountOrDistinctFalse);
    PERFORMANCE_ADVISOR_SLOW_LOG_INDEXES_NOT_IN_QUERY_STATS_TOTAL
        .labels(
            indexSuggestionEngineType.toString(),
            experimentName,
            mongoVersion == null ? "" : mongoVersion,
            "true")
        .inc(slowLogIndexesNotInQueryStatsCountOrDistinctTrue);

    int queryStatsIndexesNotInSlowLog = 0;
    for (final SuggestedIndex queryStatsIndex : queryStatsSuggestedIndexes) {
      PERFORMANCE_ADVISOR_QS_SUGGESTED_INDEXES_TOTAL
          .labels(
              indexSuggestionEngineType.toString(),
              experimentName,
              mongoVersion == null ? "" : mongoVersion,
              "false")
          .inc();
      if (slowLogSuggestedIndexes.stream()
          .noneMatch(slowLogIndex -> slowLogIndex.equals(queryStatsIndex))) {
        queryStatsIndexesNotInSlowLog++;
      }
    }
    PERFORMANCE_ADVISOR_QUERY_STATS_INDEXES_NOT_IN_SLOW_LOG_TOTAL
        .labels(
            indexSuggestionEngineType.toString(),
            experimentName,
            mongoVersion == null ? "" : mongoVersion,
            "false")
        .inc(queryStatsIndexesNotInSlowLog);
    LOG.info(
        "total query stats indexes: {}, total slow log indexes: {}, "
            + ", slowLogIndexesNotInQueryStats: {}, queryStatsIndexesNotInSlowLog: {}"
            + "groupId: {}, clusterId: {}",
        queryStatsSuggestedIndexes.size(),
        slowLogSuggestedIndexes.size(),
        slowLogIndexesNotInQueryStatsCountOrDistinctFalse
            + slowLogIndexesNotInQueryStatsCountOrDistinctTrue,
        queryStatsIndexesNotInSlowLog,
        groupId,
        clusterId);
  }

  void recordMergedResultsPromMetrics(
      final List<SuggestedIndex> slowLogSuggestedIndexes,
      final List<SuggestedIndex> mergedSuggestedIndexes,
      final IndexSuggestionEngineType indexSuggestionEngineType,
      final boolean isQueryStatsExperimentEnabled,
      String mongoVersion,
      final ObjectId groupId,
      final ObjectId clusterId) {
    final String experimentName = isQueryStatsExperimentEnabled ? "queryStats" : "none";

    if (mergedSuggestedIndexes.isEmpty()) {
      PERFORMANCE_ADVISOR_ZERO_MERGED_SUGGESTIONS_TOTAL
          .labels(
              indexSuggestionEngineType.toString(),
              experimentName,
              mongoVersion == null ? "" : mongoVersion)
          .inc();
    } else {
      PERFORMANCE_ADVISOR_MERGED_SUGGESTED_INDEXES_TOTAL
          .labels(
              indexSuggestionEngineType.toString(),
              experimentName,
              mongoVersion == null ? "" : mongoVersion)
          .inc(mergedSuggestedIndexes.size());
    }

    int slowLogIndexesNotInMergedCountOrDistinctFalse = 0;
    int slowLogIndexesNotInMergedCountOrDistinctTrue = 0;
    for (final SuggestedIndex slowLogIndex : slowLogSuggestedIndexes) {
      boolean notInMerged =
          mergedSuggestedIndexes.stream()
              .noneMatch(mergedIndex -> mergedIndex.equals(slowLogIndex));
      if (!slowLogIndex.isFromCountOrDistinct() && notInMerged) {
        slowLogIndexesNotInMergedCountOrDistinctFalse++;
      }
      if (slowLogIndex.isFromCountOrDistinct() && notInMerged) {
        slowLogIndexesNotInMergedCountOrDistinctTrue++;
      }
    }

    PERFORMANCE_ADVISOR_SLOW_LOG_INDEXES_NOT_IN_MERGED_TOTAL
        .labels(
            indexSuggestionEngineType.toString(),
            experimentName,
            mongoVersion == null ? "" : mongoVersion,
            "false")
        .inc(slowLogIndexesNotInMergedCountOrDistinctFalse);
    PERFORMANCE_ADVISOR_SLOW_LOG_INDEXES_NOT_IN_MERGED_TOTAL
        .labels(
            indexSuggestionEngineType.toString(),
            experimentName,
            mongoVersion == null ? "" : mongoVersion,
            "true")
        .inc(slowLogIndexesNotInMergedCountOrDistinctTrue);

    int mergedIndexesNotInSlowLogCountOrDistinctFalse = 0;
    int mergedIndexesNotInSlowLogCountOrDistinctTrue = 0;
    for (final SuggestedIndex mergedIndex : mergedSuggestedIndexes) {
      boolean notInSlowLog =
          slowLogSuggestedIndexes.stream()
              .noneMatch(slowLogIndex -> slowLogIndex.equals(mergedIndex));
      if (!mergedIndex.isFromCountOrDistinct() && notInSlowLog) {
        mergedIndexesNotInSlowLogCountOrDistinctFalse++;
      }
      if (mergedIndex.isFromCountOrDistinct() && notInSlowLog) {
        mergedIndexesNotInSlowLogCountOrDistinctTrue++;
      }
    }

    PERFORMANCE_ADVISOR_MERGED_INDEXES_NOT_IN_SLOW_LOG_TOTAL
        .labels(
            indexSuggestionEngineType.toString(),
            experimentName,
            mongoVersion == null ? "" : mongoVersion,
            "false")
        .inc(mergedIndexesNotInSlowLogCountOrDistinctFalse);
    PERFORMANCE_ADVISOR_MERGED_INDEXES_NOT_IN_SLOW_LOG_TOTAL
        .labels(
            indexSuggestionEngineType.toString(),
            experimentName,
            mongoVersion == null ? "" : mongoVersion,
            "true")
        .inc(mergedIndexesNotInSlowLogCountOrDistinctTrue);

    LOG.info(
        "total merged indexes: {}, total slow log indexes: {}, "
            + ", slowLogIndexesNotInMerged: {}, mergedIndexesNotInSlowLog: {}"
            + "groupId: {}, clusterId: {}",
        mergedSuggestedIndexes.size(),
        slowLogSuggestedIndexes.size(),
        slowLogIndexesNotInMergedCountOrDistinctFalse
            + slowLogIndexesNotInMergedCountOrDistinctTrue,
        mergedIndexesNotInSlowLogCountOrDistinctFalse
            + mergedIndexesNotInSlowLogCountOrDistinctTrue,
        groupId,
        clusterId);
  }

  public ApiPerformanceAdvisorResponseView getIndexCreationAdviceForCCPAPublicAPI(
      final List<Host> pHosts,
      final HostCluster pCluster,
      final Group pGroup,
      final Instant pSince,
      final Instant pUntil,
      final AppUser pUser,
      final Organization pOrg,
      final IndexSuggestionEngineType pIndexSuggestionEngineType,
      final RollupType pRollupType,
      final List<String> pNamespaces)
      throws SvcException {
    final Instant start = Instant.now();

    var metadata = new PerformanceAdvisorResponseMetadata();

    final var targetHostReplSetIdClusterIdTriple =
        getTargetHostReplSetIdAndClusterId(pHosts, pCluster);
    if (targetHostReplSetIdClusterIdTriple == null) {
      return null;
    }
    final Host targetHost = targetHostReplSetIdClusterIdTriple.getLeft();
    final ObjectId replSetId = targetHostReplSetIdClusterIdTriple.getMiddle();
    final ObjectId clusterId = targetHostReplSetIdClusterIdTriple.getRight();

    final IndexSuggestionResults results =
        getIndexSuggestionResults(
            pHosts,
            pCluster,
            targetHost,
            pGroup,
            pSince,
            pUntil,
            pUser,
            pOrg,
            pIndexSuggestionEngineType,
            pRollupType,
            pNamespaces,
            false);

    var indexSuggestionsWithImpactedShapes = results.getIndexSuggestions();

    List<PerformanceAdvisorIndexImpactedShapes> dismissedRecommendations = null;
    if (isFeatureFlagEnabled(FeatureFlag.AUTO_INDEXING, _settings, pOrg, pGroup)) {
      final var includedAndExcludedShapes =
          filterDismissedRecommendations(targetHost, pGroup, indexSuggestionsWithImpactedShapes);
      indexSuggestionsWithImpactedShapes = includedAndExcludedShapes.getLeft();
      dismissedRecommendations = includedAndExcludedShapes.getRight();
    }

    List<SuggestedIndex> suggestedIndexes = new ArrayList<>();
    for (PerformanceAdvisorIndexImpactedShapes idx : indexSuggestionsWithImpactedShapes) {
      suggestedIndexes.add(idx.getSuggestedIndex());
    }

    final var createIndexesResponseView =
        new CreateIndexesResponseView(
            results.getShapes(),
            indexSuggestionsWithImpactedShapes,
            results.getExistingIndexes(),
            results.getSortedNamespaces(),
            results.getDurationMillis(),
            metadata,
            replSetId,
            clusterId,
            List.of(),
            dismissedRecommendations);

    final var view =
        new ApiPerformanceAdvisorResponseView(
            indexSuggestionsWithImpactedShapes, results.getShapes(), dismissedRecommendations, 5);

    if (!suggestedIndexes.isEmpty()) {
      final ObjectId runId =
          storePARunResult(
              targetHost,
              pOrg,
              pGroup,
              clusterId,
              new CreateIndexesResponseView(
                  results.getShapes(),
                  indexSuggestionsWithImpactedShapes,
                  results.getExistingIndexes(),
                  results.getSortedNamespaces(),
                  results.getDurationMillis(),
                  metadata,
                  replSetId,
                  clusterId,
                  List.of(),
                  dismissedRecommendations),
              pIndexSuggestionEngineType,
              results.getLogLineLimit(),
              results.getImprovementMinimum(),
              results.getCumulativeOperationTimeMinimumSeconds(),
              "",
              _paRunResultSvc,
              _settings);
      createIndexesResponseView.setRunId(runId);

      // We want to save non-alert PA recommendations for index tracking
      if (_settings.getBoolProp(
          "mms.monitoring.performanceadvisor.tracking.willStoreIndexes", false)) {
        try {
          storeIndexSuggestions(
              pOrg, pGroup, targetHost, results, _performanceAdvisorTrackingSvc, _settings);
        } catch (final Exception pE) {
          LOG.error(
              "Error when saving PA indexes for tracking {}",
              LogUtils.entries(
                  "orgId", pOrg.getId(),
                  "groupId", pGroup.getId(),
                  "hostId", targetHost.getId(),
                  "hostname", targetHost.getEntityName()),
              pE);
          _performanceAdvisorTrackingSvc.incrementError(WorkflowType.SAVE_INDEXES, pE, 1);
        }
      }
    }

    CCPA_GET_INDEX_ADVICE_TIME_TOTAL.observe(Duration.between(start, Instant.now()).toMillis());
    return view;
  }

  // for standalones, clusterId will be null and rollupType will be HOST
  // for replicaSets and sharded clusters, clusterId will be of the top level cluster
  //    1. rollupType will be CLUSTER if not hostIds are passed in
  //    2. rollupType will be HOST if a subset of hostIds are passed in
  //
  // rollupType will be used to determine whether we should use HOST level or CLUSTER level rollup
  // docs
  public Triple<HostCluster, List<Host>, RollupType> validateHostsAndCluster(
      final List<String> pHostIds, final ObjectId pGroupId, final HostCluster pHostCluster)
      throws SvcException {
    final RollupType rollupType;
    final List<String> hostIds;
    final List<Host> hosts;
    final HostCluster cluster;

    // it won't be a standalone if no hostId is passed in
    // check if this is a CLUSTER level lookup for replicaSet or sharded cluster
    if (pHostIds == null || pHostIds.isEmpty()) {
      cluster = pHostCluster;
      // check if the groupId matches the groupId of the cluster
      // group is being used to check if current user has access
      if (!cluster.getGroupId().equals(pGroupId)) {
        LOG.warn(
            "clusterId={} has groupId={} does not belong to groupId={}",
            cluster.getClusterId(),
            cluster.getGroupId(),
            pGroupId);
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "clusterId");
      }
      rollupType = RollupType.CLUSTER;
      hostIds = new ArrayList<>(cluster.getHostIds());

      // load hosts for the cluster
      cluster.setHosts(
          _hostSvc.findHostsByIds(
              cluster.getGroupId(), SetUtils.toList(cluster.getHostIds()), false));
    } else {
      // hostIds are being passed in, it can be HOST level lookup for standalones, replicaSets or
      // sharded clusters
      //
      // validate hostIds
      if (!pHostIds.stream().allMatch(ValidationUtils::isValidHostName)) {
        LOG.warn("Invalid hostId found, hostIds: {}", pHostIds);
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "hostIds");
      }

      // check if it's standalone
      if (pHostCluster == null) {
        if (pHostIds.size() == 1) {
          cluster = null;
          rollupType = RollupType.HOST;
          hosts = _hostSvc.findHostsByIds(pGroupId, pHostIds, false);
          if (hosts.isEmpty()) {
            LOG.warn("Unable to find hostId={}", pHostIds);
            throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "hostIds");
          }
          // check if not standalone
          if (hosts.get(0).hasCluster()) {
            LOG.warn("ClusterId not included for hostId={}", pHostIds);
            throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "clusterId");
          }
          return Triple.of(cluster, hosts, rollupType);
        } else {
          LOG.warn("Expecting one host in hostIds={}", pHostIds);
          throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "hostIds");
        }
      }

      // it's not a standalone, it's either a replicaSet or a sharded cluster
      // find top level cluster for the host
      final String firstHostId = pHostIds.get(0);
      final List<HostCluster> clusters =
          _hostClusterSvc.findAllActiveWithHostId(
              pGroupId, firstHostId, ReadPreference.primaryPreferred());

      // replicaSet
      if (clusters.isEmpty()) {
        LOG.warn("Unable to find cluster associated with hostId={}", firstHostId);
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "hostIds");
      } else if (clusters.size() == 1) {
        cluster = clusters.get(0);
      } else {
        // sharded cluster
        final Optional<HostCluster> sharded =
            clusters.stream()
                .filter(c -> c.getType().equals(ClusterType.SHARDED_REPLICA_SET))
                .findFirst();
        if (sharded.isEmpty()) {
          LOG.warn("Unable to find sharded cluster. clusters={}", clusters);
          throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "hostIds");
        }
        cluster = sharded.get();
      }

      // check if all hostIds are part of the same cluster
      if (!cluster.getHostIds().containsAll(pHostIds)) {
        LOG.warn("Not all hosts={} are part of the cluster={}", pHostIds, cluster.getClusterId());
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "hostIds");
      }

      // check if the groupId matches the groupId of the cluster
      // group is being used to check if current user has access
      if (!cluster.getGroupId().equals(pGroupId)) {
        LOG.warn("cluster={} does not belong to groupId={}", cluster, pGroupId);
        throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "hostIds");
      }
      rollupType = RollupType.HOST;
      hostIds = pHostIds;

      // load hosts for the cluster
      cluster.setHosts(
          _hostSvc.findHostsByIds(
              cluster.getGroupId(), SetUtils.toList(cluster.getHostIds()), false));
    }
    hosts = getHosts(pGroupId, cluster, hostIds);
    return Triple.of(cluster, hosts, rollupType);
  }

  public Pair<Instant, Instant> validateSinceAndUntil(final Long pSince, final Long pUntil)
      throws SvcException {
    final Instant since;
    final Instant until;

    if (pSince == null) {
      since = now().minus(ofDays(1));
    } else {
      since = Instant.ofEpochMilli(pSince);
    }

    if (pUntil == null) {
      until = now();
    } else {
      until = Instant.ofEpochMilli(pUntil);
    }

    if (until.isBefore(since)) {
      LOG.warn("The user provided an until={} before since={} which is invalid", pUntil, pSince);
      throw new SvcException(CommonErrorCode.INVALID_PARAMETER, "since");
    }

    // there is ~5.5min delay in PA Rollup shapes generated from slow logs,
    // so it's impossible to find rollups newer than that.
    // shift back the entire interval by 5.5 min if until is later than 5.5 minutes ago
    return until.isAfter(now().minus(_rollupDelayMillis, ChronoUnit.MILLIS))
        ? Pair.of(
            since.minus(_rollupDelayMillis, ChronoUnit.MILLIS),
            until.minus(_rollupDelayMillis, ChronoUnit.MILLIS))
        : Pair.of(since, until);
  }

  public Pair<
          List<PerformanceAdvisorIndexImpactedShapes>, List<PerformanceAdvisorIndexImpactedShapes>>
      filterDismissedRecommendations(
          final Host pHost,
          final Group pGroup,
          final List<PerformanceAdvisorIndexImpactedShapes> pSuggestedIndexShapes) {
    final Optional<ClusterDescriptionId> clusterDescriptionId =
        _ndsLookupSvc.getCachedClusterDescriptionIdForHostname(pHost.getName());
    if (clusterDescriptionId.isPresent()) {
      final String ndsClusterName = clusterDescriptionId.get().getClusterName();
      final AutoIndexingConfig autoIndexingConfig =
          _autoIndexingConfigDao.findByProjectIdAndClusterName(pGroup.getId(), ndsClusterName);
      if (autoIndexingConfig != null) {
        return filterExcludedIndexesFromShapes(
            pSuggestedIndexShapes, autoIndexingConfig.getExcludedIndexes());
      }
    }
    return Pair.of(pSuggestedIndexShapes, new ArrayList<>());
  }

  public List<CollectedIndexes> getCollectedIndexesForUIRun(
      final SuggestedIndexesAndShapes pSuggestedIndexesAndShapes,
      final Group pGroup,
      final Organization pOrg,
      final Host pHost,
      final HostCluster pHostCluster,
      final AppUser pUser)
      throws SvcException {
    final Set<Namespace> namespaceSet =
        getNamespacesSet(pSuggestedIndexesAndShapes.getShapesToIndexes());
    final List<ObjectId> getIndexesJobIds =
        _performanceAdvisorSvc.submitListIndexStatsJobRequest(
            namespaceSet, pGroup, pHostCluster, pHost, pOrg, CREATE_INDEX_SUGGESTIONS);
    final var existingAndBuildingIndexes =
        _dataExplorerSvc.getListIndexStatsResponse(
            pGroup.getId(), pUser.getId(), getIndexesJobIds, false);

    // existing indexes
    final List<CollectedIndexes> collectedIndexes =
        PerformanceAdvisorUtils.dataExplorerToCollectedIndexes(
            existingAndBuildingIndexes, pGroup.getId(), pHost.getId());

    final List<Namespace> failedNamespaces =
        PerformanceAdvisorUtils.getFailedNamespacesFromDataExplorerResponses(
            existingAndBuildingIndexes);
    pSuggestedIndexesAndShapes.removeNamespaces(failedNamespaces);
    return collectedIndexes;
  }

  public List<CollectedIndexes> getCollectedIndexesForAlerts(final Host pHost, final Group pGroup)
      throws SvcException {
    try {
      return _statsCollectionSvc.getResponses(pHost.getId());
    } catch (final Exception pEx) {
      CCPA_ERROR_TOTAL.labels("getIndexesForAlert").inc();
      LOG.warn(
          "Error retrieving collected indexes - groupId={}, hostId={}", pGroup, pHost.getId(), pEx);
      throw new SvcException(SERVER_ERROR);
    }
  }

  public Map<String, Double> getAvgObjSizes(
      final AppUser pUser,
      final List<String> pIdentifiers,
      final Host pTargetHost,
      final Group pGroup,
      final List<String> pNamespaces,
      final IndexSuggestionEngineType pIndexSuggestionEngineType,
      final List<PARollupShapesDaosByGranularity> pDaosByGranularity)
      throws SvcException {
    final Map<String, Double> avgObjSizes = new HashMap<>();
    final Set<String> namespaces;
    try {
      if (pNamespaces.isEmpty()) {
        namespaces = findBusiestNamespacesSinceUntil(pDaosByGranularity, pIdentifiers, pGroup);
      } else {
        namespaces = new HashSet<>(pNamespaces);
      }

      final var filteredNamespaces = filterNamespaces(namespaces);
      if (filteredNamespaces.isEmpty()) {
        LOG.info(
            "No namespaces found after filtering for identifiers={} groupId={} since {}; won't"
                + " create agent job. Returning empty avgObjSize map.",
            pIdentifiers,
            pGroup.getId(),
            now().minus(ofHours(24)));
        return avgObjSizes;
      }

      final AgentJob job =
          CollStatsCollectionJob.createAgentJob(
              new ObjectId(),
              pGroup.getId(),
              pUser.getId(),
              pTargetHost.getId(),
              pTargetHost.getName(),
              pTargetHost.getPort(),
              filteredNamespaces);
      _agentJobSvc.putJob(job);

      final boolean succeeded = hasCollStatsCollectionJobResponse(job.getId(), pTargetHost, job);
      if (!succeeded) {
        throw new SvcException(SERVER_ERROR);
      } else {
        final List<CollectionStats> collectionStats =
            _collectionStatsDao.findByHostNamespaces(
                pTargetHost.getId(), new ArrayList<>(filteredNamespaces));
        if (collectionStats.isEmpty()) {
          LOG.info(
              "Empty collectionStats received for groupId={}, hostId={}, will return empty "
                  + "avgObjSizes",
              pGroup.getId(),
              pTargetHost.getId());
        }
        collectionStats.stream()
            .forEach(
                collStats ->
                    avgObjSizes.computeIfAbsent(
                        collStats.getNamespace(), k -> collStats.getAvgObjSize()));
      }
    } catch (final Exception pE) {
      CCPA_ERROR_TOTAL.labels("collStatsCollection").inc();
      LOG.error("Error submitting collStatsCollection agent job for host {}", pTargetHost, pE);
      COLLSTATS_COLLECTION_FAILED_TOTAL
          .labels(pIndexSuggestionEngineType.toString(), NO_EXPERIMENT_NAME)
          .inc(1);
      throw new SvcException(SERVER_ERROR);
    }
    return avgObjSizes;
  }

  @MethodCallPromTimed(
      name = "mms_monitoring_alert_CCPAIndexSuggestionSvc_getAvgObjSizesForAlerts_duration_seconds",
      help = "Duration in seconds to compute avg object sizes")
  private Map<String, Double> getAvgObjSizesForAlerts(
      final List<String> pIdentifiers,
      final Host pTargetHost,
      final List<PARollupShapesDaosByGranularity> pDaosByGranularity,
      final Group pGroup) {
    final Map<String, Double> avgObjSizes = new HashMap<>();

    // we fetch avgObjSizes from backing DB for alert runs since it is faster, it's the same idea as
    // how we collect indexes in UI vs alert run
    try {
      final Set<String> namespaces =
          findBusiestNamespacesSinceUntil(pDaosByGranularity, pIdentifiers, pGroup);

      final List<CollectionStats> collectionStats =
          _collectionStatsDao.findByHostNamespaces(
              pTargetHost.getId(), new ArrayList<>(namespaces));
      collectionStats.forEach(
          collStats ->
              avgObjSizes.computeIfAbsent(
                  collStats.getNamespace(), k -> collStats.getAvgObjSize()));
    } catch (final Exception pE) {
      CCPA_ERROR_TOTAL.labels("collStats").inc();
      LOG.error("Error getting collStats for host {}", pTargetHost, pE);
    }
    return avgObjSizes;
  }

  @MethodCallPromTimed(
      name =
          "mms_monitoring_alert_CCPAIndexSuggestionSvc_findBusiestNamespacesSinceUntil_duration_seconds",
      help = "Duration in seconds to find busiest namespaces for a time period")
  Set<String> findBusiestNamespacesSinceUntil(
      final List<PARollupShapesDaosByGranularity> pDaosByGranularity,
      final List<String> pIdentifiers,
      final Group pGroup) {
    final Map<String, Long> namespacesLogCount = new HashMap<>();

    final List<Future<Map<String, Long>>> namespacesLogCountFutures = new ArrayList<>();

    pDaosByGranularity.forEach(
        (daos) -> {
          for (final Dao<PARollupShape> dao : daos.getDaos().descendingSet()) {
            namespacesLogCountFutures.add(
                _sharedWorkerPool.submit(
                    () ->
                        findBusiestNamespacesSinceUntilInDao(
                            dao,
                            pIdentifiers,
                            daos.getStartTime(),
                            daos.getEndTime(),
                            DEFAULT_BUSIEST_NAMESPACE_LIMIT),
                    pGroup.getId(),
                    LOG));
          }
        });

    namespacesLogCountFutures.forEach(
        (f) -> {
          _sharedWorkerPool
              .getFutureValue(f)
              .forEach((key, value) -> namespacesLogCount.merge(key, value, Long::sum));
        });

    return namespacesLogCount.entrySet().stream()
        .sorted(reverseOrder(Entry.comparingByValue()))
        .limit(DEFAULT_BUSIEST_NAMESPACE_LIMIT)
        .map(Entry::getKey)
        .collect(Collectors.toSet());
  }

  private boolean checkAgentSupportsCollStatsCollection(final Group pGroup) {
    AgentVersion agentVersion = _automationAgentSupportSvc.getMinAgentVersion(pGroup);
    if (agentVersion == null) {
      CCPA_ERROR_TOTAL.labels("minAgentVersionFail").inc();
      LOG.warn(
          "Null minAgentVersion for group '{}'. Cannot check against"
              + " MIN_SUPPORT_COLLSTATS_COLLECTION_JOB_VERSION so will not enable sized"
              + " based PA.",
          pGroup);
    }
    return agentVersion != null && agentVersion.supportsCollStatsCollection();
  }

  private boolean checkAgentSupportsQueryStats(final Group pGroup) {
    AgentVersion agentVersion = _automationAgentSupportSvc.getMinAgentVersion(pGroup);
    if (agentVersion == null) {
      CCPA_ERROR_TOTAL.labels("minAgentVersionFail").inc();
      LOG.warn(
          "Null minAgentVersion for group '{}'. Cannot check against"
              + " MIN_SUPPORT_QUERY_STATS_VERSION so will not enable query stats based PA.",
          pGroup);
    }
    return agentVersion != null && agentVersion.supportsQueryStats();
  }

  boolean hasCollStatsCollectionJobResponse(
      final ObjectId pJobId, final Host pHost, final AgentJob pJob) throws SvcException {
    final Instant startTime = now();
    final Instant timeout = startTime.plus(Duration.ofMillis(COLLSTATS_COLLECTION_TIMEOUT_MILLIS));
    boolean hasResults = false;
    AgentJob job = null;

    while (now().isBefore(timeout) && !hasResults) {
      sleep();
      job = _agentJobsProcessorSvc.findById(pJobId);
      if (job != null) {
        hasResults = job.getStatus() == Status.FAILED || job.getStatus() == Status.COMPLETED;
      }
    }
    if (job == null) {
      CCPA_ERROR_TOTAL.labels("collStatsCollectionTimeout").inc();
      LOG.error("collStatsCollection job with id {} timed out for host {}", pJobId, pHost);
      return false;
    } else if (job.getStatus() == Status.FAILED) {
      CCPA_ERROR_TOTAL.labels("collStatsCollection").inc();
      LOG.error("collStatsCollection job with id {} failed for host {}", pJobId, pHost);
      return false;
    }
    return true;
  }

  private void sleep() throws SvcException {
    try {
      Thread.sleep(500L);
    } catch (final InterruptedException pE) {
      Thread.currentThread().interrupt();
      throw new SvcException(SERVER_ERROR, pE);
    }
  }

  /**
   * Merges query stats and slow log index suggestion results: Combine indexes either common to both
   * or only present in query stats into a single list. Sort this list according to Query stats
   * weight descending - take the first MAX_QUERY_STATS_INDEXES of these indexes. From the remaining
   * indexes only present in the slow logs list, append MAX_SLOW_LOG_INDEXES with the largest Slow
   * log weight. Update the shapes field, existing indexes field, and sorted namespaces according to
   * the merged results
   */
  public IndexSuggestionResults mergeIndexSuggestionResults(
      final IndexSuggestionResults queryStatsResults, final IndexSuggestionResults slowLogResults) {

    final List<PerformanceAdvisorIndexImpactedShapes> queryStatsIndexes =
        queryStatsResults.getIndexSuggestions();
    final List<PerformanceAdvisorIndexImpactedShapes> slowLogIndexes =
        slowLogResults.getIndexSuggestions();

    // Combine indexes common to both lists or only present in query stats list
    final List<PerformanceAdvisorIndexImpactedShapes> combinedIndexes = new ArrayList<>();
    final Set<SuggestedIndex> processedIndexes = new HashSet<>();

    for (final PerformanceAdvisorIndexImpactedShapes queryStatsIndex : queryStatsIndexes) {
      final SuggestedIndex suggestedIndex = queryStatsIndex.getSuggestedIndex();
      combinedIndexes.add(queryStatsIndex);
      processedIndexes.add(suggestedIndex);
    }

    // Sort by query stats weight descending and take top DEFAULT_MAX_QUERY_STATS_INDEXES
    final int topQueryStatsIndexLimit =
        _settings.getIntProp(
            "mms.monitoring.performanceadvisor.queryStats.topQueryStatsIndexLimit",
            DEFAULT_MAX_QUERY_STATS_INDEXES);
    combinedIndexes.sort(
        (a, b) -> Double.compare(b.getSuggestedIndexWeight(), a.getSuggestedIndexWeight()));
    final List<PerformanceAdvisorIndexImpactedShapes> topQueryStatsIndexes =
        combinedIndexes.stream().limit(topQueryStatsIndexLimit).collect(toList());

    // Step 3: From slow log indexes not present in query stats (or replaceable by top query
    // stats indexes), take top DEFAULT_MAX_SLOW_LOG_INDEXES by weight
    final int topSlowLogIndexLimit =
        _settings.getIntProp(
            "mms.monitoring.performanceadvisor.queryStats.topSlowLogIndexLimit",
            DEFAULT_MAX_SLOW_LOG_INDEXES);
    final List<PerformanceAdvisorIndexImpactedShapes> remainingSlowLogIndexes =
        slowLogIndexes.stream()
            .filter(
                slowLogIndex -> {
                  final SuggestedIndex suggestedIndex = slowLogIndex.getSuggestedIndex();
                  return !processedIndexes.contains(suggestedIndex)
                      && !isIndexReplaceableByList(suggestedIndex, topQueryStatsIndexes);
                })
            .sorted(
                (a, b) -> Double.compare(b.getSuggestedIndexWeight(), a.getSuggestedIndexWeight()))
            .limit(topSlowLogIndexLimit)
            .toList();

    // Combine the final list
    final List<PerformanceAdvisorIndexImpactedShapes> mergedIndexSuggestions = new ArrayList<>();
    mergedIndexSuggestions.addAll(topQueryStatsIndexes);
    mergedIndexSuggestions.addAll(remainingSlowLogIndexes);

    // Calculate shapes, existing indexes, and sorted namespaces based on merged results
    final Set<PerformanceAdvisorShape> mergedShapes = new HashSet<>();
    final Set<String> mergedNamespaces = new HashSet<>();

    for (final PerformanceAdvisorIndexImpactedShapes indexShape : mergedIndexSuggestions) {
      mergedShapes.addAll(indexShape.getImpactedShapes());
      mergedNamespaces.add(indexShape.getNamespace());
    }

    Set<CollectedIndexes> existingIndexesSet = new HashSet<>();
    for (var collectedIndexes : queryStatsResults.getExistingIndexes()) {
      if (mergedNamespaces.contains(collectedIndexes.getNamespace())) {
        existingIndexesSet.add(collectedIndexes);
      }
    }
    for (var collectedIndexes : slowLogResults.getExistingIndexes()) {
      if (mergedNamespaces.contains(collectedIndexes.getNamespace())
          && existingIndexesSet.stream()
              .noneMatch(
                  indexes -> indexes.getNamespace().equals(collectedIndexes.getNamespace()))) {
        existingIndexesSet.add(collectedIndexes);
      }
    }

    final List<String> sortedNamespaces =
        BaseIndexSuggestionEngine.getSortedNamespaces(mergedIndexSuggestions);

    return new IndexSuggestionResults(
        mergedIndexSuggestions,
        mergedShapes,
        existingIndexesSet.stream().toList(),
        sortedNamespaces,
        // the four field below are not used by anything
        Math.max(queryStatsResults.getDurationMillis(), slowLogResults.getDurationMillis()),
        queryStatsResults.getLogLineLimit(),
        queryStatsResults.getImprovementMinimum(),
        queryStatsResults.getCumulativeOperationTimeMinimumSeconds());
  }

  /** Checks if a suggested index can be replaced by any index in the given list */
  private boolean isIndexReplaceableByList(
      final SuggestedIndex suggestedIndex,
      final List<PerformanceAdvisorIndexImpactedShapes> indexList) {
    return indexList.stream()
        .map(PerformanceAdvisorIndexImpactedShapes::getSuggestedIndex)
        .anyMatch(suggestedIndex::canBeReplacedBy);
  }

  private Host getTargetHostForReplicaSet(List<Host> pHosts) {
    // find first primary if it exists, otherwise use the first secondary host
    final var primaryHost = pHosts.stream().filter(Host::getIsPrimary).findFirst();
    if (primaryHost.isPresent()) {
      return primaryHost.get();
    } else {
      // find first secondary if it exits, otherwise use the first host
      final var secondaryHost = pHosts.stream().filter(Host::getIsSecondary).findFirst();
      return secondaryHost.isPresent() ? secondaryHost.get() : pHosts.get(0);
    }
  }

  private List<Host> getHosts(ObjectId pGroupId, HostCluster cluster, List<String> pHostIds) {
    return pHostIds != null
        ? _hostSvc.findHostsByIds(pGroupId, pHostIds, false)
        : cluster.getHosts();
  }
}
