package com.xgen.svc.mms.svc.billing.pricing;

import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.module.metering.common.model.getter.RegionNameGetter;
import com.xgen.module.metering.common.view.MeterUsageAggregateView;

public abstract class CloudRegionPricing implements PricingStrategy {
  public double getUnitPriceDollars(
      SKU pSKU,
      SkuPricing skuPricing,
      MeterUsageAggregateView pMeterUsage,
      AdditionalPricingInfo additionalPricingInfo,
      AppSettings appSettings) {
    RegionNameGetter regionNameGetter = (RegionNameGetter) pMeterUsage.getUsageDimensions();
    return skuPricing.getRegionUnitPriceDollars(regionNameGetter.getRegionName());
  }

  public double getTieredUnitPriceDollars(
      SKU pSKU, SkuPricing skuPricing, int pTier, MeterUsageAggregateView pMeterUsage) {
    RegionNameGetter regionNameGetter = (RegionNameGetter) pMeterUsage.getUsageDimensions();
    return skuPricing.getTieredRegionUnitPriceDollars(regionNameGetter.getRegionName(), pTier);
  }
}
