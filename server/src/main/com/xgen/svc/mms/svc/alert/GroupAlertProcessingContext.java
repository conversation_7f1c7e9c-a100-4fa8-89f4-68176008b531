package com.xgen.svc.mms.svc.alert;

import static com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.ALERT_STATE_FLAPPING_DETECTION;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static net.logstash.logback.argument.StructuredArguments.entries;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.assistedinject.Assisted;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.ReadPreference;
import com.xgen.cloud.activity._public.model.alert.config.AlertConfig;
import com.xgen.cloud.alerts.alert._public.svc.AlertConfigSvc;
import com.xgen.cloud.alerts.context._public.model.BiConnectorAlertCheckTarget;
import com.xgen.cloud.alerts.context._public.svc.AbstractGroupAlertProcessingBatchContext;
import com.xgen.cloud.alerts.context._public.svc.AbstractGroupAlertProcessingContext;
import com.xgen.cloud.atm.core._private.dao.LastBiConnectorStatusDao;
import com.xgen.cloud.atm.core._public.model.AgentAudit;
import com.xgen.cloud.atm.core._public.model.BiConnectorStatusEntry;
import com.xgen.cloud.atm.core._public.model.CrashLog;
import com.xgen.cloud.atm.core._public.model.LastBiConnectorStatus;
import com.xgen.cloud.atm.core._public.svc.AutomationAgentAuditSvc;
import com.xgen.cloud.atm.core._public.svc.AutomationConfigAliasSvc;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.atm.core._public.svc.CrashLogSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.brs.core._private.dao.BackupAgentSessionDao;
import com.xgen.cloud.brs.core._private.dao.BackupGroupConfigDao;
import com.xgen.cloud.brs.core._private.dao.ClustershotDao;
import com.xgen.cloud.brs.core._private.dao.RestoreJobDao;
import com.xgen.cloud.brs.core._private.dao.SyncDao;
import com.xgen.cloud.brs.core._private.dao.ThirdPartyBackupJobDao;
import com.xgen.cloud.brs.core._public.model.BackupStatus;
import com.xgen.cloud.brs.core._public.model.ClusterStatus;
import com.xgen.cloud.brs.core._public.model.Clustershot;
import com.xgen.cloud.brs.core._public.model.restore.RestoreStatus;
import com.xgen.cloud.brs.core._public.model.thirdparty.ThirdPartyJob;
import com.xgen.cloud.brs.core._public.svc.BackupEncryptionUtil;
import com.xgen.cloud.brs.daemon._public.grid.dao.BlockstoreJobDao;
import com.xgen.cloud.brs.daemon._public.grid.dao.ClusterStatusDao;
import com.xgen.cloud.brs.daemon._public.grid.dao.ImplicitJobDaoV2;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.authn._public.svc.AuthnOAuthClient.UnableToRetrieveCloudJwtException;
import com.xgen.cloud.common.brs._public.model.DaemonMachine;
import com.xgen.cloud.common.brs._public.model.Sync;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.common.util._public.util.SetUtils;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.customermetrics._public.model.query.InstantQueryRequest;
import com.xgen.cloud.customermetrics._public.svc.CustomerMetricsQuerySvc;
import com.xgen.cloud.customermetrics._public.view.PromQLResponseView;
import com.xgen.cloud.customermetrics._public.view.PromQLResponseView.Data;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.IndexConfig;
import com.xgen.cloud.deployment._public.model.MongosqldConfig;
import com.xgen.cloud.deployment._public.model.MongotConfig;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.VersionManifest;
import com.xgen.cloud.fts.activity._public.alert.RequiredSearchDiskResult;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.agent._public.svc.AgentPingSvc;
import com.xgen.cloud.monitoring.common._public.model.retention.Retention;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostAlertSvc;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.metrics._private.dao.rrd.RetentionPolicyDao;
import com.xgen.cloud.monitoring.metrics._public.model.DiskMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.FTSDiskMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.FTSIndexStatsMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.FTSProcessCpuMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.FTSProcessMemoryMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.FTSServerStatusMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.HostMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.MetricDataSnapshot;
import com.xgen.cloud.monitoring.metrics._public.model.MetricsException;
import com.xgen.cloud.monitoring.metrics._public.model.RealmMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.SystemMemoryMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.Systemable;
import com.xgen.cloud.monitoring.metrics._public.model.hardware.DiskPartition;
import com.xgen.cloud.monitoring.metrics._public.model.serverless.ServerlessClusterMeasurement;
import com.xgen.cloud.monitoring.metrics._public.svc.MetricsSvc;
import com.xgen.cloud.monitoring.metrics._public.svc.hardware.DiskPartitionSvc;
import com.xgen.cloud.monitoring.metrics._public.svc.realm.RealmMeasurementSvc;
import com.xgen.cloud.monitoring.metrics._public.svc.serverless.ServerlessMetricsSvc;
import com.xgen.cloud.monitoring.monitoredhost._public.svc.DecoupledMongotSvc;
import com.xgen.cloud.monitoring.topology._public.model.AlertableRealmInstance;
import com.xgen.cloud.monitoring.topology._public.model.ClusterType;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.Host.DataSource;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.MonitoredHost;
import com.xgen.cloud.monitoring.topology._public.svc.CanonicalHostSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostClusterSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostLastPingSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.nds.admin._public.model.AdminClusterLock;
import com.xgen.cloud.nds.admin._public.svc.AdminClusterLockSvc;
import com.xgen.cloud.nds.checkmetadataconsistency._public.model.CheckShardedMetadataConsistencyRun;
import com.xgen.cloud.nds.checkmetadataconsistency._public.svc.NDSCheckMetadataConsistencySvc;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster;
import com.xgen.cloud.nds.common._public.model.NDSProxyAudit;
import com.xgen.cloud.nds.dbcheck._public.model.DbCheck;
import com.xgen.cloud.nds.dbcheck._public.svc.DbCheckSvc;
import com.xgen.cloud.nds.flex._public.model.AlertableFlexCluster;
import com.xgen.cloud.nds.flex._public.model.FlexMTMCluster;
import com.xgen.cloud.nds.flex._public.model.FlexTenantProviderOptions;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.fts._public.model.FTSIndexConfig;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveExpirationHistoryDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveHistoryDao;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveExpirationHistory;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveHistory;
import com.xgen.cloud.nds.project._private.dao.usersecurity.NDSUserCertDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.EncryptionAtRestProvider;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSUserCert;
import com.xgen.cloud.nds.project._public.model.versions.FixedAgentVersion;
import com.xgen.cloud.nds.serverless._public.model.AlertableServerlessInstance;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.serverless._public.model.ServerlessTenantProviderOptions;
import com.xgen.cloud.nds.tenant._public.model.TenantCloudProviderContainer;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.search.decoupled.external._public.model.DedicatedMongotHost;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchDeploymentSvc;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchDeploymentTargets;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchInstanceSvc;
import com.xgen.module.liveimport.model.LiveImport;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.model.agent.AgentPing;
import com.xgen.svc.mms.model.agent.AgentStatus;
import com.xgen.svc.mms.svc.agent.AgentStatusSvc;
import com.xgen.svc.mms.svc.host.ClusterLookupSvc;
import com.xgen.svc.mms.svc.pausefreetiermonitoring.NDSM0ClusterActivitySvc;
import com.xgen.svc.nds.liveimport.svc.LiveImportSvc;
import com.xgen.svc.nds.svc.NDSLookupSvc;
import com.xgen.svc.nds.svc.NDSProxyAuditSvc;
import com.xgen.svc.nds.svc.SearchForGroupAlertSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.BasicBSONObject;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Encapsulates state and data that is shared across AlertChecks in the context of a single group. A
 * new context is created for each group while processing alerts. This class is not thread-safe;
 * it's expected that alert processing for a group will be single threaded.
 *
 * <p>Since many alert checks may share data, fields should generally be lazily loaded to ensure
 * they're only loaded one time, as needed. For example, a group with multiple HostAlertChecks will
 * only load the host list once, whereas a group with no HostAlertChecks will not load the hosts at
 * all.
 */
public class GroupAlertProcessingContext extends AbstractGroupAlertProcessingContext {
  private static final Logger LOG = LoggerFactory.getLogger(GroupAlertProcessingContext.class);

  private static final long DEFAULT_HIGHEST_RESOLUTION_RETENTION = 60000L;
  public static final long DEFAULT_MEASUREMENT_DURATION_MILLIS = Duration.ofMinutes(25).toMillis();
  private final AutomationAgentAuditSvc _automationAgentAuditSvc;
  private final NDSProxyAuditSvc _ndsProxyAuditSvc;
  private final AgentStatusSvc _agentStatusSvc;
  private final AutomationConfigPublishingSvc _automationConfigSvc;
  private final AutomationConfigAliasSvc _automationConfigAliasSvc;
  private final AgentPingSvc _agentPingSvc;
  private final BackupGroupConfigDao _backupGroupConfigDao;
  private final MTMClusterDao _mtmClusterDao;
  private final ClusterStatusDao _clusterStatusDao;
  private final ClustershotDao _clustershotDao;
  private final SyncDao _syncDao;
  private final ImplicitJobDaoV2 _jobDao;
  private final InvoiceDao _invoiceDao;
  private final OnlineArchiveSvc _onlineArchiveSvc;
  private final HostSvc _hostSvc;
  private final DecoupledMongotSvc _decoupledMongotSvc;
  private final SearchInstanceSvc _searchInstanceSvc;
  private final SearchDeploymentSvc _searchDeploymentSvc;
  private final HostClusterSvc _hostClusterSvc;
  private final HostLastPingSvc _hostLastPingSvc;
  private final CanonicalHostSvc _canonicalHostSvc;
  private final HostClusterLifecycleSvc _hostClusterLifecycleSvc;
  private final HostAlertSvc _hostAlertSvc;
  private final MetricsSvc _metricsSvc;
  private final CustomerMetricsQuerySvc _customerMetricsQuerySvc;
  private final ObjectMapper _objectMapper;
  private final ServerlessMetricsSvc _serverlessMetricsSvc;
  private final DiskPartitionSvc _diskPartitionSvc;
  private final RestoreJobDao _restoreJobDao;
  private final BlockstoreJobDao _blockstoreJobDao;
  private final LastBiConnectorStatusDao _biConnectorStatusDao;
  private final BackupAgentSessionDao _backupAgentSessionDao;
  private final Map<String, Optional<Host>> _hosts;
  private final Map<String, AgentStatus> _backupAgentStatus;
  private final Map<ObjectId, Clustershot> _successfulClustershots;
  private final Map<ObjectId, Clustershot> _failedClustershots;
  private final Map<ObjectId, Integer> _failedClustershotCounts;
  private final Map<String, Set<String>> _hostDataPartitions;
  private final Map<String, Set<String>> _hostIndexPartitions;
  private final Map<String, String> _hostJournalPartition;
  private final Map<String, Optional<ClusterDescriptionId>> _clusterDescriptionIdsByHostname;
  private final AbstractGroupAlertProcessingBatchContext _groupAlertProcessingBatchContext;
  private final NDSM0ClusterActivitySvc _ndsM0ClusterActivitySvc;
  private final NDSLookupSvc _ndsLookupSvc;
  private final NDSUserCertDao _ndsUserCertDao;
  private final DbCheckSvc _dbCheckSvc;
  private final BackupJobDao _backupJobDao;
  private final ThirdPartyBackupJobDao _thirdPartyBackupJobDao;
  private final BackupSnapshotDao _backupSnapshotDao;
  private final AppSettings _appSettings;
  private final SearchForGroupAlertSvc _searchForGroupAlertSvc;
  private final CrashLogSvc _crashLogSvc;
  private final AutomationMongoDbVersionSvc _versionSvc;
  private final AlertConfigSvc _alertConfigSvc;
  private final OnlineArchiveHistoryDao _onlineArchiveHistoryDao;
  private final OnlineArchiveExpirationHistoryDao _onlineArchiveExpirationHistoryDao;
  private final RealmMeasurementSvc _realmMeasurementSvc;
  private final ClusterLookupSvc _clusterLookupSvc;
  private final LiveImportSvc _liveImportSvc;
  private final NDSCheckMetadataConsistencySvc _checkMetadataConsistencySvc;
  private final AdminClusterLockSvc _adminClusterLockSvc;

  // These are intentionally *not* final because they are lazily loaded.
  private List<AgentAudit> _automationAgentAudits;
  private List<NDSProxyAudit> _ndsProxyAudits;
  private List<ClusterDescription> _cachedLookedUpClusterDescriptions;
  private List<ClusterDescription> _activeClusterDescriptions;
  private List<BackupJob> _cpsBackupJobs;

  private List<DbCheck> _dbChecks;
  private List<BasicDBObject> _canonicalHosts;
  private List<MonitoredHost> _monitoredHosts;
  private List<RestoreStatus> _incompleteRestoreJobs;
  private List<DBObject> _backupAgentSessions;
  private List<AdminClusterLock> _adminClusterLocks;
  private Map<ObjectId, AlertableServerlessInstance> _alertableServerlessInstances;
  private Map<ObjectId, AlertableFlexCluster> _alertableFlexClusters;
  private Map<ObjectId, AlertableRealmInstance> _alertableRealmInstances;
  private Map<ObjectId, HostCluster> _replicaSets;
  private Map<ObjectId, HostCluster> _shardedClusters;
  private Map<ObjectId, ClusterStatus> _clusterStatus;
  private Map<Pair<ObjectId, String>, BackupStatus> _backupStatus;
  private Map<ObjectId, ThirdPartyJob> _thirdPartyBackupJobs;
  private Map<ObjectId, List<BackupSnapshot>> _queuedCpsSnapshots;
  private Map<String, Boolean> _ignoreHealthCheck; // key is hostname and port joined by colon
  private Optional<Invoice> _pendingInvoice;
  private RetentionPolicyDao _retentionPolicyDao;
  private Optional<AutomationConfig> _publishedAutomationConfig;
  private Map<Pair<ObjectId, String>, List<DBObject>> _blockstoreJob;
  private List<MTMCluster> _mtmClusters;
  private boolean _allDiskPartitionsLoaded;
  private AgentPing _agentPing;
  private Optional<NDSGroup> _ndsGroup;
  private Map<MTMCluster, List<NDSProxyAudit>> _lastServerlessProxyAuditsByMTM;
  private Map<MTMCluster, List<NDSProxyAudit>> _lastFlexProxyAuditsByMTM;
  private Map<String, MetricDataSnapshot<FTSDiskMeasurement>> _ftsDiskMeasurements;
  private Map<String, MetricDataSnapshot<DiskMeasurement>> _diskMeasurements;
  private Map<String, MetricDataSnapshot<HostMeasurement>> _hostMeasurements;
  private Map<String, MetricDataSnapshot<FTSProcessMemoryMeasurement>>
      _ftsProcessMemoryMeasurements;
  private Map<String, MetricDataSnapshot<FTSProcessCpuMeasurement>> _ftsProcessCpuMeasurements;
  private Map<String, MetricDataSnapshot<SystemMemoryMeasurement>> _systemMemoryMeasurements;
  private Map<String, MetricDataSnapshot<? extends Systemable>> _processMeasurements;
  private Map<ObjectId, MetricDataSnapshot<ServerlessClusterMeasurement>>
      _serverlessClusterMeasurements;
  private Map<String, MetricDataSnapshot<FTSIndexStatsMeasurement>> _ftsIndexStatsMeasurements;
  private Map<String, MetricDataSnapshot<FTSServerStatusMeasurement>> _ftsServerStatusMeasurements;
  private Map<ObjectId, MetricDataSnapshot<RealmMeasurement>> _realmMeasurements;
  private List<FTSIndexConfig> _ftsIndexConfigs;
  private List<CrashLog> _mongotCrashLogs;
  private Map<ObjectId, ObjectId> _hostClusterClusterIdToClusterUniqueId;
  private Map<ObjectId, Set<String>> _mtmClusterUniqueIdToSomeServerlessTenantIds;
  private Map<ObjectId, Set<String>> _mtmClusterUniqueIdToSomeFlexTenantIds;
  private List<CheckShardedMetadataConsistencyRun> _checkMetadataConsistencyRuns;

  private Map<String, MTMCluster> _mtmClusterByTenantId;
  private Map<ObjectId, Boolean> _flappingDetectionEnabled;

  @Inject
  public GroupAlertProcessingContext(
      final OnlineArchiveHistoryDao pOnlineArchiveHistoryDao,
      final OnlineArchiveExpirationHistoryDao pOnlineArchiveExpirationHistoryDao,
      final AutomationAgentAuditSvc pAutomationAgentAuditSvc,
      final NDSProxyAuditSvc pNDSProxyAuditSvc,
      final AutomationMongoDbVersionSvc pVersionSvc,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final AutomationConfigAliasSvc pAutomationConfigAliasSvc,
      final BackupGroupConfigDao pBackupGroupConfigDao,
      final HostSvc pHostSvc,
      final DecoupledMongotSvc pDecoupledMongotSvc,
      final SearchInstanceSvc pSearchInstanceSvc,
      final SearchDeploymentSvc pSearchDeploymentSvc,
      final HostClusterSvc pHostClusterSvc,
      final HostLastPingSvc pHostLastPingSvc,
      final CanonicalHostSvc pCanonicalHostSvc,
      final HostClusterLifecycleSvc pHostClusterLifecycleSvc,
      final HostAlertSvc pHostAlertSvc,
      final MetricsSvc pMetricsSvc,
      final CustomerMetricsQuerySvc pCustomerMetricsQuerySvc,
      final ObjectMapper pObjectMapper,
      final ServerlessMetricsSvc pServerlessMetricsSvc,
      final AgentPingSvc pAgentPingSvc,
      final AgentStatusSvc pAgentStatusSvc,
      final InvoiceDao pInvoiceDao,
      final MTMClusterDao pMTMClusterDao,
      final ClusterStatusDao pClusterStatusDao,
      final ClustershotDao pClustershotDao,
      final SyncDao pSyncDao,
      final ImplicitJobDaoV2 pJobDao,
      final OnlineArchiveSvc pOnlineArchiveSvc,
      final RetentionPolicyDao pRetentionPolicyDao,
      final DiskPartitionSvc pDiskPartitionSvc,
      final RestoreJobDao pRestoreJobDao,
      final BlockstoreJobDao pBlockstoreJobDao,
      final LastBiConnectorStatusDao pBiConnectorStatusDao,
      final BackupAgentSessionDao pBackupAgentSessionDao,
      final NDSM0ClusterActivitySvc pNdsM0ClusterActivitySvc,
      final NDSLookupSvc pNDSLookupSvc,
      final NDSUserCertDao pNdsUserCertDao,
      final BackupJobDao backupJobDao,
      final BackupSnapshotDao backupSnapshotDao,
      final AppSettings pAppSettings,
      final SearchForGroupAlertSvc pSearchForGroupAlertSvc,
      final CrashLogSvc pCrashLogSvc,
      final AlertConfigSvc pAlertConfigSvc,
      final RealmMeasurementSvc pRealmMeasurementSvc,
      final ClusterLookupSvc pClusterLookupSvc,
      final DbCheckSvc pDbCheckSvc,
      final ThirdPartyBackupJobDao thirdPartyBackupJobDao,
      final LiveImportSvc pLiveImportSvc,
      final NDSCheckMetadataConsistencySvc pCheckMetadataConsistencySvc,
      final AdminClusterLockSvc pAdminClusterLockSvc,
      @Assisted final Group pGroup,
      @Assisted final Organization pOrganization,
      @Assisted final AbstractGroupAlertProcessingBatchContext pGroupAlertProcessingBatchContext) {
    super(pGroupAlertProcessingBatchContext, pGroup, pOrganization);
    _onlineArchiveHistoryDao = pOnlineArchiveHistoryDao;
    _onlineArchiveExpirationHistoryDao = pOnlineArchiveExpirationHistoryDao;
    _versionSvc = pVersionSvc;
    _automationAgentAuditSvc = pAutomationAgentAuditSvc;
    _ndsProxyAuditSvc = pNDSProxyAuditSvc;
    _automationConfigSvc = pAutomationConfigSvc;
    _automationConfigAliasSvc = pAutomationConfigAliasSvc;
    _backupGroupConfigDao = pBackupGroupConfigDao;
    _hostSvc = pHostSvc;
    _decoupledMongotSvc = pDecoupledMongotSvc;
    _searchInstanceSvc = pSearchInstanceSvc;
    _searchDeploymentSvc = pSearchDeploymentSvc;
    _hostClusterSvc = pHostClusterSvc;
    _hostLastPingSvc = pHostLastPingSvc;
    _canonicalHostSvc = pCanonicalHostSvc;
    _hostClusterLifecycleSvc = pHostClusterLifecycleSvc;
    _hostAlertSvc = pHostAlertSvc;
    _metricsSvc = pMetricsSvc;
    _customerMetricsQuerySvc = pCustomerMetricsQuerySvc;
    _objectMapper = pObjectMapper;
    _serverlessMetricsSvc = pServerlessMetricsSvc;
    _agentPingSvc = pAgentPingSvc;
    _agentStatusSvc = pAgentStatusSvc;
    _mtmClusterDao = pMTMClusterDao;
    _clusterStatusDao = pClusterStatusDao;
    _clustershotDao = pClustershotDao;
    _retentionPolicyDao = pRetentionPolicyDao;
    _syncDao = pSyncDao;
    _jobDao = pJobDao;
    _invoiceDao = pInvoiceDao;
    _onlineArchiveSvc = pOnlineArchiveSvc;
    _diskPartitionSvc = pDiskPartitionSvc;
    _restoreJobDao = pRestoreJobDao;
    _blockstoreJobDao = pBlockstoreJobDao;
    _biConnectorStatusDao = pBiConnectorStatusDao;
    _backupAgentSessionDao = pBackupAgentSessionDao;
    _groupAlertProcessingBatchContext = pGroupAlertProcessingBatchContext;
    _ndsM0ClusterActivitySvc = pNdsM0ClusterActivitySvc;
    _ndsLookupSvc = pNDSLookupSvc;
    _ndsUserCertDao = pNdsUserCertDao;
    _backupJobDao = backupJobDao;
    _backupSnapshotDao = backupSnapshotDao;
    _appSettings = pAppSettings;
    _searchForGroupAlertSvc = pSearchForGroupAlertSvc;
    _crashLogSvc = pCrashLogSvc;
    _alertConfigSvc = pAlertConfigSvc;
    _realmMeasurementSvc = pRealmMeasurementSvc;
    _clusterLookupSvc = pClusterLookupSvc;
    _dbCheckSvc = pDbCheckSvc;
    _thirdPartyBackupJobDao = thirdPartyBackupJobDao;
    _liveImportSvc = pLiveImportSvc;
    _checkMetadataConsistencySvc = pCheckMetadataConsistencySvc;
    _adminClusterLockSvc = pAdminClusterLockSvc;

    if (pGroup != null) {
      pGroup
          .getGroupStorageConfig()
          .setRetentionPolicy(
              _retentionPolicyDao.findCachedById(
                  pGroup.getGroupStorageConfig().getRetentionPolicyId()));
    }

    _hosts = new HashMap<>();
    _backupAgentStatus = new HashMap<>();
    _successfulClustershots = new HashMap<>();
    _failedClustershots = new HashMap<>();
    _failedClustershotCounts = new HashMap<>();
    _hostDataPartitions = new HashMap<>();
    _hostIndexPartitions = new HashMap<>();
    _hostJournalPartition = new HashMap<>();
    _ftsDiskMeasurements = new HashMap<>();
    _diskMeasurements = new HashMap<>();
    _hostMeasurements = new HashMap<>();
    _ftsProcessMemoryMeasurements = new HashMap<>();
    _ftsProcessCpuMeasurements = new HashMap<>();
    _processMeasurements = new HashMap<>();
    _systemMemoryMeasurements = new HashMap<>();
    _clusterDescriptionIdsByHostname = new HashMap<>();
    _queuedCpsSnapshots = new HashMap<>();
    _serverlessClusterMeasurements = new HashMap<>();
    _lastServerlessProxyAuditsByMTM = new HashMap<>();
    _lastFlexProxyAuditsByMTM = new HashMap<>();
    _alertableServerlessInstances = new HashMap<>();
    _alertableFlexClusters = new HashMap<>();
    _alertableRealmInstances = new HashMap<>();
    _ftsIndexStatsMeasurements = new HashMap<>();
    _ftsServerStatusMeasurements = new HashMap<>();
    _realmMeasurements = new HashMap<>();
    _hostClusterClusterIdToClusterUniqueId = new HashMap<>();
    _mtmClusterUniqueIdToSomeServerlessTenantIds = new HashMap<>();
    _mtmClusterUniqueIdToSomeFlexTenantIds = new HashMap<>();
    _mtmClusterByTenantId = new HashMap<>();
    _flappingDetectionEnabled = new HashMap<>();
  }

  private static final Gauge FROM_TIMESTAMP_FOR_METRICS_QUERY =
      Gauge.build()
          .name("mms_groupalertprocessingcontext_start_metrics_query")
          .help("from query endpoint for all metrics svc requests")
          .register();

  private static final Counter METRICS_SVC_CALLS =
      Counter.build()
          .name("mms_groupalertprocessingcontext_metrics_svc_calls_total")
          .help("Total number of calls to MetricsSvc methods during alert processing")
          .labelNames("method_name", "host_data_source")
          .register();

  private void incrementMetricsSvcCounter(
      final String methodName, final Host.DataSource pHostDataSource) {
    String hostDataSource = pHostDataSource == null ? "null" : pHostDataSource.toString();
    METRICS_SVC_CALLS.labels(methodName, hostDataSource).inc();
  }

  public Collection<RestoreStatus> getIncompleteRestoreJobs() {
    if (_incompleteRestoreJobs == null) {
      _incompleteRestoreJobs =
          Collections.unmodifiableList(_restoreJobDao.getAllIncompleteJobs(getGroupId()));
    }

    return _incompleteRestoreJobs;
  }

  public List<BasicDBObject> getCanonicalHosts() {
    if (_canonicalHosts == null) {
      _canonicalHosts =
          Collections.unmodifiableList(_canonicalHostSvc.findCanonicalHostsByGroupId(getGroupId()));
    }
    return _canonicalHosts;
  }

  // TODO mb: This is NOT group-specific; optimize?
  public AutomationMongoDbVersionSvc getVersionSvc() {
    return _versionSvc;
  }

  public boolean isMaintenanceUpgradeAvailable(
      final VersionUtils.Version pVersion, final boolean isAtlas) {
    return getVersionSvc().isMaintenanceUpgradeAvailable(pVersion, isAtlas);
  }

  /**
   * The minimum version is defined as the latest revision for two major releases ago. For example,
   * if 3.6.1 is the current version than 3.2.latest is the minimum version.
   */
  public VersionUtils.Version getMinimumVersion() {
    final boolean isAtlas = getOrganization().isAtlas();
    final VersionManifest versionManifest = _versionSvc.getDefaultVersionManifest(isAtlas);
    VersionUtils.Version latestVersion =
        isAtlas
            ? versionManifest.getCurrentLatestProductionVersion()
            : versionManifest.getCurrentLatestOMCMVersion();
    return getVersionManifest()
        .getPreviousLatestVersion(
            getVersionManifest().getPreviousLatestVersion(latestVersion, !isAtlas), !isAtlas);
  }

  public VersionManifest getVersionManifest() {
    final boolean isAtlas = getOrganization().isAtlas();
    return _versionSvc.getDefaultVersionManifest(isAtlas);
  }

  public Host getHost(final String pHostId) {
    Optional<Host> hostOpt = _hosts.get(pHostId);
    if (hostOpt == null) {
      hostOpt = Optional.ofNullable(_hostAlertSvc.findHostByIdForAlerts(pHostId, getGroupId()));
      _hosts.put(pHostId, hostOpt);
    }
    return hostOpt.orElse(null);
  }

  private static boolean configHasDedicatedMongots(final AutomationConfig pConfig) {
    return pConfig != null
        && pConfig.getDeployment() != null
        && pConfig.getDeployment().getMongots() != null
        && pConfig.getDeployment().getMongots().stream().anyMatch(MongotConfig::isOnDedicatedHost);
  }

  public List<MonitoredHost> getMonitoredHosts() {
    if (_monitoredHosts == null) {
      final List<Host> hosts = _hostAlertSvc.loadHostsForAlerts(getGroupId());
      for (final Host host : hosts) {
        _hosts.put(host.getId(), Optional.of(host));
      }

      final List<BasicDBObject> canonicalHosts = getCanonicalHosts();
      final List<MonitoredHost> monitoredHosts =
          _hostClusterLifecycleSvc.filterCanonicalAndDeletedHosts(
              getGroupId(), canonicalHosts, hosts);

      if (configHasDedicatedMongots(getPublishedAutomationConfig())) {
        final List<MonitoredHost> dedicatedMongotHosts =
            _decoupledMongotSvc.getDedicatedMongotsMonitoredHostByGroupId(getGroupId());
        for (final MonitoredHost dedicatedMongotHost : dedicatedMongotHosts) {
          if (dedicatedMongotHost
              .getSearchInstanceState()
              .equals(DedicatedMongotHost.State.RUNNING.toString())) {
            monitoredHosts.add(dedicatedMongotHost);
          }
        }
      }
      _monitoredHosts = Collections.unmodifiableList(monitoredHosts);
    }
    return _monitoredHosts;
  }

  public Collection<HostCluster> getReplicaSets() {
    loadReplicaSetsAndShardedClusters();
    return _replicaSets.values();
  }

  public HostCluster getReplicaSetByName(final String pRsId) {
    loadReplicaSetsAndShardedClusters();
    return _replicaSets.values().stream()
        .filter(hc -> hc.getReplicaSetIds().contains(pRsId))
        .findFirst()
        .orElse(null);
  }

  public HostCluster getReplicaSetByClusterId(final ObjectId pClusterId) {
    loadReplicaSetsAndShardedClusters();
    return _replicaSets.get(pClusterId);
  }

  public HostCluster getShardedCluster(final ObjectId pClusterId) {
    loadReplicaSetsAndShardedClusters();
    return _shardedClusters.get(pClusterId);
  }

  public Collection<HostCluster> getShardedClusters() {
    loadReplicaSetsAndShardedClusters();
    return _shardedClusters.values();
  }

  public Map<ObjectId, ClusterStatus> getClusterStatuses() {
    if (_clusterStatus == null) {
      final Map<ObjectId, ClusterStatus> clusterStatus = new HashMap<>();
      for (final ClusterStatus cs : _clusterStatusDao.getClustersForGroup(getGroupId())) {
        clusterStatus.put(cs.getClusterId(), cs);
      }
      _clusterStatus = Collections.unmodifiableMap(clusterStatus);
    }
    return _clusterStatus;
  }

  public ClusterStatus getClusterStatus(final ObjectId pClusterId) {
    return getClusterStatuses().get(pClusterId);
  }

  public Map<Pair<ObjectId, String>, BackupStatus> getBackupStatuses() {
    if (_backupStatus == null) {
      final Map<Pair<ObjectId, String>, BackupStatus> backupStatus = new HashMap<>();
      for (final BackupStatus bs : _jobDao.getBackupsForGroup(getGroupId())) {
        backupStatus.put(Pair.of(bs.getGroupId(), bs.getRsId()), bs);
      }
      _backupStatus = Collections.unmodifiableMap(backupStatus);
    }

    return _backupStatus;
  }

  public BackupStatus getBackupStatus(final String pRsId) {
    return getBackupStatuses().get(Pair.of(getGroupId(), pRsId));
  }

  public Map<ObjectId, ThirdPartyJob> getThirdPartyBackupJobs() {
    if (_thirdPartyBackupJobs == null || _thirdPartyBackupJobs.isEmpty()) {
      _thirdPartyBackupJobs = new HashMap<>();
      for (final ThirdPartyJob job : _thirdPartyBackupJobDao.getThirdPartyJobs(getGroupId())) {
        _thirdPartyBackupJobs.put(job.getId(), job);
      }
    }

    return _thirdPartyBackupJobs;
  }

  public ThirdPartyJob getThirdPartyBackupJob(final ObjectId jobId) {
    return getThirdPartyBackupJobs().get(jobId);
  }

  public List<BackupSnapshot> getQueuedCpsSnapshots(final ObjectId pClusterId) {
    if (!_queuedCpsSnapshots.containsKey(pClusterId)) {
      List<BackupSnapshot> snapshots =
          _backupSnapshotDao.findActiveQueuedNonCopy(
              getGroupId(), pClusterId, DriverUtils.SECONDARY_PREFERRED_MINIMUM);
      _queuedCpsSnapshots.put(pClusterId, snapshots);
    }
    return _queuedCpsSnapshots.get(pClusterId);
  }

  public List<Date> getQueuedCpsSnapshotScheduledCreationDates(final ObjectId pClusterId) {
    return getQueuedCpsSnapshots(pClusterId).stream()
        .map(BackupSnapshot::getScheduledCreationDate)
        .collect(Collectors.toList());
  }

  public Optional<BackupSnapshot> getLastSuccessfulCpsSnapshot(
      final ObjectId pProjectId, final ObjectId pClusterId) {
    return _backupSnapshotDao.findLastCompletedNonCopyByCluster(
        pProjectId, pClusterId, false, DriverUtils.SECONDARY_PREFERRED_MINIMUM);
  }

  public Optional<Date> getLastSuccessfulCpsSnapshotInitiationDate(
      final ObjectId pProjectId, final ObjectId pClusterId) {
    Optional<BackupSnapshot> snapshot = getLastSuccessfulCpsSnapshot(pProjectId, pClusterId);
    if (snapshot.isEmpty()) {
      return Optional.empty();
    }
    return snapshot.map(BackupSnapshot::getSnapshotInitiationDate);
  }

  public boolean isEncryptionAvailable() {
    return BackupEncryptionUtil.isGroupSupportEncryption(_backupGroupConfigDao, getGroup());
  }

  public AgentStatus getMonitoringAgentStatus(final long pMinDowntimeMsec) {
    final Long latestPingOrConf = getMonitoringAgentPing().getLatestPingOrConf();
    return _agentStatusSvc.getMonitoringAgentStatus(latestPingOrConf, pMinDowntimeMsec);
  }

  public Set<String> getBackupAgentTags() {
    final Set<String> tags =
        getBackupStatuses().values().stream()
            .filter(s -> !s.isRetiredClusterMember())
            .map(s -> s.getAgentTag().getName())
            .collect(Collectors.toSet());

    getClusterStatuses().values().stream().map(s -> s.getAgentTag().getName()).forEach(tags::add);

    return tags;
  }

  public AgentStatus getBackupAgentStatus(final String pTag, final long pMinDowntimeMsec) {
    if (!_backupAgentStatus.containsKey(pTag)) {
      _backupAgentStatus.put(
          pTag, _agentStatusSvc.existsReachableBackupAgent(getGroupId(), pTag, pMinDowntimeMsec));
    }
    return _backupAgentStatus.get(pTag);
  }

  public List<AgentAudit> getAutomationAgentAudits() {
    final AutomationConfig config = getPublishedAutomationConfig();
    if (_automationAgentAudits == null) {
      _automationAgentAudits =
          Collections.unmodifiableList(
              _automationAgentAuditSvc.findCurrent(
                  getGroup().getId(),
                  config == null
                      ? Collections.emptyList()
                      : config.getDeployment().getManagedHostnames()));
    }

    return _automationAgentAudits;
  }

  public List<NDSProxyAudit> getNDSProxyAudits() {
    if (_ndsProxyAudits == null) {
      List<MTMCluster> mtmClusters = getMtmClustersForGroup();
      if (mtmClusters == null || mtmClusters.isEmpty()) {
        return Collections.emptyList();
      }
      _ndsProxyAudits =
          Collections.unmodifiableList(
              _ndsProxyAuditSvc.findCurrent(getGroup().getId(), mtmClusters));
    }

    return _ndsProxyAudits;
  }

  public AgentPing getMonitoringAgentPing() {
    if (_agentPing == null) {
      _agentPing = _agentPingSvc.findByGroupId(getGroupId());
    }
    return _agentPing;
  }

  public NDSM0ClusterActivitySvc getNdsM0ClusterActivitySvc() {
    return _ndsM0ClusterActivitySvc;
  }

  public boolean isNdsM0ClusterPauseFreeTierMonitoringEnabled() {
    return getNdsM0ClusterActivitySvc().isPauseFreeTierMonitoringEnabled();
  }

  public boolean isNdsM0ClusterActive(ClusterDescription pClusterDescription) {
    return getNdsM0ClusterActivitySvc().isActiveCluster(pClusterDescription);
  }

  public ClusterDescriptionId getClusterDescriptionId(final String pHostname) {
    Optional<ClusterDescriptionId> id = _clusterDescriptionIdsByHostname.get(pHostname);

    // Compare the Optional to null to avoid re-querying when we've already determined
    // the hardware doesn't exist.
    if (id == null) {
      final Optional<ReplicaSetHardware> hardware =
          _ndsLookupSvc.getReplicaSetHardwareForHostname(pHostname, getGroupId());
      if (hardware.isEmpty()) {
        LOG.debug("No replica set hardware found for hostname ({})", pHostname);

        // update as empty for next time.
        id = Optional.empty();
      } else {
        final Optional<ClusterDescriptionId> newId =
            Optional.of(
                new ClusterDescriptionId(
                    hardware.get().getClusterName(), hardware.get().getGroupId()));

        // populate all the hardware's hostnames for any future callers since we already
        // went through the work of loading the hardware for this one hostname.
        hardware
            .get()
            .getAllHardware()
            .filter(ih -> ih.getHostnameForAgents().isPresent())
            .map(ih -> ih.getHostnameForAgents().get())
            .forEach(hostname -> _clusterDescriptionIdsByHostname.put(hostname, newId));

        // update for return value
        id = newId;
      }

      _clusterDescriptionIdsByHostname.put(pHostname, id);
    }

    return id.orElse(null);
  }

  public Optional<ClusterDescription> getClusterDescription(
      final ObjectId pGroupId, final String pClusterName) {
    return _ndsLookupSvc.getActiveClusterDescription(pGroupId, pClusterName);
  }

  public Optional<ClusterDescription> getClusterDescriptionByUniqueId(
      @Nullable final ObjectId pOptionalGroupId, final ObjectId pUniqueId) {
    final Optional<ClusterDescription> cachedClusterDescription =
        getCachedClusterDescription(pUniqueId);
    if (cachedClusterDescription.isPresent()) {
      return cachedClusterDescription;
    }
    final Optional<ClusterDescription> loadedClusterDescription =
        _ndsLookupSvc.getActiveClusterDescription(pOptionalGroupId, pUniqueId);
    loadedClusterDescription.ifPresent(_cachedLookedUpClusterDescriptions::add);
    return loadedClusterDescription;
  }

  private Optional<ClusterDescription> getCachedClusterDescription(final ObjectId pUniqueId) {
    if (_cachedLookedUpClusterDescriptions == null) {
      _cachedLookedUpClusterDescriptions = new LinkedList<>();
    }
    return _cachedLookedUpClusterDescriptions.stream()
        .filter(pClusterDescription -> pClusterDescription.getUniqueId().equals(pUniqueId))
        .findFirst();
  }

  public List<ClusterDescription> getActiveClusterDescriptions() {
    if (_activeClusterDescriptions != null) {
      return _activeClusterDescriptions;
    }
    _activeClusterDescriptions = _ndsLookupSvc.getActiveClusterDescriptions(getGroupId());
    return _activeClusterDescriptions;
  }

  public List<AlertConfig> getAlertConfigsForGroup() {
    return _alertConfigSvc.getAlertConfigs(getGroupId());
  }

  public List<ReplicaSetHardware> getReplicaSetHardwaresForCluster(
      final ObjectId pGroupId, final String pClusterName) {
    return _ndsLookupSvc.getReplicaSetHardwareForCluster(pGroupId, pClusterName);
  }

  public List<InstanceHardware> getSearchNodeInstancesForCluster(
      final ObjectId pGroupId, final String pClusterName) {
    var clusterDescription = _ndsLookupSvc.getActiveClusterDescription(pGroupId, pClusterName);
    if (clusterDescription.isEmpty()) {
      return Collections.emptyList();
    }

    return _searchInstanceSvc.findSearchInstancesByClusterUniqueId(
        pGroupId, clusterDescription.get().getUniqueId(), DriverUtils.SECONDARY_PREFERRED_MINIMUM);
  }

  public EnumSet<SearchDeploymentTargets> getSearchDeploymentTargetsForCluster(
      final ObjectId pGroupId, final String pClusterName) {
    var clusterDescription = _ndsLookupSvc.getActiveClusterDescription(pGroupId, pClusterName);
    if (clusterDescription.isEmpty()) {
      return EnumSet.noneOf(SearchDeploymentTargets.class);
    }

    return _searchDeploymentSvc.getDeployedSearchTargetsForMongoCluster(
        clusterDescription.get().getUniqueId(), DriverUtils.SECONDARY_PREFERRED_MINIMUM);
  }

  public List<BackupJob> getCpsBackupJobs() {
    if (_cpsBackupJobs == null) {
      _cpsBackupJobs =
          _backupJobDao.findForProject(getGroupId(), DriverUtils.SECONDARY_PREFERRED_MINIMUM);
    }
    return _cpsBackupJobs;
  }

  public List<DbCheck> getDbChecks() {
    if (_dbChecks == null) {
      _dbChecks = _dbCheckSvc.findExistingActiveForGroup(getGroupId());
    }
    return _dbChecks;
  }

  @Override
  public List<AdminClusterLock> getAdminClusterLocks() {
    if (_adminClusterLocks == null) {
      _adminClusterLocks = _adminClusterLockSvc.findByGroupId(getGroupId());
    }
    return _adminClusterLocks;
  }

  public List<CheckShardedMetadataConsistencyRun> getCheckMetadataConsistencyRuns() {
    if (_checkMetadataConsistencyRuns == null) {
      _checkMetadataConsistencyRuns =
          _checkMetadataConsistencySvc.findActiveRunsForGroup(getGroupId());
    }
    return _checkMetadataConsistencyRuns;
  }

  public Optional<ClusterDescription> getClusterDescriptionByDeploymentClusterName(
      final String pDeploymentClusterName) {
    if (_cachedLookedUpClusterDescriptions == null) {
      _cachedLookedUpClusterDescriptions = new LinkedList<>();
    }

    final Optional<ClusterDescription> cachedClusterDescription =
        _cachedLookedUpClusterDescriptions.stream()
            .filter(cd -> cd.getDeploymentClusterName().equals(pDeploymentClusterName))
            .findFirst();
    if (cachedClusterDescription.isPresent()) {
      return cachedClusterDescription;
    }

    final Optional<ClusterDescription> loadedClusterDescription =
        _ndsLookupSvc.getClusterDescriptionForDeploymentClusterName(
            getGroupId(), pDeploymentClusterName);
    loadedClusterDescription.ifPresent(_cachedLookedUpClusterDescriptions::add);
    return loadedClusterDescription;
  }

  public Optional<ClusterDescription> getClusterDescriptionByInstanceHostname(
      final String pHostname) {
    final ClusterDescriptionId clusterDescriptionId = getClusterDescriptionId(pHostname);
    if (clusterDescriptionId == null) {
      return Optional.empty();
    }

    if (_cachedLookedUpClusterDescriptions == null) {
      _cachedLookedUpClusterDescriptions = new LinkedList<>();
    }

    final Optional<ClusterDescription> cachedClusterDescription =
        _cachedLookedUpClusterDescriptions.stream()
            .filter(cd -> cd.getName().equals(clusterDescriptionId.getClusterName()))
            .findFirst();
    if (cachedClusterDescription.isPresent()) {
      return cachedClusterDescription;
    }

    final Optional<ClusterDescription> loadedClusterDescription =
        _ndsLookupSvc.getActiveClusterDescription(
            getGroupId(), clusterDescriptionId.getClusterName());
    loadedClusterDescription.ifPresent(_cachedLookedUpClusterDescriptions::add);
    return loadedClusterDescription;
  }

  public long getLastPingProcessedTime() {
    final Long lastPingProcessedTime = getMonitoringAgentPing().getLastPingProcessedTime();
    return lastPingProcessedTime == null ? 0L : lastPingProcessedTime;
  }

  public Long getLastServerlessMetricsSnapshotPingTime(final ObjectId pClusterUniqueId) {
    MetricDataSnapshot<ServerlessClusterMeasurement> snapshot;
    try {
      snapshot = getServerlessClusterMeasurements(pClusterUniqueId);
    } catch (final MetricsException ex) {
      LOG.warn("Could not find metrics snapshot for clusterUniqueId={}", pClusterUniqueId);
      snapshot = null;
    }
    if (snapshot != null && !snapshot.getMeasurements().isEmpty()) {
      final List<ServerlessClusterMeasurement> measurements = snapshot.getMeasurements();
      return measurements
          .get(measurements.size() - 1)
          .getSampleTime()
          .getTime(); // measurements come presorted by ascending sample time
    }

    return null;
  }

  public List<NDSProxyAudit> getLastProxyAudits(final ObjectId pTenantClusterUniqueId) {
    if (!_mtmClusterByTenantId.containsKey(pTenantClusterUniqueId.toString())) {
      final Optional<ServerlessMTMCluster> mtmCluster =
          _ndsLookupSvc.findServerlessMTMClusterByTenantClusterDescriptionUniqueId(
              pTenantClusterUniqueId);
      if (mtmCluster.isEmpty()) {
        LOG.warn(
            "Could not find mtm cluster for tenant with groupId={}, tenantClusterId={}",
            getGroupId(),
            pTenantClusterUniqueId);
        return Collections.emptyList();
      }

      final Set<String> tenantIds =
          _ndsLookupSvc.findTenantIdsFromServerlessMTM(
              mtmCluster.get().getGroupId(), mtmCluster.get().getName());
      tenantIds.forEach(tenantId -> _mtmClusterByTenantId.put(tenantId, mtmCluster.get()));
    }

    final MTMCluster mtmCluster = _mtmClusterByTenantId.get(pTenantClusterUniqueId.toString());
    if (mtmCluster != null && !_lastServerlessProxyAuditsByMTM.containsKey(mtmCluster)) {
      final List<NDSProxyAudit> proxyAuditList =
          _ndsProxyAuditSvc.findCurrent(mtmCluster.getGroupId(), List.of(mtmCluster));
      if (proxyAuditList.isEmpty()) {
        LOG.warn(
            "Could not find NDSProxyAudit for MTM groupId={}, MTM name={}",
            mtmCluster.getGroupId(),
            mtmCluster.getName());
        return Collections.emptyList();
      }

      _lastServerlessProxyAuditsByMTM.put(mtmCluster, proxyAuditList);
    }

    final List<NDSProxyAudit> result = _lastServerlessProxyAuditsByMTM.get(mtmCluster);
    return Optional.ofNullable(result).orElse(Collections.emptyList());
  }

  public List<NDSProxyAudit> getLastFlexProxyAudits(final ObjectId pTenantClusterUniqueId) {
    if (!_mtmClusterByTenantId.containsKey(pTenantClusterUniqueId.toString())) {
      final Optional<FlexMTMCluster> mtmCluster =
          _ndsLookupSvc.findFlexMTMClusterByTenantClusterDescriptionUniqueId(
              pTenantClusterUniqueId);
      if (mtmCluster.isEmpty()) {
        LOG.warn(
            "Could not find mtm cluster for tenant with groupId={}, tenantClusterId={}",
            getGroupId(),
            pTenantClusterUniqueId);
        return Collections.emptyList();
      }

      final Set<String> tenantIds =
          _ndsLookupSvc.findTenantIdsFromFlexMTM(
              mtmCluster.get().getGroupId(), mtmCluster.get().getName());
      tenantIds.forEach(tenantId -> _mtmClusterByTenantId.put(tenantId, mtmCluster.get()));
    }

    final MTMCluster mtmCluster = _mtmClusterByTenantId.get(pTenantClusterUniqueId.toString());
    if (mtmCluster != null && !_lastFlexProxyAuditsByMTM.containsKey(mtmCluster)) {
      final List<NDSProxyAudit> proxyAuditList =
          _ndsProxyAuditSvc.findCurrent(mtmCluster.getGroupId(), List.of(mtmCluster));
      if (proxyAuditList.isEmpty()) {
        LOG.warn(
            "Could not find NDSProxyAudit for MTM groupId={}, MTM name={}",
            mtmCluster.getGroupId(),
            mtmCluster.getName());
        return Collections.emptyList();
      }

      _lastFlexProxyAuditsByMTM.put(mtmCluster, proxyAuditList);
    }

    final List<NDSProxyAudit> result = _lastFlexProxyAuditsByMTM.get(mtmCluster);
    return Optional.ofNullable(result).orElse(Collections.emptyList());
  }

  public static boolean belongsToMTMCluster(
      final TenantCloudProviderContainer pContainer, final MTMCluster pCluster) {
    return pContainer.getClusterName().equals(pCluster.getName())
        && pContainer.getGroupId().equals(pCluster.getGroupId());
  }

  public Invoice getPendingInvoice() {
    if (_pendingInvoice == null) {
      _pendingInvoice = Optional.ofNullable(_invoiceDao.findPendingMonthlyByOrgId(getOrgId()));
    }
    return _pendingInvoice.orElse(null);
  }

  public Clustershot getLastSuccessfulClustershot(final ObjectId pClusterId) {
    if (!_successfulClustershots.containsKey(pClusterId)) {
      _successfulClustershots.put(
          pClusterId, _clustershotDao.findLastSuccessfulClustershot(getGroupId(), pClusterId));
    }
    return _successfulClustershots.get(pClusterId);
  }

  public Clustershot getLastFailedClustershot(final ObjectId pClusterId) {
    if (!_failedClustershots.containsKey(pClusterId)) {
      _failedClustershots.put(
          pClusterId, _clustershotDao.findLastFailedClustershot(getGroupId(), pClusterId));
    }
    return _failedClustershots.get(pClusterId);
  }

  public int getFailedClustershotCount(final ObjectId pClusterId) {
    if (!_failedClustershotCounts.containsKey(pClusterId)) {
      final Clustershot lastSuccess = getLastSuccessfulClustershot(pClusterId);
      final BSONTimestamp since =
          lastSuccess != null ? lastSuccess.getTimestamp() : new BSONTimestamp();
      _failedClustershotCounts.put(
          pClusterId,
          _clustershotDao.countFailedClustershotsSince(getGroupId(), pClusterId, since));
    }
    return _failedClustershotCounts.get(pClusterId);
  }

  public List<DBObject> getBackupAgentSessions() {
    if (_backupAgentSessions == null) {
      // There's only a session for the primary agent. So a list with more than one
      // session here would indicate backup tags are in use.
      _backupAgentSessions =
          Collections.unmodifiableList(_backupAgentSessionDao.getForGroup(getGroupId()));
    }
    return _backupAgentSessions;
  }

  public DBObject getBackupAgentSession(final String pTag) {
    return getBackupAgentSessions().stream()
        .filter(s -> pTag.equals(s.get("tag")))
        .findFirst()
        .orElse(null);
  }

  public List<OnlineArchive> findArchivesForGroup() {
    return _onlineArchiveSvc.findArchivesForGroup(getGroupId());
  }

  public List<OnlineArchive> findArchivesForGroupCreatedBefore(final Date pBeforeDate) {
    return _onlineArchiveSvc.findArchivesForGroup(getGroupId(), pBeforeDate);
  }

  public Optional<OnlineArchiveHistory> findOnlineArchiveHistoryOnDate(
      final ObjectId pArchiveId, final Date pDate) {
    return _onlineArchiveHistoryDao.find(pArchiveId, pDate);
  }

  public List<OnlineArchiveExpirationHistory> findOnlineArchiveExpirationHistoryOnDate(
      final ObjectId pArchiveId, final Date pStartDate, final Date pEndDate) {
    return _onlineArchiveExpirationHistoryDao.find(pArchiveId, pStartDate, pEndDate);
  }

  private List<Host> getHostsForCluster(final HostCluster pHostCluster) {
    // hostIds for the cluster that haven't already been loaded individually.

    final Set<String> unloadedHostIds = getUnloadedHostIds(pHostCluster.getHostIds());
    if (!unloadedHostIds.isEmpty()) {
      final List<Host> foundHosts =
          _hostSvc.findHostsByIds(
              pHostCluster.getGroupId(), SetUtils.toList(unloadedHostIds), false);
      for (final Host host : foundHosts) {
        _hosts.put(host.getId(), Optional.of(host));
        unloadedHostIds.remove(host.getId());
      }

      // If any hostIds remaining then they must not be in database at all.
      // Add empty Optional mappings for them. This avoids any chance of
      // NullPointerException below.
      for (final String missingHostId : unloadedHostIds) {
        _hosts.put(missingHostId, Optional.empty());
      }
    }

    // We're now guaranteed to at least have attempted to load every Host
    // for the given cluster's hostIds; any Host not present in map is not present in database.
    // Now go back through and add all Hosts to cluster using _hosts map.
    final List<Host> hosts = new ArrayList<>(pHostCluster.getHostIds().size());
    for (final String hostId : pHostCluster.getHostIds()) {
      final Host host = _hosts.get(hostId).orElse(null);
      if (host != null) {
        hosts.add(host);
      }
    }

    return hosts;
  }

  private Set<String> getUnloadedHostIds(final Set<String> pHostIds) {
    final Set<String> unloadedHostIds = new HashSet<>();
    for (final String hostId : pHostIds) {
      if (!_hosts.containsKey(hostId)) {
        unloadedHostIds.add(hostId);
      }
    }
    return unloadedHostIds;
  }

  public BasicBSONObject getLastPingForHost(final String pHostId) {
    return _hostLastPingSvc.getLastPing(pHostId);
  }

  public Collection<Host> getMongosHosts(final ObjectId pClusterId) {
    final HostCluster cluster = getShardedCluster(pClusterId);
    if (cluster != null) {
      if (cluster.getHosts() == null) {
        cluster.setHosts(getHostsForCluster(cluster));
      }
      return cluster.getMongosHosts();
    }
    return null;
  }

  public Collection<Host> getReplicaSetHosts(final String pRsId) {
    final HostCluster cluster = getReplicaSetByName(pRsId);
    if (cluster != null) {
      if (cluster.getHosts() == null) {
        cluster.setHosts(getHostsForCluster(cluster));
      }
      return cluster.getHosts();
    }
    return null;
  }

  public Sync getSync(final ObjectId pSyncId) {
    return _syncDao.getSync(pSyncId);
  }

  public Set<String> getHostDataPartitions(final String pHostId) {
    loadAllHostPartitions();
    final Set<String> partitionNames = _hostDataPartitions.get(pHostId);
    return partitionNames == null ? Collections.emptySet() : partitionNames;
  }

  public Set<String> getHostIndexPartitions(final String pHostId) {
    loadAllHostPartitions();
    final Set<String> partitionNames = _hostIndexPartitions.get(pHostId);
    return partitionNames == null ? Collections.emptySet() : partitionNames;
  }

  public String getHostJournalPartition(final String pHostId) {
    loadAllHostPartitions();
    return _hostJournalPartition.get(pHostId);
  }

  public AutomationConfig getPublishedAutomationConfig() {
    if (_publishedAutomationConfig == null) {
      _publishedAutomationConfig =
          Optional.ofNullable(_automationConfigSvc.findPublished(getGroupId()));
    }
    return _publishedAutomationConfig.orElse(null);
  }

  public List<MTMCluster> getMtmClustersForGroup() {
    if (_mtmClusters == null) {
      _mtmClusters =
          _mtmClusterDao.findClustersByGroupId(
              getGroupId(), DriverUtils.SECONDARY_PREFERRED_MINIMUM);
    }

    return _mtmClusters;
  }

  /**
   * The Search Alert code does not currently look up the cluster for a host. Instead, it pulls all
   * the configs for the entire NDS Project and sifts through them for the desired hostname.
   */
  public List<FTSIndexConfig> getFtsIndexConfigsForGroup() {
    if (_ftsIndexConfigs == null) {
      _ftsIndexConfigs =
          isMongotConfigured()
              ? _searchForGroupAlertSvc.getFTSIndexConfigsForGroup(
                  getGroupId(), DriverUtils.SECONDARY_PREFERRED_MINIMUM)
              : List.of();
    }

    return _ftsIndexConfigs;
  }

  public RequiredSearchDiskResult getRequiredDiskSpaceForSearchReindexing(
      final Group pGroup, final String pHostId, final String pHostname) throws MetricsException {
    if (!isMongotConfigured()) {
      return new RequiredSearchDiskResult(false, 0, 0);
    }

    return _searchForGroupAlertSvc.calculateRequiredDiskSpaceForSearchReindexing(
        pGroup, pHostId, pHostname, getFtsIndexConfigsForGroup());
  }

  public List<CrashLog> getMongotCrashLogsForGroup() {
    if (_mongotCrashLogs == null) {
      _mongotCrashLogs =
          _crashLogSvc.getCrashLogsByType(getGroupId(), CrashLog.CrashLogType.MONGOT);
    }

    return _mongotCrashLogs;
  }

  public List<BiConnectorAlertCheckTarget> getBiConnectors(
      final List<MongosqldConfig> pManagedConnectors) {
    final List<LastBiConnectorStatus> dbStatuses =
        _biConnectorStatusDao.findByGroupId(getGroupId());
    final Map<String, LastBiConnectorStatus> statusByHostname =
        dbStatuses.stream()
            .collect(
                Collectors.toMap(LastBiConnectorStatus::getAgentHostname, Function.identity()));

    return pManagedConnectors.stream()
        .filter(
            connector -> {
              final LastBiConnectorStatus status = statusByHostname.get(connector.getHostname());
              return status != null && status.getEntryById(connector.getId()).isPresent();
            })
        .map(
            connector -> {
              final LastBiConnectorStatus status = statusByHostname.get(connector.getHostname());
              final BiConnectorStatusEntry entry = status.getEntryById(connector.getId()).get();
              return new BiConnectorAlertCheckTarget(
                  status.getGroupId(),
                  connector.getHostname(),
                  connector.getPort(),
                  connector.getDeploymentItem(),
                  connector.getId(),
                  entry.getLastUp(),
                  status.getAgentTime(),
                  status.getReceivedTime());
            })
        .collect(Collectors.toList());
  }

  public boolean isAnyQueryableRestoreDaemonAvailable() {
    return _groupAlertProcessingBatchContext.isOneQueryableRestoreDaemonAvailable();
  }

  public boolean isOneQueryableRestoreDaemonAvailable(
      final Collection<DaemonMachine> pDaemonMachines) {
    return _groupAlertProcessingBatchContext.isOneQueryableRestoreDaemonAvailable(pDaemonMachines);
  }

  private void loadAllHostPartitions() {
    if (!_allDiskPartitionsLoaded) {
      final List<DiskPartition> allDiskPartitions =
          _diskPartitionSvc.findByGroupId(
              getGroupId(),
              ReadPreference.secondaryPreferred().withMaxStalenessMS(90L, TimeUnit.SECONDS));
      final Map<String, Set<DiskPartition>> partitionsByHostId =
          new HashMap<>(allDiskPartitions.size());
      for (final DiskPartition diskPartition : allDiskPartitions) {
        for (final String hostId : diskPartition.getAllHostIds()) {
          Set<DiskPartition> partitionsForHostId = partitionsByHostId.get(hostId);
          if (partitionsForHostId == null) {
            partitionsForHostId = new HashSet<>();
            partitionsByHostId.put(hostId, partitionsForHostId);
          }
          partitionsForHostId.add(diskPartition);
        }
      }

      for (final Map.Entry<String, Set<DiskPartition>> entry : partitionsByHostId.entrySet()) {
        final Set<String> dataPartitions = new HashSet<>();
        final Set<String> indexPartitions = new HashSet<>();
        DiskPartition journalPartition = null;

        final String hostId = entry.getKey();
        for (final DiskPartition diskPartition : entry.getValue()) {
          if (diskPartition.getDataPartitionAllocations().stream()
              .anyMatch(a -> a.getHostId().equals(hostId))) {
            dataPartitions.add(diskPartition.getPartitionName());
          }

          if (diskPartition.getIndexPartitionAllocations().stream()
              .anyMatch(a -> a.getHostId().equals(hostId))) {
            indexPartitions.add(diskPartition.getPartitionName());
          }

          if (diskPartition.getJournalHostIds().contains(hostId)) {
            if (journalPartition != null) {
              // There is more than 1 partition that contains the journal. Take the most recently
              // updated. This is expected with some RAID setups, but could indicate host aliasing
              // ambiguities on OM/CM. In those scenarios, the "Reset Duplicates" button in project
              // settings can resolve this
              journalPartition =
                  journalPartition.getLastUpdated().getTime()
                          < diskPartition.getLastUpdated().getTime()
                      ? diskPartition
                      : journalPartition;
              LOG.debug(
                  "Multiple journal partitions for host detected, taking the most recent partition."
                      + " {}",
                  entries(
                      Map.of(
                          "groupId",
                          String.valueOf(getGroupId()),
                          "hostId",
                          hostId,
                          "partition",
                          String.valueOf(journalPartition.getPartitionName()))));
            } else {
              journalPartition = diskPartition;
            }
          }
        }

        _hostDataPartitions.put(hostId, Collections.unmodifiableSet(dataPartitions));
        _hostIndexPartitions.put(hostId, Collections.unmodifiableSet(indexPartitions));
        _hostJournalPartition.put(
            hostId, journalPartition == null ? null : journalPartition.getPartitionName());
      }

      _allDiskPartitionsLoaded = true;
    }
  }

  private void loadReplicaSetsAndShardedClusters() {
    if (_replicaSets == null && _shardedClusters == null) {
      final Map<ObjectId, HostCluster> shardedClusters = new HashMap<>();
      final Map<ObjectId, HostCluster> replicaSets = new HashMap<>();
      final List<MonitoredHost> monitoredHosts = getMonitoredHosts();
      final boolean areAllHostsIdleFreeTier =
          !monitoredHosts.isEmpty()
              && monitoredHosts.stream().allMatch(this::isUnmonitoredIdleFreeTier);
      // Optimization: don't even query for any sharded cluster HostClusters if all
      // MonitoringHosts for the Group are a idle unmonitored M0 -- the common case
      // across all Atlas projects.
      //
      // This is safe even for callers of getShardedCluster(clusterId), because those
      // callers are all downstream of first identifying any sharded clusters from here.
      final Collection<ClusterType> clusterTypes =
          areAllHostsIdleFreeTier
              ? List.of(ClusterType.REPLICA_SET)
              : List.of(ClusterType.REPLICA_SET, ClusterType.SHARDED_REPLICA_SET);
      final List<HostCluster> hostClusters =
          _hostClusterSvc.findActiveHostClustersByGroupIdAndTypes(
              getGroupId(), clusterTypes.toArray(ClusterType[]::new));

      for (final HostCluster hostCluster : hostClusters) {
        if (hostCluster.getType() == ClusterType.REPLICA_SET) {
          replicaSets.put(hostCluster.getClusterId(), hostCluster);
        } else if (hostCluster.getType() == ClusterType.SHARDED_REPLICA_SET) {
          shardedClusters.put(hostCluster.getClusterId(), hostCluster);
        }
      }
      _shardedClusters = Collections.unmodifiableMap(shardedClusters);
      _replicaSets = Collections.unmodifiableMap(replicaSets);
    }
  }

  public List<DBObject> getUnfinishedBlockstoreJobs(final ObjectId pGroupId, final String pRsId) {
    if (_blockstoreJob == null) {
      final Map<Pair<ObjectId, String>, List<DBObject>> blockstoreJobsByGroupIdAndRsId =
          new HashMap<>();
      for (final DBObject bs : _blockstoreJobDao.getAllUnfinishedJobsByGroupId(pGroupId)) {
        if (blockstoreJobsByGroupIdAndRsId.containsKey(
            Pair.of((ObjectId) bs.get("groupId"), bs.get("rsId").toString()))) {
          blockstoreJobsByGroupIdAndRsId
              .get(Pair.of((ObjectId) bs.get("groupId"), bs.get("rsId").toString()))
              .add(bs);
        } else {
          final List<DBObject> unfinishedJobs = new LinkedList<>();
          unfinishedJobs.add(bs);
          blockstoreJobsByGroupIdAndRsId.put(
              Pair.of((ObjectId) bs.get("groupId"), bs.get("rsId").toString()), unfinishedJobs);
        }
      }
      _blockstoreJob = Collections.unmodifiableMap(blockstoreJobsByGroupIdAndRsId);
    }

    return _blockstoreJob.get(Pair.of(pGroupId, pRsId));
  }

  /**
   * Return true if we don't want to consider the given hostname:port unhealthy due to one of the
   * reasons: - it is marked as disabled by automation config - it is undergoing backup restore
   */
  public boolean ignoreHealth(final String pHostnameAndPort) {
    if (_ignoreHealthCheck == null) {
      _ignoreHealthCheck = new HashMap<>();
    }
    if (_ignoreHealthCheck.containsKey(pHostnameAndPort)) {
      return _ignoreHealthCheck.get(pHostnameAndPort);
    }
    final boolean shouldIgnore = shouldIgnoreHealth(pHostnameAndPort);
    _ignoreHealthCheck.put(pHostnameAndPort, shouldIgnore);
    return shouldIgnore;
  }

  private boolean shouldIgnoreHealth(final String pHostnameAndPort) {
    final AutomationConfig automationConfig = getPublishedAutomationConfig();
    if (automationConfig == null) {
      return false;
    }

    final List<IndexConfig> indexConfigs = automationConfig.getDeployment().getIndexConfigs();
    if (indexConfigs != null && !indexConfigs.isEmpty()) {
      return true;
    }

    final List<Process> processList = automationConfig.getDeployment().getProcesses();
    final Optional<Process> matchingProcessOpt =
        _automationConfigAliasSvc.findManagedProcessByHostAndAlias(
            getGroup(), pHostnameAndPort, processList);
    if (!matchingProcessOpt.isPresent()) {
      return false;
    }
    final Process matchingProcess = matchingProcessOpt.get();
    return matchingProcess.isDisabled() || matchingProcess.getBackupRestoreUrl() != null;
  }

  public NDSGroup getNDSGroup() {
    if (_ndsGroup == null) {
      _ndsGroup = _ndsLookupSvc.getNDSGroup(getGroupId());
    }

    return _ndsGroup.orElse(null);
  }

  public NDSUserCertDao getNdsUserCertDao() {
    return _ndsUserCertDao;
  }

  public List<NDSUserCert> getValidNDSCerts(final ObjectId groupId, final String username) {
    return getNdsUserCertDao()
        .getValidNDSCerts(groupId, username, DriverUtils.SECONDARY_PREFERRED_MINIMUM);
  }

  public MetricDataSnapshot<DiskMeasurement> getDiskMeasurements(
      final String pHostId, final Host.DataSource pHostDataSource) throws MetricsException {
    if (!_diskMeasurements.containsKey(pHostId)) {
      recordOldestRequestMetricTimestamp();
      incrementMetricsSvcCounter("findDiskMeasurements", pHostDataSource);
      final MetricDataSnapshot<DiskMeasurement> snapshot =
          _metricsSvc.findDiskMeasurements(
              getGroup(),
              pHostId,
              getMetricInterval(),
              0L,
              DEFAULT_MEASUREMENT_DURATION_MILLIS,
              _appSettings.getHostMeasurementAlertMode());
      _diskMeasurements.put(pHostId, snapshot);
    }
    return _diskMeasurements.get(pHostId);
  }

  public MetricDataSnapshot<FTSDiskMeasurement> getFTSDiskMeasurements(
      final String pHostId, final Host.DataSource pHostDataSource) throws MetricsException {
    if (!_ftsDiskMeasurements.containsKey(pHostId)) {
      recordOldestRequestMetricTimestamp();
      incrementMetricsSvcCounter("findFTSDiskMeasurements", pHostDataSource);
      final MetricDataSnapshot<FTSDiskMeasurement> snapshot =
          _metricsSvc.findFTSDiskMeasurements(
              getGroup(),
              pHostId,
              getMetricInterval(),
              0L,
              DEFAULT_MEASUREMENT_DURATION_MILLIS,
              _appSettings.getHostMeasurementAlertMode());
      _ftsDiskMeasurements.put(pHostId, snapshot);
    }
    return _ftsDiskMeasurements.get(pHostId);
  }

  public MetricDataSnapshot<HostMeasurement> getHostMeasurements(
      final String pHostId, final Host.DataSource pHostDataSource) throws MetricsException {
    ReadPreference readPreference = ReadPreference.primaryPreferred();
    Optional<Duration> readTimeOut = Optional.empty();
    if (pHostDataSource == DataSource.PROXY) {
      final String readPrefStr =
          _appSettings.getStrProp(
              "mms.monitoring.alert.freeTierHostMeasurement.readPreference", "primaryPreferred");
      try {
        readPreference = ReadPreference.valueOf(readPrefStr);
        // don't allow reads from stale secondaries
        if (readPreference.isSecondaryOk()) {
          readPreference = readPreference.withMaxStalenessMS(90L, TimeUnit.SECONDS);
        }
      } catch (IllegalArgumentException e) {
        readPreference = ReadPreference.primaryPreferred();
      }
      final long readTimeOutMillis =
          _appSettings.getLong(
              "mms.monitoring.alert.freeTierHostMeasurement.readTimeoutMillis", 500);
      readTimeOut = Optional.of(Duration.ofMillis(readTimeOutMillis));
    }
    if (!_hostMeasurements.containsKey(pHostId)) {
      recordOldestRequestMetricTimestamp();
      incrementMetricsSvcCounter("findHostMeasurements", pHostDataSource);
      final MetricDataSnapshot<HostMeasurement> snapshot =
          _metricsSvc.findHostMeasurements(
              getGroup(),
              pHostId,
              getMetricInterval(),
              0L,
              _appSettings.getAlertsHostMeasurementLookbackDuration(
                  DEFAULT_MEASUREMENT_DURATION_MILLIS),
              _appSettings.getHostMeasurementAlertMode(),
              readPreference,
              readTimeOut);
      _hostMeasurements.put(pHostId, snapshot);
    }
    return _hostMeasurements.get(pHostId);
  }

  public MetricDataSnapshot<SystemMemoryMeasurement> getSystemMemoryMeasurements(
      final String pHostname, final Host.DataSource pHostDataSource) throws MetricsException {
    if (!_systemMemoryMeasurements.containsKey(pHostname)) {
      recordOldestRequestMetricTimestamp();
      incrementMetricsSvcCounter("findSystemMemoryMeasurements", pHostDataSource);
      final MetricDataSnapshot<SystemMemoryMeasurement> snapshot =
          _metricsSvc.findSystemMemoryMeasurements(
              getGroup(),
              pHostname,
              getMetricInterval(),
              0L,
              DEFAULT_MEASUREMENT_DURATION_MILLIS,
              _appSettings.getHostMeasurementAlertMode());
      _systemMemoryMeasurements.put(pHostname, snapshot);
    }
    return _systemMemoryMeasurements.get(pHostname);
  }

  /**
   * Given a query template which allows for the insertion of a groupId, adds the relevant groupId
   * and sends the query to MaaS over HTTP.
   */
  public PromQLResponseView<Data> getMeasurementsFromMaaS(String queryTemplate, UUID usecaseId)
      throws IOException, UnableToRetrieveCloudJwtException {
    final String groupId = getGroupId().toHexString();
    final String query = String.format(queryTemplate, groupId);
    final InstantQueryRequest instantQueryRequest =
        new InstantQueryRequest.Builder(query, usecaseId).build();
    final PromQLResponseView<Data> responseView =
        _customerMetricsQuerySvc.getMetricsViaInstantQuery(instantQueryRequest);
    return responseView;
  }

  public MetricDataSnapshot<FTSProcessMemoryMeasurement> getFTSProcessMemoryMeasurements(
      final String pHostId, final Host.DataSource pHostDataSource) throws MetricsException {
    if (!_ftsProcessMemoryMeasurements.containsKey(pHostId)) {
      recordOldestRequestMetricTimestamp();
      incrementMetricsSvcCounter("findFTSProcessMemoryMeasurements", pHostDataSource);
      final MetricDataSnapshot<FTSProcessMemoryMeasurement> snapshot =
          _metricsSvc.findFTSProcessMemoryMeasurements(
              getGroup(),
              pHostId,
              getMetricInterval(),
              0L,
              DEFAULT_MEASUREMENT_DURATION_MILLIS,
              _appSettings.getHostMeasurementAlertMode());
      _ftsProcessMemoryMeasurements.put(pHostId, snapshot);
    }
    return _ftsProcessMemoryMeasurements.get(pHostId);
  }

  public MetricDataSnapshot<FTSProcessCpuMeasurement> getFTSProcessCpuMeasurements(
      final String pHostId, final Host.DataSource pHostDataSource) throws MetricsException {
    if (!_ftsProcessCpuMeasurements.containsKey(pHostId)) {
      recordOldestRequestMetricTimestamp();
      incrementMetricsSvcCounter("findFTSProcessCpuMeasurements", pHostDataSource);
      final MetricDataSnapshot<FTSProcessCpuMeasurement> snapshot =
          _metricsSvc.findFTSProcessCpuMeasurements(
              getGroup(),
              pHostId,
              getMetricInterval(),
              0L,
              DEFAULT_MEASUREMENT_DURATION_MILLIS,
              _appSettings.getHostMeasurementAlertMode());
      _ftsProcessCpuMeasurements.put(pHostId, snapshot);
    }
    return _ftsProcessCpuMeasurements.get(pHostId);
  }

  public MetricDataSnapshot<? extends Systemable> getProcessMeasurements(
      final String pHostId, final Host.DataSource pHostDataSource) throws MetricsException {
    if (!_processMeasurements.containsKey(pHostId)) {
      recordOldestRequestMetricTimestamp();
      incrementMetricsSvcCounter("findProcessMeasurements", pHostDataSource);
      final MetricDataSnapshot<? extends Systemable> snapshot =
          _metricsSvc.findProcessMeasurements(
              getGroup(),
              pHostId,
              getMetricInterval(),
              0L,
              DEFAULT_MEASUREMENT_DURATION_MILLIS,
              _appSettings.getHostMeasurementAlertMode());
      _processMeasurements.put(pHostId, snapshot);
    }
    return _processMeasurements.get(pHostId);
  }

  public MetricDataSnapshot<ServerlessClusterMeasurement> getServerlessClusterMeasurements(
      final ObjectId pClusterUniqueId) throws MetricsException {
    if (!_serverlessClusterMeasurements.containsKey(pClusterUniqueId)) {
      final String tenantId = DriverUtils.reverseObjectId(pClusterUniqueId);
      final MetricDataSnapshot<ServerlessClusterMeasurement> snapshot =
          _serverlessMetricsSvc.findServerlessClusterMeasurements(
              getGroup(), tenantId, getMetricInterval(), 0L, DEFAULT_MEASUREMENT_DURATION_MILLIS);
      _serverlessClusterMeasurements.put(pClusterUniqueId, snapshot);
    }
    return _serverlessClusterMeasurements.get(pClusterUniqueId);
  }

  public MetricDataSnapshot<FTSIndexStatsMeasurement> getFTSIndexStatsMeasurements(
      final String pHostId, final Host.DataSource pHostDataSource) throws MetricsException {
    if (!_ftsIndexStatsMeasurements.containsKey(pHostId)) {
      recordOldestRequestMetricTimestamp();
      incrementMetricsSvcCounter("findFTSIndexStatsMeasurements", pHostDataSource);
      final MetricDataSnapshot<FTSIndexStatsMeasurement> snapshot =
          _metricsSvc.findFTSIndexStatsMeasurements(
              getGroup(),
              pHostId,
              getMetricInterval(),
              0L,
              DEFAULT_MEASUREMENT_DURATION_MILLIS,
              getMetricInterval(),
              _appSettings.getHostMeasurementAlertMode());
      _ftsIndexStatsMeasurements.put(pHostId, snapshot);
    }
    return _ftsIndexStatsMeasurements.get(pHostId);
  }

  public MetricDataSnapshot<FTSServerStatusMeasurement> getFTSServerStatusMeasurements(
      final String pHostId, final Host.DataSource pHostDataSource) throws MetricsException {
    if (!_ftsServerStatusMeasurements.containsKey(pHostId)) {
      recordOldestRequestMetricTimestamp();
      incrementMetricsSvcCounter("findFTSServerStatusMeasurements", pHostDataSource);
      final MetricDataSnapshot<FTSServerStatusMeasurement> snapshot =
          _metricsSvc.findFTSServerStatusMeasurements(
              getGroup(),
              pHostId,
              getMetricInterval(),
              0L,
              DEFAULT_MEASUREMENT_DURATION_MILLIS,
              _appSettings.getHostMeasurementAlertMode());
      _ftsServerStatusMeasurements.put(pHostId, snapshot);
    }
    return _ftsServerStatusMeasurements.get(pHostId);
  }

  public MetricDataSnapshot<RealmMeasurement> getRealmMeasurementsForApp(final ObjectId pAppId)
      throws MetricsException {
    if (!_realmMeasurements.containsKey(pAppId)) {
      final MetricDataSnapshot<RealmMeasurement> snapshot =
          _metricsSvc.findRealmMeasurements(
              getGroup(),
              pAppId,
              getRealmMetricInterval(),
              0L,
              _appSettings.getMonitoringRealmRecentMeasurementsInterval());
      _realmMeasurements.put(pAppId, snapshot);
    }
    return _realmMeasurements.get(pAppId);
  }

  public long getRealmMetricInterval() {
    return getGroup().getGroupStorageConfig().getHighestResolutionRealmRetention() == null
        ? DEFAULT_HIGHEST_RESOLUTION_RETENTION
        : getGroup()
            .getGroupStorageConfig()
            .getHighestResolutionRealmRetention()
            .getSamplePeriodMillis();
  }

  public long getMetricInterval() {
    return getGroup().getGroupStorageConfig().getHighestResolutionRetention() == null
        ? DEFAULT_HIGHEST_RESOLUTION_RETENTION
        : getGroup()
            .getGroupStorageConfig()
            .getHighestResolutionRetention()
            .getSamplePeriodMillis();
  }

  public long getMeasurementDurationMillis() {
    return GroupAlertProcessingContext.DEFAULT_MEASUREMENT_DURATION_MILLIS;
  }

  public Optional<Integer> getMaxConnectionsForHost(final Host pHost) {
    if (pHost.getMaxConns() != null) {
      return Optional.ofNullable(pHost.getMaxConns());
    }

    if (pHost.isTenant()) {
      final Optional<ClusterDescription> clusterDescriptionOpt =
          getClusterDescriptionByInstanceHostname(pHost.getName());

      if (clusterDescriptionOpt.isPresent()) {
        final ClusterDescription clusterDescription = clusterDescriptionOpt.get();

        final CloudProvider tenantType = clusterDescription.getTenantType();
        switch (tenantType) {
          case FREE -> {
            return Optional.of(
                ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
                    .getConnectionLimit());
          }
          case SERVERLESS -> {
            return Optional.of(
                ((ServerlessTenantProviderOptions)
                        clusterDescription.getServerlessTenantProviderOptions())
                    .getConnectionLimit());
          }
          case FLEX -> {
            return Optional.of(
                ((FlexTenantProviderOptions) clusterDescription.getFlexTenantProviderOptions())
                    .getCollectionLimit());
          }
          default -> {
            throw new IllegalStateException("Provider is not a tenant provider.");
          }
        }
      }

      LOG.warn(
          "Unable to get the ClusterDescription for host {} in group {}.",
          pHost.getName(),
          pHost.getGroupId());

      return Optional.empty();
    }

    return Optional.ofNullable(pHost.getMaxConnsWithVersionDefault());
  }

  public boolean skipHardwareMetricsAlertEvaluation(final MonitoredHost pHost) {
    if (pHost.getDataSource() != null && pHost.getDataSource() == Host.DataSource.PROXY) {
      return _appSettings.getBoolProp(
          "mms.monitoring.alert.freeTierHostMeasurement.skipHardwareMetricsAlertEvaluation", false);
    }
    return false;
  }

  /**
   * @return true iff: the free tier activity monitoring service is enabled, and the Host's source
   *     type is PROXY, and we can find the Host's ClusterDescription, and the free tier activity is
   *     not active.
   */
  public boolean isUnmonitoredIdleFreeTier(final MonitoredHost pHost) {
    if (getNdsM0ClusterActivitySvc().isPauseFreeTierMonitoringEnabled()) {
      if (pHost.getDataSource() == Host.DataSource.PROXY) {

        final Optional<ClusterDescription> clusterDescription =
            getClusterDescriptionByInstanceHostname(pHost.getHostname());
        if (clusterDescription.isPresent()) {
          return !getNdsM0ClusterActivitySvc().isActiveCluster(clusterDescription.get());
        }

        LOG.error(
            "Unable to get the ClusterDescription for host {} in group {}",
            pHost.getHostname(),
            pHost.getGroupId());
      }
    }

    return false;
  }

  public AppSettings getAppSettings() {
    return _appSettings;
  }

  public Map<ObjectId, AlertableServerlessInstance> getAlertableServerlessInstances() {
    if (_alertableServerlessInstances.isEmpty()) {
      final List<ClusterDescription> clusterDescriptions = getActiveClusterDescriptions();

      for (ClusterDescription cluster : clusterDescriptions) {
        if (cluster.isServerlessTenantCluster()) {
          final Integer maxConnections =
              ((ServerlessTenantProviderOptions) cluster.getServerlessTenantProviderOptions())
                  .getConnectionLimit();
          _alertableServerlessInstances.put(
              cluster.getUniqueId(),
              new AlertableServerlessInstance(
                  cluster.getName(), cluster.getGroupId(), cluster.getUniqueId(), maxConnections));
        }
      }
    }
    return _alertableServerlessInstances;
  }

  @Override
  public Map<ObjectId, AlertableFlexCluster> getAlertableFlexClusters() {
    if (_alertableFlexClusters.isEmpty()) {
      final List<ClusterDescription> clusterDescriptions = getActiveClusterDescriptions();

      for (ClusterDescription clusterDescription : clusterDescriptions) {
        if (clusterDescription.isFlexTenantCluster()) {
          final Integer maxConnections =
              ((FlexTenantProviderOptions) clusterDescription.getFlexTenantProviderOptions())
                  .getConnectionLimit();

          _alertableFlexClusters.put(
              clusterDescription.getUniqueId(),
              new AlertableFlexCluster(
                  clusterDescription.getName(),
                  clusterDescription.getGroupId(),
                  clusterDescription.getUniqueId(),
                  maxConnections));
        }
      }
    }

    return _alertableFlexClusters;
  }

  public List<RealmMeasurement> findRealmMeasurements(
      final ObjectId pApplicationId,
      final Retention pRetention,
      final Instant pStart,
      final Instant pEnd) {
    return _realmMeasurementSvc.findRealmMeasurements(
        getGroup(), pApplicationId, pRetention, pStart, pEnd, ReadPreference.primaryPreferred());
  }

  public Optional<ClusterDescription> getClusterDescriptionFromHostCluster(
      final HostCluster pHostCluster) {
    final ObjectId clusterId = pHostCluster.getClusterId();

    if (!_hostClusterClusterIdToClusterUniqueId.containsKey(
        clusterId)) { // map hostCluster to clusterUniqueId so we can cache in _clusterDescriptions
      // and filter by clusterUniqueId
      final ClusterDescription clusterDescription =
          _clusterLookupSvc.getClusterDescriptionFromFirstHostClusterHost(
              getGroupId(), pHostCluster);
      final ObjectId clusterUniqueId = clusterDescription.getUniqueId();
      _hostClusterClusterIdToClusterUniqueId.put(clusterId, clusterUniqueId);
      final Optional<ClusterDescription> cachedClusterDescription =
          getCachedClusterDescription(clusterDescription.getUniqueId());
      if (cachedClusterDescription.isPresent()) {
        return cachedClusterDescription;
      }
      _cachedLookedUpClusterDescriptions.add(clusterDescription);
    }
    final ObjectId foundClusterUniqueId = _hostClusterClusterIdToClusterUniqueId.get(clusterId);
    return getClusterDescriptionByUniqueId(pHostCluster.getGroupId(), foundClusterUniqueId);
  }

  public Set<String> findServerlessTenantIdsFromMTM(
      final ClusterDescription pMtmClusterDescription, final int pLimit) {
    final ObjectId mtmUniqueId = pMtmClusterDescription.getUniqueId();
    if (!_mtmClusterUniqueIdToSomeServerlessTenantIds.containsKey(mtmUniqueId)) {
      final Set<String> serverlessTenantIds =
          _ndsLookupSvc
              .findTenantIdsFromServerlessMTM(
                  pMtmClusterDescription.getGroupId(), pMtmClusterDescription.getName())
              .stream()
              .filter(
                  id ->
                      !id.equals(
                          mtmUniqueId
                              .toString())) // one of the entries will be the mtm unique id so we'll
              // want to filter that out
              .limit(
                  pLimit) // only need to sample a few of the tenants and there could potentially be
              // a lot
              .collect(Collectors.toSet());
      _mtmClusterUniqueIdToSomeServerlessTenantIds.put(mtmUniqueId, serverlessTenantIds);
    }
    return _mtmClusterUniqueIdToSomeServerlessTenantIds.get(mtmUniqueId);
  }

  public Set<String> findFlexTenantIdsFromMTM(
      final ClusterDescription pMtmClusterDescription, final int pLimit) {
    final ObjectId mtmUniqueId = pMtmClusterDescription.getUniqueId();
    if (!_mtmClusterUniqueIdToSomeFlexTenantIds.containsKey(mtmUniqueId)) {
      final Set<String> flexTenantIds =
          _ndsLookupSvc
              .findTenantIdsFromFlexMTM(
                  pMtmClusterDescription.getGroupId(), pMtmClusterDescription.getName())
              .stream()
              .filter(
                  id ->
                      !id.equals(
                          mtmUniqueId
                              .toString())) // one of the entries will be the mtm unique id so we'll
              // want to filter that out
              .limit(
                  pLimit) // only need to sample a few of the tenants and there could potentially be
              // a lot
              .collect(Collectors.toSet());
      _mtmClusterUniqueIdToSomeFlexTenantIds.put(mtmUniqueId, flexTenantIds);
    }
    return _mtmClusterUniqueIdToSomeFlexTenantIds.get(mtmUniqueId);
  }

  @Override
  public boolean doesClusterHaveActiveMongomirrorLiveImport(
      final ClusterDescription pClusterDescription) {
    final Optional<LiveImport> activeLiveImportOpt =
        _liveImportSvc.getLiveImportForCluster(
            pClusterDescription.getGroupId(), pClusterDescription.getName());
    return activeLiveImportOpt.isPresent()
        && LiveImport.MigrationToolType.MONGOMIRROR
            == activeLiveImportOpt.get().getMigrationToolType();
  }

  public List<ClusterDescription> getClusterDescriptionsSplitByFixedVersionType() {
    return getActiveClusterDescriptions().stream()
        .flatMap(clusterDescription -> clusterDescription.splitByFixedVersionType().stream())
        .toList();
  }

  public List<FixedAgentVersion> getFixedAgentVersions() {
    return Optional.ofNullable(getNDSGroup())
        .map(NDSGroup::getFixedAgentVersions)
        .orElse(List.of());
  }

  // Optimization to look into the automation config to figure out if alerts should be set.
  // The MongotConfig should be added before mongots are brought up and removed when mongots
  // are brought down, so the alerting behavior should be unaffected.
  @Override
  public boolean isMongotConfigured() {
    AutomationConfig automationConfig = getPublishedAutomationConfig();
    return automationConfig != null
        && automationConfig.getDeployment() != null
        && automationConfig.getDeployment().getMongots() != null
        && !automationConfig.getDeployment().getMongots().isEmpty();
  }

  @Override
  public boolean isFlappingDetectionEnabled() {
    return _flappingDetectionEnabled.computeIfAbsent(
        getGroupId(),
        k -> isFeatureFlagEnabled(ALERT_STATE_FLAPPING_DETECTION, _appSettings, null, getGroup()));
  }

  @Override
  public Long getBatchSequenceNumber() {
    return _groupAlertProcessingBatchContext.getBatchSequenceNumber();
  }

  /**
   * Used to record estimated oldest timestamp requested from Metrics cluster. Call to this method
   * is placed before actual call to the metrics code, this way recorded timestamp is guaranteed to
   * be older than the beginning of query interval
   */
  private void recordOldestRequestMetricTimestamp() {
    Instant until = Instant.now();
    Duration duration = Duration.ofMillis(DEFAULT_MEASUREMENT_DURATION_MILLIS);
    Instant oldestRequestedEndpoint = until.minus(duration);
    FROM_TIMESTAMP_FOR_METRICS_QUERY.set(oldestRequestedEndpoint.toEpochMilli());
  }

  @Override
  public EncryptionAtRestProvider getClusterEncryptionAtRestProvider(final String pHostname) {
    final Optional<ClusterDescription> clusterDescriptionOpt =
        getClusterDescriptionByInstanceHostname(pHostname);

    return clusterDescriptionOpt
        .map(ClusterDescription::getEncryptionAtRestProvider)
        .orElse(EncryptionAtRestProvider.NONE);
  }
}
