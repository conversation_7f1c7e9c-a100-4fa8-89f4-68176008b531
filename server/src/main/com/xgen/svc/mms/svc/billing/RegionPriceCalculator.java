package com.xgen.svc.mms.svc.billing;

import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.svc.billing.calculator.PriceCalculator;

public class RegionPriceCalculator extends PriceCalculator {

  private final RegionName region;

  public RegionPriceCalculator(RegionName region) {
    this.region = region;
  }

  @Override
  public double calcUnitPriceDollars(SKU sku, SkuPricing skuPricing) {
    return skuPricing.getRegionUnitPriceDollars(region);
  }

  @Override
  public double calcTieredUnitPriceDollars(SKU sku, int tier, SkuPricing skuPricing) {
    return skuPricing.getTieredRegionUnitPriceDollars(region, tier);
  }

  @Override
  public void addLineItemMetadata(LineItem.Builder builder) {
    builder.regionName(region);
  }
}
