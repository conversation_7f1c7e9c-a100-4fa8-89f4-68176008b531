version: 1.0.0
aliases:
  - //.github/CODEOWNERS-aliases.yml
filters:
  - "/NpsSurveySenderSvc*":
    approvers:
      - 10gen/code-review-team-growth-platforms
  - "/OWNERS.yaml":
    approvers:
      - 10gen/code-review-team-growth-platforms
      - 10gen/code-review-team-online-archive
  - "/DbProfile*Svc.java":
    approvers:
      - 10gen/code-review-team-intel-ii
  - "/performanceadvisor/*":
    approvers:
      - 10gen/code-review-team-intel-ii
  - "SlowQuery*.java":
    approvers:
      - 10gen/code-review-team-intel-ii
  - "SetProfilingLevelSvc.java":
    approvers:
      - 10gen/code-review-team-intel-ii
  - "ServerlessAutoIndexingSvc.java":
    approvers:
      - 10gen/code-review-team-intel-ii
  - "AutoIndexingSvc.java":
    approvers:
      - 10gen/code-review-team-intel-ii
  - "AutoApplyIndexes*.java":
    approvers:
      - 10gen/code-review-team-intel-ii
  - "OrgMigrationSvc.java":
    approvers:
      - ace-iam-authz
  - "/GroupSvcImpl.java":
    approvers:
      - ace-iam-authz
  - "/GroupSvcProvider.java":
    approvers:
      - ace-iam-authz
  - "/GroupSvcCloud.java":
    approvers:
      - ace-iam-authz
  - "/GroupClosureSvc.java":
    approvers:
      - ace-iam-authz
  - "/GroupSvcProxy.java":
    approvers:
      - ace-iam-authz
  - "/GroupSvcSaml.java":
    approvers:
      - ace-iam-authz
  - "/NDSOrgSvc.java":
    approvers:
      - ace-iam-authz
  - "/TeamSvc.java":
    approvers:
      - ace-iam-authz
  - "/AccountClosureSvc.java":
    approvers:
      - ace-iam-authz
  - "/AdminOrgSvc.java":
    approvers:
      - 10gen/code-review-team-payments
  - "/IngestionPipeline*Svc.java":
    approvers:
      - 10gen/code-review-team-online-archive
  - "/JettyLoginSvc.java":
    approvers:
      - ace-iam-workload-identity
