/* (C) Copyright 2012, MongoDB, Inc. */

package com.xgen.svc.core.svc.cron;

import static com.xgen.svc.core.svc.cron.CronJobLifecycleMetrics.decrementNumActiveThreadsMetric;
import static com.xgen.svc.core.svc.cron.CronJobLifecycleMetrics.incrementNumActiveThreadsMetric;
import static com.xgen.svc.core.svc.cron.CronJobLifecycleMetrics.recordJobStartedExecutionMetric;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.cron._private.dao.CronStateDao;
import com.xgen.cloud.cron._public.svc.CronJobAcquisitionStrategy;
import com.xgen.cloud.cron._public.svc.CronJobCtx;
import com.xgen.cloud.cron._public.svc.DistributedCronSvc;
import com.xgen.cloud.group._public.svc.GroupSvc;
import io.prometheus.client.Summary;
import java.util.List;
import java.util.stream.Collectors;
import org.quartz.InterruptableJob;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** The cron job base class. Your job must extend this object. */
public abstract class CronJob implements InterruptableJob {

  private static final Logger LOG = LoggerFactory.getLogger(CronJob.class);
  private static final Summary CRON_JOB_DURATION_SUMMARY =
      PromMetricsSvc.registerSummary(
          "mms_cron_job_duration_seconds",
          "Time taken to complete one CronJob execution",
          List.of(
              PromMetricsSvc.getSummaryQuantile(0.99, 0.001),
              PromMetricsSvc.getSummaryQuantile(0.95, 0.01),
              PromMetricsSvc.getSummaryQuantile(0.50, 0.05)),
          "name");

  /** Execute the cron job. Override this method to implement a cron job. */
  public abstract void execute(final CronJobCtx pCtx) throws Exception;

  /** This method calls the execute method above. Abstracting out quartz. */
  @Override
  public final void execute(final JobExecutionContext pCtx) throws JobExecutionException {
    try {
      final String jobName = pCtx.getJobDetail().getKey().getName();
      recordJobStartedExecutionMetric(jobName);
      incrementNumActiveThreadsMetric(jobName);

      final DistributedCronSvc svc = AppConfig.getInstance(DistributedCronSvc.class);
      final boolean force = svc.tryForce(pCtx.getJobDetail().getKey());
      execute(new CronJobCtx(pCtx, force));
    } catch (final Throwable t) {
      throw new JobExecutionException(t);
    } finally {
      decrementNumActiveThreadsMetric(pCtx.getJobDetail().getKey().getName());
    }
  }

  public static class ShouldAcquireCronLockResult {
    private final String _strategy;
    private final boolean _outcome;

    public ShouldAcquireCronLockResult(final String pStrategy, final boolean pOutcome) {
      _strategy = pStrategy;
      _outcome = pOutcome;
    }

    public String getStrategyName() {
      return _strategy;
    }

    public boolean getOutcome() {
      return _outcome;
    }
  }

  @VisibleForTesting
  protected final ShouldAcquireCronLockResult shouldProceed(
      final List<CronJobAcquisitionStrategy> pStrategies,
      final String pCronName,
      final CronJobCtx pContext) {

    for (final CronJobAcquisitionStrategy strategy : pStrategies) {
      if (!strategy.shouldAcquire(pCronName, pContext)) {
        return new ShouldAcquireCronLockResult(strategy.getName(), false);
      }
    }
    final String evaluatedStrategies =
        pStrategies.stream()
            .map(CronJobAcquisitionStrategy::getName)
            .collect(Collectors.joining(AppSettings.COMMA_DELIMITER));
    return new ShouldAcquireCronLockResult(evaluatedStrategies, true);
  }

  /** Returns true if the job can execute (looks at the global lock). */
  protected boolean canProceed(final String pCronName, final CronJobCtx pContext) {
    final CronStateDao cronStateDao = AppConfig.getInstance(CronStateDao.class);
    final AppSettings appSettings = AppConfig.getInstance(AppSettings.class);
    final GroupSvc groupSvc = AppConfig.getInstance(GroupSvc.class);

    if (!appSettings.isMMSCronEnabled()) {
      LOG.debug("Not proceeding with CronJob {}. Cron subsystem disabled.", pCronName);
      return false;
    }

    if (cronStateDao.getManuallyDisabled(pCronName)) {
      LOG.debug("Not proceeding with CronJob {}. Specific job is disabled.", pCronName);
      return false;
    }

    // Until fully configured the crons shouldn't run as there can be
    // configuration that they require that is not yet set.
    if (groupSvc.count(true) == 0) {
      LOG.debug("Not proceeding with CronJob {}. No Projects.", pCronName);
      return false;
    }

    // apply acquisition strategies here to determine if we should attempt to acquire this CronJob
    // lock or exit
    final DistributedCronSvc svc = AppConfig.getInstance(DistributedCronSvc.class);
    final ShouldAcquireCronLockResult shouldAcquireLock =
        shouldProceed(svc.getCronJobAcquisitionStrategies(), pCronName, pContext);
    if (!shouldAcquireLock.getOutcome()) {
      LOG.debug(
          "Not proceeding to acquire CronJob lock: {}. Failed acquisition strategy: {}",
          pCronName,
          shouldAcquireLock.getStrategyName());
      return false;
    }
    LOG.debug(
        "Proceeding to acquire CronJob lock: {}. Passed acquisition strategies: {}",
        pCronName,
        shouldAcquireLock.getStrategyName());
    return svc.isOwnerOrAcquireLock(pCronName);
  }

  protected void emitDurationForCronJob(final String pCronJobName, final long pDurationInMs) {
    PromMetricsSvc.recordTimer(CRON_JOB_DURATION_SUMMARY, pDurationInMs / 1000.0, pCronJobName);
  }
}
