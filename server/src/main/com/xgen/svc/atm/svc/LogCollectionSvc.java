package com.xgen.svc.atm.svc;

import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.ATLAS_PROXY;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.AUTOMATION_AGENT;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.BACKUP_AGENT;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.CLUSTER_DATABASE_LOG_TYPES;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.CPS_MODULE;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.CPS_PIT;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.FTDC;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.MONGODB;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.MONGOT;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.MONGOTUNE;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.MONGOTUNE_FTDC;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.MONGOT_FTDC;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.MONITORING_AGENT;
import static com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType.UNRESTRICTED_LOG_COLLECTION_TYPES;
import static com.xgen.cloud.common.mongo._public.mongo.VersionUtils.isGreaterOrEqualTo;
import static com.xgen.cloud.common.util._public.util.DriverUtils.reverseObjectId;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.compress.archivers.tar.TarArchiveOutputStream.LONGFILE_POSIX;
import static org.apache.commons.io.IOUtils.copy;

import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xgen.cloud.access.activity._public.audit.AccessAuditEvent;
import com.xgen.cloud.access.activity._public.event.AccessEvent.Type;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.alert.InformationalAlertSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.atm.activity._public.audit.LogCollectionDownloadAudit;
import com.xgen.cloud.atm.activity._public.audit.LogCollectionRequestAudit;
import com.xgen.cloud.atm.agentjobs._public.svc.AgentJobSvc;
import com.xgen.cloud.atm.core._public.model.logcollection.LogCollectionType;
import com.xgen.cloud.atm.core._public.model.logcollection.LogResourceType;
import com.xgen.cloud.atm.core._public.svc.AutomationAgentSupportSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob;
import com.xgen.cloud.common.jobqueue._public.svc.AgentJobsProcessorSvc;
import com.xgen.cloud.common.model._public.access.EmployeeAccessGrantType;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.common.util._public.util.AgentVersion;
import com.xgen.cloud.deployment._public.model.AgentConfig;
import com.xgen.cloud.deployment._public.model.AtlasProxyConfig;
import com.xgen.cloud.deployment._public.model.AutomationAgentConfig;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.MaintainedMongotuneConfig;
import com.xgen.cloud.deployment._public.model.MongotConfig;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessType;
import com.xgen.cloud.deployment._public.model.ReplicaSet;
import com.xgen.cloud.deployment._public.model.Shard;
import com.xgen.cloud.deployment._public.model.ShardedCluster;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.accesstransparency._public.model.AccessTransparencyEventInformation;
import com.xgen.cloud.nds.accesstransparency._public.model.HiddenEmptyAccessTransparencyEventInformation;
import com.xgen.cloud.nds.accesstransparency._public.svc.AccessTransparencyEventSvc;
import com.xgen.cloud.nds.admin._public.svc.DataPlaneAccessRequestSvc;
import com.xgen.cloud.nds.admin._public.svc.NDSSupportTicketAuditorSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSAtlasProxyDefaults;
import com.xgen.cloud.nds.project._public.model.NDSFTSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSMongotuneDefaults;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchInstance;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchDeploymentSvc;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchDeploymentTargets;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchInstanceSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.mdc.MDCUtil;
import com.xgen.svc.atm.dao.AgentJobSummaryDao;
import com.xgen.svc.atm.dao.LogCollectionDao;
import com.xgen.svc.atm.dao.LogCollectionStorageDao;
import com.xgen.svc.atm.dao.LogCollectionStorageDaoFactory;
import com.xgen.svc.atm.jobs.GetMongoLogByDiskspaceJob;
import com.xgen.svc.atm.model.logcollection.AgentJobSummary;
import com.xgen.svc.atm.model.logcollection.LogCollectionErrorCode;
import com.xgen.svc.atm.model.logcollection.LogCollectionEstimate;
import com.xgen.svc.atm.model.logcollection.LogCollectionFileMetadata;
import com.xgen.svc.atm.model.logcollection.LogCollectionJob;
import com.xgen.svc.atm.model.logcollection.LogCollectionRequest;
import com.xgen.svc.atm.model.logcollection.LogCollectionStatus;
import com.xgen.svc.atm.model.logcollection.LogCollectionStorageBackend;
import com.xgen.svc.atm.util.LogRedactionUtil;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.nds.svc.NDSEmployeeAccessRestrictedSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.ServletOutputStream;
import jakarta.ws.rs.core.StreamingOutput;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.compress.archivers.tar.TarConstants;
import org.apache.commons.compress.utils.CountingOutputStream;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.server.HttpOutput;
import org.glassfish.jersey.media.multipart.ContentDisposition;
import org.slf4j.MDC;

/**
 * Service class for dealing with log collection requests. Log collection request creates jobs for
 * automation agents to send the log files back to ops manager where they will be stored in GridFS.
 * The user can request the download of all the files as some archive (tarball for now) later.
 */
@Singleton
public class LogCollectionSvc {
  private static final org.slf4j.Logger LOG =
      org.slf4j.LoggerFactory.getLogger(LogCollectionSvc.class);

  public static final int MAXIMUM_EXTENSION_DATE_MONTHS = 6;

  public static final int LOG_JOB_SAVE_TRIALS_NUMBER = 20;
  public static final int LOG_COLLECTION_GC_INTERVAL_HOURS = 4;

  public static final String DEFAULT_EXTENTION_DAYS_PROP_NAME =
      "mms.logCollectionJob.defaultExtensionDays";

  static AgentVersion LOG_COLLECTION_MINIMUM_AGENT_VERSION =
      new AgentVersion("4.5.4.2347-1", AgentType.AUTOMATION);

  private final LogCollectionDao _logCollectionDao;

  private final LogCollectionStorageDaoFactory _logCollectionStorageDaoFactory;

  private final AgentJobSummaryDao _agentJobSummaryDao;

  private final AgentJobSvc _agentJobSvc;

  private final AgentJobsProcessorSvc _agentJobsProcessorSvc;

  private final AutomationConfigPublishingSvc _automationConfigSvc;

  private final AutomationAgentSupportSvc _automationAgentSupportSvc;

  private final AuditSvc _auditSvc;

  private final AppSettings _appSettings;

  private final NDSClusterSvc _ndsClusterSvc;

  private final NDSEmployeeAccessRestrictedSvc _ndsEmployeeAccessSvc;

  private final SearchDeploymentSvc _searchDeploymentSvc;
  private final SearchInstanceSvc _searchInstanceSvc;
  private final NDSSupportTicketAuditorSvc _ndsSupportTicketAuditorSvc;
  private final DataPlaneAccessRequestSvc _dataPlaneAccessRequestSvc;
  private final InformationalAlertSvc _informationalAlertSvc;
  private final AccessTransparencyEventSvc _accessTransparencyEventSvc;

  @Inject
  public LogCollectionSvc(
      final LogCollectionDao pLogCollectionDao,
      final LogCollectionStorageDaoFactory pLogCollectionStorageDaoFactory,
      final AgentJobSummaryDao pAgentJobSummaryDao,
      final AgentJobSvc pAgentJobSvc,
      final AgentJobsProcessorSvc pAgentJobsProcessorSvc,
      final AutomationConfigPublishingSvc pAutomationConfigSvc,
      final AutomationAgentSupportSvc pAutomationAgentSupportSvc,
      final AuditSvc pAuditSvc,
      final AppSettings pAppSettings,
      final NDSClusterSvc pNDSClusterSvc,
      final NDSEmployeeAccessRestrictedSvc pNDSEmployeeAccessSvc,
      final SearchDeploymentSvc pSearchDeploymentSvc,
      final SearchInstanceSvc pSearchInstanceSvc,
      final NDSSupportTicketAuditorSvc pNDSSupportTicketAuditorSvc,
      final DataPlaneAccessRequestSvc pDataPlaneAccessRequestSvc,
      final InformationalAlertSvc pInformationalAlertSvc,
      final AccessTransparencyEventSvc pAccessTransparencyEventSvc) {
    _logCollectionDao = pLogCollectionDao;
    _logCollectionStorageDaoFactory = pLogCollectionStorageDaoFactory;
    _agentJobSummaryDao = pAgentJobSummaryDao;
    _agentJobSvc = pAgentJobSvc;
    _agentJobsProcessorSvc = pAgentJobsProcessorSvc;
    _automationConfigSvc = pAutomationConfigSvc;
    _automationAgentSupportSvc = pAutomationAgentSupportSvc;
    _auditSvc = pAuditSvc;
    _appSettings = pAppSettings;
    _ndsClusterSvc = pNDSClusterSvc;
    _ndsEmployeeAccessSvc = pNDSEmployeeAccessSvc;
    _searchDeploymentSvc = pSearchDeploymentSvc;
    _searchInstanceSvc = pSearchInstanceSvc;
    _ndsSupportTicketAuditorSvc = pNDSSupportTicketAuditorSvc;
    _dataPlaneAccessRequestSvc = pDataPlaneAccessRequestSvc;
    _informationalAlertSvc = pInformationalAlertSvc;
    _accessTransparencyEventSvc = pAccessTransparencyEventSvc;
  }

  /**
   * Makes sure all automation agent in the group meets the minimum required version to use the log
   * collection feature.
   */
  public void validateVersionForLogCollection(final Group pGroup) throws SvcException {
    final AutomationConfig config = findAutomationConfig(pGroup);
    final Deployment deployment = config.getDeployment();
    final AutomationAgentConfig automationAgentConfig = deployment.getAgentVersion();
    final AgentVersion currentVersion =
        (automationAgentConfig != null)
            ? new AgentVersion(automationAgentConfig.getVersion(), AgentType.AUTOMATION)
            : null;
    // No need to validate the agent version AgentType.SERVERLESS as all serverless agents meet the
    // minimum required version for log collection
    _automationAgentSupportSvc.ensureCurrentVersionNewerThan(
        pGroup, currentVersion, LOG_COLLECTION_MINIMUM_AGENT_VERSION, false);
  }

  /**
   * Main entry point for log requests creation. Finds all hosts which logs should be collected,
   * creates agent jobs, job summaries (one per each agent job) and one log collection job.
   *
   * @return id of main job created
   */
  public String submitLogRequest(
      final AuditInfo pAuditInfo,
      final LogResourceType pLogResourceType,
      final LogCollectionRequest pLogRequest,
      final String pResourceName,
      final Organization pOrg,
      final Group pGroup,
      final AppUser pUser)
      throws SvcException {
    Stopwatch stopwatch = Stopwatch.createStarted();

    validateRestrictedOrgAccess(
        pGroup,
        pUser,
        pLogResourceType,
        pResourceName,
        Arrays.stream(pLogRequest.getTypes()).toList());
    validateRequest(pLogRequest, pGroup);
    validateDateRangeAtlasFlag(pLogRequest, pGroup, pOrg);

    final AutomationConfig automationConfig = findAutomationConfig(pGroup);

    final Deployment deployment = automationConfig.getDeployment();

    List<Process> processes =
        findAllProcessesForResource(pLogResourceType, pResourceName, deployment, pGroup.getId());

    LogCollectionJob job =
        submitJobs(
            automationConfig,
            processes,
            pLogRequest,
            pOrg,
            pGroup,
            pUser,
            pLogResourceType,
            pResourceName);

    LOG.info(
        "Created log collection job #{} for {} [creation parameters: {}, children jobs ids: {}]"
            + " time: {}",
        job.getId(),
        pLogResourceType,
        job.getChildrenJobs().stream()
            .map(AgentJobSummary::getAutomationAgentId)
            .limit(25)
            .collect(toList()),
        pLogRequest,
        stopwatch);

    auditForLogRequest(pAuditInfo, pLogResourceType, pLogRequest, pResourceName, pGroup);

    return job.getId().toString();
  }

  public void validateRestrictedOrgAccess(
      final Group pGroup,
      final AppUser pAppUser,
      final LogResourceType pLogResourceType,
      final String pResourceName,
      final List<LogCollectionType> pLogCollectionTypes)
      throws SvcException {

    final Set<LogCollectionType> specifiedRestrictedLogCollectionTypes =
        pLogCollectionTypes.stream()
            .filter(type -> !UNRESTRICTED_LOG_COLLECTION_TYPES.contains(type))
            .collect(toSet());

    // if only unrestricted log types have been specified, allow the request to proceed
    if (specifiedRestrictedLogCollectionTypes.isEmpty()) {
      return;
    }

    final boolean logTypesAvailableWithDatabaseLogGrant =
        CLUSTER_DATABASE_LOG_TYPES.containsAll(specifiedRestrictedLogCollectionTypes);
    final EmployeeAccessGrantType grantTypeRequired =
        logTypesAvailableWithDatabaseLogGrant
            ? EmployeeAccessGrantType.CLUSTER_DATABASE_LOGS
            : EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE;

    final Optional<String> hostname =
        findHostnameForRequest(pGroup, pLogResourceType, pResourceName);

    try {
      _ndsEmployeeAccessSvc.checkOrgEmployeeAccessRestricted(
          pGroup, hostname.orElse(null), grantTypeRequired, pAppUser);
    } catch (SvcException e) {
      throw new SvcException(LogCollectionErrorCode.LOG_ACCESS_REQUIRES_CUSTOMER_GRANT);
    }
  }

  protected Optional<String> findHostnameForRequest(
      final Group pGroup, final LogResourceType pResourceType, final String pResourceName) {

    try {
      final AutomationConfig automationConfig = findAutomationConfig(pGroup);

      final Optional<String> processHostname =
          findAllProcessesForResource(
                  pResourceType, pResourceName, automationConfig.getDeployment(), pGroup.getId())
              .stream()
              .filter(Process::isMongod)
              .findFirst()
              .map(Process::getHostname);

      return processHostname;
    } catch (final SvcException pE) {
      LOG.warn(pE.getMessage(), pE);
      return Optional.empty();
    }
  }

  /** Returns the full list of log collection requests. */
  public List<LogCollectionJob> getLogRequests(final Group pGroup, final boolean pFindChildren) {
    return _logCollectionDao.findJobsForGroup(pGroup.getId(), pFindChildren);
  }

  /**
   * Returns the list of log collection requests except logs marked as MARKED_FOR_DELETION
   *
   * @param pGroup
   * @return List<LogCollectionJob> is the list of log collection requests except logs marked as
   *     MARKED_FOR_DELETION
   */
  public List<LogCollectionJob> getExistingLogRequests(
      final Group pGroup, final boolean pFindChildren) {
    return _logCollectionDao.findExistingJobsForGroup(pGroup.getId(), pFindChildren);
  }

  public ModelCursor<LogCollectionJob> getModelCursorByGroup(
      final Group pGroup, final boolean pFindChildren) {
    if (pFindChildren) {
      return _logCollectionDao.getModelCursorForGroupWithChildren(pGroup);
    }

    return _logCollectionDao.getModelCursorForGroup(pGroup);
  }

  public void changeLogJobStatus(final ObjectId pJobId, final LogCollectionStatus pStatus) {
    _logCollectionDao.changeLogJobStatus(pJobId, pStatus);
  }

  public void changeExpirationDate(final ObjectId pJobId, final Date pExtendDate) {
    _logCollectionDao.changeExpirationDate(pJobId, pExtendDate);
  }

  public Optional<LogCollectionJob> findOneJobWithChildren(final ObjectId pLogCollectionJobId) {
    return _logCollectionDao.findOneJobWithChildren(pLogCollectionJobId);
  }

  public Optional<LogCollectionJob> findOneJobWithoutChildren(final ObjectId pLogCollectionJobId) {
    return _logCollectionDao.findOneJobWithoutChildren(pLogCollectionJobId);
  }

  public List<LogCollectionJob> findJobsByStatus(
      final LogCollectionStatus... pLogCollectionStatuses) {
    return _logCollectionDao.findJobsByStatus(pLogCollectionStatuses);
  }

  public List<LogCollectionJob> findJobsWithChildrenByStatus(
      final LogCollectionStatus... pLogCollectionStatus) {
    return _logCollectionDao.findJobsWithChildrenByStatus(pLogCollectionStatus);
  }

  public void markJobsExpired(final Date pDate) {
    _logCollectionDao.markJobsExpired(pDate);
  }

  public int expireJobs() {
    return _logCollectionDao.expireJobs();
  }

  public void remove(final ObjectId pLogCollectionJobId) {
    _logCollectionDao.remove(pLogCollectionJobId);
  }

  public boolean updateOptimistic(final LogCollectionJob pLogCollectionJob) {
    return _logCollectionDao.updateOptimistic(pLogCollectionJob);
  }

  public void create(final LogCollectionJob pLogCollectionJob) {
    _logCollectionDao.create(pLogCollectionJob);
  }

  /**
   * Retries the job in case it's current status is failed. Results in creations of new agent jobs
   * and drop/create of matching job summaries.
   */
  public void retryLogRequestInGroup(final ObjectId pLogCollectionJobId, final ObjectId pGroupId)
      throws SvcException {
    Stopwatch stopwatch = Stopwatch.createStarted();

    final LogCollectionJob job = findLogCollectionJobInGroup(pLogCollectionJobId, pGroupId, true);

    if (job.getStatus() != LogCollectionStatus.FAILURE) {
      throw new SvcException(LogCollectionErrorCode.ONLY_FAILED_JOB_CAN_BE_RESTARTED);
    }

    final List<AgentJobSummary> failedAgentJobsSummaries =
        job.getChildrenJobs().stream()
            .filter(j -> j.getStatus() == LogCollectionStatus.FAILURE)
            .collect(toList());

    Preconditions.checkState(
        failedAgentJobsSummaries.size() > 0,
        "Internal error: no failed children jobs found, but log " + "request is marked as FAILED!");

    int restarted = 0;
    for (final AgentJobSummary failedAgentJobSummary : failedAgentJobsSummaries) {
      try {
        retryChildJob(failedAgentJobSummary);
        restarted++;
      } catch (Exception e) {
        LOG.error("Failed to restart job {}", failedAgentJobSummary.getAutomationAgentId(), e);
      }
    }

    if (restarted > 0) {
      changeLogJobStatus(pLogCollectionJobId, LogCollectionStatus.IN_PROGRESS);
    } else {
      LOG.error(
          "Failed to restart any job inside request {} - cannot mark it IN_PROGRESS.",
          pLogCollectionJobId);
      throw new RuntimeException(
          "Failed to restart log collection job as no job summaries were restarted.");
    }

    LOG.info("Restarted {} agent jobs, time: {}", failedAgentJobsSummaries.size(), stopwatch);
  }

  /**
   * Returns estimates for number of log files grouped by log types (e.g. MONGODB, FTDC etc) that
   * can be requested for collection for specified {@code pResourceName}. This is expected to be
   * called before making real request for log collection. Size of each inner map is the same and
   * equal to the number of all {@link LogCollectionType} possible. If any of types doesn't exist -
   * it will have count equal to 0.
   *
   * <p>Note, that these estimates consider one log file per process/host, but in fact there can be
   * many of them as log files are rotated.
   */
  public LogCollectionEstimate getLogCollectionsEstimate(
      final Group pGroup, final LogResourceType pResourceType, final String pResourceName)
      throws SvcException {
    validateVersionForLogCollection(pGroup);

    final AutomationConfig automationConfig = findAutomationConfig(pGroup);
    final Deployment deployment = automationConfig.getDeployment();

    try {
      List<Process> processes =
          findAllProcessesForResource(pResourceType, pResourceName, deployment, pGroup.getId());
      final Set<String> mongotHostnames = getMongotHostnames(pGroup, pResourceType, processes);

      return calculateEstimates(pGroup, deployment, processes, mongotHostnames);
    } catch (final SvcException e) {
      return LogCollectionEstimate.builder()
          .numLogHostsAvailable(Map.of())
          .numSyslogHosts(Map.of())
          .build();
    }
  }

  public void deleteLogRequestInGroup(final ObjectId pJobId, final ObjectId pGroupId)
      throws SvcException {
    final Stopwatch stopwatch = Stopwatch.createStarted();

    final LogCollectionJob job = findLogCollectionJobInGroup(pJobId, pGroupId, true);
    changeLogJobStatus(pJobId, LogCollectionStatus.MARKED_FOR_DELETION);
    LOG.info(
        "log job {} has been marked as {}, "
            + "it will be removed with the clean-up cron task, time: {}",
        job.getId(),
        LogCollectionStatus.MARKED_FOR_DELETION.name(),
        stopwatch);
  }

  /**
   * Extends the expiration date for the job by a further {@code DEFAULT_EXTENSION_DAYS} days.
   * Throws an error if the job is already expired.
   */
  public void extendLogRequestInGroup(final ObjectId pJobId, final ObjectId pGroupId)
      throws SvcException {
    final LogCollectionJob job = findLogCollectionJobInGroup(pJobId, pGroupId, false);
    Date defaultExtendDate = DateUtils.addDays(job.getExpirationDate(), getDefaultExtensionDays());
    this.extendLogRequestInGroup(pJobId, pGroupId, defaultExtendDate);
  }

  /**
   * Extends the expiration date for the job to a given Date in the future. Throws the error if the
   * job is already expired, or the date provided is in the past.
   */
  public void extendLogRequestInGroup(
      final ObjectId pJobId, final ObjectId pGroupId, final Date pExtendDate) throws SvcException {
    final LogCollectionJob job = findLogCollectionJobInGroup(pJobId, pGroupId, false);

    final Date previousExpirationDate = job.getExpirationDate();
    if (job.getStatus() == LogCollectionStatus.EXPIRED) {
      throw new SvcException(LogCollectionErrorCode.CANNOT_EXTEND_EXPIRED_JOB);
    }

    Date now = new Date();
    if (pExtendDate.before(now)) {
      throw new SvcException(LogCollectionErrorCode.EXPIRATION_DATE_MUST_BE_IN_FUTURE);
    }

    Date expirationLimit =
        DateUtils.addMonths(job.getCreationDate(), MAXIMUM_EXTENSION_DATE_MONTHS);
    if (pExtendDate.after(expirationLimit)) {
      throw new SvcException(LogCollectionErrorCode.EXPIRATION_DATE_TOO_DISTANT);
    }

    changeExpirationDate(job.getId(), pExtendDate);

    LOG.info(
        "Extended the job {} from expiration date {} to new expiration date {}.",
        job.getId(),
        previousExpirationDate,
        pExtendDate);
  }

  public long writeLogs(
      final LogCollectionJob pParentJob,
      final ObjectId pAgentJobId,
      final List<Map.Entry<ContentDisposition, InputStream>> pInput,
      final Stopwatch pStopwatch)
      throws SvcException {
    long cumulativeDiskspaceBytes = 0;

    final AgentJobSummary jobSummary;
    try {
      jobSummary = findAgentJobSummary(pAgentJobId);
    } catch (final SvcException pEx) {
      LOG.error("Error finding agent summary job with id {}", pAgentJobId, pEx);
      tryMarkJobSummaryFailed(
          pAgentJobId, "Internal error: couldn't find agent summary job", 0, new TreeMap<>());
      throw pEx;
    }

    final SortedMap<String, LogCollectionFileMetadata> fileMetadata = jobSummary.getFileMetadata();
    final ObjectId groupId = pParentJob.getGroupId();
    for (Map.Entry<ContentDisposition, InputStream> fileData : pInput) {
      try {
        final String filename = fileData.getKey().getFileName();
        final long bytesWritten =
            writeFileGzippedAndEncrypted(pParentJob, jobSummary, fileData, groupId);
        cumulativeDiskspaceBytes += bytesWritten;
        fileMetadata.put(
            filename,
            LogCollectionFileMetadata.Builder.builder()
                .location(
                    getFileLocationForStorageBackend(pParentJob, groupId, pAgentJobId, filename))
                .size(bytesWritten)
                .build());
      } catch (final Exception pEx) {
        LOG.error(
            "Error writing logs to {} for job {}, bytes written: {}",
            pParentJob.getStorageBackend(),
            pAgentJobId,
            cumulativeDiskspaceBytes,
            pEx);
        tryMarkJobSummaryFailed(
            pAgentJobId,
            "Internal error: couldn't write logs to " + pParentJob.getStorageBackend().name(),
            cumulativeDiskspaceBytes,
            fileMetadata);
        throw new SvcException(CommonErrorCode.SERVER_ERROR, pEx);
      }
    }

    try {
      markJobSummarySuccessful(pAgentJobId, cumulativeDiskspaceBytes, fileMetadata);
    } catch (final SvcException pEx) {
      LOG.error(
          "Failed to mark job summary {} successful, marking it as failed...", pAgentJobId, pEx);
      tryMarkJobSummaryFailed(pAgentJobId, "Internal error", 0, fileMetadata);
      throw pEx;
    }
    LOG.info(
        "Finished processing mongo log job {}, {} files were written to {} ({}"
            + " bytes total), time: {})",
        pAgentJobId,
        pInput.size(),
        pParentJob.getStorageBackend() == LogCollectionStorageBackend.GRIDFS
            ? "GridFS bucket " + pParentJob.getId()
            : "S3",
        cumulativeDiskspaceBytes,
        pStopwatch);

    return cumulativeDiskspaceBytes;
  }

  public void tryMarkJobSummaryFailed(
      final ObjectId pId,
      final String pErrorMessage,
      final long pUncompressedDiskspaceBytes,
      final SortedMap<String, LogCollectionFileMetadata> pFileMetadata) {
    try {
      markJobSummaryFailed(pId, pErrorMessage, pUncompressedDiskspaceBytes, pFileMetadata);
    } catch (Exception e) {
      LOG.error("Failed to mark job summary {} as failed", pId, e);
      return;
    }
    LOG.info("Marked mongo log job summary {} as failed", pId);
  }

  /*
  Some logic behind: the stream is written to gridFs/s3 as gzipped and encrypted one. So we need to
  manually add some metadata information about uncompressed size of stream being written.
  This will later been used by tar stream to provide correct headers about entry sizes.
  The size provided by agent is written to log collection job summary and is displayed on
  UI only but shouldn't be used for metadata
  */
  long writeFileGzippedAndEncrypted(
      final LogCollectionJob pLogCollectionJob,
      final AgentJobSummary pAgentJobSummary,
      final Map.Entry<ContentDisposition, InputStream> pFileDataEntry,
      final ObjectId pGroupId)
      throws Exception {
    final ContentDisposition fileMetaData = pFileDataEntry.getKey();
    final InputStream fileInputStream = pFileDataEntry.getValue();
    final boolean shouldRedact =
        pLogCollectionJob.isRedacted()
            && !pAgentJobSummary.getLogCollectionType().equals(LogCollectionType.FTDC);
    final String filename = fileMetaData.getFileName();
    final LogCollectionStorageDao storageDao =
        _logCollectionStorageDaoFactory.getLogStorageDao(pLogCollectionJob.getStorageBackend());

    return storageDao.write(
        pLogCollectionJob,
        pAgentJobSummary.getAutomationAgentId(),
        fileInputStream,
        getFilenameForStorageBackend(
            pLogCollectionJob, pGroupId, pAgentJobSummary.getAutomationAgentId(), filename),
        shouldRedact);
  }

  /**
   * Creates the tar file stream for log collection job. All the log files uploaded by agent jobs
   * before will be packed into {@link TarArchiveOutputStream} and streamed to client. The directory
   * structure inside tar file has the directory with the same name as tar file in the root and then
   * log files are grouped by hostname. See {@link #resolvePath} for more details.
   */
  public StreamingOutput createStreamingForDownload(
      final Group pGroup,
      final AppUser pUser,
      final LogCollectionJob pJob,
      final AuditInfo pAuditInfo,
      final ServletOutputStream pResponseOut,
      final String pReason)
      throws SvcException {

    // ensure the org is not restricted or if it is restricted, a bypass has been granted (Atlas
    // only)
    validateRestrictedOrgAccess(
        pGroup,
        pUser,
        pJob.getResourceType(),
        pJob.getResourceName(),
        Arrays.stream(pJob.getLogCollectionTypes()).collect(Collectors.toUnmodifiableList()));

    if (pJob.getStatus() == LogCollectionStatus.EXPIRED) {
      throw new SvcException(LogCollectionErrorCode.CANNOT_DOWNLOAD_EXPIRED_JOB);
    }
    if (pJob.getStatus() == LogCollectionStatus.IN_PROGRESS) {
      throw new SvcException(LogCollectionErrorCode.CANNOT_DOWNLOAD_JOB_IN_PROGRESS);
    }
    final List<AgentJobSummary> agentJobSummaries = sortedSummaries(pJob);

    final Boolean dataPlaneAccessRequestValidated;
    final String reason;
    final boolean existsNonFTDCLogs =
        Arrays.stream(pJob.getLogCollectionTypes())
            .anyMatch(
                logType -> logType != FTDC && logType != MONGOT_FTDC && logType != MONGOTUNE_FTDC);
    final boolean isAtlas = pGroup.isAtlas();
    final boolean shouldLogAccessRequest = existsNonFTDCLogs && isAtlas;
    if (shouldLogAccessRequest) {
      dataPlaneAccessRequestValidated = _ndsSupportTicketAuditorSvc.validateTicket(pReason);
      reason = pReason;
    } else {
      dataPlaneAccessRequestValidated = null;
      reason = null;
    }

    final AccessTransparencyEventInformation accessTransparencyEventInformation;
    if (requiresCustomerFacingAudit(pJob)) {
      accessTransparencyEventInformation =
          _accessTransparencyEventSvc.getAccessTransparencyEventFields(pGroup.getId(), pUser);
    } else {
      // NOTE: the hidden property in the result below should not be used.
      accessTransparencyEventInformation = new HiddenEmptyAccessTransparencyEventInformation();
    }

    auditForLogDownload(
        pAuditInfo,
        pJob,
        agentJobSummaries,
        pUser.getUsername(),
        shouldLogAccessRequest,
        reason,
        dataPlaneAccessRequestValidated,
        pGroup.getGroupType() == GroupType.ONPREM,
        accessTransparencyEventInformation);

    final Stopwatch stopwatch = Stopwatch.createStarted();

    return output -> {
      try (CountingOutputStream cout = new CountingOutputStream(output);
          GZIPOutputStream gzout = new GZIPOutputStream(cout);
          TarArchiveOutputStream tarOs = new TarArchiveOutputStream(gzout)) {
        // purposefully nested try, so that channel is aborted before it is cleaned up
        try {
          /*
          According to http://commons.apache.org/proper/commons-compress/tar.html#Long_File_Names
          this mode is the most preferable for handling long file names in tar files (> 100 bytes).
          */
          tarOs.setLongFileMode(LONGFILE_POSIX);
          for (AgentJobSummary jobSummary : agentJobSummaries) {
            streamLogsForSingleJobSummary(pGroup, pJob, tarOs, jobSummary);
          }
          tarOs.finish();
          gzout.finish();

          LOG.info(
              "Tar gz file for job \"{}\" successfully downloaded, size: {} bytes,  time: {}",
              pJob.getId(),
              cout.getBytesWritten(),
              stopwatch.stop());
        } catch (Exception e) {
          LOG.error(
              "Failed to stream tar file for job \"{}\" to client, time: {}",
              pJob.getId(),
              stopwatch);
          ((HttpOutput) pResponseOut).getHttpChannel().abort(e);
        }
      }
    };
  }

  /**
   * Formats the file name of download artifact. This name is also used as the root directory in it.
   */
  public String formatFileNameForDownload(final LogCollectionJob pJob) {
    String creationDateFormatted = formatDateISO8601(pJob.getCreationDate());

    return String.format("mongodb-logfiles_%s_%s", pJob.getResourceName(), creationDateFormatted);
  }

  public static String formatDateISO8601(final Date pDate) {
    final Instant creationDateInstant = pDate.toInstant().truncatedTo(ChronoUnit.MINUTES);
    final OffsetDateTime utcDateTime = creationDateInstant.atOffset(ZoneOffset.UTC);

    return utcDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HHmm'Z'"));
  }

  /**
   * Finds log collection request or throws {@link SvcException}.
   *
   * @param pObjectId
   */
  public LogCollectionJob findLogCollectionJob(
      final ObjectId pObjectId, final boolean pFindChildren) throws SvcException {
    Optional<LogCollectionJob> job;

    if (pFindChildren) {
      job = findOneJobWithChildren(pObjectId);
    } else {
      job = findOneJobWithoutChildren(pObjectId);
    }

    return job.orElseThrow(
        () -> new SvcException(LogCollectionErrorCode.LOG_COLLECTION_JOB_NOT_FOUND, pObjectId));
  }

  /**
   * Finds log collection request in a group or throws {@link SvcException}.
   *
   * @param pObjectId
   */
  public LogCollectionJob findLogCollectionJobInGroup(
      final ObjectId pObjectId, final ObjectId pGroupId, final boolean pFindChildren)
      throws SvcException {
    final LogCollectionJob pJob = this.findLogCollectionJob(pObjectId, pFindChildren);
    if (!pJob.getGroupId().equals(pGroupId)
        || pJob.getStatus().equals(LogCollectionStatus.MARKED_FOR_DELETION)) {
      throw new SvcException(LogCollectionErrorCode.LOG_COLLECTION_JOB_NOT_FOUND, pObjectId);
    }

    return pJob;
  }

  /**
   * Finds log collection request or throws {@link SvcException}.
   *
   * @param pObjectId
   */
  public AgentJobSummary findAgentJobSummary(final ObjectId pObjectId) throws SvcException {
    return _agentJobSummaryDao
        .findById(pObjectId)
        .orElseThrow(
            () -> new SvcException(LogCollectionErrorCode.AGENT_JOB_SUMMARY_NOT_FOUND, pObjectId));
  }

  /**
   * Internal API for AgentJob only. Marks the job as finished. Method is not thread safe as it's
   * expected that it will be called only once by job handler. Transactional behaviour: as there's
   * no transaction manager there is slight possibility that state of parent job will become
   * inconsistent if {@link LogCollectionSvc#recalculateParentJob} method fails for some reason. If
   * the problem is temporary then it is supposed to be fixed eventually as soon as other job
   * summary is changed.
   */
  public void markJobSummarySuccessful(
      final ObjectId pJobId,
      final long pUncompressedDiskspaceBytes,
      final SortedMap<String, LogCollectionFileMetadata> pFileMetadata)
      throws SvcException {
    final AgentJobSummary agentJobSummary = findAgentJobSummary(pJobId);
    markJobSummarySuccessful(agentJobSummary, pUncompressedDiskspaceBytes, pFileMetadata);
  }

  public void markJobSummarySuccessful(
      final AgentJobSummary pAgentJobSummary,
      final long pUncompressedDiskspaceBytes,
      final SortedMap<String, LogCollectionFileMetadata> pFileMetadata)
      throws SvcException {
    /*
    Save file metadata regardless of what the status is. This way, even if the status
    is not success, reading the file won't have an issue.
     */
    final boolean isJobMarkedInProgress =
        pAgentJobSummary.getStatus() == LogCollectionStatus.IN_PROGRESS;
    final AgentJobSummary.AgentJobSummaryBuilder builder =
        pAgentJobSummary.toBuilder()
            .finishDate(new Date())
            .uncompressedDiskspaceBytes(pUncompressedDiskspaceBytes)
            .errorMessage(null)
            .fileMetadata(pFileMetadata);

    if (isJobMarkedInProgress) {
      builder.status(LogCollectionStatus.SUCCESS);
    }
    _agentJobSummaryDao.save(builder.build());

    /*
    Throw exception if the job is not marked in progress, because we weren't able to update
    the status and the error message field.
     */
    if (!isJobMarkedInProgress) {
      throw new SvcException(LogCollectionErrorCode.CANNOT_MARK_JOB_SUMMARY_WRONG_STATUS);
    }

    // Recalculate parent job if saving the job was successful.
    recalculateParentJob(pAgentJobSummary.getLogCollectionJobId());
  }

  /**
   * Internal API for AgentJob only. Marks the job as failed. Method is not thread safe as it's
   * expected that it will be called only once by job handler. Transactional behaviour: see
   * description for {{@link LogCollectionSvc#markJobSummarySuccessful}.
   */
  public void markJobSummaryFailed(
      final ObjectId pJobId,
      final String pErrorMsg,
      final long pUncompressedDiskspaceBytes,
      final SortedMap<String, LogCollectionFileMetadata> pFileMetadata)
      throws SvcException {
    final AgentJobSummary agentJobSummary = findAgentJobSummary(pJobId);
    markJobSummaryFailed(agentJobSummary, pErrorMsg, pUncompressedDiskspaceBytes, pFileMetadata);
  }

  public void markJobSummaryFailed(
      final AgentJobSummary pAgentJobSummary,
      final String pErrorMsg,
      final long pUncompressedDiskspaceBytes,
      final SortedMap<String, LogCollectionFileMetadata> pFileMetadata)
      throws SvcException {
    /*
    Save file metadata regardless of what the status is. This way, even if the status
    is not success, reading the file won't have an issue.
     */
    final boolean isJobMarkedInProgress =
        pAgentJobSummary.getStatus() == LogCollectionStatus.IN_PROGRESS;
    final AgentJobSummary.AgentJobSummaryBuilder builder =
        pAgentJobSummary.toBuilder()
            .finishDate(new Date())
            .uncompressedDiskspaceBytes(pUncompressedDiskspaceBytes)
            .fileMetadata(pFileMetadata);

    if (isJobMarkedInProgress) {
      builder.status(LogCollectionStatus.FAILURE);
      builder.errorMessage(pErrorMsg);
    }
    _agentJobSummaryDao.save(builder.build());

    /*
    Throw exception if the job is not marked in progress, because we weren't able to update
    the status and the error message field.
     */
    if (!isJobMarkedInProgress) {
      throw new SvcException(LogCollectionErrorCode.CANNOT_MARK_JOB_SUMMARY_WRONG_STATUS);
    }

    // Recalculate parent job if saving the job was successful.
    recalculateParentJob(pAgentJobSummary.getLogCollectionJobId());
  }

  /**
   * Does expiration, complete removal of jobs and cleanup of log collection artifacts. Method is
   * not thread-safe so is expected to be called only from cron scheduled job. The procedure is
   * eventually consistent - even if it fails at any stage it's supposed to finish work on next
   * invocation.
   */
  public void performGc() {
    final Stopwatch stopwatch = Stopwatch.createStarted();

    // 1. Mark all job summaries which started before previous cron run as failed. Recalculating
    // parent jobs in this case.
    int markedFailedCount = 0;
    try {
      markedFailedCount =
          _agentJobSummaryDao.markJobSummariesFailed(
              DateUtils.addHours(new Date(), -LOG_COLLECTION_GC_INTERVAL_HOURS),
              "Marked failed by GC process as ran too long.",
              new Date());
      if (markedFailedCount > 0) {
        /*
        Unfortunately we cannot say which log requests were affected by previous operation so
        let's walk through all IN_PROGRESS jobs.
        */
        findJobsByStatus(LogCollectionStatus.IN_PROGRESS)
            .forEach(request -> recalculateParentJob(request.getId()));
      }
    } catch (final Exception e) {
      LOG.error(
          "Failed to mark long running job summaries as failed, will retry during next gc task.",
          e);
    }

    // 2. Mark jobs expired
    try {
      markJobsExpired(new Date());
    } catch (final Exception e) {
      LOG.error(
          "Failed to mark log collection jobs expired during background cleanup procedure.", e);
    }

    // 3. Remove all GridFs buckets/s3 objects for log requests marked expired
    int bucketsRemovedCount;
    try {
      bucketsRemovedCount = removeBucketsForStatus(LogCollectionStatus.MARKED_FOR_EXPIRY);
    } catch (final Exception e) {
      // This is fatal situation; if some buckets were not removed, we cannot finalize jobs for them
      throw new RuntimeException(
          "Failed to drop buckets marked for expiration during background cleanup procedure.", e);
    }

    // 4. Remove all waiting deletion log requests
    int logRequestsDeleted;
    try {
      List<LogCollectionJob> completedWaitingDeletionJobs = getCompletedWaitingDeletionJobs();
      bucketsRemovedCount += removeBucketsForJobs(completedWaitingDeletionJobs);
      deleteLogRequests(completedWaitingDeletionJobs);
      logRequestsDeleted = completedWaitingDeletionJobs.size();
    } catch (final Exception e) {
      throw new RuntimeException("Failed to delete log collection jobs marked for deletion.", e);
    }

    // 5. Set all marked expired log requests to expired
    int expiredLogRequestsCount = 0;
    try {
      expiredLogRequestsCount = expireJobs();
    } catch (final Exception e) {
      LOG.error(
          "Failed to expire log collection jobs during background cleanup procedure, will retry"
              + " during next gc task.",
          e);
    }

    LOG.info(
        "Log collection GC is finished, {} log requests were EXPIRED, data for {} job requests was"
            + " removed, {} job summaries marked failed, {} log request jobs marked for deletion"
            + " were deleted, time: {}",
        expiredLogRequestsCount,
        bucketsRemovedCount,
        markedFailedCount,
        logRequestsDeleted,
        stopwatch);
  }

  public LogCollectionStorageBackend getStorageBackend(
      final Organization pOrg, final Group pGroup) {
    if (isFeatureFlagEnabled(FeatureFlag.LOGCOLLECTION_S3_BACKEND, _appSettings, pOrg, pGroup)) {
      return LogCollectionStorageBackend.S3;
    } else {
      return LogCollectionStorageBackend.GRIDFS;
    }
  }

  public String getFilenameForStorageBackend(
      final LogCollectionJob pLogCollectionJob,
      final ObjectId pGroupId,
      final ObjectId pAgentJobId,
      final String pFilename) {
    if (pLogCollectionJob.getStorageBackend() == LogCollectionStorageBackend.S3) {
      return getFileLocationForStorageBackend(pLogCollectionJob, pGroupId, pAgentJobId, pFilename);
    } else {
      return pFilename;
    }
  }

  public String getFileLocationForStorageBackend(
      final LogCollectionJob pLogCollectionJob,
      final ObjectId pGroupId,
      final ObjectId pAgentJobId,
      final String pFilename) {
    final boolean slashNeededBeforeFilename = !pFilename.startsWith("/");
    if (pLogCollectionJob.getStorageBackend() == LogCollectionStorageBackend.S3) {
      return LogCollectionStorageBackend.S3.getFileNamePrefix()
          + reverseObjectId(pGroupId)
          + "/"
          + pLogCollectionJob.getId()
          + "/"
          + pAgentJobId
          + (slashNeededBeforeFilename ? "/" : "")
          + pFilename;
    } else {
      return LogCollectionStorageBackend.GRIDFS.getFileNamePrefix()
          + pLogCollectionJob.getId()
          + (slashNeededBeforeFilename ? "/" : "")
          + pFilename;
    }
  }

  // ************************************* Private methods
  // ***********************************************************

  /**
   * Removes the log request and all linked artifacts (agent summaries and agent jobs). Job status
   * is not checked
   *
   * <p>Note, that the removal procedure has the "fail fast" strategy - any exception is raised as
   * soon as it happens. This makes sure that no garbage is left after successful removal but user
   * will have to retry the operation in case of any errors.
   */
  private void deleteLogRequest(final LogCollectionJob pJob) {
    final Stopwatch stopwatch = Stopwatch.createStarted();

    for (final AgentJobSummary jobSummary : pJob.getChildrenJobs()) {
      final ObjectId automationAgentId = jobSummary.getAutomationAgentId();
      try {
        _agentJobSummaryDao.remove(automationAgentId);
        _agentJobsProcessorSvc.deleteJob(automationAgentId);
      } catch (Exception e) {
        LOG.error("Failed to clean resources for agent job {}", automationAgentId, e);
        throw e;
      }
    }

    // removing the log request job after all others - makes sure all other artifacts are removed
    remove(pJob.getId());

    LOG.info(
        "Removed log job {} and all resources bound with it, time: {}", pJob.getId(), stopwatch);
  }

  private void deleteLogRequests(final List<LogCollectionJob> pJobs) {
    for (LogCollectionJob job : pJobs) {
      deleteLogRequest(job);
    }
  }

  private int removeBucketsForJobs(final List<LogCollectionJob> pJobs) {
    boolean hasFailures = false;
    for (final LogCollectionJob job : pJobs) {
      MDC.put(MDCUtil.GROUP_ID, job.getGroupId().toString());
      try {
        final LogCollectionStorageDao storageDao =
            _logCollectionStorageDaoFactory.getLogStorageDao(job.getStorageBackend());

        storageDao.delete(job);
        LOG.debug(
            "Removed data for LogCollectionJob ID: {} (job is {}) from {}",
            job.getId(),
            job.getStatus(),
            job.getStorageBackend() == null
                ? LogCollectionStorageBackend.GRIDFS.name()
                : job.getStorageBackend().name());
      } catch (Exception e) {
        LOG.error("Failed to remove data for LogCollectionJob ID: {}", job.getId(), e);
        hasFailures = true;
      } finally {
        MDC.remove(MDCUtil.GROUP_ID);
      }
    }

    if (hasFailures) {
      throw new RuntimeException("Some log data was not cleaned up.");
    }
    return pJobs.size();
  }

  /*
   getCompletedWaitingRemovalJobs returns a list of completed Waiting Removal Job.
   A log collection job marked as MARKED_FOR_DELETION is considered completed if
   it doesn't have any childrenJob in progress.
  */
  private List<LogCollectionJob> getCompletedWaitingDeletionJobs() {
    List<LogCollectionJob> jobs =
        findJobsWithChildrenByStatus(LogCollectionStatus.MARKED_FOR_DELETION);

    List<LogCollectionJob> completedJobs = new ArrayList<>() {};
    boolean hasChildrenInProgress;
    for (LogCollectionJob job : jobs) {
      hasChildrenInProgress =
          job.getChildrenJobs().stream()
              .noneMatch(
                  child -> child.getStatus().name().equals(LogCollectionStatus.IN_PROGRESS.name()));

      if (hasChildrenInProgress) {
        completedJobs.add(job);
      }
    }

    return completedJobs;
  }

  private int removeBucketsForStatus(final LogCollectionStatus... pStatuses) {
    final List<LogCollectionJob> jobs = findJobsWithChildrenByStatus(pStatuses);
    return removeBucketsForJobs(jobs);
  }

  private void checkStatusIsInProgress(final AgentJobSummary pJobSummary) throws SvcException {
    if (pJobSummary.getStatus() != LogCollectionStatus.IN_PROGRESS) {
      throw new SvcException(LogCollectionErrorCode.CANNOT_MARK_JOB_SUMMARY_WRONG_STATUS);
    }
  }

  /*
  Makes parent job consistent with all children jobs. Done in CAS way: reading the request
  with its children, calculating different data and trying to optimistically update
  the log collection request. To make sure operation succeeds even in some unexpected contention -
  the short sleep is done after each iteration. CAS is restricted with 20 trials just in case.
  */
  private void recalculateParentJob(final ObjectId pLogCollectionJobId) {
    for (int i = 0; i < LOG_JOB_SAVE_TRIALS_NUMBER; i++) {
      final LogCollectionJob logCollectionJob;
      try {
        logCollectionJob = findLogCollectionJob(pLogCollectionJobId, true);
      } catch (SvcException e) {
        throw new RuntimeException(e);
      }

      if (logCollectionJob.getStatus() == LogCollectionStatus.EXPIRED
          || logCollectionJob.getStatus() == LogCollectionStatus.SUCCESS
          || logCollectionJob.getStatus() == LogCollectionStatus.MARKED_FOR_DELETION) {
        // Expired / Successful / Marked for Deletion log request jobs are considered readonly
        return;
      }
      LogCollectionStatus newStatus = logCollectionJob.getStatus();
      boolean hasFailed = false;
      long sizeBytes = 0;
      int numFinishedJobs = 0;

      for (final AgentJobSummary jobSummary : logCollectionJob.getChildrenJobs()) {
        if (jobSummary.getStatus() == LogCollectionStatus.FAILURE) {
          hasFailed = true;
        }
        if (jobSummary.getStatus() == LogCollectionStatus.SUCCESS
            || jobSummary.getStatus() == LogCollectionStatus.FAILURE) {
          numFinishedJobs++;
        }
        sizeBytes += jobSummary.getUncompressedDiskspaceBytes();
      }
      LogCollectionJob.LogCollectionJobBuilder builder =
          logCollectionJob.toBuilder().status(newStatus).sizeTotalBytes(sizeBytes);

      if (numFinishedJobs == logCollectionJob.getChildrenJobs().size()) {
        builder = builder.status(LogCollectionStatus.SUCCESS).finishDate(new Date());
        if (hasFailed) {
          builder = builder.status(LogCollectionStatus.FAILURE);
        }
      }

      final LogCollectionJob updatedJob = builder.build();
      if (updatedJob.equals(logCollectionJob) || updateOptimistic(updatedJob)) {
        LogRedactionUtil.removeInstanceByJobId(logCollectionJob.getId());
        return;
      }
      try {
        TimeUnit.MILLISECONDS.sleep(100);
      } catch (InterruptedException e) {
        LOG.error("Interrupted while waiting in CAS loop to update LogCollectionJob", e);
        return;
      }
    }
    LOG.error(
        "Failed to update LogCollectionJob {} after {} attempts. This may imply some unexpected"
            + " high contention on your AppDB.",
        pLogCollectionJobId,
        LOG_JOB_SAVE_TRIALS_NUMBER);
  }

  protected AutomationConfig findAutomationConfig(final Group pGroup) throws SvcException {
    final AutomationConfig config = _automationConfigSvc.findPublished(pGroup.getId());
    if (config == null) {
      throw new SvcException(LogCollectionErrorCode.AUTOMATION_CONFIG_NOT_FOUND, pGroup.getId());
    }
    return config;
  }

  private void validateRequest(final LogCollectionRequest pLogRequest, final Group pGroup)
      throws SvcException {
    if (pLogRequest.getSizeInBytes() <= 0) {
      throw new SvcException(LogCollectionErrorCode.INVALID_LOG_REQUEST_SIZE);
    }
    if (pLogRequest.getTypes().length == 0) {
      throw new SvcException(LogCollectionErrorCode.INVALID_LOG_REQUEST_TYPES);
    }
    validateVersionForLogCollection(pGroup);
  }

  boolean isValidDateRange(final Date fromDate, final Date toDate) {
    Date todayDate = new Date();

    return (fromDate == null && toDate == null)
        || (fromDate != null
            && toDate != null
            && fromDate.before(toDate)
            && fromDate.before(todayDate)
            && toDate.before(todayDate));
  }

  void validateDateRangeAtlasFlag(
      final LogCollectionRequest pLogRequest, final Group pGroup, final Organization pOrg)
      throws SvcException {
    final Date fromDate = pLogRequest.getLogCollectionFromDate();
    final Date toDate = pLogRequest.getLogCollectionToDate();
    if (!isValidDateRange(fromDate, toDate)) {
      throw new SvcException(LogCollectionErrorCode.INVALID_LOG_REQUEST_DATES);
    }

    if (fromDate != null && toDate != null) {
      if (!FeatureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.ENABLE_LOG_REQUEST_TIMEFRAME, _appSettings, pOrg, pGroup)) {
        throw new SvcException(LogCollectionErrorCode.LOG_REQUEST_DATES_FLAG_NOT_ENABLED);
      }
      if (!pGroup.isAtlas()) {
        throw new SvcException(LogCollectionErrorCode.INVALID_LOG_REQUEST_DATES_CLUSTER);
      }
    }
  }

  private LogCollectionEstimate calculateEstimates(
      final Group pGroup,
      final Deployment pDeployment,
      final List<Process> pProcesses,
      final Set<String> mongotHostnames) {
    Map<LogCollectionType, Integer> numLogHostsAvailable = new HashMap<>();
    Map<LogCollectionType, Integer> numSyslogHosts = new HashMap<>();

    // Adding all possible log collection types with 0 count
    Arrays.stream(LogCollectionType.values())
        .forEach(
            t -> {
              numLogHostsAvailable.put(t, 0);
              numSyslogHosts.put(t, 0);
            });

    traverseProcessesAndFindLogs(
        pGroup,
        pDeployment,
        pProcesses,
        mongotHostnames,
        LogCollectionType.values(),
        (pProcess, pLogCollectionType) -> {
          // only mongod process can have syslog configured but this may change in future
          if (pLogCollectionType == MONGODB && pProcess.getArgs2_6().isSysLog()) {
            numSyslogHosts.compute(MONGODB, (type, currentCount) -> ++currentCount);
            return;
          }
          numLogHostsAvailable.compute(pLogCollectionType, (type, currentCount) -> ++currentCount);
        });

    return LogCollectionEstimate.builder()
        .numLogHostsAvailable(numLogHostsAvailable)
        .numSyslogHosts(numSyslogHosts)
        .build();
  }

  protected List<Process> findAllProcessesForResource(
      final LogResourceType pLogResourceType,
      final String pResourceName,
      final Deployment pDeployment,
      final ObjectId pGroupId)
      throws SvcException {
    List<Process> processes = null;
    Supplier<SvcException> svcExceptionSupplier =
        () ->
            new SvcException(
                LogCollectionErrorCode.RESOURCE_NOT_FOUND, pResourceName, pLogResourceType);
    switch (pLogResourceType) {
      case CLUSTER:
        processes =
            pDeployment.getProcesses(
                pDeployment.getClusterByName(pResourceName).orElseThrow(svcExceptionSupplier));
        break;
      case REPLICA_SET:
        processes =
            pDeployment.getProcesses(
                pDeployment.getReplicaSetByName(pResourceName).orElseThrow(svcExceptionSupplier));
        break;
      case PROCESS:
        processes =
            List.of(pDeployment.getProcessByName(pResourceName).orElseThrow(svcExceptionSupplier));
    }
    if (CollectionUtils.isEmpty(processes)) {
      throw new IllegalArgumentException(
          String.format("No processes found for %s (%s)", pLogResourceType, pResourceName));
    }

    final boolean anyProcessBelongToPausedCluster =
        processes.stream()
            .filter(p -> p.getCluster() != null)
            .collect(groupingBy(Process::getCluster))
            .keySet()
            .stream()
            .anyMatch(
                deploymentName ->
                    _ndsClusterSvc
                        .getActiveClusterDescriptionByDeploymentClusterName(
                            pGroupId, deploymentName)
                        .map(ClusterDescription::isPaused)
                        .orElse(false));

    if (anyProcessBelongToPausedCluster) {
      throw new SvcException(
          LogCollectionErrorCode.CLUSTER_IS_PAUSED, pResourceName, pLogResourceType);
    }

    return processes;
  }

  private LogCollectionJob submitJobs(
      final AutomationConfig pAutomationConfig,
      final List<Process> pProcesses,
      final LogCollectionRequest pLogRequest,
      final Organization pOrg,
      final Group pGroup,
      final AppUser pUser,
      final LogResourceType pLogResourceType,
      final String pResourceName)
      throws SvcException {
    final List<AgentJob> agentJobs = new ArrayList<>();
    final List<AgentJobSummary> agentJobsSummaries = new ArrayList<>();

    final ObjectId parentJobId = new ObjectId();

    fillJobsAndSummaries(
        parentJobId,
        pAutomationConfig,
        pProcesses,
        pLogRequest,
        pLogResourceType,
        pGroup,
        pUser,
        agentJobs,
        agentJobsSummaries);

    if (agentJobs.isEmpty()) {
      throw new RuntimeException("No jobs could be created based on your parameter.");
    }
    /*
    Create the parent log collection first, as the children jobs expect it to be present.
    It is possible when many jobs are being created that the jobs will check the parent before
    the current method has completed.
    */
    final LogCollectionJob logCollectionJob =
        createLogCollectionJob(
            parentJobId,
            pOrg,
            pGroup,
            pUser,
            pLogRequest,
            pLogResourceType,
            pResourceName,
            agentJobsSummaries,
            pAutomationConfig);
    create(logCollectionJob);

    // Create the summary next, so the parent log collection job can report on the job status
    for (final AgentJobSummary agentJobsSummary : agentJobsSummaries) {
      _agentJobSummaryDao.save(
          agentJobsSummary.toBuilder().logCollectionJobId(logCollectionJob.getId()).build());
    }

    /*
    Finally create the agent jobs. No "transaction" logic here: if anything fails, the summary
    status for the remaining jobs will be marked as expired in the next run of the
    logCollectionCleanup cron job (LogCollectionSvc::performGc), once the expiry limit has
    been reached.
    */
    for (final AgentJob agentJob : agentJobs) {
      _agentJobSvc.putJob(agentJob);
    }

    return logCollectionJob;
  }

  private void fillJobsAndSummaries(
      final ObjectId pParentJobId,
      final AutomationConfig pAutomationConfig,
      final List<Process> pProcesses,
      final LogCollectionRequest pLogRequest,
      final LogResourceType pLogResourceType,
      final Group pGroup,
      final AppUser pUser,
      final List<AgentJob> pAgentJobs,
      final List<AgentJobSummary> pAgentJobsSummaries) {
    _automationConfigSvc.ensureDefaultAgentTemplates(pAutomationConfig, pGroup.isAtlas());

    final String backupAgentLogPath = pAutomationConfig.getBackupAgentTemplate().getLogPath();
    final String monitoringAgentLogPath =
        pAutomationConfig.getMonitoringAgentTemplate().getLogPath();
    final String backupAgentLogWindowsPath =
        pAutomationConfig.getBackupAgentTemplate().getLogPathWindows();
    final String monitoringAgentLogWindowsPath =
        pAutomationConfig.getMonitoringAgentTemplate().getLogPathWindows();
    final String atlasProxyLogPath =
        pAutomationConfig.getDeployment().getAtlasProxies().isEmpty()
            ? NDSAtlasProxyDefaults.LOG_PATH
            : pAutomationConfig.getDeployment().getAtlasProxies().get(0).getLogPath();

    final String cpsModuleLogPath = pAutomationConfig.getCpsModuleTemplate().getLogPath();
    final String mongotLogPath =
        pAutomationConfig.getDeployment().getMongots().isEmpty()
            ? NDSFTSDefaults.MONGOT_LOG_PATH
            : pAutomationConfig.getDeployment().getMongots().get(0).getLogPath();
    final Set<String> mongotHostnames = getMongotHostnames(pGroup, pLogResourceType, pProcesses);

    final String onlineArchiveLogPath =
        pAutomationConfig.getOnlineArchiveModuleTemplate().getLogPath();
    final String onlineArchiveWindowsPath =
        pAutomationConfig.getOnlineArchiveModuleTemplate().getLogPathWindows();
    final String mongotuneLogPath =
        pAutomationConfig.getDeployment().getMaintainedMongotunes().isEmpty()
            ? NDSMongotuneDefaults.DEFAULT_LOG_PATH
            : pAutomationConfig.getDeployment().getMaintainedMongotunes().get(0).getLogPath();

    traverseProcessesAndFindLogs(
        pGroup,
        pAutomationConfig.getDeployment(),
        pProcesses,
        mongotHostnames,
        pLogRequest.getTypes(),
        (pProcess, pLogCollectionType) -> {
          String logPath = null, logPathWindows = null;
          AgentJob job = null;
          switch (pLogCollectionType) {
            case AUTOMATION_AGENT:
              job =
                  GetMongoLogByDiskspaceJob.createForAutomationAgent(
                      ObjectId.get(),
                      pGroup.getId(),
                      pUser.getId(),
                      pProcess.getHostname(),
                      pParentJobId,
                      pLogRequest.getSizeInBytes(),
                      pGroup.getGroupType(),
                      pLogRequest.getLogCollectionFromDate(),
                      pLogRequest.getLogCollectionToDate());
              break;
            case FTDC:
              {
                String diagnosticDataPath = null;
                String dbPath = null;
                String path;
                if (pProcess.isMongod()) {
                  dbPath = pProcess.getArgs2_6().getDbPath();
                } else {
                  boolean isVersionForDiagnosticPath =
                      _appSettings.getAutomationAgentVersion().supportsDiagnosticDataPath();
                  path = getFtdcDiagnosticPath(pProcess, isVersionForDiagnosticPath);
                  if (isVersionForDiagnosticPath) {
                    diagnosticDataPath = path;
                  } else {
                    // for old version we need to send path in dbPath
                    dbPath = path;
                  }
                }
                job =
                    GetMongoLogByDiskspaceJob.createForFTDCData(
                        ObjectId.get(),
                        pGroup.getId(),
                        pUser.getId(),
                        pProcess.getHostname(),
                        pParentJobId,
                        pLogRequest.getSizeInBytes(),
                        pProcess.getProcessType(),
                        dbPath,
                        diagnosticDataPath,
                        pLogRequest.getLogCollectionFromDate(),
                        pLogRequest.getLogCollectionToDate());
                break;
              }
            case MONGOT_FTDC:
              {
                String dbPath = NDSFTSDefaults.MONGOT_DATA_PATH;
                /*
                The job is specified as processType == "mongod" because our current data model
                doesn't really support mongot. The "diagnostic.data" directory will be appended
                to the DB Path for log collection.
                */
                job =
                    GetMongoLogByDiskspaceJob.createForFTDCData(
                        ObjectId.get(),
                        pGroup.getId(),
                        pUser.getId(),
                        pProcess.getHostname(),
                        pParentJobId,
                        pLogRequest.getSizeInBytes(),
                        pProcess.getProcessType(),
                        dbPath,
                        null,
                        pLogRequest.getLogCollectionFromDate(),
                        pLogRequest.getLogCollectionToDate());
                break;
              }
            case MONGOTUNE_FTDC:
              {
                String dbPath = NDSMongotuneDefaults.DATA_PATH;
                /*
                  process type is mongotune. we do not want to add mongotune to processType enum because mongotune is a
                  maintainer, not an agent director.
                */
                job =
                    GetMongoLogByDiskspaceJob.createForFTDCData(
                        ObjectId.get(),
                        pGroup.getId(),
                        pUser.getId(),
                        pProcess.getHostname(),
                        pParentJobId,
                        pLogRequest.getSizeInBytes(),
                        "mongotune",
                        dbPath,
                        NDSMongotuneDefaults.DEFAULT_FTDC_DIRECTORY,
                        pLogRequest.getLogCollectionFromDate(),
                        pLogRequest.getLogCollectionToDate());
                break;
              }
            case MONGODB:
              // No agent job for syslog!
              if (pProcess.getArgs2_6().isSysLog()) {
                return;
              }
              logPath = logPathWindows = pProcess.getArgs2_6().getLogPath();
              break;
            case BACKUP_AGENT:
              logPath = backupAgentLogPath;
              logPathWindows = backupAgentLogWindowsPath;
              break;
            case MONITORING_AGENT:
              logPath = monitoringAgentLogPath;
              logPathWindows = monitoringAgentLogWindowsPath;
              break;
            case CPS_MODULE:
              {
                logPath = cpsModuleLogPath;
                logPathWindows = cpsModuleLogPath;
                break;
              }
            case CPS_PIT:
              logPath = getPointInTimePath(pProcess);
              logPathWindows = getPointInTimePath(pProcess);
              break;
            case ATLAS_PROXY:
              logPath = atlasProxyLogPath;
              logPathWindows = atlasProxyLogPath;
              break;
            case MONGOT:
              {
                // The job is specified as processType == "mongod" because our current data model
                // doesn't really support mongot.
                logPath = logPathWindows = mongotLogPath;
                break;
              }
            case ONLINE_ARCHIVE:
              logPath = onlineArchiveLogPath;
              logPathWindows = onlineArchiveWindowsPath;
              break;
            case MONGOTUNE:
              logPath = mongotuneLogPath;
              logPathWindows = mongotuneLogPath;
              break;
            default:
              throw new IllegalArgumentException("Unknown collection type: " + pLogCollectionType);
          }
          if (job == null) {
            job =
                GetMongoLogByDiskspaceJob.create(
                    ObjectId.get(),
                    pGroup.getId(),
                    pUser.getId(),
                    pProcess.getHostname(),
                    pParentJobId,
                    pLogRequest.getSizeInBytes(),
                    logPath,
                    logPathWindows,
                    pProcess.getProcessType(),
                    pGroup.getGroupType(),
                    pLogRequest.getLogCollectionFromDate(),
                    pLogRequest.getLogCollectionToDate());
          }
          pAgentJobs.add(job);
          pAgentJobsSummaries.add(
              createSummary(pLogCollectionType, pLogResourceType, pProcess, job));
        });
  }

  /*
  The following cases are addressed to determine the path sent to AA for diagnostic data:
  to save space, ddp: diagnosticDataCollectionDirectoryPath
  1. ddp is configured & AA >= 6.4.0.5655: Send ddp into diagnosticDataPath variable
  2. ddp is configured & AA < 6.4.0.5655: Send ddp from config with stripped filename into dbPath
  variable
  3. ddp not configured & AA >= 6.4.0.5655: Send logpath with Stripped extension +
  ".diagnostic.data" to diagnosticDataPath variable
  4. ddp not configured & AA < 6.4.0.5655: Send logpath with stripped filename to dbPath Variable
  */
  private String getFtdcDiagnosticPath(Process pProcess, boolean pAgentSupportsDiagPath) {
    // use diagnosticDataCollectionDirectoryPath value if configured
    String path = pProcess.getArgs2_6().getDiagnosticDataCollectionDirectoryPath();

    if (path == null) {
      // use logpath instead
      path = pProcess.getArgs2_6().getLogPath();
      if (pAgentSupportsDiagPath) {
        path = createDiagnosticPathFromLogPath(path);
      }
    }

    // Earlier versions of AA take parent dir path only and add the diagnostics data directory
    // so we remove the file from the path and send only the directory
    if (!pAgentSupportsDiagPath) {
      final int lastSlashIndex = Math.max(path.lastIndexOf("/"), path.lastIndexOf("\\"));
      if (lastSlashIndex > 0) {
        path = path.substring(0, lastSlashIndex);
      }
    }

    return path;
  }

  String getPointInTimePath(final Process pProcess) {
    final String base = _appSettings.getAutomationDefaultDownloadBase();
    final String processName = pProcess.getName();
    return Paths.get(base, "cps-pit", processName, "pit.log").toString();
  }

  static String createDiagnosticPathFromLogPath(String path) {
    // Strip off extension only, keep filename and add ".diagnostic.data"
    final int cutOffIndex = path.lastIndexOf(".");
    final int lastSlashIndex = Math.max(path.lastIndexOf("/"), path.lastIndexOf("\\"));
    if (cutOffIndex > lastSlashIndex) {
      path = path.substring(0, cutOffIndex);
    }
    path = path.concat(".diagnostic.data");
    return path;
  }

  private LogCollectionJob createLogCollectionJob(
      final ObjectId pParentJobId,
      final Organization pOrg,
      final Group pGroup,
      final AppUser pUser,
      final LogCollectionRequest pLogRequest,
      final LogResourceType pLogResourceType,
      final String pResourceName,
      final List<AgentJobSummary> pAgentJobsSummaries,
      final AutomationConfig pAutomationConfig) {
    final Date currentDate = new Date();

    final LogCollectionJob.LogCollectionJobBuilder builder = LogCollectionJob.builder();

    resolveRootResource(pAutomationConfig, pResourceName, pLogResourceType, builder);

    return builder
        .id(pParentJobId)
        .childrenJobs(pAgentJobsSummaries)
        .groupId(pGroup.getId())
        .userId(pUser.getId())
        .expirationDate(DateUtils.addDays(currentDate, getDefaultExtensionDays()))
        .creationDate(currentDate)
        .status(LogCollectionStatus.IN_PROGRESS)
        .sizeRequestedPerFileBytes(pLogRequest.getSizeInBytes())
        .resourceType(pLogResourceType)
        .resourceName(pResourceName)
        .logCollectionTypes(pLogRequest.getTypes())
        .redacted(pLogRequest.isRedacted())
        .storageBackend(getStorageBackend(pOrg, pGroup))
        .logCollectionFromDate(pLogRequest.getLogCollectionFromDate())
        .logCollectionToDate(pLogRequest.getLogCollectionToDate())
        .build();
  }

  private void resolveRootResource(
      final AutomationConfig pAutomationConfig,
      final String pResourceName,
      final LogResourceType pLogResourceType,
      final LogCollectionJob.LogCollectionJobBuilder pBuilder) {
    String rootResourceName;
    LogResourceType rootResourceType;
    if (pLogResourceType == LogResourceType.CLUSTER) {
      rootResourceName = pResourceName;
      rootResourceType = LogResourceType.CLUSTER;
    } else {
      final Deployment deployment = pAutomationConfig.getDeployment();

      if (pLogResourceType == LogResourceType.REPLICA_SET) {
        /*
        Iterating through all sharded clusters and trying to find selected replicaset in any of
        them. If failed - this means replicaset is a standalone one.
        */
        rootResourceType = LogResourceType.CLUSTER;
        rootResourceName =
            deployment.getSharding().stream()
                .filter(
                    s ->
                        s.getShards().stream()
                                .map(Shard::getReplicaSet)
                                .anyMatch(r -> r.equals(pResourceName))
                            || pResourceName.equals(s.getConfigServerReplica()))
                .findFirst()
                .map(ShardedCluster::getName)
                .orElse(pResourceName);

        if (rootResourceName.equals(pResourceName)) {
          rootResourceType = LogResourceType.REPLICA_SET;
        }
      } else {
        /*
        For process we first check all sharded clusters, then if failed - all replica sets. If
        both failed - this means process is standalone.
        */
        rootResourceType = LogResourceType.CLUSTER;
        rootResourceName =
            deployment.getSharding().stream()
                .filter(
                    s ->
                        deployment.getProcesses(s).stream()
                            .map(Process::getName)
                            .anyMatch(p -> p.equals(pResourceName)))
                .findFirst()
                .map(ShardedCluster::getName)
                .orElse(null);

        if (rootResourceName == null) {
          rootResourceType = LogResourceType.REPLICA_SET;
          rootResourceName =
              deployment.getStandaloneReplicaSets().stream()
                  .filter(
                      r ->
                          deployment.getProcesses(r).stream()
                              .map(Process::getName)
                              .anyMatch(p -> p.equals(pResourceName)))
                  .findFirst()
                  .map(ReplicaSet::getId)
                  .orElse(pResourceName);

          if (rootResourceName.equals(pResourceName)) {
            rootResourceType = LogResourceType.PROCESS;
          }
        }
      }
    }
    pBuilder.rootResourceName(rootResourceName);
    pBuilder.rootResourceType(rootResourceType);
  }

  @FunctionalInterface
  interface LogTraversalVisitor {

    void visit(Process pProcess, LogCollectionType pLogCollectionType);
  }

  /*
  Iterate through processes and log request types and "visit" each eligible combination.
  "Visitor" pattern is caused by need to do different things during this traversal: either build
  agent jobs or build the estimation map. So it has the contract to visit each process only if
  agent job is to be created during this visit.
  */
  private void traverseProcessesAndFindLogs(
      final Group pGroup,
      final Deployment pDeployment,
      final List<Process> pProcesses,
      final Set<String> mongotHostnames,
      final LogCollectionType[] types,
      final LogTraversalVisitor pVisitor) {

    final Set<String> hostnamesForProcesses =
        pProcesses.stream().map(Process::getHostname).collect(toSet());

    /*
     For Mongodbs and FTDC data there is 1:1 mapping from processes to log files paths (so 1:1
     mapping to agent jobs that should be created). E.g. host1:27001 and host1:27002 are
     different processes on the same host and they will log to different log directories.
    */
    if (ArrayUtils.contains(types, FTDC)) {
      for (Process process : pProcesses) {
        if (!process.isMongoSWithSysLogConfiguredAndDiagnosticDataCollectionSetToNull()) {
          pVisitor.visit(process, FTDC);
        }
      }
    }

    visitLogsForMongot(pDeployment, types, pVisitor, mongotHostnames, MONGOT_FTDC);
    visitLogsForMongot(pDeployment, types, pVisitor, mongotHostnames, MONGOT);

    if (ArrayUtils.contains(types, MONGODB)) {
      for (Process process : pProcesses) {
        if (!process.isMongoSWithSysLogConfiguredAndDiagnosticDataCollectionSetToNull()) {
          pVisitor.visit(process, MONGODB);
        }
      }
    }

    /*
     For automation agents picture is different: there can be only one agent of specific type on
     one machine, so we need to "deduplicate" processes by hosts.
    */
    traverseHostsForAgent(types, pVisitor, hostnamesForProcesses.stream(), AUTOMATION_AGENT);
    traverseHostsForAgent(
        types,
        pVisitor,
        Sets.intersection(
            hostnamesForProcesses,
            pDeployment.getCpsModules().stream().map(AgentConfig::getHostname).collect(toSet()))
            .stream(),
        CPS_MODULE);
    traverseHostsForAgent(
        types,
        pVisitor,
        Sets.intersection(
            hostnamesForProcesses,
            pDeployment.getAtlasProxies().stream()
                .map(AtlasProxyConfig::getHostname)
                .collect(toSet()))
            .stream(),
        ATLAS_PROXY);

    traverseHostsForPit(types, pVisitor, pProcesses);

    /*
    Deciding which agent jobs to create for monitoring and backup agents is more complicated:
    On Atlas: clusters can be paused. Skip monitoring / backup agents on paused clusters.
    If running on 4.2 on Atlas: There is one backup agent running locally on each host.
    For backup agents, the hostname of each process can therefore be used.
    Otherwise, the backup and monitoring agents can be anywhere in the deployment and we should
    use any of them.
    */

    final boolean isAtlas = pGroup.isAtlas();
    final boolean is_4_2 =
        pProcesses.stream()
            .map(Process::getVersion)
            .allMatch(
                version -> isGreaterOrEqualTo(version, VersionUtils.FOUR_TWO_ZERO.toString()));

    // Atlas on 4.2: each backup agent is on the same host, so use the hostnames of each process.
    // Otherwise, backup agents can be anywhere in the deployment
    Set<String> backupAgentHostnames =
        (isAtlas && is_4_2)
            ? hostnamesForProcesses
            : pDeployment.getBackupAgentVersions().stream()
                .map(AgentConfig::getHostname)
                .collect(toSet());

    Set<String> monitoringAgentHostnames =
        pDeployment.getMonitoringAgentVersions().stream()
            .map(AgentConfig::getHostname)
            .collect(toSet());

    if (isAtlas) {
      // Filter out any clusters which are paused

      List<ClusterDescription> clusterDescriptions =
          _ndsClusterSvc.getActiveClusterDescriptions(pGroup.getId());

      List<String> pausedClusterNames =
          clusterDescriptions.stream()
              .filter(ClusterDescription::isPaused)
              .map(ClusterDescription::getName)
              .collect(toList());

      Stream<Process> pausedReplicaSetProcesses =
          pausedClusterNames.stream()
              .map(pDeployment::getReplicaSetByName)
              .flatMap(Optional::stream)
              .map(pDeployment::getProcesses)
              .flatMap(Collection::stream);

      Stream<Process> pausedClusterProcesses =
          pausedClusterNames.stream()
              .map(pDeployment::getClusterByName)
              .flatMap(Optional::stream)
              .map(pDeployment::getProcesses)
              .flatMap(Collection::stream);

      Set<String> pausedHostnames =
          Stream.concat(pausedClusterProcesses, pausedReplicaSetProcesses)
              .map(Process::getHostname)
              .collect(toSet());

      backupAgentHostnames =
          backupAgentHostnames.stream()
              .filter(Predicate.not(pausedHostnames::contains))
              .collect(toSet());

      monitoringAgentHostnames =
          monitoringAgentHostnames.stream()
              .filter(Predicate.not(pausedHostnames::contains))
              .collect(toSet());
    }

    traverseHostsForAgent(types, pVisitor, backupAgentHostnames.stream(), BACKUP_AGENT);
    traverseHostsForAgent(types, pVisitor, monitoringAgentHostnames.stream(), MONITORING_AGENT);

    visitLogsForMongotune(pDeployment, types, pVisitor, hostnamesForProcesses, MONGOTUNE);
    visitLogsForMongotune(pDeployment, types, pVisitor, hostnamesForProcesses, MONGOTUNE_FTDC);
  }

  /**
   * Returns a set containing a superset of the hostnames that may contain mongots.
   *
   * <p>For a cluster without dedicated search nodes, it returns the processes that were already
   * selected, as these will be checked later to see if mongot is running on the same host.
   *
   * <p>For a cluster with dedicated search nodes, it returns the hostnames of those hosts.
   */
  private Set<String> getMongotHostnames(
      final Group pGroup, final LogResourceType pLogResourceType, List<Process> processes) {
    Set<String> processHostnames =
        processes.stream().map(Process::getHostname).collect(Collectors.toUnmodifiableSet());

    // Even in a cluster with dedicated search nodes there should be at least one process hostname.
    // If there isn't we won't be able to do anything regardless, so bail out early.
    if (processHostnames.isEmpty()) {
      return processHostnames;
    }

    switch (pLogResourceType) {
        // We don't support collecting from a single dedicated search node for now so this request
        // can't be for a dedicated search node, so return the process hostnames.
      case PROCESS:
        // We don't support sharded clusters yet, so this can't be a request for a cluster with
        // dedicated search nodes, so return the process hostnames.
      case CLUSTER:
        return processHostnames;
    }
    Preconditions.checkArgument(pLogResourceType.equals(LogResourceType.REPLICA_SET));

    final ClusterDescription cluster;
    try {
      cluster =
          _ndsClusterSvc.getClusterDescriptionForHostname(
              processes.get(0).getHostname(), pGroup.getId());
    } catch (SvcException e) {
      // If we're unable to retrieve the cluster for whatever reason, assume that one does not exist
      // for a valid reason, and that it's not possible that we have dedicated search nodes.
      return processHostnames;
    }

    final EnumSet<SearchDeploymentTargets> searchTargets =
        _searchDeploymentSvc.getDeployedSearchTargetsForMongoCluster(cluster.getUniqueId());

    Set<String> mongotHostnames = new HashSet<>();
    if (searchTargets.contains(SearchDeploymentTargets.COUPLED)) {
      // If this is deployment has coupled hosts, append all of the MongoDB hosts as candidates for
      // mongot to be running on.
      mongotHostnames.addAll(processHostnames);
    }

    if (searchTargets.contains(SearchDeploymentTargets.DEDICATED_NODES)) {
      // If this is deployment have dedicated search nodes, append all search nodes host names.
      _searchInstanceSvc
          .findSearchInstancesByClusterUniqueId(pGroup.getId(), cluster.getUniqueId())
          .stream()
          .map(SearchInstance::getInstanceHardware)
          .map(InstanceHardware::getHostnameForAgents)
          .filter(Optional::isPresent)
          .map(Optional::get)
          .forEach(mongotHostnames::add);
    }

    return Collections.unmodifiableSet(mongotHostnames);
  }

  private void visitLogsForMongot(
      final Deployment pDeployment,
      final LogCollectionType[] types,
      final LogTraversalVisitor pVisitor,
      final Set<String> mongotHostnames,
      final LogCollectionType mongotLogType) {
    if (ArrayUtils.contains(types, mongotLogType)) {
      for (MongotConfig mongot : pDeployment.getMongots()) {
        if (!mongotHostnames.contains(mongot.getHostname())) {
          continue;
        }

        final Process process = new Process();
        process.setHostname(mongot.getHostname());
        // The job is specified as processType == "mongod" because our current data model
        // doesn't really support mongot.
        process.setProcessType(ProcessType.MONGOD);
        pVisitor.visit(process, mongotLogType);
      }
    }
  }

  private void visitLogsForMongotune(
      final Deployment pDeployment,
      final LogCollectionType[] types,
      final LogTraversalVisitor pVisitor,
      final Set<String> mongotuneHostnames,
      final LogCollectionType mongotuneLogType) {
    if (ArrayUtils.contains(types, mongotuneLogType)) {
      for (MaintainedMongotuneConfig mongotune : pDeployment.getMaintainedMongotunes()) {
        if (!mongotuneHostnames.contains(mongotune.getHostname())) {
          continue;
        }

        final Process process = new Process();
        process.setHostname(mongotune.getHostname());
        // The job is specified as processType == "mongod" because our current data model
        // doesn't really support mongotune.
        process.setProcessType(ProcessType.MONGOD);
        pVisitor.visit(process, mongotuneLogType);
      }
    }
  }

  private void traverseHostsForAgent(
      final LogCollectionType[] types,
      final LogTraversalVisitor pVisitor,
      final Stream<String> pHosts,
      final LogCollectionType pAgentType) {
    if (ArrayUtils.contains(types, pAgentType)) {
      pHosts
          .distinct()
          .forEach(
              host -> {
                final Process process = new Process();
                process.setHostname(host);
                pVisitor.visit(process, pAgentType);
              });
    }
  }

  void traverseHostsForPit(
      final LogCollectionType[] types,
      final LogTraversalVisitor pVisitor,
      final List<Process> pProcesses) {
    if (ArrayUtils.contains(types, CPS_PIT)) {
      pProcesses.stream()
          .filter(p -> p.getProcessType() == ProcessType.MONGOD)
          .forEach(
              (p -> {
                final Process process = new Process();
                process.setHostname(p.getHostname());
                process.setName(p.getName());
                pVisitor.visit(process, CPS_PIT);
              }));
    }
  }

  private AgentJobSummary createSummary(
      final LogCollectionType pLogCollectionType,
      final LogResourceType pLogResourceType,
      final Process process,
      final AgentJob job) {
    final String hostname =
        (pLogCollectionType == FTDC || pLogCollectionType == MONGODB)
            ? process.getHostnameAndPort()
            : process.getHostname();

    final String path = resolvePath(pLogResourceType, pLogCollectionType, process);

    return AgentJobSummary.builder()
        .automationAgentId(job.getId())
        .hostName(hostname)
        .path(path)
        .startDate(new Date())
        .logCollectionType(pLogCollectionType)
        .status(LogCollectionStatus.IN_PROGRESS)
        .build();
  }

  /*
  This is the path in final artifact for download.
  Rules:
     For Agents
           <hostname>/<type_of_resource>/files...
     For Mongod/FTDC (replicaset/process)
           <hostname>/<port>/<type_of_resource>/files...
     For Mongod/FTDC (cluster)
           <hostname>/<replicaset>/<port>/<type_of_resource>/files...
           <hostname>/mongos/<port>/files...
  */
  private String resolvePath(
      final LogResourceType pLogResourceType,
      final LogCollectionType pCollectionType,
      final Process pProcess) {
    List<String> path = Lists.newArrayList(pProcess.getHostname());
    if (pCollectionType == MONGODB || pCollectionType == FTDC) {
      // If the whole cluster was requested then we add another directory - replica set name
      if (pLogResourceType == LogResourceType.CLUSTER) {
        if (pProcess.isMongos()) {
          path.add("mongos");
        } else {
          path.add(pProcess.getArgs().getReplSetName());
        }
      }
      path.add(String.valueOf(pProcess.getPort()));
    }
    path.add(pCollectionType.getDownloadFolderName());
    return Joiner.on('/').join(path);
  }

  private void retryChildJob(final AgentJobSummary failedAgentJobSummary) throws SvcException {
    final AgentJob existingJob =
        _agentJobsProcessorSvc.findById(failedAgentJobSummary.getAutomationAgentId());

    Preconditions.checkState(
        existingJob != null,
        "Internal error: agent job with id \"%s\" not fond",
        failedAgentJobSummary.getAutomationAgentId());

    final AgentJob clonedJob = new AgentJob(ObjectId.get(), existingJob);

    final AgentJobSummary clonedAgentSummary =
        failedAgentJobSummary.toBuilder()
            .automationAgentId(clonedJob.getId())
            .status(LogCollectionStatus.IN_PROGRESS)
            .startDate(new Date())
            .errorMessage(null)
            .finishDate(null)
            .uncompressedDiskspaceBytes(0)
            .build();

    _agentJobSvc.putJob(clonedJob);

    // logic: remove old record and add the cloned one with updated agent job id
    _agentJobSummaryDao.remove(failedAgentJobSummary.getAutomationAgentId());
    _agentJobSummaryDao.save(clonedAgentSummary);
  }

  /*
  Creates all the tar entries for single job summary. They include the directory where log files
  should reside and all the
  log files produced by matching agent job
  */
  private void streamLogsForSingleJobSummary(
      final Group pGroup,
      final LogCollectionJob pJob,
      final TarArchiveOutputStream pTarOs,
      final AgentJobSummary pJobSummary)
      throws IOException {
    final String rootDirectory = formatFileNameForDownload(pJob);

    /*
     Logic: if the job is in progress then either there is no data in GridFs/s3 or it's being
     currently uploaded. If the job has FAILURE status then there are chances something was
     uploaded anyway so we'll try to download data.
    */
    if (pJobSummary.getStatus() == LogCollectionStatus.IN_PROGRESS) {
      return;
    }

    final ObjectId agentJobId = pJobSummary.getAutomationAgentId();
    final LogCollectionStorageDao logCollectionStorageDao =
        _logCollectionStorageDaoFactory.getLogStorageDao(pJob.getStorageBackend());
    final SortedMap<String, LogCollectionFileMetadata> fileMetadata = pJobSummary.getFileMetadata();

    for (Map.Entry<String, LogCollectionFileMetadata> entry : fileMetadata.entrySet()) {
      final String filename = entry.getKey();
      final TarArchiveEntry archiveEntry =
          new TarArchiveEntry(
              rootDirectory + '/' + pJobSummary.getPath() + '/' + parseFileName(filename),
              TarConstants.LF_NORMAL);

      try (final InputStream inputStream =
              logCollectionStorageDao.read(
                  pJob,
                  agentJobId,
                  getFilenameForStorageBackend(
                      pJob, pGroup.getId(), pJobSummary.getAutomationAgentId(), filename));
          final GZIPInputStream gzipInputStream = new GZIPInputStream(inputStream)) {
        /*
        Unfortunately we cannot use convenient dataStream.getGridFSFile().getLength() as it
        returns compressed size. We have to save size of file before compression to metadata
        for gridFs and read it here.
        */
        final Long uncompressedFileSizeBytes = entry.getValue().getSize();

        /*
        The metadata is always written after the whole file is written. If it doesn't exist
        then this means write to gridFs/s3 started but hasn't finished properly.
        */
        if (uncompressedFileSizeBytes != null) {
          archiveEntry.setSize(uncompressedFileSizeBytes);
          pTarOs.putArchiveEntry(archiveEntry);
          copy(gzipInputStream, pTarOs);
          pTarOs.closeArchiveEntry();
        }
      }
    }
  }

  private void auditForLogRequest(
      final AuditInfo pAuditInfo,
      final LogResourceType pLogResourceType,
      final LogCollectionRequest pLogRequest,
      final String pResourceName,
      final Group pGroup) {
    final LogCollectionRequestAudit.Builder eventBuilder =
        new LogCollectionRequestAudit.Builder(pAuditInfo);
    eventBuilder.logCollectionTypes(pLogRequest.getTypes());
    eventBuilder.sizeRequestedPerFileBytes(pLogRequest.getSizeInBytes());
    eventBuilder.resourceName(pResourceName);
    eventBuilder.resourceType(pLogResourceType);
    if (pLogResourceType != null && pLogResourceType == LogResourceType.CLUSTER) {
      if (pGroup != null && pGroup.getId() != null && !StringUtils.isBlank(pResourceName)) {
        final ClusterDescription clusterDescription =
            _ndsClusterSvc
                .getClusterDescriptionIncludingDeleted(pGroup.getId(), pResourceName)
                .orElse(null);
        if (clusterDescription != null && clusterDescription.getUniqueId() != null) {
          eventBuilder.clusterId(clusterDescription.getUniqueId());
        }
      }
    }
    final boolean isOnPrem = pGroup != null && pGroup.getGroupType() == GroupType.ONPREM;

    eventBuilder.redacted(pLogRequest.isRedacted());
    eventBuilder.hidden(!isOnPrem); // Hide event for all non-on-prem users.
    addEventInformation(eventBuilder, pGroup.getId());

    _auditSvc.saveAuditEvent(eventBuilder.build());
  }

  private void auditForLogDownload(
      final AuditInfo pAuditInfo,
      final LogCollectionJob pJob,
      final List<AgentJobSummary> pAgentJobSummaries,
      final String pUsername,
      final boolean shouldLogAccessRequest,
      final String pReason,
      final Boolean pDataPlaneAccessRequestValidated,
      final boolean pIsOnPrem,
      final AccessTransparencyEventInformation pAccessTransparencyEventInformation) {
    customerLogDownloadEvent(pJob, pAccessTransparencyEventInformation);
    internalLogDownloadEvent(
        pAuditInfo,
        pJob,
        pAgentJobSummaries,
        pUsername,
        shouldLogAccessRequest,
        pReason,
        pDataPlaneAccessRequestValidated,
        pIsOnPrem,
        pAccessTransparencyEventInformation);
  }

  private boolean requiresCustomerFacingAudit(final LogCollectionJob pJob) {
    return !UNRESTRICTED_LOG_COLLECTION_TYPES.containsAll(
        Arrays.asList(pJob.getLogCollectionTypes()));
  }

  private void customerLogDownloadEvent(
      final LogCollectionJob pJob,
      final AccessTransparencyEventInformation pAccessTransparencyEventInformation) {
    if (!requiresCustomerFacingAudit(pJob)) {
      return;
    }

    final AccessAuditEvent.Builder accessEvent =
        new AccessAuditEvent.Builder(Type.EMPLOYEE_DOWNLOADED_CLUSTER_LOGS);

    accessEvent.groupId(pJob.getGroupId());
    accessEvent.auditInfo(null); // Don't expose the employee to the customer.
    accessEvent.hidden(false);

    switch (pJob.getResourceType()) {
      case CLUSTER -> accessEvent.clusterName(pJob.getResourceName());
      case REPLICA_SET -> accessEvent.replicaSetName(pJob.getResourceName());
      case PROCESS -> accessEvent.processName(pJob.getResourceName());
    }

    accessEvent.employeeIdentifier(pAccessTransparencyEventInformation.getEmployeeIdentifier());
    accessEvent.deskLocation(pAccessTransparencyEventInformation.getDeskLocation());

    _auditSvc.saveAuditEvent(accessEvent.build());
  }

  private void internalLogDownloadEvent(
      final AuditInfo pAuditInfo,
      final LogCollectionJob pJob,
      final List<AgentJobSummary> pAgentJobSummaries,
      final String pUsername,
      final boolean shouldLogAccessRequest,
      final String pReason,
      final Boolean pDataPlaneAccessRequestValidated,
      final boolean pIsOnPrem,
      final AccessTransparencyEventInformation pAccessTransparencyEventInformation) {
    final Duration jobDuration = pJob.jobRunTime();
    final LogCollectionType[] agentTypes =
        pAgentJobSummaries.stream()
            .map(AgentJobSummary::getLogCollectionType)
            .distinct()
            .toArray(LogCollectionType[]::new);
    LogCollectionDownloadAudit.Builder builder = new LogCollectionDownloadAudit.Builder(pAuditInfo);
    builder.jobTotalTimeMinutes(jobDuration.toMinutes());
    builder.archiveFileName(formatFileNameForDownload(pJob) + ".tar.gz");

    builder.resourceName(pJob.getResourceName());
    builder.resourceType(pJob.getResourceType());

    if (pJob.getResourceType() != null && pJob.getResourceType() == LogResourceType.CLUSTER) {
      if (pJob.getGroupId() != null && !StringUtils.isBlank(pJob.getResourceName())) {
        final ClusterDescription clusterDescription =
            _ndsClusterSvc
                .getClusterDescriptionIncludingDeleted(pJob.getGroupId(), pJob.getResourceName())
                .orElse(null);
        if (clusterDescription != null && clusterDescription.getUniqueId() != null) {
          builder.clusterId(clusterDescription.getUniqueId());
        }
      }
    }

    builder.sizeRequestedPerFileBytes(pJob.getSizeRequestedPerFileBytes());
    builder.logCollectionTypes(agentTypes);
    if (_appSettings.isReasonsRequireJira()) {
      builder.supportTicket(pReason);
      builder.dataPlaneAccessRequestValidated(pDataPlaneAccessRequestValidated);
    }
    addEventInformation(builder, pJob.getGroupId());
    builder.hidden(!pIsOnPrem); // Hide event for all non-on-prem users.

    if (requiresCustomerFacingAudit(pJob)) {
      builder.employeeIdentifier(pAccessTransparencyEventInformation.getEmployeeIdentifier());
      builder.deskLocation(pAccessTransparencyEventInformation.getDeskLocation());
    }

    final LogCollectionDownloadAudit auditEvent = builder.build();
    _auditSvc.saveAuditEvent(auditEvent);
    _informationalAlertSvc.enqueueEvent(auditEvent);

    if (shouldLogAccessRequest && _appSettings.isReasonsRequireJira()) {
      _dataPlaneAccessRequestSvc.submitLogDownloadAccessRequest(
          ObjectId.get(),
          Arrays.stream(agentTypes).map(LogCollectionType::toString).toList(),
          pAgentJobSummaries.stream().map(AgentJobSummary::getHostName).distinct().toList(),
          new Date(),
          pUsername,
          pReason,
          pDataPlaneAccessRequestValidated);
    }
  }

  private void addEventInformation(final Event.Builder pBuilder, final ObjectId pGroupId) {
    pBuilder.groupId(pGroupId);
  }

  /*
  Tries to guess the filename by file path if this path is absolute. This is needed as agents
  send full path to files instead of just file names when they upload log files.
  */
  static String parseFileName(final String pFilePath) {
    return FilenameUtils.getName(pFilePath);
  }

  private List<AgentJobSummary> sortedSummaries(final LogCollectionJob pJob) {
    return pJob.getChildrenJobs().stream()
        .sorted(Comparator.comparing(AgentJobSummary::getPath))
        .collect(toList());
  }

  public int getDefaultExtensionDays() {
    return _appSettings.getIntProp(DEFAULT_EXTENTION_DAYS_PROP_NAME, 30);
  }
}
