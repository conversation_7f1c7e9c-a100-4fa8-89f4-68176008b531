package com.xgen.svc.cukes.helpers.atm;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.atm.core._public.view.ImportDeploymentRequestView;
import com.xgen.cloud.deployment._public.model.AuthMechanism;
import com.xgen.svc.cukes.net.PublicAPIClient;
import io.cucumber.guice.ScenarioScoped;
import jakarta.inject.Inject;
import java.util.List;
import java.util.function.Consumer;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ScenarioScoped
public class AutomationImportApiClient {
  private static final Logger LOG = LoggerFactory.getLogger(AutomationImportApiClient.class);

  private final PublicAPIClient publicAPIClient;

  @Inject
  public AutomationImportApiClient(final PublicAPIClient publicAPIClient) {
    this.publicAPIClient = publicAPIClient;
  }

  public String createImportDeploymentRequest(
      final String groupId,
      final String seedHostname,
      final int port,
      final String username,
      final String password,
      final List<String> requiredProcesses,
      final AuthMechanism authMechanism,
      final Consumer<ImportDeploymentRequestView> mutator) {
    ImportDeploymentRequestView rq = new ImportDeploymentRequestView();
    rq.setSeedHostport(seedHostname + ":" + port);
    rq.setRequiredProcesses(requiredProcesses);

    rq.setAuthMechanism(authMechanism);
    if (username != null && password != null) {
      rq.setAdminUsername(username);
      rq.setAdminPassword(password);
      rq.setAdminDb("admin");
    }

    // Apply additional settings to the request payload
    mutator.accept(rq);

    ObjectMapper mapper = new ObjectMapper();
    try {
      JSONObject response =
          publicAPIClient.post(
              "/api/public/v1.0/automation/importDeployment/" + groupId,
              mapper.writeValueAsString(rq));
      LOG.info("Import deployment request created: {}", response);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }

    JSONObject requests =
        publicAPIClient.get("/api/public/v1.0/automation/importDeployment/" + groupId);

    return requests.getJSONArray("results").getJSONObject(0).getString("id");
  }

  public JSONObject getImportDeploymentRequestStatus(String groupId, String requestId) {
    return publicAPIClient.get(
        "/api/public/v1.0/automation/importDeployment/" + groupId + "/" + requestId);
  }

  public void waitForImportCompletion(String groupId, String requestId, int timeoutSeconds)
      throws Exception {
    LOG.info("Waiting for import to complete for requestId: {}", requestId);

    long startTime = System.currentTimeMillis();
    long endTime = startTime + (timeoutSeconds * 1000L);

    while (System.currentTimeMillis() < endTime) {
      JSONObject status = getImportDeploymentRequestStatus(groupId, requestId);
      String state = status.getString("state");

      LOG.info("Import state: {}", state);

      if ("SUCCESS".equals(state)) {
        LOG.info("Import completed successfully");
        return;
      }

      if ("FAILED".equals(state)) {
        String error = status.has("error") ? status.getString("error") : "Unknown error";
        LOG.error("Import failed: {}", status);
        throw new Exception("Import failed: " + error);
      }

      Thread.sleep(3000);
    }

    throw new Exception("Import timed out after " + timeoutSeconds + " seconds");
  }
}
