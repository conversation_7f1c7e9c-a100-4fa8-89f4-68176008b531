create_cluster_response[2023-01-01]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "autoScaling": {
      "compute": {
        "enabled": false,
        "predictiveEnabled": false,
        "scaleDownEnabled": false
      },
      "diskGBEnabled": true
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": { },
    "createDate": "ignored",
    "diskSizeGB": 10,
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "mongoURIUpdated": "ignored",
    "name": "api-test-cluster-2023-01-01",
    "numShards": 1,
    "paused": false,
    "pitEnabled": false,
    "providerBackupEnabled": false,
    "providerSettings": {
      "autoScaling": {
        "compute": { }
      },
      "diskIOPS": 3000,
      "encryptEBSVolume": true,
      "instanceSizeName": "M10",
      "providerName": "AWS",
      "regionName": "US_EAST_1",
      "volumeType": "STANDARD"
    },
    "replicationFactor": 3,
    "replicationSpec": {
      "US_EAST_1": {
        "analyticsNodes": 0,
        "electableNodes": 3,
        "priority": 7,
        "readOnlyNodes": 0
      }
    },
    "replicationSpecs": [
      {
        "id": "ignored",
        "numShards": 1,
        "regionsConfig": {
          "US_EAST_1": {
            "analyticsNodes": 0,
            "electableNodes": 3,
            "priority": 7,
            "readOnlyNodes": 0
          }
        },
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "CREATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


create_cluster_response[2023-02-01]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": { },
    "createDate": "ignored",
    "diskSizeGB": 10,
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2023-02-01",
    "paused": false,
    "pitEnabled": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "numShards": 1,
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "CREATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


create_cluster_response[2024-08-05]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": { },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "CREATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


create_cluster_with_log_redaction_response[2024-08-05_false]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": { },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "CREATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


create_cluster_with_log_redaction_response[2024-08-05_null]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": { },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "CREATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


create_cluster_with_log_redaction_response[2024-08-05_true]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": { },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": true,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "CREATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


list_cluster_response[2023-01-01]=[
  {
    "links": "ignored",
    "results": [
      {
        "advancedConfiguration": {
          "customOpensslCipherConfigTls12": [ ],
          "minimumEnabledTlsProtocol": "TLS1_2",
          "tlsCipherConfigMode": "DEFAULT"
        },
        "autoScaling": {
          "compute": {
            "enabled": false,
            "predictiveEnabled": false,
            "scaleDownEnabled": false
          },
          "diskGBEnabled": true
        },
        "backupEnabled": false,
        "biConnector": {
          "enabled": false,
          "readPreference": "secondary"
        },
        "clusterType": "REPLICASET",
        "connectionStrings": {
          "standard": "ignored",
          "standardSrv": "ignored"
        },
        "createDate": "ignored",
        "diskSizeGB": 10,
        "diskWarmingMode": "FULLY_WARMED",
        "encryptionAtRestProvider": "NONE",
        "globalClusterSelfManagedSharding": false,
        "groupId": "ignored",
        "id": "ignored",
        "labels": [ ],
        "links": "ignored",
        "mongoDBMajorVersion": "7.0",
        "mongoDBVersion": "ignored",
        "mongoURI": "ignored",
        "mongoURIUpdated": "ignored",
        "mongoURIWithOptions": "ignored",
        "name": "api-test-cluster-2023-01-01",
        "numShards": 1,
        "paused": false,
        "pitEnabled": false,
        "providerBackupEnabled": false,
        "providerSettings": {
          "autoScaling": {
            "compute": { }
          },
          "diskIOPS": 3000,
          "encryptEBSVolume": true,
          "instanceSizeName": "M10",
          "providerName": "AWS",
          "regionName": "US_EAST_1",
          "volumeType": "STANDARD"
        },
        "replicationFactor": 3,
        "replicationSpec": {
          "US_EAST_1": {
            "analyticsNodes": 0,
            "electableNodes": 3,
            "priority": 7,
            "readOnlyNodes": 0
          }
        },
        "replicationSpecs": [
          {
            "id": "ignored",
            "numShards": 1,
            "regionsConfig": {
              "US_EAST_1": {
                "analyticsNodes": 0,
                "electableNodes": 3,
                "priority": 7,
                "readOnlyNodes": 0
              }
            },
            "zoneName": "Zone 1"
          }
        ],
        "rootCertType": "ISRGROOTX1",
        "srvAddress": "ignored",
        "stateName": "IDLE",
        "terminationProtectionEnabled": false,
        "versionReleaseSystem": "LTS"
      }
    ],
    "totalCount": 1
  }
]


list_cluster_response[2023-02-01]=[
  {
    "links": "ignored",
    "results": [
      {
        "advancedConfiguration": {
          "customOpensslCipherConfigTls12": [ ],
          "minimumEnabledTlsProtocol": "TLS1_2",
          "tlsCipherConfigMode": "DEFAULT"
        },
        "backupEnabled": false,
        "biConnector": {
          "enabled": false,
          "readPreference": "secondary"
        },
        "clusterType": "REPLICASET",
        "connectionStrings": {
          "standard": "ignored",
          "standardSrv": "ignored"
        },
        "createDate": "ignored",
        "diskSizeGB": 10,
        "diskWarmingMode": "FULLY_WARMED",
        "encryptionAtRestProvider": "NONE",
        "globalClusterSelfManagedSharding": false,
        "groupId": "ignored",
        "id": "ignored",
        "labels": [ ],
        "links": "ignored",
        "mongoDBMajorVersion": "7.0",
        "mongoDBVersion": "ignored",
        "name": "api-test-cluster-2023-02-01",
        "paused": false,
        "pitEnabled": false,
        "replicationSpecs": [
          {
            "id": "ignored",
            "numShards": 1,
            "regionConfigs": [
              {
                "analyticsSpecs": {
                  "diskIOPS": 3000,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "autoScaling": {
                  "compute": {
                    "enabled": false,
                    "predictiveEnabled": false,
                    "scaleDownEnabled": false
                  },
                  "diskGB": {
                    "enabled": false
                  }
                },
                "electableSpecs": {
                  "diskIOPS": 3000,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 3
                },
                "priority": 7,
                "providerName": "AWS",
                "readOnlySpecs": {
                  "diskIOPS": 3000,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "regionName": "US_EAST_1"
              }
            ],
            "zoneId": "ignored",
            "zoneName": "Zone 1"
          }
        ],
        "rootCertType": "ISRGROOTX1",
        "stateName": "IDLE",
        "terminationProtectionEnabled": false,
        "versionReleaseSystem": "LTS"
      }
    ],
    "totalCount": 1
  }
]


list_cluster_response[2024-08-05]=[
  {
    "links": "ignored",
    "results": [
      {
        "advancedConfiguration": {
          "customOpensslCipherConfigTls12": [ ],
          "minimumEnabledTlsProtocol": "TLS1_2",
          "tlsCipherConfigMode": "DEFAULT"
        },
        "backupEnabled": false,
        "biConnector": {
          "enabled": false,
          "readPreference": "secondary"
        },
        "clusterType": "REPLICASET",
        "connectionStrings": {
          "standard": "ignored",
          "standardSrv": "ignored"
        },
        "createDate": "ignored",
        "diskWarmingMode": "FULLY_WARMED",
        "encryptionAtRestProvider": "NONE",
        "featureCompatibilityVersion": "7.0",
        "globalClusterSelfManagedSharding": false,
        "groupId": "ignored",
        "id": "ignored",
        "internalClusterRole": "NONE",
        "labels": [ ],
        "links": "ignored",
        "mongoDBMajorVersion": "7.0",
        "mongoDBVersion": "ignored",
        "name": "api-test-cluster-2024-08-05",
        "paused": false,
        "pitEnabled": false,
        "redactClientLogData": false,
        "replicationSpecs": [
          {
            "id": "ignored",
            "regionConfigs": [
              {
                "analyticsSpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "autoScaling": {
                  "compute": {
                    "enabled": false,
                    "predictiveEnabled": false,
                    "scaleDownEnabled": false
                  },
                  "diskGB": {
                    "enabled": false
                  }
                },
                "electableSpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 3
                },
                "priority": 7,
                "providerName": "AWS",
                "readOnlySpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "regionName": "US_EAST_1"
              }
            ],
            "zoneId": "ignored",
            "zoneName": "Zone 1"
          }
        ],
        "rootCertType": "ISRGROOTX1",
        "stateName": "IDLE",
        "terminationProtectionEnabled": false,
        "versionReleaseSystem": "LTS"
      }
    ],
    "totalCount": 1
  }
]


list_cluster_with_log_redaction_response[2024-08-05_false]=[
  {
    "links": "ignored",
    "results": [
      {
        "advancedConfiguration": {
          "customOpensslCipherConfigTls12": [ ],
          "minimumEnabledTlsProtocol": "TLS1_2",
          "tlsCipherConfigMode": "DEFAULT"
        },
        "backupEnabled": false,
        "biConnector": {
          "enabled": false,
          "readPreference": "secondary"
        },
        "clusterType": "REPLICASET",
        "connectionStrings": {
          "standard": "ignored",
          "standardSrv": "ignored"
        },
        "createDate": "ignored",
        "diskWarmingMode": "FULLY_WARMED",
        "encryptionAtRestProvider": "NONE",
        "featureCompatibilityVersion": "7.0",
        "globalClusterSelfManagedSharding": false,
        "groupId": "ignored",
        "id": "ignored",
        "internalClusterRole": "NONE",
        "labels": [ ],
        "links": "ignored",
        "mongoDBMajorVersion": "7.0",
        "mongoDBVersion": "ignored",
        "name": "api-test-cluster-2024-08-05",
        "paused": false,
        "pitEnabled": false,
        "redactClientLogData": false,
        "replicationSpecs": [
          {
            "id": "ignored",
            "regionConfigs": [
              {
                "analyticsSpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "autoScaling": {
                  "compute": {
                    "enabled": false,
                    "predictiveEnabled": false,
                    "scaleDownEnabled": false
                  },
                  "diskGB": {
                    "enabled": false
                  }
                },
                "electableSpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 3
                },
                "priority": 7,
                "providerName": "AWS",
                "readOnlySpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "regionName": "US_EAST_1"
              }
            ],
            "zoneId": "ignored",
            "zoneName": "Zone 1"
          }
        ],
        "rootCertType": "ISRGROOTX1",
        "stateName": "IDLE",
        "terminationProtectionEnabled": false,
        "versionReleaseSystem": "LTS"
      }
    ],
    "totalCount": 1
  }
]


list_cluster_with_log_redaction_response[2024-08-05_null]=[
  {
    "links": "ignored",
    "results": [
      {
        "advancedConfiguration": {
          "customOpensslCipherConfigTls12": [ ],
          "minimumEnabledTlsProtocol": "TLS1_2",
          "tlsCipherConfigMode": "DEFAULT"
        },
        "backupEnabled": false,
        "biConnector": {
          "enabled": false,
          "readPreference": "secondary"
        },
        "clusterType": "REPLICASET",
        "connectionStrings": {
          "standard": "ignored",
          "standardSrv": "ignored"
        },
        "createDate": "ignored",
        "diskWarmingMode": "FULLY_WARMED",
        "encryptionAtRestProvider": "NONE",
        "featureCompatibilityVersion": "7.0",
        "globalClusterSelfManagedSharding": false,
        "groupId": "ignored",
        "id": "ignored",
        "internalClusterRole": "NONE",
        "labels": [ ],
        "links": "ignored",
        "mongoDBMajorVersion": "7.0",
        "mongoDBVersion": "ignored",
        "name": "api-test-cluster-2024-08-05",
        "paused": false,
        "pitEnabled": false,
        "redactClientLogData": false,
        "replicationSpecs": [
          {
            "id": "ignored",
            "regionConfigs": [
              {
                "analyticsSpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "autoScaling": {
                  "compute": {
                    "enabled": false,
                    "predictiveEnabled": false,
                    "scaleDownEnabled": false
                  },
                  "diskGB": {
                    "enabled": false
                  }
                },
                "electableSpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 3
                },
                "priority": 7,
                "providerName": "AWS",
                "readOnlySpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "regionName": "US_EAST_1"
              }
            ],
            "zoneId": "ignored",
            "zoneName": "Zone 1"
          }
        ],
        "rootCertType": "ISRGROOTX1",
        "stateName": "IDLE",
        "terminationProtectionEnabled": false,
        "versionReleaseSystem": "LTS"
      }
    ],
    "totalCount": 1
  }
]


list_cluster_with_log_redaction_response[2024-08-05_true]=[
  {
    "links": "ignored",
    "results": [
      {
        "advancedConfiguration": {
          "customOpensslCipherConfigTls12": [ ],
          "minimumEnabledTlsProtocol": "TLS1_2",
          "tlsCipherConfigMode": "DEFAULT"
        },
        "backupEnabled": false,
        "biConnector": {
          "enabled": false,
          "readPreference": "secondary"
        },
        "clusterType": "REPLICASET",
        "connectionStrings": {
          "standard": "ignored",
          "standardSrv": "ignored"
        },
        "createDate": "ignored",
        "diskWarmingMode": "FULLY_WARMED",
        "encryptionAtRestProvider": "NONE",
        "featureCompatibilityVersion": "7.0",
        "globalClusterSelfManagedSharding": false,
        "groupId": "ignored",
        "id": "ignored",
        "internalClusterRole": "NONE",
        "labels": [ ],
        "links": "ignored",
        "mongoDBMajorVersion": "7.0",
        "mongoDBVersion": "ignored",
        "name": "api-test-cluster-2024-08-05",
        "paused": false,
        "pitEnabled": false,
        "redactClientLogData": true,
        "replicationSpecs": [
          {
            "id": "ignored",
            "regionConfigs": [
              {
                "analyticsSpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "autoScaling": {
                  "compute": {
                    "enabled": false,
                    "predictiveEnabled": false,
                    "scaleDownEnabled": false
                  },
                  "diskGB": {
                    "enabled": false
                  }
                },
                "electableSpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 3
                },
                "priority": 7,
                "providerName": "AWS",
                "readOnlySpecs": {
                  "diskIOPS": 3000,
                  "diskSizeGB": 10,
                  "ebsVolumeType": "STANDARD",
                  "instanceSize": "M10",
                  "nodeCount": 0
                },
                "regionName": "US_EAST_1"
              }
            ],
            "zoneId": "ignored",
            "zoneName": "Zone 1"
          }
        ],
        "rootCertType": "ISRGROOTX1",
        "stateName": "IDLE",
        "terminationProtectionEnabled": false,
        "versionReleaseSystem": "LTS"
      }
    ],
    "totalCount": 1
  }
]


read_cluster_response_after_access_grant[2024-08-05]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBEmployeeAccessGrant": {
      "expirationTime": "2050-01-01T00:00:00Z",
      "grantType": "CLUSTER_INFRASTRUCTURE"
    },
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "IDLE",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


read_cluster_response_after_access_grant_revoked[2024-08-05]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "IDLE",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


read_created_cluster_response[2023-01-01]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "autoScaling": {
      "compute": {
        "enabled": false,
        "predictiveEnabled": false,
        "scaleDownEnabled": false
      },
      "diskGBEnabled": true
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskSizeGB": 10,
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "mongoURI": "ignored",
    "mongoURIUpdated": "ignored",
    "mongoURIWithOptions": "ignored",
    "name": "api-test-cluster-2023-01-01",
    "numShards": 1,
    "paused": false,
    "pitEnabled": false,
    "providerBackupEnabled": false,
    "providerSettings": {
      "autoScaling": {
        "compute": { }
      },
      "diskIOPS": 3000,
      "encryptEBSVolume": true,
      "instanceSizeName": "M10",
      "providerName": "AWS",
      "regionName": "US_EAST_1",
      "volumeType": "STANDARD"
    },
    "replicationFactor": 3,
    "replicationSpec": {
      "US_EAST_1": {
        "analyticsNodes": 0,
        "electableNodes": 3,
        "priority": 7,
        "readOnlyNodes": 0
      }
    },
    "replicationSpecs": [
      {
        "id": "ignored",
        "numShards": 1,
        "regionsConfig": {
          "US_EAST_1": {
            "analyticsNodes": 0,
            "electableNodes": 3,
            "priority": 7,
            "readOnlyNodes": 0
          }
        },
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "srvAddress": "ignored",
    "stateName": "IDLE",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


read_created_cluster_response[2023-02-01]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskSizeGB": 10,
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2023-02-01",
    "paused": false,
    "pitEnabled": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "numShards": 1,
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "IDLE",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


read_created_cluster_response[2024-08-05]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "IDLE",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


read_updated_cluster_response[2023-01-01]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "autoScaling": {
      "compute": {
        "enabled": false,
        "predictiveEnabled": false,
        "scaleDownEnabled": false
      },
      "diskGBEnabled": true
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskSizeGB": 10,
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "mongoURI": "ignored",
    "mongoURIUpdated": "ignored",
    "mongoURIWithOptions": "ignored",
    "name": "api-test-cluster-2023-01-01",
    "numShards": 1,
    "paused": false,
    "pitEnabled": false,
    "providerBackupEnabled": false,
    "providerSettings": {
      "autoScaling": {
        "compute": { }
      },
      "diskIOPS": 3000,
      "encryptEBSVolume": true,
      "instanceSizeName": "M20",
      "providerName": "AWS",
      "regionName": "US_EAST_1",
      "volumeType": "STANDARD"
    },
    "replicationFactor": 3,
    "replicationSpec": {
      "US_EAST_1": {
        "analyticsNodes": 0,
        "electableNodes": 3,
        "priority": 7,
        "readOnlyNodes": 0
      }
    },
    "replicationSpecs": [
      {
        "id": "ignored",
        "numShards": 1,
        "regionsConfig": {
          "US_EAST_1": {
            "analyticsNodes": 0,
            "electableNodes": 3,
            "priority": 7,
            "readOnlyNodes": 0
          }
        },
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "srvAddress": "ignored",
    "stateName": "IDLE",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


read_updated_cluster_response[2023-02-01]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskSizeGB": 10,
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2023-02-01",
    "paused": false,
    "pitEnabled": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "numShards": 1,
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "IDLE",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


read_updated_cluster_response[2024-08-05]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "IDLE",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


update_cluster_response[2023-01-01]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "autoScaling": {
      "compute": {
        "enabled": false,
        "predictiveEnabled": false,
        "scaleDownEnabled": false
      },
      "diskGBEnabled": true
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskSizeGB": 10,
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "mongoURI": "ignored",
    "mongoURIUpdated": "ignored",
    "mongoURIWithOptions": "ignored",
    "name": "api-test-cluster-2023-01-01",
    "numShards": 1,
    "paused": false,
    "pitEnabled": false,
    "providerBackupEnabled": false,
    "providerSettings": {
      "autoScaling": {
        "compute": { }
      },
      "diskIOPS": 3000,
      "encryptEBSVolume": true,
      "instanceSizeName": "M20",
      "providerName": "AWS",
      "regionName": "US_EAST_1",
      "volumeType": "STANDARD"
    },
    "replicationFactor": 3,
    "replicationSpec": {
      "US_EAST_1": {
        "analyticsNodes": 0,
        "electableNodes": 3,
        "priority": 7,
        "readOnlyNodes": 0
      }
    },
    "replicationSpecs": [
      {
        "id": "ignored",
        "numShards": 1,
        "regionsConfig": {
          "US_EAST_1": {
            "analyticsNodes": 0,
            "electableNodes": 3,
            "priority": 7,
            "readOnlyNodes": 0
          }
        },
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "srvAddress": "ignored",
    "stateName": "UPDATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


update_cluster_response[2023-02-01]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskSizeGB": 10,
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2023-02-01",
    "paused": false,
    "pitEnabled": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "numShards": 1,
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "UPDATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


update_cluster_response[2024-08-05]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M20",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "UPDATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


update_cluster_with_log_redaction_response[2024-08-05_false]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": false,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "UPDATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]


update_cluster_with_log_redaction_response[2024-08-05_null]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "diskWarmingMode": "FULLY_WARMED",
    "paused": false,
    "pitEnabled": false,
    "groupId": "ignored",
    "redactClientLogData": false,
    "labels": [ ],
    "replicationSpecs": [
      {
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "instanceSize": "M10",
              "diskIOPS": 3000,
              "nodeCount": 0,
              "ebsVolumeType": "STANDARD",
              "diskSizeGB": 10.0
            },
            "autoScaling": {
              "compute": {
                "scaleDownEnabled": false,
                "predictiveEnabled": false,
                "enabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "regionName": "US_EAST_1",
            "electableSpecs": {
              "instanceSize": "120",
              "diskIOPS": 3000,
              "nodeCount": 3,
              "ebsVolumeType": "STANDARD",
              "diskSizeGB": 10.0
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "instanceSize": "M10",
              "diskIOPS": 3000,
              "nodeCount": 0,
              "ebsVolumeType": "STANDARD",
              "diskSizeGB": 10.0
            }
          }
        ],
        "zoneId": "ignored",
        "id": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "backupEnabled": false,
    "biConnector": {
      "readPreference": "secondary",
      "enabled": false
    },
    "globalClusterSelfManagedSharding": false,
    "terminationProtectionEnabled": false,
    "stateName": "UPDATING",
    "rootCertType": "ISRGROOTX1",
    "versionReleaseSystem": "LTS",
    "name": "api-test-cluster-2024-08-05",
    "id": "ignored",
    "encryptionAtRestProvider": "NONE",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "createDate": "ignored",
    "links": "ignored"
  }
]


update_cluster_with_log_redaction_response[2024-08-05_true]=[
  {
    "advancedConfiguration": {
      "customOpensslCipherConfigTls12": [ ],
      "minimumEnabledTlsProtocol": "TLS1_2",
      "tlsCipherConfigMode": "DEFAULT"
    },
    "backupEnabled": false,
    "biConnector": {
      "enabled": false,
      "readPreference": "secondary"
    },
    "clusterType": "REPLICASET",
    "connectionStrings": {
      "standard": "ignored",
      "standardSrv": "ignored"
    },
    "createDate": "ignored",
    "diskWarmingMode": "FULLY_WARMED",
    "encryptionAtRestProvider": "NONE",
    "featureCompatibilityVersion": "7.0",
    "globalClusterSelfManagedSharding": false,
    "groupId": "ignored",
    "id": "ignored",
    "internalClusterRole": "NONE",
    "labels": [ ],
    "links": "ignored",
    "mongoDBMajorVersion": "7.0",
    "mongoDBVersion": "ignored",
    "name": "api-test-cluster-2024-08-05",
    "paused": false,
    "pitEnabled": false,
    "redactClientLogData": true,
    "replicationSpecs": [
      {
        "id": "ignored",
        "regionConfigs": [
          {
            "analyticsSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "autoScaling": {
              "compute": {
                "enabled": false,
                "predictiveEnabled": false,
                "scaleDownEnabled": false
              },
              "diskGB": {
                "enabled": false
              }
            },
            "electableSpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 3
            },
            "priority": 7,
            "providerName": "AWS",
            "readOnlySpecs": {
              "diskIOPS": 3000,
              "diskSizeGB": 10,
              "ebsVolumeType": "STANDARD",
              "instanceSize": "M10",
              "nodeCount": 0
            },
            "regionName": "US_EAST_1"
          }
        ],
        "zoneId": "ignored",
        "zoneName": "Zone 1"
      }
    ],
    "rootCertType": "ISRGROOTX1",
    "stateName": "UPDATING",
    "terminationProtectionEnabled": false,
    "versionReleaseSystem": "LTS"
  }
]
