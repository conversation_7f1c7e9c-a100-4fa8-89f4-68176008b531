@cm @deployment @importforautomation
Feature: Import for Automation using API

  Background:
    Given I register and login a new user via the API
    And I create a new CLOUD group via the API
    And I create a new API Key with the following properties:
      | description | API Key to Edit |
      | orgRole     | ORG_OWNER       |
    And I install an automation agent on the local server with hostname local1.10gen.cc
    And I install a monitoring agent on hostname local1.10gen.cc through automation

  Scenario: Import a standalone into automation using API
    Given I create a deployment with the following properties:
      | Type       | Version | Hostname        |
      | standalone | 8.0     | local1.10gen.cc |
    And I publish and wait for goal state
    And I see monitoring data for the process
    Then I can import the standalone using the API to be managed by automation into a new group

  Scenario: Import a replica set into automation using API
    Given I create a deployment with the following properties:
      | Type        | Version | Hostname        |
      | replica set | 8.0     | local1.10gen.cc |
    And I publish and wait for goal state
    And I see monitoring data for the process
    Then I can import the replica set using the API to be managed by automation into a new group

  @sanity
  Scenario: Import a cluster with auth into automation using API
    Given I create a deployment with the following properties:
      | Type            | Version | Hostname        |
      | sharded cluster | 8.0     | local1.10gen.cc |
    And I enable MONGODB-CR auth for the deployment
    And I add test users to my deployment
    And I add a custom role to one user in my deployment
    And I see monitoring data for the process
    Then I can import the cluster with auth using the API to be managed by automation into a new group
