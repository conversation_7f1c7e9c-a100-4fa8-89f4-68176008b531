package com.xgen.cloud.atm.deploymentimport._public.svc;

import static com.xgen.cloud.common.util._public.util.DriverUtils.reverseObjectId;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.atm.core._public.model.AgentAudit;
import com.xgen.cloud.atm.core._public.model.ImportDeployment;
import com.xgen.cloud.atm.core._public.model.LastAgentStatus;
import com.xgen.cloud.atm.core._public.model.status.EnhancedProcessStatus;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.atm.core._public.svc.LastAgentStatusSvc;
import com.xgen.cloud.atm.core._public.view.ImportDeploymentRequestView;
import com.xgen.cloud.atm.deploymentimport._public.model.AuthResponse;
import com.xgen.cloud.atm.deploymentimport._public.view.GetProcessConfigJobResponseView;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.agentaudit._public.dao.MonitoringAgentAuditDao;
import com.xgen.cloud.common.jobqueue._private.dao.AgentJobsProcessorDao;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.system._public.model.PlatformConstants;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.deployment._public.model.AuthMechanism;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.MongoDbBuild;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6;
import com.xgen.cloud.deployment._public.model.ProcessType;
import com.xgen.cloud.deployment._public.model.ValidationException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.topology._private.dao.HostDao;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostUpdateRequest;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.io.IOException;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ImportDeploymentSvcIntTests extends JUnit5BaseSvcTest {
  private static final Logger LOG = LoggerFactory.getLogger(ImportDeploymentSvcIntTests.class);

  @Inject private ImportDeploymentSvc importDeploymentSvc;

  @Inject private HostDao hostDao;

  @Inject private HostClusterLifecycleSvc hostClusterLifecycleSvc;

  @Inject private GetProcessConfigJob getProcessConfigJob;

  @Inject private AgentJobsProcessorDao agentJobsProcessorDao;

  @Inject private ImportRequestSvc importRequestSvc;

  @Inject private LastAgentStatusSvc lastAgentStatusSvc;

  @Inject private AutomationConfigPublishingSvc automationConfigSvc;

  @Inject private AutomationMongoDbVersionSvc _versionSvc;

  private Organization organization;
  private Group group;
  private AppUser user;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    organization = MmsFactory.createOrganizationWithStandardPlan();
    group = MmsFactory.createGroup(organization);
    user = MmsFactory.createUser(group);

    _versionSvc.invalidateVersionManifestCache();
    _versionSvc.autoUpdateDefaultVersions();
  }

  @Test
  public void submitRequestForItem_ValidationErrorThrowsScvError() throws Exception {
    final ImportDeploymentRequestView request = new ImportDeploymentRequestView();

    SvcException svcException =
        assertThrows(
            SvcException.class,
            () -> importDeploymentSvc.submitImportDeploymentRequest(group, user, request));
    assertEquals("INVALID_PARAMETER", svcException.getMessage());
    assertEquals("Missing or malformed seedHostport", svcException.getMessageParams().get(0));
  }

  @Test
  public void endToEndFlow_happyPath() throws Exception {
    final ImportDeploymentRequestView request = new ImportDeploymentRequestView();
    request.setAuthMechanism(AuthMechanism.GSSAPI);
    request.setRequiredProcesses(List.of("hostname:1234"));
    request.setSeedHostport("hostname:1234");
    request.setTimeouts(new ImportDeploymentRequestView.TimeoutsView());

    injectMonitoringData(group, "host");

    importDeploymentSvc.submitImportDeploymentRequest(group, user, request);

    {
      List<ImportDeployment> requests = importDeploymentSvc.getRequests(group.getId());
      assertEquals(1, requests.size());
      assertEquals(ImportDeployment.State.PENDING, requests.get(0).state());
      assertNull(requests.get(0).hostId());
    }

    // One step forward
    importDeploymentSvc.handleWork(group.getId());

    String hostId;

    // Test the host monitoring ID is set
    // Verify we are in state "WAITING_smth"
    {
      List<ImportDeployment> requests = importDeploymentSvc.getRequests(group.getId());
      assertEquals(1, requests.size());
      assertEquals(ImportDeployment.State.AWAIT_SEED_HOST_CONNECTED, requests.get(0).state());
      assertNotNull(requests.get(0).hostId());
      hostId = requests.get(0).hostId();
    }

    // One step forward. Shouldn't change the state
    importDeploymentSvc.handleWork(group.getId());

    {
      List<ImportDeployment> requests = importDeploymentSvc.getRequests(group.getId());
      assertEquals(1, requests.size());
      assertEquals(ImportDeployment.State.AWAIT_SEED_HOST_CONNECTED, requests.get(0).state());
    }

    Host byId = hostDao.findById(hostId, group.getId());

    // Host gets the ping
    final HostUpdateRequest hostUpdateRequest =
        new HostUpdateRequest(
            group.getId(), byId.getName(), byId.getPort(), Calendar.getInstance());
    hostUpdateRequest.shouldSetLastMongosPing();
    hostUpdateRequest.shouldSetVersion();
    hostUpdateRequest.setVersion("8.0.1");
    hostClusterLifecycleSvc.updateHost(hostUpdateRequest);

    // One step forward. Should change the state to AWAIT_HOSTS_DISCOVERED
    importDeploymentSvc.handleWork(group.getId());

    {
      List<ImportDeployment> requests = importDeploymentSvc.getRequests(group.getId());
      assertEquals(1, requests.size());
      assertEquals(ImportDeployment.State.AWAIT_HOSTS_DISCOVERED, requests.get(0).state());
    }

    importDeploymentSvc.handleWork(group.getId());

    {
      List<ImportDeployment> requests = importDeploymentSvc.getRequests(group.getId());
      assertEquals(1, requests.size());
      assertEquals(ImportDeployment.State.AWAIT_AUTOMATION_IMPORTED, requests.get(0).state());
    }

    importDeploymentSvc.handleWork(group.getId());

    ObjectId importRequestId;
    {
      List<ImportDeployment> requests = importDeploymentSvc.getRequests(group.getId());
      assertEquals(1, requests.size());
      assertEquals(ImportDeployment.State.AWAIT_AUTOMATION_IMPORTED, requests.get(0).state());
      importRequestId = requests.get(0).importRequestId();
    }

    // Should be one job associated to an import request
    agentJobsProcessorDao.findAllAgentJobs().stream()
        .filter(agentJob -> Objects.equals(importRequestId, agentJob.getBatchId()))
        .forEach(
            agentJob -> {
              LOG.info("Found Job with type: {}", agentJob.getCommand());
              if (agentJob.getCommand() == AgentJob.Command.Type.GetProcessConfig) {
                final MongoDbBuild build =
                    new MongoDbBuild.Builder()
                        .trueName("8.0.1")
                        .platform(PlatformConstants.Platforms.linux)
                        .gitVersion(GetProcessConfigJob.NO_GIT_VERSION)
                        .build();

                var args26 = new ProcessArguments2_6();
                args26.setPort(1234);
                args26.setDbPath("/db/path");
                args26.setBindIp("*******,localhost");
                args26.setLogPath("/logs");

                var process = new Process();
                process.setFullVersion(build);
                process.setHostname("hostname");
                process.setArgs2_6(args26);
                process.setProcessType(ProcessType.MONGOD);
                process.setAuthSchemaVersion(5);
                process.setFeatureCompatibilityVersion("8.0");

                var pResponse = new GetProcessConfigJobResponseView();
                pResponse.setProcess(process);

                getProcessConfigJob.handleSuccess(agentJob, pResponse);
              } else if (agentJob.getCommand() == AgentJob.Command.Type.GetAuthConfig) {
                // Simulating the agent response
                importRequestSvc.addAuthResponse(
                    agentJob.getBatchId(),
                    agentJob.getId(),
                    new AuthResponse(List.of(), new Auth(), null));
              }
            });

    importDeploymentSvc.handleWork(group.getId());

    {
      List<ImportDeployment> requests = importDeploymentSvc.getRequests(group.getId());
      assertEquals(1, requests.size());
      assertEquals(ImportDeployment.State.AWAIT_GOAL_STATE, requests.get(0).state());
    }

    final AutomationConfig automationConfig = automationConfigSvc.findPublished(group.getId());
    simulateGoalState(automationConfig);

    importDeploymentSvc.handleWork(group.getId());

    {
      List<ImportDeployment> requests = importDeploymentSvc.getRequests(group.getId());
      assertEquals(1, requests.size());
      assertEquals(ImportDeployment.State.SUCCESS, requests.get(0).state());
    }
  }

  @Test
  public void noAutomationAgentDetected() throws Exception {
    final ImportDeploymentRequestView request = new ImportDeploymentRequestView();
    request.setAuthMechanism(AuthMechanism.GSSAPI);
    request.setRequiredProcesses(List.of("hostname:1234"));
    request.setSeedHostport("hostname:1234");

    ValidationException validationException =
        assertThrows(
            ValidationException.class,
            () -> importDeploymentSvc.submitImportDeploymentRequest(group, user, request));
    assertTrue(validationException.getMessage().contains("No automation agent detected"));
  }

  private void injectMonitoringData(Group group, String hostname) {
    try {
      final Map<String, Object> automationAgentAuditParams =
          Map.of(
              "objectId",
              new ObjectId(),
              "groupId",
              group.getId().toString(),
              "day",
              TimeUtils2.getNowRrdDayStr(),
              "hostname",
              hostname,
              "agentVer",
              "111.0.112");

      TestDataUtils.populateCollectionFromJsonFtlFile(
          "mms/dao/agent/ServerSvc/automationAgentAudits.json.ftl",
          automationAgentAuditParams,
          AgentAudit.DB_NAME,
          AgentAudit.COLLECTION_NAME);

      final Map<String, Object> monitoringAgentAuditParams =
          Map.of(
              "objectId", new ObjectId(),
              "groupId", group.getId().toString(),
              "rcid", reverseObjectId(group.getId()),
              "day", TimeUtils2.getNowRrdDayStr(),
              "hostname", hostname);

      TestDataUtils.populateCollectionFromJsonFtlFile(
          "mms/dao/agent/ServerSvc/monitoringAgentAudits.json.ftl",
          monitoringAgentAuditParams,
          MonitoringAgentAuditDao.DB_NAME,
          MonitoringAgentAuditDao.COLLECTION_NAME);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  private void simulateGoalState(final AutomationConfig pConfig) {
    final Map<String, List<Process>> processesByHostname =
        pConfig.getDeployment().getProcesses().stream()
            .collect(Collectors.groupingBy(Process::getHostname));

    for (final String hostname : processesByHostname.keySet()) {
      final List<Process> processes = processesByHostname.get(hostname);
      final List<EnhancedProcessStatus> processStatus =
          processes.stream()
              .map(
                  p ->
                      new EnhancedProcessStatus(
                          p.getName(), pConfig.getVersion().intValue(), Collections.emptyList()))
              .collect(Collectors.toList());
      final LastAgentStatus status =
          new LastAgentStatus(
              LastAgentStatus.DEFAULT_GROUP_LOCATION,
              pConfig.getGroupId(),
              hostname,
              processStatus,
              0L);
      lastAgentStatusSvc.save(status);
    }
  }
}
