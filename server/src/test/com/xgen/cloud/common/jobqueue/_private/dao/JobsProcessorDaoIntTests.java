package com.xgen.cloud.common.jobqueue._private.dao;

import static com.xgen.cloud.billingplatform.model.jobqueue._public.model.ServerTag.USAGE_TRACKING;
import static com.xgen.cloud.common.jobqueue._private.settings.JobQueueSettings.JOB_EXPIRATION;
import static com.xgen.cloud.common.jobqueue._private.settings.JobQueueSettings.JOB_QUEUE_SHUFFLING;
import static com.xgen.cloud.common.jobqueue._private.settings.JobQueueSettings.OWNER_THREAD_HEARTBEAT_USE_DISABLED;
import static com.xgen.cloud.common.jobqueue._private.settings.JobQueueSettings.RETRY_WAIT_TIME;
import static com.xgen.cloud.common.jobqueue._private.settings.JobQueueSettings.SHOULD_USE_OWNER_THREAD_HEARTBEAT_POOL_OPT_OUT_LIST;
import static com.xgen.cloud.common.jobqueue._public.model.Job.DB_NAME;
import static com.xgen.cloud.common.jobqueue._public.model.ServerTags.Tags.LOAD_SAMPLE_DATASET;
import static com.xgen.cloud.common.jobqueue._public.model.ServerTags.Tags.TENANT_UPGRADE;
import static java.lang.Boolean.FALSE;
import static java.time.Duration.ofSeconds;
import static java.time.Instant.now;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.CoreMatchers.hasItem;
import static org.hamcrest.CoreMatchers.hasItems;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.in;
import static org.hamcrest.Matchers.not;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.DuplicateKeyException;
import com.mongodb.MongoConnectionPoolClearedException;
import com.mongodb.MongoException;
import com.mongodb.MongoNodeIsRecoveringException;
import com.mongodb.ServerAddress;
import com.mongodb.connection.ClusterId;
import com.mongodb.connection.ServerId;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appversion._public.settings.PingerSettings;
import com.xgen.cloud.common.db.legacy._private.svc.impl.MongoSvcUriImpl;
import com.xgen.cloud.common.driverwrappers._public.common.MongoClientRuntimeSettingsCache;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCollection;
import com.xgen.cloud.common.driverwrappers._public.legacy.MongoClient;
import com.xgen.cloud.common.jobqueue._private.dao.JobsProcessorDao.JobShuffleMorphiaCursor;
import com.xgen.cloud.common.jobqueue._private.settings.JobQueueSettings;
import com.xgen.cloud.common.jobqueue._public.model.BaseJob;
import com.xgen.cloud.common.jobqueue._public.model.BatchJobStatistics;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.model.Job.Builder;
import com.xgen.cloud.common.jobqueue._public.model.Job.Status;
import com.xgen.cloud.common.jobqueue._public.model.Job.Type;
import com.xgen.cloud.common.jobqueue._public.model.JobHandler;
import com.xgen.cloud.common.jobqueue._public.model.ServerTags;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.util._public.util.SetUtils;
import com.xgen.svc.brs.clusterconfig.handlers.CancelRestoreRsDeploymentJobHandler;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.svc.billing.ChargePaymentJobHandler;
import dev.morphia.Datastore;
import dev.morphia.Morphia;
import dev.morphia.query.Query;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.BsonDocument;
import org.bson.BsonString;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

/** Job consumer must be off for these tests */
public class JobsProcessorDaoIntTests extends JUnit5BaseSvcTest {

  public static final String HOST_1 = "host1";
  public static final String HOST_2 = "host2";
  public static final String HOST_3 = "host3";

  public static final String OWNER_1_0 = HOST_1 + "-0";
  public static final String OWNER_2_0 = HOST_2 + "-0";
  public static final String OWNER_2_1 = HOST_2 + "-1";
  public static final String OWNER_3_0 = HOST_3 + "-0";
  public static final String OWNER_3_1 = HOST_3 + "-1";

  static final String EU_WEST_1_TAG = "EU_WEST_1";
  static final String JOB_ORPHANED_REASON = "Job orphaned as referenced target no longer exists";
  static final String TEMP_COLLECTION_NAME = Job.COLLECTION_NAME + "_temp";
  static final String DEFAULT_POOL = "cool-pool";

  @Inject private AppSettings _appSettings;
  @Inject private PingerSettings _pingerSettings;
  @Inject private JobQueueSettings _jobQueueSettings;
  @Inject private JobsProcessorDao _jobsProcessorDao;

  private Morphia _morphia;
  private Datastore _morphiaDs;
  private MongoClient _mongoClient;
  private MongoSvcUriImpl _mongoSvc;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/JobsProcessorDao/data.jobsProcessor.json.ftl", null, DB_NAME, Job.COLLECTION_NAME);

    _mongoSvc = (MongoSvcUriImpl) _jobsProcessorDao.getMongoSvc();
    _mongoClient = _mongoSvc.getMongo(DB_NAME);
    _morphia = _mongoSvc.getMorphia(DB_NAME);
    _morphiaDs = _mongoSvc.getMorphiaDs(DB_NAME);
    doReturn(_morphiaDs).when(_mongoSvc).getCutoverMorphiaDs(DB_NAME, DB_NAME);
    doReturn(_mongoClient).when(_mongoSvc).getCutoverMongoClient(DB_NAME);
  }

  @Override
  @AfterEach
  public void tearDown() throws Exception {
    super.tearDown();
    Mockito.reset(_mongoSvc);
  }

  @Test
  public void testNextJobTimeOrdering() {
    final Job job1 =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId job1id = _jobsProcessorDao.putJob(job1);

    final Job job2 =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId job2id = _jobsProcessorDao.putJob(job2);

    final List<Job> josbTaken =
        new ArrayList<>(
            _jobsProcessorDao.legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 3));
    final List<ObjectId> takenJobIds =
        josbTaken.stream().map(Job::getId).collect(Collectors.toList());

    assertEquals(2, takenJobIds.size());
    assertThat(takenJobIds, containsInAnyOrder(job1id, job2id));
  }

  @Test
  public void shouldTakeNextJobWithListOfTags() {
    final Job taggedJob =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .pinnable(true)
            .tags(List.of(TENANT_UPGRADE, USAGE_TRACKING.name()))
            .build();

    final ObjectId taggedJobId = _jobsProcessorDao.putJob(taggedJob);

    final List<Job> takenJob =
        _jobsProcessorDao
            .legacyTakeNextJobs(
                "me",
                new ServerTags(
                    "me", List.of(TENANT_UPGRADE, USAGE_TRACKING.name(), LOAD_SAMPLE_DATASET)),
                2)
            .stream()
            .collect(Collectors.toList());

    assertEquals(1, takenJob.size());
    assertEquals(taggedJobId, takenJob.get(0).getId());
    assertEquals("me", takenJob.get(0).getPinnedHost());
  }

  @Test
  public void shouldNotTakeNextUntaggedJobInRegionalJobProcessing() {
    final Job untaggedJob =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .pinnable(true)
            .build();

    _jobsProcessorDao.putJob(untaggedJob);

    final List<Job> takenJob =
        _jobsProcessorDao
            .legacyTakeNextJobs("me", new ServerTags("me", List.of(EU_WEST_1_TAG)), 2)
            .stream()
            .collect(Collectors.toList());

    assertEquals(0, takenJob.size());
  }

  @Test
  public void shouldTakeNextRegionJobWithOnlySingleRegionTag() {
    final Job taggedJob =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .pinnable(true)
            .tags(List.of(EU_WEST_1_TAG))
            .build();

    final ObjectId taggedJobId = _jobsProcessorDao.putJob(taggedJob);

    final List<Job> takenJob =
        _jobsProcessorDao
            .legacyTakeNextJobs("me", new ServerTags("me", List.of(EU_WEST_1_TAG)), 2)
            .stream()
            .collect(Collectors.toList());

    assertEquals(1, takenJob.size());
    assertEquals(taggedJobId, takenJob.get(0).getId());
  }

  @Test
  public void shouldNotTakeNextRegionJobWithOnlySingleRegionTag() {
    final Job taggedJob =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .pinnable(true)
            .tags(List.of(TENANT_UPGRADE, USAGE_TRACKING.name(), EU_WEST_1_TAG))
            .build();
    final Job onlyRegionTagJob =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .pinnable(true)
            .tags(List.of(EU_WEST_1_TAG))
            .build();

    final ObjectId taggedJobId = _jobsProcessorDao.putJob(taggedJob);
    _jobsProcessorDao.putJob(onlyRegionTagJob);

    final List<Job> takenJob =
        _jobsProcessorDao
            .legacyTakeNextJobs(
                "me",
                new ServerTags(
                    "me",
                    List.of(
                        TENANT_UPGRADE, USAGE_TRACKING.name(), LOAD_SAMPLE_DATASET, EU_WEST_1_TAG)),
                2)
            .stream()
            .collect(Collectors.toList());

    assertEquals(1, takenJob.size());
    assertEquals(taggedJobId, takenJob.get(0).getId());
  }

  @Test
  public void shouldNotTakeNextNotRegionJobInRegionalJobProcessing() {
    final Job notRegionalJob =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .pinnable(true)
            .tags(List.of(TENANT_UPGRADE, USAGE_TRACKING.name()))
            .build();
    final Job regionalJob =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .pinnable(true)
            .tags(List.of(TENANT_UPGRADE, USAGE_TRACKING.name(), EU_WEST_1_TAG))
            .build();

    _jobsProcessorDao.putJob(notRegionalJob);
    final ObjectId regionalJobId = _jobsProcessorDao.putJob(regionalJob);

    final List<Job> takenJob =
        _jobsProcessorDao
            .legacyTakeNextJobs(
                "me",
                new ServerTags(
                    "me",
                    List.of(
                        TENANT_UPGRADE, USAGE_TRACKING.name(), LOAD_SAMPLE_DATASET, EU_WEST_1_TAG)),
                2)
            .stream()
            .collect(Collectors.toList());

    assertEquals(1, takenJob.size());
    assertEquals(regionalJobId, takenJob.get(0).getId());
  }

  @Test
  public void testJobIsOwned() {
    final Job job1 =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId job1id = _jobsProcessorDao.putJob(job1);

    final List<Job> jobTaken1 =
        new ArrayList<>(
            _jobsProcessorDao.legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 2));
    assertEquals(1, jobTaken1.size());
    assertEquals(job1id, jobTaken1.get(0).getId());
    final Job jobOwned1 = _jobsProcessorDao.getJob(job1id);
    assertEquals(Job.Status.OWNED, jobOwned1.getStatus());
    assertEquals("me", jobOwned1.getOwner());
    assertNotNull(jobOwned1.getOwnedAt());
  }

  @Test
  public void testRetrieveRetryExhaustedFailSingleJobs() {

    final ObjectId job1id =
        _jobsProcessorDao.putJob(
            new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
                .retriesRemaining(0)
                .intervalUntilRerun(Duration.ZERO)
                .build());
    final ObjectId job2id =
        _jobsProcessorDao.putJob(
            new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
                .retriesRemaining(0)
                .intervalUntilRerun(Duration.ZERO)
                .build());
    final ObjectId job3id =
        _jobsProcessorDao.putJob(
            new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
                .retriesRemaining(0)
                .intervalUntilRerun(Duration.ZERO)
                .build());
    final ObjectId job4id =
        _jobsProcessorDao.putJob(
            new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
                .retriesRemaining(0)
                .intervalUntilRerun(Duration.ZERO)
                .build());
    final String err1 = "blah blah something went wrong ... so sad...";
    _jobsProcessorDao.markJobAsFailedNoRetriesRemaining(job1id, err1);

    final Job job1 = _jobsProcessorDao.getJob(job1id);
    long expectedExpiredAtTime = job1.getLastUpdated() + JOB_EXPIRATION.toMillis();
    assertEquals(err1, job1.getErrMsg());
    assertEquals(0, job1.getRetriesRemaining());
    assertEquals(Job.Status.FAILED, job1.getStatus());
    assertNotNull(job1.getCompletedAt());
    assertTrue(job1.getExpireAt().getTime() >= expectedExpiredAtTime);

    // expire two jobs above that have no retries remaining
    _jobsProcessorDao.exhaustFailedSingleJobsWithoutRetries();
    final Job job1exhuasted = _jobsProcessorDao.getJob(job1id);
    assertEquals(Job.Status.RETRY_EXHAUSTED_FAIL, job1exhuasted.getStatus());

    final List<Job> exhaustedSingleJobs =
        _jobsProcessorDao.retrieveRetriesExhaustedSingleJobs(
            CancelRestoreRsDeploymentJobHandler.class, 5 * 60 * 1000);
    assertEquals(exhaustedSingleJobs.size(), 1);
    assertEquals(exhaustedSingleJobs.get(0).getId(), job1id);
    assertTrue(job1exhuasted.getExpireAt().getTime() >= expectedExpiredAtTime);
  }

  @Test
  public void testExhaustExpiredSingleJobs() throws InterruptedException {
    final ObjectId job1id =
        _jobsProcessorDao.putJob(
            new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
                .retriesRemaining(0)
                .intervalUntilRerun(Duration.ZERO)
                .build());

    final Job job1 =
        new ArrayList<>(
                _jobsProcessorDao.legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 1))
            .get(0);
    assertEquals(job1id, job1.getId());
    assertEquals(Job.Status.OWNED, job1.getStatus());
    assertEquals("me", job1.getOwner());
    assertNotNull(job1.getOwnedAt());
    Thread.sleep(1000);
    _jobsProcessorDao.exhaustExpiredSingleJobsWithoutRetriesLeft();
    final Job job1exhuasted = _jobsProcessorDao.getJob(job1id);
    assertEquals(Job.Status.RETRY_EXHAUSTED_FAIL, job1exhuasted.getStatus());
    assertTrue(
        job1exhuasted.getExpireAt().getTime()
            >= job1exhuasted.getLastUpdated() + JOB_EXPIRATION.toMillis());
  }

  private Job createUniqueRecurringJob(
      final Class<? extends JobHandler> pClass,
      final BasicDBObject pParams,
      final Duration pIntervalUntilRerun) {
    return new Job.Builder(pClass, pParams)
        .type(Job.Type.RECURRING)
        .handlerId(pClass.getName())
        .intervalUntilRerun(pIntervalUntilRerun)
        .retriesRemaining(Job.NO_RETRY)
        .build();
  }

  @Test
  public void testResetRecurringJobs() throws InterruptedException {
    final Job job1 =
        createUniqueRecurringJob(
            CancelRestoreRsDeploymentJobHandler.class, null, Duration.ofMillis(1));
    final ObjectId jobId1 = _jobsProcessorDao.putJob(job1);
    final Job completedJob1 = _jobsProcessorDao.markJobAsCompleted(jobId1);
    assertEquals(Job.Status.COMPLETED, completedJob1.getStatus());
    assertNotNull(completedJob1.getCompletedAt());
    assertEquals(Job.Type.RECURRING, completedJob1.getType());

    final Job job2 =
        createUniqueRecurringJob(ChargePaymentJobHandler.class, null, Duration.ofMillis(1));
    final ObjectId jobId2 = _jobsProcessorDao.putJob(job2);
    _jobsProcessorDao.markJobAsFailed(jobId2, "blah blah");
    final Job completedJob2 = _jobsProcessorDao.getJob(jobId2);
    assertEquals(Job.Status.FAILED, completedJob2.getStatus());
    assertNotNull(completedJob2.getCompletedAt());
    assertEquals(Job.Type.RECURRING, completedJob2.getType());
    assertNull(completedJob2.getExpireAt());

    Thread.sleep(1000);
    _jobsProcessorDao.resetRecurringJobs();
    final Job availableJob = _jobsProcessorDao.getJob(jobId1);
    assertEquals(Job.Status.NEW, availableJob.getStatus());
  }

  @Test
  public void testResetRecurringJobs_withScheduledForUpdate_setsScheduledForToCurrentTime() {
    final long pastTime = System.currentTimeMillis() - Duration.ofHours(2).toMillis();
    final long testStartTime = System.currentTimeMillis();

    // Job 1: COMPLETED recurring job (should be reset)
    final Job completedJob =
        new Job.Builder(DummyJobHandler.class, null)
            .type(Job.Type.RECURRING)
            .intervalUntilRerun(Duration.ofMinutes(30))
            .retriesRemaining(Job.NO_RETRY)
            .scheduledFor(pastTime)
            .build();
    final ObjectId completedJobId = _jobsProcessorDao.putJob(completedJob);
    _jobsProcessorDao.markJobAsCompleted(completedJobId);

    // Job 2: FAILED recurring job (should be reset)
    final Job failedJob =
        new Job.Builder(DummyJobHandler.class, null)
            .type(Job.Type.RECURRING)
            .status(Job.Status.FAILED)
            .intervalUntilRerun(Duration.ofMinutes(30))
            .retriesRemaining(Job.NO_RETRY)
            .scheduledFor(pastTime)
            .build();
    final ObjectId failedJobId = _jobsProcessorDao.putJob(failedJob);

    // Job 3: NEW recurring job (should NOT be reset - wrong status)
    final Job newJob =
        new Job.Builder(DummyJobHandler.class, null)
            .type(Job.Type.RECURRING)
            .intervalUntilRerun(Duration.ofMinutes(30))
            .retriesRemaining(Job.NO_RETRY)
            .scheduledFor(pastTime)
            .build();
    final ObjectId newJobId = _jobsProcessorDao.putJob(newJob);

    // Reset recurring jobs
    final Set<ObjectId> resetJobIds = _jobsProcessorDao.resetRecurringJobs();
    final long testEndTime = System.currentTimeMillis();

    assertThat(resetJobIds, hasSize(2)); // Only 2 jobs should be reset
    assertThat(resetJobIds, containsInAnyOrder(completedJobId, failedJobId));

    // Verify scheduledFor field is updated to current time (within reasonable tolerance)
    final Job updatedCompletedJob = _jobsProcessorDao.getJob(completedJobId);
    assertNotEquals(updatedCompletedJob.getScheduledFor(), pastTime);
    assertTrue(
        updatedCompletedJob.getScheduledFor() >= testStartTime,
        "scheduledFor should be >= test start time");
    assertTrue(
        updatedCompletedJob.getScheduledFor() <= testEndTime,
        "scheduledFor should be <= test end time");
    assertEquals(Job.Status.NEW, updatedCompletedJob.getStatus());

    final Job updatedFailedJob = _jobsProcessorDao.getJob(failedJobId);
    assertNotEquals(updatedCompletedJob.getScheduledFor(), pastTime);
    assertTrue(
        updatedFailedJob.getScheduledFor() >= testStartTime,
        "scheduledFor should be >= test start time");
    assertTrue(
        updatedFailedJob.getScheduledFor() <= testEndTime,
        "scheduledFor should be <= test end time");
    assertEquals(Job.Status.NEW, updatedFailedJob.getStatus());
  }

  @Test
  public void testUniqueRecurringJobs() {
    _jobsProcessorDao.putJob(
        createUniqueRecurringJob(
            CancelRestoreRsDeploymentJobHandler.class, null, Duration.ofMillis(1)));
    assertThrows(
        DuplicateKeyException.class,
        () ->
            _jobsProcessorDao.putJob(
                createUniqueRecurringJob(
                    CancelRestoreRsDeploymentJobHandler.class, null, Duration.ofMillis(1))));
  }

  @Test
  // This test makes sure that two jobs with the same worker type but different handler id can be
  // accepted
  public void testMultipleJobsForWorkerType() {
    final Job job1 =
        createUniqueRecurringJob(
            CancelRestoreRsDeploymentJobHandler.class, null, Duration.ofMillis(1));
    _jobsProcessorDao.putJob(job1);
    _jobsProcessorDao.putJob(new Job.Builder(job1).handlerId(new ObjectId().toString()).build());
  }

  @Test
  public void testResetExpiredSingleJobs() throws InterruptedException {
    final ObjectId jobId =
        _jobsProcessorDao.putJob(
            new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
                .retriesRemaining(2)
                .intervalUntilRerun(Duration.ofMillis(1))
                .build());

    final Job jobFirstTry =
        new ArrayList<>(
                _jobsProcessorDao.legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 1))
            .get(0);
    assertEquals(jobId, jobFirstTry.getId());
    assertEquals(Job.Status.OWNED, jobFirstTry.getStatus());
    assertEquals("me", jobFirstTry.getOwner());
    assertNotNull(jobFirstTry.getOwnedAt());
    Thread.sleep(1000);
    _jobsProcessorDao.resetExpiredSingleJobsWithRetriesLeft();
    final Job jobFirstTryFail = _jobsProcessorDao.getJob(jobId);
    assertEquals(Job.Status.NEW, jobFirstTryFail.getStatus());
    assertEquals(1, jobFirstTryFail.getRetriesRemaining());
    assertNull(jobFirstTryFail.getOwner());
    assertNull(jobFirstTryFail.getOwnedAt());

    final Job jobSecondTry =
        new ArrayList<>(
                _jobsProcessorDao.legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 1))
            .get(0);
    assertEquals(jobId, jobSecondTry.getId());
    assertEquals(Job.Status.OWNED, jobSecondTry.getStatus());
    assertEquals("me", jobSecondTry.getOwner());
    assertNotNull(jobSecondTry.getOwnedAt());
    Thread.sleep(1000);
    _jobsProcessorDao.resetExpiredSingleJobsWithRetriesLeft();

    final Job jobSecondTryFail = _jobsProcessorDao.getJob(jobId);
    assertEquals(Job.Status.NEW, jobSecondTryFail.getStatus());
    assertEquals(0, jobSecondTryFail.getRetriesRemaining());
    assertNull(jobSecondTryFail.getOwner());
    assertNull(jobSecondTryFail.getOwnedAt());
  }

  @Test
  public void testResetAllOwnedJobs() {
    final ObjectId jobId1 =
        _jobsProcessorDao.putJob(
            createUniqueRecurringJob(
                CancelRestoreRsDeploymentJobHandler.class, null, Duration.ofMinutes(5)));
    final ObjectId jobId2 =
        _jobsProcessorDao.putJob(
            new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
                .retriesRemaining(2)
                .intervalUntilRerun(Duration.ofMinutes(5))
                .build());
    final ObjectId jobId3 =
        _jobsProcessorDao.putJob(
            new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
                .retriesRemaining(1)
                .intervalUntilRerun(Duration.ofMinutes(5))
                .build());
    final ObjectId jobId4 =
        _jobsProcessorDao.putJob(
            new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
                .retriesRemaining(2)
                .intervalUntilRerun(Duration.ofMinutes(5))
                .build());
    final ObjectId jobId5 =
        _jobsProcessorDao.putJob(
            createUniqueRecurringJob(ChargePaymentJobHandler.class, null, Duration.ofMinutes(5)));

    _appSettings.setProperty(JOB_QUEUE_SHUFFLING, false);

    List<Job> takenJobs =
        List.of(
            _jobsProcessorDao
                .legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 1)
                .iterator()
                .next(),
            _jobsProcessorDao
                .legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 1)
                .iterator()
                .next(),
            _jobsProcessorDao
                .legacyTakeNextJobs("you", ServerTags.getDisabled("test"), 1)
                .iterator()
                .next());
    Map<ObjectId, Job> takenJobsMap =
        takenJobs.stream()
            .map(j -> Pair.of(j.getId(), j))
            .collect(toMap(Pair::getLeft, Pair::getRight));
    Map<String, List<Job>> ownedJobsMap =
        takenJobs.stream().collect(Collectors.groupingBy(Job::getOwner));

    // job 1 is recurring so no retries
    Job start1 = takenJobsMap.get(jobId1);
    assertEquals(jobId1, start1.getId());
    assertEquals(Job.Status.OWNED, start1.getStatus());
    assertThat(start1.getOwner(), in(List.of("me", "you")));
    assertNotNull(start1.getOwnedAt());

    Job start2 = takenJobsMap.get(jobId2);
    assertEquals(jobId2, start2.getId());
    assertEquals(Job.Status.OWNED, start2.getStatus());
    assertEquals(2, start2.getRetriesRemaining());
    assertThat(start2.getOwner(), in(List.of("me", "you")));
    assertNotNull(start2.getOwnedAt());

    Job start3 = takenJobsMap.get(jobId3);
    assertEquals(jobId3, start3.getId());
    assertEquals(Job.Status.OWNED, start3.getStatus());
    assertEquals(1, start3.getRetriesRemaining());
    assertThat(start3.getOwner(), in(List.of("me", "you")));
    assertNotNull(start3.getOwnedAt());

    final Job job4 = _jobsProcessorDao.getJob(jobId4);
    assertNotNull(job4);
    assertEquals(Job.Status.NEW, job4.getStatus());
    final Job job5 = _jobsProcessorDao.getJob(jobId5);
    assertNotNull(job5);
    assertEquals(Job.Status.NEW, job5.getStatus());

    _jobsProcessorDao.resetAllOwnedJobs("me");
    final Job job1reset = _jobsProcessorDao.getJob(ownedJobsMap.get("me").get(0).getId());
    assertEquals(Job.Status.NEW, job1reset.getStatus());
    assertNull(job1reset.getOwner());
    assertNull(job1reset.getOwnedAt());
    assertNull(job1reset.getCompletedAt());

    final Job job2reset = _jobsProcessorDao.getJob(ownedJobsMap.get("me").get(1).getId());
    assertEquals(Job.Status.NEW, job2reset.getStatus());
    assertEquals(
        ownedJobsMap.get("me").get(1).getRetriesRemaining(), job2reset.getRetriesRemaining());
    assertNull(job2reset.getOwner());
    assertNull(job2reset.getOwnedAt());
    assertNull(job2reset.getCompletedAt());

    final Job job3reset = _jobsProcessorDao.getJob(ownedJobsMap.get("you").get(0).getId());
    assertEquals(Job.Status.OWNED, job3reset.getStatus());
    assertEquals("you", job3reset.getOwner());
    assertNotNull(job3reset.getOwnedAt());
  }

  @Test
  public void testCountUnfinishedJobsInBatch() {
    assertEquals(0, _jobsProcessorDao.countUnfinishedJobsInBatch("batch0"));
    assertEquals(1, _jobsProcessorDao.countUnfinishedJobsInBatch("batch1"));
    assertEquals(3, _jobsProcessorDao.countUnfinishedJobsInBatch("batch2"));
  }

  @Test
  public void testCountCompletedJobsInBatch() {
    assertEquals(0, _jobsProcessorDao.countCompletedJobsInBatch("batch0"));
    assertEquals(0, _jobsProcessorDao.countCompletedJobsInBatch("batch1"));
    assertEquals(1, _jobsProcessorDao.countCompletedJobsInBatch("batch2"));
  }

  @Test
  public void testCountFailedJobsInBatch() {
    assertEquals(0, _jobsProcessorDao.countFailedJobsInBatch("batch0"));
    assertEquals(0, _jobsProcessorDao.countFailedJobsInBatch("batch1"));
    assertEquals(3, _jobsProcessorDao.countFailedJobsInBatch("batch2"));
  }

  @Test
  public void testCountAllJobsInBatch() {
    assertEquals(0, _jobsProcessorDao.countAllJobsInBatch("batch0"));
    assertEquals(1, _jobsProcessorDao.countAllJobsInBatch("batch1"));
    assertEquals(7, _jobsProcessorDao.countAllJobsInBatch("batch2"));
  }

  @Test
  public void testComputeBatchJobStatistics() {
    // non-existent batch
    final BatchJobStatistics stats0 = _jobsProcessorDao.computeBatchJobStatistics("batch0");
    assertEquals(0L, stats0.getTimeToScheduleRound());
    assertEquals(0L, stats0.getTimeToFinishRound());
    // batch with no finished jobs
    final BatchJobStatistics stats1 = _jobsProcessorDao.computeBatchJobStatistics("batch1");
    assertEquals(0L, stats1.getTimeToScheduleRound());
    assertEquals(0L, stats1.getTimeToFinishRound());
    // batch with some finished jobs
    final BatchJobStatistics stats2 = _jobsProcessorDao.computeBatchJobStatistics("batch2");
    assertEquals(1L, Duration.ofMillis(stats2.getTimeToScheduleRound()).getSeconds());
    assertEquals(5L, Duration.ofMillis(stats2.getTimeToFinishRound()).getSeconds());
  }

  @Test
  public void testMarkJobAsCompletedAndExpired() {
    class DummyJobHandler implements JobHandler {

      public DummyJobHandler() {}

      @Override
      public void handleWork(final BasicDBObject pParameters, final ObjectId pJobId) {}
    }
    final Job job1a =
        new Job.Builder(DummyJobHandler.class, new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId id1 = _jobsProcessorDao.putJob(job1a);
    final Job job1b = _jobsProcessorDao.markJobAsCompletedAndExpired(id1, null);
    assertEquals(Job.Status.COMPLETED, job1b.getStatus());
    assertNotNull(job1b.getCompletedAt());
    assertEquals(0, job1b.getRetriesRemaining());
    assertTrue(job1b.getExpireAt().getTime() >= job1b.getLastUpdated() + JOB_EXPIRATION.toMillis());
    // The DB update sets the "updated" field to now, but some time will have passed. So make sure
    // it's close.
    final long elapsed1 = System.currentTimeMillis() - job1b.getCreated();
    assertTrue(elapsed1 < 600L, "elapsed time " + elapsed1 + "ms exceeded threshold of 600ms");

    final Job job2a =
        new Job.Builder(DummyJobHandler.class, new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId id2 = _jobsProcessorDao.putJob(job2a);
    final Date now = new Date();
    _jobsProcessorDao.markJobAsCompletedAndExpired(id2, now);
    final Job job2b = _jobsProcessorDao.getJob(id2);
    assertEquals(Job.Status.COMPLETED, job2b.getStatus());
    assertNotNull(job2b.getCompletedAt());
    assertEquals(0, job2b.getRetriesRemaining());
    assertEquals(now, job2b.getExpireAt());

    // The DB update sets the "updated" field to now, but some time will have passed. So make sure
    // it's close.
    final long elapsed2 = System.currentTimeMillis() - job2b.getCreated();
    assertTrue(elapsed2 < 600L, "elapsed time " + elapsed2 + "ms exceeded threshold of 600ms");
  }

  @Test
  public void testCancelInactiveJobWhenInactive() {
    ObjectId groupId = new ObjectId();
    final BasicDBObject params = new BasicDBObject();
    params.put("groupId", groupId);
    final Job job =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, params)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId jobId = _jobsProcessorDao.putJob(job);

    assertTrue(_jobsProcessorDao.cancelInactiveJob(jobId, groupId, "cause"));

    assertEquals(
        0, _jobsProcessorDao.legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 1).size());

    final Job retJob = _jobsProcessorDao.getJob(jobId);
    assertNotNull(retJob);
    assertEquals(Job.Status.CANCELLED, retJob.getStatus());
    assertNotNull(retJob.getCompletedAt());
    assertEquals("cause", retJob.getErrMsg());
    assertNull(retJob.getOwner());
    assertTrue(
        retJob.getExpireAt().getTime() >= retJob.getLastUpdated() + JOB_EXPIRATION.toMillis());
  }

  @Test
  public void testCancelInactiveJobWhenActive() {
    ObjectId groupId = new ObjectId();
    final BasicDBObject params = new BasicDBObject();
    params.put("groupId", groupId);

    final Job job =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, params)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId jobId = _jobsProcessorDao.putJob(job);

    assertEquals(
        1, _jobsProcessorDao.legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 1).size());

    assertFalse(_jobsProcessorDao.cancelInactiveJob(jobId, groupId, "cause"));
  }

  @Test
  public void testMarkJobAsCancelled() {
    final Job job =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId jobId = _jobsProcessorDao.putJob(job);

    _jobsProcessorDao.markJobAsCancelled(jobId, "cause");

    assertEquals(
        0, _jobsProcessorDao.legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 1).size());

    final Job retJob = _jobsProcessorDao.getJob(jobId);
    assertNotNull(retJob);
    assertEquals(Job.Status.CANCELLED, retJob.getStatus());
    assertNotNull(retJob.getCompletedAt());
    assertEquals("cause", retJob.getErrMsg());
    assertNull(retJob.getOwner());
    assertTrue(
        retJob.getExpireAt().getTime() >= retJob.getLastUpdated() + JOB_EXPIRATION.toMillis());
  }

  // CLOUDP-250177 - Ensure races between jobs resetting themselves and publishes caling cancel
  // result in consistent data model
  @Test
  public void testMarkJobAsCancelledFollwedByMarkJobAsReset() {
    final Job job =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId jobId = _jobsProcessorDao.putJob(job);

    _jobsProcessorDao.markJobAsReset(jobId, 1000L);

    final Job retJob1 = _jobsProcessorDao.getJob(jobId);
    assertNotNull(retJob1);
    assertEquals(Job.Status.NEW, retJob1.getStatus());
    assertNull(retJob1.getCompletedAt());

    _jobsProcessorDao.markJobAsCancelled(jobId, "cause");

    final Job retJob2 = _jobsProcessorDao.getJob(jobId);
    assertNotNull(retJob2);
    assertEquals(Job.Status.CANCELLED, retJob2.getStatus());
    assertNotNull(retJob2.getCompletedAt());
    assertEquals("cause", retJob2.getErrMsg());
    assertNull(retJob2.getOwner());
    assertTrue(
        retJob2.getExpireAt().getTime() >= retJob2.getLastUpdated() + JOB_EXPIRATION.toMillis());

    _jobsProcessorDao.markJobAsReset(jobId, 1000L);

    final Job retJob3 = _jobsProcessorDao.getJob(jobId);
    assertNotNull(retJob3);
    assertEquals(Job.Status.CANCELLED, retJob3.getStatus());
    assertNotNull(retJob3.getCompletedAt());
    assertEquals("cause", retJob3.getErrMsg());
    assertNull(retJob3.getOwner());
  }

  @Test
  public void testUpdateOwnerThreadHeartbeats() {
    final Date start = new Date();
    final Job jobOne =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final Job jobTwo =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, null)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId jobIdOne = _jobsProcessorDao.putJob(jobOne);
    final ObjectId jobIdTwo = _jobsProcessorDao.putJob(jobTwo);

    // claim jobs (marking them as owned)
    Collection<Job> jobs =
        _jobsProcessorDao.legacyTakeNextJobs("me", ServerTags.getDisabled("test"), 2);
    assertEquals(2, jobs.size());

    // find the job from the returned jobs that matches jobIdOne and extract the owner thread
    final String threadOne =
        jobs.stream()
            .filter(j -> j.getId().equals(jobIdOne))
            .map(Job::getExecutionId)
            .findFirst()
            .orElse(null);
    assertNotNull(threadOne);

    final String threadTwo =
        jobs.stream()
            .filter(j -> j.getId().equals(jobIdTwo))
            .map(Job::getExecutionId)
            .findFirst()
            .orElse(null);
    assertNotNull(threadTwo);

    // normal heartbeats
    Job job1 = _jobsProcessorDao.updateJobExecutionHeartbeat(jobIdOne, threadOne);
    Job job2 = _jobsProcessorDao.updateJobExecutionHeartbeat(jobIdTwo, threadTwo);
    assertNotNull(job1);
    assertNotNull(job2);

    // These should not match the underlying job in the db and thus not return a job
    //  this mimics no longer owning the job
    assertNull(_jobsProcessorDao.updateJobExecutionHeartbeat(jobIdOne, null));
    assertNull(_jobsProcessorDao.updateJobExecutionHeartbeat(jobIdOne, threadTwo));

    assertEquals(threadOne, job1.getExecutionId());
    assertEquals(threadTwo, job2.getExecutionId());
    assertNotNull(job1.getHeartbeatedAt());
    assertTrue(job1.getHeartbeatedAt().getTime() >= start.getTime());
    assertNotNull(job2.getHeartbeatedAt());
    assertTrue(job2.getHeartbeatedAt().getTime() >= start.getTime());

    // Marking jobs as completed and then trying to update the heartbeat should still work
    final long previousTime1 = job1.getHeartbeatedAt().getTime();
    final long previousTime2 = job2.getHeartbeatedAt().getTime();
    _jobsProcessorDao.markJobAsCompleted(jobIdOne);
    job1 = _jobsProcessorDao.updateJobExecutionHeartbeat(jobIdOne, threadOne);
    assertNotNull(job1);
    assertEquals(threadOne, job1.getExecutionId());
    assertTrue(job1.getHeartbeatedAt().getTime() > previousTime1);
    // Same for cancelled
    _jobsProcessorDao.markJobAsCancelled(jobIdTwo, "some good reason");
    job2 = _jobsProcessorDao.updateJobExecutionHeartbeat(jobIdTwo, threadTwo);
    assertNotNull(job2);
    assertEquals(threadTwo, job2.getExecutionId());
    assertTrue(job2.getHeartbeatedAt().getTime() > previousTime2);
  }

  @Test
  public void findJobForWorkerByStatusAndGroup() {

    ObjectId groupId = new ObjectId();
    BasicDBObject params = new BasicDBObject();
    params.put("groupId", groupId);

    List<Job> jobs =
        _jobsProcessorDao.findJobForWorkerByStatusAndGroup(
            CancelRestoreRsDeploymentJobHandler.class,
            Collections.singletonList(Job.Status.NEW),
            groupId);

    assertNotNull(jobs);
    assertEquals(0, jobs.size());

    final Job job =
        new Job.Builder(CancelRestoreRsDeploymentJobHandler.class, params)
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId jobId = _jobsProcessorDao.putJob(job);

    jobs =
        _jobsProcessorDao.findJobForWorkerByStatusAndGroup(
            CancelRestoreRsDeploymentJobHandler.class,
            Collections.singletonList(Job.Status.NEW),
            groupId);

    assertNotNull(jobs);
    assertEquals(1, jobs.size());
  }

  @Test
  public void tryTakingJob() {
    final long now = new Date().getTime();
    final Job job1 =
        new Job.Builder(DummyJobHandler.class, null)
            .status(Job.Status.OWNED)
            .scheduledFor(now)
            .build();
    final ObjectId jobId1 = _jobsProcessorDao.putJob(job1);
    final Job ownedJob = _jobsProcessorDao.getJob(jobId1);
    final ServerTags serverTags = ServerTags.getDisabled("me");

    // trying to take an OWNED job returns null
    assertNull(
        _jobsProcessorDao.tryTakingJob(now, ownedJob, "me", serverTags, null),
        "Trying to take an owned job should return null");

    final Job job2 =
        new Job.Builder(DummyJobHandler.class, null)
            .status(Job.Status.NEW)
            .scheduledFor(now)
            .build();
    final ObjectId jobId2 = _jobsProcessorDao.putJob(job2);
    final Job newJob = _jobsProcessorDao.getJob(jobId2);

    // trying to take a NEW job returns the job
    final Job takenJob = _jobsProcessorDao.tryTakingJob(now, newJob, "me", serverTags, null);
    assertEquals(jobId2, takenJob.getId(), "Trying to take a new job returns the job");
  }

  @Test
  public void legacyTakeNextJobsReturnsNoMoreThanRequested() {
    final ServerTags noServerTags = ServerTags.getDisabled("me");
    final Job job1 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();
    final Job job2 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();
    final Job job3 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.OWNED).build();
    final ObjectId jobId1 = _jobsProcessorDao.putJob(job1);

    // ignoring the return values here; don't need it
    final ObjectId jobId2 = _jobsProcessorDao.putJob(job2);
    _jobsProcessorDao.putJob(job3);

    // take jobs doesn't return more jobs than requested
    final Collection<Job> jobs = _jobsProcessorDao.legacyTakeNextJobs("me", noServerTags, 1);
    final Set<ObjectId> jobIds = extractIds(jobs);
    final List<ObjectId> possibleTakenJobIds = Arrays.asList(jobId1, jobId2);
    assertEquals(jobs.size(), 1);
    assertTrue(possibleTakenJobIds.contains(jobs.iterator().next().getId()));
  }

  @Test
  public void testLegacyTakeNextJobs_OneBadJobDoesNotBlockJobQueue() {
    final ServerTags noServerTags = ServerTags.getDisabled("me");

    final BasicDBObject badJob =
        new BasicDBObject()
            .append(BaseJob.PARAMS_FIELD, new BasicDBObject())
            .append(Job.TYPE_FIELD, Job.Type.SINGLE.name())
            .append(Job.STATUS_FIELD, Job.Status.NEW.name())
            .append(Job.SCHEDULED_FOR_FIELD, 0L)
            .append(Job.CREATED_FIELD, 0L)
            .append(Job.WORKER_FIELD, "com.xgen.svc.BadJobHandler.class");
    final Job goodJob = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();

    _jobsProcessorDao.insertMajority(badJob);
    final ObjectId goodJobId = _jobsProcessorDao.putJob(goodJob);

    // take next jobs return the jobId for the valid job
    final Collection<Job> jobs = _jobsProcessorDao.legacyTakeNextJobs("me", noServerTags, 1);
    final Set<ObjectId> jobIds = extractIds(jobs);
    assertEquals(Set.of(goodJobId), jobIds);
  }

  @Test
  public void testBuildPartitionedLegacyTakeNextJobsQuery() {
    final ServerTags serverTags = new ServerTags("someHost", List.of("TAG"));

    final int modValue = 10;
    final int numDocsPerMod = 5;

    final int tsPresent = 2000;
    final int tsFuture = 2001;

    IntStream.range(0, modValue * numDocsPerMod)
        .forEach(
            i -> {
              givenTaggedJobScheduledFor(tsPresent - i, "TAG");
              // these should be excluded from the results
              givenTaggedJobScheduledFor(tsFuture + i, "TAG");
            });

    final Query<Job> modQuery =
        _jobsProcessorDao.buildPartitionedTakeNextJobQuery(
            tsPresent, "someHost", serverTags, null, modValue);

    final List<Job> partitionedJobs = modQuery.find().toList();
    assertEquals(numDocsPerMod, partitionedJobs.size());

    // found jobs should all share the same mod partition
    assertEquals(
        1,
        partitionedJobs.stream()
            .map(job -> job.getScheduledFor() % modValue)
            .collect(toSet())
            .size());
  }

  @Test
  public void legacyTakeNextJobsReturnsAsManyAsAreAvailable() {
    final ServerTags noServerTags = ServerTags.getDisabled("me");
    final Job job1 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();
    final Job job2 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();
    final Job job3 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.OWNED).build();
    final ObjectId jobId1 = _jobsProcessorDao.putJob(job1);
    final ObjectId jobId2 = _jobsProcessorDao.putJob(job2);
    _jobsProcessorDao.putJob(job3); // ignoring the return value here; don't need it

    // take jobs returns as many jobs are available
    final Collection<Job> jobs = _jobsProcessorDao.legacyTakeNextJobs("me", noServerTags, 3);
    final Set<ObjectId> jobIds = extractIds(jobs);
    assertEquals(Set.of(jobId1, jobId2), jobIds);
  }

  @Test
  public void legacyTakeNextJobsReturnsEmptyListIfNoJobsAvailable() {
    final Job job = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();
    _jobsProcessorDao.putJob(job);

    // take jobs returns an empty list if no matching jobs are available
    final ServerTags serverTags = new ServerTags("me", List.of("BLAH"));
    final Collection<Job> jobs = _jobsProcessorDao.legacyTakeNextJobs("me", serverTags, 3);
    assertTrue(jobs.isEmpty());
  }

  @Test
  // Note: this test is an integration test and so is usually discouraged from using mocks. However,
  // this is the most convenient way to test the error handling of the cursor iteration without
  // having to mock the entire database including morphia
  public void
      testLegacyTakeNextJobs_ErrorIteratingCursorReturnAlreadyClaimedJobs_MongoConnectionPoolClearedException() {
    _appSettings.setProperty(JobQueueSettings.JOB_QUEUE_NUM_PARTITIONS, 1);
    final ServerTags noServerTags = ServerTags.getDisabled("me");
    _jobsProcessorDao = Mockito.spy(_jobsProcessorDao);
    final Job job1 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();
    final Job job2 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();
    _jobsProcessorDao.putJob(job1);
    _jobsProcessorDao.putJob(job2);
    final JobShuffleMorphiaCursor realCursor =
        _jobsProcessorDao.getTakeNextJobsShuffleCursor("me", noServerTags, 3);
    assertTrue(realCursor.hasNext());
    final JobShuffleMorphiaCursor spiedCursor = Mockito.spy(realCursor);
    doReturn(true)
        .doThrow(
            new MongoConnectionPoolClearedException(
                new ServerId(new ClusterId("localhost"), new ServerAddress("localhost")),
                new MongoNodeIsRecoveringException(
                    new BsonDocument("error", new BsonString("Simulated error")),
                    new ServerAddress("localhost"))))
        .when(spiedCursor)
        .hasNext();
    doReturn(spiedCursor)
        .when(_jobsProcessorDao)
        .getTakeNextJobsShuffleCursor(eq("me"), eq(noServerTags), eq(3));

    // Two jobs are inserted and available but only one is returned due to the error
    final Collection<Job> jobs = _jobsProcessorDao.legacyTakeNextJobs("me", noServerTags, 3);
    assertEquals(1, jobs.size());
  }

  // Note: this test is an integration test and so is usually discouraged from using mocks. However,
  // this is the most convenient way to test the error handling of the cursor iteration without
  // having to mock the entire database including morphia
  @Test
  public void
      testLegacyTakeNextJobs_ErrorIteratingCursorReturnAlreadyClaimedJobs_MongoNodeIsRecoveringException() {
    _appSettings.setProperty(JobQueueSettings.JOB_QUEUE_NUM_PARTITIONS, 1);
    final ServerTags noServerTags = ServerTags.getDisabled("me");
    _jobsProcessorDao = Mockito.spy(_jobsProcessorDao);
    final Job job1 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();
    final Job job2 = new Job.Builder(DummyJobHandler.class, null).status(Job.Status.NEW).build();
    _jobsProcessorDao.putJob(job1);
    _jobsProcessorDao.putJob(job2);
    final JobShuffleMorphiaCursor realCursor =
        _jobsProcessorDao.getTakeNextJobsShuffleCursor("me", noServerTags, 3);
    assertTrue(realCursor.hasNext());
    final JobShuffleMorphiaCursor spiedCursor = Mockito.spy(realCursor);
    doReturn(true)
        .doThrow(
            new MongoNodeIsRecoveringException(
                new BsonDocument("error", new BsonString("Simulated error")),
                new ServerAddress("localhost")))
        .when(spiedCursor)
        .hasNext();
    doReturn(spiedCursor)
        .when(_jobsProcessorDao)
        .getTakeNextJobsShuffleCursor(eq("me"), eq(noServerTags), eq(3));

    // Two jobs are inserted and available but only one is returned due to the error
    final Collection<Job> jobs = _jobsProcessorDao.legacyTakeNextJobs("me", noServerTags, 3);
    assertEquals(1, jobs.size());
  }

  @Test
  public void testInsertJob() {
    final ObjectId id = ObjectId.get();
    final Job job =
        new Job.Builder(id, DummyJobHandler.class, new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    _jobsProcessorDao.insertJob(job);

    final Job savedJob = _jobsProcessorDao.getJob(id);
    assertEquals(id, savedJob.getId());
    assertEquals(Job.NO_RETRY, savedJob.getRetriesRemaining());
    assertEquals(Duration.ZERO, savedJob.getIntervalUntilRerun());

    try {
      _jobsProcessorDao.insertJob(job);
    } catch (final MongoException e) {
      assertEquals(DbUtils.DUPLICATE_KEY_ERROR_CODE.intValue(), e.getCode());
    }
  }

  @Test
  public void testCountAllJobsForHandlerAndDate() {
    final Date startOfToday = DateUtils.truncate(new Date(), Calendar.DATE);
    _jobsProcessorDao.insertJob(
        new Job.Builder(
                new ObjectId(DateUtils.addHours(startOfToday, -1)),
                DummyJobHandler.class,
                new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build());
    _jobsProcessorDao.insertJob(
        new Job.Builder(
                new ObjectId(DateUtils.addHours(startOfToday, 1)),
                DummyJobHandler.class,
                new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build());
    _jobsProcessorDao.insertJob(
        new Job.Builder(
                new ObjectId(DateUtils.addHours(startOfToday, 2)),
                DummyJobHandler.class,
                new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build());
    _jobsProcessorDao.insertJob(
        new Job.Builder(
                new ObjectId(DateUtils.addMinutes(DateUtils.addDays(startOfToday, 1), 1)),
                DummyJobHandler.class,
                new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build());
    final long count =
        _jobsProcessorDao.countAllJobsForHandlerAndDate(DummyJobHandler.class, startOfToday);
    assertEquals(2, count);
  }

  @Test
  public void testCountIncompleteJobsForHandlerAndDate() {
    final Date startOfToday = DateUtils.truncate(new Date(), Calendar.DATE);
    _jobsProcessorDao.insertJob(
        new Job.Builder(
                new ObjectId(DateUtils.addHours(startOfToday, -1)),
                DummyJobHandler.class,
                new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build());
    final Job job1 =
        new Job.Builder(
                new ObjectId(DateUtils.addHours(startOfToday, 1)),
                DummyJobHandler.class,
                new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    _jobsProcessorDao.insertJob(job1);
    _jobsProcessorDao.insertJob(
        new Job.Builder(
                new ObjectId(DateUtils.addHours(startOfToday, 2)),
                DummyJobHandler.class,
                new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build());
    _jobsProcessorDao.insertJob(
        new Job.Builder(
                new ObjectId(DateUtils.addMinutes(DateUtils.addDays(startOfToday, 1), 1)),
                DummyJobHandler.class,
                new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build());

    _jobsProcessorDao.markJobAsCompleted(job1.getId());
    final long count =
        _jobsProcessorDao.countIncompleteJobsForHandlerAndDate(DummyJobHandler.class, startOfToday);
    assertEquals(1, count);
  }

  @Test
  public void testCountIncompleteJobsForHandlerBetweenDates() {
    final Date minDate = DateUtils.truncate(new Date(), Calendar.DATE);
    final Date maxDate = DateUtils.addDays(minDate, 1);
    Stream.of(
            DateUtils.addDays(minDate, -1),
            minDate,
            DateUtils.addHours(minDate, 1),
            maxDate,
            DateUtils.addDays(maxDate, 1))
        .map(
            date ->
                new Job.Builder(new ObjectId(date), DummyJobHandler.class, new BasicDBObject())
                    .retriesRemaining(Job.NO_RETRY)
                    .intervalUntilRerun(Duration.ZERO)
                    .build())
        .forEach(_jobsProcessorDao::insertJob);
    final long count =
        _jobsProcessorDao.countIncompleteJobsForHandlerBetweenDates(
            DummyJobHandler.class, minDate, maxDate);
    assertEquals(2, count); // minDate and minDate+1h are in the range [minDate, maxDate)
  }

  @Test
  public void testFindIncompleteJobsForHandlerAndDate() {
    final Date minDate = DateUtils.truncate(new Date(), Calendar.DATE);
    final Date maxDate = DateUtils.addDays(minDate, 1);
    ObjectId expId1 = new ObjectId(minDate);
    ObjectId expId2 = new ObjectId(DateUtils.addHours(minDate, 1));
    Stream.of(
            new ObjectId(DateUtils.addDays(minDate, -1)),
            expId1,
            expId2,
            new ObjectId(maxDate),
            new ObjectId(DateUtils.addDays(maxDate, 1)))
        .map(
            id ->
                new Job.Builder(id, DummyJobHandler.class, new BasicDBObject())
                    .retriesRemaining(Job.NO_RETRY)
                    .intervalUntilRerun(Duration.ZERO)
                    .build())
        .forEach(_jobsProcessorDao::insertJob);
    // minDate and minDate+1h are in the range [minDate, maxDate)
    List<ObjectId> expected = List.of(expId1, expId2);
    final List<ObjectId> actual =
        _jobsProcessorDao
            .findIncompleteJobsForHandlerAndDate(DummyJobHandler.class, minDate)
            .stream()
            .map(Job::getId)
            .toList();
    assertEquals(expected, actual);
  }

  @Test
  public void testMarkJobAsFailedWithDelay() {
    final ObjectId jobId = new ObjectId();
    final Job job1 =
        new Job.Builder(jobId, DummyJobHandler.class, new BasicDBObject())
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId jobId1 = _jobsProcessorDao.putJob(job1);
    final long delayTimeInMillis = 900000L;

    _jobsProcessorDao.markJobAsFailedWithDelay(jobId, "error message", delayTimeInMillis);
    final Job completedJob1 = _jobsProcessorDao.getJob(jobId1);
    assertEquals(Job.Status.FAILED, completedJob1.getStatus());
    assertNotNull(completedJob1.getCompletedAt());
    assertEquals("error message", completedJob1.getErrMsg());
    assertNull(completedJob1.getExpireAt());

    final long expectedScheduledTime = (new Date()).getTime() + delayTimeInMillis;
    final long differenceBetweenTimes = expectedScheduledTime - completedJob1.getScheduledFor();
    // since the times will not be exact, we want to verify they are within a second of each other
    assertTrue(differenceBetweenTimes < 1000);
  }

  @Test
  public void testLegacyTakeNextJobsShufflesJobsDisabled() {
    _appSettings.setProperty(JOB_QUEUE_SHUFFLING, false);
    final ServerTags noServerTags = ServerTags.getDisabled("me");
    final List<ObjectId> jobIds = new ArrayList<>();
    final long now = now().toEpochMilli();
    final int jobSize = 20;

    for (int i = jobSize; i > 0; --i) {
      final Job job =
          new Job.Builder(DummyJobHandler.class, null)
              .status(Job.Status.NEW)
              .scheduledFor(now - i)
              .build();
      jobIds.add(_jobsProcessorDao.putJob(job));
    }

    final List<ObjectId> nextJobIds =
        _jobsProcessorDao.legacyTakeNextJobs("me", noServerTags, 20).stream()
            .map(BaseJob::getId)
            .collect(Collectors.toList());
    assertEquals(nextJobIds, jobIds);
  }

  @Test
  public void testLegacyTakeNextJobsShufflesJobsEnabled() {
    _appSettings.setProperty(JOB_QUEUE_SHUFFLING, true);
    final ServerTags noServerTags = ServerTags.getDisabled("me");
    final List<ObjectId> jobIds = new ArrayList<>();
    final long now = now().toEpochMilli();
    final int jobSize = 20;

    for (int i = jobSize; i > 0; --i) {
      final Job job =
          new Job.Builder(DummyJobHandler.class, null)
              .status(Job.Status.NEW)
              .scheduledFor(now - i)
              .build();
      jobIds.add(_jobsProcessorDao.putJob(job));
    }

    final List<ObjectId> nextJobIds =
        _jobsProcessorDao.legacyTakeNextJobs("me", noServerTags, 20).stream()
            .map(BaseJob::getId)
            .collect(Collectors.toList());
    assertNotEquals(nextJobIds, jobIds);
  }

  @Test
  public void testMarkJobAsFinalizedExhaustedFailure() {
    final ObjectId jobId = new ObjectId();
    final Job job =
        new Job.Builder(jobId, DummyJobHandler.class, new BasicDBObject())
            .retriesRemaining(0)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    _jobsProcessorDao.putJob(job);

    _jobsProcessorDao.markJobAsFinalizedExhaustedFailure(jobId);
    final Job exhaustedJob = _jobsProcessorDao.getJob(jobId);
    assertEquals(exhaustedJob.getStatus(), Status.FINALIZED_EXHAUSTED_FAIL);
    assertTrue(
        exhaustedJob.getExpireAt().getTime()
            > exhaustedJob.getLastUpdated() + JOB_EXPIRATION.toMillis());
  }

  @Test
  public void testResetFailedSingleJobsWithRemainingRetries() {
    final ObjectId jobId = new ObjectId();
    _jobsProcessorDao.putJob(
        new Job.Builder(jobId, DummyJobHandler.class, new BasicDBObject())
            .status(Status.FAILED)
            .type(Type.SINGLE)
            .retriesRemaining(1)
            .build());
    _jobsProcessorDao.resetFailedSingleJobsWithRemainingRetries();

    final Job job = _jobsProcessorDao.getJob(jobId);
    assertEquals(job.getScheduledFor(), job.getLastUpdated() + RETRY_WAIT_TIME);
    assertEquals(job.getRetriesRemaining(), 0);
    assertNull(job.getOwnedAt());
    assertNull(job.getOwner());
    assertNull(job.getCompletedAt());
    assertNull(job.getExpireAt());
  }

  @Test
  public void shouldResetAllJobsOwnedNotByGivenProcessesAndOlderThanActiveProcessPeriod() {
    final Instant jobOwnedAt = now().minus(_pingerSettings.getActiveProcessPeriod().plusMinutes(1));
    final List<String> allOwners = List.of(OWNER_1_0, OWNER_2_0, OWNER_2_1, OWNER_3_0, OWNER_3_1);
    final List<ObjectId> singleJobs = givenOwnedJobs(Type.SINGLE, jobOwnedAt, allOwners);
    final List<ObjectId> recurringJobs = givenOwnedJobs(Type.RECURRING, jobOwnedAt, allOwners);
    final List<ObjectId> youngJobs = givenOwnedJobs(Type.SINGLE, now(), allOwners);

    Set<Pair<ObjectId, String>> result =
        _jobsProcessorDao.findAllJobsOwnedNotBy(Set.of(OWNER_1_0, OWNER_3_1, OWNER_3_0));
    for (final Pair<ObjectId, String> staleJob : result) {
      _jobsProcessorDao.resetJobOwnedByInactiveInstance(staleJob.getLeft(), staleJob.getRight());
    }

    assertThat(
        result.stream().map(Pair::getLeft).collect(toSet()),
        hasItems(singleJobs.get(1), singleJobs.get(2), recurringJobs.get(1), recurringJobs.get(2)));

    assertNewJobs(
        List.of(singleJobs.get(1), singleJobs.get(2), recurringJobs.get(1), recurringJobs.get(2)));
    assertOwnedJobs(List.of(singleJobs.get(0), singleJobs.get(3), singleJobs.get(4)));
    assertOwnedJobs(List.of(recurringJobs.get(0), recurringJobs.get(3), recurringJobs.get(4)));
    assertOwnedJobs(youngJobs);
  }

  @Test
  public void shouldNotResetJobsYoungerThanActiveProcessPeriod() {
    final List<String> allOwners = List.of(OWNER_1_0, OWNER_2_0, OWNER_2_1, OWNER_3_0, OWNER_3_1);
    final List<ObjectId> youngJobs = givenOwnedJobs(Type.SINGLE, now(), allOwners);

    Set<Pair<ObjectId, String>> result =
        _jobsProcessorDao.findAllJobsOwnedNotBy(Set.of(OWNER_1_0, OWNER_3_1, OWNER_3_0));
    for (final Pair<ObjectId, String> staleJob : result) {
      _jobsProcessorDao.resetJobOwnedByInactiveInstance(staleJob.getLeft(), staleJob.getRight());
    }

    assertThat(result, hasSize(0));
    assertOwnedJobs(youngJobs);
  }

  @Test
  public void shouldResetAllJobsWhenGivenProcessesIsEmptySet() {
    final Instant jobOwnedAt = now().minus(_pingerSettings.getActiveProcessPeriod().plusMinutes(1));
    final List<String> allOwners = List.of(OWNER_1_0, OWNER_2_0, OWNER_2_1, OWNER_3_0, OWNER_3_1);
    final List<ObjectId> singleJobs = givenOwnedJobs(Type.SINGLE, jobOwnedAt, allOwners);
    final List<ObjectId> recurringJobs = givenOwnedJobs(Type.RECURRING, jobOwnedAt, allOwners);

    Set<Pair<ObjectId, String>> result = _jobsProcessorDao.findAllJobsOwnedNotBy(Set.of());
    for (final Pair<ObjectId, String> staleJob : result) {
      _jobsProcessorDao.resetJobOwnedByInactiveInstance(staleJob.getLeft(), staleJob.getRight());
    }

    assertNewJobs(List.of(singleJobs.get(1), singleJobs.get(2)));
    assertNewJobs(List.of(recurringJobs.get(1), recurringJobs.get(2)));
    assertNewJobs(List.of(singleJobs.get(0), singleJobs.get(3), singleJobs.get(4)));
    assertNewJobs(List.of(recurringJobs.get(0), recurringJobs.get(3), recurringJobs.get(4)));
  }

  @Test
  public void shouldResetAllJobsWhenGivenProcessesIsNull() {
    final Instant jobOwnedAt = now().minus(_pingerSettings.getActiveProcessPeriod().plusMinutes(1));
    final List<String> allOwners = List.of(OWNER_1_0, OWNER_2_0, OWNER_2_1, OWNER_3_0, OWNER_3_1);
    final List<ObjectId> singleJobs = givenOwnedJobs(Type.SINGLE, jobOwnedAt, allOwners);
    final List<ObjectId> recurringJobs = givenOwnedJobs(Type.RECURRING, jobOwnedAt, allOwners);

    Set<Pair<ObjectId, String>> result = _jobsProcessorDao.findAllJobsOwnedNotBy(Set.of());
    for (final Pair<ObjectId, String> staleJob : result) {
      _jobsProcessorDao.resetJobOwnedByInactiveInstance(staleJob.getLeft(), staleJob.getRight());
    }

    assertNewJobs(List.of(singleJobs.get(1), singleJobs.get(2)));
    assertNewJobs(List.of(recurringJobs.get(1), recurringJobs.get(2)));
    assertNewJobs(List.of(singleJobs.get(0), singleJobs.get(3), singleJobs.get(4)));
    assertNewJobs(List.of(recurringJobs.get(0), recurringJobs.get(3), recurringJobs.get(4)));
  }

  @Test
  public void shouldResetAllJobsWithStaleHeartbeat() {
    _appSettings.setProperty(OWNER_THREAD_HEARTBEAT_USE_DISABLED, false);
    final Instant jobHeartbeatedAt =
        now()
            .minus(
                _jobQueueSettings
                    .getMaxAllowedHeartbeatStalenessMillis(DEFAULT_POOL)
                    .plusMinutes(1));
    final List<String> allOwners = List.of(OWNER_1_0, OWNER_2_0);
    final List<ObjectId> singleJobs = givenOwnedJobs(Type.SINGLE, jobHeartbeatedAt, allOwners);
    final List<ObjectId> recurringJobs =
        givenOwnedJobs(Type.RECURRING, jobHeartbeatedAt, allOwners);
    final List<ObjectId> youngJobs = givenOwnedJobs(Type.SINGLE, now(), allOwners);

    Set<ObjectId> result =
        _jobsProcessorDao.resetJobsWithStaleHeartbeats(
            _jobQueueSettings.getStaleJobResetLimitPerRound(), DEFAULT_POOL);

    assertThat(result, hasItems(singleJobs.toArray(new ObjectId[0])));
    assertThat(result, hasItems(recurringJobs.toArray(new ObjectId[0])));
    assertThat(result, not(hasItems(youngJobs.toArray(new ObjectId[0]))));

    assertNewJobs(singleJobs);
    assertNewJobs(recurringJobs);
    assertOwnedJobs(youngJobs);
  }

  @Test
  public void shouldResetStaleJobsWithoutHeartbeat() {
    _appSettings.setProperty(OWNER_THREAD_HEARTBEAT_USE_DISABLED, false);
    final Instant jobHeartbeatedAt =
        now()
            .minus(
                _jobQueueSettings
                    .getMaxAllowedHeartbeatStalenessMillis(DEFAULT_POOL)
                    .plusMinutes(1));

    // make an owned stale job with no heartbeat
    final ObjectId jobId =
        _jobsProcessorDao.putJob(
            new Job.Builder(ObjectId.get(), DummyJobHandler.class, new BasicDBObject())
                .status(Status.OWNED)
                .owner(OWNER_3_1)
                .pool(DEFAULT_POOL)
                .ownedAt(Date.from(jobHeartbeatedAt))
                .updated(jobHeartbeatedAt.toEpochMilli())
                .type(Type.SINGLE)
                .retriesRemaining(1)
                .build());

    final List<ObjectId> youngJobs =
        givenOwnedJobs(Type.SINGLE, now(), List.of(OWNER_1_0, OWNER_2_0));

    Set<ObjectId> result =
        _jobsProcessorDao.resetJobsWithStaleHeartbeats(
            _jobQueueSettings.getStaleJobResetLimitPerRound(), DEFAULT_POOL);

    assertThat(result, hasItem(jobId));
    assertThat(result, not(hasItems(youngJobs.toArray(new ObjectId[0]))));

    assertNewJobs(List.of(jobId));
    assertOwnedJobs(youngJobs);
  }

  @Test
  public void shouldNotResetJobsWithActiveHeartbeat() {
    _appSettings.setProperty(OWNER_THREAD_HEARTBEAT_USE_DISABLED, false);
    final List<String> allOwners = List.of(OWNER_1_0, OWNER_2_0, OWNER_2_1, OWNER_3_0, OWNER_3_1);
    final List<ObjectId> youngJobs = givenOwnedJobs(Type.SINGLE, now(), allOwners);

    Set<ObjectId> result =
        _jobsProcessorDao.resetJobsWithStaleHeartbeats(
            _jobQueueSettings.getStaleJobResetLimitPerRound(), DEFAULT_POOL);

    assertThat(result, hasSize(0));
    assertOwnedJobs(youngJobs);
  }

  @Test
  public void shouldNotResetNonStaleJobWithoutHeartbeat() {
    _appSettings.setProperty(OWNER_THREAD_HEARTBEAT_USE_DISABLED, false);
    // make a job owned now with no heartbeat
    final ObjectId jobId =
        _jobsProcessorDao.putJob(
            new Job.Builder(ObjectId.get(), DummyJobHandler.class, new BasicDBObject())
                .status(Status.OWNED)
                .owner(OWNER_3_1)
                .pool(DEFAULT_POOL)
                .ownedAt(Date.from(now()))
                .updated(now().toEpochMilli())
                .type(Type.SINGLE)
                .retriesRemaining(1)
                .build());

    Set<ObjectId> result =
        _jobsProcessorDao.resetJobsWithStaleHeartbeats(
            _jobQueueSettings.getStaleJobResetLimitPerRound(), DEFAULT_POOL);

    assertThat(result, hasSize(0));
    assertOwnedJobs(List.of(jobId));
  }

  @Test
  public void shouldHonorHeartbeatPoolOptOut() {
    // start with heartbeat use disabled globally
    _appSettings.setProperty(OWNER_THREAD_HEARTBEAT_USE_DISABLED, true);

    final Instant jobHeartbeatedAt =
        now()
            .minus(
                _jobQueueSettings
                    .getMaxAllowedHeartbeatStalenessMillis(DEFAULT_POOL)
                    .plusMinutes(1));
    final List<String> allOwners = List.of(OWNER_1_0, OWNER_2_0);
    final List<ObjectId> singleJobs = givenOwnedJobs(Type.SINGLE, jobHeartbeatedAt, allOwners);

    Set<ObjectId> result =
        _jobsProcessorDao.resetJobsWithStaleHeartbeats(
            _jobQueueSettings.getStaleJobResetLimitPerRound(), DEFAULT_POOL);

    // It still returns stale jobs
    assertThat(result, hasItems(singleJobs.toArray(new ObjectId[0])));
    // But it doesn't reset them to new (should still be owned)
    assertOwnedJobs(singleJobs);

    // Now add the pool to the opt out list and enable heartbeat use globally
    _appSettings.setProperty(OWNER_THREAD_HEARTBEAT_USE_DISABLED, false);
    _appSettings.setProperty(SHOULD_USE_OWNER_THREAD_HEARTBEAT_POOL_OPT_OUT_LIST, DEFAULT_POOL);

    result =
        _jobsProcessorDao.resetJobsWithStaleHeartbeats(
            _jobQueueSettings.getStaleJobResetLimitPerRound(), DEFAULT_POOL);

    // It still returns stale jobs
    assertThat(result, hasItems(singleJobs.toArray(new ObjectId[0])));
    // But it doesn't reset them to new (should still be owned)
    assertOwnedJobs(singleJobs);

    // Now remove it from the opt out list
    _appSettings.setProperty(SHOULD_USE_OWNER_THREAD_HEARTBEAT_POOL_OPT_OUT_LIST, "");
    result =
        _jobsProcessorDao.resetJobsWithStaleHeartbeats(
            _jobQueueSettings.getStaleJobResetLimitPerRound(), DEFAULT_POOL);
    assertThat(result, hasItem(singleJobs.get(0)));
    assertNewJobs(List.of(singleJobs.get(0)));
  }

  @Test
  public void shouldPutJobToTempCollectionWhenWriteBlockIsEnabled() {
    final Job job =
        new Job.Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .type(Type.SINGLE)
            .retriesRemaining(1)
            .build();

    final Map<String, Object> cutoverProps =
        Map.of("mongo.mmsdbjobs.cutover.writes.enabled", FALSE.toString());
    MongoClientRuntimeSettingsCache.update(cutoverProps);

    _jobsProcessorDao.putJob(job);

    MongoClientRuntimeSettingsCache.removeConfig("mongo.mmsdbjobs.cutover.writes.enabled");

    assertThat(_jobsProcessorDao.getJob(job.getId()), nullValue());
    final Job tempJob = getTempJob(job.getId());
    assertThat(tempJob, notNullValue());
    assertThat(tempJob.getStatus(), is(Status.NEW));
  }

  @Test
  public void shouldReturnEmptySetWhenGivenIdSetIsNull() {
    final long now = System.currentTimeMillis();
    final Set<ObjectId> ids = _jobsProcessorDao.filterOutExpiredAndTimeoutJobs(null, now);
    assertThat(ids, empty());
  }

  @Test
  public void shouldReturnEmptySetWhenGivenIdSetIsEmpty() {
    final long now = System.currentTimeMillis();
    final Set<ObjectId> ids = _jobsProcessorDao.filterOutExpiredAndTimeoutJobs(Set.of(), now);
    assertThat(ids, empty());
  }

  @Test
  public void shouldFilterExistingJobsFromGivenIds() {
    final long now = System.currentTimeMillis();
    final List<Job> existingJobs =
        List.of(
            givenJob(Status.NEW, now + 1),
            givenJob(Status.OWNED, now + 1),
            givenJob(Status.COMPLETED, now + 1));
    final Set<ObjectId> existingJobIds = existingJobs.stream().map(Job::getId).collect(toSet());

    final Set<ObjectId> givenJobIds = new HashSet<>(existingJobIds);
    givenJobIds.add(ObjectId.get());
    givenJobIds.add(ObjectId.get());

    final Set<ObjectId> filterJobIds =
        _jobsProcessorDao.filterOutExpiredAndTimeoutJobs(givenJobIds, now);

    MongoClientRuntimeSettingsCache.removeConfig("mongo.mmsdbjobs.cutover.writes.enabled");

    assertThat(filterJobIds, is(existingJobIds));
  }

  @Test
  public void shouldFilterNotTimeoutJobsFromGivenIds() {
    final long now = System.currentTimeMillis();
    final List<Job> existingJobs =
        List.of(
            givenJob(Status.NEW, now - 1),
            givenJob(Status.OWNED, now - 1),
            givenJob(Status.COMPLETED, now - 1));

    final Set<ObjectId> givenJobIds = existingJobs.stream().map(Job::getId).collect(toSet());

    final Set<ObjectId> filterJobIds =
        _jobsProcessorDao.filterOutExpiredAndTimeoutJobs(givenJobIds, now);

    assertThat(filterJobIds, hasSize(2));
    assertThat(filterJobIds, hasItems(existingJobs.get(0).getId(), existingJobs.get(2).getId()));
  }

  @Test
  public void shouldInsertJobToTempCollectionWhenWriteBlockIsEnabled() {
    final Job job =
        new Job.Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .type(Type.SINGLE)
            .retriesRemaining(1)
            .build();

    final Map<String, Object> cutoverProps =
        Map.of("mongo.mmsdbjobs.cutover.writes.enabled", FALSE.toString());
    MongoClientRuntimeSettingsCache.update(cutoverProps);

    _jobsProcessorDao.insertJob(job);

    MongoClientRuntimeSettingsCache.removeConfig("mongo.mmsdbjobs.cutover.writes.enabled");

    assertThat(_jobsProcessorDao.getJob(job.getId()), nullValue());
    final Job tempJob = getTempJob(job.getId());
    assertThat(tempJob, notNullValue());
    assertThat(tempJob.getStatus(), is(Status.NEW));
  }

  @Test
  public void shouldPutRecurringJobToTempCollectionWhenWriteBlockIsEnabled() {
    final Job job =
        new Job.Builder(DummyJobHandler.class, new BasicDBObject())
            .type(Type.RECURRING)
            .handlerId(DummyJobHandler.class.getName())
            .retriesRemaining(1)
            .build();

    final Map<String, Object> cutoverProps =
        Map.of("mongo.mmsdbjobs.cutover.writes.enabled", FALSE.toString());
    MongoClientRuntimeSettingsCache.update(cutoverProps);

    _jobsProcessorDao.putRecurringJob(job);

    MongoClientRuntimeSettingsCache.removeConfig("mongo.mmsdbjobs.cutover.writes.enabled");

    assertThat(_jobsProcessorDao.getJob(job.getId()), nullValue());
    final Job tempJob = getTempJob(job.getId());
    assertThat(tempJob, notNullValue());
    assertThat(tempJob.getStatus(), is(Status.NEW));
    assertThat(tempJob.getType(), is(Type.RECURRING));
  }

  private Job getTempJob(ObjectId jobId) {
    final DBCollection tempCollection =
        _jobsProcessorDao.getDb().getCollection(TEMP_COLLECTION_NAME);
    final DBObject dbObject =
        jobId != null ? tempCollection.findOne(jobId) : tempCollection.findOne();
    return _morphia.fromDBObject(_morphiaDs, Job.class, dbObject);
  }

  private void assertNewJobs(List<ObjectId> jobIds) {
    final List<Job> jobs = _jobsProcessorDao.findJobsByIds(jobIds);
    assertThat(jobs, hasSize(jobIds.size()));
    for (Job job : jobs) {
      assertThat(job.getStatus(), is(Status.NEW));
      assertThat(job.getScheduledFor(), notNullValue());
      assertThat(job.getLastUpdated(), is(job.getScheduledFor()));
      assertThat(job.getOwnedAt(), nullValue());
      assertThat(job.getOwner(), nullValue());
      assertThat(job.getCompletedAt(), nullValue());
    }
  }

  private void assertOwnedJobs(List<ObjectId> jobIds) {
    final List<Job> jobs = _jobsProcessorDao.findJobsByIds(jobIds);
    assertThat(jobs, hasSize(jobIds.size()));
    for (Job job : jobs) {
      assertThat(job.getStatus(), is(Status.OWNED));
      assertThat(job.getOwnedAt(), notNullValue());
      assertThat(job.getOwner(), notNullValue());
    }
  }

  private List<ObjectId> givenOwnedJobs(Type jobType, Instant ownedAt, List<String> owners) {
    return owners.stream()
        .map(
            owner ->
                _jobsProcessorDao.putJob(
                    new Job.Builder(ObjectId.get(), DummyJobHandler.class, new BasicDBObject())
                        .status(Status.OWNED)
                        .owner(owner)
                        .ownedAt(Date.from(ownedAt))
                        .pool(DEFAULT_POOL)
                        .heartbeatedAt(Date.from(ownedAt))
                        .updated(ownedAt.toEpochMilli())
                        .type(jobType)
                        .retriesRemaining(1)
                        .build()))
        .collect(Collectors.toList());
  }

  @Test
  public void testMarkJobAsReset() {
    final ObjectId jobId = new ObjectId();
    _jobsProcessorDao.putJob(
        new Job.Builder(jobId, DummyJobHandler.class, new BasicDBObject())
            .status(Status.OWNED)
            .type(Type.SINGLE)
            .retriesRemaining(1)
            .build());
    _jobsProcessorDao.markJobAsReset(jobId, 1000);

    final Job job = _jobsProcessorDao.getJob(jobId);
    assertEquals(job.getStatus(), Job.Status.NEW);
    assertEquals(job.getScheduledFor(), job.getLastUpdated() + 1000);
    assertNull(job.getOwnedAt());
    assertNull(job.getOwner());
    assertNull(job.getCompletedAt());
    assertNull(job.getExpireAt());
  }

  @Test
  public void testMarkJobAsNew() {
    final ObjectId jobId = new ObjectId();
    _jobsProcessorDao.putJob(
        new Job.Builder(jobId, DummyJobHandler.class, new BasicDBObject())
            .status(Status.OWNED)
            .type(Type.SINGLE)
            .retriesRemaining(1)
            .build());
    _jobsProcessorDao.markJobAsNew(jobId);

    final Job job = _jobsProcessorDao.getJob(jobId);
    assertEquals(job.getStatus(), Job.Status.NEW);
    assertEquals(job.getScheduledFor(), job.getLastUpdated());
    assertNull(job.getOwnedAt());
    assertNull(job.getOwner());
    assertNull(job.getCompletedAt());
    assertNull(job.getExpireAt());
  }

  @Test
  public void testMarkJobAsNewWithScheduledForDate() {
    final ObjectId jobId = new ObjectId();
    _jobsProcessorDao.putJob(
        new Job.Builder(jobId, DummyJobHandler.class, new BasicDBObject())
            .status(Status.OWNED)
            .type(Type.SINGLE)
            .retriesRemaining(1)
            .build());
    _jobsProcessorDao.markJobAsNewWithScheduledForDate(jobId, 42);

    final Job job = _jobsProcessorDao.getJob(jobId);
    assertEquals(job.getStatus(), Job.Status.NEW);
    assertEquals(job.getScheduledFor(), 42);
    assertNotEquals(job.getScheduledFor(), job.getLastUpdated());
    assertNull(job.getOwnedAt());
    assertNull(job.getOwner());
    assertNull(job.getCompletedAt());
    assertNull(job.getExpireAt());
  }

  @Test
  public void testMarkJobAsNewWithRetries() {
    final ObjectId jobId = new ObjectId();
    _jobsProcessorDao.putJob(
        new Job.Builder(jobId, DummyJobHandler.class, new BasicDBObject())
            .status(Status.RETRY_EXHAUSTED_FAIL)
            .type(Type.SINGLE)
            .retriesRemaining(0)
            .build());

    _jobsProcessorDao.markJobAsNewWithRetries(jobId, 3);

    final Job job = _jobsProcessorDao.getJob(jobId);
    assertEquals(job.getStatus(), Job.Status.NEW);
    assertEquals(job.getScheduledFor(), job.getLastUpdated());
    assertNull(job.getOwnedAt());
    assertNull(job.getOwner());
    assertNull(job.getCompletedAt());
    assertNull(job.getExpireAt());
    assertEquals(3, job.getRetriesRemaining());
  }

  @Test
  public void testMarkJobAsCompletedAndExpiredJobsHaveBeenRemoved() {
    final Job job =
        new Job.Builder(DummyJobHandler.class, new BasicDBObject())
            .retriesRemaining(Job.NO_RETRY)
            .intervalUntilRerun(Duration.ZERO)
            .build();
    final ObjectId jobId = _jobsProcessorDao.putJob(job);

    final Date now = new Date();
    final Job updatedJob = _jobsProcessorDao.markJobAsCompletedAndExpired(jobId, now);
    assertEquals(now, updatedJob.getExpireAt());

    await()
        .atMost(ofSeconds(60))
        .until(
            () -> _jobsProcessorDao.findJobsByIds(Collections.singletonList(jobId)).size(),
            equalTo(0));
  }

  @Test
  public void testGenerateJobExpirationMs() {
    long now = System.currentTimeMillis();
    assertTrue(
        now + JOB_EXPIRATION.toMillis()
            <= _jobsProcessorDao.generateJobExpirationMs(now).getTime());
  }

  private Job givenUntaggedJobScheduledFor(long scheduledForTs) {
    var job =
        new Builder(DummyJobHandler.class, null)
            .status(Status.NEW)
            .scheduledFor(scheduledForTs)
            .build();

    var id = _jobsProcessorDao.putJob(job);
    return _jobsProcessorDao.getJob(id);
  }

  @Test
  public void testTakeNextJobs() {
    final Duration interval1 = Duration.ofMinutes(10);
    final Instant now = now();
    final String poolName = "folklore";

    final long oldScheduledFor = now.toEpochMilli() - 100;
    final Job job1_interval1 =
        new Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .intervalUntilRerun(interval1)
            .type(Type.SINGLE)
            .scheduledFor(oldScheduledFor)
            .retriesRemaining(1)
            .pool(poolName)
            .build();
    _jobsProcessorDao.putJob(job1_interval1);

    final Job job2_interval1 =
        new Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .intervalUntilRerun(interval1)
            .type(Type.SINGLE)
            .scheduledFor(oldScheduledFor)
            .retriesRemaining(1)
            .pool(poolName)
            .build();
    _jobsProcessorDao.putJob(job2_interval1);

    final Job job_interval1_owned =
        new Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.OWNED)
            .intervalUntilRerun(interval1)
            .type(Type.SINGLE)
            .owner("lorde")
            .ownedAt(new Date())
            .scheduledFor(oldScheduledFor)
            .retriesRemaining(1)
            .pool(poolName)
            .build();
    _jobsProcessorDao.putJob(job_interval1_owned);

    final Duration interval2 = Duration.ofDays(1);
    final Job job1_interval2 =
        new Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .intervalUntilRerun(interval2)
            .type(Type.SINGLE)
            .scheduledFor(oldScheduledFor)
            .retriesRemaining(1)
            .pool(poolName)
            .build();
    _jobsProcessorDao.putJob(job1_interval2);

    final Job job2_interval2 =
        new Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .intervalUntilRerun(interval2)
            .type(Type.SINGLE)
            .scheduledFor(oldScheduledFor)
            .retriesRemaining(1)
            .pool(poolName)
            .build();
    _jobsProcessorDao.putJob(job2_interval2);

    final String owner = "taylor_swift";
    final long nowAtTakeNextJobs = now.toEpochMilli();
    final Collection<Job> takenJobs =
        _jobsProcessorDao.takeNextJobs(owner, ServerTags.getDisabled("hostname"), 3);

    assertEquals(3, takenJobs.size());
    assertTrue(
        takenJobs.stream()
            .allMatch(
                job ->
                    job.getStatus() == Status.OWNED
                        && job.getOwner().equals(owner)
                        && job.getScheduledFor()
                                - (nowAtTakeNextJobs + job.getIntervalUntilRerun().toMillis())
                            < Duration.ofSeconds(1).toMillis()));

    // already owned job is not taken
    assertTrue(
        takenJobs.stream().noneMatch(job -> job.getId().equals(job_interval1_owned.getId())));

    final List<Job> jobsAfter =
        _jobsProcessorDao.findJobsByIds(
            List.of(
                job1_interval1.getId(),
                job2_interval1.getId(),
                job_interval1_owned.getId(),
                job1_interval2.getId(),
                job2_interval2.getId()));

    // one of the NEW jobs should be untouched
    assertEquals(
        1,
        jobsAfter.stream()
            .filter(
                job -> job.getStatus() == Status.NEW && job.getScheduledFor() == oldScheduledFor)
            .count());
  }

  @Test
  public void testUpdateScheduledForFields() {
    final Duration interval1 = Duration.ofMinutes(10);
    final Instant now = now();

    final long oldScheduledFor = now.toEpochMilli() - 100;
    final Job job1_interval1 =
        new Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .intervalUntilRerun(interval1)
            .type(Type.SINGLE)
            .scheduledFor(oldScheduledFor)
            .retriesRemaining(1)
            .build();
    _jobsProcessorDao.putJob(job1_interval1);

    final Job job2_interval1 =
        new Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .intervalUntilRerun(interval1)
            .type(Type.SINGLE)
            .scheduledFor(oldScheduledFor)
            .retriesRemaining(1)
            .build();
    _jobsProcessorDao.putJob(job2_interval1);

    final Duration interval2 = Duration.ofDays(1);
    final Job job1_interval2 =
        new Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .intervalUntilRerun(interval2)
            .type(Type.SINGLE)
            .scheduledFor(oldScheduledFor)
            .retriesRemaining(1)
            .build();
    _jobsProcessorDao.putJob(job1_interval2);

    final Job job2_interval2 =
        new Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.NEW)
            .intervalUntilRerun(interval2)
            .type(Type.SINGLE)
            .scheduledFor(oldScheduledFor)
            .retriesRemaining(1)
            .build();
    _jobsProcessorDao.putJob(job2_interval2);

    final Set<ObjectId> ids =
        Set.of(
            job1_interval1.getId(),
            job2_interval1.getId(),
            job1_interval2.getId(),
            job2_interval2.getId());

    final String owner = "taylor_swift";
    final long nowAtStart = now.toEpochMilli() - 1000;
    final long nowAtUpdate = now.toEpochMilli();
    _jobsProcessorDao.claimIdentifiedJobs(owner, ids, nowAtStart, nowAtUpdate);

    // claiming doesn't update scheduled for
    final List<Job> retrievedClaimedJobs = _jobsProcessorDao.findJobsByIds(ids);
    assertTrue(
        retrievedClaimedJobs.stream().allMatch(job -> job.getScheduledFor() == oldScheduledFor));

    final List<Job> updatedJobs =
        _jobsProcessorDao.updateScheduledForFields(
            retrievedClaimedJobs, nowAtStart, nowAtUpdate, owner);

    final List<Job> refreshedClaimedJobs = _jobsProcessorDao.findJobsByIds(ids);
    assertTrue(
        refreshedClaimedJobs.stream()
            .allMatch(
                job ->
                    job.getScheduledFor()
                        == now.toEpochMilli() + job.getIntervalUntilRerun().toMillis()));

    assertTrue(refreshedClaimedJobs.containsAll(updatedJobs));
  }

  @Test
  public void testBuildFindClaimedJobsQuery() {
    final Job job1_new = givenJob(Status.NEW, now().toEpochMilli() - 1000);
    final Job job2_new = givenJob(Status.NEW, now().toEpochMilli() - 1001);
    final Job job3_new = givenJob(Status.NEW, now().toEpochMilli() - 1002);
    final Job job1_owned =
        givenOwnedJob(now().toEpochMilli() - 1001, "lorde", new Date(now().toEpochMilli() - 1001));

    final long nowAtStart = Date.from(Instant.now().minus(Duration.ofSeconds(10))).getTime();
    final long nowAtUpdate = now().toEpochMilli();

    final Set<ObjectId> newJobIds = Set.of(job1_new.getId(), job2_new.getId());
    final Set<ObjectId> ownedJobIds = Set.of(job1_owned.getId());

    final Set<ObjectId> ids = SetUtils.union(newJobIds, ownedJobIds);

    final String me = "taylor_swift";
    final List<Job> claimedJobs =
        _jobsProcessorDao.claimIdentifiedJobs(me, ids, nowAtStart, nowAtUpdate);

    _jobsProcessorDao.claimIdentifiedJobs(
        "jack_antonoff", Set.of(job3_new.getId()), nowAtStart, nowAtUpdate);

    final Query<Job> query = _jobsProcessorDao.buildFindClaimedJobsQuery(me, nowAtStart, ids);
    final List<Job> retrievedClaimedJobs = query.find().toList();

    assertEquals(claimedJobs.size(), retrievedClaimedJobs.size());
    assertEquals(newJobIds.size(), retrievedClaimedJobs.size());
    assertTrue(
        claimedJobs.stream().map(Job::getId).collect(Collectors.toSet()).containsAll(newJobIds));
    assertTrue(
        retrievedClaimedJobs.stream()
            .map(Job::getId)
            .collect(Collectors.toSet())
            .containsAll(newJobIds));
  }

  @Test
  public void testClaimIdentifiedJobs() {
    final Job job1_new = givenJob(Status.NEW, now().toEpochMilli() - 1000);
    final Job job2_new = givenJob(Status.NEW, now().toEpochMilli() - 1001);
    final Job job3_new = givenJob(Status.NEW, now().toEpochMilli() - 1002);
    final Job job1_owned =
        givenOwnedJob(
            now().toEpochMilli() - 1001, "aaron_dessner", new Date(now().toEpochMilli() - 1001));

    final long nowAtStart = now().toEpochMilli() - 500;
    final long nowAtUpdate = now().toEpochMilli();

    final Set<ObjectId> newJobIds = Set.of(job1_new.getId(), job2_new.getId());
    final Set<ObjectId> ownedJobIds = Set.of(job1_owned.getId());

    final Set<ObjectId> ids = SetUtils.union(newJobIds, ownedJobIds);

    final String me = "taylor_swift";
    final List<Job> claimedJobs =
        _jobsProcessorDao.claimIdentifiedJobs(me, ids, nowAtStart, nowAtUpdate);

    assertEquals(2, claimedJobs.size());

    // claims jobs
    final List<Job> newJobsAfterUpdate = _jobsProcessorDao.findJobsByIds(newJobIds);
    assertTrue(newJobsAfterUpdate.stream().allMatch(job -> job.getStatus() == Status.OWNED));
    assertTrue(newJobsAfterUpdate.stream().allMatch(job -> job.getOwner().equals(me)));
    assertTrue(
        newJobsAfterUpdate.stream()
            .allMatch(job -> job.getOwnedAt().equals(new Date(nowAtUpdate))));
    assertTrue(newJobsAfterUpdate.stream().allMatch(job -> job.getLastUpdated() == nowAtUpdate));
    assertTrue(
        newJobsAfterUpdate.stream()
            .allMatch(job -> Optional.ofNullable(job.getExecutionId()).isPresent()));

    // does not update jobs that got owned by other workers in the meantime
    final List<Job> ownedJobsAfterUpdate = _jobsProcessorDao.findJobsByIds(ownedJobIds);
    assertTrue(ownedJobsAfterUpdate.stream().allMatch(job -> job.getStatus() == Status.OWNED));
    assertTrue(ownedJobsAfterUpdate.stream().noneMatch(job -> job.getOwner().equals(me)));
  }

  @Test
  public void testBuildFindIdentifiedJobsQuery() {
    final Job job1_new = givenJob(Status.NEW, now().toEpochMilli() - 1000);
    final Job job2_new = givenJob(Status.NEW, now().toEpochMilli() - 1001);
    final Job job3_new = givenJob(Status.NEW, now().toEpochMilli() - 1002);
    final Job job1_owned = givenJob(Status.OWNED, now().toEpochMilli() - 1001);
    final Job job2_owned = givenJob(Status.OWNED, now().toEpochMilli() - 1002);

    final Query<Job> query1 =
        _jobsProcessorDao.buildFindIdentifiedJobsQuery(
            Set.of(job1_new.getId(), job2_new.getId(), job1_owned.getId()));

    // exclude owned jobs & jobs not specified by the query
    assertEquals(2, query1.find().toList().size());
  }

  private Job givenTaggedJobScheduledFor(long scheduledForTs, String... tags) {
    var job =
        new Job.Builder(DummyJobHandler.class, null)
            .status(Job.Status.NEW)
            .scheduledFor(scheduledForTs)
            .tags(List.of(tags))
            .build();
    var id = _jobsProcessorDao.putJob(job);
    return _jobsProcessorDao.getJob(id);
  }

  private Job givenJob(Status status, long scheduleForMs) {
    final Job job =
        new Job.Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(status)
            .type(Type.SINGLE)
            .scheduledFor(scheduleForMs)
            .retriesRemaining(1)
            .build();
    _jobsProcessorDao.putJob(job);
    return job;
  }

  private Job givenOwnedJob(long scheduleForMs, String owner, Date ownedAt) {
    final Job job =
        new Job.Builder(new ObjectId(), DummyJobHandler.class, new BasicDBObject())
            .status(Status.OWNED)
            .type(Type.SINGLE)
            .owner(owner)
            .ownedAt(ownedAt)
            .scheduledFor(scheduleForMs)
            .retriesRemaining(1)
            .build();
    _jobsProcessorDao.putJob(job);
    return job;
  }

  static class DummyJobHandler implements JobHandler {

    public DummyJobHandler() {}

    @Override
    public void handleWork(final BasicDBObject pParameters, final ObjectId pJobId) {}
  }
}
