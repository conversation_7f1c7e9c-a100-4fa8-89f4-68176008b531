load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = True,
    runtime_deps = [
        "//server/src/main/com/xgen/cloud/partnerintegrations/vercelnative/runtime/res",
    ],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/access/rolecheck",
        "//server/src/main/com/xgen/cloud/activity/_public/svc",
        "//server/src/main/com/xgen/cloud/appconfig/_public/config",
        "//server/src/main/com/xgen/cloud/auditinfosvc",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo/_public/model",
        "//server/src/main/com/xgen/cloud/common/authn",
        "//server/src/main/com/xgen/cloud/common/authn/_public/model",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/ratelimit",
        "//server/src/main/com/xgen/cloud/organization/_public/svc",
        "//server/src/main/com/xgen/cloud/partnerintegrations/common/_public/model",
        "//server/src/main/com/xgen/cloud/partnerintegrations/vercelnative/_private/dao",
        "//server/src/main/com/xgen/cloud/partnerintegrations/vercelnative/_private/svc",
        "//server/src/main/com/xgen/cloud/partnerintegrations/vercelnative/_public/config",
        "//server/src/main/com/xgen/cloud/partnerintegrations/vercelnative/_public/constant",
        "//server/src/main/com/xgen/cloud/partnerintegrations/vercelnative/_public/model",
        "//server/src/main/com/xgen/cloud/partnerintegrations/vercelnative/_public/svc",
        "//server/src/main/com/xgen/cloud/partnerintegrations/vercelnative/runtime/res",
        "//server/src/main/com/xgen/cloud/partners/vercel/billing",
        "//server/src/main/com/xgen/cloud/partners/vercel/sdk/model",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/svc",
        "//server/src/main/com/xgen/svc/mms/util/http",
        "//server/src/test/com/xgen/cloud/monitoring/ratelimit/utils",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/testlib/junit5/extensions/guicetest",
        "@maven//:com_auth0_java_jwt",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
