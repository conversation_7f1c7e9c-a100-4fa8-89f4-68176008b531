package com.xgen.cloud.partnerintegrations.vercelnative.runtime.res.v1;

import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.RATE_LIMIT_POLICY_PREFIX;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.auditinfosvc._public.svc.AuditInfoSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authn._public.model.PartnerIdentityType;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited.Type;
import com.xgen.cloud.monitoring.ratelimit._public.model.RateLimitSuspension.RateLimitType;
import com.xgen.cloud.monitoring.ratelimit._public.svc.RateLimitSuspensionSvc;
import com.xgen.cloud.monitoring.ratelimit.utils.RateLimitedResourceIntTestUtils;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.partnerintegrations.common._public.model.VercelRole;
import com.xgen.cloud.partnerintegrations.vercelnative._private.dao.VercelNativeInstallationDao;
import com.xgen.cloud.partnerintegrations.vercelnative._private.svc.VercelNativeInstallationsSvcImpl;
import com.xgen.cloud.partnerintegrations.vercelnative._public.config.VercelNativeConfig;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.AtlasResources;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.InstalledProduct;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelAuthSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeAccountSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeInstallationsDaoSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeInstallationsSvc;
import com.xgen.cloud.partners.vercel.billing._public.config.VercelBillingConfig;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInstallationBillingHandler;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInstallationSvc;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.InstallationGeneral200Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelContact;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.delete.DeleteInstallationRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.delete.DeleteInstallationResult;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.update.UpdateInstallationRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.upsert.UpsertInstallationRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.upsert.UpsertInstallationRequestAccount;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.upsert.UpsertInstallationRequestAccountContact;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.upsert.UpsertInstallationRequestCredentials;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.plan.VercelBillingPlanId;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.UserMetadata;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceStatus;
import com.xgen.cloud.user._private.svc.UserSvcDb;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.util.http.HttpUtils.HttpRequestBuilder;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class VercelNativeInstallationsResourceIntTests extends JUnit5BaseResourceTest {

  @Inject private AppSettings appSettings;
  @Inject private RateLimitedResourceIntTestUtils rateLimitedResourceIntTestUtils;
  @Inject private RateLimitSuspensionSvc rateLimitSuspensionSvc;
  @Inject private ObjectMapper objectMapper;
  @Inject private VercelNativeInstallationDao vercelNativeInstallationDao;
  @Inject private VercelNativeConfig vercelNativeConfig;
  @Inject private VercelInstallationSvc vercelInstallationBillingSvc;
  @Inject private VercelBillingConfig vercelBillingConfig;
  @Inject private VercelAuthSvc vercelAuthSvc;
  // used in getStubbedOutInstallationSvc
  private final VercelInstallationBillingHandler installationBillingHandler =
      spy(AppConfig.getInstance(VercelInstallationBillingHandler.class));
  private final VercelNativeAccountSvc vercelNativeAccountSvc =
      spy(AppConfig.getInstance(VercelNativeAccountSvc.class));

  // Spoofing an installation ID (42424242424242424242)
  final String TEST_INSTALLATION_ID = "42424242424242424242";
  final String RATE_LIMITING_API_PATH =
      "/api/private/vercelnative/v1/installations/42424242424242424242";
  final String UPSERT_INSTALLATION_ENDPOINT =
      "/api/private/vercelnative/v1/installations/new-installation-123";
  final String IDEMPOTENCY_KEY = "test-idempotency-key";

  private AppUser user;
  private String authToken;
  private AppUser systemUser;
  private String systemAuthToken;
  private final String rateLimiterName = "vercelNative";

  private final String mockAccessToken = "test-access-token";

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    appSettings.setProp("mms.userSvcClass", UserSvcDb.class.getSimpleName(), SettingType.MEMORY);
    var org = MmsFactory.createOrganizationWithNDSPlan();
    user = MmsFactory.createPartnerApiUserWithRoleInOrganization("adminUser", org, Role.ORG_OWNER);
    authToken = getAuthnBackedPartnerToken(user);
    systemUser =
        MmsFactory.createPartnerApiUserWithRoleInOrganization("systemUser", org, Role.ORG_OWNER);
    systemAuthToken =
        getAuthnBackedPartnerToken(systemUser, PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER);
  }

  @AfterEach
  public void tearDown() {
    appSettings.clearMemory();
  }

  @Test
  public void testRateLimiting_hasRateLimitingBySuspendableIp() {
    rateLimitedResourceIntTestUtils.setRateLimitingConfig(
        Type.SUSPENDABLE_IP, RATE_LIMIT_POLICY_PREFIX, rateLimiterName);

    // An alternative is to create an Okta user in memory, which would be closer to the 'real' flow,
    // but for simplicity in testing's sake, we will use an api key
    // These first requests should get rate-limited by IP
    rateLimitedResourceIntTestUtils.expectJsonRateLimiting(
        getHttpUtils()
            .put()
            .data(new JSONObject())
            .path(RATE_LIMITING_API_PATH)
            .addHeader(
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    TEST_INSTALLATION_ID))
            .initiatorAuth(authToken));
    //  No rate-limiting should happen with a different IP
    rateLimitedResourceIntTestUtils.expectNoRateLimiting(
        getHttpUtils()
            .put()
            .data(new JSONObject())
            .path(RATE_LIMITING_API_PATH)
            .initiatorAuth(authToken)
            .addHeader(
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    TEST_INSTALLATION_ID))
            .headers(Map.of(appSettings.getRemoteIPHeader(), UUID.randomUUID().toString())));
  }

  @Test
  public void testRateLimiting_ipRateLimitingCanBeSuspended() {
    HttpRequestBuilder request =
        getHttpUtils().put().data(new JSONObject()).path(RATE_LIMITING_API_PATH);

    suspendIpRateLimiting();
    rateLimitedResourceIntTestUtils.setRateLimitingConfig(
        Type.SUSPENDABLE_IP, RATE_LIMIT_POLICY_PREFIX, rateLimiterName);

    // After suspension of rate limiting, no rate-limiting should occur when a call is made to the
    // endpoint
    rateLimitedResourceIntTestUtils.expectNoRateLimiting(request);
  }

  @Test
  public void testRateLimiting_hasRateLimitingByPathParam() {
    rateLimitedResourceIntTestUtils.setPathParamRateLimitingConfig(
        RATE_LIMIT_POLICY_PREFIX, rateLimiterName, "installationId");

    // These first requests should get rate-limited by path param
    rateLimitedResourceIntTestUtils.expectJsonRateLimiting(
        getHttpUtils()
            .put()
            .data(new JSONObject())
            .path(RATE_LIMITING_API_PATH)
            .addHeader(
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    TEST_INSTALLATION_ID))
            .initiatorAuth(authToken));
    //  No rate-limiting should happen with a different installationId
    rateLimitedResourceIntTestUtils.expectNoRateLimiting(
        getHttpUtils()
            .put()
            .data(new JSONObject())
            .path("/api/private/vercelnative/v1/installations/12345678901234567890")
            .addHeader(
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    TEST_INSTALLATION_ID))
            .initiatorAuth(authToken));
  }

  @Test
  public void testUpsertInstallation_newInstallation_success() throws Exception {
    // Arrange
    final UpsertInstallationRequest request = createTestUpsertRequest(null);
    final String installationId = "new-installation-123";

    // Verify installation doesn't exist before the test
    Optional<VercelNativeInstallation> beforeInstallation =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertFalse(beforeInstallation.isPresent(), "Installation should not exist before test");

    // Act - Test with a new installation ID that doesn't exist
    final InstallationGeneral200Response response =
        getHttpUtils()
            .put()
            .data(objectMapper.writeValueAsString(request))
            .path(UPSERT_INSTALLATION_ENDPOINT)
            .headers(
                Map.of(
                    "Idempotency-Key",
                    IDEMPOTENCY_KEY,
                    "Authorization",
                    getVercelToken(
                        PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                        VercelRole.ADMIN,
                        installationId)))
            .initiatorAuth(authToken)
            .returnType(InstallationGeneral200Response.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();

    // Assert HTTP response
    assertNotNull(response);
    assertNotNull(response.billingPlan());
    assertEquals(VercelBillingPlanId.FREE, response.billingPlan().id());

    // Assert database document was created correctly
    Optional<VercelNativeInstallation> afterInstallation =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(afterInstallation.isPresent(), "Installation should exist after creation");

    VercelNativeInstallation installation = afterInstallation.get();
    assertNotNull(installation.id(), "Installation should have an ID");
    assertNotNull(installation.createdAt(), "Installation should have createdAt timestamp");
    assertNotNull(installation.updatedAt(), "New installation should have updatedAt timestamp");
    assertNull(installation.uninstalledAt(), "New installation should not have uninstalledAt");
    assertNull(
        installation.atlasResources(), "New installation should not have atlasResources yet");

    // Verify VercelData fields
    VercelData vercelData = installation.vercelData();
    assertEquals(installationId, vercelData.installationId());
    assertEquals("Test Team", vercelData.teamName());
    assertEquals("https://test.vercel.app", vercelData.installationUrl());
    assertNotNull(vercelData.accessToken(), "Access token should be encrypted and stored");

    // Verify access token can be decrypted
    String decryptedToken =
        VercelData.decryptAccessToken(
            vercelData.accessToken(), vercelNativeConfig.getPartnerIntegrationEncryptionKey());
    assertEquals("test-access-token", decryptedToken);

    // Verify contact information
    VercelContact contact = vercelData.contact();
    assertNotNull(contact);
    assertEquals("Test User", contact.name());
    assertEquals("<EMAIL>", contact.email());

    // Verify accepted policies
    Map<String, String> policies = vercelData.acceptedPolicies();
    assertEquals("accepted", policies.get("privacy"));
    assertEquals("accepted", policies.get("terms"));
  }

  @Test
  public void testUpsertInstallation_nonAdmin_forbidden() throws Exception {
    // Arrange
    final UpsertInstallationRequest request = createTestUpsertRequest(null);
    final String installationId = "new-installation-123";

    // Verify installation doesn't exist before the test
    Optional<VercelNativeInstallation> beforeInstallation =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertFalse(beforeInstallation.isPresent(), "Installation should not exist before test");

    // Act - Test with token w/user (not admin) permissions
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(request))
        .path(UPSERT_INSTALLATION_ENDPOINT)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY,
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER, VercelRole.USER, installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_FORBIDDEN)
        .send();
  }

  @Test
  public void testUpsertInstallation_existingInstallation_success() throws Exception {
    // Arrange
    final UpsertInstallationRequest request = createTestUpsertRequest(null);
    final String installationId = "new-installation-123";

    // Act - First call to create the installation
    final InstallationGeneral200Response firstResponse =
        getHttpUtils()
            .put()
            .data(objectMapper.writeValueAsString(request))
            .path(UPSERT_INSTALLATION_ENDPOINT)
            .headers(
                Map.of(
                    "Idempotency-Key",
                    IDEMPOTENCY_KEY + "-first",
                    "Authorization",
                    getVercelToken(
                        PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                        VercelRole.ADMIN,
                        installationId)))
            .initiatorAuth(authToken)
            .returnType(InstallationGeneral200Response.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();

    // Assert first call succeeded
    assertNotNull(firstResponse);
    assertNotNull(firstResponse.billingPlan());

    // Verify database document was created
    Optional<VercelNativeInstallation> afterFirstCall =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(afterFirstCall.isPresent(), "Installation should exist after first call");

    VercelNativeInstallation firstInstallation = afterFirstCall.get();
    Instant firstCreatedAt = firstInstallation.createdAt();
    Instant firstUpdatedAt = firstInstallation.updatedAt();

    // Wait a moment to ensure updatedAt timestamp will be different
    Thread.sleep(100);

    // Act - Second call to update the existing installation with different token
    final UpsertInstallationRequest secondRequest = createTestUpsertRequest("updated-access-token");

    final InstallationGeneral200Response secondResponse =
        getHttpUtils()
            .put()
            .data(objectMapper.writeValueAsString(secondRequest))
            .path(UPSERT_INSTALLATION_ENDPOINT)
            .headers(
                Map.of(
                    "Idempotency-Key",
                    IDEMPOTENCY_KEY + "-second",
                    "Authorization",
                    getVercelToken(
                        PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                        VercelRole.ADMIN,
                        installationId)))
            .initiatorAuth(authToken)
            .returnType(InstallationGeneral200Response.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();

    // Assert second call also succeeded (upsert behavior)
    assertNotNull(secondResponse);
    assertNotNull(secondResponse.billingPlan());
    assertEquals(VercelBillingPlanId.FREE, secondResponse.billingPlan().id());

    // Assert database document was updated correctly
    Optional<VercelNativeInstallation> afterSecondCall =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(afterSecondCall.isPresent(), "Installation should still exist after update");

    VercelNativeInstallation updatedInstallation = afterSecondCall.get();

    // Verify the same installation was updated (same ID and createdAt)
    assertEquals(
        firstInstallation.id(), updatedInstallation.id(), "Should be the same installation");
    assertEquals(
        firstCreatedAt, updatedInstallation.createdAt(), "createdAt should not change on update");

    // For existing installations, updatedAt should be set
    assertNotNull(
        updatedInstallation.updatedAt(),
        "updatedAt should be set for existing installation updates");
    if (firstUpdatedAt != null) {
      assertTrue(
          updatedInstallation.updatedAt().isAfter(firstUpdatedAt), "updatedAt should be newer");
    }

    // Verify the access token was updated
    VercelData updatedVercelData = updatedInstallation.vercelData();
    String decryptedUpdatedToken =
        VercelData.decryptAccessToken(
            updatedVercelData.accessToken(),
            vercelNativeConfig.getPartnerIntegrationEncryptionKey());
    assertEquals("updated-access-token", decryptedUpdatedToken, "Access token should be updated");
  }

  @Test
  public void testUpsertInstallation_deletedExistingInstallation_conflict() throws Exception {
    // Arrange
    final String installationId = "deleted-installation-123";
    final UpsertInstallationRequest request = createTestUpsertRequest(null);

    // Create a deleted installation in the billing system
    createDeletedBillingInstallation(installationId);

    // Act & Assert
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(request))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-deleted",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_CONFLICT)
        .send();
  }

  @Test
  public void testUpsertInstallation_paidToFreeDowngrade_conflict() throws Exception {
    // Arrange
    final String installationId = "paid-installation-123";
    final UpsertInstallationRequest request = createTestUpsertRequest(null);

    // Create a paid installation in the billing system
    createPaidBillingInstallation(installationId);

    // Act & Assert - Attempting to upsert with FREE plan (default) should fail
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(request))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-downgrade",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_CONFLICT)
        .send();
  }

  @Test
  public void testGetInstallation_existingInstallation_success() throws Exception {
    // Arrange
    final String installationId = "existing-installation-123";
    final UpsertInstallationRequest request = createTestUpsertRequest(null);

    // First create an installation
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(request))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-get-test",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    // Act - Get the installation using system user token
    final InstallationGeneral200Response response =
        getHttpUtils()
            .get()
            .path("/api/private/vercelnative/v1/installations/" + installationId)
            .initiatorAuth(systemAuthToken)
            .addHeader(
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER,
                    VercelRole.ADMIN,
                    installationId))
            .returnType(InstallationGeneral200Response.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();

    // Assert
    assertNotNull(response);
    assertNotNull(response.billingPlan());
    assertEquals(VercelBillingPlanId.FREE, response.billingPlan().id());
  }

  @Test
  public void testGetInstallation_nonExistentInstallation_notFound() {
    // Arrange
    final String nonExistentInstallationId = "non-existent-installation-456";

    // Act & Assert
    getHttpUtils()
        .get()
        .path("/api/private/vercelnative/v1/installations/" + nonExistentInstallationId)
        .addHeader(
            "Authorization",
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER,
                VercelRole.ADMIN,
                nonExistentInstallationId))
        .initiatorAuth(systemAuthToken)
        .expectedReturnStatus(HttpStatus.SC_NOT_FOUND)
        .send();
  }

  @Test
  public void testUpdateInstallation_freeToPayUpgrade_success() throws Exception {
    // Arrange
    final String installationId = "update-installation-123";
    final UpsertInstallationRequest upsertRequest = createTestUpsertRequest(null);

    // First create a FREE installation
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(upsertRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-create",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    // Verify initial installation is FREE
    Optional<VercelNativeInstallation> beforeUpdate =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(beforeUpdate.isPresent(), "Installation should exist before update");

    // Act - Update to PAID plan
    final UpdateInstallationRequest updateRequest =
        UpdateInstallationRequest.of(VercelBillingPlanId.PAID);

    final InstallationGeneral200Response response =
        getHttpUtils()
            .patch()
            .data(objectMapper.writeValueAsString(updateRequest))
            .path("/api/private/vercelnative/v1/installations/" + installationId)
            .headers(
                Map.of(
                    "Idempotency-Key",
                    IDEMPOTENCY_KEY + "-update",
                    "Authorization",
                    getVercelToken(
                        PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                        VercelRole.ADMIN,
                        installationId)))
            .initiatorAuth(authToken)
            .returnType(InstallationGeneral200Response.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();

    // Assert HTTP response
    assertNotNull(response);
    assertNotNull(response.billingPlan());
    assertEquals(VercelBillingPlanId.PAID, response.billingPlan().id());
  }

  @Test
  public void testUpdateInstallation_samePlan_success() throws Exception {
    // Arrange
    final String installationId = "same-plan-installation-123";
    final UpsertInstallationRequest upsertRequest = createTestUpsertRequest(null);

    // First create a FREE installation
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(upsertRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-create-same",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    // Act - Update to same FREE plan
    final UpdateInstallationRequest updateRequest =
        UpdateInstallationRequest.of(VercelBillingPlanId.FREE);

    final InstallationGeneral200Response response =
        getHttpUtils()
            .patch()
            .data(objectMapper.writeValueAsString(updateRequest))
            .path("/api/private/vercelnative/v1/installations/" + installationId)
            .headers(
                Map.of(
                    "Idempotency-Key",
                    IDEMPOTENCY_KEY + "-update-same",
                    "Authorization",
                    getVercelToken(
                        PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                        VercelRole.ADMIN,
                        installationId)))
            .initiatorAuth(authToken)
            .returnType(InstallationGeneral200Response.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .send();

    // Assert HTTP response
    assertNotNull(response);
    assertNotNull(response.billingPlan());
    assertEquals(VercelBillingPlanId.FREE, response.billingPlan().id());
  }

  @Test
  public void testUpdateInstallation_nonExistentInstallation_notFound() throws Exception {
    // Arrange
    final String nonExistentInstallationId = "non-existent-update-456";
    final UpdateInstallationRequest updateRequest =
        UpdateInstallationRequest.of(VercelBillingPlanId.PAID);

    // Act & Assert
    getHttpUtils()
        .patch()
        .data(objectMapper.writeValueAsString(updateRequest))
        .path("/api/private/vercelnative/v1/installations/" + nonExistentInstallationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-not-found",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    nonExistentInstallationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_BAD_REQUEST)
        .send();
  }

  @Test
  public void testUpdateInstallation_deletedInstallation_conflict() throws Exception {
    // Arrange
    final String installationId = "deleted-update-installation-123";
    final UpdateInstallationRequest updateRequest =
        UpdateInstallationRequest.of(VercelBillingPlanId.PAID);

    // Create a deleted installation in the billing system
    createDeletedBillingInstallation(installationId);

    // Act & Assert
    getHttpUtils()
        .patch()
        .data(objectMapper.writeValueAsString(updateRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-deleted-update",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_CONFLICT)
        .send();
  }

  @Test
  public void testUpdateInstallation_paidToFreeDowngrade_conflict() throws Exception {
    // Arrange
    final String installationId = "paid-downgrade-installation-123";
    final UpdateInstallationRequest updateRequest =
        UpdateInstallationRequest.of(VercelBillingPlanId.FREE);

    // Create a paid installation in the billing system
    createPaidBillingInstallation(installationId);

    // Act & Assert
    getHttpUtils()
        .patch()
        .data(objectMapper.writeValueAsString(updateRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-downgrade",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_CONFLICT)
        .send();
  }

  @Test
  public void testDeleteInstallation() throws Exception {
    // Arrange
    final String installationId = "delete-installation-validation-123";
    final UpsertInstallationRequest upsertRequest = createTestUpsertRequest(null);
    final DeleteInstallationRequest deleteRequest =
        DeleteInstallationRequest.builder().cascadeResourceDeletion(false).build();

    // First create an installation
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(upsertRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-create-for-delete",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    // Verify installation exists before deletion
    Optional<VercelNativeInstallation> beforeDelete =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(beforeDelete.isPresent(), "Installation should exist before deletion");
    assertNull(
        beforeDelete.get().uninstalledAt(), "Installation should not be marked as uninstalled");

    final VercelNativeInstallationsSvc vercelNativeInstallationsSvc =
        getStubbedOutInstallationSvc();
    // to actually use our modified vercelNativeInstallationsSvc, we need to explicitly create a
    // VercelNativeInstallationsResourceImpl instance
    final VercelNativeInstallationsResourceImpl vercelNativeInstallationsResource =
        new VercelNativeInstallationsResourceImpl(
            vercelBillingConfig, vercelNativeInstallationsSvc, vercelAuthSvc);

    // stubbing out the billing and iam handlers for deleteInstallation so they don't try to call
    // out to external services
    when(installationBillingHandler.onDeleteInstallation(anyString(), anyString()))
        .thenReturn(DeleteInstallationResult.of());
    doNothing()
        .when(vercelNativeAccountSvc)
        .uninstallVercelIntegration(anyString(), any(AuditInfo.class));
    // mock out vercel auth header so we can validate inline auth checks
    var mockRequest = mock(HttpServletRequest.class);
    when(mockRequest.getHeader("Authorization"))
        .thenReturn(
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER, VercelRole.ADMIN, installationId));

    // Act - delete the installation
    final Response response =
        vercelNativeInstallationsResource.deleteInstallation(
            installationId,
            IDEMPOTENCY_KEY + "-delete",
            deleteRequest,
            mock(AppUser.class),
            mock(AuditInfo.class),
            mockRequest);

    // Assert - Verify installation was deleted
    assertEquals(HttpStatus.SC_OK, response.getStatus());
    Optional<VercelNativeInstallation> afterDelete =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(afterDelete.isPresent(), "Installation should still exist in database");
    assertNotNull(
        afterDelete.get().uninstalledAt(),
        "Installation should be marked as uninstalled after delete");
  }

  @Test
  public void testDeleteInstallation_notFound() throws Exception {
    // Arrange
    final String nonExistentInstallationId = "non-existent-delete-456";
    final DeleteInstallationRequest deleteRequest =
        DeleteInstallationRequest.builder().cascadeResourceDeletion(false).build();

    // Act & Assert
    getHttpUtils()
        .delete()
        .data(objectMapper.writeValueAsString(deleteRequest))
        .path("/api/private/vercelnative/v1/installations/" + nonExistentInstallationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-not-found",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    nonExistentInstallationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_NOT_FOUND)
        .send();
  }

  @Test
  public void testDeleteInstallation_alreadyDeletedInstallation_conflict() throws Exception {
    // Arrange
    final String installationId = "already-deleted-installation-123";
    final UpsertInstallationRequest upsertRequest = createTestUpsertRequest(null);
    final DeleteInstallationRequest deleteRequest =
        DeleteInstallationRequest.builder().cascadeResourceDeletion(false).build();

    // First create an installation in the main database
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(upsertRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-create-for-already-deleted",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    // Then soft-delete it in the main database to simulate an already deleted installation
    Optional<VercelNativeInstallation> installation =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(installation.isPresent(), "Installation should exist before soft deletion");

    VercelNativeInstallation softDeletedInstallation =
        installation.get().toBuilder().uninstalledAt(Instant.now()).build();
    vercelNativeInstallationDao.upsertInstallation(installationId, softDeletedInstallation);

    // Act & Assert - Attempting to delete an already soft-deleted installation should return
    // conflict
    getHttpUtils()
        .delete()
        .data(objectMapper.writeValueAsString(deleteRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-already-deleted",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_CONFLICT)
        .send();
  }

  @Test
  public void testDeleteInstallation_withActiveClusters_conflict() throws Exception {
    // Arrange
    final String installationId = "installation-with-active-clusters-123";
    final DeleteInstallationRequest deleteRequest =
        DeleteInstallationRequest.builder().cascadeResourceDeletion(false).build();

    // Create an installation with active clusters directly in the database
    createInstallationWithActiveClusters(installationId);

    // Act & Assert - Attempting to delete installation with active clusters should fail
    // This should fail with CONFLICT before reaching the payment service
    getHttpUtils()
        .delete()
        .data(objectMapper.writeValueAsString(deleteRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-active-clusters",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_CONFLICT)
        .send();
  }

  @Test
  public void testDeleteInstallation_existingInstallation_paymentsInternalServerError()
      throws Exception {
    // Arrange
    final String installationId = "delete-installation-123";
    final UpsertInstallationRequest upsertRequest = createTestUpsertRequest(null);
    final DeleteInstallationRequest deleteRequest =
        DeleteInstallationRequest.builder().cascadeResourceDeletion(false).build();

    // First create an installation
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(upsertRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-create-for-delete",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    // Verify installation exists before deletion
    Optional<VercelNativeInstallation> beforeDelete =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(beforeDelete.isPresent(), "Installation should exist before deletion");
    assertNull(
        beforeDelete.get().uninstalledAt(), "Installation should not be marked as uninstalled");

    // Act - Delete the installation (this will fail due to missing payment service dependencies)
    // This tests the error handling path when external services are unavailable
    getHttpUtils()
        .delete()
        .data(objectMapper.writeValueAsString(deleteRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-delete",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_INTERNAL_SERVER_ERROR)
        .send();

    // Assert - Verify installation was NOT deleted due to the error
    Optional<VercelNativeInstallation> afterDelete =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(afterDelete.isPresent(), "Installation should still exist in database");
    assertNull(
        afterDelete.get().uninstalledAt(),
        "Installation should not be marked as uninstalled after failed delete");
  }

  @Test
  public void testDeleteInstallation_existingInstallation_accountsInternalServerError()
      throws Exception {
    // Arrange
    final String installationId = "delete-installation-123";
    final UpsertInstallationRequest upsertRequest = createTestUpsertRequest(null);
    final DeleteInstallationRequest deleteRequest =
        DeleteInstallationRequest.builder().cascadeResourceDeletion(false).build();

    // First create an installation
    getHttpUtils()
        .put()
        .data(objectMapper.writeValueAsString(upsertRequest))
        .path("/api/private/vercelnative/v1/installations/" + installationId)
        .headers(
            Map.of(
                "Idempotency-Key",
                IDEMPOTENCY_KEY + "-create-for-delete",
                "Authorization",
                getVercelToken(
                    PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER,
                    VercelRole.ADMIN,
                    installationId)))
        .initiatorAuth(authToken)
        .expectedReturnStatus(HttpStatus.SC_OK)
        .send();

    // Verify installation exists before deletion
    Optional<VercelNativeInstallation> beforeDelete =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(beforeDelete.isPresent(), "Installation should exist before deletion");
    assertNull(
        beforeDelete.get().uninstalledAt(), "Installation should not be marked as uninstalled");

    final VercelNativeInstallationsSvc vercelNativeInstallationsSvc =
        getStubbedOutInstallationSvc();
    // to actually use our modified vercelNativeInstallationsSvc, we need to explicitly create a
    // VercelNativeInstallationsResourceImpl instance
    final VercelNativeInstallationsResourceImpl vercelNativeInstallationsResource =
        new VercelNativeInstallationsResourceImpl(
            vercelBillingConfig, vercelNativeInstallationsSvc, vercelAuthSvc);

    // stubbing out the billing handler for deleteInstallation so it doesn't try to call
    // out to external services
    when(installationBillingHandler.onDeleteInstallation(anyString(), anyString()))
        .thenReturn(DeleteInstallationResult.of());
    // no stub for VercelNativeAccountSvc (allow IAM to fail naturally)
    var mockRequest = mock(HttpServletRequest.class);
    when(mockRequest.getHeader("Authorization"))
        .thenReturn(
            getVercelToken(
                PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER, VercelRole.ADMIN, installationId));

    // Act - Delete the installation (this will fail due to missing account service dependencies)
    final Response result =
        vercelNativeInstallationsResource.deleteInstallation(
            installationId,
            IDEMPOTENCY_KEY + "-delete",
            deleteRequest,
            mock(AppUser.class),
            mock(AuditInfo.class),
            mockRequest);

    // Assert - Verify installation was NOT deleted due to the error
    assertEquals(HttpStatus.SC_INTERNAL_SERVER_ERROR, result.getStatus());
    Optional<VercelNativeInstallation> afterDelete =
        vercelNativeInstallationDao.findByInstallationId(installationId);
    assertTrue(afterDelete.isPresent(), "Installation should still exist in database");
    assertNull(
        afterDelete.get().uninstalledAt(),
        "Installation should not be marked as uninstalled after failed delete");
  }

  // Helper methods

  private void suspendIpRateLimiting() {
    rateLimitSuspensionSvc.suspendRateLimiting(
        RateLimitType.IP,
        new ObjectId(),
        RateLimitSuspensionSvc.DEFAULT_RATE_LIMIT_SUSPENSION_TIME,
        null);
  }

  private UpsertInstallationRequest createTestUpsertRequest(String accessToken) {
    UpsertInstallationRequestCredentials credentials =
        UpsertInstallationRequestCredentials.builder()
            .accessToken(accessToken == null ? mockAccessToken : accessToken)
            .tokenType("Bearer")
            .build();

    UpsertInstallationRequestAccountContact contact =
        UpsertInstallationRequestAccountContact.builder()
            .name("Test User")
            .email("<EMAIL>")
            .build();

    UpsertInstallationRequestAccount account =
        UpsertInstallationRequestAccount.builder()
            .name("Test Team")
            .url("https://test.vercel.app")
            .contact(contact)
            .build();

    return UpsertInstallationRequest.builder()
        .scopes(List.of("read", "write"))
        .acceptedPolicies(Map.of("privacy", "accepted", "terms", "accepted"))
        .credentials(credentials)
        .account(account)
        .build();
  }

  private void createDeletedBillingInstallation(String installationId) {
    VercelInstallation deletedInstallation =
        VercelInstallation.builder()
            .installationId(installationId)
            .orgId(new ObjectId())
            .accessTokenEncrypted("deleted-access-token", vercelBillingConfig.getAccessTokenKey())
            .tokenType("Bearer")
            .billingPlanId(VercelBillingPlanId.FREE)
            .installationUrl("https://deleted.vercel.app")
            .deletedAt(LocalDateTime.now())
            .build();
    vercelInstallationBillingSvc.upsert(deletedInstallation);
  }

  private void createPaidBillingInstallation(String installationId) {
    VercelInstallation paidInstallation =
        VercelInstallation.builder()
            .installationId(installationId)
            .orgId(new ObjectId())
            .accessTokenEncrypted("paid-access-token", vercelBillingConfig.getAccessTokenKey())
            .tokenType("Bearer")
            .billingPlanId(VercelBillingPlanId.PAID)
            .installationUrl("https://paid.vercel.app")
            .build();
    vercelInstallationBillingSvc.upsert(paidInstallation);
  }

  private void createInstallationWithActiveClusters(String installationId) {
    // Create test data for an installation with active clusters
    ObjectId organizationId = new ObjectId();
    ObjectId projectId = new ObjectId();
    ObjectId vercelResourceId = new ObjectId();

    // Create an active cluster (status != UNINSTALLED)
    InstalledProduct activeCluster =
        InstalledProduct.builder()
            .vercelResourceId(vercelResourceId)
            .projectId(projectId)
            .clusterName("test-active-cluster")
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .status(VercelResourceStatus.READY) // Active status
            .createdAt(Instant.now())
            .build();

    // Create atlas resources with the active cluster
    AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(List.of(activeCluster))
            .build();

    // Create vercel data
    VercelData vercelData =
        VercelData.builder()
            .installationId(installationId)
            .accessToken(mockAccessToken, vercelNativeConfig.getPartnerIntegrationEncryptionKey())
            .teamName("Test Team")
            .installationUrl("https://test.vercel.app")
            .contact(VercelContact.builder().name("Test User").email("<EMAIL>").build())
            .acceptedPolicies(Map.of("privacy", "accepted", "terms", "accepted"))
            .build();

    // Create and save the installation with active clusters
    VercelNativeInstallation installationWithClusters =
        VercelNativeInstallation.builder()
            .id(new ObjectId())
            .vercelData(vercelData)
            .atlasResources(atlasResources)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(installationId, installationWithClusters);
  }

  private VercelNativeInstallationsSvc getStubbedOutInstallationSvc() {
    // returns a slightly mocked out version of VercelNativeInstallationsSvc to bypass
    // Payments and Authn service not running
    // (hopefully Billing and IAM have their own int tests for that part of the logic)
    final OrganizationSvc organizationSvc = AppConfig.getInstance(OrganizationSvc.class);
    final VercelNativeInstallationsDaoSvc vercelNativeInstallationsDaoSvc =
        AppConfig.getInstance(VercelNativeInstallationsDaoSvc.class);
    final AuditSvc auditSvc = AppConfig.getInstance(AuditSvc.class);
    final AuditInfoSvc auditInfoSvc = AppConfig.getInstance(AuditInfoSvc.class);

    return new VercelNativeInstallationsSvcImpl(
        vercelNativeConfig,
        installationBillingHandler, // spying
        vercelNativeInstallationsDaoSvc,
        vercelNativeAccountSvc, // spying
        organizationSvc,
        auditSvc,
        auditInfoSvc);
  }
}
