package com.xgen.cloud.partnerintegrations.vercelnative._private.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.partnerintegrations.vercelnative._public.config.VercelNativeConfig;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.AtlasResources;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.InstalledProduct;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelContact;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.UserMetadata;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceNotification;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceNotificationLevel;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceStatus;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class VercelNativeInstallationDaoIntTests extends JUnit5BaseSvcTest {

  private static final String TEST_INSTALLATION_ID = "test-installation-id";
  private static final String TEST_INSTALLATION_ID_2 = "test-installation-id-2";
  private static final String TEST_ACCESS_TOKEN = "test-access-token";
  private static final String TEST_TEAM_NAME = "test-team";
  private static final String TEST_INSTALLATION_URL = "https://vercel.com/test";
  private static final String TEST_USER_NAME = "Test User";
  private static final String TEST_USER_EMAIL = "<EMAIL>";
  private static final String TEST_CLUSTER_NAME = "test-cluster";
  private static final Map<String, String> TEST_ACCEPTED_POLICIES =
      Map.of("terms", "true", "privacy", "true");
  private static final UserMetadata TEST_USER_METADATA =
      new UserMetadata(UserMetadata.ClusterTier.M10, UserMetadata.VercelRegion.IAD1);
  private static final UserMetadata UPDATED_USER_METADATA =
      new UserMetadata(UserMetadata.ClusterTier.FLEX, UserMetadata.VercelRegion.SFO1);
  private static final VercelResourceNotification TEST_NOTIFICATION =
      new VercelResourceNotification(
          VercelResourceNotificationLevel.INFO, "Test Title", "Test Message", null);

  @Inject private VercelNativeInstallationDao vercelNativeInstallationDao;
  @Inject private VercelNativeConfig vercelNativeConfig;

  private ObjectId projectId;
  private ObjectId organizationId;
  private ObjectId vercelResourceId;
  private ObjectId id;
  private VercelNativeInstallation installation;

  @BeforeEach
  public void setUp() {
    projectId = ObjectId.get();
    organizationId = ObjectId.get();
    id = ObjectId.get();
    vercelResourceId = ObjectId.get();
    installation =
        createTestInstallation(
            TEST_INSTALLATION_ID, projectId, organizationId, vercelResourceId, id);
  }

  @AfterEach
  public void tearDown() {
    // Clean up test data after each test
    vercelNativeInstallationDao.deleteInstallation(TEST_INSTALLATION_ID);
    vercelNativeInstallationDao.deleteInstallation(TEST_INSTALLATION_ID_2);
  }

  @Test
  public void testCreateInstallation() {
    // Create test data
    VercelNativeInstallation installation =
        createTestInstallation(
            TEST_INSTALLATION_ID, projectId, organizationId, vercelResourceId, id);

    // Test creation and retrieval
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);
    Optional<VercelNativeInstallation> retrievedInstallation =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(retrievedInstallation.isPresent());
    assertEquals(TEST_INSTALLATION_ID, retrievedInstallation.get().vercelData().installationId());
  }

  @Test
  public void testUpsertInstallationWithSameProjectId() {
    // Create first installation
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Create second installation with same project ID but different installation ID
    ObjectId newId = ObjectId.get();
    VercelNativeInstallation installation2 =
        createTestInstallation(
            TEST_INSTALLATION_ID_2, projectId, ObjectId.get(), vercelResourceId, newId);

    // This should succeed as we allow multiple installations to reference the same
    // project
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID_2, installation2);

    // Verify both installations exist and can be retrieved
    Optional<VercelNativeInstallation> retrievedInstallation1 =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    Optional<VercelNativeInstallation> retrievedInstallation2 =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID_2);

    assertTrue(retrievedInstallation1.isPresent());
    assertTrue(retrievedInstallation2.isPresent());
    assertEquals(
        projectId,
        retrievedInstallation1.get().atlasResources().installedProducts().get(0).projectId());
    assertEquals(
        projectId,
        retrievedInstallation2.get().atlasResources().installedProducts().get(0).projectId());
  }

  @Test
  public void testUpsertInstallationWithSameInstallationId() {
    // Create initial installation
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Create new installation with same installation ID but different project ID
    ObjectId newProjectId = ObjectId.get();
    ObjectId newId = ObjectId.get();
    VercelNativeInstallation updatedInstallation =
        createTestInstallation(
            TEST_INSTALLATION_ID, newProjectId, ObjectId.get(), vercelResourceId, newId);

    // This should update the existing installation
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, updatedInstallation);

    // Verify the installation was updated
    Optional<VercelNativeInstallation> retrievedInstallation =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(retrievedInstallation.isPresent());
    assertEquals(
        newProjectId,
        retrievedInstallation.get().atlasResources().installedProducts().get(0).projectId());
  }

  @Test
  public void testFindByInstallationId() {
    // Create test data
    VercelNativeInstallation installation2 =
        createTestInstallation(
            TEST_INSTALLATION_ID_2,
            ObjectId.get(),
            ObjectId.get(),
            vercelResourceId,
            ObjectId.get());

    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID_2, installation2);

    // Test retrieval of a specific installation
    Optional<VercelNativeInstallation> retrievedInstallation =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(retrievedInstallation.isPresent());
    assertEquals(TEST_INSTALLATION_ID, retrievedInstallation.get().vercelData().installationId());
    assertEquals(
        organizationId,
        Objects.requireNonNull(retrievedInstallation.get().atlasResources()).organizationId());
  }

  @Test
  public void testFindNonExistentInstallation() {
    Optional<VercelNativeInstallation> retrievedInstallation =
        vercelNativeInstallationDao.findByInstallationId("non-existent-id");
    assertFalse(retrievedInstallation.isPresent());
  }

  @Test
  public void testFindByProjectId() {
    // Test creation and retrieval
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);
    Optional<VercelNativeInstallation> retrievedInstallation =
        vercelNativeInstallationDao.findByProjectId(projectId);
    assertTrue(retrievedInstallation.isPresent());
    assertEquals(
        projectId,
        Objects.requireNonNull(retrievedInstallation.get().atlasResources())
            .installedProducts()
            .get(0)
            .projectId());
  }

  @Test
  public void testFindNonExistentProject() {
    Optional<VercelNativeInstallation> retrievedInstallation =
        vercelNativeInstallationDao.findByProjectId(ObjectId.get());
    assertFalse(retrievedInstallation.isPresent());
  }

  @Test
  public void testFindByVercelResourceId() {
    // Test creation and retrieval
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);
    Optional<VercelNativeInstallation> retrievedInstallation =
        vercelNativeInstallationDao.findByVercelResourceId(vercelResourceId);
    assertTrue(retrievedInstallation.isPresent());
    assertEquals(
        vercelResourceId,
        Objects.requireNonNull(retrievedInstallation.get().atlasResources())
            .installedProducts()
            .get(0)
            .vercelResourceId());
  }

  @Test
  public void testFindAllActiveInstallations() {
    // Create active installation (eg where uninstalledAt is null)
    VercelNativeInstallation activeInstallation =
        createTestInstallation(
            TEST_INSTALLATION_ID, projectId, organizationId, vercelResourceId, id);
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, activeInstallation);

    // Create inactive installation (uninstalledAt is set) with different organization ID
    ObjectId inactiveId = ObjectId.get();
    ObjectId inactiveProjectId = ObjectId.get();
    ObjectId inactiveResourceId = ObjectId.get();
    ObjectId inactiveOrganizationId = ObjectId.get(); // Different organization ID
    VercelNativeInstallation inactiveInstallation =
        createTestInstallation(
                TEST_INSTALLATION_ID_2,
                inactiveProjectId,
                inactiveOrganizationId,
                inactiveResourceId,
                inactiveId)
            .toBuilder()
            .uninstalledAt(Instant.now()) // Mark as inactive
            .build();
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID_2, inactiveInstallation);

    // Test findAllActiveInstallations
    List<VercelNativeInstallation> activeInstallations =
        vercelNativeInstallationDao.findAllActiveInstallations().toList();

    // Should only return the active installation
    assertEquals(1, activeInstallations.size());
    assertEquals(TEST_INSTALLATION_ID, activeInstallations.get(0).vercelData().installationId());
    assertNull(activeInstallations.get(0).uninstalledAt());
  }

  @Test
  public void testFindAllActiveInstallations_NoActiveInstallations() {
    // Create only inactive installations with different organization IDs
    ObjectId inactiveId1 = ObjectId.get();
    ObjectId inactiveId2 = ObjectId.get();
    ObjectId inactiveOrganizationId1 = ObjectId.get();
    ObjectId inactiveOrganizationId2 = ObjectId.get();
    VercelNativeInstallation inactiveInstallation1 =
        createTestInstallation(
                TEST_INSTALLATION_ID,
                projectId,
                inactiveOrganizationId1,
                vercelResourceId,
                inactiveId1)
            .toBuilder()
            .uninstalledAt(Instant.now())
            .build();
    VercelNativeInstallation inactiveInstallation2 =
        createTestInstallation(
                TEST_INSTALLATION_ID_2,
                ObjectId.get(),
                inactiveOrganizationId2,
                ObjectId.get(),
                inactiveId2)
            .toBuilder()
            .uninstalledAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, inactiveInstallation1);
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID_2, inactiveInstallation2);

    // Test findAllActiveInstallations
    List<VercelNativeInstallation> activeInstallations =
        vercelNativeInstallationDao.findAllActiveInstallations().toList();

    // Should return empty list
    assertEquals(0, activeInstallations.size());
    assertTrue(activeInstallations.isEmpty());
  }

  @Test
  public void testUpsertVercelDataForInstallation_NewInstallation() {
    // Create new VercelData
    VercelData newVercelData = createTestVercelData(TEST_INSTALLATION_ID);

    // Verify installation doesn't exist initially
    Optional<VercelNativeInstallation> beforeUpsert =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertFalse(beforeUpsert.isPresent());

    // Upsert VercelData for new installation
    vercelNativeInstallationDao.upsertVercelDataForInstallation(
        TEST_INSTALLATION_ID, newVercelData);

    // Verify installation was created
    Optional<VercelNativeInstallation> afterUpsert =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpsert.isPresent());

    VercelNativeInstallation createdInstallation = afterUpsert.get();
    assertEquals(newVercelData, createdInstallation.vercelData());
    assertNotNull(createdInstallation.id());
    assertNotNull(createdInstallation.createdAt());
    assertNotNull(createdInstallation.updatedAt());
    assertNull(createdInstallation.atlasResources()); // Should be null for new installation
    assertNull(createdInstallation.uninstalledAt());
  }

  @Test
  public void testUpsertVercelDataForInstallation_UpdateExistingInstallation() {
    // Create initial installation
    VercelNativeInstallation initialInstallation =
        createTestInstallation(
            TEST_INSTALLATION_ID, projectId, organizationId, vercelResourceId, id);
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, initialInstallation);

    // Get initial state
    Optional<VercelNativeInstallation> beforeUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeUpdate.isPresent());
    Instant initialCreatedAt = beforeUpdate.get().createdAt();
    Instant initialUpdatedAt = beforeUpdate.get().updatedAt();

    // Create updated VercelData with different values
    VercelData updatedVercelData =
        VercelData.builder()
            .installationId(TEST_INSTALLATION_ID)
            .accessToken(
                "updated-access-token", vercelNativeConfig.getPartnerIntegrationEncryptionKey())
            .teamName("updated-team-name")
            .installationUrl("https://vercel.com/updated")
            .contact(new VercelContact(TEST_USER_NAME, TEST_USER_EMAIL))
            .acceptedPolicies(TEST_ACCEPTED_POLICIES)
            .build();

    // Upsert with updated VercelData
    vercelNativeInstallationDao.upsertVercelDataForInstallation(
        TEST_INSTALLATION_ID, updatedVercelData);

    // Verify installation was updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());

    VercelNativeInstallation updatedInstallation = afterUpdate.get();
    assertEquals(updatedVercelData, updatedInstallation.vercelData());
    assertEquals(id, updatedInstallation.id()); // ID should remain the same
    assertEquals(
        initialCreatedAt, updatedInstallation.createdAt()); // createdAt should remain the same
    assertTrue(
        updatedInstallation.updatedAt().isAfter(initialUpdatedAt)); // updatedAt should be newer
    assertNotNull(updatedInstallation.atlasResources()); // AtlasResources should be preserved
  }

  @Test
  public void testUpdateInstallationOrganizationId() {
    // Create installation with initial organization
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Verify initial organization ID
    Optional<VercelNativeInstallation> beforeUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeUpdate.isPresent());
    assertEquals(organizationId, beforeUpdate.get().atlasResources().organizationId());

    // Update to new organization ID
    ObjectId newOrganizationId = ObjectId.get();
    vercelNativeInstallationDao.updateInstallationOrganizationId(
        TEST_INSTALLATION_ID, newOrganizationId);

    // Verify organization ID was updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    assertEquals(newOrganizationId, afterUpdate.get().atlasResources().organizationId());

    // Verify other fields remain unchanged
    assertEquals(
        beforeUpdate.get().vercelData().installationId(),
        afterUpdate.get().vercelData().installationId());
    assertEquals(
        beforeUpdate.get().atlasResources().installedProducts().size(),
        afterUpdate.get().atlasResources().installedProducts().size());
  }

  @Test
  public void testUpdateInstallationOrganizationId_NonExistentInstallation() {
    // Try to update organization ID for non-existent installation
    ObjectId newOrganizationId = ObjectId.get();
    vercelNativeInstallationDao.updateInstallationOrganizationId(
        "non-existent-installation", newOrganizationId);

    // Verify no installation was created or modified
    Optional<VercelNativeInstallation> result =
        vercelNativeInstallationDao.findByInstallationId("non-existent-installation");
    assertFalse(result.isPresent());
  }

  @Test
  public void
      testUpdateInstalledProductIdempotencyKeyAndUserMetadataForCreate_InitializeAtlasResources() {
    // Create installation without AtlasResources (first resource scenario)
    VercelNativeInstallation installationWithoutAtlasResources =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(null) // No AtlasResources yet
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithoutAtlasResources);

    // Verify initial state - no AtlasResources
    Optional<VercelNativeInstallation> beforeUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeUpdate.isPresent());
    assertNull(beforeUpdate.get().atlasResources());

    // Update with shouldInitializeAtlasResources = true
    String testIdempotencyKey = "test-create-key";
    String initialProjectName = "test-initial-project";
    vercelNativeInstallationDao.updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
        TEST_INSTALLATION_ID,
        initialProjectName,
        testIdempotencyKey,
        TEST_USER_METADATA,
        true, // shouldInitializeAtlasResources
        true); // shouldInitializeInstalledProduct - shouldn't matter since AtlasResources is null,
    // but good to be realistic

    // Verify AtlasResources was created with one product
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    assertNotNull(afterUpdate.get().atlasResources());

    List<InstalledProduct> products = afterUpdate.get().atlasResources().installedProducts();
    assertEquals(1, products.size());

    InstalledProduct createdProduct = products.get(0);
    assertEquals(
        testIdempotencyKey,
        createdProduct
            .requestIdempotencyKeys()
            .get(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name()));
    assertEquals(TEST_USER_METADATA, createdProduct.userMetadata());
    assertNull(createdProduct.initialProjectName());
    assertNull(createdProduct.createdAt());
  }

  @Test
  public void
      testUpdateInstalledProductIdempotencyKeyAndUserMetadataForCreate_InitializeInstalledProduct() {
    // Create installation with AtlasResources but no matching installed products
    AtlasResources emptyAtlasResources =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(List.of()) // Empty products list
            .build();

    VercelNativeInstallation installationWithEmptyProducts =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(emptyAtlasResources)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithEmptyProducts);

    // Verify initial state - empty products list
    Optional<VercelNativeInstallation> beforeUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeUpdate.isPresent());
    assertNotNull(beforeUpdate.get().atlasResources());
    assertTrue(beforeUpdate.get().atlasResources().installedProducts().isEmpty());

    // Update with shouldInitializeInstalledProduct = true
    String testIdempotencyKey = "test-create-key";
    String initialProjectName = "test-initial-project";
    vercelNativeInstallationDao.updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
        TEST_INSTALLATION_ID,
        initialProjectName,
        testIdempotencyKey,
        UPDATED_USER_METADATA,
        false, // shouldInitializeAtlasResources
        true); // shouldInitializeInstalledProduct

    // Verify new product was added to the list
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());

    List<InstalledProduct> products = afterUpdate.get().atlasResources().installedProducts();
    assertEquals(1, products.size());

    InstalledProduct createdProduct = products.get(0);
    assertEquals(
        testIdempotencyKey,
        createdProduct
            .requestIdempotencyKeys()
            .get(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name()));
    assertEquals(UPDATED_USER_METADATA, createdProduct.userMetadata());
    assertNull(createdProduct.initialProjectName());
    assertNull(createdProduct.createdAt());
  }

  @Test
  public void
      testUpdateInstalledProductIdempotencyKeyAndUserMetadataForCreate_UpdateExistingProduct() {
    // Create installation with a product that has initialProjectName (a partially installed
    // product)
    String initialProjectName = "test-initial-project";
    String oldIdempotencyKey = "old-key";
    InstalledProduct productWithInitialProjectName =
        InstalledProduct.builder()
            .userMetadata(TEST_USER_METADATA)
            .initialProjectName(initialProjectName)
            .projectId(projectId)
            .requestIdempotencyKeys(
                Map.of(
                    InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name(), oldIdempotencyKey))
            .build();

    AtlasResources atlasResourcesWithProduct =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(List.of(productWithInitialProjectName))
            .build();

    VercelNativeInstallation installationWithProduct =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(atlasResourcesWithProduct)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installationWithProduct);

    // Verify initial state - product exists
    Optional<VercelNativeInstallation> beforeUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeUpdate.isPresent());
    InstalledProduct productBeforeUpdate =
        beforeUpdate.get().atlasResources().installedProducts().get(0);
    assertEquals(initialProjectName, productBeforeUpdate.initialProjectName());
    assertEquals(projectId, productBeforeUpdate.projectId());
    assertEquals(
        oldIdempotencyKey,
        productBeforeUpdate
            .requestIdempotencyKeys()
            .get(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name()));
    assertEquals(TEST_USER_METADATA, productBeforeUpdate.userMetadata());

    // Update existing product with idempotency key and new metadata
    String testIdempotencyKey = "test-create-key";
    vercelNativeInstallationDao.updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
        TEST_INSTALLATION_ID,
        initialProjectName,
        testIdempotencyKey,
        UPDATED_USER_METADATA,
        false, // shouldInitializeAtlasResources
        false); // shouldInitializeInstalledProduct

    // Verify the existing product was updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());

    List<InstalledProduct> products = afterUpdate.get().atlasResources().installedProducts();
    assertEquals(1, products.size());

    InstalledProduct updatedProduct = products.get(0);
    assertEquals(
        testIdempotencyKey,
        updatedProduct
            .requestIdempotencyKeys()
            .get(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name()));
    assertEquals(UPDATED_USER_METADATA, updatedProduct.userMetadata());

    // Other fields should remain unchanged
    assertEquals(initialProjectName, updatedProduct.initialProjectName());
    assertEquals(projectId, updatedProduct.projectId());
  }

  @Test
  public void
      testUpdateInstalledProductIdempotencyKeyAndUserMetadataForCreate_NonExistentInitialProjectName() {
    // Create installation with existing products
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Try to update with non-existent initial project name
    String nonExistentProjectName = "non-existent-project";
    String testIdempotencyKey = "test-create-key";
    vercelNativeInstallationDao.updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
        TEST_INSTALLATION_ID,
        nonExistentProjectName,
        testIdempotencyKey,
        UPDATED_USER_METADATA,
        false, // shouldInitializeAtlasResources
        false); // shouldInitializeInstalledProduct

    // Verify no products were affected
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());

    List<InstalledProduct> products = afterUpdate.get().atlasResources().installedProducts();
    assertEquals(1, products.size());

    InstalledProduct unchangedProduct = products.get(0);
    assertTrue(unchangedProduct.requestIdempotencyKeys().isEmpty());
    assertEquals(TEST_USER_METADATA, unchangedProduct.userMetadata());
    assertEquals(projectId, unchangedProduct.projectId());
    assertEquals(vercelResourceId, unchangedProduct.vercelResourceId());
  }

  @Test
  public void
      testUpdateInstalledProductIdempotencyKeyAndUserMetadataForCreate_NonExistentInstallation() {
    // Try to update non-existent installation
    String testIdempotencyKey = "test-create-key";
    String initialProjectName = "test-initial-project";
    vercelNativeInstallationDao.updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
        "non-existent-installation",
        initialProjectName,
        testIdempotencyKey,
        TEST_USER_METADATA,
        false, // shouldInitializeAtlasResources
        false); // shouldInitializeInstalledProduct

    // Verify no installation was created
    Optional<VercelNativeInstallation> result =
        vercelNativeInstallationDao.findByInstallationId("non-existent-installation");
    assertFalse(result.isPresent());
  }

  @Test
  public void testUpdateInstalledProductIdempotencyKeyAndUserMetadataForCreate_MultipleProducts() {
    // Create installation with multiple products, only one with matching initial project name
    String targetInitialProjectName = "target-project";
    String otherInitialProjectName = "other-project";

    InstalledProduct targetProduct =
        InstalledProduct.builder()
            .projectId(projectId)
            .vercelResourceId(vercelResourceId)
            .clusterName("target-cluster")
            .userMetadata(TEST_USER_METADATA)
            .status(VercelResourceStatus.PENDING)
            .initialProjectName(targetInitialProjectName)
            .createdAt(Instant.now())
            .build();

    ObjectId otherProjectId = ObjectId.get();
    ObjectId otherResourceId = ObjectId.get();
    InstalledProduct otherProduct =
        InstalledProduct.builder()
            .projectId(otherProjectId)
            .vercelResourceId(otherResourceId)
            .clusterName("other-cluster")
            .userMetadata(TEST_USER_METADATA)
            .status(VercelResourceStatus.READY)
            .initialProjectName(otherInitialProjectName)
            .createdAt(Instant.now())
            .build();

    AtlasResources atlasResourcesWithMultipleProducts =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(List.of(targetProduct, otherProduct))
            .build();

    VercelNativeInstallation installationWithMultipleProducts =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(atlasResourcesWithMultipleProducts)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithMultipleProducts);

    // Update only the product with matching initial project name
    String testIdempotencyKey = "test-create-key";
    vercelNativeInstallationDao.updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
        TEST_INSTALLATION_ID,
        targetInitialProjectName,
        testIdempotencyKey,
        UPDATED_USER_METADATA,
        false, // shouldInitializeAtlasResources
        false); // shouldInitializeInstalledProduct

    // Verify only the target product was updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    List<InstalledProduct> products = afterUpdate.get().atlasResources().installedProducts();
    assertEquals(2, products.size());

    // Find the products by their resource IDs
    InstalledProduct updatedProduct =
        products.stream()
            .filter(p -> vercelResourceId.equals(p.vercelResourceId()))
            .findFirst()
            .orElseThrow();
    InstalledProduct unchangedProduct =
        products.stream()
            .filter(p -> otherResourceId.equals(p.vercelResourceId()))
            .findFirst()
            .orElseThrow();

    // Updated product should have new idempotency key and metadata
    assertEquals(
        testIdempotencyKey,
        updatedProduct
            .requestIdempotencyKeys()
            .get(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name()));
    assertEquals(UPDATED_USER_METADATA, updatedProduct.userMetadata());
    assertEquals("target-cluster", updatedProduct.clusterName());

    // Other product should remain unchanged
    assertTrue(unchangedProduct.requestIdempotencyKeys().isEmpty());
    assertEquals(TEST_USER_METADATA, unchangedProduct.userMetadata());
    assertEquals("other-cluster", unchangedProduct.clusterName());
  }

  @Test
  public void testUpdateInstalledProductByIdempotencyKey() {
    // Create installation with a product that has an idempotency key
    String testIdempotencyKey = "test-create-key";
    InstalledProduct productWithIdempotencyKey =
        InstalledProduct.builder()
            .projectId(projectId)
            .vercelResourceId(vercelResourceId)
            .clusterName(TEST_CLUSTER_NAME)
            .userMetadata(TEST_USER_METADATA)
            .status(VercelResourceStatus.PENDING)
            .requestIdempotencyKeys(
                Map.of(
                    InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name(), testIdempotencyKey))
            .createdAt(Instant.now())
            .build();

    AtlasResources atlasResourcesWithIdempotencyKey =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(List.of(productWithIdempotencyKey))
            .build();

    VercelNativeInstallation installationWithIdempotencyKey =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(atlasResourcesWithIdempotencyKey)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithIdempotencyKey);

    // Create updated product with new status and cluster name
    ObjectId newProjectId = ObjectId.get();
    InstalledProduct updatedProduct =
        InstalledProduct.builder()
            .projectId(newProjectId)
            .vercelResourceId(vercelResourceId)
            .clusterName("updated-cluster-name")
            .userMetadata(UPDATED_USER_METADATA)
            .status(VercelResourceStatus.READY)
            .requestIdempotencyKeys(
                Map.of(
                    InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name(), testIdempotencyKey))
            .updatedAt(Instant.now())
            .build();

    // Update the product by idempotency key
    vercelNativeInstallationDao.updateInstalledProductByIdempotencyKey(
        testIdempotencyKey, InstalledProduct.RequestIdempotencyKeyTypes.CREATE, updatedProduct);

    // Verify the product was updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    InstalledProduct resultProduct = afterUpdate.get().atlasResources().installedProducts().get(0);

    assertEquals(newProjectId, resultProduct.projectId());
    assertEquals("updated-cluster-name", resultProduct.clusterName());
    assertEquals(UPDATED_USER_METADATA, resultProduct.userMetadata());
    assertEquals(VercelResourceStatus.READY, resultProduct.status());
    assertEquals(
        testIdempotencyKey,
        resultProduct
            .requestIdempotencyKeys()
            .get(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name()));
  }

  @Test
  public void testUpdateInstalledProductByIdempotencyKey_NonExistentKey() {
    // Create installation with a product
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Try to update with non-existent idempotency key
    String nonExistentKey = "non-existent-key";
    InstalledProduct updatedProduct =
        InstalledProduct.builder()
            .projectId(ObjectId.get())
            .vercelResourceId(ObjectId.get())
            .clusterName("should-not-be-updated")
            .userMetadata(UPDATED_USER_METADATA)
            .status(VercelResourceStatus.READY)
            .requestIdempotencyKeys(
                Map.of(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name(), nonExistentKey))
            .build();

    vercelNativeInstallationDao.updateInstalledProductByIdempotencyKey(
        nonExistentKey, InstalledProduct.RequestIdempotencyKeyTypes.CREATE, updatedProduct);

    // Verify the original product was not affected
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    InstalledProduct resultProduct = afterUpdate.get().atlasResources().installedProducts().get(0);

    assertEquals(projectId, resultProduct.projectId());
    assertEquals(TEST_CLUSTER_NAME, resultProduct.clusterName());
    assertEquals(TEST_USER_METADATA, resultProduct.userMetadata());
    assertEquals(VercelResourceStatus.READY, resultProduct.status());
  }

  @Test
  public void testUpdateInstalledProductByProjectId() {
    // Create installation with a product
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Verify initial state
    Optional<VercelNativeInstallation> beforeUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeUpdate.isPresent());
    InstalledProduct originalProduct =
        beforeUpdate.get().atlasResources().installedProducts().get(0);
    assertEquals(projectId, originalProduct.projectId());
    assertEquals(TEST_CLUSTER_NAME, originalProduct.clusterName());
    assertEquals(VercelResourceStatus.READY, originalProduct.status());

    // Create updated product with new values
    ObjectId newVercelResourceId = ObjectId.get();
    InstalledProduct updatedProduct =
        InstalledProduct.builder()
            .projectId(projectId) // Same project ID for matching
            .vercelResourceId(newVercelResourceId)
            .clusterName("updated-cluster-name")
            .userMetadata(UPDATED_USER_METADATA)
            .status(VercelResourceStatus.PENDING)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();

    // Update the product by project ID
    vercelNativeInstallationDao.updateInstalledProductByProjectId(projectId, updatedProduct);

    // Verify the product was updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    InstalledProduct resultProduct = afterUpdate.get().atlasResources().installedProducts().get(0);

    assertEquals(projectId, resultProduct.projectId());
    assertEquals(newVercelResourceId, resultProduct.vercelResourceId());
    assertEquals("updated-cluster-name", resultProduct.clusterName());
    assertEquals(UPDATED_USER_METADATA, resultProduct.userMetadata());
    assertEquals(VercelResourceStatus.PENDING, resultProduct.status());
    assertNotNull(resultProduct.updatedAt());
  }

  @Test
  public void testUpdateInstalledProductByProjectId_NonExistentProjectId() {
    // Create installation with a product
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Try to update with non-existent project ID
    ObjectId nonExistentProjectId = ObjectId.get();
    InstalledProduct updatedProduct =
        InstalledProduct.builder()
            .projectId(nonExistentProjectId)
            .vercelResourceId(ObjectId.get())
            .clusterName("should-not-be-updated")
            .userMetadata(UPDATED_USER_METADATA)
            .status(VercelResourceStatus.PENDING)
            .build();

    vercelNativeInstallationDao.updateInstalledProductByProjectId(
        nonExistentProjectId, updatedProduct);

    // Verify the original product was not affected
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    InstalledProduct resultProduct = afterUpdate.get().atlasResources().installedProducts().get(0);

    assertEquals(projectId, resultProduct.projectId());
    assertEquals(vercelResourceId, resultProduct.vercelResourceId());
    assertEquals(TEST_CLUSTER_NAME, resultProduct.clusterName());
    assertEquals(TEST_USER_METADATA, resultProduct.userMetadata());
    assertEquals(VercelResourceStatus.READY, resultProduct.status());
  }

  @Test
  public void testUpdateInstalledProductByProjectId_MultipleProducts() {
    // Create installation with multiple products
    ObjectId secondProjectId = ObjectId.get();
    ObjectId secondResourceId = ObjectId.get();
    InstalledProduct firstProduct = createTestCluster(projectId, vercelResourceId);
    InstalledProduct secondProduct = createTestCluster(secondProjectId, secondResourceId);

    AtlasResources atlasResourcesWithMultipleProducts =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(List.of(firstProduct, secondProduct))
            .build();

    VercelNativeInstallation installationWithMultipleProducts =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(atlasResourcesWithMultipleProducts)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithMultipleProducts);

    // Update only the second product
    InstalledProduct updatedSecondProduct =
        InstalledProduct.builder()
            .projectId(secondProjectId)
            .vercelResourceId(secondResourceId)
            .clusterName("updated-second-cluster")
            .userMetadata(UPDATED_USER_METADATA)
            .status(VercelResourceStatus.PENDING)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();

    vercelNativeInstallationDao.updateInstalledProductByProjectId(
        secondProjectId, updatedSecondProduct);

    // Verify only the second product was updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    List<InstalledProduct> products = afterUpdate.get().atlasResources().installedProducts();
    assertEquals(2, products.size());

    // Find products by their resource IDs
    InstalledProduct firstProductResult =
        products.stream()
            .filter(p -> vercelResourceId.equals(p.vercelResourceId()))
            .findFirst()
            .orElseThrow();
    InstalledProduct secondProductResult =
        products.stream()
            .filter(p -> secondResourceId.equals(p.vercelResourceId()))
            .findFirst()
            .orElseThrow();

    // First product should remain unchanged
    assertEquals(projectId, firstProductResult.projectId());
    assertEquals(TEST_CLUSTER_NAME, firstProductResult.clusterName());
    assertEquals(TEST_USER_METADATA, firstProductResult.userMetadata());
    assertEquals(VercelResourceStatus.READY, firstProductResult.status());

    // Second product should be updated
    assertEquals(secondProjectId, secondProductResult.projectId());
    assertEquals("updated-second-cluster", secondProductResult.clusterName());
    assertEquals(UPDATED_USER_METADATA, secondProductResult.userMetadata());
    assertEquals(VercelResourceStatus.PENDING, secondProductResult.status());
  }

  @Test
  public void testUpdateInstalledProductProjectIdAndNameByIdempotencyKey() {
    // Create installation with a product that has an idempotency key but no project data
    String testIdempotencyKey = "test-create-key";
    InstalledProduct partiallyInstalledProduct =
        InstalledProduct.builder()
            .userMetadata(TEST_USER_METADATA)
            .requestIdempotencyKeys(
                Map.of(
                    InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name(), testIdempotencyKey))
            .build();

    AtlasResources atlasResourcesWithIdempotencyKey =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(List.of(partiallyInstalledProduct))
            .build();

    VercelNativeInstallation installationWithPartiallyInstalledProduct =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(atlasResourcesWithIdempotencyKey)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithPartiallyInstalledProduct);

    // Verify initial state - no project ID or initial project name
    Optional<VercelNativeInstallation> beforeUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeUpdate.isPresent());
    InstalledProduct productBeforeUpdate =
        beforeUpdate.get().atlasResources().installedProducts().get(0);
    assertNull(productBeforeUpdate.projectId());
    assertNull(productBeforeUpdate.initialProjectName());

    // Update project ID and initial project name by idempotency key
    ObjectId newProjectId = ObjectId.get();
    String newInitialProjectName = "new-project-name";
    vercelNativeInstallationDao.updateInstalledProductProjectIdAndNameByIdempotencyKey(
        testIdempotencyKey,
        InstalledProduct.RequestIdempotencyKeyTypes.CREATE,
        newProjectId,
        newInitialProjectName);

    // Verify the project ID and initial project name were updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    InstalledProduct productAfterUpdate =
        afterUpdate.get().atlasResources().installedProducts().get(0);

    assertEquals(newProjectId, productAfterUpdate.projectId());
    assertEquals(newInitialProjectName, productAfterUpdate.initialProjectName());

    // Verify other fields remain unchanged
    assertEquals(TEST_USER_METADATA, productAfterUpdate.userMetadata());
    assertEquals(
        testIdempotencyKey,
        productAfterUpdate
            .requestIdempotencyKeys()
            .get(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name()));
  }

  @Test
  public void testUpdateInstalledProductProjectIdAndNameByIdempotencyKey_NonExistentKey() {
    // Create installation with a product that has an idempotency key but no project data
    String testIdempotencyKey = "test-create-key";
    InstalledProduct partiallyInstalledProduct =
        InstalledProduct.builder()
            .userMetadata(TEST_USER_METADATA)
            .requestIdempotencyKeys(
                Map.of(
                    InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name(), testIdempotencyKey))
            .build();

    AtlasResources atlasResourcesWithIdempotencyKey =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(List.of(partiallyInstalledProduct))
            .build();

    VercelNativeInstallation installationWithPartiallyInstalledProduct =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(atlasResourcesWithIdempotencyKey)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithPartiallyInstalledProduct);

    // Try to update with non-existent idempotency key
    String nonExistentKey = "non-existent-key";
    ObjectId newProjectId = ObjectId.get();
    String newInitialProjectName = "should-not-be-updated";

    vercelNativeInstallationDao.updateInstalledProductProjectIdAndNameByIdempotencyKey(
        nonExistentKey,
        InstalledProduct.RequestIdempotencyKeyTypes.CREATE,
        newProjectId,
        newInitialProjectName);

    // Verify the original product was not affected
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    InstalledProduct resultProduct = afterUpdate.get().atlasResources().installedProducts().get(0);

    assertNull(resultProduct.projectId());
    assertNull(resultProduct.initialProjectName());
    assertEquals(TEST_USER_METADATA, resultProduct.userMetadata());
    assertEquals(
        testIdempotencyKey,
        resultProduct
            .requestIdempotencyKeys()
            .get(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name()));
  }

  @Test
  public void testUpdateInstalledProductProjectIdAndNameByIdempotencyKey_MultipleProducts() {
    // Create installation with multiple products, only one with matching idempotency key
    String testIdempotencyKey = "test-create-key";
    String otherIdempotencyKey = "other-create-key";

    InstalledProduct productWithMatchingKey =
        InstalledProduct.builder()
            .userMetadata(TEST_USER_METADATA)
            .requestIdempotencyKeys(
                Map.of(
                    InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name(), testIdempotencyKey))
            .build();

    ObjectId otherResourceId = ObjectId.get();
    InstalledProduct productWithDifferentKey =
        InstalledProduct.builder()
            .initialProjectName("some-project-name")
            .projectId(ObjectId.get())
            .vercelResourceId(otherResourceId)
            .clusterName("other-cluster")
            .userMetadata(TEST_USER_METADATA)
            .status(VercelResourceStatus.READY)
            .requestIdempotencyKeys(
                Map.of(
                    InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name(), otherIdempotencyKey))
            .createdAt(Instant.now())
            .build();

    AtlasResources atlasResourcesWithMultipleProducts =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(List.of(productWithMatchingKey, productWithDifferentKey))
            .build();

    VercelNativeInstallation installationWithMultipleProducts =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(atlasResourcesWithMultipleProducts)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithMultipleProducts);

    // Update only the product with matching idempotency key
    ObjectId newProjectId = ObjectId.get();
    String newInitialProjectName = "updated-project-name";
    vercelNativeInstallationDao.updateInstalledProductProjectIdAndNameByIdempotencyKey(
        testIdempotencyKey,
        InstalledProduct.RequestIdempotencyKeyTypes.CREATE,
        newProjectId,
        newInitialProjectName);

    // Verify only the matching product was updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    List<InstalledProduct> products = afterUpdate.get().atlasResources().installedProducts();
    assertEquals(2, products.size());

    // Find the products by their resource IDs
    InstalledProduct updatedProduct =
        products.stream()
            .filter(
                p ->
                    testIdempotencyKey.equals(
                        p.requestIdempotencyKeys()
                            .get(InstalledProduct.RequestIdempotencyKeyTypes.CREATE.name())))
            .findFirst()
            .orElseThrow();
    InstalledProduct unchangedProduct =
        products.stream()
            .filter(p -> otherResourceId.equals(p.vercelResourceId()))
            .findFirst()
            .orElseThrow();

    // Updated product should have new project ID and name
    assertEquals(newProjectId, updatedProduct.projectId());
    assertEquals(newInitialProjectName, updatedProduct.initialProjectName());

    // Other product should remain unchanged
    assertNotEquals(newProjectId, unchangedProduct.projectId());
    assertNotEquals(newInitialProjectName, unchangedProduct.initialProjectName());
  }

  @Test
  public void testUpdateInstalledProductUserMetadata() {
    // Create installation with an active product
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Update all fields: metadata, status, and notification
    vercelNativeInstallationDao.updateInstalledProductUserMetadata(
        vercelResourceId, UPDATED_USER_METADATA, VercelResourceStatus.PENDING, TEST_NOTIFICATION);

    // Verify all fields were updated
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    InstalledProduct productAfterUpdate =
        afterUpdate.get().atlasResources().installedProducts().get(0);
    assertEquals(UPDATED_USER_METADATA, productAfterUpdate.userMetadata());
    assertEquals(VercelResourceStatus.PENDING, productAfterUpdate.status());
    assertEquals(TEST_NOTIFICATION, productAfterUpdate.notification());
    assertNotNull(productAfterUpdate.updatedAt());
  }

  @Test
  public void testUpdateInstalledProductUserMetadata_UpdateUserMetadataOnly() {
    // Create installation with an active product
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Verify the product has initial metadata
    Optional<VercelNativeInstallation> beforeUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeUpdate.isPresent());
    InstalledProduct productBeforeUpdate =
        beforeUpdate.get().atlasResources().installedProducts().get(0);
    assertEquals(TEST_USER_METADATA, productBeforeUpdate.userMetadata());
    assertEquals(VercelResourceStatus.READY, productBeforeUpdate.status());
    assertNull(productBeforeUpdate.notification());

    // Update only the user metadata
    vercelNativeInstallationDao.updateInstalledProductUserMetadata(
        vercelResourceId, UPDATED_USER_METADATA, null, null);

    // Verify the metadata was updated but status and notification remain unchanged
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    InstalledProduct productAfterUpdate =
        afterUpdate.get().atlasResources().installedProducts().get(0);
    assertEquals(UPDATED_USER_METADATA, productAfterUpdate.userMetadata());
    assertNotNull(productAfterUpdate.updatedAt());

    // these values should be unchanged from before
    assertEquals(VercelResourceStatus.READY, productAfterUpdate.status());
    assertNull(productAfterUpdate.notification());
  }

  @Test
  public void testUpdateInstalledProductUserMetadata_NonExistentResource() {
    // Create installation with an active product
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Try to update a non-existent resource
    ObjectId nonExistentResourceId = ObjectId.get();
    vercelNativeInstallationDao.updateInstalledProductUserMetadata(
        nonExistentResourceId, UPDATED_USER_METADATA, VercelResourceStatus.PENDING, null);

    // Verify the existing product was not affected
    Optional<VercelNativeInstallation> afterUpdate =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterUpdate.isPresent());
    InstalledProduct product = afterUpdate.get().atlasResources().installedProducts().get(0);
    assertEquals(TEST_USER_METADATA, product.userMetadata()); // Should remain unchanged
    assertEquals(VercelResourceStatus.READY, product.status()); // Should remain unchanged
  }

  @Test
  public void testUpdateInstalledProductUserMetadata_MultipleProducts() {
    // Create installation with multiple products
    ObjectId secondProjectId = ObjectId.get();
    ObjectId secondResourceId = ObjectId.get();
    InstalledProduct secondProduct = createTestCluster(secondProjectId, secondResourceId);

    AtlasResources atlasResourcesWithMultipleProducts =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(
                List.of(createTestCluster(projectId, vercelResourceId), secondProduct))
            .build();

    VercelNativeInstallation installationWithMultipleProducts =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(atlasResourcesWithMultipleProducts)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithMultipleProducts);

    // Update only the second product
    vercelNativeInstallationDao.updateInstalledProductUserMetadata(
        secondResourceId, UPDATED_USER_METADATA, VercelResourceStatus.PENDING, TEST_NOTIFICATION);
  }

  @Test
  public void testSoftDeleteInstallation() {
    // Create installation with access token
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Verify initial state - installation exists and is active
    Optional<VercelNativeInstallation> beforeDelete =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeDelete.isPresent());
    assertNull(beforeDelete.get().uninstalledAt());
    assertNotNull(
        beforeDelete
            .get()
            .vercelData()
            .accessToken()); // Access token is encrypted, just verify it exists

    // Soft delete the installation
    vercelNativeInstallationDao.softDeleteInstallation(TEST_INSTALLATION_ID);

    // Verify installation still exists but is marked as deleted
    Optional<VercelNativeInstallation> afterDelete =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterDelete.isPresent());

    VercelNativeInstallation deletedInstallation = afterDelete.get();
    assertNotNull(deletedInstallation.uninstalledAt());
    assertNull(deletedInstallation.vercelData().accessToken()); // Access token should be removed

    // Other fields should remain unchanged
    assertEquals(TEST_INSTALLATION_ID, deletedInstallation.vercelData().installationId());
    assertEquals(TEST_TEAM_NAME, deletedInstallation.vercelData().teamName());
    assertEquals(TEST_INSTALLATION_URL, deletedInstallation.vercelData().installationUrl());
    assertNotNull(deletedInstallation.atlasResources());
  }

  @Test
  public void testSoftDeleteInstallation_NonExistentInstallation() {
    // Try to soft delete non-existent installation
    vercelNativeInstallationDao.softDeleteInstallation("non-existent-installation");

    // Verify no installation was created or modified
    Optional<VercelNativeInstallation> result =
        vercelNativeInstallationDao.findByInstallationId("non-existent-installation");
    assertFalse(result.isPresent());
  }

  @Test
  public void testSoftDeleteInstallation_AlreadyDeleted() {
    // Create installation that's already soft deleted -- don't expect this to happen in reality but
    // currently this is technically allowed by the DAO

    VercelNativeInstallation alreadyDeletedInstallation =
        installation.toBuilder().uninstalledAt(Instant.now()).build();
    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, alreadyDeletedInstallation);

    // Get the initial uninstalledAt timestamp
    Optional<VercelNativeInstallation> beforeSecondDelete =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeSecondDelete.isPresent());
    Instant firstUninstalledAt = beforeSecondDelete.get().uninstalledAt();
    assertNotNull(firstUninstalledAt);

    // Soft delete again
    vercelNativeInstallationDao.softDeleteInstallation(TEST_INSTALLATION_ID);

    // Verify uninstalledAt was updated to a new timestamp
    Optional<VercelNativeInstallation> afterSecondDelete =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterSecondDelete.isPresent());
    Instant secondUninstalledAt = afterSecondDelete.get().uninstalledAt();
    assertNotNull(secondUninstalledAt);
    assertTrue(secondUninstalledAt.isAfter(firstUninstalledAt));
    assertNull(afterSecondDelete.get().vercelData().accessToken());
  }

  @Test
  public void testSoftDeleteInstalledProduct() {
    // Create installation with an active product
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Verify the product is initially active
    Optional<VercelNativeInstallation> beforeDelete =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(beforeDelete.isPresent());
    InstalledProduct productBeforeDelete =
        beforeDelete.get().atlasResources().installedProducts().get(0);
    assertEquals(VercelResourceStatus.READY, productBeforeDelete.status());
    assertNull(productBeforeDelete.uninstalledAt());

    // Delete the installed product
    vercelNativeInstallationDao.softDeleteInstalledProduct(vercelResourceId);

    // Verify the product was marked as deleted
    Optional<VercelNativeInstallation> afterDelete =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterDelete.isPresent());
    InstalledProduct productAfterDelete =
        afterDelete.get().atlasResources().installedProducts().get(0);
    assertEquals(VercelResourceStatus.UNINSTALLED, productAfterDelete.status());
    assertNotNull(productAfterDelete.uninstalledAt());
  }

  @Test
  public void testSoftDeleteInstalledProduct_NonExistentResource() {
    // Create installation with an active product
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);

    // Try to delete a non-existent resource
    ObjectId nonExistentResourceId = ObjectId.get();
    vercelNativeInstallationDao.softDeleteInstalledProduct(nonExistentResourceId);

    // Verify the existing product was not affected
    Optional<VercelNativeInstallation> afterDelete =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterDelete.isPresent());
    InstalledProduct product = afterDelete.get().atlasResources().installedProducts().get(0);
    assertEquals(VercelResourceStatus.READY, product.status());
    assertNull(product.uninstalledAt());
  }

  @Test
  public void testSoftDeleteInstalledProduct_MultipleProducts() {
    // Create installation with multiple products
    ObjectId secondProjectId = ObjectId.get();
    ObjectId secondResourceId = ObjectId.get();
    InstalledProduct secondProduct = createTestCluster(secondProjectId, secondResourceId);

    AtlasResources atlasResourcesWithMultipleProducts =
        AtlasResources.builder()
            .organizationId(organizationId)
            .installedProducts(
                List.of(createTestCluster(projectId, vercelResourceId), secondProduct))
            .build();

    VercelNativeInstallation installationWithMultipleProducts =
        VercelNativeInstallation.builder()
            .id(id)
            .vercelData(createTestVercelData(TEST_INSTALLATION_ID))
            .atlasResources(atlasResourcesWithMultipleProducts)
            .createdAt(Instant.now())
            .build();

    vercelNativeInstallationDao.upsertInstallation(
        TEST_INSTALLATION_ID, installationWithMultipleProducts);

    // Delete only the second product
    vercelNativeInstallationDao.softDeleteInstalledProduct(secondResourceId);

    // Verify only the second product was marked as deleted
    Optional<VercelNativeInstallation> afterDelete =
        vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID);
    assertTrue(afterDelete.isPresent());
    List<InstalledProduct> products = afterDelete.get().atlasResources().installedProducts();
    assertEquals(2, products.size());

    // Find the products by their resource IDs
    InstalledProduct firstProduct =
        products.stream()
            .filter(p -> vercelResourceId.equals(p.vercelResourceId()))
            .findFirst()
            .orElseThrow();
    InstalledProduct deletedProduct =
        products.stream()
            .filter(p -> secondResourceId.equals(p.vercelResourceId()))
            .findFirst()
            .orElseThrow();

    // First product should remain active
    assertEquals(VercelResourceStatus.READY, firstProduct.status());
    assertNull(firstProduct.uninstalledAt());

    // Second product should be marked as deleted
    assertEquals(VercelResourceStatus.UNINSTALLED, deletedProduct.status());
    assertNotNull(deletedProduct.uninstalledAt());
  }

  @Test
  public void testDeleteInstallation() {
    // Test creation, deletion, and verification
    vercelNativeInstallationDao.upsertInstallation(TEST_INSTALLATION_ID, installation);
    Optional<VercelNativeInstallation> deletedInstallation =
        vercelNativeInstallationDao.deleteInstallation(TEST_INSTALLATION_ID);
    assertTrue(deletedInstallation.isPresent());
    assertEquals(TEST_INSTALLATION_ID, deletedInstallation.get().vercelData().installationId());
    assertFalse(vercelNativeInstallationDao.findByInstallationId(TEST_INSTALLATION_ID).isPresent());
  }

  @Test
  public void testDeleteNonExistentInstallation() {
    Optional<VercelNativeInstallation> deletedInstallation =
        vercelNativeInstallationDao.deleteInstallation("non-existent-id");
    assertFalse(deletedInstallation.isPresent());
  }

  // TODO when have time: can prob remove/refactor these to use
  // server/src/unit/com/xgen/cloud/partnerintegrations/vercelnative/utils/TestUtils.java fns
  private VercelData createTestVercelData(String installationId) {
    return VercelData.builder()
        .installationId(installationId)
        .accessToken(TEST_ACCESS_TOKEN, vercelNativeConfig.getPartnerIntegrationEncryptionKey())
        .teamName(TEST_TEAM_NAME)
        .installationUrl(TEST_INSTALLATION_URL)
        .contact(VercelContact.builder().name(TEST_USER_NAME).email(TEST_USER_EMAIL).build())
        .acceptedPolicies(TEST_ACCEPTED_POLICIES)
        .build();
  }

  private InstalledProduct createTestCluster(ObjectId projectId, ObjectId vercelResourceId) {
    return InstalledProduct.builder()
        .projectId(projectId)
        .vercelResourceId(vercelResourceId)
        .clusterName(TEST_CLUSTER_NAME)
        .userMetadata(TEST_USER_METADATA)
        .status(VercelResourceStatus.READY)
        .createdAt(Instant.now())
        .build();
  }

  private AtlasResources createTestAtlasResources(
      ObjectId organizationId, InstalledProduct cluster) {
    return AtlasResources.builder()
        .organizationId(organizationId)
        .installedProducts(List.of(cluster))
        .build();
  }

  private VercelNativeInstallation createTestInstallation(
      String installationId,
      ObjectId projectId,
      ObjectId organizationId,
      ObjectId vercelResourceId,
      ObjectId id) {
    VercelData vercelData = createTestVercelData(installationId);
    InstalledProduct cluster = createTestCluster(projectId, vercelResourceId);
    AtlasResources atlasResources = createTestAtlasResources(organizationId, cluster);

    Instant timestamp = Instant.now();

    return VercelNativeInstallation.builder()
        .id(id)
        .vercelData(vercelData)
        .atlasResources(atlasResources)
        .createdAt(timestamp)
        .updatedAt(timestamp)
        .build();
  }
}
