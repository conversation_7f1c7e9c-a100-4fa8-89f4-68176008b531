package com.xgen.cloud.services.metrics._private.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.github.benmanes.caffeine.cache.Cache;
import com.xgen.cloud.services.core.ServicesGuiceTestExtension;
import com.xgen.cloud.services.core.WithModule;
import com.xgen.cloud.services.metrics._private.dao.mongodb.apikey.AdminApiKeyMetadataDao;
import com.xgen.cloud.services.metrics._private.dao.mongodb.apikey.PublisherApiKeyMetadataDao;
import com.xgen.cloud.services.metrics._private.model.apikey.AdminApiKeyMetadata;
import com.xgen.cloud.services.metrics._private.model.apikey.ApiKeyType;
import com.xgen.cloud.services.metrics._private.model.apikey.PublisherApiKeyMetadata;
import com.xgen.cloud.services.metrics._private.svc.apikey.ApiKeyJwtSvc;
import com.xgen.cloud.services.metrics._private.svc.apikey.PublisherApiKeyInfo;
import com.xgen.cloud.services.metrics.runtime.guice.MetricsServiceModule;
import jakarta.inject.Inject;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(ServicesGuiceTestExtension.class)
@WithModule(MetricsServiceModule.class)
public class ApiKeyJwtSvcIntTests {

  @Inject private PublisherApiKeyMetadataDao publisherApiKeyMetadataDao;
  @Inject private AdminApiKeyMetadataDao adminApiKeyDao;
  @Inject private ApiKeyJwtSvc apiKeyJwtSvc;

  static final int DEFAULT_SKIP = 0;
  static final int DEFAULT_LIMIT = 100;

  @BeforeEach
  public void setup() throws Exception {
    // Wipe the db
    adminApiKeyDao.getCollection().deleteMany(new Document());
    publisherApiKeyMetadataDao.getCollection().deleteMany(new Document());

    // Clear the JWT verification cache to ensure test isolation
    apiKeyJwtSvc.getJwtVerificationCache().invalidateAll();
  }

  @Test
  public void testDecodeAndVerifyJwt_invalidSignature() {
    // sample jwt generated using https://www.scottbrady91.com/tools/jwt with ES512 algo
    // signing key was a randomly generated key
    final String jwt =
        "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    assertThrows(JWTVerificationException.class, () -> apiKeyJwtSvc.decodeAndVerifyJwt(jwt));
  }

  @Test
  public void testCreateAdminApiKeyJWT_succeeds() {
    final UUID sampleUsecaseUUID = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final String jwt = apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID);

    // decode the jwt and verify the signature
    final DecodedJWT decodedJWT = apiKeyJwtSvc.decodeAndVerifyJwt(jwt);

    // check if custom claims were populated in the jwt
    assertEquals(decodedJWT.getClaim("usecaseUUID").asString(), sampleUsecaseUUID.toString());
    assertEquals(decodedJWT.getClaim("type").asString(), ApiKeyType.ADMIN.name());

    // check if we saved metadata in our backing db
    final Optional<AdminApiKeyMetadata> apiKeyMetadata =
        apiKeyJwtSvc.getAdminApiKeyMetadataForUsecaseAndJwtId(
            sampleUsecaseUUID, UUID.fromString(decodedJWT.getId()));
    assertTrue(apiKeyMetadata.isPresent());
  }

  @Test
  public void testGetAllAdminApiKeyMetadataForUsecase() {
    final UUID sampleUsecaseUUID1 = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final UUID sampleUsecaseUUID2 = UUID.fromString("e8754efd-670e-537d-9545-6b74799ae785");
    final UUID sampleUsecaseUUID3 = UUID.fromString("9ccf0195-31a4-5514-98e6-de114243dd26");
    final DecodedJWT jwt1 =
        apiKeyJwtSvc.decodeAndVerifyJwt(apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID1));
    final DecodedJWT jwt2 =
        apiKeyJwtSvc.decodeAndVerifyJwt(apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID1));
    final DecodedJWT jwt3 =
        apiKeyJwtSvc.decodeAndVerifyJwt(apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID1));
    final DecodedJWT jwt4 =
        apiKeyJwtSvc.decodeAndVerifyJwt(apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID2));
    final DecodedJWT jwt5 =
        apiKeyJwtSvc.decodeAndVerifyJwt(apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID2));

    assertEquals(3, apiKeyJwtSvc.getAllAdminApiKeyMetadataForUsecase(sampleUsecaseUUID1).size());
    assertEquals(2, apiKeyJwtSvc.getAllAdminApiKeyMetadataForUsecase(sampleUsecaseUUID2).size());
    assertEquals(0, apiKeyJwtSvc.getAllAdminApiKeyMetadataForUsecase(sampleUsecaseUUID3).size());
  }

  @Test
  public void testRevokeAdminJWT() {
    final UUID sampleUsecaseUUID = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final DecodedJWT jwt1 =
        apiKeyJwtSvc.decodeAndVerifyJwt(apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID));
    final DecodedJWT jwt2 =
        apiKeyJwtSvc.decodeAndVerifyJwt(apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID));

    apiKeyJwtSvc.revokeAdminJWT(sampleUsecaseUUID, UUID.fromString(jwt1.getId()));
    final Optional<AdminApiKeyMetadata> apiKeyMetadata1 =
        apiKeyJwtSvc.getAdminApiKeyMetadataForUsecaseAndJwtId(
            sampleUsecaseUUID, UUID.fromString(jwt1.getId()));
    final Optional<AdminApiKeyMetadata> apiKeyMetadata2 =
        apiKeyJwtSvc.getAdminApiKeyMetadataForUsecaseAndJwtId(
            sampleUsecaseUUID, UUID.fromString(jwt2.getId()));

    assertTrue(apiKeyMetadata1.orElseThrow().getRevoked());
    assertFalse(apiKeyMetadata2.orElseThrow().getRevoked());
  }

  @Test
  public void testExtractAndVerifyAdminApiKeyFromAuthHeader() {
    final UUID sampleUsecaseUUID = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final String jwt = apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID);
    final String authHeader = "Bearer " + jwt;
    final DecodedJWT decodedJWT =
        apiKeyJwtSvc.extractAndVerifyAdminApiKeyFromAuthHeader(authHeader);

    // check if custom claims were populated in the jwt
    assertEquals(decodedJWT.getClaim("usecaseUUID").asString(), sampleUsecaseUUID.toString());
    assertEquals(decodedJWT.getClaim("type").asString(), ApiKeyType.ADMIN.name());
  }

  @Test
  public void testExtractAndVerifyPublisherApiKeyFromAuthHeader() {
    final UUID sampleUsecaseUUID = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final Map<String, String> publisherLabels = Map.of("groupId", "foo", "orgId", "bar");
    final String jwt = apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID, publisherLabels);
    final String authHeader = "Bearer " + jwt;
    final PublisherApiKeyInfo decodedClaims =
        apiKeyJwtSvc.extractAndVerifyPublisherApiKeyFromAuthHeader(authHeader);

    // check if custom claims were populated in the jwt
    assertEquals(decodedClaims.usecaseId(), sampleUsecaseUUID);
    assertEquals(decodedClaims.requiredLabels().size(), publisherLabels.size());
    assertEquals(decodedClaims.requiredLabels().get("groupId"), publisherLabels.get("groupId"));
    assertEquals(decodedClaims.requiredLabels().get("orgId"), publisherLabels.get("orgId"));
    assertNotNull(decodedClaims.jwtId());
  }

  @Test
  public void testExtractRevokedPublisherApiKey() throws Exception {
    final UUID sampleUsecaseUUID = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final Map<String, String> publisherLabels1 = Map.of("groupId", "foo1", "orgId", "bar1");
    final String jwt = apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID, publisherLabels1);
    final String authHeader = "Bearer " + jwt;

    final DecodedJWT decodedJWT = apiKeyJwtSvc.decodeAndVerifyJwt(jwt);
    assertEquals(0, apiKeyJwtSvc.getRevokedPublisherKeysCache().size());

    apiKeyJwtSvc.revokePublisherJWT(sampleUsecaseUUID, UUID.fromString(decodedJWT.getId()));
    TimeUnit.SECONDS.sleep(70);

    final PublisherApiKeyInfo decodedClaims =
        apiKeyJwtSvc.extractAndVerifyPublisherApiKeyFromAuthHeader(authHeader);
    assertNull(decodedClaims);

    assertEquals(1, apiKeyJwtSvc.getRevokedPublisherKeysCache().size());
  }

  @Test
  public void testCreatePublisherApiKeyJWT_succeeds() {
    final UUID sampleUsecaseUUID = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final Map<String, String> publisherLabels = Map.of("groupId", "foo", "orgId", "bar");
    final String jwt = apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID, publisherLabels);

    // decode the jwt and verify the signature
    final DecodedJWT decodedJWT = apiKeyJwtSvc.decodeAndVerifyJwt(jwt);

    // check if custom claims were populated in the jwt
    assertEquals(decodedJWT.getClaim("usecaseUUID").asString(), sampleUsecaseUUID.toString());
    assertEquals(decodedJWT.getClaim("type").asString(), ApiKeyType.PUBLISHER.name());
    final Map<String, Object> decodedPublisherLabels =
        decodedJWT.getClaim("requiredPublisherLabels").asMap();
    assertEquals(decodedPublisherLabels.size(), publisherLabels.size());
    assertEquals(decodedPublisherLabels.get("groupId"), publisherLabels.get("groupId"));
    assertEquals(decodedPublisherLabels.get("orgId"), publisherLabels.get("orgId"));

    // check if we saved metadata in our backing db
    final Optional<PublisherApiKeyMetadata> apiKeyMetadata =
        apiKeyJwtSvc.getPublisherApiKeyMetadataForUsecaseAndJwtId(
            sampleUsecaseUUID, UUID.fromString(decodedJWT.getId()));
    assertTrue(apiKeyMetadata.isPresent());
    assertNotNull(apiKeyMetadata.get().getRequiredLabels());
  }

  @Test
  public void testGetAllPublisherApiKeyMetadataForUsecase() {
    final UUID sampleUsecaseUUID1 = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final UUID sampleUsecaseUUID2 = UUID.fromString("e8754efd-670e-537d-9545-6b74799ae785");
    final UUID sampleUsecaseUUID3 = UUID.fromString("9ccf0195-31a4-5514-98e6-de114243dd26");

    final Map<String, String> publisherLabels1 = Map.of("groupId", "foo1", "orgId", "bar1");
    final Map<String, String> publisherLabels2 = Map.of("groupId", "foo2", "orgId", "bar2");
    final Map<String, String> publisherLabels3 = Map.of("groupId", "foo3", "orgId", "bar3");
    final DecodedJWT jwt1 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels1));
    final DecodedJWT jwt2 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels2));
    final DecodedJWT jwt3 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels3));
    final DecodedJWT jwt4 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels1));
    final DecodedJWT jwt5 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels1));

    assertEquals(
        3,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecase(
                sampleUsecaseUUID1, DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());
    assertEquals(
        2,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecase(
                sampleUsecaseUUID2, DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());
    assertEquals(
        0,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecase(
                sampleUsecaseUUID3, DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());
  }

  @Test
  public void testGetAllPublisherApiKeyMetadataForUsecasePaging() {
    final UUID sampleUsecaseUUID1 = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final UUID sampleUsecaseUUID2 = UUID.fromString("e8754efd-670e-537d-9545-6b74799ae785");

    final Map<String, String> publisherLabels1 = Map.of("groupId", "foo1", "orgId", "bar1");
    final Map<String, String> publisherLabels2 = Map.of("groupId", "foo2", "orgId", "bar2");
    final Map<String, String> publisherLabels3 = Map.of("groupId", "foo3", "orgId", "bar3");
    final Map<String, String> publisherLabels4 = Map.of("groupId", "foo4", "orgId", "bar4");
    final Map<String, String> publisherLabels5 = Map.of("groupId", "foo5", "orgId", "bar5");
    final Map<String, String> publisherLabels6 = Map.of("groupId", "foo6", "orgId", "bar6");
    final DecodedJWT jwt1 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels1));
    final DecodedJWT jwt2 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels2));
    final DecodedJWT jwt3 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels3));
    final DecodedJWT jwt4 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels4));
    final DecodedJWT jwt5 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels5));
    final DecodedJWT jwt6 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels6));
    final DecodedJWT jwt7 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels1));
    final DecodedJWT jwt8 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels2));
    final DecodedJWT jwt9 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels3));
    final DecodedJWT jwt10 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels4));
    final DecodedJWT jwt11 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels5));
    final DecodedJWT jwt12 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels6));

    // check if we all metadata docs for a use-case with a liberal limit and zero skip
    assertEquals(
        6,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecase(
                sampleUsecaseUUID1, DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());
    assertEquals(
        6,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecase(
                sampleUsecaseUUID2, DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());

    // check if we get the latest jwt for a use-case with a skip of 0 and limit of 1
    assertEquals(
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecase(sampleUsecaseUUID1, 0, 1)
            .get(0)
            .getId()
            .toString(),
        jwt6.getId());

    // check if we get 2 jwt for a use-case with a skip of 4 docs and limit of 100
    assertEquals(
        2, apiKeyJwtSvc.getAllPublisherApiKeyMetadataForUsecase(sampleUsecaseUUID1, 4, 100).size());
  }

  @Test
  public void testGetAllPublisherApiKeyMetadataForUsecaseAndPublisherLabels() {
    final UUID sampleUsecaseUUID1 = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final UUID sampleUsecaseUUID2 = UUID.fromString("e8754efd-670e-537d-9545-6b74799ae785");
    final UUID sampleUsecaseUUID3 = UUID.fromString("9ccf0195-31a4-5514-98e6-de114243dd26");

    final Map<String, String> publisherLabels1 = Map.of("groupId", "foo1", "orgId", "bar1");
    final Map<String, String> publisherLabels2 = Map.of("groupId", "foo2", "orgId", "bar2");
    final Map<String, String> publisherLabels3 = Map.of("groupId", "foo3", "orgId", "bar3");
    final DecodedJWT jwt1 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels1));
    final DecodedJWT jwt2 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels1));
    final DecodedJWT jwt3 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID1, publisherLabels3));
    final DecodedJWT jwt4 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels2));
    final DecodedJWT jwt5 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID2, publisherLabels2));
    final DecodedJWT jwt6 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID3, publisherLabels1));

    assertEquals(
        2,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecaseAndRequiredLabels(
                sampleUsecaseUUID1, Map.of("orgId", "bar1"), DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());
    assertEquals(
        1,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecaseAndRequiredLabels(
                sampleUsecaseUUID1, Map.of("orgId", "bar3"), DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());
    assertEquals(
        0,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecaseAndRequiredLabels(
                sampleUsecaseUUID1, Map.of("orgId", "bar2"), DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());
    assertEquals(
        2,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecaseAndRequiredLabels(
                sampleUsecaseUUID2, Map.of("groupId", "foo2"), DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());
    assertEquals(
        0,
        apiKeyJwtSvc
            .getAllPublisherApiKeyMetadataForUsecaseAndRequiredLabels(
                sampleUsecaseUUID2, Map.of("groupId", "foo1"), DEFAULT_SKIP, DEFAULT_LIMIT)
            .size());
  }

  @Test
  public void testRevokePublisherJWT() {
    final UUID sampleUsecaseUUID = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final Map<String, String> publisherLabels1 = Map.of("groupId", "foo1", "orgId", "bar1");
    final Map<String, String> publisherLabels2 = Map.of("groupId", "foo2", "orgId", "bar2");
    final DecodedJWT jwt1 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID, publisherLabels1));
    final DecodedJWT jwt2 =
        apiKeyJwtSvc.decodeAndVerifyJwt(
            apiKeyJwtSvc.createPublisherJWT(sampleUsecaseUUID, publisherLabels2));

    apiKeyJwtSvc.revokePublisherJWT(sampleUsecaseUUID, UUID.fromString(jwt1.getId()));
    final Optional<PublisherApiKeyMetadata> apiKeyMetadata1 =
        apiKeyJwtSvc.getPublisherApiKeyMetadataForUsecaseAndJwtId(
            sampleUsecaseUUID, UUID.fromString(jwt1.getId()));
    final Optional<PublisherApiKeyMetadata> apiKeyMetadata2 =
        apiKeyJwtSvc.getPublisherApiKeyMetadataForUsecaseAndJwtId(
            sampleUsecaseUUID, UUID.fromString(jwt2.getId()));

    assertTrue(apiKeyMetadata1.orElseThrow().getRevoked());
    assertFalse(apiKeyMetadata2.orElseThrow().getRevoked());
  }

  @Test
  public void testGetJwtVerificationCache() {
    // Test that the JWT verification cache is always created
    final Cache<String, String> jwtVerificationCache = apiKeyJwtSvc.getJwtVerificationCache();
    assertNotNull(jwtVerificationCache);

    // Initially the cache should be empty
    assertEquals(0, jwtVerificationCache.estimatedSize());
  }

  @Test
  public void testJwtVerificationCaching() {
    final UUID sampleUsecaseUUID = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final String jwt = apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID);

    final Cache<String, String> jwtVerificationCache = apiKeyJwtSvc.getJwtVerificationCache();
    final long initialCacheSize = jwtVerificationCache.estimatedSize();

    // First verification should work
    final DecodedJWT decodedJWT1 = apiKeyJwtSvc.decodeAndVerifyJwt(jwt);
    assertNotNull(decodedJWT1);

    // Second verification of the same JWT should work
    final DecodedJWT decodedJWT2 = apiKeyJwtSvc.decodeAndVerifyJwt(jwt);
    assertNotNull(decodedJWT2);
    assertEquals(decodedJWT1.getId(), decodedJWT2.getId());

    // Test cache behavior based on enabled flag
    if (apiKeyJwtSvc.isJwtVerificationCacheEnabled()) {
      // When enabled, cache should contain the JWT
      assertEquals(initialCacheSize + 1, jwtVerificationCache.estimatedSize());

      final String jwtId = decodedJWT1.getId();
      final String cachedToken = jwtVerificationCache.getIfPresent(jwtId);
      assertNotNull(cachedToken);
      assertEquals(jwt, cachedToken);
    } else {
      // When disabled, cache should remain empty
      assertEquals(initialCacheSize, jwtVerificationCache.estimatedSize());
    }
  }

  @Test
  public void testJwtVerificationCacheWithDifferentTokens() {
    final UUID sampleUsecaseUUID1 = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final UUID sampleUsecaseUUID2 = UUID.fromString("e8754efd-670e-537d-9545-6b74799ae785");

    final String jwt1 = apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID1);
    final String jwt2 = apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID2);

    final Cache<String, String> jwtVerificationCache = apiKeyJwtSvc.getJwtVerificationCache();
    final long initialCacheSize = jwtVerificationCache.estimatedSize();

    // Verify both JWTs
    final DecodedJWT decodedJWT1 = apiKeyJwtSvc.decodeAndVerifyJwt(jwt1);
    final DecodedJWT decodedJWT2 = apiKeyJwtSvc.decodeAndVerifyJwt(jwt2);

    assertNotNull(decodedJWT1);
    assertNotNull(decodedJWT2);

    // Test cache behavior based on enabled flag
    if (apiKeyJwtSvc.isJwtVerificationCacheEnabled()) {
      // When enabled, cache should contain both tokens
      assertEquals(initialCacheSize + 2, jwtVerificationCache.estimatedSize());
      assertEquals(jwt1, jwtVerificationCache.getIfPresent(decodedJWT1.getId()));
      assertEquals(jwt2, jwtVerificationCache.getIfPresent(decodedJWT2.getId()));
    } else {
      // When disabled, cache should remain empty
      assertEquals(initialCacheSize, jwtVerificationCache.estimatedSize());
    }
  }

  @Test
  public void testJwtVerificationCacheSecurityWithSameJwtId() {
    // This test only makes sense when caching is enabled
    if (!apiKeyJwtSvc.isJwtVerificationCacheEnabled()) {
      return; // Skip test when caching is disabled
    }

    final UUID sampleUsecaseUUID = UUID.fromString("3afd5b48-3005-51e9-8009-0e8d3b784628");
    final String jwt = apiKeyJwtSvc.createAdminApiKeyJWT(sampleUsecaseUUID);

    // First verification should cache the token
    final DecodedJWT decodedJWT = apiKeyJwtSvc.decodeAndVerifyJwt(jwt);
    assertNotNull(decodedJWT);

    final Cache<String, String> jwtVerificationCache = apiKeyJwtSvc.getJwtVerificationCache();
    final String jwtId = decodedJWT.getId();

    // Verify the token is cached
    assertEquals(jwt, jwtVerificationCache.getIfPresent(jwtId));

    // Manually put a different token with the same JWT ID in cache (simulating attack)
    final String fakeToken = "fake-token-with-same-id";
    jwtVerificationCache.put(jwtId, fakeToken);

    // Verification should still work correctly and update cache with correct token
    final DecodedJWT decodedJWTAfterAttack = apiKeyJwtSvc.decodeAndVerifyJwt(jwt);
    assertNotNull(decodedJWTAfterAttack);

    // Cache should now contain the correct token again
    assertEquals(jwt, jwtVerificationCache.getIfPresent(jwtId));
  }
}
