{"platforms": {"/linux/mongodb-linux-x86_64-x.x.x.tgz": {"supportedVersionSegments": [{"min": "2.6.0", "max": "4.0.28"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux"}}, "/osx/mongodb-osx-x86_64-x.x.x.tgz": {"supportedVersionSegments": [{"min": "2.6.0", "max": "3.4.24"}], "versionGenericParameters": {"architecture": "amd64", "platform": "osx"}}, "/win32/mongodb-win32-x86_64-x.x.x.zip": {"supportedVersionSegments": [{"min": "2.6.0", "max": "3.4.24"}], "versionGenericParameters": {"architecture": "amd64", "platform": "windows"}}, "/win32/mongodb-win32-x86_64-2008plus-x.x.x.zip": {"supportedVersionSegments": [{"min": "2.6.0", "max": "3.2.11"}, {"min": "3.4.0", "max": "3.4.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "windows", "win2008plus": true}}, "/linux/mongodb-linux-x86_64-enterprise-amzn64-x.x.x.tgz": {"supportedVersionSegments": [{"min": "2.6.0", "max": "5.0.31"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "amazon", "maxOsVersion": "", "minOsVersion": "2013.03", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-rhel57-x.x.x.tgz": {"supportedVersionSegments": [{"min": "2.6.0", "max": "3.2.22"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "rhel", "maxOsVersion": "6.0", "minOsVersion": "5.7", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-rhel62-x.x.x.tgz": {"supportedVersionSegments": [{"min": "2.6.0", "max": "4.4.29"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "rhel", "maxOsVersion": "7.0", "minOsVersion": "6.2", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-suse11-x.x.x.tgz": {"supportedVersionSegments": [{"min": "2.6.0", "max": "3.2.19"}, {"min": "3.4.0", "max": "3.4.14"}, {"min": "3.6.0", "max": "3.6.3"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "suse", "maxOsVersion": "12", "minOsVersion": "11", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-ubuntu1204-x.x.x.tgz": {"supportedVersionSegments": [{"min": "2.6.0", "max": "3.2.19"}, {"min": "3.4.0", "max": "3.4.14"}, {"min": "3.6.0", "max": "3.6.3"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "ubuntu", "maxOsVersion": "13.04", "minOsVersion": "12.04", "platform": "linux"}}, "/win32/mongodb-win32-x86_64-enterprise-windows-64-x.x.x.zip": {"supportedVersionSegments": [{"min": "2.6.0", "max": "4.2.25"}], "versionGenericParameters": {"architecture": "amd64", "platform": "windows", "win2008plus": true, "winVCRedistOptions": ["/quiet", "/norestart"]}}, "/linux/mongodb-linux-x86_64-enterprise-rhel70-x.x.x.tgz": {"supportedVersionSegments": [{"min": "2.6.1", "max": "7.3.999"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "rhel", "maxOsVersion": "8.0", "minOsVersion": "7.0", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-ubuntu1404-x.x.x.tgz": {"supportedVersionSegments": [{"min": "2.6.1", "max": "3.4.20"}, {"min": "3.4.23", "max": "3.6.12"}, {"min": "3.6.14", "max": "4.0.9"}, {"min": "4.0.12", "max": "4.0.28"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "ubuntu", "maxOsVersion": "15.04", "minOsVersion": "14.04", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-rhel55-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.0.0", "max": "3.2.22"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "rhel", "maxOsVersion": "6.0", "minOsVersion": "5.5", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-rhel62-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.0.0", "max": "4.4.29"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "rhel", "maxOsVersion": "7.0", "minOsVersion": "6.2", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-rhel70-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.0.0", "max": "7.3.999"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "rhel", "maxOsVersion": "8.0", "minOsVersion": "7.0", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-suse11-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.0.0", "max": "3.2.19"}, {"min": "3.4.0", "max": "3.4.14"}, {"min": "3.6.0", "max": "3.6.3"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "suse", "maxOsVersion": "12", "minOsVersion": "11", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-debian71-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.0.0", "max": "3.2.20"}, {"min": "3.4.0", "max": "3.4.15"}, {"min": "3.6.0", "max": "3.6.4"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "debian", "maxOsVersion": "8.0", "minOsVersion": "7.1", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-ubuntu1204-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.0.0", "max": "3.2.19"}, {"min": "3.4.0", "max": "3.4.14"}, {"min": "3.6.0", "max": "3.6.3"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "ubuntu", "maxOsVersion": "13.04", "minOsVersion": "12.04", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-ubuntu1404-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.0.0", "max": "3.4.20"}, {"min": "3.4.23", "max": "3.6.12"}, {"min": "3.6.14", "max": "4.0.9"}, {"min": "4.0.12", "max": "4.0.28"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "ubuntu", "maxOsVersion": "15.04", "minOsVersion": "14.04", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-ubuntu2404-x.x.x.tgz": {"supportedVersionSegments": [{"min": "8.0.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "ubuntu", "maxOsVersion": "25.04", "minOsVersion": "24.04", "platform": "linux"}}, "/linux/mongodb-linux-aarch64-ubuntu2404-x.x.x.tgz": {"supportedVersionSegments": [{"min": "8.0.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "25.04", "minOsVersion": "24.04"}}, "/linux/mongodb-linux-x86_64-enterprise-ubuntu2404-x.x.x.tgz": {"supportedVersionSegments": [{"min": "8.0.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "ubuntu", "maxOsVersion": "25.04", "minOsVersion": "24.04", "platform": "linux"}}, "/linux/mongodb-linux-aarch64-enterprise-ubuntu2404-x.x.x.tgz": {"supportedVersionSegments": [{"min": "8.0.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "25.04", "minOsVersion": "24.04"}}, "/linux/mongodb-linux-x86_64-amazon-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.0.0", "max": "5.0.31"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "amazon", "maxOsVersion": "", "minOsVersion": "2013.03", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-suse12-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.2.0", "max": "7.3.999"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "suse", "maxOsVersion": "13", "minOsVersion": "12", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-suse12-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.2.0", "max": "7.3.999"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "suse", "maxOsVersion": "13", "minOsVersion": "12", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-debian71-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.2.0", "max": "3.2.20"}, {"min": "3.4.0", "max": "3.4.15"}, {"min": "3.6.0", "max": "3.6.4"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "debian", "maxOsVersion": "8.0", "minOsVersion": "7.1", "platform": "linux"}}, "/osx/mongodb-osx-x86_64-enterprise-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.2.0", "max": "4.0.28"}], "versionGenericParameters": {"architecture": "amd64", "platform": "osx"}}, "/linux/mongodb-linux-x86_64-ubuntu1604-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.2.7", "max": "4.4.29"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "ubuntu", "maxOsVersion": "17.04", "minOsVersion": "16.04", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-ubuntu1604-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.2.7", "max": "4.4.29"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "ubuntu", "maxOsVersion": "17.04", "minOsVersion": "16.04", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-debian81-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.2.8", "max": "4.0.28"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "debian", "maxOsVersion": "9.0", "minOsVersion": "8.1", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-debian81-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.2.8", "max": "4.0.28"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "debian", "maxOsVersion": "9.0", "minOsVersion": "8.1", "platform": "linux"}}, "/win32/mongodb-win32-x86_64-2008plus-ssl-x.x.x.zip": {"supportedVersionSegments": [{"min": "3.2.12", "max": "3.2.22"}, {"min": "3.4.1", "max": "4.0.28"}], "versionGenericParameters": {"architecture": "amd64", "platform": "windows", "win2008plus": true, "winVCRedistOptions": ["/quiet", "/norestart"]}}, "/linux/mongodb-linux-ppc64le-enterprise-rhel71-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.4.0", "max": "4.0.18"}, {"min": "4.0.20", "max": "4.0.27"}, {"min": "4.2.0", "max": "4.4.29"}, {"min": "5.0.19", "max": "5.0.31"}, {"min": "6.0.7", "max": "6.0.20"}], "versionGenericParameters": {"architecture": "ppc64le", "flavor": "rhel", "maxOsVersion": "8.0", "minOsVersion": "7.0", "platform": "linux"}}, "/linux/mongodb-linux-ppc64le-enterprise-ubuntu1604-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.4.0", "max": "3.4.20"}, {"min": "3.6.0", "max": "3.6.12"}, {"min": "4.0.0", "max": "4.0.18"}, {"min": "4.0.20", "max": "4.0.24"}], "versionGenericParameters": {"architecture": "ppc64le", "flavor": "ubuntu", "maxOsVersion": "17.04", "minOsVersion": "16.04", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-rhel80-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.4.24", "max": "3.4.24"}, {"min": "3.6.17", "max": "3.6.23"}, {"min": "4.0.14", "max": "4.0.28"}, {"min": "4.2.1", "max": "5.0.28"}, {"min": "5.1.0-rc0", "max": "6.0.16"}, {"min": "6.1.0-rc0", "max": "7.0.13"}, {"min": "7.1.0-rc0", "max": "7.3.3"}, {"min": "8.0.0-rc0", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/linux/mongodb-linux-x86_64-enterprise-rhel80-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.4.24", "max": "3.4.24"}, {"min": "3.6.17", "max": "3.6.23"}, {"min": "4.0.14", "max": "4.0.28"}, {"min": "4.2.1", "max": "5.0.28"}, {"min": "5.1.0-rc0", "max": "6.0.16"}, {"min": "6.1.0-rc0", "max": "7.0.13"}, {"min": "7.1.0-rc0", "max": "7.3.3"}, {"min": "8.0.0-rc0", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/linux/mongodb-linux-x86_64-rhel8-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.29", "max": "5.0.31"}, {"min": "6.0.17", "max": "6.0.26"}, {"min": "7.0.14", "max": "7.0.24"}, {"min": "7.3.4", "max": "7.3.4"}, {"min": "8.0.0-rc15", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/linux/mongodb-linux-aarch64-rhel8-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.29", "max": "5.0.31"}, {"min": "6.0.17", "max": "6.0.26"}, {"min": "7.0.14", "max": "7.0.24"}, {"min": "7.3.4", "max": "7.3.4"}, {"min": "8.0.0-rc15", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/linux/mongodb-linux-x86_64-enterprise-rhel8-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.29", "max": "5.0.31"}, {"min": "6.0.17", "max": "6.0.26"}, {"min": "7.0.14", "max": "7.0.24"}, {"min": "7.3.4", "max": "7.3.4"}, {"min": "8.0.0-rc15", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/linux/mongodb-linux-aarch64-enterprise-rhel8-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.29", "max": "5.0.31"}, {"min": "6.0.17", "max": "6.0.26"}, {"min": "7.0.14", "max": "7.0.24"}, {"min": "7.3.4", "max": "7.3.4"}, {"min": "8.0.0-rc15", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/osx/mongodb-osx-ssl-x86_64-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.6.0", "max": "4.0.28"}], "versionGenericParameters": {"architecture": "amd64", "platform": "osx"}}, "/linux/mongodb-linux-x86_64-debian92-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.6.5", "max": "5.3.2"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "debian", "maxOsVersion": "10.0", "minOsVersion": "9.1", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-enterprise-debian92-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.6.5", "max": "5.3.2"}], "versionGenericParameters": {"architecture": "amd64", "flavor": "debian", "maxOsVersion": "10.0", "minOsVersion": "9.1", "platform": "linux"}}, "/linux/mongodb-linux-x86_64-ubuntu1804-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.6.20", "max": "3.6.23"}, {"min": "4.0.1", "max": "6.3.2"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "19.04", "minOsVersion": "18.04"}}, "/linux/mongodb-linux-x86_64-enterprise-ubuntu1804-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.6.20", "max": "3.6.23"}, {"min": "4.0.1", "max": "6.3.2"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "19.04", "minOsVersion": "18.04"}}, "/linux/mongodb-linux-x86_64-amazon2-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.6.22", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "amazon2", "maxOsVersion": "", "minOsVersion": "2"}}, "/linux/mongodb-linux-x86_64-enterprise-amazon2-x.x.x.tgz": {"supportedVersionSegments": [{"min": "3.6.22", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "amazon2", "maxOsVersion": "", "minOsVersion": "2"}}, "/linux/mongodb-linux-x86_64-enterprise-amazon2-openssl111-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.27", "max": "5.0.27"}, {"min": "8.0.0-rc4", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "amazon2-openssl111", "maxOsVersion": "", "minOsVersion": "2"}}, "/linux/mongodb-linux-s390x-enterprise-rhel67-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.0.0", "max": "4.0.12"}, {"min": "4.0.14", "max": "4.0.24"}, {"min": "4.2.0", "max": "4.2.0"}, {"min": "4.2.2", "max": "4.2.14"}], "versionGenericParameters": {"architecture": "s390x", "flavor": "rhel", "maxOsVersion": "7.0", "minOsVersion": "6.2", "platform": "linux"}}, "/linux/mongodb-linux-s390x-enterprise-rhel72-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.0.6", "max": "4.0.27"}, {"min": "4.2.0", "max": "4.2.17"}, {"min": "4.2.21", "max": "5.2.0-rc1"}, {"min": "6.0.0", "max": "6.0.26"}], "versionGenericParameters": {"architecture": "s390x", "platform": "linux", "flavor": "rhel", "maxOsVersion": "8.0", "minOsVersion": "7.0"}}, "/linux/mongodb-linux-s390x-enterprise-ubuntu1804-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.0.6", "max": "4.0.25"}, {"min": "4.2.1", "max": "4.2.14"}, {"min": "4.4.0", "max": "4.4.6"}], "versionGenericParameters": {"architecture": "s390x", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "19.04", "minOsVersion": "18.04"}}, "/linux/mongodb-linux-s390x-enterprise-suse12-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.0.6", "max": "4.0.12"}, {"min": "4.0.14", "max": "4.0.24"}, {"min": "4.2.0", "max": "4.2.14"}, {"min": "4.4.0", "max": "4.4.4"}, {"min": "4.4.6", "max": "4.4.6"}], "versionGenericParameters": {"architecture": "s390x", "platform": "linux", "flavor": "suse", "maxOsVersion": "13", "minOsVersion": "12"}}, "/win32/mongodb-win32-x86_64-2012plus-x.x.x.zip": {"supportedVersionSegments": [{"min": "4.2.0", "max": "4.2.25"}], "versionGenericParameters": {"architecture": "amd64", "platform": "windows", "win2008plus": true, "winVCRedistOptions": ["/quiet", "/norestart"]}}, "/osx/mongodb-macos-x86_64-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "osx"}}, "/osx/mongodb-macos-x86_64-enterprise-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "osx"}}, "/linux/mongodb-linux-x86_64-suse15-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.1", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "suse", "maxOsVersion": "16", "minOsVersion": "15"}}, "/linux/mongodb-linux-x86_64-debian10-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.1", "max": "6.3.2"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "debian", "maxOsVersion": "11.0", "minOsVersion": "10.0"}}, "/linux/mongodb-linux-x86_64-enterprise-suse15-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.1", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "suse", "maxOsVersion": "16", "minOsVersion": "15"}}, "/linux/mongodb-linux-x86_64-enterprise-debian10-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.1", "max": "6.3.2"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "debian", "maxOsVersion": "11.0", "minOsVersion": "10.0"}}, "/linux/mongodb-linux-ppc64le-enterprise-ubuntu1804-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.1", "max": "4.2.14"}, {"min": "4.4.0", "max": "4.4.5"}], "versionGenericParameters": {"architecture": "ppc64le", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "19.04", "minOsVersion": "18.04"}}, "/linux/mongodb-linux-ppc64le-enterprise-rhel81-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.7", "max": "4.2.8"}, {"min": "4.2.14", "max": "5.3.0-rc1"}, {"min": "6.0.0", "max": "6.0.20"}, {"min": "7.0.0", "max": "7.0.24"}, {"min": "8.0.0-rc13", "max": "8.0.0-rc20"}, {"min": "8.0.0-rc4", "max": "8.0.13"}, {"min": "8.2.0-alpha1", "max": "8.2.0-alpha1"}, {"min": "8.2.0-rc0", "max": "8.2.0-rc0"}, {"min": "8.2.0-rc2", "max": "8.2.0"}], "versionGenericParameters": {"architecture": "ppc64le", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/linux/mongodb-linux-ppc64le-enterprise-rhel9-x.x.x.tgz": {"supportedVersionSegments": [{"min": "8.0.12", "max": "8.0.13"}, {"min": "8.2.0-alpha1", "max": "8.2.0-alpha1"}, {"min": "8.2.0-rc0", "max": "8.2.0-rc0"}, {"min": "8.2.0-rc2", "max": "8.2.0"}], "versionGenericParameters": {"architecture": "ppc64le", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-aarch64-amazon2-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.18", "max": "4.2.25"}, {"min": "4.4.4", "max": "4.4.4"}, {"min": "4.4.11", "max": "4.4.29"}, {"min": "5.0.5", "max": "5.0.31"}, {"min": "5.2.0-rc6", "max": "5.2.0-rc6"}, {"min": "5.2.1-rc0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "amazon2", "maxOsVersion": "", "minOsVersion": "2"}}, "/linux/mongodb-linux-aarch64-enterprise-amazon2-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.18", "max": "4.2.25"}, {"min": "4.4.4", "max": "4.4.4"}, {"min": "4.4.11", "max": "4.4.29"}, {"min": "5.0.5", "max": "5.0.31"}, {"min": "5.1.0", "max": "5.1.0"}, {"min": "5.2.0-rc6", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "amazon2", "maxOsVersion": "", "minOsVersion": "2"}}, "/linux/mongodb-linux-aarch64-enterprise-amazon2-openssl111-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.27", "max": "5.0.27"}, {"min": "8.0.0-rc4", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "amazon2-openssl111", "maxOsVersion": "", "minOsVersion": "2"}}, "/linux/mongodb-linux-aarch64-ubuntu1804-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.25", "max": "4.2.25"}, {"min": "4.4.22", "max": "4.4.29"}, {"min": "5.0.18", "max": "5.0.31"}, {"min": "6.0.6", "max": "6.0.26"}, {"min": "6.3.2-rc0", "max": "6.3.2"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "19.04", "minOsVersion": "18.04"}}, "/linux/mongodb-linux-aarch64-enterprise-ubuntu1604-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.25", "max": "4.2.25"}, {"min": "4.4.22", "max": "4.4.29"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "17.04", "minOsVersion": "16.04"}}, "/linux/mongodb-linux-aarch64-enterprise-ubuntu1804-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.2.25", "max": "4.2.25"}, {"min": "4.4.22", "max": "4.4.29"}, {"min": "5.0.18", "max": "5.0.31"}, {"min": "6.0.6", "max": "6.0.26"}, {"min": "6.3.2-rc0", "max": "6.3.2"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "19.04", "minOsVersion": "18.04"}}, "/linux/mongodb-linux-x86_64-ubuntu2004-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.4.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "21.04", "minOsVersion": "20.04"}}, "/windows/mongodb-windows-x86_64-x.x.x.zip": {"supportedVersionSegments": [{"min": "4.4.0", "max": "8.1.9999"}, {"min": "8.2.0-alpha2", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "windows"}}, "/linux/mongodb-linux-x86_64-enterprise-ubuntu2004-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.4.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "21.04", "minOsVersion": "20.04"}}, "/windows/mongodb-windows-x86_64-enterprise-x.x.x.zip": {"supportedVersionSegments": [{"min": "4.4.0", "max": "8.1.9999"}, {"min": "8.2.0-alpha2", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "windows"}}, "/linux/mongodb-linux-aarch64-enterprise-rhel82-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.4.4", "max": "5.0.6"}, {"min": "5.0.9", "max": "5.0.28"}, {"min": "5.1.0-rc0", "max": "5.3.1"}, {"min": "5.3.2-rc2", "max": "6.0.16"}, {"min": "6.1.0-rc0", "max": "7.0.13"}, {"min": "7.1.0-rc0", "max": "7.3.3"}, {"min": "8.0.0-rc0", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/linux/mongodb-linux-aarch64-rhel82-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.4.5", "max": "5.0.6"}, {"min": "5.0.9", "max": "5.0.28"}, {"min": "5.1.0-rc0", "max": "5.3.1"}, {"min": "5.3.2-rc2", "max": "6.0.16"}, {"min": "6.1.0-rc0", "max": "7.0.13"}, {"min": "7.1.0-rc0", "max": "7.3.3"}, {"min": "8.0.0-rc0", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/linux/mongodb-linux-aarch64-ubuntu2004-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.4.0", "max": "4.4.29"}, {"min": "5.0.0", "max": "5.3.2"}, {"min": "6.0.0", "max": "6.0.26"}, {"min": "6.1.0-rc0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "21.04", "minOsVersion": "20.04"}}, "/linux/mongodb-linux-aarch64-enterprise-ubuntu2004-x.x.x.tgz": {"supportedVersionSegments": [{"min": "4.4.0", "max": "4.4.29"}, {"min": "5.0.0", "max": "5.3.2"}, {"min": "6.0.0", "max": "6.0.26"}, {"min": "6.1.0-rc0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "21.04", "minOsVersion": "20.04"}}, "/linux/mongodb-linux-x86_64-debian11-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.8", "max": "5.0.31"}, {"min": "6.0.0", "max": "7.3.999"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "debian", "maxOsVersion": "12.0", "minOsVersion": "11.0"}}, "/linux/mongodb-linux-x86_64-enterprise-debian11-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.8", "max": "5.0.31"}, {"min": "6.0.0", "max": "7.3.999"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "debian", "maxOsVersion": "12.0", "minOsVersion": "11.0"}}, "/linux/mongodb-linux-s390x-enterprise-rhel83-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.9", "max": "5.0.31"}, {"min": "6.0.0", "max": "6.0.26"}, {"min": "7.0.0", "max": "7.0.24"}, {"min": "8.0.0-rc13", "max": "8.0.0-rc20"}, {"min": "8.0.0-rc4", "max": "8.0.13"}, {"min": "8.2.0-alpha1", "max": "8.2.0-alpha1"}, {"min": "8.2.0-rc0", "max": "8.2.0-rc0"}, {"min": "8.2.0-rc2", "max": "8.2.0"}], "versionGenericParameters": {"architecture": "s390x", "platform": "linux", "flavor": "rhel", "maxOsVersion": "9.0", "minOsVersion": "8.0"}}, "/linux/mongodb-linux-s390x-enterprise-rhel9-x.x.x.tgz": {"supportedVersionSegments": [{"min": "7.0.20", "max": "7.0.24"}, {"min": "8.0.7", "max": "8.0.13"}, {"min": "8.2.0-alpha1", "max": "8.2.0-alpha1"}, {"min": "8.2.0-rc0", "max": "8.2.0-rc0"}, {"min": "8.2.0-rc2", "max": "8.2.0"}], "versionGenericParameters": {"architecture": "s390x", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-x86_64-ubuntu2204-x.x.x.tgz": {"supportedVersionSegments": [{"min": "6.0.4", "max": "6.0.26"}, {"min": "6.3.2-rc0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "23.04", "minOsVersion": "22.04"}}, "/linux/mongodb-linux-aarch64-ubuntu2204-x.x.x.tgz": {"supportedVersionSegments": [{"min": "6.0.4", "max": "6.0.26"}, {"min": "6.3.2-rc0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "23.04", "minOsVersion": "22.04"}}, "/linux/mongodb-linux-x86_64-rhel90-x.x.x.tgz": {"supportedVersionSegments": [{"min": "6.0.4", "max": "6.1.0"}, {"min": "6.2.0-rc1", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-x86_64-rhel93-x.x.x.tgz": {"supportedVersionSegments": [{"min": "8.0.0-rc15", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-aarch64-rhel93-x.x.x.tgz": {"supportedVersionSegments": [{"min": "8.0.0-rc15", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-x86_64-enterprise-rhel93-x.x.x.tgz": {"supportedVersionSegments": [{"min": "8.0.0-rc15", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-aarch64-enterprise-rhel93-x.x.x.tgz": {"supportedVersionSegments": [{"min": "8.0.0-rc15", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-x86_64-enterprise-ubuntu2204-x.x.x.tgz": {"supportedVersionSegments": [{"min": "6.0.4", "max": "6.0.26"}, {"min": "6.3.2-rc0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "23.04", "minOsVersion": "22.04"}}, "/linux/mongodb-linux-aarch64-enterprise-ubuntu2204-x.x.x.tgz": {"supportedVersionSegments": [{"min": "6.0.4", "max": "6.0.26"}, {"min": "6.3.2-rc0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "ubuntu", "maxOsVersion": "23.04", "minOsVersion": "22.04"}}, "/linux/mongodb-linux-x86_64-enterprise-rhel90-x.x.x.tgz": {"supportedVersionSegments": [{"min": "6.0.4", "max": "6.1.0"}, {"min": "6.2.0-rc1", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-aarch64-rhel90-x.x.x.tgz": {"supportedVersionSegments": [{"min": "6.0.7", "max": "6.0.26"}, {"min": "6.3.0-rc0", "max": "7.0.2"}, {"min": "7.0.3", "max": "7.1.0-rc3"}, {"min": "7.1.1-rc0", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-aarch64-enterprise-rhel90-x.x.x.tgz": {"supportedVersionSegments": [{"min": "6.0.7", "max": "6.0.26"}, {"min": "6.3.0-rc0", "max": "7.0.2"}, {"min": "7.0.3", "max": "7.1.0-rc3"}, {"min": "7.1.1-rc0", "max": "8.0.0-rc9"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "rhel", "maxOsVersion": "10.0", "minOsVersion": "9.0"}}, "/linux/mongodb-linux-x86_64-amazon2023-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.30", "max": "5.0.31"}, {"min": "6.0.17", "max": "6.0.26"}, {"min": "7.0.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "amazon2023", "maxOsVersion": "", "minOsVersion": "2023"}}, "/linux/mongodb-linux-aarch64-amazon2023-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.30", "max": "5.0.31"}, {"min": "6.0.17", "max": "6.0.26"}, {"min": "7.0.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "amazon2023", "maxOsVersion": "", "minOsVersion": "2023"}}, "/linux/mongodb-linux-x86_64-enterprise-amazon2023-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.30", "max": "5.0.31"}, {"min": "6.0.17", "max": "6.0.26"}, {"min": "7.0.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "amazon2023", "maxOsVersion": "", "minOsVersion": "2023"}}, "/linux/mongodb-linux-aarch64-enterprise-amazon2023-x.x.x.tgz": {"supportedVersionSegments": [{"min": "5.0.30", "max": "5.0.31"}, {"min": "6.0.17", "max": "6.0.26"}, {"min": "7.0.0", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "aarch64", "platform": "linux", "flavor": "amazon2023", "maxOsVersion": "", "minOsVersion": "2023"}}, "/linux/mongodb-linux-x86_64-debian12-x.x.x.tgz": {"supportedVersionSegments": [{"min": "7.0.5", "max": "7.0.24"}, {"min": "7.2.0-rc1", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "debian", "maxOsVersion": "13.0", "minOsVersion": "12.0"}}, "/linux/mongodb-linux-x86_64-enterprise-debian12-x.x.x.tgz": {"supportedVersionSegments": [{"min": "7.0.5", "max": "7.0.24"}, {"min": "7.2.0-rc1", "max": "999.999.0"}], "versionGenericParameters": {"architecture": "amd64", "platform": "linux", "flavor": "debian", "maxOsVersion": "13.0", "minOsVersion": "12.0"}}}}