package com.xgen.cloud.billingplatform.process.invoicelocking._private.svc;

import static com.xgen.cloud.billingplatform.process.invoicelocking._public.context.LockedInvoiceContextInterceptor.LOCKED_INVOICE_CONTEXT_KEY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.process.invoicelocking._private.dao.InvoiceDao;
import com.xgen.cloud.billingplatform.process.invoicelocking._public.svc.InvoiceLockException;
import com.xgen.cloud.billingplatform.process.invoicelocking._public.svc.LockedInvoiceSvc;
import com.xgen.cloud.billingplatform.process.invoicelocking._public.svc.LockedInvoiceSvc.LockParams;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import io.grpc.Context;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class LockedInvoiceSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private LockedInvoiceSvc lockedInvoiceSvc;
  @Inject private InvoiceDao invoiceDao;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    // Ensure clean context before each test
    clearContext();
  }

  @Override
  @AfterEach
  public void tearDown() {
    // Ensure clean context after each test
    clearContext();
  }

  private void clearContext() {
    // Clear any existing context to prevent leakage between tests
    if (LOCKED_INVOICE_CONTEXT_KEY.get() != null) {
      Context.current().withValue(LOCKED_INVOICE_CONTEXT_KEY, null).attach();
    }
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_basicFunctionality() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    // Verify invoice is initially unlocked
    Invoice initialInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(initialInvoice.getLockedBy(), "Invoice should initially be unlocked");

    AtomicBoolean functionExecuted = new AtomicBoolean(false);
    AtomicReference<String> capturedTransactionId = new AtomicReference<>();

    // Test that the method actually locks the invoice during execution
    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(),
            new LockParams("basicFunctionalityTest"),
            lockedInvoice -> {
              functionExecuted.set(true);

              // Verify we have the correct invoice
              assertEquals(invoice.getId(), lockedInvoice.getId());

              // Verify context is available
              String transactionId = LOCKED_INVOICE_CONTEXT_KEY.get().getTransactionId();
              capturedTransactionId.set(transactionId);
              assertNotNull(transactionId, "Transaction ID should be available in context");
              assertThat(transactionId).startsWith("basicFunctionalityTest-");

              // Verify invoice is locked in database during execution
              Invoice lockedInDb = findById(Invoice.class, invoice.getId()).orElseThrow();
              assertEquals(
                  transactionId, lockedInDb.getLockedBy(), "Invoice should be locked in database");
              assertNotNull(lockedInDb.getLockedAt(), "Invoice should have lock timestamp");

              return Mono.just("function-result-" + lockedInvoice.getId());
            });

    // Verify the result
    StepVerifier.create(result).expectNext("function-result-" + invoice.getId()).verifyComplete();

    // Verify function was executed
    assertThat(functionExecuted.get()).isTrue();
    assertNotNull(capturedTransactionId.get(), "Transaction ID should be captured");

    // Verify invoice is unlocked after completion
    Invoice finalInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(finalInvoice.getLockedBy(), "Invoice should be unlocked after completion");
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_success() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    // Test reactive function that returns a result
    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(),
            new LockParams("reactiveIntTest"),
            lockedInvoice -> Mono.just("result-" + lockedInvoice.getId()));

    // Verify the result
    StepVerifier.create(result).expectNext("result-" + invoice.getId()).verifyComplete();

    // Verify invoice is unlocked after completion
    Invoice updatedInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(updatedInvoice.getLockedBy(), "Invoice should be unlocked");
    assertNull(updatedInvoice.getLockedAt(), "Invoice should be unlocked");
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_withError() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    // Test reactive function that returns an error
    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(),
            new LockParams("reactiveErrorIntTest"),
            lockedInvoice -> Mono.error(new RuntimeException("Test error")));

    // Verify the error is propagated
    StepVerifier.create(result).expectError(RuntimeException.class).verify();

    // Verify invoice is unlocked even after error
    Invoice updatedInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(updatedInvoice.getLockedBy(), "Invoice should be unlocked after error");
    assertNull(updatedInvoice.getLockedAt(), "Invoice should be unlocked after error");
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_invoiceNotFound() {
    ObjectId nonExistentInvoiceId = ObjectId.get();

    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            nonExistentInvoiceId,
            new LockParams("notFoundTest"),
            lockedInvoice -> Mono.just("should-not-execute"));

    // Verify InvoiceLockException is thrown
    StepVerifier.create(result).expectError(InvoiceLockException.class).verify();
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_withHooks() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    AtomicBoolean beforeLockCalled = new AtomicBoolean(false);
    AtomicBoolean afterLockCalled = new AtomicBoolean(false);
    AtomicBoolean beforeUnlockCalled = new AtomicBoolean(false);
    AtomicBoolean afterUnlockCalled = new AtomicBoolean(false);

    LockParams params =
        new LockParams(
            "hooksTest",
            () -> beforeLockCalled.set(true),
            () -> afterLockCalled.set(true),
            () -> beforeUnlockCalled.set(true),
            () -> afterUnlockCalled.set(true));

    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(), params, lockedInvoice -> Mono.just("hooks-result"));

    // Verify the result and hooks
    StepVerifier.create(result).expectNext("hooks-result").verifyComplete();

    // Verify all hooks were called
    assertThat(beforeLockCalled.get()).isTrue();
    assertThat(afterLockCalled.get()).isTrue();
    assertThat(beforeUnlockCalled.get()).isTrue();
    assertThat(afterUnlockCalled.get()).isTrue();
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_contextManagement() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    AtomicReference<String> capturedTransactionId = new AtomicReference<>();

    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(),
            new LockParams("contextTest"),
            lockedInvoice -> {
              // Capture the transaction ID from context
              String transactionId = LOCKED_INVOICE_CONTEXT_KEY.get().getTransactionId();
              capturedTransactionId.set(transactionId);
              return Mono.just("context-result");
            });

    // Verify the result
    StepVerifier.create(result).expectNext("context-result").verifyComplete();

    // Verify transaction ID was captured and follows expected format
    String transactionId = capturedTransactionId.get();
    assertNotNull(transactionId, "Transaction ID should be captured");
    assertThat(transactionId).startsWith("contextTest-");

    // Verify context is cleaned up after completion
    assertNull(LOCKED_INVOICE_CONTEXT_KEY.get(), "Context should be cleaned up");
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_complexReactiveChain() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(),
            new LockParams("complexChainTest"),
            lockedInvoice ->
                Mono.just(lockedInvoice.getId().toString())
                    .map(id -> "processed-" + id)
                    .flatMap(processed -> Mono.just(processed + "-final"))
                    .delayElement(Duration.ofMillis(10))); // Small delay to test async behavior

    // Verify the result
    StepVerifier.create(result)
        .expectNext("processed-" + invoice.getId() + "-final")
        .verifyComplete();

    // Verify invoice is unlocked
    Invoice updatedInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(updatedInvoice.getLockedBy(), "Invoice should be unlocked");
  }

  /**
   * Verifies concurrent locking behavior for the same invoice.
   *
   * <ul>
   *   <li>Thread 1 acquires the lock and completes successfully.
   *   <li>Thread 2, started after thread 1 has the lock, fails with {@link InvoiceLockException}.
   *   <li>Both threads finish and the invoice is unlocked at the end.
   * </ul>
   *
   * A {@link CountDownLatch} is used to deterministically start Thread 2 only after Thread 1 has
   * acquired the lock to eliminate flakiness.
   */
  @RepeatedTest(value = 10, name = RepeatedTest.LONG_DISPLAY_NAME)
  public void lockInvoiceAndExecuteFunctionReactive_concurrentAccess() throws InterruptedException {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch thread1HasLockLatch = new CountDownLatch(1);
    CountDownLatch completeLatch = new CountDownLatch(2);
    AtomicReference<Exception> thread1Exception = new AtomicReference<>();
    AtomicReference<Exception> thread2Exception = new AtomicReference<>();
    AtomicReference<String> thread1Result = new AtomicReference<>();
    AtomicReference<String> thread2Result = new AtomicReference<>();

    // Thread 1: Should succeed in locking
    Thread thread1 =
        new Thread(
            () -> {
              try {
                startLatch.await();
                Mono<String> result =
                    lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
                        invoice.getId(),
                        new LockParams("concurrentTest1"),
                        lockedInvoice -> {
                          // Signal that thread 1 has acquired the lock and started executing
                          thread1HasLockLatch.countDown();
                          // Hold lock for a bit
                          return Mono.just("thread1-result").delayElement(Duration.ofMillis(10));
                        });

                thread1Result.set(result.block());
              } catch (Exception e) {
                thread1Exception.set(e);
              } finally {
                completeLatch.countDown();
              }
            });

    // Thread 2: Should fail due to lock contention
    Thread thread2 =
        new Thread(
            () -> {
              try {
                startLatch.await();
                // Wait until thread 1 has actually acquired the lock and started executing
                thread1HasLockLatch.await(2, TimeUnit.SECONDS);
                Mono<String> result =
                    lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
                        invoice.getId(),
                        new LockParams("concurrentTest2"),
                        lockedInvoice -> Mono.just("thread2-result"));

                thread2Result.set(result.block());
              } catch (Exception e) {
                thread2Exception.set(e);
              } finally {
                completeLatch.countDown();
              }
            });

    thread1.start();
    thread2.start();
    startLatch.countDown(); // Start both threads

    // Wait for completion
    assertThat(completeLatch.await(5, TimeUnit.SECONDS)).isTrue();

    // Verify results
    assertNull(thread1Exception.get(), "Thread 1 should succeed");
    assertEquals("thread1-result", thread1Result.get());

    assertNotNull(thread2Exception.get(), "Thread 2 should fail with lock exception");
    assertThat(thread2Exception.get()).isInstanceOf(InvoiceLockException.class);
    assertNull(thread2Result.get(), "Thread 2 should not get result");

    // Verify invoice is unlocked after both threads complete
    Invoice updatedInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(updatedInvoice.getLockedBy(), "Invoice should be unlocked");
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_errorInHooks() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    LockParams params =
        new LockParams(
            "errorHooksTest",
            () -> {
              throw new RuntimeException("beforeLock error");
            },
            null,
            null,
            null);

    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(), params, lockedInvoice -> Mono.just("should-not-execute"));

    // Based on the logs, it seems the hook error is caught and logged, not propagated
    // The function still executes, so let's test that the function executes despite hook error
    StepVerifier.create(result).expectNext("should-not-execute").verifyComplete();

    // Verify invoice is unlocked after completion
    Invoice updatedInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(updatedInvoice.getLockedBy(), "Invoice should be unlocked");
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_alreadyLockedBySameTransaction() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    // First, lock the invoice to get the actual transaction ID that would be generated
    AtomicReference<String> actualTransactionId = new AtomicReference<>();

    Mono<String> firstLock =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(),
            new LockParams("sameTransactionTest"),
            lockedInvoice -> {
              // Capture the actual transaction ID
              String transactionId = LOCKED_INVOICE_CONTEXT_KEY.get().getTransactionId();
              actualTransactionId.set(transactionId);

              // Now within the same context, try to lock again - should use NoOpLockedInvoice
              Mono<String> secondLock =
                  lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
                      invoice.getId(),
                      new LockParams("sameTransactionTest"),
                      innerLockedInvoice -> {
                        assertEquals(invoice.getId(), innerLockedInvoice.getId());
                        return Mono.just("same-transaction-result");
                      });

              return secondLock;
            });

    // Verify the result
    StepVerifier.create(firstLock).expectNext("same-transaction-result").verifyComplete();

    // Verify transaction ID was captured
    assertNotNull(actualTransactionId.get(), "Transaction ID should be captured");
    assertThat(actualTransactionId.get()).startsWith("sameTransactionTest-");
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_alreadyLockedByAnotherTransaction() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    // First, lock the invoice with a different transaction
    String otherTransactionId = "otherTransaction-12345";
    invoiceDao.tryLockInvoice(invoice.getId(), otherTransactionId);

    // Now try to lock with a different transaction - should fail
    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(),
            new LockParams("differentTransactionTest"),
            lockedInvoice -> Mono.just("should-not-execute"));

    // Verify InvoiceLockException is thrown
    StepVerifier.create(result).expectError(InvoiceLockException.class).verify();

    // Verify invoice is still locked by the other transaction
    Invoice updatedInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertEquals(otherTransactionId, updatedInvoice.getLockedBy());

    // Clean up - unlock manually
    invoiceDao.unlockInvoice(invoice.getId(), otherTransactionId);
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_withDelayedExecution() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(),
            new LockParams("delayedTest"),
            lockedInvoice ->
                Mono.just("delayed-result")
                    .delayElement(Duration.ofMillis(100)) // Shorter delay to avoid timing issues
                    .map(s -> s + "-" + lockedInvoice.getId()));

    // Verify the result with timeout
    StepVerifier.create(result).expectNext("delayed-result-" + invoice.getId()).verifyComplete();

    // Verify invoice is unlocked after delayed execution
    Invoice updatedInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(updatedInvoice.getLockedBy(), "Invoice should be unlocked after delayed execution");
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_multipleSequentialCalls() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    // First call
    Mono<String> result1 =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(), new LockParams("sequential1"), lockedInvoice -> Mono.just("result1"));

    StepVerifier.create(result1).expectNext("result1").verifyComplete();

    // Second call - should succeed since first call released the lock
    Mono<String> result2 =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(), new LockParams("sequential2"), lockedInvoice -> Mono.just("result2"));

    StepVerifier.create(result2).expectNext("result2").verifyComplete();

    // Verify invoice is unlocked
    Invoice updatedInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(updatedInvoice.getLockedBy(), "Invoice should be unlocked");
  }

  @Test
  public void lockInvoiceAndExecuteFunctionReactive_errorInAfterUnlockHook() {
    // Create and save an invoice
    Invoice invoice = save(new Invoice.Builder().build());

    LockParams params =
        new LockParams(
            "afterUnlockErrorTest",
            null,
            null,
            null,
            () -> {
              throw new RuntimeException("afterUnlock error");
            });

    Mono<String> result =
        lockedInvoiceSvc.lockInvoiceAndExecuteFunctionReactive(
            invoice.getId(), params, lockedInvoice -> Mono.just("success-result"));

    // The function should succeed, but afterUnlock error should be swallowed
    // (based on the doFinally behavior in reactive streams)
    StepVerifier.create(result).expectNext("success-result").verifyComplete();

    // Verify invoice is unlocked despite afterUnlock hook error
    Invoice updatedInvoice = findById(Invoice.class, invoice.getId()).orElseThrow();
    assertNull(updatedInvoice.getLockedBy(), "Invoice should be unlocked despite hook error");
  }
}
