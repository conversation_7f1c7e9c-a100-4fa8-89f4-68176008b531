package com.xgen.cloud.billingplatform.support._private.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.billingplatform.model.plan._public.model.PlanSubscriptionTier;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanSubscriptionTiers;
import com.xgen.cloud.billingplatform.model.plan._public.model.SubscriptionPricingModel;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.support._public.model.AtlasSubscriptionBillerContext;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.OrgPlan;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class CrossOrgTieringTrueUpSvcImplIntTests extends JUnit5BaseSvcTest {

  private static final PlanSubscriptionTiers ENTERPRISE_TIER_PRICING =
      new PlanSubscriptionTiers(
          List.of(
              PlanSubscriptionTier.create(2500000, 70),
              PlanSubscriptionTier.create(5000000, 50),
              PlanSubscriptionTier.create(10000000, 40),
              PlanSubscriptionTier.create(50000000, 30),
              PlanSubscriptionTier.create(Long.MAX_VALUE, 20)));
  @Inject private CrossOrgTieringTrueUpSvcImpl crossOrgTieringTrueUpSvc;

  @Test
  public void trueUpCalculatedSuccessfully() {
    ObjectId orgId = ObjectId.get();
    ObjectId invoiceId = ObjectId.get();
    ObjectId groupId = ObjectId.get();
    Date startDate = Date.from(Instant.parse("2023-02-01T00:00:00.000Z"));
    Date endDate = DateUtils.addDays(startDate, 1);
    LineItem lineItem =
        new LineItem.Builder()
            .orgId(orgId)
            .invoiceId(invoiceId)
            .groupId(groupId)
            .sku(SKU.NDS_AWS_DATA_TRANSFER_INTERNET)
            .startDate(startDate)
            .endDate(endDate)
            .created(endDate)
            .totalPriceCents(2500000)
            .unitPriceDollars(1)
            .quantity(2500000)
            .build();
    save(lineItem);

    OrgPlan orgPlan = generateOrgPlan(orgId);
    save(orgPlan);

    AtlasSubscriptionBillerContext context =
        new AtlasSubscriptionBillerContext.Builder()
            .billDate(DateTimeUtils.localDateOf(endDate))
            .invoiceId(invoiceId)
            .isCrossOrg(true)
            .subscriptionPricingModel(orgPlan.getSubscriptionPricingModel())
            .billPeriodStartDate(DateTimeUtils.localDateOf(startDate))
            .orgId(orgId)
            .payingOrgId(orgPlan.getOrgId())
            // total support charge should be 1750000 but instead we force it to be 1749995 so we
            // can create a true up
            .supportChargesForBillPeriod(1749995L)
            .crossOrgEntityInvoices(List.of(invoiceId))
            .build();

    long result = crossOrgTieringTrueUpSvc.calculate(context);
    assertEquals(5, result);
  }

  @Test
  public void noTieredTrueUp_onlyMinimumMet() {
    ObjectId orgId = ObjectId.get();
    ObjectId invoiceId = ObjectId.get();
    ObjectId groupId = ObjectId.get();
    Date startDate = Date.from(Instant.parse("2023-02-01T00:00:00.000Z"));
    Date endDate = DateUtils.addDays(startDate, 1);
    LineItem lineItem =
        new LineItem.Builder()
            .orgId(orgId)
            .invoiceId(invoiceId)
            .groupId(groupId)
            .sku(SKU.NDS_AWS_DATA_TRANSFER_INTERNET)
            .startDate(startDate)
            .endDate(endDate)
            .created(endDate)
            .totalPriceCents(1)
            .unitPriceDollars(1)
            .quantity(1)
            .build();
    save(lineItem);

    OrgPlan orgPlan = generateOrgPlan(orgId);
    save(orgPlan);

    AtlasSubscriptionBillerContext context =
        new AtlasSubscriptionBillerContext.Builder()
            .billDate(DateTimeUtils.localDateOf(endDate))
            .invoiceId(invoiceId)
            .isCrossOrg(true)
            .subscriptionPricingModel(orgPlan.getSubscriptionPricingModel())
            .billPeriodStartDate(DateTimeUtils.localDateOf(startDate))
            .orgId(orgId)
            .payingOrgId(orgPlan.getOrgId())
            .supportChargesForBillPeriod(1)
            .crossOrgEntityInvoices(List.of(invoiceId))
            .build();

    long result = crossOrgTieringTrueUpSvc.calculate(context);
    assertEquals(0, result, "Since minimum is not met, a true up should not be calculated");
  }

  private OrgPlan generateOrgPlan(ObjectId orgId) {
    return new OrgPlan.Builder()
        .orgId(orgId)
        .subscriptionPricingModel(SubscriptionPricingModel.tiered(1500000, ENTERPRISE_TIER_PRICING))
        .startDate(TimeUtils.fromISOString("2022-12-01"))
        .endDate(TimeUtils.fromISOString("2023-12-01"))
        .build();
  }
}
