package com.xgen.cloud.partners.vercel.billing._private.dao;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.mongodb.client.result.InsertOneResult;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoice200Response;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoiceRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelBillingItem;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelBillingPeriod;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoice;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoiceState;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.plan.VercelBillingPlanId;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class VercelInvoiceDocumentDaoImplIntTests extends JUnit5BaseSvcTest {

  private static final LocalDateTime STATIC_NOW = LocalDateTime.parse("2025-06-17T12:30:01");
  private static final String INSTALLATION_ID = "test-installation-id";
  private static final String EXTERNAL_ID = "507f1f77bcf86cd799439011"; // Valid ObjectId hex string
  private static final String INVOICE_ID = "test-invoice-id";

  @Inject Clock clock;
  @Inject VercelInvoiceDocumentDaoImpl vercelInvoiceDocumentDao;

  private SubmitInvoiceRequest testSubmitInvoiceRequest;
  private SubmitInvoice200Response testSubmitInvoice200Response;
  private VercelInvoice testVercelInvoice;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    when(clock.instant()).thenReturn(STATIC_NOW.toInstant(ZoneOffset.UTC));

    // Create test data
    testSubmitInvoiceRequest = createTestSubmitInvoiceRequest();
    testSubmitInvoice200Response = createTestSubmitInvoice200Response();
    testVercelInvoice = createTestVercelInvoice();
  }

  private SubmitInvoiceRequest createTestSubmitInvoiceRequest() {
    return SubmitInvoiceRequest.builder()
        .externalId(EXTERNAL_ID)
        .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
        .memo("Test invoice memo")
        .period(createTestBillingPeriod())
        .items(List.of(createTestInvoiceItem()))
        .build();
  }

  private SubmitInvoice200Response createTestSubmitInvoice200Response() {
    return SubmitInvoice200Response.builder().invoiceId(INVOICE_ID).test(false).build();
  }

  private VercelInvoice createTestVercelInvoice() {
    return VercelInvoice.builder()
        .invoiceId(INVOICE_ID)
        .externalId(EXTERNAL_ID)
        .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
        .period(createTestBillingPeriod())
        .items(List.of(createTestInvoiceItem()))
        .total(BigDecimal.valueOf(100.00))
        .build();
  }

  private VercelBillingPeriod createTestBillingPeriod() {
    OffsetDateTime start = STATIC_NOW.minusDays(30).atOffset(ZoneOffset.UTC);
    OffsetDateTime end = STATIC_NOW.atOffset(ZoneOffset.UTC);
    return VercelBillingPeriod.builder().start(start).end(end).build();
  }

  private VercelBillingItem createTestInvoiceItem() {
    return VercelBillingItem.builder()
        .billingPlanId(VercelBillingPlanId.PAID)
        .name("Test Item")
        .price(BigDecimal.valueOf(50.00))
        .quantity(BigDecimal.valueOf(2))
        .units("units")
        .total(BigDecimal.valueOf(100.00))
        .build();
  }

  @Test
  public void testUpsertInsertsNewDocument() {
    // Create a complete document first to test basic functionality
    VercelInvoiceDocument completeDocument =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(null)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    // Insert directly for testing
    vercelInvoiceDocumentDao.insertMajority(completeDocument);

    // Verify we can find it
    List<VercelInvoiceDocument> results =
        vercelInvoiceDocumentDao.findByInstallationId(INSTALLATION_ID);

    assertFalse(results.isEmpty(), "Should find documents");
    assertThat("Should find exactly one document", results.size(), equalTo(1));
    assertThat(
        "Should find correct document", results.get(0).installationId(), equalTo(INSTALLATION_ID));
  }

  @Test
  public void testUpdateResponseWithResponse() {
    // Create a complete document first
    VercelInvoiceDocument initialDocument =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(null) // No response initially
            .attemptedAt(null) // No attempt initially
            .succeededAt(null) // No success initially
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    // Insert directly
    InsertOneResult insertOneResult = vercelInvoiceDocumentDao.insertMajority(initialDocument);
    ObjectId savedDocumentId = insertOneResult.getInsertedId().asObjectId().getValue();

    // Mock a later time for update
    LocalDateTime laterTime = STATIC_NOW.plusMinutes(10);
    when(clock.instant()).thenReturn(laterTime.toInstant(ZoneOffset.UTC));

    // Update with response
    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateResponse(savedDocumentId, testSubmitInvoice200Response);

    assertTrue(result.isPresent(), "Update should return a document");
    VercelInvoiceDocument document = result.get();

    // Verify response fields
    assertThat("response populated", document.response(), equalTo(testSubmitInvoice200Response));
    assertThat("succeededAt populated", document.succeededAt(), equalTo(laterTime));
    assertThat("updatedAt updated", document.updatedAt(), equalTo(laterTime));

    // Verify other timestamps remain unchanged
    assertThat("createdAt unchanged", document.createdAt(), equalTo(STATIC_NOW));
    assertThat("attemptedAt populated", document.attemptedAt(), equalTo(laterTime));
  }

  @Test
  public void testFindByInstallationId() {
    // Create test documents
    VercelInvoiceDocument doc1 =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    // Create a different invoice for doc2 to avoid duplicate key error
    VercelInvoice differentInvoice =
        VercelInvoice.builder()
            .invoiceId("different-invoice-id")
            .externalId("different-external-id")
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .total(BigDecimal.valueOf(200.00))
            .build();

    SubmitInvoiceRequest differentRequest =
        SubmitInvoiceRequest.builder()
            .externalId("different-external-id")
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .memo("Different test invoice memo")
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .build();

    SubmitInvoice200Response differentResponse =
        SubmitInvoice200Response.builder().invoiceId("different-invoice-id").test(false).build();

    VercelInvoiceDocument doc2 =
        VercelInvoiceDocument.builder()
            .installationId("other-installation")
            .invoice(differentInvoice)
            .request(differentRequest)
            .response(differentResponse)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    vercelInvoiceDocumentDao.insertMajority(doc1);
    vercelInvoiceDocumentDao.insertMajority(doc2);

    List<VercelInvoiceDocument> results =
        vercelInvoiceDocumentDao.findByInstallationId(INSTALLATION_ID);

    assertFalse(results.isEmpty(), "Should find documents");
    assertThat("Should find exactly one document", results.size(), equalTo(1));
    assertThat(
        "Should find correct document", results.get(0).installationId(), equalTo(INSTALLATION_ID));
  }

  @Test
  public void testFindByRequestExternalId() {
    // Create test document
    VercelInvoiceDocument doc =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    vercelInvoiceDocumentDao.insertMajority(doc);

    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.findByRequestExternalId(EXTERNAL_ID);

    assertTrue(result.isPresent(), "Should find document by request external ID");
    assertThat(
        "Should find correct document", result.get().request().externalId(), equalTo(EXTERNAL_ID));
  }

  @Test
  public void testUpdateResponseNonExistentDocument() {
    // Try to update a document that doesn't exist
    SubmitInvoice200Response response =
        SubmitInvoice200Response.builder().invoiceId("non-existent-invoice-id").test(false).build();

    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateResponse(ObjectId.get(), response);

    assertFalse(result.isPresent(), "Update should return empty for non-existent document");
  }

  @Test
  public void testUpdateInvoice() {
    // Create a document to update
    VercelInvoiceDocument initialDocument =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(null) // No invoice initially
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    vercelInvoiceDocumentDao.insertMajority(initialDocument);

    // Mock a later time for update
    LocalDateTime laterTime = STATIC_NOW.plusMinutes(15);
    when(clock.instant()).thenReturn(laterTime.toInstant(ZoneOffset.UTC));

    // Update with invoice using paymentId (from external ID)
    ObjectId paymentId = new ObjectId(EXTERNAL_ID);
    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateInvoice(paymentId, testVercelInvoice);

    assertTrue(result.isPresent(), "Update should return a document");
    VercelInvoiceDocument document = result.get();

    // Verify invoice fields
    assertThat("invoice populated", document.invoice(), equalTo(testVercelInvoice));
    assertThat("updatedAt updated", document.updatedAt(), equalTo(laterTime));

    // Verify other timestamps remain unchanged
    assertThat("createdAt unchanged", document.createdAt(), equalTo(STATIC_NOW));
    assertThat("attemptedAt unchanged", document.attemptedAt(), equalTo(STATIC_NOW));
    assertThat("succeededAt unchanged", document.succeededAt(), equalTo(STATIC_NOW));
  }

  @Test
  public void testUpdateInvoiceNonExistentDocument() {
    // Try to update a document that doesn't exist
    ObjectId nonExistentPaymentId = ObjectId.get();
    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateInvoice(nonExistentPaymentId, testVercelInvoice);

    assertFalse(result.isPresent(), "Update should return empty for non-existent document");
  }

  @Test
  public void testUpdateInvoicePreservesOtherFields() {
    // Create a document with response first
    VercelInvoiceDocument initialDocument =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(null) // No invoice initially
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    vercelInvoiceDocumentDao.insertMajority(initialDocument);

    // Mock a later time for invoice update
    LocalDateTime laterTime = STATIC_NOW.plusMinutes(20);
    when(clock.instant()).thenReturn(laterTime.toInstant(ZoneOffset.UTC));

    // Update with invoice using paymentId (from external ID)
    ObjectId paymentId = new ObjectId(EXTERNAL_ID);
    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateInvoice(paymentId, testVercelInvoice);

    assertTrue(result.isPresent(), "Update should return a document");
    VercelInvoiceDocument document = result.get();

    // Verify invoice was added
    assertThat("invoice populated", document.invoice(), equalTo(testVercelInvoice));
    assertThat("updatedAt updated", document.updatedAt(), equalTo(laterTime));

    // Verify other fields are preserved
    assertThat("response preserved", document.response(), equalTo(testSubmitInvoice200Response));
    assertThat("request preserved", document.request(), equalTo(testSubmitInvoiceRequest));
    assertThat("succeededAt preserved", document.succeededAt(), equalTo(STATIC_NOW));
    assertThat("attemptedAt preserved", document.attemptedAt(), equalTo(STATIC_NOW));
    assertThat("createdAt preserved", document.createdAt(), equalTo(STATIC_NOW));
  }

  @Test
  public void testUpsertWithRequestThenUpdateWithInvoice() {
    // First, upsert a document with just the request object
    Optional<VercelInvoiceDocument> upsertResult =
        vercelInvoiceDocumentDao.upsert(INSTALLATION_ID, testSubmitInvoiceRequest);

    assertTrue(upsertResult.isPresent(), "Upsert should return a document");
    VercelInvoiceDocument upsertedDocument = upsertResult.get();

    // Verify the upserted document has the request and proper timestamps
    assertThat(
        "request externalId populated",
        upsertedDocument.request().externalId(),
        equalTo(testSubmitInvoiceRequest.externalId()));
    assertThat(
        "request memo populated",
        upsertedDocument.request().memo(),
        equalTo(testSubmitInvoiceRequest.memo()));
    assertThat(
        "installationId populated", upsertedDocument.installationId(), equalTo(INSTALLATION_ID));
    assertThat(
        "attemptedAt should be null initially", upsertedDocument.attemptedAt(), equalTo(null));
    assertThat("updatedAt populated", upsertedDocument.updatedAt(), equalTo(STATIC_NOW));
    assertThat("createdAt populated", upsertedDocument.createdAt(), equalTo(STATIC_NOW));

    // Verify fields that should be null initially
    assertThat("invoice should be null initially", upsertedDocument.invoice(), equalTo(null));
    assertThat("response should be null initially", upsertedDocument.response(), equalTo(null));
    assertThat(
        "succeededAt should be null initially", upsertedDocument.succeededAt(), equalTo(null));

    // Mock a later time for the invoice update
    LocalDateTime laterTime = STATIC_NOW.plusMinutes(30);
    when(clock.instant()).thenReturn(laterTime.toInstant(ZoneOffset.UTC));

    // Now update the same document with an invoice using paymentId (from external ID)
    ObjectId paymentId = new ObjectId(EXTERNAL_ID);
    Optional<VercelInvoiceDocument> updateResult =
        vercelInvoiceDocumentDao.updateInvoice(paymentId, testVercelInvoice);

    assertTrue(updateResult.isPresent(), "Update should return a document");
    VercelInvoiceDocument updatedDocument = updateResult.get();

    // Verify the invoice was added
    assertThat(
        "invoice invoiceId populated",
        updatedDocument.invoice().invoiceId(),
        equalTo(testVercelInvoice.invoiceId()));
    assertThat(
        "invoice externalId populated",
        updatedDocument.invoice().externalId(),
        equalTo(testVercelInvoice.externalId()));
    assertThat(
        "invoice total populated",
        updatedDocument.invoice().total(),
        equalTo(testVercelInvoice.total()));
    assertThat("updatedAt updated", updatedDocument.updatedAt(), equalTo(laterTime));

    // Verify the original request and timestamps are preserved
    assertThat(
        "request externalId preserved",
        updatedDocument.request().externalId(),
        equalTo(testSubmitInvoiceRequest.externalId()));
    assertThat(
        "request memo preserved",
        updatedDocument.request().memo(),
        equalTo(testSubmitInvoiceRequest.memo()));
    assertThat(
        "installationId preserved", updatedDocument.installationId(), equalTo(INSTALLATION_ID));
    assertThat("attemptedAt should still be null", updatedDocument.attemptedAt(), equalTo(null));
    assertThat("createdAt preserved", updatedDocument.createdAt(), equalTo(STATIC_NOW));

    // Verify fields that should still be null
    assertThat("response should still be null", updatedDocument.response(), equalTo(null));
    assertThat("succeededAt should still be null", updatedDocument.succeededAt(), equalTo(null));
  }

  @Test
  public void testUpdateResponseAfterUpsertWithRequest() {
    // First, upsert a document with just the request object
    Optional<VercelInvoiceDocument> upsertResult =
        vercelInvoiceDocumentDao.upsert(INSTALLATION_ID, testSubmitInvoiceRequest);

    assertTrue(upsertResult.isPresent(), "Upsert should return a document");
    VercelInvoiceDocument upsertedDocument = upsertResult.get();

    // Verify the upserted document has the request and proper timestamps
    assertThat(
        "request externalId populated",
        upsertedDocument.request().externalId(),
        equalTo(testSubmitInvoiceRequest.externalId()));
    assertThat(
        "request memo populated",
        upsertedDocument.request().memo(),
        equalTo(testSubmitInvoiceRequest.memo()));
    assertThat(
        "installationId populated", upsertedDocument.installationId(), equalTo(INSTALLATION_ID));
    assertThat(
        "attemptedAt should be null initially", upsertedDocument.attemptedAt(), equalTo(null));
    assertThat("updatedAt populated", upsertedDocument.updatedAt(), equalTo(STATIC_NOW));
    assertThat("createdAt populated", upsertedDocument.createdAt(), equalTo(STATIC_NOW));

    // Verify fields that should be null initially
    assertThat("invoice should be null initially", upsertedDocument.invoice(), equalTo(null));
    assertThat("response should be null initially", upsertedDocument.response(), equalTo(null));
    assertThat(
        "succeededAt should be null initially", upsertedDocument.succeededAt(), equalTo(null));

    // Mock a later time for the response update
    LocalDateTime laterTime = STATIC_NOW.plusMinutes(25);
    when(clock.instant()).thenReturn(laterTime.toInstant(ZoneOffset.UTC));

    // Now update the same document with a response
    Optional<VercelInvoiceDocument> updateResult =
        vercelInvoiceDocumentDao.updateResponse(
            upsertedDocument.id(), testSubmitInvoice200Response);

    assertTrue(updateResult.isPresent(), "Update should return a document");
    VercelInvoiceDocument updatedDocument = updateResult.get();

    // Verify the response was added
    assertThat(
        "response populated", updatedDocument.response(), equalTo(testSubmitInvoice200Response));
    assertThat("succeededAt populated", updatedDocument.succeededAt(), equalTo(laterTime));
    assertThat("updatedAt updated", updatedDocument.updatedAt(), equalTo(laterTime));

    // Verify the original request and timestamps are preserved
    assertThat(
        "request externalId preserved",
        updatedDocument.request().externalId(),
        equalTo(testSubmitInvoiceRequest.externalId()));
    assertThat(
        "request memo preserved",
        updatedDocument.request().memo(),
        equalTo(testSubmitInvoiceRequest.memo()));
    assertThat(
        "installationId preserved", updatedDocument.installationId(), equalTo(INSTALLATION_ID));
    assertThat("attemptedAt updated", updatedDocument.attemptedAt(), equalTo(laterTime));
    assertThat("createdAt preserved", updatedDocument.createdAt(), equalTo(STATIC_NOW));

    // Verify invoice should still be null
    assertThat("invoice should still be null", updatedDocument.invoice(), equalTo(null));
  }

  @Test
  public void testUpdateForFailure() {
    // Create a document with initial failure count
    VercelInvoiceDocument initialDocument =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(2) // Start with 2 failures
            .errorMessage("Previous error")
            .build();

    InsertOneResult insertOneResult = vercelInvoiceDocumentDao.insertMajority(initialDocument);
    ObjectId savedDocumentId = insertOneResult.getInsertedId().asObjectId().getValue();

    // Mock a later time for failure update
    LocalDateTime laterTime = STATIC_NOW.plusMinutes(5);
    when(clock.instant()).thenReturn(laterTime.toInstant(ZoneOffset.UTC));

    // Update with failure
    String newErrorMessage = "New failure occurred";
    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateForFailure(savedDocumentId, newErrorMessage);

    assertTrue(result.isPresent(), "Update should return a document");
    VercelInvoiceDocument document = result.get();

    // Verify failure fields were updated
    assertThat("countFailures incremented", document.countFailures(), equalTo(3));
    assertThat("errorMessage updated", document.errorMessage(), equalTo(newErrorMessage));
    assertThat("updatedAt updated", document.updatedAt(), equalTo(laterTime));

    // Verify other fields remain unchanged
    assertThat("installationId preserved", document.installationId(), equalTo(INSTALLATION_ID));
    assertThat("invoice preserved", document.invoice(), equalTo(testVercelInvoice));
    assertThat("request preserved", document.request(), equalTo(testSubmitInvoiceRequest));
    assertThat("response preserved", document.response(), equalTo(testSubmitInvoice200Response));
    assertThat("attemptedAt updated", document.attemptedAt(), equalTo(laterTime));
    assertThat("succeededAt preserved", document.succeededAt(), equalTo(STATIC_NOW));
    assertThat("createdAt preserved", document.createdAt(), equalTo(STATIC_NOW));
  }

  @Test
  public void testUpdateForFailureFromZeroFailures() {
    // Create a document with zero failures
    VercelInvoiceDocument initialDocument =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(null)
            .attemptedAt(STATIC_NOW)
            .succeededAt(null)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0) // Start with 0 failures
            .errorMessage(null) // No previous error
            .build();

    InsertOneResult insertOneResult = vercelInvoiceDocumentDao.insertMajority(initialDocument);
    ObjectId savedDocumentId = insertOneResult.getInsertedId().asObjectId().getValue();

    // Mock a later time for failure update
    LocalDateTime laterTime = STATIC_NOW.plusMinutes(3);
    when(clock.instant()).thenReturn(laterTime.toInstant(ZoneOffset.UTC));

    // Update with first failure
    String errorMessage = "First failure occurred";
    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateForFailure(savedDocumentId, errorMessage);

    assertTrue(result.isPresent(), "Update should return a document");
    VercelInvoiceDocument document = result.get();

    // Verify failure fields were updated
    assertThat("countFailures incremented to 1", document.countFailures(), equalTo(1));
    assertThat("errorMessage set", document.errorMessage(), equalTo(errorMessage));
    assertThat("updatedAt updated", document.updatedAt(), equalTo(laterTime));

    // Verify other fields remain unchanged
    assertThat("response still null", document.response(), equalTo(null));
    assertThat("succeededAt still null", document.succeededAt(), equalTo(null));
  }

  @Test
  public void testUpdateForFailureNonExistentDocument() {
    // Try to update a document that doesn't exist
    String errorMessage = "Error for non-existent document";
    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateForFailure(ObjectId.get(), errorMessage);

    assertFalse(result.isPresent(), "Update should return empty for non-existent document");
  }

  @Test
  public void testUpdateForFailureWithNullErrorMessage() {
    // Create a document
    VercelInvoiceDocument initialDocument =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(1)
            .errorMessage("Previous error")
            .build();

    InsertOneResult insertOneResult = vercelInvoiceDocumentDao.insertMajority(initialDocument);
    ObjectId savedDocumentId = insertOneResult.getInsertedId().asObjectId().getValue();

    // Mock a later time for failure update
    LocalDateTime laterTime = STATIC_NOW.plusMinutes(7);
    when(clock.instant()).thenReturn(laterTime.toInstant(ZoneOffset.UTC));

    // Update with null error message
    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateForFailure(savedDocumentId, null);

    assertTrue(result.isPresent(), "Update should return a document");
    VercelInvoiceDocument document = result.get();

    // Verify failure fields were updated
    assertThat("countFailures incremented", document.countFailures(), equalTo(2));
    assertThat("errorMessage set to null", document.errorMessage(), equalTo(null));
    assertThat("updatedAt updated", document.updatedAt(), equalTo(laterTime));
  }

  @Test
  public void testFindAllByRequestExternalIds() {
    // Create test documents with different external IDs
    String externalId1 = "external-id-1";
    String externalId2 = "external-id-2";
    String externalId3 = "external-id-3";

    SubmitInvoiceRequest request1 =
        SubmitInvoiceRequest.builder()
            .externalId(externalId1)
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .memo("Test invoice memo 1")
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .build();

    SubmitInvoiceRequest request2 =
        SubmitInvoiceRequest.builder()
            .externalId(externalId2)
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .memo("Test invoice memo 2")
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .build();

    SubmitInvoiceRequest request3 =
        SubmitInvoiceRequest.builder()
            .externalId(externalId3)
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .memo("Test invoice memo 3")
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .build();

    VercelInvoice invoice1 =
        VercelInvoice.builder()
            .invoiceId("invoice-id-1")
            .externalId(externalId1)
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .total(BigDecimal.valueOf(100.00))
            .build();

    VercelInvoice invoice2 =
        VercelInvoice.builder()
            .invoiceId("invoice-id-2")
            .externalId(externalId2)
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .total(BigDecimal.valueOf(200.00))
            .build();

    VercelInvoice invoice3 =
        VercelInvoice.builder()
            .invoiceId("invoice-id-3")
            .externalId(externalId3)
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .total(BigDecimal.valueOf(300.00))
            .build();

    SubmitInvoice200Response response1 =
        SubmitInvoice200Response.builder().invoiceId("invoice-id-1").test(false).build();

    SubmitInvoice200Response response2 =
        SubmitInvoice200Response.builder().invoiceId("invoice-id-2").test(false).build();

    SubmitInvoice200Response response3 =
        SubmitInvoice200Response.builder().invoiceId("invoice-id-3").test(false).build();

    VercelInvoiceDocument doc1 =
        VercelInvoiceDocument.builder()
            .installationId("installation-1")
            .invoice(invoice1)
            .request(request1)
            .response(response1)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    VercelInvoiceDocument doc2 =
        VercelInvoiceDocument.builder()
            .installationId("installation-2")
            .invoice(invoice2)
            .request(request2)
            .response(response2)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    VercelInvoiceDocument doc3 =
        VercelInvoiceDocument.builder()
            .installationId("installation-3")
            .invoice(invoice3)
            .request(request3)
            .response(response3)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    // Insert test documents
    vercelInvoiceDocumentDao.insertMajority(doc1);
    vercelInvoiceDocumentDao.insertMajority(doc2);
    vercelInvoiceDocumentDao.insertMajority(doc3);

    // Test finding multiple documents by external IDs
    Set<String> searchExternalIds = Set.of(externalId1, externalId2);
    List<VercelInvoiceDocument> results =
        vercelInvoiceDocumentDao
            .findAllByRequestExternalIds(searchExternalIds)
            .collect(Collectors.toList());

    assertThat("Should find exactly 2 documents", results.size(), equalTo(2));

    Set<String> foundExternalIds =
        results.stream().map(doc -> doc.request().externalId()).collect(Collectors.toSet());

    assertTrue(foundExternalIds.contains(externalId1), "Should find document with external ID 1");
    assertTrue(foundExternalIds.contains(externalId2), "Should find document with external ID 2");
    assertFalse(
        foundExternalIds.contains(externalId3), "Should not find document with external ID 3");
  }

  @Test
  public void testFindAllByRequestExternalIdsWithEmptyCollection() {
    // Create a test document
    VercelInvoiceDocument doc =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    vercelInvoiceDocumentDao.insertMajority(doc);

    // Test with empty collection
    List<VercelInvoiceDocument> results =
        vercelInvoiceDocumentDao.findAllByRequestExternalIds(Set.of()).collect(Collectors.toList());

    assertTrue(results.isEmpty(), "Should return empty list for empty external IDs collection");
  }

  @Test
  public void testFindAllByRequestExternalIdsWithNonExistentIds() {
    // Create a test document
    VercelInvoiceDocument doc =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    vercelInvoiceDocumentDao.insertMajority(doc);

    // Test with non-existent external IDs
    Set<String> nonExistentIds = Set.of("non-existent-1", "non-existent-2");
    List<VercelInvoiceDocument> results =
        vercelInvoiceDocumentDao
            .findAllByRequestExternalIds(nonExistentIds)
            .collect(Collectors.toList());

    assertTrue(results.isEmpty(), "Should return empty list for non-existent external IDs");
  }

  @Test
  public void testFindAllByRequestExternalIdsWithMixedExistingAndNonExistent() {
    // Create test documents
    String existingExternalId = "existing-external-id";
    SubmitInvoiceRequest existingRequest =
        SubmitInvoiceRequest.builder()
            .externalId(existingExternalId)
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .memo("Existing test invoice memo")
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .build();

    VercelInvoice existingInvoice =
        VercelInvoice.builder()
            .invoiceId("existing-invoice-id")
            .externalId(existingExternalId)
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .total(BigDecimal.valueOf(150.00))
            .build();

    SubmitInvoice200Response existingResponse =
        SubmitInvoice200Response.builder().invoiceId("existing-invoice-id").test(false).build();

    VercelInvoiceDocument existingDoc =
        VercelInvoiceDocument.builder()
            .installationId("existing-installation")
            .invoice(existingInvoice)
            .request(existingRequest)
            .response(existingResponse)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    vercelInvoiceDocumentDao.insertMajority(existingDoc);

    // Test with mixed existing and non-existent external IDs
    Set<String> mixedIds = Set.of(existingExternalId, "non-existent-1", "non-existent-2");
    List<VercelInvoiceDocument> results =
        vercelInvoiceDocumentDao.findAllByRequestExternalIds(mixedIds).collect(Collectors.toList());

    assertThat("Should find exactly 1 document", results.size(), equalTo(1));
    assertThat(
        "Should find the existing document",
        results.get(0).request().externalId(),
        equalTo(existingExternalId));
  }

  @Test
  public void testFindAllByRequestExternalIdsWithSingleId() {
    // Create a test document
    VercelInvoiceDocument doc =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(testVercelInvoice)
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    vercelInvoiceDocumentDao.insertMajority(doc);

    // Test with single external ID
    Set<String> singleId = Set.of(EXTERNAL_ID);
    List<VercelInvoiceDocument> results =
        vercelInvoiceDocumentDao.findAllByRequestExternalIds(singleId).collect(Collectors.toList());

    assertThat("Should find exactly 1 document", results.size(), equalTo(1));
    assertThat(
        "Should find correct document",
        results.get(0).request().externalId(),
        equalTo(EXTERNAL_ID));
  }

  @Test
  public void updateInvoiceState_existingInvoice_updatesStateAndTimestamp() {
    // Create a document with an invoice and initial state
    VercelInvoice invoiceWithState =
        VercelInvoice.builder()
            .invoiceId(INVOICE_ID)
            .externalId(EXTERNAL_ID)
            .state(VercelInvoiceState.PENDING)
            .invoiceDate(STATIC_NOW.atOffset(ZoneOffset.UTC))
            .period(createTestBillingPeriod())
            .items(List.of(createTestInvoiceItem()))
            .total(BigDecimal.valueOf(100.00))
            .build();

    VercelInvoiceDocument initialDocument =
        VercelInvoiceDocument.builder()
            .installationId(INSTALLATION_ID)
            .invoice(invoiceWithState)
            .request(testSubmitInvoiceRequest)
            .response(testSubmitInvoice200Response)
            .attemptedAt(STATIC_NOW)
            .succeededAt(STATIC_NOW)
            .createdAt(STATIC_NOW)
            .updatedAt(STATIC_NOW)
            .countFailures(0)
            .build();

    vercelInvoiceDocumentDao.insertMajority(initialDocument);

    // Mock later time
    LocalDateTime laterTime = STATIC_NOW.plusMinutes(12);
    when(clock.instant()).thenReturn(laterTime.toInstant(ZoneOffset.UTC));

    // Update state
    Optional<VercelInvoiceDocument> result =
        vercelInvoiceDocumentDao.updateInvoiceState(INVOICE_ID, VercelInvoiceState.INVOICED);

    assertTrue(result.isPresent(), "Update should return a document");
    VercelInvoiceDocument updated = result.get();

    // Verify state updated and timestamps
    assertThat(
        "invoice state updated", updated.invoice().state(), equalTo(VercelInvoiceState.INVOICED));
    assertThat("updatedAt updated", updated.updatedAt(), equalTo(laterTime));

    // Verify other fields preserved
    assertThat("invoiceId preserved", updated.invoice().invoiceId(), equalTo(INVOICE_ID));
    assertThat("externalId preserved", updated.invoice().externalId(), equalTo(EXTERNAL_ID));
    assertThat("createdAt preserved", updated.createdAt(), equalTo(STATIC_NOW));
  }
}
