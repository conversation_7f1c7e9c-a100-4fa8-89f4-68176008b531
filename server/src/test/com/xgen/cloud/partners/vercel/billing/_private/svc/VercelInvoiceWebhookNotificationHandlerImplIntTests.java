package com.xgen.cloud.partners.vercel.billing._private.svc;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.nimbusds.oauth2.sdk.token.BearerAccessToken;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.partners.vercel.billing._private.dao.VercelInternalEventDao;
import com.xgen.cloud.partners.vercel.billing._public.config.VercelBillingConfig;
import com.xgen.cloud.partners.vercel.billing._public.model.VercelInvoiceDocument;
import com.xgen.cloud.partners.vercel.billing._public.model.event.VercelInternalEvent;
import com.xgen.cloud.partners.vercel.billing._public.model.event.VercelInternalEventType;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInstallationSvc;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInvoiceDocumentSvc;
import com.xgen.cloud.partners.vercel.sdk.client._public.svc.VercelMarketplaceApiClient;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.SubmitInvoiceRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelBillingPeriod;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoice;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelInvoiceState;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.plan.VercelBillingPlanId;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceCreatedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceCreatedPayload;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceNotPaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceNotPaidPayload;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoicePaidNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoicePaidPayload;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceRefundedNotification;
import com.xgen.cloud.partners.vercel.webhook._public.model.MarketplaceInvoiceRefundedPayload;
import com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient;
import com.xgen.cloud.payments.grpc._public.client.RefundQueryingClient;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.RefundDao;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.InvoiceDao;
import com.xgen.svc.mms.dao.billing.PaymentDao;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.Refund;
import com.xgen.svc.mms.model.billing.RevenueRefundReason;
import com.xgen.svc.mms.model.billing.VercelPaymentContext;
import com.xgen.svc.mms.util.billing.testFactories.CreateInvoiceParams;
import com.xgen.svc.mms.util.billing.testFactories.InvoiceFactory;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * Integration tests for VercelInvoiceWebhookNotificationHandlerImpl. Tests core webhook
 * notification handler methods to ensure proper processing of Vercel marketplace invoice events and
 * basic functionality.
 */
@ExtendWith({GuiceTestExtension.class, MockitoExtension.class})
public class VercelInvoiceWebhookNotificationHandlerImplIntTests extends JUnit5BaseSvcTest {

  private static final LocalDateTime STATIC_NOW = LocalDateTime.of(2025, Month.JUNE, 26, 12, 30, 0);
  private static final String TEST_INSTALLATION_ID = "test-installation-123";
  private static final String TEST_INVOICE_ID = "vercel-invoice-456";
  private static final String TEST_WEBHOOK_ID = "webhook-789";
  private static final String TEST_REGION = "us-east-1";

  @Inject private PaymentDao paymentDao;
  @Inject private InvoiceDao invoiceDao;
  @Inject private RefundDao refundDao;
  @Inject private VercelInstallationSvc vercelInstallationSvc;
  @Inject private VercelInvoiceDocumentSvc vercelInvoiceDocumentSvc;
  @Inject private VercelMarketplaceApiClient vercelMarketplaceApiClient;
  @Inject private VercelInvoiceWebhookNotificationHandlerImpl handler;
  @Inject private VercelBillingConfig vercelBillingConfig;
  @Inject private VercelInternalEventDao vercelInternalEventDao;
  @Inject private InvoiceFactory invoiceFactory;
  @Inject private PaymentMethodClient paymentMethodClient;
  @Inject private RefundQueryingClient refundQueryingClient;
  @Inject private Clock clock;

  private VercelInstallation testInstallation;
  private Organization testOrganization;
  private ObjectId testPaymentId;
  private Payment testPayment;
  private Invoice testInvoice;
  private OffsetDateTime testCreatedAt;
  private VercelInvoice testVercelInvoice;
  private VercelInvoiceDocument testInvoiceDocument;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    when(clock.instant()).thenReturn(STATIC_NOW.toInstant(ZoneOffset.UTC));
    // set to false by default for existing tests
    doReturn(false).when(vercelBillingConfig).isInvoiceFinalizeEnabled();

    // Create test organization
    testOrganization = MmsFactory.createOrganizationWithNDSPlan();

    // Create test installation with properly encrypted access token
    testInstallation =
        VercelInstallation.builder()
            .installationId(TEST_INSTALLATION_ID)
            .orgId(testOrganization.getId())
            .accessTokenEncrypted(
                "test-access-token-for-webhook-tests", vercelBillingConfig.getAccessTokenKey())
            .tokenType("Bearer")
            .billingPlanId(VercelBillingPlanId.PAID)
            .installationUrl("https://vercel.com/test")
            .build();
    vercelInstallationSvc.upsert(testInstallation);

    // Create test invoice for MongoDB Atlas billing
    testCreatedAt = OffsetDateTime.now(clock);
    Date testStartDate = Date.from(testCreatedAt.minusDays(30).toInstant());
    Date testEndDate = Date.from(testCreatedAt.toInstant());

    testInvoice =
        invoiceFactory.createInvoice(
            new CreateInvoiceParams.Builder()
                .startDate(testStartDate)
                .endDate(testEndDate)
                .organizationId(testOrganization.getId())
                .usageAmountCents(10000L) // $100.00
                .invoiceStatus(Invoice.Status.PENDING)
                .build());

    // Create test payment with Vercel context
    testPaymentId = ObjectId.get();
    testPayment =
        Payment.builder()
            .id(testPaymentId)
            .orgId(testOrganization.getId())
            .invoiceId(testInvoice.getId()) // Link to the MongoDB Atlas invoice
            .status(Payment.Status.INVOICED)
            .amountBilledCents(10000L) // $100.00
            .vercel(VercelPaymentContext.builder().invoiceId(TEST_INVOICE_ID).build())
            .build();
    paymentDao.save(testPayment);

    // Create test Vercel invoice
    testVercelInvoice =
        VercelInvoice.builder()
            .invoiceId(TEST_INVOICE_ID)
            .externalId(testPaymentId.toString())
            .state(VercelInvoiceState.INVOICED)
            .invoiceDate(testCreatedAt.minusDays(1))
            .period(
                VercelBillingPeriod.builder()
                    .start(testCreatedAt.minusDays(30))
                    .end(testCreatedAt)
                    .build())
            .items(List.of())
            .discounts(List.of())
            .total(new BigDecimal("100.00"))
            .created(testCreatedAt)
            .updated(testCreatedAt)
            .build();

    // Create test invoice document
    SubmitInvoiceRequest testRequest =
        SubmitInvoiceRequest.builder()
            .externalId(testPaymentId.toString())
            .invoiceDate(testCreatedAt.minusDays(1))
            .memo("Test invoice")
            .build();

    testInvoiceDocument =
        VercelInvoiceDocument.builder()
            .installationId(TEST_INSTALLATION_ID)
            .request(testRequest)
            .createdAt(STATIC_NOW)
            .build();

    // Create the invoice document in the database
    vercelInvoiceDocumentSvc.upsert(TEST_INSTALLATION_ID, testRequest);
    // Ensure the invoice document has the embedded invoice so state updates can match by invoiceId
    vercelInvoiceDocumentSvc.updateInvoice(testPaymentId, testVercelInvoice);

    // Mock VercelMarketplaceApiClient.getInvoice to return the test invoice
    when(vercelMarketplaceApiClient.getInvoice(
            eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class)))
        .thenReturn(Mono.just(testVercelInvoice));
  }

  @Override
  @AfterEach
  public void tearDown() {
    // Reset all mocks to prevent test interference
    reset(paymentMethodClient);
    reset(refundQueryingClient);
    reset(vercelMarketplaceApiClient);
    reset(vercelBillingConfig);
    reset(clock);
  }

  @Test
  public void testHandleMarketplaceInvoiceCreatedNotification() {
    // Arrange
    MarketplaceInvoiceCreatedPayload payload =
        MarketplaceInvoiceCreatedPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("100.00"))
            .build();

    MarketplaceInvoiceCreatedNotification notification =
        MarketplaceInvoiceCreatedNotification.builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert - Test that the handler processes the notification without error
    StepVerifier.create(handler.handle(notification)).verifyComplete();

    // Verify that the VercelMarketplaceApiClient.getInvoice was called
    verify(vercelMarketplaceApiClient, atLeastOnce())
        .getInvoice(eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class));

    // Verify that the invoice document was updated with the fetched invoice
    List<VercelInvoiceDocument> invoiceDocuments =
        vercelInvoiceDocumentSvc.findByInstallationId(TEST_INSTALLATION_ID);
    assertEquals(1, invoiceDocuments.size(), "Should have one invoice document");

    VercelInvoiceDocument updatedDocument = invoiceDocuments.get(0);
    assertNotNull(updatedDocument.invoice(), "Invoice document should have invoice data");
    assertEquals(
        TEST_INVOICE_ID,
        updatedDocument.invoice().invoiceId(),
        "Invoice document should contain the correct invoice ID");
    assertEquals(
        VercelInvoiceState.INVOICED,
        updatedDocument.invoice().state(),
        "Invoice document should contain the correct invoice state");
    assertNotNull(updatedDocument.updatedAt(), "Invoice document should have updated timestamp");

    // Verify that the internal event was published
    assertInvoiceEventCreated(
        TEST_INSTALLATION_ID,
        testPaymentId,
        TEST_INVOICE_ID,
        VercelInternalEventType.MARKETPLACE_INVOICE_CREATED);
  }

  @Test
  public void testHandleMarketplaceInvoiceNotPaidNotification() {
    // Mock PaymentMethodClient to prevent gRPC calls during email sending
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getActivePaymentMethod(any(ObjectId.class), any(Boolean.class));
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getMostRecentBeforeOrEqualToDate(any(ObjectId.class), any());

    // Mock RefundQueryingClient to prevent gRPC calls during email sending
    doReturn(Collections.emptyList())
        .when(refundQueryingClient)
        .getRefundsByInvoiceId(any(ObjectId.class));

    // Arrange
    VercelInvoice notPaidInvoice =
        testVercelInvoice.toBuilder().state(VercelInvoiceState.NOTPAID).build();
    when(vercelMarketplaceApiClient.getInvoice(
            eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class)))
        .thenReturn(Mono.just(notPaidInvoice));

    MarketplaceInvoiceNotPaidPayload payload =
        MarketplaceInvoiceNotPaidPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("100.00"))
            .build();

    MarketplaceInvoiceNotPaidNotification notification =
        new MarketplaceInvoiceNotPaidNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert - Test that the handler processes the notification and updates payment status
    StepVerifier.create(handler.handle(notification)).verifyComplete();

    // Verify payment status was updated to FAILED
    Payment updatedPayment = paymentDao.findById(testPaymentId);
    assertNotNull(updatedPayment);
    assertEquals(Payment.Status.FAILED, updatedPayment.getStatus());

    // Verify that the VercelMarketplaceApiClient.getInvoice was called
    verify(vercelMarketplaceApiClient, atLeastOnce())
        .getInvoice(eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class));

    // Verify that the invoice document was updated with the fetched invoice
    List<VercelInvoiceDocument> invoiceDocuments =
        vercelInvoiceDocumentSvc.findByInstallationId(TEST_INSTALLATION_ID);
    assertEquals(1, invoiceDocuments.size(), "Should have one invoice document");

    VercelInvoiceDocument updatedDocument = invoiceDocuments.get(0);
    assertNotNull(updatedDocument.invoice(), "Invoice document should have invoice data");
    assertEquals(
        VercelInvoiceState.NOTPAID,
        updatedDocument.invoice().state(),
        "Invoice document should reflect NOTPAID state");

    // Verify that the internal event was published
    assertInvoiceEventCreated(
        TEST_INSTALLATION_ID,
        testPaymentId,
        TEST_INVOICE_ID,
        VercelInternalEventType.MARKETPLACE_INVOICE_NOTPAID);
  }

  @Test
  public void testHandleNotificationWithNonExistentInstallation() {
    // Arrange
    MarketplaceInvoiceCreatedPayload payload =
        MarketplaceInvoiceCreatedPayload.builder()
            .installationId("non-existent-installation")
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("100.00"))
            .build();

    MarketplaceInvoiceCreatedNotification notification =
        MarketplaceInvoiceCreatedNotification.builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert - Should handle error gracefully when installation doesn't exist
    StepVerifier.create(handler.handle(notification))
        .expectError(NoSuchElementException.class)
        .verify();
  }

  @Test
  public void testHandleInvoiceNotPaidWithInvalidPaymentId() {
    // Arrange
    MarketplaceInvoiceNotPaidPayload payload =
        MarketplaceInvoiceNotPaidPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId("invalid-payment-id")
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("100.00"))
            .build();

    MarketplaceInvoiceNotPaidNotification notification =
        new MarketplaceInvoiceNotPaidNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert - Should handle error gracefully when payment ID is invalid
    StepVerifier.create(handler.handle(notification)).expectError().verify();
  }

  @Test
  public void testHandleMarketplaceInvoicePaidNotification() {
    // Mock PaymentMethodClient to prevent gRPC calls during email sending
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getActivePaymentMethod(any(ObjectId.class), any(Boolean.class));
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getMostRecentBeforeOrEqualToDate(any(ObjectId.class), any());

    // Mock RefundQueryingClient to prevent gRPC calls during email sending
    doReturn(Collections.emptyList())
        .when(refundQueryingClient)
        .getRefundsByInvoiceId(any(ObjectId.class));

    // Arrange
    VercelInvoice paidInvoice =
        testVercelInvoice.toBuilder().state(VercelInvoiceState.PAID).build();
    when(vercelMarketplaceApiClient.getInvoice(
            eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class)))
        .thenReturn(Mono.just(paidInvoice));

    MarketplaceInvoicePaidPayload payload =
        MarketplaceInvoicePaidPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("123.45"))
            .build();

    MarketplaceInvoicePaidNotification notification =
        new MarketplaceInvoicePaidNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert
    StepVerifier.create(handler.handle(notification)).verifyComplete();

    // Verify payment status was updated to PAID
    Payment updatedPayment = paymentDao.findById(testPaymentId);
    assertNotNull(updatedPayment);
    assertEquals(Payment.Status.PAID, updatedPayment.getStatus());

    // Verify payment amount paid was updated by PaymentProcessedSvc
    assertEquals(
        12345L, updatedPayment.getAmountPaidCents(), "Payment amount paid should be updated");

    // Verify invoice exists and PaymentProcessedSvc was called
    Invoice updatedInvoice = invoiceDao.findById(testInvoice.getId());
    assertNotNull(updatedInvoice, "Invoice should exist");
    // Note: In test environment with mocked external services, invoice status may remain PENDING
    // The important verification is that PaymentProcessedSvc.handleEvent() was called without
    // errors

    // Verify that the VercelMarketplaceApiClient.getInvoice was called
    verify(vercelMarketplaceApiClient, atLeastOnce())
        .getInvoice(eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class));

    // Verify that the invoice document was updated with the fetched invoice
    List<VercelInvoiceDocument> invoiceDocuments =
        vercelInvoiceDocumentSvc.findByInstallationId(TEST_INSTALLATION_ID);
    assertEquals(1, invoiceDocuments.size(), "Should have one invoice document");

    VercelInvoiceDocument updatedDocument = invoiceDocuments.get(0);
    assertNotNull(updatedDocument.invoice(), "Invoice document should have invoice data");
    assertEquals(
        VercelInvoiceState.PAID,
        updatedDocument.invoice().state(),
        "Invoice document should reflect PAID state");

    // Verify that the internal event was published
    assertInvoiceEventCreated(
        TEST_INSTALLATION_ID,
        testPaymentId,
        TEST_INVOICE_ID,
        VercelInternalEventType.MARKETPLACE_INVOICE_PAID);
  }

  @Test
  public void testHandleMarketplaceInvoicePaidNotification_invalidInvoiceState() {
    // Mock PaymentMethodClient to prevent gRPC calls during email sending
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getActivePaymentMethod(any(ObjectId.class), any(Boolean.class));
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getMostRecentBeforeOrEqualToDate(any(ObjectId.class), any());

    // Mock RefundQueryingClient to prevent gRPC calls during email sending
    doReturn(Collections.emptyList())
        .when(refundQueryingClient)
        .getRefundsByInvoiceId(any(ObjectId.class));

    // Arrange - Mock API to return invoice with INVOICED state instead of PAID
    VercelInvoice invoicedInvoice =
        testVercelInvoice.toBuilder().state(VercelInvoiceState.INVOICED).build();
    when(vercelMarketplaceApiClient.getInvoice(
            eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class)))
        .thenReturn(Mono.just(invoicedInvoice));

    MarketplaceInvoicePaidPayload payload =
        MarketplaceInvoicePaidPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("123.45"))
            .build();

    MarketplaceInvoicePaidNotification notification =
        new MarketplaceInvoicePaidNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert - Verify that the handler throws an error due to invalid invoice state
    StepVerifier.create(handler.handle(notification))
        .expectErrorMatches(
            throwable ->
                throwable instanceof IllegalStateException
                    && throwable.getMessage().equals("Invoice state is not PAID"))
        .verify();

    // Verify that the invoice document was still updated with the fetched invoice
    List<VercelInvoiceDocument> invoiceDocuments =
        vercelInvoiceDocumentSvc.findByInstallationId(TEST_INSTALLATION_ID);
    assertEquals(1, invoiceDocuments.size(), "Should have one invoice document");

    VercelInvoiceDocument updatedDocument = invoiceDocuments.get(0);
    assertNotNull(updatedDocument.invoice(), "Invoice document should have invoice data");
    assertEquals(
        VercelInvoiceState.INVOICED,
        updatedDocument.invoice().state(),
        "Invoice document should reflect INVOICED state");

    // Verify that the internal event was still published
    assertInvoiceEventCreated(
        TEST_INSTALLATION_ID,
        testPaymentId,
        TEST_INVOICE_ID,
        VercelInternalEventType.MARKETPLACE_INVOICE_PAID);

    // Verify payment status was NOT updated to PAID due to validation failure
    Payment updatedPayment = paymentDao.findById(testPaymentId);
    assertNotNull(updatedPayment);
    assertEquals(
        Payment.Status.INVOICED,
        updatedPayment.getStatus(),
        "Payment status should remain INVOICED due to validation failure");
  }

  @Test
  public void testHandleMarketplaceInvoicePaidNotification_notPaidInvoiceState() {
    // Mock PaymentMethodClient to prevent gRPC calls during email sending
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getActivePaymentMethod(any(ObjectId.class), any(Boolean.class));
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getMostRecentBeforeOrEqualToDate(any(ObjectId.class), any());

    // Mock RefundQueryingClient to prevent gRPC calls during email sending
    doReturn(Collections.emptyList())
        .when(refundQueryingClient)
        .getRefundsByInvoiceId(any(ObjectId.class));

    // Arrange - Mock API to return invoice with NOTPAID state instead of PAID
    VercelInvoice notPaidInvoice =
        testVercelInvoice.toBuilder().state(VercelInvoiceState.NOTPAID).build();
    when(vercelMarketplaceApiClient.getInvoice(
            eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class)))
        .thenReturn(Mono.just(notPaidInvoice));

    MarketplaceInvoicePaidPayload payload =
        MarketplaceInvoicePaidPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("123.45"))
            .build();

    MarketplaceInvoicePaidNotification notification =
        new MarketplaceInvoicePaidNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert - Verify that the handler throws an error due to invalid invoice state
    StepVerifier.create(handler.handle(notification))
        .expectErrorMatches(
            throwable ->
                throwable instanceof IllegalStateException
                    && throwable.getMessage().equals("Invoice state is not PAID"))
        .verify();

    // Verify that the invoice document was still updated with the fetched invoice
    List<VercelInvoiceDocument> invoiceDocuments =
        vercelInvoiceDocumentSvc.findByInstallationId(TEST_INSTALLATION_ID);
    assertEquals(1, invoiceDocuments.size(), "Should have one invoice document");

    VercelInvoiceDocument updatedDocument = invoiceDocuments.get(0);
    assertNotNull(updatedDocument.invoice(), "Invoice document should have invoice data");
    assertEquals(
        VercelInvoiceState.NOTPAID,
        updatedDocument.invoice().state(),
        "Invoice document should reflect NOTPAID state");

    // Verify that the internal event was still published
    assertInvoiceEventCreated(
        TEST_INSTALLATION_ID,
        testPaymentId,
        TEST_INVOICE_ID,
        VercelInternalEventType.MARKETPLACE_INVOICE_PAID);

    // Verify payment status was NOT updated to PAID due to validation failure
    Payment updatedPayment = paymentDao.findById(testPaymentId);
    assertNotNull(updatedPayment);
    assertEquals(
        Payment.Status.INVOICED,
        updatedPayment.getStatus(),
        "Payment status should remain INVOICED due to validation failure");
  }

  @Test
  public void testHandleMarketplaceInvoiceRefundedNotification() {
    // Arrange - Set payment to PAID status first
    Payment paidPayment = testPayment.toBuilder().status(Payment.Status.PAID).build();
    paymentDao.save(paidPayment);

    // Create refunded invoice
    VercelInvoice refundedInvoice =
        testVercelInvoice.toBuilder()
            .state(VercelInvoiceState.REFUNDED)
            .refundReason("Customer requested refund")
            .refundTotal(new BigDecimal("50.00"))
            .build();
    when(vercelMarketplaceApiClient.getInvoice(
            eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class)))
        .thenReturn(Mono.just(refundedInvoice));

    // Create test refund with Vercel context
    ObjectId testRefundId = ObjectId.get();
    Refund testRefund =
        new Refund.Builder()
            .id(testRefundId)
            .orgId(testOrganization.getId())
            .invoiceId(ObjectId.get())
            .paymentId(testPaymentId)
            .created(new Date())
            .reason("Test refund for webhook")
            .amountCents(5000L)
            .amountTaxCents(500L)
            .revRecReason(RevenueRefundReason.BILLING_CORRECTION)
            .vercel(
                VercelPaymentContext.builder()
                    .invoiceId(TEST_INVOICE_ID)
                    .refundRequestedAt(STATIC_NOW.minusDays(1))
                    .build())
            .build();
    refundDao.save(testRefund);

    MarketplaceInvoiceRefundedPayload payload =
        MarketplaceInvoiceRefundedPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .amount(new BigDecimal("50.00"))
            .reason("Customer requested refund")
            .build();

    MarketplaceInvoiceRefundedNotification notification =
        new MarketplaceInvoiceRefundedNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert
    StepVerifier.create(handler.handle(notification)).verifyComplete();

    // Verify payment status was updated to REFUNDED
    Payment updatedPayment = paymentDao.findById(testPaymentId);
    assertNotNull(updatedPayment);
    assertEquals(Payment.Status.REFUNDED, updatedPayment.getStatus());

    // Verify payment vercel.refundConfirmedAt was updated
    assertNotNull(updatedPayment.getVercel(), "Payment should have Vercel context");
    assertNotNull(
        updatedPayment.getVercel().getRefundConfirmedAt(),
        "Payment vercel.refundConfirmedAt should be set");
    assertEquals(
        testCreatedAt.toLocalDateTime(),
        updatedPayment.getVercel().getRefundConfirmedAt(),
        "Payment refundConfirmedAt should match notification createdAt");

    // Verify refund vercel.refundConfirmedAt was updated
    Refund updatedRefund = refundDao.findById(testRefundId);
    assertNotNull(updatedRefund, "Refund should exist");
    assertNotNull(updatedRefund.getVercel(), "Refund should have Vercel context");
    assertNotNull(
        updatedRefund.getVercel().getRefundConfirmedAt(),
        "Refund vercel.refundConfirmedAt should be set");
    assertEquals(
        testCreatedAt.toLocalDateTime(),
        updatedRefund.getVercel().getRefundConfirmedAt(),
        "Refund refundConfirmedAt should match notification createdAt");

    // Verify refundRequestedAt was preserved
    assertNotNull(
        updatedRefund.getVercel().getRefundRequestedAt(),
        "Refund vercel.refundRequestedAt should be preserved");

    // Verify that the VercelMarketplaceApiClient.getInvoice was called
    verify(vercelMarketplaceApiClient, atLeastOnce())
        .getInvoice(eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class));

    // Verify that the invoice document was updated with the fetched invoice
    List<VercelInvoiceDocument> invoiceDocuments =
        vercelInvoiceDocumentSvc.findByInstallationId(TEST_INSTALLATION_ID);
    assertEquals(1, invoiceDocuments.size(), "Should have one invoice document");

    VercelInvoiceDocument updatedDocument = invoiceDocuments.get(0);
    assertNotNull(updatedDocument.invoice(), "Invoice document should have invoice data");
    assertEquals(
        VercelInvoiceState.REFUNDED,
        updatedDocument.invoice().state(),
        "Invoice document should reflect REFUNDED state");
    assertEquals(
        "Customer requested refund",
        updatedDocument.invoice().refundReason(),
        "Invoice document should contain refund reason");
    assertEquals(
        new BigDecimal("50.00"),
        updatedDocument.invoice().refundTotal(),
        "Invoice document should contain refund total");

    // Verify that the internal event was published
    assertInvoiceEventCreated(
        TEST_INSTALLATION_ID,
        testPaymentId,
        TEST_INVOICE_ID,
        VercelInternalEventType.MARKETPLACE_INVOICE_REFUNDED);
  }

  @Test
  public void testHandleMarketplaceInvoiceRefundedNotification_noMatchingRefund() {
    // Arrange - Set payment to PAID status first
    Payment paidPayment = testPayment.toBuilder().status(Payment.Status.PAID).build();
    paymentDao.save(paidPayment);

    // Create refunded invoice
    VercelInvoice refundedInvoice =
        testVercelInvoice.toBuilder()
            .state(VercelInvoiceState.REFUNDED)
            .refundReason("Customer requested refund")
            .refundTotal(new BigDecimal("50.00"))
            .build();
    when(vercelMarketplaceApiClient.getInvoice(
            eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class)))
        .thenReturn(Mono.just(refundedInvoice));

    // Create test refund with different payment ID (won't match)
    ObjectId differentPaymentId = ObjectId.get();
    Refund testRefund =
        new Refund.Builder()
            .id(ObjectId.get())
            .orgId(testOrganization.getId())
            .invoiceId(ObjectId.get())
            .paymentId(differentPaymentId)
            .created(new Date())
            .reason("Test refund with different payment ID")
            .amountCents(5000L)
            .amountTaxCents(500L)
            .revRecReason(RevenueRefundReason.BILLING_CORRECTION)
            .vercel(
                VercelPaymentContext.builder()
                    .invoiceId(TEST_INVOICE_ID)
                    .refundRequestedAt(STATIC_NOW.minusDays(1))
                    .build())
            .build();
    refundDao.save(testRefund);

    MarketplaceInvoiceRefundedPayload payload =
        MarketplaceInvoiceRefundedPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .amount(new BigDecimal("50.00"))
            .reason("Customer requested refund")
            .build();

    MarketplaceInvoiceRefundedNotification notification =
        new MarketplaceInvoiceRefundedNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert
    StepVerifier.create(handler.handle(notification)).verifyComplete();

    // Verify payment status was updated to REFUNDED
    Payment updatedPayment = paymentDao.findById(testPaymentId);
    assertNotNull(updatedPayment);
    assertEquals(Payment.Status.REFUNDED, updatedPayment.getStatus());

    // Verify payment vercel.refundConfirmedAt was updated
    assertNotNull(updatedPayment.getVercel(), "Payment should have Vercel context");
    assertNotNull(
        updatedPayment.getVercel().getRefundConfirmedAt(),
        "Payment vercel.refundConfirmedAt should be set");

    // Verify refund was NOT updated (different payment ID)
    Refund unchangedRefund = refundDao.findById(testRefund.getId());
    assertNotNull(unchangedRefund, "Refund should exist");
    assertNotNull(unchangedRefund.getVercel(), "Refund should have Vercel context");
    assertEquals(
        differentPaymentId,
        unchangedRefund.getPaymentId(),
        "Refund should still have different payment ID");
    assertEquals(
        TEST_INVOICE_ID,
        unchangedRefund.getVercel().getInvoiceId(),
        "Refund should have same Vercel invoice ID");
    assertNull(
        unchangedRefund.getVercel().getRefundConfirmedAt(),
        "Refund vercel.refundConfirmedAt should NOT be set");

    // Verify that the VercelMarketplaceApiClient.getInvoice was called
    verify(vercelMarketplaceApiClient, atLeastOnce())
        .getInvoice(eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class));

    // Verify that the invoice document was updated with the fetched invoice
    List<VercelInvoiceDocument> invoiceDocuments =
        vercelInvoiceDocumentSvc.findByInstallationId(TEST_INSTALLATION_ID);
    assertEquals(1, invoiceDocuments.size(), "Should have one invoice document");

    VercelInvoiceDocument updatedDocument = invoiceDocuments.get(0);
    assertNotNull(updatedDocument.invoice(), "Invoice document should have invoice data");
    assertEquals(
        VercelInvoiceState.REFUNDED,
        updatedDocument.invoice().state(),
        "Invoice document should reflect REFUNDED state");
  }

  @Test
  public void testHandleInvoiceCreatedNotification_apiClientFailure() {
    // Arrange - Mock API client to fail
    when(vercelMarketplaceApiClient.getInvoice(
            eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class)))
        .thenReturn(Mono.error(new RuntimeException("API call failed")));

    MarketplaceInvoiceCreatedPayload payload =
        MarketplaceInvoiceCreatedPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("100.00"))
            .build();

    MarketplaceInvoiceCreatedNotification notification =
        MarketplaceInvoiceCreatedNotification.builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act & Assert - Should propagate the error from the API client
    StepVerifier.create(handler.handle(notification)).expectError(RuntimeException.class).verify();

    // Verify that the VercelMarketplaceApiClient.getInvoice was called
    verify(vercelMarketplaceApiClient, atLeastOnce())
        .getInvoice(eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class));
  }

  @Test
  public void testHandleInvoicePaidNotification_invoiceDocumentUpdateSuccess() {
    // Mock PaymentMethodClient to prevent gRPC calls during email sending
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getActivePaymentMethod(any(ObjectId.class), any(Boolean.class));
    doReturn(Optional.empty())
        .when(paymentMethodClient)
        .getMostRecentBeforeOrEqualToDate(any(ObjectId.class), any());

    // Mock RefundQueryingClient to prevent gRPC calls during email sending
    doReturn(Collections.emptyList())
        .when(refundQueryingClient)
        .getRefundsByInvoiceId(any(ObjectId.class));

    // Arrange - Create a more detailed invoice with additional fields
    VercelInvoice detailedInvoice =
        testVercelInvoice.toBuilder()
            .state(VercelInvoiceState.PAID)
            .invoiceNumber("INV-2025-001")
            .memo("Detailed test invoice")
            .build();

    when(vercelMarketplaceApiClient.getInvoice(
            eq(TEST_INSTALLATION_ID), eq(TEST_INVOICE_ID), any(BearerAccessToken.class)))
        .thenReturn(Mono.just(detailedInvoice));

    MarketplaceInvoicePaidPayload payload =
        MarketplaceInvoicePaidPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("156.78"))
            .build();

    MarketplaceInvoicePaidNotification notification =
        new MarketplaceInvoicePaidNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Act
    StepVerifier.create(handler.handle(notification)).verifyComplete();

    // Verify payment status was updated to PAID
    Payment updatedPayment = paymentDao.findById(testPaymentId);
    assertNotNull(updatedPayment);
    assertEquals(Payment.Status.PAID, updatedPayment.getStatus());

    // Verify payment amount paid was updated by PaymentProcessedSvc
    assertEquals(
        15678L, updatedPayment.getAmountPaidCents(), "Payment amount paid should be updated");

    // Verify invoice exists and PaymentProcessedSvc was called
    Invoice updatedInvoice = invoiceDao.findById(testInvoice.getId());
    assertNotNull(updatedInvoice, "Invoice should exist");
    // Note: In test environment with mocked external services, invoice status may remain PENDING
    // The important verification is that PaymentProcessedSvc.handleEvent() was called without
    // errors

    // Assert - Verify detailed invoice data was persisted
    List<VercelInvoiceDocument> invoiceDocuments =
        vercelInvoiceDocumentSvc.findByInstallationId(TEST_INSTALLATION_ID);
    assertEquals(1, invoiceDocuments.size(), "Should have one invoice document");

    VercelInvoiceDocument updatedDocument = invoiceDocuments.get(0);
    assertNotNull(updatedDocument.invoice(), "Invoice document should have invoice data");
    assertEquals(
        "INV-2025-001",
        updatedDocument.invoice().invoiceNumber(),
        "Invoice document should contain invoice number");
    assertEquals(
        "Detailed test invoice",
        updatedDocument.invoice().memo(),
        "Invoice document should contain memo");
    assertNotNull(updatedDocument.updatedAt(), "Invoice document should have updatedAt timestamp");
  }

  private void assertInvoiceEventCreated(
      String vercelInstallationId,
      ObjectId paymentId,
      String vercelInvoiceId,
      VercelInternalEventType eventType) {

    List<VercelInternalEvent> events =
        vercelInternalEventDao.findByVercelInstallationIdAndEventType(
            vercelInstallationId, eventType);
    assertFalse(
        events.isEmpty(),
        "No events found for installation "
            + vercelInstallationId
            + " and event type "
            + eventType);
    assertEquals(1, events.size(), "Expected exactly one event, but found " + events.size());

    VercelInternalEvent event = events.get(0);
    assertThat("Event type should match", event.getType(), is(eventType));
    assertThat(
        "Event source should match type's source", event.getSource(), is(eventType.getSource()));
    assertThat(
        "Vercel installation ID should match",
        event.getDetail().getVercelInstallationId(),
        is(vercelInstallationId));
    assertThat("Payment ID should match", event.getDetail().getPaymentId(), is(paymentId));
    assertThat(
        "Vercel invoice ID should match",
        event.getDetail().getVercelInvoiceId(),
        is(vercelInvoiceId));
    assertNotNull(event.getTime(), "Event should have a timestamp");
    assertNotNull(event.getId(), "Event should have an ID");
  }

  @Test
  public void testHandleMarketplaceInvoiceCreatedNotification_withInvoiceFinalizeEnabled() {
    // Arrange - enable invoice finalize feature flag (use doReturn for spy to avoid calling real
    // method)
    doReturn(true).when(vercelBillingConfig).isInvoiceFinalizeEnabled();

    MarketplaceInvoiceCreatedPayload payload =
        MarketplaceInvoiceCreatedPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("100.00"))
            .build();

    MarketplaceInvoiceCreatedNotification notification =
        MarketplaceInvoiceCreatedNotification.builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Pre-populate invoice on the document so updateInvoiceState can match by invoiceId
    vercelInvoiceDocumentSvc.updateInvoice(testPaymentId, testVercelInvoice);

    // Act & Assert
    StepVerifier.create(handler.handle(notification)).expectComplete().verify();

    // Verify that the invoice document state was updated directly without API call
    Optional<VercelInvoiceDocument> updatedDocument =
        vercelInvoiceDocumentSvc.findByInvoiceId(TEST_INVOICE_ID);
    assertThat("Invoice document should be found", updatedDocument.isPresent(), is(true));
    assertThat(
        "Invoice state should be INVOICED",
        updatedDocument.get().invoice().state(),
        is(VercelInvoiceState.INVOICED));
  }

  @Test
  public void testHandleMarketplaceInvoicePaidNotification_withInvoiceFinalizeEnabled() {
    // Arrange - enable invoice finalize feature flag (use doReturn for spy)
    doReturn(true).when(vercelBillingConfig).isInvoiceFinalizeEnabled();

    MarketplaceInvoicePaidPayload payload =
        MarketplaceInvoicePaidPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("100.00"))
            .build();

    MarketplaceInvoicePaidNotification notification =
        new MarketplaceInvoicePaidNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Pre-populate invoice on the document so updateInvoiceState can match by invoiceId
    vercelInvoiceDocumentSvc.updateInvoice(testPaymentId, testVercelInvoice);

    // Act & Assert
    StepVerifier.create(handler.handle(notification)).expectComplete().verify();

    // Verify that the invoice document state was updated directly without API call
    Optional<VercelInvoiceDocument> updatedDocument =
        vercelInvoiceDocumentSvc.findByInvoiceId(TEST_INVOICE_ID);
    assertThat("Invoice document should be found", updatedDocument.isPresent(), is(true));
    assertThat(
        "Invoice state should be PAID",
        updatedDocument.get().invoice().state(),
        is(VercelInvoiceState.PAID));

    // Verify that API client was NOT called when feature flag is enabled
    verify(vercelMarketplaceApiClient, never()).getInvoice(any(), any(), any());
  }

  @Test
  public void testHandleMarketplaceInvoiceNotPaidNotification_withInvoiceFinalizeEnabled() {
    // Arrange - enable invoice finalize feature flag (use doReturn for spy)
    doReturn(true).when(vercelBillingConfig).isInvoiceFinalizeEnabled();

    MarketplaceInvoiceNotPaidPayload payload =
        MarketplaceInvoiceNotPaidPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .invoiceDate(testCreatedAt.minusDays(1))
            .invoiceTotal(new BigDecimal("100.00"))
            .build();

    MarketplaceInvoiceNotPaidNotification notification =
        new MarketplaceInvoiceNotPaidNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Pre-populate invoice on the document so updateInvoiceState can match by invoiceId
    vercelInvoiceDocumentSvc.updateInvoice(testPaymentId, testVercelInvoice);

    // Act & Assert
    StepVerifier.create(handler.handle(notification)).expectComplete().verify();

    // Verify that the invoice document state was updated directly without API call
    Optional<VercelInvoiceDocument> updatedDocument =
        vercelInvoiceDocumentSvc.findByInvoiceId(TEST_INVOICE_ID);
    assertThat("Invoice document should be found", updatedDocument.isPresent(), is(true));
    assertThat(
        "Invoice state should be NOTPAID",
        updatedDocument.get().invoice().state(),
        is(VercelInvoiceState.NOTPAID));

    // Verify that API client was NOT called when feature flag is enabled
    verify(vercelMarketplaceApiClient, never()).getInvoice(any(), any(), any());
  }

  @Test
  public void testHandleMarketplaceInvoiceRefundedNotification_withInvoiceFinalizeEnabled() {
    // Arrange - enable invoice finalize feature flag (use doReturn for spy)
    doReturn(true).when(vercelBillingConfig).isInvoiceFinalizeEnabled();

    MarketplaceInvoiceRefundedPayload payload =
        MarketplaceInvoiceRefundedPayload.builder()
            .installationId(TEST_INSTALLATION_ID)
            .invoiceId(TEST_INVOICE_ID)
            .externalInvoiceId(testPaymentId.toString())
            .periodStart(testCreatedAt.minusDays(30))
            .periodEnd(testCreatedAt)
            .amount(new BigDecimal("50.00"))
            .reason("Customer requested refund")
            .build();

    MarketplaceInvoiceRefundedNotification notification =
        new MarketplaceInvoiceRefundedNotification.Builder()
            .id(ObjectId.get())
            .webhookId(TEST_WEBHOOK_ID)
            .createdAt(testCreatedAt)
            .region(TEST_REGION)
            .payload(payload)
            .build();

    // Pre-populate invoice on the document so updateInvoiceState can match by invoiceId
    vercelInvoiceDocumentSvc.updateInvoice(testPaymentId, testVercelInvoice);

    // Act & Assert
    StepVerifier.create(handler.handle(notification)).expectComplete().verify();

    // Verify that the invoice document state was updated directly without API call
    Optional<VercelInvoiceDocument> updatedDocument =
        vercelInvoiceDocumentSvc.findByInvoiceId(TEST_INVOICE_ID);
    assertThat("Invoice document should be found", updatedDocument.isPresent(), is(true));
    assertThat(
        "Invoice state should be REFUNDED",
        updatedDocument.get().invoice().state(),
        is(VercelInvoiceState.REFUNDED));

    // Verify that API client was NOT called when feature flag is enabled
    verify(vercelMarketplaceApiClient, never()).getInvoice(any(), any(), any());
  }
}
