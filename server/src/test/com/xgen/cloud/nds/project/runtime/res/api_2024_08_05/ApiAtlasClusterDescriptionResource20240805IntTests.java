package com.xgen.cloud.nds.project.runtime.res.api_2024_08_05;

import static com.xgen.cloud.nds.project.runtime.res.api_2024_10_23.ApiAtlasClusterDescriptionResource20241023.ASYMMETRIC_SHARDED_CLUSTER_EXAMPLE_20240805;
import static com.xgen.svc.mms.api.res.atlas.ApiAtlasBaseClusterDescriptionResourceIntTestUtils.createPlanCreditForGroup;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_CREATED;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.amazonaws.services.ec2.model.VolumeType;
import com.mongodb.BasicDBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.activity._private.dao.EventDao;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.access.EmployeeAccessGrantType;
import com.xgen.cloud.common.model._public.cluster.CipherSuiteTLS12;
import com.xgen.cloud.common.model._public.cluster.TlsCipherConfigMode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.security._public.tls.CipherSuite;
import com.xgen.cloud.common.security._public.tls.TLSVersion;
import com.xgen.cloud.common.view._public.base.ApiListView;
import com.xgen.cloud.common.view._public.base.ApiView;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit.Type;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.NDSAWSKMS;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureSubscription;
import com.xgen.cloud.nds.azure._public.model.NDSAzureKeyVault;
import com.xgen.cloud.nds.azure._public.model.SupportedAzureEnvironment;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzureKeyVaultEARPrivateEndpoint;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.CloudProviderPrivateEndpoint;
import com.xgen.cloud.nds.common._public.model.Limits;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.gcp._private.dao.GCPOrganizationDao;
import com.xgen.cloud.nds.gcp._public.model.GCPHardwareSpec;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.NDSGoogleCloudKMS;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionProcessArgsDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionUpdatesDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.AutoSharding;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgsUpdatable;
import com.xgen.cloud.nds.project._public.model.ConfigServerManagementMode;
import com.xgen.cloud.nds.project._public.model.FlexTenantMigrationState;
import com.xgen.cloud.nds.project._public.model.GeoSharding;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSSettings;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.resourcepolicy._public.model.Policy;
import com.xgen.cloud.nds.resourcepolicy._public.svc.AtlasResourcePolicySvc;
import com.xgen.cloud.nds.resourcepolicy.util.AtlasResourcePolicyTestUtil;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.cloud.nds.spothealthcheck.util.NDSClusterSpotHealthCheckTestUtil;
import com.xgen.cloud.nds.tenantupgrade._private.dao.TenantUpgradeStatusDao;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeStatus;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.OrgLimits;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.core.dao.base.BaseDao.StaleUpdateException;
import com.xgen.svc.mms.api.res.ApiBaseResourceTest;
import com.xgen.svc.mms.api.res.atlas.ApiAtlasBaseClusterDescriptionResourceIntTestUtils;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAWSInstanceSizeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAWSRegionNameView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAzureInstanceSizeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAzureKeyVaultView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasAzureRegionNameView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasBaseClusterDescriptionView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasBiConnectorView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasCloudProviderView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterAdvancedConfigurationView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasClusterDescriptionV15View;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasCustomZoneMappingsView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasEmployeeAccessGrantView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasGCPHardwareSpecView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasGCPInstanceSizeView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasGCPRegionNameView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasGeoShardingView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasLegacyClusterDescriptionView.Fields;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasManagedNamespacesView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasReplicationSpecView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasTenantHardwareSpecView;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasZoneMappingView;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasAWSHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasAzureHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescription20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescriptionProcessArgs20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasGCPHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasReplicationSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasTenantHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasTenantRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasAutoScalingV15View;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasComputeAutoScalingV15View;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasDiskGBAutoScalingView;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFlexClusterDescriptionConfig;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFreeClusterDescriptionConfig;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestServerlessClusterDescriptionConfig;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.JSONCompare;
import org.skyscreamer.jsonassert.JSONCompareMode;

@RunWith(GuiceTestRunner.class)
public class ApiAtlasClusterDescriptionResource20240805IntTests extends ApiBaseResourceTest {

  private static class TestClusterNames {
    private static final String BASIC_AWS_CLUSTER = "basicAWSCluster";

    private static final String TENANT_CLUSTER = "tenantCluster";

    private static final String SERVERLESS_CLUSTER = "serverlessCluster";
    private static final String M2_CLUSTER = "m2Cluster";
    private static final String CROSS_CLOUD_CLUSTER = "crossCloudCluster";
    private static final String NVME_CLUSTER = "nvmeCluster";
    private static final String ASYMMETRIC_CLUSTER_WITH_EMBEDDED_CONFIG =
        "asymmetricWithEmbeddedConfig";

    private static final String FLEX_CLUSTER = "flexCluster";
    private static final String FLEX_CLUSTER_MIGRATED_FROM_SERVERLESS =
        "flexClusterMigrationServerless";
    private static final String FLEX_CLUSTER_CREATED_FROM_SHIM_LOGIC =
        "flexClusterMigrationShimmed";
    private static final String FLEX_CLUSTER_MIGRATED_FROM_SHARED = "flexClusterMigrationShared";

    private static final String LEGACY_SHARDED_CLUSTER = "legacyShardedCluster";

    private static final String DELETED_CLUSTER_WITH_RETAINED_BACKUPS =
        "deletedClusterWithRetainedBackups";
  }

  private static final String BASE_URL = "/api/atlas/v2/groups/";

  private static final LocalDate V20240805_VERSION = LocalDate.of(2024, 8, 5);

  private static final ObjectId DEFAULT_GROUP = oid(118);

  private Group _group;
  private AppUser _adminUser;
  private String _adminUserApiKey;

  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private TenantUpgradeStatusDao _tenantUpgradeStatusDao;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private GCPOrganizationDao _gcpOrganizationDao;
  @Inject private AzureSubscriptionDao _azureSubscriptionDao;
  @Inject private MTMClusterDao _mtmClusterDao;
  @Inject private GroupDao _groupDao;
  @Inject private OrganizationDao _organizationDao;
  @Inject private ClusterDescriptionUpdatesDao _clusterDescriptionUpdatesDao;
  @Inject private NDSClusterSvc _ndsClusterSvc;
  @Inject private AutomationMongoDbVersionSvc _versionSvc;
  @Inject private FeatureFlagSvc _featureFlagSvc;
  @Inject private AppSettings _appSettings;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private EventDao _eventDao;
  @Inject private AtlasResourcePolicySvc _atlasResourcePolicySvc;
  @Inject private ClusterDescriptionProcessArgsDao _clusterDescriptionProcessArgsDao;
  @Inject private NDSClusterSpotHealthCheckTestUtil _healthCheckTestUtil;
  @Inject private PaymentMethodStubber stubber;
  @Inject private BackupJobDao _backupJobDao;

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();

    _ndsGroupSvc.ensureGroup(_group.getId());
    setUpClusters();
    _adminUser = MmsFactory.createApiUserWithRoleInGroup("adminUser", _group, Role.GROUP_OWNER);
    _adminUserApiKey = MmsFactory.generateUserApiKey(_adminUser.getId(), "apiKey").getKey();
    _awsAccountDao.saveReplicaSafe(NDSModelTestFactory.getFullyAvailableAWSAccount());
    _gcpOrganizationDao.save(
        new GCPOrganization(NDSModelTestFactory.getFullyAvailableGCPOrganization()));
    _azureSubscriptionDao.save(
        new AzureSubscription(NDSModelTestFactory.getFullyAvailableAzureSubscription()));
    _mtmClusterDao.saveMajority(
        NDSModelTestFactory.getSharedMTMCluster(
            "M240AWSUSEast1MTM",
            FreeInstanceSize.M2,
            AWSRegionName.US_EAST_1,
            NDSSettings.getNewSharedInstanceVersion(_appSettings)));
    _mtmClusterDao.saveMajority(NDSModelTestFactory.getFlexMTMCluster());
    stubber.stubPaymentMethod(_group.getOrgId(), true);

    // populate phased versions
    _versionSvc.invalidateVersionManifestCache();
    _versionSvc.autoUpdateDefaultVersions();
    _ndsClusterSvc.start();

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, "nds", "config.nds.groups");

    stubber.stubPaymentMethod(_groupDao.findById(oid(118)).getOrgId(), true);

    // Required for some inherited v1.5 tests
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    _mtmClusterDao.saveMajority(
        NDSModelTestFactory.getSharedMTMCluster(
            "DobbyIsAFreeElf",
            FreeInstanceSize.M0,
            GCPRegionName.WESTERN_EUROPE,
            NDSSettings.getNewSharedInstanceVersion(_appSettings)));

    FeatureFlagIntTestUtil.enableFeatureGlobally(
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);
  }

  private void setUpClusters() {
    // basic AWS replica set
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(),
            TestClusterNames.BASIC_AWS_CLUSTER,
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0))));

    // tenant cluster
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(),
            TestClusterNames.TENANT_CLUSTER,
            List.of(
                NDSModelTestFactory.getShardRegionConfigForFreeRegion(
                    AWSRegionName.US_EAST_1, 3, 0, 0, 0))));

    // serverless cluster
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getServerlessClusterDescription(
            new TestServerlessClusterDescriptionConfig()
                .setGroupId(_group.getId())
                .setClusterName(TestClusterNames.SERVERLESS_CLUSTER)
                .setHostUris(Collections.emptyList())));

    // tenant cluster
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig()
                .setGroupId(_group.getId())
                .setClusterName(TestClusterNames.M2_CLUSTER)
                .setInstanceSize(FreeInstanceSize.M2)));

    // cross-cloud cluster
    final List<RegionConfig> crossCloudRegionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AzureRegionName.US_EAST_2, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_WEST_1, RegionConfig.MAX_PRIORITY - 1, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                GCPRegionName.ASIA_EAST_2, RegionConfig.MAX_PRIORITY - 2, 2, 0, 0, 0));
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(), TestClusterNames.CROSS_CLOUD_CLUSTER, crossCloudRegionConfigs));

    final List<RegionConfig> nvmeRegionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 1));
    final ClusterDescription nvmeCD =
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(), TestClusterNames.NVME_CLUSTER, nvmeRegionConfigs);
    _clusterDescriptionDao.save(
        nvmeCD.copy().setDiskBackupEnabled(true).setDiskSizeGB(380).build());

    // cluster with retain backups
    final List<RegionConfig> retainBackupsRegionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0));
    ClusterDescription deletedWithRetainedBackupsCluster =
        NDSModelTestFactory.getDefaultClusterDescription(
                _group.getId(),
                TestClusterNames.DELETED_CLUSTER_WITH_RETAINED_BACKUPS,
                retainBackupsRegionConfigs)
            .copy()
            .setDiskBackupEnabled(true)
            .setDiskSizeGB(32.0)
            .build();
    _clusterDescriptionDao.save(deletedWithRetainedBackupsCluster);
    _clusterDescriptionDao.markDeleted(
        _group.getId(), TestClusterNames.DELETED_CLUSTER_WITH_RETAINED_BACKUPS, new Date());

    // Add a retained backup job for the deleted cluster
    var backupJobId =
        _backupJobDao.create(
            _group.getId(),
            TestClusterNames.DELETED_CLUSTER_WITH_RETAINED_BACKUPS,
            deletedWithRetainedBackupsCluster.getUniqueId(),
            BackupJob.ClusterType.REPLICA_SET,
            null,
            35,
            List.of(),
            true,
            new Date());
    _backupJobDao.markRetaining(backupJobId);
    _backupJobDao.updateRetainBackups(backupJobId, true);

    // asymmetric sharded cluster with embedded config
    _clusterDescriptionDao.save(
        new ShardedClusterDescription(
            NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithEmbeddedConfigServer(
                _group.getId(), TestClusterNames.ASYMMETRIC_CLUSTER_WITH_EMBEDDED_CONFIG)));

    // flex cluster made from shim logic
    final FlexTenantMigrationState state =
        FlexTenantMigrationState.builder()
            .setIsTenantCreatedFromApi(true)
            .setTenantApiCloudProvider(CloudProvider.FREE)
            .setTenantApiInstanceSize(FreeInstanceSize.M2)
            .build();
    _clusterDescriptionDao.save(
        new ClusterDescription(
                NDSModelTestFactory.getFlexClusterDescription(
                    new TestFlexClusterDescriptionConfig()
                        .setGroupId(_group.getId())
                        .setClusterName(TestClusterNames.FLEX_CLUSTER_CREATED_FROM_SHIM_LOGIC)))
            .copy()
            .setFlexTenantMigrationState(state)
            .build());

    // flex cluster migrated from shared
    final FlexTenantMigrationState state2 =
        FlexTenantMigrationState.builder()
            .setIsTenantCreatedFromApi(false)
            .setFormerCloudProvider(CloudProvider.FREE)
            .setFormerInstanceSize(FreeInstanceSize.M2)
            .build();
    _clusterDescriptionDao.save(
        new ClusterDescription(
                NDSModelTestFactory.getFlexClusterDescription(
                    new TestFlexClusterDescriptionConfig()
                        .setGroupId(_group.getId())
                        .setClusterName(TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED)))
            .copy()
            .setFlexTenantMigrationState(state2)
            .build());

    // flex cluster migrated from serverless
    final FlexTenantMigrationState serverlessState =
        FlexTenantMigrationState.builder()
            .setIsTenantCreatedFromApi(true)
            .setTenantApiCloudProvider(CloudProvider.SERVERLESS)
            .setTenantApiInstanceSize(ServerlessInstanceSize.SERVERLESS_V2)
            .build();
    _clusterDescriptionDao.save(
        new ClusterDescription(
                NDSModelTestFactory.getFlexClusterDescription(
                    new TestFlexClusterDescriptionConfig()
                        .setGroupId(_group.getId())
                        .setClusterName(TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SERVERLESS)))
            .copy()
            .setFlexTenantMigrationState(serverlessState)
            .build());

    // normal flex cluster
    _clusterDescriptionDao.save(
        new ClusterDescription(
            NDSModelTestFactory.getFlexClusterDescription(
                new TestFlexClusterDescriptionConfig()
                    .setGroupId(_group.getId())
                    .setClusterName(TestClusterNames.FLEX_CLUSTER))));
  }

  @Test
  public void testUpdateManagedNamespaces() throws Exception {
    final ClusterDescription geoSharded =
        new ShardedClusterDescription(
                NDSModelTestFactory.getAWSShardedClusterDescription(_group.getId(), "awsGeo"))
            .copy()
            .setClusterType(ClusterDescription.ClusterType.GEOSHARDED)
            .setMongoDBMajorVersion(VersionUtils.FOUR_FOUR_ZERO.getMajorVersionString())
            .setMongoDBVersion(VersionUtils.FOUR_FOUR_ZERO.getVersion())
            .build();
    testUpdateManagedNamespaces(geoSharded);
  }

  @Test
  public void testUpdateManagedNamespaces_CrossCloud() throws Exception {
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AWS, AWSRegionName.US_EAST_1, 7, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AZURE, AzureRegionName.US_EAST_2, 6, 1, 0, 0, 0));
    final ClusterDescription crossCloudCluster =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
                _group.getId(), "crossCloud", regionConfigs)
            .copy()
            .setDiskSizeGB(64)
            .setClusterType(ClusterDescription.ClusterType.GEOSHARDED)
            .setMongoDBMajorVersion(VersionUtils.FOUR_FOUR_ZERO.getMajorVersionString())
            .setMongoDBVersion(VersionUtils.FOUR_FOUR_ZERO.getVersion())
            .build();
    testUpdateManagedNamespaces(crossCloudCluster);
  }

  private void testUpdateManagedNamespaces(final ClusterDescription pClusterDescription)
      throws Exception {
    final ObjectId groupId = _group.getId();
    final String clusterName = pClusterDescription.getName();

    _clusterDescriptionDao.save(pClusterDescription);
    _ndsClusterSvc.splitReplicationSpecs(groupId, AuditInfoHelpers.fromSystem());

    assertEquals(
        2,
        _clusterDescriptionDao
            .findByName(groupId, clusterName)
            .get()
            .getReplicationSpecsWithShardData()
            .size());
    assertEquals(Optional.empty(), _clusterDescriptionUpdatesDao.findByName(groupId, clusterName));

    final String dataAccessAdminUsername = "dataAccessAdmin";
    final AppUser dataUser =
        MmsFactory.createApiUserWithRoleInGroup(
            dataAccessAdminUsername, _group, Role.GROUP_DATA_ACCESS_ADMIN);
    final String apiKey = MmsFactory.generateUserApiKey(dataUser.getId(), "apiKey").getKey();

    final AppUser readOnly =
        MmsFactory.createApiUserWithRoleInGroup(
            "readOnly", _group, Role.GROUP_DATA_ACCESS_READ_ONLY);
    final String readOnlyApiKey =
        MmsFactory.generateUserApiKey(readOnly.getId(), "apiKey").getKey();

    // Can add a managed namespace
    final JSONObject firstManagedNs = new JSONObject();
    firstManagedNs.put(ApiAtlasManagedNamespacesView.DB_FIELD, "food");
    firstManagedNs.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "fruit");
    firstManagedNs.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "name");

    // User without the right permissions are denied
    doDigestJsonPostWithApiVersion(
        BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
        firstManagedNs,
        HttpStatus.SC_UNAUTHORIZED,
        readOnly.getUsername(),
        readOnlyApiKey,
        V20240805_VERSION);

    final JSONObject updatedWithNamespace =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            firstManagedNs,
            HttpStatus.SC_OK,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    assertEquals(
        2,
        _clusterDescriptionDao
            .findByName(groupId, clusterName)
            .get()
            .getReplicationSpecsWithShardData()
            .size());
    assertEquals(
        2,
        _clusterDescriptionUpdatesDao
            .findByName(groupId, clusterName)
            .get()
            .getReplicationSpecsWithShardData()
            .size());

    final JSONArray oneNs =
        updatedWithNamespace.getJSONArray(ApiAtlasGeoShardingView.MANAGED_NAMESPACES_FIELD);
    assertEquals(1, oneNs.length());
    final JSONObject firstNs = oneNs.getJSONObject(0);
    assertEquals("food", firstNs.get(ApiAtlasManagedNamespacesView.DB_FIELD));
    assertEquals("fruit", firstNs.get(ApiAtlasManagedNamespacesView.COLLECTION_FIELD));
    assertEquals("name", firstNs.get(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD));
    assertFalse(firstNs.getBoolean(ApiAtlasManagedNamespacesView.IS_SHARD_KEY_UNIQUE_FIELD));
    assertFalse(firstNs.getBoolean(ApiAtlasManagedNamespacesView.IS_CUSTOM_SHARD_KEY_HASHED_FIELD));
    assertFalse(firstNs.getBoolean(ApiAtlasManagedNamespacesView.PRESPLIT_HASHED_ZONES_FIELD));
    assertEquals(
        JSONObject.NULL, firstNs.get(ApiAtlasManagedNamespacesView.NUM_INITIAL_CHUNKS_FIELD));

    // Cannot add a second managed namespace for the same db and collection
    final JSONObject duplicateManagedNs = new JSONObject();
    duplicateManagedNs.put(ApiAtlasManagedNamespacesView.DB_FIELD, "food");
    duplicateManagedNs.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "fruit");
    duplicateManagedNs.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "second");

    final JSONObject updatedWithDuplicateNs =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            duplicateManagedNs,
            HttpStatus.SC_BAD_REQUEST,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, updatedWithDuplicateNs.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "DUPLICATE_MANAGED_NAMESPACE", updatedWithDuplicateNs.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "Managed Namespace food.fruit already exists.",
        updatedWithDuplicateNs.getString(ApiError.DETAIL_FIELD));

    // Can add a second distinct managed namespace
    final JSONObject secondManagedNs = new JSONObject();
    secondManagedNs.put(ApiAtlasManagedNamespacesView.DB_FIELD, "music");
    secondManagedNs.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "rap");
    secondManagedNs.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "artist");

    final JSONObject updatedWithSecondNamespace =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            secondManagedNs,
            HttpStatus.SC_OK,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);

    assertEquals(
        2,
        _clusterDescriptionDao
            .findByName(groupId, clusterName)
            .get()
            .getReplicationSpecsWithShardData()
            .size());
    assertEquals(
        2,
        _clusterDescriptionUpdatesDao
            .findByName(groupId, clusterName)
            .get()
            .getReplicationSpecsWithShardData()
            .size());
    final JSONArray twoNs =
        updatedWithSecondNamespace.getJSONArray(ApiAtlasGeoShardingView.MANAGED_NAMESPACES_FIELD);
    assertEquals(2, twoNs.length());
    final JSONObject secondNs = twoNs.getJSONObject(1);
    assertEquals("music", secondNs.get(ApiAtlasManagedNamespacesView.DB_FIELD));
    assertEquals("rap", secondNs.get(ApiAtlasManagedNamespacesView.COLLECTION_FIELD));
    assertEquals("artist", secondNs.get(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD));
    assertFalse(secondNs.getBoolean(ApiAtlasManagedNamespacesView.IS_SHARD_KEY_UNIQUE_FIELD));
    assertFalse(
        secondNs.getBoolean(ApiAtlasManagedNamespacesView.IS_CUSTOM_SHARD_KEY_HASHED_FIELD));
    assertFalse(secondNs.getBoolean(ApiAtlasManagedNamespacesView.PRESPLIT_HASHED_ZONES_FIELD));
    assertEquals(
        JSONObject.NULL, secondNs.get(ApiAtlasManagedNamespacesView.NUM_INITIAL_CHUNKS_FIELD));

    // Can add a shard key with uniqueness constraint
    final JSONObject thirdManagedNs = new JSONObject();
    thirdManagedNs.put(ApiAtlasManagedNamespacesView.DB_FIELD, "testDB");
    thirdManagedNs.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "testCollection");
    thirdManagedNs.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "uniqueKey");
    thirdManagedNs.put(ApiAtlasManagedNamespacesView.IS_SHARD_KEY_UNIQUE_FIELD, true);

    final JSONObject updatedWithThirdNamespace =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            thirdManagedNs,
            HttpStatus.SC_OK,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    assertEquals(
        2,
        _clusterDescriptionDao
            .findByName(groupId, clusterName)
            .get()
            .getReplicationSpecsWithShardData()
            .size());
    assertEquals(
        2,
        _clusterDescriptionUpdatesDao
            .findByName(groupId, clusterName)
            .get()
            .getReplicationSpecsWithShardData()
            .size());
    final JSONArray threeNs =
        updatedWithThirdNamespace.getJSONArray(ApiAtlasGeoShardingView.MANAGED_NAMESPACES_FIELD);
    assertEquals(3, threeNs.length());
    final JSONObject thirdNs = threeNs.getJSONObject(2);
    assertEquals("testDB", thirdNs.get(ApiAtlasManagedNamespacesView.DB_FIELD));
    assertEquals("testCollection", thirdNs.get(ApiAtlasManagedNamespacesView.COLLECTION_FIELD));
    assertEquals("uniqueKey", thirdNs.get(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD));
    assertTrue(thirdNs.getBoolean(ApiAtlasManagedNamespacesView.IS_SHARD_KEY_UNIQUE_FIELD));
    assertFalse(thirdNs.getBoolean(ApiAtlasManagedNamespacesView.IS_CUSTOM_SHARD_KEY_HASHED_FIELD));
    assertFalse(thirdNs.getBoolean(ApiAtlasManagedNamespacesView.PRESPLIT_HASHED_ZONES_FIELD));
    assertEquals(
        JSONObject.NULL, thirdNs.get(ApiAtlasManagedNamespacesView.NUM_INITIAL_CHUNKS_FIELD));

    // Can add a shard key with hashed field
    final JSONObject fourthManagedNs = new JSONObject();
    fourthManagedNs.put(ApiAtlasManagedNamespacesView.DB_FIELD, "testDB");
    fourthManagedNs.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "testCollection1");
    fourthManagedNs.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "hashedKey");
    fourthManagedNs.put(ApiAtlasManagedNamespacesView.IS_CUSTOM_SHARD_KEY_HASHED_FIELD, true);

    final JSONObject updatedWithFourthNamespace =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            fourthManagedNs,
            HttpStatus.SC_OK,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    final JSONArray fourNs =
        updatedWithFourthNamespace.getJSONArray(ApiAtlasGeoShardingView.MANAGED_NAMESPACES_FIELD);
    assertEquals(4, fourNs.length());
    final JSONObject fourthNs = fourNs.getJSONObject(3);
    assertEquals("testDB", fourthNs.get(ApiAtlasManagedNamespacesView.DB_FIELD));
    assertEquals("testCollection1", fourthNs.get(ApiAtlasManagedNamespacesView.COLLECTION_FIELD));
    assertEquals("hashedKey", fourthNs.get(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD));
    assertFalse(fourthNs.getBoolean(ApiAtlasManagedNamespacesView.IS_SHARD_KEY_UNIQUE_FIELD));
    assertTrue(fourthNs.getBoolean(ApiAtlasManagedNamespacesView.IS_CUSTOM_SHARD_KEY_HASHED_FIELD));
    assertFalse(fourthNs.getBoolean(ApiAtlasManagedNamespacesView.PRESPLIT_HASHED_ZONES_FIELD));
    assertEquals(
        JSONObject.NULL, fourthNs.get(ApiAtlasManagedNamespacesView.NUM_INITIAL_CHUNKS_FIELD));

    // Can add a shard key with all hashed sharding options
    final JSONObject fifthManagedNs = new JSONObject();
    fifthManagedNs.put(ApiAtlasManagedNamespacesView.DB_FIELD, "testDB");
    fifthManagedNs.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "testCollection2");
    fifthManagedNs.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "hashedKey");
    fifthManagedNs.put(ApiAtlasManagedNamespacesView.IS_CUSTOM_SHARD_KEY_HASHED_FIELD, true);
    fifthManagedNs.put(ApiAtlasManagedNamespacesView.PRESPLIT_HASHED_ZONES_FIELD, true);
    fifthManagedNs.put(ApiAtlasManagedNamespacesView.NUM_INITIAL_CHUNKS_FIELD, 2L);

    final JSONObject updatedWithFifthNamespace =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            fifthManagedNs,
            HttpStatus.SC_OK,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    final JSONArray fiveNs =
        updatedWithFifthNamespace.getJSONArray(ApiAtlasGeoShardingView.MANAGED_NAMESPACES_FIELD);
    assertEquals(5, fiveNs.length());
    final JSONObject fifthNs = fiveNs.getJSONObject(4);
    assertEquals("testDB", fifthNs.get(ApiAtlasManagedNamespacesView.DB_FIELD));
    assertEquals("testCollection2", fifthNs.get(ApiAtlasManagedNamespacesView.COLLECTION_FIELD));
    assertEquals("hashedKey", fifthNs.get(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD));
    assertFalse(fifthNs.getBoolean(ApiAtlasManagedNamespacesView.IS_SHARD_KEY_UNIQUE_FIELD));
    assertTrue(fifthNs.getBoolean(ApiAtlasManagedNamespacesView.IS_CUSTOM_SHARD_KEY_HASHED_FIELD));
    assertTrue(fifthNs.getBoolean(ApiAtlasManagedNamespacesView.PRESPLIT_HASHED_ZONES_FIELD));
    assertEquals(2L, fifthNs.getLong(ApiAtlasManagedNamespacesView.NUM_INITIAL_CHUNKS_FIELD));

    // Get an error if request body is incorrectly formatted
    final JSONObject badRequest = new JSONObject();
    badRequest.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "artist");

    final JSONObject badRequestResponse =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            badRequest,
            HttpStatus.SC_BAD_REQUEST,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, badRequestResponse.getInt(ApiError.ERROR_FIELD));
    assertEquals("MISSING_ATTRIBUTES", badRequestResponse.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "The required attributes [db, collection] were not specified.",
        badRequestResponse.getString(ApiError.DETAIL_FIELD));

    // Get an error if any field is empty
    final JSONObject emptyDbRequest = new JSONObject();
    emptyDbRequest.put(ApiAtlasManagedNamespacesView.DB_FIELD, "");
    emptyDbRequest.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "rap");
    emptyDbRequest.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "artist");

    final JSONObject emptyDbRequestResponse =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            emptyDbRequest,
            HttpStatus.SC_BAD_REQUEST,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, emptyDbRequestResponse.getInt(ApiError.ERROR_FIELD));
    assertEquals("MISSING_ATTRIBUTES", emptyDbRequestResponse.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "The required attributes [db] were not specified.",
        emptyDbRequestResponse.getString(ApiError.DETAIL_FIELD));

    // Get an error if shard key is invalid (shard key specified with uniqueness constraint and has
    // hashed fields)
    final JSONObject nsWithInvalidShardKey = new JSONObject();
    nsWithInvalidShardKey.put(ApiAtlasManagedNamespacesView.DB_FIELD, "testDB");
    nsWithInvalidShardKey.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "testCollection3");
    nsWithInvalidShardKey.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "testKey");
    nsWithInvalidShardKey.put(ApiAtlasManagedNamespacesView.IS_SHARD_KEY_UNIQUE_FIELD, true);
    nsWithInvalidShardKey.put(ApiAtlasManagedNamespacesView.IS_CUSTOM_SHARD_KEY_HASHED_FIELD, true);

    final JSONObject invalidShardKeyResponse =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            nsWithInvalidShardKey,
            HttpStatus.SC_BAD_REQUEST,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, invalidShardKeyResponse.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "INVALID_SHARD_KEY_CONFIGURATION",
        invalidShardKeyResponse.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "Invalid shard key configuration: You cannot specify a unique constraint on a shard key"
            + " with a hashed index.",
        invalidShardKeyResponse.getString(ApiError.DETAIL_FIELD));

    // Get an error if hashed shard key requested from a cluster with incompatible MongoDB version
    // (<4.4)
    final String cluster42Name = clusterName + "42";
    final ClusterDescription incompatibleCluster =
        pClusterDescription
            .copy()
            .setName(cluster42Name)
            .setClusterNamePrefix(cluster42Name)
            .setMongoDBVersion(VersionUtils.FOUR_TWO_ZERO.getVersion())
            .setMongoDBMajorVersion(VersionUtils.FOUR_TWO_ZERO.getMajorVersionString())
            .build();
    _clusterDescriptionDao.save(incompatibleCluster);
    _ndsClusterSvc.splitReplicationSpecs(groupId, AuditInfoHelpers.fromSystem());

    final JSONObject createIncompatibleClusterResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + groupId + "/clusters/" + cluster42Name,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        "4.2.0",
        createIncompatibleClusterResponse.getString(
            ApiAtlasBaseClusterDescriptionView.Fields.MONGODB_VERSION_FIELD));

    final JSONObject nsWithIncompatibleVersion = new JSONObject();
    nsWithIncompatibleVersion.put(ApiAtlasManagedNamespacesView.DB_FIELD, "testDB");
    nsWithIncompatibleVersion.put(
        ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "testCollection4");
    nsWithIncompatibleVersion.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "testKey");
    nsWithIncompatibleVersion.put(
        ApiAtlasManagedNamespacesView.IS_CUSTOM_SHARD_KEY_HASHED_FIELD, true);

    final JSONObject nsWithIncompatibleVersionResponse =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + cluster42Name + "/globalWrites/managedNamespaces",
            nsWithIncompatibleVersion,
            HttpStatus.SC_BAD_REQUEST,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    assertEquals(
        HttpStatus.SC_BAD_REQUEST, nsWithIncompatibleVersionResponse.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "INVALID_SHARD_KEY_CONFIGURATION",
        nsWithIncompatibleVersionResponse.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "Invalid shard key configuration: Compound hashed indexes are only supported in MongoDB"
            + " versions 4.4 and above.",
        nsWithIncompatibleVersionResponse.getString(ApiError.DETAIL_FIELD));

    // Can remove a managed namespace
    final JSONObject removedNamespace =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + groupId
                + "/clusters/"
                + clusterName
                + "/globalWrites/managedNamespaces?db=food&collection=fruit",
            HttpStatus.SC_OK,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    final JSONArray nsAfterRemove =
        removedNamespace.getJSONArray(ApiAtlasGeoShardingView.MANAGED_NAMESPACES_FIELD);
    assertEquals(4, nsAfterRemove.length());
    final JSONObject afterRemove = nsAfterRemove.getJSONObject(0);
    assertEquals("music", afterRemove.get(ApiAtlasManagedNamespacesView.DB_FIELD));
    assertEquals("rap", afterRemove.get(ApiAtlasManagedNamespacesView.COLLECTION_FIELD));
    assertEquals("artist", afterRemove.get(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD));

    final Set<String> zoneIds =
        _ndsClusterSvc
            .getMergedClusterDescription(groupId, cluster42Name)
            .get()
            .getReplicationSpecsWithShardData()
            .stream()
            .map(replicationSpec -> replicationSpec.getId().toHexString())
            .collect(Collectors.toSet());
    final Set<String> rsIds =
        _ndsClusterSvc
            .getMergedClusterDescription(groupId, cluster42Name)
            .get()
            .getReplicationSpecsWithShardData()
            .stream()
            .map(replicationSpec -> replicationSpec.getZoneId().toHexString())
            .collect(Collectors.toSet());

    {
      final JSONObject mapping =
          removedNamespace.getJSONObject(ApiAtlasGeoShardingView.CUSTOM_ZONE_MAPPING_FIELD);
      mapping
          .keys()
          .forEachRemaining(
              key -> {
                if (mapping.get(key) != null) {
                  assertTrue(zoneIds.contains(mapping.get(key)));
                  assertFalse(rsIds.contains(mapping.get(key)));
                }
              });
    }

    // Get an error if managed namespace remove request not formatted properly
    final JSONObject badRemoveRequest =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + groupId
                + "/clusters/"
                + clusterName
                + "/globalWrites/managedNamespaces?db=food",
            HttpStatus.SC_BAD_REQUEST,
            dataAccessAdminUsername,
            apiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, badRemoveRequest.getInt(ApiError.ERROR_FIELD));
    assertEquals("MISSING_QUERY_PARAMETER", badRemoveRequest.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "The required query parameter collection was not specified.",
        badRemoveRequest.getString(ApiError.DETAIL_FIELD));
  }

  @Test
  public void testCreateCluster_openApiExample() {
    doDigestJsonPostWithApiVersion(
        BASE_URL + oid(118) + "/clusters",
        new JSONObject(ASYMMETRIC_SHARDED_CLUSTER_EXAMPLE_20240805),
        SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V20240805_VERSION);
  }

  @Test
  public void testCreateCluster_zones_errors() {
    final ObjectId groupId = oid(118);
    JSONObject cannotHaveZones = createDefaultGlobalClusterView(NDSDefaults.CLUSTER_NAME, groupId);

    cannotHaveZones.put(Fields.CLUSTER_TYPE_FIELD, "REPLICASET");

    final JSONObject createResponse_invalidClusterTypeRSForZones =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters",
            cannotHaveZones,
            SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.INVALID_CLUSTER_CONFIGURATION.name(),
        createResponse_invalidClusterTypeRSForZones.getString("errorCode"));

    cannotHaveZones.put(Fields.CLUSTER_TYPE_FIELD, "SHARDED");

    final JSONObject createResponse_invalidClusterTypeShardedForZones =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters",
            cannotHaveZones,
            SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.INVALID_CLUSTER_CONFIGURATION.name(),
        createResponse_invalidClusterTypeShardedForZones.getString("errorCode"));

    cannotHaveZones.put(Fields.CLUSTER_TYPE_FIELD, "GEOSHARDED");

    doDigestJsonPostWithApiVersion(
        BASE_URL + groupId + "/clusters/",
        cannotHaveZones,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V20240805_VERSION);
  }

  @Test
  public void createCluster_duplicateRegionConfig() {
    final ObjectId groupId = _group.getId();

    final JSONObject invalidRegionConfigCluster =
        createDefaultSingleRegionReplicaSetView(
            "Cluster0", groupId, "M30", CloudProvider.AWS.name(), AWSRegionName.US_EAST_1.name());
    final List<JSONObject> regionConfigs =
        List.of(
            new JSONObject()
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                    new JSONObject()
                        .put(ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M30")
                        .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 2))
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD,
                    CloudProvider.AWS.name())
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD,
                    AWSRegionName.US_EAST_1.name())
                .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7),
            new JSONObject()
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                    new JSONObject()
                        .put(ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M30")
                        .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 1))
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD,
                    CloudProvider.AWS.name())
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD,
                    AWSRegionName.US_EAST_1.name())
                .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 6));

    final JSONObject replicationSpec =
        (JSONObject)
            invalidRegionConfigCluster
                .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                .get(0);
    replicationSpec.put(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD, regionConfigs);

    final JSONObject invalidRegionConfigResponse =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters",
            invalidRegionConfigCluster,
            SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.DUPLICATE_REGION_CONFIGS.name(),
        invalidRegionConfigResponse.getString("errorCode"));
  }

  @Test
  public void updateCluster_duplicateRegionConfig() {
    final ObjectId groupId = _group.getId();
    final String clusterName = "Cluster0";

    final JSONObject clusterView =
        createDefaultSingleRegionReplicaSetView(
            clusterName, groupId, "M30", CloudProvider.AWS.name(), AWSRegionName.US_EAST_1.name());
    doDigestJsonPostWithApiVersion(
        BASE_URL + groupId + "/clusters",
        clusterView,
        SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    final List<JSONObject> regionConfigs =
        List.of(
            new JSONObject()
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                    new JSONObject()
                        .put(ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M30")
                        .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 2))
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD,
                    CloudProvider.AWS.name())
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD,
                    AWSRegionName.US_EAST_1.name())
                .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7),
            new JSONObject()
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                    new JSONObject()
                        .put(ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M30")
                        .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 1))
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD,
                    CloudProvider.AWS.name())
                .put(
                    ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD,
                    AWSRegionName.US_EAST_1.name())
                .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 6));

    final JSONObject replicationSpec =
        (JSONObject)
            clusterView
                .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                .get(0);
    replicationSpec.put(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD, regionConfigs);

    final JSONObject invalidRegionConfigResponse =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName,
            clusterView,
            SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.DUPLICATE_REGION_CONFIGS.name(),
        invalidRegionConfigResponse.getString("errorCode"));
  }

  @Test
  public void testGlobalWritesSelfManaged_FFOFF() throws Exception {
    final ObjectId groupId = _group.getId();
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AWS, AWSRegionName.US_EAST_1, 7, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AZURE, AzureRegionName.US_EAST_2, 6, 1, 0, 0, 0));
    final ClusterDescription selfManagedCluster =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
                _group.getId(), "crossCloud", regionConfigs)
            .copy()
            .setDiskSizeGB(64)
            .setClusterType(ClusterDescription.ClusterType.GEOSHARDED)
            .setMongoDBMajorVersion(VersionUtils.FOUR_FOUR_ZERO.getMajorVersionString())
            .setMongoDBVersion(VersionUtils.FOUR_FOUR_ZERO.getVersion())
            .setGeoSharding(new GeoSharding(Map.of(), List.of(), true))
            .build();
    final String clusterName = selfManagedCluster.getName();

    _clusterDescriptionDao.save(selfManagedCluster);

    // Cannot add a managed namespace
    final JSONObject firstManagedNs = new JSONObject();
    firstManagedNs.put(ApiAtlasManagedNamespacesView.DB_FIELD, "food");
    firstManagedNs.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "fruit");
    firstManagedNs.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "name");

    final JSONObject updatedWithNamespace =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            firstManagedNs,
            HttpStatus.SC_INTERNAL_SERVER_ERROR,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals("SERVICE_UNAVAILABLE", updatedWithNamespace.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals("SERVICE_MAINTENANCE", updatedWithNamespace.getString(ApiError.REASON_FIELD));
  }

  @Test
  public void testGlobalWritesSelfManaged_FFOn() throws Exception {
    final ObjectId groupId = _group.getId();
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AWS, AWSRegionName.US_EAST_1, 7, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AZURE, AzureRegionName.US_EAST_2, 6, 1, 0, 0, 0));
    ClusterDescription selfManagedCluster =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
                _group.getId(), "crossCloud", regionConfigs)
            .copy()
            .setDiskSizeGB(64)
            .setClusterType(ClusterDescription.ClusterType.GEOSHARDED)
            .setMongoDBMajorVersion(VersionUtils.FOUR_FOUR_ZERO.getMajorVersionString())
            .setMongoDBVersion(VersionUtils.FOUR_FOUR_ZERO.getVersion())
            .setGeoSharding(new GeoSharding(Map.of(), List.of(), true))
            .build();
    selfManagedCluster =
        selfManagedCluster
            .copy()
            .setReplicationSpecList(
                List.of(
                    selfManagedCluster
                        .getReplicationSpecsWithShardData()
                        .get(0)
                        .copy()
                        .setNumShards(1)
                        .build(),
                    selfManagedCluster
                        .getReplicationSpecsWithShardData()
                        .get(0)
                        .copy()
                        .setNumShards(1)
                        .setReplicationSpecId(new ObjectId())
                        .setExternalId(new ObjectId())
                        .build()))
            .build();
    final String clusterName = selfManagedCluster.getName();

    _clusterDescriptionDao.save(selfManagedCluster);

    // Cannot add a managed namespace
    final JSONObject firstManagedNs = new JSONObject();
    firstManagedNs.put(ApiAtlasManagedNamespacesView.DB_FIELD, "food");
    firstManagedNs.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "fruit");
    firstManagedNs.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "name");

    final JSONObject updatedWithNamespace =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/managedNamespaces",
            firstManagedNs,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE",
        updatedWithNamespace.getString(ApiError.ERROR_CODE_FIELD));

    final JSONObject customMapping = new JSONObject();
    customMapping.put(ApiAtlasZoneMappingView.LOCATION_FIELD, "CA");
    customMapping.put(ApiAtlasZoneMappingView.ZONE_FIELD, "Zone 1");
    final JSONArray customMappingList = new JSONArray();
    customMappingList.put(customMapping);
    final JSONObject customMappingView = new JSONObject();
    customMappingView.put(
        ApiAtlasCustomZoneMappingsView.CUSTOM_ZONE_MAPPINGS_FIELD, customMappingList);

    // Cannot add zone mapping
    final JSONObject updateWithZoneMapping =
        doDigestJsonPostWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/globalWrites/customZoneMapping",
            customMappingView,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "INVALID_SELF_MANAGED_GLOBAL_CLUSTER_STATE",
        updateWithZoneMapping.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetGeoSharding_Serverless() {
    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature disabled
    final JSONObject resp1 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/serverlessCluster/globalWrites",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp1.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);

    final JSONObject resp2 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/serverlessCluster/globalWrites",
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API",
        resp2.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testAddManagedNamespaceServerless() {
    final JSONObject managedNamespaces = new JSONObject();
    managedNamespaces.put(ApiAtlasManagedNamespacesView.DB_FIELD, "foo");
    managedNamespaces.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "bar");
    managedNamespaces.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "foobar");

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature disabled
    final JSONObject resp1 =
        doDigestJsonPostWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/serverlessCluster/globalWrites/managedNamespaces",
            managedNamespaces,
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp1.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);

    final JSONObject resp2 =
        doDigestJsonPostWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/serverlessCluster/globalWrites/managedNamespaces",
            managedNamespaces,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API",
        resp2.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testRemoveManagedNamespaceServerless() {

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature disabled
    final JSONObject resp1 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/serverlessCluster/globalWrites/managedNamespaces?db=foo&collection=bar",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp1.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);

    final JSONObject resp2 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/serverlessCluster/globalWrites/managedNamespaces?db=foo&collection=bar",
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API",
        resp2.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testAddCustomZoneMappingsServerless() {
    final JSONObject customMapping = new JSONObject();
    customMapping.put(ApiAtlasZoneMappingView.LOCATION_FIELD, "CA");
    customMapping.put(ApiAtlasZoneMappingView.ZONE_FIELD, "Zone 2");
    final JSONArray customMappingList = new JSONArray();
    customMappingList.put(customMapping);
    final JSONObject customMappingView = new JSONObject();
    customMappingView.put(
        ApiAtlasCustomZoneMappingsView.CUSTOM_ZONE_MAPPINGS_FIELD, customMappingList);

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature disabled
    final JSONObject resp1 =
        doDigestJsonPostWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/serverlessCluster/globalWrites/customZoneMapping",
            customMappingView,
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp1.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);

    final JSONObject resp2 =
        doDigestJsonPostWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/serverlessCluster/globalWrites/customZoneMapping",
            customMappingView,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API",
        resp2.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testRemoveCustomZoneMappingsServerless() {
    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature disabled
    final JSONObject resp1 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/serverlessCluster/globalWrites/customZoneMapping",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp1.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);

    final JSONObject resp2 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/serverlessCluster/globalWrites/customZoneMapping",
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API",
        resp2.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetCluster_20240805_schema_details() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));
    final ClusterDescription asymmetricWithEmbeddedConfig =
        _clusterDescriptionDao
            .findByName(_group.getId(), TestClusterNames.ASYMMETRIC_CLUSTER_WITH_EMBEDDED_CONFIG)
            .get();
    final JSONObject resp_asymmetricWithEmbeddedConfig =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.ASYMMETRIC_CLUSTER_WITH_EMBEDDED_CONFIG,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        asymmetricWithEmbeddedConfig.getUniqueId().toString(),
        resp_asymmetricWithEmbeddedConfig.getString(
            ApiAtlasClusterDescription20240805View.ID_FIELD));

    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());

    assertEquals(
        List.of(),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    // replication spec ID is based on replicationSpec.externalId
    IntStream.range(0, asymmetricWithEmbeddedConfig.getReplicationSpecsWithShardData().size())
        .forEach(
            i -> {
              assertEquals(
                  asymmetricWithEmbeddedConfig
                      .getReplicationSpecsWithShardData()
                      .get(i)
                      .getExternalId()
                      .toString(),
                  resp_asymmetricWithEmbeddedConfig
                      .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                      .getJSONObject(i)
                      .getString(ApiAtlasReplicationSpec20240805View.ID_FIELD));
            });

    // zoneId is exposed
    IntStream.range(0, asymmetricWithEmbeddedConfig.getReplicationSpecsWithShardData().size())
        .forEach(
            i -> {
              assertEquals(
                  asymmetricWithEmbeddedConfig
                      .getReplicationSpecsWithShardData()
                      .get(i)
                      .getZoneId()
                      .toString(),
                  resp_asymmetricWithEmbeddedConfig
                      .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                      .getJSONObject(i)
                      .getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
            });

    // numShards is not present on replicationSpecs
    IntStream.range(
            0,
            resp_asymmetricWithEmbeddedConfig
                .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                .length())
        .forEach(
            i -> {
              assertFalse(
                  resp_asymmetricWithEmbeddedConfig
                      .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                      .getJSONObject(i)
                      .has(ApiAtlasReplicationSpecView.NUM_SHARDS_FIELD));
            });

    // diskSizeGB is present on each hardware spec
    final double expectedDiskSizeGB = asymmetricWithEmbeddedConfig.getDiskSizeGB();
    IntStream.range(
            0,
            resp_asymmetricWithEmbeddedConfig
                .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                .length())
        .forEach(
            i -> {
              final JSONArray regionConfigs =
                  resp_asymmetricWithEmbeddedConfig
                      .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                      .getJSONObject(i)
                      .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD);

              IntStream.range(0, regionConfigs.length())
                  .forEach(
                      j -> {
                        final JSONObject regionConfig = regionConfigs.getJSONObject(j);
                        assertEquals(
                            expectedDiskSizeGB,
                            regionConfig
                                .getJSONObject(
                                    ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
                                .getDouble(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
                            0);
                        assertEquals(
                            expectedDiskSizeGB,
                            regionConfig
                                .getJSONObject(
                                    ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD)
                                .getDouble(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
                            0);
                        assertEquals(
                            expectedDiskSizeGB,
                            regionConfig
                                .getJSONObject(
                                    ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD)
                                .getDouble(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
                            0);
                      });
            });
  }

  @Test
  public void testGetCluster_basicClusterTypes() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));
    final ClusterDescription basicAWSCluster =
        _clusterDescriptionDao.findByName(_group.getId(), TestClusterNames.BASIC_AWS_CLUSTER).get();
    final JSONObject resp_basicAWSCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        basicAWSCluster.getUniqueId().toString(),
        resp_basicAWSCluster.getString(ApiAtlasClusterDescription20240805View.ID_FIELD));

    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());

    assertEquals(
        List.of(),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final ClusterDescription crossCloudCluster =
        _clusterDescriptionDao
            .findByName(_group.getId(), TestClusterNames.CROSS_CLOUD_CLUSTER)
            .get();
    final JSONObject resp_crossCloudCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.CROSS_CLOUD_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        crossCloudCluster.getUniqueId().toString(),
        resp_crossCloudCluster.getString(ApiAtlasClusterDescription20240805View.ID_FIELD));
  }

  @Test
  public void testGetCluster_duringTenantUpgrade() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));
    // set tenant cluster to IDLE
    _clusterDescriptionDao.setState(_group.getId(), TestClusterNames.TENANT_CLUSTER, State.IDLE);

    final ClusterDescription tenantCluster =
        _clusterDescriptionDao.findByName(_group.getId(), TestClusterNames.TENANT_CLUSTER).get();
    final JSONObject resp_tenantCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.TENANT_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        tenantCluster.getUniqueId().toString(),
        resp_tenantCluster.getString(ApiAtlasClusterDescription20240805View.ID_FIELD));

    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());

    assertEquals(
        List.of(),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    // in the absence of a tenant upgrade status, cluster is IDLE
    assertEquals(
        "IDLE",
        resp_tenantCluster.getString(ApiAtlasClusterDescription20240805View.STATE_NAME_FIELD));

    // add a tenant upgrade status
    final TenantUpgradeStatus tenantUpgradeStatus =
        new TenantUpgradeStatus(
            TestClusterNames.TENANT_CLUSTER,
            _group.getId(),
            "MTM",
            ObjectId.get(),
            ObjectId.get(),
            new Date());
    _tenantUpgradeStatusDao.save(tenantUpgradeStatus);

    // tenant cluster should now be UPDATING
    final JSONObject resp_tenantCluster_updating =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.TENANT_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "UPDATING",
        resp_tenantCluster_updating.getString(
            ApiAtlasClusterDescription20240805View.STATE_NAME_FIELD));

    // after the original cluster is deleted during the tenant upgrade, status should still be
    // UPDATING
    _clusterDescriptionDao.markDeleted(_group.getId(), TestClusterNames.TENANT_CLUSTER, new Date());
    assertEquals(
        State.DELETED,
        _clusterDescriptionDao
            .findByName(_group.getId(), TestClusterNames.TENANT_CLUSTER, true)
            .map(ClusterDescription::getState)
            .orElseThrow());

    final JSONObject resp_tenantCluster_stillUpdating =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.TENANT_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "UPDATING",
        resp_tenantCluster_stillUpdating.getString(
            ApiAtlasClusterDescription20240805View.STATE_NAME_FIELD));
  }

  @Test
  public void testGetCluster_deletedWithRetainedBackups() {
    assertTrue(
        _clusterDescriptionDao
            .findByName(
                _group.getId(), TestClusterNames.DELETED_CLUSTER_WITH_RETAINED_BACKUPS, true)
            .isPresent());

    // assert the following returns a 404 error
    final JSONObject deletedClusterWithRetainedBackupsRes =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.DELETED_CLUSTER_WITH_RETAINED_BACKUPS,
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // Verify the 404 error response details
    assertEquals(
        HttpStatus.SC_NOT_FOUND, deletedClusterWithRetainedBackupsRes.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CLUSTER_NOT_FOUND",
        deletedClusterWithRetainedBackupsRes.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals("Not Found", deletedClusterWithRetainedBackupsRes.getString("reason"));
    assertEquals(
        "No cluster named deletedClusterWithRetainedBackups exists in group "
            + _group.getId()
            + ".",
        deletedClusterWithRetainedBackupsRes.getString("detail"));
  }

  @Test
  public void testGetAllClusters_deletedWithRetainedBackups() {
    assertTrue(
        _clusterDescriptionDao
            .findByName(
                _group.getId(), TestClusterNames.DELETED_CLUSTER_WITH_RETAINED_BACKUPS, true)
            .isPresent());

    // Verify deleted with retained backups doesn't show up when includeDeletedWithRetainedBackups
    // param is not set
    final JSONObject allClustersRes =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    final JSONArray allClustersJson = allClustersRes.getJSONArray("results");
    assertEquals(6, allClustersJson.length());
    // Verify deleted cluster with retained backups is not in the list of clusters
    for (Object clusterDescriptionJson : allClustersJson) {
      assertNotEquals(
          TestClusterNames.DELETED_CLUSTER_WITH_RETAINED_BACKUPS,
          ((JSONObject) clusterDescriptionJson).getString("name"));
    }

    // Verify deleted with retained backups shows up when includeDeletedWithRetainedBackups param is
    // set
    final JSONObject allClustersIncludingDeletedRes =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters?includeDeletedWithRetainedBackups=true",
            SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    final JSONArray allClustersIncludingDeletedWithRetainedBackupsJson =
        allClustersIncludingDeletedRes.getJSONArray("results");
    assertEquals(7, allClustersIncludingDeletedWithRetainedBackupsJson.length());
    // Verify deleted cluster with retained backups is in the list of clusters
    boolean containsDeletedWithRetainedBackupsCluster = false;
    for (Object clusterDescriptionObj : allClustersIncludingDeletedWithRetainedBackupsJson) {
      var clusterDescriptionJson = (JSONObject) clusterDescriptionObj;
      if (clusterDescriptionJson
          .getString("name")
          .equals(TestClusterNames.DELETED_CLUSTER_WITH_RETAINED_BACKUPS)) {
        containsDeletedWithRetainedBackupsCluster = true;
        break;
      }
    }
    assertTrue(containsDeletedWithRetainedBackupsCluster);
  }

  @Test
  public void testGetCluster_basicErrors() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    // cluster doesn't exist
    final JSONObject resp_nonexistentCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/fake",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp_nonexistentCluster.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp_nonexistentCluster.getString(ApiError.ERROR_CODE_FIELD));

    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());

    assertEquals(
        List.of(),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    // group doesn't exist
    final JSONObject resp_nonexistentGroup =
        doDigestJsonGetWithApiVersion(
            BASE_URL + "66217ccd9bafc61d9233a040" + "/clusters/fake",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp_nonexistentGroup.getInt(ApiError.ERROR_FIELD));
    assertEquals("GROUP_NOT_FOUND", resp_nonexistentGroup.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetCluster_unsplitShardedClusterDisallowed() {
    // a sharded cluster on the old schema, with unsplit replication specs
    _clusterDescriptionDao.save(
        new ShardedClusterDescription(
            NDSModelTestFactory.getShardedAWSClusterDescription(
                _group.getId(), TestClusterNames.LEGACY_SHARDED_CLUSTER, 3)));

    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    // get cluster; allowed
    final JSONObject respUnsplitCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.LEGACY_SHARDED_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(3, respUnsplitCluster.getJSONArray("replicationSpecs").length());

    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());

    assertEquals(
        List.of(),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    _clusterDescriptionDao.markDeleted(
        _group.getId(), TestClusterNames.LEGACY_SHARDED_CLUSTER, new Date());
  }

  @Test
  public void testGetCluster_unsplitShardedClusterDisallowed_SecondRequest_NoPlanASAP() {
    // a sharded cluster on the old schema, with unsplit replication specs
    _clusterDescriptionDao.save(
        new ShardedClusterDescription(
            NDSModelTestFactory.getShardedAWSClusterDescription(
                _group.getId(), TestClusterNames.LEGACY_SHARDED_CLUSTER, 3)));

    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    // get cluster; should be split
    final JSONObject respUnsplitCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.LEGACY_SHARDED_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(3, respUnsplitCluster.getJSONArray("replicationSpecs").length());

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    // FF was already enabled so planASAP / audit is not necessary
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    _clusterDescriptionDao.markDeleted(
        _group.getId(), TestClusterNames.LEGACY_SHARDED_CLUSTER, new Date());
  }

  @Test
  public void testGetCluster_redactClientLogData() {
    // A cluster description without the redactClientLogData field should still have it populated
    // when reading it back, defaulting to false
    final BasicDBObject cluster =
        _clusterDescriptionDao
            .findByName(_group.getId(), TestClusterNames.BASIC_AWS_CLUSTER)
            .get()
            .toDBObject();
    cluster.remove("redactClientLogData");
    _clusterDescriptionDao.save(cluster, WriteConcern.MAJORITY);

    // It should default to false if the field is absent in the ClusterDescription
    final JSONObject respNull =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertFalse(respNull.getBoolean("redactClientLogData"));

    final ClusterDescription clusterDescription =
        _clusterDescriptionDao.findByName(_group.getId(), TestClusterNames.BASIC_AWS_CLUSTER).get();

    // Create a cluster with log redaction disabled
    _clusterDescriptionDao.save(clusterDescription.copy().setRedactClientLogData(false).build());

    // Ensure that it is returned correctly
    final JSONObject respFalse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertFalse(respFalse.getBoolean("redactClientLogData"));

    // Create a cluster with log redaction enabled
    _clusterDescriptionDao.save(clusterDescription.copy().setRedactClientLogData(true).build());

    // Ensure that it is returned correctly
    final JSONObject respTrue =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertTrue(respTrue.getBoolean("redactClientLogData"));
  }

  @Test
  public void testGetCluster_responseContainsLinks() {
    final JSONObject basicAWSClusterResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray links =
        basicAWSClusterResponse.getJSONArray(ApiAtlasClusterDescription20240805View.LINKS_FIELD);

    assertEquals(3, links.length());
  }

  @Test
  public void testGetAllClusters() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
    assertEquals(
        new Date(0), _ndsGroupSvc.find(_group.getId()).orElseThrow().getNextPlanningDate());
    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));

    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final JSONObject getAllResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray results = getAllResponse.getJSONArray(ApiListView.RESULTS_FIELD);

    // Shim logic app setting and feature flag is disabled
    // note: this utility evaluates the list contains only the clusters listed, and so will
    // validate the absence of the serverless cluster and flex clusters in the response
    assertTrue(
        ApiAtlasBaseClusterDescriptionResourceIntTestUtils.equalsClusters(
            results,
            TestClusterNames.BASIC_AWS_CLUSTER,
            TestClusterNames.TENANT_CLUSTER,
            TestClusterNames.CROSS_CLOUD_CLUSTER,
            TestClusterNames.ASYMMETRIC_CLUSTER_WITH_EMBEDDED_CONFIG,
            TestClusterNames.M2_CLUSTER,
            TestClusterNames.NVME_CLUSTER));

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    // FF was already enabled so planASAP / audit is not necessary
    assertEquals(
        new Date(0), _ndsGroupSvc.find(_group.getId()).orElseThrow().getNextPlanningDate());
    assertEquals(
        List.of(),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    // Shim logic app setting set to DEPRECATED but feature flag disabled so still no flex cluster
    // returned
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);
    final JSONObject getAllResponse2 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray results2 = getAllResponse2.getJSONArray(ApiListView.RESULTS_FIELD);
    assertTrue(
        ApiAtlasBaseClusterDescriptionResourceIntTestUtils.equalsClusters(
            results2,
            TestClusterNames.BASIC_AWS_CLUSTER,
            TestClusterNames.TENANT_CLUSTER,
            TestClusterNames.CROSS_CLOUD_CLUSTER,
            TestClusterNames.ASYMMETRIC_CLUSTER_WITH_EMBEDDED_CONFIG,
            TestClusterNames.M2_CLUSTER,
            TestClusterNames.NVME_CLUSTER));

    // Enabling our shim feature flag -- should include flex cluster created from shim logic and
    // migrated from shared in the response
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);

    final JSONObject getAllResponse3 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray results3 = getAllResponse3.getJSONArray(ApiListView.RESULTS_FIELD);
    assertTrue(
        ApiAtlasBaseClusterDescriptionResourceIntTestUtils.equalsClusters(
            results3,
            TestClusterNames.BASIC_AWS_CLUSTER,
            TestClusterNames.TENANT_CLUSTER,
            TestClusterNames.CROSS_CLOUD_CLUSTER,
            TestClusterNames.ASYMMETRIC_CLUSTER_WITH_EMBEDDED_CONFIG,
            TestClusterNames.M2_CLUSTER,
            TestClusterNames.NVME_CLUSTER,
            TestClusterNames.FLEX_CLUSTER_CREATED_FROM_SHIM_LOGIC,
            TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED));
  }

  @Test
  public void testGetAllClusters_unsplitShardedClustersEnablesFFSetsPlanASAP() {
    // a sharded cluster on the old schema, with unsplit replication specs
    _clusterDescriptionDao.save(
        new ShardedClusterDescription(
            NDSModelTestFactory.getShardedAWSClusterDescription(
                _group.getId(), TestClusterNames.LEGACY_SHARDED_CLUSTER, 3)));

    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    // get all clusters; bad request
    final JSONObject getAllResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final Optional<JSONObject> result =
        IntStream.range(0, getAllResponse.getJSONArray("results").length())
            .mapToObj(getAllResponse.getJSONArray("results")::getJSONObject)
            .filter(obj -> obj.getString("name").equals(TestClusterNames.LEGACY_SHARDED_CLUSTER))
            .findFirst();
    assertNotEquals(Optional.empty(), result);
    assertEquals(3, result.get().getJSONArray("replicationSpecs").length());

    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));
    _clusterDescriptionDao.markDeleted(
        _group.getId(), TestClusterNames.LEGACY_SHARDED_CLUSTER, new Date());
  }

  @Test
  public void testGetAllClusters_unsplitShardedClusters_FFEnabled_NoSetPlanASAP() {
    // a sharded cluster on the old schema, with unsplit replication specs
    _clusterDescriptionDao.save(
        new ShardedClusterDescription(
            NDSModelTestFactory.getShardedAWSClusterDescription(
                _group.getId(), TestClusterNames.LEGACY_SHARDED_CLUSTER, 3)));

    // unambiguous next planning date which we'll check in a moment
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));

    // get all clusters succeeds
    final JSONObject getAllResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final Optional<JSONObject> result =
        IntStream.range(0, getAllResponse.getJSONArray("results").length())
            .mapToObj(getAllResponse.getJSONArray("results")::getJSONObject)
            .filter(obj -> obj.getString("name").equals(TestClusterNames.LEGACY_SHARDED_CLUSTER))
            .findFirst();
    assertNotEquals(Optional.empty(), result);

    assertEquals(3, result.get().getJSONArray("replicationSpecs").length());

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    // FF was already enabled so planASAP / audit is not necessary
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));
    _clusterDescriptionDao.markDeleted(
        _group.getId(), TestClusterNames.LEGACY_SHARDED_CLUSTER, new Date());
  }

  @Test
  public void testGetAllClusters_responseContainsLinks() {
    final JSONObject getAllResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // check links in the list response itself
    final JSONArray links = getAllResponse.getJSONArray(ApiView.LINKS_FIELD);

    assertEquals(1, links.length());

    // check links within each cluster in the list
    final JSONArray clusterList = getAllResponse.getJSONArray("results");
    final boolean allClustersContainLinks =
        IntStream.range(0, clusterList.length())
            .mapToObj(clusterList::getJSONObject)
            .allMatch(
                cluster ->
                    cluster
                            .getJSONArray(ApiAtlasClusterDescription20240805View.LINKS_FIELD)
                            .length()
                        == 3);
    assertTrue(allClustersContainLinks);
  }

  @Test
  public void testCreateCluster_basicErrors() throws Exception {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    // missing JSON content
    final JSONObject response_emptyJSON =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            new JSONObject(),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.MISSING_ATTRIBUTE.name(),
        response_emptyJSON.getString(ApiError.ERROR_CODE_FIELD));

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));
    // attempting to create M0 via API
    final JSONObject resp_m0 =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultFreeReplicaSetView("freeCluster", _group.getId()),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.CANNOT_CREATE_FREE_CLUSTER_VIA_PUBLIC_API.name(),
        resp_m0.getString(ApiError.ERROR_CODE_FIELD));

    // attempting to create a serverless instance fails with invalid JSON attribute for
    // providerName = SERVERLESS
    final JSONObject resp_serverless =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultServerlessReplicaSetView("serverless", _group.getId()),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp_serverless.getInt(ApiError.ERROR_FIELD));
    assertEquals("INVALID_JSON_ATTRIBUTE", resp_serverless.getString(ApiError.ERROR_CODE_FIELD));

    // attempting to create flex cluster fails with invalid JSON attribute for providerName = FLEX
    final JSONObject resp_flex =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultFlexReplicaSetView("flex", _group.getId()),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp_flex.getInt(ApiError.ERROR_FIELD));
    assertEquals("INVALID_JSON_ATTRIBUTE", resp_flex.getString(ApiError.ERROR_CODE_FIELD));

    // attempting to create a sharded cluster on the legacy schema (eg, with numShards) fails
    final JSONObject resp_legacySharded =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createLegacyShardedClusterView("legacy sharded", _group.getId()),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp_legacySharded.getInt(ApiError.ERROR_FIELD));
    assertEquals("INVALID_ATTRIBUTE", resp_legacySharded.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals("numShards", resp_legacySharded.getJSONArray("parameters").getString(0));
  }

  @Test
  public void testCreateClusterInstanceSize() {
    final ObjectId pGroupId = _group.getId();
    // create cluster with electable instance size different in the same shard
    // should error
    List<NDSModelTestFactory.MultiRegionConfig> regionConfigsList =
        Arrays.asList(
            new NDSModelTestFactory.MultiRegionConfig(
                "M30", "AWS", "US_EAST_1", 40.0, 3, 7, false, null, null, 0, null, null, 0),
            new NDSModelTestFactory.MultiRegionConfig(
                "M10", "AWS", "EU_WEST_2", 40.0, 2, 6, false, null, null, 0, null, null, 0));
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, "myCluster");
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(NDSModelTestFactory.getDefaultMultiRegionReplicationSpec(regionConfigsList)));

    final JSONObject differentInstanceSize =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            cluster,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ApiErrorCode.INSTANCE_SIZE_MUST_MATCH_IN_REGION_CONFIGS.name(),
        differentInstanceSize.getString(ApiError.ERROR_CODE_FIELD));

    // Analytics node count are not the same
    List<NDSModelTestFactory.MultiRegionConfig> regionConfigsListAnalytics =
        Arrays.asList(
            new NDSModelTestFactory.MultiRegionConfig(
                "M30", "AWS", "US_EAST_1", 40.0, 3, 7, false, null, null, 0, "M30", 40.0, 2),
            new NDSModelTestFactory.MultiRegionConfig(
                "M30", "AWS", "EU_WEST_2", 40.0, 2, 6, false, null, null, 0, "M10", 40.0, 1));
    final JSONObject cluster1 = new JSONObject();
    cluster1.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, "myCluster");
    cluster1.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster1.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster1.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(
                NDSModelTestFactory.getDefaultMultiRegionReplicationSpec(
                    regionConfigsListAnalytics)));

    final JSONObject differentInstanceSizeAnalytics =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            cluster1,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ApiErrorCode.ANALYTICS_NODE_SIZE_MUST_MATCH_IN_REGION_CONFIGS.name(),
        differentInstanceSizeAnalytics.getString(ApiError.ERROR_CODE_FIELD));

    // Read-only node count are not the same
    List<NDSModelTestFactory.MultiRegionConfig> regionConfigsListReadOnly =
        Arrays.asList(
            new NDSModelTestFactory.MultiRegionConfig(
                "M30", "AWS", "US_EAST_1", 40.0, 3, 7, false, "M30", 40.0, 1, null, null, 0),
            new NDSModelTestFactory.MultiRegionConfig(
                "M30", "AWS", "EU_WEST_2", 40.0, 2, 6, false, "M10", 40.0, 2, null, null, 0));
    final JSONObject cluster2 = new JSONObject();
    cluster2.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, "myCluster");
    cluster2.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster2.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster2.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(
                NDSModelTestFactory.getDefaultMultiRegionReplicationSpec(
                    regionConfigsListReadOnly)));

    final JSONObject differentInstanceSizeReadOnly =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            cluster2,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ApiErrorCode.READ_ONLY_NODE_SIZE_MUST_MATCH_IN_REGION_CONFIGS.name(),
        differentInstanceSizeReadOnly.getString(ApiError.ERROR_CODE_FIELD));

    // create cluster with electable instance size same in the same shard
    // should succeed
    List<NDSModelTestFactory.MultiRegionConfig> regionConfigsList2 =
        Arrays.asList(
            new NDSModelTestFactory.MultiRegionConfig(
                "M30", "AWS", "US_EAST_1", 40.0, 3, 7, false, "M30", 40.0, 1, "M30", 40.0, 1),
            new NDSModelTestFactory.MultiRegionConfig(
                "M30", "AWS", "EU_WEST_2", 40.0, 2, 6, false, "M30", 40.0, 1, "M30", 40.0, 1));
    final JSONObject cluster3 = new JSONObject();
    cluster3.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, "myCluster");
    cluster3.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster3.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster3.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(NDSModelTestFactory.getDefaultMultiRegionReplicationSpec(regionConfigsList2)));

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        cluster3,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
  }

  @Test
  public void testCreateFreeCluster_MultipleM0s() {
    _ndsGroupDao.setLimit(DEFAULT_GROUP, Limits.FieldDefs.NUM_SALES_SOLD_M0.getValue(), 2);

    /// First Shared Cluster
    // successful request to create M0 with the public API
    doDigestJsonPostWithApiVersion(
        BASE_URL + oid(118) + "/clusters",
        createDefaultFreeReplicaSetView("freeCluster", oid(118)),
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V20240805_VERSION);

    /// Second Shared Cluster
    // cannot create more than one M0 cluster in a project
    final JSONObject fCD2ndM0 = createDefaultFreeReplicaSetView("freeCluster2", oid(118));
    final JSONObject respfCD2ndM0 =
        doDigestJsonPostWithApiVersion(
            BASE_URL + oid(118) + "/clusters",
            fCD2ndM0,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V20240805_VERSION);

    assertEquals(HttpStatus.SC_BAD_REQUEST, respfCD2ndM0.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CREATE_FREE_CLUSTER_VIA_PUBLIC_API",
        respfCD2ndM0.getString(ApiError.ERROR_CODE_FIELD));

    //  set admin override payment method
    stubber.stubOverridePaymentMethod(_groupDao.findById(DEFAULT_GROUP).getOrgId(), true);

    //  creation now succeeds
    doDigestJsonPostWithApiVersion(
        BASE_URL + oid(118) + "/clusters",
        fCD2ndM0,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V20240805_VERSION);

    /// Third Shared Cluster
    // cannot create because has reached M0 limit for project
    final JSONObject fCD3rdM0 = createDefaultFreeReplicaSetView("freeCluster3", oid(118));
    final JSONObject respfCD3rdM0 =
        doDigestJsonPostWithApiVersion(
            BASE_URL + oid(118) + "/clusters",
            fCD3rdM0,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, respfCD3rdM0.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CREATE_FREE_CLUSTER_VIA_PUBLIC_API",
        respfCD3rdM0.getString(ApiError.ERROR_CODE_FIELD));

    //  set a non-override payment method and increase limit
    _ndsGroupDao.setLimit(DEFAULT_GROUP, Limits.FieldDefs.NUM_SALES_SOLD_M0.getValue(), 4);
    stubber.stubPaymentMethod(_groupDao.findById(DEFAULT_GROUP).getOrgId(), true);

    // fails again because not a sales-sold org
    final JSONObject respfCD3rdM0_1 =
        doDigestJsonPostWithApiVersion(
            BASE_URL + oid(118) + "/clusters",
            fCD3rdM0,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, respfCD3rdM0_1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CREATE_FREE_CLUSTER_VIA_PUBLIC_API",
        respfCD3rdM0_1.getString(ApiError.ERROR_CODE_FIELD));

    //  create an activation credit
    createPlanCreditForGroup(_groupDao, _organizationDao, DEFAULT_GROUP);

    //  creation now succeeds
    doDigestJsonPostWithApiVersion(
        BASE_URL + oid(118) + "/clusters",
        fCD3rdM0,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V20240805_VERSION);
  }

  @Test
  public void testCreateCluster_responseContainsLinks() {
    final String clusterName = "defaultRS";
    final JSONObject createClusterResponse =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultSingleRegionReplicaSetView(
                clusterName, _group.getId(), "M10", "AWS", "US_EAST_1"),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray links =
        createClusterResponse.getJSONArray(ApiAtlasClusterDescription20240805View.LINKS_FIELD);

    assertEquals(3, links.length());
  }

  @Test
  public void testCreateCluster_inconsistentRegionTopologyInZone() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final JSONObject response_inconsistentRegionTopology =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createAsymmetricShardedClusterView_inconsistentRegionTopologyInZone(
                "myCluster", _group.getId()),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.ASYMMETRIC_REGION_TOPOLOGY_IN_ZONE.name(),
        response_inconsistentRegionTopology.getString(ApiError.ERROR_CODE_FIELD));

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertTrue(
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .toList()
            .contains(NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE));
  }

  @Test
  public void testCreateCluster_inconsistentDiskSizeGB() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final JSONObject response_inconsistentDiskSize =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createAsymmetricShardedClusterView_inconsistentDiskSizeGB("myCluster", _group.getId()),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.DISK_SIZE_GB_INCONSISTENT.name(),
        response_inconsistentDiskSize.getString(ApiError.ERROR_CODE_FIELD));

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));
  }

  @Test
  public void testCreateCluster_invalidShardSizeRange() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final ObjectId pGroupId = _group.getId();
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, "myCluster");
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(getDefaultSingleRegionReplicationSpec("M10", "AWS", "US_EAST_1", 20.0, true))
            .put(getDefaultSingleRegionReplicationSpec("M200", "AWS", "US_EAST_1", 20.0, true)));

    final JSONObject response_invalidShardRange =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            cluster,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // In the next API, this would be INVALID_INSTANCE_SIZE_RANGE to validate the range of the
    // autoscaler.
    // In this API, autoscaling is off, so it doesn't even get that far.
    assertEquals(
        ApiErrorCode.ASYMMETRIC_BASE_AUTO_SCALING_UNAVAILABLE.name(),
        response_invalidShardRange.getString(ApiError.ERROR_CODE_FIELD));

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));
  }

  @Test
  public void testCreateCluster_tenantCluster() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createTenantCluster";
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createDefaultTenantReplicaSetView(clusterName, _group.getId(), "M2", "US_EAST_1"),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE, NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject resp_getCreatedTenantCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // ensure diskSizeGB made it through the various API/Svc translations intact; API and core model
    // schema currently differ here
    final Double createdTenantClusterDiskSizeGB_view =
        resp_getCreatedTenantCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getDouble(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD);
    final Double createdTenantClusterDiskSizeGB_db =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).get().getDiskSizeGB();

    assertEquals(2.0, createdTenantClusterDiskSizeGB_view, 0);
    assertEquals(2.0, createdTenantClusterDiskSizeGB_db, 0);
  }

  @Test
  public void testCreateCluster_dedicatedReplicaSet_AWS() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createAWSRS";
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createDefaultSingleRegionReplicaSetView(
            clusterName, _group.getId(), "M10", "AWS", "US_EAST_1"),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    // PlanASAP is not updated on the second request thoug
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject resp_getCreatedBasicReplicaSetAWS =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // ensure diskSizeGB made it through the various API/Svc translations intact; API and core model
    // schema currently differ here
    final JSONObject createdHardwareSpec_view =
        resp_getCreatedBasicReplicaSetAWS
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);

    final Double createdReplicaSetDiskSizeGB_db =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).get().getDiskSizeGB();

    assertEquals(
        10.0,
        createdHardwareSpec_view.getDouble(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0);
    assertEquals(10.0, createdReplicaSetDiskSizeGB_db, 0);

    // ensure hardware attributes unique to AWS are all in expected state
    final AWSHardwareSpec createdHardwareSpec_db =
        (AWSHardwareSpec)
            _clusterDescriptionUpdatesDao
                .findByName(_group.getId(), clusterName)
                .get()
                .getReplicationSpecsWithShardData()
                .get(0)
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs();

    // instance size
    assertEquals(
        "M10",
        createdHardwareSpec_view.getString(
            ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(AWSNDSInstanceSize.M10, createdHardwareSpec_db.getInstanceSize());

    // disk IOPS
    assertEquals(
        3000, createdHardwareSpec_view.getInt(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD));
    assertEquals(3000, createdHardwareSpec_db.getDiskIOPS());

    // volume type
    assertEquals(
        "STANDARD",
        createdHardwareSpec_view.getString(
            ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD));
    assertEquals(VolumeType.Gp3, createdHardwareSpec_db.getEBSVolumeType());

    final ClusterDescription createdClusterDescription =
        _ndsClusterSvc.getMergedClusterDescription(_group.getId(), clusterName).orElseThrow();
    assertEquals(AutoScalingMode.CLUSTER, createdClusterDescription.getAutoScalingMode());
  }

  @Test
  public void testCreateCluster_dedicatedReplicaSet_GCP() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createGCPRS";
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createDefaultSingleRegionReplicaSetView(
            clusterName, _group.getId(), "M10", "GCP", "EASTERN_US"),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject resp_getCreatedBasicReplicaSetGCP =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // ensure diskSizeGB made it through the various API/Svc translations intact; API and core model
    // schema currently differ here
    final JSONObject createdHardwareSpec_view =
        resp_getCreatedBasicReplicaSetGCP
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);

    final Double createdReplicaSetDiskSizeGB_db =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).get().getDiskSizeGB();

    assertEquals(
        10.0,
        createdHardwareSpec_view.getDouble(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0);
    assertEquals(10.0, createdReplicaSetDiskSizeGB_db, 0);

    // ensure hardware attributes unique to GCP are all in expected state
    final GCPHardwareSpec createdHardwareSpec_db =
        (GCPHardwareSpec)
            _clusterDescriptionUpdatesDao
                .findByName(_group.getId(), clusterName)
                .get()
                .getReplicationSpecsWithShardData()
                .get(0)
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs();

    // instance size
    assertEquals(
        "M10", createdHardwareSpec_view.getString(ApiAtlasGCPHardwareSpecView.INSTANCE_SIZE_FIELD));
    assertEquals(GCPNDSInstanceSize.M10, createdHardwareSpec_db.getInstanceSize());

    // no provisioned IOPs, will fallback to default value
    assertTrue(createdHardwareSpec_db.getDiskIOPS().isEmpty());

    final ClusterDescription createdClusterDescription =
        _ndsClusterSvc.getMergedClusterDescription(_group.getId(), clusterName).orElseThrow();
    assertEquals(AutoScalingMode.CLUSTER, createdClusterDescription.getAutoScalingMode());
  }

  @Test
  public void testCreateCluster_dedicatedReplicaSet_GCP_provisionedIOPS() {
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    final String clusterName = "gcpProvisionedIOPS";
    final JSONObject clusterDescription =
        createDefaultSingleRegionReplicaSetView(
            clusterName, _group.getId(), "M10", "GCP", "EASTERN_US");

    clusterDescription.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        getGcpReplicationSpecWithProvisionedIOPS(ApiAtlasGCPInstanceSizeView.M10));

    _appSettings.setProp(
        AppSettings.Fields.GCP_PROVISIONED_IOPS_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    // App setting for provisioned IOPS on GCP is disabled
    final JSONObject response_provisionedIOPSDisabled =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            clusterDescription,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        response_provisionedIOPSDisabled.getString(ApiError.ERROR_CODE_FIELD),
        ApiErrorCode.INVALID_IOPS_CONFIGURATION_FOR_GCP_FEATURE_DISABLED.name());

    // Enable provisioned IOPS on GCP Hyperdisk-backed instances
    _appSettings.setProp(
        AppSettings.Fields.GCP_PROVISIONED_IOPS_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);

    // Request still fails because M10 instance tier never uses Hyperdisk storage
    final JSONObject response_noProvisionedIOPSM10 =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            clusterDescription,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        response_noProvisionedIOPSM10.getString(ApiError.ERROR_CODE_FIELD),
        ApiErrorCode.INVALID_IOPS_CONFIGURATION_FOR_GCP_UNSUPPORTED_INSTANCE_FAMILY.name());

    // Update to M30. M30 in CENTRAL_US supports N4 after the N4 feature flag enabled.
    _featureFlagSvc.enableNonConfigServiceFeatureFlag(_group, null, FeatureFlag.GCP_N4_FAMILY);
    clusterDescription.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        getGcpReplicationSpecWithProvisionedIOPS(ApiAtlasGCPInstanceSizeView.M30));

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        clusterDescription,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    final JSONObject resp_getCreatedBasicReplicaSetGCP =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONObject createdHardwareSpec_view =
        resp_getCreatedBasicReplicaSetGCP
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);

    final ClusterDescription foundClusterDescription =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).orElseThrow();

    assertEquals(
        50.0,
        createdHardwareSpec_view.getDouble(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0);
    assertEquals(50.0, foundClusterDescription.getDiskSizeGB(), 0);

    final GCPHardwareSpec createdHardwareSpec_db =
        (GCPHardwareSpec)
            foundClusterDescription
                .getReplicationSpecsWithShardData()
                .get(0)
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs();

    assertEquals(
        "M30", createdHardwareSpec_view.getString(ApiAtlasGCPHardwareSpecView.INSTANCE_SIZE_FIELD));
    assertEquals(GCPNDSInstanceSize.M30, createdHardwareSpec_db.getInstanceSize());

    assertTrue(createdHardwareSpec_db.getDiskIOPS().isPresent());
    assertEquals(12345, (int) createdHardwareSpec_db.getDiskIOPS().get());
    assertEquals(
        12345,
        createdHardwareSpec_view.getInt(ApiAtlasGCPHardwareSpec20240805View.DISK_IOPS_FIELD));
  }

  private static JSONArray getGcpReplicationSpecWithProvisionedIOPS(
      final ApiAtlasGCPInstanceSizeView instanceSize) {
    final JSONObject regionConfig =
        new JSONObject()
            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 7)
            .put(
                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                ApiAtlasCloudProviderView.GCP.name())
            .put(
                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                ApiAtlasGCPRegionNameView.CENTRAL_US.name())
            .put(
                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasGCPHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
                        instanceSize.name())
                    .put(ApiAtlasGCPHardwareSpec20240805View.NODE_COUNT_FIELD, 3)
                    .put(ApiAtlasGCPHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 50.0)
                    .put(ApiAtlasGCPHardwareSpec20240805View.DISK_IOPS_FIELD, 12345));
    return new JSONArray()
        .put(
            new JSONObject()
                .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 1")
                .put(
                    ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                    new JSONArray(List.of(regionConfig))));
  }

  @Test
  public void testCreateCluster_dedicatedReplicaSet_Azure() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createAzureRS";
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createDefaultSingleRegionReplicaSetView(
            clusterName, _group.getId(), "M10", "AZURE", "US_EAST"),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject resp_getCreatedBasicReplicaSetAzure =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // ensure diskSizeGB made it through the various API/Svc translations intact; API and core model
    // schema currently differ here
    final JSONObject createdHardwareSpec_view =
        resp_getCreatedBasicReplicaSetAzure
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);

    final Double createdReplicaSetDiskSizeGB_db =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).get().getDiskSizeGB();

    assertEquals(
        8.0,
        createdHardwareSpec_view.getDouble(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0);
    assertEquals(8.0, createdReplicaSetDiskSizeGB_db, 0);

    // ensure hardware attributes unique to Azure are all in expected state
    final AzureHardwareSpec createdHardwareSpec_db =
        (AzureHardwareSpec)
            _clusterDescriptionUpdatesDao
                .findByName(_group.getId(), clusterName)
                .get()
                .getReplicationSpecsWithShardData()
                .get(0)
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs();

    // instance size
    assertEquals(
        "M10",
        createdHardwareSpec_view.getString(
            ApiAtlasAzureHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(AzureNDSInstanceSize.M10, createdHardwareSpec_db.getInstanceSize());

    // disk IOPS
    assertEquals(
        120,
        createdHardwareSpec_view.getInt(ApiAtlasAzureHardwareSpec20240805View.DISK_IOPS_FIELD));
    // an Azure cluster with P2 disks doesn't store IOPS in db, but reflects default value to the
    // API; see AzureHardwareSpec.getDiskIOPS
    assertNull(createdHardwareSpec_db.getDiskIOPS());

    // disk throughput
    assertEquals(
        25,
        createdHardwareSpec_view.getInt(
            ApiAtlasAzureHardwareSpec20240805View.DISK_THROUGHPUT_FIELD));
    // an Azure cluster with P2 disks doesn't store throughput in the db, but reflects default
    // value to the API; see AzureHardwareSpec.getDiskThroughput
    assertNull(createdHardwareSpec_db.getDiskThroughput());

    final ClusterDescription createdClusterDescription =
        _ndsClusterSvc.getMergedClusterDescription(_group.getId(), clusterName).orElseThrow();
    assertEquals(AutoScalingMode.CLUSTER, createdClusterDescription.getAutoScalingMode());
  }

  @Test
  public void testCreateCluster_dedicatedReplicaSet_crossCloud() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createCrossCloudRS";
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createDefaultCrossCloudReplicaSetView(clusterName, _group.getId()),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject resp_getCreatedCrossCloudReplicaSet =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // provider-specific hardware details have been tested above; validate basic shape
    // of region configs

    final JSONArray createdRegionConfigs_view =
        resp_getCreatedCrossCloudReplicaSet
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD);
    final List<RegionConfig> createdRegionConfigs_db =
        _clusterDescriptionUpdatesDao
            .findByName(_group.getId(), clusterName)
            .get()
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigs();

    assertEquals(4, createdRegionConfigs_view.length());
    assertEquals(4, createdRegionConfigs_db.size());
  }

  @Test
  public void testCreateCluster_dedicatedReplicaSet_crossCloud_defaultDiskSize() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final JSONObject regionConfigAWS =
        new JSONObject()
            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 7)
            .put(
                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                ApiAtlasCloudProviderView.AWS.name())
            .put(
                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                ApiAtlasAWSRegionNameView.US_EAST_1.name())
            .put(
                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
                        ApiAtlasAWSInstanceSizeView.M30.name())
                    .put(ApiAtlasAWSHardwareSpec20240805View.NODE_COUNT_FIELD, 2));

    final JSONObject regionConfigAzure =
        new JSONObject()
            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 6)
            .put(
                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                ApiAtlasCloudProviderView.AZURE.name())
            .put(
                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                ApiAtlasAzureRegionNameView.US_EAST_2.name())
            .put(
                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasAzureHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
                        ApiAtlasAzureInstanceSizeView.M30.name())
                    .put(ApiAtlasAzureHardwareSpec20240805View.NODE_COUNT_FIELD, 1));

    final JSONArray replicationSpecs =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 1")
                    .put(
                        ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                        new JSONArray(List.of(regionConfigAWS, regionConfigAzure))));

    final String clusterName = "crossCloudDefaultDiskSize";
    final JSONObject createClusterPayload =
        new JSONObject()
            .put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET")
            .put(ApiAtlasClusterDescription20240805View.NAME_FIELD, clusterName)
            .put(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD, replicationSpecs);

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createClusterPayload,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject getClusterResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray createdRegionConfigs =
        getClusterResponse
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD);

    final JSONObject createdAWSRegionConfig = createdRegionConfigs.getJSONObject(0);
    final JSONObject createdAzureRegionConfig = createdRegionConfigs.getJSONObject(1);

    assertEquals(
        ApiAtlasAWSInstanceSizeView.M30.name(),
        createdAWSRegionConfig
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        32.0,
        createdAWSRegionConfig
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getDouble(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0.001);

    assertEquals(
        ApiAtlasAzureInstanceSizeView.M30.name(),
        createdAzureRegionConfig
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasAzureHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        32.0,
        createdAzureRegionConfig
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getDouble(ApiAtlasAzureHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0.001);
  }

  @Test
  public void testCreateCluster_dedicatedReplicaSet_crossCloudAWSAndAzure_minDiskSize() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final JSONObject regionConfigAWS =
        new JSONObject()
            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 7)
            .put(
                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                ApiAtlasCloudProviderView.AWS.name())
            .put(
                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                ApiAtlasAWSRegionNameView.US_EAST_1.name())
            .put(
                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
                        ApiAtlasAWSInstanceSizeView.M10.name())
                    .put(ApiAtlasAWSHardwareSpec20240805View.NODE_COUNT_FIELD, 2)
                    .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 8.0));

    final JSONObject regionConfigAzure =
        new JSONObject()
            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 6)
            .put(
                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                ApiAtlasCloudProviderView.AZURE.name())
            .put(
                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                ApiAtlasAzureRegionNameView.US_EAST_2.name())
            .put(
                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasAzureHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
                        ApiAtlasAzureInstanceSizeView.M10.name())
                    .put(ApiAtlasAzureHardwareSpec20240805View.NODE_COUNT_FIELD, 1)
                    .put(ApiAtlasAzureHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 8.0));

    final JSONArray replicationSpecs =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 1")
                    .put(
                        ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                        new JSONArray(List.of(regionConfigAWS, regionConfigAzure))));

    final String clusterName = "crossCloudAWSAndAzureMinDiskSize";
    final JSONObject createClusterPayload =
        new JSONObject()
            .put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET")
            .put(ApiAtlasClusterDescription20240805View.NAME_FIELD, clusterName)
            .put(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD, replicationSpecs);

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createClusterPayload,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject getClusterResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray createdRegionConfigs =
        getClusterResponse
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD);

    final JSONObject createdAWSRegionConfig = createdRegionConfigs.getJSONObject(0);
    final JSONObject createdAzureRegionConfig = createdRegionConfigs.getJSONObject(1);

    assertEquals(
        8.0,
        createdAWSRegionConfig
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getDouble(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0.001);
    assertEquals(
        8.0,
        createdAzureRegionConfig
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getDouble(ApiAtlasAzureHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0.001);
  }

  @Test
  public void testCreateCluster_dedicatedReplicaSet_crossCloudAWSAndGCP_minDiskSize() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final JSONObject regionConfigAWS =
        new JSONObject()
            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 7)
            .put(
                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                ApiAtlasCloudProviderView.AWS.name())
            .put(
                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                ApiAtlasAWSRegionNameView.US_EAST_1.name())
            .put(
                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
                        ApiAtlasAWSInstanceSizeView.M10.name())
                    .put(ApiAtlasAWSHardwareSpec20240805View.NODE_COUNT_FIELD, 2)
                    .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 10.0));

    final JSONObject regionConfigGCP =
        new JSONObject()
            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 6)
            .put(
                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                ApiAtlasCloudProviderView.GCP.name())
            .put(
                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                ApiAtlasGCPRegionNameView.CENTRAL_US.name())
            .put(
                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasGCPHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
                        ApiAtlasGCPInstanceSizeView.M10.name())
                    .put(ApiAtlasGCPHardwareSpec20240805View.NODE_COUNT_FIELD, 1)
                    .put(ApiAtlasGCPHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 10.0));

    final JSONArray replicationSpecs =
        new JSONArray()
            .put(
                new JSONObject()
                    .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 1")
                    .put(
                        ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                        new JSONArray(List.of(regionConfigAWS, regionConfigGCP))));

    final String clusterName = "crossCloudAWSAndGCPMinDiskSize";
    final JSONObject createClusterPayload =
        new JSONObject()
            .put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET")
            .put(ApiAtlasClusterDescription20240805View.NAME_FIELD, clusterName)
            .put(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD, replicationSpecs);

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createClusterPayload,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject getClusterResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray createdRegionConfigs =
        getClusterResponse
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD);

    final JSONObject createdAWSRegionConfig = createdRegionConfigs.getJSONObject(0);
    final JSONObject createdGCPRegionConfig = createdRegionConfigs.getJSONObject(1);

    assertEquals(
        10.0,
        createdAWSRegionConfig
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getDouble(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0.001);
    assertEquals(
        10.0,
        createdGCPRegionConfig
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getDouble(ApiAtlasGCPHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        0.001);
    assertFalse(
        createdGCPRegionConfig
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .has(ApiAtlasGCPHardwareSpec20240805View.DISK_IOPS_FIELD));
  }

  @Test
  public void testCreateCluster_tenantCluster_invalidName() {
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "xxxxxxxxxxxxxxxxxxxxxx-test"; // invalid hyphen at 23rd char
    final JSONObject response_invalidClusterNamePrefix =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultTenantReplicaSetView(clusterName, _group.getId(), "M2", "US_EAST_1"),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.CLUSTER_NAME_PREFIX_INVALID.name(),
        response_invalidClusterNamePrefix.getString(ApiError.ERROR_CODE_FIELD));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(List.of(), _ndsGroupDao.find(_group.getId()).get().getCloudProviderContainers());
  }

  @Test
  public void testGetCluster_FCV_pinned() throws SvcException {

    LocalDate createDate = LocalDate.of(2024, 8, 26);
    Date createDate1 = Date.from(createDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

    LocalDate expirationDate = LocalDate.of(2024, 8, 27);
    Date expirationDate2 =
        Date.from(expirationDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

    _ndsClusterSvc.fixClusterFeatureCompatibilityVersion(
        _group.getId(),
        TestClusterNames.BASIC_AWS_CLUSTER,
        "7.0",
        "",
        null,
        expirationDate2,
        createDate1,
        null);

    final JSONObject getClusterResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals("7.0", getClusterResponse.getString("featureCompatibilityVersion"));
    assertEquals(
        "2024-08-27T00:00:00Z",
        getClusterResponse.getString("featureCompatibilityVersionExpirationDate"));
  }

  @Test
  public void testGetCluster_No_FCV_pinned() throws SvcException {

    final JSONObject getClusterResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        getClusterResponse.get("mongoDBMajorVersion"),
        getClusterResponse.get("featureCompatibilityVersion"));
    assertFalse(getClusterResponse.has("featureCompatibilityVersionExpirationDate"));
  }

  @Test
  public void testCreateCluster_symmetricShardedCluster() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createSymmetricSharded";
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createSymmetricShardedClusterView(clusterName, _group.getId()),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject resp_getCreatedCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final ClusterDescription createdCluster_db =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).get();

    // both representations show 2 replication specs, each M30
    assertEquals(
        2,
        resp_getCreatedCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());
    assertEquals(2, createdCluster_db.getReplicationSpecsWithShardData().size());

    assertEquals(
        "M30",
        resp_getCreatedCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        "M30",
        resp_getCreatedCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(1)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        AWSNDSInstanceSize.M30,
        createdCluster_db
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .getInstanceSize());
    assertEquals(
        AWSNDSInstanceSize.M30,
        createdCluster_db
            .getReplicationSpecsWithShardData()
            .get(1)
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .getInstanceSize());

    // in the db, each has 1 numShard
    assertEquals(1, createdCluster_db.getReplicationSpecsWithShardData().get(0).getNumShards(), 0);
    assertEquals(1, createdCluster_db.getReplicationSpecsWithShardData().get(1).getNumShards(), 0);
    assertEquals(2, createdCluster_db.getNumShards());
  }

  @Test
  public void testCreateCluster_asymmetricShardedCluster() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createAsymmetricSharded";
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createAsymmetricShardedClusterView(clusterName, _group.getId()),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject resp_getCreatedCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final ClusterDescription createdCluster_db =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).get();

    // both representations show 2 replication specs, one M30 one M50
    final JSONArray resp_getCreatedCluster_replSpecs =
        resp_getCreatedCluster.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
    assertEquals(2, resp_getCreatedCluster_replSpecs.length());
    assertEquals(2, createdCluster_db.getReplicationSpecsWithShardData().size());

    final Map<String, ObjectId> zoneNameToZoneId = new HashMap<>();
    for (int i = 0; i < resp_getCreatedCluster_replSpecs.length(); i++) {
      final JSONObject replSpecResp = resp_getCreatedCluster_replSpecs.getJSONObject(i);
      final String zoneNameResp =
          replSpecResp.getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD);
      final ObjectId zoneIdResp =
          new ObjectId(replSpecResp.getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
      assertNotNull(zoneIdResp);
      assertNotNull(zoneNameResp);

      final ReplicationSpec replSpecDB =
          createdCluster_db.getReplicationSpecsWithShardData().get(0);
      assertEquals(replSpecDB.getZoneId(), zoneIdResp);
      assertEquals(replSpecDB.getZoneName(), zoneNameResp);

      if (zoneNameToZoneId.containsKey(zoneNameResp)) {
        assertEquals(zoneIdResp, zoneNameToZoneId.get(zoneNameResp));
      } else {
        zoneNameToZoneId.put(zoneNameResp, zoneIdResp);
      }
    }

    assertEquals(
        "M30",
        resp_getCreatedCluster_replSpecs
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        "M50",
        resp_getCreatedCluster_replSpecs
            .getJSONObject(1)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        AWSNDSInstanceSize.M30,
        createdCluster_db
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .getInstanceSize());
    assertEquals(
        AWSNDSInstanceSize.M50,
        createdCluster_db
            .getReplicationSpecsWithShardData()
            .get(1)
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .getInstanceSize());

    // in the db, each has 1 numShard
    assertEquals(1, createdCluster_db.getReplicationSpecsWithShardData().get(0).getNumShards(), 0);
    assertEquals(1, createdCluster_db.getReplicationSpecsWithShardData().get(1).getNumShards(), 0);
    assertEquals(2, createdCluster_db.getNumShards());
  }

  @Test
  public void testCreateCluster_asymmetricShardedCluster_asymmetricIOPS() {
    final String clusterName = "createAsymmetricSharded";

    final JSONObject resp_createCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createAsymmetricShardedClusterViewMixedIOPS(clusterName, _group.getId()),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONObject resp_getCreatedCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final ClusterDescription createdCluster_db =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).get();

    final JSONArray resp_createCluster_replSpecs =
        resp_createCluster.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
    final JSONArray resp_getCreatedCluster_replSpecs =
        resp_getCreatedCluster.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);

    // both representations show 3 replication specs, M30, M40, M50
    assertEquals(3, resp_createCluster_replSpecs.length());
    assertEquals(3, resp_getCreatedCluster_replSpecs.length());
    assertEquals(3, createdCluster_db.getReplicationSpecsWithShardData().size());

    assertEquals(resp_createCluster_replSpecs.length(), resp_getCreatedCluster_replSpecs.length());

    final Map<String, ObjectId> zoneNameToZoneId = new HashMap<>();
    for (int i = 0; i < resp_getCreatedCluster_replSpecs.length(); i++) {
      final JSONObject replSpecResp = resp_getCreatedCluster_replSpecs.getJSONObject(i);
      final String zoneNameResp =
          replSpecResp.getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD);
      final ObjectId zoneIdResp =
          new ObjectId(replSpecResp.getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
      assertNotNull(zoneIdResp);
      assertNotNull(zoneNameResp);

      final ReplicationSpec replSpecDB =
          createdCluster_db.getReplicationSpecsWithShardData().get(0);
      assertEquals(replSpecDB.getZoneId(), zoneIdResp);
      assertEquals(replSpecDB.getZoneName(), zoneNameResp);

      if (zoneNameToZoneId.containsKey(zoneNameResp)) {
        assertEquals(zoneIdResp, zoneNameToZoneId.get(zoneNameResp));
      } else {
        zoneNameToZoneId.put(zoneNameResp, zoneIdResp);
      }
    }

    final JSONObject firstElectableSpecs =
        resp_getCreatedCluster_replSpecs
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);
    final JSONObject secondElectableSpecs =
        resp_getCreatedCluster_replSpecs
            .getJSONObject(1)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);
    final JSONObject thirdElectableSpecs =
        resp_getCreatedCluster_replSpecs
            .getJSONObject(2)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);

    assertEquals(
        "M30",
        firstElectableSpecs.getString(
            ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        "M40",
        secondElectableSpecs.getString(
            ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        "M50",
        thirdElectableSpecs.getString(
            ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));

    assertEquals(
        "PROVISIONED",
        firstElectableSpecs.getString(ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD));
    assertEquals(
        "PROVISIONED",
        secondElectableSpecs.getString(ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD));
    assertEquals(
        "STANDARD",
        thirdElectableSpecs.getString(ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD));

    assertEquals(
        1000, firstElectableSpecs.getNumber(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD));
    assertEquals(
        2000, secondElectableSpecs.getNumber(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD));
    assertEquals(
        3000, thirdElectableSpecs.getNumber(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD));

    final AWSHardwareSpec firstElectableSpecsDB =
        (AWSHardwareSpec)
            createdCluster_db
                .getReplicationSpecsWithShardData()
                .get(0)
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs();
    final AWSHardwareSpec secondElectableSpecsDB =
        (AWSHardwareSpec)
            createdCluster_db
                .getReplicationSpecsWithShardData()
                .get(1)
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs();
    final AWSHardwareSpec thirdElectableSpecsDB =
        (AWSHardwareSpec)
            createdCluster_db
                .getReplicationSpecsWithShardData()
                .get(2)
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs();

    assertEquals(AWSNDSInstanceSize.M30, firstElectableSpecsDB.getInstanceSize());
    assertEquals(AWSNDSInstanceSize.M40, secondElectableSpecsDB.getInstanceSize());
    assertEquals(AWSNDSInstanceSize.M50, thirdElectableSpecsDB.getInstanceSize());

    assertEquals(1000, firstElectableSpecsDB.getDiskIOPS());
    assertEquals(2000, secondElectableSpecsDB.getDiskIOPS());
    assertEquals(3000, thirdElectableSpecsDB.getDiskIOPS());

    // in the db, each has 1 numShard
    assertEquals(1, createdCluster_db.getReplicationSpecsWithShardData().get(0).getNumShards(), 0);
    assertEquals(1, createdCluster_db.getReplicationSpecsWithShardData().get(1).getNumShards(), 0);
    assertEquals(1, createdCluster_db.getReplicationSpecsWithShardData().get(2).getNumShards(), 0);

    assertEquals(3, createdCluster_db.getNumShards());
  }

  @Test
  public void testCreateCluster_globalCluster() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createAsymmetricSharded";
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createDefaultGlobalClusterView(clusterName, _group.getId()),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    final JSONObject resp_getCreatedCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final ClusterDescription createdCluster_db =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).get();

    // both representations show 2 replication specs, each M30, with right Zone Name
    assertEquals(
        2,
        resp_getCreatedCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());
    assertEquals(2, createdCluster_db.getReplicationSpecsWithShardData().size());

    final JSONObject respRS0 =
        resp_getCreatedCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0);
    final JSONObject respRS1 =
        resp_getCreatedCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(1);

    assertEquals(
        "M30",
        respRS0
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals("Zone 1", respRS0.getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        "M30",
        respRS1
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals("Zone 2", respRS1.getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertNotNull(respRS0.get(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
    assertNotNull(respRS1.get(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
    assertNotEquals(
        respRS0.get(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD),
        respRS1.get(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));

    assertEquals(
        AWSNDSInstanceSize.M30,
        createdCluster_db
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .getInstanceSize());
    assertEquals(
        "Zone 1", createdCluster_db.getReplicationSpecsWithShardData().get(0).getZoneName());
    assertEquals(
        AWSNDSInstanceSize.M30,
        createdCluster_db
            .getReplicationSpecsWithShardData()
            .get(1)
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .getInstanceSize());
    assertEquals(
        "Zone 2", createdCluster_db.getReplicationSpecsWithShardData().get(1).getZoneName());
    assertNotNull(createdCluster_db.getReplicationSpecsWithShardData().get(0).getZoneId());
    assertNotNull(createdCluster_db.getReplicationSpecsWithShardData().get(1).getZoneId());
    assertEquals(
        createdCluster_db.getReplicationSpecsWithShardData().get(0).getZoneId(),
        new ObjectId(respRS0.getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD)));
    assertEquals(
        createdCluster_db.getReplicationSpecsWithShardData().get(1).getZoneId(),
        new ObjectId(respRS1.getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD)));

    // in the db, each has 1 numShard
    assertEquals(1, createdCluster_db.getReplicationSpecsWithShardData().get(0).getNumShards(), 0);
    assertEquals(1, createdCluster_db.getReplicationSpecsWithShardData().get(1).getNumShards(), 0);
    assertEquals(2, createdCluster_db.getNumShards());
  }

  @Test
  public void testCreateCluster_redactClientLogData() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());

    // When redaction is omitted, default to false
    {
      // Create a cluster without redactClientLogData set
      final String clusterName = "redactionOmitted";
      doDigestJsonPostWithApiVersion(
          BASE_URL + _group.getId() + "/clusters",
          createDefaultSingleRegionReplicaSetView(
              clusterName, _group.getId(), "M30", "AWS", "US_EAST_1"),
          HttpStatus.SC_CREATED,
          _adminUser.getUsername(),
          _adminUserApiKey,
          V20240805_VERSION);

      assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());

      // Log redaction defaults to false if unspecified at cluster creation time.
      final JSONObject respOmitted =
          doDigestJsonGetWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertFalse(respOmitted.getBoolean("redactClientLogData"));
    }

    // When redaction is explicitly set to false, preserve the value
    {
      // Create a cluster redactClientLogData set to false.
      final String clusterName = "unredactedCluster";
      doDigestJsonPostWithApiVersion(
          BASE_URL + _group.getId() + "/clusters",
          createDefaultSingleRegionReplicaSetView(
                  clusterName, _group.getId(), "M30", "AWS", "US_EAST_1")
              .put("redactClientLogData", false),
          HttpStatus.SC_CREATED,
          _adminUser.getUsername(),
          _adminUserApiKey,
          V20240805_VERSION);

      assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());

      final JSONObject respFalse =
          doDigestJsonGetWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertFalse(respFalse.getBoolean("redactClientLogData"));
    }

    // When redaction is explicitly set to true, preserve the value
    {
      // Create a cluster redactClientLogData set to false.
      final String clusterName = "redactedCluster";
      doDigestJsonPostWithApiVersion(
          BASE_URL + _group.getId() + "/clusters",
          createDefaultSingleRegionReplicaSetView(
                  clusterName, _group.getId(), "M30", "AWS", "US_EAST_1")
              .put("redactClientLogData", true),
          HttpStatus.SC_CREATED,
          _adminUser.getUsername(),
          _adminUserApiKey,
          V20240805_VERSION);

      assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());

      final JSONObject respTrue =
          doDigestJsonGetWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertTrue(respTrue.getBoolean("redactClientLogData"));
    }
  }

  @Test
  public void testCreateCluster_globalCluster_invalid_twoZonesSamePreferredRegion() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createAsymmetricShardedInvalid";
    final JSONObject cluster = createDefaultGlobalClusterView(clusterName, _group.getId());

    // make mischief - give zone 2 the same regions (and most importantly same preferred region) as
    // zone 1
    cluster
        .getJSONArray("replicationSpecs")
        .getJSONObject(1)
        .put(
            "regionConfigs",
            cluster
                .getJSONArray("replicationSpecs")
                .getJSONObject(0)
                .getJSONArray("regionConfigs"));

    final JSONObject response =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            cluster,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals("INVALID_CLUSTER_CONFIGURATION", response.getString("errorCode"));

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));
  }

  @Test
  public void testUpdateCluster_globalCluster_invalid_twoZonesSamePreferredRegion() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createAsymmetricShardedInvalid";
    final JSONObject cluster = createDefaultGlobalClusterView(clusterName, _group.getId());

    final JSONObject createdCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            cluster,
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    // make mischief - give zone 2 the same regions (and most importantly same preferred region) as
    // zone 1
    createdCluster
        .getJSONArray("replicationSpecs")
        .getJSONObject(1)
        .put(
            "regionConfigs",
            createdCluster
                .getJSONArray("replicationSpecs")
                .getJSONObject(0)
                .getJSONArray("regionConfigs"));

    createdCluster.remove("connectionStrings");
    final JSONObject updatedCluster =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + createdCluster.getString("name"),
            createdCluster,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals("INVALID_CLUSTER_CONFIGURATION", updatedCluster.getString("errorCode"));
  }

  @Test
  public void testUpdateCluster_globalCluster_zoneNameNotRequired() {
    // unambiguously different next planning date to the one that will setASAP
    _ndsGroupDao.tryPreponeNextPlanningDate(_group.getId(), new Date(0));
    assertEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertFalse(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _group));
    assertEquals(
        List.of(),
        _eventDao.findAllForGroup(
            _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true));

    final String clusterName = "createAsymmetricGeoSharded";
    final JSONObject cluster = createDefaultGlobalClusterView(clusterName, _group.getId());

    final JSONObject createdCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            cluster,
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertTrue(
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING, null, _groupDao.findById(_group.getId())));
    assertNotEquals(new Date(0), _ndsGroupSvc.find(_group.getId()).get().getNextPlanningDate());
    assertEquals(
        List.of(
            NDSAudit.Type.INDEPENDENT_SHARD_SCALING_AVAILABLE,
            NDSAudit.Type.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED,
            NDSAudit.Type.CLUSTER_CREATED),
        _eventDao
            .findAllForGroup(
                _group.getId(), 0, 100000, null, new Date(0), new Date(), null, false, true, true)
            .stream()
            .map(Event::getEventType)
            .collect(Collectors.toList()));

    // update without zone name, ensure they're the same afterward
    final JSONArray originalReplicationSpecs = createdCluster.getJSONArray("replicationSpecs");
    final JSONArray updateReplicationSpecs = new JSONArray(originalReplicationSpecs.toString());
    for (int i = 0; i < originalReplicationSpecs.length(); i++) {
      final JSONObject replicationSpec = originalReplicationSpecs.getJSONObject(i);
      final JSONObject updateReplicationSpec = new JSONObject(replicationSpec.toString());
      updateReplicationSpec.remove(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD);
      updateReplicationSpecs.put(updateReplicationSpec);
    }
    createdCluster.remove("connectionStrings");

    final JSONObject updatedCluster =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + createdCluster.getString("name"),
            createdCluster,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray updatedReplicationSpecs = updatedCluster.getJSONArray("replicationSpecs");
    assertEquals(originalReplicationSpecs.length(), updatedReplicationSpecs.length());
    for (int i = 0; i < originalReplicationSpecs.length(); i++) {
      final JSONObject originalReplicationSpec = originalReplicationSpecs.getJSONObject(i);
      final JSONObject updatedReplicationSpec = updatedReplicationSpecs.getJSONObject(i);

      assertEquals(
          originalReplicationSpec.getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD),
          updatedReplicationSpec.getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
      assertEquals(
          originalReplicationSpec.get(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD),
          updatedReplicationSpec.get(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
      assertEquals(
          originalReplicationSpec.get(ApiAtlasReplicationSpec20240805View.ID_FIELD),
          updatedReplicationSpec.get(ApiAtlasReplicationSpec20240805View.ID_FIELD));
    }
  }

  @Test
  public void testCreateCluster_encryptionAtRest() {
    final ObjectId groupId = _group.getId();

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ENCRYPTION_AT_REST_AZURE_KEY_VAULT_PRIVATE_ENDPOINT);

    _ndsGroupDao.updateEncryptionAtRest(
        groupId,
        new NDSEncryptionAtRest(
            new NDSAWSKMS(),
            new NDSAzureKeyVault(
                true,
                new Date(),
                new Date(),
                null,
                ApiAtlasAzureKeyVaultView.CLIENT_ID,
                ApiAtlasAzureKeyVaultView.TENANT_ID,
                "SHHHH",
                SupportedAzureEnvironment.AZURE,
                "MYSUBBSCRIPTION101",
                "RG3",
                "TalkinBoutMyVault",
                "https://6ix-416-647.vault.azure.net/keys/6ix-416-647-789/t0r0nt0",
                true,
                true),
            new NDSGoogleCloudKMS()));

    _ndsGroupDao.addEncryptionAtRestPrivateEndpoint(
        groupId,
        NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT,
        AzureKeyVaultEARPrivateEndpoint.builder()
            .regionName(AzureRegionName.US_EAST_2)
            .needsUpdateAfter(new Date())
            .status(CloudProviderPrivateEndpoint.Status.ACTIVE)
            .build());

    final JSONObject resp_singleRegionNonAzureCluster =
        createClusterApiRequest(
            groupId,
            createDefaultSingleRegionEAREnabledReplicaSetView(
                "createSingleRegionEARCluster", groupId, "M10", "AWS", "US_EAST_1", "AZURE"),
            HttpStatus.SC_BAD_REQUEST);

    assertEquals(
        NDSErrorCode.ENCRYPTION_AT_REST_PROVIDER_NOT_SUPPORTED.name(),
        resp_singleRegionNonAzureCluster.getString(ApiError.ERROR_CODE_FIELD));

    final JSONObject resp_nonAzureMultiCloudCluster =
        createClusterApiRequest(
            groupId,
            createCrossCloudEAREnabledReplicaSetView(
                "createCrossCloudEARCluster", groupId, "AZURE"),
            HttpStatus.SC_BAD_REQUEST);

    assertEquals(
        NDSErrorCode.ENCRYPTION_AT_REST_PROVIDER_NOT_SUPPORTED.name(),
        resp_nonAzureMultiCloudCluster.getString(ApiError.ERROR_CODE_FIELD));

    final JSONObject resp_invalidAzureRegionCluster =
        createClusterApiRequest(
            groupId,
            createDefaultSingleRegionEAREnabledReplicaSetView(
                "createInvalidAzureRegionEARCluster",
                groupId,
                "M10",
                "AZURE",
                "US_NORTH_CENTRAL",
                "AZURE"),
            HttpStatus.SC_BAD_REQUEST);

    assertEquals(
        NDSErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTION_EXISTS_IN_REGION.name(),
        resp_invalidAzureRegionCluster.getString(ApiError.ERROR_CODE_FIELD));

    createClusterApiRequest(
        groupId,
        createDefaultSingleRegionEAREnabledReplicaSetView(
            "createInvalidAzureRegionEARCluster", groupId, "M10", "AZURE", "US_EAST_2", "AZURE"),
        HttpStatus.SC_CREATED);
  }

  @Test
  public void testCreateCluster_resourcePolicyValidation() throws Exception {
    final ObjectId groupId = _group.getId();
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);

    final Policy policy =
        new Policy(
            """
            forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
            ) when {
                context.cluster.regions.contains(ResourcePolicy::Region::"gcp:us-east1")
            };
            """);
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final JSONObject createResponse_invalidCluster =
        createClusterApiRequest(
            groupId,
            createDefaultSingleRegionReplicaSetView(
                clusterName,
                groupId,
                GCPNDSInstanceSize.M30.name(),
                CloudProvider.PROVIDER_GCP,
                GCPRegionName.EASTERN_US.getName()),
            SC_FORBIDDEN);
    assertEquals(
        ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
        createResponse_invalidCluster.getString("errorCode"));

    createClusterApiRequest(
        groupId,
        createDefaultSingleRegionReplicaSetView(
            clusterName,
            groupId,
            GCPNDSInstanceSize.M30.name(),
            CloudProvider.PROVIDER_GCP,
            GCPRegionName.WESTERN_US.getName()),
        SC_CREATED);
    assertEquals(
        Set.of(GCPRegionName.WESTERN_US),
        _clusterDescriptionUpdatesDao
            .findByName(groupId, clusterName)
            .orElseThrow()
            .getRegionNames());
  }

  @Test
  public void testCreateCluster_resourcePolicyValidation_minTLSVersion() {
    final ObjectId groupId = _group.getId();
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null,
        _organizationDao.findById(_group.getOrgId()),
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);

    final Policy policy =
        new Policy(
            """
            forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
            ) unless {
                context.cluster.minTLSVersion == ResourcePolicy::TLSVersion::"tls1_1"
            };
            """);
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final JSONObject createResponse_invalidCluster =
        createClusterApiRequest(
            groupId,
            createDefaultSingleRegionReplicaSetView(
                    clusterName,
                    groupId,
                    GCPNDSInstanceSize.M30.name(),
                    CloudProvider.PROVIDER_GCP,
                    GCPRegionName.EASTERN_US.getName())
                .put(
                    ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                    new JSONObject()
                        .put(
                            ApiAtlasClusterAdvancedConfigurationView.MINIMUM_ENABLED_TLS_PROTOCOL,
                            "TLS1_0")),
            SC_FORBIDDEN);
    assertEquals(
        ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
        createResponse_invalidCluster.getString("errorCode"));

    createClusterApiRequest(
        groupId,
        createDefaultSingleRegionReplicaSetView(
                clusterName,
                groupId,
                GCPNDSInstanceSize.M30.name(),
                CloudProvider.PROVIDER_GCP,
                GCPRegionName.WESTERN_US.getName())
            .put(
                ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                new JSONObject()
                    .put(
                        ApiAtlasClusterAdvancedConfigurationView.MINIMUM_ENABLED_TLS_PROTOCOL,
                        "TLS1_1")),
        SC_CREATED);
    assertEquals(
        Set.of(GCPRegionName.WESTERN_US),
        _clusterDescriptionUpdatesDao
            .findByName(groupId, clusterName)
            .orElseThrow()
            .getRegionNames());
    assertEquals(
        "TLS1_0",
        _clusterDescriptionProcessArgsDao
            .findMerged(clusterName, groupId)
            .orElseThrow()
            .getShardArg()
            .orElseThrow()
            .getTlsDisabledProtocols());
  }

  @Test
  public void testUpdateCluster_resourcePolicyValidation_minTLSVersion() {
    final ObjectId groupId = _group.getId();
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null,
        _organizationDao.findById(_group.getOrgId()),
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);

    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAzureClusterDescription(_group.getId(), clusterName));
    _clusterDescriptionDao.save(cluster);
    final ClusterDescriptionProcessArgsUpdatable processArgs =
        _ndsClusterSvc.getDefaultProcessArgs(cluster);
    _clusterDescriptionProcessArgsDao.save(processArgs);

    final Policy policy =
        new Policy(
            """
            forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
            ) unless {
                context.cluster.minTLSVersion == ResourcePolicy::TLSVersion::"tls1_1"
            };
            """);
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final JSONObject patchBody_TLS1_0 =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                new JSONObject()
                    .put(
                        ApiAtlasClusterAdvancedConfigurationView.MINIMUM_ENABLED_TLS_PROTOCOL,
                        "TLS1_0"));

    final JSONObject updateResponse_invalidMinTLSVersion =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            patchBody_TLS1_0,
            SC_FORBIDDEN,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
        updateResponse_invalidMinTLSVersion.getString("errorCode"));

    final JSONObject patchBody_TLS1_1 =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                new JSONObject()
                    .put(
                        ApiAtlasClusterAdvancedConfigurationView.MINIMUM_ENABLED_TLS_PROTOCOL,
                        "TLS1_1"));

    doDigestJsonPatchWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + clusterName,
        patchBody_TLS1_1,
        SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
    assertEquals(
        "TLS1_0",
        _clusterDescriptionProcessArgsDao
            .findMerged(clusterName, groupId)
            .orElseThrow()
            .getShardArg()
            .orElseThrow()
            .getTlsDisabledProtocols());
  }

  @Test
  public void testUpdateProcessArgs_resourcePolicyValidation_minTLSVersion() {
    final ObjectId groupId = _group.getId();
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null,
        _organizationDao.findById(_group.getOrgId()),
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);

    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAzureClusterDescription(_group.getId(), clusterName));
    _clusterDescriptionDao.save(cluster);
    final ClusterDescriptionProcessArgsUpdatable processArgs =
        _ndsClusterSvc.getDefaultProcessArgs(cluster);
    _clusterDescriptionProcessArgsDao.save(processArgs);

    final Policy policy =
        new Policy(
            """
            forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
            ) unless {
                context.cluster.minTLSVersion == ResourcePolicy::TLSVersion::"tls1_1"
            };
            """);
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final JSONObject patchBody_TLS1_0 =
        new JSONObject()
            .put(
                ApiAtlasClusterDescriptionProcessArgs20240805View.MINIMUM_ENABLED_TLS_PROTOCOL,
                "TLS1_0");

    final JSONObject updateResponse_invalidMinTLSVersion =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/processArgs",
            patchBody_TLS1_0,
            HttpStatus.SC_FORBIDDEN,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
        updateResponse_invalidMinTLSVersion.getString("errorCode"));

    final JSONObject patchBody_TLS1_1 =
        new JSONObject()
            .put(
                ApiAtlasClusterDescriptionProcessArgs20240805View.MINIMUM_ENABLED_TLS_PROTOCOL,
                "TLS1_1");

    doDigestJsonPatchWithApiVersion(
        BASE_URL + groupId + "/clusters/" + clusterName + "/processArgs",
        patchBody_TLS1_1,
        HttpStatus.SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
    assertEquals(
        "TLS1_0",
        _clusterDescriptionProcessArgsDao
            .findMerged(clusterName, groupId)
            .orElseThrow()
            .getShardArg()
            .orElseThrow()
            .getTlsDisabledProtocols());
  }

  @Test
  public void testCreateCluster_resourcePolicyValidation_customCipherConfig() {
    final ObjectId groupId = _group.getId();
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null,
        _organizationDao.findById(_group.getOrgId()),
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);

    final Policy policy =
        new Policy(
            """
  forbid (
    principal,
    action == ResourcePolicy::Action::"cluster.modify",
    resource
  ) unless {
    context.cluster.cipherConfigMode == ResourcePolicy::CipherConfigMode::"custom"
    && context.cluster.cipherSuites == [ResourcePolicy::CipherSuite::"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"]
  };
""");
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    {
      final JSONObject createResponse_invalidCluster =
          createClusterApiRequest(
              groupId,
              createDefaultSingleRegionReplicaSetView(
                      clusterName,
                      groupId,
                      GCPNDSInstanceSize.M30.name(),
                      CloudProvider.PROVIDER_GCP,
                      GCPRegionName.EASTERN_US.getName())
                  .put(
                      ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                      new JSONObject()
                          .put(
                              ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE,
                              TlsCipherConfigMode.DEFAULT)),
              SC_FORBIDDEN);
      assertEquals(
          ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
          createResponse_invalidCluster.getString("errorCode"));
    }

    {
      final JSONObject createResponse_invalidCluster =
          createClusterApiRequest(
              groupId,
              createDefaultSingleRegionReplicaSetView(
                      clusterName,
                      groupId,
                      GCPNDSInstanceSize.M30.name(),
                      CloudProvider.PROVIDER_GCP,
                      GCPRegionName.EASTERN_US.getName())
                  .put(
                      ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                      new JSONObject()
                          .put(
                              ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE,
                              TlsCipherConfigMode.CUSTOM)
                          .put(
                              ApiAtlasClusterAdvancedConfigurationView
                                  .CUSTOM_OPENSSL_CIPHER_CONFIG_TLS_12,
                              new JSONArray()
                                  .put(CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256))),
              SC_FORBIDDEN);
      assertEquals(
          ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
          createResponse_invalidCluster.getString("errorCode"));
    }

    createClusterApiRequest(
        groupId,
        createDefaultSingleRegionReplicaSetView(
                clusterName,
                groupId,
                GCPNDSInstanceSize.M30.name(),
                CloudProvider.PROVIDER_GCP,
                GCPRegionName.WESTERN_US.getName())
            .put(
                ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                new JSONObject()
                    .put(
                        ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE,
                        TlsCipherConfigMode.CUSTOM)
                    .put(
                        ApiAtlasClusterAdvancedConfigurationView
                            .CUSTOM_OPENSSL_CIPHER_CONFIG_TLS_12,
                        new JSONArray()
                            .put(CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384))),
        SC_CREATED);
    assertEquals(
        Set.of(GCPRegionName.WESTERN_US),
        _clusterDescriptionUpdatesDao
            .findByName(groupId, clusterName)
            .orElseThrow()
            .getRegionNames());
    assertEquals(
        CipherSuite.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384.getOpensslName(),
        _clusterDescriptionProcessArgsDao
            .findMerged(clusterName, groupId)
            .orElseThrow()
            .getShardArg()
            .orElseThrow()
            .getOpensslCipherConfig(TLSVersion.TLS1_2));
  }

  @Test
  public void testUpdateCluster_resourcePolicyValidation_customCipherConfig() {
    final ObjectId groupId = _group.getId();
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null,
        _organizationDao.findById(_group.getOrgId()),
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);

    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAzureClusterDescription(_group.getId(), clusterName));
    _clusterDescriptionDao.save(cluster);
    final ClusterDescriptionProcessArgsUpdatable processArgs =
        _ndsClusterSvc.getDefaultProcessArgs(cluster);
    _clusterDescriptionProcessArgsDao.save(processArgs);

    final Policy policy =
        new Policy(
            """
  forbid (
    principal,
    action == ResourcePolicy::Action::"cluster.modify",
    resource
  ) unless {
    context.cluster.cipherConfigMode == ResourcePolicy::CipherConfigMode::"custom"
    && context.cluster.cipherSuites == [ResourcePolicy::CipherSuite::"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"]
  };
""");
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    {
      final JSONObject patchBody_defaultInvalid =
          new JSONObject()
              .put(
                  ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                  new JSONObject()
                      .put(
                          ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE,
                          TlsCipherConfigMode.DEFAULT));

      final JSONObject updateResponse_defaultInvalid =
          doDigestJsonPatchWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              patchBody_defaultInvalid,
              SC_FORBIDDEN,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(
          ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
          updateResponse_defaultInvalid.getString("errorCode"));
    }

    final JSONObject patchBody_customValid =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                new JSONObject()
                    .put(
                        ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE,
                        TlsCipherConfigMode.CUSTOM)
                    .put(
                        ApiAtlasClusterAdvancedConfigurationView
                            .CUSTOM_OPENSSL_CIPHER_CONFIG_TLS_12,
                        new JSONArray()
                            .put(CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384)));

    doDigestJsonPatchWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + clusterName,
        patchBody_customValid,
        SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
    assertEquals(
        CipherSuite.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384.getOpensslName(),
        _clusterDescriptionProcessArgsDao
            .findMerged(clusterName, groupId)
            .orElseThrow()
            .getShardArg()
            .orElseThrow()
            .getOpensslCipherConfig(TLSVersion.TLS1_2));
  }

  @Test
  public void testUpdateProcessArgs_resourcePolicyValidation_customCipherConfig() {
    final ObjectId groupId = _group.getId();
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null,
        _organizationDao.findById(_group.getOrgId()),
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);

    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAzureClusterDescription(_group.getId(), clusterName));
    _clusterDescriptionDao.save(cluster);
    final ClusterDescriptionProcessArgsUpdatable processArgs =
        _ndsClusterSvc.getDefaultProcessArgs(cluster);
    _clusterDescriptionProcessArgsDao.save(processArgs);

    final Policy policy =
        new Policy(
            """
  forbid (
    principal,
    action == ResourcePolicy::Action::"cluster.modify",
    resource
  ) unless {
    context.cluster.cipherConfigMode == ResourcePolicy::CipherConfigMode::"custom"
    && context.cluster.cipherSuites == [ResourcePolicy::CipherSuite::"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"]
  };
""");
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final JSONObject patchBody_defaultInvalid =
        new JSONObject()
            .put(
                ApiAtlasClusterDescriptionProcessArgs20240805View.TLS_CIPHER_CONFIG_MODE,
                TlsCipherConfigMode.DEFAULT);

    final JSONObject updateResponse_defaultInvalid =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + groupId + "/clusters/" + clusterName + "/processArgs",
            patchBody_defaultInvalid,
            HttpStatus.SC_FORBIDDEN,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
        updateResponse_defaultInvalid.getString("errorCode"));

    final JSONObject patchBody_customValid =
        new JSONObject()
            .put(
                ApiAtlasClusterDescriptionProcessArgs20240805View.TLS_CIPHER_CONFIG_MODE,
                TlsCipherConfigMode.CUSTOM)
            .put(
                ApiAtlasClusterDescriptionProcessArgs20240805View
                    .CUSTOM_OPENSSL_CIPHER_CONFIG_TLS_12,
                new JSONArray().put(CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384));

    doDigestJsonPatchWithApiVersion(
        BASE_URL + groupId + "/clusters/" + clusterName + "/processArgs",
        patchBody_customValid,
        HttpStatus.SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
    assertEquals(
        CipherSuite.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384.getOpensslName(),
        _clusterDescriptionProcessArgsDao
            .findMerged(clusterName, groupId)
            .orElseThrow()
            .getShardArg()
            .orElseThrow()
            .getOpensslCipherConfig(TLSVersion.TLS1_2));
  }

  @Test
  public void testCreateCluster_replicationSpecs_autoScaling_limits() {
    final ObjectId pGroupId = _group.getId();
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, "push-your-limits");
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);

    final JSONObject m30ProvisionedIOPS =
        new JSONObject()
            .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M30")
            .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3)
            .put(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD, 600)
            .put(ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD, "PROVISIONED");
    final JSONObject computeAutoScaling =
        new JSONObject()
            .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.SCALE_DOWN_ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.PREDICTIVE_ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M40");
    final JSONObject autoScaling =
        new JSONObject()
            .put(ApiAtlasAutoScalingV15View.COMPUTE_FIELD, computeAutoScaling)
            .put(
                ApiAtlasAutoScalingV15View.DISK_GB_FIELD,
                new JSONObject().put(ApiAtlasDiskGBAutoScalingView.FieldDefs.ENABLED, true));
    final JSONObject regionConfig =
        new JSONObject()
            .put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling)
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD, m30ProvisionedIOPS)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_2")
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7);
    final JSONObject replicationSpec =
        new JSONObject()
            .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 1")
            .put(
                ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                new JSONArray().put(regionConfig));
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(replicationSpec));

    // autoScaling limits should be rejected: M10 doesn't support provisioned IOPS
    {
      computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M10");

      final JSONObject response_autoScalingLimitM10Incompatible =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              cluster,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      assertEquals(
          ApiErrorCode.AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.name(),
          response_autoScalingLimitM10Incompatible.getString("errorCode"));
      assertEquals(
          "Specified autoScaling limits ([M10, M40]) refer to instance sizes incompatible"
              + " with provisioned IOPS configurations.",
          response_autoScalingLimitM10Incompatible.getString("detail"));
    }

    // autoScaling limits should be rejected: M20 doesn't support provisioned IOPS
    {
      computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M20");

      final JSONObject response_autoScalingLimitM20Incompatible =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              cluster,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      assertEquals(
          ApiErrorCode.AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.name(),
          response_autoScalingLimitM20Incompatible.getString("errorCode"));
      assertEquals(
          "Specified autoScaling limits ([M20, M40]) refer to instance sizes incompatible"
              + " with provisioned IOPS configurations.",
          response_autoScalingLimitM20Incompatible.getString("detail"));
    }

    // autoScaling limits should be accepted: M30 supports provisioned IOPS
    {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_PREDICTIVE_AUTOSCALING);
      computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M30");

      final JSONObject response_autoScalingLimitsCompatible =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              cluster,
              HttpStatus.SC_CREATED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      final JSONObject responseReplicationSpec =
          response_autoScalingLimitsCompatible
              .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
              .getJSONObject(0);

      assertEquals(
          1,
          responseReplicationSpec
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .length());
      assertTrue(
          JSONCompare.compareJSON(
                  autoScaling,
                  responseReplicationSpec
                      .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
                      .getJSONObject(0)
                      .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD),
                  JSONCompareMode.STRICT)
              .passed());
    }

    // same set of tests for analyticsAutoScaling
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, "push-your-analytics-limits");

    final JSONObject analyticsComputeAutoScaling =
        new JSONObject()
            .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.SCALE_DOWN_ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.PREDICTIVE_ENABLED_FIELD, false)
            .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M40");

    final JSONObject analyticsAutoScaling =
        new JSONObject()
            .put(ApiAtlasAutoScalingV15View.COMPUTE_FIELD, analyticsComputeAutoScaling)
            .put(
                ApiAtlasAutoScalingV15View.DISK_GB_FIELD,
                new JSONObject().put(ApiAtlasDiskGBAutoScalingView.FieldDefs.ENABLED, true));

    regionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_AUTOSCALING_FIELD,
        analyticsAutoScaling);

    // analyticsAutoScaling limits should be rejected: M10 doesn't support provisioned IOPS
    {
      analyticsComputeAutoScaling.put(
          ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M10");

      final JSONObject response_analyticsAutoScalingLimitM10Incompatible =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              cluster,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      assertEquals(
          ApiErrorCode.ANALYTICS_AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.name(),
          response_analyticsAutoScalingLimitM10Incompatible.getString("errorCode"));
      assertEquals(
          "Specified analyticsAutoScaling limits ([M10, M40]) refer to instance sizes"
              + " incompatible with provisioned IOPS configurations.",
          response_analyticsAutoScalingLimitM10Incompatible.getString("detail"));
    }

    // analyticsAutoScaling limits should be rejected: M20 doesn't support provisioned IOPS
    {
      analyticsComputeAutoScaling.put(
          ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M20");

      final JSONObject response_analyticsAutoScalingLimitM20Incompatible =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              cluster,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      assertEquals(
          ApiErrorCode.ANALYTICS_AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.name(),
          response_analyticsAutoScalingLimitM20Incompatible.getString("errorCode"));
      assertEquals(
          "Specified analyticsAutoScaling limits ([M20, M40]) refer to instance sizes"
              + " incompatible with provisioned IOPS configurations.",
          response_analyticsAutoScalingLimitM20Incompatible.getString("detail"));
    }

    // analyticsAutoScaling limits should be accepted: M30 supports provisioned IOPS
    {
      analyticsComputeAutoScaling.put(
          ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M30");

      final JSONObject response_analyticsAutoScalingLimitsCompatible =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              cluster,
              HttpStatus.SC_CREATED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      final JSONObject responseReplicationSpec =
          response_analyticsAutoScalingLimitsCompatible
              .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
              .getJSONObject(0);

      assertEquals(
          1,
          responseReplicationSpec
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .length());
      assertTrue(
          JSONCompare.compareJSON(
                  analyticsAutoScaling,
                  responseReplicationSpec
                      .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
                      .getJSONObject(0)
                      .getJSONObject(
                          ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_AUTOSCALING_FIELD),
                  JSONCompareMode.STRICT)
              .passed());
    }

    // analyticsAutoScaling should be rejected: dedicated analytics nodes don't support predictive
    {
      analyticsComputeAutoScaling.put(
          ApiAtlasComputeAutoScalingV15View.PREDICTIVE_ENABLED_FIELD, true);
      final JSONObject response_analyticsAutoScalingPredictiveNotSupported =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              cluster,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      assertEquals(
          ApiErrorCode.ANALYTICS_NODES_NO_COMPUTE_AUTO_SCALING_PREDICTIVE.name(),
          response_analyticsAutoScalingPredictiveNotSupported.getString("errorCode"));
    }
  }

  @Test
  public void testUpdateCluster_resourcePolicyValidation() throws Exception {
    final ObjectId groupId = _group.getId();
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);

    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));
    _clusterDescriptionDao.save(cluster);

    final Policy policy =
        new Policy(
            """
            forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
            ) when {
                context.cluster.regions.contains(ResourcePolicy::Region::"gcp:us-east1")
            };
            """);
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final JSONArray replicationSpecList_withInvalidRegion = new JSONArray();
    final JSONObject replicationSpec_withInvalidRegion =
        updateProviderAndRegionForClusterReplicationSpec(
            cluster.getReplicationSpecsWithShardData().get(0), GCPRegionName.EASTERN_US);
    replicationSpecList_withInvalidRegion.put(replicationSpec_withInvalidRegion);

    final JSONObject updateResponse_invalidCluster =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            new JSONObject()
                .put(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                    replicationSpecList_withInvalidRegion),
            SC_FORBIDDEN,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
        updateResponse_invalidCluster.getString("errorCode"));

    final JSONArray replicationSpecList = new JSONArray();
    final JSONObject replicationSpec =
        updateProviderAndRegionForClusterReplicationSpec(
            cluster.getReplicationSpecsWithShardData().get(0), GCPRegionName.WESTERN_US);
    replicationSpecList.put(replicationSpec);

    doDigestJsonPatchWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + clusterName,
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                replicationSpecList),
        SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
    assertEquals(
        Set.of(GCPRegionName.WESTERN_US),
        _clusterDescriptionUpdatesDao
            .findByName(groupId, clusterName)
            .orElseThrow()
            .getRegionNames());
  }

  @Test
  public void testUpdateCluster_resourcePolicyValidation_newClusterUniqueId() throws Exception {
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);

    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));
    _clusterDescriptionDao.save(cluster);

    final Policy policy =
        new Policy(
            """
            forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
            ) when {
                resource in ResourcePolicy::Cluster::"%s" &&
                context.cluster.regions.contains(ResourcePolicy::Region::"gcp:us-east1")
            };
            """
                .formatted(cluster.getUniqueId()));
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    final JSONArray replicationSpecList_withInvalidRegion = new JSONArray();
    final JSONObject replicationSpec_withInvalidRegion =
        updateProviderAndRegionForClusterReplicationSpec(
            cluster.getReplicationSpecsWithShardData().get(0), GCPRegionName.EASTERN_US);
    replicationSpecList_withInvalidRegion.put(replicationSpec_withInvalidRegion);

    final JSONObject updateResponse_invalidCluster =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            new JSONObject()
                .put(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                    replicationSpecList_withInvalidRegion)
                .put(ApiAtlasClusterDescription20240805View.ID_FIELD, new ObjectId()),
            SC_FORBIDDEN,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
        updateResponse_invalidCluster.getString("errorCode"));
  }

  @Test
  public void testUpdateCluster_outOfComplianceUpdates() throws Exception {
    final ObjectId groupId = _group.getId();
    final String clusterName = "samba";
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);

    final ClusterDescription cluster =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));
    _clusterDescriptionDao.save(cluster);

    final Policy policy =
        new Policy(
            """
            forbid (
                principal,
                action == ResourcePolicy::Action::"cluster.modify",
                resource
            ) when {
                context.cluster.regions.contains(ResourcePolicy::Region::"aws:us-east-1")
            };
            """);
    final ObjectId policyId = new ObjectId();
    _atlasResourcePolicySvc.saveResourcePolicy(
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(policyId, _group.getOrgId()).toBuilder()
            .setPolicies(List.of(policy))
            .build(),
        Type.RESOURCE_POLICY_CREATED,
        null,
        null);

    // Cannot update cluster unless it would bring into compliance.
    final JSONObject updateResponse_invalidCluster =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            new JSONObject()
                .put(
                    ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, ClusterType.SHARDED),
            SC_FORBIDDEN,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED.name(),
        updateResponse_invalidCluster.getString("errorCode"));

    final JSONArray replicationSpecList = new JSONArray();
    final JSONObject replicationSpec =
        updateProviderAndRegionForClusterReplicationSpec(
            cluster.getReplicationSpecsWithShardData().get(0), GCPRegionName.WESTERN_US);
    replicationSpecList.put(replicationSpec);

    doDigestJsonPatchWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + clusterName,
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                replicationSpecList),
        SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
    assertEquals(
        Set.of(GCPRegionName.WESTERN_US),
        _clusterDescriptionUpdatesDao
            .findByName(groupId, clusterName)
            .orElseThrow()
            .getRegionNames());
  }

  @Test
  public void testUpdateCluster_serverlessInstance() {
    final JSONObject arbitraryUpdate =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.BACKUP_ENABLED_FIELD, true);

    // Update a serverless instance with NDS_SERVERLESS_FEATURE_ENABLED feature disabled
    final JSONObject resp0 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.SERVERLESS_CLUSTER,
            arbitraryUpdate,
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertNotNull(resp0);
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Update a serverless instance with NDS_SERVERLESS_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.SERVERLESS_CLUSTER,
            arbitraryUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API",
        resp1.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetCluster_flexCluster() {
    // Get a non-shim created/non-migrated flex cluster with NDS_FLEX_FEATURE_ENABLED feature
    // disabled and shim logic disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Get a non-shim created/non-migrated flex cluster with NDS_FLEX_FEATURE_ENABLED feature
    // enabled and shim logic disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp1.getString(ApiError.ERROR_CODE_FIELD));

    // set shim logic app setting to be DEPRECATED
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);

    // Get a non-shim created/non-migrated flex cluster with NDS_FLEX_FEATURE_ENABLED feature
    // enabled and shim logic set to DEPRECATED but our shim logic feature flag is still off
    final JSONObject resp2 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp2.getString(ApiError.ERROR_CODE_FIELD));

    // turn on group feature flag for shim logic
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);

    // Get a non-shim created/non-migrated flex cluster -- returns error
    {
      final JSONObject resp =
          doDigestJsonGetWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Get a flex cluster migrated from serverless -- returns error
    {
      final JSONObject resp =
          doDigestJsonGetWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SERVERLESS,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
          resp.getString(ApiError.ERROR_CODE_FIELD));
    }

    // Get a shim-created flex cluster -- valid response
    {
      final ClusterDescription flexCluster =
          _clusterDescriptionDao
              .findByName(_group.getId(), TestClusterNames.FLEX_CLUSTER_CREATED_FROM_SHIM_LOGIC)
              .orElseThrow();
      final JSONObject validResp =
          doDigestJsonGetWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_CREATED_FROM_SHIM_LOGIC,
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(
          flexCluster.getUniqueId().toString(),
          validResp.getString(ApiAtlasClusterDescription20240805View.ID_FIELD));
      verifyShimmedClusterResponse(
          validResp,
          FreeInstanceSize.M2.name(),
          FlexInstanceSize.FLEX.name(),
          FlexInstanceSize.FLEX.getDiskSizeGB());
    }

    // Get a flex cluster migrated from shared -- valid response
    {
      final ClusterDescription flexCluster =
          _clusterDescriptionDao
              .findByName(_group.getId(), TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED)
              .orElseThrow();
      final JSONObject validResp =
          doDigestJsonGetWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED,
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(
          flexCluster.getUniqueId().toString(),
          validResp.getString(ApiAtlasClusterDescription20240805View.ID_FIELD));
      verifyShimmedClusterResponse(
          validResp,
          FreeInstanceSize.M2.name(),
          FlexInstanceSize.FLEX.name(),
          FlexInstanceSize.FLEX.getDiskSizeGB());
    }
  }

  @Test
  public void testGetAutoScalingMode() {
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createAsymmetricShardedClusterViewMixedIOPS("thisapi", _group.getId()),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createAsymmetricShardedClusterViewMixedIOPS("nextapi", _group.getId()),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        // Look, next API!
        LocalDate.of(2025, 10, 23));

    assertJSONObjectEquals(
        new JSONObject().put("autoScalingMode", "CLUSTER_WIDE_SCALING"),
        doDigestJsonGetWithApiVersion(
            String.format(
                "%s%s/clusters/%s/autoScalingConfiguration", BASE_URL, _group.getId(), "thisapi"),
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION));

    assertJSONObjectEquals(
        new JSONObject().put("autoScalingMode", "INDEPENDENT_SHARD_SCALING"),
        doDigestJsonGetWithApiVersion(
            String.format(
                "%s%s/clusters/%s/autoScalingConfiguration", BASE_URL, _group.getId(), "nextapi"),
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION));
  }

  @Test
  public void testUpdateCluster_flexCluster() {
    final JSONObject arbitraryUpdate =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.BACKUP_ENABLED_FIELD, true);

    // Update a flex cluster with NDS_FLEX_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
            arbitraryUpdate,
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Update a flex cluster with NDS_FLEX_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
            arbitraryUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp1.getString(ApiError.ERROR_CODE_FIELD));

    // enabling our shimming logic
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);

    // Test with a non-shim created/non-migrated flex cluster -- should still return error
    {
      final JSONObject resp2 =
          doDigestJsonPatchWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
              arbitraryUpdate,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
          resp2.getString(ApiError.ERROR_CODE_FIELD));
    }

    // if we make a request with a "valid" flex cluster (e.g. one that was shimmed/migrated) we
    // should return the same error that we do for shared "TENANT_CLUSTER_UPDATE_UNSUPPORTED"
    {
      final JSONObject flexCluster =
          doDigestJsonPatchWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED,
              arbitraryUpdate,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, flexCluster.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.TENANT_CLUSTER_UPDATE_UNSUPPORTED.name(),
          flexCluster.getString(ApiError.ERROR_CODE_FIELD));

      final JSONObject flexClusterShimmed =
          doDigestJsonPatchWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_CREATED_FROM_SHIM_LOGIC,
              arbitraryUpdate,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, flexClusterShimmed.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.TENANT_CLUSTER_UPDATE_UNSUPPORTED.name(),
          flexClusterShimmed.getString(ApiError.ERROR_CODE_FIELD));

      final JSONObject tenantCluster =
          doDigestJsonPatchWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.TENANT_CLUSTER,
              arbitraryUpdate,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, tenantCluster.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.TENANT_CLUSTER_UPDATE_UNSUPPORTED.name(),
          tenantCluster.getString(ApiError.ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testDeleteCluster_flexCluster() {
    // Delete a flex cluster with NDS_FLEX_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Delete a flex cluster with NDS_FLEX_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp1.getString(ApiError.ERROR_CODE_FIELD));

    // enabling our shimming logic
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);

    // a normal flex cluster should still fail
    final JSONObject resp2 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.FLEX_CLUSTER,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API", resp2.getString(ApiError.ERROR_CODE_FIELD));

    // if we make a request with a "valid" flex cluster (e.g. one that was shimmed/migrated) our
    // request should go through
    doDigestJsonDeleteWithApiVersion(
        BASE_URL
            + _group.getId()
            + "/clusters/"
            + TestClusterNames.FLEX_CLUSTER_CREATED_FROM_SHIM_LOGIC,
        HttpStatus.SC_ACCEPTED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    doDigestJsonDeleteWithApiVersion(
        BASE_URL
            + _group.getId()
            + "/clusters/"
            + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED,
        HttpStatus.SC_ACCEPTED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
    assertTrue(
        _ndsClusterSvc
            .getMergedClusterDescription(
                _group.getId(), TestClusterNames.FLEX_CLUSTER_CREATED_FROM_SHIM_LOGIC)
            .orElseThrow()
            .isDeletedOrDeleteRequested());
    assertTrue(
        _ndsClusterSvc
            .getMergedClusterDescription(
                _group.getId(), TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED)
            .orElseThrow()
            .isDeletedOrDeleteRequested());
  }

  @Test
  public void testGetGeoSharding_flexCluster() {
    // Test with NDS_FLEX_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_FLEX_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites",
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp1.getString(ApiError.ERROR_CODE_FIELD));

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);

    // Get a non-shim created/non-migrated flex cluster -- returns error
    {
      final JSONObject resp2 =
          doDigestJsonGetWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER
                  + "/globalWrites",
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
          resp2.getString(ApiError.ERROR_CODE_FIELD));
    }
    // if we make a request with a "valid" flex cluster (e.g. one that was shimmed/migrated) we
    // should do the same as a tenant cluster -- return nothing
    final JSONObject migratedFlex =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED
                + "/globalWrites",
            SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    final JSONObject sharedTenant =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.TENANT_CLUSTER
                + "/globalWrites",
            SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertTrue(sharedTenant.similar(migratedFlex));
  }

  @Test
  public void testAddManagedNamespace_flexCluster() {
    final JSONObject managedNamespaces = new JSONObject();
    managedNamespaces.put(ApiAtlasManagedNamespacesView.DB_FIELD, "foo");
    managedNamespaces.put(ApiAtlasManagedNamespacesView.COLLECTION_FIELD, "bar");
    managedNamespaces.put(ApiAtlasManagedNamespacesView.CUSTOM_SHARD_KEY_FIELD, "foobar");

    // Test with NDS_FLEX_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonPostWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites/managedNamespaces",
            managedNamespaces,
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_FLEX_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonPostWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites/managedNamespaces",
            managedNamespaces,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp1.getString(ApiError.ERROR_CODE_FIELD));

    // enabling our shimming logic
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);
    // Get a non-shim created/non-migrated flex cluster -- returns error
    {
      final JSONObject resp2 =
          doDigestJsonPostWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER
                  + "/globalWrites/managedNamespaces",
              managedNamespaces,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
          resp2.getString(ApiError.ERROR_CODE_FIELD));
    }

    // if we make a request with a "valid" flex cluster (e.g. one that was shimmed/migrated) we
    // should do the same as a tenant cluster -- throw error
    {
      final JSONObject migratedFlex =
          doDigestJsonPostWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED
                  + "/globalWrites/managedNamespaces",
              managedNamespaces,
              HttpStatus.SC_METHOD_NOT_ALLOWED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      final JSONObject sharedTenant =
          doDigestJsonPostWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.TENANT_CLUSTER
                  + "/globalWrites/managedNamespaces",
              managedNamespaces,
              HttpStatus.SC_METHOD_NOT_ALLOWED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_METHOD_NOT_ALLOWED, migratedFlex.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.NOT_GLOBAL_WRITES_CLUSTER.name(),
          migratedFlex.getString(ApiError.ERROR_CODE_FIELD));
      assertEquals(
          sharedTenant.getString(ApiError.ERROR_CODE_FIELD),
          migratedFlex.getString(ApiError.ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testRemoveManagedNamespace_flexCluster() {
    // Test with NDS_FLEX_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites/managedNamespaces?db=foo&collection=bar",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_FLEX_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites/managedNamespaces?db=foo&collection=bar",
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp1.getString(ApiError.ERROR_CODE_FIELD));

    // enabling our shimming logic
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);

    // Get a non-shim created/non-migrated flex cluster -- returns error
    {
      final JSONObject resp2 =
          doDigestJsonDeleteWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER
                  + "/globalWrites/managedNamespaces?db=foo&collection=bar",
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
          resp2.getString(ApiError.ERROR_CODE_FIELD));
    }

    // if we make a request with a "valid" flex cluster (e.g. one that was shimmed/migrated) we
    // should do the same as a tenant cluster -- return nothing
    {
      final JSONObject migratedFlex =
          doDigestJsonDeleteWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED
                  + "/globalWrites/managedNamespaces?db=foo&collection=bar",
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      final JSONObject sharedTenant =
          doDigestJsonDeleteWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.M2_CLUSTER
                  + "/globalWrites/managedNamespaces?db=foo&collection=bar",
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertTrue(sharedTenant.similar(migratedFlex));
    }
  }

  @Test
  public void testAddCustomZoneMapping_flexCluster() {
    final JSONObject customMapping = new JSONObject();
    customMapping.put(ApiAtlasZoneMappingView.LOCATION_FIELD, "US");
    customMapping.put(ApiAtlasZoneMappingView.ZONE_FIELD, "Zone 1");
    final JSONArray customMappingList = new JSONArray();
    customMappingList.put(customMapping);
    final JSONObject customMappingView = new JSONObject();
    customMappingView.put(
        ApiAtlasCustomZoneMappingsView.CUSTOM_ZONE_MAPPINGS_FIELD, customMappingList);

    // Test with NDS_FLEX_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonPostWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites/customZoneMapping",
            customMappingView,
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_FLEX_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonPostWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites/customZoneMapping",
            customMappingView,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp1.getString(ApiError.ERROR_CODE_FIELD));

    // enabling our shimming logic
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);
    // Get a non-shim created/non-migrated flex cluster -- returns error
    {
      final JSONObject resp2 =
          doDigestJsonPostWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER
                  + "/globalWrites/customZoneMapping",
              customMappingView,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
          resp2.getString(ApiError.ERROR_CODE_FIELD));
    }

    // if we make a request with a "valid" flex cluster (e.g. one that was shimmed/migrated) we
    // should do the same as a tenant cluster -- return nothing
    {
      final JSONObject migratedFlex =
          doDigestJsonPostWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED
                  + "/globalWrites/customZoneMapping",
              customMappingView,
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      final JSONObject sharedTenant =
          doDigestJsonPostWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.M2_CLUSTER
                  + "/globalWrites/customZoneMapping",
              customMappingView,
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertTrue(sharedTenant.similar(migratedFlex));
    }
  }

  @Test
  public void testRemoveCustomZoneMapping_flexCluster() {
    // Test with NDS_FLEX_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites/customZoneMapping",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_FLEX_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonDeleteWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/globalWrites/customZoneMapping",
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp1.getString(ApiError.ERROR_CODE_FIELD));

    // enabling our shimming logic
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);
    // Get a non-shim created/non-migrated flex cluster -- returns error
    {
      final JSONObject resp2 =
          doDigestJsonDeleteWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER
                  + "/globalWrites/customZoneMapping",
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
          resp2.getString(ApiError.ERROR_CODE_FIELD));
    }
    // if we make a request with a "valid" flex cluster (e.g. one that was shimmed/migrated) we
    // should do the same as a tenant cluster -- return nothing
    {
      final JSONObject migratedFlex =
          doDigestJsonDeleteWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED
                  + "/globalWrites/customZoneMapping",
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      final JSONObject sharedTenant =
          doDigestJsonDeleteWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.M2_CLUSTER
                  + "/globalWrites/customZoneMapping",
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertTrue(sharedTenant.similar(migratedFlex));
    }
  }

  @Test
  public void testPatchProcessArgs_customCipherConfig_tls12_param() {
    getHttpUtils()
        .post()
        .path(BASE_URL + "%s/clusters", _group.getId())
        .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
        .apiVersion(V20240805_VERSION)
        .returnType(JSONObject.class)
        .data(
            createDefaultSingleRegionReplicaSetView(
                "cluster0", _group.getId(), "M10", "AWS", "US_EAST_1"))
        .expectedReturnStatus(SC_CREATED)
        .sendNonNull();

    final ClusterDescriptionProcessArgsView patchedProcessArgsView =
        new ClusterDescriptionProcessArgsView();
    patchedProcessArgsView.setTlsCipherConfigMode(TlsCipherConfigMode.CUSTOM);
    patchedProcessArgsView.setCustomOpensslCipherConfigTls12(
        List.of(
            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256));

    final ApiAtlasClusterDescriptionProcessArgs20240805View processArgsResponse =
        getHttpUtils()
            .patch()
            .path(BASE_URL + "%s/clusters/%s/processArgs", _group.getId(), "cluster0")
            .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
            .apiVersion(V20240805_VERSION)
            .returnType(ApiAtlasClusterDescriptionProcessArgs20240805View.class)
            .data(new ApiAtlasClusterDescriptionProcessArgs20240805View(patchedProcessArgsView))
            .expectedReturnStatus(HttpStatus.SC_OK)
            .sendNonNull();

    assertEquals(TlsCipherConfigMode.CUSTOM, processArgsResponse.getTlsCipherConfigMode());
    assertEquals(
        List.of(
            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256),
        processArgsResponse.getCustomOpensslCipherConfigTls12());
    assertEquals(List.of(), processArgsResponse.getCustomOpensslCipherConfigTls13());
  }

  @Test
  public void testGetProcessArgs_customCipherConfig_default_mode() {
    getHttpUtils()
        .post()
        .path(BASE_URL + "%s/clusters", _group.getId())
        .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
        .apiVersion(V20240805_VERSION)
        .returnType(JSONObject.class)
        .data(
            createDefaultSingleRegionReplicaSetView(
                "cluster0", _group.getId(), "M10", "AWS", "US_EAST_1"))
        .expectedReturnStatus(SC_CREATED)
        .sendNonNull();

    final ApiAtlasClusterDescriptionProcessArgs20240805View processArgsResponse =
        getHttpUtils()
            .get()
            .path(BASE_URL + "%s/clusters/%s/processArgs", _group.getId(), "cluster0")
            .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
            .apiVersion(V20240805_VERSION)
            .returnType(ApiAtlasClusterDescriptionProcessArgs20240805View.class)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .sendNonNull();

    assertEquals(TlsCipherConfigMode.DEFAULT, processArgsResponse.getTlsCipherConfigMode());
  }

  @Test
  public void testGetProcessArgs_flexCluster() {
    // Test with NDS_FLEX_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/processArgs",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_FLEX_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/processArgs",
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
        resp1.getString(ApiError.ERROR_CODE_FIELD));

    // enabling our shimming logic
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);

    // Get a non-shim created/non-migrated flex cluster -- returns error
    {
      final JSONObject resp2 =
          doDigestJsonGetWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER
                  + "/processArgs",
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          ApiErrorCode.CANNOT_USE_FLEX_CLUSTER_IN_CLUSTER_API.name(),
          resp2.getString(ApiError.ERROR_CODE_FIELD));
    }
    // if we make a request with a "valid" flex cluster (e.g. one that was shimmed/migrated) we
    // should do the same as a tenant cluster -- return empty
    {
      final JSONObject migratedFlex =
          doDigestJsonGetWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED
                  + "/processArgs",
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      final JSONObject sharedTenant =
          doDigestJsonGetWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.M2_CLUSTER
                  + "/processArgs",
              SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertTrue(sharedTenant.similar(migratedFlex));
    }
  }

  @Test
  public void testUpdateProcessArgs_flexCluster() {
    // Test with NDS_FLEX_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/processArgs",
            new JSONObject(),
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_FLEX_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER
                + "/processArgs",
            new JSONObject(),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "FLEX_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED", resp1.getString(ApiError.ERROR_CODE_FIELD));
    // enabling our shimming logic
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);

    // Get a non-shim created/non-migrated flex cluster -- returns error
    {
      final JSONObject resp2 =
          doDigestJsonPatchWithApiVersion(
              BASE_URL
                  + _group.getId()
                  + "/clusters/"
                  + TestClusterNames.FLEX_CLUSTER
                  + "/processArgs",
              new JSONObject(),
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
      assertEquals(
          "FLEX_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED", resp2.getString(ApiError.ERROR_CODE_FIELD));
    }

    // if we make a request with a "valid" flex cluster (e.g. one that was shimmed/migrated) we will
    // STILL return the FLEX_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED error
    final JSONObject resp2 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.FLEX_CLUSTER_MIGRATED_FROM_SHARED
                + "/processArgs",
            new JSONObject(),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp2.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        ApiErrorCode.FLEX_CLUSTER_PROCESS_ARGS_NOT_SUPPORTED.name(),
        resp2.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetProcessArgs_serverlessCluster() {
    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.SERVERLESS_CLUSTER
                + "/processArgs",
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonGetWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.SERVERLESS_CLUSTER
                + "/processArgs",
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_USE_SERVERLESS_INSTANCE_IN_CLUSTER_API",
        resp1.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateProcessArgs_serverlessCluster() {
    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature disabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp0 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.SERVERLESS_CLUSTER
                + "/processArgs",
            new JSONObject(),
            HttpStatus.SC_NOT_FOUND,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_NOT_FOUND, resp0.getInt(ApiError.ERROR_FIELD));
    assertEquals("CLUSTER_NOT_FOUND", resp0.getString(ApiError.ERROR_CODE_FIELD));

    // Test with NDS_SERVERLESS_FEATURE_ENABLED feature enabled
    _appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.TRUE.toString(),
        AppSettings.SettingType.MEMORY);
    final JSONObject resp1 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL
                + _group.getId()
                + "/clusters/"
                + TestClusterNames.SERVERLESS_CLUSTER
                + "/processArgs",
            new JSONObject(),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(HttpStatus.SC_BAD_REQUEST, resp1.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "SERVERLESS_INSTANCE_PROCESS_ARGS_NOT_SUPPORTED",
        resp1.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateGeoShardedCluster() throws Exception {
    final String clusterName = "updateGeoShardedAsymmetricCluster";
    final JSONObject cluster = createDefaultGlobalClusterView(clusterName, _group.getId());
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        cluster,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _groupDao.findById(_group.getId()),
        null,
        FeatureFlag.CONTINUOUS_BACKUP_ALLOWED_FOR_NEW_AWS_CLUSTERS);

    // Basic test
    final JSONObject enableBackup = new JSONObject();
    enableBackup.put(ApiAtlasClusterDescription20240805View.BACKUP_ENABLED_FIELD, true);
    final JSONObject resp1 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            enableBackup,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertTrue(
        resp1.toString(),
        resp1.getBoolean(ApiAtlasClusterDescription20240805View.BACKUP_ENABLED_FIELD));
    assertEquals(
        2,
        resp1
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());

    final Object zoneId0 =
        resp1
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .get("zoneId");
    final Object zoneId1 =
        resp1
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(1)
            .get("zoneId");
    assertNotNull(zoneId0);
    assertNotNull(zoneId1);
    assertNotEquals(zoneId0, zoneId1);

    // Update replication specs (add zone)
    final JSONObject addZone = new JSONObject();
    {
      final JSONObject replicationSpec = new JSONObject();
      final JSONArray regionConfigs = new JSONArray();
      final JSONObject regionConfig = new JSONObject();
      final JSONObject autoScaling =
          resp1
              .getJSONArray("replicationSpecs")
              .getJSONObject(0)
              .getJSONArray("regionConfigs")
              .getJSONObject(0)
              .getJSONObject("autoScaling");

      final JSONObject hardwareSpec = new JSONObject();
      hardwareSpec.put("nodeCount", 3);
      hardwareSpec.put("instanceSize", "M10");

      regionConfig.put("regionName", "AP_EAST_1");
      regionConfig.put("providerName", "AWS");
      regionConfig.put("priority", 7);
      regionConfig.put("electableSpecs", hardwareSpec);
      regionConfig.put("autoScaling", autoScaling);

      regionConfigs.put(regionConfig);

      replicationSpec.put("zoneName", "Asia");
      replicationSpec.put("regionConfigs", regionConfigs);

      addZone.put("replicationSpecs", resp1.getJSONArray("replicationSpecs"));
      addZone.getJSONArray("replicationSpecs").put(replicationSpec);
    }

    final JSONObject resp2 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            addZone,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        3,
        resp2
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());
    final Object zoneId2 =
        resp2
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(2)
            .get("zoneId");
    assertNotNull(zoneId2);
    assertNotEquals(zoneId0, zoneId2);
    assertNotEquals(zoneId0, zoneId2);
    assertEquals(
        zoneId0,
        resp2
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .get("zoneId"));
    assertEquals(
        zoneId1,
        resp2
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(1)
            .get("zoneId"));

    // Update replication specs (rename zone of one replication spec, where all zones were unique)
    final JSONObject renameZone = new JSONObject();
    {
      renameZone.put("replicationSpecs", resp2.getJSONArray("replicationSpecs"));
      renameZone.getJSONArray("replicationSpecs").getJSONObject(1).put("zoneName", "Zone Europe");
    }

    final JSONObject resp3 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            renameZone,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        3,
        resp3
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());
    assertEquals(
        renameZone
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(1)
            .get("zoneName"),
        resp3
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(1)
            .get("zoneName"));
    assertEquals(
        zoneId0,
        resp3
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .get("zoneId"));
    assertEquals(
        zoneId1,
        resp3
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(1)
            .get("zoneId"));
    assertEquals(
        zoneId2,
        resp3
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(2)
            .get("zoneId"));

    // Update replication specs (re-id zone)
    final JSONObject changeIdInZone = new JSONObject();
    {
      changeIdInZone.put("replicationSpecs", resp2.getJSONArray("replicationSpecs"));
      changeIdInZone
          .getJSONArray("replicationSpecs")
          .getJSONObject(1)
          .put("id", ObjectId.get().toString());
    }

    final JSONObject error4 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            changeIdInZone,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals("INVALID_CLUSTER_CONFIGURATION", error4.getString("errorCode"));
    assertEquals(
        List.of("Unknown ID for replicationSpec"), error4.getJSONArray("parameters").toList());

    // Get Cluster Description
    final JSONObject resp4 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        3,
        resp4
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());

    // Update replication specs (delete zone)
    final JSONObject deleteZone = new JSONObject();
    {
      deleteZone.put("replicationSpecs", resp4.getJSONArray("replicationSpecs"));
      deleteZone.getJSONArray("replicationSpecs").remove(2);
    }

    final JSONObject resp5 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            deleteZone,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        2,
        resp5
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());

    // Cannot update geo sharded cluster to be a replica set
    final JSONObject updateToReplicaSet = new JSONObject();
    updateToReplicaSet.put("clusterType", ClusterDescription.ClusterType.REPLICASET);

    final JSONObject toRs =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            updateToReplicaSet,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(HttpStatus.SC_BAD_REQUEST, toRs.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        toRs.toString(),
        "CANNOT_CHANGE_GEOSHARDED_CLUSTER_TYPE",
        toRs.getString(ApiError.ERROR_CODE_FIELD));

    // Cannot update geo sharded cluster to be a regular sharded cluster
    final JSONObject updateToSharded = new JSONObject();
    updateToSharded.put("clusterType", ClusterDescription.ClusterType.SHARDED);

    final JSONObject toSharded =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            updateToSharded,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(HttpStatus.SC_BAD_REQUEST, toSharded.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CANNOT_CHANGE_GEOSHARDED_CLUSTER_TYPE", toSharded.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateCluster_asymmetricShardedCluster_asymmetricIOPS() {
    final String clusterName = "updateAsymmetricSharded";

    final JSONObject resp_createCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createAsymmetricShardedClusterViewMixedIOPS(clusterName, _group.getId()),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray resp_createCluster_replSpecs =
        resp_createCluster.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);

    {
      final JSONObject firstElectableSpecs =
          resp_createCluster_replSpecs
              .getJSONObject(0)
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .getJSONObject(0)
              .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);
      final JSONObject secondElectableSpecs =
          resp_createCluster_replSpecs
              .getJSONObject(1)
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .getJSONObject(0)
              .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);
      final JSONObject thirdElectableSpecs =
          resp_createCluster_replSpecs
              .getJSONObject(2)
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .getJSONObject(0)
              .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);

      assertEquals(
          "M30",
          firstElectableSpecs.getString(
              ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
      assertEquals(
          "M40",
          secondElectableSpecs.getString(
              ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
      assertEquals(
          "M50",
          thirdElectableSpecs.getString(
              ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));

      assertEquals(
          "PROVISIONED",
          firstElectableSpecs.getString(ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD));
      assertEquals(
          "PROVISIONED",
          secondElectableSpecs.getString(
              ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD));
      assertEquals(
          "STANDARD",
          thirdElectableSpecs.getString(ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD));

      assertEquals(
          1000, firstElectableSpecs.getNumber(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD));
      assertEquals(
          2000,
          secondElectableSpecs.getNumber(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD));
      assertEquals(
          3000, thirdElectableSpecs.getNumber(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD));
    }

    // Update replication specs (change electable hardware in first shard)
    final JSONObject updateHardware = new JSONObject();

    final JSONObject replicationSpec = resp_createCluster_replSpecs.getJSONObject(0);
    final JSONArray regionConfigs = new JSONArray();
    final JSONObject regionConfig = new JSONObject();
    final JSONObject autoScaling =
        replicationSpec.getJSONArray("regionConfigs").getJSONObject(0).getJSONObject("autoScaling");

    final JSONObject hardwareSpec = new JSONObject();
    hardwareSpec.put("nodeCount", 5);
    hardwareSpec.put("instanceSize", "M60");
    hardwareSpec.put("ebsVolumeType", "PROVISIONED");
    hardwareSpec.put("diskIOPS", 1500);

    regionConfig.put("regionName", "US_EAST_1");
    regionConfig.put("providerName", "AWS");
    regionConfig.put("priority", 7);
    regionConfig.put("electableSpecs", hardwareSpec);
    regionConfig.put("autoScaling", autoScaling);

    regionConfigs.put(regionConfig);
    replicationSpec.put("regionConfigs", regionConfigs);

    updateHardware.put("replicationSpecs", resp_createCluster_replSpecs);

    final JSONObject updateFirstShardHardware_nodeCountInvalid =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            updateHardware,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "ASYMMETRIC_REGION_TOPOLOGY_IN_ZONE",
        updateFirstShardHardware_nodeCountInvalid.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "All shards in the same zone must have the same region topology.",
        updateFirstShardHardware_nodeCountInvalid.getString(ApiError.DETAIL_FIELD));

    hardwareSpec.put("nodeCount", 3);
    regionConfig.put("regionName", "US_WEST_2");
    final JSONObject updateFirstShardHardware_regionInvalid =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            updateHardware,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "ASYMMETRIC_REGION_TOPOLOGY_IN_ZONE",
        updateFirstShardHardware_regionInvalid.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "All shards in the same zone must have the same region topology.",
        updateFirstShardHardware_regionInvalid.getString(ApiError.DETAIL_FIELD));

    regionConfig.put("regionName", "US_EAST_1");

    final JSONObject updateFirstShardHardware =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            updateHardware,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    final JSONArray updateFirstShardHardware_replSpecs =
        updateFirstShardHardware.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);

    final JSONObject firstElectableSpecs_update =
        updateFirstShardHardware_replSpecs
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);
    final JSONObject secondElectableSpecs_update =
        updateFirstShardHardware_replSpecs
            .getJSONObject(1)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);
    final JSONObject thirdElectableSpecs_update =
        updateFirstShardHardware_replSpecs
            .getJSONObject(2)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);

    // unchanged
    assertEquals(
        "M40",
        secondElectableSpecs_update.getString(
            ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        "M50",
        thirdElectableSpecs_update.getString(
            ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));

    assertEquals(
        "M60",
        firstElectableSpecs_update.getString(
            ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        "PROVISIONED",
        firstElectableSpecs_update.getString(
            ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD));
    assertEquals(
        1500,
        firstElectableSpecs_update.getNumber(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD));
    assertEquals(
        3, firstElectableSpecs_update.getInt(ApiAtlasAWSHardwareSpec20240805View.NODE_COUNT_FIELD));
    final JSONArray updateFirstShardHardware_firstRegionConfigs =
        updateFirstShardHardware_replSpecs
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD);
    assertEquals(1, updateFirstShardHardware_firstRegionConfigs.length());
    assertEquals(
        "US_EAST_1",
        updateFirstShardHardware_firstRegionConfigs
            .getJSONObject(0)
            .getString(ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD));
    assertEquals(
        "AWS",
        updateFirstShardHardware_firstRegionConfigs
            .getJSONObject(0)
            .getString(ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD));
    assertEquals(
        7,
        updateFirstShardHardware_firstRegionConfigs
            .getJSONObject(0)
            .getInt(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD));
  }

  @Test
  public void testUpdateCluster_crossCloudDedicated_terminationProtectionEnabled()
      throws SvcException {
    final AppUser clusterManager =
        MmsFactory.createUserWithRoleInGroup(
            _groupDao.findById(_group.getId()),
            "<EMAIL>",
            Role.GROUP_CLUSTER_MANAGER);
    final String clusterManagerUserApiKey =
        MmsFactory.generateUserApiKey(clusterManager.getId(), "cluster manager key").getKey();

    final String clusterName = "updateClusterTerminationProtection";
    final JSONObject clusterDescription =
        createDefaultCrossCloudReplicaSetView(clusterName, _group.getId());

    final JSONObject createResponse =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            clusterDescription,
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // termination protection field defaults to false if not set
    assertFalse(
        createResponse.getBoolean(
            ApiAtlasClusterDescription20240805View.TERMINATION_PROTECTION_ENABLED_FIELD));

    final String patchUrl = BASE_URL + _group.getId() + "/clusters/" + clusterName;
    final JSONObject updateReq =
        new JSONObject()
            .put(ApiAtlasClusterDescription20240805View.TERMINATION_PROTECTION_ENABLED_FIELD, true);

    // FAIL: Cluster manager can edit cluster but cannot set Termination Protection
    {
      final JSONObject updateResponse =
          doDigestJsonPatchWithApiVersion(
              patchUrl,
              updateReq,
              HttpStatus.SC_UNAUTHORIZED,
              clusterManager.getUsername(),
              clusterManagerUserApiKey,
              V20240805_VERSION);
      assertEquals(
          ApiErrorCode.USER_UNAUTHORIZED_ATLAS_TERMINATION_PROTECTION.getMessage(),
          updateResponse.getString(ApiError.DETAIL_FIELD));
    }

    // SUCCESS
    {
      final JSONObject updateResponse =
          doDigestJsonPatchWithApiVersion(
              patchUrl,
              updateReq,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertTrue(
          updateResponse.getBoolean(
              ApiAtlasClusterDescription20240805View.TERMINATION_PROTECTION_ENABLED_FIELD));
    }
  }

  @Test
  public void testUpdateCluster_asymmetricShardedCluster_inconsistentDiskSize() {
    final String clusterName = "updateISSWithInconsistentDiskSize";

    final JSONObject resp_createCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createAsymmetricShardedClusterView(clusterName, _group.getId()),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray resp_createCluster_replSpecs =
        resp_createCluster.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);

    final JSONObject firstElectableSpecs =
        resp_createCluster_replSpecs
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);

    final int diskSize =
        firstElectableSpecs.getInt(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD);
    firstElectableSpecs.put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, diskSize + 10);

    final JSONObject updateWithInconsistentDiskSize = new JSONObject();
    updateWithInconsistentDiskSize.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        resp_createCluster_replSpecs);

    final JSONObject response_inconsistentDiskSize =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            updateWithInconsistentDiskSize,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.DISK_SIZE_GB_INCONSISTENT.name(),
        response_inconsistentDiskSize.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateCluster_asymmetricShardedCluster_updateDiskSize() {
    final String clusterName = "updateISSWithDiskSize";

    final JSONObject resp_createCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createAsymmetricShardedClusterView(clusterName, _group.getId()),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray resp_createCluster_replSpecs =
        resp_createCluster.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);

    // test assumptions
    assertEquals(2, resp_createCluster_replSpecs.length());

    final JSONObject firstElectableSpecs =
        resp_createCluster_replSpecs
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);

    final double invalidDiskSize = 50.5;
    final int validDiskSize = 50;
    firstElectableSpecs.put(
        ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, invalidDiskSize);

    final JSONObject diskSizeUpdate = new JSONObject();
    diskSizeUpdate.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        resp_createCluster_replSpecs);

    final JSONObject response_inconsistentDiskSize =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            diskSizeUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.DISK_SIZE_GB_INCONSISTENT.name(),
        response_inconsistentDiskSize.getString(ApiError.ERROR_CODE_FIELD));

    final JSONObject firstAnalyticsSpecs =
        resp_createCluster_replSpecs
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD);
    final JSONObject firstReadOnlySpecs =
        resp_createCluster_replSpecs
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD);
    final JSONObject secondElectableSpecs =
        resp_createCluster_replSpecs
            .getJSONObject(1)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);
    final JSONObject secondAnalyticsSpecs =
        resp_createCluster_replSpecs
            .getJSONObject(1)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD);
    final JSONObject secondReadOnlySpecs =
        resp_createCluster_replSpecs
            .getJSONObject(1)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD);
    firstAnalyticsSpecs.put(
        ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, invalidDiskSize);
    firstReadOnlySpecs.put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, invalidDiskSize);
    secondElectableSpecs.put(
        ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, invalidDiskSize);
    secondAnalyticsSpecs.put(
        ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, invalidDiskSize);
    secondReadOnlySpecs.put(
        ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, invalidDiskSize);

    final JSONObject response_consistentDiskSize_invalid =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            diskSizeUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.INVALID_ATTRIBUTE.name(),
        response_consistentDiskSize_invalid.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "Disk size is not a positive whole number",
        response_consistentDiskSize_invalid.getJSONArray(ApiError.PARAMETERS_FIELD).get(0));

    firstElectableSpecs.put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, validDiskSize);
    firstAnalyticsSpecs.put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, validDiskSize);
    firstReadOnlySpecs.put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, validDiskSize);
    secondElectableSpecs.put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, validDiskSize);
    secondAnalyticsSpecs.put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, validDiskSize);
    secondReadOnlySpecs.put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, validDiskSize);

    final JSONObject response_consistentDiskSize_valid =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            diskSizeUpdate,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    final JSONArray resp_consistentDiskSize_replSpecs =
        response_consistentDiskSize_valid.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);

    for (int i = 0; i < resp_consistentDiskSize_replSpecs.length(); i++) {
      final JSONObject electableSpecs =
          resp_consistentDiskSize_replSpecs
              .getJSONObject(i)
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .getJSONObject(0)
              .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD);
      final JSONObject analyticsSpecs =
          resp_consistentDiskSize_replSpecs
              .getJSONObject(i)
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .getJSONObject(0)
              .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD);
      final JSONObject readOnlySpecs =
          resp_consistentDiskSize_replSpecs
              .getJSONObject(i)
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .getJSONObject(0)
              .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD);
      assertEquals(
          validDiskSize,
          electableSpecs.getInt(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD));
      assertEquals(
          validDiskSize,
          readOnlySpecs.getInt(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD));
      assertEquals(
          validDiskSize,
          analyticsSpecs.getInt(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD));
    }
  }

  @Test
  public void testUpdateCluster_tryUpdateName() {
    final String clusterName = "neverChange";
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        createAsymmetricShardedClusterView(clusterName, _group.getId()),
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    final JSONObject nameChange = new JSONObject();
    nameChange.put("name", "badName");

    final JSONObject resp_NameChange =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            nameChange,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "CLUSTER_CANNOT_CHANGE_NAME", resp_NameChange.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateCluster_removeShards() {
    final String clusterName = "removeShard";

    final JSONObject resp_createCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createAsymmetricShardedClusterView(clusterName, _group.getId()),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray resp_createCluster_replSpecs =
        resp_createCluster.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);

    // test assumptions
    assertEquals(2, resp_createCluster_replSpecs.length());
    final ObjectId externalId =
        new ObjectId(
            resp_createCluster_replSpecs
                .getJSONObject(0)
                .getString(ApiAtlasReplicationSpec20240805View.ID_FIELD));
    assertNotEquals(
        externalId,
        resp_createCluster_replSpecs
            .getJSONObject(1)
            .get(ApiAtlasReplicationSpec20240805View.ID_FIELD));
    final JSONObject updateShardRemoval = new JSONObject();
    updateShardRemoval.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray(List.of(resp_createCluster_replSpecs.getJSONObject(0))));

    final JSONObject resp_shardRemoval =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            updateShardRemoval,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    final JSONArray resp_updateCluster_replSpecs =
        resp_shardRemoval.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
    assertEquals(1, resp_updateCluster_replSpecs.length());
    assertEquals(
        externalId.toString(),
        resp_updateCluster_replSpecs
            .getJSONObject(0)
            .getString(ApiAtlasReplicationSpec20240805View.ID_FIELD));
  }

  @Test
  public void testUpdateCluster_replicationSpecs_emptyRegionConfig() {
    final String clusterName = "clusterwithEmptyRegionConfig";
    final JSONObject resp_createCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createAsymmetricShardedClusterView(clusterName, _group.getId()),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray resp_createCluster_replSpecs =
        resp_createCluster.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);

    // test assumptions
    assertEquals(2, resp_createCluster_replSpecs.length());
    final ObjectId externalId =
        new ObjectId(
            resp_createCluster_replSpecs
                .getJSONObject(0)
                .getString(ApiAtlasReplicationSpec20240805View.ID_FIELD));
    assertNotEquals(
        externalId,
        resp_createCluster_replSpecs
            .getJSONObject(1)
            .get(ApiAtlasReplicationSpec20240805View.ID_FIELD));
    final JSONObject replicationSpecsEmptyRegionUpdate =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray().put(getCrossCloudReplicationSpecEmptyRegionConfig()));

    final JSONObject resp_replicationSpecsEmptyRegionUpdate =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            replicationSpecsEmptyRegionUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ApiErrorCode.INVALID_REGION_CONFIG_NODE_COUNT.name(),
        resp_replicationSpecsEmptyRegionUpdate.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateCluster_toGeoSharded() {
    final String clusterName = "clusterUpdateToGeoSharded";
    final JSONObject resp_createCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultSingleRegionReplicaSetView(
                clusterName, _group.getId(), "M10", "AWS", "US_EAST_1"),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // test assumptions
    assertEquals(
        1,
        resp_createCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());
    assertEquals(
        "REPLICASET",
        resp_createCluster.getString(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD));

    // This isn't valid as it has the same highest priority region in two zones
    final JSONObject replicationSpecForZone1 =
        getDefaultCrossCloudReplicationSpec()
            .put(ApiAtlasReplicationSpecView.ZONE_NAME_FIELD, "Zone 1");
    final JSONObject replicationSpecForZone2 =
        getDefaultCrossCloudReplicationSpec()
            .put(ApiAtlasReplicationSpecView.ZONE_NAME_FIELD, "Zone 2");

    final JSONObject invalidClusterUpdate =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray().put(replicationSpecForZone1).put(replicationSpecForZone2))
            .put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "GEOSHARDED");
    final JSONObject response_invalidUpgradeToGeoSharding_zoneTopology =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            invalidClusterUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ApiErrorCode.INVALID_CLUSTER_CONFIGURATION.name(),
        response_invalidUpgradeToGeoSharding_zoneTopology.getString(ApiError.ERROR_CODE_FIELD));

    replicationSpecForZone2
        .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
        .getJSONObject(0)
        .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "EU_WEST_1");

    final JSONObject response_invalidUpgradeToGeoSharding_goingDirectToMultipleShards =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            invalidClusterUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ApiErrorCode.INVALID_SHARD_COUNT_FOR_SHARDING_UPGRADE.name(),
        response_invalidUpgradeToGeoSharding_goingDirectToMultipleShards.getString(
            ApiError.ERROR_CODE_FIELD));

    final JSONObject upgradeToGeoSharded =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray().put(replicationSpecForZone1))
            .put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "GEOSHARDED");
    final JSONObject response_upgradeToGeoSharding =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            upgradeToGeoSharded,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        upgradeToGeoSharded
            .get(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD)
            .toString(),
        response_upgradeToGeoSharding.getString(
            ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD));
  }

  @Test
  public void testUpdateCluster_toSharded() {
    // set the net work permission to be arbitrarily more than the limit to avoid running into
    // CROSS_REGION_NETWORK_PERMISSIONS_LIMIT_EXCEEDED
    _ndsGroupDao.setLimit(
        _group.getId(),
        Limits.FieldDefs.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getValue(),
        1500);

    final String clusterName = "clusterUpdateToSharded";
    final JSONObject resp_createCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultSingleRegionReplicaSetView(
                clusterName, _group.getId(), "M10", "AWS", "US_EAST_1"),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    // test assumptions
    assertEquals(
        1,
        resp_createCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());
    assertEquals(
        "REPLICASET",
        resp_createCluster.getString(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD));
    final String originalZoneName =
        resp_createCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD);

    final JSONObject shardConfig = getDefaultCrossCloudReplicationSpec();
    final JSONArray replicationSpecsForUpdate =
        new JSONArray()
            .put(new JSONObject(shardConfig.toString()))
            .put(new JSONObject(shardConfig.toString()));
    final JSONObject shardCardinalityUpdate =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                replicationSpecsForUpdate)
            .put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");

    final JSONObject response_invalidShardingUpdateWrongType =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            shardCardinalityUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.INVALID_CLUSTER_CONFIGURATION.name(),
        response_invalidShardingUpdateWrongType.getString(ApiError.ERROR_CODE_FIELD));

    shardCardinalityUpdate.put(
        ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    final JSONObject response_invalidShardingUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            shardCardinalityUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.INVALID_SHARD_COUNT_FOR_SHARDING_UPGRADE.name(),
        response_invalidShardingUpdate.getString(ApiError.ERROR_CODE_FIELD));

    final JSONObject upgradeToSharded =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray().put(shardConfig))
            .put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    final JSONObject response_upgradeToSharded =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            upgradeToSharded,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        upgradeToSharded.get(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD).toString(),
        response_upgradeToSharded.getString(
            ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD));
    assertEquals(
        1,
        response_upgradeToSharded
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());

    final JSONObject response_upgradeToMoreShards_No_ZoneNames_No_Ids =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            shardCardinalityUpdate,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray response_upgradeToMoreShards_No_ZoneNames_No_Ids_Specs =
        response_upgradeToMoreShards_No_ZoneNames_No_Ids.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
    assertEquals(2, response_upgradeToMoreShards_No_ZoneNames_No_Ids_Specs.length());
    {
      final JSONObject firstSpec =
          response_upgradeToMoreShards_No_ZoneNames_No_Ids_Specs.getJSONObject(0);
      final String firstId = firstSpec.getString("id");
      final String firstZoneId = firstSpec.getString("zoneId");
      final String firstZoneName = firstSpec.getString("zoneName");

      final JSONObject secondSpec =
          response_upgradeToMoreShards_No_ZoneNames_No_Ids_Specs.getJSONObject(1);
      final String secondId = secondSpec.getString("id");
      final String secondZoneId = secondSpec.getString("zoneId");
      final String secondZoneName = secondSpec.getString("zoneName");

      assertEquals(originalZoneName, firstZoneName);
      assertEquals(firstZoneName, secondZoneName);

      assertNotNull(firstZoneId);
      assertEquals(firstZoneId, secondZoneId);

      assertNotNull(firstId);
      assertNotEquals(firstId, secondId);

      // Attach existingIds
      replicationSpecsForUpdate
          .getJSONObject(0)
          .put(ApiAtlasReplicationSpec20240805View.ID_FIELD, firstId);
      replicationSpecsForUpdate
          .getJSONObject(1)
          .put(ApiAtlasReplicationSpec20240805View.ID_FIELD, secondId);
    }

    // create new spec for 3 shards (with id'd fields at the beginning)
    replicationSpecsForUpdate.put(new JSONObject(shardConfig.toString()));

    final JSONObject response_upgradeToMoreShards =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            shardCardinalityUpdate,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        3,
        response_upgradeToMoreShards
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());
    assertEquals(
        response_upgradeToMoreShards
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getString(ApiAtlasReplicationSpec20240805View.ID_FIELD),
        response_upgradeToSharded
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getString(ApiAtlasReplicationSpec20240805View.ID_FIELD));

    final Set<String> originalReplicationSpecIDs =
        response_upgradeToMoreShards
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .toList()
            .stream()
            .map(HashMap.class::cast)
            .map(obj -> obj.get(ApiAtlasReplicationSpec20240805View.ID_FIELD))
            .map(String.class::cast)
            .collect(Collectors.toSet());

    // add existing shards to update - here put id'd fields at the end since order shouldn't matter
    replicationSpecsForUpdate.remove(2);
    replicationSpecsForUpdate.remove(1);
    replicationSpecsForUpdate.remove(0);
    assertEquals(new JSONArray().toList(), replicationSpecsForUpdate.toList());

    replicationSpecsForUpdate.putAll(
        List.of(
            new JSONObject(shardConfig.toString()),
            new JSONObject(shardConfig.toString()),
            response_upgradeToMoreShards
                .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                .get(0),
            response_upgradeToMoreShards
                .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                .get(1),
            response_upgradeToMoreShards
                .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
                .get(2)));
    final JSONObject response_upgradeTo5Shards =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            shardCardinalityUpdate,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        5,
        response_upgradeTo5Shards
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .length());

    final Set<String> newReplicationSpecIDS =
        response_upgradeTo5Shards
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .toList()
            .stream()
            .map(HashMap.class::cast)
            .map(obj -> obj.get(ApiAtlasReplicationSpec20240805View.ID_FIELD))
            .map(String.class::cast)
            .peek(id -> assertFalse(StringUtils.isBlank(id)))
            .collect(Collectors.toSet());

    assertTrue(newReplicationSpecIDS.containsAll(originalReplicationSpecIDs));
  }

  @Test
  public void testUpdateCluster_reconfigureGeoShardedCluster() {
    final String clusterName = "clusterReconfigureGeosharded";
    final JSONObject resp_createCluster =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultGlobalClusterView(clusterName, _group.getId()),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray originalReplicationSpecs =
        resp_createCluster.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);

    // test assumptions
    assertEquals(2, originalReplicationSpecs.length());
    final JSONObject firstReplicationSpec =
        new JSONObject(originalReplicationSpecs.getJSONObject(0).toString());
    final String firstZoneId =
        firstReplicationSpec.getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);
    final String firstZoneName =
        firstReplicationSpec.getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD);
    final String firstId =
        firstReplicationSpec.getString(ApiAtlasReplicationSpec20240805View.ID_FIELD);
    final JSONObject secondReplicationSpec =
        new JSONObject(originalReplicationSpecs.getJSONObject(1).toString());
    final String secondZoneId =
        secondReplicationSpec.getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);
    final String secondZoneName =
        secondReplicationSpec.getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD);
    final String secondId =
        secondReplicationSpec.getString(ApiAtlasReplicationSpec20240805View.ID_FIELD);
    assertNotEquals(firstZoneId, secondZoneId);
    assertNotEquals(firstZoneName, secondZoneName);
    assertNotEquals(firstId, secondId);

    // rename both zones to be a third zone - request fails because this results in a single zone
    // with different region topologies.
    final String thirdZoneName = "third zone";
    firstReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, thirdZoneName);
    firstReplicationSpec.remove(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);
    firstReplicationSpec
        .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
        .getJSONObject(0)
        .put(ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD, "EU_WEST_1");
    secondReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, thirdZoneName);
    secondReplicationSpec.remove(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);
    final JSONObject clusterUpdate_moveZone =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray().put(firstReplicationSpec).put(secondReplicationSpec));
    final JSONObject resp_ambiguousZoneUpdate_invalid =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            clusterUpdate_moveZone,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "ASYMMETRIC_REGION_TOPOLOGY_IN_ZONE",
        resp_ambiguousZoneUpdate_invalid.getString(ApiError.ERROR_CODE_FIELD));

    // reset
    firstReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, firstZoneName);
    secondReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, secondZoneName);
    firstReplicationSpec
        .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
        .getJSONObject(0)
        .put(ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_1");

    // first zone to be a third zone - Currently rejected due to the validation to prevent PACPCM
    // deadlocks
    // Eventually, request should suceed and grants the "new" zone a new id

    firstReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, thirdZoneName);
    firstReplicationSpec.remove(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);
    firstReplicationSpec
        .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
        .getJSONObject(0)
        .put(ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD, "EU_WEST_1");
    final JSONObject clusterUpdate_newZoneNoId =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray().put(firstReplicationSpec).put(secondReplicationSpec));
    final JSONObject resp_newZoneNoId =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            clusterUpdate_newZoneNoId,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        "Zones for global clusters can only be either added or removed in a single update. Adding"
            + " and removing zones at the same time is unsupported.",
        resp_newZoneNoId.getString("detail"));
    assertEquals(
        ApiErrorCode.ZONE_CHANGES_UNSUPPORTED.name(), resp_newZoneNoId.getString("errorCode"));

    // reset
    firstReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, firstZoneName);
    secondReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, secondZoneName);
    firstReplicationSpec
        .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
        .getJSONObject(0)
        .put(ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_1");

    // update: add new shard in second zone, and move second shard to first zone
    final JSONObject replicationSpecToAdd =
        getDefaultSingleRegionReplicationSpec("M30", "AWS", "AP_SOUTH_2");
    replicationSpecToAdd.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, secondZoneName);
    final JSONObject autoScalingReferenceDoc =
        firstReplicationSpec
            .getJSONArray(ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD);
    for (int i = 0;
        i
            < replicationSpecToAdd
                .getJSONArray(ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD)
                .length();
        i++) {
      replicationSpecToAdd
          .getJSONArray(ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD)
          .getJSONObject(i)
          .put(
              ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD,
              new JSONObject(autoScalingReferenceDoc.toString()));
    }

    secondReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, firstZoneName);
    secondReplicationSpec.remove(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);
    final JSONArray firstRegionConfigs =
        firstReplicationSpec.getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD);
    final JSONArray secondRegionConfigs =
        secondReplicationSpec.getJSONArray(
            ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD);
    secondReplicationSpec.put(
        ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD, firstRegionConfigs);

    final JSONObject clusterUpdate_addShard_moveZone =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray()
                    .put(firstReplicationSpec)
                    .put(secondReplicationSpec)
                    .put(replicationSpecToAdd));

    // this update is allowed through as 2 things should happen
    // -- second spec gains the first zone id
    // -- third spec gains the second zone id (by virtue of having the second zone name)
    final JSONObject resp_addShard_moveZone =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            clusterUpdate_addShard_moveZone,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray replicationSpecs_addShard_moveZone =
        resp_addShard_moveZone.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
    assertEquals(3, replicationSpecs_addShard_moveZone.length());
    assertEquals(
        firstZoneName,
        replicationSpecs_addShard_moveZone
            .getJSONObject(0)
            .getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        firstZoneId,
        replicationSpecs_addShard_moveZone
            .getJSONObject(0)
            .getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
    assertEquals(
        "US_EAST_1",
        replicationSpecs_addShard_moveZone
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getString(ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD));

    assertEquals(
        firstZoneName,
        replicationSpecs_addShard_moveZone
            .getJSONObject(1)
            .getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        firstZoneId,
        replicationSpecs_addShard_moveZone
            .getJSONObject(1)
            .getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
    assertEquals(
        "US_EAST_1",
        replicationSpecs_addShard_moveZone
            .getJSONObject(1)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getString(ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD));

    assertEquals(
        secondZoneName,
        replicationSpecs_addShard_moveZone
            .getJSONObject(2)
            .getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        secondZoneId,
        replicationSpecs_addShard_moveZone
            .getJSONObject(2)
            .getString(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
    assertEquals(
        "AP_SOUTH_2",
        replicationSpecs_addShard_moveZone
            .getJSONObject(2)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getString(ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD));

    // Move the second shard into the first zone
    secondReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, firstZoneName);
    secondReplicationSpec.remove(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);
    final JSONObject resp_reZone =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            new JSONObject()
                .put(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                    new JSONArray().put(firstReplicationSpec).put(secondReplicationSpec)),
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    final JSONArray replicationSpecsAfterAddShardAndReZone =
        resp_reZone.getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
    assertEquals(2, replicationSpecsAfterAddShardAndReZone.length());
    final Map<String, JSONObject> replSpecAfterAddShardAndReZoneById = new HashMap<>();
    for (int i = 0; i < replicationSpecsAfterAddShardAndReZone.length(); i++) {
      final JSONObject replSpec = replicationSpecsAfterAddShardAndReZone.getJSONObject(i);
      final String replSpecId = replSpec.getString(ApiAtlasReplicationSpec20240805View.ID_FIELD);
      replSpecAfterAddShardAndReZoneById.put(replSpecId, replSpec);
    }
    assertEquals(
        replicationSpecsAfterAddShardAndReZone.length(), replSpecAfterAddShardAndReZoneById.size());

    final JSONObject firstReplSpecAfterAddShardAndReZone =
        replSpecAfterAddShardAndReZoneById.get(firstId);
    assertEquals(
        firstZoneName,
        firstReplSpecAfterAddShardAndReZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        firstZoneId,
        firstReplSpecAfterAddShardAndReZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));

    // second shard was moved to the first zone
    final JSONObject secondReplSpecAfterAddShardAndReZone =
        replSpecAfterAddShardAndReZoneById.get(secondId);
    assertEquals(
        firstZoneName,
        secondReplSpecAfterAddShardAndReZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        firstZoneId,
        secondReplSpecAfterAddShardAndReZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));

    // undo the update - move the second shard into the second zone
    secondReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, secondZoneName);
    secondReplicationSpec.remove(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);
    secondReplicationSpec.put(
        ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD, secondRegionConfigs);
    final JSONObject resp_removeShard_reZone_invalid =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            new JSONObject()
                .put(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                    new JSONArray().put(firstReplicationSpec).put(secondReplicationSpec)),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "ZONE_CHANGES_UNSUPPORTED",
        resp_removeShard_reZone_invalid.getString(ApiError.ERROR_CODE_FIELD));
    assertEquals(
        "Zones for global clusters can only be either added or removed in a single update. Adding"
            + " and removing zones at the same time is unsupported.",
        resp_removeShard_reZone_invalid.getString(ApiError.DETAIL_FIELD));

    // rename both zones to be a third zone - request passes because this is a straightforward
    // rename now that they are both in the same zone
    secondReplicationSpec.put(
        ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD, firstRegionConfigs);
    firstReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, thirdZoneName);
    firstReplicationSpec.remove(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);
    secondReplicationSpec.put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, thirdZoneName);
    secondReplicationSpec.remove(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD);

    final JSONObject resp_zoneRename =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            clusterUpdate_newZoneNoId,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray replicationSpecsAfterZoneRename =
        resp_zoneRename.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
    assertEquals(originalReplicationSpecs.length(), replicationSpecsAfterZoneRename.length());
    final Map<String, JSONObject> replSpecAfterRemoveShardAndReZoneById = new HashMap<>();
    for (int i = 0; i < replicationSpecsAfterZoneRename.length(); i++) {
      final JSONObject replSpec = replicationSpecsAfterZoneRename.getJSONObject(i);
      final String replSpecId = replSpec.getString(ApiAtlasReplicationSpec20240805View.ID_FIELD);
      replSpecAfterRemoveShardAndReZoneById.put(replSpecId, replSpec);
    }
    assertEquals(originalReplicationSpecs.length(), replSpecAfterRemoveShardAndReZoneById.size());

    // zone ID is unchanged, even though zone was renamed
    final JSONObject firstReplSpecAfterRemoveShardAndReZone =
        replSpecAfterRemoveShardAndReZoneById.get(firstId);
    assertEquals(
        thirdZoneName,
        firstReplSpecAfterRemoveShardAndReZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        firstZoneId,
        firstReplSpecAfterRemoveShardAndReZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));

    final JSONObject secondReplSpecAfterRemoveShardAndReZone =
        replSpecAfterRemoveShardAndReZoneById.get(secondId);
    assertEquals(
        thirdZoneName,
        secondReplSpecAfterRemoveShardAndReZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        firstZoneId,
        secondReplSpecAfterRemoveShardAndReZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));

    final JSONObject clusterUpdate_addShardInNewZone =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray()
                    .put(firstReplicationSpec)
                    .put(secondReplicationSpec)
                    .put(replicationSpecToAdd));
    final JSONObject resp_addShardInNewZone =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            clusterUpdate_addShardInNewZone,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray replicationSpecsAfterAddShardToNewZone =
        resp_addShardInNewZone.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD);
    assertEquals(
        originalReplicationSpecs.length() + 1, replicationSpecsAfterAddShardToNewZone.length());
    final Map<String, JSONObject> replSpecAfterAddShardToNewZoneById = new HashMap<>();
    String thirdId = null;
    for (int i = 0; i < replicationSpecsAfterAddShardToNewZone.length(); i++) {
      final JSONObject replSpec = replicationSpecsAfterAddShardToNewZone.getJSONObject(i);
      final String replSpecId = replSpec.getString(ApiAtlasReplicationSpec20240805View.ID_FIELD);
      replSpecAfterAddShardToNewZoneById.put(replSpecId, replSpec);
      if (!StringUtils.equals(replSpecId, firstId) && !StringUtils.equals(replSpecId, secondId)) {
        thirdId = replSpecId;
      }
    }
    assertEquals(originalReplicationSpecs.length() + 1, replSpecAfterAddShardToNewZoneById.size());

    final JSONObject firstReplSpecAfterAddShardToNewZone =
        replSpecAfterAddShardToNewZoneById.get(firstId);
    assertEquals(
        thirdZoneName,
        firstReplSpecAfterAddShardToNewZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        firstZoneId,
        firstReplSpecAfterAddShardToNewZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));

    final JSONObject secondReplSpecAfterAddShardToNewZone =
        replSpecAfterAddShardToNewZoneById.get(secondId);
    assertEquals(
        thirdZoneName,
        secondReplSpecAfterAddShardToNewZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertEquals(
        firstZoneId,
        secondReplSpecAfterAddShardToNewZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));

    final JSONObject thirdReplSpecAfterAddShardToNewZone =
        replSpecAfterAddShardToNewZoneById.get(thirdId);
    assertEquals(
        replicationSpecToAdd.getString(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD),
        thirdReplSpecAfterAddShardToNewZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD));
    assertNotEquals(
        firstZoneId,
        thirdReplSpecAfterAddShardToNewZone.getString(
            ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD));
  }

  @Test
  public void testUpdateCluster_replicationSpecs_autoScaling() {
    final String clusterName = "clusterUpdateAutoscaling";

    final ObjectId pGroupId = _group.getId();
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, clusterName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);

    final JSONObject m10HardwareSpec =
        new JSONObject()
            .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
            .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3);
    final JSONObject regionConfig =
        new JSONObject()
            .put(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD, m10HardwareSpec)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_1")
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7);
    final JSONObject replicationSpec =
        new JSONObject()
            .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone " + "US_EAST_1")
            .put(
                ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                new JSONArray().put(regionConfig));
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(replicationSpec));

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        cluster,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    final JSONObject autoScalingEnabled =
        new JSONObject()
            .put(
                ApiAtlasAutoScalingV15View.COMPUTE_FIELD,
                new JSONObject()
                    .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
                    .put(ApiAtlasComputeAutoScalingV15View.SCALE_DOWN_ENABLED_FIELD, true)
                    .put(ApiAtlasComputeAutoScalingV15View.PREDICTIVE_ENABLED_FIELD, false)
                    .put(ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M50"))
            .put(
                ApiAtlasAutoScalingV15View.DISK_GB_FIELD,
                new JSONObject().put(ApiAtlasDiskGBAutoScalingView.FieldDefs.ENABLED, true));

    regionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScalingEnabled);

    final JSONObject replicationSpecsUpdate =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray().put(replicationSpec));

    final JSONObject response_replicationSpecsUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            replicationSpecsUpdate,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    final JSONObject responseReplicationSpec =
        response_replicationSpecsUpdate
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0);

    assertEquals(
        1,
        responseReplicationSpec
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .length());
    assertTrue(
        JSONCompare.compareJSON(
                autoScalingEnabled,
                responseReplicationSpec
                    .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
                    .getJSONObject(0)
                    .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD),
                JSONCompareMode.STRICT)
            .passed());

    // First create an Asymmetric Region Config with no autoscaling field
    final JSONObject m30HardwareSpec =
        new JSONObject()
            .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M30")
            .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3);
    final JSONObject asymmetricRegionConfig =
        new JSONObject()
            .put(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD, m30HardwareSpec)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_1")
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7);
    final JSONObject asymmetricReplicationSpec =
        new JSONObject()
            .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone " + "US_EAST_1")
            .put(
                ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                new JSONArray().put(asymmetricRegionConfig));

    final JSONObject response_invalid_oneMissingAutoscaling =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            new JSONObject()
                .put(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                    new JSONArray().put(replicationSpec).put(asymmetricReplicationSpec)),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        "AUTO_SCALINGS_MUST_BE_IN_EVERY_REGION_CONFIG",
        response_invalid_oneMissingAutoscaling.getString("errorCode"));

    // different autoscaling in different tiers
    final JSONObject autoScalingDisabled =
        new JSONObject()
            .put(
                ApiAtlasAutoScalingV15View.COMPUTE_FIELD,
                new JSONObject()
                    .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, false)
                    .put(ApiAtlasComputeAutoScalingV15View.SCALE_DOWN_ENABLED_FIELD, false))
            .put(
                ApiAtlasAutoScalingV15View.DISK_GB_FIELD,
                new JSONObject().put(ApiAtlasDiskGBAutoScalingView.FieldDefs.ENABLED, true));

    asymmetricRegionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScalingDisabled);

    final JSONObject response_invalid_mismatchedAutoscaling =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            new JSONObject()
                .put(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                    new JSONArray().put(replicationSpec).put(asymmetricReplicationSpec)),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        "AUTO_SCALINGS_MUST_MATCH", response_invalid_mismatchedAutoscaling.getString("errorCode"));

    // Enable autoscaling on an asymmetric cluster and get an error
    asymmetricRegionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScalingEnabled);

    final JSONObject response_invalid_enabledBaseAsymmetricAutoscaling =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            new JSONObject()
                .put(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                    new JSONArray().put(replicationSpec).put(asymmetricReplicationSpec)),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        "ASYMMETRIC_BASE_AUTO_SCALING_UNAVAILABLE",
        response_invalid_enabledBaseAsymmetricAutoscaling.getString("errorCode"));
    assertEquals(
        "Autoscaling is not yet available in conjunction with independent shard scaling. Disable"
            + " autoscaling for Base nodes or select one instance size for all Base nodes.",
        response_invalid_enabledBaseAsymmetricAutoscaling.getString("detail"));

    // disable base autoscaling, add analytics nodes, enable analytics autoscaling

    regionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScalingDisabled);
    asymmetricRegionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScalingDisabled);

    regionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD, m10HardwareSpec);
    asymmetricRegionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD, m30HardwareSpec);

    regionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_AUTOSCALING_FIELD, autoScalingEnabled);
    asymmetricRegionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_AUTOSCALING_FIELD, autoScalingEnabled);

    final JSONObject response_invalid_enabledAnalyticsAsymmetricAutoscaling =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            new JSONObject()
                .put(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                    new JSONArray().put(replicationSpec).put(asymmetricReplicationSpec)),
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        "ASYMMETRIC_ANALYTICS_AUTO_SCALING_UNAVAILABLE",
        response_invalid_enabledAnalyticsAsymmetricAutoscaling.getString("errorCode"));
    assertEquals(
        "Autoscaling is not yet available in conjunction with independent shard scaling. Disable"
            + " autoscaling for Analytics nodes or select one instance size for all Analytics"
            + " nodes.",
        response_invalid_enabledAnalyticsAsymmetricAutoscaling.getString("detail"));

    // Disable analytics autoscaling

    regionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_AUTOSCALING_FIELD, autoScalingDisabled);
    asymmetricRegionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_AUTOSCALING_FIELD, autoScalingDisabled);

    doDigestJsonPatchWithApiVersion(
        String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray().put(replicationSpec).put(asymmetricReplicationSpec)),
        HttpStatus.SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
  }

  @Test
  public void testUpdateCluster_replicationSpecs_autoScaling_limits() {
    final String clusterName = "push-your-limits";

    final ObjectId pGroupId = _group.getId();
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, clusterName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);

    final JSONObject m30ProvisionedIOPS =
        new JSONObject()
            .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M30")
            .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3)
            .put(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD, 600)
            .put(ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD, "PROVISIONED");
    final JSONObject regionConfig =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD, m30ProvisionedIOPS)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_2")
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7);
    final JSONObject replicationSpec =
        new JSONObject()
            .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 1")
            .put(
                ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                new JSONArray().put(regionConfig));
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(replicationSpec));

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        cluster,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    final JSONObject computeAutoScaling =
        new JSONObject()
            .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.SCALE_DOWN_ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.PREDICTIVE_ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M40");

    final JSONObject autoScaling =
        new JSONObject()
            .put(ApiAtlasAutoScalingV15View.COMPUTE_FIELD, computeAutoScaling)
            .put(
                ApiAtlasAutoScalingV15View.DISK_GB_FIELD,
                new JSONObject().put(ApiAtlasDiskGBAutoScalingView.FieldDefs.ENABLED, true));

    regionConfig.put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling);

    final JSONObject replicationSpecsUpdate =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray().put(replicationSpec));

    // autoScaling limits should be rejected: M10 doesn't support provisioned IOPS
    {
      computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M10");

      final JSONObject response_autoScalingLimitM10Incompatible =
          doDigestJsonPatchWithApiVersion(
              String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
              replicationSpecsUpdate,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      assertEquals(
          ApiErrorCode.AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.name(),
          response_autoScalingLimitM10Incompatible.getString("errorCode"));
      assertEquals(
          "Specified autoScaling limits ([M10, M40]) refer to instance sizes incompatible"
              + " with provisioned IOPS configurations.",
          response_autoScalingLimitM10Incompatible.getString("detail"));
    }

    // autoScaling limits should be rejected: M20 doesn't support provisioned IOPS
    {
      computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M20");

      final JSONObject response_autoScalingLimitM20Incompatible =
          doDigestJsonPatchWithApiVersion(
              String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
              replicationSpecsUpdate,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      assertEquals(
          ApiErrorCode.AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.name(),
          response_autoScalingLimitM20Incompatible.getString("errorCode"));
      assertEquals(
          "Specified autoScaling limits ([M20, M40]) refer to instance sizes incompatible"
              + " with provisioned IOPS configurations.",
          response_autoScalingLimitM20Incompatible.getString("detail"));
    }

    // autoScaling limits should be accepted: M30 supports provisioned IOPS
    {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_PREDICTIVE_AUTOSCALING);
      computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M30");

      final JSONObject response_autoScalingLimitsCompatible =
          doDigestJsonPatchWithApiVersion(
              String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
              replicationSpecsUpdate,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      final JSONObject responseReplicationSpec =
          response_autoScalingLimitsCompatible
              .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
              .getJSONObject(0);

      assertEquals(
          1,
          responseReplicationSpec
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .length());
      assertTrue(
          JSONCompare.compareJSON(
                  autoScaling,
                  responseReplicationSpec
                      .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
                      .getJSONObject(0)
                      .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD),
                  JSONCompareMode.STRICT)
              .passed());
    }

    // same set of tests for analyticsAutoScaling
    final JSONObject analyticsComputeAutoScaling =
        new JSONObject()
            .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.SCALE_DOWN_ENABLED_FIELD, true)
            .put(ApiAtlasComputeAutoScalingV15View.PREDICTIVE_ENABLED_FIELD, false)
            .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M40");

    final JSONObject analyticsAutoScaling =
        new JSONObject()
            .put(ApiAtlasAutoScalingV15View.COMPUTE_FIELD, analyticsComputeAutoScaling)
            .put(
                ApiAtlasAutoScalingV15View.DISK_GB_FIELD,
                new JSONObject().put(ApiAtlasDiskGBAutoScalingView.FieldDefs.ENABLED, true));

    regionConfig.put(
        ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_AUTOSCALING_FIELD,
        analyticsAutoScaling);

    // analyticsAutoScaling limits should be rejected: M10 doesn't support provisioned IOPS
    {
      analyticsComputeAutoScaling.put(
          ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M10");

      final JSONObject response_analyticsAutoScalingLimitM10Incompatible =
          doDigestJsonPatchWithApiVersion(
              String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
              replicationSpecsUpdate,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      assertEquals(
          ApiErrorCode.ANALYTICS_AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.name(),
          response_analyticsAutoScalingLimitM10Incompatible.getString("errorCode"));
      assertEquals(
          "Specified analyticsAutoScaling limits ([M10, M40]) refer to instance sizes"
              + " incompatible with provisioned IOPS configurations.",
          response_analyticsAutoScalingLimitM10Incompatible.getString("detail"));
    }

    // analyticsAutoScaling limits should be rejected: M20 doesn't support provisioned IOPS
    {
      analyticsComputeAutoScaling.put(
          ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M20");

      final JSONObject response_analyticsAutoScalingLimitM20Incompatible =
          doDigestJsonPatchWithApiVersion(
              String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
              replicationSpecsUpdate,
              HttpStatus.SC_BAD_REQUEST,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      assertEquals(
          ApiErrorCode.ANALYTICS_AUTO_SCALING_LIMITS_INCOMPATIBLE_WITH_PROVISIONED_IOPS.name(),
          response_analyticsAutoScalingLimitM20Incompatible.getString("errorCode"));
      assertEquals(
          "Specified analyticsAutoScaling limits ([M20, M40]) refer to instance sizes"
              + " incompatible with provisioned IOPS configurations.",
          response_analyticsAutoScalingLimitM20Incompatible.getString("detail"));
    }

    // analyticsAutoScaling limits should be accepted: M30 supports provisioned IOPS
    {
      analyticsComputeAutoScaling.put(
          ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M30");

      final JSONObject response_analyticsAutoScalingLimitsCompatible =
          doDigestJsonPatchWithApiVersion(
              String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
              replicationSpecsUpdate,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      final JSONObject responseReplicationSpec =
          response_analyticsAutoScalingLimitsCompatible
              .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
              .getJSONObject(0);

      assertEquals(
          1,
          responseReplicationSpec
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .length());
      assertTrue(
          JSONCompare.compareJSON(
                  analyticsAutoScaling,
                  responseReplicationSpec
                      .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
                      .getJSONObject(0)
                      .getJSONObject(
                          ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_AUTOSCALING_FIELD),
                  JSONCompareMode.STRICT)
              .passed());
    }
  }

  @Test
  public void testUpdateCluster_pausedFixedFCV() throws Exception {
    _clusterDescriptionDao.setState(_group.getId(), TestClusterNames.BASIC_AWS_CLUSTER, State.IDLE);
    final ClusterDescription clusterWithFixedFCV =
        _clusterDescriptionDao
            .findByName(_group.getId(), TestClusterNames.BASIC_AWS_CLUSTER)
            .get()
            .copy()
            .setMongoDBVersion("6.0.5")
            .setMongoDBMajorVersion("6.0")
            .setFixedFeatureCompatibilityVersion(Optional.of(new FixedVersion("5.0")))
            .build();
    _clusterDescriptionDao.save(clusterWithFixedFCV);
    _ndsGroupDao.addCloudContainer(
        _group.getId(), new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));

    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(_group.getId());
    _healthCheckTestUtil.setupHealthyCluster(ndsGroup, clusterWithFixedFCV.getName(), _group);

    // update cluster to be paused
    final JSONObject clusterUpdate_paused =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.PAUSED_FIELD, true);

    final JSONObject response =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterUpdate_paused,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertTrue(response.getBoolean(ApiAtlasClusterDescription20240805View.PAUSED_FIELD));
    assertEquals(
        "5.0",
        response.getString(ApiAtlasClusterDescription20240805View.FEATURE_COMPATIBILITY_VERSION));
  }

  @Test
  public void testUpdateCluster_paused() throws Exception {
    // update cluster to be paused & change disk size
    final JSONObject clusterUpdate_pauseAndEnableBackup =
        new JSONObject()
            .put(ApiAtlasClusterDescription20240805View.PAUSED_FIELD, true)
            .put(ApiAtlasClusterDescription20240805View.BACKUP_ENABLED_FIELD, true);

    final JSONObject resp0_invalidPauseUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterUpdate_pauseAndEnableBackup,
            HttpStatus.SC_CONFLICT,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ApiErrorCode.CANNOT_UPDATE_AND_PAUSE_CLUSTER.name(),
        resp0_invalidPauseUpdate.getString(ApiError.ERROR_CODE_FIELD));

    // try to update a pending cluster
    final JSONObject clusterUpdate_pause =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.PAUSED_FIELD, true);
    _clusterDescriptionDao.setState(
        _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER, State.WORKING);
    final JSONObject resp1_invalidPauseUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterUpdate_pause,
            HttpStatus.SC_CONFLICT,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.CANNOT_PAUSE_CLUSTER_WITH_PENDING_CHANGES.name(),
        resp1_invalidPauseUpdate.getString(ApiError.ERROR_CODE_FIELD));

    // update cluster to be paused
    _clusterDescriptionDao.setState(_group.getId(), TestClusterNames.BASIC_AWS_CLUSTER, State.IDLE);
    _ndsGroupDao.addCloudContainer(
        _group.getId(), new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()));
    final NDSGroup ndsGroup = _ndsGroupSvc.ensureGroup(_group.getId());
    _healthCheckTestUtil.setupHealthyCluster(ndsGroup, TestClusterNames.BASIC_AWS_CLUSTER, _group);
    final JSONObject resp2_validPauseUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterUpdate_pause,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertTrue(
        resp2_validPauseUpdate.getBoolean(ApiAtlasClusterDescription20240805View.PAUSED_FIELD));

    // update fields while the cluster is paused
    final JSONObject clusterUpdate_enableBackup =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.BACKUP_ENABLED_FIELD, true);
    final JSONObject resp3_invalidPauseUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterUpdate_enableBackup,
            HttpStatus.SC_CONFLICT,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.CANNOT_UPDATE_PAUSED_CLUSTER.name(),
        resp3_invalidPauseUpdate.getString(ApiError.ERROR_CODE_FIELD));

    final JSONObject clusterUpdate_unpause =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.PAUSED_FIELD, false);
    final JSONObject resp4_validUnPauseUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterUpdate_unpause,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertFalse(
        resp4_validUnPauseUpdate.getBoolean(ApiAtlasClusterDescription20240805View.PAUSED_FIELD));

    // Cannot pause a NVMe cluster
    final JSONObject resp5_invalidPauseNVMEUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.NVME_CLUSTER),
            clusterUpdate_pause,
            HttpStatus.SC_CONFLICT,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.CANNOT_PAUSE_NVME_CLUSTER.name(),
        resp5_invalidPauseNVMEUpdate.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateCluster_clusterType() {
    // sharded -> replicaset (not allowed)
    final JSONObject clusterTypeUpdate_replicaset =
        new JSONObject()
            .put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    final JSONObject resp0_invalidReplicasetUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s",
                BASE_URL, _group.getId(), TestClusterNames.ASYMMETRIC_CLUSTER_WITH_EMBEDDED_CONFIG),
            clusterTypeUpdate_replicaset,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.INVALID_CLUSTER_CONFIGURATION.name(),
        resp0_invalidReplicasetUpdate.getString(ApiError.ERROR_CODE_FIELD));
    assertTrue(
        resp0_invalidReplicasetUpdate
            .getJSONArray(ApiError.PARAMETERS_FIELD)
            .getString(0)
            .contains("Changing clusterType of sharded clusters"));

    final JSONObject clusterTypeUpdate_nonsense =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "NONSENSE");

    final JSONObject resp1_invalidReplicasetUpdate =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterTypeUpdate_nonsense,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.INVALID_ATTRIBUTE.name(),
        resp1_invalidReplicasetUpdate.getString(ApiError.ERROR_CODE_FIELD));

    // upgrade to sharded tested elsewhere
  }

  @Test
  public void testUpdateCluster_backup() {
    final JSONObject backupUpdate_NVMeWithoutBackup =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.BACKUP_ENABLED_FIELD, false);
    final JSONObject resp_tryDisableNvmeBackup =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.NVME_CLUSTER),
            backupUpdate_NVMeWithoutBackup,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.NVME_STORAGE_PROVIDER_BACKUP_REQUIRED.name(),
        resp_tryDisableNvmeBackup.getString(ApiError.ERROR_CODE_FIELD));

    final ClusterDescription basicCluster =
        _clusterDescriptionDao.findByName(_group.getId(), TestClusterNames.BASIC_AWS_CLUSTER).get();
    _clusterDescriptionDao.save(basicCluster.copy().setBackupEnabled(true).build());

    final JSONObject backupUpdate_enable =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.BACKUP_ENABLED_FIELD, true);
    final JSONObject resp_tryEnableBackupWithLegacyBackup =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            backupUpdate_enable,
            HttpStatus.SC_CONFLICT,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.CANNOT_ENABLE_BACKUP_WITH_ACTIVE_LEGACY_BACKUP.name(),
        resp_tryEnableBackupWithLegacyBackup.getString(ApiError.ERROR_CODE_FIELD));

    _clusterDescriptionDao.save(basicCluster);

    doDigestJsonPatchWithApiVersion(
        String.format(
            "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
        backupUpdate_enable,
        HttpStatus.SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
  }

  @Test
  public void testUpdateCluster_invalidAzureDiskSize() {
    // set up in setUp(), cross-cloud cluster containing azure
    final JSONObject resp_originalCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.CROSS_CLOUD_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray regionConfigs =
        resp_originalCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD);
    for (int i = 0; i < regionConfigs.length(); i++) {
      final JSONObject regionConfig1 =
          resp_originalCluster
              .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
              .getJSONObject(0)
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
              .getJSONObject(i);
      regionConfig1
          .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
          .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 200.0);
      regionConfig1
          .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD)
          .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 200.0);
      regionConfig1
          .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD)
          .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 200.0);
    }

    final JSONObject clusterTypeUpdate_diskSizeGB =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                resp_originalCluster.getJSONArray(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD));
    final JSONObject response0 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.CROSS_CLOUD_CLUSTER,
            clusterTypeUpdate_diskSizeGB,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.DISK_SIZE_INVALID_FOR_AZURE.name(),
        response0.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateCluster_AWSNVMeDiskSize() {
    final JSONObject resp_originalCluster =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.NVME_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONObject regionConfig0 =
        resp_originalCluster
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0);
    assertEquals(
        AWSNDSInstanceSize.M40_NVME.name(),
        regionConfig0
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD));

    // disk size below default for cluster tier
    regionConfig0
        .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
        .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 30.0);
    regionConfig0
        .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD)
        .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 30.0);
    regionConfig0
        .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD)
        .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 30.0);

    final JSONObject clusterHardwareUpdate =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                resp_originalCluster.getJSONArray(
                    ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD));

    final JSONObject response0 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.NVME_CLUSTER,
            clusterHardwareUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.CANNOT_MODIFY_DISK_SIZE_FOR_NVME_CLUSTER.name(),
        response0.getString(ApiError.ERROR_CODE_FIELD));

    // disk size above default for cluster tier
    regionConfig0
        .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
        .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 10000.0);
    regionConfig0
        .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD)
        .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 10000.0);
    regionConfig0
        .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD)
        .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 10000.0);

    final JSONObject response1 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.NVME_CLUSTER,
            clusterHardwareUpdate,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.CANNOT_MODIFY_DISK_SIZE_FOR_NVME_CLUSTER.name(),
        response1.getString(ApiError.ERROR_CODE_FIELD));

    // disk size default for cluster tier - accepted and disk & instance size unchanged
    regionConfig0
        .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
        .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 380);
    regionConfig0
        .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD)
        .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 380);
    regionConfig0
        .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD)
        .put(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 380);

    final JSONObject response2 =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.NVME_CLUSTER,
            clusterHardwareUpdate,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        regionConfig0
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .get(ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD),
        response2
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .get(ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD));
    assertEquals(
        regionConfig0
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getInt(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD),
        response2
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getInt(ApiAtlasAWSHardwareSpec20240805View.DISK_SIZE_GB_FIELD));
  }

  @Test
  public void testUpdateCluster_biConnector() {
    final String clusterName = "clusterWithAnalyticsNodes";
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(),
            "clusterWithAnalyticsNodes",
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 1, 0, 0))));
    final JSONObject clusterTypeUpdate_enableBIC =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.BI_CONNECTOR_FIELD,
                new JSONObject()
                    .put(ApiAtlasBiConnectorView.FieldDefs.ENABLED, true)
                    .put(ApiAtlasBiConnectorView.FieldDefs.READ_PREFERENCE, "secondary"));

    final JSONObject resp_enableBic =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            clusterTypeUpdate_enableBIC,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertTrue(
        resp_enableBic
            .getJSONObject(ApiAtlasClusterDescription20240805View.BI_CONNECTOR_FIELD)
            .getBoolean(ApiAtlasBiConnectorView.FieldDefs.ENABLED));

    final JSONObject clusterTypeUpdate_enableBICForAnalytics =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.BI_CONNECTOR_FIELD,
                new JSONObject()
                    .put(ApiAtlasBiConnectorView.FieldDefs.ENABLED, true)
                    .put(ApiAtlasBiConnectorView.FieldDefs.READ_PREFERENCE, "analytics"));

    final JSONObject resp1_enableBic_invalid =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterTypeUpdate_enableBICForAnalytics,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.INVALID_BI_CONNECTOR_READ_PREFERENCE_FOR_TOPOLOGY.name(),
        resp1_enableBic_invalid.getString(ApiError.ERROR_CODE_FIELD));

    // update just the read preference on a cluster with BIC enabled
    final JSONObject clusterTypeUpdate_updateReadPrefAnalytics =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.BI_CONNECTOR_FIELD,
                new JSONObject()
                    .put(ApiAtlasBiConnectorView.FieldDefs.READ_PREFERENCE, "analytics"));
    final JSONObject resp2_enableBic =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), clusterName),
            clusterTypeUpdate_updateReadPrefAnalytics,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "analytics",
        resp2_enableBic
            .getJSONObject(ApiAtlasClusterDescription20240805View.BI_CONNECTOR_FIELD)
            .getString(ApiAtlasBiConnectorView.FieldDefs.READ_PREFERENCE));
    assertTrue(
        resp2_enableBic
            .getJSONObject(ApiAtlasClusterDescription20240805View.BI_CONNECTOR_FIELD)
            .getBoolean(ApiAtlasBiConnectorView.FieldDefs.ENABLED));
  }

  @Test
  public void testUpdateCluster_encryptionAtRest() {
    final JSONObject clusterTypeUpdate_enableEAR =
        new JSONObject()
            .put(ApiAtlasClusterDescription20240805View.ENCRYPTION_AT_REST_PROVIDER_FIELD, "AWS");

    final JSONObject resp =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterTypeUpdate_enableEAR,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ApiErrorCode.ENCRYPTION_AT_REST_NOT_ENABLED.name(),
        resp.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateCluster_tenantCluster() {
    final JSONObject clusterUpdate_MDBVersion =
        new JSONObject().put(ApiAtlasClusterDescription20240805View.MONGODB_VERSION_FIELD, "8.0");

    final JSONObject resp0 =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.M2_CLUSTER),
            clusterUpdate_MDBVersion,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.TENANT_CLUSTER_UPDATE_UNSUPPORTED.name(),
        resp0.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateCluster_mongoDBMajorVersion() {
    final JSONObject clusterTypeUpdate_majorVersion =
        new JSONObject().put(ApiAtlasClusterDescriptionV15View.MONGODB_MAJOR_VERSION_FIELD, "4.0");
    final JSONObject resp0 =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterTypeUpdate_majorVersion,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.CANNOT_DOWNGRADE_WITHOUT_PINNED_FCV.name(),
        resp0.getString(ApiError.ERROR_CODE_FIELD));

    final JSONObject clusterTypeUpdate_invalidMajorVersion =
        new JSONObject()
            .put(ApiAtlasClusterDescriptionV15View.MONGODB_MAJOR_VERSION_FIELD, "notAVersion");

    final JSONObject resp1 =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterTypeUpdate_invalidMajorVersion,
            HttpStatus.SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        ApiErrorCode.MONGODB_VERSION_INVALID.name(), resp1.getString(ApiError.ERROR_CODE_FIELD));

    final String upgradeVersion = "6.0";
    final JSONObject clusterTypeUpdate_upgradeVersion =
        new JSONObject()
            .put(ApiAtlasClusterDescriptionV15View.MONGODB_MAJOR_VERSION_FIELD, upgradeVersion);

    final JSONObject resp2 =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            clusterTypeUpdate_upgradeVersion,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        resp2.getString(ApiAtlasClusterDescriptionV15View.MONGODB_MAJOR_VERSION_FIELD),
        upgradeVersion);
  }

  @Test
  public void testUpdateCluster_redactClientLogData() {
    // Expect initial state to be false
    final JSONObject originalState =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertFalse(originalState.getBoolean("redactClientLogData"));

    // Set it to true
    final JSONObject setRedactClientLogData_true =
        new JSONObject().put("redactClientLogData", true);
    final JSONObject patchResp0 =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            setRedactClientLogData_true,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertTrue(patchResp0.getBoolean("redactClientLogData"));

    final JSONObject resp0 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertTrue(resp0.getBoolean("redactClientLogData"));

    // Set it back to false
    final JSONObject setRedactClientLogData_false =
        new JSONObject().put("redactClientLogData", false);
    final JSONObject patchResp1 =
        doDigestJsonPatchWithApiVersion(
            String.format(
                "%s%s/clusters/%s", BASE_URL, _group.getId(), TestClusterNames.BASIC_AWS_CLUSTER),
            setRedactClientLogData_false,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertFalse(patchResp1.getBoolean("redactClientLogData"));

    final JSONObject resp1 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertFalse(resp1.getBoolean("redactClientLogData"));
  }

  @Test
  public void testUpdateCluster_responseContainsLinks() {
    final JSONObject upgradeVersionUpdate =
        new JSONObject().put(ApiAtlasClusterDescriptionV15View.MONGODB_MAJOR_VERSION_FIELD, "6.0");
    final JSONObject updateClusterResponse =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + TestClusterNames.BASIC_AWS_CLUSTER,
            upgradeVersionUpdate,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    final JSONArray links =
        updateClusterResponse.getJSONArray(ApiAtlasClusterDescription20240805View.LINKS_FIELD);

    assertEquals(3, links.length());
  }

  @Test
  public void testConfigServerManagementMode_updateCluster() {
    // Update a cluster the same configServerManagementMode, ff on
    final JSONObject atlasManagedCluster =
        createSymmetricShardedClusterView("cluster", _group.getId(), 5);
    atlasManagedCluster.put(
        ApiAtlasClusterDescription20240805View.CONFIG_SERVER_MANAGEMENT_MODE, "ATLAS_MANAGED");
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _groupDao.findById(_group.getId()),
        null,
        FeatureFlag.ATLAS_AUTOMATIC_EMBEDDED_CONFIG_TRANSITIONS);
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        atlasManagedCluster,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
    final JSONObject atlasManagedUpdate =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.CONFIG_SERVER_MANAGEMENT_MODE,
                "ATLAS_MANAGED");

    final JSONObject updateClusterResponse =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), "cluster"),
            atlasManagedUpdate,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertEquals(
        "DEDICATED",
        updateClusterResponse.getString(ApiAtlasClusterDescription20240805View.CONFIG_SERVER_TYPE));
    assertEquals(
        "ATLAS_MANAGED",
        updateClusterResponse.getString(
            ApiAtlasClusterDescription20240805View.CONFIG_SERVER_MANAGEMENT_MODE));

    // Update a cluster to FIXED_TO_DEDICATED, ff on
    final JSONObject fixedToDedicatedUpdate =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.CONFIG_SERVER_MANAGEMENT_MODE,
                "FIXED_TO_DEDICATED");

    final JSONObject updateClusterResponse2 =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), "cluster"),
            fixedToDedicatedUpdate,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ConfigServerManagementMode.FIXED_TO_DEDICATED.name(),
        updateClusterResponse2.getString(Fields.CONFIG_SERVER_MANAGEMENT_MODE));

    // Update a cluster to FIXED_TO_EMBEDDED, ff on
    final JSONObject fixedToEmbeddedUpdate =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.CONFIG_SERVER_MANAGEMENT_MODE,
                "FIXED_TO_EMBEDDED");

    final JSONObject updateClusterResponse3 =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), "cluster"),
            fixedToEmbeddedUpdate,
            SC_BAD_REQUEST,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        ApiErrorCode.INVALID_CONFIG_SERVER_MANAGEMENT_MODE.name(),
        updateClusterResponse3.getString("errorCode"));
    assertFalse(updateClusterResponse3.has(Fields.CONFIG_SERVER_TYPE));
    assertFalse(updateClusterResponse3.has(Fields.CONFIG_SERVER_MANAGEMENT_MODE));
  }

  @Test
  public void testUpdateCluster_enableAutoScaling_ChangeMode() {
    final JSONObject atlasManagedCluster =
        createSymmetricShardedClusterView(NDSDefaults.CLUSTER_NAME, _group.getId(), 2);
    final JSONObject createClusterResp =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            atlasManagedCluster,
            SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        AutoScalingMode.CLUSTER,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());

    final JSONObject updateEnablingAutoScaling = new JSONObject();
    updateEnablingAutoScaling.put("replicationSpecs", createClusterResp.get("replicationSpecs"));

    final JSONArray replicationSpecs = updateEnablingAutoScaling.getJSONArray("replicationSpecs");
    for (int i = 0; i < replicationSpecs.length(); i++) {
      final JSONObject spec = replicationSpecs.getJSONObject(i);
      final JSONArray regionConfigs = spec.getJSONArray("regionConfigs");
      for (int j = 0; j < regionConfigs.length(); j++) {
        final JSONObject rc = regionConfigs.getJSONObject(j);
        rc.getJSONObject("autoScaling").getJSONObject("compute").put("enabled", true);
        rc.getJSONObject("autoScaling").getJSONObject("compute").put("maxInstanceSize", "M40");
        rc.getJSONObject("autoScaling").getJSONObject("compute").put("minInstanceSize", "M10");
        rc.getJSONObject("autoScaling").getJSONObject("compute").put("scaleDownEnabled", true);
      }
    }

    final JSONObject updateClusterResponse =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), NDSDefaults.CLUSTER_NAME),
            updateEnablingAutoScaling,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        AutoScalingMode.CLUSTER,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());
  }

  @Test
  public void testUpdateCluster_enableAnalyticsAutoScaling_ChangeMode() {
    final JSONObject atlasManagedCluster =
        createSymmetricShardedClusterView(NDSDefaults.CLUSTER_NAME, _group.getId(), 2);
    final JSONObject createClusterResp =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            atlasManagedCluster,
            SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        AutoScalingMode.CLUSTER,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());

    final JSONObject updateEnablingAutoScaling = new JSONObject();
    updateEnablingAutoScaling.put("replicationSpecs", createClusterResp.get("replicationSpecs"));

    final JSONArray replicationSpecs = updateEnablingAutoScaling.getJSONArray("replicationSpecs");
    for (int i = 0; i < replicationSpecs.length(); i++) {
      final JSONObject spec = replicationSpecs.getJSONObject(i);
      final JSONArray regionConfigs = spec.getJSONArray("regionConfigs");
      for (int j = 0; j < regionConfigs.length(); j++) {
        final JSONObject rc = regionConfigs.getJSONObject(j);
        rc.put("analyticsAutoScaling", new JSONObject());
        rc.getJSONObject("analyticsAutoScaling").put("compute", new JSONObject());
        rc.getJSONObject("analyticsAutoScaling").getJSONObject("compute").put("enabled", true);
        rc.getJSONObject("analyticsAutoScaling")
            .getJSONObject("compute")
            .put("maxInstanceSize", "M40");
        rc.getJSONObject("analyticsAutoScaling")
            .getJSONObject("compute")
            .put("minInstanceSize", "M10");
        rc.getJSONObject("analyticsAutoScaling")
            .getJSONObject("compute")
            .put("scaleDownEnabled", true);
      }
    }

    final JSONObject updateClusterResponse =
        doDigestJsonPatchWithApiVersion(
            String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), NDSDefaults.CLUSTER_NAME),
            updateEnablingAutoScaling,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);

    assertEquals(
        AutoScalingMode.CLUSTER,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());
  }

  @Test
  public void testUpdateCluster_missingEnableAutoScaling_DoesNotChangeMode() {
    final JSONObject atlasManagedCluster =
        createSymmetricShardedClusterView(NDSDefaults.CLUSTER_NAME, _group.getId(), 2);
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        atlasManagedCluster,
        SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertEquals(
        AutoScalingMode.CLUSTER,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());

    final JSONObject noUpdateEnablingAutoScaling = new JSONObject();
    noUpdateEnablingAutoScaling.put("redactClientLogData", true);

    doDigestJsonPatchWithApiVersion(
        String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), NDSDefaults.CLUSTER_NAME),
        noUpdateEnablingAutoScaling,
        HttpStatus.SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertEquals(
        AutoScalingMode.CLUSTER,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());
  }

  @Test
  public void testUpdateCluster_customZoneMappings() {
    final String clusterName = "awsGeoCustomZoneMappings";
    final JSONObject cluster = createDefaultGlobalClusterView(clusterName, oid(118));

    final JSONObject hardwareSpec =
        new JSONObject()
            .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3)
            .put(
                ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
                ApiAtlasAWSInstanceSizeView.M10.name());

    final JSONObject zone1 =
        new JSONObject()
            .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 1")
            .put(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD, oid(1))
            .put(
                ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                new JSONArray()
                    .put(
                        new JSONObject()
                            .put(
                                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                                ApiAtlasCloudProviderView.AWS.name())
                            .put(
                                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                                ApiAtlasAWSRegionNameView.US_EAST_1.name())
                            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 7)
                            .put(
                                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                                hardwareSpec)));

    final JSONObject zone2 =
        new JSONObject()
            .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 2")
            .put(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD, oid(1))
            .put(
                ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                new JSONArray()
                    .put(
                        new JSONObject()
                            .put(
                                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                                ApiAtlasCloudProviderView.AWS.name())
                            .put(
                                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                                ApiAtlasAWSRegionNameView.EU_WEST_1.name())
                            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 7)
                            .put(
                                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                                hardwareSpec)));

    final JSONObject zone3 =
        new JSONObject()
            .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 3")
            .put(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD, oid(1))
            .put(
                ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
                new JSONArray()
                    .put(
                        new JSONObject()
                            .put(
                                ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD,
                                ApiAtlasCloudProviderView.AWS.name())
                            .put(
                                ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD,
                                ApiAtlasAWSRegionNameView.AP_SOUTH_1.name())
                            .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 7)
                            .put(
                                ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                                hardwareSpec)));

    cluster.put(Fields.REPLICATION_SPECS_FIELD, new JSONArray(List.of(zone1, zone2, zone3)));

    doDigestJsonPostWithApiVersion(
        BASE_URL + oid(118) + "/clusters",
        cluster,
        HttpStatus.SC_CREATED,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V20240805_VERSION);

    final JSONObject customZoneMappingView =
        new JSONObject()
            .put(
                ApiAtlasCustomZoneMappingsView.CUSTOM_ZONE_MAPPINGS_FIELD,
                new JSONArray(
                    List.of(
                        new JSONObject()
                            .put(ApiAtlasZoneMappingView.LOCATION_FIELD, "US")
                            .put(ApiAtlasZoneMappingView.ZONE_FIELD, "Zone 1"),
                        new JSONObject()
                            .put(ApiAtlasZoneMappingView.LOCATION_FIELD, "IE")
                            .put(ApiAtlasZoneMappingView.ZONE_FIELD, "Zone 2"))));

    doDigestJsonPostWithApiVersion(
        BASE_URL + oid(118) + "/clusters/" + clusterName + "/globalWrites/customZoneMapping",
        customZoneMappingView,
        HttpStatus.SC_OK,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V20240805_VERSION);

    // Delete Zone 2, SC_BAD_REQUEST
    final JSONObject deleteZone2Request =
        new JSONObject().put(Fields.REPLICATION_SPECS_FIELD, new JSONArray(List.of(zone1, zone3)));

    final JSONObject deleteZone2Response =
        doDigestJsonPatchWithApiVersion(
            BASE_URL + oid(118) + "/clusters/" + clusterName,
            deleteZone2Request,
            HttpStatus.SC_BAD_REQUEST,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V20240805_VERSION);

    assertEquals(HttpStatus.SC_BAD_REQUEST, deleteZone2Response.getInt(ApiError.ERROR_FIELD));
    assertEquals(
        "CUSTOM_ZONE_MAPPINGS_INVALID", deleteZone2Response.getString(ApiError.ERROR_CODE_FIELD));

    // Delete Zone 3, SC_OK
    final JSONObject deleteZone3Request =
        new JSONObject().put(Fields.REPLICATION_SPECS_FIELD, new JSONArray(List.of(zone1, zone2)));

    doDigestJsonPatchWithApiVersion(
        BASE_URL + oid(118) + "/clusters/" + clusterName,
        deleteZone3Request,
        HttpStatus.SC_OK,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V20240805_VERSION);

    // Delete custom zone mappings, SC_OK
    doDigestJsonDeleteWithApiVersion(
        BASE_URL + oid(118) + "/clusters/" + clusterName + "/globalWrites/customZoneMapping",
        HttpStatus.SC_OK,
        ADMIN_USERNAME,
        ADMIN_API_KEY,
        V20240805_VERSION);
  }

  @Test
  public void
      testUpdateCluster_AutoScalingModeShard_RequestDoesntTouchReplicationSpecList_Accepted()
          throws StaleUpdateException {
    final JSONObject newClusterAutoScalingOn =
        createSymmetricShardedClusterView(NDSDefaults.CLUSTER_NAME, _group.getId(), 2);
    for (Object replicationSpec :
        newClusterAutoScalingOn.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)) {
      for (Object regionConfig :
          ((JSONObject) replicationSpec)
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)) {
        ((JSONObject) regionConfig)
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasAutoScalingV15View.COMPUTE_FIELD,
                        new JSONObject()
                            .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
                            .put(
                                ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD,
                                "M200")));
      }
    }
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        newClusterAutoScalingOn,
        SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertEquals(
        Optional.empty(),
        _clusterDescriptionDao.findByName(_group.getId(), NDSDefaults.CLUSTER_NAME));

    Optional<ClusterDescription> cd =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), NDSDefaults.CLUSTER_NAME);
    assertTrue(
        cd.get()
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigs()
            .get(0)
            .getBaseAutoScaling()
            .getCompute()
            .isEnabled());
    _clusterDescriptionUpdatesDao.setAutoScalingMode(
        _group.getId(),
        NDSDefaults.CLUSTER_NAME,
        AutoScalingMode.SHARD,
        cd.get().getLastUpdateDate());

    assertEquals(
        AutoScalingMode.SHARD,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());

    final JSONObject updateNotTouchingAutoScaling = new JSONObject();
    updateNotTouchingAutoScaling.put("redactClientLogData", true);

    doDigestJsonPatchWithApiVersion(
        String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), NDSDefaults.CLUSTER_NAME),
        updateNotTouchingAutoScaling,
        HttpStatus.SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertEquals(
        AutoScalingMode.SHARD,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());
  }

  @Test
  public void testUpdateCluster_AutoScalingModeShard_RequestDoesTouchReplicationSpecList_Accepted()
      throws StaleUpdateException {
    final JSONObject newClusterAutoScalingOn =
        createSymmetricShardedClusterView(NDSDefaults.CLUSTER_NAME, _group.getId(), 2);
    for (Object replicationSpec :
        newClusterAutoScalingOn.getJSONArray(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)) {
      for (Object regionConfig :
          ((JSONObject) replicationSpec)
              .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)) {
        ((JSONObject) regionConfig)
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasAutoScalingV15View.COMPUTE_FIELD,
                        new JSONObject()
                            .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
                            .put(
                                ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD,
                                "M200")));
      }
    }
    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        newClusterAutoScalingOn,
        SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertEquals(
        Optional.empty(),
        _clusterDescriptionDao.findByName(_group.getId(), NDSDefaults.CLUSTER_NAME));

    Optional<ClusterDescription> cd =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), NDSDefaults.CLUSTER_NAME);
    assertTrue(
        cd.get()
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigs()
            .get(0)
            .getBaseAutoScaling()
            .getCompute()
            .isEnabled());
    _clusterDescriptionUpdatesDao.setAutoScalingMode(
        _group.getId(),
        NDSDefaults.CLUSTER_NAME,
        AutoScalingMode.SHARD,
        cd.get().getLastUpdateDate());

    assertEquals(
        AutoScalingMode.SHARD,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());

    final JSONObject updateTouchingAutoScaling = new JSONObject();
    updateTouchingAutoScaling.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        newClusterAutoScalingOn.get(
            ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD));

    doDigestJsonPatchWithApiVersion(
        String.format("%s%s/clusters/%s", BASE_URL, _group.getId(), NDSDefaults.CLUSTER_NAME),
        updateTouchingAutoScaling,
        HttpStatus.SC_OK,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    assertEquals(
        AutoScalingMode.SHARD,
        _ndsClusterSvc
            .getMergedClusterDescription(_group.getId(), NDSDefaults.CLUSTER_NAME)
            .orElseThrow()
            .getAutoScalingMode());
  }

  private JSONObject createClusterApiRequest(
      final ObjectId pGroupId, final JSONObject pClusterView, final int pHttpStatus) {
    return doDigestJsonPostWithApiVersion(
        BASE_URL + pGroupId + "/clusters",
        pClusterView,
        pHttpStatus,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
  }

  private JSONObject createDefaultFreeReplicaSetView(final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(getDefaultFreeReplicationSpec()));
    return cluster;
  }

  private JSONObject createDefaultServerlessReplicaSetView(
      final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(getDefaultServerlessReplicationSpec()));
    return cluster;
  }

  private JSONObject createDefaultFlexReplicaSetView(final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(getDefaultFlexReplicationSpec()));
    return cluster;
  }

  private JSONObject createDefaultTenantReplicaSetView(
      final String pName,
      final ObjectId pGroupId,
      final String pInstanceSize,
      final String pRegion) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(getDefaultTenantReplicationSpec(pInstanceSize, pRegion)));
    return cluster;
  }

  private JSONObject createDefaultSingleRegionReplicaSetView(
      final String pName,
      final ObjectId pGroupId,
      final String pInstanceSize,
      final String pCloudProvider,
      final String pRegion) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(getDefaultSingleRegionReplicationSpec(pInstanceSize, pCloudProvider, pRegion)));
    return cluster;
  }

  private JSONObject createDefaultSingleRegionReplicaSetView(
      final String pName,
      final ObjectId pGroupId,
      final String pInstanceSize,
      final String pCloudProvider,
      final String pRegion,
      final String pVersion) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(ApiAtlasClusterDescription20240805View.MONGODB_VERSION_FIELD, pVersion);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(getDefaultSingleRegionReplicationSpec(pInstanceSize, pCloudProvider, pRegion)));
    return cluster;
  }

  private JSONObject createDefaultSingleRegionEAREnabledReplicaSetView(
      final String pName,
      final ObjectId pGroupId,
      final String pInstanceSize,
      final String pCloudProvider,
      final String pRegion,
      final String pEARProvider) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.ENCRYPTION_AT_REST_PROVIDER_FIELD, pEARProvider);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(getDefaultSingleRegionReplicationSpec(pInstanceSize, pCloudProvider, pRegion)));
    return cluster;
  }

  private JSONObject createDefaultCrossCloudReplicaSetView(
      final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(getDefaultCrossCloudReplicationSpec()));
    return cluster;
  }

  private JSONObject createCrossCloudEAREnabledReplicaSetView(
      final String pName, final ObjectId pGroupId, final String pEARProvider) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "REPLICASET");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.ENCRYPTION_AT_REST_PROVIDER_FIELD, pEARProvider);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(getDefaultCrossCloudReplicationSpec()));
    return cluster;
  }

  private JSONObject createLegacyShardedClusterView(final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(
                getDefaultSingleRegionReplicationSpec("M30", "AWS", "US_EAST_1")
                    .put(ApiAtlasReplicationSpecView.NUM_SHARDS_FIELD, 2)));
    return cluster;
  }

  private JSONObject createSymmetricShardedClusterView(
      final String pName, final ObjectId pGroupId) {
    return createSymmetricShardedClusterView(pName, pGroupId, 2);
  }

  private JSONObject createSymmetricShardedClusterView(
      final String pName, final ObjectId pGroupId, final int pNumShards) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    final JSONArray replicationSpecList = new JSONArray();
    for (int i = 0; i < pNumShards; i++) {
      replicationSpecList.put(getDefaultSingleRegionReplicationSpec("M30", "AWS", "US_EAST_1"));
    }
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD, replicationSpecList);
    return cluster;
  }

  private JSONObject createAsymmetricShardedClusterView_inconsistentDiskSizeGB(
      final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(getDefaultSingleRegionReplicationSpec("M30", "AWS", "US_EAST_1", 20.0, false))
            .put(getDefaultSingleRegionReplicationSpec("M50", "AWS", "US_EAST_1", 50.0, false)));
    return cluster;
  }

  private JSONObject createAsymmetricShardedClusterView_inconsistentRegionTopologyInZone(
      final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(getDefaultSingleRegionReplicationSpec("M30", "AWS", "US_WEST_2", 20.0, false))
            .put(getDefaultSingleRegionReplicationSpec("M50", "AWS", "US_EAST_1", 20.0, false)));
    return cluster;
  }

  private JSONObject createAsymmetricShardedClusterView(
      final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(getDefaultSingleRegionReplicationSpec("M30", "AWS", "US_EAST_1"))
            .put(getDefaultSingleRegionReplicationSpec("M50", "AWS", "US_EAST_1")));
    return cluster;
  }

  private JSONObject createAsymmetricShardedClusterViewMixedIOPS(
      final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "SHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(
                getSingleRegionReplicationSpecWithCustomIOPS(
                    "M30", "AWS", "US_EAST_1", "PROVISIONED", 1000))
            .put(
                getSingleRegionReplicationSpecWithCustomIOPS(
                    "M40", "AWS", "US_EAST_1", "PROVISIONED", 2000))
            .put(
                getSingleRegionReplicationSpecWithCustomIOPS(
                    "M50", "AWS", "US_EAST_1", "STANDARD", null)));
    return cluster;
  }

  private JSONObject createDefaultGlobalClusterView(final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "GEOSHARDED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(
                getDefaultSingleRegionReplicationSpec("M30", "AWS", "US_EAST_1")
                    .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 1"))
            .put(
                getDefaultSingleRegionReplicationSpec("M30", "AWS", "US_WEST_2")
                    .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone 2")));
    return cluster;
  }

  private JSONObject getDefaultCrossCloudReplicationSpec() {
    final JSONObject autoScaling =
        new JSONObject()
            .put(
                ApiAtlasAutoScalingV15View.COMPUTE_FIELD,
                new JSONObject()
                    .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
                    .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M30"));

    final JSONObject awsRegionConfig_3electable_1readOnly =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 16.0))
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 1)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 16.0))
            .put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_1");
    final JSONObject gcpRegionConfig_2electable_1analytics =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 2)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 16.0))
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 1)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 16.0))
            .put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 6)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "GCP")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "CENTRAL_US");
    final JSONObject azureRegionConfig_2electable_1analytics =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 2)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 16.0))
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 1)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 16.0))
            .put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 5)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AZURE")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_2");
    final JSONObject gcpRegionConfig_1analytics =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 1)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 16.0))
            .put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 0)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "GCP")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_4");
    return new JSONObject()
        .put(
            ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD,
            new JSONArray()
                .put(awsRegionConfig_3electable_1readOnly)
                .put(gcpRegionConfig_2electable_1analytics)
                .put(azureRegionConfig_2electable_1analytics)
                .put(gcpRegionConfig_1analytics));
  }

  private JSONObject getDefaultFreeReplicationSpec() {
    final JSONObject regionConfig =
        new JSONObject()
            .put(
                ApiAtlasTenantRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject().put(ApiAtlasTenantHardwareSpecView.INSTANCE_SIZE_FIELD, "M0"))
            .put(ApiAtlasTenantRegionConfig20240805View.PROVIDER_NAME_FIELD, "TENANT")
            .put(ApiAtlasTenantRegionConfig20240805View.BACKING_PROVIDER_NAME_FIELD, "GCP")
            .put(ApiAtlasTenantRegionConfig20240805View.REGION_NAME_FIELD, "WESTERN_EUROPE");
    return new JSONObject()
        .put(
            ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
            new JSONArray().put(regionConfig));
  }

  private JSONObject getDefaultTenantReplicationSpec(
      final String pInstanceSize, final String pRegion) {
    final JSONObject regionConfig =
        new JSONObject()
            .put(
                ApiAtlasTenantRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasTenantHardwareSpec20240805View.INSTANCE_SIZE_FIELD, pInstanceSize))
            .put(ApiAtlasTenantRegionConfig20240805View.PROVIDER_NAME_FIELD, "TENANT")
            .put(ApiAtlasTenantRegionConfig20240805View.BACKING_PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasTenantRegionConfig20240805View.REGION_NAME_FIELD, pRegion);
    return new JSONObject()
        .put(
            ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
            new JSONArray().put(regionConfig));
  }

  private JSONObject getDefaultSingleRegionReplicationSpec(
      final String pInstanceSize, final String pProvider, final String pRegion) {
    final JSONObject regionConfig =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD, pInstanceSize)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3))
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, pProvider)
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, pRegion)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7);
    return new JSONObject()
        .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, "Zone " + pRegion)
        .put(
            ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
            new JSONArray().put(regionConfig));
  }

  private JSONObject getSingleRegionReplicationSpecWithCustomIOPS(
      final String pInstanceSize,
      final String pProvider,
      final String pRegion,
      final String pEbsVolumeType,
      final Integer pDiskIops) {
    final JSONObject regionConfig =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD, pInstanceSize)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3)
                    .put(ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD, pEbsVolumeType)
                    .put(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD, pDiskIops))
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, pProvider)
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, pRegion)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7);
    return new JSONObject()
        .put(
            ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
            new JSONArray().put(regionConfig));
  }

  private JSONObject getDefaultSingleRegionReplicationSpec(
      final String pInstanceSize,
      final String pProvider,
      final String pRegion,
      final Double pDiskSizeGB,
      final boolean pComputeAutoscalingEnabled) {
    final JSONObject regionConfig =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD, pInstanceSize)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3)
                    .put(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD, pDiskSizeGB))
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, pProvider)
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, pRegion)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7);

    if (pComputeAutoscalingEnabled) {
      final JSONObject computeAutoScaling =
          new JSONObject()
              .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
              .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M200")
              .put(ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M10")
              .put(ApiAtlasComputeAutoScalingV15View.SCALE_DOWN_ENABLED_FIELD, true);

      final JSONObject autoScaling =
          new JSONObject().put(ApiAtlasAutoScalingV15View.COMPUTE_FIELD, computeAutoScaling);

      regionConfig.put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling);
    }

    return new JSONObject()
        .put(
            ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
            new JSONArray().put(regionConfig));
  }

  private JSONObject getDefaultServerlessReplicationSpec() {
    final JSONObject regionConfig =
        new JSONObject()
            .put(
                ApiAtlasTenantRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasTenantHardwareSpecView.INSTANCE_SIZE_FIELD, "SERVERLESS_V2"))
            .put(ApiAtlasTenantRegionConfig20240805View.PROVIDER_NAME_FIELD, "SERVERLESS")
            .put(ApiAtlasTenantRegionConfig20240805View.BACKING_PROVIDER_NAME_FIELD, "GCP")
            .put(ApiAtlasTenantRegionConfig20240805View.REGION_NAME_FIELD, "WESTERN_EUROPE");
    return new JSONObject()
        .put(
            ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
            new JSONArray().put(regionConfig));
  }

  private JSONObject getDefaultFlexReplicationSpec() {
    final JSONObject regionConfig =
        new JSONObject()
            .put(
                ApiAtlasTenantRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasTenantHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "FLEX"))
            .put(ApiAtlasTenantRegionConfig20240805View.PROVIDER_NAME_FIELD, "FLEX")
            .put(ApiAtlasTenantRegionConfig20240805View.BACKING_PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasTenantRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_1");
    return new JSONObject()
        .put(
            ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD,
            new JSONArray().put(regionConfig));
  }

  private JSONObject getCrossCloudReplicationSpecEmptyRegionConfig() {
    final JSONObject autoScaling =
        new JSONObject()
            .put(
                ApiAtlasAutoScalingV15View.COMPUTE_FIELD,
                new JSONObject()
                    .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
                    .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M30"));

    final JSONObject awsRegionConfig_3electable_1readOnly =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasAWSHardwareSpec20240805View.NODE_COUNT_FIELD, 3))
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasAWSHardwareSpec20240805View.NODE_COUNT_FIELD, 1))
            .put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_1");
    final JSONObject emptyRegionConfig =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasAWSHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasAWSHardwareSpec20240805View.NODE_COUNT_FIELD, 0))
            .put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 0)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "GCP")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_4");
    return new JSONObject()
        .put(
            ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD,
            new JSONArray().put(awsRegionConfig_3electable_1readOnly).put(emptyRegionConfig));
  }

  private static JSONObject updateProviderAndRegionForClusterReplicationSpec(
      final ReplicationSpec pReplicationSpec, final RegionName pRegionName) {
    final JSONObject replicationSpec = new JSONObject();
    final JSONArray regionConfigs = new JSONArray();
    final JSONObject regionConfig = new JSONObject();
    final JSONObject hardwareSpec = new JSONObject();

    hardwareSpec.put("nodeCount", 3).put("instanceSize", "M10");

    regionConfig
        .put(ApiAtlasRegionConfig20240805View.REGION_NAME_FIELD, pRegionName.getName())
        .put(ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD, pRegionName.getProviderName())
        .put(ApiAtlasRegionConfig20240805View.PRIORITY_FIELD, 7)
        .put(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD, hardwareSpec);
    regionConfigs.put(regionConfig);

    replicationSpec
        .put(ApiAtlasReplicationSpec20240805View.ID_FIELD, pReplicationSpec.getId())
        .put(ApiAtlasReplicationSpec20240805View.ZONE_ID_FIELD, pReplicationSpec.getZoneId())
        .put(ApiAtlasReplicationSpec20240805View.ZONE_NAME_FIELD, pReplicationSpec.getZoneName())
        .put(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD, regionConfigs);

    return replicationSpec;
  }

  @Test
  public void testGrantEmployeeAccess() throws SvcException {
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(),
            "clusterName",
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0))));

    for (final EmployeeAccessGrantType grantType : EmployeeAccessGrantType.values()) {
      testGrantEmployeeAccess(grantType);
    }
  }

  private void testGrantEmployeeAccess(final EmployeeAccessGrantType pGrantType) {

    final int hoursExpiration = 15;
    final Date expiration = DateUtils.addHours(new Date(), hoursExpiration);

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + "clusterName" + ":grantMongoDBEmployeeAccess",
        new JSONObject()
            .put("grantType", pGrantType.name())
            .put("expirationTime", expiration.toInstant()),
        HttpStatus.SC_NO_CONTENT,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    final ClusterDescription clusterDescription =
        _clusterDescriptionDao.findByName(_group.getId(), "clusterName").orElseThrow();
    assertTrue(clusterDescription.getActiveEmployeeAccessGrant().isPresent());
    assertEquals(
        pGrantType, clusterDescription.getActiveEmployeeAccessGrant().get().getGrantType());
    assertTrue(
        UnitTestUtils.isRoughlyNow(
            DateUtils.addHours(
                clusterDescription.getActiveEmployeeAccessGrant().get().getExpiration(),
                -hoursExpiration)));
  }

  @Test
  public void testRevokeEmployeeAccess() throws SvcException {
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(),
            "clusterName",
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0))));

    for (final EmployeeAccessGrantType grantType : EmployeeAccessGrantType.values()) {
      testRevokeEmployeeAccess(grantType);
    }
  }

  private void testRevokeEmployeeAccess(final EmployeeAccessGrantType pGrantType) {
    final int hoursExpiration = 15;
    final Date expiration = DateUtils.addHours(new Date(), hoursExpiration);

    final ClusterDescription initialClusterDescription =
        _clusterDescriptionDao.findByName(_group.getId(), "clusterName").orElseThrow();

    _ndsClusterSvc.grantEmployeeAccess(
        initialClusterDescription,
        pGrantType,
        expiration,
        _group,
        AuditInfoHelpers.fromSystem(),
        null);

    final ClusterDescription reloadedClusterDescription =
        _clusterDescriptionDao.findByName(_group.getId(), "clusterName").orElseThrow();
    assertTrue(reloadedClusterDescription.getActiveEmployeeAccessGrant().isPresent());
    assertEquals(
        pGrantType, reloadedClusterDescription.getActiveEmployeeAccessGrant().get().getGrantType());
    assertTrue(
        UnitTestUtils.isRoughlyNow(
            DateUtils.addHours(
                reloadedClusterDescription.getActiveEmployeeAccessGrant().get().getExpiration(),
                -hoursExpiration)));

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + "clusterName" + ":revokeMongoDBEmployeeAccess",
        new JSONObject(),
        HttpStatus.SC_NO_CONTENT,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    final ClusterDescription clusterDescription =
        _clusterDescriptionDao.findByName(_group.getId(), "clusterName").orElseThrow();
    assertFalse(clusterDescription.getActiveEmployeeAccessGrant().isPresent());
  }

  @Test
  public void testGrantEmployeeAccess_missingArgs() {
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(),
            "clusterName",
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0))));

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + "clusterName" + ":grantMongoDBEmployeeAccess",
        new JSONObject().put("grantType", EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE.name()),
        HttpStatus.SC_BAD_REQUEST,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + "clusterName" + ":grantMongoDBEmployeeAccess",
        new JSONObject().put("expiration", new Date().toInstant()),
        HttpStatus.SC_BAD_REQUEST,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
  }

  @Test
  public void testGrantEmployeeAccess_invalidArgs() {
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(),
            "clusterName",
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0))));

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + "clusterName" + ":grantMongoDBEmployeeAccess",
        new JSONObject().put("grantType", "bad grant").put("expiration", new Date().toInstant()),
        HttpStatus.SC_BAD_REQUEST,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters/" + "clusterName" + ":grantMongoDBEmployeeAccess",
        new JSONObject()
            .put("grantType", EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE.name())
            .put("expiration", "bad expiration"),
        HttpStatus.SC_BAD_REQUEST,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);
  }

  @Test
  public void testGetCluster_employeeAccessGrant() throws SvcException {
    _clusterDescriptionDao.save(
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(),
            "rs1",
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0))));

    final ClusterDescription existing =
        _clusterDescriptionDao.findByName(_group.getId(), "rs1").get();
    assertTrue(existing.getEmployeeAccessGrant().isEmpty());

    final int numHours = 15;
    final Date expiration = org.apache.commons.lang.time.DateUtils.addHours(new Date(), numHours);

    _ndsClusterSvc.grantEmployeeAccess(
        existing,
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE,
        expiration,
        _group,
        AuditInfoHelpers.fromSystem(),
        null);

    // Grant present
    final JSONObject resp1 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/rs1",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V20240805_VERSION);

    assertEquals(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE.name(),
        resp1
            .getJSONObject(Fields.MONGODB_EMPLOYEE_ACCESS_GRANT)
            .getString(ApiAtlasEmployeeAccessGrantView.GRANT_TYPE_FIELD));
    assertEquals(
        org.apache.commons.lang.time.DateUtils.truncate(expiration, Calendar.SECOND)
            .toInstant()
            .toString(),
        resp1
            .getJSONObject(Fields.MONGODB_EMPLOYEE_ACCESS_GRANT)
            .getString(ApiAtlasEmployeeAccessGrantView.EXPIRATION_FIELD));

    final Date expiredDate = org.apache.commons.lang.time.DateUtils.addHours(new Date(), -numHours);

    _ndsClusterSvc.grantEmployeeAccess(
        existing,
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA,
        expiredDate,
        _group,
        AuditInfoHelpers.fromSystem(),
        null);

    // Expired grant present
    final JSONObject resp2 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/rs1",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V20240805_VERSION);

    assertEquals(
        EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA.name(),
        resp2
            .getJSONObject(Fields.MONGODB_EMPLOYEE_ACCESS_GRANT)
            .getString(ApiAtlasEmployeeAccessGrantView.GRANT_TYPE_FIELD));
    assertEquals(
        org.apache.commons.lang.time.DateUtils.truncate(expiredDate, Calendar.SECOND)
            .toInstant()
            .toString(),
        resp2
            .getJSONObject(Fields.MONGODB_EMPLOYEE_ACCESS_GRANT)
            .getString(ApiAtlasEmployeeAccessGrantView.EXPIRATION_FIELD));

    _clusterDescriptionDao.revokeEmployeeAccessGrant(_group.getId(), "rs1");

    // Grant absent
    final JSONObject resp3 =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/rs1",
            HttpStatus.SC_OK,
            ADMIN_USERNAME,
            ADMIN_API_KEY,
            V20240805_VERSION);

    assertFalse(resp3.has(Fields.MONGODB_EMPLOYEE_ACCESS_GRANT));
  }

  @Test
  public void testClusterEndpoints_replicaSetScalingStrategy_createWithoutStrategy() {
    final String clusterName = "mongodb-is-web-scale";

    // Create cluster with default strategy should NOT contain field in response
    {
      final JSONObject response =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              createDefaultSingleRegionReplicaSetView(
                  clusterName, _group.getId(), "M10", "AWS", "US_EAST_1"),
              HttpStatus.SC_CREATED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertFalse(response.has("replicaSetScalingStrategy"));
    }

    // Fetching cluster strategy should NOT contain field in response
    {
      final JSONObject response =
          doDigestJsonGetWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertFalse(response.has("replicaSetScalingStrategy"));
    }

    // Updating cluster to explicit strategy should contain field in response
    {
      final JSONObject response =
          doDigestJsonPatchWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              new JSONObject().put("replicaSetScalingStrategy", "WORKLOAD_TYPE"),
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals("WORKLOAD_TYPE", response.getString("replicaSetScalingStrategy"));
    }

    // Fetching cluster strategy should contain explicit field value in response
    {
      final JSONObject response =
          doDigestJsonGetWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals("WORKLOAD_TYPE", response.getString("replicaSetScalingStrategy"));
    }
  }

  @Test
  public void testClusterEndpoints_replicaSetScalingStrategy_createWithExplicitStrategy() {
    final String clusterName = "mongodb-is-web-scale";

    // Create cluster with explicit strategy should contain field in response
    {
      final JSONObject response =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              createDefaultSingleRegionReplicaSetView(
                      clusterName, _group.getId(), "M10", "AWS", "US_EAST_1")
                  .put("replicaSetScalingStrategy", "WORKLOAD_TYPE"),
              HttpStatus.SC_CREATED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals("WORKLOAD_TYPE", response.getString("replicaSetScalingStrategy"));
    }

    // Fetching cluster strategy should contain explicit field value in response
    {
      final JSONObject response =
          doDigestJsonGetWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals("WORKLOAD_TYPE", response.getString("replicaSetScalingStrategy"));
    }

    // Updating cluster to different strategy should contain field in response
    {
      final JSONObject response =
          doDigestJsonPatchWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              new JSONObject().put("replicaSetScalingStrategy", "NODE_TYPE"),
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals("NODE_TYPE", response.getString("replicaSetScalingStrategy"));
    }

    // Fetching cluster strategy should contain different field value in response
    {
      final JSONObject response =
          doDigestJsonGetWithApiVersion(
              BASE_URL + _group.getId() + "/clusters/" + clusterName,
              HttpStatus.SC_OK,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);
      assertEquals("NODE_TYPE", response.getString("replicaSetScalingStrategy"));
    }
  }

  @Test
  public void testCreateSharedClusterShimToFlex() throws Exception {
    // Our App setting is still at state "EXISTING" so we successfully create a shared tier cluster
    {
      final String clusterName = "DriversLicense";
      final JSONObject response =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              createDefaultTenantReplicaSetView(clusterName, _group.getId(), "M2", "US_EAST_1"),
              HttpStatus.SC_CREATED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      verifyShimmedClusterResponse(
          response,
          FreeInstanceSize.M2.name(),
          FreeInstanceSize.M2.name(),
          FreeInstanceSize.M2.getDiskSizeGB());
      assertTrue(
          _clusterDescriptionUpdatesDao
              .findByName(_group.getId(), clusterName)
              .orElseThrow()
              .isSharedTenantCluster());
    }

    // Enable our app settings but the group feature flag is turned off
    _appSettings.setProp(
        AppSettings.Fields.NDS_FLEX_API_SHARED_MODE.value, "DEPRECATED", SettingType.MEMORY);

    {
      final String clusterName = "goodForYou";
      final JSONObject response =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              createDefaultTenantReplicaSetView(clusterName, _group.getId(), "M2", "US_EAST_1"),
              HttpStatus.SC_CREATED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      verifyShimmedClusterResponse(
          response,
          FreeInstanceSize.M2.name(),
          FreeInstanceSize.M2.name(),
          FreeInstanceSize.M2.getDiskSizeGB());
      assertTrue(
          _clusterDescriptionUpdatesDao
              .findByName(_group.getId(), clusterName)
              .orElseThrow()
              .isSharedTenantCluster());
    }

    // Enable feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);

    // requesting to create a M0 cluster --> creates M0 since shim logic doesn't apply
    {
      final Group group = MmsFactory.createGroup(_organizationDao.findById(oid(313)));
      _ndsGroupSvc.ensureGroup(group.getId());
      final AppUser user = MmsFactory.createApiUserWithRoleInGroup("test", group, Role.GROUP_OWNER);
      final String apiKey = MmsFactory.generateUserApiKey(user.getId(), "apiKey").getKey();
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          group, null, FeatureFlag.LEGACY_SHARED_API_SHIM_TO_FLEX);
      _organizationDao.setLimit(oid(313), OrgLimits.FieldsDefs.MAX_M0_CLUSTERS.getValue(), 1);
      stubber.stubPaymentMethod(group.getOrgId());
      final String clusterName = "badIdeaRight";
      final JSONObject response =
          doDigestJsonPostWithApiVersion(
              BASE_URL + group.getId() + "/clusters",
              createDefaultFreeReplicaSetView(clusterName, group.getId()),
              SC_CREATED,
              user.getUsername(),
              apiKey,
              V20240805_VERSION);
      verifyShimmedClusterResponse(
          response,
          FreeInstanceSize.M0.name(),
          FreeInstanceSize.M0.name(),
          FreeInstanceSize.M0.getDiskSizeGB());
      assertTrue(
          _clusterDescriptionUpdatesDao
              .findByName(group.getId(), clusterName)
              .orElseThrow()
              .isFreeTenantCluster());
    }

    // Now we should shim the request to our flex resource and create our flex cluster internally
    {
      final String clusterName = "dejaVu";
      final JSONObject response =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              createDefaultTenantReplicaSetView(clusterName, _group.getId(), "M2", "US_EAST_1"),
              HttpStatus.SC_CREATED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      // We should still be returning the original instance size but the new disk size
      verifyShimmedClusterResponse(
          response,
          FreeInstanceSize.M2.name(),
          FlexInstanceSize.FLEX.name(),
          FlexInstanceSize.FLEX.getDiskSizeGB());
      final ClusterDescription shimmedShared =
          _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).orElseThrow();
      assertTrue(shimmedShared.isFlexTenantCluster());
      final FlexTenantMigrationState flexTenantMigrationState =
          shimmedShared.getFlexTenantMigrationState().orElseThrow();
      assertTrue(shimmedShared.isClusterCreatedFromLegacyApi());
      assertEquals(
          CloudProvider.FREE, flexTenantMigrationState.getTenantApiCloudProvider().orElseThrow());
      assertEquals(FreeInstanceSize.M2, flexTenantMigrationState.getValidMigrationInstanceSize());
    }

    // M5 example
    {
      final String clusterName = "vampire";
      final JSONObject response =
          doDigestJsonPostWithApiVersion(
              BASE_URL + _group.getId() + "/clusters",
              createDefaultTenantReplicaSetView(clusterName, _group.getId(), "M5", "US_EAST_1"),
              HttpStatus.SC_CREATED,
              _adminUser.getUsername(),
              _adminUserApiKey,
              V20240805_VERSION);

      // We should still be returning the original instance size but the new disk size
      verifyShimmedClusterResponse(
          response,
          FreeInstanceSize.M5.name(),
          FlexInstanceSize.FLEX.name(),
          FlexInstanceSize.FLEX.getDiskSizeGB());
      final ClusterDescription shimmedShared =
          _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName).orElseThrow();
      assertTrue(shimmedShared.isFlexTenantCluster());
      final FlexTenantMigrationState flexTenantMigrationState =
          shimmedShared.getFlexTenantMigrationState().orElseThrow();
      assertTrue(shimmedShared.isClusterCreatedFromLegacyApi());
      assertEquals(
          CloudProvider.FREE, flexTenantMigrationState.getTenantApiCloudProvider().orElseThrow());
      assertEquals(FreeInstanceSize.M5, flexTenantMigrationState.getValidMigrationInstanceSize());
    }
  }

  private void verifyShimmedClusterResponse(
      final JSONObject pResponse,
      final String pInstanceSize,
      final String pEffectiveInstanceSize,
      final Double pDiskSize) {
    // ensure diskSizeGB and instance size is what we requested
    final Double diskSizeGB =
        pResponse
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getDouble(ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD);

    final String instanceSize =
        pResponse
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD);

    final String effectiveInstanceSize =
        pResponse
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getJSONObject(ApiAtlasRegionConfig20240805View.ELECTABLE_SPECS_FIELD)
            .getString(ApiAtlasTenantHardwareSpec20240805View.EFFECTIVE_INSTANCE_SIZE_FIELD);
    final String providerName =
        pResponse
            .getJSONArray(ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD)
            .getJSONObject(0)
            .getJSONArray(ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD)
            .getJSONObject(0)
            .getString(ApiAtlasRegionConfig20240805View.PROVIDER_NAME_FIELD);

    assertEquals(pDiskSize, diskSizeGB, 0);
    assertEquals(pInstanceSize, instanceSize);
    assertEquals(pEffectiveInstanceSize, effectiveInstanceSize);
    assertEquals("TENANT", providerName);
  }

  @Test
  public void testCreateCluster_withAdvancedConfiguration_customCipherConfig_tls12() {
    final JSONObject createClusterPayload =
        createDefaultSingleRegionReplicaSetView(
            "cluster0", _group.getId(), "M10", "AWS", "US_EAST_1");
    createClusterPayload.put(
        ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
        new JSONObject()
            .put(
                ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE,
                TlsCipherConfigMode.CUSTOM.name())
            .put(
                ApiAtlasClusterAdvancedConfigurationView.CUSTOM_OPENSSL_CIPHER_CONFIG_TLS_12,
                new JSONArray()
                    .putAll(
                        List.of(
                            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256.name(),
                            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384.name()))));

    final JSONObject createdCluster =
        getHttpUtils()
            .post()
            .path(BASE_URL + "%s/clusters", _group.getId())
            .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
            .apiVersion(V20240805_VERSION)
            .returnType(JSONObject.class)
            .data(createClusterPayload)
            .expectedReturnStatus(SC_CREATED)
            .sendNonNull();

    assertTrue(createdCluster.has(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION));
    final JSONObject createdAdvancedConfiguration =
        createdCluster.getJSONObject(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION);
    assertEquals(
        "TLS1_2",
        createdAdvancedConfiguration.getString(
            ApiAtlasClusterAdvancedConfigurationView.MINIMUM_ENABLED_TLS_PROTOCOL));
    assertEquals(
        TlsCipherConfigMode.CUSTOM.name(),
        createdAdvancedConfiguration.getString(
            ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE));
    assertEquals(
        List.of(
            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256.name(),
            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384.name()),
        createdAdvancedConfiguration
            .getJSONArray(
                ApiAtlasClusterAdvancedConfigurationView.CUSTOM_OPENSSL_CIPHER_CONFIG_TLS_12)
            .toList());

    final Optional<ClusterDescriptionProcessArgs> processArgsOpt =
        _ndsClusterSvc.getProcessArgs(_group.getId(), "cluster0");
    assertTrue(processArgsOpt.isPresent());
    assertTrue(processArgsOpt.get().getShardArg().isPresent());
    final ProcessArguments2_6 shardArgs = processArgsOpt.get().getShardArg().get();
    assertEquals("TLS1_0,TLS1_1", shardArgs.getTlsDisabledProtocols());
    assertEquals(
        String.format(
            "%s:%s",
            CipherSuite.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256.getOpensslName(),
            CipherSuite.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384.getOpensslName()),
        shardArgs.getSetParameterMap().get("opensslCipherConfig"));
  }

  @Test
  public void testUpdateCluster_withAdvancedConfiguration_customCipherConfig_tls12() {
    final JSONObject createdCluster =
        getHttpUtils()
            .post()
            .path(BASE_URL + "%s/clusters", _group.getId())
            .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
            .apiVersion(V20240805_VERSION)
            .returnType(JSONObject.class)
            .data(
                createDefaultSingleRegionReplicaSetView(
                    "cluster0", _group.getId(), "M10", "AWS", "US_EAST_1"))
            .expectedReturnStatus(SC_CREATED)
            .sendNonNull();

    assertTrue(createdCluster.has(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION));
    final JSONObject createdAdvancedConfiguration =
        createdCluster.getJSONObject(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION);
    assertEquals(
        "TLS1_2",
        createdAdvancedConfiguration.getString(
            ApiAtlasClusterAdvancedConfigurationView.MINIMUM_ENABLED_TLS_PROTOCOL));
    assertEquals(
        TlsCipherConfigMode.DEFAULT.name(),
        createdAdvancedConfiguration.getString(
            ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE));
    assertEquals(
        List.of(),
        createdAdvancedConfiguration
            .getJSONArray(
                ApiAtlasClusterAdvancedConfigurationView.CUSTOM_OPENSSL_CIPHER_CONFIG_TLS_12)
            .toList());

    final JSONObject cdViewWithAdvancedConfiguration =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION,
                new JSONObject()
                    .put(
                        ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE,
                        TlsCipherConfigMode.CUSTOM.name())
                    .put(
                        ApiAtlasClusterAdvancedConfigurationView
                            .CUSTOM_OPENSSL_CIPHER_CONFIG_TLS_12,
                        new JSONArray()
                            .putAll(
                                List.of(
                                    CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256.name(),
                                    CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
                                        .name()))));

    final JSONObject updatedCluster =
        getHttpUtils()
            .patch()
            .path(BASE_URL + "%s/clusters/%s", _group.getId(), "cluster0")
            .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
            .apiVersion(V20240805_VERSION)
            .returnType(JSONObject.class)
            .data(cdViewWithAdvancedConfiguration)
            .expectedReturnStatus(HttpStatus.SC_OK)
            .sendNonNull();

    assertTrue(updatedCluster.has(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION));
    final JSONObject updatedAdvancedConfiguration =
        updatedCluster.getJSONObject(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION);
    assertEquals(
        "TLS1_2",
        updatedAdvancedConfiguration.getString(
            ApiAtlasClusterAdvancedConfigurationView.MINIMUM_ENABLED_TLS_PROTOCOL));
    assertEquals(
        TlsCipherConfigMode.CUSTOM.name(),
        updatedAdvancedConfiguration.getString(
            ApiAtlasClusterAdvancedConfigurationView.TLS_CIPHER_CONFIG_MODE));
    assertEquals(
        List.of(
            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256.name(),
            CipherSuiteTLS12.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384.name()),
        updatedAdvancedConfiguration
            .getJSONArray(
                ApiAtlasClusterAdvancedConfigurationView.CUSTOM_OPENSSL_CIPHER_CONFIG_TLS_12)
            .toList());

    final Optional<ClusterDescriptionProcessArgs> processArgsOpt =
        _ndsClusterSvc.getProcessArgs(_group.getId(), "cluster0");
    assertTrue(processArgsOpt.isPresent());
    assertTrue(processArgsOpt.get().getShardArg().isPresent());
    final ProcessArguments2_6 shardArgs = processArgsOpt.get().getShardArg().get();
    assertEquals("TLS1_0,TLS1_1", shardArgs.getTlsDisabledProtocols());
    assertEquals(
        String.format(
            "%s:%s",
            CipherSuite.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256.getOpensslName(),
            CipherSuite.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384.getOpensslName()),
        shardArgs.getSetParameterMap().get("opensslCipherConfig"));
  }

  @Test
  public void testUpdateProcessArgs_defaultReadMaxTime() {
    // Create a cluster first
    final JSONObject createClusterPayload =
        createDefaultSingleRegionReplicaSetView(
            "cluster0", _group.getId(), "M10", "AWS", "US_EAST_1", "8.0");

    final JSONObject createdCluster =
        getHttpUtils()
            .post()
            .path(BASE_URL + "%s/clusters", _group.getId())
            .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
            .apiVersion(V20240805_VERSION)
            .returnType(JSONObject.class)
            .data(createClusterPayload)
            .expectedReturnStatus(SC_CREATED)
            .sendNonNull();

    // Verify the cluster was created
    assertEquals(
        "cluster0", createdCluster.getString(ApiAtlasClusterDescription20240805View.NAME_FIELD));

    // Set defaultReadMaxTime to 500 via the processArgs endpoint
    final JSONObject processArgsView =
        new JSONObject()
            .put(ApiAtlasClusterDescriptionProcessArgs20240805View.DEFAULT_READ_MAX_TIME, 500);

    // Call the updateProcessArgs endpoint
    final JSONObject updatedProcessArgs =
        getHttpUtils()
            .patch()
            .path(BASE_URL + "%s/clusters/cluster0/processArgs", _group.getId())
            .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
            .apiVersion(V20240805_VERSION)
            .returnType(JSONObject.class)
            .data(processArgsView)
            .expectedReturnStatus(SC_OK)
            .sendNonNull();

    // Verify the defaultReadMaxTime was updated
    assertEquals(
        500,
        updatedProcessArgs.getInt(
            ApiAtlasClusterDescriptionProcessArgs20240805View.DEFAULT_READ_MAX_TIME));

    // Get the process args directly from the service to verify the update was persisted
    final Optional<ClusterDescriptionProcessArgs> processArgsOpt =
        _ndsClusterSvc.getProcessArgs(_group.getId(), "cluster0");
    assertTrue(processArgsOpt.isPresent());

    final ClusterDescriptionProcessArgs processArgs = processArgsOpt.get();
    assertTrue(processArgs.getSetClusterParameter().isPresent());
    assertEquals(
        Integer.valueOf(500), processArgs.getSetClusterParameter().get().get("defaultMaxTimeMS"));

    { // Update other args and verify defaultReadMaxTime is not changed
      final JSONObject processArgsView1 =
          new JSONObject()
              .put(ApiAtlasClusterDescriptionProcessArgs20240805View.JAVASCRIPT_ENABLED, true);

      final JSONObject updatedProcessArgs1 =
          getHttpUtils()
              .patch()
              .path(BASE_URL + "%s/clusters/cluster0/processArgs", _group.getId())
              .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
              .apiVersion(V20240805_VERSION)
              .returnType(JSONObject.class)
              .data(processArgsView1)
              .expectedReturnStatus(SC_OK)
              .sendNonNull();

      // Verify the defaultReadMaxTime was not updated
      assertEquals(
          500,
          updatedProcessArgs1.getInt(
              ApiAtlasClusterDescriptionProcessArgs20240805View.DEFAULT_READ_MAX_TIME));

      // Get the process args directly from the service to verify the update was persisted
      final Optional<ClusterDescriptionProcessArgs> processArgsOpt1 =
          _ndsClusterSvc.getProcessArgs(_group.getId(), "cluster0");
      assertTrue(processArgsOpt1.isPresent());

      final ClusterDescriptionProcessArgs processArgs1 = processArgsOpt1.get();
      assertTrue(processArgs1.getSetClusterParameter().isPresent());
      assertEquals(500, processArgs1.getSetClusterParameter().get().get("defaultMaxTimeMS"));
    }

    { // Disable defaultReadMaxTime
      final JSONObject processArgsView1 =
          new JSONObject()
              .put(ApiAtlasClusterDescriptionProcessArgs20240805View.DEFAULT_READ_MAX_TIME, 0);

      final JSONObject updatedProcessArgs1 =
          getHttpUtils()
              .patch()
              .path(BASE_URL + "%s/clusters/cluster0/processArgs", _group.getId())
              .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
              .apiVersion(V20240805_VERSION)
              .returnType(JSONObject.class)
              .data(processArgsView1)
              .expectedReturnStatus(SC_OK)
              .sendNonNull();

      // Verify the defaultReadMaxTime was updated
      assertEquals(
          0,
          updatedProcessArgs1.getInt(
              ApiAtlasClusterDescriptionProcessArgs20240805View.DEFAULT_READ_MAX_TIME));

      // Get the process args directly from the service to verify the update was persisted
      final Optional<ClusterDescriptionProcessArgs> processArgsOpt1 =
          _ndsClusterSvc.getProcessArgs(_group.getId(), "cluster0");
      assertTrue(processArgsOpt1.isPresent());

      final ClusterDescriptionProcessArgs processArgs1 = processArgsOpt1.get();
      assertTrue(processArgs1.getSetClusterParameter().isPresent());
      assertEquals(0, processArgs1.getSetClusterParameter().get().get("defaultMaxTimeMS"));
    }
  }

  @Test
  public void testUpdateProcessArgs_nonExistentClusterErrorsGracefully() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organizationDao.findById(_group.getOrgId()), FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null,
        _organizationDao.findById(_group.getOrgId()),
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);

    final JSONObject response =
        getHttpUtils()
            .patch()
            .path(BASE_URL + "%s/clusters/%s/processArgs", _group.getId(), "nonexistent")
            .digestAuth(_adminUser.getUsername(), _adminUserApiKey)
            .apiVersion(V20240805_VERSION)
            .returnType(JSONObject.class)
            .data(new JSONObject())
            .expectedReturnStatus(SC_BAD_REQUEST)
            .sendNonNull();

    assertEquals(
        ApiErrorCode.INVALID_ATTRIBUTE.name(), response.getString(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testGetCluster_tenantClusterDoesNotReturnAdvancedConfiguration() {
    final String clusterName = "createTenantCluster";
    final JSONObject createResponse =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultTenantReplicaSetView(clusterName, _group.getId(), "M2", "US_EAST_1"),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertFalse(createResponse.has(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION));

    final JSONObject getResponse =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertFalse(getResponse.has(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION));
  }

  @Test
  public void testGetCluster_dedicatedClusterReturnsAdvancedConfiguration() {
    final String clusterName = "createDedicatedCluster";
    final JSONObject createResponse =
        doDigestJsonPostWithApiVersion(
            BASE_URL + _group.getId() + "/clusters",
            createDefaultSingleRegionReplicaSetView(
                clusterName, _group.getId(), "M10", "AWS", "US_EAST_1"),
            HttpStatus.SC_CREATED,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertTrue(createResponse.has(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION));

    final JSONObject response =
        doDigestJsonGetWithApiVersion(
            BASE_URL + _group.getId() + "/clusters/" + clusterName,
            HttpStatus.SC_OK,
            _adminUser.getUsername(),
            _adminUserApiKey,
            V20240805_VERSION);
    assertTrue(response.has(ApiAtlasClusterDescription20240805View.ADVANCED_CONFIGURATION));
  }

  @Test
  public void testAutoShardingApiIntegration() throws JSONException {
    // Test actual API endpoint integration - Create cluster with auto-sharding via v2.0 API
    final String clusterName = "auto-sharding-api-test";

    // Enable auto-sharding feature flag for the group
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.HORIZONTAL_SCALING_AUTO_SHARDING);

    // Create cluster request with auto-sharding configuration
    final JSONObject autoShardingConfig =
        new JSONObject()
            .put("enabled", true)
            .put("maxShards", 50)
            .put("thresholdToAutoShardInBytes", 2_000_000L);

    final JSONObject clusterRequest =
        createSymmetricShardedClusterView(clusterName, _group.getId(), 2)
            .put("autoSharding", autoShardingConfig);

    doDigestJsonPostWithApiVersion(
        BASE_URL + _group.getId() + "/clusters",
        clusterRequest,
        HttpStatus.SC_CREATED,
        _adminUser.getUsername(),
        _adminUserApiKey,
        V20240805_VERSION);

    // The auto-sharding field is hidden from the response and also from the get API.
    // So in order to verify this, we need to fetch the cluster description from database directly
    // and verify the field exists on cluster description that is sitting in the update queue.
    final Optional<ClusterDescription> clusterFromDb =
        _clusterDescriptionUpdatesDao.findByName(_group.getId(), clusterName);
    assertTrue("Cluster should exist in database", clusterFromDb.isPresent());

    final ClusterDescription cluster = clusterFromDb.get();
    // Verify auto-sharding configuration was saved correctly
    final Optional<AutoSharding> autoSharding = cluster.getAutoSharding();
    assertTrue("Auto-sharding should be present", autoSharding.isPresent());

    final AutoSharding savedAutoSharding = autoSharding.get();
    assertTrue("Auto-sharding should be enabled", savedAutoSharding.isEnabled());
    assertEquals("Max shards should match", 50, savedAutoSharding.getMaxShards());
    assertEquals(
        "Threshold should match", 2_000_000L, savedAutoSharding.getThresholdToAutoShardInBytes());
  }
}
