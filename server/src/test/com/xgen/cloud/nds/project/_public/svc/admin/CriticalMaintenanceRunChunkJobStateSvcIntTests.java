package com.xgen.cloud.nds.project._public.svc.admin;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.model.JobHandlerEnum;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.nds.common._public.model.ChunkedJobState.Status;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.admin.CriticalMaintenanceRunChunkedJobStateDao;
import com.xgen.cloud.nds.project._public.model.admin.CriticalMaintenanceRunChunkedJobState;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class CriticalMaintenanceRunChunkJobStateSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private CriticalMaintenanceRunChunkJobStateSvc _svc;

  @Inject private CriticalMaintenanceRunChunkedJobStateDao _dao;

  @Inject private JobsProcessorSvc _jobsProcessorSvc;

  private CriticalMaintenanceRunChunkedJobState createCriticalMaintenanceRunMetadata() {
    return createCriticalMaintenanceRunMetadata(ObjectId.get(), Status.NOT_STARTED);
  }

  private CriticalMaintenanceRunChunkedJobState createCriticalMaintenanceRunMetadata(
      final ObjectId pJobId, final Status pStatus) {
    final String targetVersion = "2.0.0.0";
    final int rolloutDuration = 4;
    final SoftwareType criticalMaintenanceType = SoftwareType.ATLAS_PROXY;
    final CriticalMaintenanceRunChunkedJobState.Parameters parameters =
        new CriticalMaintenanceRunChunkedJobState.Parameters(
            criticalMaintenanceType, targetVersion, rolloutDuration, new ArrayList<>(), null);
    return new CriticalMaintenanceRunChunkedJobState(
        pJobId, new Date(831L), new Date(831L), 100, 0, pStatus, parameters);
  }

  @Test
  public void testCreateJob() {

    final CriticalMaintenanceRunChunkedJobState criticalMaintenanceRun =
        createCriticalMaintenanceRunMetadata();
    final CriticalMaintenanceRunChunkedJobState resultState =
        _svc.startCriticalMaintenanceRunJob(
            criticalMaintenanceRun.getId(),
            criticalMaintenanceRun.getParameters().getCriticalMaintenanceType(),
            criticalMaintenanceRun.getParameters().getTargetVersion(),
            criticalMaintenanceRun.getParameters().getRolloutDurationHours());

    assertEquals(
        criticalMaintenanceRun.getParameters().getTargetVersion(),
        resultState.getParameters().getTargetVersion());
    assertEquals(
        criticalMaintenanceRun.getParameters().getCriticalMaintenanceType(),
        resultState.getParameters().getCriticalMaintenanceType());
    assertEquals(
        criticalMaintenanceRun.getParameters().getRolloutDurationHours(),
        resultState.getParameters().getRolloutDurationHours());

    // validate job
    final Job resultJob = _jobsProcessorSvc.findJobById(criticalMaintenanceRun.getId());
    assertEquals(
        JobHandlerEnum.CRITICAL_MAINTENANCE_RUN_JOB_HANDLER.getValue(), resultJob.getWorker());
    assertEquals(
        CriticalMaintenanceRunChunkJobStateSvc.CRITICAL_MAINTENANCE_JOB_RETRIES,
        resultJob.getRetriesRemaining());
    assertTrue(new Date(resultJob.getScheduledFor()).before(new Date()));
  }

  @Test
  public void testFindCriticalMaintenanceRuns() {
    final CriticalMaintenanceRunChunkedJobState criticalMaintenanceRun =
        createCriticalMaintenanceRunMetadata();

    _svc.startCriticalMaintenanceRunJob(
        criticalMaintenanceRun.getId(),
        criticalMaintenanceRun.getParameters().getCriticalMaintenanceType(),
        criticalMaintenanceRun.getParameters().getTargetVersion(),
        criticalMaintenanceRun.getParameters().getRolloutDurationHours());

    _dao.setStatus(criticalMaintenanceRun.getId(), Status.NOT_STARTED);
    assertEquals(1, _svc.findCriticalMaintenanceRuns(Status.NOT_STARTED).size());
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.PROCESSING).size());
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.COMPLETED).size());
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.FAILED).size());

    _dao.setStatus(criticalMaintenanceRun.getId(), Status.PROCESSING);
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.NOT_STARTED).size());
    assertEquals(1, _svc.findCriticalMaintenanceRuns(Status.PROCESSING).size());
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.COMPLETED).size());
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.FAILED).size());

    _dao.setStatus(criticalMaintenanceRun.getId(), Status.COMPLETED);
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.NOT_STARTED).size());
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.PROCESSING).size());
    assertEquals(1, _svc.findCriticalMaintenanceRuns(Status.COMPLETED).size());
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.FAILED).size());

    _dao.setStatus(criticalMaintenanceRun.getId(), Status.FAILED);
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.NOT_STARTED).size());
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.PROCESSING).size());
    assertEquals(0, _svc.findCriticalMaintenanceRuns(Status.COMPLETED).size());
    assertEquals(1, _svc.findCriticalMaintenanceRuns(Status.FAILED).size());
  }

  @Test
  public void testCancelCriticalMaintenanceJob() throws SvcException {
    final CriticalMaintenanceRunChunkedJobState criticalMaintenanceRun =
        createCriticalMaintenanceRunMetadata();

    _svc.startCriticalMaintenanceRunJob(
        criticalMaintenanceRun.getId(),
        criticalMaintenanceRun.getParameters().getCriticalMaintenanceType(),
        criticalMaintenanceRun.getParameters().getTargetVersion(),
        criticalMaintenanceRun.getParameters().getRolloutDurationHours());

    _svc.cancelCriticalMaintenanceJob(criticalMaintenanceRun);

    final CriticalMaintenanceRunChunkedJobState updatedState =
        _svc.findOne(criticalMaintenanceRun.getId())
            .orElseThrow(
                () ->
                    new AssertionError(
                        "CriticalMaintenanceRunChunkedJobState not found for id : "
                            + criticalMaintenanceRun.getId()));

    assertEquals(
        Job.Status.CANCELLED,
        _jobsProcessorSvc.findJobById(criticalMaintenanceRun.getId()).getStatus());
    assertEquals(Status.CANCELLED, updatedState.getStatus());

    try {
      _svc.cancelCriticalMaintenanceJob(updatedState);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
  }

  @Test
  public void testRetryCriticalMaintenanceJob() throws SvcException {
    final ObjectId jobId = ObjectId.get();
    final CriticalMaintenanceRunChunkedJobState notStartedCriticalMaintenanceRun =
        createCriticalMaintenanceRunMetadata(jobId, Status.NOT_STARTED);
    final CriticalMaintenanceRunChunkedJobState failedCriticalMaintenanceRun =
        createCriticalMaintenanceRunMetadata(jobId, Status.FAILED);

    final CriticalMaintenanceRunChunkedJobState createdJobState =
        _svc.startCriticalMaintenanceRunJob(
            notStartedCriticalMaintenanceRun.getId(),
            notStartedCriticalMaintenanceRun.getParameters().getCriticalMaintenanceType(),
            notStartedCriticalMaintenanceRun.getParameters().getTargetVersion(),
            notStartedCriticalMaintenanceRun.getParameters().getRolloutDurationHours());

    assertEquals(
        Job.Status.NEW, _jobsProcessorSvc.findJobById(createdJobState.getId()).getStatus());
    assertEquals(Status.NOT_STARTED, createdJobState.getStatus());

    // Retrying a job in non-terminal state should fail
    try {
      _svc.retryCriticalMaintenanceJob(createdJobState);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }

    // Retrying a job where there is a matching critical maintenance job in a non-terminal state
    // should fail - race condition protection
    try {
      _svc.retryCriticalMaintenanceJob(failedCriticalMaintenanceRun);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.CRITICAL_MAINTENANCE_IN_PROGRESS, e.getErrorCode());
    }

    // Retrying a failed job should succeed
    _svc.setStatus(jobId, Status.FAILED);
    _svc.retryCriticalMaintenanceJob(failedCriticalMaintenanceRun);

    final CriticalMaintenanceRunChunkedJobState retriedState =
        _svc.findOne(notStartedCriticalMaintenanceRun.getId())
            .orElseThrow(
                () ->
                    new AssertionError(
                        "CriticalMaintenanceRunChunkedJobState not found for id : "
                            + notStartedCriticalMaintenanceRun.getId()));

    assertEquals(
        Job.Status.NEW,
        _jobsProcessorSvc.findJobById(notStartedCriticalMaintenanceRun.getId()).getStatus());
    assertEquals(Status.NOT_STARTED, retriedState.getStatus());
  }

  @Test
  public void testSetStatus() {
    final CriticalMaintenanceRunChunkedJobState criticalMaintenanceRun =
        createCriticalMaintenanceRunMetadata();

    _svc.startCriticalMaintenanceRunJob(
        criticalMaintenanceRun.getId(),
        criticalMaintenanceRun.getParameters().getCriticalMaintenanceType(),
        criticalMaintenanceRun.getParameters().getTargetVersion(),
        criticalMaintenanceRun.getParameters().getRolloutDurationHours());

    assertThat(_svc.findOne(criticalMaintenanceRun.getId()))
        .isPresent()
        .hasValueSatisfying(
            maintenance -> assertEquals(Status.NOT_STARTED, maintenance.getStatus()));

    _svc.setStatus(criticalMaintenanceRun.getId(), Status.FAILED);

    assertThat(_svc.findOne(criticalMaintenanceRun.getId()))
        .isPresent()
        .hasValueSatisfying(maintenance -> assertEquals(Status.FAILED, maintenance.getStatus()));
  }
}
