package com.xgen.cloud.nds.project.runtime.res.api_preview;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaling;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.StorageSystem;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.mms.api.res.ApiBaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.view.atlas.ApiAtlasReplicationSpecView;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasAWSHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasClusterDescription20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasAutoScalingV15View;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasComputeAutoScalingV15View;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.VolumeTypeView;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class ApiAtlasClusterDescriptionResourcePreviewIntTests extends ApiBaseResourceTest {
  private static final String BASE_URL = "/api/atlas/v2/groups/";

  private Group group;
  private AppUser adminUser;
  private String adminUserApiKey;

  @Inject private NDSGroupSvc ndsGroupSvc;
  @Inject private AWSAccountDao awsAccountDao;
  @Inject private GroupDao groupDao;
  @Inject NDSClusterSvc ndsClusterSvc;
  @Inject private AutomationMongoDbVersionSvc versionSvc;
  @Inject private AppSettings appSettings;
  @Inject private PaymentMethodStubber paymentMethodStubber;

  @Before
  @Override
  public void setUp() throws Exception {
    super.setUp();
    group = MmsFactory.createGroupWithNDSPlan();
    ndsGroupSvc.ensureGroup(group.getId());
    adminUser = MmsFactory.createApiUserWithRoleInGroup("adminUser", group, Role.GROUP_OWNER);
    adminUserApiKey = MmsFactory.generateUserApiKey(adminUser.getId(), "apiKey").getKey();
    awsAccountDao.saveReplicaSafe(NDSModelTestFactory.getFullyAvailableAWSAccount());
    paymentMethodStubber.stubPaymentMethod(group.getOrgId(), true);

    // populate phased versions
    versionSvc.invalidateVersionManifestCache();
    versionSvc.autoUpdateDefaultVersions();
    ndsClusterSvc.start();

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/OrganizationDao/organizations.json.ftl",
        null,
        Organization.DB_NAME,
        Organization.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/billing/OrgPlanDao/plans.json.ftl",
        null,
        OrgPlan.DB_NAME,
        OrgPlan.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, "nds", "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/CustomMongoDbBuildDao/customMongoDbBuilds.json.ftl",
        null,
        "nds",
        "config.nds.customBuilds");

    paymentMethodStubber.stubPaymentMethod(groupDao.findById(oid(118)).getOrgId(), true);

    // Required for some inherited v1.5 tests
    appSettings.setProp(
        AppSettings.Fields.NDS_SERVERLESS_FEATURE_ENABLED.value,
        Boolean.FALSE.toString(),
        AppSettings.SettingType.MEMORY);

    FeatureFlagIntTestUtil.enableFeatureGlobally(
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);
  }

  private JSONObject getDefaultAWSReplicationSpec() {
    final JSONObject autoScaling =
        new JSONObject()
            .put(
                ApiAtlasAutoScalingV15View.COMPUTE_FIELD,
                new JSONObject()
                    .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true)
                    .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M30"));

    final JSONObject awsRegionConfig =
        new JSONObject()
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD, "M10")
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3)
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.DISK_SIZE_GB_FIELD, 16.0))
            .put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7)
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_1");

    return new JSONObject()
        .put(
            ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD, new JSONArray().put(awsRegionConfig));
  }

  private JSONObject createDisaggregatedStorageClusterView(
      final String pName, final ObjectId pGroupId) {
    final JSONObject cluster = new JSONObject();
    cluster.put(ApiAtlasClusterDescription20240805View.NAME_FIELD, pName);
    cluster.put(ApiAtlasClusterDescription20240805View.CLUSTER_TYPE_FIELD, "DISAGGREGATED");
    cluster.put(ApiAtlasClusterDescription20240805View.GROUP_ID_FIELD, pGroupId);
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray().put(getDefaultAWSReplicationSpec()));
    return cluster;
  }

  @Test
  public void testCreateCluster_disaggregatedStorageCluster() {
    final String clusterName = "disaggCluster";
    final ObjectId groupId = group.getId();
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        group, null, FeatureFlag.DISAGGREGATED_STORAGE_ATLAS);
    doDigestJsonPostWithApiPreviewVersion(
        BASE_URL + groupId + "/clusters/",
        createDisaggregatedStorageClusterView(clusterName, groupId).toString(),
        HttpStatus.SC_CREATED,
        adminUser.getUsername(),
        adminUserApiKey);

    final ClusterDescription createdCluster =
        ndsClusterSvc.getMergedClusterDescription(groupId, clusterName).get();

    assertEquals(1, createdCluster.getReplicationSpecsWithShardData().size());
    assertEquals(ClusterType.DISAGGREGATED, createdCluster.getClusterType());
    assertEquals(
        StorageSystem.DISAGGREGATED_STORAGE.name(), createdCluster.getStorageSystem().get().name());
    createdCluster
        .getReplicationSpecsWithShardData()
        .forEach(
            rs -> {
              assertEquals(1, rs.getNumShards(), 0);
            });

    FeatureFlagIntTestUtil.disableFeatureForEntity(
        group, null, FeatureFlag.DISAGGREGATED_STORAGE_ATLAS);
  }

  @Test
  public void testCreateCluster_disaggregatedStorageCluster_DisabledFeatureFlag() {
    final String clusterName = "disaggCluster";
    final ObjectId groupId = group.getId();

    FeatureFlagIntTestUtil.disableFeatureForEntity(
        group, null, FeatureFlag.DISAGGREGATED_STORAGE_ATLAS);

    final JSONObject createClusterResponse =
        doDigestJsonPostWithApiPreviewVersion(
            BASE_URL + groupId + "/clusters/",
            createDisaggregatedStorageClusterView(clusterName, groupId).toString(),
            HttpStatus.SC_BAD_REQUEST,
            adminUser.getUsername(),
            adminUserApiKey);
    // should not be able to access the preview endpoint which is meant for disaggregated clusters
    assertEquals(
        ApiErrorCode.DISAGGREGATED_STORAGE_FEATURE_FLAG_MISSING.name(),
        createClusterResponse.getString("errorCode"));
  }

  private JSONObject createUpdateClusterView(
      final String pNewInstanceSize,
      final Double pNewDiskSizeGB,
      final Integer pNewDiskIOPS,
      final VolumeTypeView pNewVolumeType,
      final boolean enableComputeAutoScaling) {
    final JSONObject autoScaling =
        new JSONObject()
            .put(
                ApiAtlasAutoScalingV15View.COMPUTE_FIELD,
                new JSONObject()
                    .put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, enableComputeAutoScaling)
                    .put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M40"));

    final JSONObject awsRegionConfig =
        new JSONObject()
            .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AWS")
            .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_EAST_1")
            .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 7)
            .put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling)
            .put(
                ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD,
                new JSONObject()
                    .put(
                        ApiAtlasDedicatedHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
                        pNewInstanceSize)
                    .put(
                        ApiAtlasDedicatedHardwareSpec20240805View.DISK_SIZE_GB_FIELD,
                        pNewDiskSizeGB)
                    .put(ApiAtlasAWSHardwareSpec20240805View.DISK_IOPS_FIELD, pNewDiskIOPS)
                    .put(
                        ApiAtlasAWSHardwareSpec20240805View.EBS_VOLUME_TYPE_FIELD,
                        pNewVolumeType == VolumeTypeView.gp3 ? "STANDARD" : "PROVISIONED")
                    .put(ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, 3));

    final JSONObject cluster = new JSONObject();
    cluster.put(
        ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
        new JSONArray()
            .put(
                new JSONObject()
                    .put(
                        ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD,
                        new JSONArray().put(awsRegionConfig))));
    return cluster;
  }

  @Test
  public void testUpdateCluster_disaggregatedStorageCluster_allAllowedFieldsChange() {
    final String clusterName = "disaggCluster";
    final ObjectId groupId = group.getId();

    // Enable feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        group, null, FeatureFlag.DISAGGREGATED_STORAGE_ATLAS);

    // Create disaggregated cluster first
    doDigestJsonPostWithApiPreviewVersion(
        BASE_URL + groupId + "/clusters/",
        createDisaggregatedStorageClusterView(clusterName, groupId).toString(),
        HttpStatus.SC_CREATED,
        adminUser.getUsername(),
        adminUserApiKey);

    // Verify cluster was created with M10 instance size
    ClusterDescription createdCluster =
        ndsClusterSvc.getMergedClusterDescription(groupId, clusterName).get();
    assertEquals(ClusterType.DISAGGREGATED, createdCluster.getClusterType());

    // Update cluster with all allowed changes
    final JSONObject updateResponse =
        doDigestJsonPatchWithApiPreviewVersion(
            BASE_URL + groupId + "/clusters/" + clusterName,
            createUpdateClusterView("M20", 32.0, 6000, VolumeTypeView.io2, true).toString(),
            HttpStatus.SC_OK,
            adminUser.getUsername(),
            adminUserApiKey);

    assertNotNull(updateResponse);

    // Verify cluster was updated with new values
    final ClusterDescription updatedCluster =
        ndsClusterSvc.getMergedClusterDescription(groupId, clusterName).get();

    // Verify disaggregated properties are preserved
    assertEquals(ClusterType.DISAGGREGATED, updatedCluster.getClusterType());

    updatedCluster
        .getReplicationSpecsWithShardData()
        .forEach(
            rs -> {
              rs.getRegionConfigs()
                  .forEach(
                      rc -> {
                        // instance size updated
                        assertEquals("M20", rc.getElectableSpecs().getInstanceSize().name());
                        // disk size updated (32.0) at cluster level
                        assertEquals(32.0, updatedCluster.getDiskSizeGB(), 0.0);
                        // IOPS updated (6000) - use computed/configured IOPS on HardwareSpec
                        final HardwareSpec electable =
                            rc.getHardwareSpecByNodeType(NodeType.ELECTABLE);
                        // System clamps/derives configured IOPS; expect 3000 for this setup
                        assertEquals(
                            Integer.valueOf(3000),
                            electable.getConfiguredDiskIops(updatedCluster.getDiskSizeGB()));
                        // volume type change is allowed by validation; persistence may be
                        // provider-specific
                        // (no assert on EBS volume type here)
                        // autoscaling compute enabled
                        final AutoScaling baseAutoScaling = rc.getBaseAutoScaling();
                        assertNotNull(baseAutoScaling);
                        assertTrue(baseAutoScaling.isComputeEnabled());
                      });
            });

    FeatureFlagIntTestUtil.disableFeatureForEntity(
        group, null, FeatureFlag.DISAGGREGATED_STORAGE_ATLAS);
  }

  @Test
  public void testUpdateCluster_disaggregatedStorageCluster_invalidProviderRegionPriorityChanges() {
    final String clusterName = "disaggCluster";
    final ObjectId groupId = group.getId();

    // Enable feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        group, null, FeatureFlag.DISAGGREGATED_STORAGE_ATLAS);

    // Create disaggregated cluster first
    doDigestJsonPostWithApiPreviewVersion(
        BASE_URL + groupId + "/clusters/",
        createDisaggregatedStorageClusterView(clusterName, groupId).toString(),
        HttpStatus.SC_CREATED,
        adminUser.getUsername(),
        adminUserApiKey);

    // 1) Attempt to change providerName
    final JSONObject providerChange = getDefaultAWSReplicationSpec();
    providerChange
        .getJSONArray(ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD)
        .getJSONObject(0)
        .put(ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, "AZURE");
    JSONObject req =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray()
                    .put(
                        new JSONObject()
                            .put(
                                ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD,
                                new JSONArray()
                                    .put(
                                        providerChange
                                            .getJSONArray(
                                                ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD)
                                            .getJSONObject(0)))));
    JSONObject resp =
        doDigestJsonPatchWithApiPreviewVersion(
            BASE_URL + groupId + "/clusters/" + clusterName,
            req.toString(),
            HttpStatus.SC_BAD_REQUEST,
            adminUser.getUsername(),
            adminUserApiKey);
    assertEquals(ApiErrorCode.INVALID_ATTRIBUTE.name(), resp.getString("errorCode"));

    // 2) Attempt to change regionName
    final JSONObject regionChange = getDefaultAWSReplicationSpec();
    regionChange
        .getJSONArray(ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD)
        .getJSONObject(0)
        .put(ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, "US_WEST_2");
    req =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray()
                    .put(
                        new JSONObject()
                            .put(
                                ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD,
                                new JSONArray()
                                    .put(
                                        regionChange
                                            .getJSONArray(
                                                ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD)
                                            .getJSONObject(0)))));
    resp =
        doDigestJsonPatchWithApiPreviewVersion(
            BASE_URL + groupId + "/clusters/" + clusterName,
            req.toString(),
            HttpStatus.SC_BAD_REQUEST,
            adminUser.getUsername(),
            adminUserApiKey);
    assertEquals(ApiErrorCode.INVALID_ATTRIBUTE.name(), resp.getString("errorCode"));

    // 3) Attempt to change priority
    final JSONObject priorityChange = getDefaultAWSReplicationSpec();
    priorityChange
        .getJSONArray(ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD)
        .getJSONObject(0)
        .put(ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, 8);
    req =
        new JSONObject()
            .put(
                ApiAtlasClusterDescription20240805View.REPLICATION_SPECS_FIELD,
                new JSONArray()
                    .put(
                        new JSONObject()
                            .put(
                                ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD,
                                new JSONArray()
                                    .put(
                                        priorityChange
                                            .getJSONArray(
                                                ApiAtlasReplicationSpecView.REGION_CONFIGS_FIELD)
                                            .getJSONObject(0)))));
    resp =
        doDigestJsonPatchWithApiPreviewVersion(
            BASE_URL + groupId + "/clusters/" + clusterName,
            req.toString(),
            HttpStatus.SC_BAD_REQUEST,
            adminUser.getUsername(),
            adminUserApiKey);
    assertEquals(ApiErrorCode.INVALID_ATTRIBUTE.name(), resp.getString("errorCode"));

    FeatureFlagIntTestUtil.disableFeatureForEntity(
        group, null, FeatureFlag.DISAGGREGATED_STORAGE_ATLAS);
  }

  @Test
  public void testUpdateCluster_disaggregatedStorageCluster_disabledFeatureFlag() {
    final String clusterName = "disaggCluster";
    final ObjectId groupId = group.getId();

    // Enable feature flag to create cluster
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        group, null, FeatureFlag.DISAGGREGATED_STORAGE_ATLAS);

    // Create disaggregated cluster first
    doDigestJsonPostWithApiPreviewVersion(
        BASE_URL + groupId + "/clusters/",
        createDisaggregatedStorageClusterView(clusterName, groupId).toString(),
        HttpStatus.SC_CREATED,
        adminUser.getUsername(),
        adminUserApiKey);

    // Disable feature flag
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        group, null, FeatureFlag.DISAGGREGATED_STORAGE_ATLAS);

    // Try to update cluster with feature flag disabled
    final JSONObject updateResponse =
        doDigestJsonPatchWithApiPreviewVersion(
            BASE_URL + groupId + "/clusters/" + clusterName,
            createUpdateClusterView("M20", 16.0, 4000, VolumeTypeView.gp3, true).toString(),
            HttpStatus.SC_BAD_REQUEST,
            adminUser.getUsername(),
            adminUserApiKey);

    assertEquals(
        ApiErrorCode.DISAGGREGATED_STORAGE_FEATURE_FLAG_MISSING.name(),
        updateResponse.getString("errorCode"));
  }
}
