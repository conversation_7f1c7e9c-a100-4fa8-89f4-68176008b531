package com.xgen.cloud.nds.dataexfiltrationprevention._public.svc;

import static com.xgen.cloud.nds.dataexfiltrationprevention._private.settings.DataExfiltrationPreventionAppSettings.DEFAULT_PREFIX_LIST_MAX_ENTRIES;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.dataexfiltrationprevention._private.dao.AWSDataExfiltrationPrefixListDao;
import com.xgen.cloud.nds.dataexfiltrationprevention._private.model.AWSDataExfiltrationPrefixList;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.services.ec2.model.PrefixListState;

public class AWSDataExfiltrationPrefixListSvcIntTests extends JUnit5BaseSvcTest {

  private AWSDataExfiltrationPrefixListSvc awsDataExfiltrationPrefixListSvc;
  @Inject private AWSDataExfiltrationPrefixListDao awsDataExfiltrationPrefixListDao;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    appSettings = mock(AppSettings.class);
    awsDataExfiltrationPrefixListDao.ensureIndexes();
    awsDataExfiltrationPrefixListSvc =
        new AWSDataExfiltrationPrefixListSvc(awsDataExfiltrationPrefixListDao);
  }

  @Test
  public void testFindAWSPrefixListIdByAWSAccountIdAndRegion_EmptyResult() {
    // Given
    final ObjectId nonExistentAccountId = new ObjectId();
    final AWSRegionName region = AWSRegionName.US_EAST_1;

    // When
    final Optional<String> result =
        awsDataExfiltrationPrefixListSvc.findAWSPrefixListIdByAWSAccountIdAndRegion(
            nonExistentAccountId, region);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  public void testFindAWSPrefixListIdByAWSAccountIdAndRegion_Found() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName region = AWSRegionName.US_WEST_2;
    final String expectedPrefixListId = "pl-test12345";

    final AWSDataExfiltrationPrefixList prefixList =
        createTestPrefixListWithCustomId(awsAccountId, region, expectedPrefixListId);

    // Insert test data
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList);

    // When
    final Optional<String> result =
        awsDataExfiltrationPrefixListSvc.findAWSPrefixListIdByAWSAccountIdAndRegion(
            awsAccountId, region);

    // Then
    assertTrue(result.isPresent());
    assertEquals(expectedPrefixListId, result.get());
  }

  @Test
  public void testFindAWSPrefixListIdByAWSAccountIdAndRegion_WrongAccount() {
    // Given
    final ObjectId correctAccountId = new ObjectId();
    final ObjectId wrongAccountId = new ObjectId();
    final AWSRegionName region = AWSRegionName.EU_WEST_1;
    final String prefixListId = "pl-correct123";

    final AWSDataExfiltrationPrefixList prefixList =
        createTestPrefixListWithCustomId(correctAccountId, region, prefixListId);

    // Insert test data with correct account
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList);

    // When - search with wrong account ID
    final Optional<String> result =
        awsDataExfiltrationPrefixListSvc.findAWSPrefixListIdByAWSAccountIdAndRegion(
            wrongAccountId, region);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  public void testFindAWSPrefixListIdByAWSAccountIdAndRegion_WrongRegion() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName correctRegion = AWSRegionName.AP_SOUTHEAST_1;
    final AWSRegionName wrongRegion = AWSRegionName.AP_NORTHEAST_1;
    final String prefixListId = "pl-region123";

    final AWSDataExfiltrationPrefixList prefixList =
        createTestPrefixListWithCustomId(awsAccountId, correctRegion, prefixListId);

    // Insert test data with correct region
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList);

    // When - search with wrong region
    final Optional<String> result =
        awsDataExfiltrationPrefixListSvc.findAWSPrefixListIdByAWSAccountIdAndRegion(
            awsAccountId, wrongRegion);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  public void testFindAWSPrefixListIdByAWSAccountIdAndRegion_MultipleRegionsSameAccount() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName region1 = AWSRegionName.US_EAST_1;
    final AWSRegionName region2 = AWSRegionName.US_WEST_2;
    final String prefixListId1 = "pl-east123";
    final String prefixListId2 = "pl-west456";

    final AWSDataExfiltrationPrefixList prefixList1 =
        createTestPrefixListWithCustomId(awsAccountId, region1, prefixListId1);
    final AWSDataExfiltrationPrefixList prefixList2 =
        createTestPrefixListWithCustomId(awsAccountId, region2, prefixListId2);

    // Insert test data
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList1);
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList2);

    // When - search for region1
    final Optional<String> result1 =
        awsDataExfiltrationPrefixListSvc.findAWSPrefixListIdByAWSAccountIdAndRegion(
            awsAccountId, region1);

    // When - search for region2
    final Optional<String> result2 =
        awsDataExfiltrationPrefixListSvc.findAWSPrefixListIdByAWSAccountIdAndRegion(
            awsAccountId, region2);

    // Then
    assertTrue(result1.isPresent());
    assertEquals(prefixListId1, result1.get());

    assertTrue(result2.isPresent());
    assertEquals(prefixListId2, result2.get());
  }

  private AWSDataExfiltrationPrefixList createTestPrefixListWithCustomId(
      ObjectId awsAccountId, AWSRegionName region, String prefixListId) {
    return new AWSDataExfiltrationPrefixList(
        new ObjectId(),
        awsAccountId,
        region,
        prefixListId,
        1L,
        AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME,
        AWSDataExfiltrationPrefixList.Constants.ADDRESS_FAMILY,
        "arn:aws:ec2:" + region.getValue() + ":************:prefix-list/" + prefixListId,
        DEFAULT_PREFIX_LIST_MAX_ENTRIES,
        PrefixListState.CREATE_COMPLETE,
        Collections.emptySet(),
        new Date(),
        List.of());
  }
}
