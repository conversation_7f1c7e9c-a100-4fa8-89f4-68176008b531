package com.xgen.cloud.nds.dataexfiltrationprevention._private.dao;

import static com.xgen.cloud.nds.dataexfiltrationprevention._private.settings.DataExfiltrationPreventionAppSettings.DEFAULT_PREFIX_LIST_MAX_ENTRIES;
import static com.xgen.cloud.nds.dataexfiltrationprevention.testutils.AWSDataExfiltrationPrefixListIntTestUtils.assertAWSDataExfiltrationPrefixListEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.MongoWriteException;
import com.mongodb.client.result.InsertOneResult;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.dataexfiltrationprevention._private.model.AWSDataExfiltrationPrefixList;
import com.xgen.cloud.nds.dataexfiltrationprevention._private.model.PrefixListEntry;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.services.ec2.model.ManagedPrefixList;
import software.amazon.awssdk.services.ec2.model.PrefixListState;

public class AWSDataExfiltrationPrefixListDaoIntTests extends JUnit5BaseSvcTest {

  @Inject private AWSDataExfiltrationPrefixListDao awsDataExfiltrationPrefixListDao;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    awsDataExfiltrationPrefixListDao.ensureIndexes();
  }

  @Test
  public void testEnsureUniqueIndexOnAWSAccountIdAndRegion() {
    final ObjectId awsAccountId = new ObjectId();
    final AWSDataExfiltrationPrefixList prefixList1 =
        createTestPrefixList(awsAccountId, AWSRegionName.US_EAST_1, "prefixList1");
    final AWSDataExfiltrationPrefixList prefixList2 =
        createTestPrefixList(awsAccountId, AWSRegionName.US_EAST_1, "prefixList2");

    assertEquals(0, awsDataExfiltrationPrefixListDao.getDocumentCount());

    final InsertOneResult insertResult1 =
        awsDataExfiltrationPrefixListDao.insertMajority(prefixList1);
    assertTrue(insertResult1.wasAcknowledged());
    assertNotNull(insertResult1.getInsertedId());
    assertEquals(1, awsDataExfiltrationPrefixListDao.getDocumentCount());
    assertEquals(List.of(prefixList1), awsDataExfiltrationPrefixListDao.find());

    try {
      awsDataExfiltrationPrefixListDao.insertMajority(prefixList2);
      fail("Expected duplicate key error");
    } catch (final MongoWriteException e) {
      assertTrue(e.getMessage().contains("duplicate key error"));
      assertEquals(11000, e.getCode());
      assertEquals(1, awsDataExfiltrationPrefixListDao.getDocumentCount());
      assertEquals(List.of(prefixList1), awsDataExfiltrationPrefixListDao.find());
    }
  }

  @Test
  public void testFindByAWSAccountIdAndRegion() {
    final ObjectId awsAccountId1 = new ObjectId();
    final ObjectId awsAccountId2 = new ObjectId();
    final AWSDataExfiltrationPrefixList prefixListAccount1Region1 =
        createTestPrefixList(awsAccountId1, AWSRegionName.US_EAST_1, "prefixList1");
    final AWSDataExfiltrationPrefixList prefixListAccount1Region2 =
        createTestPrefixList(awsAccountId1, AWSRegionName.US_EAST_2, "prefixList2");
    final AWSDataExfiltrationPrefixList prefixListAccount2Region1 =
        createTestPrefixList(awsAccountId2, AWSRegionName.US_EAST_1, "prefixList3");

    assertTrue(
        awsDataExfiltrationPrefixListDao
            .insertMajority(prefixListAccount1Region1)
            .wasAcknowledged());
    assertTrue(
        awsDataExfiltrationPrefixListDao
            .insertMajority(prefixListAccount1Region2)
            .wasAcknowledged());
    assertTrue(
        awsDataExfiltrationPrefixListDao
            .insertMajority(prefixListAccount2Region1)
            .wasAcknowledged());

    final Optional<AWSDataExfiltrationPrefixList> account1Region1PrefixList =
        awsDataExfiltrationPrefixListDao.findByAWSAccountIdAndRegion(
            awsAccountId1, AWSRegionName.US_EAST_1);
    assertEquals(prefixListAccount1Region1, account1Region1PrefixList.orElseThrow());

    final Optional<AWSDataExfiltrationPrefixList> account1Region2PrefixList =
        awsDataExfiltrationPrefixListDao.findByAWSAccountIdAndRegion(
            awsAccountId1, AWSRegionName.US_EAST_2);
    assertEquals(prefixListAccount1Region2, account1Region2PrefixList.orElseThrow());

    final Optional<AWSDataExfiltrationPrefixList> account2Region1PrefixList =
        awsDataExfiltrationPrefixListDao.findByAWSAccountIdAndRegion(
            awsAccountId2, AWSRegionName.US_EAST_1);
    assertEquals(prefixListAccount2Region1, account2Region1PrefixList.orElseThrow());

    final Optional<AWSDataExfiltrationPrefixList> account2Region2PrefixList =
        awsDataExfiltrationPrefixListDao.findByAWSAccountIdAndRegion(
            awsAccountId2, AWSRegionName.US_EAST_2);
    assertTrue(account2Region2PrefixList.isEmpty());
  }

  @Test
  public void updatePrefixListAndEntriesFromAWS_successWithEntries_updatesMetadataAndEntries() {
    // Given - Insert an existing prefix list
    final ObjectId awsAccountId = new ObjectId();
    final AWSDataExfiltrationPrefixList existingPrefixList =
        createTestPrefixList(
            awsAccountId,
            AWSRegionName.US_EAST_1,
            AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME);
    awsDataExfiltrationPrefixListDao.insertMajority(existingPrefixList);

    // Create updated AWS prefix list with new metadata
    final ManagedPrefixList updatedAwsPrefixList =
        createTestManagedPrefixList(AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME)
            .toBuilder()
            .version(2L)
            .state(PrefixListState.MODIFY_COMPLETE)
            .prefixListName("updated-prefix-list-name")
            .maxEntries(150)
            .build();

    // Create new entries
    final Set<PrefixListEntry> newEntries =
        Set.of(
            new PrefixListEntry("10.0.0.0/16", "Test CIDR 1"),
            new PrefixListEntry("192.168.1.0/24", "Test CIDR 2"));

    // When
    final Optional<AWSDataExfiltrationPrefixList> updateResult =
        awsDataExfiltrationPrefixListDao.updatePrefixListAndEntriesFromAWS(
            existingPrefixList.id(), updatedAwsPrefixList, newEntries);

    // Then
    assertTrue(updateResult.isPresent());
    final AWSDataExfiltrationPrefixList expectedPrefixList =
        new AWSDataExfiltrationPrefixList(
            existingPrefixList.id(),
            existingPrefixList.awsAccountId(),
            existingPrefixList.region(),
            existingPrefixList.awsPrefixListId(),
            2L,
            "updated-prefix-list-name",
            existingPrefixList.addressFamily(),
            existingPrefixList.arn(),
            150,
            PrefixListState.MODIFY_COMPLETE,
            newEntries,
            new Date(),
            existingPrefixList.tags());
    assertAWSDataExfiltrationPrefixListEquals(expectedPrefixList, updateResult.get());

    // Verify the data was updated correctly in the database
    final AWSDataExfiltrationPrefixList prefixListInDB =
        awsDataExfiltrationPrefixListDao
            .findByAWSAccountIdAndRegion(awsAccountId, existingPrefixList.region())
            .orElseThrow();
    assertAWSDataExfiltrationPrefixListEquals(expectedPrefixList, prefixListInDB);
    assertEquals(newEntries, prefixListInDB.entries());
    assertEquals(
        AWSDataExfiltrationPrefixList.computeHash(newEntries, updatedAwsPrefixList.maxEntries()),
        prefixListInDB.hash());
  }

  @Test
  public void
      updatePrefixListAndEntriesFromAWS_successWithEmptyEntries_updatesMetadataAndClearsEntries() {
    // Given - Insert an existing prefix list with some entries
    final ObjectId awsAccountId = new ObjectId();
    final Set<PrefixListEntry> existingEntries =
        Set.of(new PrefixListEntry("172.16.0.0/12", "Existing CIDR"));
    final AWSDataExfiltrationPrefixList existingPrefixList =
        createTestPrefixList(
            awsAccountId,
            AWSRegionName.US_EAST_1,
            AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME,
            existingEntries);
    awsDataExfiltrationPrefixListDao.insertMajority(existingPrefixList);

    // Create updated AWS prefix list
    final ManagedPrefixList updatedAwsPrefixList =
        createTestManagedPrefixList(AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME)
            .toBuilder()
            .version(2L)
            .state(PrefixListState.MODIFY_COMPLETE)
            .build();

    // When - Update with empty entries list
    final Optional<AWSDataExfiltrationPrefixList> updateResult =
        awsDataExfiltrationPrefixListDao.updatePrefixListAndEntriesFromAWS(
            existingPrefixList.id(), updatedAwsPrefixList, Set.of());

    // Then
    assertTrue(updateResult.isPresent());
    final AWSDataExfiltrationPrefixList updatedPrefixList = updateResult.get();
    assertEquals(2L, updatedPrefixList.version());
    assertEquals(PrefixListState.MODIFY_COMPLETE, updatedPrefixList.state());
    assertEquals(Set.of(), updatedPrefixList.entries());
    assertEquals(
        AWSDataExfiltrationPrefixList.computeHash(Set.of(), updatedAwsPrefixList.maxEntries()),
        updatedPrefixList.hash());

    // Verify in database
    final Optional<AWSDataExfiltrationPrefixList> fromDb =
        awsDataExfiltrationPrefixListDao.findByAWSAccountIdAndRegion(
            awsAccountId, AWSRegionName.US_EAST_1);
    assertTrue(fromDb.isPresent());
    assertEquals(Set.of(), fromDb.get().entries());
    assertEquals(
        AWSDataExfiltrationPrefixList.computeHash(Set.of(), updatedAwsPrefixList.maxEntries()),
        fromDb.get().hash());
  }

  @Test
  public void updatePrefixListAndEntriesFromAWS_versionNotGreater_noUpdate() {
    // Given - Insert an existing prefix list with version 2
    final ObjectId awsAccountId = new ObjectId();
    final Set<PrefixListEntry> existingEntries =
        Set.of(new PrefixListEntry("172.16.0.0/12", "Existing CIDR"));
    final AWSDataExfiltrationPrefixList existingPrefixList =
        createTestPrefixList(
            awsAccountId,
            AWSRegionName.US_EAST_1,
            AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME,
            existingEntries);
    // Manually set version to 2
    final AWSDataExfiltrationPrefixList existingPrefixListV2 =
        new AWSDataExfiltrationPrefixList(
            existingPrefixList.id(),
            existingPrefixList.awsAccountId(),
            existingPrefixList.region(),
            existingPrefixList.awsPrefixListId(),
            2L, // version 2
            existingPrefixList.name(),
            existingPrefixList.addressFamily(),
            existingPrefixList.arn(),
            existingPrefixList.maxEntries(),
            existingPrefixList.state(),
            existingPrefixList.entries(),
            existingPrefixList.lastModifiedDate(),
            existingPrefixList.tags());
    awsDataExfiltrationPrefixListDao.insertMajority(existingPrefixListV2);

    // Create AWS prefix list with older version (1)
    final ManagedPrefixList olderAwsPrefixList =
        createTestManagedPrefixList(AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME)
            .toBuilder()
            .version(1L)
            .state(PrefixListState.MODIFY_COMPLETE)
            .build();

    final Set<PrefixListEntry> newEntries =
        Set.of(new PrefixListEntry("10.0.0.0/16", "Should not be updated"));

    // When
    final Optional<AWSDataExfiltrationPrefixList> updateResult =
        awsDataExfiltrationPrefixListDao.updatePrefixListAndEntriesFromAWS(
            existingPrefixListV2.id(), olderAwsPrefixList, newEntries);

    // Then - Should not update because version is not greater
    assertTrue(updateResult.isEmpty());

    // Verify the data was not updated
    final AWSDataExfiltrationPrefixList prefixListInDB =
        awsDataExfiltrationPrefixListDao
            .findByAWSAccountIdAndRegion(awsAccountId, existingPrefixList.region())
            .orElseThrow();
    assertEquals(2L, prefixListInDB.version());
    assertEquals(PrefixListState.CREATE_COMPLETE, prefixListInDB.state());
    assertEquals(existingEntries, prefixListInDB.entries());
    assertEquals(existingPrefixList.hash(), prefixListInDB.hash());
  }

  @Test
  public void updatePrefixListAndEntriesFromAWS_nonExistentPrefixList_noUpdate() {
    // Given
    final ObjectId nonExistentId = new ObjectId();
    final ManagedPrefixList awsPrefixList =
        createTestManagedPrefixList(AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME);
    final Set<PrefixListEntry> entries = Set.of(new PrefixListEntry("10.0.0.0/16", "Test CIDR"));

    // When
    final Optional<AWSDataExfiltrationPrefixList> updateResult =
        awsDataExfiltrationPrefixListDao.updatePrefixListAndEntriesFromAWS(
            nonExistentId, awsPrefixList, entries);

    // Then
    assertTrue(updateResult.isEmpty());
    assertEquals(0, awsDataExfiltrationPrefixListDao.getDocumentCount());
  }

  @Test
  public void updatePrefixListAndEntriesFromAWS_sameVersion_updatesSuccessfully() {
    // Given - Insert an existing prefix list with version 1
    final ObjectId awsAccountId = new ObjectId();
    final AWSDataExfiltrationPrefixList existingPrefixList =
        createTestPrefixList(
            awsAccountId,
            AWSRegionName.US_EAST_1,
            AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME);
    awsDataExfiltrationPrefixListDao.insertMajority(existingPrefixList);

    // Create AWS prefix list with same version (1) but different state
    final ManagedPrefixList sameVersionAwsPrefixList =
        createTestManagedPrefixList(AWSDataExfiltrationPrefixList.Constants.PREFIX_LIST_NAME)
            .toBuilder()
            .version(1L) // same version
            .state(PrefixListState.MODIFY_COMPLETE)
            .build();

    final Set<PrefixListEntry> newEntries =
        Set.of(new PrefixListEntry("10.0.0.0/16", "Updated CIDR"));

    // When
    final Optional<AWSDataExfiltrationPrefixList> updateResult =
        awsDataExfiltrationPrefixListDao.updatePrefixListAndEntriesFromAWS(
            existingPrefixList.id(), sameVersionAwsPrefixList, newEntries);

    // Then - Should update because version is equal (LTE condition)
    assertTrue(updateResult.isPresent());
    final AWSDataExfiltrationPrefixList updatedPrefixList = updateResult.get();
    assertEquals(1L, updatedPrefixList.version());
    assertEquals(PrefixListState.MODIFY_COMPLETE, updatedPrefixList.state());
    assertEquals(newEntries, updatedPrefixList.entries());
    assertEquals(
        AWSDataExfiltrationPrefixList.computeHash(
            newEntries, sameVersionAwsPrefixList.maxEntries()),
        updatedPrefixList.hash());
  }

  @Test
  public void getPrefixListHashDistribution_mixedDocuments_returnsCorrectDistribution() {
    // Given - Create prefix lists with different hash values
    final ObjectId awsAccountId1 = new ObjectId();
    final ObjectId awsAccountId2 = new ObjectId();
    final ObjectId awsAccountId3 = new ObjectId();

    final AWSDataExfiltrationPrefixList prefixList1 =
        createTestPrefixList(awsAccountId1, AWSRegionName.US_EAST_1, "prefix-list-1", Set.of());

    final Set<PrefixListEntry> entries2 = Set.of(new PrefixListEntry("10.0.0.0/16", "Test CIDR"));
    final AWSDataExfiltrationPrefixList prefixList2 =
        createTestPrefixList(awsAccountId2, AWSRegionName.US_WEST_2, "prefix-list-2", entries2);

    final Set<PrefixListEntry> entries3 =
        Set.of(new PrefixListEntry("172.16.0.0/12", "Private CIDR"));
    final AWSDataExfiltrationPrefixList prefixList3 =
        createTestPrefixList(awsAccountId3, AWSRegionName.EU_WEST_1, "prefix-list-3", entries3);

    // Insert all prefix lists
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList1);
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList2);
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList3);

    // When
    final Map<Integer, Integer> hashDistribution =
        awsDataExfiltrationPrefixListDao.getPrefixListHashDistribution();

    // Then
    assertEquals(3, hashDistribution.size());
    assertEquals(3, hashDistribution.values().stream().mapToInt(Integer::intValue).sum());

    hashDistribution.values().forEach(count -> assertEquals(1, count.intValue()));

    final Integer hash1 =
        AWSDataExfiltrationPrefixList.computeHash(prefixList1.entries(), prefixList1.maxEntries());
    final Integer hash2 =
        AWSDataExfiltrationPrefixList.computeHash(prefixList2.entries(), prefixList2.maxEntries());
    final Integer hash3 =
        AWSDataExfiltrationPrefixList.computeHash(prefixList3.entries(), prefixList3.maxEntries());

    assertTrue(hashDistribution.containsKey(hash1));
    assertTrue(hashDistribution.containsKey(hash2));
    assertTrue(hashDistribution.containsKey(hash3));
  }

  @Test
  public void getTotalPrefixListCount_mixedDocuments_returnsCorrectCount() {
    // Given - Create multiple prefix lists
    final ObjectId awsAccountId1 = new ObjectId();
    final ObjectId awsAccountId2 = new ObjectId();
    final ObjectId awsAccountId3 = new ObjectId();

    final AWSDataExfiltrationPrefixList prefixList1 =
        createTestPrefixList(awsAccountId1, AWSRegionName.US_EAST_1, "prefix-list-1");
    final AWSDataExfiltrationPrefixList prefixList2 =
        createTestPrefixList(awsAccountId2, AWSRegionName.US_WEST_2, "prefix-list-2");
    final AWSDataExfiltrationPrefixList prefixList3 =
        createTestPrefixList(awsAccountId3, AWSRegionName.EU_WEST_1, "prefix-list-3");

    awsDataExfiltrationPrefixListDao.insertMajority(prefixList1);
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList2);
    awsDataExfiltrationPrefixListDao.insertMajority(prefixList3);

    // When
    final long totalCount = awsDataExfiltrationPrefixListDao.getTotalPrefixListCount();

    // Then
    assertEquals(3L, totalCount);
  }

  private ManagedPrefixList createTestManagedPrefixList(String name) {
    return ManagedPrefixList.builder()
        .prefixListId("pl-0123123123123abcd")
        .prefixListName(name)
        .state(PrefixListState.CREATE_COMPLETE)
        .addressFamily(AWSDataExfiltrationPrefixList.Constants.ADDRESS_FAMILY)
        .maxEntries(DEFAULT_PREFIX_LIST_MAX_ENTRIES)
        .version(1L)
        .prefixListArn("arn:aws:ec2:us-east-1:************:prefix-list/pl-0123123123123abcd")
        .build();
  }

  private AWSDataExfiltrationPrefixList createTestPrefixList(
      ObjectId awsAccountId, AWSRegionName region, String name) {
    return createTestPrefixList(awsAccountId, region, name, Set.of());
  }

  private AWSDataExfiltrationPrefixList createTestPrefixList(
      ObjectId awsAccountId, AWSRegionName region, String name, Set<PrefixListEntry> entries) {
    final ManagedPrefixList managedPrefixList = createTestManagedPrefixList(name);
    return new AWSDataExfiltrationPrefixList(
        new ObjectId(),
        awsAccountId,
        region,
        managedPrefixList.prefixListId(),
        managedPrefixList.version(),
        managedPrefixList.prefixListName(),
        managedPrefixList.addressFamily(),
        managedPrefixList.prefixListArn(),
        managedPrefixList.maxEntries(),
        managedPrefixList.state(),
        entries,
        new Date(),
        List.of());
  }
}
