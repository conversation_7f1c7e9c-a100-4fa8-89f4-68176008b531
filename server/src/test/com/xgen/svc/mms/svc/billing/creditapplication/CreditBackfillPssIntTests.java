package com.xgen.svc.mms.svc.billing.creditapplication;

import com.xgen.cloud.services.payments.modules.credits._private.dao.AppliedCreditDao;
import com.xgen.cloud.services.payments.modules.credits._private.dao.AppliedCreditDao.GroupingField;
import com.xgen.cloud.services.payments.modules.credits._private.svc.CreditApplicationOrchestrator;
import com.xgen.cloud.services.payments.modules.credits._public.model.AppliedCreditAggregate;
import com.xgen.cloud.services.payments.modules.credits._public.model.CreditIdentifier;
import com.xgen.cloud.services.payments.modules.credits._public.model.LineItemFilter;
import com.xgen.svc.mms.model.billing.CreditBackfillSummary;
import jakarta.inject.Inject;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Integration tests for credit backfill using the new Payment Service System (PSS) implementation.
 * This class demonstrates how the new credit application system works and provides backwards
 * compatibility during the migration period.
 */
public class CreditBackfillPssIntTests extends CreditBackfillIntTestBase {
  @Inject private CreditApplicationOrchestrator creditApplicationOrchestrator;
  @Inject private AppliedCreditDao appliedCreditDao;

  @Override
  BackfillApplicator backfillApplicator() {
    return (invoiceIds,
        creditId,
        activationCode,
        paymentId,
        allowCreditsFromOtherOrgs,
        lineItemId,
        excludePaymentIdsFromValidation,
        backfillLinkedInvoices) -> {

      // get credit identifier
      CreditIdentifier creditIdentifier = CreditIdentifier.allForOrg();
      if (creditId != null) {
        creditIdentifier = CreditIdentifier.byCreditId(creditId);
      } else if (activationCode != null) {
        creditIdentifier = CreditIdentifier.byActivationCode(activationCode);
      }

      LineItemFilter lineItemFilter = LineItemFilter.noFilter();
      if (paymentId != null) {
        lineItemFilter = LineItemFilter.byPaymentId(paymentId);
      } else if (lineItemId != null) {
        lineItemFilter = LineItemFilter.onOrAfterLineItemId(lineItemId);
      }

      return creditApplicationOrchestrator
          .applyCreditsForBackfill(
              new HashSet<>(invoiceIds),
              creditIdentifier,
              lineItemFilter,
              allowCreditsFromOtherOrgs,
              backfillLinkedInvoices,
              excludePaymentIdsFromValidation)
          .map(
              summaries ->
                  summaries.stream()
                      .map(
                          invoiceAppliedCreditSummary -> {
                            CreditBackfillSummary summary =
                                new CreditBackfillSummary(invoiceAppliedCreditSummary.invoiceId());
                            summary.addCredits(
                                invoiceAppliedCreditSummary.credits().stream().toList());
                            summary.incrementDrawdownCentsAmount(
                                invoiceAppliedCreditSummary.drawndownAmountCents());
                            return summary;
                          })
                      .collect(Collectors.toList()))
          .block();
    };
  }

  @Override
  AppliedCreditFetcher appliedCreditFetcher() {
    return invoiceId ->
        appliedCreditDao
            .findCreditUsages(
                Set.of(invoiceId),
                Set.of(),
                null,
                Set.of(GroupingField.BILL_DATE, GroupingField.CREDIT_ID),
                null,
                null)
            .map(AppliedCreditAggregate::getAppliedCreditsCents)
            .map(amt -> -amt)
            .collectList()
            .block();
  }
}
