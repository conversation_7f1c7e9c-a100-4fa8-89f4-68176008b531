package com.xgen.svc.mms.svc;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gt;
import static org.junit.Assert.assertEquals;

import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.Policy;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.datalake._private.dao.IngestionPipelineDao;
import com.xgen.cloud.nds.datalake._private.dao.IngestionPipelineRunDao;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataSetDao;
import com.xgen.cloud.nds.datalake._public.model.dls.DLSIngestionSink;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipeline;
import com.xgen.cloud.nds.datalake._public.model.dls.IngestionPipelineRun;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSet;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSetDLSIngestion;
import com.xgen.cloud.nds.datalake._public.model.dls.PeriodicCpsSnapshotSource;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseSvcTest;
import com.xgen.svc.nds.model.NDSDataLakeStorageTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.IngestionPipelineSvc;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.cps.CpsPolicySvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class IngestionPipelineAndDatasetCleanupSvcIntTests extends BaseSvcTest {
  private static final int NUM_PIPELINES = 10;
  private final String _clusterName = IngestionPipelineAndDatasetCleanupSvcIntTests.class.getName();
  private Group _group;
  private List<IngestionPipeline> _pipelines;

  @Inject NDSGroupSvc _ndsGroupSvc;
  @Inject ClusterDescriptionDao _clusterDescriptionDao;
  @Inject CpsPolicySvc _cpsPolicySvc;
  @Inject BackupJobDao _backupJobDao;
  @Inject IngestionPipelineRunDao _ingestionPipelineRunDao;
  @Inject NDSDataSetDao _ndsDataSetDao;
  @Inject IngestionPipelineSvc _ingestionPipelineSvc;
  @Inject IngestionPipelineDao _ingestionPipelineDao;
  @Inject IngestionPipelineAndDatasetCleanupSvc _svc;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _dataLakeTestUtils.setUp();
    _group =
        MmsFactory.createGroup(
            MmsFactory.createOrganizationWithNDSPlan(),
            IngestionPipelineAndDatasetCleanupSvcIntTests.class.getName());
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_DATA_LAKE_STORAGE_DATASET_RETENTION_POLICY);
    // add cluster
    final ClusterDescription clusterDescription =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(_group.getId(), _clusterName))
            .copy()
            .setBackupEnabled(true)
            .setDiskBackupEnabled(true)
            .build();
    _clusterDescriptionDao.save(clusterDescription);
    // add backup job
    final ObjectId backupJobId =
        _cpsPolicySvc.createBackupJobForTesting(
            _group.getId(),
            clusterDescription.getName(),
            clusterDescription.getUniqueId(),
            BackupJob.ClusterType.REPLICA_SET,
            2,
            clusterDescription.isNVMe(NodeType.ELECTABLE),
            false);
    final BackupJob backupJob = _backupJobDao.find(backupJobId).orElseThrow();
    final ObjectId policyItemId =
        backupJob.getPolicies().stream()
            .findFirst()
            .map(Policy::getPolicyItems)
            .map(Collection::stream)
            .flatMap(Stream::findFirst)
            .map(PolicyItem::getId)
            .orElseThrow();
    // add some ingestion pipelines
    _pipelines =
        IntStream.range(0, NUM_PIPELINES)
            .mapToObj(
                i ->
                    NDSDataLakeStorageTestFactory.getIngestionPipeline(
                            _group.getId(), "pipeline" + i)
                        .toBuilder()
                        .source(
                            new PeriodicCpsSnapshotSource(
                                _group.getId(),
                                _clusterName,
                                "db" + i,
                                "collection" + i,
                                policyItemId))
                        .build())
            .map(
                p -> {
                  try {
                    return _ingestionPipelineSvc.createIngestionPipeline(
                        p, AuditInfoHelpers.fromSystem());
                  } catch (final SvcException pE) {
                    pE.printStackTrace();
                    return null;
                  }
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    assertEquals(NUM_PIPELINES, _pipelines.size());

    // add a pipeline run and data set
    _pipelines.forEach(
        p -> {
          // mix of cloud providers
          final CloudProvider provider =
              p.getName().charAt(p.getName().length() - 1) % 2 == 0
                  ? CloudProvider.AWS
                  : CloudProvider.AZURE;
          final String region =
              provider == CloudProvider.AWS
                  ? AWSRegionName.US_EAST_1.name()
                  : AzureRegionName.EUROPE_NORTH.name();
          final IngestionPipelineRun run =
              NDSDataLakeStorageTestFactory.getIngestionPipelineRun(p).toBuilder()
                  .metadataProvider(provider)
                  .metadataRegion(region)
                  .build();
          _ingestionPipelineRunDao.saveIngestionPipelineRun(run);
          _ndsDataSetDao.createDataSetForSinkAndPipelineRun((DLSIngestionSink) p.getSink(), run);
        });
  }

  @After
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testDisableDataSetsForDeletedPipeline() {
    // no deleted pipelines
    {
      _svc.disableDataSetsForDeletedPipelines();
      // all data sets still active
      assertEquals(
          NUM_PIPELINES, _ndsDataSetDao.findCreatedOrActiveForGroup(_group.getId()).size());
    }

    // 2 deleted pipelines
    {
      _pipelines.stream()
          .limit(2)
          .forEach(
              pipeline -> {
                try {
                  _ingestionPipelineSvc.markIngestionPipelineAsDisabled(
                      _group.getId(), pipeline.getName(), AuditInfoHelpers.fromSystem());
                } catch (final SvcException pE) {
                  pE.printStackTrace();
                }
              });
      final Date now = new Date();
      _svc.disableDataSetsForDeletedPipelines();
      // 2 data sets no longer active
      assertEquals(
          NUM_PIPELINES - 2, _ndsDataSetDao.findCreatedOrActiveForGroup(_group.getId()).size());
      // 2 pipelines marked with new deleted date
      assertEquals(
          2,
          _ingestionPipelineDao.count(
              and(
                  eq(IngestionPipeline.FieldDefs.GROUP_ID, _group.getId()),
                  eq(IngestionPipeline.FieldDefs.STATE, IngestionPipeline.State.DELETING),
                  gt(IngestionPipeline.FieldDefs.DELETED_DATE, now))));
    }
  }

  @Test
  public void testCleanupExpiredDataSets() { // no expired data sets
    {
      _svc.cleanupExpiredDataSets();
      // all data sets still active
      assertEquals(
          NUM_PIPELINES, _ndsDataSetDao.findCreatedOrActiveForGroup(_group.getId()).size());
    }

    // 2 data sets deleted too recently
    // 3 data sets deleted a week ago
    {
      _ndsDataSetDao.markDataSetsAsDisabled(
          _ndsDataSetDao.findCreatedOrActiveForGroup(_group.getId()).stream()
              .limit(2)
              .map(NDSDataSetDLSIngestion.class::cast)
              .collect(Collectors.toList()));
      _ndsDataSetDao.markDataSetsAsDisabledWithDate(
          _ndsDataSetDao.findCreatedOrActiveForGroup(_group.getId()).stream()
              .limit(3)
              .map(NDSDataSetDLSIngestion.class::cast)
              .collect(Collectors.toList()),
          DateUtils.addDays(new Date(), -7));
      final Date now = new Date();

      // Halt data set destruction. No data sets should be destroyed.
      FeatureFlagIntTestUtil.enableFeatureGlobally(
          FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION);
      _svc.cleanupExpiredDataSets();
      assertEquals(5, _ndsDataSetDao.findExpiredDLSDatasets(Duration.ZERO, NUM_PIPELINES).size());
      assertEquals(
          0,
          _ndsDataSetDao.count(
              and(
                  eq(NDSDataSet.FieldDefs.GROUP_ID, _group.getId()),
                  eq(NDSDataSet.FieldDefs.STATE, NDSDataSet.State.DELETED.name()),
                  gt(NDSDataSet.FieldDefs.DESTROYED_DATE, now))));

      // Resume data set destruction but add the group to the allow list.
      // No data sets should be destroyed.
      FeatureFlagIntTestUtil.setFeatureFlagToControlled(
          FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION);
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group,
          null,
          FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION);
      _svc.cleanupExpiredDataSets();
      assertEquals(5, _ndsDataSetDao.findExpiredDLSDatasets(Duration.ZERO, NUM_PIPELINES).size());
      assertEquals(
          0,
          _ndsDataSetDao.count(
              and(
                  eq(NDSDataSet.FieldDefs.GROUP_ID, _group.getId()),
                  eq(NDSDataSet.FieldDefs.STATE, NDSDataSet.State.DELETED.name()),
                  gt(NDSDataSet.FieldDefs.DESTROYED_DATE, now))));

      // Resume data set destruction for all groups.
      // Old data sets should be destroyed.
      FeatureFlagIntTestUtil.disableFeatureGlobally(
          FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION);
      _svc.cleanupExpiredDataSets();
      // 2 data sets still expired but not destroyed
      assertEquals(2, _ndsDataSetDao.findExpiredDLSDatasets(Duration.ZERO, NUM_PIPELINES).size());
      // 3 data sets in group now destroyed
      assertEquals(
          3,
          _ndsDataSetDao.count(
              and(
                  eq(NDSDataSet.FieldDefs.GROUP_ID, _group.getId()),
                  eq(NDSDataSet.FieldDefs.STATE, NDSDataSet.State.DELETED.name()),
                  gt(NDSDataSet.FieldDefs.DESTROYED_DATE, now))));
    }
  }

  @Test
  public void testCleanupExpiredPipelines() {
    // no expired pipelines
    {
      _svc.cleanupExpiredPipelines();
      assertEquals(NUM_PIPELINES, _ingestionPipelineDao.findActiveByGroupId(_group.getId()).size());
    }

    // 2 pipelines expired recently
    // 2 pipelines expired a week ago
    // 2 pipelines expired a week ago with no active data sets
    {
      _pipelines.stream()
          .limit(6)
          .forEach(
              pipeline -> {
                try {
                  _ingestionPipelineSvc.markIngestionPipelineAsDisabled(
                      _group.getId(), pipeline.getName(), AuditInfoHelpers.fromSystem());
                } catch (final SvcException pE) {
                  pE.printStackTrace();
                }
              });
      _pipelines.stream()
          .limit(4)
          .forEach(
              pipeline ->
                  _ingestionPipelineDao.updateIngestionPipelineDeletedDate(
                      pipeline.getId(), DateUtils.addDays(new Date(), -7)));
      _pipelines.stream()
          .limit(2)
          .map(IngestionPipeline::getId)
          .map(
              pipelineId ->
                  _ndsDataSetDao.findCreatedOrActiveDLSDataSetsForPipeline(
                      pipelineId,
                      IngestionPipelineAndDatasetCleanupSvc.DATA_SETS_DLS_OPERATION_BATCH_SIZE))
          .forEach(_ndsDataSetDao::markDataSetsAsDestroyed);
      final Date now = new Date();

      // Halt data set destruction. No pipelines should be cleaned up.
      FeatureFlagIntTestUtil.enableFeatureGlobally(
          FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION);
      _svc.cleanupExpiredPipelines();
      assertEquals(
          6,
          _ingestionPipelineDao.count(
              and(
                  eq(IngestionPipeline.FieldDefs.GROUP_ID, _group.getId()),
                  eq(IngestionPipeline.FieldDefs.STATE, IngestionPipeline.State.DELETING))));

      // Resume data set destruction but add the group to the allow list.
      // No pipelines should be cleaned up.
      FeatureFlagIntTestUtil.setFeatureFlagToControlled(
          FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION);
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group,
          null,
          FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION);
      _svc.cleanupExpiredPipelines();
      assertEquals(
          6,
          _ingestionPipelineDao.count(
              and(
                  eq(IngestionPipeline.FieldDefs.GROUP_ID, _group.getId()),
                  eq(IngestionPipeline.FieldDefs.STATE, IngestionPipeline.State.DELETING))));

      // Resume data set destruction for all groups.
      // Expired pipelines should be cleaned up.
      FeatureFlagIntTestUtil.disableFeatureGlobally(
          FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION);
      _svc.cleanupExpiredPipelines();
      // 4 pipelines still deleting
      assertEquals(
          4,
          _ingestionPipelineDao.count(
              and(
                  eq(IngestionPipeline.FieldDefs.GROUP_ID, _group.getId()),
                  eq(IngestionPipeline.FieldDefs.STATE, IngestionPipeline.State.DELETING))));
      // 2 pipelines fully deleted
      assertEquals(
          2,
          _ingestionPipelineDao.count(
              and(
                  eq(IngestionPipeline.FieldDefs.GROUP_ID, _group.getId()),
                  eq(IngestionPipeline.FieldDefs.STATE, IngestionPipeline.State.DELETED),
                  gt(IngestionPipeline.FieldDefs.DESTROYED_DATE, now))));
    }
  }

  @Test
  public void testDeleteActiveOrPausedPipelines() {
    // Feature flag disabled. No pipelines should be marked for deletion.
    FeatureFlagIntTestUtil.disableFeatureGlobally(
        FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_DISABLE_PIPELINES);
    assertEquals(NUM_PIPELINES, _ingestionPipelineDao.findActiveOrPaused(NUM_PIPELINES).size());

    // Feature flag disabled only for group. No pipelines should be marked for deletion.
    FeatureFlagIntTestUtil.setFeatureFlagToControlled(
        FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_DISABLE_PIPELINES);
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_DISABLE_PIPELINES);
    assertEquals(NUM_PIPELINES, _ingestionPipelineDao.findActiveOrPaused(NUM_PIPELINES).size());

    // Feature flag enabled. All pipelines should be marked for deletion.
    FeatureFlagIntTestUtil.enableFeatureGlobally(
        FeatureFlag.ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_DISABLE_PIPELINES);
    _svc.deleteActiveOrPausedPipelines();
    assertEquals(0, _ingestionPipelineDao.findActiveOrPaused(NUM_PIPELINES).size());
  }
}
