package com.xgen.svc.mms.svc.billing.audit.auditors;

import static com.xgen.cloud.organization._public.model.OrgPaymentStatus.ok;
import static com.xgen.svc.mms.svc.billing.audit.auditors.AuditorConstants.BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES;
import static com.xgen.svc.mms.svc.billing.audit.auditors.MeterUsageBillDateConsistencyAuditor.MAX_GROUP_PARTITION_SIZE;
import static com.xgen.svc.mms.svc.billing.audit.auditors.MeterUsageBillDateConsistencyAuditor.METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_DISABLE_SKUS;
import static com.xgen.svc.mms.svc.billing.audit.auditors.MeterUsageBillDateConsistencyAuditor.METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_END_AUDIT_DATE;
import static com.xgen.svc.mms.svc.billing.audit.auditors.MeterUsageBillDateConsistencyAuditor.METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_START_AUDIT_DATE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.xgen.cloud.billingplatform.audit._private.dao.AuditorConfigDao;
import com.xgen.cloud.billingplatform.audit._public.model.AuditFailureDetail;
import com.xgen.cloud.billingplatform.audit._public.model.BillingAuditError;
import com.xgen.cloud.billingplatform.common._public.model.BillingProperty;
import com.xgen.cloud.billingplatform.common._public.svc.BillingPropertySvc;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.util.MathUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.module.metering.common.model.MeterId;
import com.xgen.module.metering.common.model.MeterUsage;
import com.xgen.module.metering.common.model.usagedimensions.MMSBackupStorageUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.MMSBackupStorageUsageDimensions.ReplicaSetInfo;
import com.xgen.module.metering.common.model.usagedimensions.MMSBackupStorageUsageDimensions.SnapshotInfo;
import com.xgen.module.metering.common.model.usagedimensions.NoDimensions;
import com.xgen.module.metering.server.utils.testFactories.MeterUsageTestFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.mms.dao.billing.MeterIdActivationRulesDao;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.MeterIdActivationRules.ActivationRule;
import com.xgen.svc.mms.util.billing.testFactories.OrganizationFactory;
import jakarta.inject.Inject;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;

public class MeterUsageBillDateConsistencyAuditorIntTests extends JUnit5BaseSvcTest {
  private final Date AUG1 = TimeUtils.fromISOString("2022-08-01");
  private final Date AUG31 = TimeUtils.fromISOString("2022-08-31");
  private final Date SEPT1 = TimeUtils.fromISOString("2022-09-01");
  private final Date SEPT2 = TimeUtils.fromISOString("2022-09-02");
  private final Date SEPT5 = TimeUtils.fromISOString("2022-09-05");

  private final Date SEPT30 = TimeUtils.fromISOString("2022-09-30");

  private static final double TEST_SAMPLE_RATE = 0.5;
  private static final int TEST_SAMPLE_NUMBER = 2;

  @Inject private MeterUsageBillDateConsistencyAuditor auditor;

  @Inject private OrganizationFactory orgFactory;

  @Inject private AuditorConfigDao auditorConfigDao;

  @Inject private MeterIdActivationRulesDao activationRulesDao;

  @Inject private BillingPropertySvc billingPropertySvc;

  private Organization org;
  private Group group;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    // create an org and 2 groups
    org = orgFactory.createCloudOrganization(AUG1, ok(AUG1), false);
    group = orgFactory.createNdsGroup(org, AUG1);

    auditorConfigDao.updateSampleRate(auditor.getAuditorName(), -1);
  }

  private Organization getDataToAudit() {
    List<ObjectId> ids = auditor.getIdsToAudit();
    List<Organization> orgs = auditor.getDataToAudit(ids);
    assertEquals(1, orgs.size());
    return orgs.get(0);
  }

  @Test
  public void getDataToAudit_returns_correct_orgId() {
    Organization dataToAudit = getDataToAudit();
    assertEquals(this.org.getId(), dataToAudit.getId(), "Should retrieve active orgs");
  }

  @Test
  public void getAuditDates() {
    Date sept2 = TimeUtils.fromISOString("2022-09-01");
    Pair<Date, Date> result = auditor.getAuditDates(sept2);
    assertEquals(AUG31, result.getLeft(), "Audit should start from Aug 31");
    assertEquals(SEPT1, result.getRight(), "Audit should end on Sept 1");
  }

  @Test
  public void getAuditDates_multipleDays() {
    save(
        new BillingProperty(
            METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_START_AUDIT_DATE, "2022-08-01"));

    save(
        new BillingProperty(
            METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_END_AUDIT_DATE, "2022-09-01"));

    Date sept2 = TimeUtils.fromISOString("2022-09-02");
    Pair<Date, Date> result = auditor.getAuditDates(sept2);
    assertEquals(AUG1, result.getLeft(), "Audit should start from Aug 1");
    assertEquals(SEPT1, result.getRight(), "Audit should end on Sept 1");
  }

  @Test
  public void audit_fail_noMeterUsagesFound() {
    Date lineItemStartDate = new Date(SEPT1.getTime());

    while (!lineItemStartDate.after(SEPT30)) {
      LineItem lineItem =
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER)
              .invoiceId(new ObjectId())
              .startDate(lineItemStartDate)
              .endDate(DateUtils.addDays(lineItemStartDate, 1))
              .groupId(group.getId())
              .build();

      save(lineItem);
      lineItemStartDate = DateUtils.addDays(lineItemStartDate, 1);
    }

    double counterStart = BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get();
    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT30);

    assertTrue(result.isPresent());
    assertEquals(1, result.get().getBillingAuditErrors().size());
    assertEquals(
        String.format(
            "NDS_AWS_SERVERLESS_DATA_TRANSFER total quantity for 1 group(s) in org %s doesn't match"
                + " between meter usages (0.0000) and line items (10.0000), bill date 2022-09-29."
                + " groupIds=%s",
            org.getId(), List.of(group.getId())),
        result.get().getBillingAuditErrors().get(0).getErrorMessage());
    assertEquals(
        counterStart + 1,
        BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get(),
        MathUtils.EPSILON);
  }

  @Test
  public void audit_fail_noLineItemsFound() {
    Date billDateStart = DateUtils.addDays(SEPT1, -2);

    while (!billDateStart.after(SEPT30)) {
      Date nextDay = DateUtils.addDays(billDateStart, 1);

      MeterUsage meterUsage =
          MeterUsageTestFactory.createRandomAwsMeterUsage(
              MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
              group.getId(),
              AWSRegionName.EU_CENTRAL_1,
              null,
              billDateStart,
              nextDay);

      save(new MeterUsage.Builder(meterUsage).quantity(10).build());
      billDateStart = nextDay;
    }

    double counterStart = BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get();
    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT1);

    assertTrue(result.isPresent());
    assertEquals(1, result.get().getBillingAuditErrors().size());
    assertEquals(
        String.format(
            "NDS_AWS_SERVERLESS_DATA_TRANSFER total quantity for 1 group(s) in org %s doesn't match"
                + " between meter usages (10.0000) and line items (0.0000), bill date 2022-08-31."
                + " groupIds=%s",
            org.getId(), List.of(group.getId())),
        result.get().getBillingAuditErrors().get(0).getErrorMessage());
    assertEquals(
        counterStart + 1,
        BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get(),
        MathUtils.EPSILON);
  }

  @Test
  public void audit_pass() {
    Group group2 = orgFactory.createNdsGroup(org, AUG1);

    Date billDateStart = DateUtils.addDays(SEPT1, -2);

    while (!billDateStart.after(SEPT30)) {
      Date nextDay = DateUtils.addDays(billDateStart, 1);

      MeterUsage meterUsage =
          MeterUsageTestFactory.createRandomAwsMeterUsage(
              MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
              group.getId(),
              AWSRegionName.EU_CENTRAL_1,
              null,
              billDateStart,
              nextDay);

      MeterUsage meterUsage2 =
          MeterUsageTestFactory.createRandomAwsMeterUsage(
              MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
              group2.getId(),
              AWSRegionName.EU_CENTRAL_1,
              null,
              billDateStart,
              nextDay);

      LineItem lineItem =
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group2.getId())
              .build();

      save(lineItem);
      save(new MeterUsage.Builder(meterUsage).quantity(5).build());
      save(new MeterUsage.Builder(meterUsage2).quantity(5).build());

      billDateStart = nextDay;
    }

    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT30);

    assertFalse(result.isPresent());
  }

  @Test
  public void audit_failure_multiplePartitions() {
    int numberOfPartitions = 3;
    Organization org2 = orgFactory.createCloudOrganization(AUG1, ok(AUG1), false);
    IntStream.range(0, numberOfPartitions * MAX_GROUP_PARTITION_SIZE)
        .forEach(
            i -> {
              Group group2 = orgFactory.createNdsGroup(org2, AUG1);

              Date billDateStart = DateUtils.addDays(SEPT1, -2);

              while (!billDateStart.after(SEPT30)) {
                Date nextDay = DateUtils.addDays(billDateStart, 1);

                MeterUsage meterUsage2 =
                    MeterUsageTestFactory.createRandomAwsMeterUsage(
                        MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
                        group2.getId(),
                        AWSRegionName.EU_CENTRAL_1,
                        null,
                        billDateStart,
                        nextDay);

                LineItem lineItem =
                    new LineItem.Builder()
                        .quantity(10)
                        .sku(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER)
                        .invoiceId(new ObjectId())
                        .startDate(billDateStart)
                        .endDate(nextDay)
                        .groupId(group2.getId())
                        .build();

                save(lineItem);
                save(new MeterUsage.Builder(meterUsage2).quantity(5).build());

                billDateStart = nextDay;
              }
            });

    double counterStart = BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get();
    Optional<AuditFailureDetail> result = auditor.doAudit(org2, SEPT30);

    assertTrue(result.isPresent());
    assertEquals(numberOfPartitions, result.get().getBillingAuditErrors().size());
    assertEquals(
        counterStart + 1,
        BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get(),
        MathUtils.EPSILON);
  }

  @Test
  public void audit_pass_multiplePartitions() {
    int numberOfPartitions = 3;
    Organization org2 = orgFactory.createCloudOrganization(AUG1, ok(AUG1), false);
    IntStream.range(0, numberOfPartitions * MAX_GROUP_PARTITION_SIZE)
        .forEach(
            i -> {
              Group group2 = orgFactory.createNdsGroup(org2, AUG1);

              Date billDateStart = DateUtils.addDays(SEPT1, -2);

              while (!billDateStart.after(SEPT30)) {
                Date nextDay = DateUtils.addDays(billDateStart, 1);

                MeterUsage meterUsage2 =
                    MeterUsageTestFactory.createRandomAwsMeterUsage(
                        MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
                        group2.getId(),
                        AWSRegionName.EU_CENTRAL_1,
                        null,
                        billDateStart,
                        nextDay);

                LineItem lineItem =
                    new LineItem.Builder()
                        .quantity(10)
                        .sku(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER)
                        .invoiceId(new ObjectId())
                        .startDate(billDateStart)
                        .endDate(nextDay)
                        .groupId(group2.getId())
                        .build();

                save(lineItem);
                save(new MeterUsage.Builder(meterUsage2).quantity(10).build());

                billDateStart = nextDay;
              }
            });

    Optional<AuditFailureDetail> result = auditor.doAudit(org2, SEPT30);

    assertFalse(result.isPresent());
  }

  @Test
  public void audit_fail_discrepancyFound() {
    Group group2 = orgFactory.createNdsGroup(org, AUG1);

    Date billDateStart = DateUtils.addDays(SEPT1, -2);
    Date billDateEnd = DateUtils.addDays(SEPT30, 2);

    while (!billDateStart.after(billDateEnd)) {
      Date nextDay = DateUtils.addDays(billDateStart, 1);

      MeterUsage meterUsage =
          MeterUsageTestFactory.createRandomAwsMeterUsage(
              MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
              group.getId(),
              AWSRegionName.EU_CENTRAL_1,
              null,
              billDateStart,
              nextDay);

      MeterUsage meterUsage2 =
          MeterUsageTestFactory.createRandomAwsMeterUsage(
              MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
              group2.getId(),
              AWSRegionName.EU_CENTRAL_1,
              null,
              billDateStart,
              nextDay);

      save(
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group2.getId())
              .build());

      save(
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group2.getId())
              .build());

      save(new MeterUsage.Builder(meterUsage).quantity(5).build());
      save(new MeterUsage.Builder(meterUsage2).quantity(5).build());

      billDateStart = nextDay;
    }

    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT1);

    assertTrue(result.isPresent());
    assertEquals(1, result.get().getBillingAuditErrors().size());
    assertTrue(
        result
            .get()
            .getBillingAuditErrors()
            .get(0)
            .getErrorMessage()
            .contains(
                String.format(
                    "NDS_AWS_SERVERLESS_DATA_TRANSFER total quantity for 2 group(s) in org %s"
                        + " doesn't match between meter usages (10.0000) and line items (20.0000),"
                        + " bill date 2022-08-31.",
                    org.getId())));
    assertTrue(
        result
            .get()
            .getBillingAuditErrors()
            .get(0)
            .getErrorMessage()
            .contains(group.getId().toString()));
    assertTrue(
        result
            .get()
            .getBillingAuditErrors()
            .get(0)
            .getErrorMessage()
            .contains(group2.getId().toString()));
  }

  @Test
  public void audit_skuWithDiscrepancyNotSkipped_activationRulesActive() {
    // Create an activation rule that activates a SKU by default after the audit date range.
    activationRulesDao.createActivationRules(
        Set.of(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER),
        List.of(
            ActivationRule.defaultRule(
                AUG1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
    Group group2 = orgFactory.createNdsGroup(org, AUG1);

    Date billDateStart = SEPT1;

    // This meter usage would have caused discrepancies if this SKU was included.
    while (!billDateStart.after(SEPT5)) {
      Date nextDay = DateUtils.addDays(billDateStart, 1);

      MeterUsage meterUsage =
          MeterUsageTestFactory.createRandomAwsMeterUsage(
              MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
              group.getId(),
              AWSRegionName.EU_CENTRAL_1,
              null,
              billDateStart,
              nextDay);

      MeterUsage meterUsage2 =
          MeterUsageTestFactory.createRandomAwsMeterUsage(
              MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
              group2.getId(),
              AWSRegionName.EU_CENTRAL_1,
              null,
              billDateStart,
              nextDay);

      save(
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group2.getId())
              .build());

      save(
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group2.getId())
              .build());

      save(new MeterUsage.Builder(meterUsage).quantity(5).build());
      save(new MeterUsage.Builder(meterUsage2).quantity(5).build());

      billDateStart = nextDay;
    }

    double counterStart = BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get();
    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT5);

    assertTrue(result.isPresent());
    assertEquals(1, result.get().getBillingAuditErrors().size());
    assertTrue(
        result
            .get()
            .getBillingAuditErrors()
            .get(0)
            .getErrorMessage()
            .contains(
                String.format(
                    "NDS_AWS_SERVERLESS_DATA_TRANSFER total quantity for 2 group(s) in org %s"
                        + " doesn't match between meter usages (10.0000) and line items (20.0000),"
                        + " bill date 2022-09-04.",
                    org.getId())));
    assertTrue(
        result
            .get()
            .getBillingAuditErrors()
            .get(0)
            .getErrorMessage()
            .contains(group.getId().toString()));
    assertTrue(
        result
            .get()
            .getBillingAuditErrors()
            .get(0)
            .getErrorMessage()
            .contains(group2.getId().toString()));
    assertEquals(
        counterStart + 1,
        BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get(),
        MathUtils.EPSILON);
  }

  @Test
  public void audit_fail_discrepancyFound_multipleDaysAndSkus() {
    save(
        new BillingProperty(
            METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_START_AUDIT_DATE, "2022-08-30"));

    // should be filtered out during the audit
    save(
        new BillingProperty(
            METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_DISABLE_SKUS,
            "NDS_AWS_SERVERLESS_STORAGE,NDS_AWS_DATA_LAKE_STORAGE"));

    Date billDateStart = DateUtils.addDays(SEPT1, -2);
    Date billDateEnd = DateUtils.addDays(SEPT30, 2);

    while (!billDateStart.after(billDateEnd)) {
      Date nextDay = DateUtils.addDays(billDateStart, 1);

      for (MeterId meterId :
          Set.of(
              MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
              MeterId.NDS_GCP_DATA_TRANSFER_GOOGLE,
              MeterId.NDS_AWS_SERVERLESS_STORAGE,
              MeterId.NDS_AWS_DATA_LAKE_STORAGE)) {
        MeterUsage meterUsage =
            MeterUsageTestFactory.createRandomAwsMeterUsage(
                meterId, group.getId(), AWSRegionName.EU_CENTRAL_1, null, billDateStart, nextDay);

        save(
            new LineItem.Builder()
                .quantity(20)
                .sku(SKU.METER_ID_TO_SKUS_MAP.get(meterId).iterator().next())
                .invoiceId(new ObjectId())
                .startDate(billDateStart)
                .endDate(nextDay)
                .groupId(group.getId())
                .build());

        save(new MeterUsage.Builder(meterUsage).quantity(10).build());
      }

      billDateStart = nextDay;
    }

    double counterStart = BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get();
    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT1);

    assertTrue(result.isPresent());

    Set<String> expectedErrors =
        Set.of(
            "NDS_AWS_SERVERLESS_DATA_TRANSFER total quantity for 1 group(s) in org %s doesn't match"
                + " between meter usages (10.0000) and line items (20.0000), bill date 2022-08-30."
                + " groupIds=%s",
            "NDS_AWS_SERVERLESS_DATA_TRANSFER total quantity for 1 group(s) in org %s doesn't match"
                + " between meter usages (10.0000) and line items (20.0000), bill date 2022-08-31."
                + " groupIds=%s",
            "NDS_GCP_DATA_TRANSFER_GOOGLE total quantity for 1 group(s) in org %s doesn't match"
                + " between meter usages (10.0000) and line items (20.0000), bill date 2022-08-30."
                + " groupIds=%s",
            "NDS_GCP_DATA_TRANSFER_GOOGLE total quantity for 1 group(s) in org %s doesn't match"
                + " between meter usages (10.0000) and line items (20.0000), bill date"
                + " 2022-08-31. groupIds=%s");

    assertEquals(
        result.get().getBillingAuditErrors().stream()
            .map(BillingAuditError::getErrorMessage)
            .collect(Collectors.toSet()),
        expectedErrors.stream()
            .map(s -> String.format(s, org.getId(), List.of(group.getId())))
            .collect(Collectors.toSet()));
    assertEquals(
        counterStart + 1,
        BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get(),
        MathUtils.EPSILON);
  }

  @Test
  public void getAuditableSkus() {
    var auditableSkus = auditor.getAuditableSkus();
    assertTrue(auditableSkus.contains(SKU.MMS_BACKUP_STORAGE));
    assertTrue(auditableSkus.contains(SKU.METERING_E2E_BILLING));
    assertFalse(auditableSkus.contains(SKU.MMS_BACKUP_STORAGE_FREE_TIER));

    save(
        new BillingProperty(
            METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_DISABLE_SKUS,
            SKU.MMS_BACKUP_STORAGE.name() + " ," + SKU.METERING_E2E_BILLING.name()));

    auditableSkus = auditor.getAuditableSkus();
    assertFalse(auditableSkus.contains(SKU.MMS_BACKUP_STORAGE));
    assertFalse(auditableSkus.contains(SKU.METERING_E2E_BILLING));
  }

  @Test
  public void audit_pass_cloudManager() {
    Date billDateStart = DateUtils.addDays(SEPT1, -2);

    List<SKU> cloudMangerSkus =
        List.of(SKU.MMS_PREMIUM, SKU.MMS_STANDARD, SKU.MMS_FREE_TIER, SKU.MMS_FREE_TRIAL);

    String disabled =
        Arrays.stream(SKU.values())
            .filter(sku -> !cloudMangerSkus.contains(sku))
            .map(SKU::name)
            .collect(Collectors.joining(","));

    billingPropertySvc.setProperty(
        METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_DISABLE_SKUS, disabled);

    while (!billDateStart.after(SEPT30)) {
      Date nextDay = DateUtils.addDays(billDateStart, 1);

      MeterUsage meterUsage =
          MeterUsageTestFactory.createMeterUsage(
              MeterId.CLOUD_MANAGER_INSTANCE,
              group.getId(),
              billDateStart,
              nextDay,
              10,
              new NoDimensions());

      LineItem lineItem =
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.MMS_PREMIUM)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group.getId())
              .build();

      save(lineItem);
      save(new MeterUsage.Builder(meterUsage).build());

      billDateStart = nextDay;
    }

    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT30);

    assertFalse(result.isPresent());
  }

  @Test
  public void audit_fail_cloudManager() {
    List<SKU> cloudMangerSkus =
        List.of(SKU.MMS_PREMIUM, SKU.MMS_STANDARD, SKU.MMS_FREE_TIER, SKU.MMS_FREE_TRIAL);

    String disabled =
        Arrays.stream(SKU.values())
            .filter(sku -> !cloudMangerSkus.contains(sku))
            .map(SKU::name)
            .collect(Collectors.joining(","));

    billingPropertySvc.setProperty(
        METER_USAGE_BILL_DATE_CONSISTENCY_AUDITOR_DISABLE_SKUS, disabled);

    Date billDateStart = DateUtils.addDays(SEPT1, -2);
    while (!billDateStart.after(SEPT30)) {
      Date nextDay = DateUtils.addDays(billDateStart, 1);

      MeterUsage meterUsage =
          MeterUsageTestFactory.createMeterUsage(
              MeterId.CLOUD_MANAGER_INSTANCE,
              group.getId(),
              billDateStart,
              nextDay,
              10,
              new NoDimensions());

      LineItem lineItem =
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.MMS_PREMIUM)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group.getId())
              .build();

      LineItem lineItem2 =
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.MMS_FREE_TIER)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group.getId())
              .build();

      save(lineItem);
      save(lineItem2);
      save(new MeterUsage.Builder(meterUsage).build());

      billDateStart = nextDay;
    }

    double counterStart = BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get();
    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT30);

    Set<String> errors =
        result.map(AuditFailureDetail::getBillingAuditErrors).orElse(List.of()).stream()
            .map(BillingAuditError::getErrorMessage)
            .collect(Collectors.toSet());

    assertEquals(
        4,
        errors.size(),
        "There should have been 4 errors in audit result - one for each cloud manager SKU");
    assertEquals(
        counterStart + 1,
        BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get(),
        MathUtils.EPSILON);
  }

  @Test
  public void audit_pass_freeTier() {
    Date billDateStart = DateUtils.addDays(SEPT1, -2);
    while (!billDateStart.after(SEPT30)) {
      Date nextDay = DateUtils.addDays(billDateStart, 1);

      MeterUsage meterUsage =
          MeterUsageTestFactory.createMeterUsage(
              MeterId.MMS_BACKUP_STORAGE,
              group.getId(),
              billDateStart,
              nextDay,
              11,
              new MMSBackupStorageUsageDimensions.Builder()
                  .snapShotInfo(new SnapshotInfo(31, 31))
                  .replicaSet(new ReplicaSetInfo(new ObjectId(), "test"))
                  .clusterUniqueId(new ObjectId())
                  .clusterName("test")
                  .build());

      LineItem lineItem =
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.MMS_BACKUP_STORAGE)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group.getId())
              .build();

      LineItem lineItemFreeTier =
          new LineItem.Builder()
              .quantity(1)
              .sku(SKU.MMS_BACKUP_STORAGE_FREE_TIER)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group.getId())
              .build();

      save(lineItem);
      save(lineItemFreeTier);
      save(new MeterUsage.Builder(meterUsage).build());

      billDateStart = nextDay;
    }

    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT30);

    assertFalse(result.isPresent());
  }

  @Test
  public void audit_fail_freeTier() {
    Date billDateStart = DateUtils.addDays(SEPT1, -2);
    while (!billDateStart.after(SEPT30)) {
      Date nextDay = DateUtils.addDays(billDateStart, 1);

      MeterUsage meterUsage =
          MeterUsageTestFactory.createMeterUsage(
              MeterId.MMS_BACKUP_STORAGE,
              group.getId(),
              billDateStart,
              nextDay,
              15,
              new MMSBackupStorageUsageDimensions.Builder()
                  .snapShotInfo(new SnapshotInfo(31, 31))
                  .replicaSet(new ReplicaSetInfo(new ObjectId(), "test"))
                  .clusterUniqueId(new ObjectId())
                  .clusterName("test")
                  .build());

      LineItem lineItem =
          new LineItem.Builder()
              .quantity(10)
              .sku(SKU.MMS_BACKUP_STORAGE)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group.getId())
              .build();

      LineItem lineItemFreeTier =
          new LineItem.Builder()
              .quantity(1)
              .sku(SKU.MMS_BACKUP_STORAGE_FREE_TIER)
              .invoiceId(new ObjectId())
              .startDate(billDateStart)
              .endDate(nextDay)
              .groupId(group.getId())
              .build();

      save(lineItem);
      save(lineItemFreeTier);
      save(new MeterUsage.Builder(meterUsage).build());

      billDateStart = nextDay;
    }

    double counterStart = BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get();
    Optional<AuditFailureDetail> result = auditor.doAudit(getDataToAudit(), SEPT30);

    assertEquals(
        List.of(
            String.format(
                "MMS_BACKUP_STORAGE total quantity for 1 group(s) in org %s doesn't match between"
                    + " meter usages (15.0000) and line items (11.0000), bill date 2022-09-29."
                    + " groupIds=%s Free tier SKU qty (MMS_BACKUP_STORAGE_FREE_TIER) - 0.0.",
                org.getId(), List.of(group.getId()))),
        result.map(AuditFailureDetail::getBillingAuditErrors).orElse(List.of()).stream()
            .map(BillingAuditError::getErrorMessage)
            .collect(Collectors.toList()));
    assertEquals(
        counterStart + 1,
        BILLING_REVENUE_GENERATING_ORG_INCORRECT_CHARGES.get(),
        MathUtils.EPSILON);
  }

  @Test
  public void audit_embargoedOrgWithUnbilledMeterUsage_isIgnored() {
    Organization embargoedOrg = orgFactory.createEmbargoedOrganization(AUG1, AUG1);
    Group embargoedGroup = orgFactory.createNdsGroup(embargoedOrg, AUG1);

    Date billDateStart = DateUtils.addDays(SEPT1, -1);
    Date nextDay = DateUtils.addDays(billDateStart, 1);

    MeterUsage embargoedOrgMeterUsage =
        MeterUsageTestFactory.createRandomAwsMeterUsage(
            MeterId.NDS_AWS_SERVERLESS_DATA_TRANSFER,
            embargoedGroup.getId(),
            AWSRegionName.EU_CENTRAL_1,
            null,
            billDateStart,
            nextDay);

    save(new MeterUsage.Builder(embargoedOrgMeterUsage).quantity(100.0).build());

    List<ObjectId> allOrgIds = auditor.getIdsToAudit();
    List<Organization> orgsToAudit = auditor.getDataToAudit(allOrgIds);

    boolean embargoedOrgInAuditList =
        orgsToAudit.stream().anyMatch(org -> org.getId().equals(embargoedOrg.getId()));
    assertFalse(embargoedOrgInAuditList, "Embargoed orgs should not be in audit list");

    assertEquals(1, orgsToAudit.size(), "Only non-embargoed orgs should be in audit list");
    assertEquals(org.getId(), orgsToAudit.get(0).getId());

    Optional<AuditFailureDetail> embargoedResult = auditor.doAudit(embargoedOrg, SEPT1);
    assertFalse(
        embargoedResult.isPresent(), "Embargoed orgs should not be audited but should not error");

    Optional<AuditFailureDetail> normalResult = auditor.doAudit(org, SEPT1);
    assertFalse(normalResult.isPresent(), "Normal orgs should not have any audit errors");
  }

  @Test
  public void ifSampleRatesConfigured_returnsCorrectSampleNumber() {
    MeterUsageBillDateConsistencyAuditor spiedAuditor = spy(auditor);

    Organization org2 =
        new Organization.Builder()
            .id(ObjectId.get())
            .created(SEPT1)
            .updated(SEPT1)
            .name("Test Org 2")
            .paymentStatus(ok(SEPT1))
            .acceptedCNRegionsTermsOfServiceDate(new Date())
            .build();
    save(org2);

    Organization org3 =
        new Organization.Builder()
            .id(ObjectId.get())
            .created(SEPT1)
            .updated(SEPT1)
            .name("Test Org 3")
            .paymentStatus(ok(SEPT1))
            .acceptedCNRegionsTermsOfServiceDate(new Date())
            .build();
    save(org3);

    LineItem lineItem1 =
        new LineItem.Builder()
            .quantity(10)
            .totalPriceCents(100)
            .sku(SKU.NDS_AZURE_DATA_TRANSFER)
            .invoiceId(new ObjectId())
            .startDate(SEPT1)
            .endDate(SEPT2)
            .groupId(new ObjectId())
            .orgId(org.getId())
            .build();

    LineItem lineItem2 =
        new LineItem.Builder()
            .quantity(15)
            .totalPriceCents(150)
            .sku(SKU.NDS_AZURE_DATA_TRANSFER)
            .invoiceId(new ObjectId())
            .startDate(SEPT1)
            .endDate(SEPT2)
            .groupId(new ObjectId())
            .orgId(org2.getId())
            .build();

    LineItem lineItem3 =
        new LineItem.Builder()
            .quantity(20)
            .totalPriceCents(200)
            .sku(SKU.NDS_AZURE_DATA_TRANSFER)
            .invoiceId(new ObjectId())
            .startDate(SEPT1)
            .endDate(SEPT2)
            .groupId(new ObjectId())
            .orgId(org2.getId())
            .build();

    LineItem lineItem4 =
        new LineItem.Builder()
            .quantity(30)
            .totalPriceCents(300)
            .sku(SKU.NDS_AZURE_DATA_TRANSFER)
            .invoiceId(new ObjectId())
            .startDate(SEPT1)
            .endDate(SEPT2)
            .groupId(new ObjectId())
            .orgId(org3.getId())
            .build();

    save(lineItem1);
    save(lineItem2);
    save(lineItem3);
    save(lineItem4);

    when(spiedAuditor.now()).thenReturn(SEPT2);
    auditorConfigDao.updateSampleRate(spiedAuditor.getAuditorName(), TEST_SAMPLE_RATE);
    assertEquals(TEST_SAMPLE_NUMBER, spiedAuditor.getSamplingNumber());
  }

  @Test
  public void audit_orgCreatedAfterBillingRun_isSkipped() {
    MeterUsageBillDateConsistencyAuditor spiedAuditor = spy(auditor);

    Organization orgCreatedAfterBilling =
        orgFactory.createCloudOrganization(SEPT2, ok(SEPT2), false);

    ListAppender<ILoggingEvent> listAppender = getLoggingEventListAppender();

    Optional<AuditFailureDetail> result = spiedAuditor.doAudit(orgCreatedAfterBilling, SEPT1);
    assertTrue(result.isEmpty());

    boolean hasSkippingLog =
        listAppender.list.stream()
            .anyMatch(
                event ->
                    event.getLevel().equals(Level.INFO)
                        && event
                            .getFormattedMessage()
                            .contains(
                                "Skipping audit for org created after most recent billing run"
                                    + " completion."));

    assertTrue(hasSkippingLog, "Should log when skipping organization created after billing run");

    Organization orgCreatedBeforeBilling =
        orgFactory.createCloudOrganization(AUG31, ok(AUG31), false);

    listAppender.list.clear();

    result = spiedAuditor.doAudit(orgCreatedBeforeBilling, SEPT5);
    assertTrue(result.isEmpty());

    hasSkippingLog =
        logsContainsString(
            listAppender,
            "Skipping audit for org created after most recent billing run completion.");

    assertFalse(hasSkippingLog, "Should log when skipping organization created after billing run");
  }

  @Test
  public void audit_groupCreatedAfterBillingRun_isSkipped() {
    MeterUsageBillDateConsistencyAuditor spiedAuditor = spy(auditor);

    ListAppender<ILoggingEvent> listAppender = getLoggingEventListAppender();

    Organization orgCreatedBeforeBilling =
        orgFactory.createCloudOrganization(AUG31, ok(AUG31), false);
    orgFactory.createNdsGroup(orgCreatedBeforeBilling, AUG31);
    orgFactory.createNdsGroup(orgCreatedBeforeBilling, SEPT2);

    listAppender.list.clear();

    Optional<AuditFailureDetail> result = spiedAuditor.doAudit(orgCreatedBeforeBilling, SEPT1);

    assertTrue(result.isEmpty());

    boolean hasSkippingLog =
        logsContainsString(
            listAppender, "Filtered out groups created after most recent billing run");

    assertTrue(hasSkippingLog, "Should log when skipping organization created after billing run");
  }

  @Test
  public void audit_orgPaymentStatus_setToOKAfterDailyBilling_isSkipped() {
    // 3am UTC is after 2am daily billing time
    Date afterDailyBilling = TimeUtils.fromISOString("2022-09-01T3:00:00");
    Organization org = orgFactory.createCloudOrganization(AUG1, ok(afterDailyBilling), false);
    var listAppender = getLoggingEventListAppender();
    auditor.doAudit(org, SEPT1);
    boolean hasSkippingLog =
        logsContainsString(
            listAppender,
            "Skipping audit for org with recent payment status OK after most recent billing run"
                + " completion");
    assertTrue(hasSkippingLog);
  }

  private static ListAppender<ILoggingEvent> getLoggingEventListAppender() {
    Logger logger = (Logger) LoggerFactory.getLogger(MeterUsageBillDateConsistencyAuditor.class);
    ListAppender<ILoggingEvent> listAppender = new ListAppender<>();
    listAppender.start();
    logger.addAppender(listAppender);
    return listAppender;
  }

  private static boolean logsContainsString(
      ListAppender<ILoggingEvent> listAppender, String string) {
    return listAppender.list.stream()
        .anyMatch(
            event ->
                event.getLevel().equals(Level.INFO)
                    && event.getFormattedMessage().contains(string));
  }
}
