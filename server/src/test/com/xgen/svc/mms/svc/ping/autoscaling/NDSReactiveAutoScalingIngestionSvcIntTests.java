package com.xgen.svc.mms.svc.ping.autoscaling;

import static com.xgen.svc.mms.svc.ping.PingRequestTestFactory.getTestDiskGBHardwareMetrics;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.metrics._public.model.hardware.DiskPartitionMetrics;
import com.xgen.cloud.monitoring.metrics._public.model.hardware.HardwareMetrics;
import com.xgen.cloud.monitoring.metrics._public.model.hardware.Platform;
import com.xgen.cloud.monitoring.metrics._public.model.hardware.SystemCpuMetrics;
import com.xgen.cloud.monitoring.metrics._public.model.hardware.SystemMemoryMetrics;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.ping.Ping;
import com.xgen.cloud.nds.autoscaling.context._private.dao.AutoScalingContextDao;
import com.xgen.cloud.nds.autoscaling.context._public.model.AutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ClusterComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ShardComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.SingleScalingCriteria;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingCPUThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingMemoryThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.disk.DiskGBAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.util.ComputeAutoScalingContextUtil;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSAutoScaling;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSComputeAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.DiskGBAutoScaling;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._private.dao.versions.PhasedVersionDao;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.PhasedVersionFactory;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersionParameters;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.mms.svc.ping.HardwareAutomationPingMsg;
import com.xgen.svc.mms.svc.ping.IngestionTestUtils;
import com.xgen.svc.mms.svc.ping.PingEvent;
import com.xgen.svc.mms.svc.ping.PingRequest;
import com.xgen.svc.mms.svc.ping.PingRequestFactory;
import com.xgen.svc.mms.svc.ping.PingSource;
import com.xgen.svc.mms.svc.ping.PingSourceFactory;
import com.xgen.svc.mms.svc.ping.ingestion.BaseIngestionTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.stream.IntStream;
import org.apache.commons.collections4.iterators.LoopingListIterator;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.BasicBSONObject;
import org.bson.types.ObjectId;
import org.junit.Test;

public class NDSReactiveAutoScalingIngestionSvcIntTests extends BaseIngestionTest {

  private static final String CLUSTER_NAME = "autoScale";
  private static final String MOCKED_METRICS_ENABLE_PROP_KEY =
      "nds.serverless.metrics.mocks.enabled";
  public static final int MS_BUFFER_FOR_PLANNING_DATE = 1000;

  @Inject private NDSReactiveAutoScalingIngestionSvc _ndsReactiveAutoScalingIngestionSvc;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private AutoScalingContextDao _autoScalingContextDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private PingSourceFactory _pingSourceFactory;
  @Inject private PingRequestFactory _pingRequestFactory;
  @Inject private AppSettings _appSettings;
  @Inject private JUnit5NDSBaseTest _ndsBaseTest;
  @Inject private PhasedVersionDao _phasedVersionDao;
  @Inject private PhasedVersionFactory _phasedVersionFactory;

  private Organization _organization;
  private Group _mmsGroup;
  private ObjectId _ndsGroupId;

  @Override
  public void setUp() throws Exception {
    super.setUp();
    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _mmsGroup = MmsFactory.createGroup(_organization);
    _ndsGroupId = _mmsGroup.getId();
    _ndsGroupSvc.create(_ndsGroupId, new NDSManagedX509(), false);
    _appSettings.setProp(
        AppSettings.Fields.NDS_AUTO_SCALING_SCALE_DOWN_DAYS.value,
        "MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY",
        AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        Fields.NDS_AUTO_SCALING_SCALE_DOWN_HOURS.value,
        "00:00, 23:59",
        AppSettings.SettingType.MEMORY);
    _appSettings.setProp(MOCKED_METRICS_ENABLE_PROP_KEY, "true", AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        Fields.NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_MTMS_PER_POOL.value,
        "25",
        SettingType.MEMORY);
    _appSettings.setProp(
        Fields.NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_TENANTS_PER_POOL.value,
        "3000",
        SettingType.MEMORY);
    final PhasedVersionParameters osVersion =
        new PhasedVersionParameters(SoftwareType.OS_IMAGE, "153");
    _phasedVersionDao.save(_phasedVersionFactory.createPhasedVersion(osVersion));
  }

  @Test
  public void testFlagClusterForDiskGBCheck_scalingDisabled() throws Exception {
    final String clusterName = "scalingDisabled";
    final ObjectId groupId = _mmsGroup.getId();
    final BasicDBObject clusterDescriptionDoc =
        NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName);
    final ClusterDescription clusterDescription = new ClusterDescription(clusterDescriptionDoc);

    final BasicDBList hosts = new BasicDBList();
    hosts.add("scalingDisabled:27017");

    final DBObject autoScaling =
        AWSAutoScaling.getDefaultAutoScaling().toBuilder()
            .diskGB(DiskGBAutoScaling.builder().enabled(false).build())
            .build()
            .toDBObject();

    clusterDescriptionDoc.append("mongoDBUriHosts", hosts);

    final BasicDBObject regionConfig =
        (BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject)
                            ((BasicDBList) clusterDescriptionDoc.get("replicationSpecList")).get(0))
                        .get("regionConfigs"))
                .get(0);

    ((BasicDBObject) regionConfig.get("electableSpecs")).put("autoScaling", autoScaling);

    ((BasicDBObject) regionConfig.get("readOnlySpecs")).put("autoScaling", autoScaling);

    ((BasicDBObject) regionConfig.get("analyticsSpecs")).put("autoScaling", autoScaling);

    ((BasicDBObject) regionConfig.get("hiddenSecondarySpecs")).put("autoScaling", autoScaling);

    _clusterDescriptionDao.saveReplicaSafe(clusterDescriptionDoc);
    _autoScalingContextDao.save(
        AutoScalingContext.getDefaultContext(groupId, clusterName, AutoScalingMode.CLUSTER));

    saveInstanceHardwareForClusterHost(clusterDescription, groupId, "scalingDisabled");

    final long now = System.currentTimeMillis();
    final DiskPartitionMetrics metric =
        IngestionTestUtils.newDiskMetric(now, 10L, 5.5, "scalingDisabled:27017$data");
    final Map<String, DiskPartitionMetrics> diskMetrics = Collections.singletonMap("xvdb", metric);

    _ndsReactiveAutoScalingIngestionSvc
        .flagClusterForDiskGBCheck(diskMetrics, getTestPingSourceForGroup(_mmsGroup))
        .get();

    assertThat(
        _autoScalingContextDao
            .find(groupId, clusterName)
            .map(AutoScalingContext::getDiskGBContext)
            .map(DiskGBAutoScalingContext::isNeedsCheck)
            .orElseThrow(),
        equalTo(false));
    assertThat(_ndsGroupDao.find(groupId).map(NDSGroup::getPlanASAP).orElseThrow(), equalTo(false));
  }

  @Test
  public void testFlagClusterForDiskGBCheck_scalingEnabled() throws Exception {
    final String clusterName = "scalingEnabled";
    final ObjectId groupId = _mmsGroup.getId();
    final BasicDBObject clusterDescriptionDoc =
        NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName);
    final ClusterDescription clusterDescription = new ClusterDescription(clusterDescriptionDoc);

    final BasicDBList hosts = new BasicDBList();
    hosts.add("scalingEnabled:27017");

    final DBObject autoScaling = AWSAutoScaling.getDefaultAutoScaling().toDBObject();
    clusterDescriptionDoc.append("mongoDBUriHosts", hosts);

    ((BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject)
                            ((BasicDBList) clusterDescriptionDoc.get("replicationSpecList")).get(0))
                        .get("regionConfigs"))
                .get(0))
        .append("autoScaling", autoScaling);

    _clusterDescriptionDao.saveReplicaSafe(clusterDescriptionDoc);

    assertThat(
        new ClusterDescription(clusterDescriptionDoc)
            .getAutoScaling(NodeTypeFamily.BASE)
            .getDiskGB()
            .isEnabled(),
        equalTo(true));

    final DiskGBAutoScalingContext diskGBContext =
        DiskGBAutoScalingContext.buildDefaultContext().builder().needsCheck(false).build();
    _autoScalingContextDao.save(
        AutoScalingContext.getDefaultContext(groupId, clusterName, AutoScalingMode.CLUSTER)
            .toBuilder()
            .diskGBContext(diskGBContext)
            .build());

    saveInstanceHardwareForClusterHost(clusterDescription, groupId, "scalingEnabled");

    final long now = System.currentTimeMillis();
    final DiskPartitionMetrics metric =
        IngestionTestUtils.newDiskMetric(now, 10L, 5.5, "scalingEnabled:27017$data");
    final Map<String, DiskPartitionMetrics> diskMetrics = Collections.singletonMap("xvdb", metric);

    _ndsReactiveAutoScalingIngestionSvc
        .flagClusterForDiskGBCheck(diskMetrics, getTestPingSourceForGroup(_mmsGroup))
        .get();

    final AutoScalingContext autoScalingContext =
        _autoScalingContextDao.find(groupId, clusterName).orElseThrow();

    assertThat(autoScalingContext.getDiskGBContext().isNeedsCheck(), equalTo(true));
    assertThat(_ndsGroupDao.find(groupId).map(NDSGroup::getPlanASAP).orElseThrow(), equalTo(true));
  }

  @Test
  public void testFlagClusterForDiskGBCheck_atMaxSize() throws Exception {
    // maxSizeFailedDate is null
    {
      final Group group = MmsFactory.createGroup(_organization);
      final ObjectId groupId = group.getId();
      _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);
      final String clusterName = "atMaxSize0";
      final DiskGBAutoScalingContext diskGBContext =
          DiskGBAutoScalingContext.buildDefaultContext().builder().needsCheck(false).build();
      doTestFlagClusterForDiskGBCheck_atMaxSize(group, clusterName, diskGBContext, true);
    }

    // maxSizeFailedDate is within DISK_SCALE_UP_AT_MAX_DISK_SIZE_ATTEMPT_WINDOW
    {
      final Group group = MmsFactory.createGroup(_organization);
      final ObjectId groupId = group.getId();
      _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);
      final String clusterName = "atMaxSize1";
      final DiskGBAutoScalingContext diskGBContext =
          DiskGBAutoScalingContext.buildDefaultContext()
              .builder()
              .needsCheck(false)
              .atMaxSizeFailedDate(
                  Date.from(
                      Instant.now()
                          .minus(
                              NDSReactiveAutoScalingIngestionSvc
                                  .DISK_SCALE_UP_AT_MAX_DISK_SIZE_ATTEMPT_WINDOW
                                  .dividedBy(2))))
              .build();
      doTestFlagClusterForDiskGBCheck_atMaxSize(group, clusterName, diskGBContext, false);
    }

    // maxSizeFailedDate is outside of DISK_SCALE_UP_AT_MAX_DISK_SIZE_ATTEMPT_WINDOW
    {
      final Group group = MmsFactory.createGroup(_organization);
      final ObjectId groupId = group.getId();
      _ndsGroupSvc.create(groupId, new NDSManagedX509(), false);
      final String clusterName = "atMaxSize2";
      final DiskGBAutoScalingContext diskGBContext =
          DiskGBAutoScalingContext.buildDefaultContext()
              .builder()
              .needsCheck(false)
              .atMaxSizeFailedDate(
                  Date.from(
                      Instant.now()
                          .minus(
                              NDSReactiveAutoScalingIngestionSvc
                                  .DISK_SCALE_UP_AT_MAX_DISK_SIZE_ATTEMPT_WINDOW
                                  .multipliedBy(2))))
              .build();
      doTestFlagClusterForDiskGBCheck_atMaxSize(group, clusterName, diskGBContext, true);
    }
  }

  private void doTestFlagClusterForDiskGBCheck_atMaxSize(
      final Group pGroup,
      final String pClusterName,
      final DiskGBAutoScalingContext pDiskGBContext,
      final boolean pExpectedResult)
      throws ExecutionException, InterruptedException {
    final ObjectId groupId = pGroup.getId();
    final BasicDBObject clusterDescriptionDoc =
        NDSModelTestFactory.getAWSClusterDescription(groupId, pClusterName);
    final ClusterDescription clusterDescription = new ClusterDescription(clusterDescriptionDoc);

    final BasicDBList hosts = new BasicDBList();
    hosts.add(String.format("%s:27017", pClusterName));

    final DBObject autoScaling = AWSAutoScaling.getDefaultAutoScaling().toDBObject();
    clusterDescriptionDoc.append("mongoDBUriHosts", hosts);

    ((BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject)
                            ((BasicDBList) clusterDescriptionDoc.get("replicationSpecList")).get(0))
                        .get("regionConfigs"))
                .get(0))
        .append("autoScaling", autoScaling);

    _clusterDescriptionDao.saveReplicaSafe(clusterDescriptionDoc);

    assertTrue(
        new ClusterDescription(clusterDescriptionDoc)
            .getAutoScaling(NodeTypeFamily.BASE)
            .getDiskGB()
            .isEnabled());

    saveInstanceHardwareForClusterHost(clusterDescription, groupId, pClusterName);

    final long now = System.currentTimeMillis();
    final DiskPartitionMetrics metric =
        IngestionTestUtils.newDiskMetric(
            now, 10L, 5.5, String.format("%s:27017$data", pClusterName));
    final Map<String, DiskPartitionMetrics> diskMetrics = Collections.singletonMap("xvdb", metric);

    _autoScalingContextDao.save(
        AutoScalingContext.getDefaultContext(groupId, pClusterName, AutoScalingMode.CLUSTER)
            .toBuilder()
            .diskGBContext(pDiskGBContext)
            .build());

    _ndsReactiveAutoScalingIngestionSvc
        .flagClusterForDiskGBCheck(diskMetrics, getTestPingSourceForGroup(pGroup))
        .get();

    final AutoScalingContext autoScalingContext =
        _autoScalingContextDao.find(groupId, pClusterName).orElseThrow();

    assertEquals(pExpectedResult, autoScalingContext.getDiskGBContext().isNeedsCheck());
    assertEquals(
        pExpectedResult, _ndsGroupDao.find(groupId).map(NDSGroup::getPlanASAP).orElseThrow());
  }

  @Test
  public void testFlagClusterForDiskGBCheck_pingFromDifferentCluster() throws Exception {
    final String clusterName = "foo";
    final ObjectId groupId = _mmsGroup.getId();
    final BasicDBObject clusterDescriptionDoc =
        NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName);
    final ClusterDescription clusterDescription = new ClusterDescription(clusterDescriptionDoc);

    final BasicDBList hosts = new BasicDBList();
    hosts.add("foo:27017");

    final DBObject autoScaling =
        AWSAutoScaling.getDefaultAutoScaling().toBuilder()
            .diskGB(DiskGBAutoScaling.builder().enabled(true).build())
            .build()
            .toDBObject();

    clusterDescriptionDoc.append("mongoDBUriHosts", hosts).append("autoScaling", autoScaling);
    _clusterDescriptionDao.saveReplicaSafe(clusterDescriptionDoc);
    _autoScalingContextDao.save(
        AutoScalingContext.getDefaultContext(groupId, clusterName, AutoScalingMode.CLUSTER));

    saveInstanceHardwareForClusterHost(clusterDescription, groupId, "foo");

    final long now = System.currentTimeMillis();
    final DiskPartitionMetrics metric =
        IngestionTestUtils.newDiskMetric(now, 10L, 5.5, "someOtherHost:27017$data");
    final Map<String, DiskPartitionMetrics> diskMetrics = Collections.singletonMap("xvdb", metric);

    _ndsReactiveAutoScalingIngestionSvc
        .flagClusterForDiskGBCheck(diskMetrics, getTestPingSourceForGroup(_mmsGroup))
        .get();

    assertThat(
        _autoScalingContextDao
            .find(groupId, clusterName)
            .map(AutoScalingContext::getDiskGBContext)
            .map(DiskGBAutoScalingContext::isNeedsCheck)
            .orElseThrow(),
        equalTo(false));
    assertThat(_ndsGroupDao.find(groupId).map(NDSGroup::getPlanASAP).orElseThrow(), equalTo(false));
  }

  @Test
  public void testIsApplicable() {
    final PingSource mmsPingSource = getTestPingSourceForGroup(_mmsGroup);
    final PingSource nonMmsPingSource =
        getTestPingSourceForGroup(MmsFactory.createGroupWithStandardPlan());
    final HardwareMetrics hardwareMetrics = getTestDiskGBHardwareMetrics(100L, 1L);
    final HardwareMetrics repeatedHardwareMetrics = getTestDiskGBHardwareMetrics(100L, 1L, true);

    // success
    {
      final PingRequestFactory.Builder pingRequestBuilder =
          _pingRequestFactory.newPingRequestBuilder(
              new HardwareAutomationPingMsg(
                  mmsPingSource,
                  List.of(hardwareMetrics, hardwareMetrics),
                  "foobar",
                  new BasicBSONObject()));
      pingRequestBuilder.setHardwareMetrics(List.of(hardwareMetrics, hardwareMetrics));
      assertTrue(NDSReactiveAutoScalingIngestionSvc.isApplicable(pingRequestBuilder.build()));
    }

    // hardware metrics null
    {
      final PingRequestFactory.Builder pingRequestBuilder =
          _pingRequestFactory.newPingRequestBuilder(
              new HardwareAutomationPingMsg(
                  mmsPingSource, List.of(), "foobar", new BasicBSONObject()));
      assertFalse(NDSReactiveAutoScalingIngestionSvc.isApplicable(pingRequestBuilder.build()));
    }

    // hardware metrics repeated
    {
      final PingRequestFactory.Builder pingRequestBuilder =
          _pingRequestFactory.newPingRequestBuilder(
              new HardwareAutomationPingMsg(
                  mmsPingSource,
                  List.of(repeatedHardwareMetrics),
                  "foobar",
                  new BasicBSONObject()));
      pingRequestBuilder.setHardwareMetrics(List.of(repeatedHardwareMetrics));
      assertFalse(NDSReactiveAutoScalingIngestionSvc.isApplicable(pingRequestBuilder.build()));
    }

    // not an NDS group
    {
      final PingRequestFactory.Builder pingRequestBuilder =
          _pingRequestFactory.newPingRequestBuilder(
              new HardwareAutomationPingMsg(
                  nonMmsPingSource,
                  List.of(hardwareMetrics, hardwareMetrics),
                  "foobar",
                  new BasicBSONObject()));
      pingRequestBuilder.setHardwareMetrics(List.of(hardwareMetrics, hardwareMetrics));
      assertFalse(NDSReactiveAutoScalingIngestionSvc.isApplicable(pingRequestBuilder.build()));
    }

    // dedicated mongot ping
    {
      final PingRequestFactory.Builder pingRequestBuilder =
          _pingRequestFactory.newPingRequestBuilder(
              new HardwareAutomationPingMsg(
                  mmsPingSource,
                  List.of(hardwareMetrics, hardwareMetrics),
                  "foobar",
                  new BasicBSONObject()));
      pingRequestBuilder.setHardwareMetrics(List.of(hardwareMetrics, hardwareMetrics));
      pingRequestBuilder.setIsDedicatedMongot(true);
      assertFalse(NDSReactiveAutoScalingIngestionSvc.isApplicable(pingRequestBuilder.build()));
    }
  }

  @Test
  public void testIsApplicableForDiskGBAutoScaling() {
    // over usage threshold
    final HardwareMetrics hardwareMetrics0 = getTestDiskGBHardwareMetrics(100L, 1L);
    assertTrue(
        NDSReactiveAutoScalingIngestionSvc.isApplicableForDiskGBAutoScaling(hardwareMetrics0));

    // under usage threshold
    final HardwareMetrics hardwareMetrics2 = getTestDiskGBHardwareMetrics(1L, 100L);
    assertFalse(
        NDSReactiveAutoScalingIngestionSvc.isApplicableForDiskGBAutoScaling(hardwareMetrics2));
  }

  @Test
  public void testIsApplicableForAutoShardingAutoScaling() {
    // usage 0.6 >= default 0.5 -> applicable
    final HardwareMetrics metricsHigh = getTestDiskGBHardwareMetrics(60L, 40L);
    assertTrue(
        _ndsReactiveAutoScalingIngestionSvc.isApplicableForAutoShardingAutoScaling(metricsHigh));

    // usage 0.4 < default 0.5 -> not applicable
    final HardwareMetrics metricsLow = getTestDiskGBHardwareMetrics(40L, 60L);
    assertFalse(
        _ndsReactiveAutoScalingIngestionSvc.isApplicableForAutoShardingAutoScaling(metricsLow));
  }

  @Test
  public void testFlagClusterForAutoShardingCheck_scalingEnabled_underMaxShards() throws Exception {
    // Setup sharded cluster and enable auto-sharding on cluster description
    final Pair<Cluster, String> clusterAndHostname = setUpShardedCluster();
    final Cluster cluster = clusterAndHostname.getLeft();
    final String hostname = clusterAndHostname.getRight();

    final ClusterDescription cd =
        _clusterDescriptionDao
            .findByName(
                cluster.getClusterDescription().getGroupId(),
                cluster.getClusterDescription().getName())
            .orElseThrow();

    final ClusterDescription updated =
        cd.copy()
            .setAutoSharding(
                com.xgen.cloud.nds.project._public.model.AutoSharding.builder()
                    .enabled(true)
                    .maxShards(cd.getNumShards() + 5)
                    .thresholdToAutoShardInBytes(1_000_000L)
                    .build())
            .build();
    _clusterDescriptionDao.save(updated);

    // Ensure autoscaling context has auto-sharding context present with threshold 0.5 and
    // needsCheck=false
    final AutoScalingContext context =
        AutoScalingContext.getDefaultContext(cd.getGroupId(), cd.getName(), AutoScalingMode.CLUSTER)
            .toBuilder()
            .autoShardingContext(
                com.xgen.cloud.nds.autoscaling.context._public.model.autosharding
                    .AutoShardingAutoScalingContext.builder()
                    .diskUsageThresholdPercent(0.5)
                    .build())
            .build();
    _autoScalingContextDao.save(context);

    // Prepare disk metric for the host with usage = 0.6 (> 0.5 default)
    final long now = System.currentTimeMillis();
    final DiskPartitionMetrics metric =
        IngestionTestUtils.newDiskMetric(now, 60L, 40.0, hostname + ":27017$data");
    final Map<String, DiskPartitionMetrics> diskMetrics = Collections.singletonMap("xvdb", metric);

    _ndsReactiveAutoScalingIngestionSvc
        .flagClusterForAutoShardingCheck(diskMetrics, getTestPingSourceForGroup(_mmsGroup))
        .get();

    final AutoScalingContext after =
        _autoScalingContextDao.find(cd.getGroupId(), cd.getName()).orElseThrow();
    assertTrue(after.getAutoShardingContext().isNeedsCheck());
    assertTrue(_ndsGroupDao.find(cd.getGroupId()).map(NDSGroup::getPlanASAP).orElseThrow());
  }

  @Test
  public void testIsApplicableForComputeAutoScaling() {
    final AutoScalingContext context =
        AutoScalingContext.getDefaultContext(_ndsGroupId, "foo", AutoScalingMode.CLUSTER);
    final ComputeAutoScalingContext computeContext = context.getBaseComputeContext();

    // Get min threshold as to not trigger lower threshold in the case of multiples
    final float cpuUpscaleThreshold =
        computeContext.getUpScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingCPUThreshold.class::isInstance)
            .map(ComputeAutoScalingCPUThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingCPUThreshold::getCpuScaleThreshold))
            .orElseThrow()
            .getCpuScaleThreshold();
    final float cpuDownscaleThreshold =
        computeContext.getDownScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingCPUThreshold.class::isInstance)
            .map(ComputeAutoScalingCPUThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingCPUThreshold::getCpuScaleThreshold))
            .orElseThrow()
            .getCpuScaleThreshold();

    final float memoryUpscaleThreshold =
        computeContext.getUpScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingMemoryThreshold.class::isInstance)
            .map(ComputeAutoScalingMemoryThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingMemoryThreshold::getMemoryScaleThreshold))
            .orElseThrow()
            .getMemoryScaleThreshold();
    final float memoryDownscaleThreshold =
        computeContext.getDownScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingMemoryThreshold.class::isInstance)
            .map(ComputeAutoScalingMemoryThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingMemoryThreshold::getMemoryScaleThreshold))
            .orElseThrow()
            .getMemoryScaleThreshold();

    // test fail -- empty cpu and memory metrics
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of("foo-host"),
              HWMetricsState.EMPTY,
              HWMetricsState.EMPTY,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final Pair<Boolean, List<String>> isApplicable =
          NDSReactiveAutoScalingIngestionSvc.isApplicableForComputeAutoScaling(
              pingRequest, pingRequest.getHardwareMetrics());
      assertFalse(isApplicable.getLeft());
      assertTrue(isApplicable.getRight().isEmpty());
    }

    // test fail -- no repeated metrics
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of("foo-host"),
              HWMetricsState.WITHIN,
              HWMetricsState.EMPTY,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              false);
      final Pair<Boolean, List<String>> isApplicable =
          NDSReactiveAutoScalingIngestionSvc.isApplicableForComputeAutoScaling(
              pingRequest, pingRequest.getHardwareMetrics());
      assertFalse(isApplicable.getLeft());
      assertTrue(isApplicable.getRight().isEmpty());
    }

    // test fail -- no host names
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              Collections.emptyList(),
              HWMetricsState.WITHIN,
              HWMetricsState.WITHIN,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final Pair<Boolean, List<String>> isApplicable =
          NDSReactiveAutoScalingIngestionSvc.isApplicableForComputeAutoScaling(
              pingRequest, pingRequest.getHardwareMetrics());
      assertFalse(isApplicable.getLeft());
      assertTrue(isApplicable.getRight().isEmpty());
    }

    // test fail -- multiple host names
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of("foo-host", "bar-host"),
              HWMetricsState.WITHIN,
              HWMetricsState.WITHIN,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final Pair<Boolean, List<String>> isApplicable =
          NDSReactiveAutoScalingIngestionSvc.isApplicableForComputeAutoScaling(
              pingRequest, pingRequest.getHardwareMetrics());
      assertFalse(isApplicable.getLeft());
      assertEquals(List.of("foo-host", "bar-host"), isApplicable.getRight());
    }

    // test success -- empty cpu metrics, populated memory metrics
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of("foo-host"),
              HWMetricsState.EMPTY,
              HWMetricsState.WITHIN,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final Pair<Boolean, List<String>> isApplicable =
          NDSReactiveAutoScalingIngestionSvc.isApplicableForComputeAutoScaling(
              pingRequest, pingRequest.getHardwareMetrics());
      assertTrue(isApplicable.getLeft());
      assertEquals(List.of("foo-host"), isApplicable.getRight());
    }

    // test success -- empty memory metrics, populated cpu metrics
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of("foo-host"),
              HWMetricsState.WITHIN,
              HWMetricsState.EMPTY,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final Pair<Boolean, List<String>> isApplicable =
          NDSReactiveAutoScalingIngestionSvc.isApplicableForComputeAutoScaling(
              pingRequest, pingRequest.getHardwareMetrics());
      assertTrue(isApplicable.getLeft());
      assertEquals(List.of("foo-host"), isApplicable.getRight());
    }

    // test success -- populated cpu and memory metrics
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of("foo-host"),
              HWMetricsState.WITHIN,
              HWMetricsState.WITHIN,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final Pair<Boolean, List<String>> isApplicable =
          NDSReactiveAutoScalingIngestionSvc.isApplicableForComputeAutoScaling(
              pingRequest, pingRequest.getHardwareMetrics());
      assertTrue(isApplicable.getLeft());
      assertEquals(List.of("foo-host"), isApplicable.getRight());
    }
  }

  private AutoScalingContext testOnEventForCompute_setup(
      final Cluster pCluster, AutoScalingMode pScalingMode) {
    AutoScalingContext context =
        AutoScalingContext.getDefaultContext(
            pCluster.getClusterDescription().getGroupId(),
            pCluster.getClusterDescription().getName(),
            AutoScalingMode.CLUSTER);

    if (pScalingMode.equals(AutoScalingMode.SHARD)) {
      // Using conversion utils to fill the shard context fields
      context =
          context.toBuilder()
              .baseComputeContext(
                  ComputeAutoScalingContextUtil.convertClusterToShardComputeAutoScalingContext(
                      (ClusterComputeAutoScalingContext) context.getBaseComputeContext(), pCluster))
              .analyticsComputeContext(
                  ComputeAutoScalingContextUtil.convertClusterToShardComputeAutoScalingContext(
                      (ClusterComputeAutoScalingContext) context.getAnalyticsComputeContext(),
                      pCluster))
              .build();
    }

    _autoScalingContextDao.save(context);

    // Set modify date to far in the past to avoid date from preventing instrumentation checks
    final Date newInstanceSizeModifyDate = new Date(0);
    IntStream.range(0, pCluster.getReplicaSets().size())
        .mapToObj(i -> Pair.of(i, pCluster.getReplicaSets().get(i)))
        .flatMap(
            rsh -> rsh.getRight().getHardware().stream().map(ih -> Pair.of(rsh.getRight(), ih)))
        .forEach(
            ih -> {
              _replicaSetHardwareDao.setInstanceField(
                  ih.getLeft().getId(),
                  ih.getRight().getInstanceId(),
                  false,
                  InstanceHardware.FieldDefs.LAST_INSTANCE_SIZE_MODIFY_DATE,
                  newInstanceSizeModifyDate);
            });
    return context;
  }

  @Test
  public void testOnEventForCompute_Base() throws SvcException {
    final Pair<Cluster, String> clusterAndHostname =
        setUpClusterAndHostnameForOnEventCompute(false);
    testOnEventForCompute(clusterAndHostname.getLeft(), clusterAndHostname.getRight(), false);
  }

  @Test
  public void testOnEventForCompute_Analytics() throws SvcException {
    final Pair<Cluster, String> clusterAndHostname = setUpClusterAndHostnameForOnEventCompute(true);
    testOnEventForCompute(clusterAndHostname.getLeft(), clusterAndHostname.getRight(), true);
  }

  private Pair<Cluster, String> setUpClusterAndHostnameForOnEventCompute(
      final boolean pShouldGetAnalyticsHostname) throws SvcException {
    final Cluster awsCluster =
        _ndsBaseTest.setupCluster(
            _ndsGroupId, CLUSTER_NAME, ClusterDescription.ClusterType.REPLICASET, true);
    final ClusterDescription clusterDescription =
        _clusterDescriptionDao
            .findByName(
                awsCluster.getClusterDescription().getGroupId(),
                awsCluster.getClusterDescription().getName())
            .get();
    final ClusterDescription updatedClusterDescription;
    if (pShouldGetAnalyticsHostname) {
      final ClusterDescription clusterDescriptionWithAnalyticsNodes =
          NDSModelTestFactory.changeSingleZoneClusterAnalyticsNodes(clusterDescription, 1);
      updatedClusterDescription =
          clusterDescriptionWithAnalyticsNodes
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS,
                  AWSAutoScaling.getDisabledAutoScaling().toBuilder()
                      .compute(
                          AWSComputeAutoScaling.builder()
                              .enabled(true)
                              .scaleDownEnabled(true)
                              .minInstanceSize(AWSNDSInstanceSize.M10)
                              .maxInstanceSize(AWSNDSInstanceSize.M40)
                              .build())
                      .build(),
                  NodeTypeFamily.ANALYTICS)
              .build();
    } else {
      updatedClusterDescription =
          clusterDescription
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS,
                  AWSAutoScaling.getDisabledAutoScaling().toBuilder()
                      .compute(
                          AWSComputeAutoScaling.builder()
                              .enabled(true)
                              .scaleDownEnabled(true)
                              .minInstanceSize(AWSNDSInstanceSize.M10)
                              .maxInstanceSize(AWSNDSInstanceSize.M40)
                              .build())
                      .build(),
                  NodeTypeFamily.BASE)
              .build();
    }
    _clusterDescriptionDao.save(updatedClusterDescription);

    if (pShouldGetAnalyticsHostname) {
      final int analyticsIndex = awsCluster.getReplicaSets().get(0).getHardware().size() - 1;
      final String analyticsNodeHostname =
          awsCluster
              .getReplicaSets()
              .get(0)
              .getHardware()
              .get(analyticsIndex)
              .getHostnameForAgents()
              .get();
      return Pair.of(awsCluster, analyticsNodeHostname);
    } else {
      final String baseNodeHostname =
          awsCluster.getReplicaSets().get(0).getHardware().get(0).getHostnameForAgents().get();
      return Pair.of(awsCluster, baseNodeHostname);
    }
  }

  private void testOnEventForCompute(
      final Cluster pCluster, final String pHostname, final boolean pIsAnalyticsCheck) {
    final AutoScalingContext context =
        testOnEventForCompute_setup(pCluster, AutoScalingMode.CLUSTER);
    final ClusterComputeAutoScalingContext computeContext =
        (ClusterComputeAutoScalingContext)
            context.getComputeContext(
                pIsAnalyticsCheck ? NodeTypeFamily.ANALYTICS : NodeTypeFamily.BASE);

    // Get min threshold as to not trigger lower threshold in the case of multiples
    final float cpuUpscaleThreshold =
        computeContext.getUpScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingCPUThreshold.class::isInstance)
            .map(ComputeAutoScalingCPUThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingCPUThreshold::getCpuScaleThreshold))
            .orElseThrow()
            .getCpuScaleThreshold();
    final float cpuDownscaleThreshold =
        computeContext.getDownScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingCPUThreshold.class::isInstance)
            .map(ComputeAutoScalingCPUThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingCPUThreshold::getCpuScaleThreshold))
            .orElseThrow()
            .getCpuScaleThreshold();

    final float memoryUpscaleThreshold =
        computeContext.getUpScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingMemoryThreshold.class::isInstance)
            .map(ComputeAutoScalingMemoryThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingMemoryThreshold::getMemoryScaleThreshold))
            .orElseThrow()
            .getMemoryScaleThreshold();
    final float memoryDownscaleThreshold =
        computeContext.getDownScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingMemoryThreshold.class::isInstance)
            .map(ComputeAutoScalingMemoryThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingMemoryThreshold::getMemoryScaleThreshold))
            .orElseThrow()
            .getMemoryScaleThreshold();

    // test fail -- cpu and memory within range
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of(pHostname),
              HWMetricsState.WITHIN,
              HWMetricsState.WITHIN,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final AutoScalingContext beforeOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      assertFalse(beforeOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      _ndsReactiveAutoScalingIngestionSvc.onEvent(
          pingRequest, PingEvent.PING_INGESTION_COMPLETE, true);

      final AutoScalingContext afterOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      assertFalse(afterOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      assertFalse(afterOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
    }

    // Resetting Last Scale Up Attempt and Shard Contexts
    _autoScalingContextDao.setBaseComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.setAnalyticsComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.resetBaseComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());
    _autoScalingContextDao.resetAnalyticsComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());

    // test success -- cpu above and memory below
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of(pHostname),
              HWMetricsState.ABOVE,
              HWMetricsState.BELOW,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final AutoScalingContext beforeOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertFalse(beforeOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertFalse(beforeOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
      _ndsReactiveAutoScalingIngestionSvc.onEvent(
          pingRequest, PingEvent.PING_INGESTION_COMPLETE, true);

      final AutoScalingContext afterOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertTrue(afterOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertTrue(afterOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
    }

    // Resetting Last Scale Up Attempt and Shard Contexts
    _autoScalingContextDao.setBaseComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.setAnalyticsComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.resetBaseComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());
    _autoScalingContextDao.resetAnalyticsComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());

    // test success -- cpu below and memory above
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of(pHostname),
              HWMetricsState.BELOW,
              HWMetricsState.ABOVE,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final AutoScalingContext beforeOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertFalse(beforeOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertFalse(beforeOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
      _ndsReactiveAutoScalingIngestionSvc.onEvent(
          pingRequest, PingEvent.PING_INGESTION_COMPLETE, true);

      final AutoScalingContext afterOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertTrue(afterOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertTrue(afterOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
    }

    // Resetting Last Scale Up Attempt and Shard Contexts
    _autoScalingContextDao.setBaseComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.setAnalyticsComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.resetBaseComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());
    _autoScalingContextDao.resetAnalyticsComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());

    // test success -- cpu and memory above
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of(pHostname),
              HWMetricsState.ABOVE,
              HWMetricsState.ABOVE,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final AutoScalingContext beforeOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertFalse(beforeOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertFalse(beforeOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
      _ndsReactiveAutoScalingIngestionSvc.onEvent(
          pingRequest, PingEvent.PING_INGESTION_COMPLETE, true);

      final AutoScalingContext afterOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertTrue(afterOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertTrue(afterOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
    }

    // Resetting Last Scale Up Attempt and Shard Contexts
    _autoScalingContextDao.setBaseComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.setAnalyticsComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.resetBaseComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());
    _autoScalingContextDao.resetAnalyticsComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());

    // test simulated dedicated mongot is filtered -- cpu above and memory above
    {
      final PingRequest pingRequest =
          getTestComputeSimulatedDedicatedMongotPingRequest(
              List.of(pHostname),
              HWMetricsState.ABOVE,
              HWMetricsState.ABOVE,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final AutoScalingContext beforeOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertFalse(beforeOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertFalse(beforeOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
      _ndsReactiveAutoScalingIngestionSvc.onEvent(
          pingRequest, PingEvent.PING_INGESTION_COMPLETE, true);

      final AutoScalingContext afterOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      // the ping was flagged for dedicated mongot, there should be no change here
      if (pIsAnalyticsCheck) {
        assertFalse(afterOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertFalse(afterOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
    }

    // Resetting Last Scale Up Attempt and Shard Contexts
    _autoScalingContextDao.setBaseComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.setAnalyticsComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.resetBaseComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());
    _autoScalingContextDao.resetAnalyticsComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());

    // test success -- cpu below and memory below
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of(pHostname),
              HWMetricsState.BELOW,
              HWMetricsState.BELOW,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final AutoScalingContext beforeOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertFalse(beforeOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertFalse(beforeOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
      _ndsReactiveAutoScalingIngestionSvc.onEvent(
          pingRequest, PingEvent.PING_INGESTION_COMPLETE, true);

      final AutoScalingContext afterOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertTrue(afterOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertTrue(afterOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
    }

    // Resetting Last Scale Up Attempt and Shard Contexts
    _autoScalingContextDao.setBaseComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.setAnalyticsComputeLastScaleUpAttemptAndNeedsCheckForCluster(
        pCluster.getClusterDescription().getGroupId(),
        pCluster.getClusterDescription().getName(),
        new Date(0));
    _autoScalingContextDao.resetBaseComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());
    _autoScalingContextDao.resetAnalyticsComputeNeedsCheck(
        pCluster.getClusterDescription().getGroupId(), pCluster.getClusterDescription().getName());

    // test simulated dedicated mongot is filtered -- cpu below and memory below
    {
      final PingRequest pingRequest =
          getTestComputeSimulatedDedicatedMongotPingRequest(
              List.of(pHostname),
              HWMetricsState.BELOW,
              HWMetricsState.BELOW,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final AutoScalingContext beforeOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      if (pIsAnalyticsCheck) {
        assertFalse(beforeOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertFalse(beforeOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
      _ndsReactiveAutoScalingIngestionSvc.onEvent(
          pingRequest, PingEvent.PING_INGESTION_COMPLETE, true);

      final AutoScalingContext afterOnEvent =
          _autoScalingContextDao
              .find(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();
      // the ping was flagged for dedicated mongot, there should be no change here
      if (pIsAnalyticsCheck) {
        assertFalse(afterOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
      } else {
        assertFalse(afterOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      }
    }
  }

  @Test
  public void testOnEventForShardedContext() throws SvcException {
    final Pair<Cluster, String> clusterAndHostname = setUpShardedCluster();
    final Cluster cluster = clusterAndHostname.getLeft();
    final String hostname = clusterAndHostname.getRight();

    final AutoScalingContext context = testOnEventForCompute_setup(cluster, AutoScalingMode.SHARD);
    final ShardComputeAutoScalingContext computeContext =
        (ShardComputeAutoScalingContext) context.getBaseComputeContext();

    // Get min threshold as to not trigger lower threshold in the case of multiples
    final float cpuUpscaleThreshold =
        computeContext.getUpScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingCPUThreshold.class::isInstance)
            .map(ComputeAutoScalingCPUThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingCPUThreshold::getCpuScaleThreshold))
            .orElseThrow()
            .getCpuScaleThreshold();
    final float cpuDownscaleThreshold =
        computeContext.getDownScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingCPUThreshold.class::isInstance)
            .map(ComputeAutoScalingCPUThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingCPUThreshold::getCpuScaleThreshold))
            .orElseThrow()
            .getCpuScaleThreshold();

    final float memoryUpscaleThreshold =
        computeContext.getUpScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingMemoryThreshold.class::isInstance)
            .map(ComputeAutoScalingMemoryThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingMemoryThreshold::getMemoryScaleThreshold))
            .orElseThrow()
            .getMemoryScaleThreshold();
    final float memoryDownscaleThreshold =
        computeContext.getDownScalingCriterias().stream()
            .map(SingleScalingCriteria::getThresholds)
            .flatMap(Collection::stream)
            .filter(ComputeAutoScalingMemoryThreshold.class::isInstance)
            .map(ComputeAutoScalingMemoryThreshold.class::cast)
            .min(Comparator.comparing(ComputeAutoScalingMemoryThreshold::getMemoryScaleThreshold))
            .orElseThrow()
            .getMemoryScaleThreshold();

    // test fail -- cpu and memory within range
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of(hostname),
              HWMetricsState.WITHIN,
              HWMetricsState.WITHIN,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final AutoScalingContext beforeOnEvent =
          _autoScalingContextDao
              .find(
                  cluster.getClusterDescription().getGroupId(),
                  cluster.getClusterDescription().getName())
              .get();
      assertFalse(beforeOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      assertFalse(beforeOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());

      _ndsReactiveAutoScalingIngestionSvc.onEvent(
          pingRequest, PingEvent.PING_INGESTION_COMPLETE, true);
      final AutoScalingContext afterOnEvent =
          _autoScalingContextDao
              .find(
                  cluster.getClusterDescription().getGroupId(),
                  cluster.getClusterDescription().getName())
              .get();
      assertFalse(afterOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      assertFalse(afterOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());
    }

    // test success -- cpu above and memory below
    {
      final PingRequest pingRequest =
          getTestComputePingRequest(
              List.of(hostname),
              HWMetricsState.ABOVE,
              HWMetricsState.BELOW,
              cpuUpscaleThreshold,
              cpuDownscaleThreshold,
              memoryUpscaleThreshold,
              memoryDownscaleThreshold,
              true);
      final AutoScalingContext beforeOnEvent =
          _autoScalingContextDao
              .find(
                  cluster.getClusterDescription().getGroupId(),
                  cluster.getClusterDescription().getName())
              .get();

      assertFalse(beforeOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      assertFalse(beforeOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());

      _ndsReactiveAutoScalingIngestionSvc.onEvent(
          pingRequest, PingEvent.PING_INGESTION_COMPLETE, true);

      final AutoScalingContext afterOnEvent =
          _autoScalingContextDao
              .find(
                  cluster.getClusterDescription().getGroupId(),
                  cluster.getClusterDescription().getName())
              .get();
      assertTrue(afterOnEvent.getBaseComputeContext().isAnyNeedsCheckSet());
      assertFalse(afterOnEvent.getAnalyticsComputeContext().isAnyNeedsCheckSet());

      final NDSGroup group = _ndsGroupDao.find(cluster.getClusterDescription().getGroupId()).get();
      assertTrue(
          group.getNextPlanningDate().toInstant().toEpochMilli() - Instant.now().toEpochMilli()
              > NDSReactiveAutoScalingIngestionSvc.AUTO_SCALING_SHARD_CHECK_BUFFER * 1000
                  - MS_BUFFER_FOR_PLANNING_DATE);
    }
  }

  private Pair<Cluster, String> setUpShardedCluster() throws SvcException {
    final Cluster cluster = _ndsBaseTest.setupShardedCluster(_ndsGroupId);
    final ClusterDescription clusterDescription =
        _clusterDescriptionDao
            .findByName(
                cluster.getClusterDescription().getGroupId(),
                cluster.getClusterDescription().getName())
            .get();

    final ClusterDescription updatedClusterDescription =
        clusterDescription
            .copy()
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScaling.getDisabledAutoScaling().toBuilder()
                    .compute(
                        AWSComputeAutoScaling.builder()
                            .enabled(true)
                            .scaleDownEnabled(true)
                            .minInstanceSize(AWSNDSInstanceSize.M10)
                            .maxInstanceSize(AWSNDSInstanceSize.M40)
                            .build())
                    .build(),
                NodeTypeFamily.BASE)
            .setAutoScalingMode(AutoScalingMode.SHARD)
            .build();
    _clusterDescriptionDao.save(updatedClusterDescription);

    final String baseNodeHostname =
        cluster.getReplicaSets().get(0).getHardware().get(0).getHostnameForAgents().get();
    return Pair.of(cluster, baseNodeHostname);
  }

  private void saveInstanceHardwareForClusterHost(
      final ClusterDescription clusterDescription,
      final ObjectId pGroupId,
      final String pHostname) {
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterDescription.getName(), pGroupId, 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardwareModelTestFactory.getRsIdForNonConfigShard(
            clusterDescription.getDeploymentClusterName(), 0),
        true,
        false,
        clusterDescription.getReplicationSpecsWithShardData().get(0).getId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    final Hostnames hostnames = new Hostnames(pHostname);
    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        instanceId,
        false,
        InstanceHardware.FieldDefs.HOSTNAMES,
        hostnames.toDBList());
  }

  private enum HWMetricsState {
    ABOVE,
    WITHIN,
    BELOW,
    EMPTY
  }

  private PingRequestFactory.Builder getTestComputePingRequestBuilder(
      final List<String> pHostnames,
      final HWMetricsState pCPUState,
      final HWMetricsState pMemoryState,
      final float pCPUAboveThreshold,
      final float pCPUBelowThreshold,
      final float pMemoryAboveThreshold,
      final float pMemoryBelowThreshold,
      final boolean pHasRepeated) {
    final PingSource pingSource =
        IngestionTestUtils.newPingSource(_organization, _mmsGroup, new Date());

    final List<HardwareMetrics> hardwareMetrics =
        getTestComputeHardwareMetrics(
            pCPUState,
            pMemoryState,
            pCPUAboveThreshold,
            pCPUBelowThreshold,
            pMemoryAboveThreshold,
            pMemoryBelowThreshold,
            pHasRepeated);

    // passing "none" for testing purposes in the case we have an empty list since it is
    // required by AutomationPingMsg
    final PingRequestFactory.Builder builder =
        _pingRequestFactory.newPingRequestBuilder(
            new HardwareAutomationPingMsg(
                pingSource,
                hardwareMetrics,
                pHostnames.isEmpty() ? "none" : pHostnames.get(0),
                new BasicBSONObject()));
    builder.setHardwareMetrics(hardwareMetrics);
    final BasicBSONObject hostMap = new BasicBSONObject();
    pHostnames.forEach(
        hostname ->
            hostMap.append(hostname, new BasicBSONObject().append(Ping.HOST.field, hostname)));
    builder.setHosts(hostMap);
    return builder;
  }

  private PingRequest getTestComputePingRequest(
      final List<String> pHostnames,
      final HWMetricsState pCPUState,
      final HWMetricsState pMemoryState,
      final float pCPUAboveThreshold,
      final float pCPUBelowThreshold,
      final float pMemoryAboveThreshold,
      final float pMemoryBelowThreshold,
      final boolean pHasRepeated) {
    return getTestComputePingRequestBuilder(
            pHostnames,
            pCPUState,
            pMemoryState,
            pCPUAboveThreshold,
            pCPUBelowThreshold,
            pMemoryAboveThreshold,
            pMemoryBelowThreshold,
            pHasRepeated)
        .build();
  }

  private PingRequest getTestComputeSimulatedDedicatedMongotPingRequest(
      final List<String> pHostnames,
      final HWMetricsState pCPUState,
      final HWMetricsState pMemoryState,
      final float pCPUAboveThreshold,
      final float pCPUBelowThreshold,
      final float pMemoryAboveThreshold,
      final float pMemoryBelowThreshold,
      final boolean pHasRepeated) {
    final PingRequestFactory.Builder builder =
        getTestComputePingRequestBuilder(
            pHostnames,
            pCPUState,
            pMemoryState,
            pCPUAboveThreshold,
            pCPUBelowThreshold,
            pMemoryAboveThreshold,
            pMemoryBelowThreshold,
            pHasRepeated);
    builder.setIsDedicatedMongot(true);
    return builder.build();
  }

  private List<HardwareMetrics> getTestComputeHardwareMetrics(
      final HWMetricsState pCPUState,
      final HWMetricsState pMemoryState,
      final float pCPUAboveThreshold,
      final float pCPUBelowThreshold,
      final float pMemoryAboveThreshold,
      final float pMemoryBelowThreshold,
      final boolean pHasRepeated) {
    final SystemCpuMetrics cpuMetrics =
        pCPUState == HWMetricsState.EMPTY
            ? new SystemCpuMetrics(
                0L, null, null, null, null, null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, null, null, null, null, null, null, null, null,
                null, null, null)
            : new SystemCpuMetrics(
                101L,
                (long)
                    (pCPUState == HWMetricsState.ABOVE
                        ? (100 * pCPUAboveThreshold) + 1
                        : pCPUState == HWMetricsState.BELOW
                            ? (100 * pCPUBelowThreshold) - 1
                            : (100 * pCPUAboveThreshold) - 1),
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0);
    final SystemMemoryMetrics memoryMetrics =
        pMemoryState == HWMetricsState.EMPTY
            ? new SystemMemoryMetrics(
                0L, null, null, null, null, null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null)
            : new SystemMemoryMetrics(
                101L,
                100L,
                (long)
                    (pMemoryState == HWMetricsState.ABOVE
                        ? 100 - (100 * pMemoryAboveThreshold) - 1
                        : pMemoryState == HWMetricsState.BELOW
                            ? 100 - (100 * pMemoryBelowThreshold) + 1
                            : 100 - (100 * pMemoryAboveThreshold) + 1),
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L,
                0L);
    final HardwareMetrics hwMetrics =
        new HardwareMetrics(
            0,
            false,
            new Platform("irix", null, "r8000", 1L),
            cpuMetrics,
            memoryMetrics,
            null,
            null,
            Collections.emptyMap(),
            Collections.emptyMap());

    if (!pHasRepeated) {
      return List.of(hwMetrics);
    }

    final SystemCpuMetrics repeatedCPUMetrics =
        pCPUState == HWMetricsState.EMPTY
            ? new SystemCpuMetrics(
                0L, null, null, null, null, null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, null, null, null, null, null, null, null, null,
                null, null, null)
            : new SystemCpuMetrics(
                1L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0);

    final HardwareMetrics repeatedHWMetrics =
        new HardwareMetrics(
            0,
            true,
            new Platform("irix", null, "r8000", 1L),
            repeatedCPUMetrics,
            null,
            null,
            null,
            Collections.emptyMap(),
            Collections.emptyMap());

    return List.of(repeatedHWMetrics, hwMetrics);
  }

  private PingRequestFactory.Builder getTestServerlessPingRequestBuilder(
      final List<String> pHostnames, final Long pDiskSpaceUsed, final Long pDiskSpaceFree) {
    final PingSource pingSource =
        IngestionTestUtils.newPingSource(_organization, _mmsGroup, new Date());

    final List<HardwareMetrics> hardwareMetrics =
        getTestServerlessHardwareMetrics(pDiskSpaceUsed, pDiskSpaceFree);

    // passing "none" for testing purposes in the case we have an empty list since it is required by
    // AutomationPingMsg
    final PingRequestFactory.Builder builder =
        _pingRequestFactory.newPingRequestBuilder(
            new HardwareAutomationPingMsg(
                pingSource,
                hardwareMetrics,
                pHostnames.isEmpty() ? "none" : pHostnames.get(0),
                new BasicBSONObject()));
    builder.setHardwareMetrics(hardwareMetrics);
    final BasicBSONObject hostMap = new BasicBSONObject();
    pHostnames.forEach(
        hostname ->
            hostMap.append(hostname, new BasicBSONObject().append(Ping.HOST.field, hostname)));
    builder.setHosts(hostMap);
    return builder;
  }

  private PingRequest getTestServerlessPingRequest(
      final List<String> pHostnames, final Long pDiskSpaceUsed, final Long pDiskSpaceFree) {
    return getTestServerlessPingRequestBuilder(pHostnames, pDiskSpaceUsed, pDiskSpaceFree).build();
  }

  private PingRequest getTestServerlessSimulatedDedicatedMongotPingRequest(
      final List<String> pHostnames, final Long pDiskSpaceUsed, final Long pDiskSpaceFree) {
    final PingRequestFactory.Builder builder =
        getTestServerlessPingRequestBuilder(pHostnames, pDiskSpaceUsed, pDiskSpaceFree);
    builder.setIsDedicatedMongot(true);
    return builder.build();
  }

  private List<HardwareMetrics> getTestServerlessHardwareMetrics(
      final Long pDiskSpaceUsed, final Long pDiskSpaceFree) {
    final DiskPartitionMetrics diskMetrics1 =
        new DiskPartitionMetrics(0L, null, null, null, null, pDiskSpaceUsed, 0L, 0L, 0L, List.of());
    final DiskPartitionMetrics diskMetrics2 =
        new DiskPartitionMetrics(0L, null, null, null, null, 0L, pDiskSpaceFree, 0L, 0L, List.of());
    final Map<String, DiskPartitionMetrics> diskMetrics =
        Map.of("disk1", diskMetrics1, "disk2", diskMetrics2);

    return List.of(
        new HardwareMetrics(
            0,
            false,
            new Platform("irix", null, "r8000", 1L),
            null,
            null,
            null,
            null,
            Collections.emptyMap(),
            pDiskSpaceUsed != null && pDiskSpaceFree != null
                ? diskMetrics
                : Collections.emptyMap()));
  }

  private PingSource getTestPingSourceForGroup(final Group pGroup) {
    return _pingSourceFactory.newUnsampledPingSource(
        Optional.of(_organization),
        pGroup,
        TimeUtils.nowTime(),
        "127.0.0.1",
        "hostname",
        "sessionKey",
        "1.0.0",
        null,
        null,
        Host.DataSource.MONITORING_AGENT);
  }

  private ReplicaSetHardware createReplicaSetHardware(
      final ClusterDescription pClusterDescription,
      final List<Date> pLastInstanceSizeModifiedDates,
      final List<String> pHostnames,
      final int pIndex) {
    // create set
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(
            pClusterDescription.getName(), pClusterDescription.getGroupId(), pIndex);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardwareModelTestFactory.getRsIdForNonConfigShard(
            pClusterDescription.getDeploymentClusterName(), pIndex),
        true,
        false,
        pClusterDescription.getReplicationSpecsWithShardData().get(0).getId());

    final LoopingListIterator<Date> dateIterator =
        new LoopingListIterator<>(pLastInstanceSizeModifiedDates);

    // add instance hardware
    IntStream.range(0, pHostnames.size())
        .forEach(
            i -> {
              final ObjectId instanceId =
                  _replicaSetHardwareDao.addInstance(
                      replicaSetHardwareId, CloudProvider.AWS, false, i);
              _replicaSetHardwareDao.setInstanceField(
                  replicaSetHardwareId,
                  instanceId,
                  false,
                  InstanceHardware.FieldDefs.LAST_INSTANCE_SIZE_MODIFY_DATE,
                  dateIterator.next());
              final Hostnames hostnames = new Hostnames(pHostnames.get(i));
              _replicaSetHardwareDao.setInstanceField(
                  replicaSetHardwareId,
                  instanceId,
                  false,
                  InstanceHardware.FieldDefs.HOSTNAMES,
                  hostnames.toDBList());
            });

    return _replicaSetHardwareDao.findById(replicaSetHardwareId).orElseThrow();
  }
}
