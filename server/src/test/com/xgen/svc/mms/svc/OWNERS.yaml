version: 1.0.0
aliases:
  - //.github/CODEOWNERS-aliases.yml
filters:
 - "/OWNERS.yaml":
   approvers:
    - 10gen/code-review-team-intel-ii
    - ace-iam-authz
 - "/DbProfile*SvcIntTests.java":
   approvers:
    - 10gen/code-review-team-intel-ii
 - "/performanceadvisor/*":
   approvers:
    - 10gen/code-review-team-intel-ii
 - "SlowQuery*.java":
   approvers:
    - 10gen/code-review-team-intel-ii
 - "SetProfilingLevelSvcIntTests.java":
   approvers:
    - 10gen/code-review-team-intel-ii
 - "ServerlessAutoIndexingSvcIntTests.java":
   approvers:
    - 10gen/code-review-team-intel-ii
 - "AutoIndexingSvcIntTests.java":
   approvers:
    - 10gen/code-review-team-intel-ii
 - "AutoApplyIndexes*.java":
   approvers:
    - 10gen/code-review-team-intel-ii
 - "OrgMigrationSvcIntTests.java":
   approvers:
    - ace-iam-authz
 - "TeamSvcIntTests.java":
   approvers:
    - ace-iam-authz
 - "AccountClosureSvcIntTests.java":
   approvers:
     - ace-iam-authz
 - "/IngestionPipeline*IntTests.java":
   approvers:
     - 10gen/code-review-team-online-archive
 - "/JettyLoginSvcIntTests.java":
   approvers:
     - ace-iam-workload-identity
