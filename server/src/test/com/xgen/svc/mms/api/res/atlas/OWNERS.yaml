version: 1.0.0
aliases:
  - //.github/CODEOWNERS-aliases.yml
filters:
  - "/OWNERS.yaml":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-i
  - "ApiAtlasPrivateEndpointResourceIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-i
  - "ApiAtlasPeerResourceIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-i
  - "ApiAtlasWhitelistResourceIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-i
  - "/ApiAtlasEncryptionAtRestResourceIntTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-ii
  - "/BUILD.bazel":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform-ii
  - "ApiAtlasOnlineArchiveResourceIntTests.java":
    approvers:
      - 10gen/code-review-team-online-archive
  - "ApiAtlasGroupsResourceIntTests.java":
    approvers:
      - 10gen/code-review-team-iam-authz
  - "ApiAtlasSearchMigrationResourceIntTests.java":
    approvers:
      - 10gen/code-review-team-search-catalog-and-deployments
