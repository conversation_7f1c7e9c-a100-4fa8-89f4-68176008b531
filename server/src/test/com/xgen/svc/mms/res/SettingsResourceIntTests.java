package com.xgen.svc.mms.res;

import static com.xgen.cloud.appconfig._public.config.AppConfig.getInstance;
import static com.xgen.cloud.common.constants._public.model.notification.DatadogNotificationConstants.Region;
import static com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.EDITABLE_FEATURE_FLAGS;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.cloud.legacyintegration._public.model.Integration.getFullRedactedString;
import static com.xgen.cloud.legacyintegration._public.model.Integration.getRedactedString;
import static com.xgen.cloud.legacyintegration._public.model.Integration.getRedactedUrl;
import static com.xgen.cloud.user._public.model.AppUser.JSON_TIME_ZONE_DISPLAY_FIELD;
import static com.xgen.cloud.user._public.model.AppUser.JSON_TIME_ZONE_DISPLAY_SHORT_FIELD;
import static com.xgen.cloud.user._public.model.AppUser.JSON_TIME_ZONE_ID_FIELD;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsonorg.JsonOrgModule;
import com.mongodb.BasicDBObject;
import com.mongodb.ReadPreference;
import com.xgen.cloud.access.activity._public.audit.AccessAuditEvent;
import com.xgen.cloud.access.activity._public.event.AccessEvent;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.agent._public.svc.AgentApiKeySvc;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent.Type;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanType;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.constants._public.model.notification.DatadogNotificationConstants;
import com.xgen.cloud.common.constants._public.model.notification.OpsGenieNotificationConstants;
import com.xgen.cloud.common.constants._public.model.notification.PagerDutyNotificationConstants;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.date.DateFormat;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.federation._public.model.FederationSettings;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._private.dao.GroupTDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.PrometheusConfig;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.legacyintegration._public.model.DatadogIntegration;
import com.xgen.cloud.legacyintegration._public.model.HipChatIntegration;
import com.xgen.cloud.legacyintegration._public.model.MicrosoftTeamsIntegration;
import com.xgen.cloud.legacyintegration._public.model.OpsGenieIntegration;
import com.xgen.cloud.legacyintegration._public.model.PagerDutyIntegration;
import com.xgen.cloud.legacyintegration._public.model.VictorOpsIntegration;
import com.xgen.cloud.legacyintegration._public.model.WebhookIntegration;
import com.xgen.cloud.nds.aws._private.dao.AWSPrivateLinkConnectionDao;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkConnection;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkInterfaceEndpoint;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.DedicatedEndpointService;
import com.xgen.cloud.nds.module._public.util.CloudProviderRegistryUtil;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.organization._public.model.OrgPaymentStatus;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.organization.testfactories.OrgUiIpAccessListEntryFactory;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient;
import com.xgen.cloud.payments.standalone.common._public.gateway.PaymentMethodGateway;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.ProjectLandingPage;
import com.xgen.cloud.user._public.model.UserAllowList;
import com.xgen.cloud.user._public.model.UserApiKey;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseResourceTestCommon;
import com.xgen.svc.mms.model.agent.constants.AgentLogLevel;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.res.view.settings.AgentApiKeyView;
import com.xgen.svc.mms.svc.billing.OrgPlanSvc;
import com.xgen.svc.mms.svc.billing.PlanSvc;
import com.xgen.svc.mms.svc.common.GroupErrorCode;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

public class SettingsResourceIntTests extends BaseResourceTestCommon {

  private Group _group;
  private Group _ndsGroup;
  private Group _omGroup;
  private Organization _org;
  private Organization _embargoedOrg;
  private Organization _ndsOrg;
  private AppUser _user;
  private AppUser _globalOwnerAdminUser;
  private AppUser _featureFlagAdminUser;
  private AppUser _appSettingAdminUser;
  private AppUser _ndsUser;
  private AppUser _readOnlyUser;
  private AppUser _ndsReadOnlyUser;
  private AppUser _groupOwnerUser;
  private AppUser _ndsGroupOwnerUser;
  private UserDao _userDao;
  private GroupDao _groupDao;
  private GroupTDao _groupTDao;
  private NDSGroupDao _ndsGroupDao;
  private NDSGroupSvc _ndsGroupSvc;
  private AgentApiKeySvc _agentApiKeySvc;
  private AuditSvc _auditSvc;
  private OrganizationSvc _orgSvc;
  private PlanSvc _planSvc;
  private OrgPlanSvc _orgPlanSvc;
  private GroupSvc _groupSvc;
  private OrgUiIpAccessListEntryFactory _orgUiIpAccessListEntryFactory;
  private PaymentMethodClient paymentMethodClient;
  private AWSPrivateLinkConnectionDao _awsPrivateLinkConnectionDao;

  private String _slackApiToken = "slackApiToken";
  private String _slackTeamName = "slackTeamName";
  private String _slackChannelName = "slackChannelName";

  @BeforeClass
  public static void prepareEnv() {
    BaseResourceTestCommon.prepareEnv();
    CloudProviderRegistryUtil.registerAllProvider();
  }

  @AfterClass
  public static void deregisterProviders() {
    CloudProviderRegistryUtil.deregisterAllProvider();
  }

  @Before
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithStandardPlan();

    // This email address is intentionally different from the one in SettingsResourceThirdPartyTests
    // to prevent the same user from being reused across the tests, which caused flakiness.
    _user = MmsFactory.createUser(_group, "<EMAIL>");

    _groupOwnerUser =
        MmsFactory.createUserWithRoleInGroup(_group, "<EMAIL>", Role.GROUP_OWNER);
    _globalOwnerAdminUser = MmsFactory.createGlobalOwnerAdminUser(_group);
    _featureFlagAdminUser =
        MmsFactory.createUserWithGlobalRoles(
            "<EMAIL>", Role.GLOBAL_FEATURE_FLAG_ADMIN);
    _appSettingAdminUser =
        MmsFactory.createUserWithGlobalRoles(
            "<EMAIL>", Role.GLOBAL_APP_SETTING_ADMIN);
    _ndsGroup = MmsFactory.createGroupWithNDSPlan();
    _ndsGroupOwnerUser =
        MmsFactory.createUserWithRoleInGroup(
            _ndsGroup, "<EMAIL>", Role.GROUP_OWNER);
    _omGroup = MmsFactory.createGroupWithOnPremPlan();
    _ndsUser = MmsFactory.createUser(_ndsGroup, "<EMAIL>");
    _readOnlyUser =
        MmsFactory.createUserWithRoleInGroup(
            _group, "<EMAIL>", Role.GROUP_READ_ONLY);
    _ndsReadOnlyUser =
        MmsFactory.createUserWithRoleInGroup(
            _ndsGroup, "<EMAIL>", Role.GROUP_READ_ONLY);

    _agentApiKeySvc = AppConfig.getInstance(AgentApiKeySvc.class);
    _auditSvc = AppConfig.getInstance(AuditSvc.class);
    _userDao = AppConfig.getInstance(UserDao.class);
    _groupDao = AppConfig.getInstance(GroupDao.class);
    _groupSvc = AppConfig.getInstance(GroupSvc.class);
    _groupTDao = AppConfig.getInstance(GroupTDao.class);
    _ndsGroupDao = AppConfig.getInstance(NDSGroupDao.class);
    _ndsGroupSvc = AppConfig.getInstance(NDSGroupSvc.class);
    _orgUiIpAccessListEntryFactory = AppConfig.getInstance(OrgUiIpAccessListEntryFactory.class);

    _groupDao.save(_group);
    _groupDao.save(_ndsGroup);
    final BasicDBObject ndsGroup = NDSModelTestFactory.getNDSGroupAllFields(_ndsGroup.getId());
    _ndsGroupDao.saveReplicaSafe(ndsGroup);

    MmsFactory.createAutomationAgentAuditEntry(
        _group, "hostname:9090", "11.9.1.7259-1", new Date());
    MmsFactory.createAutomationAgentAuditEntry(
        _ndsGroup, "hostname:9090", "11.9.1.7259-1", new Date());

    _orgSvc = AppConfig.getInstance(OrganizationSvc.class);
    _org =
        AppConfig.getInstance(OrganizationSvc.class)
            .getOrganizationsFromGroups(Arrays.asList(_group))
            .stream()
            .findAny()
            .get();
    _ndsOrg =
        AppConfig.getInstance(OrganizationSvc.class)
            .getOrganizationsFromGroups(Arrays.asList(_ndsGroup))
            .stream()
            .findAny()
            .get();
    _embargoedOrg =
        new Organization.Builder()
            .id(ObjectId.get())
            .name("Embargoed Org")
            .paymentStatus(OrgPaymentStatus.embargoed(new Date()))
            .build();
    ;
    _orgSvc.save(_embargoedOrg);
    _planSvc = AppConfig.getInstance(PlanSvc.class);
    _orgPlanSvc = AppConfig.getInstance(OrgPlanSvc.class);

    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, _org, FeatureFlag.PROMETHEUS_INTEGRATION);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _ndsGroup, _ndsOrg, FeatureFlag.PROMETHEUS_INTEGRATION);

    paymentMethodClient = spy(getInstance(PaymentMethodClient.class));
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(PaymentMethodGateway.class),
        "paymentMethodClient",
        paymentMethodClient);
    stubPaymentMethod(_org.getId());
    stubPaymentMethod(_ndsOrg.getId());
    stubPaymentMethod(_embargoedOrg.getId());

    _awsPrivateLinkConnectionDao = AppConfig.getInstance(AWSPrivateLinkConnectionDao.class);
  }

  @Test
  public void testAddTempApiKey() {
    final Map<String, String> params = new HashMap<>();
    params.put("desc", "Test");
    final JSONObject result = doAuthedFormPost(_user, "/settings/addTempApiKey", params);
    assertNotNull(result.getString("id"));
    assertEquals("Test", result.getString(UserApiKey.DESCRIPTION_FIELD));
    assertEquals(_user.getId().toString(), result.getString(UserApiKey.USER_ID_FIELD));
    assertEquals("TEMPORARY", result.getString(UserApiKey.KEY_TYPE_FIELD));
    assertNotNull(result.getString(UserApiKey.CREATED_AT_FIELD));
    assertNotNull(result.getString(UserApiKey.EXPIRES_AT_FIELD));
  }

  @Test
  public void testUpdateBillingEmail() {
    final Map<String, String> params = new HashMap<>();
    String path = "/settings/updateBillingEmail/" + _org.getId();
    String getPath = "/settings/billingEmail/" + _org.getId();

    // assert adding a billing email
    String goodEmail = "<EMAIL>";
    params.put("emailAddress", goodEmail);
    JSONObject result = doAuthedFormPost(_globalOwnerAdminUser, path, params);
    assertEquals(goodEmail, result.getString("emailAddress"));

    JSONObject emailGetResponse = doAuthedJsonGet(_globalOwnerAdminUser, getPath);
    assertEquals(goodEmail, emailGetResponse.getString("emailAddress"));

    List<Event> billingAuditEvents1 =
        _auditSvc.findByEventTypeForOrganization(_org.getId(), Type.BILLING_EMAIL_ADDRESS_ADDED);
    assertThat(billingAuditEvents1.size(), equalTo(1));

    // assert passing the same email will be a no-op
    String sameEmail = goodEmail;
    result = doAuthedFormPost(_globalOwnerAdminUser, path, params);
    assertEquals(sameEmail, result.getString("emailAddress"));

    emailGetResponse = doAuthedJsonGet(_globalOwnerAdminUser, getPath);
    assertEquals(sameEmail, emailGetResponse.getString("emailAddress"));

    List<Event> billingAuditEvents2 =
        _auditSvc.findByEventTypeForOrganization(_org.getId(), Type.BILLING_EMAIL_ADDRESS_CHANGED);
    billingAuditEvents2.addAll(
        _auditSvc.findByEventTypeForOrganization(_org.getId(), Type.BILLING_EMAIL_ADDRESS_ADDED));
    billingAuditEvents2.addAll(
        _auditSvc.findByEventTypeForOrganization(_org.getId(), Type.BILLING_EMAIL_ADDRESS_REMOVED));
    assertEquals(
        billingAuditEvents2.size(), 1); // from the previous BILLING_EMAIL_ADDRESS_ADDED event

    // assert passing another email changes the billing email
    String newEmail = "<EMAIL>";
    params.put("emailAddress", newEmail);
    result = doAuthedFormPost(_globalOwnerAdminUser, path, params);
    assertEquals(newEmail, result.getString("emailAddress"));

    emailGetResponse = doAuthedJsonGet(_globalOwnerAdminUser, getPath);
    assertEquals(newEmail, emailGetResponse.getString("emailAddress"));

    List<Event> billingAuditEvents3 =
        _auditSvc.findByEventTypeForOrganization(_org.getId(), Type.BILLING_EMAIL_ADDRESS_CHANGED);
    assertEquals(billingAuditEvents3.size(), 1);

    // assert passing empty string unsets the billing email
    String emptyEmail = "";
    params.put("emailAddress", emptyEmail);
    result = doAuthedFormPost(_globalOwnerAdminUser, path, params);
    assertEquals("", result.getString("emailAddress"));

    emailGetResponse = doAuthedJsonGet(_globalOwnerAdminUser, getPath);
    assertTrue(emailGetResponse.isNull("emailAddress"));

    List<Event> billingAuditEvents4 =
        _auditSvc.findByEventTypeForOrganization(_org.getId(), Type.BILLING_EMAIL_ADDRESS_REMOVED);
    assertEquals(billingAuditEvents4.size(), 1);
  }

  @Test
  public void testUpdateBillingEmailBlankString() {
    final Map<String, String> params = new HashMap<>();
    String path = "/settings/updateBillingEmail/" + _org.getId();
    String getPath = "/settings/billingEmail/" + _org.getId();

    // assert adding a billing email
    String goodEmail = "<EMAIL>";
    params.put("emailAddress", goodEmail);
    JSONObject result = doAuthedFormPost(_globalOwnerAdminUser, path, params);
    assertEquals(goodEmail, result.getString("emailAddress"));

    JSONObject emailGetResponse = doAuthedJsonGet(_globalOwnerAdminUser, getPath);
    assertEquals(goodEmail, emailGetResponse.getString("emailAddress"));

    List<Event> billingAuditEvents1 =
        _auditSvc.findByEventTypeForOrganization(_org.getId(), Type.BILLING_EMAIL_ADDRESS_ADDED);
    assertThat(billingAuditEvents1.size(), equalTo(1));

    // assert passing blank string unsets the billing email
    String blankEmail = " ";
    params.put("emailAddress", blankEmail);
    result = doAuthedFormPost(_globalOwnerAdminUser, path, params);
    assertEquals("", result.getString("emailAddress"));

    emailGetResponse = doAuthedJsonGet(_globalOwnerAdminUser, getPath);
    assertTrue(emailGetResponse.isNull("emailAddress"));

    List<Event> billingAuditEvents2 =
        _auditSvc.findByEventTypeForOrganization(_org.getId(), Type.BILLING_EMAIL_ADDRESS_REMOVED);
    assertEquals(billingAuditEvents2.size(), 1);
  }

  // TODO [CLOUDP-91779]: Delete or migrate this to GPAK access list. Only being used in tests
  @Test
  public void testAddPublicApiWhitelist() {
    final HashMap<String, String> query1 = new HashMap<>();
    query1.put("ipAddress", "***********");
    final JSONObject result1 = doAuthedFormPost(_user, "/settings/addPublicApiWhitelist", query1);
    assertNotNull(result1.getString("_id"));
    assertEquals("***********", result1.getString(UserAllowList.IP_ADDRESS_FIELD));

    final HashMap<String, String> query2 = new HashMap<>();
    query2.put("ipAddress", "***********/29");
    final JSONObject result2 = doAuthedFormPost(_user, "/settings/addPublicApiWhitelist", query2);
    assertNotNull(result2.getString("_id"));
    assertEquals("***********/29", result2.getString(UserAllowList.IP_ADDRESS_FIELD));
  }

  @Test
  public void testFindAllAgentApiKeys() throws Exception {
    final AgentApiKey key1 =
        _agentApiKeySvc.generateKey(
            _group.getId(),
            _user.getId(),
            "*******",
            "Key 1",
            AgentApiKey.KeySource.USER,
            AuditInfoHelpers.fromInternal());

    Thread.sleep(100); // Ensure different created dates.

    final AgentApiKey key2 =
        _agentApiKeySvc.generateKey(
            _group.getId(),
            _user.getId(),
            "*******",
            "Key 2",
            AgentApiKey.KeySource.USER,
            AuditInfoHelpers.fromInternal());
    final JSONArray result =
        doAuthedJsonArrayGet(_readOnlyUser, "/settings/" + _group.getId() + "/agentApiKeys");
    assertEquals("Unexpected result length: " + result.toString(2), 2, result.length());

    // Expected reverse sorted order by creation date.
    final JSONObject resultKey1 = result.getJSONObject(1);
    final JSONObject resultKey2 = result.getJSONObject(0);
    assertAgentApiKeyEqualsJsonAgentApiKeyView(key1, resultKey1, true);
    assertAgentApiKeyEqualsJsonAgentApiKeyView(key2, resultKey2, true);
  }

  @Test
  public void testFindAllAgentApiKeys_PlanType() {
    try {
      doAuthedJsonArrayGet(_ndsUser, "/settings/" + _ndsGroup.getId() + "/agentApiKeys");
      fail("Expected NDS group owner to be denied Agent API key access");
    } catch (Exception e) {
      // expected
    }
  }

  @Test
  public void testCreateAgentApiKey() throws Exception {
    final Map<String, String> args = new HashMap<>();
    args.put("desc", "My key");
    try {
      doAuthedFormPost(_readOnlyUser, "/settings/" + _group.getId() + "/agentApiKeys", args);
      fail("User is not a group owner so this should have thrown an exception");
    } catch (final Exception pE) {
    }
    final JSONObject resultKey =
        doAuthedFormPost(_user, "/settings/" + _group.getId() + "/agentApiKeys", args);
    final List<AgentApiKey> keys =
        _agentApiKeySvc.getKeysForGroup(_group.getId(), ReadPreference.secondaryPreferred());
    assertEquals(1, keys.size());
    assertEquals("My key", keys.get(0).getDescription());
    assertAgentApiKeyEqualsJsonAgentApiKeyView(keys.get(0), resultKey, false);
  }

  @Test
  public void testCreateAgentApiKey_PlanType() {
    final Map<String, String> args = new HashMap<>();
    args.put("desc", "My key");
    try {
      doAuthedFormPost(_ndsUser, "/settings/" + _ndsGroup.getId() + "/agentApiKeys", args);
      fail("Expected NDS group owner to be denied Agent API key access");
    } catch (final Exception pE) {
    }
  }

  @Test
  public void testDeleteAgentApiKey() throws Exception {
    final AgentApiKey key =
        _agentApiKeySvc.generateKey(
            _group.getId(),
            _user.getId(),
            "*******",
            "Key 1",
            AgentApiKey.KeySource.USER,
            AuditInfoHelpers.fromInternal());
    final List<AgentApiKey> keys =
        _agentApiKeySvc.getKeysForGroup(_group.getId(), ReadPreference.secondaryPreferred());
    assertEquals(1, keys.size());
    key.setKey(null); // for comparison below
    assertEquals(key, keys.get(0));
    try {
      doAuthedDelete(_readOnlyUser, "/settings/" + _group.getId() + "/agentApiKeys/" + key.getId());
      fail("User is not a group owner so this should have thrown an exception");
    } catch (final Exception pE) {
    }
    doAuthedDelete(_user, "/settings/" + _group.getId() + "/agentApiKeys/" + key.getId());
    assertTrue(
        _agentApiKeySvc
            .getKeysForGroup(_group.getId(), ReadPreference.secondaryPreferred())
            .isEmpty());
  }

  @Test
  public void testDeleteAgentApiKey_PlanType() throws Exception {
    final AgentApiKey key =
        _agentApiKeySvc.generateKey(
            _ndsGroup.getId(),
            _ndsUser.getId(),
            "*******",
            "Key 1",
            AgentApiKey.KeySource.USER,
            AuditInfoHelpers.fromInternal());
    final List<AgentApiKey> keys =
        _agentApiKeySvc.getKeysForGroup(_ndsGroup.getId(), ReadPreference.secondaryPreferred());
    assertEquals(1, keys.size());
    key.setKey(null); // for comparison below
    assertEquals(key, keys.get(0));
    try {
      doAuthedDelete(_ndsUser, "/settings/" + _ndsGroup.getId() + "/agentApiKeys/" + key.getId());
      fail("Expected NDS group owner to be denied Agent API key access");
    } catch (final Exception pE) {
    }
    assertFalse(
        _agentApiKeySvc
            .getKeysForGroup(_ndsGroup.getId(), ReadPreference.secondaryPreferred())
            .isEmpty());
  }

  private void assertAgentApiKeyEqualsJsonAgentApiKeyView(
      final AgentApiKey pExpectedApiKey,
      final JSONObject pActualApiKey,
      final boolean isKeyRedacted) {
    ObjectMapper mapper = new ObjectMapper().registerModule(new JsonOrgModule());
    final AgentApiKeyView view = mapper.convertValue(pActualApiKey, AgentApiKeyView.class);
    assertEquals(pExpectedApiKey.getSource(), view.getCreatedBy());
    assertEquals(pExpectedApiKey.getCreatedIpAddress(), view.getCreatedIpAddr());
    assertEquals(Long.valueOf(pExpectedApiKey.getCreatedTime().getTime()), view.getCreatedTime());
    assertEquals(pExpectedApiKey.getCreatedUserId(), view.getCreatedUserId());
    assertEquals(pExpectedApiKey.getDescription(), view.getDesc());
    assertEquals(pExpectedApiKey.getId(), view.getId());
    if (isKeyRedacted) {
      assertEquals(AgentApiKeySvc.KEY_PREFIX + pExpectedApiKey.getLastFourChars(), view.getKey());
    } else {
      assertTrue(
          view.getKey().endsWith(pExpectedApiKey.getLastFourChars())
              && !view.getKey().startsWith("*"));
    }
  }

  // TODO [CLOUDP-91779]: Delete or migrate this to GPAK access list. Only being used in tests
  @Test
  public void testAddPublicApiWhitelistShouldReturnErrorForInvalidAddress() {
    final HashMap<String, String> query1 = new HashMap<>();
    query1.put("ipAddress", "something");
    final JSONObject result1 =
        doAuthedFormPost(_user, "/settings/addPublicApiWhitelist", query1, 400);
    assertEquals("INVALID_IP_ADDRESS_OR_CIDR_NOTATION", result1.getString("errorCode"));
    assertEquals(
        "This is neither a valid IP address nor CIDR notation: something",
        result1.getString("message"));

    final HashMap<String, String> query2 = new HashMap<>();
    query2.put("ipAddress", "192.168.1");
    final JSONObject result2 =
        doAuthedFormPost(_user, "/settings/addPublicApiWhitelist", query2, 400);
    assertEquals("INVALID_IP_ADDRESS_OR_CIDR_NOTATION", result2.getString("errorCode"));
    assertEquals(
        "This is neither a valid IP address nor CIDR notation: 192.168.1",
        result2.getString("message"));

    final HashMap<String, String> query3 = new HashMap<>();
    query3.put("ipAddress", "192.168.1/20");
    final JSONObject result3 =
        doAuthedFormPost(_user, "/settings/addPublicApiWhitelist", query3, 400);
    assertEquals("INVALID_IP_ADDRESS_OR_CIDR_NOTATION", result3.getString("errorCode"));
    assertEquals(
        "This is neither a valid IP address nor CIDR notation: 192.168.1/20",
        result3.getString("message"));
  }

  private JSONObject configureSlackSettings(
      final String apiToken,
      final String teamName,
      final String channelName,
      final int expectedStatus) {
    final String path = String.format("/settings/updateSlack/%s", _group.getId());

    // add only non-null values
    final Map<String, String> data = new HashMap<>();
    data.compute("groupSlackApiToken", (k, v) -> apiToken);
    data.compute("groupSlackTeamName", (k, v) -> teamName);
    data.compute("groupSlackChannelName", (k, v) -> channelName);
    return doAuthedFormPost(_user, path, data, expectedStatus);
  }

  private void validateGroupSlackSettings(
      final String apiToken, final String teamName, final String channelName) {
    _group = _groupDao.findById(_group.getId());
    final String updatedApiToken = StringUtils.trimToNull(_group.getRedactedSlackApiToken());
    final String updatedTeamName = StringUtils.trimToNull(_group.getSlackTeamName());
    final String updatedChannelName = StringUtils.trimToNull(_group.getSlackChannelName());

    assertEquals(getRedactedString(apiToken), updatedApiToken);
    assertEquals(teamName, updatedTeamName);
    assertEquals(channelName, updatedChannelName);
    assertEquals(teamName != null, _group.hasSlackOAuth2());
  }

  private void validateSlackSettingsResponse(
      final JSONObject response,
      final String apiToken,
      final String teamName,
      final String channelName) {

    final String updatedApiToken = response.optString("groupSlackApiToken", null);
    final String updatedTeamName = response.optString("groupSlackTeamName", null);
    final String updatedChannelName = response.optString("groupSlackChannelName", null);

    assertEquals(getRedactedString(apiToken), updatedApiToken);
    assertEquals(teamName, updatedTeamName);
    assertEquals(channelName, updatedChannelName);

    assertEquals("OK", response.getString("status"));
    assertEquals("NONE", response.getString("errorCode"));
  }

  private void validateSlackSettingsError(final JSONObject response, final String errorCode) {
    assertEquals("ERROR", response.getString("status"));
    assertEquals(errorCode, response.getString("errorCode"));
  }

  @Test
  public void testUpdateGroupSlack_legacy_success() {
    final JSONObject response =
        configureSlackSettings(_slackApiToken, null, _slackChannelName, HttpStatus.SC_OK);
    validateGroupSlackSettings(_slackApiToken, null, _slackChannelName);
    validateSlackSettingsResponse(response, _slackApiToken, null, _slackChannelName);
  }

  @Test
  public void testUpdateGroupSlack_legacyNoChannel_success() {
    final JSONObject response =
        configureSlackSettings(_slackApiToken, null, null, HttpStatus.SC_OK);
    validateGroupSlackSettings(_slackApiToken, null, null);
    validateSlackSettingsResponse(response, _slackApiToken, null, null);
  }

  @Test
  public void testUpdateGroupSlack_redacted_success() {
    // setup
    configureSlackSettings(_slackApiToken, _slackTeamName, _slackChannelName, HttpStatus.SC_OK);

    final JSONObject response =
        configureSlackSettings(
            "*********oken", _slackTeamName, _slackChannelName, HttpStatus.SC_OK);
    validateGroupSlackSettings(_slackApiToken, _slackTeamName, _slackChannelName);
    validateSlackSettingsResponse(response, _slackApiToken, _slackTeamName, _slackChannelName);
  }

  @Test
  public void testUpdateGroupSlack_oAuth2_success() {
    final JSONObject response =
        configureSlackSettings(_slackApiToken, _slackTeamName, _slackChannelName, HttpStatus.SC_OK);
    validateGroupSlackSettings(_slackApiToken, _slackTeamName, _slackChannelName);
    validateSlackSettingsResponse(response, _slackApiToken, _slackTeamName, _slackChannelName);
  }

  @Test
  public void testUpdateGroupSlack_oAuth2noChannel_success() {
    final JSONObject response =
        configureSlackSettings(_slackApiToken, _slackTeamName, null, HttpStatus.SC_OK);
    validateGroupSlackSettings(_slackApiToken, _slackTeamName, null);
    validateSlackSettingsResponse(response, _slackApiToken, _slackTeamName, null);
  }

  @Test
  public void testUpdateGroupSlack_legacy_unset() {
    configureSlackSettings(_slackApiToken, null, _slackChannelName, HttpStatus.SC_OK);
    configureSlackSettings(null, null, null, HttpStatus.SC_NO_CONTENT);
    validateGroupSlackSettings(null, null, null);
  }

  @Test
  public void testUpdateGroupSlack_noToken() {
    final JSONObject response =
        configureSlackSettings(null, null, _slackChannelName, HttpStatus.SC_BAD_REQUEST);
    validateGroupSlackSettings(null, null, null);
    validateSlackSettingsError(response, GroupErrorCode.SLACK_API_TOKEN_REQUIRED.name());
  }

  @Test
  public void testUpdateGroupSlack_existingOAuth2_noTeam() {
    configureSlackSettings(_slackApiToken, _slackTeamName, _slackChannelName, HttpStatus.SC_OK);
    final JSONObject response =
        configureSlackSettings(_slackApiToken, null, _slackChannelName, HttpStatus.SC_BAD_REQUEST);
    validateGroupSlackSettings(_slackApiToken, _slackTeamName, _slackChannelName);
    validateSlackSettingsError(response, GroupErrorCode.SLACK_TEAM_NAME_REQUIRED.name());
  }

  @Test
  public void testRemoveGroupSlack() {
    final String path = String.format("/settings/updateSlack/%s", _group.getId());
    configureSlackSettings(_slackApiToken, _slackTeamName, _slackChannelName, HttpStatus.SC_OK);
    doAuthedDelete(_user, path, HttpStatus.SC_NO_CONTENT);
    validateGroupSlackSettings(null, null, null);
  }

  @Test
  public void testUpdateDatadog_successWithoutRegion() {
    final String datadogApiKey = "********************************";
    final String redactedDatadogApiKey = getRedactedString(datadogApiKey);
    final Map<String, String> data = Map.of("datadogApiKey", datadogApiKey);

    final JSONObject response =
        doAuthedFormPost(_user, "/settings/updateDatadog/" + _group.getId(), data);
    assertEquals("NONE", response.getString("errorCode"));
    assertEquals("OK", response.getString("status"));

    _group = _groupDao.findById(_group.getId());
    assertEquals(redactedDatadogApiKey, _group.getRedactedDatadogApiKey());
    assertEquals(DatadogNotificationConstants.Region.US, _group.getDatadogRegion());
    assertEquals(redactedDatadogApiKey, response.getString("datadogApiKey"));
  }

  @Test
  public void testUpdateDatadog_successWithRegion() {
    final String datadogApiKey = "********************************";
    final String redactedDatadogApiKey = getRedactedString(datadogApiKey);
    final Map<String, String> data = Map.of("datadogApiKey", datadogApiKey, "datadogRegion", "AP1");

    final JSONObject response =
        doAuthedFormPost(_user, "/settings/updateDatadog/" + _group.getId(), data);
    assertEquals("NONE", response.getString("errorCode"));
    assertEquals("OK", response.getString("status"));

    _group = _groupDao.findById(_group.getId());
    assertEquals(getRedactedString(datadogApiKey), _group.getRedactedDatadogApiKey());
    assertEquals(redactedDatadogApiKey, response.getString("datadogApiKey"));
    assertEquals(Region.AP1, _group.getDatadogRegion());
  }

  @Test
  public void testUpdateGroupDatadog_unset() {
    final ObjectId groupId = _group.getId();
    _groupTDao.setDefaultIntegrationSettings(
        groupId, new DatadogIntegration("abcd1234", Region.US));
    final Map<String, String> emptyData = Map.of("datadogApiKey", "", "datadogRegion", "");

    doAuthedFormPost(
        _user, "/settings/updateDatadog/" + groupId, emptyData, HttpStatus.SC_NO_CONTENT);
    _group = _groupDao.findById(groupId);
    assertNull(_group.getRedactedDatadogApiKey());
    assertNull(_group.getDatadogRegion());
  }

  @Test
  public void testRemoveGroupDatadog() {
    // first, add the integration
    final ObjectId groupId = _group.getId();
    _groupTDao.setDefaultIntegrationSettings(
        groupId, new DatadogIntegration("abcd1234", Region.US));

    // then call the delete endpoint
    doAuthedDelete(_user, "/settings/updateDatadog/" + groupId, HttpStatus.SC_NO_CONTENT);
    _group = _groupDao.findById(_group.getId());
    assertNull(_group.getRedactedDatadogApiKey());
    assertNull(_group.getDatadogRegion());
  }

  @Test
  public void testUpdatePagerDuty_invalidKey() {
    final ObjectId groupId = _group.getId();
    final Map<String, String> data = Map.of("groupPagerDuty", "faroredinnayru", "region", "US");

    final JSONObject response =
        doAuthedFormPost(_user, "/settings/updatePagerDuty/" + groupId, data);
    assertEquals("INVALID_PAGER_DUTY_API_KEY", response.getString("errorCode"));
    assertEquals("ERROR", response.getString("status"));

    _group = _groupDao.findById(groupId);
    assertNull(_group.getRedactedPagerDutyServiceKey());
  }

  @Test
  public void testUpdatePagerDuty_invalidRegion() {
    final ObjectId groupId = _group.getId();
    final Map<String, String> data =
        Map.of("groupPagerDuty", "11111111111111111111111111111111", "region", "NOT A REGION");

    try {
      final JSONObject response =
          doAuthedFormPost(_user, "/settings/updatePagerDuty/" + groupId, data);
      fail();
    } catch (Exception e) {
    }

    _group = _groupDao.findById(groupId);
    assertNull(_group.getRedactedPagerDutyServiceKey());
  }

  @Test
  public void testUpdatePagerDuty_success() {
    final String pagerDutyServiceKey = "11111111111111111111111111111111";
    final String redactedPagerDutyServiceKey = getRedactedString(pagerDutyServiceKey);
    final Map<String, String> data = Map.of("groupPagerDuty", pagerDutyServiceKey, "region", "EU");

    final JSONObject response =
        doAuthedFormPost(_user, "/settings/updatePagerDuty/" + _group.getId(), data);

    _group = _groupDao.findById(_group.getId());
    assertEquals(getRedactedString(pagerDutyServiceKey), _group.getRedactedPagerDutyServiceKey());
    assertEquals(PagerDutyNotificationConstants.Region.EU, _group.getPagerDutyRegion());
    assertEquals(redactedPagerDutyServiceKey, response.getString("groupPagerDuty"));
    assertEquals("NONE", response.getString("errorCode"));
    assertEquals("OK", response.getString("status"));
  }

  @Test
  public void testUpdateGroupPagerDuty_unset() {
    final ObjectId groupId = _group.getId();
    final String pagerDutyServiceKey = "11111111111111111111111111111111";
    _groupTDao.setDefaultIntegrationSettings(
        groupId,
        new PagerDutyIntegration(pagerDutyServiceKey, PagerDutyNotificationConstants.Region.US));

    final Map<String, String> emptyData = Map.of("groupPagerDuty", "");
    doAuthedFormPost(
        _user, "/settings/updatePagerDuty/" + groupId, emptyData, HttpStatus.SC_NO_CONTENT);

    _group = _groupDao.findById(groupId);

    assertNull(_group.getRedactedPagerDutyServiceKey());
    assertNull(_group.getPagerDutyRegion());
  }

  @Test
  public void testRemoveGroupPagerDuty() {
    final ObjectId groupId = _group.getId();
    final String path = "/settings/updatePagerDuty/" + groupId;
    final String pagerDutyServiceKey = "aFakeServiceKey";
    _groupTDao.setDefaultIntegrationSettings(
        groupId,
        new PagerDutyIntegration(pagerDutyServiceKey, PagerDutyNotificationConstants.Region.EU));

    doAuthedDelete(_user, path, HttpStatus.SC_NO_CONTENT);
    _group = _groupDao.findById(_group.getId());
    assertNull(_group.getRedactedPagerDutyServiceKey());
  }

  @Test
  public void testUpdateGroupHipChat_success() {
    final ObjectId groupId = _group.getId();
    final String hipChatRoomName = "farore";
    final String hipChatNotificationToken = "din";
    final String redactedHipChatNotificationToken = getRedactedString(hipChatNotificationToken);
    final Map<String, String> data =
        Map.of(
            "groupHipChatRoomName",
            hipChatRoomName,
            "groupHipChatNotificationToken",
            hipChatNotificationToken);

    final JSONObject response = doAuthedFormPost(_user, "/settings/updateHipChat/" + groupId, data);
    assertEquals("NONE", response.getString("errorCode"));
    assertEquals("OK", response.getString("status"));

    _group = _groupDao.findById(groupId);
    assertEquals(hipChatRoomName, _group.getHipChatRoomName());
    assertEquals(redactedHipChatNotificationToken, _group.getRedactedHipChatNotificationToken());
    assertEquals(hipChatRoomName, response.getString("groupHipChatRoomName"));
    assertEquals(redactedHipChatNotificationToken, response.get("groupHipChatNotificationToken"));
  }

  @Test
  public void testUpdateGroupHipChat_missingRoom() {
    final ObjectId groupId = _group.getId();
    final Map<String, String> data =
        Map.of("groupHipChatRoomName", "", "groupHipChatNotificationToken", "din");

    final JSONObject response = doAuthedFormPost(_user, "/settings/updateHipChat/" + groupId, data);
    assertEquals("INVALID_HIPCHAT_ROOM_NAME", response.getString("errorCode"));
    assertEquals("ERROR", response.getString("status"));

    _group = _groupDao.findById(groupId);
    assertNull(_group.getHipChatRoomName());
    assertNull(_group.getRedactedHipChatNotificationToken());
  }

  @Test
  public void testUpdateGroupHipChat_missingToken() {
    final ObjectId groupId = _group.getId();
    final Map<String, String> data =
        Map.of("groupHipChatRoomName", "farore", "groupHipChatNotificationToken", "");

    final JSONObject response = doAuthedFormPost(_user, "/settings/updateHipChat/" + groupId, data);
    assertEquals("INVALID_HIPCHAT_NOTIFICATION_TOKEN", response.getString("errorCode"));
    assertEquals("ERROR", response.getString("status"));

    _group = _groupDao.findById(groupId);
    assertNull(_group.getHipChatRoomName());
    assertNull(_group.getRedactedHipChatNotificationToken());
  }

  @Test
  public void testUpdateGroupHipChat_unset() {
    final ObjectId groupId = _group.getId();
    _groupTDao.setDefaultIntegrationSettings(groupId, new HipChatIntegration("farore", "din"));

    final Map<String, String> emptyData =
        Map.of("groupHipChatRoomName", "", "groupHipChatNotificationToken", "");

    final JSONObject response =
        doAuthedFormPost(
            _user, "/settings/updateHipChat/" + groupId, emptyData, HttpStatus.SC_NO_CONTENT);

    _group = _groupDao.findById(groupId);
    assertNull("", _group.getHipChatRoomName());
    assertNull(_group.getRedactedHipChatNotificationToken());
  }

  @Test
  public void testRemoveGroupHipChat() {
    final ObjectId groupId = _group.getId();
    _groupTDao.setDefaultIntegrationSettings(groupId, new HipChatIntegration("farore", "din"));

    doAuthedDelete(_user, "/settings/updateHipChat/" + groupId, HttpStatus.SC_NO_CONTENT);

    _group = _groupDao.findById(groupId);
    assertNull(_group.getHipChatRoomName());
    assertNull(_group.getRedactedHipChatNotificationToken());
  }

  @Test
  public void testUpdateGroupOpsGenie_successWithoutRegion() {
    final ObjectId groupId = _group.getId();
    final String opsGenieApiKey = "************************************";
    final String redactedOpsGenieApiKey = getRedactedString(opsGenieApiKey);
    final Map<String, String> data = Map.of("groupOpsGenie", opsGenieApiKey);

    final JSONObject response =
        doAuthedFormPost(_user, "/settings/updateOpsGenie/" + groupId, data);
    assertEquals("NONE", response.getString("errorCode"));
    assertEquals("OK", response.getString("status"));

    _group = _groupDao.findById(groupId);
    assertEquals(redactedOpsGenieApiKey, _group.getRedactedOpsgenieApiKey());
    assertEquals(OpsGenieNotificationConstants.Region.US, _group.getOpsGenieRegion());
    assertEquals(redactedOpsGenieApiKey, response.getString("groupOpsGenie"));
  }

  @Test
  public void testUpdateGroupOpsGenie_successWithRegion() {
    final ObjectId groupId = _group.getId();
    final String opsGenieApiKey = "************************************";
    final String redactedOpsGenieApiKey = getRedactedString(opsGenieApiKey);
    final Map<String, String> data =
        Map.of("groupOpsGenie", opsGenieApiKey, "groupOpsGenieRegion", "EU");

    final JSONObject response =
        doAuthedFormPost(_user, "/settings/updateOpsGenie/" + groupId, data);
    assertEquals("NONE", response.getString("errorCode"));
    assertEquals("OK", response.getString("status"));

    _group = _groupDao.findById(groupId);
    assertEquals(getRedactedString(opsGenieApiKey), _group.getRedactedOpsgenieApiKey());
    assertEquals(OpsGenieNotificationConstants.Region.EU, _group.getOpsGenieRegion());
    assertEquals(redactedOpsGenieApiKey, response.getString("groupOpsGenie"));
    assertEquals("EU", response.getString("groupOpsGenieRegion"));
  }

  @Test
  public void testUpdateGroupOpsGenie_invalidKey() {
    final ObjectId groupId = _group.getId();
    final Map<String, String> data = Map.of("groupOpsGenie", "faroredinnayru");

    final JSONObject response =
        doAuthedFormPost(_user, "/settings/updateOpsGenie/" + groupId, data);
    assertEquals("INVALID_OPS_GENIE_KEY", response.getString("errorCode"));
    assertEquals("ERROR", response.getString("status"));

    _group = _groupDao.findById(groupId);
    assertNull(_group.getRedactedOpsgenieApiKey());
  }

  @Test
  public void testUpdateGroupOpsGenie_unset() {
    final ObjectId groupId = _group.getId();
    final String opsGenieApiKey = "************************************";
    final OpsGenieNotificationConstants.Region opsGenieRegion =
        OpsGenieNotificationConstants.Region.EU;
    _groupTDao.setDefaultIntegrationSettings(
        groupId, new OpsGenieIntegration(opsGenieApiKey, opsGenieRegion));

    final Map<String, String> emptyData = Map.of("groupOpsGenie", "", "groupOpsGenieRegion", "");
    doAuthedFormPost(
        _user, "/settings/updateOpsGenie/" + groupId, emptyData, HttpStatus.SC_NO_CONTENT);

    _group = _groupDao.findById(groupId);
    assertNull(_group.getRedactedOpsgenieApiKey());
    assertNull(_group.getOpsGenieRegion());
  }

  @Test
  public void testRemoveGroupOpsGenie() {
    final ObjectId groupId = _group.getId();
    final String opsGenieApiKey = "************************************";
    final OpsGenieNotificationConstants.Region opsGenieRegion =
        OpsGenieNotificationConstants.Region.EU;
    _groupTDao.setDefaultIntegrationSettings(
        groupId, new OpsGenieIntegration(opsGenieApiKey, opsGenieRegion));

    doAuthedDelete(_user, "/settings/updateOpsGenie/" + groupId, HttpStatus.SC_NO_CONTENT);
    _group = _groupDao.findById(groupId);
    assertNull(_group.getRedactedOpsgenieApiKey());
    assertNull(_group.getOpsGenieRegion());
  }

  @Test
  public void testUpdateNewRelicCredentials_success() {
    final ObjectId groupId = _group.getId();
    final String licenseKey = "235235fe2f2f8633f04abd2f5380b395b4d2d737";
    final String accountId = "933509";
    final String readToken = "XyjmTLN327mQyavx_CiLcyN-DG-zNOCK";
    final String writeToken = "ZV8IAe-AGF8M8kBOiZEd7l2_BkrHJtap";
    final Map<String, String> data =
        Map.of(
            Group.NEW_RELIC_LICENSE_KEY, licenseKey,
            Group.NEW_RELIC_INSIGHTS_ACCOUNT_ID, accountId,
            Group.NEW_RELIC_INSIGHTS_READ_TOKEN, readToken,
            Group.NEW_RELIC_INSIGHTS_WRITE_TOKEN, writeToken);

    doAuthedFormPost(_user, "/settings/updateNewRelic/" + groupId, data);

    _group = _groupDao.findById(groupId);
    assertEquals(licenseKey, _group.getNewRelicLicenseKey());
    assertEquals(accountId, _group.getNewRelicInsightsAccountId());
    assertEquals(readToken, _group.getNewRelicInsightsReadToken());
    assertEquals(writeToken, _group.getNewRelicInsightsWriteToken());
  }

  @Test
  public void testUpdateNewRelicCredentials_invalidData() {
    final ObjectId groupId = _group.getId();
    final String licenseKey = "235235fe2f2f8633f04abd2f5380b395b4d2d737";
    final String accountId = "";
    final String readToken = "";
    final String writeToken = "ZV8IAe-AGF8M8kBOiZEd7l2_BkrHJtap";
    final Map<String, String> data =
        Map.of(
            Group.NEW_RELIC_LICENSE_KEY, licenseKey,
            Group.NEW_RELIC_INSIGHTS_ACCOUNT_ID, accountId,
            Group.NEW_RELIC_INSIGHTS_READ_TOKEN, readToken,
            Group.NEW_RELIC_INSIGHTS_WRITE_TOKEN, writeToken);

    try {
      doAuthedFormPost(_user, "/settings/updateNewRelic/" + groupId, data);
      fail();
    } catch (Exception e) {
      _group = _groupDao.findById(groupId);
      assertNull(_group.getNewRelicLicenseKey());
      assertNull(_group.getNewRelicInsightsAccountId());
      assertNull(_group.getNewRelicInsightsReadToken());
      assertNull(_group.getNewRelicInsightsWriteToken());
    }
  }

  @Test
  public void testUpdateNewRelicCredentials_unset() {
    final ObjectId groupId = _group.getId();
    final String licenseKey = "235235fe2f2f8633f04abd2f5380b395b4d2d737";
    final String accountId = "933509";
    final String readToken = "XyjmTLN327mQyavx_CiLcyN-DG-zNOCK";
    final String writeToken = "ZV8IAe-AGF8M8kBOiZEd7l2_BkrHJtap";
    _groupDao.setDefaultNewRelicSettings(groupId, licenseKey, accountId, readToken, writeToken);

    final Map<String, String> emptyData =
        Map.of(
            Group.NEW_RELIC_LICENSE_KEY, "",
            Group.NEW_RELIC_INSIGHTS_ACCOUNT_ID, "",
            Group.NEW_RELIC_INSIGHTS_READ_TOKEN, "",
            Group.NEW_RELIC_INSIGHTS_WRITE_TOKEN, "");
    doAuthedFormPost(_user, "/settings/updateNewRelic/" + groupId, emptyData);

    _group = _groupDao.findById(groupId);
    assertNull(_group.getNewRelicLicenseKey());
    assertNull(_group.getNewRelicInsightsAccountId());
    assertNull(_group.getNewRelicInsightsReadToken());
    assertNull(_group.getNewRelicInsightsWriteToken());
  }

  @Test
  public void testDeleteNewRelicSettings() {
    final ObjectId groupId = _group.getId();
    final String licenseKey = "235235fe2f2f8633f04abd2f5380b395b4d2d737";
    final String accountId = "933509";
    final String readToken = "XyjmTLN327mQyavx_CiLcyN-DG-zNOCK";
    final String writeToken = "ZV8IAe-AGF8M8kBOiZEd7l2_BkrHJtap";
    _groupDao.setDefaultNewRelicSettings(groupId, licenseKey, accountId, readToken, writeToken);

    doAuthedDelete(_user, "/settings/updateNewRelic/" + groupId, HttpStatus.SC_NO_CONTENT);
    _group = _groupDao.findById(_group.getId());
    assertNull(_group.getNewRelicLicenseKey());
    assertNull(_group.getNewRelicInsightsAccountId());
    assertNull(_group.getNewRelicInsightsReadToken());
    assertNull(_group.getNewRelicInsightsWriteToken());
  }

  @Test
  public void testUpdateGroupVictorOps() {
    // test if we can update the default VictorOps key
    final Map<String, String> data1 =
        Map.of(
            "groupVictorOpsApiKey",
            "************************************",
            "groupVictorOpsRoutingKey",
            "Testing Routing Key");

    final JSONObject res1 =
        doAuthedFormPost(_user, "/settings/updateVictorOps/" + _group.getId(), data1);
    assertEquals("NONE", res1.getString("errorCode"));
    assertEquals("OK", res1.getString("status"));
    assertEquals(
        getRedactedString(data1.get("groupVictorOpsApiKey")),
        res1.getString("groupVictorOpsApiKey"));
    assertEquals(data1.get("groupVictorOpsRoutingKey"), res1.getString("groupVictorOpsRoutingKey"));

    // test if we will get error if the api key format is incorrect
    final Map<String, String> data2 =
        Map.of(
            "groupVictorOpsApiKey",
            "000000000000000000000000000000000000",
            "groupVictorOpsRoutingKey",
            "Testing Routing Key");

    final JSONObject res2 =
        doAuthedFormPost(_user, "/settings/updateVictorOps/" + _group.getId(), data2);
    assertEquals("INVALID_VICTOR_OPS_CONFIG", res2.getString("errorCode"));
    assertEquals("ERROR", res2.getString("status"));

    // test if we will get error if the api key is empty and the routing key is not
    final Map<String, String> data3 = Map.of("groupVictorOpsRoutingKey", "Testing Routing Key");

    final JSONObject res3 =
        doAuthedFormPost(_user, "/settings/updateVictorOps/" + _group.getId(), data3);
    assertEquals("INVALID_VICTOR_OPS_CONFIG", res3.getString("errorCode"));
    assertEquals("ERROR", res3.getString("status"));

    // test if routing key can be empty
    final Map<String, String> data4 =
        Map.of(
            "groupVictorOpsApiKey",
            "************************************",
            "groupVictorOpsRoutingKey",
            "");

    final JSONObject res4 =
        doAuthedFormPost(_user, "/settings/updateVictorOps/" + _group.getId(), data4);
    assertEquals("NONE", res4.getString("errorCode"));
    assertEquals("OK", res4.getString("status"));
    assertEquals(
        getRedactedString(data4.get("groupVictorOpsApiKey")),
        res4.getString("groupVictorOpsApiKey"));
    assertEquals(data4.get("groupVictorOpsRoutingKey"), res4.getString("groupVictorOpsRoutingKey"));
  }

  @Test
  public void testUpdateGroupVictorOps_unset() {
    final ObjectId groupId = _group.getId();
    final String victorOpsApiKey = "************************************";
    final String victorOpsRoutingKey = "Testing Routing Key";
    _groupTDao.setDefaultIntegrationSettings(
        groupId, new VictorOpsIntegration(victorOpsApiKey, victorOpsRoutingKey));

    final Map<String, String> data =
        Map.of("groupVictorOpsApiKey", "", "groupVictorOpsRoutingKey", "");

    final JSONObject response =
        doAuthedFormPost(
            _user, "/settings/updateVictorOps/" + groupId, data, HttpStatus.SC_NO_CONTENT);
    _group = _groupDao.findById(groupId);

    assertNull(_group.getRedactedVictorOpsApiKey());
    assertNull(_group.getVictorOpsRoutingKey());
  }

  @Test
  public void testRemoveGroupVictorOps() {
    final ObjectId groupId = _group.getId();
    final String victorOpsApiKey = "************************************";
    final String victorOpsRoutingKey = "Testing Routing Key";
    _groupTDao.setDefaultIntegrationSettings(
        groupId, new VictorOpsIntegration(victorOpsApiKey, victorOpsRoutingKey));

    doAuthedDelete(_user, "/settings/updateVictorOps/" + groupId, HttpStatus.SC_NO_CONTENT);
    _group = _groupDao.findById(_group.getId());
    assertNull(_group.getRedactedVictorOpsApiKey());
    assertNull(_group.getVictorOpsRoutingKey());
  }

  @Test
  public void updateGroupWebhook() {
    final ObjectId groupId = _group.getId();
    final String webhookUrl = "http://web.hook";
    final String redactedWebhookUrl = getRedactedUrl(webhookUrl);
    final String webhookSecret = "i lied to my dentist about flossing";
    final String redactedWebhookSecret = getFullRedactedString(webhookSecret);
    final Map<String, String> data =
        Map.of("webhookUrl", webhookUrl, "webhookSecret", webhookSecret);

    final JSONObject response = doAuthedFormPost(_user, "/settings/updateWebhook/" + groupId, data);

    _group = _groupDao.findById(groupId);
    assertEquals(redactedWebhookUrl, _group.getRedactedWebhookUrl());
    assertEquals(redactedWebhookSecret, _group.getRedactedWebhookSecret());
    assertEquals(redactedWebhookUrl, response.getString("webhookUrl"));
    assertEquals(redactedWebhookSecret, response.getString("webhookSecret"));
    assertEquals("NONE", response.getString("errorCode"));
    assertEquals("OK", response.getString("status"));
  }

  @Test
  public void testUpdateGroupWebhook_invalidWebhookUrl() {
    final ObjectId groupId = _group.getId();
    final Map<String, String> data = Map.of("webhookUrl", "", "webhookSecret", "");

    final JSONObject response = doAuthedFormPost(_user, "/settings/updateWebhook/" + groupId, data);

    assertEquals("INVALID_WEBHOOK_URL", response.getString("errorCode"));
    assertEquals("ERROR", response.getString("status"));
  }

  @Test
  public void deleteGroupWebhook() {
    final ObjectId groupId = _group.getId();
    final String webhookUrl = "http://web.hook";
    final String webhookSecret = "i cheated on the sats";
    _groupTDao.setDefaultIntegrationSettings(
        groupId, new WebhookIntegration(webhookUrl, webhookSecret));

    doAuthedDelete(_user, "/settings/updateWebhook/" + groupId, HttpStatus.SC_NO_CONTENT);
    _group = _groupDao.findById(_group.getId());
    assertNull(_group.getRedactedWebhookUrl());
    assertNull(_group.getRedactedWebhookSecret());
  }

  @Test
  public void testUpdateGroupMicrosoftTeams_invalidWebhookUrl() {
    final ObjectId groupId = _group.getId();
    final String microsoftTeamsWebhookUrl = "afakewebhookurl";
    final Map<String, String> data = Map.of("microsoftTeamsWebhookUrl", microsoftTeamsWebhookUrl);

    final JSONObject response =
        doAuthedFormPost(_user, "/settings/updateMicrosoftTeams/" + groupId, data);

    assertEquals("INVALID_MICROSOFT_TEAMS_WEBHOOK_URL", response.getString("errorCode"));
    assertEquals("ERROR", response.getString("status"));

    _group = _groupDao.findById(groupId);
    assertNull(_group.getRedactedMicrosoftTeamsWebhookUrl());
  }

  @Test
  public void testUpdateGroupMicrosoftTeams_success() {
    final ObjectId groupId = _group.getId();
    final String microsoftTeamsWebhookUrl = "https://www.microsoft.com";
    final String redactedMicrosoftTeamsWebhookUrl = getRedactedUrl(microsoftTeamsWebhookUrl);
    final Map<String, String> data = Map.of("microsoftTeamsWebhookUrl", microsoftTeamsWebhookUrl);
    final JSONObject response =
        doAuthedFormPost(_user, "/settings/updateMicrosoftTeams/" + groupId, data);

    _group = _groupDao.findById(_group.getId());
    assertEquals(redactedMicrosoftTeamsWebhookUrl, _group.getRedactedMicrosoftTeamsWebhookUrl());
    assertEquals(redactedMicrosoftTeamsWebhookUrl, response.getString("microsoftTeamsWebhookUrl"));
    assertEquals("NONE", response.getString("errorCode"));
    assertEquals("OK", response.getString("status"));
  }

  @Test
  public void testUpdateGroupMicrosoftTeams_unset() {
    final ObjectId groupId = _group.getId();
    final String microsoftTeamsWebhookUrl = "https://www.microsoft.com";
    _groupTDao.setDefaultIntegrationSettings(
        groupId, new MicrosoftTeamsIntegration(microsoftTeamsWebhookUrl));

    final Map<String, String> emptyData = Map.of("microsoftTeamsWebhookUrl", "");
    doAuthedFormPost(
        _user, "/settings/updateMicrosoftTeams/" + groupId, emptyData, HttpStatus.SC_NO_CONTENT);

    _group = _groupDao.findById(groupId);

    assertNull(_group.getRedactedMicrosoftTeamsWebhookUrl());
  }

  @Test
  public void testRemoveGroupMicrosoftTeams() {
    final ObjectId groupId = _group.getId();
    final String path = "/settings/updateMicrosoftTeams/" + groupId;
    final String microsoftTeamsWebhookUrl = "https://www.microsoft.com";
    _groupTDao.setDefaultIntegrationSettings(
        groupId, new MicrosoftTeamsIntegration(microsoftTeamsWebhookUrl));

    doAuthedDelete(_user, path, HttpStatus.SC_NO_CONTENT);
    _group = _groupDao.findById(_group.getId());
    assertNull(_group.getRedactedMicrosoftTeamsWebhookUrl());
  }

  @Test
  public void testGetFeatureFlag() {
    testGetFeatureFlag(_globalOwnerAdminUser);
    testGetFeatureFlag(_featureFlagAdminUser);
  }

  private void testGetFeatureFlag(final AppUser pAppUser) {
    final JSONObject response =
        doAuthedJsonGet(pAppUser, "/settings/featureFlags/orgs/" + _org.getId());
    final String firstFeature = response.getJSONArray("orgControlledFeatureFlags").getString(0);

    assertNotNull(firstFeature);
  }

  @Test
  public void testGetFeatureFlagGroup() {
    testGetFeatureFlagGroup(_globalOwnerAdminUser);
    testGetFeatureFlagGroup(_featureFlagAdminUser);
  }

  private void testGetFeatureFlagGroup(final AppUser pAppUser) {
    final List<Object> groupStandardControlledFeatureFlags =
        doAuthedJsonGet(pAppUser, "/settings/featureFlags/groups/" + _group.getId())
            .getJSONArray("groupControlledFeatureFlags")
            .toList();

    assertTrue(
        groupStandardControlledFeatureFlags.stream()
            .map(s -> (String) s)
            .anyMatch(s -> s.equals("ATLAS_ALLOW_DEPRECATED_VERSIONS")));
    // ATLAS_ALLOW_DEPRECATED_VERSIONS is available for all plans

    assertFalse(
        groupStandardControlledFeatureFlags.stream()
            .map(s -> (String) s)
            .anyMatch(s -> s.equals("AUTOMATION_MONGO_DEVELOPMENT_VERSIONS")));
    // AUTOMATION_MONGO_DEVELOPMENT_VERSIONS is only available for NDS plans

    final List<Object> ndsGroupControlledFeatureFlags =
        doAuthedJsonGet(pAppUser, "/settings/featureFlags/groups/" + _ndsGroup.getId())
            .getJSONArray("groupControlledFeatureFlags")
            .toList();

    assertTrue(
        ndsGroupControlledFeatureFlags.stream()
            .map(s -> (String) s)
            .anyMatch(s -> s.equals("ATLAS_ALLOW_DEPRECATED_VERSIONS")));
    // ATLAS_ALLOW_DEPRECATED_VERSIONS is available for all plans

    assertTrue(
        ndsGroupControlledFeatureFlags.stream()
            .map(s -> (String) s)
            .anyMatch(s -> s.equals("AUTOMATION_MONGO_DEVELOPMENT_VERSIONS")));
    // AUTOMATION_MONGO_DEVELOPMENT_VERSIONS is only available for NDS plans

    final List<Object> omGroupControlledFeatureFlags =
        doAuthedJsonGet(pAppUser, "/settings/featureFlags/groups/" + _omGroup.getId())
            .getJSONArray("groupControlledFeatureFlags")
            .toList();

    assertTrue(
        omGroupControlledFeatureFlags.stream()
            .map(s -> (String) s)
            .anyMatch(s -> s.equals("ATLAS_ALLOW_DEPRECATED_VERSIONS")));
    // ATLAS_ALLOW_DEPRECATED_VERSIONS is available for all plans

    assertFalse(
        omGroupControlledFeatureFlags.stream()
            .map(s -> (String) s)
            .anyMatch(s -> s.equals("AUTOMATION_MONGO_DEVELOPMENT_VERSIONS")));
    // AUTOMATION_MONGO_DEVELOPMENT_VERSIONS is only available for NDS plans
  }

  @Test
  public void testSetFeatureFlag() {
    testSetFeatureFlag(_globalOwnerAdminUser);
    testSetFeatureFlag(_featureFlagAdminUser);
  }

  // This test uses the ENABLE_SCRAM_SHA256_AUTH feature flag because it is a
  // group-scoped, non-Config Service Feature Flag that is not mapped to a Plan Type
  private void testSetFeatureFlag(final AppUser pAppUser) {
    final Date now = new Date();
    final AppSettings settings = AppConfig.getInstance(AppSettings.class);
    final HashMap<String, String> formParams = new HashMap<>();
    formParams.put("value", "true");
    // test to make sure you can't set a feature flag as read only
    try {
      doAuthedFormPost(
          _readOnlyUser,
          "settings/featureFlag/" + _group.getId() + "/ENABLE_SCRAM_SHA256_AUTH",
          formParams,
          HttpStatus.SC_FORBIDDEN);
    } catch (IllegalStateException e) {
      assertEquals(
          false,
          isFeatureFlagEnabled(FeatureFlag.ENABLE_SCRAM_SHA256_AUTH, settings, _org, _group));
    }

    List<Event> events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(0, events.size());

    // test to make sure you can't set a feature flag as group owner
    try {
      doAuthedFormPost(
          _groupOwnerUser,
          "settings/featureFlag/" + _group.getId() + "/ENABLE_SCRAM_SHA256_AUTH",
          formParams,
          HttpStatus.SC_FORBIDDEN);
    } catch (IllegalStateException e) {
      assertEquals(
          false,
          isFeatureFlagEnabled(FeatureFlag.ENABLE_SCRAM_SHA256_AUTH, settings, _org, _group));
    }

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(0, events.size());

    // test setting feature flag
    doAuthedFormPost(
        pAppUser,
        "settings/featureFlag/" + _group.getId() + "/ENABLE_SCRAM_SHA256_AUTH",
        formParams);
    final JSONObject paramsResult1 =
        doAuthedJsonGet(_readOnlyUser, "/v2/" + _group.getId() + "/params");
    final Optional<Object> featureFlag =
        paramsResult1
            .getJSONObject("featureFlags")
            .getJSONArray("groupEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> ((String) s).equals("ENABLE_SCRAM_SHA256_AUTH"))
            .findFirst();
    assertTrue(featureFlag.isPresent());

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(1, events.size());

    // test to be sure sending the same request twice is a no-op
    doAuthedFormPost(
        pAppUser,
        "settings/featureFlag/" + _group.getId() + "/ENABLE_SCRAM_SHA256_AUTH",
        formParams);
    final JSONObject paramsResult2 =
        doAuthedJsonGet(_readOnlyUser, "/v2/" + _group.getId() + "/params");
    final Optional<Object> featureFlag2 =
        paramsResult2
            .getJSONObject("featureFlags")
            .getJSONArray("groupEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> ((String) s).equals("ENABLE_SCRAM_SHA256_AUTH"))
            .findFirst();
    assertTrue(featureFlag2.isPresent());

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(2, events.size());

    // test turning off flag
    formParams.put("value", "false");
    doAuthedFormPost(
        pAppUser,
        "settings/featureFlag/" + _group.getId() + "/ENABLE_SCRAM_SHA256_AUTH",
        formParams);
    final JSONObject paramsResult3 =
        doAuthedJsonGet(_readOnlyUser, "/v2/" + _group.getId() + "/params");
    final Optional<Object> featureFlag3 =
        paramsResult3
            .getJSONObject("featureFlags")
            .getJSONArray("groupEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> ((String) s).equals("ENABLE_SCRAM_SHA256_AUTH"))
            .findFirst();
    assertFalse(featureFlag3.isPresent());

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(3, events.size());
  }

  @Test
  public void testSetEditableFeatureFlag() throws Exception {
    assertEquals(11, EDITABLE_FEATURE_FLAGS.size());
    for (FeatureFlag featureFlag : EDITABLE_FEATURE_FLAGS) {
      testSetEditableFeatureFlag(featureFlag);
    }
  }

  public void testSetEditableFeatureFlag(final FeatureFlag pFeatureFlag) throws Exception {
    final Date now = new Date();

    final AppSettings settings = AppConfig.getInstance(AppSettings.class);
    final HashMap<String, String> formParams = new HashMap<>();
    formParams.put("value", "true");
    // test to make sure you can't set a feature flag as read only
    try {
      doAuthedFormPost(
          _readOnlyUser,
          "settings/editableFeatureFlag/" + _group.getId() + "/PROFILER_V3",
          formParams,
          HttpStatus.SC_FORBIDDEN);
    } catch (IllegalStateException e) {
      assertEquals(false, isFeatureFlagEnabled(FeatureFlag.PROFILER_V3, settings, _org, _group));
    }

    List<Event> events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(0, events.size());

    // test to make sure you can't set an arbitrary feature flag as group owner
    try {
      doAuthedFormPost(
          _groupOwnerUser,
          "settings/editableFeatureFlag/" + _group.getId() + "/PROFILER_V3",
          formParams,
          HttpStatus.SC_INTERNAL_SERVER_ERROR);
    } catch (IllegalStateException e) {
      assertEquals(false, isFeatureFlagEnabled(FeatureFlag.PROFILER_V3, settings, _org, _group));
    }

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(1, events.size());
    assertEquals(
        ((AccessAuditEvent) events.get(0)).getFeatureFlag(), FeatureFlag.PROFILER_V3.name());

    final OrgPlan standardPlan = _orgPlanSvc.getOrgPlanForDate(_org.getId(), new Date());
    final OrgPlan premiumPlan = new OrgPlan.Builder().planType(PlanType.PREMIUM).build();
    _planSvc.updateCloudPlan(_org, premiumPlan, List.of(), new Date(), false, null);

    final Group group =
        (pFeatureFlag.equals(FeatureFlag.ATLAS_HIDE_QUICK_ACCESS_PAGE)
                || pFeatureFlag.equals(FeatureFlag.MANAGED_SLOW_MS)
                || pFeatureFlag.equals(FeatureFlag.CUSTOMER_QUERY_SHAPE_INSIGHTS_TOGGLE)
                || pFeatureFlag.equals(FeatureFlag.ENABLE_DATA_EXPLORER_GEN_AI_FEATURES)
                || pFeatureFlag.equals(
                    FeatureFlag.ENABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING)
                || pFeatureFlag.equals(
                    FeatureFlag.DISABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING)
                || pFeatureFlag.equals(
                    FeatureFlag.DATA_EXPLORER_COMPASS_WEB_USER_CONTROLLED_DISABLE))
            ? _ndsGroup
            : _group;
    final AppUser ownerUser = group == _ndsGroup ? _ndsGroupOwnerUser : _groupOwnerUser;
    final AppUser readUser = group == _ndsGroup ? _ndsReadOnlyUser : _readOnlyUser;

    formParams.put("value", "true");
    // test to make sure you can change an editable feature flag as a group owner
    doAuthedFormPost(
        ownerUser,
        "settings/editableFeatureFlag/" + group.getId() + "/" + pFeatureFlag.name(),
        formParams);
    final JSONObject paramsResult1 = doAuthedJsonGet(readUser, "/v2/" + group.getId() + "/params");
    final Optional<Object> featureFlagResult =
        paramsResult1
            .getJSONObject("featureFlags")
            .getJSONArray("groupEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> FeatureFlag.valueOf((String) s).equals(pFeatureFlag))
            .findFirst();
    assertTrue(featureFlagResult.isPresent());

    // test to make sure setting the same feature flag twice is a no op
    doAuthedFormPost(
        ownerUser,
        "settings/editableFeatureFlag/" + group.getId() + "/" + pFeatureFlag.name(),
        formParams);
    final JSONObject paramsResult2 = doAuthedJsonGet(readUser, "/v2/" + group.getId() + "/params");
    final Optional<Object> featureFlagResult2 =
        paramsResult2
            .getJSONObject("featureFlags")
            .getJSONArray("groupEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> FeatureFlag.valueOf((String) s).equals(pFeatureFlag))
            .findFirst();
    assertTrue(featureFlagResult2.isPresent());

    // test to make sure you can turn the editable feature flag back off as a group owner
    formParams.put("value", "false");
    doAuthedFormPost(
        ownerUser,
        "settings/editableFeatureFlag/" + group.getId() + "/" + pFeatureFlag.name(),
        formParams);
    final JSONObject paramsResult3 = doAuthedJsonGet(readUser, "/v2/" + group.getId() + "/params");
    final Optional<Object> featureFlagResult3 =
        paramsResult3
            .getJSONObject("featureFlags")
            .getJSONArray("groupEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> FeatureFlag.valueOf((String) s).equals(pFeatureFlag))
            .findFirst();
    assertFalse(
        "Ensure feature flag " + pFeatureFlag.name() + " can be disabled as an editable flag",
        featureFlagResult3.isPresent());

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(4, events.size());

    // test audit also happens for global admin user
    try {
      doAuthedFormPost(
          _globalOwnerAdminUser,
          "settings/editableFeatureFlag/" + _group.getId() + "/PROFILER_V3",
          formParams,
          HttpStatus.SC_INTERNAL_SERVER_ERROR);
    } catch (IllegalStateException e) {
    }

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(5, events.size());

    // test to make sure you can't turn on a feature flag that has been disabled for the plan type
    // on this endpoint
    _planSvc.updateCloudPlan(_org, standardPlan, List.of(), new Date(), false, null);
    formParams.put("value", "true");
    try {
      doAuthedFormPost(
          _groupOwnerUser,
          "settings/editableFeatureFlag/" + _group.getId() + "/PERFORMANCE_ADVISOR",
          formParams,
          HttpStatus.SC_INTERNAL_SERVER_ERROR);
    } catch (IllegalStateException e) {
      assertEquals(false, isFeatureFlagEnabled(FeatureFlag.PROFILER_V3, settings, _org, _group));
    }
    final JSONObject paramsResult4 =
        doAuthedJsonGet(_readOnlyUser, "/v2/" + _group.getId() + "/params");
    final Optional<Object> featureFlagResult4 =
        paramsResult4
            .getJSONObject("featureFlags")
            .getJSONArray("groupEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> FeatureFlag.valueOf((String) s).equals(FeatureFlag.PERFORMANCE_ADVISOR))
            .findFirst();
    assertFalse(featureFlagResult4.isPresent());
  }

  @Test
  public void testSetOrgFeatureFlag_GlobalOwner() {
    testSetOrgFeatureFlag(_globalOwnerAdminUser);
  }

  @Test
  public void testSetOrgFeatureFlag_FeatureFlagAdmin() {
    testSetOrgFeatureFlag(_featureFlagAdminUser);
  }

  // This test uses the UI_ACCESS_LIST feature flag because it is a Scope.Organization
  // non-Config Service Feature flag
  private void testSetOrgFeatureFlag(final AppUser pAppUser) {
    final Date now = new Date();
    final HashMap<String, String> formParams = new HashMap<>();
    formParams.put("value", "true");
    // test to make sure you can't set a feature flag as read only
    doAuthedFormPost(
        _readOnlyUser,
        "settings/orgfeatureFlag/" + _org.getId() + "/UI_ACCESS_LIST",
        formParams,
        HttpStatus.SC_FORBIDDEN);
    final JSONObject failureResult1 =
        doAuthedJsonGet(_readOnlyUser, "/v2/" + _group.getId() + "/params");
    final Optional<Object> featureFlagFailure1 =
        failureResult1
            .getJSONObject("featureFlags")
            .getJSONArray("orgEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> ((String) s).equals("UI_ACCESS_LIST"))
            .findFirst();
    assertFalse(featureFlagFailure1.isPresent());

    List<Event> events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(0, events.size());

    // test to make sure you can't set a feature flag as org owner
    doAuthedFormPost(
        _groupOwnerUser,
        "settings/orgfeatureFlag/" + _org.getId() + "/UI_ACCESS_LIST",
        formParams,
        HttpStatus.SC_FORBIDDEN);
    final JSONObject failureResult2 =
        doAuthedJsonGet(_readOnlyUser, "/v2/" + _group.getId() + "/params");
    final Optional<Object> featureFlagFailure2 =
        failureResult2
            .getJSONObject("featureFlags")
            .getJSONArray("orgEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> ((String) s).equals("UI_ACCESS_LIST"))
            .findFirst();
    assertFalse(featureFlagFailure2.isPresent());

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(0, events.size());

    // test to make sure you can change a feature flag as a global admin
    doAuthedFormPost(
        pAppUser, "settings/orgfeatureFlag/" + _org.getId() + "/UI_ACCESS_LIST", formParams);
    final JSONObject paramsResult1 =
        doAuthedJsonGet(_readOnlyUser, "/v2/" + _group.getId() + "/params");
    final Optional<Object> featureFlag =
        paramsResult1
            .getJSONObject("featureFlags")
            .getJSONArray("orgEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> ((String) s).equals("UI_ACCESS_LIST"))
            .findFirst();
    assertTrue(featureFlag.isPresent());

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(1, events.size());

    // test to be sure sending the same request twice is a no-op
    doAuthedFormPost(
        pAppUser, "settings/orgfeatureFlag/" + _org.getId() + "/UI_ACCESS_LIST", formParams);
    final JSONObject paramsResult2 =
        doAuthedJsonGet(_readOnlyUser, "/v2/" + _group.getId() + "/params");
    final Optional<Object> featureFlag2 =
        paramsResult2
            .getJSONObject("featureFlags")
            .getJSONArray("orgEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> ((String) s).equals("UI_ACCESS_LIST"))
            .findFirst();
    assertTrue(featureFlag2.isPresent());

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(2, events.size());

    // test to make sure you can turn the feature flag back off as a global admin
    formParams.put("value", "false");
    doAuthedFormPost(
        pAppUser, "settings/orgfeatureFlag/" + _org.getId() + "/UI_ACCESS_LIST", formParams);
    final JSONObject paramsResult3 =
        doAuthedJsonGet(_readOnlyUser, "/v2/" + _group.getId() + "/params");
    final Optional<Object> featureFlag3 =
        paramsResult3
            .getJSONObject("featureFlags")
            .getJSONArray("orgEnabledFeatureFlags")
            .toList()
            .stream()
            .filter(s -> ((String) s).equals("UI_ACCESS_LIST"))
            .findFirst();
    assertFalse(featureFlag3.isPresent());

    events = _auditSvc.findByDate(AccessEvent.Type.TOGGLEABLE_FEATURE_FLAG, now);
    assertEquals(3, events.size());
  }

  @Test
  public void testGetOrgInformation_forOrgWithIpAccessListRestriction() {
    AppUser user = MmsFactory.createUserInOrg(_org, getUniquifier() + "@test.com");
    MmsFactory.addUserWithRoleInOrganization(_embargoedOrg, user, Role.ORG_MEMBER);
    setUpIpAccessListForOrg(_org.getId(), List.of("*******"));

    final JSONObject response = doAuthedJsonGet(user, "/settings/orgs");
    final JSONArray orgs = response.getJSONArray("orgs");
    final JSONObject responseOrg = orgs.getJSONObject(0);

    assertEquals(1, orgs.length());
    assertEquals(_org.getId().toString(), responseOrg.optString("id", null));

    assertNotNull(responseOrg.optString("roles", null));
    assertNotNull(responseOrg.optString("name", null));
    assertNotNull(responseOrg.optString("planType", null));
    assertNotNull(responseOrg.optString("numOwners", null));
    assertNotNull(responseOrg.optString("multiFactorAuthRequired", null));
    assertNotNull(responseOrg.optString("hasVercelIntegration", null));
    assertNotNull(responseOrg.optString("hasDataApi", null));
    assertFalse(responseOrg.getBoolean("isFineGrainedAuthEnabled"));
    assertTrue(responseOrg.getBoolean("isRestrictedByUiAccessList"));
    assertTrue(responseOrg.isNull("maxServiceAccountSecretValidityInHours"));
    assertNull(responseOrg.optString("federationSettingsId", null));
  }

  @Test
  public void testGetOrgInformation_includesFederationId_whenOrgIsFederated() {
    final Organization org = MmsFactory.createOrganizationWithStandardPlan();
    AppUser user =
        MmsFactory.createUserWithRoleInOrganization(
            org, getUniquifier() + "@test.com", Role.ORG_OWNER);
    FederationSettings federationSettings =
        MmsFactory.createFederationSettings(
            "oktaId", Set.of(RoleAssignment.forOrg(Role.ORG_MEMBER, org.getId())));

    final JSONObject response = doAuthedJsonGet(user, "/settings/orgs");
    final JSONObject responseOrg = response.getJSONArray("orgs").getJSONObject(0);

    assertNotNull(responseOrg.optString("roles", null));
    assertNotNull(responseOrg.optString("id", null));
    assertNotNull(responseOrg.optString("name", null));
    assertNotNull(responseOrg.optString("planType", null));
    assertNotNull(responseOrg.optString("numOwners", null));
    assertNotNull(responseOrg.optString("multiFactorAuthRequired", null));
    assertFalse(responseOrg.getBoolean("isFineGrainedAuthEnabled"));
    assertFalse(responseOrg.getBoolean("isRestrictedByUiAccessList"));
    assertTrue(responseOrg.isNull("maxServiceAccountSecretValidityInHours"));
    assertNotNull(responseOrg.optString("hasVercelIntegration", null));
    assertNotNull(responseOrg.optString("hasDataApi", null));

    final String federationSettingsId = responseOrg.optString("federationSettingsId", null);
    assertEquals(federationSettings.getId().toString(), federationSettingsId);
  }

  @Test
  public void testGetOrgInformationwithEmbargoed_includesEmbargoedOrgs() {
    final AppUser user = MmsFactory.createUserInOrg(_org, getUniquifier() + "@test.com");
    MmsFactory.addUserWithRoleInOrganization(_embargoedOrg, user, Role.ORG_MEMBER);

    final JSONObject response = doAuthedJsonGet(user, "/settings/orgsWithEmbargoedOrgs");
    final JSONArray orgs = response.getJSONArray("orgs");
    final JSONObject responseOrg = orgs.getJSONObject(0);

    assertEquals(orgs.length(), 2);
    assertNotNull(responseOrg.optString("roles", null));
    assertNotNull(responseOrg.optString("id", null));
    assertNotNull(responseOrg.optString("name", null));
    assertNotNull(responseOrg.optString("planType", null));
    assertNotNull(responseOrg.optString("numOwners", null));
    assertNotNull(responseOrg.optString("multiFactorAuthRequired", null));
    assertNotNull(responseOrg.optString("hasVercelIntegration", null));
    assertNotNull(responseOrg.optString("hasDataApi", null));
    assertFalse(responseOrg.getBoolean("isFineGrainedAuthEnabled"));
    assertFalse(responseOrg.getBoolean("isRestrictedByUiAccessList"));
    assertTrue(responseOrg.isNull("maxServiceAccountSecretValidityInHours"));
    assertNull(responseOrg.optString("federationSettingsId", null));
  }

  @Test
  public void testDeletePrometheusSettings() {
    final ObjectId groupId = _group.getId();

    final String path = String.format("/settings/prometheus/%s", _group.getId());
    doAuthedDelete(_user, path, HttpStatus.SC_NO_CONTENT);
    assertTrue(_group.getPromConfig() == null);

    final PrometheusConfig promConfig =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("user")
            .passwordHash("pass_hash2")
            .passwordSalt("salt")
            .scheme("http")
            .tlsPemPath("/path/to/pem")
            .tlsPemPassword("pemPassword")
            .metricsPath("/metrics")
            .mode("atlas")
            .serviceDiscovery("file")
            .build();
    _groupDao.updatePromConfig(groupId, promConfig);

    doAuthedDelete(_user, path, HttpStatus.SC_NO_CONTENT);
    assertTrue(_group.getPromConfig() == null);
  }

  @Test
  public void testUpdatePrometheusSettingsHappyPath_newConfig() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _org, FeatureFlag.PROMETHEUS_OVER_PRIVATE_LINK);

    final Map<String, String> data = new HashMap<>();
    data.put("enabled", "true");
    data.put("username", "user");
    data.put("password", "pass");
    data.put("scheme", "http");
    data.put("tlsPemPath", "/path/to/pem");
    data.put("tlsPemPassword", "pemPassword");
    data.put("listenAddress", ":1234");
    data.put("serviceDiscovery", "file");

    doAuthedFormPost(
        _user, String.format("/settings/prometheus/%s", _group.getId()), data, HttpStatus.SC_OK);

    final Group updatedGroup = _groupDao.findById(_group.getId());
    assertEquals(true, updatedGroup.getPromConfig().isEnabled());
    assertEquals("user", updatedGroup.getPromConfig().getUsername());
    assertNotNull(updatedGroup.getPromConfig().getPasswordHash());
    assertNotNull(updatedGroup.getPromConfig().getPasswordSalt());
    assertEquals("http", updatedGroup.getPromConfig().getScheme());
    assertEquals("/path/to/pem", updatedGroup.getPromConfig().getTlsPemPath());
    assertEquals("pemPassword", updatedGroup.getPromConfig().getTlsPemPassword());
    assertEquals(":1234", updatedGroup.getPromConfig().getListenAddress());
    assertEquals("file", updatedGroup.getPromConfig().getServiceDiscovery());
    assertEquals("cloudManager", updatedGroup.getPromConfig().getMode());
    assertEquals("/metrics", updatedGroup.getPromConfig().getMetricsPath());
    assertEquals(false, updatedGroup.getPromConfig().isViaPrivateEndpoint());
    assertEquals(List.of(), updatedGroup.getPromConfig().getSelectedPrivateEndpoints());

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _ndsGroup, _ndsOrg, FeatureFlag.PROMETHEUS_OVER_PRIVATE_LINK);

    setUpAwsEndpointServices();
    data.put("isViaPrivateEndpoint", "true");
    data.put("selectedPrivateEndpoints", "endpointId0");

    doAuthedFormPost(
        _ndsUser,
        String.format("/settings/prometheus/%s", _ndsGroup.getId()),
        data,
        HttpStatus.SC_OK);

    final Group updatedNdsGroup = _groupDao.findById(_ndsGroup.getId());
    assertEquals(true, updatedNdsGroup.getPromConfig().isEnabled());
    assertEquals("user", updatedNdsGroup.getPromConfig().getUsername());
    assertNotNull(updatedNdsGroup.getPromConfig().getPasswordHash());
    assertNotNull(updatedNdsGroup.getPromConfig().getPasswordSalt());
    assertEquals("http", updatedNdsGroup.getPromConfig().getScheme());
    assertEquals("", updatedNdsGroup.getPromConfig().getTlsPemPath());
    assertEquals("", updatedNdsGroup.getPromConfig().getTlsPemPassword());
    assertEquals("127.0.0.1:9945", updatedNdsGroup.getPromConfig().getListenAddress());
    assertEquals("file", updatedNdsGroup.getPromConfig().getServiceDiscovery());
    assertEquals("atlas", updatedNdsGroup.getPromConfig().getMode());
    assertEquals("/metrics", updatedNdsGroup.getPromConfig().getMetricsPath());
    assertEquals(true, updatedNdsGroup.getPromConfig().isViaPrivateEndpoint());
    assertEquals(
        List.of("endpointId0"), updatedNdsGroup.getPromConfig().getSelectedPrivateEndpoints());
  }

  @Test
  public void testUpdatePrometheusSettingsHappyPath_existingConfig() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _org, FeatureFlag.PROMETHEUS_OVER_PRIVATE_LINK);

    final PrometheusConfig promConfig =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("user")
            .passwordHash("pass_hash2")
            .passwordSalt("salt")
            .scheme("http")
            .tlsPemPath("/path/to/pem")
            .tlsPemPassword("pemPassword")
            .metricsPath("/metrics")
            .mode("cloudManager")
            .serviceDiscovery("file")
            .build();
    _groupDao.updatePromConfig(_group.getId(), promConfig);

    final Map<String, String> data = new HashMap<>();
    data.put("enabled", "false");
    data.put("username", "usernew");
    data.put("tlsPemPath", "/path/to/pem2");
    data.put("tlsPemPassword", "pemPassword2");
    data.put("listenAddress", ":1234");
    data.put("serviceDiscovery", "http");

    doAuthedFormPut(
        _user, String.format("/settings/prometheus/%s", _group.getId()), data, HttpStatus.SC_OK);

    final Group updatedGroup = _groupDao.findById(_group.getId());
    assertEquals(false, updatedGroup.getPromConfig().isEnabled());
    assertEquals("usernew", updatedGroup.getPromConfig().getUsername());
    assertNotNull(updatedGroup.getPromConfig().getPasswordHash());
    assertNotNull(updatedGroup.getPromConfig().getPasswordSalt());
    assertEquals("http", updatedGroup.getPromConfig().getScheme());
    assertEquals("/path/to/pem2", updatedGroup.getPromConfig().getTlsPemPath());
    assertEquals("pemPassword2", updatedGroup.getPromConfig().getTlsPemPassword());
    assertEquals(":1234", updatedGroup.getPromConfig().getListenAddress());
    assertEquals("http", updatedGroup.getPromConfig().getServiceDiscovery());
    assertEquals("cloudManager", updatedGroup.getPromConfig().getMode());
    assertEquals("/metrics", updatedGroup.getPromConfig().getMetricsPath());
    assertEquals(false, updatedGroup.getPromConfig().isViaPrivateEndpoint());
    assertEquals(List.of(), updatedGroup.getPromConfig().getSelectedPrivateEndpoints());

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _ndsGroup, _ndsOrg, FeatureFlag.PROMETHEUS_OVER_PRIVATE_LINK);

    setUpAwsEndpointServices();
    data.put("isViaPrivateEndpoint", "true");
    data.put("selectedPrivateEndpoints[]", "endpointId0");

    _groupDao.updatePromConfig(_ndsGroup.getId(), promConfig);
    doAuthedFormPut(
        _ndsUser,
        String.format("/settings/prometheus/%s", _ndsGroup.getId()),
        data,
        HttpStatus.SC_OK);

    final Group updatedNdsGroup = _groupDao.findById(_ndsGroup.getId());
    assertEquals(false, updatedNdsGroup.getPromConfig().isEnabled());
    assertEquals("usernew", updatedNdsGroup.getPromConfig().getUsername());
    assertNotNull(updatedNdsGroup.getPromConfig().getPasswordHash());
    assertNotNull(updatedNdsGroup.getPromConfig().getPasswordSalt());
    assertEquals("http", updatedNdsGroup.getPromConfig().getScheme());
    assertEquals("", updatedNdsGroup.getPromConfig().getTlsPemPath());
    assertEquals("", updatedNdsGroup.getPromConfig().getTlsPemPassword());
    assertEquals("127.0.0.1:9945", updatedNdsGroup.getPromConfig().getListenAddress());
    assertEquals("http", updatedNdsGroup.getPromConfig().getServiceDiscovery());
    assertEquals("atlas", updatedNdsGroup.getPromConfig().getMode());
    assertEquals("/metrics", updatedNdsGroup.getPromConfig().getMetricsPath());
    assertEquals(true, updatedNdsGroup.getPromConfig().isViaPrivateEndpoint());
    assertEquals(
        List.of("endpointId0"), updatedNdsGroup.getPromConfig().getSelectedPrivateEndpoints());
  }

  @Test
  public void testUpdatePrometheusSettings_invalidPrivateEndpoint() {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _ndsGroup, _ndsOrg, FeatureFlag.PROMETHEUS_OVER_PRIVATE_LINK);

    final Map<String, String> data = new HashMap<>();
    data.put("enabled", "true");
    data.put("username", "user");
    data.put("password", "pass");
    data.put("scheme", "http");
    data.put("tlsPemPath", "/path/to/pem");
    data.put("tlsPemPassword", "pemPassword");
    data.put("listenAddress", ":1234");
    data.put("serviceDiscovery", "file");

    data.put("isViaPrivateEndpoint", "true");
    data.put("selectedPrivateEndpoints", "endpointId0");

    doAuthedFormPost(
        _ndsUser,
        String.format("/settings/prometheus/%s", _ndsGroup.getId()),
        data,
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testUpdateExtendedStorageSizesEnabled() {
    final ObjectId groupId = _ndsGroup.getId();
    doAuthedFormPost(
        _ndsUser,
        "/settings/updateExtendedStorageSizesEnabled/" + groupId,
        Map.of("value", "true"),
        HttpStatus.SC_OK);
    assertTrue(_ndsGroupSvc.find(groupId).orElseThrow().isExtendedStorageSizesEnabled());

    doAuthedFormPost(
        _ndsUser,
        "/settings/updateExtendedStorageSizesEnabled/" + groupId,
        Map.of("value", "false"),
        HttpStatus.SC_OK);
    assertFalse(_ndsGroupSvc.find(groupId).orElseThrow().isExtendedStorageSizesEnabled());
  }

  @Test
  public void testUpdateAtlasGoToSettings() {
    final Map<String, String> data = new HashMap<>();
    data.put("isDisabled", "true");

    doAuthedFormPost(_user, "/settings/updateAtlasGoToSettings", data, HttpStatus.SC_OK);

    final AppUser updatedAppUser = _userDao.findById(_user.getId());
    assertTrue(updatedAppUser.isGoToDisabled());
  }

  @Test
  public void testUpdateFlaggedForPersonalizationWizard() {
    _user.setFlaggedForPersonalizationWizard(true);
    final Map<String, String> data = new HashMap<>();
    data.put("enabled", "false");

    doAuthedFormPost(
        _user, "/settings/updateFlaggedForPersonalizationWizard", data, HttpStatus.SC_OK);

    final AppUser updatedAppUser = _userDao.findById(_user.getId());
    assertFalse(updatedAppUser.isFlaggedForPersonalizationWizard());
  }

  @Test
  public void testUpdatePrometheusSettingsSadPath() {
    final Map<String, String> data = new HashMap<>();
    data.put("enabled", "true");
    data.put("username", "user");
    data.put("scheme", "http");
    data.put("tlsPemPath", "/path/to/pem");
    data.put("tlsPemPassword", "pemPassword");
    data.put("listenAddress", ":1234");
    data.put("serviceDiscovery", "file");

    // required field like password or username missing in the request
    JSONObject response =
        doAuthedFormPost(
            _user,
            String.format("/settings/prometheus/%s", _group.getId()),
            data,
            HttpStatus.SC_BAD_REQUEST);

    assertEquals("MISSING_PROMETHEUS_PASSWORD", response.getString("errorCode"));

    data.put("password", "pass");
    data.remove("username", "user");

    response =
        doAuthedFormPost(
            _user,
            String.format("/settings/prometheus/%s", _group.getId()),
            data,
            HttpStatus.SC_BAD_REQUEST);

    assertEquals("MISSING_PROMETHEUS_USERNAME", response.getString("errorCode"));

    data.put("username", "user");

    // invalid or missing scheme
    data.remove("scheme");

    response =
        doAuthedFormPost(
            _user,
            String.format("/settings/prometheus/%s", _group.getId()),
            data,
            HttpStatus.SC_BAD_REQUEST);

    assertEquals("MISSING_PROMETHEUS_SCHEME", response.getString("errorCode"));

    data.put("scheme", "httpnew");

    response =
        doAuthedFormPost(
            _user,
            String.format("/settings/prometheus/%s", _group.getId()),
            data,
            HttpStatus.SC_BAD_REQUEST);

    assertEquals("INVALID_PROMETHEUS_SCHEME", response.getString("errorCode"));

    data.put("scheme", "http");

    // invalid or missing listen addreess
    data.remove("listenAddress");

    response =
        doAuthedFormPost(
            _user,
            String.format("/settings/prometheus/%s", _group.getId()),
            data,
            HttpStatus.SC_BAD_REQUEST);

    assertEquals("MISSING_LISTEN_ADDRESS", response.getString("errorCode"));

    data.put("listenAddress", "123.*******:1234");

    response =
        doAuthedFormPost(
            _user,
            String.format("/settings/prometheus/%s", _group.getId()),
            data,
            HttpStatus.SC_BAD_REQUEST);

    assertEquals("INVALID_PROMETHEUS_LISTEN_ADDRESS", response.getString("errorCode"));

    data.put("listenAddress", ":1234");

    // invalid or missing service discovery
    data.remove("serviceDiscovery");

    response =
        doAuthedFormPost(
            _user,
            String.format("/settings/prometheus/%s", _group.getId()),
            data,
            HttpStatus.SC_BAD_REQUEST);

    assertEquals("MISSING_PROMETHEUS_SERVICE_DISCOVERY", response.getString("errorCode"));

    data.put("serviceDiscovery", "random-file");

    response =
        doAuthedFormPost(
            _user,
            String.format("/settings/prometheus/%s", _group.getId()),
            data,
            HttpStatus.SC_BAD_REQUEST);

    assertEquals("INVALID_PROMETHEUS_SERVICE_DISCOVERY", response.getString("errorCode"));
  }

  @Test
  public void testTimeZoneSettings() {
    // clear up group and user tz preferences
    _groupDao.setDefaultTimeZoneId(_group.getId(), null, null);
    _userDao.setDefaultTimeZoneId(_user.getId(), null, null);

    // tz data
    final String tzUtcId = "Etc/UTC";
    final String tzUtcName = "UTC";
    final String tzNyId = "America/New_York";
    final String tzNyName = "Eastern Time (US & Canada)";
    final String tzKyivId = "Europe/Kiev";
    final String tzKyivName = "Kyiv";

    getAndAssertGroupTimezone(_user, _group.getId(), tzUtcId, tzUtcName, false);

    // Sets group timezone to ET
    updateAndAssertGroupTimezone(_user, _group.getId(), tzNyId, tzNyName);
    getAndAssertGroupTimezone(_user, _group.getId(), tzNyId, tzNyName, false);

    // Sets user timezone to Kyiv's
    updateAndAssertUserTimezone(_user, tzKyivId, tzKyivName);

    // response for group tz with considerUserPrefs=false still shows group tz
    getAndAssertGroupTimezone(_user, _group.getId(), tzNyId, tzNyName, false);

    // response for group tz with considerUserPrefs=true shows user tz
    getAndAssertGroupTimezone(_user, _group.getId(), tzKyivId, tzKyivName, true);

    // Sets user timezone back to null
    updateAndAssertUserTimezone(_user, null, null);

    // response for group tz with considerUserPrefs=true shows group tz
    getAndAssertGroupTimezone(_user, _group.getId(), tzNyId, tzNyName, true);
  }

  @Test
  public void testGroupTimeZoneSettings_whenTimeZoneNameMissing_infersTimeZoneNameFromTimeZoneId() {
    // clear up group and user tz preferences
    _groupDao.setDefaultTimeZoneId(_group.getId(), null, null);

    doAuthedFormPost(
        _user,
        String.format("/settings/updateGroupTimeZoneId/%s", _group.getId()),
        Map.of("timeZoneId", "America/New_York"),
        HttpStatus.SC_OK);

    // updates timezone id and timezone name
    getAndAssertGroupTimezone(
        _user, _group.getId(), "America/New_York", "Eastern Time (US & Canada)", false);
  }

  @Test
  public void testGroupTimeZoneSettings_whenTimeZoneIdInvalid_returns400() {
    // clear up group and user tz preferences
    _groupDao.setDefaultTimeZoneId(_group.getId(), null, null);

    doAuthedFormPost(
        _user,
        String.format("/settings/updateGroupTimeZoneId/%s", _group.getId()),
        Map.of("timeZoneId", "invalidTzId"),
        HttpStatus.SC_BAD_REQUEST);

    // does not update anything
    getAndAssertGroupTimezone(_user, _group.getId(), "Etc/UTC", "UTC", false);
  }

  @Test
  public void testGroupTimeZoneSettings_whenTimeZoneIdMissing_returns400() {
    // clear up group and user tz preferences
    _groupDao.setDefaultTimeZoneId(_group.getId(), null, null);

    doAuthedFormPost(
        _user,
        String.format("/settings/updateGroupTimeZoneId/%s", _group.getId()),
        new HashMap<>(),
        HttpStatus.SC_BAD_REQUEST);

    // does not update anything
    getAndAssertGroupTimezone(_user, _group.getId(), "Etc/UTC", "UTC", false);
  }

  @Test
  public void testGroupTimeZoneSettings_whenTimeZoneNameInvalid_returns400() {
    // clear up group and user tz preferences
    _groupDao.setDefaultTimeZoneId(_group.getId(), null, null);

    doAuthedFormPost(
        _user,
        String.format("/settings/updateGroupTimeZoneId/%s", _group.getId()),
        Map.of("timeZoneName", "invalidTzName"),
        HttpStatus.SC_BAD_REQUEST);

    // does not update anything
    getAndAssertGroupTimezone(_user, _group.getId(), "Etc/UTC", "UTC", false);
  }

  @Test
  public void testAgentLogLevel_noHttpParam() {
    doAuthedFormPost(
        _globalOwnerAdminUser,
        String.format("/settings/agentloglevel/automation/%s", _group.getId()),
        Map.of("value", "WARN"),
        HttpStatus.SC_OK);
    assertEquals(
        _groupDao.findById(_group.getId()).getAgentLogLevel(AgentType.AUTOMATION),
        AgentLogLevel.WARN);
  }

  @Test
  public void testAgentLogLevel_httpParam() throws InterruptedException {
    final AgentLogLevel logLevel =
        _groupDao.findById(_group.getId()).getAgentLogLevel(AgentType.AUTOMATION);
    doAuthedFormPost(
        _globalOwnerAdminUser,
        String.format("/settings/agentloglevel/automation/%s?http=true", _group.getId()),
        Map.of("value", "DEBUG"),
        HttpStatus.SC_OK);
    // we're reading our writes, let's try to read them a max of three times
    final int maxRetry = 3;
    AgentLogLevel httpAgentLogLevel = null;
    for (int i = 1; i <= maxRetry; i++) {
      httpAgentLogLevel =
          _groupDao.findById(_group.getId()).getAgentHttpLogLevel(AgentType.AUTOMATION);
      // httpAgentLogLevel is properly set to DEBUG or reached max number of retries, let's break
      // out
      if (AgentLogLevel.DEBUG.equals(httpAgentLogLevel) || i == maxRetry) {
        break;
      }
      Thread.sleep(i * 1_000);
    }
    assertEquals(httpAgentLogLevel, AgentLogLevel.DEBUG);

    // Confirm that the filesystem log level did not change after the HTTP log level change.
    assertEquals(
        _groupDao.findById(_group.getId()).getAgentLogLevel(AgentType.AUTOMATION), logLevel);
  }

  private void getAndAssertGroupTimezone(
      AppUser user,
      ObjectId groupId,
      String timeZoneId,
      String timeZoneName,
      boolean considerUserPrefs) {
    JSONObject response =
        doAuthedJsonGet(
            user,
            String.format(
                "/settings/groupTimeZone/%s?considerUserPrefs=%b", groupId, considerUserPrefs),
            HttpStatus.SC_OK);

    assertEquals(timeZoneId, response.getString(JSON_TIME_ZONE_ID_FIELD));
    assertEquals(timeZoneName, response.getString(JSON_TIME_ZONE_DISPLAY_FIELD));
    // since this is calculated using the machine's current millis, guaranteeing non-flakiness on
    // the result (due to daylight savings) would be tricky. So only checking for the value's
    // presence :-)
    assertNotNull(response.getNumber("timeZoneCurrentOffset"));
  }

  private void updateAndAssertGroupTimezone(
      AppUser user, ObjectId groupId, String timeZoneId, String timeZoneName) {
    final JSONObject response =
        doAuthedFormPost(
            user,
            String.format("/settings/updateGroupTimeZoneId/%s", groupId),
            Map.of("timeZoneId", timeZoneId, "timeZoneDisplay", timeZoneName),
            HttpStatus.SC_OK);

    assertEquals(timeZoneId, response.getString(JSON_TIME_ZONE_ID_FIELD));
    assertEquals(timeZoneName, response.getString(JSON_TIME_ZONE_DISPLAY_FIELD));
    // Only checking for the presence of the value as the value can change based on daylight savings
    assertNotNull(response.getString(JSON_TIME_ZONE_DISPLAY_SHORT_FIELD));
  }

  private void updateAndAssertUserTimezone(AppUser user, String timeZoneId, String timeZoneName) {
    final Map<String, String> requestBody = new HashMap<>();
    requestBody.put("timeZoneId", timeZoneId);
    requestBody.put("displayName", timeZoneName);
    final JSONObject response =
        doAuthedFormPost(user, "/settings/updateTimeZoneId", requestBody, HttpStatus.SC_OK);

    if (timeZoneId == null && timeZoneName == null) {
      assertFalse(response.has(JSON_TIME_ZONE_ID_FIELD));
      assertFalse(response.has(JSON_TIME_ZONE_DISPLAY_FIELD));
    } else {
      assertEquals(timeZoneId, response.getString(JSON_TIME_ZONE_ID_FIELD));
      assertEquals(timeZoneName, response.getString(JSON_TIME_ZONE_DISPLAY_FIELD));
    }
  }

  @Test
  public void testIsProjectOverviewEnabled() {
    // Given
    final ObjectId groupId = _group.getId();
    final Group group = _groupSvc.findById(groupId);
    assertTrue(group.getIsProjectOverviewEnabled());

    // When
    final Map<String, String> params = new HashMap<>();
    params.put("isEnabled", "false");
    doAuthedFormPost(
        _globalOwnerAdminUser,
        String.format("/settings/isProjectOverviewEnabled/%s", groupId),
        params,
        HttpStatus.SC_ACCEPTED);

    // Then
    final Group updatedGroup = _groupSvc.findById(groupId);
    assertFalse(updatedGroup.getIsProjectOverviewEnabled());
  }

  @Test
  public void testIsProjectOverviewEnabledMissingParameter() {
    // Given
    final Map<String, String> params = new HashMap<>();
    params.put("incorrectParameter", "false");

    // When
    final JSONObject result =
        doAuthedFormPost(
            _globalOwnerAdminUser,
            String.format("/settings/isProjectOverviewEnabled/%s", _group.getId()),
            params,
            HttpStatus.SC_BAD_REQUEST);

    // Then
    assertEquals("Parameter isEnabled not found in request", result.getString("message"));
  }

  @Test
  public void testSetEnableCurrentIpWarning() {
    // Given
    final ObjectId groupId = _group.getId();
    final Map<String, String> params = new HashMap<>();
    params.put("isEnabled", "false");
    final Group group = _groupSvc.findById(groupId);
    assertTrue(group.getEnableCurrentIpWarning());

    // When
    doAuthedFormPost(
        _globalOwnerAdminUser,
        String.format("/settings/setEnableCurrentIpWarning/%s", groupId),
        params,
        HttpStatus.SC_ACCEPTED);

    // Then
    final Group updatedGroup = _groupSvc.findById(groupId);
    assertFalse(updatedGroup.getEnableCurrentIpWarning());
  }

  @Test
  public void testSetEnableCurrentIpWarningReturnsErrorIfMissingParameter() {
    // Given
    final Map<String, String> params = new HashMap<>();
    params.put("incorrectParameter", "true");

    // When
    final JSONObject result =
        doAuthedFormPost(
            _globalOwnerAdminUser,
            String.format("/settings/setEnableCurrentIpWarning/%s", _group.getId()),
            params,
            HttpStatus.SC_BAD_REQUEST);

    // Then
    assertEquals("Parameter isEnabled not found in request", result.getString("message"));
  }

  @Test
  public void testUpdateProjectLandingPage_validInputValidUser() {
    String path = "/settings/projectLandingPage";

    ProjectLandingPage projectLandingPage = ProjectLandingPage.CLUSTERS;
    final Map<String, String> params = Map.of("projectLandingPage", projectLandingPage.toString());
    JSONObject putResponse = doAuthedFormPut(_globalOwnerAdminUser, path, params);
    assertEquals(projectLandingPage.toString(), putResponse.getString("projectLandingPage"));
  }

  @Test
  public void testUpdateProjectLandingPage_validInputInvalidUser() {
    String path = "/settings/projectLandingPage";
    final Map<String, String> params =
        Map.of("projectLandingPage", ProjectLandingPage.CLUSTERS.toString());
    doAuthedFormPut(_ndsUser, path, params, HttpStatus.SC_FORBIDDEN);
  }

  @Test
  public void testUpdateProjectLandingPage_invalidInputValidUser() {
    String path = "/settings/projectLandingPage";
    final Map<String, String> params = Map.of("projectLandingPage", "asdfasdf");
    doAuthedFormPut(_globalOwnerAdminUser, path, params, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void testUpdateDateFormat_validCodeInput() {
    String path = "/settings/updateDateFormat";
    final Map<String, String> params = Map.of("value", DateFormat.STANDARD_1.getCode());
    doAuthedFormPost(_globalOwnerAdminUser, path, params, HttpStatus.SC_OK);
  }

  @Test
  public void testUpdateDateFormat_invalidCodeInput() {
    String path = "/settings/updateDateFormat";
    final Map<String, String> params = Map.of("value", "this isn't a real code");
    doAuthedFormPost(_globalOwnerAdminUser, path, params, HttpStatus.SC_BAD_REQUEST);
  }

  private void setUpIpAccessListForOrg(final ObjectId orgId, final List<String> ipAddresses) {
    for (final String ip : ipAddresses) {
      _orgUiIpAccessListEntryFactory.insertEntry(orgId, ObjectId.get(), ip, "");
    }
    _orgSvc.setUiIpAccessListEnabled(orgId, true);
  }

  private void stubPaymentMethod(final ObjectId orgId) {
    doReturn(
            Optional.of(
                PaymentMethod.builder()
                    .type(PaymentMethodType.CREDIT_CARD)
                    .orgId(orgId)
                    .billingAccount(BillingAccount.MONGODB_INC)
                    .status(PaymentMethod.Status.ACTIVE)
                    .build()))
        .when(paymentMethodClient)
        .getActivePaymentMethod(eq(orgId), anyBoolean());
  }

  public void setUpAwsEndpointServices() {
    final NDSGroup ndsGroup = _ndsGroupDao.find(_ndsGroup.getId()).get();
    final CloudProviderContainer container = ndsGroup.getCloudProviderContainers().get(0);

    final AWSPrivateLinkConnection endpointService =
        new AWSPrivateLinkConnection(
            ObjectId.get(),
            DedicatedEndpointService.Status.AVAILABLE,
            "endpointServiceError0",
            false,
            null,
            List.of(
                new AWSPrivateLinkInterfaceEndpoint(
                    "endpointId0",
                    "privateHostname0",
                    AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                    "endpointError0",
                    false,
                    0)),
            "endpointServiceName0",
            "endpointServiceId0",
            "arn:aws:elasticloadbalancing:us-east-2:123456789012:targetgroup/my-targets/0000000000000000");

    _awsPrivateLinkConnectionDao.initializePrivateEndpointService(
        endpointService, _ndsGroup.getId(), container.getId());
  }
}
