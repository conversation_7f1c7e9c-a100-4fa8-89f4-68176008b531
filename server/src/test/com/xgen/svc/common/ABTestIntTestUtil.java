package com.xgen.svc.common;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.xgen.cloud.abtesting._private.dao.ABTestDao;
import com.xgen.cloud.abtesting._private.dao.AssignmentDao;
import com.xgen.cloud.abtesting._private.dao.GhostAssignmentDao;
import com.xgen.cloud.abtesting._private.dao.OverrideAssignmentDao;
import com.xgen.cloud.abtesting._public.model.ABTest;
import com.xgen.cloud.abtesting._public.model.ABTest.ABTestBuilder;
import com.xgen.cloud.abtesting._public.model.ABTest.ExperimentType;
import com.xgen.cloud.abtesting._public.model.Assignment;
import com.xgen.cloud.abtesting._public.model.AssignmentMetaData;
import com.xgen.cloud.abtesting._public.model.GhostAssignment;
import com.xgen.cloud.abtesting._public.model.OverrideAssignment;
import com.xgen.cloud.abtesting._public.model.TestGroup;
import com.xgen.cloud.abtesting._public.model.TestGroupFeatureFlag;
import com.xgen.cloud.abtesting._public.svc.ABTestSvc.AssignmentDataResponse;
import com.xgen.cloud.common.entity._public.model.EntityType;
import com.xgen.cloud.ifrabtest._public.model.ExperimentStatus;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;

@Singleton
public class ABTestIntTestUtil {
  @Inject private ABTestDao _abTestDao;
  @Inject private AssignmentDao _assignmentDao;
  @Inject private GhostAssignmentDao _ghostAssignmentDao;
  @Inject private OverrideAssignmentDao _overrideAssignmentDao;

  public ObjectId createTest(
      final String testName,
      final Integer scopePercentage,
      final Integer gradualRolloutPercentage,
      final ExperimentStatus experimentStatus,
      final EntityType entityType,
      final String tag) {
    return createTest(
        testName,
        scopePercentage,
        gradualRolloutPercentage,
        experimentStatus,
        entityType,
        tag,
        null,
        null,
        null);
  }

  public ObjectId createTest(
      final String testName,
      final Integer scopePercentage,
      final ExperimentStatus experimentStatus,
      final EntityType entityType,
      final String tag) {
    return createTest(
        testName, scopePercentage, experimentStatus, entityType, tag, null, null, null);
  }

  public ObjectId createTest(
      final String testName,
      final Integer scopePercentage,
      final ExperimentStatus experimentStatus,
      final EntityType entityType,
      final String tag,
      ObjectId testId,
      List<ObjectId> variantIds,
      List<TestGroupFeatureFlag> featureFlags) {
    return createTest(
        testName,
        scopePercentage,
        null,
        experimentStatus,
        entityType,
        tag,
        testId,
        variantIds,
        featureFlags);
  }

  public ObjectId createTest(
      final String testName,
      final Integer scopePercentage,
      final Integer gradualRolloutPercentage,
      final ExperimentStatus experimentStatus,
      final EntityType entityType,
      final String tag,
      ObjectId testId,
      List<ObjectId> variantIds,
      List<TestGroupFeatureFlag> featureFlags) {
    List<TestGroupFeatureFlag> featureFlagsToUse = featureFlags;
    if (featureFlagsToUse == null) {
      featureFlagsToUse = new ArrayList<>();
      featureFlagsToUse.add(new TestGroupFeatureFlag("key1", "value1"));
      featureFlagsToUse.add(new TestGroupFeatureFlag("key2", "value2"));
    }

    final ObjectId testIdToUse = testId == null ? new ObjectId() : testId;
    List<ObjectId> variantIdsToUse = List.of(new ObjectId(), new ObjectId(), new ObjectId());
    if (variantIds != null) {
      if (variantIds.size() != 3) {
        throw new IllegalArgumentException(
            "variantIds provided for createTest need to be of length 3");
      }
      variantIdsToUse = variantIds;
    }

    final ABTest test1 =
        ABTest.builder()
            .id(testIdToUse)
            .experimentStatus(experimentStatus)
            .testName(testName)
            .testGroups(
                List.of(
                    new TestGroup(
                        "Test1Control",
                        variantIdsToUse.get(0),
                        33.34,
                        featureFlagsToUse,
                        true,
                        null),
                    new TestGroup(
                        "Test1Variant1",
                        variantIdsToUse.get(1),
                        33.33,
                        featureFlagsToUse,
                        false,
                        null),
                    new TestGroup(
                        "Test1Variant2",
                        variantIdsToUse.get(2),
                        33.33,
                        featureFlagsToUse,
                        false,
                        null)))
            .description("desc1")
            .scope(entityType)
            .scopePercentage(scopePercentage)
            .gradualRolloutPercentage(gradualRolloutPercentage)
            .jiraLink("jira")
            .tag(tag)
            .experimentType(ExperimentType.AB)
            .build();
    _abTestDao.upsertTest(test1);
    return testIdToUse;
  }

  public boolean updateGradualRolloutPercentageByTestId(
      final ObjectId testId, final Integer gradualRolloutPercentage) {
    return _abTestDao.updateGradualRolloutPercentage(gradualRolloutPercentage, testId);
  }

  public ObjectId createHoldoutTest(
      final String testName,
      final ExperimentStatus experimentStatus,
      final EntityType entityType,
      final ObjectId controlTestGroupId,
      final ObjectId variantTestGroupId) {
    final ObjectId testId = new ObjectId();

    final ABTest test =
        ABTest.builder()
            .id(testId)
            .experimentStatus(experimentStatus)
            .testName(testName)
            .testGroups(
                List.of(
                    new TestGroup(
                        "HoldoutTestControl", controlTestGroupId, 5, new ArrayList<>(), true, null),
                    new TestGroup(
                        "HoldoutTestVariant",
                        variantTestGroupId,
                        95,
                        new ArrayList<>(),
                        false,
                        null)))
            .description("desc1")
            .scope(entityType)
            .jiraLink("jira")
            .tag("GLOBAL_HOLDOUT")
            .experimentType(ExperimentType.HOLDOUT)
            .scopePercentage(100)
            .build();
    _abTestDao.upsertTest(test);
    return testId;
  }

  public ObjectId createHoldoutTestWithGuaranteedControl(
      final String testName,
      final ExperimentStatus experimentStatus,
      final EntityType entityType,
      final ObjectId controlTestGroupId) {
    final ObjectId testId = new ObjectId();

    final ABTest test =
        ABTest.builder()
            .id(testId)
            .experimentStatus(experimentStatus)
            .testName(testName)
            .testGroups(
                List.of(
                    new TestGroup(
                        "HoldoutTestControl",
                        controlTestGroupId,
                        100,
                        new ArrayList<>(),
                        true,
                        null),
                    new TestGroup(
                        "HoldoutTestVariant", new ObjectId(), 0, new ArrayList<>(), false, null)))
            .description("desc1")
            .scope(entityType)
            .jiraLink("jira")
            .tag("GLOBAL_HOLDOUT")
            .experimentType(ExperimentType.HOLDOUT)
            .scopePercentage(100)
            .build();
    _abTestDao.upsertTest(test);
    return testId;
  }

  public ObjectId createLaunchedTest(
      String testName,
      Integer scopePercentage,
      String tag,
      ObjectId testId,
      List<ObjectId> variantIds,
      List<TestGroupFeatureFlag> featureFlagsControl,
      List<TestGroupFeatureFlag> featureFlagsVariant,
      List<String> allocationPoints) {
    final ObjectId testIdToUse = testId == null ? new ObjectId() : testId;

    List<ObjectId> variantIdsToUse = List.of(new ObjectId(), new ObjectId());
    if (variantIds != null) {
      if (variantIds.size() != 2) {
        throw new IllegalArgumentException(
            "variantIds provided for createLaunchedTest need to be of length 2");
      }
      variantIdsToUse = variantIds;
    }

    final List<TestGroupFeatureFlag> launchedTestExperimentAttributesControl = new ArrayList<>();
    launchedTestExperimentAttributesControl.add(new TestGroupFeatureFlag("key3", "value3"));
    launchedTestExperimentAttributesControl.add(new TestGroupFeatureFlag("key4", "value4"));

    final List<TestGroupFeatureFlag> launchedTestExperimentAttributesVariant = new ArrayList<>();
    launchedTestExperimentAttributesVariant.add(new TestGroupFeatureFlag("key5", "value5"));
    launchedTestExperimentAttributesVariant.add(new TestGroupFeatureFlag("key6", "value6"));

    final List<TestGroupFeatureFlag> controlExpAttributesToUse =
        featureFlagsControl == null ? launchedTestExperimentAttributesControl : featureFlagsControl;
    final List<TestGroupFeatureFlag> variantExpAttributesToUse =
        featureFlagsVariant == null ? launchedTestExperimentAttributesVariant : featureFlagsVariant;

    final ABTestBuilder testBuilder =
        ABTest.builder()
            .id(testIdToUse)
            .testName(testName)
            .experimentStatus(ExperimentStatus.LAUNCHED)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup(
                        "LaunchedTestControl",
                        variantIdsToUse.get(0),
                        50,
                        controlExpAttributesToUse,
                        true,
                        null),
                    new TestGroup(
                        "LaunchedTestVariant1",
                        variantIdsToUse.get(1),
                        50,
                        variantExpAttributesToUse,
                        false,
                        true)))
            .description("desc1")
            .scope(EntityType.ORG)
            .scopePercentage(scopePercentage)
            .jiraLink("jira")
            .tag(tag);
    if (allocationPoints != null) {
      testBuilder.allocationPoints(allocationPoints);
    }
    _abTestDao.upsertTest(testBuilder.build());

    return testIdToUse;
  }

  public ObjectId createTestWithOverride(
      final String testName,
      final ExperimentStatus experimentStatus,
      final ObjectId pUserId,
      final EntityType entityType,
      final String tag) {

    final List<TestGroupFeatureFlag> featureFlags2 = new ArrayList<>();
    featureFlags2.add(new TestGroupFeatureFlag("key3", "value3"));
    featureFlags2.add(new TestGroupFeatureFlag("key4", "value4"));

    final List<TestGroupFeatureFlag> featureFlags3 = new ArrayList<>();
    featureFlags3.add(new TestGroupFeatureFlag("key5", "value5"));
    featureFlags3.add(new TestGroupFeatureFlag("key6", "value6"));

    // need to have consistent test and database IDs across ABTests and (override) assignments
    final ObjectId overrideAssignmentTestId = new ObjectId();
    final ObjectId overrideAssignmentTestDatabaseID = new ObjectId();

    final ABTest test2 =
        ABTest.builder()
            .id(overrideAssignmentTestId)
            .experimentStatus(experimentStatus)
            .testName(testName)
            .testGroups(
                List.of(
                    new TestGroup("Test2Control", new ObjectId(), 50, featureFlags2, true, null),
                    new TestGroup("Test2Variant1", new ObjectId(), 50, featureFlags2, false, null),
                    new TestGroup(
                        "Test2ImpossibleVariant",
                        overrideAssignmentTestDatabaseID,
                        0,
                        featureFlags3,
                        false,
                        null)))
            .description("desc2")
            .scope(entityType)
            .scopePercentage(100)
            .jiraLink("jira")
            .tag(tag)
            .experimentType(ExperimentType.AB)
            .build();

    _abTestDao.upsertTest(test2);

    final OverrideAssignment overrideAssignmentToAdd =
        new OverrideAssignment.Builder()
            .userId(pUserId)
            .testName(testName)
            .testId(overrideAssignmentTestId)
            .testGroupId("Test2ImpossibleVariant")
            .testGroupDatabaseId(overrideAssignmentTestDatabaseID)
            .entityType(entityType)
            .tag("tag2")
            .assignmentDate(new Date())
            .build();

    _overrideAssignmentDao.createOverrideAssignment(overrideAssignmentToAdd);
    return overrideAssignmentTestId;
  }

  public void createAssignment(
      final String testName,
      final ObjectId entityId,
      final EntityType entityType,
      final ObjectId testId,
      final String tag,
      final ObjectId variantId,
      final String variantName) {
    final String variantNameToUse =
        variantName != null ? variantName : "CREATED_ASSIGNMENT_VARIANT_NAME";
    final Assignment assignmentToAdd =
        Assignment.builder()
            .entityId(entityId)
            .entityType(entityType)
            .testName(testName)
            .testId(testId)
            .testGroupName(variantNameToUse)
            .testGroupDatabaseId(variantId)
            .tag(tag)
            .assignmentDate(new Date())
            .meta(new AssignmentMetaData(false))
            .build();
    _assignmentDao.save(assignmentToAdd);
  }

  public void createGhostAssignment(
      final String testName,
      final ObjectId entityId,
      final EntityType entityType,
      final ObjectId testId,
      final String tag,
      final ObjectId variantId,
      final String variantName,
      final ObjectId holdoutTestId,
      final String holdoutTestName) {
    final String variantNameToUse =
        variantName != null ? variantName : "CREATED_ASSIGNMENT_VARIANT_NAME";
    final GhostAssignment ghostAssignmentToAdd =
        GhostAssignment.builder()
            .entityId(entityId)
            .entityType(entityType)
            .testName(testName)
            .testId(testId)
            .testGroupName(variantNameToUse)
            .testGroupDatabaseId(variantId)
            .tag(tag)
            .assignmentDate(new Date())
            .linkedHoldoutTestId(holdoutTestId)
            .linkedHoldoutTestName(holdoutTestName)
            .meta(new AssignmentMetaData(false))
            .build();
    _ghostAssignmentDao.save(ghostAssignmentToAdd);
  }

  public void createOverrideAssignment(
      final String testName,
      final EntityType entityType,
      final ObjectId pUserId,
      final ObjectId testId,
      final String tag,
      final ObjectId variantId,
      final String variantName) {
    final String variantNameToUse =
        variantName != null ? variantName : "CREATED_OVERRIDE_VARIANT_NAME";
    final OverrideAssignment overrideAssignmentToAdd =
        new OverrideAssignment.Builder()
            .userId(pUserId)
            .testName(testName)
            .testId(testId)
            .testGroupId(variantNameToUse)
            .testGroupDatabaseId(variantId)
            .entityType(entityType)
            .tag(tag)
            .assignmentDate(new Date())
            .build();

    _overrideAssignmentDao.createOverrideAssignment(overrideAssignmentToAdd);
  }

  public void clearAbTestsCache() {
    _abTestDao.clearCache();
  }

  public void compareAssignmentToJSONAssignment(
      final Assignment assignment, final JSONObject jsonAssignment) {
    assertEquals(assignment.getTestGroupName(), jsonAssignment.get("testGroupName"));
    assertEquals(
        assignment.getTestGroupDatabaseId().toString(), jsonAssignment.get("testGroupDatabaseId"));
    assertEquals(assignment.getTag(), jsonAssignment.get("tag"));
    assertEquals(assignment.getTestId().toString(), jsonAssignment.get("testId"));
    assertEquals(assignment.getTestName(), jsonAssignment.get("testName"));
    assertEquals(assignment.getEntityType().toString(), jsonAssignment.get("entityType"));
    assertEquals(assignment.getEntityId().toString(), jsonAssignment.get("entityId"));
  }

  /**
   * This function differs from the above in that it only checks general test and entity info. Use
   * case is for override assignment scenarios where the test assignments in the JSON response will
   * come from the override assignment and not the db assignment.
   */
  public void comparePartialInfoFromAssignmentToJSONAssignment(
      final Assignment assignment, final JSONObject jsonAssignment) {
    assertEquals(assignment.getTag(), jsonAssignment.get("tag"));
    assertEquals(assignment.getTestId().toString(), jsonAssignment.get("testId"));
    assertEquals(assignment.getEntityId().toString(), jsonAssignment.get("entityId"));
  }

  public void compareOverrideAssignmentToJSONAssignment(
      final OverrideAssignment overrideAssignment, final JSONObject jsonAssignment) {
    assertEquals(overrideAssignment.getTestGroupId(), jsonAssignment.get("testGroupId"));
    assertEquals(
        overrideAssignment.getTestGroupDatabaseId().toString(),
        jsonAssignment.get("testGroupDatabaseId"));
    assertEquals(overrideAssignment.getTag(), jsonAssignment.get("tag"));
    assertEquals(overrideAssignment.getId().toString(), jsonAssignment.get("id"));
    assertEquals(overrideAssignment.getTestId().toString(), jsonAssignment.get("testId"));
    assertEquals(overrideAssignment.getTestName(), jsonAssignment.get("testName"));
    assertEquals(overrideAssignment.getEntityType().toString(), jsonAssignment.get("entityType"));
    assertEquals(
        overrideAssignment.getAssignmentDate().getTime(), jsonAssignment.get("assignmentDate"));
  }

  /** Serializes a JSONObject into an AssignmentDataResponse object using jackson serialization */
  public AssignmentDataResponse serializeJsonObjectToAssignmentDataResponse(
      JSONObject jsonResponse) {
    final JSONArray jsonAssignments = (JSONArray) jsonResponse.get("data");
    final JSONObject jsonFeatureFlags = (JSONObject) jsonResponse.get("experimentFeatureFlags");
    final JSONArray jsonGhostAssignments = (JSONArray) jsonResponse.get("ghostAssignments");

    try {
      final ObjectMapper objectMapper = new ObjectMapper();
      objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

      final CollectionType assignmentsType =
          objectMapper.getTypeFactory().constructCollectionType(List.class, Assignment.class);

      final List<Assignment> assignments =
          objectMapper.readValue(jsonAssignments.toString(), assignmentsType);

      final TypeReference<Map<String, String>> mapTypeReference = new TypeReference<>() {};
      final Map<String, String> featureFlags =
          objectMapper.readValue(jsonFeatureFlags.toString(), mapTypeReference);

      final CollectionType ghostAssignmentsType =
          objectMapper.getTypeFactory().constructCollectionType(List.class, GhostAssignment.class);
      final List<GhostAssignment> ghostAssignments =
          objectMapper.readValue(jsonGhostAssignments.toString(), ghostAssignmentsType);

      return new AssignmentDataResponse(assignments, featureFlags, ghostAssignments);
    } catch (Exception e) {
      fail("failed to serialize JSONObject into AssignmentDataResponse");
      return null;
    }
  }
}
