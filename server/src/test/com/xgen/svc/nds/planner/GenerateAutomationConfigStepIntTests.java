package com.xgen.svc.nds.planner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.atm.core._public.svc.LastAgentStatusSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.brs.core._public.svc.BackupDeploymentSvc;
import com.xgen.cloud.brs.daemon._public.grid.svc.BackupRestoreJobSvc;
import com.xgen.cloud.brs.restore._public.svc.AutomatedBackupRestoreSvc;
import com.xgen.cloud.brs.web._public.svc.ParallelRestoreSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.common.util._public.logging.LogLevel;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.common.util._public.util.MapUtils;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupRestoreJobRequest;
import com.xgen.cloud.cps.restore._public.model.BackupRestoreJobRequest.StreamingRestoreStrategy;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.RequestingUser;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Status;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Type;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.RestoreTarget;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate.AwsSnapshotFieldBuilder;
import com.xgen.cloud.deployment._public.model.AdditionalParams;
import com.xgen.cloud.deployment._public.model.AtlasProxyConfig;
import com.xgen.cloud.deployment._public.model.AuthMechanism;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.IndexConfig;
import com.xgen.cloud.deployment._public.model.LDAP;
import com.xgen.cloud.deployment._public.model.MaintainedEnvoyConfig;
import com.xgen.cloud.deployment._public.model.MaintainedEnvoyType;
import com.xgen.cloud.deployment._public.model.MaintainedMongotuneConfig;
import com.xgen.cloud.deployment._public.model.MaintainedRamiConfig;
import com.xgen.cloud.deployment._public.model.MongotConfig;
import com.xgen.cloud.deployment._public.model.MongotTemplate;
import com.xgen.cloud.deployment._public.model.OIDC;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessArguments;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6.Security;
import com.xgen.cloud.deployment._public.model.ProcessType;
import com.xgen.cloud.deployment._public.model.ReplicaSet;
import com.xgen.cloud.deployment._public.model.ShardedCluster;
import com.xgen.cloud.deployment._public.model.ShardedCollection;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.deployment._public.model.Tag;
import com.xgen.cloud.deployment._public.model.TagRangeValue;
import com.xgen.cloud.deployment._public.model.TlsMode;
import com.xgen.cloud.deployment._public.model.UISKeyAgentView;
import com.xgen.cloud.deployment._public.model.WithProcessArguments;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.federation._public.model.FederationSettings;
import com.xgen.cloud.federation._public.model.IdentityProvider;
import com.xgen.cloud.federation._public.model.OidcIdentityProvider;
import com.xgen.cloud.federation._public.svc.FederationAppIdentityProvidersSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.ifrabtest._public.model.ExperimentStatus;
import com.xgen.cloud.ifrabtest._public.model.IFRRolloutConfig.RolloutType;
import com.xgen.cloud.monitoring.topology._public.svc.HostClusterSvc;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAccount.AWSAccountBuilder;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegion;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.autoscaling.ui.AWSAutoScalingView;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamilyFlags;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterUpdateContext;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.SubdomainLevel;
import com.xgen.cloud.nds.common._public.model.ReplicaSetType;
import com.xgen.cloud.nds.dbcheck._public.svc.DbCheckSvc;
import com.xgen.cloud.nds.deployment._public.util.AutomationConfigDeploymentBuilderFactory;
import com.xgen.cloud.nds.deployment._public.util.RollableValueStateSvc;
import com.xgen.cloud.nds.free._public.model.SharedMTMCluster;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.svc.SearchIndexConfigSvc;
import com.xgen.cloud.nds.hostname._public.svc.NDSHostnameSvc;
import com.xgen.cloud.nds.ifr._public.model.IFRManifest;
import com.xgen.cloud.nds.ifr._public.planner.IFRPlanningUtil;
import com.xgen.cloud.nds.ifr._public.svc.IFRManifestSvc;
import com.xgen.cloud.nds.mongotune.policies.util._public.svc.MongotunePolicyInitializationSvc;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionProcessArgsDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionUpdatesDao;
import com.xgen.cloud.nds.project._private.dao.CustomMongoDbBuildDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._private.dao.versions.PhasedVersionDao;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.InternalClusterRole;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ProcessRestartAllowedState;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.RestoreJobType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgsUpdatable;
import com.xgen.cloud.nds.project._public.model.GeoSharding;
import com.xgen.cloud.nds.project._public.model.MongotProcessArgs;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus.State;
import com.xgen.cloud.nds.project._public.model.NDSAtlasProxyDefaults;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSFTSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSGroupMaintenanceWindow;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSLDAP;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSUserSecurity;
import com.xgen.cloud.nds.project._public.model.versions.FixedAgentVersion;
import com.xgen.cloud.nds.project._public.model.versions.IFRState;
import com.xgen.cloud.nds.project._public.model.versions.IFRState.WaveStatus;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersionParameters;
import com.xgen.cloud.nds.project._public.model.versions.ReleaseMode;
import com.xgen.cloud.nds.project._public.svc.VersionDeprecationSettingsSvc;
import com.xgen.cloud.nds.project._public.svc.versions.PhasedVersionSvc;
import com.xgen.cloud.nds.project._public.util.NDSTestOIDCIdp;
import com.xgen.cloud.nds.serverless._private.dao.AtlasUISKeysDao;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMPoolDao;
import com.xgen.cloud.nds.serverless._public.model.AtlasUISKey;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.serverless._public.model.ServerlessNDSDefaults;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.pool.ServerlessMTMPool;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.strategy.ServerlessAutoScalingStrategyType;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.EnvoyInstance;
import com.xgen.cloud.nds.serverless._public.svc.AtlasUISKeysSvc;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.search.common._public.svc.SearchHostAdminOverridesSvc;
import com.xgen.cloud.search.decoupled.config._public.model.Migration;
import com.xgen.cloud.search.decoupled.config._public.model.Migration.Target;
import com.xgen.cloud.search.decoupled.config._public.model.SearchConfigModelTestFactory;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.cloud.search.decoupled.external._public.svc.SearchInstanceSvc;
import com.xgen.cloud.search.envoy._public.config.EnvoyOnAtlasHost;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Plan;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Step;
import com.xgen.module.federation.dao.FederationSettingsDao;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.NDSFederationTestingUtils;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.model.CrossCloudReplicationSpecModelTestFactory;
import com.xgen.svc.nds.model.NDSAutomationTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ServerlessDeploymentModelTestFactory;
import com.xgen.svc.nds.model.ui.BiConnectorView;
import com.xgen.svc.nds.model.ui.ClusterDescriptionProcessArgsView;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.svc.nds.model.ui.MongotProcessArgsView;
import com.xgen.svc.nds.model.ui.ReplicationSpecViewUtils;
import com.xgen.svc.nds.planner.snapshot.RestoreStepUtils;
import com.xgen.svc.nds.serverless.model.ServerlessTestFactory;
import com.xgen.svc.nds.serverless.svc.ServerlessMTMPoolSvc;
import com.xgen.svc.nds.svc.ConnectionRuleWithHostnamesSvc;
import com.xgen.svc.nds.svc.FTSIndexConfigSvc;
import com.xgen.svc.nds.svc.MongotConfigSvc;
import com.xgen.svc.nds.svc.NDSMongotConfigSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class GenerateAutomationConfigStepIntTests extends JUnit5NDSBaseTest {

  @Inject private ClusterDescriptionUpdatesDao _clusterDescriptionUpdatesDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSClusterSvc _clusterSvc;
  @Inject private NDSMongotConfigSvc _ndsMongotConfigSvc;
  @Inject private PlanDao _planDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AutomationMongoDbVersionSvc _versionSvc;
  @Inject private NDSPlanContext.Factory _planContextFactory;
  @Inject private FeatureFlagSvc _featureFlagSvc;
  @Inject private PhasedVersionDao _phasedVersionDao;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private AppSettings _appSettings;
  @Inject private FTSIndexConfigSvc _ftsIndexConfigSvc;
  @Inject private MongotConfigSvc _mongotConfigSvc;
  @Inject private NDSHostnameSvc _ndsHostnameSvc;
  @Inject private HostClusterSvc _hostClusterSvc;
  @Inject private CustomMongoDbBuildDao _customMongoDbBuildDao;
  @Inject private AutomatedBackupRestoreSvc _automatedBackupRestoreSvc;
  @Inject private BackupRestoreJobSvc _backupRestoreJobSvc;
  @Inject private BackupDeploymentSvc _backupDeploymentSvc;
  @Inject private OnlineArchiveDao _onlineArchiveDao;
  @Inject private ClusterDescriptionProcessArgsDao _clusterDescriptionProcessArgsDao;
  @Inject private BackupRestoreJobDao _backupRestoreJobDao;
  @Inject private BackupSnapshotDao _backupSnapshotDao;
  @Inject private NDSGroupMaintenanceSvc _ndsGroupMaintenanceSvc;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private ParallelRestoreSvc _parallelRestoreSvc;
  @Inject private ConnectionRuleWithHostnamesSvc _connectionRuleWithHostnamesSvc;
  @Inject private RestoreStepUtils _restoreStepUtils;
  @Inject private CpsSvc _cpsSvc;
  @Inject private SearchDeploymentDescriptionSvc _searchDeploymentDescriptionSvc;
  @Inject private AtlasUISKeysSvc _atlasUISKeysSvc;
  @Inject private AtlasUISKeysDao _atlasUISKeysDao;
  @Inject private ServerlessMTMPoolSvc _serverlessMtmPoolSvc;
  @Inject private ServerlessMTMPoolDao _serverlessMTMPoolDao;
  @Inject private DbCheckSvc _dbCheckSvc;
  @Inject private SearchIndexConfigSvc _searchIndexConfigSvc;
  @Inject private LastAgentStatusSvc _lastAgentStatusSvc;
  @Inject private PhasedVersionSvc _phasedVersionSvc;
  @Inject private PhasedVersion.PhasedVersionFactory _phasedVersionFactory;
  @Inject private VersionDeprecationSettingsSvc _versionDeprecationSettingsSvc;
  @Inject private RollableValueStateSvc _rollableValueStateSvc;
  @Inject private SearchHostAdminOverridesSvc _searchHostAdminOverridesSvc;
  @Inject private MongotunePolicyInitializationSvc _mongotunePolicyInitializationSvc;
  @Inject private IFRPlanningUtil _ifrPlanningUtil;
  @Inject private IFRManifestSvc _ifrManifestSvc;

  @Inject
  private AutomationConfigDeploymentBuilderFactory _automationConfigDeploymentBuilderFactory;

  @Inject private MTMClusterDao _mtmClusterDao;
  @Inject private FederationAppIdentityProvidersSvc _federationAppIdentityProvidersSvc;
  @Inject private FederationSettingsDao _federationSettingsDao;
  @Inject private OrganizationDao _organizationDao;
  @Inject private SearchInstanceSvc _searchInstanceSvc;

  private NDSGroup _ndsGroup;
  private Group _group;
  private Organization _organization;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    final List<AWSRegionName> requiredContainers =
        Arrays.asList(
            AWSRegionName.US_EAST_1, AWSRegionName.EU_CENTRAL_1, AWSRegionName.AP_SOUTHEAST_1);

    _versionSvc.invalidateVersionManifestCache();
    _versionSvc.autoUpdateDefaultVersions();
    _clusterSvc.start();

    _organization = MmsFactory.createOrganizationWithNDSPlan("Test");
    _group = MmsFactory.createGroup(_organization, "cus_0001");
    MmsFactory.createUser(_group);
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);
    final AWSAccount awsAccount =
        new AWSAccountBuilder().setName("test").setAccessKey("test").setSecretKey("est").build();
    requiredContainers.forEach(
        region -> {
          awsAccount.getRegions().add(new AWSRegion(region));
        });
    _awsAccountDao.save(awsAccount);

    requiredContainers.forEach(
        region -> {
          final BasicDBObject containerObj = NDSModelTestFactory.getAWSContainer(region);
          containerObj.append("awsAccountId", awsAccount.getId());

          final AWSCloudProviderContainer awsContainer =
              new AWSCloudProviderContainer(containerObj);
          _ndsGroupDao.addCloudContainer(_group.getId(), awsContainer);
        });

    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
  }

  private AutomationConfig testAutomationConfigForMultiRegionReplicaSet(
      final List<RegionConfig> regionConfigs) throws Exception {
    final ClusterDescription clusterDescription =
        getAWSClusterDescription(NDSModelTestFactory.MONGODB_VERSION_CURRENT, regionConfigs, "foo");
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> stepResult = step.perform();
    assertTrue(stepResult.getStatus().isDone());

    return stepResult.getData().getAutomationConfig();
  }

  @Test
  public void testPredefinedGetLastErrorModesForTreeRegionReplicaSet() throws Exception {
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 1, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.EU_CENTRAL_1, RegionConfig.MAX_PRIORITY - 1, 1, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.AP_SOUTHEAST_1, RegionConfig.MAX_PRIORITY - 2, 1, 0, 0, 0));

    final AutomationConfig resultConfig =
        testAutomationConfigForMultiRegionReplicaSet(regionConfigs);

    // check predefined getLastErrorModes
    for (ReplicaSet rs : resultConfig.getDeployment().getReplicaSets()) {
      Map<String, Map<String, Integer>> getLastErrorModes = rs.getSettings().getGetLastErrorModes();
      assertNotNull(getLastErrorModes);
      assertEquals(2, getLastErrorModes.size());
      assertEquals(Map.of("region", 2), getLastErrorModes.get("twoRegions"));
      assertEquals(Map.of("region", 3), getLastErrorModes.get("threeRegions"));
    }
  }

  @Test
  public void testPredefinedGetLastErrorModesForTwoRegionReplicaSet() throws Exception {
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.AP_SOUTHEAST_1, RegionConfig.MAX_PRIORITY - 1, 1, 0, 0, 0));

    final AutomationConfig resultConfig =
        testAutomationConfigForMultiRegionReplicaSet(regionConfigs);

    // check predefined getLastErrorModes
    for (ReplicaSet rs : resultConfig.getDeployment().getReplicaSets()) {
      Map<String, Map<String, Integer>> getLastErrorModes = rs.getSettings().getGetLastErrorModes();
      assertNotNull(getLastErrorModes);
      assertEquals(1, getLastErrorModes.size());
      assertEquals(Map.of("region", 2), getLastErrorModes.get("twoRegions"));
    }
  }

  @Test
  public void testPredefinedGetLastErrorModesForSingleRegionReplicaSet() throws Exception {
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0));

    final AutomationConfig resultConfig =
        testAutomationConfigForMultiRegionReplicaSet(regionConfigs);

    // check predefined getLastErrorModes
    for (ReplicaSet rs : resultConfig.getDeployment().getReplicaSets()) {
      Map<String, Map<String, Integer>> getLastErrorModes = rs.getSettings().getGetLastErrorModes();
      assertNull(getLastErrorModes);
    }
  }

  @Test
  public void testPredefinedGetLastErrorModesForTwoRegionsWithNonElectableNodesReplicaSet()
      throws Exception {
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.EU_CENTRAL_1, 0, 0, 1, 1, 0));

    final AutomationConfig resultConfig =
        testAutomationConfigForMultiRegionReplicaSet(regionConfigs);

    // check predefined getLastErrorModes
    for (ReplicaSet rs : resultConfig.getDeployment().getReplicaSets()) {
      Map<String, Map<String, Integer>> getLastErrorModes = rs.getSettings().getGetLastErrorModes();
      assertNull(getLastErrorModes);
    }
  }

  @Test
  public void testGenerateShardedClusterWithAllowDeprecated() throws Exception {
    // This test is intentionally testing with MongoDB 4.2 so needs the deprecated flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _organization, FeatureFlag.ATLAS_ALLOW_DEPRECATED_VERSIONS);
    final ReplicationSpec replicationSpec =
        CrossCloudReplicationSpecModelTestFactory.getDefaultCrossCloudReplicationSpec();

    final ShardedClusterDescription clusterDescription0 =
        getGeoShardedClusterDescription(
                Collections.singletonList(replicationSpec),
                GeoShardingView.getDefaultGeoShardingView().toGeoSharding(),
                "foo0")
            .copy()
            .setMongoDBMajorVersion(
                _versionDeprecationSettingsSvc
                    .getAtlasMinimumMongoDBVersionSupportedByAgent()
                    .getMajorVersionString())
            .setMongoDBVersion(
                _versionDeprecationSettingsSvc
                    .getAtlasMinimumMongoDBVersionSupportedByAgent()
                    .getVersion())
            .build();
    _clusterSvc.createDefaultProcessArgs(
        _group, clusterDescription0, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription0,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    forceHardwareToProvisioned(_ndsGroup, clusterDescription0, "123.456.789.000");

    final ShardedClusterDescription clusterDescription1 =
        getGeoShardedClusterDescription(
                Collections.singletonList(replicationSpec),
                GeoShardingView.getDefaultGeoShardingView().toGeoSharding(),
                "foo1")
            .copy()
            .setMongoDBMajorVersion(
                _versionDeprecationSettingsSvc
                    .getAtlasMinimumMongoDBVersionSupportedByAgent()
                    .getMajorVersionString())
            .setMongoDBVersion(
                _versionDeprecationSettingsSvc
                    .getAtlasMinimumMongoDBVersionSupportedByAgent()
                    .getVersion())
            .build();
    _clusterSvc.createDefaultProcessArgs(
        _group, clusterDescription1, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription1,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    forceHardwareToProvisioned(_ndsGroup, clusterDescription1, "987.654.321.000");

    final Plan plan = new Plan(_ndsGroup.getGroupId(), _planContextFactory);
    plan.addMove(new DummyMove());
    _planDao.save(plan);

    final List<Cluster> clusters =
        Stream.of(clusterDescription0, clusterDescription1)
            .map(
                c ->
                    Cluster.getCluster(
                        c,
                        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), c.getName())))
            .collect(Collectors.toList());

    final GenerateAutomationConfigStep step = getAutomationConfigStep(plan, clusters);

    // Safe replica set reconfig requires only one publish when creating clusters.
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertCommon(clusterDescription0, resultConfig);
    assertCommon(clusterDescription1, resultConfig);

    // Verify processes
    assertEquals(
        6,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOS)
            .count());
    assertEquals(
        9,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOD)
            .count());
    assertEquals(
        3,
        resultConfig.getDeployment().getProcesses().stream().filter(Process::isConfigSvr).count());

    assertMaxIncomingConnectionsOverrides(clusters, resultConfig);
    assertAuthRestrictions(_ndsGroup, clusters, resultConfig);
  }

  @Test
  public void testGenerateShardedCluster_PhasedArgs() throws Exception {
    final ClusterDescription clusterWithExistingArgs =
        getAWSClusterDescription(VersionUtils.parse("7.0.0"), "cluster1");
    final ClusterDescription clusterWithNoArgs =
        getAWSClusterDescription(VersionUtils.parse("7.0.0"), "cluster2");

    final GenerateAutomationConfigStep step1 = getPreparedStepForCluster(clusterWithExistingArgs);
    final GenerateAutomationConfigStep step2 = getPreparedStepForCluster(clusterWithNoArgs);

    final Map<String, Object> phasedReleaseArgs =
        Map.of("parameter1", "newValue", "parameter3", "value3");

    setArgsSetParameters(
        clusterWithExistingArgs,
        Map.of("parameter1", "value1", "parameter2", "value2"),
        phasedReleaseArgs);

    setArgsSetParameters(clusterWithNoArgs, Map.of(), phasedReleaseArgs);

    final Result<GenerateAutomationConfigBaseStep.Data> resultExistingArgs = step1.perform();
    final Result<GenerateAutomationConfigBaseStep.Data> resultNoArgs = step2.perform();
    assertTrue(resultExistingArgs.getStatus().isDone());
    assertTrue(resultNoArgs.getStatus().isDone());

    // After running the step, the result of merging shardArgs and phasedShardArgs is reflected
    // directly on the arguments used to build the Process class for each process in the cluster.

    // parameter1 should not have been overridden
    final Map<String, Object> expectedArgs =
        Map.of("parameter1", "value1", "parameter2", "value2", "parameter3", "value3");
    resultExistingArgs
        .getData()
        .getAutomationConfig()
        .getDeployment()
        .getProcesses()
        .forEach(
            process -> {
              assertTrue(
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .entrySet()
                      .containsAll(expectedArgs.entrySet()));
            });

    // A cluster with no custom shard args will get whatever is defined in the phased version.
    resultNoArgs
        .getData()
        .getAutomationConfig()
        .getDeployment()
        .getProcesses()
        .forEach(
            process -> {
              assertTrue(
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .entrySet()
                      .containsAll(phasedReleaseArgs.entrySet()));
            });
  }

  private void setArgsSetParameters(
      final ClusterDescription pClusterDescription,
      Map<String, Object> pShardSetParameters,
      Map<String, Object> pPhasedSetParameters) {
    final Optional<ClusterDescriptionProcessArgsUpdatable> argsOpt =
        _clusterDescriptionProcessArgsDao.findForUpdate(
            pClusterDescription.getName(),
            pClusterDescription.getGroupId(),
            ClusterDescriptionProcessArgs.Type.STANDARD);

    argsOpt.ifPresent(
        args -> {
          args.getShardArg().get().setSetParameterMap(pShardSetParameters);
          args.getPhasedVersionShardArg().get().setSetParameterMap(pPhasedSetParameters);
          _clusterDescriptionProcessArgsDao.save(args);
        });
  }

  private void assertMaxIncomingConnectionsOverrides(
      final List<Cluster> pClusters, final AutomationConfig pResultConfig) {
    pClusters.stream()
        .filter(
            c ->
                c.getClusterDescription()
                    .getMongoDBVersion()
                    .isGreaterThanOrEqualTo(VersionUtils.THREE_SIX_ZERO))
        .map(Cluster::getReplicaSets)
        .flatMap(Collection::stream)
        .flatMap(ReplicaSetHardware::getAllHardware)
        .forEach(
            ih -> {
              final List<String> defaultOverrides = List.of("127.0.0.1/32");
              final String containerCIDR =
                  _ndsGroup
                      .getDedicatedCloudProviderContainer(ih.getCloudContainerId())
                      .get()
                      .getAtlasCidr();
              final List<String> expectedOverrides =
                  Stream.concat(defaultOverrides.stream(), Stream.of(containerCIDR))
                      .collect(Collectors.toList());
              pResultConfig
                  .getDeployment()
                  .getProcessesByHostname(ih.getHostnameForAgents().get())
                  .stream()
                  .map(Process::getMaxIncomingConnectionsOverride)
                  .forEach(o -> assertEquals(expectedOverrides, o));
            });
  }

  @Test
  public void testGenerateCluster_AWSAuthNotSupported() throws Exception {
    // Since AWS is only unsupported on deprecated versions, enable deprecated versions and
    // explicitly test on 4.2.
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_ALLOW_DEPRECATED_VERSIONS);

    // test aws auth not supported (version < 4.3.0)
    final ClusterDescription clusterDescription = getAWSClusterDescription("4.2");
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    assertTrue(result.getStatus().isDone());

    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertFalse(
        resultConfig
            .getDeployment()
            .getAuth()
            .getDeploymentAuthMechanisms()
            .contains(AuthMechanism.MONGODB_AWS));
    assertFalse(
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs)
            .map(ProcessArguments::getSetParameterMap)
            .anyMatch(
                setParameterMap ->
                    setParameterMap.containsKey(ProcessArguments2_6.FieldDefs.AWS_STS_URL)));
    assertFalse(
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs2_6)
            .map(ProcessArguments2_6::getAwsSTSUrl)
            .anyMatch(Objects::nonNull));
  }

  @Test
  public void testGenerateCluster_AWSAuthSupported_LocalRegionSTS() throws Exception {
    // test aws auth supported (version >= 4.3.0)
    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    assertTrue(result.getStatus().isDone());

    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertTrue(
        resultConfig
            .getDeployment()
            .getAuth()
            .getDeploymentAuthMechanisms()
            .contains(AuthMechanism.MONGODB_AWS));
    assertTrue(
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs)
            .map(ProcessArguments::getSetParameterMap)
            .allMatch(
                setParameterMap ->
                    setParameterMap.containsKey(ProcessArguments2_6.FieldDefs.AWS_STS_URL)));
    assertTrue(
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs2_6)
            .map(ProcessArguments2_6::getAwsSTSUrl)
            .allMatch(Objects::nonNull));

    final String expectedAwsStsUrl =
        String.format(NDSDefaults.AWS_STS_URL_FORMAT, AWSRegionName.US_EAST_1.getValue());
    assertTrue(
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs2_6)
            .map(ProcessArguments2_6::getAwsSTSUrl)
            .allMatch(expectedAwsStsUrl::equals));
  }

  @Test
  public void testGenerateShardedCluster_AWSAuthSupported_LocalRegionSTS() throws Exception {
    // test aws auth supported (version >= 4.3.0)
    final ClusterDescription clusterDescription =
        new ShardedClusterDescription(
                NDSModelTestFactory.getAWSShardedClusterDescription(_ndsGroup.getGroupId()))
            .copy()
            .setMongoDBVersion(NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .build();
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    assertTrue(result.getStatus().isDone());

    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertTrue(
        resultConfig
            .getDeployment()
            .getAuth()
            .getDeploymentAuthMechanisms()
            .contains(AuthMechanism.MONGODB_AWS));
    assertTrue(
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs)
            .map(ProcessArguments::getSetParameterMap)
            .allMatch(
                setParameterMap ->
                    setParameterMap.containsKey(ProcessArguments2_6.FieldDefs.AWS_STS_URL)));
    assertTrue(
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs2_6)
            .map(ProcessArguments2_6::getAwsSTSUrl)
            .allMatch(Objects::nonNull));

    final String expectedAwsStsUrl =
        String.format(NDSDefaults.AWS_STS_URL_FORMAT, AWSRegionName.US_EAST_1.getValue());
    assertTrue(
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs2_6)
            .map(ProcessArguments2_6::getAwsSTSUrl)
            .allMatch(expectedAwsStsUrl::equals));
  }

  private void testGenerateCluster_EnableTestCommands(
      final boolean pEnableTestCommands, final Boolean pEnableTestCommandsSetParameter)
      throws Exception {
    final AppUser appUser = MmsFactory.createUser(_group);
    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());

    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);

    if (pEnableTestCommands) {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_ENABLE_TEST_COMMANDS);
    } else {
      FeatureFlagIntTestUtil.disableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_ENABLE_TEST_COMMANDS);
    }

    if (pEnableTestCommandsSetParameter != null) {
      final var argsWithTestCommandEnabledInShardArgs =
          _clusterSvc.getProcessArgs(_group.getId(), clusterDescription.getName()).get();
      argsWithTestCommandEnabledInShardArgs
          .getShardArg()
          .get()
          .setEnableTestCommands(pEnableTestCommandsSetParameter);
      _clusterSvc.updateProcessArgs(
          _group,
          clusterDescription.getName(),
          new ClusterDescriptionProcessArgsView(
              argsWithTestCommandEnabledInShardArgs,
              Optional.of(clusterDescription.getMongoDBMajorVersion())),
          AuditInfoHelpers.fromSystem(),
          null,
          appUser);
    }

    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    assertTrue(result.getStatus().isDone());

    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertTrue(
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs)
            .map(ProcessArguments::getSetParameterMap)
            .allMatch(
                setParameterMap ->
                    setParameterMap.containsKey(ProcessArguments2_6.FieldDefs.ENABLE_TEST_COMMANDS)
                        == (pEnableTestCommandsSetParameter != null || pEnableTestCommands)));

    final List<Boolean> enableTestCommands =
        resultConfig.getDeployment().getProcesses().stream()
            .map(Process::getArgs2_6)
            .map(ProcessArguments2_6::getEnableTestCommands)
            .collect(Collectors.toList());

    final Boolean expectedEnableTestCommands =
        pEnableTestCommandsSetParameter != null
            ? pEnableTestCommandsSetParameter
            : pEnableTestCommands ? true : null;

    assertTrue(
        enableTestCommands.stream().allMatch(v -> v == expectedEnableTestCommands),
        String.format(
            "Expect all enableTestCommands to be %s but was %s",
            expectedEnableTestCommands, enableTestCommands));
  }

  @Test
  public void testGenerateCluster_EnableTestCommands_Enabled_SetParam_Null() throws Exception {
    testGenerateCluster_EnableTestCommands(true, null);
  }

  @Test
  public void testGenerateCluster_EnableTestCommands_Disabled_SetParam_Null() throws Exception {
    testGenerateCluster_EnableTestCommands(false, null);
  }

  @Test
  public void testGenerateCluster_EnableTestCommands_Enabled_SetParam_False() throws Exception {
    testGenerateCluster_EnableTestCommands(true, false);
  }

  @Test
  public void testGenerateCluster_EnableTestCommands_Disabled_SetParam_False() throws Exception {
    testGenerateCluster_EnableTestCommands(false, false);
  }

  @Test
  public void testGenerateCluster_EnableTestCommands_Enabled_SetParam_True() throws Exception {
    testGenerateCluster_EnableTestCommands(true, true);
  }

  @Test
  public void testGenerateCluster_EnableTestCommands_Disabled_SetParam_True() throws Exception {
    testGenerateCluster_EnableTestCommands(false, true);
  }

  @Test
  public void testGenerateClusterWithFTS_SearchQueryTelemetryEnabled() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _organization, FeatureFlag.ATLAS_SEARCH_QUERY_TELEMETRY);

    // case: FTS not enabled
    final ClusterDescription clusterDescription =
        getAWSClusterDescription(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result0 = step.perform();
    assertTrue(result0.getStatus().isDone());
    final AutomationConfig resultConfig0 = result0.getData().getAutomationConfig();

    assertEquals(0, resultConfig0.getDeployment().getMongots().size());
    // mongot template is always set
    assertNotNull(resultConfig0.getMongotTemplate().getVersion());
    resultConfig0
        .getDeployment()
        .getProcesses()
        .forEach(
            proc -> {
              assertNull(proc.getProfilingConfig());
            });

    // case: replica set w FTS enabled
    _ftsIndexConfigSvc.addFTSIndex(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        NDSModelTestFactory.getFTSIndex(),
        null);

    final Result<GenerateAutomationConfigBaseStep.Data> result1 = step.perform();
    assertTrue(result1.getStatus().isDone());
    final AutomationConfig resultConfig1 = result1.getData().getAutomationConfig();
    assertEquals(3, resultConfig1.getDeployment().getMongots().size());

    final String expectedFilterExpr =
        "{\"$or\":[{\"millis\":{\"$gte\":100}},{\"$and\":[{\"$sampleRate\":0.25},{\"command.pipeline.0.$search.tracking.searchTerms\":{\"$exists\":true}}]}]}";
    resultConfig1
        .getDeployment()
        .getProcesses()
        .forEach(
            proc -> {
              assertEquals(expectedFilterExpr, proc.getProfilingConfig().getProfilingLevelFilter());
              assertEquals(0, proc.getProfilingConfig().getProfilingLevel().intValue());
            });

    // case: sharded cluster w FTS enabled
    final ReplicationSpec replicationSpec =
        CrossCloudReplicationSpecModelTestFactory.getDefaultCrossCloudReplicationSpec();

    final ShardedClusterDescription shardedClusterDescription =
        getGeoShardedClusterDescription(
                Collections.singletonList(replicationSpec),
                GeoShardingView.getDefaultGeoShardingView().toGeoSharding(),
                "foo0")
            .copy()
            .setMongoDBMajorVersion(
                NDSModelTestFactory.TEST_MONGODB_VERSION.getMajorVersionString())
            .setMongoDBVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion())
            .build();

    final GenerateAutomationConfigStep step2 = getPreparedStepForCluster(shardedClusterDescription);

    _ftsIndexConfigSvc.addFTSIndex(
        shardedClusterDescription.getGroupId(),
        shardedClusterDescription.getName(),
        NDSModelTestFactory.getFTSIndex(),
        null);

    final Result<GenerateAutomationConfigBaseStep.Data> result2 = step2.perform();
    assertTrue(result2.getStatus().isDone());
    final AutomationConfig resultConfig2 = result2.getData().getAutomationConfig();
    assertEquals(6, resultConfig2.getDeployment().getMongots().size());
    resultConfig2
        .getDeployment()
        .getProcesses()
        .forEach(
            proc -> {
              if (proc.isMongos()) {
                assertEquals(
                    expectedFilterExpr, proc.getProfilingConfig().getProfilingLevelFilter());
                assertNull(proc.getProfilingConfig().getProfilingLevel());
              } else {
                assertNull(proc.getProfilingConfig());
              }
            });
  }

  @Test
  public void testGenerateClusterWithServerlessFilter() throws Exception {
    _appSettings.setProp(
        "mms.monitoring.serverlessProfilingFilter.enabled", "true", AppSettings.SettingType.MEMORY);

    // Group is a serverless mtm group
    _ndsGroupDao.setMTMPassword(_ndsGroup.getGroupId(), EncryptionUtils.genEncryptStr("secure"));
    _ndsGroupDao.setServerlessMTMHolder(_ndsGroup.getGroupId());
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();
    final ServerlessMTMPool pool =
        ServerlessTestFactory.getServerlessMTMPool(
            new ObjectId(),
            "pool0",
            _ndsGroup.getGroupId(),
            CloudProvider.AWS,
            ServerlessAutoScalingStrategyType.V2,
            new ArrayList<>(),
            new ArrayList<>(),
            true,
            true,
            new ArrayList<>());
    _serverlessMTMPoolDao.createPool(pool);
    final EnvoyInstance instance =
        ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(new ObjectId(), 0);
    // Insert a key into the DB (simulate a UIS uploading a key)
    _atlasUISKeysDao.createKey(
        new AtlasUISKey(
            new ObjectId(),
            pool.getId(),
            instance.getId(),
            Date.from(Instant.now()),
            "TestPEM" + instance.getId(),
            null,
            null));

    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_UPCOMING.getMajorVersionString());
    final ClusterDescription mtmClusterDescription =
        getAWSClusterDescription(
                NDSModelTestFactory.MONGODB_VERSION_UPCOMING.getMajorVersionString())
            .copy()
            .setIsMTM(true)
            .setName(clusterDescription.getName() + "-MTM")
            .build();
    final GenerateAutomationConfigStep mtmStep = getPreparedStepForCluster(mtmClusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result = mtmStep.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig resultConfig1 = result.getData().getAutomationConfig();

    final String expectedFilterExpr =
        "{\"$or\": [{\"millis\": {\"$gte\": 100}}, {\"$expr\": {\"$gte\": [{\"$divide\":"
            + " [\"$docsExamined\", \"$nreturned\"]}, 10000]}}]}";
    resultConfig1
        .getDeployment()
        .getProcesses()
        .forEach(
            proc -> {
              assertEquals(expectedFilterExpr, proc.getProfilingConfig().getProfilingLevelFilter());
              assertEquals(0, proc.getProfilingConfig().getProfilingLevel().intValue());
            });

    // case: app setting is disabled
    _appSettings.setProp(
        "mms.monitoring.serverlessProfilingFilter.enabled",
        "false",
        AppSettings.SettingType.MEMORY);

    final ClusterDescription clusterDescription2 =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_UPCOMING.getMajorVersionString());
    final ClusterDescription mtmClusterDescription2 =
        clusterDescription2
            .copy()
            .setIsMTM(true)
            .setName(clusterDescription2.getName() + "-MTM2")
            .build();
    final GenerateAutomationConfigStep mtmStep2 = getPreparedStepForCluster(mtmClusterDescription2);
    final Result<GenerateAutomationConfigBaseStep.Data> result2 = mtmStep2.perform();
    assertTrue(result2.getStatus().isDone());
    final AutomationConfig resultConfig2 = result2.getData().getAutomationConfig();

    resultConfig2
        .getDeployment()
        .getProcesses()
        .forEach(
            proc -> {
              assertEquals(100, proc.getProfilingConfig().getProfilingLevelSlowMs().intValue());
              assertNull(proc.getProfilingConfig().getProfilingLevelFilter());
              assertEquals(0, proc.getProfilingConfig().getProfilingLevel().intValue());
            });
  }

  @Test
  public void testGenerateClusterWithFTS() throws Exception {
    // case: FTS not enabled
    final ClusterDescription clusterDescription = getAWSClusterDescription("6.0.0");
    final GenerateAutomationConfigStep step =
        getPreparedStepForClusterWithHostnames(
            clusterDescription,
            List.of(
                "foo-shard-00-00.grp123.mongodb.net",
                "foo-shard-00-01.grp123.mongodb.net",
                "foo-shard-00-02.grp123.mongodb.net"));
    final Result<GenerateAutomationConfigBaseStep.Data> result0 = step.perform();
    assertTrue(result0.getStatus().isDone());
    final AutomationConfig resultConfig0 = result0.getData().getAutomationConfig();

    assertEquals(0, resultConfig0.getDeployment().getMongots().size());
    // mongot template is always set
    assertNotNull(resultConfig0.getMongotTemplate().getVersion());
    resultConfig0
        .getDeployment()
        .getProcesses()
        .forEach(
            proc -> {
              assertNull(proc.getProfilingConfig());
              assertNull(proc.getArgs2_6().getOperationProfilingMode());
              assertNull(proc.getArgs2_6().getOperationProfilingFilter());
            });

    // case: replica set w FTS enabled
    _ftsIndexConfigSvc.addFTSIndex(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        NDSModelTestFactory.getFTSIndex(),
        null);

    final Result<GenerateAutomationConfigBaseStep.Data> result1 = step.perform();
    assertTrue(result1.getStatus().isDone());
    final AutomationConfig resultConfig1 = result1.getData().getAutomationConfig();

    final List<ReplicaSetHardware> hardware =
        _replicaSetHardwareDao.findByCluster(
            clusterDescription.getGroupId(), clusterDescription.getName());
    final List<String> hostnames =
        hardware.stream()
            .flatMap(h -> h.getHardware().stream())
            .map(h -> h.getHostnameForAgents().get())
            .collect(Collectors.toList());

    assertEquals(3, resultConfig1.getDeployment().getMongots().size());
    final List<Integer> mongotIds1 =
        resultConfig1.getDeployment().getMongots().stream()
            .map(MongotConfig::getId)
            .collect(Collectors.toList());
    assertNotNull(resultConfig1.getMongotTemplate().getVersion());
    resultConfig1
        .getDeployment()
        .getMongots()
        .forEach(
            mongotConfig -> {
              // ensure there's no duplicate ids
              assertEquals(1, Collections.frequency(mongotIds1, mongotConfig.getId()));
              assertTrue(hostnames.contains(mongotConfig.getHostname()));
              // version/urls should be populated by mongotTemplate.
              assertNull(mongotConfig.getVersion());
              assertNull(mongotConfig.getUrls());
              assertEquals(
                  clusterDescription.getDeploymentClusterName(), mongotConfig.getClusterName());
              assertEquals(NDSFTSDefaults.MONGOT_DATA_PATH, mongotConfig.getDataPath());
              assertEquals(
                  NDSFTSDefaults.MONGOT_CONFIG_BACKUP_PATH, mongotConfig.getConfigBackupPath());
              assertEquals(NDSFTSDefaults.MONGOT_LOG_LEVEL, mongotConfig.getLogLevel());
              assertEquals(
                  NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS, mongotConfig.getQueryServerAddress());
              assertEquals(NDSFTSDefaults.MONGOT_CRASH_LOG_PATH, mongotConfig.getCrashLogPath());
              assertEquals(
                  _appSettings.getMongotMmsConfigUpdatePeriodSeconds(),
                  mongotConfig.getAdditionalParams().get("mmsConfigUpdatePeriodSeconds"));
            });

    resultConfig1
        .getDeployment()
        .getProcesses()
        .forEach(
            proc -> {
              assertNull(proc.getProfilingConfig());
              assertNull(proc.getArgs2_6().getOperationProfilingMode());
              assertNull(proc.getArgs2_6().getOperationProfilingFilter());
            });

    // case: sharded cluster w FTS enabled
    final ReplicationSpec replicationSpec =
        CrossCloudReplicationSpecModelTestFactory.getDefaultCrossCloudReplicationSpec();

    final ShardedClusterDescription shardedClusterDescription =
        getGeoShardedClusterDescription(
                Collections.singletonList(replicationSpec),
                GeoShardingView.getDefaultGeoShardingView().toGeoSharding(),
                "foo0")
            .copy()
            .setMongoDBMajorVersion(
                NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .setMongoDBVersion(NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion())
            .build();

    final GenerateAutomationConfigStep step2 = getPreparedStepForCluster(shardedClusterDescription);

    _ftsIndexConfigSvc.addFTSIndex(
        shardedClusterDescription.getGroupId(),
        shardedClusterDescription.getName(),
        NDSModelTestFactory.getFTSIndex(),
        null);

    final Result<GenerateAutomationConfigBaseStep.Data> result2 = step2.perform();
    assertTrue(result2.getStatus().isDone());
    final AutomationConfig resultConfig2 = result2.getData().getAutomationConfig();

    final List<ReplicaSetHardware> shardedHardware =
        _replicaSetHardwareDao.findByCluster(
            shardedClusterDescription.getGroupId(), shardedClusterDescription.getName());
    @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
    final List<String> mongodHostnames =
        shardedHardware.stream()
            .filter(r -> r.getType().equals(ReplicaSetType.SHARD))
            .flatMap(h -> h.getHardware().stream())
            .map(h -> h.getHostnameForAgents().get())
            .collect(Collectors.toList());
    @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
    final List<String> configHostnames =
        shardedHardware.stream()
            .filter(r -> r.getType().equals(ReplicaSetType.CONFIG))
            .flatMap(h -> h.getHardware().stream())
            .map(h -> h.getHostnameForAgents().get())
            .collect(Collectors.toList());

    assertEquals(6, resultConfig2.getDeployment().getMongots().size());
    assertNotNull(resultConfig1.getMongotTemplate().getVersion());
    final List<Integer> mongotIds2 =
        resultConfig2.getDeployment().getMongots().stream()
            .map(MongotConfig::getId)
            .collect(Collectors.toList());
    resultConfig2
        .getDeployment()
        .getMongots()
        .forEach(
            mongotConfig -> {
              // ensure there's no duplicate ids
              assertEquals(1, Collections.frequency(mongotIds2, mongotConfig.getId()));
              assertTrue(mongodHostnames.contains(mongotConfig.getHostname()));
              assertFalse(configHostnames.contains(mongotConfig.getHostname()));
              // version/urls should be populated by mongotTemplate.
              assertNull(mongotConfig.getVersion());
              assertNull(mongotConfig.getUrls());
              assertEquals(
                  shardedClusterDescription.getDeploymentClusterName(),
                  mongotConfig.getClusterName());
              assertEquals(NDSFTSDefaults.MONGOT_DATA_PATH, mongotConfig.getDataPath());
              assertEquals(
                  NDSFTSDefaults.MONGOT_CONFIG_BACKUP_PATH, mongotConfig.getConfigBackupPath());
              assertEquals(NDSFTSDefaults.MONGOT_LOG_LEVEL, mongotConfig.getLogLevel());
              assertEquals(
                  NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS, mongotConfig.getQueryServerAddress());
              assertEquals(
                  _appSettings.getMongotMmsConfigUpdatePeriodSeconds(),
                  mongotConfig.getAdditionalParams().get("mmsConfigUpdatePeriodSeconds"));
            });
    assertEquals(0, resultConfig2.getDeployment().getMaintainedEnvoys().size());
    resultConfig2
        .getDeployment()
        .getProcesses()
        .forEach(
            proc -> {
              assertNull(proc.getProfilingConfig());
              assertNull(proc.getArgs2_6().getOperationProfilingMode());
              assertNull(proc.getArgs2_6().getOperationProfilingFilter());
            });

    {
      // Override mongot version for host foo-shard-00-00.grp123.mongodb.net only
      _searchHostAdminOverridesSvc.overrideMongotVersion(
          clusterDescription.getGroupId(), "foo-shard-00-00.grp123.mongodb.net", "1.50.0");
      final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
      assertTrue(result.getStatus().isDone());
      final AutomationConfig resultConfig = result.getData().getAutomationConfig();

      assertEquals(3, resultConfig.getDeployment().getMongots().size());
      assertNotNull(resultConfig.getMongotTemplate().getVersion());
      resultConfig
          .getDeployment()
          .getMongots()
          .forEach(
              mongotConfig -> {
                if (mongotConfig.getHostname().equals("foo-shard-00-00.grp123.mongodb.net")) {
                  // If admin override exists, version should be set accordingly.
                  assertEquals("1.50.0", mongotConfig.getVersion());
                } else {
                  // version should be populated by mongotTemplate when there is no overrides.
                  assertNull(mongotConfig.getVersion());
                }
                // urls should be populated by mongotTemplate.
                assertNull(mongotConfig.getUrls());
              });
    }

    // case: run mongot on remote hosts. decoupled feature flag is disabled and search index
    // management is not supported.
    _searchDeploymentDescriptionSvc.create(
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            clusterDescription.getGroupId(), clusterDescription.getUniqueId(), 1));
    final Result<GenerateAutomationConfigBaseStep.Data> result3 = step.perform();
    assertTrue(result3.getStatus().isDone());
    final AutomationConfig resultConfig3 = result3.getData().getAutomationConfig();
    assertEquals(3, resultConfig3.getDeployment().getMongots().size());
    assertEquals(0, resultConfig3.getDeployment().getMaintainedEnvoys().size());
    resultConfig3
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process -> {
              assertEquals(
                  NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS,
                  process.getArgs2_6().getSetParameterMap().get("mongotHost"));
            });

    // case: run mongot on remote hosts. decoupled feature flag is disabled and search index
    // management is supported.
    final ClusterDescription clusterDescription2 =
        getAWSClusterDescription_withClusterName("foo-1");
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_SEARCH_INDEX_MANAGEMENT);
    _searchDeploymentDescriptionSvc.create(
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            clusterDescription2.getGroupId(), clusterDescription2.getUniqueId(), 1));
    final GenerateAutomationConfigStep step3 =
        getPreparedStepForClusterWithHostnames(
            clusterDescription2,
            List.of(
                "foo-shard-00-00.grp123.mongodb.net",
                "foo-shard-00-01.grp123.mongodb.net",
                "foo-shard-00-02.grp123.mongodb.net"));
    _ftsIndexConfigSvc.addFTSIndex(
        clusterDescription2.getGroupId(),
        clusterDescription2.getName(),
        NDSModelTestFactory.getFTSIndex(),
        null);
    final Result<GenerateAutomationConfigBaseStep.Data> result4 = step3.perform();
    assertTrue(result4.getStatus().isDone());
    final AutomationConfig resultConfig4 = result4.getData().getAutomationConfig();
    assertEquals(3, resultConfig4.getDeployment().getMongots().size());
    assertEquals(3, resultConfig4.getDeployment().getMaintainedEnvoys().size());
    resultConfig4
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process -> {
              assertEquals(
                  NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS,
                  process.getArgs2_6().getSetParameterMap().get("mongotHost"));
            });

    resultConfig4
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process -> {
              assertEquals(
                  EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .get("searchIndexManagementHostAndPort"));
            });

    // case: run mongot on remote hosts. decoupled feature flag is enabled and search index
    // management is not supported.
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.DEDICATED_ATLAS_SEARCH_NODES);
    final Result<GenerateAutomationConfigBaseStep.Data> result5 = step.perform();
    assertTrue(result5.getStatus().isDone());
    final AutomationConfig resultConfig5 = result5.getData().getAutomationConfig();
    // SearchEnvoyTemplate should be set.
    assertNotNull(resultConfig5.getSearchEnvoyTemplate());
    assertEquals(0, resultConfig5.getDeployment().getMongots().size());
    assertEquals(3, resultConfig5.getDeployment().getMaintainedEnvoys().size());
    final List<Integer> maintainedEnvoyIds5 =
        resultConfig5.getDeployment().getMaintainedEnvoys().stream()
            .map(MaintainedEnvoyConfig::getId)
            .collect(Collectors.toList());
    resultConfig5
        .getDeployment()
        .getMaintainedEnvoys()
        .forEach(
            searchEnvoyConfig -> {
              // ensure there's no duplicate ids
              assertEquals(
                  1, Collections.frequency(maintainedEnvoyIds5, searchEnvoyConfig.getId()));
              assertTrue(hostnames.contains(searchEnvoyConfig.getHostname()));
              assertEquals(
                  MaintainedEnvoyType.SEARCH_MONGOD, searchEnvoyConfig.getMaintainedEnvoyType());
            });
    resultConfig5
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process -> {
              assertEquals(
                  EnvoyOnAtlasHost.MONGOT_UNIX_DOMAIN_SOCKET,
                  process.getArgs2_6().getSetParameterMap().get("mongotHost"));
            });

    // case: run mongot on remote hosts. decoupled feature flag is enabled and search index
    // management is supported.
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.DEDICATED_ATLAS_SEARCH_NODES);
    final Result<GenerateAutomationConfigBaseStep.Data> result6 = step3.perform();
    assertTrue(result6.getStatus().isDone());
    final AutomationConfig resultConfig6 = result6.getData().getAutomationConfig();
    // SearchEnvoyTemplate should be set.
    assertNotNull(resultConfig6.getSearchEnvoyTemplate());
    assertEquals(0, resultConfig6.getDeployment().getMongots().size());
    assertEquals(3, resultConfig6.getDeployment().getMaintainedEnvoys().size());
    final List<Integer> maintainedEnvoyIds6 =
        resultConfig6.getDeployment().getMaintainedEnvoys().stream()
            .map(MaintainedEnvoyConfig::getId)
            .collect(Collectors.toList());
    final List<String> maintainedEnvoyStaticConfig6 =
        resultConfig6.getDeployment().getMaintainedEnvoys().stream()
            .map(MaintainedEnvoyConfig::getStaticConfig)
            .collect(Collectors.toList());
    final List<String> maintainedEnvoyStaticConfig5 =
        resultConfig5.getDeployment().getMaintainedEnvoys().stream()
            .map(MaintainedEnvoyConfig::getStaticConfig)
            .collect(Collectors.toList());
    // The static config changed because search index management is also supported
    assertNotEquals(maintainedEnvoyStaticConfig5, maintainedEnvoyStaticConfig6);

    resultConfig6
        .getDeployment()
        .getMaintainedEnvoys()
        .forEach(
            searchEnvoyConfig -> {
              // ensure there's no duplicate ids
              assertEquals(
                  1, Collections.frequency(maintainedEnvoyIds6, searchEnvoyConfig.getId()));
              assertTrue(hostnames.contains(searchEnvoyConfig.getHostname()));
              assertEquals(
                  MaintainedEnvoyType.SEARCH_MONGOD, searchEnvoyConfig.getMaintainedEnvoyType());
            });
    resultConfig6
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process -> {
              assertEquals(
                  EnvoyOnAtlasHost.MONGOT_UNIX_DOMAIN_SOCKET,
                  process.getArgs2_6().getSetParameterMap().get("mongotHost"));
            });

    resultConfig6
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process -> {
              assertEquals(
                  EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .get("searchIndexManagementHostAndPort"));
            });

    // case: run mongot on remote hosts. Search migration is in-progress
    final ClusterDescription clusterDescription3 =
        getAWSClusterDescription_withClusterName("foo-17");
    _searchDeploymentDescriptionSvc.create(
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            clusterDescription3.getGroupId(),
            clusterDescription3.getUniqueId(),
            2,
            new Migration(Migration.Target.DEDICATED_NODES, Instant.now())));

    final GenerateAutomationConfigStep step4 =
        getPreparedStepForClusterWithHostnames(
            clusterDescription3,
            List.of(
                "foo-shard-00-00.grp123.mongodb.net",
                "foo-shard-00-01.grp123.mongodb.net",
                "foo-shard-00-02.grp123.mongodb.net"));
    _ftsIndexConfigSvc.addFTSIndex(
        clusterDescription3.getGroupId(),
        clusterDescription3.getName(),
        NDSModelTestFactory.getFTSIndex(),
        null);

    final Result<GenerateAutomationConfigBaseStep.Data> result7 = step4.perform();
    assertTrue(result7.getStatus().isDone());

    final AutomationConfig resultConfig7 = result7.getData().getAutomationConfig();
    assertEquals(3, resultConfig7.getDeployment().getMongots().size());
    assertEquals(3, resultConfig7.getDeployment().getMaintainedEnvoys().size());
    resultConfig7
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process ->
                assertEquals(
                    NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS,
                    process.getArgs2_6().getSetParameterMap().get("mongotHost")));

    resultConfig7
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process ->
                assertEquals(
                    EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
                    process
                        .getArgs2_6()
                        .getSetParameterMap()
                        .get("searchIndexManagementHostAndPort")));

    // case: run mongot on remote hosts. Search migration is completed
    final SearchDeploymentDescription searchDeployment3 =
        _searchDeploymentDescriptionSvc
            .findNonDeletedDeploymentBySourceMongoCluster(clusterDescription3.getUniqueId())
            .orElseThrow();
    _searchDeploymentDescriptionSvc.updateDeploymentMigrationState(
        searchDeployment3, Migration.State.COMPLETED);

    final Result<GenerateAutomationConfigBaseStep.Data> result8 = step4.perform();
    assertTrue(result8.getStatus().isDone());

    final AutomationConfig resultConfig8 = result8.getData().getAutomationConfig();
    assertEquals(0, resultConfig8.getDeployment().getMongots().size());
    assertEquals(3, resultConfig8.getDeployment().getMaintainedEnvoys().size());
    resultConfig8
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process ->
                assertEquals(
                    EnvoyOnAtlasHost.MONGOT_UNIX_DOMAIN_SOCKET,
                    process.getArgs2_6().getSetParameterMap().get("mongotHost")));

    resultConfig8
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process ->
                assertEquals(
                    EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
                    process
                        .getArgs2_6()
                        .getSetParameterMap()
                        .get("searchIndexManagementHostAndPort")));

    // case: run mongot on remote hosts. Search migration is in-progress
    final ClusterDescription clusterDescription4 =
        getAWSClusterDescription_withClusterName("foo-18");
    _searchDeploymentDescriptionSvc.create(
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            clusterDescription4.getGroupId(),
            clusterDescription4.getUniqueId(),
            2,
            new Migration(Target.COUPLED, Instant.now())));
    _searchDeploymentDescriptionSvc.updateDeploymentMigrationState(
        searchDeployment3, Migration.State.TARGET_STARTUP);

    final GenerateAutomationConfigStep step5 =
        getPreparedStepForClusterWithHostnames(
            clusterDescription4,
            List.of(
                "foo-shard-00-00.grp123.mongodb.net",
                "foo-shard-00-01.grp123.mongodb.net",
                "foo-shard-00-02.grp123.mongodb.net"));
    _ftsIndexConfigSvc.addFTSIndex(
        clusterDescription4.getGroupId(),
        clusterDescription4.getName(),
        NDSModelTestFactory.getFTSIndex(),
        null);

    final Result<GenerateAutomationConfigBaseStep.Data> result9 = step5.perform();
    assertTrue(result9.getStatus().isDone());

    final AutomationConfig resultConfig9 = result9.getData().getAutomationConfig();
    assertEquals(3, resultConfig9.getDeployment().getMongots().size());
    assertEquals(3, resultConfig9.getDeployment().getMaintainedEnvoys().size());
    resultConfig9
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process ->
                assertEquals(
                    NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS,
                    process.getArgs2_6().getSetParameterMap().get("mongotHost")));

    resultConfig9
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process ->
                assertEquals(
                    EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
                    process
                        .getArgs2_6()
                        .getSetParameterMap()
                        .get("searchIndexManagementHostAndPort")));

    // case: Do not deploy SearchEnvoys on serverless MTMs.
    final ClusterDescription clusterDescription5 =
        getAWSClusterDescription("7.0.0").copy().setIsMTM(true).setName("serverless-MTM").build();
    final ServerlessMTMCluster serverlessMTMCluster =
        new ServerlessMTMCluster(
            NDSModelTestFactory.getServerlessMTMCluster(
                clusterDescription5.getGroupId(), "serverless-MTM"));
    _mtmClusterDao.saveReplicaSafe(serverlessMTMCluster.toDBObject());
    final Result<GenerateAutomationConfigBaseStep.Data> result10 =
        getPreparedStepForClusterWithHostnames(
                clusterDescription5,
                List.of(
                    "mtm-shard-00-00.grp123.mongodb.net",
                    "mtm-shard-00-01.grp123.mongodb.net",
                    "mtm-shard-00-02.grp123.mongodb.net"))
            .perform();
    assertTrue(result10.getStatus().isDone());
    final AutomationConfig resultConfig10 = result10.getData().getAutomationConfig();
    assertTrue(resultConfig10.getDeployment().getMaintainedEnvoys().isEmpty());

    // case: Deploy SearchEnvoys on shared-MTMs.
    final ClusterDescription clusterDescription6 =
        getAWSClusterDescription("7.0.0").copy().setIsMTM(true).setName("shared-MTM").build();
    final SharedMTMCluster sharedMTM =
        new SharedMTMCluster(
            NDSModelTestFactory.getSharedMTMCluster(
                clusterDescription6.getGroupId(), "shared-MTM"));
    _mtmClusterDao.saveReplicaSafe(sharedMTM.toDBObject());
    final Result<GenerateAutomationConfigBaseStep.Data> result11 =
        getPreparedStepForClusterWithHostnames(
                clusterDescription6,
                List.of(
                    "shared-mtm-shard-00-00.grp123.mongodb.net",
                    "shared-mtm-shard-00-01.grp123.mongodb.net",
                    "shared-mtm-shard-00-02.grp123.mongodb.net"))
            .perform();

    final List<ReplicaSetHardware> hardwareMTM =
        _replicaSetHardwareDao.findByCluster(
            clusterDescription6.getGroupId(), clusterDescription6.getName());
    final List<String> hostnamesMTM =
        hardwareMTM.stream()
            .flatMap(h -> h.getHardware().stream())
            .map(h -> h.getHostnameForAgents().get())
            .toList();

    assertTrue(result11.getStatus().isDone());
    final AutomationConfig resultConfig11 = result11.getData().getAutomationConfig();
    assertFalse(resultConfig11.getDeployment().getMaintainedEnvoys().isEmpty());

    // SearchEnvoyTemplate should be set.
    assertNotNull(resultConfig11.getSearchEnvoyTemplate());
    assertEquals(0, resultConfig11.getDeployment().getMongots().size());
    assertEquals(3, resultConfig11.getDeployment().getMaintainedEnvoys().size());

    final List<Integer> maintainedEnvoyIds11 =
        resultConfig11.getDeployment().getMaintainedEnvoys().stream()
            .map(MaintainedEnvoyConfig::getId)
            .collect(Collectors.toList());
    final List<String> maintainedEnvoyStaticConfig11 =
        resultConfig11.getDeployment().getMaintainedEnvoys().stream()
            .map(MaintainedEnvoyConfig::getStaticConfig)
            .collect(Collectors.toList());
    final List<String> maintainedEnvoyStaticConfig10 =
        resultConfig10.getDeployment().getMaintainedEnvoys().stream()
            .map(MaintainedEnvoyConfig::getStaticConfig)
            .collect(Collectors.toList());
    // The static config changed because search index management is also supported
    assertNotEquals(
        maintainedEnvoyStaticConfig10,
        maintainedEnvoyStaticConfig11); // maintainedEnvoyStaticConfig10.size is 0

    resultConfig11
        .getDeployment()
        .getMaintainedEnvoys()
        .forEach(
            searchEnvoyConfig -> {
              // ensure there's no duplicate ids
              assertEquals(
                  1, Collections.frequency(maintainedEnvoyIds11, searchEnvoyConfig.getId()));
              assertTrue(hostnamesMTM.contains(searchEnvoyConfig.getHostname()));
              assertEquals(
                  MaintainedEnvoyType.SEARCH_MONGOD, searchEnvoyConfig.getMaintainedEnvoyType());
            });
    resultConfig11
        .getDeployment()
        .getMongodByClusterName(clusterDescription6.getDeploymentClusterName())
        .forEach(
            process -> {
              assertEquals(
                  NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS, // Dedicated nodes disabled
                  process.getArgs2_6().getSetParameterMap().get("mongotHost"));
            });

    resultConfig11
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(
            process -> {
              assertEquals(
                  EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .get("searchIndexManagementHostAndPort"));
            });
  }

  @Test
  public void testGenerateClusterWithMTM() throws Exception {

    // case: Not an MTM cluster
    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result0 = step.perform();
    assertTrue(result0.getStatus().isDone());
    final AutomationConfig resultConfig0 = result0.getData().getAutomationConfig();

    assertEquals(0, resultConfig0.getDeployment().getAtlasProxies().size());
    // atlas proxy template is always set
    assertNotNull(resultConfig0.getAtlasProxyTemplate().getVersion());

    // case: MTM cluster description
    final ClusterDescription mtmClusterDescription =
        clusterDescription
            .copy()
            .setIsMTM(true)
            .setName(clusterDescription.getName() + "-MTM")
            .build();
    final GenerateAutomationConfigStep mtmStep = getPreparedStepForCluster(mtmClusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result1 = mtmStep.perform();
    assertTrue(result1.getStatus().isDone());
    final AutomationConfig resultConfig1 = result1.getData().getAutomationConfig();

    final List<ReplicaSetHardware> hardware =
        _replicaSetHardwareDao.findByCluster(
            mtmClusterDescription.getGroupId(), mtmClusterDescription.getName());
    final List<String> hostnames =
        hardware.stream()
            .flatMap(h -> h.getHardware().stream())
            .map(h -> h.getHostnameForAgents().get())
            .collect(Collectors.toList());

    assertEquals(3, resultConfig1.getDeployment().getAtlasProxies().size());
    final List<Integer> atlasProxyIds1 =
        resultConfig1.getDeployment().getAtlasProxies().stream()
            .map(AtlasProxyConfig::getId)
            .collect(Collectors.toList());
    assertNotNull(resultConfig1.getAtlasProxyTemplate().getVersion());
    resultConfig1
        .getDeployment()
        .getAtlasProxies()
        .forEach(
            atlasProxyConfig -> {
              // ensure there's no duplicate ids
              assertEquals(1, Collections.frequency(atlasProxyIds1, atlasProxyConfig.getId()));
              assertEquals(NDSAtlasProxyDefaults.PORT, atlasProxyConfig.getPort());
              assertTrue(hostnames.contains(atlasProxyConfig.getHostname()));
              assertEquals(
                  mtmClusterDescription.getDeploymentClusterName(), atlasProxyConfig.getCluster());
              assertNotNull(atlasProxyConfig.getVersion());
              assertTrue(atlasProxyConfig.isManaged());
              assertEquals(
                  NDSAtlasProxyDefaults.CONFIG_BACKUP_PATH, atlasProxyConfig.getConfigBackupPath());
              assertEquals(LogLevel.DEBUG.name(), atlasProxyConfig.getLogLevel());
              assertEquals(NDSAtlasProxyDefaults.LOG_PATH, atlasProxyConfig.getLogPath());
              assertEquals(
                  NDSAtlasProxyDefaults.MIN_TLS_VERSION, atlasProxyConfig.getMinTlsVersion());
              assertEquals(
                  NDSAtlasProxyDefaults.TOOLS_DOWNLOAD_BASE,
                  atlasProxyConfig.getToolsDownloadBase());
              assertEquals(
                  NDSAtlasProxyDefaults.BACKUP_WORSPACE_DIR,
                  atlasProxyConfig.getBackupWorkspaceDir());
              assertEquals(NDSAtlasProxyDefaults.BIND_ADDRESS, atlasProxyConfig.getBindAddress());
              assertNotNull(atlasProxyConfig.getUrls());
              assertEquals(
                  _appSettings.getStrProp(NDSAtlasProxyDefaults.TLS_PEM_KEY_FILE_PROPERTY),
                  atlasProxyConfig.getTlsPemKeyFile());
              assertEquals(NDSAtlasProxyDefaults.SETTINGS_PATH, atlasProxyConfig.getSettingsPath());
              assertNotNull(atlasProxyConfig.getLocalMongoUri());
              assertNotNull(atlasProxyConfig.getMongoUri());
              assertFalse(atlasProxyConfig.isServerlessMode());
              assertFalse(atlasProxyConfig.isServerlessMetricsMode());

              final AdditionalParams additionalParams = atlasProxyConfig.getAdditionalParams();
              assertEquals(
                  600000L, additionalParams.get(AtlasProxyConfig.DB_STATS_CACHE_MILLIS_FIELD));
              assertEquals(
                  false, additionalParams.get(AtlasProxyConfig.ENABLE_TEST_COMMANDS_FIELD));
              assertEquals(false, additionalParams.get(AtlasProxyConfig.REQUIRE_API_VERSION_FIELD));
              assertEquals(
                  30, additionalParams.get(AtlasProxyConfig.MTM_SOCKET_TIMEOUT_SECS_FIELD));
            });
  }

  @Test
  public void testGenerateClusterWithServerlessMTM() throws Exception {

    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());

    // Group is a serverless mtm group
    _ndsGroupDao.setMTMPassword(_ndsGroup.getGroupId(), EncryptionUtils.genEncryptStr("secure"));
    _ndsGroupDao.setServerlessMTMHolder(_ndsGroup.getGroupId());
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();

    final ClusterDescription mtmClusterDescription =
        clusterDescription
            .copy()
            .setIsMTM(true)
            .setName(clusterDescription.getName() + "-MTM")
            .build();
    final GenerateAutomationConfigStep mtmStep = getPreparedStepForCluster(mtmClusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result1 = mtmStep.perform();
    assertTrue(result1.getStatus().isDone());
    final AutomationConfig resultConfig1 = result1.getData().getAutomationConfig();
    validateAutomationConfigForServerlessMTM(
        mtmClusterDescription, resultConfig1, LogLevel.DEBUG.name());

    final Result<GenerateAutomationConfigBaseStep.Data> result3 = mtmStep.perform();
    assertTrue(result3.getStatus().isDone());
    final AutomationConfig resultConfig3 = result3.getData().getAutomationConfig();
    validateAutomationConfigForServerlessMTM(
        mtmClusterDescription, resultConfig3, LogLevel.DEBUG.name());
    resultConfig3
        .getDeployment()
        .getAtlasProxies()
        .forEach(
            atlasProxyConfig -> {
              assertFalse(atlasProxyConfig.isEnableGrpc());
              assertTrue(atlasProxyConfig.getGrpcCertificatePEMFile().isEmpty());
              assertTrue(atlasProxyConfig.getGrpcRootCAPEMFile().isEmpty());
              assertTrue(atlasProxyConfig.getGrpcBindPort().isEmpty());
              assertTrue(atlasProxyConfig.getFlexMode().isEmpty());
            });
  }

  @Test
  public void testGenerateClusterWithServerlessMTM_atlasProxySettingsForServerlessPerfTests()
      throws Exception {

    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());

    // Group is a serverless mtm group
    _ndsGroupDao.setMTMPassword(_ndsGroup.getGroupId(), EncryptionUtils.genEncryptStr("secure"));
    _ndsGroupDao.setServerlessMTMHolder(_ndsGroup.getGroupId());
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();

    final ClusterDescription mtmClusterDescription =
        clusterDescription
            .copy()
            .setIsMTM(true)
            .setName(clusterDescription.getName() + "-MTM")
            .build();
    final GenerateAutomationConfigStep mtmStep = getPreparedStepForCluster(mtmClusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result1 = mtmStep.perform();
    assertTrue(result1.getStatus().isDone());
    final AutomationConfig resultConfig1 = result1.getData().getAutomationConfig();
    validateAutomationConfigForServerlessMTM(
        mtmClusterDescription, resultConfig1, LogLevel.DEBUG.name());

    // Ensure settings are disabled when the feature flag isn't set
    resultConfig1
        .getDeployment()
        .getAtlasProxies()
        .forEach(
            atlasProxyConfig -> {
              assertTrue(atlasProxyConfig.getThrottlingInitialOpsSecLimit().isEmpty());
              assertTrue(atlasProxyConfig.getThrottlingDeclineRatio().isEmpty());
              assertTrue(atlasProxyConfig.getShouldRunPreTlsValidations().isEmpty());
              assertTrue(atlasProxyConfig.getEnforceGRPCConnectionLimits().isEmpty());
              assertTrue(atlasProxyConfig.getAllowRateLimiting().isEmpty());
            });

    // Enable the feature flags for serverless perf testing Atlas Proxy settings
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_PROXY_USE_SERVERLESS_PERF_TEST_SETTINGS);

    final Result<GenerateAutomationConfigBaseStep.Data> result2 = mtmStep.perform();
    assertTrue(result2.getStatus().isDone());
    final AutomationConfig resultConfig2 = result2.getData().getAutomationConfig();
    validateAutomationConfigForServerlessMTM(
        mtmClusterDescription, resultConfig2, LogLevel.INFO.name());
    resultConfig2
        .getDeployment()
        .getAtlasProxies()
        .forEach(
            atlasProxyConfig -> {
              assertFalse(atlasProxyConfig.getShouldRunPreTlsValidations().get());
              assertFalse(atlasProxyConfig.getEnforceGRPCConnectionLimits().get());

              // Assert rate limiting settings haven't been touched
              assertTrue(atlasProxyConfig.getThrottlingInitialOpsSecLimit().isEmpty());
              assertTrue(atlasProxyConfig.getThrottlingDeclineRatio().isEmpty());
              assertTrue(atlasProxyConfig.getAllowRateLimiting().isEmpty());
            });
  }

  @Test
  public void testGenerateClusterWithServerlessMTM_atlasProxySettingsToDisableRateLimiting()
      throws Exception {

    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());

    // Group is a serverless mtm group
    _ndsGroupDao.setMTMPassword(_ndsGroup.getGroupId(), EncryptionUtils.genEncryptStr("secure"));
    _ndsGroupDao.setServerlessMTMHolder(_ndsGroup.getGroupId());
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();

    final ClusterDescription mtmClusterDescription =
        clusterDescription
            .copy()
            .setIsMTM(true)
            .setName(clusterDescription.getName() + "-MTM")
            .build();
    final GenerateAutomationConfigStep mtmStep = getPreparedStepForCluster(mtmClusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result1 = mtmStep.perform();
    assertTrue(result1.getStatus().isDone());
    final AutomationConfig resultConfig1 = result1.getData().getAutomationConfig();
    validateAutomationConfigForServerlessMTM(
        mtmClusterDescription, resultConfig1, LogLevel.DEBUG.name());

    // Ensure settings are disabled when the feature flag isn't set
    resultConfig1
        .getDeployment()
        .getAtlasProxies()
        .forEach(
            atlasProxyConfig -> {
              assertTrue(atlasProxyConfig.getThrottlingInitialOpsSecLimit().isEmpty());
              assertTrue(atlasProxyConfig.getThrottlingDeclineRatio().isEmpty());
              assertTrue(atlasProxyConfig.getAllowRateLimiting().isEmpty());
              assertTrue(atlasProxyConfig.getShouldRunPreTlsValidations().isEmpty());
              assertTrue(atlasProxyConfig.getEnforceGRPCConnectionLimits().isEmpty());
            });

    // Enable the feature flags that disable rate limiting in the Atlas Proxy
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        null, _organization, FeatureFlag.ATLAS_PROXY_DISABLE_RATE_LIMITING);

    final Result<GenerateAutomationConfigBaseStep.Data> result2 = mtmStep.perform();
    assertTrue(result2.getStatus().isDone());
    final AutomationConfig resultConfig2 = result2.getData().getAutomationConfig();
    validateAutomationConfigForServerlessMTM(
        mtmClusterDescription, resultConfig2, LogLevel.DEBUG.name());
    resultConfig2
        .getDeployment()
        .getAtlasProxies()
        .forEach(
            atlasProxyConfig -> {
              assertEquals(
                  AtlasProxyConfig.THROTTLING_INITIAL_OPS_SEC_LIMIT_DISABLE_QOS_THROTTLING_VALUE,
                  atlasProxyConfig.getThrottlingInitialOpsSecLimit().get());
              assertEquals(
                  AtlasProxyConfig.THROTTLING_DECLINE_RATIO_DISABLE_QOS_THROTTLING_VALUE,
                  atlasProxyConfig.getThrottlingDeclineRatio().get());
              assertFalse(atlasProxyConfig.getAllowRateLimiting().get());

              // Assert other performance testing settings haven't been touched
              assertTrue(atlasProxyConfig.getShouldRunPreTlsValidations().isEmpty());
              assertTrue(atlasProxyConfig.getEnforceGRPCConnectionLimits().isEmpty());
            });
  }

  void validateAutomationConfigForServerlessMTM(
      final ClusterDescription pClusterDescription,
      final AutomationConfig pAutomationConfig,
      final String pExpectedLogLevel) {
    final List<ReplicaSetHardware> hardware =
        _replicaSetHardwareDao.findByCluster(
            pClusterDescription.getGroupId(), pClusterDescription.getName());
    final List<String> hostnames =
        hardware.stream()
            .flatMap(h -> h.getHardware().stream())
            .map(h -> h.getHostnameForAgents().get())
            .collect(Collectors.toList());

    assertEquals(3, pAutomationConfig.getDeployment().getAtlasProxies().size());

    final List<Integer> atlasProxyIds1 =
        pAutomationConfig.getDeployment().getAtlasProxies().stream()
            .map(AtlasProxyConfig::getId)
            .collect(Collectors.toList());
    assertNotNull(pAutomationConfig.getAtlasProxyTemplate().getVersion());
    pAutomationConfig
        .getDeployment()
        .getAtlasProxies()
        .forEach(
            atlasProxyConfig -> {
              // ensure there's no duplicate ids
              assertEquals(1, Collections.frequency(atlasProxyIds1, atlasProxyConfig.getId()));
              assertEquals(NDSAtlasProxyDefaults.PORT, atlasProxyConfig.getPort());
              assertTrue(hostnames.contains(atlasProxyConfig.getHostname()));
              assertNotNull(atlasProxyConfig.getVersion());
              assertTrue(atlasProxyConfig.isManaged());
              assertEquals(
                  NDSAtlasProxyDefaults.CONFIG_BACKUP_PATH, atlasProxyConfig.getConfigBackupPath());
              assertEquals(pExpectedLogLevel, atlasProxyConfig.getLogLevel());
              assertEquals(NDSAtlasProxyDefaults.LOG_PATH, atlasProxyConfig.getLogPath());
              assertEquals(
                  NDSAtlasProxyDefaults.MIN_TLS_VERSION, atlasProxyConfig.getMinTlsVersion());
              assertEquals(
                  NDSAtlasProxyDefaults.TOOLS_DOWNLOAD_BASE,
                  atlasProxyConfig.getToolsDownloadBase());
              assertEquals(
                  NDSAtlasProxyDefaults.BACKUP_WORSPACE_DIR,
                  atlasProxyConfig.getBackupWorkspaceDir());
              assertEquals(NDSAtlasProxyDefaults.BIND_ADDRESS, atlasProxyConfig.getBindAddress());
              assertNotNull(atlasProxyConfig.getUrls());
              assertEquals(
                  _appSettings.getStrProp(NDSAtlasProxyDefaults.TLS_PEM_KEY_FILE_PROPERTY),
                  atlasProxyConfig.getTlsPemKeyFile());
              assertEquals(NDSAtlasProxyDefaults.SETTINGS_PATH, atlasProxyConfig.getSettingsPath());
              assertNotNull(atlasProxyConfig.getLocalMongoUri());
              assertNotNull(atlasProxyConfig.getMongoUri());
              assertTrue(atlasProxyConfig.isServerlessMode());
              assertTrue(atlasProxyConfig.isServerlessMetricsMode());
              assertTrue(atlasProxyConfig.isEnforceLoadBalancedConnections());
              assertEquals(
                  ServerlessNDSDefaults.SERVERLESS_PROXY_DB_STATS_CACHE_SIZE,
                  atlasProxyConfig.getDbStatsCacheSize());

              final AdditionalParams additionalParams = atlasProxyConfig.getAdditionalParams();
              assertEquals(
                  240000L, additionalParams.get(AtlasProxyConfig.DB_STATS_CACHE_MILLIS_FIELD));
              assertEquals(
                  false, additionalParams.get(AtlasProxyConfig.ENABLE_TEST_COMMANDS_FIELD));
              assertEquals(false, additionalParams.get(AtlasProxyConfig.REQUIRE_API_VERSION_FIELD));
              assertEquals(
                  30, additionalParams.get(AtlasProxyConfig.MTM_SOCKET_TIMEOUT_SECS_FIELD));
              assertTrue(
                  (Boolean)
                      additionalParams.get(
                          AtlasProxyConfig.ENFORCE_LOAD_BALANCED_CONNECTIONS_FIELD));
            });
  }

  @Test
  public void testGenerateGeoshardedCluster_rsId() throws Exception {
    final InstanceFamily instanceFamily =
        AWSNDSInstanceSize.M10.getInstanceFamilies().entrySet().iterator().next().getKey();

    final List<ReplicationSpec> replicationSpecList =
        ReplicationSpecViewUtils.getDefaultAWSGeosharded3ZoneReplicationSpecView(
                Map.of(NodeType.ELECTABLE, AWSNDSInstanceSize.M10))
            .stream()
            .map(
                rsv ->
                    rsv.toCrossCloudReplicationSpecFromRegionConfigs(CpuArchitecture.X86_64, 10)
                        .copy()
                        .updateAllHardware(
                            new AWSHardwareSpec.Builder()
                                .setInstanceFamilyAndOS(
                                    instanceFamily,
                                    CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                                        .getInstanceHardwareProvider()
                                        .getDefaultOs(_appSettings, instanceFamily)))
                        .build())
            .collect(Collectors.toList());

    final BasicDBList namespaceDoc = new BasicDBList();
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey")
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_SHARD_KEY_UNIQUE, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_CUSTOM_SHARD_KEY_HASHED, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.PRESPLIT_HASHED_ZONES, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.NUM_INITIAL_CHUNKS, null));
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection2")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey")
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_SHARD_KEY_UNIQUE, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_CUSTOM_SHARD_KEY_HASHED, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.PRESPLIT_HASHED_ZONES, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.NUM_INITIAL_CHUNKS, 2L));
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection3")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey")
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_SHARD_KEY_UNIQUE, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_CUSTOM_SHARD_KEY_HASHED, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.PRESPLIT_HASHED_ZONES, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.NUM_INITIAL_CHUNKS, null));
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection4")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey"));

    final BasicDBObject geoShardingDoc =
        new BasicDBObject()
            .append(GeoSharding.FieldDefs.CUSTOM_ZONE_MAPPING, new BasicDBObject())
            .append(GeoSharding.FieldDefs.MANAGED_NAMESPACES, namespaceDoc);

    final ShardedClusterDescription clusterDescription =
        getGeoShardedClusterDescription(
            replicationSpecList, new GeoSharding(geoShardingDoc), "foo");
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);

    // Safe replica set reconfig requires only one publish when creating clusters.
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertCommon(clusterDescription, resultConfig);

    assertEquals(
        9,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOS)
            .count());
    assertEquals(
        12,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOD)
            .count());
    assertEquals(
        3,
        resultConfig.getDeployment().getProcesses().stream().filter(Process::isConfigSvr).count());
    assertEquals(1, resultConfig.getDeployment().getSharding().size());

    final ShardedCluster cluster = resultConfig.getDeployment().getSharding().get(0);
    assertTrue(cluster.isManagedSharding());

    // ensure shards properly tagged
    for (int j = 0; j < replicationSpecList.size(); j++) {
      assertEquals(
          cluster.getShards().get(j).getTags(),
          Collections.singletonList(replicationSpecList.get(j).getId().toHexString()));
    }

    // ensure collections have right shape
    assertEquals(4, cluster.getCollections().size());

    final ShardedCollection collection = cluster.getCollections().get(0);
    assertEquals("myDB.myCollection", collection.getId());
    assertEquals(2, collection.getShardKeys().size());

    final List<Object> locationKey = collection.getShardKeys().get(0);
    assertEquals("location", locationKey.get(0));
    assertEquals(1, locationKey.get(1));

    final List<Object> customKey = collection.getShardKeys().get(1);
    assertEquals("myCustomKey", customKey.get(0));
    assertEquals(1, customKey.get(1));

    assertFalse(collection.isPresplitHashedZones());
    assertNull(collection.getNumInitialChunks());
    assertFalse(collection.isUnique());

    // Ensure that the direction is 'hashed' if custom shard key is hashed
    final ShardedCollection collectionCustomShardKeyHashed = cluster.getCollections().get(1);
    final List<Object> customKeyHashed = collectionCustomShardKeyHashed.getShardKeys().get(1);
    assertEquals("myCustomKey", customKeyHashed.get(0));
    assertEquals("hashed", customKeyHashed.get(1));
    assertTrue(collectionCustomShardKeyHashed.isPresplitHashedZones());
    assertEquals(2L, collectionCustomShardKeyHashed.getNumInitialChunks().longValue());
    assertFalse(collectionCustomShardKeyHashed.isUnique());

    // Ensure unique shard key works as expected
    final ShardedCollection collectionUniqueShardKey = cluster.getCollections().get(2);
    assertTrue(collectionUniqueShardKey.isUnique());
    assertFalse(collectionUniqueShardKey.isPresplitHashedZones());
    assertNull(collectionUniqueShardKey.getNumInitialChunks());

    final List<Object> uniqueCustomKey = collectionUniqueShardKey.getShardKeys().get(1);
    assertEquals("myCustomKey", uniqueCustomKey.get(0));
    assertEquals(1, uniqueCustomKey.get(1));

    // Ensure defaults for presplitHashedZones, numInitialChunks, customShardKeyUnique
    final ShardedCollection collectionDefaults = cluster.getCollections().get(3);
    assertFalse(collectionDefaults.isPresplitHashedZones());
    assertNull(collectionDefaults.getNumInitialChunks());
    assertFalse(collectionDefaults.isUnique());

    final List<Object> customKeyNoOptions = collectionDefaults.getShardKeys().get(1);
    assertEquals("myCustomKey", customKeyNoOptions.get(0));
    assertEquals(1, customKeyNoOptions.get(1));

    // ensure tags have right shape
    final List<Tag> tags = cluster.getTags();
    assertEquals(140, tags.size());

    final Set<String> customShardKeys =
        tags.stream().map(x -> x.getMin().get(1).getField()).collect(Collectors.toSet());
    for (final String customShardKey : customShardKeys) {
      final List<Tag> filteredTags =
          tags.stream()
              .filter(x -> x.getMin().get(1).getField().equals(customShardKey))
              .collect(Collectors.toList());
      for (int j = 0; j < filteredTags.size(); j++) {
        final Tag tag = filteredTags.get(j);
        // These collections share the same list of tags since they have the same shard key
        assertTrue(tag.getNamespaceList().contains("myDB.myCollection"));
        assertTrue(tag.getNamespaceList().contains("myDB.myCollection2"));

        assertEquals(2, tag.getMin().size());
        assertEquals(2, tag.getMax().size());

        // check min tag
        final TagRangeValue min1 = tag.getMin().get(0);
        assertEquals("location", min1.getField());
        assertEquals("string", min1.getFieldType());

        final TagRangeValue min2 = tag.getMin().get(1);
        assertEquals("myCustomKey", min2.getField());
        assertEquals("minKey", min2.getFieldType());
        assertEquals("1", min2.getValue());

        // check max tag
        final TagRangeValue max1 = tag.getMax().get(0);
        assertEquals("location", max1.getField());

        final TagRangeValue max2 = tag.getMax().get(1);
        assertEquals("myCustomKey", max2.getField());
        assertEquals("1", max2.getValue());

        if (j < (filteredTags.size() - 1)) {
          // min key for upper bound should be the previous alphabetical location to ensure
          // continuous
          // chunks
          assertEquals("string", max1.getFieldType());
          final Tag previousTag = filteredTags.get(j + 1);
          assertEquals(previousTag.getMin().get(0).getValue(), max1.getValue());
          assertEquals("minKey", max2.getFieldType());
        } else {
          // the final tag is maxKey
          assertEquals("maxKey", max1.getFieldType());
          assertEquals("1", max1.getValue());
          assertEquals("maxKey", max2.getFieldType());
        }
      }
    }
  }

  @Test
  public void testGenerateGeoshardedCluster_zoneName() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
    final InstanceFamily instanceFamily =
        AWSNDSInstanceSize.M10.getInstanceFamilies().entrySet().iterator().next().getKey();

    final List<ReplicationSpec> replicationSpecList =
        ReplicationSpecViewUtils.getDefaultAWSGeosharded3ZoneReplicationSpecView(
                Map.of(NodeType.ELECTABLE, AWSNDSInstanceSize.M10))
            .stream()
            .map(
                rsv ->
                    rsv.toCrossCloudReplicationSpecFromRegionConfigs(CpuArchitecture.X86_64, 10)
                        .copy()
                        .updateAllHardware(
                            new AWSHardwareSpec.Builder()
                                .setInstanceFamilyAndOS(
                                    instanceFamily,
                                    CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                                        .getInstanceHardwareProvider()
                                        .getDefaultOs(_appSettings, instanceFamily)))
                        .build())
            .collect(Collectors.toList());

    final BasicDBList namespaceDoc = new BasicDBList();
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey")
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_SHARD_KEY_UNIQUE, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_CUSTOM_SHARD_KEY_HASHED, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.PRESPLIT_HASHED_ZONES, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.NUM_INITIAL_CHUNKS, null));
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection2")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey")
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_SHARD_KEY_UNIQUE, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_CUSTOM_SHARD_KEY_HASHED, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.PRESPLIT_HASHED_ZONES, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.NUM_INITIAL_CHUNKS, 2L));
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection3")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey")
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_SHARD_KEY_UNIQUE, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_CUSTOM_SHARD_KEY_HASHED, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.PRESPLIT_HASHED_ZONES, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.NUM_INITIAL_CHUNKS, null));
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection4")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey"));

    final BasicDBObject geoShardingDoc =
        new BasicDBObject()
            .append(GeoSharding.FieldDefs.CUSTOM_ZONE_MAPPING, new BasicDBObject())
            .append(GeoSharding.FieldDefs.MANAGED_NAMESPACES, namespaceDoc);

    final ShardedClusterDescription clusterDescription =
        getGeoShardedClusterDescription(
            replicationSpecList, new GeoSharding(geoShardingDoc), "foo");
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);

    // Safe replica set reconfig requires only one publish when creating clusters.
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertCommon(clusterDescription, resultConfig);

    assertEquals(
        9,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOS)
            .count());
    assertEquals(
        12,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOD)
            .count());
    assertEquals(
        3,
        resultConfig.getDeployment().getProcesses().stream().filter(Process::isConfigSvr).count());
    assertEquals(1, resultConfig.getDeployment().getSharding().size());

    final ShardedCluster cluster = resultConfig.getDeployment().getSharding().get(0);
    assertTrue(cluster.isManagedSharding());

    // ensure shards properly tagged
    for (int j = 0; j < replicationSpecList.size(); j++) {
      assertEquals(
          cluster.getShards().get(j).getTags(),
          Collections.singletonList(replicationSpecList.get(j).getZoneName()));
    }

    // ensure collections have right shape
    assertEquals(4, cluster.getCollections().size());

    final ShardedCollection collection = cluster.getCollections().get(0);
    assertEquals("myDB.myCollection", collection.getId());
    assertEquals(2, collection.getShardKeys().size());

    final List<Object> locationKey = collection.getShardKeys().get(0);
    assertEquals("location", locationKey.get(0));
    assertEquals(1, locationKey.get(1));

    final List<Object> customKey = collection.getShardKeys().get(1);
    assertEquals("myCustomKey", customKey.get(0));
    assertEquals(1, customKey.get(1));

    assertFalse(collection.isPresplitHashedZones());
    assertNull(collection.getNumInitialChunks());
    assertFalse(collection.isUnique());

    // Ensure that the direction is 'hashed' if custom shard key is hashed
    final ShardedCollection collectionCustomShardKeyHashed = cluster.getCollections().get(1);
    final List<Object> customKeyHashed = collectionCustomShardKeyHashed.getShardKeys().get(1);
    assertEquals("myCustomKey", customKeyHashed.get(0));
    assertEquals("hashed", customKeyHashed.get(1));
    assertTrue(collectionCustomShardKeyHashed.isPresplitHashedZones());
    assertEquals(2L, collectionCustomShardKeyHashed.getNumInitialChunks().longValue());
    assertFalse(collectionCustomShardKeyHashed.isUnique());

    // Ensure unique shard key works as expected
    final ShardedCollection collectionUniqueShardKey = cluster.getCollections().get(2);
    assertTrue(collectionUniqueShardKey.isUnique());
    assertFalse(collectionUniqueShardKey.isPresplitHashedZones());
    assertNull(collectionUniqueShardKey.getNumInitialChunks());

    final List<Object> uniqueCustomKey = collectionUniqueShardKey.getShardKeys().get(1);
    assertEquals("myCustomKey", uniqueCustomKey.get(0));
    assertEquals(1, uniqueCustomKey.get(1));

    // Ensure defaults for presplitHashedZones, numInitialChunks, customShardKeyUnique
    final ShardedCollection collectionDefaults = cluster.getCollections().get(3);
    assertFalse(collectionDefaults.isPresplitHashedZones());
    assertNull(collectionDefaults.getNumInitialChunks());
    assertFalse(collectionDefaults.isUnique());

    final List<Object> customKeyNoOptions = collectionDefaults.getShardKeys().get(1);
    assertEquals("myCustomKey", customKeyNoOptions.get(0));
    assertEquals(1, customKeyNoOptions.get(1));

    // ensure tags have right shape
    final List<Tag> tags = cluster.getTags();
    assertEquals(140, tags.size());

    final Set<String> customShardKeys =
        tags.stream().map(x -> x.getMin().get(1).getField()).collect(Collectors.toSet());
    for (final String customShardKey : customShardKeys) {
      final List<Tag> filteredTags =
          tags.stream().filter(x -> x.getMin().get(1).getField().equals(customShardKey)).toList();
      for (int j = 0; j < filteredTags.size(); j++) {
        final Tag tag = filteredTags.get(j);
        // These collections share the same list of tags since they have the same shard key
        assertTrue(tag.getNamespaceList().contains("myDB.myCollection"));
        assertTrue(tag.getNamespaceList().contains("myDB.myCollection2"));

        assertEquals(2, tag.getMin().size());
        assertEquals(2, tag.getMax().size());

        // check min tag
        final TagRangeValue min1 = tag.getMin().get(0);
        assertEquals("location", min1.getField());
        assertEquals("string", min1.getFieldType());

        final TagRangeValue min2 = tag.getMin().get(1);
        assertEquals("myCustomKey", min2.getField());
        assertEquals("minKey", min2.getFieldType());
        assertEquals("1", min2.getValue());

        // check max tag
        final TagRangeValue max1 = tag.getMax().get(0);
        assertEquals("location", max1.getField());

        final TagRangeValue max2 = tag.getMax().get(1);
        assertEquals("myCustomKey", max2.getField());
        assertEquals("1", max2.getValue());

        if (j < (filteredTags.size() - 1)) {
          // min key for upper bound should be the previous alphabetical location to ensure
          // continuous
          // chunks
          assertEquals("string", max1.getFieldType());
          final Tag previousTag = filteredTags.get(j + 1);
          assertEquals(previousTag.getMin().get(0).getValue(), max1.getValue());
          assertEquals("minKey", max2.getFieldType());
        } else {
          // the final tag is maxKey
          assertEquals("maxKey", max1.getFieldType());
          assertEquals("1", max1.getValue());
          assertEquals("maxKey", max2.getFieldType());
        }
      }
    }
  }

  @Test
  public void testGenerateGeoshardedCluster_withOverrides() throws Exception {
    final InstanceFamily instanceFamily =
        AWSNDSInstanceSize.M10.getInstanceFamilies().entrySet().iterator().next().getKey();

    final List<ReplicationSpec> replicationSpecList =
        ReplicationSpecViewUtils.getDefaultAWSGeosharded3ZoneReplicationSpecView(
                Map.of(NodeType.ELECTABLE, AWSNDSInstanceSize.M10))
            .stream()
            .map(
                rsv ->
                    rsv.toCrossCloudReplicationSpecFromRegionConfigs(CpuArchitecture.X86_64, 10)
                        .copy()
                        .updateAllHardware(
                            new AWSHardwareSpec.Builder()
                                .setInstanceFamilyAndOS(
                                    instanceFamily,
                                    CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                                        .getInstanceHardwareProvider()
                                        .getDefaultOs(_appSettings, instanceFamily)))
                        .build())
            .collect(Collectors.toList());

    final BasicDBList namespaceDoc = new BasicDBList();
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey")
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_SHARD_KEY_UNIQUE, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_CUSTOM_SHARD_KEY_HASHED, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.PRESPLIT_HASHED_ZONES, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.NUM_INITIAL_CHUNKS, 2L));

    final ReplicationSpec apSoutheastReplSpec =
        replicationSpecList.stream()
            .filter(rs -> rs.getRegionConfigByRegion(AWSRegionName.AP_SOUTHEAST_1).isPresent())
            .findFirst()
            .get();
    final String apSoutheastZoneName = apSoutheastReplSpec.getZoneName();
    final ObjectId apSoutheastZoneId = apSoutheastReplSpec.getId();
    final String overrideLocation = "US-NY";
    final BasicDBObject customZoneMapping =
        new BasicDBObject().append(overrideLocation, apSoutheastZoneId);

    final BasicDBObject geoShardingDoc =
        new BasicDBObject()
            .append(GeoSharding.FieldDefs.CUSTOM_ZONE_MAPPING, customZoneMapping)
            .append(GeoSharding.FieldDefs.MANAGED_NAMESPACES, namespaceDoc);

    final ShardedClusterDescription clusterDescription =
        getGeoShardedClusterDescription(
            replicationSpecList, new GeoSharding(geoShardingDoc), "foo");
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());

    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    final List<Tag> overrideTags =
        resultConfig.getDeployment().getSharding().get(0).getTags().stream()
            .filter(tag -> tag.getMin().get(0).getValue().equals("US-NY"))
            .collect(Collectors.toList());

    assertEquals(apSoutheastZoneId.toHexString(), overrideTags.get(0).getTag());
  }

  @Test
  public void testGenerateGeoshardedCluster_withOverrides_zoneName() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
    final InstanceFamily instanceFamily =
        AWSNDSInstanceSize.M10.getInstanceFamilies().entrySet().iterator().next().getKey();

    final List<ReplicationSpec> replicationSpecList =
        ReplicationSpecViewUtils.getDefaultAWSGeosharded3ZoneReplicationSpecView(
                Map.of(NodeType.ELECTABLE, AWSNDSInstanceSize.M10))
            .stream()
            .map(
                rsv ->
                    rsv.toCrossCloudReplicationSpecFromRegionConfigs(CpuArchitecture.X86_64, 10)
                        .copy()
                        .updateAllHardware(
                            new AWSHardwareSpec.Builder()
                                .setInstanceFamilyAndOS(
                                    instanceFamily,
                                    CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                                        .getInstanceHardwareProvider()
                                        .getDefaultOs(_appSettings, instanceFamily)))
                        .build())
            .collect(Collectors.toList());

    final BasicDBList namespaceDoc = new BasicDBList();
    namespaceDoc.add(
        new BasicDBObject()
            .append(GeoSharding.ManagedNamespace.FieldDefs.DB, "myDB")
            .append(GeoSharding.ManagedNamespace.FieldDefs.COLLECTION, "myCollection")
            .append(GeoSharding.ManagedNamespace.FieldDefs.CUSTOM_SHARD_KEY, "myCustomKey")
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_SHARD_KEY_UNIQUE, false)
            .append(GeoSharding.ManagedNamespace.FieldDefs.IS_CUSTOM_SHARD_KEY_HASHED, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.PRESPLIT_HASHED_ZONES, true)
            .append(GeoSharding.ManagedNamespace.FieldDefs.NUM_INITIAL_CHUNKS, 2L));

    final ReplicationSpec apSoutheastReplSpec =
        replicationSpecList.stream()
            .filter(rs -> rs.getRegionConfigByRegion(AWSRegionName.AP_SOUTHEAST_1).isPresent())
            .findFirst()
            .get();
    final String apSoutheastZoneName = apSoutheastReplSpec.getZoneName();
    final ObjectId apSoutheastZoneId = apSoutheastReplSpec.getId();
    final String overrideLocation = "US-NY";
    final BasicDBObject customZoneMapping =
        new BasicDBObject().append(overrideLocation, apSoutheastZoneId);

    final BasicDBObject geoShardingDoc =
        new BasicDBObject()
            .append(GeoSharding.FieldDefs.CUSTOM_ZONE_MAPPING, customZoneMapping)
            .append(GeoSharding.FieldDefs.MANAGED_NAMESPACES, namespaceDoc);

    final ShardedClusterDescription clusterDescription =
        getGeoShardedClusterDescription(
            replicationSpecList, new GeoSharding(geoShardingDoc), "foo");
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());

    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    final List<Tag> overrideTags =
        resultConfig.getDeployment().getSharding().get(0).getTags().stream()
            .filter(tag -> tag.getMin().get(0).getValue().equals("US-NY"))
            .collect(Collectors.toList());

    assertEquals(apSoutheastZoneName, overrideTags.get(0).getTag());
  }

  @Test
  public void testGenerateSelfManagedGeoSharded() throws Exception {
    final InstanceFamily instanceFamily =
        AWSNDSInstanceSize.M10.getInstanceFamilies().entrySet().iterator().next().getKey();

    final List<ReplicationSpec> replicationSpecList =
        ReplicationSpecViewUtils.getDefaultAWSGeosharded3ZoneReplicationSpecView(
                Map.of(NodeType.ELECTABLE, AWSNDSInstanceSize.M10))
            .stream()
            .map(
                rsv ->
                    rsv.toCrossCloudReplicationSpecFromRegionConfigs(CpuArchitecture.X86_64, 10)
                        .copy()
                        .updateAllHardware(
                            new AWSHardwareSpec.Builder()
                                .setInstanceFamilyAndOS(
                                    instanceFamily,
                                    CloudProviderRegistry.getByCloudProvider(CloudProvider.AWS)
                                        .getInstanceHardwareProvider()
                                        .getDefaultOs(_appSettings, instanceFamily)))
                        .build())
            .collect(Collectors.toList());

    final ShardedClusterDescription clusterDescription =
        getGeoShardedClusterDescription(
            replicationSpecList,
            GeoShardingView.getDefaultGeoShardingView(true).toGeoSharding(),
            "foo");
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);

    // Safe replica set reconfig requires only one publish when creating clusters.
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertCommon(clusterDescription, resultConfig);

    assertEquals(
        9,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOS)
            .count());
    assertEquals(
        12,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOD)
            .count());
    assertEquals(
        3,
        resultConfig.getDeployment().getProcesses().stream().filter(Process::isConfigSvr).count());
    assertEquals(1, resultConfig.getDeployment().getSharding().size());

    // ensure the managedSharding flag is false
    final ShardedCluster cluster = resultConfig.getDeployment().getSharding().get(0);
    assertTrue(!cluster.isManagedSharding());

    // ensure shards are not given a zone with replication spec ID
    for (int i = 0; i < replicationSpecList.size(); i++) {
      assertEquals(0, cluster.getShards().get(i).getTags().size());
    }

    // ensure no collections were defined
    assertEquals(0, cluster.getCollections().size());

    // ensure tags are empty
    final List<Tag> tags = cluster.getTags();
    assertEquals(0, tags.size());
  }

  @Test
  public void testWithholdExistingProcessesDuringConfigUpdates() throws Exception {
    final ShardedClusterDescription clusterDescription =
        setUpClusterDescription(false, NDSModelTestFactory.TEST_MONGODB_VERSION, "foo");

    // initial disabledProtocols
    final String initDisabledProtocols = "TLS1_0,TLS1_1";
    final ClusterDescriptionProcessArgsUpdatable.Builder pArgsBuilder =
        new ClusterDescriptionProcessArgsUpdatable.Builder(
            clusterDescription.getName(), _ndsGroup.getGroupId());
    final ClusterDescriptionProcessArgsUpdatable clusterDescriptionProcessArgs =
        _clusterDescriptionProcessArgsDao
            .findForUpdate(
                clusterDescription.getName(),
                _ndsGroup.getGroupId(),
                ClusterDescriptionProcessArgs.Type.STANDARD)
            .map(pArgs -> pArgs.copy().setDisabledTlsProtocols(initDisabledProtocols).build())
            .orElse(pArgsBuilder.setDisabledTlsProtocols(initDisabledProtocols).build());
    _clusterDescriptionProcessArgsDao.save(clusterDescriptionProcessArgs);

    final GenerateAutomationConfigStep step = getStepWithClusterDescription(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    // save and publish the automation config
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(
        result.getData().getAutomationConfig(), appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final AutomationConfig existingConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());
    final List<Process> existingMongods =
        existingConfig
            .getDeployment()
            .getMongodByClusterName(clusterDescription.getDeploymentClusterName());
    final List<Process> existingMongoses =
        existingConfig
            .getDeployment()
            .getMongosByClusterName(clusterDescription.getDeploymentClusterName());

    final ClusterDescription clusterDescriptionSaved =
        _clusterSvc
            .getActiveClusterDescription(_ndsGroup.getGroupId(), clusterDescription.getName())
            .get();

    // add a new shard and update the disabledProtocols
    final ClusterDescription updatedClusterDescription =
        clusterDescriptionSaved
            .copy()
            .setReplicationSpecList(
                Collections.singletonList(
                    clusterDescriptionSaved
                        .getReplicationSpecsWithShardData()
                        .get(0)
                        .copy()
                        .setNumShards(3)
                        .build()))
            .build();
    _clusterSvc.updateCluster(
        updatedClusterDescription,
        null,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterUpdateContext.forIntTestAction());
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), updatedClusterDescription.getName())
            .get();
    forceHardwareToProvisioned(_ndsGroup, cluster.getClusterDescription());

    // update disabledProtocols
    final String updatedDisabledProtocols = "TLS1_0,TLS1_1,TLS1_2";
    final ClusterDescriptionProcessArgsUpdatable clusterDescriptionProcessArgsUpdate =
        _clusterDescriptionProcessArgsDao
            .findForUpdate(
                clusterDescription.getName(),
                _ndsGroup.getGroupId(),
                ClusterDescriptionProcessArgs.Type.STANDARD)
            .map(pArgs -> pArgs.copy().setDisabledTlsProtocols(updatedDisabledProtocols).build())
            .orElseThrow(
                () -> new RuntimeException("we should have the clusterDescriptionProcessArgs now"));
    _clusterDescriptionProcessArgsDao.save(clusterDescriptionProcessArgsUpdate);

    // needsMongoDBConfigPublishRestartAllowed not set initially
    // configs for all mongod/mongos processes should be updated since default value is IMMEDIATE.
    // This should never happen in practice.
    {
      final GenerateAutomationConfigStep stepSpy =
          getStepWithClusterDescription(updatedClusterDescription);
      final Result<GenerateAutomationConfigBaseStep.Data> result2 = stepSpy.perform();
      final AutomationConfig updatedConfig = result2.getData().getAutomationConfig();
      final List<Process> updatedMongods =
          updatedConfig
              .getDeployment()
              .getMongodByClusterName(updatedClusterDescription.getDeploymentClusterName());
      final List<Process> updatedMongoses =
          updatedConfig
              .getDeployment()
              .getMongosByClusterName(updatedClusterDescription.getDeploymentClusterName());

      assertTrue(updatedMongods.size() > existingMongods.size());
      assertTrue(updatedMongoses.size() > existingMongoses.size());

      final Map<String, Process> existingMongodMap =
          existingMongods.stream().collect(Collectors.toMap(Process::getName, p -> p));
      for (final Process updatedMongod : updatedMongods) {
        if (existingMongodMap.containsKey(updatedMongod.getName())) {
          final Process existingMongodProcess = existingMongodMap.get(updatedMongod.getName());
          assertNotEquals(existingMongodProcess, updatedMongod);
          assertEquals(updatedDisabledProtocols, updatedMongod.getArgs().getTlsDisabledProtocols());
        } else {
          assertEquals(updatedDisabledProtocols, updatedMongod.getArgs().getTlsDisabledProtocols());
        }
      }

      final Map<String, Process> existingMongosMap =
          existingMongoses.stream().collect(Collectors.toMap(this::getUniqueProcessKey, p -> p));
      for (final Process updatedMongos : updatedMongoses) {
        if (existingMongosMap.containsKey(this.getUniqueProcessKey(updatedMongos))) {
          final Process existingMongosProcess =
              existingMongosMap.get(this.getUniqueProcessKey(updatedMongos));
          assertNotEquals(existingMongosProcess, updatedMongos);
          assertEquals(updatedDisabledProtocols, updatedMongos.getArgs().getTlsDisabledProtocols());
        } else {
          assertEquals(updatedDisabledProtocols, updatedMongos.getArgs().getTlsDisabledProtocols());
        }
      }
    }

    // needsMongoDBConfigPublishRestartAllowed set to NONE
    // none of the configs for existing nodes should be updated
    // new mongod/mongos configs should be added as expected
    {
      _clusterDescriptionDao.setNeedsMongoDBConfigPublishRestartAllowedForCluster(
          _group.getId(),
          updatedClusterDescription.getName(),
          ClusterDescription.ProcessRestartAllowedState.NONE);
      final ClusterDescription clusterDescriptionWithImmediateRestart =
          _clusterSvc
              .getActiveClusterDescription(
                  updatedClusterDescription.getGroupId(), updatedClusterDescription.getUniqueId())
              .get();
      final GenerateAutomationConfigStep stepSpy =
          getStepWithClusterDescription(clusterDescriptionWithImmediateRestart);

      final Result<GenerateAutomationConfigBaseStep.Data> result2 = stepSpy.perform();
      final AutomationConfig updatedConfig = result2.getData().getAutomationConfig();
      final List<Process> updatedMongods =
          updatedConfig
              .getDeployment()
              .getMongodByClusterName(updatedClusterDescription.getDeploymentClusterName());
      final List<Process> updatedMongoses =
          updatedConfig
              .getDeployment()
              .getMongosByClusterName(updatedClusterDescription.getDeploymentClusterName());

      assertTrue(updatedMongods.size() > existingMongods.size());
      assertTrue(updatedMongoses.size() > existingMongoses.size());

      final Map<String, Process> existingMongodMap =
          existingMongods.stream().collect(Collectors.toMap(Process::getName, p -> p));
      for (final Process updatedMongod : updatedMongods) {
        if (existingMongodMap.containsKey(updatedMongod.getName())) {
          final Process existingMongodProcess = existingMongodMap.get(updatedMongod.getName());
          assertEquals(existingMongodProcess, updatedMongod);
          assertEquals(initDisabledProtocols, updatedMongod.getArgs().getTlsDisabledProtocols());
        } else {
          assertEquals(updatedDisabledProtocols, updatedMongod.getArgs().getTlsDisabledProtocols());
        }
      }

      final Map<String, Process> existingMongosMap =
          existingMongoses.stream().collect(Collectors.toMap(this::getUniqueProcessKey, p -> p));
      for (final Process updatedMongos : updatedMongoses) {
        if (existingMongosMap.containsKey(this.getUniqueProcessKey(updatedMongos))) {
          final Process existingMongosProcess =
              existingMongosMap.get(this.getUniqueProcessKey(updatedMongos));
          assertEquals(existingMongosProcess, updatedMongos);
          assertEquals(initDisabledProtocols, updatedMongos.getArgs().getTlsDisabledProtocols());
        } else {
          assertEquals(updatedDisabledProtocols, updatedMongos.getArgs().getTlsDisabledProtocols());
        }
      }
    }

    // needsMongoDBConfigPublishRestartAllowed set to IMMEDIATE
    // all of the configs for existing and new mongod/mongos processes should be updated
    {
      _clusterDescriptionDao.setNeedsMongoDBConfigPublishRestartAllowedForCluster(
          _group.getId(),
          updatedClusterDescription.getName(),
          ClusterDescription.ProcessRestartAllowedState.IMMEDIATE);
      final ClusterDescription clusterDescriptionWithImmediateRestart =
          _clusterSvc
              .getActiveClusterDescription(
                  updatedClusterDescription.getGroupId(), updatedClusterDescription.getUniqueId())
              .get();
      final GenerateAutomationConfigStep stepSpy =
          Mockito.spy(getStepWithClusterDescription(clusterDescriptionWithImmediateRestart));

      final Result<GenerateAutomationConfigBaseStep.Data> result2 = stepSpy.perform();
      final AutomationConfig updatedConfig = result2.getData().getAutomationConfig();
      final List<Process> updatedMongods =
          updatedConfig
              .getDeployment()
              .getMongodByClusterName(updatedClusterDescription.getDeploymentClusterName());
      final List<Process> updatedMongoses =
          updatedConfig
              .getDeployment()
              .getMongosByClusterName(updatedClusterDescription.getDeploymentClusterName());

      assertTrue(updatedMongods.size() > existingMongods.size());
      assertTrue(updatedMongoses.size() > existingMongoses.size());

      final Map<String, Process> existingMongodMap =
          existingMongods.stream().collect(Collectors.toMap(Process::getName, p -> p));
      for (final Process updatedMongod : updatedMongods) {
        if (existingMongodMap.containsKey(updatedMongod.getName())) {
          final Process existingMongodProcess = existingMongodMap.get(updatedMongod.getName());
          assertNotEquals(existingMongodProcess, updatedMongod);
          assertEquals(updatedDisabledProtocols, updatedMongod.getArgs().getTlsDisabledProtocols());
        } else {
          assertEquals(updatedDisabledProtocols, updatedMongod.getArgs().getTlsDisabledProtocols());
        }
      }

      final Map<String, Process> existingMongosMap =
          existingMongoses.stream().collect(Collectors.toMap(this::getUniqueProcessKey, p -> p));
      for (final Process updatedMongos : updatedMongoses) {
        if (existingMongosMap.containsKey(this.getUniqueProcessKey(updatedMongos))) {
          final Process existingMongosProcess =
              existingMongosMap.get(this.getUniqueProcessKey(updatedMongos));
          assertNotEquals(existingMongosProcess, updatedMongos);
          assertEquals(updatedDisabledProtocols, updatedMongos.getArgs().getTlsDisabledProtocols());
        } else {
          assertEquals(updatedDisabledProtocols, updatedMongos.getArgs().getTlsDisabledProtocols());
        }
      }
    }

    // needsMongoDBConfigPublishRestartAllowed set to NONE but restore map is not empty
    // all of the configs for existing and new mongod/mongos processes should be updated
    {
      _clusterDescriptionDao.setNeedsMongoDBConfigPublishRestartAllowedForCluster(
          _group.getId(),
          updatedClusterDescription.getName(),
          ClusterDescription.ProcessRestartAllowedState.NONE);

      ClusterDescription clusterDescriptionWithNoneRestart =
          _clusterSvc
              .getActiveClusterDescription(
                  updatedClusterDescription.getGroupId(), updatedClusterDescription.getUniqueId())
              .get();
      final GenerateAutomationConfigStep stepSpy =
          Mockito.spy(getStepWithClusterDescription(clusterDescriptionWithNoneRestart));

      final ObjectId snapshotId = new ObjectId();

      _backupSnapshotDao.addBackupSnapshot(
          new SnapshotUpdate()
              .setId(snapshotId)
              .setProjectId(_group.getId())
              .setClusterName(clusterDescriptionWithNoneRestart.getName())
              .setDeploymentClusterName(
                  clusterDescriptionWithNoneRestart.getDeploymentClusterName())
              .setRsId("rs")
              .setClusterUniqueId(clusterDescriptionWithNoneRestart.getUniqueId())
              .setScheduledCreationDate(new Date())
              .setSnapshotInitiationDate(new Date())
              .setScheduledDeletionDate(new Date())
              .setDeleted(false)
              .setPurged(false)
              .setEmptySnapshotField()
              .setCloudProviders(List.of(CloudProvider.AWS))
              .setAwsSnapshotField(
                  new AwsSnapshotFieldBuilder()
                      .withEbsSnapshotId("snapshot")
                      .withAWSAccountId(new ObjectId())
                      .withRegionName(AWSRegionName.US_EAST_1.getName()))
              .setMongoDbVersion(clusterDescriptionWithNoneRestart.getMongoDBVersion())
              .setFcvMongoDbVersion(
                  clusterDescriptionWithNoneRestart.getFixedFCVAwareMongoDBVersion())
              .setStatus(Status.COMPLETED)
              .setType(Type.ON_DEMAND)
              .setDescription("test")
              .setRequestingUser(new RequestingUser(AppUser.SYSTEM_USER))
              .setFrequencyType(BackupFrequencyType.HOURLY)
              .setPolicyItemIds(List.of())
              .setMainPolicyItemId(null)
              .setOverrideRetentionPolicy(false)
              .setEncryptionDetails(BackupSnapshotEncryptionCredentials.EMPTY)
              .setBackupRetentionUnit(BackupRetentionUnit.DAYS));

      final Optional<ReplicaSetBackupSnapshot> snapshot =
          _backupSnapshotDao.findReplicaSetSnapshotById(snapshotId);

      final BackupRestoreJobRequest restoreJobRequest =
          BackupRestoreJobRequest.create(
              _group,
              snapshot.get(),
              clusterDescriptionWithNoneRestart.getUniqueId(),
              clusterDescriptionWithNoneRestart.getName(),
              AppUser.SYSTEM_USER,
              new RestoreTarget(_group, clusterDescriptionWithNoneRestart.getName(), "rs"),
              null,
              null,
              new StreamingRestoreStrategy(_group.getId(), 1000, 3500),
              false);

      final ObjectId restoreJobId =
          _backupRestoreJobDao.createReplicaSetJob(
              snapshot.get(), restoreJobRequest, "rs", "4.4.0", null, null);

      clusterDescriptionWithNoneRestart =
          clusterDescriptionWithNoneRestart
              .copy()
              .setRestoreJobIds(List.of(restoreJobId))
              .setRestoreJobType(RestoreJobType.SNAPSHOT)
              .build();

      _clusterDescriptionDao.save(clusterDescriptionWithNoneRestart);

      final GenerateAutomationConfigStep stepSpy2 =
          Mockito.spy(getStepWithClusterDescription(clusterDescriptionWithNoneRestart));

      final Result<GenerateAutomationConfigBaseStep.Data> result2 = stepSpy2.perform();
      final AutomationConfig updatedConfig = result2.getData().getAutomationConfig();
      final List<Process> updatedMongods =
          updatedConfig
              .getDeployment()
              .getMongodByClusterName(clusterDescriptionWithNoneRestart.getDeploymentClusterName());
      final List<Process> updatedMongoses =
          updatedConfig
              .getDeployment()
              .getMongosByClusterName(clusterDescriptionWithNoneRestart.getDeploymentClusterName());

      assertTrue(updatedMongods.size() > existingMongods.size());
      assertTrue(updatedMongoses.size() > existingMongoses.size());

      final Map<String, Process> existingMongodMap =
          existingMongods.stream().collect(Collectors.toMap(Process::getName, p -> p));
      for (final Process updatedMongod : updatedMongods) {
        if (existingMongodMap.containsKey(updatedMongod.getName())) {
          final Process existingMongodProcess = existingMongodMap.get(updatedMongod.getName());
          assertNotEquals(existingMongodProcess, updatedMongod);
          assertEquals(updatedDisabledProtocols, updatedMongod.getArgs().getTlsDisabledProtocols());
        } else {
          assertEquals(updatedDisabledProtocols, updatedMongod.getArgs().getTlsDisabledProtocols());
        }
      }

      final Map<String, Process> existingMongosMap =
          existingMongoses.stream().collect(Collectors.toMap(this::getUniqueProcessKey, p -> p));
      for (final Process updatedMongos : updatedMongoses) {
        if (existingMongosMap.containsKey(this.getUniqueProcessKey(updatedMongos))) {
          final Process existingMongosProcess =
              existingMongosMap.get(this.getUniqueProcessKey(updatedMongos));
          assertNotEquals(existingMongosProcess, updatedMongos);
          assertEquals(updatedDisabledProtocols, updatedMongos.getArgs().getTlsDisabledProtocols());
        } else {
          assertEquals(updatedDisabledProtocols, updatedMongos.getArgs().getTlsDisabledProtocols());
        }
      }
    }
  }

  @Test
  public void testChangingClusterPriorityWithoutAddingNodesStableMongosNaming() throws Exception {
    final List<RegionConfig> initialRegionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.AP_SOUTHEAST_1, RegionConfig.MAX_PRIORITY - 1, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.EU_CENTRAL_1, RegionConfig.MAX_PRIORITY - 2, 1, 1, 1, 0));

    final ShardedClusterDescription defaultEmbeddedConfigCluster =
        NDSModelTestFactory.getAWSShardedClusterDescriptionWithEmbeddedConfig(
            _ndsGroup.getGroupId(), false);

    final ShardedClusterDescription clusterDescription =
        defaultEmbeddedConfigCluster
            .copy()
            .setName("foo")
            .setMongoDBVersion(NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion())
            .setMongoDBMajorVersion(
                NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .setDnsPin(_ndsGroup.getDNSPin())
            .setDeploymentClusterName(
                NDSDefaults.generateInternalDeploymentClusterName(_ndsGroup.getGroupId(), "foo"))
            .setGroupId(_ndsGroup.getGroupId())
            .setHostnameSchemeForAgents(HostnameScheme.INTERNAL)
            .setHostnameSubdomainLevel(SubdomainLevel.MONGODB)
            .setDedicatedConfigServerReplicationSpec(
                NDSClusterSvc.calculateDedicatedConfigServerReplicationSpec(
                    defaultEmbeddedConfigCluster.getReplicationSpecsWithShardData(),
                    40.0,
                    AppConfig.getAppSettings(),
                    NDSModelTestFactory.MONGODB_VERSION_CURRENT,
                    false,
                    false,
                    false,
                    InstanceFamilyFlags.withAllFlagsFalse()),
                false)
            .setReplicationSpecList(
                Collections.singletonList(
                    new ReplicationSpec(
                        new ObjectId(),
                        new ObjectId(),
                        new ObjectId(),
                        NDSDefaults.ZONE_NAME,
                        1,
                        initialRegionConfigs)))
            .build();

    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> stepResult = step.perform();
    assertTrue(stepResult.getStatus().isDone());

    // Save and publish the automation config.
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(
        stepResult.getData().getAutomationConfig(), appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final AutomationConfig existingConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());
    final List<Process> existingMongoses =
        existingConfig
            .getDeployment()
            .getMongosByClusterName(clusterDescription.getDeploymentClusterName());

    final ClusterDescription clusterDescriptionSaved =
        _clusterSvc
            .getActiveClusterDescription(_ndsGroup.getGroupId(), clusterDescription.getName())
            .get();

    // Change priorities within the replica sets.
    final ClusterDescription updatedClusterDescription = clusterDescriptionSaved.copy().build();
    updatedClusterDescription.getReplicationSpecsWithShardData().get(0).getRegionConfigs().clear();
    updatedClusterDescription
        .getReplicationSpecsWithShardData()
        .get(0)
        .getRegionConfigs()
        .addAll(
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.AP_SOUTHEAST_1, RegionConfig.MAX_PRIORITY, 2, 0, 0, 0),
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.EU_CENTRAL_1, RegionConfig.MAX_PRIORITY - 1, 1, 1, 1, 0),
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY - 2, 2, 0, 0, 0)));
    _clusterSvc.updateCluster(
        updatedClusterDescription,
        null,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterUpdateContext.forIntTestAction());
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), updatedClusterDescription.getName())
            .get();
    forceHardwareToProvisioned(_ndsGroup, cluster.getClusterDescription());

    final GenerateAutomationConfigStep stepSpy =
        getStepWithClusterDescription(updatedClusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result2 = stepSpy.perform();
    final AutomationConfig updatedConfig = result2.getData().getAutomationConfig();

    // Check that the priority changed for all non-config, non-read-only nodes.
    var existingRSMembers =
        existingConfig.getDeployment().getReplicaSets().stream()
            .flatMap(rss -> rss.getMembers().stream())
            .filter(rss -> rss.getVotes() > 0) // Filter out read-only nodes.
            .filter(rss -> !rss.getHost().contains("config")) // Filter out config nodes.
            .toList();
    updatedConfig.getDeployment().getReplicaSets().stream()
        .flatMap(rss -> rss.getMembers().stream())
        .filter(rss -> rss.getVotes() > 0) // Filter out read-only nodes.
        .filter(rss -> !rss.getHost().contains("config")) // Filter out config nodes.
        .forEach(
            rss -> {
              var matchingExistingRss =
                  existingRSMembers.stream()
                      .filter(mRs -> mRs.getHost().equals(rss.getHost()))
                      .findFirst();
              assertTrue(matchingExistingRss.isPresent());
              assertNotEquals(matchingExistingRss.get().getPriority(), rss.getPriority());
            });

    // Check that the names of mongos processes did not change.
    final List<Process> updatedMongoses =
        updatedConfig
            .getDeployment()
            .getMongosByClusterName(updatedClusterDescription.getDeploymentClusterName());
    updatedMongoses.forEach(
        el -> {
          var match =
              existingMongoses.stream()
                  .filter(p -> p.getHostname().equals(el.getHostname()))
                  .findFirst();
          assertTrue(match.isPresent());
          assertEquals(match.get().getName(), el.getName());
        });
  }

  @Test
  public void testWithholdExistingProcessesDuringConfigUpdates_restore() throws Exception {

    ShardedClusterDescription clusterDescription =
        setUpClusterDescription(true, NDSModelTestFactory.TEST_MONGODB_VERSION, "foo");

    // initial disabledProtocols
    final String initDisabledProtocols = "TLS1_0,TLS1_1";
    final ClusterDescriptionProcessArgsUpdatable.Builder pArgsBuilder =
        new ClusterDescriptionProcessArgsUpdatable.Builder(
            clusterDescription.getName(), _ndsGroup.getGroupId());
    final ClusterDescriptionProcessArgsUpdatable clusterDescriptionProcessArgs =
        _clusterDescriptionProcessArgsDao
            .findForUpdate(
                clusterDescription.getName(),
                _ndsGroup.getGroupId(),
                ClusterDescriptionProcessArgs.Type.STANDARD)
            .map(pArgs -> pArgs.copy().setDisabledTlsProtocols(initDisabledProtocols).build())
            .orElse(pArgsBuilder.setDisabledTlsProtocols(initDisabledProtocols).build());
    _clusterDescriptionProcessArgsDao.save(clusterDescriptionProcessArgs);

    final GenerateAutomationConfigStep step = getStepWithClusterDescription(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    // save and publish the automation config. Manually set backup restore rs version for each
    // mongod
    final AutomationConfig initialConfig = result.getData().getAutomationConfig();
    initialConfig
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
        .forEach(mongod -> mongod.setBackupRestoreRsVersion(1));
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(initialConfig, appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final AutomationConfig existingConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());
    final List<Process> existingMongods =
        existingConfig
            .getDeployment()
            .getMongodByClusterName(clusterDescription.getDeploymentClusterName());
    final List<Process> existingMongoses =
        existingConfig
            .getDeployment()
            .getMongosByClusterName(clusterDescription.getDeploymentClusterName());

    existingMongods.forEach(mongod -> assertEquals(1, (long) mongod.getBackupRestoreRsVersion()));

    // update disabledProtocols
    final String updatedDisabledProtocols = "TLS1_0,TLS1_1,TLS1_2";
    final ClusterDescriptionProcessArgsUpdatable clusterDescriptionProcessArgsUpdate =
        _clusterDescriptionProcessArgsDao
            .findForUpdate(
                clusterDescription.getName(),
                _ndsGroup.getGroupId(),
                ClusterDescriptionProcessArgs.Type.STANDARD)
            .map(pArgs -> pArgs.copy().setDisabledTlsProtocols(updatedDisabledProtocols).build())
            .orElseThrow(
                () -> new RuntimeException("we should have the clusterDescriptionProcessArgs now"));
    _clusterDescriptionProcessArgsDao.save(clusterDescriptionProcessArgsUpdate);

    assertEquals(
        ClusterDescription.ProcessRestartAllowedState.NONE,
        clusterDescription.getNeedsMongoDBConfigPublishRestartAllowed());

    {
      final GenerateAutomationConfigStep stepSpy =
          getStepWithClusterDescription(clusterDescription);
      final Result<GenerateAutomationConfigBaseStep.Data> result2 = stepSpy.perform();
      final AutomationConfig updatedConfig = result2.getData().getAutomationConfig();
      final List<Process> updatedMongods =
          updatedConfig
              .getDeployment()
              .getMongodByClusterName(clusterDescription.getDeploymentClusterName());
      final List<Process> updatedMongoses =
          updatedConfig
              .getDeployment()
              .getMongosByClusterName(clusterDescription.getDeploymentClusterName());

      assertEquals(updatedMongods.size(), existingMongods.size());
      assertEquals(updatedMongoses.size(), existingMongoses.size());

      // mongos processes will be updated since they used to have restore versions set but now they
      // don't
      final Map<String, Process> existingMongodMap =
          existingMongods.stream().collect(Collectors.toMap(Process::getName, p -> p));
      for (final Process updatedMongod : updatedMongods) {
        final Process existingMongodProcess = existingMongodMap.get(updatedMongod.getName());
        assertNotEquals(existingMongodProcess, updatedMongod);
        assertEquals(updatedDisabledProtocols, updatedMongod.getArgs().getTlsDisabledProtocols());
        assertNull(updatedMongod.getBackupRestoreRsVersion());
      }

      // mongos processes will not be updated since they do not have restore versions
      final Map<String, Process> existingMongosMap =
          existingMongoses.stream().collect(Collectors.toMap(this::getUniqueProcessKey, p -> p));
      for (final Process updatedMongos : updatedMongoses) {
        final Process existingMongosProcess =
            existingMongosMap.get(this.getUniqueProcessKey(updatedMongos));
        assertEquals(existingMongosProcess, updatedMongos);
        assertNotEquals(
            updatedDisabledProtocols, updatedMongos.getArgs().getTlsDisabledProtocols());
      }
    }
  }

  @Test
  public void testUpdateMongoDProcessesNotRequiringServerRestart() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _organization, FeatureFlag.ATLAS_SEARCH_QUERY_TELEMETRY);
    final ClusterDescription clusterDescription =
        getAWSClusterDescription(VersionUtils.Version.fromString("6.0.5"))
            .copy()
            .setNeedsMongoDBConfigPublishRestartAllowed(
                ClusterDescription.ProcessRestartAllowedState.NONE)
            .build();

    _clusterSvc.createDefaultProcessArgs(
        _group, clusterDescription, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);

    _clusterSvc.createCluster(
        clusterDescription,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);

    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    forceHardwareToProvisioned(_ndsGroup, clusterDescription);

    assertEquals(
        ClusterDescription.ProcessRestartAllowedState.NONE,
        clusterDescription.getNeedsMongoDBConfigPublishRestartAllowed());

    // initial oplogSizeMB
    final int initOplogSizeMB = 990;

    // initial oplogMinRetentionHours
    final double initOplogMinRetentionHours = 24;

    final ClusterDescriptionProcessArgsUpdatable.Builder pArgsBuilder =
        new ClusterDescriptionProcessArgsUpdatable.Builder(
            clusterDescription.getName(), _ndsGroup.getGroupId());
    final ClusterDescriptionProcessArgsUpdatable clusterDescriptionProcessArgs =
        _clusterDescriptionProcessArgsDao
            .findForUpdate(
                clusterDescription.getName(),
                _ndsGroup.getGroupId(),
                ClusterDescriptionProcessArgs.Type.STANDARD)
            .map(
                pArgs ->
                    pArgs
                        .copy()
                        .setOplogSizeMB(initOplogSizeMB)
                        .setOplogMinRetentionHours(initOplogMinRetentionHours)
                        .build())
            .orElse(
                pArgsBuilder
                    .setOplogSizeMB(initOplogSizeMB)
                    .setOplogMinRetentionHours(initOplogMinRetentionHours)
                    .build());
    _clusterDescriptionProcessArgsDao.save(clusterDescriptionProcessArgs);

    // trigger SQT enablement
    _ftsIndexConfigSvc.addFTSIndex(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        NDSModelTestFactory.getFTSIndex(),
        null);

    final GenerateAutomationConfigStep step = getStepWithClusterDescription(clusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    // save and publish the automation config.
    final AutomationConfig initialConfig = result.getData().getAutomationConfig();
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(initialConfig, appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final AutomationConfig existingConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());
    final List<Process> existingMongods =
        existingConfig
            .getDeployment()
            .getMongodByClusterName(clusterDescription.getDeploymentClusterName());

    // update oplogSizeMB
    final int updatedOplogSizeMB = 2000;

    // update oplogMinRetentionHours
    final double updatedOplogMinRetentionHours = 48;

    final String expectedFilterExpr =
        "{\"$or\":[{\"millis\":{\"$gte\":100}},{\"$and\":[{\"$sampleRate\":0.25},{\"command.pipeline.0.$search.tracking.searchTerms\":{\"$exists\":true}}]}]}";

    final ClusterDescriptionProcessArgsUpdatable clusterDescriptionProcessArgsUpdate =
        _clusterDescriptionProcessArgsDao
            .findForUpdate(
                clusterDescription.getName(),
                _ndsGroup.getGroupId(),
                ClusterDescriptionProcessArgs.Type.STANDARD)
            .map(
                pArgs ->
                    pArgs
                        .copy()
                        .setOplogSizeMB(updatedOplogSizeMB)
                        .setOplogMinRetentionHours(updatedOplogMinRetentionHours)
                        .build())
            .orElseThrow(
                () -> new RuntimeException("we should have the clusterDescriptionProcessArgs now"));
    _clusterDescriptionProcessArgsDao.save(clusterDescriptionProcessArgsUpdate);

    assertEquals(
        ClusterDescription.ProcessRestartAllowedState.NONE,
        clusterDescription.getNeedsMongoDBConfigPublishRestartAllowed());

    {
      final GenerateAutomationConfigStep stepSpy =
          getStepWithClusterDescription(clusterDescription);
      final Result<GenerateAutomationConfigBaseStep.Data> result2 = stepSpy.perform();
      final AutomationConfig updatedConfig = result2.getData().getAutomationConfig();
      final List<Process> updatedMongods =
          updatedConfig
              .getDeployment()
              .getMongodByClusterName(clusterDescription.getDeploymentClusterName());

      assertEquals(updatedMongods.size(), existingMongods.size());

      // mongod processes will be updated since they used to have restore versions set but now they
      // don't
      final Map<String, Process> existingMongodMap =
          existingMongods.stream().collect(Collectors.toMap(Process::getName, p -> p));
      for (final Process updatedMongod : updatedMongods) {
        final Process existingMongodProcess = existingMongodMap.get(updatedMongod.getName());
        assertNotEquals(existingMongodProcess, updatedMongod);
        assertEquals(updatedOplogSizeMB, (int) updatedMongod.getArgs().getOplogSizeMB());
        assertEquals(
            expectedFilterExpr, updatedMongod.getProfilingConfig().getProfilingLevelFilter());
        assertEquals(
            updatedOplogMinRetentionHours,
            updatedMongod.getArgs().getOplogMinRetentionHours(),
            0.0001);
      }
    }
  }

  @Test
  public void testUpdateMongoSProcessesNotRequiringServerRestart() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _organization, FeatureFlag.ATLAS_SEARCH_QUERY_TELEMETRY);

    final ShardedClusterDescription clusterDescription =
        setUpClusterDescription(true, NDSModelTestFactory.TEST_MONGODB_VERSION, "foo");

    final GenerateAutomationConfigStep step = getStepWithClusterDescription(clusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    // save and publish the automation config.
    final AutomationConfig initialConfig = result.getData().getAutomationConfig();
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(initialConfig, appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final AutomationConfig existingConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());
    final List<Process> existingMongoses =
        existingConfig
            .getDeployment()
            .getMongosByClusterName(clusterDescription.getDeploymentClusterName());

    // trigger SQT enablement
    _ftsIndexConfigSvc.addFTSIndex(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        NDSModelTestFactory.getFTSIndex(),
        null);

    final String expectedFilterExpr =
        "{\"$or\":[{\"millis\":{\"$gte\":100}},{\"$and\":[{\"$sampleRate\":0.25},{\"command.pipeline.0.$search.tracking.searchTerms\":{\"$exists\":true}}]}]}";

    assertEquals(
        ClusterDescription.ProcessRestartAllowedState.NONE,
        clusterDescription.getNeedsMongoDBConfigPublishRestartAllowed());

    {
      final GenerateAutomationConfigStep stepSpy =
          getStepWithClusterDescription(clusterDescription);
      final Result<GenerateAutomationConfigBaseStep.Data> result2 = stepSpy.perform();
      final AutomationConfig updatedConfig = result2.getData().getAutomationConfig();
      final List<Process> updatedMongoses =
          updatedConfig
              .getDeployment()
              .getMongosByClusterName(clusterDescription.getDeploymentClusterName());

      assertEquals(updatedMongoses.size(), existingMongoses.size());

      // mongos processes will be updated since they used to have restore versions set but now they
      // don't
      final Map<String, Process> existingMongosMap =
          existingMongoses.stream().collect(Collectors.toMap(Process::getName, p -> p));
      for (final Process updatedMongos : updatedMongoses) {
        final Process existingMongosProcess = existingMongosMap.get(updatedMongos.getName());
        assertNotEquals(existingMongosProcess, updatedMongos);
        assertEquals(
            expectedFilterExpr, updatedMongos.getProfilingConfig().getProfilingLevelFilter());
      }
    }
  }

  private List<Process> getMongoSForShardedCluster(
      final VersionUtils.Version pMongoVersion, final String pUniqueName) throws SvcException {

    final ShardedClusterDescription clusterDescription =
        setUpClusterDescription(false, pMongoVersion, pUniqueName);

    final GenerateAutomationConfigStep step = getStepWithClusterDescription(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    // save and publish the automation config
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(
        result.getData().getAutomationConfig(), appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final AutomationConfig existingConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());
    return existingConfig
        .getDeployment()
        .getMongosByClusterName(clusterDescription.getDeploymentClusterName());
  }

  private void assertMongosProcessStatus(
      final List<Process> pMongoSes, final Consumer<Process> pAssertion) {
    pMongoSes.stream().filter(Process::isMongos).forEach(pAssertion);
  }

  private List<Process> getMongoDForCluster(
      final VersionUtils.Version pMongoVersion, final String pUniqueName) throws SvcException {

    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0));
    final ClusterDescription clusterDescription =
        getAWSClusterDescription(pMongoVersion, regionConfigs, pUniqueName);

    final GenerateAutomationConfigStep step = getStepWithClusterDescription(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    // save and publish the automation config
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(
        result.getData().getAutomationConfig(), appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final AutomationConfig existingConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());
    return existingConfig
        .getDeployment()
        .getMongodByClusterName(clusterDescription.getDeploymentClusterName());
  }

  private void assertMongodProcessStatus(
      final List<Process> pMongoDProcesses, final Consumer<Process> pAssertion) {
    pMongoDProcesses.stream().filter(Process::isMongos).forEach(pAssertion);
  }

  @Test
  public void testSetMaintainedRami() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_RAMI_AGENT);

    final ShardedClusterDescription clusterDescription =
        setUpClusterDescription(false, NDSModelTestFactory.TEST_MONGODB_VERSION, "foo");
    final GenerateAutomationConfigStep step = getStepWithClusterDescription(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    // save and publish the automation config.
    final AutomationConfig initialConfig = result.getData().getAutomationConfig();
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(initialConfig, appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final AutomationConfig existingConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());

    final List<MaintainedRamiConfig> maintainedRamis =
        existingConfig.getDeployment().getMaintainedRamis();
    assertFalse(maintainedRamis.isEmpty());

    final Set<String> existingMongoses =
        existingConfig
            .getDeployment()
            .getMongosByClusterName(clusterDescription.getDeploymentClusterName())
            .stream()
            .map(p -> p.getHostname())
            .collect(Collectors.toSet());

    for (MaintainedRamiConfig maintainedRamiConfig : maintainedRamis) {
      assertTrue(existingMongoses.contains(maintainedRamiConfig.getHostname()));
    }
  }

  @Test
  public void testGetMongotVersion_when_outOfCohort_and_previous_phased_Version()
      throws SvcException {

    _ndsGroupMaintenanceSvc.setUserDefinedMaintenanceWindow(
        _group,
        NDSGroupMaintenanceWindow.builder().dayOfWeek(1).hourOfDay(10).build(),
        AuditInfoHelpers.fromSystem());
    _ndsGroupMaintenanceSvc.setStartASAP(_group.getId());
    reloadNDSGroup();

    final AppUser appUser = getAppUser();
    final AutomationConfig config = _automationConfigSvc.findPublishedOrEmpty(_group.getId());
    config.setMongotTemplate(new MongotTemplate("1.46.0"));
    final MongotConfig coupledMongot = new MongotConfig();
    coupledMongot.setId(0);
    coupledMongot.setHostname("example1.com");
    config.getDeployment().getMongots().add(coupledMongot);

    _automationConfigSvc.saveDraft(config, appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final String previousTargetVersion = "1.47.0";
    final String currentTargetVersion = "1.48.0";
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, previousTargetVersion)
                .setIsPreviousVersionSafe(true)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.MONGOT, currentTargetVersion);

    final Result<GenerateAutomationConfigBaseStep.Data> generateAutomationResult =
        getStepWithClusterDescription(
                setUpClusterDescription(false, NDSModelTestFactory.TEST_MONGODB_VERSION, "foo"))
            .perform();

    assertEquals(
        previousTargetVersion,
        generateAutomationResult.getData().getAutomationConfig().getMongotTemplate().getVersion());

    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(SoftwareType.MONGOT);

    assertEquals(previousTargetVersion, phasedVersion.getPreviousVersion().orElseThrow());
    assertEquals(currentTargetVersion, phasedVersion.getTargetVersion());
  }

  @Test
  public void testGetMongotVersion_when_outOfCohort_and_previous_phased_Version_isSafe_false()
      throws SvcException {

    _ndsGroupMaintenanceSvc.setUserDefinedMaintenanceWindow(
        _group,
        NDSGroupMaintenanceWindow.builder().dayOfWeek(1).hourOfDay(10).build(),
        AuditInfoHelpers.fromSystem());
    _ndsGroupMaintenanceSvc.setStartASAP(_group.getId());
    reloadNDSGroup();

    final AppUser appUser = getAppUser();
    final AutomationConfig config = _automationConfigSvc.findPublishedOrEmpty(_group.getId());
    final String currentVersion = "1.46.0";
    config.setMongotTemplate(new MongotTemplate(currentVersion));
    final MongotConfig coupledMongot = new MongotConfig();
    coupledMongot.setId(0);
    coupledMongot.setHostname("example1.com");
    config.getDeployment().getMongots().add(coupledMongot);

    _automationConfigSvc.saveDraft(config, appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final String previousTargetVersion = "1.47.0";
    final String currentTargetVersion = "1.48.0";
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, previousTargetVersion)
                .setIsPreviousVersionSafe(false)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.MONGOT, currentTargetVersion);

    final Result<GenerateAutomationConfigBaseStep.Data> generateAutomationResult =
        getStepWithClusterDescription(
                setUpClusterDescription(false, NDSModelTestFactory.TEST_MONGODB_VERSION, "foo"))
            .perform();

    assertEquals(
        currentVersion,
        generateAutomationResult.getData().getAutomationConfig().getMongotTemplate().getVersion());
  }

  @Test
  public void testSetMaintainedMongotune() throws Exception {
    ShardedClusterDescription clusterDescription =
        setUpClusterDescription(false, NDSModelTestFactory.TEST_MONGODB_VERSION, "foo");

    clusterDescription =
        clusterDescription
            .copy()
            .setMongotuneStatus(new MongotuneStatus(State.ENABLE, "1.0.0", Map.of()))
            .build();

    final GenerateAutomationConfigStep step = getStepWithClusterDescription(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();

    // save and publish the automation config.
    final AutomationConfig initialConfig = result.getData().getAutomationConfig();
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(initialConfig, appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    final AutomationConfig existingConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());

    final List<MaintainedMongotuneConfig> maintainedMongotunes =
        existingConfig.getDeployment().getMaintainedMongotunes();
    assertFalse(maintainedMongotunes.isEmpty());

    final Set<String> existingMongods =
        existingConfig
            .getDeployment()
            .getMongodByClusterName(clusterDescription.getDeploymentClusterName())
            .stream()
            .map(Process::getHostname)
            .collect(Collectors.toSet());

    for (MaintainedMongotuneConfig maintainedMongotuneConfig : maintainedMongotunes) {
      assertTrue(existingMongods.contains(maintainedMongotuneConfig.getHostname()));
    }
  }

  @Test
  public void testGenerateShardedCluster_loadBalancerSetParams() throws Exception {
    // Version at or above min supported has params set
    assertMongosProcessStatus(
        getMongoSForShardedCluster(
            VersionUtils.MIN_LOAD_BALANCED_MODE_VERSION_FOR_MONGOS, "new-version-with-ff"),
        process -> NDSAutomationTestUtils.assertIndividualMongoSLBState(process, true));
    // Version below min supported does not have params set
    assertMongosProcessStatus(
        getMongoSForShardedCluster(VersionUtils.FIVE_ONE_ZERO, "old-version-with-ff"),
        process -> NDSAutomationTestUtils.assertIndividualMongoSLBState(process, false));
  }

  @Test
  public void testGenerateCluster_proxyProtocolPort_FFEnabled() throws Exception {
    _group.enableFeatureFlag(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS);
    // Version at or above min supported has proxy protocol port
    assertMongodProcessStatus(
        getMongoDForCluster(VersionUtils.MIN_PROXY_PROTOCOL_PORT, "new-version-with-ff"),
        process -> NDSAutomationTestUtils.assertIndividualMongoDProxyPort(process, true));
    // Version below min supported does not have proxy protocol port
    assertMongosProcessStatus(
        getMongoDForCluster(VersionUtils.SEVEN_THREE_ZERO, "old-version-with-ff"),
        process -> NDSAutomationTestUtils.assertIndividualMongoDProxyPort(process, false));
  }

  @Test
  public void testGenerateCluster_proxyProtocolPort_FFNotEnabled() throws Exception {
    // Version at or above min supported does not have proxy protocol port due to ff not enabled
    assertMongodProcessStatus(
        getMongoDForCluster(VersionUtils.MIN_PROXY_PROTOCOL_PORT, "new-version-with-ff"),
        process -> NDSAutomationTestUtils.assertIndividualMongoDProxyPort(process, false));
    // Version below min supported does not have proxy protocol port
    assertMongosProcessStatus(
        getMongoDForCluster(VersionUtils.SEVEN_THREE_ZERO, "old-version-with-ff"),
        process -> NDSAutomationTestUtils.assertIndividualMongoDProxyPort(process, false));
  }

  private String getUniqueProcessKey(Process pProcess) {
    return String.format("%s:%s", pProcess.getName(), pProcess.getHostname());
  }

  private GenerateAutomationConfigStep getStepWithClusterDescription(
      final ClusterDescription pClusterDescription) {
    return getStepWithClusterDescription(pClusterDescription, List.of(pClusterDescription));
  }

  private GenerateAutomationConfigStep getStepWithClusterDescription(
      final ClusterDescription pTargetClusterDescription,
      final List<ClusterDescription> pActiveClusterDescriptionsForGroup) {
    final Plan plan = new Plan(_ndsGroup.getGroupId(), _planContextFactory);
    plan.addMove(new DummyMove());
    _planDao.save(plan);

    final List<Cluster> clusters =
        pActiveClusterDescriptionsForGroup.stream()
            .map(
                c ->
                    Cluster.getCluster(
                        c,
                        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), c.getName())))
            .collect(Collectors.toList());

    final Cluster targetCluster =
        clusters.stream()
            .filter(c -> c.getClusterDescription().equals(pTargetClusterDescription))
            .findFirst()
            .orElseThrow(
                () -> new IllegalArgumentException("Target Cluster not found in Cluster List"));

    return getAutomationConfigStep(plan, clusters);
  }

  private ShardedClusterDescription getGeoShardedClusterDescription(
      final List<ReplicationSpec> pReplicationSpecs,
      final GeoSharding pGeoSharding,
      final String pSetName) {
    return NDSModelTestFactory.getGeoShardedClusterDescription(
        pReplicationSpecs, pGeoSharding, pSetName, _ndsGroup.getDNSPin(), _ndsGroup.getGroupId());
  }

  private GenerateAutomationConfigStep getPreparedStepForCluster(
      final ClusterDescription pClusterDescription) throws Exception {
    return getPreparedStepForClusterWithHostnames(pClusterDescription, null);
  }

  private GenerateAutomationConfigStep getPreparedStepForClusterWithHostnames(
      final ClusterDescription pClusterDescription, final List<String> pHostnames)
      throws Exception {
    _clusterSvc.createDefaultProcessArgs(
        _group, pClusterDescription, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        pClusterDescription,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    forceHardwareToProvisioned(_ndsGroup, pClusterDescription, pHostnames);

    final Plan plan = new Plan(_ndsGroup.getGroupId(), _planContextFactory);
    plan.addMove(new DummyMove());
    _planDao.save(plan);

    final List<Cluster> clusters =
        Collections.singletonList(pClusterDescription).stream()
            .map(
                c ->
                    Cluster.getCluster(
                        c,
                        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), c.getName())))
            .collect(Collectors.toList());

    return getAutomationConfigStep(plan, clusters);
  }

  @Test
  public void testGenerateReplicaSet_NoLDAP() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _organization, FeatureFlag.ATLAS_ALLOW_DEPRECATED_VERSIONS);
    testGenerateReplicaSet(
        _versionDeprecationSettingsSvc
            .getAtlasMinimumMongoDBVersionSupportedByAgent()
            .getMajorVersionString());
  }

  @Test
  public void testGenerateReplicaSet_withLDAP() throws Exception {
    final NDSUserSecurity userSecurity = _ndsGroup.getUserSecurity();
    final NDSLDAP ndsldap =
        new NDSLDAP(
            true,
            true,
            "iamsuchanldapserver.ldapfanatix.com",
            389,
            "ldapUser",
            "IfIWereRealThizWouldBeMyPazzW0rd-:>",
            null,
            null,
            null);
    userSecurity.setLDAP(ndsldap);
    _ndsGroupDao.updateCustomerUserSecurity(_ndsGroup.getGroupId(), userSecurity);
    testGenerateReplicaSet(NDSModelTestFactory.TEST_MONGODB_VERSION.getMajorVersionString());
  }

  @Test
  public void testGenerateReplicaSet_withOIDC() throws Exception {
    final OidcIdentityProvider identityProvider =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);
    final OidcIdentityProvider identityProviderNoAssociatedDomain =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);
    identityProviderNoAssociatedDomain.setAssociatedDomains(Set.of());

    final OidcIdentityProvider workforceIdpUserAuthType =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.USER);

    final OidcIdentityProvider workloadIdentityProvider =
        NDSFederationTestingUtils.getWorkloadOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.USER);

    final OidcIdentityProvider workloadIdentityProviderGroupAuthType =
        NDSFederationTestingUtils.getWorkloadOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);

    final Set<IdentityProvider> idps =
        Set.of(
            identityProvider,
            identityProviderNoAssociatedDomain,
            workforceIdpUserAuthType,
            workloadIdentityProvider,
            workloadIdentityProviderGroupAuthType);

    NDSFederationTestingUtils.linkIdentityProvidersToOrg(idps, _group, _federationSettingsDao);

    testGenerateReplicaSet(VersionUtils.MIN_OIDC_WORKLOAD_VERSION_7_0.getVersion());
  }

  @Test
  public void testGenerateReplicaSet_withOIDC_workloadUnsupported() throws Exception {
    final OidcIdentityProvider identityProvider =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);
    final OidcIdentityProvider identityProviderNoAssociatedDomain =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);
    identityProviderNoAssociatedDomain.setAssociatedDomains(Set.of());

    final OidcIdentityProvider workforceIdpUserAuthType =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.USER);

    final OidcIdentityProvider workloadIdentityProvider =
        NDSFederationTestingUtils.getWorkloadOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.USER);

    final OidcIdentityProvider workloadIdentityProviderGroupAuthType =
        NDSFederationTestingUtils.getWorkloadOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);

    final Set<IdentityProvider> idps =
        Set.of(
            identityProvider,
            identityProviderNoAssociatedDomain,
            workforceIdpUserAuthType,
            workloadIdentityProvider,
            workloadIdentityProviderGroupAuthType);

    NDSFederationTestingUtils.linkIdentityProvidersToOrg(idps, _group, _federationSettingsDao);

    // Workload not supported
    testGenerateReplicaSet(VersionUtils.SEVEN_ZERO_ZERO.getVersion());
  }

  @Test
  public void testGenerateReplicaSet_withOIDC_workloadUnsupported_onOneCluster() throws Exception {
    final OidcIdentityProvider identityProvider =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);
    final OidcIdentityProvider identityProviderNoAssociatedDomain =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);
    identityProviderNoAssociatedDomain.setAssociatedDomains(Set.of());

    final OidcIdentityProvider workforceIdpUserAuthType =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.USER);

    final OidcIdentityProvider workloadIdentityProvider =
        NDSFederationTestingUtils.getWorkloadOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.USER);

    final OidcIdentityProvider workloadIdentityProviderGroupAuthType =
        NDSFederationTestingUtils.getWorkloadOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);

    final Set<IdentityProvider> idps =
        Set.of(
            identityProvider,
            identityProviderNoAssociatedDomain,
            workforceIdpUserAuthType,
            workloadIdentityProvider,
            workloadIdentityProviderGroupAuthType);

    NDSFederationTestingUtils.linkIdentityProvidersToOrg(idps, _group, _federationSettingsDao);

    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0));
    final ClusterDescription clusterDescription0 =
        getAWSClusterDescription(
            VersionUtils.Version.fromString("8.0.0"), regionConfigs, "cluster0");
    _clusterSvc.createDefaultProcessArgs(
        _group, clusterDescription0, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription0,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    forceHardwareToProvisioned(_ndsGroup, clusterDescription0, "123.456.789.000");

    final ClusterDescription clusterDescription1 =
        getAWSClusterDescription(VersionUtils.Version.fromString("7.0.8"), "cluster1");
    _clusterSvc.createDefaultProcessArgs(
        _group, clusterDescription1, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription1,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    forceHardwareToProvisioned(_ndsGroup, clusterDescription1, "987.654.321.000");

    final Plan plan = new Plan(_ndsGroup.getGroupId(), _planContextFactory);
    plan.addMove(new DummyMove());
    _planDao.save(plan);

    final List<Cluster> clusters =
        Stream.of(clusterDescription0, clusterDescription1)
            .map(
                c ->
                    Cluster.getCluster(
                        c,
                        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), c.getName())))
            .collect(Collectors.toList());

    final GenerateAutomationConfigStep step = getAutomationConfigStep(plan, clusters);

    // Safe replica set reconfig requires only one publish when creating clusters.
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertCommon(clusterDescription0, resultConfig);
    assertCommon(clusterDescription1, resultConfig);
  }

  @Test
  public void testGenerateReplicaSet_withRemovingOIDCFromExistingConfig() throws Exception {
    final OidcIdentityProvider identityProvider =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);
    NDSFederationTestingUtils.linkIdentityProvidersToOrg(
        Set.of(identityProvider), _group, _federationSettingsDao);

    // Generate and publish config
    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_UPCOMING.getMajorVersionString());
    final Result<GenerateAutomationConfigBaseStep.Data> result =
        getStepWithClusterDescription(clusterDescription).perform();
    final AppUser appUser = getAppUser();
    _automationConfigSvc.saveDraft(
        result.getData().getAutomationConfig(), appUser, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, appUser);

    // Oidc settings should not be null
    assertEquals(
        1, result.getData().getAutomationConfig().getDeployment().getOidcProviderConfigs().size());

    // Remove IdP from org
    final FederationSettings existingFederationSettings =
        _federationSettingsDao.findByOrgId(_group.getOrgId()).orElseThrow();
    _federationSettingsDao.deleteIdentityProvider(existingFederationSettings, identityProvider);

    // Publish new config
    final AutomationConfig resultConfig =
        testGenerateReplicaSet(
            NDSModelTestFactory.MONGODB_VERSION_UPCOMING.getMajorVersionString());
    assertNull(resultConfig.getDeployment().getOidcProviderConfigs());
  }

  @Test
  public void testGenerateReplicaSet_withOIDC_withOneIdPWithNoAssociatedDomain() throws Exception {
    final OidcIdentityProvider identityProviderNoAssociatedDomain =
        NDSFederationTestingUtils.getWorkforceOidcIdentityProvider(
            OidcIdentityProvider.AuthorizationType.GROUP);
    identityProviderNoAssociatedDomain.setAssociatedDomains(Set.of());
    NDSFederationTestingUtils.linkIdentityProvidersToOrg(
        Set.of(identityProviderNoAssociatedDomain), _group, _federationSettingsDao);

    testGenerateReplicaSet(NDSModelTestFactory.MONGODB_VERSION_UPCOMING.getMajorVersionString());
  }

  @Test
  public void testGenerateReplicaSet_withOIDC_withHardcodedIdP() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, _organization, FeatureFlag.ATLAS_PROVIDE_HARDCODED_OIDC_IDP_INFORMATION);
    testGenerateReplicaSet(NDSModelTestFactory.MONGODB_VERSION_UPCOMING.getMajorVersionString());
  }

  private ClusterDescription getAWSClusterDescription(final String pMongoDBVersionString) {
    return getAWSClusterDescription(VersionUtils.Version.fromString(pMongoDBVersionString + ".0"));
  }

  private ClusterDescription getAWSClusterDescription_withClusterName(final String clusterName) {
    return getAWSClusterDescription(VersionUtils.Version.fromString("7.0.0"), clusterName);
  }

  private ClusterDescription getAWSClusterDescription(final VersionUtils.Version mongoDBVersion) {
    return getAWSClusterDescription(mongoDBVersion, "foo");
  }

  private ClusterDescription getAWSClusterDescription(
      final VersionUtils.Version mongoDBVersion, final String pClusterName) {
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0));

    return getAWSClusterDescription(mongoDBVersion, regionConfigs, pClusterName);
  }

  private ClusterDescription getAWSClusterDescription(
      final VersionUtils.Version mongoDBVersion,
      final List<RegionConfig> regionConfigs,
      final String clusterName) {

    final ReplicationSpec replicationSpec =
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            1,
            regionConfigs);

    return new ClusterDescription.Builder<>()
        .setReplicationSpecList(Collections.singletonList(replicationSpec))
        .mixin(ClusterDescriptionBuilderTestMixin::new)
        .updateAllHardware(
            new AWSHardwareSpec.Builder()
                .setInstanceSize(AWSNDSInstanceSize.M30)
                .setDiskIOPS(AWSNDSInstanceSize.M30.getGP3StandardEBSIOPS(10)))
        .setClusterType(ClusterDescription.ClusterType.REPLICASET)
        .setInternalClusterRole(InternalClusterRole.NONE)
        .setName(clusterName)
        .setClusterNamePrefix(clusterName)
        .setDnsPin(_ndsGroup.getDNSPin())
        .setCreateDate(new Date())
        .setDiskSizeGB(10)
        .setGroupId(_ndsGroup.getGroupId())
        .setLastUpdateDate(new Date())
        .setMongoDBVersion(mongoDBVersion.getVersion())
        .setMongoDBMajorVersion(mongoDBVersion.getMajorVersionString())
        .setMongoUriHosts(new String[0])
        .setPrivateMongoUriHosts(new String[0])
        .setMongoUriLastUpdateDate(new Date())
        .setState(ClusterDescription.State.WORKING)
        .setRestoreJobIds(Collections.emptyList())
        .setNeedsMongoDBConfigPublishAfter(new Date())
        .setAutoScalingForProvider(
            CloudProvider.AWS,
            AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
            NodeTypeFamily.BASE)
        .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
        .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
        .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
        .setHostnameSchemeForAgents(HostnameScheme.INTERNAL)
        .setHostnameSubdomainLevel(SubdomainLevel.MONGODB)
        .setDeploymentClusterName(
            NDSDefaults.generateInternalDeploymentClusterName(_ndsGroup.getGroupId(), clusterName))
        .setUniqueId(new ObjectId())
        .build();
  }

  private AutomationConfig testGenerateReplicaSet(final String pMongoDBVersionString)
      throws Exception {
    final VersionUtils.Version version = VersionUtils.Version.fromString(pMongoDBVersionString);
    final ClusterDescription clusterDescription = getAWSClusterDescription(version);
    _clusterSvc.createDefaultProcessArgs(
        _group, clusterDescription, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    forceHardwareToProvisioned(_ndsGroup, clusterDescription);

    final Plan plan = new Plan(_ndsGroup.getGroupId(), _planContextFactory);
    plan.addMove(new DummyMove());
    _planDao.save(plan);

    final List<Cluster> clusters =
        Collections.singletonList(clusterDescription).stream()
            .map(
                c ->
                    Cluster.getCluster(
                        c,
                        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), c.getName())))
            .collect(Collectors.toList());

    final GenerateAutomationConfigStep step = getAutomationConfigStep(plan, clusters);

    // Safe replica set reconfig requires only one publish when creating clusters.
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig resultConfig = result.getData().getAutomationConfig();

    assertCommon(clusterDescription, resultConfig);

    assertEquals(
        0,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOS)
            .count());
    assertEquals(
        3,
        resultConfig.getDeployment().getProcesses().stream()
            .filter(p -> p.getProcessType() == ProcessType.MONGOD)
            .count());
    assertEquals(
        0,
        resultConfig.getDeployment().getProcesses().stream().filter(Process::isConfigSvr).count());

    assertMaxIncomingConnectionsOverrides(clusters, resultConfig);
    assertAuthRestrictions(_ndsGroup, clusters, resultConfig);

    return resultConfig;
  }

  @Test
  public void testAddPendingIndexes() throws Exception {
    final AppUser appUser = MmsFactory.createUser(_group);

    final ReplicationSpec replicationSpec =
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            1,
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0)));

    final String[] clusterNames = {"GoAheadAddIndexesIDareYou", "GoAheadAddIndexesIDareYouAgain"};
    for (final String clusterName : clusterNames) {
      final ClusterDescription clusterDescription =
          new ClusterDescription.Builder<>()
              .setReplicationSpecList(Collections.singletonList(replicationSpec))
              .mixin(ClusterDescriptionBuilderTestMixin::new)
              .updateAllHardware(
                  new AWSHardwareSpec.Builder()
                      .setInstanceSize(AWSNDSInstanceSize.M30)
                      .setDiskIOPS(AWSNDSInstanceSize.M30.getGP3StandardEBSIOPS(10)))
              .setClusterType(ClusterDescription.ClusterType.REPLICASET)
              .setInternalClusterRole(InternalClusterRole.NONE)
              .setName(clusterName)
              .setClusterNamePrefix(clusterName.toLowerCase())
              .setDnsPin(_ndsGroup.getDNSPin())
              .setCreateDate(new Date())
              .setDiskSizeGB(10)
              .setGroupId(_ndsGroup.getGroupId())
              .setLastUpdateDate(new Date())
              .setMongoDBVersion("4.2.1")
              .setMongoDBMajorVersion("4.2")
              .setMongoUriHosts(new String[0])
              .setPrivateMongoUriHosts(new String[0])
              .setMongoUriLastUpdateDate(new Date())
              .setState(ClusterDescription.State.WORKING)
              .setRestoreJobIds(Collections.emptyList())
              .setNeedsMongoDBConfigPublishAfter(new Date())
              .setAutoScalingForProvider(
                  CloudProvider.AWS,
                  AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                  NodeTypeFamily.BASE)
              .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
              .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
              .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
              .setHostnameSchemeForAgents(HostnameScheme.LEGACY)
              .setHostnameSubdomainLevel(SubdomainLevel.MONGODB)
              .setDeploymentClusterName(clusterName)
              .build();
      _clusterDescriptionUpdatesDao.save(clusterDescription);
      _clusterSvc.mergePendingClusterChanges(_ndsGroup);
      forceHardwareToProvisioned(_ndsGroup, clusterDescription);
    }

    final Plan firstConfigPlan = new Plan(_ndsGroup.getGroupId(), _planContextFactory);
    firstConfigPlan.addMove(new DummyMove());
    _planDao.save(firstConfigPlan);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());

    final GenerateAutomationConfigStep step = getAutomationConfigStep(firstConfigPlan, clusters);

    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig initialAutomationConfig = result.getData().getAutomationConfig();
    _automationConfigSvc.saveDraft(initialAutomationConfig, appUser, _organization, _group);
    assertEquals(0, initialAutomationConfig.getDeployment().getIndexConfigs().size());

    // publish the current config
    _automationConfigSvc.publish(_organization, _group, appUser);

    // add pending indexes
    for (final Cluster cluster : clusters) {
      final List<IndexConfig> pendingIndexConfigs =
          cluster.getLiveReplicaSets().stream()
              .flatMap(
                  rs ->
                      Arrays.asList("a", "b", "c").stream()
                          .map(
                              fieldName ->
                                  NDSModelTestFactory.getIndexConfigModel(rs.getRsId(), fieldName)))
              .collect(Collectors.toList());
      _clusterSvc.addPendingIndexes(
          _ndsGroup.getGroupId(), cluster.getClusterDescription().getName(), pendingIndexConfigs);
    }

    final List<Cluster> clustersWithPendingIndexes =
        _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());

    final Set<String> existingMongodProcessesHostnameAndPort =
        initialAutomationConfig.getDeployment().getProcesses().stream()
            .map(Process::getHostnameAndPort)
            .collect(Collectors.toSet());
    final Set<String> existingReplicaSetIds =
        initialAutomationConfig.getDeployment().getReplicaSets().stream()
            .map(ReplicaSet::getId)
            .collect(Collectors.toSet());

    // execute the plans to add the indexes from the clusters
    for (int i = 0; i < clustersWithPendingIndexes.size(); i++) {
      final Plan addIndexesConfigPlan = new Plan(_ndsGroup.getGroupId(), _planContextFactory);
      addIndexesConfigPlan.addMove(new DummyMove());
      _planDao.save(addIndexesConfigPlan);

      // run the generate automation config to add the indexes
      final GenerateAutomationConfigStep addIndexesStep =
          new GenerateAutomationConfigStep(
              _planContextFactory.create(_ndsGroup.getGroupId(), addIndexesConfigPlan.getId()),
              new Step.State(
                  addIndexesConfigPlan.getId(),
                  addIndexesConfigPlan.getMoves().get(0).getId(),
                  0,
                  addIndexesConfigPlan.getPlanContext().getPlanDao()),
              clustersWithPendingIndexes,
              clustersWithPendingIndexes.get(i),
              existingMongodProcessesHostnameAndPort,
              existingReplicaSetIds,
              _ndsGroup,
              _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId()),
              _customMongoDbBuildDao,
              _hostClusterSvc,
              _automatedBackupRestoreSvc,
              _backupRestoreJobSvc,
              _backupDeploymentSvc,
              _onlineArchiveDao,
              _clusterSvc,
              _mongotConfigSvc,
              _clusterDescriptionProcessArgsDao,
              _backupRestoreJobDao,
              _backupSnapshotDao,
              _ndsGroupDao,
              _ndsGroupMaintenanceSvc,
              _group,
              _parallelRestoreSvc,
              _connectionRuleWithHostnamesSvc
                  .getClusterPerInstanceConnectionRulesForClusterInstances(
                      _ndsGroupDao.find(_ndsGroup.getGroupId()).get(), clusters.get(0)),
              _restoreStepUtils,
              _searchDeploymentDescriptionSvc,
              _automationConfigDeploymentBuilderFactory,
              null,
              _dbCheckSvc,
              _atlasUISKeysSvc,
              _serverlessMtmPoolSvc,
              _federationAppIdentityProvidersSvc,
              _organizationDao,
              _searchInstanceSvc,
              _featureFlagSvc,
              _searchIndexConfigSvc,
              _lastAgentStatusSvc,
              _rollableValueStateSvc,
              _mongotunePolicyInitializationSvc,
              _ifrPlanningUtil);

      final Result<GenerateAutomationConfigBaseStep.Data> addIndexesResult =
          addIndexesStep.perform();
      assertTrue(addIndexesResult.getStatus().isDone());

      // expected the IndexConfigs
      final Map<IndexConfig, String> expectedPendingIndexesResultsMap =
          i == 0
              ? clustersWithPendingIndexes
                  .get(0)
                  .getClusterDescription()
                  .getPendingIndexes()
                  .stream()
                  .collect(
                      Collectors.toMap(
                          pi -> pi, pi -> pi.getRsName())) // only indexes from first cluster
              : clustersWithPendingIndexes.stream()
                  .flatMap(cluster -> cluster.getClusterDescription().getPendingIndexes().stream())
                  .collect(
                      Collectors.toMap(
                          pi -> pi, pi -> pi.getRsName())); // indexes from all clusters

      final AutomationConfig addIndexesResultConfig =
          addIndexesResult.getData().getAutomationConfig();
      final Deployment addIndexResultConfigDeployment = addIndexesResultConfig.getDeployment();
      assertEquals(
          expectedPendingIndexesResultsMap.size(),
          addIndexResultConfigDeployment.getIndexConfigs().size());

      for (final IndexConfig deploymentIndexConfig :
          addIndexResultConfigDeployment.getIndexConfigs()) {
        assertEquals(
            expectedPendingIndexesResultsMap.get(deploymentIndexConfig),
            deploymentIndexConfig.getRsName());
      }

      // publish the config with indexes
      _automationConfigSvc.saveDraft(addIndexesResultConfig, appUser, _organization, _group);
      _automationConfigSvc.publish(_organization, _group, appUser);
    }

    // execute plans to clear the indexes from the clusters
    for (int i = 0; i < clustersWithPendingIndexes.size(); i++) {
      _clusterSvc.deleteAllPendingIndexes(
          _ndsGroup.getGroupId(),
          clustersWithPendingIndexes.get(i).getClusterDescription().getName(),
          AuditInfoHelpers.fromSystem());
      final Plan clearPendingIndexesConfigPlan =
          new Plan(_ndsGroup.getGroupId(), _planContextFactory);
      clearPendingIndexesConfigPlan.addMove(new DummyMove());
      _planDao.save(clearPendingIndexesConfigPlan);

      final List<Cluster> activeClusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
      final GenerateAutomationConfigStep clearIndexesStep =
          new GenerateAutomationConfigStep(
              _planContextFactory.create(
                  _ndsGroup.getGroupId(), clearPendingIndexesConfigPlan.getId()),
              new Step.State(
                  clearPendingIndexesConfigPlan.getId(),
                  clearPendingIndexesConfigPlan.getMoves().get(0).getId(),
                  0,
                  clearPendingIndexesConfigPlan.getPlanContext().getPlanDao()),
              activeClusters,
              activeClusters.get(i),
              existingMongodProcessesHostnameAndPort,
              existingReplicaSetIds,
              _ndsGroup,
              _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId()),
              _customMongoDbBuildDao,
              _hostClusterSvc,
              _automatedBackupRestoreSvc,
              _backupRestoreJobSvc,
              _backupDeploymentSvc,
              _onlineArchiveDao,
              _clusterSvc,
              _mongotConfigSvc,
              _clusterDescriptionProcessArgsDao,
              _backupRestoreJobDao,
              _backupSnapshotDao,
              _ndsGroupDao,
              _ndsGroupMaintenanceSvc,
              _group,
              _parallelRestoreSvc,
              _connectionRuleWithHostnamesSvc
                  .getClusterPerInstanceConnectionRulesForClusterInstances(
                      _ndsGroupDao.find(_ndsGroup.getGroupId()).get(), clusters.get(0)),
              _restoreStepUtils,
              _searchDeploymentDescriptionSvc,
              _automationConfigDeploymentBuilderFactory,
              null,
              _dbCheckSvc,
              _atlasUISKeysSvc,
              _serverlessMtmPoolSvc,
              _federationAppIdentityProvidersSvc,
              _organizationDao,
              _searchInstanceSvc,
              _featureFlagSvc,
              _searchIndexConfigSvc,
              _lastAgentStatusSvc,
              _rollableValueStateSvc,
              _mongotunePolicyInitializationSvc,
              _ifrPlanningUtil);

      final Result<GenerateAutomationConfigBaseStep.Data> clearIndexesResult =
          clearIndexesStep.perform();
      assertTrue(clearIndexesResult.getStatus().isDone());

      // expected the IndexConfigs
      final Map<IndexConfig, String> expectedPendingIndexesResultsMap =
          i == 0
              ? clustersWithPendingIndexes
                  .get(1)
                  .getClusterDescription()
                  .getPendingIndexes()
                  .stream()
                  .collect(
                      Collectors.toMap(
                          pi -> pi, pi -> pi.getRsName())) // only indexes from second cluster
              : Collections.emptyMap(); // no more index after the second cluster

      final AutomationConfig clearIndexesResultConfig =
          clearIndexesResult.getData().getAutomationConfig();
      final Deployment clearIndexResultConfigDeployment = clearIndexesResultConfig.getDeployment();
      assertEquals(
          expectedPendingIndexesResultsMap.size(),
          clearIndexResultConfigDeployment.getIndexConfigs().size());

      for (final IndexConfig deploymentIndexConfig :
          clearIndexResultConfigDeployment.getIndexConfigs()) {
        assertEquals(
            expectedPendingIndexesResultsMap.get(deploymentIndexConfig),
            deploymentIndexConfig.getRsName());
      }

      // after removed the indexes from the second cluster, then there should be no more indexes.
      if (i == clustersWithPendingIndexes.size() - 1) {
        assertEquals(0, clearIndexesResultConfig.getDeployment().getIndexConfigs().size());
      }

      // publish the config with indexes
      _automationConfigSvc.saveDraft(clearIndexesResultConfig, appUser, _organization, _group);
      _automationConfigSvc.publish(_organization, _group, appUser);
    }
  }

  @Test
  public void testGenerateClusterAndUISKeysNonServerlessCluster() throws Exception {
    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());
    assertFalse(clusterDescription.isMTM());
    final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);
    final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig resultConfig1 = result.getData().getAutomationConfig();

    // Ensure there are no Atlas UIS keys for this non-MTM cluster
    final List<UISKeyAgentView> keys = resultConfig1.getDeployment().getUISPublicKeys();
    assertTrue(keys.isEmpty());
  }

  @Test
  public void testGenerateCluster_IFRFlagsPresent() throws Exception {
    // Setup: Create a phased version with IFR rollout mode
    final SoftwareType softwareType = SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION;
    final String majorVersion = "8.2";
    final String targetVersion = "8.2.0-alpha1";
    final ObjectId expId = new ObjectId();
    final int waveNumber = 1;

    final IFRManifest manifest = _ifrManifestSvc.getManifest(targetVersion).orElseThrow();

    // Insert PhasedVersion with the IFR Rollout.
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(softwareType, targetVersion)
                .setReleaseMode(ReleaseMode.IFR)
                .setIfrState(
                    new IFRState(
                        expId,
                        ExperimentStatus.LIVE,
                        RolloutType.FEATURES,
                        waveNumber,
                        WaveStatus.FINALIZED))));

    // Test that the IFR manifest initialConfiguration arguments are present.
    {
      final ClusterDescription clusterDescription =
          getAWSClusterDescription(majorVersion)
              .copy()
              .setMongoDBVersion(targetVersion)
              .setVersionReleaseSystem(ClusterDescription.VersionReleaseSystem.CONTINUOUS)
              .build();
      final GenerateAutomationConfigStep step = getPreparedStepForCluster(clusterDescription);
      final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
      assertTrue(result.getStatus().isDone());

      final AutomationConfig resultConfig = result.getData().getAutomationConfig();
      resultConfig
          .getDeployment()
          .getProcesses()
          .forEach(
              p ->
                  manifest
                      .initialConfiguration()
                      .forEach(
                          (ifrFlag, ifrValue) -> {
                            assertTrue(p.getArgs().getSetParameterMap().containsKey(ifrFlag));
                            assertEquals(ifrValue, p.getArgs().getSetParameterMap().get(ifrFlag));
                          }));
    }
  }

  @Test
  public void testGenerateClusterWithServerlessMTMAndUISKeys() throws Exception {

    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());

    // Group is a serverless mtm group
    _ndsGroupDao.setMTMPassword(_ndsGroup.getGroupId(), EncryptionUtils.genEncryptStr("secure"));
    _ndsGroupDao.setServerlessMTMHolder(_ndsGroup.getGroupId());
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();
    final ServerlessMTMPool pool =
        ServerlessTestFactory.getServerlessMTMPool(
            new ObjectId(),
            "pool0",
            _ndsGroup.getGroupId(),
            CloudProvider.AWS,
            ServerlessAutoScalingStrategyType.V2,
            new ArrayList<>(),
            new ArrayList<>(),
            true,
            true,
            new ArrayList<>());
    _serverlessMTMPoolDao.createPool(pool);
    final EnvoyInstance instance =
        ServerlessDeploymentModelTestFactory.getProvisionedAWSEnvoyInstance(new ObjectId(), 0);
    // Insert a key into the DB (simulate a UIS uploading a key)
    _atlasUISKeysDao.createKey(
        new AtlasUISKey(
            new ObjectId(),
            pool.getId(),
            instance.getId(),
            Date.from(Instant.now()),
            "TestPEM" + instance.getId(),
            null,
            null));

    final ClusterDescription mtmClusterDescription =
        clusterDescription
            .copy()
            .setIsMTM(true)
            .setName(clusterDescription.getName() + "-MTM")
            .build();
    final GenerateAutomationConfigStep mtmStep = getPreparedStepForCluster(mtmClusterDescription);

    final Result<GenerateAutomationConfigBaseStep.Data> result = mtmStep.perform();
    assertTrue(result.getStatus().isDone());
    final AutomationConfig resultConfig1 = result.getData().getAutomationConfig();
    validateAutomationConfigForServerlessMTM(
        mtmClusterDescription, resultConfig1, LogLevel.DEBUG.name());

    // Ensure we are able to fetch the public keys fetched by the UIS
    final List<UISKeyAgentView> keys = resultConfig1.getDeployment().getUISPublicKeys();
    assertEquals(1, keys.size());
    assertEquals("TestPEM" + instance.getId(), keys.get(0).getPublicKeyPEM());
  }

  @Test
  public void testGetDeploymentMongoTProcesses() throws Exception {
    FeatureFlagIntTestUtil.disableFeatureGlobally(FeatureFlag.ATLAS_SEARCH_USE_LIFECYCLE_MANAGER);

    // disable ATLAS_SEARCH_STALE_INDEXES
    FeatureFlagIntTestUtil.disableFeatureGlobally(FeatureFlag.ATLAS_SEARCH_STALE_INDEXES);
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_SEARCH_STALE_INDEXES);
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, _organization, FeatureFlag.ATLAS_SEARCH_STALE_INDEXES);

    final ReplicationSpec replicationSpec =
        CrossCloudReplicationSpecModelTestFactory.getDefaultCrossCloudReplicationSpec(
            List.of(
                CrossCloudReplicationSpecModelTestFactory.getDefaultAWSShardRegionConfig(
                    AWSNDSInstanceSize.M30, AWSInstanceFamily.M4)));
    final AppUser appUser = MmsFactory.createUser(_group);

    // Create cluster
    final ShardedClusterDescription clusterDescription =
        getGeoShardedClusterDescription(
                Collections.singletonList(replicationSpec),
                GeoShardingView.getDefaultGeoShardingView().toGeoSharding(),
                "foo0")
            .copy()
            .setMongoDBMajorVersion(
                NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .setMongoDBVersion(NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion())
            .build();
    _clusterSvc.createDefaultProcessArgs(
        _group, clusterDescription, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    forceHardwareToProvisioned(_ndsGroup, clusterDescription, "123.456.789.000");

    // Initialize process args for cluster
    final ClusterDescriptionProcessArgsView defaultProcessArgsView =
        ClusterDescriptionProcessArgsView.getDefaultProcessArgsView(
            Optional.of(clusterDescription));
    _clusterSvc.updateProcessArgs(
        _group,
        clusterDescription.getName(),
        defaultProcessArgsView,
        AuditInfoHelpers.fromSystem(),
        null,
        appUser);

    // Create GenerateAutomationConfigStep
    final Plan plan = new Plan(_ndsGroup.getGroupId(), _planContextFactory);
    plan.addMove(new DummyMove());
    _planDao.save(plan);

    final List<Cluster> clusters =
        List.of(clusterDescription).stream()
            .map(
                c ->
                    Cluster.getCluster(
                        c,
                        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), c.getName())))
            .collect(Collectors.toList());

    final GenerateAutomationConfigStep step = getAutomationConfigStep(plan, clusters);
    step.setMongotTemplate(step.getExistingAutomationConfig());

    // No mongot configs when there are no FTS indexes
    final List<ReplicaSetHardware> replicaSetHardware =
        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), clusterDescription.getName());

    final List<MongotConfig> mongotConfigsBeforeFTSIndex =
        step.getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0));
    assertEquals(0, mongotConfigsBeforeFTSIndex.size());

    // Add FTS Index
    final FTSIndex ftsIndex = getFTSIndex("theworld", "nyc", "travelaroundtheworld");
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(), clusterDescription.getName(), ftsIndex, null);

    // One mongot config per node when there are FTS indexes
    final List<MongotConfig> mongotConfigsAfterFTSIndex =
        step.getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0));
    assertEquals(3, mongotConfigsAfterFTSIndex.size());

    // Mongot configs initially have disabled = false, no JVM flags
    mongotConfigsAfterFTSIndex.stream()
        .forEach(mongotConfig -> assertFalse(mongotConfig.isDisabled()));

    // Update mongot process args such that disabled = true
    final MongotProcessArgs mongotProcessArgs1 = new MongotProcessArgs();
    mongotProcessArgs1.setFTSDisabled(true);

    _ndsMongotConfigSvc.updateMongotProcessArgs(
        _ndsGroup.getGroupId(),
        clusterDescription.getName(),
        null,
        new MongotProcessArgsView(mongotProcessArgs1),
        AuditInfoHelpers.fromSystem());

    // Mongot configs have disabled = true, default JVM flags
    final List<MongotConfig> mongotConfigsAfterDisablingMongot =
        step.getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0));
    assertEquals(3, mongotConfigsAfterDisablingMongot.size());

    mongotConfigsAfterDisablingMongot.stream()
        .forEach(mongotConfig -> assertTrue(mongotConfig.isDisabled()));
    mongotConfigsAfterDisablingMongot.stream()
        .forEach(mongotConfig -> assertEquals(4, mongotConfig.getJvmFlags().length));

    // Update mongot process args such that jvmXmsMB = 256
    final MongotProcessArgs mongotProcessArgs2 = new MongotProcessArgs();
    mongotProcessArgs2.setJvmXmsMB(256);

    _ndsMongotConfigSvc.updateMongotProcessArgs(
        _ndsGroup.getGroupId(),
        clusterDescription.getName(),
        null,
        new MongotProcessArgsView(mongotProcessArgs2),
        AuditInfoHelpers.fromSystem());

    // Mongot configs have disabled = true, JVM flags: ["Xms256m"]
    final List<MongotConfig> mongotConfigsAfterSettingJVMXmsMB =
        step.getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0));
    assertEquals(3, mongotConfigsAfterSettingJVMXmsMB.size());

    mongotConfigsAfterSettingJVMXmsMB.stream()
        .forEach(mongotConfig -> assertTrue(mongotConfig.isDisabled()));
    mongotConfigsAfterSettingJVMXmsMB.stream()
        .forEach(mongotConfig -> assertEquals(4, mongotConfig.getJvmFlags().length));
    mongotConfigsAfterSettingJVMXmsMB.stream()
        .forEach(mongotConfig -> assertEquals("-Xms256m", mongotConfig.getJvmFlags()[0]));
    mongotConfigsAfterSettingJVMXmsMB.stream()
        .forEach(mongotConfig -> assertEquals("-Xmx2g", mongotConfig.getJvmFlags()[1]));
    mongotConfigsAfterSettingJVMXmsMB.stream()
        .forEach(
            mongotConfig ->
                assertEquals(
                    "--enable-native-access=MONGOT_NON_EXISTING_MODULE",
                    mongotConfig.getJvmFlags()[2]));
    mongotConfigsAfterSettingJVMXmsMB.stream()
        .forEach(
            mongotConfig ->
                assertEquals("--add-modules=jdk.incubator.vector", mongotConfig.getJvmFlags()[3]));

    // Update mongot process args such that jvmXmxMB = 2048
    final MongotProcessArgs mongotProcessArgs3 = new MongotProcessArgs();
    mongotProcessArgs3.setJvmXmxMB(2048);

    _ndsMongotConfigSvc.updateMongotProcessArgs(
        _ndsGroup.getGroupId(),
        clusterDescription.getName(),
        null,
        new MongotProcessArgsView(mongotProcessArgs3),
        AuditInfoHelpers.fromSystem());

    // Mongot configs have disabled = true, JVM flags: ["Xms256m", "Xmx2048m"]
    final List<MongotConfig> mongotConfigsAfterSettingJVMXmxMB =
        step.getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0));
    assertEquals(3, mongotConfigsAfterSettingJVMXmxMB.size());

    mongotConfigsAfterSettingJVMXmxMB.stream()
        .forEach(mongotConfig -> assertTrue(mongotConfig.isDisabled()));
    mongotConfigsAfterSettingJVMXmxMB.stream()
        .forEach(mongotConfig -> assertEquals(4, mongotConfig.getJvmFlags().length));
    mongotConfigsAfterSettingJVMXmxMB.stream()
        .forEach(mongotConfig -> assertEquals("-Xms256m", mongotConfig.getJvmFlags()[0]));
    mongotConfigsAfterSettingJVMXmxMB.stream()
        .forEach(mongotConfig -> assertEquals("-Xmx2048m", mongotConfig.getJvmFlags()[1]));
    mongotConfigsAfterSettingJVMXmsMB.stream()
        .forEach(
            mongotConfig ->
                assertEquals(
                    "--enable-native-access=MONGOT_NON_EXISTING_MODULE",
                    mongotConfig.getJvmFlags()[2]));
    mongotConfigsAfterSettingJVMXmsMB.stream()
        .forEach(
            mongotConfig ->
                assertEquals("--add-modules=jdk.incubator.vector", mongotConfig.getJvmFlags()[3]));

    // Update mongot process args such that there are additional params
    final MongotProcessArgs mongotProcessArgs4 = new MongotProcessArgs();
    final Map<String, Object> additionalParams = new HashMap<>();
    additionalParams.put("logPath", "/some/path");
    mongotProcessArgs4.setAdditionalParams(additionalParams);

    _ndsMongotConfigSvc.updateMongotProcessArgs(
        _ndsGroup.getGroupId(),
        clusterDescription.getName(),
        null,
        new MongotProcessArgsView(mongotProcessArgs4),
        AuditInfoHelpers.fromSystem());

    // Mongot configs have additional params
    final List<MongotConfig> mongotConfigsAfterSettingAdditionalParams =
        step.getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0));
    assertEquals(3, mongotConfigsAfterSettingAdditionalParams.size());

    mongotConfigsAfterSettingAdditionalParams.forEach(
        mongotConfig -> {
          assertEquals("/some/path", mongotConfig.getAdditionalParams().get("logPath"));
          assertNull(
              MapUtils.getDeep(
                  mongotConfig.getAdditionalParams(), List.of("tracing", "sampler", "rate")));
        });

    // Mongot config ids are not incremented after the step as been performed
    assertTrue(step.performInternal().getStatus().isDone());
    final List<MongotConfig> mongotConfigsAfterStepPerformed1 =
        step.getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0));

    assertTrue(step.performInternal().getStatus().isDone());
    final List<MongotConfig> mongotConfigsAfterStepPerformed2 =
        step.getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0));

    final List<Integer> mongotConfigIdsBefore =
        mongotConfigsAfterStepPerformed1.stream()
            .map(mongotConfig -> mongotConfig.getId())
            .collect(Collectors.toList());
    final List<Integer> mongotConfigIdsAfter =
        mongotConfigsAfterStepPerformed2.stream()
            .map(mongotConfig -> mongotConfig.getId())
            .collect(Collectors.toList());
    assertEquals(mongotConfigIdsBefore, mongotConfigIdsAfter);

    // Mongot configs have querying and prometheus parameters added since mongot version 1.18.2.1085
    mongotConfigsAfterFTSIndex.forEach(
        mongotConfig -> {
          assertEquals(
              true,
              MapUtils.getDeep(
                  mongotConfig.getAdditionalParams(),
                  List.of("querying", "lucene", "disableMaxClauseLimit")));
          assertEquals(
              new HashMap<>(),
              MapUtils.getDeep(
                  mongotConfig.getAdditionalParams(), List.of("metrics", "prometheus")));
          // `enableConcurrentSearch` should only be present on dedicated nodes
          assertNull(
              MapUtils.getDeep(
                  mongotConfig.getAdditionalParams(),
                  List.of("querying", "lucene", "enableConcurrentSearch")));
          assertEquals(
              false,
              MapUtils.getDeep(
                  mongotConfig.getAdditionalParams(), List.of("lifecycle", "useLifecycleManager")));
          assertNull(
              MapUtils.getDeep(
                  mongotConfig.getAdditionalParams(),
                  MongotConfigSvc.INDEXING_LUCENE_DOCS_LIMIT_KEY));
        });

    // Mongot configs for MTM cluster should not have querying config
    final ClusterDescription mtmClusterDescription =
        getAWSClusterDescription(
                NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .copy()
            .setIsMTM(true)
            .build();
    final GenerateAutomationConfigStep stepForMtmCluster =
        getPreparedStepForCluster(mtmClusterDescription);
    stepForMtmCluster.setMongotTemplate(stepForMtmCluster.getExistingAutomationConfig());
    // Add FTS Index
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(), mtmClusterDescription.getName(), ftsIndex, null);
    final List<ReplicaSetHardware> mtmReplicaSetHardware =
        _replicaSetHardwareDao.findByCluster(
            _ndsGroup.getGroupId(), mtmClusterDescription.getName());
    FeatureFlagIntTestUtil.setFeatureFlagToControlled(
        FeatureFlag.ATLAS_SEARCH_USE_LIFECYCLE_MANAGER);
    FeatureFlagIntTestUtil.enableFeatureGlobally(FeatureFlag.ATLAS_SEARCH_STALE_INDEXES);
    final List<MongotConfig> mongotConfigsOnMtm =
        stepForMtmCluster.getDeploymentMongoTProcesses(
            mtmClusterDescription, mtmReplicaSetHardware.get(0));
    mongotConfigsOnMtm.forEach(
        mongotConfig -> {
          assertEquals(
              10_000_000L,
              MapUtils.getDeep(
                  mongotConfig.getAdditionalParams(),
                  MongotConfigSvc.TOKEN_FACETING_CARDINALITY_LIMIT));
          assertEquals(
              true,
              MapUtils.getDeep(
                  mongotConfig.getAdditionalParams(), List.of("lifecycle", "useLifecycleManager")));
          assertEquals(
              MongotConfigSvc.MAX_DOCS_LIMIT,
              MapUtils.getDeep(
                  mongotConfig.getAdditionalParams(),
                  MongotConfigSvc.INDEXING_LUCENE_DOCS_LIMIT_KEY));
        });

    // querying and prometheus parameters should exist for older mongot version now that logic for
    // old ones have been removed
    _ndsGroupDao.fixAgentVersion(
        _ndsGroup.getGroupId(), new FixedAgentVersion(AgentType.MONGOT, "1.17.1.1033"));
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
    final GenerateAutomationConfigStep stepWithOlderMongotVersion =
        getAutomationConfigStep(plan, clusters);
    stepWithOlderMongotVersion.setMongotTemplate(
        stepWithOlderMongotVersion.getExistingAutomationConfig());

    stepWithOlderMongotVersion
        .getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              assertEquals(
                  true,
                  MapUtils.getDeep(
                      mongotConfig.getAdditionalParams(),
                      List.of("querying", "lucene", "disableMaxClauseLimit")));
              assertEquals(
                  new HashMap<>(),
                  MapUtils.getDeep(
                      mongotConfig.getAdditionalParams(), List.of("metrics", "prometheus")));
            });

    // Create an M20 instance.
    final ClusterDescription m20WithOldMongotVersionClusterDescription =
        getAWSClusterDescription(
                NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M20))
            .setName("m20WithOldMongotVersion")
            .build();
    final GenerateAutomationConfigStep stepForM20ClusterWithOldMongotVersion =
        getPreparedStepForCluster(m20WithOldMongotVersionClusterDescription);
    stepForM20ClusterWithOldMongotVersion.setMongotTemplate(
        stepForM20ClusterWithOldMongotVersion.getExistingAutomationConfig());
    // Add FTS Index.
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(),
        m20WithOldMongotVersionClusterDescription.getName(),
        ftsIndex,
        null);
    final List<ReplicaSetHardware> m20WithOldMongotVersionReplicaSetHardware =
        _replicaSetHardwareDao.findByCluster(
            _ndsGroup.getGroupId(), m20WithOldMongotVersionClusterDescription.getName());

    // No longer have special logic for older mongot versions, so it will always have initialSync
    // config.
    stepForM20ClusterWithOldMongotVersion
        .getDeploymentMongoTProcesses(
            m20WithOldMongotVersionClusterDescription,
            m20WithOldMongotVersionReplicaSetHardware.get(0))
        .forEach(
            mongotConfig ->
                assertEquals(
                    new HashMap<>(),
                    MapUtils.getDeep(
                        mongotConfig.getAdditionalParams(),
                        List.of("replication", "initialSync", "bufferless"))));

    // M30 sharded cluster with the minimum mongot version should have a bufferless config.
    _ndsGroupDao.fixAgentVersion(
        _ndsGroup.getGroupId(), new FixedAgentVersion(AgentType.MONGOT, "1.33.1.1799"));
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
    final GenerateAutomationConfigStep stepWithBufferlessMongotVersion =
        getAutomationConfigStep(plan, clusters);
    stepWithBufferlessMongotVersion.setMongotTemplate(
        stepWithBufferlessMongotVersion.getExistingAutomationConfig());
    stepWithBufferlessMongotVersion
        .getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              assertNotNull(
                  MapUtils.getDeep(
                      mongotConfig.getAdditionalParams(),
                      List.of("replication", "initialSync", "bufferless")));
            });

    // Create a new M20 instance.
    final ClusterDescription m20ClusterDescription =
        getAWSClusterDescription(
                NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M20))
            .setName("m20Cluster")
            .build();
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
    final GenerateAutomationConfigStep stepForM20Cluster =
        getPreparedStepForCluster(m20ClusterDescription);
    stepForM20Cluster.setMongotTemplate(stepForM20Cluster.getExistingAutomationConfig());
    // Add FTS Index.
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(), m20ClusterDescription.getName(), ftsIndex, null);
    final List<ReplicaSetHardware> m20ReplicaSetHardware =
        _replicaSetHardwareDao.findByCluster(
            _ndsGroup.getGroupId(), m20ClusterDescription.getName());

    // An unsharded M20 cluster with the right mongot version should have a bufferless config.
    stepForM20Cluster
        .getDeploymentMongoTProcesses(m20ClusterDescription, m20ReplicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              assertNotNull(
                  MapUtils.getDeep(
                      mongotConfig.getAdditionalParams(),
                      List.of("replication", "initialSync", "bufferless")));
            });

    // Create a new M40 instance.
    final ClusterDescription m40ClusterDescription =
        getAWSClusterDescription(
                NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M40))
            .setName("m40Cluster")
            .build();
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
    final GenerateAutomationConfigStep stepForM40Cluster =
        getPreparedStepForCluster(m40ClusterDescription);
    stepForM40Cluster.setMongotTemplate(stepForM40Cluster.getExistingAutomationConfig());
    // Add FTS Index.
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(), m40ClusterDescription.getName(), ftsIndex, null);
    final List<ReplicaSetHardware> m40ReplicaSetHardware =
        _replicaSetHardwareDao.findByCluster(
            _ndsGroup.getGroupId(), m40ClusterDescription.getName());

    // An unsharded M40 cluster should have an initialSync config.
    stepForM40Cluster
        .getDeploymentMongoTProcesses(m40ClusterDescription, m40ReplicaSetHardware.get(0))
        .forEach(
            mongotConfig ->
                assertNotNull(
                    MapUtils.getDeep(
                        mongotConfig.getAdditionalParams(),
                        List.of("replication", "initialSync", "bufferless"))));

    // Should have vector merge policy set with newest mongot version
    stepForM40Cluster
        .getDeploymentMongoTProcesses(m40ClusterDescription, m40ReplicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              Map<String, Object> params = mongotConfig.getAdditionalParams();
              assertEquals(
                  40,
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "maxCompoundDataMb")));
              assertEquals(
                  409,
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "maxVectorInputMb")));
              assertEquals(
                  819,
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "mergeBudgetMb")));
              assertNull(
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "segmentHeapBudgetMb")));
              assertNull(
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "globalHeapBudgetMb")));
            });
    // System metrics should be enabled with newest mongot version.
    stepForM40Cluster
        .getDeploymentMongoTProcesses(m40ClusterDescription, m40ReplicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              assertNotNull(
                  MapUtils.getDeep(
                      mongotConfig.getAdditionalParams(), List.of("metrics", "system")));
            });

    // new segmentHeapBudgetMb and globalHeapBudgetMb parameters should appear for 1.48.0+
    stepForM40Cluster.getExistingAutomationConfig().getMongotTemplate().updateVersion("1.48.0");
    stepForM40Cluster
        .getDeploymentMongoTProcesses(m40ClusterDescription, m40ReplicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              Map<String, Object> params = mongotConfig.getAdditionalParams();
              assertEquals(
                  409, // 10% of 4GB
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "segmentHeapBudgetMb")));
              assertEquals(
                  2457, // 60% of 4GB
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "globalHeapBudgetMb")));
              // tokenFacetingCardinalityLimit should not be set for dedicated clusters
              assertNull(
                  MapUtils.getDeep(params, MongotConfigSvc.TOKEN_FACETING_CARDINALITY_LIMIT));
            });

    FeatureFlagIntTestUtil.setFeatureFlagToControlled(
        FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);

    // Test disk monitor parameters for colocated mongot with mongotune policy eligible cluster
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);
    _ndsGroupDao.fixAgentVersion(
        _ndsGroup.getGroupId(), new FixedAgentVersion(AgentType.MONGOT, "1.48.3"));
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();

    // Create a cluster eligible for mongotune disk write blocking policy
    final ClusterDescription mongotuneEligibleCluster =
        getAWSClusterDescription("8.0")
            .copy()
            .setName("mongotuneEligibleCluster")
            .setMongoDBVersion(VersionUtils.EIGHT_ONE_TWO.getVersion())
            .setMongoDBMajorVersion(VersionUtils.EIGHT_ONE_TWO.getMajorVersionString())
            .setVersionReleaseSystem(VersionReleaseSystem.CONTINUOUS)
            .setMongotuneStatus(
                MongotuneStatus.builder().version("1.0.1").policies(Map.of()).build())
            .build();

    final GenerateAutomationConfigStep stepForMongotuneEligibleCluster =
        getPreparedStepForCluster(mongotuneEligibleCluster);
    stepForMongotuneEligibleCluster.setMongotTemplate(
        stepForMongotuneEligibleCluster.getExistingAutomationConfig());

    // Add FTS Index
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(), mongotuneEligibleCluster.getName(), ftsIndex, null);
    final List<ReplicaSetHardware> mongotuneEligibleReplicaSetHardware =
        _replicaSetHardwareDao.findByCluster(
            _ndsGroup.getGroupId(), mongotuneEligibleCluster.getName());

    // Colocated mongot on mongotune eligible cluster should have disk monitor parameters
    stepForMongotuneEligibleCluster
        .getDeploymentMongoTProcesses(
            mongotuneEligibleCluster, mongotuneEligibleReplicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              Map<String, Object> params = mongotConfig.getAdditionalParams();
              assertEquals(
                  true,
                  MapUtils.getDeep(params, List.of("diskMonitor", "enableDiskBasedReplication")));
              assertEquals(
                  0.96,
                  MapUtils.getDeep(params, List.of("diskMonitor", "pauseReplicationThreshold")));
              assertEquals(
                  0.94,
                  MapUtils.getDeep(params, List.of("diskMonitor", "resumeReplicationThreshold")));
              assertEquals(
                  0.99, MapUtils.getDeep(params, List.of("diskMonitor", "crashThreshold")));
              // forceDisableReplication should not be set for colocated mongot
              assertNull(
                  MapUtils.getDeep(params, List.of("diskMonitor", "forceDisableReplication")));
            });

    // Test disk monitor parameters when mongotune feature flag is disabled
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, null, FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);

    stepForMongotuneEligibleCluster
        .getDeploymentMongoTProcesses(
            mongotuneEligibleCluster, mongotuneEligibleReplicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              Map<String, Object> params = mongotConfig.getAdditionalParams();
              // No disk monitor parameters should be set when mongotune feature flag is disabled
              assertNull(
                  MapUtils.getDeep(params, List.of("diskMonitor", "enableDiskBasedReplication")));
              assertNull(
                  MapUtils.getDeep(params, List.of("diskMonitor", "pauseReplicationThreshold")));
              assertNull(
                  MapUtils.getDeep(params, List.of("diskMonitor", "resumeReplicationThreshold")));
              assertNull(MapUtils.getDeep(params, List.of("diskMonitor", "crashThreshold")));
              assertNull(
                  MapUtils.getDeep(params, List.of("diskMonitor", "forceDisableReplication")));
            });

    // Test disk monitor parameters for cluster not eligible for mongotune policy (sharded cluster)
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);

    // Create a sharded cluster (not eligible for mongotune disk write blocking policy)
    final ClusterDescription shardedCluster =
        getGeoShardedClusterDescription(
                Collections.singletonList(replicationSpec),
                GeoShardingView.getDefaultGeoShardingView().toGeoSharding(),
                "shardedCluster")
            .copy()
            .setMongoDBVersion(VersionUtils.EIGHT_ZERO_TWELVE.getVersion())
            .setMongoDBMajorVersion(VersionUtils.EIGHT_ZERO_TWELVE.getMajorVersionString())
            .setMongotuneStatus(
                MongotuneStatus.builder().version("1.0.1").policies(Map.of()).build())
            .build();

    final GenerateAutomationConfigStep stepForShardedCluster =
        getPreparedStepForCluster(shardedCluster);
    stepForShardedCluster.setMongotTemplate(stepForShardedCluster.getExistingAutomationConfig());

    // Add FTS Index
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(), shardedCluster.getName(), ftsIndex, null);
    final List<ReplicaSetHardware> shardedReplicaSetHardware =
        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), shardedCluster.getName());

    // Colocated mongot on sharded cluster should not have disk monitor parameters
    stepForShardedCluster
        .getDeploymentMongoTProcesses(shardedCluster, shardedReplicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              Map<String, Object> params = mongotConfig.getAdditionalParams();
              // No disk monitor parameters should be set for sharded clusters (not eligible)
              assertNull(
                  MapUtils.getDeep(params, List.of("diskMonitor", "enableDiskBasedReplication")));
              assertNull(
                  MapUtils.getDeep(params, List.of("diskMonitor", "pauseReplicationThreshold")));
              assertNull(
                  MapUtils.getDeep(params, List.of("diskMonitor", "resumeReplicationThreshold")));
              assertNull(MapUtils.getDeep(params, List.of("diskMonitor", "crashThreshold")));
              assertNull(
                  MapUtils.getDeep(params, List.of("diskMonitor", "forceDisableReplication")));
            });
  }

  @Test
  public void testGetDeploymentMongoTProcesses_whenMaasFlagDisabled_shouldSkipMaasConfig()
      throws Exception {
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_SEARCH_ENABLE_MAAS_METRICS);

    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
    final GenerateAutomationConfigStep automationConfigStep =
        getPreparedStepForCluster(clusterDescription);
    automationConfigStep.setMongotTemplate(automationConfigStep.getExistingAutomationConfig());
    // Add FTS Index.
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(),
        clusterDescription.getName(),
        getFTSIndex("theworld", "nyc", "travelaroundtheworld"),
        null);
    final List<ReplicaSetHardware> replicaSetHardwareList =
        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), clusterDescription.getName());

    // mongot config should not have a Maas config
    replicaSetHardwareList.forEach(
        replicaSetHardware -> {
          automationConfigStep
              .getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware)
              .forEach(
                  mongotConfig ->
                      assertNull(
                          MapUtils.getDeep(
                              mongotConfig.getAdditionalParams(), List.of("metrics", "maas"))));
        });
  }

  @Test
  public void testGetDeploymentMongoTProcesses_whenMaasFlagEnabled_shouldSetMaasConfig()
      throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_SEARCH_ENABLE_MAAS_METRICS);

    final ClusterDescription clusterDescription =
        getAWSClusterDescription(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString());
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
    final GenerateAutomationConfigStep automationConfigStep =
        getPreparedStepForCluster(clusterDescription);
    automationConfigStep.setMongotTemplate(automationConfigStep.getExistingAutomationConfig());
    // Add FTS Index.
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(),
        clusterDescription.getName(),
        getFTSIndex("theworld", "nyc", "travelaroundtheworld"),
        null);
    final List<ReplicaSetHardware> replicaSetHardwareList =
        _replicaSetHardwareDao.findByCluster(_ndsGroup.getGroupId(), clusterDescription.getName());

    // Config from the conf-test.properties
    Map<String, Object> expectedMaasConfig =
        Map.of(
            "useCaseUuid",
            "49642286-8df9-5c7e-807b-40ad3e39fafd",
            "metricsIngestionUrl",
            "http://localhost:4318",
            "pushMetricsIntervalSec",
            60);

    // mongot config should have Maas config
    replicaSetHardwareList.forEach(
        replicaSetHardware -> {
          automationConfigStep
              .getDeploymentMongoTProcesses(clusterDescription, replicaSetHardware)
              .forEach(
                  mongotConfig ->
                      assertEquals(
                          expectedMaasConfig,
                          MapUtils.getDeep(
                              mongotConfig.getAdditionalParams(), List.of("metrics", "maas"))));
        });
  }

  @Test
  public void testMergingMongotAdditionalParametersInMongoTProcesses() throws Exception {
    // Create a new M40 instance.
    final ClusterDescription m40ClusterDescription =
        getAWSClusterDescription(
                NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M40))
            .setName("m40Cluster")
            .build();
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
    final GenerateAutomationConfigStep stepForM40Cluster =
        getPreparedStepForCluster(m40ClusterDescription);
    stepForM40Cluster.setMongotTemplate(stepForM40Cluster.getExistingAutomationConfig());
    // Add FTS Index.
    final FTSIndex ftsIndex = getFTSIndex("theworld", "nyc", "travelaroundtheworld");
    _ftsIndexConfigSvc.addFTSIndex(
        _ndsGroup.getGroupId(), m40ClusterDescription.getName(), ftsIndex, null);

    final List<ReplicaSetHardware> m40ReplicaSetHardware =
        _replicaSetHardwareDao.findByCluster(
            _ndsGroup.getGroupId(), m40ClusterDescription.getName());

    // default behaviour with no additional parameter overrides
    stepForM40Cluster
        .getDeploymentMongoTProcesses(m40ClusterDescription, m40ReplicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              final Map<String, Object> params = mongotConfig.getAdditionalParams();

              assertEquals(
                  true,
                  MapUtils.getDeep(params, List.of("querying", "lucene", "disableMaxClauseLimit")));
              assertEquals(
                  40,
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "maxCompoundDataMb")));
              assertEquals(
                  409,
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "maxVectorInputMb")));
              assertEquals(
                  819,
                  MapUtils.getDeep(
                      params,
                      List.of(
                          "indexing",
                          "lucene",
                          "mergePolicy",
                          "tiered",
                          "vectorMergePolicyConfig",
                          "mergeBudgetMb")));
              assertEquals("http://localhost:8060", params.get("searchGatewayBaseUrl"));
              assertEquals(
                  _appSettings.getMongotMmsConfigUpdatePeriodSeconds(),
                  params.get("mmsConfigUpdatePeriodSeconds"));
              assertNotNull(MapUtils.getDeep(params, List.of("metrics", "system")));
              assertNotNull(
                  MapUtils.getDeep(params, List.of("replication", "initialSync", "bufferless")));
            });

    // setting overrides for additional parameters
    final MongotProcessArgs mongotProcessArgs = new MongotProcessArgs();
    final Map<String, Object> additionalParams = new HashMap<>();
    additionalParams.put("logPath", "/some/path");
    additionalParams.put(
        "querying",
        Map.of(
            "lucene", Map.of("disableMaxClauseLimit", false))); // nested override to test merging

    mongotProcessArgs.setAdditionalParams(additionalParams);

    _ndsMongotConfigSvc.updateMongotProcessArgs(
        _ndsGroup.getGroupId(),
        m40ClusterDescription.getName(),
        null,
        new MongotProcessArgsView(mongotProcessArgs),
        AuditInfoHelpers.fromSystem());

    // to test merging of additional parameters
    stepForM40Cluster
        .getDeploymentMongoTProcesses(m40ClusterDescription, m40ReplicaSetHardware.get(0))
        .forEach(
            mongotConfig -> {
              final Map<String, Object> params = mongotConfig.getAdditionalParams();

              assertEquals("/some/path", params.get("logPath"));
              // overidden "merged" value set to false, default is true
              assertEquals(
                  false,
                  MapUtils.getDeep(params, List.of("querying", "lucene", "disableMaxClauseLimit")));
            });
  }

  @Test
  public void testGenerateCluster_ReplicaSet_redactClientLogData() throws Exception {
    // Cluster with redaction explicitly set to true
    {
      final ClusterDescription cluster =
          getAWSClusterDescription(VersionUtils.parse("7.0.0"), "cluster1")
              .copy()
              .setRedactClientLogData(true)
              .build();

      final GenerateAutomationConfigStep step = getPreparedStepForCluster(cluster);
      final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
      assertTrue(result.getStatus().isDone());
      result
          .getData()
          .getAutomationConfig()
          .getDeployment()
          .getProcesses()
          .forEach(
              process -> {
                assertTrue(process.getArgs2_6().getSecurity().getRedactClientLogData());
              });
    }

    // Cluster with redaction explicitly set to false
    {
      final ClusterDescription cluster =
          getAWSClusterDescription(VersionUtils.parse("7.0.0"), "cluster2")
              .copy()
              .setRedactClientLogData(false)
              .build();

      final GenerateAutomationConfigStep step = getPreparedStepForCluster(cluster);
      final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
      assertTrue(result.getStatus().isDone());
      result
          .getData()
          .getAutomationConfig()
          .getDeployment()
          .getProcesses()
          .forEach(
              process -> {
                assertFalse(process.getArgs2_6().getSecurity().getRedactClientLogData());
              });
    }

    // Cluster with redaction omitted
    {
      final ClusterDescription cluster =
          getAWSClusterDescription(VersionUtils.parse("7.0.0"), "cluster3")
              .copy()
              .setRedactClientLogData(null)
              .build();

      final GenerateAutomationConfigStep step = getPreparedStepForCluster(cluster);
      final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
      assertTrue(result.getStatus().isDone());
      result
          .getData()
          .getAutomationConfig()
          .getDeployment()
          .getProcesses()
          .forEach(
              process -> {
                assertTrue(
                    Optional.ofNullable(process)
                        .map(WithProcessArguments::getArgs2_6)
                        .map(ProcessArguments2_6::getSecurity)
                        .map(Security::getRedactClientLogData)
                        .isEmpty());
              });
    }
  }

  // This test also ensures the configuration is propagated to mongos processes.
  @Test
  public void testGenerateCluster_Sharded_redactClientLogData() throws Exception {
    // Cluster with redaction explicitly set to true
    {
      final ClusterDescription cluster =
          new ShardedClusterDescription(
                  NDSModelTestFactory.getAWSShardedClusterDescription(_ndsGroup.getGroupId()))
              .copy()
              .setName("cluster1")
              .setRedactClientLogData(true)
              .build();

      final GenerateAutomationConfigStep step = getPreparedStepForCluster(cluster);
      final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
      assertTrue(result.getStatus().isDone());
      result
          .getData()
          .getAutomationConfig()
          .getDeployment()
          .getProcesses()
          .forEach(
              process -> {
                assertTrue(
                    List.of(ProcessType.MONGOS, ProcessType.MONGOD)
                        .contains(process.getProcessType()));
                assertTrue(process.getArgs2_6().getSecurity().getRedactClientLogData());
              });
    }

    // Cluster with redaction explicitly set to false
    {
      final ClusterDescription cluster =
          new ShardedClusterDescription(
                  NDSModelTestFactory.getAWSShardedClusterDescription(_ndsGroup.getGroupId()))
              .copy()
              .setName("cluster2")
              .setRedactClientLogData(false)
              .build();

      final GenerateAutomationConfigStep step = getPreparedStepForCluster(cluster);
      final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
      assertTrue(result.getStatus().isDone());
      result
          .getData()
          .getAutomationConfig()
          .getDeployment()
          .getProcesses()
          .forEach(
              process -> {
                assertTrue(
                    List.of(ProcessType.MONGOS, ProcessType.MONGOD)
                        .contains(process.getProcessType()));
                assertFalse(process.getArgs2_6().getSecurity().getRedactClientLogData());
              });
    }

    // Cluster with redaction omitted
    {
      final ClusterDescription cluster =
          new ShardedClusterDescription(
                  NDSModelTestFactory.getAWSShardedClusterDescription(_ndsGroup.getGroupId()))
              .copy()
              .setName("cluster3")
              .setRedactClientLogData(null)
              .build();

      final GenerateAutomationConfigStep step = getPreparedStepForCluster(cluster);
      final Result<GenerateAutomationConfigBaseStep.Data> result = step.perform();
      assertTrue(result.getStatus().isDone());
      result
          .getData()
          .getAutomationConfig()
          .getDeployment()
          .getProcesses()
          .forEach(
              process -> {
                assertTrue(
                    List.of(ProcessType.MONGOS, ProcessType.MONGOD)
                        .contains(process.getProcessType()));
                assertTrue(
                    Optional.ofNullable(process)
                        .map(WithProcessArguments::getArgs2_6)
                        .map(ProcessArguments2_6::getSecurity)
                        .map(Security::getRedactClientLogData)
                        .isEmpty());
              });
    }
  }

  private void assertCommon(
      final ClusterDescription pClusterDescription, final AutomationConfig pResultConfig) {
    final String deploymentClusterName = pClusterDescription.getDeploymentClusterName();
    assertTrue(deploymentClusterName.matches("atlas-[a-zA-Z0-9]{6}"));

    final NDSLDAP ndsLDAP = _ndsGroup.getUserSecurity().getLDAP();
    assertNotNull(pResultConfig.getDeployment().getTLS().getCAFilePath());
    assertEquals(
        _phasedVersionDao
            .findBySoftwareType(SoftwareType.AUTOMATION_AGENT)
            .get()
            .getTargetVersion(),
        pResultConfig.getDeployment().getAgentVersion().getVersion());
    assertEquals(
        NDSDefaults.DEPRECATED_AGENTS_SENTINEL_VERSION,
        pResultConfig.getBackupAgentTemplate().getVersion());
    assertEquals(
        NDSDefaults.DEPRECATED_AGENTS_SENTINEL_VERSION,
        pResultConfig.getMonitoringAgentTemplate().getVersion());
    assertNotNull(pResultConfig.getBiConnectorTemplate());
    assertEquals(
        _appSettings.getAtlasBiConnectorVersion().getVersion(),
        pResultConfig.getBiConnectorTemplate().getVersion());

    assertNotNull(pResultConfig.getMongoDbToolsTemplate());
    assertEquals(
        _appSettings.getMongoDbToolsVersion(),
        pResultConfig.getMongoDbToolsTemplate().getVersion());

    for (final Process p :
        NDSDefaults.filterProcessesByClusterDescription(
            pResultConfig.getDeployment().getProcesses(), pClusterDescription)) {
      assertEquals(pClusterDescription.getMongoDBVersion().getVersion() + "-ent", p.getVersion());
      if (p.getFeatureCompatibilityVersion() != null) {
        assertEquals(
            pClusterDescription.getMongoDBMajorVersion(), p.getFeatureCompatibilityVersion());
      }
      assertTrue(p.getName().startsWith(deploymentClusterName));
      assertEquals(5, p.getAuthSchemaVersion().intValue());
      if (p.getProcessType() == ProcessType.MONGOD) {
        assertTrue(p.getArgs2_6().getReplSetName().startsWith(deploymentClusterName));
        assertNotNull(p.getArgs2_6().getDbPath());
      }
      assertNotNull(p.getArgs2_6().getLogPath());
      assertNotNull(p.getArgs2_6().getTlsPEMKeyFile());
      assertEquals(TlsMode.REQUIRE_TLS, p.getArgs2_6().getTlsMode());
      assertNotNull(p.getArgs2_6().getPort());
      assertNull(p.getArgs2_6().getAuditLogDestination());
      assertNull(p.getArgs2_6().getAuditLogFormat());
      assertNull(p.getArgs2_6().getAuditLogPath());
      assertNull(p.getArgs2_6().getAuditLogFilter());
      assertNull(p.getLastKmipMasterKeyRotation());
    }

    final LDAP ldap = pResultConfig.getDeployment().getLDAP();
    assertNotNull(ldap);
    if (ndsLDAP.isAuthenticationEnabled()) {
      assertEquals(
          String.format("%s:%s", ndsLDAP.getHostname().get(), ndsLDAP.getPort().get()),
          ldap.getServers());
      assertEquals(false, ldap.getValidateLdapServerConfig());
    } else {
      assertNull(ldap.getServers());
      assertNull(ldap.getValidateLdapServerConfig());
    }
    assertEquals(ndsLDAP.getBindUsername().orElse(null), ldap.getBindQueryUser());
    assertEquals(ndsLDAP.getBindPassword().orElse(null), ldap.getBindQueryPassword());
    if (ndsLDAP.isAuthorizationEnabled()) {
      assertNotNull(ldap.getAuthzQueryTemplate());
    }

    final List<OidcIdentityProvider> connectedIdps =
        _federationAppIdentityProvidersSvc.getConnectedOidcIdpsByOrgId(_group.getOrgId());

    final List<OIDC> oidcConfig = pResultConfig.getDeployment().getOidcProviderConfigs();

    // Assert config is in sorted order
    if (oidcConfig != null) {
      final List<OIDC> oidcConfigSorted =
          pResultConfig.getDeployment().getOidcProviderConfigs().stream().sorted().toList();
      assertEquals(oidcConfigSorted, oidcConfig);
    }

    // Determine this by looking at all clusters in the group
    final boolean groupSupportsOidcWorkload =
        _clusterDescriptionDao.findByGroup(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::getMongoDBVersion)
            .allMatch(VersionUtils::safeToConfigureOidcWorkload);

    final boolean hardcodedOidcIdpEnabled =
        _featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.ATLAS_PROVIDE_HARDCODED_OIDC_IDP_INFORMATION, null, _group);
    if (hardcodedOidcIdpEnabled) {
      if (groupSupportsOidcWorkload) {
        assertEquals(2, oidcConfig.size());
        assertOidcSettingsMatch(
            oidcConfig,
            List.of(
                NDSTestOIDCIdp.getTestWorkforceOIDCIdp(), NDSTestOIDCIdp.getTestWorkloadOIDCIdp()),
            true);
      } else {
        assertEquals(1, oidcConfig.size());
        assertOidcSettingsMatch(
            oidcConfig, List.of(NDSTestOIDCIdp.getTestWorkforceOIDCIdp()), false);
      }
    } else if (connectedIdps.isEmpty()) {
      assertNull(oidcConfig);
    } else {
      assertOidcSettingsMatch(oidcConfig, connectedIdps, groupSupportsOidcWorkload);
    }
  }

  private static void assertOidcSettingsMatch(
      final List<OIDC> pOIDCs,
      final List<OidcIdentityProvider> pIdPs,
      boolean pWorkloadIdpSupported) {
    assertNotNull(pOIDCs);
    assertNotNull(pIdPs);

    final List<OidcIdentityProvider> usedIdps;
    if (pWorkloadIdpSupported) {
      usedIdps = pIdPs;
    } else {
      // Remove workload IdPs and user claim IdPs from list of IdPs sent to the server
      usedIdps =
          pIdPs.stream()
              .filter(
                  idp -> !Objects.equals(idp.getIdpType(), OidcIdentityProvider.IdpType.WORKLOAD))
              .filter(
                  idp ->
                      !Objects.equals(
                          idp.getAuthorizationType(), OidcIdentityProvider.AuthorizationType.USER))
              .collect(Collectors.toList());
    }
    assertEquals(pOIDCs.size(), usedIdps.size());

    // Sort by id to ensure we compare them equally
    final List<OIDC> sortedOidc =
        pOIDCs.stream().sorted(Comparator.comparing(OIDC::getAuthNamePrefix)).toList();
    final List<OidcIdentityProvider> sortedIdPs =
        pIdPs.stream().sorted(Comparator.comparing(OidcIdentityProvider::getId)).toList();

    for (int i = 0; i < pOIDCs.size(); i++) {
      assertOidcSettingsMatch(sortedOidc.get(i), sortedIdPs.get(i), pWorkloadIdpSupported);
    }
  }

  private static void assertOidcSettingsMatch(
      final OIDC pOIDC, final OidcIdentityProvider pIdP, final boolean pWorkloadIdpSupported) {
    assertEquals(pOIDC.getAudience(), pIdP.getAudienceClaim().get(0));

    // Normalize the URI by prepending https:// if the URI does not start with it
    if (pIdP.getIssuerUri().startsWith("https://")) {
      assertEquals(pOIDC.getIssuerURI(), pIdP.getIssuerUri());
    } else {
      assertEquals(pOIDC.getIssuerURI(), "https://" + pIdP.getIssuerUri());
    }

    assertEquals(pOIDC.getClientId(), pIdP.getClientId());
    assertEquals(
        Arrays.stream(pOIDC.getRequestedScopes()).collect(Collectors.toList()),
        pIdP.getRequestedScopes());
    assertEquals(pOIDC.getUserClaim(), pIdP.getUserClaim());
    assertEquals(pOIDC.getGroupsClaim(), pIdP.getGroupsClaim());
    assertEquals(
        Arrays.stream(pOIDC.getAssociatedDomains()).collect(Collectors.toSet()),
        pIdP.getAssociatedDomains());
    if (pOIDC.getGroupsClaim() != null && !pOIDC.getGroupsClaim().isBlank()) {
      assertEquals(
          Arrays.stream(pOIDC.getLogClaims()).collect(Collectors.toList()),
          List.of("aud", pIdP.getGroupsClaim(), pIdP.getUserClaim()));
    } else {
      assertEquals(
          Arrays.stream(pOIDC.getLogClaims()).collect(Collectors.toList()),
          List.of("aud", pIdP.getUserClaim()));
    }
    assertEquals(TimeUtils.SECONDS_PER_DAY, pOIDC.getJwksPollSecs());
    assertEquals(pIdP.getId().toString(), pOIDC.getAuthNamePrefix());

    if (pWorkloadIdpSupported) {
      if (Objects.equals(
          pIdP.getAuthorizationType(), OidcIdentityProvider.AuthorizationType.USER)) {
        assertFalse(pOIDC.getUseAuthorizationClaim());
      } else {
        assertTrue(pOIDC.getUseAuthorizationClaim() == null || pOIDC.getUseAuthorizationClaim());
      }
      if (Objects.equals(pIdP.getIdpType(), IdentityProvider.IdpType.WORKLOAD)) {
        assertFalse(pOIDC.getSupportsHumanFlows());
      } else {
        assertTrue(pOIDC.getSupportsHumanFlows() == null || pOIDC.getSupportsHumanFlows());
      }
    } else {
      // These field should be null when workload Idp is not supported
      assertNull(pOIDC.getUseAuthorizationClaim());
      assertNull(pOIDC.getSupportsHumanFlows());
    }
  }

  private static FTSSearchIndex getFTSIndex(
      final String pDatabase, final String pCollection, final String pIndexName) {
    return new FTSSearchIndex.Builder()
        .setIndexId(new ObjectId())
        .setName(pIndexName)
        .setDatabase(pDatabase)
        .setLastObservedCollectionName(pCollection)
        .setMappings(new BasicDBObject("dynamic", true))
        .build();
  }

  private AppUser getAppUser() {
    final AppUser appUser = new AppUser();
    appUser.setId(new ObjectId());
    appUser.setCreated(new Date());
    appUser.setFirstName("System");
    appUser.setLastName("System");
    appUser.setUsername("System");
    appUser.setLastAuthAddr("Internal");
    return appUser;
  }

  private ShardedClusterDescription setUpClusterDescription(
      final boolean pSetNeedsMongoDBConfigPublishRestartAllowed,
      final Version pVersion,
      final String pSetName)
      throws SvcException {
    final ReplicationSpec replicationSpec =
        CrossCloudReplicationSpecModelTestFactory.getDefaultCrossCloudReplicationSpec();

    ShardedClusterDescription.Builder builder =
        getGeoShardedClusterDescription(
                Collections.singletonList(replicationSpec),
                GeoShardingView.getDefaultGeoShardingView().toGeoSharding(),
                pSetName)
            .copy()
            .setMongoDBMajorVersion(pVersion.getMajorVersionString())
            .setMongoDBVersion(pVersion.getVersion());
    if (pSetNeedsMongoDBConfigPublishRestartAllowed) {
      builder.setNeedsMongoDBConfigPublishRestartAllowed(ProcessRestartAllowedState.NONE);
    }
    final ShardedClusterDescription clusterDescription = builder.build();

    _clusterSvc.createDefaultProcessArgs(
        _group, clusterDescription, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    forceHardwareToProvisioned(_ndsGroup, clusterDescription);

    if (pSetNeedsMongoDBConfigPublishRestartAllowed) {
      assertEquals(
          ClusterDescription.ProcessRestartAllowedState.NONE,
          clusterDescription.getNeedsMongoDBConfigPublishRestartAllowed());
    }

    return clusterDescription;
  }

  private GenerateAutomationConfigStep getAutomationConfigStep(
      final Plan plan, final List<Cluster> clusters) {
    return new GenerateAutomationConfigStep(
        _planContextFactory.create(_ndsGroup.getGroupId(), plan.getId()),
        new Step.State(
            plan.getId(), plan.getMoves().get(0).getId(), 0, plan.getPlanContext().getPlanDao()),
        clusters,
        clusters.get(0),
        Set.of(),
        Set.of(),
        _ndsGroup,
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId()),
        _customMongoDbBuildDao,
        _hostClusterSvc,
        _automatedBackupRestoreSvc,
        _backupRestoreJobSvc,
        _backupDeploymentSvc,
        _onlineArchiveDao,
        _clusterSvc,
        _mongotConfigSvc,
        _clusterDescriptionProcessArgsDao,
        _backupRestoreJobDao,
        _backupSnapshotDao,
        _ndsGroupDao,
        _ndsGroupMaintenanceSvc,
        _group,
        _parallelRestoreSvc,
        _connectionRuleWithHostnamesSvc.getClusterPerInstanceConnectionRulesForClusterInstances(
            _ndsGroupDao.find(_ndsGroup.getGroupId()).get(), clusters.get(0)),
        _restoreStepUtils,
        _searchDeploymentDescriptionSvc,
        _automationConfigDeploymentBuilderFactory,
        null,
        _dbCheckSvc,
        _atlasUISKeysSvc,
        _serverlessMtmPoolSvc,
        _federationAppIdentityProvidersSvc,
        _organizationDao,
        _searchInstanceSvc,
        _featureFlagSvc,
        _searchIndexConfigSvc,
        _lastAgentStatusSvc,
        _rollableValueStateSvc,
        _mongotunePolicyInitializationSvc,
        _ifrPlanningUtil);
  }

  private void setPhasedTargetVersion(
      final SoftwareType pSoftwareType, final String pPhasedTargetVersion) {
    final PhasedVersion phasedVersion =
        _phasedVersionSvc.findBySoftwareType(pSoftwareType).orElseThrow();

    final BasicDBObject doc = phasedVersion.toDBObject();
    doc.put(PhasedVersion.FieldDefs.TARGET_VERSION, pPhasedTargetVersion);
    final PhasedVersion updated = _phasedVersionFactory.createPhasedVersion(doc);
    _phasedVersionSvc.save(updated);
  }

  private void reloadNDSGroup() {
    _ndsGroup = _ndsGroupDao.find(_group.getId()).get();
  }
}
