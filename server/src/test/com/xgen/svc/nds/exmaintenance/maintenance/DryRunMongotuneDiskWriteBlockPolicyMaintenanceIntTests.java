package com.xgen.svc.nds.exmaintenance.maintenance;

import static com.xgen.svc.nds.exmaintenance.maintenance.DryRunMongotuneDiskWriteBlockPolicyMaintenance.MIN_MONGOTUNE_VERSION_SUPPORT_DRY_RUN;
import static org.assertj.core.api.Assertions.assertThat;

import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.exmaintenance._public.model.ClusterDescriptionMaintenanceItem;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyArgVersion;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyStatus;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs;
import com.xgen.cloud.nds.project._public.model.MongotuneProcessArgs;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus.State;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class DryRunMongotuneDiskWriteBlockPolicyMaintenanceIntTests extends JUnit5BaseSvcTest {

  private static final String DRY_RUN_PROCESS_ARG = "dry_run";

  @Inject private DryRunMongotuneDiskWriteBlockPolicyMaintenance maintenance;
  @Inject private ClusterDescriptionDao clusterDescriptionDao;
  @Inject private NDSGroupSvc ndsGroupSvc;
  @Inject private NDSClusterSvc clusterSvc;
  @Inject private FeatureFlagSvc featureFlagSvc;
  @Inject private GroupSvc groupSvc;

  private Group group;
  private ClusterDescription cluster1;
  private ClusterDescription cluster2;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    final Organization org = MmsFactory.createOrganizationWithNDSPlan();

    group = MmsFactory.createGroup(org, "group1");
    final Group group2 = MmsFactory.createGroup(org, "group2");
    final Group group3 = MmsFactory.createGroup(org, "group3");

    ndsGroupSvc.ensureGroup(group.getId());
    ndsGroupSvc.ensureGroup(group2.getId());
    ndsGroupSvc.ensureGroup(group3.getId());

    cluster1 =
        createClusterWithMongotuneVersion(
            group,
            "cluster1",
            DryRunMongotuneDiskWriteBlockPolicyMaintenance.MIN_MONGOTUNE_VERSION_SUPPORT_DRY_RUN);
    cluster2 = createClusterWithMongotuneVersion(group2, "cluster2", "1.0.0");
  }

  @Test
  public void test_commandLineFiltersClusterIds() throws Exception {
    maintenance.setCommandLineWithArgs("--includeClusterIds", cluster1.getUniqueId().toHexString());
    assertThat(
            clusterDescriptionDao.getDbCollection().find(maintenance.getQueryFilter()).stream()
                .map(dbObject -> dbObject.get(FieldDefs.UNIQUE_ID))
                .collect(Collectors.toSet()))
        .containsExactly(cluster1.getUniqueId());
  }

  @Test
  public void test_commandLineFiltersGroupIds() throws Exception {
    maintenance.setCommandLineWithArgs("--includeGroupIds", group.getId().toHexString());
    assertThat(
            clusterDescriptionDao.getDbCollection().find(maintenance.getQueryFilter()).stream()
                .map(dbObject -> dbObject.get(FieldDefs.UNIQUE_ID))
                .collect(Collectors.toSet()))
        .containsExactly(cluster1.getUniqueId());
  }

  @Test
  public void test_performMaintenance() throws Exception {
    // execute maintenance
    maintenance.setCommandLineWithArgs();
    maintenance.performMaintenance(new ClusterDescriptionMaintenanceItem(cluster1));

    // verify group will plan asap
    final NDSGroup updatedGroup = ndsGroupSvc.find(group.getId()).orElseThrow();
    assertThat(updatedGroup.getNextPlanningDate()).isBefore(Instant.now());

    // verify mongotune status was updated with disk write blocking
    ClusterDescription updatedCluster =
        clusterDescriptionDao.findByName(group.getId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneStatus> status = updatedCluster.getMongotuneStatus();
    assertThat(status).isPresent();
    assertThat(status.get().policies()).containsKey(PolicyType.DISK_WRITE_BLOCKING);

    // verify a UpdateClusterMaintainedMongotuneConfigMove is needed, not pacpcm
    assertThat(updatedCluster.getNeedsMongoDBConfigPublishAfter()).isEmpty();
    assertThat(updatedCluster.getNeedsMongotuneConfigPublishAfter()).isPresent();
    assertThat(updatedCluster.getNeedsMongotuneConfigPublishAfter().get()).isBefore(new Date());

    DiskWriteBlockPolicyStatus diskWriteStatus =
        (DiskWriteBlockPolicyStatus) status.get().policies().get(PolicyType.DISK_WRITE_BLOCKING);
    assertThat(diskWriteStatus.isEnabled()).isTrue();
    assertThat(diskWriteStatus.getArgVersion())
        .isEqualTo(DiskWriteBlockPolicyArgVersion.CURRENT_VERSION);

    // verify process args were updated with dry_run
    ClusterDescriptionProcessArgs processArgs =
        clusterSvc.getProcessArgs(cluster1.getGroupId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneProcessArgs> mongotuneArgs = processArgs.getMongotuneArg();
    assertThat(mongotuneArgs).isPresent();
    assertThat(mongotuneArgs.get().getWriteBlockOverrides()).containsEntry("dry_run", true);
  }

  @Test
  public void test_performMaintenanceRollback() throws Exception {
    FeatureFlagIntTestUtil.setFeatureFlagToControlled(
        FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);

    // first apply the maintenance
    maintenance.setCommandLineWithArgs();
    maintenance.performMaintenance(new ClusterDescriptionMaintenanceItem(cluster1));

    // verify it was applied successfully
    ClusterDescription updatedCluster =
        clusterDescriptionDao.findByName(cluster1.getGroupId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneStatus> status = updatedCluster.getMongotuneStatus();
    assertThat(status).isPresent();
    assertThat(status.get().policies()).containsKey(PolicyType.DISK_WRITE_BLOCKING);

    ClusterDescriptionProcessArgs processArgs =
        clusterSvc.getProcessArgs(cluster1.getGroupId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneProcessArgs> mongotuneArgs = processArgs.getMongotuneArg();
    assertThat(mongotuneArgs).isPresent();
    assertThat(mongotuneArgs.get().getWriteBlockOverrides()).containsEntry("dry_run", true);

    // enable the FF for the group
    featureFlagSvc.enableNonConfigServiceFeatureFlag(
        group, null, FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);

    assertThat(
            featureFlagSvc.isFeatureFlagEnabled(
                FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY, null, group))
        .isTrue();
    // now apply rollback
    maintenance.setCommandLineWithArgs("--rollback", "--disableFeatureFlagOnRollback");
    maintenance.performMaintenance(new ClusterDescriptionMaintenanceItem(updatedCluster));

    // verify rollback removed the disk write blocking and dry_run flag and feature flag
    ClusterDescription rolledBackCluster =
        clusterDescriptionDao.findByName(cluster1.getGroupId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneStatus> rolledBackStatus = rolledBackCluster.getMongotuneStatus();
    assertThat(rolledBackStatus).isPresent();
    assertThat(rolledBackStatus.get().policies()).doesNotContainKey(PolicyType.DISK_WRITE_BLOCKING);

    ClusterDescriptionProcessArgs rolledBackProcessArgs =
        clusterSvc.getProcessArgs(cluster1.getGroupId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneProcessArgs> rolledBackMongotuneArgs =
        rolledBackProcessArgs.getMongotuneArg();
    assertThat(rolledBackMongotuneArgs.orElseThrow().getWriteBlockOverrides())
        .doesNotContainKey(DRY_RUN_PROCESS_ARG);

    final Group updatedGroup = groupSvc.findById(group.getId());
    assertThat(
            featureFlagSvc.isFeatureFlagEnabled(
                FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY, null, updatedGroup))
        .isFalse();
  }

  @Test
  public void test_isMaintenanceNeeded() throws Exception {
    maintenance.setCommandLineWithArgs();
    maintenance.performMaintenance(new ClusterDescriptionMaintenanceItem(cluster1));

    // verify policy was set
    ClusterDescription updatedCluster =
        clusterDescriptionDao.findByName(group.getId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneStatus> status = updatedCluster.getMongotuneStatus();
    assertThat(status).isPresent();
    assertThat(status.get().policies()).containsKey(PolicyType.DISK_WRITE_BLOCKING);

    // verify isMaintenanceNeeded returns false when policy is already set
    boolean isNeeded =
        maintenance.isMaintenanceNeeded(new ClusterDescriptionMaintenanceItem(updatedCluster));
    assertThat(isNeeded).isFalse();

    // verify isMaintenanceNeeded returns false for a cluster without the policy
    boolean isNeededForCluster2 =
        maintenance.isMaintenanceNeeded(new ClusterDescriptionMaintenanceItem(cluster2));
    assertThat(isNeededForCluster2).isFalse();

    // verify isMaintenanceNeeded returns true when in rollback mode
    maintenance.setCommandLineWithArgs("--rollback");
    boolean isNeededForRollback =
        maintenance.isMaintenanceNeeded(new ClusterDescriptionMaintenanceItem(updatedCluster));
    assertThat(isNeededForRollback).isTrue();

    // verify isMaintenanceNeeded returns false when in rollback mode since mongotune version
    // doesn't support dry-run
    maintenance.setCommandLineWithArgs("--rollback");
    isNeededForRollback =
        maintenance.isMaintenanceNeeded(new ClusterDescriptionMaintenanceItem(cluster2));
    assertThat(isNeededForRollback).isFalse();
  }

  @Test
  public void test_isMaintenanceNeeded_skipsClusterWithoutMongotuneStatus() throws Exception {
    // create cluster without mongotune status
    ClusterDescription clusterWithoutMongotune =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName("clusterWithoutMongotune")
            .setGroupId(group.getId())
            .build();
    clusterDescriptionDao.save(clusterWithoutMongotune);

    maintenance.setCommandLineWithArgs();
    boolean isNeeded =
        maintenance.isMaintenanceNeeded(
            new ClusterDescriptionMaintenanceItem(clusterWithoutMongotune));
    assertThat(isNeeded).isFalse();
  }

  @Test
  public void test_isMaintenanceNeeded_skipsClusterWithUnsupportedMongotuneVersion()
      throws Exception {
    // create cluster with mongotune version < 1.0.2
    ClusterDescription clusterWithOldVersion =
        createClusterWithMongotuneVersion(group, "clusterOldVersion", "1.0.1");

    maintenance.setCommandLineWithArgs();
    boolean isNeeded =
        maintenance.isMaintenanceNeeded(
            new ClusterDescriptionMaintenanceItem(clusterWithOldVersion));
    assertThat(isNeeded).isFalse();
  }

  @Test
  public void test_isMaintenanceNeeded_allowsClusterWithSupportedMongotuneVersion()
      throws Exception {
    // create cluster with mongotune version >= MIN_MONGOTUNE_VERSION_SUPPORT_DRY_RUN
    ClusterDescription clusterWithNewVersion =
        createClusterWithMongotuneVersion(
            group, "clusterNewVersion", MIN_MONGOTUNE_VERSION_SUPPORT_DRY_RUN);

    maintenance.setCommandLineWithArgs();
    boolean isNeeded =
        maintenance.isMaintenanceNeeded(
            new ClusterDescriptionMaintenanceItem(clusterWithNewVersion));
    assertThat(isNeeded).isTrue();
  }

  @Test
  public void test_isMaintenanceNeeded_allowsClusterWithNewerMongotuneVersion() throws Exception {
    // create cluster with mongotune version > MIN_MONGOTUNE_VERSION_SUPPORT_DRY_RUN
    ClusterDescription clusterWithNewerVersion =
        createClusterWithMongotuneVersion(group, "clusterNewerVersion", "1.1.0");

    maintenance.setCommandLineWithArgs();
    boolean isNeeded =
        maintenance.isMaintenanceNeeded(
            new ClusterDescriptionMaintenanceItem(clusterWithNewerVersion));
    assertThat(isNeeded).isTrue();
  }

  @Test
  public void test_isMaintenanceNeeded_rollbackAlsoChecksVersion() throws Exception {
    // create cluster with old mongotune version
    ClusterDescription clusterWithOldVersion =
        createClusterWithMongotuneVersion(group, "clusterOldVersionRollback", "1.0.1");

    // in rollback mode, version check is still applied (both dry-run and rollback are blocked for
    // unsupported versions)
    maintenance.setCommandLineWithArgs("--rollback");
    boolean isNeeded =
        maintenance.isMaintenanceNeeded(
            new ClusterDescriptionMaintenanceItem(clusterWithOldVersion));
    assertThat(isNeeded).isFalse();
  }

  private ClusterDescription createClusterWithMongotuneVersion(
      final Group group, final String name, final String version) throws SvcException {
    ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName(name)
            .setGroupId(group.getId())
            .build();

    clusterSvc.createDefaultProcessArgs(
        group, clusterDescription, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    clusterDescriptionDao.save(clusterDescription);
    clusterDescriptionDao.updateMongotuneStatus(
        group.getId(), name, version, State.ENABLE.name(), Map.of());

    return clusterDescriptionDao.findByName(group.getId(), name).orElseThrow();
  }

  @Test
  public void test_performMaintenance_skipsClusterWithExistingPolicy() throws Exception {
    // first apply the maintenance to set the policy
    maintenance.setCommandLineWithArgs();
    maintenance.performMaintenance(new ClusterDescriptionMaintenanceItem(cluster1));

    // verify policy was set
    ClusterDescription updatedCluster =
        clusterDescriptionDao.findByName(group.getId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneStatus> status = updatedCluster.getMongotuneStatus();
    assertThat(status).isPresent();
    assertThat(status.get().policies()).containsKey(PolicyType.DISK_WRITE_BLOCKING);

    // try to apply maintenance again - should be skipped
    boolean isNeeded =
        maintenance.isMaintenanceNeeded(new ClusterDescriptionMaintenanceItem(updatedCluster));
    assertThat(isNeeded).isFalse();
  }

  @Test
  public void test_performMaintenance_skipsClusterWithUnsupportedVersion() throws Exception {
    // create cluster with old mongotune version
    ClusterDescription clusterWithOldVersion =
        createClusterWithMongotuneVersion(group, "clusterOldVersionTest", "0.9.0");

    maintenance.setCommandLineWithArgs();
    boolean isNeeded =
        maintenance.isMaintenanceNeeded(
            new ClusterDescriptionMaintenanceItem(clusterWithOldVersion));
    assertThat(isNeeded).isFalse();
  }

  @Test
  public void test_rollback_withoutFeatureFlagDisable() throws Exception {
    // first apply the maintenance
    maintenance.setCommandLineWithArgs();
    maintenance.performMaintenance(new ClusterDescriptionMaintenanceItem(cluster1));

    // enable the feature flag
    featureFlagSvc.enableNonConfigServiceFeatureFlag(
        group, null, FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);

    // rollback without disabling feature flag
    maintenance.setCommandLineWithArgs("--rollback");
    maintenance.performMaintenance(new ClusterDescriptionMaintenanceItem(cluster1));

    // verify rollback removed the policy and process args but left feature flag enabled
    ClusterDescription rolledBackCluster =
        clusterDescriptionDao.findByName(cluster1.getGroupId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneStatus> rolledBackStatus = rolledBackCluster.getMongotuneStatus();
    assertThat(rolledBackStatus).isPresent();
    assertThat(rolledBackStatus.get().policies()).doesNotContainKey(PolicyType.DISK_WRITE_BLOCKING);

    ClusterDescriptionProcessArgs rolledBackProcessArgs =
        clusterSvc.getProcessArgs(cluster1.getGroupId(), cluster1.getName()).orElseThrow();
    Optional<MongotuneProcessArgs> rolledBackMongotuneArgs =
        rolledBackProcessArgs.getMongotuneArg();
    assertThat(rolledBackMongotuneArgs.orElseThrow().getWriteBlockOverrides())
        .doesNotContainKey(DRY_RUN_PROCESS_ARG);

    // feature flag should still be enabled
    final Group updatedGroup = groupSvc.findById(group.getId());
    assertThat(
            featureFlagSvc.isFeatureFlagEnabled(
                FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY, null, updatedGroup))
        .isTrue();
  }

  @Test
  public void test_commandLineArgs_multipleClusterIds() throws Exception {
    maintenance.setCommandLineWithArgs(
        "--includeClusterIds",
        cluster1.getUniqueId().toHexString() + "," + cluster2.getUniqueId().toHexString());

    assertThat(
            clusterDescriptionDao.getDbCollection().find(maintenance.getQueryFilter()).stream()
                .map(dbObject -> dbObject.get(FieldDefs.UNIQUE_ID))
                .collect(Collectors.toSet()))
        .containsExactlyInAnyOrder(cluster1.getUniqueId(), cluster2.getUniqueId());
  }

  @Test
  public void test_commandLineArgs_multipleGroupIds() throws Exception {
    final Group group2 =
        MmsFactory.createGroup(MmsFactory.createOrganizationWithNDSPlan(), "group2");
    ndsGroupSvc.ensureGroup(group2.getId());
    final ClusterDescription cluster3 =
        createClusterWithMongotuneVersion(
            group2,
            "cluster3",
            DryRunMongotuneDiskWriteBlockPolicyMaintenance.MIN_MONGOTUNE_VERSION_SUPPORT_DRY_RUN);

    maintenance.setCommandLineWithArgs(
        "--includeGroupIds", group.getId().toHexString() + "," + group2.getId().toHexString());

    assertThat(
            clusterDescriptionDao.getDbCollection().find(maintenance.getQueryFilter()).stream()
                .map(dbObject -> dbObject.get(FieldDefs.UNIQUE_ID))
                .collect(Collectors.toSet()))
        .containsExactlyInAnyOrder(cluster1.getUniqueId(), cluster3.getUniqueId());
  }

  @Test
  public void test_commandLineArgs_rollbackFlag() throws Exception {
    maintenance.setCommandLineWithArgs("--rollback");

    // rollback mode should allow maintenance on any cluster
    boolean isNeeded =
        maintenance.isMaintenanceNeeded(new ClusterDescriptionMaintenanceItem(cluster1));
    assertThat(isNeeded).isTrue();
  }

  @Test
  public void test_commandLineArgs_disableFeatureFlagOnRollback() throws Exception {
    FeatureFlagIntTestUtil.setFeatureFlagToControlled(
        FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);
    // first apply maintenance and enable feature flag
    maintenance.setCommandLineWithArgs();
    maintenance.performMaintenance(new ClusterDescriptionMaintenanceItem(cluster1));

    featureFlagSvc.enableNonConfigServiceFeatureFlag(
        group, null, FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);

    // rollback with feature flag disable
    maintenance.setCommandLineWithArgs("--rollback", "--disableFeatureFlagOnRollback");
    maintenance.performMaintenance(new ClusterDescriptionMaintenanceItem(cluster1));

    // verify feature flag was disabled
    final Group updatedGroup = groupSvc.findById(group.getId());
    assertThat(
            featureFlagSvc.isFeatureFlagEnabled(
                FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY, null, updatedGroup))
        .isFalse();
  }

  @Test
  public void test_queryFilter_excludesDeletedClusters() throws Exception {
    // create a deleted cluster
    ClusterDescription deletedCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName("deletedCluster")
            .setGroupId(group.getId())
            .setState(ClusterDescription.State.DELETED)
            .build();
    clusterDescriptionDao.save(deletedCluster);

    maintenance.setCommandLineWithArgs();

    // query should not include deleted clusters
    assertThat(
            clusterDescriptionDao.getDbCollection().find(maintenance.getQueryFilter()).stream()
                .map(dbObject -> dbObject.get(FieldDefs.UNIQUE_ID))
                .collect(Collectors.toSet()))
        .doesNotContain(deletedCluster.getUniqueId());
  }

  @Test
  public void test_queryFilter_includesOnlySupportedCloudProviders() throws Exception {
    maintenance.setCommandLineWithArgs();

    // verify query filter includes AWS, GCP, AZURE cloud providers
    // This is tested implicitly by the existing clusters being AWS-based
    // and being included in the query results
    assertThat(
            clusterDescriptionDao.getDbCollection().find(maintenance.getQueryFilter()).stream()
                .map(dbObject -> dbObject.get(FieldDefs.UNIQUE_ID))
                .collect(Collectors.toSet()))
        .contains(cluster1.getUniqueId(), cluster2.getUniqueId());
  }

  @Test
  public void test_versionBoundaryConditions() throws Exception {
    // test exact boundary version (should be allowed)
    ClusterDescription clusterExactVersion =
        createClusterWithMongotuneVersion(
            group, "clusterExact", MIN_MONGOTUNE_VERSION_SUPPORT_DRY_RUN);
    maintenance.setCommandLineWithArgs();
    boolean isNeededExact =
        maintenance.isMaintenanceNeeded(new ClusterDescriptionMaintenanceItem(clusterExactVersion));
    assertThat(isNeededExact).isTrue();

    // test just below boundary version 1.0.1 (should be skipped)
    ClusterDescription clusterBelowVersion =
        createClusterWithMongotuneVersion(group, "clusterBelow", "1.0.1");
    boolean isNeededBelow =
        maintenance.isMaintenanceNeeded(new ClusterDescriptionMaintenanceItem(clusterBelowVersion));
    assertThat(isNeededBelow).isFalse();

    // test well above boundary version 2.0.0 (should be allowed)
    ClusterDescription clusterAboveVersion =
        createClusterWithMongotuneVersion(group, "clusterAbove", "2.0.0");
    boolean isNeededAbove =
        maintenance.isMaintenanceNeeded(new ClusterDescriptionMaintenanceItem(clusterAboveVersion));
    assertThat(isNeededAbove).isTrue();
  }

  @Test
  public void test_maintenanceFlags() throws Exception {
    // test eligibleForElevatedHealthMonitoring
    assertThat(maintenance.eligibleForElevatedHealthMonitoring()).isTrue();

    // test requiresSetPlanASAP
    assertThat(maintenance.requiresSetPlanASAP()).isTrue();
  }
}
