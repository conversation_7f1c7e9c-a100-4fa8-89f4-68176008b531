package com.xgen.svc.nds.svc.adl;

import com.google.protobuf.Duration;
import com.google.protobuf.Timestamp;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataLakeTenantDao;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeStorage;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.mhouse.backend.grpc.Models.GetJobErrorsResponse;
import com.xgen.mhouse.backend.grpc.Models.GetJobProgressResponse;
import com.xgen.mhouse.backend.grpc.Models.TaskError;
import com.xgen.mhouse.services.billinglimits.v1.Models.DataScanningLimitStatus;
import com.xgen.mhouse.services.billinglimits.v1.Models.UsageLimit;
import com.xgen.svc.nds.model.onlinearchiveagentview.GetJobErrorsResponseView;
import com.xgen.svc.nds.model.onlinearchiveagentview.GetJobProgressResponseView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeMetricsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLScheduledUpdateView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStoreProviderView.ProviderValues;
import com.xgen.svc.nds.svc.NDSDataLakeTenantSvc;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.DataSource;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils.StorageConfigFieldDefs.Store;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveDataLakeConfigSvc;
import jakarta.inject.Inject;
import java.io.IOException;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DataLakeTestUtils {
  private static final Logger LOG = LoggerFactory.getLogger(DataLakeTestUtils.class);
  private MockDataLakeGrpcServer _awsGrpcServer;
  private MockDataLakeGrpcServer _azureGrpcServer;
  private MockDataLakeGrpcServer _gcpGrpcServer;
  private final NDSDataLakeTenantSvc _tenantSvc;
  private final NDSDataLakeTenantDao _tenantDao;
  private final AppSettings _settings;
  private final DataLakeAdminApiClient _dataLakeAdminApiClient;

  @Inject
  public DataLakeTestUtils(
      final NDSDataLakeTenantSvc pTenantSvc,
      final NDSDataLakeTenantDao pTenantDao,
      final AppSettings pAppSettings,
      final DataLakeAdminApiClient pDataLakeAdminApiClient) {
    _tenantSvc = pTenantSvc;
    _tenantDao = pTenantDao;
    _settings = pAppSettings;
    _dataLakeAdminApiClient = pDataLakeAdminApiClient;
  }

  public void setUp() throws IOException {
    _awsGrpcServer = new MockDataLakeGrpcServer(0);
    _azureGrpcServer = new MockDataLakeGrpcServer(0);
    _gcpGrpcServer = new MockDataLakeGrpcServer(0);
    _awsGrpcServer.start();
    updateAdminApiUrlForClient(CloudProvider.AWS);
    _azureGrpcServer.start();
    updateAdminApiUrlForClient(CloudProvider.AZURE);
    _gcpGrpcServer.start();
    updateAdminApiUrlForClient(CloudProvider.GCP);
    // update client used in test runner
    _dataLakeAdminApiClient.setGrpcClientsInTestEnv(_settings);
    // update client used in mms server
    final DataLakeAdminApiClient dataLakeAdminApiClientInMMS =
        AppConfig.getInstance(DataLakeAdminApiClient.class);
    dataLakeAdminApiClientInMMS.setGrpcClientsInTestEnv(_settings);
  }

  private void updateAdminApiUrlForClient(final CloudProvider pCloudProvider) {
    final MockDataLakeGrpcServer server = getGrpcServer(pCloudProvider);
    final int port = server.getServer().getPort();
    final String adminApiUrlProp = _settings.getDataLakeAdminApiUrlProp(pCloudProvider.name());
    _settings.setProp(adminApiUrlProp, "127.0.0.1:" + port, SettingType.MEMORY);
    LOG.info(
        "{} adl grpc client: override {} to 127.0.0.1:{}", pCloudProvider, adminApiUrlProp, port);
  }

  public void teardown() {
    _awsGrpcServer.stop();
    _azureGrpcServer.stop();
    _gcpGrpcServer.stop();
    _dataLakeAdminApiClient.close();
    final DataLakeAdminApiClient dataLakeAdminApiClientInMMS =
        AppConfig.getInstance(DataLakeAdminApiClient.class);
    dataLakeAdminApiClientInMMS.close();
  }

  private MockDataLakeGrpcServer getGrpcServer(final CloudProvider pCloudProvider) {
    if (pCloudProvider == CloudProvider.AWS) {
      return _awsGrpcServer;
    } else if (pCloudProvider == CloudProvider.AZURE) {
      return _azureGrpcServer;
    } else if (pCloudProvider == CloudProvider.GCP) {
      return _gcpGrpcServer;
    } else {
      throw new IllegalArgumentException("unsupported cloud provider: " + pCloudProvider);
    }
  }

  public void saveTenant(final NDSDataLakeTenant pTenant) throws DataLakeAdminApiException {
    saveTenant(pTenant, null);
  }

  public void saveTenant(final NDSDataLakeTenant pTenant, final NDSDataLakeStorageV1View pStorage)
      throws DataLakeAdminApiException {
    _tenantDao.saveTenant(pTenant);

    final NDSDataLakeStorageV1View storage =
        Optional.ofNullable(pStorage).orElseGet(NDSDataLakeStorageV1View::empty);

    _tenantSvc.updateStorageConfig(pTenant, storage, AuditInfoHelpers.fromSystem());
  }

  public void setAdminApiStorageV0(final ObjectId pTenantId, final NDSDataLakeStorage pStorage)
      throws DataLakeAdminApiException {
    _awsGrpcServer.setStorage(
        pTenantId.toHexString(), DataLakeAdminApiClient.toADLStorage(pStorage));
  }

  public void setAdminApiStorage(final ObjectId pTenantId, final NDSDataLakeStorageV1View pStorage)
      throws DataLakeAdminApiException {
    _awsGrpcServer.setStorage(
        pTenantId.toHexString(), DataLakeAdminApiClient.toADLStorageV1(pStorage));
  }

  public void setAdminApiStorageValidationErrors(
      final CloudProvider pCloudProvider, final ObjectId pTenantId, final List<String> pErrors) {
    getGrpcServer(pCloudProvider).setStorageValidationErrors(pTenantId.toHexString(), pErrors);
  }

  public String getLastRequestId() {
    return _awsGrpcServer.getLastRequestId();
  }

  public NDSDataLakeStorageV1View getAdminApiStorage(final NDSDataLakeTenant pTenant)
      throws DataLakeAdminApiException {
    return _tenantSvc.getStorageConfig(pTenant);
  }

  public Optional<NDSDataLakeTenant> getTenant(final ObjectId pGroupId, final String pName) {
    return _tenantDao.findByGroupIdAndName(pGroupId, pName);
  }

  public void deleteTenant(final ObjectId pTenantId) {
    _tenantDao.setTenantDeleted(pTenantId);
    _awsGrpcServer.deleteStorage(pTenantId.toHexString());
  }

  public void setJobProgress(
      final String pJobId, final GetJobProgressResponseView pJobProgressView) {
    final GetJobProgressResponse jobProgress =
        GetJobProgressResponse.newBuilder()
            .setJobId(pJobProgressView.getJobId())
            .setCreationTime(toTimestamp(pJobProgressView.getCreationTime()))
            .setTotalWorkUnits(pJobProgressView.getTotalWorkUnits())
            .setActiveWorkUnits(pJobProgressView.getActiveWorkUnits())
            .setCompletedWorkUnits(pJobProgressView.getCompletedWorkUnits())
            .setElapsedWorkTime(toDuration(pJobProgressView.getElapsedWorkTimeMs()))
            .setComplete(pJobProgressView.isComplete())
            .setErrorCount(pJobProgressView.getErrorCount())
            .build();

    _awsGrpcServer.setJobProgress(pJobId, jobProgress);
  }

  public void setJobErrors(
      final String pJobId, final GetJobErrorsResponseView pJobErrorsResponseView) {
    final GetJobErrorsResponse jobErrors =
        GetJobErrorsResponse.newBuilder()
            .setTaskError(
                TaskError.newBuilder()
                    .setErrorMessage(pJobErrorsResponseView.getErrorMessage())
                    .setSource(pJobErrorsResponseView.getSource())
                    .setSourceType(pJobErrorsResponseView.getSourceType())
                    .build())
            .build();

    _awsGrpcServer.setJobErrors(pJobId, jobErrors);
  }

  public boolean isDataSetCreated(
      final CloudProvider pCloudProvider, final String pGroupId, final String pDatasetName) {
    return getGrpcServer(pCloudProvider).isDataSetCreated(pGroupId, pDatasetName);
  }

  public boolean isDataSetEnabled(
      final CloudProvider pCloudProvider, final String pGroupId, final String pDatasetName) {
    return getGrpcServer(pCloudProvider).isDataSetEnabled(pGroupId, pDatasetName);
  }

  public boolean isDataSetReconciled(
      final CloudProvider pCloudProvider, final String pGroupId, final String pDatasetName) {
    return getGrpcServer(pCloudProvider).isDataSetReconciled(pGroupId, pDatasetName);
  }

  public boolean isDataSetDisabled(
      final CloudProvider pCloudProvider, final String pGroupId, final String pDatasetName) {
    return getGrpcServer(pCloudProvider).isDataSetDisabled(pGroupId, pDatasetName);
  }

  public boolean isDataSetDestroyed(
      final CloudProvider pCloudProvider, final String pGroupId, final String pDatasetName) {
    return getGrpcServer(pCloudProvider).isDataSetDestroyed(pGroupId, pDatasetName);
  }

  public boolean isOnlineArchiveDataSetEnabled(final String pGroupId, final String pDatasetName) {
    return _awsGrpcServer.isOnlineArchiveDataSetEnabled(pGroupId, pDatasetName);
  }

  public boolean isOnlineArchiveDataSetDestroyed(final OnlineArchive pOnlineArchive) {
    final String groupId = pOnlineArchive.getGroupId().toString();
    final String datasetName = pOnlineArchive.getId().toString();
    // AWS server is arbitrary - the server is ultimately mocked.
    return _awsGrpcServer.isOnlineArchiveDataSetDestroyed(groupId, datasetName);
  }

  public void setTenantMetrics(final String pTenantId, final NDSDataLakeMetricsView pMetrics) {
    _awsGrpcServer.setTenantMetrics(pTenantId, pMetrics);
  }

  public void setDataSetMetrics(
      final String pDataSetName,
      final boolean isUpdateInProgress,
      final Long pTotalDocumentCount,
      final Long pUncompressedSizeInBytes,
      final Date minDateField,
      final Date maxDateField) {
    _awsGrpcServer.setDataSetMetrics(
        pDataSetName,
        isUpdateInProgress,
        pTotalDocumentCount,
        pUncompressedSizeInBytes,
        minDateField,
        maxDateField);
  }

  public boolean isDatasetsMerged(
      final CloudProvider pCloudProvider,
      final String pGroupId,
      final String pSourceDataset,
      final String pDestinationDataset) {
    return getGrpcServer(pCloudProvider)
        .isDatasetsMerged(pGroupId, pSourceDataset, pDestinationDataset);
  }

  private Timestamp toTimestamp(final Date pDate) {
    final Instant now = pDate.toInstant();
    return Timestamp.newBuilder().setSeconds(now.getEpochSecond()).setNanos(now.getNano()).build();
  }

  private Duration toDuration(final long pMillis) {
    final long seconds = pMillis / 1000;
    final int nanos = (int) (pMillis % 1000) * 1_000_000;
    return Duration.newBuilder().setSeconds(seconds).setNanos(nanos).build();
  }

  public void setUsageLimit(final UsageLimit pUsageLimit) throws SvcException {
    _awsGrpcServer.setUsageLimit(pUsageLimit);
  }

  public Optional<DataScanningLimitStatus> getTenantUsageLimit(
      final CloudProvider pCloudProvider,
      final String pGroupId,
      final String pTenantId,
      final String pLimitSpan) {
    return getGrpcServer(pCloudProvider).getUsageLimit(pGroupId, pTenantId, pLimitSpan);
  }

  public Optional<DataScanningLimitStatus> getAwsUsageLimit(
      final String pGroupId, final String pTenantId, final String pLimitSpan) {
    return _awsGrpcServer.getUsageLimit(pGroupId, pTenantId, pLimitSpan);
  }

  public Optional<DataScanningLimitStatus> getGcpUsageLimit(
      final String pGroupId, final String pTenantId, final String pLimitSpan) {
    return _gcpGrpcServer.getUsageLimit(pGroupId, pTenantId, pLimitSpan);
  }

  public Optional<DataScanningLimitStatus> getUsageLimit(
      final String pGroupId, final String pLimitSpan) {
    return _awsGrpcServer.getUsageLimit(pGroupId, pLimitSpan);
  }

  public void deleteUsageLimit(final UsageLimit pUsageLimit) throws SvcException {
    _awsGrpcServer.deleteUsageLimit(pUsageLimit);
  }

  public void setAdminApiSQLSchemaSchedule(
      final String pTenantId, final NDSDataLakeSQLScheduledUpdateView pSchedule) {
    _awsGrpcServer.setScheduledUpdate(pTenantId, pSchedule);
  }

  public Boolean isOnlineArchiveAtlasDataSource(final Document pDataSourceDocument) {
    return pDataSourceDocument
        .getString(DataSource.STORE_NAME)
        .equals(OnlineArchiveDataLakeConfigSvc.ONLINE_ARCHIVE_TENANT_ATLAS_STORE_NAME);
  }

  public Boolean isOnlineArchiveS3DataSource(final Document pDataSourceDocument) {
    return pDataSourceDocument
        .getString(DataSource.STORE_NAME)
        .equals(OnlineArchiveDataLakeConfigSvc.ONLINE_ARCHIVE_TENANT_S3_STORE_NAME);
  }

  public Boolean hasAtlasProvider(final Document pStoreDocument) {
    return pStoreDocument.getString(Store.PROVIDER).equals(ProviderValues.ATLAS);
  }

  public Boolean hasDLSAWSProvider(final Document pStoreDocument) {
    return pStoreDocument.getString(Store.PROVIDER).equals(ProviderValues.DLS_AWS);
  }

  // This is not exhaustive and is just the fields commonly used in testing.
  public static class StorageConfigFieldDefs {
    public static final String STORAGE = "storage";
    public static final String STORES = "stores";
    public static final String DATABASES = "databases";

    public static class Store {
      // Common fields across stores
      public static final String NAME = "name";
      public static final String PROVIDER = "provider";

      // Blob Storage (S3 and Azure)
      public static final String REGION = "region";
      public static final String DELIMITER = "delimiter";
      public static final String REPLACEMENT_DELIMITER = "replacementDelimiter";
      public static final String PREFIX = "prefix";
      public static final String PUBLIC = "public";

      // S3
      public static final String BUCKET = "bucket";
      public static final String INCLUDE_TAGS = "includeTags";
      public static final String DOCUMENT_COUNT_METADATA_KEY = "documentCountMetadataKey";
      public static final String ADDITIONAL_STORAGE_CLASSES = "additionalStorageClasses";
      public static final String STORAGE_CLASS_INTELLIGENT_TIERING = "INTELLIGENT_TIERING";

      // Azure
      public static final String SERVICE_URL = "serviceURL";
      public static final String CONTAINER_NAME = "containerName";

      // HTTP
      public static final String URLS = "urls";
      public static final String ALLOW_INSECURE = "allowInsecure";
      public static final String DEFAULT_FORMAT = "defaultFormat";

      // Atlas or Online Archive
      public static final String CLUSTER_NAME = "clusterName";
      public static final String CLUSTER_ID = "clusterId";
      public static final String PROJECT_ID = "projectId";

      // Atlas
      public static final String READ_PREFERENCE = "readPreference";
      public static final String READ_PREFERENCE_MODE = "mode";
      public static final String READ_PREFERENCE_MAX_STALENESS_SECONDS = "maxStalenessSeconds";
      public static final String READ_PREFERENCE_TAG_SETS = "tagSets";
      public static final String READ_CONCERN = "readConcern";
    }

    public static class Database {
      public static final String NAME = "name";
      public static final String COLLECTIONS = "collections";
      public static final String VIEWS = "views";
      public static final String MAX_WILDCARD_COLLECTIONS = "maxWildcardCollections";
    }

    public static class Collection {
      public static final String NAME = "name";
      public static final String DATA_SOURCES = "dataSources";
    }

    public static class View {
      public static final String NAME = "name";
      public static final String SOURCE = "source";
      public static final String PIPELINE = "pipeline";
    }

    public static class DataSource {

      // All data source types
      public static final String PROVIDER = "provider";
      public static final String STORE_NAME = "storeName";
      public static final String PROVENANCE_FIELD_NAME = "provenanceFieldName";

      // Blob Storage (S3 and Azure)
      public static final String PATH = "path";
      public static final String OMIT_ATTRIBUTES = "omitAttributes";

      // Blob Storage and HTTP
      public static final String DEFAULT_FORMAT = "defaultFormat";

      // HTTP
      public static final String URLS = "urls";
      public static final String ALLOW_INSECURE = "allowInsecure";

      // Atlas
      public static final String DATABASE = "database";
      public static final String COLLECTION = "collection";
      public static final String COLLECTION_REGEX = "collectionRegex";
      public static final String DATABASE_REGEX = "databaseRegex";

      // DLS and Online Archive
      public static final String METADATA_LOCATION = "metadataLocation";
      public static final String DATASET_NAME = "datasetName";
      public static final String DATASET_PREFIX = "datasetPrefix";
      public static final String TRIM_LEVEL = "trimLevel";
      public static final String MAX_DATASETS = "maxDatasets";
    }
  }
}
