load("//server/src/test:rules.bzl", "test_package")

test_package(
    name = "TestLibrary",
    srcs = glob(["*IntTests.java"]),
    deny_warnings = True,
    runtime_deps = ["//server/src/main/com/xgen/module/metering/server/res"],
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/abtesting",
        "//server/src/main/com/xgen/cloud/access/authz",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/alerts/alert",
        "//server/src/main/com/xgen/cloud/atm/core",
        "//server/src/main/com/xgen/cloud/atm/core/_private/dao",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/billing",
        "//server/src/main/com/xgen/cloud/billingimport/azure",
        "//server/src/main/com/xgen/cloud/billingplatform/model/plan",
        "//server/src/main/com/xgen/cloud/billingplatform/model/units",
        "//server/src/main/com/xgen/cloud/brs/core",
        "//server/src/main/com/xgen/cloud/brs/core/_private/dao",
        "//server/src/main/com/xgen/cloud/brs/daemon",
        "//server/src/main/com/xgen/cloud/brs/web",
        "//server/src/main/com/xgen/cloud/common/agent",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/auditInfoHelper",
        "//server/src/main/com/xgen/cloud/common/brs",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/metrics",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/cps/backupjob",
        "//server/src/main/com/xgen/cloud/cps/backupjob/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/billing/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/billing/_public/model",
        "//server/src/main/com/xgen/cloud/cps/core/_public/config",
        "//server/src/main/com/xgen/cloud/cps/pit",
        "//server/src/main/com/xgen/cloud/cps/pit/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/restore",
        "//server/src/main/com/xgen/cloud/cps/restore/_private/dao",
        "//server/src/main/com/xgen/cloud/dataexport",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/email",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/common",
        "//server/src/main/com/xgen/cloud/monitoring/lifecycle",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/nds/activity",
        "//server/src/main/com/xgen/cloud/nds/autoscaling/common",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/billing",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/datavalidation",
        "//server/src/main/com/xgen/cloud/nds/dbcheck",
        "//server/src/main/com/xgen/cloud/nds/deployment",
        "//server/src/main/com/xgen/cloud/nds/dns",
        "//server/src/main/com/xgen/cloud/nds/events",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/hostname",
        "//server/src/main/com/xgen/cloud/nds/metering",
        "//server/src/main/com/xgen/cloud/nds/module",
        "//server/src/main/com/xgen/cloud/nds/monitoring",
        "//server/src/main/com/xgen/cloud/nds/mtmcompaction",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/planning/common",
        "//server/src/main/com/xgen/cloud/nds/pointsofpresence",
        "//server/src/main/com/xgen/cloud/nds/privatelink",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/rollingresync",
        "//server/src/main/com/xgen/cloud/nds/security",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/serverless/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/simulateregionoutage",
        "//server/src/main/com/xgen/cloud/nds/spothealthcheck",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/cloud/nds/tenantupgrade",
        "//server/src/main/com/xgen/cloud/nds/vmimage",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/performanceadvisor",
        "//server/src/main/com/xgen/cloud/search/decoupled/config",
        "//server/src/main/com/xgen/cloud/team",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/main/com/xgen/module/metering/client/svc",
        "//server/src/main/com/xgen/module/metering/common/exception",
        "//server/src/main/com/xgen/module/metering/common/model",
        "//server/src/main/com/xgen/module/metering/common/utils",
        "//server/src/main/com/xgen/module/metering/common/view",
        "//server/src/main/com/xgen/module/metering/server/svc",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/test/com/xgen/module/common/planner/inttestimpls",
        "//server/src/test/com/xgen/svc/common",
        "//server/src/test/com/xgen/svc/core",
        "//server/src/test/com/xgen/svc/nds",
        "//server/src/test/com/xgen/testlib/base/nds",
        "//server/src/test/com/xgen/testlib/junit5/extensions",
        "//server/src/unit/com/xgen/svc/mms/util",
        "//server/src/unit/com/xgen/svc/nds/model",
        "//server/src/unit/com/xgen/svc/nds/serverless/model:serverlessTestFactory",
        "//third_party:driverwrappers",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
    ],
)
