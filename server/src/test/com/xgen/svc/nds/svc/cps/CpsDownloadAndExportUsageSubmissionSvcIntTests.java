package com.xgen.svc.nds.svc.cps;

import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_CPS_AWS_DOWNLOAD_USAGE_SUBMISSION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_CPS_AWS_EXPORT_USAGE_SUBMISSION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_CPS_AZURE_DOWNLOAD_USAGE_SUBMISSION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_CPS_AZURE_EXPORT_USAGE_SUBMISSION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_CPS_EXPORT_USAGE_SUBMISSION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_CPS_GCP_DOWNLOAD_USAGE_SUBMISSION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_CPS_GCP_EXPORT_USAGE_SUBMISSION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType.MEMORY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.cps.billing._private.dao.CpsBillingPropertiesDao;
import com.xgen.module.metering.client.svc.IMeterReportSvc;
import com.xgen.module.metering.common.model.MeterId;
import com.xgen.module.metering.common.model.MeterUsage;
import com.xgen.module.metering.common.model.usagedimensions.AwsBackupDownloadExportUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.AzureBackupDownloadExportUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.CpsUsageDimensions;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.svc.BackupRestoreJobSvc;
import com.xgen.svc.nds.svc.CloudProviderContainerCleanupSvc;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class CpsDownloadAndExportUsageSubmissionSvcIntTests extends JUnit5BaseSvcTest {
  @Inject private CpsAwsDownloadUsageSubmissionSvc _cpsAwsDownloadUsageSubmissionSvc;
  @Inject private CpsAzureDownloadUsageSubmissionSvc _cpsAzureDownloadUsageSubmissionSvc;
  @Inject private CpsGcpDownloadUsageSubmissionSvc _cpsGcpDownloadUsageSubmissionSvc;
  @Inject private CpsGcpExportUsageSubmissionSvc _cpsGcpExportUsageSubmissionSvc;
  @Inject private CpsAwsExportUsageSubmissionSvc _cpsAwsExportUsageSubmissionSvc;
  @Inject private CpsAzureExportUsageSubmissionSvc _cpsAzureExportUsageSubmissionSvc;
  @Inject private CpsExportUsageSubmissionSvc _cpsExportUsageSubmissionSvc;
  @Inject private CpsBillingPropertiesDao _cpsBillingPropertiesDao;
  @Inject private AppSettings _appSettings;
  @Inject private BackupRestoreJobSvc _backupRestoreJobSvc;
  @Inject private CloudProviderContainerCleanupSvc _cloudProviderContainerCleanupSvc;
  @Inject private CpsUsageSvc _cpsUsageSvc;
  @Mock private IMeterReportSvc _meterReportSvc;
  @Captor private ArgumentCaptor<List<MeterUsage>> _meterUsagesCaptor;

  private AutoCloseable mocks;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    mocks = MockitoAnnotations.openMocks(this);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupSnapshotDao/cpsBillingProperties.json.ftl",
        null,
        "nds",
        "config.nds.backup.cpsBillingProperties");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupRestoreJobDao/backupRestoreJobsBilling.json.ftl",
        null,
        "nds",
        "config.nds.backup.restorejobs");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupRestoreJobDao/backupRestoreJobsExportsBilling.json.ftl",
        null,
        "nds",
        "config.nds.backup.restorejobs");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSSystemClusterJobDao/ndsGroups.json.ftl", null, "nds", "config.nds.groups");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSSystemClusterJobDao/systemClusterJobs.json.ftl",
        null,
        "nds",
        "config.nds.backup.systemClusterJobs");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSSystemClusterJobDao/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");

    _appSettings.setProp(NDS_CPS_AWS_DOWNLOAD_USAGE_SUBMISSION.value, "true", MEMORY);
    _appSettings.setProp(NDS_CPS_AZURE_DOWNLOAD_USAGE_SUBMISSION.value, "true", MEMORY);
    _appSettings.setProp(NDS_CPS_GCP_DOWNLOAD_USAGE_SUBMISSION.value, "true", MEMORY);
    _appSettings.setProp(NDS_CPS_GCP_EXPORT_USAGE_SUBMISSION.value, "true", MEMORY);
    _appSettings.setProp(NDS_CPS_AWS_EXPORT_USAGE_SUBMISSION.value, "true", MEMORY);
    _appSettings.setProp(NDS_CPS_AZURE_EXPORT_USAGE_SUBMISSION.value, "true", MEMORY);
    _appSettings.setProp(NDS_CPS_EXPORT_USAGE_SUBMISSION.value, "true", MEMORY);

    doNothing().when(_meterReportSvc).submitMeterUsage(any(), any());

    _cpsAwsDownloadUsageSubmissionSvc =
        new CpsAwsDownloadUsageSubmissionSvc(
            _appSettings, _cpsBillingPropertiesDao, _meterReportSvc, _backupRestoreJobSvc);
    _cpsAzureDownloadUsageSubmissionSvc =
        new CpsAzureDownloadUsageSubmissionSvc(
            _appSettings, _cpsBillingPropertiesDao, _meterReportSvc, _backupRestoreJobSvc);
    _cpsGcpDownloadUsageSubmissionSvc =
        new CpsGcpDownloadUsageSubmissionSvc(
            _appSettings, _cpsBillingPropertiesDao, _meterReportSvc, _backupRestoreJobSvc);
    _cpsGcpExportUsageSubmissionSvc =
        new CpsGcpExportUsageSubmissionSvc(
            _appSettings, _cpsBillingPropertiesDao, _meterReportSvc, _cpsUsageSvc);
    _cpsAwsExportUsageSubmissionSvc =
        new CpsAwsExportUsageSubmissionSvc(
            _appSettings, _cpsBillingPropertiesDao, _meterReportSvc, _cpsUsageSvc);
    _cpsAzureExportUsageSubmissionSvc =
        new CpsAzureExportUsageSubmissionSvc(
            _appSettings, _cpsBillingPropertiesDao, _meterReportSvc, _cpsUsageSvc);
    _cpsExportUsageSubmissionSvc =
        new CpsExportUsageSubmissionSvc(
            _appSettings, _cpsBillingPropertiesDao, _meterReportSvc, _cpsUsageSvc);
  }

  @AfterEach
  public void tearDown() throws Exception {
    mocks.close();
  }

  @Test
  public void testAppPropNotSet_doNothing() throws Exception {
    _appSettings.setProp(NDS_CPS_AWS_DOWNLOAD_USAGE_SUBMISSION.value, "false", MEMORY);
    _cpsAwsDownloadUsageSubmissionSvc.submitUsage();
    verify(_meterReportSvc, never())
        .submitMeterUsage(anyList(), eq(CpsAwsDownloadUsageSubmissionSvc.METER_SVC_JOB_NAME));
  }

  @Test
  public void testCpsAwsDownloadUsageSubmissionSvc_calculateMeterUsages_noUsage() {
    final LocalDate start = LocalDate.now().minusYears(2);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages =
        _cpsAwsDownloadUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 0);
  }

  @Test
  public void testCpsAwsDownloadUsageSubmissionSvc_calculateMeterUsages() {
    final LocalDate start = LocalDate.now().minusDays(2);
    final LocalDate end = LocalDate.now();
    final List<MeterUsage> usages =
        _cpsAwsDownloadUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 3);
  }

  @Test
  public void testCpsAwsDownloadUsageSubmissionSvc_submitUsage() throws Exception {
    _cpsAwsDownloadUsageSubmissionSvc.submitUsage();

    verify(_meterReportSvc)
        .submitMeterUsage(
            _meterUsagesCaptor.capture(), eq(CpsAwsDownloadUsageSubmissionSvc.METER_SVC_JOB_NAME));
    List<MeterUsage> meterUsages = _meterUsagesCaptor.getValue();

    assertEquals(meterUsages.size(), 3);
    final MeterUsage meterUsage1 =
        meterUsages.stream()
            .filter(m -> MeterId.NDS_AWS_BACKUP_DOWNLOAD_VM_STORAGE.equals(m.getMeterId()))
            .findFirst()
            .get();
    assertEquals(meterUsage1.getQuantity(), 779, 0);
    final MeterUsage meterUsage2 =
        meterUsages.stream()
            .filter(m -> MeterId.NDS_AWS_BACKUP_DOWNLOAD_VM_STORAGE_IOPS.equals(m.getMeterId()))
            .findFirst()
            .get();
    assertEquals(meterUsage2.getQuantity(), 285, 0);
    final MeterUsage meterUsage3 =
        meterUsages.stream()
            .filter(m -> MeterId.NDS_AWS_BACKUP_DOWNLOAD_VM.equals(m.getMeterId()))
            .findFirst()
            .get();
    assertEquals(meterUsage3.getQuantity(), 19, 0);
  }

  @Test
  public void testCpsAzureDownloadUsageSubmissionSvc_calculateMeterUsages_noUsage() {
    final LocalDate start = LocalDate.now().minusYears(2);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages =
        _cpsAzureDownloadUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 0);
  }

  @Test
  public void testCpsAzureDownloadUsageSubmissionSvc_calculateMeterUsages() {
    final LocalDate start = LocalDate.now().minusDays(2);
    final LocalDate end = LocalDate.now();
    final List<MeterUsage> usages =
        _cpsAzureDownloadUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 5);
  }

  @Test
  public void testCpsAzureDownloadUsageSubmissionSvc_submitUsage() throws Exception {
    _cpsAzureDownloadUsageSubmissionSvc.submitUsage();

    verify(_meterReportSvc)
        .submitMeterUsage(
            _meterUsagesCaptor.capture(),
            eq(CpsAzureDownloadUsageSubmissionSvc.METER_SVC_JOB_NAME));
    List<MeterUsage> meterUsages = _meterUsagesCaptor.getValue();

    assertEquals(meterUsages.size(), 5);
    final MeterUsage meterUsage1 =
        meterUsages.stream()
            .filter(m -> MeterId.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P30.equals(m.getMeterId()))
            .findFirst()
            .get();
    assertEquals(meterUsage1.getQuantity(), 24576, 0);
    final MeterUsage meterUsage2 =
        meterUsages.stream()
            .filter(m -> MeterId.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE.equals(m.getMeterId()))
            .findFirst()
            .get();
    assertEquals(meterUsage2.getQuantity(), 820, 0);
    final MeterUsage meterUsage3 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_EXTENDED_IOPS.equals(
                        m.getMeterId()))
            .findFirst()
            .get();
    assertEquals(30000, meterUsage3.getQuantity(), 0);
    final MeterUsage meterUsage4 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_AZURE_BACKUP_DOWNLOAD_VM.equals(m.getMeterId())
                        && m.getGroupId().equals(oid(142)))
            .findFirst()
            .get();
    assertEquals(meterUsage4.getQuantity(), 24, 0);
    final MeterUsage meterUsage5 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_AZURE_BACKUP_DOWNLOAD_VM.equals(m.getMeterId())
                        && m.getGroupId().equals(oid(143)))
            .findFirst()
            .get();
    assertEquals(meterUsage5.getQuantity(), 20, 0);
  }

  @Test
  public void testCpsGcpDownloadUsageSubmissionSvc_calculateMeterUsages_noUsage() {
    final LocalDate start = LocalDate.now().minusYears(2);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages =
        _cpsGcpDownloadUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 0);
  }

  @Test
  public void testCpsGcpDownloadUsageSubmissionSvc_calculateMeterUsages() {
    final LocalDate start = LocalDate.now().minusDays(2);
    final LocalDate end = LocalDate.now();
    final List<MeterUsage> usages =
        _cpsGcpDownloadUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 2);
  }

  @Test
  public void testCpsGcpDownloadUsageSubmissionSvc_submitUsage() throws Exception {
    _cpsGcpDownloadUsageSubmissionSvc.submitUsage();

    verify(_meterReportSvc)
        .submitMeterUsage(
            _meterUsagesCaptor.capture(), eq(CpsGcpDownloadUsageSubmissionSvc.METER_SVC_JOB_NAME));
    List<MeterUsage> meterUsages = _meterUsagesCaptor.getValue();

    assertEquals(meterUsages.size(), 2);
    final MeterUsage meterUsage1 =
        meterUsages.stream()
            .filter(m -> MeterId.NDS_GCP_BACKUP_DOWNLOAD_VM.equals(m.getMeterId()))
            .findFirst()
            .get();
    assertEquals(meterUsage1.getQuantity(), 19, 0);
    final MeterUsage meterUsage2 =
        meterUsages.stream()
            .filter(m -> MeterId.NDS_GCP_BACKUP_DOWNLOAD_VM_STORAGE.equals(m.getMeterId()))
            .findFirst()
            .get();
    assertEquals(meterUsage2.getQuantity(), 950, 0);
  }

  @Test
  public void testCpsGcpExportUsageSubmissionSvc_calculateMeterUsages_noUsage() {
    final LocalDate start = LocalDate.now().minusYears(2);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages =
        _cpsGcpExportUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 0);
  }

  @Test
  public void testCpsGcpExportUsageSubmissionSvc_calculateMeterUsages() {
    final LocalDate start = LocalDate.of(2021, 3, 31);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages =
        _cpsGcpExportUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 4);
  }

  @Test
  public void testCpsGcpExportUsageSubmissionSvc_submitUsage() throws Exception {
    _cpsGcpExportUsageSubmissionSvc.submitUsage();

    verify(_meterReportSvc)
        .submitMeterUsage(
            _meterUsagesCaptor.capture(), eq(CpsGcpExportUsageSubmissionSvc.METER_SVC_JOB_NAME));
    List<MeterUsage> meterUsages = _meterUsagesCaptor.getValue();

    assertEquals(meterUsages.size(), 4);
    final MeterUsage meterUsage1 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_GCP_SNAPSHOT_EXPORT_VM.equals(m.getMeterId())
                        && m.getGroupId().equals(oid(142)))
            .findFirst()
            .get();
    assertEquals(meterUsage1.getQuantity(), 1, 0);
    final MeterUsage meterUsage2 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_GCP_SNAPSHOT_EXPORT_VM.equals(m.getMeterId())
                        && m.getGroupId().equals(oid(145)))
            .findFirst()
            .get();
    assertEquals(meterUsage2.getQuantity(), 1, 0);
    final MeterUsage meterUsage3 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_GCP_SNAPSHOT_EXPORT_VM_STORAGE.equals(m.getMeterId())
                        && m.getGroupId().equals(oid(142)))
            .findFirst()
            .get();
    assertEquals(meterUsage3.getQuantity(), 10, 0);
    final MeterUsage meterUsage4 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_GCP_SNAPSHOT_EXPORT_VM_STORAGE.equals(m.getMeterId())
                        && m.getGroupId().equals(oid(145)))
            .findFirst()
            .get();
    assertEquals(meterUsage4.getQuantity(), 15, 0);
  }

  @Test
  public void testCpsAwsExportUsageSubmissionSvc_calculateMeterUsages_noUsage() {
    final LocalDate start = LocalDate.now().minusYears(2);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages =
        _cpsAwsExportUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 0);
  }

  @Test
  public void testCpsAwsExportUsageSubmissionSvc_calculateMeterUsages() {
    final LocalDate start = LocalDate.of(2021, 3, 31);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages =
        _cpsAwsExportUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 12);
  }

  @Test
  public void testCpsAwsExportUsageSubmissionSvc_submitUsage() throws Exception {
    _cpsAwsExportUsageSubmissionSvc.submitUsage();

    verify(_meterReportSvc, times(2))
        .submitMeterUsage(
            _meterUsagesCaptor.capture(), eq(CpsAwsExportUsageSubmissionSvc.METER_SVC_JOB_NAME));
    List<List<MeterUsage>> meterUsagesList = _meterUsagesCaptor.getAllValues();

    List<MeterUsage> meterUsages1 = meterUsagesList.get(0);
    assertEquals(meterUsages1.size(), 12);
    final List<MeterUsage> vmMeterUsagesDay1 =
        meterUsages1.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(vmMeterUsagesDay1.size(), 4);
    vmMeterUsagesDay1.forEach(u -> assertEquals(u.getQuantity(), 1, 0));
    final List<MeterUsage> storageMeterUsagesDay1 =
        meterUsages1.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(storageMeterUsagesDay1.size(), 4);
    storageMeterUsagesDay1.forEach(u -> assertEquals(u.getQuantity(), 10, 0));
    final List<MeterUsage> iopsMeterUsagesDay1 =
        meterUsages1.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE_IOPS.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(iopsMeterUsagesDay1.size(), 4);
    iopsMeterUsagesDay1.forEach(u -> assertEquals(u.getQuantity(), 300, 0));

    List<MeterUsage> meterUsages2 = meterUsagesList.get(1);
    assertEquals(meterUsages2.size(), 3);
    final List<MeterUsage> vmMeterUsagesDay2 =
        meterUsages2.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(vmMeterUsagesDay2.size(), 1);
    vmMeterUsagesDay2.forEach(u -> assertEquals(u.getQuantity(), 1, 0));
    final List<MeterUsage> storageMeterUsagesDay2 =
        meterUsages2.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(storageMeterUsagesDay2.size(), 1);
    storageMeterUsagesDay2.forEach(u -> assertEquals(u.getQuantity(), 10, 0));
    final List<MeterUsage> iopsMeterUsagesDay2 =
        meterUsages2.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE_IOPS.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(iopsMeterUsagesDay2.size(), 1);
    iopsMeterUsagesDay2.forEach(u -> assertEquals(u.getQuantity(), 300, 0));
  }

  @Test
  public void testCpsExportUsageSubmissionSvc_calculateMeterUsages_noUsage() {
    final LocalDate start = LocalDate.now().minusYears(2);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages = _cpsExportUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 0);
  }

  @Test
  public void testCpsExportUsageSubmissionSvc_calculateMeterUsages() {
    final LocalDate start = LocalDate.of(2024, 7, 3);
    final LocalDate end = start.plusDays(2);
    final List<MeterUsage> usages = _cpsExportUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 10);
  }

  @Test
  public void
      testCpsExportUsageSubmissionSvc_calculateMeterUsages_includingArchivedClusterDescriptions() {
    _cloudProviderContainerCleanupSvc.runCleanUp();
    final LocalDate start = LocalDate.of(2024, 7, 3);
    final LocalDate end = start.plusDays(2);
    final List<MeterUsage> usages = _cpsExportUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 10);
  }

  @Test
  public void testCpsExportUsageSubmissionSvc_submitUsage() throws Exception {
    _cloudProviderContainerCleanupSvc.runCleanUp();
    _cpsExportUsageSubmissionSvc.submitUsage();

    verify(_meterReportSvc, times(2))
        .submitMeterUsage(
            _meterUsagesCaptor.capture(), eq(CpsExportUsageSubmissionSvc.METER_SVC_JOB_NAME));
    List<List<MeterUsage>> meterUsagesList = _meterUsagesCaptor.getAllValues();

    List<MeterUsage> meterUsages1 = meterUsagesList.get(0);
    assertEquals(meterUsages1.size(), 5);
    List<MeterUsage> usage =
        meterUsages1.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 9, 0);
    assertEquals(
        ((AwsBackupDownloadExportUsageDimensions) usage.get(0).getUsageDimensions())
            .getClusterUniqueId(),
        oid(345));

    usage =
        meterUsages1.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_M50.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 18, 0);
    assertEquals(
        ((AwsBackupDownloadExportUsageDimensions) usage.get(0).getUsageDimensions())
            .getClusterUniqueId(),
        oid(345));

    usage =
        meterUsages1.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 720, 0);
    assertEquals(
        ((AwsBackupDownloadExportUsageDimensions) usage.get(0).getUsageDimensions())
            .getClusterUniqueId(),
        oid(345));

    usage =
        meterUsages1.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE_IOPS.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 36000, 0);
    assertEquals(
        ((AwsBackupDownloadExportUsageDimensions) usage.get(0).getUsageDimensions())
            .getClusterUniqueId(),
        oid(345));

    usage =
        meterUsages1.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_UPLOAD.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 2, 0);
    assertEquals(
        ((CpsUsageDimensions) usage.get(0).getUsageDimensions()).getClusterUniqueId(), oid(345));

    List<MeterUsage> meterUsages2 = meterUsagesList.get(1);
    assertEquals(meterUsages2.size(), 5);
    usage =
        meterUsages2.stream()
            .filter(m -> MeterId.NDS_AZURE_SNAPSHOT_EXPORT_VM_M50.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 72, 0);
    assertEquals(
        ((AzureBackupDownloadExportUsageDimensions) usage.get(0).getUsageDimensions())
            .getClusterUniqueId(),
        oid(234));

    usage =
        meterUsages2.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_UPLOAD.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 4, 0);
    assertEquals(
        ((CpsUsageDimensions) usage.get(0).getUsageDimensions()).getClusterUniqueId(), oid(234));

    usage =
        meterUsages2.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_M50.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 9, 0);
    assertEquals(
        ((AwsBackupDownloadExportUsageDimensions) usage.get(0).getUsageDimensions())
            .getClusterUniqueId(),
        oid(123));

    usage =
        meterUsages2.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 90, 0);
    assertEquals(
        ((AwsBackupDownloadExportUsageDimensions) usage.get(0).getUsageDimensions())
            .getClusterUniqueId(),
        oid(123));

    usage =
        meterUsages2.stream()
            .filter(m -> MeterId.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE_IOPS.equals(m.getMeterId()))
            .collect(Collectors.toList());
    assertEquals(usage.size(), 1);
    assertEquals(usage.get(0).getQuantity(), 4500, 0);
    assertEquals(
        ((AwsBackupDownloadExportUsageDimensions) usage.get(0).getUsageDimensions())
            .getClusterUniqueId(),
        oid(123));
  }

  @Test
  public void testCpsAzureExportUsageSubmissionSvc_calculateMeterUsages_noUsage() {
    final LocalDate start = LocalDate.now().minusYears(2);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages =
        _cpsAzureExportUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 0);
  }

  @Test
  public void testCpsAzureExportUsageSubmissionSvc_calculateMeterUsages() {
    final LocalDate start = LocalDate.of(2021, 3, 31);
    final LocalDate end = start.plusDays(1);
    final List<MeterUsage> usages =
        _cpsAzureExportUsageSubmissionSvc.calculateMeterUsages(start, end);
    assertEquals(usages.size(), 5);
  }

  @Test
  public void testCpsAzureExportUsageSubmissionSvc_submitUsage() throws Exception {
    _cpsAzureExportUsageSubmissionSvc.submitUsage();

    verify(_meterReportSvc)
        .submitMeterUsage(
            _meterUsagesCaptor.capture(), eq(CpsAzureExportUsageSubmissionSvc.METER_SVC_JOB_NAME));
    List<MeterUsage> meterUsages = _meterUsagesCaptor.getValue();

    assertEquals(meterUsages.size(), 5);

    final MeterUsage meterUsage1 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_AZURE_SNAPSHOT_EXPORT_VM.equals(m.getMeterId())
                        && oid(142).equals(m.getGroupId()))
            .findFirst()
            .get();
    assertEquals(meterUsage1.getQuantity(), 1, 0);

    final MeterUsage meterUsage2 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_AZURE_SNAPSHOT_EXPORT_VM.equals(m.getMeterId())
                        && oid(145).equals(m.getGroupId()))
            .findFirst()
            .get();
    assertEquals(meterUsage2.getQuantity(), 3, 0);

    final MeterUsage meterUsage3 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P3.equals(m.getMeterId())
                        && oid(142).equals(m.getGroupId()))
            .findFirst()
            .get();
    assertEquals(meterUsage3.getQuantity(), 10, 0);

    final MeterUsage meterUsage4 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE.equals(m.getMeterId())
                        && oid(145).equals(m.getGroupId()))
            .findFirst()
            .get();
    assertEquals(meterUsage4.getQuantity(), 30, 0);

    final MeterUsage meterUsage5 =
        meterUsages.stream()
            .filter(
                m ->
                    MeterId.NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_EXTENDED_IOPS.equals(
                            m.getMeterId())
                        && oid(145).equals(m.getGroupId()))
            .findFirst()
            .get();
    assertEquals(4500, meterUsage5.getQuantity(), 0);
  }
}
