package com.xgen.svc.nds.svc;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.chef._private.dao.ChefServerStatusDao;
import com.xgen.cloud.chef._public.model.ChefServerStatus;
import com.xgen.cloud.chef._public.model.ChefServerStatus.FieldDefs;
import com.xgen.cloud.chef._public.svc.ChefServerStatusSvc;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.chef.CloudChefConf;
import com.xgen.cloud.nds.cloudprovider._public.model.chef.CloudChefConf.Builder;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.project._private.dao.ChefPackageStatusDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ChefPackageStatus;
import com.xgen.cloud.nds.project._public.model.LogUploaderService;
import com.xgen.cloud.nds.project._public.model.LogUploaderServiceRecord;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.SystemdServices;
import com.xgen.cloud.nds.project._public.model.SystemdServicesRecord;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.EnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.ServerlessDeploymentModelTestFactory;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.svc.ChefCallbackSvc.Data;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ChefCallbackSvcIntTests extends JUnit5BaseSvcTest {

  @Inject private NDSGroupSvc _ndsGroupSvc;

  @Inject private ChefCallbackSvc _chefCallbackSvc;

  @Inject private ChefServerStatusSvc _chefServerStatusSvc;

  @Inject private ChefServerStatusDao _chefServerStatusDao;

  @Inject private ChefPackageStatusDao _chefPackageStatusDao;

  @Inject private CloudChefConfSvc _cloudChefConfSvc;

  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @Inject private ServerlessLoadBalancingDeploymentDao _serverlessLoadBalancingDeploymentDao;

  private final ObjectMapper _objectMapper = CustomJacksonJsonProvider.createObjectMapper();

  private Group _mmsGroup;

  private NDSGroup _ndsGroup;

  private String _clusterName;
  private ObjectId _instanceId;

  private static final String hostname = "foo.example.com";

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    setupGroupAndHardware();
    setUpServerStatus();
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  private void setupGroupAndHardware() throws Exception {
    _mmsGroup = MmsFactory.createGroupWithNDSPlan();
    _ndsGroup = _ndsGroupSvc.ensureGroup(_mmsGroup.getId());
    _clusterName = "foo";
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(_clusterName, _mmsGroup.getId(), 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(_clusterName, 0),
        true,
        false,
        new ObjectId());
    _instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    final Hostnames hostnames = new Hostnames(hostname);
    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        _instanceId,
        false,
        InstanceHardware.FieldDefs.HOSTNAMES,
        hostnames.toDBList());
  }

  private void setUpServerStatus() {
    // Insert non-null last successful chef run date.
    _chefServerStatusDao.createOrUpdate(
        hostname,
        _mmsGroup.getId(),
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        Map.of(FieldDefs.DATA_CHEF_LAST_SUCCESFUL_RUN, **********));

    // Insert null last successful chef run date.
    final Map<String, Object> data = new HashMap<>();
    data.put(FieldDefs.DATA_CHEF_LAST_SUCCESFUL_RUN, null);
    _chefServerStatusDao.createOrUpdate(
        "foo.example.null",
        _mmsGroup.getId(),
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        data);
  }

  @Test
  public void testIsChefRunning() throws Exception {

    final Map<String, Object> data = new HashMap<>();
    data.put("a", "b");

    // chef_last_successful_run field does not exist
    assertFalse(_chefServerStatusSvc.isChefRunning(data));

    // chef_last_successful_run field too old
    data.put("chef_last_successful_run", 1619617744);
    assertFalse(_chefServerStatusSvc.isChefRunning(data));

    // chef_last_successful_run field too old and string
    data.put("chef_last_successful_run", "1619617744");
    assertFalse(_chefServerStatusSvc.isChefRunning(data));

    // chef_last_successful_run field is current
    data.put("chef_last_successful_run", (int) Instant.now().getEpochSecond());
    assertTrue(_chefServerStatusSvc.isChefRunning(data));
  }

  @Test
  public void testIsChefFailure() throws Exception {
    final Map<String, Object> data = new HashMap<>();
    data.put("a", "b");

    // chef_last_stacktrace field does not exist
    assertFalse(_chefServerStatusSvc.isChefFailure(data));

    // chef_last_successful_run is empty
    data.put("chef_last_stacktrace", "");
    assertFalse(_chefServerStatusSvc.isChefFailure(data));

    // chef_last_successful_run field is current
    data.put("chef_last_stacktrace", "some content");
    assertTrue(_chefServerStatusSvc.isChefFailure(data));
  }

  @Test
  public void testUpdateServerStatusWithNonExistentHostname() throws Exception {

    final Map<String, Object> data = new HashMap<>();
    data.put("a", "b");
    data.put("c", "d");

    // Test with non-existent hostname
    _chefCallbackSvc.updateServerStatus("bar.example.com", _mmsGroup.getId(), new Date(), data);

    assertFalse(_chefServerStatusDao.findByHostname("bar.example.com").isPresent());
  }

  @Test
  public void testGetNodeAction_EnvoyInstanceHostname() throws Exception {

    final ServerlessLoadBalancingDeployment deployment =
        ServerlessDeploymentModelTestFactory.getAWSServerlessLoadBalancingDeployment(
            AWSRegionName.US_EAST_1);
    _serverlessLoadBalancingDeploymentDao.save(deployment);

    final EnvoyInstance instance0 = deployment.getEnvoyInstances().get(0);
    final String hostname0 = instance0.getInternalHostname().get();

    final Builder<?> _cloudChefConfBuilder = CloudChefConf.builder();
    _cloudChefConfBuilder.cloudProvider(CloudProvider.AWS.getChefProvider());
    _cloudChefConfBuilder.hostnameSubdomainLevel(InstanceHostname.SubdomainLevel.MONGODB.name());
    _cloudChefConfBuilder.groupId(_mmsGroup.getId().toString());
    _cloudChefConfBuilder.dataDevice("/dev/xvdb");
    _cloudChefConfBuilder.fqdn(hostname0);
    _cloudChefConfBuilder.ip("127.0.0.1");
    _cloudChefConfBuilder.clusterName("bar");
    _cloudChefConfBuilder.chefCallbackUrl("url");
    _cloudChefConfBuilder.region("region");
    _cloudChefConfBuilder.nodeName("nodeName");
    _cloudChefConfBuilder.logRetention("logRetention");
    _cloudChefConfBuilder.validDatetime(CloudChefConf.generateValidDatetime());
    _cloudChefConfBuilder.createdAt(new Date());
    _cloudChefConfBuilder.instanceId(instance0.getId());
    _cloudChefConfBuilder.pushBasedLogExportConfig(
        CloudChefConfSvc.getDefaultDisabledLogExportConfig());
    _cloudChefConfBuilder.gatewayRouterBootstrapConfig(
        CloudChefConfSvc.getDefaultDisabledGatewayRouterBootstrapConfig());

    CloudChefConf cloudChefConf = _cloudChefConfBuilder.build();
    _cloudChefConfSvc.saveCloudChefConf(cloudChefConf);

    final Map<String, Object> data = new HashMap<>();
    data.put("a", "b");
    data.put("c", "d");

    // Test with for initial boot not set
    assertTrue(
        _chefCallbackSvc
            .getNodeAction(hostname0, deployment.getGroupId(), null)
            .getConfNonce()
            .isPresent());

    final EnvoyInstance instance1 = deployment.getEnvoyInstances().get(1);
    final String hostname1 = instance1.getInternalHostname().get();

    // Test with for initial boot set to true
    cloudChefConf =
        _cloudChefConfBuilder
            .forInitialBoot(true)
            .fqdn(hostname1)
            .instanceId(instance1.getId())
            .build();
    _cloudChefConfSvc.saveCloudChefConf(cloudChefConf);
    assertFalse(
        _chefCallbackSvc
            .getNodeAction(hostname1, deployment.getGroupId(), null)
            .getConfNonce()
            .isPresent());

    // Test with for initial boot set to false
    cloudChefConf = _cloudChefConfBuilder.forInitialBoot(false).build();
    _cloudChefConfSvc.saveCloudChefConf(cloudChefConf);
    assertTrue(
        _chefCallbackSvc
            .getNodeAction(hostname1, deployment.getGroupId(), null)
            .getConfNonce()
            .isPresent());
  }

  // TODO CLOUDP-295231: Remove deprecated getNonConfigReplicaSetName method
  @SuppressWarnings("deprecation")
  @Test
  public void testGetNodeAction_WithForInitialBoot() throws Exception {

    final String clusterName = "bar";
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, _mmsGroup.getId(), 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getNonConfigReplicaSetName(clusterName, 0),
        true,
        false,
        new ObjectId());
    final ObjectId instanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    final Hostnames hostnames = new Hostnames("bar.example.com");
    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        instanceId,
        false,
        InstanceHardware.FieldDefs.HOSTNAMES,
        hostnames.toDBList());

    final Builder<?> _cloudChefConfBuilder = CloudChefConf.builder();
    _cloudChefConfBuilder.cloudProvider(CloudProvider.AWS.getChefProvider());
    _cloudChefConfBuilder.hostnameSubdomainLevel(InstanceHostname.SubdomainLevel.MONGODB.name());
    _cloudChefConfBuilder.groupId(_mmsGroup.getId().toString());
    _cloudChefConfBuilder.fqdn(hostname);
    _cloudChefConfBuilder.ip("127.0.0.1");
    _cloudChefConfBuilder.clusterName("bar");
    _cloudChefConfBuilder.dataDevice("dataDevice");
    _cloudChefConfBuilder.backupDevice("backupDevice");
    _cloudChefConfBuilder.chefCallbackUrl("url");
    _cloudChefConfBuilder.region("region");
    _cloudChefConfBuilder.nodeName("nodeName");
    _cloudChefConfBuilder.logRetention("logRetention");
    _cloudChefConfBuilder.validDatetime(CloudChefConf.generateValidDatetime());
    _cloudChefConfBuilder.createdAt(new Date());
    _cloudChefConfBuilder.instanceId(new ObjectId());
    _cloudChefConfBuilder.pushBasedLogExportConfig(
        CloudChefConfSvc.getDefaultDisabledLogExportConfig());
    _cloudChefConfBuilder.gatewayRouterBootstrapConfig(
        CloudChefConfSvc.getDefaultDisabledGatewayRouterBootstrapConfig());

    CloudChefConf cloudChefConf = _cloudChefConfBuilder.build();
    _cloudChefConfSvc.saveCloudChefConf(cloudChefConf);

    final Map<String, Object> data = new HashMap<>();
    data.put("a", "b");
    data.put("c", "d");

    // Test with for initial boot not set
    assertTrue(
        _chefCallbackSvc
            .getNodeAction(hostname, _mmsGroup.getId(), null)
            .getConfNonce()
            .isPresent());

    cloudChefConf = _cloudChefConfBuilder.forInitialBoot(true).fqdn("bar.example.com").build();
    _cloudChefConfSvc.saveCloudChefConf(cloudChefConf);

    // Test with for initial boot set to true
    assertFalse(
        _chefCallbackSvc
            .getNodeAction("bar.example.com", _mmsGroup.getId(), null)
            .getConfNonce()
            .isPresent());

    cloudChefConf = _cloudChefConfBuilder.forInitialBoot(false).build();
    _cloudChefConfSvc.saveCloudChefConf(cloudChefConf);

    // Test with for initial boot set to false
    assertTrue(
        _chefCallbackSvc
            .getNodeAction("bar.example.com", _mmsGroup.getId(), null)
            .getConfNonce()
            .isPresent());
  }

  @Test
  public void testUpdateServerStatusWithNoPackageInfo() throws Exception {

    final Map<String, Object> data = new HashMap<>();
    data.put("a", "b");
    data.put("c", "d");

    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);

    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(hostname, status.get().getHostname());
    assertNotNull(status.get().getData());
    assertNull(status.get().getPackageHash());
    assertNull(status.get().getPackageUpdateDate());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testUpdateServerStatusWithPackageInfo() throws Exception {

    final List<String> packages = new ArrayList<>();
    packages.add("centos-release-7-2.1511.el7.centos.2.10.x86_64");
    packages.add("filesystem-3.2-20.el7.x86_64");
    packages.add("gnutls-3.3.8-14.el7_2.x86_64");
    packages.add("libsoup-2.48.1-3.el7.x86_64");

    final Map<String, Object> data = new HashMap<>();
    data.put("name", "cluster124-shard-00-00-iluv4.mmscloudtest.com");
    data.put("heracles_version", "3aa10a7d1bb2a11aeeba7f51ad61cd94fcf759c7");
    data.put("chef_last_successful_run", 1513938607);
    data.put("chef_repo_revision", "87339f3a5b41314acee26445c92b7c5a15f075f9");
    data.put("packages", packages);

    final Date firstSeen = new Date();
    data.put(
        "package_manifests",
        List.of(
            Map.of(
                "type",
                "rpm",
                "packages",
                packages,
                "drift",
                Map.of("added", List.of(), "removed", List.of()),
                "package_hash",
                "hashetyhash",
                "first_seen",
                firstSeen.getTime()),
            // will be ignored b/c no hash
            Map.of(
                "type",
                "gem",
                "packages",
                packages,
                "drift",
                Map.of("added", List.of(), "removed", List.of()),
                "package_hash",
                "",
                "first_seen",
                firstSeen.getTime())));

    final Date updateDate = new Date();
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), updateDate, data);

    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(hostname, status.get().getHostname());
    assertNotNull(status.get().getData());
    assertFalse(status.get().getData().containsKey("packages"));
    assertNotNull(status.get().getPackageHash());

    String packageHash = status.get().getPackageHash();
    Date packageUpdateDate = status.get().getPackageUpdateDate();

    Optional<ChefPackageStatus> packageStatus = _chefPackageStatusDao.findByHash(packageHash);
    assertTrue(packageStatus.isPresent());
    assertEquals(4, packageStatus.get().getPackages().size());

    List<?> packageManifestSummaries = status.get().getPackageManifestSummaries();
    assertNotNull(packageManifestSummaries);
    assertEquals(1, packageManifestSummaries.size());

    // Question:  this line is necessary because _chefCallbackSvc.updateServerStatus modifies
    // `data`. Is this in poor taste?  Should `data` be cloned inside that method?
    data.put("packages", packages);
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);
    status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(packageHash, status.get().getPackageHash());
    assertEquals(packageUpdateDate, status.get().getPackageUpdateDate());

    // resave with new package info
    packages.add("pciutils-libs-3.2.1-4.el7.x86_64");
    data.put("packages", packages);
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);
    status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertNotEquals(packageHash, status.get().getPackageHash());
    assertNotEquals(packageUpdateDate, status.get().getPackageUpdateDate());

    packageStatus = _chefPackageStatusDao.findByHash(status.get().getPackageHash());
    assertTrue(packageStatus.isPresent());
    assertEquals(5, packageStatus.get().getPackages().size());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testUpdateServerStatusWithSystemdService() {
    // Initially, fluent-bit.service is active
    String timestampOne = "2025-03-11T13:59:31Z";
    LogUploaderService logUploaderService =
        new LogUploaderService(
            SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT,
            "active",
            "running",
            timestampOne,
            null);
    SystemdServices systemdServices = new SystemdServices(logUploaderService);
    Map<String, Object> data = new HashMap<>();
    Map<String, Object> systemdServicesMap = _objectMapper.convertValue(systemdServices, Map.class);
    data.put(FieldDefs.SYSTEMD_SERVICES, systemdServicesMap);

    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);

    LogUploaderServiceRecord logUploaderServiceRecord =
        getLogUploaderServiceFromChefServerStatusDoc();
    String activeState = logUploaderServiceRecord.getActiveState();
    Optional<String> nonActiveStateFirstDetected =
        logUploaderServiceRecord.getNonActiveStateFirstDetected();
    assertEquals("active", activeState);
    assertFalse(nonActiveStateFirstDetected.isPresent());

    // First update: fluent-bit.service fails and enters crash-loop; NonActiveStateFirstDetected is
    // set to ActiveExitTimestamp
    String timestampTwo = "2025-03-11T14:59:31Z";
    logUploaderService =
        new LogUploaderService(
            SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT,
            "failed",
            "failed",
            timestampTwo,
            timestampTwo);
    systemdServices = new SystemdServices(logUploaderService);
    systemdServicesMap = _objectMapper.convertValue(systemdServices, Map.class);
    data.put(FieldDefs.SYSTEMD_SERVICES, systemdServicesMap);

    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);
    logUploaderServiceRecord = getLogUploaderServiceFromChefServerStatusDoc();
    activeState = logUploaderServiceRecord.getActiveState();
    nonActiveStateFirstDetected = logUploaderServiceRecord.getNonActiveStateFirstDetected();
    assertEquals("failed", activeState);
    assertEquals(timestampTwo, nonActiveStateFirstDetected.get());

    // Second update: fluent-bit.service is still failing; NonActiveStateFirstDetected is persisted
    String timestampThree = "2025-03-11T15:14:31Z";
    logUploaderService =
        new LogUploaderService(
            SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT,
            "failed",
            "failed",
            timestampThree,
            timestampThree);
    systemdServices = new SystemdServices(logUploaderService);
    systemdServicesMap = _objectMapper.convertValue(systemdServices, Map.class);
    data.put(FieldDefs.SYSTEMD_SERVICES, systemdServicesMap);

    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);
    logUploaderServiceRecord = getLogUploaderServiceFromChefServerStatusDoc();
    activeState = logUploaderServiceRecord.getActiveState();
    nonActiveStateFirstDetected = logUploaderServiceRecord.getNonActiveStateFirstDetected();
    assertEquals("failed", activeState);
    assertEquals(timestampTwo, nonActiveStateFirstDetected.get());

    // Third update: fluent-bit.service becomes active again; NonActiveStateFirstDetected is null
    String timestampFour = "2025-03-11T16:14:31Z";
    logUploaderService =
        new LogUploaderService(
            SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT,
            "active",
            "running",
            timestampFour,
            timestampThree);
    systemdServices = new SystemdServices(logUploaderService);
    systemdServicesMap = _objectMapper.convertValue(systemdServices, Map.class);
    data.put(FieldDefs.SYSTEMD_SERVICES, systemdServicesMap);

    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);
    logUploaderServiceRecord = getLogUploaderServiceFromChefServerStatusDoc();
    activeState = logUploaderServiceRecord.getActiveState();
    nonActiveStateFirstDetected = logUploaderServiceRecord.getNonActiveStateFirstDetected();
    assertEquals("active", activeState);
    assertFalse(nonActiveStateFirstDetected.isPresent());
  }

  @Test
  public void testGetNodeAction_rebootOrDisconnect() throws Exception {

    // test with no external reboot date
    final Data nonceAndRebootFlag1 =
        _chefCallbackSvc.getNodeAction(hostname, _mmsGroup.getId(), null);

    assertFalse(nonceAndRebootFlag1.needsReboot());
    assertFalse(nonceAndRebootFlag1.needsUngracefulDisconnect());

    // test with restore machine
    final Data nonceAndRebootFlag2 =
        _chefCallbackSvc.getNodeAction(
            String.format("restore-%s-abcde.mmscloudtest.com", new ObjectId()),
            _mmsGroup.getId(),
            null);

    assertFalse(nonceAndRebootFlag2.needsReboot());
    assertFalse(nonceAndRebootFlag1.needsUngracefulDisconnect());

    // test with validation machine
    final Data nonceAndRebootFlag3 =
        _chefCallbackSvc.getNodeAction(
            String.format("validation-%s-abcde.mmscloudtest.com", new ObjectId()),
            _mmsGroup.getId(),
            null);

    assertFalse(nonceAndRebootFlag3.needsReboot());
    assertFalse(nonceAndRebootFlag1.needsUngracefulDisconnect());

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(_clusterName, _mmsGroup.getId(), 0);
    final Date rebootDate = new Date();

    // test with an external reboot date but no uptime
    _replicaSetHardwareDao.setExternalRequestedRebootDate(
        replicaSetHardwareId, _instanceId, false, rebootDate);

    final Data nonceAndRebootFlag4 =
        _chefCallbackSvc.getNodeAction(hostname, _mmsGroup.getId(), 0L);

    assertFalse(nonceAndRebootFlag4.needsReboot());

    // check that external reboot requested date has been cleared
    Optional<ReplicaSetHardware> replicaSetHardwareOptional =
        _replicaSetHardwareDao.findById(replicaSetHardwareId);
    assertTrue(replicaSetHardwareOptional.isPresent());
    ReplicaSetHardware replicaSetHardware = replicaSetHardwareOptional.get();
    Optional<InstanceHardware> instanceHardwareOptional =
        replicaSetHardware.getByHostname(hostname);
    assertTrue(instanceHardwareOptional.isPresent());
    InstanceHardware instanceHardware = instanceHardwareOptional.get();
    assertTrue(instanceHardware.getExternalRebootRequestedDate().isEmpty());

    // test with an external reboot date and plenty of uptime
    _replicaSetHardwareDao.setExternalRequestedRebootDate(
        replicaSetHardwareId, _instanceId, false, rebootDate);

    final Data nonceAndRebootFlag5 =
        _chefCallbackSvc.getNodeAction(hostname, _mmsGroup.getId(), 1000L);

    assertTrue(nonceAndRebootFlag5.needsReboot());

    // set needsUngracefulDisconnect
    _replicaSetHardwareDao.setNeedsUngracefulDisconnectDate(
        replicaSetHardwareId, _instanceId, false, new Date());

    final Data nonceAndRebootFlag6 =
        _chefCallbackSvc.getNodeAction(hostname, _mmsGroup.getId(), 1000L);
    assertTrue(nonceAndRebootFlag6.needsUngracefulDisconnect());
  }

  @Test
  public void testUpdateServeStatus_doNotOverrideLastChefRunWithNull() {
    final Map<String, Object> data = new HashMap<>();
    final Integer lastSuccessfulChefRun = **********;
    final String hostname = ChefCallbackSvcIntTests.hostname;
    data.put(FieldDefs.DATA_CHEF_LAST_SUCCESFUL_RUN, lastSuccessfulChefRun);

    // Last successful chef run is non-null. Date gets updated.
    _chefServerStatusSvc.createOrUpdate(
        hostname,
        _mmsGroup.getId(),
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        data);

    final Optional<ChefServerStatus> chefServerStatus_dateUpdated =
        _chefServerStatusDao.findByHostname(hostname);
    assertTrue(chefServerStatus_dateUpdated.isPresent());

    final Integer lastSuccessfulChefRunFromData_dateUpdated =
        (Integer)
            chefServerStatus_dateUpdated
                .get()
                .getData()
                .get(FieldDefs.DATA_CHEF_LAST_SUCCESFUL_RUN);
    assertEquals(lastSuccessfulChefRunFromData_dateUpdated, lastSuccessfulChefRun);

    // Last successful chef run is null. Date does not get updated.
    data.put(FieldDefs.DATA_CHEF_LAST_SUCCESFUL_RUN, null);
    _chefServerStatusSvc.createOrUpdate(
        hostname,
        _mmsGroup.getId(),
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        data);
    final Optional<ChefServerStatus> chefServerStatus_dateNotUpdated =
        _chefServerStatusDao.findByHostname(hostname);
    assertTrue(chefServerStatus_dateNotUpdated.isPresent());

    final Integer lastSuccessfulChefRunFromData_dateNotUpdated =
        (Integer)
            chefServerStatus_dateNotUpdated
                .get()
                .getData()
                .get(FieldDefs.DATA_CHEF_LAST_SUCCESFUL_RUN);
    assertEquals(
        lastSuccessfulChefRunFromData_dateUpdated, lastSuccessfulChefRunFromData_dateNotUpdated);

    // Last successful chef run is null on update and original chef server status object. Date
    // remains null.
    data.put(FieldDefs.DATA_CHEF_LAST_SUCCESFUL_RUN, null);
    final String hostname_nullChefLastRunDate = "foo.example.null";
    _chefServerStatusSvc.createOrUpdate(
        hostname_nullChefLastRunDate,
        _mmsGroup.getId(),
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        data);
    final Optional<ChefServerStatus> chefServerStatus_dateNull =
        _chefServerStatusDao.findByHostname(hostname_nullChefLastRunDate);
    assertTrue(chefServerStatus_dateNull.isPresent());
    assertNull(
        chefServerStatus_dateNull.get().getData().get(FieldDefs.DATA_CHEF_LAST_SUCCESFUL_RUN));
  }

  @Test
  public void testFindStatusesWithDeflatedBumperFiles() {
    final ObjectId groupId = new ObjectId();
    final String hostname1 = "hostname1";
    final String hostname2 = "hostname2";
    final String hostname3 = "hostname3";
    final String hostname4 = "hostname4";
    final String hostname5 = "hostname5";
    _chefServerStatusDao.createOrUpdate(
        hostname1,
        groupId,
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        null);

    final Map<String, Object> data2 = new HashMap<>();
    _chefServerStatusDao.createOrUpdate(
        hostname2,
        groupId,
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        data2);

    final Map<String, Object> data3 = new HashMap<>();
    data3.put(FieldDefs.DATA_CURR_NUM_BUMPER_FILES, null);
    _chefServerStatusDao.createOrUpdate(
        hostname3,
        groupId,
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        data3);

    final Map<String, Object> data4 = new HashMap<>();
    data4.put(FieldDefs.DATA_CURR_NUM_BUMPER_FILES, 3);
    _chefServerStatusDao.createOrUpdate(
        hostname4,
        groupId,
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        data4);

    final Map<String, Object> data5 = new HashMap<>();
    data5.put(FieldDefs.DATA_CURR_NUM_BUMPER_FILES, 2);
    _chefServerStatusDao.createOrUpdate(
        hostname5,
        groupId,
        new Date(),
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        data5);

    final List<ChefServerStatus> result1 =
        _chefServerStatusSvc.findStatusesWithDeflatedBumperFiles();
    assertEquals(1, result1.size());
    assertThat(
        result1.stream().map(ChefServerStatus::getHostname).collect(Collectors.toList()),
        contains(hostname5));
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testFindStatusesWithLogUploaderServiceDown() {
    final ObjectId groupId = new ObjectId();
    final String hostname1 = "hostname1";
    final String hostname2 = "hostname2";
    final String hostname3 = "hostname3";
    final String hostname4 = "hostname4";
    final String hostname5 = "hostname5";
    final String hostname6 = "hostname6";
    final String hostname7 = "hostname7";
    final String timestamp = "2025-03-11T13:59:31Z";

    // No systemd services data
    createChefServerStatus(hostname1, groupId, null, null, null);

    // Empty data
    createChefServerStatus(hostname2, groupId, null, null, null, new HashMap<>(), new Date());

    // Active log uploader service - should not be included in results
    createChefServerStatus(hostname3, groupId, "active", "running", null);

    // Inactive log uploader service with non_active_state_first_detected set - should be included
    createChefServerStatus(hostname4, groupId, "inactive", "dead", timestamp);

    // Failed log uploader service with non_active_state_first_detected set - should be included
    createChefServerStatus(hostname5, groupId, "failed", "failed", timestamp);

    // Failed log uploader service with non_active_state_first_detected but older than 6 hours -
    // should NOT be included
    Date oldDate = DateUtils.addHours(new Date(), -7);
    createChefServerStatus(hostname6, groupId, "failed", "failed", timestamp, null, oldDate);

    // Failed log uploader service with NULL non_active_state_first_detected - should NOT be
    // included
    createChefServerStatus(hostname7, groupId, "failed", "failed", null);

    final List<ChefServerStatus> result1 =
        _chefServerStatusSvc.findStatusesWithInactiveLogUploaderService();
    assertEquals(2, result1.size());
    assertThat(
        result1.stream().map(ChefServerStatus::getHostname).collect(Collectors.toList()),
        containsInAnyOrder(hostname4, hostname5));
  }

  private void createChefServerStatus(
      final String hostname,
      final ObjectId groupId,
      final String activeState,
      final String subState,
      final String nonActiveStateFirstDetected) {
    createChefServerStatus(
        hostname, groupId, activeState, subState, nonActiveStateFirstDetected, null, new Date());
  }

  private void createChefServerStatus(
      final String hostname,
      final ObjectId groupId,
      final String activeState,
      final String subState,
      final String nonActiveStateFirstDetected,
      final Map<String, Object> additionalData,
      final Date lastUpdateDate) {

    final Map<String, Object> data = additionalData != null ? additionalData : new HashMap<>();

    if (activeState != null && subState != null) {
      final String timestamp = "2025-03-11T13:59:31Z";
      final LogUploaderService logUploaderService =
          new LogUploaderService(
              SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT,
              activeState,
              subState,
              timestamp,
              timestamp);

      if (nonActiveStateFirstDetected != null) {
        logUploaderService.setNonActiveStateFirstDetected(nonActiveStateFirstDetected);
      }

      final SystemdServices systemdServices = new SystemdServices(logUploaderService);
      final Map<String, Object> systemdServicesMap = systemdServices.toMap();
      data.put(FieldDefs.SYSTEMD_SERVICES, systemdServicesMap);
    }

    _chefServerStatusDao.createOrUpdate(
        hostname,
        groupId,
        lastUpdateDate,
        "packageHash",
        new Date(),
        List.of(),
        new Date(),
        List.of(),
        data);
  }

  @SuppressWarnings("unchecked")
  private LogUploaderServiceRecord getLogUploaderServiceFromChefServerStatusDoc() {
    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());

    Optional<LogUploaderServiceRecord> logUploaderServiceRecord =
        status
            .map(ChefServerStatus::getData)
            .map(
                data ->
                    _objectMapper.convertValue(
                        data.get(FieldDefs.SYSTEMD_SERVICES), SystemdServicesRecord.class))
            .map(SystemdServicesRecord::getLogUploaderServiceRecord);

    return logUploaderServiceRecord.get();
  }

  @Test
  public void testUpdateServerStatus_withNullSystemdServicesMap() {
    // Test that updateServerStatus handles null systemd_services gracefully
    Map<String, Object> data = new HashMap<>();
    data.put("uptime", 12345L);
    data.put("packages", List.of("package1", "package2"));
    // Explicitly set systemd_services to null
    data.put(FieldDefs.SYSTEMD_SERVICES, null);

    // This should not throw an exception
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);

    // Verify the server status was created successfully
    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(hostname, status.get().getHostname());
  }

  @Test
  public void testUpdateServerStatus_withMissingSystemdServicesMap() {
    // Test that updateServerStatus handles missing systemd_services gracefully
    Map<String, Object> data = new HashMap<>();
    data.put("uptime", 12345L);
    data.put("packages", List.of("package1", "package2"));
    // Don't include systemd_services at all

    // This should not throw an exception
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);

    // Verify the server status was created successfully
    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(hostname, status.get().getHostname());
  }

  @Test
  public void testUpdateServerStatus_withEmptySystemdServicesMap() {
    // Test that updateServerStatus handles empty systemd_services map gracefully
    Map<String, Object> data = new HashMap<>();
    data.put("uptime", 12345L);
    data.put("packages", List.of("package1", "package2"));
    data.put(FieldDefs.SYSTEMD_SERVICES, new HashMap<>());

    // This should not throw an exception
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);

    // Verify the server status was created successfully
    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(hostname, status.get().getHostname());
  }

  @Test
  public void testUpdateServerStatus_withSystemdServicesMapContainingNullLogUploader() {
    // Test that updateServerStatus handles systemd_services with null log uploader service
    Map<String, Object> data = new HashMap<>();
    data.put("uptime", 12345L);
    data.put("packages", List.of("package1", "package2"));

    // Create systemd services map with null fluent-bit service
    Map<String, Object> systemdServicesMap = new HashMap<>();
    systemdServicesMap.put(SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT, null);
    data.put(FieldDefs.SYSTEMD_SERVICES, systemdServicesMap);

    // This should not throw an exception
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);

    // Verify the server status was created successfully
    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(hostname, status.get().getHostname());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testUpdateServerStatus_withValidFluentBitService() {
    // Test that updateServerStatus properly handles valid fluent-bit.service data
    Map<String, Object> data = new HashMap<>();
    data.put("uptime", 12345L);
    data.put("packages", List.of("package1", "package2"));

    // Create fluent-bit service with all standard fields populated
    LogUploaderService logUploaderService =
        new LogUploaderService(
            SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT,
            "active",
            "running",
            "2025-03-11T13:59:31Z",
            "2025-03-11T14:00:00Z");
    SystemdServices systemdServices = new SystemdServices(logUploaderService);
    Map<String, Object> systemdServicesMap = _objectMapper.convertValue(systemdServices, Map.class);
    data.put(FieldDefs.SYSTEMD_SERVICES, systemdServicesMap);

    // This should not throw an exception
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);

    // Verify the server status was created successfully
    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(hostname, status.get().getHostname());

    // Verify fluent-bit service data is properly stored
    LogUploaderServiceRecord logUploaderServiceRecord =
        getLogUploaderServiceFromChefServerStatusDoc();
    assertEquals("active", logUploaderServiceRecord.getActiveState());
    assertEquals("2025-03-11T13:59:31Z", logUploaderServiceRecord.getActiveEnterTimestamp());
    assertEquals("2025-03-11T14:00:00Z", logUploaderServiceRecord.getActiveExitTimestamp());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testUpdateServerStatus_withFluentBitServiceAndExtraFields() {
    // Test that updateServerStatus ignores unknown fields in fluent-bit.service data
    Map<String, Object> data = new HashMap<>();
    data.put("uptime", 12345L);
    data.put("packages", List.of("package1", "package2"));

    // Create systemd services map with fluent-bit service plus extra unknown fields
    Map<String, Object> systemdServicesMap = new HashMap<>();
    Map<String, Object> fluentBitServiceMap = new HashMap<>();
    fluentBitServiceMap.put("Id", SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT);
    fluentBitServiceMap.put("ActiveState", "active");
    fluentBitServiceMap.put("SubState", "running");
    fluentBitServiceMap.put("ActiveEnterTimestamp", "2025-03-11T13:59:31Z");
    fluentBitServiceMap.put("ActiveExitTimestamp", "2025-03-11T14:00:00Z");
    // Add unknown fields that should be ignored
    fluentBitServiceMap.put("UnknownField1", "should-be-ignored");
    fluentBitServiceMap.put("ExtraProperty", 12345);
    fluentBitServiceMap.put("AnotherUnknownField", true);

    systemdServicesMap.put(
        SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT, fluentBitServiceMap);
    data.put(FieldDefs.SYSTEMD_SERVICES, systemdServicesMap);

    // This should not throw an exception
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);

    // Verify the server status was created successfully
    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(hostname, status.get().getHostname());

    // Verify only known fluent-bit service fields are stored, unknown fields are ignored
    LogUploaderServiceRecord logUploaderServiceRecord =
        getLogUploaderServiceFromChefServerStatusDoc();
    assertEquals("active", logUploaderServiceRecord.getActiveState());
    assertEquals("2025-03-11T13:59:31Z", logUploaderServiceRecord.getActiveEnterTimestamp());
    assertEquals("2025-03-11T14:00:00Z", logUploaderServiceRecord.getActiveExitTimestamp());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testUpdateServerStatus_withFluentBitAndFilebeatServices() {
    // Test that updateServerStatus processes fluent-bit.service and ignores filebeat.service
    Map<String, Object> data = new HashMap<>();
    data.put("uptime", 12345L);
    data.put("packages", List.of("package1", "package2"));

    // Create systemd services map with both fluent-bit and filebeat services
    Map<String, Object> systemdServicesMap = new HashMap<>();

    // Add fluent-bit service (should be processed)
    Map<String, Object> fluentBitServiceMap = new HashMap<>();
    fluentBitServiceMap.put("Id", SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT);
    fluentBitServiceMap.put("ActiveState", "active");
    fluentBitServiceMap.put("SubState", "running");
    fluentBitServiceMap.put("ActiveEnterTimestamp", "2025-03-11T13:59:31Z");
    fluentBitServiceMap.put("ActiveExitTimestamp", "2025-03-11T14:00:00Z");
    systemdServicesMap.put(
        SystemdServices.FieldDefs.LOG_UPLOADER_SERVICE_FLUENTBIT, fluentBitServiceMap);

    // Add filebeat service (should be ignored due to migration)
    Map<String, Object> filebeatServiceMap = new HashMap<>();
    filebeatServiceMap.put("Id", "filebeat.service");
    filebeatServiceMap.put("ActiveState", "inactive");
    filebeatServiceMap.put("SubState", "dead");
    filebeatServiceMap.put("ActiveEnterTimestamp", "2025-03-11T12:00:00Z");
    filebeatServiceMap.put("ActiveExitTimestamp", "2025-03-11T12:30:00Z");
    systemdServicesMap.put("filebeat.service", filebeatServiceMap);

    data.put(FieldDefs.SYSTEMD_SERVICES, systemdServicesMap);

    // This should not throw an exception
    _chefCallbackSvc.updateServerStatus(hostname, _mmsGroup.getId(), new Date(), data);

    // Verify the server status was created successfully
    Optional<ChefServerStatus> status = _chefServerStatusDao.findByHostname(hostname);
    assertTrue(status.isPresent());
    assertEquals(hostname, status.get().getHostname());

    // Verify only fluent-bit service data is stored, filebeat service is ignored
    LogUploaderServiceRecord logUploaderServiceRecord =
        getLogUploaderServiceFromChefServerStatusDoc();
    assertEquals("active", logUploaderServiceRecord.getActiveState());
    assertEquals("2025-03-11T13:59:31Z", logUploaderServiceRecord.getActiveEnterTimestamp());
    assertEquals("2025-03-11T14:00:00Z", logUploaderServiceRecord.getActiveExitTimestamp());

    // Verify that the stored systemd services data only contains fluent-bit, not filebeat
    Map<String, Object> storedData = status.get().getData();
    assertNotNull(storedData.get(FieldDefs.SYSTEMD_SERVICES));
    SystemdServicesRecord systemdServicesRecord =
        _objectMapper.convertValue(
            storedData.get(FieldDefs.SYSTEMD_SERVICES), SystemdServicesRecord.class);
    assertNotNull(systemdServicesRecord.getLogUploaderServiceRecord());
    // The stored record should only contain fluent-bit data, filebeat should be ignored
    // We can verify this by checking that the service data matches our fluent-bit expectations
    assertEquals("active", systemdServicesRecord.getLogUploaderServiceRecord().getActiveState());
  }
}
