package com.xgen.svc.nds.svc.onlinearchive;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.email._public.model.EmailMsg;
import com.xgen.cloud.email._public.svc.EmailSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureNDSDefaults;
import com.xgen.cloud.nds.azure._public.model.ui.NDSAzureTempCredentialsView;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.Limits.FieldDefs;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataLakeTenantDao;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataSetDao;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSet;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSetOA;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveHistoryDao;
import com.xgen.cloud.nds.onlinearchive._public.model.ArchiveRunStats;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.ArchiveRun;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.ArchiveRunState;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DateCriteria.DateFormat;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.State;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveHistory;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveRun;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.onlinearchiveagentview.OnlineArchiveConfRequestView.ArchiveResolution;
import com.xgen.svc.nds.model.onlinearchiveagentview.OnlineArchiveRunInfoView;
import com.xgen.svc.nds.model.ui.ArchiveRunView;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.svc.nds.svc.NDSDataLakeTenantSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class OnlineArchiveSvcIntTests extends JUnit5BaseSvcTest {
  @Inject public NDSGroupDao _ndsGroupDao;
  @Inject public NDSGroupSvc _ndsGroupSvc;
  @Inject public NDSDataLakeTenantDao _tenantDao;
  @Inject public OnlineArchiveSvc _onlineArchiveSvc;
  @Inject public OnlineArchiveHistoryDao _onlineArchiveHistoryDao;
  @Inject public OnlineArchiveDataLakeConfigSvc _onlineArchiveDataLakeConfigSvc;
  @Inject public ClusterDescriptionDao _clusterDescriptionDao;
  @Inject public NDSDataLakeTenantSvc _ndsDataLakeTenantSvc;
  @Inject public AuditSvc _auditSvc;

  @Inject public GroupSvc _groupSvc;
  @Inject public OnlineArchiveDao _onlineArchiveDao;
  @Inject public AppSettings _appSettings;
  @Inject private EmailSvc _emailSvc;
  @Inject public DataLakeTestUtils _dataLakeTestUtils;
  @Inject public OnlineArchiveTestUtils _onlineArchiveTestUtils;
  @Inject public NDSDataSetDao _ndsDataSetDao;

  private NDSDataSetDao _originalNdsDataSetDao;

  protected Organization _organization;
  protected ObjectId _groupId;
  protected Group _group;

  @BeforeEach
  public void setup() throws Exception {
    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_organization, "cus_0001");
    _groupId = _group.getId();
    _ndsGroupSvc.create(_groupId, new NDSManagedX509(), false);
    _dataLakeTestUtils.setUp();
    _onlineArchiveTestUtils.setUpAzureTestStorageAccount(_group);
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);
    ClassModifier.modifyInstanceValue(_onlineArchiveSvc, "_azureApiSvc", azureApiSvc);
    final NDSAzureTempCredentialsView credentialsView = mock(NDSAzureTempCredentialsView.class);
    doReturn(credentialsView).when(azureApiSvc).generateStorageAccountSasToken(any(), any(), any());
    _originalNdsDataSetDao = _ndsDataSetDao; // Save original
  }

  @Test
  public void testCreateV3_CrossCloudArchiveCreation() throws SvcException {
    // Create a GCP Cluster and enable GCP support
    _onlineArchiveTestUtils.createCluster(_groupId, CloudProvider.GCP);
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ONLINE_ARCHIVE_GCP);

    // Attempt to create AWS archive without cross-cloud creation flag => should fail
    final OnlineArchive awsArchive =
        _onlineArchiveTestUtils
            .getOnlineArchive(_groupId, CloudProvider.AWS, OnlineArchiveVersion.V3)
            .copy()
            .setClusterId(_groupId, OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME)
            .setCollectionUUID(UUID.randomUUID())
            .build();
    try {
      _onlineArchiveSvc.create(awsArchive, AuditInfoHelpers.fromSystem());
      fail();
    } catch (SvcException pE) {
      assertEquals(
          NDSErrorCode.ONLINE_ARCHIVE_DATA_PROCESS_REGION_CLUSTER_CLOUD_PROVIDER_MISMATCH,
          pE.getErrorCode());
    }

    // Enable cross-cloud creation and try again => should succeed
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ONLINE_ARCHIVE_CROSS_CLOUD_CREATION);

    _onlineArchiveSvc.create(awsArchive, AuditInfoHelpers.fromSystem());

    // For V3 archives, we need to merge archive resolution which triggers proper activation
    final ArchiveResolution resolution =
        ArchiveResolution.builder()
            .collectionUUID(awsArchive.getCollectionUUID().get())
            .partitionFieldTypes(
                Map.of("field1", "string", "field2", "string", "dateField", "date"))
            .build();
    _onlineArchiveSvc.mergeArchiveResolution(awsArchive.getId(), resolution);

    // Verify archive has expected data process region (provider/region)
    final OnlineArchive createdAwsArchive =
        _onlineArchiveSvc.getOnlineArchive(awsArchive.getId()).get();
    assertTrue(createdAwsArchive.getDataProcessRegion().isPresent());
    assertEquals(
        CloudProvider.AWS.name(),
        createdAwsArchive.getDataProcessRegion().get().getCloudProvider());
    assertEquals(
        AWSRegionName.US_EAST_1.getName(),
        createdAwsArchive.getDataProcessRegion().get().getRegion());

    // Verify DLZ dataset is created/enabled in AWS for the archive dataset name
    final String awsDataSetName =
        OnlineArchiveDataLakeConfigSvc.getDataSetNameV3(createdAwsArchive);
    verifyArchiveCreated(createdAwsArchive, _group, CloudProvider.AWS, awsDataSetName);

    // Create a second archive on the same cross-cloud provider (AWS) and ensure it's allowed
    final OnlineArchive awsArchive2 =
        new OnlineArchive.Builder(awsArchive.toDBObject())
            .setArchiveId(ObjectId.get())
            .setCollName("mycoll2")
            .setCollectionUUID(UUID.randomUUID())
            .build();
    _onlineArchiveSvc.create(awsArchive2, AuditInfoHelpers.fromSystem());

    // For V3 archives, we need to merge archive resolution which triggers proper activation
    final ArchiveResolution resolution2 =
        ArchiveResolution.builder()
            .collectionUUID(awsArchive2.getCollectionUUID().get())
            .partitionFieldTypes(
                Map.of("field1", "string", "field2", "string", "dateField", "date"))
            .build();
    _onlineArchiveSvc.mergeArchiveResolution(awsArchive2.getId(), resolution2);

    final OnlineArchive createdAwsArchive2 =
        _onlineArchiveSvc.getOnlineArchive(awsArchive2.getId()).get();
    final String awsDataSetName2 =
        OnlineArchiveDataLakeConfigSvc.getDataSetNameV3(createdAwsArchive2);
    verifyArchiveCreated(createdAwsArchive2, _group, CloudProvider.AWS, awsDataSetName2);

    // Creating an Azure archive should fail since an AWS archive already exists for GCP cluster.
    try {
      final OnlineArchive azureOnlineArchive =
          _onlineArchiveTestUtils
              .getOnlineArchive(_groupId, CloudProvider.AZURE, OnlineArchiveVersion.V3)
              .copy()
              .setClusterId(_groupId, OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME)
              .setCollectionUUID(UUID.randomUUID())
              .build();
      _onlineArchiveSvc.create(azureOnlineArchive, AuditInfoHelpers.fromSystem());
      fail();
    } catch (SvcException pE) {
      assertEquals(
          NDSErrorCode.ONLINE_ARCHIVE_DATA_PROCESS_REGION_CLUSTER_CLOUD_PROVIDER_MISMATCH,
          pE.getErrorCode());
    }
  }

  @AfterEach
  public void teardown() throws Exception {
    _dataLakeTestUtils.teardown();
    if (_originalNdsDataSetDao != null) {
      ClassModifier.modifyInstanceValue(
          _onlineArchiveSvc, "_ndsDataSetDao", _originalNdsDataSetDao);
    }
  }

  @Test
  public void testCreateV3_CrossCloudProvider() throws SvcException {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ONLINE_ARCHIVE_CROSS_CLOUD_CREATION);
    {
      // Create a GCP Cluster with GCP feature flag disabled.
      _onlineArchiveTestUtils.createCluster(_groupId, CloudProvider.GCP);
      // Create the AWS Archive for the GCP cluster with feature flag disabled.
      final OnlineArchive awsOnlineArchive =
          _onlineArchiveTestUtils
              .getOnlineArchive(_groupId, CloudProvider.AWS, OnlineArchiveVersion.V3)
              .copy()
              .setClusterId(_groupId, OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME)
              .build();
      _onlineArchiveSvc.create(awsOnlineArchive, AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.activate(awsOnlineArchive.getId(), AuditInfoHelpers.fromSystem());

      // Enable the feature flag.
      FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ONLINE_ARCHIVE_GCP);

      // Creating a GCP archive will fail since an AWS archive already exists for this cluster.
      try {
        final OnlineArchive gcpOnlineArchive =
            _onlineArchiveTestUtils
                .getOnlineArchive(_groupId, CloudProvider.GCP, OnlineArchiveVersion.V3)
                .copy()
                .setClusterId(_groupId, OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME)
                .build();
        _onlineArchiveSvc.create(gcpOnlineArchive, AuditInfoHelpers.fromSystem());
        fail();
      } catch (SvcException pE) {
        assertEquals(
            NDSErrorCode.ONLINE_ARCHIVE_DATA_PROCESS_REGION_CLUSTER_CLOUD_PROVIDER_MISMATCH,
            pE.getErrorCode());
      }
    }

    {
      // Create an azure archive on an azure cluster.
      final String azureTestCluster1Name = OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME + "1";
      _onlineArchiveTestUtils.createCluster(_groupId, azureTestCluster1Name, CloudProvider.AZURE);
      final OnlineArchive azureOnlineArchive1 =
          _onlineArchiveTestUtils
              .getOnlineArchive(_groupId, CloudProvider.AZURE, OnlineArchiveVersion.V3)
              .copy()
              .setClusterId(_groupId, azureTestCluster1Name)
              .build();
      _onlineArchiveSvc.create(azureOnlineArchive1, AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.activate(azureOnlineArchive1.getId(), AuditInfoHelpers.fromSystem());

      // Creating an AWS archive should fail since an Azure archive already exists, even with
      // cross-cloud enabled.
      try {
        final OnlineArchive awsOnlineArchive1 =
            _onlineArchiveTestUtils
                .getOnlineArchive(_groupId, CloudProvider.AWS, OnlineArchiveVersion.V3)
                .copy()
                .setClusterId(_groupId, azureTestCluster1Name)
                .build();
        _onlineArchiveSvc.create(awsOnlineArchive1, AuditInfoHelpers.fromSystem());
        fail();
      } catch (SvcException pE) {
        assertEquals(
            pE.getErrorCode(),
            NDSErrorCode.ONLINE_ARCHIVE_DATA_PROCESS_REGION_CLUSTER_CLOUD_PROVIDER_MISMATCH);
      }
    }
  }

  @ParameterizedTest
  @MethodSource
  public void testGetDLZSupportedRegionsForArchive(
      CloudProvider clusterProvider, RegionName expectedPreferredRegionName) throws SvcException {

    final String testClusterName = OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME;
    _onlineArchiveTestUtils.createCluster(_groupId, testClusterName, clusterProvider);

    // Always enable GCP; multi-provider flag OFF to verify primary-only behavior
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ONLINE_ARCHIVE_GCP);
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, null, FeatureFlag.ONLINE_ARCHIVE_CROSS_CLOUD_CREATION);

    List<RegionView> regions =
        _onlineArchiveSvc.getDLZSupportedRegionsForArchive(_groupId, testClusterName);

    for (RegionView region : regions) {
      assertEquals(region.getProvider(), clusterProvider.toString());
      if (region.getName().equals(expectedPreferredRegionName.getValue())) {
        assertTrue(region.getName(), region.isRecommended());
      }
    }
  }

  static Stream<Arguments> testGetDLZSupportedRegionsForArchive() {
    final List<Arguments> args = new ArrayList<>();
    args.add(Arguments.of(CloudProvider.GCP, GCPNDSDefaults.REGION_NAME));
    args.add(Arguments.of(CloudProvider.AWS, AWSNDSDefaults.REGION_NAME));
    args.add(Arguments.of(CloudProvider.AZURE, AzureNDSDefaults.REGION_NAME));
    return args.stream();
  }

  @Test
  public void testGetDLZSupportedRegionsForArchive_CrossCloud() throws SvcException {
    final String testClusterName = OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME;
    _onlineArchiveTestUtils.createCluster(_groupId, testClusterName, CloudProvider.AWS);

    // Enable all relevant flags
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ONLINE_ARCHIVE_GCP);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ONLINE_ARCHIVE_CROSS_CLOUD_CREATION);

    List<RegionView> regions =
        _onlineArchiveSvc.getDLZSupportedRegionsForArchive(_groupId, testClusterName);

    assertFalse(regions.isEmpty());
    // First region must be from the cluster provider
    assertEquals(CloudProvider.AWS.toString(), regions.get(0).getProvider());

    // Should contain regions from all dedicated providers
    final Set<String> providers =
        regions.stream().map(RegionView::getProvider).collect(Collectors.toSet());
    assertTrue(providers.contains(CloudProvider.AWS.toString()));
    assertTrue(providers.contains(CloudProvider.AZURE.toString()));
    assertTrue(providers.contains(CloudProvider.GCP.toString()));

    // Only primary provider default region should be recommended
    List<RegionView> recommended =
        regions.stream().filter(RegionView::isRecommended).collect(Collectors.toList());

    assertEquals(1, recommended.size());
    assertEquals(CloudProvider.AWS.toString(), recommended.get(0).getProvider());
  }

  @Test
  public void testEnsureV3DataProcessingRegionDefaultRegionForGCP() {
    final OnlineArchive onlineArchive =
        _onlineArchiveTestUtils
            .getOnlineArchive(_group.getId(), CloudProvider.GCP, OnlineArchiveVersion.V3)
            .copy()
            .setDataProcessRegion(null)
            .build();

    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(
            _group.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME, CloudProvider.GCP);
    _clusterDescriptionDao.save(clusterDescription);

    // default to aws supported region for GCP cluster when ONLINE_ARCHIVE_GCP is disabled
    try {
      final OnlineArchive updatedOnlineArchive =
          _onlineArchiveSvc.ensureV3DataProcessRegion(clusterDescription, onlineArchive);

      assertEquals(
          updatedOnlineArchive.getDataProcessRegion().get().getRegion(),
          AWSRegionName.US_EAST_1.getName());
      assertEquals(
          updatedOnlineArchive.getDataProcessRegion().get().getCloudProvider(),
          CloudProvider.AWS.name());
    } catch (final SvcException pE) {
      fail(pE.getMessage());
    }

    try {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, _organization, FeatureFlag.ONLINE_ARCHIVE_GCP);
      final OnlineArchive gcpOnlineArchive =
          _onlineArchiveSvc.ensureV3DataProcessRegion(clusterDescription, onlineArchive);
      assertEquals(
          gcpOnlineArchive.getDataProcessRegion().get().getRegion(),
          GCPNDSDefaults.REGION_NAME.getName());
      assertEquals(
          gcpOnlineArchive.getDataProcessRegion().get().getCloudProvider(),
          CloudProvider.GCP.name());
    } catch (final SvcException pE) {
      fail(pE.getMessage());
    }
  }

  /////////////////// TEST UTILS FOR OnlineArchiveSvc(AWS|Azure)IntTests \\\\\\\\\\\\\\\\\\\

  void testMarkForPaused(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    // Create OnlineArchives
    final OnlineArchive onlineArchive =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.setPauseRequested(onlineArchive.getId(), AuditInfoHelpers.fromSystem());

    // test pause completed
    _onlineArchiveSvc.markPaused(onlineArchive.getId());
    final OnlineArchive pauseCompletedArchive =
        _onlineArchiveSvc.getOnlineArchive(onlineArchive.getId()).get();
    assertEquals(State.PAUSED, pauseCompletedArchive.getState());
  }

  void testMarkForDeletion(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    // Create OnlineArchives
    final OnlineArchive onlineArchive1 =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);
    final OnlineArchive onlineArchive2 =
        new OnlineArchive.Builder(onlineArchive1.toDBObject())
            .setArchiveId(ObjectId.get())
            .setDbName(onlineArchive1.getDbName() + "1")
            .build();

    _onlineArchiveSvc.create(onlineArchive1, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.create(onlineArchive2, AuditInfoHelpers.fromSystem());

    // Should create the OnlineArchives
    final List<OnlineArchive> onlineArchivesAfterCreate =
        _onlineArchiveSvc.getArchivesForCluster(
            pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME);
    assertEquals(2, onlineArchivesAfterCreate.size());
    onlineArchivesAfterCreate.stream()
        .forEach(
            onlineArchive -> {
              assertEquals(OnlineArchive.State.PENDING, onlineArchive.getState());
              assertNull(onlineArchive.getDeleteAfterDate());
            });

    final Date deletionDate = new Date();

    // Mark first archive for deletion
    // Should set archive state to DELETED
    // Should set archive delete-after date to five days in future
    _onlineArchiveSvc.markForDeletion(
        onlineArchive1.getId(), AuditInfoHelpers.fromSystem(), pGroup.getId());
    final Optional<OnlineArchive> onlineArchiveOpt1 =
        _onlineArchiveSvc.getOnlineArchive(onlineArchive1.getId());

    assertTrue(onlineArchiveOpt1.isPresent());
    assertEquals(OnlineArchive.State.DELETED, onlineArchiveOpt1.get().getState());
    assertNotNull(onlineArchiveOpt1.get().getDeleteAfterDate());
    assertEquals(
        5,
        ChronoUnit.DAYS.between(
            deletionDate.toInstant(), onlineArchiveOpt1.get().getDeleteAfterDate().toInstant()));

    // verify audit
    final List<Event> audits =
        _auditSvc.findByDate(NDSAudit.Type.ONLINE_ARCHIVE_DELETED, Date.from(Instant.EPOCH));
    assertEquals(1, audits.size());

    // Mark second archive for deletion
    // Should set archive state to DELETED
    // Should set archive delete-after date to five days in future
    _onlineArchiveSvc.markForDeletion(
        onlineArchivesAfterCreate.get(1).getId(), AuditInfoHelpers.fromSystem(), pGroup.getId());
    final Optional<OnlineArchive> onlineArchiveOpt2 =
        _onlineArchiveSvc.getOnlineArchive(onlineArchivesAfterCreate.get(1).getId());

    assertTrue(onlineArchiveOpt2.isPresent());
    assertEquals(OnlineArchive.State.DELETED, onlineArchiveOpt2.get().getState());
    assertNotNull(onlineArchiveOpt2.get().getDeleteAfterDate());
    assertEquals(
        5,
        ChronoUnit.DAYS.between(
            deletionDate.toInstant(), onlineArchiveOpt2.get().getDeleteAfterDate().toInstant()));
  }

  void testMarkArchivesForDeletion(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    // Create OnlineArchives
    final OnlineArchive onlineArchive1 =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);
    final OnlineArchive onlineArchive2 =
        new OnlineArchive.Builder(onlineArchive1.toDBObject())
            .setArchiveId(ObjectId.get())
            .setDbName(onlineArchive1.getDbName() + "1")
            .build();

    _onlineArchiveSvc.create(onlineArchive1, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.create(onlineArchive2, AuditInfoHelpers.fromSystem());

    // Should create the OnlineArchives
    final List<OnlineArchive> onlineArchivesAfterCreate =
        _onlineArchiveSvc.getArchivesForCluster(
            pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME);
    assertEquals(2, onlineArchivesAfterCreate.size());
    onlineArchivesAfterCreate.stream()
        .forEach(
            onlineArchive -> {
              assertEquals(OnlineArchive.State.PENDING, onlineArchive.getState());
              assertNull(onlineArchive.getDeleteAfterDate());
            });

    // Mark archives for deletion
    final Date deletionDate = new Date();

    _onlineArchiveSvc.markArchivesForDeletion(
        pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME);

    // Should set archive states to DELETED
    // Should set the archives' delete-after dates to five days in future
    final List<OnlineArchive> onlineArchivesAfterDelete =
        _onlineArchiveSvc.getArchivesForCluster(
            pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME);
    assertEquals(2, onlineArchivesAfterDelete.size());
    onlineArchivesAfterDelete.stream()
        .forEach(
            onlineArchive -> {
              assertEquals(OnlineArchive.State.DELETED, onlineArchive.getState());
              assertNotNull(onlineArchive.getDeleteAfterDate());
              assertEquals(
                  5,
                  ChronoUnit.DAYS.between(
                      deletionDate.toInstant(), onlineArchive.getDeleteAfterDate().toInstant()));
            });
  }

  void testResolveCollectionUUID(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    // Create an active OnlineArchive
    // Pause the archive
    // Should set archive state to PAUSED
    final OnlineArchive archive1 =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);

    _onlineArchiveSvc.create(archive1, AuditInfoHelpers.fromSystem());
    assertTrue(archive1.getCollectionUUID().isEmpty());

    final UUID uuid = UUID.randomUUID();
    final ArchiveResolution resolution = ArchiveResolution.builder().collectionUUID(uuid).build();

    _onlineArchiveSvc.resolveArchiveCollectionUUID(archive1.getId(), resolution);

    final OnlineArchive archive2 = _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());
    assertTrue(archive2.getCollectionUUID().isPresent());
    assertEquals(uuid, archive2.getCollectionUUID().get());

    // ensure that the uuid will not be changed if its already present
    final ArchiveResolution resolution2 =
        ArchiveResolution.builder().collectionUUID(UUID.randomUUID()).build();
    _onlineArchiveSvc.resolveArchiveCollectionUUID(archive2.getId(), resolution2);

    final OnlineArchive archive3 = _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());
    // assert that first uuid value is still present
    assertTrue(archive3.getCollectionUUID().isPresent());
    assertEquals(uuid, archive3.getCollectionUUID().get());
  }

  void testResolveFieldTypes(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    final OnlineArchive archive1 =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);

    _onlineArchiveSvc.create(archive1, AuditInfoHelpers.fromSystem());
    assertTrue(archive1.getCollectionUUID().isEmpty());

    final Map<String, String> fieldTypes = new HashMap<>();
    fieldTypes.put("field1", "string");
    fieldTypes.put("field2", "string");
    fieldTypes.put("dateField", "date");

    final ArchiveResolution resolution =
        ArchiveResolution.builder().partitionFieldTypes(fieldTypes).build();

    _onlineArchiveSvc.resolveArchivePartitionFieldTypes(archive1.getId(), resolution);

    final OnlineArchive archive2 = _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());
    assertTrue(archive2.getPartitionFields().get(0).getFieldType().isPresent());
    assertEquals("string", archive2.getPartitionFields().get(0).getFieldType().get());
    assertTrue(archive2.getPartitionFields().get(1).getFieldType().isPresent());
    assertEquals("string", archive2.getPartitionFields().get(1).getFieldType().get());
    assertTrue(archive2.getPartitionFields().get(2).getFieldType().isPresent());
    assertEquals("date", archive2.getPartitionFields().get(2).getFieldType().get());

    // ensure that the field types will not be changed if its already present
    final Map<String, String> fieldTypes2 = new HashMap<>();
    fieldTypes2.put("field1", "int");
    fieldTypes2.put("field2", "int");
    fieldTypes2.put("dateField", "int");

    final ArchiveResolution resolution2 =
        ArchiveResolution.builder().partitionFieldTypes(fieldTypes2).build();
    _onlineArchiveSvc.resolveArchivePartitionFieldTypes(archive2.getId(), resolution2);

    final OnlineArchive archive3 = _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());
    assertTrue(archive3.getPartitionFields().get(0).getFieldType().isPresent());
    assertEquals("string", archive3.getPartitionFields().get(0).getFieldType().get());
    assertTrue(archive3.getPartitionFields().get(1).getFieldType().isPresent());
    assertEquals("string", archive3.getPartitionFields().get(1).getFieldType().get());
    assertTrue(archive3.getPartitionFields().get(2).getFieldType().isPresent());
    assertEquals("date", archive3.getPartitionFields().get(2).getFieldType().get());

    // ensure that an exception is raised if there is an attempt to set field types for fields that
    // don't match existing
    final Map<String, String> fieldTypes3 = new HashMap<>();
    fieldTypes3.put("field1", "int");

    final ArchiveResolution resolution3 =
        ArchiveResolution.builder().partitionFieldTypes(fieldTypes3).build();

    try {
      _onlineArchiveSvc.resolveArchivePartitionFieldTypes(archive1.getId(), resolution3);
      fail();
    } catch (final Exception ex) {
      assertTrue(ex instanceof IllegalStateException);
    }
  }

  void testTuneArchiveFrequency(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    final OnlineArchive archive1 =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);

    final Calendar currentTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));

    _onlineArchiveSvc.create(archive1, AuditInfoHelpers.fromSystem());

    final OnlineArchive archive2 = _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());

    currentTime.add(Calendar.MINUTE, -30);
    final Date thirtyMinAgo = currentTime.getTime();

    currentTime.add(Calendar.MINUTE, -30);
    final Date oneHourAgo = currentTime.getTime();

    currentTime.add(Calendar.DATE, -1);

    final Calendar startDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
    startDate.set(Calendar.HOUR_OF_DAY, 0);
    startDate.set(Calendar.MINUTE, 0);
    startDate.set(Calendar.SECOND, 0);
    startDate.set(Calendar.MILLISECOND, 0);

    final Calendar endDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
    endDate.setTime(startDate.getTime());
    endDate.set(Calendar.HOUR_OF_DAY, 23);
    endDate.set(Calendar.MINUTE, 59);
    endDate.set(Calendar.SECOND, 59);
    endDate.set(Calendar.MILLISECOND, 999);

    _onlineArchiveHistoryDao.create(
        new OnlineArchiveHistory.Builder()
            .id(new ObjectId())
            .archiveId(archive1.getId())
            .clusterId(archive1.getGroupId(), archive1.getClusterName())
            .collectionType(OnlineArchive.CollectionType.STANDARD)
            .startDate(startDate.getTime())
            .endDate(endDate.getTime())
            .numDocuments(12)
            .numPuts(20)
            .fileSize(30)
            .build());

    startDate.add(Calendar.DATE, -1);
    endDate.add(Calendar.DATE, -1);

    // create history for the previous day
    final ObjectId onlineArchiveId = new ObjectId();
    _onlineArchiveHistoryDao.create(
        new OnlineArchiveHistory.Builder()
            .id(onlineArchiveId)
            .archiveId(archive1.getId())
            .clusterId(archive1.getGroupId(), archive1.getClusterName())
            .collectionType(OnlineArchive.CollectionType.STANDARD)
            .startDate(startDate.getTime())
            .endDate(endDate.getTime())
            .numDocuments(84)
            .numPuts(20)
            .fileSize(30)
            .build());

    // archive with undefined last archive run
    assertNull(archive2.getLastArchiveRun());

    // archive with intervalLastEvaluated within one hour -> frequency not change
    {
      _onlineArchiveDao.updateIntervalLastEvaluated(archive1.getId(), thirtyMinAgo);
      _onlineArchiveDao.updateNumDocsFromLastEvaluated(archive1.getId(), 0L);

      _onlineArchiveSvc.tuneArchiveFrequency(archive1.getId());

      final OnlineArchive archivePostUpdate =
          _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());

      assertEquals(
          OnlineArchive.DEFAULT_ARCHIVE_RUN_FREQUENCY,
          archivePostUpdate.getRunFrequencyInSeconds());
    }

    // lastArchiveRun.maxOffload == true -> reset frequency
    {
      _onlineArchiveDao.updateIntervalLastEvaluated(archive1.getId(), oneHourAgo);
      _onlineArchiveDao.updateNumDocsFromLastEvaluated(archive1.getId(), 1L);
      _onlineArchiveDao.incrementNumConsecutiveMaxOffloads(archive1.getId());
      _onlineArchiveDao.updateArchiveRunFrequency(archive1.getId(), 20 * 60);

      _onlineArchiveSvc.tuneArchiveFrequency(archive1.getId());

      final OnlineArchive archivePostUpdate =
          _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());
      assertEquals(
          OnlineArchive.DEFAULT_ARCHIVE_RUN_FREQUENCY,
          archivePostUpdate.getRunFrequencyInSeconds());
    }

    // archive with undefined intervalLastEvaluated - first frequencyCheck after first two days
    {
      _onlineArchiveDao.updateIntervalLastEvaluated(archive1.getId(), null);
      _onlineArchiveDao.updateNumDocsFromLastEvaluated(archive1.getId(), 1L);
      _onlineArchiveDao.incrementNumConsecutiveMaxOffloads(archive1.getId());

      _onlineArchiveSvc.tuneArchiveFrequency(archive1.getId());

      final OnlineArchive archivePostUpdate =
          _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());

      assertEquals(
          OnlineArchive.DEFAULT_ARCHIVE_RUN_FREQUENCY,
          archivePostUpdate.getRunFrequencyInSeconds());
      assertEquals(1L, archivePostUpdate.getNumDocsFromLastEvaluated().longValue());
      assertFalse(archivePostUpdate.getIntervalLastEvaluated().isEmpty());
    }

    // upper bound
    {
      _onlineArchiveDao.incrementArchiveRunFrequency(archive1.getId(), 4 * 60 * 60);
      final int currentFrequency =
          _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId()).getRunFrequencyInSeconds();
      _onlineArchiveDao.updateIntervalLastEvaluated(archive1.getId(), oneHourAgo);
      _onlineArchiveDao.updateNumDocsFromLastEvaluated(archive1.getId(), 1L);
      _onlineArchiveDao.resetNumConsecutiveMaxOffloads(archive1.getId());

      _onlineArchiveSvc.tuneArchiveFrequency(archive1.getId());

      final OnlineArchive archivePostUpdate =
          _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());

      assertEquals(currentFrequency, archivePostUpdate.getRunFrequencyInSeconds());
    }

    // lastArchiveRun.maxOffload == false && lastEvaluated one hour ago -> decrement freq
    {
      final int currentFrequency =
          _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId()).getRunFrequencyInSeconds();

      _onlineArchiveHistoryDao.save(
          new OnlineArchiveHistory.Builder()
              .id(onlineArchiveId)
              .archiveId(archive1.getId())
              .clusterId(archive1.getGroupId(), archive1.getClusterName())
              .startDate(startDate.getTime())
              .endDate(endDate.getTime())
              .numDocuments(0)
              .numPuts(20)
              .fileSize(30)
              .collectionType(OnlineArchive.CollectionType.STANDARD)
              .build());

      _onlineArchiveDao.updateIntervalLastEvaluated(archive1.getId(), oneHourAgo);
      _onlineArchiveDao.updateNumDocsFromLastEvaluated(archive1.getId(), 1L);
      _onlineArchiveDao.resetNumConsecutiveMaxOffloads(archive1.getId());

      _onlineArchiveSvc.tuneArchiveFrequency(archive1.getId());

      final OnlineArchive archivePostUpdate =
          _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());

      assertEquals(currentFrequency - 5 * 60, archivePostUpdate.getRunFrequencyInSeconds());
    }
    // lower bound
    {
      _onlineArchiveDao.updateArchiveRunFrequency(
          archive1.getId(), OnlineArchive.DEFAULT_ARCHIVE_RUN_FREQUENCY);
      _onlineArchiveDao.updateIntervalLastEvaluated(archive1.getId(), oneHourAgo);
      _onlineArchiveDao.updateNumDocsFromLastEvaluated(archive1.getId(), 1L);
      _onlineArchiveDao.resetNumConsecutiveMaxOffloads(archive1.getId());

      _onlineArchiveSvc.tuneArchiveFrequency(archive1.getId());

      final OnlineArchive archivePostUpdate =
          _onlineArchiveSvc.getValidateOnlineArchive(archive1.getId());

      assertEquals(
          OnlineArchive.DEFAULT_ARCHIVE_RUN_FREQUENCY,
          archivePostUpdate.getRunFrequencyInSeconds());
    }
  }

  void testCalculateDeviationRate(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);
    final OnlineArchive archive1 =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);
    final Calendar currentTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
    currentTime.add(Calendar.HOUR, -2);

    _onlineArchiveSvc.create(archive1, AuditInfoHelpers.fromSystem());
    _onlineArchiveDao.updateNumDocsFromLastEvaluated(archive1.getId(), 5L);
    _onlineArchiveDao.updateIntervalLastEvaluated(archive1.getId(), currentTime.getTime());

    final Optional<OnlineArchive> onlineArchivePostUpdateOpt =
        _onlineArchiveDao.find(archive1.getId());

    assertTrue(onlineArchivePostUpdateOpt.isPresent());
    assertEquals(
        currentTime.getTime(), onlineArchivePostUpdateOpt.get().getIntervalLastEvaluated().get());

    final Calendar startDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
    startDate.set(Calendar.HOUR_OF_DAY, 0);
    startDate.set(Calendar.MINUTE, 0);
    startDate.set(Calendar.SECOND, 0);
    startDate.set(Calendar.MILLISECOND, 0);

    final Calendar endDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
    endDate.setTime(startDate.getTime());
    endDate.set(Calendar.HOUR_OF_DAY, 23);
    endDate.set(Calendar.MINUTE, 59);
    endDate.set(Calendar.SECOND, 59);
    endDate.set(Calendar.MILLISECOND, 999);

    _onlineArchiveHistoryDao.create(
        new OnlineArchiveHistory.Builder()
            .id(new ObjectId())
            .archiveId(archive1.getId())
            .collectionType(OnlineArchive.CollectionType.STANDARD)
            .clusterId(archive1.getGroupId(), archive1.getClusterName())
            .startDate(startDate.getTime())
            .endDate(endDate.getTime())
            .numDocuments(12)
            .numPuts(20)
            .fileSize(30)
            .build());

    startDate.add(Calendar.DATE, -1);
    endDate.add(Calendar.DATE, -1);

    assertEquals(0, _onlineArchiveSvc.calculateDeviationRate(onlineArchivePostUpdateOpt.get()), 0);
    // create history for the previous day
    final ObjectId onlineArchiveId = new ObjectId();
    _onlineArchiveHistoryDao.create(
        new OnlineArchiveHistory.Builder()
            .id(onlineArchiveId)
            .archiveId(archive1.getId())
            .clusterId(archive1.getGroupId(), archive1.getClusterName())
            .startDate(startDate.getTime())
            .endDate(endDate.getTime())
            .numDocuments(84)
            .numPuts(20)
            .fileSize(30)
            .collectionType(OnlineArchive.CollectionType.STANDARD)
            .build());

    final double deviation =
        _onlineArchiveSvc.calculateDeviationRate(onlineArchivePostUpdateOpt.get());
    currentTime.add(Calendar.HOUR, 2);
    final double hourlyRate = (double) (12 + 84) / (currentTime.get(Calendar.HOUR_OF_DAY) + 24);

    assertEquals((5.0 / 2) / hourlyRate - 1, deviation, 0);
  }

  void testSetPauseRequested(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    // Create OnlineArchives
    final OnlineArchive onlineArchive =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(onlineArchive.getId(), AuditInfoHelpers.fromSystem());

    // test pause requested
    _onlineArchiveSvc.setPauseRequested(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
    final OnlineArchive pauseRequestedArchive =
        _onlineArchiveSvc.getOnlineArchive(onlineArchive.getId()).get();
    assertEquals(State.PAUSING, pauseRequestedArchive.getState());
  }

  void testSetPauseCompleted(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    // Create OnlineArchives
    final OnlineArchive onlineArchive =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.setPauseRequested(onlineArchive.getId(), AuditInfoHelpers.fromSystem());

    // test pause completed
    _onlineArchiveSvc.setPauseCompleted(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
    final OnlineArchive pauseCompletedArchive =
        _onlineArchiveSvc.getOnlineArchive(onlineArchive.getId()).get();
    assertEquals(State.PAUSED, pauseCompletedArchive.getState());
  }

  void testResumeWithDuplicateCollectionUUID(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    {
      final UUID uuid = UUID.randomUUID();

      // Create an archive, resolve resolution, then pause it
      final OnlineArchive onlineArchive =
          _onlineArchiveTestUtils.getOnlineArchive(
              pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);
      _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.mergeArchiveResolution(
          onlineArchive.getId(), ArchiveResolution.builder().collectionUUID(uuid).build());
      _onlineArchiveSvc.activate(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.setPauseRequested(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.setPauseCompleted(onlineArchive.getId(), AuditInfoHelpers.fromSystem());

      // Create another ACTIVE archive with the same collection UUID
      final OnlineArchive onlineArchive2 =
          _onlineArchiveTestUtils
              .getOnlineArchive(pGroup.getId(), pCloudProvider, pOnlineArchiveVersion)
              .copy()
              .setCollName("mycoll2")
              .build();
      _onlineArchiveSvc.create(onlineArchive2, AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.mergeArchiveResolution(
          onlineArchive2.getId(), ArchiveResolution.builder().collectionUUID(uuid).build());
      _onlineArchiveSvc.activate(onlineArchive2.getId(), AuditInfoHelpers.fromSystem());

      // Resume first archive - should fail w/ warning while the second archive is ACTIVE state
      //                      - ARCHIVING/ IDLE status shown in second archive
      try {
        _onlineArchiveSvc.updateOnlineArchive(
            onlineArchive.getId(),
            onlineArchive.copy().setState(State.ACTIVE).build(),
            AuditInfoHelpers.fromSystem());
        // TODO: validate warning
        fail();
      } catch (final SvcException e) {
        assertEquals(e.getErrorCode(), NDSErrorCode.NAMESPACE_HAS_ONLINE_ARCHIVE);
      }
    }

    {
      final UUID uuid2 = UUID.randomUUID();

      // Create an archive, resolve resolution, then pause it
      final OnlineArchive onlineArchive3 =
          _onlineArchiveTestUtils
              .getOnlineArchive(pGroup.getId(), pCloudProvider, pOnlineArchiveVersion)
              .copy()
              .setCollName("mycoll3")
              .build();
      _onlineArchiveSvc.create(onlineArchive3, AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.mergeArchiveResolution(
          onlineArchive3.getId(), ArchiveResolution.builder().collectionUUID(uuid2).build());
      _onlineArchiveSvc.activate(onlineArchive3.getId(), AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.setPauseRequested(onlineArchive3.getId(), AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.setPauseCompleted(onlineArchive3.getId(), AuditInfoHelpers.fromSystem());

      // Create another PENDING archive after resolution with the same collection UUID
      final OnlineArchive onlineArchive4 =
          _onlineArchiveTestUtils
              .getOnlineArchive(pGroup.getId(), pCloudProvider, pOnlineArchiveVersion)
              .copy()
              .setCollName("mycoll4")
              .build();
      _onlineArchiveSvc.create(onlineArchive4, AuditInfoHelpers.fromSystem());
      _onlineArchiveSvc.mergeArchiveResolution(
          onlineArchive4.getId(), ArchiveResolution.builder().collectionUUID(uuid2).build());

      final OnlineArchive archiveAfterResolution =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive4.getId());

      if (archiveAfterResolution.getOnlineArchiveVersion().equals(OnlineArchiveVersion.V3)) {
        assertEquals(State.ACTIVE, archiveAfterResolution.getState());
      } else {
        assertEquals(State.PENDING, archiveAfterResolution.getState());
      }

      // Resume first archive - should fail w/ warning while the second archive is PENDING state
      try {
        _onlineArchiveSvc.updateOnlineArchive(
            onlineArchive3.getId(),
            onlineArchive3.copy().setState(State.ACTIVE).build(),
            AuditInfoHelpers.fromSystem());
        // TODO: validate warning
        fail();
      } catch (final SvcException e) {
        assertEquals(e.getErrorCode(), NDSErrorCode.NAMESPACE_HAS_ONLINE_ARCHIVE);
      }
    }
  }

  void testGetArchiveRunInfo(
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    // Create OnlineArchives
    final OnlineArchive onlineArchive =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(onlineArchive.getId(), AuditInfoHelpers.fromSystem());

    OnlineArchiveRunInfoView runInfo =
        _onlineArchiveSvc.getArchiveRunInfo(
            pGroup.getId(),
            OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME,
            onlineArchive.getId());
    assertNull(runInfo.getCurrentArchiveRun());
    assertNull(runInfo.getLastArchiveRun());

    // update the current and last run w/ dao
    final ArchiveRun expectedCurrRun =
        new ArchiveRun(
            "jobid1",
            new Date(),
            new Date(),
            "Internal error",
            0,
            ArchiveRunState.PURGING,
            new ArchiveRunStats(),
            false,
            false,
            false,
            0);
    final ArchiveRunView expectedCurrRunView = new ArchiveRunView(expectedCurrRun);
    final ArchiveRun expectedLastRun =
        new ArchiveRun(
            "jobid2",
            new Date(),
            new Date(),
            null,
            0,
            ArchiveRunState.COMPLETED,
            new ArchiveRunStats(),
            false,
            false,
            false,
            0);
    final ArchiveRunView expectedLastRunView = new ArchiveRunView(expectedLastRun);
    final OnlineArchiveRunInfoView expectedRunInfoView =
        new OnlineArchiveRunInfoView(expectedCurrRunView, expectedLastRunView);

    _onlineArchiveDao.updateCurrentArchiveRun(onlineArchive.getId(), expectedCurrRun);
    _onlineArchiveDao.updateLastArchiveRun(onlineArchive.getId(), expectedLastRun);
    runInfo =
        _onlineArchiveSvc.getArchiveRunInfo(
            pGroup.getId(),
            OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME,
            onlineArchive.getId());
    final OnlineArchiveRun onlineArchiveRun =
        OnlineArchiveRun.toOnlineArchiveRun(onlineArchive, expectedLastRun);
    assertEquals(onlineArchiveRun.getStats(), expectedLastRun.getStats());
    assertNotNull(onlineArchiveRun.getStats());
    assertNotNull(runInfo.getCurrentArchiveRun());
    assertNotNull(runInfo.getLastArchiveRun());

    assertEquals(
        expectedRunInfoView._currentArchiveRun.getJobId(), runInfo._currentArchiveRun.getJobId());
    assertEquals(
        expectedRunInfoView._currentArchiveRun.getErrorMessage(),
        runInfo._currentArchiveRun.getErrorMessage());
    assertEquals(
        expectedRunInfoView._currentArchiveRun.getState(), runInfo._currentArchiveRun.getState());
    assertEquals(
        expectedRunInfoView._currentArchiveRun.getEndDate(),
        runInfo._currentArchiveRun.getEndDate());
    assertEquals(
        expectedRunInfoView._lastArchiveRun.getJobId(), runInfo._lastArchiveRun.getJobId());
  }

  void testCreateAndActivateV3(Group pGroup, final CloudProvider pCloudProvider)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    final OnlineArchive onlineArchive =
        new OnlineArchive.Builder(
                _onlineArchiveTestUtils
                    .getOnlineArchive(pGroup.getId(), pCloudProvider, OnlineArchiveVersion.V3)
                    .toDBObject())
            .setCollectionUUID(UUID.randomUUID())
            .build();

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    final Map<String, String> fieldTypes = new HashMap<>();
    fieldTypes.put("field1", "string");
    fieldTypes.put("field2", "string");
    fieldTypes.put("dateField", "date");
    final ArchiveResolution resolution =
        ArchiveResolution.builder()
            .collectionUUID(UUID.randomUUID())
            .partitionFieldTypes(fieldTypes)
            .build();

    _onlineArchiveSvc.mergeArchiveResolution(onlineArchive.getId(), resolution);

    final String dataSetName = OnlineArchiveDataLakeConfigSvc.getDataSetNameV3(onlineArchive);
    verifyArchiveCreated(onlineArchive, pGroup, pCloudProvider, dataSetName);
  }

  // testCreateV3WithRetries is a test case that simulates a failure after DLS has created the
  // dataSet but before the NDSDataSetOA entry is marked as CREATED.
  // On retry, test asserts that the NDSDataSetOA entry transitions to CREATED state and
  // DLS dataSet is in enabled state.
  void testCreateV3WithRetries(Group pGroup, final CloudProvider pCloudProvider) throws Exception {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    final OnlineArchive onlineArchive =
        new OnlineArchive.Builder(
                _onlineArchiveTestUtils
                    .getOnlineArchive(pGroup.getId(), pCloudProvider, OnlineArchiveVersion.V3)
                    .toDBObject())
            .setCollectionUUID(UUID.randomUUID())
            .build();

    // create does not call DLS InitializeDataSet. That is called by Automation Agent API
    // mergeArchiveResolution below.
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // Fail CREATED state transition so that NDSDataSet entry is in CREATING state but DLS has
    // created the dataSet.
    // Spy on the real DAO to mock markDataSetAsCreated.
    final NDSDataSetDao spyNdsDataSetDao = spy(_ndsDataSetDao);
    ClassModifier.modifyInstanceValue(_onlineArchiveSvc, "_ndsDataSetDao", spyNdsDataSetDao);
    doThrow(new RuntimeException("Database error"))
        .when(spyNdsDataSetDao)
        .markDataSetAsCreated(any(NDSDataSetOA.class));

    final Map<String, String> partitionFiledTypes = new HashMap<>();
    partitionFiledTypes.put("field1", "string");
    partitionFiledTypes.put("field2", "string");
    partitionFiledTypes.put("dateField", "date");
    final ArchiveResolution resolution =
        ArchiveResolution.builder()
            .collectionUUID(UUID.randomUUID())
            .partitionFieldTypes(partitionFiledTypes)
            .build();

    // Call the Automation Agent API used for creating archives.
    try {
      _onlineArchiveSvc.mergeArchiveResolution(onlineArchive.getId(), resolution);
    } catch (RuntimeException e) {
      // Expected exception due to the injected error on markDataSetAsCreated.
      assertEquals("Database error", e.getMessage());
    }

    final String dataSetName = OnlineArchiveDataLakeConfigSvc.getDataSetNameV3(onlineArchive);
    // Assert that the NDSDataSet entry is still in CREATING state but the dataSet is created in DLS
    // but is not enabled.
    final Optional<NDSDataSetOA> ndsOAOpt =
        _ndsDataSetDao.findOAByGroupIdAndName(onlineArchive.getGroupId(), dataSetName);
    assertTrue(ndsOAOpt.isPresent());
    assertEquals(ndsOAOpt.get().getState(), NDSDataSet.State.CREATING);
    assertTrue(
        _dataLakeTestUtils.isDataSetCreated(
            pCloudProvider, pGroup.getId().toString(), dataSetName));
    assertFalse(
        _dataLakeTestUtils.isDataSetEnabled(
            pCloudProvider, pGroup.getId().toString(), dataSetName));

    // Revert the error injector and retry the operation.
    ClassModifier.modifyInstanceValue(_onlineArchiveSvc, "_ndsDataSetDao", _ndsDataSetDao);
    _onlineArchiveSvc.mergeArchiveResolution(onlineArchive.getId(), resolution);

    verifyArchiveCreated(onlineArchive, pGroup, pCloudProvider, dataSetName);
  }

  void verifyArchiveCreated(
      final OnlineArchive onlineArchive,
      final Group pGroup,
      final CloudProvider pCloudProvider,
      final String dataSetName) {
    // config.nds.onlineArchive is updated as expected.
    final OnlineArchive updatedOA = _onlineArchiveDao.find(onlineArchive.getId()).get();
    assertEquals(dataSetName, updatedOA.getDataSetName());
    assertNotNull(updatedOA.getPartitionSetName());

    // nds dataset oa is enabled.
    final NDSDataSetOA ndsDataSetOA = _ndsDataSetDao.findByOnlineArchive(updatedOA).get();
    assertEquals(NDSDataSet.State.ACTIVE, ndsDataSetOA.getState());

    // Validate that dataSet in DLS is enabled.
    assertTrue(
        _dataLakeTestUtils.isDataSetEnabled(
            pCloudProvider, pGroup.getId().toString(), dataSetName));

    if (pCloudProvider.equals(CloudProvider.AWS)) {
      // archive S3 Path is set
      assertEquals(
          String.format("%s/%s", updatedOA.getGroupId(), updatedOA.getId()),
          updatedOA.getArchiveS3Path());
    }
  }

  void testCreateAndActivateV3_ThrowDataLakeException(
      Group pGroup, final CloudProvider pCloudProvider) throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    final OnlineArchive onlineArchive =
        new OnlineArchive.Builder(
                _onlineArchiveTestUtils
                    .getOnlineArchive(pGroup.getId(), pCloudProvider, OnlineArchiveVersion.V3)
                    .toDBObject())
            .setCollectionUUID(UUID.randomUUID())
            .build();

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    final Map<String, String> fieldTypes = new HashMap<>();
    fieldTypes.put("field1", "string");
    fieldTypes.put("field2", "string");
    fieldTypes.put("dateField", "date");
    final ArchiveResolution resolution =
        ArchiveResolution.builder()
            .collectionUUID(UUID.randomUUID())
            .partitionFieldTypes(fieldTypes)
            .build();

    final OnlineArchiveSvc onlineArchiveSvcWithException = spy(_onlineArchiveSvc);
    doThrow(new DataLakeAdminApiException(NDSErrorCode.DLS_UNEXPECTED_ERROR))
        .when(onlineArchiveSvcWithException)
        .updateClusterDataLakeStorageConfigs(any(), any());

    // merge resolution with data lake admin throwing exception
    onlineArchiveSvcWithException.mergeArchiveResolution(onlineArchive.getId(), resolution);

    // config.nds.onlineArchive is updated as expected.
    final OnlineArchive updatedOA = _onlineArchiveDao.find(onlineArchive.getId()).get();
    assertEquals(
        OnlineArchiveDataLakeConfigSvc.getDataSetNameV3(onlineArchive), updatedOA.getDataSetName());
    assertNotNull(updatedOA.getPartitionSetName());

    // verify data set is enabled.
    assertTrue(
        _dataLakeTestUtils.isDataSetEnabled(
            pCloudProvider,
            pGroup.getId().toString(),
            OnlineArchiveDataLakeConfigSvc.getDataSetNameV3(onlineArchive)));

    // nds dataset oa is enabled.
    final NDSDataSetOA ndsDataSetOA = _ndsDataSetDao.findByOnlineArchive(updatedOA).get();
    assertEquals(NDSDataSet.State.ACTIVE, ndsDataSetOA.getState());

    if (pCloudProvider.equals(CloudProvider.AWS)) {
      // archive S3 Path is set
      assertEquals(
          String.format("%s/%s", updatedOA.getGroupId(), updatedOA.getId()),
          updatedOA.getArchiveS3Path());
    }

    // state is still pending
    assertEquals(State.PENDING, updatedOA.getState());

    // resolution error is set
    assertTrue(updatedOA.getResolutionError().isPresent());
    assertEquals(
        "Failed to resolve online archive with unexpected error",
        updatedOA.getResolutionError().get());

    // merge resolution without data lake admin throwing exception on retry
    _onlineArchiveSvc.mergeArchiveResolution(onlineArchive.getId(), resolution);

    // state is now
    final OnlineArchive updatedOAOnRetry = _onlineArchiveDao.find(onlineArchive.getId()).get();
    assertEquals(State.ACTIVE, updatedOAOnRetry.getState());

    // resolution error is cleared
    assertTrue(updatedOAOnRetry.getResolutionError().isEmpty());

    // data set name and partition set name is set
    assertEquals(
        OnlineArchiveDataLakeConfigSvc.getDataSetNameV3(onlineArchive), updatedOA.getDataSetName());
    assertNotNull(updatedOA.getPartitionSetName());
  }

  void testPauseAndActivateV3(Group pGroup, final CloudProvider pCloudProvider)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);

    // Create an OnlineArchive
    final OnlineArchive onlineArchive =
        new OnlineArchive.Builder(
                _onlineArchiveTestUtils
                    .getOnlineArchive(pGroup.getId(), pCloudProvider, OnlineArchiveVersion.V3)
                    .toDBObject())
            .setCollectionUUID(UUID.randomUUID())
            .build();

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    final Map<String, String> fieldTypes = new HashMap<>();
    fieldTypes.put("field1", "string");
    fieldTypes.put("field2", "string");
    fieldTypes.put("dateField", "date");
    final ArchiveResolution resolution =
        ArchiveResolution.builder()
            .collectionUUID(UUID.randomUUID())
            .partitionFieldTypes(fieldTypes)
            .build();

    _onlineArchiveSvc.mergeArchiveResolution(onlineArchive.getId(), resolution);

    // config.nds.onlineArchive is updated as expected.
    final OnlineArchive updatedOA = _onlineArchiveDao.find(onlineArchive.getId()).get();
    assertEquals(
        OnlineArchiveDataLakeConfigSvc.getDataSetNameV3(onlineArchive), updatedOA.getDataSetName());
    assertNotNull(updatedOA.getPartitionSetName());

    // verify data set is enabled.
    assertTrue(
        _dataLakeTestUtils.isDataSetEnabled(
            pCloudProvider,
            pGroup.getId().toString(),
            OnlineArchiveDataLakeConfigSvc.getDataSetNameV3(onlineArchive)));

    // nds dataset oa is enabled.
    final NDSDataSetOA ndsDataSetOA = _ndsDataSetDao.findByOnlineArchive(updatedOA).get();
    assertEquals(NDSDataSet.State.ACTIVE, ndsDataSetOA.getState());

    // make a pause request
    _onlineArchiveSvc.setPauseRequested(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
    final OnlineArchive pauseRequestedOnlineArchive =
        _onlineArchiveSvc.getOnlineArchive(onlineArchive.getId()).get();
    assertEquals(State.PAUSING, pauseRequestedOnlineArchive.getState());

    // verify audit
    final List<Event> audits =
        _auditSvc.findByDate(Type.ONLINE_ARCHIVE_PAUSE_REQUESTED, Date.from(Instant.EPOCH));
    assertEquals(1, audits.size());

    // pause completed
    _onlineArchiveSvc.setPauseCompleted(
        pauseRequestedOnlineArchive.getId(), AuditInfoHelpers.fromSystem());
    final OnlineArchive pausedOnlineArchive =
        _onlineArchiveSvc.getOnlineArchive(pauseRequestedOnlineArchive.getId()).get();
    assertEquals(State.PAUSED, pausedOnlineArchive.getState());
    assertEquals(
        1, _auditSvc.findByDate(Type.ONLINE_ARCHIVE_PAUSED, Date.from(Instant.EPOCH)).size());

    // verify nds dataset oa is still enabled.
    final NDSDataSetOA ndsDataSetOAOnPause =
        _ndsDataSetDao.findByOnlineArchive(pausedOnlineArchive).get();
    assertEquals(NDSDataSet.State.ACTIVE, ndsDataSetOAOnPause.getState());

    // Activate the paused archive
    _onlineArchiveSvc.activate(pausedOnlineArchive.getId(), AuditInfoHelpers.fromSystem());
    final OnlineArchive pausedOnlineArchiveAfterActivation =
        _onlineArchiveSvc.getOnlineArchive(pausedOnlineArchive.getId()).get();
    assertEquals(OnlineArchive.State.ACTIVE, pausedOnlineArchiveAfterActivation.getState());

    // verify nds dataset oa is still enabled.
    final NDSDataSetOA ndsDataSetOAAfterActivation =
        _ndsDataSetDao.findByOnlineArchive(pausedOnlineArchiveAfterActivation).get();
    assertEquals(NDSDataSet.State.ACTIVE, ndsDataSetOAAfterActivation.getState());

    // verify audit
    final List<Event> audits1 =
        _auditSvc.findByDate(NDSAudit.Type.ONLINE_ARCHIVE_ACTIVE, Date.from(Instant.EPOCH));
    assertEquals(2, audits1.size());
  }

  void testUpdateOnlineArchiveDataExpirationV3(Group pGroup, final CloudProvider pCloudProvider)
      throws SvcException {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);
    final OnlineArchive onlineArchive =
        new OnlineArchive.Builder(
                _onlineArchiveTestUtils
                    .getOnlineArchive(pGroup.getId(), pCloudProvider, OnlineArchiveVersion.V3)
                    .toDBObject())
            .setArchiveId(ObjectId.get())
            .setCollName("coll2")
            .setCollectionUUID(UUID.randomUUID())
            .build();

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activateArchiveV3(
        _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId()),
        onlineArchive.getDataProcessRegion().get());
    assertEquals(
        1,
        _onlineArchiveDao.getOnlineArchiveCount(
            pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME));

    final Date enabledDate = new Date();
    final OnlineArchive v3onlineArchiveWithDataExpiration =
        new OnlineArchive.Builder(onlineArchive.toDBObject())
            .setDataExpirationRule(new OnlineArchive.DataExpirationRule(enabledDate, 50))
            .build();

    _onlineArchiveSvc.updateOnlineArchive(
        v3onlineArchiveWithDataExpiration.getId(),
        v3onlineArchiveWithDataExpiration,
        AuditInfoHelpers.fromSystem());
    // no new emails sent
    verifyEmailsSent(
        pGroup, 0, OnlineArchiveVersion.V3, OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME);
    // verify no new audits
    final List<Event> updatedAudits =
        _auditSvc.findByDate(
            NDSAudit.Type.ONLINE_ARCHIVE_DATA_EXPIRATION_RULE_UPDATED, Date.from(Instant.EPOCH));
    assertEquals(0, updatedAudits.size());

    final Optional<OnlineArchive> foundOAOpt =
        _onlineArchiveDao.find(v3onlineArchiveWithDataExpiration.getId());
    assertTrue(foundOAOpt.isPresent());
    final OnlineArchive foundOA = foundOAOpt.get();
    assertTrue(foundOA.getDataExpirationRule().isPresent());
    assertEquals(50, foundOA.getDataExpirationRule().get().getExpireAfterDays().intValue());
    assertTrue(
        _dataLakeTestUtils.isDataSetReconciled(
            pCloudProvider, foundOA.getGroupId().toString(), foundOA.getDataSetName()));
  }

  void testEnsureV3DataProcessingRegion(
      Organization pOrg, Group pGroup, final CloudProvider pCloudProvider) {
    final OnlineArchive onlineArchive =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, OnlineArchiveVersion.V3);
    final OnlineArchive serverlessOnlineArchive =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), CloudProvider.SERVERLESS, OnlineArchiveVersion.V3);

    // empty data process region with unsupported cluster region
    final ClusterDescription serverlessClusterDescription =
        NDSModelTestFactory.getClusterDescription(
            pGroup.getId(), "testClusterGCP", CloudProvider.SERVERLESS);
    _clusterDescriptionDao.save(serverlessClusterDescription);
    try {
      _onlineArchiveSvc.ensureV3DataProcessRegion(
          serverlessClusterDescription, serverlessOnlineArchive);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_STORAGE_CLOUD_PROVIDER_NOT_SUPPORTED, pE.getErrorCode());
    }

    // empty data process region with supported cluster region
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(
            pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME, pCloudProvider);
    _clusterDescriptionDao.save(clusterDescription);
    try {
      final OnlineArchive onlineArchive2 =
          _onlineArchiveSvc.ensureV3DataProcessRegion(clusterDescription, onlineArchive);
      assertEquals(
          onlineArchive2.getDataProcessRegion().get().getRegion(),
          onlineArchive.getDataProcessRegion().get().getRegion());
      assertEquals(
          onlineArchive2.getDataProcessRegion().get().getCloudProvider(),
          onlineArchive.getDataProcessRegion().get().getCloudProvider());
    } catch (final SvcException pE) {
      fail(pE.getMessage());
    }
  }

  void testUpdateOnlineArchiveLimitResumeFrom(
      final CloudProvider pCloudProvider,
      Group pGroup,
      final OnlineArchiveVersion pOnlineArchiveVersion,
      final State pPauseState) {
    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);
    final OnlineArchive onlineArchive =
        new OnlineArchive.Builder(
                _onlineArchiveTestUtils
                    .getOnlineArchive(pGroup.getId(), pCloudProvider, pOnlineArchiveVersion)
                    .toDBObject())
            .build();
    try {
      _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    } catch (final SvcException e) {
      fail("Unexpected svc exception creating online archive");
    }
    assertEquals(
        1,
        _onlineArchiveDao.getOnlineArchiveCount(
            pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME));
    assertEquals(
        0,
        _onlineArchiveDao.getActiveOnlineArchiveCount(
            pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME));

    //  Pause this onlineArchive
    try {
      if (pPauseState.equals(State.PAUSING)) {
        _onlineArchiveSvc.setPauseRequested(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
      } else if (pPauseState.equals(State.PAUSED)) {
        _onlineArchiveSvc.setPauseCompleted(onlineArchive.getId(), AuditInfoHelpers.fromSystem());
      }
    } catch (final SvcException e) {
      fail("Unexpected svc exception pausing online archive");
    }

    final OnlineArchive activeOnlineArchive =
        new OnlineArchive.Builder(onlineArchive.toDBObject()).setState(State.ACTIVE).build();
    // Test for active online archive count
    // attempt with limit=0
    try {
      _ndsGroupSvc.setLimit(
          pGroup.getId(),
          FieldDefs.MAX_ACTIVE_ONLINE_ARCHIVES_PER_CLUSTER.getValue(),
          0L,
          AuditInfoHelpers.fromSystem(),
          null);
    } catch (final Exception e) {
      fail("Unexpected error occurred when setting limits");
    }
    try {
      _onlineArchiveSvc.updateOnlineArchive(
          activeOnlineArchive.getId(), activeOnlineArchive, AuditInfoHelpers.fromSystem());
      Assert.fail("Expected limit exception to be thrown");
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.ACTIVE_ONLINE_ARCHIVE_LIMIT_EXCEEDED, e.getErrorCode());
    }

    // bump to limit=1
    try {
      _ndsGroupSvc.setLimit(
          pGroup.getId(),
          FieldDefs.MAX_ACTIVE_ONLINE_ARCHIVES_PER_CLUSTER.getValue(),
          1L,
          AuditInfoHelpers.fromSystem(),
          null);
    } catch (final Exception e) {
      fail("Unexpected error occurred when setting limits");
    }
    try {
      _onlineArchiveSvc.updateOnlineArchive(
          activeOnlineArchive.getId(), activeOnlineArchive, AuditInfoHelpers.fromSystem());
    } catch (final SvcException e) {
      fail("Unexpected svc exception updating online archive");
    }
    assertEquals(
        1,
        _onlineArchiveDao.getOnlineArchiveCount(
            pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME));
    assertEquals(
        1,
        _onlineArchiveDao.getActiveOnlineArchiveCount(
            pGroup.getId(), OnlineArchiveTestUtils.DEFAULT_TEST_CLUSTER_NAME));
  }

  void testUpdateCriteria(
      Group pGroup,
      final CloudProvider pCloudProvider,
      final OnlineArchiveVersion pOnlineArchiveVersion)
      throws SvcException {
    final int defaultJobFrequencyInSeconds = OnlineArchive.DEFAULT_ARCHIVE_RUN_FREQUENCY;
    final int startingJobFrequencyInSeconds = defaultJobFrequencyInSeconds * 10;

    _onlineArchiveTestUtils.createCluster(pGroup.getId(), pCloudProvider);
    final OnlineArchive archive =
        _onlineArchiveTestUtils.getOnlineArchive(
            pGroup.getId(), pCloudProvider, pOnlineArchiveVersion);
    _onlineArchiveSvc.create(archive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(archive.getId(), AuditInfoHelpers.fromSystem());

    _onlineArchiveDao.updateArchiveRunFrequency(archive.getId(), startingJobFrequencyInSeconds);

    final OnlineArchive.DateCriteria initialCriteria =
        (OnlineArchive.DateCriteria)
            _onlineArchiveSvc.getOnlineArchive(archive.getId()).get().getCriteria();

    // Check initial date criteria.
    assertEquals(Integer.valueOf(5), initialCriteria.getExpireAfterDays());
    assertEquals("dateField", initialCriteria.getDateField());

    // Successfully update date criteria.
    {
      final OnlineArchive archiveUpdate =
          new OnlineArchive.Builder(
                  _onlineArchiveTestUtils
                      .getOnlineArchive(pGroup.getId(), pCloudProvider, pOnlineArchiveVersion)
                      .toDBObject())
              .setArchiveId(archive.getId())
              .setCriteria(new OnlineArchive.DateCriteria("dateField", 29, DateFormat.ISODATE))
              .build();
      _onlineArchiveSvc.updateOnlineArchive(
          archive.getId(), archiveUpdate, AuditInfoHelpers.fromSystem());

      final OnlineArchive updatedArchive =
          _onlineArchiveSvc.getOnlineArchive(archive.getId()).get();

      final OnlineArchive.DateCriteria updatedCriteria =
          (OnlineArchive.DateCriteria) updatedArchive.getCriteria();
      assertEquals(Integer.valueOf(29), updatedCriteria.getExpireAfterDays());
      assertEquals("dateField", updatedCriteria.getDateField());

      // Job frequency should be reset.
      final int updatedArchiveJobFrequency = updatedArchive.getRunFrequencyInSeconds();
      assertEquals(defaultJobFrequencyInSeconds, updatedArchiveJobFrequency);

      // Verify audit.
      final List<Event> audits0 =
          _auditSvc.findByDate(NDSAudit.Type.ONLINE_ARCHIVE_UPDATED, Date.from(Instant.EPOCH));
      assertEquals(1, audits0.size());

      // Set job frequency back to starting value for subsequent tests.
      _onlineArchiveDao.updateArchiveRunFrequency(archive.getId(), startingJobFrequencyInSeconds);
    }

    final List<PartitionField> partitionFields =
        List.of(new PartitionField("field1", 0), new PartitionField("field2", 1));
    final OnlineArchive archive2 =
        _onlineArchiveTestUtils
            .getOnlineArchive(pGroup.getId(), pCloudProvider, pOnlineArchiveVersion)
            .copy()
            .setCollName("custom")
            .setPartitionFields(partitionFields)
            .setCriteria(new OnlineArchive.CustomCriteria("{\"some\": \"value\"}"))
            .build();
    _onlineArchiveSvc.create(archive2, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(archive2.getId(), AuditInfoHelpers.fromSystem());
    _onlineArchiveDao.updateArchiveRunFrequency(archive2.getId(), startingJobFrequencyInSeconds);

    // Unsuccessfully update custom criteria.
    {
      // Bad query.
      final OnlineArchive archiveUpdateCustomCriteria =
          archive2.copy().setCriteria(new OnlineArchive.CustomCriteria("{}")).build();
      try {
        _onlineArchiveSvc.updateOnlineArchive(
            archive2.getId(), archiveUpdateCustomCriteria, AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, pE.getErrorCode());

        // Job frequency should not change.
        final int jobFrequencyInSeconds =
            _onlineArchiveSvc.getOnlineArchive(archive.getId()).get().getRunFrequencyInSeconds();
        assertEquals(startingJobFrequencyInSeconds, jobFrequencyInSeconds);
      }
    }

    // Successfully update custom criteria.
    {
      final String query = "{\"field\": \"value\"}";
      final OnlineArchive archiveUpdateCustomCriteria =
          archive2.copy().setCriteria(new OnlineArchive.CustomCriteria(query)).build();
      _onlineArchiveSvc.updateOnlineArchive(
          archive2.getId(), archiveUpdateCustomCriteria, AuditInfoHelpers.fromSystem());

      final OnlineArchive updatedArchive =
          _onlineArchiveSvc.getOnlineArchive(archive2.getId()).get();

      final OnlineArchive.CustomCriteria customCriteria =
          (OnlineArchive.CustomCriteria) updatedArchive.getCriteria();
      assertEquals(query, customCriteria.getQuery());

      // Job frequency should be reset.
      final int updatedArchiveJobFrequency = updatedArchive.getRunFrequencyInSeconds();
      assertEquals(defaultJobFrequencyInSeconds, updatedArchiveJobFrequency);

      // Set job frequency back to starting value for subsequent tests.
      _onlineArchiveDao.updateArchiveRunFrequency(archive2.getId(), startingJobFrequencyInSeconds);
    }

    // Attempt to update date date criteria to custom criteria.
    {
      final String query = "{\"field\": \"value\"}";
      final OnlineArchive archiveUpdateCustomCriteria =
          new OnlineArchive.Builder(
                  _onlineArchiveTestUtils
                      .getOnlineArchive(pGroup.getId(), pCloudProvider, pOnlineArchiveVersion)
                      .toDBObject())
              .setArchiveId(archive.getId())
              .setCriteria(new OnlineArchive.CustomCriteria(query))
              .build();
      try {
        _onlineArchiveSvc.updateOnlineArchive(
            archive.getId(), archiveUpdateCustomCriteria, AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.ONLINE_ARCHIVE_CANNOT_MODIFY_FIELD, pE.getErrorCode());

        // Job frequency should not change.
        final int jobFrequencyInSeconds =
            _onlineArchiveSvc.getOnlineArchive(archive.getId()).get().getRunFrequencyInSeconds();
        assertEquals(startingJobFrequencyInSeconds, jobFrequencyInSeconds);
      }
    }

    // Attempt to update custom criteria to date criteria.
    {
      final OnlineArchive archiveUpdateDateCriteria =
          new OnlineArchive.Builder(
                  _onlineArchiveTestUtils
                      .getOnlineArchive(pGroup.getId(), pCloudProvider, pOnlineArchiveVersion)
                      .toDBObject())
              .setArchiveId(archive2.getId())
              .setCriteria(new OnlineArchive.DateCriteria("dateField", 29, DateFormat.ISODATE))
              .build();
      try {
        _onlineArchiveSvc.updateOnlineArchive(
            archive2.getId(), archiveUpdateDateCriteria, AuditInfoHelpers.fromSystem());
        fail();
      } catch (final SvcException pE) {
        assertEquals(NDSErrorCode.ONLINE_ARCHIVE_CANNOT_MODIFY_FIELD, pE.getErrorCode());

        // Job frequency should not change.
        final int jobFrequencyInSeconds =
            _onlineArchiveSvc.getOnlineArchive(archive2.getId()).get().getRunFrequencyInSeconds();
        assertEquals(startingJobFrequencyInSeconds, jobFrequencyInSeconds);
      }
    }
  }

  public void verifyEmailsSent(
      final Group pGroup,
      final int pEmailCount,
      final OnlineArchiveVersion pOnlineArchiveVersion,
      final String pClusterName) {
    final List<AppUser> users = _groupSvc.getAllUsersWithTeams(pGroup);

    users.forEach(
        user -> {
          final List<EmailMsg> emails =
              _emailSvc.findEmailsByRecipient(user.getPrimaryEmail(), 0, pEmailCount + 1);

          assertEquals(pEmailCount, emails.size());
          for (final EmailMsg email : emails) {
            assertTrue(
                email
                    .getSubject()
                    .contains("You have enabled Data Expiration for your Online Archive"));

            final String emailContent = email.getContent();
            assertTrue(
                emailContent.contains(
                    "This is an informational notice to ensure you're aware that you have enabled"
                        + " data expiration for your Online Archive."));
            assertFalse(
                emailContent.contains(
                    String.format("Documents that have been archived for at least %s days", 8)));
            assertTrue(
                emailContent.contains(
                    String.format(
                        "https://cloud.mongodb.com/v2/%s#/clusters/onlineArchive/%s",
                        pGroup.getId(), pClusterName)));
            final String effectivePeriod;
            if (!OnlineArchiveVersion.V3.equals(pOnlineArchiveVersion)) {
              effectivePeriod = "in 24 hours";
            } else {
              effectivePeriod = "immediately";
            }
            assertTrue(emailContent.contains(effectivePeriod));
          }
        });
  }
}
