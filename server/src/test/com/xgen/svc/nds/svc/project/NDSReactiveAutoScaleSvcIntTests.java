package com.xgen.svc.nds.svc.project;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.greaterThan;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.google.common.collect.Iterables;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.alert.InformationalAlertSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.chef._public.svc.ChefServerStatusSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.common._public.model.retention.Retention;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.metrics._private.dao.rrd.HostMeasurementDao;
import com.xgen.cloud.monitoring.metrics._public.model.ModelTestUtils;
import com.xgen.cloud.monitoring.metrics._public.model.SystemMemoryMeasurement;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAutoScalingAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAutoScalingAudit.Type;
import com.xgen.cloud.nds.autoscaling.alerts._public.svc.NDSAutoScaleAlertSvc;
import com.xgen.cloud.nds.autoscaling.common._public.svc.NDSAutoScaleValidationSvc;
import com.xgen.cloud.nds.autoscaling.context._private.dao.AutoScalingContextDao;
import com.xgen.cloud.nds.autoscaling.context._public.model.AutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.autosharding.AutoShardingAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ClusterComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ShardComputeAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.ShardContext;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.SingleScalingCriteria;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingCPUThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingIntervalThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingMemoryThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.criteria.ComputeAutoScalingWTUsageThreshold;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.metrics.CPUThresholdMetrics;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.metrics.IntervalThresholdMetrics;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.metrics.MemoryThresholdMetrics;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.metrics.ThresholdMetrics;
import com.xgen.cloud.nds.autoscaling.context._public.model.compute.thresholds.metrics.WTUsageThresholdMetrics;
import com.xgen.cloud.nds.autoscaling.context._public.model.disk.DiskGBAutoScalingContext;
import com.xgen.cloud.nds.autoscaling.context._public.util.ComputeAutoScalingContextUtil;
import com.xgen.cloud.nds.autoscaling.metrics._public.model.metrics.AutoScalingCalculatedMetrics;
import com.xgen.cloud.nds.autoscaling.metrics._public.svc.ComputeClusterMetricsCalculatorSvc;
import com.xgen.cloud.nds.autoscaling.predictive._public.model.PredictiveAutoScalingResult;
import com.xgen.cloud.nds.autoscaling.triggercoordinator._public.svc.ComputeAutoScalingDecisionCoordinatorSvc;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec.Builder;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSPhysicalZoneId;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSAutoScaling;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSComputeAutoScaling;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzurePhysicalZoneId;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.capacity._public.svc.AWSCapacityDenylistSvc;
import com.xgen.cloud.nds.capacity._public.svc.AzureCapacityDenylistSvc;
import com.xgen.cloud.nds.capacity._public.svc.GCPCapacityDenylistSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoIndexing;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSizeBuilderFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleSingleCloudInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.DiskGBAutoScaling;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterUpdateContext;
import com.xgen.cloud.nds.common._public.model.Limits;
import com.xgen.cloud.nds.common._public.model.Limits.FieldDefs;
import com.xgen.cloud.nds.common._public.model.ReplicaSetType;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceFamily;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPPhysicalZoneId;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.GCPZoneName;
import com.xgen.cloud.nds.metrics._public.svc.NDSComputeClusterMetricsSvc;
import com.xgen.cloud.nds.planning.summary._public.model.cluster.AutoScaleDecisions;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionUpdatesDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.AutoSharding;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.util.ClusterDescriptionUtil;
import com.xgen.cloud.nds.project._public.util.OplogValidationUtil.OplogEstimate;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.module.library.control.Result;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSReactiveAutoScaleSvc.CapacityConstrainInfo;
import com.xgen.testlib.base.nds.JUnit5NDSBaseTest;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.collections4.iterators.LoopingListIterator;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.api.Assertions;
import org.bson.types.ObjectId;
import org.hamcrest.Matcher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class NDSReactiveAutoScaleSvcIntTests extends JUnit5NDSBaseTest {

  private static final String DEFAULT_SCALE_DOWN_TIME_ZONE = "America/New_York";

  private static final String CLUSTER_NAME = "autoScale";
  private static final String SHARDED_CLUSTER_NAME = "shardedAutoScale";
  private static final String CROSS_CLOUD_CLUSTER_NAME = "crossCloudAutoScale";
  private static final String ASYMMETRIC_CLUSTER_NAME = "asymmetricAutoScale";

  @Inject NDSAutoScaleAlertSvc _ndsAutoScaleAlertSvc;
  @Inject NDSComputeClusterMetricsSvc _ndsComputeClusterMetricsSvc;
  @Inject NDSClusterSvc _ndsClusterSvc;
  @Inject ClusterDescriptionDao _clusterDescriptionDao;
  @Inject ClusterDescriptionUpdatesDao _clusterDescriptionUpdateDao;
  @Inject HostMeasurementDao _hostMeasurementDao;
  @Inject AutoScalingContextDao _autoScalingContextDao;
  @Inject ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject HostClusterLifecycleSvc _hostClusterLifecycleSvc;
  @Inject NDSGroupSvc _ndsGroupSvc;
  @Inject GroupSvc _groupSvc;
  @Inject UserSvc _userSvc;
  @Inject AppSettings _appSettings;
  @Inject AuditSvc _auditSvc;
  @Inject ComputeClusterMetricsCalculatorSvc _computeClusterMetricsCalculatorSvc;
  @Inject AWSCapacityDenylistSvc _awsCapacityDenylistSvc;
  @Inject AzureCapacityDenylistSvc _azureCapacityDenylistSvc;
  @Inject GCPCapacityDenylistSvc _gcpCapacityDenylistSvc;
  @Inject InformationalAlertSvc _informationalAlertSvc;
  @Inject ChefServerStatusSvc _chefServerStatusSvc;
  @Inject ComputeAutoScalingDecisionCoordinatorSvc _computeAutoScalingDecisionCoordinatorSvc;
  @Inject NDSGroupDao _ndsGroupDao;
  @Mock NDSAutoScaleValidationSvc _ndsAutoScaleValidationSvc;
  //  @Inject @InjectMocks NDSReactiveAutoScaleSvc _ndsAutoScaleSvc;
  private NDSReactiveAutoScaleSvc _ndsAutoScaleSvc;

  private Organization _organization;
  private Group _mmsGroup;
  private ObjectId _ndsGroupId;
  private Cluster _cluster;
  private Cluster _shardedCluster;
  private Cluster _crossCloudCluster;
  private AppUser _user;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();

    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _mmsGroup = MmsFactory.createGroup(_organization, "ndsGroup");
    _ndsGroupId = _mmsGroup.getId();
    _cluster = setupReplicaSet(_ndsGroupId, CLUSTER_NAME);
    _shardedCluster = setupShardedCluster(_ndsGroupId, SHARDED_CLUSTER_NAME);
    _crossCloudCluster = setupCrossCloudReplicaSet(_ndsGroupId, CROSS_CLOUD_CLUSTER_NAME);
    _user = MmsFactory.createUser(_mmsGroup);

    // Creating many clusters lets bump up the permission limits
    _ndsGroupDao.setLimit(
        _mmsGroup.getId(),
        Limits.FieldDefs.MAX_CROSS_REGION_NETWORK_PERMISSION_ENTRIES.getValue(),
        1500);

    List.of(_cluster, _shardedCluster)
        .forEach(
            cluster -> {
              // enable disk auto scaling flags for single cloud clusters
              final ClusterDescription cd =
                  cluster
                      .getClusterDescription()
                      .copy()
                      .setAutoScalingForProvider(
                          CloudProvider.AWS,
                          cluster
                              .getClusterDescription()
                              .getAutoScaling(NodeTypeFamily.BASE)
                              .getAutoScalingWithFlagsSet(true, false, false, false),
                          NodeTypeFamily.BASE)
                      .build();

              _clusterDescriptionDao.save(cd);
            });

    // enable disk auto scaling flags for cross cloud cluster
    final ClusterDescription crossCloudClusterDescription =
        _crossCloudCluster
            .getClusterDescription()
            .copy()
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                _crossCloudCluster
                    .getClusterDescription()
                    .getAutoScalingForProvider(CloudProvider.AWS, NodeTypeFamily.BASE)
                    .get()
                    .getAutoScalingWithFlagsSet(true, false, false, false),
                NodeTypeFamily.BASE)
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                _crossCloudCluster
                    .getClusterDescription()
                    .getAutoScalingForProvider(CloudProvider.GCP, NodeTypeFamily.BASE)
                    .get()
                    .getAutoScalingWithFlagsSet(true, false, false, false),
                NodeTypeFamily.BASE)
            .build();
    _clusterDescriptionDao.save(crossCloudClusterDescription);

    List.of(_cluster, _shardedCluster, _crossCloudCluster)
        .forEach(
            cluster -> {
              final AutoScalingContext context =
                  setAutoScalingContextFlags(
                      _autoScalingContextDao
                          .find(
                              cluster.getClusterDescription().getGroupId(),
                              cluster.getClusterDescription().getName())
                          .get(),
                      true,
                      false,
                      null,
                      cluster.getClusterDescription().getAutoScalingMode());
              _autoScalingContextDao.save(context);
            });

    _ndsAutoScaleSvc =
        new NDSReactiveAutoScaleSvc(
            _ndsAutoScaleAlertSvc,
            _ndsClusterSvc,
            _groupSvc,
            _appSettings,
            _autoScalingContextDao,
            _hostClusterLifecycleSvc,
            _auditSvc,
            _ndsGroupSvc,
            _ndsComputeClusterMetricsSvc,
            _computeClusterMetricsCalculatorSvc,
            _awsCapacityDenylistSvc,
            _gcpCapacityDenylistSvc,
            _informationalAlertSvc,
            _chefServerStatusSvc,
            _azureCapacityDenylistSvc,
            _computeAutoScalingDecisionCoordinatorSvc,
            _ndsAutoScaleValidationSvc);
  }

  private GroupSvc getGroupSvc() {
    return _groupSvc;
  }

  private UserSvc getUserSvc() {
    return _userSvc;
  }

  private ClusterDescription setIsPaused(
      final ClusterDescription pClusterDescription, final boolean pIsPaused) {
    return pClusterDescription.copy().setIsPaused(pIsPaused).build();
  }

  private ClusterDescription updateIsPausedForTest(
      final ClusterDescription pClusterDescription, final boolean pIsPaused) {
    final ClusterDescription activeClusterDescription = setIsPaused(pClusterDescription, pIsPaused);
    _clusterDescriptionDao.save(activeClusterDescription);
    // make sure the merged cluster looks paused
    _clusterDescriptionUpdateDao
        .findByName(pClusterDescription.getGroupId(), pClusterDescription.getName())
        .map(cd -> setIsPaused(cd, pIsPaused))
        .ifPresent(_clusterDescriptionUpdateDao::save);
    return activeClusterDescription;
  }

  private ClusterDescription setMaxDiskSizeGB(
      final ClusterDescription pClusterDescription, final Integer pDiskSizeGB) {
    final AWSNDSInstanceSize currentInstanceSize =
        (AWSNDSInstanceSize)
            pClusterDescription
                .getOnlyInstanceSizeForProvider(CloudProvider.AWS, NodeType.ELECTABLE)
                .orElseThrow();
    final int iops =
        Optional.ofNullable(pDiskSizeGB)
            .map(diskSizeGB -> currentInstanceSize.getGP3StandardEBSIOPS(pDiskSizeGB))
            .orElse(0);

    final int diskThroughput =
        Optional.ofNullable(pDiskSizeGB)
            .map(diskSizeGB -> currentInstanceSize.getMinThroughput(pDiskSizeGB))
            .orElse(0);

    return Optional.ofNullable(pDiskSizeGB)
        .map(
            diskSizeGB ->
                (pClusterDescription)
                    .copy()
                    .mixin(ClusterDescriptionBuilderTestMixin::new)
                    .updateAllHardwareForProvider(
                        CloudProvider.AWS,
                        new AWSHardwareSpec.Builder()
                            .setDiskIOPS(iops)
                            .setDiskThroughput(diskThroughput))
                    .setDiskSizeGB(diskSizeGB)
                    .build())
        .orElse(pClusterDescription);
  }

  private ClusterDescription updateDiskSizeGBForTest(
      final ClusterDescription pClusterDescription, final Integer pDiskSizeGB) {
    final ClusterDescription activeClusterDescription =
        setMaxDiskSizeGB(pClusterDescription, pDiskSizeGB);
    _clusterDescriptionDao.save(activeClusterDescription);
    _clusterDescriptionUpdateDao
        .findByName(pClusterDescription.getGroupId(), pClusterDescription.getName())
        .map(cd -> setMaxDiskSizeGB(cd, pDiskSizeGB))
        .ifPresent(_clusterDescriptionUpdateDao::save);
    return activeClusterDescription;
  }

  private <CD extends ClusterDescription, B extends ClusterDescription.Builder<B, CD>>
      ClusterDescription updateAutoScalingFlagsForTest(
          final CD pClusterDescription,
          final boolean pDiskGBEnabled,
          final boolean pComputeEnabled,
          final boolean pComputeScaleDownEnabled,
          final AutoScaleInstanceSize pMinInstanceSize,
          final AutoScaleInstanceSize pMaxInstanceSize) {
    final ClusterDescription.Builder<B, CD> activeClusterDescriptionBuilder =
        pClusterDescription.copy();
    for (final CloudProvider cloudProvider : pClusterDescription.getCloudProviders()) {
      activeClusterDescriptionBuilder.setAutoScalingForProvider(
          cloudProvider,
          pClusterDescription
              .getAutoScalingForProvider(cloudProvider, NodeTypeFamily.BASE)
              .get()
              .getAutoScalingWithFlagsSet(
                  pDiskGBEnabled,
                  pComputeEnabled,
                  pComputeScaleDownEnabled,
                  pMinInstanceSize,
                  pMaxInstanceSize,
                  false),
          NodeTypeFamily.BASE);
    }
    final ClusterDescription activeClusterDescription = activeClusterDescriptionBuilder.build();

    _clusterDescriptionDao.save(activeClusterDescription);

    // make sure the merged cluster looks paused
    _clusterDescriptionUpdateDao
        .findByName(pClusterDescription.getGroupId(), pClusterDescription.getName())
        .map(
            clusterDescriptionUpdate -> {
              final ClusterDescription.Builder<?, ClusterDescription> updatedCDBuilder =
                  clusterDescriptionUpdate.copy();
              for (final CloudProvider cloudProvider :
                  clusterDescriptionUpdate.getCloudProviders()) {
                updatedCDBuilder.setAutoScalingForProvider(
                    cloudProvider,
                    pClusterDescription
                        .getAutoScalingForProvider(cloudProvider, NodeTypeFamily.BASE)
                        .get()
                        .getAutoScalingWithFlagsSet(
                            pDiskGBEnabled,
                            pComputeEnabled,
                            pComputeScaleDownEnabled,
                            pMinInstanceSize,
                            pMaxInstanceSize,
                            false),
                    NodeTypeFamily.BASE);
              }
              return updatedCDBuilder.build();
            })
        .ifPresent(_clusterDescriptionUpdateDao::save);
    return activeClusterDescription;
  }

  private AutoScalingContext setAutoScalingContextFlags(
      final AutoScalingContext pContext,
      final boolean pDiskGBNeedsCheck,
      final boolean pComputeNeedsCheck,
      final Date pMaxInstanceSizeScalingFailDate,
      final AutoScalingMode pAutoScalingMode) {
    return pAutoScalingMode.equals(AutoScalingMode.CLUSTER)
        ? setAutoScalingContextFlags(
            pContext,
            pDiskGBNeedsCheck,
            pComputeNeedsCheck,
            pMaxInstanceSizeScalingFailDate,
            null,
            null)
        : setAutoScalingContextFlagsForShards(
            pContext,
            pDiskGBNeedsCheck,
            pComputeNeedsCheck,
            pMaxInstanceSizeScalingFailDate,
            null,
            null);
  }

  private AutoScalingContext setAutoScalingContextFlags(
      final AutoScalingContext pContext,
      final boolean pDiskGBNeedsCheck,
      final boolean pComputeNeedsCheck,
      final Date pMaxInstanceSizeScalingFailDate,
      final Date pLastScaleUpInitiated,
      final Date pLastScaleDownInitiated) {
    return pContext.toBuilder()
        .diskGBContext(pContext.getDiskGBContext().builder().needsCheck(pDiskGBNeedsCheck).build())
        .baseComputeContext(
            ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
                .toBuilder()
                    .needsCheck(pComputeNeedsCheck)
                    .maxInstanceSizeScalingFailedDate(pMaxInstanceSizeScalingFailDate)
                    .lastScaleUpInitiated(pLastScaleUpInitiated)
                    .lastScaleDownInitiated(pLastScaleDownInitiated)
                    .build())
        .build();
  }

  private AutoScalingContext setAutoScalingContextFlagsForShards(
      final AutoScalingContext pContext,
      final boolean pDiskGBNeedsCheck,
      final boolean pComputeNeedsCheck,
      final Date pMaxInstanceSizeScalingFailDate,
      final Date pLastScaleUpInitiated,
      final Date pLastScaleDownInitiated) {
    return pContext.toBuilder()
        .diskGBContext(pContext.getDiskGBContext().builder().needsCheck(pDiskGBNeedsCheck).build())
        .baseComputeContext(
            ((ShardComputeAutoScalingContext) pContext.getBaseComputeContext())
                .toBuilder()
                    .shardContexts(
                        ((ShardComputeAutoScalingContext) pContext.getBaseComputeContext())
                            .getShardContexts().stream()
                                .map(
                                    sc ->
                                        sc.toBuilder()
                                            .setNeedsCheck(pComputeNeedsCheck)
                                            .setMaxInstanceSizeScalingFailedDate(
                                                pMaxInstanceSizeScalingFailDate)
                                            .setLastScaleUpInitiated(pLastScaleUpInitiated)
                                            .setLastScaleDownInitiated(pLastScaleDownInitiated)
                                            .build())
                                .toList())
                    .build())
        .build();
  }

  private AutoScalingContext updateAutoScalingContextFlagsForTest(
      final AutoScalingContext pContext,
      final boolean pDiskGBNeedsCheck,
      final boolean pComputeNeedsCheck,
      final Date pMaxInstanceSizeScalingFailDate,
      final AutoScalingMode pAutoScalingMode) {
    return updateAutoScalingContextFlagsForTest(
        pContext,
        pDiskGBNeedsCheck,
        pComputeNeedsCheck,
        pMaxInstanceSizeScalingFailDate,
        null,
        null,
        pAutoScalingMode);
  }

  private AutoScalingContext updateAutoScalingContextFlagsForTest(
      final AutoScalingContext pContext,
      final boolean pDiskGBNeedsCheck,
      final boolean pComputeNeedsCheck,
      final Date pMaxInstanceSizeScalingFailDate,
      final Date pLastScaleUpInitiated,
      final Date pLastScaleDownInitiated,
      final AutoScalingMode pAutoScalingMode) {
    final AutoScalingContext asc =
        pAutoScalingMode.equals(AutoScalingMode.CLUSTER)
            ? setAutoScalingContextFlags(
                pContext,
                pDiskGBNeedsCheck,
                pComputeNeedsCheck,
                pMaxInstanceSizeScalingFailDate,
                pLastScaleUpInitiated,
                pLastScaleDownInitiated)
            : setAutoScalingContextFlagsForShards(
                pContext,
                pDiskGBNeedsCheck,
                pComputeNeedsCheck,
                pMaxInstanceSizeScalingFailDate,
                pLastScaleUpInitiated,
                pLastScaleDownInitiated);
    _autoScalingContextDao.save(asc);
    return asc;
  }

  private Pair<ClusterDescription, AutoScalingContext> testCheckAutoScaleForCompute_setup(
      final Cluster pCluster,
      final NDSReactiveAutoScaleSvc pAutoScaleSpy,
      final ComputeClusterMetricsCalculatorSvc pComputeClusterMetricsCalculatorSvcSpy,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled,
      final Float pCPUMetricUp,
      final Float pMemoryMetricUp,
      final Float pCPUMetricDown,
      final Float pMemoryMetricDown,
      final boolean pAtMaxInstanceSize,
      final Date pMaxInstanceSizeScalingFailedDate) {
    return testCheckAutoScaleForCompute_setup(
        pCluster,
        pAutoScaleSpy,
        pComputeClusterMetricsCalculatorSvcSpy,
        pNeedsCheck,
        pEnabled,
        pScaleDownEnabled,
        pCPUMetricUp,
        pMemoryMetricUp,
        pCPUMetricDown,
        pMemoryMetricDown,
        pAtMaxInstanceSize,
        pMaxInstanceSizeScalingFailedDate,
        null,
        null,
        List.of(Date.from(Instant.EPOCH)),
        false,
        List.of(Pair.of(false, null)),
        null,
        null);
  }

  private Pair<ClusterDescription, AutoScalingContext> testCheckAutoScaleForCompute_setup(
      final Cluster pCluster,
      final NDSReactiveAutoScaleSvc pAutoScaleSpy,
      final ComputeClusterMetricsCalculatorSvc pComputeClusterMetricsCalculatorSvcSpy,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled,
      final Float pCPUMetricUp,
      final Float pMemoryMetricUp,
      final Float pCPUMetricDown,
      final Float pMemoryMetricDown,
      final boolean pAtMaxInstanceSize,
      final AutoScaleInstanceSize pMinInstanceSize,
      final Integer pDiskSizeGB) {
    return testCheckAutoScaleForCompute_setup(
        pCluster,
        pAutoScaleSpy,
        pComputeClusterMetricsCalculatorSvcSpy,
        pNeedsCheck,
        pEnabled,
        pScaleDownEnabled,
        pCPUMetricUp,
        pMemoryMetricUp,
        pCPUMetricDown,
        pMemoryMetricDown,
        pAtMaxInstanceSize,
        null,
        null,
        null,
        List.of(Date.from(Instant.EPOCH)),
        false,
        List.of(Pair.of(false, null)),
        pMinInstanceSize,
        pDiskSizeGB);
  }

  private Pair<ClusterDescription, AutoScalingContext> testCheckAutoScaleForCompute_setup(
      final Cluster pCluster,
      final NDSReactiveAutoScaleSvc pAutoScaleSpy,
      final ComputeClusterMetricsCalculatorSvc pComputeClusterMetricsCalculatorSvcSpy,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled,
      final Float pCPUMetricUp,
      final Float pMemoryMetricUp,
      final Float pCPUMetricDown,
      final Float pMemoryMetricDown,
      final boolean pAtMaxInstanceSize,
      final Date pMaxInstanceSizeScalingFailedDate,
      final Date pLastScaleUpInitiated,
      final Date pLastScaleDownInitiated,
      final List<Date> pLastInstanceSizeModifyDates) {
    return testCheckAutoScaleForCompute_setup(
        pCluster,
        pAutoScaleSpy,
        pComputeClusterMetricsCalculatorSvcSpy,
        pNeedsCheck,
        pEnabled,
        pScaleDownEnabled,
        pCPUMetricUp,
        pMemoryMetricUp,
        pCPUMetricDown,
        pMemoryMetricDown,
        pAtMaxInstanceSize,
        pMaxInstanceSizeScalingFailedDate,
        pLastScaleUpInitiated,
        pLastScaleDownInitiated,
        pLastInstanceSizeModifyDates,
        false,
        List.of(Pair.of(false, null)),
        null,
        null);
  }

  private Pair<ClusterDescription, AutoScalingContext> testCheckAutoScaleForCompute_setup(
      final Cluster pCluster,
      final NDSReactiveAutoScaleSvc pAutoScaleSpy,
      final ComputeClusterMetricsCalculatorSvc pComputeClusterMetricsCalculatorSvcSpy,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled,
      final Float pCPUMetricUp,
      final Float pMemoryMetricUp,
      final Float pCPUMetricDown,
      final Float pMemoryMetricDown,
      final boolean pAtMaxInstanceSize,
      final Date pMaxInstanceSizeScalingFailedDate,
      final boolean pClusterIsPaused,
      final List<Pair<Boolean, Date>> pPausedAndLastResumeFromPauseDates) {
    return testCheckAutoScaleForCompute_setup(
        pCluster,
        pAutoScaleSpy,
        pComputeClusterMetricsCalculatorSvcSpy,
        pNeedsCheck,
        pEnabled,
        pScaleDownEnabled,
        pCPUMetricUp,
        pMemoryMetricUp,
        pCPUMetricDown,
        pMemoryMetricDown,
        pAtMaxInstanceSize,
        pMaxInstanceSizeScalingFailedDate,
        null,
        null,
        List.of(Date.from(Instant.EPOCH)),
        pClusterIsPaused,
        pPausedAndLastResumeFromPauseDates,
        null,
        null);
  }

  private Pair<ClusterDescription, AutoScalingContext> testCheckAutoScaleForCompute_setup(
      final Cluster pCluster,
      final NDSReactiveAutoScaleSvc pAutoScaleSpy,
      final ComputeClusterMetricsCalculatorSvc pComputeClusterMetricsCalculatorSvc,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled,
      final Float pCPUMetricUp,
      final Float pMemoryMetricUp,
      final Float pCPUMetricDown,
      final Float pMemoryMetricDown,
      final boolean pAtMaxInstanceSize,
      final Date pMaxInstanceSizeScalingFailedDate,
      final Date pLastScaleUpInitiated,
      final Date pLastScaleDownInitiated,
      final List<Date> pLastInstanceSizeModifyDates,
      final boolean pClusterIsPaused,
      final List<Pair<Boolean, Date>> pPausedAndLastResumeFromPauseDates,
      final AutoScaleInstanceSize pMinInstanceSize,
      final Integer pDiskSizeGB) {
    reset(pAutoScaleSpy);
    final ClusterDescription clusterDescriptionBeforeUpdate =
        _ndsClusterSvc
            .getActiveClusterDescription(_ndsGroupId, pCluster.getClusterDescription().getName())
            .get();
    final ClusterDescription clusterDescription =
        Optional.of(
                updateAutoScalingFlagsForTest(
                    clusterDescriptionBeforeUpdate,
                    true,
                    pEnabled,
                    pScaleDownEnabled,
                    pMinInstanceSize,
                    pAtMaxInstanceSize
                        ? clusterDescriptionBeforeUpdate
                            .getAutoScaleInstanceSizeFromReplicationSpecId(
                                clusterDescriptionBeforeUpdate
                                    .getReplicationSpecsWithShardData()
                                    .get(0)
                                    .getId(),
                                NodeTypeFamily.BASE)
                            .orElseThrow()
                        : null))
            .map(cd -> updateIsPausedForTest(cd, pClusterIsPaused))
            .map(cd -> updateDiskSizeGBForTest(cd, pDiskSizeGB))
            .get();
    final AutoScalingContext context =
        updateAutoScalingContextFlagsForTest(
            _autoScalingContextDao
                .find(clusterDescription.getGroupId(), clusterDescription.getName())
                .get(),
            false,
            pNeedsCheck,
            pMaxInstanceSizeScalingFailedDate,
            pLastScaleUpInitiated,
            pLastScaleDownInitiated,
            pCluster.getClusterDescription().getAutoScalingMode());

    final Map<SingleScalingCriteria, AutoScalingCalculatedMetrics> calculatorServiceResult =
        new HashMap<>();
    context
        .getBaseComputeContext()
        .getUpScalingCriterias()
        .forEach(
            criteria -> {
              final AutoScalingCalculatedMetrics.Builder autoScalingCalculatedMetricsBuilder =
                  AutoScalingCalculatedMetrics.builder();
              for (final ComputeAutoScalingThreshold<? extends ThresholdMetrics> threshold :
                  criteria.getThresholds()) {
                if (threshold instanceof ComputeAutoScalingCPUThreshold pCpuThreshold) {
                  final float cpuMetric =
                      pCpuThreshold.getCpuScaleThreshold()
                          + (pCPUMetricUp != null ? pCPUMetricUp : 0);
                  autoScalingCalculatedMetricsBuilder.addThresholdMetric(
                      pCpuThreshold,
                      CPUThresholdMetrics.builder()
                          .absoluteCpuMetric(cpuMetric)
                          .relativeCpuMetric(cpuMetric)
                          .build());
                }

                if (threshold instanceof ComputeAutoScalingMemoryThreshold pMemoryThreshold) {
                  final float calculatedMemoryMetric =
                      pMemoryThreshold.getMemoryScaleThreshold()
                          + (pMemoryMetricUp != null ? pMemoryMetricUp : 0);
                  autoScalingCalculatedMetricsBuilder.addThresholdMetric(
                      pMemoryThreshold,
                      MemoryThresholdMetrics.builder()
                          .memoryCalculatedMetric(calculatedMemoryMetric)
                          .build());
                }
              }
              calculatorServiceResult.put(criteria, autoScalingCalculatedMetricsBuilder.build());
            });

    context
        .getBaseComputeContext()
        .getDownScalingCriterias()
        .forEach(
            criteria -> {
              final AutoScalingCalculatedMetrics.Builder autoScalingCalculatedMetricsBuilder =
                  AutoScalingCalculatedMetrics.builder();
              for (final ComputeAutoScalingThreshold<? extends ThresholdMetrics> threshold :
                  criteria.getThresholds()) {
                if (threshold instanceof ComputeAutoScalingCPUThreshold pCpuThreshold) {
                  final float cpuMetric =
                      pCpuThreshold.getCpuScaleThreshold()
                          + (pCPUMetricDown != null ? pCPUMetricDown : 0);
                  autoScalingCalculatedMetricsBuilder.addThresholdMetric(
                      pCpuThreshold,
                      CPUThresholdMetrics.builder()
                          .absoluteCpuMetric(cpuMetric)
                          .relativeCpuMetric(cpuMetric)
                          .build());
                }

                if (threshold instanceof ComputeAutoScalingMemoryThreshold pMemoryThreshold) {
                  final float calculatedMemoryMetric =
                      pMemoryThreshold.getMemoryScaleThreshold()
                          + (pMemoryMetricDown != null ? pMemoryMetricDown : 0);
                  autoScalingCalculatedMetricsBuilder.addThresholdMetric(
                      pMemoryThreshold,
                      MemoryThresholdMetrics.builder()
                          .memoryCalculatedMetric(calculatedMemoryMetric)
                          .build());
                }

                if (threshold instanceof ComputeAutoScalingWTUsageThreshold pWTUsageThreshold) {
                  autoScalingCalculatedMetricsBuilder.addThresholdMetric(
                      pWTUsageThreshold, new WTUsageThresholdMetrics(0.01f, null));
                }

                if (threshold instanceof ComputeAutoScalingIntervalThreshold pIntervalThreshold) {
                  autoScalingCalculatedMetricsBuilder.addThresholdMetric(
                      pIntervalThreshold, new IntervalThresholdMetrics(new Date(0), new Date(0)));
                }
              }
              calculatorServiceResult.put(criteria, autoScalingCalculatedMetricsBuilder.build());
            });

    setLastInstanceSizeModifyDateForReplicaSetHardware(
        clusterDescription, pLastInstanceSizeModifyDates);
    setLastResumeFromPauseDateForReplicaSetHardware(
        clusterDescription, pPausedAndLastResumeFromPauseDates);

    setupAppSettingsPropsForComputeAutoScaling();

    doReturn(calculatorServiceResult)
        .when(pComputeClusterMetricsCalculatorSvc)
        .getClusterMetricsForCriteria(any(), any(), any());

    // it is also difficult to prepare data for the stable oplog condition, so we mock it.
    doReturn(Result.ok(null))
        .when(_ndsAutoScaleValidationSvc)
        .canBeAutoScaledWithinOplogTimeWindow(any(), any(), any(), any());

    return Pair.of(clusterDescription, context);
  }

  private Pair<ClusterDescription, AutoScalingContext> testCheckAutoScaleForComputeShards_setup(
      final Cluster pCluster,
      final NDSReactiveAutoScaleSvc pAutoScaleSpy,
      final ComputeClusterMetricsCalculatorSvc pComputeClusterMetricsCalculatorSvc,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled,
      final boolean pAtMaxInstanceSize,
      final Date pMaxInstanceSizeScalingFailedDate,
      final Date pLastScaleUpInitiated,
      final Date pLastScaleDownInitiated,
      final AutoScaleInstanceSize pMinInstanceSize,
      final List<String> rsIdsToScaleUp,
      final List<String> rsIdsToScaleDown,
      final List<String> rsIdsToNotChange,
      final boolean canBeAutoScaledWithinOplogWindow) {
    reset(pAutoScaleSpy);
    reset(pComputeClusterMetricsCalculatorSvc);

    final ClusterDescription clusterDescriptionBeforeUpdate =
        _ndsClusterSvc
            .getActiveClusterDescription(_ndsGroupId, pCluster.getClusterDescription().getName())
            .orElseThrow();

    final ClusterDescription clusterDescription =
        Optional.of(
                updateAutoScalingFlagsForTest(
                    clusterDescriptionBeforeUpdate,
                    true,
                    pEnabled,
                    pScaleDownEnabled,
                    pMinInstanceSize,
                    pAtMaxInstanceSize
                        ? clusterDescriptionBeforeUpdate
                            .getAutoScaleInstanceSizeFromReplicationSpecId(
                                clusterDescriptionBeforeUpdate
                                    .getReplicationSpecsWithShardData()
                                    .get(0)
                                    .getId(),
                                NodeTypeFamily.BASE)
                            .orElseThrow()
                        : null))
            .get();
    final AutoScalingContext context =
        updateAutoScalingContextFlagsForTest(
            _autoScalingContextDao
                .find(clusterDescription.getGroupId(), clusterDescription.getName())
                .get(),
            false,
            pNeedsCheck,
            pMaxInstanceSizeScalingFailedDate,
            pLastScaleUpInitiated,
            pLastScaleDownInitiated,
            pCluster.getClusterDescription().getAutoScalingMode());
    setupAppSettingsPropsForComputeAutoScaling();

    setLastInstanceSizeModifyDateForReplicaSetHardware(clusterDescription, List.of(new Date(0)));

    final Map<SingleScalingCriteria, AutoScalingCalculatedMetrics> upscaleMap = new HashMap<>();
    {
      final SingleScalingCriteria targetCriteria =
          ComputeAutoScalingContext.getDefaultBaseComputeUpScalingCriterias().get(0);
      final AutoScalingCalculatedMetrics metric =
          new AutoScalingCalculatedMetrics.Builder()
              .addThresholdMetric(
                  (ComputeAutoScalingCPUThreshold) targetCriteria.getThresholds().get(0),
                  CPUThresholdMetrics.builder()
                      .absoluteCpuMetric(0.99f)
                      .relativeCpuMetric(0.99f)
                      .build())
              .build();
      upscaleMap.put(targetCriteria, metric);
    }

    final Map<SingleScalingCriteria, AutoScalingCalculatedMetrics> downscaleMap = new HashMap<>();
    {
      final SingleScalingCriteria targetCriteria =
          ComputeAutoScalingContext.getDefaultBaseComputeDownScalingCriterias().get(0);
      final AutoScalingCalculatedMetrics metric =
          new AutoScalingCalculatedMetrics.Builder()
              .addThresholdMetric(
                  (ComputeAutoScalingCPUThreshold) targetCriteria.getThresholds().get(0),
                  CPUThresholdMetrics.builder()
                      .absoluteCpuMetric(0.01f)
                      .relativeCpuMetric(0.01f)
                      .build())
              .addThresholdMetric(
                  (ComputeAutoScalingMemoryThreshold) targetCriteria.getThresholds().get(1),
                  MemoryThresholdMetrics.builder().memoryCalculatedMetric(0.01f).build())
              .addThresholdMetric(
                  (ComputeAutoScalingWTUsageThreshold) targetCriteria.getThresholds().get(2),
                  new WTUsageThresholdMetrics(0.01f, null))
              .addThresholdMetric(
                  (ComputeAutoScalingIntervalThreshold) targetCriteria.getThresholds().get(3),
                  new IntervalThresholdMetrics(new Date(0), new Date(0)))
              .build();
      downscaleMap.put(targetCriteria, metric);
    }
    final Map<SingleScalingCriteria, AutoScalingCalculatedMetrics> noscaleMap = Map.of();

    rsIdsToScaleUp.forEach(
        rs ->
            doReturn(upscaleMap)
                .when(pComputeClusterMetricsCalculatorSvc)
                .getMetricsForCriteria(eq(Optional.of(rs)), any(), any(), any()));
    rsIdsToScaleDown.forEach(
        rs ->
            doReturn(downscaleMap)
                .when(pComputeClusterMetricsCalculatorSvc)
                .getMetricsForCriteria(eq(Optional.of(rs)), any(), any(), any()));
    rsIdsToNotChange.forEach(
        rs ->
            doReturn(noscaleMap)
                .when(pComputeClusterMetricsCalculatorSvc)
                .getMetricsForCriteria(eq(Optional.of(rs)), any(), any(), any()));

    // it is also difficult to prepare data for the stable oplog condition, so we mock it.
    final Result<OplogEstimate, OplogEstimate> canBeAutoScaledResult =
        canBeAutoScaledWithinOplogWindow
            ? Result.ok(null)
            : Result.err(new OplogEstimate(100, 1000));
    doReturn(canBeAutoScaledResult)
        .when(_ndsAutoScaleValidationSvc)
        .canBeAutoScaledWithinOplogTimeWindow(any(), any(), any(), any());

    return Pair.of(clusterDescription, context);
  }

  private void setupAppSettingsPropsForComputeAutoScaling() {
    _appSettings.setProp(
        AppSettings.Fields.NDS_AUTO_SCALING_SCALE_DOWN_DAYS.value,
        "MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY",
        AppSettings.SettingType.MEMORY);
    _appSettings.setProp(
        AppSettings.Fields.NDS_AUTO_SCALING_SCALE_DOWN_HOURS.value,
        "0:00, 23:59",
        AppSettings.SettingType.MEMORY);
    _appSettings.setProp(DEFAULT_SCALE_DOWN_TIME_ZONE, "UTC", AppSettings.SettingType.MEMORY);
  }

  private void validateCheckAutoScaleForComputeAudits(
      final Date pStartTime,
      final Date pEndTime,
      final int pNumAuditToQuery,
      final Set<NDSAutoScalingAudit.Type> pAuditTypes,
      final int pNumExpectedAudits) {
    assertEquals(
        pNumExpectedAudits,
        _auditSvc
            .findAll(
                pNumAuditToQuery,
                pAuditTypes.stream()
                    .map(NDSAutoScalingAudit.Type::name)
                    .collect(Collectors.toList()),
                pStartTime,
                pEndTime)
            .size());
  }

  private void validateCheckAutoScaleForCompute(
      final Date pStartTime,
      final ClusterDescription pClusterDescription,
      final ClusterDescription pUpdatedClusterDescription,
      final AutoScalingContext pContext,
      final AutoScalingContext pUpdatedContext,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled,
      final boolean pScaleInitiated,
      final boolean pScaleUpInitiated,
      final boolean pInstanceSizeChangeInProgress,
      final boolean pAtMaxInstanceSizeForScaleUp,
      final Date pMaxInstanceSizeScalingFailedDate,
      final Date pMaxInstanceSizeFailEmailSentAfterDate)
      throws Exception {
    final Date now = new Date();

    if (!pEnabled
        || !pNeedsCheck
        || pInstanceSizeChangeInProgress
        || pAtMaxInstanceSizeForScaleUp) {
      if (!pEnabled || (pInstanceSizeChangeInProgress || pAtMaxInstanceSizeForScaleUp)) {
        assertFalse(
            ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .isNeedsCheck());
      } else {
        assertEquals(
            pNeedsCheck,
            ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .isNeedsCheck());
        assertEquals(pContext, pUpdatedContext);
      }
      if (pInstanceSizeChangeInProgress) {
        assertNotEquals(
            pClusterDescription.getInstanceSizes(NodeType.ELECTABLE),
            pUpdatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));
      } else {
        assertEquals(
            pClusterDescription.getInstanceSizes(NodeType.ELECTABLE),
            pUpdatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));
      }
      if (pAtMaxInstanceSizeForScaleUp && pEnabled && pNeedsCheck) {
        final Event autoscalingAuditEvent =
            _auditSvc.findMostRecentByEventTypeForCluster(
                pClusterDescription.getGroupId(),
                pClusterDescription.getName(),
                Type.COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_BASE);

        if (pMaxInstanceSizeScalingFailedDate != null
            && pMaxInstanceSizeScalingFailedDate
                .toInstant()
                .isAfter(
                    Instant.now()
                        .minus(
                            Duration.ofDays(
                                NDSAutoScaleAlertSvc
                                    .COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_ALERT_WAIT_DAYS)))) {
          // Max instance size scaling failed happened during the window that we don't send alert,
          // the audit event should be from before
          assertTrue(
              autoscalingAuditEvent
                  .getCreatedAt()
                  .toInstant()
                  .isBefore(pMaxInstanceSizeFailEmailSentAfterDate.toInstant()));
        } else {
          // Confirm that audit event was saved
          assertTrue(
              autoscalingAuditEvent
                  .getCreatedAt()
                  .toInstant()
                  .isAfter(pMaxInstanceSizeFailEmailSentAfterDate.toInstant()));
        }
      }
      if (!pEnabled) {
        // ensure no side-effects on context other than resetting needsCheck
        final AutoScalingContext updatedContextForComparison =
            pContext.toBuilder()
                .baseComputeContext(
                    ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
                        .toBuilder().needsCheck(false).build())
                .build();
        assertEquals(updatedContextForComparison, pUpdatedContext);
        validateCheckAutoScaleForComputeAudits(
            pStartTime,
            now,
            2,
            Set.of(
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_BASE,
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS),
            0);
      } else if (pNeedsCheck) {
        validateCheckAutoScaleForComputeAudits(
            pStartTime,
            now,
            2,
            Set.of(
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_BASE,
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS),
            0);
      }
      assertEquals(
          ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
              .getLastScaleUpInitiated(),
          ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
              .getLastScaleUpInitiated());
      assertEquals(
          ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
              .getLastScaleDownInitiated(),
          ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
              .getLastScaleDownInitiated());
    } else {
      assertFalse(
          ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
              .isNeedsCheck());
      if (pScaleInitiated && pScaleUpInitiated) {
        pClusterDescription
            .getCloudProviders()
            .forEach(
                provider ->
                    assertTrue(
                        pClusterDescription
                                .getMinInstanceSizeForProvider(NodeType.ELECTABLE, provider)
                                .orElseThrow()
                                .compareByTypes(
                                    pUpdatedClusterDescription
                                        .getMinInstanceSizeForProvider(NodeType.ELECTABLE, provider)
                                        .orElseThrow())
                            < 0));
        assertNotEquals(
            ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
                .getLastScaleUpInitiated(),
            ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .getLastScaleUpInitiated());
        assertEquals(
            ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
                .getLastScaleDownInitiated(),
            ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .getLastScaleDownInitiated());
        validateCheckAutoScaleForComputeAudits(
            pStartTime,
            now,
            2,
            Set.of(NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_BASE),
            2);
        validateCheckAutoScaleForComputeAudits(
            pStartTime,
            now,
            2,
            Set.of(NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS),
            0);
      } else if (pScaleDownEnabled && pScaleInitiated) {
        assertEquals(
            pClusterDescription.getCloudProviders(),
            pUpdatedClusterDescription.getCloudProviders());
        pClusterDescription
            .getCloudProviders()
            .forEach(
                provider ->
                    assertTrue(
                        pClusterDescription
                                .getMinInstanceSizeForProvider(NodeType.ELECTABLE, provider)
                                .orElseThrow()
                                .compareByTypes(
                                    pUpdatedClusterDescription
                                        .getMinInstanceSizeForProvider(NodeType.ELECTABLE, provider)
                                        .orElseThrow())
                            > 0,
                        String.format(
                            "%s -> %s",
                            pClusterDescription.getInstanceSizes(NodeType.ELECTABLE),
                            pUpdatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE))));
        assertEquals(
            ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
                .getLastScaleUpInitiated(),
            ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .getLastScaleUpInitiated());
        assertNotEquals(
            ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
                .getLastScaleDownInitiated(),
            ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .getLastScaleDownInitiated());
        validateCheckAutoScaleForComputeAudits(
            pStartTime,
            now,
            2,
            Set.of(NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_BASE),
            2);
        validateCheckAutoScaleForComputeAudits(
            pStartTime,
            now,
            2,
            Set.of(NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS),
            0);
      } else {
        assertEquals(
            pClusterDescription.getInstanceSizes(NodeType.ELECTABLE),
            pUpdatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));
        assertEquals(
            ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
                .getLastScaleUpInitiated(),
            ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .getLastScaleUpInitiated());
        assertEquals(
            ((ClusterComputeAutoScalingContext) pContext.getBaseComputeContext())
                .getLastScaleDownInitiated(),
            ((ClusterComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .getLastScaleDownInitiated());
        validateCheckAutoScaleForComputeAudits(
            pStartTime,
            now,
            2,
            Set.of(
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_BASE,
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS),
            0);
      }
    }
    if (!pInstanceSizeChangeInProgress && !pAtMaxInstanceSizeForScaleUp) {
      // reset the update. note, if the cluster description is paused, reset that before saving
      // since we cannot pause a cluster with pending changes
      _ndsClusterSvc.updateCluster(
          updateIsPausedForTest(pClusterDescription, false),
          null,
          AuditInfoHelpers.fromSystem(),
          null,
          ClusterUpdateContext.forIntTestAction());
    }
  }

  private void validateCheckAutoScaleForComputeShards(
      final Date pStartTime,
      final ClusterDescription pClusterDescription,
      final ClusterDescription pUpdatedClusterDescription,
      final AutoScalingContext pContext,
      final AutoScalingContext pUpdatedContext,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pInstanceSizeChangeInProgress,
      final List<ReplicaSetHardware> pReplicaSetScaleUp,
      final List<ReplicaSetHardware> pReplicaSetScaleDown,
      final List<ReplicaSetHardware> pReplicaSetNoChange)
      throws Exception {
    final Date now = new Date();
    if (!pEnabled || !pNeedsCheck || pInstanceSizeChangeInProgress) {
      if (!pEnabled || (pInstanceSizeChangeInProgress)) {
        assertFalse(pUpdatedContext.getBaseComputeContext().isAnyNeedsCheckSet());
      } else {
        assertEquals(pNeedsCheck, pUpdatedContext.getBaseComputeContext().isAnyNeedsCheckSet());
        assertEquals(pContext, pUpdatedContext);
      }
      if (pInstanceSizeChangeInProgress) {
        assertNotEquals(
            pClusterDescription.getInstanceSizes(NodeType.ELECTABLE),
            pUpdatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));
      } else {
        assertEquals(
            pClusterDescription.getInstanceSizes(NodeType.ELECTABLE),
            pUpdatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));
      }

      if (!pEnabled) {
        // ensure no side-effects on context other than resetting needsCheck
        assertFalse(pUpdatedContext.getBaseComputeContext().isAnyNeedsCheckSet());
        validateCheckAutoScaleForComputeAudits(
            pStartTime,
            now,
            2,
            Set.of(
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_BASE,
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS),
            0);
      } else if (pNeedsCheck) {
        validateCheckAutoScaleForComputeAudits(
            pStartTime,
            now,
            2,
            Set.of(
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_BASE,
                NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS),
            0);
      }

      final List<ShardContext> shardContexts =
          ((ShardComputeAutoScalingContext) pContext.getBaseComputeContext()).getShardContexts();
      final List<ShardContext> updatedShardContexts =
          ((ShardComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
              .getShardContexts();
      IntStream.range(0, shardContexts.size())
          .forEach(
              i -> {
                assertEquals(
                    shardContexts.get(i).getLastScaleUpInitiated(),
                    updatedShardContexts.get(i).getLastScaleUpInitiated());
                assertEquals(
                    shardContexts.get(i).getLastScaleDownInitiated(),
                    updatedShardContexts.get(i).getLastScaleDownInitiated());
              });
    } else {
      assertFalse(pUpdatedContext.getBaseComputeContext().isAnyNeedsCheckSet());

      for (ReplicaSetHardware rsh : pReplicaSetScaleUp) {
        final ReplicationSpec rs =
            pClusterDescription.getReplicationSpecById(rsh.getReplicationSpecId()).orElseThrow();
        final ReplicationSpec updatedRs =
            pUpdatedClusterDescription
                .getReplicationSpecById(rsh.getReplicationSpecId())
                .orElseThrow();

        assertTrue(
            updatedRs.getElectableInstanceSize().isGreaterThan(rs.getElectableInstanceSize()),
            String.format(
                "%s should be greater than %s",
                updatedRs.getElectableInstanceSize(), rs.getElectableInstanceSize()));

        final ShardContext sc =
            ((ShardComputeAutoScalingContext) pContext.getBaseComputeContext())
                .getShardContextFromRsId(rsh.getRsId())
                .orElseThrow();
        final ShardContext updatedSc =
            ((ShardComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .getShardContextFromRsId(rsh.getRsId())
                .orElseThrow();

        assertNotEquals(sc.getLastScaleUpInitiated(), updatedSc.getLastScaleUpInitiated());
        assertEquals(sc.getLastScaleDownInitiated(), updatedSc.getLastScaleDownInitiated());
      }
      for (ReplicaSetHardware rsh : pReplicaSetScaleDown) {
        final ReplicationSpec rs =
            pClusterDescription.getReplicationSpecById(rsh.getReplicationSpecId()).orElseThrow();
        final ReplicationSpec updatedRs =
            pUpdatedClusterDescription
                .getReplicationSpecById(rsh.getReplicationSpecId())
                .orElseThrow();

        assertTrue(updatedRs.getElectableInstanceSize().isLessThan(rs.getElectableInstanceSize()));

        final ShardContext sc =
            ((ShardComputeAutoScalingContext) pContext.getBaseComputeContext())
                .getShardContextFromRsId(rsh.getRsId())
                .orElseThrow();
        final ShardContext updatedSc =
            ((ShardComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .getShardContextFromRsId(rsh.getRsId())
                .orElseThrow();

        assertEquals(sc.getLastScaleUpInitiated(), updatedSc.getLastScaleUpInitiated());
        assertNotEquals(sc.getLastScaleDownInitiated(), updatedSc.getLastScaleDownInitiated());
      }

      for (ReplicaSetHardware rsh : pReplicaSetNoChange) {
        final ReplicationSpec rs =
            pClusterDescription.getReplicationSpecById(rsh.getReplicationSpecId()).orElseThrow();
        final ReplicationSpec updatedRs =
            pUpdatedClusterDescription
                .getReplicationSpecById(rsh.getReplicationSpecId())
                .orElseThrow();

        assertTrue(updatedRs.getElectableInstanceSize().isEqualTo(rs.getElectableInstanceSize()));

        final ShardContext sc =
            ((ShardComputeAutoScalingContext) pContext.getBaseComputeContext())
                .getShardContextFromRsId(rsh.getRsId())
                .orElseThrow();
        final ShardContext updatedSc =
            ((ShardComputeAutoScalingContext) pUpdatedContext.getBaseComputeContext())
                .getShardContextFromRsId(rsh.getRsId())
                .orElseThrow();

        assertEquals(sc.getLastScaleUpInitiated(), updatedSc.getLastScaleUpInitiated());
        assertEquals(sc.getLastScaleDownInitiated(), updatedSc.getLastScaleDownInitiated());
      }
      validateCheckAutoScaleForComputeAudits(
          pStartTime,
          now,
          2,
          Set.of(NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_BASE),
          2);
      validateCheckAutoScaleForComputeAudits(
          pStartTime,
          now,
          2,
          Set.of(NDSAutoScalingAudit.Type.COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS),
          0);
    }

    if (!pInstanceSizeChangeInProgress) {
      // reset the update. note, if the cluster description is paused, reset that before saving
      // since we cannot pause a cluster with pending changes
      _ndsClusterSvc.updateCluster(
          updateIsPausedForTest(pClusterDescription, false),
          null,
          AuditInfoHelpers.fromSystem(),
          null,
          ClusterUpdateContext.forIntTestAction());
    }
  }

  private void testCheckAutoScaleForCompute(
      final Cluster pCluster,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled)
      throws Exception {
    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvcSpy =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvcSpy);

    final ClusterDescription activeClusterDescription =
        _ndsClusterSvc
            .getActiveClusterDescription(
                pCluster.getClusterDescription().getGroupId(),
                pCluster.getClusterDescription().getName())
            .get();
    final ClusterDescription mergedClusterDescription =
        _ndsClusterSvc
            .getMergedClusterDescription(
                pCluster.getClusterDescription().getGroupId(),
                pCluster.getClusterDescription().getName())
            .get();
    final boolean instanceSizeChangeInProgress =
        !activeClusterDescription
            .getInstanceSizes(NodeType.ELECTABLE)
            .equals(mergedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));

    final AutoScalingContext currentContext =
        _autoScalingContextDao
            .find(
                pCluster.getClusterDescription().getGroupId(),
                pCluster.getClusterDescription().getName())
            .get();
    final Long minCpuUpScalingLookbackWindow =
        currentContext.getBaseComputeContext().getUpScalingCriterias().stream()
            .flatMap(c -> c.getThresholds().stream())
            .filter(t -> t instanceof ComputeAutoScalingCPUThreshold)
            .map(ComputeAutoScalingThreshold::getScaleWindowSeconds)
            .min(Long::compare)
            .get();

    final Long maxCpuUpScalingLookbackWindow =
        currentContext.getBaseComputeContext().getUpScalingCriterias().stream()
            .flatMap(c -> c.getThresholds().stream())
            .filter(t -> t instanceof ComputeAutoScalingCPUThreshold)
            .map(ComputeAutoScalingThreshold::getScaleWindowSeconds)
            .max(Long::compare)
            .get();

    final Long minMemoryUpScalingLookBackWindow =
        currentContext.getBaseComputeContext().getUpScalingCriterias().stream()
            .flatMap(c -> c.getThresholds().stream())
            .filter(t -> t instanceof ComputeAutoScalingMemoryThreshold)
            .map(ComputeAutoScalingThreshold::getScaleWindowSeconds)
            .min(Long::compare)
            .get();

    final Long maxMemoryUpScalingLookbackWindow =
        currentContext.getBaseComputeContext().getUpScalingCriterias().stream()
            .flatMap(c -> c.getThresholds().stream())
            .filter(t -> t instanceof ComputeAutoScalingMemoryThreshold)
            .map(ComputeAutoScalingThreshold::getScaleWindowSeconds)
            .max(Long::compare)
            .get();

    final Long maxDownScalingLookbackWindow =
        currentContext.getBaseComputeContext().getDownScalingCriterias().stream()
            .map(SingleScalingCriteria::getMaxScalingWindowDurationForMetricsBasedThresholds)
            .map(d -> d.get().toSeconds())
            .max(Long::compare)
            .get();

    // test scale CPU above
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              null,
              null,
              null,
              false,
              null);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          true,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale memory above
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              .01f,
              null,
              null,
              false,
              null);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          true,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU and memory above
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              .01f,
              null,
              null,
              false,
              null);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          true,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU above and memory below
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              -.01f,
              null,
              null,
              false,
              null);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          true,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale memory above and CPU below
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              -.01f,
              .01f,
              null,
              null,
              false,
              null);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          true,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU below
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              null,
              false,
              null);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale memory below
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              null,
              -.01f,
              false,
              null);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU and memory below
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              false,
              null);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU above, cluster not ready for scaling up because of last instance size modify
    // date
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              null,
              null,
              null,
              false,
              null,
              Date.from(
                  Instant.now()
                      .minus(
                          Duration.ofSeconds(
                              maxCpuUpScalingLookbackWindow + 1))), // have not scaled up
              null,
              List.of(
                  Date.from(
                      Instant.now()
                          .minus(
                              Duration.ofSeconds(
                                  minCpuUpScalingLookbackWindow / 2))))); // just scaled down.
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale memory above, cluster not ready for scaling up because of last instance size
    // modify date
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              .01f,
              null,
              null,
              false,
              null,
              Date.from(
                  Instant.now()
                      .minus(
                          Duration.ofSeconds(
                              maxMemoryUpScalingLookbackWindow + 1))), // have not upScaled yet
              null,
              List.of(
                  Date.from(
                      Instant.now()
                          .minus(
                              Duration.ofSeconds(
                                  minMemoryUpScalingLookBackWindow
                                      / 2))))); // instance size just changed
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU above, cluster not ready for scaling up because of last resume date
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              null,
              null,
              null,
              false,
              null,
              false,
              List.of(
                  Pair.of(
                      false,
                      Date.from(
                          Instant.now()
                              .minus(Duration.ofSeconds(minCpuUpScalingLookbackWindow / 2)))),
                  // ensure the most recent resume date gets used for evaluation
                  Pair.of(
                      false,
                      Date.from(
                          Instant.now()
                              .minus(Duration.ofSeconds(maxCpuUpScalingLookbackWindow + 1))))));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale memory above, cluster not ready for scaling up because of last resume date
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              .01f,
              null,
              null,
              false,
              null,
              false,
              List.of(
                  Pair.of(
                      false,
                      Date.from(
                          Instant.now()
                              .minus(Duration.ofSeconds(minMemoryUpScalingLookBackWindow / 2)))),
                  // ensure the most recent resume date gets used for evaluation
                  Pair.of(
                      false,
                      Date.from(
                          Instant.now()
                              .minus(Duration.ofSeconds(maxMemoryUpScalingLookbackWindow + 1))))));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU above, cluster not ready for scaling up because cluster is paused
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              null,
              null,
              null,
              false,
              null,
              true,
              List.of(Pair.of(false, null)));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale memory above, cluster not ready for scaling up because cluster is paused
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              .01f,
              null,
              null,
              false,
              null,
              true,
              List.of(Pair.of(false, null)));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU above, cluster not ready for scaling up because instance hardware is paused
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              null,
              null,
              null,
              false,
              null,
              false,
              List.of(
                  Pair.of(true, null),
                  // ensure the instance hardware paused state overrides any valid hardware
                  Pair.of(
                      false,
                      Date.from(
                          Instant.now()
                              .minus(Duration.ofSeconds(maxCpuUpScalingLookbackWindow + 1))))));

      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale memory above, cluster not ready for scaling up because instance hardware is paused
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              .01f,
              null,
              null,
              false,
              null,
              false,
              List.of(
                  Pair.of(true, null),
                  // ensure the instance hardware paused state overrides any valid hardware
                  Pair.of(
                      false,
                      Date.from(
                          Instant.now()
                              .minus(Duration.ofSeconds(maxMemoryUpScalingLookbackWindow + 1))))));

      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU above, cluster not ready for scaling up because of last scale up initiated
    // date
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              null,
              null,
              null,
              false,
              null,
              Date.from(Instant.now().minus(Duration.ofSeconds(minCpuUpScalingLookbackWindow / 2))),
              null,
              List.of(
                  Date.from(
                      Instant.now().minus(Duration.ofSeconds(maxCpuUpScalingLookbackWindow + 1)))));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale memory above, cluster not ready for scaling up because of last scale up initiated
    // date
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              .01f,
              null,
              null,
              false,
              null,
              Date.from(
                  Instant.now().minus(Duration.ofSeconds(minMemoryUpScalingLookBackWindow / 2))),
              null,
              List.of(
                  Date.from(
                      Instant.now()
                          .minus(Duration.ofSeconds(maxMemoryUpScalingLookbackWindow + 1)))));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU and memory below, cluster not ready for scaling down because of last instance
    // size modify date
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              false,
              null,
              null,
              Date.from(Instant.now().minus(Duration.ofSeconds(maxDownScalingLookbackWindow + 1))),
              List.of(
                  Date.from(
                      Instant.now().minus(Duration.ofSeconds(maxDownScalingLookbackWindow / 2)))));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU and memory below, cluster not ready for scaling down because of last resume
    // date
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              false,
              null,
              false,
              List.of(
                  Pair.of(
                      false,
                      Date.from(
                          Instant.now()
                              .minus(Duration.ofSeconds(maxDownScalingLookbackWindow / 2)))),
                  // ensure the most recent resume date gets used for evaluation
                  Pair.of(
                      false,
                      Date.from(
                          Instant.now()
                              .minus(Duration.ofSeconds(maxDownScalingLookbackWindow + 1))))));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU and memory below, cluster not ready for scaling down because cluster is paused
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              false,
              null,
              true,
              List.of(Pair.of(false, null)));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU and memory below, cluster not ready for scaling down because instance
    // hardware is paused
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              false,
              null,
              false,
              List.of(
                  Pair.of(true, null),
                  // ensure the instance hardware paused state overrides any valid hardware
                  Pair.of(
                      false,
                      Date.from(
                          Instant.now()
                              .minus(Duration.ofSeconds(maxDownScalingLookbackWindow + 1))))));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }

    // test scale CPU and memory below, cluster not ready for scaling down because of last scale
    // down initiated date
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              false,
              null,
              null,
              Date.from(Instant.now().minus(Duration.ofSeconds(maxDownScalingLookbackWindow / 2))),
              List.of(
                  Date.from(
                      Instant.now().minus(Duration.ofSeconds(maxDownScalingLookbackWindow + 1)))));
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          instanceSizeChangeInProgress,
          false,
          null,
          null);
    }
  }

  private void doTestCheckComputeAutoScale(final Cluster pCluster) throws Exception {
    testCheckAutoScaleForCompute(pCluster, true, true, true);
    testCheckAutoScaleForCompute(pCluster, true, true, false);
    testCheckAutoScaleForCompute(pCluster, true, false, false);
    testCheckAutoScaleForCompute(pCluster, false, true, true);
    testCheckAutoScaleForCompute(pCluster, false, true, false);
    testCheckAutoScaleForCompute(pCluster, false, false, false);
  }

  private NDSReactiveAutoScaleSvc testCheckDiskGBAutoScale_setup(
      final NDSReactiveAutoScaleSvc pAutoScaleSpy,
      final AutoScalingContext autoScalingContext,
      final Double pDiskUsagePercentage,
      final boolean pOplogCanSupportAutoScale)
      throws SvcException {

    _autoScalingContextDao.save(autoScalingContext);

    reset(pAutoScaleSpy);

    doReturn(pDiskUsagePercentage)
        .when(pAutoScaleSpy)
        .getCurrentDiskUsagePercentageInDecimals(any(), any());
    final Result<OplogEstimate, OplogEstimate> canBeAutoScaledResult =
        pOplogCanSupportAutoScale ? Result.ok(null) : Result.err(new OplogEstimate(100, 1000));
    doReturn(canBeAutoScaledResult)
        .when(_ndsAutoScaleValidationSvc)
        .canBeAutoScaledWithinOplogTimeWindow(any(), any(), any(), any());

    return pAutoScaleSpy;
  }

  private void validateDiskGBAutoScalingAudits(
      final Date pStartTime,
      final Date pEndTime,
      final int pNumAuditToQuery,
      final Set<NDSAutoScalingAudit.Type> pAuditTypes,
      final Set<Integer> pNumAudits) {
    assertTrue(
        pNumAudits.contains(
            _auditSvc
                .findAll(
                    pNumAuditToQuery,
                    pAuditTypes.stream()
                        .map(NDSAutoScalingAudit.Type::name)
                        .collect(Collectors.toList()),
                    pStartTime,
                    pEndTime)
                .size()));
  }

  private void validateDiskGBAutoScaling(
      final ClusterDescription pClusterDescription,
      final Matcher<Double> pScaledDiskGBMatcher,
      final Set<NDSInstanceSize> pInstanceSize,
      final NodeTypeFamily pNodeTypeFamily) {
    final ObjectId groupId = pClusterDescription.getGroupId();
    final String clusterName = pClusterDescription.getName();

    // assert needs check is expected
    final boolean needsCheck =
        _autoScalingContextDao
            .find(groupId, clusterName)
            .map(AutoScalingContext::getDiskGBContext)
            .map(DiskGBAutoScalingContext::isNeedsCheck)
            .orElseThrow();
    assertThat(needsCheck, equalTo(false));

    final ClusterDescription scaledClusterDesc =
        _ndsClusterSvc.getMergedClusterDescription(groupId, clusterName).orElseThrow();

    // check if update resulted in diskGB change
    assertThat(scaledClusterDesc.getDiskSizeGB(), pScaledDiskGBMatcher);

    // check if instanceSize has changed
    final NodeType nodeType = NodeTypeFamily.getRepresentativeNodeTypeFromFamily(pNodeTypeFamily);
    assertThat(scaledClusterDesc.getInstanceSizes(nodeType), equalTo(pInstanceSize));
  }

  private class InstanceSizeAutoscalingPair {

    private final AWSInstanceSize _instanceSize;
    @Nullable private final AWSAutoScaling _autoScalingContext;

    InstanceSizeAutoscalingPair(
        final AWSInstanceSize pInstanceSize, final AWSAutoScaling pAutoScalingContext) {
      _instanceSize = pInstanceSize;
      _autoScalingContext = pAutoScalingContext;
    }
  }

  /**
   * @return autoscaling config with disk scaling enabled, autoindexing disabled, and compute
   *     autoscaling fully disabled (no scale up or scale down)
   */
  private AWSAutoScaling createDiskEnabledAWSAutoScaling() {
    return createDiskEnabledAWSAutoScaling(null);
  }

  /**
   * @param pComputeUpper If given, is upper bound for compute autoscaling. If null, compute
   *     autoscaling is disabled
   * @return autoscaling config with disk scaling enabled, autoindexing disabled, and compute
   *     autoscaling with downscaling disabled, and upscaling contingent on pComputeUpper being
   *     nonnull
   */
  private AWSAutoScaling createDiskEnabledAWSAutoScaling(final AWSNDSInstanceSize pComputeUpper) {
    return createDiskEnabledAWSAutoScaling(null, pComputeUpper);
  }

  /**
   * @param pComputeLower If given, is lower bound for compute autoscaling. If null, downscaling is
   *     disabled.
   * @param pComputeUpper If given, is upper bound for compute autoscaling. If null, compute
   *     autoscaling is disabled
   * @return autoscaling config with disk scaling enabled, autoindexing disabled, and compute
   *     autoscaling contingent on parameters.
   */
  private AWSAutoScaling createDiskEnabledAWSAutoScaling(
      final AWSNDSInstanceSize pComputeLower, final AWSNDSInstanceSize pComputeUpper) {
    return createAWSAutoScaling(true, false, pComputeLower, pComputeUpper);
  }

  private AWSAutoScaling createAWSAutoScaling(
      final boolean pIsDiskEnabled,
      final boolean pIsAutoIndexingEnabled,
      final AWSNDSInstanceSize pComputeLower,
      final AWSNDSInstanceSize pComputeUpper) {
    final boolean computeEnabled = pComputeUpper != null;
    final boolean scaleDownEnabled = pComputeLower != null;
    final AWSComputeAutoScaling computeAutoScaling =
        new AWSComputeAutoScaling(
            pComputeLower, pComputeUpper, computeEnabled, scaleDownEnabled, false);
    return new AWSAutoScaling(
        computeAutoScaling,
        new DiskGBAutoScaling(pIsDiskEnabled),
        new AutoIndexing(pIsAutoIndexingEnabled));
  }

  @Test
  public void testCheckDiskGBAutoScale_WithAutoScaling() throws SvcException {

    // Cases where we scale a cluster tier and autoscale config
    // Old state is M10 w 128 GiB
    // Utilization is faked to 91%
    // hardcoded target utilization is 70%
    // Target size is 128*.91/.7 ~=165 GiB
    // This is beyond M10 bounds, but within M20, so that's the post-scaling instance size floor
    {
      // Analytics autoscaling bounds are M10-M20, and we coerce the lower bound to null + disable
      // downscaling
      testDiskBoundsUpdate(
          "analyticsLowBaseHighDsDisabledPostScaling",
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M30,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M10,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M20)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M30,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20, createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20)),
          128);

      // Autoscaling docs should be identical
      testDiskBoundsUpdate(
          "analyticsLowBaseHighDsEnabledPostScaling",
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M30,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M10,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M30,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30)),
          128);
      // test transition from symmetric cluster we scale with FF on
      testDiskBoundsUpdate(
          "nullFFOnUpsize",
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(AWSNDSInstanceSize.M10, null),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30)),
          128);
      // test 🚨🚨🚨 disk emergency case where we ignore the compute max bounds
      testDiskBoundsUpdate(
          "testIgnoreMaxInstance",
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M10, createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10)),
          new InstanceSizeAutoscalingPair(AWSNDSInstanceSize.M10, null),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20, createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20, createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20)),
          128);
    }
    // Cases where we do not scale a cluster tier but do change autoscale config
    {
      // Autoscaling docs should be identical. No replication spec changes, but autoscaling
      // docs are updated
      testDiskBoundsUpdate(
          "analyticsMidBaseHighDsEnabledPostScaling",
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M30,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M30,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30)),
          128);
      // for the analytics bounds (old - m10-20; new - null-m20)
      testDiskBoundsUpdate(
          "onlyBoundsUpdatesWAnalyticsDsDisabledPostScaling",
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M30,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M20)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M30,
              createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30)),
          new InstanceSizeAutoscalingPair(
              AWSNDSInstanceSize.M20, createDiskEnabledAWSAutoScaling(AWSNDSInstanceSize.M20)),
          128);
    }
  }

  private <CD extends ClusterDescription, B extends ClusterDescription.Builder<B, CD>>
      CD createFakeCluster(
          final String pClusterPrefix,
          final Function<ClusterDescription.Builder<B, CD>, CD> pBuilder)
          throws SvcException {
    final ClusterDescription.Builder<B, CD> clusterBuilder =
        _cluster.getClusterDescription().copy();
    clusterBuilder.setName(pClusterPrefix);
    final CD clusterWithModifiedFields = pBuilder.apply(clusterBuilder);
    setupCluster(_ndsGroupId, pClusterPrefix, clusterWithModifiedFields);
    return clusterWithModifiedFields;
  }

  private <CD extends ClusterDescription, B extends ClusterDescription.Builder<B, CD>>
      void testDiskBoundsUpdate(
          final String pLabel,
          final InstanceSizeAutoscalingPair pBaseConfig,
          final InstanceSizeAutoscalingPair pAnalyticsConfig,
          final InstanceSizeAutoscalingPair pEndingBaseConfig,
          final InstanceSizeAutoscalingPair pEndingAnalyticsConfig,
          final double pDiskSizeGb)
          throws SvcException {
    final ClusterDescription startCluster =
        createFakeCluster(
            pLabel,
            (final ClusterDescription.Builder<B, CD> pBuilder) ->
                pBuilder
                    .mixin(ClusterDescriptionBuilderTestMixin::new)
                    .updateHardware(
                        new Builder().setInstanceSize(pBaseConfig._instanceSize),
                        NodeTypeFamily.BASE)
                    // Note that we still have disk size validation gated off having analytics nodes
                    // -
                    // that same logic is used to determine if an instance size is compatible or not
                    .mixin(ClusterDescriptionBuilderTestMixin::new)
                    .updateHardware(
                        new Builder()
                            .setInstanceSize(pAnalyticsConfig._instanceSize)
                            .setNodeCount(1),
                        NodeTypeFamily.ANALYTICS)
                    .setAutoScalingForProvider(
                        CloudProvider.AWS, pBaseConfig._autoScalingContext, NodeTypeFamily.BASE)
                    .setAutoScalingForProvider(
                        CloudProvider.AWS,
                        pAnalyticsConfig._autoScalingContext,
                        NodeTypeFamily.ANALYTICS)
                    .setDiskSizeGB(pDiskSizeGb)
                    .build());

    final ObjectId groupId = startCluster.getGroupId();
    final String clusterName = startCluster.getName();
    final AutoScalingContext context =
        AutoScalingContext.getDefaultContext(groupId, clusterName, AutoScalingMode.CLUSTER)
            .toBuilder()
            .diskGBContext(
                DiskGBAutoScalingContext.buildDefaultContext().builder().needsCheck(true).build())
            .build();

    final NDSReactiveAutoScaleSvc autoScaleSpy =
        testCheckDiskGBAutoScale_setup(spy(_ndsAutoScaleSvc), context, 0.91, true);
    doReturn(true).when(autoScaleSpy).checkDiskMatches(any(), any(), any());
    autoScaleSpy.checkAutoScale(
        groupId, clusterName, new AutoScaleDecisions.Builder(), new PredictiveAutoScalingResult());

    final ClusterDescription updatedCluster =
        _ndsClusterSvc.getMergedClusterDescription(groupId, clusterName).get();

    assertEquals(
        Set.of(pEndingBaseConfig._instanceSize),
        updatedCluster.getInstanceSizes(
            NodeTypeFamily.getRepresentativeNodeTypeFromFamily(NodeTypeFamily.BASE)));
    assertEquals(
        Set.of(pEndingAnalyticsConfig._instanceSize),
        updatedCluster.getInstanceSizes(
            NodeTypeFamily.getRepresentativeNodeTypeFromFamily(NodeTypeFamily.ANALYTICS)));
    assertEquals(
        pEndingBaseConfig._autoScalingContext, updatedCluster.getAutoScaling(NodeTypeFamily.BASE));
    // Note that this can be the base fallback - right now we are never explicitly checking the
    // analytics autoscaling value in this test
    assertEquals(
        pEndingAnalyticsConfig._autoScalingContext,
        updatedCluster.getAutoScaling(NodeTypeFamily.ANALYTICS));
  }

  public void testCheckDiskGBAutoScale(final Cluster pCluster) throws SvcException {
    final NDSReactiveAutoScaleSvc autoScaleSpy = spy(_ndsAutoScaleSvc);

    final ObjectId groupId = pCluster.getClusterDescription().getGroupId();
    final String clusterName = pCluster.getClusterDescription().getName();
    final Date now = new Date();

    final ClusterDescription clusterDescription =
        _ndsClusterSvc.getActiveClusterDescription(groupId, clusterName).orElseThrow();
    final Double initialDiskGB = clusterDescription.getDiskSizeGB();

    // does not need check with diskUsage below threshold
    {
      final AutoScalingContext context =
          AutoScalingContext.getDefaultContext(groupId, clusterName, AutoScalingMode.CLUSTER)
              .toBuilder()
              .diskGBContext(
                  DiskGBAutoScalingContext.buildDefaultContext()
                      .builder()
                      .needsCheck(false)
                      .build())
              .build();

      testCheckDiskGBAutoScale_setup(autoScaleSpy, context, 0.20, true)
          .checkAutoScale(
              groupId,
              clusterName,
              new AutoScaleDecisions.Builder(),
              new PredictiveAutoScalingResult());

      validateDiskGBAutoScaling(
          clusterDescription,
          equalTo(initialDiskGB),
          clusterDescription.getInstanceSizes(NodeType.ELECTABLE),
          NodeTypeFamily.BASE);

      validateDiskGBAutoScalingAudits(
          now,
          new Date(),
          1,
          Set.of(NDSAutoScalingAudit.Type.DISK_AUTO_SCALE_INITIATED),
          Set.of(0));
    }

    // needs check with diskUsage below threshold
    {
      final AutoScalingContext context =
          AutoScalingContext.getDefaultContext(groupId, clusterName, AutoScalingMode.CLUSTER)
              .toBuilder()
              .diskGBContext(
                  DiskGBAutoScalingContext.buildDefaultContext().builder().needsCheck(true).build())
              .build();

      testCheckDiskGBAutoScale_setup(autoScaleSpy, context, 0.20, true)
          .checkAutoScale(
              groupId,
              clusterName,
              new AutoScaleDecisions.Builder(),
              new PredictiveAutoScalingResult());

      validateDiskGBAutoScaling(
          clusterDescription,
          equalTo(initialDiskGB),
          clusterDescription.getInstanceSizes(NodeType.ELECTABLE),
          NodeTypeFamily.BASE);
    }

    validateDiskGBAutoScalingAudits(
        now, new Date(), 1, Set.of(NDSAutoScalingAudit.Type.DISK_AUTO_SCALE_INITIATED), Set.of(0));

    // needs check with diskUsage above threshold oplog cannot support
    {
      final AutoScalingContext context =
          AutoScalingContext.getDefaultContext(groupId, clusterName, AutoScalingMode.CLUSTER)
              .toBuilder()
              .diskGBContext(
                  DiskGBAutoScalingContext.buildDefaultContext().builder().needsCheck(true).build())
              .build();

      testCheckDiskGBAutoScale_setup(autoScaleSpy, context, 0.91, false)
          .checkAutoScale(
              groupId,
              clusterName,
              new AutoScaleDecisions.Builder(),
              new PredictiveAutoScalingResult());

      validateDiskGBAutoScaling(
          clusterDescription,
          equalTo(initialDiskGB),
          clusterDescription.getInstanceSizes(NodeType.ELECTABLE),
          NodeTypeFamily.BASE);

      validateDiskGBAutoScalingAudits(
          now,
          new Date(),
          1,
          Set.of(NDSAutoScalingAudit.Type.DISK_AUTO_SCALE_INITIATED),
          Set.of(0));
    }

    // needs check with diskUsage above threshold
    {
      final AutoScalingContext context =
          AutoScalingContext.getDefaultContext(groupId, clusterName, AutoScalingMode.CLUSTER)
              .toBuilder()
              .diskGBContext(
                  DiskGBAutoScalingContext.buildDefaultContext().builder().needsCheck(true).build())
              .build();

      testCheckDiskGBAutoScale_setup(autoScaleSpy, context, 0.91, true)
          .checkAutoScale(
              groupId,
              clusterName,
              new AutoScaleDecisions.Builder(),
              new PredictiveAutoScalingResult());

      validateDiskGBAutoScaling(
          clusterDescription,
          greaterThan(initialDiskGB),
          clusterDescription.getInstanceSizes(NodeType.ELECTABLE),
          NodeTypeFamily.BASE);

      validateDiskGBAutoScalingAudits(
          now,
          new Date(),
          1,
          Set.of(NDSAutoScalingAudit.Type.DISK_AUTO_SCALE_INITIATED),
          Set.of(1));
    }
  }

  @Test
  public void testDiskAndComputeAutoScalingUpdate() throws Exception {
    // test that when disk auto-scaling updates are applied, they are not overwritten by any applied
    // compute-autoscaling updates
    final String clusterName = _cluster.getClusterDescription().getName();
    final ObjectId clusterUniqueId = _cluster.getClusterDescription().getUniqueId();

    final AutoScaleInstanceSize autoScaleInstanceSize =
        _cluster
            .getClusterDescription()
            .getAutoScaleInstanceSizeFromReplicationSpecId(
                _cluster.getClusterDescription().getReplicationSpecsWithShardData().get(0).getId(),
                NodeTypeFamily.BASE)
            .orElseThrow();
    final AWSNDSInstanceSize awsInstanceSize =
        (AWSNDSInstanceSize)
            _cluster.getClusterDescription().getMinInstanceSize(NodeType.ELECTABLE).orElseThrow();

    final int diskSize = 30;
    final int iops = awsInstanceSize.getStandardEBSIOPS(diskSize);
    final AutoScaling autoScaling =
        _cluster
            .getClusterDescription()
            .getAutoScaling(NodeTypeFamily.BASE)
            .getAutoScalingWithFlagsSet(
                true,
                true,
                true,
                autoScaleInstanceSize,
                autoScaleInstanceSize
                    .getNextActiveInstanceSize(Set.of(CloudProvider.AWS))
                    .get()
                    .getNextActiveInstanceSize(Set.of(CloudProvider.AWS))
                    .get(),
                false);
    final ClusterDescription clusterDescription =
        _cluster
            .getClusterDescription()
            .copy()
            .setReplicationSpecList(
                _cluster.getClusterDescription().getReplicationSpecsWithShardData().stream()
                    .map(
                        ccrs ->
                            ccrs.copy()
                                .setRegionConfigs(
                                    ccrs.getRegionConfigs().stream()
                                        .map(
                                            rc -> rc.copy().setBaseAutoScaling(autoScaling).build())
                                        .collect(Collectors.toList()))
                                .build())
                    .collect(Collectors.toList()))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setDiskIOPS(iops))
            .setDiskSizeGB(diskSize)
            .build();
    _clusterDescriptionDao.save(clusterDescription);
    setDiskSizeGBForReplicaSetHardware(clusterDescription, diskSize);

    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvcSpy =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvcSpy);
    // It is extremely hard to prepare data for Free Disk Usage, so we mock it.
    doReturn(0.91).when(autoScaleSpy).getCurrentDiskUsagePercentageInDecimals(any(), any());
    // It is also difficult to prepare data for the stable oplog condition, so we mock it too.
    doReturn(Result.ok(null))
        .when(_ndsAutoScaleValidationSvc)
        .canBeAutoScaledWithinOplogTimeWindow(any(), any(), any(), any());

    // set up automation config and HostCluster for metrics
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(getMergedClusterForMeasurements(clusterDescription.getName())),
        clusterDescription);

    // set up compute auto-scaling context
    final AutoScalingContext autoScalingContextBeforeCheck =
        setAutoScalingContextFlags(
            _autoScalingContextDao.find(clusterDescription.getGroupId(), clusterName).get(),
            true,
            true,
            null,
            null,
            null);

    assertNull(
        ((ClusterComputeAutoScalingContext) autoScalingContextBeforeCheck.getBaseComputeContext())
            .getLastScaleUpInitiated());
    _autoScalingContextDao.save(autoScalingContextBeforeCheck);

    setLastInstanceSizeModifyDateForReplicaSetHardware(
        clusterDescription, List.of(Date.from(Instant.EPOCH)));
    setLastResumeFromPauseDateForReplicaSetHardware(
        clusterDescription, List.of(Pair.of(false, null)));

    setupAppSettingsPropsForComputeAutoScaling();

    // set up CPU needs scale up for compute auto-scaling
    final Map<SingleScalingCriteria, AutoScalingCalculatedMetrics> calculatorServiceResult =
        new HashMap<>();
    autoScalingContextBeforeCheck
        .getBaseComputeContext()
        .getUpScalingCriterias()
        .forEach(
            criteria -> {
              final AutoScalingCalculatedMetrics.Builder autoScalingCalculatedMetricsBuilder =
                  AutoScalingCalculatedMetrics.builder();
              for (final ComputeAutoScalingThreshold<? extends ThresholdMetrics> threshold :
                  criteria.getThresholds()) {
                if (threshold instanceof ComputeAutoScalingCPUThreshold pCpuThreshold) {
                  autoScalingCalculatedMetricsBuilder.addThresholdMetric(
                      pCpuThreshold,
                      CPUThresholdMetrics.builder()
                          .absoluteCpuMetric(pCpuThreshold.getCpuScaleThreshold() + 0.1f)
                          .relativeCpuMetric(pCpuThreshold.getCpuScaleThreshold() + 0.1f)
                          .build());
                }

                if (threshold instanceof ComputeAutoScalingMemoryThreshold pMemoryThreshold) {
                  autoScalingCalculatedMetricsBuilder.addThresholdMetric(
                      pMemoryThreshold,
                      MemoryThresholdMetrics.builder()
                          .memoryCalculatedMetric(pMemoryThreshold.getMemoryScaleThreshold() + 0.1f)
                          .build());
                }
              }
              calculatorServiceResult.put(criteria, autoScalingCalculatedMetricsBuilder.build());
            });

    doReturn(calculatorServiceResult)
        .when(computeClusterMetricsCalculatorSvcSpy)
        .getClusterMetricsForCriteria(any(), any(), any());

    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        clusterName,
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final Optional<AutoScalingContext> autoScalingContextAfterCheck =
        _autoScalingContextDao.find(_ndsGroupId, clusterName);
    final DiskGBAutoScalingContext diskGBAutoScalingContext =
        autoScalingContextAfterCheck.map(AutoScalingContext::getDiskGBContext).orElseThrow();
    final ClusterComputeAutoScalingContext computeAutoScalingContext =
        ((ClusterComputeAutoScalingContext)
            autoScalingContextAfterCheck
                .map(AutoScalingContext::getBaseComputeContext)
                .orElseThrow());
    assertThat(diskGBAutoScalingContext.isNeedsCheck(), equalTo(false));
    assertThat(computeAutoScalingContext.isNeedsCheck(), equalTo(false));
    assertNotNull(computeAutoScalingContext.getLastScaleUpInitiated());

    final ClusterDescription scaledClusterDesc =
        _ndsClusterSvc.getMergedClusterDescription(_ndsGroupId, clusterName).get();
    // disk size increased
    assertTrue(scaledClusterDesc.getDiskSizeGB() > clusterDescription.getDiskSizeGB());
    // iops increased
    assertTrue(
        scaledClusterDesc
                .getOnlyHardwareSpec(NodeType.ELECTABLE)
                .orElseThrow()
                .getDiskIOPS(scaledClusterDesc.getDiskSizeGB())
            > clusterDescription
                .getOnlyHardwareSpec(NodeType.ELECTABLE)
                .orElseThrow()
                .getDiskIOPS(clusterDescription.getDiskSizeGB()));
    // instance size has changed
    assertTrue(
        scaledClusterDesc
            .getMinInstanceSize(NodeType.ELECTABLE)
            .orElseThrow()
            .isGreaterThan(
                clusterDescription.getMinInstanceSize(NodeType.ELECTABLE).orElseThrow()));
    // scale down still enabled
    assertTrue(
        scaledClusterDesc.getAutoScaling(NodeTypeFamily.BASE).getCompute().isScaleDownEnabled());
    // the new min instance size is equal to the old min instance size
    assertTrue(
        scaledClusterDesc
            .getAutoScaling(NodeTypeFamily.BASE)
            .getCompute()
            .getMinInstanceSize()
            .equals(
                clusterDescription
                    .getAutoScaling(NodeTypeFamily.BASE)
                    .getCompute()
                    .getMinInstanceSize()));
    // the new max instance size is equal to the old max instance size
    assertEquals(
        clusterDescription.getAutoScaling(NodeTypeFamily.BASE).getCompute().getMaxInstanceSize(),
        scaledClusterDesc.getAutoScaling(NodeTypeFamily.BASE).getCompute().getMaxInstanceSize());
  }

  @Test
  public void testDiskAutoScalingWithInstanceSize() throws SvcException {
    final NDSReactiveAutoScaleSvc autoScaleSpy = spy(_ndsAutoScaleSvc);
    final ObjectId groupId = _mmsGroup.getId();
    final String clusterName = "maxDiskSizeCluster";
    final AWSNDSInstanceSize instanceSize = AWSNDSInstanceSize.M60;
    final double maxDiskSizeGB = instanceSize.getMaxAllowedDiskSizeGB(false);
    final Date now = new Date();

    final ClusterDescription defaultClusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName));

    final AWSAutoScaling autoScaling = AWSAutoScaling.getDefaultAutoScaling();
    final AWSHardwareSpec hardwareSpec =
        (AWSHardwareSpec)
            defaultClusterDescription
                .getOnlyHardwareSpecForProvider(CloudProvider.AWS, NodeType.ELECTABLE)
                .orElseThrow();

    final int scaledIOPS =
        instanceSize.getScaledIOPS(
            defaultClusterDescription
                .getOnlyHardwareSpecForProvider(CloudProvider.AWS, NodeType.ELECTABLE)
                .get()
                .getDiskIOPS(defaultClusterDescription.getDiskSizeGB()),
            hardwareSpec.getEBSVolumeType(),
            maxDiskSizeGB);

    final int throughput = instanceSize.getMinThroughput(maxDiskSizeGB);

    final ClusterDescription clusterDescription =
        defaultClusterDescription
            .copy()
            .setDiskSizeGB(maxDiskSizeGB)
            .setReplicationSpecList(
                defaultClusterDescription.getReplicationSpecsWithShardData().stream()
                    .map(
                        ccrs ->
                            ccrs.copy()
                                .setRegionConfigs(
                                    ccrs.getRegionConfigs().stream()
                                        .map(
                                            rc -> rc.copy().setBaseAutoScaling(autoScaling).build())
                                        .collect(Collectors.toList()))
                                .build())
                    .collect(Collectors.toList()))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setDiskIOPS(scaledIOPS)
                    .setDiskThroughput(throughput)
                    .setInstanceSize(instanceSize))
            .build();

    // set up replica set hardware for cluster description
    setupCluster(_mmsGroup.getId(), clusterName, clusterDescription);
    setDiskSizeGBForReplicaSetHardware(
        clusterDescription, (int) clusterDescription.getDiskSizeGB());

    final AutoScalingContext context =
        AutoScalingContext.getDefaultContext(
                clusterDescription.getGroupId(),
                clusterDescription.getName(),
                AutoScalingMode.CLUSTER)
            .toBuilder()
            .diskGBContext(
                DiskGBAutoScalingContext.buildDefaultContext().builder().needsCheck(true).build())
            .build();

    // limited by max disk size for provider (4096) => no disk change, no instance size change
    {
      final Set<NDSInstanceSize> currentInstanceSize =
          clusterDescription.getInstanceSizes(NodeType.ELECTABLE);

      testCheckDiskGBAutoScale_setup(autoScaleSpy, context, 0.91, true)
          .checkAutoScale(
              groupId,
              clusterName,
              new AutoScaleDecisions.Builder(),
              new PredictiveAutoScalingResult());

      validateDiskGBAutoScaling(
          clusterDescription, equalTo(maxDiskSizeGB), currentInstanceSize, NodeTypeFamily.BASE);

      validateDiskGBAutoScalingAudits(
          now,
          new Date(),
          1,
          Set.of(NDSAutoScalingAudit.Type.DISK_AUTO_SCALE_INITIATED),
          Set.of(0));
    }
  }

  @Test
  public void testDiskGBAutoScale_scalingEnabled() throws SvcException {
    testCheckDiskGBAutoScale(_cluster);
    testCheckDiskGBAutoScale(_shardedCluster);
    testCheckDiskGBAutoScale(_crossCloudCluster);
  }

  @Test
  public void testCheckAutoShardingAutoScale() throws SvcException {
    final NDSReactiveAutoScaleSvc autoScaleSpy = spy(_ndsAutoScaleSvc);
    ClusterDescription clusterDescription =
        ClusterDescriptionUtil.getWithSplitReplicationSpecs(
            _shardedCluster.getClusterDescription());

    final AutoSharding autoSharding =
        AutoSharding.builder()
            .enabled(true)
            .maxShards(clusterDescription.getNumShards() + 5)
            .thresholdToAutoShardInBytes(1000000L)
            .build();
    clusterDescription = clusterDescription.copy().setAutoSharding(autoSharding).build();
    _clusterDescriptionDao.save(clusterDescription);

    final int initialShardCount = clusterDescription.getNumShards();

    final ObjectId groupId = clusterDescription.getGroupId();
    final String clusterName = clusterDescription.getName();
    final AutoScalingContext context =
        AutoScalingContext.getDefaultContext(groupId, clusterName, AutoScalingMode.CLUSTER)
            .toBuilder()
            .autoShardingContext(AutoShardingAutoScalingContext.builder().needsCheck(true).build())
            .build();

    testCheckAutoShardingAutoScale_setup(autoScaleSpy, context, 0.60, true)
        .checkAutoScale(
            groupId,
            clusterName,
            new AutoScaleDecisions.Builder(),
            new PredictiveAutoScalingResult());

    final AutoShardingAutoScalingContext autoShardingContext =
        _autoScalingContextDao
            .find(groupId, clusterName)
            .map(AutoScalingContext::getAutoShardingContext)
            .orElseThrow();
    assertFalse(autoShardingContext.isNeedsCheck(), "needsCheck should be false after scaling");
    final Date oneHourLater = new Date(System.currentTimeMillis() + 60L * 60L * 1000L);
    assertTrue(
        autoShardingContext.getLastScalingTime().after(oneHourLater),
        "lastScalingTime should be set to a date in the future");

    final ClusterDescription scaledClusterDesc =
        _ndsClusterSvc.getMergedClusterDescription(groupId, clusterName).orElseThrow();
    assertEquals(
        initialShardCount + 1,
        scaledClusterDesc.getNumShards(),
        "Shard count should increase by 1 afte scaling");
  }

  private NDSReactiveAutoScaleSvc testCheckAutoShardingAutoScale_setup(
      final NDSReactiveAutoScaleSvc pAutoScaleSpy,
      final AutoScalingContext autoScalingContext,
      final Double pDiskUsagePercentage,
      final boolean pDiskMatches)
      throws SvcException {

    _autoScalingContextDao.save(autoScalingContext);

    reset(pAutoScaleSpy);

    doReturn(pDiskUsagePercentage)
        .when(pAutoScaleSpy)
        .getCurrentDiskUsagePercentageInDecimals(any(), any());
    doReturn(pDiskMatches).when(pAutoScaleSpy).checkDiskMatches(any(), any(), any());

    return pAutoScaleSpy;
  }

  @Test
  public void testCheckComputeAutoScale_normalScaling() throws Exception {
    // set up automation config and HostCluster for metrics
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(_cluster.getClusterDescription().getName())),
        _cluster.getClusterDescription());

    doTestCheckComputeAutoScale(_cluster);
  }

  @Test
  public void testCheckComputeAutoScale_sharded_normalScaling() throws Exception {
    // set up automation config and HostClusters for metrics
    getShardedHostClustersForMeasurements(
        getHostnamesForShardedClusterForMeasurements(
            getMergedClusterForMeasurements(_shardedCluster.getClusterDescription().getName())),
        _shardedCluster);

    doTestCheckComputeAutoScale(_shardedCluster);
  }

  @Test
  public void testCheckComputeAutoScale_crossCloud_normalScaling() throws Exception {
    // set up automation config and HostClusters for metrics
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(_crossCloudCluster.getClusterDescription().getName())),
        _crossCloudCluster.getClusterDescription());

    doTestCheckComputeAutoScale(_crossCloudCluster);
  }

  @Test
  public void testCheckComputeAutoScale_with_instanceSizeUpgrade() throws Exception {
    // set up automation config and HostCluster for metrics
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(_cluster.getClusterDescription().getName())),
        _cluster.getClusterDescription());

    final ClusterDescription clusterDescription =
        _ndsClusterSvc
            .getActiveClusterDescription(_ndsGroupId, _cluster.getClusterDescription().getName())
            .get();
    final AWSInstanceSize instanceSize = AWSNDSInstanceSize.M40;
    final ClusterDescription updated =
        clusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(instanceSize))
            .build();
    _clusterDescriptionUpdateDao.save(updated);

    doTestCheckComputeAutoScale(_cluster);
  }

  @Test
  public void testCheckComputeAutoScale_sharded_with_instanceSizeUpgrade() throws Exception {
    // set up automation config and HostCluster for metrics
    getShardedHostClustersForMeasurements(
        getHostnamesForShardedClusterForMeasurements(
            getMergedClusterForMeasurements(_shardedCluster.getClusterDescription().getName())),
        _shardedCluster);

    final ClusterDescription clusterDescription =
        _ndsClusterSvc
            .getActiveClusterDescription(
                _ndsGroupId, _shardedCluster.getClusterDescription().getName())
            .get();
    final AWSInstanceSize instanceSize = AWSNDSInstanceSize.M40;
    final ClusterDescription updated =
        clusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(instanceSize))
            .build();
    _clusterDescriptionUpdateDao.save(updated);

    doTestCheckComputeAutoScale(_shardedCluster);
  }

  @Test
  public void testCheckComputeAutoScale_crossCloud_with_instanceSizeUpgrade() throws Exception {
    // set up automation config and HostCluster for metrics
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(_crossCloudCluster.getClusterDescription().getName())),
        _crossCloudCluster.getClusterDescription());

    final ClusterDescription clusterDescription =
        _ndsClusterSvc
            .getActiveClusterDescription(
                _ndsGroupId, _crossCloudCluster.getClusterDescription().getName())
            .get();
    final AWSInstanceSize instanceSize = AWSNDSInstanceSize.M40;
    final ClusterDescription updated =
        clusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(instanceSize))
            .build();
    _clusterDescriptionUpdateDao.save(updated);

    doTestCheckComputeAutoScale(_crossCloudCluster);
  }

  private void testCheckComputeAutoScaleAtMaxInstanceSize(
      final Cluster pCluster,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled)
      throws Exception {
    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvcSpy =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvcSpy);

    // test scale CPU above, already at max instance size, no previous max instance size failure
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              null,
              null,
              null,
              true,
              null);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date sentAfterDate = new Date();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          true,
          false,
          true,
          null,
          sentAfterDate);
    }

    // test scale CPU above, already at max instance size, previous max instance size failure
    // within moratorium window
    {
      final Date lastMaxInstanceSizeFailureDate =
          Date.from(
              Instant.now()
                  .minus(
                      Duration.ofDays(
                              NDSAutoScaleAlertSvc
                                  .COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_ALERT_WAIT_DAYS)
                          .minus(Duration.ofMinutes(1))));
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              null,
              null,
              null,
              true,
              lastMaxInstanceSizeFailureDate);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date sentAfterDate = new Date();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          true,
          false,
          true,
          lastMaxInstanceSizeFailureDate,
          sentAfterDate);
    }

    // test scale CPU above, already at max instance size, previous max instance size failure
    // outside of moratorium window
    {
      final Date lastMaxInstanceSizeFailureDate =
          Date.from(
              Instant.now()
                  .minus(
                      Duration.ofDays(
                              NDSAutoScaleAlertSvc
                                  .COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_ALERT_WAIT_DAYS)
                          .plus(Duration.ofMinutes(1))));
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              .01f,
              null,
              null,
              null,
              true,
              lastMaxInstanceSizeFailureDate);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date sentAfterDate = new Date();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          true,
          false,
          true,
          lastMaxInstanceSizeFailureDate,
          sentAfterDate);
    }
  }

  private void doTestCheckComputeAutoScaleAtMaxInstanceSize(final Cluster pCluster)
      throws Exception {
    testCheckComputeAutoScaleAtMaxInstanceSize(pCluster, true, false, false);
    testCheckComputeAutoScaleAtMaxInstanceSize(pCluster, true, true, false);
    testCheckComputeAutoScaleAtMaxInstanceSize(pCluster, true, true, true);
    testCheckComputeAutoScaleAtMaxInstanceSize(pCluster, false, false, false);
    testCheckComputeAutoScaleAtMaxInstanceSize(pCluster, false, true, false);
    testCheckComputeAutoScaleAtMaxInstanceSize(pCluster, false, true, true);
  }

  @Test
  public void testCheckComputeAutoScaleAtMaxInstanceSize_normalScaling() throws Exception {
    // set up automation config and HostCluster for metrics
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(_cluster.getClusterDescription().getName())),
        _cluster.getClusterDescription());

    doTestCheckComputeAutoScaleAtMaxInstanceSize(_cluster);
  }

  @Test
  public void testCheckComputeAutoScaleAtMaxInstanceSize_sharded_normalScaling() throws Exception {
    // set up automation config and HostClusters for metrics
    getShardedHostClustersForMeasurements(
        getHostnamesForShardedClusterForMeasurements(
            getMergedClusterForMeasurements(_shardedCluster.getClusterDescription().getName())),
        _shardedCluster);

    doTestCheckComputeAutoScaleAtMaxInstanceSize(_shardedCluster);
  }

  @Test
  public void testCheckComputeAutoScaleAtMaxInstanceSize_crossCloud_normalScaling()
      throws Exception {
    // set up automation config and HostClusters for metrics
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(_crossCloudCluster.getClusterDescription().getName())),
        _crossCloudCluster.getClusterDescription());

    doTestCheckComputeAutoScaleAtMaxInstanceSize(_crossCloudCluster);
  }

  private void testCheckComputeAutoScaleScaleDownBoundsUpdate(
      final Cluster pCluster,
      final boolean pNeedsCheck,
      final boolean pEnabled,
      final boolean pScaleDownEnabled)
      throws Exception {
    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvcSpy =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvcSpy);

    final int defaultDiskSizeGB = (int) pCluster.getClusterDescription().getDiskSizeGB();

    // test scale down, no bounds updates
    {
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              false,
              null,
              defaultDiskSizeGB);
      setDiskSizeGBForReplicaSetHardware(pCluster.getClusterDescription(), defaultDiskSizeGB);

      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          false,
          false,
          false,
          null,
          null);
    }

    // test scale down, lower bound updated to the previous instance size
    {
      final int newDiskSizeGB =
          pCluster
              .getClusterDescription()
              .getMinInstanceSize(NodeType.ELECTABLE)
              .orElseThrow()
              .getPreviousInstanceSize()
              .get()
              .getMaxAllowedDiskSizeGB(false);
      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              false,
              null,
              newDiskSizeGB);
      setDiskSizeGBForReplicaSetHardware(pCluster.getClusterDescription(), newDiskSizeGB);

      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          true,
          false,
          false,
          false,
          null,
          null);
    }

    // test scale down, lower bound updated to the current instance size
    {
      final int newDiskSizeGB =
          pCluster
              .getClusterDescription()
              .getMinInstanceSize(NodeType.ELECTABLE)
              .orElseThrow()
              .getMaxAllowedDiskSizeGB(false);

      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              false,
              null,
              newDiskSizeGB);
      setDiskSizeGBForReplicaSetHardware(pCluster.getClusterDescription(), newDiskSizeGB);

      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      // NOTE: if the feature flag is not enabled, the update fails here with an exception in the
      //       call to update the cluster with the new instance size (M20) because the disk size is
      //       not supported and fails validation. this is the old behavior.
      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          false,
          false,
          null,
          null);
      // check min instance size updated to current instance size
      // NOTE: min instance size will be *null* under normal circumstances if scale down is
      // disabled. in this case fixtures have this field set regardless.
      assertEquals(
          pNeedsCheck && pScaleDownEnabled
              ? AutoScaleInstanceSizeBuilderFactory.getAutoScaleInstanceSizeBuilder(
                      clusterDescription.getCloudProviders())
                  .getAutoScaleInstanceSize(
                      clusterDescription.getMinInstanceSize(NodeType.ELECTABLE).orElseThrow())
              : clusterDescription.getMinAutoScaleInstanceSize(NodeTypeFamily.BASE).orElse(null),
          updatedClusterDescription.getMinAutoScaleInstanceSize(NodeTypeFamily.BASE).orElse(null));
      // check scale down enabled
      assertEquals(
          pScaleDownEnabled,
          updatedClusterDescription
              .getAutoScaling(NodeTypeFamily.BASE)
              .getCompute()
              .isScaleDownEnabled());
    }

    // test scale down, lower bound updated to current and max instance size, scale down disabled
    {
      final int newDiskSizeGB =
          pCluster
              .getClusterDescription()
              .getMinInstanceSize(NodeType.ELECTABLE)
              .orElseThrow()
              .getMaxAllowedDiskSizeGB(false);

      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForCompute_setup(
              pCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvcSpy,
              pNeedsCheck,
              pEnabled,
              pScaleDownEnabled,
              null,
              null,
              -.01f,
              -.01f,
              true,
              null,
              newDiskSizeGB);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      // NOTE: if the feature flag is not enabled, the update fails here with an exception in the
      //       call to update the cluster with the new instance size (M20) because the disk size is
      //       not supported and fails validation. this is the old behavior.
      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          pCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  pCluster.getClusterDescription().getGroupId(),
                  pCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForCompute(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          pNeedsCheck,
          pEnabled,
          pScaleDownEnabled,
          false,
          false,
          false,
          false,
          null,
          null);
      // check min instance size unset
      // NOTE: min instance size will be *null* under normal circumstances if scale down is
      // disabled. in this case fixtures have this field set regardless.
      assertEquals(
          pNeedsCheck && pScaleDownEnabled
              ? null
              : clusterDescription
                  .getAutoScaling(NodeTypeFamily.BASE)
                  .getCompute()
                  .getMinInstanceSize(),
          updatedClusterDescription
              .getAutoScaling(NodeTypeFamily.BASE)
              .getCompute()
              .getMinInstanceSize());
      // check that down scaling has been disabled
      if (pNeedsCheck) {
        assertFalse(
            updatedClusterDescription
                .getAutoScaling(NodeTypeFamily.BASE)
                .getCompute()
                .isScaleDownEnabled());
      }
    }

    setDiskSizeGBForReplicaSetHardware(pCluster.getClusterDescription(), defaultDiskSizeGB);
  }

  private void doTestCheckComputeAutoScaleScaleDownBoundsUpdate(final Cluster pCluster)
      throws Exception {
    testCheckComputeAutoScaleScaleDownBoundsUpdate(pCluster, true, true, true);
    testCheckComputeAutoScaleScaleDownBoundsUpdate(pCluster, true, true, false);
    testCheckComputeAutoScaleScaleDownBoundsUpdate(pCluster, true, false, false);
    testCheckComputeAutoScaleScaleDownBoundsUpdate(pCluster, false, true, true);
    testCheckComputeAutoScaleScaleDownBoundsUpdate(pCluster, false, true, false);
    testCheckComputeAutoScaleScaleDownBoundsUpdate(pCluster, false, false, false);
  }

  @Test
  public void testCheckComputeAutoScaleScaleDownBoundsUpdate_normalScaling() throws Exception {
    // set up automation config and HostCluster for metrics
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(_cluster.getClusterDescription().getName())),
        _cluster.getClusterDescription());

    doTestCheckComputeAutoScaleScaleDownBoundsUpdate(_cluster);
  }

  @Test
  public void testCheckComputeAutoScaleScaleDownBoundsUpdate_sharded_normalScaling()
      throws Exception {
    // set up automation config and HostClusters for metrics
    getShardedHostClustersForMeasurements(
        getHostnamesForShardedClusterForMeasurements(
            getMergedClusterForMeasurements(_shardedCluster.getClusterDescription().getName())),
        _shardedCluster);

    doTestCheckComputeAutoScaleScaleDownBoundsUpdate(_shardedCluster);
  }

  @Test
  public void testCheckComputeAutoScaleScaleDownBoundsUpdate_crossCloud_normalScaling()
      throws Exception {
    // set up automation config and HostCluster for metrics
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(_crossCloudCluster.getClusterDescription().getName())),
        _crossCloudCluster.getClusterDescription());

    doTestCheckComputeAutoScaleScaleDownBoundsUpdate(_crossCloudCluster);
  }

  @Test
  public void testCheckAutoScale_with_instanceSizeUpgrade() throws Exception {
    final String clusterName = _cluster.getClusterDescription().getName();
    final ClusterDescription clusterDescription =
        _ndsClusterSvc.getActiveClusterDescription(_ndsGroupId, clusterName).get();

    // upgrade from M30 to M50
    final AWSInstanceSize instanceSize = AWSNDSInstanceSize.M50;
    final ClusterDescription updated =
        clusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(instanceSize))
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                clusterDescription
                    .getAutoScaling(NodeTypeFamily.BASE)
                    .getAutoScalingWithFlagsSet(
                        true, false, false, false), // remove disk check flag from updated
                NodeTypeFamily.BASE)
            .build();
    _clusterDescriptionUpdateDao.save(updated);

    final NDSReactiveAutoScaleSvc autoScaleSpy = spy(_ndsAutoScaleSvc);
    // It is extremely hard to prepare data for Free Disk Usage, so we mock it.
    doReturn(0.91).when(autoScaleSpy).getCurrentDiskUsagePercentageInDecimals(any(), any());
    // It is also difficult to prepare data for the stable oplog condition, so we mock it too.
    doReturn(Result.ok(null))
        .when(_ndsAutoScaleValidationSvc)
        .canBeAutoScaledWithinOplogTimeWindow(any(), any(), any(), any());

    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        clusterName,
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    // verify needsCheck has been reset
    final DiskGBAutoScalingContext activeDiskGBContext =
        _autoScalingContextDao.find(_ndsGroupId, clusterName).orElseThrow().getDiskGBContext();
    assertThat(activeDiskGBContext.isNeedsCheck(), equalTo(false));

    final ClusterDescription scaledClusterDesc =
        _ndsClusterSvc.getMergedClusterDescription(_ndsGroupId, clusterName).orElseThrow();
    assertTrue(scaledClusterDesc.getDiskSizeGB() > clusterDescription.getDiskSizeGB());
    assertEquals(Set.of(instanceSize), scaledClusterDesc.getInstanceSizes(NodeType.ELECTABLE));
  }

  @Test
  public void testCheckAutoScale_with_instanceSizeAndDiskUpgrade() throws Exception {
    final String clusterName = _cluster.getClusterDescription().getName();
    final ClusterDescription clusterDescription =
        _ndsClusterSvc.getActiveClusterDescription(_ndsGroupId, clusterName).orElseThrow();

    // upgrade from M30 to M50
    final AWSInstanceSize instanceSize = AWSNDSInstanceSize.M50;
    // increase disk size by 20GB
    final double diskSize = clusterDescription.getDiskSizeGB() + 20;
    final ClusterDescription updated =
        clusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(instanceSize))
            .setDiskSizeGB(diskSize)
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                clusterDescription
                    .getAutoScaling(NodeTypeFamily.BASE)
                    .getAutoScalingWithFlagsSet(
                        true, false, false, false), // remove disk check flag from updated
                NodeTypeFamily.BASE)
            .build();
    _clusterDescriptionUpdateDao.save(updated);

    final NDSReactiveAutoScaleSvc autoScaleSpy = spy(_ndsAutoScaleSvc);
    // It is extremely hard to prepare data for Free Disk Usage, so we mock it.
    doReturn(0.91).when(autoScaleSpy).getCurrentDiskUsagePercentageInDecimals(any(), any());
    // It is also difficult to prepare data for the stable oplog condition, so we mock it too.
    doReturn(Result.ok(null))
        .when(_ndsAutoScaleValidationSvc)
        .canBeAutoScaledWithinOplogTimeWindow(any(), any(), any(), any());
    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        clusterName,
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final DiskGBAutoScalingContext diskGBAutoScalingContext =
        _autoScalingContextDao
            .find(_ndsGroupId, clusterName)
            .map(AutoScalingContext::getDiskGBContext)
            .orElseThrow();
    assertThat(diskGBAutoScalingContext.isNeedsCheck(), equalTo(false));

    final ClusterDescription scaledClusterDesc =
        _ndsClusterSvc.getMergedClusterDescription(_ndsGroupId, clusterName).get();
    assertEquals(scaledClusterDesc.getDiskSizeGB(), 0.0, diskSize);
    assertEquals(Set.of(instanceSize), scaledClusterDesc.getInstanceSizes(NodeType.ELECTABLE));
  }

  @Test
  public void testCheckAutoScale_with_computeMinInstanceSizeUpdated() throws Exception {
    // test that when disk auto-scaling increases the disk to a size that is invalid for the min
    // instance size defined for compute auto-scaling, the min instance size is updated
    final String clusterName = _cluster.getClusterDescription().getName();

    final AutoScaleInstanceSize autoScaleInstanceSize =
        _cluster
            .getClusterDescription()
            .getAutoScaleInstanceSizeFromReplicationSpecId(
                _cluster.getClusterDescription().getReplicationSpecsWithShardData().get(0).getId(),
                NodeTypeFamily.BASE)
            .orElseThrow();
    final AWSNDSInstanceSize awsInstanceSize =
        (AWSNDSInstanceSize)
            _cluster.getClusterDescription().getMinInstanceSize(NodeType.ELECTABLE).orElseThrow();

    final int diskSize =
        autoScaleInstanceSize.getMaxAllowedDiskSizeGB(Set.of(CloudProvider.AWS), false, false);
    final int iops = awsInstanceSize.getStandardEBSIOPS(diskSize);
    final AutoScaling autoScaling =
        _cluster
            .getClusterDescription()
            .getAutoScaling(NodeTypeFamily.BASE)
            .getAutoScalingWithFlagsSet(
                true,
                true,
                true,
                autoScaleInstanceSize,
                autoScaleInstanceSize
                    .getNextActiveInstanceSize(Set.of(CloudProvider.AWS))
                    .get()
                    .getNextActiveInstanceSize(Set.of(CloudProvider.AWS))
                    .get(),
                false);
    final ClusterDescription clusterDescription =
        _cluster
            .getClusterDescription()
            .copy()
            .setReplicationSpecList(
                _cluster.getClusterDescription().getReplicationSpecsWithShardData().stream()
                    .map(
                        ccrs ->
                            ccrs.copy()
                                .setRegionConfigs(
                                    ccrs.getRegionConfigs().stream()
                                        .map(
                                            rc -> rc.copy().setBaseAutoScaling(autoScaling).build())
                                        .collect(Collectors.toList()))
                                .build())
                    .collect(Collectors.toList()))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setDiskIOPS(iops))
            .setDiskSizeGB(diskSize)
            .build();
    _clusterDescriptionDao.save(clusterDescription);
    setDiskSizeGBForReplicaSetHardware(clusterDescription, diskSize);
    _autoScalingContextDao.setDiskGBNeedsCheck(clusterDescription.getGroupId(), clusterName);

    final NDSReactiveAutoScaleSvc autoScaleSpy = spy(_ndsAutoScaleSvc);
    // It is extremely hard to prepare data for Free Disk Usage, so we mock it.
    doReturn(0.91).when(autoScaleSpy).getCurrentDiskUsagePercentageInDecimals(any(), any());
    // It is also difficult to prepare data for the stable oplog condition, so we mock it too.
    doReturn(Result.ok(null))
        .when(_ndsAutoScaleValidationSvc)
        .canBeAutoScaledWithinOplogTimeWindow(any(), any(), any(), any());
    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        clusterName,
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final DiskGBAutoScalingContext diskGBAutoScalingContext =
        _autoScalingContextDao
            .find(_ndsGroupId, clusterName)
            .map(AutoScalingContext::getDiskGBContext)
            .orElseThrow();
    assertThat(diskGBAutoScalingContext.isNeedsCheck(), equalTo(false));

    final ClusterDescription scaledClusterDesc =
        _ndsClusterSvc.getMergedClusterDescription(_ndsGroupId, clusterName).get();
    // disk size increased
    assertTrue(scaledClusterDesc.getDiskSizeGB() > clusterDescription.getDiskSizeGB());
    // iops increased
    assertTrue(
        scaledClusterDesc
                .getOnlyHardwareSpec(NodeType.ELECTABLE)
                .orElseThrow()
                .getDiskIOPS(scaledClusterDesc.getDiskSizeGB())
            > clusterDescription
                .getOnlyHardwareSpec(NodeType.ELECTABLE)
                .orElseThrow()
                .getDiskIOPS(clusterDescription.getDiskSizeGB()));
    // instance size has changed
    assertTrue(
        scaledClusterDesc
            .getMinInstanceSize(NodeType.ELECTABLE)
            .orElseThrow()
            .isGreaterThan(
                clusterDescription.getMinInstanceSize(NodeType.ELECTABLE).orElseThrow()));
    // scale down still enabled
    assertTrue(
        scaledClusterDesc.getAutoScaling(NodeTypeFamily.BASE).getCompute().isScaleDownEnabled());
    // the new min instance size is greater than the old min instance size
    assertTrue(
        scaledClusterDesc
            .getAutoScaling(NodeTypeFamily.BASE)
            .getCompute()
            .getMinInstanceSize()
            .isGreaterThan(
                clusterDescription
                    .getAutoScaling(NodeTypeFamily.BASE)
                    .getCompute()
                    .getMinInstanceSize()));
    // the new autoscaling min instance size is equal to the new smallest cluster instance size
    assertEquals(
        scaledClusterDesc.getMinInstanceSize(NodeType.ELECTABLE).orElseThrow(),
        scaledClusterDesc.getAutoScaling(NodeTypeFamily.BASE).getCompute().getMinInstanceSize());
    // the new max autoscaling instance size is equal to the old max autoscaling instance size
    assertEquals(
        clusterDescription.getAutoScaling(NodeTypeFamily.BASE).getCompute().getMaxInstanceSize(),
        scaledClusterDesc.getAutoScaling(NodeTypeFamily.BASE).getCompute().getMaxInstanceSize());
  }

  @Test
  public void testCheckAutoScale_with_scalePastComputeAutoScaleMaxInstanceSize() throws Exception {
    // test that when disk auto-scaling increases the instance size past the max instance size
    // defined for compute auto-scaling, down scaling is disabled and the max instance size is
    // increased
    final String clusterName = _cluster.getClusterDescription().getName();

    final AutoScaleInstanceSize autoScaleInstanceSize =
        _cluster
            .getClusterDescription()
            .getAutoScaleInstanceSizeFromReplicationSpecId(
                _cluster.getClusterDescription().getReplicationSpecsWithShardData().get(0).getId(),
                NodeTypeFamily.BASE)
            .orElseThrow();
    final AWSNDSInstanceSize awsInstanceSize =
        (AWSNDSInstanceSize)
            _cluster.getClusterDescription().getMinInstanceSize(NodeType.ELECTABLE).orElseThrow();

    final int diskSize =
        autoScaleInstanceSize.getMaxAllowedDiskSizeGB(Set.of(CloudProvider.AWS), false, false);
    final int iops = awsInstanceSize.getStandardEBSIOPS(diskSize);
    final AutoScaling autoScaling =
        _cluster
            .getClusterDescription()
            .getAutoScaling(NodeTypeFamily.BASE)
            .getAutoScalingWithFlagsSet(
                true,
                true,
                true,
                autoScaleInstanceSize
                    .getPreviousActiveInstanceSize(Set.of(CloudProvider.AWS))
                    .get(),
                autoScaleInstanceSize,
                false);
    final ClusterDescription clusterDescription =
        _cluster
            .getClusterDescription()
            .copy()
            .setReplicationSpecList(
                _cluster.getClusterDescription().getReplicationSpecsWithShardData().stream()
                    .map(
                        ccrs ->
                            ccrs.copy()
                                .setRegionConfigs(
                                    ccrs.getRegionConfigs().stream()
                                        .map(
                                            rc -> rc.copy().setBaseAutoScaling(autoScaling).build())
                                        .collect(Collectors.toList()))
                                .build())
                    .collect(Collectors.toList()))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setDiskIOPS(iops))
            .setDiskSizeGB(diskSize)
            .build();
    _clusterDescriptionDao.save(clusterDescription);
    setDiskSizeGBForReplicaSetHardware(clusterDescription, diskSize);
    _autoScalingContextDao.setDiskGBNeedsCheck(clusterDescription.getGroupId(), clusterName);

    final NDSReactiveAutoScaleSvc autoScaleSpy = spy(_ndsAutoScaleSvc);
    // It is extremely hard to prepare data for Free Disk Usage, so we mock it.
    doReturn(0.91).when(autoScaleSpy).getCurrentDiskUsagePercentageInDecimals(any(), any());
    // It is also difficult to prepare data for the stable oplog condition, so we mock it too.
    doReturn(Result.ok(null))
        .when(_ndsAutoScaleValidationSvc)
        .canBeAutoScaledWithinOplogTimeWindow(any(), any(), any(), any());
    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        clusterName,
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final DiskGBAutoScalingContext diskGBAutoScalingContext =
        _autoScalingContextDao
            .find(_ndsGroupId, clusterName)
            .map(AutoScalingContext::getDiskGBContext)
            .orElseThrow();
    assertThat(diskGBAutoScalingContext.isNeedsCheck(), equalTo(false));

    final ClusterDescription scaledClusterDesc =
        _ndsClusterSvc.getMergedClusterDescription(_ndsGroupId, clusterName).get();
    // disk size increased
    assertTrue(scaledClusterDesc.getDiskSizeGB() > clusterDescription.getDiskSizeGB());
    // iops increased
    assertTrue(
        scaledClusterDesc
                .getOnlyHardwareSpec(NodeType.ELECTABLE)
                .orElseThrow()
                .getDiskIOPS(scaledClusterDesc.getDiskSizeGB())
            > clusterDescription
                .getOnlyHardwareSpec(NodeType.ELECTABLE)
                .orElseThrow()
                .getDiskIOPS(clusterDescription.getDiskSizeGB()));
    // instance size has changed
    assertTrue(
        scaledClusterDesc
            .getMinInstanceSize(NodeType.ELECTABLE)
            .orElseThrow()
            .isGreaterThan(
                clusterDescription.getMinInstanceSize(NodeType.ELECTABLE).orElseThrow()));
    // the new autoscaling max instance size equals the new min cluster instance size
    assertEquals(
        scaledClusterDesc.getMaxInstanceSize(NodeType.ELECTABLE).orElseThrow(),
        scaledClusterDesc.getAutoScaling(NodeTypeFamily.BASE).getCompute().getMaxInstanceSize());
    // the new cluster instance size is greater than the old max autoscaling instance size
    assertTrue(
        scaledClusterDesc
            .getMinInstanceSize(NodeType.ELECTABLE)
            .orElseThrow()
            .isGreaterThan(
                clusterDescription
                    .getAutoScaling(NodeTypeFamily.BASE)
                    .getCompute()
                    .getMaxInstanceSize()));
    // scale down is disabled
    assertFalse(
        scaledClusterDesc.getAutoScaling(NodeTypeFamily.BASE).getCompute().isScaleDownEnabled());
    // the min instance size has been unset
    assertNull(
        scaledClusterDesc.getAutoScaling(NodeTypeFamily.BASE).getCompute().getMinInstanceSize());
    // the new max instance size is greater than the old max instance size
    assertTrue(
        scaledClusterDesc
            .getAutoScaling(NodeTypeFamily.BASE)
            .getCompute()
            .getMaxInstanceSize()
            .isGreaterThan(
                clusterDescription
                    .getAutoScaling(NodeTypeFamily.BASE)
                    .getCompute()
                    .getMaxInstanceSize()));
  }

  @Test
  public void testCheckAutoScale_scaleUp_skipDeprecatedInstanceSize() throws Exception {
    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvcSpy =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvcSpy);

    final ClusterDescription baseCD =
        (_ndsClusterSvc
                .getActiveClusterDescription(
                    _cluster.getClusterDescription().getGroupId(),
                    _cluster.getClusterDescription().getName())
                .get())
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M80))
            .build();
    _clusterDescriptionDao.save(baseCD);

    final Cluster cluster =
        _ndsClusterSvc.getMergedCluster(
            _cluster.getClusterDescription().getGroupId(),
            _cluster.getClusterDescription().getName());

    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(cluster.getClusterDescription().getName())),
        cluster.getClusterDescription());

    final Pair<ClusterDescription, AutoScalingContext> setup =
        testCheckAutoScaleForCompute_setup(
            cluster,
            autoScaleSpy,
            computeClusterMetricsCalculatorSvcSpy,
            true,
            true,
            false,
            .01f,
            null,
            null,
            null,
            false,
            null);
    final ClusterDescription clusterDescription =
        updateAutoScalingFlagsForTest(
            setup.getLeft(),
            false,
            true,
            false,
            new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M80),
            new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M140));

    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        cluster.getClusterDescription().getName(),
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final ClusterDescription updatedClusterDescription =
        _ndsClusterSvc
            .getMergedClusterDescription(
                cluster.getClusterDescription().getGroupId(),
                cluster.getClusterDescription().getName())
            .get();
    assertEquals(
        Set.of(AWSNDSInstanceSize.M140),
        updatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));
  }

  @Test
  public void testCheckAutoScale_scaleUp_scaleToDeprecatedInstanceSize() throws Exception {
    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvcSpy =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvcSpy);

    final ClusterDescription baseCD =
        (_ndsClusterSvc
                .getActiveClusterDescription(
                    _cluster.getClusterDescription().getGroupId(),
                    _cluster.getClusterDescription().getName())
                .get())
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M80))
            .build();
    _clusterDescriptionDao.save(baseCD);

    final Cluster cluster =
        _ndsClusterSvc.getMergedCluster(
            _cluster.getClusterDescription().getGroupId(),
            _cluster.getClusterDescription().getName());

    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(cluster.getClusterDescription().getName())),
        cluster.getClusterDescription());

    final Pair<ClusterDescription, AutoScalingContext> setup =
        testCheckAutoScaleForCompute_setup(
            cluster,
            autoScaleSpy,
            computeClusterMetricsCalculatorSvcSpy,
            true,
            true,
            false,
            .01f,
            null,
            null,
            null,
            false,
            null);
    final ClusterDescription clusterDescription =
        updateAutoScalingFlagsForTest(
            setup.getLeft(),
            false,
            true,
            false,
            null,
            new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M100));

    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        cluster.getClusterDescription().getName(),
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final ClusterDescription updatedClusterDescription =
        _ndsClusterSvc
            .getMergedClusterDescription(
                cluster.getClusterDescription().getGroupId(),
                cluster.getClusterDescription().getName())
            .get();
    // M100 not skipped because it is the current max instance size
    assertEquals(
        Set.of(AWSNDSInstanceSize.M100),
        updatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));
  }

  @Test
  public void testCheckAutoScale_scaleDown_skipDeprecatedInstanceSize() throws Exception {
    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvcSpy =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvcSpy);

    final ClusterDescription baseCD =
        (_ndsClusterSvc
                .getActiveClusterDescription(
                    _cluster.getClusterDescription().getGroupId(),
                    _cluster.getClusterDescription().getName())
                .get())
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M140))
            .build();
    _clusterDescriptionDao.save(baseCD);

    final Cluster cluster =
        _ndsClusterSvc.getMergedCluster(
            _cluster.getClusterDescription().getGroupId(),
            _cluster.getClusterDescription().getName());

    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(cluster.getClusterDescription().getName())),
        cluster.getClusterDescription());

    final Pair<ClusterDescription, AutoScalingContext> setup =
        testCheckAutoScaleForCompute_setup(
            cluster,
            autoScaleSpy,
            computeClusterMetricsCalculatorSvcSpy,
            true,
            true,
            true,
            null,
            null,
            -.01f,
            -.01f,
            false,
            null);
    final ClusterDescription clusterDescription =
        updateAutoScalingFlagsForTest(
            setup.getLeft(),
            false,
            true,
            true,
            new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M80),
            new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M140));

    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        cluster.getClusterDescription().getName(),
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final ClusterDescription updatedClusterDescription =
        _ndsClusterSvc
            .getMergedClusterDescription(
                cluster.getClusterDescription().getGroupId(),
                cluster.getClusterDescription().getName())
            .get();
    // M100 skipped
    assertEquals(
        Set.of(AWSNDSInstanceSize.M80),
        updatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));
  }

  @Test
  public void testCheckAutoScale_scaleDown_scaleToDeprecatedInstanceSize() throws Exception {
    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvcSpy =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvcSpy);

    final ClusterDescription baseCD =
        (_ndsClusterSvc
                .getActiveClusterDescription(
                    _cluster.getClusterDescription().getGroupId(),
                    _cluster.getClusterDescription().getName())
                .get())
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M140))
            .build();
    _clusterDescriptionDao.save(baseCD);

    final Cluster cluster =
        _ndsClusterSvc.getMergedCluster(
            _cluster.getClusterDescription().getGroupId(),
            _cluster.getClusterDescription().getName());

    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(cluster.getClusterDescription().getName())),
        cluster.getClusterDescription());

    final Pair<ClusterDescription, AutoScalingContext> setup =
        testCheckAutoScaleForCompute_setup(
            cluster,
            autoScaleSpy,
            computeClusterMetricsCalculatorSvcSpy,
            true,
            true,
            true,
            null,
            null,
            -.01f,
            -.01f,
            false,
            null);
    final ClusterDescription clusterDescription =
        updateAutoScalingFlagsForTest(
            setup.getLeft(),
            false,
            true,
            true,
            new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M100),
            new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M140));

    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        cluster.getClusterDescription().getName(),
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final ClusterDescription updatedClusterDescription =
        _ndsClusterSvc
            .getMergedClusterDescription(
                cluster.getClusterDescription().getGroupId(),
                cluster.getClusterDescription().getName())
            .get();
    // M100 not skipped because it is the current min instance size
    assertEquals(
        Set.of(AWSNDSInstanceSize.M100),
        updatedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));
  }

  @Test
  public void testAutoScalingWithAutoIndexingEnabled() throws Exception {
    final String clusterName = _cluster.getClusterDescription().getName();
    final ClusterDescription clusterDescription =
        _ndsClusterSvc.getActiveClusterDescription(_ndsGroupId, clusterName).get();

    // upgrade from M30 to M50
    final AWSInstanceSize instanceSize = AWSNDSInstanceSize.M50;
    final ClusterDescription updated =
        clusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(instanceSize))
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                clusterDescription
                    .getAutoScaling(NodeTypeFamily.BASE)
                    .getAutoScalingWithFlagsSet(
                        true, false, false, true), // remove disk check flag from updated
                NodeTypeFamily.BASE)
            .build();
    _clusterDescriptionUpdateDao.save(updated);

    final NDSReactiveAutoScaleSvc autoScaleSpy = spy(_ndsAutoScaleSvc);
    // It is extremely hard to prepare data for Free Disk Usage, so we mock it.
    doReturn(0.91).when(autoScaleSpy).getCurrentDiskUsagePercentageInDecimals(any(), any());
    // It is also difficult to prepare data for the stable oplog condition, so we mock it too.
    doReturn(Result.ok(null))
        .when(_ndsAutoScaleValidationSvc)
        .canBeAutoScaledWithinOplogTimeWindow(any(), any(), any(), any());
    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        clusterName,
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    // verify needsCheck has been reset
    final DiskGBAutoScalingContext activeDiskGBContext =
        _autoScalingContextDao.find(_ndsGroupId, clusterName).orElseThrow().getDiskGBContext();
    assertThat(activeDiskGBContext.isNeedsCheck(), equalTo(false));

    final ClusterDescription scaledClusterDesc =
        _ndsClusterSvc.getMergedClusterDescription(_ndsGroupId, clusterName).orElseThrow();
    assertFalse(
        scaledClusterDesc.getAutoScaling(NodeTypeFamily.BASE).getAutoIndexing().isEnabled());
    assertTrue(scaledClusterDesc.getDiskSizeGB() > clusterDescription.getDiskSizeGB());
    assertEquals(Set.of(instanceSize), scaledClusterDesc.getInstanceSizes(NodeType.ELECTABLE));
  }

  @Test
  public void testShardedComputeAutoScaleMemoryMeasurement() throws Exception {
    final ClusterDescription clusterDescription = getShardedClusterDescriptionForMeasurements();
    final Cluster mergedCluster = getMergedClusterForMeasurements(clusterDescription.getName());
    final List<List<String>> hostnames =
        getHostnamesForShardedClusterForMeasurements(mergedCluster);
    final List<String> flattenedHostnames =
        hostnames.stream().flatMap(List::stream).collect(Collectors.toList());
    final List<HostCluster> hostClusters =
        getShardedHostClustersForMeasurements(hostnames, mergedCluster);

    final String firstHostname = flattenedHostnames.get(0);
    final Duration samplingPeriod = Duration.ofMinutes(3);
    final Duration windowLength = Duration.ofHours(1);
    final Instant now = Instant.now();
    final Date windowStart = Date.from(now.minus(windowLength));
    final Date windowEnd = Date.from(now);

    final Retention retention = Retention.of(Duration.ofMinutes(1), Duration.ofHours(48));
    final Optional<Float> averageMemoryUsageResultNoMetrics =
        _ndsComputeClusterMetricsSvc.computeAverageMemoryUsageForWindow(
            mergedCluster,
            hostClusters,
            _mmsGroup,
            windowStart,
            windowEnd,
            samplingPeriod,
            NodeTypeFamily.BASE);
    assertTrue(averageMemoryUsageResultNoMetrics.isEmpty());

    // memory measurements present for one host
    final List<SystemMemoryMeasurement> memoryMeasurementsOneHost =
        Arrays.asList(
            ModelTestUtils.newSystemMemoryMeasurement(
                _mmsGroup.getId(),
                firstHostname,
                Date.from(now.minus(Duration.ofMinutes(42))),
                15400904L,
                2223436L,
                186280L,
                10585204L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            ModelTestUtils.newSystemMemoryMeasurement(
                _mmsGroup.getId(),
                firstHostname,
                Date.from(now.minus(Duration.ofMinutes(22))),
                15400904L,
                2223436L,
                186280L,
                10585204L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            ModelTestUtils.newSystemMemoryMeasurement(
                _mmsGroup.getId(),
                firstHostname,
                Date.from(now.minus(Duration.ofMinutes(5))),
                15400904L,
                2223436L,
                186280L,
                10585204L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                2097148L,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null));
    _hostMeasurementDao.insertSystemMemoryMeasurements(memoryMeasurementsOneHost, retention);

    final Optional<Float> averageMemoryUsageOneHostMeasured =
        _ndsComputeClusterMetricsSvc.computeAverageMemoryUsageForWindow(
            mergedCluster,
            hostClusters,
            _mmsGroup,
            windowStart,
            windowEnd,
            samplingPeriod,
            NodeTypeFamily.BASE);
    assertTrue(averageMemoryUsageOneHostMeasured.isEmpty());

    // memory measurements present for all hosts
    final List<SystemMemoryMeasurement> memoryMeasurementsAllHosts =
        IntStream.range(0, flattenedHostnames.size())
            .mapToObj(
                i ->
                    ModelTestUtils.newSystemMemoryMeasurement(
                        _mmsGroup.getId(),
                        flattenedHostnames.get(i),
                        Date.from(now.minus(Duration.ofMinutes(59 - (10 * i)))),
                        15400904L,
                        2223436L,
                        186280L,
                        10585204L,
                        2097148L,
                        2097148L,
                        2097148L,
                        2097148L,
                        2097148L,
                        2097148L,
                        2097148L,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null))
            .collect(Collectors.toList());

    _hostMeasurementDao.insertSystemMemoryMeasurements(memoryMeasurementsAllHosts, retention);
    final Optional<Float> averageMemoryUsageAllHostsMeasured =
        _ndsComputeClusterMetricsSvc.computeAverageMemoryUsageForWindow(
            mergedCluster,
            hostClusters,
            _mmsGroup,
            windowStart,
            windowEnd,
            samplingPeriod,
            NodeTypeFamily.BASE);
    assertTrue(averageMemoryUsageAllHostsMeasured.isPresent());
    assertEquals(averageMemoryUsageAllHostsMeasured.get(), 0.1, .156f);
  }

  @Test
  public void testGetCloudProviderHasCapacityConstraint_singleCloud() {
    // M30
    final ClusterDescription cd = _shardedCluster.getClusterDescription();

    Optional<CapacityConstrainInfo> constrainInfo =
        _ndsAutoScaleSvc.getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
            cd, Optional.empty(), NodeType.ELECTABLE);
    assertTrue(constrainInfo.isEmpty());

    // M10 instead of M30
    final AWSInstanceFamily m10InstanceFamily =
        (AWSInstanceFamily)
            Iterables.getLast(
                AWSNDSInstanceSize.M10.getAvailableFamilies().get(AWSNDSDefaults.REGION_NAME));
    final Optional<AWSPhysicalZoneId> zoneId = Optional.of(new AWSPhysicalZoneId("fakeZone"));

    for (int i = 0; i < 3; i++) {
      _awsCapacityDenylistSvc.registerCapacityFailure(
          m10InstanceFamily, AWSNDSInstanceSize.M10, AWSNDSDefaults.REGION_NAME, zoneId);
    }

    constrainInfo =
        _ndsAutoScaleSvc.getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
            cd, Optional.empty(), NodeType.ELECTABLE);
    assertTrue(constrainInfo.isEmpty());

    // different region, same instance size
    final AWSInstanceFamily m30AFSouthInstanceFamily =
        (AWSInstanceFamily)
            Iterables.getLast(
                AWSNDSInstanceSize.M30.getAvailableFamilies().get(AWSRegionName.AF_SOUTH_1));
    for (int i = 0; i < 3; i++) {
      _awsCapacityDenylistSvc.registerCapacityFailure(
          m30AFSouthInstanceFamily, AWSNDSInstanceSize.M30, AWSRegionName.AF_SOUTH_1, zoneId);
    }

    constrainInfo =
        _ndsAutoScaleSvc.getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
            cd, Optional.empty(), NodeType.ELECTABLE);
    assertTrue(constrainInfo.isEmpty());

    // same region and instance size. now we expect true
    final AWSInstanceFamily instanceFamily =
        (AWSInstanceFamily)
            Iterables.getLast(
                AWSNDSInstanceSize.M30.getAvailableFamilies().get(AWSNDSDefaults.REGION_NAME));
    for (int i = 0; i < 3; i++) {
      _awsCapacityDenylistSvc.registerCapacityFailure(
          instanceFamily, AWSNDSInstanceSize.M30, AWSNDSDefaults.REGION_NAME, zoneId);
    }

    constrainInfo =
        _ndsAutoScaleSvc.getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
            cd, Optional.empty(), NodeType.ELECTABLE);
    assertTrue(constrainInfo.isPresent());
    assertEquals(CloudProvider.AWS, constrainInfo.get().cloudProvider());
    assertEquals(AWSNDSDefaults.REGION_NAME, constrainInfo.get().regionName());
    assertEquals(AWSNDSInstanceSize.M30.name(), constrainInfo.get().instanceSizeName());
  }

  @Test
  public void testGetCloudProviderHasCapacityConstraint_crossCloud() {
    // M30 AWSRegionName.US_EAST_1, GCPRegionName.CENTRAL_US
    final ClusterDescription crossCloudCD = _crossCloudCluster.getClusterDescription();

    Optional<CapacityConstrainInfo> constrainInfo =
        _ndsAutoScaleSvc.getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
            crossCloudCD, Optional.empty(), NodeType.ELECTABLE);
    assertTrue(constrainInfo.isEmpty());

    // AWS M10 instead of M30
    final AWSInstanceFamily m10InstanceFamily =
        (AWSInstanceFamily)
            Iterables.getLast(
                AWSNDSInstanceSize.M10.getAvailableFamilies().get(AWSNDSDefaults.REGION_NAME));
    final Optional<AWSPhysicalZoneId> zoneId = Optional.of(new AWSPhysicalZoneId("fakeZone"));
    for (int i = 0; i < 3; i++) {
      _awsCapacityDenylistSvc.registerCapacityFailure(
          m10InstanceFamily, AWSNDSInstanceSize.M10, AWSNDSDefaults.REGION_NAME, zoneId);
    }
    constrainInfo =
        _ndsAutoScaleSvc.getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
            crossCloudCD, Optional.empty(), NodeType.ELECTABLE);
    assertTrue(constrainInfo.isEmpty());

    // GCP incorrect region
    for (int i = 0; i < 3; i++) {
      _gcpCapacityDenylistSvc.registerCapacityFailure(
          (GCPInstanceFamily)
              Iterables.getLast(
                  GCPNDSInstanceSize.M30.getAvailableFamilies().get(GCPRegionName.EUROPE_NORTH_1)),
          GCPNDSInstanceSize.M30,
          "mockProject",
          GCPRegionName.EUROPE_NORTH_1,
          new GCPPhysicalZoneId(GCPZoneName.EUROPE_NORTH1_A));
    }

    constrainInfo =
        _ndsAutoScaleSvc.getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
            crossCloudCD, Optional.empty(), NodeType.ELECTABLE);
    assertTrue(constrainInfo.isEmpty());

    // AWS M30
    final AWSInstanceFamily m30InstanceFamily =
        (AWSInstanceFamily)
            Iterables.getLast(
                AWSNDSInstanceSize.M30.getAvailableFamilies().get(AWSNDSDefaults.REGION_NAME));
    for (int i = 0; i < 3; i++) {
      _awsCapacityDenylistSvc.registerCapacityFailure(
          m30InstanceFamily, AWSNDSInstanceSize.M30, AWSNDSDefaults.REGION_NAME, zoneId);
    }
    constrainInfo =
        _ndsAutoScaleSvc.getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
            crossCloudCD, Optional.empty(), NodeType.ELECTABLE);
    assertTrue(constrainInfo.isPresent());
    assertEquals(CloudProvider.AWS, constrainInfo.get().cloudProvider());
    assertEquals(AWSNDSDefaults.REGION_NAME, constrainInfo.get().regionName());
    assertEquals(AWSNDSInstanceSize.M30.name(), constrainInfo.get().instanceSizeName());

    for (int i = 0; i < 3; i++) {
      _gcpCapacityDenylistSvc.registerCapacityFailure(
          (GCPInstanceFamily)
              Iterables.getLast(
                  GCPNDSInstanceSize.M30.getAvailableFamilies().get(GCPRegionName.CENTRAL_US)),
          GCPNDSInstanceSize.M30,
          "mockProject",
          GCPRegionName.CENTRAL_US,
          new GCPPhysicalZoneId(GCPNDSDefaults.ZONE_NAME));
    }

    constrainInfo =
        _ndsAutoScaleSvc.getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
            crossCloudCD, Optional.empty(), NodeType.ELECTABLE);
    assertTrue(constrainInfo.isPresent());
  }

  @ParameterizedTest
  @ValueSource(ints = {0, 1, 2})
  public void testGetCloudProviderCapacityConstraintInfoForCurrentInstanceSize_shards(
      int indexOfConstrainedShard) {
    final ClusterDescription asymmetricCluster =
        new ShardedClusterDescription(
            NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithInstanceSizes(
                new ObjectId(),
                "asymmetric-cluster",
                List.of(AWSNDSInstanceSize.M20, GCPNDSInstanceSize.M40, AzureNDSInstanceSize.M80),
                true));

    List<ReplicationSpec> shards = asymmetricCluster.getReplicationSpecsWithShardData();

    // initially no shards constrained
    shards.forEach(
        rs ->
            assertTrue(
                _ndsAutoScaleSvc
                    .getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
                        asymmetricCluster, Optional.of(rs.getId()), NodeType.ELECTABLE)
                    .isEmpty()));

    // constrain the instance size of the shard
    final ReplicationSpec constrainedShard = shards.get(indexOfConstrainedShard);
    final RegionName region = Iterables.getOnlyElement(constrainedShard.getRegions());
    switch (region.getProvider()) {
      case AWS -> {
        for (int i = 0; i < 3; i++) {
          _awsCapacityDenylistSvc.registerCapacityFailure(
              (AWSInstanceFamily) constrainedShard.getElectableInstanceFamily(),
              (AWSInstanceSize) constrainedShard.getElectableInstanceSize(),
              (AWSRegionName) region,
              Optional.of(new AWSPhysicalZoneId("fakeZone")));
        }
      }
      case GCP -> {
        for (int i = 0; i < 3; i++) {
          _gcpCapacityDenylistSvc.registerCapacityFailure(
              (GCPInstanceFamily) constrainedShard.getElectableInstanceFamily(),
              (GCPInstanceSize) constrainedShard.getElectableInstanceSize(),
              "fakeProject",
              (GCPRegionName) region,
              new GCPPhysicalZoneId(GCPNDSDefaults.ZONE_NAME));
        }
      }
      case AZURE -> {
        for (int i = 0; i < 3; i++) {
          _azureCapacityDenylistSvc.registerCapacityFailure(
              (AzureInstanceFamily) constrainedShard.getElectableInstanceFamily(),
              (AzureInstanceSize) constrainedShard.getElectableInstanceSize(),
              (AzureRegionName) region,
              Optional.of(new AzurePhysicalZoneId("fakeZone")));
        }
      }

      default -> throw new IllegalStateException("Unsupported provider");
    }

    // assert only constrained shard is returning capacity constraint info, expect other shards
    // are not capacity constraint
    for (int i = 0; i < shards.size(); i++) {
      if (i == indexOfConstrainedShard) {
        assertTrue(
            _ndsAutoScaleSvc
                .getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
                    asymmetricCluster, Optional.of(shards.get(i).getId()), NodeType.ELECTABLE)
                .isPresent());
      } else {
        assertTrue(
            _ndsAutoScaleSvc
                .getCloudProviderCapacityConstraintInfoForCurrentInstanceSize(
                    asymmetricCluster, Optional.of(shards.get(i).getId()), NodeType.ELECTABLE)
                .isEmpty());
      }
    }
  }

  @Test
  public void testCheckComputeAutoScaleForShards() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _mmsGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
    final ShardedClusterDescription asymmetricClusterDescription =
        new ShardedClusterDescription(
            NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithInstanceSizes(
                _ndsGroupId,
                ASYMMETRIC_CLUSTER_NAME,
                List.of(AWSNDSInstanceSize.M40, AWSNDSInstanceSize.M40),
                true));
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _mmsGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
    final Cluster asymmetricCluster =
        setupCluster(
            _ndsGroupId,
            ASYMMETRIC_CLUSTER_NAME,
            asymmetricClusterDescription,
            null,
            ClusterCreateContext.forIssIntegrationTest());
    updateAutoScalingContextToReactiveContext(asymmetricCluster);

    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvc =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvc);
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(asymmetricCluster.getClusterDescription().getName())),
        asymmetricCluster.getClusterDescription());

    final ClusterDescription activeClusterDescription =
        _ndsClusterSvc
            .getActiveClusterDescription(
                asymmetricCluster.getClusterDescription().getGroupId(),
                asymmetricCluster.getClusterDescription().getName())
            .get();
    final ClusterDescription mergedClusterDescription =
        _ndsClusterSvc
            .getMergedClusterDescription(
                asymmetricCluster.getClusterDescription().getGroupId(),
                asymmetricCluster.getClusterDescription().getName())
            .get();
    final boolean instanceSizeChangeInProgress =
        !activeClusterDescription
            .getInstanceSizes(NodeType.ELECTABLE)
            .equals(mergedClusterDescription.getInstanceSizes(NodeType.ELECTABLE));

    // test scale both up
    {
      final List<ReplicaSetHardware> scaleUp =
          List.of(
              asymmetricCluster.getReplicaSetsWithShardData().get(0),
              asymmetricCluster.getReplicaSetsWithShardData().get(1));
      final List<ReplicaSetHardware> scaleDown = List.of();
      final List<ReplicaSetHardware> noChange = List.of();

      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForComputeShards_setup(
              asymmetricCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvc,
              true,
              true,
              true,
              false,
              null,
              null,
              null,
              null,
              scaleUp.stream().map(ReplicaSetHardware::getRsId).toList(),
              scaleDown.stream().map(ReplicaSetHardware::getRsId).toList(),
              noChange.stream().map(ReplicaSetHardware::getRsId).toList(),
              true);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          asymmetricCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  asymmetricCluster.getClusterDescription().getGroupId(),
                  asymmetricCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForComputeShards(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          true,
          true,
          instanceSizeChangeInProgress,
          scaleUp,
          scaleDown,
          noChange);
    }

    // test one up one down
    {
      final List<ReplicaSetHardware> scaleUp =
          List.of(asymmetricCluster.getReplicaSetsWithShardData().get(0));
      final List<ReplicaSetHardware> scaleDown =
          List.of(asymmetricCluster.getReplicaSetsWithShardData().get(1));
      final List<ReplicaSetHardware> noChange = List.of();

      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForComputeShards_setup(
              asymmetricCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvc,
              true,
              true,
              true,
              false,
              null,
              null,
              null,
              null,
              scaleUp.stream().map(ReplicaSetHardware::getRsId).toList(),
              scaleDown.stream().map(ReplicaSetHardware::getRsId).toList(),
              noChange.stream().map(ReplicaSetHardware::getRsId).toList(),
              true);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          asymmetricCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  asymmetricCluster.getClusterDescription().getGroupId(),
                  asymmetricCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForComputeShards(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          true,
          true,
          instanceSizeChangeInProgress,
          scaleUp,
          scaleDown,
          noChange);
    }

    // test one down one nothing
    {
      final List<ReplicaSetHardware> scaleUp = List.of();
      final List<ReplicaSetHardware> scaleDown =
          List.of(asymmetricCluster.getReplicaSetsWithShardData().get(1));
      final List<ReplicaSetHardware> noChange =
          List.of(asymmetricCluster.getReplicaSetsWithShardData().get(0));

      final Pair<ClusterDescription, AutoScalingContext> setup =
          testCheckAutoScaleForComputeShards_setup(
              asymmetricCluster,
              autoScaleSpy,
              computeClusterMetricsCalculatorSvc,
              true,
              true,
              true,
              false,
              null,
              null,
              null,
              null,
              scaleUp.stream().map(ReplicaSetHardware::getRsId).toList(),
              scaleDown.stream().map(ReplicaSetHardware::getRsId).toList(),
              noChange.stream().map(ReplicaSetHardware::getRsId).toList(),
              true);
      final ClusterDescription clusterDescription = setup.getLeft();
      final AutoScalingContext context = setup.getRight();
      final Date now = new Date();

      autoScaleSpy.checkAutoScale(
          _ndsGroupId,
          asymmetricCluster.getClusterDescription().getName(),
          new AutoScaleDecisions.Builder(),
          new PredictiveAutoScalingResult());

      final AutoScalingContext updatedContext =
          _autoScalingContextDao
              .find(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescription updatedClusterDescription =
          _ndsClusterSvc
              .getMergedClusterDescription(
                  asymmetricCluster.getClusterDescription().getGroupId(),
                  asymmetricCluster.getClusterDescription().getName())
              .get();

      validateCheckAutoScaleForComputeShards(
          now,
          clusterDescription,
          updatedClusterDescription,
          context,
          updatedContext,
          true,
          true,
          instanceSizeChangeInProgress,
          scaleUp,
          scaleDown,
          noChange);
    }
  }

  @Test
  public void testCheckComputeAutoScaleForShards_oplogFail() throws Exception {
    final ShardedClusterDescription asymmetricClusterDescription =
        new ShardedClusterDescription(
            NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithInstanceSizes(
                _ndsGroupId,
                ASYMMETRIC_CLUSTER_NAME,
                List.of(AWSNDSInstanceSize.M40, AWSNDSInstanceSize.M40),
                true));
    final Cluster asymmetricCluster =
        setupCluster(
            _ndsGroupId,
            ASYMMETRIC_CLUSTER_NAME,
            asymmetricClusterDescription,
            null,
            ClusterCreateContext.forIssIntegrationTest());
    updateAutoScalingContextToReactiveContext(asymmetricCluster);

    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvc =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvc);
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(asymmetricCluster.getClusterDescription().getName())),
        asymmetricCluster.getClusterDescription());

    final List<ReplicaSetHardware> scaleUp =
        List.of(asymmetricCluster.getReplicaSetsWithShardData().get(0));
    final List<ReplicaSetHardware> scaleDown =
        List.of(asymmetricCluster.getReplicaSetsWithShardData().get(1));
    final List<ReplicaSetHardware> noChange = List.of();

    final boolean canBeAutoScaledWithinOplogWindow = false;

    final Pair<ClusterDescription, AutoScalingContext> setup =
        testCheckAutoScaleForComputeShards_setup(
            asymmetricCluster,
            autoScaleSpy,
            computeClusterMetricsCalculatorSvc,
            true,
            true,
            true,
            false,
            null,
            null,
            null,
            null,
            scaleUp.stream().map(ReplicaSetHardware::getRsId).toList(),
            scaleDown.stream().map(ReplicaSetHardware::getRsId).toList(),
            noChange.stream().map(ReplicaSetHardware::getRsId).toList(),
            canBeAutoScaledWithinOplogWindow);

    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        asymmetricCluster.getClusterDescription().getName(),
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());
    final ClusterDescription clusterDescriptionAfter =
        _ndsClusterSvc
            .getMergedClusterDescription(
                asymmetricCluster.getClusterDescription().getGroupId(),
                asymmetricCluster.getClusterDescription().getName())
            .orElseThrow();

    // Replication specs should be unchanged
    Assertions.assertThat(setup.getLeft().getReplicationSpecsWithShardData())
        .isEqualTo(clusterDescriptionAfter.getReplicationSpecsWithShardData());
    verify(autoScaleSpy, times(1)).setOplogCheckFailedDate(any(), any(), any());
    verify(autoScaleSpy, never()).performComputeAutoScalingForShards(any(), any(), any(), any());
  }

  // The purpose of this test is to ensure that a shard cannot be downscaled if downscaling the
  // shard will make it go past the allowed instance size difference
  @Test
  public void testCheckComputeAutoScaleForShards_instanceSizeDifference_doNotAllowDownscale()
      throws Exception {
    // Cluster created with M40 and M60 tiers
    final ShardedClusterDescription asymmetricClusterDescription =
        new ShardedClusterDescription(
            NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithInstanceSizes(
                _ndsGroupId,
                ASYMMETRIC_CLUSTER_NAME,
                List.of(AWSNDSInstanceSize.M40, AWSNDSInstanceSize.M60),
                true));
    final Cluster asymmetricCluster =
        setupCluster(
            _ndsGroupId,
            ASYMMETRIC_CLUSTER_NAME,
            asymmetricClusterDescription,
            null,
            ClusterCreateContext.forIssIntegrationTest());
    updateAutoScalingContextToReactiveContext(asymmetricCluster);

    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvc =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvc);
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(asymmetricCluster.getClusterDescription().getName())),
        asymmetricCluster.getClusterDescription());

    // Attempt to scale down M40 shard
    final List<ReplicaSetHardware> scaleDown =
        List.of(asymmetricCluster.getReplicaSetsWithShardData().get(0));

    final Pair<ClusterDescription, AutoScalingContext> setup =
        testCheckAutoScaleForComputeShards_setup(
            asymmetricCluster,
            autoScaleSpy,
            computeClusterMetricsCalculatorSvc,
            true,
            true,
            true,
            false,
            null,
            null,
            null,
            null,
            List.of(),
            scaleDown.stream().map(ReplicaSetHardware::getRsId).toList(),
            List.of(),
            true);

    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        asymmetricCluster.getClusterDescription().getName(),
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final ClusterDescription clusterDescriptionAfterFirstAutoScale =
        _ndsClusterSvc
            .getMergedClusterDescription(
                asymmetricCluster.getClusterDescription().getGroupId(),
                asymmetricCluster.getClusterDescription().getName())
            .orElseThrow();

    // Replication specs should be unchanged as we do not allow shards to downscale past the
    // allowed instance size difference of 2
    verify(autoScaleSpy, never()).performComputeAutoScalingForShards(any(), any(), any(), any());
    Assertions.assertThat(setup.getLeft().getReplicationSpecsWithShardData())
        .isEqualTo(clusterDescriptionAfterFirstAutoScale.getReplicationSpecsWithShardData());

    // Set allowed instance size difference to 3
    _ndsGroupDao.setLimit(
        _ndsGroupId, FieldDefs.ALLOWED_SHARD_INSTANCE_SIZE_DIFFERENCE.getValue(), 3);

    testCheckAutoScaleForComputeShards_setup(
        asymmetricCluster,
        autoScaleSpy,
        computeClusterMetricsCalculatorSvc,
        true,
        true,
        true,
        false,
        null,
        null,
        null,
        null,
        List.of(),
        scaleDown.stream().map(ReplicaSetHardware::getRsId).toList(),
        List.of(),
        true);
    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        asymmetricCluster.getClusterDescription().getName(),
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final ClusterDescription clusterDescriptionAfterSecondAutoScale =
        _ndsClusterSvc
            .getMergedClusterDescription(
                asymmetricCluster.getClusterDescription().getGroupId(),
                asymmetricCluster.getClusterDescription().getName())
            .orElseThrow();

    // One shard should have been downscaled to M30
    verify(autoScaleSpy, times(1))
        .performComputeAutoScalingForShards(any(), argThat(map -> map.size() == 1), any(), any());
    assertEquals(
        AWSNDSInstanceSize.M30,
        clusterDescriptionAfterSecondAutoScale
            .getReplicationSpecById(scaleDown.get(0).getReplicationSpecId())
            .orElseThrow()
            .getElectableSpec()
            .getInstanceSize());
  }

  // The purpose of this test is to show that when the highest tier shard is scaled up, lower tier
  // shards will also be scaled up so the cluster does not violate the allowed instance size
  // difference
  @Test
  public void testCheckComputeAutoScaleForShards_instanceSizeDifference_scaleUpLowerShards()
      throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _mmsGroup, null, FeatureFlag.ATLAS_INDEPENDENT_SHARD_SCALING);
    // Cluster created with M40, M40 and M60 tiers
    final ShardedClusterDescription asymmetricClusterDescription =
        new ShardedClusterDescription(
            NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithInstanceSizes(
                _ndsGroupId,
                ASYMMETRIC_CLUSTER_NAME,
                List.of(AWSNDSInstanceSize.M40, AWSNDSInstanceSize.M40, AWSNDSInstanceSize.M60),
                true));
    final Cluster asymmetricCluster =
        setupCluster(
            _ndsGroupId,
            ASYMMETRIC_CLUSTER_NAME,
            asymmetricClusterDescription,
            null,
            ClusterCreateContext.forIssIntegrationTest());
    updateAutoScalingContextToReactiveContext(asymmetricCluster);

    final ComputeClusterMetricsCalculatorSvc computeClusterMetricsCalculatorSvc =
        spy(_computeClusterMetricsCalculatorSvc);
    final NDSReactiveAutoScaleSvc autoScaleSpy =
        createNDSAutoScaleSpy(computeClusterMetricsCalculatorSvc);
    getHostClusterForMeasurements(
        getHostnamesForMeasurements(
            getMergedClusterForMeasurements(asymmetricCluster.getClusterDescription().getName())),
        asymmetricCluster.getClusterDescription());

    // Scale up the M60 shard
    final List<ReplicaSetHardware> scaleUp =
        List.of(asymmetricCluster.getReplicaSetsWithShardData().get(2));

    testCheckAutoScaleForComputeShards_setup(
        asymmetricCluster,
        autoScaleSpy,
        computeClusterMetricsCalculatorSvc,
        true,
        true,
        true,
        false,
        null,
        null,
        null,
        null,
        scaleUp.stream().map(ReplicaSetHardware::getRsId).toList(),
        List.of(),
        List.of(),
        true);

    autoScaleSpy.checkAutoScale(
        _ndsGroupId,
        asymmetricCluster.getClusterDescription().getName(),
        new AutoScaleDecisions.Builder(),
        new PredictiveAutoScalingResult());

    final ClusterDescription clusterDescriptionAfterAutoScale =
        _ndsClusterSvc
            .getMergedClusterDescription(
                asymmetricCluster.getClusterDescription().getGroupId(),
                asymmetricCluster.getClusterDescription().getName())
            .orElseThrow();

    // All 3 shards should have been scaled up
    // M40 -> M50, M40 -> M50, M60 -> M80
    verify(autoScaleSpy, times(1))
        .performComputeAutoScalingForShards(any(), argThat(map -> map.size() == 3), any(), any());

    assertEquals(
        AWSNDSInstanceSize.M50,
        clusterDescriptionAfterAutoScale
            .getReplicationSpecsWithShardData()
            .get(0)
            .getElectableSpec()
            .getInstanceSize());

    assertEquals(
        AWSNDSInstanceSize.M50,
        clusterDescriptionAfterAutoScale
            .getReplicationSpecsWithShardData()
            .get(1)
            .getElectableSpec()
            .getInstanceSize());

    assertEquals(
        AWSNDSInstanceSize.M80,
        clusterDescriptionAfterAutoScale
            .getReplicationSpecsWithShardData()
            .get(2)
            .getElectableSpec()
            .getInstanceSize());
  }

  private ClusterDescription getClusterDescriptionForMeasurements() {
    return _ndsClusterSvc
        .getActiveClusterDescription(_ndsGroupId, _cluster.getClusterDescription().getName())
        .get();
  }

  private ClusterDescription getShardedClusterDescriptionForMeasurements() {
    return _ndsClusterSvc
        .getActiveClusterDescription(_ndsGroupId, _shardedCluster.getClusterDescription().getName())
        .get();
  }

  private Cluster getMergedClusterForMeasurements(final String pName) throws SvcException {
    return _ndsClusterSvc.getMergedCluster(_mmsGroup.getId(), pName);
  }

  private List<String> getHostnamesForMeasurements(final Cluster pMergedCluster) {
    return pMergedCluster.getReplicaSets().stream()
        .findFirst()
        .map(
            o ->
                o.getHardware().stream()
                    .map(h -> h.getHostnameForAgents().get())
                    .collect(Collectors.toList()))
        .get();
  }

  private HostCluster getHostClusterForMeasurements(
      final List<String> pHostnames, final ClusterDescription pClusterDescription)
      throws Exception {
    final HostCluster hostCluster =
        MmsFactory.createReplicaSet_V_4_2_WithAutomationConfigAndMultipleHostnames(
            _organization,
            _mmsGroup,
            _user,
            pHostnames,
            String.format("%s-shard-0", pClusterDescription.getDeploymentClusterName()));
    return hostCluster;
  }

  private List<List<String>> getHostnamesForShardedClusterForMeasurements(
      final Cluster pMergedCluster) {
    return pMergedCluster.getReplicaSets().stream()
        .map(
            o ->
                o.getHardware().stream()
                    .map(h -> h.getHostnameForAgents().get())
                    .collect(Collectors.toList()))
        .collect(Collectors.toList());
  }

  private List<HostCluster> getShardedHostClustersForMeasurements(
      final List<List<String>> pHostnames, final Cluster pCluster) throws Exception {
    MmsFactory.createCluster_V_4_2_WithAutomationConfigAndMultipleHostnames(
        _organization,
        _mmsGroup,
        _user,
        pHostnames.stream()
            .filter(hl -> hl.stream().anyMatch(hn -> !hn.contains("-config-")))
            .collect(Collectors.toList()),
        pHostnames.stream()
            .filter(hl -> hl.stream().anyMatch(hn -> hn.contains("-config-")))
            .findFirst()
            .get(),
        pCluster.getClusterDescription().getDeploymentClusterName());
    return _ndsAutoScaleSvc.getHostClustersForCluster(pCluster);
  }

  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  private void setDiskSizeGBForReplicaSetHardware(
      final ClusterDescription pClusterDescription, final int pDiskSizeGB) {
    final List<ReplicaSetHardware> replicaSetHardwares =
        _replicaSetHardwareDao.findByCluster(
            pClusterDescription.getGroupId(), pClusterDescription.getName());
    replicaSetHardwares.stream()
        .filter(rsh -> rsh.getType() != ReplicaSetType.CONFIG)
        .flatMap(rsh -> rsh.getAllHardware().map(ih -> Pair.of(rsh, ih)))
        .forEach(
            indexInstanceHardwarePair ->
                _replicaSetHardwareDao.setInstanceField(
                    indexInstanceHardwarePair.getLeft().getId(),
                    indexInstanceHardwarePair.getRight().getInstanceId(),
                    false,
                    InstanceHardware.FieldDefs.DISK_SIZE_GB,
                    pDiskSizeGB));
  }

  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  private void setLastInstanceSizeModifyDateForReplicaSetHardware(
      final ClusterDescription pClusterDescription, final List<Date> pLastInstanceSizeModifyDates) {
    final List<ReplicaSetHardware> replicaSetHardwares =
        _replicaSetHardwareDao.findByCluster(
            pClusterDescription.getGroupId(), pClusterDescription.getName());
    final LoopingListIterator<Date> dateIterator =
        new LoopingListIterator<>(pLastInstanceSizeModifyDates);
    replicaSetHardwares.stream()
        .filter(rsh -> rsh.getType() != ReplicaSetType.CONFIG)
        .flatMap(rsh -> rsh.getAllHardware().map(ih -> Pair.of(rsh, ih)))
        .forEach(
            indexInstanceHardwarePair ->
                _replicaSetHardwareDao.setInstanceField(
                    indexInstanceHardwarePair.getLeft().getId(),
                    indexInstanceHardwarePair.getRight().getInstanceId(),
                    false,
                    InstanceHardware.FieldDefs.LAST_INSTANCE_SIZE_MODIFY_DATE,
                    dateIterator.next()));
  }

  @SuppressWarnings("deprecation") // TODO CLOUDP-303714: remove deprecated dedicated config logic
  private void setLastResumeFromPauseDateForReplicaSetHardware(
      final ClusterDescription pClusterDescription,
      final List<Pair<Boolean, Date>> pPausedAndLastResumeFromPauseDates) {
    final List<ReplicaSetHardware> replicaSetHardwares =
        _replicaSetHardwareDao.findByCluster(
            pClusterDescription.getGroupId(), pClusterDescription.getName());
    final LoopingListIterator<Pair<Boolean, Date>> pausedAndLastResumeFromPauseDateIterator =
        new LoopingListIterator<>(pPausedAndLastResumeFromPauseDates);
    replicaSetHardwares.stream()
        .filter(rsh -> rsh.getType() != ReplicaSetType.CONFIG)
        .flatMap(rsh -> rsh.getAllHardware().map(ih -> Pair.of(rsh, ih)))
        .forEach(
            indexInstancHardwarePair -> {
              final Pair<Boolean, Date> pausedAndLastResumeFromPauseDate =
                  pausedAndLastResumeFromPauseDateIterator.next();
              _replicaSetHardwareDao.setInstanceField(
                  indexInstancHardwarePair.getLeft().getId(),
                  indexInstancHardwarePair.getRight().getInstanceId(),
                  false,
                  InstanceHardware.FieldDefs.IS_PAUSED,
                  pausedAndLastResumeFromPauseDate.getLeft());
              _replicaSetHardwareDao.setInstanceField(
                  indexInstancHardwarePair.getLeft().getId(),
                  indexInstancHardwarePair.getRight().getInstanceId(),
                  false,
                  InstanceHardware.FieldDefs.LAST_RESUME_DATE,
                  pausedAndLastResumeFromPauseDate.getLeft()
                      ? null
                      : pausedAndLastResumeFromPauseDate.getRight());
            });
  }

  private NDSReactiveAutoScaleSvc createNDSAutoScaleSpy(
      final ComputeClusterMetricsCalculatorSvc pComputeClusterMetricsCalculatorSvc) {
    return spy(
        new NDSReactiveAutoScaleSvc(
            _ndsAutoScaleAlertSvc,
            _ndsClusterSvc,
            _groupSvc,
            _appSettings,
            _autoScalingContextDao,
            _hostClusterLifecycleSvc,
            _auditSvc,
            _ndsGroupSvc,
            _ndsComputeClusterMetricsSvc,
            pComputeClusterMetricsCalculatorSvc,
            _awsCapacityDenylistSvc,
            _gcpCapacityDenylistSvc,
            _informationalAlertSvc,
            _chefServerStatusSvc,
            _azureCapacityDenylistSvc,
            _computeAutoScalingDecisionCoordinatorSvc,
            _ndsAutoScaleValidationSvc));
  }

  private void updateAutoScalingContextToReactiveContext(final Cluster pCluster) {
    // Legacy context is no compatible with asymmetric shard autoscaling replacing
    final AutoScalingContext asymmetricContext =
        AutoScalingContext.getDefaultContext(
            pCluster.getClusterDescription().getGroupId(),
            pCluster.getClusterDescription().getName(),
            AutoScalingMode.SHARD);
    _autoScalingContextDao.save(
        asymmetricContext.toBuilder()
            .baseComputeContext(
                ComputeAutoScalingContextUtil.getUpdatedContextWithNewShards(
                    (ShardComputeAutoScalingContext) asymmetricContext.getBaseComputeContext(),
                    pCluster))
            .analyticsComputeContext(
                ComputeAutoScalingContextUtil.getUpdatedContextWithNewShards(
                    (ShardComputeAutoScalingContext) asymmetricContext.getAnalyticsComputeContext(),
                    pCluster))
            .build());
  }
}
