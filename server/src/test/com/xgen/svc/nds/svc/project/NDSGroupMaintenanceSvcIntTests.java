package com.xgen.svc.nds.svc.project;

import static com.xgen.cloud.nds.activity._public.event.audit.NDSMaintenanceWindowAudit.Type.MAINTENANCE_AUTO_DEFERRED;
import static com.xgen.cloud.nds.project._public.constants.NDSMaintenanceConstants.IN_ADVANCED_NOTIFICATION_HOURS;
import static com.xgen.cloud.nds.project._public.model.ClusterDescription.State.IDLE;
import static com.xgen.cloud.nds.project._public.model.ClusterDescription.State.WORKING;
import static com.xgen.cloud.nds.project._public.model.ElevatedHealthMonitoringModelTestFactory.getMonitoringThresholds;
import static com.xgen.cloud.nds.project._public.model.MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours;
import static com.xgen.cloud.nds.project._public.model.MaintenanceRelease.MAINTENANCE_WINDOW;
import static com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.VALID_MONGODB_MAJOR_VERSIONS;
import static com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringSvc.NDS_EHM_EXTENSION_ENABLED;
import static com.xgen.svc.nds.model.NDSModelTestFactory.TEST_DEDICATED_MONGODB_MAJOR_VERSION;
import static com.xgen.svc.nds.svc.NDSEncryptionAtRestSvc.KMIP_MASTER_KEY_ROTATION_PERIOD_DAYS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.when;

import com.amazonaws.services.ec2.model.VolumeType;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.system._public.model.PlatformConstants.InternalArchitectures;
import com.xgen.cloud.common.system._public.model.PlatformConstants.Platforms;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.deployment._public.model.AtlasProxyTemplate;
import com.xgen.cloud.deployment._public.model.AtlasUserIdentityServiceTemplate;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.BaseAgentConfig;
import com.xgen.cloud.deployment._public.model.BiConnectorTemplate;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.MaintainedEnvoyTemplate;
import com.xgen.cloud.deployment._public.model.MongoDbBuild;
import com.xgen.cloud.deployment._public.model.MongoDbToolsTemplate;
import com.xgen.cloud.deployment._public.model.MongotTemplate;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6;
import com.xgen.cloud.deployment._public.model.SearchEnvoyTemplate;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.ifrabtest._public.model.ExperimentStatus;
import com.xgen.cloud.ifrabtest._public.model.IFRRolloutConfig.RolloutType;
import com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSMaintenanceWindowAudit;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.AWSCloudProviderContainerDao;
import com.xgen.cloud.nds.aws._private.dao.AWSInstanceHardwareDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAccount.AWSAccountBuilder;
import com.xgen.cloud.nds.aws._public.model.AWSAvailabilityZone;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegion;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.NDSAWSKMS;
import com.xgen.cloud.nds.aws._public.model.autoscaling.ui.AWSAutoScalingView;
import com.xgen.cloud.nds.azure._public.model.NDSAzureKeyVault;
import com.xgen.cloud.nds.azure._public.model.SupportedAzureEnvironment;
import com.xgen.cloud.nds.capacity._public.svc.CloudProviderAvailabilityFactory;
import com.xgen.cloud.nds.cloudprovider._private.dao.CloudProviderSettingsDao;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderSettings;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterCreateContext;
import com.xgen.cloud.nds.common._public.model.BiConnectorReadPreference;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.common._public.model.ProxyVersion;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.dbcheck._public.svc.DbCheckSvc;
import com.xgen.cloud.nds.free._public.model.FreeCloudProviderContainer;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.gcp._public.model.NDSGoogleCloudKMS;
import com.xgen.cloud.nds.ifr._private.dao.IFREventDao;
import com.xgen.cloud.nds.ifr._private.dao.WaveDao;
import com.xgen.cloud.nds.ifr._public.model.IFREvent;
import com.xgen.cloud.nds.ifr._public.model.IFREvent.IFREventType;
import com.xgen.cloud.nds.ifr._public.model.WaveAllocationInternal;
import com.xgen.cloud.nds.maintenance._public.model.NDSInternalMaintenanceRollout;
import com.xgen.cloud.nds.maintenance._public.svc.NDSInternalMaintenanceRolloutSvc;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyStatus;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionProcessArgsDao;
import com.xgen.cloud.nds.project._private.dao.EolVersionUpgradeHistoryDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupMaintenanceDao;
import com.xgen.cloud.nds.project._private.dao.NdsMaintenanceHistoryDao;
import com.xgen.cloud.nds.project._public.constants.NDSMaintenanceConstants;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.InternalClusterRole;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgs.Type;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionProcessArgsUpdatable;
import com.xgen.cloud.nds.project._public.model.EolVersionUpgradeHistory;
import com.xgen.cloud.nds.project._public.model.EolVersionUpgradeHistory.UpgradeType;
import com.xgen.cloud.nds.project._public.model.MaintenanceCheckResult;
import com.xgen.cloud.nds.project._public.model.MaintenanceHistoryMatchCriteria;
import com.xgen.cloud.nds.project._public.model.MaintenanceProtectedHours;
import com.xgen.cloud.nds.project._public.model.MaintenanceRelease;
import com.xgen.cloud.nds.project._public.model.MaintenanceType;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus.State;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSGroupMaintenanceWindow;
import com.xgen.cloud.nds.project._public.model.NdsMaintenanceHistory;
import com.xgen.cloud.nds.project._public.model.NdsMaintenanceHistory.InstanceHardwareDetails;
import com.xgen.cloud.nds.project._public.model.ReleaseCohorts;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.SchedulingBehavior;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.ActionToMonitor;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringThresholds;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.project._public.model.versions.FixedAgentVersion;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.project._public.model.versions.IFRState;
import com.xgen.cloud.nds.project._public.model.versions.IFRState.WaveStatus;
import com.xgen.cloud.nds.project._public.model.versions.PhasedReleaseCriteria;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.FieldDefs;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.PhasedVersionFactory;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.PhasedVersionFactory.MongoDBEOLPhasedVersion;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersionParameters;
import com.xgen.cloud.nds.project._public.model.versions.ReleaseMode;
import com.xgen.cloud.nds.project._public.svc.NDSClusterSamplingSvc;
import com.xgen.cloud.nds.project._public.svc.NdsMaintenanceHistorySvc;
import com.xgen.cloud.nds.project._public.svc.admin.CriticalMaintenanceRunChunkJobStateSvc;
import com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringResultSvc;
import com.xgen.cloud.nds.project._public.svc.versions.PhasedVersionSvc;
import com.xgen.cloud.nds.project._public.util.NDSMaintenanceDateCalculationUtil;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.nds.resourcepolicy._public.model.Policy;
import com.xgen.cloud.nds.resourcepolicy._public.model.ResourcePolicy;
import com.xgen.cloud.nds.resourcepolicy._public.model.ResourcePolicyValidationError;
import com.xgen.cloud.nds.resourcepolicy._public.svc.AtlasResourcePolicySvc;
import com.xgen.cloud.nds.resourcepolicy.util.AtlasResourcePolicyTestUtil;
import com.xgen.cloud.nds.serverless._private.dao.ServerlessMTMClusterDao;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.serverless._public.model.ServerlessNDSDefaults;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.EnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.spothealthcheck.util.NDSClusterSpotHealthCheckTestUtil;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.MongoDbVersionEolExtension;
import com.xgen.cloud.organization._public.model.MongoDbVersionEolExtensionStatus;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription;
import com.xgen.cloud.search.decoupled.config._public.model.aws.AWSSearchInstanceSize;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.cloud.search.settings._public.settings.SearchAppSettings;
import com.xgen.cloud.search.util._public.version.MongotVersion;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.liveimport.dao.LiveImportDao;
import com.xgen.module.liveimport.model.LiveImport;
import com.xgen.module.liveimport.model.MongomirrorPullLiveImport;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.BiConnectorView;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.svc.nds.serverless.dao.ServerlessLoadBalancingDeploymentDao;
import com.xgen.svc.nds.svc.CustomMongoDbBuildSvc;
import com.xgen.svc.nds.svc.FTSIndexConfigSvc;
import com.xgen.svc.nds.svc.planning.NDSPlanningSvc;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc.TargetMongoDbEolVersion;
import jakarta.inject.Inject;
import java.time.Clock;
import java.time.Duration;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This class is really a subset of NDSPlanningSvcIntTests where the planner's result is determined
 * by the changes made by the NDSGroupMaintenanceSvc
 */
public class NDSGroupMaintenanceSvcIntTests extends JUnit5BaseSvcTest {

  private static final Logger LOG = LoggerFactory.getLogger(NDSGroupMaintenanceSvcIntTests.class);

  @Inject NDSGroupSvc _ndsGroupSvc;

  @Inject NDSGroupMaintenanceSvc _ndsMaintenanceSvc;

  @Inject NDSPlanningSvc _ndsPlanningSvc;

  @Inject NDSClusterSvc _clusterSvc;

  @Inject AutomationMongoDbVersionSvc _versionSvc;

  @Inject PhasedVersionSvc _phasedVersionSvc;

  @Inject ReplicaSetHardwareSvc _hardwareSvc;

  @Inject NDSGroupDao _ndsGroupDao;

  @Inject AutomationConfigPublishingSvc _automationConfigSvc;

  @Inject AppSettings _appSettings;

  @Inject CloudProviderSettingsDao _cloudProviderSettingsDao;

  @Inject AWSAccountDao _awsAccountDao;

  @Inject AWSInstanceHardwareDao _awsInstanceHardwareDao;

  @Inject AWSCloudProviderContainerDao _containerDao;

  @Inject CloudProviderAvailabilityFactory _cloudProviderAvailabilityFactory;

  @Inject ClusterDescriptionDao _clusterDescriptionDao;

  @Inject ClusterDescriptionProcessArgsDao _clusterDescriptionProcessArgsDao;

  @Inject LiveImportDao _liveImportDao;

  @Inject AuditSvc _auditSvc;

  @Inject NDSGroupMaintenanceDao _ndsGroupMaintenanceDao;

  @Inject FTSIndexConfigSvc _ftsIndexConfigSvc;

  @Inject GroupDao _groupDao;

  @Inject NDSInternalMaintenanceRolloutSvc _internalMaintenanceRolloutSvc;

  @Inject ElevatedHealthMonitoringResultSvc _elevatedHealthMonitoringResultSvc;

  @Inject MTMClusterDao _mtmClusterDao;
  @Inject ServerlessMTMClusterDao _serverlessMTMClusterDao;

  @Inject NdsMaintenanceHistorySvc _ndsMaintenanceHistorySvc;
  @Inject NdsMaintenanceHistoryDao _ndsMaintenanceHistoryDao;
  @Inject NDSClusterSamplingSvc _ndsClusterSamplingSvc;
  @Inject DbCheckSvc _dbCheckSvc;

  @Inject CustomMongoDbBuildSvc _customMongoDbBuildSvc;
  @Inject ServerlessLoadBalancingDeploymentDao _serverlessLoadBalancingDeploymentDao;
  @Inject SearchDeploymentDescriptionSvc _searchDeploymentDescriptionSvc;
  @Inject PhasedVersionFactory _phasedVersionFactory;
  @Inject AtlasResourcePolicySvc _atlasResourcePolicySvc;
  @Inject OrganizationDao _organizationDao;
  @Inject EolVersionUpgradeHistoryDao _eolVersionUpgradeHistoryDao;

  @Inject WaveDao _waveDao;

  @Inject IFREventDao _ifrEventDao;

  @Inject NDSClusterSpotHealthCheckTestUtil _ndsClusterSpotHealthCheckTestUtil;

  private NDSGroupSvc _groupSvc;
  private NDSGroupMaintenanceSvc _maintenanceSvc;
  private NDSMaintenanceDateCalculationUtil _ndsMaintenanceDateCalculationSvc;
  private NDSGroup _ndsGroup;
  private Group _group;
  private Organization _organization;
  private Date _currentTime;
  private Map<ObjectId, AtomicInteger> _numMonitoringAddedMap;

  @BeforeEach
  public void setup() throws Exception {
    super.setUp();
    _versionSvc.invalidateVersionManifestCache();
    _versionSvc.autoUpdateDefaultVersions();
    _clusterSvc.start();

    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_organization, "cus_0001");
    _groupDao.save(_group);
    MmsFactory.createUser(_group);
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    final AWSAccount awsAccount =
        new AWSAccountBuilder().setName("test").setAccessKey("test").setSecretKey("est").build();
    final AWSCloudProviderContainer futureContainer =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());

    final AWSRegion region = new AWSRegion(AWSRegionName.US_EAST_1);
    region
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                futureContainer.getSubnets()[0].getAvailabilityZone(),
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                futureContainer.getSubnets()[0].getZoneId()));
    region
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                futureContainer.getSubnets()[1].getAvailabilityZone(),
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                futureContainer.getSubnets()[1].getZoneId()));
    region
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                futureContainer.getSubnets()[2].getAvailabilityZone(),
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                futureContainer.getSubnets()[2].getZoneId()));
    awsAccount.getRegions().add(region);
    _awsAccountDao.save(awsAccount);

    final AWSCloudProviderContainer awsContainer =
        new AWSCloudProviderContainer(awsAccount.getId(), AWSRegionName.US_EAST_1);
    _ndsGroupDao.addCloudContainer(_group.getId(), awsContainer);
    _ndsGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();

    _containerDao.setProvisionedFields(
        _ndsGroup.getGroupId(),
        _ndsGroup.getCloudProviderContainers().get(0).getId(),
        "vpc-id",
        "igw-id",
        List.of(futureContainer.getSubnets()));
    _ndsGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();

    _cloudProviderSettingsDao.save(new CloudProviderSettings(CloudProvider.AWS, 100));
    _cloudProviderSettingsDao.save(new CloudProviderSettings(CloudProvider.FREE, 100));

    // Set the currentTime to 10:30 AM Today
    final Calendar currentTimeCalendar = Calendar.getInstance();
    currentTimeCalendar.set(Calendar.HOUR_OF_DAY, 10);
    currentTimeCalendar.set(Calendar.MINUTE, 30);
    currentTimeCalendar.set(Calendar.SECOND, 0);
    currentTimeCalendar.set(Calendar.MILLISECOND, 0);
    _currentTime = currentTimeCalendar.getTime();

    final Clock fixedClock = Clock.fixed(_currentTime.toInstant(), ZoneOffset.UTC);
    _ndsMaintenanceDateCalculationSvc = new NDSMaintenanceDateCalculationUtil(fixedClock);
    _maintenanceSvc = Mockito.spy(_ndsMaintenanceSvc);
    when(_maintenanceSvc.getDateCalculationUtil()).thenReturn(_ndsMaintenanceDateCalculationSvc);

    final NdsMaintenanceHistorySvc maintenanceHistorySvc =
        new NdsMaintenanceHistorySvc(_ndsMaintenanceHistoryDao, _ndsMaintenanceDateCalculationSvc);
    when(_maintenanceSvc.getMaintenanceHistorySvc()).thenReturn(maintenanceHistorySvc);

    _groupSvc = Mockito.spy(_ndsGroupSvc);
    _phasedVersionSvc.getVersionCache().invalidateAll();

    _numMonitoringAddedMap = new HashMap<>();
  }

  private Calendar getCalendarInstance(final TimeZone pTimeZone) {
    final Calendar cal = Calendar.getInstance(pTimeZone);
    cal.setTime(_currentTime);
    return cal;
  }

  private Calendar getCalendarInstanceForGroup() {
    final String groupTimezoneId =
        _group.getDefaultTimeZoneId() != null ? _group.getDefaultTimeZoneId() : "UTC";
    return getCalendarInstance(TimeZone.getTimeZone(groupTimezoneId));
  }

  @Test
  public void testGetMongoDBMajorVersionToFullVersionMapping() throws SvcException {
    final PhasedVersion fiveZeroPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, "5.0"));
    _phasedVersionSvc.save(fiveZeroPhasedVersion);

    final Map<String, String> versionsMap =
        _maintenanceSvc.getMongoDBMajorVersionToFullVersionMapping();
    assertNotNull(versionsMap);
    assertFalse(versionsMap.isEmpty());
    assertEquals(VALID_MONGODB_MAJOR_VERSIONS.size() + 1, versionsMap.size());
    assertEquals(
        _phasedVersionSvc
            .findBySoftwareType(SoftwareType.MONGODB_5_0)
            .orElseThrow()
            .getTargetVersion(),
        versionsMap.get("CONTINUOUS_DELIVERY_MONGODB_VERSION"));
  }

  @Test
  public void testGetMongoDBMajorVersionToFullVersionMapping_fullCDVersion() throws SvcException {
    final PhasedVersion fiveOnePhasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, "5.1.2"));
    _phasedVersionSvc.save(fiveOnePhasedVersion);
    final Map<String, String> versionsMap =
        _maintenanceSvc.getMongoDBMajorVersionToFullVersionMapping();
    assertNotNull(versionsMap);
    assertFalse(versionsMap.isEmpty());
    assertEquals(VALID_MONGODB_MAJOR_VERSIONS.size() + 1, versionsMap.size());
    assertEquals(
        fiveOnePhasedVersion.getTargetVersion(),
        versionsMap.get("CONTINUOUS_DELIVERY_MONGODB_VERSION"));
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_validation_throws() throws Exception {
    final Group group = MmsFactory.createGroup(_organization, "cus_0002");
    final ObjectId groupId = group.getId();
    _groupSvc.ensureGroup(groupId);
    _groupDao.save(group);

    final int dayOfWeek = 5;
    final int hourOfDay = 12;
    final NDSGroupMaintenanceWindow maintenanceWindow =
        new NDSGroupMaintenanceWindow(dayOfWeek, hourOfDay);

    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());

    final NDSGroupMaintenanceWindow maintenanceWindowSaved =
        _groupSvc.find(groupId).orElseThrow().getMaintenanceWindow();
    assertTrue(maintenanceWindowSaved.isUserDefined());
    assertEquals(dayOfWeek, (int) maintenanceWindowSaved.getDayOfWeek());
    assertEquals(hourOfDay, (int) maintenanceWindowSaved.getHourOfDay());

    // other fields get set to defaults
    assertFalse(maintenanceWindowSaved.getAdvanceNotificationSendDate().isPresent());
    assertEquals(0, (int) maintenanceWindowSaved.getNumberOfDeferrals());
    assertFalse(maintenanceWindowSaved.getStartASAP());

    // Invalid group id
    {
      final SvcException svcException =
          assertThrows(
              SvcException.class,
              () ->
                  _maintenanceSvc.setUserDefinedMaintenanceWindow(
                      new ObjectId(),
                      new NDSGroupMaintenanceWindow(dayOfWeek, hourOfDay),
                      AuditInfoHelpers.fromSystem()));

      assertEquals(NDSErrorCode.INVALID_GROUP_ID, svcException.getErrorCode());
    }

    // Invalid day of week
    {
      final int badDayOfWeek = 8;
      final SvcException svcException =
          assertThrows(
              SvcException.class,
              () ->
                  _maintenanceSvc.setUserDefinedMaintenanceWindow(
                      groupId,
                      new NDSGroupMaintenanceWindow(badDayOfWeek, hourOfDay),
                      AuditInfoHelpers.fromSystem()));

      assertEquals(
          NDSErrorCode.INVALID_MAINTENANCE_WINDOW_DAY_OF_WEEK, svcException.getErrorCode());
    }

    // Invalid hour of day
    {
      final int badHourOfDay = 30;
      final SvcException svcException =
          assertThrows(
              SvcException.class,
              () ->
                  _maintenanceSvc.setUserDefinedMaintenanceWindow(
                      groupId,
                      new NDSGroupMaintenanceWindow(dayOfWeek, badHourOfDay),
                      AuditInfoHelpers.fromSystem()));

      assertEquals(
          NDSErrorCode.INVALID_MAINTENANCE_WINDOW_HOUR_OF_DAY, svcException.getErrorCode());
    }
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_validation_performBy_latestCompletionDate_throws()
      throws Exception {
    final int dayOfWeek = 5;
    final NDSGroupMaintenanceWindow maintenanceWindow =
        NDSGroupMaintenanceWindow.builder()
            .isUserDefined(true)
            .hourOfDay(8)
            .dayOfWeek(dayOfWeek)
            .build();
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        _ndsGroup.getGroupId(), maintenanceWindow, AuditInfoHelpers.fromSystem());

    // 1 day later so there is a maintenance window change, and it's above the max
    final NDSGroupMaintenanceWindow newMaintenanceWindow =
        maintenanceWindow.toBuilder().dayOfWeek(dayOfWeek + 1).build();

    final Date nextMaintenanceDate =
        _ndsMaintenanceDateCalculationSvc
            .getNextUserDefinedMaintenanceStartDateTime(
                newMaintenanceWindow,
                NDSMaintenanceDateCalculationUtil.getGroupUserMaintenanceTimeZoneId(_group))
            .orElseThrow()
            .getTime();

    _maintenanceSvc.resetMaintenanceAfterCompletionForGroup(
        reloadNdsGroup(),
        DateUtils.addDays(nextMaintenanceDate, -(NDSMaintenanceConstants.MAX_DEFERRAL_DAYS + 1)),
        LOG);

    // Act
    final SvcException svcException =
        assertThrows(
            SvcException.class,
            () ->
                _maintenanceSvc.setUserDefinedMaintenanceWindow(
                    _ndsGroup.getGroupId(), newMaintenanceWindow, AuditInfoHelpers.fromSystem()));

    assertEquals(NDSErrorCode.NON_COMPLIANT_MAINTENANCE_WINDOW, svcException.getErrorCode());
  }

  @Test
  public void
      testSetUserDefinedMaintenanceWindow_validation_performBy_initialMaintenanceDate_throws()
          throws Exception {
    final int dayOfWeek = 2;
    final NDSGroupMaintenanceWindow maintenanceWindow =
        NDSGroupMaintenanceWindow.builder()
            .isUserDefined(true)
            .hourOfDay(8)
            .dayOfWeek(dayOfWeek)
            .build();

    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        _ndsGroup.getGroupId(), maintenanceWindow, AuditInfoHelpers.fromSystem());

    // 1 day later so there is a maintenance window change
    final NDSGroupMaintenanceWindow newMaintenanceWindow =
        maintenanceWindow.toBuilder().dayOfWeek(dayOfWeek + 1).build();

    final Date nextMaintenanceDate =
        _ndsMaintenanceDateCalculationSvc
            .getNextUserDefinedMaintenanceStartDateTime(
                newMaintenanceWindow,
                NDSMaintenanceDateCalculationUtil.getGroupUserMaintenanceTimeZoneId(_group))
            .orElseThrow()
            .getTime();

    _ndsGroupMaintenanceDao.setInitialScheduledMaintenanceDate(
        _ndsGroup.getGroupId(),
        DateUtils.addDays(nextMaintenanceDate, -(NDSMaintenanceConstants.MAX_DEFERRAL_DAYS + 1)));

    // Act
    final SvcException svcException =
        assertThrows(
            SvcException.class,
            () ->
                _maintenanceSvc.setUserDefinedMaintenanceWindow(
                    _ndsGroup.getGroupId(), newMaintenanceWindow, AuditInfoHelpers.fromSystem()));

    assertEquals(NDSErrorCode.NON_COMPLIANT_MAINTENANCE_WINDOW, svcException.getErrorCode());
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_validation_performByDate() throws SvcException {
    final int dayOfWeek = 2;
    final NDSGroupMaintenanceWindow maintenanceWindow =
        NDSGroupMaintenanceWindow.builder()
            .isUserDefined(true)
            .hourOfDay(8)
            .dayOfWeek(dayOfWeek)
            .build();

    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        _ndsGroup.getGroupId(), maintenanceWindow, AuditInfoHelpers.fromSystem());

    // 1 day later so there is a maintenance window change
    final NDSGroupMaintenanceWindow newMaintenanceWindow =
        maintenanceWindow.toBuilder().dayOfWeek(dayOfWeek + 1).build();

    final Date nextMaintenanceDate =
        _ndsMaintenanceDateCalculationSvc
            .getNextUserDefinedMaintenanceStartDateTime(
                newMaintenanceWindow,
                NDSMaintenanceDateCalculationUtil.getGroupUserMaintenanceTimeZoneId(_group))
            .orElseThrow()
            .getTime();

    _ndsGroupMaintenanceDao.setInitialScheduledMaintenanceDate(
        _ndsGroup.getGroupId(),
        DateUtils.addDays(nextMaintenanceDate, -(NDSMaintenanceConstants.MAX_DEFERRAL_DAYS - 3)));

    // Act
    assertDoesNotThrow(
        () ->
            _maintenanceSvc.setUserDefinedMaintenanceWindow(
                _ndsGroup.getGroupId(), newMaintenanceWindow, AuditInfoHelpers.fromSystem()));
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_ProtectedHours() throws Exception {
    final Group group = MmsFactory.createGroup(_organization, "cus_0002");
    final ObjectId groupId = group.getId();
    _groupSvc.ensureGroup(groupId);
    _groupDao.save(group);

    // Setting protected hours requires a maintenance window to be set
    try {
      final NDSGroupMaintenanceWindow maintenanceWindow =
          new NDSGroupMaintenanceWindow(
              new BasicDBObject()
                  .append(
                      "protectedHours",
                      MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(3, 8)
                          .toDBObject()));

      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      fail("Expected setting protected hours without user-defined maintenance window to fail");
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS, pE.getErrorCode());
    }

    // Protected hours must be at least 4 hours
    try {
      final NDSGroupMaintenanceWindow maintenanceWindow =
          new NDSGroupMaintenanceWindow(
              3, 8, false, MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(3, 6));
      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      fail("Expected setting protected hours less than 4 hours to fail");
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS, pE.getErrorCode());
    }

    // Protected hours must be at least 4 hours, including when crossing midnight
    try {
      final NDSGroupMaintenanceWindow maintenanceWindow =
          new NDSGroupMaintenanceWindow(
              3,
              8,
              false,
              MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(22, 1));
      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      fail("Expected setting protected hours less than 4 hours to fail");
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS, pE.getErrorCode());
    }

    // Protected hours cannot be more than 18 hours
    try {
      final NDSGroupMaintenanceWindow maintenanceWindow =
          new NDSGroupMaintenanceWindow(
              3,
              8,
              false,
              MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(3, 22));
      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      fail("Expected setting protected hours more than 18 hours to fail");
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS, pE.getErrorCode());
    }

    // Protected hours cannot be more than 18 hours, including when crossing midnight
    try {
      final NDSGroupMaintenanceWindow maintenanceWindow =
          new NDSGroupMaintenanceWindow(
              3,
              8,
              false,
              MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(15, 10));
      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      fail("Expected setting protected hours more than 18 hours to fail");
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_MAINTENANCE_WINDOW_PROTECTED_HOURS, pE.getErrorCode());
    }

    // valid protected hours can be set
    {
      final NDSGroupMaintenanceWindow maintenanceWindow =
          new NDSGroupMaintenanceWindow(
              3,
              8,
              false,
              MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(9, 17));
      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      final MaintenanceProtectedHours protectedHours =
          _ndsGroupSvc
              .find(groupId)
              .map(NDSGroup::getMaintenanceWindow)
              .map(NDSGroupMaintenanceWindow::getProtectedHours)
              .orElseThrow();
      assertTrue(protectedHours.isUserDefined());
      assertEquals(9, (int) protectedHours.getStartHourOfDay());
      assertEquals(17, (int) protectedHours.getEndHourOfDay());
    }

    // valid protected hours can be set, including when crossing midnight
    {
      final NDSGroupMaintenanceWindow maintenanceWindow =
          new NDSGroupMaintenanceWindow(
              3,
              8,
              false,
              MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(23, 6));
      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      final MaintenanceProtectedHours protectedHours =
          _ndsGroupSvc
              .find(groupId)
              .map(NDSGroup::getMaintenanceWindow)
              .map(NDSGroupMaintenanceWindow::getProtectedHours)
              .orElseThrow();
      assertTrue(protectedHours.isUserDefined());
      assertEquals(23, (int) protectedHours.getStartHourOfDay());
      assertEquals(6, (int) protectedHours.getEndHourOfDay());
    }

    // valid protected hours can be set even if maintenance email has been sent, as long as the
    // maintenance window itself isn't changing
    {
      final Date sentDate = new Date();
      _maintenanceSvc.setInAdvancedNotificationSentForGroup(
          _ndsGroupSvc.find(groupId).orElseThrow(), sentDate);
      final NDSGroupMaintenanceWindow maintenanceWindow =
          new NDSGroupMaintenanceWindow(
              3,
              8,
              false,
              MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(8, 15));
      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      final NDSGroupMaintenanceWindow window =
          _ndsGroupSvc.find(groupId).map(NDSGroup::getMaintenanceWindow).orElseThrow();
      assertEquals(sentDate, window.getAdvanceNotificationSendDate().orElseThrow());

      final MaintenanceProtectedHours protectedHours = window.getProtectedHours();
      assertTrue(protectedHours.isUserDefined());
      assertEquals(8, (int) protectedHours.getStartHourOfDay());
      assertEquals(15, (int) protectedHours.getEndHourOfDay());
    }
  }

  private ServerlessLoadBalancingDeployment createServerlessLoadBalancingDeployment(
      final ObjectId pGroupId) {
    final List<EnvoyInstance> envoyInstances =
        List.of(
            EnvoyInstance.getEmptyEnvoyInstance(
                CloudProvider.AWS, ServerlessNDSDefaults.ENVOY_LOG_LEVEL, 0),
            EnvoyInstance.getEmptyEnvoyInstance(
                CloudProvider.AWS, ServerlessNDSDefaults.ENVOY_LOG_LEVEL, 1),
            EnvoyInstance.getEmptyEnvoyInstance(
                CloudProvider.AWS, ServerlessNDSDefaults.ENVOY_LOG_LEVEL, 2));

    return new ServerlessLoadBalancingDeployment(
        pGroupId,
        CloudProvider.AWS,
        AWSRegionName.US_WEST_1,
        AWSInstanceFamily.T4G,
        AWSNDSInstanceSize.M20,
        envoyInstances);
  }

  @Test
  public void testShouldRefreshOSPolicyVersionsForLoadBalancingDeployment_SameOSPolicyVersion() {
    final ObjectId groupId = _group.getId();
    final ServerlessLoadBalancingDeployment testDeployment =
        createServerlessLoadBalancingDeployment(groupId);
    final ObjectId deploymentId = testDeployment.getId();
    final String originalOSPolicyVersion = "5.0";

    _serverlessLoadBalancingDeploymentDao.save(testDeployment);
    _serverlessLoadBalancingDeploymentDao.setDesiredEnvoyInstanceOSPolicyVersion(
        deploymentId, originalOSPolicyVersion);

    final List<ServerlessLoadBalancingDeployment> deploymentList =
        _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(groupId);
    assertEquals(1, deploymentList.size());
    final ServerlessLoadBalancingDeployment updatedDeployment = deploymentList.get(0);

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, originalOSPolicyVersion)
            .setReleaseMode(ReleaseMode.CRITICAL);
    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);

    assertFalse(
        _maintenanceSvc.shouldRefreshOSPolicyVersionsForLoadBalancingDeployment(
            _ndsGroup, updatedDeployment, phasedVersion));
  }

  @Test
  public void testShouldRefreshOSPolicyVersionsForLoadBalancingDeployment_EmptyOSPolicyVersion() {
    final ObjectId groupId = _group.getId();
    final ServerlessLoadBalancingDeployment testDeployment =
        createServerlessLoadBalancingDeployment(groupId);
    final String originalOSPolicyVersion = "5.0";

    _serverlessLoadBalancingDeploymentDao.save(testDeployment);

    final List<ServerlessLoadBalancingDeployment> deploymentList =
        _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(groupId);
    assertEquals(1, deploymentList.size());
    final ServerlessLoadBalancingDeployment reloadedDeployment = deploymentList.get(0);

    assertNull(reloadedDeployment.getDesiredEnvoyInstanceOSPolicyVersion());

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, originalOSPolicyVersion)
            .setReleaseMode(ReleaseMode.CRITICAL);
    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);

    assertTrue(
        _maintenanceSvc.shouldRefreshOSPolicyVersionsForLoadBalancingDeployment(
            _ndsGroup, reloadedDeployment, phasedVersion));
  }

  @Test
  public void
      testShouldRefreshOSPolicyVersionsForLoadBalancingDeployment_DifferentOSPolicyVersion() {
    final ObjectId groupId = _group.getId();
    final ServerlessLoadBalancingDeployment testDeployment =
        createServerlessLoadBalancingDeployment(groupId);
    final ObjectId deploymentId = testDeployment.getId();
    final String originalOSPolicyVersion = "5.0";
    final String newOSPolicyVersion = "6.0";

    _serverlessLoadBalancingDeploymentDao.save(testDeployment);
    _serverlessLoadBalancingDeploymentDao.setDesiredEnvoyInstanceOSPolicyVersion(
        deploymentId, originalOSPolicyVersion);

    final List<ServerlessLoadBalancingDeployment> deploymentList =
        _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(groupId);
    assertEquals(1, deploymentList.size());
    final ServerlessLoadBalancingDeployment updatedDeployment = deploymentList.get(0);

    // Assert successfully updated desired OS policy version in deployment
    assertEquals(
        originalOSPolicyVersion, updatedDeployment.getDesiredEnvoyInstanceOSPolicyVersion());

    // Ensure we are checking condition to update to a different OS policy version
    assertNotEquals(originalOSPolicyVersion, newOSPolicyVersion);

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, newOSPolicyVersion)
            .setReleaseMode(ReleaseMode.CRITICAL);
    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);

    assertTrue(
        _maintenanceSvc.shouldRefreshOSPolicyVersionsForLoadBalancingDeployment(
            _ndsGroup, updatedDeployment, phasedVersion));
  }

  @Test
  public void
      testShouldRefreshOSPolicyVersionsForLoadBalancingDeployment_PhasedVersionEligibleCohort()
          throws Exception {
    final ObjectId groupId = _group.getId();
    final ServerlessLoadBalancingDeployment testDeployment =
        createServerlessLoadBalancingDeployment(groupId);
    final ObjectId deploymentId = testDeployment.getId();
    final String originalOSPolicyVersion = "5.0";
    final String newOSPolicyVersion = "6.0";

    _serverlessLoadBalancingDeploymentDao.save(testDeployment);
    _serverlessLoadBalancingDeploymentDao.setDesiredEnvoyInstanceOSPolicyVersion(
        deploymentId, originalOSPolicyVersion);

    final List<ServerlessLoadBalancingDeployment> deploymentList =
        _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(groupId);
    assertEquals(1, deploymentList.size());
    final ServerlessLoadBalancingDeployment updatedDeployment = deploymentList.get(0);

    // Ensure we are checking condition to update to a different OS policy version
    assertNotEquals(originalOSPolicyVersion, newOSPolicyVersion);

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, newOSPolicyVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(70, 70, null, null, null, null));
    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(10, 10));

    assertTrue(_ndsGroupDao.find(groupId).isPresent());

    final NDSGroup groupAfterSet = _ndsGroupDao.find(groupId).get();

    assertTrue(
        _maintenanceSvc.shouldRefreshOSPolicyVersionsForLoadBalancingDeployment(
            groupAfterSet, updatedDeployment, phasedVersion));
  }

  @Test
  public void
      testShouldRefreshOSPolicyVersionsForLoadBalancingDeployment_PhasedVersionNotEligibleCohort()
          throws Exception {
    final ObjectId groupId = _group.getId();
    final ServerlessLoadBalancingDeployment testDeployment =
        createServerlessLoadBalancingDeployment(groupId);
    final ObjectId deploymentId = testDeployment.getId();
    final String originalOSPolicyVersion = "5.0";
    final String newOSPolicyVersion = "6.0";

    _serverlessLoadBalancingDeploymentDao.save(testDeployment);
    _serverlessLoadBalancingDeploymentDao.setDesiredEnvoyInstanceOSPolicyVersion(
        deploymentId, originalOSPolicyVersion);

    final List<ServerlessLoadBalancingDeployment> deploymentList =
        _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(groupId);
    assertEquals(1, deploymentList.size());
    final ServerlessLoadBalancingDeployment updatedDeployment = deploymentList.get(0);

    // Ensure we are checking condition to update to a different OS policy version
    assertNotEquals(originalOSPolicyVersion, newOSPolicyVersion);

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, newOSPolicyVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(20, 20, null, null, null, null));
    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(50, 50));

    assertTrue(_ndsGroupDao.find(groupId).isPresent());

    final NDSGroup groupAfterSet = _ndsGroupDao.find(groupId).get();

    assertFalse(
        _maintenanceSvc.shouldRefreshOSPolicyVersionsForLoadBalancingDeployment(
            groupAfterSet, updatedDeployment, phasedVersion));
  }

  @Test
  public void testShouldRefreshOSPolicyVersionsForLoadBalancingDeployment() {
    final ObjectId groupId = _group.getId();

    final ServerlessLoadBalancingDeployment testDeployment_1 =
        createServerlessLoadBalancingDeployment(groupId);
    final ObjectId deploymentId_1 = testDeployment_1.getId();

    final ServerlessLoadBalancingDeployment testDeployment_2 =
        createServerlessLoadBalancingDeployment(groupId);
    final ObjectId deploymentId_2 = testDeployment_2.getId();

    final String originalOSPolicyVersion = "5.0";

    _serverlessLoadBalancingDeploymentDao.save(testDeployment_1);
    _serverlessLoadBalancingDeploymentDao.save(testDeployment_2);

    _serverlessLoadBalancingDeploymentDao.setDesiredEnvoyInstanceOSPolicyVersion(
        deploymentId_1, originalOSPolicyVersion);
    _serverlessLoadBalancingDeploymentDao.setDesiredEnvoyInstanceOSPolicyVersion(
        deploymentId_2, originalOSPolicyVersion);

    final List<ServerlessLoadBalancingDeployment> deploymentList =
        _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(groupId);
    assertEquals(2, deploymentList.size());

    deploymentList.forEach(
        deployment ->
            assertEquals(
                originalOSPolicyVersion, deployment.getDesiredEnvoyInstanceOSPolicyVersion()));

    final String expectedOSPolicyVersion = _phasedVersionSvc.getDefaultOSPolicy();

    // Ensure we are updating to a different OS policy version
    assertNotEquals(originalOSPolicyVersion, expectedOSPolicyVersion);

    _maintenanceSvc.refreshOSPolicyVersionsForLoadBalancingDeployment(
        _ndsGroup, _group, LOG, _serverlessLoadBalancingDeploymentDao);

    final List<ServerlessLoadBalancingDeployment> updatedDeploymentList =
        _serverlessLoadBalancingDeploymentDao.findDeploymentsByGroupId(groupId);
    updatedDeploymentList.forEach(
        deployment ->
            assertEquals(
                expectedOSPolicyVersion, deployment.getDesiredEnvoyInstanceOSPolicyVersion()));
  }

  @Test
  public void testGetRefreshedOSPolicyVersions_NonDedicatedCluster() {
    final ObjectId groupId = _group.getId();
    final String testClusterName = "test-cluster";
    final ClusterDescription testClusterDesc =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(testClusterName, groupId));
    _clusterDescriptionDao.save(testClusterDesc);
    final String testOSPolicyVersion = "5.0";

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription updatedClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0"));

    assertThat(
            _maintenanceSvc.getRefreshedOsPolicyVersion(
                _ndsGroup, _group, testPhasedVersion, updatedClusterDesc))
        .isEmpty();
  }

  @Test
  public void testGetRefreshedOSPolicyVersions_SimilarOSPolicyVersion() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = "5.0";

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription updatedClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, "5.0"));

    assertThat(
            _maintenanceSvc.getRefreshedOsPolicyVersion(
                _ndsGroup, _group, phasedVersion, updatedClusterDesc))
        .isEmpty();
  }

  @Test
  public void testGetRefreshedOSPolicyVersions_EmptyOSPolicyVersion() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = null;

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription updatedClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0"));

    assertThat(
            _maintenanceSvc.getRefreshedOsPolicyVersion(
                _ndsGroup, _group, testPhasedVersion, updatedClusterDesc))
        .hasValue(testPhasedVersion.getEffectiveVersion());
  }

  @Test
  public void testGetRefreshedOSPolicyVersions_DifferentOSPolicyVersion() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = "5.0";

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription updatedClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0"));

    assertThat(
            _maintenanceSvc.getRefreshedOsPolicyVersion(
                _ndsGroup, _group, testPhasedVersion, updatedClusterDesc))
        .hasValue(testPhasedVersion.getEffectiveVersion());
  }

  @Test
  public void testGetRefreshedOSPolicyVersions_PhasedVersionEligibleCohort() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = "5.0";

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription updatedClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(70, 70, null, null, null, null));
    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);

    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(10, 10));

    setMaintenanceWindowToNow(_ndsGroup);

    assertTrue(_ndsGroupDao.find(groupId).isPresent());

    final NDSGroup groupAfterSet = _ndsGroupDao.find(groupId).get();

    assertThat(
            _maintenanceSvc.getRefreshedOsPolicyVersion(
                groupAfterSet, _group, testPhasedVersion, updatedClusterDesc))
        .hasValue(testPhasedVersion.getEffectiveVersion());
  }

  @Test
  public void testGetRefreshedOSPolicyVersions_PhasedVersionNotEligibleCohort() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = "5.0";

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription updatedClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(30, 30, null, null, null, null));
    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);

    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(50, 50));
    setMaintenanceWindowToNow(_ndsGroup);
    assertTrue(_ndsGroupDao.find(groupId).isPresent());

    final NDSGroup groupAfterSet = _ndsGroupDao.find(groupId).get();

    assertThat(
            _maintenanceSvc.getRefreshedOsPolicyVersion(
                groupAfterSet, _group, testPhasedVersion, updatedClusterDesc))
        .isEmpty();
  }

  @Test
  public void testGetRefreshedOSPolicyVersions_AutomaticReleaseBypassCohort() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = "5.0";

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription updatedClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0")
            .setReleaseMode(ReleaseMode.AUTOMATIC)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(10, 10, null, null, null, null));
    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);

    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(50, 50));
    setMaintenanceWindowToNow(_ndsGroup);
    assertTrue(_ndsGroupDao.find(groupId).isPresent());

    final NDSGroup groupAfterSet = _ndsGroupDao.find(groupId).get();

    assertThat(
            _maintenanceSvc.getRefreshedOsPolicyVersion(
                groupAfterSet, _group, testPhasedVersion, updatedClusterDesc))
        .hasValue(testPhasedVersion.getEffectiveVersion());
  }

  @Test
  public void testRefreshOSPolicyVersions_InWindowAndCriticalRelease() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = "5.0";
    final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable = new HashMap<>();

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription originalClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0"));

    _phasedVersionSvc.save(testPhasedVersion);

    assertEquals(testOSPolicyVersion, originalClusterDesc.getOSPolicyVersion().orElse(""));

    final String expectedOSPolicyVersion = _phasedVersionSvc.getDefaultOSPolicy();

    // Ensure we are updating OS policy version to a different version
    assertNotEquals(testOSPolicyVersion, expectedOSPolicyVersion);

    setMaintenanceWindowToNow(_ndsGroup);

    final Cluster testCluster = _clusterSvc.getActiveCluster(groupId, originalClusterDesc);

    _maintenanceSvc.refreshOSPolicyVersions(
        _ndsGroup, _group, testCluster, numMonitoringAddedMapMutable, LOG);

    final ClusterDescription refreshedClusterDescription =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);
    assertEquals(
        expectedOSPolicyVersion, refreshedClusterDescription.getOSPolicyVersion().orElse(""));
  }

  @Test
  public void testRefreshOSPolicyVersions_OutOfWindowAndNotCriticalRelease() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = "5.0";
    final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable = new HashMap<>();

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription originalClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    assertEquals(testOSPolicyVersion, originalClusterDesc.getOSPolicyVersion().orElse(""));

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(70, 70, null, null, null, null));
    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(10, 10));

    _phasedVersionSvc.save(testPhasedVersion);

    final String expectedOSPolicyVersion = _phasedVersionSvc.getDefaultOSPolicy();

    // Ensure we are updating OS policy version to a different version
    assertNotEquals(testOSPolicyVersion, expectedOSPolicyVersion);

    setMaintenanceWindowToAnotherDay(_ndsGroup);

    final Cluster testCluster = _clusterSvc.getActiveCluster(groupId, originalClusterDesc);

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshOSPolicyVersions(
            _ndsGroup, _group, testCluster, numMonitoringAddedMapMutable, LOG);

    // Ensure we set needMaintenance to true in maintenanceCheckState
    assertTrue(maintenanceCheckResult.needMaintenance());

    final ClusterDescription refreshedClusterDescription =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    // Ensure OS policy version is not updated in cluster description if we are not in
    // maintenance window and release is not critical
    assertEquals(testOSPolicyVersion, refreshedClusterDescription.getOSPolicyVersion().orElse(""));
  }

  @Test
  public void testRefreshOSPolicyVersions_OutOfWindowAndCriticalRelease() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = "5.0";
    final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable = new HashMap<>();

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription originalClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    assertEquals(testOSPolicyVersion, originalClusterDesc.getOSPolicyVersion().orElse(""));

    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0"));

    _phasedVersionSvc.save(testPhasedVersion);

    final String expectedOSPolicyVersion = _phasedVersionSvc.getDefaultOSPolicy();

    // Ensure we are updating OS policy version to a different version
    assertNotEquals(testOSPolicyVersion, expectedOSPolicyVersion);

    setMaintenanceWindowToAnotherDay(_ndsGroup);

    final Cluster testCluster = _clusterSvc.getActiveCluster(groupId, originalClusterDesc);

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshOSPolicyVersions(
            _ndsGroup, _group, testCluster, numMonitoringAddedMapMutable, LOG);

    // Ensure we set needMaintenance to true in maintenanceCheckState
    assertTrue(maintenanceCheckResult.needMaintenance());

    final ClusterDescription refreshedClusterDescription =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    // Ensure OS policy version is updated in cluster description
    assertEquals(
        expectedOSPolicyVersion, refreshedClusterDescription.getOSPolicyVersion().orElse(""));
  }

  @Test
  public void testRefreshOSPolicyVersions_InWindowAndNotCriticalRelease() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription testClusterDesc = createProvisionedCluster();
    _clusterDescriptionDao.save(testClusterDesc);
    final String testClusterName = testClusterDesc.getName();
    final String testOSPolicyVersion = "5.0";
    final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable = new HashMap<>();

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, testClusterName, testOSPolicyVersion, true);

    final ObjectId uniqueId = testClusterDesc.getUniqueId();
    final ClusterDescription originalClusterDesc =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    assertEquals(testOSPolicyVersion, originalClusterDesc.getOSPolicyVersion().orElse(""));

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.OS_POLICY, "6.0")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(70, 70, null, null, null, null));
    final PhasedVersion testPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(10, 10));

    _phasedVersionSvc.save(testPhasedVersion);

    final String expectedOSPolicyVersion = _phasedVersionSvc.getDefaultOSPolicy();

    // Ensure we are updating OS policy version to a different version
    assertNotEquals(testOSPolicyVersion, expectedOSPolicyVersion);

    setMaintenanceWindowToNow(_ndsGroup);

    final Cluster testCluster = _clusterSvc.getActiveCluster(groupId, originalClusterDesc);

    final MaintenanceCheckResult maintenanceCheckResult =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshOSPolicyVersions(
                    _ndsGroup, _group, testCluster, numMonitoringAddedMapMutable, LOG));

    // Ensure we set needMaintenance to true in maintenanceCheckState
    assertTrue(maintenanceCheckResult.needMaintenance());

    final ClusterDescription refreshedClusterDescription =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    // Ensure OS policy version is updated in cluster description
    assertEquals(
        expectedOSPolicyVersion, refreshedClusterDescription.getOSPolicyVersion().orElse(""));

    final List<NdsMaintenanceHistory> histories =
        _ndsMaintenanceHistorySvc.findAllNotCompletedByGroupIdAndType(
            _ndsGroup.getGroupId(), MaintenanceType.OS_POLICY_VERSION_UPDATED);

    assertEquals(1, histories.size());
    assertEquals(testOSPolicyVersion, histories.get(0).getFromVersion());
    assertEquals(expectedOSPolicyVersion, histories.get(0).getToVersion());
  }

  @Test
  public void testRefreshOSPolicyVersions_when_previous_version_outOfCohort() throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription clusterDescription = createProvisionedCluster();
    _clusterDescriptionDao.save(clusterDescription);
    final String clusterName = clusterDescription.getName();
    final String currentOSPolicyVersion = "50";
    final String previousOSPolicyVersion = "60";
    final String targetOsPolicyVersion = "70";
    final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable = new HashMap<>();

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, clusterName, currentOSPolicyVersion, true);
    setMaintenanceWindowToNow(_ndsGroup);

    final ObjectId uniqueId = clusterDescription.getUniqueId();
    final ClusterDescription originalCluster =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, previousOSPolicyVersion)
                .setIsPreviousVersionSafe(true)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.OS_POLICY, targetOsPolicyVersion);

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshOSPolicyVersions(
            _ndsGroup,
            _group,
            _clusterSvc.getActiveCluster(groupId, originalCluster),
            numMonitoringAddedMapMutable,
            LOG);

    assertThat(maintenanceCheckResult.needMaintenance()).isTrue();
    assertThat(maintenanceCheckResult.getFromVersion()).isEqualTo(currentOSPolicyVersion);
    assertThat(maintenanceCheckResult.getToVersion()).isEqualTo(previousOSPolicyVersion);

    final PhasedVersion phasedVersion =
        _phasedVersionSvc.findBySoftwareType(SoftwareType.OS_POLICY).orElseThrow();

    assertThat(phasedVersion.getPreviousVersion()).hasValue(previousOSPolicyVersion);
    assertThat(phasedVersion.getTargetVersion()).isEqualTo(targetOsPolicyVersion);
  }

  @Test
  public void testRefreshOSPolicyVersions_when_previous_version_isSafe_false_outOfCohort()
      throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription clusterDescription = createProvisionedCluster();
    _clusterDescriptionDao.save(clusterDescription);
    final String clusterName = clusterDescription.getName();
    final String currentOSPolicyVersion = "50";
    final String previousOSPolicyVersion = "60";
    final String targetOsPolicyVersion = "70";
    final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable = new HashMap<>();

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, clusterName, currentOSPolicyVersion, true);
    setMaintenanceWindowToNow(_ndsGroup);

    final ObjectId uniqueId = clusterDescription.getUniqueId();
    final ClusterDescription originalCluster =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, previousOSPolicyVersion)
                .setIsPreviousVersionSafe(false)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.OS_POLICY, targetOsPolicyVersion);

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshOSPolicyVersions(
            _ndsGroup,
            _group,
            _clusterSvc.getActiveCluster(groupId, originalCluster),
            numMonitoringAddedMapMutable,
            LOG);

    assertThat(maintenanceCheckResult.needMaintenance()).isFalse();
    assertThat(maintenanceCheckResult.isCriticalRelease()).isFalse();
    assertThat(maintenanceCheckResult.getFromVersion()).isNull();
    assertThat(maintenanceCheckResult.getToVersion()).isNull();
  }

  @Test
  public void testRefreshOSPolicyVersions_previous_version_isLessThanCurrent_and_outOfCohort_()
      throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription clusterDescription = createProvisionedCluster();
    _clusterDescriptionDao.save(clusterDescription);
    final String clusterName = clusterDescription.getName();
    final String currentOSPolicyVersion = "50";
    final String previousOSPolicyVersion = "40";
    final String targetOsPolicyVersion = "70";
    final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable = new HashMap<>();

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, clusterName, currentOSPolicyVersion, true);
    setMaintenanceWindowToNow(_ndsGroup);

    final ObjectId uniqueId = clusterDescription.getUniqueId();
    final ClusterDescription originalCluster =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, previousOSPolicyVersion)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.OS_POLICY, targetOsPolicyVersion);

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshOSPolicyVersions(
            _ndsGroup,
            _group,
            _clusterSvc.getActiveCluster(groupId, originalCluster),
            numMonitoringAddedMapMutable,
            LOG);

    assertThat(maintenanceCheckResult.needMaintenance()).isFalse();
    assertThat(maintenanceCheckResult.getFromVersion()).isNull();
    assertThat(maintenanceCheckResult.getToVersion()).isNull();

    final PhasedVersion phasedVersion =
        _phasedVersionSvc.findBySoftwareType(SoftwareType.OS_POLICY).orElseThrow();

    assertThat(phasedVersion.getPreviousVersion()).hasValue(previousOSPolicyVersion);
    assertThat(phasedVersion.getTargetVersion()).isEqualTo(targetOsPolicyVersion);
  }

  @Test
  public void testRefreshOSPolicyVersions_previous_version_isEqualToCurrent_and_outOfCohort()
      throws Exception {
    final ObjectId groupId = _group.getId();
    final ClusterDescription clusterDescription = createProvisionedCluster();
    _clusterDescriptionDao.save(clusterDescription);
    final String clusterName = clusterDescription.getName();
    final String currentOSPolicyVersion = "50";
    final String previousOSPolicyVersion = "50";
    final String targetOsPolicyVersion = "70";
    final Map<ObjectId, AtomicInteger> numMonitoringAddedMapMutable = new HashMap<>();

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        groupId, clusterName, currentOSPolicyVersion, true);
    setMaintenanceWindowToNow(_ndsGroup);

    final ObjectId uniqueId = clusterDescription.getUniqueId();
    final ClusterDescription originalCluster =
        findClusterDescriptionByGroupIdAndUniqueId(groupId, uniqueId);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.OS_POLICY, previousOSPolicyVersion)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.OS_POLICY, targetOsPolicyVersion);

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshOSPolicyVersions(
            _ndsGroup,
            _group,
            _clusterSvc.getActiveCluster(groupId, originalCluster),
            numMonitoringAddedMapMutable,
            LOG);

    assertThat(maintenanceCheckResult.needMaintenance()).isFalse();
    assertThat(maintenanceCheckResult.getFromVersion()).isNull();
    assertThat(maintenanceCheckResult.getToVersion()).isNull();

    final PhasedVersion phasedVersion =
        _phasedVersionSvc.findBySoftwareType(SoftwareType.OS_POLICY).orElseThrow();

    assertThat(phasedVersion.getPreviousVersion()).hasValue(previousOSPolicyVersion);
    assertThat(phasedVersion.getTargetVersion()).isEqualTo(targetOsPolicyVersion);
  }

  @Test
  public void
      testSetUserDefinedMaintenanceWindow_when_updated_to_same_window_deferral_metadata_isNotCleared()
          throws Exception {
    final ObjectId groupId = _group.getId();
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    final Date deferralRequestDate = new Date();

    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId, new NDSGroupMaintenanceWindow(1, 1), auditInfo);
    _ndsGroupMaintenanceDao.deferMaintenanceOneWeekForGroup(groupId, 1, deferralRequestDate, null);

    final NDSGroupMaintenanceWindow maintenanceWindow =
        _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

    assertEquals(1, maintenanceWindow.getDayOfWeek());
    assertEquals(1, maintenanceWindow.getHourOfDay());
    assertEquals(1, maintenanceWindow.getNumberOfDeferrals());
    assertEquals(deferralRequestDate, maintenanceWindow.getDeferralRequestDate().orElse(null));

    // Act
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId, new NDSGroupMaintenanceWindow(1, 1), auditInfo);

    final NDSGroupMaintenanceWindow updatedMaintenanceWindow =
        _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

    assertEquals(1, updatedMaintenanceWindow.getDayOfWeek());
    assertEquals(1, updatedMaintenanceWindow.getHourOfDay());

    // NumberOfDeferrals and RequestDate are not cleared
    assertEquals(1, updatedMaintenanceWindow.getNumberOfDeferrals());
    assertEquals(
        deferralRequestDate, updatedMaintenanceWindow.getDeferralRequestDate().orElse(null));
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_updated() throws Exception {
    final ObjectId groupId = _group.getId();
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    final Date deferralRequestDate = new Date();

    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId, new NDSGroupMaintenanceWindow(1, 1), auditInfo);
    _ndsGroupMaintenanceDao.deferMaintenanceOneWeekForGroup(groupId, 1, deferralRequestDate, null);

    final NDSGroupMaintenanceWindow maintenanceWindow =
        _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

    assertEquals(1, maintenanceWindow.getDayOfWeek());
    assertEquals(1, maintenanceWindow.getHourOfDay());
    assertEquals(1, maintenanceWindow.getNumberOfDeferrals());
    assertEquals(deferralRequestDate, maintenanceWindow.getDeferralRequestDate().orElse(null));

    // Act
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId, new NDSGroupMaintenanceWindow(2, 2), auditInfo);

    final NDSGroupMaintenanceWindow updatedMaintenanceWindow =
        _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

    assertEquals(2, updatedMaintenanceWindow.getDayOfWeek());
    assertEquals(2, updatedMaintenanceWindow.getHourOfDay());

    // NumberOfDeferrals is cleared
    assertEquals(0, updatedMaintenanceWindow.getNumberOfDeferrals());
    assertTrue(updatedMaintenanceWindow.getDeferralRequestDate().isEmpty());

    final List<Event> auditEvents =
        _auditSvc.findByEventTypeForGroup(groupId, NDSAudit.Type.MAINTENANCE_WINDOW_ADDED);
    assertEquals(1, auditEvents.size());
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_update_clears_deferral_request()
      throws Exception {
    // Arrange
    final ObjectId groupId = _group.getId();
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(groupId);
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    setMaintenanceWindowToNow(ndsGroup);

    final Date nextMaintenanceDate =
        _maintenanceSvc
            .getNextMaintenanceStartDateTime(
                _groupSvc.find(groupId).orElseThrow(),
                _group,
                SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
            .getTime();

    _maintenanceSvc.deferMaintenanceOneWeek(
        groupId, auditInfo, DateUtils.addHours(nextMaintenanceDate, -7));

    final NDSGroupMaintenanceWindow window = _groupSvc.ensureGroup(groupId).getMaintenanceWindow();
    assertTrue(window.getDeferralRequestDate().isPresent());
    assertEquals(1, window.getNumberOfDeferrals().intValue());

    // Act
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId,
        new NDSGroupMaintenanceWindow(
            NDSGroupMaintenanceWindow.getNextDayOfWeek(window.getDayOfWeek()),
            window.getHourOfDay()),
        auditInfo);

    // Assert
    final NDSGroupMaintenanceWindow updatedWindow =
        _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

    assertEquals(
        NDSGroupMaintenanceWindow.getNextDayOfWeek(window.getDayOfWeek()),
        updatedWindow.getDayOfWeek());
    assertEquals(window.getHourOfDay(), updatedWindow.getHourOfDay());
    assertFalse(updatedWindow.getDeferralRequestDate().isPresent());
    assertEquals(0, updatedWindow.getNumberOfDeferrals().intValue());
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_update_when_maintenance_scheduled_throws()
      throws Exception {
    final ObjectId groupId = _group.getId();
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(groupId);
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    setMaintenanceWindowToNow(ndsGroup);

    // Update should not work if maintenance is already scheduled
    final var svcException =
        assertThrows(
            SvcException.class,
            () ->
                _maintenanceSvc.setUserDefinedMaintenanceWindow(
                    groupId, new NDSGroupMaintenanceWindow(1, 1), auditInfo));

    assertEquals(NDSErrorCode.MAINTENANCE_ALREADY_SCHEDULED, svcException.getErrorCode());
  }

  @Test
  public void
      testSetUserDefinedMaintenanceWindow_update_when_initialScheduledDate_is_less_than_max()
          throws Exception {
    final ObjectId groupId = _group.getId();
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(groupId);
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    setMaintenanceWindowToNow(ndsGroup);
    clearAdvanceNotificationDate();

    final NDSGroupMaintenanceWindow window = _groupSvc.ensureGroup(groupId).getMaintenanceWindow();
    final NDSGroupMaintenanceWindow newWindow =
        new NDSGroupMaintenanceWindow(
            NDSGroupMaintenanceWindow.getNextDayOfWeek(window.getDayOfWeek()),
            window.getHourOfDay());

    final Date nextMaintenanceDate =
        _ndsMaintenanceDateCalculationSvc
            .getNextUserDefinedMaintenanceStartDateTime(newWindow, _group.getDefaultTimeZoneId())
            .orElseThrow()
            .getTime();

    // Fake that the initial scheduled maintenance was 13 days ago
    _ndsGroupMaintenanceDao.setInitialScheduledMaintenanceDate(
        groupId, DateUtils.addDays(nextMaintenanceDate, -13));

    // Act
    // Update to a day below the 21 days window is allowed
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId,
        new NDSGroupMaintenanceWindow(newWindow.getDayOfWeek(), window.getHourOfDay()),
        auditInfo);

    final NDSGroupMaintenanceWindow updatedWindow =
        _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

    assertEquals(newWindow.getDayOfWeek(), updatedWindow.getDayOfWeek());
    assertEquals(window.getHourOfDay(), updatedWindow.getHourOfDay());
  }

  @Test
  public void
      testSetUserDefinedMaintenanceWindow_update_when_initialScheduledDate_is_outside_allowed_window_throws()
          throws Exception {
    final ObjectId groupId = _group.getId();
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(groupId);
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    setMaintenanceWindowToNow(ndsGroup);
    clearAdvanceNotificationDate();

    final NDSGroupMaintenanceWindow window = _groupSvc.ensureGroup(groupId).getMaintenanceWindow();
    final NDSGroupMaintenanceWindow newWindow =
        new NDSGroupMaintenanceWindow(
            NDSGroupMaintenanceWindow.getNextDayOfWeek(window.getDayOfWeek()),
            window.getHourOfDay());

    final Date nextMaintenanceDate =
        _ndsMaintenanceDateCalculationSvc
            .getNextUserDefinedMaintenanceStartDateTime(newWindow, _group.getDefaultTimeZoneId())
            .orElseThrow()
            .getTime();

    // Fake that the initial scheduled maintenance was 22 days ago
    _ndsGroupMaintenanceDao.setInitialScheduledMaintenanceDate(
        groupId, DateUtils.addDays(nextMaintenanceDate, -22));

    // Act
    // Update should not work because it falls outside our max compliance window
    final var svcException =
        assertThrows(
            SvcException.class,
            () -> _maintenanceSvc.setUserDefinedMaintenanceWindow(groupId, newWindow, auditInfo));

    assertEquals(NDSErrorCode.NON_COMPLIANT_MAINTENANCE_WINDOW, svcException.getErrorCode());
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_when_window_is_deferred() throws Exception {
    // Arrange
    final ObjectId groupId = _group.getId();
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(groupId);
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    setMaintenanceWindowToNow(ndsGroup);

    final Date nextMaintenanceDate =
        _maintenanceSvc
            .getNextMaintenanceStartDateTime(
                _groupSvc.find(groupId).orElseThrow(),
                _group,
                SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
            .getTime();

    _maintenanceSvc.deferMaintenanceOneWeek(
        groupId, auditInfo, DateUtils.addHours(nextMaintenanceDate, -7));

    final NDSGroupMaintenanceWindow deferredWindow =
        _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

    // Act
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId,
        new NDSGroupMaintenanceWindow(
            NDSGroupMaintenanceWindow.getNextDayOfWeek(deferredWindow.getDayOfWeek()),
            deferredWindow.getHourOfDay()),
        auditInfo);

    final NDSGroupMaintenanceWindow updatedWindow =
        _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

    assertEquals(
        NDSGroupMaintenanceWindow.getNextDayOfWeek(deferredWindow.getDayOfWeek()),
        updatedWindow.getDayOfWeek());
    assertEquals(deferredWindow.getHourOfDay(), updatedWindow.getHourOfDay());
    assertEquals(0, updatedWindow.getNumberOfDeferrals());
    assertFalse(updatedWindow.getDeferralRequestDate().isPresent());
  }

  @Test
  public void testAutoDeferMaintenanceOneWeek() throws Exception {
    // Arrange
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(_group.getId());
    createMaintenanceHistory(ndsGroup.getGroupId());

    final Pair<Integer, Integer> weekHour = getCurrentDayOfWeekHourOfDay(0);
    setMaintenanceWindow(
        ndsGroup,
        NDSGroupMaintenanceWindow.builder()
            .autoDeferEnabled(true)
            .dayOfWeek(weekHour.getLeft())
            .hourOfDay(weekHour.getRight())
            .protectedHours(new MaintenanceProtectedHours(null, null, null, false))
            .build());

    final NDSGroup updatedNDSGroup = _groupSvc.ensureGroup(_group.getId());

    final Date nextMaintenanceDate =
        _maintenanceSvc
            .getNextMaintenanceStartDateTime(
                updatedNDSGroup, _group, SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
            .getTime();

    final Date expectedDeferredMaintenanceDate =
        _maintenanceSvc.calculateTargetMaintenanceDateForDeferral(
            updatedNDSGroup, _maintenanceSvc.getNewDate());

    // Act
    _maintenanceSvc.autoDeferMaintenanceOneWeek(updatedNDSGroup, _group, LOG);

    // Assert
    final NDSGroupMaintenanceWindow updatedWindow =
        _groupSvc.ensureGroup(_group.getId()).getMaintenanceWindow();

    assertThat(updatedWindow.getNumberOfDeferrals()).isEqualTo(1);
    assertThat(updatedWindow.getDeferralRequestDate()).hasValue(_currentTime);

    final List<NdsMaintenanceHistory> maintenanceHistory =
        _ndsMaintenanceHistorySvc.findAllMaintenancesByGroupIdClusterNameAndState(
            _ndsGroup.getGroupId(), "cluster", NdsMaintenanceHistory.State.PENDING);

    assertThat(maintenanceHistory).hasSize(1);

    final NdsMaintenanceHistory history = maintenanceHistory.get(0);
    assertThat(history.getTargetMaintenanceDate()).isEqualTo(expectedDeferredMaintenanceDate);
    assertThat(
            Duration.between(
                    nextMaintenanceDate.toInstant(), history.getTargetMaintenanceDate().toInstant())
                .toDays())
        .isEqualTo(7);

    final List<Event> events =
        _auditSvc.findByEventTypeForGroup(_group.getId(), MAINTENANCE_AUTO_DEFERRED);

    assertThat(events).hasSize(1);
    assertThat(events)
        .allSatisfy(event -> assertThat(event).isInstanceOf(NDSMaintenanceWindowAudit.class));

    final NDSMaintenanceWindowAudit event = (NDSMaintenanceWindowAudit) events.get(0);
    assertThat(event.getMaintenanceDateTime())
        .isEqualTo(
            _maintenanceSvc.getDateFormatter(_group).format(expectedDeferredMaintenanceDate));
  }

  @Test
  public void testAutoDeferMaintenanceOneWeek_when_autoDeferEnabled_false() throws Exception {
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(_group.getId());
    final Pair<Integer, Integer> weekHour = getCurrentDayOfWeekHourOfDay(0);
    setMaintenanceWindow(
        ndsGroup,
        NDSGroupMaintenanceWindow.builder()
            .isUserDefined(true)
            .protectedHours(new MaintenanceProtectedHours(null, null, null, false))
            .dayOfWeek(weekHour.getLeft())
            .hourOfDay(weekHour.getRight())
            .autoDeferEnabled(false)
            .build());

    _maintenanceSvc.autoDeferMaintenanceOneWeek(_groupSvc.ensureGroup(_group.getId()), _group, LOG);

    final NDSGroupMaintenanceWindow updatedWindow =
        _groupSvc.ensureGroup(_group.getId()).getMaintenanceWindow();

    assertThat(updatedWindow.getNumberOfDeferrals()).isEqualTo(0);
    assertThat(updatedWindow.getDeferralRequestDate()).isEmpty();
  }

  @Test
  public void testAutoDeferMaintenanceOneWeek_when_numDeferral_greaterThan_zero() throws Exception {
    // Arrange
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(_group.getId());
    final Pair<Integer, Integer> weekHour = getCurrentDayOfWeekHourOfDay(0);
    setMaintenanceWindow(
        ndsGroup,
        NDSGroupMaintenanceWindow.builder()
            .autoDeferEnabled(true)
            .protectedHours(new MaintenanceProtectedHours(null, null, null, false))
            .dayOfWeek(weekHour.getLeft())
            .hourOfDay(weekHour.getRight())
            .build());
    final Date deferralRequestDate = new Date();
    final Date previousAdvancedNotificationDate = new Date();

    // Try to autodefer when numDeferral is 1
    {
      _ndsGroupMaintenanceDao.deferMaintenanceOneWeekForGroup(
          ndsGroup.getGroupId(), 1, deferralRequestDate, previousAdvancedNotificationDate);

      _maintenanceSvc.autoDeferMaintenanceOneWeek(
          _groupSvc.ensureGroup(_group.getId()), _group, LOG);

      final NDSGroupMaintenanceWindow updatedWindow =
          _groupSvc.ensureGroup(_group.getId()).getMaintenanceWindow();

      assertThat(updatedWindow.getNumberOfDeferrals()).isEqualTo(1);
      assertThat(updatedWindow.getDeferralRequestDate()).hasValue(deferralRequestDate);
      assertThat(updatedWindow.getDeferralRequestDate()).hasValue(previousAdvancedNotificationDate);
    }

    // Try to autodefer when numDeferral is 2
    {
      _ndsGroupMaintenanceDao.deferMaintenanceOneWeekForGroup(
          ndsGroup.getGroupId(), 2, deferralRequestDate, previousAdvancedNotificationDate);

      _maintenanceSvc.autoDeferMaintenanceOneWeek(
          _groupSvc.ensureGroup(_group.getId()), _group, LOG);

      final NDSGroupMaintenanceWindow updatedWindow =
          _groupSvc.ensureGroup(_group.getId()).getMaintenanceWindow();

      assertThat(updatedWindow.getNumberOfDeferrals()).isEqualTo(2);
      assertThat(updatedWindow.getDeferralRequestDate()).hasValue(deferralRequestDate);
      assertThat(updatedWindow.getDeferralRequestDate()).hasValue(previousAdvancedNotificationDate);
    }
  }

  @Test
  public void testDeferMaintenanceOneWeek() throws Exception {
    final ObjectId groupId = _group.getId();
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(groupId);
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    setMaintenanceWindowToNow(ndsGroup);

    final Date nextMaintenanceDate =
        _maintenanceSvc
            .getNextMaintenanceStartDateTime(
                _groupSvc.find(groupId).orElseThrow(),
                _group,
                SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
            .getTime();

    // Defer maintenance 1 hour before nextMaintenance
    {
      _maintenanceSvc.deferMaintenanceOneWeek(
          groupId, auditInfo, DateUtils.addHours(nextMaintenanceDate, -1));

      final NDSGroupMaintenanceWindow window =
          _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

      assertTrue(window.getDeferralRequestDate().isPresent());
      assertEquals(1, window.getNumberOfDeferrals().intValue());
    }

    // Defer maintenance 72 hours before nextMaintenance
    {
      setMaintenanceWindowToNow(ndsGroup);

      _maintenanceSvc.deferMaintenanceOneWeek(
          groupId, auditInfo, DateUtils.addHours(nextMaintenanceDate, -72));

      final NDSGroupMaintenanceWindow window =
          _groupSvc.ensureGroup(groupId).getMaintenanceWindow();

      assertTrue(window.getDeferralRequestDate().isPresent());
      assertEquals(2, window.getNumberOfDeferrals().intValue());
    }
  }

  @Test
  public void
      testDeferMaintenanceOneWeek_when_deferral_date_is_at_or_before_or_after_next_maintenance_throws()
          throws Exception {
    final ObjectId groupId = _group.getId();
    final NDSGroup ndsGroup = _groupSvc.ensureGroup(groupId);
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    setMaintenanceWindowToNow(ndsGroup);

    final Date nextMaintenanceDate =
        _maintenanceSvc
            .getNextMaintenanceStartDateTime(
                _groupSvc.find(groupId).orElseThrow(),
                _group,
                SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
            .getTime();

    // Before Advanced Notification
    final var svcExceptionForBeforeAdvancedNotification =
        assertThrows(
            SvcException.class,
            () ->
                _maintenanceSvc.deferMaintenanceOneWeek(
                    groupId,
                    auditInfo,
                    DateUtils.addHours(
                        nextMaintenanceDate, -(IN_ADVANCED_NOTIFICATION_HOURS + 1))));

    assertEquals(
        NDSErrorCode.MAINTENANCE_DEFERRAL_NOT_ALLOWED,
        svcExceptionForBeforeAdvancedNotification.getErrorCode());

    // At Maintenance
    final var svcExceptionForAtMaintenanceDate =
        assertThrows(
            SvcException.class,
            () -> _maintenanceSvc.deferMaintenanceOneWeek(groupId, auditInfo, nextMaintenanceDate));

    assertEquals(
        NDSErrorCode.MAINTENANCE_DEFERRAL_NOT_ALLOWED,
        svcExceptionForAtMaintenanceDate.getErrorCode());

    // After Maintenance
    final var svcExceptionForAfterMaintenanceDate =
        assertThrows(
            SvcException.class,
            () ->
                _maintenanceSvc.deferMaintenanceOneWeek(
                    groupId, auditInfo, DateUtils.addHours(nextMaintenanceDate, 1)));

    assertEquals(
        NDSErrorCode.MAINTENANCE_DEFERRAL_NOT_ALLOWED,
        svcExceptionForAfterMaintenanceDate.getErrorCode());
  }

  @Test
  public void testResetMaintenanceWindowForGroup() throws Exception {
    final Group group = MmsFactory.createGroup(_organization, "cus_0002");
    final ObjectId groupId = group.getId();
    _groupSvc.ensureGroup(groupId);
    _groupDao.save(group);

    final NDSGroupMaintenanceWindow defaultMaintenanceWindow =
        _groupSvc.find(groupId).get().getMaintenanceWindow();

    // set custom maintenance window
    final int dayOfWeek = 5;
    final int hourOfDay = 12;

    {
      final NDSGroupMaintenanceWindow maintenanceWindow =
          new NDSGroupMaintenanceWindow(
              dayOfWeek,
              hourOfDay,
              false,
              MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(9, 15));
      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      final NDSGroup ndsGroup = _ndsGroupSvc.find(groupId).orElseThrow();
      final NDSGroupMaintenanceWindow updatedMaintenanceWindow = ndsGroup.getMaintenanceWindow();
      assertTrue(updatedMaintenanceWindow.isUserDefined());
      assertEquals(dayOfWeek, (int) updatedMaintenanceWindow.getDayOfWeek());
      assertEquals(hourOfDay, (int) updatedMaintenanceWindow.getHourOfDay());
      final MaintenanceProtectedHours protectedHours = updatedMaintenanceWindow.getProtectedHours();
      assertTrue(protectedHours.isUserDefined());
      assertEquals(9, (int) protectedHours.getStartHourOfDay());
      assertEquals(15, (int) protectedHours.getEndHourOfDay());
    }

    // unset maintenance window and confirm that system defined window is set
    _maintenanceSvc.resetMaintenanceWindowForGroup(
        _groupDao.findById(groupId), AuditInfoHelpers.fromSystem());

    {
      final NDSGroupMaintenanceWindow updatedMaintenanceWindow =
          _groupSvc.find(groupId).get().getMaintenanceWindow();
      assertFalse(updatedMaintenanceWindow.isUserDefined());
      assertEquals(
          defaultMaintenanceWindow.getDayOfWeek(), updatedMaintenanceWindow.getDayOfWeek());
      assertEquals(
          defaultMaintenanceWindow.getHourOfDay(), updatedMaintenanceWindow.getHourOfDay());

      // other fields get set to defaults
      assertFalse(updatedMaintenanceWindow.getAdvanceNotificationSendDate().isPresent());
      assertEquals(0, (int) updatedMaintenanceWindow.getNumberOfDeferrals());
      assertFalse(updatedMaintenanceWindow.getStartASAP());
      assertFalse(updatedMaintenanceWindow.getDeferralGrantJiraTicket().isPresent());
      final MaintenanceProtectedHours protectedHours = updatedMaintenanceWindow.getProtectedHours();
      assertFalse(protectedHours.isUserDefined());
      assertNull(protectedHours.getStartHourOfDay());
      assertNull(protectedHours.getEndHourOfDay());
    }
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_resourcePolicy() throws Exception {
    final Group group = MmsFactory.createGroup(_organization, "cus_0002");
    final ObjectId groupId = group.getId();
    _groupSvc.ensureGroup(groupId);
    _groupDao.save(group);

    // set custom maintenance window
    final int dayOfWeek = 5;
    final int hourOfDay = 12;

    final NDSGroupMaintenanceWindow maintenanceWindow =
        new NDSGroupMaintenanceWindow(
            dayOfWeek,
            hourOfDay,
            false,
            MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(9, 15));

    // Request is allowed
    {
      final Policy policyEnsureWindowExists =
          new Policy(
              """
                forbid (
                  principal,
                  action == ResourcePolicy::Action::"project.maintenanceWindow.modify",
                  resource
                ) when {
                  context.project.hasDefinedMaintenanceWindow == false
                };
              """);
      final ResourcePolicy resourcePolicy =
          AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), group.getOrgId())
              .toBuilder()
              .setPolicies(List.of(policyEnsureWindowExists))
              .build();
      var savePolicy =
          _atlasResourcePolicySvc.saveResourcePolicy(
              resourcePolicy, AtlasResourcePolicyAudit.Type.RESOURCE_POLICY_CREATED, null, null);
      Assertions.assertTrue(
          savePolicy.isOk(),
          String.format(
              "Error saving policy, %s",
              savePolicy
                  .err()
                  .map(ResourcePolicyValidationError::errorType)
                  .map(ResourcePolicyValidationError.ValidationErrorType::name)
                  .orElse("no error")));

      _maintenanceSvc.setUserDefinedMaintenanceWindow(
          groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
      final NDSGroup ndsGroup = _ndsGroupSvc.find(groupId).orElseThrow();
      final NDSGroupMaintenanceWindow updatedMaintenanceWindow = ndsGroup.getMaintenanceWindow();
      assertTrue(updatedMaintenanceWindow.isUserDefined());
      assertEquals(dayOfWeek, (int) updatedMaintenanceWindow.getDayOfWeek());
      assertEquals(hourOfDay, (int) updatedMaintenanceWindow.getHourOfDay());
      final MaintenanceProtectedHours protectedHours = updatedMaintenanceWindow.getProtectedHours();
      assertTrue(protectedHours.isUserDefined());
      assertEquals(9, (int) protectedHours.getStartHourOfDay());
      assertEquals(15, (int) protectedHours.getEndHourOfDay());

      _atlasResourcePolicySvc.deleteResourcePolicy(resourcePolicy, null);
    }

    // Request is denied
    {
      final Policy policyEnsureNoWindowExists =
          new Policy(
              """
                forbid (
                  principal,
                  action == ResourcePolicy::Action::"project.maintenanceWindow.modify",
                  resource
                ) when {
                  context.project.hasDefinedMaintenanceWindow == true
                };
              """);
      final ResourcePolicy resourcePolicy =
          AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), group.getOrgId())
              .toBuilder()
              .setPolicies(List.of(policyEnsureNoWindowExists))
              .build();
      var savePolicy =
          _atlasResourcePolicySvc.saveResourcePolicy(
              resourcePolicy, AtlasResourcePolicyAudit.Type.RESOURCE_POLICY_CREATED, null, null);
      Assertions.assertTrue(
          savePolicy.isOk(),
          String.format(
              "Error saving policy, %s",
              savePolicy
                  .err()
                  .map(ResourcePolicyValidationError::errorType)
                  .map(ResourcePolicyValidationError.ValidationErrorType::name)
                  .orElse("no error")));
      final SvcException exception =
          assertThrows(
              SvcException.class,
              () ->
                  _maintenanceSvc.setUserDefinedMaintenanceWindow(
                      groupId, maintenanceWindow, AuditInfoHelpers.fromSystem()));
      assertEquals(
          NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED, exception.getErrorCode());
    }
  }

  @Test
  public void testResetMaintenanceWindowForGroup_resourcePolicy() throws Exception {
    final Group group = MmsFactory.createGroup(_organization, "cus_0002");
    final ObjectId groupId = group.getId();
    _groupSvc.ensureGroup(groupId);
    _groupDao.save(group);

    // set custom maintenance window
    final int dayOfWeek = 5;
    final int hourOfDay = 12;

    final NDSGroupMaintenanceWindow maintenanceWindow =
        new NDSGroupMaintenanceWindow(
            dayOfWeek,
            hourOfDay,
            false,
            MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(9, 15));
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());
    {
      final NDSGroup ndsGroup = _ndsGroupSvc.find(groupId).orElseThrow();
      final NDSGroupMaintenanceWindow updatedMaintenanceWindow = ndsGroup.getMaintenanceWindow();
      assertTrue(updatedMaintenanceWindow.isUserDefined());
      assertEquals(dayOfWeek, (int) updatedMaintenanceWindow.getDayOfWeek());
      assertEquals(hourOfDay, (int) updatedMaintenanceWindow.getHourOfDay());
      final MaintenanceProtectedHours protectedHours = updatedMaintenanceWindow.getProtectedHours();
      assertTrue(protectedHours.isUserDefined());
      assertEquals(9, (int) protectedHours.getStartHourOfDay());
      assertEquals(15, (int) protectedHours.getEndHourOfDay());
    }

    final Policy policyEnsureWindowExists =
        new Policy(
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"project.maintenanceWindow.modify",
                resource
              ) when {
                context.project.hasDefinedMaintenanceWindow == false
              };
            """);

    final ResourcePolicy resourcePolicy =
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), group.getOrgId()).toBuilder()
            .setPolicies(List.of(policyEnsureWindowExists))
            .build();
    var savePolicy =
        _atlasResourcePolicySvc.saveResourcePolicy(
            resourcePolicy, AtlasResourcePolicyAudit.Type.RESOURCE_POLICY_CREATED, null, null);
    Assertions.assertTrue(
        savePolicy.isOk(),
        String.format(
            "Error saving policy, %s",
            savePolicy
                .err()
                .map(ResourcePolicyValidationError::errorType)
                .map(ResourcePolicyValidationError.ValidationErrorType::name)
                .orElse("no error")));

    final SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                _maintenanceSvc.resetMaintenanceWindowForGroup(
                    _groupDao.findById(groupId), AuditInfoHelpers.fromSystem()));
    assertEquals(
        NDSErrorCode.ATLAS_RESOURCE_POLICIES_VIOLATION_NOT_AUTHORIZED, exception.getErrorCode());
    {
      final NDSGroup ndsGroup = _ndsGroupSvc.find(groupId).orElseThrow();
      final NDSGroupMaintenanceWindow updatedMaintenanceWindow = ndsGroup.getMaintenanceWindow();
      assertTrue(updatedMaintenanceWindow.isUserDefined());
      assertEquals(dayOfWeek, (int) updatedMaintenanceWindow.getDayOfWeek());
      assertEquals(hourOfDay, (int) updatedMaintenanceWindow.getHourOfDay());
      final MaintenanceProtectedHours protectedHours = updatedMaintenanceWindow.getProtectedHours();
      assertTrue(protectedHours.isUserDefined());
      assertEquals(9, (int) protectedHours.getStartHourOfDay());
      assertEquals(15, (int) protectedHours.getEndHourOfDay());
    }
  }

  @Test
  public void testResetMaintenanceAfterCompletionForGroup() {
    final Date advanceNotificationSendDate = new Date();
    final NDSGroupMaintenanceWindow ndsGroupMaintenanceWindow =
        _ndsGroup.getMaintenanceWindow().toBuilder()
            .hourOfDay(10)
            .dayOfWeek(1)
            .deferralRequestDate(new Date())
            .deferralGrantJiraTicket("Ticket")
            .advanceNotificationSendDate(advanceNotificationSendDate)
            .initialScheduledMaintenanceDate(new Date())
            .maintenanceStartedNotificationSentDate(new Date())
            .maintenanceNeededDate(new Date())
            .numberOfDeferrals(2)
            .startASAP(true)
            .build();
    assertThat(ndsGroupMaintenanceWindow)
        .hasNoNullFieldsOrPropertiesExcept(
            "_systemHourOfDayOverride",
            "_previousAdvancedNotificationSendDate",
            "_lastMaintenanceCompletionDate",
            "_lastMaintenanceDate",
            "_lastMaintenanceStartedDate",
            "_lastMaintenanceDurationMinutes");

    _ndsGroupDao.saveReplicaSafe(
        _ndsGroupDao
            .findOne(new BasicDBObject(FieldDefs.ID, _ndsGroup.getGroupId()))
            .append(NDSGroup.FieldDefs.MAINTENANCE_WINDOW, ndsGroupMaintenanceWindow.toDBObject()));

    // Act
    _maintenanceSvc.resetMaintenanceAfterCompletionForGroup(reloadNdsGroup(), LOG);

    final NDSGroupMaintenanceWindow updatedMaintenanceWindow =
        reloadNdsGroup().getMaintenanceWindow();

    // Updated Properties
    assertThat(updatedMaintenanceWindow.getPreviousAdvanceNotificationSendDate())
        .hasValue(advanceNotificationSendDate);
    assertThat(updatedMaintenanceWindow.getLastMaintenanceCompletionDate())
        .hasValue(_maintenanceSvc.getNewDate());
    assertThat(updatedMaintenanceWindow.getLastMaintenanceDate()).isPresent();
    assertThat(updatedMaintenanceWindow.getLastMaintenanceStartedDate())
        .hasValue(ndsGroupMaintenanceWindow.getMaintenanceStartedNotificationSent().orElseThrow());
    assertThat(updatedMaintenanceWindow.getLastMaintenanceDurationMinutes())
        .hasValue(
            Duration.between(
                    ndsGroupMaintenanceWindow
                        .getMaintenanceStartedNotificationSent()
                        .orElseThrow()
                        .toInstant(),
                    _maintenanceSvc.getNewDate().toInstant())
                .toMinutes());

    // Cleared properties
    assertThat(updatedMaintenanceWindow.getAdvanceNotificationSendDate()).isEmpty();
    assertThat(updatedMaintenanceWindow.getMaintenanceStartedNotificationSent()).isEmpty();
    assertThat(updatedMaintenanceWindow.getNumberOfDeferrals()).isEqualTo(0);
    assertThat(updatedMaintenanceWindow.getDeferralRequestDate()).isEmpty();
    assertThat(updatedMaintenanceWindow.getStartASAP()).isFalse();
    assertThat(updatedMaintenanceWindow.getInitialScheduledMaintenanceDate()).isEmpty();
    assertThat(updatedMaintenanceWindow.getMaintenanceNeededDate()).isEmpty();
    assertThat(updatedMaintenanceWindow.getDeferralGrantJiraTicket()).isEmpty();

    // UnChanged properties
    assertThat(updatedMaintenanceWindow.getHourOfDay())
        .isEqualTo(ndsGroupMaintenanceWindow.getHourOfDay());
    assertThat(updatedMaintenanceWindow.getDayOfWeek())
        .isEqualTo(ndsGroupMaintenanceWindow.getDayOfWeek());
    assertThat(updatedMaintenanceWindow.getSystemHourOfDayOverride())
        .isEqualTo(ndsGroupMaintenanceWindow.getSystemHourOfDayOverride());
    assertThat(updatedMaintenanceWindow.getProtectedHours())
        .isEqualTo(ndsGroupMaintenanceWindow.getProtectedHours());
  }

  @Test
  public void testToggleAutoDeferEnabled() throws Exception {
    final ObjectId groupId = _group.getId();
    // cannot enable auto defer for a group with a system defined maintenance window
    try {
      _maintenanceSvc.toggleAutoDeferEnabled(groupId, AuditInfoHelpers.fromSystem());
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.UNSUPPORTED, e.getErrorCode());
    }

    final int dayOfWeek = 5;
    final int hourOfDay = 12;
    final NDSGroupMaintenanceWindow maintenanceWindow0 =
        new NDSGroupMaintenanceWindow(dayOfWeek, hourOfDay);
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId, maintenanceWindow0, AuditInfoHelpers.fromSystem());
    assertFalse(maintenanceWindow0.getAutoDeferEnabled());

    _maintenanceSvc.toggleAutoDeferEnabled(groupId, AuditInfoHelpers.fromSystem());
    final NDSGroupMaintenanceWindow updatedMaintenanceWindow0 =
        _groupSvc.find(groupId).get().getMaintenanceWindow();
    assertTrue(updatedMaintenanceWindow0.getAutoDeferEnabled());

    _maintenanceSvc.toggleAutoDeferEnabled(groupId, AuditInfoHelpers.fromSystem());
    final NDSGroupMaintenanceWindow updatedMaintenanceWindow1 =
        _groupSvc.find(groupId).get().getMaintenanceWindow();
    assertFalse(updatedMaintenanceWindow1.getAutoDeferEnabled());
  }

  private void createMaintenanceHistory(final ObjectId pGroupId) {
    final String clusterName = "cluster";
    final Date initialScheduledMaintenanceDate = _currentTime;

    final List<NdsMaintenanceHistory> list = new ArrayList<>();
    list.add(
        NdsMaintenanceHistory.builder()
            .setGroupId(pGroupId)
            .setTargetMaintenanceDate(initialScheduledMaintenanceDate)
            .setIsCritical(false)
            .setMaintenanceType(MaintenanceType.MONGOT_VERSION_UPDATED)
            .setState(NdsMaintenanceHistory.State.PENDING)
            .setSchedulingBehavior(SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
            .setClusterName(clusterName)
            .setFromVersion("1.2.3")
            .setToVersion("1.2.4")
            .build());

    _ndsMaintenanceHistorySvc.savePendingMaintenances(list);
  }

  private void createMaintenanceHistory(
      final ObjectId pGroupId,
      final String clusterName,
      final MaintenanceType type,
      final String fromVersion,
      final String toVersion) {
    final Date initialScheduledMaintenanceDate = _currentTime;

    final List<NdsMaintenanceHistory> histories =
        List.of(
            NdsMaintenanceHistory.builder()
                .setGroupId(pGroupId)
                .setTargetMaintenanceDate(initialScheduledMaintenanceDate)
                .setIsCritical(false)
                .setMaintenanceType(type)
                .setState(NdsMaintenanceHistory.State.PENDING)
                .setSchedulingBehavior(SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
                .setClusterName(clusterName)
                .setFromVersion(fromVersion)
                .setToVersion(toVersion)
                .build());

    _ndsMaintenanceHistorySvc.savePendingMaintenances(histories);
  }

  @Test
  public void testDeferMaintenanceOneWeekForGroup() throws Exception {
    final ObjectId groupId = _group.getId();
    final NDSGroup group = _groupSvc.ensureGroup(groupId);

    final Date dateOfDeferralRequest = _currentTime;
    createMaintenanceHistory(groupId);

    // cannot defer maintenance for a group with a system defined maintenance window
    try {
      _maintenanceSvc.deferMaintenanceOneWeek(
          groupId, AuditInfoHelpers.fromSystem(), dateOfDeferralRequest);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.UNSUPPORTED, e.getErrorCode());
    }

    // set maintenance window with notification not sent
    final int dayOfWeek = 5;
    final int hourOfDay = 12;
    final NDSGroupMaintenanceWindow maintenanceWindow =
        new NDSGroupMaintenanceWindow(dayOfWeek, hourOfDay);
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());

    // cannot defer maintenance for a group for which the advance notification is not sent
    try {
      _maintenanceSvc.deferMaintenanceOneWeek(
          groupId, AuditInfoHelpers.fromSystem(), dateOfDeferralRequest);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.MAINTENANCE_NOT_SCHEDULED, e.getErrorCode());
    }

    // set maintenance window so we're in notification window
    setMaintenanceWindowInNumDays(group, 3);
    // deferral should succeed
    _maintenanceSvc.deferMaintenanceOneWeek(
        groupId, AuditInfoHelpers.fromSystem(), dateOfDeferralRequest);
    final NDSGroupMaintenanceWindow updatedMaintenanceWindow =
        _groupSvc.find(groupId).get().getMaintenanceWindow();
    assertTrue(updatedMaintenanceWindow.getDeferralRequestDate().isPresent());
    assertEquals(1, (int) updatedMaintenanceWindow.getNumberOfDeferrals());
    assertFalse(updatedMaintenanceWindow.getAdvanceNotificationSendDate().isPresent());
    final List<NdsMaintenanceHistory> maintenanceHistories =
        _ndsMaintenanceHistorySvc.findAllMaintenancesByGroupIdClusterNameAndState(
            groupId, "cluster", NdsMaintenanceHistory.State.PENDING);
    assertEquals(1, maintenanceHistories.size());
    final NdsMaintenanceHistory maintenanceHistory = maintenanceHistories.get(0);
    assertEquals(dateOfDeferralRequest, maintenanceHistory.getUpdatedAt());
    assertEquals(1, maintenanceHistory.getMaintenanceDeferrals().size());

    // artificially defer maintenance a second time and re-set advance notification send date
    final Calendar oneWeekAgo = getCalendarInstance(TimeZone.getTimeZone("UTC"));
    oneWeekAgo.add(Calendar.DAY_OF_WEEK, -7);
    _ndsGroupMaintenanceDao.deferMaintenanceOneWeekForGroup(groupId, 2, oneWeekAgo.getTime(), null);
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(groupId, new Date());

    // deferring maintenance more than twice should fail
    try {
      _maintenanceSvc.deferMaintenanceOneWeek(
          groupId, AuditInfoHelpers.fromSystem(), dateOfDeferralRequest);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.NUM_MAINTENANCE_DEFERRALS_EXCEEDED, e.getErrorCode());
    }
  }

  @Test
  public void testGrantExtraMaintenanceDeferral() throws Exception {
    final ObjectId groupId = _group.getId();
    final NDSGroup group = _groupSvc.ensureGroup(groupId);
    final String jiraTicket = "https://jira.mongodb.org/CLOUDP-00000";

    // cannot grant maintenance deferral for a group with a system defined maintenance window
    {
      final var exception =
          assertThrows(
              SvcException.class,
              () ->
                  _maintenanceSvc.grantExtraMaintenanceDeferral(
                      groupId, jiraTicket, AuditInfoHelpers.fromSystem()));
      assertEquals(NDSErrorCode.UNSUPPORTED, exception.getErrorCode());
      assertTrue(
          exception
              .getMessage()
              .contains("project does not have a user defined maintenance window"));
    }

    final NDSGroupMaintenanceWindow maintenanceWindow = new NDSGroupMaintenanceWindow(5, 12);
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        group.getGroupId(), maintenanceWindow, AuditInfoHelpers.fromSystem());

    // cannot grant maintenance deferral for a group with remaining deferrals
    {
      final var exception =
          assertThrows(
              SvcException.class,
              () ->
                  _maintenanceSvc.grantExtraMaintenanceDeferral(
                      groupId, jiraTicket, AuditInfoHelpers.fromSystem()));
      assertEquals(NDSErrorCode.UNSUPPORTED, exception.getErrorCode());
      assertTrue(exception.getMessage().contains("project still has remaining deferrals"));
    }

    // artificially defer maintenance twice
    _ndsGroupMaintenanceDao.deferMaintenanceOneWeekForGroup(groupId, 2, new Date(), null);

    try {
      _maintenanceSvc.grantExtraMaintenanceDeferral(
          groupId, jiraTicket, AuditInfoHelpers.fromSystem());
    } catch (final SvcException pE) {
      fail();
    }
    final NDSGroup refreshedGroup = _ndsGroupDao.find(groupId).get();
    assertEquals(1, refreshedGroup.getMaintenanceWindow().getNumberOfDeferrals().intValue());
    assertTrue(refreshedGroup.getMaintenanceWindow().getDeferralGrantJiraTicket().isPresent());
    assertEquals(
        jiraTicket, refreshedGroup.getMaintenanceWindow().getDeferralGrantJiraTicket().get());
  }

  private void setupForSoftwareVersionChangeTest() throws Exception {
    createProvisionedCluster();
    createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    publishDefaultAgentVersions();
  }

  @Test
  public void testAgentVersionNoChange() throws Exception {
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToNow(_ndsGroup);

    for (final SoftwareType type : getAgentSoftwares()) {
      setPhasedVersionToAutoRelease(type);
    }

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    _maintenanceSvc.refreshProjectSoftwareVersions(
        _ndsGroup, _group, clusters, publicationDate, _numMonitoringAddedMap, LOG, null);
    final NDSGroup refreshedGroup = _ndsGroupDao.find(_group.getId()).get();
    assertFalse(refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent());
  }

  @Test
  public void testAgentVersionNoChange_CriticalRelease() throws Exception {
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToNow(_ndsGroup);
    for (final SoftwareType type : getAgentSoftwares()) {
      setPhasedVersionToCriticalRelease(type);
    }

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    _maintenanceSvc.refreshProjectSoftwareVersions(
        _ndsGroup, _group, clusters, publicationDate, _numMonitoringAddedMap, LOG, null);

    final NDSGroup refreshedGroup = _ndsGroupDao.find(_group.getId()).get();
    assertFalse(refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent());
  }

  @Test
  public void testAgentVersionChange_WithinMaintenanceWindow() throws Exception {
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToNow(_ndsGroup);

    for (final SoftwareType type : getAgentSoftwares()) {
      final PhasedVersion autoVersion =
          _phasedVersionFactory.createPhasedVersion(new PhasedVersionParameters(type, "*******"));
      _phasedVersionSvc.save(autoVersion);
      setPhasedVersionToAutoRelease(type);
    }

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    List<MaintenanceCheckResult> maintenanceCheckResults =
        _maintenanceSvc.refreshProjectSoftwareVersions(
            _ndsGroup,
            _group,
            clusters,
            publicationDate,
            _numMonitoringAddedMap,
            LOG,
            automationConfig);

    final NDSGroup refreshedGroup = _ndsGroupDao.find(_group.getId()).get();
    assertTrue(refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent());
    assertEquals(
        publicationDate, refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().get());

    final MaintenanceCheckResult maintenanceCheckResult =
        maintenanceCheckResults.stream()
            .filter(
                m -> m.maintenanceType().equals(MaintenanceType.AUTOMATION_AGENT_VERSION_UPDATED))
            .findFirst()
            .get();

    assertTrue(maintenanceCheckResult.isStarted());
    assertEquals(
        maintenanceCheckResult.getFromVersion(),
        automationConfig.getDeployment().getAgentVersion().getVersion());
    assertEquals(maintenanceCheckResult.getToVersion(), "*******");
  }

  @Test
  public void testAgentVersionChange_outOfMaintenanceWindow() throws Exception {
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    for (final SoftwareType type : getAgentSoftwares()) {
      final PhasedVersion autoVersion =
          _phasedVersionFactory.createPhasedVersion(new PhasedVersionParameters(type, "*******"));
      _phasedVersionSvc.save(autoVersion);
      setPhasedVersionToAutoRelease(type);
    }

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    _maintenanceSvc.refreshProjectSoftwareVersions(
        _ndsGroup, _group, clusters, publicationDate, _numMonitoringAddedMap, LOG, null);
    final NDSGroup refreshedGroup = _ndsGroupDao.find(_group.getId()).get();
    assertFalse(refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent());
  }

  @Test
  public void testAgentVersionChange_M0s() throws Exception {
    // Test that groups with no dedicated clusters, but have automation configs and M0 clusters sets
    // setNeedsPublishForGroup.
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    for (final SoftwareType type : getAgentSoftwares()) {
      final PhasedVersion autoVersion =
          _phasedVersionFactory.createPhasedVersion(new PhasedVersionParameters(type, "*******"));
      _phasedVersionSvc.save(autoVersion);
      setPhasedVersionToCriticalRelease(type);
    }

    // Set up a group with an M0 cluster
    final ObjectId mtmGroupId = new ObjectId();
    final NDSGroup group = _groupSvc.ensureGroup(mtmGroupId);

    final ClusterDescription m0MtmClusterDesc =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(mtmGroupId, "m0Mtm"))
            .copy()
            .setState(ClusterDescription.State.IDLE)
            .build();
    _clusterDescriptionDao.save(m0MtmClusterDesc);
    final ObjectId tenantGroupId = new ObjectId();
    _groupSvc.ensureGroup(tenantGroupId);

    final ClusterDescription m0Tenant =
        new ClusterDescription(NDSModelTestFactory.getFreeClusterDescription())
            .copy()
            .setGroupId(tenantGroupId)
            .setName("m0Tenant1")
            .setState(ClusterDescription.State.IDLE)
            .build();
    _clusterDescriptionDao.save(m0Tenant);
    _ndsGroupDao.addCloudContainer(
        tenantGroupId,
        new FreeCloudProviderContainer(
            NDSModelTestFactory.getFreeContainer(
                mtmGroupId, m0MtmClusterDesc.getName(), m0Tenant.getName())));

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(tenantGroupId);
    final Date publicationDate = _currentTime;
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshProjectSoftwareVersions(
        group, _group, clusters, publicationDate, _numMonitoringAddedMap, LOG, automationConfig);
    final NDSGroup refreshedGroup = _ndsGroupDao.find(group.getGroupId()).get();
    assertTrue(refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent());
  }

  @Test
  public void testAgentVersionChange_CriticalRelease_outOfMaintenanceWindow() throws Exception {
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    for (final SoftwareType type : getAgentSoftwares()) {
      final PhasedVersion autoVersion =
          _phasedVersionFactory.createPhasedVersion(new PhasedVersionParameters(type, "*******"));
      _phasedVersionSvc.save(autoVersion);
      setPhasedVersionToCriticalRelease(type);
    }

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshProjectSoftwareVersions(
        _ndsGroup,
        _group,
        clusters,
        publicationDate,
        _numMonitoringAddedMap,
        LOG,
        automationConfig);
    final NDSGroup refreshedGroup = _ndsGroupDao.find(_group.getId()).get();
    assertTrue(refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent());
    assertEquals(
        publicationDate, refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().get());
  }

  @Test
  public void testAgentVersionChange_CriticalRelease_outOfMaintenanceWindow_addMonitoring()
      throws Exception {
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToAnotherDay(_ndsGroup);
    _appSettings.setProp(NDS_EHM_EXTENSION_ENABLED, "true", SettingType.MEMORY);

    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.AUTOMATION_AGENT, "*******"));
    _phasedVersionSvc.save(phasedVersion);
    setPhasedVersionToCriticalRelease(SoftwareType.AUTOMATION_AGENT);

    final NDSInternalMaintenanceRollout internalMaintenance =
        saveInternalMaintenanceAndEHMDocs(
            phasedVersion, MaintenanceType.AUTOMATION_AGENT_VERSION_UPDATED);
    // ensure that baseline data present so ActionToMonitor will be successfully added
    _ndsClusterSpotHealthCheckTestUtil.setupBaselineDataForActions(
        _ndsGroup, List.of(internalMaintenance.getActionId()), true);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshProjectSoftwareVersions(
        _ndsGroup,
        _group,
        clusters,
        publicationDate,
        _numMonitoringAddedMap,
        LOG,
        automationConfig);

    // assert that monitoring was added for all the clusters in the group-level maintenance
    assertEquals(1, _numMonitoringAddedMap.size());
    assertEquals(
        clusters.size(), _numMonitoringAddedMap.get(internalMaintenance.getActionId()).intValue());

    // assert that ActionToMonitor & ElevatedHealthMonitoringResults were added
    final NDSGroup updatedNDSGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
    final List<ActionToMonitor> actionToMonitorList =
        updatedNDSGroup.getHealthCheckMetadata().getActionsToMonitor();

    assertEquals(internalMaintenance.getActionId(), actionToMonitorList.get(0).getActionId());

    _elevatedHealthMonitoringResultSvc
        .findResult(
            internalMaintenance.getActionId(),
            clusters.get(0).getClusterDescription().getUniqueId())
        .orElseThrow();

    _appSettings.setProp(NDS_EHM_EXTENSION_ENABLED, "false", SettingType.MEMORY);
  }

  @Test
  public void testRefreshServerlessProxyVersion_addMonitoring() throws Exception {
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _appSettings.setProp(NDS_EHM_EXTENSION_ENABLED, "true", SettingType.MEMORY);

    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.SERVERLESS_PROXY, "*******"));
    _phasedVersionSvc.save(phasedVersion);
    setPhasedVersionToCriticalRelease(SoftwareType.SERVERLESS_PROXY);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    final NDSInternalMaintenanceRollout internalMaintenance =
        saveInternalMaintenanceAndEHMDocs(
            phasedVersion, MaintenanceType.SERVERLESS_PROXY_VERSION_UPDATED);
    // Set up health monitoring data for the action IDs so getBaselineDataForCluster returns data
    _ndsClusterSpotHealthCheckTestUtil.setupBaselineDataForActions(
        _ndsGroup, List.of(internalMaintenance.getActionId()), true);

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());
    automationConfig.setMaintainedEnvoyTemplate(new MaintainedEnvoyTemplate(_appSettings));

    _maintenanceSvc.refreshServerlessProxyVersion(
        _ndsGroup, _group, publicationDate, _numMonitoringAddedMap, LOG, automationConfig);

    // assert that monitoring was added for all the clusters in the group-level maintenance
    assertEquals(1, _numMonitoringAddedMap.size());
    assertEquals(
        clusters.size(), _numMonitoringAddedMap.get(internalMaintenance.getActionId()).intValue());

    // assert that ActionToMonitor & ElevatedHealthMonitoringResults were added
    final NDSGroup updatedNDSGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
    final List<ActionToMonitor> actionToMonitorList =
        updatedNDSGroup.getHealthCheckMetadata().getActionsToMonitor();

    assertEquals(internalMaintenance.getActionId(), actionToMonitorList.get(0).getActionId());

    _elevatedHealthMonitoringResultSvc
        .findResult(
            internalMaintenance.getActionId(),
            clusters.get(0).getClusterDescription().getUniqueId())
        .orElseThrow();

    _appSettings.setProp(NDS_EHM_EXTENSION_ENABLED, "false", SettingType.MEMORY);
  }

  @Test
  public void testRefreshUserIdentityServiceVersion_addMonitoring() throws Exception {
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _appSettings.setProp(NDS_EHM_EXTENSION_ENABLED, "true", SettingType.MEMORY);

    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.USER_IDENTITY_SERVICE, "*******"));
    _phasedVersionSvc.save(phasedVersion);
    setPhasedVersionToCriticalRelease(SoftwareType.USER_IDENTITY_SERVICE);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    final NDSInternalMaintenanceRollout internalMaintenance =
        saveInternalMaintenanceAndEHMDocs(
            phasedVersion, MaintenanceType.USER_IDENTITY_SERVICE_VERSION_UPDATED);
    _ndsClusterSpotHealthCheckTestUtil.setupBaselineDataForActions(
        _ndsGroup, List.of(internalMaintenance.getActionId()), true);

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublished(_ndsGroup.getGroupId());
    automationConfig.setAtlasUserIdentityServiceTemplate(
        new AtlasUserIdentityServiceTemplate(_appSettings));

    _maintenanceSvc.refreshUserIdentityServiceVersion(
        _ndsGroup, _group, publicationDate, _numMonitoringAddedMap, LOG, automationConfig);

    // assert that monitoring was added for all the clusters in the group-level maintenance
    assertEquals(1, _numMonitoringAddedMap.size());
    assertEquals(
        clusters.size(), _numMonitoringAddedMap.get(internalMaintenance.getActionId()).intValue());

    // assert that ActionToMonitor & ElevatedHealthMonitoringResults were added
    final NDSGroup updatedNDSGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
    final List<ActionToMonitor> actionToMonitorList =
        updatedNDSGroup.getHealthCheckMetadata().getActionsToMonitor();

    assertEquals(internalMaintenance.getActionId(), actionToMonitorList.get(0).getActionId());

    _elevatedHealthMonitoringResultSvc
        .findResult(
            internalMaintenance.getActionId(),
            clusters.get(0).getClusterDescription().getUniqueId())
        .orElseThrow();

    _appSettings.setProp(NDS_EHM_EXTENSION_ENABLED, "false", SettingType.MEMORY);
  }

  private NDSInternalMaintenanceRollout saveInternalMaintenanceAndEHMDocs(
      final PhasedVersion pPhasedVersion, final MaintenanceType pMaintenanceType) {
    final NDSInternalMaintenanceRollout internalMaintenance =
        _internalMaintenanceRolloutSvc
            .saveFromPhasedVersion(pPhasedVersion, pMaintenanceType, LOG)
            .orElseThrow();
    final MonitoringThresholds monitoringThresholds = getMonitoringThresholds();
    _internalMaintenanceRolloutSvc.addElevatedHealthMonitoring(
        internalMaintenance, monitoringThresholds);
    return internalMaintenance;
  }

  private void testFixedAgentVersion(final AgentType pType) throws Exception {
    setupForSoftwareVersionChangeTest();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    final FixedAgentVersion fixedAgentVersion = new FixedAgentVersion(pType, "*******");
    _ndsGroupDao.fixAgentVersion(_ndsGroup.getGroupId(), fixedAgentVersion);
    reloadNdsGroup();

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshProjectSoftwareVersions(
        _ndsGroup,
        _group,
        clusters,
        publicationDate,
        _numMonitoringAddedMap,
        LOG,
        automationConfig);
    final NDSGroup refreshedGroup = _ndsGroupDao.find(_group.getId()).get();
    assertTrue(refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent());
    assertEquals(
        publicationDate, refreshedGroup.getNeedsProcessAutomationConfigPublishAfter().get());
  }

  @Test
  public void testFixedAgentVersion_Automation() throws Exception {
    testFixedAgentVersion(AgentType.AUTOMATION);
  }

  @Test
  public void testRefreshSoftwareVersion_BIConnector() throws Exception {
    createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultBIConnectorVersion();
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);
    createMaintenanceHistory(
        _ndsGroup.getGroupId(),
        null,
        MaintenanceType.BI_CONNECTOR_VERSION_UPDATED,
        _appSettings.getBiConnectorVersion().getVersion(),
        "2.4.0");

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.BI_CONNECTOR, "2.4.0")));

    final Date publicationDate = _currentTime;
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());

    // BI connector is disabled by default - clusters won't need an automation config publish.
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshProjectSoftwareVersions(
        _ndsGroup,
        _group,
        clusters,
        publicationDate,
        _numMonitoringAddedMap,
        LOG,
        automationConfig);
    _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
        .map(ClusterDescription::new)
        .forEach(c -> assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent()));

    assertHistoryEntriesLackStartedDate(
        _ndsGroup.getGroupId(), MaintenanceType.BI_CONNECTOR_VERSION_UPDATED);

    // Enable BI connector but cluster is involved in a live import -> no publish.
    _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
        .map(ClusterDescription::new)
        .forEach(
            c ->
                _clusterDescriptionDao.updateBiConnector(
                    c.getGroupId(),
                    c.getName(),
                    new ClusterDescription.BiConnector(true, BiConnectorReadPreference.SECONDARY)));

    final List<Cluster> clustersUpdated = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());

    // BI Connector enabled -> publish needed
    _maintenanceSvc.refreshProjectSoftwareVersions(
        _ndsGroup,
        _group,
        clustersUpdated,
        publicationDate,
        _numMonitoringAddedMap,
        LOG,
        automationConfig);
    _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
        .map(ClusterDescription::new)
        .forEach(c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));

    assertHistoryEntriesContainStartedDate(
        _ndsGroup.getGroupId(), MaintenanceType.BI_CONNECTOR_VERSION_UPDATED);
  }

  @Test
  public void testRefreshMongotVersion_upToDate() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    saveFTSIndexForCluster(clusterDescription);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultMongotVersion();
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshMongotVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongotuneVersion_criticalRelease() throws Exception {
    _appSettings.setProp(NDS_EHM_EXTENSION_ENABLED, "true", SettingType.MEMORY);
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);

    // Assume feature flag just enabled for group, so mongotune version is not set yet
    assertTrue(clusterDescription.getMongotuneStatus().isEmpty());

    final String nextMongotuneVersion = "99.99.99";
    setMaintenanceWindowToNow(_ndsGroup);

    // Simulate patch fix released in CRITICAL mode with EHM enabled
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.MONGOTUNE, nextMongotuneVersion)
            .setReleaseMode(ReleaseMode.CRITICAL);
    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);
    _phasedVersionSvc.save(phasedVersion);
    final NDSInternalMaintenanceRollout rollout =
        saveInternalMaintenanceAndEHMDocs(phasedVersion, MaintenanceType.MONGOTUNE_VERSION_UPDATED);
    // Set up health monitoring data for the action IDs so getBaselineDataForCluster returns data
    _ndsClusterSpotHealthCheckTestUtil.setupBaselineDataForActions(
        _ndsGroup, List.of(rollout.getActionId()), true);

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongotuneVersion(
            _ndsGroup,
            _group,
            cluster,
            _currentTime,
            _numMonitoringAddedMap,
            LOG,
            automationConfig);
    assertTrue(maintenanceCheckResult.needMaintenance());
    assertTrue(maintenanceCheckResult.isCriticalRelease());

    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertEquals(
        nextMongotuneVersion,
        updatedClusterDescription.getMongotuneStatus().orElseThrow().version());
    assertEquals(
        MongotuneStatus.State.ENABLE,
        updatedClusterDescription.getMongotuneStatus().orElseThrow().state());
    assertEquals(1, _numMonitoringAddedMap.get(rollout.getActionId()).get());
    assertTrue(
        _ndsGroupDao
            .find(_ndsGroup.getGroupId())
            .orElseThrow()
            .getHealthCheckMetadata()
            .getActionsToMonitor()
            .stream()
            .anyMatch(a -> a.getActionId().equals(rollout.getActionId())));
    assertTrue(
        _elevatedHealthMonitoringResultSvc
            .findResult(rollout.getActionId(), updatedClusterDescription.getUniqueId())
            .isPresent());
  }

  @Test
  public void testRefreshMongotuneVersion_phasedRelease() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);

    final String currentVersion = "1.0.0";
    final String targetVersion = "2.0.0";
    _clusterSvc.updateMongotuneStatus(clusterDescription, currentVersion, State.ENABLE.name());

    setMaintenanceWindowToNow(_ndsGroup);

    // Simulate new version released in PHASED mode
    final PhasedReleaseCriteria phasedReleaseCriteria =
        new PhasedReleaseCriteria(70, 70, null, null, null, null);
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.MONGOTUNE, targetVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(phasedReleaseCriteria);
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(90, 90));
    final NDSGroup ndsGroupOutOfCohort = _ndsGroupDao.find(_ndsGroup.getGroupId()).orElseThrow();

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongotuneVersion(
            ndsGroupOutOfCohort,
            _group,
            cluster,
            _currentTime,
            _numMonitoringAddedMap,
            LOG,
            automationConfig);

    // Not in current release cohort, so no maintenance or cluster description updates required
    assertFalse(maintenanceCheckResult.needMaintenance());
    final ClusterDescription clusterDescriptionNoUpdate =
        _clusterDescriptionDao
            .findByName(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertEquals(
        currentVersion, clusterDescriptionNoUpdate.getMongotuneStatus().orElseThrow().version());

    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(10, 10));
    final NDSGroup ndsGroupInCohort = _ndsGroupDao.find(_ndsGroup.getGroupId()).orElseThrow();

    // In release cohort, so version on cluster description should be updated
    final MaintenanceCheckResult inCohortMaintenanceCheckResult =
        _maintenanceSvc.refreshMongotuneVersion(
            ndsGroupInCohort,
            _group,
            cluster,
            _currentTime,
            _numMonitoringAddedMap,
            LOG,
            automationConfig);
    assertTrue(inCohortMaintenanceCheckResult.needMaintenance());
    assertEquals(currentVersion, inCohortMaintenanceCheckResult.getFromVersion());
    assertEquals(targetVersion, inCohortMaintenanceCheckResult.getToVersion());

    final ClusterDescription clusterDescriptionWithUpdate =
        _clusterDescriptionDao
            .findByName(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertEquals(
        targetVersion, clusterDescriptionWithUpdate.getMongotuneStatus().orElseThrow().version());
    assertTrue(clusterDescriptionWithUpdate.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongotuneVersion_phasedRelease_previousVersion_Safe() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);

    final String currentVersion = "1.0.0";
    final String targetPreviousVersion = "1.0.1";
    final String targetNextVersion = "1.0.2";

    _clusterSvc.updateMongotuneStatus(clusterDescription, currentVersion, State.ENABLE.name());

    setMaintenanceWindowToAnotherDay(_ndsGroup);

    // Simulate new version released in PHASED mode
    final PhasedReleaseCriteria phasedReleaseCriteria =
        new PhasedReleaseCriteria(70, 70, null, null, null, null);
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.MONGOTUNE, targetPreviousVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setIsPreviousVersionSafe(true)
            .setPhasedReleaseCriteria(phasedReleaseCriteria);
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

    setPhasedTargetVersion(SoftwareType.MONGOTUNE, targetNextVersion);

    // set group outside of release cohorts
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(90, 90));
    final NDSGroup ndsGroupInCohort = _ndsGroupDao.find(_ndsGroup.getGroupId()).orElseThrow();

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    // maintenance should still be needed, and version should be set to targetPreviousVersion
    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongotuneVersion(
            ndsGroupInCohort,
            _group,
            cluster,
            _currentTime,
            _numMonitoringAddedMap,
            LOG,
            automationConfig);

    assertTrue(maintenanceCheckResult.needMaintenance());
    assertFalse(maintenanceCheckResult.isCriticalRelease());
    assertThat(maintenanceCheckResult.getFromVersion()).isEqualTo(currentVersion);
    assertThat(maintenanceCheckResult.getToVersion()).isEqualTo(targetPreviousVersion);

    final ClusterDescription clusterDescriptionNoUpdate =
        _clusterDescriptionDao
            .findByName(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertEquals(
        currentVersion, clusterDescriptionNoUpdate.getMongotuneStatus().orElseThrow().version());
  }

  @Test
  public void testRefreshMongotuneVersion_phasedRelease_previousVersion_Unsafe() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);

    final String currentVersion = "1.0.0";
    final String targetPreviousVersion = "1.0.1";
    final String targetNextVersion = "1.0.2";

    _clusterSvc.updateMongotuneStatus(clusterDescription, currentVersion, State.ENABLE.name());

    setMaintenanceWindowToAnotherDay(_ndsGroup);

    // Simulate new version released in PHASED mode
    final PhasedReleaseCriteria phasedReleaseCriteria =
        new PhasedReleaseCriteria(70, 70, null, null, null, null);
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.MONGOTUNE, targetPreviousVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setIsPreviousVersionSafe(false)
            .setPhasedReleaseCriteria(phasedReleaseCriteria);
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

    setPhasedTargetVersion(SoftwareType.MONGOTUNE, targetNextVersion);

    // set group outside of release cohorts
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(90, 90));
    final NDSGroup ndsGroupInCohort = _ndsGroupDao.find(_ndsGroup.getGroupId()).orElseThrow();

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    // maintenance should not be needed because previous version is set to unsafe
    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongotuneVersion(
            ndsGroupInCohort,
            _group,
            cluster,
            _currentTime,
            _numMonitoringAddedMap,
            LOG,
            automationConfig);

    assertFalse(maintenanceCheckResult.needMaintenance());
    assertFalse(maintenanceCheckResult.isCriticalRelease());
    assertThat(maintenanceCheckResult.getFromVersion()).isNull();
    assertThat(maintenanceCheckResult.getToVersion()).isNull();
  }

  @Test
  public void testRefreshMongotuneVersion_phasedRelease_previousVersion_Unsafe_Downgrade()
      throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);

    final String currentVersion = "1.0.1";
    final String targetPreviousVersion = "1.0.0";
    final String targetNextVersion = "1.0.2";

    _clusterSvc.updateMongotuneStatus(clusterDescription, currentVersion, State.ENABLE.name());

    setMaintenanceWindowToAnotherDay(_ndsGroup);

    // Simulate new version released in PHASED mode
    final PhasedReleaseCriteria phasedReleaseCriteria =
        new PhasedReleaseCriteria(70, 70, null, null, null, null);
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.MONGOTUNE, targetPreviousVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setIsPreviousVersionSafe(true)
            .setPhasedReleaseCriteria(phasedReleaseCriteria);
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

    setPhasedTargetVersion(SoftwareType.MONGOTUNE, targetNextVersion);

    // set group outside of release cohorts
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(90, 90));
    final NDSGroup ndsGroupInCohort = _ndsGroupDao.find(_ndsGroup.getGroupId()).orElseThrow();

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    // maintenance should not be needed because even though previous version is set to safe, it's
    // not safe to downgrade the mongotune from 1.0.1 (current) to 1.0.0 (target previous)
    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongotuneVersion(
            ndsGroupInCohort,
            _group,
            cluster,
            _currentTime,
            _numMonitoringAddedMap,
            LOG,
            automationConfig);

    assertFalse(maintenanceCheckResult.needMaintenance());
    assertFalse(maintenanceCheckResult.isCriticalRelease());
    assertThat(maintenanceCheckResult.getFromVersion()).isNull();
    assertThat(maintenanceCheckResult.getToVersion()).isNull();
  }

  @Test
  public void testRefreshMongotunePolicies() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ENABLE_MONGOTUNE);
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY);
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);

    final String currentVersion = "1.0.1";
    _clusterSvc.updateMongotuneStatus(clusterDescription, currentVersion, State.ENABLE.name());
    setMaintenanceWindowToNow(_ndsGroup);

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    // Maintenance required to roll out the write blocking policy.
    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongotunePolicies(_ndsGroup, _group, cluster, _currentTime, LOG);
    assertTrue(maintenanceCheckResult.needMaintenance());

    final Cluster updatedCluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertFalse(
        updatedCluster
            .getClusterDescription()
            .getMongotuneStatus()
            .orElseThrow()
            .policies()
            .isEmpty());
    final Map<PolicyType, PolicyStatus> policies =
        updatedCluster.getClusterDescription().getMongotuneStatus().orElseThrow().policies();
    assertTrue(policies.containsKey(PolicyType.DISK_WRITE_BLOCKING));
    // 6.0 cluster is not eligible, but enabled should be true since the policy has been rolled out
    assertFalse(policies.get(PolicyType.DISK_WRITE_BLOCKING).isEligible());
    assertTrue(policies.get(PolicyType.DISK_WRITE_BLOCKING).isEnabled());

    // Policies already initialized, so maintenance not required
    final MaintenanceCheckResult maintenanceCheckResult2 =
        _maintenanceSvc.refreshMongotunePolicies(
            _ndsGroup, _group, updatedCluster, _currentTime, LOG);
    assertFalse(maintenanceCheckResult2.needMaintenance());

    // If policy is disabled, maintenance should still be a no-op
    policies.put(
        PolicyType.DISK_WRITE_BLOCKING,
        policies.get(PolicyType.DISK_WRITE_BLOCKING).toBuilder().setEnabled(false).build());
    _clusterSvc.updateMongotunePolicies(clusterDescription, policies);
    final Cluster clusterWithDisabledMongotune =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    final MaintenanceCheckResult maintenanceCheckResult3 =
        _maintenanceSvc.refreshMongotunePolicies(
            _ndsGroup, _group, clusterWithDisabledMongotune, _currentTime, LOG);
    assertFalse(maintenanceCheckResult3.needMaintenance());
  }

  @Test
  public void testRefreshMongotVersion_searchNodes() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultMongotVersion();
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, "9.9.9.999")));
    setPhasedVersionToAutoRelease(SoftwareType.MONGOT);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    // No search indexes or search nodes. Maintenance is not needed.
    final MaintenanceCheckResult maintenanceCheckResult1 =
        _maintenanceSvc.refreshMongotVersion(
            _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);
    assertFalse(maintenanceCheckResult1.needMaintenance());
    final ClusterDescription clusterDescriptionUpdated1 =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(clusterDescriptionUpdated1.getNeedsMongoDBConfigPublishAfter().isPresent());

    // Add search nodes.
    _searchDeploymentDescriptionSvc.create(
        _searchDeploymentDescriptionSvc.buildSearchDeploymentDescription(
            clusterDescriptionUpdated1,
            clusterDescription.getRegionNames().stream()
                .collect(
                    Collectors.toMap(
                        pRegionName -> pRegionName,
                        pRegionName -> AWSSearchInstanceSize.S20_HIGHCPU_NVME)),
            clusterDescription.getRegionNames().stream()
                .collect(Collectors.toMap(pRegionName -> pRegionName, pRegionName -> 2)),
            false));

    // Maintenance is needed.
    final MaintenanceCheckResult maintenanceCheckResult2 =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshMongotVersion(
                    _ndsGroup,
                    _group,
                    cluster,
                    publicationDate,
                    new HashMap<>(),
                    LOG,
                    automationConfig));
    assertTrue(maintenanceCheckResult2.needMaintenance());

    final ClusterDescription clusterDescriptionUpdated2 =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated2.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertFalse(maintenanceCheckResult2.isCriticalRelease());
    assertEquals(
        maintenanceCheckResult2.getFromVersion(), _appSettings.getMongotVersion().getVersion());
    assertEquals("9.9.9.999", maintenanceCheckResult2.getToVersion());

    final List<NdsMaintenanceHistory> histories =
        _ndsMaintenanceHistorySvc.findAllNotCompletedByGroupIdAndType(
            _ndsGroup.getGroupId(), MaintenanceType.MONGOT_VERSION_UPDATED);

    assertEquals(1, histories.size());
    assertEquals(maintenanceCheckResult2.getFromVersion(), histories.get(0).getFromVersion());
    assertEquals(maintenanceCheckResult2.getToVersion(), histories.get(0).getToVersion());
  }

  @Test
  public void testRefreshMongotVersion_outOfDate_inWindow() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    saveFTSIndexForCluster(clusterDescription);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultMongotVersion();
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, "9.9.9.999")));
    setPhasedVersionToAutoRelease(SoftwareType.MONGOT);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    final MaintenanceCheckResult maintenanceCheckResult =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshMongotVersion(
                    _ndsGroup,
                    _group,
                    cluster,
                    publicationDate,
                    new HashMap<>(),
                    LOG,
                    automationConfig));

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertFalse(maintenanceCheckResult.isCriticalRelease());
    assertEquals(
        maintenanceCheckResult.getFromVersion(), _appSettings.getMongotVersion().getVersion());
    assertEquals("9.9.9.999", maintenanceCheckResult.getToVersion());

    final List<NdsMaintenanceHistory> histories =
        _ndsMaintenanceHistorySvc.findAllNotCompletedByGroupIdAndType(
            _ndsGroup.getGroupId(), MaintenanceType.MONGOT_VERSION_UPDATED);

    assertEquals(1, histories.size());
    assertEquals(maintenanceCheckResult.getFromVersion(), histories.get(0).getFromVersion());
    assertEquals(maintenanceCheckResult.getToVersion(), histories.get(0).getToVersion());
  }

  @Test
  public void testRefreshMongotVersion_outOfDate_notInWindow() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    saveFTSIndexForCluster(clusterDescription);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultMongotVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, "9.9.9.999")));
    setPhasedVersionToAutoRelease(SoftwareType.MONGOT);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshMongotVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongotVersion_outOfDate_criticalRelease() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    saveFTSIndexForCluster(clusterDescription);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultMongotVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, "9.9.9.999")));
    setPhasedVersionToCriticalRelease(SoftwareType.MONGOT);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshMongotVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongotVersion_previousVersion_outOfCohort_withinMaintenanceWindow()
      throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    publishMongotWithVersion(new MongotTemplate("1.46.0"));
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final String previousMongotVersion = "1.47.0";
    final String mongotVersion = "1.48.0";

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, previousMongotVersion)
                .setIsPreviousVersionSafe(true)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.MONGOT, mongotVersion);

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertThat(cluster.getClusterDescription().getNeedsMongoDBConfigPublishAfter()).isEmpty();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    // Add search nodes
    final SearchDeploymentDescription searchDeploymentDescription =
        _searchDeploymentDescriptionSvc.buildSearchDeploymentDescription(
            cluster.getClusterDescription(),
            clusterDescription.getRegionNames().stream()
                .collect(
                    Collectors.toMap(
                        pRegionName -> pRegionName,
                        pRegionName -> AWSSearchInstanceSize.S20_HIGHCPU_NVME)),
            clusterDescription.getRegionNames().stream()
                .collect(Collectors.toMap(pRegionName -> pRegionName, pRegionName -> 2)),
            false);
    _searchDeploymentDescriptionSvc.create(searchDeploymentDescription);

    final MaintenanceCheckResult maintenanceCheckResult =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshMongotVersion(
                    _ndsGroup,
                    _group,
                    cluster,
                    _currentTime,
                    new HashMap<>(),
                    LOG,
                    automationConfig));

    assertThat(maintenanceCheckResult.isStarted()).isTrue();
    assertThat(maintenanceCheckResult.needMaintenance()).isTrue();
    assertThat(maintenanceCheckResult.isCriticalRelease()).isFalse();
    assertThat(maintenanceCheckResult.getFromVersion())
        .isEqualTo(automationConfig.getMongotTemplate().getVersion());
    assertThat(maintenanceCheckResult.getToVersion()).isEqualTo(previousMongotVersion);

    assertThat(
            _clusterDescriptionDao
                .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
                .orElseThrow()
                .getNeedsMongoDBConfigPublishAfter())
        .isPresent();
  }

  @Test
  public void
      testRefreshMongotVersion_previousVersion_isSafe_false_outOfCohort_withinMaintenanceWindow()
          throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    publishMongotWithVersion(new MongotTemplate("1.46.0"));
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final String previousMongotVersion = "1.47.0";
    final String mongotVersion = "1.48.0";

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, previousMongotVersion)
                .setIsPreviousVersionSafe(false)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.MONGOT, mongotVersion);

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertThat(cluster.getClusterDescription().getNeedsMongoDBConfigPublishAfter()).isEmpty();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    // Add search nodes
    final SearchDeploymentDescription searchDeploymentDescription =
        _searchDeploymentDescriptionSvc.buildSearchDeploymentDescription(
            cluster.getClusterDescription(),
            clusterDescription.getRegionNames().stream()
                .collect(
                    Collectors.toMap(
                        pRegionName -> pRegionName,
                        pRegionName -> AWSSearchInstanceSize.S20_HIGHCPU_NVME)),
            clusterDescription.getRegionNames().stream()
                .collect(Collectors.toMap(pRegionName -> pRegionName, pRegionName -> 2)),
            false);
    _searchDeploymentDescriptionSvc.create(searchDeploymentDescription);

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongotVersion(
            _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG, automationConfig);

    assertThat(maintenanceCheckResult.needMaintenance()).isFalse();
    assertThat(maintenanceCheckResult.isStarted()).isFalse();
    assertThat(maintenanceCheckResult.isCriticalRelease()).isFalse();
    assertThat(maintenanceCheckResult.getFromVersion()).isNull();
    assertThat(maintenanceCheckResult.getToVersion()).isNull();

    assertThat(
            _clusterDescriptionDao
                .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
                .orElseThrow()
                .getNeedsMongoDBConfigPublishAfter())
        .isEmpty();
  }

  @Test
  public void testRefreshMongotVersion_previousVersion_equalToCurrent_noMaintenanceNeeded()
      throws Exception {
    // Arrange
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);

    final String currentMongotVersion = "1.47.0";

    publishMongotWithVersion(new MongotTemplate(currentMongotVersion));
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    final String targetPreviousMongotVersion = "1.47.0";
    final String targetNextMongotVersion = "1.48.0";

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, targetPreviousMongotVersion)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.MONGOT, targetNextMongotVersion);
    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(SoftwareType.MONGOT);

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    // Add search nodes
    final SearchDeploymentDescription searchDeploymentDescription =
        _searchDeploymentDescriptionSvc.buildSearchDeploymentDescription(
            cluster.getClusterDescription(),
            clusterDescription.getRegionNames().stream()
                .collect(
                    Collectors.toMap(
                        pRegionName -> pRegionName,
                        pRegionName -> AWSSearchInstanceSize.S20_HIGHCPU_NVME)),
            clusterDescription.getRegionNames().stream()
                .collect(Collectors.toMap(pRegionName -> pRegionName, pRegionName -> 2)),
            false);
    _searchDeploymentDescriptionSvc.create(searchDeploymentDescription);

    assertThat(cluster.getClusterDescription().getNeedsMongoDBConfigPublishAfter()).isEmpty();
    assertThat(phasedVersion.getPreviousVersion()).hasValue(targetPreviousMongotVersion);
    assertThat(phasedVersion.getTargetVersion()).isEqualTo(targetNextMongotVersion);

    // Act

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongotVersion(
            _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG, automationConfig);

    // Assert
    assertThat(maintenanceCheckResult.needMaintenance()).isFalse();
    assertThat(maintenanceCheckResult.isCriticalRelease()).isFalse();
    assertThat(maintenanceCheckResult.getFromVersion()).isNull();
    assertThat(maintenanceCheckResult.getToVersion()).isNull();

    assertThat(MongotVersion.parse(targetPreviousMongotVersion))
        .isEqualTo(MongotVersion.parse(currentMongotVersion));

    assertThat(
            _clusterDescriptionDao
                .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
                .orElseThrow()
                .getNeedsMongoDBConfigPublishAfter())
        .isEmpty();
  }

  @Test
  public void testRefreshMongotVersion_previousVersion_lessThanCurrent_noMaintenanceNeeded()
      throws Exception {
    // Arrange
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);

    final String currentMongotVersion = "1.47.0";

    publishMongotWithVersion(new MongotTemplate(currentMongotVersion));
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    final String targetPreviousMongotVersion = "1.46.0";
    final String targetNextMongotVersion = "1.48.0";

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGOT, targetPreviousMongotVersion)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.MONGOT, targetNextMongotVersion);
    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(SoftwareType.MONGOT);

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    // Add search nodes
    final SearchDeploymentDescription searchDeploymentDescription =
        _searchDeploymentDescriptionSvc.buildSearchDeploymentDescription(
            cluster.getClusterDescription(),
            clusterDescription.getRegionNames().stream()
                .collect(
                    Collectors.toMap(
                        pRegionName -> pRegionName,
                        pRegionName -> AWSSearchInstanceSize.S20_HIGHCPU_NVME)),
            clusterDescription.getRegionNames().stream()
                .collect(Collectors.toMap(pRegionName -> pRegionName, pRegionName -> 2)),
            false);
    _searchDeploymentDescriptionSvc.create(searchDeploymentDescription);

    assertThat(cluster.getClusterDescription().getNeedsMongoDBConfigPublishAfter()).isEmpty();
    assertThat(phasedVersion.getPreviousVersion()).hasValue(targetPreviousMongotVersion);
    assertThat(phasedVersion.getTargetVersion()).isEqualTo(targetNextMongotVersion);

    // Act

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongotVersion(
            _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG, automationConfig);

    // Assert
    assertThat(maintenanceCheckResult.needMaintenance()).isFalse();
    assertThat(maintenanceCheckResult.isCriticalRelease()).isFalse();
    assertThat(maintenanceCheckResult.getFromVersion()).isNull();
    assertThat(maintenanceCheckResult.getToVersion()).isNull();

    assertThat(
            MongotVersion.parse(targetPreviousMongotVersion)
                .isLessThan(MongotVersion.parse(currentMongotVersion)))
        .isTrue();

    assertThat(
            _clusterDescriptionDao
                .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
                .orElseThrow()
                .getNeedsMongoDBConfigPublishAfter())
        .isEmpty();
  }

  @Test
  public void testRefreshMongotVersions_fixedVersion() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    saveFTSIndexForCluster(clusterDescription);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultMongotVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    final FixedAgentVersion fixedAgentVersion =
        new FixedAgentVersion(AgentType.MONGOT, "9.9.9.999");
    _ndsGroupDao.fixAgentVersion(_ndsGroup.getGroupId(), fixedAgentVersion);
    reloadNdsGroup();

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshMongotVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshSearchEnvoyVersion_upToDate() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultSearchEnvoyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshSearchEnvoyVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshSearchEnvoyVersion_outOfDate_tenant() throws Exception {
    final ClusterDescription mtmTenantClusterDescription =
        new ClusterDescription(NDSModelTestFactory.getFreeClusterDescription())
            .copy()
            .setGroupId(_group.getId())
            .build();
    _clusterDescriptionDao.save(mtmTenantClusterDescription);
    final ClusterDescription serverlessTenantClusterDescription =
        new ClusterDescription(NDSModelTestFactory.getServerlessClusterDescription())
            .copy()
            .setGroupId(_group.getId())
            .build();
    _clusterDescriptionDao.save(serverlessTenantClusterDescription);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultSearchEnvoyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.SEARCH_ENVOY, "9.9.9.999")));
    setPhasedVersionToAutoRelease(SoftwareType.SEARCH_ENVOY);

    final Date publicationDate = _currentTime;
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshSearchEnvoyVersion(
        _ndsGroup,
        _group,
        Cluster.getCluster(mtmTenantClusterDescription, List.of()),
        publicationDate,
        new HashMap<>(),
        LOG,
        automationConfig);
    _maintenanceSvc.refreshSearchEnvoyVersion(
        _ndsGroup,
        _group,
        Cluster.getCluster(serverlessTenantClusterDescription, List.of()),
        publicationDate,
        new HashMap<>(),
        LOG,
        automationConfig);

    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), mtmTenantClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), serverlessTenantClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
  }

  @Test
  public void testRefreshSearchEnvoyVersion_outOfDate_inWindow() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultSearchEnvoyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.SEARCH_ENVOY, "9.9.9.999")));
    setPhasedVersionToAutoRelease(SoftwareType.SEARCH_ENVOY);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    final MaintenanceCheckResult maintenanceCheckResult =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshSearchEnvoyVersion(
                    _ndsGroup,
                    _group,
                    cluster,
                    publicationDate,
                    new HashMap<>(),
                    LOG,
                    automationConfig));

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertFalse(maintenanceCheckResult.isCriticalRelease());
    assertEquals(
        maintenanceCheckResult.getFromVersion(),
        new SearchAppSettings(_appSettings).getSearchEnvoyVersion().getVersion());
    assertEquals("9.9.9.999", maintenanceCheckResult.getToVersion());
  }

  @Test
  public void testRefreshSearchEnvoyVersion_outOfDate_notInWindow() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultSearchEnvoyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.SEARCH_ENVOY, "9.9.9.999")));
    setPhasedVersionToAutoRelease(SoftwareType.SEARCH_ENVOY);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshSearchEnvoyVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshSearchEnvoyVersion_outOfDate_criticalRelease() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultSearchEnvoyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.SEARCH_ENVOY, "9.9.9.999")));
    setPhasedVersionToCriticalRelease(SoftwareType.SEARCH_ENVOY);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshSearchEnvoyVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshSearchEnvoyVersions_fixedVersion() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultSearchEnvoyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    final FixedAgentVersion fixedAgentVersion =
        new FixedAgentVersion(AgentType.SEARCH_ENVOY, "9.9.9.999");
    _ndsGroupDao.fixAgentVersion(_ndsGroup.getGroupId(), fixedAgentVersion);
    reloadNdsGroup();

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    _maintenanceSvc.refreshSearchEnvoyVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshAtlasProxyVersion_upToDate() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            true);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultAtlasProxyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    _maintenanceSvc.refreshAtlasProxyVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, null);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshAtlasProxyVersion_outOfDate_inWindow() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            true);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultAtlasProxyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.ATLAS_PROXY, "9.9.9.999")));
    setPhasedVersionToAutoRelease(SoftwareType.ATLAS_PROXY);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    final MaintenanceCheckResult maintenanceCheckResult =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshAtlasProxyVersion(
                    _ndsGroup,
                    _group,
                    cluster,
                    publicationDate,
                    new HashMap<>(),
                    LOG,
                    automationConfig));

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertTrue(maintenanceCheckResult.needMaintenance());
    assertFalse(maintenanceCheckResult.isCriticalRelease());
    assertEquals(
        maintenanceCheckResult.getFromVersion(), _appSettings.getAtlasProxyVersion().getVersion());
    assertEquals(maintenanceCheckResult.getToVersion(), "9.9.9.999");
  }

  @Test
  public void testRefreshAtlasProxyVersion_outOfDate_notInWindow() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            true);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultAtlasProxyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.ATLAS_PROXY, "9.9.9.999")));
    setPhasedVersionToAutoRelease(SoftwareType.ATLAS_PROXY);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    _maintenanceSvc.refreshAtlasProxyVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, null);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshAtlasProxyVersion_outOfDate_criticalRelease() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            true);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultAtlasProxyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.ATLAS_PROXY, "9.9.9.999")));
    setPhasedVersionToCriticalRelease(SoftwareType.ATLAS_PROXY);

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());
    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshAtlasProxyVersion(
            _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertTrue(maintenanceCheckResult.isCriticalRelease());
    assertEquals(
        maintenanceCheckResult.getFromVersion(), _appSettings.getAtlasProxyVersion().getVersion());
    assertEquals(maintenanceCheckResult.getToVersion(), "9.9.9.999");
  }

  @Test
  public void testRefreshAtlasProxyVersions_fixedVersion() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            true);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultAtlasProxyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    final FixedAgentVersion fixedAgentVersion =
        new FixedAgentVersion(AgentType.ATLAS_PROXY, "9.9.9.999");
    _ndsGroupDao.fixAgentVersion(_ndsGroup.getGroupId(), fixedAgentVersion);
    reloadNdsGroup();

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshAtlasProxyVersion(
            _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());

    assertTrue(maintenanceCheckResult.isCriticalRelease());
    assertEquals(
        maintenanceCheckResult.getFromVersion(), _appSettings.getAtlasProxyVersion().getVersion());
    assertEquals(maintenanceCheckResult.getToVersion(), "9.9.9.999");
  }

  @Test
  public void testRefreshAtlasProxyVersions_notMTM() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultAtlasProxyVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    final FixedAgentVersion fixedAgentVersion =
        new FixedAgentVersion(AgentType.ATLAS_PROXY, "9.9.9.999");
    _ndsGroupDao.fixAgentVersion(_ndsGroup.getGroupId(), fixedAgentVersion);
    reloadNdsGroup();

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    _maintenanceSvc.refreshAtlasProxyVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, null);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshAtlasProxyVersions_nullTemplate() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    final FixedAgentVersion fixedAgentVersion =
        new FixedAgentVersion(AgentType.ATLAS_PROXY, "9.9.9.999");
    _ndsGroupDao.fixAgentVersion(_ndsGroup.getGroupId(), fixedAgentVersion);
    reloadNdsGroup();

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    _maintenanceSvc.refreshAtlasProxyVersion(
        _ndsGroup, _group, cluster, publicationDate, new HashMap<>(), LOG, null);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongoDBToolsVersions_fixedVersion() throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            true);
    forceAutomationConfigToPublished(_organization, _group);
    publishDefaultMongoDBToolsVersion();
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    assertEquals(
        _automationConfigSvc
            .findPublishedOrEmpty(_ndsGroup.getGroupId())
            .getMongoDbToolsTemplate()
            .getVersion(),
        _phasedVersionSvc.findBySoftwareType(SoftwareType.MONGODB_TOOLS).get().getTargetVersion());

    final FixedAgentVersion fixedAgentVersion =
        new FixedAgentVersion(AgentType.MONGODB_TOOLS, "9.9.9.999");
    _ndsGroupDao.fixAgentVersion(_ndsGroup.getGroupId(), fixedAgentVersion);
    reloadNdsGroup();

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    final AutomationConfig automationConfig =
        _automationConfigSvc.findPublishedOrEmpty(_ndsGroup.getGroupId());

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongoDbToolsVersions(
            _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG, automationConfig);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();

    assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertTrue(maintenanceCheckResult.isCriticalRelease());
    assertEquals(maintenanceCheckResult.getFromVersion(), _appSettings.getMongoDbToolsVersion());
    assertEquals(maintenanceCheckResult.getToVersion(), "9.9.9.999");
  }

  // Helper function for testing
  private ClusterDescription findClusterDescriptionByGroupIdAndUniqueId(
      ObjectId pGroupId, ObjectId pUniqueId) throws IllegalStateException {
    return _clusterDescriptionDao
        .findByUniqueId(pGroupId, pUniqueId)
        .orElseThrow(IllegalStateException::new);
  }

  @Test
  public void testGetMongoDbEolVersionUpgradeTarget_forDeprecatedCluster() throws Exception {
    final ClusterDescription deprecatedCluster =
        createProvisionedCluster()
            .copy()
            .setMongoDBVersion(NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getVersion())
            .setMongoDBMajorVersion(
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString())
            .build();
    final ClusterDescription deprecatedFixedFCVCluster =
        createProvisionedCluster()
            .copy()
            .setFixedFeatureCompatibilityVersion(
                Optional.of(
                    new FixedVersion(
                        NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString())))
            .setMongoDBVersion(NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getVersion())
            .setMongoDBMajorVersion(
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString())
            .build();

    // verify single version upgrade target returned when
    // ATLAS_ENFORCE_EOL_SINGLE_VERSION_UPGRADE_FOR_ORG
    // FF is enabled on the organization
    {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          null, _organization, FeatureFlag.ATLAS_ENFORCE_EOL_SINGLE_VERSION_UPGRADE_FOR_ORG);

      final PhasedVersionParameters multiVersionTargetParams =
          new PhasedVersionParameters(
                  SoftwareType.MONGODB_EOL_VERSION,
                  String.join(
                      ",",
                      NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
                      NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion()))
              .setReleaseMode(ReleaseMode.PHASED)
              .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));
      final MongoDBEOLPhasedVersion phasedVersionWithMultipleTargetVersions =
          (MongoDBEOLPhasedVersion)
              _phasedVersionFactory.createPhasedVersion(multiVersionTargetParams);

      final Optional<TargetMongoDbEolVersion> targetVersionOpt =
          _maintenanceSvc.getMongoDbEolVersionUpgradeTarget(
              _group, deprecatedCluster, phasedVersionWithMultipleTargetVersions);
      assertThat(targetVersionOpt).isNotEmpty();
      final TargetMongoDbEolVersion targetVersion = targetVersionOpt.get();
      assertThat(targetVersion.version()).isEqualTo(NDSModelTestFactory.MONGODB_VERSION_CURRENT);
      assertThat(targetVersion.upgradeType()).isEqualTo(UpgradeType.SINGLE_VERSION);
      assertThat(targetVersion.sequenceFinalVersion()).isNull();

      FeatureFlagIntTestUtil.disableFeatureForEntity(
          null, _organization, FeatureFlag.ATLAS_ENFORCE_EOL_SINGLE_VERSION_UPGRADE_FOR_ORG);
    }

    // verify single version upgrade target returned when consecutive version upgrades are not
    // supported by phased version configuration
    {
      final PhasedVersionParameters singleVersionTargetParams =
          new PhasedVersionParameters(
                  SoftwareType.MONGODB_EOL_VERSION,
                  NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion())
              .setReleaseMode(ReleaseMode.PHASED)
              .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));
      final MongoDBEOLPhasedVersion phasedVersionWithSingleTargetVersion =
          (MongoDBEOLPhasedVersion)
              _phasedVersionFactory.createPhasedVersion(singleVersionTargetParams);

      final Optional<TargetMongoDbEolVersion> targetVersionOpt =
          _maintenanceSvc.getMongoDbEolVersionUpgradeTarget(
              _group, deprecatedCluster, phasedVersionWithSingleTargetVersion);
      assertThat(targetVersionOpt).isNotEmpty();
      final TargetMongoDbEolVersion targetVersion = targetVersionOpt.get();
      assertThat(targetVersion.version()).isEqualTo(NDSModelTestFactory.MONGODB_VERSION_CURRENT);
      assertThat(targetVersion.upgradeType()).isEqualTo(UpgradeType.SINGLE_VERSION);
      assertThat(targetVersion.sequenceFinalVersion()).isNull();
    }

    // verify single version upgrade target returned when consecutive version upgrades are supported
    // by phased version configuration, but cluster FCV is fixed
    {
      final PhasedVersionParameters multiVersionTargetParams =
          new PhasedVersionParameters(
                  SoftwareType.MONGODB_EOL_VERSION,
                  String.join(
                      ",",
                      NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
                      NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion()))
              .setReleaseMode(ReleaseMode.PHASED)
              .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));
      final MongoDBEOLPhasedVersion phasedVersionWithMultipleTargetVersions =
          (MongoDBEOLPhasedVersion)
              _phasedVersionFactory.createPhasedVersion(multiVersionTargetParams);

      final Optional<TargetMongoDbEolVersion> targetVersionOpt =
          _maintenanceSvc.getMongoDbEolVersionUpgradeTarget(
              _group, deprecatedFixedFCVCluster, phasedVersionWithMultipleTargetVersions);
      assertThat(targetVersionOpt).isNotEmpty();
      final TargetMongoDbEolVersion targetVersion = targetVersionOpt.get();
      assertThat(targetVersion.version()).isEqualTo(NDSModelTestFactory.MONGODB_VERSION_CURRENT);
      assertThat(targetVersion.upgradeType()).isEqualTo(UpgradeType.SINGLE_VERSION);
      assertThat(targetVersion.sequenceFinalVersion()).isNull();
    }

    // verify consecutive version upgrade target returned when consecutive version upgrades are
    // supported by phased version configuration
    {
      final PhasedVersionParameters multiVersionTargetParams =
          new PhasedVersionParameters(
                  SoftwareType.MONGODB_EOL_VERSION,
                  String.join(
                      ",",
                      NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
                      NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion()))
              .setReleaseMode(ReleaseMode.PHASED)
              .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));
      final MongoDBEOLPhasedVersion phasedVersionWithMultipleTargetVersions =
          (MongoDBEOLPhasedVersion)
              _phasedVersionFactory.createPhasedVersion(multiVersionTargetParams);

      final Optional<TargetMongoDbEolVersion> targetVersionOpt =
          _maintenanceSvc.getMongoDbEolVersionUpgradeTarget(
              _group, deprecatedCluster, phasedVersionWithMultipleTargetVersions);
      assertThat(targetVersionOpt).isNotEmpty();
      final TargetMongoDbEolVersion targetVersion = targetVersionOpt.get();
      assertThat(targetVersion.version()).isEqualTo(NDSModelTestFactory.MONGODB_VERSION_CURRENT);
      assertThat(targetVersion.upgradeType()).isEqualTo(UpgradeType.CONSECUTIVE_VERSION);
      assertThat(targetVersion.sequenceFinalVersion())
          .isEqualTo(NDSModelTestFactory.MONGODB_VERSION_NEXT);
    }
  }

  @Test
  public void testGetMongoDbEolVersionUpgradeTarget_forNonDeprecatedCluster() throws Exception {
    final ClusterDescription cluster =
        createProvisionedCluster()
            .copy()
            .setMongoDBVersion(NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion())
            .setMongoDBMajorVersion(
                NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString())
            .build();

    // verify no target version returned when cluster is not deprecated and consecutive version
    // upgrade is not supported in the phased version configuration
    {
      final PhasedVersionParameters singleVersionTargetParams =
          new PhasedVersionParameters(
                  SoftwareType.MONGODB_EOL_VERSION,
                  NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion())
              .setReleaseMode(ReleaseMode.PHASED)
              .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));
      final MongoDBEOLPhasedVersion phasedVersionWithSingleTargetVersion =
          (MongoDBEOLPhasedVersion)
              _phasedVersionFactory.createPhasedVersion(singleVersionTargetParams);

      final Optional<TargetMongoDbEolVersion> targetVersionOpt =
          _maintenanceSvc.getMongoDbEolVersionUpgradeTarget(
              _group, cluster, phasedVersionWithSingleTargetVersion);
      assertThat(targetVersionOpt).isEmpty();
    }

    // when consecutive version upgrade is supported
    {
      final PhasedVersionParameters consecutiveVersionTargetParams =
          new PhasedVersionParameters(
                  SoftwareType.MONGODB_EOL_VERSION,
                  String.join(
                      ",",
                      NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
                      NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion()))
              .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));
      final MongoDBEOLPhasedVersion phasedVersionWithMultipleTargetVersions =
          (MongoDBEOLPhasedVersion)
              _phasedVersionFactory.createPhasedVersion(consecutiveVersionTargetParams);

      // verify no target version returned when there is no EOL version upgrade history
      // for the cluster
      final Optional<TargetMongoDbEolVersion> targetVersionOpt =
          _maintenanceSvc.getMongoDbEolVersionUpgradeTarget(
              _group, cluster, phasedVersionWithMultipleTargetVersions);
      assertThat(targetVersionOpt).isEmpty();

      // when there is a previously COMPLETED EOL version upgrade history for the cluster
      // and the monitoring period (2 days) has passed
      _eolVersionUpgradeHistoryDao.save(
          EolVersionUpgradeHistory.builder()
              .groupId(_ndsGroup.getGroupId())
              .orgId(_organization.getId())
              .clusterUniqueId(cluster.getUniqueId())
              .clusterName(cluster.getName())
              .status(EolVersionUpgradeHistory.Status.COMPLETED)
              .upgradeType(EolVersionUpgradeHistory.UpgradeType.CONSECUTIVE_VERSION)
              .fromVersion(NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getVersion())
              .toVersion(NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion())
              .startedDate(DateUtils.addDays(new Date(), -2))
              .completedDate(DateUtils.addDays(new Date(), -2))
              .sequenceFinalVersion(NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion())
              .build());
      // verify target version returned
      final Optional<TargetMongoDbEolVersion> targetVersionOpt2 =
          _maintenanceSvc.getMongoDbEolVersionUpgradeTarget(
              _group, cluster, phasedVersionWithMultipleTargetVersions);
      assertThat(targetVersionOpt2).isPresent();
      final TargetMongoDbEolVersion targetVersion2 = targetVersionOpt2.get();
      assertThat(targetVersion2.version()).isEqualTo(NDSModelTestFactory.MONGODB_VERSION_NEXT);
      assertThat(targetVersion2.upgradeType()).isEqualTo(UpgradeType.CONSECUTIVE_VERSION);
      assertThat(targetVersion2.sequenceFinalVersion())
          .isEqualTo(NDSModelTestFactory.MONGODB_VERSION_NEXT);

      // update EOL version upgrade history record completedDate to now
      _eolVersionUpgradeHistoryDao
          .findAll(
              _ndsGroup.getGroupId(),
              cluster.getUniqueId(),
              EolVersionUpgradeHistory.Status.COMPLETED)
          .forEach(h -> _eolVersionUpgradeHistoryDao.markCompleted(h.id(), new Date()));

      // verify no target version returned when there is a previously COMPLETED EOL version upgrade
      // history for the cluster and the monitoring period has not passed
      final Optional<TargetMongoDbEolVersion> targetVersionOpt3 =
          _maintenanceSvc.getMongoDbEolVersionUpgradeTarget(
              _group, cluster, phasedVersionWithMultipleTargetVersions);
      assertThat(targetVersionOpt3).isEmpty();
    }
  }

  @Test
  public void testRefreshMongoDbEolVersions_upToDate() throws Exception {
    createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    // Ensure EOL version upgrade maintenance is enabled
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    clusters.forEach(
        c ->
            _maintenanceSvc.refreshMongoDbEolVersions(
                _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG));

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();
    clusterDescriptions.forEach(
        c -> assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
  }

  @Test
  public void testRefreshMongoDbEolVersions_deprecatedVersion_withinMaintenanceWindow()
      throws Exception {
    final ClusterDescription clusterDescription = createProvisionedCluster();

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // Update cluster to a deprecated MongoDB version
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    clusters.forEach(
        c ->
            _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
                _ndsGroup.getGroupId(),
                c.getClusterDescription().getName(),
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED));

    // Create EOL phased version with newer target versions
    final String phasedVersionTargetVersion =
        String.join(
            ",",
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion());
    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(SoftwareType.MONGODB_EOL_VERSION, phasedVersionTargetVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));

    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    // Ensure EOL version upgrade maintenance is enabled
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    // Update org with in an ACCEPTED EOL extension but is in the past
    final Date extensionDateInThePast = DateUtils.addDays(new Date(), -1);
    final MongoDbVersionEolExtension inactiveExtension =
        new MongoDbVersionEolExtension(
            _organization.getId(),
            NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString(),
            MongoDbVersionEolExtensionStatus.ACCEPTED,
            extensionDateInThePast.getTime());
    _organizationDao.setMongoDBVersionEolExtensionState(List.of(inactiveExtension));

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final Date publicationDate = _currentTime;

    final MaintenanceCheckResult maintenanceCheckResult =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshMongoDbEolVersions(
                    _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG));

    // Verify maintenance is needed
    assertTrue(maintenanceCheckResult.needMaintenance());
    assertEquals(
        SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
        maintenanceCheckResult.getMaintenanceRelease().getSchedulingBehavior());
    assertEquals(
        NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getVersion(),
        maintenanceCheckResult.getFromVersion());
    assertEquals(
        NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
        maintenanceCheckResult.getToVersion());
    assertEquals(
        MaintenanceType.MONGODB_EOL_VERSION_UPGRADE, maintenanceCheckResult.maintenanceType());

    // Verify EOL version upgrade history is created
    final List<EolVersionUpgradeHistory> histories =
        _eolVersionUpgradeHistoryDao.findAll(
            _ndsGroup.getGroupId(),
            cluster.getClusterDescription().getUniqueId(),
            EolVersionUpgradeHistory.Status.STARTED);
    assertEquals(1, histories.size());
    final EolVersionUpgradeHistory history = histories.get(0);
    assertEquals(EolVersionUpgradeHistory.Status.STARTED, history.status());
    assertEquals(EolVersionUpgradeHistory.UpgradeType.CONSECUTIVE_VERSION, history.upgradeType());
    assertEquals(
        NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getVersion(), history.fromVersion());
    assertEquals(NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(), history.toVersion());
    assertEquals(
        NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion(), history.sequenceFinalVersion());

    // Verify cluster is marked for publish
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(updatedClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongoDbEolVersions_consecutiveVersionUpgrade_withinMaintenanceWindow()
      throws Exception {
    final ClusterDescription clusterDescription = createProvisionedCluster();

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // Simulate scenario where cluster has been previously upgraded from a deprecated version
    // to MONGODB_VERSION_CURRENT and is still undergoing a consecutive version upgrade
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    clusters.forEach(
        c ->
            _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
                _ndsGroup.getGroupId(),
                c.getClusterDescription().getName(),
                NDSModelTestFactory.MONGODB_VERSION_CURRENT));

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();

    // Create EOL upgrade history record for the cluster (completed 48 hours ago)
    _eolVersionUpgradeHistoryDao.save(
        EolVersionUpgradeHistory.builder()
            .groupId(_ndsGroup.getGroupId())
            .orgId(_organization.getId())
            .clusterUniqueId(cluster.getClusterDescription().getUniqueId())
            .clusterName(clusterDescription.getName())
            .status(EolVersionUpgradeHistory.Status.COMPLETED)
            .upgradeType(EolVersionUpgradeHistory.UpgradeType.CONSECUTIVE_VERSION)
            .fromVersion(NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getVersion())
            .toVersion(NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion())
            .startedDate(DateUtils.addDays(new Date(), -2))
            .completedDate(DateUtils.addDays(new Date(), -2))
            .sequenceFinalVersion(NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion())
            .build());

    // Create EOL phased version with newer target versions
    final String phasedVersionTargetVersion =
        String.join(
            ",",
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion());
    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(SoftwareType.MONGODB_EOL_VERSION, phasedVersionTargetVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));

    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    // Ensure EOL version upgrade maintenance is enabled
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    // Update org with in an ACCEPTED EOL extension but is in the past
    final Date extensionDateInThePast = DateUtils.addDays(new Date(), -1);
    final MongoDbVersionEolExtension inactiveExtension =
        new MongoDbVersionEolExtension(
            _organization.getId(),
            NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString(),
            MongoDbVersionEolExtensionStatus.ACCEPTED,
            extensionDateInThePast.getTime());
    _organizationDao.setMongoDBVersionEolExtensionState(List.of(inactiveExtension));

    final MaintenanceCheckResult maintenanceCheckResult =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshMongoDbEolVersions(
                    _ndsGroup, _group, cluster, _currentTime, _numMonitoringAddedMap, LOG));

    // Verify maintenance is needed
    assertTrue(maintenanceCheckResult.needMaintenance());
    assertEquals(
        SchedulingBehavior.DURING_MAINTENANCE_WINDOW,
        maintenanceCheckResult.getMaintenanceRelease().getSchedulingBehavior());
    assertEquals(
        NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
        maintenanceCheckResult.getFromVersion());
    assertEquals(
        NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion(),
        maintenanceCheckResult.getToVersion());
    assertEquals(
        MaintenanceType.MONGODB_EOL_VERSION_UPGRADE, maintenanceCheckResult.maintenanceType());

    // Verify EOL version upgrade history is created
    final List<EolVersionUpgradeHistory> histories =
        _eolVersionUpgradeHistoryDao.findAll(
            _ndsGroup.getGroupId(),
            cluster.getClusterDescription().getUniqueId(),
            EolVersionUpgradeHistory.Status.STARTED);
    assertEquals(1, histories.size());
    final EolVersionUpgradeHistory history = histories.get(0);
    assertEquals(EolVersionUpgradeHistory.Status.STARTED, history.status());
    assertEquals(EolVersionUpgradeHistory.UpgradeType.CONSECUTIVE_VERSION, history.upgradeType());
    assertEquals(NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(), history.fromVersion());
    assertEquals(NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion(), history.toVersion());
    assertEquals(
        NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion(), history.sequenceFinalVersion());

    // Verify cluster is marked for publish
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(updatedClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongoDbEolVersions_deprecatedVersion_featureFlagDisabled()
      throws Exception {
    final ClusterDescription clusterDescription = createProvisionedCluster();

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // Update cluster to a deprecated MongoDB version
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    clusters.forEach(
        c ->
            _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
                _ndsGroup.getGroupId(),
                c.getClusterDescription().getName(),
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED));

    // Create EOL phased version with newer target versions
    final String phasedVersionTargetVersion =
        String.join(
            ",",
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion());
    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(SoftwareType.MONGODB_EOL_VERSION, phasedVersionTargetVersion);
    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    // Ensure EOL version upgrade maintenance is DISABLED
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final Date publicationDate = _currentTime;

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongoDbEolVersions(
            _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG);

    // Verify maintenance is NOT needed when feature flag is disabled
    assertFalse(maintenanceCheckResult.needMaintenance());

    // Verify cluster is NOT marked for publish
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(updatedClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongoDbEolVersions_deprecatedVersion_withActiveExtensionDate()
      throws Exception {
    final ClusterDescription clusterDescription = createProvisionedCluster();

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // Update cluster to a deprecated MongoDB version
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    clusters.forEach(
        c ->
            _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
                _ndsGroup.getGroupId(),
                c.getClusterDescription().getName(),
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED));

    // Create EOL phased version with newer target versions
    final String phasedVersionTargetVersion =
        String.join(
            ",",
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion());
    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(SoftwareType.MONGODB_EOL_VERSION, phasedVersionTargetVersion);
    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    // Enable feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    // Update organization with an active EOL extension date
    final Date extensionDateInTheFuture = DateUtils.addDays(new Date(), 1);
    final MongoDbVersionEolExtension activeExtension =
        new MongoDbVersionEolExtension(
            _organization.getId(),
            NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString(),
            MongoDbVersionEolExtensionStatus.ACCEPTED,
            extensionDateInTheFuture.getTime());
    _organizationDao.setMongoDBVersionEolExtensionState(List.of(activeExtension));

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final Date publicationDate = _currentTime;

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongoDbEolVersions(
            _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG);

    // Verify maintenance is NOT needed when org has extension date
    assertFalse(maintenanceCheckResult.needMaintenance());

    // Verify cluster is NOT marked for publish
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(updatedClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongoDbEolVersions_fixedVersion() throws Exception {
    final ClusterDescription clusterDescription = createProvisionedCluster_fixedVersion();

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // Update cluster to a deprecated MongoDB version
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    clusters.forEach(
        c ->
            _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
                _ndsGroup.getGroupId(),
                c.getClusterDescription().getName(),
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED));

    // Create EOL phased version with newer target versions
    final String phasedVersionTargetVersion =
        String.join(
            ",",
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion());
    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(SoftwareType.MONGODB_EOL_VERSION, phasedVersionTargetVersion);
    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    // Enable feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final Date publicationDate = _currentTime;

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongoDbEolVersions(
            _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG);

    // Verify maintenance is NOT needed when version is fixed
    assertFalse(maintenanceCheckResult.needMaintenance());

    // Verify cluster is NOT marked for publish
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(updatedClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongoDbEolVersions_notInReleaseCohort() throws Exception {
    final ClusterDescription clusterDescription = createProvisionedCluster();

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    // Group not in release cohort
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(71, 65));
    setMaintenanceWindowToNow(_ndsGroup);

    // Update cluster to a deprecated MongoDB version
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    clusters.forEach(
        c ->
            _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
                _ndsGroup.getGroupId(),
                c.getClusterDescription().getName(),
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED));

    // Create EOL phased version with newer target versions
    final String phasedVersionTargetVersion =
        String.join(
            ",",
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion());
    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(SoftwareType.MONGODB_EOL_VERSION, phasedVersionTargetVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(70, 0, null, null, null, null));

    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    // Enable feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final Date publicationDate = _currentTime;

    final MaintenanceCheckResult maintenanceCheckResult =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshMongoDbEolVersions(
                    _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG));

    // Verify maintenance is NOT needed
    assertFalse(maintenanceCheckResult.needMaintenance());

    // Verify cluster is NOT marked for publish
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(updatedClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void
      testRefreshMongoDbVersions_doesNotRunWhenMongoDBEolVersionUpdated_maintenanceInProgress()
          throws Exception {
    final ClusterDescription clusterDescription = createProvisionedCluster();

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // Update cluster to a deprecated MongoDB version
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    clusters.forEach(
        c ->
            _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
                _ndsGroup.getGroupId(),
                c.getClusterDescription().getName(),
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED));

    // Create EOL phased version with newer target versions
    final String phasedVersionTargetVersion =
        String.join(
            ",",
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion());
    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(SoftwareType.MONGODB_EOL_VERSION, phasedVersionTargetVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));

    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    // Create a maintenance history record for MONGODB_VERSION_UPDATED maintenance type that is in
    // progress
    final NdsMaintenanceHistory conflictingMaintenanceHistory =
        NdsMaintenanceHistory.builder()
            .setGroupId(_ndsGroup.getGroupId())
            .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
            .setState(NdsMaintenanceHistory.State.PENDING)
            .setCreatedAt(DateUtils.addMinutes(_currentTime, -5))
            .setMaintenanceStartedDate(DateUtils.addMinutes(_currentTime, -5))
            .setTargetMaintenanceDate(DateUtils.addMinutes(_currentTime, -5))
            .setClusterName(clusterDescription.getName())
            .build();
    _ndsMaintenanceHistorySvc.savePendingMaintenances(List.of(conflictingMaintenanceHistory));

    // Enable feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final Date publicationDate = _currentTime;

    final MaintenanceCheckResult maintenanceCheckResult =
        runMaintenanceSavingHistory(
            () ->
                _maintenanceSvc.refreshMongoDbEolVersions(
                    _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG));

    // Verify maintenance is NOT needed
    assertFalse(maintenanceCheckResult.needMaintenance());

    // Verify cluster is NOT marked for publish
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(updatedClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongoDBVersions_UpToDate() throws Exception {
    createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c ->
            _maintenanceSvc.refreshMongoDBVersions(
                _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG));

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    clusterDescriptions.forEach(
        c -> assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
  }

  @Test
  public void testRefreshMongoDBVersions_addMonitoring() throws Exception {
    createProvisionedCluster();
    createProvisionedCluster_withVersion("7.0", "7.0.12", false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final PhasedVersion phasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0.999"));
    _phasedVersionSvc.save(phasedVersion);
    setPhasedVersionToAutoRelease(SoftwareType.MONGODB_8_0);

    final PhasedVersion otherPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_7_0, "7.0.999"));
    _phasedVersionSvc.save(otherPhasedVersion);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    // case 0: when EHM app prop is disabled -> _numMonitoringAddedMap should be empty
    clusters.forEach(
        c ->
            _maintenanceSvc.refreshMongoDBVersions(
                _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG));

    assertEquals(0, _numMonitoringAddedMap.size());

    // case 1: when internal maintenance doc is not found -> _numMonitoringAddedMap should be empty
    _appSettings.setProp(NDS_EHM_EXTENSION_ENABLED, "true", SettingType.MEMORY);
    clusters.forEach(
        c ->
            _maintenanceSvc.refreshMongoDBVersions(
                _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG));

    assertEquals(0, _numMonitoringAddedMap.size());

    //  case 2: when EHM doc is absent -> _numMonitoringAddedMap should have 2 elements with
    // actionIds but the values should be 0
    final NDSInternalMaintenanceRollout internalMaintenance =
        _internalMaintenanceRolloutSvc
            .saveFromPhasedVersion(phasedVersion, MaintenanceType.MONGODB_VERSION_UPDATED, LOG)
            .orElseThrow();
    final NDSInternalMaintenanceRollout otherInternalMaintenance =
        _internalMaintenanceRolloutSvc
            .saveFromPhasedVersion(otherPhasedVersion, MaintenanceType.MONGODB_VERSION_UPDATED, LOG)
            .orElseThrow();
    clusters.forEach(
        c ->
            _maintenanceSvc.refreshMongoDBVersions(
                _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG));

    assertTrue(_numMonitoringAddedMap.isEmpty());

    // case 3: when the internal maintenance & EHM docs are present -> _numMonitoringAddedMap should
    // have 2 elements
    final MonitoringThresholds monitoringThresholds = getMonitoringThresholds();
    _internalMaintenanceRolloutSvc.addElevatedHealthMonitoring(
        internalMaintenance, monitoringThresholds);
    _internalMaintenanceRolloutSvc.addElevatedHealthMonitoring(
        otherInternalMaintenance, monitoringThresholds);
    // Set up health monitoring data for the action IDs so getBaselineDataForCluster returns data
    _ndsClusterSpotHealthCheckTestUtil.setupBaselineDataForActions(
        _ndsGroup,
        List.of(internalMaintenance.getActionId(), otherInternalMaintenance.getActionId()),
        true);

    clusters.forEach(
        c ->
            runMaintenanceSavingHistory(
                () ->
                    _maintenanceSvc.refreshMongoDBVersions(
                        _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG)));

    assertEquals(2, _numMonitoringAddedMap.size());
    assertEquals(1, _numMonitoringAddedMap.get(internalMaintenance.getActionId()).intValue());
    assertEquals(2, _numMonitoringAddedMap.get(otherInternalMaintenance.getActionId()).intValue());

    final NDSGroup updatedNDSGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
    final List<ActionToMonitor> actionToMonitorList =
        updatedNDSGroup.getHealthCheckMetadata().getActionsToMonitor();
    assertEquals(internalMaintenance.getActionId(), actionToMonitorList.get(0).getActionId());
    assertEquals(otherInternalMaintenance.getActionId(), actionToMonitorList.get(1).getActionId());
  }

  @Test
  public void testRefreshMongoDBVersions_OutOfDate_WithinMaintenanceWindow() throws Exception {
    createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);
    final String defaultMongodbVersion =
        _clusterSvc.getDefaultMongoDBVersionForOrg(_organization.getId());

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0.999")));
    setPhasedVersionToAutoRelease(SoftwareType.MONGODB_8_0);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    final List<MaintenanceCheckResult> maintenanceCheckResults =
        clusters.stream()
            .map(
                c ->
                    runMaintenanceSavingHistory(
                        () ->
                            _maintenanceSvc.refreshMongoDBVersions(
                                _ndsGroup,
                                _group,
                                c,
                                publicationDate,
                                _numMonitoringAddedMap,
                                LOG)))
            .toList();

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();
    clusterDescriptions.forEach(c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));

    assertEquals(1, maintenanceCheckResults.size());
    assertTrue(maintenanceCheckResults.get(0).isStarted());
    assertTrue(maintenanceCheckResults.get(0).needMaintenance());
    assertFalse(maintenanceCheckResults.get(0).isCriticalRelease());
    assertEquals(defaultMongodbVersion, maintenanceCheckResults.get(0).getFromVersion());
    assertEquals("8.0.999", maintenanceCheckResults.get(0).getToVersion());
  }

  @Test
  public void testRefreshMongoDBVersions_OutOfDate_outMaintenanceWindow() throws Exception {
    createProvisionedCluster();
    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0.999")));
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, getNextCDPatchVersion())));
    setPhasedVersionToAutoRelease(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);
    setPhasedVersionToAutoRelease(SoftwareType.MONGODB_8_0);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c ->
            _maintenanceSvc.refreshMongoDBVersions(
                _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG));

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    clusterDescriptions.forEach(
        c -> assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
  }

  @Test
  public void testRefreshMongoDBVersions_OutOfDate_CriticalRelease_outMaintenanceWindow()
      throws Exception {
    createProvisionedCluster();
    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0.999")));
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, getNextCDPatchVersion())));
    setPhasedVersionToCriticalRelease(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);
    setPhasedVersionToCriticalRelease(SoftwareType.MONGODB_8_0);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c ->
            _maintenanceSvc.refreshMongoDBVersions(
                _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG));

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    clusterDescriptions.forEach(c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
  }

  @Test
  public void testRefreshMongoDBVersions_fixedVersion() throws Exception {
    createProvisionedCluster_fixedVersion();
    createProvisionedCluster_withCDVersion(true);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0.999")));
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, getNextCDPatchVersion())));

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c ->
            _maintenanceSvc.refreshMongoDBVersions(
                _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG));

    final List<ClusterDescription> refreshedClusters =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    refreshedClusters.forEach(c -> assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
  }

  @Test
  public void testRefreshMongoDBVersions_previousVersion_outOfCohort_withinMaintenanceWindow()
      throws Exception {
    createProvisionedCluster(
        Collections.singletonList(getReplicationSpec(1, 3)),
        null,
        ClusterDescription.ClusterType.REPLICASET,
        "8.0",
        "8.0.2",
        false,
        true,
        VersionReleaseSystem.LTS);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final String targetNextVersion = "8.0.999";
    final String targetPreviousVersion = "8.0.8";

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_8_0, targetPreviousVersion)
                .setIsPreviousVersionSafe(true)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.MONGODB_8_0, targetNextVersion);

    final PhasedVersion phasedVersion =
        _phasedVersionSvc.getCachedVersionForSoftwareTypes().get(SoftwareType.MONGODB_8_0);

    final List<MaintenanceCheckResult> maintenanceCheckResults =
        _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId()).stream()
            .map(
                c ->
                    runMaintenanceSavingHistory(
                        () ->
                            _maintenanceSvc.refreshMongoDBVersions(
                                _ndsGroup, _group, c, _currentTime, _numMonitoringAddedMap, LOG)))
            .toList();

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    assertThat(phasedVersion.getTargetVersion()).isEqualTo(targetNextVersion);
    assertThat(phasedVersion.getPreviousVersion()).hasValue(targetPreviousVersion);
    assertThat(NDSGroupMaintenanceSvc.isInVersionReleaseCohort(_ndsGroup, phasedVersion)).isFalse();
    assertThat(clusterDescriptions)
        .hasSize(1)
        .allSatisfy(
            c -> {
              assertThat(c.getNeedsMongoDBConfigPublishAfter()).isPresent();
              assertThat(c.getMongoDBVersion().getVersion()).isEqualTo(targetPreviousVersion);
            });

    assertThat(maintenanceCheckResults)
        .hasSize(1)
        .allSatisfy(
            m -> {
              assertThat(m.needMaintenance()).isTrue();
              assertThat(m.isCriticalRelease()).isFalse();
              assertThat(m.getFromVersion())
                  .isEqualTo(m.clusterDescription().getMongoDBVersion().getVersion());
              assertThat(m.getToVersion())
                  .isEqualTo(phasedVersion.getPreviousVersion().orElseThrow());
            });
  }

  @Test
  public void
      testRefreshMongoDBVersions_previousVersion_isSafe_false_outOfCohort_withinMaintenanceWindow()
          throws Exception {
    createProvisionedCluster(
        Collections.singletonList(getReplicationSpec(1, 3)),
        null,
        ClusterDescription.ClusterType.REPLICASET,
        "8.0",
        "8.0.0",
        false,
        true,
        VersionReleaseSystem.LTS);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final String targetNextVersion = "8.0.8";
    final String targetPreviousVersion = "8.0.7";

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_8_0, targetPreviousVersion)
                .setIsPreviousVersionSafe(false)
                .setReleaseMode(ReleaseMode.PHASED)));
    setPhasedTargetVersion(SoftwareType.MONGODB_8_0, targetNextVersion);

    final List<MaintenanceCheckResult> maintenanceCheckResults =
        _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId()).stream()
            .map(
                c ->
                    _maintenanceSvc.refreshMongoDBVersions(
                        _ndsGroup, _group, c, _currentTime, _numMonitoringAddedMap, LOG))
            .toList();

    assertThat(maintenanceCheckResults)
        .hasSize(1)
        .allSatisfy(
            m -> {
              assertThat(m.needMaintenance()).isFalse();
              assertThat(m.isCriticalRelease()).isFalse();
              assertThat(m.getFromVersion()).isNull();
              assertThat(m.getToVersion()).isNull();
            });
  }

  @Test
  public void testRefreshMongoDBVersions_eolMaintenanceFFEnabled_noPatchVersionUpgradeRequired()
      throws Exception {
    final ClusterDescription clusterDescription = createProvisionedCluster();

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // Get phased version for latest deprecated major version
    final PhasedVersion phasedVersionForDeprecatedMajorVersion =
        _phasedVersionSvc
            .getCachedVersionForSoftwareTypes()
            .get(
                VALID_MONGODB_MAJOR_VERSIONS.get(
                    NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString()));

    // Update cluster to the latest patch version of the deprecated MongoDB major version
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    clusters.forEach(
        c ->
            _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
                _ndsGroup.getGroupId(),
                c.getClusterDescription().getName(),
                VersionUtils.parse(phasedVersionForDeprecatedMajorVersion.getTargetVersion())));

    // Create EOL phased version with newer target versions
    final String targetVersion =
        String.join(
            ",",
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion());
    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(SoftwareType.MONGODB_EOL_VERSION, targetVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));

    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    // Enable feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final Date publicationDate = _currentTime;

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongoDBVersions(
            _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG);

    // Verify maintenance is NOT needed because EOL version maintenance FF is enabled
    // and there is no patch version upgrade required
    assertFalse(maintenanceCheckResult.needMaintenance());

    // Verify cluster is NOT marked for publish
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(updatedClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongoDBVersions_eolMaintenanceFFEnabled_inMaintenanceWindow()
      throws Exception {
    final ClusterDescription clusterDescription = createProvisionedCluster();

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // Update cluster to a deprecated MongoDB version
    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    clusters.forEach(
        c ->
            _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
                _ndsGroup.getGroupId(),
                c.getClusterDescription().getName(),
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED));

    // Create EOL phased version with newer target versions
    final String targetVersion =
        String.join(
            ",",
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getVersion());
    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(SoftwareType.MONGODB_EOL_VERSION, targetVersion)
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 0, null, null, null, null));

    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    // Enable feature flag
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE);

    final Cluster cluster =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), clusterDescription.getName()).get();
    final Date publicationDate = _currentTime;

    final MaintenanceCheckResult maintenanceCheckResult =
        _maintenanceSvc.refreshMongoDBVersions(
            _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG);

    // Verify maintenance is needed
    assertTrue(maintenanceCheckResult.needMaintenance());
    assertEquals(MaintenanceType.MONGODB_VERSION_UPDATED, maintenanceCheckResult.maintenanceType());

    // Verify cluster is marked for publish
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(updatedClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshMongoDBFeatureCompatibilityVersion() throws Exception {
    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c -> {
          _clusterDescriptionDao.updateContinuousDeliveryFCV(
              _ndsGroup.getGroupId(), c.getClusterDescription().getName(), "5.0");
        });

    final Version cdVersion = new Version(_clusterSvc.getDefaultCDMongoDBVersion());
    final List<Cluster> updatedClusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    updatedClusters.forEach(
        c -> {
          assertTrue(c.getClusterDescription().getContinuousDeliveryFCV().isPresent());
          final MaintenanceCheckResult maintenanceCheckResult =
              _maintenanceSvc.refreshMongoDBFeatureCompatibilityVersion(
                  _ndsGroup, _group, c, publicationDate, new HashMap<>(), LOG);
          assertTrue(maintenanceCheckResult.isCriticalRelease());
          assertEquals("5.0", maintenanceCheckResult.getFromVersion());
          assertEquals(cdVersion.getMajorVersionString(), maintenanceCheckResult.getToVersion());
        });

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());

    clusterDescriptions.forEach(
        c -> {
          assertTrue(c.getContinuousDeliveryFCV().isPresent());
          assertNotEquals("5.0", c.getContinuousDeliveryFCV().get());
          assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        });
  }

  @Test
  public void testRefreshMongoDBVersions_EOLVersionUpgrade() throws Exception {
    createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    // test assumptions
    assertEquals(
        NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString(),
        _appSettings.getStrProp("mms.versionToDeprecate"));
    assertTrue(
        VersionUtils.isValidVersionChange(
            NDSModelTestFactory.MONGODB_VERSION_DEPRECATED,
            NDSModelTestFactory.MONGODB_VERSION_CURRENT));

    clusters.forEach(
        c -> {
          _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
              _ndsGroup.getGroupId(),
              c.getClusterDescription().getName(),
              NDSModelTestFactory.MONGODB_VERSION_DEPRECATED);
        });

    final PhasedVersionParameters eolPhasedVersionParams =
        new PhasedVersionParameters(
            SoftwareType.MONGODB_EOL_VERSION,
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion());
    final MongoDBEOLPhasedVersion eolPhasedVersion =
        (MongoDBEOLPhasedVersion) _phasedVersionFactory.createPhasedVersion(eolPhasedVersionParams);
    _phasedVersionSvc.save(eolPhasedVersion);

    final SoftwareType targetPhasedVersionSoftwareType =
        PhasedVersion.getSoftwareType(
            NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString());
    assertNotNull(targetPhasedVersionSoftwareType);
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                targetPhasedVersionSoftwareType,
                NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getVersion())));

    final List<Cluster> updatedClusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    updatedClusters.forEach(
        c -> {
          final MaintenanceCheckResult maintenanceCheckResult =
              _maintenanceSvc.refreshMongoDBVersions(
                  _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG);
          assertTrue(maintenanceCheckResult.needMaintenance());
          assertTrue(maintenanceCheckResult.isCriticalRelease());
          assertEquals(
              NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getVersion(),
              maintenanceCheckResult.getFromVersion());
          assertEquals(
              NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
              maintenanceCheckResult.getToVersion());
        });

    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    clusterDescriptions.forEach(
        c -> {
          assertNotNull(c.getMongoDBVersion());

          assertNotEquals(
              NDSModelTestFactory.MONGODB_VERSION_DEPRECATED.getMajorVersionString(),
              c.getMongoDBMajorVersion());
          assertEquals(
              NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
              c.getMongoDBMajorVersion());
          assertEquals(
              NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
              c.getMongoDBVersion().getVersion());

          assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        });
  }

  @Test
  public void testRefreshMongoDBFeatureCompatibilityVersion_PausedVersion() throws Exception {
    // Paused FCV version
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.CONTINUOUS_DELIVERY_FCV, "7.2").setIsPaused(true);
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c -> {
          _clusterDescriptionDao.updateContinuousDeliveryFCV(
              _ndsGroup.getGroupId(), c.getClusterDescription().getName(), "7.1");
        });

    final List<Cluster> updatedClusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    updatedClusters.forEach(
        c -> {
          assertTrue(c.getClusterDescription().getContinuousDeliveryFCV().isPresent());
          final MaintenanceCheckResult maintenanceCheckResult =
              _maintenanceSvc.refreshMongoDBFeatureCompatibilityVersion(
                  _ndsGroup, _group, c, publicationDate, new HashMap<>(), LOG);
          assertFalse(maintenanceCheckResult.needMaintenance());
        });

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());

    clusterDescriptions.forEach(
        c -> {
          assertTrue(c.getContinuousDeliveryFCV().isPresent());
          assertEquals("7.1", c.getContinuousDeliveryFCV().get());
          assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        });
  }

  @Test
  public void testRefreshMongoDBFeatureCompatibilityVersion_EligibleCohort() throws Exception {
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, "7.2.1")));
    // FCV version percent larger than the group's cohort values.
    final PhasedReleaseCriteria phasedReleaseCriteria =
        new PhasedReleaseCriteria(70, 70, null, null, null, null);
    final PhasedVersionParameters fcvPhasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.CONTINUOUS_DELIVERY_FCV, "7.2")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(phasedReleaseCriteria);
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(fcvPhasedVersionParameters));
    createProvisionedCluster_withCDVersion(false);
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(10, 10));
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c -> {
          _clusterDescriptionDao.updateContinuousDeliveryFCV(
              _ndsGroup.getGroupId(), c.getClusterDescription().getName(), "7.1");
        });

    final List<Cluster> updatedClusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    updatedClusters.forEach(
        c -> {
          assertTrue(c.getClusterDescription().getContinuousDeliveryFCV().isPresent());
          final MaintenanceCheckResult maintenanceCheckResult =
              runMaintenanceSavingHistory(
                  () ->
                      _maintenanceSvc.refreshMongoDBFeatureCompatibilityVersion(
                          _ndsGroup, _group, c, publicationDate, new HashMap<>(), LOG));

          assertTrue(maintenanceCheckResult.needMaintenance());
          assertFalse(maintenanceCheckResult.isCriticalRelease());
          assertEquals("7.1", maintenanceCheckResult.getFromVersion());
          assertEquals("7.2", maintenanceCheckResult.getToVersion());
        });

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());

    clusterDescriptions.forEach(
        c -> {
          assertTrue(c.getContinuousDeliveryFCV().isPresent());
          assertEquals("7.2", c.getContinuousDeliveryFCV().get());
          assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        });
  }

  @Test
  public void testRefreshMongoDBFeatureCompatibilityVersion_IneligibleCohort() throws Exception {
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, "7.2.1")));
    // FCV version percent larger than the group's cohort values.
    final PhasedReleaseCriteria phasedReleaseCriteria =
        new PhasedReleaseCriteria(50, 50, null, null, null, null);
    final PhasedVersionParameters fcvPhasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.CONTINUOUS_DELIVERY_FCV, "7.2")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(phasedReleaseCriteria);
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(fcvPhasedVersionParameters));

    createProvisionedCluster_withCDVersion(false);
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(51, 51));
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c -> {
          _clusterDescriptionDao.updateContinuousDeliveryFCV(
              _ndsGroup.getGroupId(), c.getClusterDescription().getName(), "7.1");
        });

    final List<Cluster> updatedClusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    updatedClusters.forEach(
        c -> {
          assertTrue(c.getClusterDescription().getContinuousDeliveryFCV().isPresent());
          final MaintenanceCheckResult maintenanceCheckResult =
              _maintenanceSvc.refreshMongoDBFeatureCompatibilityVersion(
                  _ndsGroup, _group, c, publicationDate, new HashMap<>(), LOG);
          assertFalse(maintenanceCheckResult.needMaintenance());
        });

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());

    clusterDescriptions.forEach(
        c -> {
          assertTrue(c.getContinuousDeliveryFCV().isPresent());
          assertEquals("7.1", c.getContinuousDeliveryFCV().get());
        });
  }

  @Test
  public void testRefreshMongoDBFeatureCompatibilityVersion_LTStoLatestDuringInterimPeriod()
      throws Exception {
    // The interim period is one where we introduce a new quarterly version binary but still
    // have the FCV set to the previous quarterly version (5.2.0 / FCV 5.1 as an example)
    // When a cluster upgrades from LTS to latest quarterly release during this interim period
    // we have to retain their FCV on LTS so in the above example FCV must remain 5.0

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, "5.2.0")));
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.CONTINUOUS_DELIVERY_FCV, "5.1")));

    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c -> {
          _clusterDescriptionDao.updateContinuousDeliveryFCV(
              _ndsGroup.getGroupId(), c.getClusterDescription().getName(), "5.0");
        });

    final List<Cluster> updatedClusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    updatedClusters.forEach(
        c -> {
          assertTrue(c.getClusterDescription().getContinuousDeliveryFCV().isPresent());
          _maintenanceSvc.refreshMongoDBFeatureCompatibilityVersion(
              _ndsGroup, _group, c, publicationDate, new HashMap<>(), LOG);
        });

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());

    clusterDescriptions.forEach(
        c -> {
          assertTrue(c.getContinuousDeliveryFCV().isPresent());
          assertEquals("5.0", c.getContinuousDeliveryFCV().get());
          assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        });
  }

  @Test
  public void testRefreshMongoDBFeatureCompatibilityVersion_outMaintenanceWindow()
      throws Exception {
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.CONTINUOUS_DELIVERY_FCV, "5.2")
            .setReleaseMode(ReleaseMode.AUTOMATIC);
    final PhasedVersion fiveZeroPhasedVersion =
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters);
    _phasedVersionSvc.save(fiveZeroPhasedVersion);

    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToAnotherDay(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c -> {
          _clusterDescriptionDao.updateContinuousDeliveryFCV(
              _ndsGroup.getGroupId(), c.getClusterDescription().getName(), "4.4");
        });

    final List<Cluster> updatedClusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    updatedClusters.forEach(
        c -> {
          assertTrue(c.getClusterDescription().getContinuousDeliveryFCV().isPresent());
          _maintenanceSvc.refreshMongoDBFeatureCompatibilityVersion(
              _ndsGroup, _group, c, publicationDate, new HashMap<>(), LOG);
        });

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());

    clusterDescriptions.forEach(
        c -> {
          assertTrue(c.getContinuousDeliveryFCV().isPresent());
          assertEquals("4.4", c.getContinuousDeliveryFCV().get());
          assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        });
  }

  @Test
  public void testRefreshTenantMongoDBFeatureCompatibilityVersion() throws Exception {
    final String originalFCV = "5.0";
    final String newFCV = "5.1";

    final ObjectId mtmGroupId = new ObjectId();
    _groupSvc.ensureGroup(mtmGroupId);

    final ClusterDescription m0MtmClusterDesc =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(mtmGroupId, "m0Mtm"))
            .copy()
            .setContinuousDeliveryFCV(originalFCV)
            .setState(ClusterDescription.State.IDLE)
            .build();

    final ClusterDescription serverlessMtmClusterDesc =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(mtmGroupId, "serverlessMtm"))
            .copy()
            .setContinuousDeliveryFCV(originalFCV)
            .setState(ClusterDescription.State.IDLE)
            .build();
    _clusterDescriptionDao.save(m0MtmClusterDesc);
    _clusterDescriptionDao.save(serverlessMtmClusterDesc);

    final ObjectId tenantGroupId = _group.getId();
    final ClusterDescription m0Tenant =
        new ClusterDescription(NDSModelTestFactory.getFreeClusterDescription())
            .copy()
            .setGroupId(tenantGroupId)
            .setName("m0Tenant1")
            .setContinuousDeliveryFCV(m0MtmClusterDesc.getContinuousDeliveryFCV().get())
            .setState(ClusterDescription.State.IDLE)
            .build();
    final ClusterDescription serverlessTenant =
        new ClusterDescription(NDSModelTestFactory.getServerlessClusterDescription())
            .copy()
            .setGroupId(tenantGroupId)
            .setName("serverlessTenant1")
            .setContinuousDeliveryFCV(serverlessMtmClusterDesc.getContinuousDeliveryFCV().get())
            .setState(ClusterDescription.State.IDLE)
            .build();
    _clusterDescriptionDao.save(m0Tenant);
    _clusterDescriptionDao.save(serverlessTenant);

    _ndsGroupDao.addCloudContainer(
        tenantGroupId,
        new FreeCloudProviderContainer(
            NDSModelTestFactory.getFreeContainer(
                mtmGroupId, m0MtmClusterDesc.getName(), m0Tenant.getName())));
    _ndsGroupDao.addCloudContainer(
        tenantGroupId,
        new FreeCloudProviderContainer(
            NDSModelTestFactory.getServerlessContainer(
                mtmGroupId, serverlessMtmClusterDesc.getName(), serverlessTenant.getName())));

    // test scenario: refresh the tenants continuous FCVs without upgrading the mtms'
    {
      final NDSGroup tenantGroup = _ndsGroupDao.find(tenantGroupId).get();
      final List<Cluster> tenantClusters = _clusterSvc.getAllActiveClusters(tenantGroupId);
      final Map<String, ClusterDescription> tenant2MtmMap =
          _clusterSvc.getTenantToMtmMap(tenantGroup);
      for (final Cluster tenant : tenantClusters) {
        final MaintenanceCheckResult maintenanceCheckResult =
            _maintenanceSvc.refreshTenantMongoDBFeatureCompatibilityVersion(
                tenant, tenant2MtmMap, LOG);
        assertFalse(maintenanceCheckResult.needMaintenance());

        final ClusterDescription tenantClusterDesc =
            _clusterSvc
                .getActiveClusterDescription(
                    tenant.getClusterDescription().getGroupId(),
                    tenant.getClusterDescription().getUniqueId())
                .get();
        assertEquals(originalFCV, tenantClusterDesc.getContinuousDeliveryFCV().get());
      }
    }

    // test scenario: refresh the tenant's continuous FCVs after the mtms are upgraded,
    // but the mtms are in working state
    {
      _clusterDescriptionDao.save(
          m0MtmClusterDesc
              .copy()
              .setContinuousDeliveryFCV(newFCV)
              .setState(ClusterDescription.State.WORKING)
              .build());
      _clusterDescriptionDao.save(
          serverlessMtmClusterDesc
              .copy()
              .setContinuousDeliveryFCV(newFCV)
              .setState(ClusterDescription.State.WORKING)
              .build());

      final NDSGroup tenantGroup = _ndsGroupDao.find(tenantGroupId).get();
      final List<Cluster> tenantClusters = _clusterSvc.getAllActiveClusters(tenantGroupId);
      final Map<String, ClusterDescription> tenant2MtmMap =
          _clusterSvc.getTenantToMtmMap(tenantGroup);
      for (final Cluster tenant : tenantClusters) {
        final MaintenanceCheckResult maintenanceCheckResult =
            _maintenanceSvc.refreshTenantMongoDBFeatureCompatibilityVersion(
                tenant, tenant2MtmMap, LOG);
        assertFalse(maintenanceCheckResult.needMaintenance());

        final ClusterDescription tenantClusterDesc =
            _clusterSvc
                .getActiveClusterDescription(
                    tenant.getClusterDescription().getGroupId(),
                    tenant.getClusterDescription().getUniqueId())
                .get();
        assertEquals(originalFCV, tenantClusterDesc.getContinuousDeliveryFCV().get());
      }
    }

    // test scenario: refresh the tenant'scontinuous FCVs after the mtms are upgraded,
    // and the mtms are in IDLE state
    {
      _clusterDescriptionDao.save(
          m0MtmClusterDesc
              .copy()
              .setContinuousDeliveryFCV(newFCV)
              .setState(ClusterDescription.State.IDLE)
              .build());
      _clusterDescriptionDao.save(
          serverlessMtmClusterDesc
              .copy()
              .setContinuousDeliveryFCV(newFCV)
              .setState(ClusterDescription.State.IDLE)
              .build());

      final NDSGroup tenantGroup = _ndsGroupDao.find(tenantGroupId).get();
      final List<Cluster> tenantClusters = _clusterSvc.getAllActiveClusters(tenantGroupId);
      final Map<String, ClusterDescription> tenant2MtmMap =
          _clusterSvc.getTenantToMtmMap(tenantGroup);
      for (final Cluster tenant : tenantClusters) {
        final MaintenanceCheckResult maintenanceCheckResult =
            _maintenanceSvc.refreshTenantMongoDBFeatureCompatibilityVersion(
                tenant, tenant2MtmMap, LOG);
        assertFalse(maintenanceCheckResult.needMaintenance());

        final ClusterDescription tenantClusterDesc =
            _clusterSvc
                .getActiveClusterDescription(
                    tenant.getClusterDescription().getGroupId(),
                    tenant.getClusterDescription().getUniqueId())
                .get();
        assertEquals(newFCV, tenantClusterDesc.getContinuousDeliveryFCV().get());
      }
    }
  }

  @Test
  public void testRefreshMongoDBFeatureCompatibilityVersion_fixedFCV() throws Exception {
    final ClusterDescription cd =
        createProvisionedCluster_withCDVersion(false)
            .copy()
            .setFixedFeatureCompatibilityVersion(Optional.of(new FixedVersion("6.0")))
            .setMongoDBVersion("6.0.0")
            .setMongoDBMajorVersion("6.0")
            .setContinuousDeliveryFCV("6.0")
            .build();
    _clusterDescriptionDao.save(cd);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // fix FCV
    _clusterDescriptionDao.fixMongoDBFCVForCluster(
        _ndsGroup.getGroupId(), cd.getName(), "6.0", "Fixing for test", null, new Date(), null);

    final Cluster clusterAfterFcvFix =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), cd.getName()).orElseThrow();
    assertTrue(clusterAfterFcvFix.getClusterDescription().isFeatureCompatibilityVersionFixed());

    // run FCV maintenance and there should be no update required
    final MaintenanceCheckResult maintenanceCheckResult1 =
        _maintenanceSvc.refreshMongoDBFeatureCompatibilityVersion(
            _ndsGroup, _group, clusterAfterFcvFix, _currentTime, new HashMap<>(), LOG);
    assertFalse(maintenanceCheckResult1.needMaintenance());

    // unfix FCV
    _clusterDescriptionDao.unfixMongoDBFCVForCluster(_ndsGroup.getGroupId(), cd.getName());
    final Cluster clusterAfterFcvUnfix =
        _clusterSvc.getActiveCluster(_ndsGroup.getGroupId(), cd.getName()).orElseThrow();
    assertFalse(clusterAfterFcvUnfix.getClusterDescription().isFeatureCompatibilityVersionFixed());

    // run FCV maintenance and there should be an update
    final MaintenanceCheckResult maintenanceCheckResult2 =
        _maintenanceSvc.refreshMongoDBFeatureCompatibilityVersion(
            _ndsGroup, _group, clusterAfterFcvUnfix, _currentTime, new HashMap<>(), LOG);
    assertTrue(maintenanceCheckResult2.needMaintenance());
    assertEquals(
        MaintenanceType.CONTINUOUS_DELIVERY_FCV_UPDATED, maintenanceCheckResult2.maintenanceType());
  }

  @Test
  public void testRefreshMongoDBVersions_MajorVersionUpdate_CDEnabled_WithinMaintenanceWindow()
      throws Exception {
    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final String nextLTSVersion = getNextLTSVersion();
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, nextLTSVersion)));
    setPhasedVersionToAutoRelease(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c ->
            runMaintenanceSavingHistory(
                () ->
                    _maintenanceSvc.refreshMongoDBVersions(
                        _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG)));

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    clusterDescriptions.forEach(c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
    clusterDescriptions.forEach(
        c -> assertEquals(nextLTSVersion.substring(0, 3), c.getMongoDBMajorVersion()));
    clusterDescriptions.forEach(
        c -> assertEquals(nextLTSVersion, c.getMongoDBVersion().getVersion()));
  }

  @Test
  public void testRefreshMongoDBVersions_MajorVersionUpdate_CDEnabled_MTM() throws Exception {
    final Version originalVersion = NDSModelTestFactory.MONGODB_VERSION_CURRENT;
    final Version newVersion =
        VersionUtils.parse(
            String.format(
                "%d.%d.%d",
                originalVersion.getMajor(),
                originalVersion.getMinor() + 1,
                originalVersion.getMaintenance()));
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, originalVersion.getVersion())));
    setPhasedVersionToAutoRelease(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);

    final ClusterDescription clusterDesc =
        createProvisionedCluster(
                Collections.singletonList(getReplicationSpec(1, 3)),
                null,
                ClusterDescription.ClusterType.REPLICASET,
                _clusterSvc.getDefaultCDMongoDBMajorVersion(),
                _clusterSvc.getDefaultCDMongoDBVersion(),
                true,
                false,
                VersionReleaseSystem.CONTINUOUS)
            .copy()
            .setMongoDBMajorVersion(originalVersion.getMajorVersionString())
            .setMongoDBVersion(originalVersion.getVersion())
            .build();
    _clusterDescriptionDao.save(clusterDesc);

    final ServerlessMTMCluster mtmCluster =
        new ServerlessMTMCluster(
                NDSModelTestFactory.getServerlessMTMCluster(
                    clusterDesc.getGroupId(), clusterDesc.getName()))
            .copy()
            .setMongoDBMajorVersion(originalVersion.getMajorVersionString())
            .build();
    _mtmClusterDao.saveReplicaSafe(mtmCluster.toDBObject());

    assertEquals(originalVersion, clusterDesc.getMongoDBVersion());
    assertEquals(originalVersion.getMajorVersionString(), clusterDesc.getMongoDBMajorVersion());
    assertEquals(originalVersion.getMajorVersionString(), mtmCluster.getMongoDBMajorVersion());
    assertFalse(mtmCluster.getMongoDBMajorVersionChangeInProgress());

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // update the C/D target version
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, newVersion.getVersion())));

    final Date publicationDate = _currentTime;
    final Cluster c =
        _clusterSvc.getActiveCluster(clusterDesc.getGroupId(), clusterDesc.getName()).get();
    _maintenanceSvc.refreshMongoDBVersions(
        _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG);

    final ClusterDescription clusterDescUpdated =
        _clusterSvc
            .getActiveClusterDescription(clusterDesc.getGroupId(), clusterDesc.getName())
            .get();

    final ServerlessMTMCluster mtmClusterUpdated =
        _serverlessMTMClusterDao
            .findServerlessCluster(mtmCluster.getGroupId(), mtmCluster.getName())
            .get();

    assertEquals(newVersion, clusterDescUpdated.getMongoDBVersion());
    assertEquals(newVersion.getMajorVersionString(), clusterDescUpdated.getMongoDBMajorVersion());
    assertEquals(newVersion.getMajorVersionString(), mtmClusterUpdated.getMongoDBMajorVersion());
    assertTrue(mtmClusterUpdated.getMongoDBMajorVersionChangeInProgress());
  }

  @Test
  public void testRefreshMongoDBVersions_PatchVersionUpdate_CDEnabled_WithinMaintenanceWindow()
      throws Exception {
    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, getNextCDPatchVersion())));

    setPhasedVersionToAutoRelease(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c ->
            runMaintenanceSavingHistory(
                () ->
                    _maintenanceSvc.refreshMongoDBVersions(
                        _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG)));

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    clusterDescriptions.forEach(c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
    clusterDescriptions.forEach(
        c ->
            assertEquals(
                c.getMongoDBMajorVersion(), _clusterSvc.getDefaultCDMongoDBMajorVersion()));
    clusterDescriptions.forEach(
        c -> assertEquals(c.getMongoDBVersion().getVersion(), getNextCDPatchVersion()));
  }

  @Test
  public void testRefreshMongoDBVersions_QuarterlyVersionUpdate_CDEnabled_WithinMaintenanceWindow()
      throws Exception {
    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final String nextQuarterlyVersion = getNextCDQuarterlyVersion();

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, nextQuarterlyVersion)));

    setPhasedVersionToAutoRelease(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c ->
            runMaintenanceSavingHistory(
                () ->
                    _maintenanceSvc.refreshMongoDBVersions(
                        _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG)));

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();
    clusterDescriptions.forEach(c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
    clusterDescriptions.forEach(
        c -> assertEquals(nextQuarterlyVersion.substring(0, 4), c.getMongoDBMajorVersion()));
    clusterDescriptions.forEach(
        c -> assertEquals(nextQuarterlyVersion, c.getMongoDBVersion().getVersion()));

    // update to rc version
    final String rcVersion = String.format("%s-rc0", nextQuarterlyVersion);
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, rcVersion)));

    setPhasedVersionToAutoRelease(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);

    final List<Cluster> clusters1 = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate1 = _currentTime;

    clusters1.forEach(
        c ->
            runMaintenanceSavingHistory(
                () ->
                    _maintenanceSvc.refreshMongoDBVersions(
                        _ndsGroup, _group, c, publicationDate1, _numMonitoringAddedMap, LOG)));

    final List<ClusterDescription> clusterDescriptions1 =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();
    clusterDescriptions1.forEach(
        c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
    clusterDescriptions1.forEach(
        c -> assertEquals(rcVersion.substring(0, 4), c.getMongoDBMajorVersion()));
    clusterDescriptions1.forEach(c -> assertEquals(rcVersion, c.getMongoDBVersion().getVersion()));
  }

  @Test
  public void testRefreshMongoDBVersions_isInAllowedInstanceSizes_WithinMaintenanceWindow()
      throws Exception {
    testRefreshMongoDBVersions_WithinMaintenanceWindow(true);
  }

  @Test
  public void testRefreshMongoDBVersions_isNotInAllowedInstanceSizes_WithinMaintenanceWindow()
      throws Exception {
    testRefreshMongoDBVersions_WithinMaintenanceWindow(false);
  }

  private void testRefreshMongoDBVersions_WithinMaintenanceWindow(
      final boolean isInAllowedInstanceSizes) throws Exception {
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion("6.0", "6.0.17", false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final String allowedInstanceSize = "M30";
    final String otherInstanceSize = "M20";

    // test assumptions
    Stream.of(NodeType.values())
        .forEach(
            n -> {
              final Set<NDSInstanceSize> instanceSizes = clusterDescription.getInstanceSizes(n);
              if (n == NodeType.ELECTABLE) {
                assertEquals(1, instanceSizes.size());
                assertTrue(instanceSizes.contains(AWSNDSInstanceSize.valueOf(allowedInstanceSize)));
              } else {
                assertEquals(Set.of(), instanceSizes);
              }
            });
    final PhasedReleaseCriteria releaseCriteria;
    if (isInAllowedInstanceSizes) {
      releaseCriteria =
          new PhasedReleaseCriteria(1, 1, null, null, null, List.of(allowedInstanceSize));
    } else {
      releaseCriteria =
          new PhasedReleaseCriteria(1, 1, null, null, null, List.of(otherInstanceSize));
    }
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(1, 1));

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_6_0, "6.0.18")
                .setReleaseMode(ReleaseMode.PHASED)
                .setPhasedReleaseCriteria(releaseCriteria)));

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c ->
            runMaintenanceSavingHistory(
                () ->
                    _maintenanceSvc.refreshMongoDBVersions(
                        _ndsGroupDao.find(_ndsGroup.getGroupId()).get(),
                        _group,
                        c,
                        publicationDate,
                        _numMonitoringAddedMap,
                        LOG)));

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    if (isInAllowedInstanceSizes) {
      assertNotEquals(
          Optional.empty(), clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter());
      assertEquals("6.0", clusterDescriptionUpdated.getMongoDBMajorVersion());
      assertEquals("6.0.18", clusterDescriptionUpdated.getMongoDBVersion().getVersion());
    } else {
      assertEquals(Optional.empty(), clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter());
      assertEquals("6.0", clusterDescriptionUpdated.getMongoDBMajorVersion());
      assertNotEquals("6.0.18", clusterDescriptionUpdated.getMongoDBVersion().getVersion());
    }
  }

  @Test
  public void testRefreshMongoDBVersions_NoVersionUpdate_CDEnabled_WithinMaintenanceWindow()
      throws Exception {
    createProvisionedCluster_withCDVersion(false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<Cluster> clusters = _clusterSvc.getAllActiveClusters(_ndsGroup.getGroupId());
    final Date publicationDate = _currentTime;

    clusters.forEach(
        c ->
            _maintenanceSvc.refreshMongoDBVersions(
                _ndsGroup, _group, c, publicationDate, _numMonitoringAddedMap, LOG));

    final List<ClusterDescription> clusterDescriptions =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    clusterDescriptions.forEach(
        c -> assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
  }

  @Test
  public void testRefreshTenantMongoDBVersions() throws Exception {
    final VersionUtils.Version originalVersion = VersionUtils.FIVE_ZERO_ZERO;
    final VersionUtils.Version newVersion = VersionUtils.FIVE_ZERO_ZERO_RC1;

    final ObjectId mtmGroupId = new ObjectId();
    _groupSvc.ensureGroup(mtmGroupId);

    final ClusterDescription m0MtmClusterDesc =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(mtmGroupId, "m0Mtm"))
            .copy()
            .setMongoDBVersion(originalVersion.getVersion())
            .setMongoDBMajorVersion(originalVersion.getMajorVersionString())
            .setState(ClusterDescription.State.IDLE)
            .build();

    final ClusterDescription serverlessMtmClusterDesc =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(mtmGroupId, "serverlessMtm"))
            .copy()
            .setMongoDBVersion(originalVersion.getVersion())
            .setMongoDBMajorVersion(originalVersion.getMajorVersionString())
            .setState(ClusterDescription.State.IDLE)
            .build();
    _clusterDescriptionDao.save(m0MtmClusterDesc);
    _clusterDescriptionDao.save(serverlessMtmClusterDesc);

    final ObjectId tenantGroupId = _group.getId();
    final ClusterDescription m0Tenant =
        new ClusterDescription(NDSModelTestFactory.getFreeClusterDescription())
            .copy()
            .setGroupId(tenantGroupId)
            .setName("m0Tenant1")
            .setMongoDBVersion(m0MtmClusterDesc.getMongoDBVersion().getVersion())
            .setMongoDBMajorVersion(m0MtmClusterDesc.getMongoDBMajorVersion())
            .setState(ClusterDescription.State.IDLE)
            .build();
    final ClusterDescription serverlessTenant =
        new ClusterDescription(NDSModelTestFactory.getServerlessClusterDescription())
            .copy()
            .setGroupId(tenantGroupId)
            .setName("serverlessTenant1")
            .setMongoDBVersion(m0MtmClusterDesc.getMongoDBVersion().getVersion())
            .setMongoDBMajorVersion(m0MtmClusterDesc.getMongoDBMajorVersion())
            .setState(ClusterDescription.State.IDLE)
            .build();
    _clusterDescriptionDao.save(m0Tenant);
    _clusterDescriptionDao.save(serverlessTenant);

    _ndsGroupDao.addCloudContainer(
        tenantGroupId,
        new FreeCloudProviderContainer(
            NDSModelTestFactory.getFreeContainer(
                mtmGroupId, m0MtmClusterDesc.getName(), m0Tenant.getName())));
    _ndsGroupDao.addCloudContainer(
        tenantGroupId,
        new FreeCloudProviderContainer(
            NDSModelTestFactory.getServerlessContainer(
                mtmGroupId, serverlessMtmClusterDesc.getName(), serverlessTenant.getName())));

    // test scenario: refresh the tenants mongodb versions without upgrading the mtms'
    {
      final NDSGroup tenantGroup = _ndsGroupDao.find(tenantGroupId).get();
      final List<Cluster> tenantClusters = _clusterSvc.getAllActiveClusters(tenantGroupId);
      final Map<String, ClusterDescription> tenant2MtmMap =
          _clusterSvc.getTenantToMtmMap(tenantGroup);
      for (final Cluster tenant : tenantClusters) {
        final MaintenanceCheckResult maintenanceCheckResult =
            _maintenanceSvc.refreshTenantMongoDBVersions(tenant, tenant2MtmMap, LOG);
        assertFalse(maintenanceCheckResult.needMaintenance());

        final ClusterDescription tenantClusterDesc =
            _clusterSvc
                .getActiveClusterDescription(
                    tenant.getClusterDescription().getGroupId(),
                    tenant.getClusterDescription().getUniqueId())
                .get();
        assertEquals(originalVersion, tenantClusterDesc.getMongoDBVersion());
        assertEquals(
            originalVersion.getMajorVersionString(), tenantClusterDesc.getMongoDBMajorVersion());
      }
    }

    // test scenario: refresh the tenants mongodb versions after the mtms are upgraded,
    // but the mtms are in working state
    {
      _clusterDescriptionDao.save(
          m0MtmClusterDesc
              .copy()
              .setMongoDBVersion(newVersion.getVersion())
              .setMongoDBMajorVersion(newVersion.getMajorVersionString())
              .setState(ClusterDescription.State.WORKING)
              .build());
      _clusterDescriptionDao.save(
          serverlessMtmClusterDesc
              .copy()
              .setMongoDBVersion(newVersion.getVersion())
              .setMongoDBMajorVersion(newVersion.getMajorVersionString())
              .setState(ClusterDescription.State.WORKING)
              .build());

      final NDSGroup tenantGroup = _ndsGroupDao.find(tenantGroupId).get();
      final List<Cluster> tenantClusters = _clusterSvc.getAllActiveClusters(tenantGroupId);
      final Map<String, ClusterDescription> tenant2MtmMap =
          _clusterSvc.getTenantToMtmMap(tenantGroup);
      for (final Cluster tenant : tenantClusters) {
        final MaintenanceCheckResult maintenanceCheckResult =
            _maintenanceSvc.refreshTenantMongoDBVersions(tenant, tenant2MtmMap, LOG);
        assertFalse(maintenanceCheckResult.needMaintenance());

        final ClusterDescription tenantClusterDesc =
            _clusterSvc
                .getActiveClusterDescription(
                    tenant.getClusterDescription().getGroupId(),
                    tenant.getClusterDescription().getUniqueId())
                .get();
        assertEquals(originalVersion, tenantClusterDesc.getMongoDBVersion());
        assertEquals(
            originalVersion.getMajorVersionString(), tenantClusterDesc.getMongoDBMajorVersion());
      }
    }

    // test scenario: refresh the tenants mongodb versions after the mtms are upgraded,
    // and the mtms are in IDLE state
    {
      _clusterDescriptionDao.save(
          m0MtmClusterDesc
              .copy()
              .setMongoDBVersion(newVersion.getVersion())
              .setMongoDBMajorVersion(newVersion.getMajorVersionString())
              .setState(ClusterDescription.State.IDLE)
              .build());
      _clusterDescriptionDao.save(
          serverlessMtmClusterDesc
              .copy()
              .setMongoDBVersion(newVersion.getVersion())
              .setMongoDBMajorVersion(newVersion.getMajorVersionString())
              .setState(ClusterDescription.State.IDLE)
              .build());

      final NDSGroup tenantGroup = _ndsGroupDao.find(tenantGroupId).get();
      final List<Cluster> tenantClusters = _clusterSvc.getAllActiveClusters(tenantGroupId);
      final Map<String, ClusterDescription> tenant2MtmMap =
          _clusterSvc.getTenantToMtmMap(tenantGroup);
      for (final Cluster tenant : tenantClusters) {
        final MaintenanceCheckResult maintenanceCheckResult =
            _maintenanceSvc.refreshTenantMongoDBVersions(tenant, tenant2MtmMap, LOG);
        assertFalse(maintenanceCheckResult.needMaintenance());

        final ClusterDescription tenantClusterDesc =
            _clusterSvc
                .getActiveClusterDescription(
                    tenant.getClusterDescription().getGroupId(),
                    tenant.getClusterDescription().getUniqueId())
                .get();
        assertEquals(newVersion, tenantClusterDesc.getMongoDBVersion());
        assertEquals(
            newVersion.getMajorVersionString(), tenantClusterDesc.getMongoDBMajorVersion());
      }
    }
  }

  private ClusterDescription createClusterWithProviderAndEncryptionProvider(
      CloudProvider pCloudProvider,
      ClusterDescription.EncryptionAtRestProvider pEncryptionAtRestProvider) {
    switch (pCloudProvider) {
      case AWS:
        {
          final ClusterDescription awsClusterDescription =
              new ClusterDescription(
                  NDSModelTestFactory.getAWSClusterDescriptionWithEncryptionAtRestProvider(
                      _group.getId(), "awsCluster", pEncryptionAtRestProvider));
          assertFalse(awsClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
          _clusterDescriptionDao.save(awsClusterDescription);
          return awsClusterDescription;
        }
      case AZURE:
        {
          final ClusterDescription azureClusterDescription =
              new ClusterDescription(
                  NDSModelTestFactory.getAzureClusterDescriptionWithEncryptionAtRestProvider(
                      _group.getId(), "azureCluster", pEncryptionAtRestProvider));
          assertFalse(azureClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
          _clusterDescriptionDao.save(azureClusterDescription);
          return azureClusterDescription;
        }
      case GCP:
        {
          final ClusterDescription gcpClusterDescription =
              new ClusterDescription(
                  NDSModelTestFactory.getGCPClusterDescriptionWithEncryptionAtRestProvider(
                      _group.getId(), "gcpCluster", pEncryptionAtRestProvider));
          assertFalse(gcpClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
          _clusterDescriptionDao.save(gcpClusterDescription);
          return gcpClusterDescription;
        }
      default:
        fail("Unexpected Cloud Provider for Cluster with Encryption at Rest:" + pCloudProvider);
    }
    return null;
  }

  private void enableEncryptionAtRestWithRotationDate(final Date pDate) {
    _ndsGroupDao.updateEncryptionAtRest(
        _group.getId(),
        new NDSEncryptionAtRest(
            new NDSAWSKMS.Builder()
                .setEnabled(true)
                .setAccessKeyID("aki")
                .setSecretAccessKey("sak")
                .setCustomerMasterKeyID("cmki")
                .setRegion(AWSNDSDefaults.REGION_NAME)
                .setLastKmipMasterKeyRotation(pDate)
                .setLastUpdatedKeyID(pDate)
                .setValid(true)
                .build(),
            new NDSAzureKeyVault(
                true,
                pDate,
                pDate,
                null,
                "cid",
                "tid",
                "secret",
                SupportedAzureEnvironment.AZURE,
                "sid",
                "rgn",
                "kvn",
                "kid",
                true),
            new NDSGoogleCloudKMS.Builder()
                .setEnabled(true)
                .setLastUpdatedKeyID(pDate)
                .setLastKmipMasterKeyRotation(pDate)
                .setServiceAccountKey("sak")
                .setKeyVersionResourceID("kvri")
                .setValid(true)
                .build()));
    reloadNdsGroup();
    final NDSEncryptionAtRest encryptionNotNeedingRotation = _ndsGroup.getEncryptionAtRest();
    assertEquals(
        pDate, encryptionNotNeedingRotation.getAWSKMS().getLastKmipMasterKeyRotation().get());
    assertTrue(encryptionNotNeedingRotation.getAWSKMS().isEnabled());
    assertEquals(
        pDate,
        encryptionNotNeedingRotation.getAzureKeyVault().getLastKmipMasterKeyRotation().get());
    assertTrue(encryptionNotNeedingRotation.getAzureKeyVault().isEnabled());
    assertEquals(
        pDate,
        encryptionNotNeedingRotation.getGoogleCloudKMS().getLastKmipMasterKeyRotation().get());
    assertTrue(encryptionNotNeedingRotation.getGoogleCloudKMS().isEnabled());
  }

  @Test
  public void testRotateKmipMasterKey_earDisabled_inWindow() throws Exception {
    // Setup clusters with EAR disabled
    final ClusterDescription awsClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AWS, ClusterDescription.EncryptionAtRestProvider.NONE);
    final ClusterDescription azureClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AZURE, ClusterDescription.EncryptionAtRestProvider.NONE);
    final ClusterDescription gcpClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.GCP, ClusterDescription.EncryptionAtRestProvider.NONE);

    // Disable encryption providers on group.
    final Date now = new Date();
    _ndsGroupDao.updateEncryptionAtRest(
        _group.getId(),
        new NDSEncryptionAtRest(
            new NDSAWSKMS.Builder()
                .setEnabled(false)
                .setValid(false)
                .setLastKmipMasterKeyRotation(now)
                .setLastUpdatedKeyID(now)
                .build(),
            new NDSAzureKeyVault(
                false, now, now, null, null, null, null, null, null, null, null, null, false),
            new NDSGoogleCloudKMS.Builder()
                .setLastUpdatedKeyID(now)
                .setLastKmipMasterKeyRotation(now)
                .build()));
    final NDSGroup groupWithEncryptionDisabled = _ndsGroupDao.find(_group.getId()).get();
    final NDSEncryptionAtRest encryptionDisabled =
        groupWithEncryptionDisabled.getEncryptionAtRest();
    assertEquals(now, encryptionDisabled.getAWSKMS().getLastKmipMasterKeyRotation().get());
    assertFalse(encryptionDisabled.getAWSKMS().isEnabled());
    assertEquals(now, encryptionDisabled.getAzureKeyVault().getLastKmipMasterKeyRotation().get());
    assertFalse(encryptionDisabled.getAzureKeyVault().isEnabled());
    assertEquals(now, encryptionDisabled.getGoogleCloudKMS().getLastKmipMasterKeyRotation().get());
    assertFalse(encryptionDisabled.getGoogleCloudKMS().isEnabled());

    // Maintenance has started
    setMaintenanceWindowToNow(groupWithEncryptionDisabled);
    List<MaintenanceCheckResult> result =
        _maintenanceSvc.rotateKmipMasterKey(groupWithEncryptionDisabled, _group);
    assertEquals(3, result.size());
    result.forEach(
        mCheckState -> {
          assertFalse(mCheckState.needMaintenance());
        });

    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), awsClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), azureClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), gcpClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    final NDSEncryptionAtRest encryptionDisabledAfterCheck =
        _ndsGroupDao.find(_group.getId()).get().getEncryptionAtRest();
    assertEquals(
        now, encryptionDisabledAfterCheck.getAWSKMS().getLastKmipMasterKeyRotation().get());
    assertEquals(
        now, encryptionDisabledAfterCheck.getAzureKeyVault().getLastKmipMasterKeyRotation().get());
    assertEquals(
        now, encryptionDisabledAfterCheck.getGoogleCloudKMS().getLastKmipMasterKeyRotation().get());
  }

  @Test
  public void testRotateKmipMasterKey_earDisabled_notInWindow() throws Exception {
    final Date now = new Date();

    // Setup clusters with EAR disabled
    final ClusterDescription awsClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AWS, ClusterDescription.EncryptionAtRestProvider.NONE);
    final ClusterDescription azureClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AZURE, ClusterDescription.EncryptionAtRestProvider.NONE);
    final ClusterDescription gcpClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.GCP, ClusterDescription.EncryptionAtRestProvider.NONE);

    // Disable encryption providers on group.
    _ndsGroupDao.updateEncryptionAtRest(
        _group.getId(),
        new NDSEncryptionAtRest(
            new NDSAWSKMS.Builder()
                .setEnabled(false)
                .setValid(false)
                .setLastKmipMasterKeyRotation(now)
                .setLastUpdatedKeyID(now)
                .build(),
            new NDSAzureKeyVault(
                false, now, now, null, null, null, null, null, null, null, null, null, false),
            new NDSGoogleCloudKMS.Builder()
                .setLastUpdatedKeyID(now)
                .setLastKmipMasterKeyRotation(now)
                .build()));
    final NDSGroup groupWithEncryptionDisabled = _ndsGroupDao.find(_group.getId()).get();
    final NDSEncryptionAtRest encryptionDisabled =
        groupWithEncryptionDisabled.getEncryptionAtRest();
    assertEquals(now, encryptionDisabled.getAWSKMS().getLastKmipMasterKeyRotation().get());
    assertFalse(encryptionDisabled.getAWSKMS().isEnabled());
    assertEquals(now, encryptionDisabled.getAzureKeyVault().getLastKmipMasterKeyRotation().get());
    assertFalse(encryptionDisabled.getAzureKeyVault().isEnabled());
    assertEquals(now, encryptionDisabled.getGoogleCloudKMS().getLastKmipMasterKeyRotation().get());
    assertFalse(encryptionDisabled.getGoogleCloudKMS().isEnabled());

    // Maintenance has not started
    setMaintenanceWindowToAnotherDay(groupWithEncryptionDisabled);
    List<MaintenanceCheckResult> result =
        _maintenanceSvc.rotateKmipMasterKey(groupWithEncryptionDisabled, _group);
    assertEquals(3, result.size());
    result.forEach(
        mCheckState -> {
          assertFalse(mCheckState.needMaintenance());
        });

    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), awsClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), azureClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), gcpClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    final NDSEncryptionAtRest encryptionDisabledAfterCheck =
        _ndsGroupDao.find(_group.getId()).get().getEncryptionAtRest();
    assertEquals(
        now, encryptionDisabledAfterCheck.getAWSKMS().getLastKmipMasterKeyRotation().get());
    assertEquals(
        now, encryptionDisabledAfterCheck.getAzureKeyVault().getLastKmipMasterKeyRotation().get());
    assertEquals(
        now, encryptionDisabledAfterCheck.getGoogleCloudKMS().getLastKmipMasterKeyRotation().get());
  }

  @Test
  public void testRotateKmipMasterKey_earEnabled_rotationNotNeeded_inWindow() throws Exception {
    // Setup clusters with EAR enabled
    final ClusterDescription awsClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AWS, ClusterDescription.EncryptionAtRestProvider.AWS);
    final ClusterDescription azureClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AZURE, ClusterDescription.EncryptionAtRestProvider.AZURE);
    final ClusterDescription gcpClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.GCP, ClusterDescription.EncryptionAtRestProvider.GCP);

    // Enable encryption providers on group and set last rotation date to now.
    final Date now = new Date();
    enableEncryptionAtRestWithRotationDate(now);

    // Maintenance has started
    setMaintenanceWindowToNow(_ndsGroup);
    List<MaintenanceCheckResult> results = _maintenanceSvc.rotateKmipMasterKey(_ndsGroup, _group);
    assertEquals(3, results.size());
    results.forEach(
        maintenanceCheckState -> {
          assertFalse(maintenanceCheckState.needMaintenance());
        });

    assertEquals(
        0,
        _auditSvc
            .findByEventTypeForGroup(_group.getId(), NDSAudit.Type.KMIP_KEY_ROTATION_SCHEDULED)
            .size());

    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), awsClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), azureClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), gcpClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    final NDSEncryptionAtRest encryptionNotNeedingRotationAfterCheck =
        _ndsGroupDao.find(_group.getId()).get().getEncryptionAtRest();
    assertEquals(
        now,
        encryptionNotNeedingRotationAfterCheck.getAWSKMS().getLastKmipMasterKeyRotation().get());
    assertEquals(
        now,
        encryptionNotNeedingRotationAfterCheck
            .getAzureKeyVault()
            .getLastKmipMasterKeyRotation()
            .get());
    assertEquals(
        now,
        encryptionNotNeedingRotationAfterCheck
            .getGoogleCloudKMS()
            .getLastKmipMasterKeyRotation()
            .get());
  }

  @Test
  public void testRotateKmipMasterKey_earEnabled_rotationNotNeeded_notInWindow() throws Exception {
    // Setup clusters with EAR enabled
    final ClusterDescription awsClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AWS, ClusterDescription.EncryptionAtRestProvider.AWS);
    final ClusterDescription azureClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AZURE, ClusterDescription.EncryptionAtRestProvider.AZURE);
    final ClusterDescription gcpClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.GCP, ClusterDescription.EncryptionAtRestProvider.GCP);

    // Enable encryption providers on group and set last rotation date to now.
    final Date now = new Date();
    enableEncryptionAtRestWithRotationDate(now);

    // Maintenance has not started
    setMaintenanceWindowToAnotherDay(_ndsGroup);
    List<MaintenanceCheckResult> results = _maintenanceSvc.rotateKmipMasterKey(_ndsGroup, _group);
    assertEquals(3, results.size());
    results.forEach(
        maintenanceCheckState -> {
          assertFalse(maintenanceCheckState.needMaintenance());
        });

    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), awsClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), azureClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), gcpClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    final NDSEncryptionAtRest encryptionNotNeedingRotationAfterCheck =
        _ndsGroupDao.find(_group.getId()).get().getEncryptionAtRest();
    assertEquals(
        now,
        encryptionNotNeedingRotationAfterCheck.getAWSKMS().getLastKmipMasterKeyRotation().get());
    assertEquals(
        now,
        encryptionNotNeedingRotationAfterCheck
            .getAzureKeyVault()
            .getLastKmipMasterKeyRotation()
            .get());
    assertEquals(
        now,
        encryptionNotNeedingRotationAfterCheck
            .getGoogleCloudKMS()
            .getLastKmipMasterKeyRotation()
            .get());
  }

  @Test
  public void testRotateKmipMasterKey_earEnabled_rotationNeeded_inWindow() throws Exception {
    final Date now = new Date();

    // Setup clusters with EAR enabled
    final ClusterDescription awsClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AWS, ClusterDescription.EncryptionAtRestProvider.AWS);
    final ClusterDescription azureClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AZURE, ClusterDescription.EncryptionAtRestProvider.AZURE);
    final ClusterDescription gcpClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.GCP, ClusterDescription.EncryptionAtRestProvider.GCP);

    // Test group with AWS KMS enabled and it's been more than 83 days since key rotation
    final Date lastKeyRotationDate = DateUtils.addDays(now, -KMIP_MASTER_KEY_ROTATION_PERIOD_DAYS);
    enableEncryptionAtRestWithRotationDate(lastKeyRotationDate);

    // Maintenance has started
    setMaintenanceWindowToNow(_ndsGroup);
    final List<MaintenanceCheckResult> resultsMaintenanceNow =
        runMaintenanceSavingHistories(() -> _maintenanceSvc.rotateKmipMasterKey(_ndsGroup, _group));

    assertEquals(3, resultsMaintenanceNow.size());
    resultsMaintenanceNow.forEach(
        maintenanceCheckState -> {
          assertTrue(maintenanceCheckState.needMaintenance());
        });
    assertTrue(
        _clusterDescriptionDao
            .findByName(_group.getId(), awsClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent()); // is not present :(
    assertTrue(
        _clusterDescriptionDao
            .findByName(_group.getId(), azureClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertTrue(
        _clusterDescriptionDao
            .findByName(_group.getId(), gcpClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());

    assertEquals(
        1,
        _auditSvc
            .findByEventTypeForGroup(_group.getId(), NDSAudit.Type.KMIP_KEY_ROTATION_SCHEDULED)
            .size());

    final NDSEncryptionAtRest encryptionNeedingRotationAfterCheck =
        _ndsGroupDao.find(_group.getId()).get().getEncryptionAtRest();
    assertTrue(
        encryptionNeedingRotationAfterCheck
            .getAWSKMS()
            .getLastKmipMasterKeyRotation()
            .get()
            .after(now));
    assertTrue(
        encryptionNeedingRotationAfterCheck
            .getAzureKeyVault()
            .getLastKmipMasterKeyRotation()
            .get()
            .after(now));
    assertTrue(
        encryptionNeedingRotationAfterCheck
            .getGoogleCloudKMS()
            .getLastKmipMasterKeyRotation()
            .get()
            .after(now));
  }

  @Test
  public void testRotateKmipMasterKey_earEnabled_rotationNeeded_notInWindow() throws Exception {
    // Setup clusters with EAR enabled
    final ClusterDescription awsClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AWS, ClusterDescription.EncryptionAtRestProvider.AWS);
    final ClusterDescription azureClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.AZURE, ClusterDescription.EncryptionAtRestProvider.AZURE);
    final ClusterDescription gcpClusterDescription =
        createClusterWithProviderAndEncryptionProvider(
            CloudProvider.GCP, ClusterDescription.EncryptionAtRestProvider.GCP);

    // Test group with AWS KMS enabled and it's been more than 83 days since key rotation
    final Date now = new Date();
    final Date lastKeyRotationDate = DateUtils.addDays(now, -KMIP_MASTER_KEY_ROTATION_PERIOD_DAYS);
    enableEncryptionAtRestWithRotationDate(lastKeyRotationDate);

    // Maintenance has not started
    setMaintenanceWindowToAnotherDay(_ndsGroup);
    List<MaintenanceCheckResult> results = _maintenanceSvc.rotateKmipMasterKey(_ndsGroup, _group);
    assertEquals(3, results.size());
    results.forEach(
        maintenanceCheckState -> {
          assertTrue(maintenanceCheckState.needMaintenance());
        });

    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), awsClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), azureClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertFalse(
        _clusterDescriptionDao
            .findByName(_group.getId(), gcpClusterDescription.getName())
            .get()
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    final NDSEncryptionAtRest encryptionStillNeedingRotationAfterCheck =
        _ndsGroupDao.find(_group.getId()).get().getEncryptionAtRest();
    assertEquals(
        lastKeyRotationDate,
        encryptionStillNeedingRotationAfterCheck.getAWSKMS().getLastKmipMasterKeyRotation().get());
    assertEquals(
        lastKeyRotationDate,
        encryptionStillNeedingRotationAfterCheck
            .getAzureKeyVault()
            .getLastKmipMasterKeyRotation()
            .get());
    assertEquals(
        lastKeyRotationDate,
        encryptionStillNeedingRotationAfterCheck
            .getGoogleCloudKMS()
            .getLastKmipMasterKeyRotation()
            .get());
  }

  @Test
  public void testMaintenanceCompletionCleanup_StillHaveWorkingCluster() throws Exception {
    final ClusterDescription c1 = createProvisionedCluster();
    final ClusterDescription c2 = createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);
    final ObjectId groupId = _ndsGroup.getGroupId();
    final Integer currentHour = getCurrentDayOfWeekHourOfDay(0).getRight();
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(groupId, _currentTime);

    _clusterDescriptionDao.setState(c1.getGroupId(), c1.getName(), WORKING);
    _clusterDescriptionDao.setState(c2.getGroupId(), c2.getName(), IDLE);
    final List<MaintenanceCheckResult> states = Collections.emptyList();

    _maintenanceSvc.maintenanceCompletionCleanup(
        _ndsGroupDao.find(_ndsGroup.getGroupId()).get(),
        _group,
        states,
        _clusterSvc.getActiveClusterDescriptions(_ndsGroup.getGroupId()),
        LOG);

    // If there is a non-idle cluster, maintenance should not be considered completed even if
    // there's no additional maintenance necessary
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();
    assertEquals(
        Optional.of(_currentTime),
        _ndsGroup.getMaintenanceWindow().getAdvanceNotificationSendDate());

    _clusterDescriptionDao.setState(c1.getGroupId(), c1.getName(), IDLE);

    _maintenanceSvc.maintenanceCompletionCleanup(
        _ndsGroupDao.find(_ndsGroup.getGroupId()).get(),
        _group,
        states,
        _clusterSvc.getActiveClusterDescriptions(_ndsGroup.getGroupId()),
        LOG);

    // Once the cluster becomes idle, then we can consider maintenance completed
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();
    assertEquals(
        Optional.empty(), _ndsGroup.getMaintenanceWindow().getAdvanceNotificationSendDate());

    // Set startASAP and sanity check it applies
    _ndsGroupMaintenanceDao.setStartASAP(_ndsGroup.getGroupId());

    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();
    assertEquals(
        Optional.empty(), _ndsGroup.getMaintenanceWindow().getAdvanceNotificationSendDate());
    assertTrue(_ndsGroup.getMaintenanceWindow().getStartASAP());

    _maintenanceSvc.maintenanceCompletionCleanup(
        _ndsGroupDao.find(_ndsGroup.getGroupId()).get(),
        _group,
        states,
        _clusterSvc.getActiveClusterDescriptions(_ndsGroup.getGroupId()),
        LOG);

    // Maintenance is still considered completed
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();
    assertEquals(
        Optional.empty(), _ndsGroup.getMaintenanceWindow().getAdvanceNotificationSendDate());
    assertFalse(_ndsGroup.getMaintenanceWindow().getStartASAP());
  }

  @Test
  public void testMaintenanceProtectedHoursCompletionCleanup_StillHaveWorkingCluster()
      throws Exception {
    final ObjectId groupId = _ndsGroup.getGroupId();
    final ClusterDescription c1 = createProvisionedCluster();
    final ClusterDescription c2 = createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);
    final Integer currentHour = getCurrentDayOfWeekHourOfDay(0).getRight();
    final NDSGroupMaintenanceWindow maintenanceWindow =
        _ndsGroup.getMaintenanceWindow().toBuilder()
            .protectedHours(
                new MaintenanceProtectedHours(currentHour, currentHour + 4, _currentTime, true))
            .build();
    _ndsGroupMaintenanceDao.setProtectedHoursMaintenanceNeededDate(groupId, _currentTime);
    _clusterDescriptionDao.setState(c1.getGroupId(), c1.getName(), WORKING);
    _clusterDescriptionDao.setState(c2.getGroupId(), c2.getName(), IDLE);

    // initially working clusters should not be marked as completed
    _maintenanceSvc.maintenanceProtectedHoursCompletionCleanup(
        groupId,
        getProtectedHours(groupId),
        List.of(),
        _clusterSvc.getActiveClusterDescriptions(groupId),
        LOG);
    assertEquals(Optional.of(_currentTime), getProtectedHours(groupId).getMaintenanceNeededDate());

    // Once the cluster becomes idle, then we can consider protected hour maintenance completed
    _clusterDescriptionDao.setState(c1.getGroupId(), c1.getName(), IDLE);
    _maintenanceSvc.maintenanceProtectedHoursCompletionCleanup(
        groupId,
        getProtectedHours(groupId),
        List.of(),
        _clusterSvc.getActiveClusterDescriptions(groupId),
        LOG);
    assertEquals(Optional.empty(), getProtectedHours(groupId).getMaintenanceNeededDate());

    // protected hour maintenance should still be considered complete
    _maintenanceSvc.maintenanceProtectedHoursCompletionCleanup(
        groupId,
        getProtectedHours(groupId),
        List.of(),
        _clusterSvc.getActiveClusterDescriptions(_ndsGroup.getGroupId()),
        LOG);
    assertEquals(Optional.empty(), getProtectedHours(groupId).getMaintenanceNeededDate());
  }

  final MaintenanceProtectedHours getProtectedHours(final ObjectId pGroupId) {
    return _ndsGroupDao
        .find(pGroupId)
        .map(NDSGroup::getMaintenanceWindow)
        .map(NDSGroupMaintenanceWindow::getProtectedHours)
        .orElseThrow();
  }

  @Test
  public void testMaintenanceCompletionCleanup_SystemGeneratedMaintenanceWindow() throws Exception {
    final ClusterDescription c1 = createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    final Calendar now = getCalendarInstance(TimeZone.getTimeZone("UTC"));
    _maintenanceSvc.setMaintenanceWindowForGroupForTest(
        _group.getId(),
        NDSGroupMaintenanceWindow.getTestSystemDefinedMaintenanceWindow(
            now.get(Calendar.HOUR_OF_DAY)));
    _ndsGroupMaintenanceDao.setMaintenanceNeededDate(_group.getId(), _currentTime);

    final List<MaintenanceCheckResult> states = Collections.emptyList();
    _clusterDescriptionDao.setState(c1.getGroupId(), c1.getName(), WORKING);
    _maintenanceSvc.maintenanceCompletionCleanup(
        _ndsGroupDao.find(_ndsGroup.getGroupId()).get(),
        _group,
        states,
        _clusterSvc.getActiveClusterDescriptions(_ndsGroup.getGroupId()),
        LOG);

    // If there is a non-idle cluster, maintenance should not be considered completed even if
    // there's no additional maintenance necessary
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();
    assertTrue(_ndsGroup.getMaintenanceWindow().getMaintenanceNeededDate().isPresent());

    _clusterDescriptionDao.setState(c1.getGroupId(), c1.getName(), IDLE);
    _maintenanceSvc.maintenanceCompletionCleanup(
        _ndsGroupDao.find(_ndsGroup.getGroupId()).get(),
        _group,
        states,
        _clusterSvc.getActiveClusterDescriptions(_ndsGroup.getGroupId()),
        LOG);

    // Once the cluster becomes idle, then we can consider maintenance completed
    _ndsGroup = _ndsGroupDao.find(_ndsGroup.getGroupId()).get();
    assertFalse(_ndsGroup.getMaintenanceWindow().getMaintenanceNeededDate().isPresent());
  }

  @Test
  public void testRefreshMongoDBVersions_fixedVersion_noAudit() throws Exception {
    createProvisionedCluster_fixedVersion();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_4_2, "4.2.3")));

    // For the test, manually set a Mongo URI host in the created cluster's description
    // Must be set in order for NDSClusterSvc::clusterChangesStarted to save the audit
    final List<BasicDBObject> clusters_objects =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId());

    assertEquals(1, clusters_objects.size());

    BasicDBObject pDBObject = clusters_objects.get(0);
    final ClusterDescription clusterDescription =
        new ClusterDescription.Builder<>(pDBObject)
            .setMongoUriHosts(new String[] {"localhost:27017"})
            .build();

    _clusterDescriptionDao.save(clusterDescription);

    final Date sincePlanning = _currentTime;

    // Call the planner (to call refreshMongoDBVersions)
    _ndsPlanningSvc.doPlanning(_ndsGroupDao.find(_ndsGroup.getGroupId()).get(), LOG, null);

    // Ensure that an excess update started event is not generated (CLOUDP-36860)
    final List<Event> updateEventsSincePlanning =
        _auditSvc.findByDate(NDSAudit.Type.CLUSTER_UPDATE_STARTED, sincePlanning);

    assertEquals(0, updateEventsSincePlanning.size());
  }

  // TODO: All of the tests below here should be in NDSPlanningSvcIntTests
  @Test
  public void testRefreshMongoDBVersions_OutOfDate_HasRunningImports() throws Exception {
    createProvisionedCluster();
    createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final List<ClusterDescription> existingClusters =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    final String importClusterName = existingClusters.get(0).getName();
    final ObjectId importClusterUniqueId = existingClusters.get(0).getUniqueId();

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0.999")));

    final MongomirrorPullLiveImport mongomirrorPullLiveImport =
        new MongomirrorPullLiveImport.Builder()
            .groupId(_group.getId())
            .destination(
                new LiveImport.Destination(importClusterName, importClusterUniqueId, false))
            .src(new LiveImport.Source("", "", "", "", false, "", ""))
            .build();

    _liveImportDao.saveReplicaSafe(mongomirrorPullLiveImport.toDBObject());
    _ndsPlanningSvc.doPlanning(_ndsGroupDao.find(_ndsGroup.getGroupId()).get(), LOG, null);
    final List<ClusterDescription> clusters =
        _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());

    // Clusters involved in live imports are blocked from version updates
    clusters.forEach(c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));
  }

  @Test
  public void testRefreshMongoDBVersions_ForMTMClusterWithTenants() throws Exception {
    final Version originalVersion = NDSModelTestFactory.MONGODB_VERSION_CURRENT;
    final Version newVersion =
        VersionUtils.parse(
            String.format(
                "%d.%d.%d",
                originalVersion.getMajor(),
                originalVersion.getMinor() + 1,
                originalVersion.getMaintenance()));
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, originalVersion.getVersion())));
    setPhasedVersionToAutoRelease(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);

    final ClusterDescription mtmClusterDesc =
        createProvisionedCluster(
                Collections.singletonList(getReplicationSpec(1, 3)),
                null,
                ClusterDescription.ClusterType.REPLICASET,
                _clusterSvc.getDefaultCDMongoDBMajorVersion(),
                _clusterSvc.getDefaultCDMongoDBVersion(),
                false,
                true,
                VersionReleaseSystem.CONTINUOUS)
            .copy()
            .setMongoDBMajorVersion(originalVersion.getMajorVersionString())
            .setMongoDBVersion(originalVersion.getVersion())
            .setContinuousDeliveryFCV(originalVersion.toString())
            .build();
    _clusterDescriptionDao.save(mtmClusterDesc);

    // add tenant clusters
    final ObjectId tenantGroupId = new ObjectId();
    _groupSvc.ensureGroup(tenantGroupId);
    final ClusterDescription serverlessTenantDesc =
        new ClusterDescription(NDSModelTestFactory.getServerlessClusterDescription())
            .copy()
            .setGroupId(tenantGroupId)
            .setName("serverlessTenant1")
            .setContinuousDeliveryFCV(mtmClusterDesc.getContinuousDeliveryFCV().get())
            .setState(ClusterDescription.State.IDLE)
            .build();
    _clusterDescriptionDao.save(serverlessTenantDesc);

    final Map<String, ProxyVersion> proxyVersions =
        Map.of(
            mtmClusterDesc.getUniqueId().toString(),
            new ProxyVersion(1L, Map.of()),
            serverlessTenantDesc.getUniqueId().toString(),
            new ProxyVersion(1L, Map.of()));

    final ServerlessMTMCluster mtmCluster =
        new ServerlessMTMCluster(
                NDSModelTestFactory.getServerlessMTMCluster(
                    mtmClusterDesc.getGroupId(), mtmClusterDesc.getName()))
            .copy()
            .setMongoDBMajorVersion(originalVersion.getMajorVersionString())
            .setProxyVersions(proxyVersions)
            .build();
    _mtmClusterDao.saveReplicaSafe(mtmCluster.toDBObject());

    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // update the C/D target version
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            new PhasedVersionParameters(
                SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, newVersion.getVersion())));

    final Date publicationDate = _currentTime;
    final Cluster cluster =
        _clusterSvc.getActiveCluster(mtmClusterDesc.getGroupId(), mtmClusterDesc.getName()).get();
    final Date previousPlanningDate = _ndsGroupDao.find(tenantGroupId).get().getNextPlanningDate();

    // check that tenant clusters not updated when property is not enabled
    _appSettings.setProp(
        CriticalMaintenanceRunChunkJobStateSvc.NDS_CRITICAL_MAINTENANCE_TENANT_UPDATE_ENABLED,
        "false",
        SettingType.MEMORY);

    _maintenanceSvc.refreshMongoDBVersions(
        _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG);

    assertFalse(
        _ndsGroupDao.find(tenantGroupId).get().getNextPlanningDate().before(previousPlanningDate));

    _appSettings.setProp(
        CriticalMaintenanceRunChunkJobStateSvc.NDS_CRITICAL_MAINTENANCE_TENANT_UPDATE_ENABLED,
        "true",
        SettingType.MEMORY);

    _maintenanceSvc.refreshMongoDBVersions(
        _ndsGroup, _group, cluster, publicationDate, _numMonitoringAddedMap, LOG);

    assertTrue(
        _ndsGroupDao.find(tenantGroupId).get().getNextPlanningDate().before(previousPlanningDate));

    _appSettings.clearMemory();
  }

  @Test
  public void testRefreshAutomationConfig_UpToDate() throws Exception {
    // Automation Config after clusters means it is up to date
    createProvisionedCluster();
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    _ndsPlanningSvc.doPlanning(_ndsGroup, LOG, null);
    reloadNdsGroup();
    assertFalse(_ndsGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent());
  }

  @Test
  public void testRefreshAutomationConfig_OutOfDate() throws Exception {
    // Automation config does need to be updated if a cluster changes after it was last updated
    createProvisionedCluster();
    setMaintenanceWindowToNow(_ndsGroup);

    _ndsPlanningSvc.doPlanning(_ndsGroupDao.find(_ndsGroup.getGroupId()).get(), LOG, null);
    reloadNdsGroup();
    assertFalse(_ndsGroup.getNeedsProcessAutomationConfigPublishAfter().isPresent());
  }

  @Test
  public void testCheckExternalMaintenance() {
    assertThat(_maintenanceSvc.checkExternalMaintenance(_ndsGroup))
        .allMatch(m -> !m.needMaintenance());

    _ndsGroupMaintenanceDao.addExternalMaintenance(_ndsGroup.getGroupId(), "swapMachine_r1");
    _ndsGroupMaintenanceDao.addExternalMaintenance(_ndsGroup.getGroupId(), "refreshIps_r2");
    reloadNdsGroup();

    assertThat(_ndsGroup.getMaintenanceWindow().getExternalMaintenanceNames())
        .isEqualTo(List.of("swapMachine_r1", "refreshIps_r2"));

    // Act
    final List<MaintenanceCheckResult> maintenanceCheckResults =
        _maintenanceSvc.checkExternalMaintenance(_ndsGroup);

    assertThat(maintenanceCheckResults).hasSize(2);
    assertThat(maintenanceCheckResults)
        .doesNotContainNull()
        .isNotEmpty()
        .allSatisfy(
            m -> {
              assertEquals(MaintenanceType.EXTERNAL_MAINTENANCE, m.maintenanceType());
              assertTrue(m.needMaintenance());
              assertFalse(m.isCriticalRelease());
            });
    assertThat(maintenanceCheckResults)
        .flatMap(MaintenanceCheckResult::getExternalMaintenanceNames)
        .containsExactlyInAnyOrder("swapMachine_r1", "refreshIps_r2");
  }

  @Test
  public void testSetUserDefinedMaintenanceWindow_MaintenanceHistoryGetsUpdated() throws Exception {
    final Group group = MmsFactory.createGroup(_organization, "cus_0002");
    final ObjectId groupId = group.getId();
    _groupSvc.ensureGroup(groupId);
    _groupDao.save(group);

    final List<NdsMaintenanceHistory> list = new ArrayList<>();
    list.add(
        NdsMaintenanceHistory.builder()
            .setGroupId(groupId)
            .setTargetMaintenanceDate(_currentTime)
            .setIsCritical(false)
            .setMaintenanceType(MaintenanceType.CLOUD_PROVIDER_SCHEDULED_MAINTENANCE_PERFORMED)
            .setState(NdsMaintenanceHistory.State.PENDING)
            .setSchedulingBehavior(SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
            .build());

    list.add(
        NdsMaintenanceHistory.builder()
            .setGroupId(groupId)
            .setTargetMaintenanceDate(_currentTime)
            .setIsCritical(false)
            .setMaintenanceType(MaintenanceType.CLOUD_PROVIDER_SCHEDULED_MAINTENANCE_PERFORMED)
            .setState(NdsMaintenanceHistory.State.PENDING)
            .setSchedulingBehavior(SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
            .build());
    _ndsMaintenanceHistorySvc.savePendingMaintenances(list);
    assertEquals(2, _ndsMaintenanceHistorySvc.findAllByGroupIdAndStateNotCompleted(groupId).size());

    // Act
    // Set custom maintenance window
    final NDSGroupMaintenanceWindow maintenanceWindow = new NDSGroupMaintenanceWindow(5, 12);
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        groupId, maintenanceWindow, AuditInfoHelpers.fromSystem());

    final List<NdsMaintenanceHistory> maintenanceHistories =
        _ndsMaintenanceHistorySvc.findAllByGroupIdAndStateNotCompleted(groupId);

    assertEquals(2, maintenanceHistories.size());

    maintenanceHistories.forEach(
        m -> {
          final Date maintenanceNextStartDateTime =
              _ndsMaintenanceDateCalculationSvc
                  .getNextMaintenanceStartDateTime(
                      maintenanceWindow, group, SchedulingBehavior.DURING_MAINTENANCE_WINDOW)
                  .getTime();

          final Calendar maintenanceNextStartCalendar = getCalendarInstanceForGroup();
          maintenanceNextStartCalendar.setTime(maintenanceNextStartDateTime);

          assertEquals(m.getTargetMaintenanceDate(), maintenanceNextStartDateTime);
          assertEquals(
              maintenanceWindow.getDayOfWeek(),
              maintenanceNextStartCalendar.get(Calendar.DAY_OF_WEEK));
          assertEquals(
              maintenanceWindow.getHourOfDay(),
              maintenanceNextStartCalendar.get(Calendar.HOUR_OF_DAY));
        });
  }

  @Test
  public void testScheduleDbCheck() throws Exception {
    setMaintenanceWindowToNow(_ndsGroup);

    final ClusterDescription clusterDescription = createProvisionedCluster();
    _ndsClusterSamplingSvc.setNeedsDbCheckAfter(
        _ndsGroup.getGroupId(), clusterDescription.getName(), new Date());

    final ClusterDescription clusterDescriptionPreScheduling =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertNull(clusterDescriptionPreScheduling.getLastDbCheckDate());
    assertNotNull(clusterDescriptionPreScheduling.getNeedsDbCheckAfter());

    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();

    final MaintenanceCheckResult state =
        runMaintenanceSavingHistory(
            () -> _maintenanceSvc.scheduleDbCheck(_ndsGroup, _group, cluster, LOG, true, false));

    assertEquals(MaintenanceType.DBCHECK_AUTOMATED, state.maintenanceType());
    assertTrue(state.needMaintenance());

    final ClusterDescription clusterDescriptionPostScheduling =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertNotNull(clusterDescriptionPostScheduling.getLastDbCheckDate());
    assertNull(clusterDescriptionPostScheduling.getNeedsDbCheckAfter());

    // possibly define replset hardware
    assertTrue(
        _dbCheckSvc
            .findExistingActiveForCluster(clusterDescriptionPostScheduling.getUniqueId())
            .isPresent());
  }

  @Test
  public void testReloadSslOnProcesses() throws Exception {
    // not in window or during system hour
    setMaintenanceWindowInNumDays(_ndsGroup, 5);
    _ndsGroupMaintenanceDao.overrideSystemMaintenanceWindowForTest(_ndsGroup.getGroupId(), 2);
    reloadNdsGroup();
    final ClusterDescription clusterDescription = createProvisionedCluster();
    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    // no nodes need SSL reload
    {
      final List<MaintenanceCheckResult> states =
          _maintenanceSvc.reloadSslOnProcesses(_ndsGroup, cluster, _numMonitoringAddedMap, LOG);
      assertEquals(3, states.size(), "Returns a state per node");
      assertTrue(
          states.stream().noneMatch(MaintenanceCheckResult::needMaintenance),
          "None of the nodes need TLS reload");
    }

    // request reload for one node
    {
      final ReplicaSetHardware replicaSetHardware = cluster.getReplicaSets().get(0);
      final InstanceHardware hardware = replicaSetHardware.getHardware().get(0);
      _clusterSvc.setReloadSslOnProcessesRequestedDate(replicaSetHardware, hardware);
      final List<MaintenanceCheckResult> states =
          _maintenanceSvc.reloadSslOnProcesses(
              _ndsGroup,
              _clusterSvc
                  .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
                  .orElseThrow(),
              _numMonitoringAddedMap,
              LOG);
      assertEquals(3, states.size(), "Returns a state per node");
      assertEquals(
          1,
          states.stream().filter(MaintenanceCheckResult::needMaintenance).count(),
          "One node needs maintenance");
      assertEquals(
          hardware.getHostnameForAgents().orElseThrow(),
          states.stream()
              .filter(MaintenanceCheckResult::needMaintenance)
              .findFirst()
              .map(MaintenanceCheckResult::getInstanceHardwareDetails)
              .map(InstanceHardwareDetails::getHostname)
              .orElseThrow(),
          "First node needs maintenance");
      assertEquals(
          0,
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .map(Cluster::getReplicaSets)
              .stream()
              .flatMap(Collection::stream)
              .map(ReplicaSetHardware::getHardware)
              .flatMap(Collection::stream)
              .filter(InstanceHardware::needsReloadSslOnProcesses)
              .count(),
          "Not during system defined hour so not marked as needed now");
    }
  }

  @Test
  public void testReloadSslOnProcesses_notInProtectedHours() throws Exception {

    // not during protected hours
    final Calendar calendar = getCalendarInstanceForGroup();
    calendar.add(Calendar.DAY_OF_WEEK, 5);
    setMaintenanceWindow(
        _ndsGroup,
        new NDSGroupMaintenanceWindow(
            calendar.get(Calendar.DAY_OF_WEEK),
            calendar.get(Calendar.HOUR_OF_DAY),
            false,
            MaintenanceProtectedHours.getUserDefinedMaintenanceProtectedHours(2, 9)));

    final ClusterDescription clusterDescription = createProvisionedCluster();
    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    // no nodes need SSL reload
    {
      final List<MaintenanceCheckResult> states =
          _maintenanceSvc.reloadSslOnProcesses(_ndsGroup, cluster, _numMonitoringAddedMap, LOG);
      assertEquals(3, states.size(), "Returns a state per node");
      assertTrue(
          states.stream().noneMatch(MaintenanceCheckResult::needMaintenance),
          "None of the nodes need TLS reload");
    }

    // request reload for one node
    {
      final ReplicaSetHardware replicaSetHardware = cluster.getReplicaSets().get(0);
      final InstanceHardware hardware = replicaSetHardware.getHardware().get(0);
      _clusterSvc.setReloadSslOnProcessesRequestedDate(replicaSetHardware, hardware);
      final List<MaintenanceCheckResult> states =
          _maintenanceSvc.reloadSslOnProcesses(
              _ndsGroup,
              _clusterSvc
                  .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
                  .orElseThrow(),
              _numMonitoringAddedMap,
              LOG);
      assertEquals(3, states.size(), "Returns a state per node");
      assertEquals(
          1,
          states.stream().filter(MaintenanceCheckResult::needMaintenance).count(),
          "One node needs maintenance");
      assertEquals(
          hardware.getHostnameForAgents().orElseThrow(),
          states.stream()
              .filter(MaintenanceCheckResult::needMaintenance)
              .findFirst()
              .map(MaintenanceCheckResult::getInstanceHardwareDetails)
              .map(InstanceHardwareDetails::getHostname)
              .orElseThrow(),
          "First node needs maintenance");
      assertEquals(
          1,
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .map(Cluster::getReplicaSets)
              .stream()
              .flatMap(Collection::stream)
              .map(ReplicaSetHardware::getHardware)
              .flatMap(Collection::stream)
              .filter(InstanceHardware::needsReloadSslOnProcesses)
              .count(),
          "Not in protected hours window so marked as needed now");
    }
  }

  @Test
  public void testReloadSslOnProcesses_inProtectedHours() throws Exception {
    // move into maintenance window
    setMaintenanceWindowWithProtectedHoursToNow(_ndsGroup);
    final ClusterDescription clusterDescription = createProvisionedCluster();
    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    final ReplicaSetHardware replicaSetHardware = cluster.getReplicaSets().get(0);
    _clusterSvc.setReloadSslOnProcessesRequestedDate(
        replicaSetHardware, replicaSetHardware.getHardware().get(0));

    final List<MaintenanceCheckResult> states =
        _maintenanceSvc.reloadSslOnProcesses(
            _ndsGroup,
            _clusterSvc
                .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
                .orElseThrow(),
            _numMonitoringAddedMap,
            LOG);

    // hardware was not marked as needing SSL reload immediately
    assertEquals(
        0,
        _clusterSvc
            .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
            .map(Cluster::getReplicaSets)
            .stream()
            .flatMap(Collection::stream)
            .map(ReplicaSetHardware::getHardware)
            .flatMap(Collection::stream)
            .filter(InstanceHardware::needsReloadSslOnProcesses)
            .count(),
        "In protected hours window so reload is not marked as needed now");
  }

  @Test
  public void testRefreshMongoDBParameters() throws Exception {
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0.1")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(
                new PhasedReleaseCriteria(
                    100, 50, Map.of("referenceArgument", "referenceValue"), null, null, null));
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(100, 50));
    setMaintenanceWindowToNow(_ndsGroup);

    // Non-eligible version
    {
      final ClusterDescription clusterDescription =
          createProvisionedCluster_withVersion(
              TEST_DEDICATED_MONGODB_MAJOR_VERSION, "8.0.0", false);
      final Cluster cluster =
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();

      clearNeedsPublish();

      final MaintenanceCheckResult state =
          _maintenanceSvc.refreshMongoDBParameters(
              _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG);
      assertEquals(MaintenanceType.MONGODB_PARAMETERS_UPDATED, state.maintenanceType());
      assertFalse(state.needMaintenance());
      assertFalse(state.isCriticalRelease());
      final ClusterDescription clusterDescriptionUpdated =
          _clusterDescriptionDao
              .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    }

    // Eligible version, equal parameters
    {
      final ClusterDescription clusterDescription =
          createProvisionedCluster_withVersion(
              TEST_DEDICATED_MONGODB_MAJOR_VERSION, "8.0.1", false);
      final Cluster cluster =
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();

      clearNeedsPublish();

      final ClusterDescriptionProcessArgsUpdatable args =
          _clusterDescriptionProcessArgsDao
              .findForUpdate(
                  clusterDescription.getName(), clusterDescription.getGroupId(), Type.STANDARD)
              .get();

      args.getPhasedVersionShardArg()
          .get()
          .setSetParameterMap(Map.of("referenceArgument", "referenceValue"));
      _clusterDescriptionProcessArgsDao.save(args);

      final MaintenanceCheckResult state =
          _maintenanceSvc.refreshMongoDBParameters(
              _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG);
      assertEquals(MaintenanceType.MONGODB_PARAMETERS_UPDATED, state.maintenanceType());
      assertFalse(state.needMaintenance());
      assertFalse(state.isCriticalRelease());
      final ClusterDescription clusterDescriptionUpdated =
          _clusterDescriptionDao
              .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    }

    // Eligible version, parameters diff.
    {
      final ClusterDescription clusterDescription =
          createProvisionedCluster_withVersion(
              TEST_DEDICATED_MONGODB_MAJOR_VERSION, "8.0.1", false);
      final Cluster cluster =
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();

      clearNeedsPublish();

      final ClusterDescriptionProcessArgsUpdatable args =
          _clusterDescriptionProcessArgsDao
              .findForUpdate(
                  clusterDescription.getName(), clusterDescription.getGroupId(), Type.STANDARD)
              .get();

      args.getPhasedVersionShardArg()
          .get()
          .setSetParameterMap(
              Map.of("referenceArgument", "referenceValue", "newArgument", "newValue"));
      _clusterDescriptionProcessArgsDao.save(args);

      final MaintenanceCheckResult state =
          _maintenanceSvc.refreshMongoDBParameters(
              _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG);
      assertEquals(MaintenanceType.MONGODB_PARAMETERS_UPDATED, state.maintenanceType());
      assertTrue(state.needMaintenance());
      assertFalse(state.isCriticalRelease());
      final ClusterDescription clusterDescriptionUpdated =
          _clusterDescriptionDao
              .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    }

    // Eligible version, parameters diff, outside of cohort
    {
      final ClusterDescription clusterDescription =
          createProvisionedCluster_withVersion(
              TEST_DEDICATED_MONGODB_MAJOR_VERSION, "8.0.1", false);
      final Cluster cluster =
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescriptionProcessArgsUpdatable args =
          _clusterDescriptionProcessArgsDao
              .findForUpdate(
                  clusterDescription.getName(), clusterDescription.getGroupId(), Type.STANDARD)
              .get();

      clearNeedsPublish();
      setReleaseCohorts(_ndsGroup, new ReleaseCohorts(100, 100));
      reloadNdsGroup();

      final ProcessArguments2_6 newParameters = args.getPhasedVersionShardArg().get();
      newParameters.setSetParameterMap(
          Map.of("referenceArgument", "referenceValue", "newArgument", "newValue"));
      final ClusterDescriptionProcessArgsUpdatable newArgs =
          args.copy().setPhasedShardArgs(newParameters).build();
      _clusterDescriptionProcessArgsDao.save(newArgs);

      final MaintenanceCheckResult state =
          _maintenanceSvc.refreshMongoDBParameters(
              _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG);
      assertEquals(MaintenanceType.MONGODB_PARAMETERS_UPDATED, state.maintenanceType());
      assertFalse(state.needMaintenance());
      assertFalse(state.isCriticalRelease());
      final ClusterDescription clusterDescriptionUpdated =
          _clusterDescriptionDao
              .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    }

    // Eligible version, parameters diff, instance size not allowed
    {
      final ClusterDescription clusterDescription =
          createProvisionedCluster_withVersion(
              TEST_DEDICATED_MONGODB_MAJOR_VERSION, "8.0.1", false);
      final String clusterInstanceSize = "M30";
      final String otherInstanceSize = "M20";

      // test assumptions
      Stream.of(NodeType.values())
          .forEach(
              n -> {
                final Set<NDSInstanceSize> instanceSizes = clusterDescription.getInstanceSizes(n);
                if (n == NodeType.ELECTABLE) {
                  assertEquals(1, instanceSizes.size());
                  assertTrue(
                      instanceSizes.contains(AWSNDSInstanceSize.valueOf(clusterInstanceSize)));
                } else {
                  assertEquals(Set.of(), instanceSizes);
                }
              });
      final Cluster cluster =
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescriptionProcessArgsUpdatable args =
          _clusterDescriptionProcessArgsDao
              .findForUpdate(
                  clusterDescription.getName(), clusterDescription.getGroupId(), Type.STANDARD)
              .get();
      phasedVersionParameters.setPhasedReleaseCriteria(
          new PhasedReleaseCriteria(
              1,
              1,
              Map.of("referenceArgument", "referenceValue"),
              null,
              null,
              List.of(otherInstanceSize)));
      _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

      clearNeedsPublish();
      setReleaseCohorts(_ndsGroup, new ReleaseCohorts(1, 1));
      reloadNdsGroup();

      final ProcessArguments2_6 newParameters = args.getPhasedVersionShardArg().get();
      newParameters.setSetParameterMap(
          Map.of("referenceArgument", "referenceValue", "newArgument", "newValue"));
      final ClusterDescriptionProcessArgsUpdatable newArgs =
          args.copy().setPhasedShardArgs(newParameters).build();
      _clusterDescriptionProcessArgsDao.save(newArgs);

      final MaintenanceCheckResult state =
          _maintenanceSvc.refreshMongoDBParameters(
              _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG);
      assertEquals(MaintenanceType.MONGODB_PARAMETERS_UPDATED, state.maintenanceType());
      assertFalse(state.needMaintenance());
      assertFalse(state.isCriticalRelease());
      final ClusterDescription clusterDescriptionUpdated =
          _clusterDescriptionDao
              .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    }

    // Eligible version, parameters diff, instance size is allowed
    {
      final ClusterDescription clusterDescription =
          createProvisionedCluster_withVersion(
              TEST_DEDICATED_MONGODB_MAJOR_VERSION, "8.0.1", false);
      final String clusterInstanceSize = "M30";

      // test assumptions
      Stream.of(NodeType.values())
          .forEach(
              n -> {
                final Set<NDSInstanceSize> instanceSizes = clusterDescription.getInstanceSizes(n);
                if (n == NodeType.ELECTABLE) {
                  assertEquals(1, instanceSizes.size());
                  assertTrue(
                      instanceSizes.contains(AWSNDSInstanceSize.valueOf(clusterInstanceSize)));
                } else {
                  assertEquals(Set.of(), instanceSizes);
                }
              });
      final Cluster cluster =
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      final ClusterDescriptionProcessArgsUpdatable args =
          _clusterDescriptionProcessArgsDao
              .findForUpdate(
                  clusterDescription.getName(), clusterDescription.getGroupId(), Type.STANDARD)
              .get();
      phasedVersionParameters.setPhasedReleaseCriteria(
          new PhasedReleaseCriteria(
              1,
              1,
              Map.of("referenceArgument", "referenceValue"),
              null,
              null,
              List.of(clusterInstanceSize)));
      _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

      clearNeedsPublish();
      setReleaseCohorts(_ndsGroup, new ReleaseCohorts(1, 1));
      reloadNdsGroup();

      final ProcessArguments2_6 newParameters = args.getPhasedVersionShardArg().get();
      newParameters.setSetParameterMap(
          Map.of("referenceArgument", "referenceValue", "newArgument", "newValue"));
      final ClusterDescriptionProcessArgsUpdatable newArgs =
          args.copy().setPhasedShardArgs(newParameters).build();
      _clusterDescriptionProcessArgsDao.save(newArgs);

      final MaintenanceCheckResult state =
          _maintenanceSvc.refreshMongoDBParameters(
              _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG);
      assertEquals(MaintenanceType.MONGODB_PARAMETERS_UPDATED, state.maintenanceType());
      assertTrue(state.needMaintenance());
      assertFalse(state.isCriticalRelease());
      final ClusterDescription clusterDescriptionUpdated =
          _clusterDescriptionDao
              .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      assertTrue(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    }
  }

  // This scenario should not normally be possible in production, as the phased version data is
  // validated against having parameter percent without a list of parameters. It is still useful
  // to ensure that no updates would be performed in face of an in
  @Test
  public void testRefreshMongoDBParameters_noParameters() throws Exception {
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0.1")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 50, null, null, null, null));
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(100, 50));
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    // Eligible version, no parameters set
    {
      final ClusterDescription clusterDescription =
          createProvisionedCluster_withVersion(
              TEST_DEDICATED_MONGODB_MAJOR_VERSION, "8.0.1", false);
      final Cluster cluster =
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();

      clearNeedsPublish();

      final MaintenanceCheckResult state =
          _maintenanceSvc.refreshMongoDBParameters(
              _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG);
      assertEquals(MaintenanceType.MONGODB_PARAMETERS_UPDATED, state.maintenanceType());
      assertFalse(state.needMaintenance());
      assertFalse(state.isCriticalRelease());
      final ClusterDescription clusterDescriptionUpdated =
          _clusterDescriptionDao
              .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    }

    // Eligible version, cluster already has custom parameters.
    {
      final ClusterDescription clusterDescription =
          createProvisionedCluster_withVersion(
              TEST_DEDICATED_MONGODB_MAJOR_VERSION, "8.0.1", false);
      final Cluster cluster =
          _clusterSvc
              .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();

      clearNeedsPublish();

      final ClusterDescriptionProcessArgsUpdatable args =
          _clusterDescriptionProcessArgsDao
              .findForUpdate(
                  clusterDescription.getName(), clusterDescription.getGroupId(), Type.STANDARD)
              .get();

      args.getPhasedVersionShardArg()
          .get()
          .setSetParameterMap(
              Map.of("referenceArgument", "referenceValue", "newArgument", "newValue"));
      _clusterDescriptionProcessArgsDao.save(args);

      final MaintenanceCheckResult state =
          _maintenanceSvc.refreshMongoDBParameters(
              _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG);
      assertEquals(MaintenanceType.MONGODB_PARAMETERS_UPDATED, state.maintenanceType());
      assertFalse(state.needMaintenance());
      assertFalse(state.isCriticalRelease());
      final ClusterDescription clusterDescriptionUpdated =
          _clusterDescriptionDao
              .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
              .get();
      assertFalse(clusterDescriptionUpdated.getNeedsMongoDBConfigPublishAfter().isPresent());
    }
  }

  // Parameter rollouts are not supported for custom builds.
  @Test
  public void testRefreshMongoDBParameters_customBuild() throws Exception {
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0.1")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(100, 50, null, null, null, null));
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

    _customMongoDbBuildSvc.addCustomBuild(
        new MongoDbBuild.Builder()
            .trueName("8.0.0-test")
            .url("s3://custom.build.path")
            .platform(Platforms.linux)
            .architecture(InternalArchitectures.AARCH64.getName())
            .gitVersion("deadbeef")
            .build());

    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(100, 50));
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);

    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion("8.0.0-test", "8.0.0-test", false);
    final Cluster cluster =
        _clusterSvc
            .getActiveCluster(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();

    clearNeedsPublish();

    final MaintenanceCheckResult state =
        _maintenanceSvc.refreshMongoDBParameters(
            _ndsGroup, _group, cluster, _currentTime, new HashMap<>(), LOG);
    assertEquals(MaintenanceType.MONGODB_PARAMETERS_UPDATED, state.maintenanceType());
    assertFalse(state.needMaintenance());
    assertFalse(state.isCriticalRelease());
  }

  @Test
  public void testGetIsInVersionReleaseCohort() throws Exception {
    // CD version percent larger than the group's cohort values.
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, "7.2.1")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(70, 70, null, null, null, null));
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));

    final ClusterDescription cd = createProvisionedCluster_withCDVersion(false);
    setReleaseCohorts(_ndsGroup, new ReleaseCohorts(80, 10));

    assertFalse(
        _maintenanceSvc.getIsInOngoingPhasedVersionRelease(_ndsGroup.getGroupId(), cd.getName()));
    // phased version is paused
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            phasedVersionParameters
                .setIsPaused(true)
                .setPhasedReleaseCriteria(
                    new PhasedReleaseCriteria(90, 90, null, null, null, null))));
    assertFalse(_maintenanceSvc.getIsInOngoingPhasedVersionRelease(cd.getGroupId(), cd.getName()));

    // in cohort and not paused
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters.setIsPaused(false)));
    assertTrue(_maintenanceSvc.getIsInOngoingPhasedVersionRelease(cd.getGroupId(), cd.getName()));

    final Set<NDSInstanceSize> instanceSizes = cd.getInstanceSizes(NodeType.ELECTABLE);
    // test assumption
    assertEquals(Set.of(AWSNDSInstanceSize.M30), instanceSizes);

    // instance size disallowed
    final PhasedReleaseCriteria instanceSizeDisallowed =
        new PhasedReleaseCriteria(90, 90, null, null, null, List.of("M10"));
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            phasedVersionParameters.setPhasedReleaseCriteria(instanceSizeDisallowed)));
    assertFalse(_maintenanceSvc.getIsInOngoingPhasedVersionRelease(cd.getGroupId(), cd.getName()));

    // instance size explicitly allowed
    final PhasedReleaseCriteria instanceSizeAllowed =
        new PhasedReleaseCriteria(
            90, 90, null, null, null, instanceSizes.stream().map(NDSInstanceSize::name).toList());
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            phasedVersionParameters.setPhasedReleaseCriteria(instanceSizeAllowed)));
    assertTrue(_maintenanceSvc.getIsInOngoingPhasedVersionRelease(cd.getGroupId(), cd.getName()));
  }

  @Test
  public void testGenerateMaintenanceStartedAuditsIfNecessary_noAuditsGenerated() throws Exception {
    final MaintenanceCheckResult checkResultNeedsFalse =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.AUTOMATION_AGENT_VERSION_UPDATED)
            .needMaintenance(false)
            .maintenanceRelease(MAINTENANCE_WINDOW)
            .build();

    final MaintenanceCheckResult checkResultCritical =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.MONGOT_VERSION_UPDATED)
            .needMaintenance(true)
            .maintenanceRelease(new MaintenanceRelease(null, null, SchedulingBehavior.CRITICAL))
            .build();

    setMaintenanceWindowToNow(_ndsGroup);

    // No check result needs maintenance
    {
      _maintenanceSvc.generateMaintenanceStartedAuditsIfNecessary(
          _ndsGroup, _group, List.of(checkResultNeedsFalse));

      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(), NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE))
          .hasSize(0);
      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(),
                  NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE_OUTSIDE_OF_PROTECTED_HOURS))
          .hasSize(0);
    }

    // All check results need maintenance and are critical
    {
      _maintenanceSvc.generateMaintenanceStartedAuditsIfNecessary(
          _ndsGroup, _group, List.of(checkResultCritical));

      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(), NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE))
          .hasSize(0);
      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(),
                  NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE_OUTSIDE_OF_PROTECTED_HOURS))
          .hasSize(0);
    }

    // Maintenance is needed but maintenance started Notification already sent
    {
      _ndsGroupMaintenanceDao.setMaintenanceStartedNotificationSentForGroup(
          _ndsGroup.getGroupId(), _currentTime);

      _maintenanceSvc.generateMaintenanceStartedAuditsIfNecessary(
          reloadNdsGroup(), _group, List.of(checkResultCritical));

      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(), NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE))
          .hasSize(0);
      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(),
                  NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE_OUTSIDE_OF_PROTECTED_HOURS))
          .hasSize(0);
    }
  }

  @Test
  public void testGenerateMaintenanceStartedAuditsIfNecessary_auditsGenerated() throws Exception {
    final MaintenanceCheckResult checkResultNeedsTrueDuringMaintenance =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
            .needMaintenance(true)
            .maintenanceRelease(MAINTENANCE_WINDOW)
            .build();

    final MaintenanceCheckResult checkResultNeedsTrueDuringProtected =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.TLS_CERTIFICATE_ROTATED)
            .needMaintenance(true)
            .maintenanceRelease(MaintenanceRelease.RESPECT_PROTECTED_HOURS)
            .build();

    // Maintenance is needed but all maintenances are during maintenance window
    {
      setMaintenanceWindowToNow(_ndsGroup);

      _maintenanceSvc.generateMaintenanceStartedAuditsIfNecessary(
          _ndsGroup, _group, List.of(checkResultNeedsTrueDuringMaintenance));

      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(), NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE))
          .hasSize(1);
      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(),
                  NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE_OUTSIDE_OF_PROTECTED_HOURS))
          .hasSize(0);
    }

    // Maintenance is needed but all maintenances are for protected hours
    {
      _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(_ndsGroup.getGroupId(), null);
      setMaintenanceWindowToOutsideProtectedHours(_ndsGroup);

      _maintenanceSvc.generateMaintenanceStartedAuditsIfNecessary(
          _ndsGroup, _group, List.of(checkResultNeedsTrueDuringProtected));

      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(), NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE))
          .hasSize(1);
      assertThat(
              _auditSvc.findByEventTypeForGroup(
                  _group.getId(),
                  NDSAudit.Type.PROJECT_SCHEDULED_MAINTENANCE_OUTSIDE_OF_PROTECTED_HOURS))
          .hasSize(1);
    }
  }

  @Test
  public void
      testQueuedBeforeAdvancedNotificationSendDate_true_when_checkResult_with_hardwareDetails()
          throws Exception {
    final Date advancedNotificationSendDate = _currentTime;
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);

    final InstanceHardwareDetails instanceHardwareDetails =
        new InstanceHardwareDetails("hostname", new Date(), List.of(), null, null);

    final List<NdsMaintenanceHistory> maintenanceHistories =
        List.of(
            NdsMaintenanceHistory.builder()
                .setGroupId(_ndsGroup.getGroupId())
                .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                .setState(NdsMaintenanceHistory.State.PENDING)
                .setCreatedAt(DateUtils.addMinutes(advancedNotificationSendDate, -10))
                .setTargetMaintenanceDate(_currentTime)
                .setClusterName(clusterDescription.getName())
                .setInstanceHardwareDetails(instanceHardwareDetails)
                .build());
    _ndsMaintenanceHistorySvc.savePendingMaintenances(maintenanceHistories);

    setMaintenanceWindowToNow(_ndsGroup);
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(
        _ndsGroup.getGroupId(), advancedNotificationSendDate);
    reloadNdsGroup();

    assertThat(
            _maintenanceSvc.queuedBeforeAdvancedNotificationSendDate(
                _ndsGroup.getGroupId(),
                _ndsGroup.getMaintenanceWindow(),
                MaintenanceHistoryMatchCriteria.builder()
                    .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                    .setClusterName(clusterDescription.getName())
                    .setInstanceHardwareDetails(instanceHardwareDetails)
                    .build()))
        .isTrue();
  }

  @Test
  public void testQueuedBeforeAdvancedNotificationSendDate_true_when_simple_criteria()
      throws Exception {
    final Date advancedNotificationSendDate = _currentTime;
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);

    final List<NdsMaintenanceHistory> maintenanceHistories =
        List.of(
            NdsMaintenanceHistory.builder()
                .setGroupId(_ndsGroup.getGroupId())
                .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                .setState(NdsMaintenanceHistory.State.PENDING)
                .setCreatedAt(DateUtils.addMinutes(advancedNotificationSendDate, -10))
                .setTargetMaintenanceDate(_currentTime)
                .setClusterName(clusterDescription.getName())
                .build());
    _ndsMaintenanceHistorySvc.savePendingMaintenances(maintenanceHistories);

    setMaintenanceWindowToNow(_ndsGroup);
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(
        _ndsGroup.getGroupId(), advancedNotificationSendDate);
    reloadNdsGroup();

    assertThat(
            _maintenanceSvc.queuedBeforeAdvancedNotificationSendDate(
                _ndsGroup.getGroupId(),
                _ndsGroup.getMaintenanceWindow(),
                MaintenanceHistoryMatchCriteria.builder()
                    .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                    .setClusterName(clusterDescription.getName())
                    .build()))
        .isTrue();
  }

  @Test
  public void testQueuedBeforeAdvancedNotificationSendDate_false_when_multiple_match()
      throws Exception {
    final Date advancedNotificationSendDate = _currentTime;
    final List<NdsMaintenanceHistory> maintenanceHistories =
        List.of(
            NdsMaintenanceHistory.builder()
                .setGroupId(_ndsGroup.getGroupId())
                .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                .setState(NdsMaintenanceHistory.State.PENDING)
                .setCreatedAt(DateUtils.addMinutes(advancedNotificationSendDate, -10))
                .setTargetMaintenanceDate(_currentTime)
                .build(),
            NdsMaintenanceHistory.builder()
                .setGroupId(_ndsGroup.getGroupId())
                .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                .setState(NdsMaintenanceHistory.State.PENDING)
                .setCreatedAt(
                    DateUtils.addMinutes(
                        advancedNotificationSendDate,
                        1)) // history entry that is not before advancedDate
                .setTargetMaintenanceDate(_currentTime)
                .build());
    _ndsMaintenanceHistorySvc.savePendingMaintenances(maintenanceHistories);

    setMaintenanceWindowToNow(_ndsGroup);
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(
        _ndsGroup.getGroupId(), advancedNotificationSendDate);
    reloadNdsGroup();

    assertThat(
            _maintenanceSvc.queuedBeforeAdvancedNotificationSendDate(
                _ndsGroup.getGroupId(),
                _ndsGroup.getMaintenanceWindow(),
                MaintenanceHistoryMatchCriteria.builder()
                    .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                    .build()))
        .isFalse();
  }

  @Test
  public void testQueuedBeforeAdvancedNotificationSendDate_false_when_clusterName_Missing()
      throws Exception {
    final Date advancedNotificationSendDate = _currentTime;
    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString(),
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getVersion(),
            false);

    final List<NdsMaintenanceHistory> maintenanceHistories =
        List.of(
            NdsMaintenanceHistory.builder()
                .setGroupId(_ndsGroup.getGroupId())
                .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                .setState(NdsMaintenanceHistory.State.PENDING)
                .setCreatedAt(DateUtils.addMinutes(advancedNotificationSendDate, -10))
                .setTargetMaintenanceDate(advancedNotificationSendDate)
                .setClusterName(null)
                .build());
    _ndsMaintenanceHistorySvc.savePendingMaintenances(maintenanceHistories);

    setMaintenanceWindowToNow(_ndsGroup);
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(
        _ndsGroup.getGroupId(), advancedNotificationSendDate);
    reloadNdsGroup();

    assertThat(
            _maintenanceSvc.queuedBeforeAdvancedNotificationSendDate(
                _ndsGroup.getGroupId(),
                _ndsGroup.getMaintenanceWindow(),
                MaintenanceHistoryMatchCriteria.builder()
                    .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                    .setClusterName(clusterDescription.getName())
                    .build()))
        .isFalse();
  }

  @Test
  public void testQueuedBeforeAdvancedNotificationSendDate_false_when_no_history_pending()
      throws Exception {
    final Date advancedNotificationSendDate = _currentTime;
    final List<NdsMaintenanceHistory> maintenanceHistories =
        List.of(
            NdsMaintenanceHistory.builder()
                .setGroupId(_ndsGroup.getGroupId())
                .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                .setState(NdsMaintenanceHistory.State.NO_LONGER_NEEDED)
                .setCreatedAt(DateUtils.addMinutes(advancedNotificationSendDate, -10))
                .setTargetMaintenanceDate(advancedNotificationSendDate)
                .build());
    _ndsMaintenanceHistorySvc.savePendingMaintenances(maintenanceHistories);

    setMaintenanceWindowToNow(_ndsGroup);
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(
        _ndsGroup.getGroupId(), advancedNotificationSendDate);
    reloadNdsGroup();

    assertThat(
            _maintenanceSvc.queuedBeforeAdvancedNotificationSendDate(
                _ndsGroup.getGroupId(),
                _ndsGroup.getMaintenanceWindow(),
                MaintenanceHistoryMatchCriteria.builder()
                    .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                    .build()))
        .isFalse();
  }

  @Test
  public void testQueuedBeforeAdvancedNotificationSendDate_false_when_notificationSendDate_isEmpty()
      throws Exception {
    setMaintenanceWindowToNow(_ndsGroup);
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(_ndsGroup.getGroupId(), null);
    reloadNdsGroup();

    assertThat(
            _maintenanceSvc.queuedBeforeAdvancedNotificationSendDate(
                _ndsGroup.getGroupId(),
                _ndsGroup.getMaintenanceWindow(),
                MaintenanceHistoryMatchCriteria.builder()
                    .setMaintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
                    .build()))
        .isFalse();
  }

  @Test
  public void testIsMaintenanceCompleted_when_window_isUserDefined() throws Exception {
    final Date advancedNotificationSendDate = new Date();
    final Date beforeAdvancedNotifDate = DateUtils.addMinutes(advancedNotificationSendDate, -10);
    final Date afterAdvancedNotifDate = DateUtils.addMinutes(advancedNotificationSendDate, 10);

    final List<ClusterDescription> clusterDescriptions =
        List.of(
            new ClusterDescription(
                    NDSModelTestFactory.getAWSClusterDescription(
                        _ndsGroup.getGroupId(), "cluster0"))
                .copy()
                .setState(ClusterDescription.State.IDLE)
                .build(),
            new ClusterDescription(
                    NDSModelTestFactory.getAWSClusterDescription(
                        _ndsGroup.getGroupId(), "cluster1"))
                .copy()
                .setState(ClusterDescription.State.IDLE)
                .build());

    final NDSGroupMaintenanceWindow maintenanceWindow =
        NDSGroupMaintenanceWindow.builder()
            .hourOfDay(10)
            .dayOfWeek(2)
            .isUserDefined(true)
            .advanceNotificationSendDate(advancedNotificationSendDate)
            .build();
    setMaintenanceWindow(_ndsGroup, maintenanceWindow);

    final MaintenanceCheckResult checkResultBeforeAdvNeededFalse =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
            .clusterDescription(clusterDescriptions.get(0))
            .needMaintenance(false)
            .maintenanceRelease(MaintenanceRelease.MAINTENANCE_WINDOW)
            .build();
    savePendingHistoryForCheckResult(checkResultBeforeAdvNeededFalse, beforeAdvancedNotifDate);

    final MaintenanceCheckResult checkResultBeforeAdvNeededFalse2 =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.AUTOMATION_AGENT_VERSION_UPDATED)
            .needMaintenance(false)
            .maintenanceRelease(MaintenanceRelease.MAINTENANCE_WINDOW)
            .build();
    savePendingHistoryForCheckResult(checkResultBeforeAdvNeededFalse2, beforeAdvancedNotifDate);

    final MaintenanceCheckResult checkResultBeforeAdvNeededTrue =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
            .clusterDescription(clusterDescriptions.get(1))
            .needMaintenance(true)
            .maintenanceRelease(MaintenanceRelease.MAINTENANCE_WINDOW)
            .build();
    savePendingHistoryForCheckResult(checkResultBeforeAdvNeededTrue, beforeAdvancedNotifDate);

    final MaintenanceCheckResult checkResultAfterAdvNeededTrue =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.BI_CONNECTOR_VERSION_UPDATED)
            .needMaintenance(true)
            .maintenanceRelease(MaintenanceRelease.MAINTENANCE_WINDOW)
            .build();
    savePendingHistoryForCheckResult(checkResultAfterAdvNeededTrue, afterAdvancedNotifDate);

    // No check results are provided maintenance is complete
    assertTrue(_maintenanceSvc.isMaintenanceCompleted(_ndsGroup, List.of(), clusterDescriptions));

    // All check results are before maintenance and not needed
    assertTrue(
        _maintenanceSvc.isMaintenanceCompleted(
            _ndsGroup,
            List.of(checkResultBeforeAdvNeededFalse, checkResultBeforeAdvNeededFalse2),
            clusterDescriptions));

    // All check results before the advanced notification are not needed the one after is ignored
    assertTrue(
        _maintenanceSvc.isMaintenanceCompleted(
            _ndsGroup,
            List.of(
                checkResultBeforeAdvNeededFalse,
                checkResultBeforeAdvNeededFalse2,
                checkResultAfterAdvNeededTrue),
            clusterDescriptions));

    // A check result before advanced notification is needed
    assertFalse(
        _maintenanceSvc.isMaintenanceCompleted(
            _ndsGroup,
            List.of(checkResultBeforeAdvNeededFalse, checkResultBeforeAdvNeededTrue),
            clusterDescriptions));
  }

  @Test
  public void testIsMaintenanceCompleted_when_window_not_userDefined() {
    final Date advancedNotificationSendDate = new Date();
    final Date beforeAdvancedNotifDate = DateUtils.addMinutes(advancedNotificationSendDate, -10);
    final Date afterAdvancedNotifDate = DateUtils.addMinutes(advancedNotificationSendDate, 10);

    final List<ClusterDescription> clusterDescriptions =
        List.of(
            new ClusterDescription(
                    NDSModelTestFactory.getAWSClusterDescription(
                        _ndsGroup.getGroupId(), "cluster0"))
                .copy()
                .setState(ClusterDescription.State.IDLE)
                .build(),
            new ClusterDescription(
                    NDSModelTestFactory.getAWSClusterDescription(
                        _ndsGroup.getGroupId(), "cluster1"))
                .copy()
                .setState(ClusterDescription.State.IDLE)
                .build());

    _ndsGroupMaintenanceDao.resetToSystemDefaultWindow(_ndsGroup.getGroupId());
    _ndsGroupMaintenanceDao.setMaintenanceNeededDate(_ndsGroup.getGroupId(), new Date());
    reloadNdsGroup();

    final MaintenanceCheckResult checkResultBeforeAdvNeededFalse =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.MONGODB_VERSION_UPDATED)
            .clusterDescription(clusterDescriptions.get(0))
            .needMaintenance(false)
            .maintenanceRelease(MaintenanceRelease.MAINTENANCE_WINDOW)
            .build();
    savePendingHistoryForCheckResult(checkResultBeforeAdvNeededFalse, beforeAdvancedNotifDate);

    final MaintenanceCheckResult checkResultBeforeAdvNeededFalse2 =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.AUTOMATION_AGENT_VERSION_UPDATED)
            .needMaintenance(false)
            .maintenanceRelease(MaintenanceRelease.MAINTENANCE_WINDOW)
            .build();
    savePendingHistoryForCheckResult(checkResultBeforeAdvNeededFalse2, beforeAdvancedNotifDate);

    final MaintenanceCheckResult checkResultAfterAdvNeededTrue =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.BI_CONNECTOR_VERSION_UPDATED)
            .needMaintenance(true)
            .maintenanceRelease(MaintenanceRelease.MAINTENANCE_WINDOW)
            .build();
    savePendingHistoryForCheckResult(checkResultAfterAdvNeededTrue, afterAdvancedNotifDate);

    final MaintenanceCheckResult checkResultAfterAdvNeededFalse =
        MaintenanceCheckResult.builder()
            .maintenanceType(MaintenanceType.BI_CONNECTOR_VERSION_UPDATED)
            .needMaintenance(false)
            .maintenanceRelease(MaintenanceRelease.MAINTENANCE_WINDOW)
            .build();
    savePendingHistoryForCheckResult(checkResultAfterAdvNeededFalse, afterAdvancedNotifDate);

    // Note - When the window has user defined as false, the date on the history entries
    // is ignored. These windows are know as system maintenance windows.

    // No check results are provided maintenance is complete
    assertTrue(_maintenanceSvc.isMaintenanceCompleted(_ndsGroup, List.of(), clusterDescriptions));

    // All check results are before maintenance and not needed
    assertTrue(
        _maintenanceSvc.isMaintenanceCompleted(
            _ndsGroup,
            List.of(checkResultBeforeAdvNeededFalse, checkResultBeforeAdvNeededFalse2),
            clusterDescriptions));

    // All check results not needed regardless of advanced notification date result in maintenance
    // completing
    assertTrue(
        _maintenanceSvc.isMaintenanceCompleted(
            _ndsGroup,
            List.of(
                checkResultAfterAdvNeededFalse,
                checkResultBeforeAdvNeededFalse,
                checkResultBeforeAdvNeededFalse2),
            clusterDescriptions));

    // One needed check result results in maintenance not completing
    assertFalse(
        _maintenanceSvc.isMaintenanceCompleted(
            _ndsGroup,
            List.of(
                checkResultBeforeAdvNeededFalse,
                checkResultBeforeAdvNeededFalse2,
                checkResultAfterAdvNeededTrue),
            clusterDescriptions));
  }

  private void savePendingHistoryForCheckResult(
      final MaintenanceCheckResult pCheckResult, final Date pCreatedAtDate) {
    _ndsMaintenanceHistorySvc.savePendingMaintenances(
        List.of(checkResultToNdsHistory(pCheckResult, _ndsGroup, pCreatedAtDate)));
  }

  @Test
  public void testRefreshMongoDBVersions_withIFRRolloutMode() throws Exception {
    // Setup: Create a phased version with IFR rollout mode
    final SoftwareType softwareType = SoftwareType.MONGODB_8_2;
    final String majorVersion = "8.2";
    final String targetVersion = "8.2.1";
    final String currentVersion = "8.2.0";
    final int currentWave = 2;
    final ObjectId expId = new ObjectId();

    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(majorVersion, currentVersion, false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);
    final Cluster cluster =
        _clusterSvc.getActiveCluster(clusterDescription.getGroupId(), clusterDescription);
    // Create wave allocation that enables the cluster to be eligible for the rollout
    final WaveAllocationInternal waveAllocation =
        new WaveAllocationInternal(
            expId, clusterDescription.getUniqueId(), currentWave, new Date(), new Date());
    _waveDao.insertMajority(waveAllocation);

    // Regular wave
    {
      // Create the phased version with IFR rollout mode and IFR state
      final PhasedVersionParameters phasedVersionParameters =
          new PhasedVersionParameters(softwareType, targetVersion)
              .setReleaseMode(ReleaseMode.IFR)
              .setIfrState(
                  new IFRState(
                      expId,
                      ExperimentStatus.LIVE,
                      RolloutType.BINARY,
                      currentWave,
                      WaveStatus.FINALIZED));
      final PhasedVersion ifrPhasedVersion =
          new PhasedVersionFactory().createPhasedVersion(phasedVersionParameters);
      _phasedVersionSvc.save(ifrPhasedVersion);

      final MaintenanceCheckResult result =
          _maintenanceSvc.refreshMongoDBVersions(
              _ndsGroup, _group, cluster, new Date(), _numMonitoringAddedMap, LOG);

      assertTrue(result.needMaintenance());
      assertEquals(SchedulingBehavior.DURING_MAINTENANCE_WINDOW, result.getSchedulingBehavior());
      assertEquals(MaintenanceType.MONGODB_VERSION_UPDATED, result.maintenanceType());
      assertEquals(currentVersion, result.getFromVersion());
      assertEquals(targetVersion, result.getToVersion());

      // Verify ifr event is emitted
      List<IFREvent> ifrEvents =
          _ifrEventDao.findEventsByClusterAndExperiment(clusterDescription.getUniqueId(), expId);
      assertEquals(1, ifrEvents.size());
      assertEquals(IFREventType.UPDATE_DECISION, ifrEvents.get(0).type());
      assertEquals(currentWave, ifrEvents.get(0).wave());
      assertFalse(ifrEvents.get(0).hasOverride());
      _ifrEventDao.deleteManyMajority(new BasicDBObject());

      // Verify that the version was determined correctly for IFR mode
      final String actualTargetVersion =
          _maintenanceSvc.getVersionForCluster(
              _ndsGroup,
              cluster,
              ifrPhasedVersion,
              VersionUtils.parse(currentVersion),
              VersionUtils.parse("8.2.0"));
      assertEquals(targetVersion, actualTargetVersion);
    }

    // Cooldown wave
    {
      // Create the phased version with IFR rollout mode and IFR state
      final PhasedVersionParameters phasedVersionParameters =
          new PhasedVersionParameters(softwareType, targetVersion)
              .setReleaseMode(ReleaseMode.IFR)
              .setIfrState(
                  new IFRState(
                      expId,
                      ExperimentStatus.COOLDOWN,
                      RolloutType.BINARY,
                      currentWave,
                      WaveStatus.FINALIZED));
      final PhasedVersion ifrPhasedVersion =
          new PhasedVersionFactory().createPhasedVersion(phasedVersionParameters);
      _phasedVersionSvc.save(ifrPhasedVersion);

      final MaintenanceCheckResult result =
          _maintenanceSvc.refreshMongoDBVersions(
              _ndsGroup, _group, cluster, new Date(), _numMonitoringAddedMap, LOG);

      assertTrue(result.needMaintenance());
      assertEquals(SchedulingBehavior.DURING_MAINTENANCE_WINDOW, result.getSchedulingBehavior());
      assertEquals(MaintenanceType.MONGODB_VERSION_UPDATED, result.maintenanceType());
      assertEquals(currentVersion, result.getFromVersion());
      assertEquals(targetVersion, result.getToVersion());

      // Verify ifr event is emitted
      List<IFREvent> ifrEvents =
          _ifrEventDao.findEventsByClusterAndExperiment(clusterDescription.getUniqueId(), expId);
      assertEquals(1, ifrEvents.size());
      assertEquals(IFREventType.UPDATE_DECISION, ifrEvents.get(0).type());
      assertEquals(currentWave, ifrEvents.get(0).wave());
      assertFalse(ifrEvents.get(0).hasOverride());
      _ifrEventDao.deleteManyMajority(new BasicDBObject());

      // Verify that the version was determined correctly for IFR mode
      final String actualTargetVersion =
          _maintenanceSvc.getVersionForCluster(
              _ndsGroup,
              cluster,
              ifrPhasedVersion,
              VersionUtils.parse(currentVersion),
              VersionUtils.parse("8.2.0"));
      assertEquals(targetVersion, actualTargetVersion);
    }

    // Reconciliation phase
    {
      // Create the phased version with IFR rollout mode and IFR state
      final PhasedVersionParameters phasedVersionParameters =
          new PhasedVersionParameters(softwareType, targetVersion)
              .setReleaseMode(ReleaseMode.IFR)
              .setIfrState(
                  new IFRState(
                      expId,
                      ExperimentStatus.COMPLETE,
                      RolloutType.BINARY,
                      currentWave + 1,
                      WaveStatus.FINALIZED));
      final PhasedVersion ifrPhasedVersion =
          new PhasedVersionFactory().createPhasedVersion(phasedVersionParameters);
      _phasedVersionSvc.save(ifrPhasedVersion);

      final MaintenanceCheckResult result =
          _maintenanceSvc.refreshMongoDBVersions(
              _ndsGroup, _group, cluster, new Date(), _numMonitoringAddedMap, LOG);

      assertTrue(result.needMaintenance());
      assertEquals(SchedulingBehavior.DURING_MAINTENANCE_WINDOW, result.getSchedulingBehavior());
      assertEquals(MaintenanceType.MONGODB_VERSION_UPDATED, result.maintenanceType());
      assertEquals(currentVersion, result.getFromVersion());
      assertEquals(targetVersion, result.getToVersion());

      // Verify ifr event is emitted
      List<IFREvent> ifrEvents =
          _ifrEventDao.findEventsByClusterAndExperiment(clusterDescription.getUniqueId(), expId);
      assertEquals(1, ifrEvents.size());
      assertEquals(IFREventType.UPDATE_DECISION, ifrEvents.get(0).type());
      assertEquals(currentWave + 1, ifrEvents.get(0).wave());
      assertFalse(ifrEvents.get(0).hasOverride());
      _ifrEventDao.deleteManyMajority(new BasicDBObject());

      // Verify that the version was determined correctly for IFR mode
      final String actualTargetVersion =
          _maintenanceSvc.getVersionForCluster(
              _ndsGroup,
              cluster,
              ifrPhasedVersion,
              VersionUtils.parse(currentVersion),
              VersionUtils.parse("8.2.0"));
      assertEquals(targetVersion, actualTargetVersion);
    }
  }

  @Test
  public void testRefreshMongoDBVersions_cancelledIFRExperiment() throws Exception {
    // Setup: Create a phased version with IFR rollout mode
    final SoftwareType softwareType = SoftwareType.MONGODB_8_2;
    final String majorVersion = "8.2";
    final String targetVersion = "8.2.1";
    final String currentVersion = "8.2.0";
    final int currentWave = 2;
    final ObjectId expId = new ObjectId();

    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(majorVersion, currentVersion, false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);
    final Cluster cluster =
        _clusterSvc.getActiveCluster(clusterDescription.getGroupId(), clusterDescription);
    // Create wave allocation that enables the cluster to be eligible for the rollout
    final WaveAllocationInternal waveAllocation =
        new WaveAllocationInternal(
            expId, clusterDescription.getUniqueId(), currentWave, new Date(), new Date());
    _waveDao.insertMajority(waveAllocation);

    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(softwareType, targetVersion)
            .setReleaseMode(ReleaseMode.IFR)
            .setIfrState(
                new IFRState(
                    expId,
                    ExperimentStatus.CANCELLED,
                    RolloutType.BINARY,
                    currentWave,
                    WaveStatus.FINALIZED));
    final PhasedVersion ifrPhasedVersion =
        new PhasedVersionFactory().createPhasedVersion(phasedVersionParameters);
    _phasedVersionSvc.save(ifrPhasedVersion);

    final MaintenanceCheckResult result =
        _maintenanceSvc.refreshMongoDBVersions(
            _ndsGroup, _group, cluster, new Date(), _numMonitoringAddedMap, LOG);

    assertFalse(result.needMaintenance());

    // Verify no events are emitted.
    List<IFREvent> ifrEvents =
        _ifrEventDao.findEventsByClusterAndExperiment(clusterDescription.getUniqueId(), expId);
    assertEquals(0, ifrEvents.size());

    // Verify that the cluster still has the original version
    final String actualTargetVersion =
        _maintenanceSvc.getVersionForCluster(
            _ndsGroup,
            cluster,
            ifrPhasedVersion,
            VersionUtils.parse(currentVersion),
            VersionUtils.parse("8.2.0"));
    assertEquals(currentVersion, actualTargetVersion);
  }

  @Test
  public void testRefreshMongoDBVersions_withIFRRolloutMode_unactive() throws Exception {
    // Setup: Create a phased version with IFR rollout mode
    final SoftwareType softwareType = SoftwareType.MONGODB_8_2;
    final String majorVersion = "8.2";
    final String targetVersion = "8.2.1";
    final String currentVersion = "8.2.0";
    final int currentWave = 2;
    final ObjectId expId = new ObjectId();

    final ClusterDescription clusterDescription =
        createProvisionedCluster_withVersion(majorVersion, currentVersion, false);
    forceAutomationConfigToPublished(_organization, _group);
    clearNeedsPublish();
    setMaintenanceWindowToNow(_ndsGroup);
    final Cluster cluster =
        _clusterSvc.getActiveCluster(clusterDescription.getGroupId(), clusterDescription);
    // Create wave allocation that enables the cluster to be eligible for the rollout
    final WaveAllocationInternal waveAllocation =
        new WaveAllocationInternal(
            expId, clusterDescription.getUniqueId(), currentWave, new Date(), new Date());
    _waveDao.insertMajority(waveAllocation);

    // Waves are not finalized
    {
      // Create the phased version with IFR rollout mode and IFR state.
      // The wave will not be finalized, so the maintenance should not be allowed.
      final PhasedVersionParameters phasedVersionParameters =
          new PhasedVersionParameters(softwareType, targetVersion)
              .setReleaseMode(ReleaseMode.IFR)
              .setIfrState(
                  new IFRState(
                      expId,
                      ExperimentStatus.LIVE,
                      RolloutType.BINARY,
                      currentWave,
                      WaveStatus.INITIALIZING));
      final PhasedVersion ifrPhasedVersion =
          new PhasedVersionFactory().createPhasedVersion(phasedVersionParameters);
      _phasedVersionSvc.save(ifrPhasedVersion);

      final MaintenanceCheckResult result =
          _maintenanceSvc.refreshMongoDBVersions(
              _ndsGroup, _group, cluster, new Date(), _numMonitoringAddedMap, LOG);
      assertFalse(result.needMaintenance());

      // Verify no ifr event is emitted
      List<IFREvent> ifrEvents =
          _ifrEventDao.findEventsByClusterAndExperiment(clusterDescription.getUniqueId(), expId);
      assertEquals(0, ifrEvents.size());

      // Verify that the version was determined correctly for IFR mode
      final String actualTargetVersion =
          _maintenanceSvc.getVersionForCluster(
              _ndsGroup,
              cluster,
              ifrPhasedVersion,
              VersionUtils.parse(currentVersion),
              VersionUtils.parse("8.2.0"));
      assertEquals(currentVersion, actualTargetVersion);
    }

    // Unsupported experiment status
    {
      // Create the phased version with IFR rollout mode and IFR state.
      // The wave will not be finalized, so the maintenance should not be allowed.
      final PhasedVersionParameters phasedVersionParameters =
          new PhasedVersionParameters(softwareType, targetVersion)
              .setReleaseMode(ReleaseMode.IFR)
              .setIfrState(
                  new IFRState(
                      expId,
                      ExperimentStatus.DRAFT,
                      RolloutType.BINARY,
                      currentWave,
                      WaveStatus.INITIALIZING));
      final PhasedVersion ifrPhasedVersion =
          new PhasedVersionFactory().createPhasedVersion(phasedVersionParameters);
      _phasedVersionSvc.save(ifrPhasedVersion);

      final MaintenanceCheckResult result =
          _maintenanceSvc.refreshMongoDBVersions(
              _ndsGroup, _group, cluster, new Date(), _numMonitoringAddedMap, LOG);
      assertFalse(result.needMaintenance());

      // Verify no ifr event is emitted
      List<IFREvent> ifrEvents =
          _ifrEventDao.findEventsByClusterAndExperiment(clusterDescription.getUniqueId(), expId);
      assertEquals(0, ifrEvents.size());

      // Verify that the version was determined correctly for IFR mode
      final String actualTargetVersion =
          _maintenanceSvc.getVersionForCluster(
              _ndsGroup,
              cluster,
              ifrPhasedVersion,
              VersionUtils.parse(currentVersion),
              VersionUtils.parse("8.2.0"));
      assertEquals(currentVersion, actualTargetVersion);
    }
  }

  private MaintenanceCheckResult runMaintenanceSavingHistory(
      final Supplier<MaintenanceCheckResult> pMaintenanceRunner) {
    final MaintenanceCheckResult maintenanceCheckResult = pMaintenanceRunner.get();
    // CreatedAt is 10 minutes before the currentTime (equal to advanced notification send date)
    final Date createdAt = DateUtils.addMinutes(_currentTime, -10);
    _ndsMaintenanceHistorySvc.savePendingMaintenances(
        List.of(checkResultToNdsHistory(maintenanceCheckResult, _ndsGroup, createdAt)));
    return pMaintenanceRunner.get();
  }

  private List<MaintenanceCheckResult> runMaintenanceSavingHistories(
      final Supplier<List<MaintenanceCheckResult>> pMaintenanceRunner) {
    final List<MaintenanceCheckResult> maintenanceCheckResults = pMaintenanceRunner.get();
    // CreatedAt is 10 minutes before currentTime (equal to advanced notification send date)
    final Date createdAt = DateUtils.addMinutes(_currentTime, -10);
    _ndsMaintenanceHistorySvc.savePendingMaintenances(
        maintenanceCheckResults.stream()
            .map(c -> checkResultToNdsHistory(c, _ndsGroup, createdAt))
            .toList());
    return pMaintenanceRunner.get();
  }

  private static NdsMaintenanceHistory checkResultToNdsHistory(
      final MaintenanceCheckResult pMaintenanceCheckResult,
      final NDSGroup pNDSGroup,
      final Date pCreatedAt) {
    final NdsMaintenanceHistory.Builder maintenanceHistoryBd =
        NdsMaintenanceHistory.builder()
            .setCreatedAt(pCreatedAt)
            .setState(NdsMaintenanceHistory.State.PENDING)
            .setGroupId(pNDSGroup.getGroupId())
            .setTargetMaintenanceDate(new Date())
            .setIsCritical(pMaintenanceCheckResult.isCriticalRelease())
            .setMaintenanceType(pMaintenanceCheckResult.maintenanceType())
            .setSchedulingBehavior(pMaintenanceCheckResult.getSchedulingBehavior())
            .setFromVersion(pMaintenanceCheckResult.getFromVersion())
            .setToVersion(pMaintenanceCheckResult.getToVersion())
            .setClusterName(
                Optional.ofNullable(pMaintenanceCheckResult.clusterDescription())
                    .map(ClusterDescription::getName)
                    .orElse(null));

    if (pMaintenanceCheckResult.maintenanceType() == MaintenanceType.EXTERNAL_MAINTENANCE) {
      maintenanceHistoryBd.setExternalMaintenanceNames(
          pMaintenanceCheckResult.getExternalMaintenanceNames());
    }

    if (pMaintenanceCheckResult.getInstanceHardwareDetails() != null) {
      maintenanceHistoryBd.setInstanceHardwareDetails(
          new InstanceHardwareDetails(
              pMaintenanceCheckResult.getInstanceHardwareDetails().getHostname(),
              pMaintenanceCheckResult.getInstanceHardwareDetails().getRebootRequestedDate(),
              Collections.emptyList(),
              pMaintenanceCheckResult
                  .getInstanceHardwareDetails()
                  .getRebootRequestedChefCommitHash(),
              null));
    }

    return maintenanceHistoryBd.build();
  }

  private void setMaintenanceWindowToNow(final NDSGroup pGroup) throws Exception {
    setMaintenanceWindowInNumDays(pGroup, 0);
  }

  private Pair<Integer, Integer> getCurrentDayOfWeekHourOfDay(final int pDaysAway) {
    final Calendar calendar = getCalendarInstanceForGroup();
    calendar.add(Calendar.DAY_OF_WEEK, pDaysAway);
    return Pair.of(calendar.get(Calendar.DAY_OF_WEEK), calendar.get(Calendar.HOUR_OF_DAY));
  }

  private void setMaintenanceWindowInNumDays(final NDSGroup pGroup, final int pDaysAway)
      throws Exception {
    final Pair<Integer, Integer> weekHour = getCurrentDayOfWeekHourOfDay(pDaysAway);
    final NDSGroupMaintenanceWindow maintenanceWindow =
        new NDSGroupMaintenanceWindow(weekHour.getLeft(), weekHour.getRight());
    setMaintenanceWindow(pGroup, maintenanceWindow);
  }

  private void setMaintenanceWindowToAnotherDay(final NDSGroup pGroup) throws Exception {
    final Calendar calendar = getCalendarInstanceForGroup();
    calendar.roll(Calendar.DAY_OF_WEEK, 1);
    final NDSGroupMaintenanceWindow maintenanceWindow =
        new NDSGroupMaintenanceWindow(
            calendar.get(Calendar.DAY_OF_WEEK), calendar.get(Calendar.HOUR_OF_DAY));
    setMaintenanceWindow(pGroup, maintenanceWindow);
  }

  private void setMaintenanceWindowToOutsideProtectedHours(final NDSGroup pGroup) throws Exception {
    final Calendar calendarStart = getCalendarInstanceForGroup();
    calendarStart.add(Calendar.HOUR_OF_DAY, 2);

    final Calendar calendarEnd = getCalendarInstanceForGroup();
    calendarEnd.add(Calendar.HOUR_OF_DAY, 6);

    final NDSGroupMaintenanceWindow maintenanceWindow =
        new NDSGroupMaintenanceWindow(
            calendarStart.get(Calendar.DAY_OF_WEEK),
            calendarStart.get(Calendar.HOUR_OF_DAY),
            false,
            getUserDefinedMaintenanceProtectedHours(
                calendarStart.get(Calendar.HOUR_OF_DAY), calendarEnd.get(Calendar.HOUR_OF_DAY)));
    setMaintenanceWindow(pGroup, maintenanceWindow);
  }

  private void setMaintenanceWindowWithProtectedHoursToNow(final NDSGroup pGroup) throws Exception {
    final Calendar calendar = getCalendarInstanceForGroup();
    final int currentHourOfDay = calendar.get(Calendar.HOUR_OF_DAY);

    final NDSGroupMaintenanceWindow maintenanceWindow =
        new NDSGroupMaintenanceWindow(
            calendar.get(Calendar.DAY_OF_WEEK),
            calendar.get(Calendar.HOUR_OF_DAY),
            false,
            getUserDefinedMaintenanceProtectedHours(
                currentHourOfDay % 24, (currentHourOfDay + 4) % 24));
    setMaintenanceWindow(pGroup, maintenanceWindow);
  }

  private void setMaintenanceWindow(final NDSGroup pGroup, final NDSGroupMaintenanceWindow pWindow)
      throws SvcException {
    _maintenanceSvc.setUserDefinedMaintenanceWindow(
        pGroup.getGroupId(), pWindow, AuditInfoHelpers.fromSystem());
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(
        pGroup.getGroupId(), pWindow.getAdvanceNotificationSendDate().orElse(_currentTime));
    reloadNdsGroup();
  }

  private void clearAdvanceNotificationDate() {
    _ndsGroupMaintenanceDao.setInAdvancedNotificationSentForGroup(_ndsGroup.getGroupId(), null);
  }

  private void setReleaseCohorts(final NDSGroup pGroup, final ReleaseCohorts pReleaseCohorts)
      throws Exception {
    _ndsGroupDao.overrideReleaseCohorts(pGroup.getGroupId(), pReleaseCohorts);
  }

  private void clearNeedsPublish() {
    _clusterDescriptionDao.findByGroupId(_ndsGroup.getGroupId()).stream()
        .map(ClusterDescription::new)
        .filter(c -> c.getNeedsMongoDBConfigPublishAfter().isPresent())
        .forEach(
            c ->
                _clusterDescriptionDao.clearNeedsPublishDateForCluster(
                    _ndsGroup.getGroupId(),
                    c.getName(),
                    c.getNeedsMongoDBConfigPublishAfter().get()));
  }

  private void forceAutomationConfigToPublished(
      final Organization pOrganization, final Group pGroup) throws SvcException {
    final AutomationConfig config = _automationConfigSvc.findPublishedOrEmpty(pGroup.getId());
    final AppUser user = MmsFactory.createUser(pGroup);
    _automationConfigSvc.saveDraft(config, user, pOrganization, pGroup);
    _automationConfigSvc.publish(pOrganization, pGroup, user);
  }

  private void publishDefaultBIConnectorVersion() throws SvcException {
    final AutomationConfig config = _automationConfigSvc.findPublishedOrEmpty(_group.getId());
    config.setBiConnectorTemplate(new BiConnectorTemplate(_appSettings));
    final AppUser user = MmsFactory.createUser(_group);
    _automationConfigSvc.saveDraft(config, user, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, user);
  }

  private void publishDefaultMongotVersion() throws SvcException {
    publishMongotWithVersion(new MongotTemplate(_appSettings));
  }

  private void publishMongotWithVersion(final MongotTemplate pMongotTemplate) throws SvcException {
    final AutomationConfig config = _automationConfigSvc.findPublishedOrEmpty(_group.getId());
    config.setMongotTemplate(pMongotTemplate);
    final AppUser user = MmsFactory.createUser(_group);
    _automationConfigSvc.saveDraft(config, user, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, user);
  }

  private void publishDefaultSearchEnvoyVersion() throws SvcException {
    final AutomationConfig config = _automationConfigSvc.findPublishedOrEmpty(_group.getId());
    config.setSearchEnvoyTemplate(new SearchEnvoyTemplate(_appSettings));
    final AppUser user = MmsFactory.createUser(_group);
    _automationConfigSvc.saveDraft(config, user, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, user);
  }

  private void publishDefaultAtlasProxyVersion() throws SvcException {
    final AutomationConfig config = _automationConfigSvc.findPublishedOrEmpty(_group.getId());
    config.setAtlasProxyTemplate(new AtlasProxyTemplate(_appSettings));
    final AppUser user = MmsFactory.createUser(_group);
    _automationConfigSvc.saveDraft(config, user, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, user);
  }

  private void publishDefaultMongoDBToolsVersion() throws SvcException {
    final AutomationConfig config = _automationConfigSvc.findPublishedOrEmpty(_group.getId());
    config.setMongoDbToolsTemplate(new MongoDbToolsTemplate(_appSettings));
    final AppUser user = MmsFactory.createUser(_group);
    _automationConfigSvc.saveDraft(config, user, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, user);
  }

  private void saveFTSIndexForCluster(final ClusterDescription pClusterDescription)
      throws SvcException {
    final FTSIndex index = NDSModelTestFactory.getFTSIndex();
    _ftsIndexConfigSvc.addFTSIndex(
        pClusterDescription.getGroupId(), pClusterDescription.getName(), index, null);
  }

  private ClusterDescription createProvisionedCluster_fixedVersion() throws SvcException {
    return createProvisionedCluster(
        Collections.singletonList(getReplicationSpec(1, 3)),
        true,
        ClusterDescription.ClusterType.REPLICASET);
  }

  private ClusterDescription createProvisionedCluster_fixedVersion(
      final String pMongoDBMajorVersion, final String pMongoDBVersion) throws SvcException {
    return createProvisionedCluster(
        Collections.singletonList(getReplicationSpec(1, 3)),
        true,
        ClusterDescription.ClusterType.REPLICASET,
        pMongoDBMajorVersion,
        pMongoDBVersion,
        false);
  }

  private ClusterDescription createProvisionedCluster_withVersion(
      final String pMongoDBMajorVersion, final String pMongoDBVersion, final boolean pIsMTM)
      throws Exception {
    return createProvisionedCluster(
        Collections.singletonList(getReplicationSpec(1, 3)),
        null,
        ClusterDescription.ClusterType.REPLICASET,
        pMongoDBMajorVersion,
        pMongoDBVersion,
        pIsMTM,
        false,
        VersionReleaseSystem.LTS);
  }

  private ClusterDescription createServerlessMTMProvisionedCluster_withVersion(
      final String pMongoDBMajorVersion, final String pMongoDBVersion) throws Exception {
    return createProvisionedCluster(
        Collections.singletonList(getReplicationSpec(1, 3)),
        null,
        ClusterDescription.ClusterType.REPLICASET,
        pMongoDBMajorVersion,
        pMongoDBVersion,
        false,
        true,
        VersionReleaseSystem.LTS);
  }

  private ClusterDescription createProvisionedCluster_withCDVersion(final boolean pIsVersionFixed)
      throws Exception {
    return createProvisionedCluster(
        Collections.singletonList(getReplicationSpec(1, 3)),
        pIsVersionFixed ? new FixedVersion(_clusterSvc.getDefaultCDMongoDBVersion()) : null,
        ClusterDescription.ClusterType.REPLICASET,
        _clusterSvc.getDefaultCDMongoDBMajorVersion(),
        _clusterSvc.getDefaultCDMongoDBVersion(),
        false,
        false,
        VersionReleaseSystem.CONTINUOUS);
  }

  private ClusterDescription createProvisionedServerlessMTMCluster_withCDVersion(
      final boolean pIsVersionFixed) throws Exception {
    return createProvisionedCluster(
        Collections.singletonList(getReplicationSpec(1, 3)),
        pIsVersionFixed ? new FixedVersion(_clusterSvc.getDefaultCDMongoDBVersion()) : null,
        ClusterDescription.ClusterType.REPLICASET,
        _clusterSvc.getDefaultCDMongoDBMajorVersion(),
        _clusterSvc.getDefaultCDMongoDBVersion(),
        false,
        true,
        VersionReleaseSystem.CONTINUOUS);
  }

  private ClusterDescription createProvisionedCluster(
      final List<ReplicationSpec> pReplicationSpecs,
      final boolean pIsVersionFixed,
      final ClusterDescription.ClusterType pClusterType)
      throws SvcException {
    return createProvisionedCluster(
        pReplicationSpecs,
        pIsVersionFixed ? new FixedVersion(_clusterSvc.getDefaultCDMongoDBVersion()) : null,
        pClusterType,
        NDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION,
        _clusterSvc.getDefaultMongoDBVersion(NDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION),
        false,
        false,
        VersionReleaseSystem.LTS);
  }

  private ClusterDescription createProvisionedCluster(
      final List<ReplicationSpec> pReplicationSpecs,
      final boolean pIsVersionFixed,
      final ClusterDescription.ClusterType pClusterType,
      final String pMongoDBMajorVersion,
      final String pMongoDBVersion,
      final boolean pIsMTM)
      throws SvcException {
    return createProvisionedCluster(
        pReplicationSpecs,
        pIsVersionFixed ? new FixedVersion(pMongoDBVersion) : null,
        pClusterType,
        pMongoDBMajorVersion,
        pMongoDBVersion,
        pIsMTM,
        false,
        ClusterDescription.VersionReleaseSystem.LTS);
  }

  private ClusterDescription createProvisionedCluster(
      final List<ReplicationSpec> pReplicationSpecs,
      final FixedVersion pFixedMongoDBVersion,
      final ClusterDescription.ClusterType pClusterType,
      final String pMongoDBMajorVersion,
      final String pMongoDBVersion,
      final boolean pIsFreeTierMTM,
      final boolean pIsServerlessMTM,
      final ClusterDescription.VersionReleaseSystem pVersionReleaseSystem)
      throws SvcException {
    // toHexString returns 24 byte string, but first 23 characters of new clusters
    // must be unique - so we remove the first character
    final String clusterName = new ObjectId().toHexString().substring(1);
    final ClusterDescription clusterDescription =
        new ClusterDescription.Builder<>()
            .setReplicationSpecList(pReplicationSpecs)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(AWSNDSInstanceSize.M30.getGP3StandardEBSIOPS(10)))
            .setClusterType(pClusterType)
            .setName(clusterName)
            .setClusterNamePrefix(clusterName.toLowerCase())
            .setDnsPin(_ndsGroup.getDNSPin())
            .setCreateDate(_currentTime)
            .setDiskSizeGB(10)
            .setGroupId(_ndsGroup.getGroupId())
            .setUniqueId(ObjectId.get())
            .setLastUpdateDate(_currentTime)
            .setMongoDBMajorVersion(pMongoDBMajorVersion)
            .setMongoDBVersion(pMongoDBVersion)
            .setFixedMongoDBVersion(Optional.ofNullable(pFixedMongoDBVersion))
            .setMongoUriHosts(new String[0])
            .setPrivateMongoUriHosts(new String[0])
            .setMongoUriLastUpdateDate(_currentTime)
            .setState(WORKING)
            .setBackupEnabled(false)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .setHostnameSchemeForAgents(HostnameScheme.INTERNAL)
            .setIsMTM(pIsFreeTierMTM || pIsServerlessMTM)
            .setVersionReleaseSystem(pVersionReleaseSystem)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .build();
    if (pIsFreeTierMTM) {
      MmsFactory.createGlobalOwnerAdminUser(_group);
      _groupSvc.setFreeMTMHolder(clusterDescription.getGroupId());
    } else if (pIsServerlessMTM) {
      MmsFactory.createGlobalOwnerAdminUser(_group);
      _groupSvc.setServerlessMTMHolder(clusterDescription.getGroupId());
    }
    _clusterSvc.createDefaultProcessArgs(
        _group, clusterDescription, AuditInfoHelpers.fromSystem(), AppUser.SYSTEM_USER);
    _clusterSvc.createCluster(
        clusterDescription,
        AuditInfoHelpers.fromSystem(),
        null,
        ClusterCreateContext.forIntegrationTest(),
        null);
    _clusterSvc.mergePendingClusterChanges(_ndsGroup);
    _hardwareSvc.ensureHardware(
        _ndsGroup, Cluster.getCluster(clusterDescription, Collections.emptyList()), LOG, _group);

    final AWSHardwareSpec hardwareSpec =
        (AWSHardwareSpec)
            clusterDescription
                .getOnlyHardwareSpecForProvider(CloudProvider.AWS, NodeType.ELECTABLE)
                .orElseThrow();

    _hardwareSvc
        .getReplicaSetHardware(_ndsGroup.getGroupId(), clusterDescription.getName())
        .forEach(
            r -> {
              final ReplicationSpec regionsConfig =
                  clusterDescription.getReplicationSpecById(r.getReplicationSpecId()).orElseThrow();
              final Map<ObjectId, RegionName> instanceIdToRegionNameMap =
                  r.getInstanceIdToRegionNameMap(regionsConfig.getRegionConfigs());
              r.getHardware().stream()
                  .forEach(
                      i -> {
                        final RegionName region = instanceIdToRegionNameMap.get(i.getInstanceId());
                        final AWSCloudProviderContainer container =
                            (AWSCloudProviderContainer)
                                _ndsGroup
                                    .getCloudProviderContainer(
                                        CloudProvider.AWS, region, clusterDescription.getName())
                                    .get();

                        final int regionHardwareIdx =
                            r.getRegionHardwareIndex(i.getInstanceId(), instanceIdToRegionNameMap);
                        final String zone =
                            _cloudProviderAvailabilityFactory
                                .getCloudProviderAvailability(container)
                                .getInstancePreferredZone(
                                    regionHardwareIdx,
                                    i.getInstanceId(),
                                    instanceIdToRegionNameMap);
                        String subnet = null;
                        for (int n = 0; n < container.getSubnets().length; n++) {
                          if (container.getSubnets()[n].getAvailabilityZone().equals(zone)) {
                            subnet = container.getSubnets()[n].getSubnetId();
                            break;
                          }
                        }
                        final String legacyHostname =
                            r.getReplicaSetMemberName(
                                clusterDescription.getDeploymentClusterName(), i.getInstanceId());
                        final Hostnames hostnames = new Hostnames(legacyHostname);

                        _awsInstanceHardwareDao.setProvisionedFields(
                            r.getId(),
                            i.getInstanceId(),
                            r.isInstanceInternal(i.getInstanceId()),
                            "ebs-1",
                            VolumeType.Gp3.toString(),
                            hardwareSpec.getEncryptEBSVolume(),
                            clusterDescription.getDiskSizeGB(),
                            hardwareSpec.getDiskIOPS(),
                            null,
                            null,
                            null,
                            null,
                            null,
                            hardwareSpec.getInstanceSize().name(),
                            hardwareSpec.getInstanceFamily().getName(),
                            hardwareSpec.getOS(),
                            "eip-1",
                            "127.0.0.1",
                            false,
                            subnet,
                            hostnames,
                            HostnameScheme.LEGACY,
                            "i-1",
                            "dev/xvdb",
                            r.isInstanceInternal(i.getInstanceId()) ? "/dev/xvdc" : null,
                            List.of(),
                            container.getId(),
                            null);
                      });
            });
    return clusterDescription;
  }

  private ClusterDescription createProvisionedCluster() throws SvcException {
    return createProvisionedCluster(1, 3, ClusterDescription.ClusterType.REPLICASET);
  }

  private ClusterDescription createProvisionedCluster(
      final int pNumShards,
      final int pReplicationFactor,
      final ClusterDescription.ClusterType pClusterType)
      throws SvcException {
    return createProvisionedCluster(
        Collections.singletonList(getReplicationSpec(pNumShards, pReplicationFactor)),
        false,
        pClusterType);
  }

  private ReplicationSpec getReplicationSpec(final int pNumShards, final int pNumElectableNodes) {
    return new ReplicationSpec(
        new ObjectId(),
        new ObjectId(),
        new ObjectId(),
        NDSDefaults.ZONE_NAME,
        pNumShards,
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, pNumElectableNodes, 0, 0, 0)));
  }

  private void publishDefaultAgentVersions() throws SvcException {
    final AutomationConfig config = _automationConfigSvc.findPublishedOrEmpty(_group.getId());
    config
        .getDeployment()
        .setAgentVersion(
            Deployment.getDefaultAutomationAgentConfig(
                _appSettings,
                _phasedVersionSvc
                    .findBySoftwareType(SoftwareType.AUTOMATION_AGENT)
                    .get()
                    .getTargetVersion()));
    config.setMonitoringAgentTemplate(
        BaseAgentConfig.getDefaultMonitoringAgentTemplate(
            _appSettings, NDSDefaults.DEPRECATED_AGENTS_SENTINEL_VERSION, true));
    config.setBackupAgentTemplate(
        BaseAgentConfig.getDefaultBackupAgentTemplate(
            _appSettings, NDSDefaults.DEPRECATED_AGENTS_SENTINEL_VERSION, true));

    final AppUser user = MmsFactory.createUser(_group);
    _automationConfigSvc.saveDraft(config, user, _organization, _group);
    _automationConfigSvc.publish(_organization, _group, user);
  }

  private void setPhasedVersionToCriticalRelease(final SoftwareType pSoftwareType)
      throws SvcException {
    final PhasedVersion phasedVersion = _phasedVersionSvc.findBySoftwareType(pSoftwareType).get();

    final BasicDBObject doc = phasedVersion.toDBObject();
    doc.put(FieldDefs.RELEASE_MODE, ReleaseMode.CRITICAL);
    final PhasedVersion updated = _phasedVersionFactory.createPhasedVersion(doc);
    _phasedVersionSvc.save(updated);
  }

  private void setPhasedTargetVersion(
      final SoftwareType pSoftwareType, final String pPhasedTargetVersion) {
    final PhasedVersion phasedVersion =
        _phasedVersionSvc.findBySoftwareType(pSoftwareType).orElseThrow();

    final BasicDBObject doc = phasedVersion.toDBObject();
    doc.put(FieldDefs.TARGET_VERSION, pPhasedTargetVersion);
    final PhasedVersion updated = _phasedVersionFactory.createPhasedVersion(doc);
    _phasedVersionSvc.save(updated);
  }

  private void setPhasedVersionToAutoRelease(final SoftwareType pSoftwareType) {
    final PhasedVersion phasedVersion = _phasedVersionSvc.findBySoftwareType(pSoftwareType).get();

    final BasicDBObject doc = phasedVersion.toDBObject();
    doc.put(FieldDefs.RELEASE_MODE, ReleaseMode.AUTOMATIC);
    final PhasedVersion updated = _phasedVersionFactory.createPhasedVersion(doc);
    _phasedVersionSvc.save(updated);
  }

  private NDSGroup reloadNdsGroup() {
    return _ndsGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
  }

  private List<SoftwareType> getAgentSoftwares() {
    return Arrays.asList(SoftwareType.AUTOMATION_AGENT, SoftwareType.BI_CONNECTOR);
  }

  private String getNextCDQuarterlyVersion() {
    // 5.0.2 -> 5.99.0
    final String currentCDVersion = _clusterSvc.getDefaultCDMongoDBVersion();
    String[] versions = currentCDVersion.split("\\.");
    versions[1] = "99";
    versions[2] = "0";
    return String.join(".", versions);
  }

  private String getNextCDPatchVersion() {
    // 5.0.2 -> 5.0.99
    final String currentCDVersion = _clusterSvc.getDefaultCDMongoDBVersion();
    String[] versions = currentCDVersion.split("\\.");
    versions[2] = "99";
    return String.join(".", versions);
  }

  private String getNextLTSVersion() {
    // 5.1.3 -> 6.0.0
    final String currentCDVersion = _clusterSvc.getDefaultCDMongoDBVersion();
    String[] versions = currentCDVersion.split("\\.");
    versions[0] = String.format("%d", Integer.parseInt(versions[0]) + 1);
    versions[1] = "0";
    versions[2] = "0";
    return String.join(".", versions);
  }

  private void assertHistoryEntriesLackStartedDate(
      final ObjectId groupId, final MaintenanceType type) {
    var histories =
        _ndsMaintenanceHistoryDao.findAllByGroupIdAndStateNotCompleted(groupId).stream()
            .filter(h -> h.getMaintenanceType() == type)
            .toList();

    assertThat(histories).isNotEmpty();
    assertThat(histories).allSatisfy(h -> assertThat(h.getMaintenanceStartedDate()).isNull());
  }

  private void assertHistoryEntriesContainStartedDate(
      final ObjectId groupId, final MaintenanceType type) {
    var histories =
        _ndsMaintenanceHistoryDao.findAllByGroupIdAndStateNotCompleted(groupId).stream()
            .filter(h -> h.getMaintenanceType() == type)
            .toList();

    assertThat(histories).isNotEmpty();
    assertThat(histories).allSatisfy(h -> assertThat(h.getMaintenanceStartedDate()).isNotNull());
  }
}
