package com.xgen.svc.nds.dao;

import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.AWS;
import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.AZURE;
import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.GCP;
import static com.xgen.svc.nds.model.TenantPrivateNetworkingModelTestFactory.AWS_ENDPOINT_ID;
import static com.xgen.svc.nds.model.TenantPrivateNetworkingModelTestFactory.AWS_ENDPOINT_ID_2;
import static com.xgen.svc.nds.model.TenantPrivateNetworkingModelTestFactory.PRIVATE_ENDPOINT_SRV_ADDRESS;
import static com.xgen.svc.nds.model.TenantPrivateNetworkingModelTestFactory.PRIVATE_ENDPOINT_SRV_ADDRESS_2;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

import com.google.common.collect.Iterables;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.ReadPreference;
import com.mongodb.WriteResult;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.driverwrappers._public.legacy.Cursor;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.model._public.access.EmployeeAccessGrantType;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.mongo._public.mongo.FeatureCompatibilityVersion;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.deployment._public.model.IndexConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType.PreferredStorageType;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.util.AzureClusterTestUtils;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.DisaggregatedStorageConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.ShardRegionConfig;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.common._public.model.ProxyProtocolForPrivateLinkMode;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceFamily;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyStatus;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyArgVersion;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyStatus;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.AutoSharding;
import com.xgen.cloud.nds.project._public.model.BumperFileOverride;
import com.xgen.cloud.nds.project._public.model.CheckMetadataConsistency;
import com.xgen.cloud.nds.project._public.model.ClusterConnectionStringConfiguration;
import com.xgen.cloud.nds.project._public.model.ClusterConnectionStringConfiguration.Status;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterProvisionType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.EncryptionAtRestProvider;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.OsTunedFileOverrides;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.SwapIpMaintenanceRound;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionId;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionEligibility;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionOperationOrigin;
import com.xgen.cloud.nds.project._public.model.FleetAttributes.FleetAttribute;
import com.xgen.cloud.nds.project._public.model.FlexTenantMigrationState;
import com.xgen.cloud.nds.project._public.model.GeoSharding;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion.FixedBy;
import com.xgen.cloud.partnerintegrations.common._public.model.IntegrationType;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import com.xgen.svc.core.dao.base.BaseDao.StaleUpdateException;
import com.xgen.svc.core.dao.base.SearchOperator;
import com.xgen.svc.mms.misc.FleetDataValidationUtil;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFreeClusterDescriptionConfig;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestServerlessClusterDescriptionConfig;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import dev.morphia.Datastore;
import dev.morphia.mapping.Mapper;
import jakarta.inject.Inject;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class ClusterDescriptionDaoIntTests extends JUnit5BaseSvcTest {

  final Mapper _morphiaMapper = new Mapper();
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AppSettings _appSettings;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _clusterDescriptionDao.ensureIndexes();
  }

  @Test
  public void testFindActiveByUniqueIds() {
    final ClusterDescription c0 =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(new ObjectId(), "Cluster0"))
            .copy()
            .setDnsPin("c0group")
            .build();
    final ClusterDescription c1 =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(new ObjectId(), "Cluster1"))
            .copy()
            .setState(State.DELETED)
            .setDnsPin("c1group")
            .build();
    final ClusterDescription c2 =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(new ObjectId(), "Cluster2"))
            .copy()
            .setDeleteRequested(true)
            .setDnsPin("c2group")
            .build();
    final ClusterDescription c3 =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(new ObjectId(), "Cluster2"))
            .copy()
            .setIsMTM(true)
            .setDnsPin("c3group")
            .build();

    _clusterDescriptionDao.save(c0);
    _clusterDescriptionDao.save(c1);
    _clusterDescriptionDao.save(c2);
    _clusterDescriptionDao.save(c3);

    final Set<ObjectId> uniqueIds =
        Set.of(c0.getUniqueId(), c1.getUniqueId(), c2.getUniqueId(), c3.getUniqueId());

    try (final DBCursor c =
        _clusterDescriptionDao.findActiveCustomerClusterByUniqueIds(uniqueIds)) {
      assertEquals(1, c.count());
      assertEquals(c0.getUniqueId(), c.next().get(FieldDefs.UNIQUE_ID));
    }
  }

  @Test
  public void testFindClustersToValidate_NoEncryptionAtRestClusters() {
    final BasicDBObject clusterWithEncryptionAtRest =
        NDSModelTestFactory.getAWSClusterDescriptionWithEncryptionAtRestProvider(
                new ObjectId(), "a", EncryptionAtRestProvider.AWS)
            .append(FieldDefs.MONGODB_MAJOR_VERSION, "4.0")
            .append(FieldDefs.CREATE_DATE, new Date(0L));
    _clusterDescriptionDao.saveReplicaSafe(clusterWithEncryptionAtRest);

    // No encryption at rest clusters
    try (final DBCursor cursor =
        _clusterDescriptionDao.findClustersToValidate(
            "3.4", new Date(), FleetDataValidationUtil.DEFAULT_PROVIDERS)) {
      assertTrue(cursor.toArray().isEmpty());
    }
  }

  @Test
  public void testFindClustersToValidate_MatchingProviders() {
    _clusterDescriptionDao.saveMajority(
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "awsGroup"));
    _clusterDescriptionDao.saveMajority(
        NDSModelTestFactory.getAzureClusterDescription().append(FieldDefs.DNS_PIN, "azrGroup"));
    _clusterDescriptionDao.saveMajority(
        NDSModelTestFactory.getGCPClusterDescription().append(FieldDefs.DNS_PIN, "gcpGroup"));

    final ClusterDescription freeCluster =
        new ClusterDescription(NDSModelTestFactory.getFreeClusterDescription());
    _clusterDescriptionDao.save(freeCluster);

    final ClusterDescription serverlessCluster =
        new ClusterDescription(
            NDSModelTestFactory.getServerlessClusterDescription(
                new TestServerlessClusterDescriptionConfig().setGroupId(new ObjectId())));
    _clusterDescriptionDao.save(serverlessCluster);

    final Date aFewMomentsFromNow = DateUtils.addMinutes(new Date(), 1);
    try (final DBCursor cursor =
        _clusterDescriptionDao.findClustersToValidate(
            NDSModelTestFactory.TEST_MONGODB_VERSION.getMajorVersionString(),
            aFewMomentsFromNow,
            FleetDataValidationUtil.DEFAULT_PROVIDERS)) {
      final List<DBObject> clusters = cursor.toArray();
      assertEquals(3, clusters.size());
      assertTrue(
          clusters.stream()
              .map(BasicDBObject.class::cast)
              .noneMatch(
                  cid ->
                      freeCluster
                              .getName()
                              .equals(DbUtils.getNestedValue(cid, FieldDefs.ID, FieldDefs.GROUP_ID))
                          && freeCluster
                              .getGroupId()
                              .equals(
                                  DbUtils.getNestedValue(cid, FieldDefs.ID, FieldDefs.GROUP_ID))));
    }
  }

  @Test
  public void testFindClustersToValidate_MatchingVersionAndDates() {
    final BasicDBObject oldCluster =
        NDSModelTestFactory.getAWSClusterDescription(new ObjectId(), "x")
            .append(FieldDefs.MONGODB_MAJOR_VERSION, "4.0")
            .append(FieldDefs.CREATE_DATE, new Date(0L))
            .append(
                FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
                ClusterDescription.EncryptionAtRestProvider.NONE.name());

    final BasicDBObject newCluster =
        NDSModelTestFactory.getAWSClusterDescription(new ObjectId(), "y")
            .append(FieldDefs.MONGODB_MAJOR_VERSION, "4.0")
            .append(FieldDefs.CREATE_DATE, new Date(4000L))
            .append(
                FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
                ClusterDescription.EncryptionAtRestProvider.NONE.name());

    final BasicDBObject cluster36 =
        NDSModelTestFactory.getAWSClusterDescription(new ObjectId(), "z")
            .append(FieldDefs.MONGODB_MAJOR_VERSION, "3.6")
            .append(FieldDefs.CREATE_DATE, new Date(0L));

    _clusterDescriptionDao.saveReplicaSafe(oldCluster);
    _clusterDescriptionDao.saveReplicaSafe(newCluster);
    _clusterDescriptionDao.saveReplicaSafe(cluster36);

    // No 3.4 clusters
    try (final DBCursor cursor =
        _clusterDescriptionDao.findClustersToValidate("3.4", new Date(), List.of(AWS))) {
      assertTrue(cursor.toArray().isEmpty());
    }

    // Exactly one 3.6 cluster created before now
    try (final DBCursor cursor =
        _clusterDescriptionDao.findClustersToValidate("3.6", new Date(), List.of(AWS))) {
      assertEquals(1, cursor.toArray().size());
    }

    // No 3.6 cluster created before 0L
    try (final DBCursor cursor =
        _clusterDescriptionDao.findClustersToValidate("3.6", new Date(0L), List.of(AWS))) {
      assertEquals(0, cursor.toArray().size());
    }

    // Two 4.0 clusters created before now
    try (final DBCursor cursor =
        _clusterDescriptionDao.findClustersToValidate("4.0", new Date(), List.of(AWS))) {
      assertEquals(2, cursor.toArray().size());
    }

    // One 4.0 cluster created before 4000L
    try (final DBCursor cursor =
        _clusterDescriptionDao.findClustersToValidate("4.0", new Date(4000L), List.of(AWS))) {
      assertEquals(1, cursor.toArray().size());
    }
  }

  @Test
  public void testSave() {
    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    assertEquals(0, _clusterDescriptionDao.count());
    _clusterDescriptionDao.save(clusterDescription);
    assertEquals(1, _clusterDescriptionDao.count());
  }

  @Test
  public void testRemove() {
    // Insert and remove single document
    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c1group");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);
    assertEquals(1, _clusterDescriptionDao.count());
    _clusterDescriptionDao.remove(clusterDescription.getGroupId(), clusterDescription.getName());
    assertEquals(0, _clusterDescriptionDao.count());

    // Insert two documents and make sure only one is removed
    final BasicDBObject descDoc2 = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc2.get("_id")).append("name", "foo2");
    descDoc2.append(FieldDefs.CLUSTER_NAME_PREFIX, "foo2");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);
    assertEquals(2, _clusterDescriptionDao.count());
    _clusterDescriptionDao.remove(clusterDescription.getGroupId(), clusterDescription.getName());
    assertEquals(1, _clusterDescriptionDao.count());
    final DBObject doc = _clusterDescriptionDao.getDbCollection().findOne();
    assertEquals("foo2", ((BasicDBObject) doc.get("_id")).get("name"));
  }

  @Test
  public void testFindByName() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c1Group");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c2Group");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);

    when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());
    final ClusterDescription result =
        _clusterDescriptionDao.findByName(g.getGroupId(), clusterDescription.getName()).get();
    when(g.getGroupId()).thenReturn(clusterDescription2.getGroupId());
    final ClusterDescription result2 =
        _clusterDescriptionDao.findByName(g.getGroupId(), clusterDescription2.getName()).get();
    assertEquals(clusterDescription.getGroupId(), result.getGroupId());
    assertEquals(clusterDescription2.getGroupId(), result2.getGroupId());
  }

  @Test
  public void testFindByName_Deleted() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc.get("_id")).append("name", "expected");
    descDoc.append(FieldDefs.CLUSTER_NAME_PREFIX, "expected");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    ((BasicDBObject) descDoc.get("_id")).append("name", "notExpected");
    descDoc.append(FieldDefs.CLUSTER_NAME_PREFIX, "notexpected");
    descDoc.put("state", ClusterDescription.State.DELETED.name());
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);

    // Only should get the expected cluster
    when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());
    final ClusterDescription result =
        _clusterDescriptionDao.findByName(g.getGroupId(), clusterDescription.getName()).get();
    assertEquals("expected", result.getName());

    // Should turn up nothing
    assertFalse(
        _clusterDescriptionDao
            .findByName(g.getGroupId(), clusterDescription2.getName())
            .isPresent());
  }

  @Test
  public void testFindByNameAnyState() {
    final String clusterName = "testCluster";
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    // should return nothing when there is nothing to find
    assertTrue(_clusterDescriptionDao.findByNameAnyState(g.getGroupId(), "doesNotExist").isEmpty());

    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc.get(FieldDefs.ID)).append(FieldDefs.NAME, clusterName);

    // Should find the cluster no matter what state it is
    Arrays.stream(ClusterDescription.State.values())
        .forEach(
            state -> {
              descDoc.put(FieldDefs.STATE, state.name());
              final ClusterDescription clusterDescription =
                  ClusterDescription.getCloudProviderClusterDescription(descDoc);
              _clusterDescriptionDao.save(clusterDescription);

              when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());
              final Optional<ClusterDescription> result =
                  _clusterDescriptionDao.findByNameAnyState(
                      g.getGroupId(), clusterDescription.getName());
              assertTrue(
                  result
                      .filter(cluster -> cluster.getName().equals(clusterName))
                      .filter(cluster -> cluster.getState().equals(state))
                      .isPresent());
            });
  }

  @Test
  public void testFindByCaseInsensitiveNameAnyState() {
    final String clusterName = "testCluster";
    final String differentCaseName = "TestClustEr";
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    // should return nothing when there is nothing to find
    assertTrue(
        _clusterDescriptionDao
            .findByCaseInsensitiveNameAnyState(g.getGroupId(), "doesNotExist")
            .isEmpty());

    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc.get(FieldDefs.ID)).append(FieldDefs.NAME, clusterName);

    // Should find the cluster no matter what search casing or what state it is
    List.of(clusterName, differentCaseName)
        .forEach(
            searchName -> {
              Arrays.stream(ClusterDescription.State.values())
                  .forEach(
                      state -> {
                        descDoc.put(FieldDefs.STATE, state.name());
                        final ClusterDescription clusterDescription =
                            ClusterDescription.getCloudProviderClusterDescription(descDoc);
                        _clusterDescriptionDao.save(clusterDescription);

                        when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());
                        final Optional<ClusterDescription> result =
                            _clusterDescriptionDao.findByCaseInsensitiveNameAnyState(
                                g.getGroupId(), searchName);
                        assertTrue(
                            result
                                .filter(cluster -> cluster.getName().equals(clusterName))
                                .filter(cluster -> cluster.getState().equals(state))
                                .isPresent());
                      });
            });
  }

  @Test
  public void findByDeploymentClusterName() {
    final BasicDBObject cd0 = NDSModelTestFactory.getAWSClusterDescription(new ObjectId(), "foo1");
    cd0.append("deploymentClusterName", "atlas-expect");
    final ClusterDescription clusterDescription0 =
        ClusterDescription.getCloudProviderClusterDescription(cd0);
    final BasicDBObject cd1 = NDSModelTestFactory.getAWSClusterDescription(new ObjectId(), "foo2");
    cd1.append("deploymentClusterName", "atlas-tcepxe");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(cd1);
    _clusterDescriptionDao.save(clusterDescription0);
    _clusterDescriptionDao.save(clusterDescription1);

    final ClusterDescription result =
        _clusterDescriptionDao
            .findByDeploymentClusterName(
                clusterDescription0.getGroupId(),
                "atlas-expect",
                DriverUtils.SECONDARY_PREFERRED_MINIMUM)
            .get();
    final ClusterDescription result2 =
        _clusterDescriptionDao
            .findByDeploymentClusterName(
                clusterDescription1.getGroupId(),
                "atlas-tcepxe",
                DriverUtils.SECONDARY_PREFERRED_MINIMUM)
            .get();
    assertEquals(clusterDescription0.getGroupId(), result.getGroupId());
    assertEquals(clusterDescription1.getGroupId(), result2.getGroupId());
  }

  @Test
  public void findByDeploymentClusterName_Deleted() {
    final BasicDBObject cd0 = NDSModelTestFactory.getAWSClusterDescription();
    cd0.append("deploymentClusterName", "atlas-expect");
    cd0.append(FieldDefs.DNS_PIN, "c0Group");
    final ClusterDescription clusterDescription0 =
        ClusterDescription.getCloudProviderClusterDescription(cd0);
    final BasicDBObject cd1 = NDSModelTestFactory.getAWSClusterDescription();
    cd1.append("deploymentClusterName", "atlas-tcepxe");
    cd1.append(FieldDefs.DNS_PIN, "c1Group");
    cd1.put("state", ClusterDescription.State.DELETED.name());
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(cd1);
    _clusterDescriptionDao.save(clusterDescription0);
    _clusterDescriptionDao.save(clusterDescription1);

    // Only should get the expected cluster
    final ClusterDescription result =
        _clusterDescriptionDao
            .findByDeploymentClusterName(
                clusterDescription0.getGroupId(),
                "atlas-expect",
                DriverUtils.SECONDARY_PREFERRED_MINIMUM)
            .get();
    assertEquals("atlas-expect", result.getDeploymentClusterName());
    assertEquals(clusterDescription0.getGroupId(), result.getGroupId());

    // Should turn up nothing
    assertFalse(
        _clusterDescriptionDao
            .findByDeploymentClusterName(
                clusterDescription1.getGroupId(),
                "atlas-tcepxe",
                DriverUtils.SECONDARY_PREFERRED_MINIMUM)
            .isPresent());
  }

  @Test
  public void testFindByGroupAfterDeletedDate() {
    final ObjectId groupId = new ObjectId();
    final Date now = new Date();
    final BasicDBObject activeCluster =
        NDSModelTestFactory.getAWSClusterDescription(groupId, "activeCluster")
            .append(FieldDefs.STATE, ClusterDescription.State.WORKING.name());
    final BasicDBObject oldDeletedCluster =
        NDSModelTestFactory.getAWSClusterDescription(groupId, "oldDeletedCluster")
            .append(FieldDefs.STATE, ClusterDescription.State.DELETED.name())
            .append(FieldDefs.DELETED_DATE, DateUtils.addDays(now, -30));
    final BasicDBObject recentlyDeletedCluster =
        NDSModelTestFactory.getAWSClusterDescription(groupId, "recentlyDeletedCluster")
            .append(FieldDefs.STATE, ClusterDescription.State.DELETED.name())
            .append(FieldDefs.DELETED_DATE, DateUtils.addHours(now, -12));
    _clusterDescriptionDao.saveReplicaSafe(activeCluster);
    _clusterDescriptionDao.saveReplicaSafe(oldDeletedCluster);
    _clusterDescriptionDao.saveReplicaSafe(recentlyDeletedCluster);

    // 3 clusters - 1 active, 2 with deletedDate after 0L
    {
      final List<ClusterDescription> clusters =
          _clusterDescriptionDao.findClustersByGroupAfterDeletedDate(groupId, new Date(0L));
      assertEquals(3, clusters.size());
    }

    // 2 clusters - 1 active, 1 with deletedDate after 1 day ago
    {
      final List<ClusterDescription> clusters =
          _clusterDescriptionDao.findClustersByGroupAfterDeletedDate(
              groupId, DateUtils.addDays(now, -1));
      assertEquals(2, clusters.size());
    }

    // 1 cluster - 1 active, none deleted after now
    {
      final List<ClusterDescription> clusters =
          _clusterDescriptionDao.findClustersByGroupAfterDeletedDate(groupId, new Date());
      assertEquals(1, clusters.size());
    }
  }

  @Test
  public void testMarkDeleted() {
    final ObjectId groupId = new ObjectId();
    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription(groupId)
            .append(FieldDefs.ENSURE_CLUSTER_CONNECTIVITY_AFTER, new Date())
            .append(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, new Date());
    final ObjectId uniqueId = new ObjectId();
    descDoc.append(FieldDefs.UNIQUE_ID, uniqueId);
    ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.markDeleted(groupId, clusterDescription.getName(), new Date());

    final Optional<ClusterDescription> cl =
        _clusterDescriptionDao.findByUniqueId(groupId, uniqueId);
    assertTrue(cl.isPresent());
    clusterDescription = cl.get();
    assertEquals(State.DELETED, clusterDescription.getState());
    assertFalse(clusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertFalse(clusterDescription.getEnsureClusterConnectivityAfter().isPresent());
  }

  @Test
  public void testFindByGroup() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c1Group");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c2Group");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);
    ((BasicDBObject) descDoc2.get("_id")).append("name", "foo2");
    descDoc2.append(FieldDefs.CLUSTER_NAME_PREFIX, "foo2");
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription3);

    // Single result
    when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());
    final List<ClusterDescription> result = _clusterDescriptionDao.findByGroup(g.getGroupId());
    assertEquals(1, result.size());
    assertEquals(clusterDescription.getGroupId(), result.get(0).getGroupId());

    // Multiple results
    // Single result
    when(g.getGroupId()).thenReturn(clusterDescription2.getGroupId());
    final List<ClusterDescription> result2 = _clusterDescriptionDao.findByGroup(g.getGroupId());
    assertEquals(2, result2.size());
    assertEquals(clusterDescription2.getGroupId(), result2.get(0).getGroupId());
    assertEquals(clusterDescription2.getGroupId(), result2.get(1).getGroupId());

    // No results
    when(g.getGroupId()).thenReturn(new ObjectId());
    final List<ClusterDescription> result3 = _clusterDescriptionDao.findByGroup(g.getGroupId());
    assertEquals(0, result3.size());
  }

  @Test
  public void testFindByGroups() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final NDSGroup group2 = NDSModelTestFactory.getAWSMockedGroup();
    final NDSGroup group3 = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c1Group");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c2Group");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c3Group");
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    // Single result
    when(group.getGroupId()).thenReturn(clusterDescription.getGroupId());
    when(group2.getGroupId()).thenReturn(clusterDescription2.getGroupId());
    when(group3.getGroupId()).thenReturn(clusterDescription3.getGroupId());

    final List<ClusterDescription> result =
        _clusterDescriptionDao.findByGroups(List.of(group.getGroupId()));
    assertEquals(1, result.size());
    assertEquals(clusterDescription.getGroupId(), result.get(0).getGroupId());

    // Multiple results
    final List<ClusterDescription> result2 =
        _clusterDescriptionDao.findByGroups(List.of(group2.getGroupId(), group3.getGroupId()));
    assertEquals(2, result2.size());
    assertEquals(clusterDescription2.getGroupId(), result2.get(0).getGroupId());
    assertEquals(clusterDescription3.getGroupId(), result2.get(1).getGroupId());

    // No results
    when(group.getGroupId()).thenReturn(new ObjectId());
    final List<ClusterDescription> result3 = _clusterDescriptionDao.findByGroup(group.getGroupId());
    assertEquals(0, result3.size());
  }

  @Test
  public void testFindByGroup_Deleted() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc.get("_id")).append("name", "expected");
    descDoc.append(FieldDefs.CLUSTER_NAME_PREFIX, "expected");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    ((BasicDBObject) descDoc.get("_id")).append("name", "notExpected");
    descDoc.append(FieldDefs.CLUSTER_NAME_PREFIX, "notExpected");
    descDoc.put("state", ClusterDescription.State.DELETED.name());
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);

    // Only should get the active cluster
    when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());
    final List<ClusterDescription> result = _clusterDescriptionDao.findByGroup(g.getGroupId());
    assertEquals(1, result.size());
    assertEquals("expected", result.get(0).getName());
  }

  @Test
  public void testFindByNames() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc.get("_id"))
        .append("name", "cluster1")
        .append("groupId", g.getGroupId());
    descDoc.append(FieldDefs.CLUSTER_NAME_PREFIX, "cluster1");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    final BasicDBObject descDoc2 = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc2.get("_id"))
        .append("name", "cluster2")
        .append("groupId", g.getGroupId());
    descDoc2.append(FieldDefs.CLUSTER_NAME_PREFIX, "cluster2");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);

    final List<String> clusterNames = new ArrayList<>();
    clusterNames.add(clusterDescription.getName());
    clusterNames.add(clusterDescription2.getName());

    when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());
    when(g.getGroupId()).thenReturn(clusterDescription2.getGroupId());
    final List<ClusterDescription> result =
        _clusterDescriptionDao.findByNames(g.getGroupId(), clusterNames, ReadPreference.primary());

    assertEquals(clusterDescription.getGroupId(), result.get(0).getGroupId());
    assertEquals(clusterDescription2.getGroupId(), result.get(1).getGroupId());
    assertEquals(clusterDescription.getName(), result.get(0).getName());
    assertEquals(clusterDescription2.getName(), result.get(1).getName());
    assertEquals(2, result.size());
  }

  @Test
  public void testFindDeletedClustersForGroup() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc.get("_id")).append("name", "notExpected");
    descDoc.append(FieldDefs.CLUSTER_NAME_PREFIX, "notExpected");

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);

    ((BasicDBObject) descDoc.get("_id")).append("name", "expected");
    descDoc.append(FieldDefs.CLUSTER_NAME_PREFIX, "expected");
    descDoc.put("state", ClusterDescription.State.DELETED.name());

    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);

    when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());
    final List<ClusterDescription> result =
        _clusterDescriptionDao.findDeletedByGroup(g.getGroupId(), ReadPreference.primary());
    assertEquals(1, result.size());
    assertEquals("expected", result.get(0).getName());
    assertEquals(State.DELETED, result.get(0).getState());
  }

  @Test
  public void testFindLastUpdatedClusterByGroup() {
    final NDSGroup mockGroup = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription clusterDescription0 =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setName("0")
            .setClusterNamePrefix("0")
            .build();
    final ClusterDescription clusterDescription1 =
        clusterDescription0.copy().setName("1").setClusterNamePrefix("1").build();

    final ClusterDescription clusterDescription2 =
        clusterDescription0.copy().setName("2").setClusterNamePrefix("2").build();
    _clusterDescriptionDao.save(clusterDescription0);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);

    when(mockGroup.getGroupId()).thenReturn(clusterDescription0.getGroupId());

    // When one of the clusters is updated
    final String expectedResult = clusterDescription1.getName();
    _clusterDescriptionDao.setLastUpdateDate(mockGroup.getGroupId(), expectedResult, new Date());
    final String result =
        _clusterDescriptionDao
            .findLastUpdatedClusterByGroup(mockGroup.getGroupId())
            .get()
            .getName();
    assertEquals(expectedResult, result);

    // When there are no clusters
    when(mockGroup.getGroupId()).thenReturn(new ObjectId());
    assertFalse(
        _clusterDescriptionDao.findLastUpdatedClusterByGroup(mockGroup.getGroupId()).isPresent());
  }

  @Test
  public void testFindForBillingByGroup() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    final Date now = new Date();
    final Date threeHoursAgo = DateUtils.addHours(now, -3);
    final Date twoHoursAgo = DateUtils.addHours(now, -2);
    final Date oneHourAgo = DateUtils.addHours(now, -1);
    final Date halfHourAgo = DateUtils.addMinutes(now, -30);
    final Date tenMinutesAgo = DateUtils.addMinutes(now, -10);
    final Date nextMonth = DateUtils.addDays(now, 30);

    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc.get("_id")).append("groupId", g.getGroupId()).append("name", "future");
    descDoc.put("createDate", nextMonth);
    descDoc.put("dnsPin", g.getDNSPin());
    descDoc.put("clusterNamePrefix", "future");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    final BasicDBObject descDoc2 = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc2.get("_id")).append("groupId", g.getGroupId()).append("name", "past");
    descDoc2.put("createDate", threeHoursAgo);
    descDoc2.put("deletedDate", twoHoursAgo);
    descDoc2.put("state", "DELETED");
    descDoc2.put("dnsPin", g.getDNSPin());
    descDoc2.put("clusterNamePrefix", "past");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final BasicDBObject descDoc3 = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc3.get("_id"))
        .append("groupId", g.getGroupId())
        .append("name", "expected1");
    descDoc3.put("createDate", threeHoursAgo);
    descDoc3.put("deletedDate", twoHoursAgo);
    descDoc3.put("dnsPin", g.getDNSPin());
    descDoc3.put("clusterNamePrefix", "expected1");
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    final BasicDBObject descDoc4 = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc4.get("_id"))
        .append("groupId", g.getGroupId())
        .append("name", "expected2");
    descDoc4.put("createDate", halfHourAgo);
    descDoc4.put("deletedDate", tenMinutesAgo);
    descDoc4.put("state", "DELETED");
    descDoc4.put("dnsPin", g.getDNSPin());
    descDoc4.put("clusterNamePrefix", "expected2");
    final ClusterDescription clusterDescription4 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc4);
    final BasicDBObject descDoc5 = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc5.get("_id"))
        .append("groupId", g.getGroupId())
        .append("name", "expected3");
    descDoc5.put("createDate", halfHourAgo);
    descDoc5.put("deletedDate", nextMonth);
    descDoc5.put("state", "DELETED");
    final ClusterDescription clusterDescription5 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc5);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);
    _clusterDescriptionDao.save(clusterDescription4);
    _clusterDescriptionDao.save(clusterDescription5);

    final List<ClusterDescription> result =
        _clusterDescriptionDao.findForBillingByGroup(g.getGroupId(), oneHourAgo, now);
    assertEquals(3, result.size());
    for (final ClusterDescription desc : result) {
      assertTrue(desc.getName().startsWith("expected"));
    }
  }

  @Test
  public void testFindByUniqueIds() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c1group");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c2group");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);
    ((BasicDBObject) descDoc2.get("_id")).append("name", "foo2");
    descDoc2.append(FieldDefs.CLUSTER_NAME_PREFIX, "foo2");
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription3);

    // Empty
    when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());
    final Map<ObjectId, Optional<ClusterDescription>> empty =
        _clusterDescriptionDao.findByUniqueIds(Collections.emptyList());
    assertNotNull(empty);
    assertEquals(0, empty.size());

    // All results present
    final Map<ObjectId, Optional<ClusterDescription>> result =
        _clusterDescriptionDao.findByUniqueIds(
            List.of(clusterDescription.getUniqueId(), clusterDescription2.getUniqueId()));
    assertEquals(2, result.size(), "Expected 2 results, was: " + result);
    assertEquals(
        clusterDescription.getUniqueId(),
        result.get(clusterDescription.getUniqueId()).get().getUniqueId());
    assertEquals(
        clusterDescription2.getUniqueId(),
        result.get(clusterDescription2.getUniqueId()).get().getUniqueId());

    // Some missing results
    final ObjectId unknownId = ObjectId.get();
    final Map<ObjectId, Optional<ClusterDescription>> result2 =
        _clusterDescriptionDao.findByUniqueIds(
            List.of(clusterDescription.getUniqueId(), unknownId));
    assertEquals(2, result2.size(), "Expected 2 results, was: " + result2);
    assertEquals(
        clusterDescription.getUniqueId(),
        result2.get(clusterDescription.getUniqueId()).get().getUniqueId());
    assertNotNull(result2.get(unknownId));
    assertFalse(result2.get(unknownId).isPresent());
  }

  @Test
  public void testFindClusterDescriptionIdsByUniqueIds() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c1group");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);

    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c2group");
    ((BasicDBObject) descDoc2.get("_id")).append("name", "foo2");
    descDoc2.append(FieldDefs.CLUSTER_NAME_PREFIX, "foo2");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);

    when(g.getGroupId()).thenReturn(clusterDescription.getGroupId());

    // Empty
    {
      final Map<ObjectId, Optional<ClusterDescriptionId>> result =
          _clusterDescriptionDao.findClusterDescriptionIdsByUniqueIds(Collections.emptyList());
      assertEquals(Map.of(), result);
    }

    // All results present
    {
      final Map<ObjectId, Optional<ClusterDescriptionId>> result =
          _clusterDescriptionDao.findClusterDescriptionIdsByUniqueIds(
              List.of(clusterDescription.getUniqueId(), clusterDescription2.getUniqueId()));
      assertEquals(
          Map.ofEntries(
              Map.entry(
                  clusterDescription.getUniqueId(),
                  Optional.of(new ClusterDescriptionId(clusterDescription))),
              Map.entry(
                  clusterDescription2.getUniqueId(),
                  Optional.of(new ClusterDescriptionId(clusterDescription2)))),
          result);
    }

    // Some missing results
    {
      final ObjectId unknownId = ObjectId.get();
      final Map<ObjectId, Optional<ClusterDescriptionId>> result =
          _clusterDescriptionDao.findClusterDescriptionIdsByUniqueIds(
              List.of(clusterDescription.getUniqueId(), unknownId));
      assertEquals(
          Map.ofEntries(
              Map.entry(
                  clusterDescription.getUniqueId(),
                  Optional.of(new ClusterDescriptionId(clusterDescription))),
              Map.entry(unknownId, Optional.empty())),
          result);
    }
  }

  @Test
  public void testFindByIds() {
    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription(oid(1), "cluster1");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    final BasicDBObject descDoc2 = NDSModelTestFactory.getAWSClusterDescription(oid(2), "cluster2");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);

    // empty
    {
      final List<ClusterDescription> empty =
          _clusterDescriptionDao.findByIds(Collections.emptyList());
      assertNotNull(empty);
      assertTrue(empty.isEmpty(), "Expected empty, was: " + empty);
    }

    // bad ids
    {
      final List<ClusterDescription> empty =
          _clusterDescriptionDao.findByIds(
              List.of(
                  new BasicDBObject()
                      .append(FieldDefs.GROUP_ID, oid(999))
                      .append(FieldDefs.NAME, "doesnotexist")));
      assertNotNull(empty);
      assertTrue(empty.isEmpty(), "Expected empty, was: " + empty);
    }

    // matching ids, different field orders
    {
      final List<ClusterDescription> result =
          _clusterDescriptionDao.findByIds(
              List.of(
                  new BasicDBObject()
                      .append(FieldDefs.GROUP_ID, clusterDescription.getGroupId())
                      .append(FieldDefs.NAME, clusterDescription.getName()),
                  new BasicDBObject()
                      .append(FieldDefs.NAME, clusterDescription2.getName())
                      .append(FieldDefs.GROUP_ID, clusterDescription2.getGroupId())));
      assertEquals(2, result.size(), "Expected 2 results, was: " + result);
      assertEquals(
          Set.of(clusterDescription.getUniqueId(), clusterDescription2.getUniqueId()),
          result.stream().map(ClusterDescription::getUniqueId).collect(Collectors.toSet()));
    }
  }

  @Test
  public void testFindNamesByUniqueIds() {
    Map<ObjectId, Set<ObjectId>> projectToClusterMapping = new HashMap<>();

    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription(oid(1), "cluster1");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);

    final BasicDBObject descDoc2 = NDSModelTestFactory.getAWSClusterDescription(oid(2), "cluster2");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);

    projectToClusterMapping.put(clusterDescription1.getGroupId(), new HashSet<>());
    projectToClusterMapping
        .get(clusterDescription1.getGroupId())
        .add(clusterDescription1.getUniqueId());

    projectToClusterMapping.put(clusterDescription2.getGroupId(), new HashSet<>());
    projectToClusterMapping
        .get(clusterDescription2.getGroupId())
        .add(clusterDescription2.getUniqueId());

    final Collection<Pair<ObjectId, ObjectId>> projectIdClusterIdPairs =
        projectToClusterMapping.entrySet().stream()
            .map(
                entry ->
                    entry.getValue().stream()
                        .map(clusterId -> new ImmutablePair<>(entry.getKey(), clusterId))
                        .collect(Collectors.toSet()))
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());

    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);

    // empty
    {
      final Map<ObjectId, String> emptyMap =
          _clusterDescriptionDao.findNamesByUniqueIds(Collections.emptySet());
      assertTrue(emptyMap.isEmpty());
    }

    // not empty
    {
      final Map<ObjectId, String> nonEmptyMap =
          _clusterDescriptionDao.findNamesByUniqueIds(projectIdClusterIdPairs);
      assertFalse(nonEmptyMap.isEmpty());
      for (Pair<ObjectId, ObjectId> projectIdClusterIdPair : projectIdClusterIdPairs) {
        final Optional<String> clusterName =
            _clusterDescriptionDao.findNameByUniqueId(
                projectIdClusterIdPair.getLeft(), projectIdClusterIdPair.getRight());
        assertTrue(clusterName.isPresent());
        assertEquals(clusterName.get(), nonEmptyMap.get(projectIdClusterIdPair.getRight()));
      }
    }
  }

  @Test
  public void testFindByClusterDescriptionIds() {
    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription(oid(1), "cluster1");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);

    final BasicDBObject descDoc2 = NDSModelTestFactory.getAWSClusterDescription(oid(2), "cluster2");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);

    _clusterDescriptionDao.save(clusterDescription);
    _clusterDescriptionDao.save(clusterDescription2);

    // empty
    {
      final List<ClusterDescription> empty =
          _clusterDescriptionDao.findByClusterDescriptionIds(Collections.emptyList());
      assertNotNull(empty);
      assertTrue(empty.isEmpty(), "Expected empty, was: " + empty);
    }

    // bad ids
    {
      final List<ClusterDescription> empty =
          _clusterDescriptionDao.findByClusterDescriptionIds(
              List.of(new ClusterDescriptionId("abcd", new ObjectId())));
      assertNotNull(empty);
      assertTrue(empty.isEmpty(), "Expected empty, was: " + empty);
    }

    // matching ids, different field orders
    {
      final List<ClusterDescription> result =
          _clusterDescriptionDao.findByClusterDescriptionIds(
              List.of(
                  clusterDescription.getClusterDescriptionId(),
                  clusterDescription2.getClusterDescriptionId()));
      assertEquals(2, result.size(), "Expected 2 results, was: " + result);
      assertEquals(
          Set.of(clusterDescription.getUniqueId(), clusterDescription2.getUniqueId()),
          result.stream().map(ClusterDescription::getUniqueId).collect(Collectors.toSet()));
    }
  }

  @Test
  public void testHasActiveByGroupId() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();
    assertFalse(_clusterDescriptionDao.hasActiveByGroupId(g.getGroupId()));

    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc.get("_id")).append("groupId", g.getGroupId());
    descDoc.put("state", ClusterDescription.State.DELETED.name());
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);
    assertFalse(_clusterDescriptionDao.hasActiveByGroupId(g.getGroupId()));

    final BasicDBObject descDoc2 = NDSModelTestFactory.getAWSClusterDescription();
    ((BasicDBObject) descDoc2.get("_id")).append("groupId", g.getGroupId());
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription2);
    assertTrue(_clusterDescriptionDao.hasActiveByGroupId(g.getGroupId()));
  }

  @Test
  public void testUpdateMongotuneStatus() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();
    final DiskWriteBlockPolicyStatus policyStatus =
        DiskWriteBlockPolicyStatus.builder()
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .setEligible(true)
            .setEnabled(false)
            .build();
    final BasicDBObject descDoc =
        NDSModelTestFactory.getAWSClusterDescription()
            .append(
                "mongotune",
                MongotuneStatus.builder()
                    .state(MongotuneStatus.State.DISABLE)
                    .version("1.0.0")
                    .policies(Map.of(PolicyType.DISK_WRITE_BLOCKING, policyStatus))
                    .build()
                    .toDBObject());
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);

    // Bump version, enable mongotune binary and write blocking policy
    _clusterDescriptionDao.updateMongotuneStatus(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        "1.1.0",
        MongotuneStatus.State.ENABLE.name(),
        Map.of(PolicyType.DISK_WRITE_BLOCKING, policyStatus.toBuilder().setEnabled(true).build()));

    doReturn(clusterDescription.getGroupId()).when(g).getGroupId();
    final ClusterDescription updated =
        _clusterDescriptionDao
            .findByName(g.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    assertEquals("1.1.0", updated.getMongotuneStatus().orElseThrow().version());
    assertEquals(MongotuneStatus.State.ENABLE, updated.getMongotuneStatus().orElseThrow().state());

    final Map<PolicyType, PolicyStatus> policies =
        updated.getMongotuneStatus().orElseThrow().policies();
    assertEquals(1, policies.size());
    assertTrue(policies.containsKey(PolicyType.DISK_WRITE_BLOCKING));

    final PolicyStatus diskWriteBlockPolicyStatus = policies.get(PolicyType.DISK_WRITE_BLOCKING);
    assertTrue(diskWriteBlockPolicyStatus.isEnabled());
    assertInstanceOf(DiskWriteBlockPolicyStatus.class, diskWriteBlockPolicyStatus);

    final DiskWriteBlockPolicyStatus diskWriteBlockPolicy =
        (DiskWriteBlockPolicyStatus) diskWriteBlockPolicyStatus;
    assertEquals(DiskWriteBlockPolicyArgVersion.V1, diskWriteBlockPolicy.getArgVersion());
  }

  @Test
  public void testUpdateMongoDBVersion() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);

    _clusterDescriptionDao.updateMongoDBVersion(
        clusterDescription.getGroupId(), clusterDescription.getName(), "1.2.3");

    doReturn(clusterDescription.getGroupId()).when(g).getGroupId();
    final ClusterDescription updated =
        _clusterDescriptionDao.findByName(g.getGroupId(), clusterDescription.getName()).get();
    assertEquals("1.2.3", updated.getMongoDBVersion().getVersion());
  }

  @Test
  public void testUpdateMongoDBMajorAndFullVersions() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);

    _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
        clusterDescription.getGroupId(), clusterDescription.getName(), VersionUtils.parse("5.1.0"));

    doReturn(clusterDescription.getGroupId()).when(g).getGroupId();
    final ClusterDescription updated =
        _clusterDescriptionDao.findByName(g.getGroupId(), clusterDescription.getName()).get();
    assertEquals("5.1", updated.getMongoDBMajorVersion());
    assertEquals("5.1.0", updated.getMongoDBVersion().getVersion());
  }

  @Test
  public void testFixUnfixMongoDBVersion() {
    final NDSGroup g = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    final String majorVersion = "3.2";
    final String version = "3.2.0";
    final String reason = "Test reason";
    final Date expirationDate = new Date();
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);

    _clusterDescriptionDao.fixMongoDBVersion(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        majorVersion,
        version,
        reason,
        true,
        expirationDate,
        null);

    doReturn(clusterDescription.getGroupId()).when(g).getGroupId();
    final ClusterDescription updated =
        _clusterDescriptionDao.findByName(g.getGroupId(), clusterDescription.getName()).get();
    assertTrue(updated.isMongoDBVersionFixed());
    assertTrue(updated.isMongoDBVersionFixed());
    assertEquals(version, updated.getMongoDBVersion().getVersion());
    assertEquals(version, updated.getFixedMongoDBVersion().get().getVersion());
    assertEquals(reason, updated.getFixedMongoDBVersion().get().getReason());
    assertEquals(VersionReleaseSystem.LTS, updated.getVersionReleaseSystem());
    assertEquals(expirationDate, updated.getFixedMongoDBVersion().get().getExpirationDate());

    _clusterDescriptionDao.unfixMongoDBVersion(
        clusterDescription.getGroupId(), clusterDescription.getName());

    final ClusterDescription updated2 =
        _clusterDescriptionDao.findByName(g.getGroupId(), clusterDescription.getName()).get();
    assertFalse(updated2.isMongoDBVersionFixed());
    assertFalse(updated2.isMongoDBVersionFixed());
  }

  @Test
  public void testSetCancelShardDrainRequested() {
    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);
    _clusterDescriptionDao.save(clusterDescription);

    final ClusterDescription saved =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertEquals(Optional.empty(), saved.getCancelShardDrainRequested());

    final Date shardDrainRequestedDate = new Date();
    _clusterDescriptionDao.setCancelShardDrainRequested(
        clusterDescription.getGroupId(), clusterDescription.getName(), shardDrainRequestedDate);

    final ClusterDescription updated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertEquals(Optional.of(shardDrainRequestedDate), updated.getCancelShardDrainRequested());

    _clusterDescriptionDao.setCancelShardDrainRequested(
        clusterDescription.getGroupId(), clusterDescription.getName(), null);
    final ClusterDescription updated2 =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertEquals(Optional.empty(), updated2.getCancelShardDrainRequested());
  }

  @Test
  public void testSetResetForceReplicaSetReconfig() {
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription);
    final ClusterDescription original =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(original.getForceReplicaSetReconfig());

    // Set the value to true.
    _clusterDescriptionDao.setForceReplicaSetReconfig(
        clusterDescription.getGroupId(), clusterDescription.getName());
    final ClusterDescription updated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(updated.getForceReplicaSetReconfig());

    // Reset the value to false.
    _clusterDescriptionDao.resetForceReplicaSetReconfig(
        clusterDescription.getGroupId(), clusterDescription.getName());
    final ClusterDescription reset =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(reset.getForceReplicaSetReconfig());
  }

  @Test
  public void testFindFixedVersionClusters() {
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c1group");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    _clusterDescriptionDao.save(clusterDescription1);

    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c2group");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription2);

    final Date expirationDate = new Date();
    _clusterDescriptionDao.fixMongoDBVersion(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        "3.2",
        "3.2.0",
        "Test reason.",
        true,
        expirationDate,
        null);

    final List<BasicDBObject> result = _clusterDescriptionDao.findFixedVersionClusters();
    assertEquals(1, result.size());

    assertEquals(
        result.get(0).getString(FieldDefs.VERSION_RELEASE_SYSTEM), VersionReleaseSystem.LTS.name());
    assertEquals(
        "Test reason.",
        Optional.of(result.get(0))
            .map(cd -> cd.get(FieldDefs.FIXED_MONGODB_VERSION))
            .map(BasicDBObject.class::cast)
            .map(
                fixedMongoDBVersion -> fixedMongoDBVersion.getString(FixedVersion.FieldDefs.REASON))
            .get());
    assertEquals(
        expirationDate,
        Optional.of(result.get(0))
            .map(cd -> cd.get(FieldDefs.FIXED_MONGODB_VERSION))
            .map(BasicDBObject.class::cast)
            .map(
                fixedMongoDBVersion ->
                    fixedMongoDBVersion.getDate(FixedVersion.FieldDefs.EXPIRATION_DATE))
            .get());
  }

  @Test
  public void test_fixMongoDBVersion_toAndFromQuarterlyVersions() {
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription()
            .append(FieldDefs.MONGODB_MAJOR_VERSION, "8.1")
            .append(FieldDefs.MONGODB_VERSION, "8.1.0")
            .append(FieldDefs.VERSION_RELEASE_SYSTEM, VersionReleaseSystem.CONTINUOUS.name());
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    _clusterDescriptionDao.save(clusterDescription1);

    _clusterDescriptionDao.fixMongoDBVersion(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        "8.0",
        "8.0.5",
        "Test reason.",
        true,
        new Date(),
        null);

    final List<BasicDBObject> results1 = _clusterDescriptionDao.findFixedVersionClusters();
    assertEquals(1, results1.size());
    assertEquals("8.0", results1.get(0).getString(FieldDefs.MONGODB_MAJOR_VERSION));
    assertEquals("8.0.5", results1.get(0).getString(FieldDefs.MONGODB_VERSION));
    assertEquals(
        VersionReleaseSystem.LTS.name(),
        results1.get(0).getString(FieldDefs.VERSION_RELEASE_SYSTEM));

    _clusterDescriptionDao.fixMongoDBVersion(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        "8.2",
        "8.2.0-rc0",
        "Test reason.",
        true,
        new Date(),
        null);

    final List<BasicDBObject> results2 = _clusterDescriptionDao.findFixedVersionClusters();
    assertEquals(1, results2.size());
    assertEquals("8.2", results2.get(0).getString(FieldDefs.MONGODB_MAJOR_VERSION));
    assertEquals("8.2.0-rc0", results2.get(0).getString(FieldDefs.MONGODB_VERSION));
    assertEquals(
        VersionReleaseSystem.CONTINUOUS.name(),
        results2.get(0).getString(FieldDefs.VERSION_RELEASE_SYSTEM));

    _clusterDescriptionDao.unfixMongoDBVersion(
        clusterDescription1.getGroupId(), clusterDescription1.getName());

    // Should leave it as continuous delivery mode.  This will get automatically upgraded
    final Optional<ClusterDescription> clusterDescriptionOptional =
        _clusterDescriptionDao.findByName(
            clusterDescription1.getGroupId(), clusterDescription1.getName());
    assertTrue(clusterDescriptionOptional.isPresent());
    assertEquals("8.2", clusterDescriptionOptional.get().getMongoDBMajorVersion());
    assertEquals("8.2.0-rc0", clusterDescriptionOptional.get().getMongoDBVersion().toString());
    assertEquals(
        VersionReleaseSystem.CONTINUOUS,
        clusterDescriptionOptional.get().getVersionReleaseSystem());

    // Do not change version release system if explicitly instructed not to
    _clusterDescriptionDao.fixMongoDBVersion(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        "8.0",
        "8.0.5",
        "Test reason.",
        false,
        new Date(),
        null);

    final List<BasicDBObject> results3 = _clusterDescriptionDao.findFixedVersionClusters();
    assertEquals(1, results3.size());
    assertEquals("8.0", results3.get(0).getString(FieldDefs.MONGODB_MAJOR_VERSION));
    assertEquals("8.0.5", results3.get(0).getString(FieldDefs.MONGODB_VERSION));
    assertEquals(
        VersionReleaseSystem.CONTINUOUS.name(),
        results3.get(0).getString(FieldDefs.VERSION_RELEASE_SYSTEM));
  }

  private static Collection<Arguments> getFixVersionTestCases() {
    return List.of(
        Arguments.of(
            "8.0.5", VersionReleaseSystem.CONTINUOUS, "8.0.4", VersionReleaseSystem.CONTINUOUS),
        Arguments.of("8.0.5", VersionReleaseSystem.LTS, "8.0.4", VersionReleaseSystem.LTS),
        Arguments.of("8.0.5", VersionReleaseSystem.LTS, "8.1.0", VersionReleaseSystem.CONTINUOUS),
        Arguments.of("8.1.5", VersionReleaseSystem.CONTINUOUS, "8.0.0", VersionReleaseSystem.LTS),
        Arguments.of(
            "8.1.5", VersionReleaseSystem.CONTINUOUS, "8.1.3", VersionReleaseSystem.CONTINUOUS));
  }

  @ParameterizedTest
  @MethodSource("getFixVersionTestCases")
  public void test_fixMongoDBVersion(
      final String initialVersionString,
      final VersionReleaseSystem initialVersionReleaseSystem,
      final String pinnedVersionString,
      final VersionReleaseSystem expectedVersionReleaseSystem) {

    final Version initialVersion = VersionUtils.parse(initialVersionString);
    final Version pinnedVersion = VersionUtils.parse(pinnedVersionString);

    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription()
            .append(FieldDefs.MONGODB_MAJOR_VERSION, initialVersion.getMajorVersionString())
            .append(FieldDefs.MONGODB_VERSION, initialVersion.toString())
            .append(FieldDefs.VERSION_RELEASE_SYSTEM, initialVersionReleaseSystem);

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    _clusterDescriptionDao.save(clusterDescription);

    _clusterDescriptionDao.fixMongoDBVersion(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        pinnedVersion.getMajorVersionString(),
        pinnedVersionString,
        "Test reason.",
        true,
        new Date(),
        null);

    final List<BasicDBObject> results1 = _clusterDescriptionDao.findFixedVersionClusters();
    assertEquals(1, results1.size());
    assertEquals(
        pinnedVersion.getMajorVersionString(),
        results1.get(0).getString(FieldDefs.MONGODB_MAJOR_VERSION));
    assertEquals(pinnedVersionString, results1.get(0).getString(FieldDefs.MONGODB_VERSION));
    assertEquals(
        expectedVersionReleaseSystem.toString(),
        results1.get(0).getString(FieldDefs.VERSION_RELEASE_SYSTEM));
  }

  @Test
  public void testFixUnfixFeatureCompatibilityVersion() {
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c1Group");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    _clusterDescriptionDao.save(clusterDescription);

    final String reason = "Test Reason";
    final Date expirationDate = new Date();
    _clusterDescriptionDao.fixMongoDBFCVForCluster(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        clusterDescription.getMongoDBMajorVersion(),
        reason,
        expirationDate,
        new Date(),
        null);

    final ClusterDescription updated =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertTrue(updated.isFeatureCompatibilityVersionFixed());
    assertEquals(
        clusterDescription.getMongoDBMajorVersion(),
        updated.getFixedFeatureCompatibilityVersion().get().getVersion());
    assertEquals(
        clusterDescription.getMongoDBMajorVersion(),
        updated.getFixedFeatureCompatibilityVersion().get().getVersion());
    assertEquals(reason, updated.getFixedFeatureCompatibilityVersion().get().getReason());
    assertEquals(
        expirationDate, updated.getFixedFeatureCompatibilityVersion().get().getExpirationDate());

    _clusterDescriptionDao.unfixMongoDBFCVForCluster(
        clusterDescription.getGroupId(), clusterDescription.getName());

    final ClusterDescription updated2 =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .get();
    assertFalse(updated2.isFeatureCompatibilityVersionFixed());
  }

  @Test
  public void testFindFixedFCVClusters() {
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c1Group");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    _clusterDescriptionDao.save(clusterDescription1);

    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.DNS_PIN, "c2Group");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription2);

    _clusterDescriptionDao.fixMongoDBFCVForCluster(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        clusterDescription1.getMongoDBMajorVersion(),
        "Test Reason",
        new Date(),
        new Date(),
        null);

    final List<BasicDBObject> result = _clusterDescriptionDao.findFixedMongoDBFCVClusters();

    assertEquals(1, result.size());

    _clusterDescriptionDao.unfixMongoDBFCVForCluster(
        clusterDescription1.getGroupId(), clusterDescription1.getName());

    final List<BasicDBObject> result2 = _clusterDescriptionDao.findFixedMongoDBFCVClusters();

    assertEquals(0, result2.size());
  }

  @Test
  public void testFindFixed6_1FCVClusters() {
    final BasicDBObject descDoc1 = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    _clusterDescriptionDao.save(clusterDescription1);

    _clusterDescriptionDao.fixMongoDBFCVForCluster(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        FeatureCompatibilityVersion.SIX_ONE.getParameterValue(),
        "Test Reason",
        new Date(),
        new Date(),
        null);

    final List<BasicDBObject> result = _clusterDescriptionDao.findFixedMongoDBFCVClusters();

    assertEquals(1, result.size());

    assertEquals(
        "6.1",
        Optional.of(result.get(0))
            .map(cd -> cd.get(FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION))
            .map(BasicDBObject.class::cast)
            .map(
                fixedFeatureCompatibilityVersion ->
                    fixedFeatureCompatibilityVersion.getString(FixedVersion.FieldDefs.VERSION))
            .get());

    _clusterDescriptionDao.unfixMongoDBFCVForCluster(
        clusterDescription1.getGroupId(), clusterDescription1.getName());

    final List<BasicDBObject> result2 = _clusterDescriptionDao.findFixedMongoDBFCVClusters();

    assertEquals(0, result2.size());
  }

  @Test
  public void testFindFixed6_3FCVClusters() {
    final BasicDBObject descDoc1 = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    _clusterDescriptionDao.save(clusterDescription1);

    _clusterDescriptionDao.fixMongoDBFCVForCluster(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        FeatureCompatibilityVersion.SIX_THREE.getParameterValue(),
        "Test Reason",
        new Date(),
        new Date(),
        null);

    final List<BasicDBObject> result = _clusterDescriptionDao.findFixedMongoDBFCVClusters();

    assertEquals(1, result.size());

    assertEquals(
        "6.3",
        Optional.of(result.get(0))
            .map(cd -> cd.get(FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION))
            .map(BasicDBObject.class::cast)
            .map(
                fixedFeatureCompatibilityVersion ->
                    fixedFeatureCompatibilityVersion.getString(FixedVersion.FieldDefs.VERSION))
            .get());

    _clusterDescriptionDao.unfixMongoDBFCVForCluster(
        clusterDescription1.getGroupId(), clusterDescription1.getName());

    final List<BasicDBObject> result2 = _clusterDescriptionDao.findFixedMongoDBFCVClusters();

    assertEquals(0, result2.size());
  }

  @Test
  public void testFixedOsField() {
    FixedVersion fixedVersion =
        new FixedVersion(OS.AL2023.name(), "Testing", new Date(), new Date(), FixedBy.ADMIN);
    final BasicDBObject descDoc1 = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription1 =
        new ClusterDescription(descDoc1).copy().setFixedOs(Optional.of(fixedVersion)).build();
    assertNotNull(clusterDescription1.getFixedOs());

    _clusterDescriptionDao.save(clusterDescription1);

    final List<BasicDBObject> result = _clusterDescriptionDao.findFixedOsClusters();
    assertEquals(1, result.size());

    assertEquals(
        "AL2023",
        Optional.of(result.get(0))
            .map(cd -> cd.get(FieldDefs.FIXED_OS))
            .map(BasicDBObject.class::cast)
            .map(fixedOs -> fixedOs.getString(FixedVersion.FieldDefs.VERSION))
            .get());
  }

  @Test
  public void testSetEnsureClusterConnectivityAfterForCluster() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getAWSMockedGroup();
    final int numClusters = 3;
    final List<ClusterDescription> savedClusters = new ArrayList<>();
    for (int i = 0; i < numClusters; i++) {
      final BasicDBObject descDoc =
          NDSModelTestFactory.getAWSClusterDescription(ndsGroup.getGroupId(), "cluster" + i);
      final ClusterDescription clusterDescription =
          ClusterDescription.getCloudProviderClusterDescription(descDoc);
      _clusterDescriptionDao.save(clusterDescription);
      savedClusters.add(clusterDescription);
    }

    final Date date = new Date();

    final ClusterDescription clusterToUpdate = savedClusters.get(0);
    _clusterDescriptionDao.setEnsureClusterConnectivityAfterForCluster(
        ndsGroup.getGroupId(), clusterToUpdate.getName(), date);

    final List<ClusterDescription> clustersAfterUpdate =
        _clusterDescriptionDao.findByGroupId(ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    for (final ClusterDescription c : clustersAfterUpdate) {
      if (c.getName().equals(clusterToUpdate.getName())) {
        assertTrue(c.getEnsureClusterConnectivityAfter().isPresent());
      } else {
        assertFalse(c.getEnsureClusterConnectivityAfter().isPresent());
      }
    }
  }

  @Test
  public void testUnsetEnsureClusterConnectivityAfterForCluster() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getAWSMockedGroup();
    final Date initialEnsureClusterConnectivityAfterDate = DateUtils.addHours(new Date(), -1);
    final int numClusters = 3;
    final List<ClusterDescription> savedClusters = new ArrayList<>();
    for (int i = 0; i < numClusters; i++) {
      final BasicDBObject descDoc =
          NDSModelTestFactory.getAWSClusterDescription(ndsGroup.getGroupId(), "cluster" + i)
              .append(
                  FieldDefs.ENSURE_CLUSTER_CONNECTIVITY_AFTER,
                  initialEnsureClusterConnectivityAfterDate);
      final ClusterDescription clusterDescription =
          ClusterDescription.getCloudProviderClusterDescription(descDoc);
      _clusterDescriptionDao.save(clusterDescription);
      savedClusters.add(clusterDescription);
    }

    final ClusterDescription clusterToUpdate = savedClusters.get(0);
    assertTrue(clusterToUpdate.getEnsureClusterConnectivityAfter().isPresent());
    _clusterDescriptionDao.unsetEnsureClusterConnectivityAfterForCluster(
        ndsGroup.getGroupId(), clusterToUpdate.getName(), new Date());

    final List<ClusterDescription> clustersAfterNoop =
        _clusterDescriptionDao.findByGroupId(ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    for (final ClusterDescription c : clustersAfterNoop) {
      assertTrue(c.getEnsureClusterConnectivityAfter().isPresent());
      assertEquals(
          initialEnsureClusterConnectivityAfterDate, c.getEnsureClusterConnectivityAfter().get());
    }

    _clusterDescriptionDao.unsetEnsureClusterConnectivityAfterForCluster(
        ndsGroup.getGroupId(),
        clusterToUpdate.getName(),
        clusterToUpdate.getEnsureClusterConnectivityAfter().get());

    final List<ClusterDescription> clustersAfterUpdate =
        _clusterDescriptionDao.findByGroupId(ndsGroup.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    for (final ClusterDescription c : clustersAfterUpdate) {
      if (c.getName().equals(clusterToUpdate.getName())) {
        assertFalse(c.getEnsureClusterConnectivityAfter().isPresent());
      } else {
        assertTrue(c.getEnsureClusterConnectivityAfter().isPresent());
        assertEquals(
            initialEnsureClusterConnectivityAfterDate, c.getEnsureClusterConnectivityAfter().get());
      }
    }
  }

  @Test
  public void testSetReplicaSetVersionOverride() {
    final ClusterDescription cluster = NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    assertTrue(cluster.getReplicaSetVersionOverride().isEmpty());
    _clusterDescriptionDao.save(cluster);

    _clusterDescriptionDao.setReplicaSetVersionOverride(
        cluster.getGroupId(), cluster.getName(), 10);
    assertEquals(
        10,
        _clusterDescriptionDao
            .findByName(cluster.getGroupId(), cluster.getName())
            .get()
            .getReplicaSetVersionOverride()
            .get()
            .intValue());
  }

  @Test
  public void testSetNeedsPublishDateForCluster() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    final Date date = new Date();

    _clusterDescriptionDao.setNeedsPublishDateForCluster(
        g1.getGroupId(), clusterDescription1.getName(), date);

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    for (final ClusterDescription c : list1) {
      if (c.getName().equals(clusterDescription1.getName())) {
        assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      } else {
        assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      }
      assertEquals(
          ClusterDescription.ProcessRestartAllowedState.NONE,
          c.getNeedsMongoDBConfigPublishRestartAllowed());
    }
  }

  @Test
  public void testSetNeedsPublishDateForClusters() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    final Date date = new Date();

    _clusterDescriptionDao.setNeedsPublishDateForClusters(
        g1.getGroupId(),
        List.of(clusterDescription1.getName(), clusterDescription2.getName()),
        date);

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    // Check that only cluster names in the list are marked as needs config publish
    for (final ClusterDescription c : list1) {
      if (c.getName().equals(clusterDescription1.getName())
          || c.getName().equals(clusterDescription2.getName())) {
        assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      } else {
        assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      }
      assertEquals(
          ClusterDescription.ProcessRestartAllowedState.NONE,
          c.getNeedsMongoDBConfigPublishRestartAllowed());
    }
  }

  @Test
  public void testSetNeedsPublishWithRestartForClusters() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    final Date date = new Date();

    _clusterDescriptionDao.setNeedsPublishWithRestartForClusters(
        g1.getGroupId(),
        List.of(clusterDescription1.getName(), clusterDescription2.getName()),
        date);

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    // Check that only cluster names in the list are marked as needs config publish
    for (final ClusterDescription c : list1) {
      if (c.getName().equals(clusterDescription1.getName())
          || c.getName().equals(clusterDescription2.getName())) {
        assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        assertEquals(
            ClusterDescription.ProcessRestartAllowedState.IMMEDIATE,
            c.getNeedsMongoDBConfigPublishRestartAllowed());
      } else {
        assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        assertEquals(
            ClusterDescription.ProcessRestartAllowedState.NONE,
            c.getNeedsMongoDBConfigPublishRestartAllowed());
      }
    }
  }

  @Test
  public void testSetNeedsPublishWithRestartForCluster() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    final Date date = new Date();

    _clusterDescriptionDao.setNeedsPublishWithRestartForCluster(
        g1.getGroupId(), clusterDescription1.getName(), date);

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    for (final ClusterDescription c : list1) {
      if (c.getName().equals(clusterDescription1.getName())) {
        assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        assertEquals(
            ClusterDescription.ProcessRestartAllowedState.IMMEDIATE,
            c.getNeedsMongoDBConfigPublishRestartAllowed());
      } else {
        assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        assertEquals(
            ClusterDescription.ProcessRestartAllowedState.NONE,
            c.getNeedsMongoDBConfigPublishRestartAllowed());
      }
    }
  }

  @Test
  public void testSetAndUnsetNeedsPublishWithEnvoySyncForCluster() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription awsClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getGroupId(), "x"));
    final ClusterDescription serverlessClusterDescription =
        new ClusterDescription(NDSModelTestFactory.getServerlessClusterDescription())
            .copy()
            .setGroupId(group.getGroupId())
            .build();
    _clusterDescriptionDao.save(awsClusterDescription);
    _clusterDescriptionDao.save(serverlessClusterDescription);

    final Date date = new Date();

    // ensure that aws cluster cannot be set with envoy sync
    _clusterDescriptionDao.setNeedsPublishWithEnvoySyncForServerlessCluster(
        awsClusterDescription.getGroupId(), awsClusterDescription.getName(), date);

    final ClusterDescription refreshedAWSClusterDescription =
        _clusterDescriptionDao
            .findByName(awsClusterDescription.getGroupId(), awsClusterDescription.getName())
            .get();
    assertFalse(refreshedAWSClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertFalse(refreshedAWSClusterDescription.getNeedsEnvoySyncAfter().isPresent());

    // ensure that serverless cluster does get set with envoy sync
    _clusterDescriptionDao.setNeedsPublishWithEnvoySyncForServerlessCluster(
        serverlessClusterDescription.getGroupId(), serverlessClusterDescription.getName(), date);

    final ClusterDescription refreshedServerlessClusterDescription =
        _clusterDescriptionDao
            .findByName(
                serverlessClusterDescription.getGroupId(), serverlessClusterDescription.getName())
            .get();
    assertTrue(
        refreshedServerlessClusterDescription.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertTrue(refreshedServerlessClusterDescription.getNeedsEnvoySyncAfter().isPresent());

    // ensure the serverless cluster envoy sync date is not unset because date does not match
    _clusterDescriptionDao.clearNeedsEnvoySyncDateForCluster(
        serverlessClusterDescription.getGroupId(),
        serverlessClusterDescription.getName(),
        new Date());

    final ClusterDescription refreshedServerlessClusterDescriptionRemains =
        _clusterDescriptionDao
            .findByName(
                serverlessClusterDescription.getGroupId(), serverlessClusterDescription.getName())
            .get();
    assertTrue(
        refreshedServerlessClusterDescriptionRemains
            .getNeedsMongoDBConfigPublishAfter()
            .isPresent());
    assertTrue(refreshedServerlessClusterDescriptionRemains.getNeedsEnvoySyncAfter().isPresent());

    // ensure envoy sync date is unset because date does match, config publish will remain
    _clusterDescriptionDao.clearNeedsEnvoySyncDateForCluster(
        serverlessClusterDescription.getGroupId(), serverlessClusterDescription.getName(), date);

    final ClusterDescription refreshedServerlessClusterDescriptionUnset =
        _clusterDescriptionDao
            .findByName(
                serverlessClusterDescription.getGroupId(), serverlessClusterDescription.getName())
            .get();
    assertTrue(
        refreshedServerlessClusterDescriptionUnset.getNeedsMongoDBConfigPublishAfter().isPresent());
    assertFalse(refreshedServerlessClusterDescriptionUnset.getNeedsEnvoySyncAfter().isPresent());
  }

  @Test
  public void testSetNeedsPublishRestartAllowedForCluster() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    _clusterDescriptionDao.setNeedsMongoDBConfigPublishRestartAllowedForCluster(
        g1.getGroupId(),
        clusterDescription1.getName(),
        ClusterDescription.ProcessRestartAllowedState.IMMEDIATE);

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());

    for (final ClusterDescription c : list1) {
      if (c.getName().equals(clusterDescription1.getName())) {
        assertEquals(
            ClusterDescription.ProcessRestartAllowedState.IMMEDIATE,
            c.getNeedsMongoDBConfigPublishRestartAllowed());
      } else {
        assertEquals(
            ClusterDescription.ProcessRestartAllowedState.NONE,
            c.getNeedsMongoDBConfigPublishRestartAllowed());
      }
    }
  }

  @Test
  public void testSetNeedsPublishDateForClustersInGroup() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc0 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "w");
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final ClusterDescription clusterDescription0 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc0);
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    _clusterDescriptionDao.save(clusterDescription0);
    _clusterDescriptionDao.save(clusterDescription1);

    final NDSGroup g2 = NDSModelTestFactory.getFreeMockedGroup();
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g2.getGroupId(), "y");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription2);

    _clusterDescriptionDao.setNeedsPublishDateForClustersInGroup(g1.getGroupId());

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::getCloudProviderClusterDescription)
            .toList();

    for (final ClusterDescription c : list1) {
      assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      assertEquals(
          ClusterDescription.ProcessRestartAllowedState.NONE,
          c.getNeedsMongoDBConfigPublishRestartAllowed());
    }

    final List<ClusterDescription> list2 =
        _clusterDescriptionDao.findByGroupId(g2.getGroupId()).stream()
            .map(ClusterDescription::getCloudProviderClusterDescription)
            .toList();

    for (final ClusterDescription c : list2) {
      assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      assertEquals(
          ClusterDescription.ProcessRestartAllowedState.NONE,
          c.getNeedsMongoDBConfigPublishRestartAllowed());
    }
  }

  @Test
  public void testSetNeedsPublishWithRestartForClustersInGroup() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc0 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "w");
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final ClusterDescription clusterDescription0 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc0);
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    _clusterDescriptionDao.save(clusterDescription0);
    _clusterDescriptionDao.save(clusterDescription1);

    final NDSGroup g2 = NDSModelTestFactory.getFreeMockedGroup();
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g2.getGroupId(), "y");
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription2);

    _clusterDescriptionDao.setNeedsPublishWithRestartForClustersInGroup(g1.getGroupId());

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::getCloudProviderClusterDescription)
            .toList();

    for (final ClusterDescription c : list1) {
      assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      assertEquals(
          ClusterDescription.ProcessRestartAllowedState.IMMEDIATE,
          c.getNeedsMongoDBConfigPublishRestartAllowed());
    }

    final List<ClusterDescription> list2 =
        _clusterDescriptionDao.findByGroupId(g2.getGroupId()).stream()
            .map(ClusterDescription::getCloudProviderClusterDescription)
            .toList();

    for (final ClusterDescription c : list2) {
      assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      assertEquals(
          ClusterDescription.ProcessRestartAllowedState.NONE,
          c.getNeedsMongoDBConfigPublishRestartAllowed());
    }
  }

  @Test
  public void testSetBiConnectorNeedsSyncForClusters() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    final Date date = new Date();

    _clusterDescriptionDao.setBiConnectorNeedsSyncForClusters(
        g1.getGroupId(),
        List.of(clusterDescription1.getName(), clusterDescription2.getName()),
        date);

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    // Check that only cluster names in the list are marked as needs config publish
    for (final ClusterDescription c : list1) {
      if (c.getName().equals(clusterDescription1.getName())
          || c.getName().equals(clusterDescription2.getName())) {
        assertTrue(c.getBiConnector().getNeedsSync().isPresent());
      } else {
        assertFalse(c.getBiConnector().getNeedsSync().isPresent());
      }
    }
  }

  @Test
  public void testSetNeedsPublishDateForDedicatedClustersInGroup() {
    final NDSGroup g1 = NDSModelTestFactory.getFreeMockedGroup();
    final BasicDBObject descDoc0 =
        NDSModelTestFactory.getDefaultServerlessClusterDescription(g1.getGroupId(), "w");
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig().setGroupId(g1.getGroupId()).setClusterName("x"));
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig().setGroupId(g1.getGroupId()).setClusterName("y"));
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final BasicDBObject descDoc4 =
        NDSModelTestFactory.getFlexClusterDescription("flex", g1.getGroupId());
    final ClusterDescription clusterDescription0 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc0);
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    final ClusterDescription clusterDescription4 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc4);
    _clusterDescriptionDao.save(clusterDescription0);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);
    _clusterDescriptionDao.save(clusterDescription4);

    _clusterDescriptionDao.setNeedsPublishDateForDedicatedClustersInGroup(g1.getGroupId());

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::getCloudProviderClusterDescription)
            .toList();

    for (final ClusterDescription c : list1) {
      if (c.getName().equals(clusterDescription0.getName())
          || c.getName().equals(clusterDescription1.getName())
          || c.getName().equals(clusterDescription2.getName())
          || c.getName().equals(clusterDescription4.getName())) {
        assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      } else {
        assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      }
      assertEquals(
          ClusterDescription.ProcessRestartAllowedState.NONE,
          c.getNeedsMongoDBConfigPublishRestartAllowed());
    }
  }

  @Test
  public void testSetNeedsPublishWithRestartForActiveDedicatedClustersInGroup() {
    final NDSGroup g1 = NDSModelTestFactory.getFreeMockedGroup();
    final BasicDBObject serverlessDoc =
        NDSModelTestFactory.getDefaultServerlessClusterDescription(g1.getGroupId(), "a");
    final BasicDBObject freeDoc =
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig().setGroupId(g1.getGroupId()).setClusterName("b"));
    final BasicDBObject freeDoc2 =
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig().setGroupId(g1.getGroupId()).setClusterName("c"));
    final BasicDBObject awsDoc = NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "d");
    final BasicDBObject pausedDoc =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "e")
            .append(FieldDefs.IS_PAUSED, true);
    final BasicDBObject deletedDoc =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "f")
            .append(ClusterDescription.FieldDefs.STATE, ClusterDescription.State.DELETED.name())
            .append(ClusterDescription.FieldDefs.DELETED_DATE, new Date());
    final ClusterDescription serverlessCD =
        ClusterDescription.getCloudProviderClusterDescription(serverlessDoc);
    final ClusterDescription freeCD =
        ClusterDescription.getCloudProviderClusterDescription(freeDoc);
    final ClusterDescription freeCD2 =
        ClusterDescription.getCloudProviderClusterDescription(freeDoc2);
    final ClusterDescription awsCD = ClusterDescription.getCloudProviderClusterDescription(awsDoc);
    final ClusterDescription pausedCD =
        ClusterDescription.getCloudProviderClusterDescription(pausedDoc);
    final ClusterDescription deletedCD =
        ClusterDescription.getCloudProviderClusterDescription(deletedDoc);
    _clusterDescriptionDao.save(serverlessCD);
    _clusterDescriptionDao.save(freeCD);
    _clusterDescriptionDao.save(freeCD2);
    _clusterDescriptionDao.save(awsCD);
    _clusterDescriptionDao.save(pausedCD);
    _clusterDescriptionDao.save(deletedCD);

    _clusterDescriptionDao.setNeedsPublishWithRestartForActiveDedicatedClustersInGroup(
        g1.getGroupId());

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::getCloudProviderClusterDescription)
            .toList();
    assertEquals(6, list1.size());

    for (final ClusterDescription c : list1) {
      if (c.getName().equals(serverlessCD.getName())
          || c.getName().equals(freeCD.getName())
          || c.getName().equals(freeCD2.getName())) {
        assertEquals(Optional.empty(), c.getNeedsMongoDBConfigPublishAfter());
        assertEquals(
            ClusterDescription.ProcessRestartAllowedState.NONE,
            c.getNeedsMongoDBConfigPublishRestartAllowed());
      } else if (c.getName().equals(awsCD.getName())) {
        assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
        assertEquals(
            ClusterDescription.ProcessRestartAllowedState.IMMEDIATE,
            c.getNeedsMongoDBConfigPublishRestartAllowed());
      } else if (c.getName().equals(pausedCD.getName())
          || c.getName().equals(deletedCD.getName())) {
        assertEquals(Optional.empty(), c.getNeedsMongoDBConfigPublishAfter());
        assertEquals(
            ClusterDescription.ProcessRestartAllowedState.NONE,
            c.getNeedsMongoDBConfigPublishRestartAllowed());
      } else {
        fail("Unexpected cluster!");
      }
    }
  }

  private NDSGroup testSetNeedsPublishForTenantClustersInGroupSetup() {
    final NDSGroup g1 = NDSModelTestFactory.getFreeMockedGroup();
    final BasicDBObject descDoc0 =
        NDSModelTestFactory.getDefaultServerlessClusterDescription(g1.getGroupId(), "serverless");
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig()
                .setGroupId(g1.getGroupId())
                .setClusterName("free0"));
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig()
                .setGroupId(g1.getGroupId())
                .setClusterName("free1"));
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "aws");
    final BasicDBObject descDoc4 =
        NDSModelTestFactory.getFlexClusterDescription("flex", g1.getGroupId());
    final ClusterDescription clusterDescription0 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc0);
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    final ClusterDescription clusterDescription4 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc4);

    _clusterDescriptionDao.save(clusterDescription0);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);
    _clusterDescriptionDao.save(clusterDescription4);

    return g1;
  }

  @Test
  public void testSetNeedsPublishDateForTenantClustersInGroup() {
    final NDSGroup g1 = testSetNeedsPublishForTenantClustersInGroupSetup();

    _clusterDescriptionDao.setNeedsPublishDateForTenantClustersInGroup(g1.getGroupId());

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::getCloudProviderClusterDescription)
            .toList();

    for (final ClusterDescription c : list1) {
      if (!c.getName().equals("aws")) {
        assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      } else {
        assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
      }

      assertEquals(
          ClusterDescription.ProcessRestartAllowedState.NONE,
          c.getNeedsMongoDBConfigPublishRestartAllowed());
    }
  }

  @Test
  public void testSetBiConnectorNeedsSync() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    final Date date = new Date();

    _clusterDescriptionDao.setBiConnectorNeedsSync(
        g1.getGroupId(), clusterDescription1.getName(), date);

    final List<ClusterDescription> list1 =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();

    for (final ClusterDescription c : list1) {
      if (c.getName().equals(clusterDescription1.getName())) {
        assertTrue(c.getBiConnector().getNeedsSync().isPresent());
      } else {
        assertFalse(c.getBiConnector().getNeedsSync().isPresent());
      }
    }
  }

  @Test
  public void testClearNeedsPublishForCluster() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final Date date = new Date();
    descDoc1.put(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, date);
    descDoc2.put(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, DateUtils.addMinutes(date, 1));
    descDoc3.put(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, DateUtils.addMinutes(date, 2));
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    // Nothing should be cleared if date doesn't match
    _clusterDescriptionDao.clearNeedsPublishDateForCluster(
        g1.getGroupId(), clusterDescription1.getName(), DateUtils.addMinutes(date, 3));
    List<ClusterDescription> clusters =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    clusters.forEach(c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));

    // Cluster should be cleared if everything matches
    _clusterDescriptionDao.clearNeedsPublishDateForCluster(
        g1.getGroupId(), clusterDescription1.getName(), date);
    clusters =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();
    clusters.forEach(
        c -> {
          if (c.getName().equals(clusterDescription1.getName())) {
            assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
          } else {
            assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
          }
        });
  }

  @Test
  public void testClearNeedsPublishAndRestartAllowedForCluster() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final Date date = new Date();
    descDoc1.put(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, date);
    descDoc1.put(
        FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
        ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name());
    descDoc2.put(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, DateUtils.addMinutes(date, 1));
    descDoc2.put(
        FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
        ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name());
    descDoc3.put(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_AFTER, DateUtils.addMinutes(date, 2));
    descDoc3.put(
        FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
        ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name());
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    // Nothing should be cleared if date doesn't match
    _clusterDescriptionDao.clearNeedsPublishDateAndRestartAllowedForCluster(
        g1.getGroupId(), clusterDescription1.getName(), DateUtils.addMinutes(date, 3));
    List<ClusterDescription> clusters =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    clusters.forEach(c -> assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent()));

    // Cluster should be cleared if everything matches
    _clusterDescriptionDao.clearNeedsPublishDateAndRestartAllowedForCluster(
        g1.getGroupId(), clusterDescription1.getName(), date);
    clusters =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();
    clusters.forEach(
        c -> {
          if (c.getName().equals(clusterDescription1.getName())) {
            assertFalse(c.getNeedsMongoDBConfigPublishAfter().isPresent());
            assertEquals(
                ClusterDescription.ProcessRestartAllowedState.NONE,
                c.getNeedsMongoDBConfigPublishRestartAllowed());
          } else {
            assertTrue(c.getNeedsMongoDBConfigPublishAfter().isPresent());
            assertEquals(
                ClusterDescription.ProcessRestartAllowedState.IMMEDIATE,
                c.getNeedsMongoDBConfigPublishRestartAllowed());
          }
        });
  }

  @Test
  public void testClearBiConnectorNeedsSync() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final BasicDBObject descDoc3 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "z");
    final BasicDBObject descDoc1BiConnector = (BasicDBObject) descDoc1.get(FieldDefs.BI_CONNECTOR);
    final BasicDBObject descDoc2BiConnector = (BasicDBObject) descDoc2.get(FieldDefs.BI_CONNECTOR);
    final BasicDBObject descDoc3BiConnector = (BasicDBObject) descDoc3.get(FieldDefs.BI_CONNECTOR);
    final Date date = new Date();
    descDoc1BiConnector.put(ClusterDescription.BiConnector.FieldDefs.NEEDS_SYNC, date);
    descDoc2BiConnector.put(
        ClusterDescription.BiConnector.FieldDefs.NEEDS_SYNC, DateUtils.addMinutes(date, 1));
    descDoc3BiConnector.put(
        ClusterDescription.BiConnector.FieldDefs.NEEDS_SYNC, DateUtils.addMinutes(date, 2));
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc3);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    _clusterDescriptionDao.save(clusterDescription3);

    // Nothing should be cleared if date doesn't match
    _clusterDescriptionDao.clearBiConnectorNeedsSync(
        g1.getGroupId(), clusterDescription1.getName(), DateUtils.addMinutes(date, 3));
    List<ClusterDescription> clusters =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .collect(Collectors.toList());
    clusters.forEach(c -> assertTrue(c.getBiConnector().getNeedsSync().isPresent()));

    // Cluster should be cleared if everything matches
    _clusterDescriptionDao.clearBiConnectorNeedsSync(
        g1.getGroupId(), clusterDescription1.getName(), date);
    clusters =
        _clusterDescriptionDao.findByGroupId(g1.getGroupId()).stream()
            .map(ClusterDescription::new)
            .toList();
    clusters.forEach(
        c -> {
          if (c.getName().equals(clusterDescription1.getName())) {
            assertFalse(c.getBiConnector().getNeedsSync().isPresent());
          } else {
            assertTrue(c.getBiConnector().getNeedsSync().isPresent());
          }
        });
  }

  @Test
  public void testUpdateShardsDrainingList() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(new ObjectId(), AWS);
    _clusterDescriptionDao.save(clusterDescription);
    assertEquals(0, clusterDescription.getShardsDraining().size());

    final List<String> updatedShardsDrainingList = new ArrayList<>();
    updatedShardsDrainingList.add("rs0");
    updatedShardsDrainingList.add("rs1");
    updatedShardsDrainingList.add("rs2");
    _clusterDescriptionDao.updateShardsDrainingList(
        clusterDescription.getGroupId(), clusterDescription.getName(), updatedShardsDrainingList);

    final Optional<ClusterDescription> updatedClusterDescription =
        _clusterDescriptionDao.findByName(
            clusterDescription.getGroupId(), clusterDescription.getName());
    assertTrue(updatedClusterDescription.isPresent());
    assertEquals(3, updatedClusterDescription.get().getShardsDraining().size());
    assertTrue(updatedClusterDescription.get().getShardsDraining().contains("rs0"));
    assertTrue(updatedClusterDescription.get().getShardsDraining().contains("rs1"));
    assertTrue(updatedClusterDescription.get().getShardsDraining().contains("rs2"));
  }

  @Test
  public void testAdminSearch() throws Exception {
    final Group group1 = MmsFactory.createGroupWithNDSPlan("adminSearch1");
    final Group group2 = MmsFactory.createGroupWithNDSPlan("adminSearch2");
    _ndsGroupSvc.create(group1.getId(), new NDSManagedX509(), false);
    _ndsGroupSvc.create(group2.getId(), new NDSManagedX509(), false);
    _ndsGroupDao.addCloudContainer(
        group1.getId(),
        new AWSCloudProviderContainer(new ObjectId(), AWSRegionName.AP_NORTHEAST_2));
    _ndsGroupDao.addCloudContainer(
        group2.getId(),
        new AWSCloudProviderContainer(new ObjectId(), AWSRegionName.AP_NORTHEAST_2));
    final NDSGroup ndsGroup1 = _ndsGroupDao.find(group1.getId()).get();
    final NDSGroup ndsGroup2 = _ndsGroupDao.find(group2.getId()).get();

    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(ndsGroup1.getGroupId())
            .append(FieldDefs.DNS_PIN, "c1Group");
    descDoc1.put("cloudProviderContainerId", ndsGroup1.getCloudProviderContainers().get(0).getId());
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(ndsGroup2.getGroupId())
            .append(FieldDefs.DNS_PIN, "c2Group");
    descDoc2.put("cloudProviderContainerId", ndsGroup2.getCloudProviderContainers().get(0).getId());
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);
    ((BasicDBObject) descDoc2.get("_id")).append("name", "bar");
    descDoc2.append(FieldDefs.CLUSTER_NAME_PREFIX, "bar");
    final ClusterDescription clusterDescription3 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription3);

    final ClusterDescription deletedCluster =
        NDSModelTestFactory.getClusterDescription(ndsGroup2.getGroupId(), AWS)
            .copy()
            .setName("deleted")
            .setState(State.DELETED)
            .setDeletedDate(new Date())
            .build();
    _clusterDescriptionDao.save(deletedCluster);

    final ArrayList<ObjectId> ids = new ArrayList<>();

    ids.add(group1.getId());

    final List<ClusterDescription> singleResult =
        _clusterDescriptionDao.adminSearch(ids, List.of(), List.of(), SearchOperator.OR);
    assertEquals(1, singleResult.size());
    assertEquals(ndsGroup1.getGroupId(), singleResult.get(0).getGroupId());

    final List<String> names = new ArrayList<>();
    names.add("foo");
    final List<ClusterDescription> fromDifferentGroups =
        _clusterDescriptionDao.adminSearch(ids, List.of(), names, SearchOperator.OR);
    assertEquals(2, fromDifferentGroups.size());
    assertEquals(
        1,
        fromDifferentGroups.stream()
            .map(ClusterDescription::getGroupId)
            .filter(id -> id.equals(ndsGroup1.getGroupId()))
            .count());
    assertEquals(
        1,
        fromDifferentGroups.stream()
            .map(ClusterDescription::getGroupId)
            .filter(id -> id.equals(ndsGroup2.getGroupId()))
            .count());

    final List<ClusterDescription.State> states = new ArrayList<>();
    states.add(State.DELETED);
    final List<ClusterDescription> deletedCD =
        _clusterDescriptionDao.adminSearch(List.of(), states, List.of(), SearchOperator.AND);
    assertEquals(1, deletedCD.size());
    assertEquals(deletedCluster.getName(), deletedCD.get(0).getName());

    final List<ClusterDescription> fromOneGroup =
        _clusterDescriptionDao.adminSearch(
            List.of(ndsGroup2.getGroupId()), List.of(), List.of(), SearchOperator.AND);
    assertEquals(3, fromOneGroup.size());
    assertTrue(fromOneGroup.stream().allMatch(c -> c.getGroupId().equals(ndsGroup2.getGroupId())));
    // Deleted clusters are sorted to the bottom of the list
    assertEquals(deletedCluster.getName(), fromOneGroup.get(2).getName());

    for (int i = 0; i < NDSDefaults.ADMIN_SEARCH_LIMIT + 1; i++) {
      final String name = "manyClusters" + i;
      _clusterDescriptionDao.save(
          clusterDescription3.copy().setName(name).setClusterNamePrefix(name).build());
    }

    assertEquals(
        NDSDefaults.ADMIN_SEARCH_LIMIT,
        _clusterDescriptionDao
            .adminSearch(
                List.of(), List.of(), Collections.singletonList("manyClusters"), SearchOperator.AND)
            .size());
  }

  @Test
  public void testAddPendingIndexes() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "INeedSomeIndexes";
    final BasicDBObject clusterDescription =
        NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName);
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription);

    final Datastore datastore =
        _clusterDescriptionDao.getMongoSvc().getMorphiaDs(_clusterDescriptionDao.getDbName());
    final List<IndexConfig> pendingIndexConfigs =
        IntStream.range(0, 2)
            .mapToObj(
                i ->
                    _morphiaMapper.fromDBObject(
                        datastore,
                        IndexConfig.class,
                        NDSModelTestFactory.getIndexConfig(
                            clusterName, String.format("index%s", i)),
                        null))
            .collect(Collectors.toList());

    _clusterDescriptionDao.addPendingIndexes(groupId, clusterName, pendingIndexConfigs);

    final ClusterDescription clusterDescriptionUpdated =
        _clusterDescriptionDao.findByName(groupId, clusterName).get();
    assertEquals(pendingIndexConfigs.size(), clusterDescriptionUpdated.getPendingIndexes().size());

    for (int i = 0; i < pendingIndexConfigs.size(); i++) {
      assertEquals(
          pendingIndexConfigs.get(i), clusterDescriptionUpdated.getPendingIndexes().get(i));
    }

    // add to an existing collection
    final IndexConfig indexConfigToAdd =
        _morphiaMapper.fromDBObject(
            datastore,
            IndexConfig.class,
            NDSModelTestFactory.getIndexConfig(clusterName, "index4"),
            null);

    _clusterDescriptionDao.addPendingIndexes(
        groupId, clusterName, Collections.singletonList(indexConfigToAdd));

    final ClusterDescription clusterDescriptionUpdatedAddToExisting =
        _clusterDescriptionDao.findByName(groupId, clusterName).get();
    assertEquals(
        pendingIndexConfigs.size() + 1,
        clusterDescriptionUpdatedAddToExisting.getPendingIndexes().size());

    for (int i = 0; i < pendingIndexConfigs.size(); i++) {
      assertEquals(
          pendingIndexConfigs.get(i),
          clusterDescriptionUpdatedAddToExisting.getPendingIndexes().get(i));
    }
    assertEquals(
        indexConfigToAdd,
        clusterDescriptionUpdatedAddToExisting.getPendingIndexes().get(pendingIndexConfigs.size()));
  }

  @Test
  public void testRemovePendingIndexes() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "IHaveIndexesYouWillRemove";
    final BasicDBObject clusterDescription =
        NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName);
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription);

    final Datastore datastore =
        _clusterDescriptionDao.getMongoSvc().getMorphiaDs(_clusterDescriptionDao.getDbName());
    final List<IndexConfig> indexConfigs =
        IntStream.range(0, 4)
            .mapToObj(
                i ->
                    _morphiaMapper.fromDBObject(
                        datastore,
                        IndexConfig.class,
                        NDSModelTestFactory.getIndexConfig(
                            clusterName, String.format("index%s", i)),
                        null))
            .collect(Collectors.toList());

    _clusterDescriptionDao.addPendingIndexes(groupId, clusterName, indexConfigs);

    final ClusterDescription clusterDescriptionWithPendingIndexes =
        _clusterDescriptionDao.findByName(groupId, clusterName).get();
    assertEquals(
        indexConfigs.size(), clusterDescriptionWithPendingIndexes.getPendingIndexes().size());

    final List<IndexConfig> indexesToRemove =
        IntStream.range(0, indexConfigs.size() - 2)
            .mapToObj(indexConfigs::get)
            .collect(Collectors.toList());

    _clusterDescriptionDao.removePendingIndexes(groupId, clusterName, indexesToRemove);
    final ClusterDescription clusterDescriptionWithRemovedIndexes =
        _clusterDescriptionDao.findByName(groupId, clusterName).get();
    assertEquals(
        indexConfigs.size() - 2, clusterDescriptionWithRemovedIndexes.getPendingIndexes().size());

    for (int i = 2; i < indexConfigs.size(); i++) {
      assertEquals(
          indexConfigs.get(i), clusterDescriptionWithRemovedIndexes.getPendingIndexes().get(i - 2));
    }
  }

  @Test
  public void testUpdateTenantClusterNdsAccessTemporarilyRevoked() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "clusterName";
    final BasicDBObject clusterDescription =
        NDSModelTestFactory.getFreeClusterDescription(clusterName, groupId);
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription);
    final Optional<ClusterDescription> result =
        _clusterDescriptionDao.findByName(groupId, clusterName);
    assertTrue(result.isPresent());
    assertTrue(
        _clusterDescriptionDao.updateTenantClusterNdsAccessTemporarilyRevoked(result.get(), true));
  }

  @Test
  public void testUpdateByLastUpdateDate() throws StaleUpdateException {
    final BasicDBObject clusterDescription = NDSModelTestFactory.getAWSClusterDescription();

    // Disable disk backup. This could be any field, we just pick this one as it's top level and
    // easy.
    clusterDescription.append(FieldDefs.DISK_BACKUP_ENABLED, false);
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription);

    clusterDescription.append(FieldDefs.DISK_BACKUP_ENABLED, true);
    // Setting it enabled should succeed as the dates match
    _clusterDescriptionDao.updateByLastUpdateDate(new ClusterDescription(clusterDescription));

    clusterDescription.append(FieldDefs.DISK_BACKUP_ENABLED, false);
    // Setting it back to disabled should fail as lastUpdateDate does not match the last change
    assertThrows(
        StaleUpdateException.class,
        () ->
            _clusterDescriptionDao.updateByLastUpdateDate(
                new ClusterDescription(clusterDescription)));

    final Date lastUpdateDate =
        _clusterDescriptionDao
            .findOne(new BasicDBObject("_id", clusterDescription.get("_id")))
            .getDate(FieldDefs.LAST_UPDATE_DATE);

    clusterDescription.append(FieldDefs.LAST_UPDATE_DATE, lastUpdateDate);

    _clusterDescriptionDao.updateByLastUpdateDate(new ClusterDescription(clusterDescription));
  }

  @Test
  public void testUpdateOsTunedFileOverrides() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "clusterName";
    final BasicDBObject clusterDescription =
        NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName);
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription);

    // list with values should update list
    OsTunedFileOverrides osTunedFileOverrides =
        new OsTunedFileOverrides(Map.of("sysctl", List.of("vm.dirty_bytes=1024")), Map.of());
    _clusterDescriptionDao.updateOsTunedFileOverrides(groupId, clusterName, osTunedFileOverrides);
    Optional<ClusterDescription> result = _clusterDescriptionDao.findByName(groupId, clusterName);
    assertTrue(result.isPresent());
    assertEquals(result.get().getOsTunedFileOverrides(), osTunedFileOverrides);

    // update with config
    osTunedFileOverrides =
        new OsTunedFileOverrides(Map.of(), Map.of("sysctl", List.of("vm.dirty_bytes=1024")));
    _clusterDescriptionDao.updateOsTunedFileOverrides(groupId, clusterName, osTunedFileOverrides);
    result = _clusterDescriptionDao.findByName(groupId, clusterName);
    assertTrue(result.isPresent());
    assertEquals(result.get().getOsTunedFileOverrides(), osTunedFileOverrides);

    // empty list should clear osTunedFileOverrides
    osTunedFileOverrides = new OsTunedFileOverrides(Map.of(), Map.of());
    _clusterDescriptionDao.updateOsTunedFileOverrides(groupId, clusterName, osTunedFileOverrides);
    result = _clusterDescriptionDao.findByName(groupId, clusterName);
    assertTrue(result.isPresent());
    assertTrue(result.get().getOsTunedFileOverrides().getShardOverrides().isEmpty());
  }

  @Test
  public void testSetFastProvisionType() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "clusterName";
    final BasicDBObject clusterDescription =
        NDSModelTestFactory.getServerlessClusterDescription(
            new TestServerlessClusterDescriptionConfig()
                .setGroupId(groupId)
                .setClusterName(clusterName)
                .setClusterProvisionType(ClusterProvisionType.FAST));
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription);
    final ClusterDescription clusterDescriptionBefore =
        _clusterDescriptionDao.findByName(groupId, clusterName).get();
    assertEquals(ClusterProvisionType.FAST, clusterDescriptionBefore.getClusterProvisionType());
    _clusterDescriptionDao.setClusterProvisionType(
        groupId, clusterName, ClusterProvisionType.REGULAR);
    final ClusterDescription clusterDescriptionAfter =
        _clusterDescriptionDao.findByName(groupId, clusterName).get();
    assertEquals(ClusterProvisionType.REGULAR, clusterDescriptionAfter.getClusterProvisionType());
  }

  @Test
  public void testSetPendingIndexActionCancel() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "cluster";
    final BasicDBObject clusterDescription =
        NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName);
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription);

    final Datastore datastore =
        _clusterDescriptionDao.getMongoSvc().getMorphiaDs(_clusterDescriptionDao.getDbName());
    final List<IndexConfig> indexConfigs =
        IntStream.range(0, 4)
            .mapToObj(
                i ->
                    _morphiaMapper.fromDBObject(
                        datastore,
                        IndexConfig.class,
                        NDSModelTestFactory.getIndexConfig(
                            clusterName, String.format("index%s", i)),
                        null))
            .collect(Collectors.toList());

    _clusterDescriptionDao.addPendingIndexes(groupId, clusterName, indexConfigs);

    final ClusterDescription clusterDescriptionWithPendingIndexes =
        _clusterDescriptionDao.findByName(groupId, clusterName).get();
    assertEquals(
        indexConfigs.size(), clusterDescriptionWithPendingIndexes.getPendingIndexes().size());

    final IndexConfig indexesToCancel = indexConfigs.get(0);

    _clusterDescriptionDao.setPendingIndexActionCancel(groupId, clusterName, indexesToCancel);
    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao.findByName(groupId, clusterName).get();
    assertEquals(indexConfigs.size(), updatedClusterDescription.getPendingIndexes().size());

    indexesToCancel.setCancel();

    for (int i = 0; i < indexConfigs.size(); i++) {
      assertEquals(indexConfigs.get(i), updatedClusterDescription.getPendingIndexes().get(i));
    }
  }

  @Test
  public void testUpdatePrivateEndpointSRVAddressMap() {
    final NDSGroup g1 = NDSModelTestFactory.getAWSMockedGroup();
    final BasicDBObject descDoc1 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "x");
    final BasicDBObject descDoc2 =
        NDSModelTestFactory.getAWSClusterDescription(g1.getGroupId(), "y");
    final ClusterDescription clusterDescription1 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc1);
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2);
    _clusterDescriptionDao.save(clusterDescription1);
    _clusterDescriptionDao.save(clusterDescription2);

    final Map<String, String> privateEndpointSRVAddressMap =
        Map.of(
            AWS_ENDPOINT_ID,
            PRIVATE_ENDPOINT_SRV_ADDRESS,
            AWS_ENDPOINT_ID_2,
            PRIVATE_ENDPOINT_SRV_ADDRESS_2);
    _clusterDescriptionDao.updatePrivateEndpointSRVAddressMap(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        privateEndpointSRVAddressMap);

    _clusterDescriptionDao.findByGroupId(clusterDescription1.getGroupId()).stream()
        .map(ClusterDescription::new)
        .forEach(
            c -> {
              if (c.getName().equals(clusterDescription1.getName())) {
                assertEquals(privateEndpointSRVAddressMap, c.getPrivateSrvAddressMap());
              } else {
                assertTrue(c.getPrivateSrvAddressMap().isEmpty());
              }
            });
  }

  @Test
  public void testSetHasPrivateEndpointLegacyConnectionStrings() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject clusterDescriptionDoc =
        NDSModelTestFactory.getShardedAWSClusterDescription(group.getGroupId(), "cluster0", 2)
            .append(
                ShardedClusterDescription.FieldDefs.HAS_PRIVATE_ENDPOINT_LEGACY_CONNECTION_STRINGS,
                false);

    final ShardedClusterDescription clusterDescription =
        (ShardedClusterDescription)
            ShardedClusterDescription.getCloudProviderClusterDescription(clusterDescriptionDoc);
    assertFalse(clusterDescription.hasPrivateEndpointLegacyConnectionStrings());

    _clusterDescriptionDao.save(clusterDescription);

    _clusterDescriptionDao.setHasPrivateEndpointLegacyConnectionStrings(
        clusterDescription.getGroupId(), clusterDescription.getName(), true);

    final ShardedClusterDescription newClusterDescription =
        (ShardedClusterDescription)
            _clusterDescriptionDao
                .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
                .orElseThrow();
    assertTrue(newClusterDescription.hasPrivateEndpointLegacyConnectionStrings());
  }

  @Test
  public void testClearPauseRequest() {
    final String name = "Cluster0";
    final ObjectId groupId = oid(1);
    final BasicDBObject id =
        new BasicDBObject().append(FieldDefs.NAME, name).append(FieldDefs.GROUP_ID, groupId);

    // Set now to a minute ago, so the first new dates created the dao update methods are fresher
    final Date now = Date.from(Instant.now().minus(Duration.ofMinutes(1)));
    final Date earlier = Date.from(Instant.now().minus(Duration.ofMinutes(5)));

    {
      // Successfully clear flag
      _clusterDescriptionDao.saveMajority(
          new BasicDBObject()
              .append(FieldDefs.ID, id)
              .append(FieldDefs.LAST_UPDATE_DATE, now)
              .append(FieldDefs.IS_PAUSED, true));

      assertTrue(_clusterDescriptionDao.clearPauseRequest(name, groupId, now));
      final BasicDBObject foundDoc =
          _clusterDescriptionDao.findOne(new BasicDBObject(FieldDefs.ID, id));
      assertFalse(foundDoc.getBoolean(FieldDefs.IS_PAUSED));
      assertNotEquals(now, foundDoc.getDate(FieldDefs.LAST_UPDATE_DATE));
    }

    {
      // Fail to clear flag - already false
      _clusterDescriptionDao.saveMajority(
          new BasicDBObject()
              .append(FieldDefs.ID, id)
              .append(FieldDefs.LAST_UPDATE_DATE, now)
              .append(FieldDefs.IS_PAUSED, false));

      assertFalse(_clusterDescriptionDao.clearPauseRequest(name, groupId, now));
      final BasicDBObject foundDoc =
          _clusterDescriptionDao.findOne(new BasicDBObject(FieldDefs.ID, id));
      assertFalse(foundDoc.getBoolean(FieldDefs.IS_PAUSED));
      assertEquals(now, foundDoc.getDate(FieldDefs.LAST_UPDATE_DATE));
    }

    {
      // Fail to clear flag - last update mismatch
      _clusterDescriptionDao.saveMajority(
          new BasicDBObject()
              .append(FieldDefs.ID, id)
              .append(FieldDefs.LAST_UPDATE_DATE, earlier)
              .append(FieldDefs.IS_PAUSED, true));

      assertFalse(_clusterDescriptionDao.clearPauseRequest(name, groupId, now));
      final BasicDBObject foundDoc =
          _clusterDescriptionDao.findOne(new BasicDBObject(FieldDefs.ID, id));
      assertTrue(foundDoc.getBoolean(FieldDefs.IS_PAUSED));
      assertEquals(earlier, foundDoc.getDate(FieldDefs.LAST_UPDATE_DATE));
    }
  }

  @Test
  public void testSetOSForCluster_replica() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getGroupId(), "cluster0"));

    final OS originalOS = clusterDescription.getOS(NodeType.ELECTABLE);
    final OS otherOS = Arrays.stream(OS.values()).filter(os -> os != originalOS).findFirst().get();
    _clusterDescriptionDao.save(clusterDescription);

    assertTrue(clusterDescription.getClusterType().isReplicaSet());

    {
      // Set to another OS on GCP
      Arrays.stream(GCPInstanceFamily.values())
          .forEach(
              gcpInstanceFamily ->
                  _clusterDescriptionDao.setOSForCluster(
                      group.getGroupId(),
                      clusterDescription.getName(),
                      clusterDescription.getClusterType().isSharded(),
                      GCP,
                      gcpInstanceFamily,
                      otherOS));

      final ClusterDescription cdAfterGCPOSUpdate =
          _clusterDescriptionDao.findByGroup(group.getGroupId()).get(0);
      final ReplicationSpec rs = cdAfterGCPOSUpdate.getReplicationSpecsWithShardData().get(0);
      final List<RegionConfig> rcs = rs.getRegionConfigs();
      // Confirm everything is unchanged as the hardware was not in GCP
      rcs.forEach(
          rc -> {
            assertEquals(originalOS, rc.getElectableSpecs().getOS());
            assertEquals(originalOS, rc.getAnalyticsSpecs().getOS());
            assertEquals(originalOS, rc.getHiddenSecondarySpecs().getOS());
            assertEquals(originalOS, rc.getReadOnlySpecs().getOS());
          });
    }

    {
      // Now set to the other OS on AWS
      Arrays.stream(AWSInstanceFamily.values())
          .forEach(
              awsInstanceFamily ->
                  _clusterDescriptionDao.setOSForCluster(
                      group.getGroupId(),
                      clusterDescription.getName(),
                      clusterDescription.getClusterType().isSharded(),
                      AWS,
                      awsInstanceFamily,
                      otherOS));

      final ClusterDescription cdAfterAWSOSUpdate =
          _clusterDescriptionDao.findByGroup(group.getGroupId()).get(0);
      final ReplicationSpec rs = cdAfterAWSOSUpdate.getReplicationSpecsWithShardData().get(0);
      final List<RegionConfig> rcs = rs.getRegionConfigs();
      // Confirm everything is changed as we updated OS for AWS this time
      rcs.forEach(
          rc -> {
            assertEquals(otherOS, rc.getElectableSpecs().getOS());
            assertEquals(otherOS, rc.getAnalyticsSpecs().getOS());
            assertEquals(otherOS, rc.getHiddenSecondarySpecs().getOS());
            assertEquals(otherOS, rc.getReadOnlySpecs().getOS());
          });
    }
  }

  @Test
  public void testSetOSForCluster_sharded() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSShardedClusterDescription(group.getGroupId(), "cluster0"));

    final OS originalOS = clusterDescription.getOS(NodeType.ELECTABLE);
    final OS otherOS = Arrays.stream(OS.values()).filter(os -> os != originalOS).findFirst().get();
    _clusterDescriptionDao.save(clusterDescription);

    assertTrue(clusterDescription.getClusterType().isSharded());

    {
      // Set to another OS on GCP
      Arrays.stream(GCPInstanceFamily.values())
          .forEach(
              gcpInstanceFamily ->
                  _clusterDescriptionDao.setOSForCluster(
                      group.getGroupId(),
                      clusterDescription.getName(),
                      clusterDescription.getClusterType().isSharded(),
                      GCP,
                      gcpInstanceFamily,
                      otherOS));

      final ClusterDescription cdAfterGCPOSUpdate =
          _clusterDescriptionDao.findByGroup(group.getGroupId()).get(0);
      final List<ReplicationSpec> rsList =
          cdAfterGCPOSUpdate.getAllReplicationSpecsIncludingConfig();

      // Confirm everything is unchanged as the hardware was not in GCP
      rsList.stream()
          .flatMap(rs -> rs.getRegionConfigs().stream())
          .forEach(
              rc -> {
                assertEquals(originalOS, rc.getElectableSpecs().getOS());
                if (rc.getAnalyticsSpecs() != null) {
                  assertEquals(originalOS, rc.getAnalyticsSpecs().getOS());
                }
                if (rc.getHiddenSecondarySpecs() != null) {
                  assertEquals(originalOS, rc.getHiddenSecondarySpecs().getOS());
                }
                if (rc.getReadOnlySpecs() != null) {
                  assertEquals(originalOS, rc.getReadOnlySpecs().getOS());
                }
              });
    }

    {
      // Now set to the other OS on AWS
      Arrays.stream(AWSInstanceFamily.values())
          .forEach(
              awsInstanceFamily ->
                  _clusterDescriptionDao.setOSForCluster(
                      group.getGroupId(),
                      clusterDescription.getName(),
                      clusterDescription.getClusterType().isSharded(),
                      AWS,
                      awsInstanceFamily,
                      otherOS));

      final ClusterDescription cdAfterAWSOSUpdate =
          _clusterDescriptionDao.findByGroup(group.getGroupId()).get(0);
      final ReplicationSpec rs = cdAfterAWSOSUpdate.getReplicationSpecsWithShardData().get(0);

      // Confirm everything is changed as we updated OS for AWS this time
      rs.getRegionConfigs()
          .forEach(
              rc -> {
                assertEquals(otherOS, rc.getElectableSpecs().getOS());
                assertEquals(otherOS, rc.getAnalyticsSpecs().getOS());
                assertEquals(otherOS, rc.getHiddenSecondarySpecs().getOS());
                assertEquals(otherOS, rc.getReadOnlySpecs().getOS());
              });

      final ReplicationSpec rsConfig =
          ((ShardedClusterDescription) cdAfterAWSOSUpdate)
              .getDedicatedConfigServerReplicationSpec()
              .get();
      rsConfig
          .getRegionConfigs()
          .forEach(rc -> assertEquals(otherOS, rc.getElectableSpecs().getOS()));
    }
  }

  @Test
  public void testSetAzurePreferredStorageTypeForCluster_replica() {
    final NDSGroup group = NDSModelTestFactory.getAzureMockedGroup();

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAzureClusterDescription(group.getGroupId(), "cluster0"));
    _clusterDescriptionDao.save(clusterDescription);

    assertTrue(clusterDescription.getClusterType().isReplicaSet());
    testSetAzurePreferredStorageTypeForCluster(clusterDescription);
  }

  @Test
  public void testSetAzurePreferredStorageTypeForCluster_sharded_dedicatedConfig() {
    final NDSGroup group = NDSModelTestFactory.getAzureMockedGroup();

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAzureShardedClusterDescription(group.getGroupId()));
    _clusterDescriptionDao.save(clusterDescription);

    assertTrue(clusterDescription.getClusterType().isSharded());
    testSetAzurePreferredStorageTypeForCluster(clusterDescription);
  }

  @Test
  public void testSetAzurePreferredStorageTypeForCluster_sharded_embeddedConfig() {
    final NDSGroup group = NDSModelTestFactory.getAzureMockedGroup();

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAzureShardedClusterDescriptionWithEmbeddedConfig(
                group.getGroupId()));

    _clusterDescriptionDao.save(clusterDescription);

    assertTrue(clusterDescription.getClusterType().isSharded());
    testSetAzurePreferredStorageTypeForCluster(clusterDescription);
  }

  @Test
  public void testSetAzurePreferredStorageTypeForCluster_aws() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getGroupId()));
    _clusterDescriptionDao.save(clusterDescription);

    assertTrue(clusterDescription.getClusterType().isReplicaSet());
    assertEquals(
        Collections.emptyList(),
        AzureClusterTestUtils.getAzurePreferredStorageTypes(clusterDescription));

    _clusterDescriptionDao.setAzurePreferredStorageTypeForCluster(clusterDescription, "V2");

    assertEquals(
        Collections.emptyList(),
        AzureClusterTestUtils.getAzurePreferredStorageTypes(
            _clusterDescriptionDao
                .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
                .orElseThrow()));
  }

  private void testSetAzurePreferredStorageTypeForCluster(final ClusterDescription pCluster) {
    // preferred disk type should be NONE by default
    assertPreferredStorageType(pCluster, PreferredStorageType.NONE);

    _clusterDescriptionDao.setAzurePreferredStorageTypeForCluster(pCluster, "V2");

    assertPreferredStorageType(
        _clusterDescriptionDao.findByName(pCluster.getGroupId(), pCluster.getName()).orElseThrow(),
        PreferredStorageType.V2);
  }

  private void assertPreferredStorageType(
      final ClusterDescription pCluster, final PreferredStorageType pStorageType) {
    final List<PreferredStorageType> types =
        AzureClusterTestUtils.getAzurePreferredStorageTypes(pCluster);
    final boolean hasDedicatedConfig =
        pCluster.getClusterType().isSharded()
            && ((ShardedClusterDescription) pCluster).getConfigServerType().isDedicated();
    final int configHardwareSpecCount = hasDedicatedConfig ? 1 : 0;
    final int hardwareSpecCount = pCluster.getNumShards() * 4;
    assertEquals(configHardwareSpecCount + hardwareSpecCount, types.size());
    assertEquals(Set.of(pStorageType), new HashSet<>(types));
  }

  @Test
  public void testSetNeedsDbCheckAfter() {
    final ClusterDescription cd = NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    _clusterDescriptionDao.save(cd);
    assertNull(cd.getNeedsDbCheckAfter());

    final Date date = new Date();
    _clusterDescriptionDao.setNeedsDbCheckAfter(cd.getGroupId(), cd.getName(), date);

    final ClusterDescription updatedCd =
        _clusterDescriptionDao
            .findByUniqueId(cd.getGroupId(), cd.getUniqueId())
            .orElseThrow(IllegalStateException::new);
    assertEquals(date, updatedCd.getNeedsDbCheckAfter());
  }

  @Test
  public void testResetDbCheckPreflightRetryCount() {
    final ClusterDescription cd =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setDbCheckPreflightRetryCount(2)
            .build();
    _clusterDescriptionDao.save(cd);
    assertEquals(2, cd.getDbCheckPreflightRetryCount());
    assertNull(cd.getNeedsDbCheckAfter());

    _clusterDescriptionDao.resetDbCheckPreflightRetryCount(cd.getGroupId(), cd.getName());

    final ClusterDescription updatedCd =
        _clusterDescriptionDao
            .findByUniqueId(cd.getGroupId(), cd.getUniqueId())
            .orElseThrow(IllegalStateException::new);
    assertEquals(0, updatedCd.getDbCheckPreflightRetryCount());

    // Assert that the date exists and is in the future. Not going to bother with exact date checks.
    assertNotNull(updatedCd.getNeedsDbCheckAfter());
    assertTrue(updatedCd.getNeedsDbCheckAfter().compareTo(new Date()) > 0);
  }

  @Test
  public void testIncrementDbCheckPreflightRetryCount() {
    final ClusterDescription cd = NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    _clusterDescriptionDao.save(cd);
    assertEquals(0, cd.getDbCheckPreflightRetryCount()); // Deserializes to 0 even when undefined.

    _clusterDescriptionDao.incrementDbCheckPreflightRetryCount(cd.getGroupId(), cd.getName());

    {
      final ClusterDescription updatedCd =
          _clusterDescriptionDao
              .findByUniqueId(cd.getGroupId(), cd.getUniqueId())
              .orElseThrow(IllegalStateException::new);
      assertEquals(1, updatedCd.getDbCheckPreflightRetryCount());
    }

    {
      _clusterDescriptionDao.incrementDbCheckPreflightRetryCount(cd.getGroupId(), cd.getName());
      final ClusterDescription updatedCd =
          _clusterDescriptionDao
              .findByUniqueId(cd.getGroupId(), cd.getUniqueId())
              .orElseThrow(IllegalStateException::new);
      assertEquals(2, updatedCd.getDbCheckPreflightRetryCount());
    }
  }

  @Test
  public void testSetLastDbCheckDate() {
    final ClusterDescription cd = NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    _clusterDescriptionDao.save(cd);
    assertNull(cd.getLastDbCheckDate());

    final Date date = new Date();
    _clusterDescriptionDao.setLastDbCheckDate(cd.getGroupId(), cd.getName(), date);

    final ClusterDescription updatedCd =
        _clusterDescriptionDao
            .findByUniqueId(cd.getGroupId(), cd.getUniqueId())
            .orElseThrow(IllegalStateException::new);
    assertEquals(date, updatedCd.getLastDbCheckDate());
  }

  @Test
  public void testSetOSPolicyVersionAndIsCriticalOSPolicyRelease() {
    final ClusterDescription cd = NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    _clusterDescriptionDao.save(cd);
    assertEquals(
        Optional.of(NDSModelTestFactory.DEFAULT_OS_POLICY_VERSION), cd.getOSPolicyVersion());
    // Test default value of isCriticalOSPolicyRelease is false
    assertEquals(Optional.of(false), cd.getIsCriticalOSPolicyRelease());

    final String newOSPolicyVersion = "190";
    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        cd.getGroupId(), cd.getName(), newOSPolicyVersion, true);

    final ClusterDescription updatedCd =
        _clusterDescriptionDao
            .findByUniqueId(cd.getGroupId(), cd.getUniqueId())
            .orElseThrow(IllegalStateException::new);
    assertEquals(newOSPolicyVersion, updatedCd.getOSPolicyVersion().orElse(""));
    assertEquals(true, updatedCd.getIsCriticalOSPolicyRelease().orElse(false));
  }

  @Test
  public void testSetLastDataValidationDate() {
    final ClusterDescription cd = NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    _clusterDescriptionDao.save(cd);
    assertNull(cd.getLastDataValidationDate());

    final Date date = new Date();
    _clusterDescriptionDao.setLastDataValidationDate(cd.getGroupId(), cd.getName(), date);

    final ClusterDescription updatedCd =
        _clusterDescriptionDao
            .findByUniqueId(cd.getGroupId(), cd.getUniqueId())
            .orElseThrow(IllegalStateException::new);
    assertEquals(date, updatedCd.getLastDataValidationDate());
  }

  @Test
  public void testCountCorruptionDetectionEligibleClusters() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final Date oldDate = new Date(0);
    _appSettings.clearMemory();
    _appSettings.setProp(
        Fields.CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX.value, "", SettingType.MEMORY);

    // No eligible clusters yet
    assertEquals(
        new CorruptionDetectionEligibility(0, 0, 0),
        _clusterDescriptionDao.countCorruptionDetectionEligibleClusters());

    final ClusterDescription newCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "newCluster", CloudProvider.AWS)
            .copy()
            .setCreateDate(new Date())
            .build();
    _clusterDescriptionDao.save(newCluster);
    final ClusterDescription deletedCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "deletedCluster", CloudProvider.AWS)
            .copy()
            .setCreateDate(oldDate)
            .setState(State.DELETED)
            .build();
    _clusterDescriptionDao.save(deletedCluster);
    final ClusterDescription pausedCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "pausedCluster", CloudProvider.AWS)
            .copy()
            .setCreateDate(oldDate)
            .setIsPaused(true)
            .build();
    _clusterDescriptionDao.save(pausedCluster);
    final ClusterDescription freeCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "freeCluster", CloudProvider.FREE)
            .copy()
            .setCreateDate(oldDate)
            .build();
    _clusterDescriptionDao.save(freeCluster);
    final ClusterDescription serverlessCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "serverlessCluster", CloudProvider.SERVERLESS)
            .copy()
            .setCreateDate(oldDate)
            .build();
    _clusterDescriptionDao.save(serverlessCluster);
    final ClusterDescription checkedTooRecently =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "checkedTooRecently", CloudProvider.AWS)
            .copy()
            .setCreateDate(oldDate)
            .setLastDbCheckDate(new Date())
            .setLastDataValidationDate(new Date())
            .build();
    _clusterDescriptionDao.save(checkedTooRecently);
    final ShardedClusterDescription shardedClusterTooNew =
        NDSModelTestFactory.getShardedClusterDescription(group.getGroupId(), CloudProvider.AWS)
            .copy()
            .setCreateDate(new Date())
            .build();
    _clusterDescriptionDao.save(shardedClusterTooNew);

    // No eligible clusters yet
    assertEquals(
        new CorruptionDetectionEligibility(0, 0, 0),
        _clusterDescriptionDao.countCorruptionDetectionEligibleClusters());

    // dbCheck and dataValidation ELIGIBLE
    final ClusterDescription neverChecked =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "neverChecked", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("8.0.3")
            .setCreateDate(oldDate)
            .build();
    assertNull(neverChecked.getLastDbCheckDate());
    assertNull(neverChecked.getLastDataValidationDate());
    assertEquals(oldDate, neverChecked.getCreateDate());
    assertNotEquals(State.DELETED, neverChecked.getState());
    assertFalse(neverChecked.isPaused());
    assertTrue(oldDate.compareTo(Date.from(Instant.now().minus(182, ChronoUnit.DAYS))) < 0);
    _clusterDescriptionDao.save(neverChecked);
    assertEquals(
        new CorruptionDetectionEligibility(1, 1, 0),
        _clusterDescriptionDao.countCorruptionDetectionEligibleClusters());

    // dbCheck ELIGIBLE
    final ClusterDescription oldDbChecked =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "oldDbChecked", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("8.0.1-rc0")
            .setCreateDate(oldDate)
            .setLastDbCheckDate(oldDate)
            .setLastDataValidationDate(new Date())
            .build();
    _clusterDescriptionDao.save(oldDbChecked);
    assertEquals(
        new CorruptionDetectionEligibility(2, 1, 0),
        _clusterDescriptionDao.countCorruptionDetectionEligibleClusters());

    // data validation ELIGIBLE
    final ClusterDescription oldDataValidated =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "oldDataValidated", CloudProvider.AWS)
            .copy()
            .setCreateDate(oldDate)
            .setMongoDBVersion("8.1.0")
            .setLastDbCheckDate(new Date())
            .setLastDataValidationDate(oldDate)
            .build();
    _clusterDescriptionDao.save(oldDataValidated);
    assertEquals(
        new CorruptionDetectionEligibility(2, 2, 0),
        _clusterDescriptionDao.countCorruptionDetectionEligibleClusters());

    // checkMetadataConsistency ELIGIBLE
    final ShardedClusterDescription shardedCluster =
        NDSModelTestFactory.getShardedClusterDescription(group.getGroupId(), CloudProvider.AWS)
            .copy()
            .setName("foo")
            .setCreateDate(oldDate)
            .setMongoDBVersion("8.0.0")
            .setLastDbCheckDate(new Date())
            .setLastDataValidationDate(new Date())
            .setCheckMetadataConsistency(
                new CheckMetadataConsistency(
                    oldDate, false, false, null, CorruptionDetectionOperationOrigin.MANUAL, 0))
            .build();
    _clusterDescriptionDao.save(shardedCluster);
    assertEquals(
        new CorruptionDetectionEligibility(2, 2, 1),
        _clusterDescriptionDao.countCorruptionDetectionEligibleClusters());

    // checkMetadataConsistency INELIGIBLE
    // Current date is after needsCheckAfter date, but minDays since last check has not been met
    final ShardedClusterDescription ineligibleShardedCluster =
        NDSModelTestFactory.getShardedClusterDescription(group.getGroupId(), CloudProvider.AWS)
            .copy()
            .setName("bar")
            .setCreateDate(oldDate)
            .setMongoDBVersion("8.0.0")
            .setLastDbCheckDate(new Date())
            .setLastDataValidationDate(new Date())
            .setCheckMetadataConsistency(
                new CheckMetadataConsistency(
                    oldDate,
                    false,
                    false,
                    new Date(),
                    CorruptionDetectionOperationOrigin.MANUAL,
                    0))
            .build();
    _clusterDescriptionDao.save(ineligibleShardedCluster);
    assertEquals(
        new CorruptionDetectionEligibility(2, 2, 1),
        _clusterDescriptionDao.countCorruptionDetectionEligibleClusters());

    // checkMetadataConsistency INELIGIBLE
    // MongoDB version is older than 7.0
    final ShardedClusterDescription ineligiblePre70 =
        NDSModelTestFactory.getShardedClusterDescription(group.getGroupId(), CloudProvider.AWS)
            .copy()
            .setName("bar")
            .setCreateDate(oldDate)
            .setMongoDBVersion("5.0.0")
            .setCheckMetadataConsistency(
                new CheckMetadataConsistency(
                    oldDate, false, false, oldDate, CorruptionDetectionOperationOrigin.MANUAL, 0))
            .build();
    _clusterDescriptionDao.save(ineligiblePre70);
    assertEquals(
        new CorruptionDetectionEligibility(3, 3, 1),
        _clusterDescriptionDao.countCorruptionDetectionEligibleClusters());

    // When targeting 8.0 only, we rule out the 8.1.0 cluster
    _appSettings.setProp(
        Fields.CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX.value, "^8.0", SettingType.MEMORY);
    assertEquals(
        new CorruptionDetectionEligibility(2, 1, 1),
        _clusterDescriptionDao.countCorruptionDetectionEligibleClusters());
  }

  @Test
  public void testSampleClustersForDbCheck() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final Date oldDate = new Date(0);
    _appSettings.clearMemory();
    _appSettings.setProp(
        Fields.CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX.value, "", SettingType.MEMORY);

    final ClusterDescription dbCheckEligible =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "dbCheckEligible", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("7.2.0")
            .setCreateDate(oldDate)
            .setLastDbCheckDate(oldDate)
            .build();
    _clusterDescriptionDao.save(dbCheckEligible);
    final ClusterDescription dbCheckEligible2 =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "dbCheckEligible2", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("8.0.1-rc0")
            .setCreateDate(oldDate)
            .build();
    _clusterDescriptionDao.save(dbCheckEligible2);
    final ClusterDescription recentlyDbChecked =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "recentlyDbChecked", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("8.0.1-rc0")
            .setCreateDate(oldDate)
            .setLastDbCheckDate(new Date())
            .build();
    _clusterDescriptionDao.save(recentlyDbChecked);
    final ClusterDescription alreadyScheduled =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "alreadyScheduled", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("8.0.1-rc0")
            .setCreateDate(oldDate)
            .setLastDbCheckDate(oldDate)
            .setNeedsDbCheckAfter(new Date())
            .build();
    _clusterDescriptionDao.save(alreadyScheduled);

    try (final Cursor sampleResult = _clusterDescriptionDao.sampleClustersForDbCheck(4)) {
      final Set<String> sampledClusterNames = new HashSet<>();
      sampleResult.forEachRemaining(
          (sample) -> {
            final BasicDBObject sampleDBObject = (BasicDBObject) sample;
            assertEquals(1, sampleDBObject.size());

            final BasicDBObject sampleId = (BasicDBObject) sampleDBObject.get(FieldDefs.ID);
            final ObjectId groupId = sampleId.getObjectId(FieldDefs.GROUP_ID);
            final String clusterName = sampleId.getString(FieldDefs.NAME);

            assertEquals(group.getGroupId(), groupId);
            sampledClusterNames.add(clusterName);
          });
      final Set<String> expectedSampledClusterNames =
          Set.of(dbCheckEligible.getName(), dbCheckEligible2.getName());
      assertEquals(expectedSampledClusterNames, sampledClusterNames);
    }

    // When targeting 8.0 clusters, we rule out the 7.2.0 cluster
    _appSettings.setProp(
        Fields.CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX.value, "^8.0", SettingType.MEMORY);
    try (final Cursor sampleResult = _clusterDescriptionDao.sampleClustersForDbCheck(4)) {
      final Set<String> sampledClusterNames = new HashSet<>();
      sampleResult.forEachRemaining(
          (sample) -> {
            final BasicDBObject sampleDBObject = (BasicDBObject) sample;
            assertEquals(1, sampleDBObject.size());

            final BasicDBObject sampleId = (BasicDBObject) sampleDBObject.get(FieldDefs.ID);
            final ObjectId groupId = sampleId.getObjectId(FieldDefs.GROUP_ID);
            final String clusterName = sampleId.getString(FieldDefs.NAME);

            assertEquals(group.getGroupId(), groupId);
            sampledClusterNames.add(clusterName);
          });
      final Set<String> expectedSampledClusterNames = Set.of(dbCheckEligible2.getName());
      assertEquals(expectedSampledClusterNames, sampledClusterNames);
    }
  }

  @Test
  public void testSampleClustersForDataValidation() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final Date oldDate = new Date(0);
    _appSettings.clearMemory();
    _appSettings.setProp(
        Fields.CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX.value, "", SettingType.MEMORY);

    final ClusterDescription dataValidationEligible =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "dataValidationEligible", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("7.2.0")
            .setCreateDate(oldDate)
            .setLastDataValidationDate(oldDate)
            .build();
    _clusterDescriptionDao.save(dataValidationEligible);
    final ClusterDescription dataValidationEligible2 =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "dataValidationEligible2", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("8.0.1-rc0")
            .setCreateDate(oldDate)
            .build();
    _clusterDescriptionDao.save(dataValidationEligible2);
    final ClusterDescription recentlyValidated =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "recentlyValidated", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("8.0.1-rc0")
            .setCreateDate(oldDate)
            .setLastDataValidationDate(new Date())
            .build();
    _clusterDescriptionDao.save(recentlyValidated);
    final ClusterDescription nvmeCluster =
        new ClusterDescription(
            NDSModelTestFactory.getAzureNVMeClusterDescription(group.getGroupId(), "nvmeCluster")
                .append(FieldDefs.CREATE_DATE, oldDate));
    _clusterDescriptionDao.save(nvmeCluster);
    final ClusterDescription earCluster =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescriptionWithEncryptionAtRestProvider(
                    group.getGroupId(), "earCluster", EncryptionAtRestProvider.AWS))
            .copy()
            .setMongoDBVersion("8.0.1-rc0")
            .setCreateDate(oldDate)
            .build();
    _clusterDescriptionDao.save(earCluster);

    try (final Cursor sampleResult = _clusterDescriptionDao.sampleClustersForDataValidation(5)) {
      final Set<String> sampledClusterNames = new HashSet<>();
      sampleResult.forEachRemaining(
          (sample) -> {
            final BasicDBObject sampleDBObject = (BasicDBObject) sample;
            assertEquals(1, sampleDBObject.size());

            final BasicDBObject sampleId = (BasicDBObject) sampleDBObject.get(FieldDefs.ID);
            final ObjectId groupId = sampleId.getObjectId(FieldDefs.GROUP_ID);
            final String clusterName = sampleId.getString(FieldDefs.NAME);

            assertEquals(group.getGroupId(), groupId);
            sampledClusterNames.add(clusterName);
          });
      final Set<String> expectedSampledClusterNames =
          Set.of(dataValidationEligible.getName(), dataValidationEligible2.getName());
      assertEquals(expectedSampledClusterNames, sampledClusterNames);
    }

    // When targeting 8.0 clusters, we rule out the 7.2.0 cluster
    _appSettings.setProp(
        Fields.CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX.value, "^8.0", SettingType.MEMORY);

    try (final Cursor sampleResult = _clusterDescriptionDao.sampleClustersForDataValidation(5)) {
      final Set<String> sampledClusterNames = new HashSet<>();
      sampleResult.forEachRemaining(
          (sample) -> {
            final BasicDBObject sampleDBObject = (BasicDBObject) sample;
            assertEquals(1, sampleDBObject.size());

            final BasicDBObject sampleId = (BasicDBObject) sampleDBObject.get(FieldDefs.ID);
            final ObjectId groupId = sampleId.getObjectId(FieldDefs.GROUP_ID);
            final String clusterName = sampleId.getString(FieldDefs.NAME);

            assertEquals(group.getGroupId(), groupId);
            sampledClusterNames.add(clusterName);
          });
      final Set<String> expectedSampledClusterNames = Set.of(dataValidationEligible2.getName());
      assertEquals(expectedSampledClusterNames, sampledClusterNames);
    }
  }

  @Test
  public void testSampleClustersForCheckMetadataConsistency() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final Date oldDate = new Date(0);
    _appSettings.clearMemory();

    final ClusterDescription checkMetadataConsistencyEligible =
        new ShardedClusterDescription(
                NDSModelTestFactory.getAWSShardedClusterDescription(
                    group.getGroupId(), "checkMetadataConsistencyEligible"))
            .copy()
            .setMongoDBVersion("7.2.0")
            .setCreateDate(oldDate)
            .setCheckMetadataConsistency(
                new CheckMetadataConsistency(
                    null, false, false, oldDate, CorruptionDetectionOperationOrigin.AUTOMATED, 0))
            .build();
    _clusterDescriptionDao.save(checkMetadataConsistencyEligible);
    final ClusterDescription checkMetadataConsistencyEligible2 =
        new ShardedClusterDescription(
                NDSModelTestFactory.getAWSShardedClusterDescription(
                    group.getGroupId(), "checkMetadataConsistencyEligible2"))
            .copy()
            .setMongoDBVersion("8.0.1")
            .setCreateDate(oldDate)
            .build();
    _clusterDescriptionDao.save(checkMetadataConsistencyEligible2);
    final ClusterDescription recentlyCheckedMetadataConsistency =
        new ShardedClusterDescription(
                NDSModelTestFactory.getAWSShardedClusterDescription(
                    group.getGroupId(), "recentlyCheckedMetadataConsistency"))
            .copy()
            .setMongoDBVersion("8.0.1")
            .setCreateDate(oldDate)
            .setCheckMetadataConsistency(
                new CheckMetadataConsistency(
                    null,
                    false,
                    false,
                    new Date(),
                    CorruptionDetectionOperationOrigin.AUTOMATED,
                    0))
            .build();
    _clusterDescriptionDao.save(recentlyCheckedMetadataConsistency);
    final ClusterDescription alreadyScheduled =
        new ShardedClusterDescription(
                NDSModelTestFactory.getAWSShardedClusterDescription(
                    group.getGroupId(), "alreadyScheduled"))
            .copy()
            .setMongoDBVersion("8.0.1")
            .setCreateDate(oldDate)
            .setCheckMetadataConsistency(
                new CheckMetadataConsistency(
                    new Date(),
                    false,
                    false,
                    oldDate,
                    CorruptionDetectionOperationOrigin.AUTOMATED,
                    0))
            .build();
    _clusterDescriptionDao.save(alreadyScheduled);
    final ClusterDescription oldVersion =
        new ShardedClusterDescription(
                NDSModelTestFactory.getAWSShardedClusterDescription(
                    group.getGroupId(), "checkMetadataConsistencyOldVersion"))
            .copy()
            .setMongoDBVersion("5.0.1")
            .setCreateDate(oldDate)
            .build();
    _clusterDescriptionDao.save(oldVersion);

    try (final Cursor sampleResult =
        _clusterDescriptionDao.sampleClustersForCheckMetadataConsistency(10)) {
      final Set<String> sampledClusterNames = new HashSet<>();
      sampleResult.forEachRemaining(
          (sample) -> {
            final BasicDBObject sampleDBObject = (BasicDBObject) sample;
            assertEquals(1, sampleDBObject.size());

            final BasicDBObject sampleId = (BasicDBObject) sampleDBObject.get(FieldDefs.ID);
            final ObjectId groupId = sampleId.getObjectId(FieldDefs.GROUP_ID);
            final String clusterName = sampleId.getString(FieldDefs.NAME);

            assertEquals(group.getGroupId(), groupId);
            sampledClusterNames.add(clusterName);
          });
      final Set<String> expectedSampledClusterNames =
          Set.of(
              checkMetadataConsistencyEligible.getName(),
              checkMetadataConsistencyEligible2.getName());
      assertEquals(expectedSampledClusterNames, sampledClusterNames);
    }

    // With a specific version targeting, we ignore the checkMetadataConsistency min version.
    _appSettings.setProp(
        Fields.CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX.value, "^5.0", SettingType.MEMORY);
    try (final Cursor sampleResult =
        _clusterDescriptionDao.sampleClustersForCheckMetadataConsistency(10)) {
      final Set<String> sampledClusterNames = new HashSet<>();
      sampleResult.forEachRemaining(
          (sample) -> {
            final BasicDBObject sampleDBObject = (BasicDBObject) sample;
            assertEquals(1, sampleDBObject.size());

            final BasicDBObject sampleId = (BasicDBObject) sampleDBObject.get(FieldDefs.ID);
            final ObjectId groupId = sampleId.getObjectId(FieldDefs.GROUP_ID);
            final String clusterName = sampleId.getString(FieldDefs.NAME);

            assertEquals(group.getGroupId(), groupId);
            sampledClusterNames.add(clusterName);
          });
      final Set<String> expectedSampledClusterNames = Set.of(oldVersion.getName());
      assertEquals(expectedSampledClusterNames, sampledClusterNames);
    }
  }

  @Test
  public void testSampleClustersForCorruptionDetection_zeroSampleSize() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final Date oldDate = new Date(0);
    _appSettings.clearMemory();
    _appSettings.setProp(
        Fields.CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX.value, "", SettingType.MEMORY);

    // Create an eligible cluster for data validation
    final ClusterDescription eligible =
        NDSModelTestFactory.getClusterDescription(group.getGroupId(), "eligible", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("8.0.0")
            .setCreateDate(oldDate)
            .setLastDataValidationDate(oldDate)
            .build();
    _clusterDescriptionDao.save(eligible);

    // Test that zero sample size returns empty cursor without MongoDB error
    try (final Cursor sampleResult = _clusterDescriptionDao.sampleClustersForDataValidation(0)) {
      assertFalse(sampleResult.hasNext(), "Expected empty cursor for zero sample size");
    }

    // Test that negative sample size returns empty cursor without MongoDB error
    try (final Cursor sampleResult = _clusterDescriptionDao.sampleClustersForDataValidation(-1)) {
      assertFalse(sampleResult.hasNext(), "Expected empty cursor for negative sample size");
    }

    // Test that positive sample size works normally
    try (final Cursor sampleResult = _clusterDescriptionDao.sampleClustersForDataValidation(1)) {
      assertTrue(sampleResult.hasNext(), "Expected non-empty cursor for positive sample size");
      final BasicDBObject sampleDBObject = (BasicDBObject) sampleResult.next();
      assertEquals(1, sampleDBObject.size());

      final BasicDBObject sampleId = (BasicDBObject) sampleDBObject.get(FieldDefs.ID);
      final ObjectId groupId = sampleId.getObjectId(FieldDefs.GROUP_ID);
      final String clusterName = sampleId.getString(FieldDefs.NAME);

      assertEquals(group.getGroupId(), groupId);
      assertEquals(eligible.getName(), clusterName);
    }
  }

  @Test
  public void testCountByGroup() {
    final ObjectId groupId1 = new ObjectId();
    final ObjectId groupId2 = new ObjectId();

    // Add a cluster with each state to group 1
    for (State state : State.values()) {
      final ClusterDescription cd =
          new ClusterDescription(
                  NDSModelTestFactory.getAWSClusterDescription(groupId1, "Cluster" + state))
              .copy()
              .setState(state)
              .build();
      _clusterDescriptionDao.save(cd);
    }

    // Add one deleted cluster to group 2
    final ClusterDescription cd =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId2, "Cluster0"))
            .copy()
            .setState(State.DELETED)
            .build();
    _clusterDescriptionDao.save(cd);

    assertEquals(State.values().length, _clusterDescriptionDao.countByGroup(groupId1, false));
    assertEquals(State.values().length - 1, _clusterDescriptionDao.countByGroup(groupId1, true));

    assertEquals(1, _clusterDescriptionDao.countByGroup(groupId2, false));
    assertEquals(0, _clusterDescriptionDao.countByGroup(groupId2, true));
  }

  @Test
  public void test_updateMTMClustersMongoDBMajorVersion() {
    final ObjectId groupId = new ObjectId();
    final VersionUtils.Version newMajorVersion = VersionUtils.parse("7.0");
    final VersionUtils.Version newFullVersion = VersionUtils.parse("7.0.5");

    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(
                groupId, "dataValidationEligible", CloudProvider.AWS)
            .copy()
            .setCreateDate(new Date(0))
            .setLastDataValidationDate(new Date(0))
            .setIsMTM(true)
            .build();
    _clusterDescriptionDao.save(clusterDescription);

    final VersionUtils.Version originalMajorVersion =
        VersionUtils.parse(clusterDescription.getMongoDBMajorVersion());

    // perform version update
    final Date publishDate = _ndsGroupDao.getPlanASAPDate();
    _clusterDescriptionDao.updateMTMClustersMongoDBMajorVersion(
        groupId, originalMajorVersion, newMajorVersion, newFullVersion, publishDate);

    // assert db update
    final List<ClusterDescription> savedClusterDescriptions =
        _clusterDescriptionDao.findByGroup(groupId);
    assertEquals(
        newMajorVersion.getVersion(), savedClusterDescriptions.get(0).getMongoDBMajorVersion());
    assertEquals(newFullVersion, savedClusterDescriptions.get(0).getMongoDBVersion());
    assertEquals(
        publishDate,
        savedClusterDescriptions.get(0).getNeedsMongoDBConfigPublishAfter().orElse(null));
    assertEquals(
        ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.toString(),
        savedClusterDescriptions.get(0).getNeedsMongoDBConfigPublishRestartAllowed().toString());
  }

  @Test
  public void testSetInstanceFamilyForCluster_updateAllFamilies() {
    final String clusterName = "cluster0";
    final ObjectId groupId = new ObjectId();
    final ClusterDescription description =
        new ClusterDescription(
            NDSModelTestFactory.getMultiRegionAWSClusterDescription(groupId, clusterName));
    _clusterDescriptionDao.save(description);

    final InstanceFamily initialInstanceFamily = AWSInstanceFamily.I3;
    final InstanceFamily newInstanceFamily = AWSInstanceFamily.I3_METAL;
    setClusterInstanceFamilyTo(description, Map.of(AWS, initialInstanceFamily));

    final ClusterDescription instanceSizeFixedDescription =
        _clusterDescriptionDao
            .findByName(description.getGroupId(), description.getName())
            .orElseThrow();
    assertClusterInstanceFamiliesEqual(instanceSizeFixedDescription, initialInstanceFamily);

    final WriteResult writeResult =
        _clusterDescriptionDao.setInstanceFamilyForCluster(
            groupId,
            clusterName,
            false,
            AWS,
            List.of(
                RegionConfig.FieldDefs.ELECTABLE_SPECS,
                ShardRegionConfig.FieldDefs.ANALYTICS_SPECS,
                ShardRegionConfig.FieldDefs.READ_ONLY_SPECS,
                ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS),
            initialInstanceFamily,
            newInstanceFamily);

    assertEquals(1, writeResult.getN());
    final ClusterDescription updatedDescription =
        _clusterDescriptionDao.findByGroup(groupId).get(0);
    assertClusterInstanceFamiliesEqual(updatedDescription, newInstanceFamily);
  }

  @Test
  public void testSetInstanceFamilyForCluster_updateAllFamilies_sharded() {
    final String clusterName = "cluster0";
    final ObjectId groupId = new ObjectId();
    final List<RegionConfig> regionConfigs =
        NDSModelTestFactory.getRegionConfigs(
            3, List.of(AWSRegionName.AF_SOUTH_1, AWSRegionName.US_EAST_1));
    final ClusterDescription description =
        new ShardedClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(
                groupId, clusterName, List.of(), 2, regionConfigs));
    _clusterDescriptionDao.save(description);

    final InstanceFamily initialInstanceFamily = AWSInstanceFamily.I3;
    final InstanceFamily newInstanceFamily = AWSInstanceFamily.I3_METAL;
    setClusterInstanceFamilyTo(description, Map.of(AWS, initialInstanceFamily));

    final ClusterDescription instanceSizeFixedDescription =
        _clusterDescriptionDao
            .findByName(description.getGroupId(), description.getName())
            .orElseThrow();
    assertClusterInstanceFamiliesEqual(instanceSizeFixedDescription, initialInstanceFamily);

    final WriteResult writeResult =
        _clusterDescriptionDao.setInstanceFamilyForCluster(
            groupId,
            clusterName,
            true,
            AWS,
            List.of(
                RegionConfig.FieldDefs.ELECTABLE_SPECS,
                ShardRegionConfig.FieldDefs.ANALYTICS_SPECS,
                ShardRegionConfig.FieldDefs.READ_ONLY_SPECS,
                ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS),
            initialInstanceFamily,
            newInstanceFamily);

    assertEquals(1, writeResult.getN());
    final ClusterDescription updatedDescription =
        _clusterDescriptionDao.findByGroup(groupId).get(0);
    assertClusterInstanceFamiliesEqual(updatedDescription, newInstanceFamily);
  }

  @Test
  public void testSetInstanceFamilyForCluster_updateOnlyAnalyticsNodes() {
    final String clusterName = "cluster0";
    final ObjectId groupId = new ObjectId();
    final List<RegionConfig> regionConfigs =
        NDSModelTestFactory.getRegionConfigs(
            3, List.of(AWSRegionName.AF_SOUTH_1, AWSRegionName.US_EAST_1));
    final ClusterDescription description =
        new ShardedClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(
                groupId, clusterName, List.of(), 2, regionConfigs));
    _clusterDescriptionDao.save(description);

    final InstanceFamily initialInstanceFamily = AWSInstanceFamily.I3;
    final InstanceFamily newInstanceFamily = AWSInstanceFamily.I3_METAL;
    setClusterInstanceFamilyTo(description, Map.of(AWS, initialInstanceFamily));

    final ClusterDescription instanceSizeFixedDescription =
        _clusterDescriptionDao
            .findByName(description.getGroupId(), description.getName())
            .orElseThrow();
    assertClusterInstanceFamiliesEqual(instanceSizeFixedDescription, initialInstanceFamily);

    final WriteResult writeResult =
        _clusterDescriptionDao.setInstanceFamilyForCluster(
            groupId,
            clusterName,
            true,
            AWS,
            Collections.singletonList(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS),
            initialInstanceFamily,
            newInstanceFamily);

    assertEquals(1, writeResult.getN());
    final ClusterDescription updatedDescription =
        _clusterDescriptionDao.findByGroup(groupId).get(0);
    updatedDescription.getReplicationSpecsWithShardData().stream()
        .flatMap(rs -> rs.getRegionConfigs().stream())
        .forEach(
            rc ->
                Arrays.stream(NodeType.values())
                    .forEach(
                        nodeType -> {
                          final InstanceFamily expectedInstanceFamily =
                              nodeType == NodeType.ANALYTICS
                                  ? newInstanceFamily
                                  : initialInstanceFamily;
                          assertEquals(
                              expectedInstanceFamily,
                              rc.getHardwareSpecByNodeType(nodeType).getInstanceFamily(),
                              String.format(
                                  "Instance families were not %s as expected for node type" + " %s",
                                  expectedInstanceFamily, nodeType));
                        }));
  }

  @Test
  public void testSetInstanceFamilyForCluster_updateAllFamilies_sharded_embeddedConfig() {
    final ClusterDescription description =
        NDSModelTestFactory.getAWSShardedClusterDescriptionWithEmbeddedConfig();
    final ObjectId groupId = description.getGroupId();
    final String clusterName = description.getName();
    _clusterDescriptionDao.save(description);

    final InstanceFamily initialInstanceFamily = AWSInstanceFamily.I3;
    final InstanceFamily newInstanceFamily = AWSInstanceFamily.I3_METAL;
    setClusterInstanceFamilyTo(description, Map.of(AWS, initialInstanceFamily));

    final ClusterDescription instanceSizeFixedDescription =
        _clusterDescriptionDao
            .findByName(description.getGroupId(), description.getName())
            .orElseThrow();
    assertClusterInstanceFamiliesEqual(instanceSizeFixedDescription, initialInstanceFamily);

    final WriteResult writeResult =
        _clusterDescriptionDao.setInstanceFamilyForCluster(
            groupId,
            clusterName,
            false,
            AWS,
            List.of(
                RegionConfig.FieldDefs.ELECTABLE_SPECS,
                ShardRegionConfig.FieldDefs.ANALYTICS_SPECS,
                ShardRegionConfig.FieldDefs.READ_ONLY_SPECS,
                ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS),
            initialInstanceFamily,
            newInstanceFamily);

    assertEquals(1, writeResult.getN());
    final ClusterDescription updatedDescription =
        _clusterDescriptionDao.findByGroup(groupId).get(0);
    assertClusterInstanceFamiliesEqual(updatedDescription, newInstanceFamily);
  }

  @Test
  public void testSetInstanceFamilyForCluster_updateForEachProvider() {
    final String clusterName = "cluster0";
    final ObjectId groupId = new ObjectId();
    final List<RegionConfig> regionConfigs =
        NDSModelTestFactory.getRegionConfigs(
            3, List.of(AWSRegionName.AF_SOUTH_1, AzureRegionName.BRAZIL_SOUTHEAST));
    final ClusterDescription description =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
            groupId, clusterName, regionConfigs, false);
    _clusterDescriptionDao.save(description);

    final InstanceFamily initialAWSInstanceFamily = AWSInstanceFamily.I3;
    final InstanceFamily initialAzureInstanceFamily = AzureInstanceFamily.STANDARD_B;
    final InstanceFamily newAWSInstanceFamily = AWSInstanceFamily.I3_METAL;
    final InstanceFamily newAzureInstanceFamily = AzureInstanceFamily.STANDARD_DSV3;
    setClusterInstanceFamilyTo(
        description, Map.of(AWS, initialAWSInstanceFamily, AZURE, initialAzureInstanceFamily));

    final ClusterDescription instanceSizeFixedDescription =
        _clusterDescriptionDao
            .findByName(description.getGroupId(), description.getName())
            .orElseThrow();
    assertClusterInstanceFamiliesEqualForProvider(
        instanceSizeFixedDescription, initialAWSInstanceFamily, AWS);
    assertClusterInstanceFamiliesEqualForProvider(
        instanceSizeFixedDescription, initialAzureInstanceFamily, AZURE);

    final WriteResult awsWriteResult =
        _clusterDescriptionDao.setInstanceFamilyForCluster(
            groupId,
            clusterName,
            true,
            AWS,
            List.of(
                RegionConfig.FieldDefs.ELECTABLE_SPECS,
                ShardRegionConfig.FieldDefs.ANALYTICS_SPECS,
                ShardRegionConfig.FieldDefs.READ_ONLY_SPECS,
                ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS),
            initialAWSInstanceFamily,
            newAWSInstanceFamily);

    assertEquals(1, awsWriteResult.getN());
    final ClusterDescription awsUpdatedDescription =
        _clusterDescriptionDao.findByGroup(groupId).get(0);
    assertClusterInstanceFamiliesEqualForProvider(awsUpdatedDescription, newAWSInstanceFamily, AWS);
    assertClusterInstanceFamiliesEqualForProvider(
        awsUpdatedDescription, initialAzureInstanceFamily, AZURE);

    final WriteResult azureWriteResult =
        _clusterDescriptionDao.setInstanceFamilyForCluster(
            groupId,
            clusterName,
            true,
            AZURE,
            List.of(
                RegionConfig.FieldDefs.ELECTABLE_SPECS,
                ShardRegionConfig.FieldDefs.ANALYTICS_SPECS,
                ShardRegionConfig.FieldDefs.READ_ONLY_SPECS,
                ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS),
            initialAzureInstanceFamily,
            newAzureInstanceFamily);

    assertEquals(1, azureWriteResult.getN());
    final ClusterDescription azureUpdatedDescription =
        _clusterDescriptionDao.findByGroup(groupId).get(0);
    assertClusterInstanceFamiliesEqualForProvider(
        azureUpdatedDescription, newAWSInstanceFamily, AWS);
    assertClusterInstanceFamiliesEqualForProvider(
        azureUpdatedDescription, newAzureInstanceFamily, AZURE);
  }

  @Test
  public void testBumperFileOverridesField() {
    final ObjectId groupId = new ObjectId();
    final BumperFileOverride bumperFileOverride1 = new BumperFileOverride("host1.mongodb.com", 2);
    final BumperFileOverride bumperFileOverride2 = new BumperFileOverride("host2.mongodb.com", 1);
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .setBumperFileOverrides(List.of(bumperFileOverride1, bumperFileOverride2))
            .build();
    assertEquals(2, clusterDescription.getBumperFileOverrides().size());

    _clusterDescriptionDao.save(clusterDescription);

    final List<BumperFileOverride> bumperFileOverrides =
        Iterables.getOnlyElement(_clusterDescriptionDao.findByGroup(groupId))
            .getBumperFileOverrides();
    assertEquals(2, bumperFileOverrides.size());
    Stream.of(bumperFileOverride1, bumperFileOverride2)
        .forEach(
            bumperFileOverride -> assertTrue(bumperFileOverrides.contains(bumperFileOverride)));
  }

  private void assertClusterInstanceFamiliesEqual(
      final ClusterDescription pClusterDescription, final InstanceFamily pInstanceFamily) {
    assertClusterInstanceFamiliesEqualForProvider(pClusterDescription, pInstanceFamily, null);
  }

  private void assertClusterInstanceFamiliesEqualForProvider(
      final ClusterDescription pClusterDescription,
      final InstanceFamily pInstanceFamily,
      final CloudProvider pProvider) {
    assertTrue(
        pClusterDescription.getAllReplicationSpecsIncludingConfig().stream()
            .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
            .filter(
                regionConfig ->
                    pProvider == null || regionConfig.getCloudProvider().equals(pProvider))
            .flatMap(regionConfig -> regionConfig.getHardwareSpecs().stream())
            .map(HardwareSpec::getInstanceFamily)
            .allMatch(pInstanceFamily::equals),
        String.format("Some instance families were not %s as expected", pInstanceFamily));
  }

  private void setClusterInstanceFamilyTo(
      final ClusterDescription pClusterDescription,
      final Map<CloudProvider, InstanceFamily> pInstanceFamilyMap) {
    final Set<InstanceFamily> existingFamilies =
        pClusterDescription.getAllReplicationSpecsIncludingConfig().stream()
            .map(ReplicationSpec::getRegionConfigs)
            .flatMap(List::stream)
            .map(RegionConfig::getHardwareSpecs)
            .flatMap(List::stream)
            .map(HardwareSpec::getInstanceFamily)
            .collect(Collectors.toSet());

    final boolean clusterHasDedicatedConfigServer =
        pClusterDescription.getClusterType().isSharded()
            && ((ShardedClusterDescription) pClusterDescription)
                .getConfigServerType()
                .isDedicated();

    existingFamilies.forEach(
        existingFamily ->
            pInstanceFamilyMap.forEach(
                (provider, family) ->
                    _clusterDescriptionDao.setInstanceFamilyForCluster(
                        pClusterDescription.getGroupId(),
                        pClusterDescription.getName(),
                        clusterHasDedicatedConfigServer,
                        provider,
                        List.of(
                            RegionConfig.FieldDefs.ELECTABLE_SPECS,
                            ShardRegionConfig.FieldDefs.ANALYTICS_SPECS,
                            ShardRegionConfig.FieldDefs.READ_ONLY_SPECS,
                            ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS),
                        existingFamily,
                        pInstanceFamilyMap.get(provider))));

    final ClusterDescription updatedDescription =
        _clusterDescriptionDao
            .findByName(pClusterDescription.getGroupId(), pClusterDescription.getName())
            .orElseThrow();

    pInstanceFamilyMap.forEach(
        (provider, family) ->
            assertClusterInstanceFamiliesEqualForProvider(updatedDescription, family, provider));
  }

  @Test
  public void testClearBumperFileOverride() {
    final ObjectId groupId = new ObjectId();
    final String hostToRemoveBumperFileOverride = "host1.mongodb.com";
    final BumperFileOverride bumperFileOverride1 =
        new BumperFileOverride(hostToRemoveBumperFileOverride, 2);
    final BumperFileOverride bumperFileOverride2 = new BumperFileOverride("host2.mongodb.com", 1);
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .setBumperFileOverrides(List.of(bumperFileOverride1, bumperFileOverride2))
            .build();

    _clusterDescriptionDao.save(clusterDescription);

    { // assert pre-condition that 2 bumper file overrides exist
      final List<BumperFileOverride> bumperFileOverrides =
          _clusterDescriptionDao
              .findByName(groupId, clusterDescription.getName())
              .orElseThrow()
              .getBumperFileOverrides();
      assertEquals(2, bumperFileOverrides.size());
      assertTrue(
          bumperFileOverrides.stream()
              .anyMatch(override -> override.hostname().equals(hostToRemoveBumperFileOverride)));
    }

    _clusterDescriptionDao.clearBumperFileOverride(
        groupId, clusterDescription.getName(), hostToRemoveBumperFileOverride);

    { // assert that bumper file override is removed
      final List<BumperFileOverride> bumperFileOverrides =
          Iterables.getOnlyElement(_clusterDescriptionDao.findByGroup(groupId))
              .getBumperFileOverrides();
      assertEquals(1, bumperFileOverrides.size());
      assertTrue(
          bumperFileOverrides.stream()
              .noneMatch(override -> override.hostname().equals(hostToRemoveBumperFileOverride)));
    }
  }

  @Test
  public void testSetBumperFileOverride_updateExistingOverride() {
    final ObjectId groupId = new ObjectId();
    final String hostToUpdateBumperFileOverride = "host1.mongodb.com";
    final BumperFileOverride bumperFileOverride1 =
        new BumperFileOverride(hostToUpdateBumperFileOverride, 2);
    final BumperFileOverride bumperFileOverride2 = new BumperFileOverride("host2.mongodb.com", 1);
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .setBumperFileOverrides(List.of(bumperFileOverride1, bumperFileOverride2))
            .build();

    _clusterDescriptionDao.save(clusterDescription);

    { // assert pre-conditions
      final List<BumperFileOverride> bumperFileOverrides =
          _clusterDescriptionDao
              .findByName(groupId, clusterDescription.getName())
              .orElseThrow()
              .getBumperFileOverrides();
      assertEquals(2, bumperFileOverrides.size());
      assertEquals(
          1,
          bumperFileOverrides.stream()
              .filter(override -> override.hostname().equals(hostToUpdateBumperFileOverride))
              .count());
      final BumperFileOverride existingBumperFileOverride = bumperFileOverrides.get(0);
      assertEquals(hostToUpdateBumperFileOverride, existingBumperFileOverride.hostname());
      assertEquals(2, existingBumperFileOverride.numberOfBumperFiles());
    }

    _clusterDescriptionDao.setBumperFileOverride(
        groupId, clusterDescription.getName(), hostToUpdateBumperFileOverride, 1);

    { // assert that bumper file override was updated
      final List<BumperFileOverride> bumperFileOverrides =
          _clusterDescriptionDao
              .findByName(groupId, clusterDescription.getName())
              .orElseThrow()
              .getBumperFileOverrides();
      assertEquals(2, bumperFileOverrides.size());
      assertEquals(
          1,
          bumperFileOverrides.stream()
              .filter(override -> override.hostname().equals(hostToUpdateBumperFileOverride))
              .count());
      final BumperFileOverride existingBumperFileOverride = bumperFileOverrides.get(0);
      assertEquals(hostToUpdateBumperFileOverride, existingBumperFileOverride.hostname());
      assertEquals(1, existingBumperFileOverride.numberOfBumperFiles());
    }
  }

  @Test
  public void testSetBumperFileOverride_addNewOverride() {
    final ObjectId groupId = new ObjectId();
    final String hostToCreateBumperFileOverride = "host1.mongodb.com";
    final BumperFileOverride existingBumperFileOverride =
        new BumperFileOverride("host2.mongodb.com", 1);
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .setBumperFileOverrides(List.of(existingBumperFileOverride))
            .build();

    _clusterDescriptionDao.save(clusterDescription);

    { // assert pre-conditions
      final List<BumperFileOverride> bumperFileOverrides =
          _clusterDescriptionDao
              .findByName(groupId, clusterDescription.getName())
              .orElseThrow()
              .getBumperFileOverrides();
      assertEquals(1, bumperFileOverrides.size());
      assertTrue(
          bumperFileOverrides.stream()
              .noneMatch(override -> override.hostname().equals(hostToCreateBumperFileOverride)));
    }

    _clusterDescriptionDao.setBumperFileOverride(
        groupId, clusterDescription.getName(), hostToCreateBumperFileOverride, 2);

    { // assert that bumper file override was updated
      final List<BumperFileOverride> bumperFileOverrides =
          _clusterDescriptionDao
              .findByName(groupId, clusterDescription.getName())
              .orElseThrow()
              .getBumperFileOverrides();
      assertEquals(2, bumperFileOverrides.size());
      assertEquals(
          1,
          bumperFileOverrides.stream()
              .filter(override -> override.hostname().equals(hostToCreateBumperFileOverride))
              .count());
      final BumperFileOverride newBumperFileOverride = bumperFileOverrides.get(1);
      assertEquals(hostToCreateBumperFileOverride, newBumperFileOverride.hostname());
      assertEquals(2, newBumperFileOverride.numberOfBumperFiles());
    }
  }

  @Test
  public void testSetAndRevokeEmployeeAccessGrant() {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());

    _clusterDescriptionDao.save(clusterDescription);

    final ClusterDescription initialFromDb =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertTrue(initialFromDb.getEmployeeAccessGrant().isEmpty());

    final Date expiration = new Date();
    final EmployeeAccessGrantType grantType = EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE;

    _clusterDescriptionDao.setEmployeeAccessGrant(
        clusterDescription.getGroupId(), clusterDescription.getName(), grantType, expiration);

    final ClusterDescription withAccessGrant =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertTrue(withAccessGrant.getEmployeeAccessGrant().isPresent());
    assertEquals(expiration, withAccessGrant.getEmployeeAccessGrant().get().getExpiration());
    assertEquals(grantType, withAccessGrant.getEmployeeAccessGrant().get().getGrantType());

    _clusterDescriptionDao.revokeEmployeeAccessGrant(
        clusterDescription.getGroupId(), clusterDescription.getName());

    final ClusterDescription withAccessGrantRemoved =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertTrue(withAccessGrantRemoved.getEmployeeAccessGrant().isEmpty());
  }

  @Test
  public void testSetRedactClientLogData() {
    // Saving and restoring works for a document without the new field.
    {
      final BasicDBObject dbObj = NDSModelTestFactory.getAWSClusterDescription(new ObjectId());
      dbObj.remove(FieldDefs.REDACT_CLIENT_LOG_DATA);
      final ClusterDescription cd = new ClusterDescription(dbObj);
      _clusterDescriptionDao.save(cd);

      final ClusterDescription readCD =
          _clusterDescriptionDao
              .findByUniqueId(cd.getGroupId(), cd.getUniqueId())
              .orElseThrow(IllegalStateException::new);
      assertTrue(readCD.getRedactClientLogData().isEmpty());
    }

    // Saving and restoring works for documents with the new field.
    {
      final BasicDBObject dbObj = NDSModelTestFactory.getAWSClusterDescription(new ObjectId());
      dbObj.append(FieldDefs.REDACT_CLIENT_LOG_DATA, true);
      final ClusterDescription cd = new ClusterDescription(dbObj);
      _clusterDescriptionDao.save(cd);

      final ClusterDescription readCD =
          _clusterDescriptionDao
              .findByUniqueId(cd.getGroupId(), cd.getUniqueId())
              .orElseThrow(IllegalStateException::new);
      assertTrue(readCD.getRedactClientLogData().isPresent());
      assertTrue(readCD.getRedactClientLogData().get());
    }
  }

  @Test
  public void testSetAutoScalingMode() throws StaleUpdateException {
    final ClusterDescription cd =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setAutoScalingMode(AutoScalingMode.CLUSTER)
            .build();
    _clusterDescriptionDao.save(cd);

    _clusterDescriptionDao.setAutoScalingMode(
        cd.getGroupId(), cd.getName(), AutoScalingMode.SHARD, cd.getLastUpdateDate());
    final ClusterDescription modifiedCd =
        _clusterDescriptionDao.findByName(cd.getGroupId(), cd.getName()).orElseThrow();
    assertEquals(AutoScalingMode.SHARD, modifiedCd.getAutoScalingMode());
  }

  @Test
  public void testSetShardingMetadataConsistencyCheck() {
    final ShardedClusterDescription cd =
        new ShardedClusterDescription(NDSModelTestFactory.getAWSShardedClusterDescription());
    _clusterDescriptionDao.save(cd);

    {
      assertTrue(
          _clusterDescriptionDao.setShardingMetadataConsistencyCheck(
              cd.getGroupId(),
              cd.getName(),
              new Date(1776),
              true,
              true,
              CorruptionDetectionOperationOrigin.SHARD_DRAINING));

      final ShardedClusterDescription modifiedCd =
          _clusterDescriptionDao
              .findByName(cd.getGroupId(), cd.getName())
              .map(ShardedClusterDescription.class::cast)
              .orElseThrow();
      assertEquals(new Date(1776), modifiedCd.getCheckMetadataConsistency().needsCheckAfter());
      assertTrue(modifiedCd.getCheckMetadataConsistency().bypassMaintenanceWindow());
      assertTrue(modifiedCd.getCheckMetadataConsistency().skipPreFlightChecks());
      assertEquals(
          CorruptionDetectionOperationOrigin.SHARD_DRAINING,
          modifiedCd.getCheckMetadataConsistency().operationOrigin());
      assertNull(modifiedCd.getCheckMetadataConsistency().lastCheckDate());
    }

    {
      assertTrue(
          _clusterDescriptionDao.setShardingMetadataConsistencyCheck(
              cd.getGroupId(),
              cd.getName(),
              new Date(2000),
              false,
              false,
              CorruptionDetectionOperationOrigin.VERSION_UPGRADE));

      final ShardedClusterDescription modifiedCd =
          _clusterDescriptionDao
              .findByName(cd.getGroupId(), cd.getName())
              .map(ShardedClusterDescription.class::cast)
              .orElseThrow();
      assertEquals(new Date(2000), modifiedCd.getCheckMetadataConsistency().needsCheckAfter());
      assertFalse(modifiedCd.getCheckMetadataConsistency().bypassMaintenanceWindow());
      assertFalse(modifiedCd.getCheckMetadataConsistency().skipPreFlightChecks());
      assertEquals(
          CorruptionDetectionOperationOrigin.VERSION_UPGRADE,
          modifiedCd.getCheckMetadataConsistency().operationOrigin());
      assertNull(modifiedCd.getCheckMetadataConsistency().lastCheckDate());
    }
  }

  @Test
  public void testUnsetShardingMetadataConsistencyCheck() {
    assertFalse(
        _clusterDescriptionDao.unsetShardingMetadataConsistencyCheck(ObjectId.get(), "foo"));

    final ShardedClusterDescription.Builder builder =
        new ShardedClusterDescription.Builder(NDSModelTestFactory.getAWSShardedClusterDescription())
            .setCheckMetadataConsistency(
                new CheckMetadataConsistency(
                    new Date(1776),
                    true,
                    true,
                    null,
                    CorruptionDetectionOperationOrigin.SHARD_DRAINING,
                    0));
    final ShardedClusterDescription cd = builder.build();
    _clusterDescriptionDao.save(cd);

    assertTrue(
        _clusterDescriptionDao.unsetShardingMetadataConsistencyCheck(
            cd.getGroupId(), cd.getName()));

    final ShardedClusterDescription modifiedCd =
        _clusterDescriptionDao
            .findByName(cd.getGroupId(), cd.getName())
            .map(ShardedClusterDescription.class::cast)
            .orElseThrow();
    assertNull(modifiedCd.getCheckMetadataConsistency().needsCheckAfter());
    assertFalse(modifiedCd.getCheckMetadataConsistency().bypassMaintenanceWindow());
    assertFalse(modifiedCd.getCheckMetadataConsistency().skipPreFlightChecks());
    assertNull(modifiedCd.getCheckMetadataConsistency().operationOrigin());
    assertNotNull(modifiedCd.getCheckMetadataConsistency().lastCheckDate());

    assertFalse(
        _clusterDescriptionDao.unsetShardingMetadataConsistencyCheck(
            cd.getGroupId(), cd.getName()));

    assertNull(modifiedCd.getCheckMetadataConsistency().needsCheckAfter());
    assertFalse(modifiedCd.getCheckMetadataConsistency().bypassMaintenanceWindow());
    assertFalse(modifiedCd.getCheckMetadataConsistency().skipPreFlightChecks());
    assertNull(modifiedCd.getCheckMetadataConsistency().operationOrigin());
    assertNotNull(modifiedCd.getCheckMetadataConsistency().lastCheckDate());
  }

  @Test
  public void testSetCpuSocketBinding() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .build();
    _clusterDescriptionDao.save(clusterDescription);

    validateClusterDescriptionSocketBindingChange(clusterDescription, List.of(0, 1));
    validateClusterDescriptionSocketBindingChange(clusterDescription, List.of());
    validateClusterDescriptionSocketBindingChange(clusterDescription, List.of(4, 5, 6, 7));
    validateClusterDescriptionSocketBindingChange(clusterDescription, null);
  }

  private void validateClusterDescriptionSocketBindingChange(
      final ClusterDescription pClusterDescription, final List<Integer> pCpuSocketBinding) {
    _clusterDescriptionDao.setCpuSocketBinding(
        pClusterDescription.getGroupId(), pClusterDescription.getName(), pCpuSocketBinding);
    final ClusterDescription cdFromDb =
        _clusterDescriptionDao
            .findByName(pClusterDescription.getGroupId(), pClusterDescription.getName())
            .orElseThrow();
    if (pCpuSocketBinding == null || pCpuSocketBinding.isEmpty()) {
      assertTrue(cdFromDb.getCpuSocketBinding().isEmpty());
    } else {
      assertEquals(pCpuSocketBinding, cdFromDb.getCpuSocketBinding());
    }
    assertTrue(cdFromDb.getNeedsMongoDBConfigPublishAfter().get().before(new Date()));
    assertEquals(
        ClusterDescription.ProcessRestartAllowedState.IMMEDIATE,
        cdFromDb.getNeedsMongoDBConfigPublishRestartAllowed());
  }

  @Test
  public void testSetReserveIpamIpRequested() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .build();
    _clusterDescriptionDao.save(clusterDescription);

    final ClusterDescription initialCd =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertFalse(initialCd.getReserveIpamIpRequested());

    _clusterDescriptionDao.setReserveIpamIpRequested(
        clusterDescription.getGroupId(), clusterDescription.getName(), true);
    final ClusterDescription cdAfterReserveTrue =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertTrue(cdAfterReserveTrue.getReserveIpamIpRequested());

    _clusterDescriptionDao.setReserveIpamIpRequested(
        clusterDescription.getGroupId(), clusterDescription.getName(), false);
    final ClusterDescription cdAfterReserveFalse =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertFalse(cdAfterReserveFalse.getReserveIpamIpRequested());
  }

  @Test
  public void testUnsetReserveIpamIpRequested() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .build();
    _clusterDescriptionDao.save(clusterDescription);

    _clusterDescriptionDao.setReserveIpamIpRequested(
        clusterDescription.getGroupId(), clusterDescription.getName(), true);
    final ClusterDescription cdAfterReserveTrue =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertTrue(cdAfterReserveTrue.getReserveIpamIpRequested());

    _clusterDescriptionDao.unsetReserveIpamIpRequested(
        clusterDescription.getGroupId(), clusterDescription.getName());
    final ClusterDescription cdAfterUnset =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertFalse(cdAfterUnset.getReserveIpamIpRequested());

    final var cdDoc =
        _clusterDescriptionDao
            .getDbCollection()
            .findOne(
                new BasicDBObject(
                    FieldDefs.ID,
                    ClusterDescription.getId(
                        clusterDescription.getGroupId(), clusterDescription.getName())));
    assertNotNull(cdDoc);
    assertFalse(cdDoc.containsField(FieldDefs.RESERVE_IPAM_IP_REQUESTED));
  }

  @Test
  public void testSetSwapIpMaintenanceRoundCompleted() {
    final ObjectId groupId = new ObjectId();
    ClusterDescription cd =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId));
    _clusterDescriptionDao.save(cd);

    cd = _clusterDescriptionDao.findByName(cd.getGroupId(), cd.getName()).orElseThrow();
    assertFalse(cd.getSwapIpMaintenanceRoundCompleted().isPresent());

    _clusterDescriptionDao.setSwapIpMaintenanceRoundCompleted(
        cd.getGroupId(), cd.getName(), Optional.of(SwapIpMaintenanceRound.FIRST));
    cd = _clusterDescriptionDao.findByName(cd.getGroupId(), cd.getName()).orElseThrow();
    assertEquals(
        Optional.of(SwapIpMaintenanceRound.FIRST), cd.getSwapIpMaintenanceRoundCompleted());

    _clusterDescriptionDao.setSwapIpMaintenanceRoundCompleted(
        cd.getGroupId(), cd.getName(), Optional.empty());
    cd = _clusterDescriptionDao.findByName(cd.getGroupId(), cd.getName()).orElseThrow();
    assertFalse(cd.getSwapIpMaintenanceRoundCompleted().isPresent());
  }

  @Test
  public void testSetClusterConnectionStringConfiguration() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .build();
    _clusterDescriptionDao.save(clusterDescription);

    final ClusterConnectionStringConfiguration initialConfiguration =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow()
            .getClusterConnectionStringConfiguration();
    assertEquals(Status.DEFAULT, initialConfiguration.regionalizedConnectionStringStatus());
    assertEquals(Status.DEFAULT, initialConfiguration.nonRegionalizedConnectionStringStatus());
    assertEquals(Status.DEFAULT, initialConfiguration.legacyConnectionStringStatus());
    assertEquals(Status.DEFAULT, initialConfiguration.loadBalancedConnectionStringStatus());

    final ClusterConnectionStringConfiguration newConfiguration =
        new ClusterConnectionStringConfiguration(
            Status.DISABLED, Status.ENABLED, Status.DISABLED, Status.ENABLED);
    _clusterDescriptionDao.setClusterConnectionStringConfiguration(
        clusterDescription.getGroupId(), clusterDescription.getName(), newConfiguration);

    final ClusterConnectionStringConfiguration updatedConfiguration =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow()
            .getClusterConnectionStringConfiguration();
    assertEquals(Status.DISABLED, updatedConfiguration.regionalizedConnectionStringStatus());
    assertEquals(Status.ENABLED, updatedConfiguration.nonRegionalizedConnectionStringStatus());
    assertEquals(Status.DISABLED, updatedConfiguration.legacyConnectionStringStatus());
    assertEquals(Status.ENABLED, updatedConfiguration.loadBalancedConnectionStringStatus());
  }

  @Test
  public void testSetFlexTenantMigrationState() {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getFreeClusterDescription());
    _clusterDescriptionDao.save(clusterDescription);

    assertTrue(
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow()
            .getFlexTenantMigrationState()
            .isEmpty());

    final FlexTenantMigrationState state =
        new FlexTenantMigrationState.Builder()
            .setFormerCloudProvider(CloudProvider.FREE)
            .setFormerInstanceSize(FreeInstanceSize.M2)
            .setIsTenantCreatedFromApi(false)
            .build();
    _clusterDescriptionDao.setFlexTenantMigrationState(
        clusterDescription.getGroupId(), clusterDescription.getName(), state);

    assertEquals(
        state,
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow()
            .getFlexTenantMigrationState()
            .orElseThrow());
  }

  @Test
  public void testSetClusterAllowUnsafeRollingOperation() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .build();
    _clusterDescriptionDao.save(clusterDescription);

    final boolean initialConfiguration =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow()
            .getAllowUnsafeRollingOperation();
    assertFalse(initialConfiguration);

    _clusterDescriptionDao.setAllowUnsafeRollingOperation(
        clusterDescription.getGroupId(), clusterDescription.getName(), true);

    final boolean updatedConfiguration =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow()
            .getAllowUnsafeRollingOperation();
    assertTrue(updatedConfiguration);
  }

  @Test
  public void testSetCreatedDate() {
    final Date updatedCreatedDate = new GregorianCalendar(2025, Calendar.JANUARY, 1).getTime();

    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription);

    _clusterDescriptionDao.setCreatedDate(
        clusterDescription.getGroupId(), clusterDescription.getName(), updatedCreatedDate);

    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertNotEquals(clusterDescription.getCreateDate(), updatedClusterDescription.getCreateDate());
    assertEquals(updatedCreatedDate, updatedClusterDescription.getCreateDate());
  }

  @Test
  public void testGetActiveClusterAttributesAggregation() {
    final ClusterDescription clusterDescription1 =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription1);

    final ClusterDescription clusterDescription2 =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription2);

    final ClusterDescription pausedClusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(pausedClusterDescription);
    _clusterDescriptionDao.setPauseRequest(
        pausedClusterDescription.getGroupId(), pausedClusterDescription.getName());

    final ClusterDescription mtmClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.IS_MTM, true));
    _clusterDescriptionDao.save(mtmClusterDescription);
    _clusterDescriptionDao.setPauseRequest(
        mtmClusterDescription.getGroupId(), pausedClusterDescription.getName());

    _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        VersionUtils.EIGHT_ZERO_FIVE);
    _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
        clusterDescription2.getGroupId(),
        clusterDescription2.getName(),
        VersionUtils.SEVEN_ZERO_SIXTEEN);

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        clusterDescription1.getGroupId(), clusterDescription1.getName(), "190", false);
    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        clusterDescription2.getGroupId(), clusterDescription2.getName(), "191", false);

    // do not set the monogotune status for cd1.
    // This could happen while we rolling out the mongotune binary
    //    _clusterDescriptionDao.updateMongotuneStatus(
    //        clusterDescription1.getGroupId(), clusterDescription1.getName(), "1.1.1", null);
    _clusterDescriptionDao.updateMongotuneStatus(
        clusterDescription2.getGroupId(),
        clusterDescription2.getName(),
        "1.2.1",
        MongotuneStatus.State.ENABLE.name(),
        null);

    final Map<String, List<FleetAttribute>> attributes =
        _clusterDescriptionDao.getActiveClusterAttributesAggregation();

    // Ensure all fields are present
    // note we do not add mongotune to this list to account for potential null mongotune value in
    // production
    final String MONGOTUNE_VERSION = "mongotuneVersion";
    final List<String> requiredFields =
        List.of(
            FieldDefs.MONGODB_VERSION,
            FieldDefs.MONGODB_MAJOR_VERSION,
            FieldDefs.VERSION_RELEASE_SYSTEM,
            FieldDefs.OS_POLICY_VERSION,
            FieldDefs.IS_MTM,
            FieldDefs.IS_PAUSED,
            FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
            MONGOTUNE_VERSION);
    for (String requiredField : requiredFields) {
      assertTrue(attributes.containsKey(requiredField));
    }

    // Confirm a few fields are formatted as expected
    assertEquals(2, attributes.get(FieldDefs.MONGODB_VERSION).size());
    assertTrue(
        attributes.get(FieldDefs.MONGODB_VERSION).stream()
            .anyMatch(
                a -> a.equals(new FleetAttribute(VersionUtils.EIGHT_ZERO_FIVE.toString(), 1))));
    assertTrue(
        attributes.get(FieldDefs.MONGODB_VERSION).stream()
            .anyMatch(
                a -> a.equals(new FleetAttribute(VersionUtils.SEVEN_ZERO_SIXTEEN.toString(), 1))));

    assertEquals(2, attributes.get(FieldDefs.OS_POLICY_VERSION).size());
    assertTrue(
        attributes.get(FieldDefs.OS_POLICY_VERSION).stream()
            .anyMatch(a -> a.equals(new FleetAttribute("190", 1))));
    assertTrue(
        attributes.get(FieldDefs.OS_POLICY_VERSION).stream()
            .anyMatch(a -> a.equals(new FleetAttribute("190", 1))));

    assertEquals(2, attributes.get(MONGOTUNE_VERSION).size());
    // cd1 doesn't have mongotune field set, we expect key being "null"
    assertTrue(
        attributes.get(MONGOTUNE_VERSION).stream()
            .anyMatch(a -> a.equals(new FleetAttribute("null", 1))));
    // cd2 has mongotune set, the key of FleetAttribute is now just the version string.
    assertTrue(
        attributes.get(MONGOTUNE_VERSION).stream()
            .anyMatch(a -> a.equals(new FleetAttribute("1.2.1", 1))));
  }

  @Test
  public void testGetPausedClusterAttributesAggregation() {
    final ClusterDescription clusterDescription1 =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription1);

    final ClusterDescription clusterDescription2 =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription2);

    final ClusterDescription nonPausedClusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(nonPausedClusterDescription);

    final BasicDBObject tenantClusterDescriptionDoc =
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig()
                .setGroupId(clusterDescription1.getGroupId())
                .setClusterName("x"));

    // Tenant clusters should be excluded
    final ClusterDescription tenantClusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(tenantClusterDescriptionDoc);
    _clusterDescriptionDao.save(tenantClusterDescription);

    _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
        tenantClusterDescription.getGroupId(),
        tenantClusterDescription.getName(),
        VersionUtils.EIGHT_ZERO_FIVE);

    _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        VersionUtils.EIGHT_ZERO_FIVE);
    _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
        clusterDescription2.getGroupId(),
        clusterDescription2.getName(),
        VersionUtils.SEVEN_ZERO_SIXTEEN);

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        clusterDescription1.getGroupId(), clusterDescription1.getName(), "190", false);
    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        clusterDescription2.getGroupId(), clusterDescription2.getName(), "191", false);

    _clusterDescriptionDao.setPauseRequest(
        clusterDescription1.getGroupId(), clusterDescription1.getName());
    _clusterDescriptionDao.setPauseRequest(
        clusterDescription2.getGroupId(), clusterDescription2.getName());

    final Map<String, List<FleetAttribute>> attributes =
        _clusterDescriptionDao.getPausedClusterAttributesAggregation();

    // Ensure all fields are present
    final List<String> requiredFields =
        List.of(
            FieldDefs.MONGODB_VERSION,
            FieldDefs.MONGODB_MAJOR_VERSION,
            FieldDefs.VERSION_RELEASE_SYSTEM,
            FieldDefs.OS_POLICY_VERSION,
            FieldDefs.IS_MTM,
            FieldDefs.IS_PAUSED,
            FieldDefs.ENCRYPTION_AT_REST_PROVIDER);
    for (String requiredField : requiredFields) {
      assertTrue(attributes.containsKey(requiredField));
    }

    // Confirm a few fields are formatted as expected
    assertEquals(2, attributes.get(FieldDefs.MONGODB_VERSION).size());
    assertTrue(
        attributes.get(FieldDefs.MONGODB_VERSION).stream()
            .anyMatch(
                a -> a.equals(new FleetAttribute(VersionUtils.EIGHT_ZERO_FIVE.toString(), 1))));
    assertTrue(
        attributes.get(FieldDefs.MONGODB_VERSION).stream()
            .anyMatch(
                a -> a.equals(new FleetAttribute(VersionUtils.SEVEN_ZERO_SIXTEEN.toString(), 1))));

    assertEquals(2, attributes.get(FieldDefs.OS_POLICY_VERSION).size());
    assertTrue(
        attributes.get(FieldDefs.OS_POLICY_VERSION).stream()
            .anyMatch(a -> a.equals(new FleetAttribute("190", 1))));
    assertTrue(
        attributes.get(FieldDefs.OS_POLICY_VERSION).stream()
            .anyMatch(a -> a.equals(new FleetAttribute("190", 1))));
  }

  @Test
  public void testGetMTMClusterAttributesAggregation() {
    final ClusterDescription clusterDescription1 =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.IS_MTM, true));
    _clusterDescriptionDao.save(clusterDescription1);

    final ClusterDescription clusterDescription2 =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription().append(FieldDefs.IS_MTM, true));
    _clusterDescriptionDao.save(clusterDescription2);

    final ClusterDescription nonMTMClusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(nonMTMClusterDescription);

    _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
        clusterDescription1.getGroupId(),
        clusterDescription1.getName(),
        VersionUtils.EIGHT_ZERO_FIVE);
    _clusterDescriptionDao.updateMongoDBMajorAndFullVersions(
        clusterDescription2.getGroupId(),
        clusterDescription2.getName(),
        VersionUtils.SEVEN_ZERO_SIXTEEN);

    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        clusterDescription1.getGroupId(), clusterDescription1.getName(), "190", false);
    _clusterDescriptionDao.setOSPolicyVersionAndIsCriticalOSPolicyRelease(
        clusterDescription2.getGroupId(), clusterDescription2.getName(), "191", false);

    _clusterDescriptionDao.setPauseRequest(
        clusterDescription1.getGroupId(), clusterDescription1.getName());
    _clusterDescriptionDao.setPauseRequest(
        clusterDescription2.getGroupId(), clusterDescription2.getName());

    final Map<String, List<FleetAttribute>> attributes =
        _clusterDescriptionDao.getMTMClusterAttributesAggregation();

    // Ensure all fields are present
    final List<String> requiredFields =
        List.of(
            FieldDefs.MONGODB_VERSION,
            FieldDefs.MONGODB_MAJOR_VERSION,
            FieldDefs.VERSION_RELEASE_SYSTEM,
            FieldDefs.OS_POLICY_VERSION,
            FieldDefs.IS_MTM,
            FieldDefs.IS_PAUSED,
            FieldDefs.ENCRYPTION_AT_REST_PROVIDER);
    for (String requiredField : requiredFields) {
      assertTrue(attributes.containsKey(requiredField));
    }

    // Confirm a few fields are formatted as expected
    assertEquals(2, attributes.get(FieldDefs.MONGODB_VERSION).size());
    assertTrue(
        attributes.get(FieldDefs.MONGODB_VERSION).stream()
            .anyMatch(
                a -> a.equals(new FleetAttribute(VersionUtils.EIGHT_ZERO_FIVE.toString(), 1))));
    assertTrue(
        attributes.get(FieldDefs.MONGODB_VERSION).stream()
            .anyMatch(
                a -> a.equals(new FleetAttribute(VersionUtils.SEVEN_ZERO_SIXTEEN.toString(), 1))));

    assertEquals(2, attributes.get(FieldDefs.OS_POLICY_VERSION).size());
    assertTrue(
        attributes.get(FieldDefs.OS_POLICY_VERSION).stream()
            .anyMatch(a -> a.equals(new FleetAttribute("190", 1))));
    assertTrue(
        attributes.get(FieldDefs.OS_POLICY_VERSION).stream()
            .anyMatch(a -> a.equals(new FleetAttribute("190", 1))));
  }

  @Test
  public void testSetIsEligibleForReducedFlexPricing() {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    _clusterDescriptionDao.save(clusterDescription);

    assertFalse(
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow()
            .isEligibleForReducedFlexPricing());

    _clusterDescriptionDao.setIsEligibleForReducedFlexPricing(
        clusterDescription.getGroupId(), clusterDescription.getName());

    assertTrue(
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow()
            .isEligibleForReducedFlexPricing());
  }

  @Test
  public void testSetDisaggregatedStorageConfig() {
    final ObjectId groupId = new ObjectId();

    final ClusterDescription regularCd =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .build();
    _clusterDescriptionDao.save(regularCd);

    final ClusterDescription clusterDescription =
        new ClusterDescription(
                NDSModelTestFactory.getAWSDisaggregatedStorageClusterDescription(groupId))
            .copy()
            .build();
    _clusterDescriptionDao.save(clusterDescription);

    DisaggregatedStorageConfig config =
        new DisaggregatedStorageConfig(
            100L,
            List.of(
                new DisaggregatedStorageConfig.SlsCell(
                    new ObjectId(),
                    "cell1",
                    "us1-az1",
                    "metadata.server",
                    "scheduler.server",
                    "vpc-1234",
                    "subnet-1234",
                    List.of("2600:1f18:7144:cb08::/64", "2600:1f18:7144:cb20::/64"))),
            List.of("logServer"),
            "encryption.key");

    assertEquals(
        0,
        _clusterDescriptionDao
            .setDisaggregatedStorageConfig(
                regularCd.getGroupId(),
                regularCd.getName(),
                regularCd.getReplicationSpecsWithShardData().get(0).getId(),
                config)
            .getN(),
        "should not change cluster not using disaggregated storage");

    assertEquals(
        0,
        _clusterDescriptionDao
            .setDisaggregatedStorageConfig(
                clusterDescription.getGroupId(),
                clusterDescription.getName(),
                new ObjectId(),
                config)
            .getN(),
        "should not change cluster with wrong replicationId");

    ObjectId replicationSpecId =
        clusterDescription.getReplicationSpecsWithShardData().get(0).getId();

    assertEquals(
        1,
        _clusterDescriptionDao
            .setDisaggregatedStorageConfig(
                clusterDescription.getGroupId(),
                clusterDescription.getName(),
                replicationSpecId,
                config)
            .getN());

    final ClusterDescription updatedClusterDescription =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    final ReplicationSpec replicationSpec =
        updatedClusterDescription.getReplicationSpecsWithShardData().stream()
            .filter(r -> r.getId().equals(replicationSpecId))
            .findFirst()
            .orElseThrow();
    assertTrue(replicationSpec.getDisaggregatedStorageConfig().isPresent());
    final DisaggregatedStorageConfig disaggregatedStorageConfig =
        replicationSpec.getDisaggregatedStorageConfig().get();
    assertEquals(100, disaggregatedStorageConfig.getSlsLogId());
    assertEquals("encryption.key", disaggregatedStorageConfig.getEncryptionKey());
    assertEquals(List.of("logServer"), disaggregatedStorageConfig.slsLogServers);
    assertEquals(
        List.of("cell1"),
        disaggregatedStorageConfig.slsCells.stream()
            .map(DisaggregatedStorageConfig.SlsCell::getName)
            .toList());
    assertEquals(
        List.of("us1-az1"),
        disaggregatedStorageConfig.slsCells.stream()
            .map(DisaggregatedStorageConfig.SlsCell::getAvailabilityZoneId)
            .toList());
    assertEquals(
        List.of("metadata.server"),
        disaggregatedStorageConfig.slsCells.stream()
            .map(DisaggregatedStorageConfig.SlsCell::getMetadataServer)
            .toList());
    assertEquals(
        List.of("vpc-1234"),
        disaggregatedStorageConfig.slsCells.stream()
            .map(DisaggregatedStorageConfig.SlsCell::getVpcId)
            .toList());
    assertEquals(
        List.of("subnet-1234"),
        disaggregatedStorageConfig.slsCells.stream()
            .map(DisaggregatedStorageConfig.SlsCell::getInterfacePoolSubnetId)
            .toList());
    assertEquals(
        List.of(List.of("2600:1f18:7144:cb08::/64", "2600:1f18:7144:cb20::/64")),
        disaggregatedStorageConfig.slsCells.stream()
            .map(DisaggregatedStorageConfig.SlsCell::getIpv6Cidrs)
            .toList());
  }

  @Test
  public void testSetProxyProtocolForPrivateLinkMode() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getGroupId()));
    _clusterDescriptionDao.save(clusterDescription);

    assertTrue(clusterDescription.getClusterType().isReplicaSet());
    testSetProxyProtocolForPrivateLinkMode(clusterDescription);
  }

  private void testSetProxyProtocolForPrivateLinkMode(final ClusterDescription pCluster) {
    assertProxyProtocolForPrivateLinkMode(pCluster, ProxyProtocolForPrivateLinkMode.STANDARD);

    _clusterDescriptionDao.setProxyProtocolForPrivateLinkMode(pCluster, "DISABLED");

    assertProxyProtocolForPrivateLinkMode(
        _clusterDescriptionDao.findByName(pCluster.getGroupId(), pCluster.getName()).orElseThrow(),
        ProxyProtocolForPrivateLinkMode.DISABLED);
  }

  private void assertProxyProtocolForPrivateLinkMode(
      final ClusterDescription pCluster, final ProxyProtocolForPrivateLinkMode pMode) {
    final ProxyProtocolForPrivateLinkMode proxyProtocolForPrivateLinkMode =
        pCluster.getProxyProtocolForPrivateLinkMode().orElseThrow();
    assertEquals(pMode, proxyProtocolForPrivateLinkMode);
  }

  @Test
  public void testSetPartnerIntegrationsData() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getGroupId()));
    _clusterDescriptionDao.save(clusterDescription);

    // Initially, partner integrations data should be null
    assertNull(clusterDescription.getPartnerIntegrationsData().orElse(null));

    // Test setting partner integrations data with VERCEL_NATIVE type
    final PartnerIntegrationsData vercelPartnerIntegrationsData =
        new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false);
    _clusterDescriptionDao.setPartnerIntegrationsData(
        clusterDescription.getGroupId(),
        clusterDescription.getName(),
        vercelPartnerIntegrationsData);

    final ClusterDescription updatedCluster1 =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    final PartnerIntegrationsData retrievedData1 =
        updatedCluster1.getPartnerIntegrationsData().orElseThrow();
    assertEquals(IntegrationType.VERCEL_NATIVE, retrievedData1.getIntegrationType());
    assertFalse(retrievedData1.isPending());

    // Test setting partner integrations data back to null
    _clusterDescriptionDao.setPartnerIntegrationsData(
        clusterDescription.getGroupId(), clusterDescription.getName(), null);

    final ClusterDescription updatedCluster2 =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();
    assertFalse(updatedCluster2.getPartnerIntegrationsData().isPresent());
  }

  @Test
  public void testAutoShardingFieldSaveAndRetrieve() {
    // Test saving and retrieving cluster with default auto-sharding configuration (null)
    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(descDoc);

    // Verify default auto-sharding configuration is null
    assertFalse(clusterDescription.getAutoSharding().isPresent());

    _clusterDescriptionDao.save(clusterDescription);

    // Retrieve and verify the auto-sharding configuration is still null
    final ClusterDescription retrievedCluster =
        _clusterDescriptionDao
            .findByName(clusterDescription.getGroupId(), clusterDescription.getName())
            .orElseThrow();

    assertFalse(retrievedCluster.getAutoSharding().isPresent());

    // Test saving and retrieving cluster with enabled auto-sharding configuration
    final BasicDBObject descDoc2 = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription2 =
        ClusterDescription.getCloudProviderClusterDescription(descDoc2)
            .copy()
            .setAutoSharding(AutoSharding.getDefaultAutoSharding())
            .build();

    // Verify enabled auto-sharding configuration
    assertTrue(clusterDescription2.getAutoSharding().isPresent());
    assertTrue(clusterDescription2.getAutoSharding().get().isEnabled());
    assertEquals(100, clusterDescription2.getAutoSharding().get().getMaxShards());
    assertEquals(
        1_000_000L, clusterDescription2.getAutoSharding().get().getThresholdToAutoShardInBytes());

    _clusterDescriptionDao.save(clusterDescription2);

    // Retrieve and verify the auto-sharding configuration is preserved
    final ClusterDescription retrievedCluster2 =
        _clusterDescriptionDao
            .findByName(clusterDescription2.getGroupId(), clusterDescription2.getName())
            .orElseThrow();

    assertTrue(retrievedCluster2.getAutoSharding().isPresent());
    assertTrue(retrievedCluster2.getAutoSharding().get().isEnabled());
    assertEquals(100, retrievedCluster2.getAutoSharding().get().getMaxShards());
    assertEquals(
        1_000_000L, retrievedCluster2.getAutoSharding().get().getThresholdToAutoShardInBytes());
  }

  @Test
  public void testConvertToSelfManagedGeoSharding_success() {
    // Create a dedicated sharded cluster using the factory method
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject clusterDoc =
        NDSModelTestFactory.getAWSShardedClusterDescription(group.getGroupId(), "test-cluster");
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(clusterDoc);

    _clusterDescriptionDao.save(clusterDescription);

    // Convert to self-managed geo-sharding
    _clusterDescriptionDao.convertToSelfManagedGeoSharding(group.getGroupId(), "test-cluster");

    // Verify the conversion
    final ClusterDescription updatedCluster =
        _clusterDescriptionDao.findByName(group.getGroupId(), "test-cluster").orElseThrow();

    assertEquals(ClusterDescription.ClusterType.GEOSHARDED, updatedCluster.getClusterType());
    assertTrue(updatedCluster.getGeoSharding().isSelfManagedSharding());
    assertTrue(updatedCluster.getGeoSharding().getCustomZoneMapping().isEmpty());
    assertTrue(updatedCluster.getGeoSharding().getManagedNamespaces().isEmpty());
  }

  @Test
  public void testConvertToSelfManagedGeoSharding_clusterNotFound() {
    final ObjectId groupId = new ObjectId();
    final String clusterName = "non-existent-cluster";

    // Attempt to convert non-existent cluster - should not throw exception
    _clusterDescriptionDao.convertToSelfManagedGeoSharding(groupId, clusterName);

    // Verify no cluster was created
    assertTrue(_clusterDescriptionDao.findByName(groupId, clusterName).isEmpty());
  }

  @Test
  public void testConvertToSelfManagedGeoSharding_alreadyConverted() {
    // Create a cluster already configured for self-managed geo-sharding
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject clusterDoc =
        NDSModelTestFactory.getAWSShardedClusterDescription(
                group.getGroupId(), "already-converted-cluster")
            .append(FieldDefs.CLUSTER_TYPE, ClusterDescription.ClusterType.GEOSHARDED.name())
            .append(
                FieldDefs.GEO_SHARDING,
                new BasicDBObject()
                    .append(GeoSharding.FieldDefs.SELF_MANAGED_SHARDING, true)
                    .append(GeoSharding.FieldDefs.CUSTOM_ZONE_MAPPING, new BasicDBObject())
                    .append(GeoSharding.FieldDefs.MANAGED_NAMESPACES, new BasicDBList()));

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(clusterDoc);

    _clusterDescriptionDao.save(clusterDescription);

    // Convert again (should be idempotent)
    _clusterDescriptionDao.convertToSelfManagedGeoSharding(
        group.getGroupId(), "already-converted-cluster");

    // Verify it's still configured correctly
    final ClusterDescription updatedCluster =
        _clusterDescriptionDao
            .findByName(group.getGroupId(), "already-converted-cluster")
            .orElseThrow();

    assertEquals(ClusterDescription.ClusterType.GEOSHARDED, updatedCluster.getClusterType());
    assertTrue(updatedCluster.getGeoSharding().isSelfManagedSharding());
    assertTrue(updatedCluster.getGeoSharding().getCustomZoneMapping().isEmpty());
    assertTrue(updatedCluster.getGeoSharding().getManagedNamespaces().isEmpty());
  }

  @Test
  public void testConvertToSelfManagedGeoSharding_clearsExistingConfiguration() {
    // Create a cluster with existing geo-sharding configuration
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final BasicDBObject existingZoneMapping = new BasicDBObject("zone1", new ObjectId());
    final BasicDBList existingNamespaces = new BasicDBList();
    existingNamespaces.add(
        new BasicDBObject()
            .append("db", "testdb")
            .append("collection", "testcoll")
            .append("shardKey", "testkey"));

    final BasicDBObject clusterDoc =
        NDSModelTestFactory.getAWSShardedClusterDescription(
                group.getGroupId(), "cluster-with-config")
            .append(FieldDefs.CLUSTER_TYPE, ClusterDescription.ClusterType.SHARDED.name())
            .append(
                FieldDefs.GEO_SHARDING,
                new BasicDBObject()
                    .append(GeoSharding.FieldDefs.SELF_MANAGED_SHARDING, false)
                    .append(GeoSharding.FieldDefs.CUSTOM_ZONE_MAPPING, existingZoneMapping)
                    .append(GeoSharding.FieldDefs.MANAGED_NAMESPACES, existingNamespaces));

    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(clusterDoc);

    _clusterDescriptionDao.save(clusterDescription);

    // Verify initial state has existing configuration
    ClusterDescription initialCluster =
        _clusterDescriptionDao.findByName(group.getGroupId(), "cluster-with-config").orElseThrow();
    assertFalse(initialCluster.getGeoSharding().getCustomZoneMapping().isEmpty());
    assertFalse(initialCluster.getGeoSharding().getManagedNamespaces().isEmpty());

    // Convert to self-managed geo-sharding
    _clusterDescriptionDao.convertToSelfManagedGeoSharding(
        group.getGroupId(), "cluster-with-config");

    // Verify the conversion cleared existing configuration
    final ClusterDescription updatedCluster =
        _clusterDescriptionDao.findByName(group.getGroupId(), "cluster-with-config").orElseThrow();

    assertEquals(ClusterDescription.ClusterType.GEOSHARDED, updatedCluster.getClusterType());
    assertTrue(updatedCluster.getGeoSharding().isSelfManagedSharding());
    assertTrue(updatedCluster.getGeoSharding().getCustomZoneMapping().isEmpty());
    assertTrue(updatedCluster.getGeoSharding().getManagedNamespaces().isEmpty());
  }

  @Test
  public void testSetGatewayRouterEligibilityState_toggleEligibility() {
    // GIVEN
    final ObjectId groupId = new ObjectId();
    final String clusterName = "test-cluster-toggle";
    final ClusterDescription cluster =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId, clusterName));

    _clusterDescriptionDao.save(cluster);

    final Date firstUpdateDate = new Date();
    final Date secondUpdateDate = DateUtils.addMinutes(firstUpdateDate, 5);

    // WHEN - Set to not eligible first
    _clusterDescriptionDao.setGatewayRouterEligibilityState(
        groupId, clusterName, false, firstUpdateDate);

    // THEN - Verify not eligible
    ClusterDescription updatedCluster =
        _clusterDescriptionDao.findByName(groupId, clusterName).orElseThrow();

    assertTrue(updatedCluster.getGatewayRouterEligible().isPresent());
    assertFalse(updatedCluster.getGatewayRouterEligible().get());
    assertEquals(
        firstUpdateDate, updatedCluster.getGatewayRouterEligibilityLastUpdateDate().orElseThrow());

    // WHEN - Toggle to eligible
    _clusterDescriptionDao.setGatewayRouterEligibilityState(
        groupId, clusterName, true, secondUpdateDate);

    // THEN - Verify now eligible with updated timestamp
    updatedCluster = _clusterDescriptionDao.findByName(groupId, clusterName).orElseThrow();

    assertTrue(updatedCluster.getGatewayRouterEligible().isPresent());
    assertTrue(updatedCluster.getGatewayRouterEligible().get());
    assertEquals(
        secondUpdateDate, updatedCluster.getGatewayRouterEligibilityLastUpdateDate().orElseThrow());
  }
}
