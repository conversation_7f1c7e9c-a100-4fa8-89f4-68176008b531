package com.xgen.svc.nds.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.db.legacy._public.cursor.RawModelCursor;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.RestoreStatus;
import com.xgen.cloud.cps.restore._public.model.TopologyHandler;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseSvcTest;
import jakarta.inject.Inject;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class BackupRestoreJobDaoFilterSortIntTests extends JUnit5BaseSvcTest {
  @Inject private BackupRestoreJobDao backupRestoreJobDao;

  // truncate gets current current date at midnight
  private static final Date today = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);

  private static final Date lowerBound = DateUtils.addDays(today, -2);
  private static final Date upperBound = DateUtils.addDays(today, 1);

  @BeforeEach
  public void setUp() throws Exception {
    backupRestoreJobDao.getDbCollection().drop();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/cps/restore/restoreJobsFilterSortTestData.json.ftl",
        null,
        BackupRestoreJobDao.DB_NAME,
        BackupRestoreJobDao.COLLECTION_NAME);
  }

  /**
   * Provides test parameters for target version filtering tests. Each argument contains:
   * targetVersion, expectedCount, description
   */
  private static Stream<Arguments> targetVersionFilterTestParameters() {
    return Stream.of(
        Arguments.of(
            "7.0.1",
            11,
            "Filter by target version 7.0.1 (should return 11 jobs: 6 regular + 1 sharded cluster +"
                + " 4 new test jobs)"),
        Arguments.of("8.0.1", 1, "Filter by target version 8.0.1 (should return 1 job)"),
        Arguments.of(
            null, 12, "No filter - should return all 12 jobs, sorted by duration (longer first)"));
  }

  @ParameterizedTest(name = "{2}")
  @MethodSource("targetVersionFilterTestParameters")
  public void findTopLevelWithFiltersSortedByDuration_targetVersionFilter_filtersCorrectly(
      String targetVersion, int expectedCount, String description) {

    try (final RawModelCursor<TopologyHandler> cursor =
        backupRestoreJobDao.findTopLevelWithFiltersSortedByDuration(
            lowerBound,
            upperBound,
            null,
            targetVersion,
            null,
            null,
            null,
            RestoreStatus.NONE,
            20)) {
      final List<TopologyHandler> results = cursor.toList();

      assertEquals(expectedCount, results.size());

      // If filtering by target version, verify all results have the expected version
      if (targetVersion != null) {
        for (TopologyHandler result : results) {
          assertEquals(targetVersion, result.getMetadata().getTarget().getTargetMdbVersion());
        }
      }

      // Verify sorting by duration (longest duration first) for all test cases
      verifySortingByDuration(results);
    }
  }

  /**
   * Provides test parameters for cloud provider filtering tests. Each argument contains:
   * cloudProvider, expectedCount, description
   */
  private static Stream<Arguments> cloudProviderFilterTestParameters() {
    return Stream.of(
        Arguments.of(
            "AWS",
            11,
            "Filter by AWS cloud provider (should return 11 jobs: 6 direct attach + 1 streaming + 4"
                + " new test jobs)"),
        Arguments.of("GCP", 1, "Filter by GCP cloud provider (should return 1 job)"),
        Arguments.of(null, 12, "No cloud provider filter - should return all 12 jobs"),
        Arguments.of("AZURE", 0, "Filter by non-existent cloud provider (should return 0 jobs)"));
  }

  /**
   * Provides test parameters for target project visibility filtering tests. Each argument contains:
   * targetProjectVisibility, expectedCount, description
   */
  private static Stream<Arguments> targetProjectVisibilityFilterTestParameters() {
    return Stream.of(
        Arguments.of(null, 12, "No target project visibility filter - should return all 12 jobs"),
        Arguments.of("", 12, "Empty target project visibility filter - should return all 12 jobs"),
        Arguments.of(
            "INTERNAL",
            1,
            "Filter by INTERNAL visibility (should return 1 job with INTERNAL visibility)"),
        Arguments.of(
            "DEFAULT_VISIBLE",
            11,
            "Filter by non-INTERNAL visibility (should return 11 jobs: 10 DEFAULT_VISIBLE + 1"
                + " null)"));
  }

  @ParameterizedTest(name = "{2}")
  @MethodSource("cloudProviderFilterTestParameters")
  public void findTopLevelWithFiltersSortedByDuration_cloudProviderFilter_filtersCorrectly(
      String cloudProvider, int expectedCount, String description) {

    try (final RawModelCursor<TopologyHandler> cursor =
        backupRestoreJobDao.findTopLevelWithFiltersSortedByDuration(
            lowerBound,
            upperBound,
            cloudProvider,
            null,
            null,
            null,
            null,
            RestoreStatus.NONE,
            20)) {
      final List<TopologyHandler> results = cursor.toList();

      assertEquals(expectedCount, results.size());
      // Note: Cloud provider access depends on the restore job type

      // Verify sorting by duration (longest duration first) for all test cases
      verifySortingByDuration(results);
    }
  }

  @ParameterizedTest(name = "{2}")
  @MethodSource("targetProjectVisibilityFilterTestParameters")
  public void
      findTopLevelWithFiltersSortedByDuration_targetProjectVisibilityFilter_filtersCorrectly(
          String targetProjectVisibility, int expectedCount, String description) {

    try (final RawModelCursor<TopologyHandler> cursor =
        backupRestoreJobDao.findTopLevelWithFiltersSortedByDuration(
            lowerBound,
            upperBound,
            null,
            null,
            null,
            targetProjectVisibility,
            null,
            RestoreStatus.NONE,
            20)) {
      final List<TopologyHandler> results = cursor.toList();
      assertEquals(expectedCount, results.size());
      verifySortingByDuration(results);
    }
  }

  /**
   * Provides test parameters for restore status filtering tests. Each argument contains:
   * restoreStatus, expectedCount, description
   */
  private static Stream<Arguments> restoreStatusFilterTestParameters() {
    return Stream.of(
        Arguments.of(
            RestoreStatus.SUCCESS,
            7,
            "Filter by SUCCESS status (should return 7 jobs: original jobs minus 1 streaming job)"),
        Arguments.of(
            RestoreStatus.FAILED,
            1,
            "Filter by FAILED status (should return 1 job with failed=true)"),
        Arguments.of(
            RestoreStatus.CANCELED,
            1,
            "Filter by CANCELED status (should return 1 job with canceled=true)"),
        Arguments.of(
            RestoreStatus.EXPIRED,
            1,
            "Filter by EXPIRED status (should return 1 job with expired=true)"),
        Arguments.of(
            RestoreStatus.IN_PROGRESS,
            2,
            "Filter by IN_PROGRESS status (should return 2 jobs: 1 streaming + 1 new in_progress)"),
        Arguments.of(
            RestoreStatus.NONE, 12, "No restore status filter - should return all 12 jobs"));
  }

  @ParameterizedTest(name = "{2}")
  @MethodSource("restoreStatusFilterTestParameters")
  public void findTopLevelWithFiltersSortedByDuration_restoreStatusFilter_filtersCorrectly(
      RestoreStatus restoreStatus, int expectedCount, String description) {

    try (final RawModelCursor<TopologyHandler> cursor =
        backupRestoreJobDao.findTopLevelWithFiltersSortedByDuration(
            lowerBound, upperBound, null, null, null, null, null, restoreStatus, 20)) {
      final List<TopologyHandler> results = cursor.toList();

      assertEquals(expectedCount, results.size());

      // Verify that all results match the expected restore status
      if (restoreStatus != RestoreStatus.NONE) {
        for (TopologyHandler result : results) {
          RestoreStatus actualStatus = determineRestoreStatus(result);
          assertEquals(
              restoreStatus,
              actualStatus,
              "Job " + result.getMetadata().getId() + " should have status " + restoreStatus);
        }
      }

      // Verify sorting by duration (longest duration first) for all test cases
      verifySortingByDuration(results);
    }
  }

  /**
   * Determines the RestoreStatus of a restore job based on its fields. This mirrors the logic used
   * in the DAO filtering.
   */
  private RestoreStatus determineRestoreStatus(TopologyHandler job) {
    final boolean hasFinishedDate = job.getFinishedDate().isPresent();
    final boolean isFailed = job.getMetadata().isFailed();
    final boolean isCanceled = job.getMetadata().getCanceled();
    final boolean isExpired = job.getMetadata().getExpired();

    if (!hasFinishedDate) {
      return RestoreStatus.IN_PROGRESS;
    } else if (isFailed) {
      return RestoreStatus.FAILED;
    } else if (isCanceled) {
      return RestoreStatus.CANCELED;
    } else if (isExpired) {
      return RestoreStatus.EXPIRED;
    } else {
      return RestoreStatus.SUCCESS;
    }
  }

  /**
   * Verifies that the results are sorted by duration in descending order (longest duration first).
   * Duration is calculated as: finishedDate - creationTime (extracted from ObjectId).
   */
  private void verifySortingByDuration(List<TopologyHandler> results) {
    if (results.size() <= 1) {
      return; // No sorting verification needed for 0 or 1 result
    }

    for (int i = 0; i < results.size() - 1; i++) {
      final TopologyHandler current = results.get(i);
      final TopologyHandler next = results.get(i + 1);

      // Only verify sorting for jobs that have finished (have finishedDate)
      if (current.getFinishedDate().isPresent() && next.getFinishedDate().isPresent()) {
        final long currentDuration = calculateDuration(current);
        final long nextDuration = calculateDuration(next);

        // Current job should have duration >= next job (descending order)
        assertTrue(
            currentDuration >= nextDuration,
            String.format(
                "Jobs not sorted by duration correctly. Job at index %d has duration %d ms, "
                    + "but job at index %d has duration %d ms. Expected descending order.",
                i, currentDuration, i + 1, nextDuration));
      }
    }
  }

  /**
   * Test to verify that jobs with finishedDate = null are included in results. These are jobs that
   * are still in progress and haven't completed yet.
   */
  @Test
  public void findTopLevelWithFiltersSortedByDuration_includesJobsWithNullFinishedDate() {

    try (final RawModelCursor<TopologyHandler> cursor =
        backupRestoreJobDao.findTopLevelWithFiltersSortedByDuration(
            lowerBound, upperBound, null, null, null, null, null, RestoreStatus.NONE, 20)) {
      final List<TopologyHandler> results = cursor.toList();

      // Should return all 12 jobs including those with finishedDate = null
      assertEquals(12, results.size());

      // Verify that we have at least one job with null finishedDate
      long nullFinishedDateCount =
          results.stream().filter(job -> job.getFinishedDate().isEmpty()).count();

      assertEquals(2, nullFinishedDateCount, "Should have 2 jobs with null finishedDate");

      // Verify that jobs with null finishedDate are included in sorting
      // (they should appear at the end since they have 0 duration)
      final TopologyHandler lastJob = results.get(results.size() - 1);
      assertTrue(
          lastJob.getFinishedDate().isEmpty(),
          "Job with null finishedDate should be sorted last (shortest duration = 0)");
    }
  }

  /**
   * Calculates the duration of a restore job as finishedDate - creationTime. CreationTime is
   * extracted from the ObjectId timestamp.
   */
  private long calculateDuration(TopologyHandler job) {
    final Date finishedDate = job.getFinishedDate().orElse(null);
    if (finishedDate == null) {
      return 0; // Job hasn't finished yet
    }

    // Extract creation time from ObjectId (same logic as used in the DAO aggregation pipeline)
    final Date creationTime = new Date(job.getMetadata().getId().getTimestamp() * 1000L);
    return finishedDate.getTime() - creationTime.getTime();
  }
}
