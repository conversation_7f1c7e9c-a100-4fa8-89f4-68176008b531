package com.xgen.svc.nds.res;

import static com.xgen.svc.core.JsonTestUtils.assertJSONEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.activity.AuditSvcIntTestUtils;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit.Type;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.module._public.util.CloudProviderRegistryUtil;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupMaintenanceDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.NDSGroupMaintenanceWindow;
import com.xgen.cloud.nds.project._public.model.ReleaseCohorts;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.project._public.model.versions.PhasedReleaseCriteria;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.PhasedVersionFactory;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersionParameters;
import com.xgen.cloud.nds.project._public.model.versions.ReleaseMode;
import com.xgen.cloud.nds.project._public.svc.versions.PhasedVersionSvc;
import com.xgen.cloud.nds.project._public.util.NDSMaintenanceDateCalculationUtil;
import com.xgen.cloud.nds.resourcepolicy._public.model.Policy;
import com.xgen.cloud.nds.resourcepolicy._public.model.ResourcePolicy;
import com.xgen.cloud.nds.resourcepolicy._public.model.ResourcePolicyValidationError;
import com.xgen.cloud.nds.resourcepolicy._public.svc.AtlasResourcePolicySvc;
import com.xgen.cloud.nds.resourcepolicy.util.AtlasResourcePolicyTestUtil;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.BaseResourceTestCommon;
import com.xgen.svc.core.GuiceTestRunner;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.MaintenanceProtectedHoursView;
import com.xgen.svc.nds.model.ui.MaintenanceWindowView;
import com.xgen.svc.nds.svc.project.NDSGroupMaintenanceSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class NDSGroupMaintenanceResourceIntTests extends BaseResourceTestCommon {

  @Inject private NDSGroupMaintenanceDao _maintenanceDao;
  @Inject private NDSMaintenanceDateCalculationUtil _maintenanceDateCalculationUtil;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private PhasedVersionSvc _phasedVersionSvc;
  @Inject private PhasedVersionFactory _phasedVersionFactory;
  @Inject private AuditSvc _auditSvc;
  @Inject private AtlasResourcePolicySvc _atlasResourcePolicySvc;

  private Group _group;
  private AppUser _user;

  @BeforeClass
  public static void prepareEnv() {
    BaseResourceTestCommon.prepareEnv();
    CloudProviderRegistryUtil.registerAllProvider();
  }

  @AfterClass
  public static void deregisterProviders() {
    CloudProviderRegistryUtil.deregisterAllProvider();
  }

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createUser(_group);
    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);

    FeatureFlagIntTestUtil.enableFeatureGlobally(FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureGlobally(
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);
  }

  public NDSGroupDao getNDSGroupDao() {
    return _ndsGroupDao;
  }

  public Group getTargetGroup() {
    return _group;
  }

  public AppUser getGroupUser() {
    return _user;
  }

  @Test
  public void testGetMaintenanceWindow() {
    final String endpoint = String.format("/nds/%s/maintenanceWindow", getTargetGroup().getId());

    final JSONObject retVal = doAuthedJsonGet(getGroupUser(), endpoint);
    assertFalse(retVal.keySet().isEmpty());
    assertJSONEquals(
        retVal.getJSONObject("protectedHours"),
        new JSONObject()
            .put(MaintenanceProtectedHoursView.FieldDefs.IS_USER_DEFINED, false)
            .put(MaintenanceProtectedHoursView.FieldDefs.START_HOUR_OF_DAY, JSONObject.NULL)
            .put(MaintenanceProtectedHoursView.FieldDefs.END_HOUR_OF_DAY, JSONObject.NULL));
  }

  @Test
  public void testPatchMaintenanceWindow() {
    final String endpoint = String.format("/nds/%s/maintenanceWindow", getTargetGroup().getId());

    // Test setting new maintenance window
    final JSONObject maintenanceWindow = new JSONObject().put("dayOfWeek", 1).put("hourOfDay", 2);
    final JSONObject windowSetResponse =
        doAuthedJsonPatch(getGroupUser(), endpoint, maintenanceWindow);

    assertEquals(maintenanceWindow.get("dayOfWeek"), windowSetResponse.get("dayOfWeek"));
    assertEquals(maintenanceWindow.get("hourOfDay"), windowSetResponse.get("hourOfDay"));
    assertFalse(windowSetResponse.getBoolean("startASAP"));

    // Test starting maintenance ASAP
    final JSONObject startASAP = new JSONObject().put("startASAP", true);
    final JSONObject startASAPResponse = doAuthedJsonPatch(getGroupUser(), endpoint, startASAP);
    assertTrue(startASAPResponse.getBoolean("startASAP"));
    // verify protectedHours is initial value
    final JSONObject protectedHours = startASAPResponse.getJSONObject("protectedHours");
    assertFalse(protectedHours.getBoolean(MaintenanceProtectedHoursView.FieldDefs.IS_USER_DEFINED));

    // protected hours allowed when feature flag is enabled
    {
      final JSONObject protectedHoursRequest =
          new JSONObject()
              .put(
                  "protectedHours",
                  new JSONObject()
                      .put(MaintenanceProtectedHoursView.FieldDefs.START_HOUR_OF_DAY, 0)
                      .put(MaintenanceProtectedHoursView.FieldDefs.END_HOUR_OF_DAY, 4));
      final JSONObject protectedHoursResponse =
          doAuthedJsonPatch(getGroupUser(), endpoint, protectedHoursRequest);
      assertJSONEquals(
          new JSONObject()
              .put(MaintenanceProtectedHoursView.FieldDefs.IS_USER_DEFINED, true)
              .put(MaintenanceProtectedHoursView.FieldDefs.START_HOUR_OF_DAY, 0)
              .put(MaintenanceProtectedHoursView.FieldDefs.END_HOUR_OF_DAY, 4),
          protectedHoursResponse.getJSONObject("protectedHours"));

      final JSONObject removeProtectedHoursRequest =
          new JSONObject().put(MaintenanceWindowView.FieldDefs.PROTECTED_HOURS, JSONObject.NULL);

      final JSONObject removeProtectedHoursResponse =
          doAuthedJsonPatch(getGroupUser(), endpoint, removeProtectedHoursRequest);
      final JSONObject protectedHoursObj =
          removeProtectedHoursResponse.getJSONObject(
              MaintenanceWindowView.FieldDefs.PROTECTED_HOURS);
      assertTrue(
          protectedHoursObj.isNull(MaintenanceProtectedHoursView.FieldDefs.START_HOUR_OF_DAY));
      assertTrue(protectedHoursObj.isNull(MaintenanceProtectedHoursView.FieldDefs.END_HOUR_OF_DAY));
      assertFalse(
          protectedHoursObj.getBoolean(MaintenanceProtectedHoursView.FieldDefs.IS_USER_DEFINED));
    }
  }

  @Test
  public void testResetMaintenanceWindow() {
    final NDSGroupMaintenanceWindow existingWindow =
        getNDSGroupDao().find(getTargetGroup().getId()).get().getMaintenanceWindow();

    // Make sure the new day / hour set is different from the existing day / hour and that they are
    // valid numbers
    final int newHourOfDay =
        Optional.ofNullable(existingWindow)
            .map(NDSGroupMaintenanceWindow::getHourOfDay)
            .map(h -> (h + 1) % 24)
            .orElse(0);

    final String endpoint = String.format("/nds/%s/maintenanceWindow", getTargetGroup().getId());

    final JSONObject maintenanceWindow = new JSONObject();
    maintenanceWindow.put("dayOfWeek", 1);
    maintenanceWindow.put("hourOfDay", newHourOfDay);
    doAuthedJsonPatch(getGroupUser(), endpoint, maintenanceWindow);

    AuditSvcIntTestUtils.verifyEvents(_auditSvc)
        .ofType(NDSAudit.Type.MAINTENANCE_PROTECTED_HOURS_REMOVED)
        .willBeAudited()
        .during(() -> doAuthedDelete(getGroupUser(), endpoint, HttpStatus.SC_OK));

    final NDSGroupMaintenanceWindow resetWindow =
        getNDSGroupDao().find(getTargetGroup().getId()).get().getMaintenanceWindow();
    assertNull(resetWindow.getDayOfWeek());
    assertNull(resetWindow.getHourOfDay());
    assertFalse(resetWindow.isUserDefined());
  }

  @Test
  public void testMaintenanceWindowResource_resourcePolicy() {
    FeatureFlagIntTestUtil.enableFeatureGlobally(
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);
    final NDSGroupMaintenanceWindow existingWindow =
        getNDSGroupDao().find(getTargetGroup().getId()).get().getMaintenanceWindow();
    final Policy policyEnsureWindowExists =
        new Policy(
            """
              forbid (
                principal,
                action == ResourcePolicy::Action::"project.maintenanceWindow.modify",
                resource
              ) when {
                context.project.hasDefinedMaintenanceWindow == false
              };
            """);

    final ResourcePolicy resourcePolicy =
        AtlasResourcePolicyTestUtil.makeDefaultPolicy(new ObjectId(), getTargetGroup().getOrgId())
            .toBuilder()
            .setPolicies(List.of(policyEnsureWindowExists))
            .build();
    var savePolicy =
        _atlasResourcePolicySvc.saveResourcePolicy(
            resourcePolicy, Type.RESOURCE_POLICY_CREATED, null, null);
    Assertions.assertTrue(
        savePolicy.isOk(),
        String.format(
            "Error saving policy, %s",
            savePolicy
                .err()
                .map(ResourcePolicyValidationError::errorType)
                .map(ResourcePolicyValidationError.ValidationErrorType::name)
                .orElse("no error")));

    // Make sure the new day / hour set is different from the existing day / hour and that they are
    // valid numbers
    final int newHourOfDay =
        Optional.ofNullable(existingWindow)
            .map(NDSGroupMaintenanceWindow::getHourOfDay)
            .map(h -> (h + 1) % 24)
            .orElse(0);

    final String endpoint = String.format("/nds/%s/maintenanceWindow", getTargetGroup().getId());

    final JSONObject maintenanceWindow = new JSONObject();
    maintenanceWindow.put("dayOfWeek", 1);
    maintenanceWindow.put("hourOfDay", newHourOfDay);
    doAuthedJsonPatch(getGroupUser(), endpoint, maintenanceWindow);

    doAuthedDelete(getGroupUser(), endpoint, HttpStatus.SC_FORBIDDEN);

    _atlasResourcePolicySvc.deleteResourcePolicy(resourcePolicy, null);

    doAuthedDelete(getGroupUser(), endpoint, HttpStatus.SC_OK);

    final NDSGroupMaintenanceWindow resetWindow =
        getNDSGroupDao().find(getTargetGroup().getId()).get().getMaintenanceWindow();
    assertNull(resetWindow.getDayOfWeek());
    assertNull(resetWindow.getHourOfDay());
    assertFalse(resetWindow.isUserDefined());
  }

  @Test
  public void testDeferMaintenanceOneWeek() {
    // artificially set user defined window and advance notification send date for group
    final Calendar calendar =
        _maintenanceDateCalculationUtil.getCalendarInstance(_group.getDefaultTimeZoneId());
    calendar.add(Calendar.HOUR, 1);

    _maintenanceDao.setUserDefinedMaintenanceWindow(
        getTargetGroup().getId(),
        calendar.get(Calendar.DAY_OF_WEEK),
        calendar.get(Calendar.HOUR_OF_DAY),
        false);
    _maintenanceDao.setInAdvancedNotificationSentForGroup(getTargetGroup().getId(), new Date());

    final NDSGroupMaintenanceWindow existingWindow =
        getNDSGroupDao().find(getTargetGroup().getId()).get().getMaintenanceWindow();
    assertFalse(existingWindow.getDeferralRequestDate().isPresent());
    assertEquals(0, (int) existingWindow.getNumberOfDeferrals());
    assertTrue(existingWindow.getAdvanceNotificationSendDate().isPresent());

    final String endpoint =
        String.format("/nds/%s/maintenanceWindow/defer", getTargetGroup().getId());
    doAuthedJsonPost(getGroupUser(), endpoint, new JSONObject());

    final NDSGroupMaintenanceWindow deferredWindow =
        getNDSGroupDao().find(getTargetGroup().getId()).get().getMaintenanceWindow();
    assertTrue(deferredWindow.getDeferralRequestDate().isPresent());
    assertEquals(1, (int) deferredWindow.getNumberOfDeferrals());
    assertFalse(deferredWindow.getAdvanceNotificationSendDate().isPresent());
  }

  @Test
  public void testGetIsInOngoingPhasedVersionRelease() throws IOException, InterruptedException {
    // setup: paused rollout
    final PhasedVersionParameters phasedVersionParameters =
        new PhasedVersionParameters(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION, "7.2.1")
            .setReleaseMode(ReleaseMode.PHASED)
            .setPhasedReleaseCriteria(new PhasedReleaseCriteria(70, 70, null, null, null, null))
            .setIsPaused(true);
    _phasedVersionSvc.save(_phasedVersionFactory.createPhasedVersion(phasedVersionParameters));
    final PhasedVersion phasedVersion =
        _phasedVersionSvc
            .getCachedVersionForSoftwareTypes()
            .get(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);
    assertTrue(phasedVersion.isPaused());

    final String clusterName = "continuousCluster";
    final ClusterDescription cd =
        NDSModelTestFactory.getDefaultClusterDescription(
                _group.getId(),
                clusterName,
                List.of(
                    NDSModelTestFactory.getShardRegionConfigForRegion(
                        AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0)))
            .copy()
            .setVersionReleaseSystem(VersionReleaseSystem.CONTINUOUS)
            .setMongoDBMajorVersion("7.2")
            .setMongoDBVersion("7.2.0")
            .build();
    _clusterDescriptionDao.save(cd);
    final Set<NDSInstanceSize> instanceSizes = cd.getInstanceSizes(NodeType.ELECTABLE);
    // test assumption
    assertEquals(Set.of(AWSNDSInstanceSize.M30), instanceSizes);

    final String endpoint =
        String.format(
            "/nds/%s/maintenanceWindow/isInOngoingPhasedVersionRelease/%s?invalidateVersionCache=true",
            getTargetGroup().getId(), clusterName);

    final ObjectMapper objectMapper = new ObjectMapper();
    final Boolean isInOngoingPhasedRelease_rolloutPaused =
        objectMapper.readValue(doAuthedGetBytes(getGroupUser(), endpoint), Boolean.class);

    assertFalse(isInOngoingPhasedRelease_rolloutPaused);

    // setup: ongoing rollout, group not in cohort
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(phasedVersionParameters.setIsPaused(false)));
    final PhasedVersion phasedVersion_resumed =
        _phasedVersionSvc
            .getCachedVersionForSoftwareTypes()
            .get(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);
    assertFalse(phasedVersion_resumed.isPaused());

    _ndsGroupDao.overrideReleaseCohorts(getTargetGroup().getId(), new ReleaseCohorts(80, 80));
    assertFalse(
        NDSGroupMaintenanceSvc.isInVersionReleaseCohort(
            _ndsGroupSvc.find(getTargetGroup().getId()).get(), phasedVersion_resumed));
    assertTrue(NDSGroupMaintenanceSvc.isInAllowedInstanceSizes(cd, phasedVersion_resumed));

    final Boolean isInOngoingPhasedRelease_rolloutOngoing =
        objectMapper.readValue(doAuthedGetBytes(getGroupUser(), endpoint), Boolean.class);

    assertFalse(isInOngoingPhasedRelease_rolloutOngoing);

    // setup: group in cohort
    _ndsGroupDao.overrideReleaseCohorts(getTargetGroup().getId(), new ReleaseCohorts(60, 60));

    assertTrue(
        NDSGroupMaintenanceSvc.isInVersionReleaseCohort(
            _ndsGroupSvc.find(getTargetGroup().getId()).get(), phasedVersion_resumed));
    assertTrue(NDSGroupMaintenanceSvc.isInAllowedInstanceSizes(cd, phasedVersion_resumed));

    final Boolean isInOngoingPhasedRelease_rolloutOngoing_inCohort =
        objectMapper.readValue(doAuthedGetBytes(getGroupUser(), endpoint), Boolean.class);
    assertTrue(isInOngoingPhasedRelease_rolloutOngoing_inCohort);

    // setup: instance size disallowed
    final PhasedReleaseCriteria instanceSizeDisallowed =
        new PhasedReleaseCriteria(70, 70, null, null, null, List.of("M10"));

    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            phasedVersionParameters.setPhasedReleaseCriteria(instanceSizeDisallowed)));
    final PhasedVersion phasedVersion_instanceSizeDisallowed =
        _phasedVersionSvc
            .getCachedVersionForSoftwareTypes()
            .get(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);

    assertTrue(
        NDSGroupMaintenanceSvc.isInVersionReleaseCohort(
            _ndsGroupSvc.find(getTargetGroup().getId()).get(), phasedVersion_resumed));
    assertFalse(
        NDSGroupMaintenanceSvc.isInAllowedInstanceSizes(cd, phasedVersion_instanceSizeDisallowed));

    final Boolean isInOngoingPhasedRelease_rolloutOngoing_instanceSizeDisallowed =
        objectMapper.readValue(doAuthedGetBytes(getGroupUser(), endpoint), Boolean.class);

    assertFalse(isInOngoingPhasedRelease_rolloutOngoing_instanceSizeDisallowed);

    // setup: instance size explicitly allowed
    final PhasedReleaseCriteria instanceSizeAllowed =
        new PhasedReleaseCriteria(
            70, 70, null, null, null, instanceSizes.stream().map(NDSInstanceSize::name).toList());
    _phasedVersionSvc.save(
        _phasedVersionFactory.createPhasedVersion(
            phasedVersionParameters.setPhasedReleaseCriteria(instanceSizeAllowed)));
    final PhasedVersion phasedVersion_instanceSizeAllowed =
        _phasedVersionSvc
            .getCachedVersionForSoftwareTypes()
            .get(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);

    assertTrue(
        NDSGroupMaintenanceSvc.isInVersionReleaseCohort(
            _ndsGroupSvc.find(getTargetGroup().getId()).get(), phasedVersion_resumed));
    assertTrue(
        NDSGroupMaintenanceSvc.isInAllowedInstanceSizes(cd, phasedVersion_instanceSizeAllowed));

    final Boolean isInOngoingPhasedRelease_rolloutOngoing_instanceSizeAllowed =
        objectMapper.readValue(doAuthedGetBytes(getGroupUser(), endpoint), Boolean.class);

    assertTrue(isInOngoingPhasedRelease_rolloutOngoing_instanceSizeAllowed);
  }
}
