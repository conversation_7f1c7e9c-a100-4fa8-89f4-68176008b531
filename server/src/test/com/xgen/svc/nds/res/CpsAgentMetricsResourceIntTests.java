package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;

import com.mongodb.BasicDBObject;
import com.mongodb.WriteConcern;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._public.model.AttachStats;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.RestoreTargetCluster;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.GroupVisibility;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class CpsAgentMetricsResourceIntTests extends JUnit5BaseResourceTest {

  @Inject private BackupRestoreJobDao backupRestoreJobDao;

  private Group group;
  private AgentApiKey agentApiKey;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("Test");

    group = MmsFactory.createGroup(organization, "group");
    final AppUser _userAWS =
        MmsFactory.createUser(group, String.format("<EMAIL>", getUniquifier()));
    agentApiKey = MmsFactory.generateApiKey(group.getId(), _userAWS.getId());
  }

  @Test
  public void testUpdateAttachStats() throws Exception {
    // Create a DirectAttachReplicaSetBackupRestoreJob for testing
    final ObjectId restoreJobId = new ObjectId();
    final ObjectId instanceId = new ObjectId();
    final String hostname = "test-hostname";

    final DirectAttachReplicaSetBackupRestoreJob.Builder jobBuilder =
        DirectAttachReplicaSetBackupRestoreJob.Builder.aDirectAttachReplicaSetRestoreJob()
            .withParentBuilder(
                ReplicaSetBackupRestoreJob.Builder.aReplicaSetBackupRestoreJob()
                    .withRegionName(AWSRegionName.US_EAST_1)
                    .withMetadataBuilder(
                        CpsRestoreMetadata.Builder.aCpsRestoreMetadata()
                            .withSnapshotId(ObjectId.get())
                            .withTarget(
                                RestoreTargetCluster.createRestoreTargetCluster(
                                    group.getId(),
                                    GroupVisibility.DEFAULT_VISIBLE,
                                    "cluster1",
                                    "cluster1",
                                    "",
                                    "UUID",
                                    "UUID"))
                            .withId(restoreJobId)
                            .withProjectId(group.getId())
                            .withClusterName("cluster0")
                            .withRequestingUser(mock(CpsRestoreMetadata.RequestingUser.class))
                            .withStrategy(CpsRestoreMetadata.StrategyName.DIRECT_ATTACH)))
            .withAttachStatuses(
                List.of(
                    new DirectAttachReplicaSetBackupRestoreJob.AttachStatus(
                        new BasicDBObject()
                            .append(
                                DirectAttachReplicaSetBackupRestoreJob.AttachStatus.FieldDefs
                                    .INSTANCE_ID,
                                instanceId)
                            .append(
                                DirectAttachReplicaSetBackupRestoreJob.AttachStatus.FieldDefs
                                    .HOSTNAME,
                                hostname))))
            .withCloudProvider(CloudProvider.AWS);

    backupRestoreJobDao.save(jobBuilder.toDBObject(), WriteConcern.ACKNOWLEDGED);

    // Set initial attach stats
    final AttachStats initialAttachStats =
        new AttachStats(
            Map.of(), Map.of(), 100L, 200L, 300L, 400L, "gp3", 500L, 600L, 700L, 8.0, null, null,
            null, null);
    backupRestoreJobDao.updateDirectAttachRestoreAttachStatsMetrics(
        restoreJobId, hostname, initialAttachStats);

    final JSONObject requestPayload = new JSONObject();
    requestPayload.put("mongoStartupStats", JSONObject.NULL);
    requestPayload.put("mongoShutdownStats", JSONObject.NULL);
    requestPayload.put("detachVolumeDurationMilliseconds", 150L);
    requestPayload.put("createVolumeFromSnapshotDurationSeconds", JSONObject.NULL);
    requestPayload.put("attachVolumeDurationMilliseconds", JSONObject.NULL);
    requestPayload.put("mountVolumeWithSnapshotDataDurationMilliseconds", JSONObject.NULL);
    requestPayload.put("diskType", JSONObject.NULL);
    requestPayload.put("bounceStopIfUpWithForceKillDurationSeconds", JSONObject.NULL);
    requestPayload.put("getDirectAttachFileListDurationSeconds", JSONObject.NULL);
    requestPayload.put("diskPreWarmDurationSeconds", 800L);
    requestPayload.put("diskPreWarmThroughputMbPerSecond", JSONObject.NULL);

    // Make the POST request
    final String endpoint =
        String.format(
            "/agents/cps/metrics/%s/updateAttachStats/%s?hostname=%s",
            group.getId(), restoreJobId, hostname);

    doAgentApiCallPost(group, agentApiKey, endpoint, requestPayload);

    // Verify the stats were updated correctly
    final DirectAttachReplicaSetBackupRestoreJob updatedJob =
        backupRestoreJobDao.findDirectAttachReplSetJob(restoreJobId);
    final AttachStats updatedAttachStats = updatedJob.getAttachStatuses().get(0).getAttachStats();

    // Check that updated metrics have new values
    assertEquals(150L, updatedAttachStats.detachVolumeDurationMilliseconds());
    assertEquals(800L, updatedAttachStats.diskPreWarmDurationSeconds());

    // Check that non-updated metrics retain original values
    assertEquals(200L, updatedAttachStats.createVolumeFromSnapshotDurationSeconds());
    assertEquals(300L, updatedAttachStats.attachVolumeDurationMilliseconds());
    assertEquals(400L, updatedAttachStats.mountVolumeWithSnapshotDataDurationMilliseconds());
    assertEquals("gp3", updatedAttachStats.diskType());
    assertEquals(500L, updatedAttachStats.bounceStopIfUpWithForceKillDurationSeconds());
    assertEquals(600L, updatedAttachStats.getDirectAttachFileListDurationSeconds());
    assertEquals(8.0, updatedAttachStats.diskPreWarmThroughputMbPerSecond());
  }
}
