package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.mpa._public.svc.MPAAuthorizationSvc;
import com.xgen.cloud.nds.mpa._public.view.ssh.SSHApprovedMPARequest;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSServerAccessDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSServerAccess;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.NDSServerAccessView;
import com.xgen.svc.nds.svc.NDSAdminSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSAdminDashboardResourceIntTests extends JUnit5BaseResourceTest {

  @Inject private NDSAdminSvc ndsAdminSvc;
  @Inject private NDSGroupSvc ndsGroupSvc;
  @Inject private ReplicaSetHardwareDao replicaSetHardwareDao;
  @Inject private ClusterDescriptionDao clusterDescriptionDao;
  @Inject private NDSServerAccessDao ndsServerAccessDao;

  private Group group;
  private AppUser globalAtlasOperator;
  private AppUser groupOwner;
  private final String hostname = "test-hostname";
  private final int port = 27017;
  private final String clusterName = "test-cluster";

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    group = MmsFactory.createGroupWithNDSPlan();
    globalAtlasOperator = MmsFactory.createGlobalAtlasOperatorUser(group);
    groupOwner = MmsFactory.createGroupOwnerUser(group);

    // Set up MPA authorization service mock
    setupMpaAuthorizationMock();

    // Create actual infrastructure with a valid hostname
    MmsFactory.createStandalone(group, hostname, port);

    // Create NDS group infrastructure for hostname lookups
    ndsGroupSvc.ensureGroup(group.getId());

    // Create cluster description for cluster lookup
    createClusterDescription();

    // Create replica set hardware for hostname-to-group mapping
    createReplicaSetHardware();
  }

  private void setupMpaAuthorizationMock() throws Exception {
    // Create mock MPA authorization service
    MPAAuthorizationSvc mockMpaAuthorizationSvc = mock(MPAAuthorizationSvc.class);

    // Create an approved MPA request to return from validateMPAAccess
    final SSHApprovedMPARequest approvedMpaRequest =
        new SSHApprovedMPARequest(
            group.getId(),
            clusterName,
            "test-cluster-id",
            "Test reason for integration test",
            "http://test-cluster-link",
            "http://test-retry-link",
            globalAtlasOperator.getUsername(),
            globalAtlasOperator.getPrimaryEmail(),
            "Test Approver",
            "<EMAIL>");

    // Mock the validateMPAAccess method to return the approved request
    doReturn(approvedMpaRequest)
        .when(mockMpaAuthorizationSvc)
        .validateMPAAccess(anyString(), any(), any());

    // Replace the real service with our mock in the NDSAdminSvc instance
    // This is the correct approach for integration tests
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSAdminSvc.class), "_mpaAuthorizationSvc", mockMpaAuthorizationSvc);
  }

  private void createClusterDescription() {
    // Create cluster description that matches the cluster name used in replica set hardware
    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getId(), clusterName));
    clusterDescriptionDao.save(clusterDescription);
  }

  private void createReplicaSetHardware() {
    // Create replica set hardware that maps hostname to group ID
    // This is needed for _replicaSetHardwareSvc.getGroupIdByHostname() to work
    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(clusterName, group.getId(), 0);

    // Create the replica set hardware entry
    replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(
                group.getId(), clusterName, clusterName, List.of())
            .next()
            .rsId(),
        true,
        false,
        new ObjectId()); // replicationSpecId

    // Add an instance to the replica set
    final ObjectId instanceId =
        replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);

    // Set the hostname mapping and required fields for the instance
    final Hostnames hostnames = new Hostnames(hostname);
    replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        instanceId,
        false,
        InstanceHardware.FieldDefs.HOSTNAMES,
        hostnames.toDBList());

    // Set hostname scheme for agents (required for getHostnameForAgents to work)
    replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        instanceId,
        false,
        InstanceHardware.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
        InstanceHostname.HostnameScheme.LEGACY.name());

    // Mark instance as provisioned
    replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId, instanceId, false, InstanceHardware.FieldDefs.PROVISIONED, true);
  }

  @Test
  public void upgradeServerAccessRequest_upgradeFromBasicToElevated_success() throws Exception {
    // Create a server access request with BASIC access level
    final NDSServerAccessView initialRequest =
        createServerAccessRequest(NDSServerAccess.AccessLevel.BASIC);
    final ObjectId requestId = initialRequest.getId();

    // Prepare the upgrade request
    final JSONObject upgradeRequest = new JSONObject();
    upgradeRequest.put(
        NDSServerAccessView.FieldDefs.ACCESS_LEVEL_FIELD,
        NDSServerAccess.AccessLevel.ELEVATED.name());

    // Execute the PATCH request to upgrade access level
    final JSONObject response =
        doAuthedJsonPatch(
            globalAtlasOperator,
            "/admin/nds/serverAccess/requests/" + requestId,
            upgradeRequest,
            HttpStatus.SC_OK);

    // Verify the response
    assertNotNull(response);
    assertEquals(
        NDSServerAccess.AccessLevel.ELEVATED.name(),
        response.getString(NDSServerAccessView.FieldDefs.ACCESS_LEVEL_FIELD));
  }

  @Test
  public void upgradeServerAccessRequest_upgradeFromNoneToBasic_success() throws Exception {
    // Create a server access request with NONE access level
    final NDSServerAccessView initialRequest =
        createServerAccessRequest(NDSServerAccess.AccessLevel.NONE);
    final ObjectId requestId = initialRequest.getId();

    // Prepare the upgrade request
    final JSONObject upgradeRequest = new JSONObject();
    upgradeRequest.put(
        NDSServerAccessView.FieldDefs.ACCESS_LEVEL_FIELD, NDSServerAccess.AccessLevel.BASIC.name());

    // Execute the PATCH request to upgrade access level
    final JSONObject response =
        doAuthedJsonPatch(
            globalAtlasOperator,
            "/admin/nds/serverAccess/requests/" + requestId,
            upgradeRequest,
            HttpStatus.SC_OK);

    // Verify the response
    assertNotNull(response);
    assertEquals(
        NDSServerAccess.AccessLevel.BASIC.name(),
        response.getString(NDSServerAccessView.FieldDefs.ACCESS_LEVEL_FIELD));
  }

  @Test
  public void upgradeServerAccessRequest_attemptDowngrade_badRequest() throws Exception {
    // Create a server access request with ELEVATED access level
    final NDSServerAccessView initialRequest =
        createServerAccessRequest(NDSServerAccess.AccessLevel.ELEVATED);
    final ObjectId requestId = initialRequest.getId();

    // Attempt to downgrade to BASIC (should fail)
    final JSONObject downgradeRequest = new JSONObject();
    downgradeRequest.put(
        NDSServerAccessView.FieldDefs.ACCESS_LEVEL_FIELD, NDSServerAccess.AccessLevel.BASIC.name());

    // Execute the PATCH request and expect failure
    doAuthedJsonPatch(
        globalAtlasOperator,
        "/admin/nds/serverAccess/requests/" + requestId,
        downgradeRequest,
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void upgradeServerAccessRequest_nonExistentRequest_notFound() throws Exception {
    final ObjectId nonExistentId = new ObjectId();

    // Prepare the upgrade request
    final JSONObject upgradeRequest = new JSONObject();
    upgradeRequest.put(
        NDSServerAccessView.FieldDefs.ACCESS_LEVEL_FIELD,
        NDSServerAccess.AccessLevel.ELEVATED.name());

    // Execute the PATCH request and expect not found
    doAuthedJsonPatch(
        globalAtlasOperator,
        "/admin/nds/serverAccess/requests/" + nonExistentId,
        upgradeRequest,
        HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  public void upgradeServerAccessRequest_unauthorizedUser_forbidden() throws Exception {
    // Create a server access request with BASIC access level
    final NDSServerAccessView initialRequest =
        createServerAccessRequest(NDSServerAccess.AccessLevel.BASIC);
    final ObjectId requestId = initialRequest.getId();

    // Prepare the upgrade request
    final JSONObject upgradeRequest = new JSONObject();
    upgradeRequest.put(
        NDSServerAccessView.FieldDefs.ACCESS_LEVEL_FIELD,
        NDSServerAccess.AccessLevel.ELEVATED.name());

    // Execute the PATCH request with unauthorized user and expect forbidden
    doAuthedJsonPatch(
        groupOwner,
        "/admin/nds/serverAccess/requests/" + requestId,
        upgradeRequest,
        HttpStatus.SC_FORBIDDEN);
  }

  @Test
  public void upgradeServerAccessRequest_sameAccessLevel_success() throws Exception {
    // Create a server access request with BASIC access level
    final NDSServerAccessView initialRequest =
        createServerAccessRequest(NDSServerAccess.AccessLevel.BASIC);
    final ObjectId requestId = initialRequest.getId();

    // Prepare the upgrade request with same access level
    final JSONObject upgradeRequest = new JSONObject();
    upgradeRequest.put(
        NDSServerAccessView.FieldDefs.ACCESS_LEVEL_FIELD, NDSServerAccess.AccessLevel.BASIC.name());

    // Execute the PATCH request (should fail as it's not an upgrade)
    doAuthedJsonPatch(
        globalAtlasOperator,
        "/admin/nds/serverAccess/requests/" + requestId,
        upgradeRequest,
        HttpStatus.SC_BAD_REQUEST);
  }

  /** Helper method to create a server access request with the specified access level */
  private NDSServerAccessView createServerAccessRequest(NDSServerAccess.AccessLevel accessLevel)
      throws Exception {
    // Create a server access request using the real hostname from the infrastructure
    final String reason = "Test access request for integration test";

    // Use the service to create the initial request with the real hostname
    final NDSServerAccessView initialView =
        ndsAdminSvc.createAccessRequest(
            hostname,
            reason,
            false, // overwriteExistingHostAccess
            globalAtlasOperator,
            AuditInfoHelpers.fromSystem(),
            com.xgen.cloud.nds.mpa._public.svc.MPAAuthorizationSvc.MPAReasonScope.SINGLE_HOST);

    // If the requested access level is different from what was created, update it using the DAO
    if (accessLevel != initialView.getAccessLevel()) {
      final boolean updated =
          ndsServerAccessDao.setAccessLevel(
              group.getId(), hostname, globalAtlasOperator.getId(), accessLevel);
      if (!updated) {
        throw new IllegalStateException("Failed to set access level to " + accessLevel);
      }

      // Fetch the updated server access object from the database
      final NDSServerAccess updatedAccess =
          ndsServerAccessDao.findByRequestId(
              initialView.getId(),
              globalAtlasOperator.getId(),
              initialView.getApiUserUsername(),
              new Date());
      if (updatedAccess == null) {
        throw new IllegalStateException("Failed to fetch updated server access");
      }

      // Return a new view with the updated access level
      return new NDSServerAccessView(
          updatedAccess, initialView.isOrgRestrictedEmployeeAccess(), initialView.getAdminNote());
    }

    return initialView;
  }
}
