package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.atm.core._private.dao.AgentModuleStatusDao;
import com.xgen.cloud.atm.core._public.model.AgentModuleStatus;
import com.xgen.cloud.atm.core._public.model.AgentModuleStatus.ProcessName;
import com.xgen.cloud.atm.core._public.model.AgentModuleStatus.Status;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.atm.model.ui.AgentModuleStatusView;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import java.util.Date;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AgentModuleStatusResourceIntTests extends JUnit5BaseResourceTest {
  private AgentModuleStatusDao _statusDao;
  private Group _group;
  private AppUser _lowPermissionUser;
  private AppUser _groupAdminUser;
  private AppUser _tseUser;
  private AppUser _monitoringUser;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();

    _statusDao = AppConfig.getInstance(AgentModuleStatusDao.class);

    final Organization organization = MmsFactory.createOrganizationWithNDSPlan("Test");
    _group = MmsFactory.createGroup(organization, "cus_0001");

    _lowPermissionUser =
        MmsFactory.createUserWithRoleInGroup(_group, "<EMAIL>", Role.GROUP_READ_ONLY);
    _groupAdminUser = MmsFactory.createUserWithRoleInGroup(_group, "<EMAIL>", Role.GROUP_OWNER);
    _tseUser = MmsFactory.createGlobalAtlasTSEUser(_group);
    _monitoringUser = MmsFactory.createMonitoringAdminUser(_group);
  }

  @Test
  public void successfulFind() {
    final AgentModuleStatus seedStatus =
        new AgentModuleStatus(
            _group.getId(),
            "hostname",
            ProcessName.FTDCExportForcePush,
            Status.Completed,
            new Date());
    _statusDao.save(seedStatus);

    final String uiGetEndpoint =
        String.format(
            "/agents/moduleStatus/%s?process=%s&hostname=%s&afterTimestamp=%d",
            _group.getId(),
            seedStatus.getProcessName().toString(),
            seedStatus.getAgentHostname(),
            seedStatus.getTimestamp().getTime() - 10);
    final JSONObject response = doAuthedJsonGet(_tseUser, uiGetEndpoint, HttpStatus.SC_OK);

    assertEquals(
        _group.getId().toHexString(), response.getString(AgentModuleStatusView.GROUP_ID_FIELD));
    assertEquals(
        seedStatus.getAgentHostname(),
        response.getString(AgentModuleStatusView.AGENT_HOSTNAME_FIELD));
    assertEquals(
        Status.Completed.toString(), response.getString(AgentModuleStatusView.STATUS_FIELD));
    assertEquals(
        ProcessName.FTDCExportForcePush.toString(),
        response.getString(AgentModuleStatusView.PROCESS_FIELD));
    assertEquals(
        seedStatus.getTimestamp().getTime(),
        response.getLong(AgentModuleStatusView.TIMESTAMP_FIELD));
  }

  @Test
  public void noMatchingStatus_returnsNotFound() throws Exception {
    final AgentModuleStatus seedStatus =
        new AgentModuleStatus(
            _group.getId(),
            "hostname",
            ProcessName.FTDCExportForcePush,
            Status.Completed,
            new Date());
    _statusDao.save(seedStatus);

    final String uiGetEndpoint =
        String.format(
            "/agents/moduleStatus/%s?process=%s&hostname=%s&afterTimestamp=%d",
            _group.getId(),
            seedStatus.getProcessName().toString(),
            seedStatus.getAgentHostname(),
            seedStatus.getTimestamp().getTime() + 10);
    doAuthedHtmlGet(_tseUser, uiGetEndpoint, HttpStatus.SC_NOT_FOUND);
  }

  @Test
  public void invalidRequest_fails() {
    final AgentModuleStatus seedStatus =
        new AgentModuleStatus(
            _group.getId(),
            "hostname",
            ProcessName.FTDCExportForcePush,
            Status.Completed,
            new Date());
    _statusDao.save(seedStatus);

    /*
    Process
    */
    // Missing query param
    doAuthedHtmlGet(
        _tseUser,
        String.format(
            "/agents/moduleStatus/%s?hostname=%s&afterTimestamp=%d",
            _group.getId(),
            seedStatus.getAgentHostname(),
            seedStatus.getTimestamp().getTime() - 10),
        HttpStatus.SC_BAD_REQUEST);
    // Missing value
    doAuthedHtmlGet(
        _tseUser,
        String.format(
            "/agents/moduleStatus/%s?process=%s&hostname=%s&afterTimestamp=%d",
            _group.getId(),
            "",
            seedStatus.getAgentHostname(),
            seedStatus.getTimestamp().getTime() - 10),
        HttpStatus.SC_BAD_REQUEST);
    // Invalid value
    doAuthedHtmlGet(
        _tseUser,
        String.format(
            "/agents/moduleStatus/%s?process=%s&hostname=%s&afterTimestamp=%d",
            _group.getId(),
            "abc",
            seedStatus.getAgentHostname(),
            seedStatus.getTimestamp().getTime() - 10),
        HttpStatus.SC_BAD_REQUEST);

    /*
    Hostname
    */
    // Missing query param
    doAuthedHtmlGet(
        _tseUser,
        String.format(
            "/agents/moduleStatus/%s?process=%s&afterTimestamp=%d",
            _group.getId(), seedStatus.getProcessName(), seedStatus.getTimestamp().getTime() - 10),
        HttpStatus.SC_BAD_REQUEST);
    // Missing value
    doAuthedHtmlGet(
        _tseUser,
        String.format(
            "/agents/moduleStatus/%s?process=%s&hostname=%s&afterTimestamp=%d",
            _group.getId(),
            seedStatus.getProcessName(),
            "",
            seedStatus.getTimestamp().getTime() - 10),
        HttpStatus.SC_BAD_REQUEST);

    /*
    Timestamp
    */
    // Missing query param
    doAuthedHtmlGet(
        _tseUser,
        String.format(
            "/agents/moduleStatus/%s?process=%s&hostname=%s",
            _group.getId(), seedStatus.getProcessName(), seedStatus.getAgentHostname()),
        HttpStatus.SC_BAD_REQUEST);
    // Missing value
    doAuthedHtmlGet(
        _tseUser,
        String.format(
            "/agents/moduleStatus/%s?process=%s&hostname=%s&afterTimestamp=%d",
            _group.getId(), seedStatus.getProcessName(), seedStatus.getAgentHostname(), 0),
        HttpStatus.SC_BAD_REQUEST);
    // Invalid value
    doAuthedHtmlGet(
        _tseUser,
        String.format(
            "/agents/moduleStatus/%s?process=%s&hostname=%s&afterTimestamp=%s",
            _group.getId(), seedStatus.getProcessName(), seedStatus.getAgentHostname(), "abc"),
        HttpStatus.SC_NOT_FOUND);
  }

  @Test
  public void usersWithoutPermissions_requestFails() {

    final AgentModuleStatus seedStatus =
        new AgentModuleStatus(
            _group.getId(),
            "hostname",
            ProcessName.FTDCExportForcePush,
            Status.Completed,
            new Date());
    _statusDao.save(seedStatus);

    final String uiGetEndpoint =
        String.format(
            "/agents/moduleStatus/%s?process=%s&hostname=%s&afterTimestamp=%d",
            _group.getId(),
            seedStatus.getProcessName().toString(),
            seedStatus.getAgentHostname(),
            seedStatus.getTimestamp().getTime() - 10);
    doAuthedHtmlGet(_lowPermissionUser, uiGetEndpoint, HttpStatus.SC_FORBIDDEN);
    doAuthedHtmlGet(_groupAdminUser, uiGetEndpoint, HttpStatus.SC_FORBIDDEN);
    doAuthedJsonGet(_monitoringUser, uiGetEndpoint, HttpStatus.SC_OK);
    doAuthedJsonGet(_tseUser, uiGetEndpoint, HttpStatus.SC_OK);
  }
}
