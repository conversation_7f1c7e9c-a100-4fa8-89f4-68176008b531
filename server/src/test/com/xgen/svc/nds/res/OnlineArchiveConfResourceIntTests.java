package com.xgen.svc.nds.res;

import static com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveHistory.partitionTypeStatsToMap;
import static java.time.temporal.ChronoUnit.HOURS;
import static java.time.temporal.ChronoUnit.SECONDS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveHistoryDao;
import com.xgen.cloud.nds.onlinearchive._private.settings.OnlineArchiveSettings;
import com.xgen.cloud.nds.onlinearchive._public.model.ArchiveRunStats;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.ArchiveRun;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.ArchiveRunState;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.ArchiveSettingOverrides;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.ArchiveWindow;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.CollectionType;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DataProcessRegion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DateCriteria.DateFormat;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.State;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveHistory;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveSchedule.DailySchedule;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.organization._public.model.OrgPaymentStatus;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.onlinearchiveagentview.GetJobErrorsResponseView;
import com.xgen.svc.nds.model.onlinearchiveagentview.GetJobProgressResponseView;
import com.xgen.svc.nds.model.ui.OnlineArchiveBaseView;
import com.xgen.svc.nds.model.ui.dataLake.OnlineArchiveNewOperationResponseView.OperationType;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveTestUtils;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.UUID;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class OnlineArchiveConfResourceIntTests extends JUnit5BaseResourceTest {
  private static final String ERROR_CODE_FIELD = "errorCode";
  private static final String BASE_URL = "/conf/onlinearchive";
  private static final String CONFIGURE_URL = BASE_URL + "/%s/cluster/%s?ah=%s&sk=%s&av=%s";
  private static final String LAST_RUN_STATS_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/lastRunStats?ah=%s&sk=%s";
  private static final String REQUEST_NEW_OPERATION_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/newOperation?ah=%s&sk=%s";
  private static final String GET_CREDENTIALS_URL =
      BASE_URL + "/%s/cluster/%s/tempCredentials?ah=%s&sk=%s";
  private static final String GET_DLZ_BUCKET_CREDENTIALS_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/dataLandingZoneBucketCredentials?ah=%s&sk=%s";
  private static final String PAUSE_OA_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/pauseOA?ah=%s&sk=%s&av=%s";
  private static final String SET_VERSION_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/setVersion?ah=%s&sk=%s&version=%s";
  private static final String GET_JOB_PROGRESS_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/jobId/%s/getJobProgress?ah=%s&sk=%s";

  private static final String GET_JOB_ERRORS_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/jobId/%s/getJobErrors?ah=%s&sk=%s";
  private static final String REGISTER_OA_FILES_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/registerOAFiles?ah=%s&sk=%s";
  private static final String MARK_OA_PAUSED_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/markOAPaused?ah=%s&sk=%s&av=%s";
  private static final String UPLOAD_DATA_FILES_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/uploadDataFiles?ah=%s&sk=%s";
  private static final String GET_ARCHIVE_RUN_INFO_URL =
      BASE_URL + "/%s/cluster/%s/archive/%s/getArchiveRunInfo?ah=%s&sk=%s";

  private static final String CLUSTER_NAME_AWS = "awsTestCluster";
  private static final String CLUSTER_NAME_AZURE = "azureTestCluster";
  private static final String CLUSTER_NAME_GCP = "gcpTestCluster";
  private static final String HOSTNAME = "hostname";
  private static final String SESSION_KEY1 = "sk1";
  private static final String SESSION_KEY2 = "sk2";
  private static final String AGENT_VERSION = "1.0";

  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private OnlineArchiveSvc _onlineArchiveSvc;
  @Inject private OnlineArchiveHistoryDao _onlineArchiveHistoryDao;
  @Inject private OnlineArchiveDao _onlineArchiveDao;
  @Inject private OnlineArchiveTestUtils _onlineArchiveTestUtils;
  @Inject private AutomationConfigPublishingSvc _automationConfigSvc;
  @Inject private OrganizationSvc _orgSvc;
  @Inject private DataLakeTestUtils _dataLakeTestUtils;
  @Inject private OnlineArchiveSettings _onlineArchiveSettings;

  private Group _group;
  private AgentApiKey _agentApiKey;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _agentApiKey = MmsFactory.generateApiKey(_group.getId(), new ObjectId());
    final Organization org = _orgSvc.findById(_group.getOrgId());

    _ndsGroupSvc.create(_group.getId(), new NDSManagedX509(), false);
    final BasicDBObject awsClusterDescription =
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), CLUSTER_NAME_AWS);
    awsClusterDescription.append(ClusterDescription.FieldDefs.UNIQUE_ID, ObjectId.get());
    _clusterDescriptionDao.saveReplicaSafe(awsClusterDescription);

    final BasicDBObject azureClusterDescription =
        NDSModelTestFactory.getAzureClusterDescription(_group.getId(), CLUSTER_NAME_AZURE);
    azureClusterDescription.append(ClusterDescription.FieldDefs.UNIQUE_ID, ObjectId.get());
    _clusterDescriptionDao.saveReplicaSafe(azureClusterDescription);

    final BasicDBObject gcpClusterDescription =
        NDSModelTestFactory.getGCPClusterDescription(_group.getId(), CLUSTER_NAME_GCP);
    gcpClusterDescription.append(ClusterDescription.FieldDefs.UNIQUE_ID, ObjectId.get());
    _clusterDescriptionDao.saveReplicaSafe(gcpClusterDescription);

    final AppUser appUser = MmsFactory.createGroupOwnerUser(_group);

    final AutomationConfig automationConfig =
        _automationConfigSvc.findDraftOrEmpty(_group.getId(), appUser.getId());
    final Auth auth = automationConfig.getDeployment().getAuth();
    auth.setAutoUser("username");
    auth.setAutoPwd("password");
    _automationConfigSvc.saveDraft(automationConfig, appUser, org, _group);
    _automationConfigSvc.publish(org, _group, appUser);
    _dataLakeTestUtils.setUp();
  }

  @AfterEach
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testConfigure() throws Exception {
    FeatureFlagIntTestUtil.enableFeatureForEntity(_group, null, FeatureFlag.ONLINE_ARCHIVE_GCP);

    _onlineArchiveTestUtils.setUpAzureTestStorageAccount(_group);
    // Conf call with no OnlineArchiveDataLakeConfig
    // onlineArchiveConfig is null in the response
    {
      final JSONObject awsConfCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(awsConfCallResult.has("onlineArchiveConfig"));
      assertTrue(awsConfCallResult.isNull("onlineArchiveConfig"));

      final JSONObject azureConfCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AZURE,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(azureConfCallResult.has("onlineArchiveConfig"));
      assertTrue(azureConfCallResult.isNull("onlineArchiveConfig"));

      final JSONObject gcpConfCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_GCP,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(gcpConfCallResult.has("onlineArchiveConfig"));
      assertTrue(gcpConfCallResult.isNull("onlineArchiveConfig"));
    }

    final Date lastRunStart = Date.from(Instant.now().minus(21, HOURS));
    final Date lastRunEnd = Date.from(Instant.now().minus(20, HOURS));
    final ArchiveRun lastArchiveRun =
        ArchiveRun.builder()
            .startDate(lastRunStart)
            .endDate(lastRunEnd)
            .errorMessage("failed")
            .build();
    final Date currentRunStart = Date.from(Instant.now().minus(10, SECONDS));
    final ArchiveRun currentArchiveRun =
        ArchiveRun.builder().jobId("jobId-3456").startDate(currentRunStart).build();

    // Create online archives
    final OnlineArchive[] onlineArchives = {
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
          .setLastArchiveRun(lastArchiveRun)
          .setCurrentArchiveRun(currentArchiveRun)
          .build(),
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
          .setCollName("mycoll2")
          .setCollectionUUID(UUID.randomUUID())
          .setLastArchiveRun(lastArchiveRun)
          .build(),
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
          .setCollName("mycoll3")
          .build(),
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
          .setCollName("mycoll4")
          .build(),
      // test archive state, schedule, currentRun, window, dataProcessRegion, settings, and
      // dataLandingZoneConfig
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
          .setCollName("mycoll5")
          .setCriteria(new OnlineArchive.CustomCriteria("{\"field\": \"value\"}"))
          .setSchedule(new DailySchedule(1, 30, 3, 40))
          .setCurrentArchiveRun(currentArchiveRun)
          .setCurrentArchiveWindow(new ArchiveWindow(new Date(), new Date(), 10, 2))
          .setDataProcessRegion(new DataProcessRegion("AWS", "US_EAST_1"))
          .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
          .setDataSizeThresholdStartDate(DateUtils.addDays(new Date(), -1))
          .setArchiveSettingOverrides(
              new ArchiveSettingOverrides("snappy", 1, 3, 1000, 102400L, true, false))
          .build(),
      // test archive settings with null ArchiveSettingOverrides
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
          .setCollName("mycoll6")
          .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
          .setArchiveSettingOverrides(null)
          .build(),
      // test archive settings with ArchiveSettingOverrides contents are null
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
          .setCollName("mycoll7")
          .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
          .setArchiveSettingOverrides(
              new ArchiveSettingOverrides(null, null, null, null, null, null, null))
          .build(),
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
          .setCollName("mycoll8")
          .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
          .setDataProcessRegion(
              new DataProcessRegion(CloudProvider.PROVIDER_AWS, AWSRegionName.US_EAST_1.name()))
          .build(),
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AZURE).toDBObject())
          .setCollName("mycoll9")
          .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
          .setDataProcessRegion(
              new DataProcessRegion(CloudProvider.PROVIDER_AZURE, AzureRegionName.US_EAST_2.name()))
          .build(),
      new OnlineArchive.Builder(getOnlineArchive(CloudProvider.GCP).toDBObject())
          .setCollName("mycoll10")
          .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
          .setDataProcessRegion(
              new DataProcessRegion(CloudProvider.PROVIDER_GCP, GCPRegionName.CENTRAL_US.name()))
          .build(),
    };

    _onlineArchiveSvc.create(onlineArchives[0], AuditInfoHelpers.fromSystem());

    _onlineArchiveSvc.create(onlineArchives[1], AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(onlineArchives[1].getId(), AuditInfoHelpers.fromSystem());

    _onlineArchiveSvc.create(onlineArchives[2], AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.setPauseCompleted(onlineArchives[2].getId(), AuditInfoHelpers.fromSystem());

    _onlineArchiveSvc.create(onlineArchives[3], AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.markForDeletion(
        onlineArchives[3].getId(), AuditInfoHelpers.fromSystem(), _group.getId());

    _onlineArchiveSvc.create(onlineArchives[4], AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.create(onlineArchives[5], AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.create(onlineArchives[6], AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.create(onlineArchives[7], AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.create(onlineArchives[8], AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.create(onlineArchives[9], AuditInfoHelpers.fromSystem());

    assertEquals(
        8, _onlineArchiveSvc.getArchivesForCluster(_group.getId(), CLUSTER_NAME_AWS).size());
    assertEquals(
        1, _onlineArchiveSvc.getArchivesForCluster(_group.getId(), CLUSTER_NAME_AZURE).size());
    assertEquals(
        1, _onlineArchiveSvc.getArchivesForCluster(_group.getId(), CLUSTER_NAME_GCP).size());

    // Conf call after creating online archives made using SESSION_KEY1
    // onlineArchiveConfig is returned with archives
    {
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
      assertFalse(confCallResult.isNull("onlineArchiveConfig"));

      // onlineArchiveConfig object properties
      final JSONObject onlineArchiveConfig = confCallResult.getJSONObject("onlineArchiveConfig");
      assertTrue(onlineArchiveConfig.has("archives"));
      assertTrue(onlineArchiveConfig.has("clusterId"));
      assertTrue(onlineArchiveConfig.getJSONObject("clusterId").has("groupId"));
      assertTrue(onlineArchiveConfig.getJSONObject("clusterId").has("clusterName"));
      assertTrue(onlineArchiveConfig.has("s3Bucket"));
      assertTrue(onlineArchiveConfig.has("s3BucketRegion"));
      assertTrue(onlineArchiveConfig.has("uniqueClusterId"));

      // only ACTIVE and PENDING archives returned
      final JSONArray archives = onlineArchiveConfig.getJSONArray("archives");
      assertEquals(7, archives.length());
      final JSONObject archive0 = archives.getJSONObject(0);
      final JSONObject archive1 = archives.getJSONObject(1);
      final JSONObject archive2 = archives.getJSONObject(2);
      final JSONObject archive3 = archives.getJSONObject(3);
      final JSONObject archive4 = archives.getJSONObject(4);
      final JSONObject archive5 = archives.getJSONObject(5);
      final JSONObject archive6 = archives.getJSONObject(6);

      assertTrue(
          archive0.getString("archiveState").equals(OnlineArchive.State.ACTIVE.name())
              || archive0.getString("archiveState").equals(OnlineArchive.State.PENDING.name()));
      assertTrue(
          archive1.getString("archiveState").equals(OnlineArchive.State.ACTIVE.name())
              || archive1.getString("archiveState").equals(OnlineArchive.State.PENDING.name()));
      assertTrue(
          archive3.getString("archiveState").equals(OnlineArchive.State.ACTIVE.name())
              || archive3.getString("archiveState").equals(OnlineArchive.State.PENDING.name()));

      // archive object properties
      assertTrue(archive0.has("_id"));
      assertTrue(archive0.has("dbName"));
      assertTrue(archive0.has("collName"));
      assertTrue(archive0.has("collectionUUID"));
      assertTrue(archive0.has("archiveState"));
      assertTrue(archive0.has("partitionFields"));
      assertTrue(archive0.has("archiveRunFrequencyInSeconds"));
      assertTrue(archive0.has("criteria"));
      assertTrue(archive0.has("schedule"));
      assertTrue(archive0.has("archiveS3Path"));
      assertTrue(archive0.has("hasSufficientIndexes"));
      assertTrue(archive0.has("dataProcessRegion"));
      assertTrue(archive0.has("archiveLevelSettings"));
      assertTrue(archive0.has("dataLandingZoneConfig"));

      // collection type archive object properties
      assertTrue(archive0.has("collectionType"));
      assertEquals(CollectionType.STANDARD.name(), archive0.getString("collectionType"));

      assertTrue(archive0.has("onlineArchiveVersion"));
      assertEquals(OnlineArchiveVersion.V1.name(), archive0.getString("onlineArchiveVersion"));

      assertTrue(archive0.has("ratioOfPartitionsToFilesThreshold"));
      assertEquals(
          _onlineArchiveSettings.getOnlineArchivePartitionsToFilesThreshold(),
          archive0.getDouble("ratioOfPartitionsToFilesThreshold"),
          0);

      assertTrue(archive0.has("averageFileSizeThreshold"));
      assertEquals(
          _onlineArchiveSettings.getOnlineArchiveAvgFileSizeThreshold(),
          archive0.getInt("averageFileSizeThreshold"));

      assertTrue(archive0.has("v2UpgradeEnabled"));
      assertFalse(archive0.getBoolean("v2UpgradeEnabled"));

      final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX");
      assertTrue(archive0.has("lastArchiveRun"));
      final JSONObject lastArchiveRunObj = archive0.getJSONObject("lastArchiveRun");
      assertTrue(lastArchiveRunObj.has("jobId"));
      assertTrue(lastArchiveRunObj.isNull("jobId"));
      assertTrue(lastArchiveRunObj.has("startDate"));
      assertEquals(
          sdf.format(onlineArchives[0].getLastArchiveRun().getStartDate()),
          lastArchiveRunObj.getString("startDate"));
      assertTrue(lastArchiveRunObj.has("endDate"));
      assertEquals(
          sdf.format(onlineArchives[0].getLastArchiveRun().getEndDate()),
          lastArchiveRunObj.getString("endDate"));
      assertTrue(lastArchiveRunObj.has("errorMessage"));
      assertEquals(
          onlineArchives[0].getLastArchiveRun().getErrorMessage(),
          lastArchiveRunObj.getString("errorMessage"));
      assertTrue(lastArchiveRunObj.has("numFrsJobErrors"));
      assertTrue(lastArchiveRunObj.isNull("numFrsJobErrors"));

      assertTrue(archive0.has("currentArchiveRun"));
      final JSONObject currentArchiveRunObj = archive0.getJSONObject("currentArchiveRun");
      assertTrue(currentArchiveRunObj.has("jobId"));
      assertEquals(
          onlineArchives[0].getCurrentArchiveRun().orElseThrow().getJobId(),
          currentArchiveRunObj.getString("jobId"));
      assertTrue(currentArchiveRunObj.has("startDate"));
      assertEquals(
          sdf.format(onlineArchives[0].getCurrentArchiveRun().orElseThrow().getStartDate()),
          currentArchiveRunObj.getString("startDate"));
      assertTrue(currentArchiveRunObj.has("endDate"));
      assertTrue(currentArchiveRunObj.isNull("endDate"));
      assertTrue(currentArchiveRunObj.has("errorMessage"));
      assertTrue(currentArchiveRunObj.isNull("errorMessage"));

      assertTrue(archive1.has("lastArchiveRun"));
      assertFalse(archive1.isNull("lastArchiveRun"));
      assertTrue(archive1.has("currentArchiveRun"));
      assertTrue(archive1.isNull("currentArchiveRun"));

      assertEquals(State.PAUSED.name(), archive2.getString("archiveState"));

      assertTrue(archive3.has("lastArchiveRun"));
      assertTrue(archive3.isNull("lastArchiveRun"));
      assertTrue(archive3.has("currentArchiveRun"));
      assertFalse(archive3.isNull("currentArchiveRun"));

      // custom criteria archive object properties
      assertTrue(archive3.has("criteria"));
      assertTrue(archive3.getJSONObject("criteria").has("query"));

      // Archive schedule
      assertTrue(archive3.has("schedule"));
      final JSONObject scheduleObj = archive3.getJSONObject("schedule");
      final DailySchedule dailySchedule = (DailySchedule) onlineArchives[4].getSchedule();
      assertEquals(dailySchedule.getType().name(), scheduleObj.getString("type"));
      assertEquals(dailySchedule.getStartHour(), scheduleObj.getInt("startHour"));
      assertEquals(dailySchedule.getStartMinute(), scheduleObj.getInt("startMinute"));
      assertEquals(dailySchedule.getEndHour(), scheduleObj.getInt("endHour"));
      assertEquals(dailySchedule.getEndMinute(), scheduleObj.getInt("endMinute"));

      // data process region
      assertTrue(archive3.has("dataProcessRegion"));
      final JSONObject dataProcessRegionObj = archive3.getJSONObject("dataProcessRegion");
      assertEquals("us-east-1", dataProcessRegionObj.getString("region"));
      assertEquals("aws", dataProcessRegionObj.getString("cloudProvider"));

      // archive settings
      assertTrue(archive3.has("archiveLevelSettings"));
      assertTrue(archive4.has("archiveLevelSettings"));
      assertTrue(archive5.has("archiveLevelSettings"));
      final JSONObject[] archiveLevelSettingsObjs = {
        archive3.getJSONObject("archiveLevelSettings"),
        archive4.getJSONObject("archiveLevelSettings"),
        archive5.getJSONObject("archiveLevelSettings")
      };

      assertEquals("snappy", archiveLevelSettingsObjs[0].getString("fileCompressionType"));
      assertEquals(1, archiveLevelSettingsObjs[0].getInt("numDeleteDocumentWorkers"));
      assertEquals(3, archiveLevelSettingsObjs[0].getInt("numReadFileWorkers"));
      assertEquals(1000, archiveLevelSettingsObjs[0].getInt("deleteBatchSize"));
      assertEquals(102400L, archiveLevelSettingsObjs[0].getLong("dataSizeThreshold"));
      assertTrue(archiveLevelSettingsObjs[0].getBoolean("disablePurgingWithShardKeys"));
      assertFalse(archiveLevelSettingsObjs[0].getBoolean("disablePurgingCountCheck"));

      // archive settings with null ArchiveSettingOverrides
      assertTrue((archiveLevelSettingsObjs[1].isNull("fileCompressionType")));
      assertEquals(1, archiveLevelSettingsObjs[1].getInt("numDeleteDocumentWorkers"));
      assertEquals(1, archiveLevelSettingsObjs[1].getInt("numReadFileWorkers"));
      assertTrue(archiveLevelSettingsObjs[1].isNull("deleteBatchSize"));
      assertEquals(0L, archiveLevelSettingsObjs[1].getLong("dataSizeThreshold"));
      // Default values are false for these two fields.
      assertFalse(archiveLevelSettingsObjs[1].getBoolean("disablePurgingWithShardKeys"));
      assertFalse(archiveLevelSettingsObjs[1].getBoolean("disablePurgingCountCheck"));

      assertTrue((archiveLevelSettingsObjs[2].isNull("fileCompressionType")));
      assertEquals(1, archiveLevelSettingsObjs[2].getInt("numDeleteDocumentWorkers"));
      assertEquals(1, archiveLevelSettingsObjs[2].getInt("numReadFileWorkers"));
      assertTrue(archiveLevelSettingsObjs[2].isNull("deleteBatchSize"));
      assertEquals(0L, archiveLevelSettingsObjs[2].getLong("dataSizeThreshold"));
      assertFalse(archiveLevelSettingsObjs[2].getBoolean("disablePurgingWithShardKeys"));
      assertFalse(archiveLevelSettingsObjs[2].getBoolean("disablePurgingCountCheck"));

      // current archive window
      assertTrue(archive3.has("currentArchiveWindow"));
      final JSONObject currentArchiveWindowObj = archive3.getJSONObject("currentArchiveWindow");
      final ArchiveWindow currentArchiveWindow =
          onlineArchives[4].getCurrentArchiveWindow().orElseThrow();
      assertTrue(currentArchiveWindowObj.has("startDate"));
      assertEquals(
          sdf.format(currentArchiveWindow.getStartDate()),
          currentArchiveWindowObj.get("startDate"));

      assertTrue(currentArchiveWindowObj.has("endDate"));
      assertEquals(
          sdf.format(currentArchiveWindow.getEndDate()),
          currentArchiveWindowObj.getString("endDate"));

      assertTrue(currentArchiveWindowObj.has("numArchiveRuns"));
      assertEquals(
          currentArchiveWindow.getNumArchiveRuns(),
          currentArchiveWindowObj.getInt("numArchiveRuns"));
      assertTrue(currentArchiveWindowObj.has("numMaxOffloads"));
      assertEquals(
          currentArchiveWindow.getNumMaxOffloads(),
          currentArchiveWindowObj.getInt("numMaxOffloads"));

      // check clusterMongoSettings

      final JSONObject clusterMongoSettings =
          onlineArchiveConfig.getJSONObject("clusterMongoSettings");
      assertNotNull(clusterMongoSettings);
      final String mongoUri = clusterMongoSettings.getString("mongoUri");
      assertNotNull(mongoUri);

      // aws dataLandingZoneConfig
      assertTrue(archive6.has("dataLandingZoneConfig"));
      final JSONObject dataLandingZoneConfigObj0 = archive6.getJSONObject("dataLandingZoneConfig");
      assertEquals("us-east-1", dataLandingZoneConfigObj0.getString("region"));
      assertEquals("aws", dataLandingZoneConfigObj0.getString("cloudProvider"));
      assertEquals(
          String.format("%s/%s", _group.getId(), onlineArchives[7].getId()),
          dataLandingZoneConfigObj0.get("pathPrefix"));
      assertEquals(
          "atlas-online-archive-data-landing-zone-test-us-east-1",
          dataLandingZoneConfigObj0.get("dataLandingZoneBucketName"));
    }

    {
      // testing azure oa
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AZURE,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
      assertFalse(confCallResult.isNull("onlineArchiveConfig"));

      // onlineArchiveConfig object properties
      final JSONObject onlineArchiveConfig = confCallResult.getJSONObject("onlineArchiveConfig");
      assertTrue(onlineArchiveConfig.has("archives"));
      assertTrue(onlineArchiveConfig.has("clusterId"));
      assertTrue(onlineArchiveConfig.getJSONObject("clusterId").has("groupId"));
      assertTrue(onlineArchiveConfig.getJSONObject("clusterId").has("clusterName"));

      // only ACTIVE and PENDING archives returned
      final JSONArray archives = onlineArchiveConfig.getJSONArray("archives");
      assertEquals(1, archives.length());
      final JSONObject archive = archives.getJSONObject(0);

      // azure dataLandingZoneConfig
      assertTrue(archive.has("dataLandingZoneConfig"));
      final JSONObject dataLandingZoneConfigObj0 = archive.getJSONObject("dataLandingZoneConfig");
      assertEquals("eastus2", dataLandingZoneConfigObj0.getString("region"));
      assertEquals("azure", dataLandingZoneConfigObj0.getString("cloudProvider"));
      assertEquals("", dataLandingZoneConfigObj0.get("pathPrefix"));
      assertEquals(
          String.format("%s-%s", _group.getId(), onlineArchives[8].getId()),
          dataLandingZoneConfigObj0.get("containerName"));
      assertEquals("mdbdlzoaeaus2l", dataLandingZoneConfigObj0.get("storageAccountName"));
    }

    {
      // testing GCP oa
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_GCP,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
      assertFalse(confCallResult.isNull("onlineArchiveConfig"));

      // onlineArchiveConfig object properties
      final JSONObject onlineArchiveConfig = confCallResult.getJSONObject("onlineArchiveConfig");
      assertTrue(onlineArchiveConfig.has("archives"));
      assertTrue(onlineArchiveConfig.has("clusterId"));
      assertTrue(onlineArchiveConfig.getJSONObject("clusterId").has("groupId"));
      assertTrue(onlineArchiveConfig.getJSONObject("clusterId").has("clusterName"));

      // only ACTIVE and PENDING archives returned
      final JSONArray archives = onlineArchiveConfig.getJSONArray("archives");
      assertEquals(1, archives.length());
      final JSONObject archive = archives.getJSONObject(0);

      // gcp dataLandingZoneConfig

      final JSONObject dataLandingZoneConfigObj0 = archive.getJSONObject("dataLandingZoneConfig");
      assertEquals("us-central1", dataLandingZoneConfigObj0.getString("region"));
      assertEquals("gcp", dataLandingZoneConfigObj0.getString("cloudProvider"));
      assertEquals(
          String.format("%s/%s", _group.getId(), onlineArchives[9].getId()),
          dataLandingZoneConfigObj0.get("pathPrefix"));
      assertEquals(
          "atlas-online-archive-dlz-test-us-central1",
          dataLandingZoneConfigObj0.get("dataLandingZoneBucketName"));
      assertTrue(archive.has("dataLandingZoneConfig"));
    }

    // Conf call made using SESSION_KEY2 after call made with SESSION_KEY1
    // onlineArchiveConfig is not returned - lease is held by SESSION_KEY1
    {
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY2,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
      assertTrue(confCallResult.isNull("onlineArchiveConfig"));
    }
  }

  @Test
  public void testConfigure_orgIsDelinquent() throws Exception {

    // Create online archives
    final OnlineArchive archive1 =
        new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject()).build();

    final OnlineArchive archive2 =
        new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
            .setCollName("mycoll2")
            .setCollectionUUID(UUID.randomUUID())
            .build();

    _onlineArchiveSvc.create(archive1, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.create(archive2, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(archive1.getId(), AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(archive2.getId(), AuditInfoHelpers.fromSystem());

    // Test before embargo org
    {
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
      assertFalse(confCallResult.isNull("onlineArchiveConfig"));

      // Make conf call
      final JSONObject onlineArchiveConfig = confCallResult.getJSONObject("onlineArchiveConfig");
      assertTrue(onlineArchiveConfig.has("archives"));

      // Should return two archives
      assertEquals(2, onlineArchiveConfig.getJSONArray("archives").length());
    }

    // embargo org
    final Organization organization = _orgSvc.findById(_group.getOrgId());
    _orgSvc.updateOrgPaymentStatus(organization, OrgPaymentStatus.embargoed(new Date()));

    // Test after embargo org
    {
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
      // should be null because org is embargoed
      assertTrue(confCallResult.isNull("onlineArchiveConfig"));
    }
  }

  @Test
  public void testConfigure_hasError() throws Exception {
    // create online archive
    final OnlineArchive archive = getOnlineArchive(CloudProvider.AWS);
    _onlineArchiveSvc.create(archive, AuditInfoHelpers.fromSystem());

    final JSONObject error_notFound =
        new JSONObject().put("code", "COLLECTION_NOT_FOUND").put("message", "Collection not found");
    final JSONObject resolutions_notFound =
        new JSONObject()
            .put(archive.getId().toString(), new JSONObject().put("error", error_notFound));

    {
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", resolutions_notFound)));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
    }

    final JSONObject error_fieldType =
        new JSONObject()
            .put("code", "FIELD_TYPE_UNRESOLVABLE")
            .put("message", "Could not resolve type for field");
    final JSONObject resolutions_fieldType =
        new JSONObject()
            .put(archive.getId().toString(), new JSONObject().put("error", error_fieldType));

    {
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", resolutions_fieldType)));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
    }

    final JSONObject error_general =
        new JSONObject().put("code", "GENERAL_ERROR").put("message", "Something went wrong");
    final JSONObject resolutions_general =
        new JSONObject()
            .put(archive.getId().toString(), new JSONObject().put("error", error_general));

    {
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", resolutions_general)));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
    }
  }

  @Test
  public void testSetOnlineArchiveVersion() throws Exception {

    // Create online archive
    final OnlineArchive onlineArchive =
        getOnlineArchive(CloudProvider.AWS, OnlineArchiveVersion.V2);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    final OnlineArchive onlineArchivePreRequest =
        _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

    final String newVersion = OnlineArchiveVersion.V1.name();

    // request by a non-primary agent
    final JSONObject response =
        new JSONObject(
            doAgentApiCallPostWithStatus(
                _group,
                _agentApiKey,
                String.format(
                    SET_VERSION_URL,
                    _group.getId(),
                    CLUSTER_NAME_AWS,
                    onlineArchivePreRequest.getId(),
                    HOSTNAME,
                    SESSION_KEY1,
                    newVersion),
                new byte[] {},
                HttpStatus.SC_BAD_REQUEST));

    assertEquals(
        "NON_PRIMARY_CANNOT_MAKE_ARCHIVE_OPERATION_REQUEST", response.getString("errorCode"));

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // Set new version
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            SET_VERSION_URL,
            _group.getId(),
            CLUSTER_NAME_AWS,
            onlineArchivePreRequest.getId(),
            HOSTNAME,
            SESSION_KEY1,
            newVersion),
        new byte[] {});

    // Fetch OA version after update
    {
      final JSONObject confCallResult =
          new JSONObject(
              doAgentApiCallPost(
                  _group,
                  _agentApiKey,
                  String.format(
                      CONFIGURE_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY1,
                      AGENT_VERSION),
                  new JSONObject().put("archiveResolutions", new JSONObject())));
      assertTrue(confCallResult.has("onlineArchiveConfig"));
      assertFalse(confCallResult.isNull("onlineArchiveConfig"));

      // onlineArchiveConfig object properties
      final JSONObject onlineArchiveConfig = confCallResult.getJSONObject("onlineArchiveConfig");
      assertTrue(onlineArchiveConfig.has("archives"));

      final JSONArray archives = onlineArchiveConfig.getJSONArray("archives");
      assertEquals(1, archives.length());
      final JSONObject archive0 = archives.getJSONObject(0);

      assertTrue(archive0.has("onlineArchiveVersion"));
      assertEquals(newVersion, archive0.getString("onlineArchiveVersion"));
    }

    // Set an invalid version
    final List<String> invalidVersions =
        Arrays.asList(
            OnlineArchiveVersion.UNKNOWN.name(), // Invalid transition: V2 -> UNKNOWN
            "someRandomString",
            "",
            null);
    for (final String version : invalidVersions) {
      doAgentApiCallPostWithStatus(
          _group,
          _agentApiKey,
          String.format(
              SET_VERSION_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchivePreRequest.getId(),
              HOSTNAME,
              SESSION_KEY1,
              version),
          new byte[] {},
          HttpStatus.SC_BAD_REQUEST);
    }
  }

  /** tests the new operation endpoint in mms */
  @Test
  public void testNewOperation() throws Exception {
    final OnlineArchive onlineArchive =
        getOnlineArchive(CloudProvider.AWS, OnlineArchiveVersion.V3);

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    String jobId = ObjectId.get().toString();

    final JSONObject request =
        new JSONObject().put("startDate", sdf.format(new Date())).put("jobId", jobId);

    final JSONObject jsonResponse1 =
        new JSONObject(
            doAgentApiCallPost(
                _group,
                _agentApiKey,
                String.format(
                    REQUEST_NEW_OPERATION_URL,
                    _group.getId(),
                    CLUSTER_NAME_AWS,
                    onlineArchive.getId(),
                    HOSTNAME,
                    SESSION_KEY1),
                request));

    // verify operation type.
    assertEquals(OperationType.NEW.name(), jsonResponse1.getString("operationType"));

    JSONObject currentArchiveRunJson1 = jsonResponse1.getJSONObject("currentArchiveRun");
    // verify state.
    assertEquals(ArchiveRunState.PREPARING_DLZ.name(), currentArchiveRunJson1.getString("state"));

    // verify stats.
    JSONObject stats1 = currentArchiveRunJson1.getJSONObject("stats");
    assertEquals(0, stats1.getInt("numFiles"));
    assertEquals(0, stats1.getInt("numPuts"));
    assertEquals(0, stats1.getInt("numDocuments"));
    assertEquals(0L, stats1.getLong("fileSize"));
    assertEquals(0L, stats1.getLong("dataSize"));
    assertEquals(0L, stats1.getLong("archiveQueryElapsedTimeMs"));
    assertEquals(0L, stats1.getLong("createPartitionsElapsedTimeMs"));
    assertEquals(0L, stats1.getLong("uploadFilesElapsedTimeMs"));
    assertEquals(0L, stats1.getLong("uploadFilesDLSRequestElapsedTimeMs"));
    assertEquals(0L, stats1.getLong("frsJobPollingElapsedTimeMs"));
    assertEquals(0L, stats1.getLong("cleanUpArchiveElapsedTimeMs"));
    assertEquals(0L, stats1.getLong("totalElapsedTimeMs"));
    assertEquals(1, stats1.getInt("numDeleteDocumentWorkers"));

    final ArchiveRun currentArchiveRun =
        ArchiveRun.builder()
            .jobId(jobId)
            .startDate(new Date())
            .state(ArchiveRunState.PURGING)
            .stats(
                ArchiveRunStats.builder()
                    .numFiles(10)
                    .numPuts(15)
                    .numDocuments(20)
                    .fileSize(25)
                    .dataSize(30)
                    .archiveQueryElapsedTimeMs(35)
                    .createPartitionsElapsedTimeMs(40)
                    .uploadFilesElapsedTimeMs(45)
                    .uploadFilesDLSRequestElapsedTimeMs(50)
                    .frsJobPollingElapsedTimeMs(55)
                    .cleanUpArchiveElapsedTimeMs(60)
                    .totalElapsedTimeMs(
                        65) // purging state would not have value for total elapsed time, this value
                    // is only set for testing purpose
                    .numDeleteDocumentWorkers(1)
                    .build())
            .build();

    _onlineArchiveDao.updateCurrentArchiveRun(onlineArchive.getId(), currentArchiveRun);

    final JSONObject jsonResponse2 =
        new JSONObject(
            doAgentApiCallPost(
                _group,
                _agentApiKey,
                String.format(
                    REQUEST_NEW_OPERATION_URL,
                    _group.getId(),
                    CLUSTER_NAME_AWS,
                    onlineArchive.getId(),
                    HOSTNAME,
                    SESSION_KEY1),
                request));

    // verify operation type.
    assertEquals(OperationType.EXISTING.name(), jsonResponse2.getString("operationType"));

    JSONObject currentArchiveRunJson2 = jsonResponse2.getJSONObject("currentArchiveRun");
    // verify state.
    assertEquals(ArchiveRunState.PURGING.name(), currentArchiveRunJson2.getString("state"));

    // verify stats.
    JSONObject stats2 = currentArchiveRunJson2.getJSONObject("stats");
    assertEquals(10, stats2.getInt("numFiles"));
    assertEquals(15, stats2.getInt("numPuts"));
    assertEquals(20, stats2.getInt("numDocuments"));
    assertEquals(25L, stats2.getLong("fileSize"));
    assertEquals(30L, stats2.getLong("dataSize"));
    assertEquals(35L, stats2.getLong("archiveQueryElapsedTimeMs"));
    assertEquals(40L, stats2.getLong("createPartitionsElapsedTimeMs"));
    assertEquals(45L, stats2.getLong("uploadFilesElapsedTimeMs"));
    assertEquals(50L, stats2.getLong("uploadFilesDLSRequestElapsedTimeMs"));
    assertEquals(55L, stats2.getLong("frsJobPollingElapsedTimeMs"));
    assertEquals(60L, stats2.getLong("cleanUpArchiveElapsedTimeMs"));
    assertEquals(
        65L,
        stats2.getLong(
            "totalElapsedTimeMs")); // purging state would not have value for total elapsed time,
    // this value is only set for testing purpose
    assertEquals(1, stats2.getInt("numDeleteDocumentWorkers"));
  }

  @Test
  public void testLastRunStats() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(CloudProvider.AWS);

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // Initially
    // lastArchiveRun not set
    // numConsecutiveMaxOffloads is zero
    // no OnlineArchiveHistory exists for this archive
    // onlineArchivePostCreate.isHasSufficientIndexes() = Optional.empty() (default)
    final OnlineArchive onlineArchivePostCreate =
        _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());
    assertNull(onlineArchivePostCreate.getLastArchiveRun());
    assertEquals(0, onlineArchivePostCreate.getNumConsecutiveMaxOffloads());
    assertTrue(_onlineArchiveHistoryDao.find(onlineArchivePostCreate.getId()).isEmpty());
    assertTrue(onlineArchivePostCreate.isHasSufficientIndexes().isEmpty());
    assertTrue(onlineArchivePostCreate.getIntervalLastEvaluated().isEmpty());

    final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // Request 1
    // sets lastArchiveRun
    // maxOffload = false -> clears numConsecutiveMaxOffloads
    // creates an OnlineArchiveHistory for this archive
    // hasSufficientIndex = undefined -> no change attempted to same property on archive
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 1")
              .put("numDocuments", 2)
              .put("numPuts", 1)
              .put("fileSize", 123)
              .put("maxOffload", false)
              .put(
                  "partitionTypeStats",
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put("fieldName", "field1")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "string")
                                              .put("numDocs", 3L))))
                      .put(
                          new JSONObject()
                              .put("fieldName", "dateField")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "date")
                                              .put("numDocs", 3L)))));

      final OnlineArchive onlineArchivePreRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      // lastArchiveRun was set
      assertNotNull(onlineArchivePostRequest.getLastArchiveRun());
      assertEquals(
          sdf.parse(request.getString("startDate")),
          onlineArchivePostRequest.getLastArchiveRun().getStartDate());
      assertEquals(
          request.getString("error"),
          onlineArchivePostRequest.getLastArchiveRun().getErrorMessage());

      // numFrsJobErrors defaults to 0 when not included in request
      assertEquals(
          Integer.valueOf(0), onlineArchivePostRequest.getLastArchiveRun().getNumFrsJobErrors());

      // maxOffload was false, numConsecutiveMaxOffloads cleared
      assertEquals(0, onlineArchivePostRequest.getNumConsecutiveMaxOffloads());

      // after tuneArchiveFrequency is called, intervalLastEvaluated is updated
      assertTrue(onlineArchivePostRequest.getIntervalLastEvaluated().isPresent());

      // OnlineArchiveHistory created
      final List<OnlineArchiveHistory> onlineArchiveHistories =
          _onlineArchiveHistoryDao.find(onlineArchivePostCreate.getId());
      assertEquals(1, onlineArchiveHistories.size());
      final OnlineArchiveHistory onlineArchiveHistory = onlineArchiveHistories.get(0);
      assertEquals(onlineArchive.getId(), onlineArchiveHistory.getArchiveId());
      assertEquals(onlineArchive.getGroupId(), onlineArchiveHistory.getGroupId());
      assertEquals(onlineArchive.getClusterName(), onlineArchiveHistory.getClusterName());
      final Calendar startDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      startDate.set(Calendar.HOUR_OF_DAY, 0);
      startDate.set(Calendar.MINUTE, 0);
      startDate.set(Calendar.SECOND, 0);
      startDate.set(Calendar.MILLISECOND, 0);
      final Calendar endDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      endDate.set(Calendar.HOUR_OF_DAY, 23);
      endDate.set(Calendar.MINUTE, 59);
      endDate.set(Calendar.SECOND, 59);
      endDate.set(Calendar.MILLISECOND, 999);
      assertEquals(startDate.getTime(), onlineArchiveHistory.getStartDate());
      assertEquals(endDate.getTime(), onlineArchiveHistory.getEndDate());
      assertEquals(1, onlineArchiveHistory.getNumPuts());
      assertEquals(2L, onlineArchiveHistory.getNumDocuments());
      assertEquals(123L, onlineArchiveHistory.getFileSize());
      final Map<String, Map<String, Long>> partitionTypeStatsMap =
          partitionTypeStatsToMap(onlineArchiveHistory.getPartitionTypeStats());
      assertEquals(2, partitionTypeStatsMap.size());
      assertTrue(partitionTypeStatsMap.containsKey("field1"));
      assertTrue(partitionTypeStatsMap.containsKey("dateField"));
      final Map<String, Long> field1Distribution = partitionTypeStatsMap.get("field1");
      assertEquals(1, field1Distribution.size());
      assertTrue(field1Distribution.containsKey("string"));
      assertEquals(Long.valueOf(3L), field1Distribution.get("string"));
      final Map<String, Long> dateFieldDistribution = partitionTypeStatsMap.get("dateField");
      assertEquals(1, dateFieldDistribution.size());
      assertTrue(dateFieldDistribution.containsKey("date"));
      assertEquals(Long.valueOf(3L), dateFieldDistribution.get("date"));
      assertEquals("US_EAST_1", onlineArchiveHistory.getS3DataRegion());

      // create onlineArchiveHistory for previous day so that tuneArchiveFrequency can run correctly
      // (request 5)
      startDate.add(Calendar.DATE, -1);
      endDate.add(Calendar.DATE, -1);
      _onlineArchiveHistoryDao.create(
          new OnlineArchiveHistory.Builder()
              .id(new ObjectId())
              .archiveId(onlineArchiveHistory.getArchiveId())
              .clusterId(onlineArchiveHistory.getGroupId(), onlineArchiveHistory.getClusterName())
              .startDate(startDate.getTime())
              .endDate(endDate.getTime())
              .numDocuments(2000L)
              .numPuts(onlineArchiveHistory.getNumPuts())
              .fileSize(onlineArchiveHistory.getFileSize())
              .build());

      // hasSufficientIndex was null, no change made to same property on archive
      assertEquals(
          onlineArchivePreRequest.isHasSufficientIndexes(),
          onlineArchivePostRequest.isHasSufficientIndexes());
    }

    // Request 2
    // updates lastArchiveRun
    // maxOffload = true -> increments numConsecutiveMaxOffloads
    // increments values in OnlineArchiveHistory for this archive
    // hasSufficientIndex = true -> change needed to same property on archive
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 2")
              .put("numDocuments", 6)
              .put("numPuts", 5)
              .put("fileSize", 234)
              .put("hasSufficientIndex", true)
              .put("maxOffload", true)
              .put(
                  "partitionTypeStats",
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put("fieldName", "field1")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "int")
                                              .put("numDocs", 2L))))
                      .put(
                          new JSONObject()
                              .put("fieldName", "dateField")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "date")
                                              .put("numDocs", 2L)))));

      final OnlineArchive onlineArchivePreRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      // lastArchiveRun updated
      assertNotNull(onlineArchivePostRequest.getLastArchiveRun());
      assertEquals(
          sdf.parse(request.getString("startDate")),
          onlineArchivePostRequest.getLastArchiveRun().getStartDate());
      assertEquals(
          request.getString("error"),
          onlineArchivePostRequest.getLastArchiveRun().getErrorMessage());
      assertEquals(2 + 6, onlineArchivePostRequest.getNumDocsFromLastEvaluated().longValue());

      // maxOffload was true, numConsecutiveMaxOffloads incremented
      assertEquals(1, onlineArchivePostRequest.getNumConsecutiveMaxOffloads());

      // Existing OnlineArchiveHistory updated (new one not generated)
      final List<OnlineArchiveHistory> onlineArchiveHistories =
          _onlineArchiveHistoryDao.find(onlineArchivePostCreate.getId());
      assertEquals(2, onlineArchiveHistories.size());
      final OnlineArchiveHistory onlineArchiveHistory = onlineArchiveHistories.get(1);
      assertEquals(onlineArchive.getId(), onlineArchiveHistory.getArchiveId());
      assertEquals(onlineArchive.getGroupId(), onlineArchiveHistory.getGroupId());
      assertEquals(onlineArchive.getClusterName(), onlineArchiveHistory.getClusterName());
      final Calendar startDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      startDate.set(Calendar.HOUR_OF_DAY, 0);
      startDate.set(Calendar.MINUTE, 0);
      startDate.set(Calendar.SECOND, 0);
      startDate.set(Calendar.MILLISECOND, 0);
      final Calendar endDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      endDate.set(Calendar.HOUR_OF_DAY, 23);
      endDate.set(Calendar.MINUTE, 59);
      endDate.set(Calendar.SECOND, 59);
      endDate.set(Calendar.MILLISECOND, 999);
      assertEquals(startDate.getTime(), onlineArchiveHistory.getStartDate());
      assertEquals(endDate.getTime(), onlineArchiveHistory.getEndDate());
      assertEquals(1 + 5, onlineArchiveHistory.getNumPuts());
      assertEquals(2 + 6, onlineArchiveHistory.getNumDocuments());
      assertEquals(123 + 234, onlineArchiveHistory.getFileSize());
      assertTrue(onlineArchivePostRequest.isHasSufficientIndexes().isPresent());
      assertTrue(onlineArchivePostRequest.isHasSufficientIndexes().get());

      final Map<String, Map<String, Long>> partitionTypeStatsMap =
          partitionTypeStatsToMap(onlineArchiveHistory.getPartitionTypeStats());
      assertEquals(2, partitionTypeStatsMap.size());
      assertTrue(partitionTypeStatsMap.containsKey("field1"));
      assertTrue(partitionTypeStatsMap.containsKey("dateField"));
      final Map<String, Long> field1Distribution = partitionTypeStatsMap.get("field1");
      assertEquals(2, field1Distribution.size());
      assertTrue(field1Distribution.containsKey("string"));
      assertEquals(Long.valueOf(3L), field1Distribution.get("string"));
      assertTrue(field1Distribution.containsKey("int"));
      assertEquals(Long.valueOf(2L), field1Distribution.get("int"));
      final Map<String, Long> dateFieldDistribution = partitionTypeStatsMap.get("dateField");
      assertEquals(1, dateFieldDistribution.size());
      assertTrue(dateFieldDistribution.containsKey("date"));
      assertEquals(Long.valueOf(5L), dateFieldDistribution.get("date"));

      // hasSufficientIndex was true
      assertNotEquals(
          onlineArchivePreRequest.isHasSufficientIndexes(),
          onlineArchivePostRequest.isHasSufficientIndexes());
    }

    // Request 3
    // updates lastArchiveRun
    // maxOffload = true -> increments numConsecutiveMaxOffloads
    // increments values in OnlineArchiveHistory for this archive
    // hasSufficientIndex = null -> no change needed to same property on archive
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 3")
              .put("numDocuments", 10)
              .put("numPuts", 9)
              .put("fileSize", 345)
              .put("hasSufficientIndex", (Boolean) null)
              .put("maxOffload", true);

      final OnlineArchive onlineArchivePreRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      // lastArchiveRun updated
      assertNotNull(onlineArchivePostRequest.getLastArchiveRun());
      assertEquals(
          sdf.parse(request.getString("startDate")),
          onlineArchivePostRequest.getLastArchiveRun().getStartDate());
      assertEquals(
          request.getString("error"),
          onlineArchivePostRequest.getLastArchiveRun().getErrorMessage());
      assertEquals(2 + 6 + 10, onlineArchivePostRequest.getNumDocsFromLastEvaluated().longValue());

      // maxOffload was true, numConsecutiveMaxOffloads incremented
      assertEquals(2, onlineArchivePostRequest.getNumConsecutiveMaxOffloads());

      // Existing OnlineArchiveHistory updated (new one not generated)
      final List<OnlineArchiveHistory> onlineArchiveHistories =
          _onlineArchiveHistoryDao.find(onlineArchivePostCreate.getId());
      assertEquals(2, onlineArchiveHistories.size());
      final OnlineArchiveHistory onlineArchiveHistory = onlineArchiveHistories.get(1);
      assertEquals(onlineArchive.getId(), onlineArchiveHistory.getArchiveId());
      assertEquals(onlineArchive.getGroupId(), onlineArchiveHistory.getGroupId());
      assertEquals(onlineArchive.getClusterName(), onlineArchiveHistory.getClusterName());
      final Calendar startDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      startDate.set(Calendar.HOUR_OF_DAY, 0);
      startDate.set(Calendar.MINUTE, 0);
      startDate.set(Calendar.SECOND, 0);
      startDate.set(Calendar.MILLISECOND, 0);
      final Calendar endDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      endDate.set(Calendar.HOUR_OF_DAY, 23);
      endDate.set(Calendar.MINUTE, 59);
      endDate.set(Calendar.SECOND, 59);
      endDate.set(Calendar.MILLISECOND, 999);
      assertEquals(startDate.getTime(), onlineArchiveHistory.getStartDate());
      assertEquals(endDate.getTime(), onlineArchiveHistory.getEndDate());
      assertEquals(1 + 5 + 9, onlineArchiveHistory.getNumPuts());
      assertEquals(2 + 6 + 10, onlineArchiveHistory.getNumDocuments());
      assertEquals(123 + 234 + 345, onlineArchiveHistory.getFileSize());

      assertEquals(
          onlineArchivePreRequest.isHasSufficientIndexes(),
          onlineArchivePostRequest.isHasSufficientIndexes());

      assertTrue(onlineArchivePostRequest.isHasSufficientIndexes().isPresent());
      assertTrue(onlineArchivePostRequest.isHasSufficientIndexes().get());
    }

    // Request 4
    // updates lastArchiveRun
    // maxOffload = undefined -> no change attempted to numConsecutiveMaxOffloads
    // increments values in OnlineArchiveHistory for this archive
    // hasSufficientIndex = false -> change needed to same property on archive
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 4")
              .put("numDocuments", 14)
              .put("numPuts", 13)
              .put("fileSize", 456)
              .put("hasSufficientIndex", false);

      final OnlineArchive onlineArchivePreRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      // lastArchiveRun updated
      assertNotNull(onlineArchivePostRequest.getLastArchiveRun());
      assertEquals(
          sdf.parse(request.getString("startDate")),
          onlineArchivePostRequest.getLastArchiveRun().getStartDate());
      assertEquals(
          request.getString("error"),
          onlineArchivePostRequest.getLastArchiveRun().getErrorMessage());
      assertEquals(
          2 + 6 + 10 + 14, onlineArchivePostRequest.getNumDocsFromLastEvaluated().longValue());

      // maxOffload was undefined, numConsecutiveMaxOffloads not modified
      assertEquals(2, onlineArchivePostRequest.getNumConsecutiveMaxOffloads());

      // Existing OnlineArchiveHistory updated (new one not generated)
      final List<OnlineArchiveHistory> onlineArchiveHistories =
          _onlineArchiveHistoryDao.find(onlineArchivePostCreate.getId());
      assertEquals(2, onlineArchiveHistories.size());
      final OnlineArchiveHistory onlineArchiveHistory = onlineArchiveHistories.get(1);
      assertEquals(onlineArchive.getId(), onlineArchiveHistory.getArchiveId());
      assertEquals(onlineArchive.getGroupId(), onlineArchiveHistory.getGroupId());
      assertEquals(onlineArchive.getClusterName(), onlineArchiveHistory.getClusterName());
      final Calendar startDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      startDate.set(Calendar.HOUR_OF_DAY, 0);
      startDate.set(Calendar.MINUTE, 0);
      startDate.set(Calendar.SECOND, 0);
      startDate.set(Calendar.MILLISECOND, 0);
      final Calendar endDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      endDate.set(Calendar.HOUR_OF_DAY, 23);
      endDate.set(Calendar.MINUTE, 59);
      endDate.set(Calendar.SECOND, 59);
      endDate.set(Calendar.MILLISECOND, 999);
      assertEquals(startDate.getTime(), onlineArchiveHistory.getStartDate());
      assertEquals(endDate.getTime(), onlineArchiveHistory.getEndDate());
      assertEquals(1 + 5 + 9 + 13, onlineArchiveHistory.getNumPuts());
      assertEquals(2 + 6 + 10 + 14, onlineArchiveHistory.getNumDocuments());
      assertEquals(123 + 234 + 345 + 456, onlineArchiveHistory.getFileSize());

      // when not specified, elapsed times in history is 0
      assertEquals(0, onlineArchiveHistory.getTotalElapsedTimeMs());
      assertEquals(0, onlineArchiveHistory.getArchiveQueryElapsedTimeMs());
      assertEquals(0, onlineArchiveHistory.getCreatePartitionsElapsedTimeMs());
      assertEquals(0, onlineArchiveHistory.getUploadFilesElapsedTimeMs());
      assertEquals(0, onlineArchiveHistory.getUploadFilesDLSRequestElapsedTimeMs());
      assertEquals(0, onlineArchiveHistory.getCleanUpArchiveElapsedTimeMs());

      // hasSufficientIndex was true, updated to false
      assertEquals(Optional.of(true), onlineArchivePreRequest.isHasSufficientIndexes());
      assertEquals(Optional.of(false), onlineArchivePostRequest.isHasSufficientIndexes());
    }

    // Request 5
    // intervalLastEvaluated = one hour ago -> calculate deviation rate -> frequency increase
    //      -> reset numDocsFromLastEvaluated
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 4")
              .put("numDocuments", 0)
              .put("numPuts", 0)
              .put("fileSize", 0)
              .put("hasSufficientIndex", false)
              .put("maxOffload", false);

      final OnlineArchive onlineArchivePreRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      final Calendar currentTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
      currentTime.add(Calendar.HOUR, -2);

      _onlineArchiveDao.updateIntervalLastEvaluated(onlineArchive.getId(), currentTime.getTime());

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      assertEquals(0, onlineArchivePostRequest.getNumDocsFromLastEvaluated().longValue());

      // maxOffload was undefined, numConsecutiveMaxOffloads not modified
      assertEquals(0, onlineArchivePostRequest.getNumConsecutiveMaxOffloads());

      // frequency increases by 5 minutes
      assertEquals(10 * 60, onlineArchivePostRequest.getRunFrequencyInSeconds());
    }

    // Request 6
    // hasSufficientIndex = null -> change needed to same property on archive
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 4")
              .put("numDocuments", 14)
              .put("numPuts", 13)
              .put("fileSize", 456)
              .put("hasSufficientIndex", (Boolean) null);

      final OnlineArchive onlineArchivePreRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      assertTrue(onlineArchivePreRequest.isHasSufficientIndexes().isPresent());
      assertFalse(onlineArchivePreRequest.isHasSufficientIndexes().get());
      assertFalse(onlineArchivePostRequest.isHasSufficientIndexes().isPresent());
    }

    // Request 7
    // updates elapsed times
    // increments values in OnlineArchiveHistory for this archive
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 4")
              .put("numDocuments", 14)
              .put("numPuts", 13)
              .put("fileSize", 456)
              .put("hasSufficientIndex", true)
              .put("totalElapsedTimeMs", 1000)
              .put("archiveQueryElapsedTimeMs", 50)
              .put("createPartitionsElapsedTimeMs", 100)
              .put("uploadFilesElapsedTimeMs", 150)
              .put("uploadFilesDLSRequestElapsedTimeMs", 250)
              .put("frsJobPollingElapsedTimeMs", 150)
              .put("cleanUpArchiveElapsedTimeMs", 700);

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final List<OnlineArchiveHistory> onlineArchiveHistories =
          _onlineArchiveHistoryDao.find(onlineArchivePostCreate.getId());
      assertEquals(2, onlineArchiveHistories.size());
      final OnlineArchiveHistory onlineArchiveHistory = onlineArchiveHistories.get(1);
      assertEquals(onlineArchive.getId(), onlineArchiveHistory.getArchiveId());
      assertEquals(onlineArchive.getGroupId(), onlineArchiveHistory.getGroupId());
      assertEquals(onlineArchive.getClusterName(), onlineArchiveHistory.getClusterName());
      assertEquals(
          request.getLong("totalElapsedTimeMs"), onlineArchiveHistory.getTotalElapsedTimeMs());
      assertEquals(
          request.getLong("archiveQueryElapsedTimeMs"),
          onlineArchiveHistory.getArchiveQueryElapsedTimeMs());
      assertEquals(
          request.getLong("createPartitionsElapsedTimeMs"),
          onlineArchiveHistory.getCreatePartitionsElapsedTimeMs());
      assertEquals(
          request.getLong("uploadFilesElapsedTimeMs"),
          onlineArchiveHistory.getUploadFilesElapsedTimeMs());
      assertEquals(
          request.getLong("uploadFilesDLSRequestElapsedTimeMs"),
          onlineArchiveHistory.getUploadFilesDLSRequestElapsedTimeMs());
      assertEquals(
          request.getLong("frsJobPollingElapsedTimeMs"),
          onlineArchiveHistory.getFrsJobPollingElapsedTimeMs());
      assertEquals(
          request.getLong("cleanUpArchiveElapsedTimeMs"),
          onlineArchiveHistory.getCleanUpArchiveElapsedTimeMs());

      // verify elapsed times remain the same after a request where fields are missing
      final JSONObject request2 =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 4")
              .put("numDocuments", 14)
              .put("numPuts", 13)
              .put("fileSize", 456);
      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request2);

      final List<OnlineArchiveHistory> onlineArchiveHistories2 =
          _onlineArchiveHistoryDao.find(onlineArchivePostCreate.getId());
      assertEquals(2, onlineArchiveHistories2.size());
      final OnlineArchiveHistory onlineArchiveHistory2 = onlineArchiveHistories2.get(1);
      assertEquals(
          request.getLong("totalElapsedTimeMs"), onlineArchiveHistory2.getTotalElapsedTimeMs());
      assertEquals(
          request.getLong("archiveQueryElapsedTimeMs"),
          onlineArchiveHistory2.getArchiveQueryElapsedTimeMs());
      assertEquals(
          request.getLong("createPartitionsElapsedTimeMs"),
          onlineArchiveHistory2.getCreatePartitionsElapsedTimeMs());
      assertEquals(
          request.getLong("uploadFilesElapsedTimeMs"),
          onlineArchiveHistory2.getUploadFilesElapsedTimeMs());
      assertEquals(
          request.getLong("uploadFilesDLSRequestElapsedTimeMs"),
          onlineArchiveHistory2.getUploadFilesDLSRequestElapsedTimeMs());
      assertEquals(
          request.getLong("frsJobPollingElapsedTimeMs"),
          onlineArchiveHistory2.getFrsJobPollingElapsedTimeMs());
      assertEquals(
          request.getLong("cleanUpArchiveElapsedTimeMs"),
          onlineArchiveHistory2.getCleanUpArchiveElapsedTimeMs());

      // verify elapsed times is sum of all the runs
      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);
      final List<OnlineArchiveHistory> onlineArchiveHistories3 =
          _onlineArchiveHistoryDao.find(onlineArchivePostCreate.getId());
      assertEquals(2, onlineArchiveHistories3.size());
      final OnlineArchiveHistory onlineArchiveHistory3 = onlineArchiveHistories3.get(1);
      assertEquals(
          request.getLong("totalElapsedTimeMs") * 2, onlineArchiveHistory3.getTotalElapsedTimeMs());
      assertEquals(
          request.getLong("archiveQueryElapsedTimeMs") * 2,
          onlineArchiveHistory3.getArchiveQueryElapsedTimeMs());
      assertEquals(
          request.getLong("createPartitionsElapsedTimeMs") * 2,
          onlineArchiveHistory3.getCreatePartitionsElapsedTimeMs());
      assertEquals(
          request.getLong("uploadFilesElapsedTimeMs") * 2,
          onlineArchiveHistory3.getUploadFilesElapsedTimeMs());
      assertEquals(
          request.getLong("uploadFilesDLSRequestElapsedTimeMs") * 2,
          onlineArchiveHistory3.getUploadFilesDLSRequestElapsedTimeMs());
      assertEquals(
          request.getLong("frsJobPollingElapsedTimeMs") * 2,
          onlineArchiveHistory3.getFrsJobPollingElapsedTimeMs());
      assertEquals(
          request.getLong("cleanUpArchiveElapsedTimeMs") * 2,
          onlineArchiveHistory3.getCleanUpArchiveElapsedTimeMs());
    }

    // Request 8
    // request made by non-primary
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 1")
              .put("numDocuments", 2)
              .put("numPuts", 1)
              .put("fileSize", 123)
              .put("maxOffload", false)
              .put(
                  "partitionTypeStats",
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put("fieldName", "field1")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "string")
                                              .put("numDocs", 3L))))
                      .put(
                          new JSONObject()
                              .put("fieldName", "dateField")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "date")
                                              .put("numDocs", 3L)))));

      final JSONObject response =
          new JSONObject(
              doAgentApiCallPostWithStatus(
                  _group,
                  _agentApiKey,
                  String.format(
                      LAST_RUN_STATS_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      onlineArchive.getId(),
                      HOSTNAME,
                      SESSION_KEY2),
                  request,
                  HttpStatus.SC_BAD_REQUEST));

      assertEquals(
          "NON_PRIMARY_CANNOT_MAKE_ARCHIVE_OPERATION_REQUEST", response.getString("errorCode"));
    }

    // Request 9
    // sets lastArchiveRun
    // has empty errorType
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 1")
              .put("numDocuments", 2)
              .put("numPuts", 1)
              .put("fileSize", 123)
              .put("maxOffload", false)
              .put("errorType", "")
              .put(
                  "partitionTypeStats",
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put("fieldName", "field1")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "string")
                                              .put("numDocs", 3L))))
                      .put(
                          new JSONObject()
                              .put("fieldName", "dateField")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "date")
                                              .put("numDocs", 3L)))));

      // Ensure empty error type will not throw an error
      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);
    }
  }

  @Test
  public void testLastRunStatsForCurrentArchiveWindow() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(CloudProvider.AWS);

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // Request 1
    // currentArchiveWindow is not set.
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 1")
              .put("numDocuments", 2)
              .put("numPuts", 1)
              .put("fileSize", 123)
              .put("maxOffload", false)
              .put(
                  "partitionTypeStats",
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put("fieldName", "field1")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "string")
                                              .put("numDocs", 3L))))
                      .put(
                          new JSONObject()
                              .put("fieldName", "dateField")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "date")
                                              .put("numDocs", 3L)))));

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      assertTrue(onlineArchivePostRequest.getCurrentArchiveWindow().isEmpty());
      assertEquals(0, onlineArchive.getNumConsecutiveMaxOffloadWindows());
    }

    final Date startDate = new Date();
    final Date endDate = new Date();

    // Request 2
    // currentArchiveWindow is sent for the first time, with maxoffload set to true.
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 2")
              .put("numDocuments", 6)
              .put("numPuts", 5)
              .put("fileSize", 234)
              .put("hasSufficientIndex", true)
              .put("maxOffload", true)
              .put(
                  "currentArchiveWindow",
                  new JSONObject()
                      .put("startDate", sdf.format(startDate))
                      .put("endDate", sdf.format(endDate)))
              .put(
                  "partitionTypeStats",
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put("fieldName", "field1")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "int")
                                              .put("numDocs", 2L))))
                      .put(
                          new JSONObject()
                              .put("fieldName", "dateField")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "date")
                                              .put("numDocs", 2L)))));

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      assertTrue(onlineArchivePostRequest.getCurrentArchiveWindow().isPresent());
      assertEquals(0, onlineArchivePostRequest.getNumConsecutiveMaxOffloadWindows());
    }

    // Request 3
    // currentArchiveWindow is sent for the second time, with maxoffload set to true.
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 2")
              .put("numDocuments", 6)
              .put("numPuts", 5)
              .put("fileSize", 234)
              .put("hasSufficientIndex", true)
              .put("maxOffload", true)
              .put(
                  "currentArchiveWindow",
                  new JSONObject()
                      .put("startDate", sdf.format(startDate))
                      .put("endDate", sdf.format(endDate)))
              .put(
                  "partitionTypeStats",
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put("fieldName", "field1")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "int")
                                              .put("numDocs", 2L))))
                      .put(
                          new JSONObject()
                              .put("fieldName", "dateField")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "date")
                                              .put("numDocs", 2L)))));

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      assertTrue(onlineArchivePostRequest.getCurrentArchiveWindow().isPresent());
      assertEquals(0, onlineArchivePostRequest.getNumConsecutiveMaxOffloadWindows());
    }

    // Request 4
    // currentArchiveWindow is sent for the third time, with maxoffload set to true.
    // this time currentArchiveWindow is the new window.
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(new Date()))
              .put("endDate", sdf.format(new Date()))
              .put("error", "error message 2")
              .put("numDocuments", 6)
              .put("numPuts", 5)
              .put("fileSize", 234)
              .put("hasSufficientIndex", true)
              .put("maxOffload", true)
              .put(
                  "currentArchiveWindow",
                  new JSONObject()
                      .put("startDate", sdf.format(new Date()))
                      .put("endDate", sdf.format(endDate)))
              .put(
                  "partitionTypeStats",
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put("fieldName", "field1")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "int")
                                              .put("numDocs", 2L))))
                      .put(
                          new JSONObject()
                              .put("fieldName", "dateField")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "date")
                                              .put("numDocs", 2L)))));

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      assertTrue(onlineArchivePostRequest.getCurrentArchiveWindow().isPresent());
      assertEquals(1, onlineArchivePostRequest.getNumConsecutiveMaxOffloadWindows());
    }

    // Request 5
    // currentArchiveWindow is not sent.
    {
      final JSONObject request =
          new JSONObject()
              .put("startDate", sdf.format(startDate))
              .put("endDate", sdf.format(endDate))
              .put("error", "error message 2")
              .put("numDocuments", 6)
              .put("numPuts", 5)
              .put("fileSize", 234)
              .put("hasSufficientIndex", true)
              .put("maxOffload", true)
              .put(
                  "partitionTypeStats",
                  new JSONArray()
                      .put(
                          new JSONObject()
                              .put("fieldName", "field1")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "int")
                                              .put("numDocs", 2L))))
                      .put(
                          new JSONObject()
                              .put("fieldName", "dateField")
                              .put(
                                  "typeDistribution",
                                  new JSONArray()
                                      .put(
                                          new JSONObject()
                                              .put("fieldType", "date")
                                              .put("numDocs", 2L)))));

      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              LAST_RUN_STATS_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      final OnlineArchive onlineArchivePostRequest =
          _onlineArchiveSvc.getValidateOnlineArchive(onlineArchive.getId());

      // current archive window has been reset to null.
      assertTrue(onlineArchivePostRequest.getCurrentArchiveWindow().isEmpty());
      assertEquals(0, onlineArchivePostRequest.getNumConsecutiveMaxOffloadWindows());
    }
  }

  @Test
  public void testGetCredentials() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(CloudProvider.AWS);

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // request made by non-primary
    {
      final JSONObject response =
          new JSONObject(
              doAgentApiCallGetWithStatus(
                  _group,
                  _agentApiKey,
                  String.format(
                      GET_CREDENTIALS_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      HOSTNAME,
                      SESSION_KEY2),
                  HttpStatus.SC_BAD_REQUEST));

      assertEquals(
          "NON_PRIMARY_CANNOT_MAKE_ARCHIVE_OPERATION_REQUEST", response.getString("errorCode"));
    }
  }

  @Test
  public void testGetDataLandingZoneBucketCredentials() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(CloudProvider.AWS);

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // request made by non-primary
    {
      final JSONObject response =
          new JSONObject(
              doAgentApiCallGetWithStatus(
                  _group,
                  _agentApiKey,
                  String.format(
                      GET_DLZ_BUCKET_CREDENTIALS_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      onlineArchive.getId(),
                      HOSTNAME,
                      SESSION_KEY2),
                  HttpStatus.SC_BAD_REQUEST));

      assertEquals(
          "NON_PRIMARY_CANNOT_MAKE_ARCHIVE_OPERATION_REQUEST", response.getString("errorCode"));
    }
  }

  @Test
  public void testPauseOA() throws SvcException, IOException {
    final OnlineArchive onlineArchive = getOnlineArchive(CloudProvider.AWS);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(onlineArchive.getId(), AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // failed - OA not found
    {
      final ObjectId randomId = ObjectId.get();
      final String pauseOAURI =
          String.format(
              PAUSE_OA_URL,
              onlineArchive.getGroupId(),
              onlineArchive.getClusterName(),
              randomId.toString(),
              HOSTNAME,
              SESSION_KEY1,
              AGENT_VERSION);

      final JSONObject respBody =
          new JSONObject(
              doAgentApiCallPatch(
                  HttpStatus.SC_BAD_REQUEST, _group, _agentApiKey, pauseOAURI, new byte[] {}));

      assertEquals(
          NDSErrorCode.ONLINE_ARCHIVE_DOES_NOT_EXIST.name(), respBody.getString(ERROR_CODE_FIELD));
    }

    // success - paused agent
    {
      final String pauseOAURI =
          String.format(
              PAUSE_OA_URL,
              onlineArchive.getGroupId(),
              onlineArchive.getClusterName(),
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1,
              AGENT_VERSION);

      final JSONObject respBody =
          new JSONObject(doAgentApiCallPatch(_group, _agentApiKey, pauseOAURI, new byte[] {}));

      assertEquals(
          OnlineArchive.State.PAUSING.name(),
          respBody.getString(OnlineArchiveBaseView.FieldDefs.ARCHIVE_STATE));
      assertEquals(
          OnlineArchive.State.PAUSING,
          _onlineArchiveDao.find(onlineArchive.getId()).map(OnlineArchive::getState).orElseThrow());
    }
  }

  @Test
  public void testMarkOAPaused() throws SvcException, IOException {
    final OnlineArchive onlineArchive = getOnlineArchive(CloudProvider.AWS);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(onlineArchive.getId(), AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    final String markOAPausedURI =
        String.format(
            MARK_OA_PAUSED_URL,
            onlineArchive.getGroupId(),
            onlineArchive.getClusterName(),
            onlineArchive.getId(),
            HOSTNAME,
            SESSION_KEY1,
            AGENT_VERSION);

    // failed - OA not found
    {
      final ObjectId randomId = ObjectId.get();
      final String markRandomOAPausedURI =
          String.format(
              MARK_OA_PAUSED_URL,
              onlineArchive.getGroupId(),
              onlineArchive.getClusterName(),
              randomId.toString(),
              HOSTNAME,
              SESSION_KEY1,
              AGENT_VERSION);

      final JSONObject respBody =
          new JSONObject(
              doAgentApiCallPatch(
                  HttpStatus.SC_BAD_REQUEST,
                  _group,
                  _agentApiKey,
                  markRandomOAPausedURI,
                  new byte[] {}));

      assertEquals(
          NDSErrorCode.ONLINE_ARCHIVE_DOES_NOT_EXIST.name(), respBody.getString(ERROR_CODE_FIELD));
    }

    // failed - the original state was ACTIVE instead of PAUSING
    {
      final JSONObject respBody =
          new JSONObject(
              doAgentApiCallPatch(
                  HttpStatus.SC_BAD_REQUEST, _group, _agentApiKey, markOAPausedURI, new byte[] {}));

      assertEquals(
          NDSErrorCode.ONLINE_ARCHIVE_MUST_BE_PAUSING_TO_MARK_PAUSED.name(),
          respBody.getString(ERROR_CODE_FIELD));
    }

    // success - agent marks OA as PAUSED
    {
      final String pauseOAURI =
          String.format(
              PAUSE_OA_URL,
              onlineArchive.getGroupId(),
              onlineArchive.getClusterName(),
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1,
              AGENT_VERSION);
      // PAUSING before mark OA as PAUSED
      doAgentApiCallPatch(_group, _agentApiKey, pauseOAURI, new byte[] {});
      doAgentApiCallPatch(_group, _agentApiKey, markOAPausedURI, new byte[] {});

      assertEquals(
          OnlineArchive.State.PAUSED,
          _onlineArchiveDao.find(onlineArchive.getId()).map(OnlineArchive::getState).orElseThrow());

      // do not throw error if already PAUSED
      doAgentApiCallPatch(_group, _agentApiKey, markOAPausedURI, new byte[] {});
      assertEquals(
          OnlineArchive.State.PAUSED,
          _onlineArchiveDao.find(onlineArchive.getId()).map(OnlineArchive::getState).orElseThrow());
    }
  }

  @Test
  public void testGetJobProgress() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(CloudProvider.AWS);

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    final String jobId = UUID.randomUUID().toString();
    final Date creationTime = new Date();
    final int totalWorkUnits = 300;
    final int activeWorkUnits = 100;
    final int completedWorkUnits = 200;
    final long elapsedWorkTime = 823;
    final boolean complete = false;
    final int errorCount = 0;
    final GetJobProgressResponseView jobProgress =
        new GetJobProgressResponseView(
            jobId,
            creationTime,
            totalWorkUnits,
            activeWorkUnits,
            completedWorkUnits,
            elapsedWorkTime,
            complete,
            errorCount);
    _dataLakeTestUtils.setJobProgress(jobId, jobProgress);

    final String jobURI =
        String.format(
            GET_JOB_PROGRESS_URL,
            _group.getId(),
            CLUSTER_NAME_AWS,
            onlineArchive.getId(),
            jobId,
            HOSTNAME,
            SESSION_KEY1);

    final String respBody = doAgentApiCallGet(_group, _agentApiKey, jobURI);
    final JSONObject resp = new JSONObject(respBody);

    final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX");
    assertEquals(jobId, resp.getString("jobId"));
    assertEquals(sdf.format(creationTime), resp.getString("creationTime"));
    assertEquals(totalWorkUnits, resp.getInt("totalWorkUnits"));
    assertEquals(activeWorkUnits, resp.getInt("activeWorkUnits"));
    assertEquals(completedWorkUnits, resp.getInt("completedWorkUnits"));
    assertEquals(elapsedWorkTime, resp.getLong("elapsedWorkTimeMs"));
    assertEquals(complete, resp.getBoolean("complete"));
    assertEquals(errorCount, resp.getInt("errorCount"));
  }

  @Test
  public void testGetJobErrors() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(CloudProvider.AWS);

    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    final String jobId = UUID.randomUUID().toString();
    final String errorMessage = "unexpected error";
    final String source = "bad job id";
    final String sourceType = "SourceTypeTaskQueue";
    final GetJobErrorsResponseView jobError =
        new GetJobErrorsResponseView(errorMessage, source, sourceType);
    _dataLakeTestUtils.setJobErrors(jobId, jobError);

    final String jobErrorsURI =
        String.format(
            GET_JOB_ERRORS_URL,
            _group.getId(),
            CLUSTER_NAME_AWS,
            onlineArchive.getId(),
            jobId,
            HOSTNAME,
            SESSION_KEY1);

    final String respBody = doAgentApiCallGet(_group, _agentApiKey, jobErrorsURI);
    final JSONArray resp = new JSONArray(respBody);

    assertEquals(resp.length(), 1);
    assertEquals(errorMessage, resp.getJSONObject(0).get("errorMessage"));
    assertEquals(source, resp.getJSONObject(0).get("source"));
    assertEquals(sourceType, resp.getJSONObject(0).get("sourceType"));
  }

  @Test
  public void testRegisterOAFiles() throws Exception {
    final OnlineArchive onlineArchive = getOnlineArchive(CloudProvider.AWS);
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    final JSONObject request =
        new JSONObject()
            .put("jobId", "jobId")
            .put("datasetName", "datasetName")
            .put("metadataProvider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
            .put("metadataRegion", AWSRegionName.US_EAST_1.getValue())
            .put("provider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
            .put("region", AWSRegionName.US_EAST_1.getValue())
            .put("bucket", "bucket")
            .put("manifest", "manifest")
            .put("credentialSessionId", "credentialSessionId");

    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            REGISTER_OA_FILES_URL,
            _group.getId(),
            CLUSTER_NAME_AWS,
            onlineArchive.getId(),
            HOSTNAME,
            SESSION_KEY1),
        request);
  }

  @Test
  public void testUploadDataFilesAWSRequest() throws Exception {
    final OnlineArchive onlineArchive =
        getOnlineArchive(CloudProvider.AWS).toBuilder()
            .setCurrentArchiveRun(
                new ArchiveRun(
                    "jobId",
                    new Date(),
                    new Date(),
                    "",
                    0,
                    ArchiveRunState.PREPARING_DLZ,
                    null,
                    false,
                    false,
                    false,
                    0))
            .build();
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // request made by non-primary
    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
              .put("metadataRegion", AWSRegionName.US_EAST_1.getValue())
              .put("provider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
              .put("region", AWSRegionName.US_EAST_1.getValue())
              .put("bucket", "bucket")
              .put("manifest", "manifest")
              .put("manifestLocation", JSONObject.NULL);

      final JSONObject response =
          new JSONObject(
              doAgentApiCallPostWithStatus(
                  _group,
                  _agentApiKey,
                  String.format(
                      UPLOAD_DATA_FILES_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      onlineArchive.getId(),
                      HOSTNAME,
                      SESSION_KEY2),
                  request,
                  HttpStatus.SC_BAD_REQUEST));

      assertEquals(
          "NON_PRIMARY_CANNOT_MAKE_ARCHIVE_OPERATION_REQUEST", response.getString("errorCode"));
    }

    // request missing a field
    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
              .put("metadataRegion", AWSRegionName.US_EAST_1.getValue())
              .put("provider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
              .put("region", AWSRegionName.US_EAST_1.getValue())
              .put("bucket", "bucket")
              .put("manifestLocation", JSONObject.NULL);

      doAgentApiCallPostWithStatus(
          _group,
          _agentApiKey,
          String.format(
              UPLOAD_DATA_FILES_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request,
          HttpStatus.SC_BAD_REQUEST);
    }

    // valid request is successful
    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
              .put("metadataRegion", AWSRegionName.US_EAST_1.getValue())
              .put("provider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
              .put("region", AWSRegionName.US_EAST_1.getValue())
              .put("bucket", "bucket")
              .put("manifest", "manifest")
              .put("manifestLocation", JSONObject.NULL);

      // response is OK
      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              UPLOAD_DATA_FILES_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      // archive run state set to uploading
      assertEquals(
          ArchiveRunState.UPLOADING_TO_DLS,
          _onlineArchiveDao
              .find(onlineArchive.getId())
              .map(OnlineArchive::getCurrentArchiveRun)
              .orElseThrow()
              .map(ArchiveRun::getState)
              .orElseThrow());
    }
  }

  @Test
  public void testUploadDataFilesAWSRequestWithManifestLocation() throws Exception {
    final OnlineArchive onlineArchive =
        getOnlineArchive(CloudProvider.AWS).toBuilder()
            .setCurrentArchiveRun(
                new ArchiveRun(
                    "jobId",
                    new Date(),
                    new Date(),
                    "",
                    0,
                    ArchiveRunState.PREPARING_DLZ,
                    null,
                    false,
                    false,
                    false,
                    0))
            .build();
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // request made by non-primary
    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", JSONObject.NULL)
              .put("metadataRegion", JSONObject.NULL)
              .put("provider", JSONObject.NULL)
              .put("region", JSONObject.NULL)
              .put("bucket", JSONObject.NULL)
              .put("manifest", JSONObject.NULL)
              .put("manifestLocation", getManifestLocation(CloudProvider.AWS, false));

      final JSONObject response =
          new JSONObject(
              doAgentApiCallPostWithStatus(
                  _group,
                  _agentApiKey,
                  String.format(
                      UPLOAD_DATA_FILES_URL,
                      _group.getId(),
                      CLUSTER_NAME_AWS,
                      onlineArchive.getId(),
                      HOSTNAME,
                      SESSION_KEY2),
                  request,
                  HttpStatus.SC_BAD_REQUEST));

      assertEquals(
          "NON_PRIMARY_CANNOT_MAKE_ARCHIVE_OPERATION_REQUEST", response.getString("errorCode"));
    }

    // request missing a field
    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", JSONObject.NULL)
              .put("metadataRegion", JSONObject.NULL)
              .put("provider", JSONObject.NULL)
              .put("region", JSONObject.NULL)
              .put("bucket", JSONObject.NULL)
              .put("manifest", JSONObject.NULL)
              .put("manifestLocation", getManifestLocation(CloudProvider.AWS, true));

      doAgentApiCallPostWithStatus(
          _group,
          _agentApiKey,
          String.format(
              UPLOAD_DATA_FILES_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request,
          HttpStatus.SC_BAD_REQUEST);
    }

    // valid request is successful
    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", JSONObject.NULL)
              .put("metadataRegion", JSONObject.NULL)
              .put("provider", JSONObject.NULL)
              .put("region", JSONObject.NULL)
              .put("bucket", JSONObject.NULL)
              .put("manifest", JSONObject.NULL)
              .put("manifestLocation", getManifestLocation(CloudProvider.AWS, false));

      // response is OK
      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              UPLOAD_DATA_FILES_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      // archive run state set to uploading
      assertEquals(
          ArchiveRunState.UPLOADING_TO_DLS,
          _onlineArchiveDao
              .find(onlineArchive.getId())
              .map(OnlineArchive::getCurrentArchiveRun)
              .orElseThrow()
              .map(ArchiveRun::getState)
              .orElseThrow());
    }
  }

  @Test
  public void testUploadDataFilesAzureRequestWithManifestLocation() throws Exception {
    _onlineArchiveTestUtils.setUpAzureTestStorageAccount(_group);
    final OnlineArchive onlineArchive =
        getOnlineArchive(CloudProvider.AZURE).toBuilder()
            .setOnlineArchiveVersion(OnlineArchiveVersion.V3)
            .setCurrentArchiveRun(
                new ArchiveRun(
                    "jobId",
                    new Date(),
                    new Date(),
                    "",
                    0,
                    ArchiveRunState.PREPARING_DLZ,
                    null,
                    false,
                    false,
                    false,
                    0))
            .build();
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL,
            _group.getId(),
            CLUSTER_NAME_AZURE,
            HOSTNAME,
            SESSION_KEY1,
            AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // request made by non-primary
    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", JSONObject.NULL)
              .put("metadataRegion", JSONObject.NULL)
              .put("provider", JSONObject.NULL)
              .put("region", JSONObject.NULL)
              .put("bucket", JSONObject.NULL)
              .put("manifest", JSONObject.NULL)
              .put("manifestLocation", getManifestLocation(CloudProvider.AZURE, false));

      final JSONObject response =
          new JSONObject(
              doAgentApiCallPostWithStatus(
                  _group,
                  _agentApiKey,
                  String.format(
                      UPLOAD_DATA_FILES_URL,
                      _group.getId(),
                      CLUSTER_NAME_AZURE,
                      onlineArchive.getId(),
                      HOSTNAME,
                      SESSION_KEY2),
                  request,
                  HttpStatus.SC_BAD_REQUEST));

      assertEquals(
          "NON_PRIMARY_CANNOT_MAKE_ARCHIVE_OPERATION_REQUEST", response.getString("errorCode"));
    }

    // request missing a field
    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", JSONObject.NULL)
              .put("metadataRegion", JSONObject.NULL)
              .put("provider", JSONObject.NULL)
              .put("region", JSONObject.NULL)
              .put("bucket", JSONObject.NULL)
              .put("manifest", JSONObject.NULL)
              .put("manifestLocation", getManifestLocation(CloudProvider.AZURE, true));

      doAgentApiCallPostWithStatus(
          _group,
          _agentApiKey,
          String.format(
              UPLOAD_DATA_FILES_URL,
              _group.getId(),
              CLUSTER_NAME_AZURE,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request,
          HttpStatus.SC_BAD_REQUEST);
    }

    // valid request is successful
    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", JSONObject.NULL)
              .put("metadataRegion", JSONObject.NULL)
              .put("provider", JSONObject.NULL)
              .put("region", JSONObject.NULL)
              .put("bucket", JSONObject.NULL)
              .put("manifest", JSONObject.NULL)
              .put("manifestLocation", getManifestLocation(CloudProvider.AZURE, false));

      // response is OK
      doAgentApiCallPost(
          _group,
          _agentApiKey,
          String.format(
              UPLOAD_DATA_FILES_URL,
              _group.getId(),
              CLUSTER_NAME_AZURE,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request);

      // archive run state set to uploading
      assertEquals(
          ArchiveRunState.UPLOADING_TO_DLS,
          _onlineArchiveDao
              .find(onlineArchive.getId())
              .map(OnlineArchive::getCurrentArchiveRun)
              .orElseThrow()
              .map(ArchiveRun::getState)
              .orElseThrow());
    }
  }

  @Test
  public void testUploadDataFiles_adlNotAvailable() throws Exception {
    final OnlineArchive onlineArchive =
        getOnlineArchive(CloudProvider.AWS).toBuilder()
            .setCurrentArchiveRun(
                new ArchiveRun(
                    "jobId",
                    new Date(),
                    new Date(),
                    "",
                    0,
                    ArchiveRunState.PREPARING_DLZ,
                    null,
                    false,
                    false,
                    false,
                    0))
            .build();
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());

    // make conf call to start session
    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // shut down adl services
    _dataLakeTestUtils.teardown();

    {
      final JSONObject request =
          new JSONObject()
              .put("jobId", "jobId")
              .put("dataSetName", "dataSetName")
              .put("metadataProvider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
              .put("metadataRegion", AWSRegionName.US_EAST_1.getValue())
              .put("provider", makeDLSAcceptedCloudProvider(CloudProvider.AWS))
              .put("region", AWSRegionName.US_EAST_1.getValue())
              .put("bucket", "bucket")
              .put("manifest", "manifest")
              .put("manifestLocation", JSONObject.NULL);

      // response is service unavailable
      doAgentApiCallPostWithStatus(
          _group,
          _agentApiKey,
          String.format(
              UPLOAD_DATA_FILES_URL,
              _group.getId(),
              CLUSTER_NAME_AWS,
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1),
          request,
          HttpStatus.SC_SERVICE_UNAVAILABLE);
    }
  }

  @Test
  public void testGetArchiveRunInfo() throws Exception {
    OnlineArchive onlineArchive =
        new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
            .setCollName("mycoll6")
            .setCriteria(new OnlineArchive.CustomCriteria("{\"field\": \"value\"}"))
            .setSchedule(new DailySchedule(1, 30, 3, 40))
            .setCurrentArchiveWindow(new ArchiveWindow(new Date(), new Date(), 10, 2))
            .build();

    final Date currentRunStart = Date.from(Instant.now().minus(10, SECONDS));
    final Date lastRunStart = Date.from(Instant.now().minus(1000, SECONDS));
    final String currRunJobId = "jobId-4567", lastRunJobId = "jobId-3567";
    final ArchiveRun currentArchiveRun =
        ArchiveRun.builder()
            .jobId(currRunJobId)
            .startDate(currentRunStart)
            .state(ArchiveRunState.PURGING)
            .build();
    final ArchiveRun lastArchiveRun =
        ArchiveRun.builder()
            .jobId(lastRunJobId)
            .startDate(lastRunStart)
            .state(ArchiveRunState.COMPLETED)
            .build();
    OnlineArchive onlineArchive1 =
        new OnlineArchive.Builder(getOnlineArchive(CloudProvider.AWS).toDBObject())
            .setCollName("mycoll7")
            .setCriteria(new OnlineArchive.CustomCriteria("{\"field\": \"value\"}"))
            .setSchedule(new DailySchedule(1, 30, 3, 40))
            .setCurrentArchiveRun(currentArchiveRun)
            .setLastArchiveRun(lastArchiveRun)
            .setCurrentArchiveWindow(new ArchiveWindow(new Date(), new Date(), 10, 2))
            .build();

    // Create online archive
    _onlineArchiveSvc.create(onlineArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.create(onlineArchive1, AuditInfoHelpers.fromSystem());

    doAgentApiCallPost(
        _group,
        _agentApiKey,
        String.format(
            CONFIGURE_URL, _group.getId(), CLUSTER_NAME_AWS, HOSTNAME, SESSION_KEY1, AGENT_VERSION),
        new JSONObject().put("archiveResolutions", new JSONObject()));

    // success - get the correct current and last archive runs in a list
    {
      final String getArchiveRunInfoURI =
          String.format(
              GET_ARCHIVE_RUN_INFO_URL,
              onlineArchive.getGroupId(),
              onlineArchive.getClusterName(),
              onlineArchive.getId(),
              HOSTNAME,
              SESSION_KEY1);

      final String respBody = doAgentApiCallGet(_group, _agentApiKey, getArchiveRunInfoURI);
      final JSONObject resp = new JSONObject(respBody);
      assertEquals(JSONObject.NULL, resp.get("currentArchiveRun"));
      assertEquals(JSONObject.NULL, resp.get("lastArchiveRun"));
    }

    {
      final String getArchiveRunInfoURI =
          String.format(
              GET_ARCHIVE_RUN_INFO_URL,
              onlineArchive1.getGroupId(),
              onlineArchive1.getClusterName(),
              onlineArchive1.getId(),
              HOSTNAME,
              SESSION_KEY1);

      final String respBody = doAgentApiCallGet(_group, _agentApiKey, getArchiveRunInfoURI);
      final JSONObject resp = new JSONObject(respBody);

      assertEquals(currRunJobId, resp.getJSONObject("currentArchiveRun").getString("jobId"));
      assertEquals(lastRunJobId, resp.getJSONObject("lastArchiveRun").getString("jobId"));
      assertNotNull(resp.getJSONObject("currentArchiveRun").getString("startDate"));
      assertEquals("COMPLETED", resp.getJSONObject("lastArchiveRun").getString("state"));
    }
  }

  private OnlineArchive getOnlineArchive(CloudProvider pCloudProvider) {
    return getOnlineArchive(pCloudProvider, OnlineArchiveVersion.V1);
  }

  private OnlineArchive getOnlineArchive(
      CloudProvider pCloudProvider, final OnlineArchiveVersion pVersion) {
    final String dbName = "mydb";
    final UUID collectionUUID = new UUID(1L, 1L);
    final List<PartitionField> partitionFields =
        List.of(
            new PartitionField("date", "dateField", 0), new PartitionField("string", "field1", 1));

    String clusterName;

    switch (pCloudProvider) {
      case AZURE:
        clusterName = CLUSTER_NAME_AZURE;
        break;
      case AWS:
        clusterName = CLUSTER_NAME_AWS;
        break;
      case GCP:
        clusterName = CLUSTER_NAME_GCP;
        break;
      default:
        throw new IllegalArgumentException(
            String.format("Unsupported Cloud Provider: %s", pCloudProvider));
    }

    return new OnlineArchive.Builder()
        .setArchiveId(ObjectId.get())
        .setClusterId(_group.getId(), clusterName)
        .setDbName(dbName)
        .setCollName("mycoll")
        .setCollectionUUID(collectionUUID)
        .setPartitionFields(partitionFields)
        .setCriteria(new OnlineArchive.DateCriteria("dateField", 5, DateFormat.ISODATE))
        .setState(OnlineArchive.State.ACTIVE)
        .setCollectionType(CollectionType.STANDARD)
        .setOnlineArchiveVersion(pVersion)
        .build();
  }

  private JSONObject getManifestLocation(
      final CloudProvider pProvider, final boolean hasMissingField) {
    final JSONObject obj;
    switch (pProvider) {
      case AWS:
        obj =
            new JSONObject()
                .put("provider", makeDLSAcceptedCloudProvider(pProvider))
                .put("region", AWSRegionName.US_EAST_1.getValue())
                .put(
                    "blobSource",
                    new JSONObject()
                        .put("blobSourceS3", new JSONObject().put("bucket", "bucket"))
                        .put("blobSourceAzure", JSONObject.NULL));
        if (!hasMissingField) {
          obj.put("manifest", "manifest");
        }
        return obj;
      case AZURE:
        obj =
            new JSONObject()
                .put("provider", makeDLSAcceptedCloudProvider(pProvider))
                .put("region", AzureRegionName.US_EAST_2.getValue())
                .put(
                    "blobSource",
                    new JSONObject()
                        .put("blobSourceS3", JSONObject.NULL)
                        .put(
                            "blobSourceAzure",
                            new JSONObject()
                                .put("serviceURL", "serviceURL")
                                .put("containerName", "containerName")));
        if (!hasMissingField) {
          obj.put("manifest", "manifest");
        }
        return obj;
      default:
        throw new IllegalArgumentException(
            String.format("Unsupported Cloud Provider: %s", pProvider));
    }
  }

  protected String makeDLSAcceptedCloudProvider(final CloudProvider pProvider) {
    return pProvider.toString().toLowerCase();
  }
}
