package com.xgen.svc.nds.res;

import static com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessGCPServiceAccount.ServiceAccountProvisionStatus;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.amazonaws.services.securitytoken.model.Credentials;
import com.google.auth.oauth2.AccessToken;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAccount.FieldDefs;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSModelTestFactory;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareModelTestFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessGCPServiceAccount;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessGCPServiceAccountView;
import com.xgen.svc.nds.svc.DataSetCredentialsSvc;
import com.xgen.svc.nds.svc.NDSCloudProviderAccessSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSCloudProviderAccessResourceIntTests extends JUnit5BaseResourceTest {
  private static final String HOSTNAME = "hostname";
  private static final String CLUSTER_NAME = "cluster_name";

  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private ReplicaSetHardwareDao _replicaSetHardwareDao;
  @Inject private NDSCloudProviderAccessSvc _ndsCloudProviderAccessSvc;

  private AppUser _user;
  private AgentApiKey _apiKey;
  private Group _group;
  private Group _anotherGroup;
  private ClusterDescription _clusterDescription;
  private ObjectId _awsContainerId;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan();
    _anotherGroup = MmsFactory.createGroupWithNDSPlan();
    _user = MmsFactory.createGroupOwnerUser(_group);
    _apiKey = MmsFactory.generateApiKey(_group.getId(), _user.getId());
    _ndsGroupSvc.ensureGroup(_group.getId());
    _ndsGroupSvc.ensureGroup(_anotherGroup.getId());
    final AWSAccount awsAccount = new AWSAccount(AWSModelTestFactory.getAWSAccount());
    _awsAccountDao.save(awsAccount);

    final AWSAccount dlzAccount =
        new AWSAccount(
            AWSModelTestFactory.getAWSAccount()
                .append(AWSAccount.FieldDefs.FOR_INGESTION_DATA_LANDING_ZONE, true));
    _awsAccountDao.save(dlzAccount);

    final AWSAccount logsAccount =
        new AWSAccount(AWSModelTestFactory.getAWSAccount().append(FieldDefs.FOR_LOGS, true));
    _awsAccountDao.save(logsAccount);

    _awsContainerId = new ObjectId();
    _ndsGroupDao.addCloudContainerWithId(
        _group.getId(),
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer()),
        _awsContainerId);

    final List<RegionConfig> regionConfigs =
        NDSModelTestFactory.getRegionConfigs(3, List.of(AWSRegionName.US_EAST_1));

    final BasicDBObject replicaSetHardwareId =
        ReplicaSetHardware.createNonConfigReplicaSetId(CLUSTER_NAME, _group.getId(), 0);
    _replicaSetHardwareDao.create(
        replicaSetHardwareId,
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(
                _group.getId(), CLUSTER_NAME, CLUSTER_NAME, List.of())
            .next()
            .rsId(),
        true,
        false,
        new ObjectId());

    final List<InstanceHardware> instanceHardwareList =
        List.of(
            new AWSInstanceHardware(
                InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull(
                    HOSTNAME, _awsContainerId, 0)));

    _replicaSetHardwareDao.setCloudProviderHardware(
        replicaSetHardwareId, new BasicDBList(), instanceHardwareList, true);

    final ObjectId replicaInstanceId =
        _replicaSetHardwareDao.addInstance(replicaSetHardwareId, CloudProvider.AWS, false, 0);
    Assertions.assertNotNull(replicaInstanceId);

    final Hostnames replicaHostnames = new Hostnames(HOSTNAME);
    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        replicaInstanceId,
        true,
        InstanceHardware.FieldDefs.HOSTNAMES,
        replicaHostnames.toDBList());

    _replicaSetHardwareDao.setInstanceField(
        replicaSetHardwareId,
        replicaInstanceId,
        true,
        InstanceHardware.FieldDefs.CLOUD_PROVIDER_CONTAINER_ID,
        _awsContainerId);

    final ClusterDescription replicaSetClusterDescription =
        NDSModelTestFactory.getDefaultClusterDescription(
            _group.getId(), CLUSTER_NAME, regionConfigs);
    _clusterDescriptionDao.save(replicaSetClusterDescription);
  }

  @Test
  public void testCreateTemporaryGCPCredentialsForAgent() throws Exception {
    final AccessToken accessToken = mock(AccessToken.class);
    doReturn("accessTokenValue").when(accessToken).getTokenValue();
    doReturn(new Date()).when(accessToken).getExpirationTime();
    doReturn(List.of()).when(accessToken).getScopes();

    final GCPApiSvc gcpApiSvc = mock(GCPApiSvc.class);
    doReturn(accessToken).when(gcpApiSvc).getAccessTokenForKMSAccess(any());

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSCloudProviderAccessSvc.class), "_gcpApiSvc", gcpApiSvc);

    // 1. Provision gcpContainer
    // 2. Create SA
    final ObjectId roleId = new ObjectId();
    final String gcpProjectId = "p-projectId";
    final String serviceAccountEmail =
        "mongodb-atlas-123@" + gcpProjectId + ".iam.gserviceaccount.com";

    final NDSCloudProviderAccessGCPServiceAccountView gcpServiceAccountView =
        new NDSCloudProviderAccessGCPServiceAccountView(
            roleId,
            serviceAccountEmail,
            List.of(),
            new Date(),
            ServiceAccountProvisionStatus.COMPLETE);

    final GCPCloudProviderContainer gcpContainer =
        new GCPCloudProviderContainer(
            new GCPCloudProviderContainer(new ObjectId(), NDSDefaults.ATLAS_CIDR)
                .toDBObject()
                .append("id", new ObjectId())
                .append("projectId", gcpProjectId)
                .append("networkName", "networkName")
                .append("peers", new BasicDBList()));
    final NDSGroup ndsGroup = _ndsGroupDao.find(_group.getId()).orElseThrow();
    // create the container
    _ndsGroupSvc.addCloudContainer(ndsGroup, gcpContainer);

    NDSCloudProviderAccessGCPServiceAccount serviceAccount = null;
    try {
      serviceAccount =
          _ndsCloudProviderAccessSvc.addGCPServiceAccountToCloudProviderAccess(
              _group, gcpServiceAccountView, null);
    } catch (final Exception pE) {
      fail("GCP Service Account creation failed: {}", pE);
    }

    // Generate GCP accessTokens for the serviceAccount
    // invalid roleId
    {
      final String path =
          String.format(
              "/nds/cloudProviderAccess/agent/%s/credentials/gcp/roles/%s",
              _group.getId(), new ObjectId());
      doAgentApiCallPostWithStatus(
          _group, _apiKey, path, new JSONObject(), HttpStatus.SC_NOT_FOUND);
    }

    // valid credentials
    {
      final String path =
          String.format(
              "/nds/cloudProviderAccess/agent/%s/credentials/gcp/roles/%s",
              _group.getId(), serviceAccount.getRoleId());
      doAgentApiCallPostWithStatus(_group, _apiKey, path, new JSONObject(), HttpStatus.SC_OK);
    }
  }

  @Test
  public void testCreateTemporaryAWSAssumeRoleCredentialsForAgent() throws Exception {
    final AWSApiSvc apiSvc = mock(AWSApiSvc.class);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSCloudProviderAccessSvc.class), "_awsApiSvc", apiSvc);

    final Credentials credentials = mock(Credentials.class);
    doReturn("accessKeyId").when(credentials).getAccessKeyId();
    doReturn("secretAccessKey").when(credentials).getSecretAccessKey();
    doReturn("sessionToken").when(credentials).getSessionToken();
    doReturn(new Date()).when(credentials).getExpiration();

    doReturn(credentials)
        .when(apiSvc)
        .assumeRole(any(ObjectId.class), any(), any(), any(), any(), any(), any(), any(), any());

    final NDSCloudProviderAccessAWSIAMRole role =
        NDSCloudProviderAccessAWSIAMRole.builder()
            .roleId(new ObjectId())
            .iamAssumedRoleArn("arn:aws:iam::************:role/buttered-role-arn-1")
            .atlasAssumedRoleExternalId(UUID.randomUUID().toString())
            .atlasAWSAccountArn("arn:aws:iam::************:root")
            .atlasAWSAccountId(new ObjectId())
            .featureUsages(List.of())
            .authorizedDate(new Date())
            .createdDate(new Date())
            .build();

    _ndsGroupDao.addAwsIamRoleToCloudProviderAccess(_group.getId(), role);

    // group does not exist
    {
      final String path =
          String.format(
              "/nds/cloudProviderAccess/agent/%s/credentials/aws/roles/%s",
              new ObjectId(), role.getRoleId());
      doAgentApiCallPostWithStatus(
          _group, _apiKey, path, new JSONObject(), HttpStatus.SC_BAD_REQUEST);
    }

    // role does not exist
    {
      final String path =
          String.format(
              "/nds/cloudProviderAccess/agent/%s/credentials/aws/roles/%s",
              _group.getId(), new ObjectId());

      doAgentApiCallPostWithStatus(
          _group, _apiKey, path, new JSONObject(), HttpStatus.SC_NOT_FOUND);
    }

    // creds generated successfully
    {
      final String path =
          String.format(
              "/nds/cloudProviderAccess/agent/%s/credentials/aws/roles/%s",
              _group.getId(), role.getRoleId());
      final JSONObject responseBody =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject()));

      assertEquals("accessKeyId", responseBody.getString("accessKey"));
      assertEquals("secretAccessKey", responseBody.getString("accessSecret"));
      assertEquals("sessionToken", responseBody.getString("sessionToken"));
      assertNotNull(responseBody.getString("expirationDate"));
    }
  }

  @Test
  public void testCreateTemporaryCredentialsForAgent() throws Exception {
    final AWSApiSvc apiSvc = mock(AWSApiSvc.class);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(DataSetCredentialsSvc.class), "_awsApiSvc", apiSvc);

    final Credentials credentials = mock(Credentials.class);
    doReturn("accessKeyId").when(credentials).getAccessKeyId();
    doReturn("secretAccessKey").when(credentials).getSecretAccessKey();
    doReturn("sessionToken").when(credentials).getSessionToken();
    doReturn(new Date()).when(credentials).getExpiration();

    doReturn(credentials)
        .when(apiSvc)
        .getTemporarySessionCredentials(any(AWSAccount.class), any(), any(), any(), any(), any());

    // attempt to access another group
    {
      final String path =
          String.format(
              "/nds/cloudProviderAccess/agent/%s/credentials/aws/region/%s",
              _anotherGroup.getId(), AWSRegionName.US_EAST_1.getValue());
      doAgentApiCallPostWithStatus(
          _group, _apiKey, path, new JSONObject(), HttpStatus.SC_UNAUTHORIZED);
    }

    // creds generated successfully
    {
      final String path =
          String.format(
              "/nds/cloudProviderAccess/agent/%s/credentials/aws/region/%s",
              _group.getId(), AWSRegionName.US_EAST_1.getValue());
      final JSONObject responseBody =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject()));

      assertEquals("accessKeyId", responseBody.getString("accessKey"));
      assertEquals("secretAccessKey", responseBody.getString("accessSecret"));
      assertEquals("sessionToken", responseBody.getString("sessionToken"));
      assertNotNull(responseBody.getString("expirationDate"));
    }
  }

  @Test
  public void testCreateTemporaryFTDCExportCredentialsForAgent() throws Exception {
    final AWSApiSvc apiSvc = mock(AWSApiSvc.class);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(DataSetCredentialsSvc.class), "_awsApiSvc", apiSvc);

    final Credentials credentials = mock(Credentials.class);
    doReturn("accessKeyId").when(credentials).getAccessKeyId();
    doReturn("secretAccessKey").when(credentials).getSecretAccessKey();
    doReturn("sessionToken").when(credentials).getSessionToken();
    doReturn(new Date()).when(credentials).getExpiration();

    doReturn(credentials)
        .when(apiSvc)
        .getTemporarySessionCredentials(any(AWSAccount.class), any(), any(), any(), any(), any());

    // attempt to access another group
    {
      final String path =
          String.format(
              "/nds/cloudProviderAccess/agent/%s/credentials/ftdc/aws/%s/%s",
              _anotherGroup.getId(), HOSTNAME, AWSRegionName.US_EAST_1.getValue());
      doAgentApiCallPostWithStatus(
          _group, _apiKey, path, new JSONObject(), HttpStatus.SC_UNAUTHORIZED);
    }

    // creds generated successfully for RS
    {
      final String path =
          String.format(
              "/nds/cloudProviderAccess/agent/%s/credentials/ftdc/aws/%s/%s",
              _group.getId(), HOSTNAME, AWSRegionName.US_EAST_1.getValue());
      final JSONObject responseBody =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject()));

      assertEquals("accessKeyId", responseBody.getString("accessKey"));
      assertEquals("secretAccessKey", responseBody.getString("accessSecret"));
      assertEquals("sessionToken", responseBody.getString("sessionToken"));
      assertNotNull(responseBody.getString("expirationDate"));
    }
  }
}
