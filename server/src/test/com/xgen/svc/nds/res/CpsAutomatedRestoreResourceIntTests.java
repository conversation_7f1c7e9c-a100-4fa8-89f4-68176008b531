package com.xgen.svc.nds.res;

import static org.junit.Assert.assertFalse;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.module._public.util.CloudProviderRegistryUtil;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware.ReplicaSetHardwareIds;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.BaseResourceTestCommon;
import com.xgen.svc.core.GuiceTestRunner;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(GuiceTestRunner.class)
public class CpsAutomatedRestoreResourceIntTests extends BaseResourceTestCommon {
  private static final ObjectId GROUP_ID_SHARDED_CLUSTERS = oid(155); // group with sharded clusters
  private static final ObjectId SNAPSHOT_ID_SHARDED_CLUSTER = oid(5); // sharded cluster snapshot

  private static final ObjectId GROUP_ID_REPLICA_SETS = oid(148); // group with replica sets
  private static final ObjectId SNAPSHOT_ID_REPLICA_SET = oid(10); // replica set snapshot

  private GroupDao _groupDao;
  private ReplicaSetHardwareDao _replicaSetHardwareDao;

  @BeforeClass
  public static void prepareEnv() {
    BaseResourceTestCommon.prepareEnv();
    CloudProviderRegistryUtil.registerAllProvider();
  }

  @AfterClass
  public static void deregisterProviders() {
    CloudProviderRegistryUtil.deregisterAllProvider();
  }

  @Override
  @Before
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSGroupDao/ndsGroups.json.ftl", null, NDSGroupDao.DB, NDSGroupDao.COLLECTION);

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/ClusterDescriptionDao/clusterDescriptions.json.ftl",
        null,
        "nds",
        "config.nds.clusterDescriptions");

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupSnapshotDao/backupSnapshots.json.ftl",
        null,
        "nds",
        "config.nds.backup.snapshots");

    // populate AutomationConfig for the sharded cluster
    final Map<String, Object> clusterConfigDataParams =
        Map.of(
            "groupId",
            GROUP_ID_SHARDED_CLUSTERS,
            "provisionedHostname0",
            "provisionedHostname0",
            "provisionedHostname2",
            "provisionedHostname2");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/atm/AutomationConfigDao/config-basic-sharded.json.ftl",
        clusterConfigDataParams,
        AutomationConfig.DB_NAME,
        AutomationConfig.COLLECTION_NAME);

    // populate AutomationConfig for the replica set
    final Map<String, Object> configDataParamsReplicaSet =
        Map.of(
            "groupId", GROUP_ID_REPLICA_SETS,
            "rsId", "Azure5-shard-0",
            "provisionedHostname0", "provisionedHostname0",
            "provisionedHostname1", "provisionedHostname1",
            "provisionedHostname2", "provisionedHostname2");
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/atm/AutomationConfigDao/config-basic-rs.json.ftl",
        configDataParamsReplicaSet,
        AutomationConfig.DB_NAME,
        AutomationConfig.COLLECTION_NAME);

    _groupDao = AppConfig.getInstance(GroupDao.class);
    _replicaSetHardwareDao = AppConfig.getInstance(ReplicaSetHardwareDao.class);
  }

  @Test
  public void testGetDestinationReplicaSetsForTenantRestore() {
    final Group group = _groupDao.findById(GROUP_ID_REPLICA_SETS);
    final AppUser user =
        MmsFactory.createUser(group, "<EMAIL>", Set.of(Role.GROUP_READ_ONLY));
    final String url =
        String.format(
            "/automation/restore/tenant/%s/destinationReplicaSets?snapshotId=%s",
            GROUP_ID_REPLICA_SETS, SNAPSHOT_ID_REPLICA_SET);
    final JSONObject responseJson = doAuthedJsonGet(user, url, HttpStatus.SC_OK);
    final JSONArray destinationItemsJsonArray = responseJson.getJSONArray("destinationItems");
    assertFalse(destinationItemsJsonArray.isEmpty());
  }

  @Test
  public void testGetDestinationReplicaSets() {
    final Group group = _groupDao.findById(GROUP_ID_REPLICA_SETS);
    final AppUser user =
        MmsFactory.createUser(group, "<EMAIL>", Set.of(Role.GROUP_READ_ONLY));
    final String url =
        String.format(
            "/automation/restore/%s/destinationReplicaSetsCps?snapshotId=%s",
            GROUP_ID_REPLICA_SETS, SNAPSHOT_ID_REPLICA_SET);
    final JSONObject responseJson = doAuthedJsonGet(user, url, HttpStatus.SC_OK);
    final JSONArray destinationItemsJsonArray = responseJson.getJSONArray("destinationItems");
    assertFalse(destinationItemsJsonArray.isEmpty());
  }

  @Test
  public void testGetDestinationClustersCps() {
    final Group group = _groupDao.findById(GROUP_ID_SHARDED_CLUSTERS);
    final AppUser user =
        MmsFactory.createUser(group, "<EMAIL>", Set.of(Role.GROUP_READ_ONLY));
    final ObjectId replicationSpecId = oid(1013);
    final Iterator<ReplicaSetHardwareIds> replicaSetNames =
        ReplicaSetHardware.getUnusedNonConfigReplicaSetHardwareIds(
            group.getId(), "Cluster 2", "Cluster 2", List.of());
    for (int idx = 0; idx < 3; idx++) {
      final BasicDBObject replicaSetHardwareId =
          ReplicaSetHardware.createNonConfigReplicaSetId(
              "Cluster 2", GROUP_ID_SHARDED_CLUSTERS, idx);
      _replicaSetHardwareDao.create(
          replicaSetHardwareId, replicaSetNames.next().rsId(), true, false, replicationSpecId);
    }
    final String url =
        String.format(
            "/automation/restore/%s/destinationClustersCps?snapshotId=%s",
            GROUP_ID_SHARDED_CLUSTERS, SNAPSHOT_ID_SHARDED_CLUSTER);
    final JSONObject responseJson = doAuthedJsonGet(user, url, HttpStatus.SC_OK);
    final JSONArray destinationItemsJsonArray = responseJson.getJSONArray("destinationItems");
    assertFalse(destinationItemsJsonArray.isEmpty());
  }

  @Test
  public void testGetDestinationClustersCpsPit() {
    final Group group = _groupDao.findById(GROUP_ID_REPLICA_SETS);
    final AppUser user =
        MmsFactory.createUser(group, "<EMAIL>", Set.of(Role.GROUP_READ_ONLY));
    final String clusterName = "Azure5";
    final String pitUTCSeconds = "1734438800";
    final String url =
        String.format(
            "/automation/restore/%s/destinationClustersCpsPit?sourceGroupId=%s&sourceClusterName=%s&pitUTCSeconds=%s",
            GROUP_ID_REPLICA_SETS, GROUP_ID_REPLICA_SETS, clusterName, pitUTCSeconds);
    final JSONObject responseJson = doAuthedJsonGet(user, url, HttpStatus.SC_OK);
    final JSONArray destinationItemsJsonArray = responseJson.getJSONArray("destinationItems");
    assertFalse(destinationItemsJsonArray.isEmpty());
  }
}
