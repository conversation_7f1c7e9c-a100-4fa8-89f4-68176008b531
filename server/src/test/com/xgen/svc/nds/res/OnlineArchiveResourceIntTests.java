package com.xgen.svc.nds.res;

import static com.xgen.cloud.nds.activity._public.event.audit.NDSAudit.Type.ONLINE_ARCHIVE_QUERY_LOGS_DOWNLOADED;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.util._public.compression.GZipCompressionUtils;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.CollectionType;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DataProcessRegion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DateCriteria;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DateCriteria.DateFormat;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.State;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveDataLakeConfig;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveRun;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveRun.ArchiveRunState;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.mhouse.services.billinglimits.v1.Models.DataScanningLimitStatus;
import com.xgen.mhouse.services.billinglimits.v1.Models.LimitSpan;
import com.xgen.mhouse.services.billinglimits.v1.Models.UsageLimit;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.OnlineArchiveView;
import com.xgen.svc.nds.svc.NDSDataLakeTenantSvc;
import com.xgen.svc.nds.svc.adl.DataLakeTestUtils;
import com.xgen.svc.nds.svc.adl.MockRegionsService;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class OnlineArchiveResourceIntTests extends JUnit5BaseResourceTest {
  @Inject OnlineArchiveSvc _onlineArchiveSvc;
  @Inject OnlineArchiveDao _onlineArchiveDao;

  @Inject ClusterDescriptionDao _clusterDescriptionDao;

  @Inject NDSGroupSvc _groupSvc;

  @Inject AuditSvc _auditSvc;
  @Inject NDSDataLakeTenantSvc _ndsDataLakeTenantSvc;
  @Inject DataLakeTestUtils _dataLakeTestUtils;
  @Inject private FeatureFlagSvc _featureFlagSvc;

  private Organization _organization;
  private Group _group;
  private AppUser _user;
  private AppUser _groupDataAccessReadOnly;
  private AppUser _userReadOnly;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    _dataLakeTestUtils.setUp();
    _organization = MmsFactory.createOrganizationWithNDSPlan();
    _group = MmsFactory.createGroup(_organization, "ndsCustomer1");
    _groupSvc.ensureGroup(_group.getId());
    _user = MmsFactory.createUser(_group);
    _groupDataAccessReadOnly =
        MmsFactory.createUserWithRoleInGroup(
            _group,
            String.format("dataAccessReadOnlyUser%s", getUniquifier()),
            Role.GROUP_DATA_ACCESS_READ_ONLY);
    _userReadOnly =
        MmsFactory.createUserWithRoleInGroup(
            _group, String.format("<EMAIL>", getUniquifier()), Role.GROUP_READ_ONLY);
  }

  @AfterEach
  public void teardown() {
    _dataLakeTestUtils.teardown();
  }

  @Test
  public void testGetDataSetMetrics() {
    final String dataSetName = "dataSetName";
    final boolean isUpdateInProgress = true;
    final Long totalDocumentCount = 100L;
    final Long uncompressedSizeInBytes = 2000L;

    Calendar min = Calendar.getInstance();
    min.set(Calendar.YEAR, 2020);
    min.set(Calendar.MONTH, 0);
    min.set(Calendar.DAY_OF_MONTH, 1);
    min.set(Calendar.HOUR, 1);
    min.set(Calendar.MINUTE, 0);
    min.set(Calendar.SECOND, 0);
    min.set(Calendar.AM_PM, Calendar.AM);
    final Date minDateField = min.getTime();

    Calendar max = Calendar.getInstance();
    max.set(Calendar.YEAR, 2021);
    max.set(Calendar.MONTH, 0);
    max.set(Calendar.DAY_OF_MONTH, 1);
    max.set(Calendar.HOUR, 1);
    max.set(Calendar.MINUTE, 0);
    max.set(Calendar.SECOND, 0);
    max.set(Calendar.AM_PM, Calendar.AM);
    final Date maxDateField = max.getTime();

    _dataLakeTestUtils.setDataSetMetrics(
        dataSetName,
        isUpdateInProgress,
        totalDocumentCount,
        uncompressedSizeInBytes,
        minDateField,
        maxDateField);

    final ObjectId pGroupId = ObjectId.get();
    final String clusterName = "clusterName";

    final OnlineArchive onlineArchive =
        getOnlineArchive(
            pGroupId, clusterName, CollectionType.valueOf("STANDARD"), OnlineArchiveVersion.V3);

    final OnlineArchive updateOnlineArchive =
        onlineArchive
            .copy()
            .setDataSetName(dataSetName)
            .setDataProcessRegion(new DataProcessRegion("AWS", "US_EAST_1"))
            .build();

    _onlineArchiveDao.insertMajority(updateOnlineArchive);

    final String url =
        String.format(
            "/nds/%s/onlineArchives/%s/%s/metrics",
            _group.getId(), clusterName, updateOnlineArchive.getId());
    final JSONObject onlineArchiveMetricsView = doAuthedJsonGet(_user, url, HttpStatus.SC_OK);
    assertEquals(onlineArchiveMetricsView.getLong("archivedDataSizeInBytes"), 2000L);
    assertEquals(onlineArchiveMetricsView.getLong("numArchivedDocuments"), 100L);
    assertNotNull(onlineArchiveMetricsView.getString("minDateField"), "2020-01-01T01:00:00Z");
    assertEquals(onlineArchiveMetricsView.getString("maxDateField"), "2021-01-01T01:00:00Z");
    assertTrue(onlineArchiveMetricsView.getBoolean("isStale"));
    assertFalse(onlineArchiveMetricsView.getBoolean("metricsNotAvailable"));
    assertEquals(onlineArchiveMetricsView.get("metricsNotAvailableReason"), JSONObject.NULL);
  }

  @Test
  public void testOnlineArchiveViewSetsVersionCorrectly() {
    final String clusterName1 = "Cluster1";
    final OnlineArchiveView standardArchiveView =
        getOnlineArchiveView(
            _group.getId(), clusterName1, CollectionType.STANDARD, OnlineArchiveVersion.UNKNOWN);
    final OnlineArchive archive1 =
        standardArchiveView.toOnlineArchive(_group.getId(), clusterName1, OnlineArchiveVersion.V2);
    assertEquals(OnlineArchiveVersion.V2, archive1.getOnlineArchiveVersion());

    final String clusterName2 = "Cluster2";
    final OnlineArchiveView timeseriesArchiveView =
        getOnlineArchiveView(
            _group.getId(), clusterName2, CollectionType.TIMESERIES, OnlineArchiveVersion.V2);
    final OnlineArchive timeseriesArchive2 =
        timeseriesArchiveView.toOnlineArchive(
            _group.getId(), clusterName1, OnlineArchiveVersion.V2);
    assertEquals(OnlineArchiveVersion.V2, timeseriesArchive2.getOnlineArchiveVersion());
  }

  @Test
  public void testGetOnlineArchivesForCluster() throws Exception {
    final String clusterName = "Cluster0";
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));

    final String url = String.format("/nds/%s/onlineArchives/%s/", _group.getId(), clusterName);
    final JSONArray resp0 = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
    assertEquals(0, resp0.length());

    _onlineArchiveSvc.create(
        getStandardV1OnlineArchive(_group.getId(), clusterName), AuditInfoHelpers.fromSystem());
    final JSONArray resp1 = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
    assertEquals(1, resp1.length());
  }

  @Test
  public void testGetOnlineArchivesForGroup() throws Exception {
    final String clusterName = "Cluster0";
    final String clusterName1 = "Cluster1";
    final String clusterName2 = "Cluster2";
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName1));
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName2));

    final String url = String.format("/nds/%s/onlineArchives/", _group.getId());
    final JSONArray resp0 = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
    assertEquals(0, resp0.length());

    _onlineArchiveSvc.create(
        getStandardV1OnlineArchive(_group.getId(), clusterName), AuditInfoHelpers.fromSystem());
    final JSONArray resp1 = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
    assertEquals(1, resp1.length());

    _onlineArchiveSvc.create(
        getStandardV1OnlineArchive(_group.getId(), clusterName1), AuditInfoHelpers.fromSystem());
    final JSONArray resp2 = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
    assertEquals(2, resp2.length());

    // this creates V2 OA instead of V1
    _onlineArchiveSvc.create(
        getStandardV2OnlineArchive(_group.getId(), clusterName2), AuditInfoHelpers.fromSystem());
    final JSONArray resp3 = doAuthedJsonArrayGet(_user, url, HttpStatus.SC_OK);
    assertEquals(3, resp3.length());
  }

  @Test
  public void testCreateOnlineArchive() throws Exception {
    final String clusterName = "Cluster0";

    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));

    final JSONObject body = new JSONObject();
    body.put("clusterName", clusterName);
    body.put("projectId", _group.getId().toString());
    body.put("dbName", "foo");
    body.put("collName", "bar");

    final JSONObject criteria = new JSONObject();
    criteria.put("type", "DATE");
    criteria.put("dateField", "createDate");
    criteria.put("expireAfterDays", 30);
    criteria.put("dateFormat", "ISODATE");
    body.put("criteria", criteria);

    final JSONArray partitionFields0 =
        new JSONArray().put(new JSONObject().put("fieldName", "createDate").put("order", 0));
    body.put("partitionFields", partitionFields0);

    final String url = String.format("/nds/%s/onlineArchives/%s/", _group.getId(), clusterName);

    // 1 required partitionFields and dateCriteria not include
    doAuthedJsonPost(_user, url, body);

    final List<OnlineArchive> created =
        _onlineArchiveSvc.getArchivesForCluster(_group.getId(), clusterName);

    assertEquals(1, created.size());
    assertEquals(OnlineArchive.State.PENDING, created.get(0).getState());
    assertEquals(1, created.get(0).getPartitionFields().size()); // date field
    assertEquals(OnlineArchive.CollectionType.STANDARD, created.get(0).getCollectionType());
    assertEquals(OnlineArchive.CriteriaType.DATE, created.get(0).getCriteria().getType());
    assertTrue(created.get(0).getCriteria() instanceof DateCriteria);
    assertEquals((Integer) 30, ((DateCriteria) created.get(0).getCriteria()).getExpireAfterDays());
    assertTrue(created.get(0).getDataExpirationRule().isEmpty());

    // Check that the default monthly limit has been created
    final OnlineArchiveDataLakeConfig onlineArchiveDataLakeConfig =
        _onlineArchiveSvc.getValidateOnlineArchiveDataLakeConfig(_group.getId(), clusterName);

    List.of(
            onlineArchiveDataLakeConfig.getDataLakeTenantId(),
            onlineArchiveDataLakeConfig.getArchiveOnlyDataLakeTenantId())
        .forEach(
            pNDSDataLakeTenantId -> {
              final ObjectId tenantId;
              try {
                tenantId =
                    _ndsDataLakeTenantSvc.getTenantId(
                        pNDSDataLakeTenantId.getGroupId(), pNDSDataLakeTenantId.getName());
              } catch (final Exception pE) {
                fail("Get tenant thrown an exception " + pE.getMessage());
                return;
              }
              final UsageLimit actualUsageLimit =
                  _dataLakeTestUtils
                      .getAwsUsageLimit(
                          _group.getId().toHexString(),
                          tenantId.toHexString(),
                          "LIMIT_SPAN_MONTHLY")
                      .map(DataScanningLimitStatus::getUsageLimit)
                      .orElseThrow();
              assertEquals(
                  actualUsageLimit.getTimeSpanScanningLimit().getProjectId().getValue(),
                  _group.getId().toHexString());
              assertEquals(
                  actualUsageLimit.getTimeSpanScanningLimit().getTenantId().getValue(),
                  tenantId.toHexString());
              assertEquals(
                  actualUsageLimit.getTimeSpanScanningLimit().getLimitBytes(),
                  (long) Units.convert(100.0, Units.TERABYTES, Units.BYTES));
              assertEquals(
                  actualUsageLimit.getTimeSpanScanningLimit().getLimitSpan(),
                  LimitSpan.LIMIT_SPAN_MONTHLY);
            });

    // specify two partitionFields + dateCriteria
    final String clusterName1 = "Cluster1";
    final String url1 = String.format("/nds/%s/onlineArchives/%s/", _group.getId(), clusterName1);

    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName1));

    final JSONArray partitionFields =
        new JSONArray()
            .put(new JSONObject().put("fieldName", "createDate").put("order", 0))
            .put(new JSONObject().put("fieldName", "myfield1").put("order", 1))
            .put(new JSONObject().put("fieldName", "myfield2").put("order", 2));
    body.put("partitionFields", partitionFields);

    final JSONArray partitionFieldsExceeded = new JSONArray(partitionFields.toList());
    partitionFieldsExceeded.put(new JSONObject().put("fieldName", "myfield3").put("order", 3));

    // test partition fields limit exceeded
    body.put("partitionFields", partitionFieldsExceeded);
    final JSONObject serverPartitionFieldsExceededResponse =
        doAuthedJsonPost(_user, url1, body, HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.PARTITION_FIELDS_LIMIT_EXCEEDED.name(),
        serverPartitionFieldsExceededResponse.get(ApiError.ERROR_CODE_FIELD));

    // test create successfully with valid partition fields
    final JSONArray partitionFields1 = new JSONArray(partitionFields.toList());
    body.put("partitionFields", partitionFields1);
    doAuthedJsonPost(_user, url1, body);

    final List<OnlineArchive> created1 =
        _onlineArchiveSvc.getArchivesForCluster(_group.getId(), clusterName1);

    assertEquals(1, created1.size());
    assertEquals(OnlineArchive.State.PENDING, created1.get(0).getState());
    assertEquals(3, created1.get(0).getPartitionFields().size());
    assertEquals(
        criteria.getString("dateField"),
        created1.get(0).getPartitionFields().get(0).getFieldName());
    assertEquals((Integer) 2, created1.get(0).getPartitionFields().get(2).getOrder());

    // test create with data expiration rule

    final String clusterName2 = "Cluster2";
    final String url2 = String.format("/nds/%s/onlineArchives/%s/", _group.getId(), clusterName2);

    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName2));

    final JSONObject dataExpirationRule = new JSONObject();
    dataExpirationRule.put("expireAfterDays", 45);
    body.put("dataExpirationRule", dataExpirationRule);
    doAuthedJsonPost(_user, url2, body);

    final List<OnlineArchive> created2 =
        _onlineArchiveSvc.getArchivesForCluster(_group.getId(), clusterName2);
    assertEquals(1, created2.size());
    assertEquals(OnlineArchive.State.PENDING, created2.get(0).getState());
    assertTrue(created2.get(0).getDataExpirationRule().isPresent());
    assertEquals(45, created2.get(0).getDataExpirationRule().get().getExpireAfterDays().intValue());
    assertNotNull(created2.get(0).getDataExpirationRule().get().getEnabledOn());
    // test serverless instance
    final String serverlessInstanceName = "Serverless0";

    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getDefaultServerlessClusterDescription(
            _group.getId(), serverlessInstanceName));

    final JSONObject serverlessResponse =
        doAuthedJsonPost(
            _user,
            String.format("/nds/%s/onlineArchives/%s/", _group.getId(), serverlessInstanceName),
            body,
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.ONLINE_ARCHIVE_NOT_AVAILABLE_TENANT_CLUSTER.name(),
        serverlessResponse.get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testPauseAndResumeOnlineArchiveV3() throws Exception {
    final String clusterName = "Cluster0";

    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));
    final OnlineArchive newArchive = getStandardV3OnlineArchive(_group.getId(), clusterName);
    _onlineArchiveSvc.create(newArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(newArchive.getId(), AuditInfoHelpers.fromSystem());
    final OnlineArchive archive = _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
    assertEquals(OnlineArchive.State.ACTIVE, archive.getState());

    final String updateURL =
        String.format(
            "/nds/%s/onlineArchives/%s/%s", _group.getId(), clusterName, newArchive.getId());

    // pausing archive
    {
      final JSONObject pausingRequestBody = new JSONObject();
      pausingRequestBody.put(OnlineArchive.FieldDefs.ARCHIVE_STATE, State.PAUSING);
      doAuthedJsonPatch(_user, updateURL, pausingRequestBody);

      final OnlineArchive pausingRequestedArchive =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      assertEquals(State.PAUSING, pausingRequestedArchive.getState());

      _onlineArchiveSvc.setPauseCompleted(newArchive.getId(), AuditInfoHelpers.fromSystem());

      final OnlineArchive pausedRequestedArchive =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      assertEquals(State.PAUSED, pausedRequestedArchive.getState());
    }

    // resume archive
    {
      final JSONObject resumeBody = new JSONObject();
      resumeBody.put(OnlineArchive.FieldDefs.ARCHIVE_STATE, OnlineArchive.State.ACTIVE);
      doAuthedJsonPatch(_user, updateURL, resumeBody);

      final OnlineArchive resumedArchive =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      assertEquals(OnlineArchive.State.ACTIVE, resumedArchive.getState());
    }
  }

  @Test
  public void testUpdateOnlineArchive() throws Exception {
    final String clusterName = "Cluster0";
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));

    final OnlineArchive newArchive = getStandardV1OnlineArchive(_group.getId(), clusterName);
    final OnlineArchive.DateCriteria originalDateCriteria =
        (OnlineArchive.DateCriteria) newArchive.getCriteria();
    _onlineArchiveSvc.create(newArchive, AuditInfoHelpers.fromSystem());
    _onlineArchiveSvc.activate(newArchive.getId(), AuditInfoHelpers.fromSystem());
    final OnlineArchive archive = _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
    assertEquals(OnlineArchive.State.ACTIVE, archive.getState());

    final String updateURL =
        String.format(
            "/nds/%s/onlineArchives/%s/%s", _group.getId(), clusterName, newArchive.getId());

    // pausing archive
    {
      final JSONObject pausingRequestBody = new JSONObject();
      pausingRequestBody.put(OnlineArchive.FieldDefs.ARCHIVE_STATE, State.PAUSING);
      doAuthedJsonPatch(_user, updateURL, pausingRequestBody);

      final OnlineArchive pauseRequestedArchive =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      assertEquals(State.PAUSING, pauseRequestedArchive.getState());
    }

    // resume archive
    {
      final JSONObject resumeBody = new JSONObject();
      resumeBody.put(OnlineArchive.FieldDefs.ARCHIVE_STATE, OnlineArchive.State.ACTIVE);
      doAuthedJsonPatch(_user, updateURL, resumeBody);

      final OnlineArchive resumedArchive =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      assertEquals(OnlineArchive.State.ACTIVE, resumedArchive.getState());
    }

    // paused completed
    _onlineArchiveSvc.setPauseCompleted(newArchive.getId(), AuditInfoHelpers.fromSystem());

    // resume archive again
    {
      final JSONObject resumeBody = new JSONObject();
      resumeBody.put(OnlineArchive.FieldDefs.ARCHIVE_STATE, OnlineArchive.State.ACTIVE);
      doAuthedJsonPatch(_user, updateURL, resumeBody);

      final OnlineArchive resumedArchive =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      assertEquals(OnlineArchive.State.ACTIVE, resumedArchive.getState());
    }

    // edit archive expireAfterDays
    {
      final JSONObject body = new JSONObject();
      final JSONObject criteriaUpdate = new JSONObject();
      final Integer newExpireAfterDays = originalDateCriteria.getExpireAfterDays() + 5;
      criteriaUpdate.put(OnlineArchive.Criteria.FieldDefs.TYPE, OnlineArchive.CriteriaType.DATE);
      criteriaUpdate.put(
          OnlineArchive.DateCriteria.FieldDefs.EXPIRE_AFTER_DAYS, newExpireAfterDays);
      criteriaUpdate.put(
          OnlineArchive.DateCriteria.FieldDefs.DATE_FIELD, originalDateCriteria.getDateField());
      criteriaUpdate.put(
          OnlineArchive.DateCriteria.FieldDefs.DATE_FORMAT, originalDateCriteria.getDateFormat());
      body.put(OnlineArchive.FieldDefs.CRITERIA, criteriaUpdate);
      doAuthedJsonPatch(_user, updateURL, body);

      final OnlineArchive editedArchive =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      final OnlineArchive.DateCriteria editedDateCriteria =
          (OnlineArchive.DateCriteria) editedArchive.getCriteria();

      assertEquals(newExpireAfterDays, editedDateCriteria.getExpireAfterDays());
    }

    // edit archive expireAfterDays while pausing
    {
      final JSONObject pausingRequestBody = new JSONObject();
      pausingRequestBody.put(OnlineArchive.FieldDefs.ARCHIVE_STATE, State.PAUSING);
      doAuthedJsonPatch(_user, updateURL, pausingRequestBody);

      final OnlineArchive pauseRequestedArchive =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      assertEquals(State.PAUSING, pauseRequestedArchive.getState());

      final JSONObject body1 = new JSONObject();
      final JSONObject criteriaUpdate1 = new JSONObject();
      final Integer newExpireAfterDays1 = originalDateCriteria.getExpireAfterDays() + 10;
      criteriaUpdate1.put(OnlineArchive.Criteria.FieldDefs.TYPE, OnlineArchive.CriteriaType.DATE);
      criteriaUpdate1.put(
          OnlineArchive.DateCriteria.FieldDefs.EXPIRE_AFTER_DAYS, newExpireAfterDays1);
      criteriaUpdate1.put(
          OnlineArchive.DateCriteria.FieldDefs.DATE_FIELD, originalDateCriteria.getDateField());
      criteriaUpdate1.put(
          OnlineArchive.DateCriteria.FieldDefs.DATE_FORMAT, originalDateCriteria.getDateFormat());
      body1.put(OnlineArchive.FieldDefs.CRITERIA, criteriaUpdate1);
      doAuthedJsonPatch(_user, updateURL, body1);

      final OnlineArchive editedArchive1 =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      final OnlineArchive.DateCriteria editedDateCriteria1 =
          (OnlineArchive.DateCriteria) editedArchive1.getCriteria();

      assertEquals(newExpireAfterDays1, editedDateCriteria1.getExpireAfterDays());
      assertEquals(State.PAUSING, editedArchive1.getState());
    }

    // edit archive expireAfterDays while paused
    {
      _onlineArchiveSvc.setPauseCompleted(newArchive.getId(), AuditInfoHelpers.fromSystem());

      final JSONObject body1 = new JSONObject();
      final JSONObject criteriaUpdate1 = new JSONObject();
      final Integer newExpireAfterDays1 = originalDateCriteria.getExpireAfterDays() + 10;
      criteriaUpdate1.put(OnlineArchive.Criteria.FieldDefs.TYPE, OnlineArchive.CriteriaType.DATE);
      criteriaUpdate1.put(
          OnlineArchive.DateCriteria.FieldDefs.EXPIRE_AFTER_DAYS, newExpireAfterDays1);
      criteriaUpdate1.put(
          OnlineArchive.DateCriteria.FieldDefs.DATE_FIELD, originalDateCriteria.getDateField());
      criteriaUpdate1.put(
          OnlineArchive.DateCriteria.FieldDefs.DATE_FORMAT, originalDateCriteria.getDateFormat());
      body1.put(OnlineArchive.FieldDefs.CRITERIA, criteriaUpdate1);
      doAuthedJsonPatch(_user, updateURL, body1);

      final OnlineArchive editedArchive1 =
          _onlineArchiveSvc.getOnlineArchive(newArchive.getId()).get();
      final OnlineArchive.DateCriteria editedDateCriteria1 =
          (OnlineArchive.DateCriteria) editedArchive1.getCriteria();

      assertEquals(newExpireAfterDays1, editedDateCriteria1.getExpireAfterDays());

      // ensure this bug doesn't re-appear: the state should not have been flipped to ACTIVE
      assertEquals(State.PAUSED, editedArchive1.getState());
    }

    // test serverless instance
    {
      final JSONObject body = new JSONObject();
      final JSONObject criteriaUpdate = new JSONObject();
      final Integer newExpireAfterDays = originalDateCriteria.getExpireAfterDays() + 5;
      criteriaUpdate.put(OnlineArchive.Criteria.FieldDefs.TYPE, OnlineArchive.CriteriaType.DATE);
      criteriaUpdate.put(
          OnlineArchive.DateCriteria.FieldDefs.EXPIRE_AFTER_DAYS, newExpireAfterDays);
      criteriaUpdate.put(
          OnlineArchive.DateCriteria.FieldDefs.DATE_FIELD, originalDateCriteria.getDateField());
      criteriaUpdate.put(
          OnlineArchive.DateCriteria.FieldDefs.DATE_FORMAT, originalDateCriteria.getDateFormat());
      body.put(OnlineArchive.FieldDefs.CRITERIA, criteriaUpdate);
      final String serverlessInstanceName = "Serverless0";

      _clusterDescriptionDao.saveReplicaSafe(
          NDSModelTestFactory.getDefaultServerlessClusterDescription(
              _group.getId(), serverlessInstanceName));

      final JSONObject serverlessResponse =
          doAuthedJsonPatch(
              _user,
              String.format(
                  "/nds/%s/onlineArchives/%s/%s",
                  _group.getId(), serverlessInstanceName, newArchive.getId()),
              body,
              HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.ONLINE_ARCHIVE_NOT_AVAILABLE_TENANT_CLUSTER.name(),
          serverlessResponse.get(ApiError.ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testDeleteOnlineArchive() throws Exception {
    final String clusterName = "Cluster0";
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));

    final OnlineArchive newArchive = getStandardV1OnlineArchive(_group.getId(), clusterName);
    _onlineArchiveSvc.create(newArchive, AuditInfoHelpers.fromSystem());

    final String deleteURL =
        String.format("/nds/%s/onlineArchives/%s", _group.getId(), newArchive.getId());

    // Project Data Access Read-Only user cannot delete archives
    doAuthedJsonDelete(_groupDataAccessReadOnly, deleteURL, HttpStatus.SC_FORBIDDEN);

    // Project Data Access Read-Only user cannot delete archives
    doAuthedJsonDelete(_userReadOnly, deleteURL, HttpStatus.SC_FORBIDDEN);

    doAuthedJsonDelete(_user, deleteURL, HttpStatus.SC_OK);

    // this doesn't actually delete the archive from the database, it
    // sets the state to DELETED and marks the archive for deletion by
    // the archive cleanup cron job
    final Optional<OnlineArchive> archiveOptional =
        _onlineArchiveSvc.getOnlineArchive(newArchive.getId());
    assertTrue(archiveOptional.isPresent());

    final OnlineArchive archive = archiveOptional.get();
    assertEquals(OnlineArchive.State.DELETED, archive.getState());
    assertNotNull(archive.getDeleteAfterDate());
  }

  @Test
  public void testGetDataLandingZoneSupportedRegions() {
    // Ensure multi-provider expansion is disabled for this test
    FeatureFlagIntTestUtil.disableFeatureForEntity(
        _group, null, FeatureFlag.ONLINE_ARCHIVE_CROSS_CLOUD_CREATION);
    final Set<String> clientRegions =
        MockRegionsService.getMockDataLakeClientRegions().stream()
            .filter(region -> region.getProvider().equals(CloudProvider.AWS))
            .map(RegionName::getName)
            .collect(Collectors.toUnmodifiableSet());
    final String clusterName = "Cluster0";
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));
    final String endpointURL =
        String.format("/nds/%s/onlineArchives/%s/dlsSupportedRegions", _group.getId(), clusterName);

    final JSONArray resp = doAuthedJsonArrayGet(_user, endpointURL, HttpStatus.SC_OK);
    // When multi-provider is disabled, only primary provider regions should be returned
    assertEquals(
        clientRegions,
        convertJSONArrayToJSONObjectList(resp).stream()
            .map(r -> r.getString("key"))
            .collect(Collectors.toUnmodifiableSet()));
  }

  @Test
  public void testGetDataLandingZoneSupportedRegions_CrossCloudEnabled() {
    // Enable cross-cloud creation
    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ONLINE_ARCHIVE_CROSS_CLOUD_CREATION);

    final String clusterName = "Cluster0";
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));

    final String endpointURL =
        String.format("/nds/%s/onlineArchives/%s/dlsSupportedRegions", _group.getId(), clusterName);

    final JSONArray resp = doAuthedJsonArrayGet(_user, endpointURL, HttpStatus.SC_OK);
    final List<JSONObject> regions = convertJSONArrayToJSONObjectList(resp);

    // Should include regions from at least the three dedicated providers
    final java.util.Set<String> providers =
        regions.stream()
            .map(r -> r.getString("provider"))
            .collect(java.util.stream.Collectors.toSet());
    assertTrue(providers.contains("AWS"));
    assertTrue(providers.contains("AZURE"));
    assertTrue(providers.contains("GCP"));

    // Only one recommended region and it should be the primary provider
    List<JSONObject> recommended =
        regions.stream().filter(r -> r.getBoolean("isRecommended")).toList();
    assertEquals(1, recommended.size());
    assertEquals("AWS", recommended.get(0).getString("provider"));
  }

  @Test
  public void testGetDefaultDLZSupportedRegionForCluster() {
    final String clusterName = "Cluster0";
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));
    final String endpointURL =
        String.format(
            "/nds/%s/onlineArchives/%s/defaultDLZSupportedRegion", _group.getId(), clusterName);

    final JSONObject defaultDLZSupportedRegion =
        doAuthedJsonGet(_user, endpointURL, HttpStatus.SC_OK);
    assertEquals("US_EAST_1", defaultDLZSupportedRegion.getString("key"));
    assertEquals("us-east-1", defaultDLZSupportedRegion.getString("name"));
    assertEquals(39.0437567, defaultDLZSupportedRegion.getDouble("latitude"), 0.1);
    assertEquals(-77.4874416, defaultDLZSupportedRegion.getDouble("longitude"), 0.1);
    assertEquals("N. Virginia", defaultDLZSupportedRegion.getString("location"));
    assertEquals("North America", defaultDLZSupportedRegion.getString("continent"));
    assertEquals("AWS", defaultDLZSupportedRegion.getString("provider"));
    assertTrue(defaultDLZSupportedRegion.getBoolean("isRecommended"));
  }

  @Test
  public void testGetQueryLogs() throws SvcException, IOException {
    final String clusterName = "Cluster0";
    _clusterDescriptionDao.saveReplicaSafe(
        NDSModelTestFactory.getAWSClusterDescription(_group.getId(), clusterName));
    final LocalDateTime end = LocalDateTime.now().withNano(0);
    final LocalDateTime start = end.minusDays(1);
    final long endEpoch = end.toEpochSecond(ZoneOffset.UTC);
    final long startEpoch = start.toEpochSecond(ZoneOffset.UTC);
    final String endpoint =
        String.format(
            "/nds/%s/onlineArchives/%s/queryLogs.gz?&startDate=%s&endDate=%s",
            _group.getId(), clusterName, startEpoch, endEpoch);

    {
      // cluster has no online archives
      doAuthedGetBytes(_groupDataAccessReadOnly, endpoint, HttpStatus.SC_BAD_REQUEST);
    }
    {
      // start/end dates not included
      doAuthedGetBytes(
          _groupDataAccessReadOnly,
          String.format("/nds/%s/onlineArchives/%s/queryLogs.gz", _group.getId(), clusterName),
          HttpStatus.SC_BAD_REQUEST);
    }
    {
      // user does not have sufficient permissions
      doAuthedGetBytes(_userReadOnly, endpoint, HttpStatus.SC_FORBIDDEN);
    }

    // create online archive
    final OnlineArchive archive = getStandardV1OnlineArchive(_group.getId(), clusterName);
    _onlineArchiveSvc.create(archive, AuditInfoHelpers.fromSystem());

    // ok
    {
      final byte[] logsResponse =
          doAuthedGetBytes(_groupDataAccessReadOnly, endpoint, HttpStatus.SC_OK);

      // check audit
      final List<Event> pEvents =
          _auditSvc.findByDate(ONLINE_ARCHIVE_QUERY_LOGS_DOWNLOADED, Timestamp.valueOf(end));
      assertNotNull(pEvents);
      assertEquals(1, pEvents.size());
      final NDSAudit audit = (NDSAudit) pEvents.get(0);
      assertEquals(_groupDataAccessReadOnly.getId(), audit.getUserId());
      assertEquals(_group.getId(), audit.getGroupId());
      assertEquals(clusterName, audit.getClusterName());
      assertEquals(ONLINE_ARCHIVE_QUERY_LOGS_DOWNLOADED, audit.getEventType());

      // check response content and dates
      final String response = new String(GZipCompressionUtils.ungzipBytes(logsResponse));
      assertTrue(response.contains(start.toString()));
      assertTrue(response.contains(end.toString()));
    }
  }

  private OnlineArchive getStandardV1OnlineArchive(
      final ObjectId pGroupId, final String pClusterName) {
    return getOnlineArchive(
        pGroupId, pClusterName, CollectionType.valueOf("STANDARD"), OnlineArchiveVersion.V1);
  }

  private OnlineArchive getStandardV2OnlineArchive(
      final ObjectId pGroupId, final String pClusterName) {
    return getOnlineArchive(
        pGroupId, pClusterName, CollectionType.valueOf("STANDARD"), OnlineArchiveVersion.V2);
  }

  private OnlineArchive getStandardV3OnlineArchive(
      final ObjectId pGroupId, final String pClusterName) {
    return getOnlineArchive(
        pGroupId, pClusterName, CollectionType.valueOf("STANDARD"), OnlineArchiveVersion.V3);
  }

  private OnlineArchive getTimeSeriesOnlineArchive(
      final ObjectId pGroupId, final String pClusterName, final OnlineArchiveVersion pVersion) {
    return getOnlineArchive(pGroupId, pClusterName, CollectionType.TIMESERIES, pVersion);
  }

  private OnlineArchive getOnlineArchive(
      final ObjectId pGroupId,
      final String pClusterName,
      final OnlineArchive.CollectionType pCollectionType,
      final OnlineArchiveVersion pVersion) {
    final List<PartitionField> partitionFields =
        List.of(
            new PartitionField("dateField", 0),
            new PartitionField("field1", 1),
            new PartitionField("field2", 2));

    return new OnlineArchive.Builder()
        .setArchiveId(ObjectId.get())
        .setClusterId(pGroupId, pClusterName)
        .setDbName("db")
        .setCollName("coll")
        .setCollectionType(pCollectionType)
        .setPartitionFields(partitionFields)
        .setCriteria(new OnlineArchive.DateCriteria("dateField", 5, DateFormat.ISODATE))
        .setState(OnlineArchive.State.ACTIVE)
        .setOnlineArchiveVersion(pVersion)
        .build();
  }

  private List<OnlineArchiveRun> getFiveOnlineArchiveRuns(final ObjectId archiveId) {
    return List.of(
        getBaseRun(8, archiveId),
        getBaseRun(6, archiveId),
        getBaseRun(7, archiveId),
        getBaseRun(3, archiveId),
        getBaseRun(5, archiveId));
  }

  // This method is the same as OnlineArchiveRunDaoIntTests.getBaseRun
  private OnlineArchiveRun getBaseRun(final int pHourOffset, final ObjectId archiveId) {
    final Instant now = Instant.now();
    final Date startDate = Date.from(now.plus(Duration.ofHours(pHourOffset)));
    final Date endDate = Date.from(now.plus(Duration.ofHours(pHourOffset + 1)));

    return new OnlineArchiveRun.Builder()
        .id(new ObjectId())
        .archiveId(archiveId)
        .groupId(new ObjectId())
        .clusterName("cluster1")
        .jobId("job1")
        .startDate(startDate)
        .endDate(endDate)
        .errorMessage("error")
        .numFrsJobErrors(0)
        .state(ArchiveRunState.COMPLETED)
        .build();
  }

  final OnlineArchiveView getOnlineArchiveView(
      final ObjectId pGroupId,
      final String pClusterName,
      final OnlineArchive.CollectionType pCollectionType,
      final OnlineArchiveVersion pVersion) {
    return new OnlineArchiveView(
        getOnlineArchive(pGroupId, pClusterName, pCollectionType, pVersion),
        getFiveOnlineArchiveRuns(new ObjectId()));
  }
}
