package com.xgen.svc.nds.res;

import static com.xgen.cloud.activity.AuditSvcIntTestUtils.verifyEvents;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.DBObject;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.activity._public.event.audit.ADFAAdminAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ADFAAdminAudit.Type;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.ClassModifier;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.mms.api.view.atlas._private.dataLake.ApiPrivateAtlasDataLakeTenantConfigView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordValueDataSetView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordValueView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCurrentOpView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantSettingsView;
import com.xgen.svc.nds.svc.NDSDataLakePrivateSvc;
import com.xgen.svc.nds.svc.NDSDataLakeTenantConfigBuilderSvc;
import com.xgen.svc.nds.svc.NDSDataLakeTenantSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class ADFAAdminResourceIntTests extends JUnit5BaseResourceTest {
  @Inject private AuditSvc _auditSvc;

  private AppUser _normalUser;
  private AppUser _globalADFAAdminReadUser;
  private AppUser _globalADFAAdminWriteUser;

  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    Group _group = MmsFactory.createGroupWithNDSPlan();
    _normalUser = MmsFactory.createUser(_group);
    _globalADFAAdminReadUser = MmsFactory.createADFAdminReadUser();
    _globalADFAAdminWriteUser = MmsFactory.createADFAdminWriteUser();
  }

  @Test
  public void testListAndonCords() throws Exception {
    final String url = "/nds/adfa/admin/listAndonCords";

    // Ensure a non-global admin user cannot access the endpoint.
    doAuthedJsonGet(_normalUser, url, HttpStatus.SC_FORBIDDEN);

    final DataLakeAdminApiClient dataLakeAdminApiClient = mock(DataLakeAdminApiClient.class);
    when(dataLakeAdminApiClient.listAndonCords("", "", "")).thenReturn(List.of());

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class),
        "_dataLakeAdminApiClient",
        dataLakeAdminApiClient);

    // Ensure an audit event is recorded for this action.
    verifyEvents(_auditSvc)
        .ofType(Type.ADFA_LIST_ANDON_CORDS_ACTION_COMPLETED)
        .willBeAudited()
        .during(() -> doAuthedJsonGet(_globalADFAAdminReadUser, url, HttpStatus.SC_OK));
  }

  @Test
  public void testCreateOrUpdateAndonCord() throws Exception {
    final String url = "/nds/adfa/admin/createOrUpdateAndonCord";

    // Ensure a non-global admin user cannot access the endpoint.
    doAuthedJsonPut(_normalUser, url, new JSONObject(), HttpStatus.SC_FORBIDDEN);

    // Ensure an audit event is recorded for this action.
    // We are also ensuring that the view is serialized to JSON and deserialized correctly.
    ObjectId projectId = new ObjectId();
    NDSDataLakeCreateOrUpdateAndonCordView view =
        NDSDataLakeCreateOrUpdateAndonCordView.builder()
            .name("feature")
            .state("enabled")
            .region("us-east-1")
            .value(
                NDSDataLakeCreateOrUpdateAndonCordValueView.builder()
                    .defaultValue("default")
                    .dataSets(
                        List.of(
                            NDSDataLakeCreateOrUpdateAndonCordValueDataSetView.builder()
                                .projectId(projectId.toHexString())
                                .dataSetName("dsA")
                                .dataSetValue("father")
                                .build(),
                            NDSDataLakeCreateOrUpdateAndonCordValueDataSetView.builder()
                                .projectId(projectId.toHexString())
                                .dataSetName("dsB")
                                .dataSetValue("brown")
                                .build()))
                    .build())
            .build();
    ObjectMapper mapper = new ObjectMapper();
    String jsonString = mapper.writeValueAsString(view);
    JSONObject obj = new JSONObject(jsonString);

    final DataLakeAdminApiClient dataLakeAdminApiClient = mock(DataLakeAdminApiClient.class);
    doNothing().when(dataLakeAdminApiClient).createOrUpdateAndonCord(view, false);

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class),
        "_dataLakeAdminApiClient",
        dataLakeAdminApiClient);

    verifyEvents(_auditSvc)
        .ofType(ADFAAdminAudit.Type.ADFA_CREATE_OR_UPDATE_ANDON_CORD_ACTION_COMPLETED)
        .willBeAudited(
            events -> {
              assertEquals(1, events.size());
              ADFAAdminAudit event = (ADFAAdminAudit) events.get(0);
              DBObject data = event.getData();
              assertEquals("feature", data.get("name"));
              assertEquals("enabled", data.get("state"));
              assertEquals("us-east-1", data.get("region"));

              @SuppressWarnings("unchecked")
              DBObject valueData = (DBObject) data.get("value");
              assertEquals("default", valueData.get("defaultValue"));
              assertEquals(2, valueData.get("dataSetsCount"));

              @SuppressWarnings("unchecked")
              List<DBObject> dataSets = (List<DBObject>) valueData.get("dataSets");
              assertEquals(2, dataSets.size());

              // Verify first dataset
              DBObject dataset1 = dataSets.get(0);
              assertEquals(projectId.toHexString(), dataset1.get("projectId"));
              assertEquals("dsA", dataset1.get("dataSetName"));
              assertEquals("father", dataset1.get("dataSetValue"));

              // Verify second dataset
              DBObject dataset2 = dataSets.get(1);
              assertEquals(projectId.toHexString(), dataset2.get("projectId"));
              assertEquals("dsB", dataset2.get("dataSetName"));
              assertEquals("brown", dataset2.get("dataSetValue"));
            })
        .during(
            () -> {
              doAuthedJsonPut(_globalADFAAdminWriteUser, url, obj, HttpStatus.SC_OK);
            });
  }

  @Test
  public void testGetTenantSettings() throws Exception {
    final ObjectId tenantId = new ObjectId();
    final String url =
        String.format("/nds/adfa/admin/getTenantSettings?tenantId=%s", tenantId.toHexString());

    // Ensure a non-global admin user cannot access the endpoint.
    doAuthedJsonGet(_normalUser, url, HttpStatus.SC_FORBIDDEN);

    final NDSDataLakeTenantSvc ndsDataLakeTenantSvc = mock(NDSDataLakeTenantSvc.class);
    final NDSDataLakeTenant tenant = NDSDataLakeTenant.builder().tenantId(tenantId).build();
    when(ndsDataLakeTenantSvc.findByTenantId(tenantId)).thenReturn(Optional.ofNullable(tenant));

    final DataLakeAdminApiClient dataLakeAdminApiClient = mock(DataLakeAdminApiClient.class);
    Assertions.assertNotNull(tenant);
    when(dataLakeAdminApiClient.getTenantSettings(tenant))
        .thenReturn(NDSDataLakeTenantSettingsView.builder().build());

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class), "_tenantSvc", ndsDataLakeTenantSvc);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class),
        "_dataLakeAdminApiClient",
        dataLakeAdminApiClient);

    // Ensure an audit event is recorded for this action.
    verifyEvents(_auditSvc)
        .ofType(Type.ADFA_TENANT_GET_SETTINGS_ACTION_COMPLETED)
        .willBeAudited(
            events -> {
              assertEquals(1, events.size());
              ADFAAdminAudit event = (ADFAAdminAudit) events.get(0);
              assertEquals(tenantId, event.getTenantId());
            })
        .during(() -> doAuthedJsonGet(_globalADFAAdminReadUser, url, HttpStatus.SC_OK));
  }

  @Test
  public void testSetTenantSettings() throws Exception {
    final ObjectId tenantId = new ObjectId();
    final String url =
        String.format("/nds/adfa/admin/setTenantSettings?tenantId=%s", tenantId.toHexString());

    // Ensure a non-global admin user cannot access the endpoint.
    doAuthedJsonPut(_normalUser, url, new JSONObject(), HttpStatus.SC_FORBIDDEN);

    // Ensure an audit event is recorded for this action.
    final Date modifiedAt = new Date();
    NDSDataLakeTenantSettingsView view =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(10)
            .maxConnections(10)
            .allowUnlimitedConcurrentQueries(false)
            .parquetWriterVersion(2)
            .tracingEnabled(true)
            .cursorMaxFileSize(10)
            .cursorMaxFiles(1000)
            .cursorMaxWaitTimeForAvailableSpace(1000)
            .queryExecutionBranchingDepth(2)
            .queryExecutionBranchingFactor(4)
            .queryExecutionMaxConcurrency(10)
            .queryExecutionMaxSerialNum(10)
            .queryExecutionMaxSerialSize(10)
            .queryRoutingStrategy("round_robin")
            .build();

    ObjectMapper mapper = new ObjectMapper();
    String jsonString = mapper.writeValueAsString(view);
    JSONObject obj = new JSONObject(jsonString);

    final NDSDataLakeTenantSvc ndsDataLakeTenantSvc = mock(NDSDataLakeTenantSvc.class);
    final NDSDataLakeTenant tenant = NDSDataLakeTenant.builder().tenantId(tenantId).build();
    when(ndsDataLakeTenantSvc.findByTenantId(tenantId)).thenReturn(Optional.ofNullable(tenant));

    final DataLakeAdminApiClient dataLakeAdminApiClient = mock(DataLakeAdminApiClient.class);
    Assertions.assertNotNull(tenant);
    doNothing().when(dataLakeAdminApiClient).setTenantSettings(tenant, view);

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class), "_tenantSvc", ndsDataLakeTenantSvc);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class),
        "_dataLakeAdminApiClient",
        dataLakeAdminApiClient);

    verifyEvents(_auditSvc)
        .ofType(Type.ADFA_TENANT_SET_SETTINGS_ACTION_COMPLETED)
        .willBeAudited(
            events -> {
              assertEquals(1, events.size());
              ADFAAdminAudit event = (ADFAAdminAudit) events.get(0);
              assertEquals(tenantId, event.getTenantId());
              DBObject data = event.getData();
              assertEquals(modifiedAt.toString(), data.get("modifiedAt"));
              assertEquals(10L, data.get("maxConcurrentQueries"));
              assertEquals(10L, data.get("maxConnections"));
              assertEquals(false, data.get("allowUnlimitedConcurrentQueries"));
              assertEquals(2, data.get("parquetWriterVersion"));
              assertEquals(true, data.get("tracingEnabled"));
              assertEquals(10L, data.get("cursorMaxFileSize"));
              assertEquals(1000, data.get("cursorMaxFiles"));
              assertEquals(1000L, data.get("cursorMaxWaitTimeForAvailableSpace"));
              assertEquals(2, data.get("queryExecutionBranchingDepth"));
              assertEquals(4, data.get("queryExecutionBranchingFactor"));
              assertEquals(10, data.get("queryExecutionMaxConcurrency"));
              assertEquals(10, data.get("queryExecutionMaxSerialNum"));
              assertEquals(10L, data.get("queryExecutionMaxSerialSize"));
              assertEquals("round_robin", data.get("queryRoutingStrategy"));
            })
        .during(() -> doAuthedJsonPut(_globalADFAAdminWriteUser, url, obj, HttpStatus.SC_OK));
  }

  @Test
  public void testGetTenantStorageConfig() throws Exception {
    final ObjectId tenantId = new ObjectId();
    final String url =
        String.format("/nds/adfa/admin/tenantStorageConfig?tenantId=%s", tenantId.toHexString());

    // Ensure a non-global admin user cannot access the endpoint.
    doAuthedJsonGet(_normalUser, url, HttpStatus.SC_FORBIDDEN);

    final NDSDataLakeTenantSvc ndsDataLakeTenantSvc = mock(NDSDataLakeTenantSvc.class);
    final NDSDataLakeTenant tenant = NDSDataLakeTenant.builder().tenantId(tenantId).build();
    when(ndsDataLakeTenantSvc.findByTenantId(tenantId)).thenReturn(Optional.ofNullable(tenant));
    when(ndsDataLakeTenantSvc.getStorageConfig(tenant))
        .thenReturn(NDSDataLakeStorageV1View.builder().build());
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class), "_tenantSvc", ndsDataLakeTenantSvc);

    // Ensure an audit event is recorded for this action.
    verifyEvents(_auditSvc)
        .ofType(Type.ADFA_TENANT_GET_STORAGE_CONFIG_ACTION_COMPLETED)
        .willBeAudited(
            events -> {
              assertEquals(1, events.size());
              ADFAAdminAudit event = (ADFAAdminAudit) events.get(0);
              assertEquals(tenantId, event.getTenantId());
            })
        .during(() -> doAuthedJsonGet(_globalADFAAdminReadUser, url, HttpStatus.SC_OK));
  }

  @Test
  public void testDeleteTenantStorageConfig() throws Exception {
    final ObjectId tenantId = new ObjectId();
    final String url =
        String.format("/nds/adfa/admin/tenantStorageConfig?tenantId=%s", tenantId.toHexString());

    // Ensure a non-global admin user cannot access the endpoint.
    doAuthedDelete(_normalUser, url, HttpStatus.SC_FORBIDDEN);

    final NDSDataLakeTenantSvc ndsDataLakeTenantSvc = mock(NDSDataLakeTenantSvc.class);
    final NDSDataLakeTenant tenant = NDSDataLakeTenant.builder().tenantId(tenantId).build();
    when(ndsDataLakeTenantSvc.findByTenantId(tenantId)).thenReturn(Optional.ofNullable(tenant));

    final DataLakeAdminApiClient dataLakeAdminApiClient = mock(DataLakeAdminApiClient.class);
    Assertions.assertNotNull(tenant);

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class), "_tenantSvc", ndsDataLakeTenantSvc);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class),
        "_dataLakeAdminApiClient",
        dataLakeAdminApiClient);

    // Ensure an audit event is recorded for this action.
    verifyEvents(_auditSvc)
        .ofType(Type.ADFA_TENANT_DELETE_STORAGE_CONFIG_ACTION_COMPLETED)
        .willBeAudited(
            events -> {
              assertEquals(1, events.size());
              ADFAAdminAudit event = (ADFAAdminAudit) events.get(0);
              assertEquals(tenantId, event.getTenantId());
            })
        .during(() -> doAuthedDelete(_globalADFAAdminWriteUser, url, HttpStatus.SC_OK));
  }

  @Test
  public void testGetTenantConfig() throws Exception {
    final ObjectId tenantId = new ObjectId();
    final ObjectId groupId = new ObjectId();
    final String url =
        String.format("/nds/adfa/admin/tenantConfig?tenantId=%s", tenantId.toHexString());

    // Ensure a non-global admin user cannot access the endpoint.
    doAuthedJsonGet(_normalUser, url, HttpStatus.SC_FORBIDDEN);

    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    when(tenant.getTenantId()).thenReturn(tenantId);
    when(tenant.getGroupId()).thenReturn(groupId);

    final NDSDataLakeTenantSvc ndsDataLakeTenantSvc = mock(NDSDataLakeTenantSvc.class);
    when(ndsDataLakeTenantSvc.findByTenantId(tenantId)).thenReturn(Optional.of(tenant));

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    when(ndsGroupSvc.find(tenant.getGroupId())).thenReturn(Optional.of(ndsGroup));

    final NDSDataLakeTenantConfigBuilderSvc ndsDataLakeTenantConfigBuilderSvc =
        mock(NDSDataLakeTenantConfigBuilderSvc.class);
    when(ndsDataLakeTenantConfigBuilderSvc.buildTenantConfig(tenant, ndsGroup))
        .thenReturn(ApiPrivateAtlasDataLakeTenantConfigView.builder().build());

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class), "_tenantSvc", ndsDataLakeTenantSvc);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class), "_ndsGroupSvc", ndsGroupSvc);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class),
        "_tenantConfigBuilderSvc",
        ndsDataLakeTenantConfigBuilderSvc);

    // Verify an audit event is recorded for this action.
    verifyEvents(_auditSvc)
        .ofType(Type.ADFA_TENANT_GET_CONFIG_ACTION_COMPLETED)
        .willBeAudited(
            events -> {
              assertEquals(1, events.size());
              ADFAAdminAudit event = (ADFAAdminAudit) events.get(0);
              assertEquals(tenantId, event.getTenantId());
            })
        .during(() -> doAuthedJsonGet(_globalADFAAdminReadUser, url, HttpStatus.SC_OK));
  }

  @Test
  public void testListDataSets() throws Exception {
    final ObjectId projectId = new ObjectId();
    final String url =
        String.format("/nds/adfa/admin/dataSets?projectId=%s", projectId.toHexString());
    verifyEvents(_auditSvc)
        .ofType(ADFAAdminAudit.Type.ADFA_LIST_DATA_SETS_ACTION_COMPLETED)
        .willBeAudited()
        .during(
            () -> {
              doAuthedGetBytes(_globalADFAAdminReadUser, url, HttpStatus.SC_OK);
            });
  }

  @Test
  public void testListCurrentOps() throws Exception {
    final ObjectId tenantId = new ObjectId();
    final String url =
        String.format("/nds/adfa/admin/currentOps?tenantId=%s", tenantId.toHexString());

    // Ensure a non-global admin user cannot access the endpoint.
    doAuthedJsonGet(_normalUser, url, HttpStatus.SC_FORBIDDEN);

    final NDSDataLakeTenantSvc ndsDataLakeTenantSvc = mock(NDSDataLakeTenantSvc.class);
    final NDSDataLakeTenant tenant = NDSDataLakeTenant.builder().tenantId(tenantId).build();
    when(ndsDataLakeTenantSvc.findByTenantId(tenantId)).thenReturn(Optional.ofNullable(tenant));

    final DataLakeAdminApiClient dataLakeAdminApiClient = mock(DataLakeAdminApiClient.class);
    Assertions.assertNotNull(tenant);
    when(dataLakeAdminApiClient.listCurrentOps(tenant))
        .thenReturn(
            List.of(
                NDSDataLakeCurrentOpView.builder().correlationId("id1").build(),
                NDSDataLakeCurrentOpView.builder().correlationId("id2").build()));

    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class), "_tenantSvc", ndsDataLakeTenantSvc);
    ClassModifier.modifyInstanceValue(
        AppConfig.getInstance(NDSDataLakePrivateSvc.class),
        "_dataLakeAdminApiClient",
        dataLakeAdminApiClient);

    // Ensure an audit event is recorded for this action.
    verifyEvents(_auditSvc)
        .ofType(Type.ADFA_LIST_CURRENT_OPS_ACTION_COMPLETED)
        .willBeAudited(
            events -> {
              assertEquals(1, events.size());
              ADFAAdminAudit event = (ADFAAdminAudit) events.get(0);
              assertEquals(tenantId, event.getTenantId());
            })
        .during(() -> doAuthedJsonArrayGet(_globalADFAAdminReadUser, url, HttpStatus.SC_OK));
  }
}
