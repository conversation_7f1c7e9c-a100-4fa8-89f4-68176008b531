package com.xgen.svc.nds.res;

import static java.lang.String.format;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.dao.AWSPrivateLinkConnectionDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkInterfaceEndpoint;
import com.xgen.cloud.nds.azure._private.dao.AzurePrivateLinkConnectionDao;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureSubscription;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.services.payments.modules.paymentMethod.common.PaymentMethodStubber;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.aws.model.ui.AWSPrivateLinkConnectionView;
import com.xgen.svc.nds.aws.model.ui.AWSPrivateLinkConnectionView.ConnectionStatus;
import com.xgen.svc.nds.azure.model.ui.AzurePrivateLinkConnectionView;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.EndpointServiceView;
import com.xgen.svc.nds.model.ui.EndpointServiceView.FieldDefs;
import com.xgen.svc.nds.model.ui.EndpointServiceView.Status;
import com.xgen.svc.nds.model.ui.privateLink.EndpointModificationRequest;
import com.xgen.svc.nds.model.ui.privateLink.EndpointServiceCreateRequest;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class NDSPrivateEndpointResourceIntTests extends JUnit5BaseResourceTest {

  private static final String ERROR_CODE_FIELD = "errorCode";
  private static final String ERROR_MESSAGE_FIELD = "message";

  private AppUser _user;
  private ObjectId _groupId;

  @Inject private NDSGroupDao _ndsGroupDao;
  @Inject private NDSGroupSvc _ndsGroupSvc;
  @Inject private AWSPrivateLinkConnectionDao _awsPrivateLinkDao;
  @Inject private AzurePrivateLinkConnectionDao _azurePrivateLinkDao;

  @Inject private AzureSubscriptionDao _azureSubscriptionDao;
  @Inject private AWSAccountDao _awsAccountDao;
  @Inject private ClusterDescriptionDao _clusterDescriptionDao;
  @Inject private PaymentMethodStubber paymentMethodStubber;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    FeatureFlagIntTestUtil.enableFeatureGlobally(FeatureFlag.ATLAS_RESOURCE_POLICIES);
    FeatureFlagIntTestUtil.enableFeatureGlobally(
        FeatureFlag.ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI);
  }

  private void createNDSResources(final boolean pUseCNRegionsOnly) throws Exception {
    Organization _organization = MmsFactory.createOrganizationWithNDSPlan(pUseCNRegionsOnly);
    paymentMethodStubber.stubPaymentMethod(_organization.getId(), true);
    final Group group =
        MmsFactory.createGroup(_organization, "testGroup", ObjectId.get(), pUseCNRegionsOnly);
    _user = MmsFactory.createUser(group);

    final NDSGroup _ndsGroup =
        _ndsGroupSvc.ensureGroup(
            group.getId(), pUseCNRegionsOnly, RegionUsageRestrictions.NONE, false);
    _groupId = _ndsGroup.getGroupId();

    // set up accounts
    _awsAccountDao.save(new AWSAccount(NDSModelTestFactory.getFullyAvailableAWSAccount()));
    _azureSubscriptionDao.save(
        new AzureSubscription(NDSModelTestFactory.getFullyAvailableAzureSubscription()));
  }

  @FunctionalInterface
  public interface VerifyEndpointFunction {
    void verify(final JSONObject pEndpoint);
  }

  public void testAddEndpointToEndpointService(
      final ObjectId pGroupId,
      final AppUser pUser,
      final CloudProvider pProposedCloudProvider,
      final RegionName pProposedRegion,
      final String pProposedEndpointId0,
      final String pProposedPrivateIPAddress0,
      final String pProposedEndpointId1,
      final String pProposedPrivateIPAddress1,
      final VerifyEndpointFunction pVerifyEndpointServiceInitial,
      final VerifyEndpointFunction pVerifyEndpointServiceDeleted,
      final VerifyEndpointFunction pVerifyEndpointServiceProvisioned,
      final VerifyEndpointFunction pVerifyEndpointInitial,
      final VerifyEndpointFunction pVerifyEndpointAvailable,
      final VerifyEndpointFunction pVerifyPatchedEndpoint,
      final VerifyEndpointFunction pVerifyDeletedEndpoint) {

    assertEquals(
        0,
        _ndsGroupDao
            .find(pGroupId)
            .orElseThrow()
            .getCloudProviderContainersByType(pProposedCloudProvider)
            .size());

    // fail - missing cloud provider
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService",
              pGroupId.toString(), pProposedCloudProvider.name());

      final JSONObject body =
          new JSONObject()
              .put(EndpointServiceCreateRequest.FieldDefs.REGION, pProposedRegion.getName());

      final JSONObject response = doAuthedJsonPut(pUser, path, body, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.INVALID_CLOUD_PROVIDER.name(), response.getString(ERROR_CODE_FIELD));
      assertEquals("Invalid Cloud Provider", response.getString(ERROR_MESSAGE_FIELD));
    }

    // fail - missing region
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService",
              pGroupId.toString(), pProposedCloudProvider.name());

      final JSONObject body =
          new JSONObject()
              .put(
                  EndpointServiceCreateRequest.FieldDefs.CLOUD_PROVIDER,
                  pProposedCloudProvider.name());

      final JSONObject response = doAuthedJsonPut(pUser, path, body, HttpStatus.SC_BAD_REQUEST);
      assertEquals(NDSErrorCode.INVALID_REGION.name(), response.getString(ERROR_CODE_FIELD));
      assertEquals(
          format("No region %s exists for provider %s.", "null", pProposedCloudProvider.name()),
          response.getString(ERROR_MESSAGE_FIELD));
    }

    // fail - incorrect region
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService",
              pGroupId.toString(), pProposedCloudProvider.name());

      final String dneRegion = "dneRegion";

      final JSONObject body =
          new JSONObject()
              .put(
                  EndpointServiceCreateRequest.FieldDefs.CLOUD_PROVIDER,
                  pProposedCloudProvider.name())
              .put(EndpointServiceCreateRequest.FieldDefs.REGION, dneRegion);

      final JSONObject response = doAuthedJsonPut(pUser, path, body, HttpStatus.SC_BAD_REQUEST);
      assertEquals(NDSErrorCode.INVALID_REGION.name(), response.getString(ERROR_CODE_FIELD));
      assertEquals(
          format("No region %s exists for provider %s.", dneRegion, pProposedCloudProvider.name()),
          response.getString(ERROR_MESSAGE_FIELD));
    }

    // success - create endpoint service
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService",
              pGroupId.toString(), pProposedCloudProvider.name());

      final JSONObject body =
          new JSONObject()
              .put(
                  EndpointServiceCreateRequest.FieldDefs.CLOUD_PROVIDER,
                  pProposedCloudProvider.name())
              .put(EndpointServiceCreateRequest.FieldDefs.REGION, pProposedRegion.getName());

      doAuthedJsonPut(pUser, path, body, HttpStatus.SC_ACCEPTED);
    }

    // check endpoint service created in initial state
    verifyEndpoint(pProposedCloudProvider, pVerifyEndpointServiceInitial, pUser, pGroupId);

    assertEquals(
        1,
        _ndsGroupDao
            .find(pGroupId)
            .orElseThrow()
            .getCloudProviderContainersByType(pProposedCloudProvider)
            .size());

    final CloudProviderContainer container0 =
        _ndsGroupDao
            .find(pGroupId)
            .orElseThrow()
            .getCloudProviderContainer(pProposedCloudProvider, pProposedRegion, null)
            .orElseThrow();

    final String endpointServiceId0 =
        container0.getEndpointServices().stream().findFirst().orElseThrow().getId().toString();

    // mark endpoint service for deletion
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService/%s",
              pGroupId.toString(), pProposedCloudProvider.name(), endpointServiceId0);

      doAuthedJsonDelete(pUser, path, HttpStatus.SC_ACCEPTED);
    }

    // checked transitioned to delete
    verifyEndpoint(pProposedCloudProvider, pVerifyEndpointServiceDeleted, pUser, pGroupId);

    // remove endpoint service to recreate
    if (pProposedCloudProvider.equals(CloudProvider.AWS)) {
      _awsPrivateLinkDao.removePrivateLink(pGroupId, container0.getId());
    } else if (pProposedCloudProvider.equals(CloudProvider.AZURE)) {
      _azurePrivateLinkDao.removePrivateLink(pGroupId, container0.getId());
    } else {
      fail();
    }

    // re-create endpoint service
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService",
              pGroupId.toString(), pProposedCloudProvider.name());

      final JSONObject body =
          new JSONObject()
              .put(
                  EndpointServiceCreateRequest.FieldDefs.CLOUD_PROVIDER,
                  pProposedCloudProvider.name())
              .put(EndpointServiceCreateRequest.FieldDefs.REGION, pProposedRegion.getName());

      doAuthedJsonPut(pUser, path, body, HttpStatus.SC_ACCEPTED);
    }

    final CloudProviderContainer container =
        _ndsGroupDao
            .find(pGroupId)
            .orElseThrow()
            .getCloudProviderContainer(pProposedCloudProvider, pProposedRegion, null)
            .orElseThrow();

    final String endpointServiceId =
        container.getEndpointServices().stream().findFirst().orElseThrow().getId().toString();

    // check endpoint service created in initial state
    verifyEndpoint(pProposedCloudProvider, pVerifyEndpointServiceInitial, pUser, pGroupId);

    // transition endpoint service to provisioned
    if (pProposedCloudProvider.equals(CloudProvider.AWS)) {
      _awsPrivateLinkDao.setPrivateLinkServiceProvisionedFields(
          pGroupId, container.getId(), "serviceName", "serviceId", "loadBalancerArn");
    } else if (pProposedCloudProvider.equals(CloudProvider.AZURE)) {
      _azurePrivateLinkDao.setPrivateLinkServiceProvisionedFields(
          pGroupId, container.getId(), "serviceName", "serviceResourceId", "loadBalancerName");
    } else {
      fail();
    }

    // check endpoint service in available state
    verifyEndpoint(pProposedCloudProvider, pVerifyEndpointServiceProvisioned, pUser, pGroupId);

    // fail - endpointServiceId not found
    {
      final ObjectId randomEndpointServiceId = ObjectId.get();

      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService/%s/endpoint",
              pGroupId.toString(), pProposedCloudProvider.name(), randomEndpointServiceId);

      final JSONObject body =
          new JSONObject()
              .put(EndpointModificationRequest.FieldDefs.ENDPOINT_ID, pProposedEndpointId0);

      Optional.ofNullable(pProposedPrivateIPAddress0)
          .ifPresent(
              ipAddress -> {
                body.put(
                    EndpointModificationRequest.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS, ipAddress);
              });

      final JSONObject response = doAuthedJsonPut(pUser, path, body, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.PRIVATE_ENDPOINT_SERVICE_NOT_FOUND.name(),
          response.getString(ERROR_CODE_FIELD));
      assertEquals(
          format(
              "Private endpoint service with id %s was not found.",
              randomEndpointServiceId.toString()),
          response.getString(ERROR_MESSAGE_FIELD));
    }

    // fail - endpointId missing
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService/%s/endpoint",
              pGroupId.toString(), pProposedCloudProvider.name(), endpointServiceId);

      final JSONObject body = new JSONObject();

      Optional.ofNullable(pProposedPrivateIPAddress0)
          .ifPresent(
              ipAddress -> {
                body.put(
                    EndpointModificationRequest.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS, ipAddress);
              });
      ;
      final JSONObject response = doAuthedJsonPut(pUser, path, body, HttpStatus.SC_BAD_REQUEST);
      assertEquals(NDSErrorCode.INVALID_ARGUMENT.name(), response.getString(ERROR_CODE_FIELD));
      assertEquals("Invalid endpointId", response.getString(ERROR_MESSAGE_FIELD));
    }

    // add endpoint to available endpoint service
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService/%s/endpoint",
              pGroupId.toString(), pProposedCloudProvider.name(), endpointServiceId);

      final JSONObject body =
          new JSONObject()
              .put(EndpointModificationRequest.FieldDefs.ENDPOINT_ID, pProposedEndpointId0);

      Optional.ofNullable(pProposedPrivateIPAddress0)
          .ifPresent(
              ipAddress -> {
                body.put(
                    EndpointModificationRequest.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS, ipAddress);
              });

      doAuthedJsonPut(pUser, path, body, HttpStatus.SC_ACCEPTED);
    }

    // check endpoint added to service in initial state
    verifyEndpoint(pProposedCloudProvider, pVerifyEndpointInitial, pUser, pGroupId);

    // transition endpoint to available
    if (pProposedCloudProvider.equals(CloudProvider.AWS)) {
      _awsPrivateLinkDao.setInterfaceEndpointPrivateLinkHostname(
          pGroupId, container.getId(), pProposedEndpointId0, "somehostname");
      _awsPrivateLinkDao.setEndpointConnectionStatus(
          pGroupId,
          container.getId(),
          pProposedEndpointId0,
          AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE);
    } else if (pProposedCloudProvider.equals(CloudProvider.AZURE)) {
      _azurePrivateLinkDao.setPrivateEndpointAvailable(
          pGroupId,
          container.getId(),
          pProposedEndpointId0,
          "somehostname",
          "somemongodbhostname",
          "subId/endpointServiceName/endpointName");
    } else {
      fail();
    }

    // check endpoint available
    verifyEndpoint(pProposedCloudProvider, pVerifyEndpointAvailable, pUser, pGroupId);

    // transition endpoint to error state to allow patch
    if (pProposedCloudProvider.equals(CloudProvider.AWS)) {
      _awsPrivateLinkDao.setEndpointRejectedWithReason(
          pGroupId, container.getId(), pProposedEndpointId0, "ERROR");
    } else if (pProposedCloudProvider.equals(CloudProvider.AZURE)) {
      _azurePrivateLinkDao.setPrivateEndpointStatusFailedWithReason(
          pGroupId, container.getId(), pProposedEndpointId0, "ERROR");
    } else {
      fail();
    }

    // fail - delete endpoint service with existing endpoint
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService/%s",
              pGroupId.toString(), pProposedCloudProvider.name(), endpointServiceId);

      final JSONObject response = doAuthedJsonDelete(pUser, path, HttpStatus.SC_BAD_REQUEST);

      assertEquals(
          NDSErrorCode.CANNOT_DELETE_PRIVATE_ENDPOINT_SERVICE_WITH_ENDPOINTS.name(),
          response.getString(ERROR_CODE_FIELD));
      assertEquals(
          "A private endpoint service with endpoints attached cannot be deleted. Please delete all"
              + " endpoints.",
          response.getString(ERROR_MESSAGE_FIELD));
    }

    // patch endpoint
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService/%s/endpoint/%s",
              pGroupId.toString(),
              pProposedCloudProvider.name(),
              endpointServiceId,
              URLEncoder.encode(pProposedEndpointId0, StandardCharsets.UTF_8));

      final JSONObject body =
          new JSONObject()
              .put(EndpointModificationRequest.FieldDefs.ENDPOINT_ID, pProposedEndpointId1);

      Optional.ofNullable(pProposedPrivateIPAddress1)
          .ifPresent(
              ipAddress -> {
                body.put(
                    EndpointModificationRequest.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS, ipAddress);
              });

      doAuthedJsonPatch(pUser, path, body, HttpStatus.SC_ACCEPTED);
    }

    // check endpoint has been updated
    verifyEndpoint(pProposedCloudProvider, pVerifyPatchedEndpoint, pUser, pGroupId);

    // delete endpoint
    {
      final String path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService/%s/endpoint/%s",
              pGroupId.toString(),
              pProposedCloudProvider.name(),
              endpointServiceId,
              URLEncoder.encode(pProposedEndpointId1, StandardCharsets.UTF_8));
      doAuthedJsonDelete(pUser, path, HttpStatus.SC_ACCEPTED);
    }

    // check endpoint has been marked for deletion
    verifyEndpoint(pProposedCloudProvider, pVerifyDeletedEndpoint, pUser, pGroupId);
  }

  private void verifyEndpoint(
      final CloudProvider pProposedCloudProvider,
      final VerifyEndpointFunction pVerifyFn,
      final AppUser pUser,
      final ObjectId pGroupId) {
    final JSONArray endpoints =
        doAuthedJsonArrayGet(
            pUser,
            format(
                "/nds/%s/privateEndpoint/%s/endpointService",
                pGroupId.toString(), pProposedCloudProvider.name()));
    pVerifyFn.verify(endpoints.getJSONObject(0));
  }

  private void assertAddEndpointToEndpointService_AWS(
      final ObjectId pGroupId, final AppUser pUser, final AWSRegionName pRegionName) {
    final CloudProvider cloudProvider = CloudProvider.AWS;
    final String endpointId0 = "vpce-123";
    final String endpointId1 = "vpce-234";

    testAddEndpointToEndpointService(
        pGroupId,
        pUser,
        cloudProvider,
        pRegionName,
        endpointId0,
        null,
        endpointId1,
        null,
        (endpoint) -> {
          assertEquals(cloudProvider.name(), endpoint.getString(FieldDefs.CLOUD_PROVIDER));
          assertEquals(
              pRegionName.getName(), endpoint.getJSONObject(FieldDefs.REGION).getString("key"));

          assertEquals(Status.INITIATING.name(), endpoint.getString(FieldDefs.STATUS));
          assertEquals(JSONObject.NULL, endpoint.get(FieldDefs.ENDPOINT_ID));
          assertEquals(
              JSONObject.NULL,
              endpoint.get(AWSPrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(Status.DELETING.name(), endpoint.getString(FieldDefs.STATUS));
          assertEquals(JSONObject.NULL, endpoint.get(FieldDefs.ENDPOINT_ID));
          assertEquals(
              JSONObject.NULL,
              endpoint.get(AWSPrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(Status.AVAILABLE.name(), endpoint.getString(FieldDefs.STATUS));
          assertEquals(JSONObject.NULL, endpoint.get(FieldDefs.ENDPOINT_ID));
          assertEquals(
              JSONObject.NULL,
              endpoint.get(AWSPrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(Status.AVAILABLE.name(), endpoint.getString(FieldDefs.STATUS));
          assertEquals(endpointId0, endpoint.get(FieldDefs.ENDPOINT_ID));
          assertEquals(
              ConnectionStatus.PENDING_ACCEPTANCE.name(),
              endpoint.getString(AWSPrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(Status.AVAILABLE.name(), endpoint.getString(FieldDefs.STATUS));
          assertEquals(endpointId0, endpoint.get(FieldDefs.ENDPOINT_ID));
          assertEquals(
              ConnectionStatus.AVAILABLE.name(),
              endpoint.getString(AWSPrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(endpointId1, endpoint.get(FieldDefs.ENDPOINT_ID));
          assertEquals(
              ConnectionStatus.PENDING_ACCEPTANCE.name(),
              endpoint.getString(AWSPrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(endpointId1, endpoint.get(FieldDefs.ENDPOINT_ID));
          assertEquals(
              ConnectionStatus.DELETING.name(),
              endpoint.getString(AWSPrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        });
  }

  private void assertUpdateRegionalizedPrivateLinkEnabled_EnableValidation(
      final ObjectId pGroupId, final AppUser pUser) {
    final String path = String.format("/nds/%s/privateEndpoint/regionalMode", _groupId);
    final JSONObject toggleEnabled = new JSONObject().put("enabled", true);
    final JSONObject toggleDisabled = new JSONObject().put("enabled", false);

    {
      doAuthedJsonPatch(pUser, path, toggleDisabled, HttpStatus.SC_ACCEPTED);

      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getClusterDescription(pGroupId, CloudProvider.AWS);

      _clusterDescriptionDao.save(clusterDescription);

      final JSONObject badRequestResponse =
          doAuthedJsonPatch(pUser, path, toggleEnabled, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CANNOT_ENABLE_REGIONALIZED_PRIVATE_LINK.name(),
          badRequestResponse.getString(ERROR_CODE_FIELD));
    }
  }

  private void assertUpdateRegionalizedPrivateLinkEnabled_DisableValidation(
      final ObjectId pGroupId,
      final AppUser pUser,
      final RegionName region1,
      final RegionName region2) {
    final CloudProvider cloudProvider = CloudProvider.AWS;
    final String path = String.format("/nds/%s/privateEndpoint/regionalMode", pGroupId);
    final JSONObject toggleEnabled = new JSONObject().put("enabled", true);
    final JSONObject toggleDisabled = new JSONObject().put("enabled", false);

    // test simple toggle
    {
      final JSONObject response = doAuthedJsonGet(pUser, path, HttpStatus.SC_OK);
      assertFalse(response.getBoolean("enabled"));

      final JSONObject response1 =
          doAuthedJsonPatch(pUser, path, toggleEnabled, HttpStatus.SC_ACCEPTED);
      assertTrue(response1.getBoolean("enabled"));
    }

    {
      final String createEndpointServicePath =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService",
              pGroupId.toString(), cloudProvider.name());

      final JSONObject endpointService1Body =
          new JSONObject()
              .put(EndpointServiceCreateRequest.FieldDefs.CLOUD_PROVIDER, cloudProvider.name())
              .put(EndpointServiceCreateRequest.FieldDefs.REGION, region1.getName());
      doAuthedJsonPut(
          pUser, createEndpointServicePath, endpointService1Body, HttpStatus.SC_ACCEPTED);

      final JSONObject endpointService2Body =
          new JSONObject()
              .put(EndpointServiceCreateRequest.FieldDefs.CLOUD_PROVIDER, cloudProvider.name())
              .put(EndpointServiceCreateRequest.FieldDefs.REGION, region2.getName());
      doAuthedJsonPut(
          pUser, createEndpointServicePath, endpointService2Body, HttpStatus.SC_ACCEPTED);

      final CloudProviderContainer region1Container =
          _ndsGroupDao
              .find(pGroupId)
              .orElseThrow()
              .getCloudProviderContainer(cloudProvider, region1, null)
              .orElseThrow();
      _awsPrivateLinkDao.setPrivateLinkServiceProvisionedFields(
          pGroupId, region1Container.getId(), "serviceName", "serviceId", "loadBalancerArn");

      final CloudProviderContainer region2Container =
          _ndsGroupDao
              .find(pGroupId)
              .orElseThrow()
              .getCloudProviderContainer(cloudProvider, region2, null)
              .orElseThrow();
      _awsPrivateLinkDao.setPrivateLinkServiceProvisionedFields(
          pGroupId, region2Container.getId(), "serviceName", "serviceId", "loadBalancerArn");

      // can still toggle off
      final JSONObject response =
          doAuthedJsonPatch(pUser, path, toggleDisabled, HttpStatus.SC_ACCEPTED);
      assertFalse(response.getBoolean("enabled"));
      doAuthedJsonPatch(pUser, path, toggleEnabled, HttpStatus.SC_ACCEPTED);
    }

    {
      final CloudProviderContainer region1Container =
          _ndsGroupDao
              .find(pGroupId)
              .orElseThrow()
              .getCloudProviderContainer(cloudProvider, region1, null)
              .orElseThrow();
      final ObjectId endpointService1Id = region1Container.getEndpointServices().get(0).getId();

      final CloudProviderContainer region2Container =
          _ndsGroupDao
              .find(pGroupId)
              .orElseThrow()
              .getCloudProviderContainer(cloudProvider, region2, null)
              .orElseThrow();
      final ObjectId endpointService2Id = region2Container.getEndpointServices().get(0).getId();

      final String addEndpoint1Path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService/%s/endpoint",
              pGroupId.toString(), cloudProvider.name(), endpointService1Id);

      final String addEndpoint2Path =
          format(
              "/nds/%s/privateEndpoint/%s/endpointService/%s/endpoint",
              pGroupId.toString(), cloudProvider.name(), endpointService2Id);

      final JSONObject addEndpointBody0 =
          new JSONObject().put(EndpointModificationRequest.FieldDefs.ENDPOINT_ID, "vpce-012");

      final JSONObject addEndpointBody1 =
          new JSONObject().put(EndpointModificationRequest.FieldDefs.ENDPOINT_ID, "vpce-345");

      doAuthedJsonPut(pUser, addEndpoint1Path, addEndpointBody0, HttpStatus.SC_ACCEPTED);
      doAuthedJsonPut(pUser, addEndpoint2Path, addEndpointBody1, HttpStatus.SC_ACCEPTED);

      // can still toggle off
      final JSONObject response =
          doAuthedJsonPatch(pUser, path, toggleDisabled, HttpStatus.SC_ACCEPTED);
      assertFalse(response.getBoolean("enabled"));
      doAuthedJsonPatch(pUser, path, toggleEnabled, HttpStatus.SC_ACCEPTED);

      final JSONObject addEndpoint1Body =
          new JSONObject().put(EndpointModificationRequest.FieldDefs.ENDPOINT_ID, "vpce-6789");
      doAuthedJsonPut(pUser, addEndpoint1Path, addEndpoint1Body, HttpStatus.SC_ACCEPTED);

      // can no longer toggle off
      final JSONObject badRequestResponse =
          doAuthedJsonPatch(pUser, path, toggleDisabled, HttpStatus.SC_BAD_REQUEST);
      assertEquals(
          NDSErrorCode.CANNOT_DISABLE_REGIONALIZED_PRIVATE_LINK.name(),
          badRequestResponse.getString(ERROR_CODE_FIELD));
    }
  }

  @Test
  public void testAddEndpointToEndpointService_AWS() throws Exception {
    createNDSResources(false);
    assertAddEndpointToEndpointService_AWS(_groupId, _user, AWSRegionName.US_EAST_1);
  }

  @Test
  public void testAddEndpointToEndpointService_Azure() throws Exception {
    final CloudProvider cloudProvider = CloudProvider.AZURE;
    final RegionName regionName = AzureRegionName.US_EAST_2;
    final String endpointId0 =
        "/subscriptions/a1b2c3d4-dead-abcd-1111-000000000000/resourceGroups/test-private-link/providers/Microsoft.Network/privateEndpoints/testEndpoint0";
    final String privateIPAddress0 = "***********";
    final String endpointId1 =
        "/subscriptions/a1b2c3d4-dead-abcd-1111-000000000000/resourceGroups/test-private-link/providers/Microsoft.Network/privateEndpoints/testEndpoint1";
    final String privateIPAddress1 = "***********";

    createNDSResources(false);

    testAddEndpointToEndpointService(
        _groupId,
        _user,
        cloudProvider,
        regionName,
        endpointId0,
        privateIPAddress0,
        endpointId1,
        privateIPAddress1,
        (endpoint) -> {
          assertEquals(
              cloudProvider.name(),
              endpoint.getString(EndpointServiceView.FieldDefs.CLOUD_PROVIDER));
          assertEquals(
              regionName.getName(),
              endpoint.getJSONObject(EndpointServiceView.FieldDefs.REGION).getString("key"));
          assertEquals(
              EndpointServiceView.Status.INITIATING.name(),
              endpoint.getString(EndpointServiceView.FieldDefs.STATUS));
          assertEquals(JSONObject.NULL, endpoint.get(EndpointServiceView.FieldDefs.ENDPOINT_ID));
          assertEquals(
              JSONObject.NULL,
              endpoint.get(AzurePrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(
              EndpointServiceView.Status.DELETING.name(),
              endpoint.getString(EndpointServiceView.FieldDefs.STATUS));
          assertEquals(JSONObject.NULL, endpoint.get(EndpointServiceView.FieldDefs.ENDPOINT_ID));
          assertEquals(
              JSONObject.NULL,
              endpoint.get(AzurePrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(
              EndpointServiceView.Status.AVAILABLE.name(),
              endpoint.getString(EndpointServiceView.FieldDefs.STATUS));

          assertEquals(JSONObject.NULL, endpoint.get(EndpointServiceView.FieldDefs.ENDPOINT_ID));
          assertEquals(
              JSONObject.NULL,
              endpoint.get(AzurePrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(
              EndpointServiceView.Status.AVAILABLE.name(),
              endpoint.getString(EndpointServiceView.FieldDefs.STATUS));
          assertEquals(endpointId0, endpoint.get(EndpointServiceView.FieldDefs.ENDPOINT_ID));
          assertEquals(
              privateIPAddress0,
              endpoint.get(AzurePrivateLinkConnectionView.FieldDefs.PRIVATE_ENDPOINT_ID_ADDRESS));
          assertEquals(
              AzurePrivateLinkConnectionView.EndpointStatus.INITIATING.name(),
              endpoint.getString(AzurePrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(
              EndpointServiceView.Status.AVAILABLE.name(),
              endpoint.getString(EndpointServiceView.FieldDefs.STATUS));
          assertEquals(endpointId0, endpoint.get(EndpointServiceView.FieldDefs.ENDPOINT_ID));
          assertEquals(
              privateIPAddress0,
              endpoint.get(AzurePrivateLinkConnectionView.FieldDefs.PRIVATE_ENDPOINT_ID_ADDRESS));
          assertEquals(
              AzurePrivateLinkConnectionView.EndpointStatus.AVAILABLE.name(),
              endpoint.getString(AzurePrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(endpointId1, endpoint.get(EndpointServiceView.FieldDefs.ENDPOINT_ID));
          assertEquals(
              privateIPAddress1,
              endpoint.get(AzurePrivateLinkConnectionView.FieldDefs.PRIVATE_ENDPOINT_ID_ADDRESS));
          assertEquals(
              AzurePrivateLinkConnectionView.EndpointStatus.INITIATING.name(),
              endpoint.getString(AzurePrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        },
        (endpoint) -> {
          assertEquals(endpointId1, endpoint.get(EndpointServiceView.FieldDefs.ENDPOINT_ID));
          assertEquals(
              AzurePrivateLinkConnectionView.EndpointStatus.DELETING.name(),
              endpoint.getString(AzurePrivateLinkConnectionView.FieldDefs.ENDPOINT_STATUS));
        });
  }

  @Test
  public void testUpdateRegionalizedPrivateLinkEnabled_EnableValidation() throws Exception {
    createNDSResources(false);
    assertUpdateRegionalizedPrivateLinkEnabled_EnableValidation(_groupId, _user);
  }

  @Test
  public void testUpdateRegionalizedPrivateLinkEnabled_DisableValidation() throws Exception {
    createNDSResources(false);
    assertUpdateRegionalizedPrivateLinkEnabled_DisableValidation(
        _groupId, _user, AWSRegionName.US_EAST_1, AWSRegionName.US_WEST_1);
  }
}
