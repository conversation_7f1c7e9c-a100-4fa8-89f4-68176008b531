package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.appconfig._public.config.AppConfig;
import com.xgen.cloud.common.agent._public.model.AgentApiKey;
import com.xgen.cloud.cps.agent._private.dao.CpsAgentSessionDao;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._private.dao.CpsBackupCursorFileListsDao;
import com.xgen.cloud.cps.restore._public.model.CpsBackupCursorFileList;
import com.xgen.cloud.cps.restore._public.model.CpsBackupCursorFileList.CursorFile;
import com.xgen.cloud.cps.restore._public.model.CpsBackupCursorFileList.FieldDefs;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot.CpsWtCheckpoint.Builder;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot.CpsWtCheckpoint.State;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.common.TestDataUtils;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.nds.svc.cps.CpsWtcSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class CpsWtCheckpointsResourceIntTests extends JUnit5BaseResourceTest {

  private static final ObjectId GROUP_ID = oid(155);
  private static final ObjectId BACKUP_SNAPSHOT_ID = oid(6);
  private static final String CLUSTER_NAME = "ShardedCluster155";
  private static final String URL_PREFIX = "/agents/cps/checkpoints";
  private static final long CURSOR_ID = 6097361614580866405L;
  private static final String RS_ID = "ShardedCluster155-shard-0";
  private static final String BACKUP_ID = "02cae306-934f-4b9e-b649-8ea313be7567";
  private static final long EXTENDED_CURSOR_ID = 6097361614580865555L;
  private static final String SESSION_ID = "SESSION_ID";
  private static final String AGENT_HOST = "*******.1.1";
  private static final List<CursorFile> WTC_FILE_LIST_1 =
      Arrays.asList(
          new CursorFile("/tmp/var/log/somefile_1.wt", "db.col1", 1024),
          new CursorFile("/tmp/var/log/somefile_2.bson", "db.col2", 1024),
          new CursorFile("/tmp/var/log/index-3--4776843778073959199.wt", "db.col3", 1024),
          new CursorFile("/tmp/var/log/0_realllyloooongfile" + genStr('e', 5000), "db2.col", 1024));

  private static final List<CursorFile> WTC_FILE_LIST_2 =
      Arrays.asList(
          new CursorFile("/tmp/var/log/deep/blob/mongod.lock", "db3.col1", 1024),
          new CursorFile("/tmp/var/log/deep/blob/README", "db3.col2", 1024));

  private static final List<String> EXPECTED_COMPLETE_FILE_LIST =
      Arrays.asList(
          "somefile_1.wt",
          "somefile_2.bson",
          "index-3--4776843778073959199.wt",
          "0_realllyloooongfile" + genStr('e', 5000),
          "deep/blob/mongod.lock",
          "deep/blob/README");

  private static final List<String> DB_PATH_EXPECTED_COMPLETE_FILE_LIST =
      Arrays.asList(
          "log/somefile_1.wt",
          "log/somefile_2.bson",
          "log/index-3--4776843778073959199.wt",
          "log/0_realllyloooongfile" + genStr('e', 5000),
          "log/deep/blob/mongod.lock",
          "log/deep/blob/README");

  @Inject private CpsWtcSvc _cpsWtcSvc;
  @Inject private BackupSnapshotDao _backupSnapshotDao;
  private GroupDao _groupDao;
  private AgentApiKey _agentApiKey;
  private CpsBackupCursorFileListsDao _fileListDao;
  private BackupSnapshotDao _snapshotDao;
  private CpsAgentSessionDao _cpsAgentSessionDao;
  private BackupRestoreJobDao _restoreJobDao;

  private Group _group;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "mms/dao/GroupDao/groups.json.ftl", null, Group.DB_NAME, Group.COLLECTION_NAME);
    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupSnapshotDao/backupSnapshots.json.ftl",
        null,
        "nds",
        "config.nds.backup.snapshots");

    TestDataUtils.populateCollectionFromJsonFtlFile(
        "nds/dao/NDSBackupRestoreJobDao/backupRestoreJobs.json.ftl",
        null,
        "nds",
        "config.nds.backup.restorejobs");

    _groupDao = AppConfig.getInstance(GroupDao.class);
    _fileListDao = AppConfig.getInstance(CpsBackupCursorFileListsDao.class);
    _group = _groupDao.findById(GROUP_ID);
    _agentApiKey = MmsFactory.generateApiKey(_group.getId(), new ObjectId());
    _snapshotDao = AppConfig.getInstance(BackupSnapshotDao.class);
    final long timeoutMs = TimeUnit.MINUTES.toMillis(3);
    final long now = System.currentTimeMillis();
    _cpsAgentSessionDao = AppConfig.getInstance(CpsAgentSessionDao.class);
    _cpsAgentSessionDao.tryGrabAndRenewLease(
        GROUP_ID, RS_ID, SESSION_ID, AGENT_HOST, "HOST_NAME", timeoutMs, now);
    _restoreJobDao = AppConfig.getInstance(BackupRestoreJobDao.class);
  }

  @Test
  public void putWtcInitialBatch() throws Exception {
    final JSONObject initialBatch = createInitialBatchObject();
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), initialBatch);
    verifyBatchCorrectness(1, 1);
  }

  @Test
  public void putWtcAnyFileBatch() throws Exception {
    // request should fail due to jumping from batch 1 to batch 3
    final JSONObject batch = createLastBatchObject(3);
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), batch, 400);
  }

  @Test
  public void putWtcLastBatch() throws Exception {
    _cpsWtcSvc.initializeState(BACKUP_SNAPSHOT_ID);
    _cpsWtcSvc.transitionIfApplicable(
        BACKUP_SNAPSHOT_ID, ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.CMD_OPEN_BACKUP_CURSOR);
    final JSONObject initialBatch = createInitialBatchObject();
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), initialBatch);

    final ReplicaSetBackupSnapshot snapshot =
        (ReplicaSetBackupSnapshot) _snapshotDao.findById(BACKUP_SNAPSHOT_ID).get();
    final Date firstHeartBeat = snapshot.getCpsWtCheckpoint().getLastHeartBeat();
    assertNotNull(firstHeartBeat);
    assertEquals(1, _fileListDao.count());

    // sleep for a second to allow creation of a new heartbeat
    Thread.sleep(1000);

    final JSONObject batch = createLastBatchObject(2);
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), batch);
    assertEquals(2, _fileListDao.count());
    verifySnapshotAgentStatusUpdated(
        ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.LAST_BATCH_RECEIVED);

    final ReplicaSetBackupSnapshot snapshot2 =
        (ReplicaSetBackupSnapshot) _snapshotDao.findById(BACKUP_SNAPSHOT_ID).get();
    final Date lastHeartBeat = snapshot2.getCpsWtCheckpoint().getLastHeartBeat();
    assertNotNull(lastHeartBeat);
    assertTrue(lastHeartBeat.after(firstHeartBeat));
  }

  @Test
  public void putBadBatch() throws Exception {
    _backupSnapshotDao.updateWtcStatus(BACKUP_SNAPSHOT_ID, State.CMD_OPEN_BACKUP_CURSOR);
    final JSONObject initialBatch = createInitialBatchObject();
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), initialBatch);

    final JSONObject badBatch = createBadBatchObject(2);
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), badBatch);
    verifySnapshotAgentStatusUpdated(ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.CURSOR_FAILED);
  }

  @Test
  public void setBackupCursorExtendComplete() throws Exception {
    _cpsWtcSvc.initializeState(BACKUP_SNAPSHOT_ID);
    _cpsWtcSvc.transitionIfApplicable(
        BACKUP_SNAPSHOT_ID, ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.CMD_OPEN_BACKUP_CURSOR);

    _cpsWtcSvc.transitionIfApplicable(
        BACKUP_SNAPSHOT_ID, ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.LAST_BATCH_RECEIVED);
    final ReplicaSetBackupSnapshot snap1 =
        (ReplicaSetBackupSnapshot) _snapshotDao.findById(BACKUP_SNAPSHOT_ID).get();
    assertEquals(
        ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.LAST_BATCH_RECEIVED,
        snap1.getCpsWtCheckpoint().getState());
    _cpsWtcSvc.transitionIfApplicable(
        BACKUP_SNAPSHOT_ID, ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.CMD_START_CURSOR_EXTEND);
    final ReplicaSetBackupSnapshot snap2 =
        (ReplicaSetBackupSnapshot) _snapshotDao.findById(BACKUP_SNAPSHOT_ID).get();
    assertEquals(
        ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.CMD_START_CURSOR_EXTEND,
        snap2.getCpsWtCheckpoint().getState());

    final JSONObject extendComplete = createBackupCursorExtendCompleted();
    doAgentApiCallPost(_group, _agentApiKey, getPutCursorExtentCompleteURL(), extendComplete);

    final ReplicaSetBackupSnapshot snap3 =
        (ReplicaSetBackupSnapshot) _snapshotDao.findById(BACKUP_SNAPSHOT_ID).get();
    assertEquals(
        ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.CURSOR_EXTEND_COMPLETE,
        snap3.getCpsWtCheckpoint().getState());
  }

  @Test
  public void setBackupCursorKilled() throws Exception {
    _cpsWtcSvc.initializeState(BACKUP_SNAPSHOT_ID);
    final SnapshotUpdate snapshotUpdate =
        new SnapshotUpdate().setCheckpoint(new Builder().setCursorId(1L));
    _backupSnapshotDao.updateBackupSnapshot(BACKUP_SNAPSHOT_ID, snapshotUpdate);
    _cpsWtcSvc.transitionIfApplicable(
        BACKUP_SNAPSHOT_ID, ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.CMD_KILL_CURSOR);
    final JSONObject cursorKilled = createBackupCursorKilled();
    doAgentApiCallPut(_group, _agentApiKey, getPutCursorKilledURL(), cursorKilled);
    verifySnapshotAgentStatusUpdated(ReplicaSetBackupSnapshot.CpsWtCheckpoint.State.CURSOR_CLOSED);
  }

  @Test
  public void putDuplicateBatch() throws Exception {
    _backupSnapshotDao.updateWtcStatus(BACKUP_SNAPSHOT_ID, State.CMD_OPEN_BACKUP_CURSOR);
    final JSONObject initialBatch = createInitialBatchObject();
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), initialBatch);
    assertEquals(_fileListDao.getCursorFiles(BACKUP_SNAPSHOT_ID).count(), 1);

    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), initialBatch);
    assertEquals(_fileListDao.getCursorFiles(BACKUP_SNAPSHOT_ID).count(), 1);

    final JSONObject batch2 = createBatchObject(2);
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), batch2);

    final JSONObject batch3 = createBatchObject(3);
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), batch3);

    assertEquals(_fileListDao.getCursorFiles(BACKUP_SNAPSHOT_ID).count(), 3);
    doAgentApiCallPut(_group, _agentApiKey, getPutBatchURl(), batch3);
    assertEquals(_fileListDao.getCursorFiles(BACKUP_SNAPSHOT_ID).count(), 3);
  }

  @Test
  public void getWtcFileListsV2() throws IOException {
    setupWtcData();
    final ReplicaSetBackupRestoreJob restoreJob = _restoreJobDao.findReplicaSetById(oid(5));
    final ObjectId restoreJobId = restoreJob.getId();

    final JSONObject responseJson =
        new JSONObject(
            doAgentApiCallGet(_group, _agentApiKey, getGetCursorFilesV2Url(restoreJobId)));
    final JSONArray filesJsonArray = responseJson.getJSONArray("files");
    final List<String> parsedFilesFromResponse = new LinkedList<>();
    filesJsonArray.forEach(file -> parsedFilesFromResponse.add((String) file));
    assertEquals(EXPECTED_COMPLETE_FILE_LIST, parsedFilesFromResponse);
  }

  @Test
  public void getWtcFileListsForSnapshot() throws IOException {
    setupWtcData();
    final ReplicaSetBackupRestoreJob restoreJob = _restoreJobDao.findReplicaSetById(oid(5));
    final ObjectId snapshotId = restoreJob.getSnapshotId();

    final JSONObject obj = new JSONObject();
    obj.put("isRelativeToVolumePath", false);

    final JSONObject responseJson =
        new JSONObject(
            doAgentApiCallGet(
                _group, _agentApiKey, getGetCursorFilesForSnapshotUrl(snapshotId, false)));
    final JSONArray filesJsonArray = responseJson.getJSONArray("files");
    final List<String> parsedFilesFromResponse = new LinkedList<>();
    filesJsonArray.forEach(file -> parsedFilesFromResponse.add((String) file));
    assertEquals(EXPECTED_COMPLETE_FILE_LIST, parsedFilesFromResponse);

    final JSONObject responseJson2 =
        new JSONObject(
            doAgentApiCallGet(
                _group, _agentApiKey, getGetCursorFilesForSnapshotUrl(snapshotId, true)));
    final JSONArray filesJsonArray2 = responseJson2.getJSONArray("files");
    final List<String> parsedFilesFromResponse2 = new LinkedList<>();
    filesJsonArray2.forEach(file -> parsedFilesFromResponse2.add((String) file));

    assertEquals(DB_PATH_EXPECTED_COMPLETE_FILE_LIST, parsedFilesFromResponse2);
  }

  private JSONObject createInitialBatchObject() {
    final JSONObject baseBatch = createBatchObject(1);
    final JSONObject metadata = new JSONObject();
    metadata.put("backupId", BACKUP_ID);
    metadata.put("dbPath", "/data/db");
    metadata.put("oplogStart", new JSONObject().put("T", 100).put("I", 1));
    metadata.put("oplogEnd", new JSONObject().put("T", 100).put("I", 1));
    metadata.put("checkpointTimestamp", new JSONObject().put("T", 100).put("I", 1));
    baseBatch.put("metadata", metadata);
    baseBatch.put("scheduledCreationTime", new Date().getTime());
    return baseBatch;
  }

  private JSONObject createLastBatchObject(final int batchIndex) {
    return createBatchObject(batchIndex, new JSONArray());
  }

  private JSONObject createBatchObject(final int batchIndex) {
    final JSONArray files = new JSONArray();
    files.put(new JSONObject().put("filename", "db1.wt").put("namespace", "db.col"));
    return createBatchObject(batchIndex, files);
  }

  private JSONObject createBatchObject(final int batchIndex, final JSONArray files) {
    final JSONObject batch = new JSONObject();
    batch.put("cursorId", CURSOR_ID);
    batch.put("batchIndex", batchIndex);
    batch.put("snapshotId", BACKUP_SNAPSHOT_ID);
    batch.put("sourceHostname", "sourceHostname");
    batch.put("files", files);
    batch.put("backupId", BACKUP_ID);
    return batch;
  }

  private JSONObject createBadBatchObject(final int batchIndex) {
    final JSONObject batch = new JSONObject();
    batch.put("cursorId", CURSOR_ID);
    batch.put("backupId", BACKUP_ID);
    batch.put("batchIndex", batchIndex);
    batch.put("snapshotId", BACKUP_SNAPSHOT_ID);
    batch.put("sourceHostname", "sourceHostname");
    batch.put("error", "this is an error");
    return batch;
  }

  private JSONObject createBackupCursorKilled() {
    final JSONObject batch = new JSONObject();
    batch.put("snapshotId", BACKUP_SNAPSHOT_ID);
    return batch;
  }

  private JSONObject createBackupCursorExtendCompleted() {
    final JSONObject obj = new JSONObject();
    obj.put("snapshotId", BACKUP_SNAPSHOT_ID);
    obj.put("backupId", BACKUP_ID);
    obj.put("extendedCursorId", EXTENDED_CURSOR_ID);
    obj.put("checkpointExtendedTo", new JSONObject().put("T", 100).put("I", 1));
    return obj;
  }

  private void setupWtcData() {
    CpsBackupCursorFileList.Builder builder =
        new CpsBackupCursorFileList.Builder()
            .withSnapshotId(oid(41))
            .withBatchIndex(1)
            .withCursorId(440L)
            .withFiles(
                WTC_FILE_LIST_1.stream()
                    .map(file -> new CursorFile(file.filename(), file.namespace(), file.filesize()))
                    .collect(Collectors.toList()));
    _fileListDao.addNewFileList(builder.build());

    builder =
        new CpsBackupCursorFileList.Builder()
            .withSnapshotId(oid(41))
            .withBatchIndex(2)
            .withCursorId(440L)
            .withFiles(
                WTC_FILE_LIST_2.stream()
                    .map(file -> new CursorFile(file.filename(), file.namespace(), file.filesize()))
                    .collect(Collectors.toList()));
    _fileListDao.addNewFileList(builder.build());

    // empty file to mark end
    builder =
        new CpsBackupCursorFileList.Builder()
            .withSnapshotId(oid(41))
            .withBatchIndex(3)
            .withCursorId(440L);
    _fileListDao.addNewFileList(builder.build());
  }

  private String getPutBatchURl() {
    return URL_PREFIX
        + "/batches/"
        + GROUP_ID
        + "/cluster/"
        + CLUSTER_NAME
        + "/"
        + RS_ID
        + "?sk="
        + SESSION_ID;
  }

  private String getPutCursorExtentCompleteURL() {
    return URL_PREFIX
        + "/wtcExtendComplete/"
        + GROUP_ID
        + "/cluster/"
        + CLUSTER_NAME
        + "/"
        + RS_ID
        + "?sk="
        + SESSION_ID;
  }

  private String getPutCursorKilledURL() {
    return URL_PREFIX
        + "/killCursor/"
        + GROUP_ID
        + "/cluster/"
        + CLUSTER_NAME
        + "/"
        + RS_ID
        + "/"
        + CURSOR_ID
        + "?sk="
        + SESSION_ID;
  }

  private String getGetCursorFilesV2Url(final ObjectId restoreJobId) {
    return URL_PREFIX + "/v2/files/" + GROUP_ID + "/" + restoreJobId.toHexString();
  }

  private String getGetCursorFilesForSnapshotUrl(
      final ObjectId snapshotId, final boolean isRelativeToVolumePath) {
    return URL_PREFIX
        + "/v2/files/"
        + GROUP_ID
        + "/snapshot/"
        + snapshotId.toHexString()
        + "?isRelativeToVolumePath="
        + isRelativeToVolumePath;
  }

  private void verifyBatchCorrectness(final int numberOfFiles, final int batchIndex) {
    final BasicDBObject file =
        _fileListDao.findOne(new BasicDBObject(FieldDefs.BATCH_INDEX, batchIndex));
    assertNotNull(file);
    final CpsBackupCursorFileList fileList = new CpsBackupCursorFileList(file);
    assertEquals(GROUP_ID, fileList.getProjectId());
    assertEquals(CURSOR_ID, fileList.getCursorId());
    assertEquals(BACKUP_SNAPSHOT_ID, fileList.getSnapshotId());
    assertEquals(batchIndex, fileList.getBatchIndex());

    final List<CursorFile> files = fileList.getFiles();
    assertEquals(numberOfFiles, files.size());
  }

  private void verifySnapshotAgentStatusUpdated(
      final ReplicaSetBackupSnapshot.CpsWtCheckpoint.State expectedState) {

    final Optional<ReplicaSetBackupSnapshot> snapshot =
        _snapshotDao.findReplicaSetSnapshotById(BACKUP_SNAPSHOT_ID);
    assertTrue(snapshot.isPresent());
    final ReplicaSetBackupSnapshot.CpsWtCheckpoint checkpoint = snapshot.get().getCpsWtCheckpoint();

    assertEquals(expectedState, checkpoint.getState());
  }

  private static String genStr(final char character, final int times) {
    final StringBuilder sb = new StringBuilder();
    for (int i = 0; i < times; i++) {
      sb.append(character);
    }
    return sb.toString();
  }
}
