package com.xgen.svc.nds.res;

import static com.xgen.svc.nds.res.FTSIndexConfigMongotApiResource.FTS_INDEX_CONF_CALL_API_REQUEST_DURATION_HIST_NAME;
import static com.xgen.svc.search.confcall.Messages.REPLICATION_STOPPED_MESSAGE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mongodb.BasicDBObject;
import com.mongodb.ConnectionString;
import com.mongodb.ReadPreference;
import com.mongodb.Tag;
import com.mongodb.TagSet;
import com.mongodb.TaggableReadPreference;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.SettingType;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.deployment._public.model.AuthUser;
import com.xgen.cloud.fts.activity._public.audit.FTSIndexAudit.Type;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.NodeReadPreferenceTag;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.fts._public.model.FTSCrashReportIncident;
import com.xgen.cloud.nds.fts._public.model.FTSCrashReportIncident.FieldDefs;
import com.xgen.cloud.nds.fts._public.model.FTSCrashReportIncident.IncidentErrorCodes;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSIndex.Status;
import com.xgen.cloud.nds.fts._public.model.FTSIndexConfig;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat.StatusCode;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndex;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.search.common._public.util.HostnameUtil;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroup;
import com.xgen.cloud.search.decoupled.config._public.util.SearchHostnameManager;
import com.xgen.svc.common.FeatureFlagIntTestUtil;
import com.xgen.svc.mms.api.res.common.ApiError;
import com.xgen.svc.mms.svc.metrics.MetricAssertionHelper;
import com.xgen.svc.nds.model.mongotview.FTSReplicationStatusView;
import com.xgen.svc.nds.svc.FTSIndexConfigMongotApiSvc;
import com.xgen.svc.search.confcall.SourceClusterType;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import io.prometheus.client.CollectorRegistry;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ExtendWith(GuiceTestExtension.class)
public class FTSIndexConfigMongotApiResourceIntTests
    extends BaseFTSIndexConfigMongotApiResourceIntTest {
  private static final Set<String> RESPONSE_FIELDS =
      Set.of(
          "mongoDbUri",
          "mongoDbClusterUri",
          "createDate",
          "version",
          "_id",
          "lastUpdateDate",
          "mongosUri",
          "indexes",
          "vectorIndexes",
          "analyzers",
          "blobstoreParams",
          "embeddingParams",
          "embeddingParamsV1");

  private static final Logger LOG =
      LoggerFactory.getLogger(FTSIndexConfigMongotApiResourceIntTests.class);

  private final String MONGOT_VERSION_WITH_SYNCHRONOUS_STEADY_STATE_REPLICATION = "1.42.0";
  private final String MONGOT_VERSION_WITHOUT_SYNCHRONOUS_STEADY_STATE_REPLICATION = "1.41.3";

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    // All tests use an aws cluster configuration.
    setupAwsSingleRegionCluster();
  }

  @Test
  public void testUpdateIndexConfigNoIndexesForDecoupled() throws Exception {
    setUpAwsDecoupledSearchNodes(Cluster.getCluster(_cluster, List.of(_hardware)));

    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    // Hostname does not exist
    doAgentApiCallPostWithStatus(
        _group,
        _apiKey,
        path,
        new JSONObject(
            Map.of(
                "hostname",
                "search-fake-partition-host",
                "mongotVersion",
                MONGOT_VERSION_WITH_SYNCHRONOUS_STEADY_STATE_REPLICATION)),
        404);
    final String hostname = getDecoupledMongotHostname();
    // Cluster does not have FTS index config
    final Map<String, Object> mongotRequest =
        Map.of(
            "hostname",
            hostname,
            "mongotVersion",
            MONGOT_VERSION_WITH_SYNCHRONOUS_STEADY_STATE_REPLICATION);
    final JSONObject response =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

    assertEquals(RESPONSE_FIELDS, response.keySet());
    assertTrue(
        RESPONSE_FIELDS.stream()
            .allMatch(
                field ->
                    response.isNull(field)
                        || field.equals("mongoDbUri")
                        || field.equals("mongoDbClusterUri")));

    final String mongoDbUri = response.getString("mongoDbUri");
    List<String> mongodHostnames =
        _hardware.getHardware().stream()
            .map(InstanceHardware::getHostnameForAgents)
            .flatMap(Optional::stream)
            .sorted()
            .collect(Collectors.toList());
    assertTrue(mongoDbUri.contains(targetMongodHostname(mongodHostnames, hostname)));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));
    assertTrue(
        CollectorRegistry.defaultRegistry.getSampleValue(
                "mms_nds_fts_conf_call_api_empty_indexes_request_total",
                new String[] {"searchHostType"},
                new String[] {"decoupled"})
            >= 1.0);

    // Verify cluster connection string:
    {
      // Verify coupled feature flag has no impact on cluster connection string
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_SEARCH_SYNCHRONOUS_STEADY_STATE_REPLICATION);

      JSONObject responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      assertFalse(responseWithClusterUri.isNull("mongoDbClusterUri"));
    }

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.DEDICATED_ATLAS_SEARCH_NODES_READ_PREFERENCE);

    {
      // Instance size not in allow-listed
      JSONObject responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      assertFalse(responseWithClusterUri.isNull("mongoDbClusterUri"));
    }

    {
      // Verify decoupled cluster connection string
      _appSettings.setProp(
          FTSIndexConfigMongotApiSvc.DECOUPLED_CLUSTER_URI_ENABLED_DEPLOYMENT_SIZES_KEY,
          "S20_HIGHCPU_NVME",
          SettingType.MEMORY);

      final JSONObject responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      assertEquals(responseWithClusterUri.keySet(), RESPONSE_FIELDS);
      assertTrue(
          RESPONSE_FIELDS.stream()
              .allMatch(
                  field ->
                      responseWithClusterUri.isNull(field)
                          || field.equals("mongoDbUri")
                          || field.equals("mongoDbClusterUri")));

      final String mongoDbClusterUri = responseWithClusterUri.getString("mongoDbClusterUri");
      assertFalse(mongoDbClusterUri.isEmpty());
      final var clusterConnectionString = new ConnectionString(mongoDbClusterUri);

      final var expectedSeedHosts =
          mongodHostnames.stream()
              .map(host -> host + ":" + NDSDefaults.MONGOD_PUBLIC_PORT)
              .sorted()
              .limit(2)
              .toList();

      assertEquals(expectedSeedHosts, clusterConnectionString.getHosts());
      assertEquals(AuthUser.MONGOT_USER, clusterConnectionString.getCredential().getUserName());
      assertEquals(
          ReadPreference.secondary().getName(),
          clusterConnectionString.getReadPreference().getName());
      assertEquals(
          List.of(
              new TagSet(
                  List.of(
                      new Tag(
                          NodeReadPreferenceTag.TagKeys.WORKLOAD_TYPE,
                          NodeReadPreferenceTag.OPERATIONAL_WORKLOAD_TYPE)))),
          ((TaggableReadPreference) clusterConnectionString.getReadPreference()).getTagSetList());
      assertNull(clusterConnectionString.isDirectConnection());
    }
  }

  @Test
  public void testUpdateIndexConfigForDecoupled() throws Exception {
    setUpAwsDecoupledSearchNodes(Cluster.getCluster(_cluster, List.of(_hardware)));

    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    final List<String> mongotHostnames = getDecoupledMongotHostnames(_mongotPartitionGroups.get(0));
    // Cluster has FTS index config
    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .build();
    _indexSvc.addFTSIndex(_group.getId(), _cluster.getName(), ftsIndex, null);
    _indexSvc.ensureFTSIndexConfig(_group.getId(), _cluster.getName());

    // add a basic index stats
    _indexSvc.setStatsDefinition(
        _group.getId(),
        _cluster.getName(),
        indexId,
        mongotHostnames.get(0),
        FTSSearchIndexHostStat.builder()
            .statusCode(FTSIndexHostStat.StatusCode.NOT_STARTED)
            .build());
    final Map<String, Object> mongotRequest =
        generateMongotRequest(mongotHostnames.get(0), StatusCode.STEADY, ftsIndex);

    // set lastUpdateTime of the host to one minute ago
    final Calendar currentTime = Calendar.getInstance();
    currentTime.add(Calendar.MINUTE, -1);
    _indexDao.setLastUpdateDateForHost(
        _group.getId(), _cluster.getName(), mongotHostnames.get(0), indexId, currentTime.getTime());
    final JSONObject response =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

    final String mongoDbUri = response.getString("mongoDbUri");
    List<String> mongodHostnames =
        _hardware.getHardware().stream()
            .map(InstanceHardware::getHostnameForAgents)
            .flatMap(Optional::stream)
            .sorted()
            .collect(Collectors.toList());
    assertTrue(mongoDbUri.contains(targetMongodHostname(mongodHostnames, mongotHostnames.get(0))));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    assertTrue(response.isNull("mongosUri"));

    assertTrue(response.has("indexes"));
    assertTrue(response.has("analyzers"));
    final JSONObject index = response.getJSONArray("indexes").getJSONObject(0);
    assertTrue(index.has("definition"));

    // If not all mongots report STEADY for index, then we report index status as IN_PROGRESS.
    assertEquals(
        Status.IN_PROGRESS,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // If all mongots report STEADY for index, then we report index status as STEADY.
    {
      _indexDao.setLastUpdateDateForHost(
          _group.getId(),
          _cluster.getName(),
          mongotHostnames.get(0),
          indexId,
          Date.from(Instant.now().minusSeconds(90)));

      final Map<String, Object> mongotRequestHost2 =
          generateMongotRequest(mongotHostnames.get(1), StatusCode.STEADY, ftsIndex);

      assertTrue(
          new JSONObject(
                  doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestHost2)))
              .has("indexes"));

      assertEquals(
          Status.STEADY,
          _indexSvc
              .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
              .orElseThrow()
              .getStatus());
    }
    // Mongot continues to report index status as STEADY => index continues to be in STEADY state.
    {
      _indexDao.setLastUpdateDateForHost(
          _group.getId(),
          _cluster.getName(),
          mongotHostnames.get(0),
          indexId,
          Date.from(Instant.now().minusSeconds(90)));
      final Map<String, Object> mongotRequestHost2 =
          generateMongotRequest(mongotHostnames.get(1), StatusCode.STEADY, ftsIndex);
      assertTrue(
          new JSONObject(
                  doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestHost2)))
              .has("indexes"));
      assertEquals(
          Status.STEADY,
          _indexSvc
              .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
              .orElseThrow()
              .getStatus());
    }

    // request delete
    _indexSvc.requestDeleteFTSIndex(_group.getId(), _cluster.getName(), indexId, null);

    // set lastUpdateTime of the host to one minute ago
    _indexDao.setLastUpdateDateForHost(
        _group.getId(),
        _cluster.getName(),
        getDecoupledMongotHostname(),
        indexId,
        currentTime.getTime());
    // index still exists after first conf call
    final JSONObject response2 =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));
    assertTrue(response2.has("indexes"));
    assertTrue(response2.has("analyzers"));

    final JSONObject index2 = response2.getJSONArray("indexes").getJSONObject(0);
    assertTrue(index2.has("definition"));

    // set lastUpdateTime of the host to one minute ago
    _indexDao.setLastUpdateDateForHost(
        _group.getId(),
        _cluster.getName(),
        getDecoupledMongotHostname(),
        indexId,
        currentTime.getTime());

    // index is deleted during second conf call
    final JSONObject response3 =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));
    assertTrue(response3.has("indexes"));
    assertTrue(response3.has("analyzers"));

    final Object index3 = response3.getJSONArray("indexes").opt(0);
    assertNull(index3);

    final List<Event> buildCompleteAudits =
        _auditSvc.findAll(
            10,
            Collections.singletonList(Type.FTS_INDEX_BUILD_COMPLETE.name()),
            new Date(0),
            DateUtils.addMinutes(new Date(), 5));

    // should have 1 build complete audit
    assertTrue(
        CollectorRegistry.defaultRegistry.getSampleValue(
                "mms_nds_fts_conf_call_api_duration_seconds_count",
                new String[] {"searchHostType", "sourceClusterType"},
                new String[] {"decoupled", SourceClusterType.DEDICATED_REPLICA_SET.name()})
            >= 1.0);
  }

  @Test
  public void testUpdateIndexConfig_search() throws Exception {
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    // Hostname does not exist
    doAgentApiCallPostWithStatus(
        _group, _apiKey, path, new JSONObject(Map.of("hostname", "fake-host")), 404);

    // Cluster does not have FTS index config
    final String hostname = _primaryHostname;
    List<String> nonPrimaryHostnames =
        _hardware.getHardware().stream()
            .map(hardware -> hardware.getHostnameForAgents().get())
            .filter(name -> !name.equals(_primaryHostname))
            .toList();
    JSONObject response =
        new JSONObject(
            doAgentApiCallPost(
                _group, _apiKey, path, new JSONObject(Map.of("hostname", hostname))));
    String mongoDbUri = response.getString("mongoDbUri");
    assertTrue(mongoDbUri.contains(hostname));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    // Cluster has FTS index config
    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .build();
    _indexSvc.addFTSIndex(_group.getId(), _cluster.getName(), ftsIndex, null);
    _indexSvc.ensureFTSIndexConfig(_group.getId(), _cluster.getName());

    // add a basic index stats
    _indexSvc.setStatsDefinition(
        _group.getId(),
        _cluster.getName(),
        indexId,
        hostname,
        FTSSearchIndexHostStat.builder()
            .statusCode(FTSIndexHostStat.StatusCode.NOT_STARTED)
            .build());
    final Map<String, Object> mongotRequest =
        generateMongotRequest(hostname, FTSIndexHostStat.StatusCode.INITIAL_SYNC, ftsIndex);
    // set lastUpdateTime of the host to one minute ago
    final Calendar currentTime = Calendar.getInstance();
    currentTime.add(Calendar.MINUTE, -1);
    _indexDao.setLastUpdateDateForHost(
        _group.getId(), _cluster.getName(), hostname, indexId, currentTime.getTime());
    response =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

    mongoDbUri = response.getString("mongoDbUri");
    assertTrue(mongoDbUri.contains(hostname));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    assertTrue(response.isNull("mongosUri"));

    assertTrue(response.has("indexes"));
    assertTrue(response.has("analyzers"));

    final JSONObject index = response.getJSONArray("indexes").getJSONObject(0);
    assertTrue(index.has("definition"));

    // If mongot for primary does not report STEADY, then we report status as IN_PROGRESS.
    assertEquals(
        Status.IN_PROGRESS,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // Except for primary, if other mongots report STEADY for index, then we report status as
    // IN_PROGRESS.
    nonPrimaryHostnames.forEach(
        currentHostname -> {
          try {
            _indexDao.setLastUpdateDateForHost(
                _group.getId(),
                _cluster.getName(),
                hostname,
                indexId,
                Date.from(Instant.now().minusSeconds(90)));
            assertTrue(
                new JSONObject(
                        doAgentApiCallPost(
                            _group,
                            _apiKey,
                            path,
                            new JSONObject(
                                generateMongotRequest(
                                    currentHostname, StatusCode.STEADY, ftsIndex))))
                    .has("indexes"));
          } catch (IOException pE) {
            throw new RuntimeException(pE);
          }
        });
    assertEquals(
        Status.IN_PROGRESS,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // Mongot for primary reports status STEADY for index => status STEADY.
    _indexDao.setLastUpdateDateForHost(
        _group.getId(),
        _cluster.getName(),
        hostname,
        indexId,
        Date.from(Instant.now().minusSeconds(90)));
    doAgentApiCallPost(
        _group,
        _apiKey,
        path,
        new JSONObject(generateMongotRequest(hostname, StatusCode.STEADY, ftsIndex)));

    assertEquals(
        Status.STEADY,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // Mongot for primary continues to report status STEADY for index => status STEADY.
    _indexDao.setLastUpdateDateForHost(
        _group.getId(),
        _cluster.getName(),
        hostname,
        indexId,
        Date.from(Instant.now().minusSeconds(90)));
    doAgentApiCallPost(
        _group,
        _apiKey,
        path,
        new JSONObject(generateMongotRequest(hostname, StatusCode.STEADY, ftsIndex)));
    assertEquals(
        Status.STEADY,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // Mongot for non-primary reports status INITIAL_SYNC => status IN_PROGRESS.
    final String nonPrimaryHostname = nonPrimaryHostnames.get(0);
    _indexDao.setLastUpdateDateForHost(
        _group.getId(),
        _cluster.getName(),
        nonPrimaryHostname,
        indexId,
        Date.from(Instant.now().minusSeconds(90)));
    new JSONObject(
        doAgentApiCallPost(
            _group,
            _apiKey,
            path,
            new JSONObject(
                generateMongotRequest(nonPrimaryHostname, StatusCode.INITIAL_SYNC, ftsIndex))));
    assertEquals(
        Status.IN_PROGRESS,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // request delete
    _indexSvc.requestDeleteFTSIndex(_group.getId(), _cluster.getName(), indexId, null);

    // set lastUpdateTime of the host to one minute ago
    _indexDao.setLastUpdateDateForHost(
        _group.getId(), _cluster.getName(), hostname, indexId, currentTime.getTime());
    // index still exists after first conf call
    final JSONObject response2 =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));
    assertTrue(response2.has("indexes"));
    assertTrue(response2.has("analyzers"));

    final JSONObject index2 = response2.getJSONArray("indexes").getJSONObject(0);
    assertTrue(index2.has("definition"));

    // set lastUpdateTime of the host to one minute ago
    _indexDao.setLastUpdateDateForHost(
        _group.getId(), _cluster.getName(), hostname, indexId, currentTime.getTime());

    // index is deleted during second conf call
    final JSONObject response3 =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));
    assertTrue(response3.has("indexes"));
    assertTrue(response3.has("analyzers"));

    final Object index3 = response3.getJSONArray("indexes").opt(0);
    assertNull(index3);

    final List<Event> buildCompleteAudits =
        _auditSvc.findAll(
            10,
            Collections.singletonList(Type.FTS_INDEX_BUILD_COMPLETE.name()),
            new Date(0),
            DateUtils.addMinutes(new Date(), 5));

    // should have 1 build complete audit
    assertEquals(1, buildCompleteAudits.size());
  }

  @Test
  public void testUpdateIndexConfig_vectorSearch() throws Exception {
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    // Hostname does not exist
    doAgentApiCallPostWithStatus(
        _group, _apiKey, path, new JSONObject(Map.of("hostname", "fake-host")), 404);

    // Cluster does not have FTS index config
    final String hostname = _primaryHostname;
    List<String> nonPrimaryHostnames =
        _hardware.getHardware().stream()
            .map(hardware -> hardware.getHostnameForAgents().get())
            .filter(name -> !name.equals(_primaryHostname))
            .toList();
    JSONObject response =
        new JSONObject(
            doAgentApiCallPost(
                _group, _apiKey, path, new JSONObject(Map.of("hostname", hostname))));
    String mongoDbUri = response.getString("mongoDbUri");
    assertTrue(mongoDbUri.contains(hostname));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    // Cluster has FTS index config
    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSVectorSearchIndex ftsIndex =
        new FTSVectorSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setFields(List.of(new BasicDBObject()))
            .build();
    _indexSvc.addFTSIndex(_group.getId(), _cluster.getName(), ftsIndex, null);
    _indexSvc.ensureFTSIndexConfig(_group.getId(), _cluster.getName());

    // add a basic index stats
    _indexSvc.setStatsDefinition(
        _group.getId(),
        _cluster.getName(),
        indexId,
        hostname,
        FTSSearchIndexHostStat.builder()
            .statusCode(FTSIndexHostStat.StatusCode.NOT_STARTED)
            .build());
    final Map<String, Object> mongotRequest =
        generateMongotRequest(hostname, FTSIndexHostStat.StatusCode.INITIAL_SYNC, ftsIndex);
    // set lastUpdateTime of the host to one minute ago
    final Calendar currentTime = Calendar.getInstance();
    currentTime.add(Calendar.MINUTE, -1);
    _indexDao.setLastUpdateDateForHost(
        _group.getId(), _cluster.getName(), hostname, indexId, currentTime.getTime());
    response =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

    mongoDbUri = response.getString("mongoDbUri");
    assertTrue(mongoDbUri.contains(hostname));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    assertTrue(response.isNull("mongosUri"));

    assertTrue(response.has("vectorIndexes"));
    assertTrue(response.has("analyzers"));

    final JSONObject index = response.getJSONArray("vectorIndexes").getJSONObject(0);
    assertTrue(index.has("definition"));

    // If mongot for primary does not report STEADY, then we report status as IN_PROGRESS.
    assertEquals(
        Status.IN_PROGRESS,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // Except for primary, if other mongots report STEADY for index, then we report status as
    // IN_PROGRESS.
    nonPrimaryHostnames.forEach(
        currentHostname -> {
          try {
            _indexDao.setLastUpdateDateForHost(
                _group.getId(),
                _cluster.getName(),
                hostname,
                indexId,
                Date.from(Instant.now().minusSeconds(90)));
            assertTrue(
                new JSONObject(
                        doAgentApiCallPost(
                            _group,
                            _apiKey,
                            path,
                            new JSONObject(
                                generateMongotRequest(
                                    currentHostname, StatusCode.STEADY, ftsIndex))))
                    .has("vectorIndexes"));

          } catch (IOException pE) {
            throw new RuntimeException(pE);
          }
        });
    assertEquals(
        Status.IN_PROGRESS,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // Mongot for primary reports status STEADY for index => status STEADY.
    _indexDao.setLastUpdateDateForHost(
        _group.getId(),
        _cluster.getName(),
        hostname,
        indexId,
        Date.from(Instant.now().minusSeconds(90)));
    doAgentApiCallPost(
        _group,
        _apiKey,
        path,
        new JSONObject(generateMongotRequest(hostname, StatusCode.STEADY, ftsIndex)));

    assertEquals(
        Status.STEADY,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // Mongot for primary continues to report status STEADY for index => status STEADY.
    _indexDao.setLastUpdateDateForHost(
        _group.getId(),
        _cluster.getName(),
        hostname,
        indexId,
        Date.from(Instant.now().minusSeconds(90)));
    doAgentApiCallPost(
        _group,
        _apiKey,
        path,
        new JSONObject(generateMongotRequest(hostname, StatusCode.STEADY, ftsIndex)));
    assertEquals(
        Status.STEADY,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // Mongot for non-primary reports status INITIAL_SYNC => status IN_PROGRESS.
    final String nonPrimaryHostname = nonPrimaryHostnames.get(0);
    _indexDao.setLastUpdateDateForHost(
        _group.getId(),
        _cluster.getName(),
        nonPrimaryHostname,
        indexId,
        Date.from(Instant.now().minusSeconds(90)));
    new JSONObject(
        doAgentApiCallPost(
            _group,
            _apiKey,
            path,
            new JSONObject(
                generateMongotRequest(nonPrimaryHostname, StatusCode.INITIAL_SYNC, ftsIndex))));
    assertEquals(
        Status.IN_PROGRESS,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .getStatus());

    // request delete
    _indexSvc.requestDeleteFTSIndex(_group.getId(), _cluster.getName(), indexId, null);

    // set lastUpdateTime of the host to one minute ago
    _indexDao.setLastUpdateDateForHost(
        _group.getId(), _cluster.getName(), hostname, indexId, currentTime.getTime());
    // index still exists after first conf call
    final JSONObject response2 =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));
    assertTrue(response2.has("vectorIndexes"));
    assertTrue(response2.has("analyzers"));

    final JSONObject index2 = response2.getJSONArray("vectorIndexes").getJSONObject(0);
    assertTrue(index2.has("definition"));

    // set lastUpdateTime of the host to one minute ago
    _indexDao.setLastUpdateDateForHost(
        _group.getId(), _cluster.getName(), hostname, indexId, currentTime.getTime());

    // index is deleted during second conf call
    final JSONObject response3 =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));
    assertTrue(response3.has("vectorIndexes"));
    assertTrue(response3.has("analyzers"));

    final Object index3 = response3.getJSONArray("vectorIndexes").opt(0);
    assertNull(index3);

    final List<Event> buildCompleteAudits =
        _auditSvc.findAll(
            10,
            Collections.singletonList(Type.FTS_INDEX_BUILD_COMPLETE.name()),
            new Date(0),
            DateUtils.addMinutes(new Date(), 5));

    // should have 1 build complete audit
    assertEquals(1, buildCompleteAudits.size());
  }

  @Test
  public void testUpdateIndexConfig_ignoreUnknownStatus() throws Exception {
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();

    final String hostname = _primaryHostname;

    // Cluster has FTS index config
    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .build();
    _indexSvc.addFTSIndex(_group.getId(), _cluster.getName(), ftsIndex, null);
    _indexSvc.ensureFTSIndexConfig(_group.getId(), _cluster.getName());

    // add a steady index stats
    _indexSvc.setStatsDefinition(
        _group.getId(),
        _cluster.getName(),
        indexId,
        hostname,
        FTSSearchIndexHostStat.builder().statusCode(StatusCode.STEADY).build());

    // The status in the mongot request is unknown.
    final Map<String, Object> mongotRequest =
        generateMongotRequest(hostname, StatusCode.NOT_STARTED, ftsIndex);
    var indexJson = ((JSONArray) mongotRequest.get("indexes")).getJSONObject(0);
    indexJson.getJSONObject("stats").getJSONObject(hostname).put("statusCode", "UNKNOWN");

    // set lastUpdateTime of the host to one minute ago
    final Calendar currentTime = Calendar.getInstance();
    currentTime.add(Calendar.MINUTE, -1);
    _indexDao.setLastUpdateDateForHost(
        _group.getId(), _cluster.getName(), hostname, indexId, currentTime.getTime());

    // Run conf call.
    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest));

    // The host is still steady.
    assertEquals(
        StatusCode.STEADY,
        _indexSvc
            .getFTSIndex(_group.getId(), _cluster.getName(), indexId)
            .orElseThrow()
            .toTextSearchIndex()
            .getStats()
            .orElseThrow()
            .getByHostname(HostnameUtil.getHostnameWithoutDomain(hostname))
            .orElseThrow()
            .getStatusCode()
            .orElseThrow());
  }

  @Test
  public void testUpdateIndexConfigNoIndexInView() throws Exception {
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();

    // Cluster has FTS index config
    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .build();
    _indexSvc.addFTSIndex(_group.getId(), _cluster.getName(), ftsIndex, null);
    _indexSvc.ensureFTSIndexConfig(_group.getId(), _cluster.getName());

    final String hostname = _hardware.getHardware().get(0).getHostnameForAgents().get();
    final Map<String, Object> mongotRequest =
        Map.of(
            "hostname",
            hostname,
            "mongotVersion",
            MONGOT_VERSION_WITH_SYNCHRONOUS_STEADY_STATE_REPLICATION);
    final Map<String, Object> oldMongotRequest =
        Map.of(
            "hostname",
            hostname,
            "mongotVersion",
            MONGOT_VERSION_WITHOUT_SYNCHRONOUS_STEADY_STATE_REPLICATION);

    final JSONObject response =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));
    final String mongoDbUri = response.getString("mongoDbUri");
    assertTrue(mongoDbUri.contains(hostname));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    assertTrue(response.isNull("mongosUri"));

    assertTrue(response.has("indexes"));
    assertTrue(response.has("analyzers"));

    final JSONObject index = response.getJSONArray("indexes").getJSONObject(0);
    assertTrue(index.has("definition"));
    assertEquals(index.getJSONObject("definition").get("name"), "foo");
    assertFalse(index.has("stats"));

    // Verify cluster connection string
    {
      // Verify decoupled feature flag has no impact on cluster connection string
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.DEDICATED_ATLAS_SEARCH_NODES_READ_PREFERENCE);

      JSONObject responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      assertFalse(responseWithClusterUri.isNull("mongoDbClusterUri"));
    }

    FeatureFlagIntTestUtil.enableFeatureForEntity(
        _group, null, FeatureFlag.ATLAS_SEARCH_SYNCHRONOUS_STEADY_STATE_REPLICATION);

    {
      // Cluster instance size is not allow-listed
      JSONObject responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      assertFalse(responseWithClusterUri.isNull("mongoDbClusterUri"));
    }

    _appSettings.setProp(
        FTSIndexConfigMongotApiSvc.COUPLED_CLUSTER_URI_ENABLED_CLUSTER_SIZES_KEY,
        "M30",
        SettingType.MEMORY);

    {
      // Version is too old
      JSONObject responseWithClusterUri =
          new JSONObject(
              doAgentApiCallPost(_group, _apiKey, path, new JSONObject(oldMongotRequest)));

      assertTrue(responseWithClusterUri.isNull("mongoDbClusterUri"));
    }

    {
      // verify coupled cluster connection string
      final JSONObject responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      assertEquals(RESPONSE_FIELDS, responseWithClusterUri.keySet());
      final String mongoDbClusterUri = responseWithClusterUri.getString("mongoDbClusterUri");
      assertFalse(mongoDbClusterUri.isEmpty());
      final var clusterConnectionString = new ConnectionString(mongoDbClusterUri);

      final var expectedSeedHosts = List.of(hostname + ":" + NDSDefaults.MONGOD_PUBLIC_PORT);
      assertEquals(expectedSeedHosts, clusterConnectionString.getHosts());
      assertEquals(AuthUser.MONGOT_USER, clusterConnectionString.getCredential().getUserName());
      assertNull(clusterConnectionString.getReadPreference());
      assertNotNull(clusterConnectionString.isDirectConnection());
      assertTrue(clusterConnectionString.isDirectConnection());
    }
  }

  // Tests both coupled and decoupled conf calls.
  @Test
  public void testUpdateIndexConfig_statsWithDomainName() throws Exception {
    setUpAwsDecoupledSearchNodes(Cluster.getCluster(_cluster, List.of(_hardware)));

    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    for (String hostname :
        List.of(
            _hardware.getHardware().get(0).getHostnameForAgents().get(),
            getDecoupledMongotHostname())) {
      final ObjectId indexId = new ObjectId();
      final UUID uuid = UUID.randomUUID();
      final FTSSearchIndex ftsIndex =
          new FTSSearchIndex.Builder()
              .setIndexId(indexId)
              .setName("foo" + HostnameUtil.getHostnameWithoutDomain(hostname))
              .setDatabase("test")
              .setLastObservedCollectionName("foo")
              .setCollectionUUID(uuid)
              .setMappings(new BasicDBObject("dynamic", true))
              .build();
      _indexSvc.addFTSIndex(_group.getId(), _cluster.getName(), ftsIndex, null);

      final String hostnameWithoutDomain = HostnameUtil.getHostnameWithoutDomain(hostname);

      final FTSIndexHostStat updatedHostStat =
          FTSSearchIndexHostStat.builder()
              .statusCode(FTSIndexHostStat.StatusCode.RECOVERING)
              .statusDetail(
                  FTSSearchIndexHostStat.StatusDetail.builder()
                      .index(FTSIndexHostStat.StatusCode.RECOVERING)
                      .synonyms(
                          List.of(
                              FTSSearchIndexHostStat.SynonymStatus.builder()
                                  .name("synonym1")
                                  .status(
                                      FTSSearchIndexHostStat.SynonymStatus.StatusCode.SYNC_ENQUEUED)
                                  .build()))
                      .build())
              .build();
      final JSONObject indexStatsMongotView =
          new JSONObject(Map.of(hostname, new JSONObject(updatedHostStat.toDBObject().toMap())));
      final JSONObject indexDefinitionView =
          new JSONObject(
              Map.of(
                  "indexID",
                  indexId.toString(),
                  "lastObservedCollectionName",
                  "foo",
                  "collectionUUID",
                  uuid.toString()));
      final List<JSONObject> mongotView =
          List.of(
              new JSONObject(
                  Map.of("definition", indexDefinitionView, "stats", indexStatsMongotView)));
      final Map<String, Object> mongotRequest =
          Map.of("hostname", hostname, "indexes", new JSONArray(mongotView));

      doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest));

      final FTSIndex ftsIndex1 =
          _indexSvc
              .getFTSIndex(_group.getId(), _cluster.getName(), ftsIndex.getIndexId())
              .orElseThrow();

      final Optional<? extends FTSIndexHostStat> maybeIndexStats =
          ftsIndex1.getHostStat(hostnameWithoutDomain);

      if (SearchHostnameManager.isSearchInstanceHostname(hostname)) {
        final FTSIndexHostStat indexStats = maybeIndexStats.orElseThrow();
        assertEquals(updatedHostStat.getStatusCode(), indexStats.getStatusCode());
        assertEquals(updatedHostStat.getStatusDetail(), indexStats.getStatusDetail());
      } else {
        assertTrue(maybeIndexStats.isEmpty());
      }
    }
  }

  public void testUpdateIndexConfig_replicationStopped(
      Status initialStatus,
      Status expectedStatus,
      StatusCode initialStatusCode,
      StatusCode otherHostStatusCode,
      StatusCode expectedStatusCode,
      String expectedErrorMessage)
      throws Exception {
    setUpAwsDecoupledSearchNodes(Cluster.getCluster(_cluster, List.of(_hardware)));

    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    final List<String> mongotHostnames = getDecoupledMongotHostnames(_mongotPartitionGroups.get(0));
    // Cluster has FTS index config
    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .setStatus(initialStatus)
            .build();
    _indexSvc.addFTSIndex(_group.getId(), _cluster.getName(), ftsIndex, null);

    // add a basic index stats
    _indexSvc.setStatsDefinition(
        _group.getId(),
        _cluster.getName(),
        indexId,
        mongotHostnames.get(0),
        FTSSearchIndexHostStat.builder()
            .statusCode(initialStatusCode)
            .statusDetail(
                new FTSSearchIndexHostStat.StatusDetail(initialStatusCode, new ArrayList<>()))
            .build());

    _indexSvc.setStatsDefinition(
        _group.getId(),
        _cluster.getName(),
        indexId,
        mongotHostnames.get(1),
        FTSSearchIndexHostStat.builder()
            .statusCode(otherHostStatusCode)
            .statusDetail(
                new FTSSearchIndexHostStat.StatusDetail(otherHostStatusCode, new ArrayList<>()))
            .build());

    final Date time = new Date();
    final FTSReplicationStatusView replicationStatusView =
        new FTSReplicationStatusView(time.toInstant(), time.toInstant(), true);
    Map<String, Object> mongotRequest =
        generateMongotRequestWithReplicationStatus(
            mongotHostnames.get(0), initialStatusCode, ftsIndex, replicationStatusView);

    // set lastUpdateTime of the host to one minute ago
    final Calendar currentTime = Calendar.getInstance();
    currentTime.add(Calendar.MINUTE, -1);
    _indexDao.setLastUpdateDateForHost(
        _group.getId(), _cluster.getName(), mongotHostnames.get(0), indexId, currentTime.getTime());
    // do the confcall
    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest));
    // get the updated config
    final FTSIndexConfig config =
        _indexSvc.getFTSIndexConfig(_group.getId(), _cluster.getName()).orElse(null);
    String hostName = HostnameUtil.getHostnameWithoutDomain(mongotHostnames.get(0));

    // Verify the replication status map is updated
    assertNotNull(config);
    assertTrue(config.getHostReplicationStatus().containsKey(hostName));
    assertTrue(config.getHostReplicationStatus().get(hostName).getIsReplicationStopped());

    final FTSIndex updatedFtsIndex = config.getFTSIndex(ftsIndex.getIndexId()).orElseThrow();
    // When replication stops,
    // the index status is marked as STALE
    assertEquals(updatedFtsIndex.getStatus(), expectedStatus);
    // indexes[?].stats.[hostname].statusCode
    assertEquals(
        updatedFtsIndex.getStats().orElseThrow().get(hostName).getStatusCode().orElseThrow(),
        expectedStatusCode);
    // indexes[?].stats.[hostname].statusDetail.index
    assertEquals(
        updatedFtsIndex
            .getStats()
            .orElseThrow()
            .get(hostName)
            .getStatusDetail()
            .orElseThrow()
            .getIndex()
            .orElseThrow(),
        expectedStatusCode);
    // indexes[?].detailedStatuses.[hostname].mainIndex.status
    assertEquals(
        updatedFtsIndex
            .getDetailedStatuses()
            .orElseThrow()
            .get(hostName)
            .getMainIndex()
            .orElseThrow()
            .getStatus(),
        expectedStatusCode);
    // indexes[?].detailedStatuses.[hostname].mainIndex.message
    assertEquals(
        updatedFtsIndex
            .getDetailedStatuses()
            .orElseThrow()
            .get(hostName)
            .getMainIndex()
            .orElseThrow()
            .getMessage()
            .orElse(null),
        expectedErrorMessage);
    // indexes[?].detailedStatuses.[hostname].stagedIndex.status
    assertEquals(
        updatedFtsIndex
            .getDetailedStatuses()
            .orElseThrow()
            .get(hostName)
            .getStagedIndex()
            .orElseThrow()
            .getStatus(),
        expectedStatusCode);
    // indexes[?].detailedStatuses.[hostname].stagedIndex.message
    assertEquals(
        updatedFtsIndex
            .getDetailedStatuses()
            .orElseThrow()
            .get(hostName)
            .getStagedIndex()
            .orElseThrow()
            .getMessage()
            .orElse(null),
        expectedErrorMessage);
  }

  @Test
  public void testUpdateIndexConfig_replicationStopped_steady() throws Exception {
    // status should be updated from STEADY to STALE if replication is stopped
    // host1: steady -> stale, host2: steady
    // index status: stale
    testUpdateIndexConfig_replicationStopped(
        Status.STEADY,
        Status.STALE,
        StatusCode.STEADY,
        StatusCode.STEADY,
        StatusCode.STALE,
        REPLICATION_STOPPED_MESSAGE);
  }

  @Test
  public void testUpdateIndexConfig_replicationStopped_inProgress() throws Exception {
    // status should not be changed to STALE for IN_PROGRESS indexes
    // host1: not_started -> in_progress, host2: steady
    // index status: in_progress
    testUpdateIndexConfig_replicationStopped(
        Status.IN_PROGRESS,
        Status.IN_PROGRESS,
        StatusCode.NOT_STARTED,
        StatusCode.STEADY,
        StatusCode.NOT_STARTED,
        null);
  }

  @Test
  public void testUpdateIndexConfig_replicationStopped_failed() throws Exception {
    // status should not be changed to STALE for FAILED indexes
    // host1: failed -> failed, host2: steady
    // index status: failed
    testUpdateIndexConfig_replicationStopped(
        Status.IN_PROGRESS,
        Status.FAILED,
        StatusCode.FAILED,
        StatusCode.STEADY,
        StatusCode.FAILED,
        null);
  }

  @Test
  public void testUpdateIndexConfig_replicationStopped_inProgress_other() throws Exception {
    // status should not be changed to STALE if other hosts are still processing the index
    // host1: steady -> staled, host2: not_started
    // index: in_progress
    testUpdateIndexConfig_replicationStopped(
        Status.IN_PROGRESS,
        Status.IN_PROGRESS,
        StatusCode.STEADY,
        StatusCode.NOT_STARTED,
        StatusCode.STALE,
        REPLICATION_STOPPED_MESSAGE);
  }

  @Test
  public void testUpdateIndexConfig_replicationStopped_stale_other() throws Exception {
    // status should be changed to STALE if it's already stale for other hosts
    // host1: steady -> staled, host2: staled
    // index: in_progress
    testUpdateIndexConfig_replicationStopped(
        Status.IN_PROGRESS,
        Status.STALE,
        StatusCode.STEADY,
        StatusCode.STALE,
        StatusCode.STALE,
        REPLICATION_STOPPED_MESSAGE);
  }

  @Test
  public void testUpdateIndexConfig_ServerlessInstance() throws Exception {
    setupServerlessInstance();
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();

    Callable<String> action =
        () ->
            doAgentApiCallPostWithStatus(
                _group,
                _apiKey,
                path,
                new JSONObject(
                    Map.of(
                        "hostname",
                        _serverlessHardware.getHardware().get(0).getHostnameForAgents().get())),
                HttpStatus.SC_BAD_REQUEST);
    final String response = action.call();
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(),
        new JSONObject(response).get(ApiError.ERROR_CODE_FIELD));

    MetricAssertionHelper.assertMetricIncrease(
        FTS_INDEX_CONF_CALL_API_REQUEST_DURATION_HIST_NAME + "_count",
        Map.of("searchHostType", "error", "sourceClusterType", "error"),
        action);
  }

  @Test
  public void testUpdateIndexConfigDecopuled_ServerlessInstance() throws Exception {
    setupServerlessInstance();
    setUpAwsDecoupledSearchNodes(
        Cluster.getCluster(_serverlessInstance, List.of(_serverlessHardware)));
    PartitionGroup serverlessPartitionGroups = _mongotPartitionGroups.get(0);
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();

    final String response =
        doAgentApiCallPostWithStatus(
            _group,
            _apiKey,
            path,
            new JSONObject(
                Map.of("hostname", getDecoupledMongotHostnames(serverlessPartitionGroups).get(0))),
            HttpStatus.SC_BAD_REQUEST);
    assertEquals(
        NDSErrorCode.INVALID_CLOUD_PROVIDER.name(),
        new JSONObject(response).get(ApiError.ERROR_CODE_FIELD));
  }

  @Test
  public void testUpdateIndexConfig_sharded() throws Exception {
    final Cluster shardedCluster = setupAwsSingleRegionShardedCluster();
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .build();
    _indexSvc.addFTSIndex(
        _group.getId(), shardedCluster.getClusterDescription().getName(), ftsIndex, null);
    _indexSvc.ensureFTSIndexConfig(
        _group.getId(), shardedCluster.getClusterDescription().getName());

    final String hostname =
        shardedCluster
            .getReplicaSetsWithShardData()
            .get(0)
            .getHardware()
            .get(0)
            .getHostnameForAgents()
            .orElseThrow();
    // add a basic index stats
    _indexSvc.setStatsDefinition(
        _group.getId(),
        shardedCluster.getClusterDescription().getName(),
        indexId,
        hostname,
        FTSSearchIndexHostStat.builder()
            .statusCode(FTSIndexHostStat.StatusCode.NOT_STARTED)
            .build());

    final FTSIndexHostStat updatedHostStat = FTSSearchIndexHostStat.builder().build();
    final JSONObject indexStatsMongotView =
        new JSONObject(Map.of(hostname, new JSONObject(updatedHostStat.toDBObject().toMap())));
    final JSONObject indexDefinitionView =
        new JSONObject(
            Map.of(
                "indexID",
                indexId.toString(),
                "lastObservedCollectionName",
                "foo",
                "collectionUUID",
                uuid.toString()));
    final List<JSONObject> mongotView =
        List.of(
            new JSONObject(
                Map.of("definition", indexDefinitionView, "stats", indexStatsMongotView)));
    final Map<String, Object> mongotRequest =
        Map.of(
            "hostname",
            hostname,
            "mongotVersion",
            MONGOT_VERSION_WITH_SYNCHRONOUS_STEADY_STATE_REPLICATION,
            "indexes",
            new JSONArray(mongotView));
    final JSONObject response =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

    final String mongoDbUri = response.getString("mongoDbUri");
    assertTrue(mongoDbUri.contains(hostname));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    final String mongosUri = response.getString("mongosUri");
    assertTrue(mongosUri.contains(hostname));
    assertTrue(mongosUri.contains(String.valueOf(NDSDefaults.MONGOS_PUBLIC_PORT)));
    assertTrue(mongosUri.contains(AuthUser.MONGOT_USER));

    assertTrue(
        CollectorRegistry.defaultRegistry.getSampleValue(
                "mms_nds_fts_conf_call_api_duration_seconds_count",
                new String[] {"searchHostType", "sourceClusterType"},
                new String[] {"coupled", SourceClusterType.DEDICATED_SHARDED.name()})
            >= 1.0);

    // Verify cluster connection string
    {
      // Verify decoupled feature flag has no impact on cluster connection string
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.DEDICATED_ATLAS_SEARCH_NODES_READ_PREFERENCE);

      JSONObject responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      assertFalse(responseWithClusterUri.isNull("mongoDbClusterUri"));
    }

    {
      // Verify coupled cluster connection string
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_SEARCH_SYNCHRONOUS_STEADY_STATE_REPLICATION);

      _appSettings.setProp(
          FTSIndexConfigMongotApiSvc.COUPLED_CLUSTER_URI_ENABLED_CLUSTER_SIZES_KEY,
          "M30,Sharded",
          SettingType.MEMORY);

      final var responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      assertEquals(RESPONSE_FIELDS, responseWithClusterUri.keySet());
      final String mongoDbClusterUri = responseWithClusterUri.getString("mongoDbClusterUri");
      assertFalse(mongoDbClusterUri.isEmpty());
      final var clusterConnectionString = new ConnectionString(mongoDbClusterUri);

      final var expectedSeedHosts = List.of(hostname + ":" + NDSDefaults.MONGOD_PUBLIC_PORT);
      assertEquals(expectedSeedHosts, clusterConnectionString.getHosts());
      assertEquals(AuthUser.MONGOT_USER, clusterConnectionString.getCredential().getUserName());
      assertNull(clusterConnectionString.getReadPreference());
      assertNotNull(clusterConnectionString.isDirectConnection());
      assertTrue(clusterConnectionString.isDirectConnection());
    }
  }

  /** Similar to {@link #testUpdateIndexConfig_sharded()} */
  @Test
  public void testUpdateIndexConfigForDecoupled_sharded() throws Exception {
    final Cluster shardedCluster = setupAwsSingleRegionShardedCluster();
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .build();
    _indexSvc.addFTSIndex(
        _group.getId(), shardedCluster.getClusterDescription().getName(), ftsIndex, null);
    _indexSvc.ensureFTSIndexConfig(
        _group.getId(), shardedCluster.getClusterDescription().getName());

    setUpAwsDecoupledSearchNodes(shardedCluster);
    final String hostname = getDecoupledMongotHostnames(_mongotPartitionGroups.get(1)).get(0);
    // add a basic index stats
    _indexSvc.setStatsDefinition(
        _group.getId(),
        shardedCluster.getClusterDescription().getName(),
        indexId,
        hostname,
        FTSSearchIndexHostStat.builder()
            .statusCode(FTSIndexHostStat.StatusCode.NOT_STARTED)
            .build());

    final FTSIndexHostStat updatedHostStat = FTSSearchIndexHostStat.builder().build();
    final JSONObject indexStatsMongotView =
        new JSONObject(Map.of(hostname, new JSONObject(updatedHostStat.toDBObject().toMap())));
    final JSONObject indexDefinitionView =
        new JSONObject(
            Map.of(
                "indexID",
                indexId.toString(),
                "lastObservedCollectionName",
                "foo",
                "collectionUUID",
                uuid.toString()));
    final List<JSONObject> mongotView =
        List.of(
            new JSONObject(
                Map.of("definition", indexDefinitionView, "stats", indexStatsMongotView)));
    final Map<String, Object> mongotRequest =
        Map.of(
            "hostname",
            hostname,
            "mongotVersion",
            MONGOT_VERSION_WITH_SYNCHRONOUS_STEADY_STATE_REPLICATION,
            "indexes",
            new JSONArray(mongotView));
    final JSONObject response =
        new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

    final String mongoDbUri = response.getString("mongoDbUri");
    assertTrue(mongoDbUri.contains("sharded-cluster-shard-01"));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    final String mongosUri = response.getString("mongosUri");
    assertTrue(mongosUri.contains("sharded-cluster-shard-01"));
    assertTrue(mongosUri.contains(String.valueOf(NDSDefaults.MONGOS_PUBLIC_PORT)));
    assertTrue(mongosUri.contains(AuthUser.MONGOT_USER));

    assertTrue(
        CollectorRegistry.defaultRegistry.getSampleValue(
                "mms_nds_fts_conf_call_api_duration_seconds_count",
                new String[] {"searchHostType", "sourceClusterType"},
                new String[] {"decoupled", SourceClusterType.DEDICATED_SHARDED.name()})
            >= 1.0);

    // Verify cluster connection string has no impact on cluster connection string.
    {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_SEARCH_SYNCHRONOUS_STEADY_STATE_REPLICATION);

      JSONObject responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      assertFalse(responseWithClusterUri.isNull("mongoDbClusterUri"));
    }

    {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.DEDICATED_ATLAS_SEARCH_NODES_READ_PREFERENCE);

      _appSettings.setProp(
          FTSIndexConfigMongotApiSvc.DECOUPLED_CLUSTER_URI_ENABLED_DEPLOYMENT_SIZES_KEY,
          "S20_HIGHCPU_NVME,Sharded",
          SettingType.MEMORY);

      final JSONObject responseWithClusterUri =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest)));

      final String mongoDbClusterUri = responseWithClusterUri.getString("mongoDbClusterUri");
      final var clusterConnectionString = new ConnectionString(mongoDbClusterUri);

      final List<String> expectedSeedHosts =
          shardedCluster.getReplicaSetsWithShardData().get(1).getHardware().stream()
              .map(i -> i.getHostnameForAgents().orElseThrow())
              .map(hostName -> hostName + ":" + NDSDefaults.MONGOD_PUBLIC_PORT)
              .sorted()
              .limit(2)
              .collect(Collectors.toUnmodifiableList());

      assertEquals(expectedSeedHosts, clusterConnectionString.getHosts());
      assertEquals(AuthUser.MONGOT_USER, clusterConnectionString.getCredential().getUserName());
      assertEquals(
          ReadPreference.secondary().getName(),
          clusterConnectionString.getReadPreference().getName());
      assertEquals(
          List.of(
              new TagSet(
                  List.of(
                      new Tag(
                          NodeReadPreferenceTag.TagKeys.WORKLOAD_TYPE,
                          NodeReadPreferenceTag.OPERATIONAL_WORKLOAD_TYPE)))),
          ((TaggableReadPreference) clusterConnectionString.getReadPreference()).getTagSetList());
      assertNull(clusterConnectionString.isDirectConnection());
    }
  }

  @Test
  public void testUpdateIndexConfigForDecoupled_multiRegion() throws Exception {
    final Cluster multiRegionCluster = setupAwsMultiRegionCluster();
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .build();
    _indexSvc.addFTSIndex(
        _group.getId(), multiRegionCluster.getClusterDescription().getName(), ftsIndex, null);
    _indexSvc.ensureFTSIndexConfig(
        _group.getId(), multiRegionCluster.getClusterDescription().getName());

    setUpAwsDecoupledSearchNodes(multiRegionCluster);

    // Make sure hostname1 is from US_EAST_1 and hostname2 is from US_WEST_1
    var hostname1 = getDecoupledMongotHostnames(_mongotPartitionGroups.get(1)).get(0);
    var hostname2 = getDecoupledMongotHostnames(_mongotPartitionGroups.get(0)).get(0);

    if (_mongotPartitionGroups
        .get(0)
        .getSyncSource()
        .getRegionName()
        .equals(AWSRegionName.US_EAST_1)) {
      hostname1 = getDecoupledMongotHostnames(_mongotPartitionGroups.get(0)).get(0);
      hostname2 = getDecoupledMongotHostnames(_mongotPartitionGroups.get(1)).get(0);
    }

    final Map<ObjectId, RegionName> instanceIdToRegionNameMap =
        multiRegionCluster
            .getReplicaSets()
            .get(0)
            .getInstanceIdToRegionNameMap(
                multiRegionCluster
                    .getClusterDescription()
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs());

    final List<String> mongodHostnames1 =
        multiRegionCluster.getReplicaSets().get(0).getHardware().stream()
            .filter(
                pInstanceHardware ->
                    instanceIdToRegionNameMap
                        .get(pInstanceHardware.getInstanceId())
                        .equals(AWSRegionName.US_EAST_1))
            .map(InstanceHardware::getHostnameForAgents)
            .flatMap(Optional::stream)
            .sorted()
            .toList();

    final List<String> mongodHostnames2 =
        multiRegionCluster.getReplicaSets().get(0).getHardware().stream()
            .filter(
                pInstanceHardware ->
                    instanceIdToRegionNameMap
                        .get(pInstanceHardware.getInstanceId())
                        .equals(AWSRegionName.US_WEST_1))
            .map(InstanceHardware::getHostnameForAgents)
            .flatMap(Optional::stream)
            .sorted()
            .toList();
    // There should be 1 node in US_EAST_1 and 2 nodes in US_WEST_1 in setupMultiRegionCluster()
    assertEquals(1, mongodHostnames1.size());
    assertEquals(2, mongodHostnames2.size());
    List<List<String>> expectedMongodHostnames = List.of(mongodHostnames1, mongodHostnames2);

    var index = 0;
    for (String hostname : List.of(hostname1, hostname2)) {
      final Map<String, Object> request =
          Map.of(
              "hostname",
              hostname,
              "mongotVersion",
              MONGOT_VERSION_WITH_SYNCHRONOUS_STEADY_STATE_REPLICATION);
      final JSONObject response =
          new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(request)));
      final String mongoDbUri = response.getString("mongoDbUri");

      assertTrue(
          mongoDbUri.contains(
              expectedMongodHostnames
                  .get(index)
                  .get(Math.floorMod(hostname.hashCode(), index + 1))));
      assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
      assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));
      assertTrue(response.isNull("mongosUri"));
      index += 1;
    }

    // Verify cluster connection string
    {
      // Verify coupled feature flag has no impact on cluster connection string
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.ATLAS_SEARCH_SYNCHRONOUS_STEADY_STATE_REPLICATION);

      for (String hostname : List.of(hostname1, hostname2)) {
        final Map<String, Object> request = Map.of("hostname", hostname);
        final JSONObject responseWithClusterUri =
            new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(request)));
        assertTrue(responseWithClusterUri.isNull("mongoDbClusterUri"));
      }
    }
    {
      FeatureFlagIntTestUtil.enableFeatureForEntity(
          _group, null, FeatureFlag.DEDICATED_ATLAS_SEARCH_NODES_READ_PREFERENCE);

      _appSettings.setProp(
          FTSIndexConfigMongotApiSvc.DECOUPLED_CLUSTER_URI_ENABLED_DEPLOYMENT_SIZES_KEY,
          "S20_HIGHCPU_NVME,Sharded",
          SettingType.MEMORY);

      final List<String> totalSeedHosts1 =
          multiRegionCluster.getReplicaSets().get(0).getHardware().stream()
              .filter(
                  i ->
                      instanceIdToRegionNameMap
                          .get(i.getInstanceId())
                          .equals(AWSRegionName.US_EAST_1))
              .map(i -> i.getHostnameForAgents().orElseThrow())
              .map(hostName -> hostName + ":" + NDSDefaults.MONGOD_PUBLIC_PORT)
              .sorted()
              .toList();
      final List<String> totalSeedHosts2 =
          multiRegionCluster.getReplicaSets().get(0).getHardware().stream()
              .filter(
                  i ->
                      instanceIdToRegionNameMap
                          .get(i.getInstanceId())
                          .equals(AWSRegionName.US_WEST_1))
              .map(i -> i.getHostnameForAgents().orElseThrow())
              .map(hostName -> hostName + ":" + NDSDefaults.MONGOD_PUBLIC_PORT)
              .sorted()
              .toList();
      assertEquals(1, totalSeedHosts1.size());
      assertEquals(2, totalSeedHosts2.size());

      index = 0;
      for (String hostname : List.of(hostname1, hostname2)) {
        final Map<String, Object> request =
            Map.of(
                "hostname",
                hostname,
                "mongotVersion",
                MONGOT_VERSION_WITH_SYNCHRONOUS_STEADY_STATE_REPLICATION);
        final JSONObject responseWithClusterUri =
            new JSONObject(doAgentApiCallPost(_group, _apiKey, path, new JSONObject(request)));
        final String mongoDbClusterUri = responseWithClusterUri.getString("mongoDbClusterUri");
        final var clusterConnectionString = new ConnectionString(mongoDbClusterUri);
        var regionName = AWSRegionName.US_EAST_1.getName();
        var expectedSeedHosts = totalSeedHosts1;
        if (index == 1) {
          expectedSeedHosts = totalSeedHosts2;
          regionName = AWSRegionName.US_WEST_1.getName();
        }
        assertEquals(expectedSeedHosts, clusterConnectionString.getHosts());
        assertEquals(AuthUser.MONGOT_USER, clusterConnectionString.getCredential().getUserName());
        assertEquals(
            ReadPreference.secondaryPreferred().getName(),
            clusterConnectionString.getReadPreference().getName());
        assertEquals(
            List.of(
                new TagSet(
                    List.of(
                        new Tag(
                            NodeReadPreferenceTag.TagKeys.WORKLOAD_TYPE,
                            NodeReadPreferenceTag.OPERATIONAL_WORKLOAD_TYPE),
                        new Tag(NodeReadPreferenceTag.TagKeys.PROVIDER, CloudProvider.PROVIDER_AWS),
                        new Tag(NodeReadPreferenceTag.TagKeys.REGION, regionName)))),
            ((TaggableReadPreference) clusterConnectionString.getReadPreference()).getTagSetList());
        assertNull(clusterConnectionString.isDirectConnection());
        index += 1;
      }
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testUpdateIndexConfig_huge(boolean gzip) throws Exception {
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    final String hostname = _hardware.getHardware().get(0).getHostnameForAgents().get();
    final int numIndexes = 3000;
    final Map<String, Object> mongotRequest =
        Map.of(
            "hostname",
            hostname,
            "mongotVersion",
            MONGOT_VERSION_WITH_SYNCHRONOUS_STEADY_STATE_REPLICATION,
            "indexes",
            generateIndexes(hostname, numIndexes));
    final Date t0 = new Date();
    final JSONObject response =
        new JSONObject(
            doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest), gzip));
    final Date t1 = new Date();

    final long requestSeconds = Duration.between(t0.toInstant(), t1.toInstant()).toSeconds();
    assertTrue(requestSeconds < 5, "Request time too long: " + requestSeconds);
    LOG.info("Request time: " + requestSeconds);

    final String mongoDbUri = response.getString("mongoDbUri");
    assertTrue(mongoDbUri.contains(hostname));
    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    assertTrue(response.isNull("mongosUri"));

    assertTrue(response.has("indexes"));
    assertTrue(response.has("analyzers"));
    assertEquals(numIndexes, response.getJSONArray("indexes").length());
  }

  /** Similar to {@link #testUpdateIndexConfig_huge(boolean)} */
  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testUpdateIndexConfigDecoupled_huge(boolean gzip) throws Exception {
    setUpAwsDecoupledSearchNodes(Cluster.getCluster(_cluster, List.of(_hardware)));

    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId();
    final String hostname = getDecoupledMongotHostname();
    final int numIndexes = 3000;
    final Map<String, Object> mongotRequest =
        Map.of("hostname", hostname, "indexes", generateIndexes(hostname, numIndexes));
    final Date t0 = new Date();
    final JSONObject response =
        new JSONObject(
            doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequest), gzip));
    final Date t1 = new Date();

    final long requestSeconds = Duration.between(t0.toInstant(), t1.toInstant()).toSeconds();
    assertTrue(requestSeconds < 5, "Request time too long: " + requestSeconds);
    LOG.info("Request time: " + requestSeconds);

    final String mongoDbUri = response.getString("mongoDbUri");
    List<String> mongodHostnames =
        _hardware.getHardware().stream()
            .map(InstanceHardware::getHostnameForAgents)
            .flatMap(Optional::stream)
            .sorted()
            .collect(Collectors.toList());
    assertTrue(mongoDbUri.contains(targetMongodHostname(mongodHostnames, hostname)));

    assertTrue(mongoDbUri.contains(String.valueOf(NDSDefaults.MONGOD_PUBLIC_PORT)));
    assertTrue(mongoDbUri.contains(AuthUser.MONGOT_USER));

    assertTrue(response.isNull("mongosUri"));

    assertTrue(response.has("indexes"));
    assertTrue(response.has("analyzers"));
    assertEquals(numIndexes, response.getJSONArray("indexes").length());
  }

  @Test
  public void testAddCrashReport() throws Exception {
    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId() + "/crash";

    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .build();
    _indexSvc.addFTSIndex(_group.getId(), _cluster.getName(), ftsIndex, null);

    final String hostname = _hardware.getHardware().get(0).getHostnameForAgents().get();
    final String message = "Search service has experienced an out of memory error";
    final String errorCodeOOM = IncidentErrorCodes.OUT_OF_MEMORY;

    final Map<String, Object> mongotRequestOOM =
        Map.of("hostname", hostname, "message", message, "errorCode", errorCodeOOM);

    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestOOM));

    final FTSIndexConfig config1 =
        _indexSvc.getFTSIndexConfig(_group.getId(), _cluster.getName()).orElse(null);
    // check first incident is added properly
    assertEquals(hostname, config1.getLastCrashReports().get(0).getHostname());
    assertEquals(errorCodeOOM, config1.getLastCrashReports().get(0).getErrorCode());
    assertEquals(
        message,
        config1
            .getLastCrashReports()
            .get(0)
            .getIncidents()
            .orElseThrow()
            .get(0)
            .get(FTSCrashReportIncident.FieldDefs.MESSAGE));

    // check that second incident with same hostname and errorCode is added to same crash report
    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestOOM));

    final FTSIndexConfig config2 =
        _indexSvc.getFTSIndexConfig(_group.getId(), _cluster.getName()).orElse(null);
    assertEquals(1, config2.getLastCrashReports().size());
    assertEquals(2, config2.getLastCrashReports().get(0).getIncidents().orElseThrow().size());

    // check that an incident with a different errorCode is stored as a new crash report
    final String errorCodeOther = "SOME_OTHER_ERROR";
    final String messageOther = "Search service has experienced some other error";

    final Map<String, Object> mongotRequestOther =
        Map.of("hostname", hostname, "message", messageOther, "errorCode", errorCodeOther);
    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestOther));
    final FTSIndexConfig config3 =
        _indexSvc.getFTSIndexConfig(_group.getId(), _cluster.getName()).orElse(null);
    assertEquals(2, config3.getLastCrashReports().size());
    assertEquals(hostname, config3.getLastCrashReports().get(1).getHostname());
    assertEquals(errorCodeOther, config3.getLastCrashReports().get(1).getErrorCode());
    assertEquals(
        messageOther,
        config3
            .getLastCrashReports()
            .get(1)
            .getIncidents()
            .orElseThrow()
            .get(0)
            .get(FTSCrashReportIncident.FieldDefs.MESSAGE));

    // add an incident that is more than 10 minutes old to the config
    final String messageOld = "this is an old message";
    assertEquals(
        1,
        _indexDao
            .setLastUpdateForAddCrashReport(
                _group.getId(),
                _cluster.getName(),
                hostname,
                errorCodeOOM,
                messageOld,
                DateUtils.addMinutes(new Date(), -11))
            .getModifiedCount());

    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestOOM));

    final FTSIndexConfig config4 =
        _indexSvc.getFTSIndexConfig(_group.getId(), _cluster.getName()).orElse(null);
    for (int i = 0; i < config4.getLastCrashReports().size(); i++) {
      for (int j = 0;
          j < config4.getLastCrashReports().get(i).getIncidents().orElseThrow().size();
          j++) {
        assertNotEquals(
            messageOld,
            config4
                .getLastCrashReports()
                .get(i)
                .getIncidents()
                .orElseThrow()
                .get(j)
                .get(FieldDefs.MESSAGE));
      }
    }
    assertTrue(
        CollectorRegistry.defaultRegistry.getSampleValue(
                "mms_nds_fts_crash_request_total",
                new String[] {"searchHostType", "sourceClusterType"},
                new String[] {"coupled", SourceClusterType.DEDICATED_REPLICA_SET.name()})
            >= 1.0);
  }

  @Test
  public void testAddCrashReportDecoupled() throws Exception {
    setUpAwsDecoupledSearchNodes(Cluster.getCluster(_cluster, List.of(_hardware)));

    final String path = "/conf/nds/fts/" + _ndsGroup.getGroupId() + "/crash";

    final ObjectId indexId = new ObjectId();
    final UUID uuid = UUID.randomUUID();
    final FTSSearchIndex ftsIndex =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName("foo")
            .setDatabase("test")
            .setLastObservedCollectionName("foo")
            .setCollectionUUID(uuid)
            .setMappings(new BasicDBObject("dynamic", true))
            .build();
    _indexSvc.addFTSIndex(_group.getId(), _cluster.getName(), ftsIndex, null);

    final String message = "Search service has experienced an out of memory error";
    final String errorCodeOOM = IncidentErrorCodes.OUT_OF_MEMORY;
    final String mongotHostName = getDecoupledMongotHostname();

    final Map<String, Object> mongotRequestOOM =
        Map.of("hostname", mongotHostName, "message", message, "errorCode", errorCodeOOM);

    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestOOM));

    final FTSIndexConfig config1 =
        _indexSvc.getFTSIndexConfig(_group.getId(), _cluster.getName()).orElseThrow();
    // check first incident is added properly
    assertEquals(mongotHostName, config1.getLastCrashReports().get(0).getHostname());
    assertEquals(errorCodeOOM, config1.getLastCrashReports().get(0).getErrorCode());
    assertEquals(
        message,
        config1
            .getLastCrashReports()
            .get(0)
            .getIncidents()
            .orElseThrow()
            .get(0)
            .get(FTSCrashReportIncident.FieldDefs.MESSAGE));

    // check that second incident with same hostname and errorCode is added to same crash report
    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestOOM));

    final FTSIndexConfig config2 =
        _indexSvc.getFTSIndexConfig(_group.getId(), _cluster.getName()).orElseThrow();
    assertEquals(1, config2.getLastCrashReports().size());
    assertEquals(2, config2.getLastCrashReports().get(0).getIncidents().orElseThrow().size());

    // check that an incident with a different errorCode is stored as a new crash report
    final String errorCodeOther = "SOME_OTHER_ERROR";
    final String messageOther = "Search service has experienced some other error";

    final Map<String, Object> mongotRequestOther =
        Map.of("hostname", mongotHostName, "message", messageOther, "errorCode", errorCodeOther);
    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestOther));
    final FTSIndexConfig config3 =
        _indexSvc.getFTSIndexConfig(_group.getId(), _cluster.getName()).orElseThrow();
    assertEquals(2, config3.getLastCrashReports().size());
    assertEquals(mongotHostName, config3.getLastCrashReports().get(1).getHostname());
    assertEquals(errorCodeOther, config3.getLastCrashReports().get(1).getErrorCode());
    assertEquals(
        messageOther,
        config3
            .getLastCrashReports()
            .get(1)
            .getIncidents()
            .orElseThrow()
            .get(0)
            .get(FTSCrashReportIncident.FieldDefs.MESSAGE));

    // add an incident that is more than 10 minutes old to the config
    final String messageOld = "this is an old message";
    assertEquals(
        1,
        _indexDao
            .setLastUpdateForAddCrashReport(
                _group.getId(),
                _cluster.getName(),
                mongotHostName,
                errorCodeOOM,
                messageOld,
                DateUtils.addMinutes(new Date(), -11))
            .getModifiedCount());

    doAgentApiCallPost(_group, _apiKey, path, new JSONObject(mongotRequestOOM));

    final FTSIndexConfig config4 =
        _indexSvc.getFTSIndexConfig(_group.getId(), _cluster.getName()).orElseThrow();
    IntStream.range(0, config4.getLastCrashReports().size())
        .forEach(
            i ->
                IntStream.range(
                        0, config4.getLastCrashReports().get(i).getIncidents().orElseThrow().size())
                    .forEach(
                        j ->
                            assertNotEquals(
                                messageOld,
                                config4
                                    .getLastCrashReports()
                                    .get(i)
                                    .getIncidents()
                                    .orElseThrow()
                                    .get(j)
                                    .get(FieldDefs.MESSAGE))));
    assertTrue(
        CollectorRegistry.defaultRegistry.getSampleValue(
                "mms_nds_fts_crash_request_total",
                new String[] {"searchHostType", "sourceClusterType"},
                new String[] {"decoupled", SourceClusterType.DEDICATED_REPLICA_SET.name()})
            >= 1.0);
  }
}
