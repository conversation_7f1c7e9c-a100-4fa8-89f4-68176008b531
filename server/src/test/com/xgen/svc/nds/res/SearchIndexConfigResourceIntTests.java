package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.fts._private.dao.FTSIndexConfigDao;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSIndex.Type;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostDetailedStatuses;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexGenerationDetail;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexHostDetailedStatuses;
import com.xgen.cloud.nds.fts._public.model.FTSSynonymMappingDefinition;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndexGenerationDetail;
import com.xgen.cloud.nds.fts._public.model.FTSVectorSearchIndexHostDetailedStatuses;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.search.api._public.view.api_2024_05_30.ApiAtlasSearchIndexResponseView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.common.MmsFactory;
import com.xgen.svc.core.JUnit5BaseResourceTest;
import com.xgen.svc.core.JsonAssertions;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFreeClusterDescriptionConfig;
import com.xgen.svc.nds.svc.FTSIndexConfigSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.guicetest.GuiceTestExtension;
import jakarta.inject.Inject;
import java.util.List;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(GuiceTestExtension.class)
public class SearchIndexConfigResourceIntTests extends JUnit5BaseResourceTest {
  private static final String CLUSTER_NAME = "ftsCluster";
  private static final String FREE_CLUSTER_NAME = "freeCluster";
  private static final String SERVERLESS_INSTANCE_NAME = "serverlessInstance";
  private static final String BASE_URL = "/nds/clusters/%s/%s/search/indexes";

  private static final String testDatabaseName = "testDb";
  private static final String testSearchAnalyzer = "testSearchAnalyzer";
  private static final String testAnalyzer = "testAnalyzer";
  private static final String testSynonymMappingName = "synonymMappingDefinitionName";
  private static final String testSynonymMappingAnalyzer = "synonymMappingDefinitionAnalyzer";
  private static final String testCollection = "testColl";
  private static final JSONArray TEST_ANALYZERS =
      new JSONArray()
          .put(
              new JSONObject()
                  .put("name", "foo")
                  .put("tokenizer", new JSONObject().put("type", "keyword")));
  private static final JSONObject TEST_MAPPINGS =
      new JSONObject()
          .put(
              "fields",
              new JSONObject()
                  .put("company", new JSONObject().put("type", "string"))
                  .put("service", new JSONObject().put("type", "text")))
          .put("dynamic", false);
  private static final JSONArray testSynonyms =
      new JSONArray()
          .put(
              new JSONObject()
                  .put(FTSSynonymMappingDefinition.FieldDefs.NAME, testSynonymMappingName)
                  .put(FTSSynonymMappingDefinition.FieldDefs.ANALYZER, testSynonymMappingAnalyzer)
                  .put(
                      FTSSynonymMappingDefinition.FieldDefs.SOURCE,
                      new JSONObject()
                          .put(FTSSynonymMappingDefinition.FieldDefs.COLLECTION, testCollection)));
  private static final List<String> testStoredSourceFields = List.of("x", "y", "z");
  private static final JSONObject testStoredSource =
      new JSONObject().put("include", new JSONArray(testStoredSourceFields));
  private static final JSONArray TEST_FIELDS =
      new JSONArray()
          .put(
              new JSONObject()
                  .put("type", "vector")
                  .put("path", "testPath")
                  .put("numDimensions", 1)
                  .put("similarity", "cosine"));

  @Inject private FTSIndexConfigSvc _ftsIndexConfigSvc;
  @Inject private FTSIndexConfigDao _ftsIndexConfigDao;
  @Inject ClusterDescriptionDao _clusterDescriptionDao;
  @Inject NDSGroupSvc _ndsGroupSvc;

  private Group _group;
  private AppUser _groupMemberReadOnly;
  private AppUser _groupSearchIndexEditor;
  private ObjectId _ndsGroupId;

  @BeforeEach
  @Override
  public void setUp() throws Exception {
    super.setUp();
    _group = MmsFactory.createGroupWithNDSPlan(CLUSTER_NAME);
    _groupMemberReadOnly =
        MmsFactory.createUserWithRoleInGroup(
            _group, "<EMAIL>", Role.GROUP_DATA_ACCESS_READ_ONLY);
    _groupSearchIndexEditor =
        MmsFactory.createUserWithRoleInGroup(
            _group, "<EMAIL>", Role.GROUP_SEARCH_INDEX_EDITOR);
    _ndsGroupId = _group.getId();
    _ndsGroupSvc.create(_ndsGroupId, new NDSManagedX509(), false);
    final BasicDBObject clusterDescription5_0_6 =
        NDSModelTestFactory.getAWSClusterDescription(_ndsGroupId, CLUSTER_NAME);
    clusterDescription5_0_6.append(ClusterDescription.FieldDefs.MONGODB_VERSION, "5.0.6");
    _clusterDescriptionDao.saveReplicaSafe(clusterDescription5_0_6);

    final BasicDBObject freeClusterDBObject =
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig()
                .setGroupId(_ndsGroupId)
                .setClusterName(FREE_CLUSTER_NAME));
    final ClusterDescription freeClusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(freeClusterDBObject);
    _clusterDescriptionDao.save(freeClusterDescription);

    final BasicDBObject serverlessClusterDBObject =
        NDSModelTestFactory.getDefaultServerlessClusterDescription(
            _ndsGroupId, SERVERLESS_INSTANCE_NAME);
    final ClusterDescription serverlessClusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(serverlessClusterDBObject);
    _clusterDescriptionDao.save(serverlessClusterDescription);
  }

  @Test
  public void testGetSearchIndex() throws SvcException {
    List<JSONObject> testIndexes =
        List.of(createSearchIndex("ftsIndex"), createVectorSearchIndex("vectorSearchIndex"));

    testGetSearchIndexAsUser(_groupMemberReadOnly, testIndexes);
    testGetSearchIndexAsUser(_groupSearchIndexEditor, testIndexes);
  }

  @Test
  public void testGetSearchIndexes() throws SvcException {
    List<JSONObject> testIndexes =
        List.of(
            createSearchIndex("ftsIndex1"),
            createSearchIndex("ftsIndex2"),
            createSearchIndex("ftsIndex3"),
            createVectorSearchIndex("vectorSearchIndex1"));

    testGetSearchIndexesAsUser(_groupMemberReadOnly, testIndexes);
    testGetSearchIndexesAsUser(_groupSearchIndexEditor, testIndexes);
  }

  private void testGetSearchIndexAsUser(AppUser pUser, List<JSONObject> indexDefinitions) {
    for (JSONObject indexDefinition : indexDefinitions) {
      final String indexId = indexDefinition.get("indexId").toString();
      JSONObject response =
          doAuthedJsonGet(
              pUser,
              String.format(BASE_URL + "/%s", _ndsGroupId, CLUSTER_NAME, indexId),
              HttpStatus.SC_OK);

      compareIndexWithResponse(
          indexDefinition, response, ApiAtlasSearchIndexResponseView.Status.BUILDING);
      JsonAssertions.assertJSONObjectEquals(
          indexDefinition.getJSONObject("definition"),
          response
              .getJSONArray("statusDetail")
              .getJSONObject(0)
              .getJSONObject("mainIndex")
              .getJSONObject("definition"));
    }
  }

  private void testGetSearchIndexesAsUser(AppUser pUser, List<JSONObject> indexDefinitions) {
    // get indexes
    final JSONArray getResp =
        doAuthedJsonArrayGet(
            pUser, String.format(BASE_URL, _ndsGroupId, CLUSTER_NAME), HttpStatus.SC_OK);

    assertEquals(indexDefinitions.size(), getResp.length());

    for (int i = 0; i < indexDefinitions.size(); i++) {
      final JSONObject indexDefinition = indexDefinitions.get(i);
      final JSONObject response = getResp.getJSONObject(i);
      compareIndexWithResponse(
          indexDefinition, response, ApiAtlasSearchIndexResponseView.Status.BUILDING);
      JsonAssertions.assertJSONObjectEquals(
          indexDefinition.getJSONObject("definition"),
          response
              .getJSONArray("statusDetail")
              .getJSONObject(0)
              .getJSONObject("mainIndex")
              .getJSONObject("definition"));
    }
  }

  private void compareIndexWithResponse(
      JSONObject definition, JSONObject response, ApiAtlasSearchIndexResponseView.Status status) {
    assertEquals(definition.get("indexId").toString(), response.getString("indexID"));
    assertEquals(definition.getString("database"), response.getString("database"));
    assertEquals(definition.getString("name"), response.getString("name"));
    assertEquals(definition.getString("collectionName"), response.getString("collectionName"));
    JsonAssertions.assertJSONObjectEquals(
        definition.getJSONObject("definition"), response.getJSONObject("latestDefinition"));
    assertEquals(status.toString(), response.getString("status"));
    assertFalse(response.getBoolean("queryable"));
    assertEquals(0L, response.getJSONObject("latestDefinitionVersion").getLong("version"));
  }

  private JSONObject createSearchIndex(final String indexName) throws SvcException {
    ObjectId indexId = new ObjectId();
    FTSSearchIndex index =
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName(indexName)
            .setDatabase(testDatabaseName)
            .setLastObservedCollectionName(testCollection)
            .setAnalyzer(testAnalyzer)
            .setAnalyzers(getDbObjectsFromJsonString(TEST_ANALYZERS.toString()))
            .setSearchAnalyzer(testSearchAnalyzer)
            .setMappings(BasicDBObject.parse(TEST_MAPPINGS.toString()))
            .setSynonyms(
                List.of(
                    new FTSSynonymMappingDefinition(
                        testSynonymMappingName, testSynonymMappingAnalyzer, testCollection)))
            .setStoredSource(FTSSearchIndex.StoredSource.include(testStoredSourceFields))
            .build();

    _ftsIndexConfigSvc.addFTSIndex(_ndsGroupId, CLUSTER_NAME, index, null);
    var statuses =
        getHostDetailedStatuses(
            _ftsIndexConfigSvc.getFTSIndex(_ndsGroupId, CLUSTER_NAME, indexId).orElseThrow(),
            null,
            FTSIndexHostStat.StatusCode.INITIAL_SYNC,
            null,
            FTSIndex.Type.SEARCH);
    _ftsIndexConfigDao.setDetailedStatusesDefinition(
        _ndsGroupId, CLUSTER_NAME, indexId, "hostname", statuses.toDBObject());

    return new JSONObject()
        .put("indexId", indexId)
        .put("name", indexName)
        .put("database", testDatabaseName)
        .put("collectionName", testCollection)
        .put(
            "definition",
            new JSONObject()
                .put("analyzer", testAnalyzer)
                .put("analyzers", TEST_ANALYZERS)
                .put("searchAnalyzer", testSearchAnalyzer)
                .put("mappings", TEST_MAPPINGS)
                .put(FTSSearchIndex.FieldDefs.SYNONYMS, testSynonyms)
                .put(FTSSearchIndex.FieldDefs.STORED_SOURCE, testStoredSource));
  }

  private JSONObject createVectorSearchIndex(final String indexName) throws SvcException {
    ObjectId indexId = new ObjectId();
    FTSVectorSearchIndex index =
        new FTSVectorSearchIndex.Builder()
            .setIndexId(indexId)
            .setName(indexName)
            .setDatabase(testDatabaseName)
            .setLastObservedCollectionName(testCollection)
            .setFields(getDbObjectsFromJsonString(TEST_FIELDS.toString()))
            .build();
    _ftsIndexConfigSvc.addFTSIndex(_ndsGroupId, CLUSTER_NAME, index, null);
    var statuses =
        getHostDetailedStatuses(
            _ftsIndexConfigSvc.getFTSIndex(_ndsGroupId, CLUSTER_NAME, indexId).orElseThrow(),
            null,
            FTSIndexHostStat.StatusCode.INITIAL_SYNC,
            null,
            Type.VECTOR_SEARCH);
    _ftsIndexConfigDao.setDetailedStatusesDefinition(
        _ndsGroupId, CLUSTER_NAME, indexId, "hostname", statuses.toDBObject());

    return new JSONObject()
        .put("indexId", indexId)
        .put("name", indexName)
        .put("database", testDatabaseName)
        .put("collectionName", testCollection)
        .put("type", "vectorSearch")
        .put(
            "definition", new JSONObject().put(FTSVectorSearchIndex.FieldDefs.FIELDS, TEST_FIELDS));
  }

  private FTSIndexHostDetailedStatuses getHostDetailedStatuses(
      FTSIndex pMainIndex,
      FTSIndex pStagedIndex,
      FTSIndexHostStat.StatusCode mainStatus,
      FTSIndexHostStat.StatusCode stagedStatus,
      FTSIndex.Type pType) {

    final FTSIndex.Type indexType = pType != null ? pType : FTSIndex.Type.SEARCH;
    switch (indexType) {
      case SEARCH:
        final var searchBuilder = FTSSearchIndexHostDetailedStatuses.builder();
        if (pMainIndex != null) {
          searchBuilder.mainIndex(
              new FTSSearchIndexGenerationDetail.Builder()
                  .definition(pMainIndex.toTextSearchIndex())
                  .status(mainStatus)
                  .build());
        }
        if (pStagedIndex != null) {
          searchBuilder.stagedIndex(
              new FTSSearchIndexGenerationDetail.Builder()
                  .definition(pStagedIndex.toTextSearchIndex())
                  .status(stagedStatus)
                  .build());
        }
        return searchBuilder.build();
      case VECTOR_SEARCH:
        final var vectorBuilder = FTSVectorSearchIndexHostDetailedStatuses.builder();
        if (pMainIndex != null) {
          vectorBuilder.mainIndex(
              new FTSVectorSearchIndexGenerationDetail.Builder()
                  .definition(pMainIndex.toVectorSearchIndex())
                  .status(mainStatus)
                  .build());
        }
        if (pStagedIndex != null) {
          vectorBuilder.stagedIndex(
              new FTSVectorSearchIndexGenerationDetail.Builder()
                  .definition(pStagedIndex.toVectorSearchIndex())
                  .status(stagedStatus)
                  .build());
        }
        return vectorBuilder.build();
      default:
        throw new IllegalArgumentException("Unknown index type: " + pType);
    }
  }
}
