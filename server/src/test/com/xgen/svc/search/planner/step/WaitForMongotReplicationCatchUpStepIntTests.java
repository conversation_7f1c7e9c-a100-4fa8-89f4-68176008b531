package com.xgen.svc.search.planner.step;

import static com.xgen.cloud.search.decoupled.blobstore._public.svc.BlobstoreFeatureSvc.BlobstoreAccess.DEFAULT_DISABLED_BLOBSTORE_ACCESS;
import static com.xgen.module.common.planner.model.Result.Status.DONE;
import static com.xgen.module.common.planner.model.Result.Status.IN_PROGRESS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareModelTestFactory;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.fts._private.dao.FTSIndexConfigStatsDao;
import com.xgen.cloud.nds.fts._public.model.FTSIndexConfigStats;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostDetailedStatuses;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat;
import com.xgen.cloud.nds.fts._public.model.FTSIndexHostStat.StatusCode;
import com.xgen.cloud.nds.fts._public.model.FTSIndexStatusMap;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexConfigStats;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexGenerationDetail;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexHostDetailedStatuses;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndexHostStat;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.search.common._public.util.HostnameUtil;
import com.xgen.cloud.search.decoupled.blobstore._public.svc.BlobstoreFeatureSvc;
import com.xgen.cloud.search.decoupled.blobstore._public.svc.BlobstoreFeatureSvc.BlobstoreAccess;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroup;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.PartitionGroupSyncSource;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchInstance;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchInstance.State;
import com.xgen.cloud.search.decoupled.cloudprovider._public.model.SearchPhysicalModelTestFactory;
import com.xgen.cloud.search.decoupled.config._public.model.Migration;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription;
import com.xgen.cloud.search.decoupled.config._public.model.aws.AWSSearchInstanceSize;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.cloud.search.settings._public.settings.SearchAppSettings;
import com.xgen.cloud.search.settings._public.settings.SearchAppSettings.Timeout;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.FTSIndexConfigSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import jakarta.inject.Inject;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import org.bson.BsonTimestamp;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

public class WaitForMongotReplicationCatchUpStepIntTests extends SearchStepBaseTest {
  private static final String HOSTNAME_ALPHA =
      "waitformongotreplicationcatchupsteptests-shard-00-search-1.grp123.mongodb.net";
  private static final String HOSTNAME_BETA =
      "waitformongotreplicationcatchupsteptests-shard-00-search-2.grp123.mongodb.net";
  private static final String PROVISIONING_HOSTNAME =
      "waitformongotreplicationcatchupsteptests-shard-00-search-3.grp123.mongodb.net";
  private static final String DATABASE_HOST =
      "waitformongotreplicationcatchupsteptests-shard-00-00.grp123.mongodb.net";

  private static final String CLUSTER_NAME = "test-cluster";

  private static final ObjectId DEPLOYMENT_ID = new ObjectId();
  private @Inject ClusterDescriptionDao clusterDescriptionDao;
  private @Inject FTSIndexConfigSvc ftsIndexConfigSvc;
  private @Inject FTSIndexConfigStatsDao ftsIndexConfigStatsDao;
  private @Inject NDSClusterSvc ndsClusterSvc;
  private final BlobstoreFeatureSvc blobstoreFeatureSvc = mock(BlobstoreFeatureSvc.class);
  private final SearchDeploymentDescriptionSvc searchDeploymentDescriptionSvc =
      mock(SearchDeploymentDescriptionSvc.class);
  private final ReplicaSetHardwareSvc replicaSetHardwareSvc = mock(ReplicaSetHardwareSvc.class);
  private final SearchAppSettings appSettings = mock(SearchAppSettings.class);
  private final Clock clock =
      Clock.fixed(Instant.parse("2020-01-01T00:00:00.000Z"), ZoneId.of("UTC"));
  private final SearchDeploymentDescription searchDeploymentDescription =
      mock(SearchDeploymentDescription.class);
  private final FeatureFlagSvc featureFlagSvc = mock(FeatureFlagSvc.class);
  private PartitionGroup partitionGroup;
  private SearchInstance provisioningHost;

  @Override
  @BeforeEach
  public void setUp() throws Exception {
    super.setUp();
    final ClusterDescription clusterDescription = newClusterDescription().build();
    ObjectId clusterUniqueId = clusterDescription.getUniqueId();

    var createInstant = clock.instant();
    var createDate = new Date(createInstant.toEpochMilli());
    final String rsId = "rs-1";

    var replicaSet =
        ReplicaSetHardware.builder()
            .containsShardData(true)
            .rsId(rsId)
            .containsSearchShardDataSinceDate(createDate)
            .createDate(createDate)
            .hardware(
                List.of(
                    InstanceHardware.getHardware(
                        InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull(
                            DATABASE_HOST, new ObjectId(), 0))))
            .build();
    when(replicaSetHardwareSvc.getReplicaSetHardware(eq(this.group.getId()), eq(CLUSTER_NAME)))
        .thenReturn(List.of(replicaSet));

    var syncSource =
        newPartitionGroupSyncSource()
            .withClusterUniqueId(clusterUniqueId)
            .withReplicaSetName(rsId)
            .withCreateDate(createInstant)
            .build();

    SearchInstance hostAlpha =
        newSearchInstance().withState(State.RUNNING).withHostname(HOSTNAME_ALPHA).build();
    SearchInstance hostBeta =
        newSearchInstance().withState(State.RUNNING).withHostname(HOSTNAME_BETA).build();
    this.provisioningHost =
        newSearchInstance().withState(State.STARTING).withHostname(PROVISIONING_HOSTNAME).build();

    this.partitionGroup =
        newPartitionGroup()
            .withInstances(List.of(hostAlpha, hostBeta, provisioningHost))
            .withSyncSource(syncSource)
            .build();
    when(this.searchDeploymentDescriptionSvc.getDeploymentById(DEPLOYMENT_ID))
        .thenReturn(this.searchDeploymentDescription);

    when(this.appSettings.getAtlasSearchPlannerReplicationDeltaToleranceSeconds()).thenReturn(120);
    when(this.appSettings.getAtlasSearchPlannerMaxReplicationStatusAge())
        .thenReturn(Duration.ofMinutes(5));
    when(this.appSettings.getAtlasSearchPlannerReplicationCatchUpStepTimeout())
        .thenReturn(Timeout.of(Duration.ofHours(2)));

    when(this.featureFlagSvc.isFeatureFlagEnabled(
            eq(FeatureFlag.ATLAS_SEARCH_DISABLE_PLANNER_REPLICATION_LAG_CATCH_UP_TIMEOUT),
            isNull(),
            any()))
        .thenReturn(true);
    when(this.blobstoreFeatureSvc.getBlobstoreAccessState(any(), any(), any(), any()))
        .thenReturn(DEFAULT_DISABLED_BLOBSTORE_ACCESS);
  }

  public enum Expectation {
    READY,
    NOT_READY,
  }

  public record SimpleTestCase(
      StatusCode provisioningStatus, StatusCode runningStatus, Expectation expectation) {}

  static List<SimpleTestCase> simpleTestCases() {
    // We will do (STEADY, STEADY) tests separately, so it's not included below.
    //
    // If the provisioning host index status is STEADY, then as long as the running host index
    // status is not NOT_STARTED, we can mark the host as READY.
    var cases =
        new ArrayList<>(
            List.of(
                new SimpleTestCase(StatusCode.STEADY, StatusCode.RECOVERING, Expectation.READY),
                new SimpleTestCase(StatusCode.STEADY, StatusCode.STALE, Expectation.READY),
                new SimpleTestCase(StatusCode.STEADY, StatusCode.INITIAL_SYNC, Expectation.READY),
                new SimpleTestCase(StatusCode.STEADY, StatusCode.DOES_NOT_EXIST, Expectation.READY),
                new SimpleTestCase(StatusCode.STEADY, StatusCode.FAILED, Expectation.READY)));
    // If the provisioning host index status is STEADY and the running host index status is
    // NOT_STARTED, we should consider this host as NOT READY yet.
    cases.add(new SimpleTestCase(StatusCode.STEADY, StatusCode.NOT_STARTED, Expectation.NOT_READY));
    // If the index on the provisioning host has these statuses, it is considered not ready.
    for (var provisioningStatus :
        List.of(
            StatusCode.RECOVERING,
            StatusCode.STALE,
            StatusCode.INITIAL_SYNC,
            StatusCode.NOT_STARTED,
            StatusCode.FAILED)) {
      for (var runningStatus : StatusCode.values()) {
        cases.add(new SimpleTestCase(provisioningStatus, runningStatus, Expectation.NOT_READY));
      }
    }
    // If the provisioning host index status is DOES_NOT_EXIST, it is considered READY regardless of
    // what the index status is on the running host.
    for (var runningStatus : StatusCode.values()) {
      cases.add(new SimpleTestCase(StatusCode.DOES_NOT_EXIST, runningStatus, Expectation.READY));
    }
    return cases;
  }

  @ParameterizedTest
  @MethodSource("simpleTestCases")
  public void testSimple(SimpleTestCase testCase) throws Exception {
    Instant instant = clock.instant();

    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant)
            .withMainIndexStatus(testCase.runningStatus)
            .build());

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant)
            .withMainIndexStatus(testCase.provisioningStatus)
            .build());

    var outcome = createStep(clock).perform().getStatus();
    if (testCase.expectation == Expectation.READY) {
      assertEquals(DONE, outcome);
    } else {
      assertEquals(IN_PROGRESS, outcome);
    }
  }

  @Test
  public void testInProgressStepStatsNotReported() throws Exception {
    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);
    ftsIndexConfigStatsDao.create(new IndexHostStatsBuilder(HOSTNAME_BETA, indexId).build());

    // The provisioning host has not reported a status for the index.
    assertEquals(IN_PROGRESS, createStep(clock).perform().getStatus());
  }

  @Test
  public void testInProgressStep_StatsPartiallyReportedOnNewHost() throws Exception {
    Instant instant = clock.instant();

    ObjectId indexId1 = new ObjectId();
    ObjectId indexId2 = new ObjectId();
    addIndexToCatalog(indexId1);
    addIndexToCatalog(indexId2);
    // RUNNING hosts have stats for all indexes
    for (var host : List.of(HOSTNAME_ALPHA, HOSTNAME_BETA)) {
      for (var index : List.of(indexId1, indexId2)) {
        ftsIndexConfigStatsDao.create(
            new IndexHostStatsBuilder(host, index)
                .withMainIndexStatus(StatusCode.STEADY)
                .withOptime(instant)
                .build());
      }
    }
    // The provisioning host has only reported one of the indexes.
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId1)
            .withMainIndexStatus(StatusCode.STEADY)
            .withOptime(instant)
            .build());

    assertEquals(IN_PROGRESS, createStep(clock).perform().getStatus());
  }

  @Test
  public void testInProgressStep_StatsPartiallyReportedOnRunningHosts() throws Exception {
    Instant instant = clock.instant();

    ObjectId indexId1 = new ObjectId();
    ObjectId indexId2 = new ObjectId();
    addIndexToCatalog(indexId1);
    addIndexToCatalog(indexId2);
    // RUNNING hosts only reported stats for indexId1
    for (var host : List.of(HOSTNAME_ALPHA, HOSTNAME_BETA)) {
      ftsIndexConfigStatsDao.create(
          new IndexHostStatsBuilder(host, indexId1)
              .withMainIndexStatus(StatusCode.STEADY)
              .withOptime(instant)
              .build());
    }
    // The provisioning host reported stats for all indexes
    for (var index : List.of(indexId1, indexId2)) {
      ftsIndexConfigStatsDao.create(
          new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, index)
              .withMainIndexStatus(StatusCode.STEADY)
              .withOptime(instant)
              .build());
    }

    assertEquals(IN_PROGRESS, createStep(clock).perform().getStatus());
  }

  @Test
  public void testInProgressStepStatsReplicationLagTooHigh() throws Exception {
    Instant instant = clock.instant();

    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    // RUNNING hosts have a 0 replication lag.
    for (var host : List.of(HOSTNAME_ALPHA, HOSTNAME_BETA)) {
      ftsIndexConfigStatsDao.create(
          new IndexHostStatsBuilder(host, indexId)
              .withMainIndexStatus(StatusCode.STEADY)
              .withOptime(instant)
              .build());
    }

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            // The optime is an hour behind.
            .withOptime(instant.minus(1, ChronoUnit.HOURS))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    assertEquals(IN_PROGRESS, createStep(clock).perform().getStatus());
  }

  @Test
  public void testDifferentLagOnRunningHosts() throws Exception {
    Instant instant = clock.instant();
    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    // Host A has an optime 1 hour in the past
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant.minus(1, ChronoUnit.HOURS))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    // Provisioning host has an optime 1 minute in the past (below the threshold).
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant.minus(1, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    // Host B has an optime at instant()
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_BETA, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    // Host B should take precedence and the mark the new host as READY
    assertEquals(DONE, createStep(clock).perform().getStatus());
  }

  @Test
  public void testDifferentLagForMultipleIndexesOnRunningHosts() throws Exception {
    Instant instant = clock.instant();

    ObjectId indexId1 = new ObjectId();
    ObjectId indexId2 = new ObjectId();
    addIndexToCatalog(indexId1);
    addIndexToCatalog(indexId2);

    // Host A has Index 1 at an optime 1 hour in the past
    // Host B has Index 2 at an optime 1 hour in the past
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId1)
            .withOptime(instant.minus(1, ChronoUnit.HOURS))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_BETA, indexId2)
            .withOptime(instant.minus(1, ChronoUnit.HOURS))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    // Both indexes on the provisioning host are only behind 1 minute
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId1)
            .withOptime(instant.minus(1, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId2)
            .withOptime(instant.minus(1, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    // Host A has an optime of instant for Index 2
    // Host B has an optime of instant for Index 1
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId2)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_BETA, indexId1)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    // For the provisioning host, both indexes are almost fully caught up.
    assertEquals(DONE, createStep(clock).perform().getStatus());
  }

  @Test
  public void testHighReplicationLagOnRunningHosts() throws Exception {
    Instant instant = clock.instant();
    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);
    // Host A is 6 hours behind instant()
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant.minus(6, ChronoUnit.HOURS))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    // Provisioning host is 5 hours 1 minute behind instant()
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant.minus(5, ChronoUnit.HOURS).minus(1, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    // Host B is 5 hours behind instant()
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_BETA, indexId)
            .withOptime(instant.minus(5, ChronoUnit.HOURS))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    assertEquals(DONE, createStep(clock).perform().getStatus());
  }

  public record HostIndexStatus(StatusCode mainStatus, Optional<StatusCode> stagedStatus) {}

  public record StagedIndexTestCase(
      HostIndexStatus runningStatus, HostIndexStatus provisioningStatus, Expectation expectation) {}

  static List<StagedIndexTestCase> stagedIndexTestCases() {
    List<StagedIndexTestCase> testCases = new ArrayList<>();
    var codes = StatusCode.values();
    for (int i = 0; i < codes.length; i++) {
      // Calculate a few different combinations of statuses.
      int j = codes.length - i - 1;
      // Test cases where there is a staged index on PROVISIONING nodes.
      testCases.add(
          new StagedIndexTestCase(
              new HostIndexStatus(codes[i], Optional.empty()),
              new HostIndexStatus(codes[j], Optional.of(codes[i])),
              // A staged index on PROVISIONING host is always considered not ready.
              Expectation.NOT_READY));
      // Test cases where there is a staged index on RUNNING nodes.
      testCases.add(
          new StagedIndexTestCase(
              new HostIndexStatus(codes[i], Optional.of(codes[j])),
              new HostIndexStatus(codes[j], Optional.empty()),
              // If there is a staged index on the RUNNING host, we compare the optime of the
              // index on the PROVISIONING host against now(). So, it will be considered ready if
              // it is in one of the valid index states.
              (codes[j] == StatusCode.STEADY || codes[j] == StatusCode.DOES_NOT_EXIST)
                  ? Expectation.READY
                  : Expectation.NOT_READY));
    }
    return testCases;
  }

  /**
   * Verifies that if there is a staged index on either the RUNNING or the PROVISIONING host, the
   * PROVISIONING host is not marked as READY.
   */
  @ParameterizedTest
  @MethodSource("stagedIndexTestCases")
  public void testWithStagedIndexes(StagedIndexTestCase test) throws Exception {
    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    var runningBuilder =
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(clock.instant())
            .withMainIndexStatus(test.runningStatus.mainStatus);
    test.runningStatus.stagedStatus.ifPresent(runningBuilder::withStagedIndexStatus);
    ftsIndexConfigStatsDao.create(runningBuilder.build());

    var provisioningBuilder =
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(clock.instant())
            .withMainIndexStatus(test.provisioningStatus.mainStatus);
    test.provisioningStatus.stagedStatus.ifPresent(provisioningBuilder::withStagedIndexStatus);
    ftsIndexConfigStatsDao.create(provisioningBuilder.build());

    if (test.expectation == Expectation.READY) {
      assertEquals(DONE, createStep(clock).perform().getStatus());
    } else {
      assertEquals(IN_PROGRESS, createStep(clock).perform().getStatus());
    }
  }

  @Test
  public void testOutDatedStatsOnProvisioningHost() throws Exception {
    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    Instant instant = clock.instant();

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .lastUpdated(instant)
            .build());

    // The data was last updated 1 hour ago (staleness threshold is 5m).
    // So, even though the optime is the same, it will be marked as IN_PROGRESS.
    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .lastUpdated(instant.minus(1, ChronoUnit.HOURS))
            .build());

    // Since PROVISIONING host doesn't have a fresh stat, we don't mark the node as READY.
    assertEquals(IN_PROGRESS, createStep(clock).perform().getStatus());
  }

  @Test
  public void testOutDatedStatsOnRunningHosts() throws Exception {
    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    Instant instant = clock.instant();

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            // Running host stats were last updated an hour ago.
            .lastUpdated(instant.minus(1, ChronoUnit.HOURS))
            .build());

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .lastUpdated(instant)
            .build());
    // Since RUNNING host doesn't have a fresh stat, we don't mark the node as READY.
    assertEquals(IN_PROGRESS, createStep(clock).perform().getStatus());
  }

  @Test
  public void testSuccessfulStepMigrationFromCoupled() throws Exception {
    Instant instant = clock.instant();

    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    setupMigration();
    addIndexStatsToMigration(indexId, PROVISIONING_HOSTNAME, instant, instant);

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(DATABASE_HOST, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    assertEquals(DONE, createStep(clock).perform().getStatus());
  }

  @Test
  public void testInProgressStepMigrationFromCoupledReplicationLagTooHigh() throws Exception {
    Instant instant = clock.instant();

    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    setupMigration();
    // The provisioning host is 1 hour behind the data from the coupled search instance.
    addIndexStatsToMigration(
        indexId, PROVISIONING_HOSTNAME, instant.minus(Duration.ofHours(1)), instant);

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(DATABASE_HOST, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    assertEquals(IN_PROGRESS, createStep(clock).perform().getStatus());
  }

  @Test
  public void testNoRunningHosts_lowReplicationLag() throws Exception {
    final ClusterDescription clusterDescription = newClusterDescription().build();
    ObjectId clusterUniqueId = clusterDescription.getUniqueId();

    var createInstant = clock.instant();
    var createDate = new Date(createInstant.toEpochMilli());
    final String rsId = "rs-1";

    var replicaSet =
        ReplicaSetHardware.builder()
            .containsShardData(true)
            .rsId(rsId)
            .containsSearchShardDataSinceDate(createDate)
            .createDate(createDate)
            .hardware(
                List.of(
                    InstanceHardware.getHardware(
                        InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull(
                            DATABASE_HOST, new ObjectId(), 0))))
            .build();
    when(replicaSetHardwareSvc.getReplicaSetHardware(eq(this.group.getId()), eq(CLUSTER_NAME)))
        .thenReturn(List.of(replicaSet));

    var syncSource =
        newPartitionGroupSyncSource()
            .withClusterUniqueId(clusterUniqueId)
            .withReplicaSetName(rsId)
            .withCreateDate(createInstant)
            .build();

    var host3 =
        newSearchInstance().withState(State.STARTING).withHostname(PROVISIONING_HOSTNAME).build();

    this.partitionGroup =
        newPartitionGroup().withInstances(List.of(host3)).withSyncSource(syncSource).build();
    when(this.searchDeploymentDescriptionSvc.getDeploymentById(DEPLOYMENT_ID))
        .thenReturn(this.searchDeploymentDescription);

    var indexId = new ObjectId();
    addIndexToCatalog(indexId);

    // Insert index stats for the provisioning host with an optime close to now.
    this.ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(clock.instant().minus(1, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .lastUpdated(clock.instant())
            .build());

    assertEquals(DONE, createStep(clock).perform().getStatus());
  }

  @Test
  public void testNoRunningHosts_highReplicationLag() throws Exception {
    final ClusterDescription clusterDescription = newClusterDescription().build();
    ObjectId clusterUniqueId = clusterDescription.getUniqueId();

    var createInstant = clock.instant();
    var createDate = new Date(createInstant.toEpochMilli());
    final String rsId = "rs-1";

    var replicaSet =
        ReplicaSetHardware.builder()
            .containsShardData(true)
            .rsId(rsId)
            .containsSearchShardDataSinceDate(createDate)
            .createDate(createDate)
            .hardware(
                List.of(
                    InstanceHardware.getHardware(
                        InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull(
                            DATABASE_HOST, new ObjectId(), 0))))
            .build();
    when(replicaSetHardwareSvc.getReplicaSetHardware(eq(this.group.getId()), eq(CLUSTER_NAME)))
        .thenReturn(List.of(replicaSet));

    var syncSource =
        newPartitionGroupSyncSource()
            .withClusterUniqueId(clusterUniqueId)
            .withReplicaSetName(rsId)
            .withCreateDate(createInstant)
            .build();

    var host3 =
        newSearchInstance().withState(State.STARTING).withHostname(PROVISIONING_HOSTNAME).build();

    this.partitionGroup =
        newPartitionGroup().withInstances(List.of(host3)).withSyncSource(syncSource).build();
    when(this.searchDeploymentDescriptionSvc.getDeploymentById(DEPLOYMENT_ID))
        .thenReturn(this.searchDeploymentDescription);

    var indexId = new ObjectId();
    addIndexToCatalog(indexId);

    // Insert index stats for the provisioning host with an optime well in the past compared to now.
    this.ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(clock.instant().minus(1, ChronoUnit.HOURS))
            .withMainIndexStatus(StatusCode.STEADY)
            .lastUpdated(clock.instant())
            .build());

    assertEquals(IN_PROGRESS, createStep(clock).perform().getStatus());
  }

  @Test
  public void testTimeoutFeatureFlagEnabled_blobstoreDisabled_shouldBeDone() throws Exception {
    // Enable the feature flag for this group.
    when(this.featureFlagSvc.isFeatureFlagEnabled(
            eq(FeatureFlag.ATLAS_SEARCH_DISABLE_PLANNER_REPLICATION_LAG_CATCH_UP_TIMEOUT),
            isNull(),
            any()))
        .thenReturn(true);
    // Set a timeout of 1 hour.
    when(this.appSettings.getAtlasSearchPlannerReplicationCatchUpStepTimeout())
        .thenReturn(Timeout.ofSeconds(3600));
    // Disable FCIS
    when(this.blobstoreFeatureSvc.getBlobstoreAccessState(any(), any(), any(), any()))
        .thenReturn(DEFAULT_DISABLED_BLOBSTORE_ACCESS);

    Instant instant = clock.instant();

    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant.minus(10, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    var startTime = clock.instant().minus(2, ChronoUnit.HOURS);
    assertEquals(DONE, createStep(startTime, clock).perform().getStatus());
  }

  @Test
  public void testTimeoutFeatureFlagDisabled_blobstoreDownloadEnabled_shouldWait()
      throws Exception {
    // Disable the feature flag for this group.
    when(this.featureFlagSvc.isFeatureFlagEnabled(
            eq(FeatureFlag.ATLAS_SEARCH_DISABLE_PLANNER_REPLICATION_LAG_CATCH_UP_TIMEOUT),
            isNull(),
            any()))
        .thenReturn(false);
    // Enabled only FCIS downloads
    when(this.blobstoreFeatureSvc.getBlobstoreAccessState(any(), any(), any(), any()))
        .thenReturn(new BlobstoreAccess(false, false, true));
    // Set a timeout of 1 day for FCIS download enabled clusters.
    when(this.appSettings.getAtlasSearchPlannerReplicationCatchUpStepTimeoutForFCIS())
        .thenReturn(Timeout.of(Duration.ofDays(1)));

    Instant instant = clock.instant();

    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant.minus(10, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    var startTime = clock.instant().minus(2, ChronoUnit.DAYS);
    assertEquals(IN_PROGRESS, createStep(startTime, clock).perform().getStatus());
  }

  @Test
  public void testTimeoutFeatureFlagDisabled_blobstoreUploadEnabled_shouldBeDone()
      throws Exception {
    // Disable the feature flag for this group.
    when(this.featureFlagSvc.isFeatureFlagEnabled(
            eq(FeatureFlag.ATLAS_SEARCH_DISABLE_PLANNER_REPLICATION_LAG_CATCH_UP_TIMEOUT),
            isNull(),
            any()))
        .thenReturn(false);
    // Enable FCIS uploads only
    when(this.blobstoreFeatureSvc.getBlobstoreAccessState(any(), any(), any(), any()))
        .thenReturn(new BlobstoreAccess(false, true, false));
    // Set a timeout of 1 day for FCIS download enabled clusters.
    when(this.appSettings.getAtlasSearchPlannerReplicationCatchUpStepTimeoutForFCIS())
        .thenReturn(Timeout.of(Duration.ofDays(1)));

    Instant instant = clock.instant();

    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant.minus(10, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    var startTime = clock.instant().minus(2, ChronoUnit.DAYS);
    assertEquals(DONE, createStep(startTime, clock).perform().getStatus());
  }

  @Test
  public void testTimeoutFeatureFlagDisabled_blobstorePrivatePreview_shouldWait() throws Exception {
    // Disable the feature flag for this group.
    when(this.featureFlagSvc.isFeatureFlagEnabled(
            eq(FeatureFlag.ATLAS_SEARCH_DISABLE_PLANNER_REPLICATION_LAG_CATCH_UP_TIMEOUT),
            isNull(),
            any()))
        .thenReturn(false);
    // Enable FCIS private preview (definitionally enables downloads even if the flag isn't set).
    when(this.blobstoreFeatureSvc.getBlobstoreAccessState(any(), any(), any(), any()))
        .thenReturn(new BlobstoreAccess(true, false, false));
    // Set a timeout of 1 day for FCIS download enabled clusters.
    when(this.appSettings.getAtlasSearchPlannerReplicationCatchUpStepTimeoutForFCIS())
        .thenReturn(Timeout.of(Duration.ofDays(1)));

    Instant instant = clock.instant();

    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant.minus(10, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    var startTime = clock.instant().minus(2, ChronoUnit.HOURS);
    assertEquals(IN_PROGRESS, createStep(startTime, clock).perform().getStatus());
  }

  @Test
  public void testTimeoutFeatureFlagEnabled_blobstoreDownloadEnabled_shouldBeDone()
      throws Exception {
    // Enable the feature flag for this group.
    when(this.featureFlagSvc.isFeatureFlagEnabled(
            eq(FeatureFlag.ATLAS_SEARCH_DISABLE_PLANNER_REPLICATION_LAG_CATCH_UP_TIMEOUT),
            isNull(),
            any()))
        .thenReturn(true);
    // Enable FCIS downloads
    when(this.blobstoreFeatureSvc.getBlobstoreAccessState(any(), any(), any(), any()))
        .thenReturn(new BlobstoreAccess(false, false, true));
    // Set a timeout of 1 day for FCIS clusters.
    when(this.appSettings.getAtlasSearchPlannerReplicationCatchUpStepTimeoutForFCIS())
        .thenReturn(Timeout.of(Duration.ofDays(1)));

    Instant instant = clock.instant();

    ObjectId indexId = new ObjectId();
    addIndexToCatalog(indexId);

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(HOSTNAME_ALPHA, indexId)
            .withOptime(instant)
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    ftsIndexConfigStatsDao.create(
        new IndexHostStatsBuilder(PROVISIONING_HOSTNAME, indexId)
            .withOptime(instant.minus(10, ChronoUnit.MINUTES))
            .withMainIndexStatus(StatusCode.STEADY)
            .build());

    var startTime = clock.instant().minus(2, ChronoUnit.DAYS);
    assertEquals(DONE, createStep(startTime, clock).perform().getStatus());
  }

  private void addIndexToCatalog(ObjectId indexId) throws SvcException {
    this.ftsIndexConfigSvc.addFTSIndex(
        this.group.getId(),
        CLUSTER_NAME,
        new FTSSearchIndex.Builder()
            .setIndexId(indexId)
            .setName(indexId.toHexString())
            .setDatabase("database")
            .setLastObservedCollectionName("collection")
            .setMappings(new BasicDBObject("dynamic", true))
            .build(),
        null);
  }

  private class IndexHostStatsBuilder {
    private final ObjectId indexId;
    private final String hostname;
    private BsonTimestamp optime = new BsonTimestamp(0, 0);
    private StatusCode mainStatus = StatusCode.NOT_STARTED;
    private Optional<StatusCode> stagedStatus = Optional.empty();
    private Instant lastUpdated = clock.instant();

    public IndexHostStatsBuilder(String hostname, ObjectId indexId) {
      this.hostname = hostname;
      this.indexId = indexId;
    }

    public IndexHostStatsBuilder withOptime(Instant optime) {
      this.optime = new BsonTimestamp((int) optime.getEpochSecond(), optime.getNano());
      return this;
    }

    public IndexHostStatsBuilder withMainIndexStatus(StatusCode status) {
      this.mainStatus = status;
      return this;
    }

    public IndexHostStatsBuilder withStagedIndexStatus(StatusCode status) {
      this.stagedStatus = Optional.of(status);
      return this;
    }

    public IndexHostStatsBuilder lastUpdated(Instant updated) {
      this.lastUpdated = updated;
      return this;
    }

    public FTSSearchIndexConfigStats build() {
      var id =
          new FTSIndexConfigStats.Id(
              group.getId(),
              CLUSTER_NAME,
              indexId,
              HostnameUtil.getHostnameWithoutDomain(hostname));
      var indexHostStat =
          FTSSearchIndexHostStat.builder()
              .statusCode(this.mainStatus)
              .optime(optime.getValue())
              .lastUpdateDate(new Date(lastUpdated.toEpochMilli()))
              .build();

      var detailedStatus =
          FTSSearchIndexHostDetailedStatuses.builder()
              .mainIndex(FTSSearchIndexGenerationDetail.builder().status(this.mainStatus).build());
      stagedStatus.ifPresent(
          status -> {
            detailedStatus.stagedIndex(
                FTSSearchIndexGenerationDetail.builder().status(status).build());
          });
      return new FTSSearchIndexConfigStats.Builder()
          .id(id)
          .stats(indexHostStat)
          .detailedStatuses(detailedStatus.build())
          .build();
    }
  }

  private void setupMigration() {
    Migration migration = mock(Migration.class);
    when(migration.getState()).thenReturn(Migration.State.TARGET_STARTUP);
    var statsMap = new HashMap<ObjectId, FTSIndexStatusMap<FTSIndexHostStat>>();
    when(migration.getAllMigrationIndexStats()).thenReturn(statsMap);
    var detailedStatusesMap =
        new HashMap<ObjectId, FTSIndexStatusMap<FTSIndexHostDetailedStatuses>>();
    when(migration.getAllMigrationIndexDetailedStatuses()).thenReturn(detailedStatusesMap);

    when(this.searchDeploymentDescription.getMigrationOptional())
        .thenReturn(Optional.of(migration));
  }

  private void addIndexStatsToMigration(
      ObjectId indexId, String hostname, Instant optime, Instant lastUpdated) {
    String hostnameWithoutDomain = HostnameUtil.getHostnameWithoutDomain(hostname);
    Migration migration = this.searchDeploymentDescription.getMigrationOptional().orElseThrow();
    if (!migration.getAllMigrationIndexStats().containsKey(indexId)) {
      migration.getAllMigrationIndexStats().put(indexId, new FTSIndexStatusMap<>());
    }
    migration
        .getAllMigrationIndexStats()
        .get(indexId)
        .put(
            hostnameWithoutDomain,
            FTSSearchIndexHostStat.builder()
                .optime(
                    new BsonTimestamp((int) optime.getEpochSecond(), optime.getNano()).getValue())
                .statusCode(StatusCode.STEADY)
                .lastUpdateDate(new Date(lastUpdated.toEpochMilli()))
                .build());
    if (!migration.getAllMigrationIndexDetailedStatuses().containsKey(indexId)) {
      migration.getAllMigrationIndexDetailedStatuses().put(indexId, new FTSIndexStatusMap<>());
    }
    migration
        .getAllMigrationIndexDetailedStatuses()
        .get(indexId)
        .put(hostnameWithoutDomain, FTSSearchIndexHostDetailedStatuses.builder().build());
  }

  private Step<Result.NoData> createStep(Clock clock) {
    return new WaitForMongotReplicationCatchUpStep(
        this.planContext,
        this.state,
        this.partitionGroup,
        this.provisioningHost,
        WaitForMongotReplicationCatchUpStepIntTests.PROVISIONING_HOSTNAME,
        new WaitForMongotInitialSyncStep.Data(clock.instant()),
        ftsIndexConfigSvc,
        ftsIndexConfigStatsDao,
        ndsClusterSvc,
        searchDeploymentDescriptionSvc,
        replicaSetHardwareSvc,
        featureFlagSvc,
        blobstoreFeatureSvc,
        group,
        appSettings,
        clock);
  }

  private Step<Result.NoData> createStep(Instant startTime, Clock clock) {
    return new WaitForMongotReplicationCatchUpStep(
        this.planContext,
        this.state,
        this.partitionGroup,
        this.provisioningHost,
        WaitForMongotReplicationCatchUpStepIntTests.PROVISIONING_HOSTNAME,
        new WaitForMongotInitialSyncStep.Data(startTime),
        ftsIndexConfigSvc,
        ftsIndexConfigStatsDao,
        ndsClusterSvc,
        searchDeploymentDescriptionSvc,
        replicaSetHardwareSvc,
        featureFlagSvc,
        blobstoreFeatureSvc,
        group,
        appSettings,
        clock);
  }

  private PartitionGroupBuilder newPartitionGroup() {
    return new PartitionGroupBuilder().withGroupId(this.group.getId());
  }

  private PartitionGroupSyncSourceBuilder newPartitionGroupSyncSource() {
    return new PartitionGroupSyncSourceBuilder();
  }

  private SearchInstanceBuilder newSearchInstance() {
    return new SearchInstanceBuilder();
  }

  private ClusterBuilder newClusterDescription() {
    return new ClusterBuilder();
  }

  private static class SearchInstanceBuilder {
    private State state = State.STARTING;
    private final InstanceHardware hardware =
        SearchPhysicalModelTestFactory.getInstanceHardware(AWSSearchInstanceSize.S20_HIGHCPU_NVME);

    public SearchInstanceBuilder withState(State state) {
      this.state = state;
      return this;
    }

    public SearchInstanceBuilder withHostname(String hostname) {
      hardware.setHostNames(new Hostnames(hostname, hostname));
      return this;
    }

    public SearchInstance build() {
      return new SearchInstance(hardware, state, true);
    }
  }

  private class PartitionGroupSyncSourceBuilder {
    private ObjectId clusterUniqueId = new ObjectId();
    private String rsId = "default-rsId";
    private Instant createDate = clock.instant();

    public PartitionGroupSyncSourceBuilder withClusterUniqueId(ObjectId clusterUniqueId) {
      this.clusterUniqueId = clusterUniqueId;
      return this;
    }

    public PartitionGroupSyncSourceBuilder withReplicaSetName(String rsId) {
      this.rsId = rsId;
      return this;
    }

    public PartitionGroupSyncSourceBuilder withCreateDate(Instant createDate) {
      this.createDate = createDate;
      return this;
    }

    public PartitionGroupSyncSource build() {
      return new PartitionGroupSyncSource(
          clusterUniqueId, new Date(createDate.toEpochMilli()), AWSRegionName.US_EAST_1, rsId);
    }
  }

  private class PartitionGroupBuilder {

    private final ObjectId deploymentId = DEPLOYMENT_ID;
    private PartitionGroupSyncSource partitionGroupSyncSource =
        newPartitionGroupSyncSource().build();
    private ObjectId groupId = new ObjectId();
    private List<SearchInstance> instances = new ArrayList<>();

    public PartitionGroupBuilder withGroupId(ObjectId groupId) {
      this.groupId = groupId;
      return this;
    }

    public PartitionGroupBuilder withSyncSource(PartitionGroupSyncSource partitionGroupSyncSource) {
      this.partitionGroupSyncSource = partitionGroupSyncSource;
      return this;
    }

    public PartitionGroupBuilder withInstances(List<SearchInstance> instances) {
      this.instances = instances;
      return this;
    }

    public PartitionGroup build() {
      return new PartitionGroup(
          new ObjectId(), deploymentId, partitionGroupSyncSource, groupId, instances, null);
    }
  }

  private class ClusterBuilder {
    public ClusterDescription build() {
      final ClusterDescription clusterDescription =
          ClusterDescription.getCloudProviderClusterDescription(
              NDSModelTestFactory.getAWSClusterDescription(group.getId(), CLUSTER_NAME));
      clusterDescriptionDao.save(clusterDescription);
      return clusterDescription;
    }
  }
}
