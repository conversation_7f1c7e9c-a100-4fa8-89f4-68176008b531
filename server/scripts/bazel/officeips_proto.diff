diff --git gosre/master/proto/officeips/BUILD gosre/master/proto/officeips/BUILD
new file mode 100644
index 0000000..83c1cc0
--- /dev/null
+++ gosre/master/proto/officeips/BUILD
@@ -0,0 +1,12 @@
+load("@com_google_protobuf//bazel:proto_library.bzl", "proto_library")
+
+proto_library(
+    name = "officeips_proto",
+    srcs = ["officeips.proto"],
+    visibility = [
+      "//visibility:public",
+    ],
+    deps = [
+        "//gosre/master/proto/coretypes:coretypes_proto"
+    ]
+)
diff --git gosre/master/proto/officeips/officeips.proto gosre/master/proto/officeips/officeips.proto
index d25b905..e31b381 100644
--- gosre/master/proto/officeips/officeips.proto
+++ gosre/master/proto/officeips/officeips.proto
@@ -2,7 +2,7 @@ syntax = "proto3";
 
 package officeips;
 
-import "internal/proto/coretypes/coretypes.proto";
+import "gosre/master/proto/coretypes/coretypes.proto";
 
 service OfficeIps {
   // List all office IPs
