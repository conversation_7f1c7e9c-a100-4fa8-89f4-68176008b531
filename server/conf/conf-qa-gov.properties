# NB: The following properties are owned by <PERSON><PERSON> and managed with <PERSON>. Do not set them in this file as they will be overridden
# mms.http.bindhostname=127.0.0.1
# mms.https.PEMKeyFile=
# mongodb.ssl.PEMKeyFile=
# mongodb.ssl.CAFile=
# local.aws.accessKey=
# local.aws.secretKey=
client.authn.http.addressEnvVar=KUBE_AUTHN_SERVICE_KUBE_HTTP_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.authn.grpc.addressEnvVar=KUBE_AUTHN_SERVICE_KUBE_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.authz.grpc.addressEnvVar=KUBE_AUTHZ_SERVICE_KUBE_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.authzv2.grpc.addressEnvVar=KUBE_AUTHZ_SERVICE_KUBE_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.comment.grpc.addressEnvVar=KUBE_MMS_COMMENT_SERVICE_KUBE_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.meterusages.grpc.addressEnvVar=KUBE_MMS_METERING_METER_USAGES_SERVICE_KUBE_QA_US_EAST_1_AWS_CLOUD_QA_10GEN_CC
client.config.grpc.addressEnvVar=KUBE_CONFIG_SERVICE_KUBE_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.payments.grpc.addressEnvVar=KUBE_PAYMENTS_SERVICE_KUBE_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.event.grpc.addressEnvVar=KUBE_EVENTS_API_KUBE_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.pops.grpc.addressEnvVar=KUBE_SODAFOUNTAIN_SERVICE_KUBE_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.communication.grpc.addressEnvVar=KUBE_COMMUNICATION_SERVICE_KUBE_QA_US_GOV_WEST_1_AWS_CLOUD_QA_10GEN_CC
client.pricing.grpc.addressEnvVar=KUBE_MMS_PRICING_SERVICE_KUBE_QA_US_EAST_1_AWS_CLOUD_QA_10GEN_CC
account.centralUrl=https://account-qa.mongodbgov.com
account.email.fromAddr.support=MongoDB Account Government <<EMAIL>>
account.email.replyAddr.support=<EMAIL>
account.session.maxConcurrent=3
account.session.maxHours=12
amplitude.apiKey=
amplitude.secretKey=
atlas.backup.regional.deployment.enabled=false
atlasProxy.location=https://internal-downloads.mongodb.com/atlasproxy/releases/qa/
atlasUIS.location=
atlasUIS.version=0.0.0.0
authn.adminSecretKey=<SECRETMANAGER>
authn.internalClient.id=mdb_ic_id_6515ec52852acd7574f6f27a
authn.internalClient.secret=<SECRETMANAGER>
authn.enabled=true
authn.oauth.enabled=true
auto.provision.aws.reaper.runningLimit.days=2
mms.atlasMinimumMongoDBVersionSupportedByAgent=5.0
mms.migration.path.verify=false
mms.monitoring.performanceadvisor.rollup.scheduler.enabled=true
mms.monitoring.alwaysUpdateCollectedIndex.enabled=true
auto.provision.aws.trusted.accesskey=
auto.provision.aws.trusted.id=
auto.provision.aws.trusted.secretkey=
automation.agent.location=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/automation-agent/qa/
automation.agent.location.atlas=https://internal-downloads.mongodb.com/automation-dev/automation-agent/qa/
automation.agent.minimumVersion=13.10.0.8620-1
automation.agent.atlasMinimumVersion=13.37.2.9605-1
automation.agent.minimumVersionForClientPIT=4.3.0.2231-1
automation.agent.serverlessLocation=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/atlas-serverless-agent/qa/
automation.agent.serverlessLocation.atlas=https://internal-downloads.mongodb.com/automation-dev/automation-agent/qa/
automation.agent.serverlessMinimumVersion=0.0.0.0
automation.agent.version=13.41.0.9760-1
automation.kmipTool.location=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/automation-agent/qa/
automation.kmipTool.location.atlas=https://internal-downloads.mongodb.com/automation-dev/automation-agent/qa/
automation.versions.autoRefreshUri=classpath://mongodb_version_manifest.json
automation.versions.source=remote
automation.versions.atlasDecoupledFromCm=false
avalara.accountNumber=**********
avalara.companyCode.ireland=mongodb-ltd-qa
avalara.companyCode.inc=mongodb-qa
avalara.licenseKey=
avalara.url=https://sandbox-rest.avatax.com
aws.accessKey.dataWarehouse=<SECRETMANAGER>
aws.accesskey=<SECRETMANAGER>
aws.dataWarehouse.bucket=mongodb-datawarehouse-qa
aws.dataWarehouse.bucket.prefix=data_dump/cloud-gov/
aws.dataWarehouse.kinesis.accessKey=
aws.dataWarehouse.kinesis.secretKey=
aws.secretKey.dataWarehouse=<SECRETMANAGER>
aws.secretkey=<SECRETMANAGER>
aws.ses.endpoint=https://email.us-gov-west-1.amazonaws.com
awsGraviton.minimumMongoDBVersion=4.2
local.azure.multiTenantAppId=
local.azure.multiTenantAppSecret=
baas.centralUrl=
backup.kmip.server.ca.file=/opt/mongodb-mms/classes/kmip_test_server_ca.pem
backup.kmip.server.host=ec2-34-225-178-37.compute-1.amazonaws.com
backup.kmip.server.port=5696
biConnector.location=https://info-mongodb-com.s3.amazonaws.com/mongodb-bi/v2/
biConnector.location.atlas=https://internal-downloads.mongodb.org/mongodb-bi/v2/
biConnector.minimumVersion=2.3.1
biConnector.version=2.14.24
atlas.biConnector.version=2.14.24
atlas.api.versioning.rejectFutureDateCalls=false
atlas.api.versioning.enabled=true
atlas.api.versioning.preview=disabled
atlas.api.versioning.upcoming=disabled
braintree.clientId=
braintree.environment=sandbox
braintree.merchantAccountId.inc=
braintree.merchantAccountId.ltd=
braintree.merchantId=
braintree.privateKey=
braintree.publicKey=
braintree.url=https://sandbox.braintreegateway.com
braintree.maxRequestsPerSecond=20
brs.agent.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/backup-agent/qa/
brs.agent.minimumVersion=*********-1
brs.agent.version=7.8.1.1109-1
brs.client.pit.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/mongodb-backup-restore-util/qa/
brs.client.pit.version=13.41.0.9760-1
brs.queryable.lruCacheCapacityMB=512
brs.queryable.mongod.memoryQuotaMB=8192
brs.queryable.proxyDebugPort=26999
brs.queryable.proxyPort=25999
brs.queryable.tls.disabledProtocols=SSLv2Hello,SSLv3,TLSv1,TLSv1.1,TLSv1.3
brs.queryable.tls.disabledCiphers=TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384
brs.queryable.wiredTiger.engineConfig.cacheSizeGB=1
brs.s3.validation.testing=disabled
brs.tunnel.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/backup-tunnel/
brs.tunnel.version=13.41.0.9760-1
mms.backup.snapshot.volume.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/snapshot-volume/${env}/
mms.backup.snapshot.volume.version=13.41.0.9760-1
brs.wtc.maxNumBufferedFileBatchesOnAgent=1
charts.api.url=
charts.api.secretKey=
charts.apiSecret=
charts.stitch.app.id=
charts.centralUrl=
charts.stitch.useLocalDeploymentModel=false
charts.template.aws.accessKey=
charts.template.aws.secretKey=
charts.template.bucketName=
charts.upgrade.poolSize=5
charts.version=0.0.0
clienthub.domain=<SECRETMANAGER>
clienthub.password=<SECRETMANAGER>
clienthub.user=<SECRETMANAGER>
data.delete.queue.size=500
dataWarehouse.dailyDataExport=true
dataWarehouse.hourlyDataExport=false
dataWarehouse.dataExport.nonOverlappingNamespaces=nds.planner.plans,mmsdbconfig.config.alertConfigHistory
datadog.api.url=https://api.ddog-gov.com/api/v1
devHub.centralUrls=
distributed.cron.svc.registries=com.xgen.svc.mms.svc.cron.MmsCronJobRegistry,com.xgen.svc.nds.svc.AtlasClustersCronJobRegistry,com.xgen.svc.brs.svc.cron.BackupCronJobRegistry,com.xgen.svc.mms.svc.billing.audit.BillingAuditorCronRegistry,com.xgen.svc.mms.svc.billing.cron.BillingCronJobRegistry,com.xgen.svc.mms.svc.billing.cron.PaymentCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportAuditorCronJobRegistry
distributedAlert.executorThreadCount=30
distributedAlert.groupCollateQueueOfferTimeoutMs=1000
distributedAlert.groupCollateQueueSize=100
distributedAlert.groupQueueOfferTimeoutMs=1000
distributedAlert.groupQueueSize=100
distributedAlert.hostQueueSize=100
dyn.customerName=<SECRETMANAGER>
dyn.password=
dyn.username=
ecosystem.enabled=false
eloqua.siteId=*********
eloqua.sync.enabled=false
executor.thread.count=2
fts.confcall.circuitBreaker.maxConcurrent=20
ga.accountId=********
ga.enabled=true
ga.p12file=ga-cloud-manager-analytics-api-nonprod.p12
ga.propertyId=***********-1
ga.serviceAccount=<EMAIL>
ga.viewId=*********
gtm.containerId=GTM-5ZD5LD
hipchat.api.message.url.v1=https://api.hipchat.com/v1/rooms
hipchat.api.message.url=https://api.hipchat.com/v2/room
iam.login.postAuth.allowLocalRedirect=true
intercom.apiKey=<SECRETMANAGER>
intercom.appId=rdi8z17e
intercom.enabled=false
intercom.enabledLoggedInPages=false
intercom.qualtricsSurvey.api.token=
intercom.secretKey=<SECRETMANAGER>
ipstack.api.accessKey=
java.fipsprovider.default=true
job.processor.await.termination.time=60
job.processor.await.termination.unit=SECONDS
job.processor.aws.requests.per.second=10.0
job.processor.azure.requests.per.second=10.0
job.processor.blacklist.refresh.time=5
job.processor.blacklist.refresh.unit=SECONDS
job.processor.core.thread.count=10
job.processor.empty.queue.wait=5000
job.processor.enabled=false
job.processor.keepalive.time=1
job.processor.keepalive.unit=MINUTES
job.processor.max.thread.count=10
job.processor.server.tags.enabled=true
job.processor.server.tags.refresh.time=5
job.processor.server.tags.refresh.unit=SECONDS
job.processor.partitionQuery.numPartitions=1
job.processor.ping.time=10
job.processor.ping.unit=SECONDS
job.processor.ping.staleCount=60
local.aws.secretsRegion=us-gov-east-1,us-gov-west-1
local.aws.secretsPrimaryRegion=us-gov-west-1
logCollection.maxDiskSpaceMB=20480
marketing.centralUrls=
mlab.clientId=atlas-qa
mlab.portalBaseUrl=https://www.mlab.com
mlab.sharedSecret=
nds.serverless.sniproxy.default.count=3
nds.meterUsage.submission.enabled=true
nds.kms.privateNetworking.meterUsage.submission.enabled=true
nds.premiumSKU.meterUsage.submission.enabled=true
mms.accountMultiFactorAuth.enabled=true
mms.accountSuspension.enabled=true
mms.accountDeletion.enabled=true
mms.sendAccountDeletionEmailEnabled=false
mms.enableNewAdminUsersPage=true
mms.enableCommentService=true
mms.acme.defaultProvider=LETS_ENCRYPT_V2
mms.acme.tracking.publicKey=<SECRETMANAGER>
mms.acme.tracking.privateKey=<SECRETMANAGER>
mms.addUserApi.countryRequired=true
mms.admin.filterBackupJobs=false
mms.adminEmailAddr=<EMAIL>
mms.azCapacity.internalProjectId=67d1a1f365a90038fba6bf83
mms.agent.apiKeyReadPolicy=CACHED
mms.agent.groupReadPolicy=CACHED
mms.agent.organizationReadPolicy=CACHED
mms.agentApiKey.cacheSnapshotMillis=30000
mms.agentCentralUrl=https://api-agents-qa.mongodbgov.com
mms.alertFromEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertFromEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alertReplyToEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertReplyToEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alerts.AutomationAgentDown.maximumPingAgeMinutes=2
mms.alerts.BackingDatabaseProcessDown.connectTimeoutMs=10000
mms.alerts.BackupAgentDown.maximumPingAgeMinutes=15
mms.alerts.BackupDaemonLowFreeHeadSpace.frequency=0 0 * * * ?
mms.alerts.BackupJobTooBusy.frequency=0 0 0 * * ?
mms.alerts.BiConnectorAlert.maximumPingAgeMinutes=6
mms.alerts.ClusterMongosIsMissing.maximumPingAgeMinutes=6
mms.alerts.HostDown.maximumPingAgeMinutes=6
mms.alerts.HostExposed.addToNewGroups=true
mms.alerts.ServerlessProxyDown.maximumPingAgeMinutes=4
mms.alerts.FlexProxyDown.maximumPingAgeMinutes=4
mms.alerts.MonitoringAgentDown.maximumPingAgeMinutes=4
mms.alerts.RealmDown.maximumPingAgeMinutes=4
mms.alerts.NdsProxyDown.maximumPingAgeMinutes=1
mms.alerts.OplogBehind.maximumOplogPushAgeMinutes=75
mms.alerts.PrepaidPlanMissingSKU.frequency=0 0 7 * * ?
mms.alerts.ReplicaSetAlertCheck.maximumPingAgeMinutes=6
mms.alerts.StalePendingInvoices.frequency=0 0 7 * * ?
mms.alerts.SubscriptionUsageCronJobDidntRunLastHour.frequency=0 0 * * * ?
mms.alerts.SystemDatabaseProcessStartupWarnings.frequency=0 0 * * * ?
mms.alerts.TooManyPendingInvoices.frequency=0 0 7 * * ?
mms.alerts.EncryptionAtRestKMSNetworkAccessDenied.frequency=PT15M
mms.alerts.EncryptionAtRestConfigNoLongerValid.frequency=PT15M
mms.alerts.UsersWithoutMultiFactorAuth.frequency=0 0/15 * * * ?
mms.alerts.createSystemAlertsEnabled=false
mms.alerts.delaySeconds=120
mms.alerts.disabledEventTypes=CREDIT_CARD_CURRENT,CHARGE_SUCCEEDED,CHARGE_FAILED,CREDIT_CARD_ABOUT_TO_EXPIRE,ORG_CREDIT_CARD_ADDED,ORG_CREDIT_CARD_UPDATED,ORG_CREDIT_CARD_CURRENT,ORG_CREDIT_CARD_ABOUT_TO_EXPIRE,SERVICE_ACCOUNT_CREATED,SERVICE_ACCOUNT_DELETED,ORG_SERVICE_ACCOUNT_MAX_SECRET_VALIDITY_EDITED,SERVICE_ACCOUNT_ROLES_CHANGED,SERVICE_ACCOUNT_DETAILS_CHANGED,SERVICE_ACCOUNT_ADDED_TO_GROUP,SERVICE_ACCOUNT_REMOVED_FROM_GROUP,SERVICE_ACCOUNT_UI_IP_ACCESS_LIST_INHERITANCE_ENABLED,SERVICE_ACCOUNT_UI_IP_ACCESS_LIST_INHERITANCE_DISABLED,SERVICE_ACCOUNT_ACCESS_LIST_ENTRY_ADDED,SERVICE_ACCOUNT_ACCESS_LIST_ENTRY_DELETED,SERVICE_ACCOUNT_SECRET_ADDED,SERVICE_ACCOUNT_SECRET_DELETED,ORG_SERVICE_ACCOUNT_SECRETS_EXPIRED,ORG_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRED, ORG_SERVICE_ACCOUNT_SECRETS_EXPIRING,ORG_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRING,CUSTOM_SESSION_TIMEOUT_MODIFIED,GROUP_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRING,GROUP_SERVICE_ACCOUNT_SECRETS_EXPIRING,GROUP_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRED,GROUP_SERVICE_ACCOUNT_SECRETS_EXPIRED
mms.alerts.intervalSeconds=30
mms.alerts.unreachable.lookBackMinutes=5
mms.alerts.unreachable.minimumConfCount=250
mms.alerts.unreachable.thresholdPercent=90
mms.alerts.webhook.readTimeoutMs=5000
mms.alerts.webhook.socketTimeoutMs=2000
mms.alerts.GlobalServiceAccountSecretExpiredAlertCheck.frequency=PT12H
mms.atlasApiDeprecation.fromEmailAddr=<EMAIL>
mms.atlasApiDeprecation.replyToEmailAddr=<EMAIL>
mms.allowOpsManagerOrgs=false
mms.api.readPreference=secondaryPreferred
mms.api.throttling.enabled=false
mms.assets.browserErrorTracking=false
mms.assets.browserErrorTrackingApiKey=6666d88d5b732b7725a8107ba15b4eb6
mms.assets.minified=true
mms.assets.packaged=false
mms.atlas.backup.skipBillingOnOplogMigrationDestination=false
mms.atlas.backup.skipBillingOplogMigrationForAzureOplogStore=false
mms.atlas.backup.skipBillingOplogMigrationForGcpOplogStore=false
mms.atlas.defaultReplicaSetScalingStrategy=WORKLOAD_TYPE
mms.atlasAWSMinimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlas.gcp.minimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlasSearch.tracingSamplerRate=0.0
mms.auth.logo.fileName=mdb_logo_gov
mms.auth.methods.google.enabled=false
mms.auth.methods.google.oktaIdpId=
mms.auth.methods.github.enabled=false
mms.auth.methods.github.oktaIdpId=
mms.authz.enableAuthzSvc=true
mms.authz.roleSetParityCheck.percentage=-1
mms.authz.epaParityCheck.percentage=-1
mms.authz.syncResources=true
mms.authz.tags.timeoutMillis=100
mms.authz.syncTeamsToUserGroups=false
mms.authz.mandatoryResourceSync.percentage=-1
mms.userGroupFailureJob.retryDelayInMin=3
mms.userGroupFailureJob.numberRetries=3
mms.authz.rolesCache.enabled=true
mms.cps.billing.oplogStorageRegionUsage=true
mms.cps.directAttachPreWarm.skip.gcp=true
mms.cps.billing.cpsAwsDownloadUsageSubmission=true
mms.cps.billing.cpsAwsExportUsageSubmission=true
mms.cps.billing.cpsAwsSnapshotUsageSubmission=true
mms.cps.billing.cpsAzureDownloadUsageSubmission=false
mms.cps.billing.cpsAzureExportUsageSubmission=false
mms.cps.billing.cpsGcpDownloadUsageSubmission=true
mms.cps.billing.cpsGcpExportUsageSubmission=true
mms.cps.billing.cpsGcpSnapshotUsageSubmission=true
mms.cps.billing.cpsExportUsageSubmission=true
mms.cps.billing.oplogUsageCollection=true
mms.cps.billing.oplogUsageSubmission=true
mms.cps.billing.oplogUsageMigration=true
mms.cps.collectionMetadataBucketRegionName=us-east-1
mms.cps.collectionMetadataBucketRegionNameGov=us-gov-east-1
mms.cps.collectionMetadataBucketRegionNameSecondary=us-east-2
mms.cps.collectionMetadataBucketRegionNameSecondaryGov=us-gov-west-1
mms.cps.collectionMetadataBucket=atlas-backup-collection-metadata-us-east-1-qa-gov
mms.cps.collectionMetadataBucketGov=atlas-backup-collection-metadata-us-gov-east-1-qa-gov
mms.cps.collectionMetadataBucketSecondary=atlas-backup-collection-metadata-us-east-2-qa-gov
mms.cps.collectionMetadataBucketSecondaryGov=atlas-backup-collection-metadata-us-gov-west-1-qa-gov
mms.cps.collectionRestore.diskSpaceThreshold=.90
mms.runFgaOverRbacWhenAnnotated=false
mms.automation.agentaudit.conf.allowPercent=60.0
mms.automation.changes.pruning.retentionDays=30
mms.automation.changes.pruning.changesPerSecond=500
mms.automation.changes.pruning.maxPerRun=1000000
mms.automation.changes.watcher.enabled=false
mms.automation.settings.refreshPeriodSecs=45
mms.automation.settings.dnsCacheTtlSeconds=300
mms.automation.settings.sleepTimes.confCallSleepTimeSecs=15
mms.automation.settings.sleepTimes.errorStateSleepTimeSecs=15
mms.automation.settings.sleepTimes.goalStateSleepTimeSecs=15
mms.automation.settings.agentStatus.sendPeriodSecs=120
mms.automation.settings.agentStatus.maxPlansToSend=5
mms.automation.settings.timeBetweenMongosRollingRestartMs=20000
mms.featureFlag.awsGraviton4=disabled
mms.featureFlag.awsGraviton4NVMe=disabled
mms.featureFlag.awsCapacityAwareAZSelection=enabled
mms.featureFlag.azureCapacityAwareAZSelection=enabled
mms.featureFlag.gcpCapacityAwareAZSelection=enabled
mms.featureFlag.capacityAwareAZPreventativeRollbackSupport=controlled
mms.featureFlag.backup.s3blockstore.calculateMD5=disabled
mms.featureFlag.backup.s3oplogstore.calculateMD5=disabled
mms.featureFlag.atlasDbcheckEnableMidCollectionResumability=controlled
mms.featureFlag.atlasPrioritizeHavingPrimaryOnUpscaledNode=enabled
mms.featureFlag.atlasDiskWarmingSupport=enabled
mms.featureFlag.atlasIgnoreDiskWarmingState=controlled
mms.featureFlag.sqlSchemaManagementUI=disabled
mms.featureFlag.streamsEnableAzure=disabled
mms.featureFlag.streamsEnableAdditionalAzureRegions=disabled
mms.featureFlag.streamsAzurePrivateLink=disabled
mms.featureFlag.streamsAWSPrivateLink=disabled
mms.featureFlag.streamsEnableAdditionalRegions=disabled
mms.featureFlag.streamsAWSProxyAL2023Enabled=controlled
mms.featureFlag.streamsAzureProxyAL2023Enabled=controlled
mms.featureFlag.atlasDbcheckWaitForAllNodesToObserveDbcheckStop=controlled
mms.featureFlag.atlasAutomaticEmbeddedConfigTransitions=enabled
mms.featureFlag.vqpBinning=controlled
mms.featureFlag.atlasAzureDsv5AndEsv5InstanceFamilies=enabled
mms.featureFlag.atlasLiveImportHelixPush=controlled
mms.featureFlag.atlasLiveImportHelixPull=enabled
mms.featureFlag.atlasShardedClustersBehindUniformFrontend=disabled
mms.featureFlag.atlasMongomirrorLiveImportHelix=enabled
mms.featureFlag.atlasLiveImportLargeNodePool=disabled
mms.featureFlag.atlasEnableAdditionalDbcheckValidations=controlled
mms.featureFlag.atlasStreamsSP10InstanceTier=disabled
mms.featureFlag.dataExplorerCompassWeb=disabled
mms.featureFlag.dataExplorerCompassWeb.userControlledDisable=controlled
mms.featureFlag.atlasClusterScalingImprovementsPhase1=enabled
mms.featureFlag.atlasIndependentShardScaling=enabled
mms.featureFlag.atlasAsymmetricShardAutoscaling=controlled
mms.featureFlag.baas.PricingChange=disabled
mms.featureFlag.billingResourceTaggingBulkEndpoint=enabled
mms.featureFlag.streamsVpcPeeringPreview=disabled
mms.featureFlag.enableLogRequestTimeframe=disabled
mms.featureFlag.enabledRevampedTriggersUi=enabled
mms.featureFlag.enableTypeScriptInFunctions=controlled
mms.featureFlag.enterpriseLandingPage=disabled
mms.featureFlag.enterpriseSandbox=disabled
mms.featureFlag.deprecateEndpointsAndDataApi=disabled
mms.featureFlag.deprecateThirdPartyServices=disabled
mms.featureFlag.allowCrossRegionExtendedStorage=disabled
mms.featureFlag.disableDarkReader=disabled
mms.featureFlag.disableDiscoveryIpAddressAliases=disabled
mms.featureFlag.bicDeprecationShowWarning=disabled
mms.featureFlag.bicDeprecation=disabled
mms.featureFlag.exemptFromBicDeprecation=disabled
mms.featureFlag.atlasMigrationHubCutover=enabled
mms.featureFlag.customerPinnedFcvAndMdbDowngrade=enabled
mms.featureFlag.atlasAllowOneNodeDownInPacpcm=enabled
mms.featureFlag.atlasAllowSeveralNodesDownInPacpcm=enabled
mms.featureFlag.atlasGcpHyperdiskExtreme=controlled
mms.featureFlag.atlasAutomationShorterQuiesceTime=enabled
mms.featureFlag.atlasAutomationSpecifyForceTrueForShutdowns=enabled
mms.featureFlag.atlasAllowUpscalingToCleanlyAbandonPlans=enabled
mms.featureFlag.RtppDiskThroughputMetrics=enabled
mms.featureFlag.atlasChainPauseMoves=controlled
mms.featureFlag.atlasFcvPinningInUi=enabled
mms.featureFlag.atlasKmipKeyRotationInMaintenanceWindows=controlled
mms.featureFlag.validateS3ChunksOnDownload=controlled
mms.featureFlag.atlasConfigurableGp3Iops=controlled
mms.featureFlag.atlasExcludeRegionUseast2FromAzureDsv5Esv5Families=controlled
mms.featureFlag.customSessionTimeouts=controlled
mms.featureFlag.atlasAutomatePriorityTakeover=controlled
mms.featureFlag.atlasAdvancedRegionalizedPrivateEndpoints=controlled
mms.featureFlag.atlasResourcePoliciesExtensionWithUi=enabled
mms.featureFlag.serverlessUpgradeToDedicatedDebugMode=disabled
mms.featureFlag.atlasAutohealResyncImprovements=enabled
mms.featureFlag.atlasAutohealRemoveIcmpPing=enabled
mms.featureFlag.atlasAcknowledgesApiKeyFromChefConfCall=enabled
mms.featureFlag.atlasAlwaysManagedDefaultRwConcern=enabled
mms.featureFlag.atlasTunedInstanceIndexBuildLimits=enabled
mms.featureFlag.atlasTurnDiskWarmingOffForAzureLegacy=controlled
mms.featureFlag.atlasAutohealReducedThreshold=enabled
mms.featureFlag.disableScramSha1Auth=controlled
mms.featureFlag.atlasAzureExcludeConstrainedCombos=controlled
nds.autoEnableAtlasTunedInstanceIndexBuildLimitsNewProjects=true
mms.featureFlag.dataExfiltrationManageSecurityGroupsEnabled=disabled
mms.featureFlag.dataExfiltrationSecurityGroupsExcludeAllowAllRuleEnabled=disabled
mms.featureFlag.dataExfilPreventionSidecarEnabled=disabled
mms.featureFlag.dataExfilPreventionSidecarToProxyRoutingEnabled=disabled
mms.featureFlag.atlasAutohealReduceShutdownTime=enabled
mms.featureFlag.atlasAutohealReduceShutdownTimeHealRepair=controlled
mms.atlasAccessTransparencyX509CertsEnabled=false
mms.atlasEmployeeInformationOktaReadOnlyClientCredentials=
mms.atlasEmployeeInformationOktaApiKeyUrl=
mms.atlasEmployeeInformationOktaApiKeyClientId=
mms.atlasEmployeeInformationOktaIntegrationEnabled=false
mms.atlasClusterUpdateOptimisticConcurrency=true
mms.featureFlag.streamsVPCPeering=disabled
mms.featureFlag.disableCloudNav=controlled
mms.featureFlag.atlasFcvDowngradeAdminAction=enabled
mms.featureFlag.atlasResourcePolicies=disabled
mms.featureFlag.orgLevelGenAiControlSwitch=disabled
mms.featureFlag.agentRotatesAtlasProxyLogs=disabled
mms.featureFlag.al2023AWSOs=disabled
mms.featureFlag.awsAL2023ForceMigrateNVMe=disabled
mms.featureFlag.al2023AzureOs=disabled
mms.featureFlag.azureAL2023ForceMigrateNVMe=disabled
mms.featureFlag.al2023GCPOs=disabled
mms.featureFlag.allowEnforceMinTls13=disabled
mms.featureFlag.atlasTls13AutoUpgradeEnabled=disabled
mms.featureFlag.atlasDeprecateTls10AndTls11=disabled
mms.featureFlag.dataExplorerGenAIFeatures=controlled
mms.featureFlag.dataExplorerGenAISampleDocument=controlled
mms.featureFlag.disableDataExplorerGenAISampleDocumentPassing=controlled
mms.featureFlag.atlasReleaseAwsIps=disabled
mms.featureFlag.alertHostSSHSessionStarted=controlled
mms.featureFlag.awsUseIpamIp=disabled
mms.featureFlag.autoscaling.predictive.compute.enabled=disabled
mms.featureFlag.autoscaling.predictive.compute.m10m20enabled=disabled
mms.featureFlag.enableParcaAgent=disabled
mms.featureFlag.customer.queryShapeInsights=controlled
mms.rootVolumeMetrics.rolloutPercentage=100
mms.backup.alertsEmailAddr=<EMAIL>
mms.backup.allowDevelopmentVersions=true
mms.backup.allowFastSchedule=true
mms.backup.allowRefTimeOfDay=true
mms.backup.allowSnapshotExpiryUpdate=true
mms.backup.archive.aws.accesskey=<SECRETMANAGER>
mms.backup.archive.aws.secretkey=<SECRETMANAGER>
mms.backup.automatedRestoreExpirationHours=48
mms.backup.blockstore.aws.accesskey=<SECRETMANAGER>
mms.backup.blockstore.aws.secretkey=<SECRETMANAGER>
mms.backup.blockstore.aws.bucket=oplogstore-slices-test1
mms.backup.blockstore.aws.bucket.objectlock=brs-test-objectlock
mms.backup.blockstore.aws.endpoint=s3.amazonaws.com
mms.backup.blockstore.aws.path=false
mms.backup.blockstore.hitachi.accesskey=<SECRETMANAGER>
mms.backup.blockstore.hitachi.secretkey=<SECRETMANAGER>
mms.backup.blockstore.hitachi.bucket=mongo
mms.backup.blockstore.hitachi.endpoint=https://tryhcpforcloudscale.hitachivantara.com
mms.backup.blockstore.hitachi.path=true
mms.backup.blockstore.cohesity.accesskey=<SECRETMANAGER>
mms.backup.blockstore.cohesity.secretkey=<SECRETMANAGER>
mms.backup.blockstore.cohesity.bucket=MDBView
mms.backup.blockstore.cohesity.endpoint=https://cohesitymdbaccess-1567838672.us-east-1.elb.amazonaws.com
mms.backup.blockstore.cohesity.path=true
mms.backup.blockstore.cohesity.cert=-----BEGIN CERTIFICATE-----\r\nMIID0DCCArgCCQC86yt1l98PNTANBgkqhkiG9w0BAQsFADCBqTELMAkGA1UEBhMC\r\nVVMxEzARBgNVBAgMCkNhbGlmcm9uaWExETAPBgNVBAcMCFNhbiBqb3NlMREwDwYD\r\nVQQKDAhDb2hlc2l0eTEUMBIGA1UECwwLRW5naW5lZXJpbmcxITAfBgNVBAMMGGNv\r\naGVzaXR5LmVuZ2luZWVyaW5nLmNvbTEmMCQGCSqGSIb3DQEJARYXc2FyYW4ucmF2\r\naUBjb2hlc2l0eS5jb20wHhcNMjEwOTIwMTM0MzIyWhcNMjIwOTIwMTM0MzIyWjCB\r\nqTELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmcm9uaWExETAPBgNVBAcMCFNh\r\nbiBqb3NlMREwDwYDVQQKDAhDb2hlc2l0eTEUMBIGA1UECwwLRW5naW5lZXJpbmcx\r\nITAfBgNVBAMMGGNvaGVzaXR5LmVuZ2luZWVyaW5nLmNvbTEmMCQGCSqGSIb3DQEJ\r\nARYXc2FyYW4ucmF2aUBjb2hlc2l0eS5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB\r\nDwAwggEKAoIBAQCnDunK1oYGzsmQWF8i1wvy5RYBrN6YTbzyYD4FG1BNgkpl0z7I\r\ngmS9ge6MqU4C1MEhQ+TjPKbRN+yOlXYNe7rFNDvYEHDc03HvRhc9mHHqRbsV6V92\r\nOmOHVb6CTNmPBS6QGSb3rMJJ0W1ate6zKeUbUKlHMvysPX6rPOWDNO6VkdUuNo4J\r\nJ7JatKVB4tuU9cVUcdDrJZYCEmEBp7QXyCvycezji1NfPPQc/jGcqKeKtluFEU7t\r\nTeCJO1B1Xo/QRWGZZFDNfowzMvi347rsCcY+4QxbYMk2uuN7FDRVqYYyXsWVgvjl\r\nSVi+8rhcTo77FbYswi7avRBHIirqMo066nUrAgMBAAEwDQYJKoZIhvcNAQELBQAD\r\nggEBAHUNf3fRGZdTScqvx/SAuzcZJ913AEPLU1NNOYKqLmpFPKPip5R+B9d1HZnB\r\nGtEX4C5PmSTUS3nbimRZt0J8986Nd+ybmVQE8DK2F++clt1WJVQvr7kQiZvtSNfX\r\n1pC/DMFigEJ6BNBftQfrp5n0XqQonkxziZ/Bu4kQsF+QFI4sbj2hosV0/jv9lscE\r\n3HizbeFZosxKjGNDcExTim04grYHObIwRFVf/BJRMRZ9XFKCIl2qyjGYx05KJ6Ba\r\nZ/MPknvZO+s4aKn0+66uewUQdw5yWyuDRG/Vx8zAc80L24jPWhNY85TVcIeIpa9N\r\ncJXEQAny9S6XpYTDLmLqc6FxWtE=\r\n-----END CERTIFICATE-----
mms.backup.daemonAssignment.replSetSizeMultiple=1.3
mms.backup.daemonAssignment.replSetSizeMultiplePreferred=1.1
mms.backup.daemonAssignment.usableHeadPercent=0.80
mms.backup.daemonAssignment.usableHeadPreferredPercent=0.95
mms.backup.daemon.diagnostics.minNumJobs=1
mms.backup.donate.chosenJob.space.lower.threshold=0.04
mms.backup.donate.chosenJob.space.upper.threshold=0.13
mms.backup.donate.donor.space.threshold=0.85
mms.backup.logSnapshotFileInfo=false
mms.backup.maximumPITRestoreOplogWindowDays=7
mms.backup.minimumOplogWindowHours=1
mms.backup.provisioningRequired=true
mms.backup.region.default=NY_NJ
mms.backup.s3OplogStoreEnabled=true
mms.backup.wiredTiger.engineConfig.cacheSizeGB=2
mms.backupCentralUrl=https://api-backup-qa.mongodbgov.com
mms.billing.alertsEnabled=false
mms.billing.bccEmailAddress=<EMAIL>
mms.billing.coreBIEmailAddress=<EMAIL>
mms.billing.enabled=true
mms.billing.salescomp.enabled=true
mms.billing.cronJobsDisabled=false
mms.billing.gcp.authProviderX509CertUrl=https://www.googleapis.com/oauth2/v1/certs
mms.billing.gcp.authUri=https://accounts.google.com/o/oauth2/auth
mms.billing.gcp.clientEmail=<EMAIL>
mms.billing.gcp.clientId=<SECRETMANAGER>
mms.billing.gcp.clientX509CertUrl=https://www.googleapis.com/robot/v1/metadata/x509/service-account-officeit-61928%40mdb-gcp-marketplace-test.iam.gserviceaccount.com
mms.billing.gcp.privateKey=<SECRETMANAGER>
mms.billing.gcp.privateKeyId=<SECRETMANAGER>
mms.billing.gcp.projectId=mdb-gcp-marketplace-test
mms.billing.gcp.accountIds=d3806197-e921-4343-aa90-fc287785a82a
mms.billing.gcp.tokenUri=https://oauth2.googleapis.com/token
mms.billing.gcp.type=service_account
mms.billing.gcp.selfServeMetric=mongodb-atlas-self-service-qa.endpoints.mdb-gcp-marketplace-test.cloud.goog/pay_as_you_go_usage
mms.billing.gcp.atlasPrivateMetric=mongodb-atlas-producer-qa.endpoints.mdb-gcp-marketplace-test.cloud.goog/atlas_private_usage
mms.billing.gcp.atlasPrepaidMetric=mongodb-atlas-producer-qa.endpoints.mdb-gcp-marketplace-test.cloud.goog/atlas_prepaid_usage
mms.billing.gcp.selfServeProductName=mongodb-atlas-self-service-qa.endpoints.mdb-gcp-marketplace-test.cloud.goog
mms.billing.gcp.atlasGovAnnualCommitMetric=mongodb-atlas-for-government-private-offer-test.endpoints.mdb-gcp-marketplace-test.cloud.goog/Mongodb_atlas_gov
mms.billing.gcp.atlasGovPrepaidMetric=mongodb-atlas-for-government-private-offer-test.endpoints.mdb-gcp-marketplace-test.cloud.goog/Mongodb_atlas_gov_prepaid_atlas
mms.billing.gcp.marketplaceUrl=https://console.cloud.google.com
mms.billing.aws.mp.accessKey=<SECRETMANAGER>
mms.billing.aws.mp.secretKey=<SECRETMANAGER>
mms.billing.aws.mp.disbursement.crons.enabled=false
mms.billing.aws.mp.athena.accessKey=
mms.billing.aws.mp.athena.secretKey=
mms.billing.aws.mp.customerIds=
mms.billing.aws.mp.productCodes=
mms.billing.azure.mp.azureNative.service.marketplaceHost=
mms.billing.azure.mp.aad.azureNative.service.clientId=
mms.billing.azure.mp.aad.azureNative.service.secretKey=
mms.billing.azure.mp.aad.landing.clientId=
mms.billing.azure.mp.aad.landing.secretKey=
mms.billing.azure.mp.aad.landing.secretKey.expiration=
mms.billing.azure.mp.aad.service.clientId=
mms.billing.azure.mp.aad.service.secretKey=
mms.billing.azure.mp.aad.service.secretKey.expiration=
mms.billing.azure.mp.aad.productGraph.clientId=
mms.billing.azure.mp.aad.productGraph.secretKey=
mms.billing.azure.mp.aad.productGraph.secretKey.expiration=
mms.billing.azure.mp.corsUrls=https://login.live.com,https://login.microsoftonline.com,https://partner.microsoft.com,https://portal.azure.com
mms.billing.azure.export.api.storage.account.name=atlasbilling
mms.billing.azure.export.api.client.secretKey=
mms.billing.azure.export.api.blob.container.name=atlasbilling
mms.billing.azure.export.api.clientId=
mms.billing.azure.export.api.tenantId=
mms.billing.azure.export.destinationS3Bucket=
mms.billing.govUpliftPercentage=7
mms.billing.internalBillingPlatformRecipientEmail==<EMAIL>
mms.billing.internalPaymentCheck=true
mms.billing.promoCodeCreatedEmailAddress=<EMAIL>
mms.billing.retrieveStripeEvents=false
mms.billing.selfServePayments.enabled=false
mms.billing.selfServeProducts=seed-data/SelfServeProducts.json
mms.billing.sendInvoiceEmails=false
mms.billing.sfdcProductCodes=seed-data/SalesforceProductCodes.json
mms.billing.sfscSyncEnabled=false
mms.billing.statusPage.apiKey=
mms.billing.dataExportConfig=data-export/data-export-conf-gov.json
mms.billing.auditors.config=seed-data/BillingAuditorConfigs.json
mms.billing.statusPage.enabled=false
mms.billing.partners.aws.reporting.minAutoReportingDate=2024-03-20
mms.billing.partners.aws.reporting.mode=ALL
mms.billing.partners.azure.reporting.mode=ALL
mms.billing.partners.gcp.reporting.mode=ALL
mms.billing.pricingApiConsumers=
mms.billing.resourceTagRetrievalBatchSize=500
mms.billing.sendDailyBillingReport=true
mms.billing.lineItemMaterializedViewJob.enabled=true
mms.billing.internalReportRecipientEmail=<EMAIL>
mms.billing.featureteam.lineitem.rateLimitPerMinute=30
mms.billing.featureteam.lineitem.sku.size=50
mms.billing.featureteam.lineitem.group.size=5
mms.billing.featureteam.lineitem.startdate.size=5
mms.billing.featureteam.lineitem.usagedate.size=5
mms.billingImport.azure.useLegacyBillingReportDate=true
mms.metering.featureteam.usage.rateLimitPerMinute=30
mms.metering.featureteam.usage.meterid.size=50
mms.metering.featureteam.usage.groupid.size=5
mms.metering.featureteam.usage.reporteddate.size=5
mms.metering.featureteam.usage.startdate.size=5
mms.payments.chargePayments.maxConcurrency=64
mms.payments.chargePayments.invoiceLock.retries.maxAttempts=20
mms.payments.chargePayments.invoiceLock.retries.minBackoff=2000
mms.payments.chargePayments.invoiceLock.retries.maxBackoff=15000
mms.payments.opportunityChange.auth.jira.uri=
mms.payments.opportunityChange.auth.jira.privateAccessToken=
mms.payments.revrec.email.selfServe.recipientAddress=<EMAIL>
mms.payments.revrec.email.salesSold.recipientAddress=<EMAIL>
mms.payments.revrec.email.subjectPrefix=GOV
mms.payments.revrec.maxConcurrency=256
mms.payments.revrec.query.maxConcurrency=40
mms.payments.revrec.query.batchSize=250
mms.payments.yaypay.baseUrl=
mms.payments.yaypay.inc.clientKey=
mms.payments.yaypay.inc.clientSecret=
mms.payments.yaypay.inc.scope=
mms.payments.yaypay.ltd.clientKey=
mms.payments.yaypay.ltd.clientSecret=
mms.payments.yaypay.ltd.scope=
mms.payments.yaypay.brazil.clientKey=
mms.payments.yaypay.brazil.clientSecret=
mms.payments.yaypay.brazil.scope=
mms.payments.internalNetwork.maxRequestsPerSecond=100
mms.payments.vercel.api.maxRetries=5
mms.payments.vercel.api.minBackoffMillis=2000
mms.payments.vercel.billing.maxRetries=5
mms.payments.vercel.billing.minBackoffMillis=2000
mms.payments.vercel.billing.accessTokenKey=
mms.centralUrl=https://cloud-qa.mongodbgov.com
mms.centralDomainAllowedOrigin=https://cloud-qa.mongodbgov.com
mms.cloudManagerEnabled=false
mms.cron.enabled=false
mms.cron.track.ttlDays=90
mms.dao.capped.apiRequestTrackDao.sizeInBytes=52428800
mms.dao.capped.default.sizeInBytes=1048576
mms.dao.capped.metricAlertDispatchCounterDao.sizeInBytes=10485760
mms.dao.capped.mlabSharedMigrationLogDao.sizeInBytes=524288000
mms.dao.capped.ndsMongoMirrorLogDao.sizeInBytes=524288000
mms.dao.capped.ndsTenantUpgradeLogDao.sizeInBytes=1048576000
mms.databaseIdpDiscovery.enabled=true
mms.databaseIdpDiscovery.mongoOktaId=0oawq91yiP5kvFi5f356
mms.dataExplorer.rateLimit=500
mms.docsUrl.nds=https://www.mongodb.com/docs/atlas
mms.docsUrl.search=https://www.mongodb.com/docs/search
mms.docsUrl=https://www.mongodb.com/docs/cloud-manager
mms.email.fromAddr.alerts=MongoDB Atlas for Government <<EMAIL>>
mms.email.fromAddr.support=MongoDB Atlas for Government <<EMAIL>>
mms.email.includeAppEnvName=true
mms.email.includeHostname=true
mms.email.logo.force=true
mms.email.logo.height=29
mms.email.logo.width=120
mms.email.logo=/static/images/logo-mongodb-cloud-gov-email.png
mms.email.recipientFilters=^mms\.servercukes-.*@mongodb\.com$,^ops-manager-team.*@mongodb\.com$,@domain\.com$
mms.email.replyAddr.alerts=<EMAIL>
mms.email.replyAddr.support=<EMAIL>
mms.email.validation=STRICT
mms.emailDaoClass=com.xgen.cloud.email._private.dao.AwsEmailDao
mms.emailVerificationEnabled=false
mms.event.sns.awsAccessKey=
mms.event.sns.awsSecretKey=
mms.event.sns.enabled=false
mms.event.errorOnValidationFailure=false
mms.featureFlag.automationChangesWebClient=disabled
mms.featureFlag.atlasProvideHardcodedOidcIdpInformation=controlled
mms.featureFlag.atlasAzureSsdPV2=disabled
mms.featureFlag.atlasAzureSsdPV2Wave2=disabled
mms.featureFlag.atlasAzureSsdPV2EnablePreviewRegions=disabled
mms.featureFlag.atlasAzureSsdForcePV1=disabled
mms.featureFlag.clusterCentricPerformanceAdvisor=enabled
mms.featureFlag.clusterCentricQueryProfiler=controlled
mms.featureFlag.namespaceInsights=controlled
mms.featureFlag.updateMetricsUiForEmbeddedConfigServers=enabled
mms.featureFlag.queryInsights=disabled
mms.featureFlag.atlasOptOutPeriodicCorruptionDetection=controlled
mms.feature.enablePauseFreeTierMonitoring=true
mms.featureFlag.adfa.allowRoutingViaCRMInsteadOfEnvoy=enabled
mms.featureFlag.adl.readConcernMajority=controlled
mms.featureFlag.adl.schemaUniqueFieldLimit=controlled
mms.featureFlag.adl.templateRegexGenerationOptimization=controlled
mms.featureFlag.adl.testManualControlledFlag=controlled
mms.featureFlag.adl.testAutomatedControlledFlag=controlled
mms.featureFlag.adl.testEnabledFlag=enabled
mms.featureFlag.adl.testDisabledFlag=disabled
mms.featureFlag.adl.useMongodInsteadOfMqlrun=enabled
mms.featureFlag.alertStateFlappingDetection=controlled
mms.featureFlag.atlasAllowDeprecatedVersions=controlled
mms.featureFlag.atlasMongoDB80=enabled
mms.featureFlag.atlasAutoApplyIndexesForMTMs=controlled
mms.featureFlag.atlasAzureNVMe=disabled
mms.featureFlag.atlasCrossCloudReplication=disabled
mms.featureFlag.atlasCrossRegionPeering=controlled
mms.featureFlag.atlasDataFederationTemplatedOnboarding=enabled
mms.featureFlag.atlasDataFederationAzureFeedDownstreamSystems=disabled
mms.featureFlag.atlasDataFederationAzurePrivateLink=controlled
mms.featureFlag.atlasDataRegionalization=disabled
mms.featureFlag.atlasDataRegionalizationGroup=disabled
mms.featureFlag.atlasDeploysUIS=disabled
mms.featureFlag.atlasSearchResolveViews=disabled
mms.featureFlag.atlasGatewayProxy=controlled
mms.featureFlag.atlasMaintenanceWindows=controlled
mms.featureFlag.atlasMountAzureDiskWithLun=disabled
mms.featureFlag.atlasAzureForceMigrationToAZs=controlled
mms.featureFlag.atlasServerlessGRPCSupport=disabled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettings=disabled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettingsOrg=disabled
mms.featureFlag.atlasProxyDisableRateLimiting=disabled
mms.featureFlag.atlasServerlessUsesServerlessAgent=disabled
mms.featureFlag.atlasEnableTestCommands=controlled
mms.featureFlag.atlasSharedTierX509Auth=controlled
mms.featureFlag.atlasX509CRL=controlled
mms.featureFlag.atlasSearch.dedicatedNodes=disabled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreference=disabled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreferenceGroupOverride=controlled
mms.featureFlag.atlasSearch.synchronousSteadyStateReplication=disabled
mms.featureFlag.atlasSearch.indexManagement=disabled
mms.featureFlag.atlasSearch.indexPartitioningUI=disabled
mms.featureFlag.atlasSearch.vibCustomAnalyzersUI=disabled
mms.featureFlag.atlasSearch.confCallViaSearchGateway=disabled
mms.featureFlag.atlasSearch.disableDetailedStatusWrite=controlled
mms.featureFlag.atlasSearch.searchTesterQueryTemplates=disabled
mms.featureFlag.atlasSearch.searchIndexStatusReporting=disabled
mms.featureFlag.atlasSearch.dedicatedNodesAWSSeriesSeven=disabled
mms.featureFlag.atlasSearch.dedicatedNodesEnableReplicationCompression=disabled
mms.featureFlag.atlasSearch.dedicatedNodesForceDisableReplication=disabled
mms.featureFlag.atlasSearch.dedicatedNodesDisableProvisionTimeout=controlled
mms.featureFlag.atlasSearch.useAggregateCommand=disabled
mms.featureFlag.atlasSearch.multipleSubIndexes=controlled
mms.featureFlag.atlasSearch.staleIndexes=enabled
mms.featureFlag.atlasSearch.byok=disabled
mms.featureFlag.atlasSearch.useLifecycleManager=enabled
mms.featureFlag.atlasSearch.envoy.disableStreamIdleTimeout=controlled
mms.featureFlag.atlasSearch.envoy.loadBalancingPolicyRandom=disabled
mms.featureFlag.atlasSearch.indexConfigStatsWrites=disabled
mms.featureFlag.atlasSearch.searchHostStatsWrites=disabled
mms.featureFlag.atlasSearch.readIndexStatsFromNewCollection=disabled
mms.featureFlag.atlasSearch.enableTextOperatorNewSynonymsSyntax=controlled
mms.featureFlag.atlasSearch.indexConfigUpdater=disabled
mms.featureFlag.atlasSearch.disableMongotJvmNativeAccess=enabled
mms.featureFlag.atlasSearch.indexQuantizationUI=disabled
mms.featureFlag.atlasSearch.mongotPushBasedLogExportDownload=disabled
mms.featureFlag.atlasSearch.dedicatedNodesGrpcMode=controlled
mms.featureFlag.atlasDataLakeStorageForOnlineArchive=disabled
mms.featureFlag.atlasDataLakeStorageForceV2UpgradeForOnlineArchive=disabled
mms.featureFlag.atlasDataLakeIngestionPipelinesDeprecation=disabled
mms.featureFlag.atlasDataLakeStorageAllowHourlyIngestion=controlled
mms.featureFlag.atlasDataLakeStorageDatasetRetentionPolicy=controlled
mms.featureFlag.atlasDataFederationDedicatedHostnames=controlled
mms.featureFlag.atlasDataFederationOnGCP=disabled
mms.featureFlag.atlasCloudProviderAccessForGCP=disabled
mms.featureFlag.atlasOnlineArchiveV3TimeSeries=disabled
mms.featureFlag.atlasOnlineArchiveGCP=disabled
mms.featureFlag.atlasOnlineArchiveV1=disabled
mms.featureFlag.atlasOnlineArchiveAsDataSource=disabled
mms.featureFlag.atlasOnlineArchiveIgnoreDataSizeThreshold=disabled
mms.featureFlag.atlasOnlineArchiveEncryption=controlled
mms.featureFlag.atlasOnlineArchivePinToPrimaryRegion=disabled
mms.featureFlag.atlasPushBasedLogExport=controlled
mms.featureFlag.atlasPushBasedLogExportClusterLevel=disabled
mms.featureFlag.atlasGovGcpAssuredWorkloads=enabled
mms.featureFlag.atlasUseDataExplorerService=controlled
mms.featureFlag.atlasUseProviderSubdomains=disabled
mms.featureFlag.atlasUseLegacyHostnameScheme=disabled
mms.featureFlag.dbAccessDuringTenantUpgradeBlocked=enabled
mms.featureFlag.dbAccessDuringTenantUpgradeAllowed=controlled
mms.featureFlag.atlasMultiCloudGlobalClusters=disabled
mms.featureFlag.atlasMaintenanceAutoDeferral=controlled
mms.featureFlag.atlasCNRegionsOnly=disabled
mms.featureFlag.atlas50Cluster=controlled
mms.featureFlag.atlasChurnSurvey=disabled
mms.featureFlag.atlasHideQuickAccessPage=controlled
mms.featureFlag.atlasDeferServerNodeTypeTagUntilPhase2=enabled
mms.featureFlag.atlasDailyBigQueryBilling=enabled
mms.featureFlag.atlasHaltGroupLogIngestion=controlled
mms.featureFlag.atlasForceSkipFastProvision=enabled
mms.featureFlag.atlasAutoRetrieveDraftClusterConfig=controlled
mms.featureFlag.awsGraviton=disabled
mms.featureFlag.awsIntelOverGraviton=disabled
mms.featureFlag.gcpInstanceOSAL2=enabled
mms.featureFlag.singleTargetServerlessDeployment=controlled
mms.featureFlag.streamsAuditLogs=disabled
mms.featureFlag.streamsEnabled=disabled
mms.featureFlag.autoIndexing=controlled
mms.featureFlag.allowVulnerabilityScanning=controlled
mms.featureFlag.automation.changeListener=disabled
mms.featureFlag.automation.configPublishChangeEvent=disabled
mms.featureFlag.automation.configForAgentReturnsOnlyLocal=enabled
mms.featureFlag.automation.chefConfigChangeEvent=disabled
mms.featureFlag.automation.exposeEditorAutofill=enabled
mms.featureFlag.automation.mongoDevelopmentVersions=enabled
mms.featureFlag.automation.sentry=disabled
mms.featureFlag.automation.staggerMongosRollingRestart=controlled
mms.featureFlag.automation.newDeploymentServerUI=enabled
mms.featureFlag.automation.rollingIndexes=controlled
mms.featureFlag.automation.verifyDownloads=enabled
mms.featureFlag.automation.proxyConfigChangeEvent=disabled
mms.featureFlag.automation.enableV6=enabled
mms.featureFlag.automation.enableV7=enabled
mms.featureFlag.automation.enableV8=enabled
mms.featureFlag.automation=controlled
mms.featureFlag.automation.enableRamiAgent=disabled
mms.featureFlag.automation.enableMongotune=disabled
mms.featureFlag.mongotune.enableWriteBlockPolicy=disabled
mms.featureFlag.payments.partners.aws.usageReportCreation=controlled
mms.featureFlag.payments.partners.azure.usageReportCreation=controlled
mms.featureFlag.payments.partners.gcp.usageReportCreation=controlled
mms.featureFlag.payments.salesSoldWarningOrgPaymentStatus=enabled
mms.featureFlag.payments.standaloneCalls.revRec=disabled
mms.featureFlag.awsPrivateEndpoint.waitingForUser=controlled
mms.featureFlag.baas=enabled
mms.featureFlag.baas.billingMigration=disabled
mms.featureFlag.baas.hostingDeprecated=controlled
mms.featureFlag.baas.graphqlDeprecated=controlled
mms.featureFlag.backup.allowEditOplogWindow=controlled
mms.featureFlag.backup.allowWhitelists=enabled
mms.featureFlag.backup.continuousBackupAllowedForNewAWSClusters=controlled
mms.featureFlag.backup.cpsResurrectWithRetainedBackups=enabled
mms.featureFlag.backup.cpsBackupLockMVP=controlled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGA=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGADisablePolicy=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicy2PersonDisablement=controlled
mms.featureFlag.backup.cpsConcurrentSnapshots=enabled
mms.featureFlag.backup.cpsDirectAttach=enabled
mms.featureFlag.backup.cpsDirectAttachOff=controlled
mms.featureFlag.backup.cpsEmbeddedConfig=enabled
mms.featureFlag.backup.cpsEmbeddedConfigUi=enabled
mms.featureFlag.backup.cpsExtendedSnapshotRetry=controlled
mms.featureFlag.backup.cpsExtraOpenBackupCursorTimeout=controlled
mms.featureFlag.backup.useNoWholeFileRsync=controlled
mms.featureFlag.backup.cpsNoWholeFileRsyncOff=controlled
mms.featureFlag.backup.cpsRestoreSearchIndex=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAws=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsMigration=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsNewCMK=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectGCP=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAzure=disabled
mms.featureFlag.backup.cpsOptimizedDaRestoreAws=controlled
mms.featureFlag.backup.cpsOptimizedDaRestoreAzure=controlled
mms.featureFlag.backup.cpsOptimizedDaRestoreGcp=controlled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAws=enabled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAzure=enabled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapGcp=enabled
mms.featureFlag.backup.cpsAwsDaRestoreWithIo2=controlled
mms.featureFlag.backup.cpsDownloadEncryptedSnapshot=enabled
mms.featureFlag.backup.cpsGcpAndAzureNewClustersOnlyCps=controlled
mms.featureFlag.backup.cpsPv2InstantRestoreOff=disabled
mms.featureFlag.backup.cpsPv2StreamingRestore=disabled
mms.featureFlag.backup.cpsSnapshotExportHighFrequency=controlled
mms.featureFlag.backup.cpsSnapshotExportUi=controlled
mms.featureFlag.backup.cpsSnapshotExportAzure=controlled
mms.featureFlag.backup.cpsSnapshotConsistentExport=controlled
mms.featureFlag.backup.cpsSnapshotConsistentExportSplitLargeCollections=enabled
mms.featureFlag.backup.cpsSnapshotDistributionAws=enabled
mms.featureFlag.backup.cpsSnapshotDistributionGcp=enabled
mms.featureFlag.backup.cpsSnapshotDistributionAzure=controlled
mms.featureFlag.backup.cpsSnapshotDistributionUi=enabled
mms.featureFlag.backup.cpsSnapshotDistributionLargeRegionSet=controlled
mms.featureFlag.backup.cpsGcpIncrementalCopySnapshots=disabled
mms.featureFlag.backup.cpsSnapshotAWSPrivateDownload=enabled
mms.featureFlag.backup.cpsSnapshotAzurePrivateDownload=controlled
mms.featureFlag.backup.turnCpsSnapshotAWSPrivateDownloadOff=controlled
mms.featureFlag.backup.turnCpsSnapshotAzurePrivateDownloadOff=controlled
mms.featureFlag.backup.cpsSkipSystemClusterDestroy=controlled
mms.featureFlag.backup.cpsSystemProjectsForExports=enabled
mms.featureFlag.backup.cpsSystemProjectsForDataLakeIngestionPipelineExports=controlled
mms.featureFlag.backup.cpsOplogInGcp=disabled
mms.featureFlag.backup.cpsOplogInGcpExtendMigration=disabled
mms.featureFlag.backup.cpsOplogMigration=enabled
mms.featureFlag.backup.cpsOplogInAzure=controlled
mms.featureFlag.backup.cpsSuccessiveUpgradeQuarterly=enabled
mms.featureFlag.backup.cpsLegacyBackupMigration=enabled
mms.featureFlag.backup.incrementalWtEnabled=controlled
mms.featureFlag.backup.incrementalWtFullSnapshotDayOfWeek=controlled
mms.featureFlag.backup.multipleWorkersPerFile=enabled
mms.featureFlag.backup.parallelRestores=controlled
mms.featureFlag.backup.directS3Restore=controlled
mms.featureFlag.backup.queryable=enabled
mms.featureFlag.backup.queryableFsCache=enabled
mms.featureFlag.backup.s3OplogStoreInOm=controlled
mms.featureFlag.backup.storageEngine=enabled
mms.featureFlag.backup.thirdPartyManaged=disabled
mms.featureFlag.backup.thirdPartyWithManagedOplog=disabled
mms.featureFlag.backup.v2=enabled
mms.featureFlag.backup.wt.queryable=enabled
mms.featureFlag.backup.wtBackpressure=controlled
mms.featureFlag.backup.wtConcurrentGrooms=enabled
mms.featureFlag.backup.wtConcurrentMongoBlockstoreGrooms=disabled
mms.featureFlag.backup.wtEncryptionAtRest=controlled
mms.featureFlag.backup.wtLocalKeyFile=disabled
mms.featureFlag.backup.wtLocalKeyFile.forOrg=controlled
mms.featureFlag.backup.wtNamespaceFiltering=enabled
mms.featureFlag.backup.enableAWSPrivateLinkOption=disabled
mms.featureFlag.backup.optimalSuccessiveUpgradeForRestoreEnabled=controlled
mms.featureFlag.backup.pGzipEnabled=enabled
mms.featureFlag.backup.snapshot.onDemand=disabled
mms.featureFlag.biConnector=controlled
mms.featureFlag.charts=enabled
mms.featureFlag.chartsActivationOptimization=controlled
mms.featureFlag.classicApiAccess=controlled
mms.featureFlag.classicCharts=controlled
mms.featureFlag.classicDashboard=controlled
mms.featureFlag.clientMetadataCollection=controlled
mms.featureFlag.clusterReferencesAutocorrect=enabled
mms.featureFlag.commentServiceEnabled=disabled
mms.featureFlag.crossOrgBilling=enabled
mms.featureFlag.customerFederation=enabled
mms.featureFlag.pagerduty.includeMaintenanceWindowStatus=controlled
mms.featureFlag.dataExplorer=controlled
mms.featureFlag.dataExplorerAggregation=controlled
mms.featureFlag.dataExplorerCrud=controlled
mms.featureFlag.dataExplorerMultiTenant=controlled
mms.featureFlag.extendMaxAllowedDiskSizes=disabled
mms.featureFlag.hostMappingsAutocorrect=controlled
mms.featureFlag.installAgentScript=controlled
mms.featureFlag.managedSlowMs=controlled
mms.featureFlag.m10ShardedClusters=enabled
mms.featureFlag.mongoDBAccessHistory=controlled
mms.featureFlag.monitoring.dataDogMetrics=controlled
mms.featureFlag.monitoring.dataDogDbAndCollMetrics=disabled
mms.featureFlag.monitoring.newRelic=enabled
mms.featureFlag.monitoring.shardedClusterNamespaceMetrics=controlled
mms.featureFlag.monitoring.namespaceQueryLatencyMetrics=controlled
mms.featureFlag.monitoring.skipUpdateClustersDuringLiveMigration=disabled
mms.featureFlag.monthlyUsageMetricsEmail=controlled
mms.featureFlag.ndsClusters=controlled
mms.featureFlag.billingDistributedLinkedOrgBilling=enabled
mms.featureFlag.billing.testMeterDataGeneration=controlled
mms.featureFlag.billingClusterTaggingExport=controlled
mms.featureFlag.disablePremiumDatadog=controlled
mms.featureFlag.serverlessAutoIndexing=enabled
mms.featureFlag.enableFineGrainedAuth=disabled
mms.featureFlag.fineGrainedAuth.userGroups=disabled
mms.featureFlag.serviceAccountManagement=disabled
mms.featureFlag.enableScramSha256Auth=disabled
mms.featureFlag.logcollectionS3Backend=disabled
mms.featureFlag.maxAggregatedDiskChartsForAtlas=controlled
mms.featureFlag.opLatency=enabled
mms.featureFlag.opsManagerConfigForGlobalMonitoringAdmin=enabled
mms.featureFlag.paBugsAndSustainability=controlled
mms.featureFlag.paFeedback=controlled
mms.featureFlag.performanceAdvisor=controlled
mms.featureFlag.historicalReplicaState=controlled
mms.featureFlag.profilerNDS=controlled
mms.featureFlag.profilerv2=controlled
mms.featureFlag.profilerv3=controlled
mms.featureFlag.prometheus=disabled
mms.FeatureFlag.prometheus.allowCustomerRateLimitConfig=controlled
mms.featureFlag.realtime=controlled
mms.featureFlag.realmMetrics=disabled
mms.featureFlag.realmMetricsAlerts=disabled
mms.featureFlag.rsyncBasedHeadCreation=disabled
mms.featureFlag.schemaAdvisor=controlled
mms.featureFlag.segmentGroupSynchronizer=controlled
mms.featureFlag.selfServeConsultingUnits=enabled
mms.featureFlag.standardFreeTier=controlled
mms.featureFlag.teams=controlled
mms.featureFlag.unknown=controlled
mms.featureFlag.atlasContinuousDelivery=enabled
mms.featureFlag.realmEvents=disabled
mms.featureFlag.realm.syncBeta=controlled
mms.featureFlag.failedRollingIndexCleanup=enabled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToSingleShard=disabled
mms.featureFlag.atlasLiveMigrateShardedHorizons=controlled
mms.featureFlag.atlasLiveMigrateOlderVersionSupport=enabled
mms.featureFlag.atlasLiveMigrateOneNinePreviewSupport=disabled
mms.featureFlag.atlasLiveMigrateLiftOplogLagRestriction=disabled
mms.featureFlag.atlasLiveMigrateRunsOnlyOnAL2Hosts=enabled
mms.featureFlag.atlasMigrationHubMilestoneOne=enabled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToShardedCluster=enabled
mms.featureFlag.atlasLiveMigrateMongosyncPushBasedReplicasetToShardedCluster=disabled
mms.featureFlag.atlasPushLiveMigrationsMongosyncPrivateEndpoints=disabled
mms.featureFlag.atlasLiveMigrateMongosyncVariableShardCount=enabled
mms.featureFlag.atlasLiveMigrateMongosyncEmbeddedVerifier=enabled
mms.featureFlag.atlasLiveMigrateDestinationOplogValidationForVerifier=enabled
mms.featureFlag.optOutFromKinesis=enabled
mms.featureFlag.performanceAdvisorRecommendSearch=controlled
mms.featureFlag.uiAccessList=controlled
mms.featureFlag.tokenizedQueryShapeStatsMetrics=disabled
mms.featureFlag.atlasSearchQueryTelemetry=controlled
mms.featureFlag.randomSamplingSlowLogStreamer=enabled
mms.featureFlag.datadogCustomEndpoint=controlled
mms.featureFlag.iaSlowLogsReads=disabled
mms.featureFlag.allowGCPPreviewRegions=disabled
mms.featureFlag.allowAWSPreviewRegions=disabled
mms.featureFlag.allowAzurePreviewRegions=disabled
mms.featureFlag.atlasDedicatedBackboneToReact=enabled
mms.featureFlag.atlasDedicatedReactClusterDetails=controlled
mms.featureFlag.encryptionAtRestAzureKeyVaultPrivateEndpoint=enabled
mms.featureFlag.encryptionAtRestAwsKmsPrivateEndpoint=enabled
mms.featureFlag.earGcpKmsRoleBasedAuth=disabled
mms.featureFlag.atlasAllowAtlasAdminFourFourPrivileges=controlled
mms.featureFlag.apix.enableApiTelemetryCustomFields=disabled
mms.featureFlag.connectionEstablishmentRateLimiting=enabled
mms.federation.restrictOrgMembership.default=true
mms.federation.bypassSsoDomains=
mms.enableOidcIdpNonAtlas=false
mms.featureFlag.privateLinkProxyProtocolAws=controlled
mms.featureFlag.privateLinkMaxIncomingConnectionsMultiplier=controlled
mms.fromEmailAddr=MongoDB Cloud <<EMAIL>>
mms.globalRoles.mapping=gov
mms.group.tags.edit.roleRequired=GLOBAL_MONITORING_ADMIN
mms.group.tags.read.roleRequired=GLOBAL_READ_ONLY
mms.group.useSoftDelete=false
mms.groupType.default=CLOUD
mms.helpAndSupportPage.enabled=true
mms.https.ClientCertificateMode=none
mms.limits.maxAlertConfigsPerGroup=250
mms.limits.maxApiUsersPerOrg=500
mms.limits.maxGroupsPerOrg=250
mms.limits.maxGroupsPerUser=500
mms.limits.maxNotificationsPerAlert=25
mms.limits.maxOrgsPerUser=**********
mms.limits.maxTeamsPerGroup=100
mms.limits.maxTeamsPerOrg=250
mms.limits.maxTeamsPerUser=100
mms.limits.maxUsersPerGroup=500
mms.limits.maxUsersPerOrg=500
mms.limits.maxUsersPerTeam=250
mms.limits.maxExportsPerGroup=100
mms.limits.maxM0sPerUnverifiedOrg=10
mms.limits.maxServiceAccountsPerOrg=500
mms.limits.maxCustomPoliciesPerOrg=100
mms.limit.maxNetworkAddressesPerServiceAccount=200
mms.limit.groupSearchMaxNumOfReturnAllowed=3000
mms.login.newDeviceNotification.enabled=false
mms.marketing.sync=false
mms.monitoring.agent.session.timeoutMillis=90000
mms.monitoring.agent.standbyCollectionFactor=2
mms.featureFlag.monitoring.canonicalHostsTTLFilteredReads.enabled=enabled
mms.featureFlag.monitoring.canonicalHostsTTLWrites.enabled=enabled
mms.featureFlag.resourceTagComponent.clusters=enabled
mms.featureFlag.resourceTagComponent.projects=enabled
mms.featureFlag.enableExperimentHoldouts=disabled
mms.monitoring.canonicalHostsTTLMinutes=1440
mms.monitoring.canonicalHostsTTLWrites.probabilisticPercentage=0
mms.monitoring.canonicalHostsTTLWrites.guaranteedPercentage=50
mms.monitoring.chartDomain=
mms.monitoring.historicalReplicaState.ingestionPercentage=0
mms.monitoring.ingestion.bucketedMetricRollup.enabled=true
mms.monitoring.ingestion.bucketedMetricRollup.bucketCount=2
mms.monitoring.ingestion.bucketedMetricRollup.serverlessBucketCount=2
mms.monitoring.ingestion.bucketedMetricRollup.realmBucketCount=2
mms.monitoring.ingestion.munin.poolsize=2
mms.monitoring.ingestion.newrelic.guid=com.mongodb.monitoring.integrations.newrelic.cloudmanager.dev
mms.monitoring.ingestion.newrelic.version=1.0.0
mms.monitoring.ingestion.readPreference=secondaryPreferred
mms.monitoring.performanceadvisor.iterative.maxLogLines=200000
mms.monitoring.performanceadvisor.textQueryThreshold=300
mms.monitoring.discovery.secondaryReads.enabled=true
mms.monitoring.realm.metrics.api.url=
mms.monitoring.rrd.realmMaintenanceEnabled=false
mms.monitoring.rrd.realmNumInitialChunks=10
mms.monitoring.rrd.ftsMaintenanceEnabled=false
mms.monitoring.rrd.ftsNumInitialChunks=10
mms.monitoring.rrd.serverlessMaintenanceEnabled=true
mms.monitoring.slowlogs.ingestion.parsingPercentage=0
mms.mana.http.socketTimeout=10000
mms.mana.http.connectTimeout=10000
mms.mana.http.url=http://mana-http.mana-qa.us-gov-west-1.aws.cloud-gov-qa.kube:5000/alohomora
mms.mana.isUsingInternalMeshNetwork=true
mms.mana.clientId=<SECRETMANAGER>
mms.mana.clientSecret=<SECRETMANAGER>
mms.mana.authToken=<SECRETMANAGER>
mms.multiFactorAuth.allowReset=true
mms.multiFactorAuth.issuer=MongoDB-QA
mms.multiFactorAuth.level=OFF
mms.multiFactorAuth.encouragement.enabled=true
mms.multiFactorAuth.encouragement.enabledForNoFactors=true
iam.multiFactorAuth.requiredAfterRegistration=false
iam.multiFactorAuth.requiredForAllUsers=true
iam.multiFactorAuth.rememberDevice.enabled=true
iam.partnerIntegrations.vercelNative.webhook.clientSecrets=
iam.partnerIntegrations.vercelNative.openInProvider.clientId=
iam.partnerIntegrations.vercelNative.openInProvider.clientSecret=
mms.personalizationWizardEnabled=true
mms.personalizationWizardRedirectEnabled=true
mms.darkMode.preview.enabled=false
mms.cloudNav.override.enabled=true
mms.ofac.enabled=true
mms.ofac.notificationEmailAddr=
mms.ofac.supportEmailAddr=<EMAIL>
mms.plan.default=FREE_TIER
mms.publicApi.allowUnauthRegistrations=true
mms.publicApi.globalKeyRoleDenylist=GLOBAL_OWNER
mms.publicApi.maxTempKeys=100
mms.pushLiveMigrations.enabled=false
mms.pushLiveMigrations.mongoClient.validations.enabled=false
mms.pushLiveMigrations.mmsUi.defaultAccessListCidr=10.0.0.0/8
mms.pushLiveMigrations.mmsUi.serviceMeshEnvVar=MESH_MMS_UI_QA_GOV_US_GOV_WEST_1_AWS_CLOUD_GOV_QA_10GEN_CC
mms.remoteIp.header=X-Cluster-Client-Ip
mms.replyToEmailAddr=<EMAIL>
mms.replyToSuspensionAdminEmailAddr=<EMAIL>
mms.root.redirect=/user/login
mms.search.envoy.location=https://mongodb-mms-build-envoy-serverless.s3.amazonaws.com/tarballs/
mms.search.offlineUpgradePackages=1.38.1.2120
mms.search.offlineDowngradePackages=1.41.0
mms.search.commonLabelsEnabledTiers=
mms.search.staleEnabledTiers=MTM
mms.search.incubatorVectorEnabledTiers=ALL
mms.search.apiRateLimitRules=
mms.security.allowCORS=true
mms.security.backdoor=true
mms.security.cors.allow.localhost.origin=false
mms.security.disableBrowserCaching=false
mms.security.enforceLastAuthMins=false
mms.security.globalWhitelist=seed-data/GlobalWhitelistDao-qa.json
mms.security.hstsMaxAgeSeconds=31536000
mms.security.redactSecretsInStoredMessages=false
mms.security.requireCSRFToken=true
mms.server.sentry.dsn=
mms.server.sentry.enabled=false
mms.server.sentry.sampleRate=0.1
mms.server.sentry.apiKey=
mms.serverless.xDSServer.enabled=false
mms.serverless.xDSServer.tlsRequired=
mms.serverless.xds.error.log.alert.test.enabled=false
mms.session.authn.tokens.filter.enabled=true
mms.session.authn.tokens.minting.enabled=true
mms.session.maxConcurrent=3
mms.session.maxHours=12
iam.session.idleTestingMode.enabled=true
mms.session.idleTimeoutMinutes=720
mms.session.customSessionTimeouts.enabled=false
mms.siteFullName.nds=MongoDB Atlas for Government
mms.siteName.nds=Atlas Government
mms.snapshotQueryHost=queryable-backup-qa.mongodbgov.com:27217
mms.staleAuthCodeLimit=5
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.maxHitsPerPeriod=50
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.minutesPeriod=1
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.maxHitsPerPeriod=20
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.minutesPeriod=1
mms.testUtil.enabled=true
mms.ui.mongonav.env=government
mms.usageData.awsAccessKey=<SECRETMANAGER>
mms.usageData.awsSecretKey=<SECRETMANAGER>
mms.usageData.collection=false
mms.usageData.ingestion=false
mms.usageData.pgp.publicKey=/mongodb-inc-opsmanager.pub.asc
mms.usageData.pgp.secretKey=/mongodb-inc-opsmanager.private.asc
mms.usageData.s3.bucket=mongodb-datawarehouse-qa
mms.user.bypassInviteForExistingUsers=false
mms.user.invitationOnly=true
mms.user.invitationOnly.redirectUrl=https://www.mongodb.com/cloud/atlas/government
mms.user.redirectIfPasswordIsEmpty=false
mms.user.registration.defaultCountry=US
mms.user.registration.extraFields=company
mms.user.username.restrictedDomains=legacyusername[.]mongodb[.]com,invalidusername[.]mongodb[.]com
mms.userSvcClass=UserSvcOkta
mms.vercel.clientId=
mms.vercelIntegration.enabled=false
mms.vercel.secret=
mms.versionMismatch.detect=false
mms.versionMismatch.pingInterval.unit=SECONDS
mms.versionMismatch.pingInterval=10
mms.versionMismatch.pingInterval.staleCount=60
mms.versionToDeprecate=6.0
mms.forcedDeprecatedVersionUpgradeTargets=7.0,8.0
mms.versionDeprecatedByDate=2025-07-31
mms.enableFutureEOLExtensionCheck=true
# V1 Degraded Experience Configuration
mms.v1.degradedExperience.phase1.enabled=false
mms.v1.degradedExperience.phase2.enabled=false
mms.v1.degradedExperience.shadowMode.enabled=true
mms.v1.degradedExperience.creationCutoffDate.org=2025-09-08T00:00:00.000Z
mms.v1.degradedExperience.creationCutoffDate.project=2025-09-08T00:00:00.000Z
mmsdbautomationlog.queuesize=10000
mmsdbautomationlog.writers=4
mmsdbserverlog.logs.enabled=false
mms.azure.ssdv2.regions=
mms.azure.ssdv2.rollout.regions=
mms.azure.ssdv2.preview.regions=
mongo.account.encryptedCredentials=
mongo.account.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.account.ssl=
mongo.atlasbackup.encryptedCredentials=
mongo.atlasbackup.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.atlasbackup.ssl=
mongo.automationcore.encryptedCredentials=
mongo.automationcore.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.automationcore.ssl=
mongo.automationstatus.encryptedCredentials=
mongo.automationstatus.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.automationstatus.ssl=
mongo.authz.encryptedCredentials=
mongo.authz.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.authz.ssl=
mongo.backupstatus.encryptedCredentials=
mongo.backupstatus.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.backupstatus.ssl=
mongo.backupusage.encryptedCredentials=
mongo.backupusage.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.backupusage.ssl=
mongo.cloudconf.encryptedCredentials=
mongo.cloudconf.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.cloudconf.ssl=
mongo.automationagentlogs/mmsdbautomation.encryptedCredentials=
mongo.automationagentlogs/mmsdbautomation.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.automationagentlogs/mmsdbautomation.ssl=
mongo.mmsdbbilling.encryptedCredentials=
mongo.mmsdbbilling.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&w=majority&retryReads=false
mongo.mmsdbbilling.ssl=
mongo.mmsdbcloudproviders.encryptedCredentials=
mongo.mmsdbcloudproviders.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false&waitQueueTimeoutMS=120000
mongo.mmsdbcloudproviders.ssl=
mongo.mmsdbcollstatslatencyconfig.encryptedCredentials=
mongo.mmsdbcollstatslatencyconfig.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbcollstatslatencyconfig.ssl=
mongo.realmmetricsstate/serverlesschartselect/metricsmisc/metricsmisc2/discovery/tokenization/controlledfeatures.encryptedCredentials=
mongo.realmmetricsstate/serverlesschartselect/metricsmisc/metricsmisc2/discovery/tokenization/controlledfeatures.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.realmmetricsstate/serverlesschartselect/metricsmisc/metricsmisc2/discovery/tokenization/controlledfeatures.ssl=
mongo.iam.encryptedCredentials=
mongo.iam.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.iam.ssl=
mongo.mmsalertconfig.encryptedCredentials=
mongo.mmsalertconfig.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsalertconfig.cutover.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsalertconfig.ssl=
mongo.mmsdbjobs.encryptedCredentials=
mongo.mmsdbjobs.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false&w=majority
mongo.mmsdbjobs.ssl=
mongo.realtimemeasurements/dataexplorer/logcollection.encryptedCredentials=
mongo.realtimemeasurements/dataexplorer/logcollection.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.realtimemeasurements/dataexplorer/logcollection.ssl=
mongo.mmsdbrrdcache/iadbpings.encryptedCredentials=
mongo.mmsdbrrdcache/iadbpings.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbrrdcache/iadbpings.ssl=
mongo.dbprofile.encryptedCredentials=
mongo.dbprofile.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.dbprofile.ssl=
mongo.atlaslogsmigration/mmsdbqueues.encryptedCredentials=
mongo.atlaslogsmigration/mmsdbqueues.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.atlaslogsmigration/mmsdbqueues.ssl=
mongo.mmsrt.encryptedCredentials=
mongo.mmsrt.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsrt.ssl=
mongo.monitoringdiagnostics/iaaccesslogs.encryptedCredentials=
mongo.monitoringdiagnostics/iaaccesslogs.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.monitoringdiagnostics/iaaccesslogs.ssl=
mongo.monitoringftstelemetry.encryptedCredentials=
mongo.monitoringftstelemetry.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.monitoringftstelemetry.ssl=
mongo.monitoringstatus.encryptedCredentials=
mongo.monitoringstatus.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.monitoringstatus.ssl=
mongo.sessions.encryptedCredentials=
mongo.sessions.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.sessions.ssl=
mongo.nds.encryptedCredentials=
mongo.nds.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.nds.ssl=
mongo.fts.encryptedCredentials=
mongo.fts.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.fts.ssl=
mongo.atlasndsstatus.encryptedCredentials=
mongo.atlasndsstatus.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.atlasndsstatus.ssl=
mongo.platformdiagnostics.encryptedCredentials=
mongo.platformdiagnostics.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.platformdiagnostics.ssl=
mongo.oa.encryptedCredentials=
mongo.oa.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.oa.ssl=
mongo.adl.encryptedCredentials=
mongo.adl.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.adl.ssl=
mongo.atlasbilling.encryptedCredentials=
mongo.atlasbilling.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.atlasbilling.ssl=
mongodb.release.autoDownload.development=true
mongodb.release.autoDownload.enterprise=true
mongodb.release.autoDownload.rc=true
mongodb.release.autoDownload=true
mongodb.release.modulePreference=enterprisePreferred
mongodb.release.publicKeyFile=classpath://mongodb-inc-server.pub
mongot.location=https://internal-downloads.mongodb.com/search/
mongotools.version=100.13.0
# `/prod/*` is the release bucket for mongotune binaries
mongotune.location=https://internal-downloads.mongodb.com/mongotune/prod/
mongosh.version=2.5.6
monitoring.agent.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/qa/
monitoring.agent.location.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/qa/
monitoring.agent.minimumVersion=5.0.0.309
monitoring.agent.version=7.2.0.488-1
morphia.search.packages=com.xgen.cloud.common.featureFlag._public.model,com.xgen.svc.mms.model.metrics.realtime,com.xgen.svc.mms.deployment.auth,com.xgen.svc.mms.model.performanceadvisor,com.xgen.cloud.activity._public.model.alert,com.xgen.cloud.activity._public.model.alert.config,com.xgen.svc.mms.model,com.xgen.svc.atm.model,com.xgen.svc.mms.model.performanceadvisor.autoindexing,com.xgen.cloud.atm.core._public.model,com.xgen.cloud.deployment._public.model,com.xgen.cloud.monitoring.topology._public.model,com.xgen.cloud.organization._public.model,com.xgen.cloud.atm.core._public.model.status,com.xgen.cloud.billingplatform.activity._public.alert,com.xgen.cloud.billingplatform.activity._public.alert.config,com.xgen.cloud.billingplatform.activity._public.audit,com.xgen.cloud.billingplatform.activity._public.event,com.xgen.cloud.billingplatform.audit._public.model,com.xgen.cloud.billingplatform.invoice._public.model,com.xgen.cloud.billingplatform.model.cloudprovider._public.aws,com.xgen.cloud.billingplatform.model.cloudprovider._public.azure,com.xgen.cloud.billingplatform.model.plan._public.model,com.xgen.cloud.dataexport._public.model,com.xgen.cloud.partners.registration._public.model,com.xgen.cloud.payments.netsuite._public.models,com.xgen.cloud.revrec.selfserve._private.model,com.xgen.svc.mms.model.billing,com.xgen.svc.mms.model.billing.reporting,com.xgen.svc.mms.model.marketing,com.xgen.cloud.common.explorer._public.model
nds.acme.accountDocument=
nds.acme.le.accountDocument=
nds.acme.gts.accountDocument=
nds.atlasproxy.conf.maxProcessedAccounts=100
nds.atlasproxy.conf.tlsPEMKeyFile=/etc/pki/tls/private/mongod.pem
nds.atlasproxy.restartProxyProcessOnFallbackCertRotation=true
nds.admin.reasonsRequireJira=false
nds.admin.dataPlaneAccessRequestsOnlineValidation=false
nds.admin.createRespectProtectedHoursMaintenanceAdminUi.enabled=true
nds.agent.envoyConfigServer.enabled=true
nds.agent.envoyConfigServer.nodeId=atlas-envoy
nds.agent.envoyConfigServer.port=9902
nds.agent.envoyConfigServer.adminPort=9901
nds.agent.envoyConfigServer.promExternalPort=27018
nds.agent.envoyConfigServer.promInternalPort=9945
nds.agent.envoyConfigServer.gatewayProxyExternalPort=27014
nds.agent.envoyConfigServer.mongotPromPort=9946
nds.agent.envoyConfigServer.searchEnvoyAdminPort=9905
nds.agent.envoyConfigServer.tlsCertConfigPath=/etc/envoy/sds.yaml
nds.agent.envoyConfigServer.tlsFilterServerName=*.mongodb-qa.net
nds.agentApiKey.allowUnrestricted=false
nds.autoScaling.compute.initiateScaleDownWindow.days=MONDAY,TUESDAY,WEDNESDAY,THURSDAY,FRIDAY,SATURDAY,SUNDAY
nds.autoScaling.compute.initiateScaleDownWindow.hours=00:00,23:59
nds.autoScaling.burstable.compute.newClusterEnabled=true
nds.aws.al2023.newGroups=false
nds.aws.billingReport.bucketName=mongodb-atlas-fedramp-billing
nds.aws.billingReport.name=daily-report
nds.aws.billingReport.prefix=custom-reports
nds.aws.billingReport.regionName=US_EAST_1
nds.aws.dataValidation.instanceSize=M10
nds.aws.dataValidation.min.diskSizeGB=1024
nds.aws.dns.domain=mongodbgov-qa.net
nds.aws.dns.hostedzoneid=Z08277131M7KZV10MQZXW
nds.azure.al2023.newGroups=false
nds.azure.dataValidation.instanceSize=M10
nds.azure.dataValidation.min.diskSizeGB=1024
nds.azure.dns.domain=azure.mongodb-qa.net
nds.backup.snapshots.expiration_extend_minutes=60
nds.backup.snapshots.restore.minimum_time_needed_for_restore_minutes=30
nds.backup.snapshots.aws.copySnapshotKmsKeyArnTemplate=arn:aws:kms:%s:691458415294:alias/cps-snapshot-copy
nds.backup.snapshots.aws.copySnapshotKmsGovKeyArnTemplate=arn:aws-us-gov:kms:%s:212373982077:alias/cps-snapshot-copy
nds.backup.snapshots.aws.fasterRestoreJobKmsKeyArnTemplate=arn:aws:kms:%s:%s:alias/cloud-qa-gov-volume-encryption-key
nds.backup.snapshots.aws.fasterRestoreJobKmsGovKeyArnTemplate=arn:aws-us-gov:kms:%s:%s:alias/cloud-qa-gov-volume-encryption-key
nds.criticalMaintenance.job.enabled=true
nds.corruptionDetection.dataValidation.dailySamplePercent=0.0
nds.corruptionDetection.dataValidation.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dataValidation.minClusterAgeDays=182
nds.corruptionDetection.dbCheck.dailySamplePercent=0.0
nds.corruptionDetection.dbCheck.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dbCheck.minClusterAgeDays=182
nds.corruptionDetection.checkMetadataConsistency.dailySamplePercent=0.0
nds.corruptionDetection.checkMetadataConsistency.minDaysBetweenClusterValidations=182
nds.corruptionDetection.checkMetadataConsistency.minClusterAgeDays=182
nds.connectionEstablishmentRateLimiting.newGroups=false
nds.dataValidation.maxWorkingRecords=10
nds.adminEmailAddress=<EMAIL>
nds.delete.after.hours=12
nds.externalcaching.redis.cacheEnabled=false
nds.gateway.proxy.enabled=false
nds.gcp.SnapshotBillingReport.dataset=mongodbgov_net_billing
nds.gcp.SnapshotBillingReport.projectId=atlas-billing-gov
nds.gcp.billingReport.bucketName=mongodb-net-billing-reports
nds.gcp.billingReport.prefix=mongodb-net-billing-
nds.gcp.dataValidation.instanceSize=M10
nds.gcp.dataValidation.min.diskSizeGB=1024
nds.gcp.dns.domain=gcp.mongodbgov-qa.net
nds.gcp.dns.hostedzoneid.crosscloud.private=Z01920562DGJX7TWQ2L51
nds.gcp.dns.hostedzoneid.private=Z04892241F0PJ126ATZM7
nds.gcp.dns.hostedzoneid.public=Z0384208IMUH0XA9PE9J
nds.gcp.projectCreate.count=500
nds.gcp.al2023.newGroups=false
nds.atlasPrioritizeHavingPrimaryOnUpscaledNode.newGroups=true
nds.privateLinkProxyProtocolAws.newGroups=true
nds.gov.us.enabled=true
nds.gov.us.siteName=Atlas Government
nds.gov.us.gcp.cmek.projectName=cloud-qa-gov-root
nds.healthCheck.staleJobThreshold.hours=1
nds.images.s3Bucket.aws.accesskey=<SECRETMANAGER>
nds.images.s3Bucket.aws.secretkey=<SECRETMANAGER>
nds.images.s3Bucket.aws.local-gov.accesskey=
nds.images.s3Bucket.aws.local-gov.secretkey=
nds.images.gcp.projectId=atlas-images-gov
nds.instances.debug=true
nds.instances.heracles.s3KeyId=<SECRETMANAGER>
nds.instances.heracles.s3SecretKey=<SECRETMANAGER>
nds.instances.heracles.s3Bucket=<SECRETMANAGER>
nds.instances.heracles.s3Region=<SECRETMANAGER>
nds.instances.heracles.keyring=mQENBGBHi5QBCAC0PKQHkZnhfbd7pfQtMEPWCMLI5DbkSZZvQD909dLsfRqYYijV7igUjulvE7eKATuKRRa7X+v2ZHmOBsHBB1FvCSuhawYjdlZAbYqhmRiFuwN9rEQLcfFcf1PhpO3GG33iUnuB5TjncanQ4TJ4dnbxMeOF2BCKAqNPXfa183ZnlBhtXFIVrdk6uWFkVmkrsCu4+34Ndf+lCcrJRtSczM7SpTNJpFUzLQixgVQHLv3VkhFq7psY12kUWHL+KbFNA5k+N5zVz/Z7c1DxKnmCp8FouyJm+CMJFTI17gjRBbTyQKvMv1HONENaRLetMpK8t8iXhp0pgrTmamu7jJcJ3/HhABEBAAG0D3NyZUBtb25nb2RiLmNvbYkBTgQTAQgAOBYhBDMp6PyrqCi2rsQxlpEuLUAXXRIqBQJgR4uUAhsDBQsJCAcCBhUKCQgLAgQWAgMBAh4BAheAAAoJEJEuLUAXXRIqmZkH/0MWYehhQ0dDo+lMknlGIrtsWWSQKiuEcrVVq9gNGSWpqPFkv5bi486wue5fbf6J9o7NzAag/xokUfzSKXbILJbcbFPvhw8P2qscRx6wchqrK9NtXFFvSzI3GM+xtZVRWR2X9WGWUV6Y8ck9L94G3wJtYjb1qPN1+mOzjSa3p/qkoevPCHtnaKu9b+fcrFP/i6LB0gvfHwTNPFig06DO4V3aznnaQXYbKTi2+OOfOLk72Xk1vW6HIzMi2L99bPVY1bMPRBHXE8q5HsREl9OZ8VST1GP6wQjNep/hbt+ZBcP+3nJ+p+57PH5ACLPlMTWpaqP+jYud1ACQQzlMjbEZLsE=
nds.instances.secret-names=aws-creds,aws,log-uploader-keys,repo-gateway,s3logwriter-keys,firehoselogwriter-keys,wildcard-cert-pem,tenable,clamav-db,deviceSyncDebugAccessSharedSecret
nds.instances.secrets.CN.wildcard-cert-pem=BLOCKED
nds.instances.secrets.aws.aws-creds=<SECRETMANAGER>
nds.instances.secrets.aws.aws=<SECRETMANAGER>
nds.instances.secrets.aws.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.aws.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.aws.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.us-gov-west-1.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.us-gov-east-1.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.firehoselogwriter-keys=
nds.instances.secrets.azure.aws-creds=<SECRETMANAGER>
nds.instances.secrets.azure.aws=<SECRETMANAGER>
nds.instances.secrets.azure.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.azure.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.azure.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.azure.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.gcp.aws-creds=<SECRETMANAGER>
nds.instances.secrets.gcp.aws=<SECRETMANAGER>
nds.instances.secrets.gcp.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.gcp.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.clamav-db=<SECRETMANAGER>
nds.instances.secrets.tenable=<SECRETMANAGER>
nds.instances.secrets.deviceSyncDebugAccessSharedSecret=
nds.instances.shared.version=8.0
nds.instances.serverless.size=SERVERLESS_V2
nds.instances.os.aws=AL2
nds.instances.provisioningLogUpload.enabled=true
nds.instances.provisioningLogUpload.bucket=atlas-provisioning-logs-us-gov-west-1-qa-gov
nds.instances.anomaliesBucket=atlas-anomalies-logs-us-gov-west-1-qa-gov
nds.jira.uri=
nds.jira.privateAccessToken=
nds.jira.bypassJiraStagingFirewallHeader=
nds.jira.bypassJiraStagingFirewallValue=
nds.leakedItem.cleanup.retries=3
nds.leakedItem.cleanup.mode=JOB_HANDLER
nds.leakedItem.detection.aws.mode=JOB_HANDLER
nds.leakedItem.aws.shutdownLeakedInstance=true
nds.leakedItem.aws.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.leakedItem.detection.azure.mode=PAUSED
nds.leakedItem.detection.gcp.mode=PAUSED
nds.leakedItem.detection.gcp.chunkSize=0
nds.leakedItem.detection.gcp.projectsProcessedPerIteration=0
nds.leakedItem.gcp.shutdownLeakedInstance=true
nds.leakedItem.gcp.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.leakedItem.azure.shutdownLeakedInstance=true
nds.leakedItem.azure.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.leakedItem.handler.enableDryRun=false
nds.liveImport.servers.hasPublicIp=false
nds.liveImport.oplogBufferDir=/srv/10gen/atlas-import-qa-gov/buffer
nds.liveImport.workingDir=/var/log/atlas-import-qa-gov
nds.liveImport.mongosync.version=1.15.0
nds.liveImport.mongosync.latestVersion=1.15.0
nds.pushLiveImport.mongosync.version=1.13.1
nds.liveImport.mongomirror.version=0.13.1
nds.liveImport.mongomirror.dockerImageTag=v0.13.1-202411011952-80adf99
nds.liveImport.kubeResourceCleaner.enabled=true
nds.liveImport.mongosyncWorkingDir=/var/log/atlas-live-import-qa-gov
nds.liveImport.sudoUser=atlas-import-qa-gov
nds.liveImport.mongosync.binaryPathPrefix=/opt/10gen/mongosync
nds.liveImport.mongosync.numInsertersPerPartition=4
nds.liveImport.mongosync.maxNumParallelPartitions=4
nds.liveImport.mongomirror.oplogBatchSize=1000
nds.mongosync.log.verbosity=DEBUG
nds.metrics.deliverySystemForBilling.enabled=false
nds.mongomirror.log.verbosity=3
nds.okta.oidc.testAuthorizationServer.apiKey=
nds.orphaned.disk.aws.keephours=24
nds.orphaned.disk.azure.keephours=24
nds.orphaned.disk.gcp.keephours=24
nds.orphaned.ip.keepHours=1
nds.payment.exemptUsernameRegex=mms\.servercukes-.*@mongodb\.com
nds.planner.deleteAfterDate.rollout.percentage=100
nds.planner.group.interval.short=86400000
nds.planner.group.interval.medium=172800000
nds.planner.group.interval.long=604800000
nds.reservedPorts.basePort=20180
nds.root.cert=ISRGROOTX1
nds.serverless.feature.enabled=false
nds.flex.feature.enabled=false
nds.flex.migration.warning.enabled=false
nds.flex.shared.migration.cron.enabled=false
nds.flex.migration.cron.schedule=0 * 13-20 ? * 2-6
nds.serverless.mtm.consumption.debug.logging.enabled=false
nds.serverless.trace.autoscale.enabled=false
nds.serverless.trace.autoscale.exporter=
nds.serverless.trace.autoscale.prefix=
nds.serverless.trace.autoscale.processor=
nds.serverless.supportsLBDeploymentIdOnEnvoyInstance=false
nds.serverless.privateNetworking.supportedCloudProviders=
nds.serverless.version.upgrade.window.enabled=
nds.serverless.version.upgrade.window.size=
nds.serverless.envoy.healthcheck.max.thread.count=1
nds.serverless.group.limits.maxResidentMTMs=85
nds.serverless.load.deadlineSeconds=
nds.serverless.load.maxThreads=
nds.serverless.load.minThreads=
nds.serverless.metrics.mocks.enabled=false
nds.serverless.metrics.api.version=v2
nds.serverless.mtm.autoscale.maxInstanceSize=M80
nds.serverless.mtm.load.updateDocuments.enabled=false
nds.serverless.mtm.limits.maxResidentTenants=2000
nds.serverless.pool.limits.maxResidentMTMs=100
nds.serverless.pool.limits.maxResidentMTMsForAutoScaleMTMCapacity=25
nds.serverless.pool.limits.maxResidentTenants=6000
nds.serverless.pool.limits.maxResidentTenantsThreshold=.2
nds.serverless.mtm.backingInstanceSize=M40
nds.mtm.sentinel.cron.enabled=false
nds.serverless.tenant.privateNetworking.reservedButUnavailablePrivateEndpointExpirationThreshold=P7D
nds.serverless.serverlessUpgradeToDedicated.enabled=false
nds.serverParams.shouldSetRateLimiterParams=true
nds.sharding.minSize=M10
nds.svc.ipToRegionMapping.executorEnabled=true
nds.tenantUpgrade.s3BucketName=atlas-snapshots-us-east-1-qa
nds.xdsCentralUrl=
nds.fleetAttributeCollection=true
netsuite.account.atlas.revenue.internal.id=1463
netsuite.account.cloud.receivable.internal.id=1541
netsuite.account.contra.liability.internal.id=1686
netsuite.account.mms.revenue.internal.id=257
netsuite.account.reserve.contra.internal.id=1319
netsuite.account.refund.salestax.debit.internal.id=109
netsuite.account.refund.vat.debit.internal.id=160
netsuite.api.accountNumber=1169967_SB1
netsuite.api.consumerKey=<SECRETMANAGER>
netsuite.api.consumerSecret=<SECRETMANAGER>
netsuite.app.endpoint=https://1169967-sb1.app.netsuite.com/
netsuite.api.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/
netsuite.api.token=<SECRETMANAGER>
netsuite.api.tokenSecret=<SECRETMANAGER>
netsuite.department.d15000.internal.id=213
netsuite.department.d15100.internal.id=259
netsuite.location.dublin.irl.internal.id=10
netsuite.location.nyc.usa.internal.id=4
netsuite.subsidiary.mongo.inc.item.id=1
netsuite.subsidiary.mongo.ltd.item.id=15
netsuite.rest.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/record/v1
netsuite.rest.query.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/query/v1
netsuite.restlet.endpoint=https://1169967-sb1.restlets.api.netsuite.com/app/site/hosting/restlet.nl
netsuite.restlet.tokenEndpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token
netsuite.restlet.cert.privateKey=<SECRETMANAGER>
netsuite.restlet.cert.id=<SECRETMANAGER>
netsuite.restlet.cert.scope=restlets,rest_webservices,suite_analytics
oa.dlzTTLService.enabled=false
officeIps.service.urlMeshEnvVar=KUBE_OFFICEIPS_SERVICE_DEV_GOV_US_GOV_WEST_1_AWS_CLOUD_GOV_DEV_10GEN_CC
okta.account.client.id=<SECRETMANAGER>
okta.account.client.secret=<SECRETMANAGER>
okta.api.token.emp-qa=
okta.api.token=<SECRETMANAGER>
okta.client.id=<SECRETMANAGER>
okta.client.secret=<SECRETMANAGER>
okta.cmab.client.secret=
okta.cmab.client.id=
okta.internal.authorization.server.id=
okta.internal.domain=
# We purposefully share an Okta instance between Atlas Commercial and Atlas Gov
okta.external.domain=https://auth-qa.mongodb.com
okta.idpDiscoveryPolicyId=00pk4f0955YwdJeAC356
okta.idpId=00ok4f8icpP2iAvzI356
okta.requireNonce=false
okta.restrictedEnv.customerGroup=00g3yx9az6erS2vjs357
okta.restrictedEnv.userGroupNames=10gen-gov,Government
okta.sessionDiscovery.enabled=false
okta.users.groupids=00g3yx9az6erS2vjs357
okta.session.maxMinutes=10
okta.profile.target.id=otyk4f8iwXh8C2Q0f356
okta.external.authorization.server.id=default
okta.token.authorized.client.ids=0oafm2iv10KRawp5U357
onlinearchive.aws.dataLakeExternalId=
onlinearchive.aws.dataLakeRoleARN=
opsgenie.api.eu.url=https://api.eu.opsgenie.com/v2/alerts
opsgenie.api.url=https://api.opsgenie.com/v2/alerts
opsgenie.api.us.url=https://api.opsgenie.com/v2/alerts
pagerduty.events.api.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.us.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.eu.v2.url=https://events.eu.pagerduty.com/v2/enqueue
pagerduty.api.regions.options=US
pagerduty.app.id=PJ8TL1B
ping.queue.size=10
ping.thread.count=10
ping.throttling.enabled=false
prom.saving.publicMetrics=false
provision.allowReaperOptions=true
provision.allowTerminateAll=true
provision.allowUrlOverride=true
provision.config.loc=/opt/mongodb-mms/classes/scripts/automation/
provision.dns.resolution.wait.secs=5
provision.dns.zone=mongo.plumbing
provision.includeRandomPin=false
provision.script.setup.name=run_atm_linux_agent.sh
queryengine.aws.assumeRoleAccessKey=
queryengine.aws.assumeRoleSecretKey=
queryengine.aws.assumeRoleUserARN=
streams.processManager.enabled=false
iam.gcp.reCaptcha.apiKey=
iam.gcp.reCaptcha.project.id=iam-recaptcha-dev
iam.gcp.reCaptcha.site.key=
reCaptcha.enabled.atlas=false
reCaptcha.enabled.login=false
iam.reCaptcha.enterprise.login.enabled=false
iam.reCaptcha.login.minScore=0.5
iam.reCaptcha.registration.enabled=false
iam.reCaptcha.registration.minScore=0.5
iam.reCaptcha.external.registration.enabled=false
iam.reCaptcha.external.registration.minScore=0.5
iam.reCaptcha.linkExternalAccount.enabled=false
iam.reCaptcha.linkExternalAccount.minScore=0.5
reCaptcha.enabled.externalRegistration=false
reCaptcha.private.key=
reCaptcha.public.key=
realm.billing.api.url=
segment.batch.url=https://api.segment.io/v1/batch
segment.clientSide.writeKey=qTI4pdLeQubXB5rTTqyv2aU3hN8Wbhz8
segment.group.url=https://api.segment.io/v1/group
segment.identify.url=https://api.segment.io/v1/identify
segment.profile.url=https://profiles.segment.com/v1/spaces
segment.serverSide.accessToken=
segment.serverSide.personasSpaceId=spa_smteJ81u1XbJMRgmDR9Td5
segment.serverSide.writeKey=FyFTYXbSswqzuGRClXYGgIIrrtaNNef0
segment.track.url=https://api.segment.io/v1/track
serverlessProxy.location=
sfdc.api.endpoint=https://test.salesforce.com/services/Soap/c/38.0/00DK000000W4vnY
sfdc.api.integrationOwnerId=005A0000002yZoK
sfdc.api.password=<SECRETMANAGER>
sfdc.api.username=<SECRETMANAGER>
sfdc.sandbox=true
sfdc.sync.cron.enabled=false
sfsc.api.password=<SECRETMANAGER>
sfsc.api.username=<SECRETMANAGER>
sfsc.api.client.id=<SECRETMANAGER>
sfsc.api.client.secret=<SECRETMANAGER>
sfsc.api.endpoint=https://mongodbgov--tstools.my.salesforce.com/services/Soap/c/38.0/00D3R0000008dyL
slack.api.message.url=https://slack.com/api/
slack.oauth2.clientId=<SECRETMANAGER>
slack.oauth2.clientSecret=<SECRETMANAGER>
slack.oauth2.url=https://slack.com/api/oauth.access
stitch.analytics.api.url=
stitch.api.key=
stitch.billing.api.url=
stripe.accountId.ltd=acct_19bBkZF5ZalzLjVw
stripe.accountId=acct_14RbHmBlIEdFNMKQ
stripe.apiKey.ltd=
stripe.apiKey.inc=
stripe.pubKey.ltd=
stripe.pubKey.inc=
stripe.webhook.updatecard.secret=
stripe.webhook.updatecard.secret.ltd=
stripe.maxRequestsPerSecond=20
stripe.retry.maxAttempts=5
stripe.retry.minBackoff=2000
support.appEmbedLink=https://support.mongodb.com/api/auth/oidc/initial
taxamo.api.privateToken=
taxamo.api.url=
twilio.account.sid=<SECRETMANAGER>
twilio.auth.token=<SECRETMANAGER>
twilio.from.num=<SECRETMANAGER>
twilio.messagingService.sid=<SECRETMANAGER>
university.centralUrls=https://education-staging.corp.mongodb.com
victorops.alert.api.url=https://alert.victorops.com/integrations/generic/********/alert/
xgen.webServerStatsEnabled=true
mms.backup.e2e.blockstore.aws.accesskey=
mms.backup.e2e.blockstore.aws.secretkey=
mms.backup.e2e.oplogstore.aws.accesskey=
mms.backup.e2e.oplogstore.aws.secretkey=
mms.backup.rollingReplacement.email=true
mms.featureFlag.backup.autoRecoveryUnsafeApplyOps=controlled
mongo.encryptedCredentials=true
mongo.ssl=true
mongo.backupdb.encryptedCredentials=true
mongo.backupdb.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27018,db-5.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27018,db-6.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27018/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.backupdb.ssl=true
mongo.backuplogs.encryptedCredentials=true
mongo.backuplogs.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27018,db-5.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27018,db-6.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27018/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.backuplogs.ssl=true
mongo.metricsstats.encryptedCredentials=true
mongo.metricsstats.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.metricsstats.ssl=true
mongo.mmsdbevents.encryptedCredentials=true
mongo.mmsdbevents.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbevents.ssl=true
mongo.event-service.encryptedCredentials=true
mongo.event-service.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.event-service.ssl=true
mongo.mmsdbalerts/mmsdbclusterdescriptions.encryptedCredentials=true
mongo.mmsdbalerts/mmsdbclusterdescriptions.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbalerts/mmsdbclusterdescriptions.ssl=true
mongo.mmsdbemails.encryptedCredentials=true
mongo.mmsdbemails.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbemails.ssl=true
mongo.mmsdbcollstatslatencyrrd.encryptedCredentials=true
mongo.mmsdbcollstatslatencyrrd.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbcollstatslatencyrrd.ssl=true
mongo.mmsdbrealmrrd.encryptedCredentials=true
mongo.mmsdbrealmrrd.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbrealmrrd.ssl=true
mongo.mmsdbftsrrd.encryptedCredentials=true
mongo.mmsdbftsrrd.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbftsrrd.ssl=true
mongo.mmsdbrrd.encryptedCredentials=true
mongo.mmsdbrrd.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbrrd.ssl=true
mongo.realmdiscovery.encryptedCredentials=
mongo.realmdiscovery.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.realmdiscovery.ssl=
mongo.mmsdbserverlessrrd.encryptedCredentials=true
mongo.mmsdbserverlessrrd.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbserverlessrrd.ssl=true
mongo.ratelimits.encryptedCredentials=true
mongo.ratelimits.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.ratelimits.ssl=true
mongo.mmsdbsharedtier/atlaslogsnds/atlaslogsoa/cpsagentlogs/backupagentlogs/monitoringagentlogs.encryptedCredentials=true
mongo.mmsdbsharedtier/atlaslogsnds/atlaslogsoa/cpsagentlogs/backupagentlogs/monitoringagentlogs.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbsharedtier/atlaslogsnds/atlaslogsoa/cpsagentlogs/backupagentlogs/monitoringagentlogs.ssl=true
mongo.chartsmetadata.encryptedCredentials=true
mongo.chartsmetadata.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.chartsmetadata.ssl=true
charts.cache.encryptedCredentials=true
charts.cache.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
charts.cache.ssl=true
charts.sample.encryptedCredentials=true
charts.sample.mongoUri=mongodb://bf2293fb423fe1f7195ca6128ef4f5366bc1c234a917fbf8364d1c5172566921-4db218ce3232b74831a82ce4bed822ae-90a263202d34443acd2400712ad9847de38876a261ce15e5a916b04fcbb8b2f7-f4848b9d9a53125ee80a4cc4a234e9bcd7a33668fbceb876974d6eedf57ee734ceac5bf58f296f380dd6b6d0f6d79dff6e70ec50548e0ce7105d91fea21505b3:<EMAIL>/test?ssl=true&readPreference=secondaryPreferred&retryWrites=false&retryReads=false
charts.sample.ssl=true
mongo.mmsdbmetering.encryptedCredentials=
mongo.mmsdbmetering.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdbmetering.ssl=true
mongo.billingauditing.encryptedCredentials=
mongo.billingauditing.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.billingauditing.ssl=
mongo.mmsdblogtooling.encryptedCredentials=true
mongo.mmsdblogtooling.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.mmsdblogtooling.ssl=true
mongo.iaquerystats.encryptedCredentials=true
mongo.iaquerystats.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.iaquerystats.ssl=true
mongo.abbrvslowlogs.encryptedCredentials=true
mongo.abbrvslowlogs.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.abbrvslowlogs.ssl=true
mms.monitoring.abbrvslowlogs.schemaAdvisorFieldsRolloutPercentage=100
mongo.ccpa.encryptedCredentials=true
mongo.ccpa.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.ccpa.ssl=true
mongo.iaslowlogs.encryptedCredentials=true
mongo.iaslowlogs.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27198,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27198/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.iaslowlogs.ssl=true
metering.centralUrl=https://cloud-qa.mongodb.com
metering.user.publicKey=<SECRETMANAGER>
metering.user.privateKey=<SECRETMANAGER>
mms.serverless.envoyInstanceSize=M10
mms.featureFlag.serverless.grpc.incrementalRollout.global.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.tenant.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.continuous.enabled=disabled
mms.featureFlag.serverless.serverlessMtmEligibleForUpgradeToDedicated=disabled
mms.featureFlag.serverless.serverlessSharedUIOptionEnabled=disabled
nds.mongodb.log.level=2
azure.ai.openai.compass.accessKey.1=
azure.ai.openai.compass.accessKey.2=
azure.ai.openai.compass.apiBase.1=
azure.ai.openai.compass.apiBase.2=
azure.ai.openai.compass.secretKey=
azure.ai.openai.compass.modelName=
azure.ai.openai.charts.accessKey.1=
azure.ai.openai.charts.accessKey.2=
azure.ai.openai.charts.apiBase.1=
azure.ai.openai.charts.apiBase.2=
azure.ai.openai.charts.gpt4o.accessKey.1=
azure.ai.openai.charts.gpt4o.apiBase.1=
azure.ai.openai.migrator.accessKey.1=
azure.ai.openai.migrator.accessKey.2=
azure.ai.openai.migrator.apiBase.1=
azure.ai.openai.migrator.apiBase.2=
azure.ai.openai.migrator.gpt4o.accessKey.1=
azure.ai.openai.migrator.gpt4o.apiBase.1=
azure.ai.openai.migrator.gpt4-turbo.accessKey.1=
azure.ai.openai.migrator.gpt4-turbo.apiBase.1=
azure.ingestion.dlz.storageAccountKey=
azure.oa.dlz.storageAccountKey=
mms.monitoring.managedslowms.setprofilinglevel.filterExpression.rollout.percentage=100
mms.monitoring.performanceadvisor.serverless.autoindexing.groupPercentageAllowed=100
# The following three are used for product validation, and are therefore empty above dev.
mms.slack.test.channel=
mms.slack.test.token=
mms.alert.test.email.emailAddress=
mms.account.mfa.getMfaFactors.ratelimit.enabled=true
mms.account.mfa.getMfaFactors.ratelimit.maxHitsPerPeriod=60
mms.account.mfa.getMfaFactors.ratelimit.minutesPeriod=15
mms.account.mfa.enrollMfaFactor.ratelimit.enabled=true
mms.account.mfa.enrollMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.enrollMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaFactor.ratelimit.enabled=true
mms.account.mfa.activateMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.activateMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaPushFactor.ratelimit.enabled=true
mms.account.mfa.activateMfaPushFactor.ratelimit.maxHitsPerPeriod=30
mms.account.mfa.activateMfaPushFactor.ratelimit.minutesPeriod=15
mms.account.mfa.verifyMfaFactor.ratelimit.enabled=true
mms.account.mfa.verifyMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyMfaFactor.ratelimit.minutesPeriod=5
mms.account.mfa.verifyPushFactorChallenge.ratelimit.enabled=true
mms.account.mfa.verifyPushFactorChallenge.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyPushFactorChallenge.ratelimit.minutesPeriod=5
mms.account.mfa.deleteMfaFactor.ratelimit.enabled=true
mms.account.mfa.deleteMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.deleteMfaFactor.ratelimit.minutesPeriod=15
mms.account.auth.getAuthMfaState.ratelimit.enabled=true
mms.account.auth.getAuthMfaState.ratelimit.maxHitsPerPeriod=30
mms.account.auth.getAuthMfaState.ratelimit.minutesPeriod=5
mms.account.auth.verifyAuthMfa.ratelimit.enabled=true
mms.account.auth.verifyAuthMfa.ratelimit.maxHitsPerPeriod=10
mms.account.auth.verifyAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.resendAuthMfa.ratelimit.enabled=true
mms.account.auth.resendAuthMfa.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resendAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.cancelAuthVerify.ratelimit.enabled=true
mms.account.auth.cancelAuthVerify.ratelimit.maxHitsPerPeriod=5
mms.account.auth.cancelAuthVerify.ratelimit.minutesPeriod=15
mms.account.auth.cancelAuthVerify.ratelimit.payload.jsonPath=$.username
mms.account.auth.checkPassword.ratelimit.enabled=true
mms.account.auth.checkPassword.ratelimit.maxHitsPerPeriod=10
mms.account.auth.checkPassword.ratelimit.minutesPeriod=5
nds.embeddedConfig.minMongoDBVersion=8.0.0-rc0
nds.embeddedConfig.maxShardCount=3
mms.account.auth.socialSSO.ratelimit.enabled=true
mms.account.auth.socialSSO.ratelimit.maxHitsPerPeriod=50
mms.account.auth.socialSSO.ratelimit.minutesPeriod=5
mms.user.redirectInvite.ratelimit.enabled=true
mms.user.redirectInvite.ratelimit.maxHitsPerPeriod=60
mms.user.redirectInvite.ratelimit.minutesPeriod=15
mms.user.redirectInvite.ratelimit.pathParam.parameterName=username
mms.user.resetSend.ratelimit.enabled=true
mms.user.resetSend.ratelimit.maxHitsPerPeriod=10
mms.user.resetSend.ratelimit.minutesPeriod=15
mms.user.resetSend.ratelimit.payload.jsonPath=$.username
mms.organization.setSecurityContact.ratelimit.enabled=true
mms.organization.setSecurityContact.ratelimit.maxHitsPerPeriod=10
mms.organization.setSecurityContact.ratelimit.minutesPeriod=5
mms.account.profile.deleteEmailChangeRequest.ratelimit.enabled=true
mms.account.profile.deleteEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.deleteEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.createEmailChangeRequest.ratelimit.enabled=true
mms.account.profile.createEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.createEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.checkPasswordForProfileChange.ratelimit.enabled=true
mms.account.profile.checkPasswordForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.checkPasswordForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.verifyFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyFactorForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.minutesPeriod=15
# Experimentation Properties
# analytics.enabled must be true and pholiota properties must be filled if setting the experiments.enabled property to true
analytics.enabled=false
experiments.enabled=false
pholiota.getExperimentsUrl=
pholiota.mmsApiKey=
pholiota.mmsHttpReferer=
mms.account.profile.getOktaUser.ratelimit.enabled=true
mms.account.profile.getOktaUser.ratelimit.maxHitsPerPeriod=20
mms.account.profile.getOktaUser.ratelimit.minutesPeriod=1
mms.account.profile.updatePassword.ratelimit.enabled=true
mms.account.profile.updatePassword.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updatePassword.ratelimit.minutesPeriod=1
mms.account.profile.updateProfile.ratelimit.enabled=true
mms.account.profile.updateProfile.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updateProfile.ratelimit.minutesPeriod=1
mms.account.profile.activateFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.activateFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.activateFactorForProfileChange.ratelimit.minutesPeriod=1
mms.user.getUserPartnerIntegrationsData.ratelimit.enabled=true
mms.user.getUserPartnerIntegrationsData.ratelimit.maxHitsPerPeriod=100
mms.user.getUserPartnerIntegrationsData.ratelimit.minutesPeriod=20
mms.user.getUserPartnerIntegrationsData.ratelimit.pathParam.parameterName=username
mms.user.registerCall.ratelimit.enabled=true
mms.user.registerCall.ratelimit.maxHitsPerPeriod=100
mms.user.registerCall.ratelimit.minutesPeriod=20
mms.account.auth.unauthedResendVerificationEmail.ratelimit.enabled=true
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.maxHitsPerPeriod=1
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.minutesPeriod=60
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.jsonPath=$.username
mms.account.auth.resendVerificationEmail.ratelimit.enabled=true
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resendVerificationEmail.ratelimit.user.maxHitsPerPeriod=1
mms.account.auth.resendVerificationEmail.ratelimit.user.minutesPeriod=60
mms.account.auth.resetPasswordRequestSend.ratelimit.enabled=true
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.jsonPath=$.username
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.enabled=true
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.jsonPath=$.username
mms.account.auth.resetComplete.ratelimit.enabled=true
mms.account.auth.resetComplete.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resetComplete.ratelimit.minutesPeriod=20
mms.account.auth.resetComplete.ratelimit.payload.jsonPath=$.username
mms.user.mfa.auth.ratelimit.enabled=true
mms.user.mfa.auth.ratelimit.maxHitsPerPeriod=10
mms.user.mfa.auth.ratelimit.minutesPeriod=20
mms.authz.isAuthorized.ratelimit.enabled=true
mms.authz.isAuthorized.ratelimit.maxHitsPerPeriod=10
mms.authz.isAuthorized.ratelimit.minutesPeriod=1
mms.account.device.verifyDeviceCode.ratelimit.enabled=true
mms.account.device.verifyDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.verifyDeviceCode.ratelimit.minutesPeriod=20
mms.account.device.confirmDeviceCode.ratelimit.enabled=true
mms.account.device.confirmDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.confirmDeviceCode.ratelimit.minutesPeriod=20
mms.api.organizations.updateOrganizationSettings.ratelimit.enabled=true
mms.api.organizations.updateOrganizationSettings.ratelimit.maxHitsPerPeriod=10
mms.api.organizations.updateOrganizationSettings.ratelimit.minutesPeriod=5
mms.api.private.account.device.generateCode.ratelimit.enabled=true
mms.api.private.account.device.generateCode.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.generateCode.ratelimit.minutesPeriod=20
mms.api.private.account.device.getToken.ratelimit.enabled=true
mms.api.private.account.device.getToken.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.getToken.ratelimit.minutesPeriod=20
mms.api.private.account.device.revoke.ratelimit.enabled=true
mms.api.private.account.device.revoke.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.revoke.ratelimit.minutesPeriod=20
mms.api.private.email.sendToSecurityContact.ratelimit.enabled=true
mms.api.private.email.sendToSecurityContact.ratelimit.maxHitsPerPeriod=100
mms.api.private.email.sendToSecurityContact.ratelimit.minutesPeriod=20
mms.api.private.dbUser.validate.ratelimit.enabled=true
mms.api.private.dbUser.validate.ratelimit.maxHitsPerPeriod=100
mms.api.private.dbUser.validate.ratelimit.minutesPeriod=20
datadog.api.regions.options=US1_FED
nds.acme.newDedicatedClusterAllowMixedCerts=false
aws.atlasSelfServeMarketplaceProductId=
aws.marketplaceSellerId=
mongo.integrations.encryptedCredentials=true
mongo.integrations.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.integrations.ssl=true
mongo.ui.encryptedCredentials=true
mongo.ui.mongoUri=mongodb://c27b9eaf629a415bae0cbcea9e340d3efb7f813cd0d2718a29f97304aa80edc5-2b25ad14d8dadfd6df9e718ee41a0a8f-8b6f30eaa2af273d325272843eb33481-18c8966a531a6967c7bf800ff56c48f307d06be30fa3109b2b8da8c61395919606b74503834e5923f3611a89a93279c3f94fa82dd6e21fd12b9e6b81ac223ef3:<EMAIL>:27017,db-2.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017,db-3.us-gov-west-1.aws.cloud-gov-qa.10gen.cc:27017/admin?maxPoolSize=50&compressors=snappy&uuidRepresentation=standard&retryWrites=false&retryReads=false
mongo.ui.ssl=true
mms.azurenative.AzureNativeRateLimiting.ratelimit.enabled=true
mms.azurenative.AzureNativeRateLimiting.ratelimit.maxHitsPerPeriod=100
mms.azurenative.AzureNativeRateLimiting.ratelimit.minutesPeriod=5
mms.featureFlag.gcpArmAxion=disabled
mms.featureFlag.azureArmDpsv6Epsv6=disabled
mms.featureFlag.horizontalScalingAutoSharding=disabled
mms.featureFlag.gcpN4Family=disabled
integrations.deletion.dryRun=false
slack.oauth.v2.clientId=<SECRETMANAGER>
slack.oauth.v2.clientSecret=<SECRETMANAGER>
slack.oauth.v2.url.backend=https://slack.com/api/oauth.v2.access
slack.oauth.v2.url.frontend=https://slack.com/oauth/v2/authorize
slack.oauth.v2.scopes=chat:write,chat:write.customize,chat:write.public
slack.oauth.v2.enabled=true
mms.disaggregatedStorage.fetchLatestBuildFromDev.globalAdminApiSecret=
nds.gcp.provisionedIOPS.enabled=false
communication.publisher.profile=aws
communication.publisher.awsRegion=us-gov-west-1
communication.publisher.awsRoleArn=arn:aws-us-gov:iam::255955262727:role/communication-service-resources-qa/communication-service-queue-qa-gov-aws-us-gov-west-1
communication.messagebus.publisher.kinesis.streamName=communication-service-stream-qa-gov-aws-us-gov-west-1
communication.statusUpdate.publisher.kinesis.streamName=status-update-stream-qa-gov-aws-us-gov-west-1
mms.new.db.user.scramSHA256IterationCount=600000
eventbus.publisher.queueingEnabled=false
eventbus.publisher.awsRegion=us-gov-west-1
eventbus.publisher.awsRoleArn=arn:aws-us-gov:iam::255955262727:role/event-service-resources-qa/event-service-queue-producer-qa-gov-aws-us-gov-west-1
eventbus.publisher.kinesis.streamName=event-service-stream-qa-gov-aws-us-gov-west-1
mms.robustNodeHorizonMappingEnabled=true
mms.github.publicips.refreshEnabled=true
# partner integrations is not enabled in gov
mms.partnerIntegrations.encryptionKey=
# Rate Limiting Configuration - filter enabled via config service feature flag mms.featureFlag.apix.ratelimit.apiMiddleware
## Shadow mode (false = log only, true = reject requests) - currently disabled for future use
ratelimit.apiMiddleware.rejectRequests=false
## Circuit Breaker Configuration (disabled via circuitbreaker.enabled=false)
ratelimit.apiMiddleware.circuitbreaker.name=ratelimit-redis-breaker
ratelimit.apiMiddleware.circuitbreaker.failureRateThreshold=40.0
ratelimit.apiMiddleware.circuitbreaker.slidingWindowSize=50
ratelimit.apiMiddleware.circuitbreaker.permittedCallsInHalfOpenState=3
ratelimit.apiMiddleware.circuitbreaker.waitDurationSeconds=15
ratelimit.apiMiddleware.circuitbreaker.slowCallRateThreshold=20.0
ratelimit.apiMiddleware.circuitbreaker.slowCallDurationThresholdMillis=50
## the minimum number of calls which are required before circuitbreaker can calculate error rate
## default value 100 + 1 to count for the redis coldstart.
ratelimit.apiMiddleware.circuitbreaker.minimumNumberOfCalls=101
# Redis cache configuration (if disabled, in-memory local cache will be used)
ratelimit.redis.enabled=false
ratelimit.redis.auth.mode=AWS
ratelimit.redis.endpoint=
ratelimit.redis.port=6379
ratelimit.redis.cache.name=rate-limit-service-cache-qa
ratelimit.redis.region=us-east-1
ratelimit.redis.roleArn=
ratelimit.redis.credentials.username=rate-limit-service-cache-qa
ratelimit.redis.credentials.password=
ratelimit.redis.ssl.enabled=true
## IP Policy Configuration
ratelimit.policies.default.ip.config.capacity=400
ratelimit.policies.default.ip.config.refillRate=100
ratelimit.policies.default.ip.config.refillDurationSeconds=60
## Group Policy Configuration
ratelimit.policies.default.group.config.capacity=2000
ratelimit.policies.default.group.config.refillRate=1000
ratelimit.policies.default.group.config.refillDurationSeconds=60
## Organization Policy Configuration
ratelimit.policies.default.organization.config.capacity=5000
ratelimit.policies.default.organization.config.refillRate=2000
ratelimit.policies.default.organization.config.refillDurationSeconds=60
## User Policy Configuration
ratelimit.policies.default.user.config.capacity=1000
ratelimit.policies.default.user.config.refillRate=500
ratelimit.policies.default.user.config.refillDurationSeconds=60
