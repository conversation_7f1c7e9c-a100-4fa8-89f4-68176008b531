client.authn.http.addressEnvVar=KUBE_AUTHN_SERVICE_KUBE_HTTP_PROD_ACTIVE_CLOUD_10GEN_CC
client.authn.grpc.addressEnvVar=KUBE_AUTHN_SERVICE_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
client.authz.grpc.addressEnvVar=KUBE_AUTHZ_SERVICE_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
client.authzv2.grpc.addressEnvVar=KUBE_AUTHZ_SERVICE_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
client.comment.grpc.addressEnvVar=KUBE_MMS_COMMENT_SERVICE_KUBE_PROD_US_EAST_1_AWS_CLOUD_10GEN_CC
client.meterusages.grpc.addressEnvVar=KUBE_MMS_METERING_METER_USAGES_SERVICE_KUBE_PROD_US_EAST_1_AWS_CLOUD_10GEN_CC
client.config.grpc.addressEnvVar=KUBE_CONFIG_SERVICE_KUBE_PROD_US_EAST_1_AWS_CLOUD_10GEN_CC
client.payments.grpc.addressEnvVar=KUBE_PAYMENTS_SERVICE_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
client.event.grpc.addressEnvVar=KUBE_EVENTS_API_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
client.pops.grpc.addressEnvVar=KUBE_SODAFOUNTAIN_SERVICE_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
client.apiregistry.grpc.addressEnvVar=KUBE_API_REGISTRY_SERVICE_KUBE_GRPC_PROD_US_EAST_1_AWS_CLOUD_10GEN_CC
client.metrics.http.addressEnvVar=KUBE_CUSTOMER_METRICS_INGESTION_KUBE_HTTP_PROD_US_EAST_1_AWS_CLOUD_10GEN_CC
client.communication.grpc.addressEnvVar=KUBE_COMMUNICATION_SERVICE_KUBE_PROD_US_EAST_1_AWS_CLOUD_10GEN_CC
client.slsbackup.v1.grpc.addressEnvVar=KUBE_SLSBACKUP_SERVICE_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
client.pricing.grpc.addressEnvVar=KUBE_MMS_PRICING_SERVICE_KUBE_PROD_US_EAST_1_AWS_CLOUD_10GEN_CC
client.predictive-metrics.grpc.address=predictive-scaling-grpc-api.predictive-scaling-prod.svc.cluster.local:50051
account.centralUrl=https://account.mongodb.com
account.session.maxConcurrent=10
account.session.maxHours=12
alert.flapping.start.threshold=0.4
alert.flapping.stop.threshold=0.3
alert.flapping.linear.weight=0.2
alert.flapping.strategy=fixed
alert.flapping.skip.enabled=true
alert.flapping.enabled.types=OUTSIDE_METRIC_THRESHOLD
alert.flapping.sufficient.states.limit=20
amplitude.apiKey=
amplitude.secretKey=
regionalization.enabledOrgs=5b2225f696e8212c0fd8273e,5b57200b0bd66b654df0ef4c,62d912b3cba31a14f96c61a4,5f3a84ebc363cd6f5044888a,63c187070d0f2078cf32f7c6,62400fe6d7b4711dc67b097c
atlas.backup.regional.deployment.enabled=true
mms.featureFlag.awsGraviton4=controlled
mms.featureFlag.awsGraviton4NVMe=controlled
mms.featureFlag.awsCapacityAwareAZSelection=controlled
mms.featureFlag.azureCapacityAwareAZSelection=controlled
mms.featureFlag.gcpCapacityAwareAZSelection=controlled
mms.featureFlag.capacityAwareAZPreventativeRollbackSupport=controlled
mms.featureFlag.automationChangesWebClient=enabled
mms.featureFlag.atlasDataRegionalization=controlled
mms.featureFlag.atlasDataRegionalizationGroup=controlled
mms.featureFlag.atlasAzureSsdPV2=enabled
mms.featureFlag.atlasAzureSsdPV2Wave2=controlled
mms.featureFlag.atlasAzureSsdPV2EnablePreviewRegions=controlled
mms.featureFlag.atlasAzureSsdForcePV1=controlled
mms.featureFlag.logcollectionS3Backend=controlled
mms.featureFlag.atlasProvideHardcodedOidcIdpInformation=disabled
mms.featureFlag.clusterCentricPerformanceAdvisor=enabled
mms.featureFlag.clusterCentricQueryProfiler=controlled
mms.featureFlag.namespaceInsights=controlled
mms.featureFlag.updateMetricsUiForEmbeddedConfigServers=enabled
mms.featureFlag.queryInsights=disabled
mms.featureFlag.atlasOptOutPeriodicCorruptionDetection=controlled
mms.featureFlag.atlasDbcheckEnableMidCollectionResumability=controlled
mms.featureFlag.atlasPrioritizeHavingPrimaryOnUpscaledNode=controlled
mms.featureFlag.atlasDiskWarmingSupport=enabled
mms.featureFlag.atlasIgnoreDiskWarmingState=controlled
mms.featureFlag.customSessionTimeouts=disabled
mms.featureFlag.atlasAutomatePriorityTakeover=controlled
mms.featureFlag.atlasAdvancedRegionalizedPrivateEndpoints=controlled
mms.featureFlag.atlasResourcePoliciesExtensionWithUi=enabled
mms.featureFlag.serverlessUpgradeToDedicatedDebugMode=disabled
mms.featureFlag.atlasAutohealResyncImprovements=controlled
mms.featureFlag.atlasAutohealRemoveIcmpPing=controlled
mms.featureFlag.atlasAcknowledgesApiKeyFromChefConfCall=enabled
mms.featureFlag.atlasAlwaysManagedDefaultRwConcern=controlled
mms.featureFlag.atlasTunedInstanceIndexBuildLimits=controlled
mms.featureFlag.dataExfiltrationManageSecurityGroupsEnabled=disabled
mms.featureFlag.dataExfiltrationSecurityGroupsExcludeAllowAllRuleEnabled=disabled
mms.featureFlag.dataExfilPreventionSidecarEnabled=disabled
mms.featureFlag.dataExfilPreventionSidecarToProxyRoutingEnabled=disabled
mms.featureFlag.atlasAutohealReduceShutdownTime=controlled
mms.featureFlag.atlasAutohealReduceShutdownTimeHealRepair=controlled
mms.featureFlag.atlasTurnDiskWarmingOffForAzureLegacy=controlled
mms.featureFlag.atlasAutohealReducedThreshold=controlled
mms.featureFlag.disableScramSha1Auth=controlled
mms.featureFlag.atlasAzureExcludeConstrainedCombos=controlled
mms.monitoring.performanceadvisor.rollup.initial.bucketCount=2
mms.monitoring.performanceadvisor.rollup.threadPoolSize=20
mms.monitoring.performanceadvisor.rollup.scheduler.enabled=true
mms.monitoring.performanceadvisor.rollup.job.PT24H.workingRetryWait=********
mms.monitoring.performanceadvisor.rollup.job.PT1H.workingRetryWait=7200000
mms.monitoring.performanceadvisor.rollup.job.PT1M.workingRetryWait=300000
mms.monitoring.groupPingData.sharding.enabled=true
mms.monitoring.hostLastDailyPing.sharding.enabled=true
mms.monitoring.hostLastPing.sharding.enabled=true
mms.featureFlag.sqlSchemaManagementUI=enabled
mms.featureFlag.streamsEnableAzure=enabled
mms.featureFlag.streamsEnableAdditionalAzureRegions=enabled
mms.featureFlag.streamsAzurePrivateLink=enabled
mms.featureFlag.streamsAWSPrivateLink=controlled
mms.featureFlag.streamsEnableAdditionalRegions=enabled
mms.featureFlag.atlasDbcheckWaitForAllNodesToObserveDbcheckStop=controlled
mms.featureFlag.atlasAutomaticEmbeddedConfigTransitions=enabled
mms.featureFlag.vqpBinning=controlled
mms.featureFlag.atlasAzureDsv5AndEsv5InstanceFamilies=controlled
mms.featureFlag.atlasLiveImportHelixPush=enabled
mms.featureFlag.atlasLiveImportHelixPull=enabled
mms.featureFlag.atlasShardedClustersBehindUniformFrontend=disabled
mms.featureFlag.atlasMongomirrorLiveImportHelix=enabled
mms.featureFlag.atlasLiveImportLargeNodePool=enabled
mms.featureFlag.atlasEnableAdditionalDbcheckValidations=controlled
mms.featureFlag.atlasStreamsSP10InstanceTier=enabled
mms.featureFlag.dataExplorerCompassWeb=disabled
mms.featureFlag.dataExplorerCompassWeb.userControlledDisable=controlled
mms.featureFlag.disableDiscoveryIpAddressAliases=disabled
mms.featureFlag.atlasClusterScalingImprovementsPhase1=enabled
mms.featureFlag.atlasIndependentShardScaling=enabled
mms.featureFlag.atlasAsymmetricShardAutoscaling=controlled
mms.featureFlag.baas.PricingChange=enabled
mms.featureFlag.billingResourceTaggingBulkEndpoint=enabled
mms.featureFlag.streamsVpcPeeringPreview=enabled
mms.featureFlag.enableLogRequestTimeframe=enabled
mms.featureFlag.enabledRevampedTriggersUi=enabled
mms.featureFlag.enableTypeScriptInFunctions=controlled
mms.featureFlag.enterpriseLandingPage=controlled
mms.featureFlag.enterpriseSandbox=controlled
mms.featureFlag.deprecateEndpointsAndDataApi=controlled
mms.featureFlag.deprecateThirdPartyServices=controlled
mms.featureFlag.allowCrossRegionExtendedStorage=controlled
mms.featureFlag.disableDarkReader=disabled
mms.featureFlag.bicDeprecationShowWarning=enabled
mms.featureFlag.bicDeprecation=controlled
mms.featureFlag.exemptFromBicDeprecation=controlled
mms.featureFlag.atlasMigrationHubCutover=enabled
mms.featureFlag.customerPinnedFcvAndMdbDowngrade=enabled
mms.featureFlag.atlasAllowOneNodeDownInPacpcm=enabled
mms.featureFlag.atlasGcpHyperdiskExtreme=controlled
mms.featureFlag.atlasAllowSeveralNodesDownInPacpcm=enabled
mms.featureFlag.atlasAutomationShorterQuiesceTime=enabled
mms.featureFlag.atlasAutomationSpecifyForceTrueForShutdowns=enabled
mms.featureFlag.atlasAllowUpscalingToCleanlyAbandonPlans=enabled
mms.featureFlag.RtppDiskThroughputMetrics=enabled
mms.featureFlag.atlasChainPauseMoves=controlled
mms.featureFlag.atlasFcvPinningInUi=enabled
mms.featureFlag.atlasKmipKeyRotationInMaintenanceWindows=enabled
mms.featureFlag.validateS3ChunksOnDownload=controlled
mms.featureFlag.atlasConfigurableGp3Iops=controlled
mms.featureFlag.atlasExcludeRegionUseast2FromAzureDsv5Esv5Families=controlled
mms.atlasAccessTransparencyX509CertsEnabled=false
mms.atlasEmployeeInformationOktaReadOnlyClientCredentials=<SECRETMANAGER>
mms.atlasEmployeeInformationOktaApiKeyUrl=https://corp.mongodb.com
mms.atlasEmployeeInformationOktaApiKeyClientId=0oavmhemprHQKUiC5297
mms.atlasEmployeeInformationOktaIntegrationEnabled=true
mms.atlasClusterUpdateOptimisticConcurrency=true
mms.featureFlag.streamsVPCPeering=enabled
mms.featureFlag.disableCloudNav=controlled
mms.featureFlag.atlasFcvDowngradeAdminAction=enabled
mms.featureFlag.atlasResourcePolicies=controlled
mms.featureFlag.orgLevelGenAiControlSwitch=enabled
mms.featureFlag.agentRotatesAtlasProxyLogs=disabled
mms.featureFlag.al2023AWSOs=controlled
mms.featureFlag.awsAL2023ForceMigrateNVMe=disabled
mms.featureFlag.al2023AzureOs=disabled
mms.featureFlag.azureAL2023ForceMigrateNVMe=disabled
mms.featureFlag.al2023GCPOs=controlled
mms.featureFlag.allowEnforceMinTls13=controlled
mms.featureFlag.atlasTls13AutoUpgradeEnabled=controlled
mms.featureFlag.atlasDeprecateTls10AndTls11=controlled
mms.featureFlag.dataExplorerGenAIFeatures=controlled
mms.featureFlag.dataExplorerGenAISampleDocument=controlled
mms.featureFlag.disableDataExplorerGenAISampleDocumentPassing=controlled
mms.featureFlag.atlasReleaseAwsIps=controlled
mms.featureFlag.alertHostSSHSessionStarted=controlled
mms.featureFlag.awsUseIpamIp=controlled
mms.featureFlag.autoscaling.predictive.compute.enabled=controlled
mms.featureFlag.autoscaling.predictive.compute.m10m20enabled=controlled
mms.featureFlag.customer.queryShapeInsights=controlled
nds.autoScaling.predictive.cron.maxNumberOfInstanceTiers=1
mms.featureFlag.enableParcaAgent=disabled
nds.custom.roles.any.db.resources.enabled=true
mms.rootVolumeMetrics.rolloutPercentage=0
mms.atlasSearch.tracingSamplerRate=0.0
atlas.regionalData.enabled=true
mms.monitoring.regional.maintenance.enabled=true
mms.monitoring.alwaysUpdateCollectedIndex.enabled=true
atlasProxy.location=https://internal-downloads.mongodb.com/atlasproxy/releases/prod/
atlasUIS.location=
atlasUIS.version=0.0.0.0
authn.enabled=true
authn.oauth.enabled=true
authn.oauth.serviceAccounts.enabled=true
authn.oauth.serviceAccounts.alertsEnabled=true
# This issuer depends on the issuer value in AuthN config, they must be the same
authn.oauth.issuer=https://cloud.mongodb.com
authn.adminSecretKey=<SECRETMANAGER>
authn.internalClient.id=mdb_ic_id_644055036d35a24b3c38c1e5
authn.internalClient.secret=<SECRETMANAGER>
auto.provision.aws.trusted.accesskey=<SECRETMANAGER>
auto.provision.aws.trusted.id=<SECRETMANAGER>
auto.provision.aws.trusted.secretkey=<SECRETMANAGER>
automation.agent.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
automation.agent.location.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
automation.agent.minimumVersion=13.10.0.8620-1
automation.agent.atlasMinimumVersion=13.37.2.9605-1
automation.agent.minimumVersionForClientPIT=4.3.0.2231-1
automation.agent.serverlessLocation=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
automation.agent.serverlessLocation.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
automation.agent.serverlessMinimumVersion=0.0.0.0
automation.agent.version=13.41.0.9760-1
automation.kmipTool.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
automation.kmipTool.location.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
automation.versions.autoRefreshUri=classpath://mongodb_version_manifest.json
automation.versions.source=remote
automation.versions.atlasDecoupledFromCm=false
avalara.accountNumber=**********
avalara.companyCode.ireland=MongoDB Ltd - Atlas
avalara.companyCode.inc=10gen
avalara.licenseKey=<SECRETMANAGER>
nds.serverless.tenant.load.updateDocuments.enabled=true
avalara.url=https://rest.avatax.com
aws.accessKey.dataWarehouse=<SECRETMANAGER>
aws.accesskey=<SECRETMANAGER>
aws.dataWarehouse.bucket=mongodb-datawarehouse
aws.dataWarehouse.bucket.prefix=data_dump/cloud/
aws.dataWarehouse.kinesis.accessKey=<SECRETMANAGER>
aws.dataWarehouse.kinesis.secretKey=<SECRETMANAGER>
aws.marketplaceProductCode=a54i1ad8ag13y4xjip307xxrm
aws.marketplaceUrl=https://aws.amazon.com
aws.secretKey.dataWarehouse=<SECRETMANAGER>
aws.secretkey=<SECRETMANAGER>
aws.ses.endpoint=https://email.us-east-1.amazonaws.com
awsGraviton.minimumMongoDBVersion=4.2
local.azure.multiTenantAppId=
local.azure.multiTenantAppSecret=
baas.centralUrl=https://services.cloud.mongodb.com
baas.legacyCentralUrl=https://realm.mongodb.com
backup.kmip.server.ca.file=/opt/mongodb-mms/classes/kmip_test_server_ca.pem
backup.wtcheckpoints.blocksavepool.threads.reservationTimeoutMillis=750
biConnector.location=https://info-mongodb-com.s3.amazonaws.com/mongodb-bi/v2/
biConnector.location.atlas=https://internal-downloads.mongodb.org/mongodb-bi/v2/
biConnector.minimumVersion=2.3.1
biConnector.version=2.14.24
atlas.biConnector.version=2.14.24
atlas.api.versioning.rejectFutureDateCalls=true
atlas.api.versioning.enabled=true
atlas.api.versioning.preview=enabled
atlas.api.versioning.upcoming=enabled
braintree.clientId=
braintree.environment=production
braintree.maxRequestsPerSecond=80
braintree.merchantAccountId.inc=<SECRETMANAGER>
braintree.merchantAccountId.ltd=<SECRETMANAGER>
braintree.merchantId=<SECRETMANAGER>
braintree.privateKey=<SECRETMANAGER>
braintree.publicKey=<SECRETMANAGER>
braintree.url=https://www.braintreegateway.com
brs.agent.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
brs.agent.minimumVersion=*********-1
brs.agent.version=7.8.1.1109-1
brs.client.pit.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
brs.client.pit.version=13.41.0.9760-1
brs.daemon.mongodStartTimeoutMs=1800000
brs.daemon.queryableMongodStartTimeoutMs=********
brs.maxBlockFetcherMemoryKB=327680
brs.metadatapruning.deletedFromSanSnapshots.expireAfterDays=40
brs.metadatapruning.maxPrunePerSecond=70.0
brs.meterUsage.BatchSize=1000
brs.oplog.prune.job.intervalMinutes=180
brs.queryable.lruCacheCapacityMB=512
brs.queryable.mongod.memoryQuotaMB=8192
brs.queryable.proxyDebugPort=26999
brs.queryable.proxyPort=25999
brs.queryable.tls.disabledProtocols=SSLv2Hello,SSLv3,TLSv1,TLSv1.1,TLSv1.3
brs.queryable.tls.disabledCiphers=TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384
brs.queryable.wiredTiger.engineConfig.cacheSizeGB=1
brs.s3.validation.testing=disabled
brs.tunnel.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
brs.tunnel.version=13.41.0.9760-1
mms.backup.snapshot.volume.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/snapshot-volume/${env}/
mms.backup.snapshot.volume.version=13.41.0.9760-1
brs.wtc.maxNumBufferedFileBatchesOnAgent=1
charts.api.url=http://charts-api-http.charts-prod.us-east-1.aws.cloud.kube:8080/
charts.api.secretKey=<SECRETMANAGER>
charts.apiSecret=<SECRETMANAGER>
charts.stitch.app.id=chartstenantinfo-wlbcw
charts.cache.encryptedCredentials=true
charts.cache.mongoUri=mongodb+srv://f0d87b7995cc5eaa37c28d135a40a61a2fe1db14a9e70576ee1e767d3818380e-a6e95fd8624a75d85ba1dc8d1c3368e6-b21e716a051f55ba82c5ef62e38285c26f9e84a4f61af87c2bd795a794cfe6cc:<EMAIL>/test?retryWrites=true&w=majority&retryReads=false&uuidRepresentation=standard
charts.sample.encryptedCredentials=true
charts.sample.mongoUri=mongodb://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-4d23ebfa8e56fa32b6eeba3c4a4564a3-838afeb7580d5c277b41741f7e6eca82a15ed7b289250fc243334ece6d6c6fe8-53496cc4d0aa36801b9743caa7f97c22138366a248292f9d9e6f133ff095fc3426406e42b4343819bd9bb5c6e2700355d4b9b069674e15147b9c59f1c6cb1ffe:<EMAIL>/test?ssl=true&readPreference=secondaryPreferred
charts.centralUrl=https://charts.mongodb.com
charts.stitch.useLocalDeploymentModel=true
charts.template.aws.accessKey=<SECRETMANAGER>
charts.template.aws.secretKey=<SECRETMANAGER>
charts.template.bucketName=<SECRETMANAGER>
charts.upgrade.poolSize=5
charts.version=0.0.0
clienthub.password=<SECRETMANAGER>
clienthub.user=<SECRETMANAGER>
cmabService.urlSuffix=-prod-prod.dsplatform.prod.corp.mongodb.com/predict/cmab
daemon.http.bindhostname=0.0.0.0
data.delete.queue.size=500
dataWarehouse.dailyDataExport=true
dataWarehouse.hourlyDataExport=true
dataWarehouse.dataExport.nonOverlappingNamespaces=nds.planner.plans,mmsdbconfig.config.alertConfigHistory
datadog.api.eu.url=https://api.datadoghq.eu/api/v1
datadog.api.url=https://app.datadoghq.com/api/v1
datadog.api.us.url=https://app.datadoghq.com/api/v1
datadog.api.us3.url=https://api.us3.datadoghq.com/api/v1
datadog.api.us5.url=https://api.us5.datadoghq.com/api/v1
datadog.api.ap1.url=https://api.ap1.datadoghq.com/api/v1
datadog.api.us1.fed=https://app.ddog-gov.com/api/v1
datadog.api.us.apiKey=
datadog.api.us.applicationKey=
datadog.api.regions.options=US,US3,US5,EU,AP1,US1_FED
dbobject.read.cache.size=1
dbobject.write.cache.size=400
devHub.centralUrls=https://www.mongodb.com/developer,https://devhub-ui.devrel.prod.corp.mongodb.com
distributed.cron.svc.registries=com.xgen.svc.mms.svc.cron.MmsCronJobRegistry,com.xgen.svc.nds.svc.AtlasClustersCronJobRegistry,com.xgen.svc.brs.svc.cron.BackupCronJobRegistry,com.xgen.svc.mms.svc.billing.audit.BillingAuditorCronRegistry,com.xgen.svc.mms.svc.billing.cron.BillingCronJobRegistry,com.xgen.svc.mms.svc.billing.cron.PaymentCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportAuditorCronJobRegistry
distributedAlert.executorThreadCount=90
distributedAlert.groupCollateQueueOfferTimeoutMs=1000
distributedAlert.groupCollateQueueSize=100
distributedAlert.groupQueueOfferTimeoutMs=1000
distributedAlert.groupQueueSize=500
distributedAlert.hostQueueSize=1000
dyn.customerName=<SECRETMANAGER>
dyn.password=<SECRETMANAGER>
dyn.username=<SECRETMANAGER>
ecosystem.enabled=true
eloqua.siteId=*********
eloqua.sync.enabled=true
fts.decoupled.planner.enabled=true
fts.decoupled.planner.timeoutHours=168
ga.accountId=7301842
ga.enabled=true
ga.p12file=ga-cloud-manager-analytics-api-prod.p12
ga.propertyId=**********-14
ga.serviceAccount=<EMAIL>
ga.viewId=*********
gtm.containerId=GTM-5ZD5LD
hipchat.api.message.url.v1=https://api.hipchat.com/v1/rooms
hipchat.api.message.url=https://api.hipchat.com/v2/room
iam.login.postAuth.allowLocalRedirect=false
ingestion.thread.count=4
intercom.apiKey=<SECRETMANAGER>
intercom.appId=qq7v0gqb
intercom.enabled=true
intercom.enabledLoggedInPages=true
intercom.qualtricsSurvey.api.token=<SECRETMANAGER>
intercom.secretKey=<SECRETMANAGER>
internalatlas.appdb.username=<SECRETMANAGER>
internalatlas.appdb.password=<SECRETMANAGER>
internalatlas.ldap.username=<SECRETMANAGER>
internalatlas.ldap.password=<SECRETMANAGER>
ipstack.enabled=true
ipstack.api.accessKey=<SECRETMANAGER>
ipstack.api.url=https://api.ipstack.com
jira.default.project=MMSSUPPORT
jira.password=<SECRETMANAGER>
jira.restApiRoot=https://jira.mongodb.org/rest/api/2
jira.username=<SECRETMANAGER>
job.processor.await.termination.time=60
job.processor.await.termination.unit=SECONDS
job.processor.aws.requests.per.second=3.0
job.processor.azure.requests.per.second=3.0
job.processor.blacklist.refresh.time=5
job.processor.blacklist.refresh.unit=SECONDS
job.processor.core.thread.count=10
job.processor.empty.queue.wait=5000
job.processor.enabled=false
job.processor.keepalive.time=1
job.processor.keepalive.unit=MINUTES
job.processor.max.thread.count=10
job.processor.server.tags.enabled=true
job.processor.server.tags.refresh.time=5
job.processor.server.tags.refresh.unit=SECONDS
job.processor.partitionQuery.numPartitions=1
job.processor.ping.time=10
job.processor.ping.unit=SECONDS
job.processor.ping.staleCount=60
onlineArchive.onlineArchiveExpiredFilesDeleter.deleteJobTimeoutMinutes=60
onlineArchive.onlineArchiveDLZCleanup.deleteJobTimeoutMinutes=10
oa.v3Migration.migrationPlans.maxRunning=20
oa.v3Migration.uploadJobs.maxRunning=250
oa.v3Migration.uploadJobs.timeout=1
oa.v3Migration.cutoverRollbackJobs.timeout=30
oa.v3Migration.cutoverRollbackJobs.numThreads=10
local.aws.secretsRegion=us-east-1,eu-west-1
local.aws.secretsPrimaryRegion=us-east-1
logCollection.maxDiskSpaceMB=2048000
marketing.centralUrls=https://mongodb.com,https://www.mongodb.com,https://mongodbcom.website.prod.corp.mongodb.com
metering.serviceMeshEnvVar=KUBE_MMS_METERING_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
metering.user.publicKey=<SECRETMANAGER>
metering.user.privateKey=<SECRETMANAGER>
metering.readFromDb=true
nds.meteringService.authn.migrationFlag=true
metricCache.receiveQueueSize=3000
metricCache.sendQueueSize=7000
mlab.clientId=atlas
mlab.portalBaseUrl=https://www.mlab.com
mlab.sharedSecret=<SECRETMANAGER>
mms.conf.splay.enabled=true
mms.ClientCertificateMode=none
mms.accountMultiFactorAuth.enabled=true
iam.accountMultiFactorAuth.smsBackoffLevels.enabled=true
iam.accountMultiFactorAuth.smsBackoffLevels.lockFactorThreshold=5
iam.accountMultiFactorAuth.smsBackoffLevelOne.rateLimitMinutes=1
iam.accountMultiFactorAuth.smsBackoffLevelTwo.rateLimitMinutes=5
iam.accountMultiFactorAuth.smsBackoffLevelThree.rateLimitMinutes=15
iam.accountMultiFactorAuth.smsBackoffLevelFour.rateLimitMinutes=30
iam.accountMultiFactorAuth.smsBackoffLevelFive.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelSix.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelSeven.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelEight.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelNine.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelTen.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByOne.countryCodes=62,92,880
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByTwo.countryCodes=998,994,93
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByThree.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByFour.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByFive.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.denyList.countryCodes=223
iam.partnerIntegrations.vercelNative.webhook.clientSecrets=<SECRETMANAGER>
iam.partnerIntegrations.vercelNative.openInProvider.clientId=oac_jnzmjqM10gllKmSrG0SGrHOH
iam.partnerIntegrations.vercelNative.openInProvider.clientSecret=<SECRETMANAGER>
mms.accountSuspension.enabled=true
mms.accountDeletion.enabled=true
mms.sendAccountDeletionEmailEnabled=true
mms.enableNewAdminUsersPage=true
mms.enableCommentService=true
nds.acme.failover.lets_encrypt.eventThreshold=300
nds.acme.failover.gts.eventThreshold=50
nds.acme.failover.failoverEventLookbackMinutes=5
nds.acme.failover.retryIntervalMinutes=30
nds.acme.failover.retryResolutionIntervalMinutes=5
mms.acme.defaultProvider=LETS_ENCRYPT_V2
mms.acme.tracking.publicKey=
mms.acme.tracking.privateKey=
mms.addUserApi.countryRequired=true
mms.admin.queuedAdminActions.enabled=true
mms.admin.queuedAdminActions.ui.enabled=true
mms.admin.filterBackupJobs=true
mms.adminEmailAddr=<EMAIL>
mms.azCapacity.internalProjectId=67d1a07984bce60e40083d76
mms.securityEmailAddr=<EMAIL>
mms.agent.apiKeyReadPolicy=CACHED
mms.agentApiKey.cacheSnapshotMillis=30000
mms.agentCentralUrl=https://api-agents.mongodb.com
mms.alertFromEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertFromEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alertReplyToEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertReplyToEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alerts.AutomationAgentDown.maximumPingAgeMinutes=5
mms.alerts.BackingDatabaseProcessDown.connectTimeoutMs=10000
mms.alerts.BackupAgentDown.maximumPingAgeMinutes=15
mms.alerts.BackupDaemonLowFreeHeadSpace.frequency=0 0 * * * ?
mms.alerts.BackupJobTooBusy.frequency=0 0 0 * * ?
mms.alerts.BiConnectorAlert.maximumPingAgeMinutes=9
mms.alerts.ClusterMongosIsMissing.maximumPingAgeMinutes=9
mms.alerts.HostDiskSpaceInsufficientForSearchIndexRebuild.frequency=0 0 0/1 * * ?
mms.alerts.HostDown.maximumPingAgeMinutes=9
mms.alerts.HostExposed.addToNewGroups=true
mms.alerts.HostHasIndexSuggestions.frequency=0 * * * * ?
mms.alerts.InvoiceBilledDoesNotEqualLineItemsTotal.frequency=0 0 7 * * ?
mms.alerts.InvoiceBilledDoesNotEqualPaid.frequency=0 0 7 * * ?
mms.alerts.ServerlessProxiesStoppedReportingForMTM.minTenants=
mms.alerts.ServerlessProxiesStoppedReportingForMTM.maxTenantSample=
mms.alerts.ServerlessProxyDown.maximumPingAgeMinutes=7
mms.alerts.FlexProxiesStoppedReportingForMTM.minTenants=
mms.alerts.FlexProxiesStoppedReportingForMTM.maxTenantSample=
mms.alerts.FlexProxyDown.maximumPingAgeMinutes=7
mms.alerts.MonitoringAgentDown.maximumPingAgeMinutes=7
mms.alerts.RealmDown.maximumPingAgeMinutes=7
mms.alerts.MultipleInvoicesForMonth.frequency=0 0 7 * * ?
mms.alerts.NdsProxyDown.matchingGroupTag=ATLASMTM
mms.alerts.NdsProxyDown.maximumPingAgeMinutes=5
mms.alerts.OplogBehind.maximumOplogPushAgeMinutes=75
mms.alerts.PendingInvoiceOverThreshold.frequency=0 0 12 * * ?
mms.alerts.PrepaidPlanMissingSKU.frequency=0 0 7 * * ?
mms.alerts.ReplicaSetAlertCheck.maximumPingAgeMinutes=9
mms.alerts.StalePendingInvoices.frequency=0 0 7 * * ?
mms.alerts.SubscriptionUsageCronJobDidntRunLastHour.frequency=0 0 * * * ?
mms.alerts.SystemDatabaseProcessStartupWarnings.frequency=0 0 * * * ?
mms.alerts.TooManyPendingInvoices.frequency=0 0 7 * * ?
mms.alerts.EncryptionAtRestKMSNetworkAccessDenied.frequency=PT15M
mms.alerts.EncryptionAtRestConfigNoLongerValid.frequency=PT15M
mms.alerts.createSystemAlertsEnabled=true
mms.alerts.delaySeconds=120
mms.alerts.disabledEventTypes=USER_GROUP_CREATED,USER_GROUP_DELETED,USER_GROUP_MEMBER_ADDED,USER_GROUP_MEMBER_REMOVED,USER_GROUP_NAME_CHANGED,USER_GROUP_POLICIES_CHANGED,CUSTOM_SESSION_TIMEOUT_MODIFIED
mms.alerts.globalSummaryEmailIntervalHours=6
mms.alerts.intervalSeconds=120
mms.alerts.group.cron.intervalSeconds=60
mms.alerts.unreachable.lookBackMinutes=5
mms.alerts.unreachable.minimumConfCount=1000
mms.alerts.unreachable.thresholdPercent=90
mms.alerts.webhook.readTimeoutMs=5000
mms.alerts.webhook.socketTimeoutMs=2000
mms.alerts.GlobalServiceAccountSecretExpiredAlertCheck.frequency=PT12H
mms.alerts.GroupServiceAccountSecretsExpiring.frequency=PT12H
mms.atlasApiDeprecation.fromEmailAddr=<EMAIL>
mms.atlasApiDeprecation.replyToEmailAddr=<EMAIL>
mms.allowOpsManagerOrgs=false
mms.apiregistry.cronjob.enabled=true
mms.api.readPreference=secondaryPreferred
mms.api.throttling.enabled=true
mms.assets.browserErrorTracking=true
mms.assets.browserErrorTrackingApiKey=6666d88d5b732b7725a8107ba15b4eb6
mms.assets.assetsHostname=https://assets.mongodb-cdn.com/mms
mms.assets.minified=true
mms.assets.packaged=true
mms.atlas.backup.skipBillingOnOplogMigrationDestination=false
mms.atlas.backup.skipBillingOplogMigrationForAzureOplogStore=false
mms.atlas.backup.skipBillingOplogMigrationForGcpOplogStore=false
mms.atlas.defaultReplicaSetScalingStrategy=WORKLOAD_TYPE
mms.atlasAWSMinimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlas.gcp.minimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlasClusterWebsocketConnection.baseUrl=wss://atlas-cluster-ws.us-east-1.aws.cloud.mongodb.com
nds.serverless.sniproxy.default.count=3
mms.auth.logo.fileName=mdb_logo
mms.auth.methods.google.enabled=true
mms.auth.methods.google.oktaIdpId=0oa6p2il4dbB3bS2X297
mms.auth.methods.github.enabled=true
mms.auth.methods.github.oktaIdpId=0oad9crpx0cp3kueD297
mms.authn-service.serviceMeshEnvVar=KUBE_AUTHN_SERVICE_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
mms.cps.billing.cpsAwsDownloadUsageSubmission=true
mms.cps.billing.cpsAwsExportUsageSubmission=true
mms.cps.billing.cpsAwsSnapshotUsageSubmission=true
mms.cps.billing.cpsAzureDownloadUsageSubmission=true
mms.cps.billing.cpsAzureExportUsageSubmission=true
mms.cps.billing.cpsAzureSnapshotUsageSubmission=true
mms.cps.billing.cpsGcpDownloadUsageSubmission=true
mms.cps.billing.cpsGcpExportUsageSubmission=true
mms.cps.billing.cpsGcpSnapshotUsageSubmission=true
mms.cps.billing.cpsExportUsageSubmission=true
mms.cps.billing.oplogStorageRegionUsage=true
mms.cps.directAttachPreWarm.skip.gcp=true
mms.cps.billing.oplogUsageCollection=true
mms.cps.billing.oplogUsageSubmission=true
mms.cps.billing.oplogUsageMigration=true
mms.cps.collectionMetadataBucketRegionName=us-east-1
mms.cps.collectionMetadataBucketRegionNameSecondary=us-east-2
mms.cps.collectionMetadataBucket=atlas-backup-collection-metadata-us-east-1-prod
mms.cps.collectionMetadataBucketSecondary=atlas-backup-collection-metadata-us-east-2-prod
mms.cps.collectionRestore.diskSpaceThreshold=.90
mms.cps.metadatapruning.snapshot.maxPrunePerSecond=500.0
mms.cps.metadatapruning.snapshot.expireAfterDays=730
mms.cps.metadatapruning.snapshot.batchSize=100
mms.cps.metadatapruning.snapshot.disabled=false
mms.customer.metrics.exporter.diskingestable.usecaseId=296e3c80-f42c-51f1-9a84-5477e61d0277
mms.customer.metrics.exporter.diskingestable.publisherKey=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
mms.customer.metrics.exporter.diskingestable.drain.interval=500
mms.authz.enableAuthzSvc=true
mms.authz.roleSetParityCheck.percentage=-1
mms.authz.epaParityCheck.percentage=-1
mms.authz.syncResources=true
mms.authz.tags.timeoutMillis=100
mms.authz.syncTeamsToUserGroups=true
mms.authz.mandatoryResourceSync.percentage=-1
mms.userGroupFailureJob.retryDelayInMin=3
mms.userGroupFailureJob.numberRetries=3
mms.authz.rolesCache.enabled=true
mms.authz.fgaOrgIdsCacheDurationSeconds=10
mms.authz.fgaOrgIdsCache.enabled=true
mms.runFgaOverRbacWhenAnnotated=false
mms.automation.agentaudit.conf.allowPercent=40.0
mms.automation.agentaudit.conf.maxWriters=5
mms.automation.agentFeatures.migrationHosts.canManageDeployments=false
mms.automation.lastagentstatus.maxWriters=8
mms.automation.changes.pruning.retentionDays=30
mms.automation.changes.pruning.changesPerSecond=500
mms.automation.changes.pruning.maxPerRun=1000000
mms.automation.changes.watcher.enabled=false
mms.automation.publishable.writeconcern=replicasafederived
mms.automation.settings.refreshPeriodSecs=90
mms.automation.settings.dnsCacheTtlSeconds=300
mms.automation.settings.jobManager.refreshPeriodSecs=60
mms.automation.settings.sleepTimes.confCallSleepTimeSecs=15
mms.automation.settings.sleepTimes.errorStateSleepTimeSecs=15
mms.automation.settings.sleepTimes.goalStateSleepTimeSecs=15
mms.automation.settings.agentStatus.sendPeriodSecs=120
mms.automation.settings.agentStatus.maxPlansToSend=5
mms.automation.settings.timeBetweenMongosRollingRestartMs=20000
mms.automation.config.automation.sz.rcid.migration.sleepMillis=100
mms.featureFlag.backup.s3blockstore.calculateMD5=disabled
mms.featureFlag.backup.s3oplogstore.calculateMD5=disabled
mms.backup.alertsEmailAddr=<EMAIL>
mms.backup.allowDevelopmentVersions=false
mms.backup.allowFastSchedule=false
mms.backup.allowRefTimeOfDay=false
mms.backup.allowSnapshotExpiryUpdate=false
mms.backup.automatedRestoreExpirationHours=48
mms.backup.daemon.getApplyOpsJobBufferSize=8
mms.backup.daemon.getBoundInitialSyncJobBufferSize=8
mms.backup.daemon.snapshotQuery.addedMaxTimeMillis=-300000
mms.backup.daemon.s3groom.copyExecutorThreads=30
mms.backup.daemonAssignment.replSetSizeMultiple=1.3
mms.backup.daemonAssignment.replSetSizeMultiplePreferred=1.1
mms.backup.daemonAssignment.usableHeadPercent=0.80
mms.backup.daemonAssignment.usableHeadPreferredPercent=0.95
mms.backup.datastore.refresh=false
mms.backup.defaultFileListInsertBatchSize=2
mms.backup.donate.chosenJob.space.lower.threshold=0.04
mms.backup.donate.chosenJob.space.upper.threshold=0.13
mms.backup.donate.donor.space.threshold=0.90
mms.backup.gzipS3CompressionLevel=1
mms.backup.initialOplogTTLDays=30
mms.backup.logSnapshotFileInfo=false
mms.backup.maximumPITRestoreOplogWindowDays=3
mms.backup.metadata.BackupCursorDescriptionDao.writeconcern=replicasafederived
mms.backup.metadata.BackupCursorFileListDao.writeconcern=replicasafederived
mms.backup.metadata.SnapshotDao.writeconcern=replicasafederived
mms.backup.minimumOplogWindowHours=0
mms.backup.numBlockstoreWorkers=2
mms.backup.numS3BlockstoreWorkers=2
mms.backup.provisioningRequired=true
mms.backup.region.default=us-east-1
mms.backup.resourceUsageTracker.corePoolSize=6
mms.backup.resourceUsageTracker.maxPoolSize=6
mms.backup.s3oplogstore.pruneJobBatchSize=40
mms.backup.s3OplogStoreEnabled=true
mms.backup.snapshotValidation.kubernetesNamespace=snapshot-validation-prod
mms.backup.terminate.snapshotBatchSize=1
mms.backup.terminate.snapshotNoCursorTimeout=true
mms.backup.wiredTiger.engineConfig.cacheSizeGB=6
mms.backup.wtcheckpoint.clusterAbortDescriptionDelay=1209600
mms.backup.wtcheckpoint.clusterAbortSnapshotDelay=1209600
mms.backup.wtcheckpoints.saveWTSnapshotFilesBatchSize=1000
mms.backupCentralUrl=https://api-backup.mongodb.com
mms.backupPrivateLinkUrl=https://pl.api-backup.mongodb.com
mms.backupRestoreCentralUrl=https://restore-backup.mongodb.com
mms.backup.snapshotValidation.s3Bucket.prefix=snapshot-validation-prod
mms.backup.snapshotValidation.roleArnPrefix=arn:aws:iam::************:role/mms-bgrid-resources-prod/bgrid-snapshot-validation-rw-prod-aws
mms.backup.snapshotValidation.kubernetesClusterName=kube-1-%s-aws-cloud
mms.billing.alertsEnabled=true
mms.billing.awsBillingUsageImportThreads=48
mms.billing.awsBillingUsage.bulkWrite.maxConcurrencyReadingFiles=12
mms.billing.awsBillingUsage.bulkWrite.maxConcurrencyWritingBatches=8
mms.billing.awsBillingUsage.bulkWrite.batchSize=125
# Start of Billing Anomaly Detection properties
mms.billing.bad.authUrl=https://corp.mongodb.com/oauth2/aus4k4jv00hWjNnps297
mms.billing.bad.baseUrl=https://bad-prod-prod.dsplatform.prod.corp.mongodb.com
mms.billing.bad.scope=dsplatform-prod
mms.billing.bad.clientId=<SECRETMANAGER>
mms.billing.bad.secret=<SECRETMANAGER>
# --- End of Billing Anomaly Detection properties
mms.billing.bccEmailAddress=<EMAIL>
mms.billing.coreBIEmailAddress=<EMAIL>
mms.billing.enabled=true
mms.billing.salescomp.enabled=true
mms.billing.cronJobsDisabled=false
mms.billing.gcp.authProviderX509CertUrl=https://www.googleapis.com/oauth2/v1/certs
mms.billing.gcp.authUri=https://accounts.google.com/o/oauth2/auth
mms.billing.gcp.clientEmail=<EMAIL>
mms.billing.gcp.clientId=<SECRETMANAGER>
mms.billing.gcp.clientX509CertUrl=https://www.googleapis.com/robot/v1/metadata/x509/mdb-gcp-marketplace-service-ac%40mdb-gcp-marketplace.iam.gserviceaccount.com
mms.billing.gcp.privateKey=<SECRETMANAGER>
mms.billing.gcp.privateKeyId=<SECRETMANAGER>
mms.billing.gcp.projectId=mdb-gcp-marketplace
mms.billing.gcp.tokenUri=https://oauth2.googleapis.com/token
mms.billing.gcp.type=service_account
mms.billing.gcp.selfServeMetric=mongodb-atlas-self-service-1.endpoints.mdb-gcp-marketplace.cloud.goog/pay_as_you_go_usage
mms.billing.gcp.atlasPrivateMetric=mdb-gcp-marketplace-atlas.endpoints.mdb-gcp-marketplace.cloud.goog/atlas_private_usage
mms.billing.gcp.atlasPrepaidMetric=mdb-gcp-marketplace-atlas.endpoints.mdb-gcp-marketplace.cloud.goog/prepaid_atlas
mms.billing.gcp.selfServeProductName=mongodb-atlas-self-service-1.endpoints.mdb-gcp-marketplace.cloud.goog
mms.billing.gcp.atlasGovAnnualCommitMetric=mongodb-atlas-for-government-private-offer.endpoints.mdb-gcp-marketplace.cloud.goog/Mongodb_atlas_gov
mms.billing.gcp.atlasGovPrepaidMetric=mongodb-atlas-for-government-private-offer.endpoints.mdb-gcp-marketplace.cloud.goog/Mongodb_atlas_gov_prepaid_atlas
mms.billing.gcp.marketplaceUrl=https://console.cloud.google.com
mms.billing.aws.mp.accessKey=<SECRETMANAGER>
mms.billing.aws.mp.secretKey=<SECRETMANAGER>
mms.billing.aws.mp.athena.accessKey=<SECRETMANAGER>
mms.billing.aws.mp.athena.secretKey=<SECRETMANAGER>
mms.billing.aws.mp.athena.database=mdb-awsmp-data-feeds
mms.billing.aws.mp.athena.executionTimeoutMillis=60000
mms.billing.aws.mp.athena.kmsKeyArn=arn:aws:kms:us-east-1:711489243244:key/86c993d7-1baa-4b27-ae08-64272b408dbf
mms.billing.aws.mp.athena.kmsKeyType=SSE_KMS
mms.billing.aws.mp.athena.maxPageSize=200
mms.billing.aws.mp.athena.outputBucket=s3://mdb-athena-results-data-feeds/prod/
mms.billing.aws.mp.athena.region=us-east-1
mms.billing.aws.mp.athena.workgroup=primary
mms.billing.aws.mp.disbursement.crons.enabled=true
mms.billing.azure.mp.azureNative.service.marketplaceHost=https://mongodb-billing.gateway.azliftr.io/api/billing
mms.billing.azure.mp.aad.azureNative.service.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.azureNative.service.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.secretKey.expiration=2025-04-18
mms.billing.azure.mp.aad.service.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.service.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.service.secretKey.expiration=2025-04-18
mms.billing.azure.mp.aad.productGraph.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.productGraph.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.productGraph.secretKey.expiration=2025-04-18
mms.billing.azure.mp.corsUrls=https://login.live.com,https://login.microsoftonline.com,https://partner.microsoft.com,https://portal.azure.com
mms.billing.azure.mp.saas.partnerTeam.webhookUrl=https://us-central1-gcp-pov.cloudfunctions.net/azure-marketplace-webhook
mms.billing.azure.export.api.storage.account.name=atlasbilling
mms.billing.azure.export.api.clientId=cba167e2-60a5-4977-9e14-22a7a86e3b13
mms.billing.azure.export.api.tenantId=5537e8ff-e2ba-475a-a67a-daa445542246
mms.billing.azure.export.api.client.secretKey=<SECRETMANAGER>
mms.billing.azure.export.api.blob.container.name=atlasbilling
mms.billing.azure.export.destinationS3Bucket=
mms.billing.internalPaymentCheck=false
mms.billing.lowPrepaidCreditEmailAddress=<EMAIL>
mms.billing.promoCodeCreatedEmailAddress=<EMAIL>
mms.billing.retrieveStripeEvents=true
mms.billing.selfServePayments.enabled=true
mms.billing.selfServeProducts=seed-data/SelfServeProducts.json
mms.billing.sendInvoiceEmails=true
mms.billing.sfdcProductCodes=seed-data/SalesforceProductCodes.json
mms.billing.sfscSyncEnabled=true
mms.billing.sfdc.user.sync.partition.size=1000
mms.billing.statusPage.apiKey=<SECRETMANAGER>
mms.billing.updatePayment.ratelimit.attemptsAllowed=5
mms.billing.updatePayment.ratelimit.lockedPeriodMinutes=10
mms.billing.invoicingStatusEmailAddress=<EMAIL>
mms.billing.dataExportConfig=data-export/data-export-conf.json
mms.billing.numThreadsForCrossOrgBilling=5
mms.billing.crossOrgBillingThreadAwaitTimeOut=60
mms.billing.auditors.auditEnabled=true
mms.billing.auditors.config=seed-data/BillingAuditorConfigs.json
mms.billing.auditors.jiraIntegrationEnabled=true
mms.billing.internalReportRecipientEmail=<EMAIL>,<EMAIL>
mms.billing.internalBillingPlatformRecipientEmail=<EMAIL>
mms.billing.metering.submissionJob.numUsageFetchingThreads=15
mms.billing.sendDailyBillingReport=true
mms.billing.lineItemMaterializedViewJob.enabled=true
mms.billing.statusPage.enabled=true
mms.billing.partners.aws.reporting.mode=ALL
mms.billing.partners.azure.reporting.mode=ALL
mms.billing.partners.gcp.reporting.mode=ALL
mms.billing.pricingApiConsumers=
mms.billing.resourceTagRetrievalBatchSize=500
mms.billing.dailyBilling.batchSize=125
mms.billing.dailyBilling.spreadInMs=0
mms.billing.orgIdsToAlwaysLog=5b71ff2f96e82120d0aaec14,599eec2c9f78f769464d077f,599eecf19f78f769464d17c7,636428d55f17cf1ed9cb7e8f,5e3d2861f2a30b7a5550bc5d,5e3d28b1a6f239333d6f50d6,59c9437e0bd66b042d30dae0,5e679e5d8734db1b2ff6affa,5e3d282af2a30b7a5550ba25,5e3d2905a6f23933204aeb9f,61714ca48522c01863113b76,620d23fefade5a2576690610,5f402d3ea8438a527236d46c,60830c757afae3585e28259d,5eb4b8a313670469ad201fd9,5f3ee1af6d78da38d44666a9,5f40294f79374520db699a42,5c900681014b760c65e3ec1e,624afcd3663da2329fa85647,62425a0da9f6a0109c9c6e77,62552ccf29bf907f60772884,62df96dd617d9f71d2b067df,5cf9365579358e1da6cc2f6d,636a04a051dfb578e5e56366,5a3b7c62df9db1337744e327,6350654c6ea33c710e8a004f,63a24b94fc2a9116991630df,63a24b3b198ef05c11e50ae5,63d22a758a7de4083dcda2dd,63d22b1f8fe4ef3b0c5706ba,630e325423173b37c529a19a
mms.billing.featureteam.lineitem.rateLimitPerMinute=30
mms.billing.featureteam.lineitem.sku.size=50
mms.billing.featureteam.lineitem.group.size=5
mms.billing.featureteam.lineitem.startdate.size=5
mms.billing.featureteam.lineitem.usagedate.size=5
mms.costExplorer.costExplorerAggregateCreationJob.creationTimeframeInMonths=1
mms.billingImport.azure.readUser.clientId=cba167e2-60a5-4977-9e14-22a7a86e3b13
mms.billingImport.azure.readUser.tenantId=5537e8ff-e2ba-475a-a67a-daa445542246
mms.billingImport.azure.readUser.clientSecret=<SECRETMANAGER>
mms.billingImport.azure.blobContainer.name=mongodb-atlas-billing
mms.billingImport.azure.blobContainer.accountName=mongodbatlasbilling
mms.billingImport.azure.blobContainer.reportDirectory=billing-profile/mongodb-atlas-billing-v2/
mms.billingImport.azure.useLegacyBillingReportDate=true
mms.metering.featureteam.usage.rateLimitPerMinute=30
mms.metering.featureteam.usage.meterid.size=50
mms.metering.featureteam.usage.groupid.size=5
mms.metering.featureteam.usage.reporteddate.size=5
mms.metering.featureteam.usage.startdate.size=5
mms.metering.purge.batchSize=2600
mms.metering.purge.maxRuntimeSeconds=120
mms.metering.purge.sleepBetweenDeletionMillis=1000
mms.migration.path.verify=false
mms.payments.paymentMethods.grpcCalls.getActivePaymentMethod.enabled=false
mms.payments.chargePayments.maxConcurrency=256
mms.payments.chargePayments.invoiceLock.retries.maxAttempts=20
mms.payments.chargePayments.invoiceLock.retries.minBackoff=2000
mms.payments.chargePayments.invoiceLock.retries.maxBackoff=15000
mms.payments.opportunityChange.auth.jira.uri=https://jira.mongodb.org
mms.payments.opportunityChange.auth.jira.privateAccessToken=<SECRETMANAGER>
mms.payments.revrec.email.selfServe.recipientAddress=<EMAIL>
mms.payments.revrec.email.salesSold.recipientAddress=<EMAIL>
mms.payments.revrec.email.subject.prefix=
mms.payments.revrec.maxConcurrency=256
mms.payments.revrec.query.maxConcurrency=40
mms.payments.revrec.query.batchSize=250
mms.payments.yaypay.ui.baseUrl=https://app.yaypay.com
mms.payments.yaypay.baseUrl=https://app.yaypay.com/api/v2
mms.payments.yaypay.inc.clientKey=<SECRETMANAGER>
mms.payments.yaypay.inc.clientSecret=<SECRETMANAGER>
mms.payments.yaypay.inc.scope=ar.read,customer.read,crm.read
mms.payments.yaypay.ltd.clientKey=<SECRETMANAGER>
mms.payments.yaypay.ltd.clientSecret=<SECRETMANAGER>
mms.payments.yaypay.ltd.scope=ar.read,customer.read,crm.read
mms.payments.yaypay.brazil.clientKey=<SECRETMANAGER>
mms.payments.yaypay.brazil.clientSecret=<SECRETMANAGER>
mms.payments.yaypay.brazil.scope=ar.read,customer.read,crm.read
mms.payments.tax.vatDisabledCountries=CL,JP
mms.payments.internalNetwork.maxRequestsPerSecond=100
mms.payments.opensanctions.enabled=true
mms.payments.opensanctions.apikey=<SECRETMANAGER>
mms.payments.opensanctions.maxRetryAttempts=5
mms.payments.vercel.api.maxRetries=5
mms.payments.vercel.api.minBackoffMillis=2000
mms.payments.vercel.billing.maxRetries=5
mms.payments.vercel.billing.minBackoffMillis=2000
mms.payments.vercel.billing.accessTokenKey=<SECRETMANAGER>
mms.payments.vercel.billingInstallation.ratelimit.enabled=true
mms.payments.vercel.billingInstallation.ratelimit.suspendableIp.maxHitsPerPeriod=6000
mms.payments.vercel.billingInstallation.ratelimit.suspendableIp.minutesPeriod=1
mms.payments.vercel.billingInstallation.ratelimit.pathParam.maxHitsPerPeriod=600
mms.payments.vercel.billingInstallation.ratelimit.pathParam.minutesPeriod=1
mms.payments.vercel.billingInstallation.ratelimit.pathParam.parameterName=installationId
mms.payments.vercel.billingProduct.ratelimit.enabled=true
mms.payments.vercel.billingProduct.ratelimit.suspendableIp.maxHitsPerPeriod=6000
mms.payments.vercel.billingProduct.ratelimit.suspendableIp.minutesPeriod=1
mms.payments.vercel.webhook.ratelimit.enabled=true
mms.payments.vercel.webhook.ratelimit.suspendableIp.maxHitsPerPeriod=6000
mms.payments.vercel.webhook.ratelimit.suspendableIp.minutesPeriod=1
mms.payments.credits.creation.rolloverAmbiguity.email.recipient=<EMAIL>
mms.payments.credits.creation.rolloverAmbiguity.email.replyTo=<EMAIL>
mms.payments.credits.creation.rolloverAmbiguity.email.subjectTemplate=Rollover ambiguity auto-resolved for opportunity %s
mms.payments.mcDiscrepancies.auditors.cutoffDate=2025-09-01
mms.centralUrl=https://cloud.mongodb.com
mms.centralDomainAllowedOrigin=https://cloud.mongodb.com
mms.charts.exportChartsDashboard.ratelimit.enabled=true
mms.charts.exportChartsDashboard.ratelimit.maxHitsPerPeriod=50
mms.charts.exportChartsDashboard.ratelimit.minutesPeriod=10
mms.charts.importChartsDashboard.ratelimit.enabled=true
mms.charts.importChartsDashboard.ratelimit.maxHitsPerPeriod=50
mms.charts.importChartsDashboard.ratelimit.minutesPeriod=10
mms.clientmetrics.rateLimitMaxPerUser=60
mms.cloudManagerEnabled=true
mms.cron.enabled=false
mms.cron.track.ttlDays=558
mms.dao.backup.logDao.ttlHours=125
mms.dao.backup.agentLogDao.ttlHours=6
mms.dao.capped.apiRequestTrackDao.sizeInBytes=1073741824
mms.dao.capped.default.sizeInBytes=1073741824
mms.dao.capped.metricAlertDispatchCounterDao.sizeInBytes=1073741824
mms.dao.capped.mlabSharedMigrationLogDao.sizeInBytes=10737418240
mms.dao.capped.ndsMongoMirrorLogDao.sizeInBytes=26843545600
mms.dao.capped.ndsTenantUpgradeLogDao.sizeInBytes=53687091200
mms.databaseIdpDiscovery.enabled=true
mms.databaseIdpDiscovery.mongoOktaId=0oa1rmcoy4lK9isNt297
mms.dataExplorer.rateLimit.enabled=false
mms.dataExplorer.rateLimit.cache.enabled=true
mms.deploymentdiscovery.enabled=false
mms.docsUrl.nds=https://www.mongodb.com/docs/atlas
mms.docsUrl.search=https://www.mongodb.com/docs/search
mms.docsUrl=https://www.mongodb.com/docs/cloud-manager
mms.email.includeAppEnvName=false
mms.email.logo.force=false
mms.email.logo.height=35
mms.email.logo.width=140
mms.email.logo=/static/images/logo-mongodb.png
mms.email.ttlMinutes=86400
mms.email.validation=STRICT
mms.emailDaoClass=com.xgen.cloud.email._private.dao.AwsEmailDao
mms.emailVerificationEnabled=true
mms.enableOldHostCheck=true
mms.event.sns.awsAccessKey=<SECRETMANAGER>
mms.event.sns.awsRegion=us-east-1
mms.event.sns.awsSecretKey=<SECRETMANAGER>
mms.event.sns.enabled=true
mms.event.sns.topic.CLUSTER_DELETE_SUBMITTED=arn:aws:sns:us-east-1:************:MMS_NDS_PROD_CLUSTER_DELETE_SUBMITTED
mms.event.sns.topic.MATERIAL_CLUSTER_UPDATE_COMPLETED_INTERNAL=arn:aws:sns:us-east-1:************:MMS_NDS_PROD_MATERIAL_CLUSTER_UPDATE_COMPLETED_INTERNAL
mms.event.sns.topic.CHARTS_RELEVANT_CLUSTER_EVENT_INTERNAL=arn:aws:sns:us-east-1:************:CHARTS_RELEVANT_CLUSTER_EVENT_INTERNAL_PROD
mms.event.event-service.writes.enabled=true
mms.event.event-service.write.sync=false
mms.event.event-service.eventTesting.enabled=false
mms.events.sns.awsRole=arn:aws:iam::************:role/mms-event-publisher-prod
mms.event.errorOnValidationFailure=false
mms.feature.enablePauseFreeTierMonitoring=true
mms.featureFlag.adfa.allowRoutingViaCRMInsteadOfEnvoy=enabled
mms.featureFlag.adl.readConcernMajority=controlled
mms.featureFlag.adl.schemaUniqueFieldLimit=controlled
mms.featureFlag.adl.templateRegexGenerationOptimization=controlled
mms.featureFlag.adl.testManualControlledFlag=disabled
mms.featureFlag.adl.testAutomatedControlledFlag=disabled
mms.featureFlag.adl.testEnabledFlag=disabled
mms.featureFlag.adl.testDisabledFlag=disabled
mms.featureFlag.adl.useMongodInsteadOfMqlrun=enabled
mms.featureFlag.alertStateFlappingDetection=controlled
mms.featureFlag.atlasAllowDeprecatedVersions=disabled
mms.featureFlag.atlasMongoDB80=enabled
mms.featureFlag.atlasAutoApplyIndexesForMTMs=controlled
mms.featureFlag.atlasAzureNVMe=enabled
mms.featureFlag.atlasCrossCloudReplication=enabled
mms.featureFlag.atlasCrossRegionPeering=controlled
mms.featureFlag.atlasDataFederationTemplatedOnboarding=enabled
mms.featureFlag.atlasDataFederationAzureFeedDownstreamSystems=disabled
mms.featureFlag.atlasDataFederationAzurePrivateLink=controlled
mms.featureFlag.atlasDeploysUIS=disabled
mms.featureFlag.atlasSearchResolveViews=enabled
mms.featureFlag.atlasGatewayProxy=controlled
mms.featureFlag.atlasMaintenanceWindows=controlled
mms.featureFlag.atlasMountAzureDiskWithLun=controlled
mms.featureFlag.atlasAzureForceMigrationToAZs=controlled
mms.featureFlag.atlasServerlessGRPCSupport=controlled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettings=disabled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettingsOrg=disabled
mms.featureFlag.atlasProxyDisableRateLimiting=disabled
mms.featureFlag.atlasServerlessUsesServerlessAgent=disabled
mms.featureFlag.atlasEnableTestCommands=disabled
mms.featureFlag.atlasSharedTierX509Auth=controlled
mms.featureFlag.atlasX509CRL=controlled
mms.featureFlag.atlasSearch.dedicatedNodes=enabled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreference=controlled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreferenceGroupOverride=controlled
fts.search.decoupledClusterUriEnabledDeploymentSizes=All,Sharded
mms.featureFlag.atlasSearch.synchronousSteadyStateReplication=controlled
fts.search.coupledClusterUriEnabledClusterSizes=All,Sharded
mms.featureFlag.atlasSearch.indexManagement=enabled
mms.featureFlag.atlasSearch.indexPartitioningUI=controlled
mms.featureFlag.atlasSearch.vibCustomAnalyzersUI=enabled
mms.featureFlag.atlasSearch.confCallViaSearchGateway=controlled
mms.featureFlag.atlasSearch.disableDetailedStatusWrite=controlled
mms.featureFlag.atlasSearch.searchTesterQueryTemplates=disabled
mms.featureFlag.atlasSearch.searchIndexStatusReporting=controlled
mms.featureFlag.atlasSearch.dedicatedNodesAWSSeriesSeven=controlled
mms.featureFlag.atlasSearch.dedicatedNodesEnableReplicationCompression=controlled
mms.featureFlag.atlasSearch.dedicatedNodesForceDisableReplication=disabled
mms.featureFlag.atlasSearch.dedicatedNodesDisableProvisionTimeout=controlled
mms.featureFlag.atlasSearch.useAggregateCommand=enabled
mms.featureFlag.atlasSearch.multipleSubIndexes=controlled
mms.featureFlag.atlasSearch.staleIndexes=controlled
mms.featureFlag.atlasSearch.byok=disabled
mms.featureFlag.atlasSearch.useLifecycleManager=controlled
mms.featureFlag.atlasSearch.envoy.disableStreamIdleTimeout=controlled
mms.featureFlag.atlasSearch.envoy.loadBalancingPolicyRandom=controlled
mms.featureFlag.atlasSearch.indexConfigStatsWrites=disabled
mms.featureFlag.atlasSearch.searchHostStatsWrites=disabled
mms.featureFlag.atlasSearch.readIndexStatsFromNewCollection=disabled
mms.featureFlag.atlasSearch.enableTextOperatorNewSynonymsSyntax=controlled
mms.featureFlag.atlasSearch.indexConfigUpdater=disabled
mms.featureFlag.atlasSearch.disableMongotJvmNativeAccess=controlled
mms.featureFlag.atlasSearch.indexQuantizationUI=controlled
mms.featureFlag.atlasSearch.mongotPushBasedLogExportDownload=disabled
mms.featureFlag.atlasSearch.dedicatedNodesGrpcMode=disabled
mms.featureFlag.atlasDataLakeStorageForOnlineArchive=disabled
mms.featureFlag.atlasDataLakeStorageForceV2UpgradeForOnlineArchive=controlled
mms.featureFlag.atlasDataLakeIngestionPipelinesDeprecation=enabled
mms.featureFlag.atlasDataLakeStorageAllowHourlyIngestion=controlled
mms.featureFlag.atlasDataLakeStorageDatasetRetentionPolicy=enabled
mms.featureFlag.atlasDataFederationDedicatedHostnames=enabled
mms.featureFlag.atlasDataFederationOnGCP=enabled
mms.featureFlag.atlasCloudProviderAccessForGCP=enabled
mms.featureFlag.atlasOnlineArchiveV3TimeSeries=enabled
mms.featureFlag.atlasOnlineArchiveGCP=enabled
mms.featureFlag.atlasOnlineArchiveV1=controlled
mms.featureFlag.atlasOnlineArchiveAsDataSource=enabled
mms.featureFlag.atlasOnlineArchiveIgnoreDataSizeThreshold=controlled
mms.featureFlag.atlasOnlineArchiveEncryption=controlled
mms.featureFlag.atlasOnlineArchivePinToPrimaryRegion=controlled
mms.featureFlag.atlasPushBasedLogExport=enabled
mms.featureFlag.atlasPushBasedLogExportClusterLevel=disabled
mms.featureFlag.atlasGovGcpAssuredWorkloads=disabled
mms.featureFlag.atlasUseDataExplorerService=disabled
mms.featureFlag.atlasUseProviderSubdomains=disabled
mms.featureFlag.atlasUseLegacyHostnameScheme=disabled
mms.featureFlag.dbAccessDuringTenantUpgradeBlocked=enabled
mms.featureFlag.dbAccessDuringTenantUpgradeAllowed=controlled
mms.featureFlag.atlasMultiCloudGlobalClusters=enabled
mms.featureFlag.atlasMaintenanceAutoDeferral=controlled
mms.featureFlag.atlasCNRegionsOnly=controlled
mms.featureFlag.atlas50Cluster=enabled
mms.featureFlag.atlasChurnSurvey=enabled
mms.featureFlag.atlasHideQuickAccessPage=controlled
mms.featureFlag.atlasDeferServerNodeTypeTagUntilPhase2=enabled
mms.featureFlag.atlasDailyBigQueryBilling=enabled
mms.featureFlag.atlasHaltGroupLogIngestion=controlled
mms.featureFlag.atlasForceSkipFastProvision=controlled
mms.featureFlag.atlasAutoRetrieveDraftClusterConfig=enabled
mms.featureFlag.awsGraviton=enabled
mms.featureFlag.awsIntelOverGraviton=controlled
mms.featureFlag.gcpInstanceOSAL2=enabled
mms.featureFlag.singleTargetServerlessDeployment=controlled
mms.featureFlag.autoIndexing=controlled
mms.featureFlag.allowVulnerabilityScanning=disabled
mms.featureFlag.automation.changeListener=controlled
mms.featureFlag.automation.configPublishChangeEvent=controlled
mms.featureFlag.automation.configForAgentReturnsOnlyLocal=disabled
mms.featureFlag.automation.chefConfigChangeEvent=enabled
mms.featureFlag.automation.exposeEditorAutofill=disabled
mms.featureFlag.automation.mongoDevelopmentVersions=controlled
mms.featureFlag.automation.sentry=disabled
mms.featureFlag.automation.staggerMongosRollingRestart=controlled
mms.featureFlag.automation.newDeploymentServerUI=enabled
mms.featureFlag.automation.rollingIndexes=controlled
mms.featureFlag.automation.verifyDownloads=disabled
mms.featureFlag.automation.proxyConfigChangeEvent=enabled
mms.featureFlag.automation.enableV6=enabled
mms.featureFlag.automation.enableV7=enabled
mms.featureFlag.automation.enableV8=enabled
mms.featureFlag.automation=controlled
mms.featureFlag.automation.enableRamiAgent=disabled
mms.featureFlag.automation.enableMongotune=enabled
mms.featureFlag.mongotune.enableWriteBlockPolicy=controlled
nds.mongotune.writeBlockPolicy.enableForNewGroups=true
nds.mongotune.enableForNewGroups=true
nds.mongotune.plannerCheckConfigUpdate=true
nds.mongotune.maasDataSource=aerx0wcgz6ha8c
mms.featureFlag.payments.partners.aws.usageReportCreation=controlled
mms.featureFlag.payments.partners.azure.usageReportCreation=controlled
mms.featureFlag.payments.partners.gcp.usageReportCreation=controlled
mms.featureFlag.payments.salesSoldWarningOrgPaymentStatus=enabled
mms.featureFlag.payments.standaloneCalls.revRec=disabled
mms.featureFlag.awsPrivateEndpoint.waitingForUser=controlled
mms.featureFlag.baas=enabled
mms.featureFlag.baas.billingMigration=controlled
mms.featureFlag.baas.hostingDeprecated=enabled
mms.featureFlag.baas.graphqlDeprecated=enabled
mms.featureFlag.backup.allowEditOplogWindow=controlled
mms.featureFlag.backup.allowWhitelists=enabled
mms.featureFlag.backup.continuousBackupAllowedForNewAWSClusters=controlled
mms.featureFlag.backup.cpsResurrectWithRetainedBackups=enabled
mms.featureFlag.backup.cpsBackupLockMVP=controlled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGA=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGADisablePolicy=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicy2PersonDisablement=controlled
mms.featureFlag.backup.cpsConcurrentSnapshots=enabled
mms.featureFlag.backup.cpsDirectAttach=enabled
mms.featureFlag.backup.cpsDirectAttachOff=controlled
mms.featureFlag.backup.cpsEmbeddedConfig=enabled
mms.featureFlag.backup.cpsEmbeddedConfigUi=enabled
mms.featureFlag.backup.cpsExtendedSnapshotRetry=controlled
mms.featureFlag.backup.cpsExtraOpenBackupCursorTimeout=controlled
mms.featureFlag.backup.useNoWholeFileRsync=controlled
mms.featureFlag.backup.cpsNoWholeFileRsyncOff=enabled
mms.featureFlag.backup.cpsRestoreSearchIndex=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAws=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsMigration=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsNewCMK=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectGCP=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAzure=enabled
mms.featureFlag.backup.cpsOptimizedDaRestoreAws=controlled
mms.featureFlag.backup.cpsOptimizedDaRestoreAzure=disabled
mms.featureFlag.backup.cpsOptimizedDaRestoreGcp=controlled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAws=controlled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAzure=controlled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapGcp=controlled
mms.featureFlag.backup.cpsAwsDaRestoreWithIo2=controlled
mms.featureFlag.backup.cpsDownloadEncryptedSnapshot=enabled
mms.featureFlag.backup.cpsGcpAndAzureNewClustersOnlyCps=enabled
mms.featureFlag.backup.cpsPv2InstantRestoreOff=controlled
mms.featureFlag.backup.cpsPv2StreamingRestore=enabled
mms.featureFlag.backup.cpsSnapshotExportHighFrequency=enabled
mms.featureFlag.backup.cpsSnapshotExportUi=controlled
mms.featureFlag.backup.cpsSnapshotExportAzure=enabled
mms.featureFlag.backup.cpsSnapshotConsistentExport=controlled
mms.featureFlag.backup.cpsSnapshotConsistentExportSplitLargeCollections=controlled
mms.featureFlag.backup.cpsSnapshotDistributionAws=enabled
mms.featureFlag.backup.cpsSnapshotDistributionGcp=enabled
mms.featureFlag.backup.cpsSnapshotDistributionAzure=enabled
mms.featureFlag.backup.cpsSnapshotDistributionUi=enabled
mms.featureFlag.backup.cpsSnapshotDistributionLargeRegionSet=controlled
mms.featureFlag.backup.cpsGcpIncrementalCopySnapshots=disabled
mms.featureFlag.backup.cpsSnapshotAWSPrivateDownload=enabled
mms.featureFlag.backup.cpsSnapshotAzurePrivateDownload=enabled
mms.featureFlag.backup.turnCpsSnapshotAWSPrivateDownloadOff=controlled
mms.featureFlag.backup.turnCpsSnapshotAzurePrivateDownloadOff=controlled
mms.featureFlag.backup.cpsSkipSystemClusterDestroy=disabled
mms.featureFlag.backup.cpsSystemProjectsForExports=controlled
mms.featureFlag.backup.cpsSystemProjectsForDataLakeIngestionPipelineExports=controlled
mms.featureFlag.backup.cpsOplogInGcp=enabled
mms.featureFlag.backup.cpsOplogInGcpExtendMigration=disabled
mms.featureFlag.backup.cpsOplogMigration=enabled
mms.featureFlag.backup.cpsOplogInAzure=enabled
mms.featureFlag.backup.cpsSuccessiveUpgradeQuarterly=enabled
mms.featureFlag.backup.cpsLegacyBackupMigration=enabled
mms.featureFlag.backup.incrementalWtEnabled=controlled
mms.featureFlag.backup.incrementalWtFullSnapshotDayOfWeek=controlled
mms.featureFlag.backup.multipleWorkersPerFile=enabled
mms.featureFlag.backup.parallelRestores=controlled
mms.featureFlag.backup.directS3Restore=controlled
mms.featureFlag.backup.queryable=enabled
mms.featureFlag.backup.queryableFsCache=enabled
mms.featureFlag.backup.s3OplogStoreInOm=controlled
mms.featureFlag.backup.storageEngine=enabled
mms.featureFlag.backup.thirdPartyManaged=disabled
mms.featureFlag.backup.thirdPartyWithManagedOplog=disabled
mms.featureFlag.backup.v2=controlled
mms.featureFlag.backup.wt.queryable=enabled
mms.featureFlag.backup.wtBackpressure=controlled
mms.featureFlag.backup.wtConcurrentGrooms=enabled
mms.featureFlag.backup.wtConcurrentMongoBlockstoreGrooms=disabled
mms.featureFlag.backup.wtEncryptionAtRest=controlled
mms.featureFlag.backup.wtLocalKeyFile=controlled
mms.featureFlag.backup.wtLocalKeyFile.forOrg=controlled
mms.featureFlag.backup.wtNamespaceFiltering=enabled
mms.featureFlag.backup.enableAWSPrivateLinkOption=controlled
mms.featureFlag.backup.optimalSuccessiveUpgradeForRestoreEnabled=disabled
mms.featureFlag.backup.pGzipEnabled=controlled
mms.featureFlag.biConnector=controlled
mms.featureFlag.backup.snapshot.onDemand=disabled
mms.featureFlag.charts=enabled
mms.featureFlag.chartsActivationOptimization=enabled
mms.featureFlag.classicApiAccess=controlled
mms.featureFlag.classicCharts=controlled
mms.featureFlag.classicDashboard=controlled
mms.featureFlag.clientMetadataCollection=controlled
mms.featureFlag.clusterReferencesAutocorrect=enabled
mms.featureFlag.commentServiceEnabled=controlled
mms.featureFlag.crossOrgBilling=enabled
mms.featureFlag.customerFederation=enabled
mms.featureFlag.pagerduty.includeMaintenanceWindowStatus=controlled
mms.featureFlag.dataExplorer=controlled
mms.featureFlag.dataExplorerAggregation=controlled
mms.featureFlag.dataExplorerCrud=controlled
mms.featureFlag.dataExplorerMultiTenant=controlled
mms.featureFlag.extendMaxAllowedDiskSizes=disabled
mms.featureFlag.hostMappingsAutocorrect=controlled
mms.featureFlag.installAgentScript=disabled
mms.featureFlag.streamsAuditLogs=enabled
mms.featureFlag.streamsEnabled=enabled
mms.featureFlag.managedSlowMs=controlled
mms.featureFlag.m10ShardedClusters=controlled
mms.featureFlag.mongoDBAccessHistory=controlled
mms.featureFlag.monitoring.dataDogMetrics=controlled
mms.featureFlag.monitoring.dataDogDbAndCollMetrics=enabled
mms.featureFlag.monitoring.newRelic=disabled
mms.featureFlag.monitoring.shardedClusterNamespaceMetrics=enabled
mms.featureFlag.monitoring.namespaceQueryLatencyMetrics=controlled
mms.featureFlag.monitoring.skipUpdateClustersDuringLiveMigration=controlled
mms.featureFlag.monthlyUsageMetricsEmail=controlled
mms.featureFlag.ndsClusters=controlled
nds.serverless.mergeStrategy.cpuUtilizationThreshold=0.01
mms.featureFlag.billingDistributedLinkedOrgBilling=controlled
mms.featureFlag.billing.testMeterDataGeneration=disabled
mms.featureFlag.billingClusterTaggingExport=enabled
mms.featureFlag.maxAggregatedDiskChartsForAtlas=controlled
mms.featureFlag.opLatency=enabled
mms.featureFlag.opsManagerConfigForGlobalMonitoringAdmin=enabled
mms.featureFlag.paBugsAndSustainability=controlled
mms.featureFlag.paFeedback=controlled
mms.featureFlag.performanceAdvisor=controlled
mms.featureFlag.historicalReplicaState=controlled
mms.featureFlag.profilerNDS=controlled
mms.featureFlag.profilerv2=controlled
mms.featureFlag.profilerv3=controlled
mms.featureFlag.prometheus=enabled
mms.featureFlag.disablePremiumDatadog=controlled
mms.featureFlag.serverlessAutoIndexing=controlled
mms.featureFlag.enableFineGrainedAuth=disabled
mms.featureFlag.fineGrainedAuth.userGroups=disabled
mms.featureFlag.serviceAccountManagement=enabled
mms.featureFlag.enableScramSha256Auth=controlled
mms.FeatureFlag.prometheus.allowCustomerRateLimitConfig=controlled
mms.featureFlag.realtime=controlled
mms.featureFlag.realmMetrics=enabled
mms.featureFlag.realmMetricsAlerts=enabled
mms.featureFlag.rsyncBasedHeadCreation=disabled
mms.featureFlag.schemaAdvisor=controlled
mms.featureFlag.segmentGroupSynchronizer=enabled
mms.featureFlag.selfServeConsultingUnits=enabled
mms.featureFlag.standardFreeTier=controlled
mms.featureFlag.teams=disabled
mms.featureFlag.unknown=controlled
mms.featureFlag.atlasContinuousDelivery=enabled
mms.featureFlag.realmEvents=enabled
mms.featureFlag.realm.syncBeta=enabled
mms.featureFlag.failedRollingIndexCleanup=enabled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToSingleShard=disabled
mms.featureFlag.atlasLiveMigrateShardedHorizons=controlled
mms.featureFlag.atlasLiveMigrateOlderVersionSupport=enabled
mms.featureFlag.atlasLiveMigrateOneNinePreviewSupport=disabled
mms.featureFlag.atlasLiveMigrateLiftOplogLagRestriction=disabled
mms.featureFlag.atlasLiveMigrateRunsOnlyOnAL2Hosts=enabled
mms.featureFlag.atlasMigrationHubMilestoneOne=enabled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToShardedCluster=enabled
mms.featureFlag.atlasLiveMigrateMongosyncPushBasedReplicasetToShardedCluster=enabled
mms.featureFlag.atlasPushLiveMigrationsMongosyncPrivateEndpoints=enabled
mms.featureFlag.atlasLiveMigrateMongosyncVariableShardCount=enabled
mms.featureFlag.atlasLiveMigrateMongosyncEmbeddedVerifier=enabled
mms.featureFlag.atlasLiveMigrateDestinationOplogValidationForVerifier=enabled
mms.featureFlag.performanceAdvisorRecommendSearch=controlled
mms.featureFlag.uiAccessList=controlled
mms.featureFlag.tokenizedQueryShapeStatsMetrics=controlled
mms.featureFlag.atlasSearchQueryTelemetry=controlled
mms.featureFlag.randomSamplingSlowLogStreamer=enabled
mms.featureFlag.datadogCustomEndpoint=controlled
mms.featureFlag.resourceTagComponent.clusters=enabled
mms.featureFlag.resourceTagComponent.projects=enabled
mms.featureFlag.enableExperimentHoldouts=enabled
mms.featureFlag.iaSlowLogsReads=controlled
mms.featureFlag.allowGCPPreviewRegions=disabled
mms.featureFlag.allowAWSPreviewRegions=disabled
mms.featureFlag.allowAzurePreviewRegions=disabled
mms.featureFlag.atlasDedicatedBackboneToReact=enabled
mms.featureFlag.atlasDedicatedReactClusterDetails=controlled
mms.featureFlag.encryptionAtRestAzureKeyVaultPrivateEndpoint=enabled
mms.featureFlag.encryptionAtRestAwsKmsPrivateEndpoint=enabled
mms.featureFlag.earGcpKmsRoleBasedAuth=disabled
mms.featureFlag.atlasAllowAtlasAdminFourFourPrivileges=controlled
mms.featureFlag.connectionEstablishmentRateLimiting=controlled
mms.federation.restrictOrgMembership.default=false
mms.federation.bypassSsoDomains=nih.gov
mms.enableOidcIdpNonAtlas=true
mms.featureFlag.optOutFromKinesis=controlled
mms.featureFlag.privateLinkProxyProtocolAws=controlled
mms.featureFlag.privateLinkMaxIncomingConnectionsMultiplier=controlled
mms.fromEmailAddr=MongoDB Cloud <<EMAIL>>
mms.globalRoles.mapping=default
mms.group.tags.edit.roleRequired=GLOBAL_MONITORING_ADMIN
mms.group.tags.read.roleRequired=GLOBAL_READ_ONLY
mms.group.useSoftDelete=false
mms.groupType.default=CLOUD
mms.helpAndSupportPage.enabled=true
mms.http.bindhostname=127.0.0.1
mms.https.dualConnectors=true
mms.invite.salesSold.ratelimit.maxAllowed=100
mms.invite.ratelimit.maxAllowed=10
mms.invite.ratelimit.periodMinutes=1
mms.jetty.stopTimeoutMS=60000
mms.kinesis.parseFTSPingPayload.enabled=true
mms.kinesis.parseHardwarePingPayload.enabled=true
mms.kubernetes.enabled=true
mms.kubernetes.templates.baseFilePathRequiredForCustomTemplates=/mongodb-ops-manager
mms.kubernetes.templates.defaultCredentialsFilePath=classpath://kubernetes-template-credentials.yaml.hbs
mms.kubernetes.templates.defaultProjectFilePath=classpath://kubernetes-template-project.yaml.hbs
mms.limits.maxAlertConfigsPerGroup=250
mms.limits.maxApiUsersPerOrg=500
mms.limits.maxGroupsPerOrg=250
mms.limits.maxGroupsPerUser=500
mms.limits.maxNotificationsPerAlert=25
mms.limits.maxOrgsPerUser=250
mms.limits.maxTeamsPerGroup=100
mms.limits.maxTeamsPerOrg=250
mms.limits.maxTeamsPerUser=100
mms.limits.maxUsersPerGroup=500
mms.limits.maxUsersPerOrg=500
mms.limits.maxUsersPerTeam=250
mms.limits.maxExportsPerGroup=100
mms.limits.maxM0sPerUnverifiedOrg=10
mms.limits.maxServiceAccountsPerOrg=200
mms.limits.maxCustomPoliciesPerOrg=100
mms.limit.maxNetworkAddressesPerServiceAccount=200
mms.limit.groupSearchMaxNumOfReturnAllowed=3000
mms.logcollection.s3Bucket.accessKey=<SECRETMANAGER>
mms.logcollection.s3Bucket.secretKey=<SECRETMANAGER>
mms.logCollectionJob.defaultExtensionDays=30
mms.login.exemptIps=***************
mms.login.newDeviceNotification.enabled=true
mms.login.ratelimit.attemptsAllowed=15
mms.login.ratelimit.lockedPeriodMinutes=5
mms.marketing.sync=true
mms.migration.config.customers.rcid.sleepMillis=50
maas.ingestion.usecase.336c2b2f-e495-5e7d-9a9c-9e6f492fa157.adminKey=<SECRETMANAGER>
maas.ingestion.usecase.91a39a8e-b296-5bd5-8cbe-cf54085f1bb1.adminKey=<SECRETMANAGER>
mms.monitoring.accessLogs.db.numInitialChunks=1000
mms.monitoring.accessLogs.maintenanceEnabled=false
mms.monitoring.accesslogs.rateLimit.hours=48
mms.monitoring.accesslogs.rateLimit.linesPerHost=80000
mms.monitoring.agent.session.timeoutMillis=120000
mms.monitoring.agentlog.writers=1
mms.featureFlag.monitoring.canonicalHostsTTLFilteredReads.enabled=controlled
mms.featureFlag.monitoring.canonicalHostsTTLWrites.enabled=controlled
mms.monitoring.canonicalHostsTTLMinutes=1440
mms.monitoring.canonicalHostsTTLWrites.probabilisticPercentage=25
mms.monitoring.canonicalHostsTTLWrites.guaranteedPercentage=50
mms.monitoring.chartDomain=https://chart-@<EMAIL>
mms.monitoring.clientmetadata.rateLimit.hours=48
mms.monitoring.clientmetadata.rateLimit.linesPerHost=28800
mms.monitoring.config.hostclusters.rcid.migration.sleepMillis=100
mms.monitoring.config.hosts.migration.sleepMillis=500
mms.automation.agent.config.atlasClusterNameRollout=0
mms.monitoring.groupRetentionPolicy.migration.heartbeatExpirationMillis=300000
mms.monitoring.groupRetentionPolicy.migration.maxScheduled=4
mms.monitoring.historicalReplicaState.ingestionPercentage=0
mms.monitoring.hostLastDailyPings.batchSize=20
mms.monitoring.hostLastPings.pruneRatePerSecond=250
mms.monitoring.ingestion.bucketedMetricRollup.enabled=true
mms.monitoring.ingestion.bucketedMetricRollup.bucketCount=8
mms.monitoring.ingestion.bucketedMetricRollup.serverlessBucketCount=8
mms.monitoring.ingestion.bucketedMetricRollup.realmBucketCount=2
mms.monitoring.ingestion.datadogmetrics.offer.timeout.millis=5
mms.monitoring.ingestion.diskpartition.poolsize=2
mms.monitoring.ingestion.diskpartition.queuesize=100
mms.monitoring.ingestion.kinesis.enabled=true
mms.monitoring.ingestion.kinesis.streams.collStatsLatency.enabled=true
mms.monitoring.ingestion.kinesis.streams.dedicated.discovery.enabled=true
mms.monitoring.ingestion.kinesis.streams.dedicated.ingestion.enabled=true
mms.monitoring.ingestion.kinesis.streams.fts.enabled=true
mms.monitoring.ingestion.kinesis.streams.hardware.enabled=true
mms.monitoring.ingestion.kinesis.streams.serverless.enabled=true
mms.monitoring.ingestion.hostping.poolsize=8
mms.monitoring.ingestion.hostping.queuesize=250
mms.monitoring.ingestion.munin.poolsize=2
mms.monitoring.ingestion.ndsautoscaling.poolsize=1
mms.monitoring.ingestion.newrelic.guid=com.mongodb.monitoring.integrations.newrelic.cloudmanager.production
mms.monitoring.ingestion.newrelic.poolsize=1
mms.monitoring.ingestion.newrelic.queuesize=100
mms.monitoring.ingestion.newrelic.version=1.0.0
mms.monitoring.ingestion.readPreference=secondaryPreferred
mms.monitoring.ingestion.rollup.job.scheduledRetryWait=1800000
mms.monitoring.ingestion.rollup.job.PT1M.workingRetryWait=300000
mms.monitoring.ingestion.rollup.job.PT24H.workingRetryWait=********
mms.monitoring.tokenization.checkOtherKeysThreshold=1
mms.monitoring.kinesis.datawarehouse.cloud__intel__collstatslatency_ping.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__discovery_ping_dedicated.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__fts_ping.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__hardware_ping.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__ingestion_ping_dedicated.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__ingestion_ping_shared.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__serverless_ping.groupPercentageAllowed=100
mms.monitoring.abbrvslowlogs.maintenanceEnabled=false
mms.monitoring.parollupshapes.maintenanceEnabled=false
mms.monitoring.parollupshapes.db.numInitialChunks=8000
mms.monitoring.parollupshapes.retentionHours=168
mms.monitoring.abbrvslowlogs.intervalStrategyHours=8
mms.monitoring.abbrvslowlogs.db.numInitialChunks=12000
mms.monitoring.abbrvslowlogs.schemaAdvisorFieldsRolloutPercentage=100
mms.monitoring.iaslowquerylogs.db.numInitialChunks=12000
mms.monitoring.iaslowquerylogs.intervalStrategyHours=24
mms.monitoring.iaslowquerylogs.maintenanceEnabled=false
mms.monitoring.iaaccessLogs.maintenanceEnabled=false
mms.monitoring.performanceadvisor.indexcollection.rerunIntervalMinutes=60
mms.monitoring.performanceadvisor.indexcollection.scheduleIntervalMinutes=360
mms.monitoring.performanceadvisor.iterative.maxLogLines=200000
mms.monitoring.performanceadvisor.textQueryThreshold=300
mms.monitoring.realm.enabled=true
mms.monitoring.realm.metrics.api.url=https://realm.mongodb.com/api/private/v1.0/app_metrics
mms.monitoring.rrd.collStatsLatencyMaintenanceEnabled=false
mms.monitoring.rrd.collStatsLatencyNumInitialChunks=200
mms.monitoring.rrd.maintenanceEnabled=false
mms.monitoring.rrd.rawdb.maxDatabases=2000
mms.monitoring.rrd.rrdIntervalsStrategy=DEFAULT
mms.monitoring.rrd.realmMaintenanceEnabled=false
mms.monitoring.rrd.realmNumInitialChunks=200
mms.monitoring.rrd.ftsMaintenanceEnabled=false
mms.monitoring.rrd.ftsNumInitialChunks=200
mms.monitoring.rrd.realm.thread.count=6
mms.monitoring.rrd.serverlessMaintenanceEnabled=false
mms.monitoring.slowlogs.intervalStrategyHours=24
mms.monitoring.slowlogs.rateLimit.hours=24
mms.monitoring.slowlogs.rateLimit.linesPerTenantShared=18000
mms.monitoring.slowLogIngestionHostMetadata.migration.sleepMillis=10
mms.monitoring.iaslowquerylogs.ingestion.poolsize=6
mms.monitoring.queryStats.exporter.usecaseId=82653516-67ba-5ffa-be02-5e4dd0376682
mms.monitoring.queryStats.exporter.publisherKey=<SECRETMANAGER>
mms.monitoring.queryStats.agent.scrapeRolloutPercentage=100
mms.monitoring.queryStats.ingestionRolloutPercentage=100
mms.monitoring.queryStats.ui.summaryLimit=300
mms.mana.http.socketTimeout=10000
mms.mana.http.connectTimeout=10000
mms.mana.clientId=<SECRETMANAGER>
mms.mana.clientSecret=<SECRETMANAGER>
mms.mana.authToken=<SECRETMANAGER>
mms.mana.isUsingInternalMeshNetwork=true
mms.monthlyUsageMetricsEmail.useMinuteBucket=false
mms.logIngestion.poolsize=4
mms.logIngestion.intervalRetentionHours=144
mms.multiFactorAuth.allowReset=true
mms.multiFactorAuth.issuer=MongoDB
mms.multiFactorAuth.level=REQUIRED_FOR_GLOBAL_ROLES
mms.multiFactorAuth.encouragement.enabled=true
mms.multiFactorAuth.encouragement.enabledForNoFactors=true
iam.multiFactorAuth.requiredAfterRegistration=false
iam.multiFactorAuth.requiredForAllUsers=true
iam.multiFactorAuth.rememberDevice.enabled=true
mms.personalizationWizardEnabled=true
mms.personalizationWizardRedirectEnabled=true
mms.appServicesGuideCue.enabled=true
mms.darkMode.preview.enabled=true
mms.darkMode.default=LIGHT
mms.ofac.enabled=true
mms.ofac.notificationEmailAddr=<EMAIL>
mms.ofac.supportEmailAddr=<EMAIL>
mms.plan.default=FREE_TIER
mms.publicApi.backup.rateLimitGranularity=1
mms.publicApi.backup.rateLimitMaxPerGroup=5000
mms.publicApi.events.rateLimitGranularity=1
mms.publicApi.events.rateLimitMaxPerGroup=100
mms.publicApi.globalKeyRoleDenylist=GLOBAL_OWNER
mms.publicApi.globalReadOnlyUserLimit=8000
mms.publicApi.globalReadOnlyUserPeriodMinutes=1
mms.publicApi.ignoreEnabledForGlobalRoles=false
mms.publicApi.maxTempKeys=100
mms.publicApi.metric.rateLimitGranularity=1
mms.publicApi.metric.rateLimitMaxPerGroup=100
mms.publicApi.performanceAdvisor.rateLimitGranularity=1
mms.publicApi.performanceAdvisor.rateLimitMaxPerGroup=100
mms.publicApi.queryShapeInsights.rateLimitGranularity=1
mms.publicApi.queryShapeInsights.rateLimitMaxPerGroup=10
mms.publicApi.dataExplorer.rateLimitGranularity=1
mms.publicApi.dataExplorer.rateLimitMaxPerGroup=100
mms.pushLiveMigrations.enabled=true
mms.pushLiveMigrations.mongoClient.validations.enabled=true
mms.pushLiveMigrations.fetchJob.intervalSeconds=60
mms.pushLiveMigrations.mmsUi.defaultAccessListCidr=10.0.0.0/8
mms.pushLiveMigrations.mmsUi.serviceMeshEnvVar=KUBE_MMS_UI_KUBE_PROD_US_EAST_1_AWS_CLOUD_10GEN_CC
mms.pushLiveMigrations.syncJobs.enabled=true
mms.pushLiveMigrations.syncJobs.cm.failedAttempts.max=1440
mms.pushLiveMigrations.updateJob.intervalSeconds=60
mms.pushLiveMigrations.updateJob.cooldownSeconds=10
mms.quartz.thread.count=40
mms.remoteIp.header=X-Cluster-Client-Ip
mms.replyToEmailAddr=<EMAIL>
mms.replyToSuspensionAdminEmailAddr=<EMAIL>
mms.root.redirect=https://www.mongodb.com/cloud
mms.search.envoy.location=https://mongodb-mms-build-envoy-serverless.s3.amazonaws.com/tarballs/
mms.search.xds.host=search-xds.mongodb.com
mms.search.gateway.host=search-gateway.mongodb.com
mms.search.gateway.centralUrl=https://search-gateway.mongodb.com
mms.search.offlineUpgradePackages=1.38.1.2120
mms.search.offlineDowngradePackages=1.41.0
mms.search.commonLabelsEnabledTiers=
mms.search.staleEnabledTiers=MTM
mms.search.incubatorVectorEnabledTiers=ALL
mms.search.lifecycleManagerEnabledTiers=M10
mms.search.apiRateLimitRules=default:2
mms.search.tokenFacetingCardinalityLimitShared=10000000
mms.search.tokenFacetingCardinalityLimitDedicated=0
mms.search.excludedChangeStreamFields=updateDescription.disambiguatedPaths
mms.search.maas.metricsIngestionUrl=http://localhost:4318
mms.search.maas.useCaseUuid=49642286-8df9-5c7e-807b-40ad3e39fafd
mms.search.maas.pushMetricsIntervalSec=60
mms.security.allowCORS=true
mms.security.backdoor=false
mms.security.cors.allow.localhost.origin=false
mms.security.disableBrowserCaching=false
mms.security.globalWhitelist=seed-data/GlobalWhitelistDao-prod.json
mms.security.hstsMaxAgeSeconds=31536000
mms.security.requireCSRFToken=true
mms.server.sentry.dsn=<SECRETMANAGER>
mms.server.sentry.enabled=true
mms.server.sentry.sampleRate=1.0
mms.server.sentry.apiKey=<SECRETMANAGER>
mms.server.sentry.webhook.threads.core=2
mms.server.sentry.webhook.threads.max=5
mms.serverless.xDSServer.enabled=false
mms.serverless.xDSServer.tlsRequired=true
mms.serverless.xds.error.log.alert.test.enabled=false
mms.session.maxConcurrent=10
mms.session.maxHours=12
mms.session.authn.tokens.filter.enabled=true
mms.session.authn.tokens.minting.enabled=true
mms.session.customSessionTimeouts.enabled=false
mms.siteFullName.nds=MongoDB Atlas
mms.siteName.nds=Atlas
mms.snapshotQueryHost=queryable-backup.mongodb.com:27017
mms.staleAuthCodeLimit=5
mms.telemetry.enabled=true
mms.telemetry.atlascli.sampling.percentage=100
mms.telemetry.atlascli.unauth.sampling.percentage=100
mms.telemetry.mdbmcp.unauth.sampling.percentage=100
mms.telemetry.mdbmcp.sampling.percentage=100
mms.telemetry.meko.unauth.sampling.percentage=100
mms.featureFlag.apix.enableApiTelemetryCustomFields=disabled
mms.telemetry.api.sampling.percentage=100
mms.telemetry.api.denylistexemptions=/api/private/unauth/telemetry/events
mms.telemetry.api.denylist=/api/private,/api/scim,/accessLogs,/account,/activity,/admin,/agent,/agentlog,/agentsApi,/alertOAuth2,/allClusters,/analytics,/automation,/backingDatabases,/backup,/billing,/canonicalHosts,/chart,/charts,/conf,/customer,/dashboard,/deployment,/design,/docs,/download,/email,/exception,/explorer,/export,/federation,/federationSettings,/group,/host,/links,/log,/maintenance,/metrics,/monitor,/nds,/okta,/orgs,/performanceAdvisor,/ping,/recovery,/saml,/servers,/settings,/setup,/sso,/support,/system,/test,/usage,/usageData,/usageTypes,/user,/v2,/{groupId},/{orgId},/{privateLinkId},/uiMsgs,/goto,admin,/selfServe,/partners,/prometheus,/segmentTraits,/cloud,/v3,/org,/integrations,/go,/ecosystem,/unsupportedBrowser,/pricing,/redirect,/sitemap,/sitemap,/version,/regional_token_receiver,/robots.txt,/freemonitoring,/api/openapi,/webhooks,/iframe,/signup,/learn-more,/openapi,/learn-more,/webhook,/stories,/authz,nds
mms.telemetry.aws.secrets.accessKey=<SECRETMANAGER>
mms.telemetry.aws.secrets.secretKey=<SECRETMANAGER>
mms.telemetry.aws.kinesis.streamName=cloud__apix__api_telemetry
mms.telemetry.aws.kinesis.region=us-east-1
mms.telemetry.aws.kinesis.requestTimeout=2000
mms.telemetry.aws.kinesis.socketTimeout=15000
mms.telemetry.producer.queue.maxSize=300
mms.telemetry.producer.queue.offerTimeoutMillis=10
mms.telemetry.producer.threadCount=1
mms.telemetry.producer.batchSize=50
mms.telemetry.producer.queue.drainIntervalMillis=1000
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.maxHitsPerPeriod=50
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.minutesPeriod=1
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.maxHitsPerPeriod=20
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.minutesPeriod=1
mms.testUtil.enabled=false
mms.throttleSvcEnabled=false
mms.ui.filters.globalUserAccessFilter.enabled=true
mms.ui.filters.globalUserAccessFilter.additionalIps=*************,*************,*************,*************
mms.ui.mongonav.env=commercial
mms.usageData.awsAccessKey=<SECRETMANAGER>
mms.usageData.awsSecretKey=<SECRETMANAGER>
mms.usageData.collection=false
mms.usageData.ingestion=true
mms.usageData.pgp.publicKey=/mongodb-inc-opsmanager.pub.asc
mms.usageData.pgp.secretKey=/mongodb-inc-opsmanager.private.asc
mms.usageData.pgp.secretPassPhrase=<SECRETMANAGER>
mms.usageData.s3.bucket=mongodb-datawarehouse
mms.user.bypassInviteForExistingUsers=false
mms.user.invitationOnly=false
mms.user.redirectIfPasswordIsEmpty=false
mms.user.registration.extraFields=company
mms.user.username.restrictedDomains=legacyusername[.]mongodb[.]com,invalidusername[.]mongodb[.]com,belgianairways[.]com,gopostal[.]top,blueink[.]top,e-mail[.]lol,message[.]rest,letters[.]monster,yesreply[.]online,e-boss[.]xyz,writeme[.]live,ultramail[.]pro,protonbox[.]pro,mailgod[.]xyz,anymail[.]xyz,guesswho[.]click,sendme[.]digital,mypost[.]lol,echat[.]rest,gogomail[.]ink,specialmail[.]online,electroletter[.]space,snacksberry[.]com[.]ua,rambler[.]ua,.*[.]ru,kwaaka[.]com
mms.userSvcClass=UserSvcOkta
mms.vercel.clientId=<SECRETMANAGER>
mms.vercelIntegration.enabled=true
mms.vercel.secret=<SECRETMANAGER>
mms.versionMismatch.detect=false
mms.versionMismatch.pingInterval.unit=SECONDS
mms.versionMismatch.pingInterval=10
mms.versionMismatch.pingInterval.staleCount=60
mms.versionToDeprecate=6.0
mms.forcedDeprecatedVersionUpgradeTargets=7.0,8.0
mms.versionDeprecatedByDate=2025-07-31
mms.enableFutureEOLExtensionCheck=true
# V1 Degraded Experience Configuration
mms.v1.degradedExperience.phase1.enabled=false
mms.v1.degradedExperience.phase2.enabled=false
mms.v1.degradedExperience.shadowMode.enabled=true
mms.v1.degradedExperience.creationCutoffDate.org=2025-09-08T00:00:00.000Z
mms.v1.degradedExperience.creationCutoffDate.project=2025-09-08T00:00:00.000Z
mmsdbautomationlog.queuesize=1500
mmsdbautomationlog.writers=2
mmsdbserverlog.logs.enabled=false
mmsdbserverlog.logs.ttlSeconds=600
mms.azure.ssdv2.regions=francecentral,eastasia,canadacentral,koreacentral,norwayeast,polandcentral,southafricanorth,swedencentral,switzerlandnorth,uaenorth,australiaeast,northeurope,centralindia,japaneast,uksouth,brazilsouth,southeastasia,germanywestcentral,southcentralus,westus3,centralus,eastus2,westeurope,eastus,westus2,italynorth,israelcentral,spaincentral,mexicocentral
mms.azure.ssdv2.rollout.regions=
mms.azure.ssdv2.preview.regions=eastus2euap
mongo.account.encryptedCredentials=true
mongo.account.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&waitQueueTimeoutMS=60000&uuidRepresentation=standard&localThresholdMS=8
mongo.account.ssl=true
mongo.chartsmetadata.encryptedCredentials=true
mongo.chartsmetadata.mongoUri=mongodb+srv://3646a12497d1c4b838ab1835c90491c3-02ae3b85835816e3a42e34d9d7cdf53b80991ede66b6525d:<EMAIL>/test?retryWrites=true&retryReads=false&uuidRepresentation=standard
mongo.chartsmetadata.ssl=false
mongo.monitoringagentlogs.encryptedCredentials=true
mongo.monitoringagentlogs.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=15000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.monitoringagentlogs.ssl=true
mongo.atlasbackup.encryptedCredentials=true
mongo.atlasbackup.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=100&retryWrites=false&retryReads=false&uuidRepresentation=standard&compressors=snappy
mongo.atlasbackup.ssl=true
mongo.automationcore.encryptedCredentials=true
mongo.automationcore.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=20&waitQueueTimeoutMS=60000&waitQueueMultiple=10&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.automationcore.ssl=true
mongo.automationstatus.encryptedCredentials=true
mongo.automationstatus.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=15&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.automationstatus.ssl=true
mongo.authz.encryptedCredentials=true
mongo.authz.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=200&compressors=snappy&uuidRepresentation=standard&localThresholdMS=8
mongo.authz.ssl=true
mongo.backupdb.encryptedCredentials=true
mongo.backupdb.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=32&socketTimeoutMS=1800000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.backupdb.ssl=true
mongo.backupstatus.encryptedCredentials=true
mongo.backupstatus.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=32&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.backupstatus.ssl=true
mongo.backupusage.encryptedCredentials=true
mongo.backupusage.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=32&retryWrites=false&socketTimeoutMS=120000&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.backupusage.ssl=true
mongo.backupagentlogs/backuplogs.encryptedCredentials=true
mongo.backupagentlogs/backuplogs.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=15000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.backupagentlogs/backuplogs.ssl=true
mongo.controlledfeatures.encryptedCredentials=true
mongo.controlledfeatures.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=38&waitQueueTimeoutMS=60000&uuidRepresentation=standard&localThresholdMS=8
mongo.controlledfeatures.ssl=true
mongo.cloudconf.encryptedCredentials=true
mongo.cloudconf.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=10&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.cloudconf.ssl=true
mongo.cloudconf.cutover.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=10&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.cloudconf.cutover.writes.temp.collections=app.systemCronTrack.v2
mongo.cloudconf.cutover.writes.temp.collection.app.systemCronTrack.v2.merge.strategy=com.xgen.cloud.migration._private.impl.liveimport.merge.strategy.AlwaysReplaceDocumentStrategy
mongo.settings.encryptedCredentials=true
mongo.settings.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=10&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.settings.ssl=true
mongo.tokenization.encryptedCredentials=true
mongo.tokenization.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=38&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.tokenization.ssl=true
mongo.discovery.encryptedCredentials=true
mongo.discovery.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=60&connectTimeoutMS=60000&socketTimeoutMS=120000&waitQueueTimeoutMS=60000&retryWrites=false&uuidRepresentation=standard&localThresholdMS=8
mongo.discovery.ssl=true
mongo.realmmetricsstate.encryptedCredentials=true
mongo.realmmetricsstate.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=38&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.realmmetricsstate.ssl=true
mongo.serverlesschartselect.encryptedCredentials=true
mongo.serverlesschartselect.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=38&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.serverlesschartselect.ssl=true
mongo.metricsmisc.encryptedCredentials=true
mongo.metricsmisc.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=38&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.metricsmisc.ssl=true
mongo.metricsmisc2.encryptedCredentials=true
mongo.metricsmisc2.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=38&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.metricsmisc2.ssl=true
mongo.iam.encryptedCredentials=true
mongo.iam.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=38&waitQueueTimeoutMS=60000&uuidRepresentation=standard&localThresholdMS=8
mongo.iam.ssl=true
mongo.mmsalertconfig.encryptedCredentials=true
mongo.mmsalertconfig.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=38&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsalertconfig.ssl=true
mongo.dataexplorer.encryptedCredentials=true
mongo.dataexplorer.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=22&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.dataexplorer.ssl=true
mongo.event-service.encryptedCredentials=true
mongo.event-service.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.event-service.ssl=true
mongo.mmsdbevents.encryptedCredentials=true
mongo.mmsdbevents.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=20&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=15000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbevents.ssl=true
mongo.mmsdbeventslongrunningtools.encryptedCredentials=true
mongo.mmsdbeventslongrunningtools.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=20&connectTimeoutMS=300000&socketTimeoutMS=6000000&waitQueueTimeoutMS=6000000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbeventslongrunningtools.ssl=true
mongo.mmsdbalerts.encryptedCredentials=true
mongo.mmsdbalerts.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=20&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=15000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbalerts.ssl=true
mongo.mmsdbclusterdescriptions.encryptedCredentials=true
mongo.mmsdbclusterdescriptions.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=20&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=15000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbclusterdescriptions.ssl=true
mongo.mmsdbemails.encryptedCredentials=true
mongo.mmsdbemails.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=20&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=15000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbemails.ssl=true
mongo.mmsdbautomation.encryptedCredentials=true
mongo.mmsdbautomation.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=20&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbautomation.ssl=true
mongo.mmsdbqueues.encryptedCredentials=true
mongo.mmsdbqueues.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=20&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbqueues.ssl=true
mongo.automationagentlogs.encryptedCredentials=true
mongo.automationagentlogs.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=15000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.automationagentlogs.ssl=true
mongo.mmsdbbilling.encryptedCredentials=true
mongo.mmsdbbilling.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&waitQueueTimeoutMS=60000&retryWrites=true&w=majority&retryReads=true&uuidRepresentation=standard
mongo.mmsdbbilling.ssl=true
mongo.mmsdbcloudproviders.encryptedCredentials=true
mongo.mmsdbcloudproviders.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&waitQueueTimeoutMS=120000&retryWrites=true&retryReads=true&uuidRepresentation=standard
mongo.mmsdbcloudproviders.ssl=true
mongo.mmsdbcollstatslatencyrrd.encryptedCredentials=true
mongo.mmsdbcollstatslatencyrrd.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbcollstatslatencyrrd.ssl=true
mongo.mmsdbcollstatslatencyconfig.encryptedCredentials=true
mongo.mmsdbcollstatslatencyconfig.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbcollstatslatencyconfig.ssl=true
mongo.mmsdbjobs.encryptedCredentials=true
mongo.mmsdbjobs.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=20&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=false&uuidRepresentation=standard&w=majority
mongo.mmsdbjobs.ssl=true
mongo.logcollection.encryptedCredentials=true
mongo.logcollection.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=22&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.logcollection.ssl=true
mongo.realtimemeasurements.encryptedCredentials=true
mongo.realtimemeasurements.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=22&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.realtimemeasurements.ssl=true
mongo.mmsdbmetering.encryptedCredentials=true
mongo.mmsdbmetering.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?retryWrites=true&retryReads=true&w=majority&uuidRepresentation=standard
mongo.mmsdbmetering.ssl=true
mongo.billingauditing.encryptedCredentials=true
mongo.billingauditing.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&waitQueueTimeoutMS=60000&retryWrites=true&retryReads=true&uuidRepresentation=standard
mongo.billingauditing.ssl=true
mongo.iadbpings.encryptedCredentials=true
mongo.iadbpings.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=30&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.iadbpings.ssl=true
mongo.mmsdbrrdcache.encryptedCredentials=true
mongo.mmsdbrrdcache.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=30&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbrrdcache.ssl=true
mongo.dbprofile.encryptedCredentials=true
mongo.dbprofile.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.dbprofile.ssl=true
mongo.atlaslogsmigration.encryptedCredentials=true
mongo.atlaslogsmigration.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=12&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.atlaslogsmigration.ssl=true
mongo.atlasndsstatus.encryptedCredentials=true
mongo.atlasndsstatus.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.atlasndsstatus.ssl=true
mongo.mmsdbrealmrrd4ia.encryptedCredentials=true
mongo.mmsdbrealmrrd4ia.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbrealmrrd4ia.ssl=true
mongo.mmsdbsharedtier/atlaslogsnds.encryptedCredentials=true
mongo.mmsdbsharedtier/atlaslogsnds.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=12&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbsharedtier/atlaslogsnds.ssl=true
mongo.atlaslogsoa.encryptedCredentials=true
mongo.atlaslogsoa.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=15000&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.atlaslogsoa.ssl=true
mongo.mmsdbftsrrd.encryptedCredentials=true
mongo.mmsdbftsrrd.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbftsrrd.ssl=true
mongo.mmsdbrrd4dbia.encryptedCredentials=true
mongo.mmsdbrrd4dbia.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=35&connectTimeoutMS=15000&socketTimeoutMS=180000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbrrd4dbia.ssl=true
mongo.mmsdbrrd4ia.encryptedCredentials=true
mongo.mmsdbrrd4ia.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=35&connectTimeoutMS=15000&socketTimeoutMS=180000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbrrd4ia.ssl=true
mongo.metricsstats.encryptedCredentials=true
mongo.metricsstats.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=35&connectTimeoutMS=15000&socketTimeoutMS=180000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.metricsstats.ssl=true
mongo.realmdiscovery.encryptedCredentials=true
mongo.realmdiscovery.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=30000&socketTimeoutMS=60000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.realmdiscovery.ssl=true
mongo.mmsdbserverlessrrd4ia.encryptedCredentials=true
mongo.mmsdbserverlessrrd4ia.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=35&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbserverlessrrd4ia.ssl=true
mongo.ratelimits.encryptedCredentials=true
mongo.ratelimits.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=35&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.ratelimits.ssl=true
mongo.mmsrt.encryptedCredentials=true
mongo.mmsrt.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=100&retryWrites=false&retryReads=false&connectTimeoutMS=5000&socketTimeoutMS=30000&waitQueueTimeoutMS=100&waitQueueMultiple=1&serverSelectionTimeoutMS=2000&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsrt.ssl=true
mongo.monitoringdiagnostics.encryptedCredentials=true
mongo.monitoringdiagnostics.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=30&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.monitoringdiagnostics.ssl=true
mongo.iaaccesslogs.encryptedCredentials=true
mongo.iaaccesslogs.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=30&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.iaaccesslogs.ssl=true
mongo.monitoringftstelemetry.encryptedCredentials=true
mongo.monitoringftstelemetry.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=30&retryWrites=false&retryReads=true&uuidRepresentation=standard
mongo.monitoringftstelemetry.ssl=true
mongo.monitoringstatus.encryptedCredentials=true
mongo.monitoringstatus.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=15&waitQueueMultiple=10&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.monitoringstatus.ssl=true
mongo.sessions.encryptedCredentials=true
mongo.sessions.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=15&waitQueueMultiple=10&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.sessions.ssl=true
mongo.nds.encryptedCredentials=true
mongo.nds.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&connectTimeoutMS=60000&socketTimeoutMS=120000&waitQueueTimeoutMS=60000&retryWrites=true&retryReads=false&uuidRepresentation=standard&compressors=snappy&localThresholdMS=8
mongo.nds.ssl=true
mongo.platformdiagnostics.encryptedCredentials=true
mongo.platformdiagnostics.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&connectTimeoutMS=60000&socketTimeoutMS=120000&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=false&uuidRepresentation=standard&compressors=snappy&localThresholdMS=8
mongo.platformdiagnostics.ssl=true
mongo.fts.encryptedCredentials=true
mongo.fts.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&connectTimeoutMS=60000&socketTimeoutMS=120000&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=false&uuidRepresentation=standard&compressors=snappy&localThresholdMS=8
mongo.fts.ssl=true
mongo.oa.encryptedCredentials=true
mongo.oa.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&connectTimeoutMS=60000&socketTimeoutMS=120000&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=false&uuidRepresentation=standard&compressors=snappy
mongo.oa.ssl=true
mongo.adl.encryptedCredentials=true
mongo.adl.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&connectTimeoutMS=60000&socketTimeoutMS=120000&waitQueueTimeoutMS=60000&retryWrites=false&retryReads=false&uuidRepresentation=standard&compressors=snappy
mongo.adl.ssl=true
mongo.atlasbilling.encryptedCredentials=true
mongo.atlasbilling.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=40&waitQueueTimeoutMS=60000&retryWrites=true&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.atlasbilling.ssl=
mongo.mmsdblogtooling.encryptedCredentials=true
mongo.mmsdblogtooling.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=true&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdblogtooling.ssl=true
mongo.iaquerystats.encryptedCredentials=true
mongo.iaquerystats.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.iaquerystats.ssl=true
mongo.abbrvslowlogs.encryptedCredentials=true
mongo.abbrvslowlogs.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.abbrvslowlogs.ssl=true
mongo.ccpa.encryptedCredentials=true
mongo.ccpa.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=25&connectTimeoutMS=15000&socketTimeoutMS=30000&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.ccpa.ssl=true
mongo.iaslowlogs.encryptedCredentials=true
mongo.iaslowlogs.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?maxPoolSize=30&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=60000&retryWrites=false&uuidRepresentation=standard&compressors=snappy&localThresholdMS=8
mongo.iaslowlogs.ssl=true
mongo.integrations.encryptedCredentials=true
mongo.integrations.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?retryWrites=true&w=majority&appName=ia-prod-integrations&localThresholdMS=8
mongo.integrations.ssl=true
mongo.ui.encryptedCredentials=true
mongo.ui.mongoUri=mongodb+srv://672a6fb2df247d1d0dabda3c3482b4af9b9faba5a46f5e8cd20b01d3c4e50954-3ad0687e478a2fb511df9ba25f8b7d30-7de4d5e61f7f3dbcf227397c5d63320ffcf53057e68df69e610c1edc27e9c629-2d76442763acfb98ba12d87b66cb8bbe723c79ff73a432e318599c9bc6fb1e678b70dbf582031bfbb608666e800f118250c21c89bd1b15cff03323091c3b5160:<EMAIL>/?retryWrites=true&w=majority&appName=ia-prod-ui&localThresholdMS=8
mongo.ui.ssl=true
mongodb.disable.numa=true
mongodb.release.autoDownload.enterprise=true
mongodb.release.autoDownload.rc=true
mongodb.release.autoDownload=true
mongodb.release.modulePreference=communityRequired
mongodb.release.publicKeyFile=classpath://mongodb-inc-server.pub
mongodb.ssl.CAFile=/etc/ssl/certs/ca-bundle.crt
mongot.location=https://internal-downloads.mongodb.com/search/
mongotools.version=100.13.0
# `/prod/*` is the release bucket for mongotune binaries
mongotune.location=https://internal-downloads.mongodb.com/mongotune/prod/
mongotune.loglevel=INFO
nds.mongotune.minAgentVersion=13.35.2.9529-1
mongosh.version=2.5.6
monitoring.agent.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
monitoring.agent.location.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
monitoring.agent.minimumVersion=5.0.0.309
monitoring.agent.version=7.2.0.488-1
morphia.search.packages=com.xgen.cloud.common.featureFlag._public.model,om.xgen.svc.mms.model.metrics.realtime,com.xgen.svc.mms.deployment.auth,com.xgen.svc.mms.model.performanceadvisor,com.xgen.cloud.activity._public.model.alert,com.xgen.cloud.activity._public.model.alert.config,com.xgen.svc.mms.model,com.xgen.svc.atm.model,com.xgen.svc.mms.model.performanceadvisor.autoindexing,com.xgen.cloud.atm.core._public.model,com.xgen.cloud.deployment._public.model,com.xgen.cloud.monitoring.topology._public.model,com.xgen.cloud.organization._public.model,com.xgen.cloud.atm.core._public.model.status,com.xgen.cloud.billingplatform.activity._public.alert,com.xgen.cloud.billingplatform.activity._public.alert.config,com.xgen.cloud.billingplatform.activity._public.audit,com.xgen.cloud.billingplatform.activity._public.event,com.xgen.cloud.billingplatform.audit._public.model,com.xgen.cloud.billingplatform.invoice._public.model,com.xgen.cloud.billingplatform.model.cloudprovider._public.aws,com.xgen.cloud.billingplatform.model.cloudprovider._public.azure,com.xgen.cloud.billingplatform.model.plan._public.model,com.xgen.cloud.dataexport._public.model,com.xgen.cloud.partners.registration._public.model,com.xgen.cloud.payments.netsuite._public.models,com.xgen.cloud.revrec.selfserve._private.model,com.xgen.svc.mms.model.billing,com.xgen.svc.mms.model.billing.reporting,com.xgen.svc.mms.model.marketing,com.xgen.cloud.common.explorer._public.model
nds.acme.accountDocument=
nds.acme.le.accountDocument=
nds.acme.gts.accountDocument=
nds.atlasproxy.conf.maxProcessedAccounts=100
nds.atlasproxy.conf.tlsPEMKeyFile=/etc/pki/tls/private/proxy.pem
nds.atlasproxy.restartProxyProcessOnFallbackCertRotation=false
nds.admin.reasonsRequireJira=true
nds.admin.dataPlaneAccessRequestsOnlineValidation=true
nds.admin.createRespectProtectedHoursMaintenanceAdminUi.enabled=true
nds.agent.envoyConfigServer.enabled=true
nds.agent.envoyConfigServer.nodeId=atlas-envoy
nds.agent.envoyConfigServer.port=9902
nds.agent.envoyConfigServer.adminPort=9901
nds.agent.envoyConfigServer.promExternalPort=27018
nds.agent.envoyConfigServer.promInternalPort=9945
nds.agent.envoyConfigServer.gatewayProxyExternalPort=27014
nds.agent.envoyConfigServer.mongotPromPort=9946
nds.agent.envoyConfigServer.searchEnvoyAdminPort=9905
nds.agent.envoyConfigServer.tlsCertConfigPath=/etc/envoy/sds.yaml
nds.agent.envoyConfigServer.tlsFilterServerName=*.mongodb.net
nds.agentApiKey.allowUnrestricted=false
nds.autoScaling.compute.initiateScaleDownWindow.days=MONDAY,TUESDAY,WEDNESDAY,THURSDAY,FRIDAY,SATURDAY,SUNDAY
nds.autoScaling.compute.initiateScaleDownWindow.hours=00:00,23:59
nds.autoScaling.burstable.compute.newClusterEnabled=true
nds.autoScaling.predictive.cron.enabled=true
nds.autoscaling.predictive.autoscaler.compute.enabled=true
nds.autoScaling.predictive.grpc.client.timeoutInSeconds=15
nds.aws.al2023.newGroups=true
nds.aws.billingReport.bucketName=mongodb-atlas-billing
nds.aws.billingReport.name=daily-report
nds.aws.billingReport.prefix=custom-reports
nds.aws.billingReport.regionName=US_EAST_1
nds.aws.dataValidation.instanceSize=R40
nds.aws.dataValidation.min.diskSizeGB=1024
nds.aws.dataValidation.max.instanceSize=M80
nds.aws.dns.domain=mongodb.net
nds.aws.dns.hostedzoneid=Z206VG4B61KLXY
nds.aws.dns.datalake.hostedzoneid=Z028260237C1UDY3Z8ZJE
nds.azure.al2023.newGroups=false
nds.azure.dataValidation.instanceSize=M40
nds.azure.dataValidation.min.diskSizeGB=1024
nds.azure.dataValidation.max.instanceSize=M80
nds.azure.dns.datalake.hostedzoneid=Z0900613F49WGV5AQ0OM
nds.azure.dns.domain=azure.mongodb.net
nds.azure.dns.hostedzoneid.private=Z3F3OJ1W13IJ1W
nds.azure.dns.hostedzoneid.public=Z13EA34IVPR1VY
nds.backup.oplogs.expiration.threads=48
nds.backup.oplogs.purge.threads=24
nds.backup.oplogs.removal.dropInactive.threads=2
nds.backup.oplogs.removal.threads=10
nds.backup.oplogs.checkAbnormalities.threads=2
nds.backup.pit.validateOplogBehind.threads=40
nds.backup.restore.gzipCompressionLevel=1
nds.backup.snapshots.expiration_extend_minutes=60
nds.backup.snapshots.gc.threads=30
nds.backup.snapshots.restore.minimum_time_needed_for_restore_minutes=30
nds.backup.snapshots.aws.copySnapshotKmsKeyArnTemplate=arn:aws:kms:%s:962727799805:alias/cps-snapshot-copy
nds.backup.snapshots.aws.fasterRestoreJobKmsKeyArnTemplate=arn:aws:kms:%s:%s:alias/cloud-prod-volume-encryption-key
nds.backup.standardDSV5RolloutPercentage=1.0
nds.billing.purge.expireAfterSeconds=16931410
nds.billing.purge.isDryRun=false
nds.criticalMaintenance.job.enabled=true
nds.corruptionDetection.targetClusterVersionRegex=
nds.corruptionDetection.dataValidation.dailySamplePercent=0.80
nds.corruptionDetection.dataValidation.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dataValidation.minClusterAgeDays=182
nds.corruptionDetection.dbCheck.dailySamplePercent=0.0
nds.corruptionDetection.dbCheck.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dbCheck.minClusterAgeDays=182
nds.corruptionDetection.checkMetadataConsistency.dailySamplePercent=2.0
nds.corruptionDetection.checkMetadataConsistency.minDaysBetweenClusterValidations=182
nds.corruptionDetection.checkMetadataConsistency.minClusterAgeDays=182
nds.connectionEstablishmentRateLimiting.newGroups=true
nds.dataValidation.maxWorkingRecords=1250
nds.dns.changes.resolverThreads=70
nds.adminEmailAddress=<EMAIL>
nds.externalcaching.redis.cacheEnabled=true
nds.externalcaching.redis.sslEnabled=true
nds.externalcaching.redis.primary.host=master.mms-nds-external-cache-prod.m2hzsp.use1.cache.amazonaws.com
nds.externalcaching.redis.primary.port=6379
nds.externalcaching.redis.replica.host=replica.mms-nds-external-cache-prod.m2hzsp.use1.cache.amazonaws.com
nds.externalcaching.redis.replica.port=6379
nds.externalcaching.redis.username=<SECRETMANAGER>
nds.externalcaching.redis.password=<SECRETMANAGER>
nds.externalcaching.redis.domainAuthRedisEnabled=true
nds.fastSharedTier.precreateCronSvc.enabled=true
nds.fastSharedTier.enabled=true
nds.fastSharedTier.maxRecordsPerRegion=5
nds.fastFlex.maxRecordsPerRegion=5
nds.fastFlexProvisioning.enabled=true
nds.flex.api.shared.mode=DEPRECATED
nds.flex.api.serverless.mode=DEPRECATED
nds.flexModeForServerless.enabled=true
nds.lookupSvc.redisCache.entry.ttlHours=12
nds.lookupSvc.redisCache.entry.extendTtlBeforeExpiryThresholdMins=15
nds.lookupSvc.redisCache.entry.maxExtraTTLSecs=3600
nds.orphanCleanup.azure.numThreads=4
nds.gateway.proxy.enabled=true
nds.gcp.SnapshotBillingReport.dataset=mongodb_net_billing
nds.gcp.SnapshotBillingReport.projectId=cloud-prod-root
nds.gcp.billingReport.bucketName=mongodb-net-billing-reports
nds.gcp.billingReport.prefix=mongodb-net-billing-
nds.gcp.dailyBilling.auditDays=9
nds.gcp.dataValidation.instanceSize=M40
nds.gcp.dataValidation.min.diskSizeGB=1024
nds.gcp.dataValidation.max.instanceSize=M80
nds.gcp.dns.domain=gcp.mongodb.net
nds.gcp.dns.hostedzoneid.crosscloud.private=Z01907646ZQ7KDUF6Y3Q
nds.gcp.dns.hostedzoneid.private=ZQE851S26RM7K
nds.gcp.dns.hostedzoneid.public=Z107AB0NJ2UVNG
nds.gcp.projectCreate.count=7500
nds.gcp.al2023.newGroups=false
nds.atlasPrioritizeHavingPrimaryOnUpscaledNode.newGroups=true
nds.privateLinkProxyProtocolAws.newGroups=true
nds.gcp.psc.regionGroup.defaultSize=50
nds.gov.us.enabled=false
nds.healthCheck.max.thread.count=35
nds.planner.deleteAfterDate.rollout.percentage=0
nds.healthCheck.staleJobThreshold.hours=1
nds.images.s3Bucket.aws.accesskey=<SECRETMANAGER>
nds.images.s3Bucket.aws.secretkey=<SECRETMANAGER>
nds.images.gcp.projectId=atlas-images-prod
nds.instances.advancedTesting.allow=false
nds.instances.heracles.s3KeyId=<SECRETMANAGER>
nds.instances.heracles.s3SecretKey=<SECRETMANAGER>
nds.instances.heracles.s3Bucket=<SECRETMANAGER>
nds.instances.heracles.s3Region=<SECRETMANAGER>
nds.instances.secret-names=aws-creds,aws,log-uploader-keys,repo-gateway,s3logwriter-keys,firehoselogwriter-keys,wildcard-cert-pem,tenable,clamav-db,deviceSyncDebugAccessSharedSecret
nds.instances.secrets.CN.wildcard-cert-pem=BLOCKED
nds.instances.secrets.CN.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.aws-creds=<SECRETMANAGER>
nds.instances.secrets.aws.aws=<SECRETMANAGER>
nds.instances.secrets.aws.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.aws.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.aws.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.firehoselogwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.azure.aws-creds=<SECRETMANAGER>
nds.instances.secrets.azure.aws=<SECRETMANAGER>
nds.instances.secrets.azure.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.azure.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.azure.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.azure.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.gcp.aws-creds=<SECRETMANAGER>
nds.instances.secrets.gcp.aws=<SECRETMANAGER>
nds.instances.secrets.gcp.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.gcp.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.clamav-db=
nds.instances.secrets.tenable=<SECRETMANAGER>
nds.instances.secrets.deviceSyncDebugAccessSharedSecret=<SECRETMANAGER>
nds.instances.shared.version=8.0
nds.instances.serverless.size=SERVERLESS_V2
nds.instances.os.aws=AL2
nds.instances.os.azure=AL2
nds.instances.os.gcp=AL2
nds.serverlessProxy.os.aws=AL2
nds.serverlessProxy.os.azure=AL2
nds.serverlessProxy.os.gcp=AL2
nds.streamsProxy.os.aws=AL2
nds.streamsProxy.os.azure=AL2
mms.featureFlag.streamsAWSProxyAL2023Enabled=controlled
mms.featureFlag.streamsAzureProxyAL2023Enabled=controlled
nds.ifr.s3BucketName=mms-ifr-prod-us-east-1
nds.ifr.s3BucketReadOnlyRoleArn=arn:aws:iam::************:role/mms-ifr-resources-prod/mms-ifr-bucket-ro-prod-aws-us-east-1
nds.instances.provisioningLogUpload.enabled=true
nds.instances.provisioningLogUpload.bucket=atlas-provisioning-logs-us-east-1-prod
nds.instances.anomaliesBucket=atlas-anomalies-logs-us-east-1-prod
nds.jira.uri=https://jira.mongodb.org
nds.jira.privateAccessToken=<SECRETMANAGER>
nds.jira.bypassJiraStagingFirewallHeader=
nds.jira.bypassJiraStagingFirewallValue=
nds.leakedItem.cleanup.retries=3
nds.leakedItem.cleanup.mode=JOB_HANDLER
nds.leakedItem.detection.aws.mode=JOB_HANDLER
nds.leakedItem.detection.azure.mode=JOB_HANDLER
nds.leakedItem.detection.gcp.mode=JOB_HANDLER
nds.leakedItem.detection.gcp.chunkSize=50
nds.leakedItem.detection.gcp.projectsProcessedPerIteration=10
nds.leakedItem.aws.shutdownLeakedInstance=true
nds.leakedItem.aws.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.leakedItem.gcp.shutdownLeakedInstance=true
nds.leakedItem.gcp.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.leakedItem.azure.shutdownLeakedInstance=true
nds.leakedItem.azure.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.liveImport.mongosyncLog.ttlDays=730
nds.liveImport.servers.hasPublicIp=true
nds.liveImport.oplogBufferDir=/srv/10gen/atlas-import-prod/buffer
nds.liveImport.publicIpOnly=true
nds.liveImport.sudoUser=atlas-import-prod
nds.liveImport.workingDir=/var/log/atlas-import-prod
nds.liveImport.mongosync.version=1.15.0
nds.liveImport.mongosync.latestVersion=1.15.0
nds.pushLiveImport.mongosync.version=1.13.1
nds.liveImport.mongomirror.version=0.13.1
nds.liveImport.mongomirror.dockerImageTag=v0.13.1-202411011952-80adf99
nds.liveImport.kubeResourceCleaner.enabled=true
nds.liveImport.mongosyncWorkingDir=/var/log/atlas-live-import-prod
nds.liveImport.mongosync.binaryPathPrefix=/opt/10gen/mongosync
nds.liveImport.mongosync.numInsertersPerPartition=4
nds.liveImport.mongosync.maxNumParallelPartitions=4
nds.liveImport.mongomirror.oplogBatchSize=1000
nds.mongosync.log.verbosity=DEBUG
nds.metrics.deliverySystemForBilling.enabled=true
nds.meterUsage.submission.enabled=true
nds.kms.privateNetworking.meterUsage.submission.enabled=true
nds.premiumSKU.meterUsage.submission.enabled=true
nds.mongomirror.log.verbosity=2
nds.mtm.autoscale.capacity.enabled=true
nds.serverless.mtm.autoscale.capacity.enabled=false
nds.mtmCompaction.enabled=true
nds.mtm.lowThreshold.m0=250
nds.mtm.lowThreshold.m2=0
nds.mtm.lowThreshold.m5=0
nds.mtm.lowThreshold.serverless_v2=50
nds.mtm.lowThreshold.flex=50
nds.mtm.maxReservedTenants.m0=62
nds.mtm.maxReservedTenants.m2=0
nds.mtm.maxReservedTenants.m5=0
nds.mtm.maxReservedTenants.aws.us_east_1.m0=125
nds.mtm.maxReservedTenants.flex=12
nds.mtm.organization.id=599eecf19f78f769464d17c7
nds.serverless.mtm.organization.id=6061369bce3adb716456185f
nds.serverless.sentinel.organization.id=663a9c4c541e6e2055525d24
nds.shared.sentinel.organization.id=663a9ceb66e117266aba1563
nds.flex.sentinel.organization.id=66fffbd4d8b0b85d632a3386
nds.okta.oidc.testAuthorizationServer.apiKey=
nds.orphaned.ip.keepHours=36
nds.planExecutorJobPriority.enabled=true
nds.planner.group.interval.short=86400000
nds.planner.group.interval.medium=172800000
nds.planner.group.interval.long=604800000
# collect planner logs for specific groups, values should be comma separated groupId
# i.e. 5fa4d93d5a745371b330871c,5eb12370b68581605a1e1820
nds.planner.group.logger.enhancement=
nds.planning.jobQueue.batchingGroupSize=250
nds.planning.trace.enabled=true
nds.planning.trace.minGroupCountBeforeExport=3000
nds.planning.trace.durationThresholdMillis=10000
nds.planning.trace.report.rootSpanLimit=5
nds.planning.trace.report.enabled=true
nds.planning.trace.export.limit=1
nds.restore.maxConcurrentExports=5
nds.root.cert=ISRGROOTX1
nds.serverless.feature.enabled=true
nds.flex.feature.enabled=true
nds.flex.migration.warning.enabled=true
nds.flex.shared.migration.cron.enabled=true
nds.flex.serverless.migration.cron.enabled=true
nds.flex.migration.cron.schedule=0 * 13-20 ? * 2-6
nds.flex.mtm.organization.id=6724e177bc2dbc3a14327d0c
nds.flex.mtm.autoscale.capacity.enabled=true
nds.instances.flex.version=8.0
nds.serverless.mtm.consumption.debug.logging.enabled=false
nds.serverless.trace.autoscale.enabled=true
nds.serverless.trace.autoscale.exporter=database
nds.serverless.trace.autoscale.prefix=com.xgen.nds.serverless.autoscale
nds.serverless.trace.autoscale.processor=batch
nds.serverless.supportsLBDeploymentIdOnEnvoyInstance=true
nds.serverless.privateNetworking.supportedCloudProviders=AWS,AZURE
nds.serverless.metrics.useMaxIops=true
nds.serverless.version.upgrade.window.enabled=
nds.serverless.version.upgrade.window.size=
nds.serverless.envoy.healthcheck.max.thread.count=10
nds.serverless.group.limits.maxResidentMTMs=85
nds.serverless.load.deadlineSeconds=900
nds.serverless.load.maxThreads=20
nds.serverless.load.minThreads=5
nds.serverless.metrics.mocks.enabled=false
nds.serverless.metrics.api.version=v2
nds.serverless.mtm.autoscale.maxInstanceSize=M80
nds.serverless.mtm.load.updateDocuments.enabled=true
nds.serverless.mtm.limits.maxResidentTenants=2000
nds.serverless.pool.limits.maxResidentMTMs=100
nds.serverless.pool.limits.maxResidentMTMsForAutoScaleMTMCapacity=25
nds.serverless.pool.limits.maxResidentTenants=6000
nds.serverless.pool.limits.maxResidentTenantsThreshold=.2
nds.serverless.mtm.backingInstanceSize=M80
nds.serverless.restore.denylist=admin,config,local
nds.mtm.sentinel.cron.enabled=true
nds.serverless.tenant.privateNetworking.reservedButUnavailablePrivateEndpointExpirationThreshold=P7D
nds.serverless.serverlessUpgradeToDedicated.enabled=true
nds.serverParams.shouldSetRateLimiterParams=true
nds.fastServerlessProvisioning.enabled=true
nds.svc.ipToRegionMapping.executorEnabled=true
nds.tenantUpgrade.s3BucketName=atlas-snapshots-us-east-1-prod
nds.xdsCentralUrl=xds-agents.mongodb.com
nds.fleetAttributeCollection=true
netsuite.account.atlas.revenue.internal.id=1463
netsuite.account.cloud.receivable.internal.id=1541
netsuite.account.contra.liability.internal.id=1686
netsuite.account.mms.revenue.internal.id=257
netsuite.account.reserve.contra.internal.id=1319
netsuite.account.refund.salestax.debit.internal.id=109
netsuite.account.refund.vat.debit.internal.id=160
netsuite.api.accountNumber=1169967
netsuite.api.consumerKey=<SECRETMANAGER>
netsuite.api.consumerSecret=<SECRETMANAGER>
netsuite.app.endpoint=https://1169967.app.netsuite.com/
netsuite.api.endpoint=https://1169967.suitetalk.api.netsuite.com/
netsuite.api.token=<SECRETMANAGER>
netsuite.api.tokenSecret=<SECRETMANAGER>
netsuite.department.d15000.internal.id=213
netsuite.department.d15100.internal.id=259
netsuite.location.dublin.irl.internal.id=10
netsuite.location.nyc.usa.internal.id=4
netsuite.subsidiary.mongo.inc.item.id=1
netsuite.subsidiary.mongo.ltd.item.id=15
netsuite.rest.endpoint=https://1169967.suitetalk.api.netsuite.com/services/rest/record/v1
netsuite.rest.query.endpoint=https://1169967.suitetalk.api.netsuite.com/services/rest/query/v1
netsuite.restlet.endpoint=https://1169967.restlets.api.netsuite.com/app/site/hosting/restlet.nl
netsuite.restlet.tokenEndpoint=https://1169967.suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token
netsuite.restlet.cert.privateKey=<SECRETMANAGER>
netsuite.restlet.cert.id=<SECRETMANAGER>
netsuite.restlet.cert.scope=restlets,rest_webservices,suite_analytics
notification-service.queue.size=2000
officeIps.service.enabled=true
officeIps.service.urlMeshEnvVar=KUBE_OFFICEIPS_SERVICE_KUBE_PROD_ACTIVE_CLOUD_10GEN_CC
okta.account.client.id=<SECRETMANAGER>
okta.account.client.secret=<SECRETMANAGER>
okta.api.token.emp-qa=
okta.api.token=<SECRETMANAGER>
okta.client.id=<SECRETMANAGER>
okta.client.secret=<SECRETMANAGER>
okta.cmab.client.secret=<SECRETMANAGER>
okta.cmab.client.id=0oabsjfygzEYUqVGC297
okta.internal.authorization.server.id=aus4k4jv00hWjNnps297
okta.internal.domain=https://corp.mongodb.com
okta.external.domain=https://auth.mongodb.com
okta.idpDiscoveryPolicyId=00p1c091n88Tz0HJq297
okta.idpId=00o1bkrirn0R4SMFc297
okta.requireNonce=true
okta.restrictedEnv.customerGroup=
okta.restrictedEnv.userGroupNames=
okta.sessionDiscovery.enabled=true
okta.profile.target.id=oty1bkris6I92FPzp297
okta.external.authorization.server.id=default
okta.webhooks.secretKey=
okta.token.authorized.client.ids=0oabtxactgS3gHIR0297,0oaxtqkbj9prde5HQ297
onlinearchive.aws.dataLakeExternalId=<SECRETMANAGER>
onlinearchive.aws.dataLakeRoleARN=<SECRETMANAGER>
onlinearchive.fileCompressionType=snappy
opsgenie.api.eu.url=https://api.eu.opsgenie.com/v2/alerts
opsgenie.api.url=https://api.opsgenie.com/v2/alerts
opsgenie.api.us.url=https://api.opsgenie.com/v2/alerts
pagerduty.events.api.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.us.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.eu.v2.url=https://events.eu.pagerduty.com/v2/enqueue
pagerduty.api.regions.options=US,EU
pagerduty.app.id=P7XGM67
ping.queue.size=150
ping.thread.count=6
ping.throttling.enabled=true
productionScore.hostname=https://production-moment.dsplatform.prod.corp.mongodb.com
prom.saving.intervalSeconds=600
provision.allowReaperOptions=false
provision.allowTerminateAll=false
provision.allowUrlOverride=false
provision.dns.resolution.wait.secs=30
provision.dns.zone=mongodbdns.com
provision.includeRandomPin=true
provision.script.setup.name=run_atm_linux_agent.sh
qualtrics.api.token=<SECRETMANAGER>
queryengine.aws.assumeRoleAccessKey=<SECRETMANAGER>
queryengine.aws.assumeRoleSecretKey=<SECRETMANAGER>
queryengine.aws.assumeRoleUserARN=<SECRETMANAGER>
queryengine.adminApi.gcp.regionName=CENTRAL_US
streams.processManager.enabled=true
iam.gcp.reCaptcha.apiKey=<SECRETMANAGER>
iam.gcp.reCaptcha.project.id=iam-recaptcha-prod
iam.gcp.reCaptcha.site.key=<SECRETMANAGER>
reCaptcha.enabled.atlas=true
reCaptcha.enabled.login=true
iam.reCaptcha.enterprise.login.enabled=true
iam.reCaptcha.login.minScore=0.7
iam.reCaptcha.registration.enabled=true
iam.reCaptcha.registration.minScore=0.65
iam.reCaptcha.external.registration.enabled=true
iam.reCaptcha.external.registration.minScore=0.65
iam.reCaptcha.linkExternalAccount.enabled=false
iam.reCaptcha.linkExternalAccount.minScore=0.7
reCaptcha.enabled.externalRegistration=false
reCaptcha.private.key=<SECRETMANAGER>
reCaptcha.public.key=<SECRETMANAGER>
realm.billing.api.url=https://realm.mongodb.com/api/private/v1.0/billing/metrics/apps
rrd.legacyIngestion.periodSeconds=27
segment.batch.url=https://api.segment.io/v1/batch
segment.clientSide.writeKey=tFLI8kTsjVV1Whb5D6Cgthsjp2f26tRF
segment.group.url=https://api.segment.io/v1/group
segment.identify.url=https://api.segment.io/v1/identify
segment.profile.url=https://profiles.segment.com/v1/spaces
segment.serverSide.accessToken=<SECRETMANAGER>
segment.serverSide.personasSpaceId=spa_smteJ81u1XbJMRgmDR9Td5
segment.serverSide.writeKey=MKfzIHQzxwU6M0S7xldO0dc4EjtK4k97
segment.track.url=https://api.segment.io/v1/track
serverlessProxy.location=https://internal-downloads.mongodb.com/releases/prod/
sfdc.api.campaignId=701A0000000q3z7IAA
sfdc.api.endpoint=https://login.salesforce.com/services/Soap/c/38.0/0DFA0000000PZRg
sfdc.api.integrationOwnerId=005A0000002yZoK
sfdc.api.password=<SECRETMANAGER>
sfdc.api.username=<SECRETMANAGER>
sfdc.sandbox=false
sfdc.sync.cron.enabled=true
sfdc.salesforceIdCache.inUse=true
sfsc.api.password=<SECRETMANAGER>
sfsc.api.username=<SECRETMANAGER>
sfsc.api.client.id=<SECRETMANAGER>
sfsc.api.client.secret=<SECRETMANAGER>
sfsc.sync.url=https://sf-sync.prod.corp.mongodb.com/cloud/org
sfsc.api.endpoint=https://login.salesforce.com/services/Soap/c/38.0/0DFA0000000PZRg
slack.api.message.url=https://slack.com/api/
slack.oauth2.clientId=<SECRETMANAGER>
slack.oauth2.clientSecret=<SECRETMANAGER>
slack.oauth2.url=https://slack.com/api/oauth.access
stitch.analytics.api.url=https://realm.mongodb.com/api/private/v1.0/analytics/reports/apps
stitch.api.key=<SECRETMANAGER>
stitch.billing.api.url=https://realm.mongodb.com/api/private/v1.0/billing/reports/apps
stripe.accountId.ltd=acct_19NoKYJcsjvuVuBZ
stripe.accountId=acct_1fLcLdWa8akf0S84TMot
stripe.apiKey.ltd=<SECRETMANAGER>
stripe.apiKey.inc=<SECRETMANAGER>
stripe.maxRequestsPerSecond=80
stripe.retry.maxAttempts=5
stripe.retry.minBackoff=2000
stripe.pubKey.ltd=<SECRETMANAGER>
stripe.pubKey.inc=<SECRETMANAGER>
stripe.webhook.updatecard.secret=<SECRETMANAGER>
stripe.webhook.updatecard.secret.ltd=<SECRETMANAGER>
support.appEmbedLink=https://support.mongodb.com/api/auth/oidc/initial
taxamo.api.privateToken=<SECRETMANAGER>
taxamo.api.url=https://services.taxamo.com/api/v2
twilio.account.sid=<SECRETMANAGER>
twilio.auth.token=<SECRETMANAGER>
twilio.from.num=<SECRETMANAGER>
twilio.messagingService.sid=<SECRETMANAGER>
university.centralUrls=https://learn.mongodb.com,https://university.mongodb.com,https://learn-staging.mongodb.com,https://api.learn.mongodb.com,https://api-stage.learn.mongodb.com
victorops.alert.api.url=https://alert.victorops.com/integrations/generic/********/alert/
mms.backup.e2e.blockstore.aws.accesskey=
mms.backup.e2e.blockstore.aws.secretkey=
mms.backup.e2e.oplogstore.aws.accesskey=
mms.backup.e2e.oplogstore.aws.secretkey=
mms.beamerApiKey=**********************************************
mms.backup.rollingReplacement.email=true
mms.featureFlag.backup.autoRecoveryUnsafeApplyOps=controlled
nds.xds.bindAddress=127.0.0.1
mms.serverless.envoyInstanceSize=M30
mms.featureFlag.serverless.grpc.incrementalRollout.global.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.tenant.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.continuous.enabled=disabled
mms.featureFlag.serverless.serverlessMtmEligibleForUpgradeToDedicated=controlled
mms.featureFlag.serverless.serverlessSharedUIOptionEnabled=controlled
# This ratio is a double between [0,1]. Setting any value >1 will cause this threshold to effectively not be used.
oa.ratio.partitions.to.files.threshold=1.01
oa.avg.filesize.threshold.kilobytes=1024
# defaultDataSizeThresholdBytes is set to 5 MBs
oa.defaultDataSizeThresholdBytes=5242880
mms.publicApi.prometheus.rateLimitGranularity=1
mms.publicApi.prometheus.rateLimitMaxPerRemoteAddress=120
mms.autoIndexing.sharedTier.maxIndexes=2
mms.clusterActiveSystem=NEW
mms.clusterActiveBatchSize=10
mms.clusterActiveGroupLimit=2500000
mms.clusterActiveNewBroadcast=true
mms.clusterActiveBatchSpread=100
mms.invitationCron.batchSizeBytes=500000
mms.invitationCron.timeoutMs=50
email.dbName=mmsdbcore
azure.ai.openai.compass.accessKey.1=<SECRETMANAGER>
azure.ai.openai.compass.accessKey.2=<SECRETMANAGER>
azure.ai.openai.compass.apiBase.1=https://query-generator-resource-1.openai.azure.com/
azure.ai.openai.compass.apiBase.2=https://query-generator-resource-2.openai.azure.com/
azure.ai.openai.compass.secretKey=<SECRETMANAGER>
azure.ai.openai.compass.modelName=compass-gpt4o
azure.ai.openai.charts.accessKey.1=<SECRETMANAGER>
azure.ai.openai.charts.accessKey.2=<SECRETMANAGER>
azure.ai.openai.charts.apiBase.1=https://atlas-charts-1.openai.azure.com/
azure.ai.openai.charts.apiBase.2=https://atlas-charts-2.openai.azure.com/
azure.ai.openai.charts.gpt4o.accessKey.1=<SECRETMANAGER>
azure.ai.openai.charts.gpt4o.apiBase.1=https://atlas-charts.openai.azure.com/
azure.ai.openai.migrator.accessKey.1=<SECRETMANAGER>
azure.ai.openai.migrator.accessKey.2=<SECRETMANAGER>
azure.ai.openai.migrator.apiBase.1=https://relational-migrator-1.openai.azure.com/
azure.ai.openai.migrator.apiBase.2=https://relational-migrator-1.openai.azure.com/
azure.ai.openai.migrator.gpt4o.accessKey.1=<SECRETMANAGER>
azure.ai.openai.migrator.gpt4o.apiBase.1=https://relational-migrator-2.openai.azure.com/
azure.ai.openai.migrator.gpt4-turbo.accessKey.1=<SECRETMANAGER>
azure.ai.openai.migrator.gpt4-turbo.apiBase.1=https://relational-migrator-3.openai.azure.com/
azure.ingestion.dlz.storageAccountKey=
azure.oa.dlz.storageAccountKey=
azure.cps.oplogs.storageAccountKeys=
mms.monitoring.performanceadvisor.serverless.autoindexing.cronJobEnabled=true
mms.invoiceApi.rateLimitPerMinute=10
mms.invoiceApi.rateLimitPerMinute.getAllInvoices=30
mms.lineItemsApi.rateLimitPerMinute=30
mms.contentStackApiKey=bltf2fca5bf44f5e817
mms.contentStackDeliveryToken=csbecc052f2f89b4c73ffc19ea
mms.contentStackBranch=main
mms.contentStackEnabled=false
# The following three are used for product validation, and are therefore empty above dev.
mms.slack.test.channel=
mms.slack.test.token=
mms.alert.test.email.emailAddress=
mms.alert.test.sms.phoneNumber=
mms.alert.test.microsoftTeams.url=
mms.alert.test.victorOps.apiKey=
mms.alert.test.datadog.apiToken=
mms.alert.test.pagerduty.serviceKey=
mms.alert.test.victorOps.routingKey=
mms.alert.test.webhook.apiKey=
mms.alert.test.opsGenie.apiKey=
mms.account.mfa.getMfaFactors.ratelimit.enabled=true
mms.account.mfa.getMfaFactors.ratelimit.maxHitsPerPeriod=60
mms.account.mfa.getMfaFactors.ratelimit.minutesPeriod=15
mms.account.mfa.enrollMfaFactor.ratelimit.enabled=true
mms.account.mfa.enrollMfaFactor.ratelimit.maxHitsPerPeriod=20
mms.account.mfa.enrollMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaFactor.ratelimit.enabled=true
mms.account.mfa.activateMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.activateMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaPushFactor.ratelimit.enabled=true
mms.account.mfa.activateMfaPushFactor.ratelimit.maxHitsPerPeriod=40
mms.account.mfa.activateMfaPushFactor.ratelimit.minutesPeriod=15
mms.account.mfa.verifyMfaFactor.ratelimit.enabled=true
mms.account.mfa.verifyMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyMfaFactor.ratelimit.minutesPeriod=5
mms.account.mfa.verifyPushFactorChallenge.ratelimit.enabled=true
mms.account.mfa.verifyPushFactorChallenge.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyPushFactorChallenge.ratelimit.minutesPeriod=5
mms.account.mfa.deleteMfaFactor.ratelimit.enabled=true
mms.account.mfa.deleteMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.deleteMfaFactor.ratelimit.minutesPeriod=15
mms.account.auth.getAuthMfaState.ratelimit.enabled=true
mms.account.auth.getAuthMfaState.ratelimit.maxHitsPerPeriod=30
mms.account.auth.getAuthMfaState.ratelimit.minutesPeriod=5
mms.account.auth.verifyAuthMfa.ratelimit.enabled=true
mms.account.auth.verifyAuthMfa.ratelimit.maxHitsPerPeriod=15
mms.account.auth.verifyAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.resendAuthMfa.ratelimit.enabled=true
mms.account.auth.resendAuthMfa.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resendAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.cancelAuthVerify.ratelimit.enabled=true
mms.account.auth.cancelAuthVerify.ratelimit.maxHitsPerPeriod=10
mms.account.auth.cancelAuthVerify.ratelimit.minutesPeriod=15
mms.account.auth.cancelAuthVerify.ratelimit.payload.jsonPath=$.username
mms.account.auth.checkPassword.ratelimit.enabled=true
mms.account.auth.checkPassword.ratelimit.maxHitsPerPeriod=10
mms.account.auth.checkPassword.ratelimit.minutesPeriod=5
nds.embeddedConfig.minMongoDBVersion=8.0
nds.embeddedConfig.maxShardCount=3
mms.account.auth.socialSSO.ratelimit.enabled=true
mms.account.auth.socialSSO.ratelimit.maxHitsPerPeriod=30
mms.account.auth.socialSSO.ratelimit.minutesPeriod=5
mms.user.redirectInvite.ratelimit.enabled=true
mms.user.redirectInvite.ratelimit.maxHitsPerPeriod=60
mms.user.redirectInvite.ratelimit.minutesPeriod=15
mms.user.redirectInvite.ratelimit.pathParam.parameterName=username
mms.user.resetSend.ratelimit.enabled=true
mms.user.resetSend.ratelimit.maxHitsPerPeriod=10
mms.user.resetSend.ratelimit.minutesPeriod=15
mms.user.resetSend.ratelimit.payload.jsonPath=$.username
mms.organization.setSecurityContact.ratelimit.enabled=true
mms.organization.setSecurityContact.ratelimit.maxHitsPerPeriod=10
mms.organization.setSecurityContact.ratelimit.minutesPeriod=5
mms.account.profile.deleteEmailChangeRequest.ratelimit.enabled=true
mms.account.profile.deleteEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.deleteEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.createEmailChangeRequest.ratelimit.enabled=true
mms.account.profile.createEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.createEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.checkPasswordForProfileChange.ratelimit.enabled=true
mms.account.profile.checkPasswordForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.checkPasswordForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.verifyFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyFactorForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.minutesPeriod=15
mms.admin.account.forcePasswordReset.ratelimit.enabled=true
mms.admin.account.forcePasswordReset.ratelimit.maxHitsPerPeriod=200
mms.admin.account.forcePasswordReset.ratelimit.minutesPeriod=1
cypress.aws.ses.accesskey=
cypress.aws.ses.secretkey=
# Experimentation Properties
# analytics.enabled must be true and pholiota properties must be filled if setting the experiments.enabled property to true
analytics.enabled=true
experiments.enabled=true
pholiota.getExperimentsUrl=https://us-east-1.aws.data.mongodb-api.com/app/pholiota-ol-prod-khixd/endpoint/getExperiments
pholiota.mmsApiKey=<SECRETMANAGER>
pholiota.mmsHttpReferer=https://PROD-mms/
streams.planner.enabled=true
streams.vpcpeering.scanner.planner.enabled=true
streams.vpcpeering.scanner.planner.delaySeconds=300
streams.privatelink.planner.enabled=true
streams.proxyInstanceDao.ttlDays=90
streams.proxyDeploymentDao.ttlDays=90
mms.account.profile.getOktaUser.ratelimit.enabled=true
mms.account.profile.getOktaUser.ratelimit.maxHitsPerPeriod=20
mms.account.profile.getOktaUser.ratelimit.minutesPeriod=1
mms.account.profile.updatePassword.ratelimit.enabled=true
mms.account.profile.updatePassword.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updatePassword.ratelimit.minutesPeriod=1
mms.account.profile.updateProfile.ratelimit.enabled=true
mms.account.profile.updateProfile.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updateProfile.ratelimit.minutesPeriod=1
mms.account.profile.activateFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.activateFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.activateFactorForProfileChange.ratelimit.minutesPeriod=1
mms.user.getUserPartnerIntegrationsData.ratelimit.enabled=true
mms.user.getUserPartnerIntegrationsData.ratelimit.maxHitsPerPeriod=100
mms.user.getUserPartnerIntegrationsData.ratelimit.minutesPeriod=20
mms.user.getUserPartnerIntegrationsData.ratelimit.pathParam.parameterName=username
mms.user.registerCall.ratelimit.enabled=true
mms.user.registerCall.ratelimit.maxHitsPerPeriod=100
mms.user.registerCall.ratelimit.minutesPeriod=20
mms.account.auth.unauthedResendVerificationEmail.ratelimit.enabled=true
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.maxHitsPerPeriod=1
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.minutesPeriod=60
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.jsonPath=$.username
mms.account.auth.resendVerificationEmail.ratelimit.enabled=true
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resendVerificationEmail.ratelimit.user.maxHitsPerPeriod=1
mms.account.auth.resendVerificationEmail.ratelimit.user.minutesPeriod=60
mms.account.auth.resetPasswordRequestSend.ratelimit.enabled=true
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.jsonPath=$.username
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.enabled=true
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.jsonPath=$.username
mms.account.auth.resetComplete.ratelimit.enabled=true
mms.account.auth.resetComplete.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resetComplete.ratelimit.minutesPeriod=20
mms.account.auth.resetComplete.ratelimit.payload.jsonPath=$.username
mms.user.mfa.auth.ratelimit.enabled=true
mms.user.mfa.auth.ratelimit.maxHitsPerPeriod=10
mms.user.mfa.auth.ratelimit.minutesPeriod=20
mms.authz.isAuthorized.ratelimit.enabled=true
mms.authz.isAuthorized.ratelimit.maxHitsPerPeriod=10
mms.authz.isAuthorized.ratelimit.minutesPeriod=1
mms.account.device.verifyDeviceCode.ratelimit.enabled=true
mms.account.device.verifyDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.verifyDeviceCode.ratelimit.minutesPeriod=20
mms.account.device.confirmDeviceCode.ratelimit.enabled=true
mms.account.device.confirmDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.confirmDeviceCode.ratelimit.minutesPeriod=20
mms.api.organizations.updateOrganizationSettings.ratelimit.enabled=true
mms.api.organizations.updateOrganizationSettings.ratelimit.maxHitsPerPeriod=10
mms.api.organizations.updateOrganizationSettings.ratelimit.minutesPeriod=5
mms.api.private.account.device.generateCode.ratelimit.enabled=true
mms.api.private.account.device.generateCode.ratelimit.maxHitsPerPeriod=20
mms.api.private.account.device.generateCode.ratelimit.minutesPeriod=1
mms.api.private.account.device.getToken.ratelimit.enabled=true
mms.api.private.account.device.getToken.ratelimit.maxHitsPerPeriod=40
mms.api.private.account.device.getToken.ratelimit.minutesPeriod=1
mms.api.private.account.device.revoke.ratelimit.enabled=true
mms.api.private.account.device.revoke.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.revoke.ratelimit.minutesPeriod=20
mms.api.private.email.sendToSecurityContact.ratelimit.enabled=true
mms.api.private.email.sendToSecurityContact.ratelimit.maxHitsPerPeriod=200
mms.api.private.email.sendToSecurityContact.ratelimit.minutesPeriod=30
mms.api.private.dbUser.validate.ratelimit.enabled=true
mms.api.private.dbUser.validate.ratelimit.maxHitsPerPeriod=500
mms.api.private.dbUser.validate.ratelimit.minutesPeriod=30
mms.api.private.telemetry.events.createEvents.ratelimit.enabled=true
mms.api.private.telemetry.events.createEvents.ratelimit.maxHitsPerPeriod=100
mms.api.private.telemetry.events.createEvents.ratelimit.minutesPeriod=1
mms.api.oauth.tokenManagement.ratelimit.enabled=true
mms.api.oauth.tokenManagement.ratelimit.maxHitsPerPeriod=10
mms.api.oauth.tokenManagement.ratelimit.minutesPeriod=1
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.enabled=false
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.maxHitsPerPeriod=20
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.minutesPeriod=1
mms.generativeAI.compass.enabled=true
mms.generativeAI.compass.percentAccessEnabled=100
mms.generativeAI.dataExplorer.enabled=true
mms.logcollection.s3uploader.partsize=********
mms.monitoring.accesslogs.intervalRetentionHours=144
mms.monitoring.agent.intervals.confCollectionMillis=85000
mms.monitoring.clientmetadata.pruneRatePerSecond=500
mms.monitoring.clientmetadata.retentionDays=21
mms.monitoring.collStatsLatency.gradualRollOutPercentage=100
mms.monitoring.explorer.DataExplorerSvc.sleepBetweenDropsMillis=1000
mms.monitoring.hostLastDailyPings.pruneRatePerSecond=450
mms.monitoring.hostLastPings.batchSize=250
mms.monitoring.iaslowlogs.readRolloutPercentage=100
mms.monitoring.iaslowquerylogs.ingestion.offertimemillis=100
mms.monitoring.iaslowquerylogs.maintenance.repeatDurationHours=1
mms.monitoring.ingestion.database.rollup.job.enabled=false
mms.monitoring.ingestion.host.rollup.job.enabled=false
mms.monitoring.ingestion.kinesis.streams.dedicated.tokenizedQueryShapeStats.enabled=true
mms.monitoring.ingestion.kinesis.streams.shared.ingestion.enabled=true
mms.monitoring.ingestion.rollup.batchWriteSize=500
mms.monitoring.ingestion.rollup.cursorBatchSize=10000
mms.monitoring.ingestion.rollup.job.PT1H.workingRetryWait=5400000
mms.monitoring.ingestion.rollup.job.workingRetryWait=3300000
mms.monitoring.ingestion.rollup.maxAccumulatorTasks=10000
mms.monitoring.ingestion.rollup.maxNamespaces=500
mms.monitoring.ingestion.rollup.maxRunningRollups=4
mms.monitoring.ingestion.rollup.maxStorageDatabases=500
mms.monitoring.ingestion.storeGroupPingsSampleRate=0.10
mms.monitoring.ingestion.storeHostLastDailyPings=true
mms.monitoring.ingestion.storeSharedTierHostLastPings=true
mms.monitoring.kinesis.datawarehouse.mmsdb.indexStatistics.tokenizationEnabled=true
mms.monitoring.managedslowms.metadata.limit=80000
mms.monitoring.managedslowms.setprofilinglevel.maxDeviationPercent=98
mms.monitoring.managedslowms.setprofilinglevel.maximumTargetSlowMs=200
mms.monitoring.managedslowms.setprofilinglevel.minimumTargetSlowMs=35
mms.monitoring.monitoringslowlogs.db.numInitialChunks=8000
mms.monitoring.parseProfilerPayload.enabled=true
mms.monitoring.pausefreetier.cacheRefreshMillis=40000
mms.monitoring.pausefreetier.idleThresholdInDays=2
mms.monitoring.performanceadvisor.serverless.autoindexing.experimentEnabled=true
mms.monitoring.performanceadvisor.serverless.autoindexing.groupPercentageAllowed=100
mms.monitoring.performanceadvisor.serverless.autoindexing.maxCpuPercent=100
mms.monitoring.performanceadvisor.serverless.autoindexing.maxIoWaitPercent=100
mms.monitoring.performanceadvisor.serverless.autoindexing.maxMemoryUtilPercent=90
mms.monitoring.performanceadvisor.serverless.autoindexing.replicationLagSeconds=60
mms.monitoring.performanceadvisor.serverless.sizebased.allocationpercentage=100
mms.monitoring.pinnedNamespaceCollectionLimit=15
mms.monitoring.discovery.secondaryReads.enabled=true
mms.monitoring.rrd.databaseNumInitialChunks=2000
mms.monitoring.rrd.fts.metricscluster.enabled=true
mms.monitoring.rrd.hostNumInitialChunks=9000
mms.monitoring.rrd.preallocateDbMeasurementCollectionsUntilMillis=259200000
mms.monitoring.rrd.preallocateHostMeasurementCollectionsUntilMillis=259200000
mms.monitoring.rrd.rawdisk.drain.interval=200
mms.monitoring.rrd.rawprocess.queue.size=400
mms.monitoring.rrd.rawsystem.drain.interval=200
mms.monitoring.rrd.rawsystemmem.queue.size=400
mms.monitoring.rrd.serverlessNumInitialChunks=600
mms.monitoring.serverlessProfilingFilter.enabled=true
mms.monitoring.slowlogs.compressed.rate=1.0
mms.monitoring.slowlogs.ingestion.iaWritesPercentage=1.0
mms.monitoring.slowlogs.ingestion.parsingPercentage=1.0
mms.monitoring.slowlogs.ingestion.targetLogsPerMinute=150.0
mms.monitoring.slowlogs.ingestion.targetRate=1
mms.monitoring.slowlogs.intervalRetentionHours=168
mms.monitoring.slowlogs.rateLimit.linesPerHost=86400
mms.monitoring.slowlogs.rateLimit.linesPerTenant=18000
mms.monitoring.slowlogs.rateLimit.linesPerTenantServerless=18000
mms.monitoring.tokenization.allowExclusions=true
# MongoDB versions >= 7.1 have tokenized query shape stats automatically enabled by the agent, so we only need to list out versions and rollout percentages before 7.1
mms.monitoring.tokenizedQueryShapeStatsMetrics.rollout.versions=6.0.20,7.0.16,7.0.17
mms.monitoring.tokenizedQueryShapeStatsMetrics.rolloutPercentage.version.6.0=100
mms.monitoring.tokenizedQueryShapeStatsMetrics.rolloutPercentage.version.7.0=100
mms.monitoring.topNamespaceCollectionLimit=20
mms.monitoring.totalNamespaceCollectionLimit=40
mms.platformdiagnostics.stackTrace.jobs.threadDumpPollIntervalMillis=1000
monitoring.databasemeasurements.writes.cutover.enabled=true
monitoring.hostmeasurements.writes.cutover.enabled=true
monitoring.hosts.metrics.alert.mode=READ_DEST_ONLY
monitoring.serverlessmeasurements.writes.cutover.enabled=true
prom.saving.publicMetrics=true
okta.client.connectionTtlMilli=4000
nds.acme.phase1RolloutToExistingClusters=true
nds.acme.newDedicatedClusterAllowMixedCerts=true
aws.atlasSelfServeMarketplaceProductId=ab5111b7-41e1-4340-8742-3246c00b0032
aws.marketplaceSellerId=c9032c7b-70dd-459f-834f-c1e23cf3d092
mms.azurenative.AzureNativeRateLimiting.ratelimit.enabled=true
mms.azurenative.AzureNativeRateLimiting.ratelimit.maxHitsPerPeriod=100
mms.azurenative.AzureNativeRateLimiting.ratelimit.minutesPeriod=5
mms.partnerIntegrations.vercelNative.ratelimit.enabled=true
mms.partnerIntegrations.vercelNative.ratelimit.suspendableIp.maxHitsPerPeriod=20
mms.partnerIntegrations.vercelNative.ratelimit.suspendableIp.minutesPeriod=1
mms.partnerIntegrations.vercelNative.ratelimit.pathParam.maxHitsPerPeriod=100
mms.partnerIntegrations.vercelNative.ratelimit.pathParam.minutesPeriod=5
mms.partnerIntegrations.vercelNative.ratelimit.pathParam.parameterName=installationId
mms.partnerIntegrations.vercelNativeGet.ratelimit.enabled=true
mms.partnerIntegrations.vercelNativeGet.ratelimit.suspendableIp.maxHitsPerPeriod=100
mms.partnerIntegrations.vercelNativeGet.ratelimit.suspendableIp.minutesPeriod=1
mms.partnerIntegrations.vercelNativeGet.ratelimit.pathParam.maxHitsPerPeriod=500
mms.partnerIntegrations.vercelNativeGet.ratelimit.pathParam.minutesPeriod=5
mms.partnerIntegrations.vercelNativeGet.ratelimit.pathParam.parameterName=installationId
mms.partnerIntegrations.vercelNative.canProvisionNewResources=true
mms.partnerIntegrations.vercelNative.vercelProductId=iap_BAyprWZjlpwv2Uqo
mms.partnerIntegrations.encryptionKey=<SECRETMANAGER>
mms.featureFlag.gcpArmAxion=disabled
mms.featureFlag.azureArmDpsv6Epsv6=disabled
mms.featureFlag.horizontalScalingAutoSharding=disabled
mms.featureFlag.gcpN4Family=disabled
integrations.deletion.dryRun=false
slack.oauth.v2.clientId=<SECRETMANAGER>
slack.oauth.v2.clientSecret=<SECRETMANAGER>
slack.oauth.v2.url.backend=https://slack.com/api/oauth.v2.access
slack.oauth.v2.url.frontend=https://slack.com/oauth/v2/authorize
slack.oauth.v2.scopes=chat:write,chat:write.customize,chat:write.public
slack.oauth.v2.enabled=true
mms.disaggregatedStorage.fetchLatestBuildFromDev.globalAdminApiSecret=
nds.shadowClusters.enabled=false
nds.gcp.provisionedIOPS.enabled=false
eventbus.publisher.queueingEnabled=true
eventbus.publisher.awsRoleArn=arn:aws:iam::************:role/event-service-resources-prod/event-service-queue-producer-prod-aws-us-east-1
eventbus.publisher.kinesis.streamName=event-service-stream-prod-aws-us-east-1
communication.publisher.profile=aws
communication.publisher.awsRegion=us-east-1
communication.publisher.awsRoleArn=arn:aws:iam::************:role/communication-service-resources-prod/communication-service-queue-prod-aws-us-east-1
communication.messagebus.publisher.kinesis.streamName=communication-service-stream-prod-aws-us-east-1
communication.statusUpdate.publisher.kinesis.streamName=status-update-stream-prod-aws-us-east-1
customer.metrics.service.baseUrl=https://observability-agents.mongodb.com
mms.new.db.user.scramIterationCount=1300000
mms.new.db.user.scramSHA256IterationCount=600000
maas.ingestion.usecase.150760c1-7a40-5033-b8ff-265e24a4a6b0.adminKey=<SECRETMANAGER>
maas.ingestion.usecase.a152094e-259b-5d73-bd16-e49e3fd6899c.adminKey=<SECRETMANAGER>
maas.internalGrafana.url=https://grafana.helix.corp.mongodb.com
maas.internalGrafana.usecaseId.150760c1-7a40-5033-b8ff-265e24a4a6b0.datasourceId=aenve3lpykc1sa
maas.internalGrafana.usecaseId.296e3c80-f42c-51f1-9a84-5477e61d0277.datasourceId=cenv6rnn3j8cgf
maas.internalGrafana.usecaseId.336c2b2f-e495-5e7d-9a9c-9e6f492fa157.datasourceId=feqn009sme6tcc
maas.internalGrafana.usecaseId.82653516-67ba-5ffa-be02-5e4dd0376682.datasourceId=denvcw3620wsgd
maas.internalGrafana.usecaseId.ff6b9a5f-39e0-5d18-9248-b29c9b4a97a9.datasourceId=ceok1866zzo5ce
# Rate Limiting Configuration - filter enabled via config service feature flag mms.featureFlag.apix.ratelimit.apiMiddleware
## Shadow mode (false = log only, true = reject requests) - currently disabled for future use
ratelimit.apiMiddleware.rejectRequests=false
## Circuit Breaker Configuration (disabled via circuitbreaker.enabled=false)
ratelimit.apiMiddleware.circuitbreaker.name=ratelimit-redis-breaker
ratelimit.apiMiddleware.circuitbreaker.failureRateThreshold=40.0
ratelimit.apiMiddleware.circuitbreaker.slidingWindowSize=50
ratelimit.apiMiddleware.circuitbreaker.permittedCallsInHalfOpenState=3
ratelimit.apiMiddleware.circuitbreaker.waitDurationSeconds=15
ratelimit.apiMiddleware.circuitbreaker.slowCallRateThreshold=20.0
ratelimit.apiMiddleware.circuitbreaker.slowCallDurationThresholdMillis=50
## the minimum number of calls which are required before circuitbreaker can calculate error rate
## default value 100 + 1 to count for the redis coldstart.
ratelimit.apiMiddleware.circuitbreaker.minimumNumberOfCalls=101
# Redis cache configuration (if disabled, in-memory local cache will be used)
ratelimit.redis.enabled=false
ratelimit.redis.auth.mode=AWS
ratelimit.redis.endpoint=
ratelimit.redis.port=6379
ratelimit.redis.cache.name=rate-limit-service-cache-prod
ratelimit.redis.region=us-east-1
ratelimit.redis.roleArn=
ratelimit.redis.credentials.username=rate-limit-service-cache-prod
ratelimit.redis.credentials.password=
ratelimit.redis.ssl.enabled=true
## IP Policy Configuration
ratelimit.policies.default.ip.config.capacity=400
ratelimit.policies.default.ip.config.refillRate=100
ratelimit.policies.default.ip.config.refillDurationSeconds=60
## Group Policy Configuration
ratelimit.policies.default.group.config.capacity=2000
ratelimit.policies.default.group.config.refillRate=1000
ratelimit.policies.default.group.config.refillDurationSeconds=60
## Organization Policy Configuration
ratelimit.policies.default.organization.config.capacity=5000
ratelimit.policies.default.organization.config.refillRate=2000
ratelimit.policies.default.organization.config.refillDurationSeconds=60
## User Policy Configuration
ratelimit.policies.default.user.config.capacity=1000
ratelimit.policies.default.user.config.refillRate=500
ratelimit.policies.default.user.config.refillDurationSeconds=60
# Data Exfiltration Control Plane IPs
nds.dataExfiltration.loadBalancerNames=*
