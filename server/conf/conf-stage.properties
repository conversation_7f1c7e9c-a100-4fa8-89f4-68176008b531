client.authn.http.addressEnvVar=KUBE_AUTHN_SERVICE_KUBE_HTTP_STAGING_GLOBAL_CLOUD_10GEN_CC
client.authn.grpc.addressEnvVar=KUBE_AUTHN_SERVICE_KUBE_STAGING_GLOBAL_CLOUD_10GEN_CC
client.authz.grpc.addressEnvVar=KUBE_AUTHZ_SERVICE_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC
client.authzv2.grpc.addressEnvVar=KUBE_AUTHZ_SERVICE_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC
client.comment.grpc.addressEnvVar=KUBE_MMS_COMMENT_SERVICE_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC
client.meterusages.grpc.addressEnvVar=KUBE_MMS_METERING_METER_USAGES_SERVICE_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC
client.payments.grpc.addressEnvVar=KUBE_PAYMENTS_SERVICE_KUBE_STAGING_ACTIVE_AWS_CLOUD_STAGING_10GEN_CC
client.config.grpc.addressEnvVar=KUBE_CONFIG_SERVICE_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC
client.communication.grpc.addressEnvVar=KUBE_COMMUNICATION_SERVICE_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC
client.event.grpc.addressEnvVar=KUBE_EVENTS_API_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC
client.pops.grpc.addressEnvVar=KUBE_SODAFOUNTAIN_SERVICE_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC
client.apiregistry.grpc.addressEnvVar=KUBE_API_REGISTRY_SERVICE_KUBE_GRPC_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC
client.metrics.http.addressEnvVar=KUBE_CUSTOMER_METRICS_INGESTION_KUBE_HTTP_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC
client.slsbackup.v1.grpc.addressEnvVar=KUBE_SLSBACKUP_SERVICE_KUBE_STAGING_ACTIVE_CLOUD_STAGING_10GEN_CC
client.pricing.grpc.addressEnvVar=KUBE_MMS_PRICING_SERVICE_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC
client.predictive-metrics.grpc.address=predictive-scaling-grpc-api.predictive-scaling-staging.svc.cluster.local:50051
client.predictive-metrics-test.grpc.address=predictive-scaling-debug-grpc-api.predictive-scaling-staging.svc.cluster.local:50051
account.centralUrl=https://account-stage.mongodb.com
account.session.maxConcurrent=10
account.session.maxHours=12
alert.flapping.start.threshold=0.4
alert.flapping.stop.threshold=0.3
alert.flapping.linear.weight=0.2
alert.flapping.strategy=fixed
alert.flapping.skip.enabled=true
alert.flapping.enabled.types=OUTSIDE_METRIC_THRESHOLD
alert.flapping.sufficient.states.limit=20
amplitude.apiKey=
amplitude.secretKey=
atlas.backup.regional.deployment.enabled=true
mms.featureFlag.awsGraviton4=controlled
mms.featureFlag.awsGraviton4NVMe=controlled
mms.featureFlag.awsCapacityAwareAZSelection=enabled
mms.featureFlag.azureCapacityAwareAZSelection=enabled
mms.featureFlag.gcpCapacityAwareAZSelection=enabled
mms.featureFlag.capacityAwareAZPreventativeRollbackSupport=controlled
mms.featureFlag.automationChangesWebClient=disabled
mms.featureFlag.atlasDataRegionalization=controlled
mms.featureFlag.atlasDataRegionalizationGroup=controlled
mms.featureFlag.atlasAzureSsdPV2=enabled
mms.featureFlag.atlasAzureSsdPV2Wave2=controlled
mms.featureFlag.atlasAzureSsdPV2EnablePreviewRegions=controlled
mms.featureFlag.atlasAzureSsdForcePV1=controlled
mms.featureFlag.logcollectionS3Backend=disabled
mms.featureFlag.atlasProvideHardcodedOidcIdpInformation=controlled
mms.featureFlag.clusterCentricPerformanceAdvisor=enabled
mms.featureFlag.clusterCentricQueryProfiler=controlled
mms.featureFlag.namespaceInsights=controlled
mms.featureFlag.updateMetricsUiForEmbeddedConfigServers=enabled
mms.featureFlag.queryInsights=disabled
mms.featureFlag.atlasOptOutPeriodicCorruptionDetection=controlled
mms.featureFlag.atlasDbcheckEnableMidCollectionResumability=controlled
mms.featureFlag.atlasPrioritizeHavingPrimaryOnUpscaledNode=enabled
mms.featureFlag.atlasDiskWarmingSupport=enabled
mms.featureFlag.atlasIgnoreDiskWarmingState=controlled
mms.featureFlag.sqlSchemaManagementUI=enabled
mms.featureFlag.streamsEnableAzure=enabled
mms.featureFlag.streamsEnableAdditionalAzureRegions=enabled
mms.featureFlag.streamsAzurePrivateLink=enabled
mms.featureFlag.streamsAWSPrivateLink=controlled
mms.featureFlag.streamsEnableAdditionalRegions=enabled
mms.featureFlag.atlasDbcheckWaitForAllNodesToObserveDbcheckStop=controlled
mms.featureFlag.atlasAutomaticEmbeddedConfigTransitions=enabled
mms.featureFlag.vqpBinning=controlled
mms.featureFlag.atlasAzureDsv5AndEsv5InstanceFamilies=enabled
mms.featureFlag.atlasLiveImportHelixPush=enabled
mms.featureFlag.atlasLiveImportHelixPull=enabled
mms.featureFlag.atlasShardedClustersBehindUniformFrontend=disabled
mms.featureFlag.atlasMongomirrorLiveImportHelix=enabled
mms.featureFlag.atlasLiveImportLargeNodePool=disabled
mms.featureFlag.atlasEnableAdditionalDbcheckValidations=controlled
mms.featureFlag.atlasStreamsSP10InstanceTier=enabled
mms.featureFlag.dataExplorerCompassWeb=controlled
mms.featureFlag.dataExplorerCompassWeb.userControlledDisable=controlled
mms.featureFlag.disableDiscoveryIpAddressAliases=disabled
mms.featureFlag.atlasClusterScalingImprovementsPhase1=enabled
mms.featureFlag.atlasIndependentShardScaling=enabled
mms.featureFlag.atlasAsymmetricShardAutoscaling=controlled
mms.featureFlag.baas.PricingChange=enabled
mms.featureFlag.billingResourceTaggingBulkEndpoint=enabled
mms.featureFlag.streamsVpcPeeringPreview=enabled
mms.featureFlag.enableLogRequestTimeframe=enabled
mms.featureFlag.enabledRevampedTriggersUi=enabled
mms.featureFlag.enableTypeScriptInFunctions=controlled
mms.featureFlag.enterpriseLandingPage=controlled
mms.featureFlag.enterpriseSandbox=controlled
mms.featureFlag.deprecateEndpointsAndDataApi=controlled
mms.featureFlag.deprecateThirdPartyServices=controlled
mms.featureFlag.allowCrossRegionExtendedStorage=controlled
mms.featureFlag.disableDarkReader=disabled
mms.featureFlag.bicDeprecationShowWarning=enabled
mms.featureFlag.bicDeprecation=controlled
mms.featureFlag.exemptFromBicDeprecation=controlled
mms.featureFlag.atlasMigrationHubCutover=enabled
mms.featureFlag.customerPinnedFcvAndMdbDowngrade=enabled
mms.featureFlag.atlasAllowOneNodeDownInPacpcm=enabled
mms.featureFlag.atlasAllowSeveralNodesDownInPacpcm=enabled
mms.featureFlag.atlasGcpHyperdiskExtreme=controlled
mms.featureFlag.atlasAutomationShorterQuiesceTime=enabled
mms.featureFlag.atlasAutomationSpecifyForceTrueForShutdowns=enabled
mms.featureFlag.atlasAllowUpscalingToCleanlyAbandonPlans=enabled
mms.featureFlag.RtppDiskThroughputMetrics=enabled
mms.featureFlag.atlasChainPauseMoves=controlled
mms.featureFlag.atlasFcvPinningInUi=enabled
mms.featureFlag.atlasKmipKeyRotationInMaintenanceWindows=controlled
mms.featureFlag.validateS3ChunksOnDownload=controlled
mms.featureFlag.atlasConfigurableGp3Iops=controlled
mms.featureFlag.atlasExcludeRegionUseast2FromAzureDsv5Esv5Families=controlled
mms.featureFlag.customSessionTimeouts=controlled
mms.featureFlag.atlasAutomatePriorityTakeover=controlled
mms.featureFlag.atlasAdvancedRegionalizedPrivateEndpoints=controlled
mms.featureFlag.atlasResourcePoliciesExtensionWithUi=enabled
mms.featureFlag.serverlessUpgradeToDedicatedDebugMode=disabled
mms.featureFlag.atlasAutohealResyncImprovements=enabled
mms.featureFlag.atlasAutohealRemoveIcmpPing=enabled
mms.featureFlag.atlasAcknowledgesApiKeyFromChefConfCall=enabled
mms.featureFlag.atlasAlwaysManagedDefaultRwConcern=controlled
mms.featureFlag.atlasTunedInstanceIndexBuildLimits=enabled
mms.featureFlag.atlasTurnDiskWarmingOffForAzureLegacy=controlled
mms.featureFlag.atlasAutohealReducedThreshold=enabled
mms.featureFlag.disableScramSha1Auth=controlled
mms.featureFlag.atlasAzureExcludeConstrainedCombos=controlled
nds.autoEnableAtlasTunedInstanceIndexBuildLimitsNewProjects=true
mms.featureFlag.dataExfiltrationManageSecurityGroupsEnabled=disabled
mms.featureFlag.dataExfiltrationSecurityGroupsExcludeAllowAllRuleEnabled=disabled
mms.featureFlag.dataExfilPreventionSidecarEnabled=disabled
mms.featureFlag.dataExfilPreventionSidecarToProxyRoutingEnabled=disabled
mms.featureFlag.atlasAutohealReduceShutdownTime=enabled
mms.featureFlag.atlasAutohealReduceShutdownTimeHealRepair=controlled
mms.atlasAccessTransparencyX509CertsEnabled=false
mms.atlasEmployeeInformationOktaReadOnlyClientCredentials=<SECRETMANAGER>
mms.atlasEmployeeInformationOktaApiKeyUrl=https://corp-qa.mongodb.com
mms.atlasEmployeeInformationOktaApiKeyClientId=0oa2bfy8z3wXb5gET0h8
mms.atlasEmployeeInformationOktaIntegrationEnabled=true
mms.atlasClusterUpdateOptimisticConcurrency=true
mms.featureFlag.streamsVPCPeering=enabled
mms.featureFlag.disableCloudNav=controlled
mms.featureFlag.atlasFcvDowngradeAdminAction=enabled
mms.featureFlag.atlasResourcePolicies=controlled
mms.featureFlag.orgLevelGenAiControlSwitch=enabled
mms.featureFlag.agentRotatesAtlasProxyLogs=enabled
mms.featureFlag.al2023AWSOs=controlled
mms.featureFlag.awsAL2023ForceMigrateNVMe=controlled
mms.featureFlag.al2023AzureOs=controlled
mms.featureFlag.azureAL2023ForceMigrateNVMe=controlled
mms.featureFlag.al2023GCPOs=controlled
mms.featureFlag.allowEnforceMinTls13=controlled
mms.featureFlag.atlasTls13AutoUpgradeEnabled=controlled
mms.featureFlag.atlasDeprecateTls10AndTls11=controlled
mms.featureFlag.dataExplorerGenAIFeatures=controlled
mms.featureFlag.dataExplorerGenAISampleDocument=controlled
mms.featureFlag.disableDataExplorerGenAISampleDocumentPassing=controlled
mms.featureFlag.atlasReleaseAwsIps=controlled
mms.featureFlag.alertHostSSHSessionStarted=controlled
mms.featureFlag.awsUseIpamIp=controlled
mms.featureFlag.autoscaling.predictive.compute.enabled=controlled
mms.featureFlag.autoscaling.predictive.compute.m10m20enabled=disabled
mms.featureFlag.enableParcaAgent=disabled
mms.featureFlag.customer.queryShapeInsights=controlled
mms.rootVolumeMetrics.rolloutPercentage=100
atlas.regionalData.enabled=true
mms.migration.path.verify=false
mms.monitoring.regional.maintenance.enabled=true
mms.monitoring.groupPingData.sharding.enabled=true
mms.monitoring.hostLastDailyPing.sharding.enabled=true
mms.monitoring.hostLastPing.sharding.enabled=true
mms.monitoring.alwaysUpdateCollectedIndex.enabled=true
atlasProxy.location=https://internal-downloads.mongodb.com/atlasproxy/releases/stage/
atlasUIS.location=
atlasUIS.version=0.0.0.0
authn.enabled=true
authn.oauth.enabled=true
authn.oauth.serviceAccounts.enabled=true
authn.oauth.serviceAccounts.alertsEnabled=true
# This issuer depends on the issuer value in AuthN config, they must be the same
authn.oauth.issuer=https://cloud-stage.mongodb.com
authn.adminSecretKey=<SECRETMANAGER>
authn.internalClient.id=mdb_ic_id_643ed56a3a4a4c1704dff702
authn.internalClient.secret=<SECRETMANAGER>
auto.provision.aws.trusted.accesskey=<SECRETMANAGER>
auto.provision.aws.trusted.id=<SECRETMANAGER>
auto.provision.aws.trusted.secretkey=<SECRETMANAGER>
automation.agent.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
automation.agent.location.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
automation.agent.minimumVersion=13.10.0.8620-1
automation.agent.atlasMinimumVersion=13.37.2.9605-1
automation.agent.minimumVersionForClientPIT=4.3.0.2231-1
automation.agent.serverlessLocation=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
automation.agent.serverlessLocation.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
automation.agent.serverlessMinimumVersion=0.0.0.0
automation.agent.version=13.41.0.9760-1
automation.kmipTool.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
automation.kmipTool.location.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
automation.versions.autoRefreshUri=classpath://mongodb_version_manifest.json
automation.versions.source=remote
automation.versions.atlasDecoupledFromCm=false
avalara.accountNumber=**********
avalara.companyCode.ireland=mongodb-ltd-stage
avalara.companyCode.inc=mongodb-stage
avalara.licenseKey=<SECRETMANAGER>
avalara.url=https://sandbox-rest.avatax.com
aws.accessKey.dataWarehouse=<SECRETMANAGER>
aws.secretKey.dataWarehouse=<SECRETMANAGER>
aws.dataWarehouse.bucket=mongo-datawarehouse-staging
aws.dataWarehouse.bucket.prefix=data_dump/cloud/
aws.accesskey=<SECRETMANAGER>
aws.secretkey=<SECRETMANAGER>
aws.dataWarehouse.kinesis.accessKey=<SECRETMANAGER>
aws.dataWarehouse.kinesis.secretKey=<SECRETMANAGER>
aws.ses.endpoint=https://email.us-east-1.amazonaws.com
awsGraviton.minimumMongoDBVersion=5.0
local.azure.multiTenantAppId=
local.azure.multiTenantAppSecret=
baas.centralUrl=https://services.cloud-stage.mongodb.com
baas.legacyCentralUrl=https://realm-staging.mongodb.com
backup.kmip.server.ca.file=conf/kmip_test_server_ca.pem
biConnector.location=https://info-mongodb-com.s3.amazonaws.com/mongodb-bi/v2/
biConnector.location.atlas=https://internal-downloads.mongodb.org/mongodb-bi/v2/
biConnector.minimumVersion=2.3.1
biConnector.version=2.14.24
atlas.biConnector.version=2.14.24
atlas.api.versioning.rejectFutureDateCalls=false
atlas.api.versioning.enabled=true
atlas.api.versioning.preview=enabled
atlas.api.versioning.upcoming=enabled
braintree.clientId=
braintree.environment=sandbox
braintree.merchantAccountId.inc=<SECRETMANAGER>
braintree.merchantAccountId.ltd=<SECRETMANAGER>
braintree.merchantId=<SECRETMANAGER>
braintree.privateKey=<SECRETMANAGER>
braintree.publicKey=<SECRETMANAGER>
braintree.url=https://sandbox.braintreegateway.com
braintree.maxRequestsPerSecond=20
brs.agent.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
brs.agent.minimumVersion=*********-1
brs.agent.version=7.8.1.1109-1
brs.client.pit.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
brs.meterUsage.BatchSize=1000
brs.client.pit.version=13.41.0.9760-1
brs.queryable.lruCacheCapacityMB=512
brs.queryable.mongod.memoryQuotaMB=8192
brs.queryable.proxyDebugPort=26999
brs.queryable.proxyPort=25999
brs.queryable.tls.disabledProtocols=SSLv2Hello,SSLv3,TLSv1,TLSv1.1,TLSv1.3
brs.queryable.tls.disabledCiphers=TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384
brs.queryable.wiredTiger.engineConfig.cacheSizeGB=1
brs.s3.validation.testing=disabled
brs.tunnel.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
brs.tunnel.version=13.41.0.9760-1
mms.backup.snapshot.volume.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/snapshot-volume/${env}/
mms.backup.snapshot.volume.version=13.41.0.9760-1
charts.api.url=http://charts-api-http.charts-staging.us-east-1.aws.cloud.kube:8080/
charts.api.secretKey=<SECRETMANAGER>
charts.apiSecret=<SECRETMANAGER>
charts.stitch.app.id=chartstenantinfo-uqhmt
charts.cache.encryptedCredentials=true
charts.cache.mongoUri=mongodb+srv://b159a591775b50f7e4d9d15125f3593e77b0c9ea8b552dbbbd0ccc1787b67ef2-027ef39da830163c91b0a0aaaaf607b8-e00dbf257d8e3c6e4bb08e2f1f79e2f27aa33c02f85899c3b808f12f810246f9:<EMAIL>/test?retryWrites=true&w=majority&retryReads=false&uuidRepresentation=standard
charts.sample.encryptedCredentials=true
charts.sample.mongoUri=mongodb://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-46609a72d151ed2db76c2db5783f3965-9e2460daed8a95670a97b18065cc9835c5833ed223cd5d6938a583761376a929-9da89b826706b30132f33401ddf6e7c9771c6ffa1a296a1590ece34a5df40f30c44aff7525314498d3308bd17cc02a05918a2a5ce90b96b7c2c2e1891248bd37:<EMAIL>/test?ssl=true&readPreference=secondaryPreferred
charts.centralUrl=https://charts-stage.mongodb.com
charts.stitch.useLocalDeploymentModel=true
charts.template.aws.accessKey=<SECRETMANAGER>
charts.template.aws.secretKey=<SECRETMANAGER>
charts.template.bucketName=<SECRETMANAGER>
charts.upgrade.poolSize=5
charts.version=0.0.0
cmabService.urlSuffix=
daemon.http.bindhostname=0.0.0.0
data.delete.queue.size=500
dataWarehouse.dailyDataExport=false
dataWarehouse.hourlyDataExport=true
dataWarehouse.dataExport.nonOverlappingNamespaces=nds.planner.plans,mmsdbconfig.config.alertConfigHistory
datadog.api.eu.url=https://api.datadoghq.eu/api/v1
datadog.api.url=https://app.datadoghq.com/api/v1
datadog.api.us.url=https://app.datadoghq.com/api/v1
datadog.api.us3.url=https://api.us3.datadoghq.com/api/v1
datadog.api.us5.url=https://api.us5.datadoghq.com/api/v1
datadog.api.ap1.url=https://api.ap1.datadoghq.com/api/v1
datadog.api.us1.fed=https://app.ddog-gov.com/api/v1
datadog.api.us.apiKey=
datadog.api.us.applicationKey=
datadog.api.regions.options=US,US3,US5,EU,AP1,US1_FED
devHub.centralUrls=https://devhub-local.mongodb.com:8000,https://devhub-ui.devrel.staging.corp.mongodb.com
distributed.cron.svc.registries=com.xgen.svc.mms.svc.cron.MmsCronJobRegistry,com.xgen.svc.nds.svc.AtlasClustersCronJobRegistry,com.xgen.svc.brs.svc.cron.BackupCronJobRegistry,com.xgen.svc.mms.svc.billing.audit.BillingAuditorCronRegistry,com.xgen.svc.mms.svc.billing.cron.BillingCronJobRegistry,com.xgen.svc.mms.svc.billing.cron.PaymentCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportAuditorCronJobRegistry
distributedAlert.executorThreadCount=90
distributedAlert.groupCollateQueueOfferTimeoutMs=1000
distributedAlert.groupCollateQueueSize=100
distributedAlert.groupQueueOfferTimeoutMs=1000
distributedAlert.groupQueueSize=100
distributedAlert.hostQueueSize=100
dyn.customerName=<SECRETMANAGER>
dyn.password=
dyn.username=
ecosystem.enabled=true
eloqua.siteId=*********
eloqua.sync.enabled=false
executor.thread.count=5
fts.confcall.circuitBreaker.maxConcurrent=20
fts.decoupled.planner.enabled=true
fts.decoupled.planner.timeoutHours=48
fts.search.coupledClusterUriEnabledClusterSizes=All,Sharded
fts.search.decoupledClusterUriEnabledDeploymentSizes=All,Sharded
ga.accountId=********
ga.enabled=true
ga.p12file=ga-cloud-manager-analytics-api-nonprod.p12
ga.propertyId=UA-********-1
ga.serviceAccount=<EMAIL>
ga.viewId=*********
hipchat.api.message.url.v1=https://api.hipchat.com/v1/rooms
hipchat.api.message.url=https://api.hipchat.com/v2/room
iam.login.postAuth.allowLocalRedirect=false
intercom.apiKey=<SECRETMANAGER>
intercom.appId=wjeh6he1
intercom.enabled=false
intercom.enabledLoggedInPages=false
intercom.qualtricsSurvey.api.token=
intercom.secretKey=<SECRETMANAGER>
internalatlas.appdb.username=<SECRETMANAGER>
internalatlas.appdb.password=<SECRETMANAGER>
internalatlas.ldap.username=
internalatlas.ldap.password=
ipstack.enabled=true
ipstack.api.accessKey=<SECRETMANAGER>
ipstack.api.url=https://api.ipstack.com
job.processor.await.termination.time=60
job.processor.await.termination.unit=SECONDS
job.processor.aws.requests.per.second=10.0
job.processor.azure.requests.per.second=10.0
job.processor.blacklist.refresh.time=5
job.processor.blacklist.refresh.unit=SECONDS
job.processor.core.thread.count=10
job.processor.empty.queue.wait=5000
job.processor.enabled=false
job.processor.keepalive.time=1
job.processor.keepalive.unit=MINUTES
job.processor.max.thread.count=10
job.processor.server.tags.enabled=true
job.processor.server.tags.refresh.time=5
job.processor.server.tags.refresh.unit=SECONDS
job.processor.partitionQuery.numPartitions=1
job.processor.ping.time=10
job.processor.ping.unit=SECONDS
job.processor.ping.staleCount=60
local.aws.secretsRegion=us-east-1,eu-west-1
local.aws.secretsPrimaryRegion=us-east-1
logCollection.maxDiskSpaceMB=20480
marketing.centralUrls=https://mongodb.com,https://www.mongodb.com,https://mongodbcom.website.prod.corp.mongodb.com
mlab.clientId=atlas-stage
mlab.portalBaseUrl=https://www.mlab.com
mlab.sharedSecret=<SECRETMANAGER>
mms.accountMultiFactorAuth.enabled=true
iam.accountMultiFactorAuth.smsBackoffLevels.enabled=true
iam.accountMultiFactorAuth.smsBackoffLevels.lockFactorThreshold=3
iam.accountMultiFactorAuth.smsBackoffLevelOne.rateLimitMinutes=1
iam.accountMultiFactorAuth.smsBackoffLevelTwo.rateLimitMinutes=2
iam.accountMultiFactorAuth.smsBackoffLevelThree.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelFour.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelFive.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelSix.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelSeven.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelEight.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelNine.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelTen.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByOne.countryCodes=62,92,880
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByTwo.countryCodes=998,994,93
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByThree.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByFour.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByFive.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.denyList.countryCodes=223
iam.partnerIntegrations.vercelNative.webhook.clientSecrets=<SECRETMANAGER>
iam.partnerIntegrations.vercelNative.openInProvider.clientId=oac_9cAhQms5A4FX3lqUKrwGoEi3
iam.partnerIntegrations.vercelNative.openInProvider.clientSecret=<SECRETMANAGER>
mms.accountSuspension.enabled=true
mms.accountDeletion.enabled=true
mms.sendAccountDeletionEmailEnabled=false
mms.enableNewAdminUsersPage=true
mms.enableCommentService=true
nds.acme.failover.lets_encrypt.eventThreshold=300
nds.acme.failover.gts.eventThreshold=50
nds.acme.failover.failoverEventLookbackMinutes=5
nds.acme.failover.retryIntervalMinutes=30
nds.acme.failover.retryResolutionIntervalMinutes=60
mms.acme.defaultProvider=LETS_ENCRYPT_V2
mms.acme.tracking.publicKey=<SECRETMANAGER>
mms.acme.tracking.privateKey=<SECRETMANAGER>
nds.acme.phase1RolloutToExistingClusters=true
nds.acme.newDedicatedClusterAllowMixedCerts=true
mms.addUserApi.countryRequired=true
mms.admin.queuedAdminActions.enabled=true
mms.admin.queuedAdminActions.ui.enabled=true
mms.admin.filterBackupJobs=false
mms.adminEmailAddr=<EMAIL>
mms.agentCentralUrl=https://api-agents-stage.mongodb.com
mms.automation.realtime.centralUrl=https://real-time-api-agents-qa.mongodb.com
mms.alertFromEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertFromEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alertReplyToEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertReplyToEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alerts.AutomationAgentDown.maximumPingAgeMinutes=2
mms.alerts.BackingDatabaseProcessDown.connectTimeoutMs=10000
mms.alerts.BackupAgentDown.maximumPingAgeMinutes=15
mms.alerts.BackupDaemonLowFreeHeadSpace.frequency=0 0 * * * ?
mms.alerts.BackupJobTooBusy.frequency=0 0 0 * * ?
mms.alerts.BiConnectorAlert.maximumPingAgeMinutes=9
mms.alerts.ClusterMongosIsMissing.maximumPingAgeMinutes=9
mms.alerts.HostDown.maximumPingAgeMinutes=9
mms.alerts.HostExposed.addToNewGroups=true
mms.alerts.HostHasIndexSuggestions.frequency=0 * * * * ?
mms.alerts.ServerlessProxyDown.maximumPingAgeMinutes=7
mms.alerts.FlexProxyDown.maximumPingAgeMinutes=7
mms.alerts.MonitoringAgentDown.maximumPingAgeMinutes=7
mms.alerts.RealmDown.maximumPingAgeMinutes=7
mms.alerts.NdsProxyDown.maximumPingAgeMinutes=5
mms.alerts.OplogBehind.maximumOplogPushAgeMinutes=75
mms.alerts.PrepaidPlanMissingSKU.frequency=0 0 7 * * ?
mms.alerts.ReplicaSetAlertCheck.maximumPingAgeMinutes=9
mms.alerts.StalePendingInvoices.frequency=0 0 7 * * ?
mms.alerts.SubscriptionUsageCronJobDidntRunLastHour.frequency=0 0 * * * ?
mms.alerts.SystemDatabaseProcessStartupWarnings.frequency=0 0 * * * ?
mms.alerts.TooManyPendingInvoices.frequency=0 0 7 * * ?
mms.alerts.EncryptionAtRestKMSNetworkAccessDenied.frequency=PT15M
mms.alerts.EncryptionAtRestConfigNoLongerValid.frequency=PT15M
mms.alerts.createSystemAlertsEnabled=false
mms.alerts.delaySeconds=120
mms.alerts.disabledEventTypes=CUSTOM_SESSION_TIMEOUT_MODIFIED
mms.alerts.intervalSeconds=30
mms.alerts.unreachable.lookBackMinutes=5
mms.alerts.unreachable.minimumConfCount=100
mms.alerts.unreachable.thresholdPercent=90
mms.alerts.webhook.readTimeoutMs=5000
mms.alerts.webhook.socketTimeoutMs=2000
mms.alerts.GlobalServiceAccountSecretExpiredAlertCheck.frequency=PT12H
mms.alerts.GroupServiceAccountSecretsExpiring.frequency=PT12H
mms.atlasApiDeprecation.fromEmailAddr=<EMAIL>
mms.atlasApiDeprecation.replyToEmailAddr=<EMAIL>
mms.allowOpsManagerOrgs=false
mms.apiregistry.cronjob.enabled=true
mms.api.readPreference=secondaryPreferred
mms.api.throttling.enabled=false
mms.assets.assetsHostname=https://assets-stage.mongodb-cdn.com/mms
mms.assets.minified=true
mms.assets.packaged=true
mms.atlas.backup.skipBillingOnOplogMigrationDestination=false
mms.atlas.backup.skipBillingOplogMigrationForAzureOplogStore=false
mms.atlas.backup.skipBillingOplogMigrationForGcpOplogStore=false
mms.atlas.defaultReplicaSetScalingStrategy=WORKLOAD_TYPE
mms.atlasAWSMinimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlas.gcp.minimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlasClusterWebsocketConnection.baseUrl=wss://atlas-cluster-ws.cloud-stage.mongodb.com
mms.atlasSearch.tracingSamplerRate=0.0
nds.serverless.sniproxy.default.count=3
mms.auth.logo.fileName=mdb_logo
mms.auth.methods.google.enabled=true
mms.auth.methods.google.oktaIdpId=0oa1bdkv07SQWRRIB357
mms.auth.methods.github.enabled=true
mms.auth.methods.github.oktaIdpId=0oaijfsmclTTzWxCs357
mms.authz.enableAuthzSvc=true
mms.authz.roleSetParityCheck.percentage=-1
mms.authz.epaParityCheck.percentage=-1
mms.authz.syncResources=true
mms.authz.tags.timeoutMillis=100
mms.authz.syncTeamsToUserGroups=true
mms.authz.mandatoryResourceSync.percentage=-1
mms.authz.usersToSkipForFgaChecks=578cec0fe4b052aebe8fb3f6,51b0a5c8e4b0344b149f9a2d,527a99e7e4b009359de228bb,52546897e4b0ddc6df873bdc,530b605ae4b0751692793133,53a078e0e4b0a536c8eba410,56462e0de4b09736dcc45956,57cececa33d9326dabd147ab,5d238cf40002f51f24f34235,5d238cf50002f51f24f34236,5d238cf50002f51f24f34277,631ee3c113c5b91422aad4bf
mms.userGroupFailureJob.retryDelayInMin=3
mms.authz.rolesCache.enabled=true
mms.userGroupFailureJob.numberRetries=3
mms.cps.billing.cpsAwsDownloadUsageSubmission=true
mms.cps.billing.cpsAwsExportUsageSubmission=true
mms.cps.billing.cpsAwsSnapshotUsageSubmission=true
mms.cps.billing.cpsAzureDownloadUsageSubmission=true
mms.cps.billing.cpsAzureExportUsageSubmission=true
mms.cps.billing.cpsAzureSnapshotUsageSubmission=true
mms.cps.billing.cpsGcpDownloadUsageSubmission=true
mms.cps.billing.cpsGcpExportUsageSubmission=true
mms.cps.billing.cpsGcpSnapshotUsageSubmission=true
mms.cps.billing.cpsExportUsageSubmission=true
mms.cps.billing.oplogUsageCollection=true
mms.cps.billing.oplogUsageSubmission=true
mms.cps.billing.oplogUsageMigration=true
mms.cps.billing.oplogStorageRegionUsage=true
mms.cps.directAttachPreWarm.skip.gcp=true
mms.cps.collectionMetadataBucketRegionName=us-east-1
mms.cps.collectionMetadataBucketRegionNameSecondary=us-east-2
mms.cps.collectionMetadataBucket=atlas-backup-collection-metadata-us-east-1-stage
mms.cps.collectionMetadataBucketSecondary=atlas-backup-collection-metadata-us-east-2-stage
mms.cps.collectionRestore.diskSpaceThreshold=.90
mms.customer.metrics.exporter.diskingestable.usecaseId=296e3c80-f42c-51f1-9a84-5477e61d0277
mms.customer.metrics.exporter.diskingestable.publisherKey=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
mms.runFgaOverRbacWhenAnnotated=false
mms.automation.agentFeatures.migrationHosts.canManageDeployments=false
mms.automation.changes.pruning.retentionDays=30
mms.automation.changes.pruning.changesPerSecond=500
mms.automation.changes.pruning.maxPerRun=1000000
mms.automation.changes.watcher.enabled=false
mms.automation.settings.refreshPeriodSecs=90
mms.automation.settings.dnsCacheTtlSeconds=300
mms.automation.settings.jobManager.refreshPeriodSecs=60
mms.automation.settings.sleepTimes.confCallSleepTimeSecs=15
mms.automation.settings.sleepTimes.errorStateSleepTimeSecs=15
mms.automation.settings.sleepTimes.goalStateSleepTimeSecs=15
mms.automation.settings.agentStatus.sendPeriodSecs=120
mms.automation.settings.agentStatus.maxPlansToSend=5
mms.automation.settings.timeBetweenMongosRollingRestartMs=20000
mms.featureFlag.backup.s3blockstore.calculateMD5=disabled
mms.featureFlag.backup.s3oplogstore.calculateMD5=disabled
mms.backup.alertsEmailAddr=<EMAIL>
mms.backup.allowDevelopmentVersions=true
mms.backup.allowFastSchedule=true
mms.backup.allowRefTimeOfDay=false
mms.backup.allowSnapshotExpiryUpdate=true
mms.backup.automatedRestoreExpirationHours=48
mms.backup.daemonAssignment.replSetSizeMultiple=1.3
mms.backup.daemonAssignment.replSetSizeMultiplePreferred=1.1
mms.backup.daemonAssignment.usableHeadPercent=0.80
mms.backup.daemonAssignment.usableHeadPreferredPercent=0.95
mms.backup.daemon.diagnostics.minNumJobs=4
mms.backup.datastore.refresh=false
mms.backup.donate.chosenJob.space.lower.threshold=0.04
mms.backup.donate.chosenJob.space.upper.threshold=0.13
mms.backup.donate.donor.space.threshold=0.85
mms.backup.logSnapshotFileInfo=false
mms.backup.maximumPITRestoreOplogWindowDays=7
mms.backup.minimumOplogWindowHours=0
mms.backup.provisioningRequired=true
mms.backup.region.default=NY_NJ
mms.backup.s3OplogStoreEnabled=true
mms.backup.snapshotValidation.kubernetesNamespace=snapshot-validation-staging
mms.backup.wiredTiger.engineConfig.cacheSizeGB=2
mms.backupCentralUrl=https://api-backup-stage.mongodb.com
mms.backup.snapshotValidation.s3Bucket.prefix=snapshot-validation-staging
mms.backup.snapshotValidation.roleArnPrefix=arn:aws:iam::************:role/mms-bgrid-resources-staging/bgrid-snapshot-validation-rw-staging-aws
mms.backup.snapshotValidation.kubernetesClusterName=kube-1-%s-aws-cloud
mms.billing.alertsEnabled=false
# Start of Billing Anomaly Detection properties
mms.billing.bad.clientId=
mms.billing.bad.secret=
# --- End of Billing Anomaly Detection properties
mms.billing.bccEmailAddress=<EMAIL>
mms.billing.enabled=true
mms.billing.salescomp.enabled=true
mms.billing.cronJobsDisabled=false
mms.billing.gcp.authProviderX509CertUrl=https://www.googleapis.com/oauth2/v1/certs
mms.billing.gcp.authUri=https://accounts.google.com/o/oauth2/auth
mms.billing.gcp.clientEmail=<EMAIL>
mms.billing.gcp.clientId=<SECRETMANAGER>
mms.billing.gcp.clientX509CertUrl=https://www.googleapis.com/robot/v1/metadata/x509/service-account-officeit-61928%40mdb-gcp-marketplace-test.iam.gserviceaccount.com
mms.billing.gcp.privateKey=<SECRETMANAGER>
mms.billing.gcp.privateKeyId=<SECRETMANAGER>
mms.billing.gcp.projectId=mdb-gcp-marketplace-test
mms.billing.gcp.accountIds=d3806197-e921-4343-aa90-fc287785a82a
mms.billing.gcp.tokenUri=https://oauth2.googleapis.com/token
mms.billing.gcp.type=service_account
mms.billing.gcp.selfServeMetric=mongodb-atlas-self-service-dev.endpoints.mdb-gcp-marketplace-test.cloud.goog/pay_as_you_go_usage
mms.billing.gcp.atlasPrivateMetric=mongodb-atlas-producer-dev.endpoints.mdb-gcp-marketplace-test.cloud.goog/atlas_private_usage
mms.billing.gcp.atlasPrepaidMetric=mongodb-atlas-producer-dev.endpoints.mdb-gcp-marketplace-test.cloud.goog/atlas_prepaid_usage
mms.billing.gcp.selfServeProductName=mongodb-atlas-self-service-dev.endpoints.mdb-gcp-marketplace-test.cloud.goog
mms.billing.gcp.atlasGovAnnualCommitMetric=mongodb-atlas-for-government-private-offer-test.endpoints.mdb-gcp-marketplace-test.cloud.goog/Mongodb_atlas_gov
mms.billing.gcp.atlasGovPrepaidMetric=mongodb-atlas-for-government-private-offer-test.endpoints.mdb-gcp-marketplace-test.cloud.goog/Mongodb_atlas_gov_prepaid_atlas
mms.billing.gcp.marketplaceUrl=https://console.cloud.google.com
mms.billing.aws.mp.accessKey=<SECRETMANAGER>
mms.billing.aws.mp.secretKey=<SECRETMANAGER>
mms.billing.aws.mp.athena.accessKey=<SECRETMANAGER>
mms.billing.aws.mp.athena.secretKey=<SECRETMANAGER>
mms.billing.aws.mp.disbursement.crons.enabled=true
mms.billing.aws.mp.customerIds=7uDMB5LbfdD
mms.billing.aws.mp.productCodes=9maw2mikw1ksjup1olntbvc6r
mms.billing.azure.mp.azureNative.service.marketplaceHost=https://mongodb-billing.gateway-canary.azliftr.io/api/billing
mms.billing.azure.mp.aad.azureNative.service.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.azureNative.service.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.secretKey.expiration=2026-07-10
mms.billing.azure.mp.aad.service.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.service.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.service.secretKey.expiration=2026-07-10
mms.billing.azure.mp.aad.productGraph.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.productGraph.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.productGraph.secretKey.expiration=2024-11-29
mms.billing.azure.mp.corsUrls=https://login.live.com,https://login.microsoftonline.com,https://partner.microsoft.com,https://portal.azure.com
mms.billing.azure.mp.saas.partnerTeam.webhookUrl=https://us-central1-gcp-pov.cloudfunctions.net/azure-marketplace-webhook-nonprod
mms.billing.azure.export.api.storage.account.name=atlasbilling
mms.billing.azure.export.api.client.secretKey=
mms.billing.azure.export.api.blob.container.name=atlasbilling
mms.billing.azure.export.api.clientId=
mms.billing.azure.export.api.tenantId=
mms.billing.azure.export.destinationS3Bucket=
mms.billing.internalPaymentCheck=true
mms.featureFlag.billing.testMeterDataGeneration=disabled
mms.billing.retrieveStripeEvents=false
mms.billing.selfServePayments.enabled=true
mms.billing.selfServeProducts=seed-data/SelfServeProducts.json
mms.billing.sendInvoiceEmails=false
mms.billing.sfdcProductCodes=seed-data/SalesforceProductCodes.json
mms.billing.sfscSyncEnabled=false
mms.billing.statusPage.apiKey=
mms.billing.dataExportConfig=data-export/data-export-conf.json
mms.billing.auditors.config=seed-data/BillingAuditorConfigs.json
mms.billing.statusPage.enabled=false
mms.billing.partners.aws.reporting.mode=ALL
mms.billing.partners.azure.reporting.mode=ALL
mms.billing.partners.gcp.reporting.mode=ALL
mms.billing.pricingApiConsumers=
mms.billing.resourceTagRetrievalBatchSize=500
mms.billing.featureteam.lineitem.rateLimitPerMinute=30
mms.billing.featureteam.lineitem.sku.size=50
mms.billing.featureteam.lineitem.group.size=5
mms.billing.featureteam.lineitem.startdate.size=5
mms.billing.featureteam.lineitem.usagedate.size=5
mms.metering.featureteam.usage.rateLimitPerMinute=30
mms.metering.featureteam.usage.meterid.size=50
mms.metering.featureteam.usage.groupid.size=5
mms.metering.featureteam.usage.reporteddate.size=5
mms.metering.featureteam.usage.startdate.size=5
mms.metering.purge.batchSize=100
mms.metering.purge.maxRuntimeSeconds=60
mms.metering.purge.sleepBetweenDeletionMillis=1000
mms.billingImport.azure.readUser.clientId=cba167e2-60a5-4977-9e14-22a7a86e3b13
mms.billingImport.azure.readUser.tenantId=5537e8ff-e2ba-475a-a67a-daa445542246
mms.billingImport.azure.readUser.clientSecret=<SECRETMANAGER>
mms.billingImport.azure.blobContainer.name=mongodb-atlas-billing
mms.billingImport.azure.blobContainer.accountName=mongodbatlasbilling
mms.billingImport.azure.blobContainer.reportDirectory=billing-profile/mongodb-atlas-billing-v2/
mms.billingImport.azure.useLegacyBillingReportDate=true
mms.payments.chargePayments.maxConcurrency=64
mms.payments.chargePayments.invoiceLock.retries.maxAttempts=20
mms.payments.chargePayments.invoiceLock.retries.minBackoff=2000
mms.payments.chargePayments.invoiceLock.retries.maxBackoff=15000
mms.payments.opportunityChange.auth.jira.uri=https://jira-staging.corp.mongodb.com
mms.payments.opportunityChange.auth.jira.privateAccessToken=<SECRETMANAGER>
mms.payments.yaypay.ui.baseUrl=https://developer.yaypay.com
mms.payments.yaypay.baseUrl=https://developer.yaypay.com/api/v2
mms.payments.yaypay.inc.clientKey=<SECRETMANAGER>
mms.payments.yaypay.inc.clientSecret=<SECRETMANAGER>
mms.payments.yaypay.inc.scope=ar.read,customer.read,crm.read
mms.payments.yaypay.ltd.clientKey=
mms.payments.yaypay.ltd.clientSecret=
mms.payments.yaypay.ltd.scope=
mms.payments.yaypay.brazil.clientKey=
mms.payments.yaypay.brazil.clientSecret=
mms.payments.yaypay.brazil.scope=
mms.payments.internalNetwork.maxRequestsPerSecond=100
mms.payments.opensanctions.enabled=true
mms.payments.opensanctions.apikey=<SECRETMANAGER>
mms.payments.vercel.api.maxRetries=5
mms.payments.vercel.api.minBackoffMillis=2000
mms.payments.vercel.billing.maxRetries=5
mms.payments.vercel.billing.minBackoffMillis=2000
mms.payments.vercel.billing.accessTokenKey=<SECRETMANAGER>
mms.payments.vercel.billingInstallation.ratelimit.enabled=true
mms.payments.vercel.billingInstallation.ratelimit.suspendableIp.maxHitsPerPeriod=6000
mms.payments.vercel.billingInstallation.ratelimit.suspendableIp.minutesPeriod=1
mms.payments.vercel.billingInstallation.ratelimit.pathParam.maxHitsPerPeriod=600
mms.payments.vercel.billingInstallation.ratelimit.pathParam.minutesPeriod=1
mms.payments.vercel.billingInstallation.ratelimit.pathParam.parameterName=installationId
mms.payments.vercel.billingProduct.ratelimit.enabled=true
mms.payments.vercel.billingProduct.ratelimit.suspendableIp.maxHitsPerPeriod=6000
mms.payments.vercel.billingProduct.ratelimit.suspendableIp.minutesPeriod=1
mms.payments.vercel.webhook.ratelimit.enabled=true
mms.payments.vercel.webhook.ratelimit.suspendableIp.maxHitsPerPeriod=6000
mms.payments.vercel.webhook.ratelimit.suspendableIp.minutesPeriod=1
mms.payments.credits.creation.rolloverAmbiguity.email.recipient=<EMAIL>
mms.payments.credits.creation.rolloverAmbiguity.email.replyTo=<EMAIL>
mms.payments.credits.creation.rolloverAmbiguity.email.subjectTemplate=[STAGE] Rollover ambiguity auto-resolved for opportunity %s
mms.payments.mcDiscrepancies.auditors.cutoffDate=2025-09-01
mms.centralUrl=https://cloud-stage.mongodb.com
mms.centralDomainAllowedOrigin=https://cloud-stage.mongodb.com
mms.charts.exportChartsDashboard.ratelimit.enabled=true
mms.charts.exportChartsDashboard.ratelimit.maxHitsPerPeriod=50
mms.charts.exportChartsDashboard.ratelimit.minutesPeriod=10
mms.charts.importChartsDashboard.ratelimit.enabled=true
mms.charts.importChartsDashboard.ratelimit.maxHitsPerPeriod=50
mms.charts.importChartsDashboard.ratelimit.minutesPeriod=10
mms.cloudManagerEnabled=true
mms.cron.enabled=false
mms.cron.track.ttlDays=90
mms.dao.capped.apiRequestTrackDao.sizeInBytes=52428800
mms.dao.capped.default.sizeInBytes=1048576
mms.dao.capped.metricAlertDispatchCounterDao.sizeInBytes=10485760
mms.dao.capped.mlabSharedMigrationLogDao.sizeInBytes=524288000
mms.dao.capped.ndsMongoMirrorLogDao.sizeInBytes=52428800
mms.dao.capped.ndsTenantUpgradeLogDao.sizeInBytes=104857600
mms.databaseIdpDiscovery.enabled=true
mms.databaseIdpDiscovery.mongoOktaId=0oawq91yiP5kvFi5f356
mms.dataExplorer.rateLimit.enabled=false
mms.deploymentdiscovery.enabled=false
mms.docsUrl.nds=https://www.mongodb.com/docs/atlas
mms.docsUrl.search=https://www.mongodb.com/docs/search
mms.docsUrl=https://www.mongodb.com/docs/cloud-manager
mms.email.includeAppEnvName=true
mms.email.includeHostname=true
mms.email.logo.force=false
mms.email.logo.height=35
mms.email.logo.width=140
mms.email.logo=/static/images/logo-mongodb.png
mms.email.validation=STRICT
mms.emailDaoClass=AWS_MAILER
mms.emailVerificationEnabled=false
mms.event.sns.awsAccessKey=<SECRETMANAGER>
mms.event.sns.awsRegion=us-east-1
mms.event.sns.awsSecretKey=<SECRETMANAGER>
mms.event.sns.enabled=true
mms.event.sns.topic.CLUSTER_DELETE_SUBMITTED=arn:aws:sns:us-east-1:************:MMS_NDS_STAGE_CLUSTER_DELETE_SUBMITTED
mms.event.sns.topic.MATERIAL_CLUSTER_UPDATE_COMPLETED_INTERNAL=arn:aws:sns:us-east-1:************:MMS_NDS_STAGE_MATERIAL_CLUSTER_UPDATE_COMPLETED_INTERNAL
mms.event.sns.topic.CHARTS_RELEVANT_CLUSTER_EVENT_INTERNAL=arn:aws:sns:us-east-1:************:CHARTS_RELEVANT_CLUSTER_EVENT_INTERNAL_STAGE
mms.event.event-service.writes.enabled=true
mms.event.event-service.write.sync=false
mms.event.event-service.eventTesting.enabled=false
mms.events.sns.awsRole=arn:aws:iam::************:role/mms-event-publisher-stage
mms.event.errorOnValidationFailure=false
mms.feature.enablePauseFreeTierMonitoring=true
mms.featureFlag.adfa.allowRoutingViaCRMInsteadOfEnvoy=enabled
mms.featureFlag.adl.readConcernMajority=controlled
mms.featureFlag.adl.schemaUniqueFieldLimit=controlled
mms.featureFlag.adl.templateRegexGenerationOptimization=controlled
mms.featureFlag.adl.testManualControlledFlag=controlled
mms.featureFlag.adl.testAutomatedControlledFlag=controlled
mms.featureFlag.adl.testEnabledFlag=enabled
mms.featureFlag.adl.testDisabledFlag=disabled
mms.featureFlag.adl.useMongodInsteadOfMqlrun=enabled
mms.featureFlag.alertStateFlappingDetection=controlled
mms.featureFlag.atlasAllowDeprecatedVersions=controlled
mms.featureFlag.atlasMongoDB80=enabled
mms.featureFlag.atlasAutoApplyIndexesForMTMs=controlled
mms.featureFlag.atlasAzureNVMe=enabled
mms.featureFlag.atlasCrossCloudReplication=enabled
mms.featureFlag.atlasCrossRegionPeering=disabled
mms.featureFlag.atlasDataFederationTemplatedOnboarding=enabled
mms.featureFlag.atlasDataFederationAzureFeedDownstreamSystems=disabled
mms.featureFlag.atlasDataFederationAzurePrivateLink=controlled
mms.featureFlag.atlasDeploysUIS=disabled
mms.featureFlag.atlasSearchResolveViews=enabled
mms.featureFlag.atlasGatewayProxy=controlled
mms.featureFlag.atlasMaintenanceWindows=controlled
mms.featureFlag.atlasMountAzureDiskWithLun=controlled
mms.featureFlag.atlasAzureForceMigrationToAZs=controlled
mms.featureFlag.atlasServerlessGRPCSupport=controlled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettings=disabled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettingsOrg=disabled
mms.featureFlag.atlasProxyDisableRateLimiting=disabled
mms.featureFlag.atlasServerlessUsesServerlessAgent=disabled
mms.featureFlag.atlasEnableTestCommands=disabled
mms.featureFlag.atlasSharedTierX509Auth=controlled
mms.featureFlag.atlasX509CRL=disabled
mms.featureFlag.atlasSearch.dedicatedNodes=enabled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreference=controlled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreferenceGroupOverride=controlled
mms.featureFlag.atlasSearch.synchronousSteadyStateReplication=controlled
mms.featureFlag.atlasSearch.indexManagement=enabled
mms.featureFlag.atlasSearch.indexPartitioningUI=controlled
mms.featureFlag.atlasSearch.vibCustomAnalyzersUI=enabled
mms.featureFlag.atlasSearch.confCallViaSearchGateway=controlled
mms.featureFlag.atlasSearch.disableDetailedStatusWrite=controlled
mms.featureFlag.atlasSearch.searchTesterQueryTemplates=disabled
mms.featureFlag.atlasSearch.searchIndexStatusReporting=disabled
mms.featureFlag.atlasSearch.dedicatedNodesAWSSeriesSeven=enabled
mms.featureFlag.atlasSearch.dedicatedNodesEnableReplicationCompression=controlled
mms.featureFlag.atlasSearch.dedicatedNodesForceDisableReplication=disabled
mms.featureFlag.atlasSearch.dedicatedNodesDisableProvisionTimeout=controlled
mms.featureFlag.atlasSearch.useAggregateCommand=enabled
mms.featureFlag.atlasSearch.multipleSubIndexes=controlled
mms.featureFlag.atlasSearch.staleIndexes=enabled
mms.featureFlag.atlasSearch.byok=disabled
mms.featureFlag.atlasSearch.useLifecycleManager=enabled
mms.featureFlag.atlasSearch.envoy.disableStreamIdleTimeout=controlled
mms.featureFlag.atlasSearch.envoy.loadBalancingPolicyRandom=controlled
mms.featureFlag.atlasSearch.indexConfigStatsWrites=disabled
mms.featureFlag.atlasSearch.searchHostStatsWrites=disabled
mms.featureFlag.atlasSearch.readIndexStatsFromNewCollection=disabled
mms.featureFlag.atlasSearch.enableTextOperatorNewSynonymsSyntax=controlled
mms.featureFlag.atlasSearch.indexConfigUpdater=disabled
mms.featureFlag.atlasSearch.disableMongotJvmNativeAccess=enabled
mms.featureFlag.atlasSearch.indexQuantizationUI=controlled
mms.featureFlag.atlasSearch.mongotPushBasedLogExportDownload=disabled
mms.featureFlag.atlasSearch.dedicatedNodesGrpcMode=controlled
mms.featureFlag.atlasDataLakeStorageForOnlineArchive=disabled
mms.featureFlag.atlasDataLakeStorageForceV2UpgradeForOnlineArchive=controlled
mms.featureFlag.atlasDataLakeIngestionPipelinesDeprecation=enabled
mms.featureFlag.atlasDataLakeStorageAllowHourlyIngestion=controlled
mms.featureFlag.atlasDataLakeStorageDatasetRetentionPolicy=enabled
mms.featureFlag.atlasDataFederationDedicatedHostnames=enabled
mms.featureFlag.atlasDataFederationOnGCP=enabled
mms.featureFlag.atlasCloudProviderAccessForGCP=enabled
mms.featureFlag.atlasOnlineArchiveV3TimeSeries=enabled
mms.featureFlag.atlasOnlineArchiveGCP=enabled
mms.featureFlag.atlasOnlineArchiveV1=controlled
mms.featureFlag.atlasOnlineArchiveAsDataSource=enabled
mms.featureFlag.atlasOnlineArchiveIgnoreDataSizeThreshold=controlled
mms.featureFlag.atlasOnlineArchiveEncryption=controlled
mms.featureFlag.atlasOnlineArchivePinToPrimaryRegion=controlled
mms.featureFlag.atlasPushBasedLogExport=enabled
mms.featureFlag.atlasPushBasedLogExportClusterLevel=controlled
mms.featureFlag.atlasGovGcpAssuredWorkloads=disabled
mms.featureFlag.atlasUseDataExplorerService=controlled
mms.featureFlag.atlasUseProviderSubdomains=disabled
mms.featureFlag.atlasUseLegacyHostnameScheme=disabled
mms.featureFlag.dbAccessDuringTenantUpgradeBlocked=enabled
mms.featureFlag.dbAccessDuringTenantUpgradeAllowed=controlled
mms.featureFlag.atlasMultiCloudGlobalClusters=enabled
mms.featureFlag.atlasMaintenanceAutoDeferral=controlled
mms.featureFlag.atlasCNRegionsOnly=controlled
mms.featureFlag.atlas50Cluster=enabled
mms.featureFlag.atlasChurnSurvey=enabled
mms.featureFlag.atlasHideQuickAccessPage=controlled
mms.featureFlag.atlasDeferServerNodeTypeTagUntilPhase2=enabled
mms.featureFlag.atlasDailyBigQueryBilling=enabled
mms.featureFlag.atlasHaltGroupLogIngestion=controlled
mms.featureFlag.atlasForceSkipFastProvision=controlled
mms.featureFlag.atlasAutoRetrieveDraftClusterConfig=enabled
mms.featureFlag.awsGraviton=enabled
mms.featureFlag.awsIntelOverGraviton=controlled
mms.featureFlag.gcpInstanceOSAL2=enabled
mms.featureFlag.singleTargetServerlessDeployment=controlled
mms.featureFlag.autoIndexing=controlled
mms.featureFlag.allowVulnerabilityScanning=disabled
mms.featureFlag.automation.changeListener=enabled
mms.featureFlag.automation.configPublishChangeEvent=enabled
mms.featureFlag.automation.configForAgentReturnsOnlyLocal=disabled
mms.featureFlag.automation.chefConfigChangeEvent=enabled
mms.featureFlag.automation.exposeEditorAutofill=disabled
mms.featureFlag.automation.mongoDevelopmentVersions=controlled
mms.featureFlag.automation.sentry=disabled
mms.featureFlag.automation.staggerMongosRollingRestart=controlled
mms.featureFlag.automation.newDeploymentServerUI=enabled
mms.featureFlag.automation.rollingIndexes=controlled
mms.featureFlag.automation.verifyDownloads=disabled
mms.featureFlag.automation.proxyConfigChangeEvent=enabled
mms.featureFlag.automation.enableV6=enabled
mms.featureFlag.automation.enableV7=enabled
mms.featureFlag.automation.enableV8=enabled
mms.featureFlag.automation=controlled
mms.featureFlag.automation.enableRamiAgent=disabled
mms.featureFlag.automation.enableMongotune=enabled
mms.featureFlag.mongotune.enableWriteBlockPolicy=enabled
nds.mongotune.writeBlockPolicy.enableForNewGroups=true
mms.featureFlag.payments.partners.aws.usageReportCreation=controlled
mms.featureFlag.payments.partners.azure.usageReportCreation=controlled
mms.featureFlag.payments.partners.gcp.usageReportCreation=controlled
mms.featureFlag.payments.salesSoldWarningOrgPaymentStatus=enabled
mms.featureFlag.payments.standaloneCalls.revRec=disabled
mms.featureFlag.awsPrivateEndpoint.waitingForUser=controlled
mms.featureFlag.baas=enabled
mms.featureFlag.baas.billingMigration=controlled
mms.featureFlag.baas.hostingDeprecated=enabled
mms.featureFlag.baas.graphqlDeprecated=enabled
mms.featureFlag.backup.allowEditOplogWindow=controlled
mms.featureFlag.backup.allowWhitelists=enabled
mms.featureFlag.backup.continuousBackupAllowedForNewAWSClusters=controlled
mms.featureFlag.backup.cpsResurrectWithRetainedBackups=enabled
mms.featureFlag.backup.cpsBackupLockMVP=controlled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGA=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGADisablePolicy=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicy2PersonDisablement=controlled
mms.featureFlag.backup.cpsConcurrentSnapshots=enabled
mms.featureFlag.backup.cpsDirectAttach=enabled
mms.featureFlag.backup.cpsDirectAttachOff=controlled
mms.featureFlag.backup.cpsEmbeddedConfig=enabled
mms.featureFlag.backup.cpsEmbeddedConfigUi=enabled
mms.featureFlag.backup.cpsExtendedSnapshotRetry=controlled
mms.featureFlag.backup.cpsExtraOpenBackupCursorTimeout=controlled
mms.featureFlag.backup.useNoWholeFileRsync=controlled
mms.featureFlag.backup.cpsNoWholeFileRsyncOff=controlled
mms.featureFlag.backup.cpsRestoreSearchIndex=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAws=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsMigration=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsNewCMK=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectGCP=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAzure=enabled
mms.featureFlag.backup.cpsOptimizedDaRestoreAws=controlled
mms.featureFlag.backup.cpsOptimizedDaRestoreAzure=controlled
mms.featureFlag.backup.cpsOptimizedDaRestoreGcp=controlled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAws=enabled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAzure=enabled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapGcp=enabled
mms.featureFlag.backup.cpsAwsDaRestoreWithIo2=controlled
mms.featureFlag.backup.cpsDownloadEncryptedSnapshot=enabled
mms.featureFlag.backup.cpsGcpAndAzureNewClustersOnlyCps=enabled
mms.featureFlag.backup.cpsPv2InstantRestoreOff=controlled
mms.featureFlag.backup.cpsPv2StreamingRestore=enabled
mms.featureFlag.backup.cpsSnapshotExportHighFrequency=controlled
mms.featureFlag.backup.cpsSnapshotExportUi=controlled
mms.featureFlag.backup.cpsSnapshotExportAzure=enabled
mms.featureFlag.backup.cpsSnapshotConsistentExport=controlled
mms.featureFlag.backup.cpsSnapshotConsistentExportSplitLargeCollections=enabled
mms.featureFlag.backup.cpsSnapshotDistributionAws=enabled
mms.featureFlag.backup.cpsSnapshotDistributionGcp=enabled
mms.featureFlag.backup.cpsSnapshotDistributionAzure=enabled
mms.featureFlag.backup.cpsSnapshotDistributionUi=enabled
mms.featureFlag.backup.cpsSnapshotDistributionLargeRegionSet=controlled
mms.featureFlag.backup.cpsGcpIncrementalCopySnapshots=disabled
mms.featureFlag.backup.cpsSnapshotAWSPrivateDownload=enabled
mms.featureFlag.backup.cpsSnapshotAzurePrivateDownload=enabled
mms.featureFlag.backup.turnCpsSnapshotAWSPrivateDownloadOff=controlled
mms.featureFlag.backup.turnCpsSnapshotAzurePrivateDownloadOff=controlled
mms.featureFlag.backup.cpsSkipSystemClusterDestroy=controlled
mms.featureFlag.backup.cpsSystemProjectsForExports=enabled
mms.featureFlag.backup.cpsSystemProjectsForDataLakeIngestionPipelineExports=controlled
mms.featureFlag.backup.cpsOplogInGcp=enabled
mms.featureFlag.backup.cpsOplogInGcpExtendMigration=disabled
mms.featureFlag.backup.cpsOplogMigration=enabled
mms.featureFlag.backup.cpsOplogInAzure=controlled
mms.featureFlag.backup.cpsSuccessiveUpgradeQuarterly=enabled
mms.featureFlag.backup.cpsLegacyBackupMigration=enabled
mms.featureFlag.backup.incrementalWtEnabled=controlled
mms.featureFlag.backup.incrementalWtFullSnapshotDayOfWeek=controlled
mms.featureFlag.backup.multipleWorkersPerFile=enabled
mms.featureFlag.backup.parallelRestores=controlled
mms.featureFlag.backup.directS3Restore=controlled
mms.featureFlag.backup.queryable=enabled
mms.featureFlag.backup.queryableFsCache=controlled
mms.featureFlag.backup.s3OplogStoreInOm=controlled
mms.featureFlag.backup.storageEngine=enabled
mms.featureFlag.backup.thirdPartyManaged=disabled
mms.featureFlag.backup.thirdPartyWithManagedOplog=disabled
mms.featureFlag.backup.v2=controlled
mms.featureFlag.backup.wt.queryable=enabled
mms.featureFlag.backup.wtBackpressure=controlled
mms.featureFlag.backup.wtConcurrentGrooms=enabled
mms.featureFlag.backup.wtConcurrentMongoBlockstoreGrooms=disabled
mms.featureFlag.backup.wtEncryptionAtRest=controlled
mms.featureFlag.backup.wtLocalKeyFile=disabled
mms.featureFlag.backup.wtLocalKeyFile.forOrg=controlled
mms.featureFlag.backup.wtNamespaceFiltering=enabled
mms.featureFlag.backup.enableAWSPrivateLinkOption=controlled
mms.featureFlag.backup.optimalSuccessiveUpgradeForRestoreEnabled=controlled
mms.featureFlag.backup.pGzipEnabled=enabled
mms.featureFlag.biConnector=controlled
mms.featureFlag.backup.snapshot.onDemand=disabled
mms.featureFlag.charts=enabled
mms.featureFlag.chartsActivationOptimization=enabled
mms.featureFlag.classicApiAccess=controlled
mms.featureFlag.classicCharts=controlled
mms.featureFlag.classicDashboard=controlled
mms.featureFlag.clientMetadataCollection=controlled
mms.featureFlag.clusterReferencesAutocorrect=enabled
mms.featureFlag.commentServiceEnabled=controlled
mms.featureFlag.crossOrgBilling=enabled
mms.featureFlag.customerFederation=enabled
mms.featureFlag.pagerduty.includeMaintenanceWindowStatus=controlled
mms.featureFlag.dataExplorer=controlled
mms.featureFlag.dataExplorerAggregation=controlled
mms.featureFlag.dataExplorerCrud=controlled
mms.featureFlag.dataExplorerMultiTenant=controlled
mms.featureFlag.extendMaxAllowedDiskSizes=disabled
mms.featureFlag.hostMappingsAutocorrect=controlled
mms.featureFlag.installAgentScript=disabled
mms.featureFlag.streamsAuditLogs=enabled
mms.featureFlag.streamsEnabled=enabled
mms.featureFlag.managedSlowMs=controlled
mms.featureFlag.m10ShardedClusters=controlled
mms.featureFlag.mongoDBAccessHistory=controlled
mms.featureFlag.monitoring.dataDogMetrics=controlled
mms.featureFlag.monitoring.dataDogDbAndCollMetrics=enabled
mms.featureFlag.monitoring.newRelic=enabled
mms.featureFlag.monitoring.shardedClusterNamespaceMetrics=enabled
mms.featureFlag.monitoring.namespaceQueryLatencyMetrics=controlled
mms.featureFlag.monitoring.skipUpdateClustersDuringLiveMigration=controlled
mms.featureFlag.monthlyUsageMetricsEmail=controlled
mms.featureFlag.ndsClusters=controlled
nds.serverless.mergeStrategy.cpuUtilizationThreshold=0.08
mms.featureFlag.billingDistributedLinkedOrgBilling=controlled
mms.featureFlag.billingClusterTaggingExport=controlled
mms.featureFlag.disablePremiumDatadog=controlled
mms.featureFlag.serverlessAutoIndexing=enabled
mms.featureFlag.enableFineGrainedAuth=disabled
mms.featureFlag.fineGrainedAuth.userGroups=disabled
mms.featureFlag.serviceAccountManagement=enabled
mms.featureFlag.enableScramSha256Auth=controlled
mms.featureFlag.maxAggregatedDiskChartsForAtlas=controlled
mms.featureFlag.opLatency=enabled
mms.featureFlag.opsManagerConfigForGlobalMonitoringAdmin=enabled
mms.featureFlag.paBugsAndSustainability=controlled
mms.featureFlag.paFeedback=controlled
mms.featureFlag.performanceAdvisor=controlled
mms.featureFlag.historicalReplicaState=controlled
mms.featureFlag.profilerNDS=controlled
mms.featureFlag.profilerv2=controlled
mms.featureFlag.profilerv3=controlled
mms.featureFlag.prometheus=enabled
mms.FeatureFlag.prometheus.allowCustomerRateLimitConfig=controlled
mms.featureFlag.realtime=controlled
mms.featureFlag.realmMetrics=enabled
mms.featureFlag.realmMetricsAlerts=enabled
mms.featureFlag.rsyncBasedHeadCreation=disabled
mms.featureFlag.schemaAdvisor=controlled
mms.featureFlag.segmentGroupSynchronizer=enabled
mms.featureFlag.selfServeConsultingUnits=enabled
mms.featureFlag.standardFreeTier=controlled
mms.featureFlag.teams=disabled
mms.featureFlag.unknown=controlled
mms.featureFlag.atlasContinuousDelivery=enabled
mms.featureFlag.realmEvents=disabled
mms.featureFlag.realm.syncBeta=enabled
mms.featureFlag.failedRollingIndexCleanup=enabled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToSingleShard=disabled
mms.featureFlag.atlasLiveMigrateShardedHorizons=controlled
mms.featureFlag.atlasLiveMigrateOlderVersionSupport=enabled
mms.featureFlag.atlasLiveMigrateOneNinePreviewSupport=disabled
mms.featureFlag.atlasLiveMigrateLiftOplogLagRestriction=disabled
mms.featureFlag.atlasLiveMigrateRunsOnlyOnAL2Hosts=enabled
mms.featureFlag.atlasMigrationHubMilestoneOne=controlled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToShardedCluster=enabled
mms.featureFlag.atlasLiveMigrateMongosyncPushBasedReplicasetToShardedCluster=enabled
mms.featureFlag.atlasPushLiveMigrationsMongosyncPrivateEndpoints=disabled
mms.featureFlag.atlasLiveMigrateMongosyncVariableShardCount=enabled
mms.featureFlag.atlasLiveMigrateMongosyncEmbeddedVerifier=enabled
mms.featureFlag.atlasLiveMigrateDestinationOplogValidationForVerifier=enabled
mms.featureFlag.optOutFromKinesis=controlled
mms.featureFlag.performanceAdvisorRecommendSearch=controlled
mms.featureFlag.uiAccessList=controlled
mms.featureFlag.tokenizedQueryShapeStatsMetrics=disabled
mms.featureFlag.atlasSearchQueryTelemetry=controlled
mms.featureFlag.randomSamplingSlowLogStreamer=enabled
mms.featureFlag.datadogCustomEndpoint=controlled
mms.featureFlag.resourceTagComponent.clusters=enabled
mms.featureFlag.resourceTagComponent.projects=enabled
mms.featureFlag.enableExperimentHoldouts=enabled
mms.featureFlag.iaSlowLogsReads=disabled
mms.featureFlag.allowGCPPreviewRegions=disabled
mms.featureFlag.allowAWSPreviewRegions=disabled
mms.featureFlag.allowAzurePreviewRegions=disabled
mms.featureFlag.atlasDedicatedBackboneToReact=enabled
mms.featureFlag.atlasDedicatedReactClusterDetails=controlled
mms.featureFlag.encryptionAtRestAzureKeyVaultPrivateEndpoint=enabled
mms.featureFlag.encryptionAtRestAwsKmsPrivateEndpoint=enabled
mms.featureFlag.earGcpKmsRoleBasedAuth=disabled
mms.featureFlag.atlasAllowAtlasAdminFourFourPrivileges=controlled
mms.featureFlag.connectionEstablishmentRateLimiting=enabled
mms.federation.restrictOrgMembership.default=false
mms.federation.bypassSsoDomains=
mms.enableOidcIdpNonAtlas=true
mms.featureFlag.privateLinkProxyProtocolAws=controlled
mms.featureFlag.privateLinkMaxIncomingConnectionsMultiplier=controlled
mms.fromEmailAddr=MongoDB Cloud <<EMAIL>>
mms.globalRoles.mapping=default
mms.group.tags.edit.roleRequired=GLOBAL_MONITORING_ADMIN
mms.group.tags.read.roleRequired=GLOBAL_READ_ONLY
mms.group.useSoftDelete=false
mms.groupType.default=CLOUD
mms.helpAndSupportPage.enabled=true
mms.http.bindhostname=127.0.0.1
mms.https.ClientCertificateMode=none
mms.https.dualConnectors=true
mms.invitationCron.batchSizeBytes=500000
mms.invitationCron.timeoutMs=50
mms.jetty.stopTimeoutMS=60000
mms.kubernetes.enabled=true
mms.kubernetes.templates.baseFilePathRequiredForCustomTemplates=/mongodb-ops-manager
mms.kubernetes.templates.defaultCredentialsFilePath=classpath://kubernetes-template-credentials.yaml.hbs
mms.kubernetes.templates.defaultProjectFilePath=classpath://kubernetes-template-project.yaml.hbs
mms.limits.maxAlertConfigsPerGroup=250
mms.limits.maxApiUsersPerOrg=500
mms.limits.maxGroupsPerOrg=250
mms.limits.maxGroupsPerUser=500
mms.limits.maxNotificationsPerAlert=25
mms.limits.maxOrgsPerUser=250
mms.limits.maxTeamsPerGroup=100
mms.limits.maxTeamsPerOrg=250
mms.limits.maxTeamsPerUser=100
mms.limits.maxUsersPerGroup=500
mms.limits.maxUsersPerOrg=500
mms.limits.maxUsersPerTeam=250
mms.limits.maxExportsPerGroup=100
mms.limits.maxM0sPerUnverifiedOrg=10
mms.limits.maxServiceAccountsPerOrg=500
mms.limits.maxCustomPoliciesPerOrg=100
mms.limit.maxNetworkAddressesPerServiceAccount=200
mms.limit.groupSearchMaxNumOfReturnAllowed=3000
mms.logcollection.s3Bucket.accessKey=<SECRETMANAGER>
mms.logcollection.s3Bucket.secretKey=<SECRETMANAGER>
mms.logCollectionJob.defaultExtensionDays=30
mms.login.newDeviceNotification.enabled=true
mms.login.ratelimit.attemptsAllowed=15
mms.login.ratelimit.lockedPeriodMinutes=5
mms.marketing.sync=false
maas.ingestion.usecase.336c2b2f-e495-5e7d-9a9c-9e6f492fa157.adminKey=<SECRETMANAGER>
maas.ingestion.usecase.91a39a8e-b296-5bd5-8cbe-cf54085f1bb1.adminKey=<SECRETMANAGER>
mms.monitoring.abbrvslowlogs.intervalStrategyHours=8
mms.monitoring.abbrvslowlogs.schemaAdvisorFieldsRolloutPercentage=100
mms.monitoring.agent.session.timeoutMillis=120000
mms.monitoring.agentlog.writers=1
mms.automation.agent.config.atlasClusterNameRollout=0
mms.featureFlag.monitoring.canonicalHostsTTLFilteredReads.enabled=enabled
mms.featureFlag.monitoring.canonicalHostsTTLWrites.enabled=enabled
mms.monitoring.canonicalHostsTTLMinutes=1440
mms.monitoring.canonicalHostsTTLWrites.probabilisticPercentage=25
mms.monitoring.canonicalHostsTTLWrites.guaranteedPercentage=50
mms.monitoring.chartDomain=https://chart-@<EMAIL>
mms.monitoring.historicalReplicaState.ingestionPercentage=100
mms.monitoring.ingestion.bucketedMetricRollup.enabled=true
mms.monitoring.ingestion.bucketedMetricRollup.bucketCount=2
mms.monitoring.ingestion.bucketedMetricRollup.serverlessBucketCount=2
mms.monitoring.ingestion.bucketedMetricRollup.realmBucketCount=2
mms.monitoring.ingestion.munin.poolsize=2
mms.monitoring.ingestion.newrelic.guid=com.mongodb.monitoring.integrations.newrelic.cloudmanager.production
mms.monitoring.ingestion.newrelic.version=1.0.0
mms.monitoring.ingestion.readPreference=secondaryPreferred
mms.monitoring.ingestion.rollup.destinationMatchingEnabled=true
mms.monitoring.performanceadvisor.indexcollection.rerunIntervalMinutes=60
mms.monitoring.performanceadvisor.indexcollection.scheduleIntervalMinutes=360
mms.monitoring.performanceadvisor.iterative.maxLogLines=200000
mms.monitoring.performanceadvisor.textQueryThreshold=300
mms.monitoring.queryStats.exporter.usecaseId=82653516-67ba-5ffa-be02-5e4dd0376682
mms.monitoring.queryStats.exporter.publisherKey=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
mms.monitoring.queryStats.agent.scrapeRolloutPercentage=100
mms.monitoring.queryStats.ingestionRolloutPercentage=100
mms.monitoring.realm.enabled=true
mms.monitoring.realm.metrics.api.url=https://realm-staging.mongodb.com/api/private/v1.0/app_metrics
mms.monitoring.rrd.collStatsLatencyMaintenanceEnabled=false
mms.monitoring.rrd.collStatsLatencyNumInitialChunks=10
mms.monitoring.rrd.rrdIntervalsStrategy=DEFAULT
mms.monitoring.rrd.realmMaintenanceEnabled=false
mms.monitoring.rrd.realmNumInitialChunks=10
mms.monitoring.rrd.realm.thread.count=6
mms.monitoring.rrd.ftsMaintenanceEnabled=false
mms.monitoring.rrd.ftsNumInitialChunks=10
mms.monitoring.rrd.serverlessMaintenanceEnabled=false
mms.monitoring.rrd.serverlessNumInitialChunks=60
mms.monitoring.slowlogs.ingestion.parsingPercentage=0
mms.monitoring.serverlessProfilingFilter.enabled=true
mms.mana.http.url=http://mana-http.mana-qa.us-east-1.aws.cloud-qa.kube:5000/alohomora
mms.mana.isUsingInternalMeshNetwork=true
mms.mana.clientId=<SECRETMANAGER>
mms.mana.clientSecret=<SECRETMANAGER>
mms.mana.authToken=<SECRETMANAGER>
mms.monthlyUsageMetricsEmail.useMinuteBucket=false
mms.multiFactorAuth.allowReset=true
mms.multiFactorAuth.issuer=MongoDB-STAGE
mms.multiFactorAuth.level=OPTIONAL
mms.multiFactorAuth.encouragement.enabled=false
mms.multiFactorAuth.encouragement.enabledForNoFactors=true
iam.multiFactorAuth.requiredAfterRegistration=false
iam.multiFactorAuth.requiredForAllUsers=true
iam.multiFactorAuth.rememberDevice.enabled=true
mms.personalizationWizardEnabled=true
mms.personalizationWizardRedirectEnabled=true
mms.darkMode.preview.enabled=true
mms.darkMode.default=LIGHT
mms.ofac.supportEmailAddr=<EMAIL>
mms.plan.default=FREE_TIER
mms.publicApi.globalKeyRoleDenylist=GLOBAL_OWNER
mms.publicApi.maxTempKeys=100
mms.pushLiveMigrations.enabled=true
mms.pushLiveMigrations.mongoClient.validations.enabled=true
mms.pushLiveMigrations.fetchJob.intervalSeconds=60
mms.pushLiveMigrations.mmsUi.defaultAccessListCidr=10.0.0.0/8
mms.pushLiveMigrations.mmsUi.serviceMeshEnvVar=KUBE_MMS_UI_KUBE_STAGING_US_EAST_1_AWS_CLOUD_10GEN_CC
mms.pushLiveMigrations.syncJobs.enabled=true
mms.pushLiveMigrations.syncJobs.cm.failedAttempts.max=1440
mms.pushLiveMigrations.updateJob.intervalSeconds=60
mms.pushLiveMigrations.updateJob.cooldownSeconds=10
mms.remoteIp.header=X-Cluster-Client-Ip
mms.replyToEmailAddr=<EMAIL>
mms.replyToSuspensionAdminEmailAddr=<EMAIL>
mms.root.redirect=/user/login
mms.search.envoy.location=https://mongodb-mms-build-envoy-serverless.s3.amazonaws.com/tarballs/
mms.search.xds.host=search-xds-staging.mongodb.com
mms.search.gateway.host=search-gateway-staging.mongodb.com
mms.search.gateway.centralUrl=https://search-gateway-staging.mongodb.com
mms.search.offlineUpgradePackages=1.38.1.2120
mms.search.offlineDowngradePackages=1.41.0
mms.search.commonLabelsEnabledTiers=M10
mms.search.staleEnabledTiers=MTM
mms.search.incubatorVectorEnabledTiers=ALL
mms.search.apiRateLimitRules=default:2
mms.search.tokenFacetingCardinalityLimitShared=10000000
mms.search.tokenFacetingCardinalityLimitDedicated=0
mms.search.excludedChangeStreamFields=updateDescription.disambiguatedPaths
mms.search.maas.metricsIngestionUrl=http://localhost:4318
mms.search.maas.useCaseUuid=49642286-8df9-5c7e-807b-40ad3e39fafd
mms.search.maas.pushMetricsIntervalSec=60
mms.security.allowCORS=true
mms.security.backdoor=true
mms.security.cors.allow.localhost.origin=false
mms.security.disableBrowserCaching=false
mms.security.globalWhitelist=seed-data/GlobalWhitelistDao-stage.json
mms.security.hstsMaxAgeSeconds=31536000
mms.security.requireCSRFToken=true
mms.server.sentry.dsn=<SECRETMANAGER>
mms.server.sentry.enabled=true
mms.server.sentry.sampleRate=0.1
mms.server.sentry.apiKey=<SECRETMANAGER>
mms.session.authn.tokens.filter.enabled=true
mms.session.authn.tokens.minting.enabled=true
mms.session.maxConcurrent=10
mms.session.maxHours=12
mms.session.customSessionTimeouts.enabled=false
mms.siteFullName.nds=MongoDB Atlas
mms.siteName.nds=Atlas
mms.snapshotQueryHost=queryable-backup-stage.mongodb.com:27117
mms.staleAuthCodeLimit=5
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.maxHitsPerPeriod=50
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.minutesPeriod=1
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.maxHitsPerPeriod=20
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.minutesPeriod=1
mms.testUtil.enabled=true
mms.ui.mongonav.env=commercial
mms.usageData.collection=false
mms.usageData.ingestion=false
mms.usageData.pgp.publicKey=/mongodb-inc-opsmanager.pub.asc
mms.usageData.pgp.secretKey=/mongodb-inc-opsmanager.private.asc
mms.usageData.s3.bucket=mongodb-datawarehouse-qa
mms.user.bypassInviteForExistingUsers=false
mms.user.invitationOnly=false
mms.user.redirectIfPasswordIsEmpty=false
mms.user.registration.extraFields=company
mms.user.username.restrictedDomains=legacyusername[.]mongodb[.]com,invalidusername[.]mongodb[.]com
mms.userSvcClass=UserSvcOkta
mms.vercel.clientId=
mms.vercelIntegration.enabled=false
mms.vercel.secret=
mms.versionMismatch.detect=false
mms.versionMismatch.pingInterval.unit=SECONDS
mms.versionMismatch.pingInterval=10
mms.versionMismatch.pingInterval.staleCount=60
mms.versionToDeprecate=6.0
mms.forcedDeprecatedVersionUpgradeTargets=7.0,8.0
mms.versionDeprecatedByDate=2025-07-31
mms.enableFutureEOLExtensionCheck=true
# V1 Degraded Experience Configuration
mms.v1.degradedExperience.phase1.enabled=false
mms.v1.degradedExperience.phase2.enabled=false
mms.v1.degradedExperience.shadowMode.enabled=true
mms.v1.degradedExperience.creationCutoffDate.org=2025-09-08T00:00:00.000Z
mms.v1.degradedExperience.creationCutoffDate.project=2025-09-08T00:00:00.000Z
mms.azure.ssdv2.regions=francecentral,eastasia,canadacentral,koreacentral,norwayeast,polandcentral,southafricanorth,swedencentral,switzerlandnorth,uaenorth,australiaeast,northeurope,centralindia,japaneast,uksouth,brazilsouth,southeastasia,germanywestcentral,southcentralus,westus3,centralus,eastus2,westeurope,eastus,westus2,italynorth,israelcentral,spaincentral,mexicocentral
mms.azure.ssdv2.rollout.regions=
mms.azure.ssdv2.preview.regions=eastus2euap
mmsdbautomationlog.queuesize=10000
mmsdbautomationlog.writers=4
mmsdbserverlog.logs.enabled=false
mongo.account.encryptedCredentials=true
mongo.account.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&uuidRepresentation=standard&localThresholdMS=8
mongo.account.ssl=true
mongo.chartsmetadata.encryptedCredentials=true
mongo.chartsmetadata.mongoUri=mongodb+srv://af29572739b3a4d2900a14c0f64aed7d-b5542aca67752ef796df7ffffc5d5627a498a7f67e0f0e33:<EMAIL>/test?retryWrites=true&retryReads=false&uuidRepresentation=standard
mongo.chartsmetadata.ssl=false
mongo.monitoringagentlogs.encryptedCredentials=
mongo.monitoringagentlogs.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.monitoringagentlogs.ssl=
mongo.atlasbackup.encryptedCredentials=
mongo.atlasbackup.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.atlasbackup.ssl=
mongo.automationcore.encryptedCredentials=
mongo.automationcore.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.automationcore.ssl=
mongo.automationstatus.encryptedCredentials=
mongo.automationstatus.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.automationstatus.ssl=
mongo.authz.encryptedCredentials=true
mongo.authz.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=200&compressors=snappy&uuidRepresentation=standard&localThresholdMS=8
mongo.authz.ssl=true
mongo.backupdb.encryptedCredentials=true
mongo.backupdb.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=50&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.backupdb.ssl=true
mongo.backupagentlogs/backuplogs.encryptedCredentials=true
mongo.backupagentlogs/backuplogs.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=50&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.backupagentlogs/backuplogs.ssl=true
mongo.backupstatus.encryptedCredentials=true
mongo.backupstatus.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.backupstatus.ssl=true
mongo.backupusage.encryptedCredentials=true
mongo.backupusage.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.backupusage.ssl=true
mongo.cloudconf.encryptedCredentials=
#temp collection will be renamed CLOUDP-241263
mongo.cloudconf.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=10&compressors=snappy&retryWrites=false&retryReads=true&uuidRepresentation=standard
mongo.cloudconf.ssl=
mongo.event-service.encryptedCredentials=true
mongo.event-service.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.event-service.ssl=true
mongo.encryptedCredentials=true
mongo.mmsdbevents.encryptedCredentials=
mongo.mmsdbevents.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbevents.ssl=
mongo.mmsdbalerts/mmsdbclusterdescriptions.encryptedCredentials=
mongo.mmsdbalerts/mmsdbclusterdescriptions.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbalerts/mmsdbclusterdescriptions.ssl=
mongo.automationagentlogs.encryptedCredentials=
mongo.automationagentlogs.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.automationagentlogs.ssl=
mongo.mmsdbautomation.encryptedCredentials=
mongo.mmsdbautomation.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbautomation.ssl=
mongo.mmsdbqueues.encryptedCredentials=
mongo.mmsdbqueues.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbqueues.ssl=
mongo.mmsdbbilling.encryptedCredentials=
mongo.mmsdbbilling.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=true&w=majority&retryReads=true&uuidRepresentation=standard
mongo.mmsdbbilling.ssl=
mongo.mmsdbcloudproviders.encryptedCredentials=
mongo.mmsdbcloudproviders.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=true&retryReads=true&uuidRepresentation=standard&waitQueueTimeoutMS=120000
mongo.mmsdbcloudproviders.ssl=
mongo.mmsdbcollstatslatencyconfig.encryptedCredentials=
mongo.mmsdbcollstatslatencyconfig.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbcollstatslatencyconfig.ssl=
mongo.controlledfeatures.encryptedCredentials=
mongo.controlledfeatures.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&uuidRepresentation=standard&localThresholdMS=8
mongo.controlledfeatures.ssl=
mongo.tokenization.encryptedCredentials=
mongo.tokenization.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.tokenization.ssl=
mongo.discovery.encryptedCredentials=
mongo.discovery.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&uuidRepresentation=standard&localThresholdMS=8
mongo.discovery.ssl=
mongo.realmmetricsstate.encryptedCredentials=
mongo.realmmetricsstate.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.realmmetricsstate.ssl=
mongo.serverlesschartselect.encryptedCredentials=
mongo.serverlesschartselect.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.serverlesschartselect.ssl=
mongo.metricsmisc.encryptedCredentials=
mongo.metricsmisc.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.metricsmisc.ssl=
mongo.metricsmisc2.encryptedCredentials=
mongo.metricsmisc2.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.metricsmisc2.ssl=
mongo.iam.encryptedCredentials=
mongo.iam.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&uuidRepresentation=standard&localThresholdMS=8
mongo.iam.ssl=
mongo.mmsalertconfig.encryptedCredentials=
mongo.mmsalertconfig.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsalertconfig.ssl=
mongo.mmsdbemails.encryptedCredentials=
mongo.mmsdbemails.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbemails.ssl=
mongo.mmsdbjobs.encryptedCredentials=
mongo.mmsdbjobs.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&w=majority
mongo.mmsdbjobs.ssl=
mongo.dataexplorer.encryptedCredentials=
mongo.dataexplorer.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.dataexplorer.ssl=
mongo.logcollection.encryptedCredentials=
mongo.logcollection.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.logcollection.ssl=
mongo.realtimemeasurements.encryptedCredentials=
mongo.realtimemeasurements.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.realtimemeasurements.ssl=
mongo.iadbpings.encryptedCredentials=
mongo.iadbpings.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.iadbpings.ssl=
mongo.mmsdbrrdcache.encryptedCredentials=
mongo.mmsdbrrdcache.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbrrdcache.ssl=
mongo.dbprofile.encryptedCredentials=
mongo.dbprofile.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.dbprofile.ssl=
mongo.atlaslogsmigration.encryptedCredentials=
mongo.atlaslogsmigration.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.atlaslogsmigration.ssl=
mongo.mmsdbsharedtier/atlaslogsnds.encryptedCredentials=
mongo.mmsdbsharedtier/atlaslogsnds.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbsharedtier/atlaslogsnds.ssl=
mongo.atlaslogsoa.encryptedCredentials=
mongo.atlaslogsoa.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.atlaslogsoa.ssl=
mongo.mmsdbcollstatslatencyrrd.encryptedCredentials=
mongo.mmsdbcollstatslatencyrrd.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbcollstatslatencyrrd.ssl=
mongo.mmsdbrealmrrd4ia.encryptedCredentials=
mongo.mmsdbrealmrrd4ia.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbrealmrrd4ia.ssl=
mongo.mmsdbftsrrd.encryptedCredentials=
mongo.mmsdbftsrrd.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbftsrrd.ssl=
mongo.mmsdbrrd4ia.encryptedCredentials=
mongo.mmsdbrrd4ia.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbrrd4ia.ssl=
mongo.mmsdbrrd4dbia.encryptedCredentials=
mongo.mmsdbrrd4dbia.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbrrd4dbia.ssl=
mongo.metricsstats.encryptedCredentials=
mongo.metricsstats.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.metricsstats.ssl=
mongo.realmdiscovery.encryptedCredentials=
mongo.realmdiscovery.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.realmdiscovery.ssl=
mongo.mmsdbserverlessrrd4ia.encryptedCredentials=
mongo.mmsdbserverlessrrd4ia.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdbserverlessrrd4ia.ssl=
mongo.ratelimits.encryptedCredentials=
mongo.ratelimits.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.ratelimits.ssl=
mongo.mmsrt.encryptedCredentials=true
mongo.mmsrt.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&retryWrites=false&retryReads=false&connectTimeoutMS=5000&socketTimeoutMS=30000&waitQueueTimeoutMS=100&waitQueueMultiple=1&serverSelectionTimeoutMS=2000&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsrt.ssl=true
mongo.monitoringdiagnostics.encryptedCredentials=
mongo.monitoringdiagnostics.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.monitoringdiagnostics.ssl=
mongo.iaaccesslogs.encryptedCredentials=
mongo.iaaccesslogs.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.iaaccesslogs.ssl=
mongo.monitoringftstelemetry.encryptedCredentials=
mongo.monitoringftstelemetry.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.monitoringftstelemetry.ssl=
mongo.monitoringstatus.encryptedCredentials=
mongo.monitoringstatus.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.monitoringstatus.ssl=
mongo.sessions.encryptedCredentials=
mongo.sessions.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.sessions.ssl=
mongo.nds.encryptedCredentials=
mongo.nds.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=true&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.nds.ssl=
mongo.atlasndsstatus.encryptedCredentials=
mongo.atlasndsstatus.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.atlasndsstatus.ssl=
mongo.fts.encryptedCredentials=
mongo.fts.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.fts.ssl=
mongo.platformdiagnostics.encryptedCredentials=
mongo.platformdiagnostics.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.platformdiagnostics.ssl=
mongo.ssl=true
mongo.oa.encryptedCredentials=
mongo.oa.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.oa.ssl=
mongo.adl.encryptedCredentials=
mongo.adl.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.adl.ssl=
mongo.atlasbilling.encryptedCredentials=
mongo.atlasbilling.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.atlasbilling.ssl=
mongo.mmsdblogtooling.encryptedCredentials=
mongo.mmsdblogtooling.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=true&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.mmsdblogtooling.ssl=
mongo.iaquerystats.encryptedCredentials=
mongo.iaquerystats.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard&localThresholdMS=8
mongo.iaquerystats.ssl=
mongo.abbrvslowlogs.encryptedCredentials=
mongo.abbrvslowlogs.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.abbrvslowlogs.ssl=
mongo.ccpa.encryptedCredentials=
mongo.ccpa.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=false&retryReads=true&uuidRepresentation=standard&localThresholdMS=8
mongo.ccpa.ssl=
mongo.iaslowlogs.encryptedCredentials=
mongo.iaslowlogs.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/maxPoolSize=30&connectTimeoutMS=15000&socketTimeoutMS=30000&waitQueueTimeoutMS=15000&retryWrites=false&uuidRepresentation=standard&compressors=snappy&localThresholdMS=8
mongo.iaslowlogs.ssl=
mongo.integrations.encryptedCredentials=
mongo.integrations.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?retryWrites=true&w=majority&appName=ia-staging-integrations&localThresholdMS=8
mongo.integrations.ssl=
mongo.ui.encryptedCredentials=true
mongo.ui.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?retryWrites=true&w=majority&appName=ia-staging-ui&localThresholdMS=8
mongo.ui.ssl=true
mongodb.disable.numa=true
mongodb.release.autoDownload.enterprise=true
mongodb.release.autoDownload.rc=true
mongodb.release.autoDownload=true
mongodb.release.modulePreference=communityRequired
mongodb.release.publicKeyFile=classpath://mongodb-inc-server.pub
mongodb.ssl.CAFile=/etc/ssl/certs/ca-bundle.crt
mongot.location=https://internal-downloads.mongodb.com/search/
mongotools.version=100.13.0
# `/prod/*` is the release bucket for mongotune binaries
mongotune.location=https://internal-downloads.mongodb.com/mongotune/prod/
mongotune.version=1.0.2
mongotune.previous.version=1.0.0
mongotune.loglevel=DEBUG
nds.mongotune.minAgentVersion=13.35.2.9529-1
nds.mongotune.plannerCheckConfigUpdate=true
nds.mongotune.maasDataSource=eeqn076ojuvwgd
mongosh.version=2.5.6
monitoring.agent.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
monitoring.agent.location.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
monitoring.agent.minimumVersion=5.0.0.309
monitoring.agent.version=7.2.0.488-1
morphia.search.packages=com.xgen.cloud.common.featureFlag._public.model,com.xgen.svc.mms.model.metrics.realtime,com.xgen.svc.mms.deployment.auth,com.xgen.svc.mms.model.performanceadvisor,com.xgen.cloud.activity._public.model.alert,com.xgen.cloud.activity._public.model.alert.config,com.xgen.svc.mms.model,com.xgen.svc.atm.model,com.xgen.svc.mms.model.performanceadvisor.autoindexing,com.xgen.cloud.atm.core._public.model,com.xgen.cloud.deployment._public.model,com.xgen.cloud.monitoring.topology._public.model,com.xgen.cloud.organization._public.model,com.xgen.cloud.atm.core._public.model.status,com.xgen.cloud.billingplatform.activity._public.alert,com.xgen.cloud.billingplatform.activity._public.alert.config,com.xgen.cloud.billingplatform.activity._public.audit,com.xgen.cloud.billingplatform.activity._public.event,com.xgen.cloud.billingplatform.audit._public.model,com.xgen.cloud.billingplatform.invoice._public.model,com.xgen.cloud.billingplatform.model.cloudprovider._public.aws,com.xgen.cloud.billingplatform.model.cloudprovider._public.azure,com.xgen.cloud.billingplatform.model.plan._public.model,com.xgen.cloud.dataexport._public.model,com.xgen.cloud.partners.registration._public.model,com.xgen.cloud.payments.netsuite._public.models,com.xgen.cloud.revrec.selfserve._private.model,com.xgen.svc.mms.model.billing,com.xgen.svc.mms.model.billing.reporting,com.xgen.svc.mms.model.marketing,com.xgen.cloud.common.explorer._public.model
nds.mtmCompaction.enabled=true
nds.mtm.maxReservedTenants.m0=250
nds.mtm.maxReservedTenants.m2=0
nds.mtm.maxReservedTenants.m5=0
nds.mtm.maxReservedTenants.flex=50
nds.acme.accountDocument=
nds.acme.le.accountDocument=
nds.acme.gts.accountDocument=
nds.atlasproxy.conf.maxProcessedAccounts=100
nds.atlasproxy.conf.tlsPEMKeyFile=/etc/pki/tls/private/proxy.pem
nds.atlasproxy.restartProxyProcessOnFallbackCertRotation=false
nds.admin.reasonsRequireJira=false
nds.admin.dataPlaneAccessRequestsOnlineValidation=false
nds.admin.createRespectProtectedHoursMaintenanceAdminUi.enabled=true
nds.agent.envoyConfigServer.enabled=true
nds.agent.envoyConfigServer.nodeId=atlas-envoy
nds.agent.envoyConfigServer.port=9902
nds.agent.envoyConfigServer.adminPort=9901
nds.agent.envoyConfigServer.promExternalPort=27018
nds.agent.envoyConfigServer.promInternalPort=9945
nds.agent.envoyConfigServer.gatewayProxyExternalPort=27014
nds.agent.envoyConfigServer.mongotPromPort=9946
nds.agent.envoyConfigServer.searchEnvoyAdminPort=9905
nds.agent.envoyConfigServer.tlsCertConfigPath=/etc/envoy/sds.yaml
nds.agent.envoyConfigServer.tlsFilterServerName=*.mongodb-dev.net
nds.agentApiKey.allowUnrestricted=false
nds.autoScaling.burstable.compute.newClusterEnabled=true
nds.autoScaling.predictive.cron.enabled=true
nds.autoscaling.predictive.autoscaler.compute.enabled=true
nds.aws.al2023.newGroups=false
nds.aws.billingReport.bucketName=mongodb-atlas-billing
nds.aws.billingReport.name=daily-report
nds.aws.billingReport.prefix=custom-reports
nds.aws.billingReport.regionName=US_EAST_1
nds.aws.dataValidation.instanceSize=M10
nds.aws.dataValidation.min.diskSizeGB=1024
nds.aws.dns.domain=mongodb-stage.net
nds.aws.dns.hostedzoneid=ZJFVW46B2U780
nds.aws.dns.datalake.hostedzoneid=Z01305531SWH7W1BOHGSE
nds.azure.al2023.newGroups=false
nds.azure.dataValidation.instanceSize=M10
nds.azure.dataValidation.min.diskSizeGB=1024
nds.azure.dns.datalake.hostedzoneid=Z0894525DQS9BYWXDCOC
nds.azure.dns.domain=azure.mongodb-stage.net
nds.azure.dns.hostedzoneid.private=Z3NKBHP1MP8PED
nds.azure.dns.hostedzoneid.public=Z1OD138PSAKAXH
nds.azure.oplogStore.unsupportedRegions=eastus2euap,brazilsoutheast,southafricawest,australiacentral2,francesouth,norwaywest,qatarcentral,swedensouth,switzerlandwest,uaecentral
nds.backup.snapshots.expiration_extend_minutes=60
nds.backup.snapshots.restore.minimum_time_needed_for_restore_minutes=30
nds.backup.snapshots.aws.copySnapshotKmsKeyArnTemplate=arn:aws:kms:%s:124616370075:alias/cps-snapshot-copy
nds.backup.snapshots.aws.fasterRestoreJobKmsKeyArnTemplate=arn:aws:kms:%s:%s:alias/cloud-stage-volume-encryption-key
nds.criticalMaintenance.job.enabled=true
nds.corruptionDetection.dataValidation.dailySamplePercent=0.0
nds.corruptionDetection.dataValidation.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dataValidation.minClusterAgeDays=182
nds.corruptionDetection.dbCheck.dailySamplePercent=0.0
nds.corruptionDetection.dbCheck.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dbCheck.minClusterAgeDays=182
nds.corruptionDetection.checkMetadataConsistency.dailySamplePercent=0.0
nds.corruptionDetection.checkMetadataConsistency.minDaysBetweenClusterValidations=182
nds.corruptionDetection.checkMetadataConsistency.minClusterAgeDays=182
nds.connectionEstablishmentRateLimiting.newGroups=false
nds.dataValidation.maxWorkingRecords=10
nds.adminEmailAddress=<EMAIL>
nds.externalcaching.redis.cacheEnabled=true
nds.externalcaching.redis.sslEnabled=true
nds.externalcaching.redis.primary.host=master.mms-nds-external-cache-staging.m2hzsp.use1.cache.amazonaws.com
nds.externalcaching.redis.primary.port=6379
nds.externalcaching.redis.replica.host=replica.mms-nds-external-cache-staging.m2hzsp.use1.cache.amazonaws.com
nds.externalcaching.redis.replica.port=6379
nds.externalcaching.redis.username=<SECRETMANAGER>
nds.externalcaching.redis.password=<SECRETMANAGER>
nds.externalcaching.redis.domainAuthRedisEnabled=true
nds.gateway.proxy.enabled=true
nds.gcp.SnapshotBillingReport.dataset=mongodb_net_billing
nds.gcp.SnapshotBillingReport.projectId=cloud-prod-root
nds.gcp.dailyBilling.auditDays=9
nds.gcp.billingReport.bucketName=mongodb-net-billing-reports
nds.gcp.billingReport.prefix=mongodb-net-billing-
nds.gcp.dataValidation.instanceSize=M10
nds.gcp.dataValidation.min.diskSizeGB=1024
nds.gcp.dns.domain=gcp.mongodb-stage.net
nds.gcp.dns.hostedzoneid.crosscloud.private=Z00922401DONQ23XBNW0O
nds.gcp.dns.hostedzoneid.private=Z4LPJP9MGSPOZ
nds.gcp.dns.hostedzoneid.public=Z1J75ZMKJ4DI5O
nds.gcp.projectCreate.count=250
nds.gcp.al2023.newGroups=false
nds.atlasPrioritizeHavingPrimaryOnUpscaledNode.newGroups=true
nds.privateLinkProxyProtocolAws.newGroups=true
nds.gov.us.enabled=false
nds.healthCheck.staleJobThreshold.hours=1
nds.images.s3Bucket.aws.accesskey=<SECRETMANAGER>
nds.images.s3Bucket.aws.secretkey=<SECRETMANAGER>
nds.images.gcp.projectId=atlas-images-prod
nds.instances.heracles.s3KeyId=<SECRETMANAGER>
nds.instances.heracles.s3SecretKey=<SECRETMANAGER>
nds.instances.heracles.s3Bucket=<SECRETMANAGER>
nds.instances.heracles.s3Region=<SECRETMANAGER>
nds.instances.secret-names=aws-creds,aws,log-uploader-keys,repo-gateway,s3logwriter-keys,firehoselogwriter-keys,wildcard-cert-pem,tenable,clamav-db,deviceSyncDebugAccessSharedSecret
nds.instances.secrets.CN.wildcard-cert-pem=BLOCKED
nds.instances.secrets.CN.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.aws-creds=<SECRETMANAGER>
nds.instances.secrets.aws.aws=<SECRETMANAGER>
nds.instances.secrets.aws.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.aws.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.aws.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.firehoselogwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.azure.aws-creds=<SECRETMANAGER>
nds.instances.secrets.azure.aws=<SECRETMANAGER>
nds.instances.secrets.azure.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.azure.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.azure.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.azure.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.gcp.aws-creds=<SECRETMANAGER>
nds.instances.secrets.gcp.aws=<SECRETMANAGER>
nds.instances.secrets.gcp.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.gcp.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.clamav-db=
nds.instances.secrets.tenable=
nds.instances.secrets.deviceSyncDebugAccessSharedSecret=<SECRETMANAGER>
nds.instances.shared.version=8.0
nds.instances.serverless.size=SERVERLESS_V2
nds.instances.os.aws=AL2
nds.instances.os.azure=AL2
nds.instances.os.gcp=AL2
nds.serverlessProxy.os.aws=AL2
nds.serverlessProxy.os.azure=AL2
nds.serverlessProxy.os.gcp=AL2
nds.streamsProxy.os.aws=AL2
nds.streamsProxy.os.azure=AL2
mms.featureFlag.streamsAWSProxyAL2023Enabled=controlled
mms.featureFlag.streamsAzureProxyAL2023Enabled=controlled
nds.instances.provisioningLogUpload.enabled=true
nds.instances.provisioningLogUpload.bucket=atlas-provisioning-logs-us-east-1-stage
nds.instances.anomaliesBucket=atlas-anomalies-logs-us-east-1-stage
nds.jira.privateAccessToken=<SECRETMANAGER>
nds.jira.bypassJiraStagingFirewallHeader=<SECRETMANAGER>
nds.jira.bypassJiraStagingFirewallValue=<SECRETMANAGER>
nds.leakedItem.cleanup.retries=3
nds.leakedItem.cleanup.mode=JOB_HANDLER
nds.leakedItem.detection.aws.mode=JOB_HANDLER
nds.leakedItem.detection.azure.mode=JOB_HANDLER
nds.leakedItem.detection.gcp.mode=JOB_HANDLER
nds.leakedItem.detection.gcp.chunkSize=50
nds.leakedItem.detection.gcp.projectsProcessedPerIteration=10
nds.leakedItem.aws.shutdownLeakedInstance=true
nds.leakedItem.aws.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.leakedItem.gcp.shutdownLeakedInstance=true
nds.leakedItem.gcp.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.leakedItem.azure.shutdownLeakedInstance=true
nds.leakedItem.azure.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.liveImport.servers.hasPublicIp=false
nds.liveImport.publicIpOnly=true
nds.liveImport.mongosync.version=1.15.0
nds.liveImport.mongosync.latestVersion=1.15.0
nds.pushLiveImport.mongosync.version=1.13.1
nds.liveImport.mongomirror.version=0.13.1
nds.liveImport.mongomirror.dockerImageTag=v0.13.1-202411011952-80adf99
nds.liveImport.kubeResourceCleaner.enabled=true
nds.liveImport.mongosyncWorkingDir=/var/log/atlas-live-import-stage
nds.liveImport.mongosync.binaryPathPrefix=/opt/10gen/mongosync
nds.liveImport.mongosync.numInsertersPerPartition=4
nds.liveImport.mongosync.maxNumParallelPartitions=4
nds.liveImport.mongomirror.oplogBatchSize=1000
nds.liveImport.sudoUser=atlas-import-stage
nds.mongosync.log.verbosity=DEBUG
nds.metrics.deliverySystemForBilling.enabled=true
nds.mongomirror.log.verbosity=2
nds.okta.oidc.testAuthorizationServer.apiKey=
nds.orphaned.ip.keepHours=1
nds.planner.group.interval.short=86400000
nds.planner.group.interval.medium=172800000
nds.planner.group.interval.long=604800000
nds.planning.jobQueue.maxThreadCount=10
nds.planState.inMemoryObject.rolloutPercentage=100
nds.planState.inMemoryObject.reads.enabled=true
nds.restore.maxConcurrentExports=5
nds.root.cert=ISRGROOTX1
nds.fastFlex.maxRecordsPerRegion=2
nds.fastFlexProvisioning.enabled=true
nds.flex.feature.enabled=true
nds.flex.migration.warning.enabled=true
nds.flex.serverless.migration.cron.enabled=true
nds.flex.shared.migration.cron.enabled=true
nds.flex.migration.cron.schedule=0 * 13-20 ? * 2-6
nds.flex.mtm.autoscale.capacity.enabled=true
nds.flex.mtm.organization.id=599deac39f78f703d855c218
nds.mtm.lowThreshold.flex=50
nds.instances.flex.version=8.0
nds.serverless.feature.enabled=true
nds.serverless.mtm.consumption.debug.logging.enabled=false
nds.serverless.trace.autoscale.enabled=false
nds.serverless.trace.autoscale.exporter=
nds.serverless.trace.autoscale.prefix=com.xgen.nds.serverless.autoscale
nds.serverless.trace.autoscale.processor=
nds.serverless.supportsLBDeploymentIdOnEnvoyInstance=true
nds.serverless.privateNetworking.supportedCloudProviders=AWS,AZURE
nds.serverless.metrics.useMaxIops=true
nds.serverless.version.upgrade.window.enabled=
nds.serverless.version.upgrade.window.size=
nds.serverless.envoy.healthcheck.max.thread.count=5
nds.serverless.group.limits.maxResidentMTMs=85
nds.serverless.load.deadlineSeconds=900
nds.serverless.load.maxThreads=20
nds.serverless.load.minThreads=5
nds.serverless.metrics.mocks.enabled=false
nds.serverless.metrics.api.version=v2
nds.serverless.mtm.autoscale.maxInstanceSize=M80
nds.serverless.mtm.load.updateDocuments.enabled=true
nds.serverless.mtm.limits.maxResidentTenants=2000
nds.serverless.pool.limits.maxResidentMTMs=100
nds.serverless.pool.limits.maxResidentMTMsForAutoScaleMTMCapacity=25
nds.serverless.pool.limits.maxResidentTenants=6000
nds.serverless.pool.limits.maxResidentTenantsThreshold=.2
nds.serverless.mtm.backingInstanceSize=M40
nds.mtm.sentinel.cron.enabled=true
nds.serverless.tenant.load.updateDocuments.enabled=true
nds.serverless.tenant.privateNetworking.reservedButUnavailablePrivateEndpointExpirationThreshold=P7D
nds.serverless.serverlessUpgradeToDedicated.enabled=true
nds.serverParams.shouldSetRateLimiterParams=true
nds.serverless.mtm.autoscale.capacity.enabled=false
nds.serverless.mtm.organization.id=599deac39f78f703d855c218
nds.serverless.sentinel.organization.id=66390539e9868b4c34ae8250
nds.shared.sentinel.organization.id=66391bc3ac7fa1355b146b0f
nds.flex.sentinel.organization.id=66d87d312fca996b1dca896f
nds.svc.ipToRegionMapping.executorEnabled=true
nds.tenantUpgrade.s3BucketName=atlas-snapshots-us-east-1-stage
nds.xdsCentralUrl=xds-agents-stage.mongodb.com
nds.fleetAttributeCollection=true
mms.serverless.xDSServer.tlsRequired=true
mms.serverless.xds.error.log.alert.test.enabled=false
mms.serverless.xDSServer.enabled=false
netsuite.account.atlas.revenue.internal.id=1463
netsuite.account.cloud.receivable.internal.id=1541
netsuite.account.contra.liability.internal.id=1686
netsuite.account.mms.revenue.internal.id=257
netsuite.account.reserve.contra.internal.id=1319
netsuite.account.refund.salestax.debit.internal.id=109
netsuite.account.refund.vat.debit.internal.id=160
netsuite.api.accountNumber=1169967_SB1
netsuite.api.consumerKey=<SECRETMANAGER>
netsuite.api.consumerSecret=<SECRETMANAGER>
netsuite.app.endpoint=https://1169967-sb1.app.netsuite.com/
netsuite.api.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/
netsuite.api.token=<SECRETMANAGER>
netsuite.api.tokenSecret=<SECRETMANAGER>
netsuite.department.d15000.internal.id=213
netsuite.department.d15100.internal.id=259
netsuite.location.dublin.irl.internal.id=10
netsuite.location.nyc.usa.internal.id=4
netsuite.subsidiary.mongo.inc.item.id=1
netsuite.subsidiary.mongo.ltd.item.id=15
netsuite.rest.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/record/v1
netsuite.rest.query.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/query/v1
netsuite.restlet.endpoint=https://1169967-sb1.restlets.api.netsuite.com/app/site/hosting/restlet.nl
netsuite.restlet.tokenEndpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token
netsuite.restlet.cert.privateKey=<SECRETMANAGER>
netsuite.restlet.cert.id=<SECRETMANAGER>
netsuite.restlet.cert.scope=restlets,rest_webservices,suite_analytics
officeIps.service.urlMeshEnvVar=KUBE_OFFICEIPS_SERVICE_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC
okta.account.client.id=<SECRETMANAGER>
okta.account.client.secret=<SECRETMANAGER>
okta.api.token.emp-qa=
okta.api.token=<SECRETMANAGER>
okta.client.id=<SECRETMANAGER>
okta.client.secret=<SECRETMANAGER>
okta.cmab.client.secret=
okta.cmab.client.id=
okta.internal.server.id=aus4k4jv00hWjNnps297
okta.internal.domain=https://corp.mongodb.com
okta.external.domain=https://auth-qa.mongodb.com
okta.idpDiscoveryPolicyId=00pk4f0955YwdJeAC356
okta.idpId=00ok4f8icpP2iAvzI356
okta.requireNonce=true
okta.restrictedEnv.customerGroup=
okta.restrictedEnv.userGroupNames=
okta.sessionDiscovery.enabled=true
okta.profile.target.id=otyk4f8iwXh8C2Q0f356
okta.external.authorization.server.id=default
okta.webhooks.secretKey=
okta.token.authorized.client.ids=0oadn4hoajpzxeSEy357,0oa1j0jp84jXF8H4x358
onlinearchive.aws.dataLakeExternalId=<SECRETMANAGER>
onlinearchive.aws.dataLakeRoleARN=<SECRETMANAGER>
onlinearchive.fileCompressionType=snappy
opsgenie.api.eu.url=https://api.eu.opsgenie.com/v2/alerts
opsgenie.api.url=https://api.opsgenie.com/v2/alerts
opsgenie.api.us.url=https://api.opsgenie.com/v2/alerts
pagerduty.events.api.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.us.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.eu.v2.url=https://events.eu.pagerduty.com/v2/enqueue
pagerduty.api.regions.options=US,EU
pagerduty.app.id=P3LVLTH
ping.queue.size=50
ping.thread.count=10
ping.throttling.enabled=true
productionScore.hostname=
provision.allowReaperOptions=true
provision.allowTerminateAll=true
provision.allowUrlOverride=true
provision.dns.resolution.wait.secs=30
provision.dns.zone=mongo.plumbing
provision.includeRandomPin=false
provision.script.setup.name=run_atm_linux_agent.sh
qualtrics.api.token=
queryengine.aws.assumeRoleAccessKey=<SECRETMANAGER>
queryengine.aws.assumeRoleSecretKey=<SECRETMANAGER>
queryengine.aws.assumeRoleUserARN=<SECRETMANAGER>
queryengine.adminApi.gcp.regionName=CENTRAL_US
streams.processManager.enabled=true
iam.gcp.reCaptcha.apiKey=<SECRETMANAGER>
iam.gcp.reCaptcha.project.id=iam-recaptcha-dev
iam.gcp.reCaptcha.site.key=<SECRETMANAGER>
reCaptcha.enabled.atlas=true
reCaptcha.enabled.login=true
iam.reCaptcha.enterprise.login.enabled=true
iam.reCaptcha.login.minScore=0.5
iam.reCaptcha.registration.enabled=false
iam.reCaptcha.registration.minScore=0.5
iam.reCaptcha.external.registration.enabled=false
iam.reCaptcha.external.registration.minScore=0.5
iam.reCaptcha.linkExternalAccount.enabled=false
iam.reCaptcha.linkExternalAccount.minScore=0.5
reCaptcha.enabled.externalRegistration=false
reCaptcha.private.key=<SECRETMANAGER>
reCaptcha.public.key=<SECRETMANAGER>
realm.billing.api.url=https://realm-stage.mongodb.com/api/private/v1.0/billing/metrics/apps
segment.batch.url=https://api.segment.io/v1/batch
segment.clientSide.writeKey=fnpMEPjMyH0Z5e0A4Z9qrs1HC9ThUlO6
segment.group.url=https://api.segment.io/v1/group
segment.identify.url=https://api.segment.io/v1/identify
segment.profile.url=https://profiles.segment.com/v1/spaces
segment.serverSide.accessToken=<SECRETMANAGER>
segment.serverSide.personasSpaceId=spa_smteJ81u1XbJMRgmDR9Td5
segment.serverSide.writeKey=OsO5BIaGGknBz0z40t7NDWlNk52ZQPn6
segment.track.url=https://api.segment.io/v1/track
serverlessProxy.location=https://internal-downloads.mongodb.com/releases/prod/
sfdc.api.endpoint=https://test.salesforce.com/services/Soap/c/38.0/00DK000000W4vnY
sfdc.api.integrationOwnerId=005A0000002yZoK
sfdc.api.password=<SECRETMANAGER>
sfdc.api.username=<SECRETMANAGER>
sfdc.sandbox=true
sfdc.sync.cron.enabled=false
sfsc.api.password=<SECRETMANAGER>
sfsc.api.username=<SECRETMANAGER>
sfsc.api.client.id=<SECRETMANAGER>
sfsc.api.client.secret=<SECRETMANAGER>
sfsc.sync.url=https://sf-sync.staging.corp.mongodb.com/cloud/org
sfsc.api.endpoint=https://test.salesforce.com/services/Soap/c/38.0/00DK000000W4vnY
slack.api.message.url=https://slack.com/api/
slack.oauth2.clientId=<SECRETMANAGER>
slack.oauth2.clientSecret=<SECRETMANAGER>
slack.oauth2.url=https://slack.com/api/oauth.access
stitch.analytics.api.url=https://stitch-stage.mongodb.com/api/private/v1.0/analytics/reports/apps
stitch.api.key=<SECRETMANAGER>
stitch.billing.api.url=https://stitch-stage.mongodb.com/api/private/v1.0/billing/reports/apps
stripe.accountId.ltd=acct_19bBmHFBA1YvsEQI
stripe.accountId=acct_14T0SHL4RbRxRAxx
stripe.apiKey.ltd=<SECRETMANAGER>
stripe.apiKey.inc=<SECRETMANAGER>
stripe.pubKey.ltd=<SECRETMANAGER>
stripe.pubKey.inc=<SECRETMANAGER>
stripe.webhook.updatecard.secret=
stripe.webhook.updatecard.secret.ltd=
stripe.maxRequestsPerSecond=20
stripe.retry.maxAttempts=5
stripe.retry.minBackoff=2000
support.appEmbedLink=https://support.mongodb.com/api/auth/oidc/initial
taxamo.api.privateToken=<SECRETMANAGER>
taxamo.api.url=https://services.taxamo.com/api/v2
twilio.account.sid=<SECRETMANAGER>
twilio.auth.token=<SECRETMANAGER>
twilio.from.num=<SECRETMANAGER>
twilio.messagingService.sid=<SECRETMANAGER>
university.centralUrls=https://education-staging.corp.mongodb.com
victorops.alert.api.url=https://alert.victorops.com/integrations/generic/********/alert/
mms.backup.e2e.blockstore.aws.accesskey=
mms.backup.e2e.blockstore.aws.secretkey=
mms.backup.e2e.oplogstore.aws.accesskey=
mms.backup.e2e.oplogstore.aws.secretkey=
mms.backup.rollingReplacement.email=true
mms.featureFlag.backup.autoRecoveryUnsafeApplyOps=controlled
mongo.mmsdbmetering.encryptedCredentials=true
mongo.mmsdbmetering.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?retryWrites=true&w=majority&uuidRepresentation=standard
mongo.mmsdbmetering.ssl=true
mongo.billingauditing.encryptedCredentials=
mongo.billingauditing.mongoUri=mongodb+srv://f2a7d1882f4b9ff338aadce404d6cd9bee15cf8600cd9bc7e32f70e9d2639b77-e82307667026456c987d581da7aa60de-152f61cafea36ee63a0342db6639b5c840919aba38d6d0de9d4208cf5f824d36-7908645a001299dbff11ab4c23db6a32139e0bed7d93dd5d7f2111bc22f6dc4b6c247a3e7772c234b98a05893423d244a099a0414fe60ba35430994696c68d18:<EMAIL>/?maxPoolSize=100&compressors=snappy&retryWrites=true&retryReads=true&uuidRepresentation=standard
mongo.billingauditing.ssl=
metering.serviceMeshEnvVar=KUBE_MMS_METERING_KUBE_STAGING_ACTIVE_CLOUD_10GEN_CC
metering.user.publicKey=<SECRETMANAGER>
metering.user.privateKey=<SECRETMANAGER>
nds.meteringService.authn.migrationFlag=true
nds.xds.bindAddress=127.0.0.1
mms.serverless.envoyInstanceSize=M10
mms.featureFlag.serverless.grpc.incrementalRollout.global.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.tenant.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.continuous.enabled=disabled
mms.featureFlag.serverless.serverlessMtmEligibleForUpgradeToDedicated=controlled
mms.featureFlag.serverless.serverlessSharedUIOptionEnabled=controlled
mms.beamerApiKey=**********************************************
# This ratio is a double between [0,1]. Setting any value >1 will cause this threshold to effectively not be used.
oa.ratio.partitions.to.files.threshold=1.01
oa.avg.filesize.threshold.kilobytes=1024
nds.deleted.ingestion.pipeline.grace.period.minutes=5
nds.deleted.dataset.grace.period.minutes=5
azure.ai.openai.compass.accessKey.1=<SECRETMANAGER>
azure.ai.openai.compass.accessKey.2=<SECRETMANAGER>
azure.ai.openai.compass.apiBase.1=https://query-generator-resource-stage-1.openai.azure.com/
azure.ai.openai.compass.apiBase.2=https://query-generator-resource-stage-1.openai.azure.com/
azure.ai.openai.compass.secretKey=<SECRETMANAGER>
azure.ai.openai.compass.modelName=compass-gpt4o
azure.ai.openai.charts.accessKey.1=<SECRETMANAGER>
azure.ai.openai.charts.accessKey.2=<SECRETMANAGER>
azure.ai.openai.charts.apiBase.1=https://atlas-charts-stage-1.openai.azure.com/
azure.ai.openai.charts.apiBase.2=https://atlas-charts-stage-1.openai.azure.com/
azure.ai.openai.charts.gpt4o.accessKey.1=<SECRETMANAGER>
azure.ai.openai.charts.gpt4o.apiBase.1=https://atlas-charts-stage.openai.azure.com/
azure.ai.openai.migrator.accessKey.1=<SECRETMANAGER>
azure.ai.openai.migrator.accessKey.2=<SECRETMANAGER>
azure.ai.openai.migrator.apiBase.1=https://relational-migrator-dev-1.openai.azure.com/
azure.ai.openai.migrator.apiBase.2=https://relational-migrator-dev-1.openai.azure.com/
azure.ai.openai.migrator.gpt4o.accessKey.1=<SECRETMANAGER>
azure.ai.openai.migrator.gpt4o.apiBase.1=https://relational-migrator-dev-2.openai.azure.com/
azure.ai.openai.migrator.gpt4-turbo.accessKey.1=<SECRETMANAGER>
azure.ai.openai.migrator.gpt4-turbo.apiBase.1=https://relational-migrator-dev-3.openai.azure.com/
azure.ingestion.dlz.storageAccountKey=
azure.oa.dlz.storageAccountKey=
azure.cps.oplogs.storageAccountKeys=
mms.featureFlag.apix.enableApiTelemetryCustomFields=disabled
mms.telemetry.api.sampling.percentage=100
mms.telemetry.api.denylistexemptions=/api/private/unauth/telemetry/events
mms.telemetry.api.denylist=/api/private,/api/scim,/accessLogs,/account,/activity,/admin,/agent,/agentlog,/agentsApi,/alertOAuth2,/allClusters,/analytics,/automation,/backingDatabases,/backup,/billing,/canonicalHosts,/chart,/charts,/conf,/customer,/dashboard,/deployment,/design,/docs,/download,/email,/exception,/explorer,/export,/federation,/federationSettings,/group,/host,/links,/log,/maintenance,/metrics,/monitor,/nds,/okta,/orgs,/performanceAdvisor,/ping,/recovery,/saml,/servers,/settings,/setup,/sso,/support,/system,/test,/usage,/usageData,/usageTypes,/user,/v2,/{groupId},/{orgId},/{privateLinkId},/uiMsgs,/goto,admin,/selfServe,/partners,/prometheus
mms.telemetry.aws.secrets.accessKey=<SECRETMANAGER>
mms.telemetry.aws.secrets.secretKey=<SECRETMANAGER>
mms.telemetry.aws.kinesis.streamName=cloud__apix__api_telemetry__staging
mms.telemetry.aws.kinesis.region=us-east-1
mms.telemetry.aws.kinesis.requestTimeout=2000
mms.telemetry.aws.kinesis.socketTimeout=15000
mms.telemetry.producer.queue.maxSize=200
mms.telemetry.producer.queue.offerTimeoutMillis=10
mms.telemetry.producer.threadCount=1
mms.telemetry.producer.batchSize=50
mms.telemetry.producer.queue.drainIntervalMillis=1000
mms.monitoring.performanceadvisor.serverless.autoindexing.groupPercentageAllowed=100
# The following three are used for product validation, and are therefore empty above dev.
mms.slack.test.channel=
mms.slack.test.token=
mms.alert.test.email.emailAddress=
mms.alert.test.sms.phoneNumber=
mms.alert.test.microsoftTeams.url=
mms.alert.test.victorOps.apiKey=
mms.alert.test.datadog.apiToken=
mms.alert.test.pagerduty.serviceKey=
mms.alert.test.victorOps.routingKey=
mms.alert.test.webhook.apiKey=
mms.alert.test.opsGenie.apiKey=
mms.account.mfa.getMfaFactors.ratelimit.enabled=true
mms.account.mfa.getMfaFactors.ratelimit.maxHitsPerPeriod=60
mms.account.mfa.getMfaFactors.ratelimit.minutesPeriod=15
mms.account.mfa.enrollMfaFactor.ratelimit.enabled=true
mms.account.mfa.enrollMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.enrollMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaFactor.ratelimit.enabled=true
mms.account.mfa.activateMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.activateMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaPushFactor.ratelimit.enabled=true
mms.account.mfa.activateMfaPushFactor.ratelimit.maxHitsPerPeriod=30
mms.account.mfa.activateMfaPushFactor.ratelimit.minutesPeriod=15
mms.account.mfa.verifyMfaFactor.ratelimit.enabled=true
mms.account.mfa.verifyMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyMfaFactor.ratelimit.minutesPeriod=5
mms.account.mfa.verifyPushFactorChallenge.ratelimit.enabled=true
mms.account.mfa.verifyPushFactorChallenge.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyPushFactorChallenge.ratelimit.minutesPeriod=5
mms.account.mfa.deleteMfaFactor.ratelimit.enabled=true
mms.account.mfa.deleteMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.deleteMfaFactor.ratelimit.minutesPeriod=15
mms.account.auth.getAuthMfaState.ratelimit.enabled=true
mms.account.auth.getAuthMfaState.ratelimit.maxHitsPerPeriod=30
mms.account.auth.getAuthMfaState.ratelimit.minutesPeriod=5
mms.account.auth.verifyAuthMfa.ratelimit.enabled=true
mms.account.auth.verifyAuthMfa.ratelimit.maxHitsPerPeriod=10
mms.account.auth.verifyAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.resendAuthMfa.ratelimit.enabled=true
mms.account.auth.resendAuthMfa.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resendAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.cancelAuthVerify.ratelimit.enabled=true
mms.account.auth.cancelAuthVerify.ratelimit.maxHitsPerPeriod=5
mms.account.auth.cancelAuthVerify.ratelimit.minutesPeriod=15
mms.account.auth.cancelAuthVerify.ratelimit.payload.jsonPath=$.username
mms.account.auth.checkPassword.ratelimit.enabled=true
mms.account.auth.checkPassword.ratelimit.maxHitsPerPeriod=10
mms.account.auth.checkPassword.ratelimit.minutesPeriod=5
nds.embeddedConfig.minMongoDBVersion=8.0
nds.embeddedConfig.maxShardCount=3
mms.account.auth.socialSSO.ratelimit.enabled=true
mms.account.auth.socialSSO.ratelimit.maxHitsPerPeriod=50
mms.account.auth.socialSSO.ratelimit.minutesPeriod=5
mms.user.redirectInvite.ratelimit.enabled=true
mms.user.redirectInvite.ratelimit.maxHitsPerPeriod=60
mms.user.redirectInvite.ratelimit.minutesPeriod=15
mms.user.redirectInvite.ratelimit.pathParam.parameterName=username
mms.user.resetSend.ratelimit.enabled=true
mms.user.resetSend.ratelimit.maxHitsPerPeriod=10
mms.user.resetSend.ratelimit.minutesPeriod=15
mms.user.resetSend.ratelimit.payload.jsonPath=$.username
mms.organization.setSecurityContact.ratelimit.enabled=true
mms.organization.setSecurityContact.ratelimit.maxHitsPerPeriod=10
mms.organization.setSecurityContact.ratelimit.minutesPeriod=5
mms.account.profile.deleteEmailChangeRequest.ratelimit.enabled=true
mms.account.profile.deleteEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.deleteEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.createEmailChangeRequest.ratelimit.enabled=true
mms.account.profile.createEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.createEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.checkPasswordForProfileChange.ratelimit.enabled=true
mms.account.profile.checkPasswordForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.checkPasswordForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.verifyFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyFactorForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.minutesPeriod=15
cypress.aws.ses.accesskey=
cypress.aws.ses.secretkey=
# Experimentation Properties
# analytics.enabled must be true and pholiota properties must be filled if setting the experiments.enabled property to true
analytics.enabled=false
experiments.enabled=true
pholiota.getExperimentsUrl=https://us-east-1.aws.data.mongodb-api.com/app/pholiota-ol-qa-qeaue/endpoint/getExperiments?expType=IFR
pholiota.mmsApiKey=<SECRETMANAGER>
pholiota.mmsHttpReferer=https://STAGE-mms/
streams.planner.enabled=true
streams.vpcpeering.scanner.planner.enabled=true
streams.privatelink.planner.enabled=true
streams.proxyInstanceDao.ttlDays=30
streams.proxyDeploymentDao.ttlDays=30
mms.account.profile.getOktaUser.ratelimit.enabled=true
mms.account.profile.getOktaUser.ratelimit.maxHitsPerPeriod=20
mms.account.profile.getOktaUser.ratelimit.minutesPeriod=1
mms.account.profile.updatePassword.ratelimit.enabled=true
mms.account.profile.updatePassword.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updatePassword.ratelimit.minutesPeriod=1
mms.account.profile.updateProfile.ratelimit.enabled=true
mms.account.profile.updateProfile.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updateProfile.ratelimit.minutesPeriod=1
mms.account.profile.activateFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.activateFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.activateFactorForProfileChange.ratelimit.minutesPeriod=1
mms.user.getUserPartnerIntegrationsData.ratelimit.enabled=true
mms.user.getUserPartnerIntegrationsData.ratelimit.maxHitsPerPeriod=100
mms.user.getUserPartnerIntegrationsData.ratelimit.minutesPeriod=20
mms.user.getUserPartnerIntegrationsData.ratelimit.pathParam.parameterName=username
mms.user.registerCall.ratelimit.enabled=true
mms.user.registerCall.ratelimit.maxHitsPerPeriod=100
mms.user.registerCall.ratelimit.minutesPeriod=20
mms.account.auth.unauthedResendVerificationEmail.ratelimit.enabled=true
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.maxHitsPerPeriod=1
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.minutesPeriod=60
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.jsonPath=$.username
mms.account.auth.resendVerificationEmail.ratelimit.enabled=true
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resendVerificationEmail.ratelimit.user.maxHitsPerPeriod=1
mms.account.auth.resendVerificationEmail.ratelimit.user.minutesPeriod=60
mms.account.auth.resetPasswordRequestSend.ratelimit.enabled=true
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.jsonPath=$.username
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.enabled=true
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.jsonPath=$.username
mms.account.auth.resetComplete.ratelimit.enabled=true
mms.account.auth.resetComplete.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resetComplete.ratelimit.minutesPeriod=20
mms.account.auth.resetComplete.ratelimit.payload.jsonPath=$.username
mms.user.mfa.auth.ratelimit.enabled=true
mms.user.mfa.auth.ratelimit.maxHitsPerPeriod=10
mms.user.mfa.auth.ratelimit.minutesPeriod=20
mms.authz.isAuthorized.ratelimit.enabled=true
mms.authz.isAuthorized.ratelimit.maxHitsPerPeriod=10
mms.authz.isAuthorized.ratelimit.minutesPeriod=1
mms.authz.fgaOrgIdsCacheDurationSeconds=10
mms.authz.fgaOrgIdsCache.enabled=true
mms.account.device.verifyDeviceCode.ratelimit.enabled=true
mms.account.device.verifyDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.verifyDeviceCode.ratelimit.minutesPeriod=20
mms.account.device.confirmDeviceCode.ratelimit.enabled=true
mms.account.device.confirmDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.confirmDeviceCode.ratelimit.minutesPeriod=20
mms.api.organizations.updateOrganizationSettings.ratelimit.enabled=true
mms.api.organizations.updateOrganizationSettings.ratelimit.maxHitsPerPeriod=10
mms.api.organizations.updateOrganizationSettings.ratelimit.minutesPeriod=5
mms.api.private.account.device.generateCode.ratelimit.enabled=true
mms.api.private.account.device.generateCode.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.generateCode.ratelimit.minutesPeriod=20
mms.api.private.account.device.getToken.ratelimit.enabled=true
mms.api.private.account.device.getToken.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.getToken.ratelimit.minutesPeriod=20
mms.api.private.account.device.revoke.ratelimit.enabled=true
mms.api.private.account.device.revoke.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.revoke.ratelimit.minutesPeriod=20
mms.api.private.email.sendToSecurityContact.ratelimit.enabled=true
mms.api.private.email.sendToSecurityContact.ratelimit.maxHitsPerPeriod=100
mms.api.private.email.sendToSecurityContact.ratelimit.minutesPeriod=20
mms.api.private.dbUser.validate.ratelimit.enabled=true
mms.api.private.dbUser.validate.ratelimit.maxHitsPerPeriod=100
mms.api.private.dbUser.validate.ratelimit.minutesPeriod=20
mms.api.private.telemetry.events.createEvents.ratelimit.enabled=true
mms.api.private.telemetry.events.createEvents.ratelimit.maxHitsPerPeriod=100
mms.api.private.telemetry.events.createEvents.ratelimit.minutesPeriod=1
mms.api.oauth.tokenManagement.ratelimit.enabled=true
mms.api.oauth.tokenManagement.ratelimit.maxHitsPerPeriod=10
mms.api.oauth.tokenManagement.ratelimit.minutesPeriod=1
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.enabled=false
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.maxHitsPerPeriod=20
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.minutesPeriod=1
mms.monitoring.metrics.backfill.createdbchunks.cron.enabled=false
mms.monitoring.metrics.backfill.max.jobs=0
mms.monitoring.rrd.fts.metricscluster.enabled=true
monitoring.databasemeasurements.writes.cutover.enabled=true
monitoring.hostmeasurements.writes.cutover.enabled=true
monitoring.hosts.metrics.alert.mode=READ_DEST_ONLY
monitoring.realmmeasurements.writes.cutover.enabled=true
aws.atlasSelfServeMarketplaceProductId=
aws.marketplaceSellerId=
mms.azurenative.AzureNativeRateLimiting.ratelimit.enabled=true
mms.azurenative.AzureNativeRateLimiting.ratelimit.maxHitsPerPeriod=100
mms.azurenative.AzureNativeRateLimiting.ratelimit.minutesPeriod=5
mms.partnerIntegrations.vercelNative.ratelimit.enabled=true
mms.partnerIntegrations.vercelNative.ratelimit.suspendableIp.maxHitsPerPeriod=20
mms.partnerIntegrations.vercelNative.ratelimit.suspendableIp.minutesPeriod=1
mms.partnerIntegrations.vercelNative.ratelimit.pathParam.maxHitsPerPeriod=100
mms.partnerIntegrations.vercelNative.ratelimit.pathParam.minutesPeriod=5
mms.partnerIntegrations.vercelNative.ratelimit.pathParam.parameterName=installationId
mms.partnerIntegrations.vercelNativeGet.ratelimit.enabled=true
mms.partnerIntegrations.vercelNativeGet.ratelimit.suspendableIp.maxHitsPerPeriod=20
mms.partnerIntegrations.vercelNativeGet.ratelimit.suspendableIp.minutesPeriod=1
mms.partnerIntegrations.vercelNativeGet.ratelimit.pathParam.maxHitsPerPeriod=100
mms.partnerIntegrations.vercelNativeGet.ratelimit.pathParam.minutesPeriod=5
mms.partnerIntegrations.vercelNativeGet.ratelimit.pathParam.parameterName=installationId
mms.partnerIntegrations.vercelNative.loadTesting.concurrentThreadCount=10
mms.partnerIntegrations.vercelNative.loadTesting.cleanupMaxAttempts=2
mms.partnerIntegrations.vercelNative.canProvisionNewResources=true
mms.partnerIntegrations.vercelNative.vercelProductId=iap_sDUZklvnk4PU1AcZ
mms.partnerIntegrations.encryptionKey=<SECRETMANAGER>
mms.featureFlag.gcpArmAxion=disabled
mms.featureFlag.azureArmDpsv6Epsv6=disabled
mms.featureFlag.horizontalScalingAutoSharding=disabled
mms.featureFlag.gcpN4Family=disabled
integrations.deletion.dryRun=false
slack.oauth.v2.clientId=<SECRETMANAGER>
slack.oauth.v2.clientSecret=<SECRETMANAGER>
slack.oauth.v2.url.backend=https://slack.com/api/oauth.v2.access
slack.oauth.v2.url.frontend=https://slack.com/oauth/v2/authorize
slack.oauth.v2.scopes=chat:write,chat:write.customize,chat:write.public
slack.oauth.v2.enabled=true
mms.disaggregatedStorage.fetchLatestBuildFromDev.globalAdminApiSecret=
nds.shadowClusters.enabled=false
nds.gcp.provisionedIOPS.enabled=false
eventbus.publisher.queueingEnabled=true
eventbus.publisher.awsRoleArn=arn:aws:iam::************:role/event-service-resources-staging/event-service-queue-producer-staging-aws-us-east-1
eventbus.publisher.kinesis.streamName=event-service-stream-staging-aws-us-east-1
communication.messagebus.publisher.kinesis.streamName=communication-service-stream-staging-aws-us-east-1
communication.statusUpdate.publisher.kinesis.streamName=status-update-service-stream-staging-aws-us-east-1
communication.publisher.awsRoleArn=arn:aws:iam::************:role/communication-service-resources-staging/communication-service-queue-staging-aws-us-east-1
customer.metrics.service.baseUrl=https://observability-agents-staging.mongodb.com
mms.new.db.user.scramIterationCount=1300000
mms.new.db.user.scramSHA256IterationCount=600000
maas.ingestion.usecase.150760c1-7a40-5033-b8ff-265e24a4a6b0.adminKey=<SECRETMANAGER>
maas.ingestion.usecase.a152094e-259b-5d73-bd16-e49e3fd6899c.adminKey=<SECRETMANAGER>
mms.robustNodeHorizonMappingEnabled=true
mms.github.publicips.refreshEnabled=true
maas.internalGrafana.url=https://grafana-staging.helix.corp.mongodb.com
maas.internalGrafana.usecaseId.150760c1-7a40-5033-b8ff-265e24a4a6b0.datasourceId=deo3lcwmmkjy8f
maas.internalGrafana.usecaseId.296e3c80-f42c-51f1-9a84-5477e61d0277.datasourceId=aeo3l9f4enpq8d
maas.internalGrafana.usecaseId.336c2b2f-e495-5e7d-9a9c-9e6f492fa157.datasourceId=eeqn076ojuvwgd
maas.internalGrafana.usecaseId.82653516-67ba-5ffa-be02-5e4dd0376682.datasourceId=ceo3li3gkny0we
# Rate Limiting Configuration - filter enabled via config service feature flag mms.featureFlag.apix.ratelimit.apiMiddleware
## Shadow mode (false = log only, true = reject requests) - currently disabled for future use
ratelimit.apiMiddleware.rejectRequests=false
## Circuit Breaker Configuration (disabled via circuitbreaker.enabled=false)
ratelimit.apiMiddleware.circuitbreaker.name=ratelimit-redis-breaker
ratelimit.apiMiddleware.circuitbreaker.failureRateThreshold=40.0
ratelimit.apiMiddleware.circuitbreaker.slidingWindowSize=50
ratelimit.apiMiddleware.circuitbreaker.permittedCallsInHalfOpenState=3
ratelimit.apiMiddleware.circuitbreaker.waitDurationSeconds=15
ratelimit.apiMiddleware.circuitbreaker.slowCallRateThreshold=20.0
ratelimit.apiMiddleware.circuitbreaker.slowCallDurationThresholdMillis=50
## the minimum number of calls which are required before circuitbreaker can calculate error rate
## default value 100 + 1 to count for the redis coldstart.
ratelimit.apiMiddleware.circuitbreaker.minimumNumberOfCalls=101
# Redis cache configuration (if disabled, in-memory local cache will be used)
ratelimit.redis.enabled=false
ratelimit.redis.auth.mode=AWS
ratelimit.redis.endpoint=
ratelimit.redis.port=6379
ratelimit.redis.cache.name=rate-limit-service-cache-stage
ratelimit.redis.region=us-east-1
ratelimit.redis.roleArn=
ratelimit.redis.credentials.username=rate-limit-service-cache-stage
ratelimit.redis.credentials.password=
ratelimit.redis.ssl.enabled=true
## IP Policy Configuration
ratelimit.policies.default.ip.config.capacity=400
ratelimit.policies.default.ip.config.refillRate=100
ratelimit.policies.default.ip.config.refillDurationSeconds=60
## Group Policy Configuration
ratelimit.policies.default.group.config.capacity=2000
ratelimit.policies.default.group.config.refillRate=1000
ratelimit.policies.default.group.config.refillDurationSeconds=60
## Organization Policy Configuration
ratelimit.policies.default.organization.config.capacity=5000
ratelimit.policies.default.organization.config.refillRate=2000
ratelimit.policies.default.organization.config.refillDurationSeconds=60
## User Policy Configuration
ratelimit.policies.default.user.config.capacity=1000
ratelimit.policies.default.user.config.refillRate=500
ratelimit.policies.default.user.config.refillDurationSeconds=60
# Data Exfiltration Control Plane IPs
nds.dataExfiltration.loadBalancerNames=*
