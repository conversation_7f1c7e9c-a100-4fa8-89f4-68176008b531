#mms.monitoring.chart.export.logo=client/images/logo-mongodb.png Uncomment this to test chart export functionality locally
client.authn.http.address=localhost:30053
client.authn.grpc.address=localhost:30051
client.alertprocessing.grpc.address=localhost:30067
client.communication.grpc.address=localhost:30079
client.authz.grpc.address=localhost:50059
client.authzv2.grpc.address=localhost:50059
client.comment.grpc.address=localhost:50051
client.meterusages.grpc.address=localhost:50061
client.config.grpc.address=localhost:50071
client.event.grpc.address=localhost:50505
client.job.grpc.address=localhost:50229
client.payments.grpc.address=localhost:50055
client.pricing.grpc.addressEnvVar=localhost:50057
client.apiregistry.grpc.address=localhost:50052
client.slsbackup.v1.grpc.address=localhost:50138
#https://github.com/10gen/mpa/blob/main/dev-atlas/docker-compose.yml can be used to spin up predictive service
client.predictive-metrics.grpc.address=localhost:52300
client.predictive-metrics-test.grpc.address=localhost:52301
client.schedulerservice.v1.grpc.address=local.sls.mmscloudteam.com:32000
client.controlplaneservice.v1.grpc.address=local.sls.mmscloudteam.com:32000
client.cellmetadata.v1.grpc.address=local.sls.mmscloudteam.com:32005
sls.multicell=true
sls.cell2.schedulerservice.v1.grpc.address=local.sls.mmscloudteam.com:33000
sls.cell2.cellmetadata.v1.grpc.address=local.sls.mmscloudteam.com:33005
sls.cell3.schedulerservice.v1.grpc.address=local.sls.mmscloudteam.com:34000
sls.cell3.cellmetadata.v1.grpc.address=local.sls.mmscloudteam.com:34005
mms.monitoring.slowlogs.ingestion.targetLogsPerMinute=600000
mms.monitoring.maas.backfill.write.usecaseId=296e3c80-f42c-51f1-9a84-5477e61d0277
mms.monitoring.maas.backfill.write.publisherKey=eyJhbGciOiJFUzUxMiIsInR5cCI6IkpXVCIsImtpZCI6Im1yay1mMjRhNmQ0ZTljYzE0MGM5ODUwMjFkMGUxNDlkMjE2YSJ9.********************************************************************************************************************************************************************************************************************************************************************************.AGTdFNveErFXi3uPIUhOea3yGQbPdCQ7EaF8vVLwgqaQqP5ZeEG__YfWlmKtBPMjquZ5F0cbG_OSQlgc7R6oS_KKABJNogL39G3yT01SK1ZDUolX-v-F0-ZTwqFjLO_nBAM6oIbiE62wLyr1XyqqtP0CVj-1O6L4FbAwMQ0xwfRkKP6W
client.metrics.http.address=localhost:32001
account.centralUrl.override=http://localhost:8080
account.centralUrl=http://localhost:8080
account.session.maxConcurrent=100
account.session.maxHours=1440
alert.flapping.start.threshold=0.4
alert.flapping.stop.threshold=0.3
alert.flapping.linear.weight=0.2
alert.flapping.strategy=fixed
alert.flapping.skip.enabled=true
alert.flapping.enabled.types=OUTSIDE_METRIC_THRESHOLD
alert.flapping.sufficient.states.limit=20
amplitude.apiKey=<SECRETMANAGER>
amplitude.secretKey=<SECRETMANAGER>
atlas.backup.regional.deployment.enabled=false
mms.checkExtraMaintenanceEnabled=true
mms.aws.api.credentialsProviders=STATIC
mms.featureFlag.awsGraviton4=controlled
mms.featureFlag.awsGraviton4NVMe=controlled
mms.featureFlag.awsCapacityAwareAZSelection=enabled
mms.featureFlag.azureCapacityAwareAZSelection=enabled
mms.featureFlag.gcpCapacityAwareAZSelection=enabled
mms.featureFlag.capacityAwareAZPreventativeRollbackSupport=controlled
mms.featureFlag.atlasDataRegionalization=controlled
mms.featureFlag.atlasDataRegionalizationGroup=controlled
mms.featureFlag.logcollectionS3Backend=enabled
mms.featureFlag.RtppDiskThroughputMetrics=enabled
mms.featureFlag.atlasChainPauseMoves=controlled
mms.featureFlag.atlasFcvPinningInUi=enabled
mms.featureFlag.atlasKmipKeyRotationInMaintenanceWindows=controlled
mms.featureFlag.validateS3ChunksOnDownload=controlled
mms.featureFlag.atlasConfigurableGp3Iops=controlled
mms.featureFlag.atlasExcludeRegionUseast2FromAzureDsv5Esv5Families=controlled
mms.featureFlag.customSessionTimeouts=controlled
mms.featureFlag.atlasAutomatePriorityTakeover=controlled
mms.featureFlag.atlasResourcePoliciesExtensionWithUi=enabled
mms.featureFlag.serverlessUpgradeToDedicatedDebugMode=controlled
mms.featureFlag.atlasAutohealResyncImprovements=enabled
mms.featureFlag.atlasAutohealRemoveIcmpPing=enabled
mms.featureFlag.atlasAcknowledgesApiKeyFromChefConfCall=enabled
mms.featureFlag.atlasAlwaysManagedDefaultRwConcern=enabled
mms.featureFlag.atlasTunedInstanceIndexBuildLimits=enabled
mms.featureFlag.atlasTurnDiskWarmingOffForAzureLegacy=controlled
mms.featureFlag.atlasAutohealReducedThreshold=enabled
mms.featureFlag.disableScramSha1Auth=controlled
mms.featureFlag.atlasAzureExcludeConstrainedCombos=controlled
nds.autoEnableAtlasTunedInstanceIndexBuildLimitsNewProjects=true
mms.featureFlag.dataExfiltrationManageSecurityGroupsEnabled=controlled
mms.featureFlag.dataExfiltrationSecurityGroupsExcludeAllowAllRuleEnabled=controlled
mms.featureFlag.dataExfilPreventionSidecarEnabled=controlled
mms.featureFlag.dataExfilPreventionSidecarToProxyRoutingEnabled=controlled
mms.featureFlag.atlasAutohealReduceShutdownTime=enabled
mms.featureFlag.atlasAutohealReduceShutdownTimeHealRepair=controlled
mms.atlasAccessTransparencyX509CertsEnabled=true
mms.featureFlag.atlasAdvancedRegionalizedPrivateEndpoints=controlled
mms.atlasEmployeeInformationOktaReadOnlyClientCredentials=<SECRETMANAGER>
mms.atlasEmployeeInformationOktaApiKeyUrl=https://corp-qa.mongodb.com
mms.atlasEmployeeInformationOktaApiKeyClientId=0oa2bfy8z3wXb5gET0h8
mms.atlasEmployeeInformationOktaIntegrationEnabled=false
mms.monitoring.performanceadvisor.rollup.scheduler.enabled=true
mms.featureFlag.atlasProvideHardcodedOidcIdpInformation=controlled
mms.featureFlag.clusterCentricPerformanceAdvisor=enabled
mms.featureFlag.clusterCentricQueryProfiler=controlled
mms.featureFlag.namespaceInsights=controlled
mms.featureFlag.updateMetricsUiForEmbeddedConfigServers=enabled
mms.featureFlag.queryInsights=disabled
mms.featureFlag.atlasAzureSsdPV2=enabled
mms.featureFlag.atlasAzureSsdPV2Wave2=controlled
mms.featureFlag.atlasAzureSsdPV2EnablePreviewRegions=controlled
mms.featureFlag.atlasAzureSsdForcePV1=controlled
mms.featureFlag.atlasOptOutPeriodicCorruptionDetection=controlled
mms.featureFlag.atlasDbcheckEnableMidCollectionResumability=controlled
mms.featureFlag.atlasPrioritizeHavingPrimaryOnUpscaledNode=enabled
mms.featureFlag.atlasDiskWarmingSupport=enabled
mms.featureFlag.atlasIgnoreDiskWarmingState=controlled
mms.featureFlag.sqlSchemaManagementUI=enabled
mms.featureFlag.streamsEnableAzure=controlled
mms.featureFlag.streamsEnableAdditionalAzureRegions=controlled
mms.featureFlag.streamsAzurePrivateLink=enabled
mms.featureFlag.streamsAWSPrivateLink=enabled
nds.ifr.s3BucketName=mms-ifr-local
nds.ifr.s3RegionName=US_EAST_2
mms.featureFlag.streamsEnableAdditionalRegions=disabled
mms.featureFlag.atlasDbcheckWaitForAllNodesToObserveDbcheckStop=controlled
mms.featureFlag.atlasAutomaticEmbeddedConfigTransitions=enabled
mms.featureFlag.vqpBinning=controlled
mms.featureFlag.atlasAzureDsv5AndEsv5InstanceFamilies=enabled
mms.featureFlag.atlasLiveImportHelixPush=controlled
mms.featureFlag.atlasLiveImportHelixPull=controlled
mms.featureFlag.atlasShardedClustersBehindUniformFrontend=controlled
mms.featureFlag.atlasMongomirrorLiveImportHelix=controlled
mms.featureFlag.atlasLiveImportLargeNodePool=disabled
mms.featureFlag.atlasEnableAdditionalDbcheckValidations=controlled
mms.featureFlag.atlasStreamsSP10InstanceTier=enabled
mms.featureFlag.dataExplorerCompassWeb=controlled
mms.featureFlag.dataExplorerCompassWeb.userControlledDisable=controlled
mms.featureFlag.disableDiscoveryIpAddressAliases=controlled
mms.featureFlag.atlasClusterScalingImprovementsPhase1=enabled
mms.featureFlag.atlasIndependentShardScaling=enabled
mms.featureFlag.atlasAsymmetricShardAutoscaling=controlled
mms.featureFlag.baas.PricingChange=enabled
mms.featureFlag.billingResourceTaggingBulkEndpoint=enabled
mms.featureFlag.streamsVpcPeeringPreview=enabled
mms.featureFlag.enableLogRequestTimeframe=enabled
mms.featureFlag.enabledRevampedTriggersUi=enabled
mms.featureFlag.enableTypeScriptInFunctions=controlled
mms.featureFlag.enterpriseLandingPage=controlled
mms.featureFlag.enterpriseSandbox=controlled
mms.featureFlag.deprecateEndpointsAndDataApi=controlled
mms.featureFlag.deprecateThirdPartyServices=controlled
mms.featureFlag.allowCrossRegionExtendedStorage=controlled
mms.featureFlag.disableDarkReader=controlled
mms.featureFlag.bicDeprecationShowWarning=enabled
mms.featureFlag.bicDeprecation=controlled
mms.featureFlag.exemptFromBicDeprecation=controlled
mms.featureFlag.atlasMigrationHubCutover=enabled
mms.featureFlag.customerPinnedFcvAndMdbDowngrade=enabled
mms.featureFlag.atlasAllowOneNodeDownInPacpcm=enabled
mms.featureFlag.atlasAllowSeveralNodesDownInPacpcm=enabled
mms.featureFlag.atlasGcpHyperdiskExtreme=controlled
mms.featureFlag.atlasAutomationShorterQuiesceTime=enabled
mms.featureFlag.atlasAutomationSpecifyForceTrueForShutdowns=enabled
mms.featureFlag.atlasAllowUpscalingToCleanlyAbandonPlans=enabled
mms.atlasClusterUpdateOptimisticConcurrency=true
mms.featureFlag.streamsVPCPeering=enabled
mms.featureFlag.disableCloudNav=controlled
mms.featureFlag.atlasFcvDowngradeAdminAction=enabled
mms.featureFlag.atlasResourcePolicies=controlled
mms.featureFlag.orgLevelGenAiControlSwitch=enabled
mms.featureFlag.agentRotatesAtlasProxyLogs=enabled
mms.featureFlag.al2023AWSOs=controlled
mms.featureFlag.awsAL2023ForceMigrateNVMe=controlled
mms.featureFlag.al2023AzureOs=controlled
mms.featureFlag.azureAL2023ForceMigrateNVMe=controlled
mms.featureFlag.al2023GCPOs=controlled
mms.featureFlag.allowEnforceMinTls13=controlled
mms.featureFlag.atlasTls13AutoUpgradeEnabled=controlled
mms.featureFlag.atlasDeprecateTls10AndTls11=controlled
mms.featureFlag.dataExplorerGenAIFeatures=controlled
mms.featureFlag.dataExplorerGenAISampleDocument=controlled
mms.featureFlag.disableDataExplorerGenAISampleDocumentPassing=controlled
mms.featureFlag.atlasReleaseAwsIps=controlled
mms.featureFlag.alertHostSSHSessionStarted=controlled
mms.featureFlag.awsUseIpamIp=controlled
mms.featureFlag.autoscaling.predictive.compute.enabled=controlled
mms.featureFlag.autoscaling.predictive.compute.m10m20enabled=controlled
mms.featureFlag.enableParcaAgent=controlled
mms.featureFlag.customer.queryShapeInsights=controlled
nds.autoscaling.predictive.autoscaler.crossvalidation.threshold=0.2
nds.autoscaling.predictive.autoscaler.crossvalidation.windowInMinutes=10
nds.custom.roles.any.db.resources.enabled=true
mms.rootVolumeMetrics.rolloutPercentage=100
atlas.regionalData.enabled=true
mms.monitoring.alwaysUpdateCollectedIndex.enabled=true
mms.monitoring.regional.maintenance.enabled=true
mms.monitoring.abbrvslowlogs.maintenanceEnabled=true
mms.monitoring.parollupshapes.maintenanceEnabled=true
mms.monitoring.queryStats.exporter.usecaseId=82653516-67ba-5ffa-be02-5e4dd0376682
mms.monitoring.queryStats.exporter.publisherKey=
mms.monitoring.tokenization.allowExclusions=true
mms.automation.agent.config.atlasClusterNameRollout=100
mms.mana.http.socketTimeout=10000
mms.mana.http.connectTimeout=10000
mms.mana.http.url=https://mana-qa.corp.mongodb.com/alohomora
mms.mana.clientId=<SECRETMANAGER>
mms.mana.clientSecret=<SECRETMANAGER>
mms.mana.authToken=
atlasProxy.location=https://internal-downloads.mongodb.com/atlasproxy-dev/
atlasProxy.version=********.0.0.**********
atlasUIS.location=https://mongodb-mms-build-atlas-uis.s3.amazonaws.com/atlas-uis/tarballs/
atlasUIS.version=********.0.0.**********
authn.oauth.enabled=true
authn.oauth.serviceAccounts.enabled=true
authn.oauth.serviceAccounts.alertsEnabled=true
# This issuer depends on the issuer value in AuthN config, they must be the same
authn.oauth.issuer=http://localhost
authn.adminSecretKey=<SECRETMANAGER>
authn.internalClient.id=mdb_ic_id_MMS_LOCAL
authn.internalClient.secret=<SECRETMANAGER>
auto.provision.aws.trusted.accesskey=<SECRETMANAGER>
auto.provision.aws.trusted.id=<SECRETMANAGER>
auto.provision.aws.trusted.secretkey=<SECRETMANAGER>
automation.agent.location=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/automation-agent/${env}/
automation.agent.location.atlas=https://internal-downloads.mongodb.com/automation-dev/automation-agent/${env}/
automation.agent.minimumVersion=13.10.0.8620-1
automation.agent.atlasMinimumVersion=13.37.2.9605-1
automation.agent.minimumVersionForClientPIT=4.3.0.2231-1
automation.agent.serverlessLocation=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/atlas-serverless-agent/${env}/
automation.agent.serverlessLocation.atlas=https://internal-downloads.mongodb.com/automation-dev/automation-agent/${env}/
automation.agent.serverlessVersion=1.0.0.7984-1
automation.agent.serverlessMinimumVersion=1.0.0.7984-1
automation.agent.version=13.41.0.9760-1
automation.kmipTool.location=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/automation-agent/${env}/
automation.kmipTool.location.atlas=https://internal-downloads.mongodb.com/automation-dev/automation-agent/${env}/
automation.versions.autoRefreshUri=classpath://mongodb_version_manifest.json
automation.versions.source=remote
automation.versions.atlasDecoupledFromCm=true
avalara.accountNumber=**********
avalara.companyCode.ireland=mongodb-ltd-local
avalara.companyCode.inc=mongodb-local
avalara.licenseKey=<SECRETMANAGER>
avalara.url=https://sandbox-rest.avatax.com
aws.accessKey.dataWarehouse=<SECRETMANAGER>
aws.accesskey=<SECRETMANAGER>
aws.dataWarehouse.bucket=mongodb-datawarehouse-local
aws.dataWarehouse.bucket.prefix=data_dump/cloud/
aws.dataWarehouse.kinesis.accessKey=<SECRETMANAGER>
aws.dataWarehouse.kinesis.secretKey=<SECRETMANAGER>
aws.marketplaceProductCode=9maw2mikw1ksjup1olntbvc6r
aws.marketplaceUrl=https://aws.amazon.com
aws.secretKey.dataWarehouse=<SECRETMANAGER>
aws.secretkey=<SECRETMANAGER>
aws.ses.endpoint=https://email.us-east-1.amazonaws.com
awsGraviton.minimumMongoDBVersion=4.2
local.azure.multiTenantAppId=<SECRETMANAGER>
local.azure.multiTenantAppSecret=<SECRETMANAGER>
baas.centralUrl=http://localhost:9090
baas.legacyCentralUrl=http://localhost:9090
backup.kmip.server.ca.file=server/conf/kmip_test_server_ca.pem
backup.kmip.server.host=ec2-34-225-178-37.compute-1.amazonaws.com
backup.kmip.server.port=5696
biConnector.location=https://info-mongodb-com.s3.amazonaws.com/mongodb-bi/v2/
biConnector.location.atlas=https://internal-downloads.mongodb.org/mongodb-bi/v2/
biConnector.minimumVersion=2.3.1
biConnector.version=2.14.24
atlas.biConnector.version=2.14.24
atlas.api.versioning.rejectFutureDateCalls=false
atlas.api.versioning.enabled=true
atlas.api.versioning.preview=enabled
atlas.api.versioning.upcoming=enabled
backup.wtcheckpoints.blocksavepool.threads.core=8
backup.wtcheckpoints.blocksavepool.threads.max=8
braintree.clientId=<SECRETMANAGER>
braintree.environment=sandbox
braintree.merchantAccountId.inc=<SECRETMANAGER>
braintree.merchantAccountId.ltd=<SECRETMANAGER>
braintree.merchantId=<SECRETMANAGER>
braintree.privateKey=<SECRETMANAGER>
braintree.publicKey=<SECRETMANAGER>
braintree.url=https://sandbox.braintreegateway.com
braintree.maxRequestsPerSecond=20
brs.agent.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/backup-agent/${env}/
brs.agent.minimumVersion=*********-1
brs.agent.version=7.8.1.1109-1
brs.client.pit.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/mongodb-backup-restore-util/${env}/
brs.meterUsage.BatchSize=1000
brs.client.pit.version=13.41.0.9760-1
brs.queryable.lruCacheCapacityMB=512
brs.queryable.mongod.memoryQuotaMB=8192
brs.queryable.proxyDebugPort=26999
brs.queryable.proxyPort=25999
brs.queryable.tls.disabledProtocols=SSLv2Hello,SSLv3,TLSv1,TLSv1.1,TLSv1.3
brs.queryable.tls.disabledCiphers=TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384
brs.queryable.wiredTiger.engineConfig.cacheSizeGB=1
brs.restore.digest.method=SHA1
brs.s3.validation.testing=enabled
brs.snapshot.checkUncleanShutdown=false
brs.snapshot.systemInitiated.enabled=true
brs.snapshotSchedule.interval=6
brs.snapshotSchedule.retention.base=2
brs.snapshotSchedule.retention.daily=7
brs.snapshotSchedule.retention.monthly=13
brs.snapshotSchedule.retention.weekly=4
brs.thirdparty.enablemocks=true
brs.tunnel.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/backup-tunnel/
brs.tunnel.version=13.41.0.9760-1
mms.backup.snapshot.volume.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/snapshot-volume/${env}/
mms.backup.snapshot.volume.version=13.41.0.9760-1
brs.wtc.maxNumBufferedFileBatchesOnAgent=1
charts.api.url=
charts.api.secretKey=
charts.apiSecret=<SECRETMANAGER>
charts.stitch.app.id=chartstenantinfo-ldtvg
charts.cache.encryptedCredentials=
charts.cache.mongoUri=
charts.sample.encryptedCredentials=
charts.sample.mongoUri=
charts.centralUrl=http://localhost:8020
charts.stitch.useLocalDeploymentModel=false
charts.template.aws.accessKey=
charts.template.aws.secretKey=
charts.template.bucketName=
charts.upgrade.poolSize=1
charts.version=0.0.0
clienthub.domain=
clienthub.password=
clienthub.user=
cmabService.urlSuffix=-dev-prod.dsplatform.prod.corp.mongodb.com/predict/cmab
data.delete.queue.size=500
dataWarehouse.dailyDataExport=false
dataWarehouse.hourlyDataExport=false
dataWarehouse.dataExport.nonOverlappingNamespaces=nds.planner.plans,mmsdbconfig.config.alertConfigHistory
datadog.api.eu.url=https://api.datadoghq.eu/api/v1
datadog.api.url=https://app.datadoghq.com/api/v1
datadog.api.us.url=https://app.datadoghq.com/api/v1
datadog.api.us3.url=https://api.us3.datadoghq.com/api/v1
datadog.api.us5.url=https://api.us5.datadoghq.com/api/v1
datadog.api.ap1.url=https://api.ap1.datadoghq.com/api/v1
datadog.api.us1.fed=https://app.ddog-gov.com/api/v1
datadog.api.us.apiKey=<SECRETMANAGER>
datadog.api.us.applicationKey=<SECRETMANAGER>
datadog.api.regions.options=US,US3,US5,EU,AP1,US1_FED
datadog.siteSelection.enabled=true
devHub.centralUrls=https://devhub-local.mongodb.com:8000,https://devhub-ui.devrel.staging.corp.mongodb.com
distributed.cron.svc.registries=com.xgen.svc.mms.svc.cron.MmsCronJobRegistry,com.xgen.svc.nds.svc.AtlasClustersCronJobRegistry,com.xgen.svc.brs.svc.cron.BackupCronJobRegistry,com.xgen.svc.mms.svc.billing.audit.BillingAuditorCronRegistry,com.xgen.svc.mms.svc.billing.cron.BillingCronJobRegistry,com.xgen.svc.mms.svc.billing.cron.PaymentCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportAuditorCronJobRegistry
distributedAlert.executorThreadCount=30
distributedAlert.groupCollateQueueOfferTimeoutMs=1000
distributedAlert.groupCollateQueueSize=100
distributedAlert.groupQueueOfferTimeoutMs=1000
distributedAlert.groupQueueSize=100
distributedAlert.hostQueueSize=100
dyn.customerName=<SECRETMANAGER>
dyn.password=<SECRETMANAGER>
dyn.username=<SECRETMANAGER>
ecosystem.enabled=true
eloqua.siteId=*********
eloqua.sync.enabled=false
executor.thread.count=2
ga.accountId=********
ga.enabled=false
ga.p12file=ga-cloud-manager-analytics-api-nonprod.p12
ga.propertyId=UA-********-1
ga.serviceAccount=<EMAIL>
ga.viewId=*********
gtm.containerId=GTM-5ZD5LD
hipchat.api.message.url.v1=https://api.hipchat.com/v1/rooms
hipchat.api.message.url=https://api.hipchat.com/v2/room
iam.login.postAuth.allowLocalRedirect=true
# CLOUDP-294439: dev/qa setup will also enable this for local
iam.okta.webhook.issuer=https://auth-qa.mongodb.com/oauth2/aus1fvc5ha5ri5WEZ358
iam.okta.webhook.audience=okta-webhook
iam.partnerIntegrations.vercelNative.enabled=true
iam.partnerIntegrations.vercelNative.webhook.clientSecrets=<SECRETMANAGER>
iam.partnerIntegrations.vercelNative.openInProvider.clientId=oac_bvUQ9EgBX9kfYrrvQwlWNjOu
iam.partnerIntegrations.vercelNative.openInProvider.clientSecret=<SECRETMANAGER>
iam.partnerApiCallFilter.enabled=false
intercom.apiKey=<SECRETMANAGER>
intercom.appId=wjeh6he1
intercom.enabled=false
intercom.enabledLoggedInPages=false
intercom.qualtricsSurvey.api.token=
intercom.secretKey=<SECRETMANAGER>
internalatlas.appdb.username=
internalatlas.appdb.password=
internalatlas.ldap.username=
internalatlas.ldap.password=
ipstack.enabled=true
ipstack.api.accessKey=<SECRETMANAGER>
ipstack.api.url=https://api.ipstack.com
jira.password=
jira.username=
job.processor.await.termination.time=60
job.processor.await.termination.unit=SECONDS
job.processor.aws.requests.per.second=10.0
job.processor.azure.requests.per.second=10.0
job.processor.blacklist.refresh.time=0
job.processor.blacklist.refresh.unit=SECONDS
job.processor.core.thread.count=10
job.processor.empty.queue.wait=5000
job.processor.keepalive.time=1
job.processor.keepalive.unit=MINUTES
job.processor.max.thread.count=10
job.processor.server.tags.enabled=false
job.processor.server.tags.refresh.time=5
job.processor.server.tags.refresh.unit=SECONDS
job.processor.partitionQuery.numPartitions=2
job.processor.ping.time=10
job.processor.ping.unit=SECONDS
job.processor.ping.staleCount=6
job.processor.legacyPollingQuery.enabled=false
job.parking.cron.schedule=0 0/5 * 1/1 * ? *
job.parking.cron.limitPerRound=10
job.unparking.cron.schedule=0 0/5 * 1/1 * ? *
job.unparking.cron.limitPerRound=10
local.aws.secretsRegion=us-east-1
local.aws.secretsPrimaryRegion=us-east-1
logCollection.maxDiskSpaceMB=204800
metering.centralUrl=http://localhost:8080
metering.user.publicKey=
metering.user.privateKey=
mlab.clientId=atlas-local
mlab.portalBaseUrl=https://www.mlab.com
mlab.sharedSecret=
mms.accountMultiFactorAuth.enabled=true
iam.accountMultiFactorAuth.smsBackoffLevels.enabled=true
iam.accountMultiFactorAuth.smsBackoffLevels.lockFactorThreshold=3
iam.accountMultiFactorAuth.smsBackoffLevelOne.rateLimitMinutes=1
iam.accountMultiFactorAuth.smsBackoffLevelTwo.rateLimitMinutes=2
iam.accountMultiFactorAuth.smsBackoffLevelThree.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelFour.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelFive.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelSix.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelSeven.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelEight.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelNine.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelTen.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByOne.countryCodes=62,92,880
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByTwo.countryCodes=998,994,93
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByThree.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByFour.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByFive.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.denyList.countryCodes=223
iam.accountMultiFactorAuth.telephonyHook.enabled=true
iam.accountMultiFactorAuth.telephonyHook.percent=100
mms.azure.ssdv2.regions=francecentral,eastasia,canadacentral,koreacentral,norwayeast,polandcentral,southafricanorth,swedencentral,switzerlandnorth,uaenorth,australiaeast,northeurope,centralindia,japaneast,uksouth,brazilsouth,southeastasia,germanywestcentral,southcentralus,westus3,centralus,eastus2,westeurope,eastus,westus2,italynorth,israelcentral,spaincentral,mexicocentral
mms.azure.ssdv2.rollout.regions=
mms.azure.ssdv2.preview.regions=eastus2euap
mms.accountSuspension.enabled=true
mms.accountDeletion.enabled=true
mms.sendAccountDeletionEmailEnabled=true
mms.enableNewAdminUsersPage=true
mms.enableCommentService=true
mms.acme.defaultProvider=LETS_ENCRYPT_V2
mms.acme.tracking.publicKey=<SECRETMANAGER>
mms.acme.tracking.privateKey=<SECRETMANAGER>
mms.addUserApi.countryRequired=true
mms.admin.filterBackupJobs=false
mms.adminEmailAddr=<EMAIL>
mms.agentCentralUrl.override=http://localhost:8080
mms.alertFromEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertFromEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alertReplyToEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertReplyToEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alerts.AutomationAgentDown.maximumPingAgeMinutes=2
mms.alerts.BackingDatabaseProcessDown.connectTimeoutMs=10000
mms.alerts.BackupAgentDown.maximumPingAgeMinutes=1
mms.alerts.BackupDaemonLowFreeHeadSpace.frequency=0 0 * * * ?
mms.alerts.BackupJobTooBusy.frequency=0 0 0 * * ?
mms.alerts.BiConnectorAlert.maximumPingAgeMinutes=3
mms.alerts.ClusterMongosIsMissing.maximumPingAgeMinutes=3
mms.alerts.HostDown.maximumPingAgeMinutes=3
mms.alerts.HostExposed.addToNewGroups=true
mms.alerts.ServerlessProxiesStoppedReportingForMTM.minTenants=3
mms.alerts.ServerlessProxiesStoppedReportingForMTM.maxTenantSample=10
mms.alerts.ServerlessProxyDown.maximumPingAgeMinutes=2
mms.alerts.FlexProxiesStoppedReportingForMTM.minTenants=0
mms.alerts.FlexProxiesStoppedReportingForMTM.maxTenantSample=10
mms.alerts.FlexProxyDown.maximumPingAgeMinutes=7
mms.alerts.MonitoringAgentDown.maximumPingAgeMinutes=2
mms.alerts.RealmDown.maximumPingAgeMinutes=2
mms.alerts.NdsProxyDown.maximumPingAgeMinutes=1
mms.alerts.OplogBehind.maximumOplogPushAgeMinutes=5
mms.alerts.PrepaidPlanMissingSKU.frequency=0 0 7 * * ?
mms.alerts.ReplicaSetAlertCheck.maximumPingAgeMinutes=3
mms.alerts.StalePendingInvoices.frequency=0 0 7 * * ?
mms.alerts.SubscriptionUsageCronJobDidntRunLastHour.frequency=0 0 * * * ?
mms.alerts.SystemDatabaseProcessStartupWarnings.frequency=0 0 * * * ?
mms.alerts.TooManyPendingInvoices.frequency=0 0 7 * * ?
mms.alerts.EncryptionAtRestKMSNetworkAccessDenied.frequency=PT15M
mms.alerts.EncryptionAtRestConfigNoLongerValid.frequency=PT15M
mms.alerts.createSystemAlertsEnabled=false
mms.alerts.delaySeconds=120
mms.alerts.disabledEventTypes=
mms.alerts.intervalSeconds=30
mms.alerts.unreachable.lookBackMinutes=5
mms.alerts.unreachable.minimumConfCount=100
mms.alerts.unreachable.thresholdPercent=90
mms.alerts.webhook.readTimeoutMs=5000
mms.alerts.webhook.socketTimeoutMs=2000
mms.alerts.GlobalServiceAccountSecretExpiredAlertCheck.frequency=PT12H
mms.atlasApiDeprecation.fromEmailAddr=<EMAIL>
mms.atlasApiDeprecation.replyToEmailAddr=<EMAIL>
mms.appsettings.fetchSecretsInBatch=true
mms.allowOpsManagerOrgs=false
mms.apiregistry.cronjob.enabled=false
mms.apiregistry.cronjob.frequency=0 0/30 * * * ?
mms.api.throttling.enabled=false
mms.assets.minified=false
mms.assets.packaged=false
mms.assets.crossOrigin=true
mms.atlas.bastionIps=
mms.atlas.backup.skipBillingOnOplogMigrationDestination=false
mms.atlas.backup.skipBillingOplogMigrationForAzureOplogStore=false
mms.atlas.backup.skipBillingOplogMigrationForGcpOplogStore=false
mms.atlas.defaultReplicaSetScalingStrategy=WORKLOAD_TYPE
mms.atlasAWSMinimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlas.gcp.minimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlasClusterWebsocketConnection.baseUrl=ws://localhost:8888
mms.atlasSearch.tracingSamplerRate=0.0
nds.serverless.sniproxy.default.count=1
mms.auth.logo.fileName=mdb_logo
mms.auth.methods.google.enabled=true
mms.auth.methods.google.oktaIdpId=0oa1bdkv07SQWRRIB357
mms.auth.methods.github.enabled=true
mms.auth.methods.github.oktaIdpId=0oaijfsmclTTzWxCs357
mms.cps.billing.oplogStorageRegionUsage=true
mms.cps.directAttachPreWarm.skip.gcp=true
mms.cps.collectionMetadataBucketRegionName=us-east-1
mms.cps.collectionMetadataBucketRegionNameSecondary=us-east-2
mms.cps.collectionMetadataBucket=atlas-backup-collection-metadata-us-east-1-local
mms.cps.collectionMetadataBucketSecondary=atlas-backup-collection-metadata-us-east-2-local
mms.cps.collectionRestore.diskSpaceThreshold=.90
mms.customer.metrics.exporter.diskingestable.usecaseId=
mms.customer.metrics.exporter.diskingestable.publisherKey=
mms.customer.metrics.exporter.diskingestable.exportPercentage=
mms.authz.enableAuthzSvc=false
mms.authz.roleSetParityCheck.percentage=-1
mms.authz.epaParityCheck.percentage=-1
mms.authz.syncResources=false
mms.authz.syncTeamsToUserGroups=false
mms.authz.mandatoryResourceSync.percentage=-1
mms.userGroupFailureJob.retryDelayInMin=3
mms.userGroupFailureJob.numberRetries=3
mms.authz.fgaOrgIdsCacheDurationSeconds=10
mms.authz.fgaOrgIdsCache.enabled=true
mms.authz.rolesCache.enabled=true
mms.authz.tags.timeoutMillis=100
mms.runFgaOverRbacWhenAnnotated=false
mms.automation.agentFeatures.migrationHosts.canManageDeployments=false
mms.automation.changes.pruning.retentionDays=30
mms.automation.changes.pruning.changesPerSecond=500
mms.automation.changes.pruning.maxPerRun=1000000
mms.automation.changes.watcher.enabled=true
mms.automation.publishable.writeconcern=replicasafederived
mms.automation.settings.agentStatus.sendPeriodSecs=120
mms.automation.settings.agentStatus.maxPlansToSend=5
mms.automation.settings.timeBetweenMongosRollingRestartMs=20000
mms.backup.alertsEmailAddr=<EMAIL>
mms.backup.allowDevelopmentVersions=true
mms.backup.allowFastSchedule=true
mms.backup.allowRefTimeOfDay=false
mms.backup.allowSnapshotExpiryUpdate=true
mms.backup.archive.aws.accesskey=
mms.backup.archive.aws.secretkey=
mms.backup.automatedRestoreExpirationHours=48
mms.backup.blockstore.aws.accesskey=
mms.backup.blockstore.aws.secretkey=
mms.backup.e2e.blockstore.aws.accesskey=<SECRETMANAGER>
mms.backup.e2e.blockstore.aws.secretkey=<SECRETMANAGER>
mms.backup.e2e.oplogstore.aws.accesskey=<SECRETMANAGER>
mms.backup.e2e.oplogstore.aws.secretkey=<SECRETMANAGER>
mms.backup.daemonAssignment.replSetSizeMultiple=1.3
mms.backup.daemonAssignment.replSetSizeMultiplePreferred=1.1
mms.backup.daemonAssignment.usableHeadPercent=0.80
mms.backup.daemonAssignment.usableHeadPreferredPercent=0.95
mms.backup.donate.chosenJob.space.lower.threshold=0.04
mms.backup.donate.chosenJob.space.upper.threshold=0.13
mms.backup.donate.donor.space.threshold=0.85
mms.backup.logSnapshotFileInfo=false
mms.backup.maximumPITRestoreOplogWindowDays=7
mms.backup.minimumOplogWindowHours=0
mms.backup.provisioningRequired=true
mms.backup.region.default=NY_NJ
mms.backup.s3OplogStoreEnabled=true
mms.backup.snapshotValidation.dryRunEnabled=true
mms.backup.snapshotValidation.jobRunnerDeleteInProgressSecrets=true
mms.backup.snapshotValidation.jobRunnerTargetMinikube=true
mms.backup.snapshotValidation.jobRunnerDeleteSecrets=true
mms.backup.snapshotValidation.jobRunnerDeletePersistentVolumeClaims=true
mms.backup.snapshotValidation.jobRunnerMaxAllowedSizeInGb=10
mms.backup.snapshotValidation.kubernetesContext=minikube
mms.backup.snapshotValidation.UseDummyImage=false
mms.backup.snapshotValidation.UseLocalImage=true
mms.backup.snapshotValidation.PinnedImageVersion=
mms.backup.snapshotValidation.IngestionEndpointForDevelopment=http://host.minikube.internal:8080
mms.backup.snapshotValidation.kubernetesRetryLimit=6
mms.backup.snapshotValidation.s3Bucket.prefix=snapshot-validation-local
mms.backup.snapshotValidation.roleArnPrefix=arn:aws:iam::664315256653:role/mms-bgrid-resources-local/bgrid-snapshot-validation-rw-local-aws
mms.backup.enableMultiRegionJobCreation=true
mms.backup.useS3InIntTests=false
mms.backup.useDisableModuleFlowForAtlas=true
mms.backup.disableBackupModuleForClustersStartingFromEpochSeconds=1746124801
mms.billing.alertsEnabled=true
# Start of Billing Anomaly Detection properties
mms.billing.bad.authUrl=https://corp.mongodb.com/oauth2/aus4k4jv00hWjNnps297
mms.billing.bad.baseUrl=https://bad-staging-prod.dsplatform.prod.corp.mongodb.com
mms.billing.bad.scope=dsplatform-staging
mms.billing.bad.clientId=<SECRETMANAGER>
mms.billing.bad.secret=<SECRETMANAGER>
# --- End of Billing Anomaly Detection properties
mms.billing.bccEmailAddress=<EMAIL>
mms.billing.coreBIEmailAddress=<EMAIL>
mms.billing.enabled=true
mms.billing.salescomp.enabled=true
mms.billing.cronJobsDisabled=true
mms.billing.gcp.authProviderX509CertUrl=https://www.googleapis.com/oauth2/v1/certs
mms.billing.gcp.authUri=https://accounts.google.com/o/oauth2/auth
mms.billing.gcp.clientEmail=<EMAIL>
mms.billing.gcp.clientId=<SECRETMANAGER>
mms.billing.gcp.clientX509CertUrl=https://www.googleapis.com/robot/v1/metadata/x509/service-account-officeit-61928%40mdb-gcp-marketplace-test.iam.gserviceaccount.com
mms.billing.gcp.privateKey=<SECRETMANAGER>
mms.billing.gcp.privateKeyId=<SECRETMANAGER>
mms.billing.gcp.projectId=mdb-gcp-marketplace-test
mms.billing.gcp.accountIds=d3806197-e921-4343-aa90-fc287785a82a
mms.billing.gcp.tokenUri=https://oauth2.googleapis.com/token
mms.billing.gcp.type=service_account
mms.billing.gcp.selfServeMetric=mongodb-atlas-self-service-dev.endpoints.mdb-gcp-marketplace-test.cloud.goog/pay_as_you_go_usage
mms.billing.gcp.atlasPrivateMetric=mongodb-atlas-producer-dev.endpoints.mdb-gcp-marketplace-test.cloud.goog/atlas_private_usage
mms.billing.gcp.atlasPrepaidMetric=mongodb-atlas-producer-dev.endpoints.mdb-gcp-marketplace-test.cloud.goog/atlas_prepaid_usage
mms.billing.gcp.selfServeProductName=mongodb-atlas-self-service-dev.endpoints.mdb-gcp-marketplace-test.cloud.goog
mms.billing.gcp.atlasGovAnnualCommitMetric=mongodb-atlas-for-government-private-offer-test.endpoints.mdb-gcp-marketplace-test.cloud.goog/Mongodb_atlas_gov
mms.billing.gcp.atlasGovPrepaidMetric=mongodb-atlas-for-government-private-offer-test.endpoints.mdb-gcp-marketplace-test.cloud.goog/Mongodb_atlas_gov_prepaid_atlas
mms.billing.gcp.marketplaceUrl=https://console.cloud.google.com
mms.billing.aws.mp.accessKey=<SECRETMANAGER>
mms.billing.aws.mp.secretKey=<SECRETMANAGER>
mms.billing.aws.mp.athena.accessKey=<SECRETMANAGER>
mms.billing.aws.mp.athena.secretKey=<SECRETMANAGER>
mms.billing.aws.mp.disbursement.crons.enabled=false
mms.billing.aws.mp.customerIds=7uDMB5LbfdD
mms.billing.aws.mp.productCodes=9maw2mikw1ksjup1olntbvc6r
mms.billing.azure.mp.azureNative.service.marketplaceHost=https://mongodb-billing.gateway-canary.azliftr.io/api/billing
mms.billing.azure.mp.aad.azureNative.service.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.azureNative.service.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.secretKey.expiration=2026-07-10
mms.billing.azure.mp.aad.service.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.service.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.service.secretKey.expiration=2026-07-10
mms.billing.azure.mp.aad.productGraph.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.productGraph.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.productGraph.secretKey.expiration=2024-11-29
mms.billing.azure.mp.corsUrls=https://login.live.com,https://login.microsoftonline.com,https://partner.microsoft.com,https://portal.azure.com
mms.billing.azure.mp.saas.partnerTeam.webhookUrl=https://us-central1-gcp-pov.cloudfunctions.net/azure-marketplace-webhook-nonprod
# TODO:  These next few settings are specific to a storage account I setup for the test.  Later I will create more appropriate and shared namings.
mms.billing.azure.export.api.storage.account.name=benptestbilling2
mms.billing.azure.export.api.client.secretKey=
mms.billing.azure.export.api.blob.container.name=benptestbillingcontainer2
mms.billing.azure.export.api.clientId=f9981470-8d18-4cf5-b1f3-30a6eb374dc9
mms.billing.azure.export.api.tenantId=********-d71e-47f5-92dd-759e272cdc1c
mms.billing.azure.export.destinationS3Bucket=
mms.billing.internalPaymentCheck=false
mms.billing.retrieveStripeEvents=false
mms.billing.selfServePayments.enabled=true
mms.billing.selfServeProducts=seed-data/SelfServeProducts.json
mms.billing.sendInvoiceEmails=false
mms.billing.sfdcProductCodes=seed-data/SalesforceProductCodes.json
mms.billing.sfscSyncEnabled=false
mms.billing.statusPage.apiKey=
mms.billing.dataExportConfig=data-export/data-export-conf.json
mms.billing.auditors.auditEnabled=true
mms.billing.auditors.config=seed-data/BillingAuditorConfigs.json
mms.billing.statusPage.enabled=false
mms.billing.partners.aws.reporting.mode=ALL
mms.billing.partners.azure.reporting.mode=ALL
mms.billing.partners.gcp.reporting.mode=ALL
mms.billing.pricingApiConsumers=
mms.billing.resourceTagRetrievalBatchSize=500
mms.billing.lineItemMaterializedViewJob.enabled=true
mms.billingImport.azure.useLegacyBillingReportDate=true
mms.payments.chargePayments.maxConcurrency=64
mms.payments.chargePayments.invoiceLock.retries.maxAttempts=20
mms.payments.chargePayments.invoiceLock.retries.minBackoff=2000
mms.payments.chargePayments.invoiceLock.retries.maxBackoff=15000
mms.payments.opportunityChange.auth.jira.uri=https://jira-staging.corp.mongodb.com
mms.payments.opportunityChange.auth.jira.privateAccessToken=<SECRETMANAGER>
mms.payments.revrec.maxConcurrency=256
mms.payments.revrec.query.maxConcurrency=40
mms.payments.revrec.query.batchSize=250
mms.payments.revrec.email.selfServe.recipientAddress=<EMAIL>
mms.payments.revrec.email.salesSold.recipientAddress=<EMAIL>
mms.payments.revrec.email.subjectPrefix=
mms.payments.yaypay.ui.baseUrl=https://developer.yaypay.com
mms.payments.yaypay.baseUrl=https://developer.yaypay.com/api/v2
mms.payments.yaypay.inc.clientKey=<SECRETMANAGER>
mms.payments.yaypay.inc.clientSecret=<SECRETMANAGER>
mms.payments.yaypay.inc.scope=ar.read,customer.read,crm.read
mms.payments.yaypay.ltd.clientKey=
mms.payments.yaypay.ltd.clientSecret=
mms.payments.yaypay.ltd.scope=
mms.payments.yaypay.brazil.clientKey=
mms.payments.yaypay.brazil.clientSecret=
mms.payments.yaypay.brazil.scope=
mms.payments.internalNetwork.maxRequestsPerSecond=100
mms.payments.opensanctions.apikey=
mms.payments.opensanctions.maxRetryAttempts=2
mms.payments.vercel.api.maxRetries=3
mms.payments.vercel.api.minBackoffMillis=10
mms.payments.vercel.billing.maxRetries=5
mms.payments.vercel.billing.minBackoffMillis=10
mms.payments.vercel.billing.accessTokenKey=<SECRETMANAGER>
mms.payments.vercel.billingInstallation.ratelimit.enabled=true
mms.payments.vercel.billingInstallation.ratelimit.suspendableIp.maxHitsPerPeriod=6000
mms.payments.vercel.billingInstallation.ratelimit.suspendableIp.minutesPeriod=1
mms.payments.vercel.billingInstallation.ratelimit.pathParam.maxHitsPerPeriod=600
mms.payments.vercel.billingInstallation.ratelimit.pathParam.minutesPeriod=1
mms.payments.vercel.billingInstallation.ratelimit.pathParam.parameterName=installationId
mms.payments.vercel.billingProduct.ratelimit.enabled=true
mms.payments.vercel.billingProduct.ratelimit.suspendableIp.maxHitsPerPeriod=6000
mms.payments.vercel.billingProduct.ratelimit.suspendableIp.minutesPeriod=1
mms.payments.vercel.webhook.ratelimit.enabled=true
mms.payments.vercel.webhook.ratelimit.suspendableIp.maxHitsPerPeriod=6000
mms.payments.vercel.webhook.ratelimit.suspendableIp.minutesPeriod=1
mms.payments.credits.creation.rolloverAmbiguity.email.recipient=<EMAIL>
mms.payments.credits.creation.rolloverAmbiguity.email.replyTo=<EMAIL>
mms.payments.credits.creation.rolloverAmbiguity.email.subjectTemplate=[LOCAL] Rollover ambiguity auto-resolved for opportunity %s
mms.centralUrl.override=http://localhost:8080
mms.centralUrl=http://localhost:8080
mms.charts.exportChartsDashboard.ratelimit.enabled=true
mms.charts.exportChartsDashboard.ratelimit.maxHitsPerPeriod=50
mms.charts.exportChartsDashboard.ratelimit.minutesPeriod=10
mms.charts.importChartsDashboard.ratelimit.enabled=true
mms.charts.importChartsDashboard.ratelimit.maxHitsPerPeriod=50
mms.charts.importChartsDashboard.ratelimit.minutesPeriod=10
mms.cloudManagerEnabled=true
mms.authz-service.client.requestThreads=16
mms.authz-service.futures.requestThreads=16
mms.cron.enabled=true
mms.cron.track.ttlDays=90
mms.dao.capped.apiRequestTrackDao.sizeInBytes=52428800
mms.dao.capped.default.sizeInBytes=1048576
mms.dao.capped.metricAlertDispatchCounterDao.sizeInBytes=10485760
mms.dao.capped.mlabSharedMigrationLogDao.sizeInBytes=52428800
mms.dao.capped.ndsMongoMirrorLogDao.sizeInBytes=52428800
mms.dao.capped.ndsTenantUpgradeLogDao.sizeInBytes=104857600
mms.databaseIdpDiscovery.enabled=true
mms.databaseIdpDiscovery.mongoOktaId=0oawq91yiP5kvFi5f356
mms.dataExplorer.rateLimit.enabled=false
mms.design.enabled=true
mms.docsUrl.nds=https://www.mongodb.com/docs/atlas
mms.docsUrl.search=https://www.mongodb.com/docs/search
mms.docsUrl=https://www.mongodb.com/docs/cloud-manager
mms.email.includeAppEnvName=true
mms.email.includeHostname=true
mms.email.logo.force=false
mms.email.logo.height=35
mms.email.logo.width=140
mms.email.logo=/static/images/logo-mongodb.png
mms.email.recipientFilters=^mms\.servercukes-.*@mongodb\.com$,^ops-manager-team.*@mongodb\.com$,@domain\.com$
mms.email.validation=STRICT
mms.emailDaoClass=com.xgen.cloud.email._private.dao.AwsEmailDao
mms.emailVerificationEnabled=false
mms.event.sns.awsAccessKey=
mms.event.sns.awsSecretKey=
mms.event.event-service.writes.enabled=false
mms.event.event-service.write.sync=false
mms.event.event-service.eventTesting.enabled=true
mms.event.sns.enabled=false
mms.event.errorOnValidationFailure=true
mms.feature.enablePauseFreeTierMonitoring=true
mms.featureFlag.adfa.allowRoutingViaCRMInsteadOfEnvoy=enabled
mms.featureFlag.adl.readConcernMajority=controlled
mms.featureFlag.adl.schemaUniqueFieldLimit=disabled
mms.featureFlag.adl.templateRegexGenerationOptimization=disabled
mms.featureFlag.adl.testManualControlledFlag=disabled
mms.featureFlag.adl.testAutomatedControlledFlag=disabled
mms.featureFlag.adl.testEnabledFlag=disabled
mms.featureFlag.adl.testDisabledFlag=disabled
mms.featureFlag.adl.useMongodInsteadOfMqlrun=enabled
mms.featureFlag.alertStateFlappingDetection=controlled
mms.featureFlag.atlasAllowDeprecatedVersions=controlled
mms.featureFlag.atlasMongoDB80=enabled
mms.featureFlag.atlasAutoApplyIndexesForMTMs=controlled
mms.featureFlag.atlasAzureNVMe=enabled
mms.featureFlag.atlasCrossCloudReplication=enabled
mms.featureFlag.atlasCrossRegionPeering=controlled
mms.featureFlag.atlasDataFederationTemplatedOnboarding=enabled
mms.featureFlag.atlasDataFederationAzureFeedDownstreamSystems=controlled
mms.featureFlag.atlasDataFederationAzurePrivateLink=controlled
mms.featureFlag.atlasDeploysUIS=disabled
mms.featureFlag.atlasSearchResolveViews=controlled
mms.featureFlag.atlasGatewayProxy=controlled
mms.featureFlag.atlasMaintenanceWindows=controlled
mms.featureFlag.atlasMountAzureDiskWithLun=controlled
mms.featureFlag.atlasAzureForceMigrationToAZs=controlled
mms.featureFlag.atlasServerlessGRPCSupport=enabled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettings=controlled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettingsOrg=controlled
mms.featureFlag.atlasProxyDisableRateLimiting=controlled
mms.featureFlag.atlasServerlessUsesServerlessAgent=controlled
mms.featureFlag.atlasEnableTestCommands=controlled
mms.featureFlag.atlasSharedTierX509Auth=controlled
mms.featureFlag.atlasX509CRL=controlled
mms.featureFlag.atlasSearch.dedicatedNodes=enabled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreference=controlled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreferenceGroupOverride=controlled
mms.featureFlag.atlasSearch.synchronousSteadyStateReplication=controlled
mms.featureFlag.atlasSearch.indexManagement=enabled
mms.featureFlag.atlasSearch.indexPartitioningUI=controlled
mms.featureFlag.atlasSearch.vibCustomAnalyzersUI=enabled
mms.featureFlag.atlasSearch.confCallViaSearchGateway=enabled
mms.featureFlag.atlasSearch.disableDetailedStatusWrite=controlled
mms.featureFlag.atlasSearch.searchTesterQueryTemplates=controlled
mms.featureFlag.atlasSearch.searchIndexStatusReporting=controlled
mms.featureFlag.atlasSearch.dedicatedNodesAWSSeriesSeven=controlled
mms.featureFlag.atlasSearch.dedicatedNodesEnableReplicationCompression=controlled
mms.featureFlag.atlasSearch.dedicatedNodesForceDisableReplication=controlled
mms.featureFlag.atlasSearch.dedicatedNodesDisableProvisionTimeout=controlled
mms.featureFlag.atlasSearch.useAggregateCommand=enabled
mms.featureFlag.atlasSearch.multipleSubIndexes=controlled
mms.featureFlag.atlasSearch.staleIndexes=enabled
mms.featureFlag.atlasSearch.byok=controlled
mms.featureFlag.atlasSearch.useLifecycleManager=enabled
mms.featureFlag.atlasSearch.envoy.disableStreamIdleTimeout=controlled
mms.featureFlag.atlasSearch.envoy.loadBalancingPolicyRandom=controlled
mms.featureFlag.atlasSearch.indexConfigStatsWrites=disabled
mms.featureFlag.atlasSearch.searchHostStatsWrites=disabled
mms.featureFlag.atlasSearch.readIndexStatsFromNewCollection=disabled
mms.featureFlag.atlasSearch.enableTextOperatorNewSynonymsSyntax=controlled
mms.featureFlag.atlasSearch.indexConfigUpdater=disabled
mms.featureFlag.atlasSearch.disableMongotJvmNativeAccess=enabled
mms.featureFlag.atlasSearch.indexQuantizationUI=controlled
mms.featureFlag.atlasSearch.mongotPushBasedLogExportDownload=disabled
mms.featureFlag.atlasSearch.dedicatedNodesGrpcMode=controlled
mms.featureFlag.atlasDataLakeStorageForOnlineArchive=disabled
mms.featureFlag.atlasDataLakeStorageForceV2UpgradeForOnlineArchive=controlled
mms.featureFlag.atlasDataLakeIngestionPipelinesDeprecation=enabled
mms.featureFlag.atlasDataLakeStorageAllowHourlyIngestion=controlled
mms.featureFlag.atlasDataLakeStorageDatasetRetentionPolicy=controlled
mms.featureFlag.atlasDataFederationDedicatedHostnames=enabled
mms.featureFlag.atlasDataFederationOnGCP=enabled
mms.featureFlag.atlasCloudProviderAccessForGCP=enabled
mms.featureFlag.atlasOnlineArchiveV3TimeSeries=enabled
mms.featureFlag.atlasOnlineArchiveGCP=enabled
mms.featureFlag.atlasOnlineArchiveV1=controlled
mms.featureFlag.atlasOnlineArchiveAsDataSource=controlled
mms.featureFlag.atlasOnlineArchiveIgnoreDataSizeThreshold=controlled
mms.featureFlag.atlasOnlineArchiveEncryption=controlled
mms.featureFlag.atlasOnlineArchivePinToPrimaryRegion=controlled
mms.featureFlag.atlasPushBasedLogExport=enabled
mms.featureFlag.atlasPushBasedLogExportClusterLevel=controlled
mms.featureFlag.atlasGovGcpAssuredWorkloads=disabled
mms.featureFlag.atlasUseDataExplorerService=controlled
mms.featureFlag.atlasUseProviderSubdomains=controlled
mms.featureFlag.atlasUseLegacyHostnameScheme=controlled
mms.featureFlag.dbAccessDuringTenantUpgradeBlocked=enabled
mms.featureFlag.dbAccessDuringTenantUpgradeAllowed=controlled
mms.featureFlag.atlasMultiCloudGlobalClusters=enabled
mms.featureFlag.atlasMaintenanceAutoDeferral=controlled
mms.featureFlag.atlasCNRegionsOnly=controlled
mms.featureFlag.atlas50Cluster=enabled
mms.featureFlag.atlasChurnSurvey=enabled
mms.featureFlag.atlasHideQuickAccessPage=controlled
mms.featureFlag.atlasDeferServerNodeTypeTagUntilPhase2=enabled
mms.featureFlag.atlasDailyBigQueryBilling=enabled
mms.featureFlag.atlasHaltGroupLogIngestion=controlled
mms.featureFlag.atlasForceSkipFastProvision=controlled
mms.featureFlag.atlasAutoRetrieveDraftClusterConfig=enabled
mms.featureFlag.awsGraviton=enabled
mms.featureFlag.awsIntelOverGraviton=controlled
mms.featureFlag.gcpInstanceOSAL2=enabled
mms.featureFlag.singleTargetServerlessDeployment=controlled
mms.featureFlag.autoIndexing=controlled
mms.featureFlag.allowVulnerabilityScanning=disabled
mms.featureFlag.automation.changeListener=enabled
mms.featureFlag.automation.configPublishChangeEvent=enabled
mms.featureFlag.automation.configForAgentReturnsOnlyLocal=enabled
mms.featureFlag.automation.chefConfigChangeEvent=enabled
mms.featureFlag.automation.exposeEditorAutofill=enabled
mms.featureFlag.automation.mongoDevelopmentVersions=enabled
mms.featureFlag.automation.sentry=disabled
mms.featureFlag.automation.staggerMongosRollingRestart=controlled
mms.featureFlag.automation.newDeploymentServerUI=enabled
mms.featureFlag.automation.rollingIndexes=controlled
mms.featureFlag.automation.verifyDownloads=controlled
mms.featureFlag.automation.proxyConfigChangeEvent=disabled
mms.featureFlag.automation.enableV6=enabled
mms.featureFlag.automation.enableV7=enabled
mms.featureFlag.automation.enableV8=enabled
mms.featureFlag.automation=controlled
mms.featureFlag.automation.enableRamiAgent=controlled
mms.featureFlag.automation.enableMongotune=enabled
mms.featureFlag.mongotune.enableWriteBlockPolicy=enabled
mms.featureFlag.payments.partners.aws.usageReportCreation=controlled
mms.featureFlag.payments.partners.azure.usageReportCreation=controlled
mms.featureFlag.payments.partners.gcp.usageReportCreation=controlled
mms.featureFlag.payments.salesSoldWarningOrgPaymentStatus=enabled
mms.featureFlag.payments.standaloneCalls.revRec=enabled
mms.featureFlag.awsPrivateEndpoint.waitingForUser=controlled
mms.featureFlag.baas=enabled
mms.featureFlag.baas.billingMigration=controlled
mms.featureFlag.baas.hostingDeprecated=controlled
mms.featureFlag.baas.graphqlDeprecated=controlled
mms.featureFlag.backup.allowEditOplogWindow=controlled
mms.featureFlag.backup.allowWhitelists=enabled
mms.featureFlag.backup.continuousBackupAllowedForNewAWSClusters=controlled
mms.featureFlag.backup.cpsResurrectWithRetainedBackups=enabled
mms.featureFlag.backup.cpsBackupLockMVP=controlled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGA=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGADisablePolicy=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicy2PersonDisablement=controlled
mms.featureFlag.backup.cpsConcurrentSnapshots=enabled
mms.featureFlag.backup.cpsDirectAttach=enabled
mms.featureFlag.backup.cpsDirectAttachOff=controlled
mms.featureFlag.backup.cpsEmbeddedConfig=enabled
mms.featureFlag.backup.cpsEmbeddedConfigUi=enabled
mms.featureFlag.backup.cpsExtendedSnapshotRetry=controlled
mms.featureFlag.backup.cpsExtraOpenBackupCursorTimeout=controlled
mms.featureFlag.backup.useNoWholeFileRsync=controlled
mms.featureFlag.backup.cpsNoWholeFileRsyncOff=controlled
mms.featureFlag.backup.cpsRestoreSearchIndex=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAws=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsMigration=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsNewCMK=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectGCP=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAzure=enabled
mms.featureFlag.backup.cpsOptimizedDaRestoreAws=controlled
mms.featureFlag.backup.cpsOptimizedDaRestoreAzure=controlled
mms.featureFlag.backup.cpsOptimizedDaRestoreGcp=controlled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAws=enabled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAzure=enabled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapGcp=enabled
mms.featureFlag.backup.cpsAwsDaRestoreWithIo2=controlled
mms.featureFlag.backup.cpsDownloadEncryptedSnapshot=enabled
mms.featureFlag.backup.cpsGcpAndAzureNewClustersOnlyCps=enabled
mms.featureFlag.backup.cpsPv2InstantRestoreOff=controlled
mms.featureFlag.backup.cpsPv2StreamingRestore=enabled
mms.featureFlag.backup.cpsSnapshotExportHighFrequency=controlled
mms.featureFlag.backup.cpsSnapshotExportUi=controlled
mms.featureFlag.backup.cpsSnapshotExportAzure=enabled
mms.featureFlag.backup.cpsSnapshotConsistentExport=controlled
mms.featureFlag.backup.cpsSnapshotConsistentExportSplitLargeCollections=enabled
mms.featureFlag.backup.cpsSnapshotDistributionAws=enabled
mms.featureFlag.backup.cpsSnapshotDistributionGcp=enabled
mms.featureFlag.backup.cpsSnapshotDistributionAzure=enabled
mms.featureFlag.backup.cpsSnapshotDistributionUi=enabled
mms.featureFlag.backup.cpsSnapshotDistributionLargeRegionSet=enabled
mms.featureFlag.backup.cpsGcpIncrementalCopySnapshots=controlled
mms.featureFlag.backup.cpsSnapshotAWSPrivateDownload=enabled
mms.featureFlag.backup.cpsSnapshotAzurePrivateDownload=enabled
mms.featureFlag.backup.turnCpsSnapshotAWSPrivateDownloadOff=controlled
mms.featureFlag.backup.turnCpsSnapshotAzurePrivateDownloadOff=controlled
mms.featureFlag.backup.cpsSkipSystemClusterDestroy=controlled
mms.featureFlag.backup.cpsSystemProjectsForExports=enabled
mms.featureFlag.backup.cpsSystemProjectsForDataLakeIngestionPipelineExports=controlled
mms.featureFlag.backup.cpsOplogInGcp=enabled
mms.featureFlag.backup.cpsOplogInGcpExtendMigration=disabled
mms.featureFlag.backup.cpsOplogMigration=enabled
mms.featureFlag.backup.cpsOplogInAzure=enabled
mms.featureFlag.backup.cpsSuccessiveUpgradeQuarterly=enabled
mms.featureFlag.backup.cpsLegacyBackupMigration=enabled
mms.featureFlag.backup.incrementalWtEnabled=controlled
mms.featureFlag.backup.incrementalWtFullSnapshotDayOfWeek=controlled
mms.featureFlag.backup.multipleWorkersPerFile=enabled
mms.featureFlag.backup.s3blockstore.calculateMD5=controlled
mms.featureFlag.backup.s3oplogstore.calculateMD5=controlled
mms.featureFlag.backup.parallelRestores=controlled
mms.featureFlag.backup.directS3Restore=controlled
mms.featureFlag.backup.queryable=enabled
mms.featureFlag.backup.queryableFsCache=enabled
mms.featureFlag.backup.s3OplogStoreInOm=controlled
mms.featureFlag.backup.storageEngine=enabled
mms.featureFlag.backup.thirdPartyManaged=controlled
mms.featureFlag.backup.thirdPartyWithManagedOplog=controlled
mms.featureFlag.backup.v2=controlled
mms.featureFlag.backup.wt.queryable=enabled
mms.featureFlag.backup.wtBackpressure=controlled
mms.featureFlag.backup.wtConcurrentGrooms=enabled
mms.featureFlag.backup.wtConcurrentMongoBlockstoreGrooms=disabled
mms.featureFlag.backup.wtEncryptionAtRest=controlled
mms.featureFlag.backup.wtLocalKeyFile=disabled
mms.featureFlag.backup.wtLocalKeyFile.forOrg=controlled
mms.featureFlag.backup.wtNamespaceFiltering=enabled
mms.featureFlag.backup.enableAWSPrivateLinkOption=controlled
mms.featureFlag.backup.optimalSuccessiveUpgradeForRestoreEnabled=controlled
mms.featureFlag.backup.pGzipEnabled=enabled
mms.featureFlag.backup.snapshot.onDemand=enabled
mms.featureFlag.biConnector=controlled
mms.featureFlag.charts=enabled
mms.featureFlag.chartsActivationOptimization=controlled
mms.featureFlag.classicApiAccess=controlled
mms.featureFlag.classicCharts=controlled
mms.featureFlag.classicDashboard=controlled
mms.featureFlag.clientMetadataCollection=enabled
mms.featureFlag.clusterReferencesAutocorrect=enabled
mms.featureFlag.commentServiceEnabled=controlled
mms.featureFlag.crossOrgBilling=enabled
mms.featureFlag.customerFederation=enabled
mms.featureFlag.pagerduty.includeMaintenanceWindowStatus=controlled
mms.featureFlag.dataExplorer=controlled
mms.featureFlag.dataExplorerAggregation=controlled
mms.featureFlag.dataExplorerCrud=controlled
mms.featureFlag.dataExplorerMultiTenant=controlled
mms.featureFlag.extendMaxAllowedDiskSizes=disabled
mms.featureFlag.hostMappingsAutocorrect=controlled
mms.featureFlag.installAgentScript=controlled
mms.featureFlag.streamsAuditLogs=enabled
mms.featureFlag.streamsEnabled=enabled
mms.featureFlag.managedSlowMs=controlled
mms.featureFlag.m10ShardedClusters=enabled
mms.featureFlag.mongoDBAccessHistory=controlled
mms.featureFlag.monitoring.dataDogMetrics=controlled
mms.featureFlag.monitoring.dataDogDbAndCollMetrics=controlled
mms.featureFlag.monitoring.newRelic=enabled
mms.featureFlag.monitoring.shardedClusterNamespaceMetrics=enabled
mms.featureFlag.monitoring.namespaceQueryLatencyMetrics=controlled
mms.featureFlag.monitoring.skipUpdateClustersDuringLiveMigration=controlled
mms.featureFlag.monthlyUsageMetricsEmail=controlled
mms.featureFlag.ndsClusters=controlled
mms.featureFlag.billingDistributedLinkedOrgBilling=enabled
mms.featureFlag.billing.testMeterDataGeneration=controlled
mms.featureFlag.billingClusterTaggingExport=enabled
mms.featureFlag.disablePremiumDatadog=controlled
mms.featureFlag.serverlessAutoIndexing=enabled
mms.featureFlag.enableFineGrainedAuth=controlled
mms.featureFlag.fineGrainedAuth.userGroups=controlled
mms.featureFlag.serviceAccountManagement=enabled
mms.featureFlag.enableScramSha256Auth=controlled
mms.featureFlag.maxAggregatedDiskChartsForAtlas=controlled
mms.featureFlag.opLatency=enabled
mms.featureFlag.opsManagerConfigForGlobalMonitoringAdmin=enabled
mms.featureFlag.paBugsAndSustainability=controlled
mms.featureFlag.paFeedback=controlled
mms.featureFlag.performanceAdvisor=controlled
mms.featureFlag.historicalReplicaState=controlled
mms.featureFlag.profilerNDS=controlled
mms.featureFlag.profilerv2=controlled
mms.featureFlag.profilerv3=controlled
mms.featureFlag.prometheus=enabled
mms.FeatureFlag.prometheus.allowCustomerRateLimitConfig=controlled
mms.featureFlag.realtime=controlled
mms.featureFlag.realmMetrics=enabled
mms.featureFlag.realmMetricsAlerts=enabled
mms.featureFlag.rsyncBasedHeadCreation=disabled
mms.featureFlag.schemaAdvisor=controlled
mms.featureFlag.segmentGroupSynchronizer=controlled
mms.featureFlag.selfServeConsultingUnits=enabled
mms.featureFlag.standardFreeTier=controlled
mms.featureFlag.teams=enabled
mms.featureFlag.unknown=controlled
mms.featureFlag.atlasContinuousDelivery=enabled
mms.featureFlag.realmEvents=enabled
mms.featureFlag.realm.syncBeta=enabled
mms.featureFlag.failedRollingIndexCleanup=enabled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToSingleShard=controlled
mms.featureFlag.atlasLiveMigrateShardedHorizons=controlled
mms.featureFlag.atlasLiveMigrateOlderVersionSupport=enabled
mms.featureFlag.atlasLiveMigrateOneNinePreviewSupport=disabled
mms.featureFlag.atlasLiveMigrateLiftOplogLagRestriction=controlled
mms.featureFlag.atlasLiveMigrateRunsOnlyOnAL2Hosts=enabled
mms.featureFlag.atlasMigrationHubMilestoneOne=enabled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToShardedCluster=enabled
mms.featureFlag.atlasLiveMigrateMongosyncPushBasedReplicasetToShardedCluster=enabled
mms.featureFlag.atlasPushLiveMigrationsMongosyncPrivateEndpoints=enabled
mms.featureFlag.atlasLiveMigrateMongosyncVariableShardCount=enabled
mms.featureFlag.atlasLiveMigrateMongosyncEmbeddedVerifier=enabled
mms.featureFlag.atlasLiveMigrateDestinationOplogValidationForVerifier=enabled
mms.featureFlag.optOutFromKinesis=controlled
mms.featureFlag.performanceAdvisorRecommendSearch=controlled
mms.featureFlag.uiAccessList=controlled
mms.featureFlag.tokenizedQueryShapeStatsMetrics=controlled
mms.featureFlag.atlasSearchQueryTelemetry=controlled
mms.featureFlag.randomSamplingSlowLogStreamer=enabled
mms.featureFlag.datadogCustomEndpoint=controlled
mms.featureFlag.automationChangesWebClient=disabled
mms.featureFlag.resourceTagComponent.clusters=disabled
mms.featureFlag.resourceTagComponent.projects=disabled
mms.featureFlag.enableExperimentHoldouts=enabled
mms.featureFlag.iaSlowLogsReads=controlled
mms.featureFlag.allowGCPPreviewRegions=disabled
mms.featureFlag.allowAWSPreviewRegions=disabled
mms.featureFlag.allowAzurePreviewRegions=disabled
mms.featureFlag.atlasDedicatedBackboneToReact=enabled
mms.featureFlag.atlasDedicatedReactClusterDetails=controlled
mms.featureFlag.encryptionAtRestAzureKeyVaultPrivateEndpoint=enabled
mms.featureFlag.encryptionAtRestAwsKmsPrivateEndpoint=enabled
mms.featureFlag.earGcpKmsRoleBasedAuth=disabled
mms.featureFlag.atlasAllowAtlasAdminFourFourPrivileges=controlled
mms.featureFlag.connectionEstablishmentRateLimiting=enabled
mms.federation.restrictOrgMembership.default=false
mms.federation.bypassSsoDomains=
mms.enableOidcIdpNonAtlas=true
mms.featureFlag.privateLinkProxyProtocolAws=controlled
mms.featureFlag.privateLinkMaxIncomingConnectionsMultiplier=controlled
mms.fromEmailAddr=MongoDB Cloud <<EMAIL>>
mms.generativeAI.compass.enabled=true
mms.generativeAI.compass.percentAccessEnabled=100.0
mms.generativeAI.dataExplorer.enabled=true
mms.globalRoles.mapping=default
mms.group.tags.edit.roleRequired=GLOBAL_MONITORING_ADMIN
mms.group.tags.read.roleRequired=GLOBAL_READ_ONLY
mms.group.useSoftDelete=false
mms.groupType.default=CLOUD
mms.helpAndSupportPage.enabled=true
mms.https.ClientCertificateMode=none
mms.kinesis.parseHardwarePingPayload.enabled=true
mms.kinesis.parseFTSPingPayload.enabled=true
mms.kubernetes.enabled=true
mms.kubernetes.templates.baseFilePathRequiredForCustomTemplates=/mongodb-ops-manager
mms.kubernetes.templates.defaultCredentialsFilePath=classpath://kubernetes-template-credentials.yaml.hbs
mms.kubernetes.templates.defaultProjectFilePath=classpath://kubernetes-template-project.yaml.hbs
mms.limits.maxAlertConfigsPerGroup=250
mms.limits.maxApiUsersPerOrg=500
mms.limits.maxGroupsPerOrg=250
mms.limits.maxGroupsPerUser=500
mms.limits.maxNotificationsPerAlert=25
mms.limits.maxOrgsPerUser=250
mms.limits.maxTeamsPerGroup=100
mms.limits.maxTeamsPerOrg=250
mms.limits.maxTeamsPerUser=100
mms.limits.maxUsersPerGroup=500
mms.limits.maxUsersPerOrg=500
mms.limits.maxUsersPerTeam=250
mms.limits.maxExportsPerGroup=100
mms.limits.maxM0sPerUnverifiedOrg=10
mms.limits.maxServiceAccountsPerOrg=500
mms.limits.maxCustomPoliciesPerOrg=100
mms.limit.maxNetworkAddressesPerServiceAccount=200
mms.limit.groupSearchMaxNumOfReturnAllowed=3000
mms.logcollection.s3Bucket.accessKey=<SECRETMANAGER>
mms.logcollection.s3Bucket.secretKey=<SECRETMANAGER>
mms.logCollectionJob.defaultExtensionDays=30
mms.login.newDeviceNotification.enabled=false
mms.marketing.sync=false
mms.migration.path.verify=false
maas.ingestion.usecase.336c2b2f-e495-5e7d-9a9c-9e6f492fa157.adminKey=
mms.monitoring.agent.session.timeoutMillis=90000
mms.monitoring.agent.standbyCollectionFactor=2
mms.monitoring.agent.intervals.collLatencyStatsIntervalMillis=600000
mms.monitoring.ingestion.namespacestats.roundModTimeMillis=1800000
mms.featureFlag.monitoring.canonicalHostsTTLFilteredReads.enabled=enabled
mms.featureFlag.monitoring.canonicalHostsTTLWrites.enabled=enabled
mms.monitoring.canonicalHostsTTLMinutes=1440
mms.monitoring.canonicalHostsTTLWrites.probabilisticPercentage=0
mms.monitoring.canonicalHostsTTLWrites.guaranteedPercentage=50
mms.monitoring.chartDomain=
mms.monitoring.historicalReplicaState.ingestionPercentage=100
mms.monitoring.tokenization.checkOtherKeysThreshold=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__discovery_ping_dedicated__dev.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__discovery_ping_dedicated__dev.desiredGranularityMillis=60000
mms.monitoring.kinesis.datawarehouse.cloud__intel__fts_ping__dev.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__hardware_ping__dev.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__ingestion_ping_dedicated__dev.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__ingestion_ping_dedicated__dev.desiredGranularityMillis=60000
mms.monitoring.kinesis.datawarehouse.cloud__intel__ingestion_ping_shared__dev.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__ingestion_ping_shared__dev.desiredGranularityMillis=60000
mms.monitoring.kinesis.datawarehouse.cloud__intel__serverless_ping__dev.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__collstatslatency_ping__dev.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__collstatslatency_ping__dev.desiredGranularityMillis=60000
mms.monitoring.kinesis.datawarehouse.cloud__intel__collstatsstorage_ping__dev.groupPercentageAllowed=100
mms.monitoring.kinesis.datawarehouse.cloud__intel__collstatsstorage_ping__dev.desiredGranularityMillis=60000
mms.monitoring.kinesis.datawarehouse.cloud__intel__query_shapes_ping__dev.groupPercentageAllowed=100
mms.monitoring.ingestion.bucketedMetricRollup.enabled=true
mms.monitoring.ingestion.bucketedMetricRollup.bucketCount=4
mms.monitoring.ingestion.bucketedMetricRollup.serverlessBucketCount=2
mms.monitoring.ingestion.bucketedMetricRollup.realmBucketCount=2
mms.monitoring.ingestion.kinesis.enabled=true
mms.monitoring.ingestion.kinesis.streams.hardware.enabled=true
mms.monitoring.ingestion.kinesis.streams.fts.enabled=true
mms.monitoring.ingestion.kinesis.streams.serverless.enabled=true
mms.monitoring.ingestion.kinesis.streams.collStatsLatency.enabled=true
mms.monitoring.ingestion.kinesis.streams.dedicated.ingestion.enabled=true
mms.monitoring.ingestion.kinesis.streams.dedicated.discovery.enabled=true
mms.monitoring.ingestion.kinesis.streams.dedicated.tokenizedQueryShapeStats.enabled=true
mms.monitoring.ingestion.kinesis.streams.collStatsStorage.enabled=true
mms.monitoring.ingestion.kinesis.streams.shared.ingestion.enabled=true
mms.monitoring.ingestion.hostping.poolsize=2
mms.monitoring.ingestion.newrelic.guid=com.mongodb.monitoring.integrations.newrelic.cloudmanager.dev
mms.monitoring.ingestion.newrelic.version=1.0.0
mms.monitoring.performanceadvisor.iterative.maxLogLines=200000
mms.monitoring.performanceadvisor.skipTimeThreshold=true
mms.monitoring.performanceadvisor.textQueryThreshold=300
mms.monitoring.performanceadvisor.serverless.autoindexing.cronJobEnabled=true
mms.monitoring.performanceadvisor.tracking.indexStats.cronJobEnabled=true
mms.monitoring.performanceadvisor.tracking.indexStats.cronJobScheduleMinutes=2
mms.monitoring.performanceadvisor.tracking.indexStats.cronTimeDeltaInMinutes=1
mms.monitoring.performanceadvisor.tracking.listIndexes.cronJobEnabled=true
mms.monitoring.performanceadvisor.tracking.listIndexes.cronJobScheduleMinutes=2
mms.monitoring.performanceadvisor.tracking.listIndexes.cronTimeDeltaInMinutes=1
mms.monitoring.performanceadvisor.tracking.overrideBuckets=true
mms.monitoring.performanceadvisor.tracking.willStoreIndexes=true
mms.monitoring.realm.enabled=false
mms.monitoring.realm.metrics.api.url=https://realm-dev.mongodb.com/api/private/v1.0/app_metrics
mms.monitoring.realm.requestThreads=2
mms.monitoring.realm.realmMetricPages1Min=2
mms.monitoring.rrd.collStatsLatencyMaintenanceEnabled=true
mms.monitoring.rrd.rawdbperf.thread.count=2
mms.monitoring.rrd.rawdbstorage.thread.count=2
mms.monitoring.rrd.rawhost.thread.count=2
mms.monitoring.rrd.rawmunin.thread.count=2
mms.monitoring.rrd.rawnamespace.thread.count=2
mms.monitoring.rrd.realmMaintenanceEnabled=true
mms.monitoring.rrd.realmNumInitialChunks=10
mms.monitoring.rrd.serverlessMaintenanceEnabled=true
mms.monitoring.rrd.ftsMaintenanceEnabled=true
mms.monitoring.rrd.ftsNumInitialChunks=10
mms.monitoring.slowlogs.ingestion.parsingPercentage=0
mms.monitoring.slowlogs.rateLimit.linesPerTenantShared=10000000
mms.monitoring.slowlogs.rateLimit.linesPerTenantServerless=10000000
mms.monitoring.collStatsStorage.enabled=true
mms.monitoring.collStatsStorage.collectionIntervalMillis=10000
mms.monitoring.tokenizedQueryShapeStatsMetrics.collectionIntervalMillis=60000
# MongoDB versions >= 7.1 have tokenized query shape stats automatically enabled by the agent, so we only need to list out versions and rollout percentages before 7.1
mms.monitoring.tokenizedQueryShapeStatsMetrics.rollout.versions=6.0.20,7.0.16,7.0.17
mms.monitoring.tokenizedQueryShapeStatsMetrics.rolloutPercentage.version.6.0=100
mms.monitoring.tokenizedQueryShapeStatsMetrics.rolloutPercentage.version.7.0=100
mms.monitoring.serverlessProfilingFilter.enabled=true
mms.monthlyUsageMetricsEmail.useMinuteBucket=false
mms.multiFactorAuth.allowReset=true
mms.multiFactorAuth.issuer=MongoDB-LOCAL
mms.multiFactorAuth.level=OPTIONAL
mms.multiFactorAuth.encouragement.enabled=true
mms.multiFactorAuth.encouragement.enabledForNoFactors=true
iam.multiFactorAuth.requiredAfterRegistration=false
iam.multiFactorAuth.requiredForAllUsers=false
iam.multiFactorAuth.rememberDevice.enabled=true
mms.personalizationWizardEnabled=true
mms.personalizationWizardRedirectEnabled=true
mms.darkMode.account.enabled=true
mms.darkMode.preview.enabled=true
mms.darkMode.default=LIGHT
mms.cloudNav.override.enabled=false
mms.ofac.supportEmailAddr=<EMAIL>
mms.plan.default=FREE_TIER
mms.publicApi.allowUnauthRegistrations=true
mms.publicApi.globalKeyRoleDenylist=GLOBAL_OWNER
mms.publicApi.maxTempKeys=10000
mms.publicApi.metric.rateLimitGranularity=1
mms.publicApi.metric.rateLimitMaxPerGroup=100
mms.publicApi.performanceAdvisor.rateLimitGranularity=1
mms.publicApi.performanceAdvisor.rateLimitMaxPerGroup=100
mms.publicApi.queryShapeInsights.rateLimitGranularity=1
mms.publicApi.queryShapeInsights.rateLimitMaxPerGroup=10
mms.publicApi.dataExplorer.rateLimitGranularity=1
mms.publicApi.dataExplorer.rateLimitMaxPerGroup=100
mms.pushLiveMigrations.enabled=true
mms.pushLiveMigrations.mongoClient.validations.enabled=true
mms.search.envoy.location=https://internal-downloads.mongodb.com/search-envoy/
mms.search.envoy.version=********-1.33.2-1-g37faa36
mms.search.xds.tlsRequired=false
mms.search.gateway.tlsRequired=false
mms.search.offlineUpgradePackages=1.38.1.2120
mms.search.offlineDowngradePackages=1.41.0
mms.search.commonLabelsEnabledTiers=M10
mms.search.staleEnabledTiers=MTM
mms.search.incubatorVectorEnabledTiers=ALL
mms.search.apiRateLimitRules=default:2
mms.liveImport.hoursUntilCutOverExpiration=2
mms.liveImport.hoursUntilCutOverExpirationWarning=1
nds.liveImport.mongosync.version=1.15.0
nds.liveImport.mongosync.latestVersion=1.15.0
nds.pushLiveImport.mongosync.version=1.13.1
nds.liveImport.mongomirror.version=0.13.1
nds.liveImport.mongomirror.dockerImageTag=v0.13.1-************-80adf99
nds.liveImport.kubeResourceCleaner.enabled=false
nds.liveImport.mongosyncWorkingDir=/tmp/mongosync
nds.liveImport.mongosync.numInsertersPerPartition=4
nds.liveImport.mongosync.maxNumParallelPartitions=4
nds.liveImport.mongomirror.oplogBatchSize=1000
nds.mongosync.log.verbosity=DEBUG
mms.remoteIp.header=X-Forwarded-For
mms.replyToEmailAddr=<EMAIL>
mms.replyToSuspensionAdminEmailAddr=<EMAIL>
mms.root.redirect=/account/login
mms.security.allowCORS=true
mms.security.backdoor=true
mms.security.cors.allow.localhost.origin=true
mms.security.disableBrowserCaching=false
mms.security.enforceLastAuthMins=false
mms.security.globalWhitelist.cacheUpdateFrequency=PT24H
mms.security.globalWhitelist=seed-data/GlobalWhitelistDao-local.json
mms.security.hstsMaxAgeSeconds=0
mms.security.redactSecretsInStoredMessages=false
mms.security.requireCSRFToken=true
mms.server.sentry.dsn=<SECRETMANAGER>
mms.server.sentry.enabled=false
mms.server.sentry.sampleRate=1.0
mms.server.sentry.apiKey=<SECRETMANAGER>
mms.server.sentry.webhook.threads.core=2
mms.server.sentry.webhook.threads.max=5
mms.serverless.xDSServer.enabled=false
mms.serverless.xDSServer.tlsRequired=false
mms.serverless.xds.error.log.alert.test.enabled=false
mms.session.maxConcurrent=100
mms.session.maxHours=1440
mms.session.customSessionTimeouts.enabled=false
mms.sharedworkerpool.poolsize=4
mms.siteFullName.nds=MongoDB Atlas
mms.siteName.nds=Atlas
mms.snapshotQueryHost=localhost:25999
mms.staleAuthCodeLimit=5
mms.telemetry.enabled=true
mms.telemetry.atlascli.sampling.percentage=100
mms.telemetry.atlascli.unauth.sampling.percentage=100
mms.telemetry.mdbmcp.unauth.sampling.percentage=100
mms.telemetry.mdbmcp.sampling.percentage=100
mms.telemetry.meko.unauth.sampling.percentage=100
mms.featureFlag.apix.enableApiTelemetryCustomFields=enabled
mms.telemetry.api.sampling.percentage=100
mms.telemetry.api.denylistexemptions=/api/private/unauth/telemetry/events
mms.telemetry.api.denylist=/api/private,/api/scim,/accessLogs,/account,/activity,/admin,/agent,/agentlog,/agentsApi,/alertOAuth2,/allClusters,/analytics,/automation,/backingDatabases,/backup,/billing,/canonicalHosts,/chart,/charts,/conf,/customer,/dashboard,/deployment,/design,/docs,/download,/email,/exception,/explorer,/export,/federation,/federationSettings,/group,/host,/links,/log,/maintenance,/metrics,/monitor,/nds,/okta,/orgs,/performanceAdvisor,/ping,/recovery,/saml,/servers,/settings,/setup,/sso,/support,/system,/test,/usage,/usageData,/usageTypes,/user,/v2,/{groupId},/{orgId},/{privateLinkId},/uiMsgs,/goto,admin,/selfServe,/partners,/prometheus
mms.telemetry.aws.secrets.accessKey=<SECRETMANAGER>
mms.telemetry.aws.secrets.secretKey=<SECRETMANAGER>
mms.telemetry.aws.kinesis.streamName=cloud__apix__api_telemetry__dev
mms.telemetry.aws.kinesis.region=us-east-1
mms.telemetry.aws.kinesis.requestTimeout=2000
mms.telemetry.aws.kinesis.socketTimeout=15000
mms.telemetry.producer.queue.maxSize=200
mms.telemetry.producer.queue.offerTimeoutMillis=10
mms.telemetry.producer.threadCount=1
mms.telemetry.producer.batchSize=50
mms.telemetry.producer.queue.drainIntervalMillis=10000
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.maxHitsPerPeriod=50
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.minutesPeriod=1
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.maxHitsPerPeriod=20
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.minutesPeriod=1
mms.testUtil.enabled=true
mms.ui.cacheStatic=false
mms.ui.filters.globalUserAccessFilter.ipAddressOverride.enabled=true
mms.ui.mongonav.env=commercial
mms.usageData.awsAccessKey=
mms.usageData.awsSecretKey=
mms.usageData.pgp.secretPassPhrase=
mms.user.bypassInviteForExistingUsers=false
mms.user.invitationOnly=false
mms.user.redirectIfPasswordIsEmpty=false
mms.user.registration.extraFields=company
mms.user.username.restrictedDomains=legacyusername[.]mongodb[.]com,invalidusername[.]mongodb[.]com
mms.userSvcClass=UserSvcOkta
mms.vercel.clientId=<SECRETMANAGER>
mms.vercelIntegration.enabled=true
mms.vercel.secret=<SECRETMANAGER>
mms.versionMismatch.detect=false
mms.versionMismatch.pingInterval.unit=SECONDS
mms.versionMismatch.pingInterval=10
mms.versionMismatch.pingInterval.staleCount=6
mms.versionToDeprecate=6.0
mms.forcedDeprecatedVersionUpgradeTargets=7.0,8.0
mms.versionDeprecatedByDate=2025-07-31
mms.voyage.metrics.enabled=true
mms.enableFutureEOLExtensionCheck=true
# V1 Degraded Experience Configuration
mms.v1.degradedExperience.phase1.enabled=false
mms.v1.degradedExperience.phase2.enabled=false
mms.v1.degradedExperience.shadowMode.enabled=true
mms.v1.degradedExperience.creationCutoffDate.org=2025-09-08T00:00:00.000Z
mms.v1.degradedExperience.creationCutoffDate.project=2025-09-08T00:00:00.000Z
mongo.account.encryptedCredentials=
mongo.account.mongoUri=
mongo.account.ssl=
mongo.mmsdbsharedtier/atlaslogsnds/atlaslogsoa/cpsagentlogs/monitoringagentlogs.encryptedCredentials=
mongo.mmsdbsharedtier/atlaslogsnds/atlaslogsoa/cpsagentlogs/monitoringagentlogs.mongoUri=
mongo.mmsdbsharedtier/atlaslogsnds/atlaslogsoa/cpsagentlogs/monitoringagentlogs.ssl=
mongo.atlasbackup.encryptedCredentials=
mongo.atlasbackup.mongoUri=
mongo.atlasbackup.ssl=
mongo.automationcore.encryptedCredentials=
mongo.automationcore.mongoUri=
mongo.automationcore.ssl=
mongo.automationstatus.encryptedCredentials=
mongo.automationstatus.mongoUri=
mongo.automationstatus.ssl=
mongo.authz.encryptedCredentials=
mongo.authz.mongoUri=
mongo.authz.ssl=
mongo.backupdb.encryptedCredentials=
mongo.backupdb.mongoUri=mongodb://127.0.0.1:27017/?maxPoolSize=50&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.backuplogs.encryptedCredentials=
mongo.backuplogs.mongoUri=mongodb://127.0.0.1:27017/?maxPoolSize=50&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.backupstatus.encryptedCredentials=
mongo.backupstatus.mongoUri=
mongo.backupstatus.ssl=
mongo.backupusage.encryptedCredentials=
mongo.backupusage.mongoUri=
mongo.backupusage.ssl=
mongo.backupagentlogs.encryptedCredentials=
mongo.backupagentlogs.mongoUri=
mongo.backupagentlogs.ssl=
mongo.chartsmetadata.encryptedCredentials=
mongo.chartsmetadata.mongoUri=
mongo.chartsmetadata.ssl=
mongo.cloudconf.encryptedCredentials=
mongo.cloudconf.mongoUri=
mongo.cloudconf.ssl=
mongo.event-service.encryptedCredentials=
mongo.event-service.mongoUri=
mongo.event-service.ssl=
mongo.encryptedCredentials=
mongo.mmsdbevents.encryptedCredentials=
mongo.mmsdbevents.mongoUri=
mongo.mmsdbevents.ssl=
mongo.mmsdbeventscopymigration.encryptedCredentials=
mongo.mmsdbeventscopymigration.mongoUri=
mongo.mmsdbeventscopymigration.ssl=
mongo.mmsdbalerts/mmsdbclusterdescriptions.encryptedCredentials=
mongo.mmsdbalerts/mmsdbclusterdescriptions.mongoUri=
mongo.mmsdbalerts/mmsdbclusterdescriptions.ssl=
mongo.mmsdbemails.encryptedCredentials=
mongo.mmsdbemails.mongoUri=
mongo.mmsdbemails.ssl=
mongo.mmsdbautomation.encryptedCredentials=
mongo.mmsdbautomation.mongoUri=
mongo.mmsdbautomation.ssl=
mongo.automationagentlogs.encryptedCredentials=
mongo.automationagentlogs.mongoUri=
mongo.automationagentlogs.ssl=
mongo.mmsdbbilling.encryptedCredentials=
mongo.mmsdbbilling.mongoUri=
mongo.mmsdbbilling.ssl=
mongo.mmsdbcloudproviders.encryptedCredentials=
mongo.mmsdbcloudproviders.mongoUri=
mongo.mmsdbcloudproviders.ssl=
mongo.billingauditing.encryptedCredentials=
mongo.billingauditing.mongoUri=
mongo.billingauditing.ssl=
mongo.mmsdbcollstatslatencyconfig.encryptedCredentials=
mongo.mmsdbcollstatslatencyconfig.mongoUri=
mongo.mmsdbcollstatslatencyconfig.ssl=
mongo.mmsdbcollstatslatencyrrd.encryptedCredentials=
mongo.mmsdbcollstatslatencyrrd.mongoUri=
mongo.mmsdbcollstatslatencyrrd.ssl=
mongo.realmmetricsstate/serverlesschartselect/metricsmisc/metricsmisc2/discovery/tokenization/controlledfeatures.encryptedCredentials=
mongo.realmmetricsstate/serverlesschartselect/metricsmisc/metricsmisc2/discovery/tokenization/controlledfeatures.mongoUri=
mongo.realmmetricsstate/serverlesschartselect/metricsmisc/metricsmisc2/discovery/tokenization/controlledfeatures.ssl=
mongo.iam.encryptedCredentials=
mongo.iam.mongoUri=
mongo.iam.ssl=
mongo.mmsalertconfig.encryptedCredentials=
mongo.mmsalertconfig.mongoUri=
mongo.mmsalertconfig.ssl=
mongo.mmsdbftsrrd.encryptedCredentials=
mongo.mmsdbftsrrd.mongoUri=
mongo.mmsdbftsrrd.ssl=
mongo.mmsdbjobs.encryptedCredentials=
mongo.mmsdbjobs.mongoUri=
mongo.mmsdbjobs.ssl=
mongo.realtimemeasurements.encryptedCredentials=
mongo.realtimemeasurements.mongoUri=
mongo.realtimemeasurements.ssl=
mongo.regionalizedJobs.encryptedCredentials=
mongo.regionalizedJobs.mongoUri=
mongo.regionalizedJobs.ssl=
mongo.logcollection.encryptedCredentials=
mongo.logcollection.mongoUri=
mongo.logcollection.ssl=
mongo.dataexplorer.encryptedCredentials=
mongo.dataexplorer.mongoUri=
mongo.dataexplorer.ssl=
mongo.mmsdbmetering.encryptedCredentials=
mongo.mmsdbmetering.mongoUri=
mongo.mmsdbmetering.ssl=
mongo.mmsdbrrdcache/iadbpings.encryptedCredentials=
mongo.mmsdbrrdcache/iadbpings.mongoUri=
mongo.mmsdbrrdcache/iadbpings.ssl=
mongo.dbprofile.encryptedCredentials=
mongo.dbprofile.mongoUri=
mongo.dbprofile.ssl=
mongo.metricsstats.encryptedCredentials=
mongo.metricsstats.mongoUri=
mongo.metricsstats.ssl=
mongo.mmsdbqueues.encryptedCredentials=
mongo.mmsdbqueues.mongoUri=
mongo.mmsdbqueues.ssl=
mongo.atlaslogsmigration.encryptedCredentials=
mongo.atlaslogsmigration.mongoUri=
mongo.atlaslogsmigration.ssl=
mongo.mmsdbrealmrrd.encryptedCredentials=
mongo.mmsdbrealmrrd.mongoUri=
mongo.mmsdbrealmrrd.ssl=
mongo.mmsdbrrd.encryptedCredentials=
mongo.mmsdbrrd.mongoUri=
mongo.mmsdbrrd.ssl=
mongo.realmdiscovery.encryptedCredentials=
mongo.realmdiscovery.mongoUri=
mongo.realmdiscovery.ssl=
mongo.mmsdbserverlessrrd.mongoUri=mongodb://127.0.0.1:27017/?maxPoolSize=80&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.mmsdbserverlessrrd.encryptedCredentials=
mongo.mmsdbserverlessrrd.ssl=
mongo.ratelimits.mongoUri=mongodb://127.0.0.1:27017/?maxPoolSize=80&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.ratelimits.encryptedCredentials=
mongo.ratelimits.ssl=
mongo.mmsrt.encryptedCredentials=
mongo.mmsrt.mongoUri=
mongo.mmsrt.ssl=
mongo.mongoUri=mongodb://127.0.0.1:27017/?maxPoolSize=80&compressors=snappy&retryWrites=false&retryReads=false&uuidRepresentation=standard
mongo.monitoringdiagnostics/iaaccesslogs.encryptedCredentials=
mongo.monitoringdiagnostics/iaaccesslogs.mongoUri=
mongo.monitoringdiagnostics/iaaccesslogs.ssl=
mongo.monitoringftstelemetry.encryptedCredentials=
mongo.monitoringftstelemetry.mongoUri=
mongo.monitoringftstelemetry.ssl=
mongo.monitoringstatus.encryptedCredentials=
mongo.monitoringstatus.mongoUri=
mongo.monitoringstatus.ssl=
mongo.sessions.encryptedCredentials=
mongo.sessions.mongoUri=
mongo.sessions.ssl=
mongo.nds.encryptedCredentials=
mongo.nds.mongoUri=
mongo.nds.ssl=
mongo.fts.encryptedCredentials=
mongo.fts.mongoUri=
mongo.fts.ssl=
mongo.atlasndsstatus.encryptedCredentials=
mongo.atlasndsstatus.mongoUri=
mongo.atlasndsstatus.ssl=
mongo.platformdiagnostics.encryptedCredentials=
mongo.platformdiagnostics.mongoUri=
mongo.platformdiagnostics.ssl=
mongo.oa.encryptedCredentials=
mongo.oa.mongoUri=
mongo.oa.ssl=
mongo.adl.encryptedCredentials=
mongo.adl.mongoUri=
mongo.adl.ssl=
mongo.atlasbilling.encryptedCredentials=
mongo.atlasbilling.mongoUri=
mongo.atlasbilling.ssl=
mongo.mmsdblogtooling.encryptedCredentials=
mongo.mmsdblogtooling.mongoUri=
mongo.mmsdblogtooling.ssl=
mongo.iaquerystats.encryptedCredentials=
mongo.iaquerystats.mongoUri=
mongo.iaquerystats.ssl=
mongo.abbrvslowlogs.encryptedCredentials=
mongo.abbrvslowlogs.mongoUri=
mongo.abbrvslowlogs.ssl=
mongo.ccpa.encryptedCredentials=
mongo.ccpa.mongoUri=
mongo.ccpa.ssl=
mongo.iaslowlogs.encryptedCredentials=
mongo.iaslowlogs.mongoUri=
mongo.iaslowlogs.ssl=
mongo.integrations.encryptedCredentials=
mongo.integrations.mongoUri=
mongo.integrations.ssl=
mongo.ui.encryptedCredentials=
mongo.ui.mongoUri=
mongo.ui.ssl=
# below 3 props are needed so SeedLocalEnvData for Config Service works. If Config Service seeding moves elsewhere, these 3 props can be removed
mongo.configservice.encryptedCredentials=
mongo.configservice.mongoUri=
mongo.configservice.ssl=
mms.monitoring.abbrvslowlogs.schemaAdvisorFieldsRolloutPercentage=100
mongodb.release.autoDownload.development=true
mongodb.release.autoDownload.enterprise=true
mongodb.release.autoDownload.rc=true
mongodb.release.autoDownload=true
mongodb.release.modulePreference=enterprisePreferred
mongodb.release.publicKeyFile=classpath://mongodb-inc-server.pub
mongot.location=https://internal-downloads.mongodb.com/search/
mongot.version=1.53.4
mongotools.version=100.13.0
# `/prod/*` is the release bucket for mongotune binaries
mongotune.location=https://internal-downloads.mongodb.com/mongotune/prod/
mongotune.version=1.0.2
mongotune.previous.version=1.0.0
mongotune.loglevel=DEBUG
nds.mongotune.minAgentVersion=13.35.2.9529-1
nds.mongotune.plannerCheckConfigUpdate=true
mongosh.version=2.5.6
monitoring.agent.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
monitoring.agent.location.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
monitoring.agent.minimumVersion=5.0.0.309
monitoring.agent.version=7.2.0.488-1
morphia.search.packages=com.xgen.cloud.common.featureFlag._public.model,com.xgen.svc.mms.model.metrics.realtime,com.xgen.svc.mms.deployment.auth,com.xgen.svc.mms.model.performanceadvisor,com.xgen.cloud.activity._public.model.alert,com.xgen.cloud.activity._public.model.alert.config,com.xgen.svc.mms.model,com.xgen.svc.atm.model,com.xgen.svc.mms.model.performanceadvisor.autoindexing,com.xgen.cloud.atm.core._public.model,com.xgen.cloud.deployment._public.model,com.xgen.cloud.monitoring.topology._public.model,com.xgen.cloud.organization._public.model,com.xgen.cloud.atm.core._public.model.status,com.xgen.cloud.billingplatform.activity._public.alert,com.xgen.cloud.billingplatform.activity._public.alert.config,com.xgen.cloud.billingplatform.activity._public.audit,com.xgen.cloud.billingplatform.activity._public.event,com.xgen.cloud.billingplatform.audit._public.model,com.xgen.cloud.billingplatform.invoice._public.model,com.xgen.cloud.billingplatform.model.cloudprovider._public.aws,com.xgen.cloud.billingplatform.model.cloudprovider._public.azure,com.xgen.cloud.billingplatform.model.plan._public.model,com.xgen.cloud.dataexport._public.model,com.xgen.cloud.partners.registration._public.model,com.xgen.cloud.payments.netsuite._public.models,com.xgen.cloud.revrec.selfserve._private.model,com.xgen.svc.mms.model.billing,com.xgen.svc.mms.model.billing.reporting,com.xgen.svc.mms.model.marketing,com.xgen.cloud.common.explorer._public.model
nds.meterUsage.submission.enabled=true
nds.kms.privateNetworking.meterUsage.submission.enabled=true
nds.premiumSKU.meterUsage.submission.enabled=true
# TODO: remove nds.acme.accountDocument later since it is replaced by nds.acme.le.accountDocument
nds.acme.accountDocument=<SECRETMANAGER>
nds.acme.le.accountDocument=<SECRETMANAGER>
nds.acme.gts.accountDocument=<SECRETMANAGER>
nds.acme.failover.lets_encrypt.eventThreshold=300
nds.acme.failover.gts.eventThreshold=50
nds.acme.failover.failoverEventLookbackMinutes=5
nds.acme.failover.retryIntervalMinutes=30
nds.acme.failover.retryResolutionIntervalMinutes=60
nds.acme.phase1RolloutToExistingClusters=true
nds.acme.newDedicatedClusterAllowMixedCerts=true
nds.atlasproxy.conf.maxProcessedAccounts=100
nds.atlasproxy.conf.tlsPEMKeyFile=/etc/pki/tls/private/mongod.pem
nds.atlasproxy.restartProxyProcessOnFallbackCertRotation=true
nds.admin.reasonsRequireJira=false
nds.admin.dataPlaneAccessRequestsOnlineValidation=false
nds.admin.createRespectProtectedHoursMaintenanceAdminUi.enabled=true
nds.agent.envoyConfigServer.enabled=true
nds.agent.envoyConfigServer.nodeId=atlas-envoy
nds.agent.envoyConfigServer.port=9902
nds.agent.envoyConfigServer.adminPort=9901
nds.agent.envoyConfigServer.promExternalPort=27018
nds.agent.envoyConfigServer.promInternalPort=9945
nds.agent.envoyConfigServer.gatewayProxyExternalPort=27014
nds.agent.envoyConfigServer.mongotPromPort=9946
nds.agent.envoyConfigServer.searchEnvoyAdminPort=9905
nds.agent.envoyConfigServer.tlsCertConfigPath=/etc/envoy/sds.yaml
nds.agent.envoyConfigServer.tlsFilterServerName=*.mmscloudteam.com
nds.agentApiKey.allowUnrestricted=true
nds.alwaysUpdateVersions=true
nds.autoScaling.compute.initiateScaleDownWindow.days=MONDAY,TUESDAY,WEDNESDAY,THURSDAY,FRIDAY,SATURDAY,SUNDAY
nds.autoScaling.compute.initiateScaleDownWindow.hours=0:00,23:59
nds.autoScaling.burstable.compute.newClusterEnabled=true
nds.aws.billingReport.bucketName=mongodb-atlas-billing
nds.aws.billingReport.name=daily-report
nds.aws.billingReport.prefix=custom-reports
nds.aws.billingReport.regionName=US_EAST_1
mms.billingImport.azure.blobContainer.name=mongodb-atlas-billing
mms.billingImport.azure.blobContainer.accountName=atlasbillinglocal
mms.billingImport.azure.blobContainer.reportDirectory=daily-export/mongodb-atlas-billing/
mms.billingImport.azure.readUser.clientId=
mms.billingImport.azure.readUser.tenantId=
mms.billingImport.azure.readUser.clientSecret=
nds.aws.al2023.newGroups=true
nds.aws.dataValidation.instanceSize=M10
nds.aws.dataValidation.min.diskSizeGB=1024
nds.aws.dns.domain=mmscloudteam.com
nds.aws.dns.hostedzoneid=Z2K45YHT6R24Z1
nds.aws.dns.datalake.hostedzoneid=Z077496432FTYTEOZFQA5
nds.aws.dns.confluent.serverless.hostedzoneid.private=Z09662983HNIYX1ZZG6N2
nds.aws.dns.confluent.dedicated.hostedzoneid.private=Z07341355QLUMJEUS4JC
nds.aws.dns.reaper.accesskey=
nds.aws.dns.reaper.secretkey=
nds.aws.reaper.accesskey=
nds.aws.reaper.reapAllLocallyCreatedResources=
nds.aws.reaper.runningLimit.days=
nds.aws.reaper.secretkey=
nds.aws.reaper.china.accesskey=
nds.aws.reaper.china.secretkey=
nds.aws.reaper.dryRun.enabled=true
nds.aws.rootARN=arn:aws:iam::************:root
nds.aws.china.rootARN=arn:aws-cn:iam::896388108293:root
nds.azure.al2023.newGroups=true
nds.azure.dataValidation.instanceSize=M10
nds.azure.dataValidation.min.diskSizeGB=1024
nds.azure.dns.datalake.hostedzoneid=Z077496432FTYTEOZFQA5
nds.azure.dns.domain=azure.mmscloudteam.com
nds.azure.dns.hostedzoneid.private=Z3UYXM4KVUWFF0
nds.azure.dns.hostedzoneid.public=Z35BZQJB9312FI
nds.azure.reaper.clientId=
nds.azure.reaper.runningLimit.days=
nds.azure.reaper.secret=
nds.azure.reaper.subscriptionId=
nds.azure.reaper.tenantId=
nds.backup.snapshots.expiration_extend_minutes=60
nds.backup.snapshots.restore.minimum_time_needed_for_restore_minutes=30
nds.backup.snapshots.aws.copySnapshotKmsKeyArnTemplate=arn:aws:kms:%s:************:alias/cps-snapshot-copy
nds.backup.snapshots.aws.fasterRestoreJobKmsKeyArnTemplate=arn:aws:kms:%s:%s:alias/cloud-local-volume-encryption-key
nds.criticalMaintenance.job.enabled=true
nds.corruptionDetection.dataValidation.dailySamplePercent=0.2
nds.corruptionDetection.dataValidation.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dataValidation.minClusterAgeDays=182
nds.corruptionDetection.dbCheck.dailySamplePercent=0.2
nds.corruptionDetection.dbCheck.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dbCheck.minClusterAgeDays=182
nds.corruptionDetection.checkMetadataConsistency.dailySamplePercent=0.2
nds.corruptionDetection.checkMetadataConsistency.minDaysBetweenClusterValidations=182
nds.corruptionDetection.checkMetadataConsistency.minClusterAgeDays=182
nds.connectionEstablishmentRateLimiting.newGroups=false
nds.dataValidation.maxWorkingRecords=10
nds.delete.reaped.clusters.cron.enabled=true
nds.adminEmailAddress=<EMAIL>
nds.externalcaching.redis.cacheEnabled=false
nds.externalcaching.redis.sslEnabled=false
nds.externalcaching.redis.primary.host=localhost
nds.externalcaching.redis.primary.port=6379
nds.externalcaching.redis.replica.host=localhost
nds.externalcaching.redis.replica.port=6379
nds.externalcaching.redis.username=<SECRETMANAGER>
nds.externalcaching.redis.password=<SECRETMANAGER>
nds.externalcaching.redis.entry.ttlHours=1
nds.externalcaching.redis.readThroughOnCacheError=true
nds.externalcaching.redis.domainAuthRedisEnabled=false
nds.lookupSvc.redisCache.entry.refreshTTLOnRead=true
nds.lookupSvc.redisCache.entry.ttlHours=1
nds.lookupSvc.redisCache.entry.readThroughOnCacheError=true
nds.gateway.proxy.enabled=false
nds.gcp.SnapshotBillingReport.dataset=mmscloudteam_billing_export
nds.gcp.SnapshotBillingReport.projectId=atlas-common-0
nds.gcp.dailyBilling.auditDays=9
nds.gcp.billingReport.bucketName=mmscloudteam-billing-reports
nds.gcp.billingReport.prefix=mmscloudteam-billing-
nds.gcp.dataValidation.instanceSize=M10
nds.gcp.dataValidation.min.diskSizeGB=1024
nds.gcp.dns.domain=gcp.mmscloudteam.com
nds.gcp.dns.hostedzoneid.crosscloud.private=Z01519243SEBKUMJAIB7P
nds.gcp.dns.hostedzoneid.private=Z3J4NWPUNXLAIV
nds.gcp.dns.hostedzoneid.public=Z1KSYSHBC93OWU
nds.gcp.projectCreate.count=25
nds.gcp.al2023.newGroups=true
nds.gcp.configurableThroughputEnabled=true
nds.atlasPrioritizeHavingPrimaryOnUpscaledNode.newGroups=true
nds.privateLinkProxyProtocolAws.newGroups=true
nds.gcp.psc.regionGroup.defaultSize=3
nds.gov.us.enabled=false
nds.planner.deleteAfterDate.rollout.percentage=100
nds.healthCheck.staleJobThreshold.hours=1
nds.images.s3Bucket.aws.accesskey=<SECRETMANAGER>
nds.images.s3Bucket.aws.secretkey=<SECRETMANAGER>
nds.images.s3Bucket.aws.local-gov.accesskey=
nds.images.s3Bucket.aws.local-gov.secretkey=
nds.images.gcp.projectId=atlas-images-161419
nds.instances.heracles.s3KeyId=<SECRETMANAGER>
nds.instances.heracles.s3SecretKey=<SECRETMANAGER>
nds.instances.heracles.s3Bucket=<SECRETMANAGER>
nds.instances.heracles.s3Region=<SECRETMANAGER>
nds.instances.secret-names=aws-creds,aws,log-uploader-keys,repo-gateway,s3logwriter-keys,firehoselogwriter-keys,wildcard-cert-pem,tenable,clamav-db,deviceSyncDebugAccessSharedSecret
nds.instances.secrets.CN.wildcard-cert-pem=BLOCKED
nds.instances.secrets.CN.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.aws-creds=<SECRETMANAGER>
nds.instances.secrets.aws.aws=<SECRETMANAGER>
nds.instances.secrets.aws.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.aws.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.aws.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.firehoselogwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.azure.aws-creds=<SECRETMANAGER>
nds.instances.secrets.azure.aws=<SECRETMANAGER>
nds.instances.secrets.azure.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.azure.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.azure.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.azure.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.gcp.aws-creds=<SECRETMANAGER>
nds.instances.secrets.gcp.aws=<SECRETMANAGER>
nds.instances.secrets.gcp.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.gcp.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.clamav-db=
nds.instances.secrets.tenable=
nds.instances.secrets.deviceSyncDebugAccessSharedSecret=<SECRETMANAGER>
nds.instances.shared.version=8.0
nds.instances.serverless.size=SERVERLESS_V2
nds.instances.getConsoleLog=true
nds.instances.dumpHeraclesLogLines=200
nds.instances.rootAuthorizedKeys=c3NoLXJzYSBBQUFBQjNOemFDMXljMkVBQUFBREFRQUJBQUFDQVFEZVlHelFlUHdMSDRYTnJaS0VDU1RHTmhmc2NGb0Qwc2dzMkQ4MW1hOU1zSGt6T2pPaS9TampkTjl0Y0NnZWFGNU9BQjdJOUc1dldEcU9aanhhVXIwZUJCL09rL3o1Q1dOUEJKS3k1K3lSaVRwVFJEL0RtTHVnOXJYZG1aNVRyT2h0QlVrR3l3RjcxZmtSR1VFajJ4dUVhVnBsdlVaczJRQUFLb25BRE9wME0wNFA5dFNSdnFwR05SZnJDNE9BbTF2L1RualBhblNqd2JGR1lYVHp3anIvOW1tUHIzSXZUaTJZUnkzOUdwVHIvYTFqM2lyU2VxR2U0S082dHpvNjhSRUgzcFRtVGRVZVYrQ1FxUlhWa1JXVDBjR0tDTlpRVGVQNWt3YUdXbk9GR05yU2xDbElRVWIwaGRxOUFTelowNGc5aDFXSXF3dFBmVDdhZXJKUGpqcDltRVJTOTc5cldXYnUrN3BEOGpWenovTkNMejdUVitTK29FTGdNYTBQMW50Q3diaDlOTFFYM3BRTEErOWlOdU14YUgraG9hQk9hWktodkk5UWNMdW1YRHkvV1BlUEdkSk1SYmRqSnRPeUtla1NZRVFndVUvU1J1YVR4dEFNaU1Zd0gyM284QWMxTnNpMSsrK0U2S0c2cEVzbFpKM3J2Q0ZPNDJjZVB2WWlOcEVVRGdaZ3dXMThSWlRyMEVnMUI4cnkyUzRXUFJvK3kxMlBCRlFBc3Bod2VmMjljWHlHd2xIRDluejFtbDRGd1kwOEZtcXZjbmdXcmhHRXI4QWNUNXVhWThNWHhQNy92UjFkQ1R1WFlkVGNOVFpCeGNxbEFhUDVPRDZjTkE4aUlzZmtxa2xpTmt4MThSTm1JSVhRbXF1K1dzTVlCUkVPMlFPM05EczJQdVFIbnc9PSBzcmUtc3NoLWtleQo=
nds.instances.os.aws=AL2
nds.instances.os.azure=AL2
nds.instances.os.gcp=AL2
nds.serverlessProxy.os.aws=AL2
nds.serverlessProxy.os.azure=AL2
nds.serverlessProxy.os.gcp=AL2
nds.streamsProxy.os.aws=AL2023
nds.streamsProxy.os.azure=AL2
mms.featureFlag.streamsAWSProxyAL2023Enabled=controlled
mms.featureFlag.streamsAzureProxyAL2023Enabled=controlled
nds.instances.provisioningLogUpload.enabled=true
nds.instances.provisioningLogUpload.bucket=atlas-provisioning-logs-us-east-1-local
nds.instances.anomaliesBucket=atlas-anomalies-logs-us-east-1-local
nds.jira.uri=https://jira-staging.corp.mongodb.com
nds.jira.privateAccessToken=<SECRETMANAGER>
nds.jira.bypassJiraStagingFirewallHeader=<SECRETMANAGER>
nds.jira.bypassJiraStagingFirewallValue=<SECRETMANAGER>
nds.leakedItem.cleanup.retries=3
nds.leakedItem.cleanup.mode=PAUSED
nds.leakedItem.detection.aws.mode=PAUSED
nds.leakedItem.aws.shutdownLeakedInstance=true
nds.leakedItem.aws.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.leakedItem.detection.azure.mode=PAUSED
nds.leakedItem.detection.gcp.mode=PAUSED
nds.leakedItem.detection.gcp.chunkSize=0
nds.leakedItem.detection.gcp.projectsProcessedPerIteration=0
nds.leakedItem.gcp.shutdownLeakedInstance=true
nds.leakedItem.gcp.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.leakedItem.azure.shutdownLeakedInstance=true
nds.leakedItem.azure.shutdownLeakedInstanceWithoutChefStatusDays=180
nds.liveImport.servers.hasPublicIp=false
nds.metrics.deliverySystemForBilling.enabled=true
nds.mongomirror.log.verbosity=3
nds.okta.oidc.testAuthorizationServer.apiKey=<SECRETMANAGER>
nds.orphaned.disk.aws.keephours=0
nds.orphaned.disk.azure.keephours=0
nds.orphaned.disk.gcp.keephours=0
nds.orphaned.ip.keepHours=1
nds.payment.exemptUsernameRegex=mms\.servercukes-.*@mongodb\.com
nds.planning.trace.enabled=false
nds.planning.trace.otelExportEnabled=false
nds.resourceCreationEnabled=true
nds.restore.instances.debug=false
nds.restore.maxConcurrentExports=5
nds.root.cert=ISRGROOTX1
nds.sampleDataset.s3BucketName=atlas-education-staging
nds.serverless.feature.enabled=true
nds.flex.feature.enabled=true
nds.flex.migration.warning.enabled=true
nds.flex.serverless.migration.cron.enabled=false
nds.flex.shared.migration.cron.enabled=false
nds.flex.migration.cron.schedule=0 * 13-20 ? * 2-6
nds.instances.flex.version=8.0
nds.flex.api.shared.mode=DEPRECATED
nds.flex.api.serverless.mode=DEPRECATED
nds.serverless.mtm.consumption.debug.logging.enabled=true
nds.serverless.trace.autoscale.enabled=true
nds.serverless.trace.autoscale.exporter=database
nds.serverless.trace.autoscale.prefix=com.xgen.nds.serverless.autoscale
nds.serverless.trace.autoscale.processor=batch
nds.serverless.supportsLBDeploymentIdOnEnvoyInstance=true
nds.serverless.privateNetworking.supportedCloudProviders=AWS,AZURE
nds.serverless.metrics.useMaxIops=true
nds.serverless.allowSetupServerlessCreateTestInstance=true
nds.serverless.version.upgrade.window.enabled=
nds.serverless.version.upgrade.window.size=
nds.serverless.envoy.healthcheck.max.thread.count=1
nds.serverless.group.limits.maxResidentMTMs=85
nds.serverless.load.deadlineSeconds=900
nds.serverless.load.maxThreads=20
nds.serverless.load.minThreads=5
nds.serverless.metrics.mocks.enabled=true
nds.serverless.metrics.api.version=v2
nds.serverless.mtm.autoscale.maxInstanceSize=M80
nds.serverless.mtm.load.updateDocuments.enabled=true
nds.serverless.mtm.limits.maxResidentTenants=2000
nds.serverless.pool.limits.maxResidentMTMs=100
nds.serverless.pool.limits.maxResidentMTMsForAutoScaleMTMCapacity=25
nds.serverless.pool.limits.maxResidentTenants=6000
nds.serverless.pool.limits.maxResidentTenantsThreshold=.2
nds.serverless.mtm.backingInstanceSize=M10
nds.serverless.restore.denylist=admin,config,local
nds.mtm.sentinel.cron.enabled=false
nds.serverless.tenant.load.updateDocuments.enabled=true
nds.serverless.tenant.privateNetworking.reservedButUnavailablePrivateEndpointExpirationThreshold=P7D
nds.serverless.loadbalancer.defaultNumPreAllocatedRecords=0
nds.serverless.serverlessUpgradeToDedicated.enabled=true
nds.serverParams.shouldSetRateLimiterParams=true
nds.fastServerlessProvisioning.enabled=true
nds.fastFlexProvisioning.enabled=false
nds.combineFreeTierContainerAndClusterProvision.enabled=true
nds.sharding.minSize=M10
nds.ssh.toor.pem=server/conf/nds.ssh.pem
nds.ssh.tse.pem=server/conf/nds.ssh.pem
nds.svc.ipToRegionMapping.executorEnabled=false
nds.tenantUpgrade.s3BucketName=atlas-tenant-data-migration
netsuite.account.atlas.revenue.internal.id=1463
netsuite.account.cloud.receivable.internal.id=1541
netsuite.account.contra.liability.internal.id=1686
netsuite.account.mms.revenue.internal.id=257
netsuite.account.reserve.contra.internal.id=1319
netsuite.account.refund.salestax.debit.internal.id=109
netsuite.account.refund.vat.debit.internal.id=160
netsuite.api.accountNumber=1169967_SB1
netsuite.api.consumerKey=<SECRETMANAGER>
netsuite.api.consumerSecret=<SECRETMANAGER>
netsuite.app.endpoint=https://1169967-sb1.app.netsuite.com/
netsuite.api.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/
netsuite.api.token=<SECRETMANAGER>
netsuite.api.tokenSecret=<SECRETMANAGER>
netsuite.department.d15000.internal.id=213
netsuite.department.d15100.internal.id=259
netsuite.location.dublin.irl.internal.id=10
netsuite.location.nyc.usa.internal.id=4
netsuite.subsidiary.mongo.inc.item.id=1
netsuite.subsidiary.mongo.ltd.item.id=15
netsuite.rest.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/record/v1
netsuite.rest.query.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/query/v1
netsuite.restlet.endpoint=https://1169967-sb1.restlets.api.netsuite.com/app/site/hosting/restlet.nl
netsuite.restlet.tokenEndpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token
netsuite.restlet.cert.privateKey=<SECRETMANAGER>
netsuite.restlet.cert.id=<SECRETMANAGER>
netsuite.restlet.cert.scope=restlets,rest_webservices,suite_analytics
okta.account.client.id=<SECRETMANAGER>
okta.account.client.secret=<SECRETMANAGER>
okta.api.token.emp-qa=<SECRETMANAGER>
okta.api.token=<SECRETMANAGER>
okta.client.id=<SECRETMANAGER>
okta.client.secret=<SECRETMANAGER>
okta.cmab.client.secret=<SECRETMANAGER>
okta.cmab.client.id=0oa4tz4mk3Xk9tHLt297
okta.internal.authorization.server.id=aus4k4jv00hWjNnps297
okta.internal.domain=https://corp.mongodb.com
okta.external.domain=https://auth-qa.mongodb.com
okta.idpDiscoveryPolicyId=00pk4f0955YwdJeAC356
okta.idpId=00ok4f8icpP2iAvzI356
okta.requireNonce=true
okta.restrictedEnv.customerGroup=
okta.restrictedEnv.userGroupNames=
okta.sessionDiscovery.enabled=true
okta.users.groupids=00gqlhuyvs5k7aSAN356
okta.session.maxMinutes=720
okta.profile.target.id=otyk4f8iwXh8C2Q0f356
okta.external.authorization.server.id=default
okta.webhooks.secretKey=<SECRETMANAGER>
okta.token.authorized.client.ids=0oadn4hoajpzxeSEy357,0oa1j0jp84jXF8H4x358
onlinearchive.aws.dataLakeExternalId=<SECRETMANAGER>
onlinearchive.aws.dataLakeRoleARN=<SECRETMANAGER>
onlinearchive.fileCompressionType=snappy
opsgenie.api.eu.url=https://api.eu.opsgenie.com/v2/alerts
opsgenie.api.url=https://api.opsgenie.com/v2/alerts
opsgenie.api.us.url=https://api.opsgenie.com/v2/alerts
pagerduty.events.api.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.us.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.eu.v2.url=https://events.eu.pagerduty.com/v2/enqueue
pagerduty.api.regions.options=US,EU
pagerduty.app.id=P3LVLTH
ping.queue.size=10
ping.thread.count=6
ping.throttling.enabled=false
productionScore.hostname=https://production-moment-dev.dsplatform.prod.corp.mongodb.com
provision.allowReaperOptions=true
provision.allowTerminateAll=true
provision.allowUrlOverride=true
provision.config.loc=scripts/automation/
provision.dns.resolution.wait.secs=5
provision.dns.zone=mongo.plumbing
provision.includeRandomPin=false
provision.script.setup.name=run_atm_linux_agent.sh
qualtrics.api.token=
queryengine.aws.assumeRoleAccessKey=
queryengine.aws.assumeRoleSecretKey=
queryengine.aws.assumeRoleUserARN=
queryengine.adminApi.gcp.regionName=US_EAST_4
queryengine.frontend.port=37017
queryengine.frontend.azure.port=37018
queryengine.frontend.gcp.port=37019
streams.processManager.enabled=false
iam.gcp.reCaptcha.project.id=iam-recaptcha-dev
iam.gcp.reCaptcha.apiKey=<SECRETMANAGER>
iam.gcp.reCaptcha.site.key=<SECRETMANAGER>
reCaptcha.enabled.atlas=false
reCaptcha.enabled.login=false
iam.reCaptcha.enterprise.login.enabled=false
iam.reCaptcha.login.minScore=0.5
iam.reCaptcha.registration.enabled=false
iam.reCaptcha.registration.minScore=0.5
iam.reCaptcha.external.registration.enabled=false
iam.reCaptcha.external.registration.minScore=0.5
iam.reCaptcha.linkExternalAccount.enabled=false
iam.reCaptcha.linkExternalAccount.minScore=0.5
reCaptcha.enabled.externalRegistration=false
reCaptcha.private.key=
reCaptcha.public.key=
realm.billing.api.url=https://realm-dev.mongodb.com/api/private/v1.0/billing/metrics/apps
segment.batch.url=https://api.segment.io/v1/batch
segment.clientSide.writeKey=lxedFh3NXzZRNoAUdGWOE3P2EF2Aub6T
segment.default.userId=5cddc5f71f1be920943ddf51
segment.group.url=https://api.segment.io/v1/group
segment.identify.url=https://api.segment.io/v1/identify
segment.profile.url=https://profiles.segment.com/v1/spaces
segment.serverSide.accessToken=<SECRETMANAGER>
segment.serverSide.personasSpaceId=spa_smteJ81u1XbJMRgmDR9Td5
segment.serverSide.writeKey=GfhDN3UjS4OQ0uffIsHp2rxkpCno6xRZ
segment.track.url=https://api.segment.io/v1/track
serverlessProxy.location=https://internal-downloads.mongodb.com/releases/prod/
serverlessProxy.version=********-1.29.2-8-gad76660
sfdc.salesforceIdCache.inUse=false
sfdc.api.endpoint=https://test.salesforce.com/services/Soap/c/38.0/00DK000000W4vnY
sfdc.api.password=<SECRETMANAGER>
sfdc.api.username=<SECRETMANAGER>
sfdc.sandbox=true
sfdc.sync.cron.enabled=false
sfsc.api.password=<SECRETMANAGER>
sfsc.api.username=<SECRETMANAGER>
sfsc.api.client.id=<SECRETMANAGER>
sfsc.api.client.secret=<SECRETMANAGER>
sfsc.sync.url=https://sf-sync.staging.corp.mongodb.com/cloud/org
sfsc.api.endpoint=https://test.salesforce.com/services/Soap/c/38.0/00DK000000W4vnY
slack.api.message.url=https://slack.com/api/
slack.oauth2.clientId=<SECRETMANAGER>
slack.oauth2.clientSecret=<SECRETMANAGER>
slack.oauth2.url=https://slack.com/api/oauth.access
stitch.api.key=
stripe.accountId.ltd=acct_19arYoIVEaFH6WhW
stripe.accountId=acct_14eBHzJWjF7b4Z9C
stripe.apiKey.ltd=<SECRETMANAGER>
stripe.apiKey.inc=<SECRETMANAGER>
stripe.pubKey.ltd=<SECRETMANAGER>
stripe.pubKey.inc=<SECRETMANAGER>
stripe.webhook.updatecard.secret=
stripe.webhook.updatecard.secret.ltd=
stripe.maxRequestsPerSecond=20
stripe.retry.maxAttempts=5
stripe.retry.minBackoff=2000
support.appEmbedLink=https://support.mongodb.com/api/auth/oidc/initial
taxamo.api.privateToken=<SECRETMANAGER>
taxamo.api.url=https://services.taxamo.com/api/v2
twilio.account.sid=<SECRETMANAGER>
twilio.auth.token=<SECRETMANAGER>
twilio.from.num=<SECRETMANAGER>
twilio.messagingService.sid=
university.centralUrls=https://education-staging.corp.mongodb.com
victorops.alert.api.url=https://alert.victorops.com/integrations/generic/********/alert/
mms.backup.rollingReplacement.email=true
mms.featureFlag.backup.autoRecoveryUnsafeApplyOps=controlled
nds.xds.bindAddress=127.0.0.1
nds.dns.cron.interval.seconds=10
mms.serverless.envoyInstanceSize=M10
mms.featureFlag.serverless.grpc.incrementalRollout.global.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.tenant.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.continuous.enabled=disabled
mms.featureFlag.serverless.serverlessMtmEligibleForUpgradeToDedicated=controlled
mms.featureFlag.serverless.serverlessSharedUIOptionEnabled=controlled
# This ratio is a double between [0,1]. Setting any value >1 will cause this threshold to effectively not be used.
oa.ratio.partitions.to.files.threshold=1.01
oa.avg.filesize.threshold.kilobytes=4
mms.publicApi.prometheus.rateLimitGranularity=1
mms.publicApi.prometheus.rateLimitMaxPerRemoteAddress=120
mms.autoIndexing.sharedTier.maxIndexes=2
mms.clientmetrics.rateLimitMaxPerUser=60
mms.clusterActiveSystem=NEW
mms.clusterActiveBatchSize=250
mms.clusterActiveGroupLimit=1000
mms.clusterActiveNewBroadcast=false
mms.clusterActiveBatchSpread=1000
mms.invitationCron.batchSizeBytes=500000
mms.invitationCron.timeoutMs=50
mms.beamerApiKey=**********************************************
nds.deleted.ingestion.pipeline.grace.period.minutes=5
nds.deleted.dataset.grace.period.minutes=5
azure.ai.openai.compass.accessKey.1=<SECRETMANAGER>
azure.ai.openai.compass.accessKey.2=<SECRETMANAGER>
azure.ai.openai.compass.apiBase.1=https://query-generator-resource-dev-1.openai.azure.com/
azure.ai.openai.compass.apiBase.2=https://query-generator-resource-dev-1.openai.azure.com/
azure.ai.openai.compass.secretKey=<SECRETMANAGER>
azure.ai.openai.compass.modelName=compass-gpt4o
azure.ai.openai.charts.accessKey.1=
azure.ai.openai.charts.accessKey.2=
azure.ai.openai.charts.apiBase.1=https://atlas-charts-local-1.openai.azure.com/
azure.ai.openai.charts.apiBase.2=https://atlas-charts-local-1.openai.azure.com/
azure.ai.openai.charts.gpt4o.accessKey.1=
azure.ai.openai.charts.gpt4o.apiBase.1=https://atlas-charts-local.openai.azure.com/
azure.ai.openai.migrator.accessKey.1=<SECRETMANAGER>
azure.ai.openai.migrator.accessKey.2=<SECRETMANAGER>
azure.ai.openai.migrator.apiBase.1=https://relational-migrator-local-1.openai.azure.com/
azure.ai.openai.migrator.apiBase.2=https://relational-migrator-local-1.openai.azure.com/
azure.ai.openai.migrator.gpt4o.accessKey.1=<SECRETMANAGER>
azure.ai.openai.migrator.gpt4o.apiBase.1=https://relational-migrator-local-2.openai.azure.com/
azure.ai.openai.migrator.gpt4-turbo.accessKey.1=<SECRETMANAGER>
azure.ai.openai.migrator.gpt4-turbo.apiBase.1=https://relational-migrator-local-3.openai.azure.com/
azure.cps.oplogs.storageAccountKeys=<SECRETMANAGER>
azure.ingestion.dlz.storageAccountKey=<SECRETMANAGER>
azure.oa.dlz.storageAccountKey=<SECRETMANAGER>
mms.invoiceApi.rateLimitPerMinute=10
mms.invoiceApi.rateLimitPerMinute.getAllInvoices=30
mms.lineItemsApi.rateLimitPerMinute=30
mms.monitoring.managedslowms.setprofilinglevel.filterExpression.rollout.percentage=100
mms.contentStackApiKey=bltf2fca5bf44f5e817
mms.contentStackDeliveryToken=cs3e28247f4f26bd029a148aa2
mms.monitoring.performanceadvisor.serverless.autoindexing.groupPercentageAllowed=100
mms.contentStackBranch=main
mms.contentStackEnabled=false
mms.monitoring.performanceadvisor.serverless.autoindexing.lookbackWindowMinutes=1440
mms.monitoring.performanceadvisor.serverless.autoindexing.minimumLogCount=1
mms.monitoring.performanceadvisor.serverless.minlogspershape=1
mms.slack.test.channel=test-local
mms.slack.test.token=<SECRETMANAGER>
mms.alert.test.email.emailAddress=<EMAIL>
streams.planner.enabled=true
streams.vpcpeering.scanner.planner.enabled=true
streams.vpcpeering.scanner.planner.delaySeconds=300
streams.proxyInstanceDao.ttlDays=30
streams.proxyDeploymentDao.ttlDays=30
mms.alert.test.sms.phoneNumber=***********
mms.alert.test.datadog.apiToken=<SECRETMANAGER>
mms.alert.test.pagerduty.serviceKey=<SECRETMANAGER>
mms.alert.test.webhook.apiKey=<SECRETMANAGER>
mms.alert.test.victorOps.apiKey=<SECRETMANAGER>
mms.alert.test.victorOps.routingKey=<SECRETMANAGER>
mms.alert.test.opsGenie.apiKey=<SECRETMANAGER>
mms.alert.test.microsoftTeams.url=<SECRETMANAGER>
mms.account.mfa.getMfaFactors.ratelimit.enabled=true
mms.account.mfa.getMfaFactors.ratelimit.maxHitsPerPeriod=60
mms.account.mfa.getMfaFactors.ratelimit.minutesPeriod=15
mms.account.mfa.enrollMfaFactor.ratelimit.enabled=true
mms.account.mfa.enrollMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.enrollMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaFactor.ratelimit.enabled=true
mms.account.mfa.activateMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.activateMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaPushFactor.ratelimit.enabled=true
mms.account.mfa.activateMfaPushFactor.ratelimit.maxHitsPerPeriod=30
mms.account.mfa.activateMfaPushFactor.ratelimit.minutesPeriod=15
mms.account.mfa.verifyMfaFactor.ratelimit.enabled=true
mms.account.mfa.verifyMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyMfaFactor.ratelimit.minutesPeriod=5
mms.account.mfa.verifyPushFactorChallenge.ratelimit.enabled=true
mms.account.mfa.verifyPushFactorChallenge.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyPushFactorChallenge.ratelimit.minutesPeriod=5
mms.account.mfa.deleteMfaFactor.ratelimit.enabled=true
mms.account.mfa.deleteMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.deleteMfaFactor.ratelimit.minutesPeriod=15
mms.account.auth.getAuthMfaState.ratelimit.enabled=true
mms.account.auth.getAuthMfaState.ratelimit.maxHitsPerPeriod=30
mms.account.auth.getAuthMfaState.ratelimit.minutesPeriod=5
mms.account.auth.verifyAuthMfa.ratelimit.enabled=true
mms.account.auth.verifyAuthMfa.ratelimit.maxHitsPerPeriod=10
mms.account.auth.verifyAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.resendAuthMfa.ratelimit.enabled=true
mms.account.auth.resendAuthMfa.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resendAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.cancelAuthVerify.ratelimit.enabled=true
mms.account.auth.cancelAuthVerify.ratelimit.maxHitsPerPeriod=5
mms.account.auth.cancelAuthVerify.ratelimit.minutesPeriod=15
mms.account.auth.cancelAuthVerify.ratelimit.payload.jsonPath=$.username
mms.account.auth.checkPassword.ratelimit.enabled=true
mms.account.auth.checkPassword.ratelimit.maxHitsPerPeriod=10
mms.account.auth.checkPassword.ratelimit.minutesPeriod=5
nds.embeddedConfig.minMongoDBVersion=8.0.0-rc0
nds.embeddedConfig.maxShardCount=3
mms.account.auth.socialSSO.ratelimit.enabled=true
mms.account.auth.socialSSO.ratelimit.maxHitsPerPeriod=50
mms.account.auth.socialSSO.ratelimit.minutesPeriod=5
mms.user.redirectInvite.ratelimit.enabled=true
mms.user.redirectInvite.ratelimit.maxHitsPerPeriod=60
mms.user.redirectInvite.ratelimit.minutesPeriod=15
mms.user.redirectInvite.ratelimit.pathParam.parameterName=username
mms.user.resetSend.ratelimit.enabled=true
mms.user.resetSend.ratelimit.maxHitsPOrPeriod=10
mms.user.resetSend.ratelimit.minutesPeriod=15
mms.user.resetSend.ratelimit.payload.jsonPath=$.username
mms.organization.setSecurityContact.ratelimit.enabled=true
mms.organization.setSecurityContact.ratelimit.maxHitsPerPeriod=10
mms.organization.setSecurityContact.ratelimit.minutesPeriod=5
mms.organization.tags.ui.enabled=false
mms.account.profile.deleteEmailChangeRequest.ratelimit.enabled=true
mms.account.profile.deleteEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.deleteEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.createEmailChangeRequest.ratelimit.enabled=true
mms.account.profile.createEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.createEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.checkPasswordForProfileChange.ratelimit.enabled=true
mms.account.profile.checkPasswordForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.checkPasswordForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.verifyFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyFactorForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.minutesPeriod=15
mms.admin.account.forcePasswordReset.ratelimit.enabled=true
mms.admin.account.forcePasswordReset.ratelimit.maxHitsPerPeriod=200
mms.admin.account.forcePasswordReset.ratelimit.minutesPeriod=1
mms.mmsGrpcServer.enabled=false
cypress.aws.ses.accesskey=<SECRETMANAGER>
cypress.aws.ses.secretkey=<SECRETMANAGER>
mms.admin.queuedAdminActions.enabled=true
mms.admin.queuedAdminActions.ui.enabled=true
# Experimentation Properties
analytics.enabled=false
experiments.enabled=true
pholiota.getExperimentsUrl=
pholiota.mmsApiKey=
pholiota.mmsHttpReferer=
mms.account.profile.getOktaUser.ratelimit.enabled=true
mms.account.profile.getOktaUser.ratelimit.maxHitsPerPeriod=20
mms.account.profile.getOktaUser.ratelimit.minutesPeriod=1
mms.account.profile.updatePassword.ratelimit.enabled=true
mms.account.profile.updatePassword.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updatePassword.ratelimit.minutesPeriod=1
mms.account.profile.updateProfile.ratelimit.enabled=true
mms.account.profile.updateProfile.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updateProfile.ratelimit.minutesPeriod=1
mms.account.profile.activateFactorForProfileChange.ratelimit.enabled=true
mms.account.profile.activateFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.activateFactorForProfileChange.ratelimit.minutesPeriod=1
mms.user.getUserPartnerIntegrationsData.ratelimit.enabled=true
mms.user.getUserPartnerIntegrationsData.ratelimit.maxHitsPerPeriod=100
mms.user.getUserPartnerIntegrationsData.ratelimit.minutesPeriod=20
mms.user.getUserPartnerIntegrationsData.ratelimit.pathParam.parameterName=username
mms.user.registerCall.ratelimit.enabled=true
mms.user.registerCall.ratelimit.maxHitsPerPeriod=100
mms.user.registerCall.ratelimit.minutesPeriod=20
mms.account.auth.unauthedResendVerificationEmail.ratelimit.enabled=true
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.maxHitsPerPeriod=1
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.minutesPeriod=60
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.jsonPath=$.username
mms.account.auth.resendVerificationEmail.ratelimit.enabled=true
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resendVerificationEmail.ratelimit.user.maxHitsPerPeriod=1
mms.account.auth.resendVerificationEmail.ratelimit.user.minutesPeriod=60
mms.account.auth.resetPasswordRequestSend.ratelimit.enabled=true
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.jsonPath=$.username
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.enabled=true
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.jsonPath=$.username
mms.account.auth.resetComplete.ratelimit.enabled=true
mms.account.auth.resetComplete.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resetComplete.ratelimit.minutesPeriod=20
mms.account.auth.resetComplete.ratelimit.payload.jsonPath=$.username
mms.user.mfa.auth.ratelimit.enabled=true
mms.user.mfa.auth.ratelimit.maxHitsPerPeriod=10
mms.user.mfa.auth.ratelimit.minutesPeriod=20
mms.authz.isAuthorized.ratelimit.enabled=true
mms.authz.isAuthorized.ratelimit.maxHitsPerPeriod=10
mms.authz.isAuthorized.ratelimit.minutesPeriod=1
mms.account.device.verifyDeviceCode.ratelimit.enabled=true
mms.account.device.verifyDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.verifyDeviceCode.ratelimit.minutesPeriod=20
mms.account.device.confirmDeviceCode.ratelimit.enabled=true
mms.account.device.confirmDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.confirmDeviceCode.ratelimit.minutesPeriod=20
mms.api.organizations.updateOrganizationSettings.ratelimit.enabled=true
mms.api.organizations.updateOrganizationSettings.ratelimit.maxHitsPerPeriod=10
mms.api.organizations.updateOrganizationSettings.ratelimit.minutesPeriod=5
mms.api.private.account.device.generateCode.ratelimit.enabled=true
mms.api.private.account.device.generateCode.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.generateCode.ratelimit.minutesPeriod=20
mms.api.private.account.device.getToken.ratelimit.enabled=true
mms.api.private.account.device.getToken.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.getToken.ratelimit.minutesPeriod=20
mms.api.private.account.device.revoke.ratelimit.enabled=true
mms.api.private.account.device.revoke.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.revoke.ratelimit.minutesPeriod=20
mms.api.private.telemetry.events.createEvents.ratelimit.enabled=true
mms.api.private.telemetry.events.createEvents.ratelimit.maxHitsPerPeriod=100
mms.api.private.telemetry.events.createEvents.ratelimit.minutesPeriod=1
mms.api.private.email.sendToSecurityContact.ratelimit.enabled=true
mms.api.private.email.sendToSecurityContact.ratelimit.maxHitsPerPeriod=100
mms.api.private.email.sendToSecurityContact.ratelimit.minutesPeriod=20
mms.api.private.dbUser.validate.ratelimit.enabled=true
mms.api.private.dbUser.validate.ratelimit.maxHitsPerPeriod=100
mms.api.private.dbUser.validate.ratelimit.minutesPeriod=20
mms.api.oauth.tokenManagement.ratelimit.enabled=true
mms.api.oauth.tokenManagement.ratelimit.maxHitsPerPeriod=10
mms.api.oauth.tokenManagement.ratelimit.minutesPeriod=1
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.enabled=true
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.maxHitsPerPeriod=20
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.minutesPeriod=1
aws.atlasSelfServeMarketplaceProductId=a27c334d-dc60-4105-89f0-4c979e667e73
aws.marketplaceSellerId=c9032c7b-70dd-459f-834f-c1e23cf3d092
mms.alertProcessing.service.enabled=true
mms.azurenative.AzureNativeRateLimiting.ratelimit.enabled=true
mms.azurenative.AzureNativeRateLimiting.ratelimit.maxHitsPerPeriod=100
mms.azurenative.AzureNativeRateLimiting.ratelimit.minutesPeriod=5
mms.partnerIntegrations.vercelNative.ratelimit.enabled=true
mms.partnerIntegrations.vercelNative.ratelimit.suspendableIp.maxHitsPerPeriod=20
mms.partnerIntegrations.vercelNative.ratelimit.suspendableIp.minutesPeriod=1
mms.partnerIntegrations.vercelNative.ratelimit.pathParam.maxHitsPerPeriod=100
mms.partnerIntegrations.vercelNative.ratelimit.pathParam.minutesPeriod=5
mms.partnerIntegrations.vercelNative.ratelimit.pathParam.parameterName=installationId
mms.partnerIntegrations.vercelNativeGet.ratelimit.enabled=true
mms.partnerIntegrations.vercelNativeGet.ratelimit.suspendableIp.maxHitsPerPeriod=20
mms.partnerIntegrations.vercelNativeGet.ratelimit.suspendableIp.minutesPeriod=1
mms.partnerIntegrations.vercelNativeGet.ratelimit.pathParam.maxHitsPerPeriod=100
mms.partnerIntegrations.vercelNativeGet.ratelimit.pathParam.minutesPeriod=5
mms.partnerIntegrations.vercelNativeGet.ratelimit.pathParam.parameterName=installationId
mms.partnerIntegrations.vercelNative.loadTesting.concurrentThreadCount=10
mms.partnerIntegrations.vercelNative.loadTesting.cleanupMaxAttempts=2
mms.partnerIntegrations.vercelNative.canProvisionNewResources=true
# for local Vercel integration testing, override this in conf-local-secure with your integration's product id. This is Ariel's integration product id
mms.partnerIntegrations.vercelNative.vercelProductId=iap_6Kz1HTNZAoaFprkw
mms.partnerIntegrations.encryptionKey=<SECRETMANAGER>
# Rate Limiting Configuration - filter enabled via config service feature flag mms.featureFlag.apix.ratelimit.apiMiddleware
# Shadow mode (false = log only, true = reject requests) - currently disabled for future use
ratelimit.apiMiddleware.rejectRequests=false
# Circuit Breaker Configuration (disabled via circuitbreaker.enabled=false)
ratelimit.apiMiddleware.circuitbreaker.name=ratelimit-redis-breaker
ratelimit.apiMiddleware.circuitbreaker.failureRateThreshold=40.0
ratelimit.apiMiddleware.circuitbreaker.slidingWindowSize=50
ratelimit.apiMiddleware.circuitbreaker.permittedCallsInHalfOpenState=3
ratelimit.apiMiddleware.circuitbreaker.waitDurationSeconds=15
ratelimit.apiMiddleware.circuitbreaker.slowCallRateThreshold=20.0
ratelimit.apiMiddleware.circuitbreaker.slowCallDurationThresholdMillis=50
# the minimum number of calls which are required before circuitbreaker can calculate error rate
# default value 100 + 1 to count for the redis coldstart.
ratelimit.apiMiddleware.circuitbreaker.minimumNumberOfCalls=101
# Redis cache configuration (if disabled, in-memory local cache will be used)
ratelimit.redis.enabled=false
ratelimit.redis.auth.mode=STATIC
ratelimit.redis.endpoint=127.0.0.1
ratelimit.redis.port=6379
ratelimit.redis.cache.name=
ratelimit.redis.region=
ratelimit.redis.roleArn=
ratelimit.redis.credentials.username=
ratelimit.redis.credentials.password=
ratelimit.redis.ssl.enabled=false
# IP Policy Configuration
ratelimit.policies.default.ip.config.capacity=400
ratelimit.policies.default.ip.config.refillRate=100
ratelimit.policies.default.ip.config.refillDurationSeconds=60
# Group Policy Configuration
ratelimit.policies.default.group.config.capacity=2000
ratelimit.policies.default.group.config.refillRate=1000
ratelimit.policies.default.group.config.refillDurationSeconds=60
# Organization Policy Configuration
ratelimit.policies.default.organization.config.capacity=5000
ratelimit.policies.default.organization.config.refillRate=2000
ratelimit.policies.default.organization.config.refillDurationSeconds=60
# User Policy Configuration
ratelimit.policies.default.user.config.capacity=1000
ratelimit.policies.default.user.config.refillRate=500
ratelimit.policies.default.user.config.refillDurationSeconds=60
mms.featureFlag.gcpArmAxion=controlled
mms.featureFlag.azureArmDpsv6Epsv6=controlled
mms.featureFlag.horizontalScalingAutoSharding=controlled
mms.featureFlag.gcpN4Family=controlled
slack.oauth.v2.clientId=<SECRETMANAGER>
slack.oauth.v2.clientSecret=<SECRETMANAGER>
slack.oauth.v2.url.backend=https://slack.com/api/oauth.v2.access
slack.oauth.v2.url.frontend=https://slack.com/oauth/v2/authorize
slack.oauth.v2.scopes=chat:write,chat:write.customize,chat:write.public
slack.oauth.v2.enabled=true
mms.disaggregatedStorage.fetchLatestBuildFromDev.globalAdminApiKey=meqcgcyo
mms.disaggregatedStorage.fetchLatestBuildFromDev.globalAdminApiSecret=<SECRETMANAGER>
nds.elevatedHealthMonitoring.extension.enabled=true
nds.shadowClusters.enabled=true
nds.gcp.provisionedIOPS.enabled=true
communication.publisher.profile=localstack
communication.publisher.awsRoleArn=000000000000
communication.messagebus.publisher.kinesis.streamName=communication-service-stream-local-aws-us-east-1
communication.statusUpdate.publisher.kinesis.streamName=status-update-service-stream-local-aws-us-east-1
mms.simplifiedAtlasDBUserPrivilegesBackend=true
mms.new.db.user.scramIterationCount=1300000
mms.new.db.user.scramSHA256IterationCount=600000
maas.ingestion.usecase.150760c1-7a40-5033-b8ff-265e24a4a6b0.adminKey=
maas.ingestion.usecase.91a39a8e-b296-5bd5-8cbe-cf54085f1bb1.adminKey=
maas.ingestion.usecase.a152094e-259b-5d73-bd16-e49e3fd6899c.adminKey=
mms.robustNodeHorizonMappingEnabled=true
maas.internalGrafana.url=https://grafana-staging.helix.corp.mongodb.com
maas.internalGrafana.usecaseId.150760c1-7a40-5033-b8ff-265e24a4a6b0.datasourceId=denve7fez6vi8b
maas.internalGrafana.usecaseId.296e3c80-f42c-51f1-9a84-5477e61d0277.datasourceId=eemkz3jcufabka
maas.internalGrafana.usecaseId.336c2b2f-e495-5e7d-9a9c-9e6f492fa157.datasourceId=eeonvauwldekge
maas.internalGrafana.usecaseId.82653516-67ba-5ffa-be02-5e4dd0376682.datasourceId=fenvcrglcc7b4a
# Data Exfiltration Control Plane IPs
nds.dataExfiltration.loadBalancerNames=*
# Voyage Control Plane API Configuration
voyage.controlPlane.url=http://*************
