# NB: The following properties are owned by SRE and managed with Chef. Do not set them in this file as they will be overridden
# mms.http.bindhostname=127.0.0.1
# mms.https.PEMKeyFile=
# mongodb.ssl.PEMKeyFile=
# mongodb.ssl.CAFile=
# local.aws.accessKey=
# local.aws.secretKey=
# Usage Data (CLOUDP-19365)
account.centralUrl=https://account-internal.mongodb.com
account.email.fromAddr.support=
account.email.replyAddr.support=
account.session.maxConcurrent=2
account.session.maxHours=4
alert.flapping.start.threshold=0.4
alert.flapping.stop.threshold=0.3
alert.flapping.linear.weight=0.2
alert.flapping.strategy=fixed
alert.flapping.skip.enabled=true
alert.flapping.enabled.types=OUTSIDE_METRIC_THRESHOLD
alert.flapping.sufficient.states.limit=20
atlas.backup.regional.deployment.enabled=false
mms.featureFlag.awsGraviton4=controlled
mms.featureFlag.awsGraviton4NVMe=controlled
mms.featureFlag.awsCapacityAwareAZSelection=controlled
mms.featureFlag.azureCapacityAwareAZSelection=controlled
mms.featureFlag.gcpCapacityAwareAZSelection=controlled
mms.featureFlag.capacityAwareAZPreventativeRollbackSupport=controlled
mms.featureFlag.automationChangesWebClient=disabled
mms.featureFlag.atlasDataRegionalization=disabled
mms.featureFlag.atlasDataRegionalizationGroup=disabled
mms.featureFlag.atlasAzureSsdPV2=enabled
mms.featureFlag.atlasAzureSsdPV2Wave2=controlled
mms.featureFlag.atlasAzureSsdPV2EnablePreviewRegions=controlled
mms.featureFlag.atlasAzureSsdForcePV1=controlled
mms.featureFlag.logcollectionS3Backend=controlled
mms.featureFlag.atlasProvideHardcodedOidcIdpInformation=controlled
mms.featureFlag.clusterCentricPerformanceAdvisor=enabled
mms.featureFlag.clusterCentricQueryProfiler=controlled
mms.featureFlag.namespaceInsights=controlled
mms.featureFlag.updateMetricsUiForEmbeddedConfigServers=enabled
mms.featureFlag.queryInsights=disabled
mms.featureFlag.atlasOptOutPeriodicCorruptionDetection=controlled
mms.featureFlag.atlasDbcheckEnableMidCollectionResumability=controlled
mms.featureFlag.atlasPrioritizeHavingPrimaryOnUpscaledNode=enabled
mms.featureFlag.atlasDiskWarmingSupport=enabled
mms.featureFlag.atlasIgnoreDiskWarmingState=controlled
mms.featureFlag.sqlSchemaManagementUI=disabled
mms.featureFlag.streamsEnableAzure=disabled
mms.featureFlag.streamsEnableAdditionalAzureRegions=disabled
mms.featureFlag.streamsAzurePrivateLink=disabled
mms.featureFlag.streamsAWSPrivateLink=disabled
mms.featureFlag.streamsEnableAdditionalRegions=disabled
mms.featureFlag.atlasDbcheckWaitForAllNodesToObserveDbcheckStop=controlled
mms.featureFlag.atlasAutomaticEmbeddedConfigTransitions=enabled
mms.featureFlag.vqpBinning=controlled
mms.featureFlag.atlasAzureDsv5AndEsv5InstanceFamilies=controlled
mms.featureFlag.atlasLiveImportHelixPush=enabled
mms.featureFlag.atlasLiveImportHelixPull=enabled
mms.featureFlag.atlasShardedClustersBehindUniformFrontend=disabled
mms.featureFlag.atlasMongomirrorLiveImportHelix=enabled
mms.featureFlag.atlasLiveImportLargeNodePool=disabled
mms.featureFlag.atlasEnableAdditionalDbcheckValidations=controlled
mms.featureFlag.atlasStreamsSP10InstanceTier=disabled
mms.featureFlag.dataExplorerCompassWeb=controlled
mms.featureFlag.dataExplorerCompassWeb.userControlledDisable=controlled
mms.featureFlag.disableDiscoveryIpAddressAliases=disabled
mms.featureFlag.atlasClusterScalingImprovementsPhase1=enabled
mms.featureFlag.atlasIndependentShardScaling=enabled
mms.featureFlag.atlasAsymmetricShardAutoscaling=controlled
mms.featureFlag.baas.PricingChange=enabled
mms.featureFlag.billingResourceTaggingBulkEndpoint=controlled
mms.featureFlag.streamsVpcPeeringPreview=disabled
mms.featureFlag.enableLogRequestTimeframe=controlled
mms.featureFlag.enabledRevampedTriggersUi=enabled
mms.featureFlag.enableTypeScriptInFunctions=controlled
mms.featureFlag.enterpriseLandingPage=disabled
mms.featureFlag.enterpriseSandbox=disabled
mms.featureFlag.deprecateEndpointsAndDataApi=controlled
mms.featureFlag.deprecateThirdPartyServices=controlled
mms.featureFlag.allowCrossRegionExtendedStorage=controlled
mms.featureFlag.disableDarkReader=disabled
mms.featureFlag.bicDeprecationShowWarning=disabled
mms.featureFlag.bicDeprecation=disabled
mms.featureFlag.exemptFromBicDeprecation=disabled
mms.featureFlag.atlasMigrationHubCutover=enabled
mms.featureFlag.customerPinnedFcvAndMdbDowngrade=enabled
mms.featureFlag.atlasAllowOneNodeDownInPacpcm=enabled
mms.featureFlag.atlasAllowSeveralNodesDownInPacpcm=enabled
mms.featureFlag.atlasGcpHyperdiskExtreme=controlled
mms.featureFlag.atlasAutomationShorterQuiesceTime=enabled
mms.featureFlag.atlasAutomationSpecifyForceTrueForShutdowns=enabled
mms.featureFlag.atlasAllowUpscalingToCleanlyAbandonPlans=enabled
mms.featureFlag.RtppDiskThroughputMetrics=enabled
mms.featureFlag.atlasChainPauseMoves=controlled
mms.featureFlag.atlasFcvPinningInUi=enabled
mms.featureFlag.atlasKmipKeyRotationInMaintenanceWindows=controlled
mms.featureFlag.validateS3ChunksOnDownload=controlled
mms.featureFlag.atlasConfigurableGp3Iops=controlled
mms.featureFlag.atlasExcludeRegionUseast2FromAzureDsv5Esv5Families=controlled
mms.featureFlag.customSessionTimeouts=disabled
mms.featureFlag.atlasAutomatePriorityTakeover=controlled
mms.featureFlag.atlasAdvancedRegionalizedPrivateEndpoints=controlled
mms.featureFlag.atlasResourcePoliciesExtensionWithUi=enabled
mms.featureFlag.serverlessUpgradeToDedicatedDebugMode=disabled
mms.featureFlag.atlasAutohealResyncImprovements=controlled
mms.featureFlag.atlasAutohealRemoveIcmpPing=controlled
mms.featureFlag.atlasAcknowledgesApiKeyFromChefConfCall=enabled
mms.featureFlag.atlasAlwaysManagedDefaultRwConcern=controlled
mms.featureFlag.atlasTunedInstanceIndexBuildLimits=controlled
mms.featureFlag.dataExfiltrationManageSecurityGroupsEnabled=disabled
mms.featureFlag.dataExfiltrationSecurityGroupsExcludeAllowAllRuleEnabled=disabled
mms.featureFlag.dataExfilPreventionSidecarEnabled=disabled
mms.featureFlag.dataExfilPreventionSidecarToProxyRoutingEnabled=disabled
mms.featureFlag.atlasAutohealReduceShutdownTime=controlled
mms.featureFlag.atlasAutohealReduceShutdownTimeHealRepair=controlled
mms.featureFlag.atlasTurnDiskWarmingOffForAzureLegacy=controlled
mms.featureFlag.atlasAutohealReducedThreshold=controlled
mms.featureFlag.disableScramSha1Auth=controlled
mms.featureFlag.atlasAzureExcludeConstrainedCombos=controlled
mms.atlasAccessTransparencyX509CertsEnabled=false
mms.atlasEmployeeInformationOktaReadOnlyClientCredentials=
mms.atlasEmployeeInformationOktaApiKeyUrl=
mms.atlasEmployeeInformationOktaApiKeyClientId=
mms.atlasEmployeeInformationOktaIntegrationEnabled=false
mms.atlasClusterUpdateOptimisticConcurrency=true
mms.featureFlag.streamsVPCPeering=disabled
mms.featureFlag.disableCloudNav=controlled
mms.featureFlag.atlasFcvDowngradeAdminAction=enabled
mms.featureFlag.atlasResourcePolicies=disabled
mms.featureFlag.orgLevelGenAiControlSwitch=disabled
mms.featureFlag.al2023AWSOs=controlled
mms.featureFlag.awsAL2023ForceMigrateNVMe=controlled
mms.featureFlag.al2023AzureOs=controlled
mms.featureFlag.azureAL2023ForceMigrateNVMe=controlled
mms.featureFlag.al2023GCPOs=controlled
mms.featureFlag.allowEnforceMinTls13=controlled
mms.featureFlag.atlasTls13AutoUpgradeEnabled=controlled
mms.featureFlag.atlasDeprecateTls10AndTls11=controlled
mms.featureFlag.dataExplorerGenAIFeatures=controlled
mms.featureFlag.dataExplorerGenAISampleDocument=controlled
mms.featureFlag.disableDataExplorerGenAISampleDocumentPassing=controlled
mms.featureFlag.atlasReleaseAwsIps=disabled
mms.featureFlag.alertHostSSHSessionStarted=disabled
mms.featureFlag.awsUseIpamIp=disabled
mms.featureFlag.autoscaling.predictive.compute.enabled=disabled
mms.featureFlag.autoscaling.predictive.compute.m10m20enabled=disabled
mms.featureFlag.enableParcaAgent=disabled
mms.rootVolumeMetrics.rolloutPercentage=100
mms.featureFlag.streamsAWSProxyAL2023Enabled=controlled
mms.featureFlag.streamsAzureProxyAL2023Enabled=controlled
atlas.regionalData.enabled=false
atlasProxy.location=https://internal-downloads.mongodb.com/atlasproxy/releases/prod/
authn.enabled=false
authn.adminSecretKey=<SECRETMANAGER>
authn.internalClient.id=mdb_ic_id_65df25047877bf3a274b426d
authn.internalClient.secret=<SECRETMANAGER>
authn.oauth.enabled=true
authn.oauth.serviceAccounts.enabled=false
authn.oauth.serviceAccounts.alertsEnabled=false
authn.oauth.serviceAccounts.projectAlertsEnabled=false
client.authn.http.addressEnvVar=KUBE_AUTHN_SERVICE_KUBE_HTTP_INTERNAL_US_EAST_2_AWS_CLOUD_INTERNAL_10GEN_CC
client.authn.grpc.addressEnvVar=KUBE_AUTHN_SERVICE_KUBE_INTERNAL_US_EAST_2_AWS_CLOUD_INTERNAL_10GEN_CC
client.apiregistry.http.addressEnvVar=KUBE_API_REGISTRY_SERVICE_KUBE_HTTP_INTERNAL_US_EAST_2_AWS_CLOUD_INTERNAL_10GEN_CC
client.apiregistry.grpc.addressEnvVar=KUBE_API_REGISTRY_SERVICE_KUBE_GRPC_INTERNAL_US_EAST_2_AWS_CLOUD_INTERNAL_10GEN_CC
client.config.grpc.addressEnvVar=KUBE_CONFIG_SERVICE_KUBE_INTERNAL_US_EAST_2_AWS_CLOUD_INTERNAL_10GEN_CC
client.pops.grpc.addressEnvVar=KUBE_SODAFOUNTAIN_SERVICE_KUBE_INTERNAL_US_EAST_2_AWS_CLOUD_INTERNAL_10GEN_CC
client.event.grpc.addressEnvVar=KUBE_EVENTS_API_KUBE_INTERNAL_US_EAST_2_AWS_CLOUD_INTERNAL_10GEN_CC
client.communication.grpc.addressEnvVar=KUBE_COMMUNICATION_SERVICE_KUBE_INTERNAL_US_EAST_2_AWS_CLOUD_INTERNAL_10GEN_CC
auto.provision.aws.trusted.accesskey=<SECRETMANAGER>
auto.provision.aws.trusted.id=<SECRETMANAGER>
auto.provision.aws.trusted.secretkey=<SECRETMANAGER>
automation.agent.location=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/automation-agent/prod/
automation.agent.location.atlas=https://internal-downloads.mongodb.com/automation-dev/automation-agent/prod/
automation.agent.minimumVersion=13.10.0.8620-1
automation.agent.atlasMinimumVersion=13.37.2.9605-1
automation.agent.minimumVersionForClientPIT=4.3.0.2231-1
automation.agent.serverlessLocation=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/atlas-serverless-agent/prod/
automation.agent.serverlessLocation.atlas=https://internal-downloads.mongodb.com/automation-dev/automation-agent/prod/
automation.agent.serverlessMinimumVersion=0.0.0.0
automation.agent.version=13.41.0.9760-1
automation.kmipTool.location=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/automation-agent/prod/
automation.kmipTool.location.atlas=https://internal-downloads.mongodb.com/automation-dev/automation-agent/prod/
automation.versions.autoRefreshUri=classpath://mongodb_version_manifest.json
automation.versions.source=remote
automation.versions.atlasDecoupledFromCm=false
avalara.accountNumber=**********
avalara.companyCode.ireland=MongoDB Ltd - Atlas
avalara.companyCode.inc=10gen
avalara.licenseKey=
avalara.url=https://rest.avatax.com
aws.accessKey.dataWarehouse=<SECRETMANAGER>
aws.accesskey=<SECRETMANAGER>
aws.dataWarehouse.bucket=mongodb-datawarehouse
aws.dataWarehouse.bucket.prefix=data_dump/cloud-internal/
aws.secretKey.dataWarehouse=<SECRETMANAGER>
aws.secretkey=<SECRETMANAGER>
aws.ses.endpoint=https://email.us-east-1.amazonaws.com
awsGraviton.minimumMongoDBVersion=4.2
local.azure.multiTenantAppId=
local.azure.multiTenantAppSecret=
baas.centralUrl=
backup.kmip.server.ca.file=/opt/mongodb-mms/classes/kmip_test_server_ca.pem
biConnector.location=https://info-mongodb-com.s3.amazonaws.com/mongodb-bi/v2/
biConnector.location.atlas=https://internal-downloads.mongodb.org/mongodb-bi/v2/
biConnector.minimumVersion=2.3.1
biConnector.version=2.14.24
atlas.biConnector.version=2.14.24
atlas.api.versioning.rejectFutureDateCalls=false
atlas.api.versioning.enabled=true
atlas.api.versioning.preview=disabled
atlas.api.versioning.upcoming=disabled
braintree.environment=production
braintree.merchantAccountId.inc=
braintree.merchantAccountId.ltd=
braintree.merchantId=
braintree.privateKey=
braintree.publicKey=
braintree.url=https://www.braintreegateway.com
brs.agent.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/backup-agent/${env}/
brs.agent.minimumVersion=*********-1
brs.agent.version=7.8.1.1109-1
brs.client.pit.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/mongodb-backup-restore-util/${env}/
brs.client.pit.version=13.41.0.9760-1
brs.queryable.lruCacheCapacityMB=512
brs.queryable.mongod.memoryQuotaMB=8192
brs.queryable.proxyDebugPort=26999
brs.queryable.proxyPort=25999
brs.queryable.tls.disabledProtocols=SSLv2Hello,SSLv3,TLSv1,TLSv1.1,TLSv1.3
brs.queryable.tls.disabledCiphers=TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384
brs.queryable.wiredTiger.engineConfig.cacheSizeGB=1
brs.s3.validation.testing=disabled
brs.tunnel.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/backup-tunnel/
brs.tunnel.version=13.41.0.9760-1
mms.backup.snapshot.volume.location=https://s3.amazonaws.com/mciuploads/mms-backup/mongodb-mms-build-agent/builds/snapshot-volume/${env}/
mms.backup.snapshot.volume.version=13.41.0.9760-1
brs.wtc.maxNumBufferedFileBatchesOnAgent=1
charts.api.url=
charts.api.secretKey=
charts.apiSecret=
charts.stitch.app.id=
charts.centralUrl=
charts.stitch.useLocalDeploymentModel=true
charts.template.aws.accessKey=
charts.template.aws.secretKey=
charts.template.bucketName=
charts.upgrade.poolSize=5
charts.version=0.0.0
clienthub.password=<SECRETMANAGER>
clienthub.user=<SECRETMANAGER>
data.delete.queue.size=500
dataWarehouse.dailyDataExport=true
dataWarehouse.hourlyDataExport=true
dataWarehouse.dataExport.nonOverlappingNamespaces=nds.planner.plans,mmsdbconfig.config.alertConfigHistory
datadog.api.eu.url=https://api.datadoghq.eu/api/v1
datadog.api.url=https://api.datadoghq.com/api/v1
datadog.api.us.url=https://api.datadoghq.com/api/v1
datadog.api.us3.url=https://api.us3.datadoghq.com/api/v1
datadog.api.us5.url=https://api.us5.datadoghq.com/api/v1
datadog.api.ap1.url=https://api.ap1.datadoghq.com/api/v1
datadog.api.us1.fed=https://app.ddog-gov.com/api/v1
datadog.api.us.apiKey=
datadog.api.us.applicationKey=
datadog.siteSelection.enabled=true
dbobject.read.cache.size=1
dbobject.write.cache.size=400
devHub.centralUrls=
distributed.cron.svc.registries=com.xgen.svc.mms.svc.cron.MmsCronJobRegistry,com.xgen.svc.nds.svc.AtlasClustersCronJobRegistry,com.xgen.svc.brs.svc.cron.BackupCronJobRegistry,com.xgen.svc.mms.svc.billing.audit.BillingAuditorCronRegistry,com.xgen.svc.mms.svc.billing.cron.BillingCronJobRegistry,com.xgen.svc.mms.svc.billing.cron.PaymentCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportCronJobRegistry,com.xgen.svc.mms.svc.billingimport.cron.BillingImportAuditorCronJobRegistry
distributedAlert.executorThreadCount=90
distributedAlert.groupCollateQueueOfferTimeoutMs=1000
distributedAlert.groupCollateQueueSize=100
distributedAlert.groupQueueOfferTimeoutMs=1000
distributedAlert.groupQueueSize=500
distributedAlert.hostQueueSize=1000
dyn.customerName=<SECRETMANAGER>
dyn.password=<SECRETMANAGER>
dyn.username=<SECRETMANAGER>
ecosystem.enabled=true
eloqua.siteId=*********
eloqua.sync.enabled=false
ga.accountId=7301842
ga.enabled=true
ga.p12file=ga-cloud-manager-analytics-api-prod.p12
ga.propertyId=**********-14
ga.serviceAccount=<EMAIL>
ga.viewId=*********
gtm.containerId=GTM-5ZD5LD
hipchat.api.message.url.v1=https://api.hipchat.com/v1/rooms
hipchat.api.message.url=https://api.hipchat.com/v2/room
iam.login.postAuth.allowLocalRedirect=false
ingestion.thread.count=4
intercom.apiKey=<SECRETMANAGER>
intercom.appId=rdi8z17e
intercom.enabled=false
intercom.enabledLoggedInPages=false
intercom.qualtricsSurvey.api.token=
intercom.secretKey=<SECRETMANAGER>
ipstack.api.accessKey=
java.fipsprovider.default=true
jira.default.project=MMSSUPPORT
jira.password=<SECRETMANAGER>
jira.restApiRoot=https://jira.mongodb.org/rest/api/2
jira.username=<SECRETMANAGER>
job.processor.await.termination.time=60
job.processor.await.termination.unit=SECONDS
job.processor.aws.requests.per.second=3.0
job.processor.azure.requests.per.second=3.0
job.processor.blacklist.refresh.time=5
job.processor.blacklist.refresh.unit=SECONDS
job.processor.core.thread.count=10
job.processor.empty.queue.wait=5000
job.processor.enabled=false
job.processor.keepalive.time=1
job.processor.keepalive.unit=MINUTES
job.processor.max.thread.count=10
job.processor.server.tags.enabled=true
job.processor.server.tags.refresh.time=5
job.processor.server.tags.refresh.unit=SECONDS
job.processor.partitionQuery.numPartitions=1
job.processor.ping.time=10
job.processor.ping.unit=SECONDS
job.processor.ping.staleCount=60
local.aws.secretsRegion=us-east-1,eu-west-1
local.aws.secretsPrimaryRegion=us-east-1
marketing.centralUrls=
metricCache.receiveQueueSize=3000
metricCache.sendQueueSize=7000
mlab.clientId=atlas
mlab.portalBaseUrl=https://www.mlab.com
mlab.sharedSecret=
mms.conf.splay.enabled=false
mms.ClientCertificateMode=none
mms.clientmetrics.rateLimitMaxPerUser=60
nds.serverless.sniproxy.default.count=3
mms.accountMultiFactorAuth.enabled=true
iam.accountMultiFactorAuth.smsBackoffLevels.enabled=true
iam.accountMultiFactorAuth.smsBackoffLevels.lockFactorThreshold=3
iam.accountMultiFactorAuth.smsBackoffLevelOne.rateLimitMinutes=1
iam.accountMultiFactorAuth.smsBackoffLevelTwo.rateLimitMinutes=2
iam.accountMultiFactorAuth.smsBackoffLevelThree.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelFour.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelFive.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelSix.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelSeven.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelEight.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelNine.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevelTen.rateLimitMinutes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByOne.countryCodes=62,92,880
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByTwo.countryCodes=998,994,93
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByThree.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByFour.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.offsetByFive.countryCodes=
iam.accountMultiFactorAuth.smsBackoffLevels.denyList.countryCodes=223
mms.accountSuspension.enabled=true
mms.accountDeletion.enabled=true
mms.sendAccountDeletionEmailEnabled=true
mms.enableNewAdminUsersPage=true
mms.enableCommentService=false
nds.acme.failover.retryIntervalMinutes=30
nds.acme.failover.retryResolutionIntervalMinutes=60
nds.acme.failover.lets_encrypt.eventThreshold=300
nds.acme.failover.gts.eventThreshold=50
nds.acme.failover.failoverEventLookbackMinutes=5
mms.acme.defaultProvider=LETS_ENCRYPT_V2
mms.addUserApi.countryRequired=true
mms.admin.queuedAdminActions.enabled=false
mms.admin.queuedAdminActions.ui.enabled=false
mms.admin.filterBackupJobs=true
mms.adminEmailAddr=<EMAIL>
mms.agent.apiKeyReadPolicy=CACHED
mms.agentApiKey.cacheSnapshotMillis=30000
mms.agentCentralUrl=https://api-agents-internal.mongodb.com
mms.automation.realtime.centralUrl=https://real-time-api-agents-internal.mongodb.com
mms.automation.sentry.dsn=<SECRETMANAGER>
mms.automation.sentry.sampleRate=0.1
mms.alertFromEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertFromEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alertReplyToEmailAddr.cloud=MongoDB Cloud Manager <<EMAIL>>
mms.alertReplyToEmailAddr.nds=MongoDB Atlas <<EMAIL>>
mms.alerts.AutomationAgentDown.maximumPingAgeMinutes=5
mms.alerts.BackingDatabaseProcessDown.connectTimeoutMs=10000
mms.alerts.BackupAgentDown.maximumPingAgeMinutes=15
mms.alerts.BackupDaemonLowFreeHeadSpace.frequency=0 0 * * * ?
mms.alerts.BackupJobTooBusy.frequency=0 0 0 * * ?
mms.alerts.BiConnectorAlert.maximumPingAgeMinutes=9
mms.alerts.ClusterMongosIsMissing.maximumPingAgeMinutes=9
mms.alerts.HostDown.maximumPingAgeMinutes=9
mms.alerts.HostExposed.addToNewGroups=true
mms.alerts.InvoiceBilledDoesNotEqualLineItemsTotal.frequency=0 0 7 * * ?
mms.alerts.InvoiceBilledDoesNotEqualPaid.frequency=0 0 7 * * ?
mms.alerts.ServerlessProxyDown.maximumPingAgeMinutes=7
mms.alerts.FlexProxyDown.maximumPingAgeMinutes=7
mms.alerts.MonitoringAgentDown.maximumPingAgeMinutes=7
mms.alerts.RealmDown.maximumPingAgeMinutes=7
mms.alerts.MultipleInvoicesForMonth.frequency=0 0 7 * * ?
mms.alerts.NdsProxyDown.maximumPingAgeMinutes=5
mms.alerts.OplogBehind.maximumOplogPushAgeMinutes=75
mms.alerts.PendingInvoiceOverThreshold.frequency=0 0 12 * * ?
mms.alerts.PrepaidPlanMissingSKU.frequency=0 0 7 * * ?
mms.alerts.ReplicaSetAlertCheck.maximumPingAgeMinutes=9
mms.alerts.StalePendingInvoices.frequency=0 0 7 * * ?
mms.alerts.SystemDatabaseProcessStartupWarnings.frequency=0 0 * * * ?
mms.alerts.TooManyPendingInvoices.frequency=0 0 7 * * ?
mms.alerts.EncryptionAtRestKMSNetworkAccessDenied.frequency=PT15M
mms.alerts.EncryptionAtRestConfigNoLongerValid.frequency=PT15M
mms.alerts.createSystemAlertsEnabled=true
mms.alerts.delaySeconds=120
mms.alerts.disabledEventTypes=CREDIT_CARD_CURRENT,CHARGE_SUCCEEDED,CHARGE_FAILED,CREDIT_CARD_ABOUT_TO_EXPIRE,ORG_CREDIT_CARD_ADDED,ORG_CREDIT_CARD_UPDATED,ORG_CREDIT_CARD_CURRENT,ORG_CREDIT_CARD_ABOUT_TO_EXPIRE,TAGS_MODIFIED,GROUP_TAGS_MODIFIED,CLUSTER_TAGS_MODIFIED,GROUP_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRING,GROUP_SERVICE_ACCOUNT_SECRETS_EXPIRING,GROUP_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRED,GROUP_SERVICE_ACCOUNT_SECRETS_EXPIRED
mms.alerts.globalSummaryEmailIntervalHours=6
mms.alerts.intervalSeconds=120
mms.alerts.unreachable.lookBackMinutes=5
mms.alerts.unreachable.minimumConfCount=1000
mms.alerts.unreachable.thresholdPercent=90
mms.alerts.webhook.readTimeoutMs=5000
mms.alerts.webhook.socketTimeoutMs=2000
mms.alerts.GlobalServiceAccountSecretExpiredAlertCheck.frequency=PT12H
mms.atlasApiDeprecation.fromEmailAddr=<EMAIL>
mms.atlasApiDeprecation.replyToEmailAddr=<EMAIL>
mms.allowOpsManagerOrgs=false
mms.apiregistry.cronjob.enabled=false
mms.api.readPreference=secondaryPreferred
# This issuer depends on the issuer value in AuthN config, they must be the same
authn.oauth.issuer=https://cloud-internal.mongodb.com
mms.api.throttling.enabled=true
mms.assets.assetsHostname=https://assets.mongodb-cdn.com/mms
mms.assets.browserErrorTracking=false
mms.assets.browserErrorTrackingApiKey=6666d88d5b732b7725a8107ba15b4eb6
mms.assets.minified=true
mms.assets.packaged=true
mms.atlas.backup.skipBillingOnOplogMigrationDestination=false
mms.atlas.backup.skipBillingOplogMigrationForAzureOplogStore=false
mms.atlas.backup.skipBillingOplogMigrationForGcpOplogStore=false
mms.atlas.defaultReplicaSetScalingStrategy=WORKLOAD_TYPE
mms.atlasAWSMinimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlas.gcp.minimumGuaranteedDiskWaitTimeHoursForUnblockingComputeUpscale=2
mms.atlasSearch.tracingSamplerRate=0.0
mms.auth.logo.fileName=mdb_logo
mms.auth.methods.google.enabled=false
mms.auth.methods.google.oktaIdpId=0oa1bdkv07SQWRRIB357
mms.auth.methods.github.enabled=false
mms.auth.methods.github.oktaIdpId=0oaijfsmclTTzWxCs357
mms.automation.agentaudit.conf.allowPercent=40.0
mms.automation.agentaudit.conf.maxWriters=5
mms.automation.lastagentstatus.maxWriters=8
mms.automation.changes.pruning.retentionDays=30
mms.automation.changes.pruning.changesPerSecond=500
mms.automation.changes.pruning.maxPerRun=1000000
mms.automation.changes.watcher.enabled=false
mms.automation.settings.refreshPeriodSecs=45
mms.automation.settings.defaultAtlasHttpLogLevel=INFO
mms.automation.settings.dnsCacheTtlSeconds=300
mms.automation.settings.sleepTimes.confCallSleepTimeSecs=15
mms.automation.settings.sleepTimes.errorStateSleepTimeSecs=15
mms.automation.settings.sleepTimes.goalStateSleepTimeSecs=15
mms.automation.settings.agentStatus.sendPeriodSecs=120
mms.automation.settings.agentStatus.maxPlansToSend=5
mms.automation.settings.timeBetweenMongosRollingRestartMs=20000
mms.automation.config.automation.sz.rcid.migration.sleepMillis=100
mms.featureFlag.backup.s3blockstore.calculateMD5=disabled
mms.featureFlag.backup.s3oplogstore.calculateMD5=disabled
mms.backup.alertsEmailAddr=<EMAIL>
mms.backup.allowDevelopmentVersions=false
mms.backup.allowFastSchedule=false
mms.backup.allowRefTimeOfDay=false
mms.backup.allowSnapshotExpiryUpdate=false
mms.backup.automatedRestoreExpirationHours=48
mms.backup.daemon.getApplyOpsJobBufferSize=8
mms.backup.daemon.getBoundInitialSyncJobBufferSize=8
mms.backup.daemon.s3groom.copyExecutorThreads=30
mms.backup.daemonAssignment.replSetSizeMultiple=1.3
mms.backup.daemonAssignment.replSetSizeMultiplePreferred=1.1
mms.backup.daemonAssignment.usableHeadPercent=0.80
mms.backup.daemonAssignment.usableHeadPreferredPercent=0.95
mms.backup.datastore.refresh=false
mms.backup.donate.chosenJob.space.lower.threshold=0.04
mms.backup.donate.chosenJob.space.upper.threshold=0.13
mms.backup.donate.donor.space.threshold=0.85
mms.backup.initialOplogTTLDays=30
mms.backup.logSnapshotFileInfo=false
mms.backup.maximumPITRestoreOplogWindowDays=2
mms.backup.minimumOplogWindowHours=1
mms.backup.provisioningRequired=true
mms.backup.region.default=us-east-1
mms.backup.s3OplogStoreEnabled=true
mms.backup.wiredTiger.engineConfig.cacheSizeGB=6
mms.backup.wtcheckpoints.saveWTSnapshotFilesBatchSize=2000
mms.backupCentralUrl=https://api-backup-internal.mongodb.com
mms.backupRestoreCentralUrl=https://restore-backup-internal.mongodb.com
mms.billing.alertsEnabled=true
mms.billing.awsBillingUsageImportThreads=16
# Start of Billing Anomaly Detection properties
mms.billing.bad.clientId=
mms.billing.bad.secret=
# --- End of Billing Anomaly Detection properties
mms.billing.bccEmailAddress=<EMAIL>
mms.billing.coreBIEmailAddress=<EMAIL>
mms.billing.enabled=false
mms.billing.salescomp.enabled=true
mms.billing.cronJobsDisabled=false
mms.billing.gcp.authProviderX509CertUrl=https://www.googleapis.com/oauth2/v1/certs
mms.billing.gcp.authUri=https://accounts.google.com/o/oauth2/auth
mms.billing.gcp.clientEmail=<EMAIL>
mms.billing.gcp.clientId=
mms.billing.gcp.clientX509CertUrl=https://www.googleapis.com/robot/v1/metadata/x509/mdb-gcp-marketplace-service-ac%40mdb-gcp-marketplace.iam.gserviceaccount.com
mms.billing.gcp.privateKey=
mms.billing.gcp.privateKeyId=
mms.billing.gcp.projectId=mdb-gcp-marketplace
mms.billing.gcp.tokenUri=https://oauth2.googleapis.com/token
mms.billing.gcp.type=service_account
mms.billing.gcp.selfServeMetric=mongodb-atlas-self-service-1.endpoints.mdb-gcp-marketplace.cloud.goog/pay_as_you_go_usage
mms.billing.gcp.atlasPrivateMetric=mdb-gcp-marketplace-atlas.endpoints.mdb-gcp-marketplace.cloud.goog/atlas_private_usage
mms.billing.gcp.atlasPrepaidMetric=mdb-gcp-marketplace-atlas.endpoints.mdb-gcp-marketplace.cloud.goog/prepaid_atlas
mms.billing.gcp.selfServeProductName=mongodb-atlas-self-service-1.endpoints.mdb-gcp-marketplace.cloud.goog
mms.billing.gcp.atlasGovAnnualCommitMetric=mongodb-atlas-for-government-private-offer.endpoints.mdb-gcp-marketplace.cloud.goog/Mongodb_atlas_gov
mms.billing.gcp.atlasGovPrepaidMetric=mongodb-atlas-for-government-private-offer.endpoints.mdb-gcp-marketplace.cloud.goog/Mongodb_atlas_gov_prepaid_atlas
mms.billing.gcp.marketplaceUrl=https://console.cloud.google.com
mms.billing.aws.mp.accessKey=
mms.billing.aws.mp.secretKey=
mms.billing.aws.mp.athena.accessKey=
mms.billing.aws.mp.athena.secretKey=
mms.billing.aws.mp.disbursement.crons.enabled=false
mms.billing.azure.mp.azureNative.service.marketplaceHost=
mms.billing.azure.mp.aad.azureNative.service.clientId=
mms.billing.azure.mp.aad.azureNative.service.secretKey=
mms.billing.azure.mp.aad.landing.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.landing.secretKey.expiration=2026-07-10
mms.billing.azure.mp.aad.service.clientId=<SECRETMANAGER>
mms.billing.azure.mp.aad.service.secretKey=<SECRETMANAGER>
mms.billing.azure.mp.aad.service.secretKey.expiration=2026-07-10
mms.billing.azure.mp.aad.productGraph.clientId=
mms.billing.azure.mp.aad.productGraph.secretKey=
mms.billing.azure.mp.aad.productGraph.secretKey.expiration=
mms.billing.azure.mp.corsUrls=https://login.live.com,https://login.microsoftonline.com,https://partner.microsoft.com,https://portal.azure.com
mms.billing.azure.mp.saas.partnerTeam.webhookUrl=https://us-central1-gcp-pov.cloudfunctions.net/azure-marketplace-webhook-nonprod
mms.billing.govUpliftPercentage=7
mms.billing.internalBillingPlatformRecipientEmail=<EMAIL>
mms.billing.internalPaymentCheck=false
mms.billing.lowPrepaidCreditEmailAddress=<EMAIL>
mms.billing.promoCodeCreatedEmailAddress=<EMAIL>
mms.billing.retrieveStripeEvents=true
mms.billing.selfServePayments.enabled=false
mms.billing.selfServeProducts=seed-data/SelfServeProducts.json
mms.billing.sendInvoiceEmails=true
mms.billing.sfdcProductCodes=seed-data/SalesforceProductCodes.json
mms.billing.sfscSyncEnabled=false
mms.billing.statusPage.apiKey=
mms.billing.updatePayment.ratelimit.attemptsAllowed=5
mms.billing.updatePayment.ratelimit.lockedPeriodMinutes=10
mms.billing.dataExportConfig=data-export/data-export-conf.json
mms.billing.numThreadsForCrossOrgBilling=5
mms.billing.crossOrgBillingThreadAwaitTimeOut=60
mms.billing.auditors.auditEnabled=true
mms.billing.auditors.config=seed-data/BillingAuditorConfigs.json
mms.billing.internalReportRecipientEmail=<EMAIL>
mms.billing.sendDailyBillingReport=false
mms.billing.lineItemMaterializedViewJob.enabled=false
mms.billing.invoicingStatusEmailAddress=<EMAIL>
mms.billingImport.azure.useLegacyBillingReportDate=true
mms.payments.internalNetwork.maxRequestsPerSecond=100
mms.centralUrl=https://cloud-internal.mongodb.com
mms.centralDomainAllowedOrigin=https://cloud-internal.mongodb.com
mms.cloudManagerEnabled=false
mms.cps.billing.oplogStorageRegionUsage=true
mms.cps.directAttachPreWarm.skip.gcp=true
mms.cps.collectionMetadataBucketRegionName=us-east-2
mms.cps.collectionMetadataBucketRegionNameSecondary=us-west-2
mms.cps.collectionMetadataBucket=atlas-backup-collection-metadata-us-east-2-internal
mms.cps.collectionMetadataBucketSecondary=atlas-backup-collection-metadata-us-west-2-internal
mms.cron.enabled=false
mms.cron.track.ttlDays=558
mms.dao.backup.logDao.ttlHours=125
mms.dao.backup.agentLogDao.ttlHours=6
mms.dao.capped.apiRequestTrackDao.sizeInBytes=1073741824
mms.dao.capped.default.sizeInBytes=1073741824
mms.dao.capped.metricAlertDispatchCounterDao.sizeInBytes=1073741824
mms.dao.capped.mlabSharedMigrationLogDao.sizeInBytes=10737418240
mms.dao.capped.ndsMongoMirrorLogDao.sizeInBytes=26843545600
mms.dao.capped.ndsTenantUpgradeLogDao.sizeInBytes=53687091200
mms.databaseIdpDiscovery.enabled=true
mms.databaseIdpDiscovery.mongoOktaId=0oa1rmcoy4lK9isNt297
mms.deploymentdiscovery.enabled=false
mms.docsUrl.nds=https://www.mongodb.com/docs/atlas
mms.docsUrl.search=https://www.mongodb.com/docs/search
mms.docsUrl=https://www.mongodb.com/docs/cloud-manager
mms.email.fromAddr.alerts=MongoDB Atlas Internal <<EMAIL>>
mms.email.fromAddr.support=MongoDB Atlas Internal <<EMAIL>>
mms.email.includeAppEnvName=false
mms.email.logo.force=true
mms.email.logo.height=29
mms.email.logo.width=120
mms.email.logo=/static/images/logo-mongodb.png
mms.email.replyAddr.alerts=<EMAIL>
mms.email.replyAddr.support=<EMAIL>
mms.email.validation=STRICT
mms.emailDaoClass=com.xgen.cloud.email._private.dao.AwsEmailDao
mms.emailVerificationEnabled=false
mms.enableOldHostCheck=true
mms.event.sns.awsAccessKey=
mms.event.sns.awsSecretKey=
mms.event.sns.enabled=false
mms.event.errorOnValidationFailure=false
mms.event-service.centralUrl=localhost:50505
mms.job-service.centralUrl=localhost:50229
mms.feature.enablePauseFreeTierMonitoring=true
mms.featureFlag.adfa.allowRoutingViaCRMInsteadOfEnvoy=enabled
mms.featureFlag.adl.readConcernMajority=controlled
mms.featureFlag.adl.schemaUniqueFieldLimit=disabled
mms.featureFlag.adl.templateRegexGenerationOptimization=disabled
mms.featureFlag.adl.testManualControlledFlag=disabled
mms.featureFlag.adl.testAutomatedControlledFlag=disabled
mms.featureFlag.adl.testEnabledFlag=disabled
mms.featureFlag.adl.testDisabledFlag=disabled
mms.featureFlag.adl.useMongodInsteadOfMqlrun=enabled
mms.featureFlag.alertStateFlappingDetection=controlled
mms.featureFlag.atlasAllowDeprecatedVersions=controlled
mms.featureFlag.atlasMongoDB80=enabled
mms.featureFlag.atlasAutoApplyIndexesForMTMs=controlled
mms.featureFlag.atlasAzureNVMe=controlled
mms.featureFlag.atlasCrossCloudReplication=disabled
mms.featureFlag.atlasCrossRegionPeering=controlled
mms.featureFlag.atlasDataFederationTemplatedOnboarding=enabled
mms.featureFlag.atlasDataFederationAzureFeedDownstreamSystems=controlled
mms.featureFlag.atlasDataFederationAzurePrivateLink=controlled
mms.featureFlag.atlasDeploysUIS=disabled
mms.featureFlag.atlasSearchResolveViews=enabled
mms.featureFlag.atlasGatewayProxy=controlled
mms.featureFlag.atlasMaintenanceWindows=controlled
mms.featureFlag.atlasMountAzureDiskWithLun=disabled
mms.featureFlag.atlasAzureForceMigrationToAZs=controlled
mms.featureFlag.atlasServerlessGRPCSupport=disabled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettings=disabled
mms.featureFlag.atlasProxyUseServerlessPerfTestSettingsOrg=disabled
mms.featureFlag.atlasProxyDisableRateLimiting=disabled
mms.featureFlag.atlasServerlessUsesServerlessAgent=disabled
mms.featureFlag.atlasEnableTestCommands=disabled
mms.featureFlag.atlasSharedTierX509Auth=controlled
mms.featureFlag.atlasX509CRL=controlled
mms.featureFlag.atlasSearch.dedicatedNodes=enabled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreference=disabled
mms.featureFlag.atlasSearch.dedicatedNodesReadPreferenceGroupOverride=controlled
mms.featureFlag.atlasSearch.synchronousSteadyStateReplication=disabled
mms.featureFlag.atlasSearch.indexManagement=enabled
mms.featureFlag.atlasSearch.indexPartitioningUI=disabled
mms.featureFlag.atlasSearch.vibCustomAnalyzersUI=enabled
mms.featureFlag.atlasSearch.confCallViaSearchGateway=disabled
mms.featureFlag.atlasSearch.disableDetailedStatusWrite=controlled
mms.featureFlag.atlasSearch.searchTesterQueryTemplates=disabled
mms.featureFlag.atlasSearch.searchIndexStatusReporting=disabled
mms.featureFlag.atlasSearch.dedicatedNodesAWSSeriesSeven=controlled
mms.featureFlag.atlasSearch.dedicatedNodesEnableReplicationCompression=controlled
mms.featureFlag.atlasSearch.dedicatedNodesForceDisableReplication=disabled
mms.featureFlag.atlasSearch.dedicatedNodesDisableProvisionTimeout=controlled
mms.featureFlag.atlasSearch.useAggregateCommand=enabled
mms.featureFlag.atlasSearch.multipleSubIndexes=controlled
mms.featureFlag.atlasSearch.staleIndexes=enabled
mms.featureFlag.atlasSearch.byok=disabled
mms.featureFlag.atlasSearch.useLifecycleManager=disabled
mms.featureFlag.atlasSearch.envoy.disableStreamIdleTimeout=disabled
mms.featureFlag.atlasSearch.envoy.loadBalancingPolicyRandom=disabled
mms.featureFlag.atlasSearch.indexConfigStatsWrites=disabled
mms.featureFlag.atlasSearch.searchHostStatsWrites=disabled
mms.featureFlag.atlasSearch.readIndexStatsFromNewCollection=disabled
mms.featureFlag.atlasSearch.enableTextOperatorNewSynonymsSyntax=controlled
mms.featureFlag.atlasSearch.indexConfigUpdater=disabled
mms.featureFlag.atlasSearch.disableMongotJvmNativeAccess=enabled
mms.featureFlag.atlasSearch.indexQuantizationUI=disabled
mms.featureFlag.atlasSearch.mongotPushBasedLogExportDownload=disabled
mms.featureFlag.atlasSearch.dedicatedNodesGrpcMode=controlled
mms.featureFlag.atlasDataLakeStorageForOnlineArchive=disabled
mms.featureFlag.atlasDataLakeStorageForceV2UpgradeForOnlineArchive=disabled
mms.featureFlag.atlasDataLakeIngestionPipelinesDeprecation=enabled
mms.featureFlag.atlasDataLakeStorageAllowHourlyIngestion=controlled
mms.featureFlag.atlasDataLakeStorageDatasetRetentionPolicy=controlled
mms.featureFlag.atlasDataFederationDedicatedHostnames=controlled
mms.featureFlag.atlasDataFederationOnGCP=disabled
mms.featureFlag.atlasCloudProviderAccessForGCP=disabled
mms.featureFlag.atlasOnlineArchiveV3TimeSeries=enabled
mms.featureFlag.atlasOnlineArchiveGCP=controlled
mms.featureFlag.atlasOnlineArchiveV1=controlled
mms.featureFlag.atlasOnlineArchiveAsDataSource=controlled
mms.featureFlag.atlasOnlineArchiveIgnoreDataSizeThreshold=controlled
mms.featureFlag.atlasOnlineArchiveEncryption=controlled
mms.featureFlag.atlasOnlineArchivePinToPrimaryRegion=controlled
mms.featureFlag.atlasPushBasedLogExport=controlled
mms.featureFlag.atlasPushBasedLogExportClusterLevel=controlled
mms.featureFlag.atlasGovGcpAssuredWorkloads=disabled
mms.featureFlag.atlasUseDataExplorerService=disabled
mms.featureFlag.atlasUseProviderSubdomains=disabled
mms.featureFlag.atlasUseLegacyHostnameScheme=disabled
mms.featureFlag.dbAccessDuringTenantUpgradeBlocked=enabled
mms.featureFlag.dbAccessDuringTenantUpgradeAllowed=controlled
mms.featureFlag.atlasMultiCloudGlobalClusters=disabled
mms.featureFlag.atlasMaintenanceAutoDeferral=controlled
mms.featureFlag.atlasCNRegionsOnly=disabled
mms.featureFlag.atlas50Cluster=controlled
mms.featureFlag.atlasChurnSurvey=disabled
mms.featureFlag.atlasHideQuickAccessPage=controlled
mms.featureFlag.atlasDeferServerNodeTypeTagUntilPhase2=enabled
mms.featureFlag.atlasDailyBigQueryBilling=enabled
mms.featureFlag.atlasHaltGroupLogIngestion=controlled
mms.featureFlag.atlasForceSkipFastProvision=enabled
mms.featureFlag.awsGraviton=enabled
mms.featureFlag.awsIntelOverGraviton=disabled
mms.featureFlag.gcpInstanceOSAL2=enabled
mms.featureFlag.atlasAutoRetrieveDraftClusterConfig=controlled
mms.featureFlag.singleTargetServerlessDeployment=controlled
mms.featureFlag.streamsAuditLogs=disabled
mms.featureFlag.streamsEnabled=disabled
mms.featureFlag.autoIndexing=controlled
mms.featureFlag.allowVulnerabilityScanning=controlled
mms.featureFlag.automation.changeListener=controlled
mms.featureFlag.automation.configPublishChangeEvent=controlled
mms.featureFlag.automation.configForAgentReturnsOnlyLocal=disabled
mms.featureFlag.automation.chefConfigChangeEvent=enabled
mms.featureFlag.automation.exposeEditorAutofill=disabled
mms.featureFlag.automation.mongoDevelopmentVersions=controlled
mms.featureFlag.automation.sentry=controlled
mms.featureFlag.automation.staggerMongosRollingRestart=controlled
mms.featureFlag.automation.newDeploymentServerUI=enabled
mms.featureFlag.automation.rollingIndexes=controlled
mms.featureFlag.automation.verifyDownloads=enabled
mms.featureFlag.automation.proxyConfigChangeEvent=disabled
mms.featureFlag.automation.enableV6=enabled
mms.featureFlag.automation.enableV7=enabled
mms.featureFlag.automation.enableV8=enabled
mms.featureFlag.automation=controlled
mms.featureFlag.automation.enableRamiAgent=disabled
mms.featureFlag.automation.enableMongotune=enabled
mms.featureFlag.mongotune.enableWriteBlockPolicy=disabled
mms.featureFlag.agentRotatesAtlasProxyLogs=disabled
mms.featureFlag.payments.partners.aws.usageReportCreation=controlled
mms.featureFlag.payments.partners.azure.usageReportCreation=controlled
mms.featureFlag.payments.partners.gcp.usageReportCreation=controlled
mms.featureFlag.payments.salesSoldWarningOrgPaymentStatus=enabled
mms.featureFlag.payments.standaloneCalls.revRec=disabled
mms.featureFlag.awsPrivateEndpoint.waitingForUser=controlled
mms.featureFlag.baas=enabled
mms.featureFlag.baas.billingMigration=controlled
mms.featureFlag.baas.hostingDeprecated=controlled
mms.featureFlag.baas.graphqlDeprecated=controlled
mms.featureFlag.backup.allowEditOplogWindow=controlled
mms.featureFlag.backup.allowWhitelists=enabled
mms.featureFlag.backup.continuousBackupAllowedForNewAWSClusters=controlled
mms.featureFlag.backup.cpsResurrectWithRetainedBackups=enabled
mms.featureFlag.backup.cpsBackupLockMVP=controlled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGA=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicyPostGADisablePolicy=enabled
mms.featureFlag.backup.cpsBackupCompliancePolicy2PersonDisablement=controlled
mms.featureFlag.backup.cpsConcurrentSnapshots=enabled
mms.featureFlag.backup.cpsDirectAttach=enabled
mms.featureFlag.backup.cpsDirectAttachOff=controlled
mms.featureFlag.backup.cpsEmbeddedConfig=enabled
mms.featureFlag.backup.cpsEmbeddedConfigUi=enabled
mms.featureFlag.backup.cpsExtendedSnapshotRetry=controlled
mms.featureFlag.backup.cpsExtraOpenBackupCursorTimeout=controlled
mms.featureFlag.backup.useNoWholeFileRsync=controlled
mms.featureFlag.backup.cpsNoWholeFileRsyncOff=controlled
mms.featureFlag.backup.cpsRestoreSearchIndex=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAws=disabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsMigration=disabled
mms.featureFlag.backup.cpsRestoreCrossProjectAwsNewCMK=disabled
mms.featureFlag.backup.cpsRestoreCrossProjectGCP=enabled
mms.featureFlag.backup.cpsRestoreCrossProjectAzure=enabled
mms.featureFlag.backup.cpsOptimizedDaRestoreAws=controlled
mms.featureFlag.backup.cpsOptimizedDaRestoreAzure=controlled
mms.featureFlag.backup.cpsOptimizedDaRestoreGcp=controlled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAws=controlled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAzure=controlled
mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapGcp=controlled
mms.featureFlag.backup.cpsAwsDaRestoreWithIo2=controlled
mms.featureFlag.backup.cpsDownloadEncryptedSnapshot=enabled
mms.featureFlag.backup.cpsGcpAndAzureNewClustersOnlyCps=enabled
mms.featureFlag.backup.cpsPv2InstantRestoreOff=controlled
mms.featureFlag.backup.cpsPv2StreamingRestore=enabled
mms.featureFlag.backup.cpsSnapshotExportHighFrequency=controlled
mms.featureFlag.backup.cpsSnapshotExportUi=controlled
mms.featureFlag.backup.cpsSnapshotExportAzure=controlled
mms.featureFlag.backup.cpsSnapshotConsistentExport=controlled
mms.featureFlag.backup.cpsSnapshotConsistentExportSplitLargeCollections=enabled
mms.featureFlag.backup.cpsSnapshotDistributionAws=enabled
mms.featureFlag.backup.cpsSnapshotDistributionGcp=controlled
mms.featureFlag.backup.cpsSnapshotDistributionAzure=controlled
mms.featureFlag.backup.cpsSnapshotDistributionUi=enabled
mms.featureFlag.backup.cpsSnapshotDistributionLargeRegionSet=enabled
mms.featureFlag.backup.cpsGcpIncrementalCopySnapshots=disabled
mms.featureFlag.backup.cpsSnapshotAWSPrivateDownload=controlled
mms.featureFlag.backup.cpsSnapshotAzurePrivateDownload=controlled
mms.featureFlag.backup.turnCpsSnapshotAWSPrivateDownloadOff=controlled
mms.featureFlag.backup.turnCpsSnapshotAzurePrivateDownloadOff=controlled
mms.featureFlag.backup.cpsSkipSystemClusterDestroy=controlled
mms.featureFlag.backup.cpsSystemProjectsForExports=enabled
mms.featureFlag.backup.cpsSystemProjectsForDataLakeIngestionPipelineExports=controlled
mms.featureFlag.backup.cpsOplogInGcp=enabled
mms.featureFlag.backup.cpsOplogInGcpExtendMigration=disabled
mms.featureFlag.backup.cpsOplogMigration=enabled
mms.featureFlag.backup.cpsOplogInAzure=controlled
mms.featureFlag.backup.cpsSuccessiveUpgradeQuarterly=enabled
mms.featureFlag.backup.cpsLegacyBackupMigration=enabled
mms.featureFlag.backup.incrementalWtEnabled=controlled
mms.featureFlag.backup.incrementalWtFullSnapshotDayOfWeek=controlled
mms.featureFlag.backup.multipleWorkersPerFile=enabled
mms.featureFlag.backup.parallelRestores=controlled
mms.featureFlag.backup.directS3Restore=controlled
mms.featureFlag.backup.queryable=disabled
mms.featureFlag.backup.queryableFsCache=disabled
mms.featureFlag.backup.s3OplogStoreInOm=controlled
mms.featureFlag.backup.storageEngine=disabled
mms.featureFlag.backup.thirdPartyManaged=disabled
mms.featureFlag.backup.thirdPartyWithManagedOplog=disabled
mms.featureFlag.backup.v2=enabled
mms.featureFlag.backup.wt.queryable=disabled
mms.featureFlag.backup.wtBackpressure=controlled
mms.featureFlag.backup.wtConcurrentGrooms=controlled
mms.featureFlag.backup.wtConcurrentMongoBlockstoreGrooms=disabled
mms.featureFlag.backup.wtEncryptionAtRest=controlled
mms.featureFlag.backup.wtLocalKeyFile=disabled
mms.featureFlag.backup.wtLocalKeyFile.forOrg=controlled
mms.featureFlag.backup.wtNamespaceFiltering=enabled
mms.featureFlag.backup.enableAWSPrivateLinkOption=disabled
mms.featureFlag.backup.optimalSuccessiveUpgradeForRestoreEnabled=controlled
mms.featureFlag.backup.pGzipEnabled=enabled
mms.featureFlag.biConnector=controlled
mms.featureFlag.backup.snapshot.onDemand=disabled
mms.featureFlag.charts=enabled
mms.featureFlag.chartsActivationOptimization=controlled
mms.featureFlag.classicApiAccess=controlled
mms.featureFlag.classicCharts=controlled
mms.featureFlag.classicDashboard=controlled
mms.featureFlag.clientMetadataCollection=controlled
mms.featureFlag.clusterReferencesAutocorrect=enabled
mms.featureFlag.commentServiceEnabled=disabled
mms.featureFlag.crossOrgBilling=controlled
mms.featureFlag.customerFederation=enabled
mms.featureFlag.pagerduty.includeMaintenanceWindowStatus=controlled
mms.featureFlag.dataExplorer=controlled
mms.featureFlag.dataExplorerAggregation=controlled
mms.featureFlag.dataExplorerCrud=controlled
mms.featureFlag.dataExplorerMultiTenant=controlled
mms.featureFlag.extendMaxAllowedDiskSizes=disabled
mms.featureFlag.hostMappingsAutocorrect=controlled
mms.featureFlag.installAgentScript=disabled
mms.featureFlag.managedSlowMs=controlled
mms.featureFlag.m10ShardedClusters=enabled
mms.featureFlag.mongoDBAccessHistory=controlled
mms.featureFlag.monitoring.canonicalHostsTTLFilteredReads.enabled=controlled
mms.featureFlag.monitoring.canonicalHostsTTLWrites.enabled=controlled
mms.featureFlag.monitoring.dataDogMetrics=controlled
mms.featureFlag.monitoring.dataDogDbAndCollMetrics=disabled
mms.featureFlag.monitoring.newRelic=enabled
mms.featureFlag.monitoring.shardedClusterNamespaceMetrics=enabled
mms.featureFlag.monitoring.namespaceQueryLatencyMetrics=controlled
mms.featureFlag.monitoring.skipUpdateClustersDuringLiveMigration=disabled
mms.featureFlag.monthlyUsageMetricsEmail=controlled
mms.featureFlag.ndsClusters=controlled
mms.featureFlag.billingDistributedLinkedOrgBilling=controlled
mms.featureFlag.billing.testMeterDataGeneration=disabled
mms.featureFlag.billingClusterTaggingExport=controlled
mms.featureFlag.disablePremiumDatadog=controlled
mms.featureFlag.serverlessAutoIndexing=enabled
mms.featureFlag.enableFineGrainedAuth=disabled
mms.featureFlag.fineGrainedAuth.userGroups=disabled
mms.featureFlag.serviceAccountManagement=disabled
mms.featureFlag.enableScramSha256Auth=controlled
mms.featureFlag.maxAggregatedDiskChartsForAtlas=controlled
mms.featureFlag.opLatency=enabled
mms.featureFlag.opsManagerConfigForGlobalMonitoringAdmin=enabled
mms.featureFlag.paBugsAndSustainability=controlled
mms.featureFlag.paFeedback=controlled
mms.featureFlag.performanceAdvisor=controlled
mms.featureFlag.historicalReplicaState=controlled
mms.featureFlag.profilerNDS=controlled
mms.featureFlag.profilerv2=controlled
mms.featureFlag.profilerv3=controlled
mms.featureFlag.prometheus=enabled
mms.FeatureFlag.prometheus.allowCustomerRateLimitConfig=controlled
mms.featureFlag.realtime=controlled
mms.featureFlag.realmMetrics=disabled
mms.featureFlag.realmMetricsAlerts=disabled
mms.featureFlag.rsyncBasedHeadCreation=disabled
mms.featureFlag.schemaAdvisor=controlled
mms.featureFlag.segmentGroupSynchronizer=controlled
mms.featureFlag.selfServeConsultingUnits=disabled
mms.featureFlag.standardFreeTier=controlled
mms.featureFlag.teams=disabled
mms.featureFlag.unknown=controlled
mms.featureFlag.atlasContinuousDelivery=enabled
mms.featureFlag.realmEvents=disabled
mms.featureFlag.realm.syncBeta=controlled
mms.featureFlag.failedRollingIndexCleanup=enabled
mms.featureFlag.optOutFromKinesis=enabled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToSingleShard=controlled
mms.featureFlag.atlasLiveMigrateShardedHorizons=controlled
mms.featureFlag.atlasLiveMigrateOlderVersionSupport=enabled
mms.featureFlag.atlasLiveMigrateOneNinePreviewSupport=disabled
mms.featureFlag.atlasLiveMigrateLiftOplogLagRestriction=controlled
mms.featureFlag.atlasLiveMigrateRunsOnlyOnAL2Hosts=enabled
mms.featureFlag.atlasMigrationHubMilestoneOne=controlled
mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToShardedCluster=enabled
mms.featureFlag.atlasLiveMigrateMongosyncPushBasedReplicasetToShardedCluster=enabled
mms.featureFlag.atlasPushLiveMigrationsMongosyncPrivateEndpoints=enabled
mms.featureFlag.atlasLiveMigrateMongosyncVariableShardCount=enabled
mms.featureFlag.atlasLiveMigrateMongosyncEmbeddedVerifier=enabled
mms.featureFlag.atlasLiveMigrateDestinationOplogValidationForVerifier=enabled
mms.featureFlag.performanceAdvisorRecommendSearch=controlled
mms.featureFlag.uiAccessList=controlled
mms.featureFlag.tokenizedQueryShapeStatsMetrics=disabled
mms.featureFlag.atlasSearchQueryTelemetry=controlled
mms.featureFlag.randomSamplingSlowLogStreamer=enabled
mms.featureFlag.datadogCustomEndpoint=controlled
mms.featureFlag.resourceTagComponent.clusters=disabled
mms.featureFlag.resourceTagComponent.projects=disabled
mms.featureFlag.enableExperimentHoldouts=disabled
mms.featureFlag.iaSlowLogsReads=controlled
mms.featureFlag.allowGCPPreviewRegions=disabled
mms.featureFlag.allowAWSPreviewRegions=disabled
mms.featureFlag.allowAzurePreviewRegions=disabled
mms.featureFlag.atlasDedicatedBackboneToReact=enabled
mms.featureFlag.atlasDedicatedReactClusterDetails=controlled
mms.featureFlag.encryptionAtRestAzureKeyVaultPrivateEndpoint=enabled
mms.featureFlag.encryptionAtRestAwsKmsPrivateEndpoint=enabled
mms.featureFlag.earGcpKmsRoleBasedAuth=disabled
mms.featureFlag.atlasAllowAtlasAdminFourFourPrivileges=controlled
mms.featureFlag.connectionEstablishmentRateLimiting=enabled
mms.featureFlag.apix.enableApiTelemetryCustomFields=disabled
mms.featureFlag.customer.queryShapeInsights=controlled
mms.federation.restrictOrgMembership.default=true
mms.federation.bypassSsoDomains=
mms.enableOidcIdpNonAtlas=true
mms.featureFlag.privateLinkProxyProtocolAws=controlled
mms.featureFlag.privateLinkMaxIncomingConnectionsMultiplier=controlled
mms.fromEmailAddr=MongoDB Atlas <<EMAIL>>
mms.globalRoles.mapping=internal
mms.group.tags.edit.roleRequired=GLOBAL_MONITORING_ADMIN
mms.group.tags.read.roleRequired=GLOBAL_READ_ONLY
mms.group.useSoftDelete=false
mms.groupType.default=CLOUD
mms.helpAndSupportPage.enabled=true
mms.http.bindhostname=127.0.0.1
mms.https.dualConnectors=true
mms.invite.salesSold.ratelimit.maxAllowed=100
mms.invite.ratelimit.maxAllowed=10
mms.invite.ratelimit.periodMinutes=1
mms.jetty.stopTimeoutMS=60000
mms.limits.maxAlertConfigsPerGroup=250
mms.limits.maxApiUsersPerOrg=500
mms.limits.maxGroupsPerOrg=250
mms.limits.maxGroupsPerUser=500
mms.limits.maxNotificationsPerAlert=25
mms.limits.maxOrgsPerUser=250
mms.limits.maxTeamsPerGroup=100
mms.limits.maxTeamsPerOrg=250
mms.limits.maxTeamsPerUser=100
mms.limits.maxUsersPerGroup=500
mms.limits.maxUsersPerOrg=500
mms.limits.maxUsersPerTeam=250
mms.limits.maxExportsPerGroup=100
mms.limits.maxCustomPoliciesPerOrg=100
mms.limits.maxM0sPerUnverifiedOrg=10
mms.limits.maxServiceAccountsPerOrg=500
mms.limit.maxNetworkAddressesPerServiceAccount=200
mms.logCollectionJob.defaultExtensionDays=30
mms.login.exemptIps=***************
mms.login.newDeviceNotification.enabled=true
mms.login.ratelimit.attemptsAllowed=15
mms.login.ratelimit.lockedPeriodMinutes=5
mms.logcollection.s3Bucket.accessKey=<SECRETMANAGER>
mms.logcollection.s3Bucket.secretKey=<SECRETMANAGER>
mms.migration.path.verify=false
mms.marketing.sync=true
mms.migration.config.customers.rcid.sleepMillis=50
mms.automation.agent.config.atlasClusterNameRollout=0
mms.monitoring.accessLogs.db.numInitialChunks=1000
mms.monitoring.accessLogs.maintenanceEnabled=false
mms.monitoring.alwaysUpdateCollectedIndex.enabled=true
mms.monitoring.accesslogs.rateLimit.hours=48
mms.monitoring.accesslogs.rateLimit.linesPerHost=80000
mms.monitoring.agent.session.timeoutMillis=120000
mms.monitoring.atlasPremiumRetention.migrationsEnabled=true
mms.monitoring.agent.intervals.chunksCollectionMillis=60000
mms.monitoring.agent.intervals.dbStatsCollectionMillis=300000
mms.monitoring.agent.intervals.listDbsCollectionMillis=300000
mms.monitoring.agent.intervals.collLatencyStatsIntervalMillis=600000
mms.monitoring.agentlog.writers=1
mms.monitoring.chartDomain=https://chart-@<EMAIL>
mms.monitoring.clientmetadata.rateLimit.hours=48
mms.monitoring.clientmetadata.rateLimit.linesPerHost=28800
mms.monitoring.config.hostclusters.rcid.migration.sleepMillis=100
mms.monitoring.config.hosts.migration.sleepMillis=500
mms.monitoring.slowLogIngestionHostMetadata.migration.sleepMillis=10
mms.monitoring.slowlogs.rateLimit.linesPerTenantShared=18000
mms.monitoring.groupRetentionPolicy.migration.heartbeatExpirationMillis=300000
mms.monitoring.historicalReplicaState.ingestionPercentage=0
mms.monitoring.ingestion.bucketedMetricRollup.enabled=true
mms.monitoring.ingestion.bucketedMetricRollup.bucketCount=2
mms.monitoring.ingestion.bucketedMetricRollup.serverlessBucketCount=2
mms.monitoring.ingestion.bucketedMetricRollup.realmBucketCount=2
mms.monitoring.ingestion.diskpartition.poolsize=2
mms.monitoring.ingestion.diskpartition.queuesize=100
mms.monitoring.ingestion.hostping.poolsize=8
mms.monitoring.ingestion.hostping.queuesize=250
mms.monitoring.ingestion.munin.poolsize=2
mms.monitoring.ingestion.ndsautoscaling.poolsize=2
mms.monitoring.ingestion.newrelic.guid=com.mongodb.monitoring.integrations.newrelic.cloudmanager.production
mms.monitoring.ingestion.newrelic.poolsize=1
mms.monitoring.ingestion.newrelic.queuesize=100
mms.monitoring.ingestion.newrelic.version=1.0.0
mms.monitoring.ingestion.readPreference=secondaryPreferred
mms.monitoring.ingestion.rollup.job.scheduledRetryWait=1800000
mms.monitoring.ingestion.rollup.job.PT1M.workingRetryWait=300000
mms.monitoring.ingestion.rollup.job.PT1H.workingRetryWait=3300000
mms.monitoring.ingestion.rollup.job.PT24H.workingRetryWait=10800000
mms.monitoring.abbrvslowlogs.maintenanceEnabled=false
mms.monitoring.abbrvslowlogs.db.numInitialChunks=12000
mms.monitoring.abbrvslowlogs.intervalStrategyHours=24
mms.monitoring.parollupshapes.maintenanceEnabled=false
mms.monitoring.parollupshapes.db.numInitialChunks=8000
mms.monitoring.parollupshapes.retentionHours=168
mms.monitoring.iaslowquerylogs.maintenanceEnabled=false
mms.monitoring.iaslowquerylogs.db.numInitialChunks=12000
mms.monitoring.iaslowquerylogs.intervalStrategyHours=24
mms.monitoring.performanceadvisor.indexcollection.rerunIntervalMinutes=60
mms.monitoring.performanceadvisor.indexcollection.scheduleIntervalMinutes=360
mms.monitoring.performanceadvisor.iterative.maxLogLines=200000
mms.monitoring.performanceadvisor.textQueryThreshold=300
mms.monitoring.performanceadvisor.rollup.job.PT24H.workingRetryWait=10800000
mms.monitoring.performanceadvisor.rollup.job.PT1H.workingRetryWait=7200000
mms.monitoring.performanceadvisor.rollup.job.PT1M.workingRetryWait=300000
mms.monitoring.performanceadvisor.rollup.scheduler.enabled=true
mms.monitoring.realm.metrics.api.url=
mms.monitoring.rrd.collStatsLatencyMaintenanceEnabled=false
mms.monitoring.rrd.databaseNumInitialChunks=208
mms.monitoring.rrd.ftsMaintenanceEnabled=false
mms.monitoring.rrd.hostNumInitialChunks=200
mms.monitoring.rrd.maintenanceEnabled=false
mms.monitoring.rrd.rawdb.maxDatabases=2000
mms.monitoring.rrd.rrdIntervalsStrategy=DEFAULT
mms.monitoring.rrd.realmMaintenanceEnabled=false
mms.monitoring.rrd.realmNumInitialChunks=200
mms.monitoring.rrd.serverlessMaintenanceEnabled=false
mms.monitoring.totalNamespaceCollectionLimit=150
mms.monitoring.pinnedNamespaceCollectionLimit=20
mms.monitoring.topNamespaceCollectionLimit=20
mms.monitoring.slowlogs.ingestion.parsingPercentage=0
mms.monitoring.slowlogs.ingestion.targetRate=5
mms.monitoring.slowlogs.intervalRetentionHours=168
mms.monitoring.slowlogs.intervalStrategyHours=24
mms.monitoring.slowlogs.rateLimit.hours=24
mms.monitoring.slowlogs.rateLimit.linesPerHost=144000
mms.monitoring.iaslowquerylogs.ingestion.poolsize=6
mms.mana.http.socketTimeout=10000
mms.mana.http.connectTimeout=10000
mms.mana.clientId=<SECRETMANAGER>
mms.mana.clientSecret=<SECRETMANAGER>
mms.mana.authToken=
mms.logIngestion.poolsize=4
mms.logIngestion.intervalRetentionHours=144
mms.multiFactorAuth.allowReset=true
mms.multiFactorAuth.issuer=MongoDB
mms.multiFactorAuth.level=OFF
mms.personalizationWizardEnabled=false
mms.personalizationWizardRedirectEnabled=false
mms.darkMode.preview.enabled=true
mms.darkMode.default=LIGHT
mms.ofac.enabled=true
mms.ofac.notificationEmailAddr=
mms.ofac.supportEmailAddr=<EMAIL>
mms.plan.default=FREE_TIER
mms.publicApi.events.rateLimitGranularity=1
mms.publicApi.events.rateLimitMaxPerGroup=100
mms.publicApi.globalKeyRoleDenylist=GLOBAL_OWNER
mms.publicApi.globalReadOnlyUserLimit=8000
mms.publicApi.globalReadOnlyUserPeriodMinutes=1
mms.publicApi.maxTempKeys=100
mms.pushLiveMigrations.enabled=false
mms.pushLiveMigrations.mongoClient.validations.enabled=false
mms.pushLiveMigrations.mmsUi.defaultAccessListCidr=10.0.0.0/8
mms.pushLiveMigrations.mmsUi.serviceMeshEnvVar=MESH_MMS_UI_INTERNAL_US_EAST_2_AWS_CLOUD_INTERNAL_10GEN_CC
mms.publicApi.metric.rateLimitGranularity=1
mms.publicApi.metric.rateLimitMaxPerGroup=100
mms.publicApi.performanceAdvisor.rateLimitGranularity=1
mms.publicApi.performanceAdvisor.rateLimitMaxPerGroup=100
mms.publicApi.queryShapeInsights.rateLimitGranularity=1
mms.publicApi.queryShapeInsights.rateLimitMaxPerGroup=10
mms.publicApi.dataExplorer.rateLimitGranularity=1
mms.publicApi.dataExplorer.rateLimitMaxPerGroup=100
mms.quartz.thread.count=40
mms.remoteIp.header=X-Cluster-Client-Ip
mms.replyToEmailAddr=<EMAIL>
mms.replyToSuspensionAdminEmailAddr=<EMAIL>
mms.root.redirect=/account/login
mms.search.envoy.location=https://mongodb-mms-build-envoy-serverless.s3.amazonaws.com/tarballs/
mms.search.offlineUpgradePackages=1.38.1.2120
mms.search.offlineDowngradePackages=1.41.0
mms.search.commonLabelsEnabledTiers=M10
mms.search.staleEnabledTiers=MTM
mms.search.incubatorVectorEnabledTiers=ALL
mms.search.apiRateLimitRules=default:2
mms.security.allowCORS=true
mms.security.backdoor=false
mms.security.cors.allow.localhost.origin=false
mms.security.disableBrowserCaching=false
mms.security.globalWhitelist=seed-data/GlobalWhitelistDao-internal.json
mms.security.hstsMaxAgeSeconds=********
mms.security.requireCSRFToken=true
mms.server.sentry.dsn=<SECRETMANAGER>
mms.server.sentry.enabled=true
mms.server.sentry.sampleRate=1
mms.server.sentry.apiKey=<SECRETMANAGER>
mms.serverless.xDSServer.enabled=false
mms.serverless.xDSServer.tlsRequired=
mms.serverless.xds.error.log.alert.test.enabled=false
mms.session.maxConcurrent=10
mms.session.maxHours=12
mms.siteFullName.nds=MongoDB Atlas Internal
mms.siteName.nds=Atlas Internal
mms.snapshotQueryHost=queryable-backup-internal.mongodb.com:27017
mms.staleAuthCodeLimit=5
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.maxHitsPerPeriod=50
mms.tempDownloadLinkService.headS3DownloadLink.ratelimit.minutesPeriod=1
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.enabled=true
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.maxHitsPerPeriod=20
mms.tempDownloadLinkService.getS3DownloadLink.ratelimit.minutesPeriod=1
mms.testUtil.enabled=false
mms.throttleSvcEnabled=false
mms.ui.mongonav.env=commercial
mms.ui.filters.globalUserAccessFilter.enabled=true
mms.ui.filters.globalUserAccessFilter.additionalIps=*************,*************,*************,*************
mms.usageData.awsAccessKey=
mms.usageData.awsSecretKey=
mms.usageData.collection=false
mms.usageData.ingestion=false
mms.usageData.pgp.publicKey=/mongodb-inc-opsmanager.pub.asc
mms.usageData.pgp.secretKey=/mongodb-inc-opsmanager.private.asc
mms.usageData.pgp.secretPassPhrase=<SECRETMANAGER>
mms.usageData.s3.bucket=mongodb-datawarehouse
mms.user.bypassInviteForExistingUsers=false
mms.user.invitationOnly=true
mms.user.invitationOnly.redirectUrl=https://www.mongodb.com/cloud/atlas/potato
mms.user.redirectIfPasswordIsEmpty=false
mms.user.registration.defaultCountry=US
mms.user.registration.extraFields=country
mms.user.username.restrictedDomains=legacyusername[.]mongodb[.]com,invalidusername[.]mongodb[.]com
mms.userSvcClass=UserSvcOkta
mms.vercel.clientId=
mms.vercelIntegration.enabled=false
mms.vercel.secret=
mms.versionMismatch.detect=false
mms.versionMismatch.pingInterval.unit=SECONDS
mms.versionMismatch.pingInterval=10
mms.versionMismatch.pingInterval.staleCount=60
mms.versionToDeprecate=6.0
mms.forcedDeprecatedVersionUpgradeTargets=7.0,8.0
mms.versionDeprecatedByDate=2025-12-19
mms.enableFutureEOLExtensionCheck=true
# V1 Degraded Experience Configuration
mms.v1.degradedExperience.phase1.enabled=false
mms.v1.degradedExperience.phase2.enabled=false
mms.v1.degradedExperience.shadowMode.enabled=true
mms.v1.degradedExperience.creationCutoffDate.org=2025-09-08T00:00:00.000Z
mms.v1.degradedExperience.creationCutoffDate.project=2025-09-08T00:00:00.000Z
mmsdbautomationlog.queuesize=10000
mmsdbautomationlog.writers=2
mmsdbserverlog.logs.enabled=false
mmsdbserverlog.logs.ttlSeconds=600
mms.azure.ssdv2.regions=francecentral,eastasia,canadacentral,koreacentral,norwayeast,polandcentral,southafricanorth,swedencentral,switzerlandnorth,uaenorth,australiaeast,northeurope,centralindia,japaneast,uksouth,brazilsouth,southeastasia,germanywestcentral,southcentralus,westus3,centralus,eastus2,westeurope,eastus,westus2,italynorth,israelcentral,spaincentral,mexicocentral
mms.azure.ssdv2.rollout.regions=
mms.azure.ssdv2.preview.regions=eastus2euap
mongo.account.encryptedCredentials=
mongo.account.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.account.ssl=
mongo.mmsdbsharedtier/atlaslogsnds/atlaslogsoa/cpsagentlogs/backupagentlogs/automationagentlogs/monitoringagentlogs.encryptedCredentials=true
mongo.mmsdbsharedtier/atlaslogsnds/atlaslogsoa/cpsagentlogs/backupagentlogs/automationagentlogs/monitoringagentlogs.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.mmsdbsharedtier/atlaslogsnds/atlaslogsoa/cpsagentlogs/backupagentlogs/automationagentlogs/monitoringagentlogs.ssl=true
mongo.atlasbackup.encryptedCredentials=
mongo.atlasbackup.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.atlasbackup.ssl=
mongo.automationcore.encryptedCredentials=
mongo.automationcore.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.automationcore.ssl=
mongo.automationstatus.encryptedCredentials=
mongo.automationstatus.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.automationstatus.ssl=
mongo.authz.encryptedCredentials=
mongo.authz.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.authz.ssl=
mongo.backupdb.encryptedCredentials=true
mongo.backupdb.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27018,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27018,mms-db-internal-6.us-east-2.aws.cloud-internal.10gen.cc:27018/?ssl=true&replicaSet=BackupDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.backupdb.ssl=true
mongo.backuplogs.encryptedCredentials=true
mongo.backuplogs.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27018,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27018,mms-db-internal-6.us-east-2.aws.cloud-internal.10gen.cc:27018/?ssl=true&replicaSet=BackupDB&authSource=admin&retryWrites=false&retryReads=false&w=1&uuidRepresentation=standard
mongo.backuplogs.ssl=true
mongo.backupstatus.encryptedCredentials=true
mongo.backupstatus.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27018,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27018,mms-db-internal-6.us-east-2.aws.cloud-internal.10gen.cc:27018/?ssl=true&replicaSet=BackupDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.backupstatus.ssl=true
mongo.backupusage.encryptedCredentials=
mongo.backupusage.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.backupusage.ssl=
mongo.billingauditing.encryptedCredentials=
mongo.billingauditing.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.billingauditing.ssl=
mongo.chartsmetadata.encryptedCredentials=
mongo.chartsmetadata.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.chartsmetadata.ssl=
mongo.cloudconf.encryptedCredentials=
mongo.cloudconf.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.cloudconf.ssl=
mongo.metricsstats.encryptedCredentials=true
mongo.metricsstats.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.metricsstats.ssl=true
mongo.mmsdbevents.encryptedCredentials=
mongo.mmsdbevents.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsdbevents.ssl=
mongo.mmsdbalerts/mmsdbclusterdescriptions.encryptedCredentials=
mongo.mmsdbalerts/mmsdbclusterdescriptions.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsdbalerts/mmsdbclusterdescriptions.ssl=
mongo.mmsdbemails.encryptedCredentials=
mongo.mmsdbemails.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsdbemails.ssl=
mongo.mmsdbautomation.encryptedCredentials=
mongo.mmsdbautomation.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsdbautomation.ssl=
mongo.mmsdbbilling.encryptedCredentials=
mongo.mmsdbbilling.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsdbbilling.ssl=
mongo.mmsdbcloudproviders.encryptedCredentials=
mongo.mmsdbcloudproviders.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&waitQueueTimeoutMS=120000&uuidRepresentation=standard
mongo.mmsdbcloudproviders.ssl=
mongo.mmsdbcollstatslatencyconfig.encryptedCredentials=true
mongo.event-service.encryptedCredentials=true
mongo.event-service.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.event-service.ssl=true
mongo.mmsdbcollstatslatencyconfig.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=1&uuidRepresentation=standard
mongo.mmsdbcollstatslatencyconfig.ssl=true
mongo.mmsdbcollstatslatencyrrd.encryptedCredentials=true
mongo.mmsdbcollstatslatencyrrd.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.mmsdbcollstatslatencyrrd.ssl=true
mongo.realmmetricsstate/serverlesschartselect/metricsmisc/metricsmisc2/discovery/tokenization/controlledfeatures.encryptedCredentials=
mongo.realmmetricsstate/serverlesschartselect/metricsmisc/metricsmisc2/discovery/tokenization/controlledfeatures.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.realmmetricsstate/serverlesschartselect/metricsmisc/metricsmisc2/discovery/tokenization/controlledfeatures.ssl=
mongo.iam.encryptedCredentials=
mongo.iam.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.iam.ssl=
mongo.mmsalertconfig.encryptedCredentials=
mongo.mmsalertconfig.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsalertconfig.cutover.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsalertconfig.ssl=
mongo.mmsdbftsrrd.encryptedCredentials=true
mongo.mmsdbftsrrd.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.mmsdbftsrrd.ssl=true
mongo.mmsdbjobs.encryptedCredentials=
mongo.mmsdbjobs.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsdbjobs.ssl=
mongo.realtimemeasurements/dataexplorer/logcollection.encryptedCredentials=
mongo.realtimemeasurements/dataexplorer/logcollection.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.realtimemeasurements/dataexplorer/logcollection.ssl=
mongo.mmsdbmetering.encryptedCredentials=
mongo.mmsdbmetering.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsdbmetering.ssl=
mongo.mmsdbrrdcache/iadbpings.encryptedCredentials=
mongo.mmsdbrrdcache/iadbpings.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.mmsdbrrdcache/iadbpings.ssl=
mongo.dbprofile.encryptedCredentials=
mongo.dbprofile.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.dbprofile.ssl=
mongo.atlaslogsmigration/mmsdbqueues.encryptedCredentials=
mongo.atlaslogsmigration/mmsdbqueues.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.atlaslogsmigration/mmsdbqueues.ssl=
mongo.mmsdbrealmrrd.encryptedCredentials=true
mongo.mmsdbrealmrrd.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.mmsdbrealmrrd.ssl=true
mongo.mmsdbrrd.encryptedCredentials=true
mongo.mmsdbrrd.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.mmsdbrrd.ssl=true
mongo.mmsdbserverlessrrd.encryptedCredentials=true
mongo.mmsdbserverlessrrd.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.mmsdbserverlessrrd.ssl=true
mongo.ratelimits.encryptedCredentials=true
mongo.ratelimits.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.ratelimits.ssl=true
mongo.mmsrt.encryptedCredentials=true
mongo.mmsrt.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27018,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27018,mms-db-internal-6.us-east-2.aws.cloud-internal.10gen.cc:27018/?ssl=true&replicaSet=BackupDB&authSource=admin&retryWrites=false&retryReads=false&connectTimeoutMS=5000&socketTimeoutMS=30000&waitQueueTimeoutMS=100&waitQueueMultiple=1&serverSelectionTimeoutMS=2000&w=1&uuidRepresentation=standard
mongo.mmsrt.ssl=true
mongo.monitoringdiagnostics/iaaccesslogs.encryptedCredentials=true
mongo.monitoringdiagnostics/iaaccesslogs.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.monitoringdiagnostics/iaaccesslogs.ssl=true
mongo.monitoringftstelemetry.encryptedCredentials=true
mongo.monitoringftstelemetry.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.monitoringftstelemetry.ssl=true
mongo.monitoringstatus.encryptedCredentials=
mongo.monitoringstatus.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.monitoringstatus.ssl=
mongo.sessions.encryptedCredentials=
mongo.sessions.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.sessions.ssl=
mongo.nds.encryptedCredentials=
mongo.nds.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.nds.ssl=
mongo.fts.encryptedCredentials=
mongo.fts.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.fts.ssl=
mongo.atlasndsstatus.encryptedCredentials=
mongo.atlasndsstatus.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.atlasndsstatus.ssl=
mongo.platformdiagnostics.encryptedCredentials=true
mongo.platformdiagnostics.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryReads=false&retryWrites=false&w=1&uuidRepresentation=standard
mongo.platformdiagnostics.ssl=true
mongo.realmdiscovery.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.realmdiscovery.ssl=
mongo.realmdiscovery.encryptedCredentials=
mongo.oa.encryptedCredentials=
mongo.oa.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.oa.ssl=
mongo.adl.encryptedCredentials=
mongo.adl.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.adl.ssl=
mongo.atlasbilling.encryptedCredentials=
mongo.atlasbilling.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.atlasbilling.ssl=
mongo.mmsdblogtooling.encryptedCredentials=true
mongo.mmsdblogtooling.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.mmsdblogtooling.ssl=true
mongo.iaquerystats.encryptedCredentials=true
mongo.iaquerystats.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.iaquerystats.ssl=true
mongo.abbrvslowlogs.encryptedCredentials=true
mongo.abbrvslowlogs.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.abbrvslowlogs.ssl=true
mongo.ccpa.encryptedCredentials=true
mongo.ccpa.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.ccpa.ssl=true
mongo.iaslowlogs.encryptedCredentials=true
mongo.iaslowlogs.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27198,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-5.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-7.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-9.us-east-2.aws.cloud-internal.10gen.cc:27198,mms-db-internal-11.us-east-2.aws.cloud-internal.10gen.cc:27198/?ssl=true&authSource=admin&retryWrites=false&w=1&retryReads=false&uuidRepresentation=standard
mongo.iaslowlogs.ssl=true
mongo.ssl=true
mongo.encryptedCredentials=true
mongodb.disable.numa=true
mongodb.release.autoDownload.enterprise=true
mongodb.release.autoDownload.rc=true
mongodb.release.autoDownload=true
mongodb.release.modulePreference=communityRequired
mongodb.release.publicKeyFile=classpath://mongodb-inc-server.pub
mongodb.ssl.CAFile=/etc/ssl/certs/ca-bundle.crt
mongot.location=https://internal-downloads.mongodb.com/search/
mongotools.version=100.13.0
# `/prod/*` is the release bucket for mongotune binaries
mongotune.location=https://internal-downloads.mongodb.com/mongotune/prod/
mongotune.loglevel=INFO
nds.mongotune.minAgentVersion=13.35.2.9529-1
mongosh.version=2.5.6
monitoring.agent.location=https://s3.amazonaws.com/mongodb-mms-build-agent/releases/${env}/
monitoring.agent.location.atlas=https://internal-downloads.mongodb.com/automation-agent/releases/${env}/
monitoring.agent.minimumVersion=5.0.0.309
monitoring.agent.version=7.2.0.488-1
morphia.search.packages=com.xgen.cloud.common.featureFlag._public.model,com.xgen.svc.mms.model.metrics.realtime,com.xgen.svc.mms.deployment.auth,com.xgen.svc.mms.model.performanceadvisor,com.xgen.cloud.activity._public.model.alert,com.xgen.cloud.activity._public.model.alert.config,com.xgen.svc.mms.model,com.xgen.svc.atm.model,com.xgen.svc.mms.model.performanceadvisor.autoindexing,com.xgen.cloud.atm.core._public.model,com.xgen.cloud.deployment._public.model,com.xgen.cloud.monitoring.topology._public.model,com.xgen.cloud.organization._public.model,com.xgen.cloud.atm.core._public.model.status,com.xgen.cloud.billingplatform.activity._public.alert,com.xgen.cloud.billingplatform.activity._public.alert.config,com.xgen.cloud.billingplatform.activity._public.audit,com.xgen.cloud.billingplatform.activity._public.event,com.xgen.cloud.billingplatform.audit._public.model,com.xgen.cloud.billingplatform.invoice._public.model,com.xgen.cloud.billingplatform.model.cloudprovider._public.aws,com.xgen.cloud.billingplatform.model.cloudprovider._public.azure,com.xgen.cloud.billingplatform.model.plan._public.model,com.xgen.cloud.dataexport._public.model,com.xgen.cloud.partners.registration._public.model,com.xgen.cloud.payments.netsuite._public.models,com.xgen.cloud.revrec.selfserve._private.model,com.xgen.svc.mms.model.billing,com.xgen.svc.mms.model.billing.reporting,com.xgen.svc.mms.model.marketing,com.xgen.cloud.common.explorer._public.model
nds.acme.accountDocument=
nds.acme.le.accountDocument=
nds.acme.gts.accountDocument=
nds.atlasproxy.conf.maxProcessedAccounts=100
nds.atlasproxy.conf.tlsPEMKeyFile=/etc/pki/tls/private/proxy.pem
nds.atlasproxy.restartProxyProcessOnFallbackCertRotation=false
nds.admin.reasonsRequireJira=false
nds.admin.dataPlaneAccessRequestsOnlineValidation=false
nds.admin.createRespectProtectedHoursMaintenanceAdminUi.enabled=true
nds.agent.envoyConfigServer.enabled=true
nds.agent.envoyConfigServer.nodeId=atlas-envoy
nds.agent.envoyConfigServer.port=9902
nds.agent.envoyConfigServer.adminPort=9901
nds.agent.envoyConfigServer.promExternalPort=27018
nds.agent.envoyConfigServer.promInternalPort=9945
nds.agent.envoyConfigServer.gatewayProxyExternalPort=27014
nds.agent.envoyConfigServer.mongotPromPort=9946
nds.agent.envoyConfigServer.searchEnvoyAdminPort=9905
nds.agent.envoyConfigServer.tlsCertConfigPath=/etc/envoy/sds.yaml
nds.agent.envoyConfigServer.tlsFilterServerName=*.mongo.com
nds.autoScaling.compute.initiateScaleDownWindow.days=MONDAY,TUESDAY,WEDNESDAY,THURSDAY,FRIDAY,SATURDAY,SUNDAY
nds.autoScaling.compute.initiateScaleDownWindow.hours=00:00,23:59
nds.autoScaling.burstable.compute.newClusterEnabled=true
nds.aws.al2023.newGroups=false
nds.aws.billingReport.bucketName=
nds.aws.billingReport.name=
nds.aws.billingReport.prefix=custom-reports
nds.aws.billingReport.regionName=US_EAST_1
nds.aws.dataValidation.instanceSize=R40
nds.aws.dataValidation.min.diskSizeGB=1024
nds.aws.dns.domain=mongo.com
nds.aws.dns.hostedzoneid=Z015217239V5WRHQM18RU
nds.azure.al2023.newGroups=false
nds.azure.dataValidation.instanceSize=M40
nds.azure.dataValidation.min.diskSizeGB=1024
nds.azure.dns.domain=azure.mongo.com
nds.azure.dns.hostedzoneid.private=Z07919582EFWUHZP24E4E
nds.azure.dns.hostedzoneid.public=Z076633029V6I9B2XSG9F
nds.backup.snapshots.expiration_extend_minutes=60
nds.backup.snapshots.aws.fasterRestoreJobKmsKeyArnTemplate=arn:aws:kms:%s:%s:alias/cloud-internal-volume-encryption-key
nds.backup.snapshots.restore.minimum_time_needed_for_restore_minutes=30
nds.backup.snapshots.aws.copySnapshotKmsKeyArnTemplate=arn:aws:kms:%s:931791181356:alias/cps-snapshot-copy
nds.corruptionDetection.dataValidation.dailySamplePercent=0.0
nds.corruptionDetection.dataValidation.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dataValidation.minClusterAgeDays=182
nds.corruptionDetection.dbCheck.dailySamplePercent=0.0
nds.corruptionDetection.dbCheck.minDaysBetweenClusterValidations=182
nds.corruptionDetection.dbCheck.minClusterAgeDays=182
nds.corruptionDetection.checkMetadataConsistency.dailySamplePercent=0.0
nds.corruptionDetection.checkMetadataConsistency.minDaysBetweenClusterValidations=182
nds.corruptionDetection.checkMetadataConsistency.minClusterAgeDays=182
nds.connectionEstablishmentRateLimiting.newGroups=false
nds.criticalMaintenance.job.enabled=true
nds.dataValidation.maxWorkingRecords=100
nds.adminEmailAddress=<EMAIL>
nds.externalcaching.redis.cacheEnabled=false
nds.gateway.proxy.enabled=false
nds.gcp.SnapshotBillingReport.dataset=mongodb_net_billing
nds.gcp.SnapshotBillingReport.projectId=cloud-prod-root
nds.gcp.billingReport.bucketName=mongodb-net-billing-reports
nds.gcp.billingReport.prefix=mongodb-net-billing-
nds.gcp.dataValidation.instanceSize=M40
nds.gcp.dataValidation.min.diskSizeGB=1024
nds.gcp.dns.domain=gcp.mongo.com
nds.gcp.dns.hostedzoneid.crosscloud.private=Z0644341UCT6D0J0VCQI
nds.gcp.dns.hostedzoneid.private=Z064396213YPSKW1CBJ6G
nds.gcp.dns.hostedzoneid.public=Z07934662VOX0VXKIN1NZ
nds.gcp.projectCreate.count=7500
nds.gcp.al2023.newGroups=false
nds.atlasPrioritizeHavingPrimaryOnUpscaledNode.newGroups=true
nds.privateLinkProxyProtocolAws.newGroups=true
nds.gov.us.enabled=false
nds.gov.us.siteName=Atlas Government
nds.healthCheck.staleJobThreshold.hours=1
nds.images.s3Bucket.aws.accesskey=<SECRETMANAGER>
nds.images.s3Bucket.aws.secretkey=<SECRETMANAGER>
nds.images.s3Bucket.aws.local-gov.accesskey=
nds.images.s3Bucket.aws.local-gov.secretkey=
nds.images.gcp.projectId=atlas-images-prod
nds.instances.advancedTesting.allow=false
nds.instances.heracles.s3KeyId=<SECRETMANAGER>
nds.instances.heracles.s3SecretKey=<SECRETMANAGER>
nds.instances.heracles.s3Bucket=<SECRETMANAGER>
nds.instances.heracles.s3Region=<SECRETMANAGER>
nds.instances.heracles.keyring=mQENBGBHi5QBCAC0PKQHkZnhfbd7pfQtMEPWCMLI5DbkSZZvQD909dLsfRqYYijV7igUjulvE7eKATuKRRa7X+v2ZHmOBsHBB1FvCSuhawYjdlZAbYqhmRiFuwN9rEQLcfFcf1PhpO3GG33iUnuB5TjncanQ4TJ4dnbxMeOF2BCKAqNPXfa183ZnlBhtXFIVrdk6uWFkVmkrsCu4+34Ndf+lCcrJRtSczM7SpTNJpFUzLQixgVQHLv3VkhFq7psY12kUWHL+KbFNA5k+N5zVz/Z7c1DxKnmCp8FouyJm+CMJFTI17gjRBbTyQKvMv1HONENaRLetMpK8t8iXhp0pgrTmamu7jJcJ3/HhABEBAAG0D3NyZUBtb25nb2RiLmNvbYkBTgQTAQgAOBYhBDMp6PyrqCi2rsQxlpEuLUAXXRIqBQJgR4uUAhsDBQsJCAcCBhUKCQgLAgQWAgMBAh4BAheAAAoJEJEuLUAXXRIqmZkH/0MWYehhQ0dDo+lMknlGIrtsWWSQKiuEcrVVq9gNGSWpqPFkv5bi486wue5fbf6J9o7NzAag/xokUfzSKXbILJbcbFPvhw8P2qscRx6wchqrK9NtXFFvSzI3GM+xtZVRWR2X9WGWUV6Y8ck9L94G3wJtYjb1qPN1+mOzjSa3p/qkoevPCHtnaKu9b+fcrFP/i6LB0gvfHwTNPFig06DO4V3aznnaQXYbKTi2+OOfOLk72Xk1vW6HIzMi2L99bPVY1bMPRBHXE8q5HsREl9OZ8VST1GP6wQjNep/hbt+ZBcP+3nJ+p+57PH5ACLPlMTWpaqP+jYud1ACQQzlMjbEZLsE=
nds.instances.secret-names=aws-creds,aws,log-uploader-keys,repo-gateway,s3logwriter-keys,firehoselogwriter-keys,wildcard-cert-pem,tenable,clamav-db,deviceSyncDebugAccessSharedSecret
nds.instances.secrets.CN.wildcard-cert-pem=BLOCKED
nds.instances.secrets.aws.aws-creds=<SECRETMANAGER>
nds.instances.secrets.aws.aws=<SECRETMANAGER>
nds.instances.secrets.aws.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.aws.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.aws.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.us-gov-west-1.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.us-gov-east-1.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.aws.firehoselogwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.us-gov-west-1.firehoselogwriter-keys=<SECRETMANAGER>
nds.instances.secrets.aws.us-gov-east-1.firehoselogwriter-keys=<SECRETMANAGER>
nds.instances.secrets.azure.aws-creds=<SECRETMANAGER>
nds.instances.secrets.azure.aws=<SECRETMANAGER>
nds.instances.secrets.azure.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.azure.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.azure.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.azure.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.azure.firehoselogwriter-keys=
nds.instances.secrets.gcp.aws-creds=<SECRETMANAGER>
nds.instances.secrets.gcp.aws=<SECRETMANAGER>
nds.instances.secrets.gcp.log-uploader-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.repo-gateway=<SECRETMANAGER>
nds.instances.secrets.gcp.s3logwriter-keys=<SECRETMANAGER>
nds.instances.secrets.gcp.wildcard-cert-pem=<SECRETMANAGER>
nds.instances.secrets.gcp.firehoselogwriter-keys=
nds.instances.secrets.clamav-db=<SECRETMANAGER>
nds.instances.secrets.tenable=<SECRETMANAGER>
nds.instances.secrets.deviceSyncDebugAccessSharedSecret=
nds.instances.shared.version=8.0
nds.instances.serverless.size=SERVERLESS_V2
nds.instances.os.aws=AL2
nds.instances.os.azure=AL2
nds.instances.os.gcp=AL2
nds.instances.provisioningLogUpload.enabled=true
nds.instances.provisioningLogUpload.bucket=atlas-provisioning-logs-us-east-2-internal
nds.instances.anomaliesBucket=atlas-anomalies-logs-us-east-2-internal
nds.jira.uri=
nds.jira.privateAccessToken=
nds.jira.bypassJiraStagingFirewallHeader=
nds.jira.bypassJiraStagingFirewallValue=
nds.leakedItem.cleanup.retries=3
nds.leakedItem.cleanup.mode=CRON
nds.leakedItem.detection.aws.mode=CRON
nds.leakedItem.detection.azure.mode=CRON
nds.leakedItem.detection.gcp.mode=CRON
nds.leakedItem.detection.gcp.chunkSize=900
nds.leakedItem.detection.gcp.projectsProcessedPerIteration=50
nds.liveImport.mongosyncLog.ttlDays=730
nds.liveImport.mongosync.version=1.15.0
nds.liveImport.mongosync.latestVersion=1.15.0
nds.pushLiveImport.mongosync.version=1.13.1
nds.liveImport.mongomirror.version=0.13.1
nds.liveImport.mongomirror.dockerImageTag=v0.13.1-202411011952-80adf99
nds.liveImport.kubeResourceCleaner.enabled=true
nds.liveImport.mongosyncWorkingDir=/var/log/atlas-live-import-internal
nds.liveImport.mongosync.binaryPathPrefix=/opt/10gen/mongosync
nds.mongosync.log.verbosity=DEBUG
nds.liveImport.servers.hasPublicIp=true
nds.liveImport.oplogBufferDir=/srv/10gen/atlas-import-internal/buffer
nds.liveImport.publicIpOnly=true
nds.liveImport.sudoUser=atlas-import-internal
nds.liveImport.workingDir=/var/log/atlas-import-internal
nds.liveImport.mongomirror.oplogBatchSize=1000
nds.metrics.deliverySystemForBilling.enabled=false
nds.mongomirror.log.verbosity=2
nds.mtm.autoscale.capacity.enabled=true
nds.serverless.mtm.autoscale.capacity.enabled=false
nds.mtm.lowThreshold.m0=250
nds.mtm.lowThreshold.m2=0
nds.mtm.lowThreshold.m5=0
nds.mtm.organization.id=
nds.serverless.mtm.organization.id=
nds.okta.oidc.testAuthorizationServer.apiKey=
nds.orphaned.ip.keepHours=36
nds.planner.group.interval.short=86400000
nds.planner.group.interval.medium=172800000
nds.planner.group.interval.long=604800000
nds.planning.jobQueue.maxThreadCount=5
nds.serverless.feature.enabled=false
nds.flex.feature.enabled=false
nds.flex.migration.warning.enabled=false
nds.flex.serverless.migration.cron.enabled=false
nds.flex.shared.migration.cron.enabled=false
nds.flex.migration.cron.schedule=0 * 13-20 ? * 2-6
nds.serverless.mtm.consumption.debug.logging.enabled=false
nds.serverless.trace.autoscale.enabled=false
nds.serverless.trace.autoscale.exporter=
nds.serverless.trace.autoscale.prefix=
nds.serverless.trace.autoscale.processor=
nds.serverless.supportsLBDeploymentIdOnEnvoyInstance=false
nds.serverless.privateNetworking.supportedCloudProviders=
nds.serverless.version.upgrade.window.enabled=
nds.serverless.version.upgrade.window.size=
nds.serverless.envoy.healthcheck.max.thread.count=1
nds.serverless.group.limits.maxResidentMTMs=85
nds.serverless.load.deadlineSeconds=900
nds.serverless.load.maxThreads=20
nds.serverless.load.minThreads=5
nds.serverless.metrics.mocks.enabled=false
nds.serverless.metrics.api.version=v2
nds.serverless.mtm.autoscale.maxInstanceSize=M80
nds.serverless.mtm.load.updateDocuments.enabled=false
nds.serverless.mtm.limits.maxResidentTenants=2000
nds.serverless.pool.limits.maxResidentMTMs=100
nds.serverless.pool.limits.maxResidentMTMsForAutoScaleMTMCapacity=25
nds.serverless.pool.limits.maxResidentTenants=6000
nds.serverless.pool.limits.maxResidentTenantsThreshold=.2
nds.serverless.mtm.backingInstanceSize=M40
nds.mtm.sentinel.cron.enabled=false
nds.serverless.tenant.privateNetworking.reservedButUnavailablePrivateEndpointExpirationThreshold=P7D
nds.serverless.serverlessUpgradeToDedicated.enabled=false
nds.serverParams.shouldSetRateLimiterParams=true
nds.svc.ipToRegionMapping.executorEnabled=true
nds.tenantUpgrade.s3BucketName=
nds.xdsCentralUrl=
nds.fleetAttributeCollection=true
netsuite.account.atlas.revenue.internal.id=1463
netsuite.account.cloud.receivable.internal.id=1541
netsuite.account.contra.liability.internal.id=1686
netsuite.account.mms.revenue.internal.id=257
netsuite.account.reserve.contra.internal.id=1319
netsuite.account.refund.salestax.debit.internal.id=109
netsuite.account.refund.vat.debit.internal.id=160
netsuite.api.accountNumber=1169967
netsuite.api.consumerKey=<SECRETMANAGER>
netsuite.api.consumerSecret=<SECRETMANAGER>
netsuite.app.endpoint=https://1169967.app.netsuite.com/
netsuite.api.endpoint=https://1169967.suitetalk.api.netsuite.com/
netsuite.api.token=<SECRETMANAGER>
netsuite.api.tokenSecret=<SECRETMANAGER>
netsuite.currency.usd.item.id=1
netsuite.currency.eur.item.id=4
netsuite.department.d15000.internal.id=213
netsuite.department.d15100.internal.id=259
netsuite.location.dublin.irl.internal.id=10
netsuite.location.nyc.usa.internal.id=4
netsuite.subsidiary.mongo.inc.item.id=1
netsuite.subsidiary.mongo.ltd.item.id=15
netsuite.rest.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/record/v1
netsuite.rest.query.endpoint=https://1169967-sb1.suitetalk.api.netsuite.com/services/rest/query/v1
netsuite.restlet.endpoint=https://1169967.restlets.api.netsuite.com/app/site/hosting/restlet.nl
netsuite.restlet.tokenEndpoint=https://1169967.suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token
netsuite.restlet.cert.privateKey=
netsuite.restlet.cert.id=
netsuite.restlet.cert.scope=restlets,rest_webservices,suite_analytics
officeIps.service.enabled=false
officeIps.service.urlMeshEnvVar=KUBE_OFFICEIPS_SERVICE_KUBE_INTERNAL_US_EAST_2_AWS_CLOUD_10GEN_CC
okta.account.client.id=<SECRETMANAGER>
okta.account.client.secret=<SECRETMANAGER>
okta.api.token.emp-qa=
okta.api.token=<SECRETMANAGER>
okta.client.id=<SECRETMANAGER>
okta.client.secret=<SECRETMANAGER>
okta.cmab.client.secret=<SECRETMANAGER>
okta.cmab.client.id=0oabsjfygzEYUqVGC297
okta.internal.authorization.server.id=aus4k4jv00hWjNnps297
okta.external.authorization.server.id=default
okta.internal.domain=https://corp.mongodb.com
okta.external.domain=https://auth.mongodb.com
okta.idpDiscoveryPolicyId=00p1c091n88Tz0HJq297
okta.idpId=00o1bkrirn0R4SMFc297
okta.requireNonce=true
okta.restrictedEnv.customerGroup=
okta.restrictedEnv.userGroupNames=10gen
okta.sessionDiscovery.enabled=true
okta.profile.target.id=oty1bkris6I92FPzp297
opsgenie.api.eu.url=https://api.eu.opsgenie.com/v2/alerts
opsgenie.api.url=https://api.opsgenie.com/v2/alerts
opsgenie.api.us.url=https://api.opsgenie.com/v2/alerts
pagerduty.events.api.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.us.v2.url=https://events.pagerduty.com/v2/enqueue
pagerduty.events.api.eu.v2.url=https://events.eu.pagerduty.com/v2/enqueue
pagerduty.api.regions.options=US,EU
pagerduty.app.id=P7XGM67
ping.queue.size=150
ping.thread.count=6
ping.throttling.enabled=true
prom.saving.publicMetrics=false
provision.allowReaperOptions=false
provision.allowTerminateAll=false
provision.allowUrlOverride=false
provision.dns.resolution.wait.secs=30
provision.dns.zone=mongodbdns.com
provision.includeRandomPin=true
provision.script.setup.name=run_atm_linux_agent.sh
qualtrics.api.token=
queryengine.aws.assumeRoleAccessKey=<SECRETMANAGER>
queryengine.aws.assumeRoleSecretKey=<SECRETMANAGER>
queryengine.aws.assumeRoleUserARN=<SECRETMANAGER>
streams.processManager.enabled=false
iam.gcp.reCaptcha.apiKey=
iam.gcp.reCaptcha.project.id=iam-recaptcha-dev
reCaptcha.enabled.atlas=false
reCaptcha.enabled.login=false
iam.reCaptcha.enterprise.login.enabled=false
iam.reCaptcha.login.minScore=0.5
iam.reCaptcha.registration.enabled=false
iam.reCaptcha.registration.minScore=0.5
iam.reCaptcha.external.registration.enabled=false
iam.reCaptcha.external.registration.minScore=0.5
reCaptcha.enabled.externalRegistration=false
reCaptcha.private.key=
reCaptcha.public.key=
realm.billing.api.url=
rrd.legacyIngestion.periodSeconds=27
segment.batch.url=https://api.segment.io/v1/batch
segment.clientSide.writeKey=tFLI8kTsjVV1Whb5D6Cgthsjp2f26tRF
segment.group.url=https://api.segment.io/v1/group
segment.identify.url=https://api.segment.io/v1/identify
segment.profile.url=https://profiles.segment.com/v1/spaces
segment.serverSide.accessToken=
segment.serverSide.personasSpaceId=spa_smteJ81u1XbJMRgmDR9Td5
segment.serverSide.writeKey=MKfzIHQzxwU6M0S7xldO0dc4EjtK4k97
segment.track.url=https://api.segment.io/v1/track
sfdc.api.campaignId=701A0000000q3z7IAA
sfdc.api.endpoint=https://login.salesforce.com/services/Soap/c/38.0/0DFA0000000PZRg
sfdc.api.integrationOwnerId=005A0000002yZoK
sfdc.api.password=<SECRETMANAGER>
sfdc.api.username=<SECRETMANAGER>
sfdc.sandbox=false
sfdc.sync.cron.enabled=false
sfsc.api.client.id=<SECRETMANAGER>
sfsc.api.client.secret=<SECRETMANAGER>
sfsc.api.endpoint=https://test.salesforce.com/services/Soap/c/38.0/00DK000000W4vnY
sfsc.api.password=<SECRETMANAGER>
sfsc.api.username=<SECRETMANAGER>
sfsc.sync.url=https://sf-sync.staging.corp.mongodb.com/cloud/org
slack.api.message.url=https://slack.com/api/
slack.oauth2.clientId=<SECRETMANAGER>
slack.oauth2.clientSecret=<SECRETMANAGER>
slack.oauth2.url=https://slack.com/api/oauth.access
stitch.analytics.api.url=
stitch.api.key=
stitch.billing.api.url=
stripe.accountId.ltd=acct_19NoKYJcsjvuVuBZ
stripe.accountId=acct_1fLcLdWa8akf0S84TMot
stripe.apiKey.ltd=
stripe.apiKey.inc=
stripe.pubKey.ltd=
stripe.pubKey.inc=
taxamo.api.privateToken=
taxamo.api.url=
stripe.webhook.updatecard.secret=
stripe.webhook.updatecard.secret.ltd=
support.appEmbedLink=https://support.mongodb.com/api/auth/oidc/initial
twilio.account.sid=<SECRETMANAGER>
twilio.auth.token=<SECRETMANAGER>
twilio.from.num=<SECRETMANAGER>
university.centralUrls=https://learn.mongodb.com,https://university.mongodb.com
victorops.alert.api.url=https://alert.victorops.com/integrations/generic/********/alert/
mms.backup.e2e.blockstore.aws.accesskey=
mms.backup.e2e.blockstore.aws.secretkey=
mms.backup.e2e.oplogstore.aws.accesskey=
mms.backup.e2e.oplogstore.aws.secretkey=
mms.backup.rollingReplacement.email=true
mms.featureFlag.backup.autoRecoveryUnsafeApplyOps=controlled
charts.cache.encryptedCredentials=
charts.cache.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
charts.cache.ssl=
charts.sample.encryptedCredentials=
charts.sample.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
charts.sample.ssl=
metering.centralUrl=https://cloud-internal.mongodb.com
metering.user.publicKey=<SECRETMANAGER>
metering.user.privateKey=<SECRETMANAGER>
mms.serverless.envoyInstanceSize=M30
mms.featureFlag.serverless.grpc.incrementalRollout.global.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.tenant.enabled=disabled
mms.featureFlag.serverless.grpc.incrementalRollout.continuous.enabled=disabled
mms.featureFlag.serverless.serverlessMtmEligibleForUpgradeToDedicated=disabled
mms.featureFlag.serverless.serverlessSharedUIOptionEnabled=disabled
mms.publicApi.prometheus.rateLimitGranularity=1
mms.publicApi.prometheus.rateLimitMaxPerRemoteAddress=120
mms.monitoring.performanceadvisor.serverless.autoindexing.groupPercentageAllowed=100
# The following three are used for product validation, and are therefore empty above dev.
mms.slack.test.channel=
mms.slack.test.token=
mms.alert.test.email.emailAddress=
mms.account.mfa.getMfaFactors.ratelimit.enabled=false
mms.account.mfa.getMfaFactors.ratelimit.maxHitsPerPeriod=60
mms.account.mfa.getMfaFactors.ratelimit.minutesPeriod=15
mms.account.mfa.enrollMfaFactor.ratelimit.enabled=false
mms.account.mfa.enrollMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.enrollMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaFactor.ratelimit.enabled=false
mms.account.mfa.activateMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.activateMfaFactor.ratelimit.minutesPeriod=15
mms.account.mfa.activateMfaPushFactor.ratelimit.enabled=false
mms.account.mfa.activateMfaPushFactor.ratelimit.maxHitsPerPeriod=30
mms.account.mfa.activateMfaPushFactor.ratelimit.minutesPeriod=15
mms.account.mfa.verifyMfaFactor.ratelimit.enabled=false
mms.account.mfa.verifyMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyMfaFactor.ratelimit.minutesPeriod=5
mms.account.mfa.verifyPushFactorChallenge.ratelimit.enabled=false
mms.account.mfa.verifyPushFactorChallenge.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.verifyPushFactorChallenge.ratelimit.minutesPeriod=5
mms.account.mfa.deleteMfaFactor.ratelimit.enabled=false
mms.account.mfa.deleteMfaFactor.ratelimit.maxHitsPerPeriod=10
mms.account.mfa.deleteMfaFactor.ratelimit.minutesPeriod=15
mms.account.auth.getAuthMfaState.ratelimit.enabled=false
mms.account.auth.getAuthMfaState.ratelimit.maxHitsPerPeriod=30
mms.account.auth.getAuthMfaState.ratelimit.minutesPeriod=5
mms.account.auth.verifyAuthMfa.ratelimit.enabled=false
mms.account.auth.verifyAuthMfa.ratelimit.maxHitsPerPeriod=10
mms.account.auth.verifyAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.resendAuthMfa.ratelimit.enabled=false
mms.account.auth.resendAuthMfa.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resendAuthMfa.ratelimit.minutesPeriod=5
mms.account.auth.cancelAuthVerify.ratelimit.enabled=false
mms.account.auth.cancelAuthVerify.ratelimit.maxHitsPerPeriod=5
mms.account.auth.cancelAuthVerify.ratelimit.minutesPeriod=15
mms.account.auth.cancelAuthVerify.ratelimit.payload.jsonPath=$.username
mms.account.auth.checkPassword.ratelimit.enabled=false
mms.account.auth.checkPassword.ratelimit.maxHitsPerPeriod=10
mms.account.auth.checkPassword.ratelimit.minutesPeriod=5
nds.embeddedConfig.minMongoDBVersion=8.0
nds.embeddedConfig.maxShardCount=3
mms.account.auth.socialSSO.ratelimit.enabled=false
mms.account.auth.socialSSO.ratelimit.maxHitsPerPeriod=50
mms.account.auth.socialSSO.ratelimit.minutesPeriod=5
mms.user.redirectInvite.ratelimit.enabled=false
mms.user.redirectInvite.ratelimit.maxHitsPerPeriod=60
mms.user.redirectInvite.ratelimit.minutesPeriod=15
mms.user.redirectInvite.ratelimit.pathParam.parameterName=username
mms.user.resetSend.ratelimit.enabled=false
mms.user.resetSend.ratelimit.maxHitsPerPeriod=10
mms.user.resetSend.ratelimit.minutesPeriod=15
mms.user.resetSend.ratelimit.payload.jsonPath=$.username
mms.account.profile.deleteEmailChangeRequest.ratelimit.enabled=false
mms.account.profile.deleteEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.deleteEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.createEmailChangeRequest.ratelimit.enabled=false
mms.account.profile.createEmailChangeRequest.ratelimit.maxHitsPerPeriod=5
mms.account.profile.createEmailChangeRequest.ratelimit.minutesPeriod=15
mms.account.profile.checkPasswordForProfileChange.ratelimit.enabled=false
mms.account.profile.checkPasswordForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.checkPasswordForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyFactorForProfileChange.ratelimit.enabled=false
mms.account.profile.verifyFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyFactorForProfileChange.ratelimit.minutesPeriod=15
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.enabled=false
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.verifyPushFactorForProfileChange.ratelimit.minutesPeriod=15
cypress.aws.ses.accesskey=
cypress.aws.ses.secretkey=
# Experimentation Properties
# analytics.enabled must be true and pholiota properties must be filled if setting the experiments.enabled property to true
analytics.enabled=false
experiments.enabled=true
pholiota.getExperimentsUrl=https://us-east-1.aws.data.mongodb-api.com/app/pholiota-ol-prod-khixd/endpoint/getExperiments?expType=IFR
pholiota.mmsApiKey=<SECRETMANAGER>
pholiota.mmsHttpReferer=https://INTERNAL-mms/
mms.account.profile.getOktaUser.ratelimit.enabled=false
mms.account.profile.getOktaUser.ratelimit.maxHitsPerPeriod=20
mms.account.profile.getOktaUser.ratelimit.minutesPeriod=1
mms.account.profile.updatePassword.ratelimit.enabled=false
mms.account.profile.updatePassword.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updatePassword.ratelimit.minutesPeriod=1
mms.account.profile.updateProfile.ratelimit.enabled=false
mms.account.profile.updateProfile.ratelimit.maxHitsPerPeriod=5
mms.account.profile.updateProfile.ratelimit.minutesPeriod=1
mms.account.profile.activateFactorForProfileChange.ratelimit.enabled=false
mms.account.profile.activateFactorForProfileChange.ratelimit.maxHitsPerPeriod=5
mms.account.profile.activateFactorForProfileChange.ratelimit.minutesPeriod=1
mms.user.getUserPartnerIntegrationsData.ratelimit.enabled=false
mms.user.getUserPartnerIntegrationsData.ratelimit.maxHitsPerPeriod=100
mms.user.getUserPartnerIntegrationsData.ratelimit.minutesPeriod=20
mms.user.getUserPartnerIntegrationsData.ratelimit.pathParam.parameterName=username
mms.user.registerCall.ratelimit.enabled=false
mms.user.registerCall.ratelimit.maxHitsPerPeriod=100
mms.user.registerCall.ratelimit.minutesPeriod=20
mms.organization.setSecurityContact.ratelimit.enabled=false
mms.organization.setSecurityContact.ratelimit.maxHitsPerPeriod=10
mms.organization.setSecurityContact.ratelimit.minutesPeriod=5
mms.account.auth.unauthedResendVerificationEmail.ratelimit.enabled=false
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.unauthedResendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.maxHitsPerPeriod=1
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.minutesPeriod=60
mms.account.auth.unauthedResendVerificationEmail.ratelimit.payload.jsonPath=$.username
mms.account.auth.resendVerificationEmail.ratelimit.enabled=false
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resendVerificationEmail.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resendVerificationEmail.ratelimit.user.maxHitsPerPeriod=1
mms.account.auth.resendVerificationEmail.ratelimit.user.minutesPeriod=60
mms.account.auth.resetPasswordRequestSend.ratelimit.enabled=false
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.resetPasswordRequestSend.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.minutesPeriod=20
mms.account.auth.resetPasswordRequestSend.ratelimit.payload.jsonPath=$.username
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.enabled=false
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.maxHitsPerPeriod=50
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.suspendableIp.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.maxHitsPerPeriod=10
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.minutesPeriod=20
mms.account.auth.createPasswordResetLinkAsAdmin.ratelimit.payload.jsonPath=$.username
mms.account.auth.resetComplete.ratelimit.enabled=false
mms.account.auth.resetComplete.ratelimit.maxHitsPerPeriod=10
mms.account.auth.resetComplete.ratelimit.minutesPeriod=20
mms.account.auth.resetComplete.ratelimit.payload.jsonPath=$.username
mms.user.mfa.auth.ratelimit.enabled=false
mms.user.mfa.auth.ratelimit.maxHitsPerPeriod=10
mms.user.mfa.auth.ratelimit.minutesPeriod=20
mms.authz.isAuthorized.ratelimit.enabled=false
mms.authz.isAuthorized.ratelimit.maxHitsPerPeriod=10
mms.authz.isAuthorized.ratelimit.minutesPeriod=1
mms.account.device.verifyDeviceCode.ratelimit.enabled=false
mms.account.device.verifyDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.verifyDeviceCode.ratelimit.minutesPeriod=20
mms.account.device.confirmDeviceCode.ratelimit.enabled=false
mms.account.device.confirmDeviceCode.ratelimit.maxHitsPerPeriod=100
mms.account.device.confirmDeviceCode.ratelimit.minutesPeriod=20
mms.api.organizations.updateOrganizationSettings.ratelimit.enabled=false
mms.api.organizations.updateOrganizationSettings.ratelimit.maxHitsPerPeriod=10
mms.api.organizations.updateOrganizationSettings.ratelimit.minutesPeriod=5
mms.api.private.account.device.generateCode.ratelimit.enabled=false
mms.api.private.account.device.generateCode.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.generateCode.ratelimit.minutesPeriod=20
mms.api.private.account.device.getToken.ratelimit.enabled=false
mms.api.private.account.device.getToken.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.getToken.ratelimit.minutesPeriod=20
mms.api.private.account.device.revoke.ratelimit.enabled=false
mms.api.private.account.device.revoke.ratelimit.maxHitsPerPeriod=100
mms.api.private.account.device.revoke.ratelimit.minutesPeriod=20
mms.api.private.email.sendToSecurityContact.ratelimit.enabled=true
mms.api.private.email.sendToSecurityContact.ratelimit.maxHitsPerPeriod=100
mms.api.private.email.sendToSecurityContact.ratelimit.minutesPeriod=20
mms.api.private.dbUser.validate.ratelimit.enabled=true
mms.api.private.dbUser.validate.ratelimit.maxHitsPerPeriod=100
mms.api.private.dbUser.validate.ratelimit.minutesPeriod=20
mms.api.private.telemetry.events.createEvents.ratelimit.enabled=true
mms.api.private.telemetry.events.createEvents.ratelimit.maxHitsPerPeriod=100
mms.api.private.telemetry.events.createEvents.ratelimit.minutesPeriod=1
mms.api.oauth.tokenManagement.ratelimit.enabled=true
mms.api.oauth.tokenManagement.ratelimit.maxHitsPerPeriod=10
mms.api.oauth.tokenManagement.ratelimit.minutesPeriod=1
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.enabled=false
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.maxHitsPerPeriod=20
mms.api.oauth.tokenManagement.ratelimit.ipWithClientId.globalserviceaccount.minutesPeriod=1
logCollection.maxDiskSpaceMB=102400
mms.monitoring.atlasPremiumRetention.retentionPolicyId=65cccc4b97e8da241e0eb739
nds.acme.newDedicatedClusterAllowMixedCerts=true
nds.acme.phase1RolloutToExistingClusters=true
aws.atlasSelfServeMarketplaceProductId=
aws.marketplaceSellerId=
mongo.integrations.encryptedCredentials=true
mongo.integrations.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.integrations.ssl=true
mongo.ui.encryptedCredentials=true
mongo.ui.mongoUri=mongodb://931d641116d9d557b6898e73bc56e47e9dadefd2a710fdb462bd15c41f5ee965-8b8a0c8671845a68feecb6d8ea8db197-6ac9de34e979fef801cfc8524f9700a3:<EMAIL>:27017,mms-db-internal-2.us-east-2.aws.cloud-internal.10gen.cc:27017,mms-db-internal-3.us-east-2.aws.cloud-internal.10gen.cc:27017/?ssl=true&replicaSet=AppDB&authSource=admin&retryWrites=false&retryReads=false&w=majority&uuidRepresentation=standard
mongo.ui.ssl=true
mms.azurenative.AzureNativeRateLimiting.ratelimit.enabled=true
mms.azurenative.AzureNativeRateLimiting.ratelimit.maxHitsPerPeriod=100
mms.azurenative.AzureNativeRateLimiting.ratelimit.minutesPeriod=5
mms.featureFlag.gcpArmAxion=disabled
mms.featureFlag.azureArmDpsv6Epsv6=disabled
mms.featureFlag.horizontalScalingAutoSharding=disabled
mms.featureFlag.gcpN4Family=disabled
mms.disaggregatedStorage.fetchLatestBuildFromDev.globalAdminApiSecret=
nds.shadowClusters.enabled=false
nds.gcp.provisionedIOPS.enabled=false
customer.metrics.service.baseUrl=https://observability-agents-internal.mongodb.com
mms.new.db.user.scramIterationCount=1300000
mms.new.db.user.scramSHA256IterationCount=600000
mms.robustNodeHorizonMappingEnabled=true
mms.github.publicips.refreshEnabled=true
communication.publisher.profile=aws
communication.publisher.awsRegion=us-east-2
communication.publisher.awsRoleArn=arn:aws:iam::************:role/communication-service-resources-internal/communication-service-queue-internal-aws-us-east-2
communication.messagebus.publisher.kinesis.streamName=communication-service-stream-internal-aws-us-east-2
communication.statusUpdate.publisher.kinesis.streamName=status-update-stream-internal-aws-us-east-2
eventbus.publisher.queueingEnabled=true
eventbus.publisher.awsRoleArn=arn:aws:iam::************:role/event-service-resources-internal/event-service-queue-producer-internal-aws-us-east-2
eventbus.publisher.kinesis.streamName=event-service-stream-internal-aws-us-east-2
eventbus.publisher.awsRegion=us-east-2
