<#include "../../../ftl/functions.ftl">

[
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 1)},
    "projectId" : ${oid("148")},
    "snapshotId" : ${oid("1")},
    "clusterName" : "TestCluster-FIXED",
    "clusterUniqueId": ${oid("17")},
    "sourceClusterName" : "TestCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(23, "hours")},
    "regionName": "US_EAST_2",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("148")},
      "targetDeploymentItemName": "TestCluster",
      "targetClusterName": "TestCluster",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("1")},
        "status": "COMPLETED",
        "timeAttached": ${json_midnight_minus(60, "days")},
        "detachedDiskId": "v-31431324124",
        "hostname": "test-host-1",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": true,
        "attachStats" : {
            "mongoStartupStats": {
                "seedRestore_restartWithOplogReplayParams" : {
                    "journalReplayDurationMs" : { "$numberLong": "1000" }
                }
            }
        }
      }
    ],
    "showRestoreReady": true,
    "provisionedDate" : ${json_midnight_minus(60, "days")},
    "timestamp" : ${json_midnight_minus(60, "days")},
    "expirationDate" : ${json_midnight_plus(60, "days")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 2)},
    "projectId" : ${oid("148")},
    "snapshotId" : ${oid("2")},
    "clusterName" : "TestCluster",
    "clusterUniqueId": ${oid("17")},
    "sourceClusterName" : "TestCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(22, "hours")},
    "regionName": "US_EAST_2",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("148")},
      "targetDeploymentItemName": "TestCluster",
      "targetClusterName": "TestCluster",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("2")},
        "status": "COMPLETED",
        "timeAttached": ${json_midnight_minus(60, "days")},
        "detachedDiskId": "v-31431324125",
        "hostname": "test-host-2",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": true,
        "attachStats" : {
            "mongoStartupStats": {
                "seedRestore_restartWithOplogReplayParams" : {
                    "journalReplayDurationMs" : { "$numberLong": "2000" }
                }
            }
        }
      }
    ],
    "showRestoreReady": true,
    "provisionedDate" : ${json_midnight_minus(60, "days")},
    "timestamp" : ${json_midnight_minus(60, "days")},
    "expirationDate" : ${json_midnight_plus(60, "days")}
 }
]
