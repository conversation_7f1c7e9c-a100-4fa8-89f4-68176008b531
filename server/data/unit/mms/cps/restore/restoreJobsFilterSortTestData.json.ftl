<#include "../../../ftl/functions.ftl">

[
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 1)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("1")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(23, "hours")},
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("200")},
      "targetDeploymentItemName": "targetCluster1",
      "targetClusterName": "targetCluster1",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("1")},
        "status": "COMPLETED",
        "timeAttached": ${json_now_minus(23, "hours")},
        "detachedDiskId": "v-test-1",
        "hostname": "test-host-1",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": true
      }
    ],
    "showRestoreReady": true,
    "provisionedDate" : ${json_now_minus(30, "minutes")},
    "timestamp" : ${json_now_minus(30, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 2)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("2")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(20, "hours")},
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("300")},
      "targetDeploymentItemName": "targetCluster2",
      "targetClusterName": "targetCluster2",
      "targetMdbVersion": "8.0.1",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("2")},
        "status": "COMPLETED",
        "timeAttached": ${json_now_minus(20, "hours")},
        "detachedDiskId": "v-test-2",
        "hostname": "test-host-2",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": true
      }
    ],
    "showRestoreReady": true,
    "provisionedDate" : ${json_now_minus(36, "minutes")},
    "timestamp" : ${json_now_minus(36, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 3)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("3")},
    "clusterName" : "sourceShardedCluster",
    "clusterUniqueId": ${oid("20")},
    "sourceClusterName" : "sourceShardedCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(23, "hours")},
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("400")},
      "targetDeploymentItemName": "targetCluster1",
      "targetClusterName": "targetCluster1",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("3")},
        "status": "COMPLETED",
        "timeAttached": ${json_now_minus(23, "hours")},
        "detachedDiskId": "v-test-3",
        "hostname": "test-host-3",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": true
      }
    ],
    "showRestoreReady": true,
    "provisionedDate" : ${json_now_minus(30, "minutes")},
    "timestamp" : ${json_now_minus(30, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")},
    "cloudProviders": ["AWS"],
    "members": [
      {
        "rsId": "sourceShardedCluster-config-0",
        "restoreJobId": ${oid("30")},
        "finishedDate": ${json_now_minus(23, "hours")}
      },
      {
        "rsId": "sourceShardedCluster-shard-0",
        "restoreJobId": ${oid("31")},
        "finishedDate": ${json_now_minus(23, "hours")}
      }
    ]
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 4)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("4")},
    "clusterName" : "sourceShardedCluster",
    "clusterUniqueId": ${oid("20")},
    "sourceClusterName" : "sourceShardedCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(20, "hours")},
    "regionName": "CENTRAL_US",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("500")},
      "targetDeploymentItemName": "targetCluster2",
      "targetClusterName": "targetCluster2",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "GCP",
    "attachStatus": [
      {
        "instanceId": ${oid("4")},
        "status": "COMPLETED",
        "timeAttached": ${json_now_minus(20, "hours")},
        "detachedDiskId": "v-test-4",
        "hostname": "test-host-4",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": true
      }
    ],
    "showRestoreReady": true,
    "provisionedDate" : ${json_now_minus(36, "minutes")},
    "timestamp" : ${json_now_minus(36, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")},
    "cloudProviders": ["GCP"],
    "members": [
      {
        "rsId": "sourceShardedCluster-config-0",
        "restoreJobId": ${oid("40")},
        "finishedDate": ${json_now_minus(20, "hours")}
      },
      {
        "rsId": "sourceShardedCluster-shard-0",
        "restoreJobId": ${oid("41")},
        "finishedDate": ${json_now_minus(20, "hours")}
      }
    ]
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 5)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("5")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(18, "hours")},
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("600")},
      "targetDeploymentItemName": "targetCluster3",
      "targetClusterName": "targetCluster3",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": null,
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("5")},
        "status": "COMPLETED",
        "timeAttached": ${json_now_minus(18, "hours")},
        "detachedDiskId": "v-test-5",
        "hostname": "test-host-5",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": true
      }
    ],
    "showRestoreReady": true,
    "provisionedDate" : ${json_now_minus(25, "minutes")},
    "timestamp" : ${json_now_minus(25, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 6)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("6")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(16, "hours")},
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("700")},
      "targetDeploymentItemName": "targetCluster4",
      "targetClusterName": "targetCluster4",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": "INTERNAL",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("6")},
        "status": "COMPLETED",
        "timeAttached": ${json_now_minus(16, "hours")},
        "detachedDiskId": "v-test-6",
        "hostname": "test-host-6",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": true
      }
    ],
    "showRestoreReady": true,
    "provisionedDate" : ${json_now_minus(20, "minutes")},
    "timestamp" : ${json_now_minus(20, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 7)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("7")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : null,
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("800")},
      "targetDeploymentItemName": "targetCluster5",
      "targetClusterName": "targetCluster5",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("7")},
        "status": "IN_PROGRESS",
        "timeAttached": null,
        "detachedDiskId": "v-test-7",
        "hostname": "test-host-7",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": false
      }
    ],
    "showRestoreReady": false,
    "provisionedDate" : ${json_now_minus(15, "minutes")},
    "timestamp" : ${json_now_minus(15, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 8)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("8")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(14, "hours")},
    "regionName": "US_EAST_1",
    "delivery": {
        "deliveryType": "STREAMING",
        "targetDeploymentItemName": "atlas-streaming-test",
        "targetClusterName": "streamingCluster",
        "targetProjectId": ${oid("900")},
        "targetProjectVisibility": "DEFAULT_VISIBLE",
        "isTargeted": true,
        "targetRsId": "atlas-streaming-test-shard-0",
        "systemUsersUUID": "d57ec50b-dadf-4d6d-b1e2-e03a0bee0d12",
        "systemRolesUUID": "b5933116-b404-4edc-ba6d-0bcdbb27d952",
        "targetMdbVersion": "7.0.1"
    },
    "instanceHardware": {
        "cloudProvider": "AWS",
        "instanceId": ${oid("8")},
        "memberIndex": 0,
        "createDate": ${json_now_minus(20, "minutes")},
        "lastUpdateDate": ${json_now_minus(15, "minutes")},
        "lastDiskModifyDate": null,
        "provisioned": true,
        "electable": true,
        "action": "NONE",
        "health": {},
        "isPaused": false,
        "biConnector": {
            "enabled": false,
            "readPreference": "secondary"
        },
        "rebootRequestedDate": null,
        "externalRebootRequestedDate": null,
        "restartRequestedDate": null
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "status": {
          "inProgress": 0,
          "succeeded": 1,
          "failed": 0,
          "totalBytes": { "$numberLong": "5000" },
          "requestStats": [
            {
              "id": 1,
              "downloadHostnamePort": "restore-streaming-test.absv3.mongodb.net:27017",
              "status": "Succeeded",
              "timeStart": ${json_now_minus(15, "hours")},
              "timeEnd": ${json_now_minus(14, "hours")},
              "downloadSpeedBytesPerSec": { "$numberLong": "5000" },
              "bytesStreamed": { "$numberLong": "5000" },
              "compressedSize": { "$numberLong": "5000" },
              "compressedDownloadSpeedBytesPerSec": { "$numberLong": "5000" }
            }
          ],
          "lastUpdateDate": ${json_now_minus(14, "hours")}
    },
    "showRestoreReady": true,
    "provisionedDate" : ${json_now_minus(20, "minutes")},
    "timestamp" : ${json_now_minus(20, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 9)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("9")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : true,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(12, "hours")},
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("1000")},
      "targetDeploymentItemName": "failedTargetCluster",
      "targetClusterName": "failedTargetCluster",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("9")},
        "status": "FAILED",
        "timeAttached": ${json_now_minus(12, "hours")},
        "detachedDiskId": "v-test-9",
        "hostname": "test-host-9",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": false
      }
    ],
    "showRestoreReady": false,
    "provisionedDate" : ${json_now_minus(40, "minutes")},
    "timestamp" : ${json_now_minus(40, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 10)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("10")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : false,
    "canceled" : true,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : ${json_now_minus(10, "hours")},
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("1100")},
      "targetDeploymentItemName": "canceledTargetCluster",
      "targetClusterName": "canceledTargetCluster",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("10")},
        "status": "FAILED",
        "timeAttached": ${json_now_minus(10, "hours")},
        "detachedDiskId": "v-test-10",
        "hostname": "test-host-10",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": false
      }
    ],
    "showRestoreReady": false,
    "provisionedDate" : ${json_now_minus(50, "minutes")},
    "timestamp" : ${json_now_minus(50, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 11)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("11")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : true,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : ${json_now_minus(8, "hours")},
    "finishedDate" : ${json_now_minus(8, "hours")},
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("1200")},
      "targetDeploymentItemName": "expiredTargetCluster",
      "targetClusterName": "expiredTargetCluster",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("11")},
        "status": "FAILED",
        "timeAttached": ${json_now_minus(8, "hours")},
        "detachedDiskId": "v-test-11",
        "hostname": "test-host-11",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": false
      }
    ],
    "showRestoreReady": false,
    "provisionedDate" : ${json_now_minus(60, "minutes")},
    "timestamp" : ${json_now_minus(60, "minutes")},
    "expirationDate" : ${json_now_minus(8, "hours")}
 },
 {
    "_id" : ${oid_date(iso_now_minus_days(1), 12)},
    "projectId" : ${oid("100")},
    "snapshotId" : ${oid("12")},
    "clusterName" : "sourceCluster",
    "clusterUniqueId": ${oid("10")},
    "sourceClusterName" : "sourceCluster",
    "expired" : false,
    "canceled" : false,
    "failed" : false,
    "expirationDate" : null,
    "finishedDate" : null,
    "regionName": "US_EAST_1",
    "delivery" : {
      "deliveryType": "DIRECT_ATTACH",
      "targetProjectId": ${oid("1300")},
      "targetDeploymentItemName": "inProgressTargetCluster",
      "targetClusterName": "inProgressTargetCluster",
      "targetMdbVersion": "7.0.1",
      "targetProjectVisibility": "DEFAULT_VISIBLE",
      "isTargeted" : true
    },
    "verificationKey" : "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-65b143baf98efdb52b7cf627cf8dcd41-dcef348bfa88862c73697b9663c47ef4787b2ad4481f2f26b11a7aa995c26675",
    "httpLinkMaxDownloads" : 1,
    "cloudProvider": "AWS",
    "attachStatus": [
      {
        "instanceId": ${oid("12")},
        "status": "IN_PROGRESS",
        "timeAttached": null,
        "detachedDiskId": null,
        "hostname": "test-host-12",
        "machineShutdown": false,
        "agentReachedGoalStateForRestore": false
      }
    ],
    "showRestoreReady": false,
    "provisionedDate" : ${json_now_minus(70, "minutes")},
    "timestamp" : ${json_now_minus(70, "minutes")},
    "expirationDate" : ${json_now_plus(1, "days")}
 }
]
