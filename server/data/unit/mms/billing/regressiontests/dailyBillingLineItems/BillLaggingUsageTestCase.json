[{"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R300", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 54690, "unitPriceDollars": 5.469, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S90_STORAGE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 37100, "unitPriceDollars": 3.71, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R400_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S50_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 14670, "unitPriceDollars": 1.467, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_INSTANCE_SP50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 15600, "unitPriceDollars": 1.56, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S70_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 37310, "unitPriceDollars": 3.731, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_100_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 14, "unitPriceDollars": 0.0014, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P30", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00024110391695205476, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S100_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 45150, "unitPriceDollars": 4.515, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S50_STORAGE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5100, "unitPriceDollars": 0.51, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M200_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_OBJECT_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 18, "unitPriceDollars": 0.0017753424657534246, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_COMPRESSED_OBJECT_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 32, "unitPriceDollars": 0.003156164383561644, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M400_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S130_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 115620, "unitPriceDollars": 11.562, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_OBJECT_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 13, "unitPriceDollars": 0.001347945205479452, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M80_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M200", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 42608, "unitPriceDollars": 4.2608, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_BACKUP_SNAPSHOT_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 26, "unitPriceDollars": 0.00263013698630137, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R700", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 109340, "unitPriceDollars": 10.934, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50000.0, "sku": "REALM_APP_REQUESTS_FREE_TIER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 24950000.0, "sku": "REALM_APP_REQUESTS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "totalPriceCents": 4990, "unitPriceDollars": 2e-06, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_FREE_INSTANCE_M2", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 41, "unitPriceDollars": 0.00410958904109589, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R300_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S50_MEMORY_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4230, "unitPriceDollars": 0.423, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R700_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S30_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1600, "unitPriceDollars": 0.16, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_AVAILABILITY_ZONE_IN", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_400_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 260, "unitPriceDollars": 0.026, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STORAGE_STANDARD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00018219178082191782, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_BACKUP_DOWNLOAD_VM", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1455, "unitPriceDollars": 0.1455, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S20_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1420, "unitPriceDollars": 0.142, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_UPLOAD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1250, "unitPriceDollars": 0.125, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S20_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1670, "unitPriceDollars": 0.167, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_A<PERSON>_SEARCH_INSTANCE_S100_MEMORY_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 43410, "unitPriceDollars": 4.341, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M80_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 23188, "unitPriceDollars": 2.3188, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3288, "unitPriceDollars": 0.3288, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1200, "unitPriceDollars": 0.12, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S60_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8540, "unitPriceDollars": 0.854, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE_IOPS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1, "unitPriceDollars": 0.0001452054794520548, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R40_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 5.0, "sku": "NDS_AWS_PIT_RESTORE_STORAGE_FREE_TIER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 95.0, "sku": "NDS_AWS_PIT_RESTORE_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "totalPriceCents": 312, "unitPriceDollars": 0.03287671232876712, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_200_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 205, "unitPriceDollars": 0.0205, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_1", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M600_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 113317, "unitPriceDollars": 11.3317, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S135_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 137760, "unitPriceDollars": 13.776, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M50_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3790, "unitPriceDollars": 0.379, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M80", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 24010, "unitPriceDollars": 2.401, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022103801829829228, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_INSTANCE_SP10", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1900, "unitPriceDollars": 0.19, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S50_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4210, "unitPriceDollars": 0.421, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S80_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 48730, "unitPriceDollars": 4.873, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M200_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 46240, "unitPriceDollars": 4.62398, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_100_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M40_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M10", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 264, "unitPriceDollars": 0.0264, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M200_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_FREE_INSTANCE_M5", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 114, "unitPriceDollars": 0.01141552511415525, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_COMPRESSED_OBJECT_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 27, "unitPriceDollars": 0.002695890410958904, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_200_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R300_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P3", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S70_COMPUTE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 24660, "unitPriceDollars": 2.466, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M20", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 621, "unitPriceDollars": 0.0621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M300_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 870, "unitPriceDollars": 0.087, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_300_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 288, "unitPriceDollars": 0.0288, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M30", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1455, "unitPriceDollars": 0.1455, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_EXTENDED_IOPS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 9.589041095890411e-06, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M200_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S120_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 72620, "unitPriceDollars": 7.262, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P6", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002913099315068493, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_200_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S90_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 34020, "unitPriceDollars": 3.402, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P2", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M140_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_BACKUP_DOWNLOAD_VM_STORAGE_IOPS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1, "unitPriceDollars": 0.0001452054794520548, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R400", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 69960, "unitPriceDollars": 6.996, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R400", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 66746, "unitPriceDollars": 6.6746, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S80_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 40460, "unitPriceDollars": 4.046, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R80_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M60", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10814, "unitPriceDollars": 1.0814, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R300", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 51110, "unitPriceDollars": 5.111, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_PRIVATE_ENDPOINT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 450, "unitPriceDollars": 0.045, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_INSTANCE_SP10", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1900, "unitPriceDollars": 0.19, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P20", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00026121040239726026, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTER_CONTINENT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 500, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S80_STORAGE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 18600, "unitPriceDollars": 1.86, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_DATA_TRANSFER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_OBJECT_STORAGE_SEEK", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 125, "unitPriceDollars": 0.0125, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_INSTANCE_SP30", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3900, "unitPriceDollars": 0.39, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M300", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 71850, "unitPriceDollars": 7.185, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_WPU", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9490, "unitPriceDollars": 0.949, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S60_COMPUTE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 17480, "unitPriceDollars": 1.748, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1685, "unitPriceDollars": 0.1685, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S40_MEMORY_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2120, "unitPriceDollars": 0.212, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S80_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 17700, "unitPriceDollars": 1.77, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_M50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8090, "unitPriceDollars": 0.809, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_DATA_LAKE_STORAGE_ACCESS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10, "unitPriceDollars": 0.001, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M80_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M140", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 31362, "unitPriceDollars": 3.1362, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P3", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "GCP_SNAPSHOT_COPY_DISK", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00031054794520547947, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P30", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00024110391695205476, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_UPLOAD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1250, "unitPriceDollars": 0.125, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_500_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 315, "unitPriceDollars": 0.0315, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M400", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 86769, "unitPriceDollars": 8.6769, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M60", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 13000, "unitPriceDollars": 1.3, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S90_MEMORY_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 30780, "unitPriceDollars": 3.078, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STANDARD_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022054794520547942, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_LAKE_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 18, "unitPriceDollars": 0.0017753424657534246, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4860, "unitPriceDollars": 0.486, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M60_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 7490, "unitPriceDollars": 0.749, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_300_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 288, "unitPriceDollars": 0.0288, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_WPU", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10000, "unitPriceDollars": 1.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M50_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M30_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_400_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 356, "unitPriceDollars": 0.0356, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R200_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M80", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 20158, "unitPriceDollars": 2.0158, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022054794520547942, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 875, "unitPriceDollars": 0.0875, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_INTER_ZONE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_CONTINUOUS_BACKUP", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 80, "unitPriceDollars": 0.007956164383561644, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 250, "unitPriceDollars": 0.025, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM_M50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5449, "unitPriceDollars": 0.5449, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M300", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 63758, "unitPriceDollars": 6.3758, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R40_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2180, "unitPriceDollars": 0.218, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S130_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 81390, "unitPriceDollars": 8.139, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R60", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9600, "unitPriceDollars": 0.96, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5341, "unitPriceDollars": 0.5341, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_AWS_SERVERLESS_RPU", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "tierLowerBound": 0.0, "tierUpperBound": 50.0, "totalPriceCents": 500, "unitPriceDollars": 0.1, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_AWS_SERVERLESS_RPU", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 2, "tierLowerBound": 50.0, "tierUpperBound": 550.0, "totalPriceCents": 250, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_100_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 14, "unitPriceDollars": 0.0014, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 23750, "unitPriceDollars": 2.375, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022103801829829228, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M40_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_DATA_TRANSFER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 150, "unitPriceDollars": 0.015, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_100_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 14, "unitPriceDollars": 0.0014, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022054794520547942, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_GLOBAL_VNET_OUT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 350, "unitPriceDollars": 0.035, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R200_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M90_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 96, "unitPriceDollars": 0.00964, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_TRANSFERRED_FROM_DIFFERENT_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON>WS_TIME_BASED_SNAPSHOT_COPY_LEVEL_2", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 180, "unitPriceDollars": 0.018, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 5.0, "sku": "NDS_AZURE_PIT_RESTORE_STORAGE_FREE_TIER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 95.0, "sku": "NDS_AZURE_PIT_RESTORE_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "totalPriceCents": 718, "unitPriceDollars": 0.07561643835616438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_INSTANCE_SP30", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3900, "unitPriceDollars": 0.39, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_300_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 192, "unitPriceDollars": 0.0192, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M250", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 53603, "unitPriceDollars": 5.3603, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_EXTENDED_STANDARD_IOPS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 9.589041095890411e-06, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_OBJECT_STORAGE_ACCESS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9, "unitPriceDollars": 0.0009, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S70_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 12260, "unitPriceDollars": 1.226, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R60_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_DATA_TRANSFER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S50_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5540, "unitPriceDollars": 0.554, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P15", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027124357876712326, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 6483, "unitPriceDollars": 0.6483, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 6560, "unitPriceDollars": 0.656, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_FREE_INSTANCE_M0", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S60_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 26410, "unitPriceDollars": 2.641, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P3", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M20_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R60", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8640, "unitPriceDollars": 0.864, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpBase": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_INTER_CONNECT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_UPLOAD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1250, "unitPriceDollars": 0.125, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_VPC_PEERING", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 500, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpDest": "Americas", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1200, "unitPriceDollars": 0.12, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4370, "unitPriceDollars": 0.437, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.009994520547945205, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M20", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 670, "unitPriceDollars": 0.067, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_M40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3288, "unitPriceDollars": 0.3288, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S110_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 63230, "unitPriceDollars": 6.323, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P15", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027124357876712326, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_M60", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16020, "unitPriceDollars": 1.602, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_500_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 315, "unitPriceDollars": 0.0315, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON><PERSON>_TIME_BASED_SNAPSHOT_COPY_LEVEL_5", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 120, "unitPriceDollars": 0.012, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M20_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_GCP_DATA_SCANNED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 50000, "unitPriceDollars": 5.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_400_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 260, "unitPriceDollars": 0.026, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_RETURNED_DIFFERENT_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P30", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00024110391695205476, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM_M40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2830, "unitPriceDollars": 0.283, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M10_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 25000, "unitPriceDollars": 2.5, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_400_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 356, "unitPriceDollars": 0.0356, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpDest": "Americas", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_INTER_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S60_STORAGE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9300, "unitPriceDollars": 0.93, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S60_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 21900, "unitPriceDollars": 2.19, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P20", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00026121040239726026, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_BACKUP_SNAPSHOT_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 125, "unitPriceDollars": 0.012493150684931509, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S30_MEMORY_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1090, "unitPriceDollars": 0.109, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R60_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_SCANNED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 50000, "unitPriceDollars": 5.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S70_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 27920, "unitPriceDollars": 2.792, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_200_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 205, "unitPriceDollars": 0.0205, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M400_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 90957, "unitPriceDollars": 9.0957, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_AZURE_SERVERLESS_RPU", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "tierLowerBound": 0.0, "tierUpperBound": 50.0, "totalPriceCents": 500, "unitPriceDollars": 0.1, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_AZURE_SERVERLESS_RPU", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 2, "tierLowerBound": 50.0, "tierUpperBound": 550.0, "totalPriceCents": 250, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_300_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 192, "unitPriceDollars": 0.0192, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_WPU", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10000, "unitPriceDollars": 1.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_REGIONAL_VNET_OUT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STORAGE_PROVISIONED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022876712328767125, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_LAKE_STORAGE_ACCESS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 80, "unitPriceDollars": 0.008, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 25000, "unitPriceDollars": 2.5, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5449, "unitPriceDollars": 0.5449, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S40_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5590, "unitPriceDollars": 0.559, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S40_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2370, "unitPriceDollars": 0.237, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P2", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S30_COMPUTE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2320, "unitPriceDollars": 0.232, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_PRIVATE_ENDPOINT_CAPACITY_UNITS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 120, "unitPriceDollars": 0.012, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 99, "unitPriceDollars": 0.00992876712328767, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M80", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 24494, "unitPriceDollars": 2.4494, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R80", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 19926, "unitPriceDollars": 1.9926, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_100_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_LAKE_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 13, "unitPriceDollars": 0.001347945205479452, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2540, "unitPriceDollars": 0.254, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00031054794520547947, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTRA_CONTINENT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R300", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 54864, "unitPriceDollars": 5.4864, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R200", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36368, "unitPriceDollars": 3.6368, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S100_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 57820, "unitPriceDollars": 5.782, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_CMK_PRIVATE_NETWORKING", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R600", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 89340, "unitPriceDollars": 8.934, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S110_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 77110, "unitPriceDollars": 7.711, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M80_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM_M60", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10814, "unitPriceDollars": 1.0814, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M40_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_DATA_LAKE_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16, "unitPriceDollars": 0.001578082191780822, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R50_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S40_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2730, "unitPriceDollars": 0.273, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R300_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_BACKUP_SNAPSHOT_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 46, "unitPriceDollars": 0.004602739726027398, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_LAKE_STORAGE_ACCESS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9, "unitPriceDollars": 0.0009, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M10", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 274, "unitPriceDollars": 0.0274, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1455, "unitPriceDollars": 0.1455, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_500_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 315, "unitPriceDollars": 0.0315, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S60_MEMORY_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8730, "unitPriceDollars": 0.873, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P4", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00030136986301369865, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_PRIVATELINK", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 700, "unitPriceDollars": 0.07, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_M60", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 12304, "unitPriceDollars": 1.2304, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_RETURNED_INTERNET", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_RETURNED_DIFFERENT_CONTINENT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 500, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002767123287671233, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_PRIVATE_ENDPOINT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 500, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2260, "unitPriceDollars": 0.226, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_PRIVATE_ENDPOINT_CAPACITY_UNITS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S80_COMPUTE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 32190, "unitPriceDollars": 3.219, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_A<PERSON>_SEARCH_INSTANCE_S20_COMPUTE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1170, "unitPriceDollars": 0.117, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M140_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_CMK_PRIVATE_NETWORKING", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON>WS_TIME_BASED_SNAPSHOT_COPY_LEVEL_7", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 50, "unitPriceDollars": 0.005, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_AVAILABILITY_ZONE_OUT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R50_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_500_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 411, "unitPriceDollars": 0.0411, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_OBJECT_STORAGE_ACCESS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10, "unitPriceDollars": 0.001, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_DATA_TRANSFER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 150, "unitPriceDollars": 0.015, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_COMPRESSED_OBJECT_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.0035506849315068492, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M20", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 622, "unitPriceDollars": 0.0622, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M60_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 12009, "unitPriceDollars": 1.2009, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "numSnapshotsScheduled": 1, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 1.0, "replicaSet": {"clusterId": {"$oid": "0000000000000***********"}, "name": "someTestRsId"}, "rsId": "someTestRsId", "sku": "MMS_BACKUP_STORAGE_FREE_TIER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "numSnapshotsScheduled": 1, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 99.0, "replicaSet": {"clusterId": {"$oid": "0000000000000***********"}, "name": "someTestRsId"}, "rsId": "someTestRsId", "sku": "MMS_BACKUP_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 814, "unitPriceDollars": 0.0821917808219178, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M140", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36140, "unitPriceDollars": 3.614, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R80_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M10_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 0.5, "sku": "REALM_APP_DATA_TRANSFER_FREE_TIER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 99.5, "sku": "REALM_APP_DATA_TRANSFER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1194, "unitPriceDollars": 0.12, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_200_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R400", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 73630, "unitPriceDollars": 7.363, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STORAGE_SSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00031054794520547947, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_DATA_TRANSFER_SAME_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S40_STORAGE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2500, "unitPriceDollars": 0.25, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_BACKUP_DOWNLOAD_VM_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00031054794520547947, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M30", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1770, "unitPriceDollars": 0.177, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M50_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_OBJECT_STORAGE_ACCESS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8, "unitPriceDollars": 0.0008, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R400_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 5.0, "sku": "NDS_GCP_PIT_RESTORE_STORAGE_FREE_TIER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 95.0, "sku": "NDS_GCP_PIT_RESTORE_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "totalPriceCents": 187, "unitPriceDollars": 0.019726027397260273, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpDest": "EMEA", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_GCP_DATA_RETURNED_DIFFERENT_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 800, "unitPriceDollars": 0.08, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 250, "unitPriceDollars": 0.025, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P20", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00026121040239726026, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2830, "unitPriceDollars": 0.283, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_500_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 411, "unitPriceDollars": 0.0411, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M200_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 28150, "unitPriceDollars": 2.815, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R60_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M20_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2717, "unitPriceDollars": 0.2717, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_INSTANCE_SP50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 15600, "unitPriceDollars": 1.56, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P10", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00028125, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON>WS_TIME_BASED_SNAPSHOT_COPY_LEVEL_3", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 160, "unitPriceDollars": 0.016, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R400_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpDest": "Americas", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_GOOGLE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S140_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 93420, "unitPriceDollars": 9.342, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R60", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10020, "unitPriceDollars": 1.002, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P6", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002913099315068493, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 82, "unitPriceDollars": 0.00821917808219178, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M30_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_500_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 411, "unitPriceDollars": 0.0411, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M400_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 57970, "unitPriceDollars": 5.797, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P15", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027124357876712326, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S50_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 11340, "unitPriceDollars": 1.134, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_OBJECT_STORAGE_V3", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16, "unitPriceDollars": 0.001578082191780822, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_BACKUP_DOWNLOAD_VM_STORAGE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002767123287671233, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S90_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 40960, "unitPriceDollars": 4.096, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpDest": "Americas", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "GCP_SNAPSHOT_COPY_DATA_TRANSFER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R40_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_100_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_SCANNED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 50000, "unitPriceDollars": 5.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022103801829829228, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1685, "unitPriceDollars": 0.1685, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M60_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M60", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 12304, "unitPriceDollars": 1.2304, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M60_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_PRIVATELINK", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 700, "unitPriceDollars": 0.07, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R200_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P4", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00030136986301369865, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_GLOBAL_VNET_IN", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 350, "unitPriceDollars": 0.035, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002913099315068493, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M50_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R80", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 18440, "unitPriceDollars": 1.844, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M10", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 260, "unitPriceDollars": 0.026, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3410, "unitPriceDollars": 0.341, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_CONTINUOUS_BACKUP", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 80, "unitPriceDollars": 0.00798904109589041, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P10", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00028125, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M250_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_M40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4210, "unitPriceDollars": 0.421, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_GCP_DATA_RETURNED_SAME_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_BACKUP_DOWNLOAD_VM", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2180, "unitPriceDollars": 0.218, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON><PERSON>_TIME_BASED_SNAPSHOT_COPY_LEVEL_6", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M300_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_DATA_TRANSFER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 150, "unitPriceDollars": 0.015, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_RETURNED_SAME_REGION", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M200", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 47980, "unitPriceDollars": 4.798, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 1.0, "sku": "CHARTS_DATA_DOWNLOADED_FREE_TIER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 99.0, "sku": "CHARTS_DATA_DOWNLOADED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9900, "unitPriceDollars": 1.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_RETURNED_INTERNET", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 870, "unitPriceDollars": 0.087, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 0.03, "sku": "REALM_APP_SYNC_FREE_TIER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 99.97, "sku": "REALM_APP_SYNC", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 800, "unitPriceDollars": 0.08, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_GCP_DATA_RETURNED_INTERNET", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 850, "unitPriceDollars": 0.085, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_300_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 288, "unitPriceDollars": 0.0288, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P4", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00030136986301369865, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_300_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 192, "unitPriceDollars": 0.0192, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M10_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_CONTINUOUS_BACKUP", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 66, "unitPriceDollars": 0.006575342465753426, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_400_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 260, "unitPriceDollars": 0.026, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_A<PERSON>_SEARCH_INSTANCE_S50_COMPUTE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9750, "unitPriceDollars": 0.975, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON><PERSON>_TIME_BASED_SNAPSHOT_COPY_LEVEL_4", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 140, "unitPriceDollars": 0.014, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M60_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M30", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1685, "unitPriceDollars": 0.1685, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P10", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00028125, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R600_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M100_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_200_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 205, "unitPriceDollars": 0.0205, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_EXTENDED_IOPS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 9.589041095890411e-06, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M30_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00023103060787671228, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R200", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36840, "unitPriceDollars": 3.684, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_CONTINENT", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_GCP_SERVERLESS_RPU", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "tierLowerBound": 0.0, "tierUpperBound": 50.0, "totalPriceCents": 475, "unitPriceDollars": 0.095, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_GCP_SERVERLESS_RPU", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 2, "tierLowerBound": 50.0, "tierUpperBound": 550.0, "totalPriceCents": 235, "unitPriceDollars": 0.047, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M40_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2020, "unitPriceDollars": 0.202, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R50_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S30_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2640, "unitPriceDollars": 0.264, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R200", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 34030, "unitPriceDollars": 3.403, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P2", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_M50", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 6483, "unitPriceDollars": 0.6483, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M80_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 13620, "unitPriceDollars": 1.362, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S40_COMPUTE_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4750, "unitPriceDollars": 0.475, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 25.0, "sku": "REALM_APP_COMPUTE_FREE_TIER", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 75.0, "sku": "REALM_APP_COMPUTE", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 150, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S80_MEMORY_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16570, "unitPriceDollars": 1.657, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M90", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 26494, "unitPriceDollars": 2.6494, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S30_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3410, "unitPriceDollars": 0.341, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00023103060787671228, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STORAGE_IOPS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1, "unitPriceDollars": 0.00011917808219178082, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M300_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 68599, "unitPriceDollars": 6.8599, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M200", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44967, "unitPriceDollars": 4.4967, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M100", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 30120, "unitPriceDollars": 3.012, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S80_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 22000, "unitPriceDollars": 2.2, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_REGIONAL_VNET_IN", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P40", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00023103060787671228, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "US_EAST", "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_PRIVATE_ENDPOINT_CAPACITY_UNITS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 300, "unitPriceDollars": 0.03, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S40_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 7090, "unitPriceDollars": 0.709, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R80", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16140, "unitPriceDollars": 1.614, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R80_PAUSED", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S110_MEMORY_NVME", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 57870, "unitPriceDollars": 5.787, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S60_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 11540, "unitPriceDollars": 1.154, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-14T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_400_USAGE_HOURS", "startDate": {"$date": "2025-01-13T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 356, "unitPriceDollars": 0.0356, "usageDate": {"$date": "2025-01-13T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R300", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 54690, "unitPriceDollars": 5.469, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S90_STORAGE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 37100, "unitPriceDollars": 3.71, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R400_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_INSTANCE_SP50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 15600, "unitPriceDollars": 1.56, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S50_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 14670, "unitPriceDollars": 1.467, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S70_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 37310, "unitPriceDollars": 3.731, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_100_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 14, "unitPriceDollars": 0.0014, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P30", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00024110391695205476, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S100_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 45150, "unitPriceDollars": 4.515, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S50_STORAGE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5100, "unitPriceDollars": 0.51, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M200_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_OBJECT_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 18, "unitPriceDollars": 0.0017753424657534246, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_COMPRESSED_OBJECT_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 32, "unitPriceDollars": 0.003156164383561644, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S130_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 115620, "unitPriceDollars": 11.562, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M400_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_OBJECT_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 13, "unitPriceDollars": 0.001347945205479452, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M80_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M200", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 42608, "unitPriceDollars": 4.2608, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_BACKUP_SNAPSHOT_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 26, "unitPriceDollars": 0.00263013698630137, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R700", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 109340, "unitPriceDollars": 10.934, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50000.0, "sku": "REALM_APP_REQUESTS_FREE_TIER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 24950000.0, "sku": "REALM_APP_REQUESTS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "totalPriceCents": 4990, "unitPriceDollars": 2e-06, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_FREE_INSTANCE_M2", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 41, "unitPriceDollars": 0.00410958904109589, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R300_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S50_MEMORY_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4230, "unitPriceDollars": 0.423, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R700_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S30_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1600, "unitPriceDollars": 0.16, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_AVAILABILITY_ZONE_IN", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_400_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 260, "unitPriceDollars": 0.026, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STORAGE_STANDARD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00018219178082191782, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_BACKUP_DOWNLOAD_VM", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1455, "unitPriceDollars": 0.1455, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S20_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1420, "unitPriceDollars": 0.142, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_UPLOAD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1250, "unitPriceDollars": 0.125, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S20_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1670, "unitPriceDollars": 0.167, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_A<PERSON>_SEARCH_INSTANCE_S100_MEMORY_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 43410, "unitPriceDollars": 4.341, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M80_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 23188, "unitPriceDollars": 2.3188, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3288, "unitPriceDollars": 0.3288, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1200, "unitPriceDollars": 0.12, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S60_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8540, "unitPriceDollars": 0.854, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE_IOPS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1, "unitPriceDollars": 0.0001452054794520548, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R40_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 5.0, "sku": "NDS_AWS_PIT_RESTORE_STORAGE_FREE_TIER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 95.0, "sku": "NDS_AWS_PIT_RESTORE_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "totalPriceCents": 312, "unitPriceDollars": 0.03287671232876712, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_200_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 205, "unitPriceDollars": 0.0205, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_1", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M600_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 113317, "unitPriceDollars": 11.3317, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S135_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 137760, "unitPriceDollars": 13.776, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M50_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3790, "unitPriceDollars": 0.379, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M80", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 24010, "unitPriceDollars": 2.401, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022103801829829228, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S50_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4210, "unitPriceDollars": 0.421, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_INSTANCE_SP10", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1900, "unitPriceDollars": 0.19, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S80_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 48730, "unitPriceDollars": 4.873, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M200_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 46240, "unitPriceDollars": 4.62398, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_100_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M40_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M10", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 264, "unitPriceDollars": 0.0264, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M200_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_FREE_INSTANCE_M5", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 114, "unitPriceDollars": 0.01141552511415525, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_COMPRESSED_OBJECT_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 27, "unitPriceDollars": 0.002695890410958904, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_200_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R300_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P3", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S70_COMPUTE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 24660, "unitPriceDollars": 2.466, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M20", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 621, "unitPriceDollars": 0.0621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M300_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 870, "unitPriceDollars": 0.087, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_300_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 288, "unitPriceDollars": 0.0288, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M30", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1455, "unitPriceDollars": 0.1455, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_EXTENDED_IOPS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 9.589041095890411e-06, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M200_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S120_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 72620, "unitPriceDollars": 7.262, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P6", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002913099315068493, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_200_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S90_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 34020, "unitPriceDollars": 3.402, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P2", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M140_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_BACKUP_DOWNLOAD_VM_STORAGE_IOPS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1, "unitPriceDollars": 0.0001452054794520548, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R400", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 69960, "unitPriceDollars": 6.996, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R400", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 66746, "unitPriceDollars": 6.6746, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S80_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 40460, "unitPriceDollars": 4.046, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R80_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M60", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10814, "unitPriceDollars": 1.0814, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_PRIVATE_ENDPOINT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 450, "unitPriceDollars": 0.045, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R300", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 51110, "unitPriceDollars": 5.111, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_INSTANCE_SP10", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1900, "unitPriceDollars": 0.19, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTER_CONTINENT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 500, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S80_STORAGE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 18600, "unitPriceDollars": 1.86, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P20", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00026121040239726026, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_DATA_TRANSFER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_OBJECT_STORAGE_SEEK", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 125, "unitPriceDollars": 0.0125, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_INSTANCE_SP30", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3900, "unitPriceDollars": 0.39, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M300", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 71850, "unitPriceDollars": 7.185, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_WPU", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9490, "unitPriceDollars": 0.949, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S60_COMPUTE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 17480, "unitPriceDollars": 1.748, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S80_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 17700, "unitPriceDollars": 1.77, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S40_MEMORY_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2120, "unitPriceDollars": 0.212, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1685, "unitPriceDollars": 0.1685, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_M50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8090, "unitPriceDollars": 0.809, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M80_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_DATA_LAKE_STORAGE_ACCESS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10, "unitPriceDollars": 0.001, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M140", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 31362, "unitPriceDollars": 3.1362, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P3", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "GCP_SNAPSHOT_COPY_DISK", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00031054794520547947, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P30", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00024110391695205476, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_UPLOAD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1250, "unitPriceDollars": 0.125, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_500_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 315, "unitPriceDollars": 0.0315, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M400", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 86769, "unitPriceDollars": 8.6769, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S90_MEMORY_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 30780, "unitPriceDollars": 3.078, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M60", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 13000, "unitPriceDollars": 1.3, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STANDARD_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022054794520547942, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_LAKE_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 18, "unitPriceDollars": 0.0017753424657534246, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4860, "unitPriceDollars": 0.486, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M60_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 7490, "unitPriceDollars": 0.749, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_300_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 288, "unitPriceDollars": 0.0288, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_WPU", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10000, "unitPriceDollars": 1.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M50_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M30_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_400_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 356, "unitPriceDollars": 0.0356, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R200_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M80", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 20158, "unitPriceDollars": 2.0158, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022054794520547942, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 875, "unitPriceDollars": 0.0875, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_INTER_ZONE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_CONTINUOUS_BACKUP", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 80, "unitPriceDollars": 0.007956164383561644, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 250, "unitPriceDollars": 0.025, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM_M50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5449, "unitPriceDollars": 0.5449, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M300", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 63758, "unitPriceDollars": 6.3758, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R40_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2180, "unitPriceDollars": 0.218, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S130_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 81390, "unitPriceDollars": 8.139, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R60", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9600, "unitPriceDollars": 0.96, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5341, "unitPriceDollars": 0.5341, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_AWS_SERVERLESS_RPU", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "tierLowerBound": 0.0, "tierUpperBound": 50.0, "totalPriceCents": 500, "unitPriceDollars": 0.1, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_AWS_SERVERLESS_RPU", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 2, "tierLowerBound": 50.0, "tierUpperBound": 550.0, "totalPriceCents": 250, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 23750, "unitPriceDollars": 2.375, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_100_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 14, "unitPriceDollars": 0.0014, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022103801829829228, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M40_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_DATA_TRANSFER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 150, "unitPriceDollars": 0.015, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_100_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 14, "unitPriceDollars": 0.0014, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022054794520547942, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_GLOBAL_VNET_OUT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 350, "unitPriceDollars": 0.035, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R200_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M90_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 96, "unitPriceDollars": 0.00964, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_TRANSFERRED_FROM_DIFFERENT_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON>WS_TIME_BASED_SNAPSHOT_COPY_LEVEL_2", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 180, "unitPriceDollars": 0.018, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 5.0, "sku": "NDS_AZURE_PIT_RESTORE_STORAGE_FREE_TIER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 95.0, "sku": "NDS_AZURE_PIT_RESTORE_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "totalPriceCents": 718, "unitPriceDollars": 0.07561643835616438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_INSTANCE_SP30", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3900, "unitPriceDollars": 0.39, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_LEGACY_300_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 192, "unitPriceDollars": 0.0192, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M250", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 53603, "unitPriceDollars": 5.3603, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_EXTENDED_STANDARD_IOPS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 9.589041095890411e-06, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_OBJECT_STORAGE_ACCESS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9, "unitPriceDollars": 0.0009, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S70_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 12260, "unitPriceDollars": 1.226, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R60_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S50_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5540, "unitPriceDollars": 0.554, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_DATA_TRANSFER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P15", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027124357876712326, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 6483, "unitPriceDollars": 0.6483, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 6560, "unitPriceDollars": 0.656, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_FREE_INSTANCE_M0", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S60_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 26410, "unitPriceDollars": 2.641, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P3", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R60", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8640, "unitPriceDollars": 0.864, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M20_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpBase": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_INTER_CONNECT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_UPLOAD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1250, "unitPriceDollars": 0.125, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_VPC_PEERING", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 500, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpDest": "Americas", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1200, "unitPriceDollars": 0.12, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4370, "unitPriceDollars": 0.437, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.009994520547945205, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M20", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 670, "unitPriceDollars": 0.067, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_M40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3288, "unitPriceDollars": 0.3288, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S110_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 63230, "unitPriceDollars": 6.323, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_M60", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16020, "unitPriceDollars": 1.602, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_500_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 315, "unitPriceDollars": 0.0315, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P15", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027124357876712326, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON><PERSON>_TIME_BASED_SNAPSHOT_COPY_LEVEL_5", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 120, "unitPriceDollars": 0.012, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M20_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_GCP_DATA_SCANNED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 50000, "unitPriceDollars": 5.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_400_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 260, "unitPriceDollars": 0.026, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_RETURNED_DIFFERENT_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P30", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00024110391695205476, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM_M40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2830, "unitPriceDollars": 0.283, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 25000, "unitPriceDollars": 2.5, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M10_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_400_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 356, "unitPriceDollars": 0.0356, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpDest": "Americas", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_INTER_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S60_STORAGE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9300, "unitPriceDollars": 0.93, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S60_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 21900, "unitPriceDollars": 2.19, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P20", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00026121040239726026, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_BACKUP_SNAPSHOT_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 125, "unitPriceDollars": 0.012493150684931509, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S30_MEMORY_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1090, "unitPriceDollars": 0.109, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R60_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_SCANNED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 50000, "unitPriceDollars": 5.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S70_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 27920, "unitPriceDollars": 2.792, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_200_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 205, "unitPriceDollars": 0.0205, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M400_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 90957, "unitPriceDollars": 9.0957, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_AZURE_SERVERLESS_RPU", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "tierLowerBound": 0.0, "tierUpperBound": 50.0, "totalPriceCents": 500, "unitPriceDollars": 0.1, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_AZURE_SERVERLESS_RPU", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 2, "tierLowerBound": 50.0, "tierUpperBound": 550.0, "totalPriceCents": 250, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_300_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 192, "unitPriceDollars": 0.0192, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_WPU", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10000, "unitPriceDollars": 1.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STORAGE_PROVISIONED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022876712328767125, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_REGIONAL_VNET_OUT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_LAKE_STORAGE_ACCESS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 80, "unitPriceDollars": 0.008, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 25000, "unitPriceDollars": 2.5, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5449, "unitPriceDollars": 0.5449, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S40_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 5590, "unitPriceDollars": 0.559, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S40_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2370, "unitPriceDollars": 0.237, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P2", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_PRIVATE_ENDPOINT_CAPACITY_UNITS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 120, "unitPriceDollars": 0.012, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S30_COMPUTE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2320, "unitPriceDollars": 0.232, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 99, "unitPriceDollars": 0.00992876712328767, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M80", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 24494, "unitPriceDollars": 2.4494, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R80", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 19926, "unitPriceDollars": 1.9926, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_100_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_LAKE_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 13, "unitPriceDollars": 0.001347945205479452, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2540, "unitPriceDollars": 0.254, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00031054794520547947, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTRA_CONTINENT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R300", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 54864, "unitPriceDollars": 5.4864, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R200", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36368, "unitPriceDollars": 3.6368, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S100_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 57820, "unitPriceDollars": 5.782, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_CMK_PRIVATE_NETWORKING", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R600", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 89340, "unitPriceDollars": 8.934, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S110_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 77110, "unitPriceDollars": 7.711, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M80_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM_M60", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10814, "unitPriceDollars": 1.0814, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M40_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_DATA_LAKE_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16, "unitPriceDollars": 0.001578082191780822, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S40_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2730, "unitPriceDollars": 0.273, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R50_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R300_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_BACKUP_SNAPSHOT_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 46, "unitPriceDollars": 0.004602739726027398, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_LAKE_STORAGE_ACCESS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9, "unitPriceDollars": 0.0009, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M10", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 274, "unitPriceDollars": 0.0274, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_SNAPSHOT_EXPORT_VM", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1455, "unitPriceDollars": 0.1455, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_LEGACY_500_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 315, "unitPriceDollars": 0.0315, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S60_MEMORY_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8730, "unitPriceDollars": 0.873, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P4", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00030136986301369865, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_PRIVATELINK", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 700, "unitPriceDollars": 0.07, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_M60", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 12304, "unitPriceDollars": 1.2304, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_RETURNED_INTERNET", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002767123287671233, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_RETURNED_DIFFERENT_CONTINENT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 500, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_PRIVATE_ENDPOINT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 500, "unitPriceDollars": 0.05, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2260, "unitPriceDollars": 0.226, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_PRIVATE_ENDPOINT_CAPACITY_UNITS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S80_COMPUTE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 32190, "unitPriceDollars": 3.219, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_A<PERSON>_SEARCH_INSTANCE_S20_COMPUTE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1170, "unitPriceDollars": 0.117, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M140_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_CMK_PRIVATE_NETWORKING", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_AVAILABILITY_ZONE_OUT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON>WS_TIME_BASED_SNAPSHOT_COPY_LEVEL_7", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 50, "unitPriceDollars": 0.005, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R50_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_OBJECT_STORAGE_ACCESS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10, "unitPriceDollars": 0.001, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AZURE_500_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 411, "unitPriceDollars": 0.0411, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_DATA_TRANSFER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 150, "unitPriceDollars": 0.015, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_COMPRESSED_OBJECT_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.0035506849315068492, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M20", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 622, "unitPriceDollars": 0.0622, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M60_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 12009, "unitPriceDollars": 1.2009, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "numSnapshotsScheduled": 1, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 1.0, "replicaSet": {"clusterId": {"$oid": "0000000000000***********"}, "name": "someTestRsId"}, "rsId": "someTestRsId", "sku": "MMS_BACKUP_STORAGE_FREE_TIER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "numSnapshotsScheduled": 1, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 99.0, "replicaSet": {"clusterId": {"$oid": "0000000000000***********"}, "name": "someTestRsId"}, "rsId": "someTestRsId", "sku": "MMS_BACKUP_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 814, "unitPriceDollars": 0.0821917808219178, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M140", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36140, "unitPriceDollars": 3.614, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R80_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M10_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 0.5, "sku": "REALM_APP_DATA_TRANSFER_FREE_TIER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 99.5, "sku": "REALM_APP_DATA_TRANSFER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1194, "unitPriceDollars": 0.12, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_200_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R400", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 73630, "unitPriceDollars": 7.363, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STORAGE_SSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00031054794520547947, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S40_STORAGE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2500, "unitPriceDollars": 0.25, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_AWS_DATA_TRANSFER_SAME_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_GCP_BACKUP_DOWNLOAD_VM_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00031054794520547947, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M30", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1770, "unitPriceDollars": 0.177, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M50_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_OBJECT_STORAGE_ACCESS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 8, "unitPriceDollars": 0.0008, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R400_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpDest": "EMEA", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_GCP_DATA_RETURNED_DIFFERENT_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 800, "unitPriceDollars": 0.08, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 5.0, "sku": "NDS_GCP_PIT_RESTORE_STORAGE_FREE_TIER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 95.0, "sku": "NDS_GCP_PIT_RESTORE_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "totalPriceCents": 187, "unitPriceDollars": 0.019726027397260273, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 250, "unitPriceDollars": 0.025, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P20", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00026121040239726026, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2830, "unitPriceDollars": 0.283, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_500_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 411, "unitPriceDollars": 0.0411, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R60_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M200_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 28150, "unitPriceDollars": 2.815, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M20_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2717, "unitPriceDollars": 0.2717, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STREAM_PROCESSING_INSTANCE_SP50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 15600, "unitPriceDollars": 1.56, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P10", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00028125, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON>WS_TIME_BASED_SNAPSHOT_COPY_LEVEL_3", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 160, "unitPriceDollars": 0.016, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R400_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpDest": "Americas", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_DATA_TRANSFER_GOOGLE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S140_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 93420, "unitPriceDollars": 9.342, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R60", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10020, "unitPriceDollars": 1.002, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P6", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002913099315068493, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 82, "unitPriceDollars": 0.00821917808219178, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M30_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_500_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 411, "unitPriceDollars": 0.0411, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P15", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027124357876712326, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M400_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 57970, "unitPriceDollars": 5.797, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S50_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 11340, "unitPriceDollars": 1.134, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_BACKUP_DOWNLOAD_VM_STORAGE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002767123287671233, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeDataSet": "test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_OBJECT_STORAGE_V3", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16, "unitPriceDollars": 0.001578082191780822, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S90_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 40960, "unitPriceDollars": 4.096, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpDest": "Americas", "gcpSource": "Americas", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "GCP_SNAPSHOT_COPY_DATA_TRANSFER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R40_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_100_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 110, "unitPriceDollars": 0.011, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_SCANNED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 50000, "unitPriceDollars": 5.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00022103801829829228, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1685, "unitPriceDollars": 0.1685, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M60", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 12304, "unitPriceDollars": 1.2304, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M60_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M60_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_STREAM_PROCESSING_PRIVATELINK", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 700, "unitPriceDollars": 0.07, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R200_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P4", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00030136986301369865, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_GLOBAL_VNET_IN", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 350, "unitPriceDollars": 0.035, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M50_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.0002913099315068493, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R80", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 18440, "unitPriceDollars": 1.844, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M10", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 260, "unitPriceDollars": 0.026, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3410, "unitPriceDollars": 0.341, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_CONTINUOUS_BACKUP", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 80, "unitPriceDollars": 0.00798904109589041, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P10", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00028125, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_GCP_DATA_RETURNED_SAME_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M250_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_SNAPSHOT_EXPORT_VM_M40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4210, "unitPriceDollars": 0.421, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "AP_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AWS_BACKUP_DOWNLOAD_VM", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2180, "unitPriceDollars": 0.218, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON><PERSON>_TIME_BASED_SNAPSHOT_COPY_LEVEL_6", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M300_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_DATA_TRANSFER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 150, "unitPriceDollars": 0.015, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_LAKE_AWS_DATA_RETURNED_SAME_REGION", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M200", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 47980, "unitPriceDollars": 4.798, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "CHARTS_DATA_DOWNLOADED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 10000, "unitPriceDollars": 1.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_RETURNED_INTERNET", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 870, "unitPriceDollars": 0.087, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 0.03, "sku": "REALM_APP_SYNC_FREE_TIER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 99.97, "sku": "REALM_APP_SYNC", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 800, "unitPriceDollars": 0.08, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "CENTRAL_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_GCP_DATA_RETURNED_INTERNET", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 850, "unitPriceDollars": 0.085, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_300_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 288, "unitPriceDollars": 0.0288, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P4", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00030136986301369865, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_300_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 192, "unitPriceDollars": 0.0192, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M10_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_CONTINUOUS_BACKUP", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 66, "unitPriceDollars": 0.006575342465753426, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_LEGACY_400_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 260, "unitPriceDollars": 0.026, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_A<PERSON>_SEARCH_INSTANCE_S50_COMPUTE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 9750, "unitPriceDollars": 0.975, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "rsId": "someTestRsId", "sku": "NDS_<PERSON><PERSON>_TIME_BASED_SNAPSHOT_COPY_LEVEL_4", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 140, "unitPriceDollars": 0.014, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M60_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M30", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1685, "unitPriceDollars": 0.1685, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R600_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P10", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00028125, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M100_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36, "unitPriceDollars": 0.00364, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_GCP_200_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 205, "unitPriceDollars": 0.0205, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_EXTENDED_IOPS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 9.589041095890411e-06, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00023103060787671228, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_M30_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_R200", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 36840, "unitPriceDollars": 3.684, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "dataLakeTenantId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_CONTINENT", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 200, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_GCP_SERVERLESS_RPU", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 1, "tierLowerBound": 0.0, "tierUpperBound": 50.0, "totalPriceCents": 475, "unitPriceDollars": 0.095, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 50.0, "sku": "NDS_GCP_SERVERLESS_RPU", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "tier": 2, "tierLowerBound": 50.0, "tierUpperBound": 550.0, "totalPriceCents": 235, "unitPriceDollars": 0.047, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M40_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2020, "unitPriceDollars": 0.202, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_R50_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44, "unitPriceDollars": 0.00438, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_SEARCH_INSTANCE_S30_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2640, "unitPriceDollars": 0.264, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R200", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 34030, "unitPriceDollars": 3.403, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P2", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3, "unitPriceDollars": 0.00027397260273972606, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M80_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 13620, "unitPriceDollars": 1.362, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_M50", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 6483, "unitPriceDollars": 0.6483, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 900, "unitPriceDollars": 0.09, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 25.0, "sku": "REALM_APP_COMPUTE_FREE_TIER", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 0, "unitPriceDollars": 0.0, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"appId": {"$oid": "0000000000000***********"}, "appName": "Test", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 75.0, "sku": "REALM_APP_COMPUTE", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 150, "unitPriceDollars": 0.02, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S40_COMPUTE_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 4750, "unitPriceDollars": 0.475, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S80_MEMORY_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16570, "unitPriceDollars": 1.657, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M90", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 26494, "unitPriceDollars": 2.6494, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_STORAGE_P40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00023103060787671228, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S30_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 3410, "unitPriceDollars": 0.341, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_STORAGE_IOPS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 1, "unitPriceDollars": 0.00011917808219178082, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M300_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 68599, "unitPriceDollars": 6.8599, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_INSTANCE_M200", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 44967, "unitPriceDollars": 4.4967, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AWS_INSTANCE_M100", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 30120, "unitPriceDollars": 3.012, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S80_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 22000, "unitPriceDollars": 2.2, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_DATA_TRANSFER_REGIONAL_VNET_IN", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 100, "unitPriceDollars": 0.01, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_PRIVATE_ENDPOINT_CAPACITY_UNITS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 300, "unitPriceDollars": 0.03, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "US_EAST", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "replicaSet": {"name": "someTestRsId"}, "restoreJobId": {"$oid": "0000000000000***********"}, "rsId": "someTestRsId", "sku": "NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P40", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 2, "unitPriceDollars": 0.00023103060787671228, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R80", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 16140, "unitPriceDollars": 1.614, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S40_COMPUTE_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 7090, "unitPriceDollars": 0.709, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "gcpRegionName": "EASTERN_US", "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_GCP_INSTANCE_R80_PAUSED", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 62, "unitPriceDollars": 0.00621, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_<PERSON><PERSON>_SEARCH_INSTANCE_S110_MEMORY_NVME", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 57870, "unitPriceDollars": 5.787, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"azureRegionName": "AUSTRALIA_CENTRAL", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "NDS_AZURE_SEARCH_INSTANCE_S60_MEMORY_LOCALSSD", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 11540, "unitPriceDollars": 1.154, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}, {"awsRegionName": "US_EAST_1", "clusterName": "someTestClusterName", "clusterUniqueId": {"$oid": "0000000000000***********"}, "discountCents": 0, "endDate": {"$date": "2025-01-15T00:00:00Z"}, "groupId": {"$oid": "000000000000000227415343"}, "orgId": {"$oid": "0000000000000***********"}, "overCommitmentFlag": false, "percentDiscount": 0.0, "quantity": 100.0, "sku": "FLEX_AWS_400_USAGE_HOURS", "startDate": {"$date": "2025-01-14T00:00:00Z"}, "supportSlaMinutes": 0, "totalPriceCents": 356, "unitPriceDollars": 0.0356, "usageDate": {"$date": "2025-01-14T00:00:00Z"}}]