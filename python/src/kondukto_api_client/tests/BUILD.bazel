# TEMPLATE START ===============================================================
# THIS FILE IS AUTO-GENERATED AND UPDATED WITH GAZELLE @unused _bin _lib _tst
load(
    "@rules_python//python:defs.bzl",
    _bin = "py_binary",
    _lib = "py_library",
    _tst = "py_test",
)
load("//python:defs.bzl", "py_binary", "py_library", "py_test")

# TEMPLATE END =================================================================
py_library(
    name = "lib",
    testonly = True,
    srcs = ["__init__.py"],
    imports = ["../.."],
    visibility = ["//python/src:__subpackages__"],
    deps = [":kondukto_api_client_test"],
)

py_binary(name = "bin")

py_test(name = "test")

py_test(
    name = "kondukto_api_client_test",
    srcs = ["kondukto_api_client_test.py"],
    imports = ["../.."],
    deps = ["//python/src/kondukto_api_client:lib"],
)
