from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import cast
from zoneinfo import ZoneInfo

import botocore.exceptions
import structlog

from immutable_evergreen.common.immutable_evergreen_consts import DeployStep
from immutable_evergreen.create_project.create_project import EvergreenProjectCreator
from immutable_evergreen.promote_project.promote_project import EvergreenProjectPromoter
from pct.apps.branch_cut.consts.deploy_consts import DeployEnv
from pct.apps.branch_cut.consts.evergreen_consts import ProjectTarget
from pct.apps.branch_cut.consts.github_consts import BranchTarget
from pct.apps.branch_cut.consts.release_consts import ReleaseStage
from pct.apps.branch_cut.evergreen import get_project_name_and_branch
from pct.apps.branch_cut.git import get_branch_name
from pct.apps.branch_cut.iam import evergreen_mms_role_trust_policy_needs_updated
from pct.apps.branch_cut.jira import (
    find_next_unreleased_version,
    get_cloud_release_ticket_or_notify,
)
from pct.apps.branch_cut.slack import (
    HOURS_TO_WAIT_FOR_SLACK_POLL,
    SLACK_POLL_CHECK_TIME,
    SLACK_POLL_CHECK_TIME_STR,
    SLACK_POLL_POST_TIME,
    SLACK_POLL_POST_TIME_STR,
)
from pct.apps.ops_manager.components import get_component_versions
from pct.atlas.document import cursor_to_branch_cut_release_state_list
from pct.atlas.foliage import get_foliage_conf_collection, project_conf_exists
from pct.commands.branch_cut import ReleaseSlackActions
from pct.consts.evg_consts import MMS_MASTER_PROJECT
from pct.consts.k8s_operator_consts import RELEASE_MDB_AGENT_VARIANT
from pct.consts.source_control_consts import MAIN_BRANCH, OrgAndRepoKey
from pct.consts.time_consts import TIMEZONE_NEW_YORK
from pct.engine.logic import Precondition, StepContext
from pct.evg.evergreen_api_client import get_evergreen_creds
from pct.evg.evergreen_helpers import get_mms_project_id
from pct.helpers.jira.search_issues import (
    BUMP_CONTAINER_IMAGE_MDB_AGENT_CM_ISSUE,
    find_maybe_preexisting_issue,
)
from pct.helpers.source_control import git_owner_and_repo_name
from pct.mongodb_slack.channels import get_cloud_slack_channel_id
from pct.mongodb_slack.tags import get_branch_cut_on_call_users
from pct.notifications.slack_release_notification import (
    ERR,
    SUCCESS,
    WARN,
    Notification,
)
from pct.notifications.slack_service import get_slack_message_hyperlink_or_raise
from pct.release_state.branch_cut_release_state import BranchCutReleaseState
from pct.source_control import PullRequestState
from pct.utils.branch_cut.jira import find_branch_cut_ticket
from pct.utils.k8s.git import (
    get_k8s_release_json_cm_version_updates,
    get_open_k8s_cm_version_bump_pr,
    issue_key_to_mdb_agent_k8s_branch_name,
)
from pct.utils.mongodb_agent.jira import (
    query_prepare_mongodb_agent_release_for_v20yymmdd_ticket,
    query_release_atlas_proxy_v20yymmdd_to_qa_stage_prod_ticket,
    query_release_mongodb_agent_to_atlas_ticket,
)
from simple_jira.constants import Project, Status
from text_utils.consts import DRY_RUN

# FUTURE:[REFACTOR](CLOUDP-187484) refactor pct precondition for common log messaging

log = structlog.get_logger()

STAGING_AND_PROD_RELEASE_TIME_OFFSET = timedelta(hours=16.5)  # 4:30 PM


class JiraFixVersionExists(Precondition):
    """Checks if the Jira fix version exists"""

    def __init__(self, expect: bool = True, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = f"{dry_run_str}" "The Jira fix version does{condition} exist"
        self.pass_message = message_template.format(condition="" if expect else " not")
        self.fail_message = message_template.format(condition=" not" if expect else "")
        super().__init__(self.fail_message)
        self.expect = expect
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        exists = context.jira.fix_version_exists(
            jira_project=Project.CLOUDP, fix_version_name=release_state.internal_version
        )
        success = exists == self.expect
        # dryrun
        if self.dry_run and not success:
            log.warning("Dryrun: bypassing check...")
            return True
        # Log Message - only log as failure if we expect fix version to exist and it does not
        # self.expect = False is used for the not_completed check so default log level on failure
        # is okay in that case since a failure for this check is expected when we have access
        log.info(
            (self.pass_message if success else self.fail_message),
            exists=exists,
            expect=self.expect,
            success=success,
            log_type=("failure" if not success and self.expect else "default"),
        )
        return success


class EnsureEvergreenAccess(Precondition):
    """Check for evergreen project access"""

    def __init__(self, expect: bool = True, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = f"{dry_run_str}" "{condition} to access base Evergreen projects"
        self.pass_message = message_template.format(condition="Able" if expect else "Unable")
        self.fail_message = message_template.format(condition="Unable" if expect else "Able")
        super().__init__(self.fail_message)
        self.expect = expect
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        config_data = get_evergreen_creds(credentials_store=context.credentials_store)
        evergreen_creator = EvergreenProjectCreator(config_data=config_data)
        template_name = "mms-qa-template"
        source_project_name = MMS_MASTER_PROJECT
        template_url = f"https://spruce.mongodb.com/project/{template_name}/settings/general"
        source_project_url = (
            f"https://spruce.mongodb.com/project/{source_project_name}/settings/general"
        )

        # Primary check
        log.info("Checking if we can access the base Evergreen Projects...")
        template_exists = evergreen_creator.project_exists(project_name=template_name)
        source_project_exists = evergreen_creator.project_exists(project_name=source_project_name)
        access = template_exists and source_project_exists
        success = access == self.expect

        # Log Message - only log as failure if we expect access and do not have it
        # self.expect = False is used for the not_completed check so default log level on failure
        # is okay on failure since a failure for this check is expected when we have access
        log.info(
            (self.pass_message if success else self.fail_message),
            projects=[template_name, source_project_name],
            urls=[template_url, source_project_url],
            access=access,
            expect=self.expect,
            success=success,
            log_type=("failure" if not success and self.expect else "default"),
        )

        # dryrun
        if self.dry_run and not success:
            log.warning("Dryrun: bypassing check...")
            return True

        # Failure message - only notify if we expect access and do not have it
        if not success and self.expect:
            context.notifications.append(
                Notification(
                    f"{' '.join(get_branch_cut_on_call_users(context.credentials_store))} "
                    "Unable to access Evergreen projects: "
                    f"<{source_project_url}|{source_project_name}>,  "
                    f"<{template_url}|{template_name}> ",
                    ERR,
                )
            )

        return success


class EvergreenProjectExists(Precondition):
    """Check if the Evergreen Project already exists"""

    def __init__(
        self, branch_target: BranchTarget, expect: bool = True, dry_run: bool = False
    ) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str} The {branch_target} branch "
            "Evergreen Project has{condition} been created"
        )
        self.pass_message = message_template.format(condition="" if expect else " not")
        self.fail_message = message_template.format(condition=" not" if expect else "")
        super().__init__(self.fail_message)
        self.dry_run = dry_run
        self.expect = expect
        self.branch_target = branch_target

    def check(self, context: StepContext) -> bool:
        """check"""
        config_data = get_evergreen_creds(credentials_store=context.credentials_store)
        evergreen_creator = EvergreenProjectCreator(config_data=config_data)
        branch_name = get_branch_name(
            context=context, branch_target=self.branch_target, dry_run=self.dry_run
        )
        project_name = get_mms_project_id(branch_name)

        # Precheck message
        log.info(
            f"Checking if: {self.pass_message}",
            url=f"https://spruce.mongodb.com/project/{project_name}/settings/general",
        )

        # Primary check
        exists = evergreen_creator.project_exists(project_name=project_name)

        # Log Message
        success = exists == self.expect
        log.info(
            (self.pass_message if success else self.fail_message),
            log_type=("default" if success else "failure"),
        )

        # Return EvergreenProjectExists based on result
        if self.dry_run and not success:
            log.warning("Dryrun: bypassing check...")
            return True
        return success


class EvergreenProjectFoliageConfExists(Precondition):
    """Check if the Evergreen Project has been added to Foliage"""

    def __init__(
        self, branch_target: BranchTarget, expect: bool = True, dry_run: bool = False
    ) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}The Evergreen Project for {branch_target} branch "
            "has{condition} been added to Foliage"
        )
        self.pass_message = message_template.format(condition="" if expect else " not")
        self.fail_message = message_template.format(condition=" not" if expect else "")
        super().__init__(self.fail_message)
        self.branch_target = branch_target
        self.dry_run = dry_run
        self.expect = expect

    def check(self, context: StepContext) -> bool:
        """check"""
        branch_name = get_branch_name(
            context=context, branch_target=self.branch_target, dry_run=self.dry_run
        )
        project_name = get_mms_project_id(branch=branch_name)
        # get foliage collection
        foliage_collection = get_foliage_conf_collection(
            credentials_store=context.credentials_store
        )

        # Precheck message
        log.info(f"Checking if: {self.pass_message}")

        # Primary check
        foliage_conf_exists = project_conf_exists(
            foliage_conf_collection=foliage_collection, project_name=project_name
        )

        # Log Message
        success = foliage_conf_exists == self.expect
        log.info(
            (self.pass_message if success else self.fail_message),
            log_type=("default" if success else "failure"),
        )

        # Return EvergreenProjectFoliageConfExists based on result
        if self.dry_run and not success:
            log.warning("Dryrun: bypassing check...")
            return True
        return success


class EvergreenProjectDeployStepIsCorrect(Precondition):
    """Check if the Evergreen Project deploy step is set to the desired value"""

    def __init__(
        self,
        branch_target: BranchTarget,
        project_target: ProjectTarget,
        deploy_step: DeployStep,
        expect: bool = True,
        dry_run: bool = False,
    ) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}"
            f"The {project_target} Evergreen Project's deploy_step "
            "is{condition} set to its expected value "
            f"{deploy_step} ({branch_target})"
        )
        self.pass_message = message_template.format(condition="" if expect else " not")
        self.fail_message = message_template.format(condition=" not" if expect else "")
        super().__init__(self.fail_message)
        self.branch_target = branch_target
        self.project_target = project_target
        self.deploy_step = deploy_step
        self.dry_run = dry_run
        self.expect = expect

    def check(self, context: StepContext) -> bool:
        """check"""
        config_data = get_evergreen_creds(credentials_store=context.credentials_store)
        evergreen_promoter = EvergreenProjectPromoter(config_data=config_data)
        evergreen_creator = EvergreenProjectCreator(config_data=config_data)
        # get project info
        project_name, _ = get_project_name_and_branch(
            context=context,
            branch_target=self.branch_target,
            project_target=self.project_target,
            dry_run=self.dry_run,
        )
        if not evergreen_creator.project_exists(project_name=project_name):
            log.info(
                "The Evergreen project does not exist.",
                project_name=project_name,
            )
            return not self.expect
        # check if deploy_step is already set to desired value
        return (
            evergreen_promoter.is_already_in_deploy_step(
                project_name=project_name, deploy_step=self.deploy_step
            )
            == self.expect
        )


class FoundBranchCutPRState(Precondition):
    """Check if the Branch cut PR is in the expected state"""

    def __init__(
        self,
        deploy_env: DeployEnv,
        expect: bool = True,
        state: PullRequestState = PullRequestState.ALL,
        dry_run: bool = False,
    ) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        state_string = "any" if state == PullRequestState.ALL else state.value
        message_template = (
            f"{dry_run_str}"
            "The Branch Cut PR does{condition} already exists in "
            f"{state_string} state ({deploy_env.cloud_env})"
        )
        self.pass_message = message_template.format(condition="" if expect else " not")
        self.fail_message = message_template.format(condition=" not" if expect else "")
        super().__init__(self.fail_message)
        self.deploy_env = deploy_env
        self.dry_run = dry_run
        self.expect = expect
        self.state = state

    def check(self, context: StepContext) -> bool:
        """check"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        org_and_repo = OrgAndRepoKey.MMS

        git_client = context.get_github_client(org_and_repo=org_and_repo)
        source_branch = release_state.get_release_issue_key(deploy_env=self.deploy_env)
        repo = git_owner_and_repo_name(
            org_and_repo=org_and_repo, credentials_store=context.credentials_store
        )
        base_branch = get_branch_name(
            context=context, branch_target=self.deploy_env.branch_target, dry_run=self.dry_run
        )

        # Precheck message
        log.info(
            f"Checking if: {self.pass_message}",
            base_branch=base_branch,
            source_branch=source_branch,
            repo=repo,
            state=self.state,
            deploy_env=self.deploy_env,
        )

        # Primary check
        found_pr, pull_request = git_client.pull_request_exists(
            base_branch=base_branch, source_branch=source_branch, repo=repo, state=self.state
        )
        url = pull_request.get("html_url", "") if isinstance(pull_request, dict) else ""

        # Log Message
        success = found_pr == self.expect
        log.info(
            (self.pass_message if success else self.fail_message),
            log_type=("default" if success else "failure"),
            url=url,
            source_branch=source_branch,
            state=self.state,
            deploy_env=self.deploy_env,
        )

        # Return FoundBranchCutPRState based on result
        if success:
            return True
        if self.dry_run:
            log.warning(
                "Dryrun: bypassing check...",
                deploy_env=self.deploy_env,
            )
            return True
        if found_pr is None:
            log.error(
                "Unable to determine if the Pull Request already exists",
                source_branch=source_branch,
                state=self.state,
                deploy_env=self.deploy_env,
            )
            return False
        return False


class BranchExists(Precondition):
    """Check if the Branch Exists"""

    def __init__(
        self, branch_target: BranchTarget, expect: bool = True, dry_run: bool = False
    ) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = f"{dry_run_str} The {branch_target} branch " "does{condition} exist"
        self.pass_message = message_template.format(condition="" if expect else " not")
        self.fail_message = message_template.format(condition=" not" if expect else "")
        super().__init__(self.fail_message)
        self.dry_run = dry_run
        self.expect = expect
        self.branch_target = branch_target

    def check(self, context: StepContext) -> bool:
        """run"""
        org_and_repo = OrgAndRepoKey.MMS

        git_client = context.get_github_client(org_and_repo=org_and_repo)
        new_branch_name = get_branch_name(
            context=context, branch_target=self.branch_target, dry_run=self.dry_run
        )
        repo = git_owner_and_repo_name(
            org_and_repo=org_and_repo, credentials_store=context.credentials_store
        )

        # Precheck message
        log.info(f"Checking if: {self.pass_message}", branch=new_branch_name, repo=repo)

        # Primary check
        exists = git_client.branch_exists(name=new_branch_name, repo=repo)

        # Log Message
        success = exists == self.expect
        log.info(
            (self.pass_message if success else self.fail_message),
            log_type=("default" if success else "failure"),
        )

        # Return BranchFromMasterExists based on result
        if self.dry_run and not success:
            log.warning("Dryrun: bypassing check...")
            return True
        return success


class EvergreenProjectIsEnabled(Precondition):
    """Check if the Evergreen project is enabled"""

    def __init__(self, branch_target: BranchTarget, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = f"{dry_run_str}The new {branch_target} Evergreen project is disabled."
        super().__init__(message_template)
        self.branch_target = branch_target
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        """check"""
        evergreen_client = context.get_evergreen_client()

        log.info("Checking if the new Evergreen project is enabled...")
        branch = get_branch_name(
            context=context, branch_target=self.branch_target, dry_run=self.dry_run
        )
        if self.dry_run:
            log.warning(f"{DRY_RUN}SlackPollIsReadyToCheck", branch=branch)
            return True
        # get mms Evergreen project ID
        evergreen_project_id = get_mms_project_id(branch=branch)
        # get the Evergreen project
        evergreen_project = evergreen_client.get_project_if_exists(project_id=evergreen_project_id)
        if evergreen_project is None:
            log.error("The Evergreen project could not be found.")
            context.notifications.append(
                Notification(
                    f"{' '.join(get_branch_cut_on_call_users(context.credentials_store))} The "
                    f"*{branch}* Evergreen project does not exist. "
                    "Please investigate why and remediate.",
                    ERR,
                )
            )
            return False
        log.info(
            "The Evergreen project status.",
            project_id=evergreen_project_id,
            enabled=evergreen_project.enabled,
        )
        return evergreen_project.enabled


class BranchCutShaHasBeenRecorded(Precondition):
    """Check the DB for the Branch Cut Sha"""

    def __init__(self, deploy_env: DeployEnv, expect: bool = True, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}" "The Branch Cut Sha has{condition} been recorded " f"({deploy_env})"
        )
        self.pass_message = message_template.format(condition="" if expect else " not")
        self.fail_message = message_template.format(condition=" not" if expect else "")
        super().__init__(self.fail_message)
        self.deploy_env = deploy_env
        self.dry_run = dry_run
        self.expect = expect

    def check(self, context: StepContext) -> bool:
        """check"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )

        # Precheck message
        log.info(f"Checking if: {self.pass_message} ({self.deploy_env})")

        # Primary check
        sha = release_state.get_since_commit_sha(deploy_env=self.deploy_env)

        # Check for invalid combination
        sha_recorded = sha != ""

        # Log Message
        success = sha_recorded == self.expect
        log.info(
            (self.pass_message if success else self.fail_message),
            log_type=("default" if success else "failure"),
            deploy_env=self.deploy_env,
        )

        # Return BranchCutShaHasBeenRecorded based on result
        if self.dry_run and not success:
            log.warning("Dryrun: bypassing check...")
            return True
        return success


class SlackPollIsNotPosted(Precondition):
    """checks if branch cut Slack poll has not been posted"""

    def __init__(self, deploy_env: DeployEnv, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        super().__init__(
            f"{dry_run_str}The Slack poll has already been posted ({deploy_env.cloud_env})"
        )
        self.deploy_env = deploy_env
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        poll_ts = release_state.get_poll_ts(deploy_env=self.deploy_env)
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}SlackPollIsNotPosted",
                release_state=release_state,
                poll_ts=poll_ts,
                deploy_env=self.deploy_env,
            )
            return True
        return poll_ts == ""


class SlackPollIsReadyToPost(Precondition):
    """checks if branch cut Slack poll is ready to post"""

    def __init__(self, deploy_env: DeployEnv, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        super().__init__(
            f"{dry_run_str}"
            f"PCT waits until after 1:00PM New York time on the day before the branch cut "
            "before posting the Slack poll for Cloud Leads to vote in."
        )
        self.deploy_env = deploy_env
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        poll_ts = release_state.get_poll_ts(deploy_env=self.deploy_env)
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}SlackPollIsReadyToPost", release_state=release_state, poll_ts=poll_ts
            )
            return True

        # check if it is after 1:00PM New York time on the day before branch cut date
        current_time = datetime.now(ZoneInfo(TIMEZONE_NEW_YORK))
        deploy_date = release_state.get_cloud_deploy_date(
            jira_client=context.jira, deploy_env=self.deploy_env
        )
        # day before branch cut date at 1:00PM New York time
        one_pm_est_day_before_deploy_date = datetime.combine(
            date=deploy_date.date(),
            time=SLACK_POLL_POST_TIME,
            tzinfo=ZoneInfo(TIMEZONE_NEW_YORK),
        ) - timedelta(days=1)
        log.debug(
            "Poll post times.",
            current_time=current_time,
            time_to_wait_until=one_pm_est_day_before_deploy_date,
        )
        if current_time < one_pm_est_day_before_deploy_date:
            log.info(
                f"Waiting until after {SLACK_POLL_POST_TIME_STR} New York time on the day "
                f"before the {self.deploy_env.cloud_env} release date before posting the Slack "
                "poll for Cloud leads to vote in.",
                current_time=current_time,
                time_to_wait_until=one_pm_est_day_before_deploy_date,
            )
            return False
        log.info("Slack poll is ready to post.")
        return True


class SlackPollIsPosted(Precondition):
    """checks if branch cut Slack poll has been posted"""

    def __init__(self, deploy_env: DeployEnv, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        super().__init__(
            f"{dry_run_str}The Slack poll has not been posted ({deploy_env.cloud_env})"
        )
        self.deploy_env = deploy_env
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}SlackPollIsPosted",
                release_state=release_state,
                poll_ts=release_state.get_poll_ts(deploy_env=self.deploy_env),
                deploy_env=self.deploy_env,
            )
            return True
        return release_state.get_poll_ts(deploy_env=self.deploy_env) != ""


def more_than_hours_since(hours: float, event: datetime) -> bool:
    """
    @return: True if there are more than the specified number of hours since the specified event
    """
    hours_since_event = (datetime.now() - event).total_seconds() / 60 / 60
    return hours_since_event >= hours


class SlackPollIsReadyToCheck(Precondition):
    """checks if branch cut Slack poll is ready to check"""

    def __init__(
        self,
        deploy_env: DeployEnv,
        hours: int = HOURS_TO_WAIT_FOR_SLACK_POLL,
        dry_run: bool = False,
    ) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        super().__init__(
            f"{dry_run_str}"
            f"The Slack poll was posted less than {hours} hours ago. "
            f"PCT is giving individuals some time to vote in the poll ({deploy_env.cloud_env})..."
        )
        self.deploy_env = deploy_env
        self.dry_run = dry_run
        self.hours = hours

    def check(self, context: StepContext) -> bool:
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        poll_ts = release_state.get_poll_ts(deploy_env=self.deploy_env)
        poll_permalink_id = release_state.get_poll_permalink_id(deploy_env=self.deploy_env)
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}SlackPollIsReadyToCheck",
                release_state=release_state,
                poll_ts=poll_ts,
                deploy_env=self.deploy_env,
            )
            return True

        if poll_ts == "":
            log.info(
                "Slack poll should be posted and the poll timestamp should already be set",
                poll_ts=poll_ts,
                deploy_env=self.deploy_env,
            )
            return False
        # check if enough time has passed since the Slack poll was posted
        enough_time_has_passed = more_than_hours_since(
            hours=self.hours, event=datetime.fromtimestamp(float(poll_ts))
        )
        if not enough_time_has_passed:
            cloud_channel_id = get_cloud_slack_channel_id(context.credentials_store)
            hyperlink = get_slack_message_hyperlink_or_raise(
                slack_service=context.slack,
                channel_id=cloud_channel_id,
                message_ts=poll_ts,
                permalink_id=poll_permalink_id,
            )
            # override title for precondition message with
            # link to Slack poll if it exists and precondition failed
            self.title = (
                f"The <{hyperlink}|Slack poll> was posted less than {self.hours} hours "
                f"ago. PCT is giving individuals some time to vote in the poll..."
            )
            return False
        # check if it is after 7:00AM New York time on the cloud environment release date
        current_time = datetime.now(ZoneInfo(TIMEZONE_NEW_YORK))
        deploy_date = release_state.get_cloud_deploy_date(
            jira_client=context.jira, deploy_env=self.deploy_env
        )
        check_poll_time = datetime.combine(
            date=deploy_date.date(),
            time=SLACK_POLL_CHECK_TIME,
            tzinfo=ZoneInfo(TIMEZONE_NEW_YORK),
        )
        log.debug(
            "Poll check times",
            current_time=current_time,
            time_to_wait_until=check_poll_time,
        )
        if current_time < check_poll_time:
            cloud_channel_id = get_cloud_slack_channel_id(context.credentials_store)
            hyperlink = get_slack_message_hyperlink_or_raise(
                slack_service=context.slack,
                channel_id=cloud_channel_id,
                message_ts=poll_ts,
                permalink_id=poll_permalink_id,
            )
            # override title for precondition message with
            # link to Slack poll if it exists and precondition failed
            self.title = (
                f"Waiting until {SLACK_POLL_CHECK_TIME_STR} New York time on the "
                f"{self.deploy_env.cloud_env} release date to check if the "
                f"<{hyperlink}|Slack poll> has passed..."
            )
            return False
        log.info("Slack poll is ready to check for passing")
        return True


class SlackPollDidNotPass(Precondition):
    """checks if branch cut Slack poll did not pass"""

    def __init__(self, deploy_env: DeployEnv, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        super().__init__(
            f"{dry_run_str}"
            f"The Slack poll indicates we are ready to proceed with the "
            f"{deploy_env.slack_poll_go_ahead_for}"
        )
        self.deploy_env = deploy_env
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}SlackPollDidNotPass",
                release_state=release_state,
                poll_pass=release_state.get_poll_pass(deploy_env=self.deploy_env),
                deploy_env=self.deploy_env,
            )
            return True
        return not release_state.get_poll_pass(deploy_env=self.deploy_env)


class SlackPollDidPass(Precondition):
    """checks if branch cut Slack poll passed"""

    def __init__(self, deploy_env: DeployEnv, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        super().__init__(f"{dry_run_str}The Slack Poll has not passed ({deploy_env})")
        self.deploy_env = deploy_env
        self.dry_run = dry_run

    @Precondition.clear_hints
    def check(self, context: StepContext) -> bool:
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        if self.deploy_env == DeployEnv.QA:
            poll_flag = "poll_pass"
        elif self.deploy_env == DeployEnv.STAGE:
            poll_flag = "staging_release_poll_pass"
        else:
            raise ValueError(f"Invalid deploy environment: '{self.deploy_env}'")
        self.add_hint(
            f"`{poll_flag}=true` will be automatically set to true "
            "if the poll has no votes in opposition. "
        )
        self.add_hint("If there are votes in opposition, please investigate why. ")

        if self.dry_run:
            log.warning(
                f"{DRY_RUN}SlackPollDidPass",
                release_state=release_state,
                key=context.release_ticket.key,
            )
            return True

        self.add_hint(
            f"If it's determined that the {self.deploy_env.slack_poll_go_ahead_for} can proceed, "
            "click →",
            slack_action=ReleaseSlackActions.get_set_poll_pass_action(
                release_version=release_state.internal_version,
                poll_flag=poll_flag,
            ),
        )

        return release_state.get_poll_pass(deploy_env=self.deploy_env)


class OpsManagerK8sCMVersionBumpPRIsNotNeededOrUpToDate(Precondition):
    """Checks if a PR update is needed to bump OM Container Image Cloud Manager version"""

    def __init__(self, expect: bool, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        super().__init__(f"{dry_run_str}" "OM K8s Cloud Manager version needs update merged")
        self.expect = expect
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        """Check if the Ticket, PR, or CM version need updates"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )

        issue = find_maybe_preexisting_issue(
            context.jira,
            BUMP_CONTAINER_IMAGE_MDB_AGENT_CM_ISSUE,
            release_state.internal_version,
        )
        org_and_repo = OrgAndRepoKey.MMS
        github_client = context.get_github_client(org_and_repo=org_and_repo)
        existing_pull_request = (
            get_open_k8s_cm_version_bump_pr(
                credentials_store=context.credentials_store,
                github_client=github_client,
                issue_key=issue.key,
            )
            if issue is not None
            else None
        )

        mms_repo_branch = release_state.release_branch if release_state.cut else MAIN_BRANCH
        # this check shouldn't run prior to the branch being cut, but logging just in case it is
        if mms_repo_branch == MAIN_BRANCH:
            log.warning(
                "The release branch has not been cut yet. "
                "Pulling Cloud Manager components' versions from main branch of 10gen/mms."
            )
        # Check if not up to date because the CM Version need updates
        component_version_map = get_component_versions(
            ref=mms_repo_branch, credential_store=context.credentials_store
        )
        # if PR exists, use the PR's source branch, otherwise use the master branch
        branch_name = (
            issue_key_to_mdb_agent_k8s_branch_name(issue_key=issue.key)
            if issue is not None and existing_pull_request is not None
            else MAIN_BRANCH
        )
        _, needs_update = get_k8s_release_json_cm_version_updates(
            credentials_store=context.credentials_store,
            github_client=github_client,
            branch_name=branch_name,
            component_version_map=component_version_map,
        )
        log.info(
            "Comparing OM K8s changes on branch", branch_name=branch_name, needs_update=needs_update
        )

        return self.expect == (not needs_update)


class EvergreenIAMRoleTrustPoliciesAreMissingNewProjectID(Precondition):
    """checks if the trust policies for the mms Evergreen IAM roles
    need updated with the new Evergreen project's unique id"""

    def __init__(self, branch_target: BranchTarget, expect: bool, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        super().__init__(
            f"{dry_run_str}"
            f"Trust Policies for the mms Evergreen IAM roles do not need updated ({branch_target})"
        )
        self.branch_target = branch_target
        self.expect = expect
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        """check"""
        # get mms Evergreen project ID
        branch = get_branch_name(
            context=context, branch_target=self.branch_target, dry_run=self.dry_run
        )
        evergreen_project_id = get_mms_project_id(branch=branch)
        log.info(
            "Checking if trust policies of mms Evergreen IAM roles need "
            "updated with the new Evergreen project's unique id...",
            evergreen_project_id=evergreen_project_id,
        )
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}TrustPoliciesNeedUpdated",
                branch_target=self.branch_target,
                key=context.release_ticket.key,
            )
            return True
        log.info(
            "Getting the Evergreen project's unique identifier...", project_id=evergreen_project_id
        )
        evergreen_client = context.get_evergreen_client()
        # get the Evergreen project
        evergreen_project = evergreen_client.get_project_if_exists(project_id=evergreen_project_id)
        if evergreen_project is None:
            log.warning("The Evergreen project could not be found.")
            return True
        unique_project_id = evergreen_project.id
        log.info(
            "Successfully retrieved the Evergreen project's unique identifier.",
            project_id=evergreen_project_id,
            unique_project_id=unique_project_id,
        )
        try:
            trust_policies_need_updated = evergreen_mms_role_trust_policy_needs_updated(
                credentials_store=context.credentials_store,
                unique_project_id=unique_project_id,
            )
        except botocore.exceptions.ClientError as boto_exception:
            exception_log_message = (
                "An exception occurred when checking if the trust policies "
                "for the mms Evergreen IAM roles need updated."
            )
            log.exception(exception_log_message, response=boto_exception.response)
            context.notifications.append(Notification(message=exception_log_message, level=ERR))
            return True

        return trust_policies_need_updated == self.expect


class PackageCloudDidCompleteSuccessfully(Precondition):
    """checks if PACKAGE_CLOUD_DEPLOY_GATE Evergreen task has completed for the release branch"""

    def __init__(self, deploy_env: DeployEnv, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        super().__init__(
            f"{dry_run_str}"
            "Waiting for *PACKAGE_CLOUD_DEPLOY_GATE* Evergreen task to successfully "
            f"complete ({deploy_env.cloud_env})..."
        )
        self.deploy_env = deploy_env
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        """check"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        branch = get_branch_name(
            context=context, branch_target=self.deploy_env.branch_target, dry_run=self.dry_run
        )
        log.info(
            "Checking if PACKAGE_CLOUD_DEPLOY_GATE has "
            "successfully finished for the Evergreen project...",
            branch=branch,
            deploy_env=self.deploy_env,
        )
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}PackageCloudDidCompleteSuccessfully",
                branch=branch,
                deploy_env=self.deploy_env,
            )
            return True
        evergreen_client = context.get_evergreen_client()
        # get mms Evergreen project ID
        evergreen_project_id = get_mms_project_id(branch=branch)
        # get commit SHA to check for completed tasks after
        since_commit = release_state.get_since_commit_sha(deploy_env=self.deploy_env)
        # check if PACKAGE_CLOUD_DEPLOY_GATE Evergreen task has completed successfully
        return evergreen_client.there_is_a_successful_task_run_for_project(
            project_id=evergreen_project_id,
            task_name="PACKAGE_CLOUD_DEPLOY_GATE",
            build_variant=self.deploy_env.deploy_gate_variant,
            since_commit=since_commit,
        )


class BranchIsDeployed(Precondition):
    """checks that the release status is FINISHED and the Jira ticket is resolved/closed"""

    def __init__(self, deploy_env: DeployEnv, expect: bool, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}"
            "The new release branch has {condition} been deployed to "
            f"{deploy_env.cloud_env}"
        )
        self.pass_message = message_template.format(condition="already" if expect else "not")
        self.fail_message = message_template.format(condition="not" if expect else "already")
        super().__init__(self.fail_message)
        self.deploy_env = deploy_env
        self.dry_run = dry_run
        self.expect = expect

    def check(self, context: StepContext) -> bool:
        """check"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        log.info(
            "Checking that the release is complete...", release_branch=release_state.release_branch
        )
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}BranchIsDeployedtoQAandQAgov",
                release_state=release_state,
                key=context.release_ticket.key,
            )
            return True

        # check if the current release to DeployEnv is complete
        deploy_status_map = {
            deploy_target: release_state.get_deploy_status(deploy_target)
            for deploy_target in self.deploy_env.deploy_targets
        }
        deployment_is_complete = all(deploy_status_map.values())
        if deployment_is_complete:
            log.info("The deployment is complete", deploy_env=self.deploy_env)
        else:
            log.info("The deployment is not complete", deploy_env=self.deploy_env)
        # check if deployment target status are in the expected state
        success = deployment_is_complete == self.expect
        if success:
            log.info(
                "The deployment(s) are in their expected state",
                expected_finished=self.expect,
                finished=deployment_is_complete,
                success=success,
            )
        else:
            log.info(
                "The deployment(s) are not in their expected state",
                expected_finished=self.expect,
                finished=deployment_is_complete,
                success=success,
            )
            # if the deployment(s) are not finished and we
            # are expecting them to be, then post the statuses of each
            if not deployment_is_complete:
                for deploy_target, status in deploy_status_map.items():
                    context.notifications.append(
                        Notification(
                            f"{deploy_target} release status: "
                            f"`{"COMPLETED" if status is True else "IN PROGRESS"}`\n",
                            SUCCESS if status is True else WARN,
                        )
                    )
        return success


class CloudReleaseTicketHasExpectedStatus(Precondition):
    """Check if the cloud release Jira ticket has the expected status"""

    def __init__(
        self, status: Status, deploy_env: DeployEnv, expect: bool, dry_run: bool = False
    ) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}"
            "The cloud release ticket is{condition} in status "
            f"{status} ({deploy_env.cloud_env})"
        )
        self.pass_message = message_template.format(condition="" if expect else " not")
        self.fail_message = message_template.format(condition=" not" if expect else "")
        super().__init__(self.fail_message)
        self.status = status
        self.deploy_env = deploy_env
        self.expect = expect
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        """check"""
        log.info(f"Checking if: {self.pass_message}")
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        # get issue
        issue = get_cloud_release_ticket_or_notify(
            context=context,
            release_state=release_state,
            deploy_env=self.deploy_env,
            issue_for=f"to check if it is in status {self.status}",
        )
        if issue is None:
            return False
        if self.status == Status.OPEN:
            result = issue.is_open() == self.expect
        elif self.status in {Status.IN_PROGRESS, Status.IN_CODE_REVIEW}:
            result = (issue.is_in_progress() or issue.is_in_code_review()) == self.expect
        elif self.status in {Status.RESOLVED, Status.CLOSED}:
            result = issue.is_closed() == self.expect
        else:
            result = (issue.get_item("Status") == self.status) == self.expect
        if self.dry_run:
            return True
        return result


class ReleaseMongodbAgentToAtlasIssueHasBeenCreated(Precondition):
    """checks if the release MongoDB Agent to Atlas release ticket has been created"""

    def __init__(self, expect: bool, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}"
            "The release MongoDB Agent to Atlas ticket has {condition} been created."
        )
        self.pass_message = message_template.format(condition="already" if expect else "not")
        self.fail_message = message_template.format(condition="not" if expect else "already")
        super().__init__(self.fail_message)
        self.dry_run = dry_run
        self.expect = expect

    def check(self, context: StepContext) -> bool:
        """check"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        log.info(
            "Checking that the release MongoDB Agent to Atlas ticket has been created...",
            release_branch=release_state.release_branch,
        )
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}ReleaseMongodbAgentToAtlasIssueHasBeenCreated",
                release_state=release_state,
                key=context.release_ticket.key,
            )
            return True
        # query Jira for ticket
        ticket = query_release_mongodb_agent_to_atlas_ticket(
            jira=context.jira, version=release_state.internal_version
        )
        # check if matching issue was found
        exists = ticket is not None

        # check if ticket's existence is in expected state
        success = exists == self.expect
        message = self.pass_message if success else self.fail_message
        log.info(
            message,
            expect=self.expect,
            exists=exists,
            success=success,
        )
        return success


class PrepareMongodbAgentReleaseForv20YYMMDDIssueHasBeenCreated(Precondition):
    """checks if the prepare MongoDB Agent release for v20YYMMDD ticket has been created"""

    def __init__(self, expect: bool, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}"
            "The prepare MongoDB Agent release for v20YYMMDD ticket has {condition} been created."
        )
        self.pass_message = message_template.format(condition="already" if expect else "not")
        self.fail_message = message_template.format(condition="not" if expect else "already")
        super().__init__(self.fail_message)
        self.dry_run = dry_run
        self.expect = expect

    def check(self, context: StepContext) -> bool:
        """check"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        log.info(
            "Checking that the prepare MongoDB Agent release for v20YYMMDD ticket "
            "has been created...",
            release_branch=release_state.release_branch,
        )
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}PrepareMongodbAgentReleaseForv20YYMMDDIssueHasBeenCreated",
                release_state=release_state,
                key=context.release_ticket.key,
            )
            return True

        # query Jira for ticket
        ticket = query_prepare_mongodb_agent_release_for_v20yymmdd_ticket(
            jira=context.jira, version=release_state.internal_version
        )
        # check if matching issue was found
        exists = ticket is not None

        # check if ticket's existence is in expected state
        success = exists == self.expect
        message = self.pass_message if success else self.fail_message
        log.info(
            message,
            expect=self.expect,
            exists=exists,
            success=success,
        )
        return success


class ReleaseAtlasProxyv20YYMMDDToQaStageProdIssueHasBeenCreated(Precondition):
    """checks if the release Atlas Proxy v20YYMMDD to QA/Staging/Prod ticket has been created"""

    def __init__(self, expect: bool, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}"
            "The release Atlas Proxy v20YYMMDD to QA/Staging/Prod ticket "
            "has {condition} been created."
        )
        self.pass_message = message_template.format(condition="already" if expect else "not")
        self.fail_message = message_template.format(condition="not" if expect else "already")
        super().__init__(self.fail_message)
        self.dry_run = dry_run
        self.expect = expect

    def check(self, context: StepContext) -> bool:
        """check"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        log.info(
            "Checking that the release Atlas Proxy v20YYMMDD to QA/Staging/Prod ticket "
            "has been created...",
            release_branch=release_state.release_branch,
        )
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}ReleaseAtlasProxyv20YYMMDDToQaStageProdIssueHasBeenCreated",
                release_state=release_state,
                key=context.release_ticket.key,
            )
            return True

        # query Jira for ticket
        ticket = query_release_atlas_proxy_v20yymmdd_to_qa_stage_prod_ticket(
            jira=context.jira, version=release_state.internal_version
        )
        # check if matching issue was found
        exists = ticket is not None

        # check if ticket's existence is in expected state
        success = exists == self.expect
        message = self.pass_message if success else self.fail_message
        log.info(
            message,
            expect=self.expect,
            exists=exists,
            success=success,
        )
        return success


class NextReleaseIsScheduled(Precondition):
    """checks that the release status is FINISHED and the Jira ticket is resolved/closed"""

    def __init__(self, expect: bool, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}"
            "The next branch cut release {condition} in Jira and the release state."
        )
        self.pass_message = message_template.format(
            condition="already exists" if expect else "does not exist"
        )
        self.fail_message = message_template.format(
            condition="does not exist" if expect else "already exists"
        )
        super().__init__(self.fail_message)
        self.expect = expect
        self.dry_run = dry_run

    def check(self, context: StepContext) -> bool:
        """check"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        log.info(f"Checking if: {self.pass_message}", release_branch=release_state.release_branch)
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}NextReleaseIsScheduled",
                release_state=release_state,
                key=context.release_ticket.key,
            )
            return True
        # get next unrelease mms version
        next_release_version = find_next_unreleased_version(
            jira=context.jira, current_version=release_state.internal_version
        )
        if next_release_version is None:
            log.error("Could not find the next unreleased mms version.")
            return False
        next_release_version_string = next_release_version.name
        log.info("Next release version", next_release_version=next_release_version_string)
        # check if next release ticket exists already
        next_release_issue = find_branch_cut_ticket(
            jira=context.jira, version=next_release_version_string, deploy_env=DeployEnv.ALL
        )
        # check if next release state exists already
        next_release_state = context.atlas.get_release_state_for_version(
            internal_version=next_release_version_string
        )
        # check if both a ticket and a release state exists for next branch cut
        if next_release_issue is not None and next_release_state is not None:
            log.info(
                "The next release has already been scheduled.",
                issue_key=next_release_issue.key,
                release_state=next_release_state,
            )
            return self.expect
        # only either a ticket or a release state exists for the next branch cut
        if next_release_state is not None:
            # release state exists but ticket does not
            log.error(
                "A release state already exists for the next branch cut release version, but "
                "a Jira issue could not be found for the release.",
                next_release_state=next_release_state,
                next_release_version=next_release_version_string,
            )
            context.notifications.append(
                Notification(
                    f"{' '.join(get_branch_cut_on_call_users(context.credentials_store))} A "
                    f"branch cut release state already exists for the "
                    f"`{next_release_version_string}` branch cut release but the release ticket "
                    "does not. Please remediate this.",
                    ERR,
                )
            )
            # fail precondition regardless of self.expect since manual remediation is required
            return False
        if next_release_issue is not None:
            # ticket exists but release state does not
            log.error(
                "A branch cut ticket already exists for the next branch cut release version "
                "but the release state does not.",
                issue_key=next_release_issue.key,
                next_release_version=next_release_version_string,
            )
            next_release_issue_link = context.jira.get_issue_link(next_release_issue.key)
            context.notifications.append(
                Notification(
                    f"{' '.join(get_branch_cut_on_call_users(context.credentials_store))} A "
                    f"branch cut <{next_release_issue_link}|ticket> already exists for the "
                    f"`{next_release_version_string}` branch cut release but the release state "
                    "does not. Please remediate this.",
                    ERR,
                )
            )
            # fail precondition regardless of self.expect since manual remediation is required
            return False

        log.info("The next branch cut release does not exist already.")
        return not self.expect


class ReadyToRunMongoDBAgentPublishTask(Precondition):
    """
    checks if the MongoDB Agent container publish task
    is ready to be ran on the Cloud Manager version bump pull request
    """

    def __init__(self) -> None:
        super().__init__("Need to run MongoDB Agent container image publish task")

    def check(self, context: StepContext) -> bool:
        """
        Run the publish task when container the image publish task has not run or passed.
        Ticket and PR are created in a separate task only if Ops Manager k8s CM Version Bump PR Is
        Needed
        """
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )

        # find Jira ticket for the Cloud Manager version bump for MongoDB Agent container images
        if not (
            issue := find_maybe_preexisting_issue(
                context.jira,
                BUMP_CONTAINER_IMAGE_MDB_AGENT_CM_ISSUE,
                release_state.internal_version,
            )
        ):
            return False

        # build GitHub client
        org_and_repo = OrgAndRepoKey.MMS
        github_client = context.get_github_client(org_and_repo=org_and_repo)

        # check if pull request already exists
        existing_pull_request = get_open_k8s_cm_version_bump_pr(
            credentials_store=context.credentials_store,
            github_client=github_client,
            issue_key=issue.key,
        )
        if existing_pull_request is None:
            msg = (
                "Open PR to bump Cloud Manager version for MongoDB Agent "
                "container image could not be found."
            )
            log.info(msg, issue_key=issue.key)
            context.notifications.append(Notification(msg))
            return False

        # get the Evergreen patch URL for the pull request
        if not (
            patch_status_url := github_client.get_pull_request_evergreen_patch_url(
                pull_request_url=existing_pull_request.url
            )
        ):
            return True

        # set variants to check
        additional_variants = [RELEASE_MDB_AGENT_VARIANT]
        # build Evergreen client
        evergreen_client = context.get_evergreen_client()
        return evergreen_client.variants_need_to_be_added_to_patch(
            patch_status_url=patch_status_url,
            variants=additional_variants,
        )


def get_releases_in_this_release_stage(
    context: StepContext,
    release_state: BranchCutReleaseState,
    release_stage: ReleaseStage,
) -> list[BranchCutReleaseState]:
    """Checks if any other active branch cut release is in the same release stage"""
    incomplete_releases: list[BranchCutReleaseState] = cursor_to_branch_cut_release_state_list(
        context.atlas.get_incomplete_releases()
    )
    log.debug("Found all incomplete branch cut releases", releases=incomplete_releases)
    same_stage_releases: list[BranchCutReleaseState] = []
    for rel in incomplete_releases:
        if (
            rel.get_release_stage() == release_stage
            and rel.internal_version != release_state.internal_version
        ):
            log.warning(
                "Found another branch cut release in the same release stage",
                issue_key=rel.issue_key,
                version=rel.internal_version,
                release_branch=rel.release_branch,
                current_version=release_state.internal_version,
                current_release_branch=release_state.release_branch,
                release_stage=release_stage,
            )
            same_stage_releases.append(rel)
    return same_stage_releases


class IsOnlyBranchCutReleaseInThisStage(Precondition):
    """checks if the current release is the only PCT branch cut release in this stage"""

    def __init__(self, release_stage: ReleaseStage, expect: bool, dry_run: bool = False) -> None:
        dry_run_str = "Dryrun: " if dry_run else ""
        message_template = (
            f"{dry_run_str}"
            "This is{condition} the only active branch cut release in this release stage "
            f"({release_stage.value})"
        )
        self.pass_message = message_template.format(condition="" if expect else " not")
        self.fail_message = message_template.format(condition=" not" if expect else "")
        super().__init__(self.fail_message)
        self.release_stage = release_stage
        self.dry_run = dry_run
        self.expect = expect

    def check(self, context: StepContext) -> bool:
        """check"""
        release_state = cast(
            BranchCutReleaseState, context.atlas.get_release_state(context.release_ticket.key)
        )
        log.info(
            "Checking if any other branch cut releases are in this release stage...",
            release_branch=release_state.release_branch,
            release_stage=self.release_stage,
        )
        if self.dry_run:
            log.warning(
                f"{DRY_RUN}IsOnlyBranchCutReleaseInThisStage",
                release_state=release_state,
                release_stage=self.release_stage,
                key=context.release_ticket.key,
            )
            return True

        same_stage_releases = get_releases_in_this_release_stage(
            context=context, release_state=release_state, release_stage=self.release_stage
        )
        if same_stage_releases:
            log.info(
                "Found other branch cut releases in this release stage",
                version=release_state.internal_version,
                release_branch=release_state.release_branch,
                release_stage=self.release_stage,
                same_release_stage_versions=[rel.internal_version for rel in same_stage_releases],
                same_release_stage_branches=[rel.release_branch for rel in same_stage_releases],
            )
            actual = False
        else:
            log.info(
                "No other branch cut releases found in this release stage",
                version=release_state.internal_version,
                release_branch=release_state.release_branch,
                release_stage=self.release_stage,
            )
            actual = True

        # check if release is only release state in this stage
        success = actual == self.expect
        message = self.pass_message if success else self.fail_message
        log.info(
            message,
            expect=self.expect,
            actual=actual,
            success=success,
        )
        return success
