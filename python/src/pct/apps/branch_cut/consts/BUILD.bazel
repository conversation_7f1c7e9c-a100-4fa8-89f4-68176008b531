# TEMPLATE START ===============================================================
# THIS FILE IS AUTO-GENERATED AND UPDATED WITH GAZELLE @unused _bin _lib _tst
load(
    "@rules_python//python:defs.bzl",
    _bin = "py_binary",
    _lib = "py_library",
    _tst = "py_test",
)
load("//python:defs.bzl", "py_binary", "py_library", "py_test")

# TEMPLATE END =================================================================
py_library(
    name = "lib",
    srcs = [
        "__init__.py",
        "argo_consts.py",
        "deploy_consts.py",
        "evergreen_consts.py",
        "github_consts.py",
        "release_consts.py",
    ],
    imports = ["../.."],
    tags = ["minimal_test"],
    visibility = ["//python/src:__subpackages__"],
    deps = [
        "//python/src/pct/consts:lib",
        "//python/src/pct/credentials:lib",
        "//python/src/release_tools/cloud:lib",
        "//python/src/slack_utils:lib",
    ],
)

py_binary(name = "bin")

py_test(name = "test")
