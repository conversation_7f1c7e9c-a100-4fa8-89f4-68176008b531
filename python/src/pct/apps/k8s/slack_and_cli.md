# Slack / CLI Commands

Slack / CLI Commands for Managing K8s Operator Releases

## k8s

- Slack

`/pct k8s`

### k8s start-release

Starts a new Enterprise Operator Release

- Slack

`/pct k8s start-release <issue_key>`

### k8s list-release

Sets a release_sha for a given release

- Slack

`/pct k8s set-release-sha <issue_key> <release_sha>`

### k8s status

Prints the status of the release process for issue_key. Most of the state needs to exist in the state database and not in the ticket.

- Slack

`/pct k8s status <issue_key>`

### k8s ok-to-publish

- Slack

`/pct k8s ok-to-publish <issue_key>`

### k8s finalize-release

Sets this release to be ready to be finalized

- Slack

`/pct k8s finalize-release <issue_key>`
