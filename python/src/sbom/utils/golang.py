"""golang utilities"""

import re

from packageurl import PackageURL

from common.logger import get_logger
from sbom.utils.purls import Purls

log = get_logger()

TIMEOUT = 15


class GolangSBOMUtils:
    """utils for golang sbom generation"""

    @staticmethod
    def go_version_text_to_purls(text: str) -> Purls:
        """
        Convert the text output of go version -m <bin> to purls

        Example input text:
            Extracting: automation-agent From: bazel-out/.../automation-agent-....tar.gz
            automation-agent: go1.21.8
                path    command-line-arguments
                dep     cloud.google.com/go/compute/metadata v0.2.3 h1:mg4jlk...J7UnBMY=
                dep     cloud.google.com/go/kms v1.15.5 h1:pj1sRfut2e...UujMIM1vVeM=
                dep     com.tengen/cm   (devel)
                dep     github.com/jessevdk/go-flags    v1.5.0
                =>      github.com/jessevdk/go-flags    v0.0.0-20170...4ae8
                build   CGO_ENABLED=1

        Example output:
            pkg:golang/cloud.google.com/go/compute/metadata@v0.2.3
            pkg:golang/cloud.google.com/go/kms@v1.15.5
            pkg:golang/com.tengen/cm@%28devel%29
        """
        go_dep_regex = re.compile(r"^\s*dep\s+(?P<name>[\S]+)\s+(?P<version>[\S]+)\s?.*")
        all_matches = Purls()
        for line in text.splitlines():
            # Extract the package name and version
            matches = go_dep_regex.match(line)
            if not matches:
                continue
            # Construct the purl
            purl_str = f"pkg:golang/{matches.group('name')}@{matches.group('version')}"
            # use the packageurl library to validate the purl
            purl = PackageURL.from_string(purl_str)
            all_matches.append(purl)

        return all_matches

    @staticmethod
    def go_version_text_to_purl_str(text: str) -> str:
        """
        Convert the text output of go version -m <bin> to purl strings
        """
        return GolangSBOMUtils.go_version_text_to_purls(text).to_str()

    @staticmethod
    def example_go_version_text_str() -> str:
        """Example go version -m <bin> text"""

        return """
Extracting: mongodb-mms-automation-agent From: bazel-out/darwin_arm64-opt-exec-2B5CBBC6/bin/server/get_agent_go_deps.runfiles/_main/server/agent/automation/mongodb-mms-automation-agent-108.0.0.8562-1.linux_x86_64.tar.gz
mongodb-mms-automation-agent: go1.21.8
    path	command-line-arguments
    dep	cloud.google.com/go/compute/metadata	v0.2.3	h1:mg4jlk7mCAj6xXp9UJ4fjI9VUI5rubuGBW5aJ7UnBMY=
    dep	cloud.google.com/go/iam	v1.1.5	h1:1jTsCu4bcsNsE4iiqNT5SHwrDRCfRmIaaaVFhRveTJI=
    dep	cloud.google.com/go/kms	v1.15.5	h1:pj1sRfut2eRbD9pFRjNnPNg/CzJPuQAzUujMIM1vVeM=
    dep	com.tengen/cm	(devel)
    dep	github.com/10gen/cloud-agent-common	v0.0.0-20240119171433-c929dc35c9a5	h1:yXvfx8623Kihi8U5rMjHY062HeJwzrxM8IdEMPLwy9M=
    dep	github.com/10gen/cloud-auth-common	v0.0.0-20240111203129-5735222a66d8	h1:rN0Wibph6VW3GT4RlczlQ0hJljyRsyrxI3ZsqS5j9Sg=
    dep	github.com/Azure/azure-sdk-for-go	v68.0.0+incompatible	h1:fcYLmCpyNYRnvJbPerq7U0hS+6+I79yEDJBqVNcqUzU=
    dep	github.com/Azure/go-autorest/autorest	v0.11.29	h1:I4+HL/JDvErx2LjyzaVxllw2lRDB5/BT2Bm4g20iqYw=
    dep	github.com/Azure/go-autorest/autorest/adal	v0.9.23	h1:Yepx8CvFxwNKpH6ja7RZ+sKX+DWYNldbLiALMC3BTz8=
    dep	github.com/Azure/go-autorest/autorest/date	v0.3.0	h1:7gUk1U5M/CQbp9WoqinNzJar+8KY+LPI6wiWrP/myHw=
    dep	github.com/Azure/go-autorest/autorest/to	v0.4.0	h1:oXVqrxakqqV1UZdSazDOPOLvOIz+XA683u8EctwboHk=
    dep	github.com/Azure/go-autorest/autorest/validation	v0.3.1	h1:AgyqjAd94fwNAoTjl/WQXg4VvFeRFpO+UhNyRXqF1ac=
    dep	github.com/Azure/go-autorest/logger	v0.2.1	h1:IG7i4p/mDa2Ce4TRyAO8IHnVhAVF3RFU+ZtXWSmf4Tg=
"""
