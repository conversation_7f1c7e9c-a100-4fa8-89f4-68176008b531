"""
This workflow handles starting the live import process of the mmsdbmetering
cluster from Commercial Atlas to IA.
"""

from change_management.config import settings
from change_management.core import OutputParam, InputParam
from change_management.core.workflow import Workflow
from change_management.database import connections
from change_management.migration import atlas_api
from common.logger import get_logger

# pylint: disable=missing-function-docstring
# pylint: disable=unused-argument
# pylint: disable=duplicate-code

workflow = Workflow(__name__)
log = get_logger()


@workflow.started(
    msg="Migration workflow of Commercial mmsdbmetering cluster to IA has started",
    next_steps=["verify_dest_ia_cluster_setup"],
)
def workflow_started() -> None:
    log.info("Verifying source database connectivity")
    connections.conn_cloudconf().admin.command("ping")


@workflow.success("mmsdbmetering IA live import has succeeded")
def workflow_success() -> None:
    pass


@workflow.failure("mmsdbmetering IA live import has failed")
def workflow_failure() -> None:
    pass


@workflow.manual(
    msg="Verify that destination IA cluster has been created in relevant environment",
    next_steps=["verify_dest_ia_cluster_whitelisted"],
)
def verify_dest_ia_cluster_setup() -> None:
    pass


@workflow.manual(
    msg="Verify destination cluster IPs is whitelisted in source cluster firewall",
    next_steps=["verify_scram_user_created"],
)
def verify_dest_ia_cluster_whitelisted() -> None:
    pass


@workflow.manual(
    msg="Verify that a SCRAM user to use for migration has been created",
    next_steps=["verify_mmsdbmetering_cutover_mongouri"],
)
def verify_scram_user_created() -> None:
    pass


@workflow.manual(
    msg="Verify that mmsdbmetering.cutover.mongoUri is set in conf-*.properties file",
    next_steps=["disable_meter_usage_bill_date_consistency_auditor"],
)
def verify_mmsdbmetering_cutover_mongouri() -> None:
    pass


@workflow.automated(
    msg="Disable MeterUsageBillDateConsistencyAuditor",
    next_steps=["disable_meter_usage_purge_job"],
    rollback_step="enable_meter_usage_bill_date_consistency_auditor",
)
def disable_meter_usage_bill_date_consistency_auditor() -> None:
    toggle_cron("meterUsageBillDateConsistencyAuditor", False)


@workflow.automated(
    msg="Disable MeterUsagePurgeJob",
    next_steps=["verify_cutover_mongo_client_initialized"],
    rollback_step="enable_meter_usage_purge_job",
)
def disable_meter_usage_purge_job() -> None:
    toggle_cron("meterUsagePurgeJob", False)


@workflow.manual(
    msg="Verify server logs that cutover MongoClient was initialized",
    next_steps=["start_live_migration"],
)
def verify_cutover_mongo_client_initialized() -> None:
    pass


@workflow.automated(
    msg="Start live migration",
    next_steps=["verify_migration_success"],
    rollback_step="stop_live_migration",
    output_parameter=OutputParam("live_import_id", filename="/tmp/metering_live_import_id"),
)
def start_live_migration() -> str:
    response = atlas_api.start_live_import(
        atlas_project_id=settings.METERING_IA_GROUP_ID,
        cluster_name=settings.METERING_CLUSTER_NAME,
        src_hostname=settings.METERING_SOURCE_PRIMARY_HOST,
        src_username=settings.METERING_SOURCE_USERNAME,
        src_password=settings.METERING_SOURCE_PASSWORD,
        atlas_api_public_key=settings.METERING_IA_API_PUBLIC_KEY,
        atlas_api_private_key=settings.METERING_IA_API_PRIVATE_KEY,
        payload_extension={"verificationEnabled": False},
    )
    if response.status_code != 202:
        # pylint: disable=broad-exception-raised
        raise Exception(f"Failed to start live import: {response.text}")
    live_import_id = response.text.replace('"', "")
    log.info(f"Live import started with ID: {live_import_id}")
    return live_import_id


@workflow.manual(
    msg="Verify that live import has completed with no errors", next_steps=["workflow_success"]
)
def verify_migration_success() -> None:
    pass


@workflow.rollback("Enable MeterUsageBillDateConsistencyAuditor")
def enable_meter_usage_bill_date_consistency_auditor() -> None:
    toggle_cron("meterUsageBillDateConsistencyAuditor", True)


@workflow.rollback("Enable MeterUsagePurgeJob")
def enable_meter_usage_purge_job() -> None:
    toggle_cron("meterUsagePurgeJob", True)


@workflow.rollback(
    "Stop live migration",
    input_parameters=[InputParam("live_import_id", from_step="start_live_migration")],
)
def stop_live_migration(live_import_id: str) -> None:
    log.info(f"Stopping live import id: {live_import_id}")
    response = atlas_api.stop_live_import(
        atlas_project_id=settings.METERING_IA_GROUP_ID,
        live_import_id=live_import_id,
        atlas_api_public_key=settings.METERING_IA_API_PUBLIC_KEY,
        atlas_api_private_key=settings.METERING_IA_API_PRIVATE_KEY,
    )
    if response.status_code == 202:
        log.info("Live import stopped successfully")
    else:
        log.info(f"Failed to stop live import: {response.text}")
        log.info("Please manually cancel the live import through the UI")


def toggle_cron(cronjob: str, enabled: bool) -> None:
    log.info(f"Toggling cron {cronjob} to enabled: {enabled}")
    connections.conn_cloudconf().cloudconf.app.systemCronState.update_one(
        {"_id": cronjob}, {"$set": {"manuallyDisabled": not enabled}}
    )
    log.info(f"Successfully toggled cron {cronjob} to enabled: {enabled}")
