syntax = "proto3";

package com.xgen.pricing.skupriceapi.v1;

import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";

// PricingInfoRequest holds the parameters for the fetchSkuPricing rpc method.
message PricingInfoRequest {
  repeated string skus = 1 [(buf.validate.field).repeated.min_items = 1];
  google.protobuf.Timestamp usageDate = 2 [(buf.validate.field).required = true];
}

message PricingInfoResponse {
  map<string, SkuPricing> pricing = 1;
}
message TieredPrices {
  repeated double prices = 1;
}

message RegionUnitPrices {
  map<string, double> pricing = 1;
}

message RegionTieredUnitPrices {
  map<string, TieredPrices> pricing = 1;
}

message GcpNetworkPricing {
  string source_continent = 1;
  string dest_continent = 2;
  double unit_price_dollars = 3;
}

message SkuPricing {
  optional DatePeriod applicability_period = 1;
  optional double float_price_dollars = 2;
  optional double unit_price_dollars = 3;
  optional double unit_price_gov_dollars = 4;
  optional TieredPrices tiered_unit_prices_dollars = 5;
  repeated double tiered_pricing_quantities = 6;
  optional double tiered_pricing_quantity_hard_cap = 7;
  map<string, RegionUnitPrices> region_unit_price_dollars = 8;
  map<string, RegionTieredUnitPrices> tiered_region_unit_price_dollars = 9;
  repeated GcpNetworkPricing gcp_network_source_dest_unit_price_dollars = 10;
  map<string, double> gcp_network_continent_unit_price_dollars = 11;
  optional double free_tier_quantity = 12;
  optional double advanced_security_upcharge = 13;
  optional double enterprise_auditing_upcharge = 14;
  optional double bi_connector_high = 15;
  optional double bi_connector_low = 16;
}

message DatePeriod {
  google.protobuf.Timestamp start_timestamp = 1;
  google.protobuf.Timestamp end_timestamp = 2;
}
