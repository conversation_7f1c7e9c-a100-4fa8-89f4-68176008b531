---
image:
  repository: "664315256653.dkr.ecr.us-east-1.amazonaws.com/mms/pricing-skupriceapi"

env:
  TEMPORAL_WORKER_ENABLED: false

externalSecrets:

additionalLabels:
  app: "pricing-skupriceapi"

replicaCount: 2

resources:
  limits:
    cpu: "2"
    memory: 2Gi
  requests:
    cpu: "2"
    memory: 2Gi

{{if not .isGov}}
tracing:
  enabled: true
{{end}}

consumers:
  core-systems-{{ .environment }}:
    - mms-cron-jobs
    - mms-jobs-common
    - mms-jobs-common-failover
    - mms-jobs-alerts
    - mms-jobs-billing-usage
    - mms-jobs-daily-billing
    - mms-jobs-cloud-provider-billing
    - mms-jobs-search
  mms-{{ .environment }}:
    - mms-agent
    - mms-agent-canary
    - mms-agent-logs
    - mms-agent-metrics
    - mms-api-meshonly
    - mms-ui
    - mms-ui-admin
  vm-{{ .environment }}:
    - mms
  pricing-{{ .environment }}:
    - "*"
  {{if .isGov}}
    - javatools
  {{else}}
  javatools-{{ .environment }}:
    - javatools
  {{end}}
