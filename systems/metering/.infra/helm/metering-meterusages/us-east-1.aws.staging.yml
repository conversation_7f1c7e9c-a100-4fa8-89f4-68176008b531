---
# Source: mergevalues/templates/merged-values.yaml
# This file is automatically generated. Do not modify this file directly, as your changes will be
# overwritten. Instead, update the systems/{system}/{component}/values.helix.tmpl template and run
# bazel run '//systems/{system}:generate_infra'
additionalLabels:
  app: metering-meterusages
  instance_id: "0"
authnServiceEnvoyFilter:
  authnServiceNamespace: core-systems-staging
  enabled: true
  ports:
  - 8080
  - 8081
  rules:
  - match:
      safe_regex:
        google_re2: {}
        regex: ^/grpc.reflection.v1alpha.ServerReflection/ServerReflectionInfo
  - match:
      prefix: /
    requires:
      provider_name: x-xgen-service-auth
consumers:
  core-systems-staging:
  - mms-cron-jobs
  - mms-jobs-common
  - mms-jobs-common-failover
  - mms-jobs-alerts
  - mms-jobs-billing-usage
  - mms-jobs-daily-billing
  - mms-jobs-cloud-provider-billing
  - mms-jobs-atlas
  - mms-jobs-atlas-prioritized
  - mms-jobs-search
  javatools-staging:
  - javatools
  mms-staging:
  - mms-agent
  - mms-agent-canary
  - mms-agent-logs
  - mms-agent-metrics
  - mms-api-meshonly
  - mms-ui
  - mms-ui-admin
  - mms-api-public
  - mms-metering
  vm-staging:
  - mms
  - mms-agent
defaultSecurityContextEnabled: true
env:
  AZURE_WEB_IDENTITY_TOKEN_TENANT_ID: 5537e8ff-e2ba-475a-a67a-daa445542246
  GRPC_LISTEN_ADDRESS: 0.0.0.0:8080
  HEAPDUMP_PATH: /tmp/oomheapdump/
  HTTP_LISTEN_ADDRESS: 0.0.0.0:8081
  INNIT_HEAPDUMPS_BUCKET_PREFIX: javatools-files-staging
  JAVATOOLSSC: "1"
  METRICS_LISTEN_ADDRESS: 0.0.0.0:9090
  OTEL_INSTRUMENTATION_MONGO_ENABLED: false
  OTEL_JAVA_DISABLED_RESOURCE_PROVIDERS: io.opentelemetry.instrumentation.resources.ProcessResourceProvider
  OTEL_JAVAAGENT_ENABLED: true
  OTEL_SDK_DISABLED: false
  OTEL_SERVICE_NAME: metering-meterusages
  RESERVED_CODE_CACHE_SIZE: 192m
  SECRETS_DIR: /run/secrets/10gen.cc/external-secrets/mms
  UPDATE_CA: "false"
  WEB_SERVER_MAX_THREADS: "50"
  WEB_SERVER_MIN_THREADS: "50"
  XHOSTS_LO: "false"
  XMS: 4096m
  XMX: 4096m
  XSS: 228k
headlessService:
  enable: true
image:
  repository: 664315256653.dkr.ecr.us-east-1.amazonaws.com/mms/metering-meterusages
istio:
  activeRegion: us-east-1
  podAnnotations:
    sidecar.istio.io/proxyCPU: 200m
    traffic.sidecar.istio.io/excludeInboundPorts: "10000"
    traffic.sidecar.istio.io/excludeOutboundPorts: 443,6379,26101,26300,27016,27017,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124
listeners:
  default:
    appProtocol: grpc
    port:
      containerPort: 8080
      protocol: TCP
  http:
    appProtocol: http
    port:
      containerPort: 8081
      name: http
      protocol: TCP
  javatools:
    appProtocol: grpc
    istio:
      disableServiceEntry: true
    port:
      containerPort: 8887
      name: javatools
      protocol: TCP
livenessProbe: null
metricListeners:
  jmx-metrics:
    podMonitor:
      podMetricsEndpoints:
      - path: /metrics
        scheme: http
      podTargetLabels:
      - app
      - environment
      - instance_id
    port:
      containerPort: 10000
      name: jmx-port
  metrics:
    podMonitor:
      podMetricsEndpoints:
      - path: /metrics
        scheme: http
      podTargetLabels:
      - app
      - environment
      - instance_id
    port:
      containerPort: 9090
nodeSelector:
  Billing: cloud
podAnnotations:
  xgen/javatools: "true"
readinessProbe:
  httpGet:
    path: /health
    port: 8882
  periodSeconds: 10
  tcpSocket: null
replicaCount: 2
resources:
  limits:
    cpu: "2"
    memory: 2Gi
  requests:
    cpu: "2"
    memory: 2Gi
securityContextOverride:
  securityContext:
    allowPrivilegeEscalation: false
    privileged: false
serviceAccount:
  tokens:
    aws:
      audience: sts.amazonaws.com
      expirationSeconds: 600
    azure:
      audience: api://AzureADTokenExchange
      expirationSeconds: 600
splunk:
  index: mms-staging
sre_owner: sre-racha
startupProbe:
  failureThreshold: 30
  httpGet:
    path: /health
    port: 8882
  initialDelaySeconds: 10
  periodSeconds: 10
  tcpSocket: null
terminationGracePeriodSeconds: 600
tmpVolumeEnabled: true
tracing:
  enabled: true
