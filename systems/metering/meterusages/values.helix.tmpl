---
image:
  repository: "664315256653.dkr.ecr.us-east-1.amazonaws.com/mms/metering-meterusages"

env:
  OTEL_SERVICE_NAME: metering-meterusages
{{if and .isDev (not .isGov) }}
  OTEL_DEBUG: true
  OTEL_TRACES_SAMPLER: always_on
  OTEL_PROPAGATORS: none
  OTEL_INSTRUMENTATION_MONGO_ENABLED: true
  OTEL_INSTRUMENTATION_AWS_SDK_ENABLED: true
{{end}}

additionalLabels:
  app: "metering-meterusages"

replicaCount: 2

resources:
  limits:
    cpu: "2"
    memory: 2Gi
  requests:
    cpu: "2"
    memory: 2Gi

{{if not .isGov}}
tracing:
  enabled: true
{{end}}

consumers:
  core-systems-{{ .environment }}:
    - mms-cron-jobs
    - mms-jobs-common
    - mms-jobs-common-failover
    - mms-jobs-alerts
    - mms-jobs-billing-usage
    - mms-jobs-daily-billing
    - mms-jobs-cloud-provider-billing
    - mms-jobs-atlas
    - mms-jobs-atlas-prioritized
    - mms-jobs-search
  mms-{{ .environment }}:
    - mms-agent
    - mms-agent-canary
    - mms-agent-logs
    - mms-agent-metrics
    - mms-api-meshonly
    - mms-ui
    - mms-ui-admin
    - mms-api-public
    - mms-metering
  vm-{{ .environment }}:
    - mms
    - mms-agent
  {{if .isGov}}
    - javatools
  {{else}}
  javatools-{{ .environment }}:
    - javatools
  {{end}}
