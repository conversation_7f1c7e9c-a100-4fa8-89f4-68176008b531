package com.xgen.metering.meterusages.model;

import com.google.common.collect.ImmutableSet;
import jakarta.annotation.Nullable;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.EnumSet;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Central enum to define and configure all meter ids. Each meter id has a corresponding {@link
 * com.xgen.cloud.billingplatform.model.sku._public.model.SKU SKU} mapped within the SKU enum
 * definition.
 */
public enum MeterId {

  // AWS report SKUs
  NDS_AWS_DATA_TRANSFER_SAME_REGION(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_DATA_TRANSFER_INTERNET(UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_PRIVATE_ENDPOINT(UsageUnits.HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_PRIVATE_ENDPOINT_CAPACITY_UNITS(
      UsageUnits.CU_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_BACKUP_SNAPSHOT_STORAGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_PIT_RESTORE_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_PIT_RESTORE_STORAGE_TEST_PRICE_CHANGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_1(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_2(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_3(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_4(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_5(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_6(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_7(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),

  // Azure report SKUs
  NDS_AZURE_DATA_TRANSFER(UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_DATA_TRANSFER_REGIONAL_VNET_IN(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_DATA_TRANSFER_REGIONAL_VNET_OUT(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_DATA_TRANSFER_GLOBAL_VNET_IN(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_DATA_TRANSFER_GLOBAL_VNET_OUT(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_DATA_TRANSFER_AVAILABILITY_ZONE_IN(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_DATA_TRANSFER_AVAILABILITY_ZONE_OUT(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTER_CONTINENT(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTRA_CONTINENT(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_PRIVATE_ENDPOINT_CAPACITY_UNITS(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_SNAPSHOT_STORAGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_PIT_RESTORE_STORAGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  // CMK Over Private Networking
  NDS_AZURE_CMK_PRIVATE_NETWORKING(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AWS_CMK_PRIVATE_NETWORKING(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  // Serverless
  NDS_AWS_SERVERLESS_RPU(UsageUnits.MILLION_RPU, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SERVERLESS_WPU(UsageUnits.MILLION_WPU, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SERVERLESS_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SERVERLESS_CONTINUOUS_BACKUP(
      UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  // TODO: Remove data transfer preview SKUs:  https://jira.mongodb.org/browse/CLOUDP-121885
  NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SERVERLESS_DATA_TRANSFER(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  NDS_GCP_SERVERLESS_RPU(UsageUnits.MILLION_RPU, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SERVERLESS_WPU(UsageUnits.MILLION_WPU, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SERVERLESS_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SERVERLESS_CONTINUOUS_BACKUP(
      UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  // TODO: Remove data transfer preview SKUs:  https://jira.mongodb.org/browse/CLOUDP-121885
  NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SERVERLESS_DATA_TRANSFER(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_AZURE_SERVERLESS_RPU(UsageUnits.MILLION_RPU, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SERVERLESS_WPU(UsageUnits.MILLION_WPU, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SERVERLESS_STORAGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SERVERLESS_CONTINUOUS_BACKUP(
      UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  // TODO: Remove data transfer preview SKUs:  https://jira.mongodb.org/browse/CLOUDP-121885
  NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SERVERLESS_DATA_TRANSFER(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  // Realm
  REALM_APP_REQUESTS(UsageUnits.REQUESTS, Frequency.DAILY, Collections.emptySet()),
  REALM_APP_COMPUTE(UsageUnits.HOURS, Frequency.DAILY, Collections.emptySet()),
  REALM_APP_SYNC(UsageUnits.MILLION_MINUTES, Frequency.DAILY, Collections.emptySet()),
  REALM_APP_DATA_TRANSFER(UsageUnits.GIGABYTES, Frequency.DAILY, Collections.emptySet()),
  BAAS_APP_COMPUTE(UsageUnits.HOURS, Frequency.DAILY, Collections.emptySet()),
  BAAS_APP_DATA_TRANSFER(UsageUnits.GIGABYTES, Frequency.DAILY, Collections.emptySet()),
  BAAS_APP_REQUESTS(UsageUnits.REQUESTS, Frequency.DAILY, Collections.emptySet()),
  BAAS_APP_SYNC(UsageUnits.MILLION_MINUTES, Frequency.DAILY, Collections.emptySet()),
  CHARTS_DATA_DOWNLOADED(UsageUnits.GIGABYTES, Frequency.DAILY, Collections.emptySet()),

  // Atlas Backup Snapshot to S3
  NDS_AWS_SNAPSHOT_EXPORT_UPLOAD(UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),

  // Atlas Backup Snapshot to Azure Blob Storage
  NDS_AZURE_SNAPSHOT_EXPORT_UPLOAD(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  NDS_GCP_SNAPSHOT_EXPORT_UPLOAD(UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.GCP)),

  // GCP Private Service Connect
  NDS_GCP_PRIVATE_ENDPOINT(UsageUnits.HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_PRIVATE_ENDPOINT_CAPACITY_UNITS(
      UsageUnits.CU, Frequency.DAILY, Set.of(CloudProvider.GCP)),

  // GCP Data Transfer
  NDS_GCP_DATA_TRANSFER_INTERNET(UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_DATA_TRANSFER_INTER_CONNECT(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_DATA_TRANSFER_INTER_ZONE(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_DATA_TRANSFER_INTER_REGION(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_DATA_TRANSFER_GOOGLE(UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  GCP_SNAPSHOT_COPY_DATA_TRANSFER(UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.GCP)),

  // GCP snapshot and PIT
  NDS_GCP_BACKUP_SNAPSHOT_STORAGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_PIT_RESTORE_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.GCP)),

  // Data Lake types
  DATA_LAKE_AWS_DATA_SCANNED(UsageUnits.TERABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  DATA_LAKE_AWS_DATA_RETURNED_SAME_REGION(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  DATA_LAKE_AWS_DATA_RETURNED_DIFFERENT_REGION(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  DATA_LAKE_AWS_DATA_RETURNED_INTERNET(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  DATA_LAKE_AWS_DATA_TRANSFERRED_FROM_DIFFERENT_REGION(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  NDS_AWS_DATA_LAKE_STORAGE_ACCESS(
      UsageUnits.THOUSAND_REQUESTS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_DATA_LAKE_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  DATA_FEDERATION_AZURE_DATA_SCANNED(
      UsageUnits.TERABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_REGION(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  DATA_FEDERATION_AZURE_DATA_RETURNED_INTERNET(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_CONTINENT(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  DATA_FEDERATION_AZURE_DATA_RETURNED_DIFFERENT_CONTINENT(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  NDS_AZURE_DATA_LAKE_STORAGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_DATA_LAKE_STORAGE_ACCESS(
      UsageUnits.THOUSAND_REQUESTS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  DATA_FEDERATION_GCP_DATA_SCANNED(
      UsageUnits.TERABYTES, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  DATA_FEDERATION_GCP_DATA_RETURNED_SAME_REGION(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  DATA_FEDERATION_GCP_DATA_RETURNED_DIFFERENT_REGION(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  DATA_FEDERATION_GCP_DATA_RETURNED_INTERNET(
      UsageUnits.GIGABYTES, Frequency.HOURLY, Set.of(CloudProvider.GCP)),

  NDS_GCP_DATA_LAKE_STORAGE_ACCESS(
      UsageUnits.THOUSAND_REQUESTS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_OBJECT_STORAGE_ACCESS(
      UsageUnits.THOUSAND_REQUESTS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),

  NDS_GCP_DATA_LAKE_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_OBJECT_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_COMPRESSED_OBJECT_STORAGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.GCP)),

  // Online Archive
  NDS_AWS_OBJECT_STORAGE_ACCESS(
      UsageUnits.THOUSAND_REQUESTS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_OBJECT_STORAGE_SEEK(
      UsageUnits.THOUSAND_REQUESTS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_OBJECT_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_OBJECT_STORAGE_V3(UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_COMPRESSED_OBJECT_STORAGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.AWS)),

  NDS_AZURE_OBJECT_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_OBJECT_STORAGE_ACCESS(
      UsageUnits.THOUSAND_REQUESTS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_COMPRESSED_OBJECT_STORAGE(
      UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  // Test
  TEST_METER_ID_WITH_NO_DIMENSIONS_HOURLY(
      UsageUnits.UNITS, Frequency.HOURLY, Collections.emptySet()),
  TEST_METER_ID_WITH_NO_DIMENSIONS_DAILY(UsageUnits.UNITS, Frequency.DAILY, Collections.emptySet()),
  METERING_E2E_BILLING(UsageUnits.SERVER_HOURS, Frequency.DAILY, Collections.emptySet()),
  DATA_LAKE_AWS_DATA_SCANNED_TEST_PRICE_CHANGE(
      UsageUnits.TERABYTES, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  // GCP Snapshot Distribution
  GCP_SNAPSHOT_COPY_DISK(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),

  // Backup Cloud storage
  MMS_BACKUP_STORAGE(UsageUnits.GIGABYTE_DAYS, Frequency.DAILY, EnumSet.allOf(CloudProvider.class)),

  // Dedicated Search Hardware
  NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S30_COMPUTE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S40_COMPUTE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S50_COMPUTE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S60_COMPUTE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S70_COMPUTE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S80_COMPUTE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S40_STORAGE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S50_STORAGE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S60_STORAGE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S80_STORAGE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S90_STORAGE_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  // Search Azure
  NDS_AZURE_SEARCH_INSTANCE_S20_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S30_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S40_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S50_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S60_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S70_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S80_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  // Low cpu
  NDS_AZURE_SEARCH_INSTANCE_S40_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S50_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S60_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S80_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S90_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S100_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S110_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S130_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SEARCH_INSTANCE_S135_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  // Search GCP
  NDS_GCP_SEARCH_INSTANCE_S20_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S30_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S40_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S50_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S60_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S70_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S80_COMPUTE_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),

  // Low-CPU
  NDS_GCP_SEARCH_INSTANCE_S30_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S40_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S50_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S60_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S70_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S80_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S90_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S100_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S110_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S120_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S130_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SEARCH_INSTANCE_S140_MEMORY_LOCALSSD(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),

  // Cloud Manager
  CLOUD_MANAGER_INSTANCE(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Collections.emptySet()),

  NDS_AWS_SEARCH_INSTANCE_S30_MEMORY_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S40_MEMORY_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S50_MEMORY_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S60_MEMORY_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S80_MEMORY_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SEARCH_INSTANCE_S90_MEMORY_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  NDS_AWS_SEARCH_INSTANCE_S100_MEMORY_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  NDS_AWS_SEARCH_INSTANCE_S110_MEMORY_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  // Atlas Free Instance
  NDS_FREE_INSTANCE_M0(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, EnumSet.allOf(CloudProvider.class)),
  NDS_FREE_INSTANCE_M2(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, EnumSet.allOf(CloudProvider.class)),
  NDS_FREE_INSTANCE_M5(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, EnumSet.allOf(CloudProvider.class)),

  // Atlas AWS Instance
  NDS_AWS_INSTANCE_M10(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M20(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M30(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M40(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M50(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M60(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M80(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M100(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M140(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M200(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M300(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R40(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R50(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R60(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R80(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R200(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R300(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R400(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R700(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M40_NVME(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M50_NVME(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M60_NVME(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M80_NVME(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M200_NVME(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M400_NVME(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M10_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M20_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M30_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M40_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M50_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M60_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M80_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M100_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M140_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M200_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_M300_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R40_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R50_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R60_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R80_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R200_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R300_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R400_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R700_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_INSTANCE_R40_TEST_PRICE_CHANGE(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  // Atlas AWS Storage
  NDS_AWS_STORAGE_PROVISIONED(
      UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_STORAGE_STANDARD(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  NDS_AWS_STORAGE_IOPS(UsageUnits.IOPS_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  // Atlas GCP Instance
  NDS_GCP_INSTANCE_M10(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M20(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M30(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M40(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M50(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M60(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M80(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M140(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M200(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M250(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M300(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M400(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R40(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R50(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R60(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R80(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R200(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R300(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R400(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R600(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M10_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M20_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M30_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M40_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M50_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M60_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M80_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M140_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M200_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M250_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M300_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_M400_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R40_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R50_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R60_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R80_PAUSED(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R200_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R300_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R400_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_INSTANCE_R600_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),

  // Atlas GCP Storage
  NDS_GCP_STORAGE_SSD(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  NDS_GCP_STORAGE_SSD_TEST_PRICE_CHANGE(
      UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),

  // Atlas Azure Instance
  NDS_AZURE_INSTANCE_M10(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M20(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M30(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M30_TEST_PRICE_CHANGE(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M40(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M50(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M60(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M80(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M90(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M200(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R40(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R50(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R60(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R80(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R200(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R300(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R400(UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M60_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M80_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M200_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M300_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M400_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M600_NVME(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M10_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M20_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M30_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M40_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M50_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M60_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M80_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M90_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_M200_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R40_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R50_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R60_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R80_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R200_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R300_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_INSTANCE_R400_PAUSED(
      UsageUnits.SERVER_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  // Atlas Azure Storage
  NDS_AZURE_STORAGE_P2(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_STORAGE_P3(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_STORAGE_P4(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_STORAGE_P6(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_STORAGE_P10(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_STORAGE_P15(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_STORAGE_P20(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_STORAGE_P30(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_STORAGE_P40(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_STORAGE_P50(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  NDS_AZURE_STORAGE_V2(UsageUnits.GIGABYTE_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  NDS_AZURE_STORAGE_IOPS(UsageUnits.IOPS_HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  // AWS snapshot download and export
  NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE_IOPS(
      UsageUnits.IOPS_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SNAPSHOT_EXPORT_VM(UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SNAPSHOT_EXPORT_VM_M40(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SNAPSHOT_EXPORT_VM_M50(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SNAPSHOT_EXPORT_VM_M60(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_BACKUP_DOWNLOAD_VM_STORAGE(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_BACKUP_DOWNLOAD_VM_STORAGE_IOPS(
      UsageUnits.IOPS_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_AWS_BACKUP_DOWNLOAD_VM(UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),

  // Azure snapshot download and export
  NDS_AZURE_SNAPSHOT_EXPORT_VM(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_M40(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_M50(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_M60(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_EXTENDED_IOPS(
      UsageUnits.IOPS_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P2(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P3(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P4(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P6(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P10(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P15(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P20(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P30(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P40(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_P50(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  NDS_AZURE_BACKUP_DOWNLOAD_VM(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_EXTENDED_IOPS(
      UsageUnits.IOPS_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P2(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P3(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P4(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P6(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P10(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P15(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P20(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P30(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P40(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_P50(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),
  NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  // GCP snapshot download and export
  NDS_GCP_SNAPSHOT_EXPORT_VM(UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SNAPSHOT_EXPORT_VM_M40(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SNAPSHOT_EXPORT_VM_M50(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SNAPSHOT_EXPORT_VM_M60(
      UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_SNAPSHOT_EXPORT_VM_STORAGE(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_BACKUP_DOWNLOAD_VM_STORAGE(
      UsageUnits.GIGABYTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  NDS_GCP_BACKUP_DOWNLOAD_VM(UsageUnits.SERVER_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),

  // Premium SKUs
  NDS_ADVANCED_SECURITY(UsageUnits.HOURS, Frequency.DAILY, EnumSet.allOf(CloudProvider.class)),
  NDS_ENTERPRISE_AUDITING(UsageUnits.HOURS, Frequency.DAILY, EnumSet.allOf(CloudProvider.class)),
  NDS_BI_CONNECTOR(UsageUnits.DAYS, Frequency.DAILY, EnumSet.allOf(CloudProvider.class)),

  NDS_AWS_STREAM_PROCESSING_DATA_TRANSFER(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AWS)),

  NDS_AZURE_STREAM_PROCESSING_DATA_TRANSFER(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  NDS_GCP_STREAM_PROCESSING_DATA_TRANSFER(
      UsageUnits.GIGABYTES, Frequency.DAILY, Set.of(CloudProvider.GCP)),

  NDS_AWS_STREAM_PROCESSING_INSTANCE_SP10(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),

  NDS_AWS_STREAM_PROCESSING_INSTANCE_SP30(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),

  NDS_AWS_STREAM_PROCESSING_INSTANCE_SP50(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),

  NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP10(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP30(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP50(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  NDS_AWS_STREAM_PROCESSING_VPC_PEERING(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),

  NDS_AZURE_STREAM_PROCESSING_PRIVATELINK(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AZURE)),

  NDS_AWS_STREAM_PROCESSING_PRIVATELINK(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.AWS)),
  NDS_GCP_STREAM_PROCESSING_PRIVATELINK(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),
  FLEX_AWS_100_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  FLEX_AWS_200_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  FLEX_AWS_300_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  FLEX_AWS_400_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  FLEX_AWS_500_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  FLEX_AZURE_100_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  FLEX_AZURE_200_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  FLEX_AZURE_300_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  FLEX_AZURE_400_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  FLEX_AZURE_500_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  FLEX_GCP_100_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  FLEX_GCP_200_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  FLEX_GCP_300_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  FLEX_GCP_400_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  FLEX_GCP_500_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),

  FLEX_AWS_LEGACY_100_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  FLEX_AWS_LEGACY_200_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  FLEX_AWS_LEGACY_300_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  FLEX_AWS_LEGACY_400_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),
  FLEX_AWS_LEGACY_500_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AWS)),

  FLEX_AZURE_LEGACY_100_USAGE_HOURS(
      UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  FLEX_AZURE_LEGACY_200_USAGE_HOURS(
      UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  FLEX_AZURE_LEGACY_300_USAGE_HOURS(
      UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  FLEX_AZURE_LEGACY_400_USAGE_HOURS(
      UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),
  FLEX_AZURE_LEGACY_500_USAGE_HOURS(
      UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.AZURE)),

  FLEX_GCP_LEGACY_100_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  FLEX_GCP_LEGACY_200_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  FLEX_GCP_LEGACY_300_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  FLEX_GCP_LEGACY_400_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),
  FLEX_GCP_LEGACY_500_USAGE_HOURS(UsageUnits.HOURS, Frequency.HOURLY, Set.of(CloudProvider.GCP)),

  NDS_GCP_STREAM_PROCESSING_INSTANCE_SP10(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),

  NDS_GCP_STREAM_PROCESSING_INSTANCE_SP30(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP)),

  NDS_GCP_STREAM_PROCESSING_INSTANCE_SP50(
      UsageUnits.COMPUTE_HOURS, Frequency.DAILY, Set.of(CloudProvider.GCP));

  public static final Map<String, MeterId> NAME_TO_ENUM =
      Stream.of(MeterId.values()).collect(Collectors.toMap(MeterId::name, Function.identity()));

  /**
   * We do not allow daily usage being reported for the same day, e.g. submission of the
   * MeterUsage{startDate: "2022-07-01", endDate: "2022-07-02"} if submitted on 7/1 will result in
   * {@link
   * com.xgen.metering.meterusages.model.exception.MeterErrorCode#USAGE_REPORTED_TIME_BEFORE_END_TIME}
   * error response because (07/02) is after (07/01). However, in some cases we allow same-day
   * submissions, for example AWS imported SKUs that are being imported by a cron job from AWS and
   * submitted to the metering service right away. AWS imported date includes same-day usages ( so a
   * raw usage with a startDate "2022-07-01" might have a usageDate set to "2022-07-01" and we
   * submit it to the metering service right after the import.
   */
  public static final Set<MeterId> ALLOW_SAME_DAY_USAGE_REPORTED =
      Set.of(
          NDS_AWS_DATA_TRANSFER_SAME_REGION,
          NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION,
          NDS_AWS_DATA_TRANSFER_INTERNET,
          NDS_AWS_PRIVATE_ENDPOINT,
          NDS_AWS_PRIVATE_ENDPOINT_CAPACITY_UNITS,
          NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
          NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_1,
          NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_2,
          NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_3,
          NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_4,
          NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_5,
          NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_6,
          NDS_AWS_TIME_BASED_SNAPSHOT_COPY_LEVEL_7);

  private final UsageUnits usageUnits;
  private final Frequency frequency;
  private final ImmutableSet<CloudProvider> cloudProviders;

  MeterId(UsageUnits usageUnits, Frequency frequency, Set<CloudProvider> cloudProviders) {
    this.usageUnits = usageUnits;
    this.frequency = frequency;
    this.cloudProviders = ImmutableSet.copyOf(cloudProviders);
  }

  public Frequency getFrequency() {
    return frequency;
  }

  public UsageUnits getUsageUnits() {
    return usageUnits;
  }

  public ImmutableSet<CloudProvider> getCloudProviders() {
    return cloudProviders;
  }

  @Nullable
  public static MeterId getByName(String meterIdName) {
    return NAME_TO_ENUM.get(meterIdName);
  }

  /**
   * Frequency with which the usage is reported to the metering service. This is enforced by a
   * server - if it's set to daily, you cannot report hourly usage, this will result in {@link
   * com.xgen.module.metering.common.exception.MeterSvcException}.
   */
  public enum Frequency {
    DAILY {
      /** Returns true if the date is on the exact day. */
      @Override
      public boolean isValid(ZonedDateTime zonedDateTime) {
        return zonedDateTime
            .toInstant()
            .equals(zonedDateTime.toInstant().truncatedTo(ChronoUnit.DAYS));
      }
    },
    HOURLY {
      /** Returns true if the date is on the exact hour. */
      @Override
      public boolean isValid(ZonedDateTime zonedDateTime) {
        return zonedDateTime
            .toInstant()
            .equals(zonedDateTime.toInstant().truncatedTo(ChronoUnit.HOURS));
      }
    };

    public abstract boolean isValid(ZonedDateTime zonedDateTime);
  }
}
