load("@com_google_protobuf//bazel:py_proto_library.bzl", "py_proto_library")
load("//systems/common/bazel:defs.bzl", "write_generated_files")
load("//systems/common/helix:defs.bzl", "helix_system")

# gazelle:resolve java com.xgen.events.api //systems/events/api/src/main/java/com/xgen/events/api
# gazelle:resolve java com.xgen.events.external //systems/events/external/src/main/java/com/xgen/events/external
# gazelle:resolve java com.xgen.events.external.testlibs //systems/events/external/src/it/java/com/xgen/events/external/testlibs
# gazelle:resolve java com.xgen.events.internal.testlibs //systems/events/internal/src/it/java/com/xgen/events/internal/testlibs
# gazelle:resolve_regexp java org.awaitility.* @maven_events//:org_awaitility_awaitility

write_generated_files(
    name = "generate_infra",
    out = ".infra",
    visibility = ["//systems:__pkg__"],
    warning_filename = "GENERATED.txt",
    deps = [
        ":events_system",
    ],
)

helix_system(
    name = "events_system",
    okta_owners = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    owner = "10gen-helix-events-prod-admin",
    path_prefix = "systems/events/.infra",
    slack_channel = "cloud",
    system_name = "events",
    version_object_prefix = "event-service",
    deps = [
        "//systems/events/api:events-api",
        "//systems/events/event-service-db",
        "//systems/events/resources:event-service-resources",
    ],
)

alias(
    name = "api_proto_v0",
    actual = "//systems/events/proto/com/xgen/cloud/services/event:com_xgen_cloud_services_event_proto",
    visibility = ["//visibility:public"],
)

alias(
    name = "common_proto_v0",
    actual = "//systems/events/proto/com/xgen/cloud/services/common:com_xgen_cloud_services_common_proto",
    visibility = ["//visibility:public"],
)

alias(
    name = "event_schemas_proto",
    actual = "//systems/events/proto/com/xgen/events/schemas:event_schemas_proto",
    visibility = ["//visibility:public"],
)

alias(
    name = "event_schemas_java_proto",
    actual = "//systems/events/proto/com/xgen/events/schemas:event_schemas_java_proto",
    visibility = ["//visibility:public"],
)

alias(
    name = "java_api_grpc_v0",
    actual = "//systems/events/proto/com/xgen/cloud/services/event:com_xgen_cloud_services_event_java_library",
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "python_api_grpc_v0",
    visibility = ["//visibility:public"],
    deps = [
        ":api_proto_v0",
        ":common_proto_v0",
    ],
)
