package com.xgen.events.internal.dao;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.elemMatch;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.nin;
import static com.mongodb.client.model.Filters.nor;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.descending;

import com.google.errorprone.annotations.Var;
import com.mongodb.WriteConcern;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.BulkWriteOptions;
import com.mongodb.client.model.InsertOneModel;
import com.xgen.cloud.services.event.proto.EventSource;
import com.xgen.common.mongo.MongoCollections;
import com.xgen.events.internal.model.AccountUserSourceInfo;
import com.xgen.events.internal.model.AlertConfigSourceInfo;
import com.xgen.events.internal.model.Event;
import com.xgen.events.internal.model.Event.FieldDefs;
import com.xgen.events.internal.model.EventOtelMetrics;
import com.xgen.events.internal.model.EventVisibility;
import com.xgen.events.internal.model.Paginated;
import com.xgen.events.internal.model.Paginated.Direction;
import com.xgen.events.internal.model.ResourceId;
import com.xgen.events.internal.model.Source;
import com.xgen.events.internal.model.SourceInfo;
import com.xgen.events.internal.model.UserSourceInfo;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

@Singleton
public class MongoEventDao implements EventDao {
  public static final String ID_FIELD = "_id";

  public static final List<Class<?>> SOURCE_CLASSES =
      List.of(
          SourceInfo.class,
          UserSourceInfo.class,
          AccountUserSourceInfo.class,
          AlertConfigSourceInfo.class);

  private final MongoCollection<Event> collection;
  private final EventOtelMetrics eventOtelMetrics;

  @Inject
  public MongoEventDao(
      MongoCollections collections,
      CodecRegistry codecRegistry,
      EventOtelMetrics eventOtelMetrics) {
    collection = collections.of(Event.class).withCodecRegistry(codecRegistry);
    this.eventOtelMetrics = eventOtelMetrics;
    // We rely on an automatic PojoCodecProvider to handle all our POJO encoding/decoding. For
    // polymorphic types, we need to either:

    // 1) Create a new, non-automatic PojoCodecProvider that explicitly registers all polymorphic
    // types and is added to the CodecRegistry AHEAD of the automatic one, or
    // 2) Use the existing automatic PojoCodecProvider but "prep" the registry with our polymorphic
    // class models by fetching them on startup

    // We are using Option 2 here. See https://jira.mongodb.org/browse/JAVA-5565 for more
    // context, and an example of how to implement Option 1.
    SOURCE_CLASSES.forEach(c -> collection.getCodecRegistry().get(c));
  }

  public Optional<Event> find(Object id) {
    return Optional.ofNullable(collection.find(eq(id)).first());
  }

  public void insertOneReplicaSafe(Event event) {
    Instant startTime = Instant.now();

    collection.withWriteConcern(WriteConcern.MAJORITY).insertOne(event);

    double durationSeconds = Duration.between(startTime, Instant.now()).toNanos() / 1e9;
    Attributes saveAttributes =
        Attributes.of(
            AttributeKey.stringKey("event_type"), event.getEventType(),
            AttributeKey.stringKey("source_type"), event.getSource().getSourceType().name());
    eventOtelMetrics.getSaveEventsDuration().record(durationSeconds, saveAttributes);
  }

  public BulkWriteResult bulkInsertEvents(List<Event> events) {
    List<InsertOneModel<Event>> batch =
        events.stream().map(InsertOneModel::new).collect(Collectors.toList());

    BulkWriteOptions options = new BulkWriteOptions().ordered(false);

    return collection.withWriteConcern(WriteConcern.ACKNOWLEDGED).bulkWrite(batch, options);
  }

  /**
   * @param dbQueryComment A comment attached to the MongoDB query, captured as the
   *     `db.query.comment` attribute in the `db_client_operation_duration` metric
   * @see <a
   *     href="https://github.com/10gen/mms/blob/master/systems/common/java/src/main/java/com/xgen/common/mongo/client/QUERY_LABELING.md">MongoDB
   *     Query Labeling with Comments</a>
   */
  private Paginated<Event> getEvents(
      @Nullable ObjectId projectId,
      @Nullable ObjectId orgId,
      @Nullable @Var Collection<String> eventTypes,
      @Nullable Collection<String> excludedEventTypes,
      @Nullable @Var Collection<ResourceId> resourceIds,
      @Nullable Collection<ResourceId> excludedResourceIds,
      @Nullable Instant startDate,
      @Nullable Instant endDate,
      @Nullable @Var Collection<EventVisibility> visibilities,
      @Nullable @Var Collection<Source> sources,
      @Nullable Collection<Source> excludedSources,
      @Nullable ObjectId previousPageToken,
      @Nullable ObjectId nextPageToken,
      @Nullable Instant previousPageTokenCreatedDate,
      @Nullable Instant nextPageTokenCreatedDate,
      int limit,
      String dbQueryComment) {
    List<Bson> filters = new ArrayList<>();

    if (projectId != null) {
      filters.add(
          eq(FieldDefs.PROJECT_ID_REVERSED_FIELD, StringUtils.reverse(projectId.toHexString())));
    }

    if (orgId != null) {
      filters.add(eq(FieldDefs.ORG_ID_FIELD, orgId));
    }

    if (CollectionUtils.isNotEmpty(eventTypes)) {
      if (CollectionUtils.isNotEmpty(excludedEventTypes)) {
        eventTypes = new ArrayList<>(eventTypes);
        eventTypes.removeAll(excludedEventTypes);
      }
      filters.add(in(FieldDefs.EVENT_TYPE_FIELD, eventTypes));
    } else if (CollectionUtils.isNotEmpty(excludedEventTypes)) {
      filters.add(nin(FieldDefs.EVENT_TYPE_FIELD, excludedEventTypes));
    }

    if (CollectionUtils.isNotEmpty(resourceIds)) {
      if (CollectionUtils.isNotEmpty(excludedResourceIds)) {
        resourceIds =
            resourceIds.stream()
                .filter(
                    r ->
                        !excludedResourceIds.stream()
                            .anyMatch(
                                er ->
                                    er.getService().equals(er.getService())
                                        && er.getType().equals(er.getType())
                                        && er.getId().equals(er.getId())))
                .toList();
      }
      List<Bson> orConditions = new ArrayList<>();

      if (CollectionUtils.isNotEmpty(excludedResourceIds)) {
        // Filter OUT resourceIds that match any in the excludedResourceIds list
        resourceIds =
            resourceIds.stream()
                .filter(
                    r ->
                        excludedResourceIds.stream()
                            .noneMatch(
                                er ->
                                    er.getService().equals(r.getService())
                                        && er.getType().equals(r.getType())
                                        && er.getId().equals(r.getId())))
                .collect(Collectors.toList());
      }
      for (ResourceId resourceId : resourceIds) {
        orConditions.add(
            elemMatch(
                FieldDefs.RESOURCES_FIELD,
                and(
                    eq(ResourceId.FieldDefs.SERVICE_FIELD, resourceId.getService()),
                    eq(ResourceId.FieldDefs.TYPE_FIELD, resourceId.getType()),
                    eq(ResourceId.FieldDefs.ID_FIELD, resourceId.getId()))));
      }

      filters.add(or(orConditions));

    } else if (CollectionUtils.isNotEmpty(excludedResourceIds)) {

      List<Bson> exclusionConditions = new ArrayList<>();

      for (ResourceId excluded : excludedResourceIds) {
        Bson elem =
            elemMatch(
                FieldDefs.RESOURCES_FIELD,
                and(
                    eq(ResourceId.FieldDefs.SERVICE_FIELD, excluded.getService()),
                    eq(ResourceId.FieldDefs.TYPE_FIELD, excluded.getType()),
                    eq(ResourceId.FieldDefs.ID_FIELD, excluded.getId())));
        exclusionConditions.add(elem);
      }

      filters.add(nor(exclusionConditions));
    }

    @Var Instant effectiveStartDate = startDate;
    @Var Instant effectiveEndDate = endDate;

    @Var Bson sort = descending(FieldDefs.CREATED_DATE_FIELD);
    @Var Paginated.Direction direction = Direction.NONE;

    // Adjust effective dates based on pagination tokens for more restrictive boundaries
    if (nextPageToken != null) {
      if (nextPageTokenCreatedDate != null) {
        effectiveEndDate = nextPageTokenCreatedDate;
      }
    } else if (previousPageToken != null) {
      if (previousPageTokenCreatedDate != null) {
        effectiveStartDate = previousPageTokenCreatedDate;
      }
    }

    if (effectiveStartDate != null) {
      filters.add(gte(FieldDefs.CREATED_DATE_FIELD, effectiveStartDate));
    }

    if (effectiveEndDate != null) {
      filters.add(lte(FieldDefs.CREATED_DATE_FIELD, effectiveEndDate));
    }

    if (CollectionUtils.isEmpty(visibilities)) {
      visibilities = List.of(EventVisibility.VISIBILITY_ALL);
    }
    filters.add(in(FieldDefs.VISIBILITY_FIELD, visibilities));

    if (CollectionUtils.isNotEmpty(sources)) {
      if (CollectionUtils.isNotEmpty(excludedSources)) {
        sources = new ArrayList<>(sources);
        sources.removeAll(excludedSources);
      }
      filters.add(or(getSourcesFilter(sources)));
    } else if (CollectionUtils.isNotEmpty(excludedSources)) {
      filters.add(nor(getSourcesFilter(excludedSources)));
    } else {
      filters.add(
          in(
              FieldDefs.SOURCE_FIELD + "." + Source.FieldDefs.SOURCE_TYPE_FIELD,
              Arrays.stream(EventSource.values()).toList()));
    }

    if (nextPageToken != null) {
      filters.add(lte(ID_FIELD, nextPageToken));
      direction = Direction.NEXT;
    } else if (previousPageToken != null) {
      filters.add(gte(ID_FIELD, previousPageToken));
      sort = ascending(FieldDefs.CREATED_DATE_FIELD);
      direction = Direction.PREVIOUS;
    }

    List<Event> events =
        collection
            .find(and(filters))
            .comment(dbQueryComment)
            .limit(limit + 2) // 2 additional records used for pagination
            .sort(sort)
            .into(new ArrayList<>());

    if (Paginated.Direction.PREVIOUS.equals(direction)) {
      // reverse the elements since the sort is reversed for previous pages
      Collections.reverse(events);
    }

    return Paginated.of(events, Event::getId, Event::getCreatedDate, limit, direction);
  }

  public Paginated<Event> getProjectEvents(
      ObjectId projectId,
      @Nullable Collection<String> eventTypes,
      @Nullable Collection<String> excludedEventTypes,
      @Nullable Collection<ResourceId> resourceIds,
      @Nullable Collection<ResourceId> excludedResourceIds,
      @Nullable Instant startDate,
      @Nullable Instant endDate,
      @Nullable Collection<EventVisibility> visibilities,
      @Nullable Collection<Source> sources,
      @Nullable Collection<Source> excludedSources,
      @Nullable ObjectId previousPageToken,
      @Nullable ObjectId nextPageToken,
      @Nullable Instant previousPageTokenCreatedDate,
      @Nullable Instant nextPageTokenCreatedDate,
      int limit) {
    return getEvents(
        projectId,
        null,
        eventTypes,
        excludedEventTypes,
        resourceIds,
        excludedResourceIds,
        startDate,
        endDate,
        visibilities,
        sources,
        excludedSources,
        previousPageToken,
        nextPageToken,
        previousPageTokenCreatedDate,
        nextPageTokenCreatedDate,
        limit,
        "getProjectEvents");
  }

  public Paginated<Event> getOrgEvents(
      ObjectId orgId,
      @Nullable Collection<String> eventTypes,
      @Nullable Collection<String> excludedEventTypes,
      @Nullable Collection<ResourceId> resourceIds,
      @Nullable Collection<ResourceId> excludedResourceIds,
      @Nullable Instant startDate,
      @Nullable Instant endDate,
      @Nullable Collection<EventVisibility> visibilities,
      @Nullable Collection<Source> sources,
      @Nullable Collection<Source> excludedSources,
      @Nullable ObjectId previousPageToken,
      @Nullable ObjectId nextPageToken,
      @Nullable Instant previousPageTokenCreatedDate,
      @Nullable Instant nextPageTokenCreatedDate,
      int limit) {
    return getEvents(
        null,
        orgId,
        eventTypes,
        excludedEventTypes,
        resourceIds,
        excludedResourceIds,
        startDate,
        endDate,
        visibilities,
        sources,
        excludedSources,
        previousPageToken,
        nextPageToken,
        previousPageTokenCreatedDate,
        nextPageTokenCreatedDate,
        limit,
        "getOrgEvents");
  }

  public Paginated<Event> getGlobalEvents(
      @Nullable Collection<String> eventTypes,
      @Nullable Collection<String> excludedEventTypes,
      @Nullable Collection<ResourceId> resourceIds,
      @Nullable Collection<ResourceId> excludedResourceIds,
      @Nullable Instant startDate,
      @Nullable Instant endDate,
      @Nullable Collection<EventVisibility> visibilities,
      @Nullable Collection<Source> sources,
      @Nullable Collection<Source> excludedSources,
      @Nullable ObjectId previousPageToken,
      @Nullable ObjectId nextPageToken,
      @Nullable Instant previousPageTokenCreatedDate,
      @Nullable Instant nextPageTokenCreatedDate,
      int limit) {
    return getEvents(
        null,
        null,
        eventTypes,
        excludedEventTypes,
        resourceIds,
        excludedResourceIds,
        startDate,
        endDate,
        visibilities,
        sources,
        excludedSources,
        previousPageToken,
        nextPageToken,
        previousPageTokenCreatedDate,
        nextPageTokenCreatedDate,
        limit,
        "getGlobalEvents");
  }

  public Stream<Event> findByProjectId(ObjectId projectId) {
    Objects.requireNonNull(projectId);

    return StreamSupport.stream(
        collection
            .find(
                eq(
                    FieldDefs.PROJECT_ID_REVERSED_FIELD,
                    StringUtils.reverse(projectId.toHexString())))
            .spliterator(),
        false);
  }

  // TODO: Index creation on startup is currently disabled for the /systems directory. This will be
  // enabled following CLOUDP-334605
  //  private List<MongoIndex> getIndexes() {
  //    ArrayList<MongoIndex> indexes = new ArrayList<>();
  //
  //    // Shard key: {projectIdReversed: 1, requestId: 1}
  //    indexes.add(
  //        new MongoIndex(
  //            MongoIndex.builder()
  //                .key(FieldDefs.PROJECT_ID_REVERSED_FIELD, 1)
  //                .key(FieldDefs.REQUEST_ID_FIELD, 1)
  //                .unique()));
  //    indexes.add(
  //        new MongoIndex(
  //            MongoIndex.builder()
  //                .key(FieldDefs.PROJECT_ID_REVERSED_FIELD, 1)
  //                .key(joinFields(FieldDefs.SOURCE_FIELD, Source.FieldDefs.SOURCE_TYPE_FIELD), 1)
  //                .key(FieldDefs.VISIBILITY_FIELD, 1)
  //                .key(FieldDefs.EVENT_TYPE_FIELD, 1)
  //                .key(FieldDefs.CREATED_DATE_FIELD, 1)
  //                .key(ID_FIELD, 1)));
  //    indexes.add(
  //        new MongoIndex(
  //            MongoIndex.builder()
  //                .key(FieldDefs.ORG_ID_FIELD, 1)
  //                .key(joinFields(FieldDefs.SOURCE_FIELD, Source.FieldDefs.SOURCE_TYPE_FIELD), 1)
  //                .key(FieldDefs.VISIBILITY_FIELD, 1)
  //                .key(FieldDefs.EVENT_TYPE_FIELD, 1)
  //                .key(FieldDefs.CREATED_DATE_FIELD, 1)
  //                .key(ID_FIELD, 1)));
  //    indexes.add(
  //        new MongoIndex(
  //            MongoIndex.builder()
  //                .key(FieldDefs.PROJECT_ID_REVERSED_FIELD, 1)
  //                .key(FieldDefs.CREATED_DATE_FIELD, 1)
  //                .key(FieldDefs.VISIBILITY_FIELD, 1)
  //                .key(ID_FIELD, 1)));
  //    indexes.add(
  //        new MongoIndex(
  //            MongoIndex.builder()
  //                .key(FieldDefs.ORG_ID_FIELD, 1)
  //                .key(FieldDefs.CREATED_DATE_FIELD, 1)
  //                .key(FieldDefs.VISIBILITY_FIELD, 1)
  //                .key(ID_FIELD, 1)));
  //    indexes.add(
  //        new MongoIndex(
  //            MongoIndex.builder()
  //                .key(FieldDefs.EVENT_TYPE_FIELD, 1)
  //                .key(FieldDefs.CREATED_DATE_FIELD, 1)));
  //    indexes.add(
  //        new MongoIndex(
  //            MongoIndex.builder()
  //                .key(FieldDefs.PROJECT_ID_REVERSED_FIELD, 1)
  //                .key(RESOURCES_ID_FIELD, 1)
  //                .key(FieldDefs.CREATED_DATE_FIELD, 1)
  //                .key(FieldDefs.VISIBILITY_FIELD, 1)
  //                .key(ID_FIELD, 1)
  //                .sparse()));
  //
  //    return indexes;
  //  }

  //  private static String joinFields(String... fields) {
  //    return String.join(".", fields);
  //  }

  private List<Bson> getSourcesFilter(Collection<Source> sources) {
    if (CollectionUtils.isEmpty(sources)) {
      return List.of();
    }

    List<EventSource> sourceTypes =
        sources.stream()
            .map(Source::getSourceType)
            .filter(sourceType -> sourceType != EventSource.USER)
            .collect(Collectors.toList());

    List<String> usernames =
        sources.stream()
            .filter(
                source ->
                    source.getSourceType() == EventSource.USER && source.getSourceInfo() != null)
            .map(source -> ((UserSourceInfo) source.getSourceInfo()).getUsername())
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());

    List<Bson> sourceFilters = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(usernames)) {
      sourceFilters.add(
          and(
              eq(
                  FieldDefs.SOURCE_FIELD + "." + Source.FieldDefs.SOURCE_TYPE_FIELD,
                  EventSource.USER),
              in(
                  FieldDefs.SOURCE_FIELD
                      + "."
                      + Source.FieldDefs.SOURCE_INFO_FIELD
                      + "."
                      + UserSourceInfo.FieldDefs.USERNAME_FIELD,
                  usernames)));
    } else if (sources.stream().anyMatch(source -> source.getSourceType() == EventSource.USER)) {
      sourceTypes.add(EventSource.USER);
    }

    sourceFilters.add(
        in(FieldDefs.SOURCE_FIELD + "." + Source.FieldDefs.SOURCE_TYPE_FIELD, sourceTypes));
    return sourceFilters;
  }
}
