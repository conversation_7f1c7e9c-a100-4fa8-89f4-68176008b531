syntax = "proto3";

package com.xgen.events.schemas.billing.v1;

import "google/protobuf/timestamp.proto";

option java_multiple_files = true;

message BillingEventMessage {
  optional string payment_method_id = 1;
  optional string credit_card_number = 2;
  optional string pay_pal_email_address = 3;
  optional google.protobuf.Timestamp expiration_date = 4;
  optional string check_number = 5;
  optional string wire_transfer_number = 6;
  optional string invoice_id = 7;
  optional int64 invoice_total_cents = 8;
  optional int64 invoice_total_paid_cents = 9;
  optional int64 invoice_total_refunded_cents = 10;
  optional int64 last_daily_bill_cents = 11;
  optional string payment_id = 12;
  optional string charge_failed_code = 13;
  optional string charge_failure_message = 14;
  optional int32 old_billable_hosts = 15;
  optional int32 new_billable_hosts = 16;
  optional google.protobuf.Timestamp duplicate_usage_date = 17;
  CreditMessage credit = 18;
  PaymentMessage payment = 19;

  // BillingAudit fields
  optional string activation_code = 20;
  optional float discount_percent = 21;
  repeated string discount_skus = 22;
  optional google.protobuf.Timestamp discount_end_date = 23;
  optional int64 credit_amount_cents = 24;
  optional int64 forgive_amount_cents = 25;
  optional int64 refund_amount_cents = 26;
  optional string reason = 27;
  optional google.protobuf.Timestamp invoice_end_date = 28;
  optional string old_plan_type = 29;
  optional string new_plan_type = 30;
  SubscriptionPricingModelMessage old_subscription_pricing_model = 31;
  optional string old_entitlements_description = 32;
  optional int32 old_support_sla_minutes = 33;
  SubscriptionPricingModelMessage new_subscription_pricing_model = 34;
  optional string new_entitlements_description = 35;
  optional int32 new_support_sla_minutes = 36;
  optional google.protobuf.Timestamp plan_end_date = 37;
  optional google.protobuf.Timestamp new_plan_end_date = 38;
  optional google.protobuf.Timestamp old_free_until_date = 39;
  optional google.protobuf.Timestamp new_free_until_date = 40;
  optional string coupon_id = 41;
  optional string gcp_marketplace_account_id = 42;
  optional string aws_marketplace_customer_id = 43;
  optional string azure_marketplace_external_subscription_id = 44;
  optional int64 partner_marketplace_reported_amount_cents = 45;
  optional string paying_org_id = 46;
  optional string linked_org_id = 47;
  UpdatedBillingPropertyMessage billing_email_address = 48;
  UpdatedBillingPropertyMessage company_address = 49;
  CrossOrgBillingAuditInfoMessage cross_org_billing_audit_info = 50;
  PendingDealActivationAuditInfoMessage pending_deal_activation_audit_info = 51;
  optional bool evergreen = 52;
  optional string rebill_source = 53;
  optional string app_user_id = 54;
  optional string username = 55;
  optional string billing_ticket_num = 56;
  optional string vercel_installation_id = 57;
  optional int64 partner_marketplace_day_value_cents = 58;
  optional int64 partner_marketplace_period_value_cents = 59;
  optional string vercel_installation_url = 60;
}

message SubscriptionPricingModelMessage {
  PlanSubscriptionTiersMessage pricing_tiers = 1;
  optional int64 plan_minimum_cents = 2;

  message PlanSubscriptionTiersMessage {
    repeated PlanSubscriptionTierMessage tiers = 1;
  }

  message PlanSubscriptionTierMessage {
    optional int64 upper_boundary_cents = 1;
    optional double uplift_percent = 2;
  }
}

message UpdatedBillingPropertyMessage {
  optional string old_value = 1;
  optional string new_value = 2;
}

message CrossOrgBillingAuditInfoMessage {
  optional string paying_org_name = 1;
  optional string linked_org_name = 2;
  optional google.protobuf.Timestamp unlink_date = 3;
  optional string pending_deal_activation_id = 4;
  optional string ticket = 5;
}

message PendingDealActivationAuditInfoMessage {
  optional google.protobuf.Timestamp apply_on_date = 1;
  optional bool has_multiple_credits = 2;
}

message CreditMessage {
  optional string id = 1;
  optional string org_id = 2;
  optional google.protobuf.Timestamp created = 3;
  optional string activation_code = 4;
  optional string reason = 5;
  optional string issuer = 6;
  optional int64 amount_cents = 7;
  optional int64 amount_remaining_cents = 8;
  optional int64 total_billed_cents = 9;
  optional string note = 10;
  optional string type = 11;
  optional string coupon_id = 12;
  optional google.protobuf.Timestamp start_date = 13;
  optional google.protobuf.Timestamp end_date = 14;
  optional bool valid_payment_method = 15;
  optional int64 hourly_billing_limit_cents = 16;
  optional string rollover_credit_id = 17;
  optional bool elastic_invoicing = 18;
  optional string gcp_marketplace_entitlement_id = 19;
  optional string aws_marketplace_customer_id = 20;
  optional string aws_marketplace_product_code = 21;
  optional string azure_external_subscription_id = 22;
  SalesforceOpportunityLineItemMessage salesforce_opportunity_line_item = 23;
  optional google.protobuf.Timestamp bill_by_date = 24;
  optional string revenue_reason = 25;
  optional bool evergreen = 26;
  optional string azure_private_plan_id = 27;
  optional string vercel_billing_plan_id = 28;
  optional string vercel_installation_id = 29;
  optional string amount_remaining_cents_pss = 30;
}

message SalesforceOpportunityLineItemMessage {
  optional string id = 1;
  optional google.protobuf.Timestamp start_date = 2;
  optional google.protobuf.Timestamp end_date = 3;
  optional double quantity = 4;
  optional string product_code = 5;
  optional double discount_percent = 6;
  optional string currency = 7;
  optional double term_months = 8;
  optional double unit_price = 9;
  optional google.protobuf.Timestamp bill_by_date = 10;
  optional bool eligible_for_tiered_pricing = 11;
  optional bool elastic_invoicing = 12;
  optional bool evergreen = 13;
  optional google.protobuf.Timestamp sfdc_created_date = 14;
}

message PaymentMessage {
  optional string id = 1;
  optional string org_id = 2;
  optional string invoice_id = 3;
  optional string invoice_type = 4;
  optional string address_id = 5;
  optional string min_line_item_id = 6;
  optional string max_line_item_id = 7;
  optional string payment_method = 8;
  optional string payment_method_id = 9;
  optional string company_code = 10;
  optional google.protobuf.Timestamp created = 11;
  optional google.protobuf.Timestamp updated = 12;
  optional int64 subtotal_cents = 13;
  optional int64 sales_tax_cents = 14;
  optional string sales_tax_status = 15;
  optional int32 failed_sales_tax_attempts = 16;
  map<string, double> sales_tax_rates = 17;
  optional int64 amount_billed_cents = 18;
  optional int64 amount_paid_cents = 19;
  optional int64 amount_reported_cents = 20;
  optional string status = 21;
  optional string card_last_4 = 22;
  optional google.protobuf.Timestamp card_expiration = 23;
  PaymentStripeMessage stripe = 24;
  PaymentBraintreeMessage braintree = 25;
  optional google.protobuf.Timestamp payment_due_date = 26;
  FailedChargeAttemptsMessage failed_charge_attempts = 27;
  optional string note = 28;
  optional string update_note = 29;
  optional string vat_number = 30;
  optional string vat_status = 31;
  optional string check_number = 32;
  optional string wire_transfer_number = 33;
  optional string ava_tax_transaction_code = 34;
  optional google.protobuf.Timestamp ava_tax_transaction_date = 35;
  optional string netsuite_invoice_internal_id = 36;
  optional string credit_id = 37;
  NetsuiteInvoiceDetailsMessage netsuite_invoice_details = 38;
  optional int64 num_times_transitioned_to_processing = 39;
  optional string billing_account = 40;
  optional google.protobuf.Timestamp charge_lock = 41;
  optional string no_tax_reason = 42;
  optional string tax_failure_source = 43;
  VercelPaymentContextMessage vercel = 44;
}

message PaymentStripeMessage {
  optional string charge_id = 1;
  optional string charge_failure_code = 2;
  optional string charge_decline_code = 3;
  optional string payment_intent_id = 4;
  optional string payment_intent_status = 5;
  optional google.protobuf.Timestamp processing_start_date = 6;
  // conversionRate is a BigDecimal value but this is not supported by protobuf (with no plans to in the near future: https://github.com/protocolbuffers/protobuf/issues/4406)
  // As such, we use a string to represent it - similar to Google's Decimal type: https://github.com/googleapis/googleapis/blob/master/google/type/decimal.proto
  optional string conversion_rate = 7;
}

message PaymentBraintreeMessage {
  optional string transaction_id = 1;
  optional string charge_failure_message = 2;
  optional string refund_id = 3;
}

message FailedChargeAttemptsMessage {
  optional google.protobuf.Timestamp last_attempted_charge = 1;
  optional int32 attempts = 2;
}

message NetsuiteInvoiceDetailsMessage {
  optional double tax_total = 1;
  optional double rate = 2;
  optional double total = 3;
  optional string currency = 4;
  optional string customer_internal_id = 5;
  optional double exchange_rate = 6;
  optional double unit_price = 7;
  optional double amount_paid = 8;
  optional google.protobuf.Timestamp due_date = 9;
  optional double listing_fee_percentage = 10;
  optional string tran_id = 11;
}

message VercelPaymentContextMessage {
  optional string invoice_id = 1;
  optional google.protobuf.Timestamp refund_requested_at = 2;
  optional google.protobuf.Timestamp refund_confirmed_at = 3;
}
