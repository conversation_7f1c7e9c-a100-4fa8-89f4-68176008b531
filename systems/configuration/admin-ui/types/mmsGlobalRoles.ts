// This is a replica of server/src/main/com/xgen/cloud/access/role/_public/model/Role.java
export enum MmsGlobalRole {
  GLOBAL_READ_ONLY = 'GLOBAL_READ_ONLY',
  GLOBAL_ORG_READ_ONLY = 'G<PERSON><PERSON><PERSON>L_ORG_READ_ONLY',
  GLOBAL_MONITORING_ADMIN = 'GLOBAL_MONITORING_ADMIN',
  GLOBAL_ADMIN_READ_ONLY = 'GLOBAL_ADMIN_READ_ONLY',
  GLOBAL_LEGAL_ADMIN = 'GLOBAL_LEGAL_ADMIN',
  GLOBAL_AUTOMATION_ADMIN = 'GLOBAL_AUTOMATION_ADMIN',
  GLOBAL_BACKUP_ADMIN = 'GLOBAL_BACKUP_ADMIN',
  GLOBAL_OWNER = 'GLOBAL_OWNER',
  GLOBAL_USER_ADMIN = 'GLOBAL_USER_ADMIN',
  GLOBAL_APP_LIMIT_OVERRIDE_ADMIN = 'GLOBAL_APP_LIMIT_OVERRIDE_ADMIN',
  GLOBAL_MFA_ADMIN = 'GLOBAL_MFA_ADMIN',
  GLOBAL_EMPLOYEE_MFA_ADMIN = 'GLOBAL_EMPLOYEE_MFA_ADMIN',
  GLOBAL_VERSION_MANAGER_READ_ONLY = 'GLOBAL_VERSION_MANAGER_READ_ONLY',
  GLOBAL_BILLING_ADMIN = 'GLOBAL_BILLING_ADMIN',
  GLOBAL_SALESFORCE_ACCOUNT_ADMIN = 'GLOBAL_SALESFORCE_ACCOUNT_ADMIN',
  GLOBAL_ATLAS_TSE = 'GLOBAL_ATLAS_TSE',
  GLOBAL_ATLAS_ENGINEERING_OPERATOR = 'GLOBAL_ATLAS_ENGINEERING_OPERATOR',
  GLOBAL_ATLAS_OPERATOR = 'GLOBAL_ATLAS_OPERATOR',
  GLOBAL_ATLAS_ADMIN = 'GLOBAL_ATLAS_ADMIN',
  GLOBAL_SERVICE_ACCOUNT_ADMIN = 'GLOBAL_SERVICE_ACCOUNT_ADMIN',
  GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN = 'GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN',
  GLOBAL_ATLAS_CUSTOM_BUILD_MANAGER = 'GLOBAL_ATLAS_CUSTOM_BUILD_MANAGER',
  GLOBAL_QUERY_ENGINE_INTERNAL_ADMIN = 'GLOBAL_QUERY_ENGINE_INTERNAL_ADMIN',
  GLOBAL_ACCOUNT_SUSPENSION_ADMIN = 'GLOBAL_ACCOUNT_SUSPENSION_ADMIN',
  GLOBAL_CHARTS_ADMIN = 'GLOBAL_CHARTS_ADMIN',
  GLOBAL_PROACTIVE_SUPPORT_ADMIN = 'GLOBAL_PROACTIVE_SUPPORT_ADMIN',
  GLOBAL_FEATURE_FLAG_ADMIN = 'GLOBAL_FEATURE_FLAG_ADMIN',
  GLOBAL_APP_SETTING_ADMIN = 'GLOBAL_APP_SETTING_ADMIN',
  GLOBAL_CRON_JOBS_ADMIN = 'GLOBAL_CRON_JOBS_ADMIN',
  GLOBAL_APP_SERVICES_CLUSTER_DEBUG_DATA_ACCESS = 'GLOBAL_APP_SERVICES_CLUSTER_DEBUG_DATA_ACCESS',
  GLOBAL_BAAS_FEATURE_ADMIN = 'GLOBAL_BAAS_FEATURE_ADMIN',
  GLOBAL_BAAS_SUPPORT = 'GLOBAL_BAAS_SUPPORT',
  GLOBAL_BAAS_ADMIN = 'GLOBAL_BAAS_ADMIN',
  GLOBAL_EXPERIMENT_ASSIGNMENT_ADMIN = 'GLOBAL_EXPERIMENT_ASSIGNMENT_ADMIN',
  GLOBAL_EXPERIMENT_OVERRIDE_ASSIGNMENT_ADMIN = 'GLOBAL_EXPERIMENT_OVERRIDE_ASSIGNMENT_ADMIN',
  GLOBAL_PARTNER_ADMIN = 'GLOBAL_PARTNER_ADMIN',
  GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN = 'GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN',
  GLOBAL_BACKUP_COMPLIANCE_POLICY_ADMIN = 'GLOBAL_BACKUP_COMPLIANCE_POLICY_ADMIN',
  GLOBAL_SECURITY_ADMIN = 'GLOBAL_SECURITY_ADMIN',
  GLOBAL_METERING_FEATURE_TEAM_USER = 'GLOBAL_METERING_FEATURE_TEAM_USER',
  GLOBAL_BILLING_FEATURE_TEAM_USER = 'GLOBAL_BILLING_FEATURE_TEAM_USER',
  GLOBAL_BILLING_HELP_TOOLING = 'GLOBAL_BILLING_HELP_TOOLING',
  GLOBAL_ATLAS_LOCALIZED_SSH_REQUEST_ACCESS = 'GLOBAL_ATLAS_LOCALIZED_SSH_REQUEST_ACCESS',
  GLOBAL_GLOBAL_ALERTS_ADMIN = 'GLOBAL_GLOBAL_ALERTS_ADMIN',
  GLOBAL_SYSTEM_ALERTS_ADMIN = 'GLOBAL_SYSTEM_ALERTS_ADMIN',
}
