// This mapping is a replica of server/src/main/com/xgen/cloud/user/_private/svc/GlobalRolesMap.java
// We keep this mapping here in order to map users' Okta groups to MMS global roles, which is ultimately associated with Config Namespaces.
import { MmsGlobalRole } from '../../types/mmsGlobalRoles';

// Helper to map group to roles
function map(...roles: Array<MmsGlobalRole>): Array<MmsGlobalRole> {
  return roles;
}

// Commercial group-to-role mapping
const GLOBAL_ROLE_MAPPING: Record<string, Array<MmsGlobalRole>> = {
  '10gen-mms-team-cs': map(MmsGlobalRole.GLOBAL_READ_ONLY),
  '10gen-mms-team-consulting-engineering': map(MmsGlobalRole.GLOBAL_READ_ONLY),
  '10gen-mms-team-finance': map(MmsGlobalRole.GLOBAL_ORG_READ_ONLY),
  '10gen-mms-team-product': map(MmsGlobalRole.GLOBAL_READ_ONLY),
  '10gen-mms-team-product-plus': map(MmsGlobalRole.GLOBAL_MONITORING_ADMIN),
  '10gen-mms-team-core-db': map(MmsGlobalRole.GLOBAL_MONITORING_ADMIN),
  '10gen-mms-team-industry-solutions': map(MmsGlobalRole.GLOBAL_READ_ONLY),
  '10gen-mms-team-analytics-biz-ops': map(MmsGlobalRole.GLOBAL_READ_ONLY),
  '10gen-mms-team-techops': map(MmsGlobalRole.GLOBAL_READ_ONLY),
  '10gen-mms-team-design': map(MmsGlobalRole.GLOBAL_READ_ONLY),
  '10gen-mms-global-admin-read-only': map(MmsGlobalRole.GLOBAL_ADMIN_READ_ONLY),
  '10gen-cloud': map(MmsGlobalRole.GLOBAL_READ_ONLY, MmsGlobalRole.GLOBAL_MONITORING_ADMIN),
  '10gen-support': map(MmsGlobalRole.GLOBAL_READ_ONLY, MmsGlobalRole.GLOBAL_MONITORING_ADMIN),
  '10gen-mms-global-legal-admin': map(MmsGlobalRole.GLOBAL_LEGAL_ADMIN),
  '10gen-mms-automation-admin': map(MmsGlobalRole.GLOBAL_AUTOMATION_ADMIN),
  '10gen-mms-backup-admin': map(MmsGlobalRole.GLOBAL_BACKUP_ADMIN),
  '10gen-mms-monitoring-admin': map(MmsGlobalRole.GLOBAL_MONITORING_ADMIN),
  '10gen-mms-owner': map(MmsGlobalRole.GLOBAL_OWNER),
  '10gen-mms-user-admin': map(MmsGlobalRole.GLOBAL_USER_ADMIN),
  '10gen-mms-app-limit-override-admin': map(MmsGlobalRole.GLOBAL_APP_LIMIT_OVERRIDE_ADMIN),
  '10gen-mms-mfa-admin': map(MmsGlobalRole.GLOBAL_MFA_ADMIN),
  '10gen-mms-employee-mfa-admin': map(MmsGlobalRole.GLOBAL_EMPLOYEE_MFA_ADMIN),
  '10gen-mms-version-manager-read-only': map(MmsGlobalRole.GLOBAL_VERSION_MANAGER_READ_ONLY),
  '10gen-mms-billing-admin': map(MmsGlobalRole.GLOBAL_BILLING_ADMIN),
  '10gen-mms-billing-help-tooling': map(MmsGlobalRole.GLOBAL_BILLING_HELP_TOOLING),
  '10gen-mms-salesforce-account-admin': map(MmsGlobalRole.GLOBAL_SALESFORCE_ACCOUNT_ADMIN),
  '10gen-mms-atlas-support': map(MmsGlobalRole.GLOBAL_ATLAS_TSE),
  '10gen-mms-global-atlas-engineering-operator': map(MmsGlobalRole.GLOBAL_ATLAS_ENGINEERING_OPERATOR),
  '10gen-mms-atlas-operator': map(MmsGlobalRole.GLOBAL_ATLAS_OPERATOR),
  '10gen-mms-atlas-admin': map(MmsGlobalRole.GLOBAL_ATLAS_ADMIN),
  '10gen-mms-gsa-admin': map(MmsGlobalRole.GLOBAL_SERVICE_ACCOUNT_ADMIN),
  '10gen-mms-atlas-software-version-rollout-admin': map(MmsGlobalRole.GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN),
  '10gen-mms-query-engine-internal-admin': map(MmsGlobalRole.GLOBAL_QUERY_ENGINE_INTERNAL_ADMIN),
  '10gen-mms-account-suspension-admin': map(MmsGlobalRole.GLOBAL_ACCOUNT_SUSPENSION_ADMIN),
  '10gen-mms-charts-admin': map(MmsGlobalRole.GLOBAL_CHARTS_ADMIN),
  '10gen-proactive-support-admin': map(MmsGlobalRole.GLOBAL_PROACTIVE_SUPPORT_ADMIN),
  '10gen-mms-feature-flag-admin': map(MmsGlobalRole.GLOBAL_FEATURE_FLAG_ADMIN),
  '10gen-mms-app-setting-admin': map(MmsGlobalRole.GLOBAL_APP_SETTING_ADMIN),
  '10gen-mms-cron-jobs-admin': map(MmsGlobalRole.GLOBAL_CRON_JOBS_ADMIN),
  '10gen-mms-atlas-app-services-debug-data-access': map(MmsGlobalRole.GLOBAL_APP_SERVICES_CLUSTER_DEBUG_DATA_ACCESS),
  '10gen-baas-feature-admin': map(MmsGlobalRole.GLOBAL_BAAS_FEATURE_ADMIN),
  '10gen-baas-support': map(MmsGlobalRole.GLOBAL_BAAS_SUPPORT),
  '10gen-baas-admin': map(MmsGlobalRole.GLOBAL_BAAS_ADMIN),
  '10gen-mms-experiment-assignment-admin': map(MmsGlobalRole.GLOBAL_EXPERIMENT_ASSIGNMENT_ADMIN),
  '10gen-mms-experiment-override-assignment-admin': map(MmsGlobalRole.GLOBAL_EXPERIMENT_OVERRIDE_ASSIGNMENT_ADMIN),
  '10gen-mms-partner-admin': map(MmsGlobalRole.GLOBAL_PARTNER_ADMIN),
  '10gen-mms-capacity-reservation-admin': map(MmsGlobalRole.GLOBAL_ATLAS_CAPACITY_RESERVATION_ADMIN),
  '10gen-mms-backup-compliance-policy-admin': map(MmsGlobalRole.GLOBAL_BACKUP_COMPLIANCE_POLICY_ADMIN),
  '10gen-mms-security-admin': map(MmsGlobalRole.GLOBAL_SECURITY_ADMIN),
  '10gen-metering-service-internal-users': map(MmsGlobalRole.GLOBAL_METERING_FEATURE_TEAM_USER),
  '10gen-billing-internal-users': map(MmsGlobalRole.GLOBAL_BILLING_FEATURE_TEAM_USER),
  '10gen-mms-atlas-localized-ssh-access': map(MmsGlobalRole.GLOBAL_ATLAS_LOCALIZED_SSH_REQUEST_ACCESS),
  '10gen-mms-global-alerts-admin': map(MmsGlobalRole.GLOBAL_GLOBAL_ALERTS_ADMIN),
  '10gen-mms-system-alerts-admin': map(MmsGlobalRole.GLOBAL_SYSTEM_ALERTS_ADMIN),
};

// Gov group-to-role mapping
const GLOBAL_GOV_ROLE_MAPPING: Record<string, Array<MmsGlobalRole>> = {
  '10gen-gov-mms-global-owner': map(MmsGlobalRole.GLOBAL_OWNER),
  '10gen-gov-mms-global-read-only': map(MmsGlobalRole.GLOBAL_READ_ONLY),
  '10gen-gov-mms-global-legal-admin': map(MmsGlobalRole.GLOBAL_LEGAL_ADMIN),
  '10gen-gov-mms-global-automation-admin': map(MmsGlobalRole.GLOBAL_AUTOMATION_ADMIN),
  '10gen-gov-mms-global-backup-admin': map(MmsGlobalRole.GLOBAL_BACKUP_ADMIN),
  '10gen-gov-mms-global-monitoring-admin': map(MmsGlobalRole.GLOBAL_MONITORING_ADMIN),
  '10gen-gov-mms-global-user-admin': map(MmsGlobalRole.GLOBAL_USER_ADMIN),
  '10gen-gov-mms-global-billing-admin': map(MmsGlobalRole.GLOBAL_BILLING_ADMIN),
  '10gen-gov-mms-global-atlas-tse': map(MmsGlobalRole.GLOBAL_ATLAS_TSE),
  '10gen-gov-mms-global-atlas-engineering-operator': map(MmsGlobalRole.GLOBAL_ATLAS_ENGINEERING_OPERATOR),
  '10gen-gov-mms-global-atlas-operator': map(MmsGlobalRole.GLOBAL_ATLAS_OPERATOR),
  '10gen-gov-mms-global-atlas-admin': map(MmsGlobalRole.GLOBAL_ATLAS_ADMIN),
  '10gen-gov-mms-global-atlas-software-version-rollout-admin': map(
    MmsGlobalRole.GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN
  ),
  '10gen-gov-mms-global-account-suspension-admin': map(MmsGlobalRole.GLOBAL_ACCOUNT_SUSPENSION_ADMIN),
  '10gen-gov-mms-global-admin-read-only': map(MmsGlobalRole.GLOBAL_ADMIN_READ_ONLY),
  '10gen-gov-mms-app-setting-admin': map(MmsGlobalRole.GLOBAL_APP_SETTING_ADMIN),
  '10gen-gov-mms-feature-flag-admin': map(MmsGlobalRole.GLOBAL_FEATURE_FLAG_ADMIN),
  '10gen-gov-mms-cron-jobs-admin': map(MmsGlobalRole.GLOBAL_CRON_JOBS_ADMIN),
  '10gen-gov-proactive-support-admin': map(MmsGlobalRole.GLOBAL_PROACTIVE_SUPPORT_ADMIN),
  '10gen-gov-mms-atlas-localized-ssh-access': map(MmsGlobalRole.GLOBAL_ATLAS_LOCALIZED_SSH_REQUEST_ACCESS),
  '10gen-gov-mms-global-alerts-admin': map(MmsGlobalRole.GLOBAL_GLOBAL_ALERTS_ADMIN),
  '10gen-gov-mms-system-alerts-admin': map(MmsGlobalRole.GLOBAL_SYSTEM_ALERTS_ADMIN),
};

// Internal group-to-role mapping
const GLOBAL_INTERNAL_ROLE_MAPPING: Record<string, Array<MmsGlobalRole>> = {
  '10gen-internal-mms-owner': map(
    MmsGlobalRole.GLOBAL_OWNER,
    MmsGlobalRole.GLOBAL_AUTOMATION_ADMIN,
    MmsGlobalRole.GLOBAL_BACKUP_ADMIN,
    MmsGlobalRole.GLOBAL_MONITORING_ADMIN,
    MmsGlobalRole.GLOBAL_USER_ADMIN,
    MmsGlobalRole.GLOBAL_BILLING_ADMIN,
    MmsGlobalRole.GLOBAL_ATLAS_TSE,
    MmsGlobalRole.GLOBAL_ATLAS_OPERATOR,
    MmsGlobalRole.GLOBAL_ATLAS_ADMIN
  ),
  '10gen-internal-mms-global-monitoring-admin': map(MmsGlobalRole.GLOBAL_MONITORING_ADMIN),
  '10gen-internal-mms-global-atlas-admin': map(MmsGlobalRole.GLOBAL_ATLAS_ADMIN),
  '10gen-internal-mms-global-atlas-software-version-rollout-admin': map(
    MmsGlobalRole.GLOBAL_ATLAS_SOFTWARE_VERSION_ROLLOUT_ADMIN
  ),
  '10gen-internal-mms-global-atlas-engineering-operator': map(MmsGlobalRole.GLOBAL_ATLAS_ENGINEERING_OPERATOR),
  '10gen-internal-mms-global-atlas-operator': map(MmsGlobalRole.GLOBAL_ATLAS_OPERATOR),
  '10gen-internal-mms-global-atlas-tse': map(MmsGlobalRole.GLOBAL_ATLAS_TSE),
  '10gen-cloud': map(MmsGlobalRole.GLOBAL_MONITORING_ADMIN, MmsGlobalRole.GLOBAL_READ_ONLY),
  '10gen-internal-mms-app-setting-admin': map(MmsGlobalRole.GLOBAL_APP_SETTING_ADMIN),
  '10gen-internal-mms-feature-flag-admin': map(MmsGlobalRole.GLOBAL_FEATURE_FLAG_ADMIN),
  '10gen-mms-internal-gsa-admin': map(MmsGlobalRole.GLOBAL_SERVICE_ACCOUNT_ADMIN),
  '10gen-internal-mms-global-alerts-admin': map(MmsGlobalRole.GLOBAL_GLOBAL_ALERTS_ADMIN),
  '10gen-internal-mms-system-alerts-admin': map(MmsGlobalRole.GLOBAL_SYSTEM_ALERTS_ADMIN),
  '10gen-support': map(MmsGlobalRole.GLOBAL_READ_ONLY, MmsGlobalRole.GLOBAL_MONITORING_ADMIN),
  '10gen-mms-team-core-db': map(MmsGlobalRole.GLOBAL_MONITORING_ADMIN),
};

// This mapping comes from https://wiki.corp.mongodb.com/spaces/ATLASIAM/pages/*********/How+Stuff+Works+corp-qa.mongodb.com#HowStuffWorks%3Acorpqa.mongodb.com-Givingadditionalgroupsthroughgrouprulesincorp-qa.mongodb.com
const NON_PROD_MANA_GROUP_EXPANSION_MAPPINGS: Record<string, Array<string>> = {
  '10gen-mms-non-prod': ['10gen-eng', '10gen'],
  '10gen-mms-non-prod-gov': ['10gen-gov', '10gen-gov-mms-global-read-only', '10gen'],
  '10gen-mms-non-prod-admin': ['10gen-mms-owner', '10gen-eng', '10gen-mms-atlas-admin', '10gen'],
  '10gen-mms-non-prod-gov-admin': [
    '10gen-gov',
    '10gen-gov-mms-global-atlas-admin',
    '10gen-gov-mms-global-owner',
    '10gen-gov-mms-global-read-only',
    '10gen',
  ],
};

// Main function: get all roles for a list of Okta groups
export function getMmsGlobalRolesFromOktaGroups(groups: Array<string>): Array<MmsGlobalRole> {
  const roles = new Set<MmsGlobalRole>();
  for (const group of groups) {
    for (const role of GLOBAL_ROLE_MAPPING[group] || []) roles.add(role);
    for (const role of GLOBAL_GOV_ROLE_MAPPING[group] || []) roles.add(role);
    for (const role of GLOBAL_INTERNAL_ROLE_MAPPING[group] || []) roles.add(role);
  }
  return Array.from(roles);
}

// MMS developers generally have more “global roles” in non-prod environments.
// When a user joins MANA groups starting with 10gen-mms-non-prod, there are rules in "non-prod Okta" to also assign proper rules to this user.
// However for Admin UI, we only have access to Prod Okta, where those rules don't exist, as a result, we manually expand user's MANA groups in code.
// See more: https://wiki.corp.mongodb.com/spaces/ATLASIAM/pages/*********/How+Stuff+Works+corp-qa.mongodb.com#HowStuffWorks%3Acorpqa.mongodb.com-Givingadditionalgroupsthroughgrouprulesincorp-qa.mongodb.com
export function getExpandedUserManaGroupsInNonProd(groups: Array<string>): Array<string> {
  const expandedGroups: Set<string> = new Set(groups);
  for (const group of groups) {
    if (group in NON_PROD_MANA_GROUP_EXPANSION_MAPPINGS) {
      NON_PROD_MANA_GROUP_EXPANSION_MAPPINGS[group].forEach((expandedGroup) => {
        expandedGroups.add(expandedGroup);
      });
    }
  }
  return Array.from(expandedGroups);
}
