package com.xgen.prototypes.internal.svc;

import com.xgen.prototypes.internal.dao.CommentDao;
import com.xgen.prototypes.internal.model.CommentDocument;
import com.xgen.prototypes.internal.websocket.CommentBroadcaster;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CommentActivitiesImpl implements CommentActivities {
  private static final Logger LOG = LoggerFactory.getLogger(CommentActivitiesImpl.class);
  private final CommentDao commentDao;
  private final CommentBroadcaster commentBroadcaster;

  //  private final EventServiceClient eventServiceClient;

  @Inject
  public CommentActivitiesImpl(CommentDao commentDao, CommentBroadcaster commentBroadcaster
      // , EventServiceClient eventServiceClient
      ) {
    this.commentDao = commentDao;
    this.commentBroadcaster = commentBroadcaster;
    //    this.eventServiceClient = eventServiceClient;
  }

  @Override
  public void saveCommentToDb(CommentDocument comment) {
    LOG.info("Saving comment to DB");
    commentDao.saveComment(comment);
  }

  @Override
  public @Nullable ObjectId submitCommentCreatedEvent(CommentDocument comment) {
    LOG.info("Saving comment created event");
    return null;
    //      return createCommentCreatedEvent(
    //          comment.id(), comment.content(), comment.orgId(), comment.groupId());
  }

  @Override
  public void broadcastComment(CommentDocument comment) {
    LOG.info("Broadcasting comment to websockets");
    commentBroadcaster.pushNewEvent(comment);
  }

  //    private ObjectId createCommentCreatedEvent(
  //        ObjectId commentId, String content, ObjectId orgId, ObjectId projectId) {
  //      CommentCreated commentCreated =
  //          CommentCreated.newBuilder()
  //              .setCommentId(commentId.toHexString())
  //              .setContent(content)
  //              .build();
  //      try {
  //        return eventServiceClient.createEvent(
  //            commentCreated,
  //            "/events/comment/comment_created",
  //            ObjectId.get(),
  //            orgId,
  //            projectId,
  //            null,
  //            null,
  //            SourceMessage.newBuilder()
  //                .setSourceType("USER")
  //                .setUser(UserMessage.getDefaultInstance())
  //                .build(),
  //            Visibility.VISIBILITY_ADMIN,
  //            Collections.emptyList());
  //      } catch (Exception e) {
  //        LOG.warn(
  //            "Failed to create comment created event for comment id: {}, content: {}",
  //            commentId,
  //            content,
  //            e);
  //        throw e;
  //      }
  //    }
}
