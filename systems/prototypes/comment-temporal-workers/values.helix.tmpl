---
image:
  repository: "664315256653.dkr.ecr.us-east-1.amazonaws.com/mms/prototypes-comment"

env:
  OTEL_SERVICE_NAME: prototypes-comment-temporal-workers
{{ if or (not .isGov) (and (.isGov) (.isDev))}}
  TEMPORAL_WORKER_ENABLED: true
{{else}}
  TEMPORAL_WORKER_ENABLED: false
{{end}}



externalSecrets:
  - name: "authn_internal_client_secret"
  - name: "mongo_comment_service_username"
  - name: "mongo_comment_service_password"
  - name: "dummy_secret"
{{if and .isProd (not .isGov)}}
  - name: "temporary_broken_secret"
{{end}}
{{if not .isGov}}
  - name: "temporal_api_key"
{{end}}

additionalLabels:
  app: "prototypes-comment-temporal-workers"

authnServiceEnvoyFilter:
  enabled: false

replicaCount: 2

resources:
  limits:
    cpu: "8"
    memory: 8Gi
  requests:
    cpu: "8"
    memory: 8Gi

consumers:
  mms-{{ .environment }}:
    - mms-ui
    - mms-ui-admin
    - mms-api-public
  {{if .isGov}}
    - javatools
  {{else}}
  javatools-{{ .environment }}:
    - javatools
  {{end}}
