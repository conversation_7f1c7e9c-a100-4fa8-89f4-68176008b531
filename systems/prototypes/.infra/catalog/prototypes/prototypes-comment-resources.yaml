---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  annotations:
    deployment.10gen.cc/workflow-versions: deploy-app=v2
  name: prototypes-comment-resources
  title: Prototypes Comment Resources
spec:
  deployment:
    deploy_yaml_path: systems/prototypes/.infra/resources.yml
    deployment_group: prototypes-resources
    labels:
      app: prototypes-comment-resources
      owner: prototypes
    slack_channel: cloud
    targets:
      prod:
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
        labels:
          app: prototypes-comment-resources-prod
        release_name: prototypes-comment-resources-prod
      qa:
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
        labels:
          app: prototypes-comment-resources-qa
        release_name: prototypes-comment-resources-qa
      staging:
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
        labels:
          app: prototypes-comment-resources-staging
        release_name: prototypes-comment-resources-staging
    workload_type: platformclaim
  lifecycle: prod
  owner: 10gen-helix-prototypes-prod-admin
  system: prototypes
  type: deployment
