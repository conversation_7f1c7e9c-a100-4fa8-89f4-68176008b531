load("//systems/common/helix:defs.bzl", "helix_resource", "helix_topology")

helix_resource(
    name = "prototypes-comment-resources",
    chart_name = "xgen-resources",
    chart_version = "1.22.0",
    deployment_group = "prototypes-resources",
    visibility = ["//systems/prototypes:__pkg__"],
    deps = [
        ":topology",
    ],
)

helix_topology(
    name = "topology",
    srcs = [
        ":values.helix.tmpl",
    ],
    cloud_providers = ["aws"],
    environments = [
        "qa",
        "staging",
        "prod",
    ],
    regions = [
        "us-east-1",
    ],
)
