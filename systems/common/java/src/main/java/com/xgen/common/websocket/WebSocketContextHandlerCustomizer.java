package com.xgen.common.websocket;

import static org.slf4j.LoggerFactory.getLogger;

import com.xgen.common.annotations.VisibleForTesting;
import com.xgen.common.http.ServletContextHandlerCustomizer;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.websocket.DeploymentException;
import jakarta.websocket.server.ServerEndpoint;
import jakarta.websocket.server.ServerEndpointConfig;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.websocket.jakarta.server.config.JakartaWebSocketServletContainerInitializer;
import org.slf4j.Logger;

/**
 * Customizes a Jetty {@link ServletContextHandler} to configure WebSocket endpoints support.
 *
 * <p>This class is responsible for initializing the WebSocket server container and registering
 * WebSocket endpoints defined by the provided classes.
 */
public class WebSocketContextHandlerCustomizer implements ServletContextHandlerCustomizer {
  private static final Logger LOG = getLogger(WebSocketContextHandlerCustomizer.class);

  private final ServerEndpointConfig.Configurator configurator;
  private final Set<Class<?>> endpointClasses;

  @Inject
  public WebSocketContextHandlerCustomizer(
      ServerEndpointConfig.Configurator configurator,
      @Named("websocket.classes") Set<Class<?>> endpointClasses) {
    this.configurator = configurator;
    this.endpointClasses = endpointClasses;
  }

  @Override
  public void customize(ServletContextHandler contextHandler) {
    JakartaWebSocketServletContainerInitializer.configure(
        contextHandler,
        (servletContext, serverContainer) -> {
          endpointClasses.stream()
              .map(this::toEndpointConfig)
              .filter(Optional::isPresent)
              .map(Optional::get)
              .forEach(
                  serverConfig -> {
                    try {
                      LOG.atInfo()
                          .setMessage("Registering WebSocket handler")
                          .addKeyValue("wsClass", serverConfig.getEndpointClass())
                          .addKeyValue("path", serverConfig.getPath())
                          .log();
                      serverContainer.addEndpoint(serverConfig);
                    } catch (DeploymentException e) {
                      throw new RuntimeException(e);
                    }
                  });
          // Disable session tracking to prevent session creation
          servletContext.setSessionTrackingModes(Set.of());
        });
  }

  @VisibleForTesting
  Optional<ServerEndpointConfig> toEndpointConfig(Class<?> wsClass) {
    var ann = wsClass.getAnnotation(ServerEndpoint.class);
    if (ann == null) {
      LOG.atDebug()
          .setMessage("WebSocket class does not have @ServerEndpoint annotation")
          .addKeyValue("class", wsClass.getName())
          .log();
      return Optional.empty();
    }
    var path = ann.value();
    return Optional.of(
        ServerEndpointConfig.Builder.create(wsClass, path)
            .subprotocols(List.of(ann.subprotocols()))
            // TODO: Implement standard JacksonDecoder
            .decoders(List.of(ann.decoders()))
            .encoders(
                Stream.concat(Stream.of(JacksonTextEncoder.class), Stream.of(ann.encoders()))
                    .toList())
            .configurator(configurator)
            .build());
  }
}
