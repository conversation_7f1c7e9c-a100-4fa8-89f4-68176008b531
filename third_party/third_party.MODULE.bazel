http_archive = use_repo_rule("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")

http_jar = use_repo_rule("@bazel_tools//tools/build_defs/repo:http.bzl", "http_jar")

third_party_extension = use_extension("//third_party:third_party_extension.bzl", "third_party_extension")
use_repo(
    third_party_extension,
    "bomber.linux_x86_64",
    "bomber.macos_arm64",
    "bomber.macos_x86_64",
    "d2.linux_x86_64",
    "d2.macos_arm64",
    "d2.macos_x86_64",
    "drone.linux_x86_64",
    "drone.macos_arm64",
    "drone.macos_x86_64",
    "foascli.linux_x86_64",
    "foascli.macos_arm64",
    "foascli.macos_x86_64",
    "gitcli.linux_x86_64",
    "gitcli.macos_arm64",
    "gitcli.macos_x86_64",
    "github_graphql_schema",
    "jq.linux_x86_64",
    "jq.macos_arm64",
    "jq.macos_x86_64",
    "k0s.linux_arm64",
    "k0s.linux_x86_64",
    "migrate.linux_x86_64",
    "migrate.macos_arm64",
    "migrate.macos_x86_64",
    "oasdiff.linux_x86_64",
    "oasdiff.macos_arm64",
    "oasdiff.macos_x86_64",
    "snyk.linux_x86_64",
    "snyk.macos_arm64",
    "snyk.macos_x86_64",
    "spectral.linux_x86_64",
    "spectral.macos_arm64",
    "spectral.macos_x86_64",
    "yq.linux_x86_64",
    "yq.macos_arm64",
    "yq.macos_x86_64",
)

# copybara
http_jar(
    name = "com_github_google_copybara",
    sha256 = "6ba38dabc9af4822c402174674e0905b19ec7e5a61641911b7d2bf53f645ec80",
    urls = ["https://github.com/google/copybara/releases/download/v20250501/copybara_deploy.jar"],
)

http_archive(
    name = "apache_maven",
    build_file = "//third_party:maven.BUILD",
    sha256 = "c8fb9f620e5814588c2241142bbd9827a08e3cb415f7aa437f2ed44a3eeab62c",
    strip_prefix = "apache-maven-3.9.7",
    urls = [
        "https://dlcdn.apache.org/maven/maven-3/3.9.7/binaries/apache-maven-3.9.7-bin.tar.gz",
        "https://archive.apache.org/dist/maven/maven-3/3.9.7/binaries/apache-maven-3.9.7-bin.tar.gz",
    ],
)
