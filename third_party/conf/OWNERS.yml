version: 1.0.0
aliases:
  - //.github/CODEOWNERS-aliases.yml
filters:
  - "bomber.BUILD":
    approvers:
      - release-infra
  - "bomber.json":
    approvers:
      - release-infra
  - "BUILD.bazel":
    approvers:
      - 10gen/code-review-team-devprod-build
  - "d2.BUILD":
    approvers:
      - 10gen/code-review-team-devprod-build
  - "d2.json":
    approvers:
      - 10gen/code-review-team-devprod-build
  - "drone.BUILD":
    approvers:
      - 10gen/code-review-team-devprod-build
      - 10gen/code-review-team-devprod-services-integrations
  - "drone.json":
    approvers:
      - 10gen/code-review-team-devprod-build
      - 10gen/code-review-team-devprod-services-integrations
  - "foascli.BUILD":
    approvers:
      - 10gen/code-review-team-apix-platform
  - "foascli.json":
    approvers:
      - 10gen/code-review-team-apix-platform
  - "gitcli.BUILD":
    approvers:
      - 10gen/code-review-team-devprod-services-integrations
  - "gitcli.json":
    approvers:
      - 10gen/code-review-team-devprod-services-integrations
  - "github_graphql_schema.json":
    approvers:
      - gmishkin
      - joe-dipilato
      - fetsko
  - "jq.json":
    approvers:
      - 10gen/code-review-team-devprod-build
  - "k0s.json":
    approvers:
      - 10gen/code-review-team-devprod-correctness
  - "migrate.BUILD":
    approvers:
      - 10gen/code-review-team-intel-i
  - "migrate.json":
    approvers:
      - 10gen/code-review-team-intel-i
  - "oasdiff.BUILD":
    approvers:
      - 10gen/code-review-team-apix-platform
  - "oasdiff.json":
    approvers:
      - 10gen/code-review-team-apix-platform
  - "OWNERS.yml":
    approvers:
      - 10gen/code-review-team-devprod-build
  - "snyk.json":
    approvers:
      - 10gen/code-review-team-systems-common
  - "spectral.json":
    approvers:
      - 10gen/code-review-team-apix-platform
      - 10gen/code-review-team-apix-devtools
  - "yq.json":
    approvers:
      - 10gen/code-review-team-devprod-build
