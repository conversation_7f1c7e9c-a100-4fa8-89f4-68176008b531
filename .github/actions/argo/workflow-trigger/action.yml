name: 'Argo Workflow Trigger'
description: 'Trigger and monitor Argo Workflows'
inputs:
  token-key:
    description: 'Key for the Argo token secret, typically the system-name (e.g. "prototypes")'
    required: true
  argo-version:
    description: 'Argo Workflows CLI version to install'
    required: false
    default: 'v3.5.15'
  argo-server:
    description: 'Argo Workflows server URL'
    required: false
    default: 'argo-workflows.corp.mongodb.com:443'
  argo-namespace:
    description: 'Argo Workflows namespace'
    required: true
  workflow-name:
    description: 'Name of the workflow template to trigger'
    required: true
  workflow-param-env:
    description: 'Environment parameter for the workflow'
    required: false
    default: 'dev'
  workflow-param-asset:
    description: 'Asset parameter for the workflow (typically git SHA)'
    required: false
    default: ${{ github.sha }}
  workflow-timeout:
    description: 'Timeout in seconds for workflow monitoring'
    required: false
    default: '300'
  workflow-auto-rollback-enabled:
    description: '(Optional) Whether to enable automatic rollback on failure. Default is false.'
    required: false
    default: 'false'
  workflow-auto-rollback-percentage:
    description: '(Optional) Percentage of failed ArgoCD app syncs required to trigger a rollback (default is zero-tolerance). Only applicable if workflow-auto-rollback-enabled is true.'
    required: false
    default: '1'
  workflow-addtl-params:
    description: '(Optional) Additional workflow parameters. Should be a comma-separated list of key=value pairs (e.g., key1=value1,key2=value2). Will be appended to the argo submit command as `-p key1=value1 -p key2=value2`'
    required: false

outputs:
  workflow-id:
    description: 'The unique ID of the submitted workflow'
    value: ${{ steps.submit-workflow.outputs.workflow-id }}
  workflow-status:
    description: 'The final status of the workflow (Succeeded, Failed, Error, etc.)'
    value: ${{ steps.monitor-workflow.outputs.workflow-status }}
  workflow-ui-url:
    description: 'Direct URL to view the workflow in Argo UI'
    value: ${{ steps.submit-workflow.outputs.workflow-ui-url }}

runs:
  using: 'composite'
  steps:
    - name: Install Argo Workflows CLI
      shell: bash
      env:
        ARGO_VERSION: ${{ inputs.argo-version }}
      run: |
        set -eu

        # Download and install Argo Workflows CLI
        curl -sLO "https://github.com/argoproj/argo-workflows/releases/download/${ARGO_VERSION}/argo-linux-amd64.gz"
        gunzip argo-linux-amd64.gz
        chmod +x argo-linux-amd64
        sudo mv argo-linux-amd64 /usr/local/bin/argo

        # Verify installation
        argo version --short

    - name: Fetch Token
      id: fetch-token
      uses: ./.github/actions/aws/json-secret
      with:
        secret-name: ArgoToken-prod
        secret-key: ${{ inputs.token-key }}

    - name: Configure Argo CLI authentication
      shell: bash
      env:
        ARGO_SERVER: ${{ inputs.argo-server }}
        ARGO_NAMESPACE: ${{ inputs.argo-namespace }}
        ARGO_HTTP1: true
        ARGO_TOKEN: ${{ steps.fetch-token.outputs.secret-value }}
      run: |
        set -eu

        echo "Configuring Argo CLI with JWT authentication"
        echo "Server: ${ARGO_SERVER}"
        echo "Namespace: ${ARGO_NAMESPACE}"
        echo "HTTP1 Mode: ${ARGO_HTTP1}"

        # Test connection
        echo "Testing connection to Argo Workflows server..."
        echo "Executing: argo list --namespace \"${ARGO_NAMESPACE}\" --since 1d"

        # Verify connection
        argo list --namespace "${ARGO_NAMESPACE}" --since 1d

    - name: Submit Argo Workflow
      id: submit-workflow
      shell: bash
      env:
        ARGO_SERVER: ${{ inputs.argo-server }}
        ARGO_HTTP1: true
        ARGO_TOKEN: ${{ steps.fetch-token.outputs.secret-value }}
        WORKFLOW_NAME: ${{ inputs.workflow-name }}
        WORKFLOW_NAMESPACE: ${{ inputs.argo-namespace }}
        WORKFLOW_ENV: ${{ inputs.workflow-param-env }}
        WORKFLOW_ASSET: ${{ inputs.workflow-param-asset }}
        WORKFLOW_TIMEOUT: ${{ inputs.workflow-timeout }}
        WORKFLOW_AUTO_ROLLBACK_ENABLED: ${{ inputs.workflow-auto-rollback-enabled }}
        WORKFLOW_AUTO_ROLLBACK_PERCENTAGE: ${{ inputs.workflow-auto-rollback-percentage }}
        WORKFLOW_ADDTL_PARAMS: ${{ inputs.workflow-addtl-params }}

      run: |
        set -eu

        echo "Submitting Argo Workflow: ${WORKFLOW_NAME}"
        echo "Namespace: ${WORKFLOW_NAMESPACE}"
        echo "Server: ${ARGO_SERVER}"
        echo "HTTP1 Mode: ${ARGO_HTTP1}"

        # Generate unique workflow name with human-readable date
        DATE_SUFFIX=$(date +"%Y%m%d-%H%M%S-%3N")

        # Truncate workflow name if needed to ensure total length <= 63 characters
        # = 20 characters reserved for the date suffix
        MAX_NAME_LENGTH=43  # 63 - 20 = 43 characters for name
        if [[ ${#WORKFLOW_NAME} -gt $MAX_NAME_LENGTH ]]; then
          TRUNCATED_NAME="${WORKFLOW_NAME:0:$MAX_NAME_LENGTH}"
          echo "Workflow name exceeds $MAX_NAME_LENGTH characters, truncating: ${WORKFLOW_NAME} -> ${TRUNCATED_NAME}"
        else
          TRUNCATED_NAME="${WORKFLOW_NAME}"
        fi

        WORKFLOW_ID="${TRUNCATED_NAME}-${DATE_SUFFIX}"

        # Process additional parameters
        ADDTL_PARAMS_STRING=""
        if [[ -n "${WORKFLOW_ADDTL_PARAMS:-}" ]]; then
          echo "Processing additional parameters: ${WORKFLOW_ADDTL_PARAMS}"
          # Split by comma and convert to -p key=value format
          IFS=',' read -ra PARAM_ARRAY <<< "${WORKFLOW_ADDTL_PARAMS}"
          for param in "${PARAM_ARRAY[@]}"; do
            # Trim whitespace and add -p prefix
            param=$(echo "$param" | xargs)
            if [[ -n "$param" ]]; then
              ADDTL_PARAMS_STRING="${ADDTL_PARAMS_STRING} -p ${param}"
            fi
          done
          echo "Additional parameters string: ${ADDTL_PARAMS_STRING}"
        fi

        # Add auto-rollback parameters if enabled
        if [[ "${WORKFLOW_AUTO_ROLLBACK_ENABLED}" == "true" ]]; then
          echo "Auto-rollback is enabled."
          echo "Setting auto-rollback percentage to: ${WORKFLOW_AUTO_ROLLBACK_PERCENTAGE}"
          ADDTL_PARAMS_STRING="${ADDTL_PARAMS_STRING} -p enable-auto-rollback=true"
          # Default rollback percentage is '0' if not specified.
          ADDTL_PARAMS_STRING="${ADDTL_PARAMS_STRING} -p auto-rollback-percentage=${WORKFLOW_AUTO_ROLLBACK_PERCENTAGE}"
        fi

        if [[ -n "${ADDTL_PARAMS_STRING}" ]]; then
            echo "Final additional parameters string: ${ADDTL_PARAMS_STRING}"
        fi

        # Build argo submit command
        ARGO_CMD="argo submit --namespace ${WORKFLOW_NAMESPACE} --name ${WORKFLOW_ID} --from workflowtemplate/${WORKFLOW_NAME} -p env=${WORKFLOW_ENV} -p asset=${WORKFLOW_ASSET} -p timeout=${WORKFLOW_TIMEOUT}${ADDTL_PARAMS_STRING}"

        # Execute the command
        echo "Executing: ${ARGO_CMD}"
        eval "${ARGO_CMD}"

        # Get the workflow status
        echo "Getting workflow status for: ${WORKFLOW_ID}"
        argo get "${WORKFLOW_ID}" --namespace "${WORKFLOW_NAMESPACE}"

        # Store workflow ID for next step and as output
        echo "WORKFLOW_ID=${WORKFLOW_ID}" >> $GITHUB_ENV
        echo "workflow-id=${WORKFLOW_ID}" >> $GITHUB_OUTPUT

        # Generate the workflow UI URL
        WORKFLOW_UI_URL="https://argo-workflows.corp.mongodb.com/workflows/${WORKFLOW_NAMESPACE}/${WORKFLOW_ID}?tab=workflow"
        echo "workflow-ui-url=${WORKFLOW_UI_URL}" >> $GITHUB_OUTPUT
        echo "Workflow UI URL: ${WORKFLOW_UI_URL}"

    - name: Monitor Workflow Progress
      id: monitor-workflow
      shell: bash
      env:
        ARGO_SERVER: ${{ inputs.argo-server }}
        ARGO_TOKEN: ${{ steps.fetch-token.outputs.secret-value }}
        ARGO_HTTP1: true
        WORKFLOW_NAMESPACE: ${{ inputs.argo-namespace }}
        WORKFLOW_TIMEOUT: ${{ inputs.workflow-timeout }}
      run: |
        set -eu

        echo "Monitoring workflow progress for: ${WORKFLOW_ID}"
        echo "Using timeout of ${WORKFLOW_TIMEOUT} seconds"

        # Initialize status
        FINAL_STATUS="Unknown"

        # Wait for workflow completion (with timeout)
        WORKFLOW_STATUS=0
        timeout ${WORKFLOW_TIMEOUT} argo wait "${WORKFLOW_ID}" --namespace "${WORKFLOW_NAMESPACE}" || {
          echo "Workflow monitoring timed out after ${WORKFLOW_TIMEOUT} seconds"
          WORKFLOW_STATUS=1
          FINAL_STATUS="Timeout"
        }

        # Get final status from argo get command
        echo "Getting final workflow status:"
        STATUS_OUTPUT=$(argo get "${WORKFLOW_ID}" --namespace "${WORKFLOW_NAMESPACE}" -o json | jq -r '.status.phase' 2>/dev/null || echo "Unknown")

        if [[ "$STATUS_OUTPUT" != "Unknown" ]]; then
          FINAL_STATUS="$STATUS_OUTPUT"
        else
          # Fallback to text parsing if jq fails
          if argo get "${WORKFLOW_ID}" --namespace "${WORKFLOW_NAMESPACE}" | grep -q "Succeeded"; then
            FINAL_STATUS="Succeeded"
          elif argo get "${WORKFLOW_ID}" --namespace "${WORKFLOW_NAMESPACE}" | grep -q "Failed"; then
            FINAL_STATUS="Failed"
          elif argo get "${WORKFLOW_ID}" --namespace "${WORKFLOW_NAMESPACE}" | grep -q "Error"; then
            FINAL_STATUS="Error"
          fi
        fi

        # Output the final status
        echo "workflow-status=${FINAL_STATUS}" >> $GITHUB_OUTPUT

        if [[ "$FINAL_STATUS" != "Succeeded" ]]; then
          echo "::error::Argo Workflow failed with status: ${FINAL_STATUS}"
          exit 1
        else
          echo "Final workflow status: ${FINAL_STATUS}"
        fi

        echo "✅ Argo Workflow completed successfully!"
