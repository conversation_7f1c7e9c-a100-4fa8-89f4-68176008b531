# MongoDB Cloud Manager/Atlas Codebase Guidelines

## Core Development Principles
- Simplicity & Readability are paramount - make minimal necessary changes only
- Style Consistency - match surrounding code patterns and conventions exactly
- Focused Work - no unrelated changes; document architectural decisions instead
- Preserve Comments - remove only if provably false or misleading
- Evergreen Comments & Names - avoid temporal language like "currently" or "soon"
- No Mock Modes and No Rewrites without explicit permission
- Preserve Whitespace - maintain existing formatting patterns

## Architecture & Domain Understanding
- This is the MongoDB Cloud Manager/Atlas codebase - a large-scale polyglot cloud platform
- System is actively migrating from monolith to microservices architecture
- Follow DAO → Service → Resource layered architecture pattern consistently
- Respect _public/_private package separation: _public exposes APIs, _private is internal
- Use Domain-Driven Design principles with clear service boundaries

## Technology Stack Requirements
- Build System: Use Bazel
- Backend: Java 11+ with Spring Framework, gRPC/Protobuf for service communication
- Frontend: TypeScript/React with Node.js 22.11.0, pnpm 9.7.1 for package management
- Database: MongoDB with advanced clustering, use majority write concern for critical operations
- Cloud Providers: Support AWS, Azure, GCP deployment patterns

## Code Style & Patterns
- Implement builder patterns for complex objects with `toBuilder()` methods
- Use Optional<T> return types for nullable values with proper null handling
- Follow Jakarta Inject patterns with constructor injection over field injection
- Organize imports: static imports first, then Java stdlib, third-party, internal packages
- Avoid wildcard imports: use specific imports instead of `import static org.junit.jupiter.api.Assertions.*;`
- Use domain-specific error codes (NDSErrorCode, CPSErrorCode) instead of generic exceptions
- Implement structured logging with key-value pairs for observability
- Follow Java naming conventions: avoid PascalCase for variables and methods, use camelCase instead
- Add Java docs for all public methods and classes. Comments should be concise and to the point

### Constructor Change Protocol - MANDATORY PROCESS

When adding, removing, or reordering parameters in ANY constructor (data models, services, DAOs, utilities, etc.), you MUST follow this comprehensive process:

#### 1. Pre-Change Analysis
- **NEVER** modify a constructor without first identifying ALL usage sites
- Use multiple search strategies to ensure complete coverage
  - Search by class name: `new ClassName(`
  - Search by fully qualified name: `new package.ClassName(`
  - Search by inner class: `new OuterClass.InnerClass(`
  - Search for import statements: `import package.ClassName` to find potential usage files
  - Check BUILD files for dependencies that indicate test files using the class

#### 2. Comprehensive Search Requirements
- **Production Code**: Search all `server/src/main/` directories
- **Unit Tests**: Search all `server/src/unit/` directories
- **Integration Tests**: Search all `server/src/test/` directories
- **Test Utilities**: Search test helper and factory classes
- **Build Files**: Check BUILD.bazel files for test dependencies

#### 3. Update Process
- Update EVERY constructor usage found in the search
- Maintain parameter order consistency across all call sites

#### 4. Verification Requirements - BLOCKING
- Compile ALL affected modules - any compilation failure is a blocking issue
- Do NOT consider the task complete until all code compiles successfully

#### 5. Common Failure Patterns to Avoid
- Missing test files that use the constructor
- Forgetting inner class constructors (e.g., `SourceMetadata` inside `LogIngestionLogEntry`)
- Not searching for fully qualified names in addition to simple class names
- Stopping after fixing compilation errors without running tests

## REST API Patterns

### Endpoint Conventions
- URL structure: `/api/{app:public|atlas}/v{version}/{resource}` with proper versioning
- Admin endpoints: `/admin/{service}/{resource}` for internal tools
- Private APIs: `/api/private/v{version}` for inter-service communication
- Resource hierarchy: `/orgs/{orgId}/teams`, `/groups/{groupId}/clusters`

### Controller Patterns
- Always extend `ApiBaseResource` or `BaseResource` as appropriate
- Use `@UiCall`, `@RolesAllowed`, or `@Auth` annotations for authorization
- Include `@QueryParam("envelope") final Boolean pEnvelope` for response wrapping
- Use `ApiResponseBuilder` for consistent response formatting
- Handle errors with domain-specific `ApiErrorCode.X.exception(pEnvelope, args)`

### Request/Response Patterns
```java
@POST
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RolesAllowed({GROUP_OWNER})
public Response createResource(@Valid @NotNull final ResourceView view) {
    return new ApiResponseBuilder(pEnvelope).created().content(result).build();
}
```

## Database & DAO Patterns

### DAO Implementation Standards
- Extend appropriate `BaseDao<T>` (modern for services, legacy for monolith)
- Use `@Singleton` annotation for dependency injection lifecycle
- Define `public static final String DB/COLLECTION` constants
- Implement `getIndexes()` method for programmatic index definition
- Add `@PromMethodMetrics(name, help, labelNames, labelValues)` for monitoring

## Testing Requirements - CRITICAL
- Strict TDD: failing test → code → green → refactor cycle mandatory
- Unit and integration tests required unless explicitly authorized to skip
- Test output must be pristine - no warnings, errors, or flaky behavior
- Capture expected errors in tests with proper assertions
- Integration tests use real dependencies, minimal mocking
- Always run tests when tasks are completed to ensure changes work correctly

### Test File Naming Conventions
- **Unit Tests**: End with `UnitTests.java` (e.g., `InstanceSizeUnitTests.java`)
- **Integration Tests**: End with `IntTests.java` (e.g., `SupportUpliftAuditorIntTests.java`)

### Test Method Naming Pattern
- Use `methodName_scenario_expectedBehavior()` format
- **DO NOT** prefix with `test` - the `@Test` annotation makes this redundant
- Examples: `getNewCollectionName_getCreationTimeFromCollectionName_inverseMethods()`

### Test Organization Structure
- **Unit Tests**: `//server/src/unit/com/xgen/...` - isolated component testing
- **Integration Tests**: `//server/src/test/com/xgen/...` - real dependencies, end-to-end flows
- **Frontend Tests**: `//client/packages/.../file.test.ts` - TypeScript unit tests

### Test Class Structure Standards
- Extend `JUnit5BaseSvcTest` for integration tests requiring service setup
- Use `@ExtendWith` for JUnit 5 (not `@RunWith` which is JUnit 4)- Clean up test data in @AfterEach methods to prevent test pollution
- Clean up test data in @AfterEach methods to prevent test pollution
- Group related test methods logically within the class
- Use descriptive test method names that explain the scenario being tested

## Security & Safety Rules - NEVER MODIFY
- Database migrations without proper review - risk of data loss
- Security-critical authentication and authorization code
- API contracts without proper versioning strategy
- Configuration files containing secrets or credentials
- Core service discovery and routing logic
- Production deployment scripts and configurations

## Essential Bazel Commands

### Testing Commands
- **All unit tests**: `bazel test //server/src/unit/com/xgen/svc/mms/...`
- **All integration tests**: `bazel test //server/src/test/com/xgen/svc/mms/...`
- **Single test class**: `bazel test //server/src/test/path/to/package:TestClassName`
- **Single test method**: `bazel test //server/src/test/path/to/package:TestClassName --test_filter=testMethodName$`
- **Package tests only**: `bazel test //server/src/test/path/to/package:all`
- **Disable test cache**: `bazel test --nocache_test_results <targets>`
- **Debug tests**: `bazel test --java_debug <target>`
- **Stream test output**: `bazel test --test_output=streamed <target>`

### Build Commands
- **Build everything**: `bazel build //...`
- **Build server code**: `bazel build //server/src/main/...`
- **Build tests**: `bazel build //server/src/test/...`
- **Build specific component**: `bazel build //server/src/main:mms-local`

### Utilities & Tools
- **Format code**: `bazel run @aspect_rules_format//format` (MUST run after task completion)

### Frontend Development

#### Build Commands
- **Build client assets**: `bazel build //client:assets`
- **Compile CSS**: `bazel build //client/less/...`
- **Compile JavaScript apps**: `bazel build //client/js/...`

#### Unit Testing
- **Run specific test file**: `bazel test //client/packages/common:js_unit_test --test_filter="**/*/utils/envUtils.test.ts" --test_output=all`
- **Run all tests in package**: `bazel test //client/packages/common:js_unit_test --test_output=all`
- **Alternative test command**: `pnpm test:unit client/packages/common/utils/envUtils.test.ts`
- **Watch mode testing**: `pnpm test:unit --watch` (re-runs on file changes)
- **All frontend unit tests**: `pnpm run test:unit`
- For JUnit 5, use @ExtendWith instead of @RunWith (which is a JUnit 4 construct).

#### TypeScript & Type Checking
- **Typecheck specific package**: `bazel test //client/packages/common:typecheck_test --test_output=all`
- **Global TypeScript check**: `pnpm run typecheck`
- **Note**: Each Bazel package exposes a `typecheck_test` target for incremental typechecking

#### Code Quality & Linting
- **ESLint specific package**: `bazel test //client/packages/common:lint --test_output=all`
- **All code health checks**: `pnpm run code_health`
- **Individual linting**: `pnpm run code_health:lint`
- **Dead code detection**: `pnpm run code_health:deadcode`
- **Format changed files**: `pnpm run format`

#### Development Notes
- **Frontend code location**: `client/packages/` (main source code)
- **Apps definition**: `client/apps/` (Bazel rules for Main app, Project app)
- **UI E2E tests**: `client/cypress/` (Cypress test files) can be run with `pnpm cypress:run --spec ` command
- **Cucumber E2E tests**: `bazel test --test_arg=--jvm_flag="-Dcucumber.filter.tags=@tag and @anotherTag" //server/src/features/*`
- **Package identification**: Look for `BUILD` files denoting Bazel packages
- **VSCode integration**: Use ESLint and Prettier plugins for immediate feedback

## Error Handling Patterns
- Use UncheckedSvcException with domain-specific error codes
- Implement proper Optional handling - check isEmpty() before accessing
- Add structured logging with meaningful context for debugging
- Use @PromMethodMetrics for observability on critical methods
- Handle terminal vs non-terminal status states appropriately

## Code Reuse and Duplication Prevention

**Before implementing new functionality:**
- **Search the existing codebase** to verify that similar or identical functionality does not already exist
- Use codebase search tools to identify existing implementations, classes, methods, or utilities
- Check for similar patterns that serve the same purpose across different modules or packages
- Review existing condition classes, validation logic, utility methods, and service implementations

**After making changes:**
- **Validate that the new implementation** doesn't duplicate existing capabilities
- If duplicate functionality is discovered, consolidate or refactor to reuse existing code
- When choosing to create new functionality instead of reusing existing code, document the rationale clearly
- Prefer extending or enhancing existing well-tested infrastructure over creating new implementations

**Benefits of reuse:**
- Reduces maintenance overhead and potential bugs
- Leverages existing test coverage and battle-tested implementations
- Maintains consistency across the codebase
- Improves code discoverability and reduces cognitive load

## Anti-Patterns to Avoid

### Database Anti-Patterns
- Direct MongoDB collection access - use DAO layer instead
- Using wrong write concerns - use `WriteConcern.MAJORITY` for critical operations
- Missing index definitions - always implement `getIndexes()` method in DAOs
- Ignoring field naming conventions - use `FieldDefs` constants for all field references

### Service Layer Anti-Patterns
- Generic RuntimeException - use domain-specific error codes (NDSErrorCode, CPSErrorCode)
- Field injection - prefer constructor injection with `@Inject`
- Implementing business logic in controllers or gRPC layers - delegate to service layer
- Bypassing the service layer - respect architectural boundaries

### API Anti-Patterns
- Not extending proper base classes - use `ApiBaseResource` or `BaseResource`
- Missing envelope parameter - include `@QueryParam("envelope") final Boolean pEnvelope`
- Direct exception throwing - use `ApiErrorCode.X.exception(pEnvelope, args)` pattern
- Inconsistent authorization - choose appropriate `@RolesAllowed`, `@Auth`, or `@UiCall`

### Validation & Security Anti-Patterns
- Skipping input validation - use Jakarta Bean Validation annotations
- Generic validation errors - use domain-specific error codes with structured messages
- Missing ObjectId validation - always use `ObjectId.isValid()` for ID parameters
- Bypassing feature flags - check feature enablement before operations

### General Anti-Patterns
- Excessive mocking in integration tests - use real dependencies
- Temporal comments or variable names - keep code evergreen
- Creating new files unnecessarily - prefer editing existing files
- Missing metrics annotations - add `@PromMethodMetrics` to critical methods
- Implementing duplicate functionality without checking for existing solutions

## Testing Authorization Override
- Tests are mandatory unless explicitly told: "I AUTHORIZE YOU TO SKIP WRITING TESTS THIS TIME"
- This override is rare and only for rapid prototyping or exploration
- Production code changes always require comprehensive test coverage

## Code Owners
- Every file needs an owning team - if a file in the PR does not have an owner, code-ownership should be assigned via an `OWNERS.yaml` file
- After any changes to `OWNERS.yaml` files, run `bazel run codeowners` to update the consolidated `.github/CODEOWNERS` file

## Create Pull Request
- Prefix the title with the Jira ticket number which is usually the branch name (e.g., CLOUDP-12345: Add new feature)
- Follow the [template](.github/pull_request_template.md) for the PR description
- Always in draft mode

## JIRA Ticket Creation (CLOUDP Project)

### Required Fields - ALL MUST BE PROVIDED
Every ticket creation MUST include these exact fields or it will fail:

```python
mcp__docker-mcp__jira_create_issue(
    project_key="CLOUDP",  # Always CLOUDP
    summary="<title>",      # Clear, specific, <255 chars
    issue_type="<type>",    # Bug|Story|Investigation
    description="<details>", # Include context, steps, impact
    components="<component>", # See valid list below
    additional_fields={
        "priority": {"name": "<priority>"},        # Critical - P2 | Major - P3 | Minor - P4
        "customfield_12751": [{"value": "<team>"}], # Assigned team (see list below)
        "customfield_10857": "<epic_key>"          # Epic link (optional, e.g. "CLOUDP-275436")
    }
)
```

### Field Selection Rules

**Issue Type:**
- Bug: Errors, failures, broken functionality
- Story: Features, enhancements, general work (default)
- Investigation: Research tasks (rare)

**Components:**
- Atlas: Cluster/migration/deployment issues (default)

**Priority:**
- Critical - P2: Production down, data loss risk, security
- Major - P3: Everything else (default)
- Minor - P4: Low impact, nice-to-have

**Teams (customfield_12751):**
**ASK USER FOR THIS**

**Epic Link (customfield_10857):**
- Optional field to link ticket to an epic
- Provide epic key as string (e.g., "CLOUDP-275436")
- Use when ticket is part of a larger feature or initiative

### Example
```python
mcp__docker-mcp__jira_create_issue(
    project_key="CLOUDP",
    summary="Atlas cluster fails to restart after maintenance",
    issue_type="Bug",
    description="Cluster abc123 failed restart. Error: timeout. Impact: 8min downtime.",
    components="Atlas",
    additional_fields={
        "priority": {"name": "Critical - P2"},
        "customfield_12751": [{"value": "Automation"}],
        "customfield_10857": "CLOUDP-275436"
    }
)
```

### Critical Rules
- NEVER omit priority or customfield_12751 - ticket will fail
- Field values are case-sensitive - use exactly as shown
- When uncertain: Story + Major - P3 + Atlas
